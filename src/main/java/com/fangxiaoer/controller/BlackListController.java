package com.fangxiaoer.controller;

import com.fangxiaoer.common.*;
import org.joda.time.DateTime;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.Random;

@Controller
public class BlackListController {

    @GetMapping("/block")
    public String showBlockPage() {
        return "error/block";
    }

    @GetMapping("/temp")
    public String showTempPage(Model model, HttpServletRequest request) {
        int a = new Random().nextInt(1000) + 1;
        int b = new Random().nextInt(1000) + 1;
        char c = "+-*/".charAt(new Random().nextInt(4));
        int r = 0;
        switch (c) {
            case '+':
                r = a + b;
                break;
            case '-':
                r = a - b;
                break;
            case '*':
                r = a * b;
                break;
            case '/':
                r = a / b;
                break;
        }
        model.addAttribute("play", String.format("var a = parseInt(%d%c%d);", a, c, b));
        HttpSession session = request.getSession();
        session.setAttribute("playResult", r);
        return "error/temp";
    }

    @ResponseBody
    @PostMapping("/verifyBlocker")
    public String getVerifycode(HttpServletRequest request, Integer a) {
        String result = "CrazyBoyYourCrawlerHasBeenBlocked";
        String url = request.getHeader("referer");
        if (StringUtils.isEmpty(url)) return result;
        url = url.replaceFirst("https?://[0-9a-z\\.]+:?\\d*", "").split("\\?")[0];
        HttpSession session = request.getSession();
        Integer playResult = (Integer) session.getAttribute("playResult");
        if (a == null || !a.equals(playResult)) {
            return result;
        }
        String jsVerifyMD5 = "";
        Cookie cookie = Utils.iterationCookie(request,"fxr-jsvid");
        if(null != cookie){
            jsVerifyMD5 = cookie.getValue();
        }
        String key = (String) session.getAttribute("fUid");
        String ua = request.getHeader("user-agent");
        String ip = Utils.getIpAddr(request);
        String dateString = DateTime.now().toString("yyyyMMdd");
        String verify = Utils.MD5encode(ip + "-" + dateString + "-" + key + "-" + ua);
        if (verify.equalsIgnoreCase(jsVerifyMD5)) {
            session.setAttribute("fVerify", verify);
            return "success";
        }
        return result;
    }


    /**
     * 获取服务器时间
     */
    @RequestMapping(value = "/getSysTime", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public HashMap<String, Object> getSysTime() {
        HashMap<String, Object> results = new HashMap<>();
        results.put("status", 1);
        results.put("msg", "");
        results.put("content", System.currentTimeMillis());
        return results;
    }

    /**
     * 验证图片验证码
     */
    @RequestMapping(value = "/verifyImageCode", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public HashMap<String, Object> verifyImageCode(HttpServletResponse response, HttpServletRequest request, String verifyCode, Integer verifySize, Long t) throws IOException {
        HashMap<String, Object> searchMap = new HashMap<>();
        searchMap.put("verifyCode", verifyCode);
        searchMap.put("verifySize", verifySize);
        searchMap.put("t", t);
        HashMap<String, Object> results = HttpUtil.connectApi(Constants.VERIFY_IMAGE_CODE, searchMap);
        int status = Double.valueOf(results.get("status").toString()).intValue();
        if (status == 1) {
            HttpSession session = request.getSession();
            String key = (String) session.getAttribute("fUid");
            String ip = Utils.getIpAddr(request);
            String ua = request.getHeader("user-agent");
            String dateString = DateTime.now().toString("yyyyMMdd");
            String codeVerifyMD5 = Utils.MD5encode(key + ip + "-" + dateString  + "-" + "yz.fangxiaoer.com" + "-" + ua);
            Utils.addCookie(response, "fxr-covid", codeVerifyMD5, "/");
        }
        return results;
    }
}
