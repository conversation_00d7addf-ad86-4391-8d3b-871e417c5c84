package com.fangxiaoer.controller;

import com.fangxiaoer.common.*;
import com.fangxiaoer.service.ImService;
import com.fangxiaoer.service.RentService;
import com.fangxiaoer.service.SecondHouseService;
import com.google.common.base.Strings;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/815
 */
@Controller
public class ImController {
    @Autowired
    private ImService imService;
    @Autowired
    private SecondHouseService shs;
    @Autowired
    RentService rentService;

    public static final String IM_URL = "/im";
    @RequestMapping(value = {IM_URL + "/{params}", IM_URL},method = RequestMethod.GET)
    public String housingprice(@Nullable @PathVariable("params") String params, Model model, HttpServletRequest request, HttpServletResponse response){
        ArrayList info_ims = new ArrayList();
        HttpSession session = request.getSession();
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = session.getAttribute("sessionId").toString();
            LinkedTreeMap<String, Object> map = (LinkedTreeMap) imService.getMyAccid(sessionId);
            try{
                if(!StringUtils.isEmpty(map.get("imProvinceState"))){
                    String user_state = map.get("imProvinceState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","此功能只针对辽宁、北京等特定地域的手机用户开通！详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                if(!StringUtils.isEmpty(map.get("imState"))){
                    String user_state = map.get("imState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","您的聊呗账号已禁用，如需开通详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                String accid = map.get("accid").toString();
                String token = map.get("token").toString();
                CookieManage.addCookie(response, "uid", accid, "/", "");
                CookieManage.addCookie(response, "sdktoken", token, "/", "");
            }catch (Exception e){
                return "instantMessaging/im/error";
            }
        }
        if (params != null && !params.equals("undefined")) {
            String [] param = params.split("-");
            String houseId = param[0];
            String type = param[1];
            switch (type){
                case "0":
                    HashMap projects = getImProject(houseId, type, param[2]);
                    info_ims.add(projects);
                    model.addAttribute("info_im",info_ims);
                    break;
                case "1":
                    HashMap sales = getImSales(houseId, type, model);
                    info_ims.add(sales);
                    model.addAttribute("info_im",info_ims);
                    break;
                case "2":
                    HashMap rents = getImRents(houseId, type, model);
                    info_ims.add(rents);
                    model.addAttribute("info_im",info_ims);
                    break;
                case "3":
                    HashMap shops = getImShops(houseId, type, model);
                    info_ims.add(shops);
                    model.addAttribute("info_im",info_ims);
                    break;
                case "4":
                    HashMap offices = getImShops(houseId, type, model);
                    info_ims.add(offices);
                    model.addAttribute("info_im",info_ims);
                    break;
            }
        }
        return "instantMessaging/im/main";
    }

    public HashMap getImProject(String houseId,String type, String memberId){
        HashMap<String, Object> projects = HttpUtil.connectApi(Constants.VIEW_PROJECT_LIST, new Params("xebProject", houseId).get());
        List<LinkedTreeMap<String, Object>> results = (List<LinkedTreeMap<String, Object>>) projects.get("content");
        LinkedTreeMap<String, Object> result = results.get(0);
        HashMap map = new HashMap();
        map.put("houseId",houseId);
        map.put("type",1);
        map.put("regionName",result.get("regionName").toString());
        map.put("plateName","");
        map.put("plateId", "");
        LinkedTreeMap person = (LinkedTreeMap) imService.checkAccid(memberId);
        map.put("keeperTel2", Utils.decode(person.get("mobileInfo").toString()));
        //对方的accid
        map.put("accid",person.get("accid").toString());
        //链接地址
        map.put("linkUrl","/house/"+houseId+ "-"+ result.get("type")+".htm");
        map.put("housePic",result.get("pic").toString());
        map.put("subName", result.get("projectName").toString());
        map.put("hallRoom", "");
        if(!StringUtils.isEmpty(result.get("area"))){
            List<LinkedTreeMap<String, Object>> areaBeans = (List<LinkedTreeMap<String, Object>>)result.get("area");
            if(areaBeans != null && areaBeans.size() > 0) {
                map.put("area", Utils.modifyNum(areaBeans.get(0).get("minArea").toString()) + "㎡~" +Utils.modifyNum(areaBeans.get(0).get("maxArea").toString()) + "㎡");
            } else {
                map.put("area", "");
            }
        }
        map.put("unit", "元/㎡");
        if(StringUtils.isEmpty(result.get("mPrice"))){
            map.put("price", "待定");
        }else{
            LinkedTreeMap<String, Object> mPrice = (LinkedTreeMap<String, Object>)result.get("mPrice");
            map.put("price", mPrice.get("priceType") + ":" + Utils.modifyNum(mPrice.get("priceMoney").toString()));
        }
        return map;
    }

    public HashMap getImSales(String houseId,String type, Model model){
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_SCDHOUSE_DETAIL,
                new Params("houseId", houseId).get(), model);
        HashMap map = new HashMap();
        map.put("houseId",houseId);
        map.put("type",type);
        map.put("regionName",result.get("regionName").toString());
        map.put("plateName",result.get("platName").toString());
        map.put("plateId",result.get("platId"));
        //手机号
        if(!StringUtils.isEmpty(result.get("keeperTel2")))
            map.put("keeperTel2", Utils.decode(result.get("keeperTel2").toString()));
        LinkedTreeMap person = (LinkedTreeMap) imService.getAccid(map.get("keeperTel2").toString());
        //对方的accid
        map.put("accid",person.get("accid").toString());
        //链接地址
        map.put("linkUrl","/salehouse/"+houseId+".htm");
        //房源图片
        if(null != result.get("pic")){
            ArrayList pics = (ArrayList) result.get("pic");
            if (pics.size() == 0) {
                map.put("housePic","https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg");
            }else {
                LinkedTreeMap pic = (LinkedTreeMap) pics.get(0);
                map.put("housePic",pic.get("url"));
            }
        }else{
            map.put("housePic","https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg");
        }

        //小区名称
        if(StringUtils.isEmpty(result.get("subName"))){
            map.put("subName","其它");
        }else {
            map.put("subName",result.get("subName").toString());
        }
        //户型
        if(!StringUtils.isEmpty(result.get("layout"))){
            map.put("hallRoom", result.get("layout").toString().replace("居", "室"));
        }
        //面积
        if(!StringUtils.isEmpty(result.get("area"))){
            map.put("area", Utils.modifyNum(result.get("area").toString())+'㎡');
        }
        //单位
        if(!StringUtils.isEmpty(result.get("price")) && Utils.modifyNum(result.get("price").toString()).equals("0")){
            map.put("unit", "");
        }else {
            map.put("unit", "万");
        }
        //价格
        if(!StringUtils.isEmpty(result.get("price"))){
            map.put("price", Utils.modifyNum(result.get("price").toString()));
            if (map.get("price").equals("0")) {
                map.put("price","面议");
            }
        }
        return map;
    }

    public HashMap getImRents(String houseId,String type, Model model){
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>)HttpUtil.connectApi(Constants.GET_RENTHOUST_DETAIL,
                new Params("houseId",houseId).get(),model);
        HashMap map = new HashMap();
        map.put("houseId",houseId);
        map.put("type",type);
        map.put("regionName",result.get("regionName").toString());
        map.put("plateName",result.get("plateName").toString());
        map.put("plateId",result.get("plateId"));
        //手机号
        if(!StringUtils.isEmpty(result.get("keeperTel2")))
            map.put("keeperTel2", Utils.decode(result.get("keeperTel2").toString()));
        LinkedTreeMap person = (LinkedTreeMap) imService.getAccid(map.get("keeperTel2").toString());
        //对方的accid
        map.put("accid",person.get("accid").toString());
        //链接地址
        map.put("linkUrl","/rent/"+houseId+".htm");
        //房源图片

        if(null != result.get("pic")){
            ArrayList pics = (ArrayList) result.get("pic");
            if (pics.size() == 0) {
                map.put("housePic","https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg");
            }else {
                LinkedTreeMap pic = (LinkedTreeMap) pics.get(0);
                map.put("housePic",pic.get("url"));
            }
        }else{
            map.put("housePic","https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg");
        }

        //小区名称
        map.put("subName",result.get("regionName").toString()+'-'+result.get("plateName").toString());
        //户型
        map.put("hallRoom", result.get("room").toString()+'室'+result.get("hall").toString()+'厅'+result.get("toilet").toString()+'卫');
        //面积
        if(!StringUtils.isEmpty(result.get("area"))){
            map.put("area", Utils.modifyNum(result.get("area").toString())+'㎡');
        }
        //单位
        if(!StringUtils.isEmpty(result.get("price")) && Utils.modifyNum(result.get("price").toString()).equals("0")){
            map.put("unit", "");
        }else {
            map.put("unit", "元/月");
        }
        //价格
        if(!StringUtils.isEmpty(result.get("price"))){
            map.put("price", Utils.modifyNum(result.get("price").toString()));
            if (map.get("price").equals("0")) {
                map.put("price","面议");
            }
        }
        return map;
    }

    public HashMap getImShops(String houseId,String type, Model model){
        LinkedTreeMap<String, Object> result = new LinkedTreeMap<>();
        HashMap map = new HashMap();
        if (type.equals("3")) {
            result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.VIEW_SHOP_DETAIL,
                    new Params("shopId", houseId).get(), model);
            //链接地址
            map.put("linkUrl","/shop/"+houseId+".htm");
            //小区名称
            map.put("subName",result.get("regionName").toString()+'-'+result.get("plateName").toString());
            //户型
            map.put("hallRoom", result.get("shopTypeName").toString());
        }else if (type.equals("4")) {
            result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.OFFICE_DETAIL,
                    new Params("shopId", houseId).get(), model);
            //链接地址
            map.put("linkUrl","/scriptorium/"+houseId+".htm");
            //小区名称
            String subName = result.get("officeName") == null?result.get("title").toString():result.get("officeName").toString();
            map.put("subName",subName);
            //户型
            map.put("hallRoom", result.get("shopCategoriesName").toString());
        }

        map.put("houseId",houseId);
        map.put("type",type);
        map.put("regionName",result.get("regionName").toString());
        map.put("plateName",result.get("plateName").toString());
        map.put("plateId",result.get("plateId"));
        //手机号
        if(!StringUtils.isEmpty(result.get("ownerPhone1")))
            map.put("keeperTel2", Utils.decode(result.get("ownerPhone1").toString()));
        LinkedTreeMap person = (LinkedTreeMap) imService.getAccid(map.get("keeperTel2").toString());
        //对方的accid
        map.put("accid",person.get("accid").toString());
        //房源图片
        if(null != result.get("pic")){
            ArrayList pics = (ArrayList) result.get("pic");
            if (pics.size() == 0) {
                map.put("housePic","https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg");
            }else {
                LinkedTreeMap pic = (LinkedTreeMap) pics.get(0);
                map.put("housePic",pic.get("pic"));
            }
        }else{
            map.put("housePic","https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg");
        }
        //面积
        if(!StringUtils.isEmpty(result.get("area"))){
            map.put("area", Utils.modifyNum(result.get("area").toString())+'㎡');
        }
        //单位
        if(!StringUtils.isEmpty(result.get("price")) && Utils.modifyNum(result.get("price").toString()).equals("0")){
            map.put("unit", "");
        }else {
            String shopType = result.get("shopType").toString();
            if (shopType.equals("1")) {
                map.put("unit", "万");
            }else if (shopType.equals("4")) {
                map.put("unit", "元/月");
            }else if (shopType.equals("5")) {
                map.put("unit", "万");
            }else if (shopType.equals("50") || shopType.equals("100") || shopType.equals("110")) {
                map.put("unit", "元/年");
            }else {
                map.put("unit", "元/月");
            }
        }
        //价格
        if(!StringUtils.isEmpty(result.get("price"))){
            map.put("price", Utils.modifyNum(result.get("price").toString()));
            if (map.get("price").equals("0")) {
                map.put("price","面议");
            }
        }
        return map;
    }

    @RequestMapping(value = "/cloudMsg",method = RequestMethod.GET)
    public String cloudMsg(@Nullable @PathVariable("params") String params, Model model){
        return "instantMessaging/im//cloudMsg";
    }

    /**
     * 添加好友并发送所看房源信息
     */
    @ResponseBody
    @RequestMapping(value = "/imToAgent",method = RequestMethod.POST)
    public Object imToAgent(String mobile){

        //添加好友
        //imService.addFriend(sessionId,mobile);

        //发送消息
        //imService.sendMsg(sessionId,mobile,houseId,houseType);

        //查询目标联系人id
        imService.getAccid(mobile);

        return imService.getAccid(mobile);
    }

    /**
     * 添加好友并发送所看房源信息
     */
    @ResponseBody
    @RequestMapping(value = "/getMyIm",method = RequestMethod.POST)
    public Object getMyIm(String sessionId) {
        //获取自己的accid
        imService.getMyAccid(sessionId);

        return imService.getMyAccid(sessionId);
    }

    /**
     * 招聘聊呗
     */
    public static final String IM_RECRUIT_URL = "/imRecruit";
    @RequestMapping(value = {IM_RECRUIT_URL + "/{params}", IM_RECRUIT_URL},method = RequestMethod.GET)
    public String imRecruit(@Nullable @PathVariable("params") String params, Model model, HttpServletRequest request, HttpServletResponse response){
        ArrayList info_ims_recruit = new ArrayList();
        HttpSession session = request.getSession();
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = session.getAttribute("sessionId").toString();
            LinkedTreeMap<String, Object> map = (LinkedTreeMap) imService.getMyAccid(sessionId);
            try{
                if(!StringUtils.isEmpty(map.get("imProvinceState"))){
                    String user_state = map.get("imProvinceState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","此功能只针对辽宁、北京等特定地域的手机用户开通！详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                if(!StringUtils.isEmpty(map.get("imState"))){
                    String user_state = map.get("imState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","您的聊呗账号已禁用，如需开通详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                String accid = map.get("accid").toString();
                String token = map.get("token").toString();
                CookieManage.addCookie(response, "uid", accid, "/", "");
                CookieManage.addCookie(response, "sdktoken", token, "/", "");
            }catch (Exception e){
                return "instantMessaging/im/error";
            }
        }
        if (params != null && !params.equals("undefined")) {
            // 风云榜成员聊呗，嵌入这里了，可以单独拿出 2020/10/17
            if(params.startsWith("chatUp")){
                String[] param = params.split("-");
                String mobile = param[1];
                String type = "10";
                HashMap map = chatUp(mobile,type);
                info_ims_recruit.add(map);
                model.addAttribute("info_im_recruit",info_ims_recruit);
                return "instantMessaging/im/main";
            }
            String jobAdId = params;
            String type = "10";
            HashMap recruit = getImRecruit(jobAdId, type, model);
            info_ims_recruit.add(recruit);
            model.addAttribute("info_im_recruit",info_ims_recruit);
        }
        return "instantMessaging/im/main";
    }

    /**
     * 风云榜聊呗拉起(适用于所有无消息发起聊呗，mobile手机号 type默认10，干嘛用的我也不懂hhh)
    * <AUTHOR>
    * @Date 2020/10/17 13:32
    * @return java.util.HashMap
    **/
    public HashMap chatUp(String mobile,String type){
        HashMap map = new HashMap();
        map.put("jobAdId","");
        map.put("typeName","");
        map.put("jobTitle","");
        map.put("type",type);
        map.put("plateName","");
        //获取手机号
        if(!StringUtils.isEmpty(mobile))
            map.put("keeperTel2", mobile);
        LinkedTreeMap person = (LinkedTreeMap) imService.getAccid(mobile);
        //对方的accid
        map.put("accid",person.get("accid").toString());
        //职位详情链接地址
        map.put("linkUrl","");
        //职位名称
        map.put("typeName", "");
        //职位title
        map.put("jobTitle", "");
        //所属板块
        map.put("plateName", "");
        return map;
    }

    /**
     * 招聘职位信息
     * @param jobAdId
     * @param type
     * @param model
     * @return
     */
    public HashMap getImRecruit(String jobAdId,String type, Model model){
        LinkedTreeMap<String, Object> position_detail = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_JOB_DETAILS,
                new Params("jobAdId", jobAdId).get(), model);
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) position_detail.get("job");
        HashMap map = new HashMap();
        map.put("jobAdId",result.get("jobAdId"));
        map.put("typeName",result.get("typeName"));
        map.put("jobTitle",result.get("jobTitle"));
        map.put("type",type);
        map.put("plateName",result.get("plateName"));

        //获取手机号
        if(!StringUtils.isEmpty(result.get("companyLiaobeiMobile")))
            map.put("keeperTel2", result.get("companyLiaobeiMobile"));
            LinkedTreeMap person = (LinkedTreeMap) imService.getAccid(map.get("keeperTel2").toString());
        //对方的accid
        map.put("accid",person.get("accid").toString());
        //职位详情链接地址
        map.put("linkUrl","/positionDetail/"+jobAdId+".htm");
        //职位名称
        if(!StringUtils.isEmpty(result.get("typeName"))){
            map.put("typeName", result.get("typeName"));
        }
        //职位title
        if(!StringUtils.isEmpty(result.get("jobTitle"))){
            map.put("jobTitle", result.get("jobTitle"));
        }
        //所属板块
        if(!StringUtils.isEmpty(result.get("plateName"))){
            map.put("plateName", result.get("plateName"));
        }
        return map;
    }

    /**
     * 小区详情聊呗
     * @Date 2019.03.13
     */
    public static final String IM_POLT_URL = "/imPolt";
    @RequestMapping(value = {IM_POLT_URL + "/{params}", IM_POLT_URL},method = RequestMethod.GET)
    public String imPolt(@Nullable @PathVariable("params") String params, Model model, HttpServletRequest request, HttpServletResponse response){
        ArrayList info_ims_recruit = new ArrayList();
        HttpSession session = request.getSession();
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = session.getAttribute("sessionId").toString();
            LinkedTreeMap<String, Object> map = (LinkedTreeMap) imService.getMyAccid(sessionId);
            try{
                if(!StringUtils.isEmpty(map.get("imProvinceState"))){
                    String user_state = map.get("imProvinceState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","此功能只针对辽宁、北京等特定地域的手机用户开通！详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                if(!StringUtils.isEmpty(map.get("imState"))){
                    String user_state = map.get("imState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","您的聊呗账号已禁用，如需开通详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                String accid = map.get("accid").toString();
                String token = map.get("token").toString();
                CookieManage.addCookie(response, "uid", accid, "/", "");
                CookieManage.addCookie(response, "sdktoken", token, "/", "");
            }catch (Exception e){
                return "instantMessaging/im/error";
            }
        }
        if (params != null && !params.equals("undefined")) {
            String mobile = params;
            String type = "10";
            HashMap recruit = getPlot(mobile, type, model);
            info_ims_recruit.add(recruit);
            model.addAttribute("info_im_recruit",info_ims_recruit);
        }
        return "instantMessaging/im/main";
    }

    /**
     *小区详情聊呗(直接使用房产直聘中的聊呗类型)
     * @param mobile
     * @param type
     * @param model
     * @return
     */
    public HashMap getPlot(String mobile,String type, Model model){
        HashMap map = new HashMap();
        map.put("jobAdId","");
        map.put("typeName","");
        map.put("jobTitle","");
        map.put("type",type);
        map.put("plateName","");
        //获取手机号
        if(!StringUtils.isEmpty(mobile))
            map.put("keeperTel2", mobile);
        LinkedTreeMap person = (LinkedTreeMap) imService.getAccid(mobile);
        //对方的accid
        map.put("accid",person.get("accid").toString());
        //职位详情链接地址
        map.put("linkUrl","");
        //职位名称
        map.put("typeName", "");
        //职位title
        map.put("jobTitle", "");
        //所属板块
        map.put("plateName", "");
        return map;
    }


    /**
     * 返回当前登录用户的聊呗信息
     * @param model
     * @param request
     * @param response
     * @return
     */
    @ResponseBody
    @RequestMapping(value = IM_URL + "/serviceIM",method = RequestMethod.POST)
    public Object serviceIM(Model model, HttpServletRequest request, HttpServletResponse response){
        HttpSession session = request.getSession();
        LinkedTreeMap<String, Object> map = null;
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = session.getAttribute("sessionId").toString();
            map = (LinkedTreeMap) imService.getMyAccid(sessionId);
            try{
                if(!StringUtils.isEmpty(map.get("imProvinceState"))){
                    String user_state = map.get("imProvinceState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","此功能只针对辽宁、北京等特定地域的手机用户开通！详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                if(!StringUtils.isEmpty(map.get("imState"))){
                    String user_state = map.get("imState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","您的聊呗账号已禁用，如需开通详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                String accid = map.get("accid").toString();
                String token = map.get("token").toString();
                CookieManage.addCookie(response, "uid", accid, "/", "");
                CookieManage.addCookie(response, "sdktoken", token, "/", "");
            }catch (Exception e){
                return map;
            }
        }
        return map;
    }

    /**
     * 跳转到客服聊呗页面
     * @param param 客服类型 1：新房   2：二手房
     * @param model
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = {IM_URL + "/service" + "/{param}", IM_URL + "/service"},method = RequestMethod.GET)
    public String serviceIM1(@Nullable @PathVariable("param") String param, Model model, HttpServletRequest request, HttpServletResponse response){
        ArrayList info_ims = new ArrayList();
        HttpSession session = request.getSession();
        String sessionId = "";
        if (request.getSession().getAttribute("sessionId") != null) sessionId = session.getAttribute("sessionId").toString();
            LinkedTreeMap<String, Object> map = (LinkedTreeMap) imService.getMyAccid(sessionId);
            try{
                if(!StringUtils.isEmpty(map.get("imProvinceState"))){
                    String user_state = map.get("imProvinceState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","此功能只针对辽宁、北京等特定地域的手机用户开通！详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                if(!StringUtils.isEmpty(map.get("imState"))){
                    String user_state = map.get("imState").toString();
                    Integer stute = Integer.valueOf(user_state);
                    if(stute == 1){
                        model.addAttribute("error_msg","您的聊呗账号已禁用，如需开通详询致电：400-893-9709");
                        return "instantMessaging/im/error";
                    }
                }
                String accid = map.get("accid").toString();
                String token = map.get("token").toString();
                CookieManage.addCookie(response, "uid", accid, "/", "");
                CookieManage.addCookie(response, "sdktoken", token, "/", "");
                //param 1:新房    2:二手房
                if (!Strings.isNullOrEmpty(param) && "1".equals(param)){
                    model.addAttribute("service_im_accid","d508b774ceedaa3481cc67c3bc5a56c4");
                } else if (!Strings.isNullOrEmpty(param) && "2".equals(param)){
                    model.addAttribute("service_im_accid","4f55630619e2d3cb2d5cfd257c7552e9");
                }
            }catch (Exception e){
                return "instantMessaging/im/error";
            }
        return "instantMessaging/im/main";
    }

    /**
     * 聊呗 打开经纪人聊天框
     *
     * @param memberId 经纪人memberId
     * @param model    model对象
     * @param request  request对象
     * @param response response对象
     * @return java.lang.String
     * <AUTHOR> power
     * @date 2023-02-17
     */
    @RequestMapping(value = {IM_URL + "/chat" + "/{memberId}", IM_URL + "/chat"}, method = RequestMethod.GET)
    public String agentIM(@Nullable @PathVariable("memberId") String memberId, Model model, HttpServletRequest request, HttpServletResponse response) {
        // 获取sessionId
        HttpSession session = request.getSession();
        String sessionId = "";
        if (request.getSession().getAttribute("sessionId") != null) {
            sessionId = session.getAttribute("sessionId").toString();
        }
        try {
            LinkedTreeMap<String, Object> map = (LinkedTreeMap) imService.getMyAccid(sessionId);
            // 判断当前用户 聊呗属地与状态
            if (!StringUtils.isEmpty(map.get("imProvinceState")) && "1".equals(map.get("imProvinceState").toString())) {
                model.addAttribute("error_msg", "此功能只针对辽宁、北京等特定地域的手机用户开通！详询致电：400-893-9709");
                return "instantMessaging/im/error";
            }
            if (!StringUtils.isEmpty(map.get("imState")) && "1".equals(map.get("imState").toString())) {
                model.addAttribute("error_msg", "您的聊呗账号已禁用，如需开通详询致电：400-893-9709");
                return "instantMessaging/im/error";
            }
            String accid = map.get("accid").toString();
            String token = map.get("token").toString();
            CookieManage.addCookie(response, "uid", accid, "/", "");
            CookieManage.addCookie(response, "sdktoken", token, "/", "");
            // 查询经纪人聊呗信息
            if (!Strings.isNullOrEmpty(memberId)) {
                LinkedTreeMap person = (LinkedTreeMap) imService.checkAccid(memberId);
                if (person != null && !StringUtils.isEmpty(person.get("accid"))) {
                    model.addAttribute("service_im_accid", person.get("accid").toString());
                }
            }
        } catch (Exception e){
            return "instantMessaging/im/error";
        }
        return "instantMessaging/im/main";
    }

    /**
     * json登录聊呗
     *
     * @param model
     * @param request
     * @param response
     * @param serviceType
     * @param houseId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = {IM_URL + "/new/service/{serviceType}"}, method = RequestMethod.POST)
    public HashMap serviceIMNew(Model model, HttpServletRequest request, HttpServletResponse response,
                                @Nullable @PathVariable("serviceType") String serviceType, String houseId) {
        HashMap<String, Object> resultMap = new HashMap<>();
        if (!StringUtils.isEmpty(serviceType) && "1".equals(serviceType)) {
            resultMap = imService.serviceIMNew(request, response, houseId);
        } else if (!StringUtils.isEmpty(serviceType) && "2".equals(serviceType)){
            resultMap = imService.serviceIMSec(request, response);
        }
        return resultMap;
    }

}
