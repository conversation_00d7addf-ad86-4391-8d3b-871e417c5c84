package com.fangxiaoer.controller;

import com.fangxiaoer.model.TouristOrder;
import com.fangxiaoer.service.HouseService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

@Controller
public class TouristController {

    @Autowired
    private HouseService houseService;
    /**
     * 旅居地产首页
     * @param model
     * @return
     * <AUTHOR> 2019-05-28
     */
    @RequestMapping(value = "/tourist.htm", method = RequestMethod.GET)
    public String viewTouristIndex(Model model, String systemDate) {
        houseService.viewIndexAD(10, model, systemDate);
        houseService.viewIndexAD(11, model, systemDate);
        houseService.touristProjectList("", 1, 4, model);
        houseService.touristNewsList(model);
        return "sojourn/index";
    }

    @RequestMapping(value = {"/tNews.htm", "/tNews{id}.htm", "/tNews{id}/-n{page:[0-9]+}"})
    public String viewTNewsList(@PathVariable @Nullable Integer id, Model model, String systemDate, @Nullable @PathVariable Integer page) {
        if (id == null) id = 1;
        houseService.viewIndexAD(13, model, systemDate);
        houseService.viewNewsFilter(model);
        houseService.viewTNewsList(id, page == null ? 1 : page,10,  model);
        houseService.recommendProjects(model, systemDate);
        model.addAttribute("categoryId", id);
        return "sojourn/living_list";
    }

    @RequestMapping(value = {"/touristList", "/touristList/-n{page:[0-9]+}"}, method = RequestMethod.GET)
    public String viewTouristList(Model model, @Nullable @PathVariable Integer page) {
        houseService.touristProjectList("", page == null ? 1 : page, 20, model);
        model.addAttribute("header", "旅居置业");
        return "sojourn/lvju_list";
    }

    @ResponseBody
    @RequestMapping(value = "/touristGuideCount", method = RequestMethod.POST)
    public HashMap<String, Object> touristGuideCount() {
        return houseService.touristGuideCount();
    }


    @RequestMapping("/tourist/{id:[0-9]+}.htm")
    public String touristProjectDetail(@PathVariable Integer id, Model model) {
        LinkedTreeMap<String, Object> project = houseService.viewTouristProject(id, model);
        model.addAttribute("header", project.get("projectName"));
        houseService.projectBrief(id, model);
        return "sojourn/details";
    }

    @RequestMapping("/tNews/{id}.htm")
    public String viewNewsDetail(@PathVariable Integer id, Model model, String systemDate) {
        String result = houseService.viewNewsDetail(id, model);
        houseService.recommendProjects(model, systemDate);
        if (StringUtils.isEmpty(result)) {
            return "redirect:/";
        } else if (result.equals("normal")) {
            model.addAttribute("header", "资讯详情");
            model.addAttribute("newsId", id);
            return "sojourn/living_details";
        } else {
            return "redirect:" + result;
        }

    }

    @RequestMapping(value = "/tourist/{id}/info.htm", method = RequestMethod.GET)
    public String touristProjectInfo(@PathVariable Integer id, Model model) {
        houseService.viewProjectInfo(id, model);
        houseService.projectBrief(id, model);
        model.addAttribute("header", "楼盘详情");
        return "sojourn/details_det";
    }

    @RequestMapping(value = {"/tourist/{id}/ask.htm", "/tourist/{id}/ask/{page:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String viewAskList(@PathVariable @Nullable Integer id, Model model, @Nullable @PathVariable Integer page) {
        houseService.viewAskList(id, page == null ? 1 : page, 10,  model);
        houseService.projectBrief(id, model);
        model.addAttribute("header", "楼盘问答").addAttribute("projectId", id);
        return "sojourn/details_ask";
    }


    @RequestMapping(value = {"/tourist/{id}/comment.htm", "/tourist/{id}/comment/{page:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String touristCommentList(@PathVariable @Nullable Integer id, Model model, @Nullable @PathVariable Integer page){
        houseService.viewCommentList(id, page == null ? 1 : page, 10,  model);
        houseService.projectBrief(id, model);
        return "sojourn/details_comment";
    }


    @RequestMapping(value = {"/tourist/{id}/dy.htm", "/tourist/{id}/dy-{dyType}.htm", "/tourist/{id}/dy/{page:[0-9]+}.htm", "/tourist/{id}/dy-{dyType}/{page:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String touristProjectDy(@PathVariable Integer id, @Nullable @PathVariable Integer dyType, Model model, @Nullable @PathVariable Integer page) {
        if (StringUtils.isEmpty(dyType)) {
            dyType = 1;
        }
        if (StringUtils.isEmpty(page)){
            page = 1;
        }
        houseService.viewProjectDy(id, page,10, dyType, model);
        houseService.projectBrief(id, model);
        houseService.viewDYFilter(id, model);
        model.addAttribute("projectId", id).addAttribute("dyType", dyType).addAttribute("header", "最新动态");
        return "sojourn/details_dyn";
    }


    @RequestMapping(value = {"/tourist/layout-{id}/{params}", "/tourist/layout-{id}/{params}.htm"}, method = RequestMethod.GET)
    public String viewLayoutDetail(@PathVariable Integer id, @Nullable @PathVariable("params") String params, Model model, HttpServletRequest request) {
        if (params == null) params = "";
        model.addAttribute("link", params);
        String requestPath = request.getServletPath();
        String baseUrl = requestPath.replaceFirst(params, "");
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        model.addAttribute("projectId", id);
        houseService.getLayoutDetail(baseUrl, params, model);
        houseService.projectBrief(id, model);
        model.addAttribute("header", "户型");
        return "sojourn/details_layout";
    }

    @RequestMapping(value = {"/tourist/{projectId:\\d+}/album.htm", "/tourist/{projectId:\\d+}/album/{photoType}.htm"}, method = RequestMethod.GET)
    public String fetchProjectPics(@PathVariable Integer projectId,@Nullable @PathVariable String photoType, Model model) {
        if (photoType == null) photoType = "";
        houseService.projectBrief(projectId, model);
        model.addAttribute("photoType", photoType);
        model.addAttribute("projectId", projectId);
        houseService.getTouristPhotos(photoType, projectId, model);
        return "sojourn/details_album";
    }

    @RequestMapping(value = "/tourist/{projectId:\\d+}/pushcomment", method = RequestMethod.GET)
    public String commentProject(@PathVariable Integer projectId, Model model){
        houseService.projectBrief(projectId, model);
        LinkedTreeMap<String,Object> commentInfo = new LinkedTreeMap<>();
        commentInfo.put("commentDesc", "");
        commentInfo.put("isNiming", null);
        model.addAttribute("commentInfo",commentInfo);
        model.addAttribute("projectId", projectId);
        return "sojourn/mycomment";
    }

    @RequestMapping(value = "/tourist/editComment/{commentId}", method = RequestMethod.GET)
    public String editComment(@PathVariable Integer commentId, Model model) {
        houseService.viewTouristCommentInfo(commentId, model);
        model.addAttribute("commentId", commentId);
        return "sojourn/mycomment";
    }

    /**
     * 个人楼盘评价保存
     */
    @ResponseBody
    @RequestMapping(value = "/tourist/saveComment", method = RequestMethod.POST)
    public HashMap saveComment(@RequestBody HashMap params) {
        HashMap result = new HashMap();
        if (StringUtils.isEmpty(params.get("sessionId"))) {
            result.put("status", 0);
            result.put("msg", "请登录");
        }else{
            result = houseService.saveTouristComment(params);
        }
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "/getTouristFilter", method = RequestMethod.POST)
    public HashMap<String, Object> getTouristFilter() {
        return houseService.getTouristFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/addTouristOrder", method = RequestMethod.POST)
    public HashMap<String, Object> addTouristOrder(TouristOrder touristOrder) {
        return houseService.addOrder(touristOrder);
    }

    @ResponseBody
    @RequestMapping(value = "/orderProject", method = RequestMethod.POST)
    public HashMap<String, Object> orderProject(String cityName) {
        return houseService.touristProjectList(cityName, 1, 50);
    }

    @ResponseBody
    @RequestMapping(value = "/addTouristAsk",method = RequestMethod.POST)
    public HashMap<String, Object> addAsk(Integer projectId, String askInfo, String sessionId){
        HashMap<String, Object> result = new HashMap<>();
        if(StringUtils.isEmpty(sessionId)){
            result.put("status",0);
            result.put("msg","请登录");
        }else{
            result =  houseService.addAsk(sessionId, projectId, askInfo);
        }
        return result;
    }

}
