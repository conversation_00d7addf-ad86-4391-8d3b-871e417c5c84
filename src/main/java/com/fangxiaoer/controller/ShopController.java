package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.service.SecondHouseService;
import com.fangxiaoer.service.ShopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

import static com.fangxiaoer.common.Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT;

/**
 * 商铺相关
 */
@Controller
public class ShopController {
    @Autowired
    private ShopService shopService;
    @Autowired
    private SecondHouseService secondHouseService;
    public static String SHOP_URL = "/shops/";
    /**
     * 商铺列表页初始化 M站链接适配
     * @param model
     * @return
     * <AUTHOR> 2017-9-19
     */
    @RequestMapping(value = {"/shops/{params}","/shops"}, method = RequestMethod.GET)
    public String shops(@Nullable @PathVariable String params, Model model, HttpServletRequest request,String systemDate){
        if(params == null) params = "";
        String baseUrl = ShopController.SHOP_URL ;
        shopService.getShops(baseUrl,params,model,request);
        HashMap<String, Object> resultMap;
        if(systemDate != null){
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("systemDate",systemDate).add("type",4).get(),true);

        }else {
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("type",4).get(),true);
        }
        model.addAttribute("advert",resultMap.get("content"));
        /*传递参数到页面上*/
        model.addAttribute("mobileAgent",params);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang4/"+params);
        return "shop/shoplist";
    }

    /**
     * 商铺成交列表页 M站链接适配
     * @param model
     * @return
     * <AUTHOR> 2017-9-19
     */
    @RequestMapping(value = {"/dealShops/{params}","/dealShops"}, method = RequestMethod.GET)
    public String dealShops(@Nullable @PathVariable String params, Model model, HttpServletRequest request, String systemDate){
        if(params == null) params = "";
        String baseUrl = "/dealShops/" ;
        shopService.getDealShops(baseUrl,params,model,request);
        HashMap<String, Object> resultMap;
        if(systemDate != null){
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("systemDate",systemDate).add("type",4).get(),true);

        }else {
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("type",4).get(),true);
        }
        model.addAttribute("advert",resultMap.get("content"));
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/txnShop");
        return "shop/dealshops";
    }


    /**
     * 商铺详情页 M站链接适配
     * <AUTHOR> 2017-9-19
     */
    @RequestMapping(value = "/shop/{shopId}.htm", method = RequestMethod.GET)
    public String shop(@PathVariable String shopId, Model model,String systemDate){
        shopService.getShopDetail(shopId, model);
        secondHouseService.getRightAdvert(systemDate,model,3);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang4/"+shopId+".htm");
        return "shop/shop";
    }

    /**
     * 商铺成交详情页
     * <AUTHOR> 2017-9-19
     */
    @RequestMapping(value = "/dealShop/{shopId}.htm", method = RequestMethod.GET)
    public String dealShop(@PathVariable String shopId, Model model, HttpServletRequest request){
        shopService.getDealShopDetail(shopId, model);
        String sessionId = HttpUtil.checkSessionId(request);
        if (!StringUtils.isEmpty(sessionId)){
            Integer houseid = Integer.parseInt(shopId);
            HttpUtil.connectApi(Constants.ADD_DEAL_DRTAIL_ORDER, new Params("houseId", houseid).add("sessionId",sessionId).add("houseType",1).get(), model);
        }
        return "shop/dealshop";
    }
}
