package com.fangxiaoer.controller;

import com.fangxiaoer.service.NeedHouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpSession;

/**
 * 求租求购
 * <AUTHOR>
 * @Date 2018/7/19
 */
@Controller
public class NeedHouseController {
    @Autowired
    private NeedHouseService needHouseService;

    /**
     * 求购列表页
     */
   /* @RequestMapping(value = {"/needSeconds/{params}","/needSeconds"}, method = RequestMethod.GET)
    public String needSeconds(@Nullable @PathVariable("params") String params, Model model) {
        if (params == null) params = "";
        model.addAttribute("needType",0);//标记是求购
        String baseUrl = "/needSeconds/";
        needHouseService.getNeedHouses(baseUrl, params, model);
        return "needHouse/needHouses";
    }*/


    /**
     * 求购详情页
     */
    @RequestMapping(value = "/needSecond/{param}.htm", method = RequestMethod.GET)
    public String needSecond(@PathVariable ("param") String param, Model model, HttpSession session){
        String sessionId = !StringUtils.isEmpty(session.getAttribute("sessionId"))? session.getAttribute("sessionId").toString() : "";
        model.addAttribute("needType",0);//标记是求购
        needHouseService.getNeedHouse(sessionId,param,model);
        return "needHouse/needHouse";
    }


    /**
     * 求租列表页
     */
  /*  @RequestMapping(value = {"/needRents/{params}","/needRents"}, method = RequestMethod.GET)
    public String needRents(@Nullable @PathVariable("params") String params, Model model) {
        if (params == null) params = "";
        model.addAttribute("needType",1);//标记是求租
        String baseUrl = "/needRents/";
        needHouseService.getNeedHouses(baseUrl, params, model);
        return "needHouse/needHouses";
    }*/

    /**
     * 求租详情页
     */
    @RequestMapping(value = "/needRent/{param}.htm", method = RequestMethod.GET)
    public String needRent(@PathVariable ("param") String param, Model model, HttpSession session){
        String sessionId = !StringUtils.isEmpty(session.getAttribute("sessionId"))? session.getAttribute("sessionId").toString() : "";
        model.addAttribute("needType",1);//标记是求租
        needHouseService.getNeedHouse(sessionId,param,model);
        return "needHouse/needHouse";
    }
}
