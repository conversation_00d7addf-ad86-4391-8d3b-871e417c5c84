package com.fangxiaoer.controller;

import com.fangxiaoer.common.ValidateUtil;
import com.fangxiaoer.model.Comment;
import com.fangxiaoer.model.Guide;
import com.fangxiaoer.model.search.AskSearchModel;
import com.fangxiaoer.model.search.CommentCountModel;
import com.fangxiaoer.model.search.CommentSearchModel;
import com.fangxiaoer.service.ActivityService;
import com.fangxiaoer.service.AgentService;
import com.fangxiaoer.service.HouseService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 新房所有控制器
 */
@Controller
public class HouseController {

    @Autowired
    private HouseService houseService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private AgentService agentService;

    /**
     * 新房列表 （mobileUrl配置完成）
     */
    public static final String NEW_HOUSE_URL = "/houses";
    public static final String BRAND_HOUSE_URL = "/brands"; //品牌房企
    public static final String SUBWAY_HOUSE_URL = "/subways"; //地铁沿线
    public static final String EXITS_HOUSE_URL = "/exits"; //现房列表
    public static final String LOWPAY_HOUSE_URL = "/lowpays"; //低首付

    @RequestMapping(value = {
            NEW_HOUSE_URL + "/{params}", NEW_HOUSE_URL,
            SUBWAY_HOUSE_URL + "/{params}", SUBWAY_HOUSE_URL,
            EXITS_HOUSE_URL + "/{params}", EXITS_HOUSE_URL,
            LOWPAY_HOUSE_URL + "/{params}", LOWPAY_HOUSE_URL,
            BRAND_HOUSE_URL + "/{params}", BRAND_HOUSE_URL},
            method = RequestMethod.GET)
    public String showHouseList(HttpServletRequest request, @Nullable @PathVariable("params") String params, Model model, String systemDate) {
        if (params == null) params = "";
        if (Pattern.matches("^[^a-zA-Z]+$", params)) {
            return "redirect:" + EXITS_HOUSE_URL;
        }
        String requestPath = request.getServletPath();
        String baseUrl = requestPath.replaceFirst(params, "");
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        String returnUrl = "house/newhouse";
        if (baseUrl.equals("/exits/")) {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1l/-ief1-" + params);
        } else if (baseUrl.equals("/subways/")) {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1dt" + params);
            returnUrl = "house/metroHouse";
            houseService.getMetroAds(model);
        } else if (baseUrl.equals("/lowpays/")) {
            model.addAttribute("mobileUrl", " https://m.fangxiaoer.com/fang1l/ilp1-" + params);
        } else {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1l/" + params);
        }
        houseService.getNewHouse(baseUrl, params, model, systemDate);
        model.addAttribute("contrastHouse", "newhouse");//判断是否是新房，是否显示对比房源悬浮窗。
        model.addAttribute("pageType", "1");
        /*传递参数到页面上*/
        model.addAttribute("mobileAgent", params);
        houseService.usuallySearchProject(model);
        /*小二精选*/
        houseService.newProjectDynamics(model);
        return returnUrl;
    }

    /**
     * 精装房列表 （mobileUrl配置完成）
     */
    @RequestMapping(value = {"/decorations", "/decorations/{params}"}, method = RequestMethod.GET)
    public String showDecorationList(@Nullable @PathVariable("params") String params, Model model, String systemDate) {
        if (params == null) params = "";
        houseService.getDecorations("/decorations/", params, model, systemDate);
        /*传递参数到页面上*/
        model.addAttribute("mobileAgent", params);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/decorates/" + params);
        return "house/decoration";
    }

    /**
     * 学区房列表页
     */
    @RequestMapping(value = {"/schools", "/schools/{params}"}, method = RequestMethod.GET)
    public String schools(@Nullable @PathVariable("params") String params, Model model, String systemDate) {
        if (params == null) params = "";
        houseService.getSchools("/schools/", params, model, systemDate);
        return "house/school";
    }

    /**
     * 优质学区学校关联房型列表页
     */
    @RequestMapping(value = "/schoolhouses/{params}")
    public String schoolhouse(@PathVariable("params") String params, Model model) {
        houseService.getSchoolHouse("/schools/", params, model);
        model.addAttribute("contrastHouse", "newhouse");
        return "house/schoolhouse";
    }

    /**
     * 精装房简介 （mobileUrl配置完成）
     */
    @RequestMapping(value = {"/house/{projectId:[0-9]+}-{projectType:[0-9]+}/decoration.htm"}, method = RequestMethod.GET)
    public String decorationDetail(@PathVariable String projectId, @PathVariable String projectType, Model model) {
        if (projectType == null) projectType = "1";
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        if ("1".equals(projectType)) {
            houseService.newHouseDetail(projectId, model);
        } else if ("3".equals(projectType)) {
            houseService.viewApartmentDetail(projectId, model);
        }
        houseService.viewDecoration(projectId, model);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-" + projectType + ".htm");
        return "house/detail/decoration";
    }

    /**
     * 精装房简介
     */
    @RequestMapping(value = "/decolayout/{layoutId}.htm", method = RequestMethod.GET)
    public String jzDetail(@PathVariable String layoutId, Model model) {
        houseService.viewDecoLayout(layoutId, model);
        return "house/detail/decolayout";
    }

    /**
     * 普房详细页 （mobileUrl配置完成）
     */
    @RequestMapping(value = {"/house/{projectId:[0-9]+}.htm", "/house/{projectId}-{projectType}.htm"}, method = RequestMethod.GET)
    public String showHouseDetail(HttpServletRequest request, @PathVariable("projectId") String projectId, @PathVariable(required = false) String projectType, Model model) {
        String url = "";
        if (projectType == null) projectType = "1";
        String sessionId=StringUtils.isEmpty(request.getSession().getAttribute("muser"))?"":(String)request.getSession().getAttribute("muser");
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        model.addAttribute("contrastHouse", "normalHouse");// 用来判断是否在详情页
        houseService.checkBuildType(projectId, model);
        houseService.selectionProjectInfo(projectId, model);
        model.addAttribute("mobileSelectionUrl", "https://m.fangxiaoer.com/evaluatingProjectInfo/" + projectId);
//        model.addAttribute("mobileSelectionUrl", "http://************:8077/evaluatingProjectInfo/" + projectId);
        if ("1".equals(projectType)) {
            houseService.newHouseDetail(projectId, model);
            houseService.handleNormalHousePics(model);
            String vip = (String) ((LinkedTreeMap<String, Object>) (model.asMap().get("houseInfo"))).get("vip");
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-1.htm");
            if ("0".equals(vip)) {
                url= "house/detail/normalHouse";
            } else {
                url= "house/detail/newVipHouse";
            }
        } else if ("2".equals(projectType)) {
            houseService.viewVillaDetail(projectId, model);
            houseService.handleVillaPicsToMap(model);
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-2.htm");
            url =  "house/villa/villaIndex";
        } else if ("3".equals(projectType)) {
            houseService.viewApartmentDetail(projectId, model);
            houseService.handleNormalHousePics(model);
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-3.htm");
            url= "house/detail/apartment";
        }else{
            ValidateUtil.throwException("");
        }
        //在线支付Map<"projectPay",List<Map>>
        activityService.projectPayCheck(Integer.valueOf(projectId), model);
        //抢优惠活动Map<"viewActivity",List<Map>>
        activityService.viewActivity(sessionId,projectId,projectType,"11", model);
        return url;
    }

    /**
     * 基本信息 （mobileUrl配置完成）
     */
    @RequestMapping(value = "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/info.htm", method = RequestMethod.GET)
    public String getBase(@PathVariable("projectId") String projectId, @PathVariable(required = false) String projectType, Model model) {
        if (projectType == null) projectType = "1";
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-" + projectType + ".htm");
        if ("1".equals(projectType)) {
            houseService.newHouseDetail(projectId, model);
            return "house/detail/project_information";
        } else if ("2".equals(projectType)) {
            houseService.viewVillaInfo(projectId, model);
            houseService.viewLayoutType(projectId, projectType, model);
            return "house/villa/villa_information";
        } else if ("3".equals(projectType)){
            houseService.viewApartmentDetail(projectId, model);
            houseService.viewLayoutType(projectId, projectType, model);
            return "house/detail/office_information";
        }
        return "";
    }

    /**
     * 户型图列表 （mobileUrl配置完成）
     */
    @RequestMapping(value = {"/house/{projectId:[0-9]+}-{projectType:[0-9]+}/layout.htm", "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/layout/{params}"}, method = RequestMethod.GET)
    public String getRoomList(@PathVariable("projectId") String projectId, @PathVariable("projectType") String projectType, @Nullable @PathVariable("params") String params, Model model) {
        model.addAttribute("projectId", projectId);
        String url = "house/detail/roomInfo";
        if (projectType != null && projectType.equals("2")) {
            if (StringUtils.isEmpty(params)) {
                params = "pid" + projectId + "-pt" + projectType;
            }
            model.addAttribute("villaShipei", '2');
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/nphotos/" + projectId + "-wxt-2.htm");
            houseService.viewVillaDetail(projectId, model);
            url = "house/villa/layout";
        }else if(projectType != null && projectType.equals("3")){
            houseService.viewApartmentDetail(projectId, model);
            url = "house/detail/office_layout";
        }else{
            houseService.viewHouseInfo(projectId, model);
        }
        houseService.getRoomType("/house/layout/", params, model);
        houseService.villaSubLayout(projectId, (String) model.asMap().get("layId"), model);
        return url;
    }


    /**
     * 获取
     *
     * @param layId
     * @param model
     * @return
     */
    @RequestMapping(value = "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/sublayout/{layId}.htm", method = RequestMethod.GET)
    public String villaSubLayout(@PathVariable("projectId") String projectId, @PathVariable("projectType") String projectType, @PathVariable("layId") String layId, Model model) {
        houseService.villaSubLayout(projectId, layId, model);
        return "house/villa/subLayout";
    }

    /**
     * 项目评价 （mobileUrl配置完成）
     *
     * @param projectId
     * @param model
     * @return
     */
    @RequestMapping(value = {"/house/{projectId:[0-9]+}-{projectType:[0-9]+}/appraise{params}.htm"}, method = RequestMethod.GET)
    public String houseAppraiseTest(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable String params, Model model) {
        String baseUrl = "/house/" + projectId + '-' + projectType + "/appraise";
        if (projectType == null) projectType = "1";
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-" + projectType + "/appraise.htm");
        if (params == null) params = "";
        houseService.getCommentList(baseUrl, params, model, projectId, "10");
        if ("1".equals(projectType)) {
            houseService.viewHouseInfo(projectId, model);
        } else if ("3".equals(projectType)) {
            houseService.viewApartmentInfo(projectId, model);
        } else{
            return "redirect:/house/" + projectId + "-" + projectType+".htm#xmpj";
        }
        model.addAttribute("endUrl", ".htm");
        return "house/detail/appraise";
    }

    /**
     * 项目咨询 （mobileUrl配置完成）
     *
     * @param projectId
     * @param model
     * @return
     */
    @RequestMapping(value = {"/house/{projectId:[0-9]+}-{projectType:[0-9]+}/ask.htm", "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/ask/{page:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String houseAsk(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable Integer page, Model model) {
        if (projectType == null) projectType = "1";
        if (page == null) page = 1;
        houseService.AskHandler(projectId, projectType, page, model, 10);
        if ("1".equals(projectType)) {
            houseService.viewHouseInfo(projectId, model);
        } else if ("3".equals(projectType)) {
            houseService.viewApartmentInfo(projectId, model);
        }
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-" + projectType + "/ask.htm");
        return "house/detail/projectAsk";
    }

    /**
     * 销售动态
     *
     * @param projectId
     * @param model
     * @return
     */
    @RequestMapping(value = {"/house/{projectId:[0-9]+}-{projectType:[0-9]+}/news.htm", "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/{page:[0-9]+}.htm", "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/dy{dyType}.htm", "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/dy{dyType}-n{page:[0-9]+}"}, method = RequestMethod.GET)
    public String villaNews(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable String dyType, @Nullable @PathVariable Integer page, Model model) {
        String url = "";
        if (projectType == null) projectType = "1";
        if (page == null) page = 1;
        Pattern pattern = Pattern.compile("^-?\\d+(\\.\\d+)?$");
        if (!StringUtils.isEmpty(dyType)) {
            if (!pattern.matcher(dyType).matches())
                dyType = null;
        }
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        model.addAttribute("dyType", dyType);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-" + projectType + "/news.htm");
        if ("2".equals(projectType)) {
            houseService.viewVillaInfo(projectId, model);
//            houseService.viewProjectSaleInfo(projectId, projectType, page, "/house/news/", model);
            url = "house/villa/villaNew";
        } else if ("1".equals(projectType)) {
            houseService.viewHouseInfo(projectId, model);
//            houseService.viewProjectSaleInfo(projectId, projectType, page, "/house/news/", model);
            url = "house/detail/newHouseSaleInfo";
        } else if ("3".equals(projectType)) {
            houseService.viewApartmentInfo(projectId, model);
//            houseService.viewProjectSaleInfo(projectId, projectType, page, "/house/news/", model);
            url = "house/detail/newHouseSaleInfo";
        }
//        houseService.viewProjectSaleInfo(projectId, projectType, page, "/house/news/", model);
        houseService.viewProjectSaleInfo(projectId, projectType, page, dyType, "/house/", model, null);
        return url;
    }


    /**
     * 楼盘说说
     */
    @RequestMapping(value = {"/house/{projectId:[0-9]+}-{projectType:[0-9]+}/say.htm", "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/say/{page:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String getSay(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable Integer page, Model model) {
        if (page == null) page = 1;
        if (projectType == null) projectType = "1";
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        if ("1".equals(projectType)) {
            houseService.viewHouseInfo(projectId, model);
        } else if ("3".equals(projectType)) {
            houseService.viewApartmentInfo(projectId, model);
        }
        houseService.getSay("/house/say/", projectId, projectType, page, model);
        return "house/detail/sayNew";
    }

    /**
     * 720°看房 （mobileUrl配置完成）
     */
    @RequestMapping(value = {"/house/{projectId:[0-9]+}-{projectType:[0-9]+}/pic720.htm", "/house/{projectId:[0-9]+}-{projectType:[0-9]+}/pic720/{panId:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String getPicture(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable String panId, Model model) {
        if (projectType == null) projectType = "1";
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        model.addAttribute("panId", panId);
        this.searchHouseInfoByType(projectType, projectId, model);
        houseService.viewPan(projectId, projectType, panId, model);
        if (StringUtils.isEmpty(panId)) {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-" + projectType + "/pic720.htm");
        } else {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-" + projectType + "/pic720/" + panId + ".htm");
        }
        if (projectType.equals("2")) {
            return "house/villa/pic720";
        } else {
            return "house/detail/pic720";
        }
    }

    /**
     * 在线选房列表(废弃）
     */
    @RequestMapping(value = "/house/onlines/{params}", method = RequestMethod.GET)
    public String getOnlineInfo(@PathVariable("params") String params, Model model) {
        houseService.getOnlineInfo("/house/onlines/", params, model);
        return "house/detail/onlineInfoNew";
    }

    /**
     * 在线选房详情页(废弃）
     */
    @RequestMapping(value = "/house/online/{projectId}-{projectType}-{roomId}.htm", method = RequestMethod.GET)
    public String getRoomDetail(@PathVariable String projectId, @PathVariable String projectType, @PathVariable String roomId, Model model) {
        if (projectType == null) projectType = "1";
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        if ("1".equals(projectType)) {
            houseService.viewHouseInfo(projectId, model);
            if (model.asMap().get("houseInfo") == null) {
                houseService.viewApartmentInfo(projectId, model);
                model.addAttribute("projectType", 3);
            }
        } else if ("3".equals(projectType)) {
            houseService.viewApartmentInfo(projectId, model);
        }
        houseService.getRoomDetail(projectId, projectType, roomId, model);
        return "house/detail/onlineDetail";
    }

    /**
     * 相册 （mobileUrl配置完成）
     */
    @RequestMapping(value = {"/house/{projectId:\\d+}-{projectType:\\d+}/album.htm", "/house/{projectId:\\d+}-{projectType:\\d+}/album/{photoType}.htm"}, method = RequestMethod.GET)
    public String getPhoto(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable String photoType, Model model) {
        if (projectType == null) projectType = "1";
        if (photoType == null) photoType = "";
        model.addAttribute("projectId", projectId);
        model.addAttribute("projectType", projectType);
        model.addAttribute("photoType", photoType);
        if (!StringUtils.isEmpty(photoType)) {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/nphotos/" + projectId + "-" + photoType + "-" + projectType + ".htm");
        } else {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/nphotos/" + projectId + "-xgt-" + projectType + ".htm");
        }
        if (projectType.equals("2")) {
            houseService.viewVillaInfo(projectId, model);
            houseService.getPhotos(photoType, projectId, projectType, model);
            return "house/villa/villaPhoto";
        }
        houseService.getPhotos(photoType, projectId, projectType, model);
        return "house/detail/album";
    }

    /**
     * 楼盘评价个人
     */
    @RequestMapping(value = "/comment/{projectId}/{projectType}", method = RequestMethod.GET)
    public String comment(@PathVariable String projectId, @PathVariable String projectType, Model model) {
        this.searchHouseInfoByType(projectType, projectId, model);
        model.addAttribute("commentInfo", new Comment());
        return "house/detail/comment";
    }

    /**
     * 楼盘评价经纪人
     */
    @RequestMapping(value = "/commentAgent/{projectId}/{projectType}", method = RequestMethod.GET)
    public String commentAgent(@PathVariable String projectId, @PathVariable String projectType, Model model) {
        this.searchHouseInfoByType(projectType, projectId, model);
        model.addAttribute("commentInfo", new Comment());
        return "house/detail/commentAgent";
    }

    @RequestMapping(value = "/editComment/{commentId}", method = RequestMethod.GET)
    public String editComment(@PathVariable Integer commentId, Model model) {
        houseService.viewCommentInfo(commentId, model);
        return "house/detail/comment";
    }

    @RequestMapping(value = "/editAgentComment/{commentId}", method = RequestMethod.GET)
    public String editAgentComment(@PathVariable Integer commentId, Model model) {
        houseService.viewCommentInfo(commentId, model);
        return "house/detail/commentAgent";
    }

    /**
     * 按类型查询房屋详情数据
     *
     * @param projectType
     * @param projectId
     * @param model
     */
    public void searchHouseInfoByType(String projectType, String projectId, Model model) {
        if (projectType.equals("1")) {
            houseService.viewHouseInfo(projectId, model);
        } else if (projectType.equals("2")) {
            houseService.viewVillaInfo(projectId, model);
        } else {
            houseService.viewApartmentInfo(projectId, model);
        }
    }

    /**
     * 特价房
     *
     * @return
     */
    @RequestMapping(value = "/tjf", method = RequestMethod.GET)
    public String viewParticularHouse() {
        return "house/particularHouse";
    }

    /**
     * 成交排行
     */
    @RequestMapping(value = "/viewDealRankingList", method = RequestMethod.GET)
    public String viewDealRankingList(Model model) {
        houseService.viewDealRankingList(model);
        return "agent/agentDealRanking";
    }

    /**
     * 查询评论各类别的数量
     * +带条件查询
     *
     * @param commentCountModel
     * @return
     */
    @RequestMapping(value = "/getCommentCount", method = RequestMethod.POST)
    @ResponseBody
    public LinkedTreeMap<String, Object> getCommentCount(CommentCountModel commentCountModel) {
        return houseService.getCommentCount(commentCountModel);
    }

    /**
     * 查询咨询各类别的数量
     *
     * @param projectId
     * @return
     */
    @RequestMapping(value = "/getAskCount", method = RequestMethod.POST)
    @ResponseBody
    public LinkedTreeMap<String, Object> getAskCount(@RequestParam String projectId) {
        return houseService.getAskCount(projectId);
    }

    /**
     * 获取评论信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getCommentInfo", consumes = MediaType.ALL_VALUE, method = RequestMethod.POST, produces = "text/html;charset=UTF-8;")
    @ResponseBody
    public String CommentHandler(CommentSearchModel commentSearchModel) {
        return houseService.CommentHandler(commentSearchModel);
    }

    /**
     * 获取评论信息
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/getAgentInfo", method = RequestMethod.POST, produces = "text/html;charset=UTF-8;")
    @ResponseBody
    public String getAgentInfo(@RequestParam String agentId) {
        return houseService.getAgentInfo(agentId);
    }

    /**
     * 获取咨询信息
     *
     * @param askSearchModel
     * @return
     */
    @RequestMapping(value = "/getAskInfo", consumes = MediaType.ALL_VALUE, method = RequestMethod.POST, produces = "text/html;charset=UTF-8;")
    @ResponseBody
    public String AskHandler(AskSearchModel askSearchModel) {
        return houseService.AskHandlerOld(askSearchModel);
    }

    /**
     * 帮你找房
     */
    @RequestMapping(value = "/helpSearch", method = RequestMethod.POST)
    @ResponseBody
    public Integer helpSearch(Guide guide, Model model, HttpSession session) {
        return houseService.saveGuide(guide, model, session);
    }

    @RequestMapping(value = "/getOnlineJson", method = RequestMethod.POST)
    @ResponseBody
    public List<LinkedTreeMap<String, Object>> getOnlineJson(@RequestParam String projectId) {
        return houseService.getOnlineJson(projectId);
    }

    /**
     * 个人楼盘评价保存
     */
    @ResponseBody
    @RequestMapping(value = "/saveComment", method = RequestMethod.POST)
    public HashMap saveComment(@RequestBody HashMap params, Model model) {
        HashMap result = checkComment(params);
        if (Integer.valueOf(result.get("status").toString()) == 1) {
            result = houseService.saveComment(params);
        }
        return result;
    }

    /**
     * 经纪人楼盘评价保存
     */
    @ResponseBody
    @RequestMapping(value = "/saveCommentAgent", method = RequestMethod.POST)
    public HashMap saveCommentAgent(@RequestBody HashMap params, Model model) {
        HashMap result = checkComment(params);
        if (Integer.valueOf(result.get("status").toString()) == 1) {
            result = houseService.saveComment(params);
        }
        return result;
    }

    public HashMap checkComment(HashMap hashMap) {
        HashMap result = new HashMap();
        if (StringUtils.isEmpty(hashMap.get("sessionId"))) {
            result.put("status", 0);
            result.put("msg", "请登录");
        } else if (StringUtils.isEmpty(hashMap.get("isNiMing"))) {
            result.put("status", 0);
            result.put("msg", "是否匿名请选择");
        } else if (!StringUtils.isEmpty(hashMap.get("isYeZhu")) && hashMap.get("isYeZhu").toString().equals("1")) {
            if (StringUtils.isEmpty(hashMap.get("money"))) {
                result.put("status", 0);
                result.put("msg", "价格不为空");
            } else if (StringUtils.isEmpty(hashMap.get("area"))) {
                result.put("status", 0);
                result.put("msg", "面积不为空");
            } else if (StringUtils.isEmpty(hashMap.get("year"))) {
                result.put("status", 0);
                result.put("msg", "年份不为空");
            } else if (StringUtils.isEmpty(hashMap.get("month"))) {
                result.put("status", 0);
                result.put("msg", "月份不为空");
            } else if (StringUtils.isEmpty(hashMap.get("propertyType"))) {
                result.put("status", 0);
                result.put("msg", "请选择物业类型");
            } else if (StringUtils.isEmpty(hashMap.get("buySources"))) {
                result.put("status", 0);
                result.put("msg", "购买途径不为空");
            } else {
                result.put("status", 1);
            }
        } else {
            result.put("status", 1);
        }
        return result;
    }

    /**
     * 楼盘评价回复保存
     */
    @ResponseBody
    @RequestMapping(value = "/saveReply", method = RequestMethod.POST)
    public HashMap saveReply(@RequestBody HashMap params, Model model) {
        HashMap result = checkReply(params);
        if (Integer.valueOf(result.get("status").toString()) == 1) {
            result = houseService.saveReply(params);
        }
        return result;
    }

    public HashMap checkReply(HashMap hashMap) {
        HashMap result = new HashMap();
        if (StringUtils.isEmpty(hashMap.get("sessionId"))) {
            result.put("status", 0);
            result.put("msg", "请登录");
        } else if (StringUtils.isEmpty(hashMap.get("commentId"))) {
            result.put("status", 0);
            result.put("msg", "数据有误");
        } else if (StringUtils.isEmpty(hashMap.get("content"))) {
            result.put("status", 0);
            result.put("msg", "内容不为空");
        } else {
            result.put("status", 1);
        }
        return result;
    }

    /**
     * 抢优惠
     */
    @ResponseBody
    @RequestMapping(value = "/orderActivity", method = RequestMethod.POST)
    public HashMap orderActivity(String sessionId, String userName, String activityId, String code, HttpServletResponse response) {
        return houseService.orderactivity(sessionId, userName, activityId, code, response);
    }


    /**
     * 搜索广告。回头抽出一个公共方法
     *
     * @param systemDate
     * @param type
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getAdvertisementList", method = RequestMethod.POST)
    public HashMap getAdvertisementList(String systemDate, String type) {
        return houseService.getAdvertisementList(systemDate, type);
    }

    @ResponseBody
    @RequestMapping(value = "/fetchNearByProject", method = RequestMethod.POST)
    public HashMap<String, Object> fetchNearByProject(Integer distance, BigDecimal longitude, BigDecimal latitude, Integer firstId) {
        return houseService.fetchNearByProject(distance, longitude, latitude, firstId);
    }

    /**
     * 别墅首页评论分页
     */
    @RequestMapping(value = "/house/{projectId}-{projectType}/index/{page:^[0-9]+$}", method = RequestMethod.GET)
    public String villaForComment(@PathVariable Integer projectId, @PathVariable Integer projectType, Model model, @PathVariable(required = false) Integer page) {
        if (page == null) page = 1;
        model.addAttribute("projectType", "2");
        houseService.viewVillaDetail(projectId + "", model, page + "");
        houseService.selectionProjectInfo(String.valueOf(projectId), model);
        houseService.handleVillaPicsToMap(model);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang1/" + projectId + "-2.htm");
        return "house/villa/villaIndex";
    }

    /**
     * 获取房源相关推荐
     *
     * @param projectId
     * @param latitude
     * @param longitude
     * @return
     * <AUTHOR> 2018-9-21
     */
    @ResponseBody
    @RequestMapping(value = "/house/relasionHousePush", method = RequestMethod.POST)
    public HashMap getRelasionHousePush(@RequestParam String projectId, @RequestParam BigDecimal latitude, @RequestParam BigDecimal longitude) {
        return houseService.getRelasionHousePush(projectId, latitude, longitude);
    }

    /**
     * 房产排行榜页面
     *
     * @return
     * <AUTHOR> 2019-03-05
     */
    @RequestMapping(value = {"/projectRank/{type:^[0-9]+$}", "/projectRank/{type}_{regionId}"}, method = RequestMethod.GET)
    public String videoSearch(@PathVariable Integer type, @Nullable @PathVariable Integer regionId, Model model) {
        model.addAttribute("type", type);
        model.addAttribute("rId", regionId);
        if (type == 5) {
            model.addAttribute("value", 2);
        } else if (type < 5) {
            model.addAttribute("value", type);
        } else if (type > 5) {
            type = 1;
            model.addAttribute("value", type);
        }
        houseService.viewRankListByType(type, regionId, model);
        return "rank/rankList";
    }

    /**
     * 通过memberId调取小程序打电话获取小程序图片
    * <AUTHOR>
    * @Date 2020/10/26 15:54
    **/
    @RequestMapping(value="/getWxACodeByAgentId", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Map getWxACodeByMemberId(String agentId){
        HashMap agent = agentService.searchAgentshop(agentId);
        LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
        String scene = null;
        if(content != null ){
            String tel = content.get("sortTel")+"";
            scene = "tel," +agentId+"-1,6," + tel.replace("转", "");
        }
        Map<String,String> map = new HashMap<>();
        String img =  houseService.getWxACode(scene, "pages/index/index",null);
        map.put("img",img);
        return map;
    }

    /**
     * 小程序打电话获取小程序图片
     *
     * @return
     * <AUTHOR> 2019-03-05
     */
    @RequestMapping(value="/getWxACode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Map getWxACode(String scene){
        Map<String,String> map = new HashMap<>();
        String img =  houseService.getWxACode(scene, "pages/index/index",null);
        map.put("img",img);
        return map;
    }


    @RequestMapping(value="/getWxSecCode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Map getWxSecCode(String scene){
        Map<String,String> map = new HashMap<>();
        String img =  houseService.getWxSecCode(scene, "pages/index/index",null);
        map.put("img",img);
        return map;
    }

    @RequestMapping(value="/getNewWxCode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    public Map getNewWxCode(String scene){
        Map<String,String> map = new HashMap<>();
        String img =  houseService.getNewWxCode(scene, "pages/index/index",null);
        map.put("img",img);
        return map;
    }

    /**
     * 新房->跳转户型对比页面
     * @return
     */
    @RequestMapping(value = "/contrastLayout",method = RequestMethod.GET)
    public String contrastLayout (){
        return "house/layoutCompare";
    }

    /**
     * 新房->进行户型对比
     * @param layId
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/doContrastLayout",method = RequestMethod.POST)
    public HashMap doContrastLayout(Integer layId,HttpServletRequest request){
        String sessionId = StringUtils.isEmpty(request.getSession().getAttribute("muser"))?"":(String)request.getSession().getAttribute("muser");
        HashMap hashMap = houseService.doContrastLayout(layId, sessionId);
        return hashMap;
    }

    /**
     * 新房->户型对比推荐户型
     * @param layId
     * @return
     */

    @ResponseBody
    @RequestMapping(value = "/getRecommendLayout",method = RequestMethod.POST)
    public HashMap getRecommendLayout(Integer layId){
        HashMap recommendLayout = houseService.getRecommendLayout(layId);
        return recommendLayout;
    }

    /**
     * 新房->收藏户型
     * @param layoutId
     * @param sessionId
     * @param method
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/manageCollect",method = RequestMethod.POST)
    public HashMap manageCollect(Integer layoutId,String sessionId,String method) {
        return houseService.manageCollect(layoutId,sessionId,method);
    }

    /**
     * 学区地图列表查询
     * @param schoolTypeId
     * @param regionId
     * @param page
     * @param pageSize
     * @param except
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/viewNewSchoolList",method = RequestMethod.POST)
    public HashMap<String, Object> viewNewSchoolList(Integer schoolTypeId, Integer regionId, Integer page, Integer pageSize, String except) {
        return houseService.viewSchoolList(schoolTypeId, regionId, page, pageSize, except);
    }

    /**
     * 学区地图整体查询
     * @param searchKey
     * @param schoolTypeId
     * @param regionId
     * @param schoolAreaId
     * @param page
     * @param pageSize
     * @param leftLng
     * @param leftLat
     * @param rightLng
     * @param rightLat
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/searchSchoolListForMap",method = RequestMethod.POST)
    public HashMap<String, Object> searchSchoolListForMap(String searchKey,Integer schoolTypeId, Integer regionId, Integer schoolAreaId, Integer page, Integer pageSize,
                                                          Double leftLng, Double leftLat, Double rightLng, Double rightLat) {
        return houseService.searchSchoolListForMap(searchKey, schoolTypeId, regionId, schoolAreaId, page, pageSize, leftLng, leftLat, rightLng, rightLat);
    }

    /**
     * 学校地图列表处理
     * @param searchKey
     * @param schoolTypeId
     * @param projectType
     * @param regionId
     * @param schoolAreaId
     * @param page
     * @param pageSize
     * @param leftLng
     * @param leftLat
     * @param rightLng
     * @param rightLat
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/viewNewSchoolHouseList",method = RequestMethod.POST)
    public HashMap<String, Object> viewNewSchoolHouseList(String searchKey,Integer schoolTypeId, Integer projectType,  Integer regionId, Integer schoolAreaId, Integer page, Integer pageSize,
                                                          Double leftLng, Double leftLat, Double rightLng, Double rightLat, Integer projectStatus) {
        return houseService.viewNewSchoolHouseList(searchKey, schoolTypeId,projectType, regionId, schoolAreaId, page, pageSize, leftLng, leftLat, rightLng, rightLat, projectStatus);
    }

}
