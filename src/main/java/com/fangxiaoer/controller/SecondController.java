package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.common.Utils;
import com.fangxiaoer.model.UrlBean;
import com.fangxiaoer.service.SecondHouseService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.fangxiaoer.common.Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT;

/**
 * 二手房相关
 */
@Controller
public class SecondController {

    @Autowired
    private SecondHouseService shs;

    /**
     * 二手房列表页 M站链接适配
     */
    //二手房列表初始化
    public static final String SECOND_HOUSE_URL = "/saleHouses";
    @RequestMapping(value = {SECOND_HOUSE_URL + "/{params}",SECOND_HOUSE_URL},method = RequestMethod.GET)
    public String getSecondHouseList(@Nullable @PathVariable("params") String params, Model model, String systemDate){
        if (params == null) params = "";
        String baseUrl = SECOND_HOUSE_URL + "/";
        shs.getSecondHouseList(baseUrl,params, model);
        shs.getRightAdvert(systemDate,model,2);
        /*传递参数到页面上*/
        model.addAttribute("mobileAgent",params);
        model.addAttribute("mobileUrl","https://m.fangxiaoer.com/fang2/" + params);
        return "secondhouse/secondhouse";
    }

    /**
     * 二手房成交列表  M站链接适配
     */
    //二手房成交列表
    public static final String DEAL_SECOND_HOUSE = "/dealSales";
    @RequestMapping(value = {DEAL_SECOND_HOUSE + "/{params}",DEAL_SECOND_HOUSE},method = RequestMethod.GET)
    public String getDealSecondList(@Nullable @PathVariable("params") String params, Model model,HttpServletRequest request, String systemDate){
        if (params == null) params = "";
        String baseUrl = DEAL_SECOND_HOUSE + "/";
        shs.getDealSecondList(params, model,request);

        HashMap hashMap = (HashMap) model.asMap();
        ArrayList regionList = (ArrayList) hashMap.get("region");
        for (int i = 0 ; i < regionList.size() ; i++) {
            UrlBean UrlBean =  (UrlBean)regionList.get(i);
            if (UrlBean.getSelected() && !UrlBean.getId().equals("")) {
                System.out.println(UrlBean.getName());
            }
        }
        shs.getRightAdvert(systemDate,model,2);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/txnSecond");
        return "secondhouse/dealsecond";
    }

    /**
     * 二手房详情页  M站链接适配
     * @return
     */
    public static final String SECOND_HOUSE_DETAIL_URL = "/salehouse";
    @RequestMapping(value = SECOND_HOUSE_DETAIL_URL + "/{houseId}.htm", method = RequestMethod.GET)
    public String getSeDetail(@PathVariable ("houseId") String houseId, Model model){
        model.addAttribute("houseTypeReport",1);
        shs.getSeDetail(houseId, model);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang2/"+houseId+".htm");
//        return "secondhouse/detail/detail";
        return "secondhouse/detail/detail2";
    }

    public static final String SECOND_HOUSE_DETAIL_URL2 = "/salehouse2";
    @RequestMapping(value = SECOND_HOUSE_DETAIL_URL2 + "/{houseId}.htm", method = RequestMethod.GET)
    public String getSeDetail2(@PathVariable ("houseId") String houseId, Model model){
        model.addAttribute("houseTypeReport",1);
        shs.getSeDetail(houseId, model);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang2/"+houseId+".htm");
        return "secondhouse/detail/detail";
    }

    /**
     * 二手房成交详情页  M站链接适配
     * @return
     */
    @RequestMapping(value = "/dealSale/{houseId}.htm", method = RequestMethod.GET)
    public String getDealSaleDetail(@PathVariable ("houseId") String houseId, Model model, HttpServletRequest request){
        shs.getDealSaleDetail(houseId, model);
        String sessionId = HttpUtil.checkSessionId(request);
        if (!StringUtils.isEmpty(sessionId)){
            Integer houseid = Integer.parseInt(houseId);
            HttpUtil.connectApi(Constants.ADD_DEAL_DRTAIL_ORDER, new Params("houseId", houseid).add("sessionId",sessionId).add("houseType",1).get(), model);
        }
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/txnDetail/"+houseId+"-1.htm");
        return "secondhouse/detail/dealsaledetail";
    }

    /**
     * 二手房小区找房列表页  M站适配链接
     * @return
     */

    @RequestMapping(value = {SecondHouseService.SECONDHOUSE_VILLAGES_HOUSE_URL + "/{params}",
            SecondHouseService.SECONDHOUSE_VILLAGES_HOUSE_URL}, method = RequestMethod.GET)
    public String getSeVillage(@Nullable @PathVariable("params") String params, Model model,String systemDate){
        model.addAttribute("", "");
        if(params == null) params = "";
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/sub/"+params);
        String baseUrl = SecondHouseService.SECONDHOUSE_VILLAGES_HOUSE_URL + "/";
        shs.getVillages(baseUrl, params, model);
        shs.getRightAdvert(systemDate,model,2);
        model.addAttribute("gang","<i class='gang'></i>");
        return "secondhouse/villages";
    }

    /**
     * 二手房地铁房  M站链接适配
     * @param input
     * @param model
     * @return
     */
    @RequestMapping(value = "/saleHouses/subway/{input}")
    public String getSeSubwayList(@PathVariable ("input") String input, Model model){
        String baseUrl = SECOND_HOUSE_URL + "/";
        shs.getSecondSubwayList(baseUrl,input, model);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang2");
        return "secondhouse/subwaylist";
    }

    @RequestMapping(value ="/dashuju", method = RequestMethod.GET)
    public String Dashuju(){
        return "secondhouse/dashuju";
    }
    //司法拍卖
    @RequestMapping(value ="/UHouse/sale/judicial.aspx", method = RequestMethod.GET)
    public String Judicial(){
        return "secondhouse/judicial";
    }

    @RequestMapping(value ="/salemap", method = RequestMethod.GET)
    public String getSaleMap(){
        return "secondhouse/secondQQmap";
    }

    @RequestMapping(value ="/salemap2", method = RequestMethod.GET)
    public String getSaleMap2(){
        return "secondhouse/secondmap";
    }

    /**
     * 二手房 地铁找房
     * @return
     */
    /*@RequestMapping(value ="/subwaymap.htm", method = RequestMethod.GET)
    public String getSubwayMap(){
        return "secondhouse/subwaymap";
    }*/
    /**
     * 虚假举报
     * @param sessionId
     * @param houseId
     * @param illegalType
     */
    @ResponseBody
    @RequestMapping(value = "/falsity", method = RequestMethod.POST)
    public HashMap<String, Object> falsity(String sessionId,String illegalType, Integer houseId,String houseType, HttpSession session) {
        return shs.falsity(sessionId,illegalType,houseId,houseType,session);
    }

    /**
     * 地图找房搜索关键字相关结果
     * @Date 2018.11.05
     */
    @ResponseBody
    @RequestMapping(value = "/searchByTitle",method = RequestMethod.GET)
    public List<LinkedTreeMap<String, Object>> search(Model model, @RequestParam("q") String q){
        try {
            q  =  URLDecoder.decode(q,"UTF-8");
        }catch (Exception e){
            e.printStackTrace();
        }
         HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("search",q);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.QUERY_SEARCH_TITLE_SECOND_MAP,params);
        List<LinkedTreeMap<String, Object>> searchList = (List<LinkedTreeMap<String, Object>>) searchs.get("content");
        LinkedTreeMap<String, Object> param = new LinkedTreeMap<String, Object>();
//        model.addAttribute("search",q);
        if(null == searchList){
            searchList = new ArrayList<LinkedTreeMap<String, Object>>();
            param.put("search","");
            param.put("_Id",0);
            searchList.add(0,param);
            return searchList;
        }else if(searchList.size() == 0){
            param.put("search","");
            param.put("_Id",0);
            searchList.add(0,param);
            return searchList;
        }else{
            return searchList;
        }
    }

    /**
     * 地图找房搜索栏查询匹配词的内容(搜索按钮)
     * @Date 2018.11.07
     */
    @ResponseBody
    @RequestMapping(value = "/searchOneInfo",method = RequestMethod.POST)
    public List searchOne(Model model, String search){
        try {
            search  =  URLDecoder.decode(search,"UTF-8");
        }catch (Exception e){
            e.printStackTrace();
        }
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("search",search);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.QUERY_SEARCH_OEN_INFO_SECONDMAP,params);
        List searchList = (List)searchs.get("content");
        return searchList;
    }

    /**
     * 地图找房  右侧地图
     * @param hashMap
     * @return
     * @Date 2018.11.09
     */
    @RequestMapping(value = "/searchSecMap" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> querySecMap(@RequestParam HashMap hashMap) {
        HashMap<String, Object> secondMap = shs.querySecondHouse(hashMap);
        return secondMap;
    }

    /**
     * 地图找房  左侧列表
     * @param hashMap
     * @return
     * @Date 2018.11.09
     */
    @RequestMapping(value = "/searchSecMapLeftList" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> querySecMapLeftList(@RequestParam HashMap<String ,Object> hashMap) {
        HashMap<String, Object> secondMap = shs.querySecondHouseList(hashMap);
        return secondMap;
    }

    /**
     * 地图找房  点击具体小区展示小区相关信息
     * @param
     * @return
     * @Date 2019.01.01
     */
    @RequestMapping(value = "/querySubDetail" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> querySub(String subId) {
        HashMap<String, Object> secondMap = shs.querySubDetail(subId);
        return secondMap;
    }

    /**
     * 地铁 搜索框 下拉联动匹配结果
     * @date 2019.08.13
     * @return
     */
    @RequestMapping(value = "/searchForSubway" ,method = RequestMethod.POST)
    @ResponseBody
    public List<LinkedTreeMap<String, Object>> queryForSubway(@RequestParam("q") String q) {
        return shs.queryForSub(q);
    }
    /**
     * 地铁  站点及小区信息
     * @return
     * @date 2019.08.15
     */
    @RequestMapping(value = "/saleMetroStation" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> secStationMap(@RequestParam HashMap hashMap) {
        return shs.secStationMap(hashMap);
    }

    /**
     * 地铁找房 左侧房源列表
     * @return
     * @date 2019.08.16
     */
    @RequestMapping(value = "/saleMetroHouseList" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> secStationMapHouse(@RequestParam HashMap hashMap) {
        return shs.secStationMapHouse(hashMap);
    }

    /**
     * 地铁找房 小区详情、小区专家
     * @return
     * @date 2019.08.21
     */
    @RequestMapping(value = "/secPlotDetailList" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> secMapPlot(String subId) {
        return shs.secMapPlot(subId);
    }

    @GetMapping("/secondhouse/entrustReleaseHouse")
    public String entrustReleaseHouse() {
        return "secondhouse/entrustReleaseHouse";
    }

    @ResponseBody
    @PostMapping("/memberIdCardAuthentication")
    public HashMap<String, Object> memberIdCardAuthentication(@RequestParam HashMap reqMap, HttpSession session) {
        return shs.memberIdCardAuthentication(reqMap, session);
    }

    @ResponseBody
    @PostMapping("/confirmAuthenticationInfo")
    public HashMap<String, Object> confirmAuthenticationInfo(@RequestParam HashMap reqMap, HttpSession session) {
        return shs.confirmAuthenticationInfo(reqMap, session);
    }

}
