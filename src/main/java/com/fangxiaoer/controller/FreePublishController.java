package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.model.*;
import com.fangxiaoer.service.FreePublishService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2017/9/4.
 */
@Controller
public class FreePublishController {
    @Autowired
    FreePublishService freePublishService;

    /**
     * 二手房 M站链接适配
     */
    @RequestMapping(value = {"/new_secondPublish","/secondPublish/{houseID}", "/myPublish/{check}/{houseID}"}, method = RequestMethod.GET)
    public String getSecondHouse(@PathVariable @Nullable String houseID,Model model ,HttpServletRequest request,
                                 String area,String totalPrice,String currentFloor,String totalFloor,String subId, @PathVariable @Nullable String check){
        model.addAttribute("check", check == null ? "0": "1");
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/pubSale.htm");
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        SaleHouseEntity saleHouseEntity = new SaleHouseEntity();
        freePublishService.getSecondHouse(model);
        saleHouseEntity.setSaletype("4");
        saleHouseEntity.setFitmenttype("3");
        saleHouseEntity.setForwardtype("0");
        saleHouseEntity.setPropertyrighttype("1");
        saleHouseEntity.setHousingtype("1");
        //根据subId查询小区名称（解决房价结果页跳转我要卖房页小区名称乱码问题）
        String subName = freePublishService.getVillagesName(subId);
        if(!StringUtils.isEmpty(subName)){
            saleHouseEntity.setSubname(subName);
        }
        //房价结果页传过来的参数
        if(!StringUtils.isEmpty(area)){
            saleHouseEntity.setBuildarea(area);
        }
        if(!StringUtils.isEmpty(totalPrice)){
            saleHouseEntity.setSaleprice(totalPrice);
        }
        if(!StringUtils.isEmpty(currentFloor)){
            saleHouseEntity.setFloor(currentFloor);
        }
        if(!StringUtils.isEmpty(totalFloor)){
            saleHouseEntity.setTotalfloornumber(totalFloor);
        }
        if(!StringUtils.isEmpty(subId)){
            saleHouseEntity.setSubid(subId);
        }
        model.addAttribute(saleHouseEntity);
        //sessionid不为空的情况下，通过sessionid查询客户是否为经纪人
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = request.getSession().getAttribute("sessionId").toString();
            freePublishService.checkMemberAgent(model,sessionId);
        }
        if (houseID != null){
            if (request.getSession().getAttribute("sessionId") == null) {
                return "freepublish/secondhouse";
            }else {
                String sessionId = request.getSession().getAttribute("sessionId").toString();
                freePublishService.updateSecond(sessionId,houseID,model);
            }
        }
        return "freepublish/secondhouse";
    }
    /**
     * 接收二手房信息
     */
    @RequestMapping(value="/saveSecondHouse", method=RequestMethod.POST)
    public String saveSecondHouse(@ModelAttribute SaleHouseEntity saleHouseEntity, Model model,
                                  HttpServletRequest request, HttpServletResponse response) {
        String password_text = request.getParameter("password_text");
        String messageCode = request.getParameter("messageCode");
        freePublishService.dealSale(request,response,saleHouseEntity,model,messageCode,password_text);
        freePublishService.getSecondHouse(model);
        String traits = saleHouseEntity.getHousetrait();
        saleHouseEntity.setHousetrait(traits);
        model.addAttribute("saleHouseEntity", saleHouseEntity);
        return "freepublish/secondhouse";
    }

    /**
     * 整套租房 M站链接适配
     */
    @RequestMapping(value = {"/rentwhole","/rentwhole/{houseID}"}, method = RequestMethod.GET)
    public String getRentWhole(@PathVariable @Nullable String houseID, Model model,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/pubRent.htm");
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        RentHouseEntity rentHouseEntity = new RentHouseEntity();
        rentHouseEntity.setForwardtype("0");
        model.addAttribute(rentHouseEntity);
        freePublishService.getRent(model);
        //sessionid不为空的情况下，通过sessionid查询客户是否为经纪人
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = request.getSession().getAttribute("sessionId").toString();
            freePublishService.checkMemberAgent(model,sessionId);
        }
        if (houseID != null){
            if (request.getSession().getAttribute("sessionId") == null) {
                return "freepublish/rentWhole";
            }else {
                String sessionId = request.getSession().getAttribute("sessionId").toString();
                //String sessionId = CookieManage.iterationCookie(request,"aspxUserTicket");
                freePublishService.updateRent(sessionId,houseID,model);
            }
        }
        return "freepublish/rentWhole";
    }
    /**
     * 接收租房整套信息
     */
    @RequestMapping(value="/saveRentWhole", method=RequestMethod.POST)
    public String saveRentWhole(@ModelAttribute RentHouseEntity rentHouseEntity, Model model,
                                HttpServletRequest request, HttpServletResponse response) {
        String password_text = request.getParameter("password_text");
        String messageCode = request.getParameter("messageCode");
        freePublishService.dealRent(request,response,rentHouseEntity,model,messageCode);
        freePublishService.getRent(model);
        String traits = rentHouseEntity.getHousetrait();
        rentHouseEntity.setHousetrait(traits);
        model.addAttribute("rentHouseEntity", rentHouseEntity);
        return "freepublish/rentWhole";
    }

    /**
     * 单间租房 M站链接适配
     */
    @RequestMapping(value = {"/rentsingls","/rentsingls/{houseID}"}, method = RequestMethod.GET)
    public String getRentSingls(@PathVariable @Nullable String houseID, Model model,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/pubRent2.htm");
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        RentHouseEntity rentHouseEntity = new RentHouseEntity();
        rentHouseEntity.setForwardtype("0");
        rentHouseEntity.setSex("0");
        rentHouseEntity.setRestype("4");
        model.addAttribute(rentHouseEntity);
        freePublishService.getRent(model);
        //sessionid不为空的情况下，通过sessionid查询客户是否为经纪人
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = request.getSession().getAttribute("sessionId").toString();
            freePublishService.checkMemberAgent(model,sessionId);
        }
        if (houseID != null){
            if (request.getSession().getAttribute("sessionId") == null) {
                return "freepublish/rentSingls";
            }else {
                String sessionId = request.getSession().getAttribute("sessionId").toString();
                freePublishService.updateRent(sessionId,houseID,model);
            }
        }
        return "freepublish/rentSingls";
    }
    /**
     * 接收租房单间信息
     */
    @RequestMapping(value="/saveRentSingls", method=RequestMethod.POST)
    public String saveRentSingls(@ModelAttribute RentHouseEntity rentHouseEntity, Model model,
                                 HttpServletRequest request, HttpServletResponse response) {
        String password_text = request.getParameter("password_text");
        String messageCode = request.getParameter("messageCode");
        freePublishService.dealRent(request,response,rentHouseEntity,model,messageCode);
        freePublishService.getRent(model);
        String traits = rentHouseEntity.getHousetrait();
        rentHouseEntity.setHousetrait(traits);
        model.addAttribute("rentHouseEntity", rentHouseEntity);
        return "freepublish/rentSingls";
    }

    /**
     * 商铺出售 M站链接适配
     */
    @RequestMapping(value = {"/shopsell","/shopsell/{houseID}"}, method = RequestMethod.GET)
    public String getShopSell(@PathVariable @Nullable String houseID, Model model,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/pubShop.htm");
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        ShopEntity shopEntity = new ShopEntity();
        model.addAttribute(shopEntity);
        freePublishService.getShop(model);
        //sessionid不为空的情况下，通过sessionid查询客户是否为经纪人
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = request.getSession().getAttribute("sessionId").toString();
            freePublishService.checkMemberAgent(model,sessionId);
        }
        if (houseID != null){
            if (request.getSession().getAttribute("sessionId") == null) {
                return "freepublish/shopSell";
            }else {
                String sessionId = request.getSession().getAttribute("sessionId").toString();
                freePublishService.updateShop(sessionId,houseID,model,3);
            }
        }
        return "freepublish/shopSell";
    }
    /**
     * 接收商铺出售信息
     */
    @RequestMapping(value="/saveShopSell", method=RequestMethod.POST)
    public String saveShopSell(@ModelAttribute ShopEntity shopEntity, Model model,
                               HttpServletRequest request, HttpServletResponse response) {
        String password_text = request.getParameter("password_text");
        String messageCode = request.getParameter("messageCode");
        freePublishService.dealShops(request,response,shopEntity,model,messageCode);
        freePublishService.getShop(model);
        model.addAttribute("rentHouseEntity", shopEntity);
        return "freepublish/shopSell";
    }

    /**
     * 商铺出租转租 M站链接适配
     */
    @RequestMapping(value ={"/shoprent","/shoprent/{houseID}"}, method = RequestMethod.GET)
    public String getShopRent(@PathVariable @Nullable String houseID, Model model,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/pubShop2.htm");
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        ShopEntity shopEntity = new ShopEntity();
        model.addAttribute(shopEntity);
        freePublishService.getShop(model);
        //sessionid不为空的情况下，通过sessionid查询客户是否为经纪人
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = request.getSession().getAttribute("sessionId").toString();
            freePublishService.checkMemberAgent(model,sessionId);
        }
        if (houseID != null){
            if (request.getSession().getAttribute("sessionId") == null) {
                return "freepublish/shopRent";
            }else {
                String sessionId = request.getSession().getAttribute("sessionId").toString();
                freePublishService.updateShop(sessionId,houseID,model,3);
            }
        }
        if (shopEntity.getIscut() == null) {
            shopEntity.setIscut("2");
        }
        return "freepublish/shopRent";
    }
    /**
     * 接收商铺出租转租信息
     */
    @RequestMapping(value="/saveShopRent", method=RequestMethod.POST)
    public String saveShopRent(@ModelAttribute ShopEntity shopEntity, Model model,
                               HttpServletRequest request, HttpServletResponse response) {
        String password_text = request.getParameter("password_text");
        String messageCode = request.getParameter("messageCode");
        freePublishService.dealShops(request,response,shopEntity,model,messageCode);
        freePublishService.getShop(model);
        model.addAttribute("rentHouseEntity", shopEntity);
        return "freepublish/shopRent";
    }

    /**
     * 商铺出兑转让 M站链接适配
     */
    @RequestMapping(value = {"/shoptransfer","/shoptransfer/{houseID}"}, method = RequestMethod.GET)
    public String getShopTransfer(@PathVariable @Nullable String houseID, Model model, HttpServletRequest request, HttpServletResponse response){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/pubShop3.htm");
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        ShopEntity shopEntity = new ShopEntity();
        model.addAttribute(shopEntity);
        freePublishService.getShop(model);
        //sessionid不为空的情况下，通过sessionid查询客户是否为经纪人
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = request.getSession().getAttribute("sessionId").toString();
            freePublishService.checkMemberAgent(model,sessionId);
        }
        if (houseID != null){
            if (request.getSession().getAttribute("sessionId") == null) {
                if (shopEntity.getIscut() == null) {
                    shopEntity.setIscut("2");
                }
                if (shopEntity.getTrantype() == null) {
                    shopEntity.setTrantype("2");
                }
                return "freepublish/shopTransfer";
            }else {
                String sessionId = request.getSession().getAttribute("sessionId").toString();
                //String sessionId = CookieManage.iterationCookie(request,"aspxUserTicket");
                freePublishService.updateShop(sessionId,houseID,model,3);
            }
        }
        if (shopEntity.getIscut() == null) {
            shopEntity.setIscut("2");
        }
        if (shopEntity.getTrantype() == null) {
            shopEntity.setTrantype("2");
        }
        return "freepublish/shopTransfer";
    }
    /**
     * 接收商铺出兑转让信息
     */
    @RequestMapping(value="/saveShopTransfer", method=RequestMethod.POST)
    public String saveShopTransfer(@ModelAttribute ShopEntity shopEntity, Model model,
                                   HttpServletRequest request, HttpServletResponse response) {
        String password_text = request.getParameter("password_text");
        String messageCode = request.getParameter("messageCode");
        String tranfee = shopEntity.getTranfee();
        String trantype = shopEntity.getTrantype();
        String [] tranfees = tranfee.split(",");
        System.out.println(tranfees.length);
        if (tranfees.length <= 3 && tranfees.length>=1) {
            if (trantype.equals("1")){
                if (tranfees[0].length() == 0) {
                    tranfees[0] = "面议";
                }
                tranfee = tranfees[0];
            } else if (trantype.equals("2")) {
                if (tranfees[1].length() == 0) {
                    tranfees[1] = "面议";
                }
                tranfee = tranfees[1];
            } else {
                tranfee = "面议";
            }
        } else {
            tranfee = "面议";
        }
        shopEntity.setTranfee(tranfee);
        freePublishService.dealShops(request,response,shopEntity,model,messageCode);
        freePublishService.getShop(model);
        model.addAttribute("rentHouseEntity", shopEntity);
        return "freepublish/shopTransfer";
    }
    /**
     *获取板块信息
     */
    @ResponseBody
    @RequestMapping(value = "/getPlates", method = RequestMethod.POST)
    public List<LinkedTreeMap<String, Object>> getPlates (@RequestParam("RegionID") String RegionID, Model model){
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("regionId",RegionID);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.GET_PLATES_BY_REGION,params);
        List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) searchs.get("content");
        return result;
    }


    /**
     * 搜索栏自动提示
     */
    @ResponseBody
    @RequestMapping(value = "/searchs",method = RequestMethod.GET)
    public List<LinkedTreeMap<String, Object>> search(Model model, @RequestParam("q") String q){
        try {
           q  =  URLDecoder.decode(q,"UTF-8");
        }catch (Exception e){
            e.printStackTrace();
        }
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("subName",q);
        //model.addAttribute("subName",q);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.SEARCH_RESIDENTIAL,params);
        List<LinkedTreeMap<String, Object>> search = (List<LinkedTreeMap<String, Object>>) searchs.get("content");
        LinkedTreeMap<String, Object> param = new LinkedTreeMap<String, Object>();
        if(null == search){
            search = new ArrayList<LinkedTreeMap<String, Object>>();
            param.put("subName","未找到对应的小区");
            param.put("subId",0);
            search.add(0,param);
            return search;
        }else if(search.size() == 0){
            param.put("subName","未找到对应的小区");
            param.put("subId",0);
            search.add(0,param);
            return search;
        }else{
            return search;
        }

    }


    /**
     * ajax接收电话查看是否存在
     */
    @ResponseBody
    @RequestMapping(value="/searchPhone", method=RequestMethod.POST)
    public HashMap<String,Object> searchPhone(@RequestParam ("phone") String phone, Model model) {
        HashMap<String,Object> result = new HashMap<String,Object>();
        result.put("mobile",phone);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.CHECK_IS_MEMBER,result);
        String ismember = searchs.get("msg").toString();
        if (ismember.equals("会员")) {
            result.put("temp","1");
            return result;
        }else {
            result.put("temp",phone);
            return result;
        }
    }

    /**
     * ajax接收sessionId查用户资料
     */
    @ResponseBody
    @RequestMapping(value="/searchUser", method=RequestMethod.POST)
    public HashMap<String,Object> searchUser(@RequestParam ("sessionId") String sessionId, Model model) {
        HashMap<String,Object> result = new HashMap<String,Object>();
        result.put("sessionId",sessionId);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.VIEW_MEMBER_INFO,result);
        return result;
    }
    /**
     * ajax接收subID
     */
    @ResponseBody
    @RequestMapping(value="/searchSub", method=RequestMethod.POST)
    public HashMap<String,Object> searchSub(@RequestParam ("subID") String subID, Model model) {
        HashMap<String,Object> result = new HashMap<String,Object>();
        result.put("subId",subID);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL,result);
        return searchs;
    }

    /**
     * 写字楼出售页面 M站链接适配
     */
    @RequestMapping(value ={"/officeSell","/officeSell/{shopId}"}, method = RequestMethod.GET)
    public String getOfficeSell(@PathVariable @Nullable String shopId, Model model,HttpServletRequest request,HttpServletResponse response){
        /*String sessionId = freePublishService.disposeSessionInfo("114680", request, response, "13478225277");*/
        //request.getSession().getAttribute("sessionId")
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/pubOffice5.htm");
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        //sessionid不为空的情况下，通过sessionid查询客户是否为经纪人
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = request.getSession().getAttribute("sessionId").toString();
            freePublishService.checkMemberAgent(model,sessionId);
        }
        if (shopId != null){//修改自己发布的房源
            if ( request.getSession().getAttribute("sessionId") == null) {
                ShopEntity shopEntity = new ShopEntity();
                model.addAttribute("shopEntity",shopEntity);
                return "freepublish/officesSell";
            }else {
                String sessionId = request.getSession().getAttribute("sessionId").toString();
                model.addAttribute("shopId",shopId);
                model.addAttribute("sessionId",sessionId);
                freePublishService.updateShop(sessionId,shopId,model,4);
                return "freepublish/officesSell";
            }
        }else {
            ShopEntity shopEntity = new ShopEntity();
            model.addAttribute("shopEntity",shopEntity);
            return "freepublish/officesSell";
        }
    }

    /**
     * 写字楼出租页面 M站链接适配
     */
    @RequestMapping(value ={"/officeRent","/officeRent/{shopId}"}, method = RequestMethod.GET)
    public String getOfficeRent(@PathVariable @Nullable String shopId, Model model,HttpServletRequest request,HttpServletResponse response){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/pubOffice4.htm");
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        //sessionid不为空的情况下，通过sessionid查询客户是否为经纪人
        if (request.getSession().getAttribute("sessionId") != null) {
            String sessionId = request.getSession().getAttribute("sessionId").toString();
            freePublishService.checkMemberAgent(model,sessionId);
        }
        if (shopId != null){//修改自己发布的房源
            if (request.getSession().getAttribute("sessionId") == null) {
                ShopEntity shopEntity = new ShopEntity();
                model.addAttribute("shopEntity",shopEntity);
                return "freepublish/officesRent";
            }else {
                String sessionId = request.getSession().getAttribute("sessionId").toString();
                model.addAttribute("shopId",shopId);
                model.addAttribute("sessionId",sessionId);
                freePublishService.updateShop(sessionId,shopId,model,4);
                return "freepublish/officesRent";
            }
        }else {
            ShopEntity shopEntity = new ShopEntity();
            model.addAttribute("shopEntity",shopEntity);
            return "freepublish/officesRent";
        }
    }

    /**
     * 写字楼保存
     */
    @ResponseBody
    @RequestMapping(value ={"/saveOffice","/saveOffice/{houseID}"}, method = RequestMethod.POST)
    public HashMap getsaveOffice(@ModelAttribute OfficeEntity officeEntity, Model model,
                                    HttpServletRequest request, HttpServletResponse response){
        HashMap result = freePublishService.dealoffices(request,response,officeEntity,model);
        ShopEntity shopEntity = new ShopEntity();
        model.addAttribute("shopEntity",shopEntity);
        return result;
    }

    /**
     * 写字楼字典
     * @param officeName
     * @param pageSize
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getOfficeDictionary",method = RequestMethod.POST)
    public HashMap getOfficeDictionary(@RequestParam ("officeName") String officeName,Integer pageSize){
        HashMap officeDictionary = HttpUtil.connectApi(Constants.OFFICE_DICTIONARY, new Params("pageSize", pageSize).add("officeName", officeName).get());
        return officeDictionary;
    }
    /**
     * 写字楼出租页面 M站链接适配
     */
    @RequestMapping(value ={
            "/secondPublish"/*,
            "/new_secondPublish",
            "/secondPublish/{houseID}",
            "/myPublish/{check}/{houseID}",
            "/rentwhole",
            "/rentwhole/{houseID}",
            "/rentsingls",
            "/rentsingls/{houseID}",
            "/shopsell",
            "/shopsell/{houseID}",
            "/shoprent",
            "/shoprent/{houseID}",
            "/shoptransfer",
            "/shoptransfer/{houseID}",
            "/officeSell",
            "/officeSell/{houseID}",
            "/officeRent",
            "/officeRent/{houseID}"*/
    }, method = RequestMethod.GET)
    public String download_app(/*@Nullable @PathVariable String houseID,
                               @PathVariable @Nullable String check*/){
       return "static/download";
    }

    @ResponseBody
    @RequestMapping(value = "/publishRealEstate", method = RequestMethod.POST)
    public HashMap<String, Object> publishRealEstate(RealEstate realEstate, HttpServletRequest request, Integer houseType){
        return freePublishService.publishRealEstate(realEstate,request,houseType);
    }

    @ResponseBody
    @RequestMapping(value = "/checkRealEstate", method = RequestMethod.POST)
    public HashMap<String, Object> checkRealEstate(Integer houseId,Integer houseType, HttpServletRequest request){
        return freePublishService.checkRealEstate(houseId, houseType, request);
    }

    @ResponseBody
    @RequestMapping(value = "/getRealEstateInfo", method = RequestMethod.POST)
    public HashMap<String, Object> getRealEstateInfo(Integer houseId,Integer houseType, HttpServletRequest request){
        return freePublishService.getRealEstateInfo(houseId, houseType, request);
    }

    @RequestMapping(value = "/verifyIt/{houseId}", method = RequestMethod.GET)
    public String verifyIt(@PathVariable Integer houseId, Model model){
        model.addAttribute("houseId", houseId);
        return "freepublish/realestate_house";
    }


    @RequestMapping(value = "/realRent/{id}", method = RequestMethod.GET)
    public String viewRealRent(@PathVariable Integer id, Model model){
        model.addAttribute("houseId", id);
        return "freepublish/rentHouse";
    }


    @RequestMapping(value = "/realShop/{id}", method = RequestMethod.GET)
    public String viewRealShop(@PathVariable Integer id, Model model){
        model.addAttribute("houseId", id);
        return "freepublish/shopVerify";
    }


    @RequestMapping(value = "/realOffice/{id}", method = RequestMethod.GET)
    public String viewRealOffice(@PathVariable Integer id, Model model){
        model.addAttribute("houseId", id);
        return "freepublish/officeBuild";
    }

    /**
     * 委托出租
     * */
    @GetMapping("/entrustedLease")
    public String entrustReleaseHouse() {
        return "freepublish/entrustedLease";
    }

    /**
     * 求租求购
     *
     * */
    @RequestMapping(value = {"/seekOrder","/seekOrder.htm"})
    public String seekOrder(Model model, @RequestParam(required = false) Integer s , HttpServletRequest request){

        if(s == null){ s = 1;}

        model.addAttribute("s",s);
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));

        //请求列表
        System.out.print(model);
        model.addAttribute("houseType",s);
        freePublishService.seekOrderList(model);

        if(s == 1){
            return "seekOrder/seekNewHouse";//新房
        }else if(s == 2){
            return "seekOrder/seekSecondHouse";//二手房
        }else if(s == 3){
            return "seekOrder/seekRentHouse";//租房
        }else if(s == 4){
            return "seekOrder/seekShopHouse";//商铺
        }else if(s == 5){
            return "seekOrder/seekOfficeHouse";//写字楼
        }else{
            return "seekOrder/seekNewHouse";
        }


    }

    @RequestMapping(value = {"/seekOrder/{houseType}/", "/seekOrder/{houseType}/{params}"})
    public String seekOrderList(HttpServletRequest request, Model model, @PathVariable("houseType")Integer houseType, @Nullable @PathVariable("params") String params) {
        HttpSession session = request.getSession();
        model.addAttribute("sessionId", session.getAttribute("muser"));
        model.addAttribute("authenticationStatus", session.getAttribute("authenticationStatus"));
        freePublishService.getSeekHouseList(model, "/seekOrder/" + houseType + "/", params, houseType);
        String url;
        switch (houseType){
            case 2: url = "seekOrder/seekSecondHouse"; break;
            case 3: url = "seekOrder/seekRentHouse"; break;
            case 4: url = "seekOrder/seekShopHouse"; break;
            case 5: url = "seekOrder/seekOfficeHouse"; break;
            default: url = "seekOrder/seekNewHouse"; break;
        }
        return url;
    }




}
