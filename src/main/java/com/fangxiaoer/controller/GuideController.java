package com.fangxiaoer.controller;

import com.fangxiaoer.common.*;
import com.fangxiaoer.model.Guide;
import com.fangxiaoer.model.Loan;
import com.fangxiaoer.service.GuideService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2017/8/28.
 */
@Controller
public class GuideController {
    @Autowired
    private GuideService guideService;
    /**
     * 保存需求定制
     * @return
     * <AUTHOR> 2017-8-23
     */
    @ResponseBody
    @RequestMapping("/saveGuide")
    public Map saveGuide(@RequestBody Guide guide){
        Map result = this.checkGuide(guide);
        if(Integer.valueOf(result.get("status").toString()) == 1 ){
            result =  guideService.saveGuide(guide);
        }
        return result;
    }

    /**
     * 验证需求定制信息
     * @param guide
     * @return
     */
    public Map checkGuide(com.fangxiaoer.model.Guide guide){
        HashMap result = new HashMap();
        if( StringUtils.isEmpty(guide.getPhone()) && StringUtils.isEmpty(guide.getSessionId())){
            result.put("status",0);
            result.put("msg","手机号码为空");
        }else if(StringUtils.isEmpty(guide.getCode()) && StringUtils.isEmpty(guide.getSessionId())){
            result.put("status",0);
            result.put("msg","验证码为空");
        }else if(StringUtils.isEmpty(guide.getArea())){
            result.put("status",0);
            result.put("msg","区域");
        }else if(StringUtils.isEmpty(guide.getBudget())){
            if(!StringUtils.isEmpty(guide.getType()) && (guide.getType()==1||guide.getType()==2||guide.getType()==3||guide.getType()==8)){
                result.put("status",0);
                result.put("msg","预算为空");
            }else {
                result.put("status",1);
            }
        }else if(StringUtils.isEmpty(guide.getRegion())){
            result.put("status",0);
            result.put("msg","区域为空");
        }else if(StringUtils.isEmpty(guide.getType())){
            result.put("status",0);
            result.put("msg","类型为空");
        }else{
            if(StringUtils.isEmpty(guide.getSessionId())){
                try {
                    HashMap vresult = this.verifySmsCode(guide.getPhone(),guide.getCode());
                    int status = Double.valueOf(vresult.get("status").toString()).intValue();
                    if(status ==0){
                        result.put("status",0);
                        result.put("msg","验证码错误");
                    }else {
                        result.put("status",1);
                        result.put("msg","success");
                    }
                }catch (Exception e ){
                    e.printStackTrace();
                }
            }else {
                result.put("status",1);
                result.put("msg","success");
            }
        }
        return  result;
    }

    /**
     * 验证验证码是否正确
     * @param mobile 手机号
     * @param code 验证码
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/verifySmsCode", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public HashMap<String, Object> verifySmsCode(@RequestParam("mobile") String mobile, @RequestParam("code") String code) throws IOException {
        HashMap<String, Object> searchMap = new HashMap<>();
        searchMap.put("mobile", mobile);
        searchMap.put("code", code);
        HashMap<String, Object> results = HttpUtil.connectApi(Constants.CHECK_PASSCODE, searchMap);
        int status = Double.valueOf(results.get("status").toString()).intValue();
        results.put("status",status);
        return results;
    }

    /**
     * 保存贷款意向
     * @param loan
     * @return
     * <AUTHOR> 2017-922
     */
    @ResponseBody
    @RequestMapping(value = "/saveLoan", method = RequestMethod.POST, produces = "application/json")
    public HashMap<String,Object> saveLoan(Loan loan, HttpServletRequest request){
        HashMap result = this.checkLoan(loan);
        if(result.get("status").equals(1)){
//            String sessionId =  request.getSession().getAttribute("sessionId").toString();
            String sessionId =  CookieManage.iterationCookie(request,"sessionId");
            loan.setSessionId(sessionId);
            result =   guideService.saveLoan(loan);
        }
        return result;
    }

    /**
     * 跳转到贷款页面
     * @param params
     * @return
     * <AUTHOR> 2017-9-22
     */
    @RequestMapping(value = {"/loan/{params}","/toLoan"}, method = RequestMethod.GET)
    public String toLoan(@Nullable @PathVariable String params){
       if(params.equals("mortgage")){
            return "/loan/mortgage";
        }else if(params.equals("credit")){
            return "/loan/credit";
        }else if(params.equals("pledge")){
            return "/loan/pledge";
        }else if(params.equals("card")){
           return "/loan/card";
       }
        return "/loan/loanindex";
    }

    /**
     * 验证贷款信息
     * @param loan
     * @return
     */
    public  HashMap checkLoan(Loan loan){
        HashMap hashMap = new HashMap();
        if(StringUtils.isEmpty(loan.getCode())){
            hashMap.put("status",0);
            hashMap.put("msg","验证码不为空");
        }else  if(StringUtils.isEmpty(loan.getPhone())){
            hashMap.put("status",0);
            hashMap.put("msg","手机号不为空");
        }else if(StringUtils.isEmpty(loan.getPrice())){
            hashMap.put("status",0);
            hashMap.put("msg","贷款金额不为空");
        }else if(StringUtils.isEmpty(loan.getRange())){
            hashMap.put("status",0);
            hashMap.put("msg","贷款类型不为空");
        }else if(StringUtils.isEmpty(loan.getType())){
            hashMap.put("status",0);
            hashMap.put("msg","类型不为空");
        }else if(StringUtils.isEmpty(loan.isSex())){
            hashMap.put("status",0);
            hashMap.put("msg","性别不为空");
        }else {
            hashMap.put("status",1);
        }
        return hashMap;
    }
    /**
     * 保存项目咨询
     * @return
     * <AUTHOR> 2017-8-23
     */
    @ResponseBody
    @RequestMapping(value = "/addAsk",method = RequestMethod.POST, produces = "application/json")
    public Map addAsk(@RequestBody HashMap ask){
        Map result = this.checkAsk(ask);
        if(Integer.valueOf(result.get("status").toString()) == 1 ){
            result =  guideService.saveASk(ask);
        }
        return result;
    }
    public Map checkAsk(HashMap ask){
        HashMap result = new HashMap();
        if(ask.size()==0){
            result.put("status",0);
            result.put("msg","咨询内容有误");
        }else if(StringUtils.isEmpty(ask.get("sessionId").toString())){
            result.put("status",0);
            result.put("msg","请登录");
        }else if(StringUtils.isEmpty(ask.get("projectId").toString())){
            result.put("status",0);
            result.put("msg","项目有误");
        }else if(StringUtils.isEmpty(ask.get("askType").toString())){
            result.put("status",0);
            result.put("msg","请登录");
        }else if(StringUtils.isEmpty(ask.get("content").toString())){
            result.put("status",0);
            result.put("msg","内容为空");
        } else if(StringUtils.isEmpty(ask.get("projectType").toString())||"0".equals(ask.get("projectType").toString())){
            result.put("status",0);
            result.put("msg","项目类型有误");
        }else {
            result.put("status",1);
        }
        return result;
    }
    /**
     * 个人向经纪人咨询
     * @return
     * <AUTHOR> 2017-8-23
     */
    @ResponseBody
    @RequestMapping(value = "/addMessageToAgent",method = RequestMethod.POST, produces = "application/json")
    public Map add(@RequestBody HashMap ask){
        Map result = this.checkAskToAgent(ask);
        if(Integer.valueOf(result.get("status").toString()) == 1 ){
            result =  guideService.save(ask);
        }
        return result;
    }
    public Map checkAskToAgent(HashMap ask){
        HashMap result = new HashMap();
        if(ask.size()==0){
            result.put("status",0);
            result.put("msg","咨询内容有误");
        }else if(StringUtils.isEmpty(ask.get("sessionId").toString())){
            result.put("status",0);
            result.put("msg","请登录");
        }else if(StringUtils.isEmpty(ask.get("projectId").toString())){
            result.put("status",0);
            result.put("msg","项目有误");
        }else if(StringUtils.isEmpty(ask.get("comment").toString())){
            result.put("status",0);
            result.put("msg","内容为空");
        }else {
            result.put("status",1);
        }
        return result;
    }
    /**
     * 获取佣金95折订单
     * @return
     * <AUTHOR> 2017-8-23
     */
    @ResponseBody
    @RequestMapping(value = "/getPayOrder",method = RequestMethod.POST)
    public Map getPayOrder(@RequestBody HashMap ask){
        Map result = this.checkPayOrder(ask);
        if(Integer.valueOf(result.get("status").toString()) == 1 ){
            result =  guideService.getPayOrder(ask);
        }
        return result;
    }
    public HashMap checkPayOrder(HashMap payOrder){
        HashMap result = new HashMap();
        if(payOrder.size()==0){
            result.put("status",0);
            result.put("msg","订单内容有误");
        }else if(StringUtils.isEmpty(payOrder.get("houseId").toString())){
            result.put("status",0);
            result.put("msg","房源数据有误");
        }else if(StringUtils.isEmpty(payOrder.get("houseType").toString())){
            result.put("status",0);
            result.put("msg","房源类型不为空");
        }else if(StringUtils.isEmpty(payOrder.get("body").toString())){
            result.put("status",0);
            result.put("msg","内容为空");
        }else {
            boolean flag = true;
            if(StringUtils.isEmpty(payOrder.get("mobile").toString())|| StringUtils.isEmpty(payOrder.get("pwd").toString())){
                if(StringUtils.isEmpty(payOrder.get("sessionId").toString())){
                    flag = false;
                }
            }
            if(flag){
                result.put("status",1);
            }else {
                result.put("status",0);
                result.put("msg","账号，密码不为空");
            }
        }
        return result;
    }
    /**
     * 预约带看
     * @return
     * <AUTHOR> 2017-8-23
     */
    @ResponseBody
    @RequestMapping(value = "/orderGuide", method = RequestMethod.POST)
    public HashMap<String, Object> orderGuide(@Nullable String sessionId, String projectId, Integer type,@Nullable String mobile, @Nullable String code,HttpSession session) {
        HashMap<String, Object> value ;
        if(!StringUtils.isEmpty(sessionId)){
            value=HttpUtil.connectApi(Constants.ORDER_HOUSE_GUIDE, new Params("sessionId", sessionId).add("projectId", projectId).add("type", type).get());
        }else {
            value= HttpUtil.connectApi(Constants.ORDER_HOUSE_GUIDE, new Params("mobile", mobile).add("projectId", projectId).add("type", type).add("code",code).get());
        }
        int status = Double.valueOf((double) value.get("status")).intValue();
        if (status == -1) {
            Utils.removeSession(session);
        }
        return value;
    }

    /**
     * 查看预约状态
     * @return
     * <AUTHOR> 2017-8-23
     */
    @ResponseBody
    @RequestMapping(value = "/checkGuideState", method = RequestMethod.POST)
    public HashMap<String, Object> checkGuideState(String sessionId, String projectId, Integer type) {
        return HttpUtil.connectApi(Constants.CHECK_ORDERSTATE, new Params("sessionId", sessionId).add("projectId", projectId).add("type", type).get());
    }
    /**
     * 查看预约状态
     * @return
     * <AUTHOR> 2017-8-23
     */
    @ResponseBody
    @RequestMapping(value = "/cancelGuide", method = RequestMethod.POST)
    public HashMap<String, Object> cancelGuide(String sessionId, String projectId, Integer type, HttpSession session) {
        HashMap<String, Object> value = HttpUtil.connectApi(Constants.CANCEL_GUIDE, new Params("sessionId", sessionId).add("projectId", projectId).add("type", type).get());
        int status = Double.valueOf((double) value.get("status")).intValue();
        if (status == -1) {
            Utils.removeSession(session);
        }
        return value;
    }

    /**
     * 首页底部抽屉栏目订单添加，新房列表页无验证码订单添加。
     * @return
     * <AUTHOR> 2017-8-23
     */
    @ResponseBody
    @RequestMapping("/saveHouseOrder")
    public Map saveOrder(@RequestBody Guide guide){
        Map result =  guideService.saveGuide(guide);
        return result;
    }

    /**
     * 首页底部抽屉栏目订单添加，新房列表页无验证码订单添加。
     * @return
     * <AUTHOR>
     */
    @ResponseBody
    @RequestMapping(value = "/addSessionPopup",method = RequestMethod.POST)
    public void saveSessionPopup(HttpServletRequest request){
        HttpSession session = request.getSession();
        session.setAttribute("sessionPopup",1);
    }

    /**
     * 跳转保障页面
     * <AUTHOR> 2018-5-10
     */
    @RequestMapping(value = {"/serviceGuarantee#kgdp","/serviceGuarantee#gfdjt","/serviceGuarantee#mfgj","/serviceGuarantee"},method = RequestMethod.GET)
    public String serviceGuarantee(){
        return "serviceGuarantee/serviceGuarantee";
    }

    @ResponseBody
    @RequestMapping(value = "/serviceGuarantee",method = RequestMethod.POST)
    public HashMap<String,Integer> serviceGuaranteeInfo(){
        return guideService.serviceGuarantee();
    }
}
