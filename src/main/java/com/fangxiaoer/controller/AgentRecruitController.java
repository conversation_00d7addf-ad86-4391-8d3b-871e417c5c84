package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Utils;
import com.fangxiaoer.service.AgentRecruitService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

@Controller
public class AgentRecruitController {

    @Autowired
    private AgentRecruitService agentRecruitService;
    /**
     * 经纪人店长招聘列表
     * @Date
     */
    public static final String AGENT_RECRUIT_URL = "/agentRecruit";
    @RequestMapping(value = {AGENT_RECRUIT_URL+"/{params}",AGENT_RECRUIT_URL},method = RequestMethod.GET)
    public String agentRecruit(@Nullable @PathVariable("params") String params, Model model,HttpServletRequest request){
        if (params == null){
            params = "";
        }
        String baseUrl = AGENT_RECRUIT_URL + "/";
        agentRecruitService.getAgentRecuritList(baseUrl,params, model,request);

        model.addAttribute("_year",Utils.getInitYear());
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentRecruit");
        return "agentrecruit/agentRecruitList";
    }

    /**
     * 公司详情页
     * @Date
     */
    public static final String COMPANY_DETAIL_URL = "/companyDetail";
    @RequestMapping(value = COMPANY_DETAIL_URL + "/{companyId}.htm", method = RequestMethod.GET)
    public String getSeDetail(@PathVariable ("companyId") String companyId, Model model){
        LinkedTreeMap<String, Object>  re_list = agentRecruitService.getCompanyDetail(companyId, model);
        if(null == re_list){
            return "redirect:/";
        }
//        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang2/"+companyId+".htm");
        HashMap<String, Object> params = new HashMap<String, Object>();
        HashMap<String,Object> experienceList = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_EXPERIENCE,params);
        List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) experienceList.get("content");
        model.addAttribute("experience",result);
        model.addAttribute("_year",Utils.getInitYear());
        return "agentrecruit/detail/companyDetail";
    }
    /**
     * 职位详情页
     * @Date
     */
    public static final String POSITION_DETAIL_URL = "/positionDetail";
    @RequestMapping(value = POSITION_DETAIL_URL + "/{positionId}.htm", method = RequestMethod.GET)
    public String getPositionDetail(@PathVariable ("positionId") String positionId, Model model){
        LinkedTreeMap<String, Object>  re_list = agentRecruitService.getPositionDetail(positionId, model);
        if(null == re_list){
            return "redirect:/";
        }
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/positionDetail/"+positionId+".htm");
        HashMap<String, Object> params = new HashMap<String, Object>();
        HashMap<String,Object> experienceList = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_EXPERIENCE,params);
        List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) experienceList.get("content");
        // 2020/11/20 10:20 Bowen 获取版块集合、学历要求、入职类型
        HashMap<String, Object> plateListResult = HttpUtil.connectApi(Constants.FILTER_SCDHOUSE_PLATES, params);
        HashMap<String, Object> educationResult = HttpUtil.connectApi(Constants.FILTER_RESUME_EDUCATION, params);
        HashMap<String, Object> inductionResult = HttpUtil.connectApi(Constants.FILTER_RESUME_INDUCTION, params);
        HashMap<String, Object> workingLifeResult = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_EXPERIENCE, params);
        HashMap<String, Object> regionResult = HttpUtil.connectApi(Constants.FILTER_SCDHOUSE_REGIONS, params);
        model.addAttribute("plateList",plateListResult.get("content"));
        model.addAttribute("regionList",regionResult.get("content"));
        model.addAttribute("education", educationResult.get("content"));
        model.addAttribute("induction",inductionResult.get("content"));
        model.addAttribute("workingLife",workingLifeResult.get("content"));
        model.addAttribute("experience",result);
        model.addAttribute("_year",Utils.getInitYear());
        return "agentrecruit/detail/positionDetail";
    }

    /**
     * 申请职位
     * @Date: 2018.11.22
     */

    @ResponseBody
    @RequestMapping(value ="/applyPosition", method = RequestMethod.POST)
    public HashMap<String,Object> getPosition(String mobile, String code,String sessionId,String sex,String realName,String jobWorkingLife,
                                              String jobAdId,Model model,String resumeId,String workedUnits,String birthDate,String livingArea){
        // 2020/11/23 Bowen 复用参数整理(新增other1,出生年月;other3,居住地;other4,工作单位)
        HashMap<String, Object> params = new HashMap<>();
        params.put("mobile", mobile);
        params.put("code",code);
        params.put("sessionId",sessionId);
        params.put("sex",sex);
        params.put("realName",realName);
        params.put("jobWorkingLife",jobWorkingLife);
        params.put("jobAdId",jobAdId);
        params.put("resumeId",resumeId = resumeId == null ? "" : resumeId);
        params.put("birthYear","");//模板接口 出生年月
        params.put("other1",birthDate);//提交接口 出生年月
        params.put("birthDate",birthDate);//模板接口 出生年月
        params.put("other3",livingArea);//提交接口 现居住地
        params.put("livingArea",livingArea);//模板接口 现居住地
        params.put("other4",workedUnits);//提交接口 工作单位
        params.put("workedUnits",workedUnits);//模板接口 工作单位
        // 2020/11/23 Bowen 新增简历模板或修改简历模板
        if(resumeId == null || resumeId.isEmpty()){
            // 2020/11/23 Bowen 保存简历模板
            HttpUtil.connectApi(Constants.ADD_COMMONMEMBER_RESUME,params);
        }else{
            // 2020/11/23 Bowen 如果有简历就修改已有模板
            HttpUtil.connectApi(Constants.SET_COMMONMEMBER_RESUME,params);
        }
        // 申请职位
        HashMap<String,Object> result = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_APPLY,params);
        return result;
    }

    /**
     * 获取会员简历模板
     * @param sessionId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getCommonMemberResume", method = RequestMethod.POST)
    public LinkedTreeMap<String, Object> getCommonMemberResume(String sessionId){
        LinkedTreeMap<String, Object> commonMemberResume = agentRecruitService.getCommonMemberResume(sessionId);
        return commonMemberResume;
    }


}
