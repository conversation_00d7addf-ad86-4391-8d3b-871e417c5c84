package com.fangxiaoer.controller;

import com.fangxiaoer.service.ActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * Created by le<PERSON><PERSON> on 2017/5/4.
 *
 *
 *
 *
 */
@Controller
public class ActivityController {
    @Autowired
    private ActivityService activityService;

    public static String DISCOUNT_BASEURL = "/discount/";
    /**
     * 抢礼包列表
     */
    @RequestMapping(value="/marketing/{inputParams}", method = RequestMethod.GET)
    public String Marketing(@PathVariable("inputParams") String inputParams, Model model){
        activityService.queryActivity(inputParams,model);
        return "house/marketing";
    }

    /**
     * 抢优惠列表页 （mobileUrl配置完成）
     * @param model
     * @return
     * <AUTHOR> 2017-9-11
     */
    @RequestMapping(value = {"/discount/{input}","/discount"})
    public String discount(Model model,@Nullable@PathVariable String input,String systemDate){
        if(input == null) input = "";
        activityService.discount(model,input,DISCOUNT_BASEURL,systemDate);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/activity");
        return "discount/discount";
    }

    /**
     * 帮您找房页
     * @param model
     * @param ids
     * @return
     * <AUTHOR> 2017-9-11
     */
    @RequestMapping(value = {"/helpSearch","/helpSearch.htm"})
    public String helpSearch(Model model, @RequestParam(required = false) Integer ids, @RequestParam(required = false) String ly){
        if(null == ids){
            ids = 1;
        }
        model.addAttribute("helpId",ids);
        model.addAttribute("ids",ids);
        model.addAttribute("ly",ly);
        if(ids == 1){//新房
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/normalsale.htm");
            return "findhouse/helpsearch1";
        }else if(ids == 2){
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/normalsale.htm");
            return "findhouse/helpsearch2";//二手房
        }else if(ids == 3){
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/normalRent.htm");
            return "findhouse/helpsearch3";//租房
        }else if(ids == 4){
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/normalShop.htm");
            return "findhouse/helpsearch4";//商业
        }else if(ids == 5){
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/kfztc.htm");
            return "help/helpsearch2";
        }else if(ids == 9){
            return "findhouse/helpsearch9";//写字楼 企业选址
        }else if(ids == 6){
            return "findhouse/helpsearch6";//装修
        }else{
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/normalnew.htm");
            return "help/helpsearch1";
        }
    }
    @ResponseBody
    @RequestMapping(value = "/viewActivity2", method = RequestMethod.POST)
    public HashMap<String,Object> viewActivity(String sessionId,String projectId,Integer projectType,Integer activityType){
        return activityService.viewActivity(sessionId, projectId, projectType, activityType);
    }

    /**
     * 小二管家抢优惠项目列表
     * ActivityType = 20
     * pageSize = 12;
     * @param model
     * @return
     */
    @RequestMapping(value = {"/viewManagerDiscount","/viewManagerDiscount/{params}"})
    public String viewManagerDiscount (HttpServletRequest request, Model model, @Nullable @PathVariable("params") String params){
        params=StringUtils.isEmpty(params)?"":params;
        String sessionId=StringUtils.isEmpty(request.getSession().getAttribute("muser"))?"":(String)request.getSession().getAttribute("muser");
        activityService.viewManagerDiscountList(model,params,sessionId,"/viewManagerDiscount/");
        return "houseKeeper/Coupon";
    }

    @ResponseBody
    @RequestMapping(value = "/saveCollection", method = RequestMethod.POST)
    public HashMap<String,Object> saveCollection(String sessionId, String memberName, String price, String panTime, String projectAddress, String trafficInfo,
                                                 String layoutInfo, String sortTel, String otherInfo, Integer projectId){
        return activityService.saveCollection(sessionId, memberName, price, panTime, projectAddress, trafficInfo, layoutInfo, sortTel, otherInfo, projectId);
    }

    /**
     * 获取付款的二维码
     *
     * @auther zhouboyu
     *
     * @param payMoney
     * @param payType
     * @param memberName
     * @param activityId
     * @param mobile
     * @param sessionId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/pay_getQR", method = RequestMethod.POST)
    public HashMap<String,Object> pay_getQR(String payMoney, String payType, String memberName, String activityId, String mobile, String sessionId,String body){
        return activityService.pay_getQR(payMoney, payType, memberName, activityId, mobile, sessionId,body);
    }

    /**
     * 轮询请求获取是否在线支付订单已经支付成功
     *
     * @auther zhouboyu
     *
     * @param orderId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/orderPaySuccess", method = RequestMethod.POST)
    public HashMap<String, Object> viewPayOrderDetail(String orderId){
        HashMap<String, Object> map  = activityService.viewPayOrderDetail(orderId);
        //测试代码，正式提交前删掉
//        try {
//            Thread.sleep(5000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        LinkedTreeMap<String, Object> content = (LinkedTreeMap<String, Object>) map.get("content");
//        content.put("status", "1");
//        map.put("content", content);
        return map;
    }

    @RequestMapping(value = "/contactInfo/{orderId}", method = RequestMethod.GET)
    public String viewContract(@PathVariable String orderId, Model model) {
        activityService.viewEarnestPayOrderDetail(orderId, model);
        return "discount/contract_show";
    }

    /**
     * 新房认购->获取户型
     * @param projectId
     * @param buildType
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getLayOut",method = RequestMethod.POST)
    public HashMap<String,Object> getLayOut(@RequestParam("projectId") Integer projectId,@RequestParam("buildType") Integer buildType){
        return activityService.getLayOut(projectId, buildType);
    }

    /**
     * 新房认购->获取楼号
     * @param projectId
     * @param buildType
     * @param layId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getBuildName",method = RequestMethod.POST)
    public HashMap<String,Object> getLayOut(@RequestParam("projectId") Integer projectId,@RequestParam("buildType") Integer buildType, Integer layId){
        return activityService.getBuildName(projectId, buildType,layId);
    }

    /**
     * 新房认购->获取单元
     * @param projectId
     * @param buildType
     * @param layId
     * @param buildId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getUnit",method = RequestMethod.POST)
    public HashMap<String,Object> getUnit(@RequestParam("projectId") Integer projectId,@RequestParam("buildType") Integer buildType, Integer layId,Integer buildId){
        return activityService.getUnit(projectId, buildType,layId,buildId);
    }

    /**
     * 新房认购->获取房号
     * @param projectId
     * @param buildType
     * @param layId
     * @param buildId
     * @param unit
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getRoom",method = RequestMethod.POST)
    public HashMap<String,Object> getRoom(@RequestParam("projectId") Integer projectId,@RequestParam("buildType") Integer buildType, Integer layId,Integer buildId,String unit){
        return activityService.getRoom(projectId, buildType,layId,buildId,unit);
    }

    /**
     * 新房认购->提交订单
     * @param sessionId
     * @param whetherAgent
     * @param IDcard
     * @param memberName
     * @param mobile
     * @param projectId
     * @param buildType
     * @param roomId
     * @param address
     * @param email
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/createOrder",method = RequestMethod.POST)
    public HashMap<String,Object> createOrder(String sessionId,Integer whetherAgent,String IDcard,String memberName,String mobile,Integer projectId,Integer buildType,Integer roomId,String address,String email,String smsCode){
        return activityService.createOrder(sessionId,whetherAgent,IDcard,memberName,mobile,projectId,buildType,roomId,address,email,smsCode);
    }

    /**
     * 新房认购->订单详情
     * @param orderId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getOrderDetail",method = RequestMethod.POST)
    public HashMap<String, Object> getOrderDetail(String orderId){
        return activityService.getOrderDetail(orderId);
    }

    /**
     * 新房认购->支付
     * @param payMoney
     * @param id
     * @param sessionId
     * @param body
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/orderPay",method = RequestMethod.POST)
    public HashMap orderPay(String payMoney, Integer id, String sessionId,String body) {
        return activityService.orderPay(payMoney,id,sessionId,body);
    }

    /**
     * 新房认购->检查支付是否成功
     * @param orderId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/payForSuccess", method = RequestMethod.POST)
    public HashMap payForSuccess( String orderId) {
        return activityService.payForSuccess(orderId);
    }

    /**
     * 新房认购->检查是否签署
     * @param orderId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/checkIsSign",method = RequestMethod.POST)
    public HashMap checkIsSign(String orderId){
        return activityService.checkIsSign(orderId);
    }

    @ResponseBody
    @RequestMapping(value = "/saleOthersFilter", method = RequestMethod.POST)
    public HashMap<String, Object> saleOthersFilter() {
        return activityService.saleOthersFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/saleInfoFilter", method = RequestMethod.POST)
    public HashMap<String, Object> saleInfoFilter() {
        return activityService.saleInfoFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/rentInfoFilter", method = RequestMethod.POST)
    public HashMap<String, Object> rentInfoFilter() {
        return activityService.rentInfoFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/shopInfoFilter", method = RequestMethod.POST)
    public HashMap<String, Object> shopInfoFilter() {
        return activityService.shopInfoFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/saleFeatureFilter", method = RequestMethod.POST)
    public HashMap<String, Object> saleFeatureFilter() {
        return activityService.saleFeatureFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/rentFeatureFilter", method = RequestMethod.POST)
    public HashMap<String, Object> rentFeatureFilter() {
        return activityService.rentFeatureFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/newRegionFilter", method = RequestMethod.POST)
    public HashMap<String, Object> newRegionFilter() {
        return activityService.regionFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/usedRegionFilter", method = RequestMethod.POST)
    public HashMap<String, Object> usedRegionFilter() {
        return activityService.usedRegionFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/layoutFilter", method = RequestMethod.POST)
    public HashMap<String, Object> layoutFilter() {
        return activityService.layoutFilter();
    }

    @ResponseBody
    @RequestMapping(value = "/relationShipFilter", method = RequestMethod.POST)
    public HashMap<String, Object> relationShipFilter() {
        return activityService.relationShipFilter();
    }



    /**
     * 抢优惠 获取补贴项目情况
     *
     * @param projectId 项目id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/viewSubsidyForProject", method = RequestMethod.POST)
    public HashMap<String, Object> viewSubsidyForProject(String projectId) {
        return activityService.viewSubsidyForProject(projectId);
    }

    /**
     * 抢优惠 领取项目补贴
     *
     * @param sessionId     用户表示
     * @param subsidyId     活动ID
     * @param memberName    用户名
     * @param whetherChoose 0-系统分配经纪人，1-自选经纪人
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/fetchSubsidyForProject", method = RequestMethod.POST)
    public HashMap<String, Object> fetchSubsidyForProject(String sessionId, Integer subsidyId, String memberName, Integer whetherChoose) {
        return activityService.fetchSubsidyForProject(sessionId, subsidyId, memberName, whetherChoose);
    }

}
