package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.service.PlotService;
import com.fangxiaoer.service.RentService;
import com.fangxiaoer.service.SecondHouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;

import static com.fangxiaoer.common.Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT;

/***
 *@Author: CDS
 *@Description: 小区
 *@Data: Create in 10:022018-04-10
 *
 **/
@Controller
public class PlotController {
    @Autowired
    private SecondHouseService secondHouseService;
    @Autowired
    private RentService rentService;
    @Autowired
    private PlotService plotService;

    /**
     * 小区详情服务 M站适配链接
     * @param subId
     * @param model
     * @return
     */
    @RequestMapping(value = "/saleVillages/{subId}/index.htm",method = RequestMethod.GET)
//    @RequestMapping(value = "/plotOverview/{subId}.htm",method = RequestMethod.GET)
    public String plotOverview(@PathVariable("subId") String subId, Model model){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/sub/"+subId+".htm");
        rentService.fetchNewSubDetail(subId,model);
        // plotService.getPlotDetail(subId,model);
        return "/plot/plotoverviewQQMap";
    }

    @RequestMapping(value = "/saleVillages2/{subId}/index.htm",method = RequestMethod.GET)
//    @RequestMapping(value = "/plotOverview/{subId}.htm",method = RequestMethod.GET)
    public String plotOverview2(@PathVariable("subId") String subId, Model model){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/sub/"+subId+".htm");
        rentService.fetchNewSubDetail(subId,model);
        // plotService.getPlotDetail(subId,model);
        return "/plot/plotoverview";
    }

    /**
     * 小区二手房列表 M站适配链接
     */
    @RequestMapping(value = {"/saleVillages/{subId:[0-9]+}/plotSecondVillage/{params}","/saleVillages/{subId:[0-9]+}/plotSecondVillage","/saleVillages/{subId:[0-9]+}/plotSecondVillage/"},method = RequestMethod.GET)//salevillage/项目ID/plotSecondvillage.htm
    public String plotSecondHouse(@Nullable @PathVariable("params") String params,@PathVariable("subId") String subId, Model model, String systemDate){
        if (params == null) params = "";
        model.addAttribute("mobileUrl", params == "" ? "https://m.fangxiaoer.com/fang2/z" + subId: "https://m.fangxiaoer.com/fang2/z" + subId);
        String baseUrl = "/saleVillages/"+subId+"/plotSecondVillage/";
        if(StringUtils.isEmpty(params)){
            params = "-v" + subId;
        }
        if(!StringUtils.isEmpty(params) && params.indexOf("-v") == -1){
            params = "-v" + subId + params;
        }

        secondHouseService.getSecondHouseList(baseUrl,params, model);
        plotService.getPlotInfo(subId,model);
        secondHouseService.getRightAdvert(systemDate,model,2);
        return "plot/plotSecondHouseList";
    }

    /**
     * 小区租房列表 M站适配链接
     */
//    public static final String PLOT_RENT_HOUSE = "/plotRentHouse/";
    @RequestMapping(value = {"/saleVillages/{subId:[0-9]+}/plotRentVillage/{params}","/saleVillages/{subId:[0-9]+}/plotRentVillage","/saleVillages/{subId:[0-9]+}/plotRentVillage/"},method = RequestMethod.GET)
//    @RequestMapping(value = {"/plotRentHouse/{params}"},method = RequestMethod.GET)
    public String plotRentHouse(@Nullable @PathVariable("params") String params,@PathVariable("subId") String subId, Model model, HttpServletRequest request, String systemDate){
        if (params == null) params = "";
        model.addAttribute("mobileUrl", params == "" ? "https://m.fangxiaoer.com/fang3/z" + subId: "https://m.fangxiaoer.com/fang3/z" + subId);
        String baseUrl = "/saleVillages/"+subId+"/plotRentVillage/";
        if(StringUtils.isEmpty(params)){
            params = "-v" + subId;
        }
        rentService.getRentsHouse(baseUrl,params, model,"30",request);
        plotService.getPlotInfo(subId,model);
        HashMap<String, Object> resultMap;
        if(systemDate != null){
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("systemDate",systemDate).add("type",3).get(),true);

        }else {
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("type",3).get(),true);
        }
        model.addAttribute("advert",resultMap.get("content"));
        return "plot/plotRentHouseList";
    }

    /**
     * 小区图片页 M站适配链接
     * @param subId
     * @param model
     * @return
     */
    @RequestMapping(value = "/saleVillages/{subId}/plotRealImage.htm",method = RequestMethod.GET)
//    @RequestMapping(value = "/plotRealImage/{subId}",method = RequestMethod.GET)//salevillage/项目ID/plotRealImage/
    public String plotRealImage(@PathVariable("subId") String subId, Model model){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/subphotos/"+subId+"-0.htm");
        plotService.fetchNewSubPhotos(subId,model);
        plotService.viewNewSubBasicInfo(subId, model);
        return "plot/plotImages";
    }

    /**
     * 小区问答列表
     * @param subId
     * @param page
     * @param model
     * @return
     */
    @RequestMapping(value = {"/saleVillages/{subId:[0-9]+}/plotAsk.htm" , "/saleVillages/{subId:[0-9]+}/-n{page:[0-9]+}"},method = RequestMethod.GET)
//   {"/plotAsk/{subId:[0-9]+}.htm" , "/plotAsk/{subId:[0-9]+}-n{page:[0-9]+}"}/salevillage/项目ID/plotAsk.htm
    public String plotAsk(@PathVariable("subId") String subId, @Nullable @PathVariable Integer page, Model model,HttpServletRequest request){
        plotService.getPlotAsk(subId,page,"/saleVillages/",model,request.getSession());
        return "/plot/plotAsks";
    }

    /**
     *问答详情
     * @param askId
     * @param page
     * @param model
     * @return
     */
    @RequestMapping(value = {"/saleVillages/{askId:[0-9]+}/getAskDetail.htm","/saleVillages/{askId:[0-9]+}/getAskDetail/-n{page:[0-9]+}"},method = RequestMethod.GET)
//    @RequestMapping(value = {"/getAskDetail/{askId:[0-9]+}.htm","/getAskDetail/{askId:[0-9]+}-n{page:[0-9]+}"},method = RequestMethod.GET)
    public String getAskDetail(@PathVariable("askId") String askId, @Nullable @PathVariable Integer page, Model model,HttpServletRequest request){
        plotService.getAskDetail(askId,page,"/saleVillages/",model,request);
        return "/plot/plotDetailed";
    }

    /**
     * 添加回答
     * @param askId
     * @param content
     * @param sessionId
     * @return
     */
    @RequestMapping(value = "/addReply",method = RequestMethod.POST)
    @ResponseBody
    public HashMap addReply(@RequestParam String askId,@RequestParam String  content, @RequestParam String sessionId){

        return plotService.addReply(askId,content,sessionId);
    }


    /**
     * 跳转我要提问页
     * @param subId
     * @param model
     * @return
     */
    @RequestMapping(value = "/saleVillages/{subId}/forAddPlotAsk.htm",method = RequestMethod.GET)
//    @RequestMapping(value = "/forAddPlotAsk/{subId}",method = RequestMethod.GET)
    public String forAddPlotAsk(@PathVariable("subId") String subId, Model model){
        plotService.getPlotInfo(subId,model);
        return "/plot/iNeedAsk";
    }


    /**
     * 添加问题
     * @param subId
     * @param subName
     * @param content
     * @param sessionId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addAskWithSub",method = RequestMethod.POST)
    public HashMap<String ,Object> addAskWithSub(@RequestParam Integer subId, @RequestParam String subName,@RequestParam String  content, @RequestParam String sessionId){
        return plotService.addAsk(subId,subName,content,sessionId);
    }


    /**
     * 专家解读列表
     * @param subId
     * @param page
     * @param model
     * @return
     */
    @RequestMapping(value = {"/saleVillages/{subId:[0-9]+}/plotExpertInterpretation.htm","/plotExpertInterpretation/{subId:[0-9]+}/-n{page:[0-9]+}"},method = RequestMethod.GET)
//    @RequestMapping(value = {"/plotExpertInterpretation/{subId:[0-9]+}.htm","/plotExpertInterpretation/{subId:[0-9]+}-n{page:[0-9]+}"},method = RequestMethod.GET)
    public String plotExpertInterpretation(@PathVariable("subId") String subId, @Nullable @PathVariable Integer page, Model model){
        plotService.getPlotExpertInterpretation(subId,page,"/plotExpertInterpretation/",model);
        model.addAttribute("isUnscramble",9);
        return "/plot/plotExpertInterpretations";
    }

    /**
     * 问题列表页
     * @param subId
     * @param page
     * @param model
     * @return
     */
    @RequestMapping(value = {"/saleVillages/{subId:[0-9]+}/getAskNoReply.htm","/saleVillages/{subId:[0-9]+}/getAskNoReply/-n{page:[0-9]+}"},method = RequestMethod.GET)
//    @RequestMapping(value = {"/getAskNoReply/{subId:[0-9]+}.htm","/getAskNoReply/{subId:[0-9]+}-n{page:[0-9]+}"},method = RequestMethod.GET)
    public String getAskNoReply(@PathVariable("subId") String subId, @Nullable @PathVariable Integer page, Model model){//
        plotService.getAskNoReplyList(subId,page,model);
        return "/plot/plotQuestion";
    }


    /**
     *小区配套页
     * @param subId
     * @param model
     * @return
     */
    @RequestMapping(value = "/saleVillages/{subId}/plotSupport.htm",method = RequestMethod.GET)
//    @RequestMapping(value = "/plotSupport/{subId}.htm",method = RequestMethod.GET)/salevillage/项目ID/plotSupport.htm
    public String getPlotSupport(@PathVariable("subId") String subId, Model model){
        plotService.getPlotSupportInfo(subId,model);
        return "/plot/plotSupportQQMap";
    }
    @RequestMapping(value = "/saleVillages2/{subId}/plotSupport.htm",method = RequestMethod.GET)
//    @RequestMapping(value = "/plotSupport/{subId}.htm",method = RequestMethod.GET)/salevillage/项目ID/plotSupport.htm
    public String getPlotSupport2(@PathVariable("subId") String subId, Model model){
        plotService.getPlotSupportInfo(subId,model);
        return "/plot/plotSupport";
    }

    /**
     * 更新回答消息提示
     * @param askId
     * @param session
     */
    @ResponseBody
    @RequestMapping(value = "/msgIsRead",method = RequestMethod.POST)
    public void msgIsRead(@RequestParam("askId") String askId, HttpSession session){
        Params params = new Params().add("askId", askId).add("sessionId", session.getAttribute("sessionId"));
        HttpUtil.connectApi(Constants.UPDATE_MY_ASK_MIND, params.get());
    }

    /**
     * 采纳功能
     * @param sessionId
     * @param replyId 回答id
     * @param askId 问题id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/acceptReplay",method = RequestMethod.POST)
    public HashMap<String ,Object> acceptReplay(String  sessionId,Integer replyId , Integer askId){
        return plotService.acceptReplay(sessionId,replyId,askId);
    }

}
