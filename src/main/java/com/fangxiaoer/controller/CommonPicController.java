package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.google.gson.Gson;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.HashMap;

/**
 * Created by Administrator on 2017/10/13.
 */
@Controller
public class CommonPicController {

    @ResponseBody
    @RequestMapping(value = "/uploadNewCommentPic", method = RequestMethod.POST)
    public void photoUpload(HttpServletResponse response, @RequestParam MultipartFile file, String photoType) {
        PrintWriter writer = null;
        String msg = "";
        try {
            okhttp3.RequestBody body = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("photoType", photoType)
                    .addFormDataPart("file", file.getOriginalFilename(), okhttp3.RequestBody.create(okhttp3.MediaType.parse(file.getContentType()), file.getBytes()))
                    .build();
            OkHttpClient client = new OkHttpClient.Builder().build();
            String json = client.newCall(new Request.Builder().url(Constants.UPLOAD_NEW_PHOTO).post(body).build()).execute().body().string();
            if (!StringUtils.isEmpty(json)) {
                Gson gson = new Gson();
                HashMap<String, Object> resultMap = gson.fromJson(json, HashMap.class);
                msg = (String) resultMap.get("content");
            }
            writer = response.getWriter();
        } catch (Exception e) {
            e.printStackTrace();
            msg = "error";
        }
        writer.write(msg);
    }

    @ResponseBody
    @RequestMapping(value = "/uploadHousePic", method = RequestMethod.POST)
    public HashMap<String, Object> uploadHousePic(@RequestParam MultipartFile file, String photoType) {
        HashMap<String, Object> resultMap = new HashMap<>();
        try{
            okhttp3.RequestBody body = null;
            if(!StringUtils.isEmpty(photoType)){
                body = new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("photoType", photoType)
                        .addFormDataPart("file", file.getOriginalFilename(), okhttp3.RequestBody.create(okhttp3.MediaType.parse(file.getContentType()), file.getBytes()))
                        .build();
            }else{
                body = new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", file.getOriginalFilename(), okhttp3.RequestBody.create(okhttp3.MediaType.parse(file.getContentType()), file.getBytes()))
                        .build();
            }
            OkHttpClient client = new OkHttpClient.Builder().build();
            String json = client.newCall(new Request.Builder().url(Constants.UPLOAD_NEW_PHOTOS).post(body).build()).execute().body().string();
            if (!StringUtils.isEmpty(json)) {
                Gson gson = new Gson();
                resultMap = gson.fromJson(json, HashMap.class);
            }else{
                resultMap.put("status", "0");
                resultMap.put("msg", "服务器异常");
            }
        }catch (Exception e){
            resultMap.put("status", "0");
            resultMap.put("msg", "服务器异常");
        }
        return resultMap;
    }

}
