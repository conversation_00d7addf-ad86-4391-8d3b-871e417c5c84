package com.fangxiaoer.controller;

import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.service.ScriptoriumService;
import com.fangxiaoer.service.SecondHouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

import static com.fangxiaoer.common.Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT;

/**
 * Created by Administrator on 2018/1/4.
 */
@Controller
public class ScriptoriumControler {
    @Autowired
    private ScriptoriumService scriptoriumService;
    @Autowired
    private SecondHouseService secondHouseService;

    public static String SCRIPTORIUM_URL = "/scriptoriums/";
    public static String OFFICEPROJECT_URL = "/officeProjects/";
    /**
     * 写字楼房源列表页初始化 M站链接适配
     * @param model
     * @return
     * <AUTHOR> 2017-9-19
     */
    @RequestMapping(value = {"/scriptoriums/{params}","/scriptoriums"}, method = RequestMethod.GET)
    public String shops(@Nullable @PathVariable String params, Model model, HttpServletRequest request, String systemDate){
        if(params == null) params = "";
        String baseUrl = ScriptoriumControler.SCRIPTORIUM_URL ;
        scriptoriumService.getOffices(baseUrl,params,model,request);
        HashMap<String, Object> resultMap;
        if(systemDate != null){
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("systemDate",systemDate).add("type",5).get(),true);

        }else {
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("type",5).get(),true);
        }
        model.addAttribute("advert",resultMap.get("content"));
        /*传递参数到页面上*/
        model.addAttribute("mobileAgent",params);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang5/" + params);
        return "scriptorium/scriptoriumlist";
    }
    /**
     * 写字楼房源详情页 M站链接适配
     * <AUTHOR> 2017-9-19
     */
    @RequestMapping(value = "/scriptorium/{shopId}.htm", method = RequestMethod.GET)
    public String shop(@PathVariable String shopId, Model model,String systemDate){
        scriptoriumService.getOfficeDetail(shopId, model);
        secondHouseService.getRightAdvert(systemDate,model,5);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang5/"+shopId+".htm");
        return "scriptorium/scriptorium";
    }

    /**
     * 写字楼项目列表页初始化
     * @param model
     * @return
     * <AUTHOR> 2018-3-22
     */
    @RequestMapping(value = {"/officeProjects/{params}","/officeProjects"}, method = RequestMethod.GET)
    public String officeProjects(@Nullable @PathVariable String params, Model model, HttpServletRequest request, String systemDate){
        if(params == null) params = "";
        String baseUrl = ScriptoriumControler.OFFICEPROJECT_URL ;
        scriptoriumService.getOfficeProjects(baseUrl,params,model,request);
        //广告
        HashMap<String, Object> resultMap;
        if(systemDate != null){
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("systemDate",systemDate).add("type",5).get(),true);
        }else {
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("type",5).get(),true);
        }
        model.addAttribute("advert",resultMap.get("content"));
        /*传递参数到页面上*/
        model.addAttribute("mobileAgent",params);
        return "scriptorium/officeprojectlist";
    }

    /**
     * 写字楼房源详情页
     * <AUTHOR> 2017-9-19
     */
    @RequestMapping(value = "/officeProject/{officeId}.htm", method = RequestMethod.GET)
    public String officeProject(@PathVariable String officeId, Model model){
        scriptoriumService.getOfficeProject(officeId, model);
        return "scriptorium/officeproject";
    }
}
