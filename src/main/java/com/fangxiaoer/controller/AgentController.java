package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.ParamsUtil;
import com.fangxiaoer.common.Utils;
import com.fangxiaoer.service.AgentRecruitService;
import com.fangxiaoer.service.AgentService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2017/9/14.
 */
@Controller
public class AgentController {
    @Autowired
    private AgentService agentService;
    @Autowired
    private AgentRecruitService  agentRecruitService;

    /**
     * 风云榜下拉刷新
    * <AUTHOR>
    * @Date 2020/10/26 14:19
    **/
    @ResponseBody
    @RequestMapping(value = "/memberTopListOnload")
    public HashMap<String, Object> memberTopListOnload(Integer regionID, Integer page, Integer pageSize){
        return agentService.quaryMemberTopList(regionID,page,pageSize);
    }

    /**
     * 优秀经纪人风云榜初始化
    * <AUTHOR>
    * @Date 2020/10/26 14:18
    **/
    @RequestMapping(value = {"/memberTopList", "/memberTopList/{params}"}, method = RequestMethod.GET)
    public String memberTopList(@Nullable @PathVariable("params")String params, Model model){
        if(params != null && params != ""){
            model.addAttribute("regionId",params.substring(1));
        }
        String baseUrl = "/memberTopList" + "/";
        params = params == null ? "" : params;
        agentService.quaryRegionFilter(params,model,baseUrl);
        ParamsUtil.addSeoTitle(model, "");
        return "agent/memberTopList";
    }

    /**
     * 经纪人店铺页
     * @param type
     * @param params
     * @param agentId
     * @param model
     * @return
     * <AUTHOR> 2017-9-18
     */
    @RequestMapping(value = {"/agent/{type}/{agentId:^[0-9]+$}/{params}","/agent/{type}/{agentId:^[0-9]+$}"})
    public String agentHouse(@Nullable @PathVariable String type, @Nullable @PathVariable String params, @PathVariable String agentId, Model model, HttpServletRequest request){
        if(params == null) params = "";
        model.addAttribute("agencyId",agentId);
        //agentService.haveIntermediary(model,agentId);
        //查询经纪人店铺信息
        boolean agentState = agentService.getAgentInfo(agentId,model);
        if(agentState){
            return "agent/closeShop";
        }
        if(type.equals("shops")){
            agentService.agentShop(model,agentId,params,null);
            return "agent/shop";
        }else if(type.equals("rents")){
            agentService.agentRent(model,agentId,params,null);
            return "agent/rent";
        }else if (type.equals("newsecond")){
            HashMap agent = agentService.searchAgentshop(agentId,model);
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            String redirectType = content.get("redirectType").toString();
            if(redirectType.equalsIgnoreCase("2")){
                return "redirect:/agent/rents/" + agentId;
            }else if(redirectType.equalsIgnoreCase("3")){
                return "redirect:/agent/shops/" + agentId;
            }else if(redirectType.equalsIgnoreCase("4")){
                return "redirect:/agent/office/" + agentId;
            }
            agentService.agentNewSecond(model,agentId,params,request);
            return "agent/newsecond";
        }else if (type.equals("second") || type.equals("seconds")){
            HashMap agent = agentService.searchAgentshop(agentId,model);
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            String redirectType = content.get("redirectType").toString();
            if(redirectType.equalsIgnoreCase("2")){
                return "redirect:/agent/rents/" + agentId;
            }else if(redirectType.equalsIgnoreCase("3")){
                return "redirect:/agent/shops/" + agentId;
            }else if(redirectType.equalsIgnoreCase("4")){
                return "redirect:/agent/office/" + agentId;
            }
            agentService.agentSecond(model,agentId,params,null);
            return "agent/second";
        }else if (type.equals("office")){
            agentService.agentOffice(model,agentId,params,null);
            return "agent/office";
        }else if (type.equals("dealSecond")){
            agentService.agentDealSecond(model,agentId,params,request);
            return "agent/dealsecond.html";
        }else if (type.equals("dealRents")){
            agentService.agentDealRent(model,agentId,params,request);
            return "agent/dealrent";
        }else if (type.equals("dealShops")){
            agentService.agentDealShop(model,agentId,params,request);
            return "agent/dealshop";
        }else if (type.equals("info")){
            agentService.agentDealInfo(model,agentId,params,request);
            return "agent/info";
        }else if (type.equals("ask")){
            agentService.agentAskInfo(model,agentId,params,request);
            return "agent/agentAskInfo";
        }else if(type.equals("news")){
            agentService.news(model,agentId,params,request);
            return "agent/news";
        }
        else {
            return "";
        }
    }

    /**
     * 店铺机构页面
     */
    @RequestMapping(value = {"/agentIntermediary/{type}/{agentIntermediaryId}/{params}","/agentIntermediary/{type}/{agentIntermediaryId}"})
    public String intermediary(@Nullable @PathVariable String type, @Nullable @PathVariable String params, @PathVariable String agentIntermediaryId, Model model, HttpServletRequest request){
        if(params == null) params = "";
        model.addAttribute("agentIntermediaryId",agentIntermediaryId);
        //查询经纪人店铺信息
        HashMap<String, Object> contentMap = agentService.getIntermediaryInfo(agentIntermediaryId,model);
        String hasHouseFlag = Utils.objToStr(contentMap.get("hasHouseFlag"),"0");
        model.addAttribute("hasHouseFlag", hasHouseFlag);
        //如果机构没有审核通过，跳转默认页面
        if(!"1".equals(contentMap.get("intermediaryState"))){
            return "agent/closeShop";
        }

        if(type.equals("shops")){
            agentService.agentShop(model,null,params,agentIntermediaryId);
            return "agentShop/shop";
        }else if(type.equals("rents")){
            agentService.agentRent(model,null,params,agentIntermediaryId);
            return "agentShop/rent";
        }else if (type.equals("second") || type.equals("seconds")){
            /*HashMap agent = agentService.searchAgentshop(agentId,model);
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            String redirectType = content.get("redirectType").toString();*/
            if(hasHouseFlag.equalsIgnoreCase("2")){
                return "redirect:/agentIntermediary/rents/" + agentIntermediaryId;
            }else if(hasHouseFlag.equalsIgnoreCase("3")){
                return "redirect:/agentIntermediary/shops/" + agentIntermediaryId;
            }else if(hasHouseFlag.equalsIgnoreCase("4")){
                return "redirect:/agentIntermediary/office/" + agentIntermediaryId;
            }
            agentService.agentSecond(model,null,params,agentIntermediaryId);
            return "agentShop/second";
        }else if (type.equals("office")){
            agentService.agentOffice(model,null,params,agentIntermediaryId);
            return "agentShop/office";
        }
        return "";
    }

    //找经纪人列表初始化
    public static final String FIND_AGENT_URL = "/findAgent";
    @RequestMapping(value = {FIND_AGENT_URL+"/{params}",FIND_AGENT_URL},method = RequestMethod.GET)
    public String findAgent(@Nullable @PathVariable("params") String params, Model model){
        if (params == null) params = "";
        String baseUrl = FIND_AGENT_URL + "/";
        agentService.findAgentInfoList(baseUrl,params,model,0);
        return "agent/findAgentList";
    }

    @RequestMapping(value = "/findGoldAgent",method = RequestMethod.GET)
    public String findGoldAgent(Model model){
        agentService.findAgentInfoList("/findGoldAgent/",null,model,1);
        return "agent/findGoldAgent";
    }

    @RequestMapping(value = "/closeShop",method = RequestMethod.GET)
    public String closeShop(Model model){

        return "agent/closeShop";
    }
    /**
     * 中介管理页
     * @param type
     * @param params
     * @param companyId
     * @param model
     * @return
     * @date 2019.03.28
     */
    @RequestMapping(value = {"/agent/intermediary/{type}/{companyId}/{params}","/agent/intermediary/{type}/{companyId}"})
    public String agentIntermediary(@Nullable @PathVariable String type, @Nullable @PathVariable String params, @PathVariable String companyId, Model model, HttpServletRequest request){
        if(params == null) params = "";
        String baseUrl = "";
        model.addAttribute("companyId",companyId);
        if(type.equals("shop")){
            baseUrl = "/agent/intermediary/shop/"+companyId+"/";
            agentService.intermed_pub_info(model,companyId);
            agentService.intermediaryShop(baseUrl,model,companyId,params,request);
            model.addAttribute("firstNavIndex",5);
            return "agent/intermediary/shop";
        }else if(type.equals("office")){
            baseUrl = "/agent/intermediary/office/"+companyId+"/";
            agentService.intermed_pub_info(model,companyId);
            agentService.intermediaryOffice(baseUrl,model,companyId,params,request);
            model.addAttribute("firstNavIndex",9);
            return "agent/intermediary/office";
        }else if (type.equals("rent")){
            baseUrl = "/agent/intermediary/rent/"+companyId+"/";
            agentService.intermed_pub_info(model,companyId);
            agentService.intermediaryRent(baseUrl,model,companyId,params,request);
            model.addAttribute("firstNavIndex",4);
            return "agent/intermediary/rent";
        }else if (type.equals("second")){
            baseUrl = "/agent/intermediary/second/"+companyId+"/";
            agentService.intermed_pub_info(model,companyId);
            agentService.intermediarySecond(baseUrl,model,companyId,params,request);
            model.addAttribute("firstNavIndex",3);
            return "agent/intermediary/second";
        }else if (type.equals("store")){
            agentService.intermediaryStroe(model,companyId,params,request);
            agentService.intermed_pub_info(model,companyId);
            model.addAttribute("firstNavIndex",3);
            return "agent/intermediary/store";
        }else if (type.equals("home")){
            boolean co_home = agentService.intermediaryInfo(model,companyId,params,request);
            if(co_home){
                agentService.intermed_pub_info(model,companyId);
                model.addAttribute("firstNavIndex",3);
                return "agent/intermediary/homenew";
            }else{
                return "redirect:/";
            }
        }else if(type.equals("elite")){
            agentService.intermed_pub_info(model,companyId);
            agentService.intermediarySecond(baseUrl,model,companyId,params,request);
            agentService.intermediaryRent(baseUrl,model,companyId,params,request);
            agentService.intermediaryShop(baseUrl,model,companyId,params,request);
            agentService.intermediaryOffice(baseUrl,model,companyId,params,request);
            model.addAttribute("firstNavIndex",6);
            return "agent/intermediary/elite";
        }else if(type.equals("recruit")){
            agentService.intermed_pub_info(model,companyId);
            agentService.intermediarySecond(baseUrl,model,companyId,params,request);
            agentService.intermediaryRent(baseUrl,model,companyId,params,request);
            agentService.intermediaryShop(baseUrl,model,companyId,params,request);
            agentService.intermediaryOffice(baseUrl,model,companyId,params,request);
            HashMap<String, Object> par = new HashMap<String, Object>();
            HashMap<String,Object> experienceList = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_EXPERIENCE,par);
            HashMap<String, Object> regionResult = HttpUtil.connectApi(Constants.FILTER_SCDHOUSE_REGIONS, par);
            HashMap<String, Object> educationResult = HttpUtil.connectApi(Constants.FILTER_RESUME_EDUCATION, par);
            HashMap<String, Object> inductionResult = HttpUtil.connectApi(Constants.FILTER_RESUME_INDUCTION, par);
            HashMap<String, Object> workingLifeResult = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_EXPERIENCE, par);
            List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) experienceList.get("content");
            model.addAttribute("education", educationResult.get("content"));
            model.addAttribute("induction",inductionResult.get("content"));
            model.addAttribute("workingLife",workingLifeResult.get("content"));
            model.addAttribute("regionList",regionResult.get("content"));
            model.addAttribute("experience",result);
            model.addAttribute("_year", Utils.getInitYear());
            model.addAttribute("firstNavIndex",7);
            return "agent/intermediary/recruit";
        }
        else {
            return "redirect:/";
        }
    }

    @ResponseBody
    @RequestMapping(value = "/viewRecommandYiAgent",method = RequestMethod.POST)
    public HashMap<String, Object> viewRecommandYiAgent(Integer houseId){
        return agentService.viewRecommandYiAgent(houseId);
    }
}
