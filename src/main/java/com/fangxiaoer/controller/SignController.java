package com.fangxiaoer.controller;

import com.fangxiaoer.common.Utils;
import com.fangxiaoer.common.ValidateUtil;
import com.fangxiaoer.service.SignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.HashMap;


/**
 * 用户相关
 */
@Controller
public class SignController {
    private static final String SIGNUP_VIEW_NAME = "home/signup";

    @Autowired
    private SignService signService;

    @RequestMapping(value = "/signin")
    public String signin(Model model,String url, HttpServletRequest request, HttpServletResponse response, HttpSession session,String cookie) {
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/signin");
        if(!StringUtils.isEmpty(cookie)){
            Boolean status = signService.checkCookie(request,response,session);
            if(status && !StringUtils.isEmpty(url)){
                return "redirect:"+url;
            }else if(status){
                return "home/index";
            }
        }
        model.addAttribute("url",url);
        return "redirect:https://my.fangxiaoer.com/login";
    }

    @RequestMapping(value = "/signin", method = RequestMethod.POST)
    public String login(String telnumber,String url, String password, String code, Model model, HttpServletResponse response, HttpSession httpSession) {
        ValidateUtil.checkNull("telnumber", telnumber);
        String pwd = "";
        String show = "";
        if (!StringUtils.isEmpty(password)) {
            pwd = Utils.MD5encode(password);
            show = "密码";
        } else if (!StringUtils.isEmpty(code)) {
            pwd = code;
            show = "验证码";
        } else {
            model.addAttribute("error", "登录错误, 请检查填写信息");
            model.addAttribute("url",url);
            return "redirect:https://my.fangxiaoer.com/login";
        }
        Boolean check = signService.simpleLogin(telnumber,pwd,httpSession,response);
        if (!check) {
            model.addAttribute("error", "用户名或" + show + "错误");
            model.addAttribute("url",url);
            return "redirect:https://my.fangxiaoer.com/login";
        }
        if(!StringUtils.isEmpty(url)){
            return  "redirect:"+url;
        }
        return "index/index";
    }

    @ResponseBody
    @RequestMapping(value = "/login",method = RequestMethod.POST)
    public HashMap<String,Object> login(String telNumber, String code,String password, HttpSession httpSession, HttpServletResponse response, String goods, String registFromUrl){
        if(!StringUtils.isEmpty(password)){
            password = Utils.MD5encode(password);
        }else if(!StringUtils.isEmpty(code)){
            password = code;
        }
        return signService.simpleLogins(telNumber,password,httpSession,response,goods, registFromUrl);
    }
/*    @ResponseBody
    @RequestMapping(value = "/loginComment",method = RequestMethod.POST)
    public HashMap<String,Object> loginComment(String telNumber, String code,String password, HttpSession httpSession, HttpServletResponse response){
        if(!StringUtils.isEmpty(password)){
            password = Utils.MD5encode(password);
        }else if(!StringUtils.isEmpty(code)){
            password = code;
        }
        return signService.simpleLoginsComment(telNumber,password,httpSession,response);
    }*/
    @RequestMapping(value = {"/quit", "/Action/logout.aspx"})
    public String logout(HttpSession session, HttpServletResponse response,HttpServletRequest request){
        signService.logout(session,response,request);
        String referer = request.getHeader("Referer");
        if(StringUtils.isEmpty(referer)) referer = "/";
        return "redirect:" + referer;
    }

    @RequestMapping(value = "/checkSignin")
    @ResponseBody
    public String checkSignin(HttpSession session){
        return signService.checkLogin(session);
    }

    @RequestMapping(value = "/regist")
    public String regist(Model model){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/regist");
        return "home/regist";
    }


    @RequestMapping(value = "/sendSmsCode", method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String,Object> sendSMSCode(String mobile, HttpServletRequest request){
        checkPermission(request);
        return signService.sendSMSCode(mobile);
    }

    public void checkPermission(HttpServletRequest request) {
        // 发不出验证码请申请NeedSMS权限，参考AspectMethod
        String sessionWarrenty = (String) request.getSession().getAttribute("warrenty");
        Cookie cookieWarrenty = Utils.iterationCookie(request, "warrenty");
        if(sessionWarrenty == null || cookieWarrenty == null) {
            ValidateUtil.throwException("No permission");
        } else if(!sessionWarrenty.equals(cookieWarrenty.getValue())) {
            ValidateUtil.throwException("No permission");
        }
    }

    @RequestMapping(value = "/quickregist" , method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String,Object> quickRegist(String telNumber, String code, String loginWay, String loginVerion,HttpSession httpSession,HttpServletResponse response, String registFromUrl){
        return signService.quickRegist(telNumber,code,loginWay,loginVerion,httpSession,response, registFromUrl);
    }

    @ResponseBody
    @RequestMapping(value = "/checkPhone", method = RequestMethod.POST)
    public Integer checkPhone(String mobile, Model model,HttpSession session){
        return signService.checkPhone(mobile,model,session);
    }

    @ResponseBody
    @RequestMapping(value = "/checkPasscode", method = RequestMethod.POST)
    public Integer checkPasscode(String mobile,String code){
        return signService.checkPasscode(mobile,code);
    }

    @RequestMapping(value = "/transit")
    public String transit(Model model){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/transit");
        return "home/transit";
    }

    @RequestMapping(value = "/checkOCRIDCard", method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String,Object> checkOCRIDCard(String imageUrl, String side, String timeMillis) {
        ValidateUtil.checkNull("imageUrl", imageUrl);
        ValidateUtil.checkNull("timeMillis", timeMillis);
        return signService.checkOCRIDCard(imageUrl, side, timeMillis);
    }
}
