package com.fangxiaoer.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/21.
 */
@Controller
public class StaticController {
    @GetMapping("/static/{path}.htm")
    public String staticPage(@PathVariable String path, Model model) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        model.addAttribute("yearTime",sdf.format(new Date()).substring(0,4));
        return "static/" + path;
    }

    /**
     * 房产快搜 (M站适配连接)
     *
     * @param model Model对象
     * @return
     */
    @RequestMapping("/fastSeek")
    public String fastSeek(Model model) {
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fastSeek");
        return "static/fastSeek";
    }

    /**
     * static文件夹下 二级目录 动态路由
     *
     * @param dir   二级目录
     * @param path  页面
     * @return
     */
    @GetMapping("/static/{dir}/{path}.htm")
    public String staticPage(@PathVariable String dir, @PathVariable String path) {
        return "static/" + dir + "/" + path;
    }
}
