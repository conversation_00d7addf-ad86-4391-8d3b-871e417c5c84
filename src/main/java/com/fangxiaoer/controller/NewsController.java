package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.common.ParamsUtil;
import com.fangxiaoer.model.WeizanSearch;
import com.fangxiaoer.service.NewsService;
import com.fangxiaoer.service.SignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;

/**
 * 资讯
 */
@Controller
public class NewsController {

    @Autowired
    private NewsService newsService;
    @Autowired
    private SignService signService;
    /**
     * 资讯列表页 M站链接适配
     */
    public static final String GET_NEWS_LIST = "/news";
    public static final String GET_AGENT_NEWS_LIST = "/agentnews";
    /**
     * 楼盘销售动态路由
     */
    public static final String PROJECT_NEWS_URL = "/projectnews";

    @RequestMapping(value = {GET_NEWS_LIST + "/{params}", GET_NEWS_LIST}, method = RequestMethod.GET)
    public String getVillages(@Nullable @PathVariable("params") String params, Model model, HttpServletRequest request, String systemDate) {
        if (params == null) params = "";
        String baseUrl = GET_NEWS_LIST + "/";
        newsService.getNews(baseUrl, params, model, request, systemDate);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/news"+params+".htm");
        return "news/news";
    }


    /**
     * 资讯详情页 M站链接适配
     */
    @RequestMapping(value = "/news/{id}.htm", method = RequestMethod.GET)
    public String getNewsDetail(@PathVariable String id, Model model, HttpSession session) {
        String redirectUrl = newsService.getNewsDetail(id, model, signService.checkLogin(session));
        if (StringUtils.isEmpty(redirectUrl)) {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/news/"+id+".htm");
            return "news/detail/newssdetail";
        } else {
            if (redirectUrl.startsWith("http")) {
                return "redirect:" + redirectUrl;
            } else {
                return "redirect:https://" + redirectUrl;
            }
        }
    }

    /**
     * 资讯评论 点赞
     */
    @RequestMapping(value = "/news/getNewsCommentsAndLike", method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> getNewsCommentsAndLike(HttpSession session, Integer newsId) {
        String sessionId = signService.checkLogin(session);
        return HttpUtil.connectApi(Constants.NEWS_COMMENTS_AND_LIKE, new Params("sessionId", sessionId).add("newsId", newsId).get());
    }


    /**
     * 视频列表页 M站链接适配
     */
    public static final String GET_VIDEO_LIST = "/videos";

    @RequestMapping(value = {GET_VIDEO_LIST + "/{params}", GET_VIDEO_LIST}, method = RequestMethod.GET)
    public String getVideosList(@Nullable @PathVariable("params") String params, Model model) {
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/video.htm");
        String baseUrl = GET_VIDEO_LIST + "/";
        if (params == null) {
//            newsService.getVideosList(baseUrl, params, model);
            newsService.getNewVideoIndex(baseUrl, params, model);
            return "video/videoIndex";
        }else {
//            newsService.getVideos(baseUrl, params, model);
            boolean result = newsService.getNewVideos(baseUrl, params, model);
            return result ? "video/videoList" : "redirect:" + GET_VIDEO_LIST;
        }
    }


    /**
     * 视频详细页 M站链接适配
     */
    @RequestMapping(value = "/video/{videoId:[0-9]+}.htm")
    public String getVideoDetail(@PathVariable(name ="videoId") String videoId,@PathVariable(name ="projectId",required = false) String projectId, Model model, HttpServletRequest request) {
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/video/"+videoId+".htm");
        if(StringUtils.isEmpty(projectId))projectId="";
//        newsService.getVideoDetail(videoId,projectId, model);
        String sessionId = HttpUtil.checkSessionId(request);
        newsService.getNewVideoDetail(videoId,projectId,sessionId, model);
        return "video/video_details";
    }


    /**
     * 视频详细页 M站链接适配
     */
    @RequestMapping(value = "/video/{videoId:[0-9]+}-{projectId:[0-9]+}.htm")
    public String viewVideoWithProject(@PathVariable(name ="videoId") String videoId,@PathVariable(name ="projectId",required = false) String projectId, Model model, HttpServletRequest request) {
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/video/"+videoId+".htm");
        String sessionId = HttpUtil.checkSessionId(request);
        newsService.getNewVideoDetail(videoId,projectId,sessionId, model);
        model.addAttribute("projectId", projectId);
        return "video/video_project_details";
    }


    /**
     * 小二说房列表页 M站链接适配
     */
    public static final String GET_AUDIO2_LIST = "/audios";

    @RequestMapping(value = {GET_AUDIO2_LIST + "/{params}", GET_AUDIO2_LIST}, method = RequestMethod.GET)
    public String getAudiosList2(@Nullable @PathVariable("params") String params, Model model) {
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/audio.htm");
        if (params == null) params = "";
        String baseUrl = GET_AUDIO2_LIST + "/";
        newsService.getAudiosList(baseUrl, params, model);
        return "news/audios";
    }


    /**
     * 2018-06-05
     * 经纪人资讯列表页面
     * @param params
     * @param model
     * @param request
     * @return
     */
    @RequestMapping(value = {GET_AGENT_NEWS_LIST + "/{params}", GET_AGENT_NEWS_LIST}, method = RequestMethod.GET)
    public String getAgentNewsList(@Nullable @PathVariable("params") String params, Model model, HttpServletRequest request) {
        if (params == null) params = "";
        String baseUrl = GET_AGENT_NEWS_LIST + "/";
        newsService.getAgentNews(baseUrl, params, model, request);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/news"+params+".htm");
        return  "news/agentnews";
    }

    /**
     * 2018-06-05
     * 经纪人资讯详情页 M站链接适配
     */
    @RequestMapping(value = "/agentnews/{id}.htm", method = RequestMethod.GET)
    public String getAgentNewsDetail(@PathVariable String id, Model model) {
        String redirectUrl = newsService.getAgentNewsDetail(id, model);
        if (StringUtils.isEmpty(redirectUrl)) {
            model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/news/"+id+".htm");
            return "news/detail/agentnewsdetail";
        } else {
            if (redirectUrl.startsWith("http")) {
                return "redirect:" + redirectUrl;
            } else {
                return "redirect:https://" + redirectUrl;
            }
        }
    }

    /**
     * 视频列表搜索结果列表
     * @param params
     * @param model
     * @return
     * <AUTHOR> 2018-11-22
     */
    @RequestMapping(value = {"/videoSearchlist/{params}","/videoSearchlist"}, method = RequestMethod.GET)
    public String getVideosResultList(@Nullable @PathVariable("params") String params, Model model) {
        String baseUrl = "/videoSearchlist/";
        if(params == null) params = "";
//        newsService.getSearchVideosList(baseUrl, params, model);
        newsService.getNewSearchVideosList(baseUrl, params, model);
        // 小二精选楼盘
        HttpUtil.connectApi(Constants.GET_RANK_LIST_BYTYPE, new Params("rankType", 1).add("page", "1").add("pageSize", "10").get(), model, "rankList");
        return "video/videoSearchlist";
    }



    /**
     * 视频页面搜索ajax
     * @param hashMap
     * @return
     * <AUTHOR> 2018-11-22
     */
    @ResponseBody
    @RequestMapping(value = {"/videoSearch"}, method = RequestMethod.POST)
    public HashMap getVideosSearchResultList(@RequestParam HashMap hashMap) {
//        return newsService.getVideosSearchResultList(hashMap);
        return newsService.getNewVideosSearchResultList(hashMap);
    }

    /**
     * 添加小二说房数量
     * @param hashMap
     * @return
     * <AUTHOR> 2018-11-28
     */
    @ResponseBody
    @RequestMapping(value = {"/addAudioNum"}, method = RequestMethod.POST)
    public HashMap addAudioNum(@RequestParam HashMap hashMap) {
        return newsService.addAudioNum(hashMap);
    }
    /**
     * 关于收藏操作
     * @param hashMap
     * @return
     * <AUTHOR> 2019-1-4
     */
    @ResponseBody
    @RequestMapping(value = {"/manageMyCollection"}, method = RequestMethod.POST)
    public HashMap manageMyCollection(@RequestParam HashMap hashMap) {
        return newsService.manageMyCollection(hashMap);
    }

    @ResponseBody
    @RequestMapping(value = "/newsCommentLike", method = RequestMethod.POST)
    public HashMap newsCommentLike(String sessionId, Integer newsId) {
        return newsService.newsCommentLike(sessionId, newsId, 1);
    }

    /**
     * 视频点赞功能
     **/
    @ResponseBody
    @RequestMapping(value = "/setVideoLike" , method = RequestMethod.POST)
    public HashMap<String,Object> setVideoLike(String sessionId, Integer videoId){
        return newsService.newsCommentLike(sessionId, videoId, 3);
    }

    @ResponseBody
    @RequestMapping(value = "/addNewsComment", method = RequestMethod.POST)
    public HashMap<String, Object> addNewsComment(String sessionId, Integer newsId, String commentDesc){
        return newsService.addNewsComment(sessionId, newsId, 1, commentDesc);
    }

    @ResponseBody
    @RequestMapping(value = "/addNewsReply", method = RequestMethod.POST)
    public HashMap<String, Object> addNewsReply(String sessionId, String replyDesc, Integer commentId, Integer replyId){
        return newsService.addNewsReply(sessionId, replyDesc, commentId, replyId);
    }

    @ResponseBody
    @RequestMapping(value = "/addNewsCommentLike", method = RequestMethod.POST)
    public HashMap<String, Object> addNewsCommentLike(String sessionId, Integer commentId){
        return newsService.addNewsCommentLike(sessionId, commentId);
    }

    @ResponseBody
    @RequestMapping(value = "/viewSimilarProjectDetail",method = RequestMethod.POST)
    public HashMap<String, Object> viewSimilarProjectDetail(Integer projectId){
        return newsService.viewContrastProject(projectId);
    }

    @ResponseBody
    @RequestMapping(value = "/viewContrastProjects",method = RequestMethod.POST)
    public HashMap<String, Object> viewContrastProjects(Integer projectId){
        return newsService.viewContrastProjects(projectId);
    }

    /**
     * 楼盘销售动态
     *
     * <AUTHOR> power
     * @date 2022-03-25 16:21
     * @param params    筛选参数
     * @param model     Model对象
     * @return java.lang.String
     */
    @RequestMapping(value = {PROJECT_NEWS_URL, PROJECT_NEWS_URL + "/{params}"})
    public String newProjectDynamics(@Nullable @PathVariable("params") String params, Model model) {
        params = params == null ? "" : params;
        String baseUrl = PROJECT_NEWS_URL + "/";
        newsService.newProjectDynamics(baseUrl, params, model);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/projectnews" + params);
        // 动态title 配合html
        ParamsUtil.addSeoTitle(model, "");
        return "news/project_dynamic";
    }



    @RequestMapping(value = "/liveList", method = RequestMethod.GET)
    public String liveList() {
        return "liveHouse/liveList";
    }



    @RequestMapping(value = "/liveDetail/{id}.htm", method = RequestMethod.GET)
    public String liveDetail(@PathVariable String id, Model model) {
        newsService.viewWeizanDetail(id, model);
        return "liveHouse/liveDetail";
    }

    @ResponseBody
    @RequestMapping(value = "/viewWeizanList", method = RequestMethod.POST)
    public HashMap<String, Object> viewWeizanList(WeizanSearch weizanSearch) {
        return newsService.viewWeizanList(weizanSearch);
    }

}
