package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.service.RentService;
import com.fangxiaoer.service.SecondHouseService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * Created by Administrator on 2017/8/24.
 */
@Controller
public class RentController {
    @Autowired
    RentService rentService;
    @Autowired
    private SecondHouseService secondHouseService;
    /**
     * 租房列表页  M站链接适配
     */
    public static final String RENTS_HOUSE_URL = "/rents";
    @RequestMapping(value = {RENTS_HOUSE_URL + "/{params}",RENTS_HOUSE_URL},method = RequestMethod.GET)
    public String showRentsHouseList(@Nullable @PathVariable("params") String params, Model model, HttpServletRequest request, String systemDate){
        if (params == null) params = "";
        String pigeSize = "";
        //行级
        if (params.indexOf("h1") == -1) {
            pigeSize = "60";
        }
        //块级
        else {
            pigeSize = "60";
        }
        String baseUrl = RentController.RENTS_HOUSE_URL+"/";
        rentService.getRentsHouse(baseUrl,params,model,pigeSize,request);
        secondHouseService.getRightAdvert(systemDate,model,3);
        /*传递参数到页面上*/
        model.addAttribute("mobileAgent",params);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang3/" + params);
        return "rent/rents";
    }


    /**
     * 租房成交列表页  M站链接适配
     */
    public static final String DETAIL_RENTS_URL = "/dealRents";
    @RequestMapping(value = {DETAIL_RENTS_URL + "/{params}",DETAIL_RENTS_URL},method = RequestMethod.GET)
    public String dealRentsList(@Nullable @PathVariable("params") String params, Model model, HttpServletRequest request, String systemDate){
        if (params == null) params = "";
        String pigeSize = "";
        //行级
        if (params.indexOf("h1") == -1) {
            pigeSize = "30";
        }
        //块级
        else {
            pigeSize = "28";
        }
        String baseUrl = RentController.DETAIL_RENTS_URL+"/";
        rentService.getDealRentsHouse(baseUrl,params,model,pigeSize,request);
        HashMap<String, Object> resultMap;
        if(systemDate != null){
            resultMap = HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("systemDate",systemDate).add("type",3).get(),true);

        }else {
            resultMap = HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("type",3).get(),true);
        }
        model.addAttribute("advert",resultMap.get("content"));
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/txnRent");
        return "rent/dealrents";
    }

    /**
     * 小区找房列表页  M站链接适配
     */
    public static final String RENT_VILLAGES_HOUSE_URL = "/villages";
    @RequestMapping(value = {RENT_VILLAGES_HOUSE_URL + "/{params}",RENT_VILLAGES_HOUSE_URL},method = RequestMethod.GET)
    public String getVillages(@Nullable @PathVariable("params") String params, Model model,String systemDate){
        if (params == null) params = "";
        String baseUrl = RentController.RENT_VILLAGES_HOUSE_URL + "/";
        rentService.getVillages(baseUrl,params,model);
        secondHouseService.getRightAdvert(systemDate,model,3);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/sub/"+params);
        model.addAttribute("gang","<i class='gang'></i>");
        return "rent/villages";
//        return "redirect:/saleVillages/";
    }


    /**
     * 小区找房详情页  M站链接适配
     */
    @RequestMapping(value = "/village/{input}.htm",method = RequestMethod.GET)
    public String getVillage(@PathVariable("input") String input, Model model){
        //rentService.getVillage(input,model);/saleVillages/{subId}/index.htm
        return "redirect:/saleVillages/"+input+"/index.htm";
    }

    /**
     * 租房详情页
     */
    @RequestMapping(value = "/rent/{input}.htm",method = RequestMethod.GET)
    public String getRent(@PathVariable("input") String input, Model model){
        model.addAttribute("houseTypeReport",2);
        rentService.getRent(input,model);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang3/"+input+".htm");
        return "rent/detail/rentQQMap";
    }

    @RequestMapping(value = "/rent2/{input}.htm",method = RequestMethod.GET)
    public String getRent2(@PathVariable("input") String input, Model model){
        model.addAttribute("houseTypeReport",2);
        rentService.getRent(input,model);
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/fang3/"+input+".htm");
        return "rent/detail/rent";
    }

    /**
     * 租房成交详情页
     */
    @RequestMapping(value = "/dealRent/{input}.htm",method = RequestMethod.GET)
    public String getDealRent(@PathVariable("input") String input, Model model, HttpServletRequest request){
        rentService.getDealRent(input,model);
        String sessionId = HttpUtil.checkSessionId(request);
        if (!StringUtils.isEmpty(sessionId)){
            Integer houseid = Integer.parseInt(input);
            HttpUtil.connectApi(Constants.ADD_DEAL_DRTAIL_ORDER, new Params("houseId", houseid).add("sessionId",sessionId).add("houseType",1).get(), model);
        }
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/txnDetail/"+input+"-2.htm");
        return "rent/detail/dealrent";
    }


    /**
     * 地图找房
     */
    @RequestMapping(value = "/rentmap",method = RequestMethod.GET)
    public String getRentMap(Model model){
        return "rent/rentmap";
    }

    /**
     * 地图找房搜索关键字相关结果
     * @Date 2018.11.05
     */
    @ResponseBody
    @RequestMapping(value = "/searchRentByTitle",method = RequestMethod.GET)
    public List<LinkedTreeMap<String, Object>> search(Model model, @RequestParam("q") String q){
        try {
            q  =  URLDecoder.decode(q,"UTF-8");
        }catch (Exception e){
            e.printStackTrace();
        }
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("search",q);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.QUERY_SEARCH_TITLE_RENT_MAP,params);
        List<LinkedTreeMap<String, Object>> searchList = (List<LinkedTreeMap<String, Object>>) searchs.get("content");
        LinkedTreeMap<String, Object> param = new LinkedTreeMap<String, Object>();
//        model.addAttribute("search",q);
        if(null == searchList){
            searchList = new ArrayList<LinkedTreeMap<String, Object>>();
            param.put("search","");
            param.put("_Id",0);
            searchList.add(0,param);
            return searchList;
        }else if(searchList.size() == 0){
            param.put("search","");
            param.put("_Id",0);
            searchList.add(0,param);
            return searchList;
        }else{
            return searchList;
        }
    }

    /**
     * 地图找房搜索栏查询匹配词的内容(搜索按钮)
     * @Date 2018.11.07
     */
    @ResponseBody
    @RequestMapping(value = "/searchRentOneInfo",method = RequestMethod.POST)
    public List searchOne(Model model, String search){
        try {
            search  =  URLDecoder.decode(search,"UTF-8");
        }catch (Exception e){
            e.printStackTrace();
        }
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("search",search);
        HashMap<String,Object> searchs = HttpUtil.connectApi(Constants.QUERY_SEARCH_OEN_INFO_RENTMAP,params);
        List searchList = (List)searchs.get("content");
        return searchList;
    }

    /**
     * 地图找房  右侧地图
     * @param hashMap
     * @return
     * @Date 2018.11.09
     */
    @RequestMapping(value = "/searchRentMap" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> queryRentMap(@RequestParam HashMap hashMap) {
        HashMap<String, Object> secondMap = rentService.queryRentHouse(hashMap);
        return secondMap;
    }

    /**
     * 地图找房  左侧列表
     * @param hashMap
     * @return
     * @Date 2018.11.09
     */
    @RequestMapping(value = "/searchRentMapLeftList" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> queryRentMapLeftList(@RequestParam HashMap<String ,Object> hashMap) {
        HashMap<String, Object> secondMap = rentService.queryRentHouseList(hashMap);
        return secondMap;
    }

    /**
     * 租房通过位置地址获取坐标
     * @param hashMap
     * @return
     */
    @RequestMapping(value = "/queryLocationByAddress" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> queryLocationByAddress(@RequestParam HashMap<String ,Object> hashMap){
        HashMap<String, Object> location = rentService.queryLocationByAddress(hashMap);
        return location;
    }
    /**
     * 地铁  站点及小区信息
     * @return
     * @date 2019.09.10
     */
    @RequestMapping(value = "/rentMapDualInfo" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> rentStationMap(@RequestParam HashMap hashMap) {
        return rentService.rentStationMap(hashMap);
    }
    /**
     * 地铁地图  左侧列表数据
     * @param hashMap
     * @return
     * @Date 2019.09.11
     */
    @RequestMapping(value = "/getRentMapLeftList" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> getRentMapLeftList(@RequestParam HashMap<String ,Object> hashMap) {
        HashMap<String, Object> rentMap = rentService.getRentHouseList(hashMap);
        return rentMap;
    }
}
