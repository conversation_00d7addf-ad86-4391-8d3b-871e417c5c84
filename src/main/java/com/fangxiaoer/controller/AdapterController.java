package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.ParamsUtil;
import com.fangxiaoer.common.Utils;
import com.fangxiaoer.service.HouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;

@Controller
public class AdapterController {

    @Autowired
    private HouseService houseService;

    //新房一般普宅详情
    @RequestMapping(value = "/house/view/{id:^[0-9]+$}", method = RequestMethod.GET)
    public String residenceDetailMapping(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-1.htm";
    }

    //新房一般别墅详情
    @RequestMapping(value = "/villa/view/{id:^[0-9]+$}", method = RequestMethod.GET)
    public String villaDetailMapping(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-2.htm";
    }

    //新房一般写字间详情
    @RequestMapping(value = "/office/view/{id:^[0-9]+$}", method = RequestMethod.GET)
    public String officeDetailMapping(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-3.htm";
    }

    //新房VIP项目
    @RequestMapping(value = "/dealer/house/view/{id:^[0-9]+$}", method = RequestMethod.GET)
    public String vipDetailMapping(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-1.htm";
    }

    //新房列表页适配
    @RequestMapping(value = {"/house/Default.aspx", "/house/", "/house", "/house/{type:[-?\\d+_]{5,}}"}, method = RequestMethod.GET)
    public String newHouseMapping() {
        return "redirect:/houses";
    }

    //洋房列表
    @RequestMapping(value = "/house/foreignhouse/", method = RequestMethod.GET)
    public String foregionHouseMapping() {
        return "redirect:/houses/pt2";
    }

    //新房商铺列表
    @RequestMapping(value = "/house/shops/", method = RequestMethod.GET)
    public String shopsMapping() {
        return "redirect:/houses/pt3";
    }

    //新房公寓列表
    @RequestMapping(value = {"/office", "/office/", "/office/{type:^[-?\\d?_?]+$}"}, method = RequestMethod.GET)
    public String officeMapping() {
        return "redirect:/houses/pt4";
    }

    //新房别墅列表
    @RequestMapping(value = {"/villa/", "/villa"}, method = RequestMethod.GET)
    public String villaMapping() {
        return "redirect:/houses/pt5";
    }

    //抢优惠
    @RequestMapping(value = "/marketing/default.aspx", method = RequestMethod.GET)
    public String activityMapping() {
        return "redirect:/discount/";
    }

    //新房普宅户型图1
    @RequestMapping(value = "/house/housestyle.aspx", method = RequestMethod.GET)
    public String viewVillas(@RequestParam String ProjectID) {
        return "redirect:/house/" + ProjectID + "-1/layout/pid" + ProjectID + "-pt1.htm";
    }

    //新房普宅户型图2
    @RequestMapping(value = {"/house/housestyle/{pid:^\\d+$}", "/house/list/{pid:^\\d+$}", "/house/list/{pid}_{lid}", "/house/listd/{pid}_{lid}_{hid}_{rid}"}, method = RequestMethod.GET)
    public String viewLayouts(@PathVariable Integer pid, @Nullable @PathVariable Integer lid, @Nullable @PathVariable Integer hid, @Nullable @PathVariable Integer rid) {
        if (StringUtils.isEmpty(lid)) {
            return "redirect:/house/" + pid + "-1/layout/pid" + pid + "-pt1.htm";
        } else {
            return "redirect:/house/" + pid + "-1/layout/pid" + pid + "-pt1-l" + lid;
        }
    }

    //精装房列表
    @RequestMapping(value = {"/jingzhuang/s/", "/jingzhuang/s/*"}, method = RequestMethod.GET)
    public String viewDecorations() {
        return "redirect:/decorations";
    }

    //品牌房企
    @RequestMapping(value = {"/Brand/s/", "/Brand/s/*"}, method = RequestMethod.GET)
    public String viewBrands() {
        return "redirect:/brands";
    }

    //新房别墅基本信息
    @RequestMapping(value = "/villa/villa_details/{id}", method = RequestMethod.GET)
    public String villaInfoMapping(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-2/info.htm";
    }

    //地铁沿线
    @RequestMapping(value = {"/subway", "/subway/{type:^[-?\\d?_?]+$}"}, method = RequestMethod.GET)
    public String viewSubways() {
        return "redirect:/subways";
    }

    //优质学区
    @RequestMapping(value = {"/school", "/schoolhouse", "/school/{type:^[-?\\d?_?]+$}"}, method = RequestMethod.GET)
    public String viewSchools() {
        return "redirect:/schools";
    }

    //低首付
    @RequestMapping(value = {"/logpay", "/logpay/{type:^[-?\\d?_?]+$}"}, method = RequestMethod.GET)
    public String viewLowPays() {
        return "redirect:/lowpays";
    }

    //新房地图找房
    @RequestMapping(value = "/house/housemap/", method = RequestMethod.GET)
    public String viewNewHouseMap() {
        return "redirect:/static/houseMap.htm";
    }

    //新房普宅基本信息
    @RequestMapping(value = {"/house/info/{id:^\\d+}", "/house/info/{id:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String houseInfoMapping(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-1/info.htm";
    }

    //新房基础信息
    @RequestMapping(value = "/house/info/{projectId:[0-9]+}-{projectType:[0-9]+}.htm", method = RequestMethod.GET)
    public String houseInfoMapping(@PathVariable Integer projectId, @PathVariable Integer projectType) {
        return "redirect:/house/" + projectId + "-" + projectType + "/info.htm";
    }

    //户型图列表
    @RequestMapping(value = "/house/layout/{params}", method = RequestMethod.GET)
    public String getRoomListMapping(@PathVariable("params") String params) {
        HashMap<String, String> resultMap = ParamsUtil.analysisInput(params, Constants.HOUSE_LAYOUT_INFO);
        return "redirect:/house/" + resultMap.get("projectId") + "-" + resultMap.get("projectType") + "/layout/" + params;
    }

    //别墅户型图列表
    @RequestMapping(value = "/villa/layout/{projectId}.htm", method = RequestMethod.GET)
    public String villaLayout(@PathVariable("projectId") String projectId) {
        return "redirect:/house/" + projectId + "-2/layout.htm";
    }

    //别墅户型图
    @RequestMapping(value = "/villa/villastyle/{id}", method = RequestMethod.GET)
    public String viewVillaLayout(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-2/layout.htm";
    }

    //获取别墅户型详情适配
    @RequestMapping(value = "/villa/sublayout/{projectId}-{layId}.htm", method = RequestMethod.GET)
    public String villaSubLayout(@PathVariable("projectId") String projectId, @PathVariable("layId") String layId) {
        return "redirect:/house/" + projectId + "-2/sublayout/" + layId + ".htm";
    }

    //新房普宅在线选房（废弃）
    @RequestMapping(value = "/house/online/{a}_{b}", method = RequestMethod.GET)
    public String resOnlineMapping(@PathVariable Integer a, @PathVariable Integer b) {
        if (b.equals(0)) {
            return "redirect:/house/onlines/-pid" + a + "-pt1";
        }
        return "redirect:/house/onlines/r" + b + "-pid" + a + "-pt1";
    }

    //新房写字间在线选房(废弃）
    @RequestMapping(value = "/office/online/{id:^\\d+}", method = RequestMethod.GET)
    public String officeOnlineMapping(@PathVariable Integer id) {
        return "redirect:/house/onlines/pid" + id + "-pt3";
    }

    //在线选房详情(废弃）
    @RequestMapping(value = "/house/room/{a}_{b}", method = RequestMethod.GET)
    public String onlineDetailMapping(@PathVariable Integer a, @PathVariable String b) {
        if (b.indexOf("#") != -1) {
            String[] params = b.split("#");
            return "redirect:/house/online/" + a + "-1-" + params[0] + ".htm#" + params[1];
        }
        return "redirect:/house/online/" + a + "-1-" + b + ".htm";
    }

    //新房普宅相册
    @RequestMapping(value = "/house/photo/{a}_{b}", method = RequestMethod.GET)
    public String resPhoto(@PathVariable Integer a, @PathVariable String b) {
        return "redirect:/house/" + a + "-1/album.htm";
    }

    //新房写字间相册
    @RequestMapping(value = "/office/photo/{a}_{b}", method = RequestMethod.GET)
    public String officePhoto(@PathVariable Integer a, @PathVariable String b) {
        return "redirect:/house/" + a + "-3/album.htm";
    }

    //新房别墅相册
    @RequestMapping(value = "/villa/photo/{id:^\\d+}", method = RequestMethod.GET)
    public String villaPhoto(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-2/album.htm";
    }

    //新房别墅相册详细页
    @RequestMapping(value = "/picture/view/{projectType}_{projectId}_{photoType}_{photoId}", method = RequestMethod.GET)
    public String villaPhotoDetail(@PathVariable Integer projectType, @PathVariable Integer projectId, @PathVariable Integer photoType, @PathVariable Integer photoId) {
        return "redirect:/house/" + projectId + "-2/album.htm";
    }

    //普宅写字楼相册页面
    @RequestMapping(value = {"/house/album/{projectId:\\d+}-{projectType:\\d+}.htm", "/house/album/{projectId:\\d+}-{projectType:\\d+}-{photoType}.htm"}, method = RequestMethod.GET)
    public String resAndOfficeAlbumMapping(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable String photoType) {
        if (!StringUtils.isEmpty(photoType)) {
            return "redirect:/house/" + projectId + "-" + projectType + "/album/" + photoType + ".htm";
        } else {
            return "redirect:/house/" + projectId + "-" + projectType + "/album.htm";
        }
    }

    //别墅相册页
    @RequestMapping(value = "/villa/photos/{projectId}.htm", method = RequestMethod.GET)
    public String villaAlbumMapping(@PathVariable String projectId) {
        return "redirect:/house/" + projectId + "-2/album.htm";
    }

    //项目评价
    @RequestMapping(value = {"/house/appraise/{projectId:[0-9]+}-{projectType:[0-9]+}.htm{params}"}, method = RequestMethod.GET)
    public String houseAppraiseMapping(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable String params) {
        if (projectType.equals("2")) {
            return "redirect:/house/" + projectId + "-" + projectType + ".htm" + params;
        }
        return "redirect:/house/" + projectId + "-" + projectType + "/appraise.htm" + params;
    }

    //新房普宅项目评价
    @RequestMapping(value = "/house/view/{id}#xmpj", method = RequestMethod.GET)
    public String resComment(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-1/appraise.htm";
    }

    //新房写字间项目评价
    @RequestMapping(value = "/office/view/{id}#xmpj", method = RequestMethod.GET)
    public String officeComment(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-3/appraise.htm";
    }

    //项目咨询
    @RequestMapping(value = {"/house/ask/{projectId:[0-9]+}-{projectType:[0-9]+}.htm", "/house/ask/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}"}, method = RequestMethod.GET)
    public String houseAskMapping(@PathVariable Integer projectId, @PathVariable String projectType, @Nullable @PathVariable Integer page) {
        if (!StringUtils.isEmpty(page)) {
            return "redirect:/house/" + projectId + "-" + projectType + "/ask/" + page + ".htm";
        } else {
            return "redirect:/house/" + projectId + "-" + projectType + "/ask.htm";
        }
    }

    //新房普宅项目咨询
    @RequestMapping(value = "/house/view/{id}#xmzx", method = RequestMethod.GET)
    public String resAsk(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-1/ask.htm";
    }

    //新房写字间项目咨询
    @RequestMapping(value = "/office/view/{id}#xmzx", method = RequestMethod.GET)
    public String officeAsk(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-3/ask.htm";
    }

    //销售动态
    @RequestMapping(value = {"/house/news/{projectId:[0-9]+}-{projectType:[0-9]+}.htm", "/house/news/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}"}, method = RequestMethod.GET)
    public String villaNewsMapping(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable Integer page) {
        if (!StringUtils.isEmpty(page)) {
            return "redirect:/house/" + projectId + "-" + projectType + "/news/" + page + ".htm";
        } else {
            return "redirect:/house/" + projectId + "-" + projectType + "/news.htm";
        }
    }

    //新房别墅销售动态
    @RequestMapping(value = "/villa/news/{id}", method = RequestMethod.GET)
    public String villaSaleInfo(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-2/news.htm";
    }

    //新房写字间最新动态
    @RequestMapping(value = "/office/news/{id}", method = RequestMethod.GET)
    public String officeSaleInfo(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-3/news.htm";
    }

    //新房普宅销售动态
    @RequestMapping(value = "/house/news/{id:^[0-9]+$}", method = RequestMethod.GET)
    public String houseNews(@PathVariable String id) {
        return "redirect:/house/" + id + "-1/news.htm";
    }

    //新房写字间平层图
    @RequestMapping(value = "/office/plan/{id}", method = RequestMethod.GET)
    public String viewPlan(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-3.htm";
    }

    //楼盘说说适配
    @RequestMapping(value = {"/house/say/{projectId:[0-9]+}-{projectType:[0-9]+}.htm", "/house/say/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}"}, method = RequestMethod.GET)
    public String viewHouseSayMapping(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable Integer page) {
        if (!StringUtils.isEmpty(page)) {
            return "redirect:/house/" + projectId + "-" + projectType + "/say/" + page + ".htm";
        } else {
            return "redirect:/house/" + projectId + "-" + projectType + "/say.htm";
        }
    }

    //新房普宅楼盘说说
    @RequestMapping(value = "/house/say/{id:^\\d+}", method = RequestMethod.GET)
    public String viewHouseSay(@PathVariable Integer id) {
        return "redirect:/house/" + id + "-1/say.htm";
    }

    //720看房
    @RequestMapping(value = {"/pic720/{projectId:[0-9]+}-{projectType:[0-9]+}.htm", "/pic720/{projectId:[0-9]+}-{projectType:[0-9]+}-{panId:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String getPictureMapper(@PathVariable String projectId, @PathVariable String projectType, @Nullable @PathVariable String panId) {
        if (!StringUtils.isEmpty(panId)) {
            return "redirect:/house/" + projectId + "-" + projectType + "/pic720/" + panId + ".htm";
        } else {
            return "redirect:/house/" + projectId + "-" + projectType + "/pic720.htm";
        }
    }

    //新房抢优惠
    @RequestMapping(value = "/activity")
    public String mDiscountList() {
        return "redirect:/";
    }

    /**
     * 商铺列表页适配
     *
     * @return
     */
    @RequestMapping(value = {"/UHouse/shangpu/", "/UHouse/shangpu", "/uhouse/shangpu/{params}"}, method = RequestMethod.GET)
    public String shopList(@Nullable @PathVariable String params) {
        return "redirect:/shops";
    }

    /**
     * 商铺详情页适配
     *
     * @param id
     * @return
     */
    @RequestMapping(value = {"/uhouse/shangpu/view/{id}", "/Uhouse/shangpu/view/{id}"}, method = RequestMethod.GET)
    public String shopDetial(@PathVariable String id) {
        StringBuffer url = new StringBuffer("/shop/");
        url.append(id).append(".htm");
        return "redirect:" + url.toString();
    }

    /**
     * 经纪人店铺页适配
     *
     * @return
     */
    @RequestMapping(value = {"/broker/{type}/{id}"}, method = RequestMethod.GET)
    public String agentHouse(@PathVariable String type, @PathVariable String id) {
        id = Utils.regId(id);
        StringBuffer url = new StringBuffer("/agent/");
        if (type.equals("sp") || type.equals("sps")) {
            type = "shops";
        } else if (type.equals("zf") || type.equals("zfs")) {
            type = "rents";
        } else {
            type = "second";
        }
        url.append(type).append("/").append(id);
        return "redirect:" + url.toString();
    }

    /**
     * 帮你找房适配
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = {"/formulate/house.aspx", "/formulate//formulate/house.aspx{params}"}, method = RequestMethod.GET)
    public String helpSearch(@RequestParam(required = false) String ids) {
        if (!StringUtils.isEmpty(ids)) {
            return "redirect:/helpSearch?ids=" + ids;
        }
        return "redirect:/helpSearch";
    }

    //房仔中心>房仔活动
    @RequestMapping(value = "/need/homeAct.aspx")
    public String houseActivityMapping() {
        return "redirect:/activities";
    }

    //房仔中心>小二管家
    @RequestMapping(value = "/need/")
    public String houseKeeperMapping() {
        return "redirect:/houseKeeper/";
    }

    //房仔中心>积分商城
    @RequestMapping(value = "/need/integral.aspx")
    public String pointsMallMapping() {
        return "redirect:/pointsMall";
    }

    //房仔中心>购房攻略
    @RequestMapping(value = "/need/strategy.aspx")
    public String strategyMapping() {
        return "redirect:/strategy/147";
    }

    //房仔中心详细页
    @RequestMapping(value = "/need/view/{path}", method = RequestMethod.GET)
    public String activityMapping(@PathVariable String path) {
        return "redirect:/news/" + path + ".htm";
    }

    //积分商城详细页
    @RequestMapping(value = "/need/goods.aspx", method = RequestMethod.GET)
    public String getGoodsMapping(@RequestParam String goodsid) {
        return "redirect:/goods/" + goodsid + ".htm";
    }

    //免费发布>二手房
    @RequestMapping(value = "/UHouse/sale/SaleHouse.aspx", method = RequestMethod.GET)
    public String publishSaleMapping(@RequestParam @Nullable String HouseID) {
        if (HouseID == null) {
            HouseID = "";
        }
        return "redirect:/secondPublish/" + HouseID;
    }

    //免费发布>租房>整套
    @RequestMapping(value = "/UHouse/rent/rent_form_1.aspx", method = RequestMethod.GET)
    public String publishRentWMapping(@RequestParam @Nullable String HouseID) {
        if (HouseID == null) {
            HouseID = "";
        }
        return "redirect:/rentwhole/" + HouseID;
    }

    //免费发布>租房>单间
    @RequestMapping(value = "/UHouse/rent/rent_form_2.aspx", method = RequestMethod.GET)
    public String publishRentSMapping(@RequestParam @Nullable String HouseID) {
        if (HouseID == null) {
            HouseID = "";
        }
        return "redirect:/rentsingls/" + HouseID;
    }

    //免费发布>商铺>出售
    @RequestMapping(value = "/UHouse/shangpu/sale.aspx")
    public String publishShopSMapping() {
        return "redirect:/shopsell";
    }

    //免费发布>商铺>出租
    @RequestMapping(value = "/UHouse/shangpu/rent.aspx")
    public String publishShopRMapping() {
        return "redirect:/shoprent";
    }

    //免费发布>商铺>出兑
    @RequestMapping(value = "/UHouse/shangpu/attorn.aspx")
    public String publishShopTMapping() {
        return "redirect:/shoptransfer";
    }

    //免费发布>商铺>出售
    @RequestMapping(value = "/uhouse/shangpu/sale.aspx", method = RequestMethod.GET)
    public String publishshopSUPdateMapping(@RequestParam @Nullable String HouseID) {
        if (HouseID == null) {
            HouseID = "";
        }
        return "redirect:/shopsell/" + HouseID;
    }

    //免费发布>商铺>出租
    @RequestMapping(value = "/uhouse/shangpu/rent.aspx", method = RequestMethod.GET)
    public String publishShopRUPdateMapping(@RequestParam @Nullable String HouseID) {
        if (HouseID == null) {
            HouseID = "";
        }
        return "redirect:/shoprent/" + HouseID;
    }

    //免费发布>商铺>出兑
    @RequestMapping(value = "/uhouse/shangpu/attorn.aspx", method = RequestMethod.GET)
    public String publishShopTUPdateMapping(@RequestParam @Nullable String HouseID) {
        if (HouseID == null) {
            HouseID = "";
        }
        return "redirect:/shoptransfer/" + HouseID;
    }

    //资讯详细页
    @RequestMapping(value = "/news/view/{path}", method = RequestMethod.GET)
    public String activityDetailMapping(@PathVariable String path) {
        return "redirect:/news/" + path + ".htm";
    }

    //视频详情页
    @RequestMapping(value = "/video/{value}_{para}", method = RequestMethod.GET)
    public String videoMapping(@PathVariable Integer value, @PathVariable String para) {
        return "redirect:/video/" + para + ".htm";
    }

    //租房列表页
    @RequestMapping(value = "/UHouse/rent/{para}", method = RequestMethod.GET)
    public String rentsMapping(@PathVariable String para) {
        if (para.equals("RentSubway")) {
            return "redirect:/rents/k1";
        } else {
            return "redirect:/rents/";
        }
    }

    //租房列表页
    @RequestMapping(value = "/UHouse/rent//RentRegion/{para}", method = RequestMethod.GET)
    public String rentsRegionMapping(@PathVariable String para) {
        return "redirect:/rents/";
    }

    //租房列表页
    @RequestMapping(value = "/UHouse/rent/RentSubway/{para}", method = RequestMethod.GET)
    public String rentsSubwayMapping(@PathVariable String para) {
        return "redirect:/rents/k1";
    }

    //租房详情页
    @RequestMapping(value = "/UHouse/rent/view/{para}", method = RequestMethod.GET)
    public String rentMapping(@PathVariable String para) {
        return "redirect:/rent/" + para + ".htm";
    }

    //租房>小区找房列表页
    @RequestMapping(value = "/UHouse/Subdistrict/SubdistrictListRt/", method = RequestMethod.GET)
    public String valligesMapping() {
        return "redirect:/villages/";
    }

    //租房>小区找房详情页
    @RequestMapping(value = "/UHouse/Subdistrict/view/{para}", method = RequestMethod.GET)
    public String valligeMapping(@PathVariable String para) {
        return "redirect:/village/" + para + ".htm";
    }

    /**
     * sy 租房区域、地铁模式地图找房（内容最新版，链接没变）
     * @return
     * @Date 2019.10.14
     */
    @RequestMapping(value = "/rent/housemap/", method = RequestMethod.GET)
    public String rentMapMapping() {
        return "redirect:/static/rentmap.htm";
    }

    //购房能力评估
    @RequestMapping(value = "/tool/jsq/goufangnenglipinggu.aspx", method = RequestMethod.GET)
    public String abilityMapping() {
        return "redirect:/static/ability.htm";
    }

    //提前还款计算器
    @RequestMapping(value = "/tool/jsq/tiqianhuankuan.aspx", method = RequestMethod.GET)
    public String advanceMapping() {
        return "redirect:/static/advance.htm";
    }

    //组合贷款计算器
    @RequestMapping(value = "/tool/jsq/zuhedaikuan.aspx", method = RequestMethod.GET)
    public String assembleMapping() {
        return "redirect:/static/assemble.htm";
    }

    //商业贷款计算器
    @RequestMapping(value = "/tool/jsq/shangyedaikuan.aspx", method = RequestMethod.GET)
    public String businessMapping() {
        return "redirect:/static/business.htm";
    }

    //公积金贷款计算器
    @RequestMapping(value = "/tool/jsq/gongjijindaikuan.aspx", method = RequestMethod.GET)
    public String fundMapping() {
        return "redirect:/static/fund.htm";
    }

    //公积金贷款额度评估
    @RequestMapping(value = "/tool/jsq/gongjijindaikuanedupinggu.aspx", method = RequestMethod.GET)
    public String fundAssessMapping() {
        return "redirect:/static/fundAssess.htm";
    }

    //税费计算器
    @RequestMapping(value = "/tool/jsq/shuifei.aspx", method = RequestMethod.GET)
    public String tallageMapping() {
        return "redirect:/static/tallage.htm";
    }

    //二手房列表页适配
    @RequestMapping(value = {"/UHouse/sale", "/UHouse/sale/", "/UHouse/sale/Region",
            "/UHouse/sale/UHouseSubway", "/UHouse/sale/Region/{param:^[-?\\d?_?]+$}", "/UHouse/sale/UHouseSubway/{param:^[-?\\d?_?]+$}"}, method = RequestMethod.GET)
    public String secondeHouseListMapping(@Nullable @PathVariable("param") String param) {
        if (!StringUtils.isEmpty(param) && param.contains("UHouseSubway")) {
            return "redirect:/saleHouses/z1";
        } else {
            return "redirect:/saleHouses/";
        }
    }

    //二手房详情页适配
    @RequestMapping(value = {"/UHouse/sale/view" + "/{houseId}", "/UHouse/sale/SaleFullDetail" + "/{houseId}"}, method = RequestMethod.GET)
    public String secondeHouseDetailMapping(@PathVariable("houseId") String houseId) {
        String rel = "redirect:/salehouse/" + houseId + ".htm";
        return rel;
    }

    //二手房小区找房列表页适配
    @RequestMapping(value = {"/UHouse/Subdistrict/SubdistrictList", "/UHouse/Subdistrict/SubdistrictList/{params}"}, method = RequestMethod.GET)
    public String secondHouseVillagesMapping() {
        return "redirect:/saleVillages/";
    }

    //二手房地图找房
    @RequestMapping(value = {"/UHouse/housemap/", "/UHouse/housemap"}, method = RequestMethod.GET)
    public String secondeHouseMapMapping() {
        return "redirect:/salemap";
    }

    //资讯列表页
    @RequestMapping(value = "/News/FeaturedList/{para}", method = RequestMethod.GET)
    public String newsList(@PathVariable String para) {
        return "redirect:/news/";
    }

    //资讯详情页
    @RequestMapping(value = "/News/View/{para}_all", method = RequestMethod.GET)
    public String newsDetailList(@PathVariable String para) {
        return "redirect:/news/" + para + ".htm/";
    }

    //新房右侧>购房百科
    @RequestMapping(value = "/need/baike.aspx", method = RequestMethod.GET)
    public String encyclopedia() {
        return "redirect:/static/encyclopedia.htm";
    }

    @RequestMapping(value = "/formulate/baike.aspx", method = RequestMethod.GET)
    public String encyclopediaB() {
        return "redirect:/static/encyclopedia.htm";
    }

    //新房右侧>专题
    @RequestMapping(value = "/event/2015/0929/pp1/", method = RequestMethod.GET)
    public String enentOne() {
        return "redirect:https://event.fangxiaoer.com/20150929.htm";
    }

    //新房右侧>专题
    @RequestMapping(value = "/event/2015/1231/", method = RequestMethod.GET)
    public String enentTwo() {
        return "redirect:https://event.fangxiaoer.com/20151231.htm";
    }

    //专题站适配
    @RequestMapping(value = "/event/{a}/{b}/{c}", method = RequestMethod.GET)
    public String event(@PathVariable String a, @PathVariable String b, @Nullable String c) {
        return "redirect:https://event.fangxiaoer.com/" + a + b + ".htm";
    }

    @RequestMapping(value = "/event/{a}/{c}", method = RequestMethod.GET)
    public String event1(@Nullable @PathVariable String a, @Nullable @PathVariable String c) {
        if (Character.isDigit(c.charAt(1))) {
            return "redirect:https://event.fangxiaoer.com/" + a + c + ".htm";
        } else {
            return "redirect:https://event.fangxiaoer.com/" + a + ".htm";
        }
    }

    @RequestMapping(value = "/event/{a}/", method = RequestMethod.GET)
    public String event2(@PathVariable String a) {
        return "redirect:https://event.fangxiaoer.com/" + a + ".htm";
    }

    @RequestMapping(value = "/event/2010/1208/default.html", method = RequestMethod.GET)
    public String event3() {
        return "https://event.fangxiaoer.com/20101108.htm";
    }

    //专题
    @RequestMapping(value = "/event/2015/0929/ms/", method = RequestMethod.GET)
    public String enentThree() {
        return "redirect:https://event.fangxiaoer.com/20150929_ms.htm";
    }

    //资讯详情页
    @RequestMapping(value = "/News/View/{para}", method = RequestMethod.GET)
    public String newsDetail(@PathVariable String para) {
        return "redirect:/news/" + para + ".htm/";
    }

    //小区找房详情页
    @RequestMapping(value = "/UHouse/rent/village/{para}.htm", method = RequestMethod.GET)
    public String village(@PathVariable String para) {
        return "redirect:/village/" + para + ".htm";
    }

    //新房下方品牌楼盘
    @RequestMapping(value = "/house/search.aspx", method = RequestMethod.GET)
    public String projectMapping(String keys) {
        return "redirect:/houses/search=" + java.net.URLEncoder.encode(keys);
    }

    //学校列表页
    @RequestMapping(value = {"/schoolhouse/{id:^[-?\\d?_?]+$}", "/schoolhouse/{id:^[-?\\d?_?]+$}/"}, method = RequestMethod.GET)
    public String schoolListMapping(@PathVariable String id) {
        String[] value = id.split("_");
        if (value[2].equals("-1")) {
            return "redirect:/schools/";
        } else {
            return "redirect:/schoolhouses/" + value[2];
        }
    }

    //精装房详情(sy只有普宅)
    @RequestMapping(value = "/house/JingZhuangHouseDetail.aspx", method = RequestMethod.GET)
    public String decoration(Integer ProjectID) {
        return "redirect:/house/" + ProjectID + "-1/decoration.htm";
    }

    @RequestMapping(value = {"/decoration/{projectId:[0-9]+}-{projectType:[0-9]+}.htm"}, method = RequestMethod.GET)
    public String decorationMapping(@PathVariable String projectId, @PathVariable String projectType) {
        return "redirect:/house/" + projectId + "-" + projectType + "/decoration.htm";
    }


    //新房评价更改
    @RequestMapping(value = "/house/AddComment.aspx", method = RequestMethod.GET)
    public String AddComment(@Nullable @RequestParam String CommentId, @Nullable @RequestParam String projectid) {
        if (StringUtils.isEmpty(CommentId)) {
            return "redirect:/comment/" + projectid;
        } else {
            return "redirect:/editComment/" + CommentId + ".htm";
        }
    }

    /**
     * 网站地图适配
     *
     * @return
     */
    @RequestMapping(value = "/Sitemap.html", method = RequestMethod.GET)
    public String AddComment() {
        return "redirect:/static/sysitemap.htm";
    }

    @RequestMapping(value = "/Index", method = RequestMethod.GET)
    public String fangIndex() {
        return "redirect:https://info.fangxiaoer.com/Index";
    }

    @RequestMapping(value = {"/About", "/About/{param}"}, method = RequestMethod.GET)
    public String fangAbouts(@Nullable @PathVariable("param") String param) {
        if (param == null) param = "";
        return "redirect:https://info.fangxiaoer.com/About/" + param;
    }

    @RequestMapping(value = {"/Help", "/Help/{param}"}, method = RequestMethod.GET)
    public String fangHelps(@Nullable @PathVariable("param") String param) {
        if (param == null) param = "";
        return "redirect:https://info.fangxiaoer.com/Help/" + param;
    }

    @RequestMapping(value = {"/Join", "/Join/{param}"}, method = RequestMethod.GET)
    public String fangJoin(@Nullable @PathVariable("param") String param) {
        if (param == null) param = "";
        return "redirect:https://info.fangxiaoer.com/Join/" + param;
    }

    //新房地图找房
    @RequestMapping(value = "/housemap1.htm")
    public String mHouseMap() {
        return "redirect:/static/houseMap.htm";
    }

    //二手房地图找房
    @RequestMapping(value = "/housemap2.htm")
    public String mSecondMap() {
        return "redirect:/salemap/";
    }


    //租房地图找房
    @RequestMapping(value = "/housemap3.htm")
    public String syRentMap() {
        return "redirect:/static/rentmap.htm";
    }

    //视频列表页
    @RequestMapping(value = {"/video.htm"})
    public String mVideosList() {
        return "redirect:/videos/3";
    }

    //房仔中心小二管家
    @RequestMapping(value = {"/static/xegj.htm"})
    public String mKeeperList() {
        return "redirect:/houseKeeper/";
    }

    //房仔中心小二管家
    @RequestMapping(value = {"/static/xelist.htm"})
    public String mKeeperMoreList() {
        return "redirect:/keeperMore/";
    }

    //经纪人店铺
    @RequestMapping(value = {"/jjrsf/{a}-{b}.htm"})
    public String mAgentShopList(@PathVariable String a, @PathVariable String b) {
        switch (b) {
            case "1":
                b = "second";
                break;
            case "2":
                b = "rents";
                break;
            case "3":
                b = "shops";
                break;
            default:
                b = "second";
                break;
        }
        return "redirect:/agent/" + b + "/" + a;
    }

    //积分商城
    @RequestMapping(value = {"/fzsc{a}.htm"})
    public String mPointsMall(@Nullable @PathVariable String a) {
        if (a == null) {
            return "redirect:/pointsMall";
        } else {
            return "redirect:/pointsMall/" + a;
        }
    }

    //积分商城详情页
    @RequestMapping(value = {"/fzsc/{a}.htm"})
    public String mPointsMallDetail(@Nullable @PathVariable String a) {
        return "redirect:/goods/" + a + ".htm";
    }

    //购房攻略列表页
    @RequestMapping(value = {"/fzgl.htm"})
    public String mStrategyList() {
        return "redirect:/strategy/147";
    }

    //免费发布
    @RequestMapping(value = {"/publish", "/pubSale.htm"})
    public String mPublishSecondList() {
        return "redirect:/new_secondPublish";
    }

    //免费发布
    @RequestMapping(value = {"/pubRent.htm"})
    public String mPublishRentList() {
        return "redirect:/rentwhole";
    }

    //免费发布
    @RequestMapping(value = {"/pubShop.htm"})
    public String mPublishShopsList() {
        return "redirect:/shopsell";
    }

    //帮你找房新房
    @RequestMapping(value = {"/normalnew.htm"}, method = RequestMethod.GET)
    public String mHelpFindHouseList() {
        return "redirect:/helpSearch?ids=1";
    }

    //帮你找房二手房
    @RequestMapping(value = {"/normalsale.htm"}, method = RequestMethod.GET)
    public String mHelpFindSecondList() {
        return "redirect:/helpSearch?ids=2";
    }

    //帮你找房租房
    @RequestMapping(value = {"/normalRent.htm"}, method = RequestMethod.GET)
    public String mHelpFindRentList() {
        return "redirect:/helpSearch?ids=3";
    }

    //帮你找房商业
    @RequestMapping(value = {"/normalShop.htm"}, method = RequestMethod.GET)
    public String mHelpFindShopList() {
        return "redirect:/helpSearch?ids=4";
    }

    //预约看房
    @RequestMapping(value = {"/kfztc.htm"}, method = RequestMethod.GET)
    public String mLookForHouseList() {
        return "redirect:/helpSearch?ids=5";
    }

    //过户代办
    @RequestMapping(value = {"/ghdb.htm"}, method = RequestMethod.GET)
    public String mGuoHuDaiBanList() {
        return "redirect:/helpSearch?ids=6";
    }


    //新房列表页（M）
    @RequestMapping(value = {"/fang1l", "/fang1l/", "/fang1l/{a}"}, method = RequestMethod.GET)
    public String mHouseList(@Nullable @PathVariable String a) {
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/houses/" + a;
        }
        return "redirect:/houses/";
    }


    //新房详情页(M)
    @RequestMapping(value = "/fang1/{a}.htm", method = RequestMethod.GET)
    public String mHouseDetail(@PathVariable String a) {
        return "redirect:/house/" + a + ".htm";
    }

    //新房地铁沿线列表(M)
    @RequestMapping(value = {"/fang1dt", "/fang1dt/{a}"}, method = RequestMethod.GET)
    public String mHouseSubwayList(@Nullable @PathVariable String a) {
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/subways/" + a;
        }
        return "redirect:/subways/";
    }

    //新房学区列表页(M)
    @RequestMapping(value = {"/fang1xq", "/fang1xq/{a}"}, method = RequestMethod.GET)
    public String mHouseSchoolList(@Nullable @PathVariable String a) {
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/schools/" + a;
        }
        return "redirect:/schools/";
    }

    //新房精装房列表(M)
    @RequestMapping(value = {"/decorates", "/decorates/{a}"}, method = RequestMethod.GET)
    public String mHouseDecorationsList(@Nullable @PathVariable String a) {
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/decorations/" + a;
        }
        return "redirect:/decorations/";
    }

    //新房精装房详情(M)
    @RequestMapping(value = "/decorate/{projectId}-{id}.htm", method = RequestMethod.GET)
    public String decorationDetail(@PathVariable Integer projectId, @PathVariable Integer id) {
        Integer value = houseService.viewProjectByType(projectId);
        return "redirect:/house/" + projectId + "-" + value + "/decoration.htm";
    }

    //新房学区详情(M)
    @RequestMapping(value = "/school/{id}.htm", method = RequestMethod.GET)
    public String schoolDetail(@PathVariable Integer id) {
        return "redirect:/schoolhouses/" + id;
    }

    //新房相册页（M）
    @RequestMapping(value = {"/nphotos/{projectId}-{photoType}-{projectType}.htm", "/nphotos/{projectId}-{photoType}-{projectType}-{photoId}.htm"}, method = RequestMethod.GET)
    public String projectPhoto(@PathVariable Integer projectId, @PathVariable String photoType, @PathVariable Integer projectType, @Nullable @PathVariable Integer photoId) {
        return "redirect:/house/" + projectId + "-" + projectType + "/album/" + photoType + ".htm";
    }

    //新房销售动态（M）
    @RequestMapping(value = {"/fang1/{projectId}-{projectType}/dy", "/fang1/{projectId}-{projectType}/dy-{infoType}"}, method = RequestMethod.GET)
    public String dynamicInfo(@PathVariable Integer projectId, @PathVariable Integer projectType, @PathVariable Integer infoType) {
        return "redirect:/house/" + projectId + "-" + projectType + "/news.htm";
    }

    //新房评论页面(M)
    @RequestMapping(value = "/fang1/{projectId}-{projectType}/appraise.htm", method = RequestMethod.GET)
    public String projectComment(@PathVariable Integer projectId, @PathVariable Integer projectType) {
        return "redirect:/house/" + projectId + "-" + projectType + "/appraise.htm";
    }

    //新房咨询页面(M)
    @RequestMapping(value = "/fang1/{projectId}-{projectType}/ask.htm", method = RequestMethod.GET)
    public String projectAsk(@PathVariable Integer projectId, @PathVariable Integer projectType) {
        return "redirect:/house/" + projectId + "-" + projectType + "/ask.htm";
    }

    //户型图相关页面(M)
    @RequestMapping(value = {"/fang1/{projectId}-{projectType}/layout/{a}"}, method = RequestMethod.GET)
    public String viewLayoutInfos(@PathVariable Integer projectId, @PathVariable Integer projectType, @PathVariable String a) {
        if (projectType == 3) {
            return "redirect:/house/" + projectId + "-3.htm";
        } else if (projectType == 2) {
            return "redirect:/house/" + projectId + "-2/layout.htm";
        } else {
            return "redirect:/house/" + projectId + "-" + projectType + "/layout/pid" + projectId + "-pt" + projectType;
        }
    }

    //品牌馆功能(M)
    @RequestMapping(value = "/brand", method = RequestMethod.GET)
    public String brand() {
        return "redirect:/brandCompany/";
    }

    //二手房列表页(M)
    @RequestMapping(value = {"/fang2", "/fang2/{a}"}, method = RequestMethod.GET)
    public String mSecondList(@Nullable @PathVariable String a) {
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/saleHouses/" + a;
        }
        return "redirect:/saleHouses/";
    }

    //二手房详情页(M)
    @RequestMapping(value = {"/fang2/{a}.htm"}, method = RequestMethod.GET)
    public String mSecondDetail(@PathVariable String a) {
        return "redirect:/salehouse/" + a + ".htm";
    }

    //租房列表页(M)
    @RequestMapping(value = {"/fang3/{a}", "/fang3"}, method = RequestMethod.GET)
    public String mRentList(@Nullable @PathVariable String a) {
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/rents/" + a;
        }
        return "redirect:/rents/";
    }

    //租房详情页(M)
    @RequestMapping(value = {"/fang3/{a}.htm"}, method = RequestMethod.GET)
    public String mRentDetail(@Nullable @PathVariable String a) {
        return "redirect:/rent/" + a + ".htm";
    }

    //商铺列表页(M)
    @RequestMapping(value = {"/fang4", "/fang4/{a}"}, method = RequestMethod.GET)
    public String mShopsList(@Nullable @PathVariable String a) {
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/shops/" + a;
        }
        return "redirect:/shops/";
    }

    //商铺详情页(M)
    @RequestMapping(value = {"/fang4/{a}.htm"}, method = RequestMethod.GET)
    public String mShopDetail(@Nullable @PathVariable String a) {
        return "redirect:/shop/" + a + ".htm";
    }

    //写字楼列表(M)
    @RequestMapping(value = {"/fang5", "/fang5/{a}"}, method = RequestMethod.GET)
    public String oldOffice(@Nullable @PathVariable String a) {
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/scriptoriums/" + a;
        }
        return "redirect:/scriptoriums/";
    }

    //写字楼详情(M)
    @RequestMapping(value = "/fang5/{id}.htm", method = RequestMethod.GET)
    public String oldOfficeDetail(@PathVariable Integer id) {
        return "redirect:/scriptoriums/" + id + ".htm";
    }

    //视频首页(M)
    @RequestMapping(value = "/videoIndex.htm", method = RequestMethod.GET)
    public String videoInfo() {
        return "redirect:/videos/";
    }

    //视频列表(M)
    @RequestMapping(value = "/videoByType{id}.htm", method = RequestMethod.GET)
    public String videoList(@PathVariable Integer id) {
        return "redirect:/videos/" + id;
    }

    //小二说房功能(M)
    @RequestMapping(value = "/audio.htm", method = RequestMethod.GET)
    public String audioList(){
        return "redirect:/audios/";
    }

    //资讯列表页(M)
    @RequestMapping(value = "/news{id}.htm", method = RequestMethod.GET)
    public String newsList(@Nullable @PathVariable Integer id){
        id = id == null ? 149 : id;
        return "redirect:/news/" + id;
    }

    //二手房租房小区找房列表页(M)
    @RequestMapping(value = {"/sub"}, method = RequestMethod.GET)
    public String mSubList() {
        return "redirect:/saleVillages/";
    }

    //二手房租房小区找房详情页(M)
    @RequestMapping(value = {"/sub/{a}.htm"}, method = RequestMethod.GET)
    public String mSubDetail(@Nullable @PathVariable String a) {
        return "redirect:/saleVillages/"+a+"/index.htm";
    }

    //成交页面(M)
    @RequestMapping(value = "/txnSecond", method = RequestMethod.GET)
    public String dealInfos(){
        return "redirect:/dealSales/";
    }

    //720看房适配（M）
    @RequestMapping(value = {"/fang1/{projectId}-{projectType}/pic720.htm", "/fang1/{projectId}-{projectType}/pic720/{panId}.htm"}, method = RequestMethod.GET)
    public String panoramic(@PathVariable Integer projectId, @PathVariable Integer projectType, @Nullable @PathVariable Integer panId){
        if (!StringUtils.isEmpty(panId)) {
            return "redirect:/house/" + projectId + "-" + projectType + "/pic720/" + panId + ".htm";
        } else {
            return "redirect:/house/" + projectId + "-" + projectType + "/pic720.htm";
        }
    }

    //新房列表适配（M）
    @RequestMapping(value ={"/project1l","/project1l/", "/project1l/{a}"}, method = RequestMethod.GET)
    public String newProjectList(@Nullable @PathVariable String a){
        if (!StringUtils.isEmpty(a)) {
            return "redirect:/houses/" + a;
        }
        return "redirect:/houses/";
    }

    @RequestMapping(value = {"/tourist/{id}/dy/-n{page:[0-9]+}", "/tourist/{id}/dy-{dyType}/-n{page:[0-9]+}"}, method = RequestMethod.GET)
    public String touristDyInfos(@PathVariable Integer id, @Nullable @PathVariable Integer dyType, @PathVariable Integer page){
        if (StringUtils.isEmpty(dyType)) {
            return "redirect:/tourist/"+id+"/dy/"+page+".htm";
        }else{
            return "redirect:/tourist/"+id+"/dy-"+dyType+"/"+page+".htm";
        }
    }

    @RequestMapping(value = "/tourist/{id}/ask/-n{page:[0-9]+}", method = RequestMethod.GET)
    public String touristAsks(@PathVariable Integer id, @PathVariable Integer page){
        return "redirect:/tourist/" + id + "/ask/" + page + ".htm";
    }

    @RequestMapping(value = "/tourist/{id}/comment/-n{page:[0-9]+}", method = RequestMethod.GET)
    public String touristComment(@PathVariable Integer id, @PathVariable Integer page){
        return "redirect:/tourist/" + id + "/comment/" + page + ".htm";
    }

    @RequestMapping(value = {"/tourist/{projectId:[0-9]+}/layout.htm", "/tourist/{projectId:[0-9]+}/layout/pid{projectId:[0-9]+}"}, method = RequestMethod.GET)
    public String touristLayout(@PathVariable Integer projectId){
        return "redirect:/tourist/layout-"+ projectId +"/pid"+ projectId;
    }
}
