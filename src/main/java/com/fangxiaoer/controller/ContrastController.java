package com.fangxiaoer.controller;

import com.fangxiaoer.common.*;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2018/2/24.
 */
@Controller
public class ContrastController {

    @RequestMapping(value = {"/gotoContrastHouse"})
    public String gotoContrastHouse(Model model, @RequestParam(required = false) String houseId){
        return "house/contrastHouse";
    }

    @ResponseBody
    @RequestMapping(value = "/addContrastHouse", method = RequestMethod.POST)
    public ArrayList addContrastHouse(HttpServletRequest request, HttpServletResponse response,
                                      @RequestParam("houseId") String houseId,
                                      @RequestParam("houseName") String houseName,
                                      @RequestParam("houseType") String houseType) {
        if (houseId == null && houseType == null && houseName == null) {
            return Lists.newArrayList();
        }
        LinkedTreeMap<String, Object> reqMap = new LinkedTreeMap<>();
        reqMap.put("houseId", houseId);
        reqMap.put("houseName", houseName);
        reqMap.put("houseType", houseType);

        Gson gson = new Gson();
        ArrayList<LinkedTreeMap<String, Object>> list = new ArrayList<>();

        String value = CookieManage.iterationCookie(request, "contrastHouse", true);
        if (StringUtils.isEmpty(value)) {
            list.add(reqMap);
            String json = gson.toJson(list);
            CookieManage.addCookie(response, "contrastHouse", json, "/", "fangxiaoer.com", true);
        } else {
            list = gson.fromJson(value, ArrayList.class);
            boolean present = list.stream().anyMatch(item -> item.get("houseId").equals(houseId));
            if (!present) {
                list.add(reqMap);
                String json = gson.toJson(list);
                CookieManage.addCookie(response, "contrastHouse", json, "/", "fangxiaoer.com", true);
            }
        }
        return list;
    }

    @ResponseBody
    @RequestMapping(value = "/getContrastHouse",method = RequestMethod.POST)
    public ArrayList getContrastHouse(HttpServletRequest request, Model model){
        String str = CookieManage.iterationCookie(request, "contrastHouse", true);
        Gson gson = new Gson();
        ArrayList list = StringUtils.isEmpty(str) ? Lists.newArrayList() : gson.fromJson(str, ArrayList.class);
        return list;
    }

    @ResponseBody
    @RequestMapping(value = "/deleteContrastHouse",method = RequestMethod.POST)
    public ArrayList deleteContrastHouse(@RequestParam("houseId") String houseId,HttpServletRequest request,HttpServletResponse response){
        String str = CookieManage.iterationCookie(request, "contrastHouse", true);
        Gson gson = new Gson();
        ArrayList<LinkedTreeMap<String, Object>> list = StringUtils.isEmpty(str) ? Lists.newArrayList() : gson.fromJson(str, ArrayList.class);
        list.removeIf(item -> item.get("houseId").equals(houseId));
        CookieManage.addCookie(response, "contrastHouse", gson.toJson(list), "/", "fangxiaoer.com", true);
        return list;
    }

    @ResponseBody
    @RequestMapping(value = "/deleteAllContrastHouse",method = RequestMethod.POST)
    public ArrayList deleteAllContrastHouse(HttpServletRequest request, HttpServletResponse response){
        String str = CookieManage.iterationCookie(request, "contrastHouse", true);
        Gson gson = new Gson();
        ArrayList<LinkedTreeMap<String, Object>> list = StringUtils.isEmpty(str) ? Lists.newArrayList() : gson.fromJson(str, ArrayList.class);
        list.clear();
        CookieManage.addCookie(response, "contrastHouse", gson.toJson(list), "/", "fangxiaoer.com", true);
        return list;
    }

    /**
     * 搜索栏自动提示
     */
    @ResponseBody
    @RequestMapping(value = "/searchNewHouse",method = RequestMethod.GET)
    public List<LinkedTreeMap<String, Object>> search(@RequestParam String key, @RequestParam String serachInfo){
        try {
            serachInfo  =  URLDecoder.decode(serachInfo,"UTF-8");
        }catch (Exception e){
            e.printStackTrace();
        }       HashMap<String, Object> result = HttpUtil.connectApi(Constants.COMMON_SEARCH_NEW, new Params("search", serachInfo).add("key",key).add("tab", "1").get());
        List<LinkedTreeMap<String, Object>> info = (List<LinkedTreeMap<String, Object>>) result.get("content");
        return info;
    }
}
