package com.fangxiaoer.controller;

import com.fangxiaoer.service.FreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

@Controller
public class FreeServiceController {

    @Autowired
    private FreeService freeServiceService;

    /**
     * 免费服务首页
     * @param model
     * @return
     */
    @RequestMapping(value = "/freeServiceIndex",method = RequestMethod.GET)
    public String freeServiceIndex(Model model){
        freeServiceService.getFreeServiceIndex(model);
        return "freeService/freeService";
    }

    /**
     * 服务公司详情页
     * @param model
     * @param companyId
     * @param serviceId
     * @return
     * <AUTHOR> 2018-7-27
     */
    @RequestMapping(value = {"/freeService/{companyId:[0-9]+}-{serviceId:[0-9]+}.htm","/freeService/{companyId:[0-9]+}.htm"},method = RequestMethod.GET)
    public String freeServiceDetial(Model model, @PathVariable("companyId") String companyId, @PathVariable(name ="serviceId",required = false) String serviceId){
        if(serviceId == null) serviceId = "";
        freeServiceService.getFreeServiceDetial(model,companyId,serviceId);
        model.addAttribute("companyId",companyId);
        model.addAttribute("serviceId",serviceId);
        return "freeService/service_details";
    }

    /**
     * 服务页面提交订单
     * @param params
     * @return
     * <AUTHOR> 2018-7-27
     */
    @ResponseBody
    @RequestMapping(value = "/serviceGuide",method = RequestMethod.POST)
    public HashMap<String, Object> serviceGuide(@RequestBody HashMap<String,Object> params){
        return freeServiceService.saveServiceGuide(params);
    }

    /**
     * 服务页面ajax级联获取公司服务类别
     * @param params
     * @return
     * <AUTHOR> 2018-7-30
     */
    @ResponseBody
    @RequestMapping(value = "/serviceTypes",method = RequestMethod.POST)
    public HashMap<String, Object> getServiceTypes(@RequestBody HashMap<String,Object> params){
        return freeServiceService.getServiceTypes(params);
    }
}
