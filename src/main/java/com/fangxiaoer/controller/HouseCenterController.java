package com.fangxiaoer.controller;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.service.HouseCenterService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;

import static com.fangxiaoer.common.Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT;

/**
 * Created by Administrator on 2017/7/26.
 */
@Controller
public class HouseCenterController {

    @Autowired
    HouseCenterService houseCenterService;

    /**
     * 购房攻略列表
     *
     * @param model
     * @return
     */
    public static final String GET_STRATRGY_LIST = "/strategy";
    @RequestMapping(value = {GET_STRATRGY_LIST + "/{params}", GET_STRATRGY_LIST}, method = RequestMethod.GET)
    public String getStrategy(@Nullable @PathVariable("params") String params, Model model, HttpServletRequest request) {
        if (params == null) params = "";
        String baseUrl = GET_STRATRGY_LIST + "/";
        houseCenterService.getStrategy(baseUrl, params, model, request);
        return "houseCenter/strategy";
    }

    /**
     * 房仔中心>积分商城>全部
     */
    @RequestMapping(value = "/pointsMall", method = RequestMethod.GET)
    public String getPointsMall(Model model) {
        houseCenterService.getPointsMall(model);
        return "houseCenter/pointsMall";
    }

    /**
     * 房仔中心>积分商城>条件查询
     */
    @RequestMapping(value = "/pointsMall/{inputParams}", method = RequestMethod.GET)
    public String getPointsMallBySort(@PathVariable("inputParams") String inputParams, Model model) {
        houseCenterService.getPointsMallBySort(inputParams, model);
        return "houseCenter/pointsMall";
    }

    /**
     * 房仔中心>积分商城>商品详情
     */
    @RequestMapping(value = "/goods/{inputParams}.htm", method = RequestMethod.GET)
    public String getGoods(@PathVariable("inputParams") Integer id, Model model) {
        houseCenterService.getGoods(id, model);
        return "houseCenter/detail/goods";
    }

    /**
     * 房仔中心>积分商城>商品详情>下单
     */
    @RequestMapping(value = "/takeOrder", method = RequestMethod.POST)
    public String getOrder(Integer goodsId, Integer count, String sessionId, Model model) {
        HashMap order = new HashMap();
        order.put("sessionId", sessionId);
        order.put("goodsId", goodsId);
        order.put("count", count);
        HashMap<String, Object> addOrder = HttpUtil.connectApi(Constants.ADD_GOODS_ORDER, order);
        int status = Double.valueOf((double) addOrder.get("status")).intValue();
        if (status == 1) {
            return "redirect:https://my.fangxiaoer.com/order";
        } else {
            model.addAttribute("msg", addOrder.get("msg"));
            return getGoods(goodsId, model);
        }
    }

    /**
     * 房仔中心>房仔活动页初始化
     *
     * @return
     */
    @RequestMapping(value = "/activities", method = RequestMethod.GET)
    public String getActivities(Model model) {
        houseCenterService.getActivities("", model);
        return "houseCenter/activities";
    }

    /**
     * 房仔活动详情页
     */
    @RequestMapping(value = "/activity/{id}.htm", method = RequestMethod.GET)
    public String getActivity(@PathVariable String id, Model model) {
        String redirectUrl = houseCenterService.getActivity(id, model);
        if (StringUtils.isEmpty(redirectUrl)) {
            return "houseCenter/detail/activity";
        } else {
            if (redirectUrl.startsWith("http")) {
                return "redirect:" + redirectUrl;
            } else {
                return "redirect:https://" + redirectUrl;
            }
        }
    }

    /**
     * 房仔中心>房仔活动页查询
     */
    @RequestMapping(value = "/activities/{inputParams}", method = RequestMethod.GET)
    public String getActivities(@PathVariable("inputParams") String inputParams, Model model) {
        houseCenterService.getActivities(inputParams, model);
        return "houseCenter/activities";
    }

    /**
     * 房仔中心>楼盘搜索
     */
    @ResponseBody
    @RequestMapping(value = "/search", method = RequestMethod.GET)
    public List<LinkedTreeMap<String, Object>> search(Model model, @RequestParam("q") String q) throws UnsupportedEncodingException {

        //q = new String(q.getBytes("iso-8859-1"),"UTF-8");
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("search", q);
        params.put("key", "1");
        //model.addAttribute("title",q);
        HashMap<String, Object> searchs = HttpUtil.connectApi(Constants.BASE_SEARCH, params);
        List<LinkedTreeMap<String, Object>> search = (List<LinkedTreeMap<String, Object>>) searchs.get("content");
        if (search.size() == 0) {
            LinkedTreeMap<String, Object> param = new LinkedTreeMap<String, Object>();
            param.put("tableId", 0);
            search.add(0, param);
            return search;
        }
        return search;
    }


    /**
     * 房仔中心>小二管家>查看更多
     */
    @RequestMapping(value = "/keeperMore/", method = RequestMethod.GET)
    public String getHouseKeeperMore(Model model) {
        houseCenterService.xiaoerInfos(model);
        return "houseCenter/detail/keeperMore";
    }

    /**
     *
     * 跳转vip页 存储相关信息
     * @param request
     * @param model
     * @return
     */
    @RequestMapping(value = "/skipToVip",method = RequestMethod.GET)
    public String skipToVip(HttpServletRequest request, Model model, HttpServletResponse response){
        houseCenterService.getVipInfo(request,model,response);

        return "houseCenter/vipcopy";
    }

    /**
     * 支付vip
     * @param session
     * @param vipType
     * @param payType
     * @return
     */

    @ResponseBody
    @RequestMapping(value = "/payVip",method = RequestMethod.POST)
    public HashMap<String ,Object> payVip(Integer vipType,Integer payType, HttpSession session, String sessionId){

        return houseCenterService.payVip(vipType,payType, session, sessionId);
    }

    /**
     * 获取订单状态
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/status", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public HashMap<String, Object> getStickOrderStatus(@RequestParam String orderId){
        return houseCenterService.getStickOrderStatus(orderId);
    }

    /**
     * 小二管家首页
     * @param model
     * @param session
     * @return
     * <AUTHOR> 2018-7-18
     */
    @RequestMapping(value = "/houseKeeper", method = RequestMethod.GET)
    public String getHouseKeepers(Model model, HttpSession session) {
       return "redirect:/houseKeeper1.htm";
    }
    /**
     * 小二管家首页成交故事
     * @return
     * <AUTHOR> 2018-7-19
     */
    @ResponseBody
    @RequestMapping(value = "/getDaelstories", method = RequestMethod.POST)
    public HashMap getDaelstories(@RequestBody HashMap<String, String> params) {
        //newsType 1-成交故事，2-看房团，3-节日福利
        params.put("newsType","1");
        HashMap result =   houseCenterService.getDaelstories(params);
        return result;
    }
    /**
     * 小二管家节日福利新闻列表
     * @return
     * <AUTHOR> 2018-7-20
     */
    @RequestMapping(value ={"/festivalNews/{params}","/festivalNews"}, method = RequestMethod.GET)
    public String getFestivalNews(@Nullable @PathVariable String params, Model model) {
        if(params == null) params = "";
        String baseUrl = "/festivalNews/";
        houseCenterService.getFestivalNews(model,params,baseUrl);
        return "houseKeeper/activity_list";
    }
    /**
     * 小二管家节日福利新闻详情
     * @return
     * <AUTHOR> 2018-7-20
     */
    @RequestMapping(value ="/afestivalNews/{id}.htm", method = RequestMethod.GET)
    public String getFestivalNewsDetial(@PathVariable String id, Model model) {
        houseCenterService.getFestivalNewsDetial(model,id);
        return "houseKeeper/activity_details";
    }
    /**
     *看房团列表页
     * @return
     * <AUTHOR> 2018-7-19
     */
    @RequestMapping(value ={ "/viewLookHouse","/viewLookHouse/{params}"}, method = RequestMethod.GET)
    public String viewLookHouse(Model model, @Nullable @PathVariable("params") String params){
        params=StringUtils.isEmpty(params)?"-n1":params;
        houseCenterService.getLookHouseList(model,"/viewLookHouse/",params);
        return "houseKeeper/review";
    }

    /**
     * 看房团详情页
     * @param model
     * @param id
     * @return
     * <AUTHOR> 2018-7-19
     */
    @RequestMapping(value = "/lookHouseDetail/{id}" , method = RequestMethod.GET)
    public String lookHouseDetail(Model model,@PathVariable("id") String id){
        houseCenterService.getLookHouseDetail(model,id);
        return "houseKeeper/photo";
    }

    /**
     *<AUTHOR> 2018-7-21
     * @param model
     * @return
     */
    @RequestMapping(value = {"/keeperMember","/keeperMember/{params}"}, method = RequestMethod.GET)
    public String getHouseKeeperMember(Model model, @Nullable @PathVariable("params") String params) {
        params=StringUtils.isEmpty(params)?"-n1":params;
        houseCenterService.keeperMember(model,"/keeperMember/",params);
        return "houseKeeper/housekeeper_details";
    }
    /**
     *品牌馆首页
     * @param model
     * @return
     * <AUTHOR> 2018-10-25
     */
    @RequestMapping(value = "/brandCompany", method = RequestMethod.GET)
    public String getbrandCompany(Model model) {
        houseCenterService.getbrandCompany(model);
        return "brand/brandIndex";
    }
    /**
     *品牌馆详情
     * @param model
     * @return
     * <AUTHOR> 2018-10-26
     */
    @RequestMapping(value = "/brandCompany/{brandId}.htm", method = RequestMethod.GET)
    public String getCompanyDetial(Model model,@PathVariable String brandId) {
        houseCenterService.getCompanyDetial(model,brandId);
        return "brand/brandDetails_QQMap";
    }
    @RequestMapping(value = "/brandCompany2/{brandId}.htm", method = RequestMethod.GET)
    public String getCompanyDetial2(Model model,@PathVariable String brandId) {
        houseCenterService.getCompanyDetial(model,brandId);
        return "brand/brandDetails";
    }
    /**
     *品牌馆资讯
     * @return
     * <AUTHOR> 2018-10-26
     */
    @ResponseBody
    @RequestMapping(value = "/brandCompanyNews", method = RequestMethod.POST)
    public HashMap getCompanyNews(@RequestParam String brandId,@RequestParam String page,@RequestParam String pageSize) {
        return houseCenterService.getCompanyNews(brandId,page,pageSize);
    }

    /**
     * 品牌地图
     * @param brandId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/viewBrandMap", method = RequestMethod.POST)
    public HashMap<String, Object> viewBrandMap(Integer brandId){
        return houseCenterService.viewBrandMap(brandId);
    }

    /**
     * 小二管家宣传页
     * @param model
     * @param session
     * @return
     * <AUTHOR> 2019-4-15
     */
    @RequestMapping(value = {"/houseKeeper{type:[0-9]+}.htm","/houseKeeper{type:[0-9]+}/{page}.htm"}, method = RequestMethod.GET)
    public String getHouseKeepersInfoByType(Model model, HttpSession session,@PathVariable String type,@PathVariable @Nullable String page) {
        return houseCenterService.getHouseKeepersInfoByType(model,session,type,page);
    }
    /**
     * 小二管家看房团
     * @param page
     * @param pageSize
     * @return
     * <AUTHOR> 2019-4-16
     */
    @ResponseBody
    @RequestMapping(value = "/houseKeeperTour", method = RequestMethod.POST)
    public HashMap<String, Object> getHouseKeepersTour (String page ,String pageSize) {
        return houseCenterService.getHouseKeepersTour(page,pageSize);
    }
    /**
     * 小二管家购房百问
     * @param strDate
     * @return
     * <AUTHOR> 2019-4-16
     */
    @ResponseBody
    @RequestMapping(value = "/houseKeeperAsk", method = RequestMethod.POST)
    public HashMap<String, Object> getHouseKeepersAsk (String strDate) {
        return houseCenterService.getHouseKeepersAsk(strDate);
    }
}
