package com.fangxiaoer.controller;

import com.fangxiaoer.model.HousingPriceEntity;
import com.fangxiaoer.service.HousingPriceService;
import com.fangxiaoer.service.PlotService;
import com.fangxiaoer.service.RentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Controller
public class HousingPriceController {

    @Autowired
    private HousingPriceService spService;
    @Autowired
    private RentService rentService;

    public static final String HOUSE_PRICE_URL = "/housingprice";
    @RequestMapping(value = {HOUSE_PRICE_URL + "/{params}",HOUSE_PRICE_URL},method = RequestMethod.GET)
    public String housingprice(@Nullable @PathVariable("params") String params, Model model){
        if (params == null) params = "";
        String baseUrl = HOUSE_PRICE_URL + "/";
        spService.getHousingPriceList(baseUrl,params, model);
        //  M站查房价首页链接适配 （该页面无筛选条件）
        model.addAttribute("mobileUrl","https://m.fangxiaoer.com/housingprice/");
        return "housingprice/housingprice";
    }


    @ResponseBody
    @RequestMapping(value = "/bigdata",method = RequestMethod.POST)
    public HashMap<String,Object> bigData(String innerRegion, String region, String city, String community){
        Map<String, String> value = new HashMap<>();
        value.put("innerRegion",innerRegion);
        value.put("region",region);
        value.put("city",city);
        value.put("community",community);
        //计算6周前的日期
        Date lastDate = PlotService.getOffsetMonthDate(new Date(), 6);
        //计算开始时间是当年的第几周
        Calendar c = Calendar.getInstance();
        c.setTime(lastDate);
        int betweenWeek = c.get(Calendar.WEEK_OF_YEAR) > 52 ? 52 : c.get(Calendar.WEEK_OF_YEAR);
        String lastDateUse = PlotService.convertDateToString(lastDate, "yyyy") + "-" + betweenWeek;
        value.put("startWeek", lastDateUse);
        return PlotService.citytrending(value);
    }


    @RequestMapping(value = "/housingpriceDetail",method = RequestMethod.GET)
    public String freeEvaluationDetail(HousingPriceEntity housingPriceEntity,Model model,String subId,
                                       String area,String roomId,String forwardType,String currentFloor,String totalFloor){
        //subId为空返回查房价列表页
        if(StringUtils.isEmpty(subId)){
            return "redirect:/housingprice";
        }
        if(StringUtils.isEmpty(area)){
            return "redirect:/housingprice";
        }
        if(StringUtils.isEmpty(roomId)){
            return "redirect:/housingprice";
        }
        if(StringUtils.isEmpty(forwardType)){
            return "redirect:/housingprice";
        }
        if(StringUtils.isEmpty(currentFloor)){
            return "redirect:/housingprice";
        }
        if(StringUtils.isEmpty(totalFloor)){
            return "redirect:/housingprice";
        }
        rentService.getVillage(subId,model);
        if(!StringUtils.isEmpty(subId)){
            housingPriceEntity.setSubId(Integer.parseInt(subId));
        }
        if(!StringUtils.isEmpty(area)){
            housingPriceEntity.setArea(area);
        }
        if(!StringUtils.isEmpty(roomId)){
            housingPriceEntity.setRoomId(Integer.parseInt(roomId));
        }
        if(!StringUtils.isEmpty(forwardType)){
            housingPriceEntity.setForwardType(Integer.parseInt(forwardType));
        }
        if(!StringUtils.isEmpty(currentFloor)){
            housingPriceEntity.setCurrentFloor(Integer.parseInt(currentFloor));
        }
        if(!StringUtils.isEmpty(totalFloor)){
            housingPriceEntity.setTotalFloor(Integer.parseInt(totalFloor));
        }
       //跳转m站房价结果页
        /*String p = "?subId="+subId +"&area="+area +"&roomId="+roomId
                +"&forwardType="+forwardType+"&currentFloor="+currentFloor+"&totalFloor="+totalFloor;
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/housingprice_eva_result"+ p);*/
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/housingprice");
        spService.housingPriceDetail(model,housingPriceEntity);
        return "housingprice/detail/housingpriceDetail";
    }

    @ResponseBody
    @RequestMapping(value = "/calForLoan",method = RequestMethod.POST)
    public HashMap<String, Object> calForLoan(Double priceA, Double priceB, Integer month1,Integer month2, Integer loanType, Double discount, Integer countMethod, Double basePoint) {
        return spService.calForLoan(priceA, priceB, month1, month2, loanType, discount, countMethod, basePoint);
    }

    @ResponseBody
    @RequestMapping(value = "/viewLoanFilter", method = RequestMethod.POST)
    public HashMap<String, Object> viewLoanFilter(Integer month, Double discount) {
        return spService.viewLoanFilter(month, discount);
    }

    @ResponseBody
    @RequestMapping(value = "/LPRHistoryFilter", method = RequestMethod.POST)
    public HashMap<String, Object> LPRHistoryFilter() {
        return spService.LPRHistoryFilter();
    }


}
