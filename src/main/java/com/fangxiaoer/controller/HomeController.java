package com.fangxiaoer.controller;

import com.fangxiaoer.common.Utils;
import com.fangxiaoer.service.HomeService;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mobile.device.Device;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 首页
 */
@Controller
public class HomeController {

    @Autowired
    private HomeService homeService;
    //主页
    @RequestMapping(value = "/", method = RequestMethod.GET)
    public String index(Model model, Device device, String systemDate) {
        if(device.isMobile()) {
            return "redirect:https://m.fangxiaoer.com";
        }
        if(!StringUtils.isEmpty(systemDate)){
            homeService.getAdvertisement(model,systemDate);
        }else{
            homeService.getAdvertisement(model);
        }
        //排行榜&品牌馆功能
        homeService.viewRankAndBrand(model);
        //添加主页筛选项
        homeService.generateSelection(model);
        //房产快讯
        homeService.houseFastNews(model);
        //主页友链
        homeService.getLinks(model);
        // homeService.hotList(model);
        // 房产视频 和 项目销售动态
        homeService.getHomePageVideosAndProjectDynamics(model);
        int x = (int) (3 * Math.random());
        model.addAttribute("rankNum", x);
        homeService.grayTheme(model);
        return "index/newIndex";
    }

    @RequestMapping(value = "/homeBackup", method = RequestMethod.GET)
    public String homeBackup(Model model, Device device, String systemDate) {
        if(device.isMobile()) {
            return "redirect:https://m.fangxiaoer.com";
        }
        if(!StringUtils.isEmpty(systemDate)){
            homeService.getAdvertisement(model,systemDate);
        }else{
            homeService.getAdvertisement(model);
        }
        //排行榜&品牌馆功能
        homeService.viewRankAndBrand(model);
        //添加主页筛选项
        homeService.generateSelection(model);
        //房产快讯
        homeService.houseFastNews(model);
        //主页友链
        homeService.getLinks(model);
        // homeService.hotList(model);
        // 房产视频 和 项目销售动态
        homeService.getHomePageVideosAndProjectDynamics(model);
        int x = (int) (3 * Math.random());
        model.addAttribute("rankNum", x);
        homeService.grayTheme(model);
        return "index/homeBackup";
    }

    /**
     * 搜索记录传送服务
     * <AUTHOR>
     * @Date 2020/10/29 15:04
     **/
    @ResponseBody
    @RequestMapping("/searchClickAnalyze")
    public void searchClickAnalyze(String sessionId, String searchInfo ,String searchSource, String searchType, String keyId, String keyTab){
        homeService.searchClickAnalyze(sessionId,searchInfo,searchSource,searchType,keyId,keyTab);
    }

    /**
     * 模糊查询
     *
     * @return
     */
    @RequestMapping(value = "/fuzzySearchSubd" ,method = RequestMethod.GET)
    @ResponseBody
    public List<LinkedTreeMap<String, Object>> queryLike(@RequestParam String key, @RequestParam String q, @RequestParam(required = false  ) Integer page, @RequestParam(required = false  ) Integer pageSize) {
        try {
            q = URLDecoder.decode(q,"UTF-8");//解码
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        if(!StringUtils.isEmpty(key)){
            return homeService.queryLikeHouse(key, q, page, pageSize, 1);
        }else{
            return new ArrayList<>();
        }

    }

    @ResponseBody
    @RequestMapping(value = "/commonSearch", method = RequestMethod.GET)
    public List<LinkedTreeMap<String, Object>> queryLikes(@RequestParam String key, @RequestParam String q, @RequestParam(required = false  ) Integer page, @RequestParam(required = false  ) Integer pageSize) {
        try {
            q = URLDecoder.decode(q,"UTF-8");//解码
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        if(!StringUtils.isEmpty(key) && key.equals("1")){
            return homeService.queryLikeHouse(key, q, page, pageSize, 1);
        }else{
            return homeService.queryLikeHouse(key, q, page, pageSize, null);
        }

    }

    /**
     * 跳转到相应网站个人中心
     * @param request
     * @param response
     */
    @ResponseBody
    @RequestMapping(value = "/redirectToMyOrAgent" ,method = RequestMethod.GET)
    public void redirectToMyOrAgent(HttpServletRequest request, HttpServletResponse response) {
        HttpSession session = request.getSession();
        if(session.getAttribute("memberType") != null) {
            String memberType = session.getAttribute("memberType").toString();
            homeService.toMyOrAgent(memberType,response);
        }
    }

    /**
     * 首页下方，一键订阅
     */
    @ResponseBody
    @RequestMapping(value = "/subscription" ,method = RequestMethod.POST)
    public void subscription(HttpServletRequest request, HttpServletResponse response) {
    }

    @ResponseBody
    @RequestMapping(value = "/download", method = {RequestMethod.GET, RequestMethod.POST})
    public void downloadFile(HttpServletResponse response){
        Utils.downLoadFromUrl("http://dcyjy.fangxiaoer.com/appdownload/shenyangditie.jpg", response);
    }
    /**
     * 地图查询
     *
     * @return
     */
    @RequestMapping(value = "/searchNewProject" ,method = RequestMethod.POST)
    @ResponseBody
    public List<LinkedTreeMap<String, Object>> queryLikeMap(@RequestParam String key, @RequestParam String q, @RequestParam(required = false  ) Integer page, @RequestParam(required = false  ) Integer pageSize) {
        return homeService.queryLikeMap(key, q, page, pageSize);
    }
    /**
     * 地图查询
     *
     * @return
     */
    @RequestMapping(value = "/searchNewProjectMap" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> queryLikeMap(@RequestParam HashMap hashMap) {
        return homeService.queryHouseMap(hashMap);
    }
    /**
     * 地铁找房查询
     *
     * @return
     */
    @RequestMapping(value = "/searchStationMap" ,method = RequestMethod.POST)
    @ResponseBody
    public HashMap<String, Object> searchStationMap(@RequestParam HashMap hashMap) {
        return homeService.searchStationMap(hashMap);
    }
}
