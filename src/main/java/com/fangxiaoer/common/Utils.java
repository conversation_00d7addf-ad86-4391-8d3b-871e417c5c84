package com.fangxiaoer.common;


import com.fangxiaoer.model.LoginResultFilter;
import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.DateTime;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Utils {

    public static String formatDate(Date date, String pattern) {
        return new DateTime(date).toString(pattern);
    }

    public static String uuidGenerator() {
        return UUID.randomUUID().toString();
    }

    /**
     * 获取cookie
     * @param request
     * @param key
     * @return
     */
    public static Cookie iterationCookie(HttpServletRequest request, String key) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null) return null;
        Cookie cookieValue = null;
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals(key)) {
                cookieValue = cookie;
                break;
            }
        }
        return cookieValue;
    }

    /**
     * 添加cookie
     *
     * @param response 设置
     * @param key      用户对应cookie名称
     * @param jsonStr  发布页json数据
     */
    public static void addCookie(HttpServletResponse response, String key, String jsonStr, String cookiePath) {
        addCookie(response, key, jsonStr, cookiePath, 60*60*24*7);
    }

    public static void addCookie(HttpServletResponse response, String key, String jsonStr, String cookiePath, Integer times) {
        try {
            Cookie cookie = new Cookie(key, URLEncoder.encode(jsonStr, "UTF-8"));
            cookie.setPath(cookiePath);
            cookie.setMaxAge(times);
            response.addCookie(cookie);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除Cookie
     *
     * @param key 用户对应cookie名称
     */
    public static void deleteCookie(HttpServletResponse response, String key, String cookiePath) {
        //Cookie cookie = iterationCookie(request,key);
        Cookie cookie = new Cookie(key, null);
        if (cookie != null) {
            cookie.setPath(cookiePath);
            cookie.setMaxAge(0);
            response.addCookie(cookie);
        }
    }

    /**
     * 删除Cookie
     *
     * @param key 用户对应cookie名称
     */
    public static void deleteCookie(HttpServletResponse response, String key, String cookiePath, String domain) {
        //Cookie cookie = iterationCookie(request,key);
        Cookie cookie = new Cookie(key, null);
        if (cookie != null) {
            cookie.setPath(cookiePath);
            cookie.setMaxAge(0);
            if (!StringUtils.isEmpty(domain)) {
                cookie.setDomain(domain);
            }
            response.addCookie(cookie);
        }
    }

    public static Timestamp addTime(Timestamp time, String type, Integer d) {
        Calendar c = Calendar.getInstance();
        c.setTime(time);
        if ("d".equals(type))
            c.add(Calendar.DATE, d); //+n天
        else if ("h".equals(type))
            c.add(Calendar.HOUR_OF_DAY, d); //+n小时
        else if ("m".equals(type))
            c.add(Calendar.MINUTE, d); //+n分钟
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateTime = dateFormat.format(c.getTime());
        time = Timestamp.valueOf(dateTime);
        return time;
    }

    public static Timestamp currentTime() {
        return new Timestamp(System.currentTimeMillis());
    }

    public static boolean isNumeric(String str) {
        try {
            Double.valueOf(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    /**
     * java对象转Map
     *
     * @param obj
     * @return Map
     */
    public static HashMap<String, Object> transBean2Map(Object obj) {
        if (obj == null) {
            return null;
        }
        HashMap<String, Object> map = new HashMap<>();
        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                // 过滤class属性
                if (!key.equals("class")) {
                    // 得到property对应的getter方法
                    Method getter = property.getReadMethod();
                    Object value = getter.invoke(obj);
                    if (value == null)
                        continue;
                    map.put(key, value);
                }
            }
        } catch (Exception e) {
            System.out.println("transBean2Map Error " + e);
        }
        return map;
    }

    /**
     * 手机号加密和解密操作
     */
    private static String key = "k3PnA4qTdeciQ";

    public static String encode(String telNumber) {
        if (!org.springframework.util.StringUtils.isEmpty(telNumber)) {
            String pasNum = new String(Base64.getEncoder().encode(telNumber.getBytes()));
            StringBuilder sb = new StringBuilder(pasNum);
            for (int i = 0; i < key.length(); i++) {
                sb.insert(2 * i + 1, key.charAt(i));
            }
            String pasNumber = sb.toString();
            pasNumber = pasNumber.replaceAll("=", "O0O0O").replaceAll("\\+", "o000o").replaceAll("/", "oo00o");
            return pasNumber;
        }
        return "";
    }

    public static String decode(String code) {
        if (!org.springframework.util.StringUtils.isEmpty(code)) {
            code = code.replaceAll("oo00o", "/");
            code = code.replaceAll("o000o", "+");
            code = code.replaceAll("O0O0O", "=");

            StringBuilder sb = new StringBuilder(code);
            for (int i = key.length() - 1; i >= 0; i--) {
                sb.deleteCharAt(2 * i + 1);
            }
            byte[] num = Base64.getDecoder().decode(sb.toString());
            return new String(num);
        }
        return "";
    }

    public static void addDomainCookie(String sessionId,HttpServletResponse response){
        Cookie cookie = new Cookie("aspxUserTicket",sessionId);
        cookie.setDomain("fangxiaoer.com");
        cookie.setPath("/");
        cookie.setMaxAge(60*60*24*7);
        response.addCookie(cookie);
    }


    public static void removeDomainCookie(HttpServletRequest request,HttpServletResponse response){
        Cookie[] cookies = request.getCookies();
        if(cookies == null) {
            return;
        } else {
            for(Cookie c :cookies ){
                c = new Cookie(c.getName(), null);
                c.setDomain("fangxiaoer.com");
                c.setPath("/");
                c.setMaxAge(0);
                response.addCookie(c);
            }
        }
    }

    /**
     * MD5加密
     * @param password
     * @return
     */
    public static String MD5encode(String password){
        return DigestUtils.md5Hex(password).toUpperCase();
    }


    public static void addSessionAndCookie(HttpSession httpSession , HttpServletResponse response, LoginResultFilter loginResultFilter, String password){
        httpSession.setAttribute("muser",loginResultFilter.getSessionId());
        httpSession.setAttribute("sessionId",loginResultFilter.getSessionId());
        httpSession.setAttribute("userName",loginResultFilter.getUsername());
        httpSession.setAttribute("phoneNum",loginResultFilter.getMobile());
        httpSession.setAttribute("timeMillis",System.currentTimeMillis());
        httpSession.setAttribute("memberType",loginResultFilter.getMemberType());
        httpSession.setAttribute("isVip",loginResultFilter.getVip());
        httpSession.setAttribute("avatar",loginResultFilter.getPic());
        httpSession.setAttribute("vipEndTime",loginResultFilter.getVipEndTime());
        httpSession.setAttribute("authenticationStatus", loginResultFilter.getAuthenticationStatus());
//        String mobile = loginResultFilter.getMobile();
//        Utils.addCookie(response,"muser",mobile,"/");
//        Utils.addCookie(response,"mpassword",password,"/");
        addDomainCookie(loginResultFilter.getSessionId(),response);
    }

    public static void removeSession(HttpSession httpSession){
        httpSession.removeAttribute("muser");
        httpSession.removeAttribute("userName");
        httpSession.removeAttribute("phoneNum");
    }

    public static void executeSession(RedirectAttributes attributes,HttpSession session){
        Utils.removeSession(session);
        attributes.addFlashAttribute("error","您的账号已在其他地方登录，如不是您本人操作，请重新登录并修改密码");
    }

    //去掉.0
    public static  String modifyNum(String temp){
        if(temp.indexOf(".") != -1){
            temp = temp.replaceAll("0+?$","").replaceAll("[.]$", "");
        }
        return temp;
    }
    //正则匹配id
    public static String regId(String agencyId){
        //截取数字
        if(!StringUtils.isEmpty(agencyId)){
            String reg = "^\\d+";    //提取字符串开头的数字：71封妖塔守卫88 == >> 71
            Pattern p2 = Pattern.compile(reg);
            Matcher m2 = p2.matcher(agencyId);
            if(m2.find()){
                return m2.group(0);
            }
        }
        return agencyId;
    }

    /**
     * 判断数据是否为空
     *
     * @param obj 需要转换类型的数据
     * @return 数据转换城String
     */
    public static String objToStr(Object obj, String defaultValue) {
        return !StringUtils.isEmpty(obj) ? obj.toString() : defaultValue;
    }

    /**
     * 判断数据是否为空
     *
     * @param obj 需要转换类型的数据
     * @return 数据转换城Int
     */
    public static int objToInt(Object obj, int defaultValue) {
        int intValue = 0;
        try {
            intValue = !StringUtils.isEmpty(obj) ? Integer.parseInt(obj.toString()) : defaultValue;
        } catch (NumberFormatException e) {
            intValue = defaultValue;
        }
        return intValue;
    }

    public static String getIpAddr(HttpServletRequest request){
        String ip = request.getHeader("X-real-ip");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public static String removeSpecialCForUA(String userAgent) {
        if (userAgent == null) return null;
        char[] charArray = userAgent.toCharArray();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < charArray.length; i++) {
            if (charArray[i] >= 32 && charArray[i] <= 126) {
                sb.append(charArray[i]);
            }
        }
        return sb.toString();
    }


    public static HttpServletResponse downLoadFromUrl(String urlStr, HttpServletResponse response) {
        try {

            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // 设置超时间为3秒
            conn.setConnectTimeout(3 * 1000);
            // 防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=shenyangditie.jpg" );
            OutputStream toClient = response.getOutputStream();
            response.setContentType("application/octet-stream");
            // 得到输入流
            InputStream inputStream = conn.getInputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inputStream.read(buffer)) != -1) {
                toClient.write(buffer, 0, len);
            }
            toClient.flush();
            toClient.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;

    }

    /**
     * 获取出生年份（2002-1958）
     * @Date 2018.11.22
     * @return
     */
    public static List<HashMap<String,Object>> getInitYear(){
        List<HashMap<String,Object>> list = new ArrayList<>();
//        Calendar c = Calendar.getInstance();
        for(int i = 18; i < 61; i ++){
            HashMap<String,Object> map  = new HashMap<>();
            map.put("id",i);
            map.put("name",i);
            list.add(map);
        }
        return list;

    }

    /**
     * 判断是否为整数
     * @param str 传入的字符串
     * @return 是整数返回true,否则返回false
     */
    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    /**
     * 网页输出的文本按html格式处理
     * @param describe
     * @return
     */
    public static String replaceToHtml(String describe) {
        String regEx_space = "\n"; //定义空格回车换行符
        String regEx_space2 = "\r\n"; //定义空格回车换行符
        String regEx_space3 = " "; //定义空格回车换行符
        String regEx_space4 = "<br><br>"; //定义空格回车换行符
        if (StringUtils.isEmpty(describe)) return describe;
        describe = describe.replaceAll(regEx_space2,"<br>");
        describe = describe.replaceAll(regEx_space,"<br>");
        describe = describe.replaceAll(regEx_space3,"&nbsp;");
        describe = describe.replaceAll(regEx_space4,"<br>");
        return  describe;
    }

}