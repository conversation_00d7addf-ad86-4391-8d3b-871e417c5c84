package com.fangxiaoer.common;

/**
 * 通用信息枚举类
 * Created by Administrator on 2017/3/16.
 */
public class CommonEnum {

    /**
     * 公共
     */

    public enum codeFlage {
        SHOP_TYPE_SALE(1,"商铺出售"),
        SHOP_TYPE_RENT(2,"出租转租"),
        SHOP_TYPE_CHANGE(3,"商铺出兑转让"),
        TEST(1, "");

        private int code;
        private String name;

        codeFlage(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    /**
     * 数据字典json文件
     */
    public enum dicCodeFlage {
        TEST("", "");

        private String code;
        private String name;

        dicCodeFlage(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    /**
     * 用户
     */
    public enum userCodeFlage {
        LOGIN_COOKIE_SESSION("1", "cookieGlobalSession"),//登录cookie
        ADMIN_VALIDATE_CODE("ADMIN_VALIDATE", "后台验证码名称"),
        ADMIN_MOBILE_CODE("fxrGlobalMobileValidate", "后台手机验证码cookie名称"),
        USER_TYPE_PERSON("1", "用户类型_普通用户"),//只能写1 int 类型
        USER_TYPE_AGENT("2", "用户类型_经纪人"),//只能写2 int 类型
        ROLE_ORDINARY("ORDINARY", "普通用户角色"),
        ROLE_AGENT("AGENT", "经纪人角色"),
        USER_PERSON("person", "个人"),
        USER_AGENT("agent", "经纪人"),
        ON_LINE("online", "在线"),
        OFF_LINE("offline", "离线"),
        USER_TYPE_INTERNAL_AGENT("3","内部经纪人");

        private String code;
        private String name;

        userCodeFlage(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
    /**
     * 审核
     */
    public enum auditCodeFlage {
        PASS("pass", "通过"),
        BACK("back", "不通过"),
        AUDITING("1", "待审");
        private String code;
        private String name;

        auditCodeFlage(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    /**
     * 房屋配置
     */
    public enum configCodeFlage {
        RENT_HOUSE("RENT", "租房_数据匹配房屋配置"),
        SALE_HOUSE("SALE_HOUSE", "二手房_数据匹配的房源标签"),
        LAYOUT("LAYOUT","户型匹配户型标签"),
        HOUSE_CONFIG("HOUSE_CONFIG", "租房房屋配置"),
        HOUSE_TAG("HOUSE_TAG", "房源标签"),//二手房
        LAYOUT_TAG("LAYOUT_TAG","户型标签"),
        SHOPS("SHOPS","商铺经营行业"),
        SUBDISTRICT("SUBDISTRICT","新楼盘_数据匹配房屋标签");
        private String code;
        private String name;

        configCodeFlage(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    /**
     * 短信验证接口
     */
    public enum apiServiceURL {
        BASE("base", "https://ltapi.fangxiaoer.com/apiv1/base/"),
        VERIFY_SMSCODE("verifySmsCode", "https://ltapi.fangxiaoer.com/apiv1/base/verifySmsCode"),
        SEND_CODE("sendCode", "https://ltapi.fangxiaoer.com/apiv1/other/sendCode"),
        VERIFYIMAGECODE("verifyImageCode","https://ltapi.fangxiaoer.com/apiv1/other/verifyImageCode"),
        REG_SUCCESS("regSuccess","https://ltapi.fangxiaoer.com/apiv1/other/regSuccess"),
        SAVE_GUIDE("saveGuide","https://ltapi.fangxiaoer.com/apiv1/active/saveGuide"),
        GET_WXSIGN("getWxSign","https://ltapi.fangxiaoer.com/apiv1/other/getWxSign");

        private String code;
        private String name;

        apiServiceURL(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
    /**
     * 访问方式
     */
    public enum loginWayFlage {
        PC(1, "PC端"),
        MOBILE(2, "手机"),
        TABLET(3, "平板");
        private Integer code;
        private String name;

        loginWayFlage(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
