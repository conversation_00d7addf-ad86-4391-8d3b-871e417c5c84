package com.fangxiaoer.common;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Component
public class IPMonitor {

    Logger logger = LoggerFactory.getLogger(IPMonitor.class);
    // 根据级别进行封停：
    // 级别1，js验证
    // 级别2，验证码验证
    // 级别3，503封停1小时
    public enum BLOCK_LEVEL {
        ALLOW, JS_VERIFY, CODE_VERIFY, BLOCK
    }

    private LinkedHashMap<String, BlockNode> blocker = new LinkedHashMap<>();
    private IPTree ips = new IPTree();

    // 普通人每10分钟能访问多少个页面（不包括API调用）
    private final static int PERSON_RATE = 50;

    // 每 10 分钟打印一次网站访问压力
    private static int sFrequencyCounter = 0;

    /**
     * 根据访问频率决定采用哪种屏蔽级别
     */
    public BLOCK_LEVEL fromInt(int rate) {
        BLOCK_LEVEL level = BLOCK_LEVEL.ALLOW;
        if (rate >= 1) {
            level = BLOCK_LEVEL.JS_VERIFY;
        }
        if (rate >= 2) {
            level = BLOCK_LEVEL.CODE_VERIFY;
        }
        if (rate >= 4) {
            level = BLOCK_LEVEL.BLOCK;
        }
        return level;
    }

    /**
     * 根据屏蔽级别，返回对应访问频率（仅用于比较大小）
     */
    public int toInt(BLOCK_LEVEL level) {
        if (level == BLOCK_LEVEL.ALLOW) {
            return 0;
        } else if (level == BLOCK_LEVEL.JS_VERIFY) {
            return 1;
        } else if (level == BLOCK_LEVEL.CODE_VERIFY) {
            return 2;
        } else if (level == BLOCK_LEVEL.BLOCK) {
            return 4;
        }
        return 0;
    }

    /**
     * 返回当前ip的屏蔽级别，如果null，则还没被屏蔽
     */
    public BlockNode validateIP(String ip) {
        sFrequencyCounter ++;
        ips.push(ip);
        Pattern p = Pattern.compile("^(\\d+\\.\\d+\\.\\d+)\\.\\d+$");
        Matcher m = p.matcher(ip);
        if(!m.matches()) return null;
        String headIp = m.group(1);
        if(blocker.containsKey(headIp)) {
            return blocker.get(headIp);
        }
        if (blocker.containsKey(ip)) {
            return blocker.get(ip);
        }
        return null;
    }

    /**
     * 调试用，打印出当前拦截情况
     */
    public String print() {
        Iterator<Map.Entry<String, BlockNode>> iterator = blocker.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, BlockNode> item = iterator.next();
            BlockNode node = item.getValue();
            logger.info(node.toString());
        }
        return "";
    }

    @Scheduled(cron = "0 0/10 * * * ?")
    public void cleanIps() {
        logger.error("=======start to clean======= Block size: " + blocker.size() + ", Frequency: " + sFrequencyCounter);
        sFrequencyCounter = 0;
        // 定时清掉失效的记录树和拦截
        ips.cleanTree(10*60);
        Iterator<Map.Entry<String, BlockNode>> iterator = blocker.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, BlockNode> item = iterator.next();
            BlockNode node = item.getValue();
            logger.info(node.toString());
            // 惩罚级别越高，需要解除的时间越多
            if ((System.currentTimeMillis() - node.getStartTime()) > 1000*60*60*toInt(node.getLevel())) {
                iterator.remove();
            }
        }
    }

    /**
     * 存储ip访问频率的树
     */
    public class IPTree {
        private IPTreeNode root = new IPTreeNode();

        /**
         * 将ip拆分4层，分别插入树中对应的层
         */
        public void push(String ip) {
            Pattern p = Pattern.compile("^(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)$");
            Matcher m = p.matcher(ip);
            if(!m.matches()) return;
            // 将ip拆分4层，分别插入树中对应的层
            IPTreeNode depth_1 = root.updateOrAddChild(m.group(1));
            IPTreeNode depth_2 = depth_1.updateOrAddChild(m.group(2));
            IPTreeNode depth_3 = depth_2.updateOrAddChild(m.group(3));
            IPTreeNode depth_4 = depth_3.updateOrAddChild(m.group(4));
            // 检查第三层和第四层的ip节点是否有爆掉的情况
            BLOCK_LEVEL currentLevel = checkBlockIpNode(depth_4);
            // 优先检查4层ip是否爆点，如果爆了，尽量不要对3层其他用户有负面影响
            // 理论讲，一ip爆点，尽量不要封杀ip段。一片ip爆点，才封杀ip段
            if (toInt(currentLevel) < toInt(BLOCK_LEVEL.CODE_VERIFY)) {
                checkBlockIpNode(depth_3);
            }
        }

        /**
         * 将非法节点取出，放到拦截序列中
         */
        private BLOCK_LEVEL checkBlockIpNode(IPTreeNode node) {
            // 需要检查3、4层节点的ip，1、2层不管
            if(node.depth < 3) return BLOCK_LEVEL.ALLOW;
            // 每个ip，在固定时间段内的访问比率太高，惩罚级别越高；3层是4层拦截的5倍，防止ip段攻击
            int rate = node.count / PERSON_RATE / (node.depth == 4 ? 1 : 5);
            logger.info("checkBlockIpNode: " + node.toString());
            if (rate == 0) return BLOCK_LEVEL.ALLOW;
            String ip = node.getIpStr();
            // 经过计算，建议的防护级别
            BLOCK_LEVEL suggest_level = fromInt(rate);
            BLOCK_LEVEL current_level = BLOCK_LEVEL.ALLOW;
            // 如果已经存在，取出当前防护级别
            if (blocker.containsKey(ip)) {
                current_level = blocker.get(ip).getLevel();
            }
            // 如果建议的级别，比当前级别高，那么需要升级，否则不需要
            if (toInt(suggest_level) > toInt(current_level)) {
                blocker.put(ip, new BlockNode(ip, suggest_level));
            }
            return suggest_level;
        }

        public void cleanTree(int timeoutSeconds) {
            logger.info("=======Clean tree node======= ");
            root.children.clear();
            // cleanChild(root, timeoutSeconds);

        }

        private void cleanChild(IPTreeNode upperNode, int timeoutSeconds) {
            logger.info("=======Clean tree node======= sub tree size: " + upperNode.children.size());
            Iterator<Map.Entry<String, IPTreeNode>> iterator = upperNode.children.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, IPTreeNode> item = iterator.next();
                IPTreeNode child = item.getValue();
                if (child.depth == 4) {
                    if((System.currentTimeMillis() - child.updateTime) > 1000 * timeoutSeconds) {
                        child.parent = null;
                        iterator.remove();
                    }
                } else {
                    // 当子节点是上层时，判断下一层有无数据，没有则本层节点
                    if (child.children.size() == 0) {
                        child.parent = null;
                        iterator.remove();
                    } else {
                        cleanChild(child, timeoutSeconds);
                    }
                }
            }



        }
    }


    /**
     * 被封停ip的存储节点
     */
    public class BlockNode {
        private String ip;
        private BLOCK_LEVEL level;
        private long startTime;
        // 此次封停的标识，用以判定何时解封，要不要再封
        private String uid;

        public BlockNode(String ip, BLOCK_LEVEL level) {
            this.ip = ip;
            this.level = level;
            this.startTime = System.currentTimeMillis();
            this.uid = UUID.randomUUID().toString();
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public BLOCK_LEVEL getLevel() {
            return level;
        }

        public void setLevel(BLOCK_LEVEL level) {
            this.level = level;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        @Override
        public String toString() {
            final StringBuffer sb = new StringBuffer("BlockNode{");
            sb.append("ip='").append(ip).append('\'');
            sb.append(", level=").append(level.name());
            sb.append(", startTime=").append(new DateTime(startTime).toString("HH:mm:ss"));
            sb.append(", uid='").append(uid).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }

    /**
     * ip访问频率的存储节点
     */
    public class IPTreeNode {
        private String id;
        private long updateTime = System.currentTimeMillis();
        private int count;
        private int depth;
        private IPTreeNode parent;
        private Map<String, IPTreeNode> children =  new HashMap<>();


        public IPTreeNode() {
            this.depth = 0;
            this.count = 0;
        }

        public IPTreeNode(String id) {
            this.id = id;
            this.depth = 1;
            this.count = 1;
        }

        private void addChild(IPTreeNode child) {
            child.depth = this.depth + 1;
            child.parent = this;
            this.children.put(child.id, child);
        }

        public void removeChild(String id) {
            IPTreeNode child = this.children.remove(id);
            child.parent = null;
        }

        public IPTreeNode findChild(String id) {
            return this.children.get(id);
        }

        public boolean hasChild(String id) {
            return this.children.containsKey(id);
        }

        public IPTreeNode updateOrAddChild(String id) {
//            this.updateTime = System.currentTimeMillis();
            IPTreeNode child;
            if(this.hasChild(id)) {
                child = this.findChild(id);
//                child.updateTime = System.currentTimeMillis();
                child.count += 1;
            } else {
                child = new IPTreeNode(id);
                this.addChild(child);
            }
            return child;
        }

        public String getIpStr() {
            if(this.parent != null) {
                String headStr = this.parent.getIpStr();
                return headStr == "" ? this.id : headStr + "." + this.id;
            } else {
                return "";
            }
        }

        @Override
        public String toString() {
            final StringBuffer sb = new StringBuffer("IPTreeNode{");
            sb.append("ip='").append(getIpStr()).append('\'');
            sb.append(", count='").append(count).append('\'');
            sb.append(", time='").append(new DateTime(updateTime).toString("HH:mm:ss")).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }
}
