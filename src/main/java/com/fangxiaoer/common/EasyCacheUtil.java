package com.fangxiaoer.common;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EasyCacheUtil {
    private static HashMap<String, CacheNode> CACHES = new HashMap<>();

    public static class CacheNode {
        private long timeMillis;
        private Object results;

        public CacheNode(Object content) {
            this.results = content;
            this.timeMillis = System.currentTimeMillis();
        }

        public boolean removeAble() {
            if(System.currentTimeMillis() - timeMillis > 5 * 60 * 1000) {
                return true;
            }
            return false;
        }
    }

    private static long cleanTime = System.currentTimeMillis();
    public static void cleanCache() {
        if (System.currentTimeMillis() - cleanTime < 5000) {
            return;
        }
        cleanTime = System.currentTimeMillis();
        List<String> keys = new ArrayList<>();
        for(Map.Entry<String, CacheNode> item : CACHES.entrySet()) {
            if (item.getValue().removeAble()) {
                keys.add(item.getKey());
            }
        }
        for (String key : keys) {
            CACHES.remove(key);
        }
    }

    public static String generateKey(HashMap<String, Object> params) {
        Gson gson = new Gson();
        String content = gson.toJson(params);
        return Utils.MD5encode(content);
    }

    public static void put(String key, Object value) {
        CACHES.put(key, new CacheNode(value));
    }

    public static Object get(String key) {
        cleanCache();
        return CACHES.get(key) == null ? null : CACHES.get(key).results;
    }

    public static int count(String key) {
        cleanCache();
        CacheNode node = CACHES.get(key);
        if(node == null) {
            node = new CacheNode(1);
            CACHES.put(key, node);
        }
        if (node.results instanceof Integer) {
            node.results = (int)node.results + 1;
            return (int)node.results;
        } else {
            return 0;
        }
    }

    public static void cleanCount(String key) {
        CACHES.remove(key);
    }
}
