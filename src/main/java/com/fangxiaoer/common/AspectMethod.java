package com.fangxiaoer.common;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Created by Lyn on 2017/5/6.
 */
@Aspect
@Component
public class AspectMethod {


    /**
     * 解决SY端验证码被攻击问题
     */
    @Before("execution(public * com.fangxiaoer.controller..*.*(..))")
    public void acquireSMS(JoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        Class<?> returnType = methodSignature.getReturnType();
        if (!returnType.getName().equals("java.lang.String")) {
            return;
        }
        String url = request.getRequestURI();
        if(url.contains("getAskInfo") || url.contains("getAgentInfo") || url.contains("getCommentInfo")){
            return;
        }
        String warrenty = Utils.uuidGenerator();
        request.getSession().setAttribute("warrenty", warrenty);
        Utils.addCookie(response, "warrenty", warrenty, "/", 60 * 60);
    }
}
