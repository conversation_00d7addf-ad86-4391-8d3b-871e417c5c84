package com.fangxiaoer.common;

import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * Created by t<PERSON>ai on 2017/6/21.
 */
public class EmailUtil {
    Logger logger = LoggerFactory.getLogger(EmailUtil.class);

    private static EmailUtil sEmailUtil;

    private int threadNum = 0;

    private int serverSwitcher = 0;
    private String[][] server = {
            {"<EMAIL>", "c5jVCmJFMkUs"},
            {"<EMAIL>", "TMAlwwu1jb"}
    };

    public static void postEmail(String[] receivers, Exception ex, String url) {
        String errorStr = "Error at: " + url + "</br>";
        StackTraceElement[] sts = ex.getStackTrace();
        errorStr += ex.getClass().getName() + ":" + ex.getMessage();
        for (StackTraceElement s : sts) {
            errorStr += "\n" + s.toString();
        }
        postEmail(receivers, "API system uncatch error", errorStr);
    }

    public static void postEmail(String[] receivers, String title, String content) {
        EmailUtil.getInstance().postEmailEnqueue(receivers, title, content);
    }

    public static EmailUtil getInstance() {
        if(sEmailUtil == null)
            sEmailUtil = new EmailUtil();

        return sEmailUtil;
    }


    public void postEmailEnqueue(String[] receivers, final String title, final String content) {
        if(threadNum > 3) {
            return;
        }
        Thread thread = new Thread(new EmailRunnable(receivers, title, content, new EmailCallback() {
            @Override
            public void onSuccess() {
                threadNum --;
            }

            @Override
            public void onFail() {
                threadNum --;
                serverSwitcher ++;
                if(serverSwitcher >= server.length) {
                    serverSwitcher = 0;
                }
            }
        }));
        threadNum ++;
        thread.start();
    }

    private class EmailRunnable implements Runnable {
        private String[] receivers;
        private String title;
        private String content;
        private EmailCallback callback;

        EmailRunnable(String[] receivers, String title, String content, EmailCallback callback) {
            this.receivers = receivers;
            this.title = title;
            this.content = content;
            this.callback = callback;
        }

        @Override
        public void run() {
                try {
                    if (receivers.length == 0) return;
                    Properties props = new Properties();
                    // 开启debug调试
                    props.setProperty("mail.debug", "true");
                    // 发送服务器需要身份验证
                    props.setProperty("mail.smtp.auth", "true");
                    // 设置邮件服务器主机名
                    props.setProperty("mail.host", "mail.fangxiaoer.net");
                    // 发送邮件协议名称
                    props.setProperty("mail.transport.protocol", "smtp");

                    // 设置环境信息
                    Session session = Session.getInstance(props);

                    // 创建邮件对象
                    Message msg = new MimeMessage(session);
                    msg.setSubject(title);
                    // 设置邮件内容
                    String tempContent = content + "</br>" + "系统管理员</br>" +
                            new DateTime().toString("yyyy年MM月dd日 H时m分");
                    msg.setContent(tempContent, "text/html;charset=utf-8");

                    // 设置发件人
                    msg.setFrom(new InternetAddress("<EMAIL>"));

                    Transport transport = session.getTransport();
                    // 连接邮件服务器
                    transport.connect(server[serverSwitcher][0], server[serverSwitcher][1]);
                    // 发送邮件
                    Address[] addresses = new Address[receivers.length];
                    for (int i = 0; i < receivers.length; i++) {
                        addresses[i] = new InternetAddress(receivers[i]);
                    }
                    msg.addRecipients(Message.RecipientType.TO, addresses);
                    transport.sendMessage(msg, msg.getAllRecipients());
                    // 关闭连接
                    transport.close();
                    callback.onSuccess();
                } catch (MessagingException e) {
                    logger.error("Email", e);
                    callback.onFail();
                }
        }
    }

    private interface EmailCallback {
        void onSuccess();
        void onFail();
    }
}
