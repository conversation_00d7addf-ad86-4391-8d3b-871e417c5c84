package com.fangxiaoer.common;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class EmailProcessor {

    private int emailCount = 0;
    private int failCount = 0;

    private static EmailProcessor instance;

    public static EmailProcessor getInstance() {
        if (instance == null) {
            instance = new EmailProcessor();
        }
        return instance;
    }

    private List<String> logs = new ArrayList<>();

    public void pushError(String log) {
        failCount++;
        log = "</br>\n========================" + "</br>\n" + new Date() + "</br>\n" + log + "</br>\n";
        logs.add(log);
        if (logs.size() > 15) {
            emailCount++;
            StringBuilder sb = new StringBuilder();
            logs.forEach(item -> {
                sb.append(item);
                sb.append("</br>\n");
            });
            logs.clear();
            print(sb.toString());
        }
    }

    private void print(String content) {
        String title = "Sy Site error, total email: " + emailCount + ", total failure: " + failCount;
        EmailUtil.postEmail(new String[]{"<EMAIL>"}, title, content);
    }
}

