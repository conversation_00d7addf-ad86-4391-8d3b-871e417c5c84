package com.fangxiaoer.common;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/3/14.
 */
public class Constants {
    // 线上地址
    public static final String HOST = "https://ltapi.fangxiaoer.com";
    // 测试地址
    // public static final String HOST = "http://*************:8081";
//     public static final String HOST = "http://************:8083";
//     public static final String HOST = "http://*************:8083";
//     public static final String HOST = "http://********:8083";
    /**
     * sy.fangxiaoer.com接口
     */
    public static final String INDEX_PAGE_NEW = "index_page_new";

    //处理搜索记录传送接口服务功能
    public static final String SEARCH_CLICK_ANALYZE = HOST + "/apiv1/other/searchClickAnalyze";
    //检查是否是会员
    public static final String MEMBER_VALIDATE = HOST + "/apiv1/base/checkIsMember";
    //全文检索
//    public static final String COMMON_SEARCH = HOST + "/apiv1/base/search";
    public static final String COMMON_SEARCH = HOST + "/apiv1/base/searchIndex";

    public static final String COMMON_SEARCH_NEW = HOST + "/apiv1/base/new_searchIndex";
    //新房小区
    public static final String FILTER_NEWHOUSE_REGION = HOST + "/apiv1/house/getRegionFilter";
    //新房价格
    public static final String FILTER_NEWHOUSE_PRICE = HOST + "/apiv1/house/getNewPriceFilter";
    //查看预约状态
    public static final String CHECK_ORDERSTATE = HOST + "/apiv1/active/checkGuidesState";
    //取消预约
    public static final String CANCEL_GUIDE = HOST + "/apiv1/active/cancelGuide";
    //预约看房
    public static final String ORDER_HOUSE_GUIDE = HOST + "/apiv1/active/orderHouseGuide";
    //查看活动列表
    public static final String ACTIVITIES_LIST = HOST +"/apiv1/active/viewActivities";
    //新房活动区域
    public static final String FILTER_ACTIVITIES_REGION = HOST + "/apiv1/house/activityRegionFilter";
    //地铁线
    public static final String FILTER_SUBWAY_LINE = HOST + "/apiv1/house/getSubWayFilter";
    //地铁站
    public static final String FILTER_SUBWAY_STATIONS = HOST + "/apiv1/house/getSubStationFilter";
    //学校类型
    public static final String FILTER_SCHOOL_TYPE = HOST + "/apiv1/house/getSchoolTypeFilter";
    //帮你找房功能
    public static final String HELP_SEARCH_HOUSE = HOST + "/apiv1/active/saveGuide";
    //首页广告
    public static final String HOME_PAGE_ADVERTISEMENT = HOST + "/apiv1/sysite/getSyNewAdvertisement";
    //首页品牌&排行榜
    public static final String RANK_AND_BRAND = HOST + "/apiv1/active/viewRankAndBrand";
    //二手房租商铺写字楼广告
    public static final String EXCEPT_NEWHOUSE_ADVERTISEMENT = HOST + "/apiv1/sysite/advertisementList";
    //普宅详情页
    public static final String HOUSE_DETAIL_PAGE = HOST + "/apiv1/sysite/viewResidenceDetail";
    //查看720看房
    public static final String VIEW_PAN_LIST = HOST + "/apiv1/news/viewPanList";
    //查看新房列表页
    public static final String VIEW_PROJECT_LIST = HOST + "/apiv1/house/viewProjectInfos";
    //学校列表页
    public static final String SCHOOL_LIST = HOST + "/apiv1/house/viewSchoolInfo";
    //学校地图处理
    public static final String SEARCH_SCHOOL_LIST = HOST + "/apiv1/house/viewSchoolForMap";
    //新版学校列表页
    public static final String NEW_SCHOOL_LIST = HOST + "/apiv1/house/viewNewSchoolList";
    //项目销售动态
    public static final String PROJECT_SALE_INFO = HOST + "/apiv1/sysite/viewSaleInfo";
    //新销售动态
    public static final String PROJECT_SALE_DYNAMICS = HOST + "/apiv1/house/projectDynamics";
    //楼盘在售信息
    public static final String HOUSE_SALE_INFO = HOST + "/apiv1/sysite/viewBuildSaleInfo";
    //获取新房详细页JSON数据
    public static final String HOUSE_DETAIL_JSON = HOST + "/apiv1/sysite/viewJsonInfo" ;
    //获取项目户型信息
    public static final String HOUSE_LAYOUT_INFO = HOST + "/apiv1/house/viewLayoutInfo";
    //获取项目评论数量
    public static final String PROJECT_COMMENT_COUNT = HOST + "/apiv1/sysite/viewCommentCount";
    //获取项目评论信息
    public static final String PROJECT_COMMENT_INFO = HOST +"/apiv1/other/viewComments";
    //获取项目评论星级
    public static final String PROJECT_COMMENT_SCORE = HOST +"/apiv1/other/viewGradeByProject";
    //获取项目咨询数量
    public static final String PROJECT_ASK_COUNT = HOST + "/apiv1/sysite/viewAskCount";
    //获取项目咨询信息
    public static final String PROJECT_ASK_INFO = HOST + "/apiv1/sysite/viewAskInfo";
    //户型图户型
    public static final String GET_LAYOUT_TYPE = HOST +"/apiv1/house/viewLayoutType";
    //户型图类型
    public static final String GET_LAYOUT_FILTER = HOST +"/apiv1/house/viewLayoutFilter";
    //获取在线选房分类及数量
    public static final String GET_ROOM_TYPE = HOST +"/apiv1/sysite/viewRoomType";
    //获取在线选房信息
    public static final String GET_ONLINE_INFO= HOST +"/apiv1/sysite/viewOnlineHouse";
    //获取相册分类信息
    public static final String GET_PHOTO_INFO= HOST +"/apiv1/sysite/viewPicFilter";
    //获取相册样板间信息
    public static final String GET_MODEL_ROOM= HOST +"/apiv1/sysite/viewModolPicFilter";
    //获取相册分类数量
    public static final String GET_PHOTO_COUNT= HOST +"/apiv1/sysite/viewPhotosCount";
    //查询别墅详细信息
    public static final String VIEW_VILLA_DETAIL = HOST + "/apiv1/sysite/viewVillaDetail";
    //公寓详情页
    public static final String GET_APARTMENT_DETAIL= HOST +"/apiv1/sysite/viewOfficeBuildingDetail";
    //在线选房详情
    public static final String GET_ONLINEROOM_DETAIL= HOST +"/apiv1/sysite/viewRoomDetail";
    //现房列表
    public static final String GET_EXITS_INFO= HOST +"/apiv1/sysite/viewExixtInfo";
    //现房月份
    public static final String FILTER_EXITS_MONTH = HOST +"/apiv1/sysite/getCalendar";
    //环线
    public static final String FILTER_LOOP_LINE = HOST +"/apiv1/house/getLoopLineFilter";
    //获取户型图信息
    public static final String GET_LAYOUT_INFO = HOST + "/apiv1/sysite/viewRoomInfo";
    //获取子户型详情
    public static final String GET_SUB_LAYOUT = HOST + "/apiv1/sysite/viewVillaPicSub";
    //查询学校关联房型列表接口
    public static final String GET_SCHOOL_HOUSE_LIST = HOST + "/apiv1/house/viewSchoolHouseInfo";
    //二手房房源发布
    public static final String ADD_SALEHOUSE = HOST + "/apiv1/house/addSaleHouse";
    //其他需求接口(预约看房、过户代办)
    public static final String ADD_OTHERGUIDE = HOST + "/apiv1/active/otherGuide";
    //房源所有者类型（个人、经纪人）
    public static final String MEMBER_TYPE = HOST + "/apiv1/house/memberTypeFilter";
    //新房列表页和详情页向经纪人咨询时调取经纪人信息
    public static final String AGENTINFO_TOASK = HOST + "/apiv1/other/viewAgentInfoToAsk";
    //新房列表页和详情页个人向经纪人咨询时发送电话号
    public static final String MY_TOASK_AGENT = HOST + "/apiv1/active/orderHouseGuide";
    //精装及二手房面积
    public static final String FILTER_SCDHOUSE_AREA = HOST + "/apiv1/house/getDecAreaFilter";
    //精装房
    public static final String FILTER_DECORATION_AREA = HOST + "/apiv1/house/getDecorFilter";
    //查看活动列表新
    public static final String DiscountAd_LIST = HOST +"/apiv1/sysite/viewDiscountAd";  //查看活动列表新
    //普通用户保存贷款信息
    public static final String ADDLOAN = HOST +"/apiv1/other/addLoan";
    //普通用户保存咨询
    public static final String ADD_ASK = HOST +"/apiv1/other/addAsk";
    //业主评价
    public static final String ADD_COMMENT = HOST +"/apiv1/other/addComment";
    //评价回复
    public static final String ADD_REPLY = HOST +"/apiv1/other/addreply";
    //评价回复
    public static final String ORDER_ACTIVITY = HOST +"/apiv1/active/orderActivity";
    //新房详情页地图找房
    public static final String VIEW_NEWPROJECT_MAP = HOST + "/apiv1/house/fetchNearByProject";
    //新房详情同价位、周边、二手楼盘
    public static final String VIEW_RELANTION_HOUSE = HOST + "/apiv1/house/pushProjectByPrice";
    //指定学校id查询学校具体信息
    public static final String VIEW_SCHOOL_HOUSE = HOST + "/apiv1/house/viewSchoolAndProject";
    //品牌馆首页
    public static final String VIEW_BRAND_INDEX = HOST + "/apiv1/other/brands";
    //品牌馆详情页
    public static final String VIEW_BRAND_DETIAL = HOST + "/apiv1/house/viewBrandDetail";
    //品牌馆资讯
    public static final String VIEW_BRAND_NEWS = HOST + "/apiv1/news/viewBrandNews";
    //品牌动态
    public static final String BRAND_DY_INFO = HOST + "/apiv1/house/viewBrandDyInfo";
    //品牌房地图
    public static final String VIEW_BRAND_MAP = HOST +"/apiv1/house/viewBrandMap";
    //新房地图找房搜索
    public static final String PROJECT_SEARCH_MAP = HOST +"/apiv1/house/searchNewProject";
    //新房地图找房搜索列表
    public static final String PROJECT_SEARCH_MAP_LIST = HOST +"/apiv1/house/viewNewProjectMap";
    //小二说房增加数量
    public static final String AUDIO_NUMPLUS = HOST +"/apiv1/news/playAudio";
    //监测收藏
    public static final String COLLECTION_CHECK = HOST +"/apiv1/active/checkFavorite";
    //添加收藏
    public static final String COLLECTION_ADD = HOST +"/apiv1/active/newFavorite";
    //取消收藏
    public static final String COLLECTION_CANCEL = HOST +"/apiv1/active/cancelFavorite";
    //地铁找房
    public static final String VIEW_STATION_MAP = HOST +"/apiv1/house/viewStationForMap";
    //楼盘销售状态
    public static final String JSON_PROJECT_SALESTATUS = HOST + "/apiv1/house/getProjectStatusFilter";
    //分类房产排行榜
    public static final String GET_RANK_LIST_BYTYPE = HOST + "/apiv1/house/viewRankProject";
    //排行榜分类获取
    public static final String GET_RANK_TYPE = HOST + "/apiv1/house/viewRankFilter";
    //查看榜单区域
    public static final String VIEW_RANK_REGION = HOST + "/apiv1/house/viewRegionFilter";
    //排行榜分类处理
    public static final String VIEW_RANK_FILTER = HOST + "/apiv1/house/viewNewRankFilter";
    //子榜情况
    public static final String VIEW_MINOR_RANK = HOST + "/apiv1/house/viewMinorRank";
    //新房面积filter
    public static final String PROJECT_AREA_FILTER = HOST + "/apiv1/house/getProjectAreaFilter";
    //项目动态
    public static final String VIEW_PROJECT_NEWS = HOST + "/apiv1/house/viewDyForProject";
    //项目动态类别
    public static final String VIEW_PROJECT_NEWS_FILTER= HOST + "/apiv1/house/projectDynamicFilter";
    //项目支付查询
    public static final String PROJECT_PAY_CHECK = HOST + "/apiv1/other/viewProjectPayInfo";
    //在线支付付款获取二维码，创建订单接口
    public static final String PROJECT_PAY = HOST + "/apiv1/other/projectPay";
    //项目订单查询
    public static final String PROJECT_PAY_ORDER = HOST + "/apiv1/other/viewPayOrderDetail";

    /**
     * 二手房模块接口
     */
    //二手房列表
    public static final String SECOND_HOUSE_LIST = HOST + "/apiv1/house/viewHouses";
    //二手房成交列表
    public static final String DEAL_SECOND_LIST = HOST + "/apiv1/house/viewDealSaleHouses";
    //二手房价格
    public static final String FILTER_SCDHOUSE_PRICE = HOST + "/apiv1/house/viewPriceFilter";
    //朝向
    public static final String FILTER_HOUSE_FORWARD = HOST + "/apiv1/house/getForwardFilter";
    //朝向查询相关功能
    public static final String FILTER_HOUSE_FORWARD_SEARCH = HOST + "/apiv1/house/viewForwardFilter";
    //装修类型
    public static final String FILTER_HOUSE_DECORATION = HOST + "/apiv1/house/getFitmentFilter";
    //房源特色
    public static final String GET_HOUSE_FEATURE = HOST + "/apiv1/house/viewHouseTrait";
    //房源特色(新)
    public static final String NEW_HOUSE_FEATURE = HOST + "/apiv1/house/viewNewHouseTrait";
    //二手房详情页
    public static final String GET_SCDHOUSE_DETAIL = HOST + "/apiv1/house/viewHouseDetail";
    //二手房成交详情页
    public static final String DEAL_SCDHOUSE_DETAIL = HOST + "/apiv1/house/viewDealSaleHouseDetail";
    //根据区域获取地点
    public static final String FILTER_SCDHOUSE_PLATES = HOST + "/apiv1/house/viewPlates";
    //获取二手房、租房、商铺区域
    public static final String FILTER_SCDHOUSE_REGIONS = HOST + "/apiv1/house/viewRegions";
    //写字楼区域
    public static final String FILTER_OFFICE_REGIONS = HOST + "/apiv1/house/viewRegionsForOffice";
    //获取二手房、租房、商铺区域
    public static final String FILTER_SCDHOUSE_FLOOR = HOST + "/apiv1/sysite/getFloorFilter";
    //获取品牌中介
    public static final String GET_INTERMEDIARY_FILTER = HOST + "/apiv1/house/getIntermediaryFilter";

    //获取可能感兴趣的小区
    public static final String VIEW_INTERSED_SUB = HOST + "/apiv1/sysite/viewInterstedSub";

    //二手房列表（极简模式）
    public static final String VIEW_SCDHOUSE_OTHER = HOST + "/apiv1/sysite/viewSaleList";
    //二手房新房推荐广告/apiv1/house/lottery
    public static final String SCDHOUSE_LOTTERY = HOST + "/apiv1/house/lottery";
    //获取佣金95折订单
    public static final String GET_PAY_ORDER = HOST +"/apiv1/other/getSecondHandHousePay";
    //虚假房源的选项
    public static final String FALSITY_HOUSE= HOST +"/apiv1/other/saleHouseIllegalTypeFilter";
    //虚假房源提交
    public static final String FALSITY_HOUSE_SUB= HOST +"/apiv1/other/saleHouseIllegalCommit";
    //板块信息（二手房列表专用2018.10.11）
    public static final String FILTER_PLATES_INFORMATION = HOST + "/apiv1/house/viewPlates2";
    //获取视频地址接口（2018.10.20）
    public static final String VIEW_VIDEO_PATH = HOST + "/apiv1/house/getVideoPath";

    /**
     * 租房模块
     */
    //租房列表
    public static final String GET_RENTHOUSE_LIST = HOST + "/apiv1/house/viewRentHouseList";
    //成交租房列表
    public static final String DEAL_RENT_LIST = HOST + "/apiv1/house/viewDealRentHouses";
    //租金
    public static final String GET_RENTHOUSE_PRICE = HOST + "/apiv1/house/getRentPriceFilter";
    //小区列表
    public static final String GET_RENTVILLAGE_LIST = HOST + "/apiv1/house/viewDistrictList";
    //小区列表（新）
    public static final String VIEW_NEW_SUBDISTRICT = HOST + "/apiv1/house/viewNewSubdistricts";
    //小区详情
    public static final String GET_RENTVILLAGE_DETAIL = HOST + "/apiv1/house/viewDistrictDetail";
    //小区详情（新）
    public static final String NEW_SUB_DETAIL = HOST + "/apiv1/house/viewNewSubdistrictDetail";
    //小区图片
    public static final String GET_DISTRICT_PIC = HOST + "/apiv1/house/viewDistrictPics";
    //小区图片（新）
    public static final String NEW_SUB_PHOTOS = HOST + "/apiv1/house/viewSubdistrictAlbum";

    //80.小区问答列表页--/apiv1/other/viewAskSubdistrictList
    public static final String GET_ASK_SUBDISTRICT_LIST = HOST + "/apiv1/other/viewAskSubdistrictList";
    //查询小区问题列表带答案(移动端) --/apiv1/other/getAskListWithAnswer
    public static final String GET_ALL_ASK_SUBDISTRICT_LIST = HOST + "/apiv1/other/getAskListWithAnswer";
    //获取小区年限
    public static final String GET_SUB_AGE = HOST + "/apiv1/house/getSubAge";
    //小区问答添加问题--/apiv1/other/addAskSubdistrict
    public static final String ADD_ASK_SUBDISTRICT = HOST + "/apiv1/other/addAskSubdistrict";
    //小区问答回答问题--/apiv1/other/addReplySubdistrict
    public static final String ADD_REPLY_SUBDISTRICT = HOST + "/apiv1/other/addReplySubdistrict";
    //-/apiv1/other/viewAsksNoReply  全部未解决问题  传type=1
    public static final String GET_ASKS_NO_REPLY = HOST + "/apiv1/other/viewAsksNoReply";
    ///apiv1/other/viewAskSubdistricDetail      详情
    public static final String GET_ASK_SUBDISTRIC_DETAIL = HOST + "/apiv1/other/viewAskSubdistricDetail";
    //    84.sy站，大众版查看专家解读列表--/apiv1/other/viewExpertSubdistricts
    public static final String GET_EXPERT_SUBDUSTRICTS = HOST + "/apiv1/other/viewExpertSubdistricts";
    //租房详情
    public static final String GET_RENTHOUST_DETAIL = HOST + "/apiv1/house/viewRentHouseDetail";
    //租房成交详情
    public static final String DEAL_RENTHOUST_DETAIL = HOST + "/apiv1/house/viewDealRentHouseDetail";
    //房屋配置
    public static final String GET_OTHER_ESTABLISH = HOST + "/apiv1/house/getOtherEstablish";
    //付款方式
    public static final String GET_PAYMENT_FILTER = HOST + "/apiv1/house/getPaymentFilter";
    //卧室类别
    public static final String GET_BEDROOM_FILTER = HOST + "/apiv1/house/getBedRoomFilter";
    //性别限制
    public static final String GET_SEX_FILTER = HOST + "/apiv1/house/getSexFilter";
    //查询地铁站
    public static final String SUBWAY_LINE = HOST + "/apiv1/house/getSubWayFilter";
    //装修类型
    public static final String GET_HOUSE_DECORATION = HOST + "/apiv1/house/getFitmentFilter";
    //房屋来源
    public static final String GET_MENMBER_TYPE = HOST + "/apiv1/house/getMemberTypeNew";
    //房源特色
    public static final String GET_RENT_TRAITS = HOST + "/apiv1/house/getRentTraits";
    //感兴趣的房源
    public static final String GET_INSTERT_HOUSE = HOST + "/apiv1/sysite/viewInterstedSub";

    // 租房列表（极简模式）
    public static final String VIEW_RENT_OHTER = HOST + "/apiv1/sysite/viewRentList";

    /**
     * 商铺模块
     */
    //商铺列表
    public static final String GET_SHOP_LIST = HOST + "/apiv1/house/viewShops";
    //商铺成交列表
    public static final String DEAL_SHOP_LIST = HOST + "/apiv1/house/viewDealShops";
    //商铺类型
    public static final String FILTER_SHOP_TYPE = HOST + "/apiv1/house/getShopTypeFilter";
    //供求
    public static final String FILTER_SHOP_NEEDS = HOST + "/apiv1/house/getShopTypesFilter";
    //商铺详情页
    public static final String GET_SHOP_DETAIL = HOST + "/apiv1/sysite/viewShopDetail";
    //商铺经营行业
    public static final String FILTER_SHOP_INDUSTRY = HOST + "/apiv1/house/getMIndustryFilter";
    //商铺特色
    public static final String GET_SHOP_TRAITS = HOST + "/apiv1/house/getShopTraits";
    //是否可以分割
    public static final String FILTER_IS_CUT = HOST + "/apiv1/house/getIsCutFilter";
    //商铺总价
    public static final String GET_SHOP_PRICE = HOST + "/apiv1/house/shopPriceFilter1";
    //商铺面积
    public static final String GET_SHOP_AREA = HOST + "/apiv1/house/shopAreaFilter";
    //商铺租金
    public static final String GET_SHOP_RENTAL = HOST + "/apiv1/house/shopPriceFilter2";
    /**
     *资讯模块
     */
    //资讯列表
    public static final String GET_NEWS_LIST = HOST + "/apiv1/news/viewNews";
    //资讯详情页
    public static final String GET_NEWS_DETAIL = HOST + "/apiv1/news/viewNewsDetail";
    //资讯评论点赞
    public static final String NEWS_COMMENTS_AND_LIKE = HOST + "/apiv1/news/viewNewsCommentsAndLike";
    //查看推送资讯
    public static final String GET_PUSH_NEWS = HOST + "/apiv1/news/viewPushNews";
    //获取随机小二说房
    public static final String GET_RANDOM_AUDIO = HOST + "/apiv1/news/getRandomAudio";
    //视频列表页第一页
    public static final String GET_VIDEO_LIST_FIRST = HOST + "/apiv1/news/viewVideoList";
    //视频列表页第一页(新)
    public static final String NEW_VIDEO_LIST_FIRST = HOST + "/apiv1/news/viewNormalVideoIndex";
    //视频列表页分类页
    public static final String GET_VIDEO_LIST = HOST + "/apiv1/news/viewVideos";
    //新视频列表处理
    public static final String NEW_VIDEO_LIST = HOST + "/apiv1/news/viewNormalVideoList";
    //视频详情
    public static final String GET_VIDEO_DETAIL = HOST + "/apiv1/news/viewVideoDetail";
    //视频详情（新）
    public static final String NEW_VIDEO_DETAIL = HOST + "/apiv1/news/viewNormalVideoDetail";
    //房产快讯
    public static final String GET_FLASH_LIST = HOST + "/apiv1/news/viewHomeInfo";
    //资讯类别
    public static final String GET_NEWS_CATEGORY = HOST + "/apiv1/news/viewNewsFilter";
    //视频类别
    public static final String GET_VIDEOS_CATEGORY = HOST + "/apiv1/news/videoTypeFilter";
    //视频特色filter
    public static final String NEW_VIDEOS_FEATURE = HOST + "/apiv1/news/viewVideoFilter";
    //小二说房列表接口
    public static final String GET_AUDIOS_LIST = HOST + "/apiv1/news/viewAudios";
    //小二说房分类
    public static final String GET_AUDIOS_CATEGORYS = HOST + "/apiv1/news/viewCategorys";
    //租房区域快讯
    public static final String GET_RENTNEWS_LIST = HOST + "/apiv1/news/viewRentInfo";

    //经纪人资讯列表
    public static final String GET_AGENT_NEWS_LIST = HOST + "/apiv1/news/viewSecondNews";
    //经纪人资讯详情页
    public static final String GET_AGENT_NEWS_DETAIL = HOST + "/apiv1/news/viewAgentNewsDetail";
    /**
     * 免费发布
     */
    //房屋类别
    public static final String GET_SCONDHOUSE_TYPE = HOST + "/apiv1/house/houseTypeFilter";
    //类型
    public static final String GET_HOUSE_TYPE = HOST + "/apiv1/house/getResTypeFilter";
    //产权
    public static final String GET_HOUSE_PROPERTY = HOST + "/apiv1/house/getRightTypeFilter";
    //验证是否为会员
    public static final String CHECK_PHONE = HOST + "/apiv1/base/checkIsMember";
    //验证验证码
    public static final String CHECK_PASSCODE = HOST + "/apiv1/base/verifySmsCode";
    //查看个人房源数量
    public static final String CHECK_HOUSENUM = HOST + "/apiv1/house/getUserHouseNum";
    //发布二手房
    public static final String ADD_SECONDHOUSE = HOST + "/apiv1/house/addSaleHouse";
    //发布租房
    public static final String ADD_RENTHOUSE = HOST + "/apiv1/house/addRentHouse";
    //发布商铺
    public static final String ADD_SHOPS = HOST + "/apiv1/house/addShopsHouse";
    //购房攻略右侧猜你喜欢
    public static final String GUESS_LIKE = HOST + "/apiv1/sysite/viewEnjoy";
    //验证手机号码是否是会员
    public static final String CHECK_IS_MEMBER = HOST + "/apiv1/base/checkIsMember";
    //通过区域去的板块信息
    public static final String GET_PLATES_BY_REGION = HOST + "/apiv1/house/viewPlates";
    //获取房屋信息
    public static final String GET_HOUSE_ENTITY = HOST + "/apiv1/house/viewHouseEntity";
    //小二管家模块资讯列表 1-成交故事，2-看房团，3-节日福利
    public static final String VIEW_XENEWS = HOST + "/apiv1/other/viewXiaoerNews";
    //小二管家相关资讯详情页
    public static final String VIEW_XENEWSDETAIL = HOST +"/apiv1/other/viewXeNewsDetail";
    //教育地产
    public static final String SCHOOL_NEWS_HELP = HOST + "/apiv1/news/viewSchoolNews";
    /**
     * 小二管家
     */
    //积分商城列表
    public static final String GET_HOUSEKEEP_MALLLIST = HOST + "/apiv1/other/viewGoodShop";
    //积分商城分类
    public static final String FILTER_HOUSEKEEP_MALLTYPE = HOST + "/apiv1/other/getClassifyFilter";
    //积分商城详情
    public static final String GET_HOUSEKEEP_MALLDETAIL = HOST + "/apiv1/other/viewGood";
    //获奖用户
    public static final String GET_EXCHANGE_CUSTOMRE = HOST +  "/apiv1/other/viewExchangeCustomerlist";
    //小二管家
    public static final String GET_HOUSEKEEPER = HOST +  "/apiv1/dynatown/viewDynatowns";
    //添加订单
    public static final String ADD_GOODS_ORDER = HOST +  "/apiv1/other/addGoodsToOrder";
    //热卖排行
    public static final String HOT_RANKING  = HOST +  "/apiv1/sysite/viewHotRanking";
    //小二管家购房百问
    public static final String VIEW_XIAOER_HELPASK = HOST +"/apiv1/other/viewXiaoerHelpAsk";
    //小二管家团队（type 类型1成员 2 团队视频 3 看房团视频）
    public static final String VIEW_XIAOER_TEAM = HOST +"/apiv1/other/viewXiaoerTeam";
    //小二管家看房团
    public static final String VIEW_XIAOER_CONDOTOUR = HOST + "/apiv1/other/viewXiaoCondoTour";
    //小二管家成交故事
    public static final String VIEW_XIAOER_DEALSTORY = HOST + "/apiv1/other/viewXiaoerDealStory";
	//旅游地产预约人数&小二管家家庭数
	public static final String TOURIST_ORDER_COUNT = HOST + "/apiv1/tourist/viewTouristGuideCount";

    //库存列表&推送列表查询
    public static final String VIEW_HOUSEINFO_LIST = HOST + "/apiv1/house/viewHouseInfoList";
    //房源置顶
    public static final String ADD_STICKORDER = HOST + "/apiv1/house/addStickOrder";
    //房源置顶列表
    public static final String VIEW_STICKHOUSE_LIST = HOST + "/apiv1/house/viewStickOrderHousesUnionAll";
    //微信支付
    public static final String WX_PAY = HOST + "/apiv1/other/wxPay";
    //支付宝支付
    public static final String ALI_PAY = HOST + "/apiv1/other/aliPay";
    //日志查询
    public static final String VIEW_AGENTLOG = HOST + "/apiv1/other/viewAgentLogToPC";
    //取消置顶
    public static final String UPDATE_ISSTICK = HOST + "/apiv1/house/updateIsStick";
    //置顶房源预约刷新
    public static final String ADD_HOUSE_REFRESH = HOST + "/apiv1/house/addHouseRefresh";
    //立即置顶金额
    public static final String VIEW_STICK_TIME_MONEY_DISCOUNT = HOST + "/apiv1/house/viewStickTimeMoneyDiscount";
    //用户余额
    public static final String VIEW_BALANCE = HOST + "/apiv1/house/viewBalance";
    //计划置顶金额
    public static final String VIEW_REFRESH_STICK_TIME_MONEY_DISCOUNT = "/apiv1/house/viewRefreshStickTimeMoneyDiscount";
    //经纪人站首页展示房源总数
    public static final String VIEW_HOUSE_COUNT = HOST + "/apiv1/house/viewHouseCount";
    //签到
    public static final String ADD_PROCESS = HOST + "/apiv1/other/addProcess";
    //发布租房信息
    public static final String ADD_RENT_HOUSE = HOST + "/apiv1/house/addRentHouse";
    //二手房详情页
    public static final String VIEW_SALEHOUSE_DETAIL = HOST + "/apiv1/house/viewHouseDetail";
    //二手房详情页
    public static final String VIEW_SALEHOUSE_AGE = HOST + "/apiv1/house/getSubAge";
    //租房详情页
    public static final String VIEW_RENTHOUSE_DETAIL = HOST + "/apiv1/house/viewRentHouseDetail";

    //租房列表页
    public static final String VIEW_RENTHOUSE_LIST = HOST + "/apiv1/house/viewRentHouseList";
    //经纪人房源情况
    public static final String VIEW_AGENT_DETAIL = HOST + "/apiv1/house/viewAgentDetail";
    //商铺详情页
    public static final String VIEW_SHOP_DETAIL = HOST + "/apiv1/house/viewShopDetail";
    //商铺成交详情页
    public static final String DEAL_SHOP_DETAIL = HOST + "/apiv1/house/viewDealShopDetail";
    //房源详细信息
    //public static final String FIND_"/apiv1/house/viewHouseEntity"";  sessionId     houseId   houseType房源类型 1：二手房 ；2：租房；3：商铺
    public static final String FIND_HOUSE_ENTITY = HOST + "/apiv1/house/viewHouseEntity";
    //用户登录
    public static final String SIMPLE_LOGIN = HOST + "/apiv1/base/login";
    //短信验证码
    public static final String SMS_CODE = HOST + "/apiv1/other/sendCode";
    //用户注册
    public static final String QUICK_REGIST = HOST + "/apiv1/base/register";
    //查询项目相册
    public static final String VIEW_PROJECT_ALBUM = HOST + "/apiv1/house/viewProjectAlbum";
    //查询相册filter
    public static final String VIEW_ALBUM_COLUMN = HOST + "/apiv1/house/viewAlbumColumn";
    //查看项目评论列表
    public static final String VIEW_COMMENT_LIST = HOST + "/apiv1/other/viewComments";
    //查看项目评论详情
    public static final String VIEW_COMMENT_DETAIL = HOST + "/apiv1/other/viewCommentInfo";
    //查看评论相册
    public static final String VIEW_COMMENT_PICS = HOST + "/apiv1/other/viewCommentPics";

    // 找经纪人接口/apiv1/house/viewAgentList
    public static final String FIND_AGENT_LIST = HOST + "/apiv1/house/viewAgentList";
    // 找金牌经纪人接口
    public static final String FIND_GOLD_AGENT_LIST = HOST + "/apiv1/house/viewGoldenAgentList";
    //获取个人信息
    public static final String VIEW_MEMBER_INFO = HOST + "/apiv1/base/checkMemberInfo";
    //修改个人信息
    public static final String UPDATE_MEMBER_INFO = HOST + "/apiv1/base/updateMember";
    //查看小二管家列表和详情
    public static final String VIEW_SMALLBULSTER_LIST = HOST + "/apiv1/dynatown/viewDynatowns";
    //小二管家特价房源列表
    public static final String VIEW_ROOMSCOURES_LIST = HOST + "/apiv1/dynatown/viewRoomScoures";
    //小二管家特价房源详细页
    public static final String VIEW_ROOMSCOURES_DETAIL = HOST + "/apiv1/dynatown/viewRoomScoureDetail";
    //小二管家相关动态列表页
    public static final String VIEW_WAITERDYNATOWNS_LIST = HOST + "/apiv1/dynatown/viewWaiterDynatowns";
    //小二管家评论页
    public static final String VIEW_DYNATOWN_REVIEWS = HOST + "/apiv1/dynatown/viewReviews";
    //查看精装房详细页
    public static final String VIEW_DECORATE_DETAIL = HOST + "/apiv1/house/viewDecorateDetail";
    //查看精装房简介页
    public static final String VIEW_DECORATE_BRIEF = HOST + "/apiv1/house/viewDecorateBrief";
    //查看精装房详细页相册
    public static final String VIEW_DECORATE_ALBUM = HOST + "/apiv1/house/viewDecorateAlbum";
    //查看精装房简介相册
    public static final String VIEW_DECORATE_BRIEFALBUM = HOST + "/apiv1/house/viewRenovationAlbum";
    //精装房列表
    public static final String VIEW_DECORATE_HOUSES = HOST +"/apiv1/house/viewDecorateHouses";
    //查看单个活动
    public static final String VIEW_ACTIVITY = HOST + "/apiv1/active/viewActivity2";
    //查看新的优惠
    public static final String VIEW_ACTIVITY_NEW = HOST + "/apiv1/active/viewNewActivity";
    //新房区域filter
    public static final String NEWHOUSE_REGION_FILTER = HOST + "/apiv1/house/getRegionFilter";
    //户型filter
    public static final String FILTER_LAYOUT = HOST + "/apiv1/house/getRoomTypeFilter";
    //二手房新房预算
    public static final String BUDGET_FILTER = HOST + "/apiv1/house/getBudgetFilter";

    //楼盘搜索
    public static final String BASE_SEARCH = HOST + "/apiv1/base/search";

    //==========================================
    //模糊搜索小区名称
    public static final String SEARCH_RESIDENTIAL = HOST + "/apiv1/house/subByName";
    //分站siteMap
//    public static final String GET_GETGOLBALSITE_MAP =".fangxiaoer.com/apiGolbalsite/getGolbalSiteMap";
//    //分站城市
//    public static final String GET_GETCITYS = "http://dl.fangxiaoer.com/apiGolbalsite/getCitys";

    //写字楼列表页
    public static final String OFFICE_LIST = HOST +  "/apiv1/house/viewOffices";
    //写字楼详情页
    public static final String OFFICE_DETAIL = HOST +  "/apiv1/house/viewOffice";
    //写字楼出租/出售类型
    public static final String OFFICE_SALE_OR_RENT = HOST +  "/apiv1/house/officeFilter";
    //写字楼出售价格
    public static final String OFFICE_SALE_PRICE = HOST +  "/apiv1/house/officeSalePriceAsArray";
    //写字楼出租总价
    public static final String OFFICE_RENT_BIG_PRICE = HOST +  "/apiv1/house/officeRentPriceAsArray";
    //写字楼出租单价
    public static final String OFFICE_RENT_SMALL_PRICE = HOST +  "/apiv1/house/officeUnitPriceAsArray";
    //写字楼面积筛选
    public static final String OFFICE_AREA = HOST +  "/apiv1/house/officeAreaAsArray";
    //写字楼类型
    public static final String OFFICE_TYPE = HOST +  "/apiv1/house/officeTypeFilter";
    //写字楼装修
    public static final String OFFICE_FITMENT = HOST + "/apiv1/house/officeFitmentFilter";
    //写字楼特色
    public static final String OFFICE_TRAITS = HOST + "/apiv1/house/officeTraitFilter";
    //写字楼是否含物业费
    public static final String OFFICE_PROPERTY_PRICE = HOST +  "/apiv1/house/officeContainFilter";
    //写字楼付款方式
    public static final String OFFICE_PAYMENT = HOST +  "/apiv1/house/officePaymentFilter";
    //写字楼配置
    public static final String OFFICE_INDUSTRY = HOST +  "/apiv1/house/officeIndustryFilter";
    //写字楼录入
    public static final String ADD_OFFICE = HOST + "/apiv1/house/addShopsHouse";
    //写字楼项目列表页
    public static final String OFFICE_PROJECT_LIST = HOST + "/apiv1/house/viewOfficeDictionaryList";
    //写字楼项目详情页
    public static final String OFFICE_PROJECT_DRTAIL = HOST + "/apiv1/house/viewOfficeDictionaryDetail";
    //写字楼项目售价枚举
    public static final String OFFICE_PROJECT_SALEPRICE = HOST + "/apiv1/house/officeSaleUnitPriceAsArray";
    //写楼项目附近房源
    public static final String NEAR_OFFICE_PROJECT = HOST + "/apiv1/house/viewInteresteOfficeDictionary";

    //写字楼字典
    public static final String OFFICE_DICTIONARY = HOST + "/apiv1/house/viewOfficeDictionaryByName";

    public static final String VERIFY_IMAGE_CODE = HOST + "/apiv1/other/verifyImageCode";
    //查看成交详情页订单
    public static final String ADD_DEAL_DRTAIL_ORDER = HOST + "/apiv1/active/addDealHouseGuides";

    public static final String CACHE_COUNT = HOST + "/apiv1/other/cache/count";
    public static final String CACHE_DEL_OBJ = HOST + "/apiv1/other/cache/delcount";
    public static final String CACHE_SAVE_OBJ = HOST + "/apiv1/other/cache/saveobj";
    public static final String CACHE_READ_OBJ = HOST + "/apiv1/other/cache/readobj";
    /**
     * 房屋类型
     */
    public static final String JSON_HOUSE_TYPE = "[{\"id\":\"1\",\"name\":\"普宅\"},{\"id\":\"2\",\"name\":\"洋房\"},{\"id\":\"3\",\"name\":\"商铺\"},{\"id\":\"4\",\"name\":\"写字楼\"},{\"id\":\"5\",\"name\":\"别墅\"}]";
    public static final String JSON_FLOOR = "[{\"id\":\"1\",\"name\":\"1层\"},{\"id\":\"2\",\"name\":\"6层以下\"},{\"id\":\"3\",\"name\":\"6-12层\"},{\"id\":\"4\",\"name\":\"12层以上\"}]";
    public static final String JSON_DEPARTMENT_STYLE = "[{\"id\":\"1\",\"name\":\"现代简约\"},{\"id\":\"2\",\"name\":\"欧美风情\"},{\"id\":\"3\",\"name\":\"田园风格\"},{\"id\":\"4\",\"name\":\"中式古典\"},{\"id\":\"5\",\"name\":\"西式古典\"}]";
    public static final String JSON_SCD_SORT = "[{\"id\":\"0\",\"name\":\"默认排序\"},{\"id\":\"1\",\"name\":\"按更新时间排序\"},{\"id\":\"2\",\"name\":\"按总价从低到高\"},{\"id\":\"3\",\"name\":\"按总价从高到低\"},{\"id\":\"4\",\"name\":\"按面积从大到小\"},{\"id\":\"5\",\"name\":\"按面积从小到大\"},{\"id\":\"6\",\"name\":\"按单价从低到高\"},{\"id\":\"7\",\"name\":\"按单价从高到低\"}]";
    public static final String JSON_RENT_SORT = "[{\"id\":\"0\",\"name\":\"默认排序\"},{\"id\":\"1\",\"name\":\"按更新时间排序\"},{\"id\":\"2\",\"name\":\"按租金从低到高\"},{\"id\":\"3\",\"name\":\"按租金从高到低\"},{\"id\":\"4\",\"name\":\"按面积从小到大\"},{\"id\":\"5\",\"name\":\"按面积从大到小\"}]";
    //出租方式
    public static final String JSON_RENT_WAY = "[{\"id\":\"1\",\"name\":\"整租\"},{\"id\":\"2\",\"name\":\"合租\"}]";
    //租房
    public static final String JSON_RENT_ORDER = "[{\"id\":\"0\",\"name\":\"时间\"},{\"id\":\"1\",\"name\":\"时间\"},{\"id\":\"2\",\"name\":\"租金\"},{\"id\":\"3\",\"name\":\"租金\"},{\"id\":\"4\",\"name\":\"面积\"},{\"id\":\"5\",\"name\":\"面积\"}]";
    public static final String JSON_LAYOUT_HOUSE_TYPE = "[{\"id\":\"1\",\"name\":\"高层\"},{\"id\":\"2\",\"name\":\"小高层\"},{\"id\":\"3\",\"name\":\"多层\"},{\"id\":\"4\",\"name\":\"超高层\"},{\"id\":\"5\",\"name\":\"洋房\"},{\"id\":\"6\",\"name\":\"商铺\"}]";
    public static final String JSON_LAYOUT_ROOM_TYPE = "[{\"id\":\"1\",\"name\":\"一居\"},{\"id\":\"2\",\"name\":\"二居\"},{\"id\":\"3\",\"name\":\"三居\"},{\"id\":\"4\",\"name\":\"四居\"},{\"id\":\"5\",\"name\":\"五居\"}]";
    public static final String JSON_SUBWAY = "[{\"name\":\"地铁\",\"id\":\"1\"}]";
    public static final String JSON_PIECE  = "[{\"name\":\"全部\",\"id\":\"1\"}]";
    public static final String JSON_OFFICE_CHOOSE_PIECE  = "[{\"id\":\"1\",\"name\":\"总价\"},{\"id\":\"2\",\"name\":\"单价\"}]";
    public static final String JSON_SHOWVIEW  = "[{\"id\":\"1\",\"name\":\"竖版\"},{\"id\":\"2\",\"name\":\"横版\"}]";
    public static final String JSON_REGION_SUBWAY  = "[{\"id\":\"1\",\"name\":\"区域\"},{\"id\":\"2\",\"name\":\"地铁\"}]";
    public static final String GET_SITEMAP = HOST + "/apiv1/house/getSiteMap";
    public static final String JSON_PRICE_NEWHOUSE_SHOP  = "[{\"id\":\"1\",\"name\":\"10000以下\"},{\"id\":\"2\",\"name\":\"10000-12000\"},{\"id\":\"3\",\"name\":\"12000-15000\"},{\"id\":\"4\",\"name\":\"15000-20000\"},{\"id\":\"5\",\"name\":\"20000以上\"}]";
    public static final String JSON_SUB_SORT = "[{\"id\":\"0\",\"name\":\"二手房排序\"},{\"id\":\"3\",\"name\":\"涨跌幅降序\"},{\"id\":\"4\",\"name\":\"涨跌幅升序\"},{\"id\":\"5\",\"name\":\"均价降序\"},{\"id\":\"6\",\"name\":\"均价升序\"}]";
    //增加二手房视频看房+优质好房筛选
    public static final String JSON_VIDEO_GOOD_HOUSE = "[{\"id\":\"1\",\"name\":\"全部\"},{\"id\":\"2\",\"name\":\"视频看房\"},{\"id\":\"3\",\"name\":\"优质好房\"}]";
    //视频看房&全景看房
    public static final String JSON_VIDEO_PROJECT = "[{\"id\":\"1\",\"name\":\"视频看房\"},{\"id\":\"2\",\"name\":\"全景看房\"},{\"id\":\"3\",\"name\":\"直播看房\"}]";
    //是否新房
    public static final String JSON_ISNEWHOUSE = "[{\"id\":\"1\",\"name\":\"新房\"}]";
    //是否独家房源
    public static final String JSON_COOPERATION = "[{\"id\":\"1\",\"name\":\"独家房源\"}]";
    //是否小二甄选
    public static final String JSON_SELECTION = "[{\"id\":\"1\",\"name\":\"小二甄选\"}]";
    //求租求购 新房 首付预算
    public static final String DEMAND_NEW_HOUSE_BUDGET = "[{\"id\":\"1\", \"name\":\"50万以下\"},{\"id\":\"2\", \"name\":\"50-80万\"},{\"id\":\"3\", \"name\":\"80-100万\"}]";
    public static final String DEMAND_SECOND_HOUSE_BUDGET = "[{\"id\":\"1\", \"name\":\"50万以下\"},{\"id\":\"2\", \"name\":\"50-80万\"},{\"id\":\"3\", \"name\":\"80-100万\"},{\"id\":\"4\", \"name\":\"100-120万\"},{\"id\":\"5\", \"name\":\"120-150万\"},{\"id\":\"6\", \"name\":\"150-200万\"},{\"id\":\"7\", \"name\":\"200-300万\"},{\"id\":\"8\", \"name\":\"300万以上\"}]";
    //求租求购 租房 租房方式
    public static final String DEMAND_RENT_HOUSE_DEMAND_TYPE = "[{\"id\":\"4\", \"name\":\"整租\"},{\"id\":\"5\", \"name\":\"合租\"}]";
    //求租求购 商铺 供求
    public static final String DEMAND_SHOP_DEMAND_TYPE = "[{\"id\":\"1\", \"name\":\"求购\"},{\"id\":\"2\", \"name\":\"求租\"},{\"id\":\"3\", \"name\":\"求兑\"}]";
    //求租求购 商铺 供求
    public static final String DEMAND_OFFICE_DEMAND_TYPE = "[{\"id\":\"1\", \"name\":\"写字楼求购\"},{\"id\":\"2\", \"name\":\"写字楼求租\"}]";

    /**
     * vip支付相关
     */
    //.vip会员支付金额--/apiv1/other/getVipPayMoney
    public static final String GET_VIP_PAY_MONEY = HOST + "/apiv1/other/getVipPayMoney";

    //vip会员支付ForSy站--/apiv1/other/vipPayForPC
    public static final String PAY_VIP = HOST + "/apiv1/other/vipPayForPC";
    //获取订单状态接口    /apiv1/house/viewStickStatus
    public static final String VIEW_STICK_STATUS = HOST + "/apiv1/house/viewStickStatus";

    /**
     * 二手房组  接口
     */
    //成交排行榜接口/apiv1/house/ viewDealRankingList接口
    public static final String VIEW_DEAL_RANKING_LIST = HOST + "/apiv1/house/viewDealRankingList";
    //小区问答列表---我的问答 type4 PageSize=1 page=1
    public static final String INTERLOCUTION_LIST = HOST + "/apiv1/house/viewMyNewAsk";

    //有新回答接口/apiv1/askremind/getMyAnswerRemindList
    public static final String GET_MY_ANSWER_REMIND_LIST = HOST + "/apiv1/other/getMyAnswerRemindList";
    //更改已读/apiv1/askremind/updateMyAskMind
    public static final String UPDATE_MY_ASK_MIND = HOST + "/apiv1/other/updateMyAskMind";

    //小二管家列表接口
    public static final String  VIEW_XIAOER_LIST = HOST +"/apiv1/other/viewXiaoerInfo";
    //    小区问答采纳 --/apiv1/other/acceptAskReplay
    public static final String PLOT_ACCEPT_REPLAY = HOST + "/apiv1/other/acceptAskReplay";
    //小区价格枚举--/apiv1/house/getSubPriceFilter
    public static final String GET_SUB_PRICE_FILTER = HOST + "/apiv1/house/getSubPriceFilter";

    //房价查询页面接口
    public static final String  VIEW_HOUSE_PRICE_LIST = HOST +"/apiv1/house/viewSubForPriceMap";
    //房价查询地图相关接口
    public static final String  VIEW_HOUSE_PRICE_MAP = HOST +"/apiv1/house/viewSubPriceMap";
    //房价热门小区
    public static final String  VIEW_HOT_SUB_LIST = HOST +"/apiv1/house/viewHotSubRank";
    //房价计算详情页
    public static final String  VIEW_SUB_MATH = HOST +"/apiv1/house/calculatePrice";
    //品牌物业
    public static final String VIEW_SUB_BRAN_PROPERTY = HOST +"/apiv1/other/viewPropertyFilter";
    //小二管家主页接口
    public static final String  VIEW_XIAOER_INDEX = HOST +"/apiv1/other/viewXiaoerIndex";
    //小二管家成交故事列表
    public static final String  VIEW_XIAOER_DEALSTORIES = HOST +"/apiv1/other/viewXiaoerNews";
    //小二管家相关资讯详情页
    public static final String  VIEW_XIAOER_FESTIVALSTORIES = HOST +"/apiv1/other/viewXeNewsDetail";
    //首页友情链接
    public static final String  INDEX_FRIEND_LINKS = HOST +"/apiv1/sysite/viewFriendsLinks";

    //求租求购列表
    public static final String VIEW_NEED_HOUSES = HOST +"/apiv1/house/viewDemandHouseList";
    //求购价格枚举
    public static final String VIEW_NEED_SECOND_PRICE = HOST +"/apiv1/house/getDemandHouseBudgetFilter";
    //求租价格枚举
    public static final String VIEW_NEED_RENT_PRICE = HOST +"/apiv1/active/getRentFilter";
    //求租求购详情
    public static final String VIEW_NEED_HOUSE = HOST +"/apiv1/house/viewDemandHouse";
    //免费服务首页
    public static final String VIEW_FREE_INDEX =  HOST + "/apiv1/other/viewFreeIndex";
    //免费服务公司详情页
    public static final String VIEW_FREE_COMPANY =  HOST + "/apiv1/other/viewCompanyIndex";
    //免费服务提交订单
    public static final String SAVE_FREESERVICE_ORDER =  HOST + "/apiv1/other/freeServiceOrder";
    //免费服务获取公司服务类别
    public static final String GET_COMPANY_SERVICETYPES =  HOST + "/apiv1/other/viewMainServiceFilter";
    // 二手房地图找房搜索
     public static final String QUERY_SEARCH_TITLE_SECOND_MAP =  HOST + "/apiv1/house/querySunInfoByTitle";
    // 地图搜索查询匹配词的内容(二手房地图)--/apiv1/house/querySunOneInfoByTitle
    public static final String QUERY_SEARCH_OEN_INFO_SECONDMAP =  HOST + "/apiv1/house/querySunOneInfoByTitle";
     // 二手房地图找房右侧地图(2018.11.09)
     public static final String QUERY_SECONDMAP =  HOST + "/apiv1/house/countSaleHouse";
    // 二手房地图找房左侧列表(2018.11.09)
    public static final String QUERY_SECONDMAP_LEFTLIST =  HOST + "/apiv1/house/viewMapSaleHouses";
    //im即时通讯
    //加好友
    public static final String IM_ADD_FRIEND =  HOST + "/apiv1/im/friendAdd";
    //发送普通消息
    public static final String IM_SEND_MSG =  HOST + "/apiv1/im/sendMsg";
    //查询目标联系人id
    public static final String IM_GET_ACCID =  HOST + "/apiv1/im/getAccid";
    public static final String IM_CHECK_ACCID =  HOST + "/apiv1/im/checkAccid";
    //通过自己sessionid查询自己的accid和token
    public static final String IM_MY_ACCID =  HOST + "/apiv1/im/getImUserInfo";

    //获取经纪人服务标签
    public static final String GET_AGENT_LABEL =  HOST + "/apiv1/base/getAgentServiceTags";

    // 租房地图找房搜索
    public static final String QUERY_SEARCH_TITLE_RENT_MAP =  HOST + "/apiv1/house/querySunInfoByTitle";
    // 地图搜索查询匹配词的内容(租房地图)--/apiv1/house/querySunOneInfoByTitle
    public static final String QUERY_SEARCH_OEN_INFO_RENTMAP =  HOST + "/apiv1/house/querySunOneInfoByTitle";
    // 租房地图找房右侧地图(2018.11.09)
    public static final String QUERY_RENTMAP =  HOST + "/apiv1/house/countRentHouse";
    // 租房地图找房左侧列表(2018.11.09)
    public static final String QUERY_RENTMAP_LEFTLIST =  HOST + "/apiv1/house/viewMapRentHouses";



    //店长招聘列表（ 经纪人店长招聘2018.11.20）
    public static final String GET_AGENT_RECRUIT_LIST =  HOST + "/apiv1/other/searchCompanyJob";
    //店长招聘 经纪人公司列表(展示公司logo)
    public static final String GET_AGENT_COMPANYS =  HOST + "/apiv1/other/searchAgentCompanys";
    //店长招聘列表右侧岗位急聘广告
    public static final String GET_AGENT_RECRUIT_URGENT =  HOST + "/apiv1/other/getCompanyUrgentJobs";
    //店长招聘 职位filter
    public static final String GET_AGENT_RECRUIT_JOBTYPE =  HOST + "/apiv1/other/jobTypeFilter";
    //店长招聘 薪资filter`
    public static final String GET_AGENT_RECRUIT_SALARY =  HOST + "/apiv1/other/jobsalaryRangeFilter";
    //店长招聘 工作经验filter
    public static final String GET_AGENT_RECRUIT_EXPERIENCE =  HOST + "/apiv1/other/workingLifeFilter";
    //店长招聘 福利filter
    public static final String GET_AGENT_RECRUIT_WELFARE =  HOST + "/apiv1/other/jobWelfareFilter";
    //店长招聘 申请职位
    public static final String GET_AGENT_RECRUIT_APPLY =  HOST + "/apiv1/other/postAgentJobResumes";
    //店长招聘 查看公司详情
    public static final String GET_AGENT_RECRUIT_COMPANY_DETAILS =  HOST + "/apiv1/other/getAgentCompanyById";
    //店长招聘 查看职位详情
    public static final String GET_AGENT_RECRUIT_JOB_DETAILS =  HOST + "/apiv1/other/getAgentCompanyJobById";
    //房产直聘 右侧广告位
    public static final String GET_RIGHT_ADVERTISEMENT =  HOST + "/apiv1/active/viewAdvertisement";

    //小登出
    public static final String ALL_LOGOUT =  HOST + "/apiv1/base/logout";
    //通过地址获取百度地图经纬度信息
    public static final String GET_LOCATION_BY_ADDRESS = HOST +  "/apiv1/house/queryLocationByAddress";
    //根据小区名称展示小区专家
    public static final String QUERY_PLOT_EXPERT =  HOST + "/apiv1/house/querySubAgentByOneSerachName";
    //根据搜索关键字或区域展示商铺、写字楼小区专家
    public static final String QUERY_SHOP_EXPERT =  HOST + "/apiv1/house/queryShopsExperts";

    //获取项目类型
    public static final String GET_PROJECT_TYPE = HOST + "/apiv1/house/fetchProjectType";

    //机构店铺相关过滤条件接口
    public static final String GET_COMPANY_SHOP_SECOND_HOUSE_FILTERS = HOST + "/apiv1/house/getCompanySecondHouseFilter";
    public static final String GET_COMPANY_SHOP_RENT_HOUSE_FILTERS = HOST + "/apiv1/house/getCompanyRentHouseFilter";
    public static final String GET_COMPANY_SHOP_SHOPS_HOUSE_FILTERS = HOST + "/apiv1/house/getCompanyShopHouseFilter";
    public static final String GET_COMPANY_SHOP_OFFICE_HOUSE_FILTERS = HOST + "/apiv1/house/getCompanyOfficeHouseFilter";
    /**
     * 经纪人中介管理
     */
    //根据经纪人id 获取经纪人所在中介
    public static final String QUERY_AGENT_INTERMEDIARY =  HOST + "/apiv1/house/queryAgentCompanyId";
    //根据经纪人所在中介id 获取中介店铺信息
    public static final String QUERY_AGENT_COMPANY_INFO =  HOST + "/apiv1/house/queryAgentCompanyFromAgent";
    // 获取中介店铺二手房房源
    public static final String QUERY_COMPANY_SECOND =  HOST + "/apiv1/house/viewAgentCompanyHouses";
    //根据经纪人所在中介id 获取公司精英顾问
    public static final String QUERY_ELITE_CONSULTANT =  HOST + "/apiv1/house/queryCompanyAgentList";
    //根据公司id 获取公司其他门店
    public static final String QUERY_OTHER_COMPANY =  HOST + "/apiv1/house/viewOtherAgentCompany";

    /**
     * 旅居地产
     */
    //旅游地产首页广告位
    public static final String TOURIST_INDEX_AD = HOST + "/apiv1/tourist/viewTouristAdvertisement";
    //旅游地产列表页
    public static final String TOURIST_INDEX = HOST + "/apiv1/tourist/realEstate";
    //旅游地产首页资讯首条
    public static final String TOURIST_INDEX_NEWS = HOST + "/apiv1/tourist/viewSyNewsInfo";
    //旅游地产资讯列表页
    public static final String TOURIST_NEWS_LIST = HOST + "/apiv1/tourist/viewNewsInfoList";
    //旅游地产资讯filter
    public static final String TOURIST_NEWS_FILTER = HOST + "/apiv1/tourist/viewNewsInfoFilter";
    //旅游地产项目信息
    public static final String TOURIST_PROJECT_DETAIL = HOST + "/apiv1/tourist/touristProjectDetail";
    //普通类型户型filter
    public static final String TOURIST_LAY1_FILTER = HOST + "/apiv1/tourist/viewNormalLayoutFilter";
    //其他类型户型filter
    public static final String TOURIST_LAY2_FILTER = HOST + "/apiv1/tourist/viewOtherLayoutFilter";
    //户型信息块filter
    public static final String TOURIST_LAYDETAIL_FILTER = HOST + "/apiv1/tourist/viewLayoutFilter";
    //户型详情模块
    public static final String TOURIST_LAYOUT_DETAIL = HOST + "/apiv1/tourist/viewLayoutDetail";
    //旅游地产资详情页
    public static final String TOURIST_NEWS_DETAIL = HOST + "/apiv1/tourist/viewNewsDetail";
    //旅游基本信息
    public static final String TOURIST_PROJECT_INFO = HOST + "/apiv1/tourist/viewTouristProjectInfo";
    //旅居地产简版信息
    public static final String TOURIST_PROJECT_BRIEF = HOST + "/apiv1/tourist/touristProjectBrief";
    //咨询列表
    public static final String TOURIST_ASK_LIST = HOST + "/apiv1/tourist/viewTouristAsks";
    //旅游动态页
    public static final String TOURIST_PROJECT_DY = HOST + "/apiv1/tourist/viewTouristInfo";
    //旅游动态页Filter
    public static final String TOURIST_PROJECT_DY_FILTER = HOST + "/apiv1/tourist/touristInfoFilter";
    //旅游地产相册详情
    public static final String TOURIST_PHOTOS_DETAIL = HOST + "/apiv1/tourist/viewAlbumByProject";
    //旅游地产相册页Filter
    public static final String TOURIST_PHOTOS_FILTER = HOST + "/apiv1/tourist/viewPhotoFilter";
    //评价列表
    public static final String TOURIST_COMMENT_LIST = HOST + "/apiv1/tourist/viewTouristComments";
    //评论详情
    public static final String TOURIST_COMMENT_DETAIL = HOST + "/apiv1/tourist/viewTouristCommentDetail";
    //评论相册
    public static final String TOURIST_COMMENT_PICS = HOST + "/apiv1/tourist/viewTouristCommentPics";
    //添加旅居地产评论
    public static final String ADD_TOURIST_COMMENT = HOST + "/apiv1/tourist/commentTourist";
    //旅游地产城市FILTER
    public static final String TOURIST_CITY = HOST + "/apiv1/tourist/touristCity";
    //旅游地产订单
    public static final String TOURIST_ADD_ORDER = HOST + "/apiv1/tourist/saveTouristOrder";
    //旅游地产问答
    public static final String ADD_TOURIST_ASK = HOST + "/apiv1/tourist/askTourist";

    //点赞
    public static final String NEWS_COMMENT_LIKE = HOST + "/apiv1/news/setNewsLike";
    //添加资讯评论
    public static final String ADD_NEWS_COMMENT = HOST + "/apiv1/news/addNewsComment";
    //添加资讯回复
    public static final String ADD_NEWS_REPLY = HOST + "/apiv1/news/addNewsReply";
    //评论点赞功能
    public static final String ADD_NEWS_COMMENT_LIKE = HOST + "/apiv1/other/addNewsCommentLike";

    public static final String UPLOAD_NEW_PHOTO = HOST + "/apiv1/base/upload";

    public static final String UPLOAD_NEW_PHOTOS = HOST + "/apiv1/base/newuploadpic";
    /**
     * 二手房地铁地图
     */
    //搜索框 关键字匹配查询
    public static final String SEARCH_FOR_SUB_TITLE = HOST + "/apiv1/other/querySubwaySubdistricts";
    //地铁地图站点、小区数据
    public static final String SEC_STATION_INFO = HOST + "/apiv1/other/viewSubFromBond";
    //租房地图右侧信息接口
    public static final String RENT_MAP_DUAL_INFO = HOST + "/apiv1/other/viewRentSubFromBond";

    //新房常搜提示
    public static final String USUALLY_SEARCH_PROJECT = HOST + "/apiv1/house/usuallySearchProject";
    //机构店铺 推荐二手房
    public static final String AGENT_RECOMMEND_SALE_INFO = HOST + "/apiv1/house/viewPushSaleHouseList";
    //机构店铺 推荐租房
    public static final String AGENT_RECOMMEND_RENT_INFO = HOST + "/apiv1/house/viewPushRentHouseList";
    //机构店铺 推荐商铺
    public static final String AGENT_RECOMMEND_SHOP_INFO = HOST + "/apiv1/house/viewPushShopList";
    //机构店铺 推荐写字楼
    public static final String AGENT_RECOMMEND_OFFICE_INFO = HOST + "/apiv1/house/viewPushOfficeList";

    //楼盘对比
    public static final String VIEW_PROJECT_CONTRAST = HOST + "/apiv1/house/viewSimilarProjectDetail";
    //查看对比房源
    public static final String VIEW_PROJECT_CONTRASTS = HOST + "/apiv1/house/viewSimilarProjects";


    public static final String VIEW_COLLECTION = HOST + "/apiv1/active/correctionInfo";

    //经纪人资讯类别
    public static final String GET_AGENT_NEWS_CATEGORY = HOST + "/apiv1/news/viewSecondNewsFilter";
    //获取微信小程序码用来打电话
    public static final String GET_WX_A_CODE = HOST + "/apiv1/other/getWxacode";
    //二手房小程序码扫码拨打电话
    public static final String GET_WX_SEC_CODE = HOST + "/apiv1/other/getSecondWxCode";
    //新房小程序
    public static final String GET_WX_NEW_CODE = HOST + "/apiv1/other/getNewWxCode";
    //查看订单详情
    public static final String NEWHOUSE_ORDER_DETAIL = HOST + "/apiv1/other/viewEarnestOrderDetail";


    // 新房认购->获取建筑类型
    public static final String NEWHOUSE_GET_BUILDTYPE = HOST + "/apiv1/other/viewBuildTypeForPay";
    // 新房认购->获取户型
    public static final String NEWHOUSE_GET_LAYOUT = HOST + "/apiv1/other/viewLayoutByBuildType";
    // 新房认购->获取楼号
    public static final String NEWHOUSE_GET_BUILDNAME = HOST + "/apiv1/other/viewByProjectAndBuildType";
    // 新房认购->获取单元号
    public static final String NEWHOUSE_GET_UNIT = HOST + "/apiv1/other/viewUnitByProjectIdAndBuild";
    // 新房认购->获取房号
    public static final String NEWHOUSE_GET_ROOM = HOST + "/apiv1/other/viewProjectRoomFilter";
    // 新房认购->下单
    public static final String NEWHOUSE_CREAT_ORDER = HOST + "/apiv1/other/createPayProjectOrder";
    // 新房认购->支付逻辑
    public static final String NEWHOUSE_PROJECT_PAY = HOST + "/apiv1/other/payForProject";
    // 新房认购->支付成功回调
    public static final String PAY_FOR_SUCCESS = HOST + "/apiv1/other/checkOrderId";

    // 户型对比功能
    public static final String LAYOUT_CONTRAST = HOST + "/apiv1/active/viewLayoutForCompare";
    // 户型对比推荐户型
    public static final String LAYOUT_CONMMED = HOST +"/apiv1/house/getRecommendLayout";
    //户型收藏操作方案
    public static final String ADD_LAYOUT_FAVORITE = HOST + "/apiv1/active/addLayoutFavorite";
    public static final String CHECK_LAYOUT_FAVORITE = HOST + "/apiv1/active/checkLayoutFavorite";
    public static final String CANCEL_LAYOUT_FAVORITE = HOST + "/apiv1/active/cancelLayoutFavorite";

    //获取配置信息
    public static final String GET_KEY_VALUE_CONFIG = HOST + "/apiv1/base/viewConfigByKey";

    //优秀经纪人风云榜
    public static final String MEMBET_TOP_LIST = HOST + "/apiv1/other/memberTop";
    //优秀经纪人风云榜区域filter
    public static final String VIEW_REGION_FILTER_FOR_TOPLIST = HOST + "/apiv1/house/regionByTopList";
    //房产直聘获取会员已保存简历
    public static final String VIEW_COMMONMEMBER_RESUME = HOST + "/apiv1/other/getCommonMemberResume";
    //房产直聘添加会员简历
    public static final String ADD_COMMONMEMBER_RESUME = HOST + "/apiv1/other/addCommonMemberResume";
    //房产直聘编辑会员简历
    public static final String SET_COMMONMEMBER_RESUME = HOST + "/apiv1/other/setCommonMemberResume";
    //获取学历要求Filter
    public static final String FILTER_RESUME_EDUCATION = HOST + "/apiv1/other/educationFilter";
    //获取入职类型Filter Bowen
    public static final String FILTER_RESUME_INDUCTION = HOST + "/apiv1/other/inductionFilter";

    public static final String SALE_OTHER_FILTER = HOST + "/apiv1/active/saleOthersFilter";

    public static final String SALE_INFO_FILTER = HOST + "/apiv1/active/saleInfoFilter";

    public static final String RENT_INFO_FILTER = HOST + "/apiv1/active/rentInfoFilter";

    public static final String SHOP_INFO_FILTER = HOST + "/apiv1/active/shopInfoFilter";

    public static final String SALE_FEATURE_FILTER = HOST + "/apiv1/active/saleFeaturesFilter";

    public static final String RENT_FEATURE_FILTER = HOST + "/apiv1/active/rentFeaturesFilter";
    //抢优惠-获取补贴项目情况
    public static final String VIEW_SUBSIDY_FOR_PROJECT = HOST + "/apiv1/active/viewSubsidyForProject";
    //抢优惠-领取项目补贴
    public static final String FETCH_SUBSIDY_FOR_PROJECT = HOST + "/apiv1/active/fetchSubsidyForProject";
    //保存真房源
    public static final String PUBLISH_REAL_ESTATE = HOST + "/apiv1/house/saveOrUpdateRealEstate";
    //查询真房源情况
    public static final String CHECK_RAEL_ESTATE = HOST + "/apiv1/house/checkRealEstate";
    //查询真房源情况
    public static final String GET_REAL_ESTATE_INFO = HOST + "/apiv1/house/getRealEstateInfo";
    //获取关系
    public static final String RELATIONSHIP_FILTER = HOST + "/apiv1/house/getRelationShipFilter";

    public static final String CALCULATION_FOR_LOAN = HOST + "/apiv1/other/loanWithLPR";

    public static final String LPR_LOAN_RATE = HOST + "/apiv1/other/getLPRLoanRate";

    public static final String LPR_HISTORY = HOST + "/apiv1/other/viewLPRHistoryInfo";
    // 项目销售动态
    public static final String NEW_PROJECT_DYNAMIC_LIST = HOST + "/apiv1/house/fetchNewProjectDynamicList";
    // OCR识别身份证
    public static final String CHECK_OCR_ID_CARD = HOST + "/apiv1/other/checkOCRIDCard";
    // 身份认证
    public static final String MEMBER_ID_CARD_AUTHENTICATION = HOST + "/apiv1/other/memberIdCardAuthentication";
    // 用户确认身份-可用
    public static final String CONFIRM_AUTHENTICATION_INFO = HOST + "/apiv1/other/confirmAuthenticationInfo";
    // 甄选-测评打分
    public static final String SELECTION_PROJECT_INFO = HOST + "/apiv1/sysite/selectionProjectInfo";
    //灰色主题
    public static final String GRAY_THEME = HOST + "/apiv1/other/grayTheme";
    //推荐经纪人处理
    public static final String RECOMMAND_YI_AGENT = HOST + "/apiv1/yisite/viewRecommandYiAgent";
    //求租求购列表
    public static final String GET_HOUSE_DEMAND_ORDER_LIST = HOST + "/apiv1/house/getHouseDemandOrderList";

    //微赞直播列表
    public static final String WEIZAN_LIVE_LIST = HOST + "/apiv1/news/weizanList";

    //店铺个人主页面-获取用户店铺机构首页信息
    public static final String GET_AGENTINTERMEDIARY_INDEX = HOST + "/apiv1/agentIntermediary/getAgentIntermediaryIndex";
    //店铺机构主页面-根据经纪人所在中介id 获取公司精英顾问
    public static final String QUERY_ELITE_CONSULTANT_NEW = HOST + "/apiv1/agentIntermediary/queryCompanyAgentListNew";
}

