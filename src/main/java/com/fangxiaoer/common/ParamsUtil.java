package com.fangxiaoer.common;

import com.fangxiaoer.model.NameIdBean;
import com.fangxiaoer.model.UrlBean;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ParamsUtil {

    private static HashMap<String, ArrayList<NameIdBean>> CACHE = new HashMap<>();

    public static ArrayList<NameIdBean> getFilters(String urlOrJson) {
        return getFilters(urlOrJson, null, true);
    }

    public static ArrayList<NameIdBean> getFilters(String urlOrJson, HashMap<String, Object> params, boolean cacheable) {
        Gson gson = new Gson();
        String key = Utils.MD5encode(urlOrJson);
        if (params != null) {
            key += Utils.MD5encode(gson.toJson(params));
        }
        if (CACHE.containsKey(key) && cacheable) {
            return CACHE.get(key);
        }
        ArrayList<NameIdBean> result;
        if (urlOrJson.startsWith("http")) {
            HashMap<String, Object> resultMap = HttpUtil.connectApi(urlOrJson, params);
            result = gson.fromJson(gson.toJson(resultMap.get("content"))
                    .replaceAll("regionId", "id").replaceAll("regionName", "name")
                    .replaceAll("stationId", "id").replaceAll("stationName", "name")
                    .replaceAll("subWayId","id").replaceAll("subWayName","name"), new TypeToken<ArrayList<NameIdBean>>() {
            }.getType());
        } else {
            result = gson.fromJson(urlOrJson, new TypeToken<ArrayList<NameIdBean>>() {
            }.getType());
        }
        if (result == null) result = new ArrayList<>();
        NameIdBean bean = new NameIdBean();
        bean.setId("");
        bean.setName("全部");
        result.add(0, bean);
        if (cacheable) CACHE.put(key, result);
        return result;
    }

    /**
     * 抢礼物区域动态url生成
     */
    public static ArrayList<UrlBean> activityUrl(String input, String head, String startKey) {
        Gson gson = new Gson();
        HashMap<String, Object> resultMap = HttpUtil.connectApi(Constants.FILTER_ACTIVITIES_REGION, new Params("projectType", input).get());
        ArrayList<RegionBean> content = gson.fromJson(gson.toJson(resultMap.get("content")), new TypeToken<ArrayList<RegionBean>>() {
        }.getType());
        RegionBean regionBean = new RegionBean();
        regionBean.setRegionId("25");
        regionBean.setRegionName("全部");
        content.add(0, regionBean);
        ArrayList<UrlBean> contentNew = new ArrayList<>();
        for (RegionBean item : content) {
            UrlBean urlBean = new UrlBean();
            urlBean.setId(item.getRegionId());
            urlBean.setName(item.getRegionName());
            urlBean.setUrl(head + "h" + input + "-" + startKey + item.getRegionId());
            contentNew.add(urlBean);
        }
        return contentNew;
    }

    /**
     * 此处可以自动装载各种筛选项,但需满足一下条件
     * 1.filterUrl不为空,可以匹配正确的单个筛选项并装载
     * 2.targetUrl不为空,但是filterUrl是空,可以自动装载所有targetUrl配置过的筛选
     *
     * @param targetUrl
     * @param model
     * @param baseUrl
     * @param params
     */
    public static void addFilterIntoModel(String targetUrl, Model model, String baseUrl, String params) {
        addFilterIntoModel(targetUrl, model, baseUrl, params, null);
    }

    public static void addFilterIntoModel(String targetUrl, Model model, String baseUrl, String params, HashMap<String, String> dependencyParams) {
        if (StringUtils.isEmpty(targetUrl)) return;
        for (ParamsConfig config : CONFIG_MAPPING.get(targetUrl)) {
            addFilterIntoModel(config, model, baseUrl, params, dependencyParams);
        }
    }

    private static void addFilterIntoModel(ParamsConfig config, Model model, String baseUrl, String params, HashMap<String, String> dependencyParams) {
        if(config == null) return;
        Params myParams = new Params();
        if(config.dependency != null) {
            if(dependencyParams == null) return;
            for(String key : config.dependency) {
                if(!StringUtils.isEmpty(dependencyParams.get(key))) {
                    myParams.add(key, dependencyParams.get(key)).get();
                }
            }
            if(myParams.get().size() == 0) return;
        }
        if(!StringUtils.isEmpty(config.filterUrl)) {
            ArrayList<NameIdBean> nameIdBeen = ParamsUtil.getFilters(config.filterUrl, myParams.get(), config.cacheable);
            ArrayList<UrlBean> urlBeen = ParamsUtil.generateItem(params, nameIdBeen, config.shortKey, baseUrl, config.conflict);
            model.addAttribute(config.modelKey, urlBeen);
        }
    }

    public static void addSeoTitle(Model model, String exclude) {
        String seoTitle = "";
        List<String> excludes = Arrays.asList(exclude.split(","));
        Map<String, Object> params = model.asMap();
        for(Map.Entry<String, Object> param : params.entrySet()) {
            if(excludes.contains(param.getKey())) continue;
            if(param.getValue() instanceof ArrayList) {
                if(((ArrayList) param.getValue()).size() > 0 && ((ArrayList) param.getValue()).get(0) instanceof UrlBean) {
                    ArrayList<UrlBean> urlBeen = (ArrayList<UrlBean>) param.getValue();
                    for(UrlBean item : urlBeen) {
                        if(item.getSelected() && "全部".equals(item.getName())) {
                            break;
                        } else if (item.getSelected() && !"全部".equals(item.getName())) {
                            seoTitle += item.getName();
                        }
                    }
                }
            }
        }
        model.addAttribute("seoTitle", seoTitle);
    }

    public static HashMap<String, ParamsConfig[]> CONFIG_MAPPING = new HashMap<>();
    static {
        CONFIG_MAPPING.put(Constants.MEMBET_TOP_LIST, new ParamsConfig[]{
           new ParamsConfig(Constants.VIEW_REGION_FILTER_FOR_TOPLIST,"region","regionID","r")
        });
        CONFIG_MAPPING.put("common", new ParamsConfig[] {
                new ParamsConfig("", "", "page", "n"),
                new ParamsConfig("", "", "orderKey","o")
        });
        CONFIG_MAPPING.put("other", new ParamsConfig[]{
                new ParamsConfig("", "", "existMonth", "m")
        });
        CONFIG_MAPPING.put(Constants.VIEW_PROJECT_LIST, new ParamsConfig[] {
                new ParamsConfig(Constants.FILTER_NEWHOUSE_REGION, "region", "regionId", "r").withConflict("l","s","k"),
                new ParamsConfig(Constants.FILTER_SUBWAY_LINE, "subway", "subWayId", "l").withConflict("s","r"),
                new ParamsConfig(Constants.FILTER_SUBWAY_STATIONS, "subwayStation", "subWayStationId", "s").withDependency("subWayId"),
                new ParamsConfig(Constants.FILTER_NEWHOUSE_PRICE, "price", "moneyId", "p").withConflict("lp","hp","bp"),
                new ParamsConfig(Constants.FILTER_LAYOUT, "layout", "roomType", "t"),
                new ParamsConfig("", "search", "projectName", "search"),
                new ParamsConfig(Constants.FILTER_LOOP_LINE, "loop", "loopline", "lo"),
                new ParamsConfig(Constants.JSON_SUBWAY, "kinds", "", "k").withConflict("r"),
                new ParamsConfig(Constants.JSON_HOUSE_TYPE, "projectType", "projectType", "pt"),
                new ParamsConfig("", "minPrice", "minPrice", "lp").withConflict("bp","p"),//最低价
                new ParamsConfig("", "maxPrice", "maxPrice", "hp").withConflict("bp","p"),//最高价
                new ParamsConfig(Constants.JSON_PRICE_NEWHOUSE_SHOP, "bigPrice", "bigPrice", "bp").withConflict("lp","hp","p"),
                new ParamsConfig(Constants.JSON_VIDEO_PROJECT, "videoSelect", "videos", "iv"),
                new ParamsConfig(Constants.JSON_PROJECT_SALESTATUS, "saleStatus", "projectStates", "ps"),
                new ParamsConfig(Constants.PROJECT_AREA_FILTER, "areas", "areas", "ar"),
                new ParamsConfig(Constants.JSON_SELECTION, "selectionState", "selectionState", "ss"),
        });
        CONFIG_MAPPING.put(Constants.VIEW_DECORATE_HOUSES, new ParamsConfig[] {
                new ParamsConfig(Constants.FILTER_LAYOUT, "layout","room","t"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_AREA,"area","areaId","a"),
                new ParamsConfig(Constants.FILTER_DECORATION_AREA,"style","styleId","st"),
                new ParamsConfig(Constants.FILTER_NEWHOUSE_REGION, "region", "regionId", "r"),
                new ParamsConfig(Constants.FILTER_LOOP_LINE, "loop", "loopLine", "lo"),
                new ParamsConfig(Constants.JSON_REGION_SUBWAY, "regionSubway", "regionSubwayId", "rs"),
                new ParamsConfig("", "search", "projectName", "search"),

        });
        CONFIG_MAPPING.put(Constants.NEW_SCHOOL_LIST, new ParamsConfig[] {
                new ParamsConfig(Constants.FILTER_SCHOOL_TYPE, "schoolType","schoolTypeId", "s"),
                new ParamsConfig(Constants.FILTER_NEWHOUSE_REGION, "region","regionId","r"),
                new ParamsConfig("", "search", "schoolName", "search"),
        });
        CONFIG_MAPPING.put(Constants.GET_AGENT_NEWS_LIST, new ParamsConfig[] {
                new ParamsConfig(Constants.GET_AGENT_NEWS_CATEGORY, "category","categoryId", ""),
                new ParamsConfig("", "search", "searchTitle", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.GET_RENTHOUSE_LIST, new ParamsConfig[] {
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("j","s","k","v","W","p","b","w","l"),
                new ParamsConfig(Constants.FILTER_PLATES_INFORMATION, "plate", "plateId", "j").withDependency("regionId"),
                new ParamsConfig(Constants.SUBWAY_LINE, "subway", "subWayId", "s").withConflict("r","W"),
                new ParamsConfig(Constants.GET_RENTHOUSE_PRICE, "price", "priceId", "p"),
                new ParamsConfig(Constants.FILTER_LAYOUT, "layout", "room", "l"),
                new ParamsConfig(Constants.JSON_RENT_WAY, "way", "rentalTypeId", "w").withConflict("b","l"),
                new ParamsConfig(Constants.GET_HOUSE_DECORATION, "decorate", "fitmentType", "d"),
                new ParamsConfig(Constants.GET_MENMBER_TYPE, "grigon", "sourceId", "g"),
                new ParamsConfig(Constants.GET_RENT_TRAITS, "trait", "houseTrait", "t"),
                new ParamsConfig(Constants.JSON_RENT_ORDER, "orderKey", "orderKey", "o"),
                new ParamsConfig(Constants.GET_BEDROOM_FILTER, "bedroom", "bedRoom", "b"),
                new ParamsConfig(Constants.JSON_SUBWAY, "kinds", "", "k").withConflict("r"),
                new ParamsConfig(Constants.JSON_PIECE, "piece", "", "h"),
                new ParamsConfig("", "safeTypeId", "safeTypeId", "S"),
                //增加二手房视频看房
                new ParamsConfig(Constants.JSON_VIDEO_GOOD_HOUSE, "vgType", "vgTypeId", "W"),
                new ParamsConfig("", "search", "subName", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.GET_RENTVILLAGE_LIST, new ParamsConfig[] {
                new ParamsConfig( Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("j"),
                new ParamsConfig(Constants.FILTER_PLATES_INFORMATION, "plate", "plateId", "j").withDependency("regionId"),//板块  withCacheable不使用缓存每次加载刷新数据
                new ParamsConfig("", "search", "subName", "search"),//搜索条件
                new ParamsConfig(Constants.VIEW_SALEHOUSE_AGE, "buildYear", "buildYearId", "ha"),
                new ParamsConfig(Constants.GET_SUB_PRICE_FILTER, "price", "priceId", "p").withConflict("k", "x"),
                new ParamsConfig("", "showWay", "minPrice", "k"),//最低价
                new ParamsConfig("", "showWay", "maxPrice", "x"),//最高价
                new ParamsConfig(Constants.JSON_SUB_SORT,"orderType","orderType","o"),
                new ParamsConfig(Constants.VIEW_SUB_BRAN_PROPERTY, "property", "propertyId", "w")//品牌物业
        });
        CONFIG_MAPPING.put(Constants.VIEW_NEW_SUBDISTRICT, new ParamsConfig[] {
                new ParamsConfig( Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("j"),
                new ParamsConfig(Constants.FILTER_PLATES_INFORMATION, "plate", "plateId", "j").withDependency("regionId"),//板块  withCacheable不使用缓存每次加载刷新数据
                new ParamsConfig("", "search", "subName", "search"),//搜索条件
                new ParamsConfig(Constants.VIEW_SALEHOUSE_AGE, "buildYear", "buildYearId", "ha"),
                new ParamsConfig(Constants.GET_SUB_PRICE_FILTER, "price", "priceId", "p").withConflict("k", "x"),
                new ParamsConfig("", "showWay", "minPrice", "k"),//最低价
                new ParamsConfig("", "showWay", "maxPrice", "x"),//最高价
                new ParamsConfig(Constants.JSON_SUB_SORT,"orderType","orderType","o"),
                new ParamsConfig(Constants.VIEW_SUB_BRAN_PROPERTY, "property", "propertyId", "w")//品牌物业
        });
        CONFIG_MAPPING.put(Constants.FIND_AGENT_LIST, new ParamsConfig[] {
                new ParamsConfig( Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r"),
                new ParamsConfig( Constants.GET_INTERMEDIARY_FILTER, "brand", "intermediaryId", "b"),
                new ParamsConfig("", "search", "realName", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.GET_SHOP_LIST, new ParamsConfig[] {
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS,"region","regionId","r").withConflict("j"),//二手房租房商铺区域
                new ParamsConfig(Constants.FILTER_SCDHOUSE_PLATES, "plate", "plateId", "j").withDependency("regionId").withCacheable(false),//板块
                new ParamsConfig(Constants.FILTER_SHOP_TYPE,"type","categoryId","t"),//商铺类型
                new ParamsConfig(Constants.FILTER_SHOP_NEEDS,"needs","typeId","b").withConflict("p","lp","hp"),//商铺供求关系
                new ParamsConfig(Constants.MEMBER_TYPE,"memberType","memberType","i"),//（个人/经纪人）
                new ParamsConfig(Constants.GET_SHOP_PRICE,"price","priceId","p").withConflict("lp","hp"),//（总价）
                new ParamsConfig(Constants.GET_SHOP_AREA,"area","areaId","a").withConflict("la","ha"),//（面积）
                new ParamsConfig(Constants.GET_SHOP_RENTAL,"rental","priceId","p").withConflict("lp","hp"),//（租金）
                new ParamsConfig(Constants.JSON_SHOWVIEW, "showWay", "showWay", "sw"),//商铺页面板式（横版竖版）
                new ParamsConfig("", "search", "title", "search"),//搜索条件
                new ParamsConfig("", "houseTrait", "houseTrait", "u"),//商铺特色
                new ParamsConfig("", "showWay", "minPrice", "lp"),//最低价
                new ParamsConfig("", "showWay", "maxPrice", "hp"),//最高价
                new ParamsConfig("", "showWay", "minArea", "la"),//最小面积
                new ParamsConfig("", "showWay", "maxArea", "ha")//最大面积
        });
        CONFIG_MAPPING.put(Constants.SECOND_HOUSE_LIST, new ParamsConfig[]{
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("b", "z", "j", "q", "v"),//二手房租房商铺区域
                new ParamsConfig(Constants.FILTER_PLATES_INFORMATION, "plate", "plateId", "j").withDependency("regionId").withConflict("b", "q").withCacheable(false),//商铺供求关系
                new ParamsConfig(Constants.JSON_SUBWAY, "location", "isSubway", "z").withConflict("r"),
                new ParamsConfig(Constants.FILTER_SUBWAY_LINE, "subWayId", "subWayId", "b").withConflict("r","j","q"),
                new ParamsConfig(Constants.FILTER_SUBWAY_STATIONS, "subwayStation", "subWayStationId", "q").withDependency("subWayId").withConflict("r", "j"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_PRICE, "price", "priceId", "p").withConflict("k", "x"),//商铺类型
                new ParamsConfig(Constants.FILTER_SCDHOUSE_AREA, "area", "areaId", "a").withConflict("y", "e"),
                new ParamsConfig(Constants.FILTER_LAYOUT, "room", "room", "l"),
                new ParamsConfig(Constants.FILTER_HOUSE_FORWARD_SEARCH, "forward", "forwardType", "f"),
                new ParamsConfig(Constants.NEW_HOUSE_FEATURE, "feature", "houseTrait", "u"),
                new ParamsConfig(Constants.FILTER_HOUSE_DECORATION, "decoration", "fitmentType", "d"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_FLOOR, "floor", "floorRange", "m"),
                new ParamsConfig(Constants.MEMBER_TYPE, "memberType", "memberType", "g"),
                new ParamsConfig(Constants.JSON_PIECE, "piece", "piece", "h"),
                new ParamsConfig(Constants.JSON_SCD_SORT, "orderKey", "orderKey", "o"),
                new ParamsConfig(Constants.GET_INTERMEDIARY_FILTER, "intermediary", "intermediaryId", "i"),
                //增加二手房视频看房+优质好房筛选
                new ParamsConfig(Constants.JSON_VIDEO_GOOD_HOUSE, "vgType", "vgTypeId", "W"),
                //增加新房筛选条件
                new ParamsConfig(Constants.JSON_ISNEWHOUSE, "isNewHouse", "isNewHouse", "N"),
                new ParamsConfig(Constants.JSON_COOPERATION, "isCooperation", "isCooperation", "co"),
                new ParamsConfig("", "safeTypeId", "safeTypeId", "S"),
                new ParamsConfig("", "search", "subName", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.HOUSE_LAYOUT_INFO, new ParamsConfig[] {
                new ParamsConfig("","","projectId","pid"),
                new ParamsConfig("","","projectType","pt"),
                new ParamsConfig(Constants.GET_LAYOUT_TYPE,"roomType","roomType","r").withCacheable(false)
                        .withDependency("projectType","projectId","houseType").withConflict("l"),
                new ParamsConfig("","","layId","l"),
                new ParamsConfig(Constants.GET_LAYOUT_FILTER,"houseType","houseType","h").withCacheable(false)
                        .withDependency("projectType","projectId").withConflict("l","r")
        });
        CONFIG_MAPPING.put(Constants.GET_ONLINE_INFO, new ParamsConfig[] {
                new ParamsConfig("","","projectId","pid"),
                new ParamsConfig("","","projectType","pt"),
                new ParamsConfig(Constants.GET_ROOM_TYPE,"roomType","roomType","r").withCacheable(false)
                        .withDependency("projectType","projectId")
        });
        CONFIG_MAPPING.put(Constants.GET_NEWS_LIST, new ParamsConfig[] {//资讯列表
                new ParamsConfig(Constants.GET_NEWS_CATEGORY, "category","categoryId", ""),
                new ParamsConfig("", "search", "searchTitle", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.GET_VIDEO_LIST, new ParamsConfig[] {//视频列表
                new ParamsConfig(Constants.GET_VIDEOS_CATEGORY, "videoType","videoType", ""),
                new ParamsConfig("", "search", "projectName", "search"),//搜索条件
        });
        CONFIG_MAPPING.put(Constants.NEW_VIDEO_LIST, new ParamsConfig[] {//视频列表（新）
                new ParamsConfig(Constants.NEW_VIDEOS_FEATURE, "videoFeature","videoFeature", ""),
                new ParamsConfig("", "search", "projectName", "search"),//搜索条件
        });
        CONFIG_MAPPING.put(Constants.GET_AUDIOS_LIST, new ParamsConfig[] {//小二说房列表
                new ParamsConfig(Constants.GET_AUDIOS_CATEGORYS, "categoryId","categoryId", "")
        });
        CONFIG_MAPPING.put(Constants.INDEX_PAGE_NEW, new ParamsConfig[] {
                new ParamsConfig( Constants.FILTER_NEWHOUSE_REGION, "region", "regionId", "r"),
                new ParamsConfig( Constants.FILTER_NEWHOUSE_PRICE, "price", "moneyId", "p"),
                new ParamsConfig( Constants.FILTER_SCDHOUSE_PRICE,"saleHousePrice", "priceId", "p"),
                new ParamsConfig( Constants.FILTER_LAYOUT,"room", "room", "l"),
                new ParamsConfig( Constants.FILTER_SHOP_TYPE,"shopType", "categoryId", "t"),
                new ParamsConfig( Constants.FILTER_SHOP_NEEDS,"shopNeed", "typeId", "b"),
                new ParamsConfig( Constants.GET_RENTHOUSE_PRICE,"rentBuget", "priceId", "p"),
                new ParamsConfig( Constants.FILTER_SCDHOUSE_REGIONS,"commonRegion", "regionId", "r"),
                new ParamsConfig( Constants.JSON_HOUSE_TYPE,"projectType", "projectType", "pt"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_AREA, "area", "areaId", "a"),//（二手房面积）
                new ParamsConfig(Constants.GET_SHOP_PRICE,"shopPrice","priceId","p"),//（商铺总价）
                new ParamsConfig(Constants.GET_SHOP_AREA,"shopArea","areaId","a"),//（商铺面积）
                new ParamsConfig(Constants.GET_SHOP_RENTAL,"shopRental","priceId","p"),//（商铺租金）
                new ParamsConfig( Constants.FILTER_OFFICE_REGIONS, "officeRegion", "regionId", "r"),//(写字楼区域)
                new ParamsConfig( Constants.OFFICE_PROJECT_SALEPRICE, "officePrice", "spriceId", "p"),//(写字楼售价)
                new ParamsConfig( Constants.OFFICE_RENT_SMALL_PRICE, "uprice", "upriceId", "up"),//(写字楼租金)
                new ParamsConfig( Constants.OFFICE_AREA, "officeArea", "oAreaId", "a"),//(写字楼面积)
                new ParamsConfig( Constants.OFFICE_TYPE, "category", "categoryId", "t"),//（写字楼类型）
                new ParamsConfig(Constants.MEMBER_TYPE,"shopMemberType","memberType","i"),//（商铺所属人类型 个人/经纪人）
        });
        CONFIG_MAPPING.put(Constants.OFFICE_LIST, new ParamsConfig[] {
                new ParamsConfig( Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("j"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_PLATES, "plate", "plateId", "j").withDependency("regionId").withCacheable(false),//板块
                new ParamsConfig( Constants.OFFICE_SALE_OR_RENT, "type", "typeId", "b").withConflict("p","q", "up","rn","rm","un","um","sn","sm"),
                new ParamsConfig( Constants.OFFICE_SALE_PRICE, "sprice", "spriceId", "p").withConflict("q", "up","sn","sm","rn","rm","un","um"),
                new ParamsConfig( Constants.OFFICE_RENT_BIG_PRICE, "rprice", "rpriceId", "q").withConflict("p", "up","sn","sm","rn","rm","un","um"),
                new ParamsConfig( Constants.OFFICE_RENT_SMALL_PRICE, "uprice", "upriceId", "up").withConflict("q", "p","sn","sm","rn","rm","un","um"),
                new ParamsConfig( Constants.OFFICE_AREA, "area", "oAreaId", "a").withConflict("la","ha"),
                new ParamsConfig( Constants.OFFICE_TYPE, "category", "categoryId", "t"),
                new ParamsConfig( Constants.OFFICE_FITMENT, "fitment", "fitment", "f"),
                new ParamsConfig( Constants.MEMBER_TYPE, "memberType", "memberType", "m"),
                new ParamsConfig( Constants.OFFICE_TRAITS, "houseTrait", "houseTrait", "x"),
                new ParamsConfig(Constants.JSON_SHOWVIEW, "showWay", "showWay", "sw"),//商铺页面板式（横版竖版）
                new ParamsConfig(Constants.JSON_OFFICE_CHOOSE_PIECE, "choosePrice", "choosePrice", "cp").withConflict("rp", "up"),
                new ParamsConfig( "", "officeId", "officeId", "v"),
                new ParamsConfig("", "showWay", "ominArea", "la"),//最小面积
                new ParamsConfig("", "showWay", "omaxArea", "ha"),//最大面积
                new ParamsConfig("", "sminPrice", "sminPrice", "sn").withConflict("q", "up","p","rn","rm","un","um"),//最小售价
                new ParamsConfig("", "smaxPrice", "smaxPrice", "sm").withConflict("q", "up","p","rn","rm","un","um"),//最大售价
                new ParamsConfig("", "rminPrice", "rminPrice", "rn").withConflict("q", "up","p","sn","sm","un","um"),//最小总价
                new ParamsConfig("", "rmaxPrice", "rmaxPrice", "rm").withConflict("q", "up","p","sn","sm","un","um"),//最大总价
                new ParamsConfig("", "uminPrice", "uminPrice", "un").withConflict("q", "up","p","sn","sm","rn","rm"),//最小单价
                new ParamsConfig("", "uminPrice", "umaxPrice", "um").withConflict("q", "up","p","sn","sm","rn","rm"),//最大单价
                new ParamsConfig("", "search", "title", "search"),
        });
        CONFIG_MAPPING.put(Constants.OFFICE_PROJECT_LIST, new ParamsConfig[] {
                new ParamsConfig( Constants.FILTER_OFFICE_REGIONS, "region", "regionId", "r"),
                new ParamsConfig( Constants.OFFICE_PROJECT_SALEPRICE, "sprice", "salePriceId", "p").withConflict("up","sn","sm","un","um"),
                new ParamsConfig( Constants.OFFICE_RENT_SMALL_PRICE, "uprice", "rentPriceId", "up").withConflict("p","sn","sm","un","um"),
                new ParamsConfig( Constants.OFFICE_AREA, "area", "areaId", "a").withConflict("la","ha"),
                new ParamsConfig( Constants.OFFICE_TYPE, "category", "buildType", "t"),
                new ParamsConfig( Constants.OFFICE_TRAITS, "houseTrait", "houseTrait", "x"),
                new ParamsConfig("", "showWay", "minArea", "la"),//最小面积
                new ParamsConfig("", "showWay", "maxArea", "ha"),//最大面积
                new ParamsConfig("", "sminPrice", "minSaleUnitPrice", "sn").withConflict("up","p","un","um"),//最小售价
                new ParamsConfig("", "smaxPrice", "maxSaleUnitPrice", "sm").withConflict("up","p","un","um"),//最大售价
                new ParamsConfig("", "uminPrice", "minRentUnitPrice", "un").withConflict("up","p","sn","sm"),//最小单价
                new ParamsConfig("", "uminPrice", "maxRentUnitPrice", "um").withConflict("up","p","sn","sm"),//最大单价
                new ParamsConfig("", "search", "officeName", "search"),
        });
        CONFIG_MAPPING.put(Constants.DEAL_SECOND_LIST, new ParamsConfig[]{
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("b", "z", "j", "q", "v"),//二手房租房商铺区域
                new ParamsConfig(Constants.FILTER_SCDHOUSE_PLATES, "plate", "plateId", "j").withDependency("regionId").withConflict("b", "q").withCacheable(false),//商铺供求关系
                new ParamsConfig(Constants.JSON_SUBWAY, "location", "isSubway", "z").withConflict("r"),
                new ParamsConfig(Constants.FILTER_SUBWAY_LINE, "subWayId", "subWayId", "b").withConflict("r","j"),
                new ParamsConfig(Constants.FILTER_SUBWAY_STATIONS, "subwayStation", "subWayStationId", "q").withDependency("subWayId").withConflict("r", "j"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_PRICE, "price", "priceId", "p").withConflict("k", "x"),//商铺类型
                new ParamsConfig(Constants.FILTER_SCDHOUSE_AREA, "area", "areaId", "a").withConflict("y", "e"),
                new ParamsConfig(Constants.FILTER_LAYOUT, "room", "room", "l"),
                new ParamsConfig(Constants.FILTER_HOUSE_FORWARD_SEARCH, "forward", "forwardType", "f"),
                new ParamsConfig(Constants.NEW_HOUSE_FEATURE, "feature", "houseTrait", "u"),
                new ParamsConfig(Constants.FILTER_HOUSE_DECORATION, "decoration", "fitmentType", "d"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_FLOOR, "floor", "floorRange", "m"),
                new ParamsConfig(Constants.MEMBER_TYPE, "memberType", "memberType", "g"),
                new ParamsConfig(Constants.JSON_PIECE, "piece", "piece", "h"),
                new ParamsConfig(Constants.JSON_SCD_SORT, "orderKey", "orderKey", "o"),
                new ParamsConfig("", "search", "subName", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.DEAL_RENT_LIST, new ParamsConfig[] {
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("s","k","v"),
                new ParamsConfig(Constants.SUBWAY_LINE, "subway", "subWayId", "s").withConflict("r"),
                new ParamsConfig(Constants.GET_RENTHOUSE_PRICE, "price", "priceId", "p"),
                new ParamsConfig(Constants.FILTER_LAYOUT, "layout", "room", "l"),
                new ParamsConfig(Constants.JSON_RENT_WAY, "way", "rentalTypeId", "w").withConflict("b","l"),
                new ParamsConfig(Constants.GET_HOUSE_DECORATION, "decorate", "fitmentType", "d"),
                new ParamsConfig(Constants.GET_MENMBER_TYPE, "grigon", "sourceId", "g"),
                new ParamsConfig(Constants.GET_RENT_TRAITS, "trait", "houseTrait", "t"),
                new ParamsConfig(Constants.JSON_RENT_ORDER, "orderKey", "orderKey", "o"),
                new ParamsConfig(Constants.GET_BEDROOM_FILTER, "bedroom", "bedRoom", "b"),
                new ParamsConfig(Constants.JSON_SUBWAY, "kinds", "", "k").withConflict("r"),
                new ParamsConfig(Constants.JSON_PIECE, "piece", "", "h"),
                new ParamsConfig("", "search", "subName", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.GET_ASK_SUBDISTRICT_LIST, new ParamsConfig[] {

        });
        CONFIG_MAPPING.put(Constants.DEAL_SHOP_LIST, new ParamsConfig[] {
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS,"region","regionId","r").withConflict("j"),//二手房租房商铺区域
                new ParamsConfig(Constants.FILTER_SCDHOUSE_PLATES, "plate", "plateId", "j").withDependency("regionId").withCacheable(false),//板块
                new ParamsConfig(Constants.FILTER_SHOP_TYPE,"type","categoryId","t"),//商铺类型
                new ParamsConfig(Constants.FILTER_SHOP_NEEDS,"needs","typeId","b").withConflict("p","lp","hp"),//商铺供求关系
                new ParamsConfig(Constants.MEMBER_TYPE,"memberType","memberType","i"),//（个人/经纪人）
                new ParamsConfig(Constants.GET_SHOP_PRICE,"price","priceId","p").withConflict("lp","hp"),//（总价）
                new ParamsConfig(Constants.GET_SHOP_AREA,"area","areaId","a").withConflict("la","ha"),//（面积）
                new ParamsConfig(Constants.GET_SHOP_RENTAL,"rental","priceId","p").withConflict("lp","hp"),//（租金）
                new ParamsConfig(Constants.JSON_SHOWVIEW, "showWay", "showWay", "sw"),//商铺页面板式（横版竖版）
                new ParamsConfig("", "search", "title", "search"),//搜索条件
                new ParamsConfig("", "houseTrait", "houseTrait", "u"),//商铺特色
                new ParamsConfig("", "showWay", "minPrice", "lp"),//最低价
                new ParamsConfig("", "showWay", "maxPrice", "hp"),//最高价
                new ParamsConfig("", "showWay", "minArea", "la"),//最小面积
                new ParamsConfig("", "showWay", "maxArea", "ha")//最大面积
        });
        CONFIG_MAPPING.put(Constants.PROJECT_COMMENT_INFO, new ParamsConfig[] {

        });

        CONFIG_MAPPING.put(Constants.VIEW_HOUSE_PRICE_LIST, new ParamsConfig[]{
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("j"),//区域
                new ParamsConfig(Constants.FILTER_SCDHOUSE_PLATES, "plate", "plateId", "j").withDependency("regionId").withCacheable(false),//板块
                new ParamsConfig("", "search", "subName", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.VIEW_XIAOER_DEALSTORIES, new ParamsConfig[] {
                new ParamsConfig("", "", "index", "i")//
        });

        CONFIG_MAPPING.put(Constants.VIEW_NEED_HOUSES, new ParamsConfig[] {
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r"),
                new ParamsConfig(Constants.FILTER_LAYOUT, "layout", "room", "l"),
                new ParamsConfig(Constants.VIEW_NEED_RENT_PRICE, "rentPrice", "budget", "rb").withConflict("sb"),
                new ParamsConfig(Constants.VIEW_NEED_SECOND_PRICE, "secondPrice", "budget", "sb").withConflict("rb"),
        });

        CONFIG_MAPPING.put(Constants.GET_AGENT_RECRUIT_LIST, new ParamsConfig[]{
                new ParamsConfig(Constants.GET_AGENT_RECRUIT_JOBTYPE, "jobType", "jobType", "j").withCacheable(false),//职位
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS, "region", "jobRegion", "r"),
                new ParamsConfig(Constants.GET_AGENT_RECRUIT_SALARY, "salary", "jobSalaryRange", "s"),//薪资
                new ParamsConfig(Constants.GET_AGENT_RECRUIT_EXPERIENCE, "experience", "jobWorkingLifeType", "e"),//工作经验
                new ParamsConfig(Constants.GET_AGENT_RECRUIT_WELFARE, "welfare", "jobWelfareType", "w").withCacheable(false),//福利 jobWelfareType接口中的key
                new ParamsConfig("", "search", "searchKey", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.QUERY_COMPANY_SECOND, new ParamsConfig[]{
                new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS, "region", "regionId", "r").withConflict("b", "z", "j", "q", "v","W"),//二手房租房商铺区域
                new ParamsConfig(Constants.FILTER_PLATES_INFORMATION, "plate", "plateId", "j").withDependency("regionId").withConflict("b", "q","W").withCacheable(false),//商铺供求关系
                new ParamsConfig(Constants.JSON_SUBWAY, "location", "isSubway", "z").withConflict("r"),
                new ParamsConfig(Constants.FILTER_SUBWAY_LINE, "subWayId", "subWayId", "b").withConflict("r","j","q"),
                new ParamsConfig(Constants.FILTER_SUBWAY_STATIONS, "subwayStation", "subWayStationId", "q").withDependency("subWayId").withConflict("r", "j"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_PRICE, "price", "priceId", "p").withConflict("k", "x"),//商铺类型
                new ParamsConfig(Constants.FILTER_SCDHOUSE_AREA, "area", "areaId", "a").withConflict("y", "e"),
                new ParamsConfig(Constants.FILTER_LAYOUT, "room", "room", "l"),
                new ParamsConfig(Constants.FILTER_HOUSE_FORWARD_SEARCH, "forward", "forwardType", "f"),
                new ParamsConfig(Constants.NEW_HOUSE_FEATURE, "feature", "houseTrait", "u"),
                new ParamsConfig(Constants.FILTER_HOUSE_DECORATION, "decoration", "fitmentType", "d"),
                new ParamsConfig(Constants.FILTER_SCDHOUSE_FLOOR, "floor", "floorRange", "m"),
                new ParamsConfig(Constants.MEMBER_TYPE, "memberType", "memberType", "g"),
                new ParamsConfig(Constants.JSON_PIECE, "piece", "piece", "h"),
                new ParamsConfig(Constants.JSON_SCD_SORT, "orderKey", "orderKey", "o"),
                new ParamsConfig(Constants.GET_INTERMEDIARY_FILTER, "intermediary", "intermediaryId", "i"),
                //增加二手房视频看房+优质好房筛选
                new ParamsConfig(Constants.JSON_VIDEO_GOOD_HOUSE, "vgType", "vgTypeId", "W"),
                new ParamsConfig("", "search", "subName", "search")//搜索条件
        });
        CONFIG_MAPPING.put(Constants.TOURIST_LAYOUT_DETAIL, new ParamsConfig[]{
                new ParamsConfig("", "", "projectId", "pid"),
                new ParamsConfig(Constants.TOURIST_LAY1_FILTER, "roomId", "roomId", "r").withCacheable(false)
                        .withDependency("projectId").withConflict("l", "lt"),
                new ParamsConfig(Constants.TOURIST_LAY2_FILTER, "layoutType", "layoutType", "lt").withCacheable(false)
                        .withDependency("projectId").withConflict("l", "r"),
                new ParamsConfig(Constants.TOURIST_LAYDETAIL_FILTER, "layId", "layId", "l").withCacheable(false)
                        .withDependency("projectId", "roomId", "layoutType"),
        });
        CONFIG_MAPPING.put(Constants.NEW_PROJECT_DYNAMIC_LIST, new ParamsConfig[]{
                new ParamsConfig(Constants.FILTER_NEWHOUSE_REGION, "region", "regionId", "r"),//区域
                new ParamsConfig(Constants.JSON_HOUSE_TYPE, "projectType", "projectType", "p")//类型
        });
        CONFIG_MAPPING.put(Constants.GET_HOUSE_DEMAND_ORDER_LIST, new ParamsConfig[] {
            new ParamsConfig(Constants.FILTER_SCDHOUSE_REGIONS,"region","region","r"),
            new ParamsConfig(Constants.DEMAND_NEW_HOUSE_BUDGET, "budget1", "budget1", "np").withConflict("lp", "hp"),
            new ParamsConfig(Constants.FILTER_LAYOUT, "room1", "roomType", "nl"),
            new ParamsConfig("", "minPrice", "minPrice", "lp"),
            new ParamsConfig("", "maxPrice", "maxPrice", "hp"),
            new ParamsConfig(Constants.DEMAND_SECOND_HOUSE_BUDGET, "budget2", "budget2", "sp").withConflict("lp", "hp"),
            new ParamsConfig(Constants.FILTER_LAYOUT, "room2", "roomType", "sl"),
            new ParamsConfig(Constants.GET_RENTHOUSE_PRICE, "budget3", "budget3", "rp"),
            new ParamsConfig(Constants.DEMAND_RENT_HOUSE_DEMAND_TYPE, "demandType3", "demandType", "rw"),
            new ParamsConfig(Constants.DEMAND_SHOP_DEMAND_TYPE, "demandType4", "demandType", "spw"),
            new ParamsConfig(Constants.GET_SHOP_AREA,"area4","area4","spa").withConflict("lm", "hm").withConflict("lm", "hm"),
            new ParamsConfig(Constants.DEMAND_OFFICE_DEMAND_TYPE, "demandType5", "demandType", "ow"),
            new ParamsConfig(Constants.OFFICE_AREA, "area5", "area5", "oa").withConflict("lm", "hm").withConflict("lm", "hm"),
            new ParamsConfig("", "minArea", "minArea", "lm"),
            new ParamsConfig("", "maxArea", "maxArea", "hm"),
        });

    }
    public static class ParamsConfig {
        public String filterUrl;
        public String modelKey;
        public String paramName;
        public String shortKey;
        public boolean cacheable = true;
        public String dependency[];
        public String conflict[];

        public ParamsConfig(String filterUrl, String modelKey, String paramName, String shortKey) {
            this.filterUrl = filterUrl;
            this.modelKey = modelKey;
            this.paramName = paramName;
            this.shortKey = shortKey;
        }

        public ParamsConfig withCacheable(boolean cacheable) {
            this.cacheable = cacheable;
            return this;
        }

        public ParamsConfig withDependency(String... dependency) {
            this.dependency = dependency;
            return this;
        }

        public ParamsConfig withConflict(String... conflict) {
            this.conflict = conflict;
            return this;
        }
    }

    /**
     * 根据url的参数来分析用户选择
     */
    public static HashMap<String, String> analysisInput(String field, String baseUrl) {
        HashMap<String, String> resultMap = new HashMap<>();
        if (StringUtils.isEmpty(field)) {
            return resultMap;
        }
        try {
            if(field.equals(new String(field.getBytes("ISO-8859-1"), "ISO-8859-1"))) {
                field = new String(field.getBytes("ISO-8859-1"), "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        String[] Arr = field.split("-");
        for (int i = 0; i < Arr.length; i++) {
            if (StringUtils.isEmpty(Arr[i])) {
                continue;
            }
            String[] result = groupParam(Arr[i]);
            analysisConfig(CONFIG_MAPPING.get(baseUrl), resultMap, result);
            analysisConfig(CONFIG_MAPPING.get("common"), resultMap, result);
            analysisConfig(CONFIG_MAPPING.get("other"), resultMap, result);
        }
        return resultMap;
    }

    private static void analysisConfig(ParamsConfig[] configs, HashMap<String, String> resultMap, String[] result) {
        if(configs == null || result == null) return;
        for(ParamsConfig config:configs) {
            if (config.shortKey.equals(result[0])) {
                resultMap.put(config.paramName, result[1]);
                break;
            }
        }
    }

    public static HashMap<String, String> analysisInput(String field) {
        return analysisInput(field, "");
    }

    private static String[] groupParam(String item) {
        if(item.contains("=")) {
            String[] tmp = item.split("=");
            return tmp.length > 1 ? tmp : null;
        }
        Pattern pattern = Pattern.compile("[0-9]+$");
        Matcher m = pattern.matcher(item);
        if (m.find()) {
            String num = m.group();
            String key = item.replace(num, "");
            return new String[]{key, num};
        }
        return null;
    }
    /**
     * 页码url生成
     */
    public static String pageUrlItem(String url) {
        if (StringUtils.isEmpty(url) || !url.contains("n")) return url;
        return url.replaceAll("-n[0-9]+", "");
    }

    /**
     * 区域和价格url生成
     *
     * @param input
     * @param opinions
     * @param startKey
     * @return
     */
    public static ArrayList<UrlBean> generateItem(String input, ArrayList<NameIdBean> opinions, String startKey, String head) {
        return generateItem(input, opinions, startKey, head, null);
    }

    public static ArrayList<UrlBean> generateItem(String input, ArrayList<NameIdBean> opinions, String startKey, String head, String[] conflict) {
        if (input == null) input = "";
        try {
            if(input.equals(new String(input.getBytes("ISO-8859-1"), "ISO-8859-1"))) {
                input = new String(input.getBytes("ISO-8859-1"), "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        String[] params = input.split("-");
        ArrayList<UrlBean> list = new ArrayList<>();
        // 默认设置"全部"按钮为已选择状态
        UrlBean headerBean = null;
        if (conflict == null) conflict = new String[] {};
        List<String> pConflict = Arrays.asList(conflict);
        for (NameIdBean item : opinions) {
            String url = "";
            UrlBean urlBean = new UrlBean();
            urlBean.setName(item.getName());
            urlBean.setId(item.getId());
              if(StringUtils.isEmpty(item.getId())) {
                headerBean = urlBean;
                headerBean.setSelected(true);
            }
            String currentNode = startKey + item.getId();
            for (String param : params) {
                if (StringUtils.isEmpty(param)) continue;
                String[] gParams = groupParam(param);
                if(gParams == null) continue;
                if (!"n".equals(gParams[0]) && !startKey.equals(gParams[0]) && !pConflict.contains(gParams[0])) {
                    url += "-";
                    url += param;
                } else if(currentNode.equals(param)) {
                    urlBean.setSelected(true);
                    if(headerBean != null) {
                        headerBean.setSelected(false);
                    }
                }
            }
            url = head + (StringUtils.isEmpty(item.getId()) ? "" : startKey) + item.getId() + url;
            urlBean.setUrl(url);
            list.add(urlBean);
        }
        return list;
    }

    /**
     * 小区类型
     */
    public static class RegionBean extends NameIdBean {
        private String regionId;
        private String regionName;

        public String getRegionId() {
            return regionId;
        }

        public void setRegionId(String regionId) {
            this.regionId = regionId;
        }

        public String getRegionName() {
            return regionName;
        }

        public void setRegionName(String regionName) {
            this.regionName = regionName;
        }
    }
}
