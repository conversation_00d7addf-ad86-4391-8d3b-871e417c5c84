package com.fangxiaoer.common;

import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import okhttp3.*;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class HttpUtil {
    private static Logger logger = LoggerFactory.getLogger(HttpUtil.class);

    public static OkHttpClient client = new OkHttpClient.Builder().connectTimeout(15, TimeUnit.SECONDS).readTimeout(20, TimeUnit.SECONDS).build();
    /**
     * 无传参查询数据
     */
    public static HashMap<String, Object> connectApi(String url) {
        return connectApi(url, null, false);
    }

    public static HashMap<String, Object> connectApi(String url, boolean cacheAble) {
        return connectApi(url, null, cacheAble);
    }

    /**
     * 列表页/ajax/特殊结果处理方案
     */
    public static HashMap<String, Object> connectApiString(String url, HashMap<String, String> params) {
        HashMap<String, Object> temp = new HashMap();
        temp.putAll(params);
        return connectApi(url, temp);
    }

    public static HashMap<String, Object> connectApi(String url, HashMap<String, Object> params) {
        return connectApi(url, params, false);
    }

    public static HashMap<String, Object> connectApi(String url, HashMap<String, Object> params, boolean cacheAble) {
        if(cacheAble) {
            String key = url + EasyCacheUtil.generateKey(params);
            if(EasyCacheUtil.get(key) != null) {
                return (HashMap<String, Object>) EasyCacheUtil.get(key);
            }
        }
        long startTime = System.currentTimeMillis();
        HashMap<String, Object> resultMap = new HashMap<>();
        String json = "";
        try {
            FormBody.Builder b = new FormBody.Builder();
            if(params != null) {
                for (Map.Entry<String, Object> item : params.entrySet()) {
                    b.add(item.getKey(), String.valueOf(item.getValue()));
                }
            }
            RequestBody body = b.build();
            Request.Builder requestBuilder = new Request.Builder();
            RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest servletRequest = ((ServletRequestAttributes) attributes).getRequest();
                String ip = Utils.getIpAddr(servletRequest);
                String userAgent = servletRequest.getHeader("user-agent");
                if (userAgent == null) {
                    userAgent = "unknown";
                } else {
                    userAgent = Utils.removeSpecialCForUA(userAgent);
                }
                String sessionId = (String) servletRequest.getSession().getAttribute("muser");
                requestBuilder.addHeader("fxr-forward-ip", ip)
                        .addHeader("user-agent", userAgent)
                        .addHeader("fxr-client", "pc");
                if (!StringUtils.isEmpty(sessionId)) {
                    requestBuilder.addHeader("sessionId", sessionId);
                }
            }
            Request request = requestBuilder.url(url).post(body).build();
            Response response = client.newCall(request).execute();
            json = response.body().string();
            if (!StringUtils.isEmpty(json)) {
                Gson gson = new Gson();
                resultMap = gson.fromJson(json, new TypeToken<HashMap<String, Object>>() {
                }.getType());
            }
            int status = Double.valueOf((double) resultMap.get("status")).intValue();
            if(status == -1) {
                logger.info("Session timeout");
            }

            if(cacheAble) {
                if(resultMap.get("status") != null && status == 1) {
                    String key = url + EasyCacheUtil.generateKey(params);
                    EasyCacheUtil.put(key, resultMap);
                }
            }
        } catch (Exception e) {
            logger.error("Net error, " + url, e);
            if(!StringUtils.isEmpty(json)) {
                logger.error(json);
            }
        }
        return resultMap;
    }

    /**
     * 详细页通用处理方案
     */
    public static Object connectApi(String url, HashMap<String, Object> params, Model model) {
        HashMap<String, Object> resultMap = connectApi(url, params);
        Object result = handleResult(resultMap, model, null);
        return result;
    }

    public static void connectApiString(String url, HashMap<String, String> params, Model model, String key) {
        HashMap<String, Object> temp = new HashMap();
        temp.putAll(params);
        connectApi(url, temp, model, key);
    }
    public static void connectApi(String url, HashMap<String, Object> params, Model model, String key) {
        HashMap<String, Object> resultMap = connectApi(url, params);
        handleResult(resultMap, model, key);
    }

    private static Object handleResult(HashMap<String, Object> result, Model model, String key) {
        if (result == null) {
            model.addAttribute("msg", "内容不存在");
            return null;
        }
        int status = Double.valueOf((double) result.get("status")).intValue();
        if (status == 0) {
            model.addAttribute("msg", result.get("msg"));
            return null;
        } else if (status == -1) {
            model.addAttribute("msg", result.get("msg"));
            return -1;
        }
        if(!StringUtils.isEmpty(key)) {
            model.addAttribute(key, result.get("content"));
        }
        return result.get("content");
    }

    /**
     * 处理Service中无结果集的操作
     *
     * @param result
     * @param model
     * @return
     */
    public static Integer handleServiceMethod(HashMap<String, Object> result, Model model, HttpSession session) {
        if (result == null) {
            return -2;
        }
        int status = Double.valueOf((double) result.get("status")).intValue();
        if (status == 0) {
            model.addAttribute("msg", result.get("msg"));
            return 0;
        } else if (status == -1) {
            model.addAttribute("msg", result.get("msg"));
            Utils.removeSession(session);
            return -1;
        } else {
            return 1;
        }
    }

    /**
     * 处理Service列表页问题
     *
     * @param result  返回结果集
     * @param count   数量字段名称
     * @param content 列表内容名称
     * @param model
     */
    public static Integer handleServiceList(HashMap<String, Object> result, String count, String content, Model model) {
        if (result == null) {
            model.addAttribute(count, 0);
            model.addAttribute(content, null);
            return -2;
        }
        int status = Double.valueOf((double) result.get("status")).intValue();
        if (status == 0) {
            model.addAttribute(count, 0);
            model.addAttribute("msg", result.get("msg"));
            model.addAttribute(content, null);
            return 0;
        }
        if (status == -1) {
            model.addAttribute("msg", result.get("msg"));
            return -1;
        }
        if (StringUtils.isEmpty(result.get("content"))) {
            model.addAttribute(count, 0);
            model.addAttribute("msg", result.get("msg"));
            model.addAttribute(content, null);
        }
        model.addAttribute(count, result.get("msg"));
        List<LinkedTreeMap<String, Object>> list = (List<LinkedTreeMap<String, Object>>) result.get("content");
        if (list.size() == 0) {
            model.addAttribute(content, null);
        } else {
            model.addAttribute(content, list);
        }
        return 1;
    }

    /**
     * session相关通用controller处理方案
     *
     * @param url   目标链接
     * @param check session相关的结果 0--操作异常（提示错误原因）/1--正常显示页/-1--session失效登录页/其他（-2）--无数据异常
     * @return
     */
    public static String handleSessionController(String url, Integer check, HttpServletRequest request, HttpSession session, RedirectAttributes attributes, Model model) {
        if (check == 1) {
            return url;
        } else if (check == 0) {
            return "error/exception";
        } else if (check == -1) {
            Utils.removeSession(session);
            attributes.addAttribute("url", request.getRequestURI());
            return "redirect:/signin";
        } else {
            return "error/error";
        }
    }

    /**
     * 验证sessionId
     *
     * @param request
     * @return
     */
    public static String checkSessionId(HttpServletRequest request) {
        String sessionId = "";
        HttpSession session = request.getSession();
        if (!StringUtils.isEmpty(session.getAttribute("sessionId"))) {
            sessionId = session.getAttribute("sessionId").toString();
            return sessionId;
        }
        String ticket = CookieManage.iterationCookie(request, "aspxUserTicket");
        if (!StringUtils.isEmpty(ticket)) {
            sessionId = ticket;
            return sessionId;
        }
        return sessionId;
    }
}
