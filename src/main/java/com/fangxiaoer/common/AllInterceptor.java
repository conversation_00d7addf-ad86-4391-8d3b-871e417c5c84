package com.fangxiaoer.common;

import com.alibaba.fastjson.JSONObject;
import com.fangxiaoer.model.LoginResultFilter;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import okhttp3.Request;
import okhttp3.Response;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AllInterceptor implements HandlerInterceptor {
    Logger logger = LoggerFactory.getLogger(AllInterceptor.class);

    private IPMonitor monitor;

    public AllInterceptor(IPMonitor monitor) {
        this.monitor = monitor;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        logger.info("====>  Start: " + request.getRequestURI());
        //判断用户ID是否存在，不存在就跳转到登录界面
        cookieLogin(request, response);
        if (!checkPcOrMoblie(request, response)) {
            return false;
        }
        if (!checkFrequence(request, response, handler)) {
            return false;
        }
        return true;
    }

    public void cookieLogin(HttpServletRequest request, HttpServletResponse response) {
        HttpSession httpSession = request.getSession(true);
        Object timeMillis = httpSession.getAttribute("timeMillis");
        if (timeMillis != null && (System.currentTimeMillis() - Long.valueOf(timeMillis.toString()) < 10000)) return;
        String ticket = CookieManage.iterationCookie(request, "aspxUserTicket");
        String sessionId = (String) httpSession.getAttribute("sessionId");
        if (!StringUtils.isEmpty(ticket) && StringUtils.isEmpty(sessionId)) {
            HashMap<String, Object> result = HttpUtil.connectApi(Constants.VIEW_MEMBER_INFO, new Params("sessionId", ticket).get());
            Integer status = Double.valueOf(result.get("status").toString()).intValue();
            if (status.equals(1)) {
                LinkedTreeMap<String, Object> content = (LinkedTreeMap<String, Object>) result.get("content");
                Gson gson = new Gson();
                LoginResultFilter loginResultFilter = gson.fromJson(gson.toJson(content), LoginResultFilter.class);
                loginResultFilter.setSessionId(ticket);
                Utils.addSessionAndCookie(httpSession, response, loginResultFilter, null);
            } else {
                CookieManage.deleteCookie(response, "aspxUserTicket", "/", "fangxiaoer.com");
                CookieManage.deleteCookie(response, "aspxUserTicket", "/", null);
            }
        }
        else if (!StringUtils.isEmpty(sessionId) && StringUtils.isEmpty(ticket)) {
            httpSession.invalidate();
        }
    }



    public boolean checkPcOrMoblie(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String userAgent = request.getHeader("user-agent");
        String urlString = request.getRequestURI();
        if (StringUtils.isEmpty(userAgent) || StringUtils.isEmpty(urlString)) return true;
        if (userAgent.toLowerCase().indexOf("android") != -1 || userAgent.toLowerCase().indexOf("iphone") != -1) {
            response.sendRedirect("https://m.fangxiaoer.com" + urlString);
            return false;
        }
        return true;
    }

    public boolean checkFrequence(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException, ServletException {
        String url = request.getRequestURI();
        boolean checkPOST = false;
        if (url.startsWith("/intermediary") || url.startsWith("/temp")
                || url.startsWith("/verifyBlocker") || url.startsWith("/error")
                || url.startsWith("/emulator") || url.startsWith("/block")) {
            return true;
        } else if (request.getMethod().equalsIgnoreCase("POST")) {
            String sessionWarrenty = (String) request.getSession().getAttribute("warrenty");
            Cookie cookieWarrenty = Utils.iterationCookie(request, "warrenty");
            if(sessionWarrenty == null || cookieWarrenty == null) {
                checkPOST = true;
            } else if(!sessionWarrenty.equals(cookieWarrenty.getValue())) {
                checkPOST = true;
            }else{
                return true;
            }
        } else if (!(handler instanceof HandlerMethod) || ((HandlerMethod) handler).getMethod().getReturnType().getName() != "java.lang.String") {
            return true;
        }
        String ua = request.getHeader("user-agent");
        String ip = Utils.getIpAddr(request);
        if(checkPOST){
            logger.error("Block,outside POST: " + ip + ": " + ua);
            return false;
        }
        // 如果自称是蜘蛛，检查一下ip归属地，确实是蜘蛛就放行，否则直接进小黑屋
        if (IPUtil.isSpiderUa(ua)) {
            if (IPUtil.isSpiderIp(ua, ip)) {
                return true;
            } else {
                request.getRequestDispatcher("/error").forward(request, response);
                logger.error("Block, fake spider: " + ip + ": " + ua);
                return false;
            }
        }

        IPMonitor.BlockNode result = monitor.validateIP(ip);
        // 没被拦截，弹出
        if (result == null || result.getLevel() == IPMonitor.BLOCK_LEVEL.ALLOW) return true;

        HttpSession session = request.getSession();
        session.setAttribute("fUid", result.getUid());
        String key = result.getUid();
        String dateString = DateTime.now().toString("yyyyMMdd");

        logger.info(result.toString());

        if (result.getLevel() == IPMonitor.BLOCK_LEVEL.JS_VERIFY || verfiyIp(ip) == IP_TYPE_PROVINCE) {
            // 弹出js页面进行简单校验，如果是辽宁省内，爬虫攻击可能性不太大，仅进行如下拦截
            String jsVerifyMD5 = Utils.MD5encode(ip + "-" + dateString + "-" + key + "-" + ua);
            if (session.getAttribute("fVerify") == null || !jsVerifyMD5.equals(session.getAttribute("fVerify"))) {
                Utils.addCookie(response, "fxr-jsvid", jsVerifyMD5, "/");
                request.getRequestDispatcher("/temp").forward(request, response);
                logger.info("Block, Js verify : " + ip + ": " + ua);
                return false;
            }
        } else if (result.getLevel() == IPMonitor.BLOCK_LEVEL.CODE_VERIFY) {
            // 弹出验证码页面进行校验
            String codeVerifyMD5 = Utils.MD5encode(key + ip + "-" + dateString + "-" + "yz.fangxiaoer.com" + "-" + ua);
            Cookie code = Utils.iterationCookie(request, "fxr-covid");
            if (code == null || !codeVerifyMD5.equals(code.getValue())) {
                request.getRequestDispatcher("/block").forward(request, response);
                logger.info("Block, code block : " + ip + ": " + ua);
                return false;
            }
        } else if (result.getLevel() == IPMonitor.BLOCK_LEVEL.BLOCK) {
            // 非常严重，对此ip进行封停
            request.getRequestDispatcher("/error").forward(request, response);
            logger.info("Block, other province : " + ip + ": " + ua);
            return false;
        }
        return true;
    }

    public static int IP_TYPE_NO_MATCH = 0;
    public static int IP_TYPE_PROVINCE = 2;


    String[] provinceList = {"辽宁"};

    public int verfiyIp(String ip) {
        Pattern p = Pattern.compile("^(\\d+\\.\\d+\\.\\d+\\.)\\d+$");
        Matcher m = p.matcher(ip);
        if (!m.matches()) return IP_TYPE_NO_MATCH;
        String headIp = m.group(1);
        HashMap<String, Object> cache = HttpUtil.connectApi(Constants.CACHE_READ_OBJ, new Params("key", "source:" + headIp).get());
        if (cache.size() == 0) return IP_TYPE_NO_MATCH;
        String source = (String) cache.get("content");
        if (StringUtils.isEmpty(source)) {
            try {
                String url = "https://tools.fangxiaoer.com/tools/ip?ip=" + ip;
                Response r = HttpUtil.client.newCall(new Request.Builder().url(url).build()).execute();
                String body = r.body().string();
                logger.error("==========" + body);
                source = JSONObject.parseObject(body).getString("content");
                HttpUtil.connectApi(Constants.CACHE_SAVE_OBJ, new Params("key", "source:" + headIp).add("values", source).add("minutes", 60 * 24).get());
            } catch (Exception e) {
                return IP_TYPE_NO_MATCH;
            }
        }

        for (String key : provinceList) {
            if (source.contains(key)) {
                return IP_TYPE_PROVINCE;
            }
        }
        return IP_TYPE_NO_MATCH;
    }
}
