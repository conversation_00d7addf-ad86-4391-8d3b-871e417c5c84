package com.fangxiaoer.common;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;
import org.joda.time.DateTime;

import java.io.File;
import java.io.FileWriter;
import java.io.FilenameFilter;
import java.util.Iterator;
import java.util.List;

/**
 * Created by <PERSON>yn on 2017/10/16.
 */
public class SiteMapUtil {

    private static String uri = SiteMapUtil.class.getClassLoader().getResource("").getPath() +
            "static" + File.separator;

    private static int index = 1;
    public static void init() {
        index = 1;
        removeSiteFiles();
    }

    public static boolean createIndexXml() {
        String date = new DateTime().toString("YYYY-MM-dd");
        Document document = DocumentHelper.createDocument();
        Element root = document.addElement("sitemapindex", "https://www.sitemaps.org/schemas/sitemap/0.9");
        for (int i = 1; i < index; i++) {
            Element url = root.addElement("sitemap");
            Element loc = url.addElement("loc");
            loc.setText(String.format("https://sy.fangxiaoer.com/map%d.xml", i));
            Element lastmod = url.addElement("lastmod");
            lastmod.setText(date);
        }
        try {
            saveDocument(document,uri + "sitemap.xml");
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
    }

    public static boolean createXml(List<String> links){
        if(links == null || links.size() == 0) return false;
        String date = new DateTime().toString("YYYY-MM-dd");
        Document document = DocumentHelper.createDocument();
        Element root = document.addElement("urlset", "https://www.sitemaps.org/schemas/sitemap/0.9");
        for (String link : links) {
            Element url = root.addElement("url");
            Element loc = url.addElement("loc");
            loc.setText(link);
            Element lastmod = url.addElement("lastmod");
            lastmod.setText(date);
            Element changeFreq = url.addElement("changefreq");
            changeFreq.setText("daily");
            Element priority = url.addElement("priority");
            priority.setText("1.0");
        }
        try {
            saveDocument(document,uri + String.format("map%d.xml", index++));
            return true;
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        } finally {
            links.clear();
        }

    }

    private static void removeFile(String url){
        File file = new File(uri + url);
        if(file.exists()&&file.isFile()){
            file.delete();
        }
    }

    private static void removeSiteFiles(){
        File folder = new File(uri);
        if(folder.exists() && folder.isDirectory()){
            File[] files = folder.listFiles((File file) -> file.getName().matches("^map\\d+.xml"));
            for(File file: files) {
                file.delete();
            }
        }
        removeFile("sitemap.xml");
    }

    private static void saveDocument(Document document,String fileName) throws Exception{
        try {
            XMLWriter writer = new XMLWriter(new FileWriter(new File(fileName)));
            writer.write(document);
            writer.close();
        }catch (Exception ex){
            ex.getMessage();
        }
    }
}
