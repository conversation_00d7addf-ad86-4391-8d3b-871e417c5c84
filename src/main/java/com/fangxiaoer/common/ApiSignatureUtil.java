package com.fangxiaoer.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.util.Base64;
import java.util.concurrent.ConcurrentHashMap;


public class ApiSignatureUtil {
    private static final Logger logger = LoggerFactory.getLogger(ApiSignatureUtil.class);
    private static final long SIGNATURE_EXPIRE_TIME = 300;
    private static final String SIGNATURE_SECRET = "c56A26Qtr4";

    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final ConcurrentHashMap<String, Long> NONCE_STORE = new ConcurrentHashMap<>();




    public static void checkSignature(HttpServletRequest request){
        String signature = request.getHeader("X-Signature");
        String timestamp = request.getHeader("X-Timestamp");
        String nonce = request.getHeader("X-Nonce");
        String method = request.getMethod();
        String path = request.getRequestURI();
        String clientIp = request.getRemoteAddr();

        // 日志记录基本请求信息
        logger.info("Received request - Method: {}, Path: {}, IP: {}", method, path, clientIp);
        logger.warn("Validated web-request - signature: {}, timestamp: {}, nonce: {}", signature, timestamp, nonce);

        if (signature == null || timestamp == null || nonce == null) {
            logger.warn("Missing signature headers - Method: {}, Path: {}, IP: {}", method, path, clientIp);
            ValidateUtil.throwException("Missing signature headers");
        }

        // 校验时间戳
        long requestTime = Long.parseLong(timestamp);
        if (Math.abs(System.currentTimeMillis() / 1000 - requestTime / 1000) > SIGNATURE_EXPIRE_TIME) {
            logger.warn("Signature expired - Method: {}, Path: {}, IP: {}, Timestamp: {}", method, path, clientIp, timestamp);
            logger.warn("Signature expired - Method: {}, Path: {}, IP: {}, requestTime: {}, currentTime: {}", method, path, clientIp, requestTime, System.currentTimeMillis());
            ValidateUtil.throwException("Signature expired");
        }

        // 校验 Nonce（防重放攻击）
        if (NONCE_STORE.containsKey(nonce)) {
            logger.warn("Nonce already used - Method: {}, Path: {}, IP: {}, Nonce: {}", method, path, clientIp, nonce);
            ValidateUtil.throwException("Nonce already used");
        }
        NONCE_STORE.put(nonce, System.currentTimeMillis());

        // 校验签名
        String secret = SIGNATURE_SECRET;
        String query = request.getQueryString();
        String body = "";
        String dataToSign = method + "\n" + path + "\n" + (query == null ? "" : query) + "\n" + body + "\n" + timestamp + "\n" + nonce;
        String generatedSignature = generateSignature(dataToSign, secret);


        if (!signature.equals(generatedSignature)) {
            logger.warn("Invalid signature - Method: {}, Path: {}, IP: {}, Signature: {}", method, path, clientIp, signature);
            logger.warn("dataToSign : {}", dataToSign);
            logger.warn("excepted : {}, received : {}", generatedSignature, signature);
            ValidateUtil.throwException("签名不正确");
        }

        logger.info("Signature validated successfully - Method: {}, Path: {}, IP: {}", method, path, clientIp);
    }

    public static String generateSignature(String data, String secret) {
        try {
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(), HMAC_SHA256);
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(hmacBytes);
        } catch (Exception e) {
            logger.error("Failed to generate signature.", e);
            throw new RuntimeException("Failed to generate signature.", e);
        }
    }
}
