package com.fangxiaoer.common;

import com.fangxiaoer.config.RabbitMQConfig;

/**
 * <AUTHOR>
 */
public class Logger {
    private org.slf4j.Logger logger;

    public Logger(org.slf4j.Logger logger) {
        this.logger = logger;
    }

    public void debug(String log) {
        logger.debug(log);
    }

    public void info(String log) {
        logger.info(log);
    }

    public void info(String format, Object... args) {
        logger.info(format, args);
    }

    public void error(String log) {
        logger.error(log);
        //EmailProcessor.getInstance().pushError(log);
        RabbitMQConfig.sendErrEmailMsg(log);
    }

    public void error(String log, Throwable ex) {
        logger.error(log, ex);
        String errorStr = "";
        if(ex != null) {
            StackTraceElement[] sts = ex.getStackTrace();
            errorStr += ex.getClass().getName() + ":" + ex.getMessage();
            for (StackTraceElement s : sts) {
                errorStr += "\n" + s.toString();
            }
        }
        //EmailProcessor.getInstance().pushError( log + "</br>\n" + errorStr);
        RabbitMQConfig.sendErrEmailMsg(log + "</br>\n" + errorStr);
    }

    public void warn(String log) {
        logger.warn(log);
    }
}
