package com.fangxiaoer.common;

import org.springframework.util.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;

/**
 *  有关cookie的使用方法
 * Created by Administrator on 2017/6/8.
 */
public class CookieManage {
    /**
     * 添加cookie
     *  金伟英 2017/6/8 15:44:16
     * @param response 设置
     * @param key      用户对应cookie名称
     * @param keyValue cookie值
     */
    public static void addCookie(HttpServletResponse response, String key, String keyValue, String cookiePath, String domain) {
        Cookie cookie = new Cookie(key, keyValue);
        int maxAge = 86400;
        cookie.setPath(cookiePath);
        cookie.setMaxAge(maxAge);
        if (!StringUtils.isEmpty(domain)) {
            cookie.setDomain(domain);
        }
        response.addCookie(cookie);
    }

    public static void addCookie(HttpServletResponse response, String key, String keyValue, String cookiePath, String domain, boolean encode) {
        try {
            keyValue = encode ? URLEncoder.encode(keyValue, "utf-8") : keyValue;
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        addCookie(response, key, keyValue, cookiePath, domain);
    }


    /**
     * 添加cookie 图片验证码
     * @param response
     * @param name
     * @param value
     */
    public static void addCookie(HttpServletResponse response, String name, String value){
        Cookie cookie = new Cookie(name.trim(), value.trim());
        cookie.setMaxAge(7*24*60*60);//验证码保存一周
        cookie.setPath("/");
        response.addCookie(cookie);
    }

    /**
     * 迭代Cookie
     *  金伟英 2017/6/8 15:44:16
     * @param key 用户对应的cookie key
     * @return 用户对应的cookie值
     */
    public static String iterationCookie(HttpServletRequest request, String key) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(key)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    public static String iterationCookie(HttpServletRequest request, String key, boolean decode) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(key)) {
                    String cookieValue = cookie.getValue();
                    try {
                        cookieValue = decode ? URLDecoder.decode(cookie.getValue() , "utf-8") : cookie.getValue();
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                    return cookieValue;
                }
            }
        }
        return null;
    }

    /**
     * 删除 cookies
     * @param response response
     */
    public static void deleteCookie(HttpServletResponse response, String key, String path, String domain) {
        Cookie cookie = new Cookie(key, "");
        cookie.setMaxAge(0);
        cookie.setPath(path);
        if(!StringUtils.isEmpty(domain)) {
            cookie.setDomain(domain);
        }
        response.addCookie(cookie);
    }
    /**
     * 转码
     * @param src
     * @return
     */
        public static String escape(String src) {
            int i;
            char j;
            StringBuffer tmp = new StringBuffer();
            tmp.ensureCapacity(src.length() * 6);
            for (i = 0; i < src.length(); i++) {
                j = src.charAt(i);
                if (Character.isDigit(j) || Character.isLowerCase(j)
                        || Character.isUpperCase(j))
                    tmp.append(j);
                else if (j < 256) {
                    tmp.append("%");
                    if (j < 16)
                        tmp.append("0");
                    tmp.append(Integer.toString(j, 16));
                } else {
                    tmp.append("%u");
                    tmp.append(Integer.toString(j, 16));
                }
            }
            return tmp.toString();
        }

    /**
     * 解码
     * @param src
     * @return
     */
    public static String unescape(String src) {
            StringBuffer tmp = new StringBuffer();
            tmp.ensureCapacity(src.length());
            int lastPos = 0, pos = 0;
            char ch;
            while (lastPos < src.length()) {
                pos = src.indexOf("%", lastPos);
                if (pos == lastPos) {
                    if (src.charAt(pos + 1) == 'u') {
                        ch = (char) Integer.parseInt(src
                                .substring(pos + 2, pos + 6), 16);
                        tmp.append(ch);
                        lastPos = pos + 6;
                    } else {
                        ch = (char) Integer.parseInt(src
                                .substring(pos + 1, pos + 3), 16);
                        tmp.append(ch);
                        lastPos = pos + 3;
                    }
                } else {
                    if (pos == -1) {
                        tmp.append(src.substring(lastPos));
                        lastPos = src.length();
                    } else {
                        tmp.append(src.substring(lastPos, pos));
                        lastPos = pos;
                    }
                }
            }
            return tmp.toString();
        }

    /**
     * @disc 对字符串重新编码
     * @param src
     * @return
     */
    public static String isoToGB(String src) {
        String strRet = null;
        try {
            strRet = new String(src.getBytes("ISO_8859_1"), "GB2312");
        } catch (Exception e) {

        }
        return strRet;
    }

    /**
     * @disc 对字符串重新编码
     * @param src
     * @return
     */
    public static String isoToUTF(String src) {
        String strRet = null;
        try {
            strRet = new String(src.getBytes("ISO_8859_1"), "UTF-8");
        } catch (Exception e) {

        }
        return strRet;
    }
}
