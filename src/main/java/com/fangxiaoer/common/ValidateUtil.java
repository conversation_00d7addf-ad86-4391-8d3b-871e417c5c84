package com.fangxiaoer.common;

import org.springframework.util.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/4/20.
 */

public class ValidateUtil {

    public static void checkNull(String key, Object value) {
        if (StringUtils.isEmpty(value)) {
            throwException(key + " could not be null");
        }
    }

    public static void throwException(String msg) {
        throw new RuntimeException(msg);
    }

    /**
     * 判断String变量是否是数字
     * @param str
     * @return
     */
    public static boolean isNumber(String str){
        for(int i=0;i<str.length();i++){
            if(!Character.isDigit(str.charAt(i))){
               return false;
            }
        }
        return true;
    }
}