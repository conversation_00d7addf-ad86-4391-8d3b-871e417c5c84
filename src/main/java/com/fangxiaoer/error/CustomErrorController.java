package com.fangxiaoer.error;

import com.fangxiaoer.common.Logger;
import com.fangxiaoer.common.LoggerFactory;
import com.google.common.base.Throwables;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorController;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.MessageFormat;

@Controller
class CustomErrorController implements ErrorController {

	Logger logger = LoggerFactory.getLogger(CustomErrorController.class);
	private static final String ERROR_PATH = "/error";
	/**
	 * Display an error page, as defined in web.xml <code>custom-error</code> element.
	 */
	@RequestMapping(ERROR_PATH)
	public String generalError(HttpServletRequest request, HttpServletResponse response, Model model) {
		// retrieve some useful information from the request
		Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
		Throwable throwable = (Throwable) request.getAttribute("javax.servlet.error.exception");
		// String servletName = (String) request.getAttribute("javax.servlet.error.servlet_name");
		String exceptionMessage = getExceptionMessage(throwable);
		
		String requestUri = (String) request.getAttribute("javax.servlet.error.request_uri");
		String ua = request.getHeader("User-Agent");
		if (requestUri == null) {
			requestUri = "Unknown";
		}
		String message = MessageFormat.format("{0} returned for {1} with message {2}", 
			statusCode, requestUri, exceptionMessage
		);
		model.addAttribute("msg", message);
		if(statusCode != null && statusCode == 404){
			return "redirect:/";
		} else if(statusCode == null && throwable == null) {
			// do nothing
		} else {
			logger.error(requestUri + " " + ua + message, throwable);
		}
        return "error/error";
	}

	private String getExceptionMessage(Throwable throwable) {
		String rootCauseStr = "unknown error";
		if (throwable != null) {
			Throwable rootCause = Throwables.getRootCause(throwable);
			if(rootCause != null && rootCause.getMessage() != null) {
				rootCauseStr = rootCause.getMessage();
			} else if(rootCause != null) {
				rootCauseStr = rootCause.toString();
			}
			try {
				rootCauseStr = URLEncoder.encode(rootCauseStr, "utf-8");
			} catch (UnsupportedEncodingException e) {
			} catch (NullPointerException e) {
			}
		}
		return rootCauseStr;
	}

	@Override
	public String getErrorPath() {
		return ERROR_PATH;
	}
}
