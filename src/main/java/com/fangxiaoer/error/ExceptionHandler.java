package com.fangxiaoer.error;

import com.fangxiaoer.common.Logger;
import com.fangxiaoer.common.LoggerFactory;
import com.google.common.base.Throwables;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.http.HttpStatus;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * General error handler for the application.
 */
@ControllerAdvice
class ExceptionHandler {

    private Logger logger = LoggerFactory.getLogger(ExceptionHandler.class);

    /**
     * Handle exceptions thrown by handlers.
     */
    @org.springframework.web.bind.annotation.ExceptionHandler(value = Exception.class)
    public ModelAndView exception(Exception exception, HttpServletRequest request) {
        {
            String requestUri = request.getRequestURI();
            String ua = request.getHeader("User-Agent");
            Throwable rootCause = Throwables.getRootCause(exception);
            String rootCauseStr = "unknown error";
            if (rootCause != null && rootCause.getMessage() != null) {
                rootCauseStr = rootCause.getMessage();
            } else if (rootCause != null) {
                rootCauseStr = rootCause.toString();
            }
            try {
                if(rootCauseStr.equals(new String(rootCauseStr.getBytes("ISO-8859-1"), "ISO-8859-1"))) {
                    rootCauseStr = new String(rootCauseStr.getBytes("ISO-8859-1"), "UTF-8");
                }
            } catch (UnsupportedEncodingException e) {
            } catch (NullPointerException e) {
            }
            if(exception instanceof NoDataException) {
                ModelAndView modelAndView = new ModelAndView("error/noData");
                modelAndView.addObject("msg", rootCauseStr);
                modelAndView.setStatus(HttpStatus.NOT_FOUND);
                return modelAndView;
            }

            if (!(exception instanceof HttpRequestMethodNotSupportedException)
                    && !(exception instanceof MethodArgumentTypeMismatchException)
                    && !(exception instanceof ClientAbortException)) {
                logger.error("Catch error: " + requestUri + ", " + ua, exception);
            }
            ModelAndView modelAndView = new ModelAndView("error/error");
            modelAndView.addObject("msg", rootCauseStr);
            return modelAndView;
        }
    }
}