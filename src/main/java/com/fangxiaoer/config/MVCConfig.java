package com.fangxiaoer.config;

import com.fangxiaoer.common.AllInterceptor;
import com.fangxiaoer.common.IPMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

@Configuration
public class MVCConfig extends WebMvcConfigurerAdapter {

    @Autowired
    IPMonitor ipMonitor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AllInterceptor(ipMonitor))
                .excludePathPatterns("/error","/**/*.css","/**/*.js","/**/*.png","/**/*.jpg","/**/*.jpeg","/**/*.gif")
                .addPathPatterns("/**");
        super.addInterceptors(registry);
    }
}
