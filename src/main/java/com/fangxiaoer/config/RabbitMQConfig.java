package com.fangxiaoer.config;

import com.fangxiaoer.common.Constants;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: big power
 * @date: Created in 2021-08-17 14:04
 * @description: rabbit mq 配置类
 * @version: v1.0
 */
@Configuration
public class RabbitMQConfig {

    @Autowired
    RabbitTemplate rabbit;

    static RabbitTemplate rabbitTemplate = null;

    @PostConstruct
    public void setRabbitTemplate() {
        this.rabbitTemplate = this.rabbit;
    }


    //发送异常信息到邮箱 交换机
    public final static String EXCHANGE_TEXT = "message.text";
    //发送异常信息 路由
    public final static String ROUTE_TEXT_EMAIL = "email";

    //声明Direct交换机
    @Bean(EXCHANGE_TEXT)
    public Exchange errEmailExchange() {
        return new DirectExchange(EXCHANGE_TEXT, true, false);
    }

    //声明队列
    @Bean(ROUTE_TEXT_EMAIL)
    public Queue errEmailQueue() {
        return new Queue(EXCHANGE_TEXT + "." + ROUTE_TEXT_EMAIL, true);
    }

    //交换机-队列绑定
    @Bean
    public Binding bindDirect(@Qualifier(ROUTE_TEXT_EMAIL)Queue queue, @Qualifier(EXCHANGE_TEXT)Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTE_TEXT_EMAIL).noargs();
    }

    /**
     * 发送异常信息邮件
     *
     * @param errorMsg 异常信息
     */
    public static void sendErrEmailMsg(String errorMsg) {
        if (!Constants.HOST.contains("ltapi")) return;
        Map<String, Object> map = new HashMap<>();
        map.put("station", "SY");
        map.put("errorMsg", errorMsg);
        rabbitTemplate.convertAndSend(EXCHANGE_TEXT, ROUTE_TEXT_EMAIL, map);
    }
}
