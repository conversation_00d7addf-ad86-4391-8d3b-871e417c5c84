package com.fangxiaoer.model;

import java.io.Serializable;

public class WeizanSearch implements Serializable {

    private Integer id;
    private String title;
    private String classfiy_id;
    private String topic_type;
    private String status;
    private String cid;
    private String addtime_start;
    private String addtime_end;
    private int page = 1;
    private int page_size = 10;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getClassfiy_id() {
        return classfiy_id;
    }

    public void setClassfiy_id(String classfiy_id) {
        this.classfiy_id = classfiy_id;
    }

    public String getTopic_type() {
        return topic_type;
    }

    public void setTopic_type(String topic_type) {
        this.topic_type = topic_type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getAddtime_start() {
        return addtime_start;
    }

    public void setAddtime_start(String addtime_start) {
        this.addtime_start = addtime_start;
    }

    public String getAddtime_end() {
        return addtime_end;
    }

    public void setAddtime_end(String addtime_end) {
        this.addtime_end = addtime_end;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPage_size() {
        return page_size;
    }

    public void setPage_size(int page_size) {
        this.page_size = page_size;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("WeizanSearch{");
        sb.append("id=").append(id);
        sb.append(", title='").append(title).append('\'');
        sb.append(", classfiy_id=").append(classfiy_id);
        sb.append(", topic_type=").append(topic_type);
        sb.append(", status=").append(status);
        sb.append(", cid=").append(cid);
        sb.append(", addtime_start='").append(addtime_start).append('\'');
        sb.append(", addtime_end='").append(addtime_end).append('\'');
        sb.append(", page=").append(page);
        sb.append(", page_size=").append(page_size);
        sb.append('}');
        return sb.toString();
    }
}
