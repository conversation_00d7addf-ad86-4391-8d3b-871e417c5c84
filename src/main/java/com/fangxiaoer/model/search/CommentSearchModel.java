package com.fangxiaoer.model.search;

/**
 * Created by le<PERSON><PERSON> on 2017/5/26.
 */
public class CommentSearchModel {
    private Integer isYezhu;
    private Integer isPic;
    private Integer pageIndex;
    private Integer pageSize;
    private Integer projectId;
    private Integer Flaws;
    private Integer Virtues;
    private Integer Huxing;
    private Integer Property;
    private Integer Space;
    private Integer Supporting;
    private Integer Ratio;

    public Integer getFlaws() {
        return Flaws;
    }

    public void setFlaws(Integer flaws) {
        Flaws = flaws;
    }

    public Integer getVirtues() {
        return Virtues;
    }

    public void setVirtues(Integer virtues) {
        Virtues = virtues;
    }

    public Integer getHuxing() {
        return Huxing;
    }

    public void setHuxing(Integer huxing) {
        Huxing = huxing;
    }

    public Integer getProperty() {
        return Property;
    }

    public void setProperty(Integer property) {
        Property = property;
    }

    public Integer getSpace() {
        return Space;
    }

    public void setSpace(Integer space) {
        Space = space;
    }

    public Integer getSupporting() {
        return Supporting;
    }

    public void setSupporting(Integer supporting) {
        Supporting = supporting;
    }

    public Integer getRatio() {
        return Ratio;
    }

    public void setRatio(Integer ratio) {
        Ratio = ratio;
    }

    public Integer getIsYezhu() {
        return isYezhu;
    }

    public void setIsYezhu(Integer isYezhu) {
        this.isYezhu = isYezhu;
    }

    public Integer getIsPic() {
        return isPic;
    }

    public void setIsPic(Integer isPic) {
        this.isPic = isPic;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }
}
