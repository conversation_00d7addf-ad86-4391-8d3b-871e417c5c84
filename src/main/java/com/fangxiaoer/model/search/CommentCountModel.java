package com.fangxiaoer.model.search;

/***
 *@Author: CDS
 *@Description:
 *@Data: Create in 14:392017/10/30
 *
 **/
public class CommentCountModel {
    private String projectId;
    private String Flaws;
    private String Virtues;
    private String Huxing;
    private String Property;
    private String Space;
    private String Supporting;
    private String Ratio;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getFlaws() {
        return Flaws;
    }

    public void setFlaws(String flaws) {
        Flaws = flaws;
    }

    public String getVirtues() {
        return Virtues;
    }

    public void setVirtues(String virtues) {
        Virtues = virtues;
    }

    public String getHuxing() {
        return Huxing;
    }

    public void setHuxing(String huxing) {
        Huxing = huxing;
    }

    public String getProperty() {
        return Property;
    }

    public void setProperty(String property) {
        Property = property;
    }

    public String getSpace() {
        return Space;
    }

    public void setSpace(String space) {
        Space = space;
    }

    public String getSupporting() {
        return Supporting;
    }

    public void setSupporting(String supporting) {
        Supporting = supporting;
    }

    public String getRatio() {
        return Ratio;
    }

    public void setRatio(String ratio) {
        Ratio = ratio;
    }
}
