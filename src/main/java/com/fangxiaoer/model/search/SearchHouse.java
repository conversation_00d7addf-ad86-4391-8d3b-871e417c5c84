package com.fangxiaoer.model.search;

import com.fangxiaoer.model.base.MyPageable;

/**
 * Created by Administrator on 2017/5/17.
 */
public class SearchHouse extends MyPageable {

    private String search;
    private String key;
    private Integer page;
    private Integer pageSize;
    private Integer tab = 1;

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getTab() {
        return tab;
    }

    public void setTab(Integer tab) {
        this.tab = tab;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
