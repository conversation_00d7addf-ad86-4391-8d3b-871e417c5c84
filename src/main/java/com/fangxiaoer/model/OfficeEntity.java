package com.fangxiaoer.model;

import java.util.List;

/**
 * Created by Administrator on 2018/1/24.
 */
public class OfficeEntity {
    private String shopid;
    private String shoptype;
    private String regionid;
    private String plateid;
    private String address;
    private String officename;
    private String isregist;
    private String shopcategories;
    private String floor;
    private String totalfloornumber;
    private String truearea;
    private String iscut;
    private String unitprice;
    private String containfee;
    private String price;
    private String payment;
    private String propertyfee;//物业费
    private String fitmenttype;
    private String mindustry;//配套设施
    private String housetrait;
    private String houseowner;
    private String title;
    private String describe;
    private String phone;
    private String userName;
    private String pass;
    private List<PicEntity> picEntityList;
    private String imglist;
    private String imgvalue;
    private String officeid;
    private Integer showtuindex;

    public String getOfficeid() {
        return officeid;
    }

    public void setOfficeid(String officeid) {
        this.officeid = officeid;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPass() {
        return pass;
    }

    public void setPass(String pass) {
        this.pass = pass;
    }

    public String getShopid() {
        return shopid;
    }

    public void setShopid(String shopid) {
        this.shopid = shopid;
    }

    public String getShoptype() {
        return shoptype;
    }

    public void setShoptype(String shoptype) {
        this.shoptype = shoptype;
    }

    public String getRegionid() {
        return regionid;
    }

    public void setRegionid(String regionid) {
        this.regionid = regionid;
    }

    public String getPlateid() {
        return plateid;
    }

    public void setPlateid(String plateid) {
        this.plateid = plateid;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOfficename() {
        return officename;
    }

    public void setOfficename(String officename) {
        this.officename = officename;
    }

    public String getIsregist() {
        return isregist;
    }

    public void setIsregist(String isregist) {
        this.isregist = isregist;
    }

    public String getShopcategories() {
        return shopcategories;
    }

    public void setShopcategories(String shopcategories) {
        this.shopcategories = shopcategories;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getTotalfloornumber() {
        return totalfloornumber;
    }

    public void setTotalfloornumber(String totalfloornumber) {
        this.totalfloornumber = totalfloornumber;
    }

    public String getTruearea() {
        return truearea;
    }

    public void setTruearea(String truearea) {
        this.truearea = truearea;
    }

    public String getIscut() {
        return iscut;
    }

    public void setIscut(String iscut) {
        this.iscut = iscut;
    }

    public String getUnitprice() {
        return unitprice;
    }

    public void setUnitprice(String unitprice) {
        this.unitprice = unitprice;
    }

    public String getContainfee() {
        return containfee;
    }

    public void setContainfee(String containfee) {
        this.containfee = containfee;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public String getPropertyfee() {
        return propertyfee;
    }

    public void setPropertyfee(String propertyfee) {
        this.propertyfee = propertyfee;
    }

    public String getFitmenttype() {
        return fitmenttype;
    }

    public void setFitmenttype(String fitmenttype) {
        this.fitmenttype = fitmenttype;
    }

    public String getMindustry() {
        return mindustry;
    }

    public void setMindustry(String mindustry) {
        this.mindustry = mindustry;
    }

    public String getHousetrait() {
        return housetrait;
    }

    public void setHousetrait(String housetrait) {
        this.housetrait = housetrait;
    }

    public String getHouseowner() {
        return houseowner;
    }

    public void setHouseowner(String houseowner) {
        this.houseowner = houseowner;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public List<PicEntity> getPicEntityList() {
        return picEntityList;
    }

    public void setPicEntityList(List<PicEntity> picEntityList) {
        this.picEntityList = picEntityList;
    }

    public String getImglist() {
        if(picEntityList != null && picEntityList.size() != 0){
            StringBuffer sf = new StringBuffer();
            for (PicEntity picEntity : picEntityList) {
                sf.append(picEntity.getSmallimageurl()).append(",");
            }
            return sf.toString().replaceAll("http://images.fangxiaoer.com/sy/esf/fy/middle/","").replaceAll("https://images.fangxiaoer.com/sy/esf/fy/middle/","")
                    .replaceAll("noimage375275.jpg","").replaceAll(",+$","");
        }
        return imglist;
    }

    public void setImglist(String imglist) {
        this.imglist = imglist;
    }

    public String getImgvalue() {
        if(picEntityList != null && picEntityList.size() != 0){
            StringBuffer sf = new StringBuffer();
            for (PicEntity picEntity : picEntityList) {
                sf.append(picEntity.getSmallimageurl()).append(",");
            }
            return sf.toString().replaceAll("http://images.fangxiaoer.com/sy/esf/fy/middle/","").replaceAll("https://images.fangxiaoer.com/sy/esf/fy/middle/","")
                    .replaceAll("noimage375275.jpg","").replaceAll(",+$","");
        }
        return imgvalue;
    }

    public void setImgvalue(String imgvalue) {
        this.imgvalue = imgvalue;
    }

    public Integer getShowtuindex() {
        if (picEntityList != null && picEntityList.size() != 0) {
            int i = 0;
            for (PicEntity picEntity : picEntityList) {
                if (picEntity.getIsdefault().equals("1")) {
                    return i;
                }
                i++;
            }
        }
        return showtuindex;
    }

    public void setShowtuindex(Integer showtuindex) {
        this.showtuindex = showtuindex;
    }
}

