package com.fangxiaoer.model;

/***
 *@Author: CDS
 *@Description:
 *@Data: Create in 9:422018-06-19
 *
 **/
public class HousingPriceEntity {
    private Integer subId;

    private Integer roomId;

    private Integer forwardType;

    private String area;

    private Integer currentFloor;

    private Integer totalFloor;

    public Integer getSubId() {
        return subId;
    }

    public void setSubId(Integer subId) {
        this.subId = subId;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public Integer getForwardType() {
        return forwardType;
    }

    public void setForwardType(Integer forwardType) {
        this.forwardType = forwardType;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public Integer getCurrentFloor() {
        return currentFloor;
    }

    public void setCurrentFloor(Integer currentFloor) {
        this.currentFloor = currentFloor;
    }

    public Integer getTotalFloor() {
        return totalFloor;
    }

    public void setTotalFloor(Integer totalFloor) {
        this.totalFloor = totalFloor;
    }

    @Override
    public String toString() {
        return "HousingPriceEntity{" +
                "subId=" + subId +
                ", roomId=" + roomId +
                ", forwardType=" + forwardType +
                ", area=" + area +
                ", currentFloor=" + currentFloor +
                ", totalFloor=" + totalFloor +
                '}';
    }
}
