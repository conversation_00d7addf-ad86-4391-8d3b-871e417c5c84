package com.fangxiaoer.model;

import java.io.Serializable;

public class SubBean implements Serializable {

    private Integer subId;
    private Integer saleCount;
    private Integer rentCount;
    private Integer picCount;
    private Integer exCount;

    public Integer getSubId() {
        return subId;
    }

    public void setSubId(Integer subId) {
        this.subId = subId;
    }

    public Integer getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(Integer saleCount) {
        this.saleCount = saleCount;
    }

    public Integer getRentCount() {
        return rentCount;
    }

    public void setRentCount(Integer rentCount) {
        this.rentCount = rentCount;
    }

    public Integer getPicCount() {
        return picCount;
    }

    public void setPicCount(Integer picCount) {
        this.picCount = picCount;
    }

    public Integer getExCount() {
        return exCount;
    }

    public void setExCount(Integer exCount) {
        this.exCount = exCount;
    }
}
