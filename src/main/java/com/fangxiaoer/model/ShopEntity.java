package com.fangxiaoer.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Lyn on 2017/5/5.
 */
public class ShopEntity implements Serializable {
    private String houseid;

    private String shopid;

    private String addtime;

    private String updatetime;

    private String agencyid;

    private String title;

    private String address;

    private String regionid;

    private String plateid;

    private String platename;

    private String isdel;

    private String price;

    private String payment;

    private String truearea;

    private String shopstate;

    private String mhistory;

    private String mindustry;

    private String iscut;

    private String ownerphone;

    private String houseowner;

    private String tranfee;

    private String trantype;

    private String ishousepic;

    private String shoptype;

    private String totalvisitednum;

    private String todayvisitednum;

    private String shopcategories;

    private String membertype;

    private String state;

    private String publishedend;

    private String projectid;

    private String remark;

    private String isstick;

    private String sourceid;

    private String ischeck;

    private String describe;

    private List<PicEntity> picEntityList;

    private String imglist;
    private String imgvalue;
    private Integer showtuindex;

    private String housetrait;
    private String imgurl;

    public String getImgurl() {
        return imgurl;
    }

    public void setImgurl(String imgurl) {
        this.imgurl = imgurl;
    }
    public String getHouseid() {
        return houseid;
    }

    public void setHouseid(String houseid) {
        this.houseid = houseid;
    }

    public String getHousetrait() {
        return housetrait;
    }

    public void setHousetrait(String housetrait) {
        this.housetrait = housetrait;
    }

    public String getShopid() {
        return shopid;
    }

    public void setShopid(String shopid) {
        this.shopid = shopid;
    }

    public String getAddtime() {
        return addtime;
    }

    public void setAddtime(String addtime) {
        this.addtime = addtime;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getAgencyid() {
        return agencyid;
    }

    public void setAgencyid(String agencyid) {
        this.agencyid = agencyid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRegionid() {
        return regionid;
    }

    public void setRegionid(String regionid) {
        this.regionid = regionid;
    }

    public String getPlateid() {
        return plateid;
    }

    public void setPlateid(String plateid) {
        this.plateid = plateid;
    }

    public String getIsdel() {
        return isdel;
    }

    public void setIsdel(String isdel) {
        this.isdel = isdel;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public String getTruearea() {
        return truearea;
    }

    public void setTruearea(String truearea) {
        this.truearea = truearea;
    }

    public String getShopstate() {
        return shopstate;
    }

    public void setShopstate(String shopstate) {
        this.shopstate = shopstate;
    }

    public String getMhistory() {
        return mhistory;
    }

    public void setMhistory(String mhistory) {
        this.mhistory = mhistory;
    }

    public String getMindustry() {
        return mindustry;
    }

    public void setMindustry(String mindustry) {
        this.mindustry = mindustry;
    }

    public String getIscut() {
        return iscut;
    }

    public void setIscut(String iscut) {
        this.iscut = iscut;
    }

    public String getOwnerphone() {
        return ownerphone;
    }

    public void setOwnerphone(String ownerphone) {
        this.ownerphone = ownerphone;
    }

    public String getHouseowner() {
        return houseowner;
    }

    public void setHouseowner(String houseowner) {
        this.houseowner = houseowner;
    }

    public String getTranfee() {
        return tranfee;
    }

    public void setTranfee(String tranfee) {
        this.tranfee = tranfee;
    }

    public String getTrantype() {
        return trantype;
    }

    public void setTrantype(String trantype) {
        this.trantype = trantype;
    }

    public String getIshousepic() {
        return ishousepic;
    }

    public void setIshousepic(String ishousepic) {
        this.ishousepic = ishousepic;
    }

    public String getShoptype() {
        return shoptype;
    }

    public void setShoptype(String shoptype) {
        this.shoptype = shoptype;
    }

    public String getTotalvisitednum() {
        return totalvisitednum;
    }

    public void setTotalvisitednum(String totalvisitednum) {
        this.totalvisitednum = totalvisitednum;
    }

    public String getTodayvisitednum() {
        return todayvisitednum;
    }

    public void setTodayvisitednum(String todayvisitednum) {
        this.todayvisitednum = todayvisitednum;
    }

    public String getShopcategories() {
        return shopcategories;
    }

    public void setShopcategories(String shopcategories) {
        this.shopcategories = shopcategories;
    }

    public String getMembertype() {
        return membertype;
    }

    public void setMembertype(String membertype) {
        this.membertype = membertype;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPublishedend() {
        return publishedend;
    }

    public void setPublishedend(String publishedend) {
        this.publishedend = publishedend;
    }

    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsstick() {
        return isstick;
    }

    public void setIsstick(String isstick) {
        this.isstick = isstick;
    }

    public String getSourceid() {
        return sourceid;
    }

    public void setSourceid(String sourceid) {
        this.sourceid = sourceid;
    }

    public String getIscheck() {
        return ischeck;
    }

    public void setIscheck(String ischeck) {
        this.ischeck = ischeck;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public List<PicEntity> getPicEntityList() {
        return picEntityList;
    }

    public void setPicEntityList(List<PicEntity> picEntityList) {
        this.picEntityList = picEntityList;
    }

    public String getImglist() {
        if(picEntityList != null && picEntityList.size() != 0){
            StringBuffer sf = new StringBuffer();
            for (PicEntity picEntity : picEntityList) {
                sf.append(picEntity.getSmallimageurl()).append(",");
            }
            return sf.toString().replaceAll("http://images.fangxiaoer.com/sy/esf/fy/middle/","").replaceAll("https://images.fangxiaoer.com/sy/esf/fy/middle/","")
                    .replaceAll("noimage375275.jpg","").replaceAll(",+$","");
        }
        return imglist;
    }

    public void setImglist(String imglist) {
        this.imglist = imglist;
    }

    public String getImgvalue() {
        if(picEntityList != null && picEntityList.size() != 0){
            StringBuffer sf = new StringBuffer();
            for (PicEntity picEntity : picEntityList) {
                sf.append(picEntity.getSmallimageurl()).append(",");
            }
            return sf.toString().replaceAll("http://images.fangxiaoer.com/sy/esf/fy/middle/","").replaceAll("https://images.fangxiaoer.com/sy/esf/fy/middle/","")
                    .replaceAll("noimage375275.jpg","").replaceAll(",+$","");
        }
        return imgvalue;
    }

    public void setImgvalue(String imgvalue) {
        this.imgvalue = imgvalue;
    }

    public Integer getShowtuindex() {
        if (picEntityList != null && picEntityList.size() != 0) {
            int i = 0;
            for (PicEntity picEntity : picEntityList) {
                if (picEntity.getIsdefault().equals("1")) {
                    return i;
                }
                i++;
            }
        }
        return showtuindex;
    }

    public void setShowtuindex(Integer showtuindex) {
        this.showtuindex = showtuindex;
    }

    public String getPlatename() {
        return platename;
    }

    public void setPlatename(String platename) {
        this.platename = platename;
    }
}
