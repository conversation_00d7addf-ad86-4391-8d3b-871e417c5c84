package com.fangxiaoer.model;

import com.google.gson.internal.LinkedTreeMap;

import java.util.List;

/**
 * Created by Administrator on 2017/6/22.
 */
public class NewsDetail {

    private String author;
    private String updateTime;
    private String source;
    private String searchKey;
    private String title;
    private String visitedTimes;
    private String content;
    private String categoryName;
    private String keyWords;
    private String description;
    private String pic;
    private String authPic;
    private String des;
    private String sortTel;
    private String nominal;
    private Integer totalLike;
    private Integer like;
    private Integer forEvent;
    private String eventKey;
    private Integer newsCommentCount;
    private String claimNotice;
    private Integer closeDiscuss;
    private List<LinkedTreeMap<String,Object>> newsComments;
    private List<LinkedTreeMap<String,Object>> reletedInfo;

    public String getClaimNotice() {
        return claimNotice;
    }

    public void setClaimNotice(String claimNotice) {
        this.claimNotice = claimNotice;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getKeyWords() {
        return keyWords;
    }

    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getVisitedTimes() {
        return visitedTimes;
    }

    public void setVisitedTimes(String visitedTimes) {
        this.visitedTimes = visitedTimes;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<LinkedTreeMap<String, Object>> getReletedInfo() {
        return reletedInfo;
    }

    public void setReletedInfo(List<LinkedTreeMap<String, Object>> reletedInfo) {
        this.reletedInfo = reletedInfo;
    }

    public String getAuthPic() {
        return authPic;
    }

    public void setAuthPic(String authPic) {
        this.authPic = authPic;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getSortTel() {
        return sortTel;
    }

    public void setSortTel(String sortTel) {
        this.sortTel = sortTel;
    }

    public String getNominal() {
        return nominal;
    }

    public void setNominal(String nominal) {
        this.nominal = nominal;
    }

    public Integer getTotalLike() {
        return totalLike;
    }

    public void setTotalLike(Integer totalLike) {
        this.totalLike = totalLike;
    }

    public Integer getLike() {
        return like;
    }

    public void setLike(Integer like) {
        this.like = like;
    }

    public Integer getForEvent() {
        return forEvent;
    }

    public void setForEvent(Integer forEvent) {
        this.forEvent = forEvent;
    }

    public String getEventKey() {
        return eventKey;
    }

    public void setEventKey(String eventKey) {
        this.eventKey = eventKey;
    }

    public Integer getNewsCommentCount() {
        return newsCommentCount;
    }

    public void setNewsCommentCount(Integer newsCommentCount) {
        this.newsCommentCount = newsCommentCount;
    }

    public List<LinkedTreeMap<String, Object>> getNewsComments() {
        return newsComments;
    }

    public void setNewsComments(List<LinkedTreeMap<String, Object>> newsComments) {
        this.newsComments = newsComments;
    }

    public Integer getCloseDiscuss() {
        return closeDiscuss;
    }

    public void setCloseDiscuss(Integer closeDiscuss) {
        this.closeDiscuss = closeDiscuss;
    }
}
