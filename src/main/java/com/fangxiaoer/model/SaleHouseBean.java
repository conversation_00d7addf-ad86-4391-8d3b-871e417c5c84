package com.fangxiaoer.model;

import java.io.Serializable;

/**
 * Created by Lyn on 2017/5/2.
 */
public class SaleHouseBean implements Serializable {

    private String houseId;
    private String subId;
    private String subName;
    private String room;
    private String hall;
    private String guard;
    private String buildErea;
    private String sellPrice;
    private String floor;
    private String countFloor;
    private String buildName;
    private String floorNumber;
    private String sellTypeId;
    private String fitmentTypeId;
    private String forwardTypeId;
    private String propertyRightType;
    private String houseTypeId;
    private String buildingtime;
    private String houseTrait;
    private String title;
    private String describe;

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getHall() {
        return hall;
    }

    public void setHall(String hall) {
        this.hall = hall;
    }

    public String getGuard() {
        return guard;
    }

    public void setGuard(String guard) {
        this.guard = guard;
    }

    public String getBuildErea() {
        return buildErea;
    }

    public void setBuildErea(String buildErea) {
        this.buildErea = buildErea;
    }

    public String getSellPrice() {
        return sellPrice;
    }

    public void setSellPrice(String sellPrice) {
        this.sellPrice = sellPrice;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getCountFloor() {
        return countFloor;
    }

    public void setCountFloor(String countFloor) {
        this.countFloor = countFloor;
    }

    public String getBuildName() {
        return buildName;
    }

    public void setBuildName(String buildName) {
        this.buildName = buildName;
    }

    public String getFloorNumber() {
        return floorNumber;
    }

    public void setFloorNumber(String floorNumber) {
        this.floorNumber = floorNumber;
    }

    public String getSellTypeId() {
        return sellTypeId;
    }

    public void setSellTypeId(String sellTypeId) {
        this.sellTypeId = sellTypeId;
    }

    public String getFitmentTypeId() {
        return fitmentTypeId;
    }

    public void setFitmentTypeId(String fitmentTypeId) {
        this.fitmentTypeId = fitmentTypeId;
    }

    public String getForwardTypeId() {
        return forwardTypeId;
    }

    public void setForwardTypeId(String forwardTypeId) {
        this.forwardTypeId = forwardTypeId;
    }

    public String getPropertyRightType() {
        return propertyRightType;
    }

    public void setPropertyRightType(String propertyRightType) {
        this.propertyRightType = propertyRightType;
    }

    public String getHouseTypeId() {
        return houseTypeId;
    }

    public void setHouseTypeId(String houseTypeId) {
        this.houseTypeId = houseTypeId;
    }

    public String getBuildingtime() {
        return buildingtime;
    }

    public void setBuildingtime(String buildingtime) {
        this.buildingtime = buildingtime;
    }

    public String getHouseTrait() {
        return houseTrait;
    }

    public void setHouseTrait(String houseTrait) {
        this.houseTrait = houseTrait;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }
}
