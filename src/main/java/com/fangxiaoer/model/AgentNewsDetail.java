package com.fangxiaoer.model;

import com.google.gson.internal.LinkedTreeMap;

import java.util.List;

/**
 * Created by Administrator on 2017/6/22.
 */
public class AgentNewsDetail {

    private String newsID;

    private String categoryID;

    private String titleShow;

    private String title;

    private String source;

    private String author;

    private String description;

    private String mvisitedTimes;

    private String visitedTimes;

    private String state;

    private String publishState;

    private String bigPicUrl;

    private String addTime;

    private String updateTime;

    private String memberID;

    private String editor;

    private String isPicNews;

    private String auditor;

    private String auditTime;

    private String keyWords;

    private String other1;

    private String other2;

    private String other3;

    private String content;

    public String getNewsID() {
        return newsID;
    }

    public void setNewsID(String newsID) {
        this.newsID = newsID;
    }

    public String getCategoryID() {
        return categoryID;
    }

    public void setCategoryID(String categoryID) {
        this.categoryID = categoryID;
    }

    public String getTitleShow() {
        return titleShow;
    }

    public void setTitleShow(String titleShow) {
        this.titleShow = titleShow;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMvisitedTimes() {
        return mvisitedTimes;
    }

    public void setMvisitedTimes(String mvisitedTimes) {
        this.mvisitedTimes = mvisitedTimes;
    }

    public String getVisitedTimes() {
        return visitedTimes;
    }

    public void setVisitedTimes(String visitedTimes) {
        this.visitedTimes = visitedTimes;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPublishState() {
        return publishState;
    }

    public void setPublishState(String publishState) {
        this.publishState = publishState;
    }

    public String getBigPicUrl() {
        return bigPicUrl;
    }

    public void setBigPicUrl(String bigPicUrl) {
        this.bigPicUrl = bigPicUrl;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getMemberID() {
        return memberID;
    }

    public void setMemberID(String memberID) {
        this.memberID = memberID;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }

    public String getIsPicNews() {
        return isPicNews;
    }

    public void setIsPicNews(String isPicNews) {
        this.isPicNews = isPicNews;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

    public String getKeyWords() {
        return keyWords;
    }

    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
    }

    public String getOther1() {
        return other1;
    }

    public void setOther1(String other1) {
        this.other1 = other1;
    }

    public String getOther2() {
        return other2;
    }

    public void setOther2(String other2) {
        this.other2 = other2;
    }

    public String getOther3() {
        return other3;
    }

    public void setOther3(String other3) {
        this.other3 = other3;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
