package com.fangxiaoer.model;

import java.io.Serializable;

/**
 * Created by Lyn on 2017/5/3.
 */
public class RentHouseBean implements Serializable {

    private String houseId;
    private String rentTypeId;
    private String subName;
    private String subId;
    private String paymentId;
    private String rentPrice;
    private String sexId;
    private String otherEstablishId;
    private String title;
    private String describe;
    private String room;
    private String hall;
    private String guard;
    private String buildErea;
    private String floor;
    private String countFloor;
    private String singleHouseTypeId;
    private String sellTypeId;
    private String fitmentTypeId;
    private String forwardTypeId;

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getRentTypeId() {
        return rentTypeId;
    }

    public void setRentTypeId(String rentTypeId) {
        this.rentTypeId = rentTypeId;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String getRentPrice() {
        return rentPrice;
    }

    public void setRentPrice(String rentPrice) {
        this.rentPrice = rentPrice;
    }

    public String getSexId() {
        return sexId;
    }

    public void setSexId(String sexId) {
        this.sexId = sexId;
    }

    public String getOtherEstablishId() {
        return otherEstablishId;
    }

    public void setOtherEstablishId(String otherEstablishId) {
        this.otherEstablishId = otherEstablishId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getHall() {
        return hall;
    }

    public void setHall(String hall) {
        this.hall = hall;
    }

    public String getGuard() {
        return guard;
    }

    public void setGuard(String guard) {
        this.guard = guard;
    }

    public String getBuildErea() {
        return buildErea;
    }

    public void setBuildErea(String buildErea) {
        this.buildErea = buildErea;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getCountFloor() {
        return countFloor;
    }

    public void setCountFloor(String countFloor) {
        this.countFloor = countFloor;
    }

    public String getSingleHouseTypeId() {
        return singleHouseTypeId;
    }

    public void setSingleHouseTypeId(String singleHouseTypeId) {
        this.singleHouseTypeId = singleHouseTypeId;
    }

    public String getSellTypeId() {
        return sellTypeId;
    }

    public void setSellTypeId(String sellTypeId) {
        this.sellTypeId = sellTypeId;
    }

    public String getFitmentTypeId() {
        return fitmentTypeId;
    }

    public void setFitmentTypeId(String fitmentTypeId) {
        this.fitmentTypeId = fitmentTypeId;
    }

    public String getForwardTypeId() {
        return forwardTypeId;
    }

    public void setForwardTypeId(String forwardTypeId) {
        this.forwardTypeId = forwardTypeId;
    }
}
