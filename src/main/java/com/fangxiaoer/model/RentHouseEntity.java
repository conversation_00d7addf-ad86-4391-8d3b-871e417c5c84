package com.fangxiaoer.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Lyn on 2017/5/3.
 */
public class RentHouseEntity implements Serializable {

    private String houseid;

    private String houseowner;

    private String ownerphone;

    private String regionid;

    private String plateid;

    private String subid;

    private String subname;

    private String subaddress;

    private String floor;

    private String floornumber;

    private String forwardtype;

    private String buildarea;

    private String truearea;

    private String houseage;

    private String fitmenttype;

    private String islayoutpic;

    private String agencyid;

    private String membertype;

    private String title;

    private String keeperid;

    private String keepername;

    private String totalvisitednum;

    private String todayvisitednum;

    private String description;

    private String ownerhouseid;

    private String addtime;

    private String updatetime;

    private String renttype;

    private String rentstate;

    private String rentprice;

    private String otherestablish;

    private String schoolareaid;

    private String schoolareamid;

    private String middleschoolid;

    private String housetrait;

    private String propertycosts;

    private String heatingtype;

    private String mountain;

    private String street;

    private String buildid;

    private String buildname;

    private String unit;

    private String state;

    private String source;

    private String ownertype;

    private String deliverytime;

    private String keynumber;

    private String thekey;

    private String room;

    private String hall;

    private String toilet;

    private String buildingtime;

    private String totalfloornumber;

    private String bluelabel;

    private String subwayline;

    private String subwaysite;

    private String qualitytype;

    private String clickrate;

    private String realid;

    private String realname;

    private String binid;

    private String isdel;

    private String payment;

    private String bedroom;

    private String sex;

    private String restype;

    private String roomrent;

    private String ishousepic;

    private String regionorder;

    private String allorder;

    private String publishedend;

    private String isrecommended;

    private String remark;

    private String isstick;

    private String sourceid;

    private String ischeck;

    private String describe;

    private Integer detailid;

    private Integer newsub;

    private List<PicEntity> picEntityList;
    private String imglist;
    private String imgvalue;
    private Integer showtuindex;
    private String imgurl;

    public String getImgurl() {
        return imgurl;
    }

    public void setImgurl(String imgurl) {
        this.imgurl = imgurl;
    }

    public String getImglist() {
        if(picEntityList != null && picEntityList.size() != 0){
            StringBuffer sf = new StringBuffer();
            for (PicEntity picEntity : picEntityList) {
                sf.append(picEntity.getSmallimageurl()).append(",");
            }
            return sf.toString().replaceAll("http://images.fangxiaoer.com/sy/esf/fy/middle/","").replaceAll("https://images.fangxiaoer.com/sy/esf/fy/middle/","")
                    .replaceAll("noimage375275.jpg","").replaceAll(",+$","");
        }
        return imglist;}

    public void setImglist(String imglist) {
        this.imglist = imglist;
    }

    public String getImgvalue() {
        if(picEntityList != null && picEntityList.size() != 0){
            StringBuffer sf = new StringBuffer();
            for (PicEntity picEntity : picEntityList) {
                sf.append(picEntity.getSmallimageurl()).append(",");
            }
            return sf.toString()
                    .replaceAll("http://images.fangxiaoer.com/sy/esf/fy/middle/","").replaceAll("https://images.fangxiaoer.com/sy/esf/fy/middle/","")
                    .replaceAll("noimage375275.jpg","").replaceAll(",+$","");
        }
        return imgvalue;
    }

    public void setImgvalue(String imgvalue) {
        this.imgvalue = imgvalue;
    }

    public Integer getShowtuindex() {
        if (picEntityList != null && picEntityList.size() != 0) {
            int i = 0;
            for (PicEntity picEntity : picEntityList) {
                if (picEntity.getIsdefault().equals("1")) {
                    return i;
                }
                i++;
            }
        }
        return showtuindex;
    }

    public void setShowtuindex(Integer showtuindex) {
        this.showtuindex = showtuindex;
    }

    public String getHouseid() {
        return houseid;
    }

    public void setHouseid(String houseid) {
        this.houseid = houseid;
    }

    public String getHouseowner() {
        return houseowner;
    }

    public void setHouseowner(String houseowner) {
        this.houseowner = houseowner;
    }

    public String getOwnerphone() {
        return ownerphone;
    }

    public void setOwnerphone(String ownerphone) {
        this.ownerphone = ownerphone;
    }

    public String getRegionid() {
        return regionid;
    }

    public void setRegionid(String regionid) {
        this.regionid = regionid;
    }

    public String getPlateid() {
        return plateid;
    }

    public void setPlateid(String plateid) {
        this.plateid = plateid;
    }

    public String getSubid() {
        return subid;
    }

    public void setSubid(String subid) {
        this.subid = subid;
    }

    public String getSubname() {
        return subname;
    }

    public void setSubname(String subname) {
        this.subname = subname;
    }

    public String getSubaddress() {
        return subaddress;
    }

    public void setSubaddress(String subaddress) {
        this.subaddress = subaddress;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getFloornumber() {
        return floornumber;
    }

    public void setFloornumber(String floornumber) {
        this.floornumber = floornumber;
    }

    public String getForwardtype() {
        return forwardtype;
    }

    public void setForwardtype(String forwardtype) {
        this.forwardtype = forwardtype;
    }

    public String getBuildarea() {
        return buildarea;
    }

    public void setBuildarea(String buildarea) {
        this.buildarea = buildarea;
    }

    public String getTruearea() {
        return truearea;
    }

    public void setTruearea(String truearea) {
        this.truearea = truearea;
    }

    public String getHouseage() {
        return houseage;
    }

    public void setHouseage(String houseage) {
        this.houseage = houseage;
    }

    public String getFitmenttype() {
        return fitmenttype;
    }

    public void setFitmenttype(String fitmenttype) {
        this.fitmenttype = fitmenttype;
    }

    public String getIslayoutpic() {
        return islayoutpic;
    }

    public void setIslayoutpic(String islayoutpic) {
        this.islayoutpic = islayoutpic;
    }

    public String getAgencyid() {
        return agencyid;
    }

    public void setAgencyid(String agencyid) {
        this.agencyid = agencyid;
    }

    public String getMembertype() {
        return membertype;
    }

    public void setMembertype(String membertype) {
        this.membertype = membertype;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getKeeperid() {
        return keeperid;
    }

    public void setKeeperid(String keeperid) {
        this.keeperid = keeperid;
    }

    public String getKeepername() {
        return keepername;
    }

    public void setKeepername(String keepername) {
        this.keepername = keepername;
    }

    public String getTotalvisitednum() {
        return totalvisitednum;
    }

    public void setTotalvisitednum(String totalvisitednum) {
        this.totalvisitednum = totalvisitednum;
    }

    public String getTodayvisitednum() {
        return todayvisitednum;
    }

    public void setTodayvisitednum(String todayvisitednum) {
        this.todayvisitednum = todayvisitednum;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOwnerhouseid() {
        return ownerhouseid;
    }

    public void setOwnerhouseid(String ownerhouseid) {
        this.ownerhouseid = ownerhouseid;
    }

    public String getAddtime() {
        return addtime;
    }

    public void setAddtime(String addtime) {
        this.addtime = addtime;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getRenttype() {
        return renttype;
    }

    public void setRenttype(String renttype) {
        this.renttype = renttype;
    }

    public String getRentstate() {
        return rentstate;
    }

    public void setRentstate(String rentstate) {
        this.rentstate = rentstate;
    }

    public String getRentprice() {
        return rentprice;
    }

    public void setRentprice(String rentprice) {
        this.rentprice = rentprice;
    }

    public String getOtherestablish() {
        return otherestablish;
    }

    public void setOtherestablish(String otherestablish) {
        this.otherestablish = otherestablish;
    }

    public String getSchoolareaid() {
        return schoolareaid;
    }

    public void setSchoolareaid(String schoolareaid) {
        this.schoolareaid = schoolareaid;
    }

    public String getSchoolareamid() {
        return schoolareamid;
    }

    public void setSchoolareamid(String schoolareamid) {
        this.schoolareamid = schoolareamid;
    }

    public String getMiddleschoolid() {
        return middleschoolid;
    }

    public void setMiddleschoolid(String middleschoolid) {
        this.middleschoolid = middleschoolid;
    }

    public String getHousetrait() {
        return housetrait;
    }

    public void setHousetrait(String housetrait) {
        this.housetrait = housetrait;
    }

    public String getPropertycosts() {
        return propertycosts;
    }

    public void setPropertycosts(String propertycosts) {
        this.propertycosts = propertycosts;
    }

    public String getHeatingtype() {
        return heatingtype;
    }

    public void setHeatingtype(String heatingtype) {
        this.heatingtype = heatingtype;
    }

    public String getMountain() {
        return mountain;
    }

    public void setMountain(String mountain) {
        this.mountain = mountain;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getBuildid() {
        return buildid;
    }

    public void setBuildid(String buildid) {
        this.buildid = buildid;
    }

    public String getBuildname() {
        return buildname;
    }

    public void setBuildname(String buildname) {
        this.buildname = buildname;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getOwnertype() {
        return ownertype;
    }

    public void setOwnertype(String ownertype) {
        this.ownertype = ownertype;
    }

    public String getDeliverytime() {
        return deliverytime;
    }

    public void setDeliverytime(String deliverytime) {
        this.deliverytime = deliverytime;
    }

    public String getKeynumber() {
        return keynumber;
    }

    public void setKeynumber(String keynumber) {
        this.keynumber = keynumber;
    }

    public String getThekey() {
        return thekey;
    }

    public void setThekey(String thekey) {
        this.thekey = thekey;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getHall() {
        return hall;
    }

    public void setHall(String hall) {
        this.hall = hall;
    }

    public String getToilet() {
        return toilet;
    }

    public void setToilet(String toilet) {
        this.toilet = toilet;
    }

    public String getBuildingtime() {
        return buildingtime;
    }

    public void setBuildingtime(String buildingtime) {
        this.buildingtime = buildingtime;
    }

    public String getTotalfloornumber() {
        return totalfloornumber;
    }

    public void setTotalfloornumber(String totalfloornumber) {
        this.totalfloornumber = totalfloornumber;
    }

    public String getBluelabel() {
        return bluelabel;
    }

    public void setBluelabel(String bluelabel) {
        this.bluelabel = bluelabel;
    }

    public String getSubwayline() {
        return subwayline;
    }

    public void setSubwayline(String subwayline) {
        this.subwayline = subwayline;
    }

    public String getSubwaysite() {
        return subwaysite;
    }

    public void setSubwaysite(String subwaysite) {
        this.subwaysite = subwaysite;
    }

    public String getQualitytype() {
        return qualitytype;
    }

    public void setQualitytype(String qualitytype) {
        this.qualitytype = qualitytype;
    }

    public String getClickrate() {
        return clickrate;
    }

    public void setClickrate(String clickrate) {
        this.clickrate = clickrate;
    }

    public String getRealid() {
        return realid;
    }

    public void setRealid(String realid) {
        this.realid = realid;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getBinid() {
        return binid;
    }

    public void setBinid(String binid) {
        this.binid = binid;
    }

    public String getIsdel() {
        return isdel;
    }

    public void setIsdel(String isdel) {
        this.isdel = isdel;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public String getBedroom() {
        return bedroom;
    }

    public void setBedroom(String bedroom) {
        this.bedroom = bedroom;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getRestype() {
        return restype;
    }

    public void setRestype(String restype) {
        this.restype = restype;
    }

    public String getRoomrent() {
        return roomrent;
    }

    public void setRoomrent(String roomrent) {
        this.roomrent = roomrent;
    }

    public String getIshousepic() {
        return ishousepic;
    }

    public void setIshousepic(String ishousepic) {
        this.ishousepic = ishousepic;
    }

    public String getRegionorder() {
        return regionorder;
    }

    public void setRegionorder(String regionorder) {
        this.regionorder = regionorder;
    }

    public String getAllorder() {
        return allorder;
    }

    public void setAllorder(String allorder) {
        this.allorder = allorder;
    }

    public String getPublishedend() {
        return publishedend;
    }

    public void setPublishedend(String publishedend) {
        this.publishedend = publishedend;
    }

    public String getIsrecommended() {
        return isrecommended;
    }

    public void setIsrecommended(String isrecommended) {
        this.isrecommended = isrecommended;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsstick() {
        return isstick;
    }

    public void setIsstick(String isstick) {
        this.isstick = isstick;
    }

    public String getSourceid() {
        return sourceid;
    }

    public void setSourceid(String sourceid) {
        this.sourceid = sourceid;
    }

    public String getIscheck() {
        return ischeck;
    }

    public void setIscheck(String ischeck) {
        this.ischeck = ischeck;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public List<PicEntity> getPicEntityList() {
        return picEntityList;
    }

    public void setPicEntityList(List<PicEntity> picEntityList) {
        this.picEntityList = picEntityList;
    }

    public Integer getDetailid() {
        return detailid;
    }

    public void setDetailid(Integer detailid) {
        this.detailid = detailid;
    }

    public Integer getNewsub() {
        return newsub;
    }

    public void setNewsub(Integer newsub) {
        this.newsub = newsub;
    }
}
