package com.fangxiaoer.model;

import java.io.Serializable;

public class RealEstate implements Serializable {

    private String id;

    /**
     * 我的身份
     * 0产权人
     * 1帮他人卖房
     */
    private String identity;

    /**
     * 产权人姓名
     */
    private String homeOwnerName;

    /**
     * 产权人身份证号
     */
    private String homeOwnerID;

    /**
     * 当前账号与产权人关系
     * 0当前账号即是产权人
     * 1夫妻
     * 2子女
     * 3父母
     * 4兄弟姐妹
     * 5朋友
     * 6三代以内直系亲属
     * 7其他
     */
    private String relationship;

    /**
     * 补充说明
     */
    private String supplement;

    /**
     * 房源id
     */
    private String saleHouseId;

    /**
     * 房产证号
     */
    private String houseCertificate;

    /**
     * 户室号-单元
     */
    private String unit;

    /**
     * 户室号-楼层
     */
    private String floor;

    /**
     * 户室号-门牌号
     */
    private String door;

    /**
     * 楼栋外墙体的蓝牌号信息
     */
    private String blueLabel;
    /**
     * 当前用户的真实姓名
     */
    private String rName;
    /**
     * 当前用户的身份证号
     */
    private String IDCard;
    /**
     * 小区名称
     */
    private String subName;
    /**
     * 用户ID
     */
    private String memberId;

    private Integer certificateType;

    /**
     * 图片
     */
    private String pics;

    /**
     * 房主验真姓名
     */
    private String houseOwnerName;
    /**
     * 房主验真电话
     */
    private String houseOwnerTel;
    /**
     * 房主是否验真
     */
    private Integer houseOwnerTelState;

    public String getHouseOwnerName() {
        return houseOwnerName;
    }

    public void setHouseOwnerName(String houseOwnerName) {
        this.houseOwnerName = houseOwnerName;
    }

    public String getHouseOwnerTel() {
        return houseOwnerTel;
    }

    public void setHouseOwnerTel(String houseOwnerTel) {
        this.houseOwnerTel = houseOwnerTel;
    }

    public Integer getHouseOwnerTelState() {
        return houseOwnerTelState;
    }

    public void setHouseOwnerTelState(Integer houseOwnerTelState) {
        this.houseOwnerTelState = houseOwnerTelState;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getHomeOwnerName() {
        return homeOwnerName;
    }

    public void setHomeOwnerName(String homeOwnerName) {
        this.homeOwnerName = homeOwnerName;
    }

    public String getHomeOwnerID() {
        return homeOwnerID;
    }

    public void setHomeOwnerID(String homeOwnerID) {
        this.homeOwnerID = homeOwnerID;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public String getSupplement() {
        return supplement;
    }

    public void setSupplement(String supplement) {
        this.supplement = supplement;
    }

    public String getSaleHouseId() {
        return saleHouseId;
    }

    public void setSaleHouseId(String saleHouseId) {
        this.saleHouseId = saleHouseId;
    }

    public String getHouseCertificate() {
        return houseCertificate;
    }

    public void setHouseCertificate(String houseCertificate) {
        this.houseCertificate = houseCertificate;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getDoor() {
        return door;
    }

    public void setDoor(String door) {
        this.door = door;
    }

    public String getBlueLabel() {
        return blueLabel;
    }

    public void setBlueLabel(String blueLabel) {
        this.blueLabel = blueLabel;
    }

    public String getrName() {
        return rName;
    }

    public void setrName(String rName) {
        this.rName = rName;
    }

    public String getIDCard() {
        return IDCard;
    }

    public void setIDCard(String IDCard) {
        this.IDCard = IDCard;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getPics() {
        return pics;
    }

    public void setPics(String pics) {
        this.pics = pics;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }
}
