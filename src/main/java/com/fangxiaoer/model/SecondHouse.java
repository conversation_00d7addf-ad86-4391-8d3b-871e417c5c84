package com.fangxiaoer.model;

import com.fangxiaoer.model.base.MyPageable;

/**
 * Created by Administrator on 2017/6/12.
 */
public class SecondHouse extends MyPageable {
    private String regionId;
    private String plateId;
    private String subId;
    private String subName;
    private String agencyId;
    private String memberType;
    private String room;
    private String hall;
    private String subWayId;
    private String subWayStationId;
    private String stationId;
    private String latitude;
    private String longitude;
    private String distanceId;
    private String areaId;
    private String minArea;
    private String maxArea;
    private String priceId;
    private String minPrice;
    private String maxPrice;
    private String houseTrait;
    private String forwardType;

    private String areaIds;
    private String fitmentType;
    private String saleType;
    private String except;

    private Integer page = 1;
    private Integer pageSize= 10;
    private String orderKey;
    private String timeSwitch;
    private String floorRange;
    private String isSubway;

    public String getIsSubway() {
        return isSubway;
    }

    public void setIsSubway(String isSubway) {
        this.isSubway = isSubway;
    }

    public String getFloorRange() {
        return floorRange;
    }

    public void setFloorRange(String floorRange) {
        this.floorRange = floorRange;
    }

    public String getOrderKey() {
        return orderKey;
    }

    public void setOrderKey(String orderKey) {
        this.orderKey = orderKey;
    }

    public String getTimeSwitch() {
        return timeSwitch;
    }

    public void setTimeSwitch(String timeSwitch) {
        this.timeSwitch = timeSwitch;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getPlateId() {
        return plateId;
    }

    public void setPlateId(String plateId) {
        this.plateId = plateId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getMemberType() {
        return memberType;
    }

    public void setMemberType(String memberType) {
        this.memberType = memberType;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getHall() {
        return hall;
    }

    public void setHall(String hall) {
        this.hall = hall;
    }

    public String getSubWayId() {
        return subWayId;
    }

    public void setSubWayId(String subWayId) {
        this.subWayId = subWayId;
    }

    public String getSubWayStationId() {
        return subWayStationId;
    }

    public void setSubWayStationId(String subWayStationId) {
        this.subWayStationId = subWayStationId;
    }

    public String getStationId() {
        return stationId;
    }

    public void setStationId(String stationId) {
        this.stationId = stationId;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getDistanceId() {
        return distanceId;
    }

    public void setDistanceId(String distanceId) {
        this.distanceId = distanceId;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getMinArea() {
        return minArea;
    }

    public void setMinArea(String minArea) {
        this.minArea = minArea;
    }

    public String getMaxArea() {
        return maxArea;
    }

    public void setMaxArea(String maxArea) {
        this.maxArea = maxArea;
    }

    public String getPriceId() {
        return priceId;
    }

    public void setPriceId(String priceId) {
        this.priceId = priceId;
    }

    public String getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(String minPrice) {
        this.minPrice = minPrice;
    }

    public String getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(String maxPrice) {
        this.maxPrice = maxPrice;
    }

    public String getHouseTrait() {
        return houseTrait;
    }

    public void setHouseTrait(String houseTrait) {
        this.houseTrait = houseTrait;
    }

    public String getForwardType() {
        return forwardType;
    }

    public void setForwardType(String forwardType) {
        this.forwardType = forwardType;
    }

    public String getAreaIds() {
        return areaIds;
    }

    public void setAreaIds(String areaIds) {
        this.areaIds = areaIds;
    }

    public String getFitmentType() {
        return fitmentType;
    }

    public void setFitmentType(String fitmentType) {
        this.fitmentType = fitmentType;
    }

    public String getSaleType() {
        return saleType;
    }

    public void setSaleType(String saleType) {
        this.saleType = saleType;
    }

    public String getExcept() {
        return except;
    }

    public void setExcept(String except) {
        this.except = except;
    }

    @Override
    public Integer getPage() {
        return page;
    }

    @Override
    public void setPage(Integer page) {
        this.page = page;
    }

    @Override
    public Integer getPageSize() {
        return pageSize;
    }

    @Override
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
