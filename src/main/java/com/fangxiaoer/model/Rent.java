package com.fangxiaoer.model;

import com.fangxiaoer.model.base.MyPageable;

/**
 * Created by Administrator on 2017/6/14.
 */
public class Rent extends MyPageable {
    private String regionId;
    private String agencyId;
    private String subId;
    private String latitude;
    private String longitude;
    private String distanceId;
    private String subWayStationId;
    private String subWayId;
    private String except;
    private String areaId;
    private String rentalTypeId;
    private String room;
    private String hall;
    private String priceId;
    private String subName;
    private String sourceId;
    private String bedRoom;
    private Integer page = 1;
    private Integer pageSize =10 ;
    private String orderKey;

    public String getOrderKey() {
        return orderKey;
    }

    public void setOrderKey(String orderKey) {
        this.orderKey = orderKey;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getDistanceId() {
        return distanceId;
    }

    public void setDistanceId(String distanceId) {
        this.distanceId = distanceId;
    }

    public String getSubWayStationId() {
        return subWayStationId;
    }

    public void setSubWayStationId(String subWayStationId) {
        this.subWayStationId = subWayStationId;
    }

    public String getSubWayId() {
        return subWayId;
    }

    public void setSubWayId(String subWayId) {
        this.subWayId = subWayId;
    }

    public String getExcept() {
        return except;
    }

    public void setExcept(String except) {
        this.except = except;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getRentalTypeId() {
        return rentalTypeId;
    }

    public void setRentalTypeId(String rentalTypeId) {
        this.rentalTypeId = rentalTypeId;
    }

    public String getRoom() {
        return room;
    }

    public void setRoom(String room) {
        this.room = room;
    }

    public String getHall() {
        return hall;
    }

    public void setHall(String hall) {
        this.hall = hall;
    }

    public String getPriceId() {
        return priceId;
    }

    public void setPriceId(String priceId) {
        this.priceId = priceId;
    }

    public String getSubName() {
        return subName;
    }

    public void setSubName(String subName) {
        this.subName = subName;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getBedRoom() {
        return bedRoom;
    }

    public void setBedRoom(String bedRoom) {
        this.bedRoom = bedRoom;
    }

    @Override
    public Integer getPage() {
        return page;
    }

    @Override
    public void setPage(Integer page) {
        this.page = page;
    }

    @Override
    public Integer getPageSize() {
        return pageSize;
    }

    @Override
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
