package com.fangxiaoer.model.base;

import com.fangxiaoer.common.ParamsUtil;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;

/**
 * <AUTHOR> 金伟英
 * @create ：2016/11/11
 * @time : 18:08
 */
public class MyPageable {
    protected Integer page = 1;
    protected Integer pageSize = 10;

    public MyPageable() {}

    public MyPageable(Integer page, Integer pageSize) {
        this.page = page;
        this.pageSize = pageSize;
    }

    public MyPageable(HashMap<String, String> paramMap, Integer pageSize) {
        String page = paramMap.get("page");
        if(!StringUtils.isEmpty(page)) {
            this.page = Integer.valueOf(page);
        }
        this.pageSize = pageSize;
    }

    public void putAttribute(Model model, String url, Integer totalNum) {
        putAttribute(model, url, totalNum, null);
    }
    public void putAttribute(Model model, String url, Integer totalNum, Integer pageNum) {

        if(totalNum == null) totalNum = 0;
        if(pageNum == null) pageNum = page;
        try {
            if(url.equals(new String(url.getBytes("ISO-8859-1"), "ISO-8859-1"))) {
                url = new String(url.getBytes("ISO-8859-1"), "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        Integer totalPages = (totalNum + pageSize - 1) / pageSize;
        /**
         * 分页 index 从 1 开始
         */
        int minPage = 1;
        int toPage = 10;
        int n = (pageNum-1) / 10;
        //if (n > 1) {
        if (n >= 1) {
            minPage = n * 10+1;
            toPage += minPage-1;
        }
        if (toPage > totalPages) {
            toPage = totalPages;
        }

        model.addAttribute("totalPages", totalPages);
        model.addAttribute("number", pageNum);
        model.addAttribute("minPage", minPage);
        model.addAttribute("toPage", toPage);
        String pageUrl = ParamsUtil.pageUrlItem(url);
        model.addAttribute("pageUrl", pageUrl);
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
