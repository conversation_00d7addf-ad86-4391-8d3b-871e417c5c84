package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.model.SubBean;
import com.fangxiaoer.model.search.SearchHouse;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.joda.time.DateTime;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fangxiaoer.common.Constants.HOME_PAGE_ADVERTISEMENT;


/**
 * 首页广告
 * /**
 * Created by cyz on 2017/5/12.
 */


@Service
public class HomeService {

    public void getAdvertisement(Model model,String systemDate){
        HashMap<String, Object> resultMap = HttpUtil.connectApi(HOME_PAGE_ADVERTISEMENT,new Params("systemDate",systemDate).get(),true);
        getAdvertisement(resultMap,model);
    }

    public void getAdvertisement(Model model) {
        HashMap<String, Object> resultMap = HttpUtil.connectApi(HOME_PAGE_ADVERTISEMENT,true);
        getAdvertisement(resultMap,model);
    }

    public void getAdvertisement(HashMap<String, Object> resultMap, Model model){
        LinkedTreeMap<String, Object> map = (LinkedTreeMap<String, Object>) resultMap.get("content");
        //通栏广告
        model.addAttribute("normal",map);
        //顶部通栏
        model.addAttribute("normalTop", map.get("normalTop"));
        //主页背投广告
        model.addAttribute("backGround",map.get("backGround"));
//        model.addAttribute("floatAd",map.get("floatAd"));
        //二手房模块中介零距离
        model.addAttribute("saleVideo",map.get("saleVideo"));
        //二手房模块中介零距离上房广告
        model.addAttribute("focusSale",map.get("focusSale"));
        //租房模块右侧广告位
        model.addAttribute("focusRent",map.get("focusRent"));
        //商铺模块右侧广告位
        model.addAttribute("focusHouse",map.get("focusHouse"));
        //二手房焦点图新
        model.addAttribute("secFocus", map.get("secFocus"));
        //黄金眼图片
        ArrayList<LinkedTreeMap<String, Object>> goldenEyes = (ArrayList<LinkedTreeMap<String, Object>>) map.get("goldEyeAds");
        ArrayList<LinkedTreeMap<String, Object>> newGoldenEyes = new ArrayList<>();
        if(goldenEyes != null && goldenEyes.size() > 1){
            ArrayList<LinkedTreeMap<String, Object>> goldenEye2 = new ArrayList<>();
            goldenEyes.forEach(e->goldenEye2.add(e));
            while (true){
                int len = goldenEye2.size();
                if(len == 1){
                    newGoldenEyes.add(goldenEye2.get(0));
                    break;
                }else{
                    double value = Math.random();
                    int x = (int) (value * len);
                    newGoldenEyes.add(goldenEye2.get(x));
                    goldenEye2.remove(x);
                }
            }
            model.addAttribute("ad", newGoldenEyes);

        }else{
            model.addAttribute("ad", goldenEyes);
        }
        //弹屏广告
        ArrayList<LinkedTreeMap<String, Object>> prompAds = (ArrayList<LinkedTreeMap<String, Object>>) map.get("prompAd");
        if (prompAds.size() > 0) {
            model.addAttribute("pad", prompAds.get(0));
        }

        ArrayList<LinkedTreeMap<String, Object>> secAds = (ArrayList<LinkedTreeMap<String, Object>>) map.get("secAds");
        model.addAttribute("secAds", secAds);
        //推荐
        LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>> htop = groupBy("Name", (ArrayList<LinkedTreeMap<String, Object>>) map.get("weekend"));
        addAdsToModel(htop,"names1","recommend",model);
        //新房
        LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>> newhouse = groupBy("Name", (ArrayList<LinkedTreeMap<String, Object>>) map.get("newProjectAds"));
        addAdsToModel(newhouse,"names2","newHouse",model);
        //二手房
        LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>> saleMap = groupBy("Name",  (ArrayList<LinkedTreeMap<String, Object>>) map.get("saleAds"));
        addAdsToModel(saleMap,"names3","secondhouse",model);
        //租房
        LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>> rentMap = groupBy("Name",  (ArrayList<LinkedTreeMap<String, Object>>) map.get("rentAds"));
        addAdsToModel(rentMap,"names4","renthouse",model);
        //商铺
        LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>> shopMap = groupBy("Name",  (ArrayList<LinkedTreeMap<String, Object>>) map.get("shopAndOfficeAd"));
        addAdsToModel(shopMap,"names5","shophouse",model);

        LinkedTreeMap<String, Object> ranking = (LinkedTreeMap<String, Object>) map.get("ranking");
        //最新开盘
        Object lastOpen = (ArrayList<LinkedTreeMap<String, Object>>) map.get("weekRecNew");
        //热门推荐
        Object hotRecomment = (ArrayList<LinkedTreeMap<String, Object>>) map.get("weekRecHot");
        //新房-楼盘活动
        ArrayList<LinkedTreeMap<String, Object>> projectActivity = (ArrayList<LinkedTreeMap<String, Object>>) map.get("projectActAds");
        //新房楼盘视频
        ArrayList<LinkedTreeMap<String, Object>> projectVideo = (ArrayList<LinkedTreeMap<String, Object>>) map.get("projectVideoAds");
        model.addAttribute("hotProjects", ranking.get("hotProjects"));
        model.addAttribute("hotProjectTitle", new DateTime().minusMonths(1).toString("M") + "月热评楼盘");
        model.addAttribute("searchRank", ranking.get("searchRank"));
        model.addAttribute("newSecondHouse", ranking.get("newSecondHouse"));
        model.addAttribute("lastOpen", lastOpen);
        model.addAttribute("hotRecomment", hotRecomment);
        model.addAttribute("projectActivity", projectActivity);
        model.addAttribute("projectVideo", projectVideo);
    }

    /**
     * 根据广告的Name进行分组
     * @param key
     * @param src
     * @return
     */
    public LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>> groupBy(String key, ArrayList<LinkedTreeMap<String, Object>> src) {
        if (src == null || src.isEmpty()) return new LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>>();
        LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>> result = new LinkedTreeMap<>();
        for (LinkedTreeMap<String, Object> item : src) {
            String name = item.get(key).toString();
            ArrayList<LinkedTreeMap<String, Object>> current = result.get(name);
            if (current == null) {
                current = new ArrayList<>();
                result.put(name, current);
            }
            current.add(item);
        }
        return result;
    }


    public void addObject(String OrderKey, LinkedTreeMap<String, Object> item, ArrayList list) {
        if (OrderKey == "" || OrderKey == null) return;
        if (OrderKey.equals(item.get("OrderKey"))) {
            list.add(item);
        }
    }

    /**
     *
     * @param entryList  按照Name分组后的广告集合
     * @param modelKey 存放在model里的key
     * @param modelValue  存放在model里的内容
     * @param model
     */
    private void addAdsToModel(LinkedTreeMap<String, ArrayList<LinkedTreeMap<String, Object>>> entryList, String modelKey,String modelValue,Model model){
        List<String> nameList = new ArrayList<>();//nameList 标签（名称）集合
        ArrayList<ArrayList<LinkedTreeMap<String, Object>>> elementList = new ArrayList<>();//elementList 具体广告集合
        for (Map.Entry<String, ArrayList<LinkedTreeMap<String, Object>>> item : entryList.entrySet()) {
            nameList.add(item.getKey());
            elementList.add(item.getValue());
        }
        model.addAttribute(modelKey,nameList);
        model.addAttribute(modelValue,elementList);
    }


    public void generateSelection(Model model) {
        ParamsUtil.addFilterIntoModel(Constants.INDEX_PAGE_NEW, model, "", null);
    }

    public void viewRankAndBrand(Model model){
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.RANK_AND_BRAND, new Params().get(), true);
        model.addAttribute("rb", result.get("content"));
    }

    /**
     * 获取参数
     *
     * @param model
     * <AUTHOR> 2017-8-29
     */
    public void getSerachParams(String apiUrl, String modelName, Model model, HashMap params) {
        Object object = HttpUtil.connectApi(apiUrl, params, model);
        model.addAttribute(modelName, object);
    }

    public void houseFastNews(Model model) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.GET_FLASH_LIST, new Params("pageSize", 12).get(), true);
        model.addAttribute("houseFastNews", result.get("content"));
        /*HashMap<String, Object> rentNews = HttpUtil.connectApi(Constants.GET_FLASH_LIST, new Params("ClassId", 284).add("pageSize", 3).get(), true);
        model.addAttribute("rentNews", rentNews.get("content"));*/
    }
    public void getNews(Model model) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.GET_NEWS_CATEGORY);
        model.addAttribute("newsCategory", result.get("content"));

    }

    /**
     * 搜索记录传送服务
    * <AUTHOR>
    * @Date 2020/10/29 15:04
    **/
    public void searchClickAnalyze(String sessionId, String searchInfo ,String searchSource, String searchType, String keyId, String keyTab){
        HashMap<String, Object> params = new HashMap<>();
        params.put("sessionId",sessionId);
        params.put("searchInfo",searchInfo);
        params.put("searchSource",searchSource);
        params.put("searchType",searchType);
        params.put("keyId",keyId);
        params.put("keyTab",keyTab);
        HttpUtil.connectApi(Constants.SEARCH_CLICK_ANALYZE,params);
    }

    /**
     * 模糊查询
     *
     * @param key
     * @param search
     * @param
     * @return
     */
    public List<LinkedTreeMap<String, Object>> queryLikeHouse(String key, String search, Integer page, Integer pageSize, Integer tab) {
        SearchHouse sh = new SearchHouse();
        sh.setKey(key);
        sh.setSearch(search);
        sh.setPage(page);
        sh.setPageSize(pageSize);
        sh.setTab(tab);
        if(key.equals("1")){
            sh.setTab(1);
            HashMap<String, Object> result = HttpUtil.connectApi(Constants.COMMON_SEARCH_NEW, Utils.transBean2Map(sh));
            List<LinkedTreeMap<String, Object>> info = (List<LinkedTreeMap<String, Object>>) result.get("content");
            return info;
        }else{
            HashMap<String, Object> result = HttpUtil.connectApi(Constants.COMMON_SEARCH, Utils.transBean2Map(sh));
            List<LinkedTreeMap<String, Object>> info = (List<LinkedTreeMap<String, Object>>) result.get("content");
            return info;
        }


    }

    /**
     * 房产大数据接口（逻辑可用）
     *
     * @param city
     * @param model
     * @param params
     * @return
     */
    public void citytrending(String city, Model model, @RequestParam Map<String, String> params) {
        try {
            String json;
            String msg = "本市";
            if (StringUtils.isEmpty(city)) {
                json = "{\"city\": \"" + "sy" + "\"}";
            } else {
                json = "{\"city\": \"" + city + "\"}";
            }
//            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), json.getBytes());
            okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(MediaType.parse("application/json"), json.getBytes());
            Request request = new Request.Builder().url(HOST + city + "/salehouse/v1/trending/weekly").post(requestBody).build();
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String data = response.body().string();
                model.addAttribute("highChartsData", data);
//                model.addAttribute("msg", msg);
                orderData(model, data);
            } else {
                throw new IOException("Unexpected code " + response);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * 房产大数据日期排序
     *
     * @param model
     * @param data
     */
    private void orderData(Model model, String data) {
        Gson g = new Gson();
        List<Map<String, Object>> dataList = g.fromJson(data, List.class);
        if (dataList != null && dataList.size() >= 2) {
            Double unitPriceOfThisWeek = 0.0;
            unitPriceOfThisWeek = Double.parseDouble(dataList.get(0).get("unitPrice").toString());
            Double unitPriceOfLastWeek = 0.0;
            unitPriceOfLastWeek = Double.parseDouble(dataList.get(1).get("unitPrice").toString());

            NumberFormat priceFormatter = new DecimalFormat("#00");
            NumberFormat rateFormatter = new DecimalFormat("#.####");
            model.addAttribute("unitPrice", priceFormatter.format(unitPriceOfThisWeek));
            if (unitPriceOfThisWeek > 0 && unitPriceOfLastWeek > 0) {
                model.addAttribute("rate", rateFormatter.format(((unitPriceOfThisWeek - unitPriceOfLastWeek) * 100.0 / unitPriceOfLastWeek)));
            } else {
                model.addAttribute("rate", 0);
            }
        }
    }

    public final static String HOST = "http://api-ppe-salehouse.fangxiaoer.com/";
//    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    @PostConstruct
    public void postConstruct() {
        Thread thread = new MyThread();
        thread.start();
    }


    public class MyThread extends Thread {
        public void run() {
            forXml();
        }
    }

    @Scheduled(cron = "0 0 2 * * ?")
    public void forXml() {
        SiteMapUtil.init();
        List<String> projects = new ArrayList<>();
        List<LinkedTreeMap<String, Object>> result = null;

        result = getSiteMap(15, 1, 5000);
        if (result != null && result.size() != 0) {
            for (LinkedTreeMap<String, Object> map : result) {
                if (!StringUtils.isEmpty(map.get("infoId")) && !StringUtils.isEmpty(map.get("mapType"))) {
                    Integer mapType = Integer.parseInt(map.get("mapType") + "");
                    StringBuffer sf = new StringBuffer();
                    switch (mapType) {
                        case 1: sf.append("https://sy.fangxiaoer.com/salehouse/").append(map.get("infoId").toString()).append(".htm"); break;
                        case 2: sf.append("https://sy.fangxiaoer.com/rent/").append(map.get("infoId").toString()).append(".htm"); break;
                        case 3: sf.append("https://sy.fangxiaoer.com/shop/").append(map.get("infoId").toString()).append(".htm"); break;
                        case 4: sf.append("https://sy.fangxiaoer.com/scriptorium/").append(map.get("infoId").toString()).append(".htm"); break;
                        case 5: sf.append("https://sy.fangxiaoer.com/news/").append(map.get("infoId").toString()).append(".htm"); break;
                        case 6: sf.append("https://sy.fangxiaoer.com/video/").append(map.get("infoId").toString()).append(".htm"); break;
                        default:
                    }
                    if(!StringUtils.isEmpty(sf.toString())){
                        projects.add(sf.toString());
                    }
                }
            }
            SiteMapUtil.createXml(projects);
        }

        result = getSiteMap(1, 1, 5000);
        if (result != null && result.size() != 0) {
            for (LinkedTreeMap<String, Object> map : result) {
                if (!StringUtils.isEmpty(map.get("projectId")) && !StringUtils.isEmpty(map.get("projectType"))) {
                    StringBuffer sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-")
                            .append(map.get("projectType").toString()).append(".htm");
                    projects.add(sf.toString());
                }
            }
            SiteMapUtil.createXml(projects);
        }

        result = getSiteMap(8, 1, 5000);
        if (result != null && result.size() != 0) {
            for (LinkedTreeMap<String, Object> map : result) {
                if (!StringUtils.isEmpty(map.get("projectId"))) {
                    StringBuffer sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-1/info.htm");//基本信息
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-1/layout/pid").append(map.get("projectId").toString()).append("-pt1");//户型图
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-1/album.htm");//相册
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-1/say.htm");//楼盘说说
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-1/appraise.htm");//项目评价
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-1/ask.htm");//项目咨询
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-1/news.htm");//销售动态
                    projects.add(sf.toString());
                }
            }
        }
        SiteMapUtil.createXml(projects);

        result = getSiteMap(9, 1, 5000);
        if (result != null && result.size() != 0) {
            for (LinkedTreeMap<String, Object> map : result) {
                if (!StringUtils.isEmpty(map.get("projectId"))) {
                    StringBuffer sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-2/info.htm");//基本信息
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-2/layout.htm");//户型图
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-2/album.htm");//相册
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-2/news.htm");//销售动态
                    projects.add(sf.toString());
                }
            }
        }
        SiteMapUtil.createXml(projects);

        result = getSiteMap(10, 1, 5000);
        if (result != null && result.size() != 0) {
            for (LinkedTreeMap<String, Object> map : result) {
                if (!StringUtils.isEmpty(map.get("projectId"))) {
                    StringBuffer sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-3/album.htm");//相册
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-3/say.htm");//楼盘说说
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-3/appraise.htm");//项目评价
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-3/ask.htm");//项目咨询
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/house/").append(map.get("projectId").toString()).append("-3/news.htm");//销售动态
                    projects.add(sf.toString());
                }
            }
        }
        SiteMapUtil.createXml(projects);

        for (int i = 1; i < 10; i++) {
            result = getSiteMap(14, i, 1000);
            if (result != null && result.size() != 0) {
                for (LinkedTreeMap<String, Object> map : result) {
                    Gson gson = new Gson();
                    SubBean subBean = gson.fromJson(gson.toJson(map), SubBean.class);
                    Integer subId = subBean.getSubId();
                    StringBuffer sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/saleVillages/").append(subId).append("/index.htm");
                    projects.add(sf.toString());
                    if(subBean.getSaleCount() > 0){
                        sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/saleVillages/").append(subId).append("/plotSecondVillage/-v").append(subId);
                        projects.add(sf.toString());
                    }
                    if(subBean.getRentCount() > 0){
                        sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/saleVillages/").append(subId).append("/plotRentVillage/-v").append(subId);
                        projects.add(sf.toString());
                    }
                    if(subBean.getPicCount() > 0){
                        sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/saleVillages/").append(subId).append("/plotRealImage.htm");
                        projects.add(sf.toString());
                    }
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/saleVillages/").append(subId).append("/plotSupport.htm");
                    projects.add(sf.toString());
                    sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/saleVillages/").append(subId).append("/plotAsk.htm");
                    projects.add(sf.toString());
                    if(subBean.getExCount() > 0){
                        sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/saleVillages/").append(subId).append("/plotExpertInterpretation.htm");
                        projects.add(sf.toString());
                    }
                }
                SiteMapUtil.createXml(projects);
            } else break;
        }

        for (int i = 1; i < 10; i++) {
            result = getSiteMap(2, i, 5000);
            if (result != null && result.size() != 0) {
                for (LinkedTreeMap<String, Object> map : result) {
                    if (!StringUtils.isEmpty(map.get("houseId"))) {
                        StringBuffer sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/salehouse/").append(map.get("houseId").toString()).append(".htm");
                        projects.add(sf.toString());
                    }
                }
                SiteMapUtil.createXml(projects);
            } else break;
        }

        for (int i = 1; i < 10; i++) {
            result = getSiteMap(3, i, 5000);
            if (result != null && result.size() != 0) {
                for (LinkedTreeMap<String, Object> map : result) {
                    if (!StringUtils.isEmpty(map.get("houseId"))) {
                        StringBuffer sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/rent/").append(map.get("houseId").toString()).append(".htm");
                        projects.add(sf.toString());
                    }
                }
                SiteMapUtil.createXml(projects);
            } else break;
        }

        for (int i = 1; i < 10; i++) {
            result = getSiteMap(4, i, 5000);
            if (result != null && result.size() != 0) {
                for (LinkedTreeMap<String, Object> map : result) {
                    if (!StringUtils.isEmpty(map.get("houseId"))) {
                        StringBuffer sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/shop/").append(map.get("houseId").toString()).append(".htm");
                        projects.add(sf.toString());
                    }
                }
                SiteMapUtil.createXml(projects);
            } else break;
        }

        for (int i = 1; i < 10; i++) {
            result = getSiteMap(13, i, 5000);
            if (result != null && result.size() != 0) {
                for (LinkedTreeMap<String, Object> map : result) {
                    if (!StringUtils.isEmpty(map.get("houseId"))) {
                        StringBuffer sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/scriptoriums/").append(map.get("houseId").toString()).append(".htm");
                        projects.add(sf.toString());
                    }
                }
                SiteMapUtil.createXml(projects);
            } else break;
        }


        for (int i = 1; i < 10; i++) {
            result = getSiteMap(5, i, 5000);
            if (result != null && result.size() != 0) {
                for (LinkedTreeMap<String, Object> map : result) {
                    if (!StringUtils.isEmpty(map.get("newsId")) && !StringUtils.isEmpty(map.get("categoryId"))) {
                        StringBuffer sf = new StringBuffer();
                        if (map.get("categoryId").toString().equals("150")) {
                            sf.append("https://sy.fangxiaoer.com/activity/").append(map.get("newsId").toString()).append(".htm");
                        } else {
                            sf.append("https://sy.fangxiaoer.com/news/").append(map.get("newsId").toString()).append(".htm");
                        }
                        projects.add(sf.toString());
                    }
                }
                SiteMapUtil.createXml(projects);
            } else break;
        }

        for (int i = 1; i < 10; i++) {
            result = getSiteMap(6, i, 5000);
            if (result != null && result.size() != 0) {
                for (LinkedTreeMap<String, Object> map : result) {
                    if (!StringUtils.isEmpty(map.get("videoId"))) {
                        StringBuffer sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/video/").append(map.get("videoId").toString()).append(".htm");
                        projects.add(sf.toString());
                    }
                }
                SiteMapUtil.createXml(projects);
            } else break;
        }

        for (int i = 1; i < 10; i++) {
            result = getSiteMap(7, i, 5000);
            if (result != null && result.size() != 0) {
                for (LinkedTreeMap<String, Object> map : result) {
                    if (!StringUtils.isEmpty(map.get("newsId"))) {
                        StringBuffer sf = new StringBuffer();
                        sf.append("https://sy.fangxiaoer.com/activity/").append(map.get("newsId").toString()).append(".htm");
                        projects.add(sf.toString());
                    }
                }
                SiteMapUtil.createXml(projects);
            } else break;
        }

        result = getSiteMap(11, 1, 5000);
        if (result != null && result.size() != 0) {
            for (LinkedTreeMap<String, Object> map : result) {
                if (!StringUtils.isEmpty(map.get("schoolId"))) {
                    StringBuffer sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/schoolhouses/").append(map.get("schoolId").toString());
                    projects.add(sf.toString());
                }
            }
        }
        SiteMapUtil.createXml(projects);

        result = getSiteMap(12, 1, 5000);
        if (result != null && result.size() != 0) {
            for (LinkedTreeMap<String, Object> map : result) {
                if (!StringUtils.isEmpty(map.get("projectId")) && !StringUtils.isEmpty(map.get("projectType"))) {
                    StringBuffer sf = new StringBuffer();
                    sf.append("https://sy.fangxiaoer.com/").append(map.get("projectId").toString()).append("-")
                            .append(map.get("projectType").toString()).append("/decoration.htm");
                    projects.add(sf.toString());
                }
            }
        }
        SiteMapUtil.createXml(projects);

//        //分站siteMap
//        List<LinkedTreeMap<String, Object>> citys = (List<LinkedTreeMap<String, Object>>)getGlobalStieMap(Constants.GET_GETCITYS,"1",1,1);
//        //1、新房，2、二手房，3、租房，4、商铺，5、楼盘户型
//        String[] types = {"1","2","3","4"};
//        if(citys != null && citys.size() != 0){
//            StringBuffer sb = new StringBuffer();
//            for (LinkedTreeMap<String, Object> city:citys){
//                if(city.get("letter") != null && !city.get("letter").toString().equals("sy")){
//                    for (String type:types){
//                        for (int i = 1;i<10;i++){
//                            sb.append("http://").append(city.get("letter")).append(Constants.GET_GETGOLBALSITE_MAP);
//                            result = getGlobalStieMap(sb.toString(),type,i,5000);
//                            sb.setLength(0);
//                            if(result != null && result.size() !=0){
//                                for (Object s:result){
//                                    if (s!=null && s!="") {
//                                        projects.add(s.toString());
//                                    }
//                                }
//                            }
//                        }
//                        if(projects != null && projects.size() != 0){
//                            SiteMapUtil.createXml(projects);
//                        }
//                    }
//                }
//            }
//        }
        SiteMapUtil.createIndexXml();
    }

    public List<LinkedTreeMap<String, Object>> getSiteMap(Integer type, Integer page, Integer pageSize) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.GET_SITEMAP, new Params("type", type).add("page", page).add("pageSize", pageSize).get());
        if (result == null) {
            return null;
        }
        int status = Double.valueOf((double) result.get("status")).intValue();
        if (status == 1) {
            return (List<LinkedTreeMap<String, Object>>) result.get("content");
        }
        return null;
    }
    public List getGlobalStieMap(String apiUrl,String type, Integer page, Integer pageSize) {
        try {
            HashMap<String, Object> result = HttpUtil.connectApi(apiUrl, new Params("type", type).add("page", page).add("pageSize", pageSize).get());
            if (result == null || result.size() ==0) {
                return null;
            }
            int status = Double.valueOf((double) result.get("status")).intValue();
            if (status == 1) {
                return (List) result.get("content");
            }
        }catch (Exception e){
        }
        return null;
    }

    /**
     * 相应站点跳转
     * @param memberType  1  my   2 agent
     * @param response
     */
    public static void toMyOrAgent(String memberType, HttpServletResponse response){
        if ("1".equals(memberType)){
            try {
                response.sendRedirect("https://my.fangxiaoer.com/");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if ("2".equals(memberType)){
            try {
                response.sendRedirect("https://agent.fangxiaoer.com/redirectAgent");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 主页友情链接
     * @param model
     * <AUTHOR> 2018-7-20
     */
    public void getLinks(Model model) {
        HashMap<String,Object> result = HttpUtil.connectApi(Constants.INDEX_FRIEND_LINKS ,new Params().add("type","1").get());
        if(Double.valueOf(result.get("status").toString()).intValue() == 1){
            model.addAttribute("indexLinks", result.get("content"));
        }else {
            model.addAttribute("indexLinks", "");
        }
    }

    /**
     * 模糊查询
     *
     * @param key
     * @param search
     * @param
     * @return
     */
    public List<LinkedTreeMap<String, Object>> queryLikeMap(String key, String search, Integer page, Integer pageSize) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.PROJECT_SEARCH_MAP, new Params().add("search",search).add("type",key).add("page","").add("pageSize","").get());
        List<LinkedTreeMap<String, Object>> info = (List<LinkedTreeMap<String, Object>>) result.get("content");
        return info;
    }
    /**
     * 模糊查询
     *
     * @param
     * @return
     */
    public HashMap<String, Object> queryHouseMap(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.PROJECT_SEARCH_MAP_LIST, hashMap);
    }

    /**
     * 地图找房地铁站点
     * @param hashMap
     * @return
     * <AUTHOR> 2019-1-22
     */
    public HashMap<String, Object> searchStationMap(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.VIEW_STATION_MAP, hashMap);
    }

    /**
     * 处理小区热度列表
     * @return
     */
    public void hotList(Model model){
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.VIEW_HOT_SUB_LIST);
        if(result == null || StringUtils.isEmpty(result.get("content"))){
            model.addAttribute("hotSubs", new ArrayList<>());
        }else{
            List<LinkedTreeMap<String, Object>> tempValue = (List<LinkedTreeMap<String, Object>>) result.get("content");
            BigDecimal firstNum = BigDecimal.ZERO;
            BigDecimal maxHot = new BigDecimal(10000);
            for (int i = 0; i < tempValue.size(); i++) {
                LinkedTreeMap<String, Object> value = tempValue.get(i);
                BigDecimal nums = new BigDecimal(value.get("num") + "");
                if(i == 0){
                    firstNum = nums;
                }
                if(firstNum.doubleValue() == 0d){
                    value.put("hotPoint", maxHot.doubleValue() + "");
                }else{
                    value.put("hotPoint", Math.ceil(nums.multiply(maxHot).divide(firstNum, 1, BigDecimal.ROUND_HALF_UP).doubleValue()) + "");
                }
            }
            model.addAttribute("hotSubs", tempValue);
        }
    }

    /**
     * 首页视频5条 和 项目销售动态12条
     *
     * <AUTHOR> power
     * @date 2022-03-24 20:43
     * @param model Model对象
     */
    public void getHomePageVideosAndProjectDynamics(Model model) {
        HttpUtil.connectApi(Constants.NEW_VIDEO_LIST, new Params("pageSize", 4).get(), model, "videos");
        HttpUtil.connectApi(Constants.NEW_PROJECT_DYNAMIC_LIST, new Params("pageSize", 12).add("page", 1).get(), model, "projectDynamics");
        HttpUtil.connectApi(Constants.WEIZAN_LIVE_LIST, new Params("page_size", 4).add("page", 1).get(), model, "wzLive");
    }

    public void grayTheme(Model model){
        String result = (String) HttpUtil.connectApi(Constants.GRAY_THEME, new Params().get(), model);
        model.addAttribute("theme", result);
    }
}
