package com.fangxiaoer.service;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.common.ParamsUtil;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @Date 2018/7/19
 */
@Service
public class NeedHouseService {
    public void getNeedHouses(String baseUrl, String params, Model model){
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.VIEW_NEED_HOUSES,model,baseUrl,params);

        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.VIEW_NEED_HOUSES);
        if (baseUrl.equals("/needSeconds/")) {
            paramMap.put("sources", "2");
        } else if (baseUrl.equals("/needRents/")) {
            paramMap.put("sources", "12");
        }
        paramMap.put("pageSize","20");

        ParamsUtil.addSeoTitle(model, "orderKey,vgType");
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_NEED_HOUSES,paramMap);

        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "needHosues", model);
        new MyPageable(paramMap, 20).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("schoolRight",HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type","9").get(),true).get("content"));
    }


    public void getNeedHouse (String sessionId,String param, Model model) {
        LinkedTreeMap<String, Object> needHouse =
                (LinkedTreeMap<String, Object>)HttpUtil.connectApi(Constants.VIEW_NEED_HOUSE,
                new Params("id",param).add("sessionId",sessionId).get(),model);
        model.addAttribute("needHouse",needHouse);
    }
}
