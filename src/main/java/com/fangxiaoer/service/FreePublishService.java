package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.model.*;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2017/9/4.
 */
@Service
public class FreePublishService {
    /**
     * 获取二手房页面需要的信息
     */
    public void getSecondHouse(Model model) {
        //房屋类型
        HashMap<String, Object> houseTypeRes = HttpUtil.connectApi(Constants.GET_HOUSE_TYPE);
        List<LinkedTreeMap<String, Object>> houseTypes = (List<LinkedTreeMap<String, Object>>) houseTypeRes.get("content");
        model.addAttribute("types", houseTypes);

        //装修类型
        HashMap<String, Object> fitmentTypeRes = HttpUtil.connectApi(Constants.FILTER_HOUSE_DECORATION);
        List<LinkedTreeMap<String, Object>> fitmentTypes = (List<LinkedTreeMap<String, Object>>) fitmentTypeRes.get("content");
        model.addAttribute("fitment", fitmentTypes);

        //房屋朝向
        HashMap<String, Object> forwardTypeRes = HttpUtil.connectApi(Constants.FILTER_HOUSE_FORWARD);
        List<LinkedTreeMap<String, Object>> forwardTypes = (List<LinkedTreeMap<String, Object>>) forwardTypeRes.get("content");
        model.addAttribute("forward", forwardTypes);

        //房屋产权
        HashMap<String, Object> propertyRightTypeRes = HttpUtil.connectApi(Constants.GET_HOUSE_PROPERTY);
        List<LinkedTreeMap<String, Object>> propertyRightTypes = (List<LinkedTreeMap<String, Object>>) propertyRightTypeRes.get("content");
        model.addAttribute("propertyRight", propertyRightTypes);

        //房屋类别
        HashMap<String, Object> houseKindRes = HttpUtil.connectApi(Constants.GET_SCONDHOUSE_TYPE);
        List<LinkedTreeMap<String, Object>> houseKinds = (List<LinkedTreeMap<String, Object>>) houseKindRes.get("content");
        model.addAttribute("kinds", houseKinds);

        //房屋特色
        HashMap<String, Object> houseTraitRes = HttpUtil.connectApi(Constants.GET_HOUSE_FEATURE);
        List<LinkedTreeMap<String, Object>> houseTraits = (List<LinkedTreeMap<String, Object>>) houseTraitRes.get("content");
        model.addAttribute("traits", houseTraits);
    }

    /**
     * 获取出租房源需要的消息
     */
    public void getRent(Model model) {
        //房屋类型
        HashMap<String, Object> houseTypeRes = HttpUtil.connectApi(Constants.GET_HOUSE_TYPE);
        List<LinkedTreeMap<String, Object>> houseTypes = (List<LinkedTreeMap<String, Object>>) houseTypeRes.get("content");
        model.addAttribute("types", houseTypes);
        //装修类型
        HashMap<String, Object> fitmentTypeRes = HttpUtil.connectApi(Constants.FILTER_HOUSE_DECORATION);
        List<LinkedTreeMap<String, Object>> fitmentTypes = (List<LinkedTreeMap<String, Object>>) fitmentTypeRes.get("content");
        model.addAttribute("fitment", fitmentTypes);
        //房屋朝向
        HashMap<String, Object> forwardTypeRes = HttpUtil.connectApi(Constants.FILTER_HOUSE_FORWARD);
        List<LinkedTreeMap<String, Object>> forwardTypes = (List<LinkedTreeMap<String, Object>>) forwardTypeRes.get("content");
        model.addAttribute("forward", forwardTypes);
        //房屋配置
        HashMap<String, Object> establishes = HttpUtil.connectApi(Constants.GET_OTHER_ESTABLISH);
        List<LinkedTreeMap<String, Object>> establish = (List<LinkedTreeMap<String, Object>>) establishes.get("content");
        model.addAttribute("establish", establish);
        //付款方式
        HashMap<String, Object> payments = HttpUtil.connectApi(Constants.GET_PAYMENT_FILTER);
        List<LinkedTreeMap<String, Object>> payment = (List<LinkedTreeMap<String, Object>>) payments.get("content");
        model.addAttribute("payment", payment);
        //卧室类别
        HashMap<String, Object> bedrooms = HttpUtil.connectApi(Constants.GET_BEDROOM_FILTER);
        List<LinkedTreeMap<String, Object>> bedroom = (List<LinkedTreeMap<String, Object>>) bedrooms.get("content");
        model.addAttribute("bedroom", bedroom);
        //性别限制
        HashMap<String, Object> sexs = HttpUtil.connectApi(Constants.GET_SEX_FILTER);
        List<LinkedTreeMap<String, Object>> sex = (List<LinkedTreeMap<String, Object>>) sexs.get("content");
        model.addAttribute("sex", sex);
        //房屋特色
        HashMap<String, Object> houseTraitRes = HttpUtil.connectApi(Constants.GET_RENT_TRAITS);
        List<LinkedTreeMap<String, Object>> houseTraits = (List<LinkedTreeMap<String, Object>>) houseTraitRes.get("content");
        model.addAttribute("traits", houseTraits);
    }

    /**
     * 获取商铺信息
     */
    public void getShop(Model model) {
        //区域
        HashMap<String, Object> regions = HttpUtil.connectApi(Constants.FILTER_SCDHOUSE_REGIONS);
        List<LinkedTreeMap<String, Object>> region = (List<LinkedTreeMap<String, Object>>) regions.get("content");
        model.addAttribute("region", region);
        //商铺类型
        HashMap<String, Object> shopTypes = HttpUtil.connectApi(Constants.FILTER_SHOP_TYPE);
        List<LinkedTreeMap<String, Object>> shopType = (List<LinkedTreeMap<String, Object>>) shopTypes.get("content");
        model.addAttribute("type", shopType);
        //商铺经营行业
        HashMap<String, Object> industries = HttpUtil.connectApi(Constants.FILTER_SHOP_INDUSTRY);
        List<LinkedTreeMap<String, Object>> industry = (List<LinkedTreeMap<String, Object>>) industries.get("content");
        model.addAttribute("industry", industry);
        //商铺特色
        HashMap<String, Object> traits = HttpUtil.connectApi(Constants.GET_SHOP_TRAITS);
        List<LinkedTreeMap<String, Object>> trait = (List<LinkedTreeMap<String, Object>>) traits.get("content");
        model.addAttribute("trait", trait);
        //可否分割
        HashMap<String, Object> cuts = HttpUtil.connectApi(Constants.FILTER_IS_CUT);
        List<LinkedTreeMap<String, Object>> cut = (List<LinkedTreeMap<String, Object>>) cuts.get("content");
        model.addAttribute("cut", cut);
        //付款方式
        HashMap<String, Object> payments = HttpUtil.connectApi(Constants.GET_PAYMENT_FILTER);
        List<LinkedTreeMap<String, Object>> payment = (List<LinkedTreeMap<String, Object>>) payments.get("content");
        model.addAttribute("payment", payment);
    }

    /**
     * 二手房发布
     *
     * @param request
     * @param response
     * @param saleHouseEntity 租房信息
     * @param model
     * @param messageCode     验证码
     */
    public void dealSale(HttpServletRequest request, HttpServletResponse response, SaleHouseEntity saleHouseEntity, Model model, String messageCode,String password_text) {
        String sessionId = HttpUtil.checkSessionId(request);//检查sessionId
        if (StringUtils.isEmpty(sessionId)) {
            sessionId = disposeSessionInfo(messageCode, request, response, saleHouseEntity.getOwnerphone(), model);
            if (StringUtils.isEmpty(sessionId)) {
                boolean isAgent = model.containsAttribute("isAgent");
                if (isAgent) { //如果当前操作人类型是经纪人 则建议用户去经纪人站进行操作
                    model.addAttribute("alert", "5");
                } else {
                    model.addAttribute("alert", "2");
                }
                return;
            }
        }
        //查询这个人发了几套房源
        HashMap<String, Object> houseNum = HttpUtil.connectApi(Constants.CHECK_HOUSENUM, new Params("sessionId", sessionId).get());
        LinkedTreeMap<String, Object> housenum = (LinkedTreeMap) houseNum.get("content");
        Integer saleCount = Integer.parseInt(housenum.get("saleHouseCount").toString());
        if (saleCount >= 6 && StringUtils.isEmpty(saleHouseEntity.getHouseid())) {
            //返回“亲，个人账户最多只能发布6条房源噢！”
            model.addAttribute("alert", "6");
        } else {
            //返回“房源信息发布成功，稍后会有工作人员跟你联系”
            //model.addAttribute("alert", "1");
            HashMap<String, Object> searchMap = Utils.transBean2Map(saleHouseEntity);
            String imglist = "";
            if (searchMap.get("saleprice").equals("面议")){
                searchMap.put("saleprice",0);
            }
            if (!StringUtils.isEmpty(searchMap.get("imgvalue"))) {
                imglist = subImage(searchMap.get("imgvalue").toString());
            }
            searchMap.put("imgvalue", imglist);
            searchMap.put("sessionId", sessionId);
            HashMap<String, Object> addSecondHouse = HttpUtil.connectApi(Constants.ADD_SECONDHOUSE, searchMap);
            Integer status1 = Double.valueOf(addSecondHouse.get("status").toString()).intValue();
            if (status1 == 1) {
                model.addAttribute("alert", "1");
                model.addAttribute("houseId", addSecondHouse.get("content").toString().replaceAll(".0+$",""));
            } else {
                model.addAttribute("alert", "2");
                model.addAttribute("houseId", "0");
            }
        }
    }


    /**
     * 租房发布
     *
     * @param request
     * @param response
     * @param rentHouseEntity
     * @param model
     * @param messageCode
     */
    public void dealRent(HttpServletRequest request, HttpServletResponse response, RentHouseEntity rentHouseEntity, Model model, String messageCode) {
        String sessionId = HttpUtil.checkSessionId(request);//检查sessionId
        if (StringUtils.isEmpty(sessionId)) {
            sessionId = disposeSessionInfo(messageCode, request, response, rentHouseEntity.getOwnerphone(), model);
            if (StringUtils.isEmpty(sessionId)) {
                boolean isAgent = model.containsAttribute("isAgent");
                if (isAgent) { //如果当前操作人类型是经纪人 则建议用户去经纪人站进行操作
                    model.addAttribute("alert", "5");
                } else {
                    model.addAttribute("alert", "2");
                }
                return;
            }
        }
        //查询这个人发了几套房源
        HashMap<String, Object> houseNum = HttpUtil.connectApi(Constants.CHECK_HOUSENUM, new Params("sessionId", sessionId).get());
        LinkedTreeMap<String, Object> housenum = (LinkedTreeMap) houseNum.get("content");
        Integer rentCount = Integer.parseInt(housenum.get("rentHouseCount").toString());
        if (rentCount >= 6 && StringUtils.isEmpty(rentHouseEntity.getHouseid())) {
            //返回“亲，个人账户最多只能发布6条房源噢！”
            model.addAttribute("alert", "6");
        } else {
            //返回“房源信息发布成功，稍后会有工作人员跟你联系”
            model.addAttribute("alert", "1");
            if (rentHouseEntity.getRentprice().equals("面议")) {
                rentHouseEntity.setRentprice("0");
            }
            HashMap<String, Object> searchMap = Utils.transBean2Map(rentHouseEntity);
            String imglist = "";
            if (!StringUtils.isEmpty(searchMap.get("imgvalue"))) {
                imglist = subImage(searchMap.get("imgvalue").toString());
            }
            searchMap.put("imgvalue", imglist);
            searchMap.put("sessionId", sessionId);
            HashMap<String, Object> addRentHouse = HttpUtil.connectApi(Constants.ADD_RENTHOUSE, searchMap);
            Integer status1 = Double.valueOf(addRentHouse.get("status").toString()).intValue();
            if (status1 == 1) {
                model.addAttribute("alert", "1");
                model.addAttribute("houseId", addRentHouse.get("content").toString().replaceAll(".0+$",""));
            } else {
                model.addAttribute("alert", "2");
                model.addAttribute("houseId", "0");
            }
            model.addAttribute("addStatu", addRentHouse);

        }
    }

    /**
     * 商业发布
     *
     * @param request
     * @param response
     * @param shopEntity
     * @param model
     * @param messageCode
     */
    public void dealShops(HttpServletRequest request, HttpServletResponse response, ShopEntity shopEntity, Model model, String messageCode) {
        String sessionId = HttpUtil.checkSessionId(request);//检查sessionId
        if (StringUtils.isEmpty(sessionId)) {
            sessionId = disposeSessionInfo(messageCode, request, response, shopEntity.getOwnerphone(), model);
            if (StringUtils.isEmpty(sessionId)) {
                boolean isAgent = model.containsAttribute("isAgent");
                if (isAgent) { //如果当前操作人类型是经纪人 则建议用户去经纪人站进行操作
                    model.addAttribute("alert", "5");
                } else {
                    model.addAttribute("alert", "2");
                }
                return;
            }
        }
        //查询这个人发了几套房源
        HashMap<String, Object> houseNum = HttpUtil.connectApi(Constants.CHECK_HOUSENUM, new Params("sessionId", sessionId).get());
        LinkedTreeMap<String, Object> housenum = (LinkedTreeMap) houseNum.get("content");
        Integer shopsCount = Integer.parseInt(housenum.get("shopsHouseCount").toString());
        if (shopsCount >= 6 && StringUtils.isEmpty(shopEntity.getHouseid())) {
            //返回“亲，个人账户最多只能发布3条房源噢！”
            model.addAttribute("alert", "6");
        } else {
            //返回“房源信息发布成功，稍后会有工作人员跟你联系”

            if (shopEntity.getShoptype().equals("3")) {
                shopEntity.setPrice(shopEntity.getPrice().replace(",", ""));
                shopEntity.setTranfee(shopEntity.getTranfee().replace(",,", ""));
            }
            HashMap<String, Object> searchMap = Utils.transBean2Map(shopEntity);
            if (searchMap.get("price").equals("NaN") || searchMap.get("price").equals("面议")) {
                searchMap.put("price", 0);
            }
            if (searchMap.containsKey("tranfee") && searchMap.get("tranfee").equals("面议")) {
                searchMap.put("tranfee", 0);
            }
            String imglist = "";
            if (!StringUtils.isEmpty(searchMap.get("imgvalue"))) {
                imglist = subImage(searchMap.get("imgvalue").toString());
            }
            searchMap.put("imgvalue", imglist);
            searchMap.put("sessionId", sessionId);
            searchMap.put("shopid", searchMap.get("houseid"));
            HashMap<String, Object> addShopsHouse = HttpUtil.connectApi(Constants.ADD_SHOPS, searchMap);
            Integer status = Double.valueOf(addShopsHouse.get("status").toString()).intValue();
            if (status == 1) {
                model.addAttribute("alert", "1");
                model.addAttribute("houseId", addShopsHouse.get("content").toString().replaceAll(".0+$",""));
            } else {
                model.addAttribute("alert", "2");
                model.addAttribute("houseId", "0");
            }
            model.addAttribute("addStatu", addShopsHouse);
            //Utils.addDomainCookie(sessionId, response);
        }
    }

    /**
     * 写字楼发布
     *
     * @param request
     * @param response
     * @param officeEntity
     * @param model
     */
    public HashMap dealoffices(HttpServletRequest request, HttpServletResponse response, OfficeEntity officeEntity, Model model) {
        HashMap hashMap = new HashMap();
        String sessionId = HttpUtil.checkSessionId(request);//检查sessionId
        if (StringUtils.isEmpty(sessionId)) {
            sessionId = disposeSessionInfo(officeEntity.getPass(), request, response, officeEntity.getPhone(), model);
            if (StringUtils.isEmpty(sessionId)) {
                //提示账号密码或者账号验证码错误
                model.addAttribute("publishResult", "2");
                boolean isAgent = model.containsAttribute("isAgent");
                if (isAgent) { //如果当前操作人类型是经纪人 则建议用户去经纪人站进行操作
                    hashMap.put("status", "5");
                } else {
                    hashMap.put("status", "2");
                }
                return hashMap;
            }
        }
        //查询这个人发了几套房源
        HashMap<String, Object> houseNum = HttpUtil.connectApi(Constants.CHECK_HOUSENUM, new Params("sessionId", sessionId).get());
        LinkedTreeMap<String, Object> housenum = (LinkedTreeMap) houseNum.get("content");
        Integer shopsCount = Integer.parseInt(housenum.get("officeCount").toString());
        if (shopsCount >= 6 && StringUtils.isEmpty(officeEntity.getShopid())) {
            //返回“亲，个人账户最多只能发布6条房源噢！”
            hashMap.put("status",6);
            return hashMap;
        } else {
            HashMap<String, Object> searchMap = Utils.transBean2Map(officeEntity);
            StringBuffer imglist = new StringBuffer("");
//            if (!StringUtils.isEmpty(searchMap.get("imgvalue"))) {
//                imglist = subImage(searchMap.get("imgvalue").toString());
//            }
            searchMap.put("imgvalue", searchMap.get("imgvalue").toString());
            searchMap.put("sessionId", sessionId);
            searchMap.put("houseType", 4);
            HashMap<String, Object> addShopsHouse = HttpUtil.connectApi(Constants.ADD_OFFICE, searchMap);
            Integer status = Double.valueOf(addShopsHouse.get("status").toString()).intValue();
            if (status == 1) {
                //返回“房源信息发布成功，稍后会有工作人员跟你联系”
                hashMap.put("status",1);
                hashMap.put("sessionId", sessionId);
                hashMap.put("houseId", addShopsHouse.get("content").toString().replaceAll(".0+$",""));
                return hashMap;
            }else {
                hashMap.put("status",4);
                hashMap.put("houseId","0");
                hashMap.put("sessionId", sessionId);
                return hashMap;//发布失败
            }
        }
    }

    /**
     * 修改二手房
     */
    public void updateSecond(String sessionId, String houseID, Model model) {
        HashMap sale = HttpUtil.connectApi(Constants.FIND_HOUSE_ENTITY, new Params("sessionId", sessionId).add("houseId", houseID).add("houseType", "1").get());
        LinkedTreeMap<String, Object> salemap = (LinkedTreeMap<String, Object>) sale.get("content");
        Gson gson = new Gson();
        SaleHouseEntity saleHouseEntity = gson.fromJson(gson.toJson(salemap), SaleHouseEntity.class);
        saleHouseEntity.setImgurl(fixPhoto(salemap.get("picEntityList")));
        model.addAttribute("saleHouseEntity", saleHouseEntity);
    }

    /**
     * 修改租房
     */
    public void updateRent(String sessionId, String houseID, Model model) {
        HashMap rent = HttpUtil.connectApi(Constants.FIND_HOUSE_ENTITY, new Params("sessionId", sessionId).add("houseId", houseID).add("houseType", "2").get());
        LinkedTreeMap<String, Object> rentmap = (LinkedTreeMap<String, Object>) rent.get("content");
        Gson gson = new Gson();
        RentHouseEntity rentHouseEntity = gson.fromJson(gson.toJson(rentmap), RentHouseEntity.class);
        if (rentHouseEntity.getBuildarea() != null && rentHouseEntity.getBuildarea().indexOf(".") != -1) {
            rentHouseEntity.setBuildarea(rentHouseEntity.getBuildarea().replaceAll("0+?$","").replaceAll("[.]$",""));
        }
        rentHouseEntity.setImgurl(fixPhoto(rentmap.get("picEntityList")));
        model.addAttribute("rentHouseEntity", rentHouseEntity);
    }

    /**
     * 修改商铺
     */
    public void updateShop(String sessionId, String houseID, Model model, Integer houseType) {
        HashMap shop = HttpUtil.connectApi(Constants.FIND_HOUSE_ENTITY, new Params("sessionId", sessionId).add("houseId", houseID).add("houseType", houseType).get());
        LinkedTreeMap<String, Object> shopmap = (LinkedTreeMap<String, Object>) shop.get("content");
        Gson gson = new Gson();
        ShopEntity shopEntity = gson.fromJson(gson.toJson(shopmap), ShopEntity.class);
        shopEntity.setImgurl(fixPhoto(shopmap.get("picEntityList")));
        shopEntity.setHouseid(shopEntity.getShopid());
        model.addAttribute("plates", plates(shopEntity.getRegionid()));
        model.addAttribute("shopEntity", shopEntity);
    }


    //解析朝向，装修，住宅
    public String getIdByName(String type, Object Name) {
        String name = Name.toString();
        String id = "";
        List<LinkedTreeMap<String, Object>> list = null;
        if (type.equals("forward")) {
            list = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.FILTER_HOUSE_FORWARD).get("content");
        } else if (type.equals("fitment")) {
            list = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.FILTER_HOUSE_DECORATION).get("content");
        } else if (type.equals("rentType")) {
            list = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.GET_HOUSE_TYPE).get("content");
        }
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).get("name").toString().equals(name)) {
                id = list.get(i).get("id").toString();
            }
        }
        return id;
    }

    //解析特色,配置
    public String getIdsByNames(String house, String type, Object Names) {
        String[] names = Names.toString().split(",");
        List<LinkedTreeMap<String, Object>> list = null;
        String name = "";
        if (house.equals("sale") && type.equals("housetrait")) {
            list = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.GET_HOUSE_FEATURE).get("content");
        } else if (house.equals("rent") && type.equals("otherestablish")) {
            list = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.GET_OTHER_ESTABLISH).get("content");
        } else if (house.equals("shop") && type.equals("housetrait")) {
            list = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.GET_SHOP_TRAITS).get("content");
        }
        for (int i = 0; i < list.size(); i++) {
            for (int j = 0; j < names.length; j++) {
                if (list.get(i).get("name").toString().equals(names[j])) {
                    name = name + "," + list.get(i).get("id");
                }
            }
        }
        return name;
    }

    //解析图片
    public String fixPhoto(Object picObject) {
        StringBuffer picurl = new StringBuffer();
        int num = 0;
        ArrayList pic = (ArrayList) picObject;
        for (int i = 0; i < pic.size(); i++) {
            LinkedTreeMap picMap = (LinkedTreeMap) pic.get(i);
            picurl.append(picMap.get("smallimageurl"));
            if (num < pic.size() - 1) {
                picurl.append(",");
            }
            num++;
        }
        return picurl.toString();
    }


    /**
     * session  and cookie 不存在sessionId  走接口
     *
     * @param messageCode
     * @param request
     * @param response
     * @param ownerPhone
     * @param model
     * @return
     */
    public String disposeSessionInfo(String messageCode, HttpServletRequest request, HttpServletResponse response, String ownerPhone, Model model) {
        String sessionId = "";
        HashMap<String, Object> islogin = HttpUtil.connectApi(Constants.SIMPLE_LOGIN, new Params("telNumber", ownerPhone).add("memberType", "1").add("loginWay", "1").add("pwd", Utils.MD5encode(messageCode)).get());
        Integer status = Double.valueOf(islogin.get("status").toString()).intValue();
        if (status != 1) {
            String msg = (String) islogin.get("msg");
            if (msg.equals("请使用经纪人专享的系统/APP进行操作")) model.addAttribute("isAgent", true); //如果是经纪人 在model中添加标识
            return sessionId;
        }

        LinkedTreeMap<String,Object> result = (LinkedTreeMap) islogin.get("content");
        Gson gson = new Gson();
        LoginResultFilter loginResultFilter = gson.fromJson(gson.toJson(result),LoginResultFilter.class);
        Utils.addSessionAndCookie(request.getSession(),response,loginResultFilter, null);
        return result.get("sessionId").toString();
    }

    /**
     * 处理图片
     *
     * @param imageList
     * @return
     */
    public String subImage(String imageList) {
        String imageValue =imageList.replaceAll("http://images.fangxiaoer.com/sy/esf/fy/middle/","")
                .replaceAll("noimage375275.jpg","").replaceAll(",+$","")
                .replaceAll("https://images.fangxiaoer.com/sy/esf/fy/middle/","")
                .replaceAll("https://images1.fangxiaoer.com/","")
                .replaceAll("http://images1.fangxiaoer.com/","")
                .replaceAll("/middle","");
        return imageValue;
    }

    /**
     * 板块
     *
     * @param regionid
     * @return
     */
    public List plates(String regionid) {
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put("regionId", regionid);
        HashMap<String, Object> searchs = HttpUtil.connectApi(Constants.GET_PLATES_BY_REGION, params);
        List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) searchs.get("content");
        return result;
    }


    /**
     * 检查经纪人特征
     * @param model
     * @param sessionId
     * @return
     */
    public Model checkMemberAgent(Model model,String sessionId){
        //如果当前操作人类型是经纪人 则建议用户去经纪人站进行操作
        HashMap<String, Object> memberInfo = HttpUtil.connectApi(Constants.VIEW_MEMBER_INFO, new Params("sessionId", sessionId).get());
        LinkedTreeMap<String, Object> memberContent = (LinkedTreeMap) memberInfo.get("content");
        String memberType  =  (String)memberContent.get("memberType");
        if(!memberType.equals("1")){
            model.addAttribute("alert", "5");
        }
        return model;
    }

    /**
     * 根据小区id 获取小区名称(2018.09.26)
     * @param subId
     * @return
     */
    public String getVillagesName (String subId){
        String subName ="";
        if(!StringUtils.isEmpty(subId)){
            HashMap hashMap = new HashMap();
            hashMap.put("subId",subId);
            HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL,hashMap);
            LinkedTreeMap result = (LinkedTreeMap<String,Object>)villageDetail.get("content");
            subName = (String)result.get("title");
        }
        return subName;
    }


    public HashMap<String, Object> publishRealEstate(RealEstate realEstate, HttpServletRequest request,Integer houseType){
        HashMap<String, Object> params = Utils.transBean2Map(realEstate);
        params.put("sessionId", HttpUtil.checkSessionId(request));
        params.put("houseType", houseType);
        HashMap<String, Object> result =  HttpUtil.connectApi(Constants.PUBLISH_REAL_ESTATE, params);
        int state = Double.valueOf(result.get("status").toString()).intValue();
        if(state == -1){
            Utils.removeSession(request.getSession());
        }
        return result;
    }


    public HashMap<String, Object> checkRealEstate(Integer houseId, Integer houseType, HttpServletRequest request){
        HashMap<String, Object> result =  HttpUtil.connectApi(Constants.CHECK_RAEL_ESTATE, new Params("houseId", houseId).add("houseType", houseType)
                .add("sessionId", HttpUtil.checkSessionId(request)).get());
        int state = Double.valueOf(result.get("status").toString()).intValue();
        if(state == -1){
            Utils.removeSession(request.getSession());
        }
        return result;
    }

    public HashMap<String, Object> getRealEstateInfo(Integer houseId, Integer houseType, HttpServletRequest request){
        HashMap<String, Object> result =  HttpUtil.connectApi(Constants.GET_REAL_ESTATE_INFO,
                new Params("houseId", houseId).add("houseType", houseType)
                        .add("sessionId", HttpUtil.checkSessionId(request)).get());
        int state = Double.valueOf(result.get("status").toString()).intValue();
        if(state == -1){
            Utils.removeSession(request.getSession());
        }
        return result;
    }

    /**
     * 求租求购列表
     * */
    public void seekOrderList(Model model) {
        HashMap<String, Object> seekList = HttpUtil.connectApi(Constants.GET_HOUSE_DEMAND_ORDER_LIST);
        List<LinkedTreeMap<String, Object>> addSeekList = (List<LinkedTreeMap<String, Object>>) seekList.get("content");
        model.addAttribute("addSeekList", addSeekList);
    }

    public void getSeekHouseList(Model model, String baseUrl, String params, Integer houseType) {
        // 获取筛选条件
        HashMap paramMap = ParamsUtil.analysisInput(params, Constants.GET_HOUSE_DEMAND_ORDER_LIST);
        ParamsUtil.addFilterIntoModel(Constants.GET_HOUSE_DEMAND_ORDER_LIST, model, baseUrl, params, paramMap);
        if (!StringUtils.isEmpty(paramMap.get("minPrice"))) {
            model.addAttribute("minPrice", paramMap.get("minPrice"));
        }
        if (!StringUtils.isEmpty(paramMap.get("maxPrice"))) {
            model.addAttribute("maxPrice", paramMap.get("maxPrice"));
        }
        if (!StringUtils.isEmpty(paramMap.get("minArea"))) {
            model.addAttribute("minArea", paramMap.get("minArea"));
        }
        if (!StringUtils.isEmpty(paramMap.get("maxArea"))) {
            model.addAttribute("maxArea", paramMap.get("maxArea"));
        }

        paramMap.put("houseType", String.valueOf(houseType));
        if (!StringUtils.isEmpty(paramMap.get("budget1"))) {
            Integer minPrice = null;
            Integer maxPrice = null;
            String priceKey = (String) paramMap.get("budget1");
            switch (priceKey) {
                case "1": maxPrice = 50; break;
                case "2": minPrice = 50; maxPrice = 80; break;
                case "3": minPrice = 80; maxPrice = 100; break;
            }
            if (minPrice != null ) {
                paramMap.put("minPrice", minPrice);
            }
            if (maxPrice != null) {
                paramMap.put("maxPrice", maxPrice);
            }
        }
        if (!StringUtils.isEmpty(paramMap.get("budget2"))) {
            Integer minPrice = null;
            Integer maxPrice = null;
            String priceKey = (String) paramMap.get("budget2");
            switch (priceKey) {
                case "1": maxPrice = 50; break;
                case "2": minPrice = 50; maxPrice = 80; break;
                case "3": minPrice = 80; maxPrice = 100; break;
                case "4": minPrice = 100; maxPrice = 120; break;
                case "5": minPrice = 120; maxPrice = 150; break;
                case "6": minPrice = 150; maxPrice = 200; break;
                case "7": minPrice = 200; maxPrice = 300; break;
                case "8": minPrice = 300; break;
            }
            if (minPrice != null ) {
                paramMap.put("minPrice", minPrice);
            }
            if (maxPrice != null) {
                paramMap.put("maxPrice", maxPrice);
            }
        }
        if (!StringUtils.isEmpty(paramMap.get("budget3"))) {
            Integer minPrice = null;
            Integer maxPrice = null;
            String priceKey = (String) paramMap.get("budget3");
            switch (priceKey) {
                case "0": maxPrice = 500; break;
                case "1": minPrice = 500; maxPrice = 1000; break;
                case "2": minPrice = 1000; maxPrice = 2000; break;
                case "3": minPrice = 2000; maxPrice = 3000; break;
                case "4": minPrice = 3000; break;
            }
            if (minPrice != null ) {
                paramMap.put("minPrice", minPrice);
            }
            if (maxPrice != null) {
                paramMap.put("maxPrice", maxPrice);
            }
        }
        if (!StringUtils.isEmpty(paramMap.get("area4"))) {
            Integer minArea = null;
            Integer maxArea = null;
            String priceKey = (String) paramMap.get("area4");
            switch (priceKey) {
                case "1": maxArea = 20; break;
                case "2": minArea = 20; maxArea = 50; break;
                case "3": minArea = 50; maxArea = 100; break;
                case "4": minArea = 100; maxArea = 200; break;
                case "5": minArea = 500; break;
            }
            if (minArea != null ) {
                paramMap.put("minArea", minArea);
            }
            if (maxArea != null) {
                paramMap.put("maxArea", maxArea);
            }
        }
        if (!StringUtils.isEmpty(paramMap.get("area5"))) {
            Integer minArea = null;
            Integer maxArea = null;
            String priceKey = (String) paramMap.get("area5");
            switch (priceKey) {
                case "1": maxArea = 100; break;
                case "2": minArea = 100; maxArea = 200; break;
                case "3": minArea = 200; maxArea = 300; break;
                case "4": minArea = 300; maxArea = 500; break;
                case "5": minArea = 500; maxArea = 800; break;
                case "6": minArea = 800; maxArea = 1000; break;
                case "7": minArea = 1000; maxArea = 2000; break;
                case "8": minArea = 2000; break;
            }
            if (minArea != null ) {
                paramMap.put("minArea", minArea);
            }
            if (maxArea != null) {
                paramMap.put("maxArea", maxArea);
            }
        }
        // 请求接口 获取列表数据
        HashMap result = HttpUtil.connectApi(Constants.GET_HOUSE_DEMAND_ORDER_LIST, paramMap);
        // 分页信息
        HttpUtil.handleServiceList(result, "msg", "dataList", model);
        new MyPageable(paramMap, 20).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }
}
