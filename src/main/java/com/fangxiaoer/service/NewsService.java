package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.AgentNewsDetail;
import com.fangxiaoer.model.NewsDetail;
import com.fangxiaoer.model.WeizanSearch;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2017/6/20.
 */
@Service
public class NewsService {

    /**
     * 资讯列表页
     * 2018-6-17修改，搜索内容为空加入推荐信息5条
     */
    public void getNews(String baseUrl, String params, Model model,HttpServletRequest request, String systemDate){
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.GET_NEWS_LIST,model,baseUrl,params);

        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_NEWS_LIST);
        if (paramMap.containsKey("categoryId")){
            model.addAttribute("categoryId",paramMap.get("categoryId"));
            if(paramMap.get("categoryId").toString().equals("156")){
                paramMap.put("categoryId","120");
            }
        }else{
            paramMap.put("categoryId","160");
            model.addAttribute("categoryId","160");
        }
        if(!StringUtils.isEmpty(paramMap.get("searchTitle"))){
            paramMap.remove("categoryId");
            model.addAttribute("categoryId",null);
        }
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_NEWS_LIST,paramMap);
        //参数里加入模糊搜索字段
        model.addAttribute("searchKey",paramMap.get("searchTitle"));
        //如果按照搜索条件查出列表为空，则进行推荐列表前五条数据；
        if(result.get("content") == null || ((ArrayList)result.get("content")).size() == 0){
            paramMap.remove("searchTitle");
            paramMap.put("pageSize","5");
            result =  HttpUtil.connectApiString(Constants.GET_NEWS_LIST,paramMap);
            model.addAttribute("isRecommend",true);
        }else {
            model.addAttribute("isRecommend",false);
        }
        HashMap<String, Object> adMap = HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("systemDate", systemDate).add("type", "11").get(), true);
        LinkedTreeMap<String, Object> adMapContent = (LinkedTreeMap<String, Object>) adMap.get("content");
        model.addAllAttributes(adMapContent);
        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "news", model);
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }
    /**
     * 资讯详情页
     */
    public String getNewsDetail(String id, Model model, String sessionId){
        model.addAttribute("id",id);

        LinkedTreeMap<String,Object> ltm = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_NEWS_DETAIL,
                new Params("newsId",id).add("sessionId", sessionId).get(), model);

        if(ltm == null) NoDataException.throwException();
        String redirectUrl = ltm.get("redirectUrl").toString();
        if(!StringUtils.isEmpty(redirectUrl)) return redirectUrl;
        Gson gson = new Gson();
        NewsDetail newsDetail = gson.fromJson(gson.toJson(ltm), NewsDetail.class);
        if (!StringUtils.isEmpty(newsDetail.getContent()) && newsDetail.getContent().contains("rtmp://")){
            model.addAttribute("videoPath", newsDetail.getContent().replaceAll("<p>","").replaceAll("</p>", ""));
        }else{
            newsDetail.setContent(newsDetail.getContent().replaceAll("&lt;", "<")
                    .replaceAll("&quot;", "\"").replaceAll("&gt;", ">").replaceAll("&amp;nbsp;", ""));
            model.addAttribute("videoPath", null);
        }
        model.addAttribute("newsinfo",newsDetail);
        model.addAttribute("newsltm",ltm);
        model.addAttribute("projectInfo", ltm.get("projectInfo"));

        //相关阅读
        model.addAttribute("linkLIst",ltm.get("reletedInfo"));
        model.addAttribute("pushNews",HttpUtil.connectApi(Constants.GET_PUSH_NEWS,new Params("newsId", id).get()).get("content"));
        model.addAttribute("audio",HttpUtil.connectApi(Constants.GET_RANDOM_AUDIO,new Params().get()).get("content"));
        HashMap<String, Object> adMap = HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type", "12").get(), true);
        LinkedTreeMap<String, Object> adMapContent = (LinkedTreeMap<String, Object>) adMap.get("content");
        model.addAllAttributes(adMapContent);
        return null;
    }


    /**
     * 视频列表（新）
     * @param baseUrl
     * @param params
     * @param model
     */
    public boolean getNewVideos(String baseUrl, String params, Model model){
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.NEW_VIDEO_LIST,model,baseUrl,params);

        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.NEW_VIDEO_LIST);
        paramMap.put("pageSize", "16");
        if(!StringUtils.isEmpty(paramMap.get("videoFeature"))){
            Integer videoFeature = Integer.parseInt(paramMap.get("videoFeature").toString());
            if(videoFeature > 7){
                return false;
            }
        }
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.NEW_VIDEO_LIST,paramMap);

        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "video", model);
        new MyPageable(paramMap, 16).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        return true;
    }


    /**
     * 视频列表首页（新）
     * @param baseUrl
     * @param params
     * @param model
     */
    public void getNewVideoIndex(String baseUrl, String params, Model model) {
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.NEW_VIDEO_LIST, model, baseUrl, params);

        //视频列表
        List<LinkedTreeMap<String, Object>> videosList = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.NEW_VIDEO_LIST_FIRST, new Params("pageSize", 5).get()).get("content");
        model.addAttribute("videosList", videosList);
        HashMap<String, Object> adMap = HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type", "13").get(), true);
        LinkedTreeMap<String, Object> adMapContent = (LinkedTreeMap<String, Object>) adMap.get("content");
        model.addAllAttributes(adMapContent);
    }

    /**
     * 视频详情
     */
    public void getVideoDetail(String videoId,String projectId, Model model)  {
        //视频详情
        LinkedTreeMap<String, Object> ltm = (LinkedTreeMap<String, Object>)HttpUtil.connectApi(Constants.GET_VIDEO_DETAIL,
                new Params("videoId",videoId).add("projectId",projectId).get(), model);
        if(ltm == null) NoDataException.throwException();
        model.addAttribute("video",ltm);
        model.addAttribute("videoId",videoId);
        //右侧推荐视频
        model.addAttribute("videoWith",ltm.get("relative"));
        //下方视频列表
        HashMap underVideo = new HashMap();
        underVideo.put("pageSize",4);
        underVideo.put("videoType",3);
        HashMap<String, Object> videoMap3 = HttpUtil.connectApi(Constants.GET_VIDEO_LIST, underVideo);
        List<LinkedTreeMap<String, Object>> videoList3 = (List<LinkedTreeMap<String, Object>>)videoMap3.get("content");
        model.addAttribute("video3",videoList3);
        underVideo.put("videoType",2);
        HashMap<String, Object> videoMap2 = HttpUtil.connectApi(Constants.GET_VIDEO_LIST, underVideo);
        List<LinkedTreeMap<String, Object>> videoList2 = (List<LinkedTreeMap<String, Object>>)videoMap2.get("content");
        model.addAttribute("video2",videoList2);
        underVideo.put("videoType",5);
        HashMap<String, Object> videoMap5 = HttpUtil.connectApi(Constants.GET_VIDEO_LIST, underVideo);
        List<LinkedTreeMap<String, Object>> videoList5 = (List<LinkedTreeMap<String, Object>>)videoMap5.get("content");
        model.addAttribute("video5",videoList5);
        underVideo.put("videoType",1);
        HashMap<String, Object> videoMap1 = HttpUtil.connectApi(Constants.GET_VIDEO_LIST, underVideo);
        List<LinkedTreeMap<String, Object>> videoList1 = (List<LinkedTreeMap<String, Object>>)videoMap1.get("content");
        model.addAttribute("video1",videoList1);
        underVideo.put("videoType",7);
        HttpUtil.connectApi(Constants.GET_VIDEO_LIST,underVideo,model,"video7");
        if(ltm.get("ifAgent") == null || !"1".equals(ltm.get("ifAgent"))){
            underVideo.put("videoType",10);
            HttpUtil.connectApi(Constants.GET_VIDEO_LIST,underVideo,model,"video10");
        }else {
            model.addAttribute("video10","");
        }
        model.addAttribute("houseId",videoId);
    }

    /**
     * 视频详情
     */
    public void getNewVideoDetail(String videoId,String projectId, String sessionId, Model model)  {
        //视频详情
        LinkedTreeMap<String, Object> ltm = (LinkedTreeMap<String, Object>)HttpUtil.connectApi(Constants.NEW_VIDEO_DETAIL,
                new Params("videoId",videoId).add("project",projectId).add("syTab", "1").add("sessionId", sessionId).get(), model);
        if(ltm == null) NoDataException.throwException();
        model.addAttribute("video",ltm);
        model.addAttribute("videoId",videoId);
        //右侧推荐视频
        model.addAttribute("videoWith",ltm.get("relative"));
        //下方视频列表
        HashMap underVideo = new HashMap();
        underVideo.put("pageSize",4);
        underVideo.put("videoId",videoId);
        HashMap<String, Object> videoMap = HttpUtil.connectApi(Constants.NEW_VIDEO_LIST_FIRST, underVideo);
        List<LinkedTreeMap<String, Object>> videoMaps = (List<LinkedTreeMap<String, Object>>)videoMap.get("content");
        model.addAttribute("videoMaps",videoMaps);
        model.addAttribute("houseId",videoId);
    }




    /**
     * 小二说房列表
     */
    public void getAudiosList(String baseUrl, String params, Model model){
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.GET_AUDIOS_LIST,model,baseUrl,params);
        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_AUDIOS_LIST);
        paramMap.put("pageSize", "20");
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_AUDIOS_LIST,paramMap);
        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "audios", model);
        new MyPageable(paramMap, 20).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }


    /**
     * 经纪人宣传进行时资讯列表页
     */
    public void getAgentNews(String baseUrl, String params, Model model,HttpServletRequest request){
        //楼盘视频
        List<LinkedTreeMap<String, Object>> ltmVideo = (List<LinkedTreeMap<String, Object>>)HttpUtil.connectApi(Constants.GET_VIDEO_LIST,new Params("pageSize",1).get()).get("content");
        model.addAttribute("video",ltmVideo);

        //房产快讯
        HashMap<String, Object> hm2 = HttpUtil.connectApi(Constants.GET_FLASH_LIST, true);
        List<LinkedTreeMap<String, Object>> ltmFlash  = (List<LinkedTreeMap<String, Object>>)hm2.get("content");
        model.addAttribute("flash",ltmFlash);

        //广告
        model.addAttribute("advert1",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 35).get(), true).get("content"));
        model.addAttribute("advert2",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 36).get(), true).get("content"));

        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
//        ParamsUtil.addFilterIntoModel(Constants.GET_AGENT_NEWS_LIST,model,baseUrl,params);

        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_AGENT_NEWS_LIST);
        //放入categoryId信息
//        paramMap.put("categoryId",params);
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_AGENT_NEWS_LIST, paramMap);
        //参数里加入模糊搜索字段
//        model.addAttribute("searchKey",paramMap.get("searchTitle"));
//        model.addAttribute("categoryId",params);
        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "news", model);
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }
    /**
     * 经纪人资讯详情页
     */
    public String getAgentNewsDetail(String id, Model model){
        model.addAttribute("id",id);
        String url  = Constants.GET_AGENT_NEWS_DETAIL;

        LinkedTreeMap<String,Object> ltm = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(url ,
                new Params("newsID",id).get(), model);

        if(ltm == null) NoDataException.throwException();
//        String redirectUrl = ltm.get("redirectUrl").toString();
//        if(!StringUtils.isEmpty(redirectUrl)) return redirectUrl;
        Gson gson = new Gson();
        AgentNewsDetail newsDetail = gson.fromJson(gson.toJson(ltm), AgentNewsDetail.class);
        newsDetail.setContent(newsDetail.getContent().replaceAll("&lt;", "<")
                .replaceAll("&quot;", "\"").replaceAll("&gt;", ">").replaceAll("&amp;nbsp;", ""));

        model.addAttribute("newsinfo",newsDetail);
        model.addAttribute("newsltm",ltm);

        //相关阅读
        model.addAttribute("linkLIst",ltm.get("reletedInfo"));

        //楼盘视频
        List<LinkedTreeMap<String, Object>> ltmVideo = (List<LinkedTreeMap<String, Object>>)HttpUtil.connectApi(Constants.GET_VIDEO_LIST,new Params("pageSize",1).get()).get("content");
        model.addAttribute("video",ltmVideo);

        //房产快讯
        HashMap<String, Object> hm2 = HttpUtil.connectApi(Constants.GET_FLASH_LIST, true);
        List<LinkedTreeMap<String, Object>> ltmFlash  = (List<LinkedTreeMap<String, Object>>)hm2.get("content");
        model.addAttribute("flash",ltmFlash);
        //广告
        model.addAttribute("advert1",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 37).get(), true).get("content"));
        model.addAttribute("advert2",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 38).get(), true).get("content"));
        return null;
    }

	/**
	 * 视频搜索列表
	 * @param baseUrl
	 * @param params
	 * @param model
	 * <AUTHOR> 2018-11-22
	 */
	public void getSearchVideosList(String baseUrl, String params, Model model){
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        HashMap<String, String> searchParams = ParamsUtil.analysisInput(params,Constants.GET_VIDEO_LIST);
        ParamsUtil.addFilterIntoModel(Constants.GET_VIDEO_LIST, model, baseUrl, params, searchParams);
        //右侧最新视频
        List<LinkedTreeMap<String, Object>> ltmVideo = (List<LinkedTreeMap<String, Object>>)HttpUtil.connectApi(Constants.GET_VIDEO_LIST,new Params("pageSize",1).get()).get("content");
        model.addAttribute("video",ltmVideo);
        //搜索视频列表
        searchParams.put("pageSize","12");
        HashMap<String, Object> videolist = HttpUtil.connectApiString(Constants.GET_VIDEO_LIST,searchParams);
        HttpUtil.handleServiceList(videolist, "msg", "videoList", model);
        //添加分页
        new MyPageable(searchParams, Integer.valueOf(searchParams.get("pageSize").toString())).putAttribute(model, baseUrl + params, Integer.valueOf((String) videolist.get("msg")));
        //推荐视频
        HashMap recommentResult = null;
        if(Integer.valueOf((String) videolist.get("msg")).equals(0)){
            recommentResult = HttpUtil.connectApi(Constants.GET_VIDEO_LIST,new Params("pageSize",12).get());
        }
        if(recommentResult != null && Double.valueOf((double) recommentResult.get("status")).intValue() ==1){
            model.addAttribute("recommendVideos",recommentResult.get("content"));
        }else {
            model.addAttribute("recommendVideos","");
        }
        HashMap<String, Object> adMap = HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type", "13").get(), true);
        LinkedTreeMap<String, Object> adMapContent = (LinkedTreeMap<String, Object>) adMap.get("content");
        model.addAllAttributes(adMapContent);
        model.addAttribute("searchKey",searchParams.get("projectName"));
    }

    /**
     * 处理视频搜索列表
     * @param baseUrl
     * @param params
     * @param model
     */
    public void getNewSearchVideosList(String baseUrl, String params, Model model){
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        HashMap<String, String> searchParams = ParamsUtil.analysisInput(params,Constants.NEW_VIDEO_LIST);
        ParamsUtil.addFilterIntoModel(Constants.NEW_VIDEO_LIST, model, baseUrl, params, searchParams);
        //右侧最新视频
        List<LinkedTreeMap<String, Object>> ltmVideo = (List<LinkedTreeMap<String, Object>>)HttpUtil.connectApi(Constants.NEW_VIDEO_LIST, new Params("pageSize",1).get()).get("content");
        model.addAttribute("video",ltmVideo);
        //搜索视频列表
        searchParams.put("pageSize","15");
        HashMap<String, Object> videolist = HttpUtil.connectApiString(Constants.NEW_VIDEO_LIST,searchParams);
        HttpUtil.handleServiceList(videolist, "msg", "videoList", model);
        //添加分页
        new MyPageable(searchParams, Integer.valueOf(searchParams.get("pageSize").toString())).putAttribute(model, baseUrl + params, Integer.valueOf((String) videolist.get("msg")));
        //推荐视频
        HashMap recommentResult = null;
        if(Integer.valueOf((String) videolist.get("msg")).equals(0)){
            recommentResult = HttpUtil.connectApi(Constants.NEW_VIDEO_LIST,new Params("pageSize",15).get());
        }
        if(recommentResult != null && Double.valueOf((double) recommentResult.get("status")).intValue() ==1){
            model.addAttribute("recommendVideos",recommentResult.get("content"));
        }else {
            model.addAttribute("recommendVideos","");
        }
        //房产快讯
        HashMap<String, Object> adMap = HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type", "13").get(), true);
        LinkedTreeMap<String, Object> adMapContent = (LinkedTreeMap<String, Object>) adMap.get("content");
        model.addAllAttributes(adMapContent);
        model.addAttribute("searchKey",searchParams.get("projectName"));
    }




	/**
	 * 视频列表搜索
	 * @param hashMap
	 * @return
	 * <AUTHOR> 2018-11-22
	 */
	@Deprecated
	public HashMap<String, Object> getVideosSearchResultList(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.GET_VIDEO_LIST,hashMap);
    }

    public HashMap<String, Object> getNewVideosSearchResultList(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.NEW_VIDEO_LIST,hashMap);
    }

    /**
     * 添加小二说房数量
     * @param hashMap
     * @return
     * <AUTHOR> 2018-11-28
     */
    public HashMap<String, Object> addAudioNum(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.AUDIO_NUMPLUS,hashMap);
    }
    /**
     * 关于收藏操作
     * @param hashMap
     * @return
     * <AUTHOR> 2019-1-4
     */
    public HashMap<String, Object> manageMyCollection(HashMap hashMap) {
        if(hashMap.get("methodName")!=null && !"".equals(hashMap.get("methodName"))){
            if("checkFavorite".equals(hashMap.get("methodName"))){
                return HttpUtil.connectApi(Constants.COLLECTION_CHECK,hashMap);
            }else if("newFavorite".equals(hashMap.get("methodName"))){
                return HttpUtil.connectApi(Constants.COLLECTION_ADD,hashMap);
            }else if("cancelFavorite".equals(hashMap.get("methodName"))){
                return HttpUtil.connectApi(Constants.COLLECTION_CANCEL,hashMap);
            }
        }
        return new Params().add("status","0").add("msg","系统异常").get();
    }

    public HashMap<String, Object> newsCommentLike(String sessionId, Integer newsId, Integer newsType){
        return HttpUtil.connectApi(Constants.NEWS_COMMENT_LIKE, new Params("newsId", newsId).add("newsType", newsType).add("sessionId", sessionId).get());
    }

    public HashMap<String, Object> addNewsComment(String sessionId, Integer newsId, Integer newsType, String commentDesc){
        return HttpUtil.connectApi(Constants.ADD_NEWS_COMMENT, new Params("sessionId", sessionId).add("newsId", newsId)
                .add("newsType", newsType).add("commentDesc",commentDesc).get());
    }

    public HashMap<String, Object> addNewsReply(String sessionId, String replyDesc, Integer commentId,Integer replyId){
        return HttpUtil.connectApi(Constants.ADD_NEWS_REPLY, new Params("sessionId", sessionId).add("commentId", commentId).add("replyId", replyId).add("replyDesc",replyDesc).get());
    }

    public HashMap<String, Object> addNewsCommentLike(String sessionId, Integer commentId){
        return HttpUtil.connectApi(Constants.ADD_NEWS_COMMENT_LIKE, new Params("sessionId", sessionId).add("commentId", commentId).get());
    }

    public HashMap<String,Object> viewContrastProject(Integer projectId){
        return HttpUtil.connectApi(Constants.VIEW_PROJECT_CONTRAST, new Params("projectId",projectId).get());
    }

    public HashMap<String,Object> viewContrastProjects(Integer projectId){
        return HttpUtil.connectApi(Constants.VIEW_PROJECT_CONTRASTS, new Params("projectId",projectId).get());
    }

    /**
     * 楼盘销售动态
     *
     * <AUTHOR> power
     * @date 2022-03-25 16:22
     * @param baseUrl   拼接条件地址url
     * @param params    搜索参数
     * @param model     Model对象
     */
    public void newProjectDynamics(String baseUrl, String params, Model model) {
        // 获取筛选条件
        HashMap paramMap = ParamsUtil.analysisInput(params, Constants.NEW_PROJECT_DYNAMIC_LIST);
        ParamsUtil.addFilterIntoModel(Constants.NEW_PROJECT_DYNAMIC_LIST, model, baseUrl, params, paramMap);
        // 请求接口 获取列表数据
        paramMap.put("pageSize", 20);
        HashMap result = HttpUtil.connectApi(Constants.NEW_PROJECT_DYNAMIC_LIST, paramMap);
        // 分页信息
        HttpUtil.handleServiceList(result, "msg", "dynamicList", model);
        new MyPageable(paramMap, 20).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        // 小二精选楼盘
        HttpUtil.connectApi(Constants.GET_RANK_LIST_BYTYPE, new Params("rankType", 1).add("page", "1").add("pageSize", "10").get(), model, "rankList");
        // 房产快讯
        model.addAttribute("flash",HttpUtil.connectApi(Constants.GET_NEWS_LIST, true).get("content"));
    }

    /**
     * 直播列表
     * @param weizanSearch
     * @return
     */
    public HashMap<String, Object> viewWeizanList(WeizanSearch weizanSearch){
        return HttpUtil.connectApi(Constants.WEIZAN_LIVE_LIST, Utils.transBean2Map(weizanSearch));
    }

    /**
     * 微赞详情
     * @param id
     * @param model
     */
    public void viewWeizanDetail(String id, Model model){
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.WEIZAN_LIVE_LIST, new Params("id", id).get());
        HttpUtil.handleServiceList(result, "msg", "weizan", model);
    }
}


