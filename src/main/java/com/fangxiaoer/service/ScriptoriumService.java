package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.NameIdBean;
import com.fangxiaoer.model.UrlBean;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Administrator on 2018/1/4.
 */
@Service
public class ScriptoriumService {
    @Autowired
    SecondHouseService secondHouseService;
    /**
     * 获取写字楼列表
     * @param baseUrl
     * @param params
     * @param model
     */
    public void getOffices (String baseUrl, String params, Model model, HttpServletRequest request){
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params,Constants.OFFICE_LIST);
        ParamsUtil.addFilterIntoModel(Constants.OFFICE_LIST, model, baseUrl, params,paramMap);
        ParamsUtil.addSeoTitle(model, "");
        this.setTypeId(paramMap,model);//传出租出售类型控制售价和租金显示
        this.minAndMax(paramMap,model);//将所有最大最小参数房仔model中
        this.setShowWay(paramMap,model);//控制页面显示的房源数量
        this.order(paramMap,model);//排序控制 params,paramMap,new Params().add("shortKey","u").add("searchKey","houseTrait").get()
        this.addHouseTrait(params,paramMap,new Params().add("shortKey","x").add("searchKey","houseTrait").get());
        //查询竞价
        paramMap.put("auction","1");//查询竞价
        paramMap.put("pageSize", "60");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.OFFICE_LIST,paramMap);
        //商铺特色添加
        ArrayList<NameIdBean> nameIdBeen = ParamsUtil.getFilters(Constants.OFFICE_TRAITS);
        ArrayList<UrlBean> urlBeen = this.setSelect(paramMap, nameIdBeen, "x", baseUrl,"houseTrait");
        model.addAttribute("houseTrait", urlBeen);

        HttpUtil.handleServiceList(result, "msg", "offices", model);
        new MyPageable(paramMap,Integer.parseInt(paramMap.get("pageSize"))).putAttribute(model, baseUrl + params , null == result.get("msg")?0: Integer.valueOf((String) result.get("msg")));
        //小区专家
        HashMap plotParam = new HashMap();
        if(!StringUtils.isEmpty(paramMap.get("title"))){
            plotParam.put("title",paramMap.get("title"));
        }
        if(paramMap.get("regionId") != null){
            plotParam.put("regionId",paramMap.get("regionId"));
        }
        plotParam.put("type",2); //  type 1 商铺  2  写字楼
        HashMap<String, Object> experts = HttpUtil.connectApi(Constants.QUERY_SHOP_EXPERT,plotParam);
        List<LinkedTreeMap<String, Object>> plotExpertList = (List<LinkedTreeMap<String, Object>>)experts.get("content");
        if(null != plotExpertList){
            for(LinkedTreeMap<String, Object> object : plotExpertList){
                Double memberid = (Double)object.get("memberId");
                if(null != memberid){
                    object.put("_memberId",memberid.toString().replaceAll("\\.0",""));
                }
            }
        }
        model.addAttribute("plotId","");
        model.addAttribute("agentStoreType",5);
        model.addAttribute("plotExpertList",plotExpertList);
    }
    /**
     * 为商铺赋供求关系
     */
    public void setTypeId(HashMap<String, String> paramMap,Model model){
        String typeId = paramMap.get("typeId");
        if(typeId != null){
            model.addAttribute("typeId",typeId.toString());
        }
    }
    /**
     * 为商铺赋供求关系
     */
    public void minAndMax(HashMap<String, String> paramMap,Model model){
        model.addAttribute("searchKey",paramMap.get("title"));
        model.addAttribute("sminPrice",paramMap.get("sminPrice"));
        model.addAttribute("smaxPrice",paramMap.get("smaxPrice"));
        model.addAttribute("rminPrice",paramMap.get("rminPrice"));
        model.addAttribute("rmaxPrice",paramMap.get("rmaxPrice"));
        model.addAttribute("uminPrice",paramMap.get("uminPrice"));
        model.addAttribute("umaxPrice",paramMap.get("umaxPrice"));
        model.addAttribute("ominArea",paramMap.get("ominArea"));
        model.addAttribute("omaxArea",paramMap.get("omaxArea"));
    }
    public void setShowWay(HashMap<String, String> paramMap,Model model){
        String showWay = paramMap.get("showWay");
        if(showWay != null && showWay.equals("2")){
            paramMap.put("pageSize","40");
            model.addAttribute("sw",showWay);
        }else {
            paramMap.put("pageSize","30");
        }
    }
    /**
     * shoplist页面排序
     */
    public void order(HashMap<String, String> paramMap ,Model model){
        //orderkey 是页面排序选项
        String orderKey = paramMap.get("orderKey");
        if(orderKey != null){
            model.addAttribute("orderKey",orderKey);
        }else {
            model.addAttribute("orderKey","0");
        }
    }
    /**
     * 此方法用于处理商业特色多选匹配
     * @param params
     * @param paramMap
     */
    public void addHouseTrait(String params,HashMap paramMap,HashMap searchMap){
        if(params == null)return;
        if(params.contains(searchMap.get("shortKey").toString())){
            String[] paramsList = params.split("-");
            for (String s :paramsList) {
                if(StringUtils.isEmpty(s))continue;
                if (String.valueOf(s.charAt(0)).equals("x")) {
                    String[] temp = groupParam(s);
                    paramMap.put(searchMap.get("searchKey"),temp[1]);
                }
            }
        }
    }

/*       //页面排序
        this.order(paramMap,model);
        //商铺筛选项显示总价或者租金判定
        this.setTypeId(paramMap,model);
        //商铺展示版面
        this.setShowWay(paramMap,model);
        //商铺特色解析
        this.addHouseTrait(params,paramMap,new Params().add("shortKey","u").add("searchKey","houseTrait").get());

        if(paramMap.get("typeId") == null){
            if(paramMap.get("maxPrice")!=null||paramMap.get("minPrice")!=null){
                paramMap.put("typeId","1");
            }
        }
        // 处理结果

        HashMap result = this.searchShoplist(paramMap,model,"shop",1);
        model.addAttribute("searchKey",paramMap.get("title"));
        model.addAttribute("minPrice",paramMap.get("minPrice") == null ? "":paramMap.get("minPrice").toString());
        model.addAttribute("maxPrice",paramMap.get("maxPrice") == null ? "":paramMap.get("maxPrice").toString());
        model.addAttribute("minArea",paramMap.get("minArea") == null ? "":paramMap.get("minArea").toString());
        model.addAttribute("maxArea",paramMap.get("maxArea") == null ? "":paramMap.get("maxArea").toString());
        //商铺特色添加
        ArrayList<NameIdBean> nameIdBeen = ParamsUtil.getFilters(Constants.OFFICE_LIST);
        ArrayList<UrlBean> urlBeen = this.setSelect(paramMap, nameIdBeen, "u", baseUrl,"houseTrait");
        model.addAttribute("houseTrait", urlBeen);
        //添加分页
        new MyPageable(paramMap, Integer.valueOf(paramMap.get("pageSize").toString())).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
*/

    /**
     * 写字楼详情页
     */
    public void getOfficeDetail(String shopId, Model model) {
        LinkedTreeMap<String, Object> shopdetial = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.OFFICE_DETAIL,
                new Params("shopId",shopId).get(),model);
        if(shopdetial == null) NoDataException.throwException();
        shopdetial.put("ownerPhone1", Utils.decode(shopdetial.get("ownerPhone1").toString()));
        shopdetial.put("ownerPhone",Utils.decode(shopdetial.get("ownerPhone").toString()));
        if (!StringUtils.isEmpty(shopdetial.get("ownerPhone")) && shopdetial.get("ownerPhone").toString().substring(0,3).equals("400")) {
            StringBuffer ownerPhone = new StringBuffer(shopdetial.get("ownerPhone").toString());
            ownerPhone.insert(6,"-").insert(3,"-");
            shopdetial.put("ownerPhone",ownerPhone);
        }
        ArrayList<Map> shopPic = (ArrayList)shopdetial.get("pic");
        if(shopPic == null){
            model.addAttribute("bigImage","https://images.fangxiaoer.com/sy/esf/fy/big/noimage375275.jpg");
            shopdetial.put("pic",null);
        }else {
            for (Map map :shopPic){
                if(map.get("isDefault").toString().equals("1")){
                    model.addAttribute("bigImage",map.get("pic"));
                }
                if(model.containsAttribute("bigImage"))
                    break;
            }
            if(!model.containsAttribute("bigImage")){
                model.addAttribute("bigImage",shopPic.get(0).get("pic"));
            }
        }
        //处理过亿价格
        if(shopdetial.get("price") != null && shopdetial.get("price") != ""){
            BigDecimal bigDecimal = new BigDecimal ((Double) shopdetial.get("price"));
            bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
            shopdetial.put("price",bigDecimal.toString());
        }
        //处理字符串时间
        try{
            if(shopdetial.get("updateTime") != null && shopdetial.get("updateTime") != ""){
                shopdetial.put("updateTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(shopdetial.get("updateTime").toString()));
            }
        }catch (Exception e){

        }
        //推荐商铺同价位
        HashMap<String,String> hashMapPrice = new HashMap();
        hashMapPrice.put("pageSize","6");
        hashMapPrice.put("except",shopId);
        shopdetial.get("shopType");
        Double mimPrice = Double.parseDouble(shopdetial.get("price").toString())*0.9;
        Double maxPrice = Double.parseDouble(shopdetial.get("price").toString())*1.1;
        if(shopdetial.get("shopType").toString().equals("5")) {//出租
            hashMapPrice.put("sminPrice",mimPrice.toString());
            hashMapPrice.put("smaxPrice",maxPrice.toString());
        }else if (shopdetial.get("shopType").toString().equals("4")) {//出售
            hashMapPrice.put("rminPrice",mimPrice.toString());
            hashMapPrice.put("rmaxPrice",maxPrice.toString());
        }
        HashMap relatedHouse = this.searchShoplist(hashMapPrice,model,"recommend",1);
        model.addAttribute("relatedHouse",1);
        if(relatedHouse.get("msg").equals("0")){
            HashMap<String,String> hashMap = new HashMap();
            hashMap.put("regionId",this.fixParams( shopdetial.get("regionId")).toString());
            hashMap.put("pageSize","6");
            hashMap.put("except",shopId);
            HashMap relatedHouse1 = this.searchShoplist(hashMap,model,"recommend",1);
            model.addAttribute("relatedHouse",2);
        }

        model.addAttribute("shop",shopdetial);
        String str = shopdetial.get("mIndustryName").toString().replace(","," | ");
        model.addAttribute("mIndustryName",str);
        model.addAttribute("houseId",shopId);
        //获取经纪人服务标签
        if(!StringUtils.isEmpty(shopdetial.get("agencyId"))){
            String agentId = (String)shopdetial.get("agencyId");
            secondHouseService.getAgentLabel(agentId,model);
        }
        model.addAttribute("houseVideo",shopdetial.get("houseVideo"));//把视频装进去
        //获取商铺视频地址（2019.06.13）
        LinkedTreeMap<String ,Object> con = (LinkedTreeMap<String, Object>) shopdetial.get("houseVideo");
        if(!StringUtils.isEmpty(con.get("mediaID"))){
            String mediaId =(String)con.get("mediaID");
            HashMap<String, Object> path = HttpUtil.connectApi(Constants.VIEW_VIDEO_PATH, new Params("mediaId", mediaId).get());
            LinkedTreeMap<String ,Object> cont = (LinkedTreeMap<String, Object>) path.get("content");
            ArrayList videoPath = (ArrayList<String>)cont.get("videoPath");
            model.addAttribute("videoPath",videoPath.get(0));//视频地址
            model.addAttribute("videoImgPath",videoPath.get(1));//图片地址
        }else{
            model.addAttribute("videoPath","");//无视频地址
            model.addAttribute("videoImgPath","");//无视频首图
        }
        if(shopdetial.get("housePan") != null){
            LinkedTreeMap<String, Object> pan = (LinkedTreeMap<String, Object>) shopdetial.get("housePan");
            model.addAttribute("panUrl_VR",pan.get("panUrl"));
            model.addAttribute("panUrl_VRImg",pan.get("panImageUrl"));
        }
    }
    public String fixParams(Object object){
        if(object == null){
            return "";
        }else {
            return object.toString();
        }
    }
    /**
     * 获取单价
     * @param map
     * @return
     */
    private String getUnitPrice(Map<String, Object> map,Integer pageType){
        String unitPrice = null;
        String type = this.getStringValue(map.get("shopType"));

        if(!StringUtils.isEmpty(type)){
            if(type.equals(CommonEnum.codeFlage.SHOP_TYPE_SALE.getCode()+"")){//出售
                if(map.get("price") != null && !"".equals(map.get("price"))
                        && map.get("area") != null && !"".equals(map.get("area"))) {
                    Double price = Double.valueOf(map.get("price").toString());
                    Double truearea = Double.valueOf(map.get("area").toString());
                    if (truearea.intValue() != 0 && price.intValue() !=0 ) {
                        unitPrice = getPrice(this.getStringValue(this.div((price * 10000), truearea, 0))) + "元/m²";
                    }
                }
            }else if(type.equals(CommonEnum.codeFlage.SHOP_TYPE_RENT.getCode()+"")){//出租
                if(map.get("price") != null && !"".equals(map.get("price"))
                        && map.get("area") != null && !"".equals(map.get("area"))) {
                    Double price = Double.valueOf(map.get("price").toString());
                    Double truearea = Double.valueOf(map.get("area").toString());
                    if (truearea.intValue() != 0 && price.intValue() !=0 ) {
                        if(pageType == 2){
                            if (Integer.valueOf(map.get("payment").toString()) ==50 || Integer.valueOf(map.get("payment").toString()) ==100|| Integer.valueOf(map.get("payment").toString()) ==110) {
                                unitPrice = getPrice(this.getStringValue(this.div((price / 12/30), truearea, 0))) + "元/m²·天";
                            } else {
                                unitPrice = getPrice(this.getStringValue(this.div((price/30), truearea, 0))) + "元/m²·天";
                            }
                        }else {
                            if ( Integer.valueOf(map.get("payment").toString()) ==50 || Integer.valueOf(map.get("payment").toString()) ==100|| Integer.valueOf(map.get("payment").toString()) ==110) {
                                unitPrice = getPrice(this.getStringValue(this.div((price / 12), truearea, 0))) + "元/m²·月";
                            } else {
                                unitPrice = getPrice(this.getStringValue(this.div((price), truearea, 0))) + "元/m²·月";
                            }
                        }

                    }
                }
            }else if(type.equals(CommonEnum.codeFlage.SHOP_TYPE_CHANGE.getCode()+"")){//出兑
                String tranFee = this.getStringValue(map.get("tranFee"));
                unitPrice = "  转让费  " + (tranFee.equals("0") || tranFee.equals("0.0") ? "面议" : getPrice(tranFee) + "万");
            }
        }
        return unitPrice;
    }

    /**
     * 去除小数点后的0
     * @param str
     * @return
     */
    private String getPrice(String str){
        if(str.indexOf(".") > 0){
            //正则表达
            str = str.replaceAll("0+?$", "");//去掉后面无用的零
            str = str.replaceAll("[.]$", "");//如小数点后面全是零则去掉小数点
        }
        return str;
    }
    /**
     * 返回查询结果字符串
     *
     * @param object
     * @return
     */
    public static String getStringValue(Object object) {
        String value = null;
        if (object == null) {
            return "";
        }
        if (object instanceof Integer) {
            value = String.valueOf(((Integer) object).intValue());
        } else if (object instanceof String) {
            value = (String) object;
        } else if (object instanceof Double) {
            value = String.valueOf(((Double) object).doubleValue());
        } else if (object instanceof Float) {
            value = String.valueOf(((Float) object).floatValue());
        } else if (object instanceof Long) {
            value = String.valueOf(((Long) object).longValue());
        } else if (object instanceof Boolean) {
            value = String.valueOf(((Boolean) object).booleanValue());
        } else if (object instanceof String) {
            value = (String) object;
        } else {
            value = object.toString();
        }
        return value.trim();
    }
    /**
     *    进行除法运算
     */
    public static BigDecimal div(double d1, double d2, int len) {// 进行除法运算
        BigDecimal b1 = new BigDecimal(d1);
        BigDecimal b2 = new BigDecimal(d2);
        return b1.divide(b2,len,BigDecimal.ROUND_HALF_UP).setScale(2);

    }


    /**
     * 因为商铺特色多选，专用的匹配规则
     * @param item
     * @return
     * <AUTHOR> 2017-12-6
     */
    public String[] groupParam(String item) {
        if(item.contains("=")) {
            String[] tmp = item.split("=");
            return tmp.length > 1 ? tmp : null;
        }
        Pattern pattern = Pattern.compile("[0-9][,0-9]*$");
        Matcher m = pattern.matcher(item);
        if (m.find()) {
            String num = m.group();
            String key = item.replace(num, "");
            return new String[]{key, num};
        }
        return null;
    }


    /**
     * 通过查询参数查询商铺列表
     * @param paramMap
     * @param model
     */
    public  HashMap<String, Object>  searchShoplist( HashMap<String, String> paramMap,Model model,String modelKey,Integer type){
        //访问接口查询
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.OFFICE_LIST, paramMap);
        int status = HttpUtil.handleServiceList(result, "msg", "shop", model);
        if(status == 1 && type == 1){
            List<LinkedTreeMap<String, Object>> list = (List<LinkedTreeMap<String, Object>>) result.get("content");
            if (list.size() == 0) {
                model.addAttribute(modelKey, null);
            } else {
                //添加商铺的均价
                for(LinkedTreeMap map : list){
                    map.put("unitPrice",this.getUnitPrice(map,type)) ;
                    //单价处理过亿价格
                    if(map.get("price") != null && map.get("price") != ""){
                        BigDecimal bigDecimal = new BigDecimal ((Double) map.get("price"));
                        bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
                        map.put("price",bigDecimal.toString());
                    }
                }
                model.addAttribute(modelKey, list);
            }
        }
        return  result;
    }

    /**
     * 计算商业出租出兑价格，如果过万则加万字
     * @param price
     * @return
     */
    public String calculatePrice(String price){
        Pattern pattern = Pattern.compile("[0]{4}[.][0]{2}$");
        Matcher m = pattern.matcher(price);
        if (m.find()) {
            String temp = m.group();
            price = price.replace(temp," 万");
        }
        return price;
    }
    /**
     * 处理商铺特色
     * @param params
     * @param opinions
     * @param startKey
     * @param head
     * @return
     */
    public ArrayList<UrlBean> setSelect(HashMap params, ArrayList<NameIdBean> opinions, String startKey, String head,String searchKey) {
        ArrayList<UrlBean> list = new ArrayList<>();
        // 默认设置"全部"按钮为已选择状态
        UrlBean headerBean = null;
        for (NameIdBean item : opinions) {
            String url = "";
            UrlBean urlBean = new UrlBean();
            urlBean.setName(item.getName());
            urlBean.setId(item.getId());
            if(StringUtils.isEmpty(item.getId())) {
                headerBean = urlBean;
                headerBean.setSelected(true);
            }
            String currentNode = item.getId();
            if(params.get(searchKey) != null){
                String  houseTrait = params.get(searchKey).toString();
                String[]  houseTraits = houseTrait.split(",");
                for (String param : houseTraits){
                    if (StringUtils.isEmpty(param)) continue;
                    if(currentNode.equals(param)) {
                        urlBean.setSelected(true);
                        if(headerBean != null) {
                            headerBean.setSelected(false);
                        }
                    }
                }
            }
            url = head + (StringUtils.isEmpty(item.getId()) ? "" : startKey) + item.getId() + url;
            urlBean.setUrl(url);
            list.add(urlBean);
        }
        return list;
    }

    /**
     * 获取写字楼项目列表
     * @param baseUrl
     * @param params
     * @param model
     */
    public void getOfficeProjects (String baseUrl, String params, Model model, HttpServletRequest request){
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params,Constants.OFFICE_PROJECT_LIST);
        ParamsUtil.addFilterIntoModel(Constants.OFFICE_PROJECT_LIST, model, baseUrl, params,paramMap);
        ParamsUtil.addSeoTitle(model, "");
        this.minAndMaxProject(paramMap,model);//将所有最大最小参数房仔model中
        this.addHouseTrait(params,paramMap,new Params().add("shortKey","x").add("searchKey","houseTrait").get());
        paramMap.put("pageSize","30");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.OFFICE_PROJECT_LIST,paramMap);
        //商铺特色添加
        ArrayList<NameIdBean> nameIdBeen = ParamsUtil.getFilters(Constants.OFFICE_TRAITS);
        ArrayList<UrlBean> urlBeen = this.setSelect(paramMap, nameIdBeen, "x", baseUrl,"houseTrait");
        model.addAttribute("houseTrait", urlBeen);
        if (result.get("content") != null)this.dealSalePrice((ArrayList) result.get("content"));
        HttpUtil.handleServiceList(result, "msg", "offices", model);
        new MyPageable(paramMap,30).putAttribute(model, baseUrl + params , Integer.valueOf((String) result.get("msg")));
    }
    /**
     * 为写字楼项目自定义筛选填写
     */
    public void minAndMaxProject(HashMap<String, String> paramMap,Model model){
        model.addAttribute("searchKey",paramMap.get("officeName"));
        model.addAttribute("sminPrice",paramMap.get("minSaleUnitPrice"));
        model.addAttribute("smaxPrice",paramMap.get("maxSaleUnitPrice"));
        model.addAttribute("uminPrice",paramMap.get("minRentUnitPrice"));
        model.addAttribute("umaxPrice",paramMap.get("maxRentUnitPrice"));
        model.addAttribute("ominArea",paramMap.get("minArea"));
        model.addAttribute("omaxArea",paramMap.get("maxArea"));
    }
    public void dealSalePrice(ArrayList result){
        for (int i = 0 ; i < result.size() ; i++) {
            LinkedTreeMap map = (LinkedTreeMap) result.get(i);
            if (map.get("unitPriceSale") != null){
                double salePrice = (double) map.get("unitPriceSale");
                if (salePrice >= 10000) {
                    salePrice = salePrice/10000;
                    map.put("saleUnit","万元/㎡");
                }else {
                    map.put("saleUnit","元/㎡");
                }
                map.put("salePrice",salePrice);
            }
        }
    }
    /**
     * 写字楼详情页
     */
    public void getOfficeProject(String officeId, Model model) {
        LinkedTreeMap<String, Object> officeProject = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.OFFICE_PROJECT_DRTAIL,
                new Params("officeId",officeId).get(),model);
        if(officeProject == null) NoDataException.throwException();
        /*计算租售房源总数量*/
        int allCount = 0;
        if (officeProject.get("saleCount") != null) {
            allCount = allCount + Integer.parseInt(officeProject.get("saleCount").toString());
        }
        if (officeProject.get("rentCount") != null) {
            allCount = allCount + Integer.parseInt(officeProject.get("rentCount").toString());
        }
        model.addAttribute("allCount",allCount);
        /*出售价格大于等于一万，单位改‘万元’*/
        if (officeProject.get("unitPriceSale") != null){
            double salePrice = (double) officeProject.get("unitPriceSale");
            if (salePrice >= 10000) {
                salePrice = salePrice/10000;
                officeProject.put("saleUnit","万元/㎡");
            }else {
                officeProject.put("saleUnit","元/㎡");
            }
            officeProject.put("salePrice",salePrice);
        }
        model.addAttribute("houseId",officeId);
        model.addAttribute("office",officeProject);
        /*相似房源（出租房源）*/
        HashMap hashMap = new HashMap();
        hashMap.put("officeId",officeId);
        HashMap<String, Object> similar = HttpUtil.connectApiString(Constants.OFFICE_LIST, hashMap);
        List<LinkedTreeMap<String, Object>> content = (List<LinkedTreeMap<String, Object>>)similar.get("content");
        model.addAttribute("similar",content);
        /*附近写字楼*/
        HashMap nearOfficeMap = new HashMap();
        nearOfficeMap.put("officeId",officeId);
        HashMap<String, Object> nearOffice = HttpUtil.connectApiString(Constants.NEAR_OFFICE_PROJECT, nearOfficeMap);
        List<LinkedTreeMap<String, Object>> nearOffices = (List<LinkedTreeMap<String, Object>>)nearOffice.get("content");
        model.addAttribute("nearOffices",nearOffices);
    }


    /**
     * 中介写字楼列表使用（此方法为了区分商铺和写字楼）
     * 通过查询参数查询写字楼列表
     * @param paramMap
     * @param model
     * @Date 2019.09.30
     */
    public  HashMap<String, Object>  searchOfficelist( HashMap<String, String> paramMap,Model model,String modelKey,Integer type){
        //访问接口查询
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.OFFICE_LIST, paramMap);
        int status = HttpUtil.handleServiceList(result, "msg", "officeList", model);
        if(status == 1 && type == 1){
            List<LinkedTreeMap<String, Object>> list = (List<LinkedTreeMap<String, Object>>) result.get("content");
            if (list.size() == 0) {
                model.addAttribute(modelKey, null);
            } else {
                //添加商铺的均价
                for(LinkedTreeMap map : list){
                    map.put("unitPrice",this.getUnitPrice(map,type)) ;
                    //单价处理过亿价格
                    if(map.get("price") != null && map.get("price") != ""){
                        BigDecimal bigDecimal = new BigDecimal ((Double) map.get("price"));
                        bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
                        map.put("price",bigDecimal.toString());
                    }
                }
                model.addAttribute(modelKey, list);
            }
        }
        return  result;
    }

    /**
     * 中介店铺首页 推荐写字楼房源（此方法专为推荐写字楼使用）
     * @param paramMap
     * @param model
     * @param modelKey
     * @param type
     * @return
     */
    public  HashMap<String, Object>  recommendOfficelist( HashMap<String, String> paramMap,Model model,String modelKey,Integer type){
        //访问接口查询
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.AGENT_RECOMMEND_OFFICE_INFO, paramMap);
        int status = HttpUtil.handleServiceList(result, "msg", "officeList", model);
        if(status == 1 && type == 1){
            List<LinkedTreeMap<String, Object>> list = (List<LinkedTreeMap<String, Object>>) result.get("content");
            if (list.size() == 0) {
                model.addAttribute(modelKey, null);
            } else {
                //添加商铺的均价
                for(LinkedTreeMap map : list){
                    map.put("unitPrice",this.getUnitPrice(map,type)) ;
                    //单价处理过亿价格
                    if(map.get("price") != null && map.get("price") != ""){
                        BigDecimal bigDecimal = new BigDecimal ((Double) map.get("price"));
                        bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
                        map.put("price",bigDecimal.toString());
                    }
                }
                model.addAttribute(modelKey, list);
            }
        }
        return  result;
    }
}
