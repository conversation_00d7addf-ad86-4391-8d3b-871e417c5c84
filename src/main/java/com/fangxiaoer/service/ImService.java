package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2018/8/16
 */
@Service
public class ImService {
    //添加好友
    public void addFriend(String sessionId, String mobile) {
        HashMap map = new HashMap();
        map.put("sessionId",sessionId);
        map.put("mobile",mobile);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.IM_ADD_FRIEND, map);
    }

    //发送消息
    public void sendMsg(String sessionId, String mobile, String houseId, String houseType) {
        HashMap map = new HashMap();
        map.put("sessionId",sessionId);
        map.put("mobile",mobile);
        map.put("houseId",houseId);
        map.put("houseType",houseType);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.IM_SEND_MSG, map);
    }

    //查询目标联系人id
    public Object getAccid(String mobile) {
        HashMap map = new HashMap();
        map.put("mobile",mobile);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.IM_GET_ACCID, map);
        return result.get("content");
    }

    //查询目标联系人id
    public Object checkAccid(String memberId) {
        HashMap map = new HashMap();
        map.put("memberId",memberId);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.IM_CHECK_ACCID, map);
        return result.get("content");
    }


    //通过sessionid查询自己的accid
    public Object getMyAccid(String sessionId) {
        HashMap map = new HashMap();
        map.put("sessionId",sessionId);
        map.put("freeLogin","freeLogin");//默认可以游客登陆 -- 当sessionId存在时无实际意义
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.IM_MY_ACCID, map);
        return result.get("content");
    }

    /**
     * 新房--小二客服 新房im通信
     * @param request
     * @param response
     * @param houseId 房源id
     * @return 成功失败的json对象 若该用手首次沟通房源 包含房源信息
     */
    public HashMap serviceIMNew(HttpServletRequest request, HttpServletResponse response, String houseId){
        HashMap<String, Object> resultMap = new HashMap<>();
        //1.根据sessionId查询账号有效性
        HttpSession session = request.getSession();
        //2.将用户信息写入cookie
        resultMap = checkAccount(session, response, 1);
        if ("0".equals(resultMap.get("state").toString())) return resultMap;
        /*//4.若未沟通过,并且存在房源id -- 查询房源信息
        if (houseId != "" && houseId != null){
            HashMap<String, Object> projectInfo = getImProject(houseId);
            resultMap.put("content", projectInfo);
            resultMap.put("state", 1);
        }*/
        //6.将标识返回给IM    0：失败    1:成功
        return resultMap;
    }

    /**
     * 二手房--小二管家 二手房im通信
     * @param request
     * @param response
     * @return 成功失败的json对象
     */
    public HashMap serviceIMSec(HttpServletRequest request, HttpServletResponse response){
        HashMap<String, Object> resultMap = new HashMap<>();
        //1.根据sessionId查询账号有效性
        HttpSession session = request.getSession();
        //2.将用户信息写入cookie
        resultMap = checkAccount(session, response, 2);
        if ("0".equals(resultMap.get("state").toString())) return resultMap;
        //3.将标识返回给IM    0：失败    1:成功
        return resultMap;
    }

    //查询新房信息
    public HashMap getImProject(String houseId){
        HashMap<String, Object> projects = HttpUtil.connectApi(Constants.VIEW_PROJECT_LIST, new Params("xebProject", houseId).get());
        List<LinkedTreeMap<String, Object>> results = (List<LinkedTreeMap<String, Object>>) projects.get("content");
        LinkedTreeMap<String, Object> result = results.get(0);
        HashMap map = new HashMap();
        map.put("houseId",houseId); //房源id
        map.put("type",1);  //类型
        map.put("regionName",result.get("regionName").toString());  //所在区
        map.put("plateName","");
        map.put("plateId", "");
        //链接地址
        map.put("linkUrl","/house/"+houseId+ "-"+ result.get("type")+".htm");
        map.put("housePic",result.get("pic").toString());   //图片地址
        map.put("subName", result.get("projectName").toString());   //楼盘名
        map.put("hallRoom", "");
        //面积区间
        if(!StringUtils.isEmpty(result.get("area"))){
            List<LinkedTreeMap<String, Object>> areaBeans = (List<LinkedTreeMap<String, Object>>)result.get("area");
            map.put("area", Utils.modifyNum(areaBeans.get(0).get("minArea").toString()) + "㎡~" +Utils.modifyNum(areaBeans.get(0).get("maxArea").toString()) + "㎡");
        }
        map.put("unit", "元/㎡"); //单位
        // 价钱
        if(StringUtils.isEmpty(result.get("mPrice"))){
            map.put("price", "待定");
        }else{
            LinkedTreeMap<String, Object> mPrice = (LinkedTreeMap<String, Object>)result.get("mPrice");
            map.put("price", mPrice.get("priceType") + ":" + Utils.modifyNum(mPrice.get("priceMoney").toString()));
        }
        return map;
    }

    //通用账号验证方法
    public HashMap<String, Object> checkAccount(HttpSession session, HttpServletResponse response, int serviceType) {
        HashMap<String, Object> resultMap = new HashMap<>();
        String sessionId = "";
        if (!StringUtils.isEmpty(session.getAttribute("sessionId"))) sessionId = session.getAttribute("sessionId").toString();
        LinkedTreeMap<String, Object> map = (LinkedTreeMap) getMyAccid(sessionId);
        try {
            if (!StringUtils.isEmpty(map.get("imProvinceState"))) {
                String user_state = map.get("imProvinceState").toString();
                Integer stute = Integer.valueOf(user_state);
                if (stute == 1) {
                    resultMap.put("message", "此功能只针对辽宁、北京等特定地域的手机用户开通！详询致电：400-893-9709");
                    resultMap.put("state", 0);
                    return resultMap;
                }
            }
            if (!StringUtils.isEmpty(map.get("imState"))) {
                String user_state = map.get("imState").toString();
                Integer stute = Integer.valueOf(user_state);
                if (stute == 1) {
                    resultMap.put("message", "您的聊呗账号已禁用，如需开通详询致电：400-893-9709");
                    resultMap.put("state", 0);
                    return resultMap;
                }
            }
            //2.将用户信息写入cookie
            String accid = map.get("accid").toString();
            String token = map.get("token").toString();
            CookieManage.addCookie(response, "uid", accid, "/", "");
            CookieManage.addCookie(response, "sdktoken", token, "/", "");
            //serviceType 1:新房    2:二手房
            if (serviceType == 2){
                resultMap.put("serviceId", "4f55630619e2d3cb2d5cfd257c7552e9");
            } else {
                resultMap.put("serviceId", "d508b774ceedaa3481cc67c3bc5a56c4");
            }
        } catch (Exception e) {
            resultMap.put("message", "聊天认证失败，请您稍后再试。");
            resultMap.put("state", 0);
            return resultMap;
        }
        resultMap.put("state", 1);
        return resultMap;
    }
}
