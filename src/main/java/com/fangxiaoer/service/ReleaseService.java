package com.fangxiaoer.service;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Utils;
import com.fangxiaoer.model.SaleHouseBean;
import com.fangxiaoer.model.SaleHouseEntity;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2017/6/21.
 */
@Service
public class ReleaseService {

    public void getFreeRelease(Model model){
        HashMap<String, Object> zxMap = HttpUtil.connectApi(Constants.FILTER_HOUSE_DECORATION);
        List<LinkedTreeMap<String,Object>> zxList = (List<LinkedTreeMap<String,Object>>)zxMap.get("content");
        model.addAttribute("zx",zxList);
        HashMap<String, Object> cxMap  = HttpUtil.connectApi(Constants.FILTER_HOUSE_FORWARD);
        List<LinkedTreeMap<String,Object>> cxList = (List<LinkedTreeMap<String,Object>>)cxMap.get("content");
        model.addAttribute("cx",cxList);
        HashMap<String, Object> fwlbMap  = HttpUtil.connectApi(Constants.GET_SCONDHOUSE_TYPE);
        List<LinkedTreeMap<String,Object>> fwlbList = (List<LinkedTreeMap<String,Object>>)fwlbMap.get("content");
        model.addAttribute("fwlb",fwlbList);
        HashMap<String, Object> lxMap = HttpUtil.connectApi(Constants.GET_HOUSE_TYPE);
        List<LinkedTreeMap<String, Object>> lxList = (List<LinkedTreeMap<String, Object>>)lxMap.get("content");
        model.addAttribute("lx",lxList);
        HashMap<String, Object> cqMap = HttpUtil.connectApi(Constants.GET_HOUSE_PROPERTY);
        List<LinkedTreeMap<String, Object>> cqList = (List<LinkedTreeMap<String, Object>>)cqMap.get("content");
        model.addAttribute("cq",cqList);
        HashMap<String, Object> tsMap = HttpUtil.connectApi(Constants.GET_HOUSE_FEATURE);
        List<LinkedTreeMap<String, Object>> tsList = (List<LinkedTreeMap<String, Object>>)tsMap.get("content");
        model.addAttribute("ts",tsList);

        SaleHouseBean saleHouseBean = new SaleHouseBean();
        model.addAttribute("data", saleHouseBean);
    }

    /**
     * 发布二手房
     * @param she
     * @param model
     */
    public Integer addSecondHouse(String sessionId,SaleHouseEntity she, Model model, HttpSession session){
        HashMap<String, Object> result = Utils.transBean2Map(she);
        result.put("sessionId", sessionId);
        HashMap<String, Object> values = HttpUtil.connectApi(Constants.ADD_SALEHOUSE, result);
        return HttpUtil.handleServiceMethod(values, model, session);
    }
}
