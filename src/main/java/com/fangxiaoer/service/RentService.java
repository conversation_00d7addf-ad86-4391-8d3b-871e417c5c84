package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.Rent;
import com.fangxiaoer.model.SecondHouse;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.internal.LinkedHashTreeMap;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * Created by Administrator on 2017/8/25.
 */
@Service
public class RentService {
    @Autowired
    SecondHouseService secondHouseService;

    /**
     * 租房列表页
     */
    public void getRentsHouse(String baseUrl, String params, Model model, String pageSize , HttpServletRequest request){
        secondHouseService.keyValueConfig("rent_anxuan", model, "forAnxuan");
        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_RENTHOUSE_LIST);
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.GET_RENTHOUSE_LIST,model,baseUrl,params,paramMap);
        //设置每页的数量和需要查找第几页的数据
        paramMap.put("pageSize", pageSize);
        //seo
        ParamsUtil.addSeoTitle(model, "orderKey,vgType");
        //感兴趣的房源
        HashMap hashMap = new HashMap();
        hashMap.put("orderKey",2);
        if (paramMap.get("regionId") == null) {

            hashMap.put("regionId","");
        }
        else {
            hashMap.put("regionId",paramMap.get("regionId"));
        }
        HashMap<String, Object> interests = HttpUtil.connectApi(Constants.GET_INSTERT_HOUSE,hashMap);
        List<LinkedTreeMap<String, Object>> interest = (List<LinkedTreeMap<String, Object>>)interests.get("content");
        model.addAttribute("interest",interest);

        //解析“特色”  小区
        String subId = "";
        String [] tese = params.split("-");
        String teses="";
        String seoSubName = "";
        for (int i = 1 ; i < tese.length ; i++) {
            //加一个非空判断
            if (tese[i].substring(0,1).equals("v")){
                subId = tese[i].substring(1);
                model.addAttribute("subId",subId);
                paramMap.put("subId",subId);
                HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL,new Params("subId",subId).get(), model, "sub");

                Map<String, Object> mod = model.asMap();
                if(mod.containsKey("sub")) {
                    seoSubName = (String) ((LinkedTreeMap<String, Object>)mod.get("sub")).get("title");
                    if(seoSubName == null) {
                        seoSubName = "";
                    }
                }
            }
            if (!StringUtils.isEmpty(tese[i]) && tese[i].substring(0,1).equals("t")) {
                teses = teses + tese[i].substring(1, tese[i].length()) + ",";
            }
        }
        paramMap.put("houseTrait",teses);
        model.addAttribute("seoSubName", seoSubName);
        //参数里加入模糊搜索字段
//        CookieManage.addParams("subName",model,request,paramMap);
        model.addAttribute("searchKey",paramMap.get("subName"));
        if (params.indexOf("k1")!=-1) {
            paramMap.put("isSubway","1");
        }
        paramMap.remove("");
        paramMap.remove("projectType");
        paramMap.put("auction","1");
        //如果是视频看房查询则不需要竞价查询
       /* if(!StringUtils.isEmpty(paramMap.get("vgTypeId"))){
            paramMap.put("auction","");
        }*/
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_RENTHOUSE_LIST,paramMap);
        //去掉面积和租金上的.0
        List<LinkedTreeMap<String,Object>> results = (List<LinkedTreeMap<String,Object>>)result.get("content");
        for (int i = 0 ; i<results.size();i++) {
            results.get(i).put("area",Utils.modifyNum(results.get(i).get("area").toString()));
            results.get(i).put("price",Utils.modifyNum(results.get(i).get("price").toString()));
        }
        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "rents", model);
        new MyPageable(paramMap,Integer.parseInt(pageSize)).putAttribute(model, baseUrl + params , Integer.valueOf((String) result.get("msg")));
        //此处增加如果查询数据没有或者有限的情况下，按区域查询数据进行推荐
        List<HashMap<String, Object>> rentList = (List<HashMap<String, Object>>) result.get("content");
        int pageSizes = 0;
        //如果长度是0 需要重新按区域获取房源
        if(rentList.size() >= 0 &&  rentList.size() <= 30){
            HashMap<String, String> paramCount = ParamsUtil.analysisInput(null, Constants.GET_RENTHOUSE_LIST);
            if(paramMap.get("regionId") != null ){
                paramCount.put("regionId",paramMap.get("regionId"));
                pageSizes = 60;
            }else{
                pageSizes = 60;
            }
            paramCount.put("pageSize", ""+pageSizes);
            HashMap<String, Object> re = HttpUtil.connectApiString(Constants.GET_RENTHOUSE_LIST, paramCount);
            List<LinkedTreeMap<String,Object>> recommondResults = (List<LinkedTreeMap<String,Object>>)re.get("content");
            for (int i = 0 ; i<recommondResults.size();i++) {
                recommondResults.get(i).put("area",Utils.modifyNum(recommondResults.get(i).get("area").toString()));
                recommondResults.get(i).put("price",Utils.modifyNum(recommondResults.get(i).get("price").toString()));
            }
            //需要去掉重复的数据内容
            if(rentList.size() >0){
                recommondResults = recommondResults.stream().filter(item -> !results.contains(item)).collect(toList());
            }
            re.put("content",recommondResults);
            HttpUtil.handleServiceList(re, "msg", "recommendRentList", model);
        }
        model.addAttribute("schoolRight",HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type","9").get(),true).get("content"));
        //小区专家
        HashMap plotParam = new HashMap();
        if(!StringUtils.isEmpty(seoSubName)){
            plotParam.put("title",seoSubName);
        }else if(!StringUtils.isEmpty(paramMap.get("subName"))){
            plotParam.put("title",paramMap.get("subName"));
        }
        HashMap<String, Object> experts = HttpUtil.connectApi(Constants.QUERY_PLOT_EXPERT,plotParam);
        List<LinkedTreeMap<String, Object>> plotExpertList = (List<LinkedTreeMap<String, Object>>)experts.get("content");
        if(null != plotExpertList){
            for(LinkedTreeMap<String, Object> object : plotExpertList){
                Double memberid = (Double)object.get("memberId");
                if(null != memberid){
                    object.put("_memberId",memberid.toString().replaceAll("\\.0",""));
                }
            }
            //传小区Id 获取小区相关信息
            HashMap plot = new HashMap();
            if(!StringUtils.isEmpty(experts.get("msg"))){
                plot.put("subId", experts.get("msg"));
                HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL, plot);
                LinkedTreeMap plot_info = (LinkedTreeMap<String, Object>) villageDetail.get("content");
                model.addAttribute("plotInformation",plot_info);
            }
        }
        model.addAttribute("plotId",experts.get("msg"));
        model.addAttribute("agentStoreType",3);
        model.addAttribute("plotExpertList",plotExpertList);
    }


    /**
     * 租房成交列表页
     */
    public void getDealRentsHouse(String baseUrl, String params, Model model, String pageSize , HttpServletRequest request){
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.DEAL_RENT_LIST,model,baseUrl,params);

        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.DEAL_RENT_LIST);

        //设置每页的数量和需要查找第几页的数据
        paramMap.put("pageSize", pageSize);
        //seo
        ParamsUtil.addSeoTitle(model, "orderKey");
        //感兴趣的房源
        HashMap hashMap = new HashMap();
        hashMap.put("orderKey",2);
        if (paramMap.get("regionId") == null) {

            hashMap.put("regionId","");
        }
        else {
            hashMap.put("regionId",paramMap.get("regionId"));
        }
        HashMap<String, Object> interests = HttpUtil.connectApi(Constants.GET_INSTERT_HOUSE,hashMap);
        List<LinkedTreeMap<String, Object>> interest = (List<LinkedTreeMap<String, Object>>)interests.get("content");
        model.addAttribute("interest",interest);

        //解析“特色”  小区
        String subId = "";
        String [] tese = params.split("-");
        String teses="";
        String seoSubName = "";
        for (int i = 1 ; i < tese.length ; i++) {
            //加一个非空判断
            if (tese[i].substring(0,1).equals("v")){
                subId = tese[i].substring(1);
                model.addAttribute("subId",subId);
                paramMap.put("subId",subId);
                HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL,new Params("subId",subId).get(), model, "sub");

                Map<String, Object> mod = model.asMap();
                if(mod.containsKey("sub")) {
                    seoSubName = (String) ((LinkedTreeMap<String, Object>)mod.get("sub")).get("title");
                    if(seoSubName == null) {
                        seoSubName = "";
                    }
                }
            }
            if (!StringUtils.isEmpty(tese[i]) && tese[i].substring(0,1).equals("t")) {
                teses = teses + tese[i].substring(1, tese[i].length()) + ",";
            }
        }
        paramMap.put("houseTrait",teses);
        model.addAttribute("seoSubName", seoSubName);
        //参数里加入模糊搜索字段
//        CookieManage.addParams("subName",model,request,paramMap);
        model.addAttribute("searchKey",paramMap.get("subName"));
        if (params.indexOf("k1")!=-1) {
            paramMap.put("isSubway","1");
        }
        paramMap.remove("");
        paramMap.remove("projectType");
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.DEAL_RENT_LIST,paramMap);
        secondHouseService.dealPriceAndTime(result,2);
        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "rents", model);
        new MyPageable(paramMap,Integer.parseInt(pageSize)).putAttribute(model, baseUrl + params , Integer.valueOf((String) result.get("msg")));
    }
    //租房>小区找房列表页
    public void getVillages(String baseUrl, String params, Model model){

        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.VIEW_NEW_SUBDISTRICT);
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.VIEW_NEW_SUBDISTRICT,model,baseUrl,params,paramMap);
        //根据paramMap 查询接口，得到筛选后的信息
        paramMap.put("pageSize","30");//一页30条
        paramMap.put("orderType","2");
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_NEW_SUBDISTRICT,paramMap);

        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "village", model);
        //searchKey
        model.addAttribute("searchKey",paramMap.get("subName"));
        new MyPageable(paramMap, 30).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }

    //租房>小区找房详情页
    public void getVillage(String input,Model model){
        //小区详情
        HashMap hashMap = new HashMap();
        hashMap.put("subId",input);
        HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL,hashMap);
        if(null != villageDetail && null != villageDetail.get("content")){
            LinkedTreeMap result = (LinkedTreeMap<String,Object>)villageDetail.get("content");
            if (null != result && !StringUtils.isEmpty(result.get("description"))){
                String desc = result.get("description").toString();
                desc = desc.replaceAll("&lt;", "<").replaceAll("&quot;", "\"").replaceAll("&gt;", ">").replaceAll("&amp;mdash;","—").replaceAll("&amp;nbsp;","");
                desc = Pattern.compile("<style[^>]*?>[\\s\\S]*?<\\/style>").matcher(desc).replaceAll("");
                desc = Pattern.compile("<[^>]+>").matcher(desc).replaceAll("");
                desc = Pattern.compile("\\s*|\t|\r|\n").matcher(desc).replaceAll("");
                result.put("description",desc);
            }
            model.addAttribute("v",result);
            model.addAttribute("subId",input);
        }else{
            NoDataException.throwException();
        }
        //小区图片
        hashMap.put("pageSize",4);
        HashMap<String, Object> villagePic = HttpUtil.connectApi(Constants.GET_DISTRICT_PIC,hashMap);
        List<LinkedTreeMap<String,Object>> villPic = (List<LinkedTreeMap<String,Object>>)villagePic.get("content");
        model.addAttribute("vp",villPic);

        //获取四个同小区的租房房源
        Rent rent = new Rent();
        rent.setPageSize(4);
        rent.setSubId(input);
        HashMap<String, Object> renthouses = HttpUtil.connectApi(Constants.VIEW_RENT_OHTER,Utils.transBean2Map(rent));
        List<LinkedTreeMap<String,Object>> renthouse = (List<LinkedTreeMap<String,Object>>)renthouses.get("content");
        for (int i = 0 ; i<renthouse.size();i++) {
            renthouse.get(i).put("price",Utils.modifyNum(renthouse.get(i).get("price").toString()));
        }
        model.addAttribute("rent",renthouse);

        //获取似得同小区的二手房房源
        SecondHouse secondHouse = new SecondHouse();
        secondHouse.setPageSize(4);
        secondHouse.setSubId(input);
        HashMap<String, Object> secondhouses = HttpUtil.connectApi(Constants.VIEW_SCDHOUSE_OTHER,Utils.transBean2Map(secondHouse));
        List<LinkedTreeMap<String,Object>> secondhouse = (List<LinkedTreeMap<String,Object>>)secondhouses.get("content");
        for (int i = 0 ; i<secondhouse.size();i++) {
            secondhouse.get(i).put("price",Utils.modifyNum(secondhouse.get(i).get("price").toString()));
        }
        model.addAttribute("secondhouse",secondhouse);
    }

    /**
     * 小区详情（新）
     * @param subId
     * @param model
     */
    public void fetchNewSubDetail(String subId, Model model){
        HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.NEW_SUB_DETAIL, new Params().add("subId", subId).get());
        if(villageDetail != null && villageDetail.size() != 0){
            LinkedTreeMap result = (LinkedTreeMap<String,Object>)villageDetail.get("content");
            model.addAttribute("v",result);
            model.addAttribute("subId", subId);
        }else{
            NoDataException.throwException();
        }
        HashMap<String, Object> villagePic = HttpUtil.connectApi(Constants.NEW_SUB_PHOTOS, new Params().add("subId", subId).get());
        List<LinkedTreeMap<String,Object>> villPic = (List<LinkedTreeMap<String,Object>>)villagePic.get("content");
        model.addAttribute("vp",villPic);
        //获取四个同小区的租房房源
        Rent rent = new Rent();
        rent.setPageSize(4);
        rent.setSubId(subId);
        HashMap<String, Object> renthouses = HttpUtil.connectApi(Constants.VIEW_RENT_OHTER,Utils.transBean2Map(rent));
        List<LinkedTreeMap<String,Object>> renthouse = (List<LinkedTreeMap<String,Object>>)renthouses.get("content");
        for (int i = 0 ; i<renthouse.size();i++) {
            renthouse.get(i).put("price",Utils.modifyNum(renthouse.get(i).get("price").toString()));
        }
        model.addAttribute("rent",renthouse);

        //获取似得同小区的二手房房源
        SecondHouse secondHouse = new SecondHouse();
        secondHouse.setPageSize(4);
        secondHouse.setSubId(subId);
        HashMap<String, Object> secondhouses = HttpUtil.connectApi(Constants.VIEW_SCDHOUSE_OTHER,Utils.transBean2Map(secondHouse));
        List<LinkedTreeMap<String,Object>> secondhouse = (List<LinkedTreeMap<String,Object>>)secondhouses.get("content");
        for (int i = 0 ; i<secondhouse.size();i++) {
            secondhouse.get(i).put("price",Utils.modifyNum(secondhouse.get(i).get("price").toString()));
        }
        model.addAttribute("secondhouse",secondhouse);
    }






    //租房详情页
    public void getRent (String input,Model model) {
        LinkedTreeMap<String, Object> ltm = (LinkedTreeMap<String, Object>)HttpUtil.connectApi(Constants.GET_RENTHOUST_DETAIL,
                new Params("houseId",input).get(),model);
        if(ltm == null) NoDataException.throwException();
        if (!StringUtils.isEmpty(ltm.get("sortTel"))){
            StringBuffer sortTel = new StringBuffer(ltm.get("sortTel").toString());
            sortTel.insert(6,"-").insert(3,"-");
            ltm.put("sortTel",sortTel);
        }
        ltm.put("keeperTel2",Utils.decode(ltm.get("keeperTel2").toString()));
        ltm.put("price",Utils.modifyNum(ltm.get("price").toString()));
        ltm.put("area",Utils.modifyNum(ltm.get("area").toString()));
        if(StringUtils.isEmpty(ltm.get("subName"))){
            ltm.put("subName","其它");
        }
        if(StringUtils.isEmpty(ltm.get("keeperPic2"))){
            ltm.put("keeperPic2","");
        }
        model.addAttribute("info",ltm);
        String[] st = ltm.get("updateTime").toString().split(" ");
        String time = st[0];
        model.addAttribute("time",time.replaceAll("\\.","-"));
        String[] str = ltm.get("otherEstablish").toString().split(",");
        LinkedHashTreeMap<String,Object> hm = new LinkedHashTreeMap<>();
        for(int i = 0; i<str.length; i++){
            if(str[i].equals("床")){
                hm.put("床","p1");
            }if(str[i].equals("热水器")){
                hm.put("热水器","p2");
            }if(str[i].equals("洗衣机")){
                hm.put("洗衣机","p3");
            }if(str[i].equals("空调")){
                hm.put("空调","p4");
            }if(str[i].equals("冰箱")){
                hm.put("冰箱","p5");
            }if(str[i].equals("电视")){
                hm.put("电视","p6");
            }if(str[i].equals("宽带")){
                hm.put("宽带","p7");
            }if(str[i].equals("沙发")){
                hm.put("沙发","p8");
            }if(str[i].equals("衣柜")){
                hm.put("衣柜","p9");
            }if(str[i].equals("暖气")){
                hm.put("暖气","p10");
            }
        }
        model.addAttribute("other",hm);

        HashMap hashMap = new HashMap();
        hashMap.put("except",input);
        if(!StringUtils.isEmpty(ltm.get("subId"))){
            hashMap.put("subId",ltm.get("subId").toString());
        }
//        if(!StringUtils.isEmpty(ltm.get("price")) && !ltm.get("price").equals("0")){
//            hashMap.put("standardPrice",String.valueOf(Float.parseFloat(ltm.get("price").toString())));
//        }
        hashMap.put("pageSize",6);
        HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.VIEW_RENT_OHTER,hashMap);
        model.addAttribute("same",villageDetail.get("content"));
        model.addAttribute("houseId",input);
        //虚假举报字段展示
        HashMap<String, Object> r = HttpUtil.connectApi(Constants.FALSITY_HOUSE,null);
        HttpUtil.handleServiceList(r, "msg", "falsity", model);
        // 获取您可能感兴趣的房源(2019.02.15)
        HashMap<String, String> requiredParam = new HashMap<>();
        if(!StringUtils.isEmpty(ltm.get("price")) && !ltm.get("price").equals("0")){
            requiredParam.put("minPrice",String.valueOf(Float.parseFloat( ltm.get("price").toString())-200 ));
        }
        if(!StringUtils.isEmpty(ltm.get("price")) && !ltm.get("price").equals("0")){
            requiredParam.put("maxPrice",String.valueOf(Float.parseFloat( ltm.get("price").toString())+200 ));
        }
        if(!StringUtils.isEmpty(ltm.get("regionId"))){
            requiredParam.put("regionId", ltm.get("regionId").toString());
        }
        //分页为空默认为1
        if(StringUtils.isEmpty(ltm.get("page"))){
            requiredParam.put("page","1");
        }
        requiredParam.put("pageSize", String.valueOf(10));
        requiredParam.put("auction","1");
        requiredParam.put("sourceId","2");//经纪人
        HashMap<String, Object> interestResult = HttpUtil.connectApiString(Constants.GET_RENTHOUSE_LIST, requiredParam);
        HttpUtil.handleServiceList(interestResult, "msg", "interestHouse", model);
        model.addAttribute("houseVideo",ltm.get("houseVideo"));//把视频装进去
        //获取视频地址（2018.10.22）
        LinkedTreeMap<String ,Object> con = (LinkedTreeMap<String, Object>) ltm.get("houseVideo");
        if(!StringUtils.isEmpty(con.get("mediaID"))){
            String mediaId =(String)con.get("mediaID");
            HashMap<String, Object> path = HttpUtil.connectApi(Constants.VIEW_VIDEO_PATH, new Params("mediaId", mediaId).get());
            LinkedTreeMap<String ,Object> cont = (LinkedTreeMap<String, Object>) path.get("content");
            ArrayList videoPath = (ArrayList<String>)cont.get("videoPath");
            model.addAttribute("videoPath",videoPath.get(0));//视频地址
            model.addAttribute("videoImgPath",videoPath.get(1));//图片地址
        }else{
            model.addAttribute("videoPath","");//无视频地址
            model.addAttribute("videoImgPath","");//无视频首图
        }
        if(ltm.get("housePan") != null){
            LinkedTreeMap<String, Object> pan = (LinkedTreeMap<String, Object>) ltm.get("housePan");
            model.addAttribute("panUrl_VR",pan.get("panUrl"));
            model.addAttribute("panUrl_VRImg",pan.get("panImageUrl"));
        }
        //获取经纪人服务标签
        if(!StringUtils.isEmpty(ltm.get("agencyId"))){
            String agentId = (String)ltm.get("agencyId");
            secondHouseService.getAgentLabel(agentId,model);
        }
    }

    //租房详情页
    public void getDealRent (String input,Model model) {
        LinkedTreeMap<String, Object> ltm = (LinkedTreeMap<String, Object>)HttpUtil.connectApi(Constants.GET_RENTHOUST_DETAIL,
                new Params("houseId",input).get(),model);
        HttpUtil.connectApi(Constants.DEAL_RENTHOUST_DETAIL, new Params("houseId", input).get(), model, "dealSale");
        if(ltm == null) NoDataException.throwException();
        ltm.put("keeperTel2",Utils.decode(ltm.get("keeperTel2").toString()));
        ltm.put("price",Utils.modifyNum(ltm.get("price").toString()));
        ltm.put("area",Utils.modifyNum(ltm.get("area").toString()));
        if(StringUtils.isEmpty(ltm.get("subName"))){
            ltm.put("subName","其它");
        }
        model.addAttribute("info",ltm);
        String[] st = ltm.get("updateTime").toString().split(" ");
        String time = st[0];
        model.addAttribute("time",time);

        String[] str = ltm.get("otherEstablish").toString().split(",");
        LinkedHashTreeMap<String,Object> hm = new LinkedHashTreeMap<>();
        for(int i = 0; i<str.length; i++){
            if(str[i].equals("床")){
                hm.put("床","p1");
            }if(str[i].equals("热水器")){
                hm.put("热水器","p2");
            }if(str[i].equals("洗衣机")){
                hm.put("洗衣机","p3");
            }if(str[i].equals("空调")){
                hm.put("空调","p4");
            }if(str[i].equals("冰箱")){
                hm.put("冰箱","p5");
            }if(str[i].equals("电视")){
                hm.put("电视","p6");
            }if(str[i].equals("宽带")){
                hm.put("宽带","p7");
            }if(str[i].equals("沙发")){
                hm.put("沙发","p8");
            }if(str[i].equals("衣柜")){
                hm.put("衣柜","p9");
            }if(str[i].equals("暖气")){
                hm.put("暖气","p10");
            }
        }
        model.addAttribute("other",hm);

        HashMap hashMap = new HashMap();
        hashMap.put("except",input);
        if(!StringUtils.isEmpty(ltm.get("subId"))){
            hashMap.put("subId",ltm.get("subId").toString());
        }
        if(!StringUtils.isEmpty(ltm.get("price")) && !ltm.get("price").equals("0")){
            hashMap.put("standardPrice",String.valueOf(Float.parseFloat(ltm.get("price").toString())));
        }
        hashMap.put("pageSize",6);
        HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.VIEW_RENT_OHTER,hashMap);
        model.addAttribute("same",villageDetail.get("content"));
        model.addAttribute("houseId",input);
    }

    /**
     * 地图找房  右侧地图
     * @param hashMap
     * @return
     * @Date 2018.11.09
     */
    public HashMap<String, Object> queryRentHouse(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.QUERY_RENTMAP, hashMap);
    }

    /**
     * 地图找房  左侧列表
     * @param hashMap
     * @return
     * @Date 2018.11.09
     */
    public HashMap<String, Object> queryRentHouseList(HashMap<String ,Object> hashMap) {
        return HttpUtil.connectApi(Constants.QUERY_RENTMAP_LEFTLIST, hashMap);
    }

    /**
     * 租房通过位置地址获取坐标
     * @param hashMap
     * @return
     */
    public HashMap<String, Object> queryLocationByAddress(HashMap<String ,Object> hashMap){
        return HttpUtil.connectApi(Constants.GET_LOCATION_BY_ADDRESS, hashMap);
    }

    /**
     * 租房 地铁 站点及小区信息
     * @param hashMap
     * @return
     * <AUTHOR>
     */
    public HashMap<String, Object> rentStationMap(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.RENT_MAP_DUAL_INFO, hashMap);
    }

    /**
     * 地图找房  左侧列表
     * @param hashMap
     * @return
     * @Date 2019.09.11
     */
    public HashMap<String, Object> getRentHouseList(HashMap<String ,Object> hashMap) {
        hashMap.put("auction","1");
        return HttpUtil.connectApi(Constants.GET_RENTHOUSE_LIST, hashMap);
    }
}
