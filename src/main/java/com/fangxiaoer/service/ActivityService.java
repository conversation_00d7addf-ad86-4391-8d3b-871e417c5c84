package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.model.UrlBean;
import com.fangxiaoer.model.base.MyPageable;
import com.fangxiaoer.model.search.ActivitySearchModel;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.fangxiaoer.common.ParamsUtil.activityUrl;

/**
 * Created by le<PERSON><PERSON> on 2017/5/4.
 */
@Service
public class ActivityService {

    /**
     * 抢礼包
     *
     * @param input
     * @param model
     */
    public void queryActivity(String input, Model model){
        HashMap<String,String> resultMap = ParamsUtil.analysisInput(input);
        ArrayList<UrlBean> region = activityUrl(resultMap.get("projectType"),"/marketing/","r");
        model.addAttribute("region", region);
        ActivitySearchModel activitySearchModel = new ActivitySearchModel();
        if(!StringUtils.isEmpty(resultMap.get("projectType"))){
            activitySearchModel.setProjectType(resultMap.get("projectType"));
        }
        if(!StringUtils.isEmpty(resultMap.get("regionId"))){
            if(!"25".equals(resultMap.get("regionId"))) {
                activitySearchModel.setRegionId(resultMap.get("regionId"));
            }
        }
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.ACTIVITIES_LIST, Utils.transBean2Map(activitySearchModel));
        List<LinkedTreeMap<String, Object>> info = (List<LinkedTreeMap<String, Object>>) result.get("content");
        model.addAttribute("activities",info);
    }

    public void discount(Model model,String input,String baseUrl,String systemDate){
        //设置抢优惠类型
        HashMap<String ,String> params =  ParamsUtil.analysisInput(input,Constants.DiscountAd_LIST);
        params.put("activityType","11");
        params.put("pageSize","20");
        params.put("systemDate",systemDate);
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.DiscountAd_LIST,params);
        Integer status = Double.valueOf(result.get("status").toString()).intValue();
        if(status == 1){
            LinkedTreeMap content = (LinkedTreeMap)result.get("content");
            model.addAttribute("up",content.get("up"));
            model.addAttribute("activitys",content.get("list"));
        }else {
            model.addAttribute("up","");
            model.addAttribute("activitys","");
        }
        new MyPageable(params, 20).putAttribute(model, baseUrl + input, Integer.valueOf((String) result.get("msg")));
    }

    public HashMap<String,Object> viewActivity(String sessionId,String projectId,Integer projectType,Integer activityType){
        HashMap<String, Object> params = new HashMap<>();
        params.put("sessionId", sessionId);
        params.put("projectId", projectId);
        params.put("projectType", projectType);
        params.put("activityType", activityType);
        return HttpUtil.connectApi(Constants.VIEW_ACTIVITY_NEW, params);
    }

    /**
     * 获取小二管家专属优惠券
     * @param model
     * @return
     */
    public void viewManagerDiscountList(Model model,String params,String sessionId,String baseUrl){
        ActivitySearchModel activitySearchModel = new ActivitySearchModel();
        activitySearchModel.setActivityType("20");
        String page;
        if(StringUtils.isEmpty(params)){
            page="1";
            activitySearchModel.setPage(page);
        }else {
            page=params.substring(2);
            activitySearchModel.setPage(page);
        }
        activitySearchModel.setPageSize("12");
        activitySearchModel.setSessionId(sessionId);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.ACTIVITIES_LIST, Utils.transBean2Map(activitySearchModel));
        Integer status = Double.valueOf(result.get("status").toString()).intValue();
        List<LinkedTreeMap<String, Object>> info=null;
        if(status==1){
            info = (List<LinkedTreeMap<String, Object>>) result.get("content");
            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("page",page);
            new MyPageable(hashMap, 12).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        }
        model.addAttribute("activities",info);
    }

    public HashMap<String,Object> saveCollection(String sessionId, String memberName, String price, String panTime, String projectAddress, String trafficInfo,
                                                 String layoutInfo, String sortTel, String otherInfo, Integer projectId){
        return HttpUtil.connectApi(Constants.VIEW_COLLECTION, new Params("sessionId", sessionId).add("memberName", memberName).add("price", price).add("panTime", panTime)
                .add("projectAddress", projectAddress).add("trafficInfo", trafficInfo).add("layoutInfo", layoutInfo).add("sortTel", sortTel).add("otherInfo", otherInfo)
                .add("projectId", projectId).get());
    }

    /**
     * 检查当前项目是否有认筹活动，有的话，返回结果
     *
     * @param projectId
     * @param model
     * @return
     */
    public void projectPayCheck(Integer projectId, Model model){
         HttpUtil.connectApi(Constants.PROJECT_PAY_CHECK,new Params("projectId", projectId).get(), model,"projectPay");
    }
    /**
     * 检查当前项目是否有活动
     *
     * @param model
     * @return
     */
    public void viewActivity(String sessionId,String projectId,String projectType,String activityType ,Model model){
        HashMap<String, Object> params = new HashMap<>();
        params.put("sessionId", sessionId);
        params.put("projectId", projectId);
        params.put("projectType", projectType);
        params.put("activityType", activityType);
        HttpUtil.connectApi(Constants.VIEW_ACTIVITY_NEW,params, model,"viewActivity");
    }

    /**
     * 获取在线支付微信、支付宝付款的二维码
     *
     * @auther zhouboyu
     *
     * @param payMoney
     * @param payType
     * @param memberName
     * @param activityId
     * @param mobile
     * @param sessionId
     * @return
     */
    public HashMap<String, Object> pay_getQR(String payMoney, String payType, String memberName, String activityId, String mobile, String sessionId,String body) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("payMoney", payMoney);
        params.put("payType", payType);
        params.put("memberName", memberName);
        params.put("activityId", activityId);
        params.put("mobile", mobile);
        params.put("sessionId", sessionId);
        params.put("body", body);
        return HttpUtil.connectApi(Constants.PROJECT_PAY,params);
    }

    /**
     * 通过orderId获取在线支付订单的详情信息
     * {"1", "已支付"}
     * {"2", "支付中"}
     * {"3", "支付失败"}
     * {"4", "申请退款"}
     * {"5", "已退款"}
     * {"6", "已核销"}
     * {"99", "异常订单"}
     *
     * @auther zhouboyu
     *
     * @param orderId
     * @return
     *
     *
     */
    public HashMap<String, Object> viewPayOrderDetail(String orderId){
        return HttpUtil.connectApi(Constants.PROJECT_PAY_ORDER, new Params("orderId", orderId).get());
    }

    /**
     * 获取订单详情信息处理
     *
     * @param orderId
     * @param model
     */
    public void viewEarnestPayOrderDetail(String orderId, Model model) {
        LinkedTreeMap<String, Object> result = (LinkedTreeMap) HttpUtil.connectApi(Constants.NEWHOUSE_ORDER_DETAIL,
                new Params("orderId", orderId).add("whetherMobile", 1).get(), model);
        model.addAttribute("orderInfo", result);
    }

    /**
     * 新房认购->选择户型
     * @param projectId
     * @param buildType
     * @return
     */
    public HashMap getLayOut(Integer projectId,Integer buildType){
        return HttpUtil.connectApi(Constants.NEWHOUSE_GET_LAYOUT,new Params("projectId",projectId).add("buildType",buildType).get());
    }

    /**
     * 新房认购—>获取楼号
     * @param projectId
     * @param buildType
     * @param layId
     * @return
     */
    public HashMap getBuildName(Integer projectId,Integer buildType,Integer layId){
        return HttpUtil.connectApi(Constants.NEWHOUSE_GET_BUILDNAME,new Params("projectId",projectId).add("buildType",buildType).add("layId",layId).get());
    }

    /**
     * 新房认购->获取单元
     * @param projectId
     * @param buildType
     * @param layId
     * @param buildId
     * @return
     */
    public HashMap getUnit(Integer projectId,Integer buildType,Integer layId,Integer buildId){
        return HttpUtil.connectApi(Constants.NEWHOUSE_GET_UNIT,new Params("projectId", projectId).add("buildType", buildType).add("buildId", buildId).add("layId", layId).get());
    }

    /**
     * 新房认购->获取房号
     * @param projectId
     * @param buildType
     * @param layId
     * @param buildId
     * @param unit
     * @return
     */
    public HashMap getRoom(Integer projectId,Integer buildType,Integer layId,Integer buildId,String unit){
        return HttpUtil.connectApi(Constants.NEWHOUSE_GET_ROOM,new Params("projectId", projectId).add("buildType", buildType).add("buildId", buildId).add("layId", layId).add("unit", unit).get());
    }

    /**
     * 新房认购->创建订单
     * @param sessionId
     * @param whetherAgent
     * @param IDcard
     * @param memberName
     * @param mobile
     * @param projectId
     * @param buildType
     * @param roomId
     * @param address
     * @param email
     * @param smsCode
     * @return
     */
    public HashMap createOrder(String sessionId,Integer whetherAgent,String IDcard,String memberName,String mobile,Integer projectId,Integer buildType,Integer roomId,String address,String email,String smsCode){
        return HttpUtil.connectApi(Constants.NEWHOUSE_CREAT_ORDER,new Params("sessionId", sessionId).add("whetherAgent", whetherAgent).add("IDcard", IDcard).add("memberName", memberName).add("mobile", mobile).add("projectId", projectId).add("buildType", buildType).add("roomId", roomId).add("address", address).add("email", email).add("smsCode", smsCode).get());
    }

    /**
     * 新房认购->订单详情
     * @param orderId
     * @return
     */
    public HashMap getOrderDetail(String orderId){
        return HttpUtil.connectApi(Constants.NEWHOUSE_ORDER_DETAIL,new Params("orderId",orderId).add("whetherMobile",1).get());
    }

    /**
     * 新房认购->支付
     * @param payMoney
     * @param id
     * @param sessionId
     * @param body
     * @return
     */
    public HashMap<String, Object> orderPay(String payMoney, Integer id, String sessionId,String body) {
        return  HttpUtil.connectApi(Constants.NEWHOUSE_PROJECT_PAY, new Params("payMoney", payMoney).add("sessionId", sessionId).add("payType", 99).add("id", id).add("body", body).get());

    }

    /**
     * 新房认购->支付成功回调
     *
     * @param orderId
     * @return
     */
    public HashMap payForSuccess(String orderId) {
        return HttpUtil.connectApi(Constants.PAY_FOR_SUCCESS, new Params("orderId", orderId).get());
    }

    /**
     * 新房认购->检查是否签署
     *
     * @param orderId
     * @return
     */
    public HashMap checkIsSign(String orderId) {
        return HttpUtil.connectApi(Constants.NEWHOUSE_ORDER_DETAIL, new Params("orderId", orderId).add("whetherAgent",1).get());
    }

    public HashMap<String, Object> saleOthersFilter() {
        return HttpUtil.connectApi(Constants.SALE_OTHER_FILTER, new Params().get());
    }

    public HashMap<String, Object> saleInfoFilter() {
        return HttpUtil.connectApi(Constants.SALE_INFO_FILTER, new Params().get());
    }

    public HashMap<String, Object> rentInfoFilter() {
        return HttpUtil.connectApi(Constants.RENT_INFO_FILTER, new Params().get());
    }

    public HashMap<String, Object> shopInfoFilter() {
        return HttpUtil.connectApi(Constants.SHOP_INFO_FILTER, new Params().get());
    }

    public HashMap<String, Object> saleFeatureFilter() {
        return HttpUtil.connectApi(Constants.SALE_FEATURE_FILTER, new Params().get());
    }

    public HashMap<String, Object> rentFeatureFilter() {
        return HttpUtil.connectApi(Constants.RENT_FEATURE_FILTER, new Params().get());
    }

    public HashMap<String, Object> regionFilter() {
        return HttpUtil.connectApi(Constants.NEWHOUSE_REGION_FILTER, new Params().get());
    }

    public HashMap<String, Object> usedRegionFilter() {
        return HttpUtil.connectApi(Constants.FILTER_SCDHOUSE_REGIONS, new Params().get());
    }

    public HashMap<String, Object> layoutFilter() {
        return HttpUtil.connectApi(Constants.FILTER_LAYOUT, new Params().get());
    }

    public HashMap<String, Object> relationShipFilter() {
        return HttpUtil.connectApi(Constants.RELATIONSHIP_FILTER, new Params().get());
    }



    /**
     * 抢优惠 获取补贴项目情况
     *
     * @param projectId 项目id
     * @return
     */
    public HashMap<String, Object> viewSubsidyForProject(String projectId) {
        return HttpUtil.connectApi(Constants.VIEW_SUBSIDY_FOR_PROJECT, new Params().add("projectId", projectId).get());
    }

    /**
     * 抢优惠 领取项目补贴
     *
     * @param sessionId     用户表示
     * @param subsidyId     活动ID
     * @param memberName    用户名
     * @param whetherChoose 0-系统分配经纪人，1-自选经纪人
     * @return
     */
    public HashMap<String, Object> fetchSubsidyForProject(String sessionId, Integer subsidyId, String memberName, Integer whetherChoose) {
        return HttpUtil.connectApi(
                Constants.FETCH_SUBSIDY_FOR_PROJECT,
                new Params()
                        .add("sessionId", sessionId)
                        .add("subsidyId", subsidyId)
                        .add("memberName", memberName)
                        .add("whetherChoose", whetherChoose)
                        .get()
        );
    }

}
