package com.fangxiaoer.service;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.common.ParamsUtil;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

@Service
public class AgentRecruitService {

    /**
     * 经纪人店长招聘列表
     * @param baseUrl
     * @param params
     * @param model
     * @param request
     */
    public void getAgentRecuritList(String baseUrl, String params, Model model, HttpServletRequest request){
        String [] input = params.split("-");
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_AGENT_RECRUIT_LIST);
        ParamsUtil.addFilterIntoModel(Constants.GET_AGENT_RECRUIT_LIST, model, baseUrl, params, paramMap);
        model.addAttribute("search",paramMap.get("searchKey"));
        ParamsUtil.addSeoTitle(model, "orderKey");
        String seoSubName = "";
//        model.addAttribute("seoSubName", seoSubName);
        int pageSize = 20;
        paramMap.put("pageSize",""+pageSize);
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_AGENT_RECRUIT_LIST, paramMap);
        HttpUtil.handleServiceList(result, "msg", "recruitList", model);
        new MyPageable(paramMap, pageSize).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        //经纪人公司信息logo
        HashMap<String, Object> results = HttpUtil.connectApi(Constants.GET_AGENT_COMPANYS, new Params("companyIsShow", "1").get());
        HttpUtil.handleServiceList(results, "msg", "agentCompanys", model);
        //右侧岗位急聘
        HashMap<String, Object> re = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_URGENT, new Params("jobIsUrgent","1").get());
        HttpUtil.handleServiceList(re, "msg", "agentUrgent", model);
        //右侧广告位(此广告位的位置就是 576 )
        HashMap<String, Object> r = HttpUtil.connectApi(Constants.GET_RIGHT_ADVERTISEMENT, new Params("locationId","576").get());
        HttpUtil.handleServiceList(r, "msg", "advertisement", model);
        // 2020/11/20 10:20 Bowen 获取版块集合、学历要求、入职类型
        HashMap<String, Object> searchMap = new HashMap<>();
        HashMap<String, Object> plateListResult = HttpUtil.connectApi(Constants.FILTER_SCDHOUSE_PLATES, searchMap);
        HashMap<String, Object> educationResult = HttpUtil.connectApi(Constants.FILTER_RESUME_EDUCATION, searchMap);
        HashMap<String, Object> inductionResult = HttpUtil.connectApi(Constants.FILTER_RESUME_INDUCTION, searchMap);
        HashMap<String, Object> workingLifeResult = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_EXPERIENCE, searchMap);
        HashMap<String, Object> regionResult = HttpUtil.connectApi(Constants.FILTER_SCDHOUSE_REGIONS, searchMap);
        model.addAttribute("regionList",regionResult.get("content"));
        model.addAttribute("plateList",plateListResult.get("content"));
        model.addAttribute("education", educationResult.get("content"));
        model.addAttribute("induction",inductionResult.get("content"));
        model.addAttribute("workingLife",workingLifeResult.get("content"));
    }

    /**
     * 公司详情页
     * @param companyId
     * @param model
     */
    public LinkedTreeMap<String, Object> getCompanyDetail(String companyId, Model model){
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_COMPANY_DETAILS,
                new Params("companyId", companyId).get(), model);
        if(result != null ){
            model.addAttribute("companyDetail",result.get("company"));
            model.addAttribute("jobList",result.get("jobs"));
        }
        return result;
    }
    /**
     * 职位详情页
     * @param positionId
     * @param model
     */
    public LinkedTreeMap<String, Object> getPositionDetail(String positionId, Model model){
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_JOB_DETAILS,
                new Params("jobAdId", positionId).get(), model);
        if(result != null){
            model.addAttribute("companyDetail",result.get("company"));
            model.addAttribute("jobDetail",result.get("job"));
            model.addAttribute("otherJob",result.get("otherJobs"));
        }else{
            NoDataException.throwException();
        }
        return result;
    }

    /**
     * 获取会员简历
     * <AUTHOR>
     * @Date 2020/11/21 11:04
     **/
    public LinkedTreeMap<String, Object> getCommonMemberResume(String sessionId){
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.VIEW_COMMONMEMBER_RESUME, new Params("sessionId",sessionId).get());
        LinkedTreeMap<String, Object> memberResume = (LinkedTreeMap<String, Object>) result.get("content");
        return memberResume;
    }

    /**
     * 添加会员简历
     * <AUTHOR>
     * @Date 2020/11/23 8:29
     **/
    public HashMap<String, Object> addCommonMemberResume(String sessionId, HashMap<String, Object> params){
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.ADD_COMMONMEMBER_RESUME, params);
        return result;
    }

    /**
     * 修改会员简历
     * <AUTHOR>
     * @Date 2020/11/23 8:28
     **/
    public HashMap<String, Object> setCommonMemberResume(String sessionId, HashMap<String, Object> params){
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.SET_COMMONMEMBER_RESUME, params);
        return result;
    }


}
