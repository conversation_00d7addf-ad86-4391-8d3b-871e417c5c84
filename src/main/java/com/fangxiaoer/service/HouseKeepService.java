package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.model.Mall;
import com.fangxiaoer.model.NameIdBean;
import com.fangxiaoer.model.UrlBean;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2017/6/22.
 */
@Service
public class HouseKeepService {
    public void getMall(String input, Model model){
        ArrayList<NameIdBean> titleList = ParamsUtil.getFilters(Constants.FILTER_HOUSEKEEP_MALLTYPE);
        ArrayList<UrlBean> titleArray = ParamsUtil.generateItem(input, titleList, "t", "/mall/");
        model.addAttribute("title",titleArray);

        Mall mall = new Mall();
        HashMap<String,String> result = ParamsUtil.analysisInput(input);
        if(!StringUtils.isEmpty(result.get("feature"))){
            if(!"25".equals(result.get("feature"))){
                mall.setSort(result.get("feature"));
            }
        }
        model.addAttribute("lpfl",result.get("feature"));
        mall.setPageSize(100);
        HashMap<String, Object> mallMap = HttpUtil.connectApi(Constants.GET_HOUSEKEEP_MALLLIST, Utils.transBean2Map(mall));
        List<LinkedTreeMap<String, Object>> mallList = (List<LinkedTreeMap<String, Object>>)mallMap.get("content");
        model.addAttribute("mall",mallList);
    }
    /**
     * 积分商城详情
     */
    public void getMallDetail(String id,Model model){
        LinkedTreeMap<String, Object> ltm = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_HOUSEKEEP_MALLDETAIL,
                new Params("id",id).get(), model);
        model.addAttribute("goods",ltm);
    }
}
