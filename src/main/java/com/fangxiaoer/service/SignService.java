package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.model.LoginResultFilter;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.HashMap;

/**
 * Created by Lyn on 2017/3/28.
 */
@Service
public class SignService {

    /**
     * 用户登录
     * @return
     */
    public boolean simpleLogin(String user, String password, HttpSession httpSession, HttpServletResponse response){
        // Todo loginVerion
        HashMap<String, Object> loginResult = HttpUtil.connectApi(Constants.SIMPLE_LOGIN,new Params("telNumber",user).add("pwd",password).add("memberType",1)
                .add("loginWay",1).add("loginVerion","m.com.fangxiaoer.mobile").add("registFromUrl", "syNormalLogin").get());
        if(loginResult == null) {
            return false;
        }
        int status = Double.valueOf((double)loginResult.get("status")).intValue();
        if(status == 0) {
            return false;
        }
        if(StringUtils.isEmpty(loginResult.get("content"))) {
            return false;
        }
        LinkedTreeMap<String,Object> result = (LinkedTreeMap) loginResult.get("content");
        Gson gson = new Gson();
        LoginResultFilter loginResultFilter = gson.fromJson(gson.toJson(result),LoginResultFilter.class);
        Utils.addSessionAndCookie(httpSession,response,loginResultFilter,password);
        return true;
    }

    /**
     * ajax登录方案
     * @param telNumber
     * @param password
     * @param httpSession
     * @param response
     * @return
     */
    public HashMap<String,Object> simpleLogins(String telNumber, String password, HttpSession httpSession, HttpServletResponse response, String goods, String registFromUrl){
        Integer memberType = 0;
        if (goods != null && goods.equals("1")) {
            memberType = 1;
        }
        HashMap<String, Object> loginResult = HttpUtil.connectApi(Constants.SIMPLE_LOGIN,new Params("telNumber",telNumber).add("pwd",password).add("memberType",memberType)
                .add("loginWay",1).add("loginVerion","sy.fangxiaoer.com").add("registFromUrl", registFromUrl.replaceAll("scriptorium", "foroffice")).get());
        if(loginResult != null && Double.valueOf((double)loginResult.get("status")).intValue() == 1
                && !StringUtils.isEmpty(loginResult.get("content"))) {
            LinkedTreeMap<String,Object> result = (LinkedTreeMap) loginResult.get("content");
            Gson gson = new Gson();
            LoginResultFilter loginResultFilter = gson.fromJson(gson.toJson(result),LoginResultFilter.class);
            Utils.addSessionAndCookie(httpSession,response,loginResultFilter,password);
        }
        return loginResult;
    }

/*    *//**
     * 评论ajax登录方案
     * @param telNumber
     * @param password
     * @param httpSession
     * @param response
     * @return
     *//*
    public HashMap<String,Object> simpleLoginsComment(String telNumber, String password, HttpSession httpSession, HttpServletResponse response){
        HashMap<String, Object> loginResult = HttpUtil.connectApi(Constants.SIMPLE_LOGIN,new Params("telNumber",telNumber).add("pwd",password).add("memberType",0)
                .add("loginWay",1).add("loginVerion","sy.fangxiaoer.com").get());
        if(loginResult != null && Double.valueOf((double)loginResult.get("status")).intValue() == 1
                && !StringUtils.isEmpty(loginResult.get("content"))) {
            LinkedTreeMap<String,Object> result = (LinkedTreeMap) loginResult.get("content");
            Gson gson = new Gson();
            LoginResultFilter loginResultFilter = gson.fromJson(gson.toJson(result),LoginResultFilter.class);
            Utils.addSessionAndCookie(httpSession,response,loginResultFilter,password);
        }
        return loginResult;
    }*/

    /**
     * 获取sessionId
     * @param httpSession
     * @return
     */
    public String checkLogin(HttpSession httpSession){
        Object sessionId = httpSession.getAttribute("muser");
        if(sessionId != null){
            return sessionId.toString();
        }else{
            return null;
        }
    }

    /**
     * 发送验证码
     *
     * @param mobile
     * @return
     */
    public HashMap<String,Object> sendSMSCode(String mobile){
        HashMap<String, Object> result = new HashMap<>();
        try {
            Long timeMillis = System.currentTimeMillis();
            String md5 = Utils.MD5encode("fangxiaoer#" + mobile + timeMillis);
            result = HttpUtil.connectApi(Constants.SMS_CODE,new Params("mobile",mobile).add("timeMillis",timeMillis).add("md5",md5)
                    .add("client","1").get());
            int status = Double.valueOf((double)result.get("status")).intValue();
            result.put("status",status);
        } catch (Exception e) {
            result.put("status", 0);
            result.put("msg", e.getMessage());
        }
        return result;
    }

    /**
     * 检测Cookie进行登录
     * @param request
     * @param response
     * @param session
     * @return
     */
    public boolean checkCookie(HttpServletRequest request,HttpServletResponse response,HttpSession session){
        Cookie cookievalue1 = Utils.iterationCookie(request, "muser");
        Cookie cookievalue2 = Utils.iterationCookie(request, "mpassword");
        if(cookievalue1 != null && cookievalue2 != null){
            String user = cookievalue1.getValue();
            String password = cookievalue2.getValue();
            Boolean login = simpleLogin(user,password,session,response);
            if(!login){
                Utils.deleteCookie(response,"muser","/");
                Utils.deleteCookie(response,"mpassword","/");
                return false;
            }
            return true;
        }else{
            Utils.deleteCookie(response,"muser","/");
            Utils.deleteCookie(response,"mpassword","/");
            return false;
        }
    }

    /**
     * 快速注册
     * @param mobile
     * @param code
     * @param loginWay
     * @param loginVerion
     * @return
     */
    public HashMap<String,Object> quickRegist(String mobile,String code,String loginWay, String loginVerion,HttpSession httpSession,HttpServletResponse response, String registFromUrl){
        HashMap<String,Object> result = (HashMap) HttpUtil.connectApi(Constants.QUICK_REGIST, new Params("telNumber",mobile)
                .add("code",code).add("loginWay",loginWay).add("loginVerion",loginVerion).add("registFromUrl", registFromUrl).get());
        int status = Double.valueOf((double)result.get("status")).intValue();
        LinkedTreeMap<String,Object> loginResult = (LinkedTreeMap) result.get("content");
        Gson gson = new Gson();
        LoginResultFilter loginResultFilter = gson.fromJson(gson.toJson(loginResult),LoginResultFilter.class);
        if(status==1 && loginResultFilter != null){
            Utils.addSessionAndCookie(httpSession,response,loginResultFilter,Utils.MD5encode(code));
        }
        return result;
    }

    /**
     * 注销用户
     * @param session
     * @param response
     * @return
     */
    public void logout(HttpSession session, HttpServletResponse response,HttpServletRequest request){
        //增加一个调用登出session的接口
        if(!StringUtils.isEmpty(session.getAttribute("sessionId"))){
            String sessionId  = session.getAttribute("sessionId").toString();
            HashMap<String,Object> result = (HashMap) HttpUtil.connectApi(Constants.ALL_LOGOUT, new Params("sessionId",sessionId).get());
        }
        session.invalidate();
        Utils.deleteCookie(response,"muser","/");
        Utils.deleteCookie(response,"mpassword","/");
        Utils.deleteCookie(response,"aspxUserTicket","/");
        Utils.deleteCookie(response, "aspxUserTicket", "/", ".fangxiaoer.com");
        Utils.removeDomainCookie(request,response);
    }

    /**
     * 验证是否是会员
     * @param mobile
     * @param model
     * @param session
     * @return
     */
    public Integer checkPhone(String mobile, Model model,HttpSession session){
        HashMap<String,Object> result = HttpUtil.connectApi(Constants.CHECK_PHONE,new Params("mobile",mobile).get());
        return HttpUtil.handleServiceMethod(result,model,session);
    }

    /**
     * 验证验证码问题
     * @param mobile
     * @param code
     * @return
     */
    public Integer checkPasscode(String mobile,String code){
        HashMap<String,Object> result = HttpUtil.connectApi(Constants.CHECK_PASSCODE,new Params("mobile",mobile).add("code",code).get());
        int status = Double.valueOf((double)result.get("status")).intValue();
        return status;
    }

    public HashMap<String, Object> checkOCRIDCard(String imageUrl, String side, String timeMillis) {
        String md5 = DigestUtils.md5Hex("fangxiaoer#" + timeMillis);
        return HttpUtil.connectApi(Constants.CHECK_OCR_ID_CARD,new Params("imageUrl",imageUrl).add("side",side).add("timeMillis",timeMillis).add("md5",md5).get());
    }
}
