package com.fangxiaoer.service;

import com.alibaba.fastjson.JSONObject;
import com.fangxiaoer.common.*;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.Guide;
import com.fangxiaoer.model.TNewsDetail;
import com.fangxiaoer.model.TouristOrder;
import com.fangxiaoer.model.base.MyPageable;
import com.fangxiaoer.model.search.AskSearchModel;
import com.fangxiaoer.model.search.CommentCountModel;
import com.fangxiaoer.model.search.CommentSearchModel;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * Created by leiwei on 2017/4/27.
 */
@Service
public class HouseService {
    /**
     * 新房查询(普房)
     *
     * @param params
     * @return
     */
    public void getNewHouse(String baseUrl, String params, Model model, String systemDate) {
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.VIEW_PROJECT_LIST);
        ParamsUtil.addFilterIntoModel(Constants.VIEW_PROJECT_LIST, model, baseUrl, params, paramMap);
        HashMap<String,String>  recommentParamMap = new HashMap();
        ParamsUtil.addSeoTitle(model, "");
        String titleType = paramMap.get("projectType");
        String type = "1";
        // 品牌房企
        if(baseUrl.contains("brand")) {
            titleType = "6";
            paramMap.put("isBrandHouse", "1");
        } else if(baseUrl.contains("subway")) {
            paramMap.put("isSubWay", "1");
            titleType = "7";
            type = "10";
            recommentParamMap.put("isSubWay", "1");
        } else if(baseUrl.contains("exits")) {
            if(StringUtils.isEmpty(paramMap.get("existinfo"))){
                paramMap.put("existinfo", "1");
            }
            titleType = "8";
        } else if(baseUrl.contains("lowpay")) {
            paramMap.put("isPayment","1");
            titleType = "9";
        }
        model.addAttribute("titleType", titleType);
        model.addAttribute("searchKey", paramMap.get("projectName"));
        if (paramMap.containsKey("minPrice") || paramMap.containsKey("maxPrice")){
            model.addAttribute("minPrice",paramMap.get("minPrice"));
            model.addAttribute("maxPrice",paramMap.get("maxPrice"));
        }
        if (params.contains("pt3") || params.contains("pt5") ){
            model.addAttribute("HouseType","big");
        }else {
            model.addAttribute("HouseType","small");
        }
        if (params.contains("bp1")){
            paramMap.put("maxPrice","10000");
        }else if (params.contains("bp2")){
            paramMap.put("minPrice","10000");
            paramMap.put("maxPrice","12000");
        }else if (params.contains("bp3")){
            paramMap.put("minPrice","12000");
            paramMap.put("maxPrice","15000");
        }else if (params.contains("bp4")){
            paramMap.put("minPrice","15000");
            paramMap.put("maxPrice","20000");
        }else if (params.contains("bp5")){
            paramMap.put("minPrice","20000");
        }
        paramMap.put("pageSize","20");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_PROJECT_LIST, paramMap);
        HttpUtil.handleServiceList(result, "msg", "newhouse", model);
        new MyPageable(paramMap, 20).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        //更改于2018年7月13日普宅列表加入推荐房源——zhaoxiao
        Map modelMap = model.asMap();
        HashMap<String, Object> recommentResult = null;
        Integer resultNumber = Integer.valueOf(modelMap.get("msg").toString());
        //更改于2018年7月23日添加类型--zhaoxiao
        if(paramMap.get("projectType") != null){
            recommentParamMap.put("projectType",paramMap.get("projectType"));
        }
        if(resultNumber ==1){
            ArrayList al1 = (ArrayList)modelMap.get("newhouse");
            LinkedTreeMap   currentHouse =    (LinkedTreeMap)al1.get(0);
            String regionId = currentHouse.get("regionId") == null? "":currentHouse.get("regionId").toString();
            recommentParamMap.put("regionId",regionId);
            recommentParamMap.put("excepts",currentHouse.get("projectId").toString());
            recommentResult = HttpUtil.connectApiString(Constants.VIEW_PROJECT_LIST, recommentParamMap);
        }else if(resultNumber<=7){
            if(resultNumber != 0){
                ArrayList<LinkedTreeMap> al1 = (ArrayList<LinkedTreeMap>)modelMap.get("newhouse");
                StringBuffer projectIds = new StringBuffer();
                if(al1 != null && al1.size()!=0){
                    for (LinkedTreeMap item:al1){
                        projectIds.append(item.get("projectId")).append(",");
                    }
                    recommentParamMap.put("excepts",projectIds.toString());
                }
            }
            recommentResult = HttpUtil.connectApiString(Constants.VIEW_PROJECT_LIST, recommentParamMap);
        }
        if(recommentResult != null && Double.valueOf((double) result.get("status")).intValue() ==1){
            model.addAttribute("recommentResult",recommentResult.get("content"));
        }else {
            model.addAttribute("recommentResult","");
        }
        HashMap<String, Object> adMap = getAdvertisementList(systemDate, type);
        LinkedTreeMap<String, Object> adMapContent = (LinkedTreeMap<String, Object>) adMap.get("content");
        model.addAllAttributes(adMapContent);
        model.addAttribute("baseUrl",baseUrl.replace("/",""));
    }

    /**
     * 精装房列表页
     *
     * @par/am params
     * @return
     */
    public void getDecorations(String baseUrl, String params, Model model, String systemDate) {
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.VIEW_DECORATE_HOUSES);
        ParamsUtil.addFilterIntoModel(Constants.VIEW_DECORATE_HOUSES, model, baseUrl, params, paramMap);
        paramMap.put("pageSize","20");
        paramMap.put("tab","1");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_DECORATE_HOUSES, paramMap);
        HttpUtil.handleServiceList(result, "msg", "decoration", model);
        new MyPageable(paramMap, 20).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("guesslike",HttpUtil.connectApi(Constants.GUESS_LIKE, new Params("locationId","556").add("systemDate",systemDate).get(), true).get("content"));
        model.addAttribute("flash",HttpUtil.connectApi(Constants.GET_FLASH_LIST, true).get("content"));
        model.addAttribute("baseUrl",baseUrl.replace("/",""));
        model.addAttribute("regionSubwayId",paramMap.get("regionSubwayId"));
        model.addAttribute("searchKey", paramMap.get("projectName"));
    }

    /**
     * 获取学校列表
     * @param baseUrl
     * @param params
     * @param model
     */
    public void getSchools(String baseUrl, String params, Model model, String systemDate){
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.NEW_SCHOOL_LIST);
        ParamsUtil.addFilterIntoModel(Constants.NEW_SCHOOL_LIST, model, baseUrl, params, paramMap);
        paramMap.put("pageSize","20");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.NEW_SCHOOL_LIST, paramMap);
        Integer totalNum =  Integer.valueOf((String) result.get("msg"));
        new MyPageable(paramMap, 20).putAttribute(model, baseUrl + params, totalNum);
        HttpUtil.handleServiceList(result, "msg", "schools", model);
        if(totalNum <= 7){
            HashMap<String, String> recommendParamMap = new HashMap<>();
            if(!StringUtils.isEmpty(paramMap.get("schoolTypeId"))){
                recommendParamMap.put("schoolTypeId",paramMap.get("schoolTypeId"));
            }
            if(totalNum !=0){
                StringBuffer sids = new StringBuffer();
                ArrayList<LinkedTreeMap<String,Object>> items = (ArrayList<LinkedTreeMap<String,Object>>)result.get("content");
                for(LinkedTreeMap<String,Object> item:items){
                    sids.append(Double.valueOf(item.get("schoolId").toString()).intValue()).append(",");
                }
                recommendParamMap.put("except",sids.toString());
            }
            result = HttpUtil.connectApiString(Constants.NEW_SCHOOL_LIST, recommendParamMap);
            if(Double.valueOf((double) result.get("status")).intValue()==1){
                model.addAttribute("recommendSchool",result.get("content"));
            }else {
                model.addAttribute("recommendSchool",null);
            }
        }
        model.addAttribute("schoolRight",HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type","9").get(),true).get("content"));
        model.addAttribute("schoolNews",HttpUtil.connectApi(Constants.SCHOOL_NEWS_HELP, new Params("pageSize", "7").get(),true).get("content"));
        model.addAttribute("horizontalBanner",HttpUtil.connectApi(Constants.GUESS_LIKE, new Params("locationId","566").add("systemDate", systemDate).get(),true).get("content"));//通栏广告
        model.addAttribute("baseUrl",baseUrl.replace("/",""));
        // 热搜榜
        HttpUtil.connectApi(Constants.GET_RANK_LIST_BYTYPE, new Params("rankType", 2).add("page", "1").add("pageSize", "10").get(), model, "rankList");
    }

    public void getSchoolHouse(String baseUrl, String params, Model model){
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("schoolId", params);
        ParamsUtil.addFilterIntoModel(Constants.NEW_SCHOOL_LIST, model, baseUrl, params, paramMap);
        paramMap.put("tab", "1");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_SCHOOL_HOUSE, paramMap);
        if(result != null){
            model.addAttribute("schoolInfo",result.get("content"));
        }
        model.addAttribute("schoolNews",HttpUtil.connectApi(Constants.SCHOOL_NEWS_HELP, new Params("pageSize", "7").get(),true).get("content"));
        model.addAttribute("schoolRight",HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type","9").get(),true).get("content"));
        model.addAttribute("guesslike",HttpUtil.connectApi(Constants.GUESS_LIKE, new Params("locationId","553").get(), true).get("content"));
        // 热搜榜
        HttpUtil.connectApi(Constants.GET_RANK_LIST_BYTYPE, new Params("rankType", 2).add("page", "1").add("pageSize", "10").get(), model, "rankList");
    }

    public Integer saveGuide(Guide guide, Model model, HttpSession session) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.HELP_SEARCH_HOUSE, Utils.transBean2Map(guide));
        return HttpUtil.handleServiceMethod(result, model, session);
    }

    public List<LinkedTreeMap<String, Object>> getOnlineJson(String projectId) {
        HashMap<String, Object> params = new Params("projectId", projectId).get();
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.HOUSE_DETAIL_JSON, params);
        return (List<LinkedTreeMap<String, Object>>)result.get("content");
    }
    /**
     * 新房详情页
     */
    public void newHouseDetail(String projectId, Model model) {
        viewHouseInfo(projectId, model);
        HashMap<String, Object> params = new Params("projectId", projectId).get();
        HttpUtil.connectApi(Constants.PROJECT_SALE_INFO, params, model, "saleInfo");
        HttpUtil.connectApi(Constants.HOUSE_SALE_INFO, params, model, "saleHouse");
        HttpUtil.connectApi(Constants.GET_NEWS_LIST, new Params("houseId", projectId).get(), model, "newsDetail");
        HttpUtil.connectApi(Constants.HOUSE_LAYOUT_INFO, params, model, "layoutDetail");
        HttpUtil.connectApi(Constants.GET_LAYOUT_TYPE, params, model, "layoutType");
        //项目问答
        AskHandler(projectId,"1",1,model,3);
        //项目评价
        getIndexCommentList(model,projectId,"3", "1");
    }

    public void viewHouseInfo(String projectId, Model model) {
        HashMap<String, Object> params = new Params("projectId", projectId).get();
        HttpUtil.connectApi(Constants.PROJECT_SALE_INFO, params, model, "saleInfo");
        HttpUtil.connectApi(Constants.HOUSE_DETAIL_PAGE, params, model, "houseInfo");
        if(model.asMap().get("houseInfo") == null) NoDataException.throwException();

    }

    /**
     * 720度看房
     */
    public void viewPan(String projectId, String projectType, String panId, Model model) {
        HttpUtil.connectApi(Constants.VIEW_PAN_LIST, new Params("projectId", projectId).add("projectType",projectType).get(), model, "panList");
        List<LinkedTreeMap<String, Object>> panList = (List<LinkedTreeMap<String, Object>>)model.asMap().get("panList");
        if (panList.size() == 0) NoDataException.throwException();
        LinkedTreeMap<String, Object> display = null;
        for(LinkedTreeMap<String, Object> item : panList) {
            if(!StringUtils.isEmpty(panId) && item.get("PanID").equals(panId)) {
                display = item;
            }
        }
        if(display == null) {
            display = panList.get(0);
        }
        model.addAttribute("display", display);
    }
    /**
     * 获取项目评论数量
     * 加带条件查询数量信息
     * @param commentCountModel
     * @return
     */
    public LinkedTreeMap<String, Object> getCommentCount(CommentCountModel commentCountModel) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.PROJECT_COMMENT_COUNT, Utils.transBean2Map(commentCountModel));
        return  (LinkedTreeMap<String, Object>) result.get("content");
    }

    /**
     * 获取咨询数量
     *
     * @param projectId
     * @return
     */
    public LinkedTreeMap<String, Object> getAskCount(String projectId) {
        HashMap<String, Object> askResult = HttpUtil.connectApi(Constants.PROJECT_ASK_COUNT, new Params("projectId", projectId).get());
        return (LinkedTreeMap<String, Object>) askResult.get("content");
    }

    /**
     * 获取项目评论信息
     *
     * @param commentSearchModel
     * @return
     */
    public String CommentHandler(CommentSearchModel commentSearchModel) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.PROJECT_COMMENT_INFO, Utils.transBean2Map(commentSearchModel));
        Gson gson = new Gson();
        String str = gson.toJson(result);
        return str;
    }

    /**
     * 获取项目评论信息
     *
     * @param projectId
     * @return
     */
    public void getCommentList(String baseUrl,String params,Model model,String projectId,String pageSize) {
        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.PROJECT_COMMENT_INFO,model,baseUrl,params);
        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.PROJECT_COMMENT_INFO);
        paramMap.put("pageSize", pageSize);
        paramMap.put("projectId",projectId);
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.PROJECT_COMMENT_INFO,paramMap);
        Integer msg = 0;
        //将筛选后的数据存在model中
        if (result.get("content") != null) {
            ArrayList<LinkedTreeMap<String, Object>> comments = (ArrayList<LinkedTreeMap<String, Object>>) result.get("content");
            for (LinkedTreeMap<String, Object> map : comments) {
                if (!StringUtils.isEmpty(map.get("score"))) {
                    Double score = Double.parseDouble(map.get("score").toString());
                    map.put("score",Math.round(score/5));
                }
            }
            model.addAttribute("comments",comments);
            msg = Integer.valueOf((String) result.get("msg"));
        }
        HashMap<String, Object> scoreMap = HttpUtil.connectApi(Constants.PROJECT_COMMENT_SCORE,new Params("projectId",projectId).get());
        if (scoreMap.get("msg").equals("success")) {
            LinkedTreeMap content = (LinkedTreeMap) scoreMap.get("content");
            model.addAttribute("state","1");
            model.addAttribute("totalScore",content);
            model.addAttribute("starTotalScore",Math.round(Double.parseDouble(content.get("score").toString())));
        }else {
            model.addAttribute("state","2");
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl + params, msg);
    }


    /**
     * 获取项目评论信息
     *
     * @param agentId
     * @return
     */
    public String getAgentInfo(String agentId) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.AGENTINFO_TOASK,new Params().add("memberId",agentId).get());
        LinkedTreeMap<String, Object> content = (LinkedTreeMap<String, Object>) result.get("content");
        if (content.get("askCount") == null) {
            content.put("askCount","0");
        }
        Gson gson = new Gson();
        String str = gson.toJson(result);
        return str;
    }

    /**
     * 获取项目咨询信息
     *
     * @param
     * @return
     */
    public void AskHandler(String projectId, String projectType,Integer page,Model model,Integer pageSize) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.PROJECT_ASK_INFO, new Params().add("projectId",projectId).add("projectType",projectType).add("page",page).add("pageSize",pageSize).get());
        HttpUtil.handleServiceList(result, "msg", "askList", model);
        new MyPageable(page, pageSize).putAttribute(model, "/house/ask/"+projectId+"-"+projectType , Integer.valueOf((String) result.get("msg")));
    }

    /**
     * 获取精装房简介
     *
     * @param projectId
     * @param model
     */
    public void viewDecoration(String projectId, Model model) {
        model.addAttribute("projectId", projectId);
        HashMap<String, Object> params = new Params("projectId", projectId).get();
        HttpUtil.connectApi(Constants.VIEW_DECORATE_BRIEF, params, model, "brief");
        HttpUtil.connectApi(Constants.VIEW_DECORATE_BRIEFALBUM, params, model, "album");
        HttpUtil.connectApi(Constants.VIEW_DECORATE_HOUSES, params, model, "layouts");
    }

    public void viewDecoLayout(String layoutId, Model model) {
        HashMap<String, Object> params = new Params("layId", layoutId).get();
        HttpUtil.connectApi(Constants.VIEW_DECORATE_ALBUM, params, model, "detail");
    }

    /**
     * 楼盘说说
     */
    public void getSay(String baseUrl, String projectId, String projectType,Integer page, Model model) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.GET_NEWS_LIST, new Params().add("houseId", projectId).add("page", page).get());
        HttpUtil.handleServiceList(result, "msg", "news", model);
        new MyPageable(page, 10).putAttribute(model, baseUrl + projectId + "-" + projectType, Integer.valueOf((String) result.get("msg")));
    }

    /**
     * 户型图
     */
    public void getRoomType(String baseUrl, String params, Model model) {
        HashMap<String, String> resultMap = ParamsUtil.analysisInput(params, Constants.HOUSE_LAYOUT_INFO);
        model.addAttribute("projectId", resultMap.get("projectId"));
        model.addAttribute("projectType", resultMap.get("projectType"));
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/nphotos/"+resultMap.get("projectId")+"-wxt-"+resultMap.get("projectType")+".htm");
        ParamsUtil.addFilterIntoModel(Constants.HOUSE_LAYOUT_INFO, model, baseUrl, params, resultMap);
        String layId = resultMap.get("layId");
        resultMap.remove("layId");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.HOUSE_LAYOUT_INFO, resultMap);
        List<LinkedTreeMap<String, Object>> content = (List<LinkedTreeMap<String, Object>>) result.get("content");
        if (StringUtils.isEmpty(layId)) {
            layId = content.size() > 0 ? (String) content.get(0).get("layId") : "";
        }
        for (LinkedTreeMap<String, Object> item : content) {
            if(!StringUtils.isEmpty(item.get("price"))){
                Double total = Integer.valueOf((String) item.get("price")) * Double.valueOf(item.get("trueArea").toString()) / 10000;
                item.put("totalPrice", total.intValue());
            }
        }
        model.addAttribute("baseUrl", (baseUrl + params).replace("-l" + layId, ""));
        model.addAttribute("layId", layId);
        for(LinkedTreeMap<String, Object> one:content){
            if(one.get("layId").equals(layId)){
                model.addAttribute("layoutNow",one);
            }
        }
        model.addAttribute("layouts", content);
    }
    /**
     * 在线选房
     */
    public void getOnlineInfo(String baseUrl, String params, Model model) {
        HashMap<String, String> resultMap = ParamsUtil.analysisInput(params, Constants.GET_ONLINE_INFO);
        model.addAttribute("projectId", resultMap.get("projectId"));
        model.addAttribute("projectType", resultMap.get("projectType"));
        if("1".equals(resultMap.get("projectType"))) {
            viewHouseInfo(resultMap.get("projectId"), model);
        } else if("3".equals(resultMap.get("projectType"))) {
            viewApartmentInfo(resultMap.get("projectId"), model);
        }
        ParamsUtil.addFilterIntoModel(Constants.GET_ONLINE_INFO, model, baseUrl, params, resultMap);
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_ONLINE_INFO, resultMap);
        HttpUtil.handleServiceList(result, "msg", "onlineinfo", model);
        new MyPageable(resultMap, 10).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }

    /**
     * 在线选房详情页
     */
    public void getRoomDetail(String projectId, String projectType, String roomId, Model model) {
        HttpUtil.connectApi(Constants.GET_ONLINEROOM_DETAIL, new Params("roomId", roomId).get(), model, "roominfo");
        HashMap<String, Object> params = new Params("projectId", projectId).get();
        HttpUtil.connectApi(Constants.HOUSE_LAYOUT_INFO, params, model, "layoutDetail");
    }

    /**
     * 相册
     */
    public void getPhotos(String photoType, String projectId, String projectType, Model model) {
        HashMap<String, Object> params = new Params("projectId", projectId).get();
        if("3".equals(projectType)) {
            viewApartmentInfo(projectId, model);
        } else if("1".equals(projectType)) {
            viewHouseInfo(projectId, model);
        } else if("2".equals(projectType)) {
            viewVillaInfo(projectId, model);
        }
        HttpUtil.connectApi(Constants.VIEW_ALBUM_COLUMN, params, model, "albumColumn");
        params.put("photoType", photoType);
        HttpUtil.connectApi(Constants.VIEW_PROJECT_ALBUM, params, model, "projectAlbum");
    }

    /**
     * 公寓详情页
     */
    public void viewApartmentDetail(String projectId, Model model) {
        viewApartmentInfo(projectId, model);
        HttpUtil.connectApi(Constants.PROJECT_SALE_INFO, new Params("projectId", projectId).get(), model, "saleInfo");
        HttpUtil.connectApi(Constants.HOUSE_SALE_INFO, new Params("projectId", projectId).get(), model, "saleHouse");
        //项目咨询
        AskHandler(projectId,"3",1,model,3);
        //项目评价
        getIndexCommentList(model,projectId,"3", "1");
    }

    public void viewApartmentInfo(String projectId, Model model) {
        HttpUtil.connectApi(Constants.GET_APARTMENT_DETAIL, new Params("projectId", projectId).get(), model, "houseInfo");
        if(model.asMap().get("houseInfo") == null) NoDataException.throwException();
    }


    /**
     * 别墅详情页
     *
     * @param projectId
     * @param model
     */
    public void viewVillaDetail(String projectId, Model model){
        viewVillaDetail(projectId,model,"1");
    }


    public void viewVillaDetail(String projectId, Model model, String page) {
        viewVillaInfo(projectId, model);
        HashMap<String, Object> params =  new Params("projectId", projectId).get();
        HttpUtil.connectApi(Constants.PROJECT_SALE_INFO, params, model, "saleInfo");
        params.put("photoType", "xgt");
        HttpUtil.connectApi(Constants.GET_PHOTO_INFO, params, model, "photosInfo");
       //项目评价
        getIndexCommentList(model,projectId,"5",page);
        Integer commentcount = Integer.parseInt(model.asMap().get("commentcount") + "");
        new MyPageable(Integer.parseInt(page), 5).putAttribute(model, "", commentcount);
        model.addAttribute("pageUrl","/house/"+projectId + "-2/index");
        model.addAttribute("tab","#xmpj");

    }

    /**
     * 别墅基本信息页
     *
     * @param projectId
     * @param model
     */
    public void viewVillaInfo(String projectId, Model model) {
        HashMap<String, Object> params =  new Params("projectId", projectId).get();
        HttpUtil.connectApi(Constants.VIEW_VILLA_DETAIL, params, model, "houseInfo");
        if(model.asMap().get("houseInfo") == null) NoDataException.throwException();
    }
    /**
     * 别墅相册页
     *
     * @param projectId
     * @param model
     */
    public void viewVillaPhotos(String projectId, Model model) {
        HttpUtil.connectApi(Constants.GET_PHOTO_INFO, new Params("projectId", projectId).get(), model, "villaPic");
    }

    /**
     * 别墅销售动态页
     * @param model
     */
    public void viewProjectNews(String projectId, String projectType, Integer page, String baseUrl, Model model) {
        Params params = new Params().add("projectId", projectId).add("page", page);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.PROJECT_SALE_INFO, params.get());
        HttpUtil.handleServiceList(result, "msg", "lastSaleInfo", model);
        new MyPageable(page, 10).putAttribute(model, baseUrl + projectId + '-' + projectType, Integer.valueOf((String) result.get("msg")));
    }

    /**
     * 别墅详细页轮播
     *
     * @param projectId
     * @param layId
     * @param model
     */
    public void villaSubLayout(String projectId, String layId, Model model) {
        List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.GET_SUB_LAYOUT,
                new Params("projectId", projectId).add("layId", layId).get(), model);
        model.addAttribute("layout", result);
    }

    /**
     * 保存用户评论
     *
     * @param hashMap
     */
    @Transactional
    public HashMap saveComment(HashMap hashMap) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.ADD_COMMENT, hashMap);
        return result;
    }
    /**
     * 保存用户评论
     *
     * @param hashMap
     */
    @Transactional
    public HashMap saveReply(HashMap hashMap) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.ADD_REPLY, hashMap);
        return result;
    }

    /**
     * 查询
     * @param commentId
     * @return
     */
    public void viewCommentInfo(Integer commentId,Model model) {
        HashMap result =   HttpUtil.connectApi(Constants.VIEW_COMMENT_DETAIL, new Params("commentId",commentId).get());
        if(result == null || result.get("content") == null) NoDataException.throwException();
        if(Double.valueOf(result.get("status").toString()).intValue() ==1 ){
            LinkedTreeMap<String,Object> commentInfo = (LinkedTreeMap<String,Object>)result.get("content");
            model.addAttribute("projectId",  commentInfo.get("projectId"));
            model.addAttribute("commentInfo",commentInfo);
            model.addAttribute("commentId",commentId);
        }
        HashMap pics =   this.viewCommontPhotos(commentId);
        if(Double.valueOf(pics.get("status").toString()).intValue() ==1 ){
            int num = 0;
            StringBuffer picurl = new StringBuffer();
            ArrayList<LinkedTreeMap<String,Object>> pic = (ArrayList<LinkedTreeMap<String,Object>>) pics.get("content");
            for(Map<String, Object> map : pic){
                picurl.append(map.get("url"));
                if(num < pic.size() - 1){
                    picurl.append(",");
                }
                num ++;
            }
            picurl.toString();
            model.addAttribute("imgurl",picurl);
        }
    }

    /**
     * 查询评论相册
     * @param commentId
     * @return
     */
    public HashMap viewCommontPhotos(Integer commentId) {
       return HttpUtil.connectApi(Constants.VIEW_COMMENT_PICS, new Params("commentId",commentId).get());
    }

    /**
     * 抢优惠
     */
    public HashMap orderactivity(String sessionId, String userName, String activityId, String code, HttpServletResponse response) {
        CookieManage.addCookie(response, "aspxUserTicket", sessionId, "/", ".fangxiaoer.com");
        return HttpUtil.connectApi(Constants.ORDER_ACTIVITY, new Params("sessionId",sessionId).add("userName",userName).add("activityId",activityId).add("code",code).get());
    }
    /**
     * 销售动态页（旧）
     * @param model
     */
    public void viewProjectSaleInfo(String projectId, String projectType, Integer page, String baseUrl, Model model) {
        Params params = new Params().add("projectId", projectId).add("page", page);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.PROJECT_SALE_DYNAMICS, params.get());
        HttpUtil.handleServiceList(result, "msg", "newSaleInfo", model);
        new MyPageable(page, 10).putAttribute(model, baseUrl + projectId + '-' + projectType, Integer.valueOf((String) result.get("msg")));
    }

    /**
     * 新版项目动态页
     * @param projectId
     * @param projectType
     * @param page
     * @param dyType
     * @param baseUrl
     * @param model
     */
    public void viewProjectSaleInfo(String projectId, String projectType, Integer page, String dyType, String baseUrl, Model model,Integer pageSize) {
        Params params = new Params().add("projectId", projectId).add("page", page).add("dyType", dyType);
        HashMap<String, Object> result = getDyForProject(projectId,dyType,page,pageSize);
        HashMap<String, Object> dyTypeFilter = getDyForProjectFilter(projectId);
        model.addAttribute("saleInfoNew",result.get("content"));
        model.addAttribute("dyType",dyType);
        model.addAttribute("dyTypeFilter",dyTypeFilter.get("content"));
        new MyPageable(page, 10).putAttribute(model, baseUrl + projectId + '-' + projectType + "/news/dy" + (StringUtils.isEmpty(dyType)?"":dyType), Integer.valueOf((String)(StringUtils.isEmpty(result.get("msg"))?"0":result.get("msg"))));
    }
    /**
     * 获取新版动态 dyType为空 不限，1销售动态，2 交房，3预售证
     *
     * @param projectId
     * @param dyType
     * @param page
     * @param pageSize
     * @return
     */
    public HashMap<String, Object> getDyForProject(String projectId, String dyType, Integer page, Integer pageSize) {
        return HttpUtil.connectApi(Constants.VIEW_PROJECT_NEWS, new Params("projectId", projectId).add("dyType", dyType).add("page", page).add("pageSize", pageSize).get());
    }
    /**
     * 获取新版项目动态的全部 dyType类别
     *
     * @param projectId
     * @return
     */
    public HashMap<String, Object> getDyForProjectFilter(String projectId) {
        return HttpUtil.connectApi(Constants.VIEW_PROJECT_NEWS_FILTER, new Params("projectId", projectId).get());
    }
    /**
     * 获取项目咨询信息
     *
     * @param askSearchModel
     * @return
     */
    public String AskHandlerOld(AskSearchModel askSearchModel) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.PROJECT_ASK_INFO, Utils.transBean2Map(askSearchModel));
        Gson gson = new Gson();
        String str = gson.toJson(result);
        return str;
    }
    /**
     * 首页项目评价
     * @param projectId
     * @return
     * <AUTHOR>
     */
    public void getIndexCommentList(Model model,String projectId,String pageSize, String page) {
        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("pageSize", pageSize);
        paramMap.put("projectId",projectId);
        paramMap.put("page",page);
        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.PROJECT_COMMENT_INFO,paramMap);
        Integer msg = 0;
        //将筛选后的数据存在model中
        if (result.get("content") != null) {
            msg = Integer.parseInt(result.get("msg") + "");
            ArrayList<LinkedTreeMap<String, Object>> comments = (ArrayList<LinkedTreeMap<String, Object>>) result.get("content");
            for (LinkedTreeMap<String, Object> map : comments) {
                if (!StringUtils.isEmpty(map.get("score"))) {
                    Double score = Double.parseDouble(map.get("score").toString());
                    map.put("score",Math.round(score/5));
                }
            }
            model.addAttribute("comments",comments);
        }
        model.addAttribute("commentcount", msg);
        HashMap<String, Object> scoreMap = HttpUtil.connectApi(Constants.PROJECT_COMMENT_SCORE,new Params("projectId",projectId).get());
        if (scoreMap.get("msg").equals("success")) {
            LinkedTreeMap content = (LinkedTreeMap) scoreMap.get("content");
            model.addAttribute("state","1");
            model.addAttribute("totalScore",content);
            model.addAttribute("starTotalScore",Math.round(Double.parseDouble(content.get("score").toString())));
        }else {
            model.addAttribute("state","2");
        }
    }

    public void viewDealRankingList(Model model) {
        HashMap<String, Object> monthList = HttpUtil.connectApi(Constants.VIEW_DEAL_RANKING_LIST,new Params("houseType",1).add("dateType",3).get());
        HashMap<String, Object> quarterList = HttpUtil.connectApi(Constants.VIEW_DEAL_RANKING_LIST,new Params("houseType",1).add("dateType",4).get());
        HashMap<String, Object> yearList = HttpUtil.connectApi(Constants.VIEW_DEAL_RANKING_LIST,new Params("houseType",1).add("dateType",5).get());
        HttpUtil.handleServiceList(monthList, "msg", "monthList", model);
        HttpUtil.handleServiceList(quarterList, "msg", "quarterList", model);
        HttpUtil.handleServiceList(yearList, "msg", "yearList", model);
    }


    public HashMap<String,Object> fetchNearByProject(Integer distance, BigDecimal longitude, BigDecimal latitude, Integer firstId){
        return HttpUtil.connectApi(Constants.VIEW_NEWPROJECT_MAP, new Params("distance",distance).add("longitude",longitude).add("latitude",latitude).add("firstId",firstId).get());
    }
    //查询楼盘户型类型
    public void viewLayoutType(String projectId,String projectType, Model model) {
        HashMap params = new HashMap();
        params.put("projectId",projectId);
        params.put("projectType",projectType);
        HttpUtil.connectApi(Constants.GET_LAYOUT_TYPE, params, model, "layoutType");
    }
    /**
     * 获取房源相关推荐
     * @param projectId
     */
    public HashMap<String, Object> getRelasionHousePush(String projectId, BigDecimal latitude, BigDecimal longitude) {
        return HttpUtil.connectApi(Constants.VIEW_RELANTION_HOUSE,new Params().add("projectId",projectId).add("latitude",latitude).add("longitude",longitude).get());
    }

    /**
     * 获取地铁列表右侧广告
     * @param model
     */
    public void getMetroAds(Model model) {
        model.addAttribute("metroRight",HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type","8").get(),true).get("content"));
        // 小二精选数据
        HttpUtil.connectApi(Constants.GET_RANK_LIST_BYTYPE, new Params("rankType", 1).add("page", "1").add("pageSize", "10").get(), model, "rankList");
    }
    /**
     * 新房列表页房产排行榜
     * @param params
     * <AUTHOR> 2019-3-5
     */
    public void getRankListByType(HashMap<String, String> params,Model model) {
        HashMap result = HttpUtil.connectApiString(Constants.GET_RANK_LIST_BYTYPE, params);
        HttpUtil.connectApi(Constants.GET_RANK_TYPE, new Params().get(),model,"rankType");
        if("1.0".equals(result.get("status").toString())){
            List<Map> list = (List<Map>)result.get("content");
            model.addAttribute("rankList",list);
            Object[] topList = new Object[3];
            for(int i = 0 ;i<list.size();i++){
                if(i == 0){
                    topList[1] = list.get(i);
                }else if(i == 1){
                    topList[0] = list.get(i);
                }else if(i == 2){
                    topList[2] = list.get(i);
                }
            }
            model.addAttribute("topList",topList);
        }else {
            model.addAttribute("rankList","");
            model.addAttribute("topList","");
        }
    }

    /**
     * 排行榜情况
     * @param type
     * @param regionId
     * @param model
     */
    public void viewRankListByType(Integer type, Integer regionId, Model model){
        HttpUtil.connectApi(Constants.GET_RANK_LIST_BYTYPE, new Params("rankType",type).add("page","1").add("pageSize","10").add("regionId", regionId).get(), model,"rankList");
        HttpUtil.connectApi(Constants.VIEW_RANK_REGION, new Params().get(), model,"regionFilter");
//        HttpUtil.connectApi(Constants.VIEW_MINOR_RANK, new Params().get(), model,"minorRank");
        HashMap<String, String> paramMap = new HashMap<>();
        paramMap.put("pageSize","10");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_PROJECT_LIST, paramMap);
        model.addAttribute("projects", result.get("content"));
        HttpUtil.connectApi(Constants.VIEW_RANK_FILTER, new Params().get(), model,"rankType");
    }

    public Integer viewProjectByType(Integer projectId){
        HashMap result = HttpUtil.connectApi(Constants.GET_PROJECT_TYPE, new Params("projectId", projectId).get());
        if(result == null || result.get("content") == null){
            return 0;
        }else{
            return Integer.parseInt(result.get("content") + "");
        }
    }

    /**
     * 旅居地产首页广告
     * @param type
     * @param model
     * @return
     * <AUTHOR> 2019-5-28
     */
    public void viewIndexAD(Integer type, Model model, String systemDate) {
        List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.TOURIST_INDEX_AD,
                new Params("advType", type).add("systemDate", systemDate).get(), model);
        if (result == null || result.size() == 0) {
            model.addAttribute("ad" + type, null);
        }
        model.addAttribute("ad" + type, result);
    }

    /**
     * 旅居地产房源列表
     * @param cityName
     * @param page
     * @param pageSize
     * @param model
     * @return
     * <AUTHOR> 2019-5-28
     */
    public void touristProjectList(String cityName, Integer page, Integer pageSize, Model model) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.TOURIST_INDEX, new Params("cityName", cityName).add("page", page).add("pageSize", pageSize).get());
        HttpUtil.handleServiceList(result, "msg","projectList", model);
        new MyPageable(page, pageSize).putAttribute(model, "/touristList/", Integer.valueOf((String) result.get("msg")));
    }

	/**
	 * 旅居地产首页下部资讯
	 * @param model
	 */
	public void touristNewsList(Model model) {
        HashMap result = HttpUtil.connectApi(Constants.TOURIST_INDEX_NEWS, new Params().get());
        if("1.0".equals(result.get("status").toString())){
            model.addAttribute("newsList", result.get("content"));
        }else {
            model.addAttribute("newsList", null);
        }
    }

    public void viewTNewsList(Integer newsType, Integer page, Integer pageSize,  Model model) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.TOURIST_NEWS_LIST, new Params("newsType", newsType).add("page", page).add("pageSize", pageSize).get());
        HttpUtil.handleServiceList(result, "msg","news", model);
        new MyPageable(page, pageSize).putAttribute(model, "/tNews" + newsType + "/", Integer.valueOf((String) result.get("msg")));
    }

    public void recommendProjects(Model model, String systemDate){
        model.addAttribute("recommendAdProjects",HttpUtil.connectApi(Constants.GUESS_LIKE, new Params("locationId","586").add("systemDate", systemDate).get(),true).get("content"));//好房推荐

    }

    public List<LinkedTreeMap<String, Object>> viewNewsFilter(Model model) {
        List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.TOURIST_NEWS_FILTER,
                new Params().get(), model);
        if (result == null || result.size() == 0) {
            model.addAttribute("newsFilter", null);
            return null;
        }
        model.addAttribute("newsFilter", result);
        return result;
    }

    public HashMap<String, Object> touristGuideCount() {
        return HttpUtil.connectApi(Constants.TOURIST_ORDER_COUNT, new Params().get());
    }

    public LinkedTreeMap<String, Object> viewTouristProject(Integer projectId, Model model) {
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.TOURIST_PROJECT_DETAIL,
                new Params("projectId", projectId).add("tab", "1").get(), model);
        List<LinkedTreeMap<String, Object>> layOut1 = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.TOURIST_LAY1_FILTER,
                new Params("projectId", projectId).get(), model);
        List<LinkedTreeMap<String, Object>> layOut2 = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.TOURIST_LAY2_FILTER,
                new Params("projectId", projectId).get(), model);
        model.addAttribute("layFilter1", layOut1);
        model.addAttribute("layFilter2", layOut2);
        if (result == null || result.size() == 0) {
            model.addAttribute("project", null);
            return null;
        }
        List<LinkedTreeMap<String, Object>> layout = (List<LinkedTreeMap<String, Object>>) result.get("layouts");
        List<LinkedTreeMap<String, Object>> layout2 = new LinkedList<>();
        for (LinkedTreeMap<String, Object> hashMap : layout) {
            Double price = Double.parseDouble((String) hashMap.get("price"));
            Double buildArea = Double.parseDouble((String) hashMap.get("buildArea"));
            Integer totalPrice = new Double(price * buildArea / 10000).intValue();
            hashMap.put("totalPrice", totalPrice);
            layout2.add(hashMap);
        }
        result.put("layout", layout2);
        model.addAttribute("project", result);
        return result;

    }

    public String viewNewsDetail(Integer newsId, Model model) {
        LinkedTreeMap<String, Object> newsinfo = (LinkedTreeMap) HttpUtil.connectApi(Constants.TOURIST_NEWS_DETAIL,
                new Params("newsId", newsId).get(), model);
        if (newsinfo == null || newsinfo.size() == 0) {
            return null;
        }
        Gson gson = new Gson();
        TNewsDetail newsDetail = gson.fromJson(gson.toJson(newsinfo), TNewsDetail.class);
        if (!StringUtils.isEmpty(newsDetail.getmUrl())) {
            return "https://" + newsDetail.getmUrl();
        }
        newsDetail.setNewsContent(newsDetail.getNewsContent().replaceAll("&lt;", "<").replaceAll("&nbsp;", " ")
                .replaceAll("&quot;", "\"").replaceAll("&gt;", ">"));
        String regEx_img = "<img.*src\\s*=\\s*(.*?)[^>]*?>";
        Pattern p_image = Pattern.compile(regEx_img, Pattern.CASE_INSENSITIVE);
        Matcher m_image = p_image.matcher(newsDetail.getNewsContent());
        List<String> pics = new ArrayList<>();
        if (m_image.find()) {
            String img = m_image.group();
            Matcher m = Pattern.compile("src\\s*=\\s*\"?(.*?)(\"|>|\\s+)").matcher(img);
            while (m.find()) {
                pics.add(m.group(1) + "");
            }
        }
        if (pics.size() > 3) {
            model.addAttribute("baidu", 3);
            model.addAttribute("pic1", pics.get(0));
            model.addAttribute("pic2", pics.get(1));
            model.addAttribute("pic3", pics.get(2));
        } else if (pics.size() > 0) {
            model.addAttribute("baidu", 1);
            model.addAttribute("pic", pics.get(0));
        }
        model.addAttribute("newsinfo", newsDetail);
        String updateTime = newsDetail.getUpdateTime().replaceAll("\\.", "-");
        String part1 = updateTime.substring(0, 10);
        String part2 = updateTime.substring(11);
        model.addAttribute("updateTime", part1 + "T" + part2);
        Integer projectId = newsDetail.getProjectId();
        if(projectId == null){
            model.addAttribute("tab", "0");
        }else{
            List<LinkedTreeMap<String, Object>> result =
                    (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.TOURIST_INDEX, new Params("projectId", projectId).add("page", 1).add("pageSize", 1).get(), model);
            if (result == null || result.size() == 0) {
                model.addAttribute("tab", "0");
            }else{
                model.addAttribute("tab", "1");
                model.addAttribute("projectList", result);
            }
        }
        return "normal";
    }

    public LinkedTreeMap<String, Object> viewProjectInfo(Integer projectId, Model model) {
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.TOURIST_PROJECT_INFO,
                new Params("projectId", projectId).get(), model);
        if (result == null || result.size() == 0) {
            model.addAttribute("detail", null);
            return null;
        }
        model.addAttribute("detail", result);
        return result;
    }

    public void projectBrief(Integer projectId, Model model){
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.TOURIST_PROJECT_BRIEF,
                new Params("projectId", projectId).get(), model);
        model.addAttribute("brief", result);
    }

    public void viewAskList(Integer projectId, Integer page, Integer pageSize,  Model model) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.TOURIST_ASK_LIST, new Params("projectId", projectId).add("page", page).add("pageSize", pageSize).get());
        HttpUtil.handleServiceList(result, "askCount","asks", model);
        new MyPageable(page, pageSize).putAttribute(model, "/tourist/" + projectId + "/ask/" , Integer.valueOf((String) result.get("msg")));
    }

    public void viewCommentList(Integer projectId, Integer page, Integer pageSize,  Model model) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.TOURIST_COMMENT_LIST, new Params("projectId", projectId).add("page", page).add("pageSize", pageSize).get());
        HttpUtil.handleServiceList(result, "commentCount","comments", model);
        new MyPageable(page, pageSize).putAttribute(model, "/tourist/" + projectId + "/comment/" , Integer.valueOf((String) result.get("msg")));
    }

    public void viewProjectDy(Integer projectId, Integer page, Integer pageSize, Integer dyType, Model model) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.TOURIST_PROJECT_DY, new Params("projectId", projectId).add("page", page).add("pageSize", pageSize).add("infoType", dyType).get());
        HttpUtil.handleServiceList(result, "msg","detail", model);
        if(StringUtils.isEmpty(dyType)){
            new MyPageable(page, pageSize).putAttribute(model, "/tourist/" + projectId + "/dy/" , Integer.valueOf((String) result.get("msg")));
        }else{
            new MyPageable(page, pageSize).putAttribute(model, "/tourist/" + projectId + "/dy-" + dyType + "/" , Integer.valueOf((String) result.get("msg")));
        }
    }

    public List<LinkedTreeMap<String, Object>> viewDYFilter(Integer projectId, Model model) {
        List<LinkedTreeMap<String, Object>> result = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.TOURIST_PROJECT_DY_FILTER,
                new Params("projectId", projectId).get(), model);
        if (result == null || result.size() == 0) {
            model.addAttribute("dyFilter", null);
            return null;
        }
        model.addAttribute("dyFilter", result);
        return result;
    }

    public boolean getLayoutDetail(String baseUrl, String params, Model model) {
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.TOURIST_LAYOUT_DETAIL);
        ParamsUtil.addFilterIntoModel(Constants.TOURIST_LAYOUT_DETAIL, model, baseUrl, params, paramMap);
        HashMap<String, Object> param = new HashMap<>();
        param.putAll(paramMap);
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.TOURIST_LAYOUT_DETAIL, param, model);
        if (result == null || result.size() == 0) {
            model.addAttribute("detail", null);
        } else {
            model.addAttribute("detail", result);
        }
        List<LinkedTreeMap<String, Object>> layFilter = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.TOURIST_LAYDETAIL_FILTER,
                param, model);
        model.addAttribute("layFilter", layFilter);
        String url = (String) model.asMap().get("link");
        if (url.contains("-l")) {
            String url0 = url.substring(0, url.lastIndexOf("-l"));
            model.addAttribute("link", url0);
        } else {
            model.addAttribute("link", url);
        }
        model.addAttribute("baseUrl", baseUrl.replace("/", ""));
        return true;
    }

    public void getTouristPhotos(String photoType, Integer projectId, Model model) {
        HashMap<String, Object> params = new Params("projectId", projectId).get();
        HttpUtil.connectApi(Constants.TOURIST_PHOTOS_FILTER, params, model, "albumColumn");
        params.put("photoType", photoType);
        HttpUtil.connectApi(Constants.TOURIST_PHOTOS_DETAIL, params, model, "projectAlbum");
    }

    public void viewTouristCommentInfo(Integer commentId, Model model){
        HashMap result =   HttpUtil.connectApi(Constants.TOURIST_COMMENT_DETAIL, new Params("commentId",commentId).get());
        if(result == null || result.get("content") == null) NoDataException.throwException();
        if(Double.valueOf(result.get("status").toString()).intValue() ==1 ){
            LinkedTreeMap<String,Object> commentInfo = (LinkedTreeMap<String,Object>)result.get("content");
            model.addAttribute("commentInfo",commentInfo);
            model.addAttribute("projectId",commentInfo.get("projectId"));
            model.addAttribute("commentId",commentId);
        }
        HashMap pics =   HttpUtil.connectApi(Constants.TOURIST_COMMENT_PICS, new Params("commentId",commentId).get());
        if(Double.valueOf(pics.get("status").toString()).intValue() ==1 ){
            int num = 0;
            StringBuffer picurl = new StringBuffer();
            ArrayList<LinkedTreeMap<String,Object>> pic = (ArrayList<LinkedTreeMap<String,Object>>) pics.get("content");
            for(Map<String, Object> map : pic){
                picurl.append(map.get("imageUrl"));
                if(num < pic.size() - 1){
                    picurl.append(",");
                }
                num ++;
            }
            picurl.toString();
            model.addAttribute("imgurl",picurl);
        }
    }

    public HashMap saveTouristComment(HashMap hashMap) {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.ADD_TOURIST_COMMENT, hashMap);
        return result;
    }

    public HashMap<String, Object> getTouristFilter() {
        return HttpUtil.connectApi(Constants.TOURIST_CITY, new Params().get());
    }

    public HashMap<String, Object> addOrder(TouristOrder touristOrder) {
        HashMap<String, Object> result = Utils.transBean2Map(touristOrder);
        return HttpUtil.connectApi(Constants.TOURIST_ADD_ORDER, result);
    }

    public HashMap<String, Object> touristProjectList(String cityName, Integer page, Integer pageSize) {
        return HttpUtil.connectApi(Constants.TOURIST_INDEX, new Params("cityName", cityName).add("page", page).add("pageSize", pageSize).get());
    }

    public HashMap<String, Object> addAsk(String sessionId, Integer projectId, String askInfo) {
        return HttpUtil.connectApi(Constants.ADD_TOURIST_ASK, new Params("sessionId", sessionId).add("projectId", projectId).add("askInfo", askInfo).get());
    }

    public void usuallySearchProject(Model model){
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.USUALLY_SEARCH_PROJECT, new Params("rankType", 1).get());
        model.addAttribute("ussearch", result.get("content"));
	}
    public String getWxACode( String scene, String page, Integer width){
	    String img="";
        HashMap result = HttpUtil.connectApi(Constants.GET_WX_A_CODE, new Params("scene", scene).add("page",page).add("width",width).get());
        if(result != null &&result.get("content") != null){
            img=String.valueOf(result.get("content"));
        }
        return img;
    }

    public String getWxSecCode( String scene, String page, Integer width){
        String img="";
        HashMap result = HttpUtil.connectApi(Constants.GET_WX_SEC_CODE, new Params("scene", scene).add("page",page).add("width",width).get());
        if(result != null &&result.get("content") != null){
            img=String.valueOf(result.get("content"));
        }
        return img;
    }


    public String getNewWxCode( String scene, String page, Integer width){
        String img="";
        HashMap result = HttpUtil.connectApi(Constants.GET_WX_NEW_CODE, new Params("scene", scene).add("page",page).add("width",width).get());
        if(result != null &&result.get("content") != null){
            img=String.valueOf(result.get("content"));
        }
        return img;
    }



    public void checkBuildType(String projectId, Model model){
        HttpUtil.connectApi(Constants.NEWHOUSE_GET_BUILDTYPE, new Params("projectId",projectId).get(), model, "saleBuildType");
    }


    public HashMap<String, Object> getAdvertisementList(String systemDate, String type){
        if (systemDate != null) {
            return HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("systemDate", systemDate).add("type", type).get(), true);
        } else {
            return HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type", type).get(), true);
        }
    }

    /**
     * 新房->进行户型对比
     * @param layId
     * @param sessionId
     * @return
     */
    public HashMap doContrastLayout(Integer layId,String sessionId){
        HashMap<String, Object> hashMap = HttpUtil.connectApi(Constants.LAYOUT_CONTRAST, new Params("layId", layId).add("sessionId",sessionId).get());
        Object concent = hashMap.get("content");
        HashMap results = JSONObject.parseObject(JSONObject.toJSONString(concent), HashMap.class);
        /*
        处理室、厅、卫以及参考价,便于前台页面展示
         */
        results.put("roomType",results.get("roomType")+"室");
        results.put("hallType",results.get("hallType")+"厅");
        results.put("guardType",results.get("guardType")+"卫");
        Double price = Double.parseDouble(results.get("price").toString());
        Double trueArea = Double.parseDouble(results.get("trueArea").toString());
        DecimalFormat df = new DecimalFormat(".##");
        results.put("buildArea",results.get("buildArea")+"㎡");
        String referencePrice = price == 0.0 || StringUtils.isEmpty(price) ? "待定" : df.format(price* trueArea/10000.0)+"万";
        results.put("referencePrice","参考价："+referencePrice);
        hashMap.put("content",results);
        return hashMap;
    }

    /**
     * 新房->获得推荐户型
     * @param layId
     * @return
     */
    public HashMap getRecommendLayout(Integer layId){
        return HttpUtil.connectApi(Constants.LAYOUT_CONMMED,new Params("layId",layId).get());
    }

    /**
     * 新房->收藏户型
     * @param layoutId
     * @param sessionId
     * @param method
     * @return
     */
    public HashMap<String, Object> manageCollect(Integer layoutId,String sessionId,String method) {
        if (!StringUtils.isEmpty(sessionId)){
            if("checkFavorite".equals(method)){
                return HttpUtil.connectApi(Constants.CHECK_LAYOUT_FAVORITE,new Params("layoutId",layoutId).add("sessionId",sessionId).get());
            }else if("newFavorite".equals(method)){
                return HttpUtil.connectApi(Constants.ADD_LAYOUT_FAVORITE,new Params("layoutId",layoutId).add("sessionId",sessionId).get());
            }else if("cancelFavorite".equals(method)){
                return HttpUtil.connectApi(Constants.CANCEL_LAYOUT_FAVORITE,new Params("layoutId",layoutId).add("sessionId",sessionId).get());
            }
        }
        return new Params().add("status","0").add("msg","请重新登录!").get();
    }

    public HashMap<String, Object> viewSchoolList(Integer schoolTypeId, Integer regionId, Integer page, Integer pageSize, String except){
        return HttpUtil.connectApi(Constants.SCHOOL_LIST, new Params("schoolTypeId", schoolTypeId).add("regionId", regionId)
                .add("page", page).add("pageSize", pageSize).add("except", except).get());
    }

    public HashMap<String, Object> searchSchoolListForMap(String searchKey,Integer schoolTypeId, Integer regionId, Integer schoolAreaId, Integer page, Integer pageSize,
                                                    Double leftLng, Double leftLat, Double rightLng, Double rightLat){
        return HttpUtil.connectApi(Constants.SEARCH_SCHOOL_LIST, new Params("schoolTypeId", schoolTypeId).add("regionId", regionId).add("searchKey", searchKey).add("schoolAreaId", schoolAreaId)
                .add("leftLng", leftLng).add("leftLat", leftLat).add("rightLng", rightLng).add("rightLat", rightLat)
                .add("page", page).add("pageSize", pageSize).get());
    }


    public HashMap<String, Object> viewNewSchoolHouseList(String searchKey,Integer schoolTypeId, Integer projectType, Integer regionId, Integer schoolAreaId, Integer page, Integer pageSize,
                                                          Double leftLng, Double leftLat, Double rightLng, Double rightLat, Integer projectStatus){
        return HttpUtil.connectApi(Constants.GET_SCHOOL_HOUSE_LIST, new Params("schoolTypeId", schoolTypeId).add("regionId", regionId).add("searchKey", searchKey).add("schoolAreaId", schoolAreaId)
                .add("projectType", projectType).add("leftLng", leftLng).add("leftLat", leftLat).add("rightLng", rightLng).add("rightLat", rightLat).add("projectStatus", projectStatus)
                .add("page", page).add("pageSize", pageSize).get());
    }

    /**
     * 新房详情页 调整轮播图片顺序
     * 全景放1，视频2，其他
     *
     * <AUTHOR> power
     * @date 2022-03-24 14:52
     * @param model Model对象
     */
    public void handleNormalHousePics(Model model) {
        // 详情
        Map houseInfo = (Map) model.asMap().get("houseInfo");
        // 图片集合
        List<Map<String, String>> pics = (List<Map<String, String>>) houseInfo.get("pic");
        for (Map<String, String> pic : pics) {
            if ("全景".equals(pic.get("title"))) {
                pics.remove(pic);
                pics.add(0, pic);
                break;
            }
        }
        LinkedTreeMap liveInfo = (LinkedTreeMap) houseInfo.get("liveInfo");
        String isLive = liveInfo.get("isLive") + "";
        if("1".equalsIgnoreCase(isLive)){
            HashMap<String, String> liveMap = new HashMap<>();
            liveMap.put("title", "直播看房");
            liveMap.put("url", liveInfo.get("livePic") + "");
            pics.add(0, liveMap);
        }
        houseInfo.put("pic", pics);
        model.addAttribute("houseInfo", houseInfo);
    }

    /**
     * 别墅详情页轮播图 全景图与视频图交换位置
     * 全景图排首位，视频排次位，后接普通图片
     *
     * <AUTHOR> power
     * @date 2022-03-24 14:18
     * @param model Model对象
     * @return java.util.HashMap<java.lang.String,java.lang.Object>
     */
    public void handleVillaPicsToMap(Model model) {
        // 详情
        Map houseInfo = (Map) model.asMap().get("houseInfo");
        // 是否有全景
        String isPan = (String) houseInfo.get("isPan");
        // 是否有视频
        String videoTabId = (String) houseInfo.get("videoTabId");
        // 图片集合
        List<Map<String, String>> pics = (List<Map<String, String>>) houseInfo.get("showPics");

        HashMap<String, Object> map = new HashMap<>();
        // 全景
        if (!StringUtils.isEmpty(isPan) && !isPan.equals("0")) {
            for (Map<String, String> pic : pics) {
                if ("全景".equals(pic.get("title"))) {
                    map.put("panPic", pic);
                    // 移除图片里的全景图片
                    pics.remove(pic);
                    break;
                }
            }
        } else {
            map.put("panPic", null);
        }
        // 视频
        if (!StringUtils.isEmpty(videoTabId) && !videoTabId.equals("0") && pics.size() >= 1) {
            // 默认图
            String defaultPic = (String) houseInfo.get("defaultPic");
            map.put("videoPic", defaultPic);
            // 将默认图作为视频封面，并清除图片列表中该图片
            for (Map<String, String> pic : pics) {
                if (pic.get("url").equals(defaultPic)) {
                    pics.remove(pic);
                    break;
                }
            }
        } else {
            map.put("videoPic", null);
        }
        map.put("pics", pics);

        model.addAttribute("handlePics", map);
    }


    public void newProjectDynamics(Model model){
        // 小二精选楼盘
        HttpUtil.connectApi(Constants.GET_RANK_LIST_BYTYPE, new Params("rankType", 1).add("page", "1").add("pageSize", "10").get(), model, "rankList");
        //右侧最新视频
        List<LinkedTreeMap<String, Object>> ltmVideo = (List<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.NEW_VIDEO_LIST, new Params("pageSize", 1).get()).get("content");
        model.addAttribute("video", ltmVideo);
    }

    public void selectionProjectInfo(String projectId, Model model) {
        LinkedTreeMap<String, Object> res =  (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.SELECTION_PROJECT_INFO, new Params("projectId", projectId).get()).get("content");
        model.addAttribute("comparisonId", res.get("comparisonId"));
        model.addAttribute("selectionInfo", res);
    }
}

