package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.UrlBean;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.net.URLDecoder;
import java.util.*;

import static com.fangxiaoer.common.Constants.DEAL_SECOND_LIST;
import static com.fangxiaoer.common.Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT;

/**
 * Created by Administrator on 2017/6/12.
 */

@Service
public class SecondHouseService {
    @Autowired
    PlotService plotService;
    public static final String SEHOUSE_URL = "/saleHouses/";

    public void getSecondHouseList(String baseUrl,String params, Model model){
        keyValueConfig("second_anxuan", model, "forAnxuan");
        String [] input = params.split("-");
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.SECOND_HOUSE_LIST);
        ParamsUtil.addFilterIntoModel(Constants.SECOND_HOUSE_LIST, model, baseUrl, params, paramMap);
        model.addAttribute("searchKey",paramMap.get("subName"));
        ParamsUtil.addSeoTitle(model, "orderKey,vgType");
        String trait = "";
        String seoSubName = "";

        for (int i = 0 ; i < input.length ; i++) {
            //特色
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("u")) {
                trait = trait + input[i].substring(1, input[i].length()) + ",";
                paramMap.put("houseTrait", trait);
            }
            // 小区
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("v")){
                String subId = input[i].substring(1,input[i].length());
                model.addAttribute("subId",subId);
                if(ValidateUtil.isNumber(subId) == false){
                    paramMap.put("subId","0");
                }else{
                    paramMap.put("subId",subId);
                    HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL,new Params("subId",subId).get(), model, "sub");
                    Map<String, Object> mod = model.asMap();
                    if(mod.containsKey("sub")) {
                        seoSubName = (String) ((LinkedTreeMap<String, Object>)mod.get("sub")).get("title");
                        if(seoSubName == null) {
                            seoSubName = "";
                        }
                    }

                }
            }
            model.addAttribute("seoSubName", seoSubName);
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("k")){
                String minPrice = input[i].substring(1,input[i].length());
                paramMap.put("minPrice",minPrice);
                model.addAttribute("minPrice", minPrice);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("x")){
                String maxPrice = input[i].substring(1,input[i].length());
                paramMap.put("maxPrice",maxPrice);
                model.addAttribute("maxPrice", maxPrice);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("y")){
                String minArea = input[i].substring(1,input[i].length());
                paramMap.put("minArea",minArea);
                model.addAttribute("minArea", minArea);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("e")){
                String maxArea = input[i].substring(1,input[i].length());
                paramMap.put("maxArea",maxArea);
                model.addAttribute("maxArea", maxArea);
            }
        }
        if(paramMap.get("isSubway") != null)
            model.addAttribute("isSubway", 1);
        else
            model.addAttribute("isSubway", 0);

        int pageSize = 60;
        if(paramMap.containsKey("piece")) {
            pageSize = 72;
            //这个参数不传入接口
            paramMap.remove("piece");
        }
        paramMap.put("pageSize", ""+pageSize);
        paramMap.put("auction","1");
        //bug修复subWayStationId地铁站内容在查询条件中根本不存在的问题
        if(!StringUtils.isEmpty(paramMap.get("subWayStationId"))){
            paramMap.put("stationId",paramMap.get("subWayStationId"));
        }
        //如果是视频看房查询则不需要竞价查询
        if(!StringUtils.isEmpty(paramMap.get("vgTypeId"))){
            String vgTypeId = (String)paramMap.get("vgTypeId");
            if(vgTypeId.equals("6")){
                paramMap.put("isVeryNew","1");
            }
            if(vgTypeId.equals("4")){
                paramMap.put("auction","1");
            }
            if(vgTypeId.equals("8")){
                paramMap.put("isNewHouse","1");
            }
        }
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.SECOND_HOUSE_LIST, paramMap);
        HttpUtil.handleServiceList(result, "msg", "secondhouse", model);
        int totalPage = 0;
        if(!StringUtils.isEmpty(result.get("msg"))){
            if(Utils.isInteger((String)result.get("msg"))){
                totalPage = Integer.valueOf((String) result.get("msg"));
            }
        }
        new MyPageable(paramMap, pageSize).putAttribute(model, baseUrl + params, totalPage);
        //列表搜索无数据时推荐60条房源(改为推60条2018.09.28)
        List<HashMap<String, Object>> secondList = (List<HashMap<String, Object>>) result.get("content");
        HashMap<String, String> paramCount = ParamsUtil.analysisInput(null, Constants.SECOND_HOUSE_LIST);
        int pageSizes = 0;
        //分页为空默认为1
        if(StringUtils.isEmpty(paramMap.get("page"))){
            paramMap.put("page","1");
        }
        //列表搜索数据（第一页）不足60条房源 【板块下数据不足按所在区域推】推荐+搜索数据=60条
        if(secondList.size() != 0 && secondList.size() < 60){
            if(paramMap.get("page") == "1"){
                if(paramMap.get("regionId") != null  ){
                    paramCount.put("regionId",paramMap.get("regionId"));
                    pageSizes = 60 - secondList.size();
                }else{
                    pageSizes = 60 - secondList.size();
                }
                paramCount.put("pageSize", ""+pageSizes);
                HashMap<String, Object> re = HttpUtil.connectApiString(Constants.SECOND_HOUSE_LIST, paramCount);
                HttpUtil.handleServiceList(re, "msg", "sechouseList", model);
            }
        }else if(secondList.size() == 0){
            if(paramMap.get("regionId") != null ){
                paramCount.put("regionId",paramMap.get("regionId"));
                pageSizes = 60;
            }else{
                pageSizes = 60;
            }
            paramCount.put("pageSize", ""+pageSizes);
            HashMap<String, Object> re = HttpUtil.connectApiString(Constants.SECOND_HOUSE_LIST, paramCount);
            HttpUtil.handleServiceList(re, "msg", "sechouseList", model);
        }

        //获取感兴趣的小区
        HashMap hashMap = new HashMap();
        hashMap.put("orderKey",1);
        if (paramMap.get("regionId") == null) {
            hashMap.put("regionId","");
        }
        else {
            hashMap.put("regionId",paramMap.get("regionId"));
        }
        HashMap<String, Object> interests = HttpUtil.connectApi(Constants.VIEW_INTERSED_SUB,hashMap);
        List<LinkedTreeMap<String, Object>> interest = (List<LinkedTreeMap<String, Object>>)interests.get("content");
        model.addAttribute("interest",interest);
        model.addAttribute("schoolRight",HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type","9").get(),true).get("content"));
       //小区专家
        HashMap plotParam = new HashMap();
        if(!StringUtils.isEmpty(seoSubName)){
            plotParam.put("title",seoSubName);
        }else if(!StringUtils.isEmpty(paramMap.get("subName"))){
            plotParam.put("title",paramMap.get("subName"));
        }
        HashMap<String, Object> experts = HttpUtil.connectApi(Constants.QUERY_PLOT_EXPERT,plotParam);
        List<LinkedTreeMap<String, Object>> plotExpertList = (List<LinkedTreeMap<String, Object>>)experts.get("content");
        if(null != plotExpertList){
            for(LinkedTreeMap<String, Object> object : plotExpertList){
                Double memberid = (Double)object.get("memberId");
                if(null != memberid){
                    object.put("_memberId",memberid.toString().replaceAll("\\.0",""));
                }
            }
            HashMap plot = new HashMap();
            //传小区Id 获取小区相关信息
            if(!StringUtils.isEmpty(experts.get("msg"))){
                plot.put("subId", experts.get("msg"));
                HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL, plot);
                LinkedTreeMap plot_info = (LinkedTreeMap<String, Object>) villageDetail.get("content");
                model.addAttribute("plotInformation",plot_info);
            }
        }
        model.addAttribute("plotId",experts.get("msg"));
        model.addAttribute("agentStoreType",2);//跳转经纪人店铺链接类型
        model.addAttribute("plotExpertList",plotExpertList);
        this.queryChoose(model);
    }
    /**
     *
     * 二手房详情页
     * @param houseId
     * @param model
     */
    public void getSeDetail(String houseId, Model model){
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_SCDHOUSE_DETAIL,
                new Params("houseId", houseId).get(), model);
        if(result == null) NoDataException.throwException();
        if (!StringUtils.isEmpty(result.get("sortTel"))){
            StringBuffer sortTel = new StringBuffer(result.get("sortTel").toString());
            sortTel.insert(6,"-").insert(3,"-");
            result.put("sortTel",sortTel);
        }
        if(!StringUtils.isEmpty(result.get("keeperTel2")))
            result.put("keeperTel2", Utils.decode(result.get("keeperTel2").toString()));

        if(!StringUtils.isEmpty(result.get("price"))){
            result.put("price", Utils.modifyNum(result.get("price").toString()));
        }
        if(!StringUtils.isEmpty(result.get("area"))){
            result.put("area", Utils.modifyNum(result.get("area").toString()));
        }
        if(!StringUtils.isEmpty(result.get("unitPrice"))){
            result.put("unitPrice", Utils.modifyNum(result.get("unitPrice").toString()));
        }
        if(!StringUtils.isEmpty(result.get("url"))){
            result.put("url", result.get("url").toString().replace("middle", "big"));
        }
        if(!StringUtils.isEmpty(result.get("layout"))){
            result.put("layout", result.get("layout").toString().replace("居", "室"));
        }
        result.put("configuration",result.get("configuration").toString().replaceAll("&amp;nbsp;", "").replaceAll("&amp;mdash;", "").replaceAll("&amp;rsquo;","").replaceAll("&amp;lsquo;","").replaceAll("&amp;middot;",""));
        if(result.get("addTime") != null){
            String[] str = result.get("addTime").toString().split(" ");
            String addTime = str[0];
            model.addAttribute("addTime",addTime);
        }
        if(result.get("time") != null){
            String[] str = result.get("time").toString().split(" ");
            String updateTime = str[0];
            model.addAttribute("updateTime",updateTime);
        }
        String[] str1 = result.get("houseTrait").toString().split(",");
        ArrayList list = new ArrayList();
        for(int i = 0; i<str1.length; i++){
            list.add(str1[i]);
        }
        model.addAttribute("list",list);

        //获取相似房源
        HashMap<String, String> paramMap = new HashMap<>();
        if(!StringUtils.isEmpty(result.get("subId"))){
            paramMap.put("subId", result.get("subId").toString());
        }
//        if(!StringUtils.isEmpty(result.get("price")) && !result.get("price").equals("0")){
//            paramMap.put("standardPrice",String.valueOf(Float.parseFloat( result.get("price").toString()) ));
//        }
//        paramMap.put("except",houseId);
//        if(!StringUtils.isEmpty(result.get("room"))){
//            paramMap.put("room", result.get("room").toString());
//        }
//        if(!StringUtils.isEmpty(result.get("hall"))){
//            paramMap.put("hall", result.get("hall").toString());
//        }
        paramMap.put("pageSize", String.valueOf(5));
        HashMap<String, Object> similar = HttpUtil.connectApiString(Constants.VIEW_SCDHOUSE_OTHER, paramMap);
        List<LinkedTreeMap<String, Object>> content = (List<LinkedTreeMap<String, Object>>)similar.get("content");
        model.addAttribute("similar",content);
        if(StringUtils.isEmpty(result.get("subName"))){
            result.put("subName","其它");
        }

        HashMap<String, Object> lottery = HttpUtil.connectApiString(Constants.SCDHOUSE_LOTTERY, paramMap);
        LinkedTreeMap<String, Object> contents = (LinkedTreeMap<String, Object>) lottery.get("content");
        model.addAttribute("lottery",contents.get("content"));
        model.addAttribute("sehouse",result);
        model.addAttribute("v",result);
        model.addAttribute("houseId",houseId);
        model.addAttribute("houseVideo",result.get("houseVideo"));//把视频装进去
        //获取视频地址（2018.10.22）
        LinkedTreeMap<String ,Object> con = (LinkedTreeMap<String, Object>) result.get("houseVideo");
        if(!StringUtils.isEmpty(con.get("mediaID"))){
            String mediaId =(String)con.get("mediaID");
            HashMap<String, Object> path = HttpUtil.connectApi(Constants.VIEW_VIDEO_PATH, new Params("mediaId", mediaId).get());
            LinkedTreeMap<String ,Object> cont = (LinkedTreeMap<String, Object>) path.get("content");
            ArrayList videoPath = (ArrayList<String>)cont.get("videoPath");
            model.addAttribute("videoPath",videoPath.get(0));//视频地址
            model.addAttribute("videoImgPath",videoPath.get(1));//图片地址
        }else{
            model.addAttribute("videoPath","");//无视频地址
            model.addAttribute("videoImgPath","");//无视频首图
        }

        //大数据
        /* HashMap<String, String> params = new HashMap<>();
        if (!StringUtils.isEmpty(result.get("regionName"))) {
            String regionName = result.get("regionName").toString();
            //计算6周前的日期
            Date lastDate = plotService.getOffsetMonthDate(new Date(), 6);
            //计算开始时间是当年的第几周
            Calendar c = Calendar.getInstance();
            c.setTime(lastDate);
            int betweenWeek = c.get(Calendar.WEEK_OF_YEAR) > 52 ? 52 : c.get(Calendar.WEEK_OF_YEAR);
            String lastDateUse = plotService.convertDateToString(lastDate, "yyyy") + "-" + betweenWeek;
            params.put("startWeek", lastDateUse);
            ArrayList resutList = new ArrayList();
            //获取沈阳均价
            resutList.add(plotService.citytrending(params));
            params.put("region", regionName.replaceAll("新区", "").replaceAll("区", ""));
            //获取铁西均价
            resutList.add(plotService.citytrending(params));
            params.remove("region");
            //获取具体小区均价
            params.put("community", result.get("subName").toString());
            resutList.add(plotService.citytrending(params));
            model.addAttribute("data", resutList);
            if(regionName.indexOf("周边") != -1){
                model.addAttribute("isRound",2);
            }else{
                model.addAttribute("isRound",1);
            }
        } */
        //虚假举报字段展示
        HashMap<String, Object> r = HttpUtil.connectApi(Constants.FALSITY_HOUSE,null);
        HttpUtil.handleServiceList(r, "msg", "falsity", model);
        // 获取您可能感兴趣的房源(2019.02.14)
        HashMap<String, String> requiredParam = new HashMap<>();
        if(!StringUtils.isEmpty(result.get("price")) && !result.get("price").equals("0")){
            requiredParam.put("minPrice",String.valueOf(Float.parseFloat( result.get("price").toString())-10 ));
        }
        if(!StringUtils.isEmpty(result.get("price")) && !result.get("price").equals("0")){
            requiredParam.put("maxPrice",String.valueOf(Float.parseFloat( result.get("price").toString())+10 ));
        }
        if(!StringUtils.isEmpty(result.get("regionId"))){
            requiredParam.put("regionId", result.get("regionId").toString());
        }
        //分页为空默认为1
        if(StringUtils.isEmpty(result.get("page"))){
            requiredParam.put("page","1");
        }
        requiredParam.put("pageSize", String.valueOf(10));
        requiredParam.put("auction","1");//竞价
        requiredParam.put("memberType","2");//传经纪人条件(memberType=2 表示经纪人)
        HashMap<String, Object> interestResult = HttpUtil.connectApiString(Constants.SECOND_HOUSE_LIST, requiredParam);
        HttpUtil.handleServiceList(interestResult, "msg", "interestHouse", model);
        //获取该小区单价及环比（房价走势图小区单价环比使用 2019.05.18）
        if(!StringUtils.isEmpty(result.get("subId"))){
            LinkedTreeMap<String, Object> villageDetail = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL,
                    new Params("subId", result.get("subId")).get(), model);
            model.addAttribute("villageDetail",villageDetail);
        }
        if(result.get("housePan") != null){
            LinkedTreeMap<String, Object> pan = (LinkedTreeMap<String, Object>) result.get("housePan");
            model.addAttribute("panUrl_VR",pan.get("panUrl"));
            model.addAttribute("panUrl_VRImg",pan.get("panImageUrl"));
        }
        //获取经纪人服务标签
        if(!StringUtils.isEmpty(result.get("agencyId"))){
            String agentId = (String)result.get("agencyId");
            this.getAgentLabel(agentId,model);
        }
    }
    /**
     *
     * 二手房成交详情页
     * @param houseId
     * @param model
     */
    public void getDealSaleDetail(String houseId, Model model){
        LinkedTreeMap<String, Object> result = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_SCDHOUSE_DETAIL,
                new Params("houseId", houseId).get(), model);
        HttpUtil.connectApi(Constants.DEAL_SCDHOUSE_DETAIL, new Params("houseId", houseId).get(), model, "dealSale");
        if(result == null) NoDataException.throwException();
        if(!StringUtils.isEmpty(result.get("keeperTel2")))
            result.put("keeperTel2", Utils.decode(result.get("keeperTel2").toString()));

        if(!StringUtils.isEmpty(result.get("price"))){
            result.put("price", Utils.modifyNum(result.get("price").toString()));
        }
        if(!StringUtils.isEmpty(result.get("area"))){
            result.put("area", Utils.modifyNum(result.get("area").toString()));
        }
        if(!StringUtils.isEmpty(result.get("unitPrice"))){
            result.put("unitPrice", Utils.modifyNum(result.get("unitPrice").toString()));
        }
        if(!StringUtils.isEmpty(result.get("url"))){
            result.put("url", result.get("url").toString().replace("middle", "big"));
        }
        if(!StringUtils.isEmpty(result.get("layout"))){
            result.put("layout", result.get("layout").toString().replace("居", "室"));
        }
        result.put("configuration",result.get("configuration").toString().replaceAll("&amp;nbsp;", "").replaceAll("&amp;mdash;", "").replaceAll("&amp;rsquo;","").replaceAll("&amp;lsquo;","").replaceAll("&amp;middot;",""));
        if(result.get("addTime") != null){
            String[] str = result.get("addTime").toString().split(" ");
            String addTime = str[0];
            model.addAttribute("addTime",addTime);
        }
        if(result.get("time") != null){
            String[] str = result.get("time").toString().split(" ");
            String updateTime = str[0];
            model.addAttribute("updateTime",updateTime);
        }
        String[] str1 = result.get("houseTrait").toString().split(",");
        ArrayList list = new ArrayList();
        for(int i = 0; i<str1.length; i++){
            list.add(str1[i]);
        }
        model.addAttribute("list",list);

        //获取相似房源
        HashMap<String, String> paramMap = new HashMap<>();
        if(!StringUtils.isEmpty(result.get("subId"))){
            paramMap.put("subId", result.get("subId").toString());
        }
        if(!StringUtils.isEmpty(result.get("price")) && !result.get("price").equals("0")){
            paramMap.put("standardPrice",String.valueOf(Float.parseFloat( result.get("price").toString()) ));
        }
        paramMap.put("except",houseId);
        paramMap.put("room", result.get("room").toString());
        paramMap.put("hall", result.get("hall").toString());
        paramMap.put("pageSize", String.valueOf(6));
        HashMap<String, Object> similar = HttpUtil.connectApiString(Constants.VIEW_SCDHOUSE_OTHER, paramMap);
        List<LinkedTreeMap<String, Object>> content = (List<LinkedTreeMap<String, Object>>)similar.get("content");
        model.addAttribute("similar",content);
        if(StringUtils.isEmpty(result.get("subName"))){
            result.put("subName","其它");
        }
        model.addAttribute("sehouse",result);
        model.addAttribute("houseId",houseId);
    }

    //二手房>小区找房列表页
    public static final String SECONDHOUSE_VILLAGES_HOUSE_URL = "/saleVillages";
    public void getVillages(String baseUrl, String params, Model model){
        //解析地址栏传过来的数据。生成限制条件 paramMap
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.VIEW_NEW_SUBDISTRICT);

        //找出所有限制条件参数列表，并在参数中加上 “全部” 一条。并在每条数据中增加一条url字段（根据baseUrl写），并存在model中
        ParamsUtil.addFilterIntoModel(Constants.VIEW_NEW_SUBDISTRICT,model,baseUrl,params,paramMap);
        //根据paramMap 查询接口，得到筛选后的信息
        paramMap.put("pageSize","30");//一页30条
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_NEW_SUBDISTRICT,paramMap);
        //searchKey
        model.addAttribute("searchKey",paramMap.get("subName"));
        String [] input = params.split("-");
        for (int i = 0 ; i<input.length ; i++){
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("k")){
                String minPrice = input[i].substring(1,input[i].length());
                paramMap.put("minPrice",minPrice);
                model.addAttribute("minPrice", minPrice);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("x")){
                String maxPrice = input[i].substring(1,input[i].length());
                paramMap.put("maxPrice",maxPrice);
                model.addAttribute("maxPrice", maxPrice);
            }
        }
        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "village", model);
        new MyPageable(paramMap, 30).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
//        //取得区域
//        Map<String,Object> map = model.asMap();
//        List<UrlBean> lregion = new  ArrayList<UrlBean>();
//        if(!StringUtils.isEmpty(map.get("region"))){
//            lregion = (List<UrlBean>)map.get("region");
//        }
//        //取得板块
//        List<UrlBean> lplate = new  ArrayList<UrlBean>();
//        if(!StringUtils.isEmpty(map.get("plate"))){
//            lplate = (List<UrlBean>)map.get("plate");
//        }
        this.queryChoose(model);
    }
    /**.
     * 地铁列表页
     */
    public static final String SUBWAY_URL = "/saleHouses/subway/";
    public void getSecondSubwayList(String baseUrl,String params, Model model) {
        ParamsUtil.addFilterIntoModel( Constants.SECOND_HOUSE_LIST, model, baseUrl, params);
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.SECOND_HOUSE_LIST);
        paramMap.put("pageSize", "30");
        HashMap<String, String> param = ParamsUtil.analysisInput(params);
        if (!StringUtils.isEmpty(param.get("page"))) {
            paramMap.put("page", param.get("page"));
        }
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.SECOND_HOUSE_LIST, paramMap);
        HttpUtil.handleServiceList(result, "msg", "subwaylist", model);
        new MyPageable(paramMap, 20).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }

    public static final String DEAL_SECOND_HOUSE = "/dealSales/";
    public void getDealSecondList(String params, Model model,HttpServletRequest request){
        keyValueConfig("second_anxuan", model, "forAnxuan");
        String [] input = params.split("-");
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, DEAL_SECOND_LIST);
        ParamsUtil.addFilterIntoModel(DEAL_SECOND_LIST, model, DEAL_SECOND_HOUSE, params, paramMap);
        model.addAttribute("searchKey",paramMap.get("subName"));
        ParamsUtil.addSeoTitle(model, "orderKey,");
        String trait = "";
        String seoSubName = "";

        for (int i = 0 ; i < input.length ; i++) {
            //特色
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("u")) {
                trait = trait + input[i].substring(1, input[i].length()) + ",";
                paramMap.put("houseTrait", trait);
            }
            // 小区
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("v")){
                String subId = input[i].substring(1,input[i].length());
                model.addAttribute("subId",subId);
                paramMap.put("subId",subId);
                HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL,new Params("subId",subId).get(), model, "sub");

                Map<String, Object> mod = model.asMap();
                if(mod.containsKey("sub")) {
                    seoSubName = (String) ((LinkedTreeMap<String, Object>)mod.get("sub")).get("title");
                    if(seoSubName == null) {
                        seoSubName = "";
                    }
                }
            }
            model.addAttribute("seoSubName", seoSubName);
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("k")){
                String minPrice = input[i].substring(1,input[i].length());
                paramMap.put("minPrice",minPrice);
                model.addAttribute("minPrice", minPrice);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("x")){
                String maxPrice = input[i].substring(1,input[i].length());
                paramMap.put("maxPrice",maxPrice);
                model.addAttribute("maxPrice", maxPrice);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("y")){
                String minArea = input[i].substring(1,input[i].length());
                paramMap.put("minArea",minArea);
                model.addAttribute("minArea", minArea);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0,1).equals("e")){
                String maxArea = input[i].substring(1,input[i].length());
                paramMap.put("maxArea",maxArea);
                model.addAttribute("maxArea", maxArea);
            }
        }
        if(paramMap.get("isSubway") != null)
            model.addAttribute("isSubway", 1);
        else
            model.addAttribute("isSubway", 0);

        int pageSize = 30;
        if(paramMap.containsKey("piece")) {
            pageSize = 72;
            //这个参数不传入接口
            paramMap.remove("piece");
        }
        paramMap.put("pageSize", ""+pageSize);
        HashMap<String, Object> result = HttpUtil.connectApiString(DEAL_SECOND_LIST, paramMap);
        this.dealPriceAndTime(result,1);
        HttpUtil.handleServiceList(result, "msg", "secondhouse", model);
        new MyPageable(paramMap, pageSize).putAttribute(model, DEAL_SECOND_HOUSE + params, Integer.valueOf((String) result.get("msg")));


        //获取感兴趣的小区
        HashMap hashMap = new HashMap();
        hashMap.put("orderKey",1);
        if (paramMap.get("regionId") == null) {
            hashMap.put("regionId","");
        }
        else {
            hashMap.put("regionId",paramMap.get("regionId"));
        }
        HashMap<String, Object> interests = HttpUtil.connectApi(Constants.VIEW_INTERSED_SUB,hashMap);
        List<LinkedTreeMap<String, Object>> interest = (List<LinkedTreeMap<String, Object>>)interests.get("content");
        model.addAttribute("interest",interest);
        model.addAttribute("schoolRight",HttpUtil.connectApi(Constants.EXCEPT_NEWHOUSE_ADVERTISEMENT, new Params("type","9").get(),true).get("content"));
        this.queryChoose(model);
    }

    //对挂牌价格去.0 对成交价格进行处理  对spantime数据类型转换
    public void dealPriceAndTime(HashMap<String, Object> result,Integer type){
        List<LinkedTreeMap<String,Object>> results = (List<LinkedTreeMap<String,Object>>)result.get("content");
        for (int i = 0 ; i<results.size();i++) {
            if (results.get(i).get("spanTime") == null) results.get(i).put("spanTime","0");
            int spantime = Integer.parseInt(results.get(i).get("spanTime").toString());
            String dealPrice = Utils.modifyNum(results.get(i).get("dealPrice").toString());
            /*if (dealPrice.length() > 4 && dealPrice.toString().substring(dealPrice.length()-4,dealPrice.length()).equals("0000")) {

            }*/
            results.get(i).put("spanTime",spantime);
            if (type == 1) {
                results.get(i).put("salePrice",Utils.modifyNum(results.get(i).get("salePrice").toString()));
            }else if (type == 2) {
                results.get(i).put("rentPrice",Utils.modifyNum(results.get(i).get("rentPrice").toString()));
            }else if (type == 3) {
                results.get(i).put("price",Utils.modifyNum(results.get(i).get("price").toString()));
            }
            if (spantime < 31 && !StringUtils.isEmpty(dealPrice)) {
                int dealPriceLength = dealPrice.length();
                if (dealPriceLength > 1) {
                    int starNum = dealPriceLength - 1;
                    String star =  dealPrice.substring(0,1);
                    int point = dealPrice.indexOf(".");
                    for (int j = 0; j < starNum; j++) {
                        if (point != -1 && point == j+1) {
                            star = star + ".";
                        }else {
                            star = star + "*";
                        }
                    }
                    results.get(i).put("dealPrice",star);
                }else {
                    results.get(i).put("dealPrice",dealPrice);
                }
            }
        }

    }

    /**
     * 虚假举报
     * @param sessionId
     * @param illegalType
     * @param houseId
     * @param houseType
     * @param session
     * @return
     */
    public HashMap<String, Object> falsity(String sessionId,String illegalType, Integer houseId,String houseType, HttpSession session) {
        HashMap<String, Object> value = HttpUtil.connectApi(Constants.FALSITY_HOUSE_SUB, new Params("sessionId",sessionId).add("illegalType",illegalType).add("houseId",houseId).add("houseType",houseType).get());
        int status = Double.valueOf((double) value.get("status")).intValue();
        if(status == -1){
            Utils.removeSession(session);
        }
        return value;
    }

    /**
     * 地图找房  右侧地图
     * @param hashMap
     * @return
     * @Date 2018.11.09
     */
    public HashMap<String, Object> querySecondHouse(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.QUERY_SECONDMAP, hashMap);

    }

    /**
     * 地图找房  左侧列表
     * @param hashMap
     * @return
     * @Date 2018.11.09
     */
    public HashMap<String, Object> querySecondHouseList(HashMap<String ,Object> hashMap) {
        return HttpUtil.connectApi(Constants.QUERY_SECONDMAP_LEFTLIST, hashMap);
    }

    /**
     * 地图找房 点击具体小区展示小区相关信息
     * @param subId
     * @param
     * @return
     */
    public HashMap<String, Object> querySubDetail(String subId) {
        HashMap hashMap = new HashMap();
        hashMap.put("subId", subId);
        return  HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL, hashMap);


    }

    /**
     * (二手房、租房、商铺、写字楼)列表右侧广告位
     * (包括推荐、画中画、中介广告)
     * @Date 2019.01.14
     * @param systemDate
     * @param model
     */
    public void getRightAdvert(String systemDate,Model model,Integer type){
        HashMap<String, Object> resultMap;
        if(systemDate != null){
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("systemDate",systemDate).add("type",type).get(),true);

        }else {
            resultMap = HttpUtil.connectApi(EXCEPT_NEWHOUSE_ADVERTISEMENT,new Params("type",type).get(),true);
        }
        model.addAttribute("advert",resultMap.get("content"));
    }

    /**
     * 获取经纪人服务标签
     * @param memberId
     * @param model
     * @DATE 2019.06.03
     */
    public void getAgentLabel(String memberId,Model model){
        HashMap<String, Object> re = HttpUtil.connectApi(Constants.GET_AGENT_LABEL, new Params("memberId",memberId).get());
        List<HashMap<String, Object>> labelList = (List<HashMap<String, Object>>) re.get("content");
        if(labelList.size() > 0){
            model.addAttribute("agentLabel",labelList);
        }
    }

    /**
     * 地铁 搜索框 下拉联动匹配结果
     * @date 2019.08.14
     * @param
     * @return
     */
    public List<LinkedTreeMap<String, Object>> queryForSub(String search) {
        try {
            search  =  URLDecoder.decode(search,"UTF-8");
        }catch (Exception e){
            e.printStackTrace();
        }
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.SEARCH_FOR_SUB_TITLE, new Params().add("searchName",search).get());
        List<LinkedTreeMap<String, Object>> info = (List<LinkedTreeMap<String, Object>>) result.get("content");
        return info;
    }
    /**
     * 二手房 地铁 站点及小区信息
     * @param hashMap
     * @return
     * <AUTHOR>
     */
    public HashMap<String, Object> secStationMap(HashMap hashMap) {
        return HttpUtil.connectApi(Constants.SEC_STATION_INFO, hashMap);
    }

    /**
     * 地铁找房 左侧房源列表
     * @return
     * @date 2019.08.16
     */
    public HashMap<String, Object> secStationMapHouse(HashMap hashMap) {
        hashMap.put("auction","1");
        return HttpUtil.connectApi(Constants.SECOND_HOUSE_LIST, hashMap);
    }

    /**
     * 地铁找房 小区详情、小区专家
     * @return
     * @date 2019.08.16
     */
    public HashMap<String, Object> secMapPlot(String subId) {
        HashMap<String, Object> map = new HashMap<String, Object>();
        HashMap<String, Object> plot_details = this.querySubDetail(subId);
        HashMap<String, Object> experts = HttpUtil.connectApi(Constants.QUERY_PLOT_EXPERT,new Params("subId",subId).get());
        List<LinkedTreeMap<String, Object>> plotExpertList = (List<LinkedTreeMap<String, Object>>)experts.get("content");
        if(null != plotExpertList){
            for(LinkedTreeMap<String, Object> object : plotExpertList){
                Double memberid = (Double)object.get("memberId");
                if(null != memberid){
                    object.put("_memberId",memberid.toString().replaceAll("\\.0",""));
                }
            }
        }
        map.put("plot_details",plot_details);
        map.put("plotExpertList",plotExpertList);
        return map;
    }

    /**
     * 二手房右侧 房价评估使用
     * @date 2019.12.24
     * @param model
     * @return
     */
    private  HashMap<String,Object> queryChoose (Model model){

        //取得区域
        Map<String,Object> map = model.asMap();
        List<UrlBean> region = new  ArrayList<UrlBean>();
        if(!StringUtils.isEmpty(map.get("region"))){
            region = (List<UrlBean>)map.get("region");
        }
        //取得板块
        List<UrlBean> plate = new  ArrayList<UrlBean>();
        if(!StringUtils.isEmpty(map.get("plate"))){
            plate = (List<UrlBean>)map.get("plate");
        }


        HashMap<String,Object> choose_map = new HashMap<>();
        for( UrlBean _r :region){
            if(!StringUtils.isEmpty(_r.getId())){
                boolean select = _r.getSelected();
                //选中的区域
                if(select){
                    choose_map.put("choose_regionId",_r.getId());
                    choose_map.put("choose_regionName",_r.getName());
                    break ;
                }
            }
        }
        if( plate.size() > 0){
            for( UrlBean _p :plate){
                if(!StringUtils.isEmpty(_p.getId())){
                    boolean select = _p.getSelected();
                    //选中的板块
                    if(select){
                        choose_map.put("choose_plateId",_p.getId());
                        choose_map.put("choose_plateName",_p.getName());
                        break ;
                    }
                }
            }
        }
        //判断map里有啥
        if(choose_map.size() == 0 || (choose_map.get("choose_regionName").toString().indexOf("周边")!= -1)){
            model.addAttribute("chooseName","沈阳");
            model.addAttribute("chooseType",1);
        }else if(!StringUtils.isEmpty(choose_map.get("choose_plateName"))){
            model.addAttribute("chooseName",choose_map.get("choose_plateName"));
            model.addAttribute("chooseId",choose_map.get("choose_plateId"));
            model.addAttribute("choose_region_id",choose_map.get("choose_regionId"));
            model.addAttribute("chooseType",3);
        }else if(!StringUtils.isEmpty(choose_map.get("choose_regionName"))){
            model.addAttribute("chooseName",choose_map.get("choose_regionName"));
            model.addAttribute("chooseId",choose_map.get("choose_regionId"));
            model.addAttribute("chooseType",2);
        }
        return choose_map;
    }

    /**
     * 获取相关参数信息
     * @param key
     * @param model
     * @param column
     */
    public void keyValueConfig(String key, Model model, String column){
        HttpUtil.connectApi(Constants.GET_KEY_VALUE_CONFIG, new Params("key", key).get(), model, column);
    }

    public HashMap<String, Object> memberIdCardAuthentication(HashMap reqMap, HttpSession session) {
        String mobile = session.getAttribute("phoneNum") + "";
        reqMap.put("mobile", mobile);
        HashMap res = HttpUtil.connectApi(Constants.MEMBER_ID_CARD_AUTHENTICATION, reqMap);
        String msg = res.get("msg") + "";
        session.setAttribute("authenticationStatus", "success".equals(msg) ? 1 : 2);
        int status = Double.valueOf((double) res.get("status")).intValue();
        if (-1 == status) {
            Utils.removeSession(session);
        }
        return res;
    }

    public HashMap<String, Object> confirmAuthenticationInfo(HashMap reqMap, HttpSession session) {
        HashMap res = HttpUtil.connectApi(Constants.CONFIRM_AUTHENTICATION_INFO, reqMap);
        int status = Double.valueOf((double) res.get("status")).intValue();
        if (1 == status) {
            session.setAttribute("authenticationStatus", 1);
        } else if (-1 == status) {
            Utils.removeSession(session);
        }
        return res;
    }
}
