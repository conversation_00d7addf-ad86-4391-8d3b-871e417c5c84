package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class AgentService {
    @Autowired
    private SecondHouseService secondHouseService;
    @Autowired
    private RentService rentService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private ScriptoriumService scriptoriumService;
    @Autowired
    AgentCompanyService agentCompanyService;
    @Autowired
    private AgentRecruitService  agentRecruitService;

    /**
     * 查询风云榜成员 尝试新写法
    * @Date 2020/10/15 16:01
    * @Param []
    * @return void
    **/
    public void quaryRegionFilter(String params, Model model, String baseUrl){
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.MEMBET_TOP_LIST);
        ParamsUtil.addFilterIntoModel(Constants.MEMBET_TOP_LIST, model, baseUrl, params, paramMap);
        paramMap.put("pageSize","9999");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.MEMBET_TOP_LIST, paramMap);
        HttpUtil.handleServiceList(result, "total", "memberTopList", model);
//        new MyPageable(paramMap, pageSize).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("baseUrl",baseUrl.replace("/",""));
    }

    /**
     * 查询风云榜成员
    * @Date 2020/10/14 15:53
    * @Param [regionId, model]
    * @return java.util.HashMap<java.lang.String,java.lang.Object>
    **/
    public HashMap<String,Object> quaryMemberTopList(Integer regionId, Integer page, Integer pageSize){
        HashMap<String, Object> params = new HashMap<>();
        if(regionId != null)    params.put("regionID",regionId);
        if(page != null)        params.put("page",page);
        if(pageSize != null)    params.put("pageSize",pageSize);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.MEMBET_TOP_LIST,params);
        return result;
    }

    /**
     * 经纪人商铺
     * @param model
     * @param agentId
     * @param params
     */
    public void agentShop(Model model, String agentId, String params,String agentIntermediaryId){
        model.addAttribute("mobileUrl","https://m.fangxiaoer.com/agentshop/"+agentId+"-3/" + params);
        String baseUrl = "/agent/shops/"+agentId+"/";
        if(!StringUtils.isEmpty(agentIntermediaryId)){
            baseUrl = "/agentIntermediary/shops/"+agentIntermediaryId+"/";
        }
        ParamsUtil.addFilterIntoModel(Constants.GET_SHOP_LIST, model, baseUrl, params);
        //转换url为可用参数
//        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params);
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params,Constants.GET_SHOP_LIST);
        paramMap.put("pageSize","10");
        if(!StringUtils.isEmpty(agentIntermediaryId)){
            paramMap.put("agentIntermediaryId",agentIntermediaryId);
            /*paramMap.put("agentIntermediaryState","1");
            paramMap.put("isShow","1");
            paramMap.put("checkState","1");*/
            //model.addAttribute("agentIntermediaryId",agentIntermediaryId);
        } else {
            paramMap.put("agencyId",agentId);
            //model.addAttribute("agentId",agentId);
        }
        paramMap.put("auction","1");
        //参数里加入模糊搜索字段
        model.addAttribute("searchName",paramMap.get("title"));
        HashMap shopList =  shopService.searchShoplist(paramMap,model,"shop",2);
        if(!StringUtils.isEmpty(agentId)){
            HashMap agent =  this.searchAgentshop(agentId,model);
            if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
                LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
                model.addAttribute("agentInfo",  content);
                LinkedTreeMap<String, Object> list = (LinkedTreeMap<String, Object>) content.get("shopPush");
                if(null != list){
                    if (list.size() == 0) {
                        model.addAttribute("shopPush", null);
                    } else {
                        //添加商铺的均价
                        for (int i =0;i<list.size();i++) {
                            list.put("unitPrice", shopService.getUnitPrice(list, 2));
                            //单价处理过亿价格
                            if (list.get("price") != null && list.get("price") != "") {
                                BigDecimal bigDecimal = new BigDecimal(Double.parseDouble(list.get("price").toString()) );
                                bigDecimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                                list.put("price", bigDecimal.toString());
                            }
                        }
                        model.addAttribute("shopPush",list);
                    }
                }
                model.addAttribute("shopList",content.get("shopsList"));
            }
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) shopList.get("msg")));
    }

    /**
     * 经纪人商铺成交
     * @param model
     * @param agentId
     * @param params
     */
    public void agentDealShop(Model model, String agentId, String params, HttpServletRequest request){
        model.addAttribute("mobileUrl","https://m.fangxiaoer.com/agentshop/"+agentId+"-5.htm");
        String baseUrl = "/agent/dealShops/"+agentId+"/";
        ParamsUtil.addFilterIntoModel(Constants.DEAL_SHOP_LIST, model, baseUrl, params);
        //转换url为可用参数
//        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params);
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params,Constants.DEAL_SHOP_LIST);
        paramMap.put("pageSize","10");
        paramMap.put("agencyId",agentId);
        //参数里加入模糊搜索字段
        model.addAttribute("searchName",paramMap.get("title"));
        //HashMap shopList =  shopService.searchShoplist(paramMap,model,"shop",2);
        // 处理结果
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.DEAL_SHOP_LIST, paramMap);
        int status = HttpUtil.handleServiceList(result, "msg", "shop", model);
        if(status == 1){
            List<LinkedTreeMap<String, Object>> list = (List<LinkedTreeMap<String, Object>>) result.get("content");
            if (list.size() == 0) {
                model.addAttribute("shop", null);
            } else {
                //添加商铺的均价
                for(LinkedTreeMap map : list){
                    //map.put("unitPrice",shopService.getUnitPrice(map,1));
                    //单价处理过亿价格
                    if(map.get("price") != null && map.get("price") != ""){
                        BigDecimal bigDecimal = new BigDecimal ((Double) map.get("price"));
                        bigDecimal = bigDecimal.setScale(2,BigDecimal.ROUND_HALF_UP);
                        map.put("price",bigDecimal.toString());
                    }
                }
                model.addAttribute("shop", list);
            }
        }
        //对挂牌价格去.0 对成交价格进行处理  对spantime数据类型转换
        secondHouseService.dealPriceAndTime(result,3);
        HashMap agent =  this.searchAgentshop(agentId,model);
        if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            model.addAttribute("agentInfo",  content);
            model.addAttribute("shopList",content.get("shopList"));
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("agentId",agentId);
        model.addAttribute("dealSaleNums",HttpUtil.connectApi(Constants.DEAL_SECOND_LIST, new Params("agencyId",agentId).get()).get("msg"));
        model.addAttribute("dealRentNums",HttpUtil.connectApi(Constants.DEAL_RENT_LIST, new Params("agencyId",agentId).get()).get("msg"));
        model.addAttribute("dealShopNums",result.get("msg"));
    }


    /**
     * 经纪人新房房源
     * @param model
     * @param agentId
     * @param params
     */
    public void agentNewSecond(Model model,String agentId,String params ,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentshop/"+agentId+"-1/" + params);
        String baseUrl = "/agent/newsecond/"+agentId+"/";
        ParamsUtil.addFilterIntoModel(Constants.SECOND_HOUSE_LIST,model,baseUrl,params);
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.SECOND_HOUSE_LIST);
        paramMap.put("pageSize","10");
        paramMap.put("agencyId",agentId);
        paramMap.put("auction","1");
        paramMap.put("isNewHouse","1");
        //添加了模糊查询字段
        model.addAttribute("searchName",paramMap.get("subName"));
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.SECOND_HOUSE_LIST,paramMap);
        HttpUtil.handleServiceList(result, "msg", "saleHouses", model);
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("agentId",agentId);
    }

    /**
     * 经纪人二手房
     * @param model
     * @param agentId
     * @param params
     */
    public void agentSecond(Model model,String agentId,String params,String agentIntermediaryId){
        //model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentshop/"+agentId+"-1/" + params);
        String baseUrl = "/agent/second/"+agentId+"/";
        if(!StringUtils.isEmpty(agentIntermediaryId)){
            baseUrl = "/agentIntermediary/second/"+agentIntermediaryId+"/";
        }
        ParamsUtil.addFilterIntoModel(Constants.SECOND_HOUSE_LIST,model,baseUrl,params);
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.SECOND_HOUSE_LIST);
        paramMap.put("pageSize","10");
        if(!StringUtils.isEmpty(agentIntermediaryId)){
            paramMap.put("agentIntermediaryId",agentIntermediaryId);
            /*paramMap.put("agentIntermediaryState","1");
            paramMap.put("isShow","1");
            paramMap.put("checkState","1");*/
           //model.addAttribute("agentIntermediaryId",agentIntermediaryId);
        } else {
            paramMap.put("agencyId",agentId);
            //model.addAttribute("agentId",agentId);
        }
        paramMap.put("auction","1");
        paramMap.put("isNewHouse","0");
        //添加了模糊查询字段
        model.addAttribute("searchName",paramMap.get("subName"));
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.SECOND_HOUSE_LIST,paramMap);
        HttpUtil.handleServiceList(result, "msg", "saleHouses", model);
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) result.get("msg")));
    }

    /**
     * 经纪人二手房成交
     * @param model
     * @param agentId
     * @param params
     */
    public void agentDealSecond(Model model,String agentId,String params ,HttpServletRequest request){
        model.addAttribute("https://m.fangxiaoer.com/agentshop/"+agentId+"-5.htm");
        String baseUrl = "/agent/dealSecond/"+agentId+"/";
        ParamsUtil.addFilterIntoModel(Constants.DEAL_SECOND_LIST,model,baseUrl,params);
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.DEAL_SECOND_LIST);
        paramMap.put("pageSize","10");
        paramMap.put("agencyId",agentId);
        //添加了模糊查询字段
        model.addAttribute("searchName",paramMap.get("subName"));
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.DEAL_SECOND_LIST,paramMap);
        //对挂牌价格去.0 对成交价格进行处理  对spantime数据类型转换
        secondHouseService.dealPriceAndTime(result,1);
        HttpUtil.handleServiceList(result, "msg", "saleHouses", model);
        HashMap agent =  this.searchAgentshop(agentId,model);
        if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            model.addAttribute("agentInfo",  content);
            model.addAttribute("salePush",content.get("salePush"));
            model.addAttribute("saleList",content.get("saleList"));
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("agentId",agentId);
        model.addAttribute("dealSaleNums",result.get("msg"));
        model.addAttribute("dealRentNums",HttpUtil.connectApi(Constants.DEAL_RENT_LIST, new Params("agencyId",agentId).get()).get("msg"));
        model.addAttribute("dealShopNums",HttpUtil.connectApi(Constants.DEAL_SHOP_LIST, new Params("agencyId",agentId).get()).get("msg"));
    }

    /**
     * 经纪人租房
     * @param model
     * @param agentId
     * @param params
     */
    public void agentRent(Model model,String agentId,String params,String agentIntermediaryId){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentshop/"+agentId+"-2/" + params);
        String baseUrl = "/agent/rents/"+agentId+"/";
        if(!StringUtils.isEmpty(agentIntermediaryId)){
            baseUrl = "/agentIntermediary/rents/"+agentIntermediaryId+"/";
        }
        ParamsUtil.addFilterIntoModel(Constants.GET_RENTHOUSE_LIST,model,baseUrl,params);
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_RENTHOUSE_LIST);
        paramMap.put("pageSize","10");
        if(!StringUtils.isEmpty(agentIntermediaryId)){
            paramMap.put("agentIntermediaryId",agentIntermediaryId);
            /*paramMap.put("agentIntermediaryState","1");
            paramMap.put("isShow","1");
            paramMap.put("checkState","1");*/
            //model.addAttribute("agentIntermediaryId",agentIntermediaryId);
        } else {
            paramMap.put("agencyId",agentId);
            //model.addAttribute("agentId",agentId);
        }
        paramMap.put("auction","1");
        //参数里加入模糊搜索字段
        model.addAttribute("searchName",paramMap.get("subName"));
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_RENTHOUSE_LIST,paramMap);
        HttpUtil.handleServiceList(result, "msg", "rents", model);
        if(!StringUtils.isEmpty(agentId)){
            HashMap agent =  this.searchAgentshop(agentId,model);
            if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
                LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
                model.addAttribute("agentInfo",  content);
                model.addAttribute("rentPush",content.get("rentPush"));
                model.addAttribute("rentList",content.get("rentList"));
            }
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) result.get("msg")));
        //model.addAttribute("agentId",agentId);
    }

    /**
     * 经纪人租房成交
     * @param model
     * @param agentId
     * @param params
     */
    public void agentDealRent(Model model,String agentId,String params,HttpServletRequest request){
        model.addAttribute("https://m.fangxiaoer.com/agentshop/"+agentId+"-5.htm");
        String baseUrl = "/agent/dealRents/"+agentId+"/";
        ParamsUtil.addFilterIntoModel(Constants.DEAL_RENT_LIST,model,baseUrl,params);
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.DEAL_RENT_LIST);
        paramMap.put("pageSize","10");
        paramMap.put("agencyId",agentId);
        //参数里加入模糊搜索字段
        model.addAttribute("searchName",paramMap.get("subName"));
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.DEAL_RENT_LIST,paramMap);
        //对挂牌价格去.0 对成交价格进行处理  对spantime数据类型转换
        secondHouseService.dealPriceAndTime(result,2);
        HttpUtil.handleServiceList(result, "msg", "rents", model);
        HashMap agent =  this.searchAgentshop(agentId,model);
        if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            model.addAttribute("agentInfo",  content);
            model.addAttribute("rentPush",content.get("rentPush"));
            model.addAttribute("rentList",content.get("rentList"));
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("agentId",agentId);
        model.addAttribute("dealSaleNums",HttpUtil.connectApi(Constants.DEAL_SECOND_LIST, new Params("agencyId",agentId).get()).get("msg"));
        model.addAttribute("dealRentNums",result.get("msg"));
        model.addAttribute("dealShopNums",HttpUtil.connectApi(Constants.DEAL_SHOP_LIST, new Params("agencyId",agentId).get()).get("msg"));
    }

    /**
     * 查询经纪人店铺
     * @param agencyId
     * @return
     */
    public HashMap searchAgentshop(String agencyId, Model model){
        HashMap hashMap = new HashMap();
        hashMap.put("agencyId",agencyId);
        hashMap.put("syUse",1);
        HashMap result =  HttpUtil.connectApi(Constants.VIEW_AGENT_DETAIL,hashMap);
        if(null == result || null == result.get("content")){
            NoDataException.throwException();
        }
        LinkedTreeMap<String ,Object> map = (LinkedTreeMap<String, Object>) result.get("content");
        if (!StringUtils.isEmpty(map.get("sortTel")) && !"1".equals(map.get("sortTel").toString().substring(0,1))){
            StringBuffer sortTel = new StringBuffer(map.get("sortTel").toString());
            sortTel.insert(6,"-").insert(3,"-");
            map.put("sortTel",sortTel);
        }
        // 2020/12/26 Bowen 统一调取存入model
        if(model != null){
            LinkedTreeMap content = (LinkedTreeMap) result.get("content");
            model.addAttribute("agentInfo",  content);
            model.addAttribute("salePush",content.get("salePush"));
            model.addAttribute("saleList",content.get("saleList"));
            /*model.addAttribute("subName1",content.get("subName1"));
            model.addAttribute("subName2",content.get("subName2"));
            model.addAttribute("subName3",content.get("subName3"));
            model.addAttribute("subId1",content.get("subId1"));
            model.addAttribute("subId2",content.get("subId2"));
            model.addAttribute("subId3",content.get("subId3"));
            model.addAttribute("plateName1",content.get("plateName1"));
            model.addAttribute("plateName2",content.get("plateName2"));
            model.addAttribute("plateName3",content.get("plateName3"));*/
        }
        return  result;
    }

    /**
     * 查询经纪人店铺相关信息
     * @param memberId
     * @param model
     * @return
     */
    public boolean getAgentInfo(String memberId, Model model){
        //查询经纪人店铺相关信息
        HashMap hashMap = new HashMap();
        hashMap.put("memberId",memberId);
        HashMap<String, Object> agentInfoMap = HttpUtil.connectApi(Constants.GET_AGENTINTERMEDIARY_INDEX, hashMap);
        if(null == agentInfoMap || null == agentInfoMap.get("content")){
            NoDataException.throwException();
        }
        LinkedTreeMap content = (LinkedTreeMap) agentInfoMap.get("content");
        String agentState = Utils.objToStr(content.get("agentState"),"0");
        if(!"1".equals(agentState)){
            return true;
        }
        model.addAttribute("agentData", content);
        model.addAttribute("subName1",content.get("subName1"));
        model.addAttribute("subName2",content.get("subName2"));
        model.addAttribute("subName3",content.get("subName3"));
        model.addAttribute("subId1",content.get("subId1"));
        model.addAttribute("subId2",content.get("subId2"));
        model.addAttribute("subId3",content.get("subId3"));
        model.addAttribute("plateName1",content.get("plateTitle1"));
        model.addAttribute("plateName2",content.get("plateTitle2"));
        model.addAttribute("plateName3",content.get("plateTitle3"));
        return false;
    }

    /**
     * 查询经纪人店铺相关信息
     * @param agentIntermediaryId
     * @param model
     * @return
     */
    public HashMap<String, Object> getIntermediaryInfo(String agentIntermediaryId, Model model){
        //查询经纪人店铺相关信息
        HashMap hashMap = new HashMap();
        hashMap.put("id",agentIntermediaryId);
        HashMap<String, Object> resultMap = (HashMap<String, Object>)HttpUtil.connectApi(Constants.QUERY_ELITE_CONSULTANT_NEW, hashMap);
        if(null == resultMap || null == resultMap.get("content")){
            NoDataException.throwException();
        }
        int status = Double.valueOf((double) resultMap.get("status")).intValue();
        model.addAttribute("shopStatus", status+"");
        LinkedTreeMap linkedTreeMap = (LinkedTreeMap) resultMap.get("content");
        model.addAttribute("intermediaryName", linkedTreeMap.get("intermediaryName"));
        model.addAttribute("companyAgentList", linkedTreeMap.get("companyAgentList"));
        model.addAttribute("saleCount", linkedTreeMap.get("saleCount"));
        model.addAttribute("rentCount", linkedTreeMap.get("rentCount"));
        model.addAttribute("shopCount", linkedTreeMap.get("shopCount"));
        model.addAttribute("officeCount", linkedTreeMap.get("officeCount"));
        HashMap<String, Object> contentMap = new HashMap();
        contentMap.put("hasHouseFlag",linkedTreeMap.get("hasHouseFlag"));
        contentMap.put("intermediaryState",linkedTreeMap.get("intermediaryState"));
        return contentMap;
    }

    // 2020/12/26 Bowen 查询经纪人店铺(overLoad)
    public HashMap<String, Object> searchAgentshop(String agencyId){
        HashMap result = this.searchAgentshop(agencyId, null);
        return result;
    }

    /**
     * 经纪人写字楼
     * @param model
     * @param agentId
     * @param params
     */
    public void agentOffice(Model model, String agentId, String params,String agentIntermediaryId){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentshop/"+agentId+"-4/" + params);
        String baseUrl = "/agent/office/"+agentId+"/";
        if(!StringUtils.isEmpty(agentIntermediaryId)){
            baseUrl = "/agentIntermediary/office/"+agentIntermediaryId+"/";
        }
        ParamsUtil.addFilterIntoModel(Constants.OFFICE_LIST, model, baseUrl, params);
        //转换url为可用参数
//        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params);
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params,Constants.OFFICE_LIST);
        paramMap.put("pageSize","10");
        if(!StringUtils.isEmpty(agentIntermediaryId)){
            paramMap.put("agentIntermediaryId",agentIntermediaryId);
            /*paramMap.put("agentIntermediaryState","1");
            paramMap.put("isShow","1");
            paramMap.put("checkState","1");*/
            //model.addAttribute("agentIntermediaryId",agentIntermediaryId);
        } else {
            paramMap.put("agencyId",agentId);
            //model.addAttribute("agentId",agentId);
        }
        paramMap.put("auction","1");
        //参数里加入模糊搜索字段
        model.addAttribute("searchName",paramMap.get("title"));
        HashMap shopList =  scriptoriumService.searchShoplist(paramMap,model,"shop",2);
        if(!StringUtils.isEmpty(agentId)){
            HashMap agent =  this.searchAgentshop(agentId,model);
            if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
                LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
                model.addAttribute("agentInfo",  content);
                model.addAttribute("shopList",content.get("officeList"));
            }
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) shopList.get("msg")));
        //model.addAttribute("agentId",agentId);
    }

    /**
     * 经纪人个人信息
     * @param model
     * @param agentId
     * @param params
     */
    public void agentDealInfo(Model model, String agentId, String params, HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/jjrsf/"+agentId+"-1");
        HashMap agent =  this.searchAgentshop(agentId, model);
        if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            model.addAttribute("agentInfo",  content);
            if (((ArrayList)content.get("saleList")).size() != 0) {
                model.addAttribute("mainArea",content.get("saleList"));
            }else if (((ArrayList)content.get("rentList")).size() != 0) {
                model.addAttribute("mainArea",content.get("rentList"));
            } else {
                model.addAttribute("mainArea","");
            }
        }
        HashMap regionMap = new HashMap();
        HashMap region = HttpUtil.connectApiString(Constants.FILTER_SCDHOUSE_REGIONS,regionMap);
        model.addAttribute("region",region.get("content"));

    }

    public void agentAskInfo(Model model, String agentId, String params, HttpServletRequest request) {
        String baseUrl = "/agent/ask/"+agentId+"/";
        ParamsUtil.addFilterIntoModel(Constants.GET_ASK_SUBDISTRICT_LIST,model,baseUrl,params);
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_ASK_SUBDISTRICT_LIST);
        paramMap.put("pageSize","10");
        paramMap.put("agentId",agentId);
        //参数里加入模糊搜索字段
        model.addAttribute("searchName",paramMap.get("subName"));
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_ASK_SUBDISTRICT_LIST,paramMap);
        HttpUtil.handleServiceList(result, "msg", "plotAskInfo", model);
        HashMap agent =  this.searchAgentshop(agentId);
        if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            model.addAttribute("agentInfo",  content);
            model.addAttribute("rentPush",content.get("rentPush"));
            model.addAttribute("rentList",content.get("rentList"));
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl+params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("agentId",agentId);
    }

    /**
     *
     * @param baseUrl
     * @param params
     * @param model
     * @param isGold  传1为找金牌经纪人    后续如果有分页或者筛选可以直接使用
     */
    public void findAgentInfoList(String baseUrl, String params, Model model, Integer isGold) {
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.FIND_AGENT_LIST);
        ParamsUtil.addFilterIntoModel(Constants.FIND_AGENT_LIST, model, baseUrl, params, paramMap);
        model.addAttribute("search",paramMap.get("realName"));
//        ParamsUtil.addSeoTitle(model, "orderKey");
        String url  = "";
        paramMap.put("pageSize","15");
        if (isGold == 1){
            paramMap.put("isGoldAgent","1");
            paramMap.put("pageSize","18");
            url = Constants.FIND_GOLD_AGENT_LIST;
        }else{
            url = Constants.FIND_AGENT_LIST;
        }
        HashMap<String, Object> result = HttpUtil.connectApiString(url,paramMap);
        ArrayList<LinkedTreeMap<String ,Object>> list = (ArrayList) result.get("content");
        Integer count = list.size();
        for (int i = 0 ; i<count;i++){
            if (!StringUtils.isEmpty(list.get(i).get("sortTel")) && !"1".equals(list.get(i).get("sortTel").toString().substring(0,1))){
                StringBuffer sortTel = new StringBuffer(list.get(i).get("sortTel").toString());
                sortTel.insert(6,"-").insert(3,"-");
                list.get(i).put("sortTel",sortTel);
            }
        }
        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "agentList", model);
        new MyPageable(paramMap, 15).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }
    /**
     * 经纪人市场观点
     * @param model
     * @param agentId
     * @param params
     */
    public void news(Model model, String agentId, String params, HttpServletRequest request){
        //  model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentshop/"+agentId+"-2/" + params);
        String baseUrl = "/agent/news/"+agentId+"/";
        ParamsUtil.addFilterIntoModel(Constants.GET_AGENT_NEWS_LIST,model,baseUrl,params);
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_AGENT_NEWS_LIST);
        paramMap.put("pageSize","10");
        paramMap.put("memberId",agentId);
        paramMap.put("categoryId","286");

        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_AGENT_NEWS_LIST,paramMap);
        HttpUtil.handleServiceList(result, "msg", "news", model);
        HashMap agent =  this.searchAgentshop(agentId);
        if( Double.valueOf(agent.get("status").toString()).intValue() ==1){
            LinkedTreeMap content = (LinkedTreeMap) agent.get("content");
            model.addAttribute("agentInfo",  content);
        }
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("agentId",agentId);

        //房产快讯
        HashMap<String, Object> hm2 = HttpUtil.connectApi(Constants.GET_FLASH_LIST, true);
        List<LinkedTreeMap<String, Object>> ltmFlash  = (List<LinkedTreeMap<String, Object>>)hm2.get("content");
        model.addAttribute("flash",ltmFlash);
    }
    /**
     * 中介管理首页
     * @param model
     * @param companyId
     * @param params
     * @date 2019.03.28
     */
    public boolean intermediaryInfo(Model model, String companyId, String params, HttpServletRequest request){
        boolean  result = false;
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/intermediary/"+companyId+".htm");
        LinkedTreeMap<String, Object> companyList = (LinkedTreeMap) HttpUtil.connectApi(Constants.QUERY_AGENT_COMPANY_INFO, new Params("companyId", companyId).get(), model);
        if(companyList != null && !StringUtils.isEmpty(companyList.get("company_name"))){
            model.addAttribute("companyList",companyList);
            HashMap<String, String> param = new HashMap<>();
            param.put("page","1");
            param.put("pageSize","10");
            param.put("auction","1");
            param.put("companyId",companyId);
            //推荐 二手房房源（2019.09.30 经纪人后台设置推荐房源）
            HashMap<String, Object> results = HttpUtil.connectApiString(Constants.AGENT_RECOMMEND_SALE_INFO, param);
            model.addAttribute("companySecList",results.get("content"));
            //推荐 租房房源
            HashMap<String, Object> re = HttpUtil.connectApiString(Constants.AGENT_RECOMMEND_RENT_INFO, param);
            model.addAttribute("companyRentList",re.get("content"));
            //推荐 商铺房源
            HashMap<String, String> paramMap = ParamsUtil.analysisInput(params,Constants.AGENT_RECOMMEND_SHOP_INFO);
            paramMap.put("pageSize","10");
            paramMap.put("page","1");
            paramMap.put("auction","1");
            paramMap.put("companyId",companyId);
            HashMap shopList =  shopService.recommendShoplist(paramMap,model,"shop",2);
            //推荐 写字楼房源
            HashMap<String, String> office_param = ParamsUtil.analysisInput(params,Constants.AGENT_RECOMMEND_OFFICE_INFO);
            office_param.put("pageSize","10");
            office_param.put("page","1");
            office_param.put("auction","1");
            office_param.put("companyId",companyId);
            HashMap shopLists =  scriptoriumService.recommendOfficelist(office_param,model,"shop",2);
            model.addAttribute("companyId",companyId);
            result = true;
        }else {
            result = false;
        }
        return result;
    }

    /**
     * 此方法用来判断中介店铺各列表页标签是否显示
     * @param model
     * @param companyId
     * @date 2019.04.04
     */
    public void intermediaryList(Model model, String companyId){
        HashMap<String, String> param = new HashMap<>();
        param.put("page","1");
        param.put("pageSize","10");
        param.put("auction","1");
        param.put("companyId",companyId);
        //二手房房源列表
        HashMap<String, Object> results = HttpUtil.connectApiString(Constants.QUERY_COMPANY_SECOND, param);
        model.addAttribute("second1",results.get("content"));
        //租房房源列表
        HashMap<String, Object> re = HttpUtil.connectApiString(Constants.GET_RENTHOUSE_LIST, param);
        model.addAttribute("rent1",re.get("content"));
        //商铺房源列表
        HashMap<String, Object> shop_result = HttpUtil.connectApiString(Constants.GET_SHOP_LIST, param);
        model.addAttribute("shop1",shop_result.get("content"));
        //写字楼房源列表
        HashMap<String, Object> office_result = HttpUtil.connectApiString(Constants.OFFICE_LIST, param);
        model.addAttribute("office1",office_result.get("content"));
    }
    /**
     * 中介管理门店页
     * @param model
     * @param companyId
     * @param params
     * @date 2019.03.28
     */
    public void intermediaryStroe(Model model, String companyId, String params, HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/intermediary/"+companyId+".htm");
        LinkedTreeMap<String, Object> companyList = (LinkedTreeMap) HttpUtil.connectApi(Constants.QUERY_AGENT_COMPANY_INFO, new Params("companyId", companyId).get(), model);
        if(null != companyList){
            model.addAttribute("companyInfoList",companyList);
        }
        this.intermediaryList(model,companyId);
    }
    /**
     * 中介管理二手房列表
     * @param model
     * @param companyId
     * @param params
     */
    public void intermediarySecond(String baseUrl,Model model,String companyId,String params ,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentcompany/"+companyId+"-1/");
//        String baseUrl = "/agent/intermediary/second/"+companyId+"/";
        String [] input = params.split("-");
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.QUERY_COMPANY_SECOND);
        ParamsUtil.addFilterIntoModel(Constants.SECOND_HOUSE_LIST, model, baseUrl, params, paramMap);
        for (int i = 0 ; i < input.length ; i++) {
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0, 1).equals("k")) {
                String minPrice = input[i].substring(1, input[i].length());
                paramMap.put("minPrice", minPrice);
                model.addAttribute("minPrice", minPrice);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0, 1).equals("x")) {
                String maxPrice = input[i].substring(1, input[i].length());
                paramMap.put("maxPrice", maxPrice);
                model.addAttribute("maxPrice", maxPrice);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0, 1).equals("y")) {
                String minArea = input[i].substring(1, input[i].length());
                paramMap.put("minArea", minArea);
                model.addAttribute("minArea", minArea);
            }
            if (!StringUtils.isEmpty(input[i]) && input[i].substring(0, 1).equals("e")) {
                String maxArea = input[i].substring(1, input[i].length());
                paramMap.put("maxArea", maxArea);
                model.addAttribute("maxArea", maxArea);
            }
        }
        int pageSize = 60;
        paramMap.put("auction","1");
        paramMap.put("pageSize", ""+pageSize);
        paramMap.put("companyId",companyId);
        agentCompanyService.exeSecondHousesFilterByCompanyId(model,paramMap);

        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.QUERY_COMPANY_SECOND, paramMap);
        HttpUtil.handleServiceList(result, "msg", "companySecList", model);
        int totalPage = 0;
        if(!StringUtils.isEmpty(result.get("msg"))){
            if(Utils.isInteger((String)result.get("msg"))){
                totalPage = Integer.valueOf((String) result.get("msg"));
            }
        }
        new MyPageable(paramMap, pageSize).putAttribute(model, baseUrl + params, totalPage);
//        model.addAttribute("companyId",companyId);
        this.intermediaryList(model,companyId);
    }

    /**
     * 中介店铺头部信息、右侧推荐经纪人、门店地址、岗位急聘
     * @param model
     * @param companyId
     */
    public void intermed_pub_info(Model model,String companyId){
        //中介店铺信息
        LinkedTreeMap<String, Object> companyList = (LinkedTreeMap) HttpUtil.connectApi(Constants.QUERY_AGENT_COMPANY_INFO, new Params("companyId", companyId).get(), model);
        if(companyList != null && !StringUtils.isEmpty(companyList.get("company_name"))){
            model.addAttribute("companyList",companyList);
        }
        //店铺经纪人
        HashMap<String, Object> results = HttpUtil.connectApi(Constants.QUERY_ELITE_CONSULTANT, new Params("companyId", companyId).get());
        model.addAttribute("eliteList",results.get("content"));
        //其他门店地址
        HashMap<String, Object> res = HttpUtil.connectApi(Constants.QUERY_OTHER_COMPANY, new Params("companyId", companyId).get());

        if(null != res.get("content")){
            ArrayList<LinkedTreeMap<String,String>> picList = (ArrayList) res.get("content");
            for(LinkedTreeMap temp :picList){
                if(!StringUtils.isEmpty(temp.get("company_id"))){
                    Double _companyId = (Double)temp.get("company_id");
                    String _companyid = _companyId.toString().replaceAll("\\.0","");
                    temp.put("company_id",_companyid);
                }
            }
            model.addAttribute("otherCompanyList",picList);
        }else{
            model.addAttribute("otherCompanyList",res.get("content"));
        }


        //岗位急聘
        HashMap<String, Object> re = HttpUtil.connectApi(Constants.GET_AGENT_RECRUIT_URGENT, new Params("companyId",companyId).get());
        HttpUtil.handleServiceList(re, "msg", "agentUrgent", model);
        model.addAttribute("companyId",companyId);
        agentRecruitService.getCompanyDetail(companyId,model);
        this.intermediaryList(model,companyId);
    }
    /**
     * 中介管理租房列表
     * @param model
     * @param companyId
     * @param params
     * @date 2019.03.29
     */
    public void intermediaryRent(String baseUrl,Model model,String companyId,String params ,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentcompany/"+companyId+"-2/");
//        String [] input = params.split("-");
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_RENTHOUSE_LIST);
        ParamsUtil.addFilterIntoModel(Constants.GET_RENTHOUSE_LIST,model,baseUrl,params,paramMap);
        //解析地址栏传过来的数据。生成限制条件 paramMap
        int pageSize = 60;
        paramMap.put("auction","1");
//        paramMap.put("pageSize","60");
        paramMap.put("pageSize", ""+pageSize);
        paramMap.put("companyId",companyId);
        agentCompanyService.exeRentHousesFilterByCompanyId(model,paramMap);


        //根据paramMap 查询接口，得到筛选后的信息
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_RENTHOUSE_LIST,paramMap);
        //去掉面积和租金上的.0
        List<LinkedTreeMap<String,Object>> results = (List<LinkedTreeMap<String,Object>>)result.get("content");
        for (int i = 0 ; i<results.size();i++) {
            results.get(i).put("area",Utils.modifyNum(results.get(i).get("area").toString()));
            results.get(i).put("price",Utils.modifyNum(results.get(i).get("price").toString()));
        }
        //将筛选后的数据存在model中
        HttpUtil.handleServiceList(result, "msg", "rents", model);
        HttpUtil.handleServiceList(result, "msg", "companyRentList", model);
        new MyPageable(paramMap,pageSize).putAttribute(model, baseUrl + params , Integer.valueOf((String) result.get("msg")));
        model.addAttribute("companyId",companyId);
        this.intermediaryList(model,companyId);
    }
    /**
     * 中介管理商铺列表
     * @param model
     * @param companyId
     * @param params
     */
    public void intermediaryShop(String baseUrl,Model model,String companyId,String params ,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentcompany/"+companyId+"-3/" + params);
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_SHOP_LIST);
        //确定是沈阳站，才会拆分出商业中写字间的楼盘信息
//        paramMap.put("site", "1");
//        ParamsUtil.addFilterIntoModel(Constants.GET_SHOP_LIST,model,baseUrl,params);
        ParamsUtil.addFilterIntoModel(Constants.GET_SHOP_LIST, model, baseUrl, params, paramMap);
        //页面排序
        shopService.order(paramMap, model);
        //商铺筛选项显示总价或者租金判定
        shopService.setTypeId(paramMap, model);
        //商铺展示版面
        shopService.setShowWay(paramMap, model);
        if (paramMap.get("typeId") == null) {
            if (paramMap.get("maxPrice") != null || paramMap.get("minPrice") != null) {
                paramMap.put("typeId", "1");
            }
        }
        // 处理结果
        paramMap.put("auction", "1");
        paramMap.put("pageSize","60");//设置为每页60条数据
        paramMap.put("companyId",companyId);
        HashMap result = shopService.searchShoplist(paramMap, model, "shop", 1);
        model.addAttribute("searchKey", paramMap.get("title"));
        model.addAttribute("minPrice", paramMap.get("minPrice"));
        model.addAttribute("maxPrice", paramMap.get("maxPrice"));
        model.addAttribute("minArea", paramMap.get("minArea"));
        model.addAttribute("maxArea", paramMap.get("maxArea"));
        agentCompanyService.exeShopHousesFilterByCompanyId(model,paramMap);
     /*   HashMap<String, Object> resu = HttpUtil.connectApiString(Constants.GET_SHOP_LIST,paramMap);
        HttpUtil.handleServiceList(resu, "msg", "shop", model);*/
        //添加分页
        new MyPageable(paramMap, Integer.valueOf(paramMap.get("pageSize").toString())).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
        model.addAttribute("companyId",companyId);
        this.intermediaryList(model,companyId);
    }
    /**
     * 中介管理写字楼列表
     * @param model
     * @param companyId
     * @param params
     */
    public void intermediaryOffice(String baseUrl,Model model,String companyId,String params ,HttpServletRequest request){
        model.addAttribute("mobileUrl", "https://m.fangxiaoer.com/agentcompany/"+companyId+"-4/" + params);
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params,Constants.OFFICE_LIST);
        ParamsUtil.addFilterIntoModel(Constants.OFFICE_LIST, model, baseUrl, params,paramMap);
        ParamsUtil.addSeoTitle(model, "");
        scriptoriumService.setTypeId(paramMap,model);//传出租出售类型控制售价和租金显示
        scriptoriumService.minAndMax(paramMap,model);//将所有最大最小参数房仔model中
        scriptoriumService.setShowWay(paramMap,model);//控制页面显示的房源数量
        scriptoriumService.order(paramMap,model);//排序控制 params,paramMap,new Params().add("shortKey","u").add("searchKey","houseTrait").get()
        //查询竞价
        paramMap.put("auction","1");//查询竞价
        paramMap.put("pageSize", "60");
        paramMap.put("companyId",companyId);
        HashMap shopLists =  scriptoriumService.searchOfficelist(paramMap,model,"shop",2);
        agentCompanyService.exeOfficeHousesFilterByCompanyId(model,paramMap);
        new MyPageable(paramMap,Integer.parseInt(paramMap.get("pageSize"))).putAttribute(model, baseUrl + params , Integer.valueOf((String) shopLists.get("msg")));
        this.intermediaryList(model,companyId);
    }

    /**
     * 经纪人所属机构是否存在 公司 概念
     * @param model
     * @param agentId
     */
    /*public void haveIntermediary(Model model,String agentId){
        if(!StringUtils.isEmpty(agentId)){
            LinkedTreeMap<String, Object> intermediary = (LinkedTreeMap) HttpUtil.connectApi(Constants.QUERY_AGENT_INTERMEDIARY, new Params("memberId", agentId).get(), model);
            if( intermediary != null && intermediary.size() > 0){
                model.addAttribute("intermeStore",intermediary);
            }
        }
    }*/

    /**
     * 驿站经纪人
     * @param houseId
     * @return
     */
    public HashMap<String, Object> viewRecommandYiAgent(Integer houseId){
        return HttpUtil.connectApi(Constants.RECOMMAND_YI_AGENT, new Params("houseId", houseId).get());
    }

}
