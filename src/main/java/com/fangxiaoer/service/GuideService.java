package com.fangxiaoer.service;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Utils;
import com.fangxiaoer.model.Guide;
import com.fangxiaoer.model.Loan;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;

/**
 * Created by Administrator on 2017/8/23.
 */
@Service
public class GuideService {
    /**
     * 保存需求定制
     * @param guide
     * @return
     */
    @Transactional
    public HashMap saveGuide(Guide guide){
        HashMap result = new HashMap();
        if(guide.getType()==0){
            result.put("status",0);
            result.put("msg","系统异常");
        }else {
            if(guide.getType() == 4){
                guide.setType(8);
            }
            //1、新房 2、二手房 3、租房 8、商铺
            if(guide.getType() == 1 || guide.getType() == 2 || guide.getType() == 3 ||guide.getType() == 8){
                result = HttpUtil.connectApi(Constants.HELP_SEARCH_HOUSE, Utils.transBean2Map(guide));
            }else {
                result = HttpUtil.connectApi(Constants.ADD_OTHERGUIDE, Utils.transBean2Map(guide));
            }
        }
        return  result;
    }

    /**
     * 保存贷款相关
     * @param loan
     * @return
     */
    @Transactional
    public HashMap saveLoan(Loan loan){
        HashMap result =  HttpUtil.connectApi(Constants.ADDLOAN, Utils.transBean2Map(loan));
        return  result;
    }
    /**
     * 保存项目咨询
     * @param ask
     * @return
     */
    @Transactional
    public HashMap saveASk(HashMap ask){
        HashMap result = HttpUtil.connectApi(Constants.ADD_ASK, ask);
        return  result;
    }

    /**
     * 个人向经纪人咨询
     * @param ask
     * @return
     */
    @Transactional
    public HashMap save(HashMap ask){
        HashMap result = HttpUtil.connectApi(Constants.MY_TOASK_AGENT, ask);
        return  result;
    }
    /**
     * 获取支付订单
     * @param ask
     * @return
     */
    @Transactional
    public HashMap getPayOrder(HashMap ask){
        HashMap result = HttpUtil.connectApi(Constants.GET_PAY_ORDER, ask);
        return  result;
    }


    public HashMap<String, Integer> serviceGuarantee(){
        HashMap<String, Integer> value = new HashMap<>();
        double t1 = Math.random();
        int unitPrice = (int)(t1*9000 + 4000);
        value.put("unitPrice", unitPrice);
        double t2 = Math.random();
        int area = (int)(t2*160 + 40);
        int totalPrice = unitPrice * area;
        value.put("totalPrice", totalPrice);
        if(area >= 90){
            value.put("tax", (int)(totalPrice*1.5/100));
        }else{
            value.put("tax", totalPrice*1/100);
        }
        return value;
    }

}
