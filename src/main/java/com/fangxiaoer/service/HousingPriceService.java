package com.fangxiaoer.service;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.common.ParamsUtil;
import com.google.gson.internal.LinkedTreeMap;
import com.fangxiaoer.common.*;
import com.fangxiaoer.model.HousingPriceEntity;
import com.fangxiaoer.model.SecondHouse;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * Created by Administrator.
 *
 * <AUTHOR>
 * @Date 2018/6/12 0012 下午 1:36
 */
@Service
public class HousingPriceService {
    @Autowired
    private  PlotService plotService;
    /**
     * 房价列表页
     * @param baseUrl
     * @param params
     * @param model
     */
    public void getHousingPriceList(String baseUrl, String params, Model model){
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.VIEW_HOUSE_PRICE_LIST);
        ParamsUtil.addFilterIntoModel(Constants.VIEW_HOUSE_PRICE_LIST, model, baseUrl, params, paramMap);
        ParamsUtil.addSeoTitle(model, "orderKey");
        String [] input = params.split("-");
        if( params.contains("j")){
            model.addAttribute("chooseType",3);
        }else if(params.contains("r")){
            model.addAttribute("chooseType",2);
        }else{
            model.addAttribute("chooseType",1);
        }
        HashMap<String, Object> re = HttpUtil.connectApiString(Constants.VIEW_HOUSE_PRICE_LIST, paramMap);
        LinkedTreeMap<String ,Object> content = (LinkedTreeMap<String, Object>) re.get("content");
        if (StringUtils.isEmpty(content.get("longtitude"))){
            content.put("longtitude",123.439941);
            content.put("latitude",41.798142);
        }
        if (StringUtils.isEmpty(content.get("zoomLevel"))){
            content.put("zoomLevel",13);
        }
        model.addAttribute("pathParm",params);
        model.addAttribute("housePriceList",content);
        model.addAttribute("subUpList",HttpUtil.connectApi(Constants.GET_RENTVILLAGE_LIST, new Params("orderType",3).get(),model));//降序 涨
        model.addAttribute("subDownList", HttpUtil.connectApi(Constants.GET_RENTVILLAGE_LIST, new Params("orderType",4).get(),model));//升序 跌
        //  小区热搜排行
        model.addAttribute("hotSubList",HttpUtil.connectApi(Constants.VIEW_HOT_SUB_LIST,new Params().get(),model));
        //朝向
        HashMap<String, Object> r = HttpUtil.connectApi(Constants.FILTER_HOUSE_FORWARD, new Params().get());
        model.addAttribute("forward",r.get("content"));
        //户型
        HashMap<String, Object> t = HttpUtil.connectApi(Constants.FILTER_LAYOUT, new Params().get());
        model.addAttribute("layout",t.get("content"));

    }

    /**
     * 房价列表详情页
     * @param
     * @param housingPriceEntity
     * @param model
     */
    public void housingPriceDetail(Model model,HousingPriceEntity housingPriceEntity){
        ParamsUtil.addSeoTitle(model, "orderKey");
        HashMap params = new HashMap();
        params.put("subId", housingPriceEntity.getSubId());
        params.put("roomId", housingPriceEntity.getRoomId());
        params.put("forwardType", housingPriceEntity.getForwardType());
        params.put("area", housingPriceEntity.getArea());
        params.put("currentFloor", housingPriceEntity.getCurrentFloor());
        params.put("totalFloor", housingPriceEntity.getTotalFloor());
        HashMap<String, Object> housingDetail = HttpUtil.connectApi(Constants.VIEW_SUB_MATH, params);
        LinkedTreeMap result = (LinkedTreeMap<String, Object>) housingDetail.get("content");
//        if(result.get("currentFloor").equals("null")){result.put("currentFloor","-");}//所在楼层为空时给固定值
//        if(result.get("totalFloor").equals("null")){result.put("totalFloor","-");}
        model.addAttribute("subMath", result);
        model.addAttribute("subId",housingPriceEntity.getSubId());

        HashMap map = new HashMap();
        LinkedTreeMap<String, Object> getAdvertising = (LinkedTreeMap<String, Object>) model.asMap().get("v");
        map.put("regionId", getAdvertising.get("regionId"));
        if(null != getAdvertising.get("unitPrice")){
            Double unitPrice = (Double) getAdvertising.get("unitPrice");
            if(unitPrice > 0.0) {
                map.put("price", getAdvertising.get("unitPrice"));
            }
        }
        //大数据
        HashMap<String, String> param = new HashMap<>();
        String regionName = getAdvertising.get("regionName").toString();
        //计算6周前的日期
        Date lastDate = plotService.getOffsetMonthDate(new Date(), 6);
        //计算开始时间是当年的第几周
        Calendar c=Calendar.getInstance();
        c.setTime(lastDate);
        int betweenWeek = c.get(Calendar.WEEK_OF_YEAR) > 52 ? 52 : c.get(Calendar.WEEK_OF_YEAR);
        String lastDateUse = plotService.convertDateToString(lastDate,"yyyy")+"-"+betweenWeek;
        param.put("startWeek", lastDateUse);
        ArrayList resutList = new ArrayList();
        //获取沈阳均价
        resutList.add(plotService.citytrending(param));
        param.put("region",regionName.replaceAll("新区","").replaceAll("区域","").replaceAll("区",""));
        //获取铁西均价
        resutList.add(plotService.citytrending(param));
        param.remove("region");
        //获取具体小区均价
        param.put("community", getAdvertising.get("title").toString());
        resutList.add(plotService.citytrending(param));
        model.addAttribute("data", resutList);
    }

    public HashMap<String, Object> calForLoan(Double priceA, Double priceB, Integer month1,Integer month2, Integer loanType, Double discount, Integer countMethod, Double basePoint){
        return HttpUtil.connectApi(Constants.CALCULATION_FOR_LOAN, new Params("priceA", priceA).add("priceB", priceB)
                .add("month1", month1).add("month2", month2).add("loanType", loanType).add("discount", discount)
                .add("countMethod", countMethod).add("basePoint", basePoint).get());
    }

    public HashMap<String, Object> viewLoanFilter(Integer month, Double discount){
        return HttpUtil.connectApi(Constants.LPR_LOAN_RATE, new Params("month", month).add("discount", discount).get());
    }

    public HashMap<String, Object> LPRHistoryFilter() {
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.LPR_HISTORY, new Params().get());
        return result;
    }
}
