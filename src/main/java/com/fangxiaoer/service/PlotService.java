package com.fangxiaoer.service;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.common.Utils;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.Rent;
import com.fangxiaoer.model.SecondHouse;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.internal.LinkedTreeMap;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/***
 *@Author: CDS
 *@Description:
 *@Data: Create in 15:282018-04-10
 *
 **/
@Service
public class PlotService {
    public void getPlotInfo(String sunId, Model model) {
        HashMap hashMap = new HashMap();
        hashMap.put("subId", sunId);
        HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.GET_RENTVILLAGE_DETAIL, hashMap);
        if(null == villageDetail || null == villageDetail.get("content") ){
            NoDataException.throwException();
        }
        LinkedTreeMap result = (LinkedTreeMap<String, Object>) villageDetail.get("content");
        model.addAttribute("v", result);
        model.addAttribute("subId", sunId);
    }


    public void viewNewSubBasicInfo(String subId, Model model){
        HashMap<String, Object> villageDetail = HttpUtil.connectApi(Constants.NEW_SUB_DETAIL, new Params("subId", subId).get());
        if(null == villageDetail || null == villageDetail.get("content") ){
            NoDataException.throwException();
        }
        LinkedTreeMap result = (LinkedTreeMap<String, Object>) villageDetail.get("content");
        model.addAttribute("v", result);
        model.addAttribute("subId", subId);
    }

    public void fetchNewSubPhotos(String subId, Model model){
        HashMap<String, Object> subPics = HttpUtil.connectApi(Constants.NEW_SUB_PHOTOS, new Params("subId", subId).get());
        List<LinkedTreeMap<String, Object>> subPic = (List<LinkedTreeMap<String, Object>>) subPics.get("content");
        model.addAttribute("vp", subPic);
    }


    public void getPlotImages(String subId, Model model) {
        HashMap hashMap = new HashMap();
        hashMap.put("subId", subId);
        HashMap<String, Object> villagePic = HttpUtil.connectApi(Constants.GET_DISTRICT_PIC, hashMap);
        List<LinkedTreeMap<String, Object>> villPic = (List<LinkedTreeMap<String, Object>>) villagePic.get("content");
        model.addAttribute("vp", villPic);
        getPlotInfo(subId, model);
    }


    public void getPlotAsk(String subId, Integer page, String baseUrl, Model model, HttpSession session) {
        if (StringUtils.isEmpty(page)) page = 1;
        Params params = new Params().add("subId", subId).add("page", page).add("pageSize", 10);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.GET_ALL_ASK_SUBDISTRICT_LIST, params.get());
        HttpUtil.handleServiceList(result, "msg", "plotAskInfo", model);
        new MyPageable(page, 10).putAttribute(model, baseUrl + subId + "/", Integer.valueOf((String) result.get("msg")));
        getPlotInfo(subId, model);
        if(!StringUtils.isEmpty(session.getAttribute("sessionId"))){
            Params param = new Params().add("subId", subId).add("sessionId",session.getAttribute("sessionId"));
            HashMap<String, Object> results = HttpUtil.connectApi(Constants.INTERLOCUTION_LIST, param.get());
            int status = Double.valueOf((double) results.get("status")).intValue();
            model.addAttribute("isExist",results.get("msg"));
            if (status == 1 && results.get("msg").toString().equals("1")) {
                LinkedTreeMap<String, Object> content = (LinkedTreeMap<String, Object>) results.get("content");
                model.addAttribute("myPlotAskInfo", content);
            }
        }

    }

    /**
     * 小区问答详情页的查询服务
     * @param askId
     * @param page
     * @param baseUrl
     * @param model
     */
    public void getAskDetail(String askId, Integer page, String baseUrl, Model model, HttpServletRequest request) {
        if (StringUtils.isEmpty(page)) page = 1;
        Params params = new Params().add("askId", askId).add("page", page).add("pageSize", 10).add("platform","sy");
        HashMap<String, Object> _result = HttpUtil.connectApi(Constants.GET_ASK_SUBDISTRIC_DETAIL, params.get());
        LinkedTreeMap<String, Object> result = (LinkedTreeMap)_result.get("content");
        //2018-05-08循环遍历获得的小区问答回答内容并隐匿敏感字段
        model.addAttribute("isAskUser",0);
        if (result.get("mobile").equals(request.getSession().getAttribute("phoneNum"))){
            model.addAttribute("isAskUser",1);
        }
        List<LinkedTreeMap<String, Object>> objReply = new ArrayList<>();
        if(null != result && null != result.get("reply")){
            List<LinkedTreeMap<String, Object>> orgReply=  (List<LinkedTreeMap<String, Object>>) result.get("reply");
            Iterator  iteration  = orgReply.iterator();
            while(iteration.hasNext()){
                //获得一个回答的值
                LinkedTreeMap<String, Object> one  = (LinkedTreeMap)iteration.next();
                String realName = "";
                String memberType ="";
                //获得名字
                if(!StringUtils.isEmpty(one.get("realName"))){
                    realName = (String)one.get("realName");
                }
                //获得经纪人类型;
                if(!StringUtils.isEmpty(one.get("memberType"))){
                    memberType = (String)one.get("memberType");
                }
                //如果不是经纪人 需要对电话和姓名进行隐藏
                if(!memberType.equals("2")){
                    realName = this.getUserNameWithStar(realName);
                }
                //把头像图片的内容再次做个处理
                if(StringUtils.isEmpty(one.get("pic"))){
                    one.put("pic","");
                }
                //翻滚吧字符串！
                one.put("realName",realName);
                objReply.add(one);
            }
            //把东西给出去吧！
            result.put("reply",objReply);
        }
        if(!StringUtils.isEmpty(result.get("realName"))){
            result.put("realName" ,this.getUserNameWithStar((String)result.get("realName")));
        }
        //以上为必要的转换业务代码 end
        int status = Double.valueOf((double) _result.get("status")).intValue();
//        LinkedTreeMap<String, Object> content = (LinkedTreeMap<String, Object>) result.get("content");
        if (status == 1) {
            model.addAttribute("plotAskDetail", result);
        }
//        HttpUtil.handleServiceList(result, "msg", "plotAskDetail", model);
        new MyPageable(page, 10).putAttribute(model, baseUrl + askId + "/getAskDetail/", Integer.valueOf((String) result.get("count")));
        getPlotInfo(result.get("subId").toString(), model);
    }

    public void getPlotExpertInterpretation(String subId, Integer page, String baseUrl, Model model) {
        getPlotInfo(subId, model);
        if (StringUtils.isEmpty(page)) page = 1;
        Params params = new Params().add("subId", subId).add("page", page).add("pageSize", 20);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.GET_EXPERT_SUBDUSTRICTS, params.get());
        HttpUtil.handleServiceList(result, "msg", "expertInterpretations", model);
        new MyPageable(page, 20).putAttribute(model, baseUrl + subId + "/", Integer.valueOf((String) result.get("msg")));
    }

    public HashMap<String, Object> addAsk(Integer subId, String subName, String content, String sessionId) {
        Params params = new Params().add("subId", subId).add("subName", subName).add("content", content).add("sessionId", sessionId);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.ADD_ASK_SUBDISTRICT, params.get());
        return result;
    }

    public HashMap addReply(String askId, String content, String sessionId) {
        Params params = new Params().add("askId", askId).add("content", content).add("sessionId", sessionId);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.ADD_REPLY_SUBDISTRICT, params.get());
        return result;
    }

    public void getAskNoReplyList(String subId, Integer page, Model model) {
        if (StringUtils.isEmpty(page)) page = 1;
        Params params = new Params().add("page", page).add("type", 1).add("pageSize", 5).add("subId", subId);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.GET_ASKS_NO_REPLY, params.get());
        String[] msgs = result.get("msg").toString().split("/");
        if (msgs.length == 3) {
            result.put("msg", msgs[2]);
            model.addAttribute("count",msgs[2]);
        }
        HttpUtil.handleServiceList(result, "msg", "plotAskNoReplyList", model);
        new MyPageable(page, 5).putAttribute(model, "/saleVillages/"+subId + "/getAskNoReply/", Integer.valueOf((String) result.get("msg")));
        getPlotInfo(subId, model);
    }

    public void getPlotSupportInfo(String subId, Model model) {
        getPlotInfo(subId, model);
        //获取四个同小区的租房房源
        Rent rent = new Rent();
        rent.setPageSize(4);
        rent.setSubId(subId);
        HashMap<String, Object> renthouses = HttpUtil.connectApi(Constants.VIEW_RENT_OHTER, Utils.transBean2Map(rent));
        List<LinkedTreeMap<String, Object>> renthouse = (List<LinkedTreeMap<String, Object>>) renthouses.get("content");
        for (int i = 0; i < renthouse.size(); i++) {
            renthouse.get(i).put("price", Utils.modifyNum(renthouse.get(i).get("price").toString()));
        }
        model.addAttribute("rent", renthouse);

        //获取似得同小区的二手房房源
        SecondHouse secondHouse = new SecondHouse();
        secondHouse.setPageSize(4);
        secondHouse.setSubId(subId);
        HashMap<String, Object> secondhouses = HttpUtil.connectApi(Constants.VIEW_SCDHOUSE_OTHER, Utils.transBean2Map(secondHouse));
        List<LinkedTreeMap<String, Object>> secondhouse = (List<LinkedTreeMap<String, Object>>) secondhouses.get("content");
        for (int i = 0; i < secondhouse.size(); i++) {
            secondhouse.get(i).put("price", Utils.modifyNum(secondhouse.get(i).get("price").toString()));
        }
        model.addAttribute("secondhouse", secondhouse);
    }

    public void getPlotDetail(String subId, Model model) {
        HashMap map = new HashMap();
        HashMap<String, Object> lottery = HttpUtil.connectApiString(Constants.SCDHOUSE_LOTTERY, map);
        int status = Double.valueOf((double) lottery.get("status")).intValue();
        model.addAttribute("lottery", "");
        if (status == 1 && !StringUtils.isEmpty(lottery.get("content"))) {
            LinkedTreeMap<String, Object> contents = (LinkedTreeMap<String, Object>) lottery.get("content");
            model.addAttribute("lottery", contents.get("content"));
        }
        LinkedTreeMap<String, Object> getAdvertising = (LinkedTreeMap<String, Object>) model.asMap().get("v");
        if(null == getAdvertising){
            NoDataException.throwException();
        }
        map.put("regionId", getAdvertising.get("regionId"));
        if(null != getAdvertising.get("unitPrice")){
           Double unitPrice = (Double) getAdvertising.get("unitPrice");
            if(unitPrice > 0.0) {
                map.put("price", getAdvertising.get("unitPrice"));
            }
        }
        map.put("pageSize", 5);
        map.put("except", subId);
        HashMap<String, Object> tlist = HttpUtil.connectApiString(Constants.VIEW_NEW_SUBDISTRICT, map);
        model.addAttribute("similarities", tlist.get("content"));
        //大数据
        HashMap<String, String> params = new HashMap<>();
        String regionName = getAdvertising.get("regionName").toString();
		//计算6周前的日期
        Date lastDate = getOffsetMonthDate(new Date(), 6);
        //计算开始时间是当年的第几周
        Calendar c=Calendar.getInstance();
        c.setTime(lastDate);
        int betweenWeek = c.get(Calendar.WEEK_OF_YEAR) > 52 ? 52 : c.get(Calendar.WEEK_OF_YEAR);
        String lastDateUse = convertDateToString(lastDate,"yyyy")+"-"+betweenWeek;
        params.put("startWeek", lastDateUse);
        ArrayList resutList = new ArrayList();
        //获取沈阳均价
        resutList.add(citytrending(params));
        params.put("region",regionName.replaceAll("新区","").replaceAll("区",""));
        //获取铁西均价
        resutList.add(citytrending(params));
        params.remove("region");
        //获取具体小区均价
        params.put("community", getAdvertising.get("title").toString());
        resutList.add(citytrending(params));
        model.addAttribute("data", resutList);
        if(regionName.indexOf("周边") != -1){
            model.addAttribute("isRound",2);
        }else{
            model.addAttribute("isRound",1);
        }
        //小区推荐专家
        HashMap plotParam = new HashMap();
        if(!StringUtils.isEmpty(getAdvertising.get("title"))){
            plotParam.put("title",getAdvertising.get("title")) ;
        }
        HashMap<String, Object> experts = HttpUtil.connectApi(Constants.QUERY_PLOT_EXPERT,plotParam);
        List<LinkedTreeMap<String, Object>> plotExpertList = (List<LinkedTreeMap<String, Object>>)experts.get("content");
        if(null != plotExpertList){
            for(LinkedTreeMap<String, Object> object : plotExpertList){
                Double memberid = (Double)object.get("memberId");
                if(null != memberid){
                    object.put("_memberId",memberid.toString().replaceAll("\\.0",""));
                }
            }
        }
        model.addAttribute("plotId",experts.get("msg"));
        model.addAttribute("plotExpertList",plotExpertList);
    }
    /**
     *date转string
     * @param date
     * @param format
     * @return
     */
    public static String convertDateToString(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }
    /**
     * 根据原来的时间（Date）获得相对偏移 N 月的时间（Date）

     * @param protoDate 原来的时间（java.util.Date）

     * @param monthOffset（向前移正数，向后移负数）

     * @return 时间（java.util.Date）

     */
    public static Date getOffsetMonthDate(Date protoDate, int monthOffset) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(protoDate);
        //		cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - monthOffset);错误写法
        cal.add(Calendar.MONTH, -monthOffset);
        return cal.getTime();
    }


    public final static String HOST = "http://api-ppe-salehouse.fangxiaoer.com/";
    /**
     * 房产大数据接口（逻辑可用）

     * @param params
     * @return
     */
    public static HashMap citytrending(@RequestParam Map<String, String> params) {
        HashMap<String,Object> result = new HashMap();
        try {
            StringBuffer json = new StringBuffer();

            if(params.get("region")!= null && params.get("region")!= ""){
                json.append("{\"region\": \"").append(params.get("region")).append("\",\"startWeek\": \"")
                        .append( params.get("startWeek")).append("\"}");
                result.put("keyName",params.get("region"));
            }else if(params.get("community")!= null){
                json.append("{\"community\": \"").append(params.get("community")).append("\",\"startWeek\": \"")
                        .append( params.get("startWeek")).append("\"}");
                result.put("keyName",params.get("community"));
            }else if(params.get("innerRegion")!= null && params.get("innerRegion")!= ""){
                json.append("{\"innerRegion\": \"").append(params.get("innerRegion")).append("\",\"startWeek\": \"")
                        .append( params.get("startWeek")).append("\"}");
                result.put("keyName",params.get("innerRegion"));
            }else{
                json = json.append( "{\"city\": \"").append("sy").append("\",\"startWeek\": \"")
                        .append( params.get("startWeek")).append("\"}");
                result.put("keyName","沈阳");
            }
//            RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), json.getBytes());
            okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(MediaType.parse("application/json"), json.toString().getBytes());
            Request request = new Request.Builder().url(HOST +"sy/salehouse/v1/trending/weekly").post(requestBody).build();
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String data = response.body().string();
                result.put("highChartsData",data);

            } else {
                throw new IOException("Unexpected code " + response);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 小工具转名字带星星
     * @param realName
     * @return
     */
    private String getUserNameWithStar(String realName){
        //判断内容长度
        int length = realName.length();
        if(length  == 2){
            realName = realName.substring(0,1) + "***";
        }else if(length >= 3){
            realName = realName.substring(0,1) + "***" + realName.substring(realName.length() -1);
        }else{
            realName = realName;
        }
        return realName;
    }

    public HashMap<String,Object> acceptReplay(String  sessionId, Integer replyId, Integer askId) {
        Params params = new Params().add("sessionId", sessionId).add("replyId", replyId).add("askId", askId);
        HashMap<String, Object> result =HttpUtil.connectApi(Constants.PLOT_ACCEPT_REPLAY, params.get());
        return result;
    }
}
