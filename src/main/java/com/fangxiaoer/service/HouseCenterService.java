package com.fangxiaoer.service;

import com.fangxiaoer.common.*;
import com.fangxiaoer.error.NoDataException;
import com.fangxiaoer.model.LoginResultFilter;
import com.fangxiaoer.model.Mall;
import com.fangxiaoer.model.News;
import com.fangxiaoer.model.NewsDetail;
import com.fangxiaoer.model.base.MyPageable;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Administrator on 2017/10/9.
 */
@Service
public class HouseCenterService {
    //购房攻略列表
    public void getStrategy(String baseUrl, String params, Model model,HttpServletRequest request){
        //资讯的购房攻略列表
        ParamsUtil.addFilterIntoModel(Constants.GET_NEWS_LIST,model,baseUrl,params);
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.GET_NEWS_LIST);
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_NEWS_LIST,paramMap);
        HttpUtil.handleServiceList(result, "msg", "news", model);
        new MyPageable(paramMap, 10).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));

        //房产快讯
        HashMap<String, Object> hm2 = HttpUtil.connectApi(Constants.GET_FLASH_LIST, true);
        List<LinkedTreeMap<String, Object>> ltmFlash  = (List<LinkedTreeMap<String, Object>>)hm2.get("content");
        model.addAttribute("flash",ltmFlash);
        //猜你喜欢
/*        HashMap<String, Object> guessLikes = HttpUtil.connectApi(Constants.GUESS_LIKE);
        List<LinkedTreeMap<String, Object>> guessLike = (List<LinkedTreeMap<String, Object>>)guessLikes.get("content");
        if (guessLike != null) {
            for (LinkedTreeMap map:guessLike) {
                String houseId = map.get("TargetUrl").toString();
                String regEx="[^0-9]";
                Pattern p = Pattern.compile(regEx);
                Matcher m = p.matcher(houseId);
                map.put("TargetUrl",m.replaceAll("").trim());
            }
        }
        model.addAttribute("guessLike",guessLike);*/
        //广告
        model.addAttribute("advert1",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 35).get(), true).get("content"));
        model.addAttribute("advert2",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 36).get(), true).get("content"));
    }
    /**
     * 积分商城列表页
     */
    public void getPointsMall(Model model){
        Mall mall = new Mall();
        mall.setPageSize(999);
        HashMap<String, Object> hm  = HttpUtil.connectApi(Constants.GET_HOUSEKEEP_MALLLIST, Utils.transBean2Map(mall));
        HashMap<String, Object> hm1 = HttpUtil.connectApi(Constants.FILTER_HOUSEKEEP_MALLTYPE);
        List<LinkedTreeMap<String, Object>> ltm = (List<LinkedTreeMap<String, Object>>)hm.get("content");
        List<LinkedTreeMap<String, Object>> ltm1 = (List<LinkedTreeMap<String, Object>>)hm1.get("content");
        model.addAttribute("mall",ltm);
        model.addAttribute("title",ltm1);
        model.addAttribute("msg",hm.get("msg"));
        model.addAttribute("sortId",0);

        HashMap<String,Object> users = HttpUtil.connectApi(Constants.GET_EXCHANGE_CUSTOMRE);
        List<LinkedTreeMap<String, Object>> user = (List<LinkedTreeMap<String, Object>>)users.get("content");
        model.addAttribute("user",user);

        //中奖用户
        HashMap<String, Object> midUsers  = HttpUtil.connectApi(Constants.GET_EXCHANGE_CUSTOMRE);
        List<LinkedTreeMap<String, Object>> midUser = (List<LinkedTreeMap<String, Object>>)midUsers.get("content");
        //将中奖用户电话号码中间四位换成****
        for (int i = 0 ; i < midUser.size() ; i++) {
            String phone1 = midUser.get(i).get("CommberPhone").toString().substring(0,3);
            String phone2 ="****";
            String phone3 = midUser.get(i).get("CommberPhone").toString().substring(7);
            String phone = phone1+phone2+phone3;
            midUser.get(i).put("CommberPhone",phone);
        }
        model.addAttribute("midUser",midUser);
    }
    /**
     * 积分商城分类查询
     */
    public void getPointsMallBySort(String inputParams,Model model) {
        Mall mall = new Mall();
        mall.setSort(inputParams);
        HashMap<String, Object> hm  = HttpUtil.connectApi(Constants.GET_HOUSEKEEP_MALLLIST, Utils.transBean2Map(mall));
        HashMap<String, Object> hm1 = HttpUtil.connectApi(Constants.FILTER_HOUSEKEEP_MALLTYPE);
        List<LinkedTreeMap<String, Object>> ltm = (List<LinkedTreeMap<String, Object>>)hm.get("content");
        List<LinkedTreeMap<String, Object>> ltm1 = (List<LinkedTreeMap<String, Object>>)hm1.get("content");
        model.addAttribute("mall",ltm);
        model.addAttribute("title",ltm1);
        model.addAttribute("msg",hm.get("msg"));
        model.addAttribute("sortId",inputParams);

        //中奖用户
        HashMap<String, Object> midUsers  = HttpUtil.connectApi(Constants.GET_EXCHANGE_CUSTOMRE);
        List<LinkedTreeMap<String, Object>> midUser = (List<LinkedTreeMap<String, Object>>)midUsers.get("content");
        //将中奖用户电话号码中间四位换成****
        for (int i = 0 ; i < midUser.size() ; i++) {
            String phone1 = midUser.get(i).get("CommberPhone").toString().substring(0,3);
            String phone2 ="****";
            String phone3 = midUser.get(i).get("CommberPhone").toString().substring(7);
            String phone = phone1+phone2+phone3;
            midUser.get(i).put("CommberPhone",phone);
        }
        model.addAttribute("midUser",midUser);
    }

    /**
     * 积分商城详情
     */
    public void getGoods(Integer id,Model model){
        LinkedTreeMap<String, Object> ltm = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_HOUSEKEEP_MALLDETAIL, new Params("id",id).get(), model);
        model.addAttribute("id",id);
        model.addAttribute("goods",ltm);
        //中奖用户
        HashMap<String, Object> midUsers  = HttpUtil.connectApi(Constants.GET_EXCHANGE_CUSTOMRE);
        List<LinkedTreeMap<String, Object>> midUser = (List<LinkedTreeMap<String, Object>>)midUsers.get("content");
        //将中奖用户电话号码中间四位换成****
        for (int i = 0 ; i < midUser.size() ; i++) {
            String phone1 = midUser.get(i).get("CommberPhone").toString().substring(0,3);
            String phone2 ="****";
            String phone3 = midUser.get(i).get("CommberPhone").toString().substring(7);
            String phone = phone1+phone2+phone3;
            midUser.get(i).put("CommberPhone",phone);
        }
        model.addAttribute("midUser",midUser);
    }

    //小二管家列表页
    public void getHouseKeeper(Model model){
        HashMap<String, Object> keeperMap = HttpUtil.connectApi(Constants.GET_HOUSEKEEPER);
        List<LinkedTreeMap<String, Object>> keeperList = (List<LinkedTreeMap<String, Object>>)keeperMap.get("content");
        model.addAttribute("housekeeper",keeperList);
    }

    //活动列表页
    public static final String NEWS_URL = "/activities/";
    public void getActivities(String input, Model model){
        //活动列表
        News news = new News();
        HashMap<String,String> result = ParamsUtil.analysisInput(input);
        if(!StringUtils.isEmpty(result.get("page"))){
            news.setPage(Integer.valueOf(result.get("page")));
        }
        news.setCategoryId("150");
/*        HashMap hashMap = new HashMap();
            hashMap.put("page",Integer.valueOf(result.get("page")));
        hashMap.put("categoryId","151");//Utils.transBean2Map(news)  hashMap*/
        HashMap<String, Object> hm = HttpUtil.connectApi(Constants.GET_NEWS_LIST,Utils.transBean2Map(news));
        List<LinkedTreeMap<String, Object>> ltm = (List<LinkedTreeMap<String, Object>>)hm.get("content");
        //楼盘视频
        List<LinkedTreeMap<String, Object>> ltmVideo = (List<LinkedTreeMap<String, Object>>)HttpUtil.connectApi(Constants.GET_VIDEO_LIST,new Params("pageSize",1).get()).get("content");
        model.addAttribute("video",ltmVideo);

        //房产快讯
        HashMap<String, Object> hm2 = HttpUtil.connectApi(Constants.GET_FLASH_LIST, true);
        List<LinkedTreeMap<String, Object>> ltmFlash  = (List<LinkedTreeMap<String, Object>>)hm2.get("content");
        int i = 0 ;
        for(LinkedTreeMap map:ltmFlash) {
            String str = map.get("murl").toString();
            String regEx = "[^0-9]";
            Pattern p = Pattern.compile(regEx);
            Matcher m = p.matcher(str);
            String count = m.replaceAll("").trim();
            ltmFlash.get(i).put("murl",count);
            i++;
        }
        model.addAttribute("flash",ltmFlash);

        model.addAttribute("news",ltm);
        model.addAttribute("msg",hm.get("msg"));
        Integer number;
        if(!StringUtils.isEmpty(result.get("page"))){
            number = Integer.valueOf(result.get("page"));
        }else{
            number = 1;
        }
        int count = Integer.valueOf(hm.get("msg").toString());
        news.putAttribute(model, NEWS_URL+input, count, number);

        model.addAttribute("advert1",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 35).get(), true).get("content"));
        model.addAttribute("advert2",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 36).get(), true).get("content"));
    }

    /**
     * 资讯详情页
     */
    public String getActivity(String id, Model model){
        LinkedTreeMap<String,Object> ltm = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.GET_NEWS_DETAIL, new Params("newsId",id).get(), model);
        model.addAttribute("id",id);
        if(!StringUtils.isEmpty(ltm.get("redirectUrl"))){
            return ltm.get("redirectUrl").toString();
        }
        Gson gson = new Gson();
        NewsDetail newsDetail = gson.fromJson(gson.toJson(ltm), NewsDetail.class);
        newsDetail.setContent(newsDetail.getContent().replaceAll("&lt;", "<").replaceAll("&quot;", "\"").replaceAll("&gt;", ">"));
        //newsDetail.get
        model.addAttribute("newsinfo",newsDetail);
        //相关阅读
        model.addAttribute("linkLIst",newsDetail.getReletedInfo());
        //楼盘视频
        List<LinkedTreeMap<String, Object>> ltmVideo = (List<LinkedTreeMap<String, Object>>)HttpUtil.connectApi(Constants.GET_VIDEO_LIST,new Params("pageSize",1).get()).get("content");
        model.addAttribute("video",ltmVideo);

        //房产快讯
        HashMap<String, Object> hm2 = HttpUtil.connectApi(Constants.GET_FLASH_LIST, true);
        List<LinkedTreeMap<String, Object>> ltmFlash  = (List<LinkedTreeMap<String, Object>>)hm2.get("content");
        model.addAttribute("flash",ltmFlash);

        model.addAttribute("advert1",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 37).get(), true).get("content"));
        model.addAttribute("advert2",HttpUtil.connectApi(Constants.GUESS_LIKE,new Params("locationId", 38).get(), true).get("content"));
        return null;
    }

    /**
     * 获取vip信息
     * @param request
     */
    public void getVipInfo(HttpServletRequest request, Model model, HttpServletResponse response) {
        HttpSession session = request.getSession();
        String sessionId = (String) session.getAttribute("sessionId");
        if(!StringUtils.isEmpty(sessionId)) {
            HashMap<String, Object> result = HttpUtil.connectApi(Constants.VIEW_MEMBER_INFO, new Params("sessionId", sessionId).get());
            Integer status = Double.valueOf(result.get("status").toString()).intValue();
            if (status == 1) {
                LinkedTreeMap<String, Object> content = (LinkedTreeMap<String, Object>) result.get("content");
                Gson gson = new Gson();
                LoginResultFilter loginResultFilter = gson.fromJson(gson.toJson(content), LoginResultFilter.class);
                loginResultFilter.setSessionId(sessionId);
                Utils.addSessionAndCookie(session, response, loginResultFilter, null);
                model.addAttribute("isVip",Integer.valueOf(session.getAttribute("isVip").toString()));
                model.addAttribute("avatar",session.getAttribute("avatar"));
                model.addAttribute("userName",session.getAttribute("userName"));
                model.addAttribute("vipEndTime",session.getAttribute("vipEndTime"));
            }else{
                Utils.removeSession(session);
            }
        }
        ArrayList<LinkedTreeMap<String ,Object>> map = (ArrayList<LinkedTreeMap<String, Object>>) HttpUtil.connectApi(Constants.GET_VIP_PAY_MONEY, true).get("content");
        model.addAttribute("vipPayMoney",Double.valueOf(map.get(0).get("id").toString()).intValue());
    }

    /**
     * vip支付
     * @param session
     * @param vipType 支付金额，接口获取
     * @param sessionId
     * @param payType 支付类型 1、支付宝，2、微信，3、线下支付，4、余额支付，99、其他
     *                deviceType信息来源设备枚举：1、pc，
     */
    public HashMap<String ,Object> payVip(Integer vipType, Integer payType, HttpSession session, String sessionId) {
        HashMap<String, Object> map = HttpUtil.connectApi(Constants.PAY_VIP, new Params("sessionId", sessionId).add("vipType", vipType).add("payType", payType).add("deviceType", 1).get());
        int state = Double.valueOf(map.get("status").toString()).intValue();
        if(state == -1){
            Utils.removeSession(session);
        }
        return map;
    }

    /**
     * 获取订单状态
     * @param orderId 订单id
     */
    public HashMap<String, Object> getStickOrderStatus(String orderId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("orderId", orderId);
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.VIEW_STICK_STATUS, map);
        Integer status = Double.valueOf(result.get("status").toString()).intValue();
        HashMap<String, Object> results = new HashMap<>();
        if (status == 1) {
            LinkedTreeMap<String, Object> linkedTreeMap = (LinkedTreeMap<String, Object>) result.get("content");
            results.put("status", linkedTreeMap.get("status"));
        }
        return results;
    }

    /**
     * 小二管家列表
     * @param model
     */
    public void xiaoerInfos(Model model){
        HashMap<String, Object> xiaoer = HttpUtil.connectApi(Constants.VIEW_XIAOER_LIST, new Params().get());
        List<LinkedTreeMap<String, Object>> xiaoers = (List<LinkedTreeMap<String, Object>>)xiaoer.get("content");
        model.addAttribute("data",xiaoers);
    }

    /**
     * 小二管家主页
     * @param model
     */
    public void xiaoerInfo(Model model){
        LinkedTreeMap<String, Object> xiaoer = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.VIEW_XIAOER_LIST,
                new Params("tab",1).get(), model);
        model.addAttribute("data",xiaoer);
    }


    /**
     * 小二管家主页
     * @param model
     * author zhaoxiao 2018-7-18
     */
    public void xiaoerIndex(Model model,HttpSession session){
        Object sessionId = session.getAttribute("sessionId");
        LinkedTreeMap<String, Object> xiaoer = (LinkedTreeMap<String, Object>) HttpUtil.connectApi(Constants.VIEW_XIAOER_INDEX,
                new Params("sessionId",sessionId).get(), model);
        ArrayList<LinkedTreeMap<String,Object>> dealStoris = (ArrayList<LinkedTreeMap<String,Object>>) xiaoer.get("dealstories");
        if(dealStoris != null) {
            for (LinkedTreeMap<String, Object> item : dealStoris) {
                Object t = item.get("showtime");
                if (null != t) {
                    try {
                        item.put("showDate", new SimpleDateFormat("yyyy.MM.dd").parse(t.toString()));
                    } catch (Exception e) {
                    }
                }
            }
        }
        model.addAttribute("houseKeeperInfo",xiaoer);
    }
    /**
     * 小二管家主页成交故事
     * author zhaoxiao 2018-7-18
     */
    public  HashMap<String, Object> getDaelstories( HashMap<String, String> params){
        HashMap<String, Object> xiaoer = ( HashMap<String, Object>) HttpUtil.connectApiString(Constants.VIEW_XIAOER_DEALSTORIES,params);
        return xiaoer;
    }
    /**
     * 小二管家节日福利新闻
     * @return
     * <AUTHOR> 2018-7-20
     */
    public void getFestivalNews(Model model,String params,String baseUrl){
        //转换url为可用参数
        HashMap<String, String> paramMap = ParamsUtil.analysisInput(params, Constants.VIEW_XIAOER_DEALSTORIES);
        //添加url到model
        ParamsUtil.addFilterIntoModel(Constants.VIEW_XIAOER_DEALSTORIES, model, baseUrl, params, paramMap);
        paramMap.put("newsType","3");
        paramMap.put("pageSize","5");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_XIAOER_DEALSTORIES, paramMap);
        HttpUtil.handleServiceList(result, "msg", "festivalNews", model);
        //添加热卖排行
        model.addAttribute("ranking",HttpUtil.connectApi(Constants.GUESS_LIKE, new Params("locationId","32").get(),true).get("content"));
        //添加分页
        new MyPageable(paramMap, Integer.valueOf(paramMap.get("pageSize").toString())).putAttribute(model, baseUrl + params, Integer.valueOf((String) result.get("msg")));
    }
    /**
     * 小二管家节日福利新闻详情
     * @return
     * <AUTHOR> 2018-7-20
     */
    public void getFestivalNewsDetial(Model model,String id){
        HashMap<String,Object> result = (HashMap<String, Object>)HttpUtil.connectApi(Constants.VIEW_XIAOER_FESTIVALSTORIES, new Params().add("id",id).get());
        if(result == null) NoDataException.throwException();
        if(Double.valueOf(result.get("status").toString()).intValue() == 1){
            model.addAttribute("newsInfo",result.get("content"));
        }else {
            model.addAttribute("newsInfo","");
        }
        //添加热卖排行
        model.addAttribute("ranking",HttpUtil.connectApi(Constants.GUESS_LIKE, new Params("locationId","32").get(),true).get("content"));
    }
    /**
     *看房团列表页
     */
    public void getLookHouseList(Model model,String baseUrl,String input){
        HashMap<String ,String> params =  ParamsUtil.analysisInput(input,Constants.VIEW_XENEWS);
        params.put("newsType","2");
        params.put("pageSize","9");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_XENEWS,params);
        Integer status = Double.valueOf(result.get("status").toString()).intValue();
        if(status==1){
            ArrayList content = (ArrayList<Object>)result.get("content");
            model.addAttribute("content",content);
            model.addAttribute("count",content.size());
            model.addAttribute("totalNum", Double.valueOf(result.get("msg").toString()).intValue());

        }
        model.addAttribute("ranking",HttpUtil.connectApi(Constants.GUESS_LIKE, new Params("locationId","32").get(),true).get("content"));
        new MyPageable(params, 9).putAttribute(model, baseUrl + input, Integer.valueOf((String) result.get("msg")));
    }
    /**
     * 看房团详细页
     */
    public void getLookHouseDetail(Model model,String id){
        HashMap<String ,String> params= new HashMap<>();
        params.put("id",id);
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_XENEWSDETAIL,params);
        Integer status = Double.valueOf(result.get("status").toString()).intValue();
        if(status==1){
            LinkedTreeMap<String,Object> detail =(LinkedTreeMap<String,Object>)result.get("content");
            model.addAttribute("lookDetail",detail);
        }else {
            model.addAttribute("lookDetail","");
        }
    }
    /**
     * 新版小二管家人员列表
     * @param model
     */
    public void keeperMember (Model model,String baseUrl,String input){
        HashMap<String ,String> params =  ParamsUtil.analysisInput(input,Constants.VIEW_XIAOER_LIST);
        params.put("pageSize","15");
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_XIAOER_LIST,params);
        Integer status = Double.valueOf(result.get("status").toString()).intValue();
        if(status==1){
            ArrayList content = (ArrayList<Object>)result.get("content");
            model.addAttribute("content",content);
            model.addAttribute("count",content.size());
        }
        new MyPageable(params, 15).putAttribute(model, baseUrl + input, Integer.valueOf((String) result.get("msg")));
    }
    /**
     * 品牌馆首页
     * author zhaoxiao 2018-10-25
     */
    public void getbrandCompany(Model model){
        HttpUtil.connectApi(Constants.VIEW_BRAND_INDEX,new Params().add("page","1").add("pageSize", "100").get(),model,"companies");
    }
    /**
     * 品牌馆详情
     * author zhaoxiao 2018-10-26
     */
    public void getCompanyDetial(Model model,String  brandId){
        HttpUtil.connectApi(Constants.VIEW_BRAND_DETIAL,new Params().add("brandId",brandId).get(),model,"companyInfo");
        HttpUtil.connectApi(Constants.BRAND_DY_INFO,new Params().add("brandId",brandId).add("pageSize","100").get(),model,"brandDynamic");
    }
    /**
     * 品牌馆资讯
     * author zhaoxiao 2018-10-26
     */
    public HashMap getCompanyNews(String  brandId,String page,String pageSize){
        return   HttpUtil.connectApi(Constants.VIEW_BRAND_NEWS,new Params().add("brandId",brandId).add("page",page).add("pageSize",pageSize).get());
    }
    public HashMap<String,Object> viewBrandMap(Integer brandId){
        return HttpUtil.connectApi(Constants.VIEW_BRAND_MAP, new Params("brandId", brandId).get());
    }

    /**
     * 小二管家各页面
     * @param model
     * @param session
     * <AUTHOR> 2019-4-15
     */
    public String getHouseKeepersInfoByType(Model model,HttpSession session ,String type ,String page){
        String url = "houseKeeper/houeseKeeper_newIndex";
        if("1".equals(type)){
            HttpUtil.connectApi(Constants.VIEW_XIAOER_INDEX, new Params().add("type","1").get(), model,"xiaoerData");
        }else if("2".equals(type)){
            HttpUtil.connectApi(Constants.VIEW_XIAOER_CONDOTOUR, new Params().get() ,model,"toursData");
            HttpUtil.connectApi(Constants.VIEW_XIAOER_TEAM, new Params().add("type","3").get(),model,"toursVideo");
            url =  "houseKeeper/housekeeper_tours";
        }else if("3".equals(type)){
            String baseUrl = "/houseKeeper"+type+"/";
            HashMap<String ,String> params =  ParamsUtil.analysisInput(page,Constants.VIEW_XIAOER_DEALSTORIES);
            if(params.get("page") == null){
                params.put("page","1");
            }
            params.put("pageSize","4");
            HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_XIAOER_HELPASK, params);
            if("1.0".equals(result.get("status").toString())){
                if(params.get("index") != null){
                    model.addAttribute("yIndex",params.get("index"));
                }else {
                    model.addAttribute("yIndex","0");
                }
                model.addAttribute("askData", result.get("content"));
            }else {
                model.addAttribute("askData", "");
            }
            new MyPageable(params, 4).putAttribute(model, baseUrl, Integer.valueOf((String) result.get("msg")));
            url =  "houseKeeper/help_ask";
        }else if ("4".equals(type)){
            String baseUrl = "/houseKeeper"+type+"/";
            HashMap<String ,String> params =  ParamsUtil.analysisInput(page,Constants.VIEW_XIAOER_DEALSTORIES);
            if(params.get("page") == null){
                params.put("page","1");
            }
            params.put("pageSize","5");
            HashMap<String, Object> result = HttpUtil.connectApiString(Constants.VIEW_XIAOER_DEALSTORY, params);
            if("1.0".equals(result.get("status").toString())){
                model.addAttribute("storyData", result.get("content"));
            }else {
                model.addAttribute("storyData", result.get("content"));
            }
            new MyPageable(params, 5).putAttribute(model, baseUrl, Integer.valueOf((String) result.get("msg")));
            model.addAttribute("endUrl",".htm");
            url =  "houseKeeper/deal_story";
        }else {
            String baseUrl = "/houseKeeper"+type+"/";
            HashMap<String ,String> params =  ParamsUtil.analysisInput(page,Constants.VIEW_XIAOER_DEALSTORIES);
            if(params.get("page") == null){
                params.put("page","1");
            }
            params.put("pageSize","8");
            String sessionId = StringUtils.isEmpty(session.getAttribute("muser")) ? "" : (String)session.getAttribute("muser");
            params.put("sessionId",sessionId);
            params.put("activityType","20");
            HashMap<String, Object> result = HttpUtil.connectApiString(Constants.ACTIVITIES_LIST, params);
            if("1.0".equals(result.get("status").toString())){
                model.addAttribute("couponData", result.get("content"));
            }else {
                model.addAttribute("couponData", result.get("content"));
            }
            new MyPageable(params, 8).putAttribute(model, baseUrl, Integer.valueOf((String) result.get("msg")));
            model.addAttribute("endUrl",".htm");
            url =  "houseKeeper/Coupon";
        }
        HttpUtil.connectApi(Constants.TOURIST_ORDER_COUNT, new Params().add("dustType","9").get(),model,"familyCount");
        return url;
    }

    /**
     * 小二管家购房团ajax
     * @return
     */
    public HashMap<String,Object> getHouseKeepersTour(String page ,String pageSize){
        return HttpUtil.connectApi(Constants.VIEW_XIAOER_CONDOTOUR, new Params("page", page).add("pageSize",pageSize).add("showLast","1").get());
    }

    /**
     * 小二管家购房百问ajax
     * @param strDate
     * @return
     */
    public HashMap<String,Object> getHouseKeepersAsk(String strDate){
        return HttpUtil.connectApi(Constants.VIEW_XIAOER_HELPASK, new Params("strDate", strDate).get());
    }
}
