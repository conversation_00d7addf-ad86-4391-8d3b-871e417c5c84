package com.fangxiaoer.service;

import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.common.Params;
import com.fangxiaoer.error.NoDataException;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.HashMap;
import java.util.List;

@Service
public class FreeService {
    /**
     * 免费服务首页
     * @param model
     */
    public void getFreeServiceIndex(Model model){
        HashMap<String, Object> result = HttpUtil.connectApi(Constants.VIEW_FREE_INDEX);
        Integer status = Double.valueOf(result.get("status").toString()).intValue();
        List<LinkedTreeMap<String, Object>> services;
        if(status==1){
            services = (List<LinkedTreeMap<String, Object>>) result.get("content");
            model.addAttribute("services",services);
        }else {
            model.addAttribute("services","");
        }
    }
    /**
     * 服务公司详情页
     * @param model
     * @param companyId
     * @param serviceId
     * author z<PERSON>xia<PERSON> 2018-7-27
     */
    public void getFreeServiceDetial(Model model,String companyId ,String serviceId){
        HttpUtil.connectApi(Constants.VIEW_FREE_COMPANY,new Params().add("companyId",companyId).add("serviceId",serviceId).get(),model,"companyInfo");
        if(model.asMap().get("companyInfo") == null) NoDataException.throwException();
    }

    /**
     * 免费服务提交订单
     * @param params 参数
     * @return
     * author zhaoxiao 2018-7-27
     */
    public HashMap<String, Object> saveServiceGuide(HashMap<String,Object> params){
        return HttpUtil.connectApi(Constants.SAVE_FREESERVICE_ORDER,params);
    }
    /**
     * 服务页面ajax级联获取公司服务类别
     * @param params
     * @return
     * <AUTHOR> 2018-7-30
     */
    public HashMap<String, Object> getServiceTypes(HashMap<String,Object> params){
        return HttpUtil.connectApi(Constants.GET_COMPANY_SERVICETYPES,params);
    }
}
