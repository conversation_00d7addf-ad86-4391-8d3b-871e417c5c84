package com.fangxiaoer.service;


import com.fangxiaoer.common.Constants;
import com.fangxiaoer.common.HttpUtil;
import com.fangxiaoer.model.UrlBean;
import com.google.gson.internal.LinkedTreeMap;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.*;

/**
 * 处理机构公司相关服务
 */
@Service
public class AgentCompanyService {

    //二手房总价条件
    private static final double[][] secondPrices = {{0.00,0.00},{1.00, 50.00}, {50.00, 80.00}, {80.00, 100.00}, {100.00, 120.00}, {120.00, 150.00}, {150.00, 200.00}, {200.00, 300.00},{300.00,10000.00}};
    //二手房面积
    private static final double[][] secondBuildArea = {{0.00,0.00},{1, 50.00}, {50.00, 70.00}, {70.00, 90.00}, {90.00, 110.00}, {110.00, 130.00}, {130.00, 150.00}, {150.00, 200.00},{200.00,300.00},{300.00,10000.00}};
    //租房价格
    private static final double[][] rentPrices = {{0.00,0.00},{1, 500.00}, {500.00, 1000.00}, {1000.00, 2000.00}, {2000.00, 3000.00}, {3000.00,100000.00}};
    /**
     * 机构公司店铺二手房filter过滤
     * @param model
     * @param params
     */
    public void exeSecondHousesFilterByCompanyId(Model model,HashMap<String,String> params){
        //1.将model转为map
        Map<String,Object> modelMap  = model.asMap();
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_COMPANY_SHOP_SECOND_HOUSE_FILTERS, params);
        LinkedTreeMap<String,Object> contentList = (LinkedTreeMap<String,Object>) result.get("content");
        //2.处理区域版块
        this.commonDealit(model,modelMap, contentList);
        //3.处理价格面积
        this.commonDealOtherFilter(model ,modelMap, contentList,secondPrices,"minPrice","maxPrice","price");
        this.commonDealOtherFilter(model ,modelMap, contentList,secondBuildArea,"minBuildArea","maxBuildArea","area");
    }

    /**
     * 机构公司店铺租房filter过滤
     * @param model
     * @param params
     */
    public void exeRentHousesFilterByCompanyId(Model model,HashMap<String,String> params){
        //1.将model转为map
        Map<String,Object> modelMap  = model.asMap();
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_COMPANY_SHOP_RENT_HOUSE_FILTERS, params);
        LinkedTreeMap<String,Object> contentList = (LinkedTreeMap<String,Object>) result.get("content");
        //2.处理区域版块
        this.commonDealit(model,modelMap, contentList);
        //3.处理价格
        this.commonDealOtherFilter(model ,modelMap, contentList,rentPrices,"minPrice","maxPrice","price");

    }

    /**
     * 机构公司店铺商铺filter过滤
     * @param model
     * @param params
     */
    public void exeShopHousesFilterByCompanyId(Model model,HashMap<String,String> params){
        //1.将model转为map
        Map<String,Object> modelMap  = model.asMap();
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_COMPANY_SHOP_SHOPS_HOUSE_FILTERS, params);
        LinkedTreeMap<String,Object> contentList = (LinkedTreeMap<String,Object>) result.get("content");
        this.commonDealit(model,modelMap, contentList);
    }


    /**
     * 机构公司店铺写字楼filter过滤
     * @param model
     * @param params
     */
    public void exeOfficeHousesFilterByCompanyId(Model model,HashMap<String,String> params){
        //1.将model转为map
        Map<String,Object> modelMap  = model.asMap();
        HashMap<String, Object> result = HttpUtil.connectApiString(Constants.GET_COMPANY_SHOP_OFFICE_HOUSE_FILTERS, params);
        LinkedTreeMap<String,Object> contentList = (LinkedTreeMap<String,Object>) result.get("content");
        this.commonDealit(model,modelMap, contentList);
    }


    /**
     * 通用方法
     * @param model
     * @param modelMap
     * @param contentList
     */
    private void commonDealit(Model model ,Map<String,Object> modelMap, LinkedTreeMap<String,Object> contentList){
        ArrayList<UrlBean> areaFromSys  = (ArrayList)modelMap.get("region");
        //循环处理区域
        ArrayList<LinkedTreeMap<String,Object>> fromAreaList  = (ArrayList) contentList.get("area");
        ArrayList<UrlBean> lz = new ArrayList<>();
        lz.add(areaFromSys.get(0));
        for(LinkedTreeMap<String,Object> map :fromAreaList){
            if(null != map){
                String _regionId = (String)map.get("RegionID");
                for(int i = 1; i < areaFromSys.size();i++){
                    UrlBean bean  = areaFromSys.get(i);
                    if(bean.getId().equals(_regionId)){
                        lz.add(bean);
                        break;
                    }
                }
            }
        }

        if(null != contentList.get("plate") && null != modelMap.get("plate")){
            ArrayList<UrlBean> plateFromSys  = (ArrayList)modelMap.get("plate");
            ArrayList<UrlBean> pz = new ArrayList<>();
            ArrayList<LinkedTreeMap<String,Object>> fromPlateList  = (ArrayList) contentList.get("plate");
            pz.add(plateFromSys.get(0));
            for(LinkedTreeMap<String,Object> map :fromPlateList){
                if(null != map){
                    String _plateId = (String)map.get("PlateID");
                    for(int i = 1; i < plateFromSys.size();i++){
                        UrlBean bean  = plateFromSys.get(i);
                        if(bean.getId().equals(_plateId)){
                            pz.add(bean);
                            break;
                        }
                    }
                }
            }
            if(pz.size() > 1){
                model.addAttribute("plate",pz);
            }else{
                model.addAttribute("plate" , new ArrayList<>());
            }
        }
        model.addAttribute("region",lz);
    }


    /**
     * 处理价格，面积等过滤条件
     * @param model
     * @param modelMap
     * @param contentList
     * @param filters 定义的对应filter数组
     * @param minArgs 最小值
     * @param maxArgs 最大值
     * @param modelMapName model对应的filter别名
     */
    private void commonDealOtherFilter(Model model ,Map<String,Object> modelMap, LinkedTreeMap<String,Object> contentList,double[][] filters,String minArgs,String maxArgs,String modelMapName){
        //3.处理价格面积
        if(null != contentList && null != contentList.get("sk")){
            LinkedTreeMap<String,Object> ddd =   (LinkedTreeMap<String,Object>)contentList.get("sk");
            if(null != ddd && null != ddd.get(minArgs)){
                double min = (double)ddd.get(minArgs);
                double max = (double)ddd.get(maxArgs);
                //最小值范围索引
                int _indexMin = 0 ;
                int _indexMax = 0 ;
                //循环最小值
                for(int i = 1 ;i < filters.length;i ++){
                    double s1 = filters[i][0];
                    double s2 = filters[i][1];
                    if(s1 <= min && min <= s2){
                        _indexMin = i ;
                    }
                    if(s1 <= max && max <= s2){
                        _indexMax = i ;
                    }
                }
                ArrayList<UrlBean> removeSys = new ArrayList<>();
                ArrayList<UrlBean> fromSys  = (ArrayList)modelMap.get(modelMapName);
                if(_indexMin != 0){
                    for(int i = 1 ;i < _indexMin ;i ++){
                        removeSys.add(fromSys.get(i));
                    }
                }
                if(_indexMax != 0 && _indexMax < filters.length){
                    for(int i = _indexMax + 1 ;i <= filters.length-1 ;i ++){
//                            priceFromSys.remove(i+1);
                        removeSys.add(fromSys.get(i));
                    }
                }
                fromSys.removeAll(removeSys);
                model.addAttribute(modelMapName,fromSys);
//                System.out.println(min + "/" + max + "/" + _indexMin + "/" + _indexMax);
            }
        }
    }
}
