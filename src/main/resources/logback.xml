<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 这里面定义了 CONSOLE_LOG_PATTERN, FILE_LOG_PATTERN 等日志格式, 还定义了一些日志级别 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="LOGSTASH_HOST" value="${LOGSTASH_HOST:-********}" />  <!--*************-->
    <property name="LOGSTASH_PORT" value="${LOGSTASH_PORT:-5044}" />
    <property name="LOGENABLE_ONLINE" value="${LOGENABLE_ONLINE:-false}" />
    <!-- 命令行输出, 一般线上不用 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder  charset="UTF-8">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <property name="basedir" value="./"/>
    <property name="LOG_FILE_NAME" value="sysite"/> <!-- 定义一个属性, 下面用 -->

    <!-- 输出格式 appender -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${basedir}/logs/${LOG_FILE_NAME}.log</file>  <!-- 可自己定义 -->
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern> <!-- 输出格式也可自己定义 -->
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${basedir}/logs/${LOG_FILE_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

    <!-- error 日志 appender -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${basedir}/logs/${LOG_FILE_NAME}_error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder  charset="UTF-8">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${basedir}/logs/${LOG_FILE_NAME}_error.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>

    <!-- 定义日志级别, 也可在应用配置中指定 -->
    <logger name ="com.fangxiaoer" level="ERROR" />
    <logger name="org.springframework.web" level="ERROR"/>
    <!--<root level="ERROR">
        <appender-ref ref="CONSOLE" /> &lt;!&ndash; 线上不需要输出到 CONSOLE &ndash;&gt;
        <appender-ref ref="FILE" />
        <appender-ref ref="ERROR_FILE" />
    </root>-->

    <if condition='property("LOGENABLE_ONLINE").equals("true")'>
        <then>
            <!-- Define Logstash TCP appender -->
            <appender name="LOGSTASH_TCP" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
                <destination>${LOGSTASH_HOST}:${LOGSTASH_PORT}</destination>
                <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                    <providers>
                        <timestamp />
                        <pattern>
                            <pattern>
                                {"message": "%msg", "level": "%level", "logger": "%logger", "thread": "%thread", "context": "%contextName", "projectName": "sysite"}
                            </pattern>
                        </pattern>
                        <stackTrace>
                            <fieldName>stackTrace</fieldName>
                            <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                                <exclude>\$SpringCGLIB$</exclude>
                                <maxDepthPerThrowable>30</maxDepthPerThrowable>
                                <maxLength>2048</maxLength>
                                <shortenedClassNameLength>30</shortenedClassNameLength>
                                <rootCauseFirst>true</rootCauseFirst>
                            </throwableConverter>
                        </stackTrace>
                    </providers>
                </encoder>
                <keepAliveDuration>5 minutes</keepAliveDuration>
                <reconnectionDelay>10 seconds</reconnectionDelay>
            </appender>
            <root>
                <!--<appender-ref ref="CONSOLE" />-->     <!-- 线上不需要输出到 CONSOLE -->
                <appender-ref ref="FILE" />
                <appender-ref ref="ERROR_FILE" />
                <appender-ref ref="LOGSTASH_TCP" />
            </root>
        </then>
    </if>
    <if condition='property("LOGENABLE_ONLINE").equals("false")'>
        <then>
            <root>
                <appender-ref ref="CONSOLE" />     <!-- 线上不需要输出到 CONSOLE -->
                <appender-ref ref="FILE" />
                <appender-ref ref="ERROR_FILE" />
            </root>
        </then>
    </if>
</configuration>