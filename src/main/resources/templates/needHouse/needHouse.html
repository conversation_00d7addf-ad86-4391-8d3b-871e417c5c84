<!DOCTYPE html>
<html xmlns:th="https://www.w3.org/1999/xhtml">
	<head>
		<meta charset="utf-8" />
		<title th:if="${needType == 0}"
			   th:text="${'二手房求购_'+needHouse.regionValue+'二手房_'+needHouse.regionValue+'预算二手房 - 房小二网'}"></title>
		<title th:if="${needType == 1}"
			   th:text="${'房屋求租_'+needHouse.regionValue+'租房_'+needHouse.regionValue+'预算求组 - 房小二网'}"></title>
		<meta th:if="${needType == 0}" name="keywords"
			  th:content="${'房屋求购,'+needHouse.regionValue+'二手房,'+needHouse.regionValue+'买房,沈阳预算二手房'}"/>
		<meta th:if="${needType == 1}" name="keywords"
			  th:content="${'房屋求租,'+needHouse.regionValue+'租房,'+needHouse.regionValue+'房屋求租,沈阳预算租房'}"/>
		<meta th:if="${needType == 0}" name="description"
			  th:content="${'房小二网发布'+needHouse.regionValue+'预算二手房求购信息，具体需求为：需求  买房，卖房，就上房小二网。'}" />
		<meta th:if="${needType == 1}" name="description"
			  th:content="${'房小二网发布'+needHouse.regionValue+'预算租房求租信息，具体需求为：需求  买房，卖房，就上房小二网。'}" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/rentAndBuy.css"/>
		<title></title>
	</head>
	<body>
		<!--引入头部导航栏-->
		<div id="head2017" th:if="${needType == 0}" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
		<div id="head2017" th:if="${needType == 1}" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
		<!--搜索栏-->
		<div id="search2017" th:if="${needType == 0}" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
		<div id="search2017" th:if="${needType == 1}" th:include="fragment/fragment::searchNav" th:with="type=3"></div>
		<!--面包屑-->
		<div class="crumbs">您的位置：
			<a href="/" target="_blank">沈阳房产网</a> &gt;
			<a th:if="${needType == 0}" href="/saleHouses/" target="_blank">沈阳二手房</a>
			<a th:if="${needType == 1}" href="/rents/" target="_blank">沈阳租房</a>
			&gt;
			<a th:if="${needType == 0}" href="/needSeconds">求购信息</a>
			<a th:if="${needType == 1}" href="/needRents">求租信息</a>
			&gt;
			<a th:if="${needType == 0}" th:text="${needHouse.regionValue+'求购房'}"></a>
			<a th:if="${needType == 1}" th:text="${needHouse.regionValue+'求租房'}"></a>
		</div>
		<div class="main w">
		<div class="header_sale"><h1 th:if="${needType == 0}" class="title1" th:text="${'（求购）'+needHouse.title}"></h1>
		<h1 th:if="${needType == 1}" class="title1" th:text="${'（求租）'+needHouse.title}"></h1>
		<!--<ul>
			<li th:text="${'更新时间：'+needHouse.addTime}"></li>
			<li th:text="${needHouse.hits+'人已浏览'}"></li>
		</ul>-->
		</div>
		</div>
		<!--分享标签-->
		<!--<div th:include="fragment/fragment:: shareIcon"></div>-->

		<div class="RABDetails">
			<ul class="RaBLeft">
				<li>
					<span>区域：</span>
					<a th:if="${needType == 0}" th:href="${'/needSeconds/r'+needHouse.regionId}" th:text="${needHouse.regionValue}" target="_blank"  style="color: #ff5200;"> 沈河区</a>
					<a th:if="${needType == 1}" th:href="${'/needRents/r'+needHouse.regionId}" th:text="${needHouse.regionValue}" target="_blank" style="color: #ff5200;"> 沈河区</a>
				</li>
				<li>
					<span>居室：</span>
					<p th:text="${needHouse.roomValue}">三居</p>
				</li>
				<li>
					<span>预算：</span>
					<p th:text="${needHouse.budgetValue}"> 80-100万</p>
				</li>
				<li>
					<span>发布时间：</span>
					<p th:text="${needHouse.publicationDate}">018-07-07 08:20</p>
				</li>
				<li>
					<span>需求： </span>
					<p>
						<th:block th:text="${needHouse.describe}">
						</th:block>
					</p>
				</li>
			</ul>
			<div class="RaBRight">
				<div>
					<img th:src="${#strings.isEmpty(needHouse.avatarUrl)?'https://static.fangxiaoer.com/web/images/ico/sign/men.gif':needHouse.avatarUrl}" alt="" />
					<a th:if="${#session?.getAttribute('sessionId') == null and needHouse.isAgent eq '0'}" class="call consult agentUrl" target="_blank" data-toggle="modal"  onclick="contact()" href="#loginAgent"></a>
                    <a th:if="${#session?.getAttribute('sessionId') != null and needHouse.isAgent eq '0'}" class="call consult agentUrl" ></a>
					<a th:if="${needHouse.isAgent eq '1'}" class="call consult agentUrl" ></a>
					<input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}">
					<p th:if="${#session?.getAttribute('sessionId') != null and needHouse.isAgent eq '0'}" style="display:block;text-align: center;color: #666;margin-top: 15px;">升级为经纪人端口用户查看电话</p>
					<p th:if="${#session?.getAttribute('sessionId') != null and needHouse.isAgent eq '0'}" style="display:block;text-align: center;color: #666;">联系电话：<span style="color: #ff5200;">************</span></p>
					<script th:inline="javascript">
						//防止手机号被趴
						var sessionId = $("#sessionId").val();
						if (sessionId == "" || sessionId == null) {
							$(".call").text("点击查看电话");
						}else if (sessionId != "" && sessionId != null) {
							var memberType = [[${needHouse.memberType}]];
							var mobile = [[${needHouse.mobile}]];
							var call = memberType+"   "+mobile;
							$(".call").text(call);
						}
					</script>
				</div>
			</div>

		</div>
		<div class="disclaimer"><strong>免责声明：</strong>
			房源信息由网站用户提供，其真实性、合法性由信息提供者负责，最终以政府部门登记备案为准。本网站不声明或保证内容之正确性和可靠性，租赁该房屋时，请谨慎核查。如该房源信息有误，您可以投诉或<strong>拔打举报电话：************</strong>。
		</div>
		<div class="cl"></div>
		<div th:include="fragment/fragment:: footer_detail"></div>
		<!--<div th:include="fragment/fragment::esfCommon_meiqia"></div>-->
		<div th:include="fragment/fragment::tongji"></div>
		<div th:include="house/detail/fragment_contactAgent::loginAgent"></div>
	</body>
</html>
 