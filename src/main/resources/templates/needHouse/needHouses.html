<!DOCTYPE html>
<html xmlns:th="https://www.w3.org/1999/xhtml">
	<head>
		<meta charset="utf-8" />
		<title  th:if="${needType == 0}"
				th:text="${#strings.isEmpty(seoTitle)?('二手房求购_沈阳买房_沈阳二手房 - 房小二网')
				:('二手房求购_'+seoTitle+'二手房_'+seoTitle+'买房 - 房小二网')}"></title>
		<meta th:if="${needType == 0}" name="keywords"
			  th:content="${#strings.isEmpty(seoTitle)?('房屋求购,沈阳买房,沈阳二手房,二手房求购')
			  :('房屋求购,'+seoTitle+'二手房,'+seoTitle+'买房,二手房求购')}"/>
		<meta th:if="${needType == 0}" name="description"
			  th:content="${#strings.isEmpty(seoTitle)?('房小二网为您提供最全最新最及时的沈阳二手房求购信息，真实信息，即时更新，专人审核，定期维护，为您提供最真实的买房需求。买房，卖房，就上房小二网。')
			  :('房小二网为您提供最全最新最及时的'+seoTitle+'二手房求购信息，真实信息，即时更新，专人审核，定期维护，为您提供最真实的'+seoTitle+'买房需求。买房，卖房，就上房小二网。')}" />

		<title  th:if="${needType == 1}"
				th:text="${#strings.isEmpty(seoTitle)?('房屋求租_沈阳租房_房屋急租 - 房小二网')
				:('房屋求租_'+seoTitle+'租房_'+seoTitle+'租房 - 房小二网')}"></title>
		<meta th:if="${needType == 1}" name="keywords"
			  th:content="${#strings.isEmpty(seoTitle)?('房屋求租,沈阳租房,房屋急租,租房求购')
			  :('房屋求租,'+seoTitle+'租房,'+seoTitle+'急租,租房求购')}"/>
		<meta th:if="${needType == 1}" name="description"
			  th:content="${#strings.isEmpty(seoTitle)?('房小二网为您提供最全最新最及时的沈阳房屋求租信息，真实信息，即时更新，专人审核，定期维护，为您提供最真实的租房需求。买房，卖房，就上房小二网。')
			  :('房小二网为您提供最全最新最及时的'+seoTitle+'房屋求租信息，真实信息，即时更新，专人审核，定期维护，为您提供最真实的'+seoTitle+'租房需求。买房，卖房，就上房小二网。')}" />

		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/rentAndBuy.css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2018.css?v=20180709" />
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
		<script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
		<script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body>
		<!--引入头部导航栏-->
		<div id="head2017" th:if="${needType == 0}" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
		<div id="head2017" th:if="${needType == 1}" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
		<!--搜索栏-->
		<div id="search2017" th:if="${needType == 0}" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
		<div id="search2017" th:if="${needType == 1}" th:include="fragment/fragment::searchNav" th:with="type=3"></div>
		<!--面包屑-->
		<div class="crumbs">您的位置：
			<a href="/" target="_blank">沈阳房产网</a> &gt;
			<a th:if="${needType == 0}" href="/saleHouses/" target="_blank">沈阳二手房</a>
			<a th:if="${needType == 1}" href="/rents/" target="_blank">沈阳租房</a>
			&gt;
			<a th:if="${needType == 0}" href="/needSeconds">求购信息</a>
			<a th:if="${needType == 1}" href="/needRents">求租信息</a>
		</div>
		<!--筛选条件-->
		<div id="option">
			<ul>
				<li class="fenlei">
					<p>位置：</p>
					<a id="btn1" class="">
						<b class="listIcon listIcon_dw"></b>区域<i></i>
					</a>
				</li>
				<!--区域-->
				<li id="Search_zf" class="leibie" >
					<a th:each="r:${region}" th:href="${r.url}"
					   th:text="${#strings.toString(r.name).replaceAll('全部','不限')}"
					   th:id="'r'+${r.id}" th:class="${r.selected}? 'hover':''"></a>
				</li>

				<li id="Search_ej" th:if="${needType == 0}">
					<p>总价：</p>
					<a th:each="sb:${secondPrice}" th:href="${sb.url}"
					   th:text="${#strings.toString(sb.name).replaceAll('全部','不限')}"
					   th:id="'sb'+${sb.id}" th:class="${sb.selected}? 'hover':''">></a>
				</li>
				<li id="Search_zj" th:if="${needType == 1}">
					<p>租金：</p>
					<a th:each="rb:${rentPrice}" th:href="${rb.url}"
					   th:text="${#strings.toString(rb.name).replaceAll('全部','不限')}"
					   th:id="'rb'+${rb.id}" th:class="${rb.selected}? 'hover':''">></a>
				</li>
				<li id="Search_hx">
					<p>户型：</p>
					<a th:each="l:${layout}" th:href="${l.url}"
					   th:text="${#strings.toString(l.name).replaceAll('全部','不限')}"
					   th:id="'l'+${l.id}" th:class="${l.selected}? 'hover':''">></a>
				</li>
			</ul>
		</div>

		<div id="option_info"  style="display: none">
			<b >已选：</b>
			<!--区域-->
			<div th:each="region:${region}" th:if="${region.selected and region.name ne '全部'}">
				<span class="condition"><th:block th:text="${region.name}"></th:block></span>
				<i class="cleanUrl" th:value="${'r'+region.id}"></i>
			</div>
			<!--总价-->
			<li th:if="${needType == 0}">
			<div th:each="sb:${secondPrice}" th:if="${sb.selected and sb.name ne '全部'}">
				<span class="condition"><th:block th:text="${sb.name}"></th:block></span>
				<i class="cleanUrl" th:value="${'sb'+sb.id}"></i>
			</div>
			</li>
			<!--租金-->
			<li th:if="${needType == 1}">
				<div th:each="rb:${rentPrice}" th:if="${rb.selected and rb.name ne '全部'}">
					<span class="condition"><th:block th:text="${rb.name}"></th:block></span>
					<i class="cleanUrl" th:value="${'rb'+rb.id}"></i>
				</div>
			</li>
			<!--户型-->
			<div th:each="l:${layout}" th:if="${l.selected and l.name ne '全部'}">
				<span class="condition"><th:block th:text="${l.name}"></th:block></span>
				<i class="cleanUrl" th:value="${'l'+l.id}"></i>
			</div>
			<a th:if="${needType == 0}" href="/needSeconds/" class="clean">清空筛选条件</a>
			<a th:if="${needType == 1}" href="/needRents/" class="clean">清空筛选条件</a>
			<script>
				/*判断是否显示条件栏目*/
				$(function () {
					if($(".condition").text() != ""){
						$("#option_info").css("display","block");
					}
				})
				/*去掉条件标签*/
				$(".cleanUrl").click(function () {
					var oldUrl = location.pathname;
					if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
						var moreUrl ="-" + $(this).attr("value");
					}else{ var moreUrl = $(this).attr("value");}

					var newUrl = oldUrl.replace(moreUrl,"");
					window.location.href = newUrl;
					//跳到newUrl指定链接
				});
			</script>
		</div>
		<div id="main" th:style="${!#lists.isEmpty(needHosues)?'':'border:none !important;'}">
			<div th:if="${!#lists.isEmpty(needHosues)}" id="left" class="rentAndBuy">
				<h5 th:if="${needType == 0}">求购房源</h5>
				<h5 th:if="${needType == 1}">求租房源</h5>
				<ul>
					<li th:each="need:${needHosues}">
						<a th:if="${needType == 0}" th:href="${'/needSecond/'+(#strings.toString(need.id).contains('.')?#strings.toString(need.id).replaceAll('0+?$','').replaceAll('[.]$', ''):need.id)+'.htm'}" target="_blank">
							<div>
								<p th:text="${need.title}"></p>
								<span th:text="${#strings.abbreviate(need.describe,20)}"> </span>
								<i>个人</i>
							</div>
							<span th:text="${need.budgetValue}"></span>
							<span th:text="${need.roomValue}"></span>
							<span th:text="${need.addTime}"></span>
						</a>
						<a th:if="${needType == 1}" th:href="${'/needRent/'+(#strings.toString(need.id).contains('.')?#strings.toString(need.id).replaceAll('0+?$','').replaceAll('[.]$', ''):need.id)+'.htm'}" target="_blank">
							<div>
								<p th:text="${need.title}"></p>
								<span th:text="${#strings.abbreviate(need.describe,28)}"> </span>
								<i>个人</i>
							</div>
							<span th:text="${need.budgetValue}"></span>
							<span th:text="${need.roomValue}"></span>
							<span th:text="${need.addTime}"></span>
						</a>
					</li>
				</ul>
				<div class="cl"></div>
				<div class="page">
					<div th:include="fragment/page :: page"></div>
				</div>
			</div>
            <div th:if="${#lists.isEmpty(needHosues)}" id="left" class="rentAndBuy" style="border:none !important;">
                <img class="noNeed" src="https://static.fangxiaoer.com/web/images/sy/sale/list/noNeed.png">
            </div>
			<div id="right">
				<!--列表页右侧求购-->
				<div class="rightQzQgBtn" th:if="${needType == 0}" >
					<!--<a href="/secondPublish/" target="_blank"><i class="rightQzQgBtn-icon1"></i>我要卖房</a>-->
					<a href="/housingprice/" target="_blank"  style="margin-bottom: 5px"><i class="rightQzQgBtn-icon2"></i>房价评估</a>
				</div>
				<!--列表页右侧求租-->
				<div class="rightQzQgBtn"  th:if="${needType == 1}">
					<a href="/rentwhole/" target="_blank" style="margin-bottom: 14px"><i class="rightQzQgBtn-icon3"></i>我要出租</a>
				</div>
				<!--求购-->
				<th:block th:if="${needType == 0}" class="zsfw" th:include="secondhouse/fragment::rightwantbuy"></th:block>
				<!--求租-->
				<div th:if="${needType == 1}" class="zsfw" th:include="fragment/fragment::help_search_rent"></div>
			</div>
		</div>
		<div class="cl"></div>
		<div th:include="fragment/fragment:: footer_detail"></div>
		<!--<div th:include="fragment/fragment::esfCommon_meiqia"></div>-->
		<div th:include="fragment/fragment::tongji"></div>
	</body>
</html>
