<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head >

</head>
<body>
<!--发布类型-->
<div class="sale_form_r qh_btn" style="width: 1270px;margin: 0 auto;float: none;" th:fragment="publishType">
    <a href="/new_secondPublish"  th:class=" (${currType} == 1)? 'form_btn hover' : 'form_btn' " style="margin-left: 170px;">二手房</a>
    <a href="/rentwhole" th:class=" (${currType} == 2)? 'form_btn hover' : 'form_btn' ">租房</a>
    <a href="/shopsell" th:class=" (${currType} == 3)? 'form_btn hover' : 'form_btn' ">商铺</a>
    <a href="/officeRent" th:class=" (${currType} == 4)? 'form_btn hover' : 'form_btn' ">写字楼</a>
</div>



<!--房源描述-->
<div class="sale_form_r" th:fragment="houseDescribe">
    <ul>
        <li>
            <p><span>*</span>详细介绍</p>
<!--            <div class="pr">
                <div style="width: 650px; display: inline-block; margin-bottom: 20px;">
                    <textarea name="describe"
                              th:field="*{describe}"  th:value="*{describe}"
                              rows="2" cols="20" id="Describe"></textarea>
                </div>
            </div>
            <div class="errorBox fxe_fyms">
                <label id="fxe_Label1" class="error" style="display: none;">房源描述应在30至1000字之间</label>

            </div>-->
            <textarea name="describe"
                      th:field="*{describe}"  th:value="*{describe}" id="Describe"
                      style="width: 628px;height: 190px;font-size: 16px; resize: none;padding: 10px;"
                      placeholder="个性且详尽的房源描述可以加速您的房子成交噢....."
            >
            </textarea>
            <div class="errorBox"></div>
        </li>
        <br>

        <li>
            <p>上传图片</p>
            <div class="pr">
                <div style="width: 650px; display: inline-block;">
                    <link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/diyUpload.css?t=20220902"/>
                    <link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/webuploader.css"/>

                    <script type="text/javascript" charset="utf-8" src="https://static.fangxiaoer.com/js/webuploader.js"></script>
                    <script type="text/javascript" charset="utf-8" src="/ImgUpLoad/Script/diyUpload.js?t=20180613"></script>
                    <script type="text/javascript">
                        $(function () {
                            $('#fileImg').diyUpload({
                                url: '/uploadHousePic',
                                success: function (file, data) {
                                    var files = $("#ImgUpLoad1_imgValue").val();
                                    if (file._info.width >= 450 && file._info.height >= 450) {
                                        if (data) {
                                            if (files == "") {
                                                $(".fileBoxUl li").eq(0).find(".shoutu").show();
                                                $("#ImgUpLoad1_imgValue").val(file.id + "|" + data.content);
                                            } else {
                                                $("#ImgUpLoad1_imgValue").val(files + "," + file.id + "|" + data.content);
                                            }
                                        }
                                    }
                                    //else if (file._info.width <= file._info.height) {
                                    //        t = "请上传横版图片"
                                    //        $("#fileBox_" + file.id).find(".diyCancel").click()

                                    //}
                                    else if (file._info.width < 450 || file._info.height < 450) {
                                        t = "请上传大于450*450px的图片"
                                        $("#fileBox_" + file.id).find(".diyCancel").click()

                                    }
                                    $("#zhaopian").html(t)
                                },
                                error: function (err) {
                                    console.log(err);
                                }
                            });
                            //SetImg();
                        });

                    </script>
                    <div>
                        <input type="hidden" name="showtuindex" id="ImgUpLoad1_showtuIndex" th:value="${showtuIndex}"/>
                        <input type="hidden" name="imglist" id="ImgUpLoad1_imgList" th:value="*{imgurl}"/>
                        <input type="hidden" name="imgvalue" id="ImgUpLoad1_imgValue" th:value="*{imgurl}"/>
                    </div>
                    <div id="fileImg">

                    </div>
                    添加几张照片，让您的信息更受欢迎（最多上传<i style="color:#ff5200">15</i>张，每张最大<i style="color:#ff5200">5M</i>，图片为<i style="color:#ff5200">jpg</i>格式）
                </div>
                <div class="xiaotieshi">
                    <h3>小贴士</h3>
                    1.带图片的房源排位更靠前，被看到的概率更大；<br>
                    2.您上传的首张图片将作为封面图，建议选择室内图片；<br>
                    3.为了更好展示您的房源，请选择大于450x450像素的图片。
                </div>
            </div>
            <div class="errorBox"><label id="zhaopian" class="error"></label></div>
        </li>

    </ul>
</div>
<div class="sale_form_r" th:fragment="SechouseDescribe">
    <ul>
        <li>
            <p><span>*</span>详细介绍</p>
            <!--            <div class="pr">
                            <div style="width: 650px; display: inline-block; margin-bottom: 20px;">
                                <textarea name="describe"
                                          th:field="*{describe}"  th:value="*{describe}"
                                          rows="2" cols="20" id="Describe"></textarea>
                            </div>
                        </div>
                        <div class="errorBox fxe_fyms">
                            <label id="fxe_Label1" class="error" style="display: none;">房源描述应在30至1000字之间</label>

                        </div>-->
            <textarea name="describe"
                      th:field="*{describe}" id="Describe"
                      style="width: 628px;height: 190px;font-size: 16px; resize: none;padding: 10px;"
                      placeholder="个性且详尽的房源描述可以加速您的房子成交噢.....">
            </textarea>
            <div class="errorBox"></div>
        </li>
        <br>

        <li>
            <p>上传图片</p>
            <div class="pr">
                <div style="width: 650px; display: inline-block;">
                    <link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/diyUpload.css?t=20220902"/>
                    <link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/webuploader.css"/>

                    <script type="text/javascript" charset="utf-8" src="https://static.fangxiaoer.com/js/webuploader.js"></script>
                    <script type="text/javascript" charset="utf-8" src="/ImgUpLoad/Script/diyUpload.js"></script>
<!--                    <script type="text/javascript" charset="utf-8" src="/ImgUpLoad/Script/secDiyUpload.js?t=20180613"></script>-->
                    <script type="text/javascript">
                        $(function () {
                            $('#fileImg').diyUpload({
                                url: '/uploadHousePic',
                                success: function (file, data) {
                                    var files = $("#ImgUpLoad1_imgValue").val();
                                    if (file._info.width >= 450 && file._info.height >= 450) {
                                        if (data) {
                                            if (files == "") {
                                                $(".fileBoxUl li").eq(0).find(".shoutu").show();
                                                $("#ImgUpLoad1_imgValue").val(file.id + "|" + data.content);
                                            } else {
                                                $("#ImgUpLoad1_imgValue").val(files + "," + file.id + "|" + data.content);
                                            }
                                        }
                                    }
                                        //else if (file._info.width <= file._info.height) {
                                        //        t = "请上传横版图片"
                                        //        $("#fileBox_" + file.id).find(".diyCancel").click()

                                    //}
                                    else if (file._info.width < 450 || file._info.height < 450) {
                                        t = "请上传大于450*450px的图片"
                                        $("#fileBox_" + file.id).find(".diyCancel").click()

                                    }
                                    $("#zhaopian").html(t)
                                },
                                error: function (err) {
                                    console.log(err);
                                }
                            });
                            //SetImg();
                        });
                       /* $(document).ready(function () {
                            var imgSrc = ($("#ImgUpLoad1_imgValue").val()).split(',')
                            if(imgSrc != ''){
                                console.log(imgSrc)
                                for (var i = 0; i <imgSrc.length ; i++) {
                                    var imgId = 'fileBox_'+imgSrc[i].split('|')[0]
                                    var imgUrl = imgSrc[i].split('|')[1]
                                    boxLi = '<li id="'+imgId+'" class="">'+
                                            '  <div class="viewThumb">'+
                                            '       <img src="https://images1.fangxiaoer.com/'+imgUrl+'">'+
                                            '    </div>'+
                                            '    <div class="diyCancel" style="display: block;">删除</div>'+
                                            '    <div class="diySuccess"></div>'+
                                            '    <div class="diyBar" style="display: block;">'+
                                            '    <div class="diyProgress" style="width: 100%;"></div>'+
                                            '        <a class="diyProgressText" data-index="0">设为首图</a>'+
                                            '    </div>'+
                                            '    <div class="shoutu" style="display: none"></div>'+
                                            '   </li>'
                                    $(".fileBoxUl").append(boxLi)
                                }
                                $("#fileBox_WU_FILE_0 .shoutu").show()
                            }
                        })
                        $(document).on('click', '.diyProgressText', function() {
                            $("#ImgUpLoad1_showtuIndex").val($(this).attr("data-index"));-
                            $(".shoutu").hide();
                            $(this).parent().parent().find(".shoutu").show();
                        });*/
                    </script>
                    <div>
                        <input type="hidden" name="showtuindex" id="ImgUpLoad1_showtuIndex" th:value="${showtuIndex}"/>
                        <input type="hidden" name="imglist" id="ImgUpLoad1_imgList" th:value="*{imgurl}"/>
                        <input type="hidden" name="imgvalue" id="ImgUpLoad1_imgValue" th:value="*{imgvalue}"/>
                    </div>
                    <div id="fileImg" style="height: 180px"></div>
<!--                    添加几张照片，让您的信息更受欢迎（最多上传<i style="color:#ff5200">30</i>张，每张最大<i style="color:#ff5200">5M</i>，图片为<i style="color:#ff5200">jpg</i>格式）-->
                </div>
                <div class="xiaotieshi">
                    <h3>小贴士</h3>
                    1.发布的房源最多上传30张，图片为jpg、png格式<br>
                    2.请选择大于450 x 450像素的图片上传<br>
                    3.请勿上传带有水印、马赛克或其他网站logo的图片<br>
                    4.请勿上传出现人物、联系方式或与房源无关图片
                </div>
            </div>
            <div class="errorBox"><label id="zhaopian" class="error"></label></div>
        </li>

    </ul>
</div>

<div class="cl"></div>


<!--联系方式-->
<div class="sale_form_r" th:fragment="contactWay">
    <ul>
        <li class="w220 tel">
            <p><span>*</span>联系电话</p>
            <div class="pr">
                <input name="ownerphone" type="text" maxlength="11" id="OwnerPhone"
                       th:field="*{ownerphone}"  th:value="*{ownerphone}" placeholder="您的联系方式，方便我们及时与您联系" style="width: 240px !important;"/>
            </div>
            <i id="OwnerPhone123" class="ljz"></i>
            <div class="cl"></div>
            <div class="errorBox"></div>
        </li>


        <li class="w220">
            <p><span>*</span>联系人</p>
            <div class="pr">
                <input name="houseowner" type="text" id="HouseOwner" th:field="*{houseowner}"
                       th:value="*{houseowner}" placeholder="我们应该如何称呼您" style="width: 240px !important;"/></div>
            <i id="HouseOwner123" class="ljz"></i>
            <div class="errorBox"></div>
        </li>

        <li id="messageCodeLi" class="w220 sjyzm"  th:if="${#session?.getAttribute('sessionId') == null}">
            <p><span>*</span>请输入</p>
            <div class="pr" style="float: left;">
                <input name="messageCode" type="password" id="messageCode" placeholder="验证码/密码" autocomplete="new-password" style="width: 240px !important;"/>
            </div>
            <a href="javascript:void(0);" class="requ_btn" id="validateCode" style="display: none;"></a>
            <div id="ReSendValidateCoadDom" style="overflow: hidden;"><a id="ReSendValidateCoad" class="requ_btn">获取验证码</a></div>
<!--            <div class="errorBox"><label id="messageCodeValue" class="error"></label></div>-->
            <div class="errorBox" style="margin-top: 7px;"></div>
        </li>

        <input id="HouseOwnerH" type="hidden" th:value="${#session?.getAttribute('userName')}">
        <input id="OwnerPhoneH" type="hidden" th:value="${#session?.getAttribute('phoneNum')}">
        <script>
            $("#OwnerPhone").attr("value",$("#OwnerPhoneH").val());
            $("#HouseOwner").attr("value",$("#HouseOwnerH").val());
            $(function () {
                var phone = $("#OwnerPhoneH").val();
                var name = $("#HouseOwnerH").val();
                if (phone.length != 0){
                    $("#OwnerPhone").attr("readOnly",true);
                }
                if (name.length != 0) {
                    $("#HouseOwner").attr("readOnly", true);
                }
            })
        </script>
    </ul>
</div>
<div class="sale_form_r" th:fragment="SeccontactWay">
    <ul>
        <li class="w220 tel">
            <p><span>*</span>联系电话</p>
            <div class="pr">
                <input name="ownerphone" type="text" maxlength="11" id="OwnerPhone" th:field="*{ownerphone}"
                       th:value="*{ownerphone}" placeholder="您的联系方式，方便我们及时与您联系" style="width: 240px !important;"/>
            </div>
            <i id="OwnerPhone123" class="ljz"></i>
            <div class="cl"></div>
            <div class="errorBox"></div>
        </li>


        <li class="w220">
            <p><span>*</span>联系人</p>
            <div class="pr">
                <input name="houseowner" type="text" id="HouseOwner" th:field="*{houseowner}"
                       th:value="*{ownerphone}" placeholder="我们应该如何称呼您" style="width: 240px !important;"/></div>
            <i id="HouseOwner123" class="ljz"></i>

            <div class="errorBox"></div>
        </li>

        <li id="messageCodeLi" class="w220 sjyzm"  th:if="${#session?.getAttribute('sessionId') == null}">
            <p><span>*</span>请输入</p>
            <div class="pr" style="float: left;">
                <input name="messageCode" type="password" id="messageCode" placeholder="验证码/密码" autocomplete="new-password" style="width: 240px !important;"/>
            </div>
            <a href="javascript:void(0);" class="requ_btn" id="validateCode" style="display: none;"></a>
            <div id="ReSendValidateCoadDom" style="overflow: hidden;"><a id="ReSendValidateCoad" class="requ_btn">获取验证码</a></div>
            <!--            <div class="errorBox"><label id="messageCodeValue" class="error"></label></div>-->
            <div class="errorBox" style="margin-top: 7px;"></div>
        </li>

        <input id="HouseOwnerH" type="hidden" th:value="${#session?.getAttribute('userName')}">
        <input id="OwnerPhoneH" type="hidden" th:value="${#session?.getAttribute('phoneNum')}">
        <script>
            /*$("#OwnerPhone").attr("value",$("#OwnerPhoneH").val());
            $("#HouseOwner").attr("value",$("#HouseOwnerH").val());*/
            $(function () {
                var phone = $("#OwnerPhoneH").val();
                var name = $("#HouseOwnerH").val();
                if (phone.length != 0){
                    $("#OwnerPhone").attr("readOnly",true);
                    $("#OwnerPhone").val($("#OwnerPhoneH").val())
                    $("#HouseOwner").val($("#HouseOwnerH").val())
                }
                if (name.length != 0) {
                    // $("#HouseOwner").attr("readOnly", true);
                }
            })
        </script>
    </ul>
</div>

<!--底部引入所需javascript-->
<!--房源描述-->
<div class="sale_form_r" th:fragment="upPhoto" >
    <ul>
        <li>
            <div class="pr">
                <div style="width: 650px; display: inline-block;">
                    <link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/diyUpload.css?t=20220902"/>
                    <link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/webuploader.css"/>

                    <script type="text/javascript" charset="utf-8" src="https://static.fangxiaoer.com/js/webuploader.js"></script>
                    <script type="text/javascript" charset="utf-8" src="/ImgUpLoad/Script/diyUpload.js"></script>
                    <script type="text/javascript">

                        $(function () {
                            $('#fileImg').diyUpload({
                                url: '/uploadHousePic',
                                success: function (file, data) {

                                    var files = $("#ImgUpLoad1_imgValue").val();
                                    if (file._info.width >= 450 && file._info.height >= 450) {
                                        if (data) {
                                            if (files == "") {
                                                $(".fileBoxUl li").eq(0).find(".shoutu").show();
                                                $("#ImgUpLoad1_imgValue").val(file.id + "|" + data.content);
                                            } else {
                                                $("#ImgUpLoad1_imgValue").val(files + "," + file.id + "|" + data.content);
                                            }
                                        }
                                    }
                                    //else if (file._info.width <= file._info.height) {
                                    //        t = "请上传横版图片"
                                    //        $("#fileBox_" + file.id).find(".diyCancel").click()

                                    //}
                                    else if (file._info.width < 450 || file._info.height < 450) {
                                        t = "请上传大于450*450px的图片"
                                        $("#fileBox_" + file.id).find(".diyCancel").click()

                                    }
                                    $("#zhaopian").html(t)
                                },
                                error: function (err) {
                                    console.log(err);
                                }
                            });
                            //SetImg();
                        });
                    </script>
                    <div>
                        <input type="hidden" name="showtuindex" ng-model="formData.showtuindex" id="ImgUpLoad1_showtuIndex"  value="{{ formData.showtuindex }}"  th:value="*{shopEntity.showtuindex}"/>
                        <input type="hidden" name="imglist" ng-model="formData.imglist" id="ImgUpLoad1_imgList"  value="{{ formData.imglist }}"   th:value="*{shopEntity.imgurl}"/>
                        <input type="hidden" name="imgvalue"  ng-model="formData.imgvalue" id="ImgUpLoad1_imgValue"  value="{{ formData.imgvalue }}"   th:value="*{shopEntity.imgurl}"/>
                    </div>
                    <div id="fileImg">

                    </div>
                    添加几张照片，让您的信息更受欢迎（最多上传<i style="color:#ff5200">15</i>张，每张最大<i style="color:#ff5200">5M</i>，图片为<i style="color:#ff5200">jpg</i>格式）
                </div>
                <div class="xiaotieshi">
                    <h3>小贴士</h3>
                    1.发布的房源最多上传30张，图片为jpg、png格式 <br>
                    2.请选择大于450 x 450像素的图片上传<br>
                    3.请勿上传带有水印、马赛克或其他网站logo的图片<br>
                    4.请勿上传出现人物、联系方式或与房源无关图片<br>
                </div>

            </div>
            <div class="errorBox"><label id="zhaopian" class="error"></label></div>
        </li>
    </ul>
</div>

<div th:fragment="terms">
        <style>
            .padB10{
                padding-bottom: 20px;
                margin-left: 113px;
            }
        </style>
        <div class="checkagreeInput padB10">
            <i id="checkagree" class="checkimg checked"></i><div>我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>及
            <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
    </div>
    <script>
        $("#checkagree").click(function(){
            $(this).toggleClass("checked");
        })
    </script>
</div>
</body>
</html>