<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<!--申请职位-->
<div  th:fragment="apply_position">
    <script src="/js/house/verify.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/zane-calendar.js"></script>
    <link rel="stylesheet" type="text/css" href="/css/zane-calendar.css" />
    <script type="text/javascript" src="/js/My97DatePicker/WdatePicker.js" language="javascript"></script>
    <!--<link href="/js/My97DatePicker/skin/WdatePicker.css" rel="stylesheet" type="text/css" />-->
    <style>
        .yyDk{
            width: 420px;
            height: 250px;
            position: fixed;
            left: 50%;
            top: 50%;
            margin-left: -210px;
            margin-top: -125px;
            z-index: 999999;
        }
        .yyDk>div{
            background: #fff;
            width: 420px;
            height: 224px;
            border-radius: 4px;
            /* padding: 45px 80px 0 80px; */
        }
        .yyDk>div .yyDkImg{
            text-align: center;
            padding-top: 42px;
        }
        .yyDk>div .yyDkImg>img{}
        .yyDk>div p{
            text-align: center;
            margin-top: 15px;
            color: #ff5200;
            font-size: 16px;
        }
        .yyDk>div p+p{
            margin-top: 20px;
            color: #333;
            font-size: 16px;
        }
        .yyDk .yyDk_close{
            background: none;
            text-align: center;
            padding: 0;
            width: 20px;
            height: 20px;
            position: absolute;
            right: 16px;
            top: 16px;
        }
        .yyDk .yyDk_close img{
            cursor: pointer;
            width: 20px;
            height: 20px;
        }
        #date select,.livingArea select{height: 28px;line-height: 28px;border: 1px solid #eee;width: 88px}
        .livingArea  #show {color: #3399FF;}
        .hasResume{z-index: 9999999}
    </style>

    <!--已有简历，直接投递弹窗-->
    <div class="hasResume">
        <i class="closeReport"></i>
        <h5>提示</h5>
        <p>您当前已有一份简历</p>
        <p>是否直接发送</p>
        <div class="hasBtn">
            <span class="hasBtnIco" id="forSend">直接发送</span>
            <span class="hasBtnIco" id="forEdit">查看编辑</span>
        </div>
    </div>

    <!--申请职位 简历弹窗-->
    <div class="arlTcHb"></div>
    <input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}"/>
    <!--<input type="hidden" id="session_phoneNum" th:value="${#session?.getAttribute('phoneNum')}"/>-->
    <div class="arlTcMain">
        <i class="closeReport"></i>
        <h4>简历投递</h4>
        <ul>
            <li>
                <label for=""><i>*</i>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</label>
                <input type="text" name="" id="apply_name" value="" placeholder="请输入您的姓名"  maxlength="5"/>
            </li>
            <li class="sexChose">
                <label for=""><i>*</i>性 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别：</label>
                <span class="duigou" rel="男" >男</span>
                <span rel="女">女</span>
            </li>
            <li>
                <label ><i>*</i>出生年月：</label>
                <input type="text" id="birthDate" onclick="WdatePicker({startDate:'1990-01-01',alwaysUseStartDate:true})" value="1990-01-01"/>
            </li>
            <li>
                <label for=""><i>*</i>相关工作经验：</label>
                <select id="experience" name="experience" class=" selectpicker form-control" onchange="valChange()">
                    <option th:value="''">请选择</option>
                    <option th:each="experience : ${experience}" th:value="${experience.name}"
                            th:text="${experience.name}" th:if="${experience.name != '全部'}">
                    </option>
                </select>
            </li>
            <li class="experienceHide">
                <label for="">相关工作单位：</label>
                <input type="text" id="workedUnits" name="workedUnits" class=" selectpicker form-control"/>
            </li>
            <li class="living">
                <label><i>*</i>居住城市：</label>
                <input type="hidden" value="" id="livingArea">
                <select id="region" >
                    <option value="">请选择区域</option>
                    <option th:each="region : ${regionList}"
                            th:data-id="${region.id}"
                            th:value="${region.name}"
                            th:label="${region.name}"
                    ></option>
                </select>
                <select id="plate">
                    <option >请先选择居住区</option>
                </select>
            </li>
            <li>
                <label for=""><i>*</i>手&nbsp;&nbsp;机&nbsp;&nbsp;号：</label>
                <input type="hidden" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入您的手机号" maxlength="11">
                <!--手机号需要默认显示，并隐藏中间4位-->
                <p class="showTell"></p>
            </li>
            <div th:include="fragment/freepublish::terms"></div>
            <style>
                .padB10{
                    padding-bottom: 0;
                    margin-left: 7px;
                }
            </style>
            <li style="margin-bottom: 0px"><p style="color: red; height: 20px" id="error_info" ><i class="error_info"></i></p></li>
            <a class="arlTcMainBtn" id="saveDelivery">保存并投递</a>
        </ul>
    </div>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script>
        // 区域监听二级联动
        $("#region").change(function () {
            var regionId = $("#region option:selected").data("id");
            if(regionId != null && regionId != ''){
                $.ajax({
                    type: 'post',
                    url: 'https://ltapi.fangxiaoer.com/apiv1/house/viewPlates',
                    data: {regionId:regionId},
                    async: false,
                    dataType: "json",
                    success: function (data){
                        var plates = data.content;
                        console.log(plates);
                        $("#plate").html('');
                        $("#plate").append('<option value="">请选择</option>');
                        for(var i = 0; i < plates.length; i++ ){
                            $("#plate").append('<option value="'+ plates[i].name +'">'+ plates[i].name +'</option>');
                        }
                    }
                });
            }else{
                $("#plate").html('');
                $("#plate").append('<option value="0">请先选择居住区</option>');
            }
        });
    </script>
    <script th:inline="javascript" >

        $(function(){//申请按钮

            var session_phoneNum =[[${#session?.getAttribute('phoneNum')}]]

            $(".arlLeftBTn").on("click",function(){
                var recruitNum ='';
                recruitNum = $(this).parent().parent().find("input").val();//当前职位Id
                if(recruitNum == null || recruitNum == ''){
                    recruitNum = $("#jobAdId").val();
                    console.log(recruitNum);
                }
                var sessionId = $("#sessionId").val();
                $.ajax({//点击申请的同时判断之前是否有简历
                    type:"POST",
                    url:"/getCommonMemberResume",
                    data:{
                        sessionId : sessionId,
                    },
                    dataType:"json",
                    async:false,
                    success:function(data){
                        var showPhone = $("#phone").val()
                        showPhone = "" + showPhone;
                        var tel1show =showPhone.replace(showPhone.substring(3,7), "****")
                        $(".showTell").text(tel1show)
                        console.log(data);
                        if(data != null){
                            var experience = data.jobWorkingLife;
                            var applyName = data.realName;
                            var mobile = $(".showTell").html();
                            var sex = $(".sexChose span[class='duigou']").attr("rel");
                            var workedUnits = data.workedUnits;
                            var birthDate = data.birthDate;
                            var livingArea = data.livingArea;
                            // 这里需要弹窗验证
                            $(".hasResume").show();
                            $(".arlTcHb").show()
                            $("#forSend").click(function(){
                                //直接投递简历
                                saveAndPostResume(workedUnits, livingArea, birthDate, mobile, sessionId, sex, applyName, experience, recruitNum);
                                $(".hasResume").hide();
                            })
                            // 编辑，需要把data返回的字段封装到页面字段里
                            $("#forEdit").click(function(){
                                $(".arlTcHb").show()
                                $(".arlTcMain").show();
                                $("#experience").val(data.jobWorkingLife);
                                $("#birthDate").val(data.birthDate);
                                $("#workedUnits").val(data.workedUnits);
                                $(".hasResume").hide();
                                $("#apply_name").val(data.realName);
                                var livingAreaArr = data.livingArea.split("-");
                                var thisRegionId ;
                                var thisRegionName = livingAreaArr[0];
                                var thisPlateName = livingAreaArr[1];
                                $("#region").val(thisRegionName);
                                //性别
                                console.log(data.sex)
                                $(".sexChose span").removeClass("duigou")
                                $(".sexChose span").each(function () {
                                    console.log($(this).attr("rel"))
                                    if($(this).attr("rel") == data.sex){
                                        $(this).addClass("duigou");
                                    }
                                })

                                // 如果会员选择工作经验，那么显示工作单位
                                console.log(data.jobWorkingLife);
                                if(data.jobWorkingLife != "无"){
                                    $("#experienceHide").show();
                                }else {
                                    $("#experienceHide").hide();
                                }
                                // 初始化现居住区
                                if(thisRegionName != null){
                                    $.ajax({
                                        type: 'post',
                                        url: 'https://ltapi.fangxiaoer.com/apiv1/house/viewRegions',
                                        data: {},
                                        async: false,
                                        dataType: "json",
                                        success: function (data){
//                                            $("#region").empty();
                                            var region = data.content;
                                            for(var i = 0 ; i < data.content.length ; i++){
//                                                if(thisRegionName == region[i].name){
//                                                    $("#region").append('<option value="'+ region[i].name +'" selected="selected">'+ region[i].name +'</option>')
//                                                }else{
//                                                    $("#region").append('<option value="'+ region[i].name +'">'+ region[i].name +'</option>')
//                                                }
                                                if(data.content[i].name == thisRegionName){
                                                    thisRegionId = data.content[i].id;
                                                }

                                            }
                                        }
                                    });
                                    if(thisRegionId != null && thisRegionId != ''){
                                        $.ajax({
                                            type: 'post',
                                            url: 'https://ltapi.fangxiaoer.com/apiv1/house/viewPlates',
                                            data: {regionId:thisRegionId},
                                            async: false,
                                            dataType: "json",
                                            success: function (data){
                                                var plates = data.content;
                                                $("#plate").html('');
                                                for(var i = 0; i < plates.length; i++ ){
                                                    if(thisPlateName == plates[i].name){
                                                        $("#plate").append('<option value="'+ plates[i].name +'" selected="selected">'+ plates[i].name +'</option>');
                                                    }else{
                                                        $("#plate").append('<option value="'+ plates[i].name +'">'+ plates[i].name +'</option>');
                                                    }
                                                }
                                            }
                                        });
                                    }else{
                                        $("#plate").html('');
                                        $("#plate").append('<option value="0">请选择居住区</option>');
                                    }
                                }
                            })
                        }else{
                            $(".arlTcHb").show();
                            $(".arlTcMain").show();
                        }
                    }
                })

                $("#saveDelivery").click(function(){
                    var experience = $("#experience").val();
                    var reg = /^1[1,2,3,4,5,7,8,6,9,0]{1}[0-9]{1}[0-9]{8}$/;
                    var applyName = $("#apply_name").val();
                    var mobile ='';
                    mobile = $("#phone").val();
                    var sex = $(".sexChose span[class='duigou']").attr("rel");
                    var  code = $("#code").val();
                    var workedUnits = $("#workedUnits").val();
                    var birthDate = $("#birthDate").val()
                    var livingArea = $("#region").val()+"-"+$("#plate").val()
                    var s_province = $("#region").val()
                    var s_city = $("#plate").val()
                    if(!$("#checkagree").hasClass("checked")){
                        // timeout(".checkimg", "请阅读服务条款");
                        alert("请仔细阅读并同意服务协议及隐私政策！");
                        return false;
                    }
                    if(!applyName){
                        $("#error_info i").text("请输入您的姓名").show ().delay (3000).fadeOut ();
                        return;
                    }
                    if(applyName.length > 5){
                        $("#error_info i").text("请输入正确的姓名! 请检查是否带有空格,最多可输入5个汉字").show ().delay (3000).fadeOut ();
                        return;
                    }
                    if(!birthDate){
                        $("#error_info i").text("请选择出生日期").show ().delay (3000).fadeOut ();
                        return;
                    }
                    if(!experience){
                        $("#error_info i").text("请选择工作经验").show ().delay (3000).fadeOut ();
                        return;
                    }
                    if(!s_province){
                        $("#error_info i").text("请选择居住区域").show ().delay (3000).fadeOut ();
                        return;
                    }
                    if(!s_city || s_city == "请先选择居住板块" ){
                        $("#error_info i").text("请先选择居住板块").show ().delay (3000).fadeOut ();
                        return;
                    }
                    if(!sessionId && !mobile){
                        $("#error_info i").text("请输入您的手机号码").show ().delay (3000).fadeOut ();
                        return;
                    }else if(!reg.test(mobile)){
                        $("#error_info i").text("请输入正确手机号码").show ().delay (3000).fadeOut ();
                        return;
                    }
                    if(!sessionId && !code){
                        $("#error_info i").text("请输入验证码").show ().delay (3000).fadeOut ();
                        return;
                    }


//                validate();
                    // 2020/11/26 验证部分单独取出，用来验证必填字段
                    saveAndPostResume(workedUnits,livingArea,birthDate,mobile,sessionId,sex,applyName,experience,recruitNum);
                });

            });
            if(sessionId != null && sessionId != "" && sessionId != undefined){
                $(".getCode").hide();
                mobile = $("#phone").val(session_phoneNum);
            }

        });
        function saveAndPostResume(workedUnits,livingArea,birthDate,mobile,sessionId,sex,applyName,experience,recruitNum){
            $.ajax({
                type: "POST",
                url: "/applyPosition",
                data:{
                    workedUnits:workedUnits,
                    livingArea:livingArea,
                    birthDate:birthDate,
                    mobile:mobile,
                    sex:sex,
                    sessionId:sessionId,
                    realName:applyName,
                    jobWorkingLife:experience,
                    jobAdId:recruitNum
                },
                dataType:"json",
                async:false,
                success: function (data) {
                    console.log(data)
                    console.log(status)
                    if(data.status == 1.0){
                        $("#contact_number").text("企业联系电话:"+data.content);
//                            $(".arlTcHb").hide();
                        $(".arlTcMain").hide();
                        $(".yyDk").show();
                    }else{
                        $("#error_info i").text(data.msg).show ().delay (3000).fadeOut ();
                    }
                },
                error: function () {
                    alert("网络延迟请稍后重试!");
                }
            })
        }
        function validate(){

        }
        function removeSelect(index){
            $("#option ul li:eq("+index+") a:eq(0)").click();
        }
        function removeSelectClick(index){
            $('.select_box:eq('+index+') a:eq(0)').click();
        }
        function removeSpan(index){
            $("#option ul li:eq("+index+") span a:eq(0)").click();
        }
        function showIndex(obj) {
            obj.click();
        }

        var i = 0;
        for (i = 0; i < $("#Search_PanSaleDom .select_info").length; i++) {
            if ($("#Search_PanSaleDom .select_box").eq(i).find(".hover").length > 0) {
                $("#Search_PanSaleDom .select_info").eq(i).text($("#Search_PanSaleDom .select_box").eq(i).find(".hover").text())

            }
        }

        <!--弹窗口 单选-->

        $(".sexChose span").click(function(){
            $(".sexChose span").removeClass("duigou")
            $(this).addClass("duigou")
        });

        $(".closeReport").click(function(){
            $(".arlTcHb").hide();
            $(".arlTcMain").hide();
            window.location.reload();
        })
        /* $(".arlTcHb").click(function(){
         $(".arlTcHb").hide();
         $(".arlTcMain").hide();
         $(".yyDk").hide();
         window.location.reload();
         })*/


        //    <!--发送验证码-->
        $(".fxe_ReSendValidateCoad").click(function () {
            $(".fxe-alert").hide()
        })
        sy_confirm.init(2,false)
        $(".fxe_ReSendValidateCoad").click(function () {
            if(sy_confirm.phone($("#phone").val())==true){
                $("#error_info i").text("")
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#phone").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                     $(".fxe-alert").hide()
                     sy_confirm.timeWait()     
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }else{
                $(".fxe-alert").hide()
                $("#error_info i").text(sy_confirm.phone($("#phone").val()))
            }
        })
        //        $(".fxe_ReSendValidateCoad").click(function() {
        //            if (sy_confirm.phone($("#phone").val()) != true) {} else if (sy_confirm.Code($("#phone").val()) == true) {
        //                sy_confirm.timeWait();
        //            }
        //        })
    </script>


    <div class="yyDk" style="display: none;">
        <div class="yyDk_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
        <div>
            <div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"></div>
            <p id="guide_result">恭喜您，投递成功</p>
            <p id="contact_number"></p>
        </div>
    </div>
    <!--<script src="https://static.fangxiaoer.com/js/jsAddress.js"></script>-->
    <script type="text/javascript">
        sy_confirm.init(1,true);
        $(".yydk_heibu").hide();
        $(".yyDk_close").click(function () {
            $(".kfzc_heibu").hide();
            $(".yyDk").hide();
            $(".yydk_heibu").hide();
            window.location.reload();
        });
        function valChange () {
           var txtChange = $("#experience option:selected").val()
            if(txtChange == "无" || txtChange == ""){
                $(".experienceHide").hide()
                console.log(6666)
            }else{
                $(".experienceHide").show()
                console.log(777)
            }
        }



    </script>
</div>
<!--头部搜索框-->
<div  th:fragment="search_information">
    <div   style="background: #FDFDFD;padding: 10px;">
        <div class="searchEsfMap">
            <input type="text" id="searchNames" placeholder="请输入职位名、公司查找  如：房产经纪人" class="searchMapInput" th:value="${search}"/>
            <input  type="button" class="searchMapBtn" value="搜索" >
            <img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png" id="deleteButton" alt="x" style="display: none !important;">
        </div>
    </div>
</div>

<!--页面滚动时出现的头部定位-->
<div th:fragment="infomation_float">
    <div class="infomation_float blueBg">
        <div class="container">
            <div class="unstyled" style="width: 1170px;margin: 0 auto;">
                <h4 >
                	<div th:text="${jobDetail.typeName}"></div> 
                	<div class="unstyled_2" th:text="${jobDetail.jobTitle}"></div>
                	
                </h4>
                <p th:if="${#strings.toString(jobDetail.jobSalaryRangeMin) ne '0' && #strings.toString(jobDetail.jobSalaryRangeMax) ne '0'}" th:text="${jobDetail.jobSalaryRangeMin.replaceAll('.0','')+'-'+jobDetail.jobSalaryRangeMax.replaceAll('.0','')+'/月'}">10000-12000元 </p>
                <p th:if="${(#strings.isEmpty(jobDetail.jobSalaryRangeMin)  || #strings.toString(jobDetail.jobSalaryRangeMin) eq '0') && !#strings.isEmpty(jobDetail.jobSalaryRangeMax || #strings.toString(jobDetail.jobSalaryRangeMax) eq '0')}" th:text="${'面议'}"></p>
                <div class="R">
                     <a th:if="${#session?.getAttribute('sessionId') == null}"  class="otherJobLi-login " href="#login" target="_blank" data-toggle="modal">申请职位</a>
                    <a th:if="${#session?.getAttribute('sessionId') != null}" class="arlLeftBTn otherJobLi-login">申请职位</a>
                    
                    <s class="liaobeiICon">
                        <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal">
                            <text>聊呗</text>
                        </a>
                        <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/imRecruit/'+jobDetail.jobAdId}"  target="_blank">
                            <text>聊呗</text>
                        </a>
                    </s>
                   
                </div>

            </div>
        </div>
    </div>
    <!--页面向下滚动时 头部出现定位条-->
    <!--<script src="static/2015/0321/js/verify.js" type="text/javascript"></script>-->
    <script>
        $(document).ready(function() {
            $("#peitao").hide();
            $(".infomation_float").removeClass("blueBg");

            //视频加载
//                if($("#mediaId").val() != null && $("#mediaId").val() !='null'){
//                    getVideoPath($("#mediaId").val());
//
//                }
            $(window).scroll(function() {
                var s = $(this).scrollTop();
                if (s >= 350) {
                    $(".infomation_float").addClass("blueBg");
                } else {
                    $(".infomation_float").removeClass("blueBg");
                }

            })
        });

        function classon(index) {
            var offsetTop = $("#h" + index).offset().top;
            var scrollTop = $(document).scrollTop();
            var w_height = $(window).height();
            var load_height = $("#h" + index).height();

            //if (scrollTop > offsetTop - w_height && scrollTop < offsetTop + load_height) {
            if (scrollTop > offsetTop - 50) {
                $(".infomation_float").find("li").removeClass("on");
                $(".infomation_float").find("li:eq(" + index + ")").addClass("on");
            }
        }
        $(".infomation_float").find("li").click(function(e) {
            $(".infomation_float").find("li").removeClass("on");
            $(this).addClass("on");
        })
        //$(".bkNavFloat a").click(function (e) {
        //    e.preventDefault();
        //    $('html,body').scrollTo(this.hash, this.hash);
        //});
    </script>
</div>
<!--地图展示公司地址-->
<div th:fragment="company_address_map">
    <input type="hidden" id="lati" th:value="${companyDetail.latitude}">
    <input type="hidden" id="lode" th:value="${companyDetail.longitude}">
    <div th:if="${(!#strings.isEmpty(companyDetail.latitude) && !#strings.isEmpty(companyDetail.longitude) && (#strings.toString(companyDetail.latitude) ne '0.0' && #strings.toString(companyDetail.longitude) ne '0.0'))}">
        <i class="hoverMapShow">查看大图</i>
        <div id="allmap" style="height:250px;width:248px"></div>
        <div class="bigAllmap" >
            <h4>公司地址<s class="bigAllmapClose"><img src="https://static.fangxiaoer.com/m/images/sale/reportClose.jpg" alt=""></s></h4>
            <div id="allmapBig" class="allmapBigShow"></div>
            <script type="text/javascript">
                function bigMap() {
                    var latitudeBig = $("#lati").val();
                    var longitudeBig = $("#lode").val();
                    var plateName = $("#plateName").text();
                    var companyAddress = $("#companyAddress").text();

                    // 百度地图API功能
                    var mapBig = new BMap.Map("allmapBig",{enableMapClick: false}); //去掉点击弹出位置详情
                    var pointBig = new BMap.Point(longitudeBig,latitudeBig);
                    mapBig.centerAndZoom(pointBig, 15);
                    var markerBig = new BMap.Marker(pointBig);  // 创建标注 123.439941,41.798142
//                 mapBig.disableDragging();//禁止拖拽
                    mapBig.addOverlay(markerBig);
                    mapBig.enableScrollWheelZoom(true);     //开启鼠标滚轮缩放
                    markerBig.setAnimation(BMAP_ANIMATION_BOUNCE); // 将标注动画添加到地图中
                    var label = new BMap.Label(companyAddress,{offset:new BMap.Size(30,0)});
                    markerBig.setLabel(label);
                }
            </script>
        </div>
        <div class="heiMuAllmap"></div>
        <script>
            $('.hoverMapShow').click(function(){
                $(".heiMuAllmap").show();
                $(".bigAllmap").show();
                bigMap();
                $("#allmaps").show();
            });
            $("#allmap").click(function () {
                $(".heiMuAllmap").show();
                $(".bigAllmap").show();
                bigMap();
                $("#allmaps").show();
            })
            $(".bigAllmapClose").click(function () {
                $(".bigAllmap").hide()
                $(".heiMuAllmap").hide()
            })
        </script>
        <script type="text/javascript">
            var latitude = $("#lati").val();
            var longitude = $("#lode").val();
            if(latitude == '0.0' && longitude == '0.0'){
                $("#allmap").hide();
            }
            // var plateName = $("#plateName").text();
            // 百度地图API功能
            var map = new BMap.Map("allmap",{enableMapClick: false}); //去掉点击弹出位置详情
            var point = new BMap.Point(longitude,latitude);
            map.centerAndZoom(point, 15);
            var marker = new BMap.Marker(point);  // 创建标注 123.439941,41.798142
            map.disableDragging();//禁止拖拽
            map.addOverlay(marker);
            marker.setAnimation(BMAP_ANIMATION_BOUNCE); // 将标注动画添加到地图中
            map.enableScrollWheelZoom(false);//禁止缩放
            map.disableScrollWheelZoom();//禁用滚轮放大缩小
            map.disableDoubleClickZoom();//禁用双击放大
            //  var label = new BMap.Label(plateName,{offset:new BMap.Size(20,-10)});
            //                        marker.setLabel(label);
        </script>
    </div>
</div>

<!--房产直聘公司地址地图-->
<div th:fragment="companyDetail_map">
 <!--   <input type="hidden" id="lati" th:value="${companyDetail.latitude}">
    <input type="hidden" id="lode" th:value="${companyDetail.longitude}">
    <div th:if="${(!#strings.isEmpty(companyDetail.latitude) && !#strings.isEmpty(companyDetail.longitude) && (#strings.toString(companyDetail.latitude) ne '0.0' && #strings.toString(companyDetail.longitude) ne '0.0'))}">
        <i class="hoverMapShow"><span></span>点击查看地图</i>
        <div id="allmap" style="height:225px;width:900px"></div>
        <div class="bigAllmap" >
            <h4>工作地点<s class="bigAllmapClose"><img src="https://static.fangxiaoer.com/m/images/sale/reportClose.jpg" alt=""></s></h4>
            <div id="allmapBig" class="allmapBigShow"></div>
            <script type="text/javascript">
                function bigMap() {
                    var latitudeBig = $("#lati").val();
                    var longitudeBig = $("#lode").val();
                    var plateName = $("#plateName").text();
                    var companyAddress = $("#companyAddress").text();

                    // 百度地图API功能
                    var mapBig = new BMap.Map("allmapBig",{enableMapClick: false}); //去掉点击弹出位置详情
                    var pointBig = new BMap.Point(longitudeBig,latitudeBig);
                    mapBig.centerAndZoom(pointBig, 15);
                    var markerBig = new BMap.Marker(pointBig);  // 创建标注 123.439941,41.798142
//                 mapBig.disableDragging();//禁止拖拽
                    mapBig.addOverlay(markerBig);
                    mapBig.enableScrollWheelZoom(true);     //开启鼠标滚轮缩放
                    markerBig.setAnimation(BMAP_ANIMATION_BOUNCE); // 将标注动画添加到地图中
                    var label = new BMap.Label(companyAddress,{offset:new BMap.Size(30,0)});
                    markerBig.setLabel(label);
                }
            </script>
        </div>
        <div class="heiMuAllmap"></div>
        <script>
            $('.hoverMapShow').click(function(){
                $(".heiMuAllmap").show();
                $(".bigAllmap").show();
                bigMap();
                $("#allmaps").show();
            });
            $("#allmap").click(function () {
                $(".heiMuAllmap").show();
                $(".bigAllmap").show();
                bigMap();
                $("#allmaps").show();
            })
            $(".bigAllmapClose").click(function () {
                $(".bigAllmap").hide()
                $(".heiMuAllmap").hide()
            })
        </script>
        <script type="text/javascript">
            var latitude = $("#lati").val();
            var longitude = $("#lode").val();
            if(latitude == '0.0' && longitude == '0.0'){
                $("#allmap").hide();
            }
            // var plateName = $("#plateName").text();
            // 百度地图API功能
            var map = new BMap.Map("allmap",{enableMapClick: false}); //去掉点击弹出位置详情
            var point = new BMap.Point(longitude,latitude);
            map.centerAndZoom(point, 15);
            var marker = new BMap.Marker(point);  // 创建标注 123.439941,41.798142
            map.disableDragging();//禁止拖拽
            map.addOverlay(marker);
            marker.setAnimation(BMAP_ANIMATION_BOUNCE); // 将标注动画添加到地图中
            map.enableScrollWheelZoom(false);//禁止缩放
            map.disableScrollWheelZoom();//禁用滚轮放大缩小
            map.disableDoubleClickZoom();//禁用双击放大
            //  var label = new BMap.Label(plateName,{offset:new BMap.Size(20,-10)});
            //                        marker.setLabel(label);
        </script>
    </div>
-->
</div>


<!--聊呗-->
<div th:fragment="liaobei">
    <s class="liaobeiICon">
        <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal">
            <div>
                <i class="liaobeiIcon"></i>
                <text>聊呗</text>
            </div>
        </a>
        <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/imRecruit/'+recruit.jobAdId}"  target="_blank">
            <div>
                <i class="liaobeiIcon"></i>
                <text>聊呗</text>
            </div>
        </a>
    </s>
</div>

<!--改版聊呗-->
<div th:fragment="new_liaobei">
    <s class="liaobeiICon">
        <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal">
            <div class="liaobei">
                <text>聊呗</text>
            </div>
        </a>
        <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/imRecruit/'+recruit.jobAdId}"  target="_blank">
            <div class="liaobei">
                <text>聊呗</text>
            </div>
        </a>
    </s>
</div>


</body>
</html>