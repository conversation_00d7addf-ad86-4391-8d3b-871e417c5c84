<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <!--<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>-->
</head>
<body>
<div class="reportHouse" th:fragment="falsityreport" >
    <link href="https://static.fangxiaoer.com/web/styles/main.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/reportPopup.css"/>

    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <!--举报提交选项-->
    <input type="hidden" id="projectId" th:value="${houseId}">
    <input type="hidden" id="houseTypeReport" th:value="${houseTypeReport}">
    <input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}"/>
    <div class="reportPopupBtn"><i></i>虚假举报</div>
    <div class="reportPopupHb"></div>
    <!--弹出选择框-->
    <div class="reportPopupChoose">
        <h5>请选择举报原因（信息隐私保护）<i  class="reportPopupClose"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="×"></i></h5>
        <div >
            <label  th:each="p:${falsity}">
                <input  type="radio" th:value="${#strings.toString(p.id)}"  name="chose" ><em th:text="${#strings.toString(p.name)}"></em>
            </label>
        </div>
        <button type="button" class="reportPopupChooseBtn" onclick="falsity()">提交</button>
    </div>

    <!--举报成功框-->
    <div class="reportPopupSuc">
        <p>已收到您的举报<br />我们将对举报进行审核<i  class="reportPopupClose"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="×"></i></p>
        <p>稍后我们的客服会核实房源情况，如确认虚假房源会立即对房源下架，并赠送您一份精美礼物。感谢您的支持！</p>
        <a th:if="${houseTypeReport == 1}" th:href="${'/saleHouses'}" >查看其它房源</a>
        <a th:if="${houseTypeReport == 2}" th:href="${'/rents'}" >查看其它房源</a>
    </div>

    <script th:inline="javascript">

            var gsid = [[${#session?.getAttribute('sessionId')}]]

            $(".reportPopupBtn").click(function(){
                if (gsid != null && gsid != '' && gsid != undefined) {
                    $(".reportPopupHb").show();
                    $(".reportPopupChoose").show();
                }else{
                    //登录页面
                    $("#login").show();
                    $(".reportPopupHb").show();
                }
            });

            $(".reportPopupClose").click(function(){
                $(".reportPopupHb").hide()
                $(".reportPopupChoose").hide()
                $(".reportPopupSuc").hide()
            })


        var illegalType = '';
        $(function() {
            $(":radio[name='chose']").click(function(){
                illegalType =  $(":radio[name='chose']:checked").val();
            });
        });
            function falsity(){
                var projectId = $("#projectId").val();
                var houseType = $("#houseTypeReport").val();
                if( illegalType == null|| illegalType == ''){
                    alert("请选择举报原因");
                    return;
                }
                $.ajax({
                    type: "POST",
                    data: {
                        sessionId:gsid,
                        illegalType:illegalType,
                        houseId:projectId,
                        houseType :houseType
                    },
                    url: "/falsity",
                    success: function (data) {
                        if(data.status == 1){
                            $(".reportPopupChoose").hide();
                            $(".reportPopupSuc").show();
                            //location.reload();
                        }else if(data.status == 0){
                            alert(data.msg);
                            $(".reportPopupChoose").hide();
                            $(".reportPopupHb").hide();
                            location.reload();
                        }else{
                            location.reload();
                            alert(data.msg);
                        }
                    }
                });
            }


    </script>
    <!--<script>-->
        <!--//登录关闭X-->
        <!--$("#loginClose").click(function () {-->
            <!--$("#login").hide();-->
            <!--$(".reportPopupHb").hide();-->
        <!--});-->
    <!--</script>-->
</div>
</body>
</html>