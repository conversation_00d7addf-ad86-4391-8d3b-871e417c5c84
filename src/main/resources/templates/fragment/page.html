<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>

</head>
<body>
<div th:fragment="page">
    <div class="page" th:if="${totalPages gt 1}">
        <!--分页-->
        <div id="Pager1">
            <!--<a th:if="${number eq 1}" disabled="disabled" style="margin-right:5px;cursor: pointer">首页</a>-->
            <a th:if="${number gt 1}" th:href="@{${pageUrl} + '-n1'}"
               style="margin-right:5px;cursor: pointer;">首页</a>
            <!--<a th:if="${number eq 1}" disabled="disabled" style="margin-right:5px;cursor: pointer;">上一页</a>-->
            <a th:if="${number gt 1}" th:href="@{${pageUrl} + '-n' + ${number-1}}"
               style="margin-right:5px;cursor: pointer;">上一页</a>
            <a th:if="${minPage gt 10}" th:href="@{${pageUrl} + '-n' +${minPage-1}}"
               style="margin-right:5px;cursor: pointer;">...</a>
            <th:block th:if="${number ne 1 and number gt minPage}">
                <a th:if="${number ge minPage}" th:each="i : ${#numbers.sequence( minPage, number-1)}"
                   th:href="@{${pageUrl} + '-n'+${i}}" th:text="${i}"
                   style="margin-right:5px;"></a>
            </th:block>
            <span style="margin-right:5px;font-weight:Bold;color:red;" th:text="${number}"></span>
            <th:block th:if="${number+1 le toPage}">
                <a th:each="i : ${#numbers.sequence( number+1, toPage)}"
                   th:href="@{${pageUrl} + '-n' + ${i}}" th:text="${i}"
                   style="margin-right:5px;"></a>
            </th:block>
            <a th:if="${toPage lt totalPages}" th:href="@{${pageUrl} + '-n'+${toPage +1}}"
               style="margin-right:5px;">...</a>
            <a th:if="${number lt totalPages}" th:href="@{${pageUrl} + '-n' + ${number+1}}"
               style="margin-right:5px;">下一页</a>
            <!--<a th:if="${number eq totalPages}" disabled="disabled" style="margin-right:5px;">下一页</a>-->
            <a th:if="${number lt totalPages and pageType ne null}" th:href="@{${pageUrl} + '-n' + ${totalPages}}" style="margin-right:5px;">尾页</a>
            <!--<a th:if="${number lt totalPages}" th:href="@{'javascript:__doPostPage(\'Pager\','+${totalPages}+')'}"-->
            <!--style="margin-right:5px;">尾页</a>-->
            <!--<a th:if="${number eq totalPages}" disabled="disabled" style="margin-right:5px;">尾页</a>-->
        </div>
    </div>
</div>
<div th:fragment="pageContainParams">
    <div class="page" th:if="${totalPages gt 1}">
        <!--分页-->
        <div id="Pager1">
            <!--<a th:if="${number eq 1}" disabled="disabled" style="margin-right:5px;cursor: pointer">首页</a>-->
            <a th:if="${number gt 1}" th:href="@{${pageUrl} + '-n1'+${endUrl}}"
               style="margin-right:5px;cursor: pointer;">首页</a>
            <!--<a th:if="${number eq 1}" disabled="disabled" style="margin-right:5px;cursor: pointer;">上一页</a>-->
            <a th:if="${number gt 1}" th:href="@{${pageUrl} + '-n' + ${number-1}+${endUrl}}"
               style="margin-right:5px;cursor: pointer;">上一页</a>
            <a th:if="${minPage gt 10}" th:href="@{${pageUrl} + '-n' +${minPage-1}+${endUrl}}"
               style="margin-right:5px;cursor: pointer;">...</a>
            <th:block th:if="${number ne 1 and number gt minPage}">
                <a th:if="${number ge minPage}" th:each="i : ${#numbers.sequence( minPage, number-1)}"
                   th:href="@{${pageUrl} + '-n'+${i}+${endUrl}}" th:text="${i}"
                   style="margin-right:5px;"></a>
            </th:block>
            <span style="margin-right:5px;font-weight:Bold;color:red;" th:text="${number}"></span>
            <th:block th:if="${number+1 le toPage}">
                <a th:each="i : ${#numbers.sequence( number+1, toPage)}"
                   th:href="@{${pageUrl} + '-n' + ${i}+${endUrl}}" th:text="${i}"
                   style="margin-right:5px;"></a>
            </th:block>
            <a th:if="${toPage lt totalPages}" th:href="@{${pageUrl} + '-n'+${toPage +1}+${endUrl}}"
               style="margin-right:5px;">...</a>
            <a th:if="${number lt totalPages}" th:href="@{${pageUrl} + '-n' + ${number+1}+${endUrl}}"
               style="margin-right:5px;">下一页</a>
            <!--<a th:if="${number eq totalPages}" disabled="disabled" style="margin-right:5px;">下一页</a>-->
            <a th:if="${number lt totalPages and pageType ne null}" th:href="@{${pageUrl} + '-n' + ${totalPages}+${endUrl}}" style="margin-right:5px;">尾页</a>
            <!--<a th:if="${number lt totalPages}" th:href="@{'javascript:__doPostPage(\'Pager\','+${totalPages}+')'}"-->
            <!--style="margin-right:5px;">尾页</a>-->
            <!--<a th:if="${number eq totalPages}" disabled="disabled" style="margin-right:5px;">尾页</a>-->
        </div>
    </div>
</div>
</body>
</html>