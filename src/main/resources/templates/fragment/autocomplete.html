<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<body>
    <div th:fragment="autocomplete">
        <script type="text/javascript">
            function autoSearch(searchId,showName,valueId,url) {
                $("#" + searchId).autocomplete(url, {
                    multiple: false,
                    max: 15,
                    parse: function (data) {
                        return $.map(eval(data), function (row) {
                            return {
                                data: row,
                                value: eval("row."+showName),
                                id: eval("row."+valueId),
                                result: eval("row."+showName)
                            };
                        });
                    },
                    formatItem: function (item) {
                        switch (item.projectType) {
                            case "1":
                                var type = "住宅";
                                break;
                            case "2":
                                var type = "洋房";
                                break;
                            case "3":
                                var type = "商业";
                                break;
                            case "4":
                                var type = "公寓";
                                break;
                            case "5":
                                var type = "别墅";
                                break;
                        }
                        if (item.tableId == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            return "<i style='float:right'>" + type + " [ 直达 ]</i>" + eval("item."+showName);
                        }

                    }
                }).result(function (e, item) {
                    window.location.href = "https://sy.fangxiaoer.com/house/view/"+ item.tableId;
                });

                $(".search_btn").click(function () {
                    var txt = $(".ac_input").attr("name")
                    input = $(this).prev().val() //取输入框值

                    window.location.href = "/houseCenter/newHouseSearch/b";
                });
            }
        </script>
    </div>
</body>
</html>