<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <style>
        #closeContrast {
            width: 24px;
            float: right;
            margin-top: -22px;
        }

    </style>
</head>
<body>
<!--首页导航-->
<div class="headTOP" th:fragment="indexNav" >
    <div style="width:1170px;margin:0 auto">
        <div class="logo">
            <a href="/"><img src="https://static.fangxiaoer.com/web/images/ico/head/logo.png" alt="房小二网"/></a>
        </div>
        <div style="width: 98px;height: 20px;float: left;"></div>
       <!-- <div class="city">沈阳
            <div class="city_tc">
                <i></i>
                <p>选择城市</p>
                <ul >
                    <li><a href="https://sy.fangxiaoer.com" target="_blank">沈阳</a></li>
                    <li><a href="http://tj.fangxiaoer.com" target="_blank">天津</a></li>
                    <li><a href="http://cc.fangxiaoer.com" target="_blank">长春</a></li>
                    <li><a href="http://dl.fangxiaoer.com" target="_blank">大连</a></li>
                    <li><a href="http://yk.fangxiaoer.com" target="_blank">营口</a></li>
                    <li><a href="http://as.fangxiaoer.com" target="_blank">鞍山</a></li>
                    <li><a href="http://bx.fangxiaoer.com" target="_blank">本溪</a></li>
                    <li><a href="http://jz.fangxiaoer.com" target="_blank">锦州</a></li>
                    <li><a href="http://ly.fangxiaoer.com" target="_blank">辽阳</a></li>
                    <li><a href="http://fs.fangxiaoer.com" target="_blank">抚顺</a></li>
                    <li><a href="http://tl.fangxiaoer.com" target="_blank">铁岭</a></li>
                    <li><a href="http://heb.fangxiaoer.com" target="_blank">哈尔滨</a></li>
                    <li><a href="http://hld.fangxiaoer.com" target="_blank">葫芦岛</a></li>
                </ul>
            </div>
        </div>
       --> <div class="login">
            <a href="https://download.fangxiaoer.com/" target="_blank" class="recode">手机找房 ｜
                <div>
                    <img src="https://static.fangxiaoer.com/web/images/download/ewm_4.gif" alt="">
                </div>
            </a>
            <span th:if="${#session?.getAttribute('sessionId') == null}">
                <div class="login_xl">
                    <a href="https://my.fangxiaoer.com/login" target="_blank">登录</a>
                    <p>
                        <i></i>
                        <a href="https://my.fangxiaoer.com/login" target="_blank">个人登录</a>
                        <a href="https://agent.fangxiaoer.com/" target="_blank">端口登录</a>
                    </p>
                </div>
                <!--<a target="_blank" href="/skipToVip"><img-->
                        <!--src="https://static.fangxiaoer.com/web/images/my/admin/vipLogo.png"-->
                        <!--style="margin-top: 7px;"></a>-->
                    ｜<a href="https://my.fangxiaoer.com/register.aspx" target="_blank">注册</a>
            </span>
            <span th:if="${#session?.getAttribute('sessionId') != null}">
                    <a href="https://my.fangxiaoer.com/index.aspx"><em
                            th:text="${'Hi, ' + #session?.getAttribute('userName')}"
                            th:title="${'Hi, ' + #session?.getAttribute('userName')}" id="msgInfo"></em></a>
                <!--&lt;!&ndash;<b th:text="${(#session?.getAttribute('newMsg') != null && #session?.getAttribute('newMsg') != '0' ? '('+#session?.getAttribute('newMsg') +')' : '')}"></b>&ndash;&gt;-->
                    <!--<a href="/skipToVip" target="_blank"><img-->
                            <!--src="https://static.fangxiaoer.com/web/images/my/admin/vipLogo.png"-->
                            <!--th:style="(${firstNavIndex} == 1)? 'margin-top: 21px;margin-right: 5px;vertical-align: top;':'margin-top: 16px;margin-right: 5px;vertical-align: top;'"></a>-->
                <a href="/quit">退出</a>

            </span>
            <!--<div class="MessageHints" style="display: none" th:if="${!#lists.isEmpty(#session?.getAttribute('newMsgList'))}">-->
                <!--<h4>您的提问有人回答</h4>-->
                <!--<ul th:each="m:${#session?.getAttribute('newMsgList')}">-->
                    <!--<li><a th:href="${'/saleVillages/'+m.askId+'/getAskDetail.htm'}"th:onclick="${'isRead('+m.askId+')'}" target="_blank"><span ><i th:text="${m.askTitle}" th:title="${m.askTitle}"></i><s>（<th:block th:text="${m.answerNumber}"></th:block>）</s></span></a></li>-->
                    <!--&lt;!&ndash;<li><a href="" target="_blank"><span>担心错过得分上打担心错过得分上打担心错过得分上打开</span><s>（2）</s></a></li>&ndash;&gt;-->
                    <!--&lt;!&ndash;<li><a href="" target="_blank"><span>担心错过得分上打开</span><s>（2）</s></a></li>&ndash;&gt;-->
                    <!--&lt;!&ndash;<li><a href="" target="_blank"><span>担心错过得分上打开</span><s>（2）</s></a></li>&ndash;&gt;-->
                <!--</ul>-->
            <!--</div>-->
            <script type="text/javascript">
                $("#msgInfo").mousemove(function(){
                    $(".MessageHints").show();
                    $("#msgInfo").css('color','#ff5200');
                });
                $("#msgInfo").mouseleave(function(){
                    $("#msgInfo").css('color','#333');
                });
                $(".MessageHints").mouseleave(function(){
                    $(".MessageHints").hide();
                });

                function isRead(id) {
                    $.ajax({
                        type: "post",
                        url: "/msgIsRead",
                        data: {
                            askId: id,
                        },
                        dataType: "json",
                    });
                }
            </script>
        </div>
        <div class="nav_ul">
            <!-- 没有配置项 -->
            <ul>
                <li><a href="/" class="hover" >首页</a></li>
                <li><a href="/houses/" >新房</a></li>
                <li><a href="/saleHouses/" >二手房</a></li>
                <li><a href="/rents/" >租房</a></li>
                <li><a href="/shops/" >商铺</a></li>
                <li><a href="/scriptoriums/" >写字楼</a></li>
                <li><a href="/news/" >资讯</a></li>
                <!--<li><a th:href="@{'/houseKeeper1.htm'}" >小二管家</a></li>-->
                <li><a th:href="@{/new_secondPublish/}" >免费发布</a></li>
            </ul>
        </div>
        <div class="clearfix"></div>
    </div>
</div>
<!--其他页 一级导航-->
<div id="head2017" th:fragment="firstNav">
    <div class="advertising" th:if="${indexTab == 1 and !#lists.isEmpty(normalTop)}" th:style="${'background:url('+normalTop.get(0).image+') top center'}">
        <a th:href="${normalTop.get(0).url}" target="_blank" style="width: 100%;height: 100%;display: block;">
            <!--<img th:src="${normalTop.get(0).image}" th:alt="${normalTop.get(0).projectName}">-->
        </a>
    </div>
    <div class="w" style="border: 0" >
        <h1>
            <a href="/"><img th:src="${'https://static.fangxiaoer.com/web/images/ico/head/'+ (firstNavIndex ==1 ? 'logo.png':'logo_white.png')}" alt="房小二网"></a>
        </h1>
        <div style="width: 98px;height: 20px;float: left;"></div>
       <!-- <div class="city">沈阳
            <div class="city_tc">
                <i></i>
                <p>选择城市</p>
                <ul >
                    <li><a href="https://sy.fangxiaoer.com" target="_blank">沈阳</a></li>
                    <li><a href="http://tj.fangxiaoer.com" target="_blank">天津</a></li>
                    <li><a href="http://cc.fangxiaoer.com" target="_blank">长春</a></li>
                    <li><a href="http://dl.fangxiaoer.com" target="_blank">大连</a></li>
                    <li><a href="http://yk.fangxiaoer.com" target="_blank">营口</a></li>
                    <li><a href="http://as.fangxiaoer.com" target="_blank">鞍山</a></li>
                    <li><a href="http://bx.fangxiaoer.com" target="_blank">本溪</a></li>
                    <li><a href="http://jz.fangxiaoer.com" target="_blank">锦州</a></li>
                    <li><a href="http://ly.fangxiaoer.com" target="_blank">辽阳</a></li>
                    <li><a href="http://fs.fangxiaoer.com" target="_blank">抚顺</a></li>
                    <li><a href="http://tl.fangxiaoer.com" target="_blank">铁岭</a></li>
                    <li><a href="http://heb.fangxiaoer.com" target="_blank">哈尔滨</a></li>
                    <li><a href="http://hld.fangxiaoer.com" target="_blank">葫芦岛</a></li>
                </ul>
            </div>
        </div> -->
        <script th:inline="javascript">
            var ttt = [[${firstNavIndex}]];
            console.log(ttt)
        </script>
        <div class="head_nav">
            <ul>
                <li>
                    <a href="/">首页</a>
                </li>
                <li>
                    <a href="/houses/" th:class=" (${firstNavIndex} == 2)? 'hover' : '' ">新房</a>
                    <p>
                        <i></i>
                        <a href="/houses/"><span>找楼盘</span></a>
                        <a href="/brandCompany/"><span>品牌展示区</span></a>
                        <a href="/subways/k1"><span>地铁沿线</span></a>
                        <a th:href="@{'/projectRank/1'}"><span>热门排行</span></a>
<!--                        <a href="/schools/"><span>优质学区</span></a>-->
                        <a href="/static/houseMap.htm"><span>地图找房</span></a>
                        <a th:href="@{'/helpSearch?ids=1'}" class=""><span>我要买房</span></a>
<!--                        <a th:href="@{'/liveList'}" class=""><span>直播看房</span></a>-->
                    </p>
                </li>
                <li>
                    <a href="/saleHouses/" th:class=" (${firstNavIndex} == 3)? 'hover' : '' ">二手房</a>
                    <p><i></i>
                        <a href="/saleHouses/"><span>在售房源</span></a>
                        <a href="/saleVillages/" target="_blank"><span>小区找房</span></a>
                        <!--<a href="/agentRecruit"><span>房产直聘</span></a>-->
                        <a href="/salemap/"><span>地图找房</span></a>
                        <a th:href="${'/salemap'+'?subway'}"><span>地铁沿线</span></a>
                        <!--<a href="/dealSales/"><span>成交房源</span></a>-->
<!--                        <a href="/needSeconds/">求购信息</a>-->
                        <a th:href="@{'/helpSearch?ids=2'}" class=""><span>我要买房</span></a>

                    </p>
                </li>
                <li>
                    <a href="/rents/" th:class=" (${firstNavIndex} == 4)? 'hover' : '' ">租房</a>
                    <p>
                        <i></i>
                        <a href="/rents/"><span>出租房源</span></a>
                        <a href="/villages/" target="_blank"><span>小区找房</span></a>
                        <a href="/static/rentmap.htm"><span>地图找房</span></a>
                        <a th:href="${'/static/rentmap.htm'+'?subway'}"><span>地铁沿线</span></a>
                        <!--<a href="/dealRents/">成交房源</a>-->
                        <!--                        <a href="/needRents/">求租信息</a>-->
                        <a th:href="@{'/helpSearch?ids=3'}" class=""><span>求租</span></a>

                    </p>
                </li>

                <li>
                    <a href="/shops/" th:class="${firstNavIndex} == 5 or ${firstNavIndex} == 9 or ${firstNavIndex} == 10?'hover':' '">商铺写字楼</a>
                    <p>
                        <i></i>
                        <a th:href="@{/shops/b1}"><span>商铺出售</span></a>
                        <a th:href="@{/shops/b2}"><span>商铺出租</span></a>
                        <a th:href="@{/scriptoriums/b5}"><span>写字楼出售</span></a>
                        <a th:href="@{/scriptoriums/b4}"><span>写字楼出租</span></a>
                        <!--<a th:href="@{/officeProjects/}"><span>写字楼项目</span></a>-->
                        <a th:href="@{/helpSearch?ids=4}"><span>商业选址</span></a>
                    </p>

                </li>

                <!--<li>
                    <a href="/shops/"  th:class=" (${firstNavIndex} == 5)? 'hover' : '' ">商铺</a>
                    <p>
                        <i></i>
                        <a th:href="@{/shops/b1}"><span>商铺出售</span></a>
                        <a th:href="@{/shops/b2}"><span>商铺出租</span></a>
                        <a th:href="@{/houses/pt3}"><span>商铺新盘</span></a>
                        <a th:href="@{'/helpSearch?ids=4'}" class=""><span>商铺选址</span></a>
                        &lt;!&ndash;<a href="/dealShops/">成交房源</a>&ndash;&gt;
                        &lt;!&ndash;<a th:href="@{/shopsell/}">发布商业</a>&ndash;&gt;
                        &lt;!&ndash;<a th:href="@{'/helpSearch?ids=4'}" class="hot_red">帮您找房</a>&ndash;&gt;
                    </p>
                </li>
                <li>
                    <a href="/scriptoriums/"  th:class=" (${firstNavIndex} == 9)? 'hover' : '' ">写字楼</a>
                    <p>
                        <i></i>
                        <a th:href="@{/scriptoriums/}"><span>写字楼房源</span></a>
                        <a th:href="@{/officeProjects/}"><span>写字楼项目</span></a>
                        <a th:href="@{'/helpSearch?ids=9'}" class=""><span>企业选址</span></a>
                    </p>
                </li>-->

                <!--<li>
                    <a href="/housingprice/" th:class=" (${firstNavIndex} == 20)? 'hover' : '' ">房价</a>
                </li>-->
                <li>
                    <a href="/news/" class="" th:class=" (${firstNavIndex} == 6)? 'hover' : '' ">资讯</a>
                    <p>
                        <i></i>
                        <a href="/news/149"><span>行业关注</span></a>
                        <a href="/news/151"><span>楼盘要闻</span></a>
                        <a href="/news/166"><span>学区资讯</span></a>
                        <!--<a href="/news/156"><span>专题推荐</span></a>-->
                        <a href="/videos/" class=""><span>房产视频</span></a>
                    </p>
                </li>

                <li>
                    <a th:href="@{'/helpSearch?ids=1'}" th:class="(${firstNavIndex} == 7)? 'hover' : '' ">我要买房</a>
                </li>
                <li>
                    <a href="/static/saleHouse/saleHouse.htm" th:class=" (${firstNavIndex} == 8)? 'hover' : '' ">我要卖房</a>
                </li>
                <!--<li>
                    <a th:href="@{'/projectRank/1'}" th:class=" (${firstNavIndex} == 7)? 'hover' : '' ">热门排行</a>
                </li>
                <li>
                    <a href="/new_secondPublish/" th:class=" (${firstNavIndex} == 8)? 'hover' : '' ">免费发布</a>
                    <p>
                        <i></i>
                        <a th:href="@{/new_secondPublish/}" ><span>二手房</span></a>
                        <a th:href="@{/rentwhole}" ><span>租房</span></a>
                        <a th:href="@{/shopsell}" ><span>商铺</span></a>
                        <a th:href="@{/officeRent}" ><span>写字楼</span></a>
                    </p>
                </li>
                <li>
                    <a href="/fastSeek" th:class=" (${firstNavIndex} == 88)? 'hover' : '' ">房产快搜</a>
                </li>-->
            </ul>
        </div>
        <div class="head_right_login">
            <a href="https://download.fangxiaoer.com/" target="_blank"  class="recode">手机找房 <span>|</span>
                <div>
                    <img src="https://static.fangxiaoer.com/web/images/download/ewm_4.gif" alt="">
                </div>
            </a>
            <th:block th:if="${#session?.getAttribute('sessionId') != null}">
                <a href="https://my.fangxiaoer.com/index.aspx" target="_blank" id="msgInfo"><em th:text="${#session?.getAttribute('userName')}" th:title="${#session?.getAttribute('userName')}"></em></a>
                <!--<b th:text="${(#session?.getAttribute('newMsg') != null && #session?.getAttribute('newMsg') != '0' ? '('+#session?.getAttribute('newMsg') +')' : '')}"></b>-->
                <!--<a target="_blank" href="/skipToVip"><img src="https://static.fangxiaoer.com/web/images/my/admin/vipLogo.png" th:style="(${firstNavIndex} == 1)? 'margin-top: 21px;margin-right: 5px;vertical-align: top;':'margin-top: 16px;margin-right: 5px;vertical-align: top;'"></a>-->
                <a href="/quit">退出</a>
            </th:block>
            <th:block th:if="${#session?.getAttribute('sessionId') == null}">
                <div class="login">
                    <a href="https://my.fangxiaoer.com/login" target="_blank">登录</a>
                    <p>
                        <i></i>
                        <a href="https://my.fangxiaoer.com/login" target="_blank">个人登录</a>
                        <a href="https://agent.fangxiaoer.com/" target="_blank">端口登录</a>
                    </p>
                </div>
                <!--<a target="_blank" href="/skipToVip">-->
                    <!--<img src="https://static.fangxiaoer.com/web/images/my/admin/vipLogo.png" th:style="(${firstNavIndex} == 1)? 'margin-top: 22px;margin-right: 5px;vertical-align: top;':'margin-top: 17px;margin-right: 5px;vertical-align: top;'">-->
                <!--</a>-->
                <span class="shuxian">|</span><a href="https://my.fangxiaoer.com/register" class="register">注册</a>
            </th:block>
            <!--<div class="MessageHints" style="display: none" th:if="${#session?.getAttribute('newMsgList') != null && #session?.getAttribute('newMsgList') != ''}">-->
                <!--<h4>您的提问有人回答</h4>-->
                <!--<ul th:each="m:${#session?.getAttribute('newMsgList')}">-->
                    <!--<li><a th:href="${'/saleVillages/'+m.askId+'/getAskDetail.htm'}"th:onclick="${'isRead('+m.askId+')'}" target="_blank"><span><i th:text="${m.askTitle}" th:title="${m.askTitle}"></i><s>（<th:block th:text="${m.answerNumber}"></th:block>）</s></span></a></li>-->
                    <!--&lt;!&ndash;<li><a href="" target="_blank"><span>担心错过得分上打担心错过得分上打担心错过得分上打开</span><s>（2）</s></a></li>&ndash;&gt;-->
                    <!--&lt;!&ndash;<li><a href="" target="_blank"><span>担心错过得分上打开</span><s>（2）</s></a></li>&ndash;&gt;-->
                    <!--&lt;!&ndash;<li><a href="" target="_blank"><span>担心错过得分上打开</span><s>（2）</s></a></li>&ndash;&gt;-->
                <!--</ul>-->
            <!--</div>-->
            <script type="text/javascript">
                $("#msgInfo").mousemove(function(){
                    $(".MessageHints").show();
                });
                $(".MessageHints").mouseleave(function(){
                    $(".MessageHints").hide();
                });

                function isRead(id) {
                    $.ajax({
                        type: "post",
                        url: "/msgIsRead",
                        data: {
                            askId: id,
                        },
                        dataType: "json",
                    });
                }
            </script>
        </div>
        <div class="cl"></div>
    </div>
</div>
<!--搜索-->
<div id="search2017" th:fragment="searchNav">
    <!--<script src="/js/jquery.autocomplete.js" type="text/javascript"></script>-->
    <!--<script src="/js/head2017.js" type="text/javascript"></script>-->
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <style>
        .ui-autocomplete {max-width: 462px;overflow-y: auto;overflow-x: hidden;padding-right: 20px;z-index: 100000 !important;}
        .ui-menu .ui-menu-item{line-height: 30px;height: 30px;}
        .ui-menu .ui-menu-item a{width: 101.5%;}
        * html .ui-autocomplete {height: 100px;}
    </style>
    <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/searchCommon.js?v=20201030" type="text/javascript" charset="utf-8"></script>
    <script th:inline="javascript">
        var sessionId = [[${session.muser}]];
    </script>
    <div class="w" id="searchFloat">
        <div class="search">
            <div class="searchFloatKuang">
                <a href="/" class="searchFloatImg"><img src="https://static.fangxiaoer.com/web/images/ico/head/logo_white.png" alt="房小二网"></a>
                <input id="txtkeys" type="text" name="1" th:if="${type} == 1 or ${type} == 6" autocomplete="off"  th:value="${searchKey}" placeholder="请输入楼盘名称开始找房" class="ac_input" >
                <input id="txtkeys" type="text" name="2" th:if="${type} == 2" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
                <input id="txtkeys" type="text" name="3" th:if="${type} == 3" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
                <input id="txtkeys" type="text" name="4" th:if="${type} == 4" th:value="${searchKey}" placeholder="请输入区域、板块开始找房" class="ac_input">
                <input id="txtkeys" type="text" name="5" th:if="${type} == 5" th:value="${searchKey}" placeholder="请输入您感兴趣的内容进行搜索，比如楼盘名字" class="ac_input">
                <input id="txtkeys" type="text" name="7" th:if="${type} == 7" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
                <input id="txtkeys" type="text" name="8" th:if="${type} == 8" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
                <input id="txtkeys" type="text" name="9" th:if="${type} == 9" th:value="${searchKey}" placeholder="请输入区域、板块或写字楼名称找房" class="ac_input">
                <input id="txtkeys" type="text" name="10" th:if="${type} == 10" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
                <input id="txtkeys" type="text" name="11" th:if="${type} == 11" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
                <input id="txtkeys" type="text" name="12" th:if="${type} == 12" th:value="${searchKey}" placeholder="请输入您感兴趣的楼盘或地段名" class="ac_input">
                <input id="txtkeys" type="text" name="13" th:if="${type} == 13" th:value="${searchKey}" placeholder="请输入您感兴趣的楼盘或地段名" class="ac_input">
                <input id="txtkeys" type="text" name="14" th:if="${type} == 14" th:value="${searchKey}" placeholder="请输入您感兴趣的楼盘或地段名" class="ac_input">
                <input id="txtkeys" type="text" name="15" th:if="${type} == 15" th:value="${searchKey}" placeholder="请输入您感兴趣的内容进行搜索" class="ac_input">
                <input type="button" value="楼盘搜索" th:if="${searchButton eq 'houseKepper'}" class="btn_search search_btn">
                <input type="button" value="搜 索" th:unless="${searchButton eq 'houseKepper'}" class="btn_search search_btn">
                <img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png"  id="deleteButton" alt="x">


                <span class="rightASpan" th:if="${listType == 0}">
                    <a th:if="${type} == 1 or ${type} == 14" th:href="@{'/helpSearch?ids=1'}" target="_blank" class="rightA">我要买房</a>
                    <a th:if="${type} == 2" th:href="@{/new_secondPublish/}" target="_blank" class="rightA">我要卖房</a>
                    <a th:if="${type} == 3" th:href="@{/rentwhole}" target="_blank" class="rightA">我要出租</a>
                    <a th:if="${type} == 4" th:href="@{/shopsell}" target="_blank" class="rightA">我要出售</a>
                    <a th:if="${type} == 9" th:href="@{/officeSell}" target="_blank" class="rightA">我要出售</a>
                    <a th:if="${type} == 13" th:href="@{/officeSell}" target="_blank" class="rightA">我要出售</a>
                    <i class="rightAS" ></i>
                    <a th:if="${type} == 2" th:href="@{'/helpSearch?ids=2'}" target="_blank" class="rightA" id="buy">我要买房</a>
                    <a th:if="${type} == 3" th:href="@{'/helpSearch?ids=3'}" target="_blank" class="rightA" id="rent">求租</a>
                </span>
            </div>
        </div>
        <div class="hotBuild" th:if="${searchTab == 0}" style="display: none;">
            <div>
                热门楼盘
            </div>
            <ul>
                <a th:each="one,i:${ussearch}" th:href="${'/house/'+one.projectId+'-'+one.projectType+'.htm'}" target="_blank">
                    <li>
                        <span th:text="${one.projectName}"></span>
                        <span class="hotArea" th:text="${'['+ one.regionName+ ']'}"></span>
                    </li>
                </a>
            </ul>
        </div>
    </div>
    <!--页面向下滚动时 头部出现搜索定位条-->
    <th:block  th:if="${listType == 0}">
    <script>
        $("input[name='1']").focus(function () {
            if ($("input[name='1']").val() ==""){
                $(".hotBuild").show()
            }
        })
        function stopPropagation(e) {
            if (e.stopPropagation)
                e.stopPropagation();
            else
                e.cancelBubble = true;
        }

        $(document).bind('click',function(){
            $(".hotBuild").hide()
        });

        $('.hotBuild,.ac_input').bind('click',function(e){
            stopPropagation(e);
        });
        $("input[name='1']").live('input propertychange', function()
        {
            if ($("input[name='1']").val() ==""){
                $(".hotBuild").show()
            }else{
                $(".hotBuild").hide()
            }
        })

            $(document).ready(function() {
            $("#peitao").hide();
            $("#searchFloat>div").removeClass("searchFloat");
            $(window).scroll(function() {
                var s = $(this).scrollTop();
                if (s >= 532) {
                    $("#searchFloat>div").addClass("searchFloat");

                } else {
                    $("#searchFloat>div").removeClass("searchFloat");

                            }

            })
            var buy = $('#buy').text();
            var rent = $('#rent').text();
            if( buy != "我要买房"&& rent != "求租"){
                $('.rightAS').hide();
            }
        });

    </script>
    </th:block>
    <script th:inline="javascript">
        if ($("input[name='1']")){
            $.ajax({
                type:'post',
                url:"/getAdvertisementList",
                data:{
                    type:'7',
                },
                success:function (data) {
                    if (data.status == 1.0){
                        var name = null;
                        try {
                            name = data.content.searchAd[0].projectName;
                        } catch (e) {
                            console.log("no AD");
                        }
                        if(name){
//                            alert(name);
                            $("input[name='1']").attr("placeholder",'请输入楼盘名称开始找房  '+name);
//                        请输入楼盘名称开始找房  如：首创光和城
                        }
                    }
                }
            });
        }



    </script>
    <script>
        $(window).scroll(function(){
            $(".ui-autocomplete").hide()
        })
    </script>
</div>
<!--列表页无房源数据时显示-->
<div th:fragment="list_bottom">
    <div class="warning" th:style="${msg eq '0'}?'display: block':'display: none'">
        <p>
            很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
            懒得搜索？！<a href="/helpSearch.htm" target="_blank">点击免费体验购房服务方案>></a>
        </p>
    </div>
</div>
<!--列表页底部-->
<div th:fragment="footer_list">
    <style>
        .footer_copy a{ text-decoration: none; float: none; border-right: 0px solid #b3b3b3 !important;}
    </style>
    <div class="cl"></div>
    <div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee">
        <div class="footer_copy footer_width" style="padding:20px 0; text-align: center;">
            <a>佳恒传媒</a>
            <a href="https://info.fangxiaoer.com/Index" target="_blank">关于房小二</a>
            <a href="https://info.fangxiaoer.com/About/contactus" target="_blank">联系我们</a>
            <a href="/static/sysitemap.htm" target="_blank">网站地图</a>
            <a href="https://download.fangxiaoer.com" target="_blank">手机找房</a>
            <a href="https://info.fangxiaoer.com/About/copyright" target="_blank">版权声明</a>
            <a href="https://info.fangxiaoer.com/About/disclaimer" target="_blank">免责声明</a>
            <a href="https://info.fangxiaoer.com/About/policy" target="_blank">隐私政策</a>
            <a href="https://info.fangxiaoer.com/About/protocol" target="_blank">服务协议</a>
            <div class="cl"></div>
            <p style="text-align:center;color:#999">版权所有 Copyright © <th:block th:text="'2008-' + ${#temporals.format(#temporals.createNow(), 'yyyy')}"></th:block> www.fangxiaoer.com All Rights Reserved　<a class="bor_n" style="color:#999;text-decoration: none;float: none;" href="https://beian.miit.gov.cn/" target="_blank">ICP备案：辽B2-20150134-1</a>
            </p>
        </div>
    </div>
    <script>
        //select划出时隐藏选择
        $("select").hover(function(){},function(){
            $(this).attr("disabled","disabled")
            $(this).removeAttr("disabled")
        })
    </script>
</div>
<!--首页 底部改版-->
<div th:fragment="new_footer">
    <style>
        .footer_copy a{ text-decoration: none; float: none; border-right: 0px solid #b3b3b3 !important; color: #111111 !important;}
        .footer_copy p{ color: #111111 !important;}
        .newbg{ background-color: #fff !important; border: none !important;}
        .newsh{ box-shadow: 0px 0px 158px 0px rgba(221, 221, 221, 0.49);}
    </style>
    <div class="cl"></div>
    <div class="footer newbg newsh" style="background:#f5f5f5;border-top:1px solid #eeeeee;">

        <div class="footer_160309 newbg">
            <div class="footer_top_160309 footer_width">
                <ul  class="footer_ul_2">
                    <li class="footer_t">沈阳新房</li>
                    <li><a href="/houses/" target="_blank">沈阳新楼盘</a></li>
                    <li><a href="/brandCompany/" target="_blank">品牌展示区</a></li>
                    <li><a href="/subways/k1" target="_blank">沈阳地铁房</a></li>
                    <li><a href="/houses/iv1" target="_blank">视频看房</a></li>
                    <li style="width: 60px"><a href="/houses/iv2" target="_blank">VR看房</a></li>
                    <li><a href="/static/houseMap.htm" target="_blank">地图找房</a></li>
                    <!--<li><a href="/schools/" target="_blank">沈阳学区房</a></li>
                    <li><a href="/exits/" target="_blank">沈阳现房</a></li>-->
                </ul>
                <ul>
                    <li class="footer_t">二手房 · 租房</li>
                    <li><a href="/saleHouses/" target="_blank">出售房源</a></li>
                    <li><a href="/static/saleHouse/saleHouse.htm" target="_blank">我要卖房</a></li>
                    <li><a href="/rents/" target="_blank">出租房源</a></li>
                    <li><a href="/rentwhole" target="_blank">我要出租</a></li>
                    <li><a href="/saleVillages/" target="_blank">小区找房</a></li>
                    <li><a href="/salemap/" target="_blank">地图找房</a></li>
                </ul>
                <ul style="width: 148px;">
                    <li class="footer_t">商铺写字楼</li>
                    <li><a href="/shops" target="_blank"><span>商铺房源</span></a></li>
                    <li><a href="/scriptoriums" target="_blank"><span>写字楼房源</span></a></li>
                    <li><a href="/houses/pt3" target="_blank"><span>商铺新盘</span></a></li>
                    <li><a href="/helpSearch?ids=9" target="_blank"><span>企业选址</span></a></li>
                    <li><a href="/helpSearch?ids=4" target="_blank"><span>商铺选址</span></a></li>
                </ul>
                <ul>
                    <li class="footer_t">热门资讯</li>
                    <li><a href="/news/149"><span>行业关注</span></a></li>
                    <li><a href="/news/151"><span>楼盘要闻</span></a></li>
                    <li><a href="/news/166"><span>学区资讯</span></a></li>
<!--                    <li><a href="/news/156"><span>专题推荐</span></a></li>-->
                    <li><a href="/videos/"><span>房产视频</span></a></li>
                </ul>
                <ul class="footer_ul_1">
                    <li class="footer_t">小二服务</li>
                    <li><a href="/static/business.htm" target="_blank">房贷计算器</a></li>
                    <li><a href="/fastSeek" target="_blank">房产快搜</a></li>
<!--                    <li><a href="/housingprice" target="_blank">房产评估</a></li>-->
                </ul>
                <!--<div class="footer_160309_right">-->
                <!--&lt;!&ndash;投诉邮箱：<EMAIL><br>&ndash;&gt;-->
                <!--&lt;!&ndash;企业QQ ：2328843579<br>&ndash;&gt;-->
                <!--服务质量 ：024-88098157<br>-->
                <!--早8:00 - 晚18:00<br>-->
                <!--企业合作 ：************<br>-->
                <!--&lt;!&ndash;工作时间：08:00-21:00（工作日）&ndash;&gt;-->
                <!--</div>-->
            </div>
        </div>


        <div class="footer_ewm footer_width" style="padding: 45px 0;background:none;height: 88px !important;margin: 0 auto;border: none;width: 1170px;">
            <div class="footer_ewm_img">
                <img src="https://static.fangxiaoer.com/web/images/download/appErweima101.jpg" alt="扫描下载APP"/>
                <p>扫描下载APP</p>
                手机浏览器访问网址<br><a target="_blank" style="text-decoration: none" th:href="${mobileUrl eq null ? 'https://m.fangxiaoer.com':mobileUrl}">m.fangxiaoer.com</a>
            </div>
            <div class="footer_ewm_img">
                <img src="https://static.fangxiaoer.com/web/images/ico/head/ewm_3.jpg" alt="微信订阅号"/>
                <p>关注微信订阅号</p>
                了解最新楼盘信息
            </div>
            <div class="footer_ewm_img">
                <img src="https://static.fangxiaoer.com/web/images/ico/head/fxeWxprogram.png" alt="微信小程序"/>
                <p>关注微信小程序</p>
                了解最新楼盘信息
            </div>
            <img src="https://static.fangxiaoer.com/web/images/ico/head/footer_160309.png" alt="让买房更简单" class="footer_biaoshi"/>
        </div>
        <div class="footer_copy footer_width" style="padding:0; text-align: center;">
            <a >佳恒传媒</a>
            <a href="https://info.fangxiaoer.com/Index" target="_blank">关于房小二</a>
            <a href="https://info.fangxiaoer.com/About/contactus" target="_blank">联系我们</a>
            <a href="https://sy.fangxiaoer.com/static/sysitemap.htm" target="_blank">网站地图</a>
            <a href="https://download.fangxiaoer.com" target="_blank">手机找房</a>
            <a href="https://info.fangxiaoer.com/About/copyright" target="_blank">版权声明</a>
            <a href="https://info.fangxiaoer.com/About/disclaimer" target="_blank">免责声明</a>
            <a href="https://info.fangxiaoer.com/About/policy" target="_blank">隐私政策</a>
            <a href="https://info.fangxiaoer.com/About/protocol" target="_blank">服务协议</a>
            <a href="https://sy.fangxiaoer.com/static/pdf_viewer.htm?file=https://images1.fangxiaoer.com/oss_images/material/2022/07/11/20211125142916423.pdf" target="_blank" class="bor_n">营业执照</a>

            <!--<a href="https://info.fangxiaoer.com/About/team" target="_blank">团队介绍</a>
            <a href="https://info.fangxiaoer.com/About/development" target="_blank">发展历程</a>
            <a href="https://info.fangxiaoer.com/About/reports" target="_blank">媒体报道</a>
            <a href="https://info.fangxiaoer.com/Help" target="_blank">使用帮助</a>-->

            <div class="cl"></div>
            <p style="text-align:center;color:#999">版权所有 Copyright © <th:block th:text="'2008-' + ${#temporals.format(#temporals.createNow(), 'yyyy')}"></th:block> www.fangxiaoer.com All Rights Reserved　
            </p>
            <p style="text-align:center;color:#999">
                <a class="bor_n" style="color:#999;text-decoration: none;float: none;" href="https://beian.miit.gov.cn/" target="_blank">ICP备案：辽B2-20150134-1</a>
                <a class="bor_n" style="color:#999;text-decoration: none;float: none;" href="https://images1.fangxiaoer.com/oss_images/material/2023/03/27/20230327152207073.pdf" target="_blank">广播电视节目制作证 许可证编号：（辽）字第01190号</a>
            </p>

        </div>
    </div>
    <script>
        //select划出时隐藏选择
        $("select").hover(function(){},function(){
            $(this).attr("disabled","disabled")
            $(this).removeAttr("disabled")
        })
    </script>
</div>

<!--新房下方热门楼盘和热房推荐的两行链接-->
<div th:fragment="footer_seo">
    <div class="container title_seo">
        <b>沈阳热门楼盘</b>
        <p>
            <a href="/houses/r10" target="_blank">浑南区楼盘</a>
            <a href="/houses/r11" target="_blank">沈北新区楼盘</a>
            <a href="/houses/r3" target="_blank">皇姑区楼盘</a>
            <a href="/houses/r1" target="_blank">沈河区楼盘</a>
            <a href="/houses/r4" target="_blank">和平区楼盘</a>
            <a href="/houses/r5" target="_blank">铁西区楼盘</a>
            <a href="/houses/r2" target="_blank">大东区楼盘</a>
            <a href="/houses/r9" target="_blank">于洪区楼盘</a>
            <a href="/houses/r12" target="_blank">苏家屯楼盘</a>
        </p>
        <div class="cl"></div>
        <b>沈阳新房推荐</b>
        <p>
            <a href="/houses/pt1" target="_blank">沈阳普宅</a>
            <a href="/houses/pt2" target="_blank">沈阳洋房</a>
            <a href="/houses/pt3" target="_blank">沈阳商铺</a>
            <a href="/houses/pt4" target="_blank">沈阳公寓</a>
            <a href="/houses/pt5" target="_blank">沈阳别墅</a>
            <a href="/exits/" target="_blank">沈阳现房</a>
            <a href="/static/houseMap.htm" target="_blank">沈阳地图找房</a>
        </p>
        <div class="cl"></div>
    </div>
    <div class="cl"></div>
    <script>
        //select划出时隐藏选择
        $("select").hover(function(){},function(){
            $(this).attr("disabled","disabled")
            $(this).removeAttr("disabled")
        })
    </script>
</div>
<!--首页底部-->
<div class="quanwei" th:fragment="footerAuthority">
    <div class="gong" style="text-align: center;">
        <!--<span class="t1">
            <a href="http://www.fangxiaoer.cn/" target="_blank">
                <img src="https://static.fangxiaoer.com/web/images/sy/index/logo_1.png" alt="佳恒传媒">
            </a>
            <a href="http://www.shenchengloushi.com/" target="_blank">
                <img src="https://static.fangxiaoer.com/web/images/sy/index/logo_2.gif" alt="沈城楼市报">
            </a>
            <a href="http://dcyjy.fangxiaoer.com/" target="_blank">
                <img src="https://static.fangxiaoer.com/web/images/sy/index/logo_3.gif" alt="沈阳地产研究院">
            </a>
        </span>-->
        <span class="t2">
            <a href="http://beian.miit.gov.cn/" target="_blank" rel="nofollow">
                <img src="https://static.fangxiaoer.com/web/images/sy/default/baxx.png" alt="经营性网站备案信息">
            </a>
            <a href="https://www.12377.cn/" target="_blank" rel="nofollow">
                <img src="https://static.fangxiaoer.com/web/images/sy/default/jbzx.png" alt="不良信息举报中心">
            </a>
            <a href="https://cyberpolice.mps.gov.cn/#/" target="_blank" rel="nofollow">
                <img src="https://static.fangxiaoer.com/web/images/sy/default/bjfw.png" alt="网络110报警服务">
            </a>
            <a style="border: none;float: initial; margin-left: 5px; padding-top: 7px; display: inline-block;" target="_blank" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=21010402000235" rel="nofollow">
                    <img src="https://static.fangxiaoer.com/global/imgs/ico/record.png" style="margin-right: 0px;"/>
                    <span style="font-size:12px; color:#939393;">
                    辽公网安备 21010402000235号</span >
                </a >
        </span>
    </div>
    <script>
        //select划出时隐藏选择
        $("select").hover(function(){},function(){
            $(this).attr("disabled","disabled")
            $(this).removeAttr("disabled")
        })
    </script>
</div>
<!--详情页面底部-->
<div th:fragment="footer_detail">
    <style>
        .footer_copy a{ text-decoration: none; float: none; border-right: 0px solid #b3b3b3 !important;}
    </style>
    <div class="cl"></div>
    <div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee">
        <div class="footer_copy footer_width" style="padding:20px 0; text-align: center;">
            <a >佳恒传媒</a>
            <a href="https://info.fangxiaoer.com/Index" target="_blank">关于房小二</a>
            <a href="https://info.fangxiaoer.com/About/contactus" target="_blank">联系我们</a>
            <a href="/static/sysitemap.htm" target="_blank">网站地图</a>
            <a href="https://download.fangxiaoer.com" target="_blank">手机找房</a>
            <a href="https://info.fangxiaoer.com/About/copyright" target="_blank">版权声明</a>
            <a href="https://info.fangxiaoer.com/About/disclaimer" target="_blank">免责声明</a>
            <a href="https://info.fangxiaoer.com/About/policy" target="_blank">隐私政策</a>
            <a href="https://info.fangxiaoer.com/About/protocol" target="_blank">服务协议</a>
            <div class="cl"></div>
            <p style="text-align:center;color:#999">版权所有 Copyright © <th:block th:text="'2008-' + ${#temporals.format(#temporals.createNow(), 'yyyy')}"></th:block> www.fangxiaoer.com All Rights Reserved　<a class="bor_n" style="color:#999;text-decoration: none;float: none;" href="https://beian.miit.gov.cn/" target="_blank">ICP备案：辽B2-20150134-1</a>
            </p>
        </div>
    </div>
    <script>
        //select划出时隐藏选择
        $("select").hover(function(){},function(){
            $(this).attr("disabled","disabled")
            $(this).removeAttr("disabled")
        })
    </script>
</div>
<!--统计代码-->
<div th:fragment="tongji">
    <!--统计代码-->
    <script>  //百度收录
    (function () {
        var bp = document.createElement('script');
        var curProtocol = window.location.protocol.split(':')[0];
        if (curProtocol === 'https') {
            bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
        }
        else {
            bp.src = 'http://push.zhanzhang.baidu.com/push.js';
        }
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(bp, s);
    })();
    </script>
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?198ca7ea94df8291001d3c6ac845c77f";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?f22dd9d959aacdbf455a6fad31336273";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</div>
<!--专题活动（专题页图片链接区）-->
<div th:fragment="activity_page">
    <div class="loushi">
        <div class="title"> <h1>专题活动</h1></div>
        <div class="lou_img" style="margin-left:0px">
            <a href="https://event.fangxiaoer.com/20151231.htm" target="_blank"><img src='https://static.fangxiaoer.com/web/images/sy/picture/lpzt_27.jpg' alt="学区房"></a>
            <div class="lou_pos lou_bg"></div>
            <a  href="https://event.fangxiaoer.com/20151231.htm"  target="_blank" class="lou_pos lou_title">沈阳最新学区划分解读</a>
        </div>
        <div class="lou_img">
            <a href="https://event.fangxiaoer.com/20120301.htm" target="_blank"><img src='https://static.fangxiaoer.com/web/images/sy/picture/lpzt_22.jpg' alt="地铁沿线"></a>
            <div class="lou_pos lou_bg"></div>
            <a  href="https://event.fangxiaoer.com/20120301.htm"  target="_blank" class="lou_pos lou_title">距地铁多远才算地铁房</a>
        </div>

        <div class="lou_img">
            <a href="https://event.fangxiaoer.com/20171230.htm" target="_blank"><img src='https://static.fangxiaoer.com/web/images/sy/picture/lpzt_26.jpg' alt="经广房交会传递楼市新声音"></a>
            <div class="lou_pos lou_bg"></div>
            <a  href="https://event.fangxiaoer.com/20171230.htm"  target="_blank" class="lou_pos lou_title">经广房交会传递楼市新声音</a>
        </div>

        <div class="lou_img">
            <a href="https://event.fangxiaoer.com/20150929.htm" target="_blank"><img src='https://static.fangxiaoer.com/web/images/sy/picture/lpzt_25.jpg' alt="品牌"></a>
            <div class="lou_pos lou_bg"></div>
            <a  href="https://event.fangxiaoer.com/20150929.htm"  target="_blank" class="lou_pos lou_title">买房为什么一定要选品牌</a>
        </div>

        <div class="lou_img">
            <a  href="https://event.fangxiaoer.com/20150321.htm" target="_blank"><img src='https://static.fangxiaoer.com/web/images/sy/picture/lpzt_21.jpg' alt="百科"></a>
            <div class="lou_pos lou_bg"></div>
            <a  href="https://event.fangxiaoer.com/20150321.htm"  target="_blank" class="lou_pos lou_title">教你怎样买房最省钱</a>
        </div>

    </div>

</div>
<!--新房右侧帮你找房-->
<div th:fragment="right_order">
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
        <div class="zsfw">
            <h1><span></span>帮您找房</h1>
            <ul>
                <li>
                    <span>意向区域</span>
                    <div>
                        <select id="region" >
                            <option>沈河区</option>
                            <option>大东区</option>
                            <option>皇姑区</option>
                            <option>和平区</option>
                            <option>铁西区</option>
                            <option>于洪区</option>
                            <option>浑南区</option>
                            <option>沈北新区</option>
                            <option>苏家屯</option>
                        </select>
                    </div>
                </li>
                <li class="new_huxing">
                    <span>意向户型</span>
                    <div>
                        <select id="new_huxing" >
                            <option>一居</option>
                            <option>二居</option>
                            <option>三居</option>
                            <option>四居</option>
                            <option>五居及以上</option>
                        </select>
                    </div>
                </li>
                <li class="new_yusuan">
                    <span>预算价格</span>
                    <div>
                        <select id="new_yusuan">
                            <option>35万以下</option>
                            <option>35-50万</option>
                            <option>50-80万</option>
                            <option>80-100万</option>
                            <option>100-120万</option>
                            <option>120-150万</option>
                            <option>150万以上</option>
                        </select>
                    </div>
                </li>
                <li>
                    <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
                </li>
                <li>
                    <span>手机号码</span>
                    <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
                    <input type="hidden" id="type" value="1">
                </li>
                <li>
                    <span>验证码</span>
                    <input type="tel" id="code" class="fxe_messageCode"  maxlength="6" style="width: 120px;" placeholder="请输入验证码"/>
                    <p id="hqyzm" class="fxe_ReSendValidateCoad" >获取验证码</p>
                    <p class="fxe_validateCode"></p>
                </li>
                <div class="checkagreeInput" style="margin: 0 auto 10px auto;">
                    <div style="font-size:12px;">提交即代表同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                        <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                </div>
                <b class="btn" id="new_submit">提交</b>
            </ul>
        </div>
    <script>
        $(".cheimg5").click(function () {
            if ($(this).hasClass("checked")) {
                $(this).removeClass("checked")
            } else {
                $(this).addClass("checked")
            }
        })
    </script>
</div>
<!--新房右侧广告与推荐-->
<div th:fragment="right_other">
    <dl class="recommend">
        <dt><span></span>房产快讯<a href="/news" target="_blank">更多>></a></dt>

        <div class="title">房产快讯</div>
        <div class="hot">
            <ul class="unstyled">
                <li th:each="f,i:${newFlash}">
                    <a th:href="${'/news/'+f.id+'.htm'}" target="_blank" th:text="${f.titleShow}"></a>
                </li>
            </ul>
        </div>

    </dl>

<!--    <a class="my_buyh" href="/new_secondPublish" target="_blank">我要卖房</a>-->

    <!--<div class="good" th:if="${decorationType!=1 and !#lists.isEmpty(recommendAdProjects)}">
        <h1><span></span>好房推荐</h1>
        <div class="content">
            <ul>
                <li th:each="projectAd:${recommendAdProjects}">
                    <a th:href="${projectAd.TargetUrl}" target="_blank">
                    <div class="pic">
                        <div class="photo">
                            <img th:src="${projectAd.AdFilePath}">
                        </div>
                        <div class="name">
                            <h4 th:text="${#strings.isEmpty(projectAd.AdTitle)?'':projectAd.AdTitle}">首创光和城</h4>
                        </div>
                    </div>
                    <div class="text">
                        <div class="top_sun">
                            <div class="left_sun">
                                <th:block th:if="${#strings.toString(projectAd.Adproprice) eq '0'}">
                                    <h4><span>待定</span></h4>
                                </th:block>
                                <th:block th:unless="${#strings.toString(projectAd.Adproprice) eq '0'}">
                                    <h4><th:block th:text="${#strings.toString(projectAd.Number) eq '2'?'均价：':'起价：'}"></th:block><span th:text="${#strings.isEmpty(projectAd.Adproprice)?'':projectAd.Adproprice}">8600</span>元/㎡</h4>
                                </th:block>
                            </div>
                            <div class="right_sun">
                                <h4 th:text="${#strings.isEmpty(projectAd.Propertys)?'':projectAd.Propertys}">于洪区</h4>
                            </div>
                            <div class="cl"></div>
                        </div>
                        <div class="bottom_sun">
                            <h4 th:text="${#strings.isEmpty(projectAd.AdPhrase)?'':projectAd.AdPhrase}">以租还贷 生态宜居</h4>
                        </div>
                    </div>
                    </a>
                </li>
            </ul>
        </div>
    </div>-->


</div>
<!--首页通栏6上面的友情链接-->
<div class="link index_block" th:fragment="link">
    <h3>友情链接：</h3>
    <p th:if="${!#lists.isEmpty(indexLinks)}">
        <a target="_blank" th:each="link:${indexLinks}" th:href="${link.url}" th:text="${link.title}">沈阳房产网</a>
</div>
<!--租房右侧免费发布-->
<div class="zsfw" th:fragment="help_search_rent">
    <h1><span></span>求租</h1>
    <ul>
        <li>
            <span>意向区域</span>
            <div>
                <select id="region" >
                    <option  th:each="region,stat:${region}" th:selected="${stat.index eq 1}" th:if="${region.name ne '全部'}" th:text="${region.name}" th:value="${region.name}">沈河区</option>
                </select>
            </div>
        </li>
        <li class="hx">
            <span>租住方式</span>
            <div>
                <select id="new_huxing" >
                    <option>整租</option>
                    <option>合租</option>
                </select>
            </div>
        </li>
        <li class="yx">
            <span>租金</span>
            <div>
                <select id="new_yusuan">
                    <option>600元以下</option>
                    <option>600-1000元</option>
                    <option>1000-1500元</option>
                    <option>1500-2000元</option>
                    <option>2000-2500元</option>
                    <option>2500-3000元</option>
                    <option>3000元以上</option>
                </select>
            </div>
        </li>
        <li>
            <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
        </li>
        <li>
            <span>手机号码</span>
            <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
            <input type="hidden" id="type" value="3">
        </li>
        <li>
            <span>验证码</span>
            <input type="tel" id="code" class="fxe_messageCode"  maxlength="6" style="width: 120px;" placeholder="请输入验证码"/>
            <p id="hqyzm" class="fxe_ReSendValidateCoad" >获取验证码</p>
            <p class="fxe_validateCode"></p>
        </li>
        <b class="btn" id="new_submit">提交</b>
        <li style="color: #999;width:237px;text-align:left;font-size: 12px;">若正确提交即同意接受以电话、短信等方式推荐我司业务相关的信息</li>

    </ul>
    <div class="form_phone">客服咨询电话：************</div>
</div>
<!--免费发布底-->
<div class="issuedInfo" th:fragment="publish_footer1">
    <ul class="w">
        <li>
            <img src="https://static.fangxiaoer.com/global/imgs/sale/release/issued1.png" alt="" />
            <div>
                <b>填写信息</b>
                <p>填写房源真实信息</p>
            </div>
        </li>
        <li>
            <img src="https://static.fangxiaoer.com/global/imgs/sale/release/issued2.png" alt="" />
            <div>
                <b>房源审核</b>
                <p>客服人员第一时间与您联系，核实信息</p>
            </div>
        </li>
        <li style="margin-right: 0;">
            <img src="https://static.fangxiaoer.com/global/imgs/sale/release/issued3.png" alt="" />
            <div>
                <b>房源展示</b>
                <p>生成专属房源页面，直面千万购房者</p>
            </div>
        </li>
    </ul>
    <script>
        //select划出时隐藏选择
        $("select").hover(function(){},function(){
            $(this).attr("disabled","disabled")
            $(this).removeAttr("disabled")
        })
    </script>
</div>
<!--免费发布底-->
<div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:fragment="publish_footer2">
    <style>
        .footer_copy a{ text-decoration: none; float: none; border-right: 0px solid #b3b3b3 !important;}
    </style>
    <div class="cl"></div>
    <div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee">
        <div class="footer_copy footer_width" style="padding:20px 0; text-align: center;">
            <a >佳恒传媒</a>
            <a href="https://info.fangxiaoer.com/Index" target="_blank">关于房小二</a>
            <a href="https://info.fangxiaoer.com/About/contactus" target="_blank">联系我们</a>
            <a href="/static/sysitemap.htm" target="_blank">网站地图</a>
            <a href="https://download.fangxiaoer.com" target="_blank">手机找房</a>
            <a href="https://info.fangxiaoer.com/About/copyright" target="_blank">版权声明</a>
            <a href="https://info.fangxiaoer.com/About/disclaimer" target="_blank">免责声明</a>
            <a href="https://info.fangxiaoer.com/About/policy" target="_blank">隐私政策</a>
            <a href="https://info.fangxiaoer.com/About/protocol" target="_blank">服务协议</a>
            <div class="cl"></div>
            <p style="text-align:center;color:#999">版权所有 Copyright © <th:block th:text="'2008-' + ${#temporals.format(#temporals.createNow(), 'yyyy')}"></th:block> www.fangxiaoer.com All Rights Reserved　<a class="bor_n" style="color:#999;text-decoration: none;float: none;" href="https://beian.miit.gov.cn/" target="_blank">ICP备案：辽B2-20150134-1</a>
            </p>
        </div>
    </div>

    <script>
        //select划出时隐藏选择
        $("select").hover(function(){},function(){
            $(this).attr("disabled","disabled")
            $(this).removeAttr("disabled")
        })
    </script>

</div>



<!--右侧浮标 新版本-->
<div  th:fragment="commonFloat">
    <script src="/js/house/detail/layoutCompare.js" type="text/javascript"></script>
    <div class="suspensionIcon">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>
        <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>-->
        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">
            <i></i>
            <span>
        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima101.jpg" />
        			<p>房小二网  APP<br>手机看房更方便</p>
        			<em></em>
				</span>
        </a>
        <!--        <a class="n2" th:href="@{/new_secondPublish/}" target="_blank" rel="100">-->
        <!--            <i></i>-->
        <!--            <span>在线卖房</span>-->
        <!--        </a>-->
        <!--<a class="n3" th:href="@{/helpSearch?ids=1}" target="_blank" rel="100">
            <i></i>
            <span>帮您找房</span>
        </a>-->



        <!--<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">-->
        <!--<i></i>-->
        <!--<span>客服</span>-->
        <!--</a>-->


        <a class="n20"  target="_blank" rel="100" th:if="${houseInfo?.projectStatus ne '3'}">
            <i></i>
            <span>户型对比</span>
            <div class="type-compare-main" id="close_C">
                <h1>户型对比</h1>
                <div id="closeCompare">
                    <img src="https://static.fangxiaoer.com/web/images/ico/close_img.png">
                </div>
                <dl>
                    <dt>您浏览过的户型（最多勾选4项）</dt>
                    <dd>
                        <ul class="tyleUl" id="showLayout">
                        </ul>
                        <p class="no-compare">暂没有户型可对比哦~</p>
                        <div class="typeBegin">开始对比</div>
                        <div class="typeClear" onclick="typeClear()">全部清空</div>
                    </dd>
                </dl>
                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">
            </div>
            <script>
                document.getElementById("closeCompare").onclick = function(){
                    var close = document.getElementById("close_C").style;
                    close.display="none";
                }
                //户型对比打开收起
                $(".n20").click(function () {
                    if($(".type-compare-main").css("display")=='none'){
                        $(".type-compare-main").show();
                        $("#close_w").hide()
                    }else{
                        $(".type-compare-main").hide()
                    }

                });



                //阻止冒泡
                $(".type-compare-main").click(function (ev) {
                    var oEvent = ev || event;
                    oEvent.cancelBubble = true;
                    oEvent.stopPropagation();
                })
            </script>
        </a>

        <a class="n6">
            <i></i>
            <span>************</span>
        </a>
        <a class="n7" href="#head2017"  target="_self">
            <i></i>
            <span>顶部</span>
        </a>
    </div>
    <script type="text/javascript">
        $(".suspensionIcon a").hover(function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐显示
                $(this).find("span").stop().fadeIn(200)
            } else {
                $(this).find("span").stop().show(300)
            }
        }, function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐消失
                $(this).find("span").stop().fadeOut(200)
            } else {
                $(this).find("span").stop().hide(300)
            }
        })


        //楼盘对比打开收起
        $(".suspensionIcon .n9").click(function () {
            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                $(".suspensionIcon .n9 .comparison").show();
                $(".type-compare-main").hide()
            }else{
                $(".suspensionIcon .n9 .comparison").hide()
            }

        });
        //阻止冒泡
        $(".comparison").click(function (ev) {
            var oEvent = ev || event;
            oEvent.cancelBubble = true;
            oEvent.stopPropagation();
        })
        /*  //楼盘对比打开收起
          $(".suspensionIcon .n9").click(function () {
              if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                  $(".suspensionIcon .n9 .comparison").show();
              }else{
                  $(".suspensionIcon .n9 .comparison").hide()
              }

          });

          //开始对比
          $(".comparisonBegin").click(function(){
              var txt="ProjectId="
              $(".comparisonOpen.hover").each(function(){
                  txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','
              })
              window.open("https://sy.fangxiaoer.com/?"+txt)
          })
          //勾选
          $(".comparisonOpen").click(function(){
              if($(this).hasClass("hover")){
                  $(this).removeClass("hover");
              }else if($(".comparisonOpen.hover").length>=4){
                  alert("最多勾选4项")
              }else{
                  $(this).addClass("hover");
              }
          })*/


        $(".suspensionIcon .n7").click(function () {
            $(document).scrollTop("0")
        })
    </script>
    <!--<script type='text/javascript' th:if="${commonType eq null}">
        (function(m, ei, q, i, a, j, s) {
            m[i] = m[i] || function() {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
            j = ei.createElement(q),
                    s = ei.getElementsByTagName(q)[0];
            j.async = true;
            j.charset = 'UTF-8';
            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
            s.parentNode.insertBefore(j, s);
        })(window, document, 'script', '_MEIQIA');
        _MEIQIA('entId', 33615);
        _MEIQIA('assign', {
            agentToken: 'ea33ee0dd925ff4989a620824573c909'
        });
        _MEIQIA('getPanelVisibility', yourFunction)
        function yourFunction(visibility) {
            if (visibility !== 'visible')

            {
                window.focus();
            }

        }
        // 设置 fallback
        _MEIQIA('fallback', 1);
    </script>-->
    <!--引入仿美洽按钮-->
    <div th:if="${commonType eq null}" th:include="fragment/fragment::talkBtn('/im/new/service/1')"></div>
</div>


<!--右侧漂浮 老版本-->
<!-- <div  th:fragment="commonFloat" >-->
<!--<script src="/js/house/detail/layoutCompare.js" type="text/javascript"></script>-->
<!--    <div class="suspensionIcon">-->
<!--        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>-->
<!--        &lt;!&ndash;<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>&ndash;&gt;-->
<!--        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">-->
<!--            <i></i>-->
<!--            <span>-->
<!--        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima101.jpg" />-->
<!--        			<p>房小二网  APP<br>手机看房更方便</p>-->
<!--        			<em></em>-->
<!--				</span>-->
<!--        </a>-->
<!--        <a class="n2" th:href="@{/new_secondPublish/}" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>在线卖房</span>-->
<!--        </a>-->
<!--        <a class="n3" th:href="@{/helpSearch?ids=1}" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>帮您找房</span>-->
<!--        </a>-->
<!--        <a th:if="${contrastHouse eq 'newhouse'}" class="n9" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>楼盘对比</span>-->


<!--            <div class="comparison" id="close_w">-->
<!--                <h1>楼盘对比</h1>-->
<!--                <div id="closeContrast">-->
<!--                    <img src="https://static.fangxiaoer.com/web/images/ico/close_img.png">-->
<!--                </div>-->
<!--                <dl>-->
<!--                    <dt>您浏览过的楼盘（最多勾选4项）</dt>-->
<!--                    <dd>-->
<!--                        <ul>-->
<!--                            &lt;!&ndash; <li>-->
<!--                                <div class="comparisonOpen hover"></div>-->
<!--                                <div class="comparisonSubName" rel="4">中海寰宇天下</div>-->
<!--                                <div class="comparisonClose"></div>-->
<!--                            </li> &ndash;&gt;-->
<!--                        </ul>-->
<!--                        <input class="serachHouseInfo" type="text" placeholder="请输入楼盘名称">-->
<!--                        <div class="comparisonBegin">开始对比</div>-->
<!--                        <div class="comparisonClear">全部清空</div>-->
<!--                    </dd>-->
<!--                </dl>-->
<!--                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">-->
<!--            </div>-->
<!--            <script>-->
<!--//                20190216新增对比窗口关闭按钮-->
<!--                document.getElementById("closeContrast").onclick = function(){-->
<!--                    var close = document.getElementById("close_w").style;-->
<!--                    close.display="none";-->
<!--                }-->
<!--            </script>-->
<!--        </a>-->

<!--        &lt;!&ndash;新房详情页户型对比&ndash;&gt;-->
<!--        <a class="n20" target="_blank" rel="100" >-->
<!--            <i></i>-->
<!--            <span>户型对比</span>-->
<!--            <div class="type-compare-main" id="close_C">-->
<!--                <h1>户型对比</h1>-->
<!--                <div id="closeCompare">-->
<!--                    <img src="https://static.fangxiaoer.com/web/images/ico/close_img.png">-->
<!--                </div>-->
<!--                <dl>-->
<!--                    <dt>您浏览过的户型（最多勾选4项）</dt>-->
<!--                    <dd>-->
<!--                        <ul class="tyleUl" id="showLayout">-->
<!--                            &lt;!&ndash; <li>-->
<!--                                <div class="comparisonOpen hover"></div>-->
<!--                                <div class="comparisonSubName" rel="4">中海寰宇天下</div>-->
<!--                                <div class="comparisonClose"></div>-->
<!--                            </li> &ndash;&gt;-->
<!--                        </ul>-->
<!--                        <p class="no-compare">暂没有户型可对比哦~</p>-->
<!--                        <div class="typeBegin">开始对比</div>-->
<!--                        <div class="typeClear" onclick="typeClear()">全部清空</div>-->
<!--                    </dd>-->
<!--                </dl>-->
<!--                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">-->
<!--            </div>-->
<!--            <script>-->
<!--                document.getElementById("closeCompare").onclick = function(){-->
<!--                    var close = document.getElementById("close_C").style;-->
<!--                    close.display="none";-->
<!--                }-->
<!--                //户型对比打开收起-->
<!--                $(".n20").click(function () {-->
<!--                    if($(".type-compare-main").css("display")=='none'){-->
<!--                        $(".type-compare-main").show();-->
<!--                        $("#close_w").hide()-->
<!--                    }else{-->
<!--                        $(".type-compare-main").hide()-->
<!--                    }-->

<!--                });-->



<!--                //阻止冒泡-->
<!--                $(".type-compare-main").click(function (ev) {-->
<!--                    var oEvent = ev || event;-->
<!--                    oEvent.cancelBubble = true;-->
<!--                    oEvent.stopPropagation();-->
<!--                })-->
<!--            </script>-->
<!--        </a>-->

<!--        <a class="n4" href="/static/business.htm" target="_blank" rel="88">-->
<!--            <i></i>-->
<!--            <span>计算器</span>-->
<!--        </a>-->
<!--        &lt;!&ndash;<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">&ndash;&gt;-->
<!--            &lt;!&ndash;<i></i>&ndash;&gt;-->
<!--            &lt;!&ndash;<span>客服</span>&ndash;&gt;-->
<!--        &lt;!&ndash;</a>&ndash;&gt;-->
<!--        <a class="n6">-->
<!--            <i></i>-->
<!--            <span>************</span>-->
<!--        </a>-->
<!--        <a class="n7" href="#head2017"  target="_self">-->
<!--            <i></i>-->
<!--            <span>顶部</span>-->
<!--        </a>-->
<!--    </div>-->
<!--    <script type="text/javascript">-->
<!--        $(".suspensionIcon a").hover(function () {-->
<!--            if ($(this).hasClass("n1")) {-->
<!--                //二维码渐隐显示-->
<!--                $(this).find("span").stop().fadeIn(200)-->
<!--            } else {-->
<!--                $(this).find("span").stop().show(300)-->
<!--            }-->
<!--        }, function () {-->
<!--            if ($(this).hasClass("n1")) {-->
<!--                //二维码渐隐消失-->
<!--                $(this).find("span").stop().fadeOut(200)-->
<!--            } else {-->
<!--                $(this).find("span").stop().hide(300)-->
<!--            }-->
<!--        })-->


<!--        //楼盘对比打开收起-->
<!--        $(".suspensionIcon .n9").click(function () {-->
<!--            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){-->
<!--                $(".suspensionIcon .n9 .comparison").show();-->
<!--                $(".type-compare-main").hide()-->
<!--            }else{-->
<!--                $(".suspensionIcon .n9 .comparison").hide()-->
<!--            }-->

<!--        });-->
<!--        //阻止冒泡-->
<!--        $(".comparison").click(function (ev) {-->
<!--            var oEvent = ev || event;-->
<!--            oEvent.cancelBubble = true;-->
<!--            oEvent.stopPropagation();-->
<!--        })-->
<!--        /*  //楼盘对比打开收起-->
<!--          $(".suspensionIcon .n9").click(function () {-->
<!--              if($(".suspensionIcon .n9 .comparison").css("display")=='none'){-->
<!--                  $(".suspensionIcon .n9 .comparison").show();-->
<!--              }else{-->
<!--                  $(".suspensionIcon .n9 .comparison").hide()-->
<!--              }-->

<!--          });-->

<!--          //开始对比-->
<!--          $(".comparisonBegin").click(function(){-->
<!--              var txt="ProjectId="-->
<!--              $(".comparisonOpen.hover").each(function(){-->
<!--                  txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','-->
<!--              })-->
<!--              window.open("https://sy.fangxiaoer.com/?"+txt)-->
<!--          })-->
<!--          //勾选-->
<!--          $(".comparisonOpen").click(function(){-->
<!--              if($(this).hasClass("hover")){-->
<!--                  $(this).removeClass("hover");-->
<!--              }else if($(".comparisonOpen.hover").length>=4){-->
<!--                  alert("最多勾选4项")-->
<!--              }else{-->
<!--                  $(this).addClass("hover");-->
<!--              }-->
<!--          })*/-->


<!--        $(".suspensionIcon .n7").click(function () {-->
<!--            $(document).scrollTop("0")-->
<!--        })-->
<!--    </script>-->
<!--    &lt;!&ndash;<script type='text/javascript' th:if="${commonType eq null}">-->
<!--        (function(m, ei, q, i, a, j, s) {-->
<!--            m[i] = m[i] || function() {-->
<!--                        (m[i].a = m[i].a || []).push(arguments)-->
<!--                    };-->
<!--            j = ei.createElement(q),-->
<!--                    s = ei.getElementsByTagName(q)[0];-->
<!--            j.async = true;-->
<!--            j.charset = 'UTF-8';-->
<!--            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';-->
<!--            s.parentNode.insertBefore(j, s);-->
<!--        })(window, document, 'script', '_MEIQIA');-->
<!--        _MEIQIA('entId', 33615);-->
<!--        _MEIQIA('assign', {-->
<!--            agentToken: 'ea33ee0dd925ff4989a620824573c909'-->
<!--        });-->
<!--        _MEIQIA('getPanelVisibility', yourFunction)-->
<!--        function yourFunction(visibility) {-->
<!--            if (visibility !== 'visible')-->

<!--            {-->
<!--                window.focus();-->
<!--            }-->

<!--        }-->
<!--        // 设置 fallback-->
<!--        _MEIQIA('fallback', 1);-->
<!--    </script>&ndash;&gt;-->
<!--    &lt;!&ndash;引入仿美洽按钮&ndash;&gt;-->
<!--    <div th:if="${commonType eq null}" th:include="fragment/fragment::talkBtn('/im/new/service/1')"></div>-->
<!--</div>-->




<!--新改版首页右侧漂浮-->
<div  th:fragment="newIndexFloat" >
    <script src="/js/house/detail/layoutCompare.js" type="text/javascript"></script>
    <div class="suspensionIcon">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>
        <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>-->
        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">
            <i></i>
            <span>
        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima101.jpg" />
        			<p>房小二网  APP<br>手机看房更方便</p>
        			<em></em>
				</span>
        </a>
<!--        <a class="n2" th:href="@{/static/saleHouse/saleHouse.htm}" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>在线卖房</span>-->
<!--        </a>-->
<!--        <a class="n3" th:href="@{/helpSearch?ids=1}" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>帮您找房</span>-->
<!--        </a>-->
        <!--<a  class="n9" target="_blank" rel="100">
            <i></i>
            <span>楼盘对比</span>


            <div class="comparison" id="close_w">
                <h1>楼盘对比</h1>
                <div id="closeContrast">
                    <img src="https://static.fangxiaoer.com/web/images/ico/close_img.png">
                </div>
                <dl>
                    <dt>您浏览过的楼盘（最多勾选4项）</dt>
                    <dd>
                        <ul>
                            &lt;!&ndash; <li>
                                <div class="comparisonOpen hover"></div>
                                <div class="comparisonSubName" rel="4">中海寰宇天下</div>
                                <div class="comparisonClose"></div>
                            </li> &ndash;&gt;
                        </ul>
                        <input class="serachHouseInfo" type="text" placeholder="请输入楼盘名称">
                        <div class="comparisonBegin">开始对比</div>
                        <div class="comparisonClear">全部清空</div>
                    </dd>
                </dl>
                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">
            </div>
            <script>
                //                20190216新增对比窗口关闭按钮
                document.getElementById("closeContrast").onclick = function(){
                    var close = document.getElementById("close_w").style;
                    close.display="none";
                }
            </script>
        </a>-->

        <!--新房详情页户型对比-->
        <!--<a class="n20" target="_blank" rel="100" >
            <i></i>
            <span>户型对比</span>
            <div class="type-compare-main" id="close_C">
                <h1>户型对比</h1>
                <div id="closeCompare">
                    <img src="https://static.fangxiaoer.com/web/images/ico/close_img.png">
                </div>
                <dl>
                    <dt>您浏览过的户型（最多勾选4项）</dt>
                    <dd>
                        <ul class="tyleUl" id="showLayout">
                            &lt;!&ndash; <li>
                                <div class="comparisonOpen hover"></div>
                                <div class="comparisonSubName" rel="4">中海寰宇天下</div>
                                <div class="comparisonClose"></div>
                            </li> &ndash;&gt;
                        </ul>
                        <p class="no-compare">暂没有户型可对比哦~</p>
                        <div class="typeBegin">开始对比</div>
                        <div class="typeClear" onclick="typeClear()">全部清空</div>
                    </dd>
                </dl>
                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">
            </div>
            <script>
                document.getElementById("closeCompare").onclick = function(){
                    var close = document.getElementById("close_C").style;
                    close.display="none";
                }
                //户型对比打开收起
                $(".n20").click(function () {
                    if($(".type-compare-main").css("display")=='none'){
                        $(".type-compare-main").show();
                        $("#close_w").hide()
                    }else{
                        $(".type-compare-main").hide()
                    }

                });



                //阻止冒泡
                $(".type-compare-main").click(function (ev) {
                    var oEvent = ev || event;
                    oEvent.cancelBubble = true;
                    oEvent.stopPropagation();
                })
            </script>
        </a>-->

        <!--<a class="n4" href="/static/business.htm" target="_blank" rel="88">
            <i></i>
            <span>计算器</span>
        </a>-->
        <!--<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">-->
        <!--<i></i>-->
        <!--<span>客服</span>-->
        <!--</a>-->
        <a class="n6">
            <i></i>
            <span>************</span>
        </a>
        <a class="n7" href="#head2017"  target="_self">
            <i></i>
            <span>顶部</span>
        </a>
    </div>
    <script type="text/javascript">
        $(".suspensionIcon a").hover(function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐显示
                $(this).find("span").stop().fadeIn(200)
            } else {
                $(this).find("span").stop().show(300)
            }
        }, function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐消失
                $(this).find("span").stop().fadeOut(200)
            } else {
                $(this).find("span").stop().hide(300)
            }
        })


        //楼盘对比打开收起
        $(".suspensionIcon .n9").click(function () {
            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                $(".suspensionIcon .n9 .comparison").show();
                $(".type-compare-main").hide()
            }else{
                $(".suspensionIcon .n9 .comparison").hide()
            }

        });
        //阻止冒泡
        $(".comparison").click(function (ev) {
            var oEvent = ev || event;
            oEvent.cancelBubble = true;
            oEvent.stopPropagation();
        })
        /*  //楼盘对比打开收起
          $(".suspensionIcon .n9").click(function () {
              if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                  $(".suspensionIcon .n9 .comparison").show();
              }else{
                  $(".suspensionIcon .n9 .comparison").hide()
              }

          });

          //开始对比
          $(".comparisonBegin").click(function(){
              var txt="ProjectId="
              $(".comparisonOpen.hover").each(function(){
                  txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','
              })
              window.open("https://sy.fangxiaoer.com/?"+txt)
          })
          //勾选
          $(".comparisonOpen").click(function(){
              if($(this).hasClass("hover")){
                  $(this).removeClass("hover");
              }else if($(".comparisonOpen.hover").length>=4){
                  alert("最多勾选4项")
              }else{
                  $(this).addClass("hover");
              }
          })*/


        $(".suspensionIcon .n7").click(function () {
            $(document).scrollTop("0")
        })
    </script>
    <!--<script type='text/javascript' th:if="${commonType eq null}">
        (function(m, ei, q, i, a, j, s) {
            m[i] = m[i] || function() {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
            j = ei.createElement(q),
                    s = ei.getElementsByTagName(q)[0];
            j.async = true;
            j.charset = 'UTF-8';
            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
            s.parentNode.insertBefore(j, s);
        })(window, document, 'script', '_MEIQIA');
        _MEIQIA('entId', 33615);
        _MEIQIA('assign', {
            agentToken: 'ea33ee0dd925ff4989a620824573c909'
        });
        _MEIQIA('getPanelVisibility', yourFunction)
        function yourFunction(visibility) {
            if (visibility !== 'visible')

            {
                window.focus();
            }

        }
        // 设置 fallback
        _MEIQIA('fallback', 1);
    </script>-->
    <!--引入仿美洽按钮-->
    <div th:if="${commonType eq null}" th:include="fragment/fragment::talkBtn('/im/new/service/1')"></div>
</div>











<!--新改版新房右侧漂浮-->
 <div  th:fragment="newHouseFloat" >
    <script src="/js/house/detail/layoutCompare.js" type="text/javascript"></script>
    <div class="suspensionIcon">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>
        <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>-->
        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">
            <i></i>
            <span>
        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima101.jpg" />
        			<p>房小二网  APP<br>手机看房更方便</p>
        			<em></em>
				</span>
        </a>
        <!--        <a class="n2" th:href="@{/new_secondPublish/}" target="_blank" rel="100">-->
        <!--            <i></i>-->
        <!--            <span>在线卖房</span>-->
        <!--        </a>-->
<!--        <a class="n3" href="/helpSearch?ids=1" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>帮您找房</span>-->
<!--        </a>-->

<!--        <a  class="n9" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>楼盘对比</span>-->


<!--            <div class="comparison" id="close_w">-->
<!--                <h1>楼盘对比</h1>-->
<!--                <div id="closeContrast">-->
<!--                    <img src="https://static.fangxiaoer.com/web/images/ico/close_img.png">-->
<!--                </div>-->
<!--                <dl>-->
<!--                    <dt>您浏览过的楼盘（最多勾选4项）</dt>-->
<!--                    <dd>-->
<!--                        <ul>-->
<!--                            &lt;!&ndash; <li>-->
<!--                                <div class="comparisonOpen hover"></div>-->
<!--                                <div class="comparisonSubName" rel="4">中海寰宇天下</div>-->
<!--                                <div class="comparisonClose"></div>-->
<!--                            </li> &ndash;&gt;-->
<!--                        </ul>-->
<!--                        <input class="serachHouseInfo" type="text" placeholder="请输入楼盘名称">-->
<!--                        <div class="comparisonBegin">开始对比</div>-->
<!--                        <div class="comparisonClear">全部清空</div>-->
<!--                    </dd>-->
<!--                </dl>-->
<!--                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">-->
<!--            </div>-->
<!--            <script>-->
<!--                //                20190216新增对比窗口关闭按钮-->
<!--                document.getElementById("closeContrast").onclick = function(){-->
<!--                    var close = document.getElementById("close_w").style;-->
<!--                    close.display="none";-->
<!--                }-->
<!--            </script>-->
<!--        </a>-->

        <!--新房详情页户型对比-->
<!--        <a class="n20" target="_blank" rel="100" >-->
<!--            <i></i>-->
<!--            <span>户型对比</span>-->
<!--            <div class="type-compare-main" id="close_C">-->
<!--                <h1>户型对比</h1>-->
<!--                <div id="closeCompare">-->
<!--                    <img src="https://static.fangxiaoer.com/web/images/ico/close_img.png">-->
<!--                </div>-->
<!--                <dl>-->
<!--                    <dt>您浏览过的户型（最多勾选4项）</dt>-->
<!--                    <dd>-->
<!--                        <ul class="tyleUl" id="showLayout">-->
<!--                        </ul>-->
<!--                        <p class="no-compare">暂没有户型可对比哦~</p>-->
<!--                        <div class="typeBegin">开始对比</div>-->
<!--                        <div class="typeClear" onclick="typeClear()">全部清空</div>-->
<!--                    </dd>-->
<!--                </dl>-->
<!--                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">-->
<!--            </div>-->
<!--            <script>-->
<!--                document.getElementById("closeCompare").onclick = function(){-->
<!--                    var close = document.getElementById("close_C").style;-->
<!--                    close.display="none";-->
<!--                }-->
<!--                //户型对比打开收起-->
<!--                $(".n20").click(function () {-->
<!--                    if($(".type-compare-main").css("display")=='none'){-->
<!--                        $(".type-compare-main").show();-->
<!--                        $("#close_w").hide()-->
<!--                    }else{-->
<!--                        $(".type-compare-main").hide()-->
<!--                    }-->

<!--                });-->



<!--                //阻止冒泡-->
<!--                $(".type-compare-main").click(function (ev) {-->
<!--                    var oEvent = ev || event;-->
<!--                    oEvent.cancelBubble = true;-->
<!--                    oEvent.stopPropagation();-->
<!--                })-->
<!--            </script>-->
<!--        </a>-->

<!--        <a class="n4" href="/static/business.htm" target="_blank" rel="88">-->
<!--            <i></i>-->
<!--            <span>计算器</span>-->
<!--        </a>-->
        <!--<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">-->
        <!--<i></i>-->
        <!--<span>客服</span>-->
        <!--</a>-->
        <a class="n6">
            <i></i>
            <span>************</span>
        </a>
        <a class="n7" href="#head2017"  target="_self">
            <i></i>
            <span>顶部</span>
        </a>
    </div>
    <script type="text/javascript">
        $(".suspensionIcon a").hover(function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐显示
                $(this).find("span").stop().fadeIn(200)
            } else {
                $(this).find("span").stop().show(300)
            }
        }, function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐消失
                $(this).find("span").stop().fadeOut(200)
            } else {
                $(this).find("span").stop().hide(300)
            }
        })


        //楼盘对比打开收起
        $(".suspensionIcon .n9").click(function () {
            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                $(".suspensionIcon .n9 .comparison").show();
                $(".type-compare-main").hide()
            }else{
                $(".suspensionIcon .n9 .comparison").hide()
            }

        });
        //阻止冒泡
        $(".comparison").click(function (ev) {
            var oEvent = ev || event;
            oEvent.cancelBubble = true;
            oEvent.stopPropagation();
        })
        /*  //楼盘对比打开收起
          $(".suspensionIcon .n9").click(function () {
              if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                  $(".suspensionIcon .n9 .comparison").show();
              }else{
                  $(".suspensionIcon .n9 .comparison").hide()
              }

          });

          //开始对比
          $(".comparisonBegin").click(function(){
              var txt="ProjectId="
              $(".comparisonOpen.hover").each(function(){
                  txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','
              })
              window.open("https://sy.fangxiaoer.com/?"+txt)
          })
          //勾选
          $(".comparisonOpen").click(function(){
              if($(this).hasClass("hover")){
                  $(this).removeClass("hover");
              }else if($(".comparisonOpen.hover").length>=4){
                  alert("最多勾选4项")
              }else{
                  $(this).addClass("hover");
              }
          })*/


        $(".suspensionIcon .n7").click(function () {
            $(document).scrollTop("0")
        })
    </script>
    <!--<script type='text/javascript' th:if="${commonType eq null}">
        (function(m, ei, q, i, a, j, s) {
            m[i] = m[i] || function() {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
            j = ei.createElement(q),
                    s = ei.getElementsByTagName(q)[0];
            j.async = true;
            j.charset = 'UTF-8';
            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
            s.parentNode.insertBefore(j, s);
        })(window, document, 'script', '_MEIQIA');
        _MEIQIA('entId', 33615);
        _MEIQIA('assign', {
            agentToken: 'ea33ee0dd925ff4989a620824573c909'
        });
        _MEIQIA('getPanelVisibility', yourFunction)
        function yourFunction(visibility) {
            if (visibility !== 'visible')

            {
                window.focus();
            }

        }
        // 设置 fallback
        _MEIQIA('fallback', 1);
    </script>-->
    <!--引入仿美洽按钮-->
    <div th:if="${commonType eq null}" th:include="fragment/fragment::talkBtn('/im/new/service/1')"></div>
</div>





<!--新改版二手房、租房、商铺-->
  <div  th:fragment="newRightFloat" >
    <script src="/js/house/detail/layoutCompare.js" type="text/javascript"></script>
    <div class="suspensionIcon">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>
        <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>-->
        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">
            <i></i>
            <span>
        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima101.jpg" />
        			<p>房小二网  APP<br>手机看房更方便</p>
        			<em></em>
				</span>
        </a>
        <!--        <a class="n2" th:href="@{/new_secondPublish/}" target="_blank" rel="100">-->
        <!--            <i></i>-->
        <!--            <span>在线卖房</span>-->
        <!--        </a>-->


        <!--新房详情页户型对比-->

        <!--<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">-->
        <!--<i></i>-->
        <!--<span>客服</span>-->
        <!--</a>-->
        <a class="n6">
            <i></i>
            <span>************</span>
        </a>
        <a class="n7" href="#head2017"  target="_self">
            <i></i>
            <span>顶部</span>
        </a>
    </div>
    <script type="text/javascript">
        $(".suspensionIcon a").hover(function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐显示
                $(this).find("span").stop().fadeIn(200)
            } else {
                $(this).find("span").stop().show(300)
            }
        }, function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐消失
                $(this).find("span").stop().fadeOut(200)
            } else {
                $(this).find("span").stop().hide(300)
            }
        })


        //楼盘对比打开收起
        $(".suspensionIcon .n9").click(function () {
            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                $(".suspensionIcon .n9 .comparison").show();
                $(".type-compare-main").hide()
            }else{
                $(".suspensionIcon .n9 .comparison").hide()
            }

        });
        //阻止冒泡
        $(".comparison").click(function (ev) {
            var oEvent = ev || event;
            oEvent.cancelBubble = true;
            oEvent.stopPropagation();
        })
        /*  //楼盘对比打开收起
          $(".suspensionIcon .n9").click(function () {
              if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                  $(".suspensionIcon .n9 .comparison").show();
              }else{
                  $(".suspensionIcon .n9 .comparison").hide()
              }

          });

          //开始对比
          $(".comparisonBegin").click(function(){
              var txt="ProjectId="
              $(".comparisonOpen.hover").each(function(){
                  txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','
              })
              window.open("https://sy.fangxiaoer.com/?"+txt)
          })
          //勾选
          $(".comparisonOpen").click(function(){
              if($(this).hasClass("hover")){
                  $(this).removeClass("hover");
              }else if($(".comparisonOpen.hover").length>=4){
                  alert("最多勾选4项")
              }else{
                  $(this).addClass("hover");
              }
          })*/


        $(".suspensionIcon .n7").click(function () {
            $(document).scrollTop("0")
        })
    </script>
    <!--<script type='text/javascript' th:if="${commonType eq null}">
        (function(m, ei, q, i, a, j, s) {
            m[i] = m[i] || function() {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
            j = ei.createElement(q),
                    s = ei.getElementsByTagName(q)[0];
            j.async = true;
            j.charset = 'UTF-8';
            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
            s.parentNode.insertBefore(j, s);
        })(window, document, 'script', '_MEIQIA');
        _MEIQIA('entId', 33615);
        _MEIQIA('assign', {
            agentToken: 'ea33ee0dd925ff4989a620824573c909'
        });
        _MEIQIA('getPanelVisibility', yourFunction)
        function yourFunction(visibility) {
            if (visibility !== 'visible')

            {
                window.focus();
            }

        }
        // 设置 fallback
        _MEIQIA('fallback', 1);
    </script>-->
    <!--引入仿美洽按钮-->
    <div th:if="${commonType eq null}" th:include="fragment/fragment::talkBtn('/im/new/service/1')"></div>
</div>


<!--二手房相关列表页面右侧浮标专用-->
<div  th:fragment="esfCommonFloat" >
    <script src="/js/house/detail/layoutCompare.js" type="text/javascript"></script>
    <div class="suspensionIcon">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>
        <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>-->
        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">
            <i></i>
            <span>
        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima101.jpg" />
        			<p>房小二网  APP<br>手机看房更方便</p>
        			<em></em>
				</span>
        </a>
        <!--        <a class="n2" th:href="@{/new_secondPublish/}" target="_blank" rel="100">-->
        <!--            <i></i>-->
        <!--            <span>在线卖房</span>-->
        <!--        </a>-->


        <!--新房详情页户型对比-->

        <!--<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">-->
        <!--<i></i>-->
        <!--<span>客服</span>-->
        <!--</a>-->
        <a class="n6">
            <i></i>
            <span>************</span>
        </a>
        <a class="n7" href="#head2017"  target="_self">
            <i></i>
            <span>顶部</span>
        </a>
    </div>
    <script type="text/javascript">
        $(".suspensionIcon a").hover(function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐显示
                $(this).find("span").stop().fadeIn(200)
            } else {
                $(this).find("span").stop().show(300)
            }
        }, function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐消失
                $(this).find("span").stop().fadeOut(200)
            } else {
                $(this).find("span").stop().hide(300)
            }
        })


        //楼盘对比打开收起
        $(".suspensionIcon .n9").click(function () {
            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                $(".suspensionIcon .n9 .comparison").show();
                $(".type-compare-main").hide()
            }else{
                $(".suspensionIcon .n9 .comparison").hide()
            }

        });
        //阻止冒泡
        $(".comparison").click(function (ev) {
            var oEvent = ev || event;
            oEvent.cancelBubble = true;
            oEvent.stopPropagation();
        })
        /*  //楼盘对比打开收起
          $(".suspensionIcon .n9").click(function () {
              if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                  $(".suspensionIcon .n9 .comparison").show();
              }else{
                  $(".suspensionIcon .n9 .comparison").hide()
              }

          });

          //开始对比
          $(".comparisonBegin").click(function(){
              var txt="ProjectId="
              $(".comparisonOpen.hover").each(function(){
                  txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','
              })
              window.open("https://sy.fangxiaoer.com/?"+txt)
          })
          //勾选
          $(".comparisonOpen").click(function(){
              if($(this).hasClass("hover")){
                  $(this).removeClass("hover");
              }else if($(".comparisonOpen.hover").length>=4){
                  alert("最多勾选4项")
              }else{
                  $(this).addClass("hover");
              }
          })*/


        $(".suspensionIcon .n7").click(function () {
            $(document).scrollTop("0")
        })
    </script>
    <!--<script type='text/javascript' th:if="${commonType eq null}">
        (function(m, ei, q, i, a, j, s) {
            m[i] = m[i] || function() {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
            j = ei.createElement(q),
                    s = ei.getElementsByTagName(q)[0];
            j.async = true;
            j.charset = 'UTF-8';
            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
            s.parentNode.insertBefore(j, s);
        })(window, document, 'script', '_MEIQIA');
        _MEIQIA('entId', 33615);
        _MEIQIA('assign', {
            agentToken: 'ea33ee0dd925ff4989a620824573c909'
        });
        _MEIQIA('getPanelVisibility', yourFunction)
        function yourFunction(visibility) {
            if (visibility !== 'visible')

            {
                window.focus();
            }

        }
        // 设置 fallback
        _MEIQIA('fallback', 1);
    </script>-->
    <!--引入仿美洽按钮-->
    <div th:if="${commonType eq null}" th:include="fragment/fragment::talkBtn('/im/new/service/1')"></div>
</div>


<!--二手房相关列表页面右侧浮标专用  老版本-->
<!--   <div  th:fragment="esfCommonFloat" >-->
<!--    <div class="suspensionIcon">-->
<!--        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>-->
<!--        &lt;!&ndash;<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>&ndash;&gt;-->
<!--        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">-->
<!--            <i></i>-->
<!--            <span>-->
<!--        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima.jpg" />-->
<!--        			<p>房小二网  APP<br>手机看房更方便</p>-->
<!--        			<em></em>-->
<!--				</span>-->
<!--        </a>-->
<!--        <a class="n2" th:href="@{/new_secondPublish/}" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>在线卖房</span>-->
<!--        </a>-->
<!--        <a class="n3" th:href="@{/helpSearch?ids=1}" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>帮您找房</span>-->
<!--        </a>-->
<!--        <a th:if="${contrastHouse eq 'newhouse'}" class="n9" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>楼盘对比</span>-->
<!--            <div class="comparison">-->
<!--                <h1>楼盘对比</h1>-->
<!--                <dl>-->
<!--                    <dt>您浏览过的楼盘（最多勾选4项）</dt>-->
<!--                    <dd>-->
<!--                        <ul>-->
<!--                            &lt;!&ndash; <li>-->
<!--                                <div class="comparisonOpen hover"></div>-->
<!--                                <div class="comparisonSubName" rel="4">中海寰宇天下</div>-->
<!--                                <div class="comparisonClose"></div>-->
<!--                            </li> &ndash;&gt;-->
<!--                        </ul>-->
<!--                        <input class="serachHouseInfo" type="text" placeholder="请输入楼盘名称">-->
<!--                        <div class="comparisonBegin">开始对比</div>-->
<!--                        <div class="comparisonClear">全部清空</div>-->
<!--                    </dd>-->
<!--                </dl>-->
<!--                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">-->
<!--            </div>-->
<!--        </a>-->
<!--        <a class="n4" href="/static/business.htm" target="_blank" rel="88">-->
<!--            <i></i>-->
<!--            <span>计算器</span>-->
<!--        </a>-->
<!--        &lt;!&ndash;<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">&ndash;&gt;-->
<!--            &lt;!&ndash;<i></i>&ndash;&gt;-->
<!--            &lt;!&ndash;<span>客服</span>&ndash;&gt;-->
<!--        &lt;!&ndash;</a>&ndash;&gt;-->
<!--        <a class="n6">-->
<!--            <i></i>-->
<!--            <span>************</span>-->
<!--        </a>-->
<!--        <a class="n7" href="#head2017"  target="_self">-->
<!--            <i></i>-->
<!--            <span>顶部</span>-->
<!--        </a>-->
<!--    </div>-->
<!--    <script type="text/javascript">-->
<!--        $(".suspensionIcon a").hover(function () {-->
<!--            if ($(this).hasClass("n1")) {-->
<!--                //二维码渐隐显示-->
<!--                $(this).find("span").stop().fadeIn(200)-->
<!--            } else {-->
<!--                $(this).find("span").stop().show(300)-->
<!--            }-->
<!--        }, function () {-->
<!--            if ($(this).hasClass("n1")) {-->
<!--                //二维码渐隐消失-->
<!--                $(this).find("span").stop().fadeOut(200)-->
<!--            } else {-->
<!--                $(this).find("span").stop().hide(300)-->
<!--            }-->
<!--        })-->


<!--        //楼盘对比打开收起-->
<!--        $(".suspensionIcon .n9").click(function () {-->
<!--            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){-->
<!--                $(".suspensionIcon .n9 .comparison").show();-->
<!--            }else{-->
<!--                $(".suspensionIcon .n9 .comparison").hide()-->
<!--            }-->

<!--        });-->
<!--        //阻止冒泡-->
<!--        $(".comparison").click(function (ev) {-->
<!--            var oEvent = ev || event;-->
<!--            oEvent.cancelBubble = true;-->
<!--            oEvent.stopPropagation();-->
<!--        })-->
<!--        /*  //楼盘对比打开收起-->
<!--         $(".suspensionIcon .n9").click(function () {-->
<!--         if($(".suspensionIcon .n9 .comparison").css("display")=='none'){-->
<!--         $(".suspensionIcon .n9 .comparison").show();-->
<!--         }else{-->
<!--         $(".suspensionIcon .n9 .comparison").hide()-->
<!--         }-->

<!--         });-->

<!--         //开始对比-->
<!--         $(".comparisonBegin").click(function(){-->
<!--         var txt="ProjectId="-->
<!--         $(".comparisonOpen.hover").each(function(){-->
<!--         txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','-->
<!--         })-->
<!--         window.open("https://sy.fangxiaoer.com/?"+txt)-->
<!--         })-->
<!--         //勾选-->
<!--         $(".comparisonOpen").click(function(){-->
<!--         if($(this).hasClass("hover")){-->
<!--         $(this).removeClass("hover");-->
<!--         }else if($(".comparisonOpen.hover").length>=4){-->
<!--         alert("最多勾选4项")-->
<!--         }else{-->
<!--         $(this).addClass("hover");-->
<!--         }-->
<!--         })*/-->


<!--        $(".suspensionIcon .n7").click(function () {-->
<!--            $(document).scrollTop("0")-->
<!--        })-->
<!--    </script>-->
<!--    &lt;!&ndash;-->
<!--    <script type='text/javascript'>-->
<!--        (function(m, ei, q, i, a, j, s) {-->
<!--            m[i] = m[i] || function() {-->
<!--                        (m[i].a = m[i].a || []).push(arguments)-->
<!--                    };-->
<!--            j = ei.createElement(q),-->
<!--                    s = ei.getElementsByTagName(q)[0];-->
<!--            j.async = true;-->
<!--            j.charset = 'UTF-8';-->
<!--            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';-->
<!--            s.parentNode.insertBefore(j, s);-->
<!--        })(window, document, 'script', '_MEIQIA');-->
<!--        _MEIQIA('entId', 33615);-->
<!--        _MEIQIA('assign', {-->
<!--            agentToken: '2bbf6ff2baec4ae1f0ba102484dbd959'-->
<!--        })-->
<!--        _MEIQIA('getPanelVisibility', yourFunction)-->
<!--        function yourFunction(visibility) {-->
<!--            if (visibility !== 'visible')-->

<!--            {-->
<!--                window.focus();-->
<!--            }-->

<!--        }-->
<!--        // 设置 fallback-->
<!--        _MEIQIA('fallback', 1);-->
<!--    </script>-->
<!--    &ndash;&gt;-->
<!--    &lt;!&ndash;引入仿美洽按钮&ndash;&gt;-->
<!--    &lt;!&ndash;<div th:include="fragment/fragment::talkBtn('/im/new/service/2')"></div>&ndash;&gt;-->
<!--</div>-->

<!--二手房相关右侧浮标（无底部美恰的） 新版本-->
<div  th:fragment="esfCommonFloatNoRight" >
    <script src="/js/house/detail/layoutCompare.js" type="text/javascript"></script>
    <div class="suspensionIcon">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>
        <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>-->
        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">
            <i></i>
            <span>
        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima101.jpg" />
        			<p>房小二网  APP<br>手机看房更方便</p>
        			<em></em>
				</span>
        </a>
        <!--        <a class="n2" th:href="@{/new_secondPublish/}" target="_blank" rel="100">-->
        <!--            <i></i>-->
        <!--            <span>在线卖房</span>-->
        <!--        </a>-->


        <!--新房详情页户型对比-->

        <!--<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">-->
        <!--<i></i>-->
        <!--<span>客服</span>-->
        <!--</a>-->
        <a class="n6">
            <i></i>
            <span>************</span>
        </a>
        <a class="n7" href="#head2017"  target="_self">
            <i></i>
            <span>顶部</span>
        </a>
    </div>
    <script type="text/javascript">
        $(".suspensionIcon a").hover(function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐显示
                $(this).find("span").stop().fadeIn(200)
            } else {
                $(this).find("span").stop().show(300)
            }
        }, function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐消失
                $(this).find("span").stop().fadeOut(200)
            } else {
                $(this).find("span").stop().hide(300)
            }
        })


        //楼盘对比打开收起
        $(".suspensionIcon .n9").click(function () {
            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                $(".suspensionIcon .n9 .comparison").show();
                $(".type-compare-main").hide()
            }else{
                $(".suspensionIcon .n9 .comparison").hide()
            }

        });
        //阻止冒泡
        $(".comparison").click(function (ev) {
            var oEvent = ev || event;
            oEvent.cancelBubble = true;
            oEvent.stopPropagation();
        })
        /*  //楼盘对比打开收起
          $(".suspensionIcon .n9").click(function () {
              if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                  $(".suspensionIcon .n9 .comparison").show();
              }else{
                  $(".suspensionIcon .n9 .comparison").hide()
              }

          });

          //开始对比
          $(".comparisonBegin").click(function(){
              var txt="ProjectId="
              $(".comparisonOpen.hover").each(function(){
                  txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','
              })
              window.open("https://sy.fangxiaoer.com/?"+txt)
          })
          //勾选
          $(".comparisonOpen").click(function(){
              if($(this).hasClass("hover")){
                  $(this).removeClass("hover");
              }else if($(".comparisonOpen.hover").length>=4){
                  alert("最多勾选4项")
              }else{
                  $(this).addClass("hover");
              }
          })*/


        $(".suspensionIcon .n7").click(function () {
            $(document).scrollTop("0")
        })
    </script>
    <!--<script type='text/javascript' th:if="${commonType eq null}">
        (function(m, ei, q, i, a, j, s) {
            m[i] = m[i] || function() {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
            j = ei.createElement(q),
                    s = ei.getElementsByTagName(q)[0];
            j.async = true;
            j.charset = 'UTF-8';
            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
            s.parentNode.insertBefore(j, s);
        })(window, document, 'script', '_MEIQIA');
        _MEIQIA('entId', 33615);
        _MEIQIA('assign', {
            agentToken: 'ea33ee0dd925ff4989a620824573c909'
        });
        _MEIQIA('getPanelVisibility', yourFunction)
        function yourFunction(visibility) {
            if (visibility !== 'visible')

            {
                window.focus();
            }

        }
        // 设置 fallback
        _MEIQIA('fallback', 1);
    </script>-->

</div>




<!-- 老版本 二手房相关右侧浮标（无底部美恰的）-->
<!--  <div  th:fragment="esfCommonFloatNoRight" >-->
<!--    <div class="suspensionIcon">-->
<!--        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspensionIcon.css"/>-->
<!--        &lt;!&ndash;<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>&ndash;&gt;-->
<!--        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">-->
<!--            <i></i>-->
<!--            <span>-->
<!--        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima.jpg" />-->
<!--        			<p>房小二网  APP<br>手机看房更方便</p>-->
<!--        			<em></em>-->
<!--				</span>-->
<!--        </a>-->
<!--        <a class="n2" th:href="@{/new_secondPublish/}" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>在线卖房</span>-->
<!--        </a>-->
<!--        <a class="n3" th:href="@{/helpSearch?ids=1}" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>帮您找房</span>-->
<!--        </a>-->
<!--        <a th:if="${contrastHouse eq 'newhouse'}" class="n9" target="_blank" rel="100">-->
<!--            <i></i>-->
<!--            <span>楼盘对比</span>-->
<!--            <div class="comparison">-->
<!--                <h1>楼盘对比</h1>-->
<!--                <dl>-->
<!--                    <dt>您浏览过的楼盘（最多勾选4项）</dt>-->
<!--                    <dd>-->
<!--                        <ul>-->
<!--                            &lt;!&ndash; <li>-->
<!--                                <div class="comparisonOpen hover"></div>-->
<!--                                <div class="comparisonSubName" rel="4">中海寰宇天下</div>-->
<!--                                <div class="comparisonClose"></div>-->
<!--                            </li> &ndash;&gt;-->
<!--                        </ul>-->
<!--                        <input class="serachHouseInfo" type="text" placeholder="请输入楼盘名称">-->
<!--                        <div class="comparisonBegin">开始对比</div>-->
<!--                        <div class="comparisonClear">全部清空</div>-->
<!--                    </dd>-->
<!--                </dl>-->
<!--                <img src="https://static.fangxiaoer.com/web/images/ico/comparison3.png" style="position: absolute;top: 50px;right: -9px;" alt="">-->
<!--            </div>-->
<!--        </a>-->
<!--        <a class="n4" href="/static/business.htm" target="_blank" rel="88">-->
<!--            <i></i>-->
<!--            <span>计算器</span>-->
<!--        </a>-->
<!--        &lt;!&ndash;<a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">&ndash;&gt;-->
<!--        &lt;!&ndash;<i></i>&ndash;&gt;-->
<!--        &lt;!&ndash;<span>客服</span>&ndash;&gt;-->
<!--        &lt;!&ndash;</a>&ndash;&gt;-->
<!--        <a class="n6">-->
<!--            <i></i>-->
<!--            <span>************</span>-->
<!--        </a>-->
<!--        <a class="n7" href="#head2017"  target="_self">-->
<!--            <i></i>-->
<!--            <span>顶部</span>-->
<!--        </a>-->
<!--    </div>-->
<!--    <script type="text/javascript">-->
<!--        $(".suspensionIcon a").hover(function () {-->
<!--            if ($(this).hasClass("n1")) {-->
<!--                //二维码渐隐显示-->
<!--                $(this).find("span").stop().fadeIn(200)-->
<!--            } else {-->
<!--                $(this).find("span").stop().show(300)-->
<!--            }-->
<!--        }, function () {-->
<!--            if ($(this).hasClass("n1")) {-->
<!--                //二维码渐隐消失-->
<!--                $(this).find("span").stop().fadeOut(200)-->
<!--            } else {-->
<!--                $(this).find("span").stop().hide(300)-->
<!--            }-->
<!--        })-->


<!--        //楼盘对比打开收起-->
<!--        $(".suspensionIcon .n9").click(function () {-->
<!--            if($(".suspensionIcon .n9 .comparison").css("display")=='none'){-->
<!--                $(".suspensionIcon .n9 .comparison").show();-->
<!--            }else{-->
<!--                $(".suspensionIcon .n9 .comparison").hide()-->
<!--            }-->

<!--        });-->
<!--        //阻止冒泡-->
<!--        $(".comparison").click(function (ev) {-->
<!--            var oEvent = ev || event;-->
<!--            oEvent.cancelBubble = true;-->
<!--            oEvent.stopPropagation();-->
<!--        })-->
<!--        /*  //楼盘对比打开收起-->
<!--         $(".suspensionIcon .n9").click(function () {-->
<!--         if($(".suspensionIcon .n9 .comparison").css("display")=='none'){-->
<!--         $(".suspensionIcon .n9 .comparison").show();-->
<!--         }else{-->
<!--         $(".suspensionIcon .n9 .comparison").hide()-->
<!--         }-->

<!--         });-->

<!--         //开始对比-->
<!--         $(".comparisonBegin").click(function(){-->
<!--         var txt="ProjectId="-->
<!--         $(".comparisonOpen.hover").each(function(){-->
<!--         txt+=$(this).parent().find(".comparisonSubName").attr("rel")+','-->
<!--         })-->
<!--         window.open("https://sy.fangxiaoer.com/?"+txt)-->
<!--         })-->
<!--         //勾选-->
<!--         $(".comparisonOpen").click(function(){-->
<!--         if($(this).hasClass("hover")){-->
<!--         $(this).removeClass("hover");-->
<!--         }else if($(".comparisonOpen.hover").length>=4){-->
<!--         alert("最多勾选4项")-->
<!--         }else{-->
<!--         $(this).addClass("hover");-->
<!--         }-->
<!--         })*/-->


<!--        $(".suspensionIcon .n7").click(function () {-->
<!--            $(document).scrollTop("0")-->
<!--        })-->
<!--    </script>-->

<!--</div>-->


<!--美洽-->
<div th:fragment="common_meiqia">
    <!--<script type='text/javascript'>
        (function(m, ei, q, i, a, j, s) {
            m[i] = m[i] || function() {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
            j = ei.createElement(q),
                    s = ei.getElementsByTagName(q)[0];
            j.async = true;
            j.charset = 'UTF-8';
            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
            s.parentNode.insertBefore(j, s);
        })(window, document, 'script', '_MEIQIA');
        _MEIQIA('entId', 33615);
        _MEIQIA('assign', {
            agentToken: 'ea33ee0dd925ff4989a620824573c909'
        });
        _MEIQIA('getPanelVisibility', yourFunction)
        function yourFunction(visibility) {
            if (visibility !== 'visible')

            {
                window.focus();
            }

        }
        // 设置 fallback
        _MEIQIA('fallback', 1);
    </script>-->
    <!--引入仿美洽按钮-->
    <div th:include="fragment/fragment::talkBtn('/im/new/service/1')"></div>
</div>

<!--二手房相关美洽-->
<div th:fragment="esfCommon_meiqia">
    <!--<script type='text/javascript'>
        (function(m, ei, q, i, a, j, s) {
            m[i] = m[i] || function() {
                        (m[i].a = m[i].a || []).push(arguments)
                    };
            j = ei.createElement(q),
                    s = ei.getElementsByTagName(q)[0];
            j.async = true;
            j.charset = 'UTF-8';
            j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
            s.parentNode.insertBefore(j, s);
        })(window, document, 'script', '_MEIQIA');
        _MEIQIA('entId', 33615);
        _MEIQIA('assign', {
            agentToken: '2bbf6ff2baec4ae1f0ba102484dbd959'
        })
        _MEIQIA('getPanelVisibility', yourFunction)
        function yourFunction(visibility) {
            if (visibility !== 'visible')

            {
                window.focus();
            }

        }
        // 设置 fallback
        _MEIQIA('fallback', 1);
    </script>-->
    <!--引入仿美洽按钮-->
    <!--<div th:include="fragment/fragment::talkBtn('/im/new/service/2')"></div>-->
</div>
<!--资讯，房仔活动的右侧房产快讯-->
<div class="hot" th:fragment="news_flash">
    <ul class="unstyled" >
        <ul class="unstyled" >
            <li th:each="f,i:${newFlash}">
                <a th:href="${'/news/'+f.id+'.htm'}" target="_blank" th:text="${f.titleShow}"></a>
            </li>
        </ul>
        </li>
    </ul>
    </ul>
</div>
<!--贷款表头-->
<div th:fragment="loanHead">
    <div class="w dk_nav">
        <div class="login">
            <!--<a href="/"><img src="https://static.fangxiaoer.com/web/images/ico/head/logo.png" alt="房小二网"></a>-->
        </div>
        <ul>
            <li th:class="${count==1 ? 'hover':''}"><a href="/loan/index">贷款</a></li>
            <li  th:class="${count==2 ? 'hover':''}"><a href="/loan/mortgage">按揭贷</a></li>
            <li  th:class="${count==3 ? 'hover':''}"><a href="/loan/credit">信用贷</a></li>
            <li  th:class="${count==4 ? 'hover':''}"><a href="/loan/pledge">抵押贷</a></li>
            <li th:class="${count==5 ? 'hover':''}" ><a href="/loan/card.html">信用卡</a></li>
        </ul>
    </div>
</div>
<!--分享-->
<div th:fragment="shareIcon">
    <link rel="stylesheet" type="text/css" href="/css/share_icon.css"/>
    <div class="share_icon">
        <div class="share_tan"><i><img src="/css/btn_share.png" alt=""></i>分享</div>
        <div class="share_tan_main" style="display: none">
            <a class="ewm">
                <i>发送到手机</i>
                <i>手机扫一扫</i>
                <br>
                <i>把好房源</i>
                <i>分享给朋友</i>
                <div id="qrcode"></div>
            </a>
        </div>
    </div>
    <script>
        $(function () {
//            $(".share_tan_main").hide();
            $(".share_tan").mouseover(function () {
                $(".share_tan_main").toggle()
            });
            $(".share_tan").mouseout(function () {
                $(".share_tan_main").toggle()
            });
            var replaceLocationUlr = window.location.href;
            var mhead = "fang2/";
            var type = "";
            if(replaceLocationUlr.indexOf("salehouse")!= -1){
                mhead = "fang2/";
            }else if(replaceLocationUlr.indexOf("rent")!= -1){
                mhead = "fang3/";
            }else if(replaceLocationUlr.indexOf("shop")!= -1){
                mhead = "fang4/";
            }else if(replaceLocationUlr.indexOf("video")!= -1){
                mhead = "video/";
            } else if (replaceLocationUlr.indexOf("scriptorium") != -1) {
                mhead = "fang5/";
            } else if (replaceLocationUlr.indexOf("dealSale") != -1) {
                mhead = "txnDetail/";
                type = "-1";
            }else if(replaceLocationUlr.indexOf("dealRent")!= -1){
                mhead = "txnDetail/";
                type = "-2";
            }else if(replaceLocationUlr.indexOf("dealShop")!= -1){
                mhead = "txnDetail/";
                type = "-3";
            }
            $('#qrcode').qrcode(
                {
                    width: 90,
                    height: 90,
                    text: "https://m.fangxiaoer.com/"+mhead+[[${houseId}]]+type+".htm"
                })
        });
    </script>
</div>
<!--首页漂浮一键订阅-->
<div th:fragment="bottomBanner">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css" />
    <div class="houseKeeper">
        <div class="keeperBanner" style="left: 0px;">
            <div class="kb-kuang">
                <div class="kb-ren"></div>
                <div class="kb-pp"></div>
                <div class="kb-main">
                    <p>你关注的房源有了新变化？</p>
                    <input type="" name="" id="houseKeeperPhone" value="" placeholder="输入手机号" maxlength="11" />
                    <div href="" class="yjdy_btn">一键订阅</div>
                </div>
                <div class="close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertXfff.png" alt=""></div>
            </div>
        </div>
        <div class="keeperPeople" style="left: 0px;"></div>
    </div>
    <div class="kpTc_heibu">
        <!--底部广告一键订阅弹窗-->
        <div class="kpTc">
            <div class="kpTc_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></div>
            <div>
                <div class="kpTc_icon"><img src="https://static.fangxiaoer.com/web/images/ico/sign/kpTc_03.png" /></div>
                <div class="kpTc_form">
                    <div><span>验证码已经发送到</span><input type="text" id="KeeperPhone" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" />，请注意查收</div>
                    <div>
                        <input type="text" id="houseKeeperCode" maxlength="6" onkeyup="this.value=this.value.replace(/\D/g,'')" placeholder="输入您的验证码" />
                        <b class="fxe_ReSendValidateCoadAgent ReSendCode">获取验证码</b><b class="fxe_validateCode"></b>
                    </div>
                    <div class=""></div>
                    <div class="kpTc_btn"><a>立即预约</a></div>
                    <div>
                        <i class="kbTc_txt1">免费咨询电话：************</i	>
                        <a class="kbTc_txt2" onclick="_MEIQIA('showPanel')">在线咨询</a>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div id="index_button_floactAct_error" style="display: none">
        <span>电话号输入有误</span>
    </div>
    <script src="/js/house/verify.js" type="text/javascript"></script>
    <script type="text/javascript">
        sy_confirm.init(2,true);
        $(".yjdy_btn").click(function(){
            var msgFlag = sy_confirm.phone($("#houseKeeperPhone").val());
            if(msgFlag==true){
                $("#KeeperPhone").val($("#houseKeeperPhone").val().replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'));
                $(".kpTc_heibu").show();
                $(".kpTc").show();
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#houseKeeperPhone").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                      sy_confirm.timeWait();
                      $(".ReSendCode").hide();   
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }else {
                fxe_alert_index(msgFlag);
            }
//            if(sy_confirm.phone($("#houseKeeperPhone").val())==true){
//            }
        })
        $(".ReSendCode").click(function(){
            var msgFlag = sy_confirm.phone($("#houseKeeperPhone").val());
            if(msgFlag==true){
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#houseKeeperPhone").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                     sy_confirm.timeWait();
                     $(".ReSendCode").hide();
                     setTimeout('$(".ReSendCode").show().html("重新获取")',60000)
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }else {
                alert(msgFlag);
            }
        })
    </script>
    <script type="text/javascript">
        //		一键订阅弹出框弹出隐藏
        $(".kpTc_heibu").hide();
        $(".kpTc").hide();
        $(".kpTc_close").click(function(){
            $(".kpTc_heibu").hide();
            $(".kpTc").hide();

        })

    </script>
    <script type="text/javascript">
        //首页底部广告点击收起展开动画效果
        var keeperBanner = $(".keeperBanner");
        var keeperPeople = $(".keeperPeople");
        var keeperPeople_width = keeperPeople.width();
        $().ready(function() {
            keeperPeople.animate({
                "left": -keeperPeople_width
            }, 1)
        })
        $(".keeperBanner").click(function() {
            $(".keeperPeople").animate({
                height: "300px"
            });
        })

        function app_show_hide() {
            keeperBanner.find(".close").on("click", function() {
                keeperBanner.animate({
                    "left": "-100%"
                }, 1000, function() {
                    keeperPeople.animate({
                        "left": "0"
                    }, 500)
                });
            });
            keeperPeople.on("click", function() {
                keeperPeople.animate({
                    "left": -keeperPeople_width
                }, 500, function() {
                    keeperBanner.animate({
                        "left": "0"
                    }, 1000)
                });
            });
        }
        app_show_hide();

        $(function() {
            var keeperBanner = $(".keeperBanner");
            keeperBanner.find(".close").on("click", function() {
                setCookie('app_close', '1', 24 * 3600);
            });
        })
    </script>

    <script type="text/javascript">
        //首页底部广告一键订阅订单提交
        /*$(".fxe_ReSendValidateCoad").click(function() {
            if (sy_confirm.phone($("#phone").val()) != true) {} else if (sy_confirm.Code($("#phone").val()) == true) {
                sy_confirm.timeWait();
            }
        })*/
        $(".kpTc_btn a").click(function() {
            var msgflag1 =  sy_confirm.phone($("#houseKeeperPhone").val());
            var msgflag2 =  sy_confirm.code($("#houseKeeperCode").val())
            if (msgflag1 != true) {
                fxe_alert_index(msgflag1);
            } else if (msgflag2 != true) {
                fxe_alert_index(msgflag2);
            } else {
                var params = {
                    type:4,
                    phone: $("#houseKeeperPhone").val(),
                    code: $("#houseKeeperCode").val(),
                    area:"一键订阅",
                    italy:'【Sy站】',
                }
                $.ajax({
                    type: "post",
                    url: "/saveHouseOrder",
                    data: JSON.stringify(params),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success: function(data) {
                        if (data.status == 1) {
//                            fxe_alert("提交成功！")
                            $(".kpTc_heibu").hide();
                            $(".kpTc").hide();
                            $(".yydk_heibu").show()
                            $(".yyDk").show()
                            $("#houseKeeperPhone").attr("value","");
                        } else if (data.status == 0) {
                            fxe_alert_index(data.msg);
                        } else {
                            fxe_alert_index("提交失败！");
                        }
                    },
                    error: function(data) {
                        console.log(data)
                    }
                });
            }
        })
    </script>
</div>



<!--经纪人店铺上方‘您的位置和经纪人名片’-->
<div th:fragment="agentShopCard">
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <div class="crumbs">您的位置：<a th:href="@{/}" target="_blank">沈阳房产网</a> &gt; <a href="" th:text="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺'}" >陈晓宇的店铺</a></div>
    <div class="about new-shopIndex">
        <div class="information new-cardInformation">
            <div style="display: flex;align-items: center">
                <h1 style="float: left;"><th:block th:text="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username}"></th:block></h1>
<!--                <s th:class="${'icon-xy level-icon'+agentInfo.level}" style="float: left;margin-top:6px;">-->
<!--                    <th:block th:text="${'LV'+agentInfo.stars}"></th:block>-->
<!--                    <b class="starExplain">经纪人综合评分，信用越高表示经纪人越可信</b>-->
<!--                </s>-->

                <img th:if="${agentData.intermediaryState eq '1'}" style="width: 24px;height: 24px;margin-left: 10px" src="https://static.fangxiaoer.com/web/images/sy/house/agent-vip.png" >
<!--                <a th:if="${!#strings.isEmpty(agentInfo.memberTop) and agentInfo.memberTop ne '0'}" href="/memberTopList">-->
<!--                    <img  style="width: 36px;height: 36px" id="jiangbei" src="https://static.fangxiaoer.com/web/images/sy/prominent/jiangbei.png">-->
<!--                </a>-->
            </div>
            <p th:if="${!#strings.isEmpty(agentData.agentIntroduce)}" th:text="${#strings.toString(agentData.agentIntroduce)}"></p>
            <p th:if="${#strings.isEmpty(agentData.agentIntroduce)}" th:text="${'以诚感人者 人亦诚而应 愿以我全力以赴 换来您乐享生活'}"></p>
            <div>
                <!--<p class="tel" th:text="${agentInfo.sortTel}">15754631427</p>-->
                <!--微信扫码拨号-->
                <div class="agentshop-cardTels">
                    <s></s>
                    <span>电话咨询</span>
                    <div class="show-topCardTels">
                        <img id="forcodex" src="" alt="">
                        <p>微信扫码拨号</p>
                        <p><i>快速获取经纪人联系方式</i></p>
                    </div>
                </div>
                <script>
                    /*<![CDATA[*/
                    $(function () {
                        var tels = [[${agentInfo.sortTel}]] + "";
                        var scene = 'tel,' +[[${agencyId}]]+'-1,6,' + tels.replace("转", "");
                        var img = "";
                        var sss;
                        $.ajax({
                            type: "GET",
                            async: false,
                            url:  "/getWxACode",
                            data:{"scene": scene},
                            dataType : 'json',
                            headers : {
                                'Content-Type' : 'application/json;charset=utf-8'
                            },
                            success: function (data) {
                                console.log(data)
                                img = data.img;
                                sss = data;
                            }
                        });
                        $("#forcodex").attr("src","data:text/html;base64,"+img);
                    });

                </script>
                <div class="agentSignIcon">
<!--                    <i th:if="${!#strings.isEmpty(agentInfo.agentCard)}" class="signId agentSign"><i>身份已验证</i></i>-->
<!--                    <i th:if="${#strings.toString(agentInfo.EmailState) eq '1'}" class="signEmail agentSign" ><i>邮箱已验证</i></i>-->
                </div>
            </div>

        </div>
        <img class="touxiang new-agenttou" th:src="${#strings.isEmpty(agentInfo.pic)? 'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':agentInfo.pic}" th:alt="${#strings.isEmpty(agentInfo.username)? '':agentInfo.username}">

        <!--<div th:if="${!#strings.isEmpty(agentInfo.QRCode)}" class="agent-ewm"><img th:src="${agentInfo.QRCode}" alt="">
            <h5>加Ta微信</h5>
        </div>-->
        <!--扫码查看店铺-->
        <div class="seeshop">
            <!--<img src="https://images1.fangxiaoer.com/oss_images/memberavatar/2020/02/08/202002080956581061.jpg" alt="">-->
            <!--<div>-->
            <!--<p>微信扫码查看<b>TA</b>的店铺</p>-->
            <!--<p>手机浏览更方便</p>-->
            <!--</div>-->
        </div>
    </div>
</div>


<!--店铺关闭-->
<div th:fragment="closeShopCard">
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <div class="crumbs">您的位置：<a th:href="@{/}" target="_blank">沈阳房产网</a> &gt;<a href="">经纪人</a>  &gt; <a href=""  >已关闭的店铺</a></div>
</div>


<!--中介店铺上方‘您的位置和经纪人名片’-->
<div th:fragment="intermediaryShopCard">

    <div class="crumbs">您的位置：<a th:href="@{/}" target="_blank">沈阳房产网</a>  &gt; <a href="" th:text="${#strings.isEmpty(intermediaryName)?'':intermediaryName+'的店铺'}" ></a></div>
    <div class="about new-shopIndex">

            <div style="display: flex;align-items: center;height: 100%">
                <div style="font-size: 46px;font-family: PingFang SC-Heavy, PingFang SC;font-weight: bold;line-height: normal;text-align: left; color: #303030;margin-left: 56px;">
                    <th:block th:text="${#strings.isEmpty(intermediaryName)?'':intermediaryName}"></th:block></div>

            </div>






    </div>
</div>



<!--经纪人店铺二手房租房等店铺入口-->
<div th:fragment="agentShopsHead">
    <div class="contentTop">
        <a th:if="${!#lists.isEmpty(agentInfo.newSaleList)}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/newsecond/'+agencyId}" th:class=" (${agentHouseTpye} == 0)? 'hover' : '' ">新房</a>
        <a th:if="${!#lists.isEmpty(agentInfo.saleHouseList)}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/second/'+agencyId}" th:class=" (${agentHouseTpye} == 1)? 'hover' : '' ">二手房</a>
        <a th:if="${!#lists.isEmpty(agentInfo.rentHouseList)}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/rents/'+agencyId}" th:class=" (${agentHouseTpye} == 2)? 'hover' : '' ">租房</a>
        <a th:if="${!#lists.isEmpty(agentInfo.shopList)}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/shops/'+agencyId}" th:class=" (${agentHouseTpye} == 3)? 'hover' : '' ">商铺</a>
        <a th:if="${!#lists.isEmpty(agentInfo.office)}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/office/'+agencyId}" th:class=" (${agentHouseTpye} == 4)? 'hover' : '' " >写字楼</a>



<!--        <a th:if="${#strings.toString(agentInfo.dealCount) ne '0' and !#strings.isEmpty(agentInfo.dealCount)}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/dealSecond/'+agencyId}" th:class=" (${agentHouseTpye} == 5)? 'hover' : '' ">成交</a>-->
<!--        <a th:href="${#strings.isEmpty(agencyId)? '': '/agent/info/'+agencyId}"  th:class=" (${agentHouseTpye} == 6)? 'hover' : '' ">个人信息</a>-->
<!--        <a th:if="${!#lists.isEmpty(agentInfo.agentNewsList)}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/news/'+agencyId}" th:class=" (${agentHouseTpye} == 8)? 'hover' : '' " >市场观点</a>-->
<!--        <a th:if="${!#lists.isEmpty(agentInfo.askSubdistric)}" th:href="${#strings.isEmpty(agentInfo.askSubdistric)? '': '/agent/ask/'+agencyId}"  th:class=" (${agentHouseTpye} == 7)? 'hover' : '' ">问答</a>-->
    </div>
</div>
<!--经纪人店铺二手房租房等店铺入口-->
<div th:fragment="agentIntermediaryHead">
    <div class="contentTop">
        <a th:if="${saleCount ne '0'}" th:href="${#strings.isEmpty(agentIntermediaryId)? '': '/agentIntermediary/second/'+agentIntermediaryId}" th:class=" (${agentHouseTpye} == 1)? 'hover' : '' ">二手房</a>
        <a th:if="${rentCount ne '0'}" th:href="${#strings.isEmpty(agentIntermediaryId)? '': '/agentIntermediary/rents/'+agentIntermediaryId}" th:class=" (${agentHouseTpye} == 2)? 'hover' : '' ">租房</a>
        <a th:if="${shopCount ne '0'}" th:href="${#strings.isEmpty(agentIntermediaryId)? '': '/agentIntermediary/shops/'+agentIntermediaryId}" th:class=" (${agentHouseTpye} == 3)? 'hover' : '' ">商铺</a>
        <a th:if="${officeCount ne '0'}" th:href="${#strings.isEmpty(agentIntermediaryId)? '': '/agentIntermediary/office/'+agentIntermediaryId}" th:class=" (${agentHouseTpye} == 4)? 'hover' : '' ">写字楼</a>

    </div>
</div>
<!--经纪人店铺成交二手房租房等店铺入口-->
<div th:fragment="agentShopsDetailHead">
    <div class="dealchoice" th:if="${(#strings.toString(dealSaleNums) ne '0' and #strings.toString(dealRentNums) ne '0') or (#strings.toString(dealSaleNums) ne '0' and #strings.toString(dealShopNums) ne '0') or (#strings.toString(dealRentNums) ne '0' and #strings.toString(dealShopNums) ne '0')}">
        <a th:if="${#strings.toString(dealSaleNums) ne '0'}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/dealSecond/'+agencyId}" th:class=" (${agentHouseDetailTpye} == 1)? 'hover' : '' ">二手房</a>
        <a th:if="${#strings.toString(dealRentNums) ne '0'}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/dealRents/'+agencyId}"  th:class=" (${agentHouseDetailTpye} == 2)? 'hover' : '' ">租房</a>
        <a th:if="${#strings.toString(dealShopNums) ne '0'}" th:href="${#strings.isEmpty(agencyId)? '': '/agent/dealShops/'+agencyId}"  th:class=" (${agentHouseDetailTpye} == 3)? 'hover' : '' ">商铺</a>
    </div>
</div>
<!--项目与房源详细页引导登录弹窗-->
<div  th:fragment="guideLoginPopup">
    <div th:if="${#session?.getAttribute('sessionId') == null}" id="popupBottom" style="display: none">

        <div class="popupBottomFonts">
            <div id="popupClose" class="close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertXfff.png" alt=""></div>
            <!--<p><span></span></p>-->
            <a id="popupLogin" class="popupLogin" target="_blank" data-toggle="modal" href="#login">去登录</a>
        </div>

    </div>
    <script th:inline="javascript">
        var sessionPopup = [[${#session?.getAttribute('sessionPopup')}]];
        if (sessionPopup != 1) {
            function hello(){
                $("#popupBottom").show();
            }
            window.setTimeout(hello,60000);
        }
        $("#popupClose,#popupLogin").click(function () {
            $("#popupBottom").hide();
            $.ajax({
                type: 'post',
                url: '/addSessionPopup',
                dataType: 'json',
                success: function () {
                }
            });
        })
    </script>
</div>

<!--查房价列表头部搜索专用-->
<div id="search2018" th:fragment="housingpriceSearch">
    <script src="/js/jquery.autocomplete.js" type="text/javascript"></script>
    <script src="/js/head2017.js" type="text/javascript"></script>
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <style>
        .ui-autocomplete {max-width: 462px;overflow-y: auto;overflow-x: hidden;padding-right: 20px;z-index: 100000 !important;}
        .ui-menu .ui-menu-item{line-height: 30px;height: 30px;}
        .ui-menu .ui-menu-item a{width: 101.5%;}
        * html .ui-autocomplete {height: 100px;}
    </style>
  <!--  <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js" type="text/javascript" charset="utf-8"></script>-->
    <script src="/js/searchCommon.js?v=20201030" type="text/javascript" charset="utf-8"></script>
    <script th:inline="javascript">
        var sessionId = [[${session.muser}]];
    </script>
    <div class="w">
        <div class="search">
            <input id="txtkeys" type="text" name="1" th:if="${type} == 1 or ${type} == 6"  th:value="${searchKey}" placeholder="请输入楼盘名称开始找房" class="ac_input">
            <input id="txtkeys" type="text" name="2" th:if="${type} == 2" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
            <input id="txtkeys" type="text" name="3" th:if="${type} == 3" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
            <input id="txtkeys" type="text" name="4" th:if="${type} == 4" th:value="${searchKey}" placeholder="请输入您感兴趣的楼盘或地段名" class="ac_input">
            <input id="txtkeys" type="text" name="5" th:if="${type} == 5" th:value="${searchKey}" placeholder="请输入您感兴趣的内容进行搜索，比如楼盘名字" class="ac_input">
            <input id="txtkeys" type="text" name="7" th:if="${type} == 7" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
            <input id="txtkeys" type="text" name="8" th:if="${type} == 8" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
            <input id="txtkeys" type="text" name="9" th:if="${type} == 9" th:value="${searchKey}" placeholder="请输入您感兴趣的楼盘或地段名" class="ac_input">
            <input id="txtkeys" type="text" name="10" th:if="${type} == 10" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
            <input id="txtkeys" type="text" name="11" th:if="${type} == 11" th:value="${searchKey}" placeholder="请输入区域、商圈或小区名开始找房" class="ac_input">
            <input id="txtkeys" type="text" name="12" th:if="${type} == 12" th:value="${searchKey}" placeholder="请输入您感兴趣的楼盘或地段名" class="ac_input">
            <input id="txtkeys" type="text" name="13" th:if="${type} == 13" th:value="${searchKey}" placeholder="请输入您感兴趣的楼盘或地段名" class="ac_input">
            <input type="button" value="搜 索" class="btn_search search_btn">
            <img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png"  id="deleteButton" alt="x">
        </div>
    </div>
    <script th:inline="javascript">
        if ($("input[name='1']")){
            $.ajax({
                type:'post',
                url:"/getAdvertisementList",
                data:{
                    type:'7',
                },
                success:function (data) {
                    if (data.status == 1.0){
                        var name = data.content.searchAd[0].projectName;
                        if(name){
//                            alert(name);
                            $("input[name='1']").attr("placeholder",'请输入楼盘名称开始找房  '+name);
//                        请输入楼盘名称开始找房  如：首创光和城
                        }
                    }
                }
            });
        }
    </script>
    <script>
        $(window).scroll(function(){
            $(".ui-autocomplete").hide()
        })
        $(function(){
            $("#deleteButton").click(function () {
                var redirectUrl =location.pathname;
                if( redirectUrl != null || redirectUrl != undefined || redirectUrl != ''){
                    if( redirectUrl.indexOf("search") != -1 ){
                        redirectUrl =   redirectUrl.substr(0,redirectUrl.indexOf("search"));
                        window.location.href = redirectUrl;
                    }else {
                        $("#search2018 .search .ac_input").val("");
                        $("#deleteButton").hide();
                    }
                }
            });
            $("#search2018 .search .ac_input").val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
            $("#search2018 .search .ac_input").keyup(function(){
                $(this).val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
            })
        });

    </script>
</div>
<!--新房地铁右侧广告与推荐-->
<div th:fragment="right_metro">
    <style>
        .grg a { width: 250px; line-height: 49px; padding: 0; margin-bottom: 24px; height: 49px; background: #FFF7EE; border: 1px solid #EAEAEA; font-size: 18px; font-family: Microsoft YaHei; font-weight: 400; color: #FF5200; display: block; text-align: center; }
        .grg a:hover { background-color: #FFF7EE; text-decoration: none; color: #FF5200; }

    </style>
    <div class="Along"  th:if="${metroRight.subFocus != null and !#lists.isEmpty(metroRight.subFocus)}">
        <dt><span></span>沈阳地铁规划图</dt>
        <div class="photo">
                <a href="/download">
                <img th:src="${metroRight.subFocus.get(0).image}" >
                    <div class="look">
                        <h2>点击下载</h2>
                    </div>
                </a>


        </div>
        <div class="btn">
            <a th:href="${'/static/station_map.htm'}" target="_blank">地铁找房</a>
        </div>
    </div>

    <div class="grg">
        <a th:href="@{'/helpSearch?ids=1'}" rel="2" target="_blank">我要买房</a>
        <a href="/static/saleHouse/saleHouse.htm" target="_blank" th:class=" (${firstNavIndex} == 8)? 'hover' : '' ">我要卖房</a>
    </div>

    <!--<dl class="recommend" th:if="${metroRight.subAds != null and !#lists.isEmpty(metroRight.subAds)}">
        <dt><span></span>地铁找房攻略</dt>

        <div class="title">房产快讯</div>
        <div class="hot">
            <ul class="unstyled">
                <li th:each="f,i:${metroRight.subAds}" >
                    <a th:if="${ i.index ne 6 }"  th:href="${'/news/'+f.NewsID+'.htm'}" target="_blank" th:text="${f.TitleShow}"></a>
                </li>
            </ul>
        </div>
    </dl>-->

    <dl class="recommend">
        <dt><span></span>小二精选</dt>
        <div class="hot">
            <div style=""><!--padding: 18px 15px; box-sizing: border-box;-->

                <div class="childlist">
                    <div class="childLtab">
                        <span class="chitabN">排名</span>
                        <span class="chitabL">楼盘名称</span>
                        <span class="chitabJ">价格</span>
                        <span class="chitabQ">区域</span>
                    </div>
                    <ul>
                        <li class="chilistLi pl0" th:each="project, i : ${rankList}" th:if="${i.index lt 10}">
                            <div>
                                <div th:class="${i.count le 3 ? 'chiListRank spcolor' : 'chiListRank'}" th:text="${i.count}"></div><!--前三个 加spcolor-->
                                <div class="crankList" th:style="${i.count eq 1 ? 'display:none':'display:block'}">
                                    <ul>
                                        <li class="pl0" th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></li>
                                        <li class="pl0 DDlist" th:if="${project.mPrice eq null}">待定</li>
                                        <li class="pl0" th:if="${project.mPrice ne null}"><span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡</li>
                                        <li class="p00" th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}">沈北新区</li>
                                    </ul>
                                </div>
                                <a th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}" target="_blank">
                                    <div class="crankDetails" th:style="${i.count eq 1 ? 'display:block':'display:none'}">
                                        <div class="crankDetailsdiv" style="position: relative">
                                            <img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
                                            <div class="crankDetailsdivR">
                                                <h2 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></h2>
                                                <p class="crankDetailsP" th:if="${project.mPrice eq null}" >待定</p>
                                                <p class="crankDetailsP" th:if="${project.mPrice ne null}">
                                                    <span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡
                                                    <span class="Ljun" th:text="${project.mPrice.priceType eq '起价'? '起' :'均'}"></span></p>
                                                <p class="crankDetailsD">
                                                    <span th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}"></span>
                                                    <span class="lGeng" th:if="${!#maps.isEmpty(project.area)}"
                                                          th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))
                                                          + '-' + #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}"></span>
                                                </p>
                                            </div>
                                            <div class="vru" th:if="${project.pan ne null}"></div>
                                        </div>
                                        <div class="crankDetailsreason">
                                            推荐理由：<span th:text="${project.rankDesc}"></span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </li>
                    </ul>
                </div>

            </div>
        </div>
    </dl>



    <div class="Inventory" th:if="${metroRight.subInfos != null and !#lists.isEmpty(metroRight.subInfos)}">
        <dt><span></span>沿线热销楼盘盘点</dt>
        <div class="list" >
            <ul>
                <li th:each="project,pi:${metroRight.subInfos}" th:if="${pi.index lt 3}">
                    <div class="photo">
                        <a target="_blank" th:href="${#strings.isEmpty(project.url)?'#':project.url}">
                            <img th:src="${#strings.isEmpty(project.image)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':project.image}">
                            <div class="name">
                                <h2 th:text="${#strings.isEmpty(project.projectName)?'暂无资料':project.projectName}">首创光和城</h2>
                                <h3 th:text="${#strings.isEmpty(project.region)?'暂无资料':project.region}">于洪区</h3>
                                <div class="cl"></div>
                            </div>
                        </a>
                    </div>
                    <div class="metro"  th:if="${!#strings.isEmpty(project.price)}">
                        <p><span >临近地铁</span > </p>
                        <div class="list_sun">
                            <div class="list_wei" th:each="subItem:${#strings.setSplit(project.price,'/')}" th:text="${'地铁'+subItem}"  th:title="${'地铁'+subItem}"></div>
                        </div>

                        <div class="cl"></div>
                    </div>
                    <div class="Reason" th:if="${!#strings.isEmpty(project.introduce)}">
                        <h2>推荐理由</h2>
                        <p th:text="${project.introduce}">买筑城新市府旁，全运板块，城南中央居住区，邻自贸区，地铁、公交、轻轨三位一体式交通，抵达全城，出行无阻。</p>
                    </div>
                </li>

            </ul>
        </div>
    </div>
    <div class="cnxh" th:if="${!#lists.isEmpty(guesslike)}">
        <h1><span></span>猜你喜欢</h1>
        <div class="cnxh_txt" th:each="gl:${guesslike}">
            <a th:href="${gl.TargetUrl}" target="_blank"><img th:src="${gl.AdFilePath}" th:alt="${gl.AdTitle}"></a>
            <a th:href="${gl.TargetUrl}" target="_blank"
               class="cnxh_pos cnxh_title"><th:block th:text="${gl.AdTitle}"></th:block></a>
            <div class="cnxh_m">
                <span class="cnxh_qian_icon">￥<i><th:block th:text="${#strings.isEmpty(gl.Adproprice) or gl.Adproprice eq '0'? '待定': gl.Adproprice}"></th:block></i><th:block th:if="${!#strings.isEmpty(gl.Adproprice) and gl.Adproprice ne '0'}">元/m²</th:block> </span>
            </div>
            <div class="cntx"><th:block th:text="${gl.AdPhrase}"></th:block></div>
        </div>
        <div class="cl"></div>
        <!--广告 end-->
    </div>
</div>
<!--新房学区右侧广告与推荐-->
<div th:fragment="right_school">
    <dl class="recommend" th:if="${schoolNews != null and !#lists.isEmpty(schoolNews)  and schoolList == 0}">
        <dt><span></span>教育地产指南</dt>
        <ul class="topHot">
            <li th:if="${i.count le 2}" th:each="school,i:${schoolNews}">
                <a th:href="${'/news/'+ school.id +'.htm'}">
                    <img th:src="${school.pic}" alt="">
                    <p th:text="${school.titleShow}"></p>
                </a>
            </li>
        </ul>
        <!--<div class="hot">
            <ul class="unstyled" >
                <li th:if="${i.count gt 2}" th:each="school,i:${schoolNews}">
                    <a th:href="${'/news/'+ school.id +'.htm'}" target="_blank" th:text="${school.titleShow}"></a>
                </li>
            </ul>
        </div>-->
        <a href="/news/166" class="toMoreMsg"><div><span>查看更多</span><i></i></div></a>
    </dl>

    <div class="Inventory" th:if="${schoolRight.schoolInfos != null and !#lists.isEmpty(schoolRight.schoolInfos) and schoolList == 0}">
        <dt><span></span>紧邻热点中小学楼盘</dt>
        <div class="list" >
            <ul>
                <li th:each="project,pi:${schoolRight.schoolInfos}" th:if="${pi.index lt 3}">
                    <div class="photo">
                        <a target="_blank" th:href="${#strings.isEmpty(project.url)?'#':project.url}">
                            <img th:src="${#strings.isEmpty(project.image)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':project.image}">
                            <div class="name">
                                <h2 th:text="${#strings.isEmpty(project.projectName)?'暂无资料':project.projectName}">首创光和城</h2>
                                <h3 th:text="${#strings.isEmpty(project.region)?'暂无资料':project.region}">于洪区</h3>
                                <div class="cl"></div>
                            </div>
                        </a>
                    </div>
                    <div class="metro" th:if="${!#strings.isEmpty(project.price)}">
                        <p><span >临近学校</span > </p>
                        <div class="list_sun">
                            <div class="list_wei" th:each="subItem:${#strings.setSplit(project.price,'/')}" th:text="${subItem}"  th:title="${subItem}"></div>
                        </div>
                       <div class="cl"></div>
                    </div>
                    <div class="Reason" th:if="${!#strings.isEmpty(project.introduce)}">
                        <h2>推荐理由</h2>
                        <p th:text="${project.introduce}">买筑城新市府旁，全运板块，城南中央居住区，邻自贸区，地铁、公交、轻轨三位一体式交通，抵达全城，出行无阻。</p>
                    </div>
                </li>

            </ul>
        </div>
    </div>
    <dl class="recommend" th:if="${schoolNews != null and !#lists.isEmpty(schoolNews)  and schoolList == 1}">
        <dt><span></span>教育地产指南</dt>
        <ul class="topHot">
            <li th:if="${i.count le 2}" th:each="school,i:${schoolNews}">
                <a th:href="${'/news/'+ school.id +'.htm'}">
                    <img th:src="${school.pic}" alt="">
                    <p th:text="${school.titleShow}"></p>
                </a>
            </li>
        </ul>
        <!--<div class="hot">
            <ul class="unstyled" >
                <li th:if="${i.count gt 2}" th:each="school,i:${schoolNews}">
                    <a th:href="${'/news/'+ school.id +'.htm'}" target="_blank" th:text="${school.titleShow}"></a>
                </li>
            </ul>
        </div>-->
        <a href="/news/166" class="toMoreMsg"><div><span>查看更多</span><i></i></div></a>
    </dl>

    <dl class="recommend">
        <dt><span></span>热搜榜</dt>
        <div class="">

            <div class="childlist">
                <div class="childLtab">
                    <span class="chitabN">排名</span>
                    <span class="chitabL">楼盘名称</span>
                    <span class="chitabJ">价格</span>
                    <span class="chitabQ">区域</span>
                </div>
                <ul>
                    <li class="chilistLi pl0" th:each="project , i : ${rankList}" th:if="${i.index lt 10}">
                        <div>
                            <div th:class="${i.count le 3 ? 'chiListRank spcolor' : 'chiListRank'}" th:text="${i.count}"></div>
                            <div class="crankList" th:style="${i.count eq 1 ? 'display:none':'display:block'}">
                                <ul>
                                    <li class="pl0" th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></li>
                                    <li class="pl0 DDlist" th:if="${project.mPrice eq null}">待定</li>
                                    <li class="pl0" th:if="${project.mPrice ne null}">
                                        <span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡
                                    </li>
                                    <li class="p00" th:text="${#strings.isEmpty(project.regionName) ? '' : project.regionName}"></li>
                                </ul>
                            </div>
                            <a th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}" target="_blank">
                                <div class="crankDetails" th:style="${i.count eq 1 ? 'display:block':'display:none'}">
                                    <div class="crankDetailsdiv" style="position: relative">
                                        <img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
                                        <div class="vru" th:if="${project.pan ne null}"></div>
                                        <div class="crankDetailsdivR">
                                            <h2 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></h2>
                                            <p class="crankDetailsP" th:if="${project.mPrice eq null}">待定</p>
                                            <p class="crankDetailsP" th:if="${project.mPrice ne null}">
                                                <span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡
                                                <span class="Ljun" th:text="${project.mPrice.priceType eq '起价'? '起' :'均'}"></span></p>
                                            <p  class="crankDetailsD">
                                                <span th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}"></span>
                                                <span class="lGeng" th:if="${!#maps.isEmpty(project.area)}"
                                                      th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))
                                                      + '-' + #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}"></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </li>
                </ul>
            </div>

        </div>
    </dl>
</div>
<!--二维码分享改版(2018.10.25)-->
<div th:fragment="share_code">
    <link rel="stylesheet" type="text/css" href="/css/share_icon.css?v=20181025"/>
    <div class="photoAlbum">
        <div class="secondDetailEwm">
            <i class="Ewmclose"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt=""></i>
            <div class="EwmImg" id="share_code"></div>
            <p>扫描到手机</p>
        </div>
     </div>
    <script>
        $(function () {
//            $(".share_tan_main").hide();
//            $(".share_tan").mouseover(function () {
//                $(".share_tan_main").toggle()
//            });
//            $(".share_tan").mouseout(function () {
//                $(".share_tan_main").toggle()
//            });
            var replaceLocationUlr = window.location.href;
            var mhead = "fang2/";
            var type = "";
            if(replaceLocationUlr.indexOf("salehouse")!= -1){
                mhead = "fang2/";
            }else if(replaceLocationUlr.indexOf("rent")!= -1){
                mhead = "fang3/";
            }else if(replaceLocationUlr.indexOf("shop")!= -1){
                mhead = "fang4/";
            }else if(replaceLocationUlr.indexOf("video")!= -1){
                mhead = "video/";
            }else if(replaceLocationUlr.indexOf("scriptorium")!= -1){
                mhead = "fang5/";
            }else if(replaceLocationUlr.indexOf("dealSale")!= -1){
                mhead = "txnDetail/";
                type = "-1";
            }else if(replaceLocationUlr.indexOf("dealRent")!= -1){
                mhead = "txnDetail/";
                type = "-2";
            }else if(replaceLocationUlr.indexOf("dealShop")!= -1){
                mhead = "txnDetail/";
                type = "-3";
            }
            $('#share_code').qrcode(
                    {
                        width: 90,
                        height: 90,
                        text: "https://m.fangxiaoer.com/"+mhead+[[${houseId}]]+type+".htm"
                    })
        });
        $(".Ewmclose").click(function(){
            $(".secondDetailEwm").hide();
        });
    </script>
</div>
<!--学区划分广告(2018.10.26)-->
<div th:fragment="school_detail">
    <style>
        .school_detailphoto { width: 249px;height: 192px; overflow: hidden;display: block;margin-bottom: 10px;}
        .school_detailphoto a{width: 249px;height: 192px;overflow: hidden;margin: 10px 0;display: block;}
        .school_detailphoto a img{width: 100%}
    </style>
       <!-- <div class="school_detailphoto" th:if="${schoolRight.schoolFocus != null and !#lists.isEmpty(schoolRight.schoolFocus)}">
            <a th:href="${#strings.isEmpty(schoolRight.schoolFocus.get(0).url)?'':schoolRight.schoolFocus.get(0).url}" target="_blank">
                <img th:src="${schoolRight.schoolFocus.get(0).image}">
            </a>
        </div>-->
    <div>
        <a href="https://event.fangxiaoer.com/20181123.htm" target="_blank">
            <img src="https://static.fangxiaoer.com/web/images/ico/subwayAd.jpg" alt="">
        </a>
    </div>

</div>
<!--搜索-->
<div id="search2017" th:fragment="videoSearch">
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/searchVideo.js" type="text/javascript" charset="utf-8"></script>
    <div id="search2017">
        <div class="w" id="searchFloat">
            <div class="search">
                <div class="searchFloatKuang">
                    <a href="/" class="searchFloatImg"><img src="https://static.fangxiaoer.com/web/images/ico/head/logo_white.png" alt="房小二网"></a>
                    <input id="txtkeys" type="text" name="5" th:value="${searchKey}" placeholder="请输入视频名称进行搜索" class="ac_input">
                    <input type="button" value="搜 索" class="btn_search search_btn">
                    <img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png"  id="deleteButton" alt="x">
                </div>
            </div>
        </div>

    </div>
    <script>
        $(window).scroll(function(){
            $(".ui-autocomplete").hide()
        })
    </script>
</div>
<!--首页通栏广告-便民服务-->
<div th:fragment="ConvenientService">
    <div class="ConvenientService">
        <ul>
            <li><a href="https://sy.fangxiaoer.com/news/72123.htm" target="_blank"><i class="cSIcon cSIcon1"></i><s>购房资格导读</s></a></li>
            <li><a href="https://sy.fangxiaoer.com/news/72121.htm" target="_blank"><i class="cSIcon cSIcon2"></i><s>落户流程</s></a></li>
            <li><a href="http://www.syfc.com.cn/work/ysxk/query_xukezheng.jsp" target="_blank"><i class="cSIcon cSIcon3"></i><s>预售证查询</s></a></li>
            <li><a href="http://***********:7001/newbargain/download/findht/loginHth.jsp" target="_blank"><i class="cSIcon cSIcon4"></i><s>合同备案查询</s></a></li>
            <li><a href="https://sy.fangxiaoer.com/schools/" target="_blank"><i class="cSIcon cSIcon5"></i><s>学区找房</s></a></li>
            <li><a href="http://sygjjwt.shenyang.gov.cn/login.html" target="_blank"><i class="cSIcon cSIcon6"></i><s>公积金查询</s></a></li>
            <li><a href="https://sy.fangxiaoer.com/static/business.htm" target="_blank"><i class="cSIcon cSIcon7"></i><s>房贷计算器</s></a></li>

        </ul>
    </div>
</div>
<!--新房、地铁、学区列表通栏广告-->
<div th:fragment="commom_horizontalBanner">
    <div class="Advertisement" th:if="${!#lists.isEmpty(horizontalBanner)}">
        <div class="swiper-container content01">
            <section  class="swiper-wrapper">
                <div class="swiper-slide" th:each="banner:${horizontalBanner}">
                    <a th:href="${banner.TargetUrl}" target="_blank">
                        <div class="pic">
                            <img th:src="${banner.AdFilePath}" alt="" />
                        </div>
                        <div class="pic_sun">
                            <img th:if="${#strings.toString(banner.AdTitle).indexOf('广告') ne -1}" src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" alt="" class="guanggao" />
                            <img th:if="${#strings.toString(banner.AdTitle).indexOf('活动') ne -1}"  src="https://static.fangxiaoer.com/web/images/ico/sign/ard.png" alt="" class="hongdong" />
                        </div>
                    </a>
                </div>
            </section >
            <div class="swiper-pagination swiper-pagination01"></div>
        </div>
    </div>
    <th:block  th:if="${#lists.size(horizontalBanner) gt 1}">
        <script >
            var mySwiper= new Swiper('.content01', {
                loop : true,
                pagination : '.swiper-pagination01',
                autoplay: 5000,
                loop:true,
                autoplayDisableOnInteraction: false
            });
        </script>
    </th:block>
</div>
<!--仿制美洽聊天入口-->
<div th:fragment="talkBtn(controllerPath)">
    <script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/util.js?v=4"></script>
    <!--引入登录-->
    <div th:include="house/detail/fragment_login::login"></div>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/js/esfIm_ziyuan/talkBtn.css">
    <!-- 仿美洽按钮css -->
    <!--<div class="talk_btn_box">
        &lt;!&ndash; 点击查看消息按钮 &ndash;&gt;
        <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal">
            <div class="talk_btn">
                <div class="left_icon">
                    <img width="22px" th:src="@{/img/chat.png}" alt="">
                </div>
                <div class="tip_text">在线咨询</div>
            </div>
        </a>
        <div class="talk_btn" th:if="${#session?.getAttribute('sessionId') != null}">
&lt;!&ndash;        <div class="talk_btn" th:if="${houseInfo?.projectStatus ne '3'}">&ndash;&gt;
            <a id="showMiniTalkWindow" href="javascript:void(0)">
                <div class="left_icon">
                    <img width="22px" th:src="@{/img/chat.png}" alt="">
                </div>
                <div class="tip_text">在线咨询</div>
            </a>
            <div th:if="${commonType eq null}" th:include="instantMessaging/im/main_mini::im_mini_window"></div>
        </div>

        <script th:inline="javascript">
            var im_houseId = [[${projectId}]];
            // var im_houseType = [[${houseType}]];
            var sessionId = [[${sessionId}]];
            var yunXin,
                serviceId,
                im_house_href = window.location.href,
                im_contentd_ready = false,
                imTalkList = JSON.parse(window.readCookie("imTalkList")) || {};
            $("#showMiniTalkWindow").on('click', function () {
                $.ajax({
                    type: "POST",
                    async: false,
                    url: [[${controllerPath}]],
                    // data: {'houseId': im_houseId},
                    dataType: "json",
                    success: function (data) {
                        if (data.state == 1){
                            serviceId = data.serviceId;

                            //默认发消息逻辑
                            if (!!im_houseId) {
                                if (imTalkList[im_houseId] === undefined){
                                    imTalkList[im_houseId] = true;
                                    window.addCookie("imTalkList", JSON.stringify(imTalkList), 0);
                                };
                            };

                            /**
                             * 云信实例化
                             * @see module/base/js
                             */
                            if (!yunXin || !im_contentd_ready){
                                yunXin = new YX(userUID);
                                //内部js存在默认发送链接逻辑
                            };
                            $(".im_outer").show();
                            $(".im_content_inner").scrollTop(999999);


                        } else if (data.state == 0){
                            alert(data.message);
                        }
                    },
                    error: function (err) {
                        console.log(err);
                        alert('初始化云信失败 请刷新页面后重新尝试。');
                    }
                })
            })

            $("#im_history").on('click',function () {
                window.open([[${controllerPath}]].replace("/new", ""), '_blank');
            })
        </script>
    </div>
    -->
</div>

<!--未登陆状态-点击上传图片弹登陆-->
<div th:fragment="notLoggedIn">
    <script type="text/javascript">
        $(document).on('click','#fileImg label',function(){
            if(sessionId1 == null){//没登录 打开登录弹窗
                $("#login").show()
                $(".tc_full").show()
                return false;
            }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
                $(".tc_realname").show()
                $(".tc_full").show()
                $("body").css("overflow-y","clip")
                return false;
            }
        })
    </script>
</div>


<!--求租求购 tap切换-->
<div th:fragment="seekOrderTap">
    <div class="seekTap">
        <a th:href="@{/seekOrder/1/}" target="_blank" th:class="(${seek}==1)?'tapli son':'tapli'">新房</a>
        <a th:href="@{/seekOrder/2/}" target="_blank" th:class="(${seek}==2)?'tapli son':'tapli'">二手房</a>
        <a th:href="@{/seekOrder/3/}" target="_blank" th:class="(${seek}==3)?'tapli son':'tapli'">租房</a>
        <a th:href="@{/seekOrder/4/}" target="_blank" th:class="(${seek}==4)?'tapli son':'tapli'">商铺</a>
        <a th:href="@{/seekOrder/5/}" target="_blank" th:class="(${seek}==5)?'tapli son':'tapli'">写字楼</a>
    </div>
</div>

</body>
</html>