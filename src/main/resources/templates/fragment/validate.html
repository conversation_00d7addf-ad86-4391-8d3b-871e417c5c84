<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<body>
    <div th:fragment="validate">
        <script type="text/javascript">
            $(function () {
                //手机号码验证
                $.validator.addMethod("phoneNum", function (value, element, params) {
                    var phoneNumReg = /^1[34578]\d{9}$/;
                    if (phoneNumReg.test(value))
                        return true;
                    else
                        return false;
                }, "手机号码格式不正确");
                //非0和非数字验证
                $.validator.addMethod("noNum0", function (value, element, params) {
                    if (value != 0)
                        return true;
                    else
                        return false;
                }, "不能为0或非数字");
                //字符串长度大于30小于100验证
                $.validator.addMethod("describe", function (value, element, params) {
                    if (value.length > 29 && value.length < 1001)
                        return true;
                    else
                        return false;
                }, "房源描述至少30字且不能超过1000字");

                //字符串长度大于30小于100验证
                $.validator.addMethod("discuss", function (value, element, params) {
                    if (value != 0 || value == "面议" || value=="")
                        return true;
                    else
                        return false;
                }, "不能为0或非数字");

                $.validator.addMethod("decimal", function (value, element, params) {
                    var decimalReg = /^\d+(\.\d{1,2})?$/;
                    if (value == "面议" || decimalReg.test(value))
                        return true;
                    else
                        return false;
                }, "请输入大于0的数字，且小数位只能保留两位");
                //整数验证
                $.validator.addMethod("integer", function (value, element, params) {
                    var integerReg = "^-?[1-9]\\d*$";//整数
                    if (value != 0 && integerReg.test(value))
                        return true;
                    else
                        return false;
                }, "请输入整数");



                // 验证值小数位数不能超过1位
//                $.validator.addMethod("decimal", function (value, element, params) {
//                    var decimal = /^\d+(\.\d{1,2})?$/;
//                    return this.optional(element) || (decimal.test(value));
//                }, $.validator.format("小数位数不能超过一位!且不能为0"));

                $("#secondHouseForm").validate({
                    success: function (label) {
                        label.html("<em class='success'></em>")
                    },
                    errorPlacement: function (error, element) {
                        error.appendTo(element.parents(".sale_form_r li").find(".errorBox"));
                        $("body").click();
                    },
                    rules: {
                        //小区名称
                        residential: {
                            required: true
                        },
                        //室厅
                        room: {
                            required: true,
                            digits: true,
                            min: 1
                        },
                        //厅
                        hall: {
                            required: true,
                            digits: true
                        },
                        //卫
                        toilet: {
                            required: true,
                            digits: true
                        },
                        //面积
                        buildArea: {
                            required: true,
                            decimal: true,
                            noNum0: true
                        },
                        //总价
                        totalPrice: {
                            decimal: true
                        },
                        //房源描述
                        describe:{
                            required: true,
                            describe:true
                        },
                        //租房价格
                        rentPrice: {
                            decimal: true,
                            discuss: true
                        },

                        contactPhone: {
                            required: true,
                            phoneNum: true
                        },
                        //标题
                        title: {
                            maxlength: 30,
                            minlength: 4,
                            required: true
                        },
                        //建筑面积
                        buildarea: {
                            required: true,
                            noNum0: true,
                            decimal: true
                        },
                        //层
                        floor:{
                            required: true,
                            digits: true
                        },
                        //总层数
                        totalfloor:{
                            required: true,
                            min: function () {
                                if ($("#floor").val()) {
                                    return parseInt($("#floor").val())
                                } else {
                                    return parseInt(0)
                                }
                            }
                        },
                        //建筑年代
                        buildingTime:{
                            digits: true,
                            maxlength: 4,
                            minlength: 4,
                            max: function () {
                                return parseInt(getYear())
                            }
                        },
                        //联系人
                        contactPerson: {
                            required: true,
                            maxlength: 6,
                            minlength: 2
                        }
                    },
                    messages: {
                        residential: {
                            required: "请填写小区名称"
                        },
                        room: {
                            required: "请填写数字，如 2 室 1 厅 1 卫",
                            digits: "请填写数字，如 2 室 1 厅 1 卫",
                            min: "请填写数字，如 2 室 1 厅 1 卫"
                        },
                        hall: {
                            required: "请填写数字，如 2 室 1 厅 1 卫",
                            digits: "请填写数字，如 2 室 1 厅 1 卫"
                        },
                        toilet: {
                            required: "请填写数字，如 2 室 1 厅 1 卫",
                            digits: "请填写数字，如 2 室 1 厅 1 卫"
                        },
                        buildArea: {
                            required: "请填写面积",
                            decimal: "请输入大于0的数字，且小数位只能保留两位",
                            noNum0: "面积不能为0"
                        },
                        describe:{
                            required: "请填写房源描述",
                            Describe:"房源描述至少30字且不能超过1000字"
                        },
                        rentPrice: {
                            decimal: "价格只能为数字，允许两位小数",
                            noNum0: "价格不能为0或者负数"
                        },
                        totalPrice: {
                            decimal1: "价格只能为数字，允许两位小数"
                        },
                        contactPhone: {
                            required: "请填写联系方式"
                        },
                        contactPerson: {
                            required: "请填写联系人"
                        },
                        title: {
                            required: "请填写房源标题",
                            maxlength: "房源标题最多30个字",
                            minlength: "标题太过简单"
                        },
                        buildarea: {
                            required: "请正确填写面积",
                            noNum0: "面积不能为0或其他非整数",
                            decimal: "面积必须为正整数或两位小数"
                        },
                        floor:{
                            required: "请填写楼层",
                            digits:"不能为0或其他非负整数"
                        },
                        totalFloor:{
                            required: "请填写楼层",
                            digits: "总楼层只能为正整数",
                            min: "总楼层不能小于当前楼层",
                            noNum0: "楼层不能为0或其他非整数"
                        },
                        buildingTime:{
                            required: "请填写建筑年代",
                            digits: "请正确填写 - 格式:2010",
                            maxlength: "请正确填写 - 格式:2010",
                            minlength: "请正确填写 - 格式:2010",
                            max: "不能大于当前年份",
                            min: "不能小于起始年份"
                        }
                    }
                });
            });
        </script>
    </div>
</body>
</html>