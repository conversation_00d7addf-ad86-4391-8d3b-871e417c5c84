<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div id="replie" class="house_left" th:fragment="rents_line">
    <th:block th:each="rent,i:${rents}">
        <div class="inf" >
            <a th:href="${'/rent/'+rent.houseId+'.htm'}" target="_blank" class="infLeft">
                <!--<i th:if="${rent.picNum ne null and #strings.toString(rent.picNum).length() gt 1 or rent.picNum gt '4'}">
                    <th:block th:text="${rent.picNum}"></th:block></i>-->

                <!--加广告图标-->
                <span class="sub" th:if="${#strings.toString(rent.auction) eq '1'}">
                    <img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" />
                </span>
                <span class="sub" th:if="${#strings.toString(rent.auction) ne '1' && #strings.toString(rent.stickOrder) eq '-1'}">
                    <img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" />
                </span>

                <img th:src="${rent.pic eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':rent.pic}" th:alt="${rent.title}">
                <div class="" th:if="${rent.memberType eq '2' and rent.isXiQue eq '1'}">佣金95折</div>
<!--                <s class="videoIcon" th:if="${rent.mediaID ne null}"></s>-->
                <!--VR and 视频都存在 -->
                <s class="listIconK" th:if="${#strings.toString(rent.PanID) ne null and #strings.toString(rent.mediaID) ne null }">
                    <s class="vrListIcon" th:if="${#strings.toString(rent.PanID) ne null}"></s>
                    <s class="videoListIcon" th:if="${rent.mediaID ne null}"></s>
                </s>
                <!--VR存在 -->
                <s class="listIconK" th:if="${#strings.toString(rent.PanID) ne null and #strings.toString(rent.mediaID) eq null }">
                    <s class="vrListIcon" th:if="${#strings.toString(rent.PanID) ne null}"></s>
                </s>
                <!--视频存在 -->
                <s class="listIconK" th:if="${#strings.toString(rent.PanID) eq null and #strings.toString(rent.mediaID) ne null }">
                    <s class="videoListIcon" th:if="${rent.mediaID ne null}"></s>
                </s>
            </a>&nbsp;&nbsp;
            <div class="infCtn">
                <a class="newHouseListTitle" target="_blank"  th:href="${'/rent/'+rent.houseId+'.htm'}">
                    <div th:text="${rent.title}" th:title="${rent.title}"></div>
					<!-- 增加安心好房icon -->
					<div class="anxin_icon" th:if="${#strings.equals(rent.anXuan,'-1')}">
						<div class="anxin_icon_l">安心</div>
						<div class="anxin_icon_r">好房</div>
					</div>
                    <i class="listIconBidPrice" th:if="${#strings.toString(rent.auction) eq '1'}"></i>
                    <i class="listIconIstop" th:if="${#strings.toString(rent.auction) ne '1' && #strings.toString(rent.stickOrder) eq '-1'}"></i>
                </a>
                <div  class="fourSpan">
                    <span th:if="${!#strings.isEmpty(rent.rentTypeName)}">
                        <th:block th:text="${rent.rentTypeName}"></th:block>
                    </span>
                    <span class="rent_room" >
                        <th:block th:text="${rent.room+'室'+rent.hall+'厅'+rent.toilet+'卫'}"></th:block>
                    </span>
                    <span th:if="${!#strings.isEmpty(rent.area)}">
                        <th:block th:text="${rent.area+'m²'}"></th:block>
                    </span>

                   <!-- <span class="rent_bedroom" th:id="${'bedroom'+i.index}" th:if="${#strings.toString(rent.rentType) eq '2'}">
                        <th:block th:text="${rent.bedRoom+'（'+rent.room+'室）'}"></th:block>
                    </span>-->
                    <span th:if="${!#strings.isEmpty(rent.floorDesc) && !#strings.isEmpty(rent.totalFloorNumber)}">
                        <th:block th:text="${rent.floorDesc+'/'+rent.totalFloorNumber+'层'}"></th:block>
                    </span>
                    <span th:if="${!#strings.isEmpty(rent.forwardTypeName)}"><th:block th:text="${rent.forwardTypeName}"></th:block></span>
                </div>
                <p  class="houseAddress"  th:if="${!#strings.isEmpty(rent.regionName) and !#strings.isEmpty(rent.subName) and !#strings.isEmpty(rent.plateName) and !#strings.isEmpty(rent.address)}">
                    <s th:if="${ !#strings.isEmpty(rent.subName)}" class="houseAddressSpance">
                    <a th:href="${'/saleVillages/'+rent.subId+'/index.htm'}" target='_blank' th:text="${rent.subName}"></a>
                </s>
                    <s th:if="${!#strings.isEmpty(rent.regionName) and !#strings.isEmpty(rent.plateName) and !#strings.isEmpty(rent.address)}">
                        <a th:href="${'/rents/r'+rent.regionId}" th:text="${rent.regionName}"></a>-
                        <a th:href="${'/rents/j'+rent.plateId+'-r'+rent.regionId}" th:text="${rent.plateName}"></a>-
                        <th:block th:text="${rent.address}"></th:block>
                    </s>
                </p>
                <div class="bottomtext">
                    <div  class="houseItemIcon">
                        <s th:if="${rent.houseTrait ne null and rent.houseTrait ne ''}">
                            <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(rent.houseTrait).split(',')}" th:if="${i.count le 3}" th:text="${item}" ></span>
                        </s>
                    </div>
                    <span class="personShow">
                    <div th:if="${rent.memberType eq '2'}">
                    <!-- <i class="personIcon"></i>-->
                        <div class="agentShowImg"><img th:if="${!#strings.isEmpty(rent.Avatar)}" th:src="${rent.Avatar}" alt=""></div>
                        <div class="agentShowImg"><img th:if="${#strings.isEmpty(rent.Avatar)}" src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt=""></div>
                        <i>
                            <th:block  th:text="${rent.houseOwner}"></th:block>
                            <th:block th:if="${!#strings.isEmpty(rent.spantime)}" th:text="${rent.spantime+'更新'}"></th:block>
                        </i>
                        <i><th:block th:if="${!#strings.isEmpty(rent.IntermediaryName)}" th:text="${rent.IntermediaryName}"></th:block></i>
                        <!-- 安心经纪人标识 -->
						<i th:if="${#strings.equals(rent.anXuan,'-1')}">
							<img src="https://static.fangxiaoer.com/web/images/sy/anxin/renzheng_iocn.png" style="width: 14.6px;height: 14.6px;">
						</i>
                    </div>
                    <div th:if="${rent.memberType eq '1'}">
                         <img  src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
                         <i><th:block  th:text="${'个人'}"></th:block></i>
                    </div>
                </span>
                </div>

            </div>
            <div class="infRight">
                <p class="infRightPrise">
                    <th:block th:text="${#strings.toString(rent.price) eq '0' ? '面议' : rent.price }"></th:block>
                    <i th:if="${rent.price ne '0'}">元/月</i>
                </p>
                <input type="hidden" th:id="${'isroom'+i.index}" th:value="${rent.bedRoom}">
                <p style="display: none">
                    男女不限
                </p>
<!--                <a class="checkHouse" target="_blank" th:href="'/rents/-v'+${rent.subId}">查看同小区房源 &gt;</a>-->
            </div>
        </div>

        <!-- 需要判断显示广告内容 -->
        <div class="inf" th:if="${i.index eq 6}" id="esfListEwmBanner">
            <div id = "brandpic"  class="esfListEwmBanner" style="background-color: #fff">
                <img src="https://static.fangxiaoer.com/web/images/sy/sale/list/esfListEwmBanner.png" alt="">
                <h4>扫描下载房小二app</h4>
                <p>好房为你而选，让买房更简单！</p>
                <i><img src="https://static.fangxiaoer.com/web/images/sy/sale/list/esfListClose.png" alt="" class="esfListClose" ></i>
            </div>
        </div>
    </th:block>
    <script>
        $("#esfListEwmBanner").hide();
    </script>
</div>

</div>


<div id="reprow" class="list rent" th:fragment="rents_piece">
    <a class="house" th:each="rent,i:${rents}" th:href="${'/rent/'+rent.houseId+'.htm'}"  target="_blank">
        <div  class="ico">
            <span class="ico_zdbg" th:if="${rent.stickOrder eq '-1'}"></span>
            <span class="ico_yongjin " th:if="${rent.isXiQue eq '1'}">佣金95折</span>
            <span class="jing" th:if="${#strings.toString(rent.auction) eq '1'}">精</span>
            <s class="videoIcon videoIcon2" th:if="${rent.mediaID ne null}"></s>
        </div>
        <img th:src="${rent.pic eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':rent.pic}" th:alt="${rent.title}">


        <p  class="rent_room" th:if="${rent.bedRoom eq ''}">
            <th:block th:text="${rent.room+'室'+rent.hall+'厅  '+rent.area+'m²'}"></th:block>
        </p>

        <p  class="rent_bedroom" th:if="${rent.bedRoom ne ''}">
            <th:block th:text="${rent.room+'室'+rent.hall+'厅  '+rent.bedRoom}"></th:block>&nbsp;
            <th:block th:text="${'男女不限'}"></th:block>
        </p>


        <p class="sy_price"><span th:text="${#strings.toString(rent.price) eq '0' ? '面议' : rent.price }"></span>
            <i th:if="${rent.price ne '0'}">元/月</i></p>
        <p>
            <th:block th:text="${#strings.abbreviate(rent.title,28)}"></th:block>
        </p>
        <p><span><th:block th:text="${rent.regionName}"></th:block></span><th:block th:text="${rent.subName}"></th:block></p>
    </a>
</div>



<div id="recommond" class="house_left" th:fragment="recommond_rents">
    <style>
        .sort #sortParam a+a+a+a{
            background-position: 46px 7px;}
        .sort #sortParam a+a+a+a+a{    background: none !important;}
        .sort #sortParam a+a+a+a+a:hover{border: none;color: #ff5200}
        #scdHouseLeftList {
            width: 853px;
            border: 1px solid #eaeaea;
            background: #fff;
            float: left;
        }
        #left{margin-bottom: 30px}
        #right{float: right;margin-bottom: 30px}
        .scdHouseLeftListTitle{line-height: 30px;
            font-size: 16px;
            padding-left: 20px;
            font-weight: bold;
            padding-top: 10px;
            display: block;}
    </style>
    <br>
    <div th:if="${(!#lists.isEmpty(recommendRentList) )}" id="scdHouseLeftList" class="longLeft" >
    <span class="scdHouseLeftListTitle">房源推荐</span>
    <div id="scdHouseLeft">
        <div th:each="rent,i:${recommendRentList}" >
            <div class="inf" >
                <a th:href="${'/rent/'+rent.houseId+'.htm'}" target="_blank" class="infLeft">
                    <!--<i th:if="${rent.picNum ne null and #strings.toString(rent.picNum).length() gt 1 or rent.picNum gt '4'}">
                        <th:block th:text="${rent.picNum}"></th:block></i>-->
                    <img th:src="${rent.pic eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':rent.pic}" th:alt="${rent.title}">
                    <div class="" th:if="${rent.memberType eq '2' and rent.isXiQue eq '1'}">佣金95折</div>
                    <!--                <s class="videoIcon" th:if="${rent.mediaID ne null}"></s>-->
                    <!--VR and 视频都存在 -->
                    <s class="listIconK" th:if="${#strings.toString(rent.PanID) ne null and #strings.toString(rent.mediaID) ne null }">
                        <s class="vrListIcon" th:if="${#strings.toString(rent.PanID) ne null}"></s>
                        <s class="videoListIcon" th:if="${rent.mediaID ne null}"></s>
                    </s>
                    <!--VR存在 -->
                    <s class="listIconK" th:if="${#strings.toString(rent.PanID) ne null and #strings.toString(rent.mediaID) eq null }">
                        <s class="vrListIcon" th:if="${#strings.toString(rent.PanID) ne null}"></s>
                    </s>
                    <!--视频存在 -->
                    <s class="listIconK" th:if="${#strings.toString(rent.PanID) eq null and #strings.toString(rent.mediaID) ne null }">
                        <s class="videoListIcon" th:if="${rent.mediaID ne null}"></s>
                    </s>

                </a>&nbsp;&nbsp;
                <div class="infCtn">
                    <a class="newHouseListTitle" target="_blank"  th:href="${'/rent/'+rent.houseId+'.htm'}">
                        <div th:text="${rent.title}" th:title="${rent.title}"></div>
                        <i class="listIconBidPrice" th:if="${#strings.toString(rent.auction) eq '1'}"></i>
                        <i class="listIconIstop" th:if="${#strings.toString(rent.auction) ne '1' && #strings.toString(rent.stickOrder) eq '-1'}"></i>
                    </a>
                    <div  class="fourSpan">
                    <span th:if="${!#strings.isEmpty(rent.rentTypeName)}">
                        <th:block th:text="${rent.rentTypeName}"></th:block>
                    </span>
                        <span class="rent_room" >
                        <th:block th:text="${rent.room+'室'+rent.hall+'厅'+rent.toilet+'卫'}"></th:block>
                    </span>
                        <span th:if="${!#strings.isEmpty(rent.area)}">
                        <th:block th:text="${rent.area+'m²'}"></th:block>
                    </span>

                        <!-- <span class="rent_bedroom" th:id="${'bedroom'+i.index}" th:if="${#strings.toString(rent.rentType) eq '2'}">
                             <th:block th:text="${rent.bedRoom+'（'+rent.room+'室）'}"></th:block>
                         </span>-->
                        <span th:if="${!#strings.isEmpty(rent.floorDesc) && !#strings.isEmpty(rent.totalFloorNumber)}">
                        <th:block th:text="${rent.floorDesc+'/'+rent.totalFloorNumber+'层'}"></th:block>
                    </span>
                        <span th:if="${!#strings.isEmpty(rent.forwardTypeName)}"><th:block th:text="${rent.forwardTypeName}"></th:block></span>
                    </div>
                    <p  class="houseAddress"  th:if="${!#strings.isEmpty(rent.regionName) and !#strings.isEmpty(rent.subName) and !#strings.isEmpty(rent.plateName) and !#strings.isEmpty(rent.address)}">
                        <s th:if="${ !#strings.isEmpty(rent.subName)}" class="houseAddressSpance">
                            <a th:href="${'/saleVillages/'+rent.subId+'/index.htm'}" target='_blank' th:text="${rent.subName}"></a>
                        </s>
                        <s th:if="${!#strings.isEmpty(rent.regionName) and !#strings.isEmpty(rent.plateName) and !#strings.isEmpty(rent.address)}">
                            <a th:href="${'/rents/r'+rent.regionId}" th:text="${rent.regionName}"></a>-
                            <a th:href="${'/rents/j'+rent.plateId+'-r'+rent.regionId}" th:text="${rent.plateName}"></a>-
                            <th:block th:text="${rent.address}"></th:block>
                        </s>
                    </p>
                    <div class="bottomtext">
                        <div  class="houseItemIcon">
                            <s th:if="${rent.houseTrait ne null and rent.houseTrait ne ''}">
                                <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(rent.houseTrait).split(',')}" th:if="${i.count le 3}" th:text="${item}" ></span>
                            </s>
                        </div>
                        <span class="personShow">
                    <div th:if="${rent.memberType eq '2'}">
                    <!-- <i class="personIcon"></i>-->
                        <div class="agentShowImg"><img th:if="${!#strings.isEmpty(rent.Avatar)}" th:src="${rent.Avatar}" alt=""></div>
                        <div class="agentShowImg"><img th:if="${#strings.isEmpty(rent.Avatar)}" src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt=""></div>
                        <i>
                            <th:block  th:text="${rent.houseOwner}"></th:block>
                            <th:block th:if="${!#strings.isEmpty(rent.spantime)}" th:text="${rent.spantime+'更新'}"></th:block>
                        </i>
                        <i><th:block th:if="${!#strings.isEmpty(rent.IntermediaryName)}" th:text="${rent.IntermediaryName}"></th:block></i>
                    </div>
                    <div th:if="${rent.memberType eq '1'}">
                         <img  src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
                         <i><th:block  th:text="${'个人'}"></th:block></i>
                    </div>
                </span>
                    </div>

                </div>
                <div class="infRight">
                    <p class="infRightPrise">
                        <th:block th:text="${#strings.toString(rent.price) eq '0' ? '面议' : rent.price }"></th:block>
                        <i th:if="${rent.price ne '0'}">元/月</i>
                    </p>
                    <input type="hidden" th:id="${'isroom'+i.index}" th:value="${rent.bedRoom}">
                    <p style="display: none">
                        男女不限
                    </p>
                    <!--                <a class="checkHouse" target="_blank" th:href="'/rents/-v'+${rent.subId}">查看同小区房源 &gt;</a>-->
                </div>
            </div>
        </div>
    </div>

</div>
</div>
</div>
</body>
</html>