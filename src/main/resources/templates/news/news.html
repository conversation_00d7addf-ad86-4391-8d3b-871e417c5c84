<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title th:if="${categoryId == '160' or #strings.isEmpty(categoryId)}">热门资讯,沈阳房产热门资讯 - 房小二网</title>
    <title th:if="${categoryId == '149'}">行业关注,沈阳房产行业关注 - 房小二网</title>
    <title th:if="${categoryId == '148'}">置业推荐,沈阳房产置业推荐 - 房小二网</title>
    <title th:if="${categoryId == '151'}">楼盘动态,沈阳房产楼盘动态 - 房小二网</title>
    <title th:if="${categoryId == '156'}">专题推荐,沈阳房产专题推荐 - 房小二网</title>
    <title th:if="${categoryId == '154'}">工程进度,沈阳房产工程进度 - 房小二网</title>
    <title th:if="${categoryId == '166'}">学区资讯,沈阳房产学区资讯 - 房小二网</title>
    <meta name="keywords" content="沈阳房产行业信息,沈阳楼市行业信息,楼市产行业资讯,房地产行业信息"/>
    <meta name="description" content="房小二网行业关注是房地产门户网站，覆盖楼市，土地，民生，政策，贷款，学区，房企，地铁等相关内容，提供最新的房地产行业信息，为您在寻找相关信息时提供最优价值参考"/>
    <meta name="mobile-agent" th:if="${#strings.isEmpty(categoryId)}"
          content="format=html5;url=https://m.fangxiaoer.com/news.htm">
    <meta name="mobile-agent" th:if="${!#strings.isEmpty(categoryId)}"
          th:content="${'format=html5;url=https://m.fangxiaoer.com/news'+categoryId+'.htm'}">
<!--    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css"/>-->
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20181019"/>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
        <link href="https://static.fangxiaoer.com/web/styles/sy/default/default.css?v=20200330" rel="Stylesheet" type="text/css" />
        <link href="https://static.fangxiaoer.com/web/styles/sy/news/default.css?t=20220216" rel="stylesheet"type="text/css">
<!--    <link href="/css/default.css?t=20191203" rel="stylesheet"type="text/css">-->
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/swiper/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/swiper.min.css" rel="stylesheet" type="text/css">
    <style type="text/css">
        .swiper-button-prev {
            background: url(https://static.fangxiaoer.com/web/images/sy/house/1.png) no-repeat;
            margin-left: 40px;
            height: 60px;
            width: 30px;
            opacity: 0.35;
        }

        .swiper-button-next {
            background: url(https://static.fangxiaoer.com/web/images/sy/house/2.png) no-repeat;
            margin-right: 40px;
            height: 60px;
            width: 30px;
            opacity: 0.35;
        }


        .swiper-button-next.swiper-button-disabled {
            background: url(https://static.fangxiaoer.com/web/images/sy/house/2.png) no-repeat;
            pointer-events: all;
        }

        .swiper-button-prev.swiper-button-disabled {
            background: url(https://static.fangxiaoer.com/web/images/sy/house/1.png) no-repeat;
            pointer-events: all;
        }

        .swiper-slide div {
            width: 1170px;
            margin: 0 auto;
            padding-top: 250px;
        }

        .swiper-slide div span {
            background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
            padding: 4px 14px;
            border-radius: 4px;
            color: #fff;
            float: right;
        }

        .swiper-button-prev:hover {
            background: url(https://static.fangxiaoer.com/web/images/sy/house/3.png) no-repeat;
            opacity: 1
        }

        .swiper-button-next:hover {
            background: url(https://static.fangxiaoer.com/web/images/sy/house/4.png) no-repeat;
            opacity: 1
        }

        .video_box {
            border: 1px solid #eaeaea;
            margin-bottom: 24px;
        }

        .video_box:hover a {
            color: #ff5200;
        }

        .video_box .recommend {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
        }

        .video_box .title {
            line-height: 48px;
            font-size: 18px;
        }

        .video_box span {
            display: inline-block;
            width: 4px;
            height: 18px;
            margin-bottom: -1px;
            border-radius: 50px;
            background: #ff5200;
            margin-right: 8px;
            margin-left: 16px;
        }

        .video_box p {
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 12px;
            line-height: 49px;
            margin-bottom: 0 !important;
        }

        .video_box p a {
            width: 95%;
            color: #000000;
            margin-left: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-decoration: none;
            display: inline-block;
            margin-right: 12px;
        }
        .schoolIco{ width: 76px;height: 20px;text-align: center;line-height: 20px;background: #ff6100;color: #fff !important;display: inline-block;border-radius: 4px; margin-right: 20px;}
        .unstyled{ height: 141px; overflow: hidden; margin-top: 3px}
        .grg a{ width: 250px;
            line-height: 49px;
            padding: 0;
            margin-bottom: 24px;
            height: 49px;
            background: #FFF7EE;
            border: 1px solid #EAEAEA;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #FF5200;display: block; text-align: center;}
        .grg a:hover{ background-color: #FFF7EE; text-decoration: none; color: #FF5200;}
        .gah{     font-size: 12px;
            color: #5097FF;
            cursor: pointer;
            user-select: none;
            position: absolute;
            bottom: -2px;
            right: 24px;}
        .video_box p{height: 49px;}
        .video_box p a{width: 95%;overflow: hidden;text-overflow:ellipsis; white-space: nowrap;margin-bottom: 0 !important;}
        .recommend a img {
            width:250px;
            height: 140px;
        }
        .zgs{ line-height: 24px !important;}
        #txt_Password{ border-top: 0 !important;}
        .checkagreeInput{ height: 24px;}
        .container{ margin: auto;}
        .modal-backdrop{ background-color: rgba(0,0,0,0.7) !important;}

        .tc_full {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 999999;
            display: none;
        }
    </style>
    <script type="text/javascript">
        $(function () {
            var url = window.location.href
            if (url.indexOf("keys=") != "-1") {
                url = url.substring(url.indexOf("keys=") + 5, url.lenght);
                $("#txtkeys").val(decodeURI(url));
                $(".warning i").html(decodeURI(url))
                var word = decodeURI(url)
                $(".news p").each(function () {
                    $(this).find("a").html($(this).find("a").html().replace(word, "<s>" + word + "</s>"))
                })
            }
        })
    </script>

    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
</head>

<body class="w1210" style="background-color: #fff;">
<form name="Form1" method="post" action="" id="Form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=5,listType=0"></div>
    <div id="myCarousel" class="carousel slide thzq_gg" th:if="${!#strings.isEmpty(categoryId)}">
        <div class="swiper-container">
            <div class="swiper-wrapper" th:if="${!#lists.isEmpty(up)}">
                <div class="swiper-slide" th:each="up,upc: ${up}">
                    <a th:href="${#strings.isEmpty(up.TargetUrl)?  '':up.TargetUrl }"
                       th:style="'background: url('+${#strings.isEmpty(up.AdFilePath)?'https://images.fangxiaoer.com/sy/esf/fy/big/noimage375275.jpg': up.AdFilePath}+') no-repeat center;'"
                       target="_blank">
                        <div><span><th:block th:text="${up.AdTitle}"></th:block> </span></div>
                    </a>
                </div>
            </div>
            <th:block th:if="${#lists.size(up) gt 1}">
                <div class="swiper-pagination1"></div>
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </th:block>
        </div>
        <th:block th:if="${#lists.size(up) gt 1}">
            <script>
                var mySwiper = new Swiper('.swiper-container', {
                    autoplay: 5000,//可选选项，自动滑动
                    pagination: '.swiper-pagination1',
                    // pagination: {
                    //     el: '.swiper-pagination1',
                    //     clickable: true,
                    // },
                    paginationClickable: '.swiper-pagination1',
                    prevButton: '.swiper-button-prev',
                    nextButton: '.swiper-button-next',
                    loop: true,
                    clickable:true,

                })
            </script>
        </th:block>
    </div>

    <div class="container">
        <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt;
            <a href="/news/" target="_blank">资讯</a>
            <span id="xiaoyu"></span>
            <th:block th:if="${!#strings.isEmpty(categoryId)}">&gt;</th:block>
            <a th:if="${categoryId == '160'}" href="" style="cursor: pointer">热门</a>
            <a th:if="${categoryId == '149'}" href="" style="cursor: pointer">行业关注</a>
            <a th:if="${categoryId == '148'}" href="" style="cursor: pointer">置业推荐</a>
            <a th:if="${categoryId == '151'}" href="" style="cursor: pointer">楼盘要闻</a>
            <a th:if="${categoryId == '166'}" href="" style="cursor: pointer">学区资讯</a>
            <a th:if="${categoryId == '156'}" href="" style="cursor: pointer">专题推荐</a>
            <a th:if="${categoryId == '154'}" href="" style="cursor: pointer">工程进度</a>
            <a th:if="${categoryId == '158'}" href="" style="cursor: pointer">购房百科</a>

        </div>

        <div class="row">
            <!--左侧资讯信息-->

            <div class="left-w span9" id="news">
                <div class="redTab" th:if="${!#strings.isEmpty(categoryId) && categoryId != '158'}">
                    <ul>
                        <li><a href="/news/160" class="redTab1"><div style="overflow:hidden;"><i></i><span  th:class="${categoryId == '160' ? 'sel' : ''}">热门</span></div><span th:class="${categoryId == '160' ? 'redTabG' : ''}"></span></a></li>
                        <li><a href="/news/149"><span  th:class="${categoryId == '149' ? 'sel' : ''}">行业关注</span><span th:class="${categoryId == '149' ? 'redTabG' : ''}"></span></a></li>
                        <li><a href="/news/151"><span th:class="${categoryId == '151' ? 'sel' : ''}">楼盘要闻</span><span th:class="${categoryId == '151' ? 'redTabG' : ''}"></span></a></li>
                        <li><a href="/news/166"><span th:class="${categoryId == '166' ? 'sel' : ''}">学区资讯</span><span th:class="${categoryId == '166' ? 'redTabG' : ''}"></span></a></li>
<!--                        <li><a href="/news/154"><span th:class="${categoryId == '154' ? 'sel' : ''}">工程进度</span><span th:class="${categoryId == '154' ? 'redTabG' : ''}"></span></a></li>-->
<!--                        <li><a href="/news/156"><span th:class="${categoryId == '156'? 'sel' : ''}">专题推荐</span><span th:class="${categoryId == '156'? 'redTabG' : ''}"></span></a></li>-->
                    </ul>
                </div>
                <div class="border" th:if="${isRecommend}">
                    <img class="noneImg" src="https://static.fangxiaoer.com/web/images/sy/house/house/warning1.png">
                    <p class="none" th:text="${'抱歉，没有找到与&quot;'+searchKey+'&quot;相关的内容'}"></p>
                    <div class="cl"></div>
                </div>
                <p class="Recommend" th:if="${isRecommend}">为您推荐</p>
                <div class=" news" th:each="n:${news}">
                    <div>
                        <div th:unless="${categoryId == '149' and #strings.equals(n.withoutPic,1)}" class="left">
                            <span class="nBrand" th:if="${categoryId eq null}" th:text="${n.categoryName}"></span>
                            <a th:href="${'/news/'+n.id+'.htm'}" target="_blank"><img th:src="${n.pic}" width="252"
                                                                                      height="184" th:alt="${n.title}"/></a>

                        </div>
                        <div th:class="${categoryId == '149' and #strings.equals(n.withoutPic,1) ? 'noImgNews right' : 'right'}">
                            <a class="rhead" th:href="${'/news/'+n.id+'.htm'}" target="_blank"
                               th:text="${n.titleShow}"></a><br>
                            <p class="nCon">
                                <a class="content" th:href="${'/news/'+n.id+'.htm'}" target="_blank"
                                   >
                                    <span class="conSpan" th:text="${#strings.toString(n.description).length() gt 78?#strings.substring(n.description,0,78)+'...':n.description}"></span>
                                    <span class="nDet">【详情】</span>
                                </a>
<!--                                <a th:class="${#strings.toString(n.description).length() gt 39 and #strings.toString(n.description).length() lt 44 ? 'nDet detx' :'nDet'}" th:href="${'/news/'+n.id+'.htm'}" target="_blank">-->

<!--                                </a>-->
                            </p>
                            <p class="nlyun">
                            <span th:if="${categoryId == '166'}" class="schoolIco" th:text="${n.newsTypeValue}"></span>
                            <span>
<!--                            <i th:text="${n.visitedTimes}"></i>-->
                            <!--来源 : <th:block th:text="${n.source}"></th:block>    &nbsp;-->
                                <th:block
                                    th:text="${#strings.isEmpty(n.nominal)? '编辑 :  '+n.author : '编辑 :  '+n.author+'、' + n.nominal}"></th:block>  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <th:block
                                    th:text="${n.updateTime}"></th:block>
                        </span>
                            </p>
                        </div>
                        <div class="cl"></div>
                    </div>
                </div>
                <div class="page" th:unless="${isRecommend}">
                    <div id="Pager1">
                        <div th:include="fragment/page :: page"></div>
                    </div>
                </div>
            </div>

            <!--右侧帮你找房，房产快讯，最新视频-->
            <div id="right">
                <!--<div class="zsfw">
                    <h1><span></span>帮您找房</h1>
                    <ul>
                        <li>
                            <span>意向区域</span>
                            <div>
                                <select id="region">
                                    <option>沈河区</option>
                                    <option>大东区</option>
                                    <option>皇姑区</option>
                                    <option>和平区</option>
                                    <option>铁西区</option>
                                    <option>于洪区</option>
                                    <option>浑南区</option>
                                    <option>沈北新区</option>
                                    <option>苏家屯</option>
                                </select>
                            </div>
                        </li>
                        <li class="hx">
                            <span>意向户型</span>
                            <div>
                                <select id="new_huxing">
                                    <option>一居</option>
                                    <option>二居</option>
                                    <option>三居</option>
                                    <option>四居</option>
                                    <option>五居及以上</option>
                                </select>
                            </div>
                        </li>
                        <li class="yx">
                            <span>预算价格</span>
                            <div>
                                <select id="new_yusuan">
                                    <option>35万以下</option>
                                    <option>35-50万</option>
                                    <option>50-80万</option>
                                    <option>80-100万</option>
                                    <option>100-120万</option>
                                    <option>120-150万</option>
                                    <option>150万以上</option>
                                </select>
                            </div>
                        </li>
                        <li>
                            <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
                        </li>
                        <li>
                            <span>手机号码</span>
                            <input type="tel" id="phone" onkeyup="this.value=this.value.replace(/[^\d]/g,'') "
                                   onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " class="fxe_mobile"
                                   placeholder="请输入手机号" maxlength="11">
                            <input type="hidden" id="type" value="1">
                        </li>
                        <li>
                            <span>验证码</span>
                            <input type="tel" id="code" class="fxe_messageCode" maxlength="6" style="width: 120px;"
                                   placeholder="请输入验证码"/>
                            <p id="hqyzm" class="fxe_ReSendValidateCoad">获取验证码</p>
                            <p class="fxe_validateCode"></p>
                        </li>
                        <b class="btn" id="new_submit">提交</b>
                        <div class="checkagreeInput" style="margin: 0 auto 10px auto;">
                            <div style="font-size:12px;">提交即代表同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                        </div>
                    </ul>
                </div>
                &lt;!&ndash;帮您找房&ndash;&gt;
                <div th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}">
                    <div id="newsAD1_guanggao" class="flash">&lt;!&ndash;广告&ndash;&gt;
                        <ul class="rotaion_list">
                            <li th:each="advert1:${advert1}">
                                <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                                <a th:href="${advert1.TargetUrl}" target="_blank">
                                    <img th:src="${advert1.AdFilePath}" alt=""></a>
                            </li>
                        </ul>
                    </div>
                </div>-->

                <dl class="recommend" style=" margin-top: 0;">
                    <dt style="position: relative"><span></span>房产快讯</dt>
<!--                    <div class="title">房产快讯</div>-->
                    <!--房产快讯-->
                    <div class="hot" th:include="fragment/fragment::news_flash"></div>
                </dl>

                <div class="cl"></div>
                <div class="grg">
                    <a th:href="@{'/helpSearch?ids=1'}" rel="2" target="_blank">我要买房</a>
                    <a href="/static/saleHouse/saleHouse.htm" target="_blank" th:class=" (${firstNavIndex} == 8)? 'hover' : '' ">我要卖房</a>
                </div>

                <div th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}" style="margin-bottom: 24px;">
                    <div id="newsAD1_guanggao" class="flash">
                        <ul class="rotaion_list">
                            <li th:each="advert1:${advert1}">
                                <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                                <a th:href="${advert1.TargetUrl}" target="_blank">
                                    <img th:src="${advert1.AdFilePath}" alt=""></a>
                            </li>
                        </ul>
                    </div>
                </div>

                <!--                &lt;!&ndash;楼盘视频标题&ndash;&gt;
                                <div class="title">楼盘视频</div>-->
                <!--楼盘视频信息-->
                <div class="video_box">
                    <div class="title" style="position: relative;">
                        <span></span>最新视频<em class="gah" onclick="window.open('/videos')">更多>></em>
                    </div>
                    <div class="recommend" style="border:none;" th:each="v:${video.content}">
                        <a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank">
<!--                            <i></i>-->
                            <img th:src="${v.videoPic}" th:alt="${v.videoTitle}"/>
                        </a>
                        <p>
                            <a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank" th:text="${v.videoTitle}"></a>
                        </p>
                    </div>
                </div>
                <div th:if="${advert2 ne null and #lists.toList(advert2).size() ne 0}">
                    <div id="newsAD2_guanggao" class="flash"><!--广告-->
                        <ul class="rotaion_list">
                            <li th:each="advert2:${advert2}">
                                <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                                <a th:href="${advert2.TargetUrl}" target="_blank">
                                    <img th:src="${advert2.AdFilePath}" alt=""></a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="cl"></div>
                <div class="cl"></div>
            </div>
        </div>
    </div>
    <div class="cl"></div>

    <!--<div th:include="fragment/bottom_secondhouse :: bottom"></div>-->
    <!--分页-->
<!--    <div class="cl"></div>-->
<!--    <div class="page" th:unless="${isRecommend}">-->
<!--        <div id="Pager1">-->
<!--            <div th:include="fragment/page :: page"></div>-->
<!--        </div>-->
<!--    </div>-->

    <!--专题页链接区-->
    <!--<div th:include="fragment/fragment:: activity_page"></div>-->
    <div class="cl"></div>
    <div class="cl"></div>
    <!--底部1-->
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div class="cl"></div>
    <script type="text/javascript">
        //<![CDATA[
        var theForm = document.forms['Form1'];
        if (!theForm) {
            theForm = document.Form1;
        }

        function __doPostBack(eventTarget, eventArgument) {
            if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                theForm.__EVENTTARGET.value = eventTarget;
                theForm.__EVENTARGUMENT.value = eventArgument;
                theForm.submit();
            }
        }


        //]]>
    </script>

</form>


<!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/tool/login_tc.css">-->
<script type="text/javascript" src="https://static.fangxiaoer.com/js/fxe_confirm.js"></script>


<!--<div class="modal" id="login">
    <input type="hidden" id="LoginUrl" value=""/>
    <a class="close" data-dismiss="modal" id="loginClose">×</a>
    <h1>账户登录</h1>
    <div class="signup-form clearfix">
        <p>登录名：</p>
        <input autocomplete=off name="Txt_LoginName" id="Txt_LoginName" value="" class="fxe_mobile"
               placeholder="请输入手机号">
        <p>登录密码：</p>
        <input autocomplete="new-password" name="Txt_Password" id="Txt_Password" class="fxe_password" type="password"
               placeholder="请输入密码">
        <p style="color: red;margin-bottom:10px" id="error_info"><i class="ic_wrong error_info"></i></p>
        <input type="button" onclick="submitForm();return false;" name="type" class="button-blue reg" value="登录"
               data-action="regist">
        <p style="text-align:right"><a href="https://my.fangxiaoer.com/register.aspx" target="_blank">免费注册</a>　<a
                href="https://my.fangxiaoer.com/RetrPassword.aspx" target="_blank">忘记密码</a></p>
        <div class="clearfix"></div>
        <a href="" class="various" target="_blank" id="althref"></a>
    </div>
</div>-->
<!--<div class="modal" id="login"></div>-->
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div class="tc_full"></div>
<script th:inline="javascript">
    $(document).ready(function(){
        var sessionId = [[${session.muser}]];
        $(".talk_btn_box").click(function(){
            if(sessionId==null){
                $("#login,.modal-backdrop").show()
            }
        })

        //登录关闭X
        // $("#loginClose").click(function () {
        $(document).on('click','#loginClose',function () {
            $("#login,.modal").hide();
            $(".tc_full").hide()
            $(".modal-backdrop").remove()
        });

        $(".talk_btn").click(function(){
            setTimeout(function(){
                $("#login").show()
                $(".tc_full").show()
            },500)


            setTimeout(function(){
                $(".modal-backdrop").remove()
            },7)
        })

        $(".checkagreeInput input").attr('checked',false)
    })

</script>

<script type="text/javascript" src="https://static.fangxiaoer.com/js/modal.js"></script>
<script>
    $(".cheimg5").click(function () {
        if($(this).hasClass("checked")){
            $(this).removeClass("checked")
        }else{
            $(this).addClass("checked")
        }
    })
    /*function submitForm() {
        function $Arg(id) {
            return document.getElementById(id);
        }

        var r = 0;
        if (fxeTime.mobile()) {
            if (fxeTime.password()) {
                if (confirm.mobile()) {
                    $.ajax({
                        type: "POST",
                        data: {number: $Arg("Txt_LoginName").value, passowrd: $Arg("Txt_Password").value},
                        url: "/action/userlogin.ashx",
                        async: false,
                        success: function (data) {
                            r = data;
                        }
                    });
                } else {
                    r = 0
                    $(".error_info").text(hint[0]);
                }
            }
        }
        if (r == 1) {
            if ($("#LoginUrl").val() != "" && IsCommentList == 1) {
                window.location.href = $("#LoginUrl").val();
            } else {
                window.location.reload();
            }
            //  var url = '';
            //  var adiog = '';
            // if (adiog == "1") {
            //  $("#althref").trigger("click");
            //  $("#login").modal("hide");
            //  } else {
            // if (url == "") {
            //console.log(window.location.href = window.location.href)
            //   console.log("333333")
            //window.location.reload();
            //window.location.href = window.location.href;
            //  } else {
            //location.href = url;
            //     console.log("444444")
            //  }
            //  }
        } else {
            r = 0
            $(".error_info").text(hint[0]);
        }
    }*/

</script>
<script type="text/javascript">
    /*$(document).ready(function () {
        $("a.forgot").click(function () {
            $("#login-modal").modal("hide");
            $("#forgetform").modal({show: !0});
        });
        $("#login").modal("hide");
    });
    $(document).keypress(function (e) {
        // 回车键事件  
        if (e.which == 13) {
            submitForm();
            return false;
        }
    });*/
</script>

</body>
</html>

