<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title><th:block th:text="${newsinfo.title+'_'+'市场观点'+'_房产资讯 - 房小二网'}"></th:block> </title>
    <meta name="keywords" th:content="${newsinfo.keyWords}" />
    <meta name="description" th:content="${newsinfo.description}" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/news/'+id+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy//news/view1.css?t=20190104" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/author.js"></script>
    <style>
        .bg{ display: none;}
        .paying{ display: none;}
        #goLogin{ display: none!important;}
    </style>
    <script type="text/javascript" >
        function url() {
            $(".bg,.paying").show();
            window.open("https://sy.fangxiaoer.com/create_direct_pay_by_user-CSHARP-UTF-8/VIpOrderPayInfo.aspx?zxbz=" + location.href);
        }
        function Dengl() {
            location.href = "#login";
            window.open("https://my.fangxiaoer.com/Login.aspx");
        }
        function Close() {
            window.location.reload();
        }
    </script>
    <style>
        .fxe-alert {position: fixed;top: 48%;width:20%;margin: 0 35%;background: url("https://static.fangxiaoer.com/global/imgs/ico/b60.png");color: #fff;font-size: 11pt;text-align: center;border-radius: 6px;padding: 10px;z-index: 1000000;}
    </style>
    <script src="/js/recommendhouse_code.js"></script>
    <!--<script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>-->
    <script src="/js/AjaxforJquery.js"></script>
</head>

<body class="w1210">
<form name="form1" method="post" action="63258" id="form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017"  th:include="fragment/fragment::searchNav" th:with="type=5"></div>
    <input type="hidden" th:value="${id}" id="newsId"/>
    <div class="crumbs">您的位置：
        <a href="/">沈阳房产网</a> &gt;
        <a href="/news/" target="_blank">资讯</a>&gt;
        <a th:href="${'/agentnews/999'}">市场观点</a>
    </div>
    <a data-toggle="modal" href="#login" id="goLogin">已有会员，去登录</a>

    <div class="w windows">
        <div class="left">
            <div class="left-tit">
                <p>
                    <th:block th:text="${newsinfo.title}"></th:block><br>
                    <span>
                        <i><th:block th:text="${newsinfo.visitedTimes}"></th:block></i>
                        来源： <th:block th:text="${newsinfo.source}"> </th:block>  |  编辑： <th:block th:text="${newsinfo.author}"></th:block>  |  <th:block th:text="${newsinfo.auditTime}"> </th:block>  |
                        <span class="bdsharebuttonbox"><a href="javascript:void(0);" class="bds_more" data-cmd="more" style="float: none; font-size: 14px; color: #aab2bd; background: url(https://static.fangxiaoer.com/web/images/sy/sale/fenxiang.gif) 0px 6px no-repeat;" th:onclick="'javascript:sharedetailclick(\'https://m.fangxiaoer.com/secondNews/'+${id} +'.htm\')'">分享</a></span>
                        <script src="/js/share.js"></script>
                        <script src="/js/personal_share.js"></script>
                    </span>
                </p>
            </div>
            <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/tool/login_tc.css">
            <script type="text/javascript" src="https://static.fangxiaoer.com/js/fxe_confirm.js"></script>
            <div class="modal" id="login">
                <input type="hidden" id="LoginUrl" value=""/>
                <a class="close" data-dismiss="modal" id="loginClose">×</a>
                <h1>账户登录</h1>
                <div  class="signup-form clearfix"  >
                    <p>登录名：</p>
                    <input autocomplete=off name="Txt_LoginName" id="Txt_LoginName" value="" class="fxe_mobile" placeholder="请输入手机号">
                    <p>登录密码：</p>
                    <input autocomplete="new-password" name="Txt_Password" id="Txt_Password" class="fxe_password" type="password" placeholder="请输入密码">
                    <p style="color: red;margin-bottom:10px" id="error_info"><i class="ic_wrong error_info"></i></p>
                    <input type="button" onclick="submitForm();return false;" name="type" class="button-blue reg" value="登录" data-action="regist" >
                    <p style="text-align:right"><a href="https://my.fangxiaoer.com/register.aspx" target="_blank">免费注册</a>　<a href="https://my.fangxiaoer.com/RetrPassword.aspx" target="_blank">忘记密码</a></p>
                    <div class="clearfix"></div>
                    <a href="" class="various" target="_blank"  id="althref" ></a>
                </div>
            </div>
            <script type="text/javascript" src="https://static.fangxiaoer.com/js/modal.js"></script>
            <script>
                function submitForm() {
                    function $Arg(id) { return document.getElementById(id); }
                    var r = 0;
                    if (fxeTime.mobile()) {
                        if (fxeTime.password()) {
                            if (confirm.mobile()) {
                                $.ajax({
                                    type: "POST",
                                    data: { number: $Arg("Txt_LoginName").value, passowrd: $Arg("Txt_Password").value },
                                    url: "/action/userlogin.ashx",
                                    async: false,
                                    success: function (data) {
                                        r = data;
                                    }
                                });
                            } else {
                                r=0
                                $(".error_info").text(hint[0]);
                            }
                        }
                    }
                    if (r == 1) {
                        if ($("#LoginUrl").val() != "" && IsCommentList == 1) {
                            window.location.href = $("#LoginUrl").val();
                        } else {
                            window.location.reload();
                        }
                        //  var url = '';
                        //  var adiog = '';
                        // if (adiog == "1") {
                        //  $("#althref").trigger("click");
                        //  $("#login").modal("hide");
                        //  } else {
                        // if (url == "") {
                        //console.log(window.location.href = window.location.href)
                        //   console.log("333333")
                        //window.location.reload();
                        //window.location.href = window.location.href;
                        //  } else {
                        //location.href = url;
                        //     console.log("444444")
                        //  }
                        //  }
                    } else {
                        r = 0
                        $(".error_info").text(hint[0]);
                    }
                }
            </script>
            <script type="text/javascript">
                $(document).ready(function () {
                    $("a.forgot").click(function () {
                        $("#login-modal").modal("hide");
                        $("#forgetform").modal({ show: !0 });
                    });
                    $("#login").modal("hide");
                });
                $(document).keypress(function (e) {
                    // 回车键事件
                    if (e.which == 13) {
                        submitForm();
                        return false;
                    }
                });
            </script>


            <!--新闻内容 begin-->
            <div class="view_txt">
                <div id="Pal_des">

                    <div class="news_text_yinyan hid">
                        <th:block th:text="${#strings.toString(newsinfo.Description).replaceAll('&nbsp',' ')}"></th:block>
                    </div>

                </div>
                <th:block th:utext="${#strings.toString(newsinfo.Content).replaceAll('&nbsp',' ')}"></th:block>
            </div>
            <!--新闻内容 end-->

            <div id="NewsPager" class="paginator" style="display: block"></div>
        </div>

        <div class="right">
            <input type="hidden" id="author" th:value="${newsinfo.author}"/>
            <input type="hidden" id="title" th:value="${newsinfo.title}"/>
            <script type="text/javascript">
                sy_confirm.init(1, true);
                $(".fxe_ReSendValidateCoad").click(function () {
                    if (sy_confirm.phone($("#phone").val()) != true) {
                    } else {
                        console.log('新验证码验证成功！');
                        sy_confirm.Code($("#phone").val()).then(res => {
                            console.log('发送新验证码验证成功！');
                            console.log(res);
                            if (res == 1) {
                              sy_confirm.timeWait();
                            }
                        }).catch(err => {
                            console.log('发送新验证码验证失败！');
                            console.log(err)
                        })
                    }
                })
                $(".zsfw ul .btn").click(function () {
                    if (sy_confirm.phone($("#phone").val()) != true) {
                    } else if (sy_confirm.code($("#code").val()) != true) {
                    }
//                    else if (sy_confirm.confirmCode($("#phone").val(), $("#code").val()) != true) {
//                    }
                    else {
                        console.log("意向区域：" + $("#yxqy").val() + "       手机号：" + $("#phone").val() + "       验证码：" + $("#code").val() + "        预算价格：" + $("#ysjg").val() + "       户型：" + $("#yxhx").val() + "      描述：" + $("#miaoshu").val())

                        var tphone = $.trim($("#phone").val());//电话
                        var yusuang = "无"//预算
                        var quyu = $("#yxqy").val();//区域
                        var jusi = $("#yxhx").val();//户型
                        var miaos = $.trim($("#miaoshu").val()) + "       此信息来自sy站资讯页。编辑："+$("#author").val()+"。标题："+$("#title").val();//描述

                        //    fangxiaoer.ajax("upguide", "{tel:'" + tphone + "',jusi:'" + jusi + "',type:' two',miaos:'" + miaos + "',quyu:'" + quyu + "',yusuang:'" + yusuang + "',stype:1'}", function (data) {
                        var params = {type: 1,
                            phone: tphone,
                            code: $("#code").val(),
                            region: quyu,
                            area:jusi,
                            budget:yusuang,
                            italy: miaos}
                        fangxiaoer.ajax("upguide", params, function (data) {
                            if (data.status == 1) {
                                //重置
                                $("#phone").val("");
                                $("#code").val("");
                                $("#miaoshu").val("");
                                fxe_alert("亲，您的需求已提交成功，工作人员会及时跟您联系，请耐心等待！")
                            }else {
                                fxe_alert("验证码错误")
                            }
                        });
                    }
                })
            </script>
            <div id="newsAD1_guanggao" class="flash"  th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}"><!--广告-->
                <ul class="rotaion_list">
                    <li th:each="advert1:${advert1}">
                        <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                        <a th:href="${advert1.TargetUrl}" target="_blank">
                            <img th:src="${advert1.AdFilePath}" alt="">
                        </a>
                    </li>
                </ul>
            </div>
            <div class="title">房产快讯</div>
            <!--房产快讯-->
            <div class="hot" th:include="fragment/fragment::news_flash"></div>

            <div class="title ">最新视频</div>
            <div class="video ">
                <div class="shipin " th:each="v:${video}">
                    <a th:href="'/video/'+${v.videoId}+'.htm'" target="_blank">
                        <img th:src="${v.videoPic}" width='286' height='156' alt='推荐视频' /><i><img  src='https://static.fangxiaoer.com/web/images/ico/sign/play.png' alt='开始' /></i>
                    </a>
                    <p><a th:href="'/video/'+${v.videoId}+'.htm'" target="_blank"><th:block th:text="${v.videoTitle}"></th:block></a></p>
                </div>
            </div>

            <div id="newsAD2_guanggao" class="flash"  th:if="${advert2 ne null and #lists.toList(advert2).size() ne 0}"><!--广告-->
                <ul class="rotaion_list">

                    <li th:each="advert2:${advert2}">
                        <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                        <a th:href="${advert2.TargetUrl}" target="_blank">
                            <img th:src="${advert2.AdFilePath}" alt="">
                        </a>
                    </li>

                </ul>
            </div>

        </div>
        <!--专题页链接区-->
        <!--<div th:include="fragment/fragment:: activity_page"></div>-->
        <div class="cl"></div>

    </div>

    <div class="cl"></div>
    <!--底部2-->
    <div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
    </div>

    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>
</form>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/search_xiala.js"></script>
<!--搜索下拉-->
<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.sly.js"></script>
<script>
    $(function () {
        // $("#goLogin").click();
        var one = "2017/09/06/59af51ec70fb5.jpg";//头像
        var two = "";//电话
        var three = "专业房产探盘编辑，熟悉沈阳楼盘情况，用专业的知识，为您推荐最优的新房，解决您的购房烦恼。";//描述

        var t = 0;

        //if (t == "0") {
        //    $(".bottom_info").css({ "height": "50px", "borderTop": "none", "paddingTop": "0px" })
        //}

    })
    function showBaiduFenxiang() {
        //分享点击后 划入的框框小时1秒

        $('.bds_more').click(function () {
            $(".bdshare_popup_box").hide()
            $(".bdshare_popup_box").css("opacity", "0");
        });
        $('.bdshare_popup_bottom').click(function () {
            $(".bdshare_popup_box").hide()
            $(".bdshare_popup_box").css("opacity", "0");
        });
        $('.bdshare_dialog_close').click(function () {
            $(".bdshare_popup_box").css("opacity", "1");
        });
    }
    window.onload = showBaiduFenxiang()
    setTimeout("showBaiduFenxiang()",2000)
    setTimeout("showBaiduFenxiang()", 4000)
    setTimeout("showBaiduFenxiang()", 8000)
</script>
</body>
</html>
