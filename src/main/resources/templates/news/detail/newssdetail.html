<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title><th:block th:text="${newsinfo.title+'_'+newsinfo.categoryName+'_房产资讯 - 房小二网'}"></th:block> </title>
    <meta name="keywords" th:content="${newsinfo.keyWords}" />
    <meta name="description" th:content="${newsinfo.description}" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/news/'+id+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
<!--    <link href="https://static.fangxiaoer.com/web/styles/sy/news/view1.css?v=20201031" rel="stylesheet" type="text/css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/maifang_right/view1.css?v=20201031" rel="stylesheet" type="text/css" />
    <!--<link href="/css/view1.css" rel="stylesheet" type="text/css" />-->

    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="//static.fangxiaoer.com/js/new/my_collect.js"></script>
    <script src="/js/AjaxforJquery.js"></script>
    <script src="/js/share.js"></script>
    <script src="/js/personal_share.js"></script>
    <style>
        .bg{ display: none;}
        .bg{ display: none;}
        .paying{ display: none;}
        #goLogin{ display: none!important;}
        .video_box {border: 1px solid #eaeaea;margin-bottom:24px;}
        .video_box:hover a{color:#ff5200;}
        .video_box .recommend{margin-top:0 !important;margin-bottom:0 !important;}
        .video_box .title {line-height:48px;font-size:18px;}
        .video_box span {display: inline-block;width:4px;height:18px;margin-bottom:-1px;border-radius:50px;background:#ff5200;margin-right:8px;margin-left:16px;}
        .video_box p {font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;margin-right:12px;line-height:49px;margin-bottom:0 !important;}
        .video_box p a {width:95%;color:#000000;margin-left:12px;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;text-decoration:none;display:inline-block;margin-right:12px;}
        .recommend a img {
            width: 100%;
            display: block;
        }
        .fxe-alert {position: fixed;top: 48%;width:20%;margin: 0 35%;background: url("https://static.fangxiaoer.com/global/imgs/ico/b60.png");color: #fff;font-size: 11pt;text-align: center;border-radius: 6px;padding: 10px;z-index: 1000000;}
        body{background: #f5f5f5}
        .comm_snap, .comm_snap1,.comm_reply{background: none}
        .gah { font-size: 12px; color: #5097FF; cursor: pointer; user-select: none; position: absolute; bottom: -2px; right: 24px; }
        .dhd { width: 95%; color: #000000; margin-left: 12px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; text-decoration: none; display: inline-block; margin-right: 12px; }
        .dhd:hover{ color: #ff5200;}
        .Detail_txt p img{
            max-width: 650px;
            margin: 0 auto;
            display: block;
            margin-bottom: 15px;
            margin-top: 20px;
        }
        .Detail_txt p{}
        .Detail_txt h2 {
            font-size: 14px;
            line-height: 18px;
            margin-bottom: 10px;
        }
        .Detail_txt h1 {
            font-size: 18px;
            line-height: 24px;
            margin-bottom: 10px;
        }
        .Detail_txt video {margin-bottom: 10px;}
    </style>
</head>
<body class="w1210" style="background-color: #fff;">
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>

<th:block th:if="${newsltm.categoryId != '159'}">
    <!--搜索栏-->
    <div id="search2017" th:if="${#strings.toString(newsltm.categoryName) ne '小二活动'}" th:include="fragment/fragment::searchNav" th:with="type=5"></div>
</th:block>
<input type="hidden" th:value="${id}" id="newsId"/>
<input type="hidden" value="6" id="collectType"/>
<input type="hidden" id="glzs" th:value="${#session?.getAttribute('sessionId')}" />
<div class="crumbs">您的位置：
    <a href="/">沈阳房产网</a> &gt;
    <a href="/news/" target="_blank">资讯</a>&gt;
    <th:block th:if="${newsltm.categoryId == '159'}">
        <a th:href="${'/agentnews/'+newsltm.categoryId}"><th:block th:text="${newsinfo.categoryName}"></th:block></a>
    </th:block>
    <th:block th:if="${newsltm.categoryId != '159'}">
        <a th:href="${'/news/'+newsltm.categoryId}"><th:block th:text="${newsinfo.categoryName}"></th:block></a>
    </th:block>
</div>
<a data-toggle="modal" href="#login" id="goLogin">已有会员，去登录</a>
<div class="w articleDetail">
    <div class="left">
        <div class="whiteBg">
            <!--新闻标题+作者-->
            <div class="DetailHead">
                <h4 class="DetailH4" th:text="${newsinfo.title}"></h4>
                <div class="DetailNews">
                    <div class="DetailAuthor">

                        <div class="authorImg"><img th:src="${newsinfo.authPic}" alt=""></div>
                        <div class="authorMsg">
                            <h5> <th:block th:text="${#strings.isEmpty(newsinfo.nominal)? newsinfo.author : newsinfo.author+'、' + newsinfo.nominal}"></th:block></h5>
                            <p> <th:block th:text="${newsinfo.updateTime}"></th:block>&nbsp;&nbsp;&nbsp;<th:block th:text="${newsinfo.source}"></th:block></p>
                        </div>
                    </div>
                    <div class="DetailNum">
                        <a href="javascript:void(0);" id="DetailShare" class="bds_more" data-cmd="more" th:onclick="'javascript:sharedetailclick(\'https://m.fangxiaoer.com/news/'+${id} +'.htm\')'"><i class="DetailShareIco"></i>分享</a>
                        <div th:if="${newsltm.categoryId != '159'}" id="DetailCollect"><a class="soucang"  target="_blank" data-toggle="modal" href="#login"><i id="DetailCollectIco"></i>收藏</a></div>
                        <div class="visitedTimes">
                            <i class="visitedTimesIco"></i>
                            <span th:text="${newsinfo.visitedTimes}"></span>
                        </div>
                    </div>
                </div>
            </div>
            <!--新闻内容 begin-->
            <div class="Detail_txt">
                <div id="Pal_des">
                    <div class="news_text_yinyan hid">
                        <th:block th:text="${#strings.toString(newsinfo.description).replaceAll('&nbsp',' ')}"></th:block>
                    </div>
                </div>
                <th:block th:if="${videoPath eq null}" th:utext="${#strings.toString(newsinfo.content).replaceAll('&nbsp',' ')}"></th:block>
                <th:block th:if="${videoPath ne null}">
                    <link rel="stylesheet" href="https://g.alicdn.com/de/prismplayer/2.6.0/skins/default/aliplayer-min.css" />
                    <script type="text/javascript" src="https://g.alicdn.com/de/prismplayer/2.6.0/aliplayer-min.js"></script>
                    <input type="hidden" id="videoPath" th:value="${videoPath}">
                    <input type="hidden" id="pic" th:value="${newsinfo.pic}">
                    <div class="prism-player" id="J_prismPlayer"></div>
                    <script>
                        $(document).ready(function () {
                            var player = new Aliplayer({
                                id: 'J_prismPlayer',
                                autoplay: false,
                                isLive:true,
                                //支持播放地址播放,此播放优先级最高
                                source: $("#videoPath").val(),
                                cover: $("#pic").val(),
                                useH5Prism:true,
                                playsinline:true,
                                x5_type:'h5',
                            }, function (player) {
                                player.on("ended", endedHandle);
                                console.log('播放器创建好了。')
                            })
                            function endedHandle() {
                                player.dispose(); //销毁
                                $('#J_prismPlayer').empty();
                                //重新创建
                                player = new Aliplayer({
                                    id: 'J_prismPlayer',
                                    autoplay: false,
                                    //支持播放地址播放,此播放优先级最高
                                    source: $("#videoPath").val(),
                                    cover: $("#pic").val(),
                                });
                            };
                        });
                    </script>
                </th:block>
            </div>
            <!--新闻内容 end-->
            <div id="NewsPager" class="paginator" style="display: block"></div>
            <div><span class="noticeclaim" th:text="${newsinfo.claimNotice}"></span></div>
            <style>
                .noticeclaim{
                    width: 795px;
                    display: block;
                    margin: 0 auto;
                    color: #999;
                    padding-bottom: 20px;
                }
            </style>
        </div>
        <!--资讯推荐开始-->
        <div class="infoRecommend" th:if="${#lists.size(pushNews) > 0}">
            <h4 class="DetailTitle">资讯推荐</h4>
            <ul class="infoUl">
                <li class="infoLi" th:each="newsif:${pushNews}">
                    <a th:href="'/news/'+${newsif.id}+'.htm'" target="_blank"><th:block th:text="${newsif.title}">></th:block></a>
                    <span class="infoSpan" th:text="${#strings.toString(newsif.updateTime).substring(0,#strings.toString(newsif.updateTime).length()-8)}"></span>
                </li>
            </ul>
        </div>
        <!--资讯推荐结束-->

        <!--点赞 snap-uuip1-->
        <div class="snap">
            <a data-toggle="modal" href="#login" class="forLike">
               <!-- <div th:class="${newsinfo.like eq 1 ? 'snap-uuip snap-uuip1' : 'snap-uuip'}" th:text="${newsinfo.totalLike eq 0 ? '点赞': newsinfo.totalLike}"></div>-->
                <div class="snap-uuip"></div>
            </a>
       </div>
        <!--报名入口-->
        <!--<div class="enroll_rk" th:if="${newsinfo.forEvent ne null and newsinfo.forEvent eq 1}">
            <div class="comment_title">
                <i></i>
                <p>报名入口</p>
            </div>
            <div class="enroll_kj">
                <div class="input enroll_name">
                    <span>姓&nbsp;&nbsp;&nbsp;名</span>
                    <input type="text" id="name1">
                    <input type="hidden" id="eventKey" th:value="${newsinfo.eventKey}">
                </div>
                <div class="input enroll_phone">
                    <span>手机号</span>
                    <input type="text" id="phone1" maxlength="11">
                </div>
                <div class="input enroll_code">
                    <span>验证码</span>
                    <div class="enroll_uupio">
                        <input type="text" id="code1">
                        <div class="enro_code fxe_ReSendValidateCoad">获取验证码</div>
                        <div class="enro_code fxe_validateCode"></div>
                    </div>
                </div>
                <div class="checkagreeInput" style="width: 537px;margin: 0 auto;margin-bottom: 10px;">
                    <div style="font-size: 12px;">提交即代表同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                    <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                </div>
                <button id="enrolrk">提 交</button>
            </div>
            <script type="text/javascript">
                $(function () {
                    $(".enro_code").click(function () {
                        var name1 = $("#name1").val();
                        var phone1 = $("#phone1").val();
                        if(name1 == null || name1 == ''){
                            fxe_alert('请填写姓名');
                            return;
                        }else if(phone1 == null || phone1 == ''){
                            fxe_alert('请输入电话');
                            return;
                        }else if(phone1.length != 11){
                            fxe_alert('请输入正确电话');
                            return;
                        }else {
                            fxeTime.timeWait();
                        }
                    })
                    $("#enrolrk").click(function () {
                        var name1 = $("#name1").val();
                        var phone1 = $("#phone1").val();
                        var code1= $("#code1").val();
                        if(name1 == null || name1 == ''){
                            fxe_alert('请填写姓名');
                            return;
                        }
                        if(phone1 == null || phone1 == ''){
                            fxe_alert('请输入电话');
                            return;
                        }
                        if(phone1.length != 11){
                            fxe_alert('请输入正确电话');
                            return;
                        }
                        if(code1 == null || code1 == ''){
                            fxe_alert('请输入验证码');
                            return;
                        }
                        var eventKey = $("#eventKey").val();
                        $.ajax({
                            type: "POST",
                            data: { region:name1,phone: phone1,code:code1,type:10, budget: eventKey, area: eventKey, italy: eventKey},
                            url: "/helpSearch",
                            success: function (data) {
                                if(data== 1){
                                    fxe_alert("提交成功");
                                    $("#name1").val("");
                                    $("#phone1").val("");
                                    $("#code1").val("");
                                }else if(data == -1){
                                    fxe_alert("登录超时，提交失败");
                                }else{
                                    fxe_alert('提交失败');
                                    return;
                                }
                            }
                        });

                    });

                });
            </script>
        </div>
       -->
        <!--发布评论-->
        <div class="comment">
            <th:block th:if="${newsinfo.closeDiscuss eq 0}">
            <div class="comment_title">
                <i></i>
                <p>相关评论</p>
            </div>
            <div class="xiangxi">
                <textarea name="virtues" type="text" id="virtues" maxlength="120" placeholder="请输入评论内容"></textarea>
                <div class="limit">
                    <div class="bottom">
                        <p>已输入<i><s id="zi">0</s></i>/120字(至少10个字)</p>
                        <div id="submit">发表评论</div>
                    </div>
                </div>
            </div>
            </th:block>
        </div>
        <!--评论显示-->
        <!--<div class="comments_show comments_List" >
            <div class="title1">全部评论(<span></span>)</div>
            <div class="comms_show">
                <ul class="comm_ul1">
                    <li class="li" th:each="one,i:${newsinfo.newsComments}" th:style="${i.count le 3 ? 'display:block':'display:none;'}">
                        <div class="left"><img th:src="${one.memberPic eq null ? 'https://static.fangxiaoer.com/m/static/images/house/noagent.png':one.memberPic}"/></div>
                        <div class="right1">
                            <div class="comm_content">
                                <h2 th:text="${(one.customerName eq '' ? one.memberName : one.customerName) + ':'}"></h2>
                                <p th:text="${one.commentDesc}"></p>
                            </div>
                            <div class="comm_time">
                                <span th:text="${one.addTime}"></span>
                                <div class="comm_reply" data-toggle="modal" href="#login"><i></i>回复(<span th:text="${one.replyCount}"></span>)</div>
                                &lt;!&ndash;已赞 comm_snap1&ndash;&gt;
                                <div th:id="${'comm_snap' + one.commentId}" th:class="${#strings.equals(one.likeOrNot, '0') ? 'comm_snap' : 'comm_snap comm_snap1'}" data-toggle="modal" href="#login" th:data-id="${one.commentId}"><i></i>赞(<span th:id="${'replyCount' + one.commentId}" th:text="${one.likeCount}"></span>)</div>
                            </div>
                            <div class="reply_box">
                                <div style="position: relative;">
                                    <textarea name="virtues" type="text" th:data-id="${one.commentId}" class="virtues1" th:id="${'virtues1_' + one.commentId}" maxlength="120" placeholder="回复"></textarea>
                                    <div class="jiao"></div>
                                </div>
                                <div class="limit">
                                    <div class="bottom">
                                        <p>已输入<i><s id="zi1" th:class="${'zi1_' + one.commentId}">0</s></i>/120字(至少10个字)</p>
                                        <button id="submit1" class="submit1" th:data-id="${one.commentId}">提交</button>
                                    </div>
                                </div>
                            </div>
                            <ul class="comm_ul2" th:if="${one.reply ne null and #lists.toList(one.reply).size() ne 0}" >
                                <li th:each="re,i:${one.reply}" th:style="${i.count le 3 ? 'display:block':'display:none;'}">
                                    <th:block th:unless="${#strings.toString(re.parentId) eq '0'}">
                                    <div style="width:96%;">
                                        <span th:text="${re.fromMemberName}"></span>
                                        <i>回复</i>
                                        <span th:text="${re.toMemberName + '：'}"></span>
                                        <span th:text="${re.replyDesc}"></span>
                                    </div>
                                    <div class="comm_time1">
                                        <span th:text="${re.addTime}"></span>
                                        <div class="comm_reply1" data-toggle="modal" href="#login"><i></i>回复</div>
                                    </div>
                                    </th:block>
                                    <th:block th:if="${#strings.toString(re.parentId) eq '0'}">
                                        <div style="width:96%;">
                                            <span th:text="${re.fromMemberName + '：'}"></span>
                                            <span th:text="${re.replyDesc}"></span>
                                        </div>

                                        <div class="comm_time1">
                                            <span th:text="${re.addTime}"></span>
                                            <div class="comm_reply1" data-toggle="modal" href="#login"><i></i>回复</div>
                                        </div>
                                    </th:block>
                                    <div class="reply_box1">
                                        <div style="position: relative;">
                                            <textarea name="virtues" type="text" th:data-id="${re.id}" th:id="${'virtues2_' + re.id}" class="virtues2" maxlength="120" placeholder="回复"></textarea>
                                            <div class="jiao"></div>
                                        </div>
                                        <div class="limit">
                                            <div class="bottom">
                                                <p>已输入<i><s id="zi2" th:class="${'zi2_' + re.id}">0</s></i>/120字(至少10个字)</p>
                                                <button id="submit2" class="submit2" th:data-id="${re.commentId + '-' + re.id}">提交</button>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <div class="view_more" th:style="${#lists.toList(one.reply).size() gt 3 ? 'display:block': 'display:none'}">查看更多</div>
                            </ul>

                        </div>
                    </li>
                    <li class="li viewmore" th:style="${#lists.toList(newsinfo.newsComments).size() gt 3 ? 'display:block': 'display:none'}"><span>查看更多</span> <i style="display: inline-flex;margin-left: 3px;"><img  src="https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png" alt="" ></i></li>

                </ul>
            </div>
        </div>-->
        <!--无评论-->
        <!--<div class="comments_hide" th:unless="${newsinfo.newsComments ne null and #lists.toList(newsinfo.newsComments).size() ne 0}">成为第一个评论的人</div>-->

        <!--新评论 --有评论显示 -->
        <div class="comments_show comments_List" id="comments_show" th:if="${newsinfo.closeDiscuss eq 0}">
            <div class="title1" id="title1">全部评论(<span></span>)</div>
            <div class="comms_show" id="comms_show">
                <ul class="comm_ul1" id="comm_ul1"></ul>
            </div>
        </div>
        <!--新评论 --无评论显示 -->
        <div th:if="${newsinfo.closeDiscuss eq 0}" class="comments_hide" id="comments_hide">成为第一个评论的人</div>



<!--小编荐房-->
        <!--<th:block th:unless="${newsinfo.forEvent ne null and newsinfo.forEvent eq 1}">
        <th:block th:if="${newsltm.categoryId != '159'}">
            <div class="bottom_info" th:if="${!#strings.isEmpty(newsinfo.des)}">
                &lt;!&ndash;小编个人信息&ndash;&gt;
                <div class="author">
                    <div class='author_l'>
                        <img th:src="${newsinfo.authPic}" /><br>
                    </div>
                    <p><i class="shuangyinhao1"></i>我是<span><th:block th:text="${newsinfo.author}"></th:block></span><th:block th:text="${'，'+newsinfo.des}"></th:block>
                        <i class="shuangyinhao2"></i></p>
                    <a href='https://download.fangxiaoer.com/' target='_blank'>
                        <img src='https://static.fangxiaoer.com/web/images/ico/sign/app_download.gif' alt='app下载' class="appxz"/>
                        <div class="guideappbg">
                            <img src="https://static.fangxiaoer.com/web/images/guideapp.png" alt="">
                            <div class="p">扫描下载APP</div>
                        </div>
                    </a>
                </div>
                <script type="text/javascript">
                    $(".appxz,.guideappbg").hover(function () {
                        $(".guideappbg").show()
                    },function(){
                        $(".guideappbg").hide()
                    });
                </script>
                &lt;!&ndash;小编荐房&ndash;&gt;
                <div class="zsfw">
                    <h1>小编荐房<span>（输入您的需求，小编为您选房）</span></h1>
                    <ul>
                        <li>
                            <input type="tel" id="yxqy"  placeholder="请输入意向楼盘">
                        </li>
                        <li class="yx">
                            <div>
                                <select id="yxhx" >
                                    <option value="一居">一居</option>
                                    <option value="二居">二居</option>
                                    <option value="三居">三居</option>
                                    <option value="四居">四居</option>
                                    <option value="五居及以上">五居及以上</option>
                                </select>
                            </div>
                        </li>

                        <li>
                            <input type="tel" id="miaoshu" placeholder="请输入您的姓名">
                        </li>

                        <li>
                            <input type="tel" id="phone" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
                        </li>

                        <li>
                            <input type="tel" id="code"  maxlength="6" style="width: 198px;" placeholder="请输入验证码">
                            <p id="hqyzm" class="fxe_ReSendValidateCoad" >获取验证码</p>
                            <p class="fxe_validateCode"></p>
                        </li>
                        <b class="btn">提交</b>
                        <li style="padding-bottom: 0;">
                            <div class="checkagreeInput" style="margin:0 auto 10px auto;">
                                <div style="overflow: hidden;float: none;text-align: left;">提交即代表同意<a style="display: initial;width: auto;padding: 0;float: none;text-align: left;" href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                                    <a  style="display: initial;width:auto;padding: 0;float: none;text-align: left;" href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                            </div>
                        </li>
                        <li style="margin-bottom:0;color: #999;width:237px;text-align:left;padding: 0;color: #ff5200;" id="linkPhone">
                            联系电话：<th:block th:text="${newsinfo.sortTel}">></th:block></li>
                    </ul>
                    <script type="text/javascript">
                        $(".zsfw .fxe_ReSendValidateCoad").click(function () {
                            if (confirm.phone($("#phone").val()) != true) {
                            } else if (confirm.Code($("#phone").val()) == 1) {
                                fxeTime.timeWait();
                            }
                        })
                        $(".zsfw ul .btn").click(function () {
                            if($("#yxqy").val()== "" || $("#yxqy").val()== null){
                                fxe_alert("请填写意向楼盘");
                            }else if (confirm.phone($("#phone").val()) != true) {
                            } else if (confirm.code($("#code").val()) != true) {
                            }else {
                                console.log("意向区域：" + $("#yxqy").val() + "       手机号：" + $("#phone").val() + "       验证码：" + $("#code").val() + "        预算价格：" + $("#ysjg").val() + "       户型：" + $("#yxhx").val() + "      描述：" + $("#miaoshu").val())

                                var tphone = $.trim($("#phone").val());//电话
                                var yusuang = "无"//预算
                                var quyu = $("#yxqy").val();//区域
                                var jusi = $("#yxhx").val();//户型
                                var miaos = $.trim($("#miaoshu").val()) + "       此信息来自sy站资讯页。编辑："+$("#author").val()+"。标题："+$("#title").val();//描述
                                var params = {type: 1,
                                    phone: tphone,
                                    code: $("#code").val(),
                                    region: quyu,
                                    area:jusi,
                                    budget:yusuang,
                                    italy: miaos}
                                fangxiaoer.ajax("upguide", params, function (data) {
                                    if (data.status == 1) {
                                        //重置
                                        $("#phone").val("");
                                        $("#code").val("");
                                        $("#miaoshu").val("");
                                        fxe_alert("亲，您的需求已提交成功，小二管家会及时跟您联系，请耐心等待！")
                                    }else {
                                        fxe_alert("验证码错误")
                                    }
                                });
                            }
                        })
                    </script>
                </div>
            </div>
            &lt;!&ndash;房产名片开始&ndash;&gt;
            <div id="newhinfo"  th:if="${projectInfo ne null}">
                <a th:href="${'/house/'+#strings.toString(projectInfo.ProjectID).replace('.0','')
                +'-' + #strings.toString(projectInfo.projectType).replace('.0','') + '.htm'}" target="_blank">
                    <img th:src="${projectInfo.ImageUrl}" th:alt="${projectInfo.ProjectName}"></a>
                <div class="newhinfo_t">
                    <a th:href="${'/house/'+#strings.toString(projectInfo.ProjectID).replace('.0','')
                +'-' + #strings.toString(projectInfo.projectType).replace('.0','')+ '.htm'}" target="_blank">
                        <th:block th:text="${projectInfo.ProjectName}"></th:block>
                    </a><br>
                    <div class="cl"></div>
                    销售电话: <th:block th:text="${projectInfo.sortelTel }"></th:block><br>
                    项目地址:<th:block th:text="${projectInfo.SaleAddress}"></th:block>
                </div>
            </div>
            &lt;!&ndash;房产名片结束&ndash;&gt;
        </th:block>
        </th:block>-->


    </div>

    <div class="Detailright">
        <input type="hidden" id="author" th:value="${newsinfo.author}"/>
        <input type="hidden" id="title" th:value="${newsinfo.title}"/>

        <!--相关楼盘-->
        <div class="aboutHouse" th:if="${projectInfo ne null and projectInfo.ProjectID ne null}">
            <h4 class="rightTitle"><i></i>相关楼盘</h4>
            <a class="HouseMain" th:href="${'/house/'+#strings.toString(projectInfo.ProjectID).replace('.0','')+'-' + #strings.toString(projectInfo.projectType).replace('.0','') + '.htm'}" target="_blank">
                <img class="HouseMainImg" th:src="${projectInfo.ImageUrl}" th:alt="${projectInfo.ProjectName}" >
                <h5 class="HouseMainH5" th:text="${projectInfo.ProjectName}"></h5>
                <p class="HouseMainP">销售电话: <span th:text="${projectInfo.sortelTel }"></span></p>
                <p class="HouseMainP">项目地址:<th:block th:text="${projectInfo.SaleAddress}"></th:block></p>
            </a>
        </div>

        <!--<div id="newsAD1_guanggao" class="flash"  th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}">&lt;!&ndash;广告&ndash;&gt;
            <ul class="rotaion_list">
                <li th:each="advert1:${advert1}">
                    <sup><img th:src="${#strings.toString(advert1.AdTitle).indexOf('活动') ne -1 ? 'https://static.fangxiaoer.com/web/images/ico/sign/ard.png' : 'https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png'}"></sup>
                    <a th:href="${advert1.TargetUrl}" target="_blank">
                        <img th:src="${advert1.AdFilePath}" alt="">
                    </a>
                </li>
            </ul>
        </div>-->

        <!--相关阅读-->
        <div class="aboutMsg" th:if="${#lists.size(linkLIst) > 0}">
            <h4 class="rightTitle"><i></i>相关阅读</h4>
            <ul class="aboutMsgUl">
                <li class="aboutMsgLi" th:each="newsif:${linkLIst}">
                    <i></i><a th:href="'/news/'+${newsif.id}+'.htm'" target="_blank" th:text="${newsif.title}"></a>
                </li>
            </ul>
        </div>



        <!--APP下载-->
        <div class="downloadApp" style="margin-bottom: 24px;">
            <img class="downloadImg" src="https://static.fangxiaoer.com/web/images/download/apperweima.png" alt="">
            <span>房小二网APP</span>
            <div class="dpp">随时随地<br>掌握房产资讯</div>
            <!--<p>用房小二网APP</p>
            <p>随时随地掌握房产资讯</p>-->
<!--            <a class="downloadHref" href="https://download.fangxiaoer.com/">了解更多</a>-->
        </div>
        <!--房产快讯-->
        <!--<div class="houseInfo" >
            <h4 class="rightTitle"><i></i>房产快讯</h4>
            <div class="hot" th:include="fragment/fragment::news_flash"></div>
        </div>-->

        <div class="grg">
            <a th:href="@{'/helpSearch?ids=１'}" rel="2" target="_blank">我要买房</a>
            <a href="/static/saleHouse/saleHouse.htm" th:class=" (${firstNavIndex} == 8)? 'hover' : '' ">我要卖房</a>
        </div>

        <!--最新视频-->
        <div class="newVideo" th:each="v:${video.content}">
            <h4 class="rightTitle" style="position: relative"><i></i>最新视频<span class="gah" onclick="window.open('/videos')">更多>></span></h4>
            <a class="newVideoA" th:href="${'/video/'+v.videoId+'.htm'}" target="_blank">
<!--                <i class="newVideoIco"></i>-->
                <img class="newVideoImg" th:src="${v.videoPic}" th:alt="推荐视频" />
                <p class="newVideoP dhd" th:text="${v.videoTitle}"></p>
            </a>
        </div>

        <!--<div id="newsAD2_guanggao" class="flash"  th:if="${advert2 ne null and #lists.toList(advert2).size() ne 0}">&lt;!&ndash;广告&ndash;&gt;
            <ul class="rotaion_list">

                <li th:each="advert2:${advert2}">
                    <sup><img th:src="${#strings.toString(advert2.AdTitle).indexOf('活动') ne -1 ? 'https://static.fangxiaoer.com/web/images/ico/sign/ard.png' : 'https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png'}"></sup>
                    <a th:href="${advert2.TargetUrl}" target="_blank">
                        <img th:src="${advert2.AdFilePath}" alt="">
                    </a>
                </li>

            </ul>
        </div>-->

    </div>
    <!--专题页链接区-->
    <!--<div th:include="fragment/fragment:: activity_page"></div>-->
    <div class="cl"></div>

</div>

<div class="cl"></div>
<!--底部2-->
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
</div>

<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/search_xiala.js"></script>
<!--搜索下拉-->
<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.sly.js"></script>
<script>
    function showBaiduFenxiang() {
        //分享点击后 划入的框框小时1秒

        $('.bds_more').click(function () {
            $(".bdshare_popup_box").hide()
            $(".bdshare_popup_box").css("opacity", "0");
        });
        $('.bdshare_popup_bottom').click(function () {
            $(".bdshare_popup_box").hide()
            $(".bdshare_popup_box").css("opacity", "0");
        });
        $('.bdshare_dialog_close').click(function () {
            $(".bdshare_popup_box").css("opacity", "1");
        });
    }
    window.onload = showBaiduFenxiang()
    setTimeout("showBaiduFenxiang()",2000)
    setTimeout("showBaiduFenxiang()", 4000)
    setTimeout("showBaiduFenxiang()", 8000)
</script>
<script>

    var f = false;
    $(document).on('click','.comm_ul2 .view_more',function(){
        if(f = !f) {
            $(this).parent(".comm_ul2").children("li").addClass("display")
            $(this).addClass("view_more1")
            $(this).html("收起全文")
            $(".comm_ul2 li:nth-last-child(2)").css("border-bottom","none")
            // $(".comm_ul2 li:nth-child(3)").css("border-bottom","1px solid #e6e6e6")
        } else {
            $(this).parent(".comm_ul2").children("li").removeClass("display")
            $(".comm_ul2 li:nth-child(3)").css("border-bottom","none")
            $(this).removeClass("view_more1")
            $(this).html("查看更多")
        }
    })
    $(document).on('click','#comms_show .viewmore',function(){
        if(f = !f) {
            $(this).parent(".comm_ul1").children(".li").addClass("display")
            $(this).find("span").html("收起全文")
            $(this).find("img").attr("src"," https://static.fangxiaoer.com/web/images/sy/house/userEvaluate2.png")
            $(".comm_ul1 .li:nth-last-child(2)").css("border-bottom","none")
            // $(".comm_ul1 .li:nth-child(3)").css("border-bottom","1px solid #e6e6e6")
        } else {
            $(this).parent(".comm_ul1").children(".li").removeClass("display")
            $(this).find("span").html("查看更多")
            $(this).find("img").attr("src"," https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png")
            $(".comm_ul1 .li:nth-child(3)").css("border-bottom","none")
        }
    })
    var commul1=$(".comm_ul1 li").length
    if(commul1 > 1){
        $(".xgyd").css("border-top","none")
    }
    if(commul1.length < 4){
        $(".bottom_info").css("border-top","none")
    }else {
        $(".bottom_info").css("border-top","1px solid #e6e6e6")
    }
    var commu2=$(".comm_ul2 li").length
    if(commu2 == 2){
        $(".comm_ul2 li:eq(0)").css("border-bottom","none")
    }
    if(commu2 == 3){
        $(".comm_ul2 li:eq(1)").css("border-bottom","none")
    }
    if(commu2 == 4){
        $(".comm_ul2 li:eq(2)").css("border-bottom","none")
    }
    var commu21=$(".comm_ul2:eq(1) li").length
    if(commu21 == 2){
        $(".comm_ul2:eq(1) li:eq(0)").css("border-bottom","none")
    }
    if(commu21 == 3){
        $(".comm_ul2:eq(1) li:eq(1)").css("border-bottom","none")
    }
    if(commu21 == 4){
        $(".comm_ul2:eq(1) li:eq(2)").css("border-bottom","none")
    }
</script>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<script>
    $(function () {

        var news_sessionId = $("#glzs").val();
        var  newsId = (window.location.href).match(/(news)(\S*)(.htm)/)[2].replace("/","")
        console.log(newsId)
        $.ajax({
            type: "POST",
            url: "/news/getNewsCommentsAndLike",
            dataType: "json",
            data: {
                newsId:newsId
            },
            success: function(data) {
                console.log(data)
                if(data.status == 1){
                    dataAll = data.content
                    console.log(data.newsComments)
                    data = dataAll.newsComments
                    if(dataAll.like == 1){
                        $(".forLike div").addClass("snap-uuip1")
                    }
                    if(dataAll.totalLike == 0){
                        $(".snap-uuip").text("点赞")
                    }else{
                        $(".snap-uuip").text(dataAll.totalLike)
                    }

                    if(data.length > 0){
                        $("#comments_hide").hide()
                        $(".title1").show()
                        console.log(data)
                        $("#title1 span").text(dataAll.newsCommentCount)
                        for (var i = 0; i < data.length; i++) {
                            var likeOrNot = data[i].likeOrNot
                            if(likeOrNot == 0){
                                likeOrNot = "comm_snap"
                            }else{
                                likeOrNot = "comm_snap comm_snap1"
                            }
                            var rName = data[i].memberName
                            if(rName == "" || rName == null){
                                rName = data[i].customerName
                            }else{
                                rName = rName
                            }
                            var PLli = '<li class="li" id="'+data[i].commentId+'">' +
                                '     <div class="left"><img src="'+data[i].memberPic+'"/></div>' +
                                '     <div class="right1">' +
                                '         <div class="comm_content">' +
                                '             <h2>'+rName+' ：</h2>' +
                                '             <p>'+data[i].commentDesc+'</p>' +
                                '         </div>' +
                                '         <div class="comm_time">' +
                                '             <span>'+data[i].addTime+'</span>' +
                                '             <div class="comm_reply" data-toggle="modal" href="#login"><i></i>回复(<span>'+data[i].replyCount+'</span>)</div>' +
                                '             <div id="comm_snap'+data[i].commentId+'" class="'+likeOrNot+'" data-toggle="modal" href="#login" data-id="'+data[i].commentId+'"><i></i>赞(<span id="replyCount'+data[i].commentId+'">'+data[i].likeCount+'</span>)</div>' +
                                '         </div>' +
                                '         <div class="reply_box">' +
                                '             <div style="position: relative;">' +
                                '                 <textarea name="virtues" type="text" data-id="'+data[i].commentId+'" class="virtues1" id="virtues1_'+data[i].commentId+'" maxlength="120" placeholder="回复"></textarea>' +
                                '                 <div class="jiao"></div>' +
                                '             </div>' +
                                '             <div class="limit">' +
                                '                <div class="bottom">' +
                                '                    <p>已输入<i><s id="zi1" class="zi1_'+data[i].commentId+'">0</s></i>/120字(至少10个字)</p>'+
                                '                     <button id="submit1" class="submit1" data-id="'+data[i].commentId+'">提交</button>' +
                                '                 </div>' +
                                '             </div>' +
                                '         </div>' +
                                ' </li>'
                            $("#comm_ul1").append(PLli)
                            reliId =  data[i].commentId
                            if(data[i].reply.length != 0 ){
                                re = data[i].reply
                                console.log(re)
                                $("#" +reliId).find(".right1").append('<ul class="comm_ul2"></ul>')
                                for (var t = 0; t < re.length; t++) {
                                    var divParentId ;
                                    if(re[t].parentId == 0){
                                        divParentId ='<div style="width:96%;">' +
                                            '                   <span>'+re[t].fromMemberName+' ：</span>' +
                                            '                       <span>'+re[t].replyDesc+'</span>' +
                                            '                   </div>' +
                                            '                   <div class="comm_time1">' +
                                            '                       <span>'+re[t].addTime+'</span>' +
                                            '                       <div class="comm_reply1" data-toggle="modal" href="#login"><i></i>回复</div>' +
                                            ' 					 </div>' +
                                            '           </div>'
                                    }else{
                                        divParentId =  '<div style="width:96%;">' +
                                            '                   <span>'+re[t].fromMemberName+'</span>' +
                                            '                   <i>回复</i>' +
                                            '                  <span>'+re[t].toMemberName+'：</span>' +
                                            '                   <span>'+re[t].replyDesc+'</span>' +
                                            '            </div>'+
                                            '			 <div class="comm_time1">' +
                                            '	               <span>'+re[t].addTime+'</span>' +
                                            '              	   <div class="comm_reply1" data-toggle="modal" href="#login"><i></i>回复</div>' +
                                            '            </div>'
                                    }
                                    var reLi ='<li>' +divParentId+
                                        '               <div class="reply_box1">' +
                                        '                   <div style="position: relative;">' +
                                        '                       <textarea name="virtues" type="text" data-id="'+re[t].id+'" id="virtues2_' + +re[t].id+'" class="virtues2" maxlength="120" placeholder="回复"></textarea>' +
                                        '                       <div class="jiao"></div>' +
                                        '                   </div>' +
                                        '                   <div class="limit">' +
                                        '                       <div class="bottom">' +
                                        '                           <p>已输入<i><s id="zi2" class="zi2_' +re[t].id+'">0</s></i>/120字(至少10个字)</p>' +
                                        '                           <button id="submit2" class="submit2" data-id="'+ re[t].commentId +'-' + re[t].id +'">提交</button>' +
                                        '                       </div>' +
                                        '                   </div>' +
                                        '               </div>' +
                                        '           </li>'
                                    $("#" +reliId).find(".comm_ul2").append(reLi)
                                }
                                $("#" +reliId).find(".comm_ul2").append('<div class="view_more">查看更多</div>')
                                if(re.length > 3){
                                    $("#" +reliId).find("li:gt(2)").hide()
                                    $("#" +reliId).find(".view_more").show()
                                }else{
                                    $("#" +reliId).find(".view_more").hide()

                                }
                            }
                        }
                        $("#comm_ul1").append('<li class="li viewmore" ><span>查看更多</span><i style="display: inline-flex;margin-left: 3px;"><img  src="https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png" alt="" ></i></li>')
                    }
                        console.log(data.length)
                        if(data.length == 0){
                            $("#comments_show").hide()
                            $("#comments_hide").show()
                        }else if(data.length>0 && data.length <4){
                            $("#comm_ul1 .viewmore").hide()
                        }else if(data.length > 4){
                            $("#comm_ul1>li:gt(2)").hide()
                            $("#comm_ul1 .viewmore").show()
                        }
                }
                if(news_sessionId != ''){
                    $(".forLike").removeAttr('href');
                    $(".comm_reply").removeAttr('href');
                    $(".comm_reply1").removeAttr('href');
                    $(".comm_snap").removeAttr('href');
                }
                //封装一个限制字数方法
                var checkStrLengths = function (str, maxLength) {
                    var maxLength = maxLength;
                    var result = 0;
                    if (str && str.length > maxLength) {
                        result = maxLength;
                    } else {
                        result = str.length;
                    }
                    return result;
                }
                //监听输入
                $("#virtues").on('input propertychange', function () {
                    //获取输入内容
                    var userDesc = $(this).val();
                    var len;
                    //判断字数
                    if (userDesc) {
                        len = checkStrLengths(userDesc, 500);
                    } else {
                        len = 0
                    }
                    //显示字数
                    $("#zi").html(len);
                });
                $(".virtues1").on('input propertychange', function () {
                    //获取输入内容
                    var userDesc = $(this).val();
                    var len;
                    //判断字数
                    if (userDesc) {
                        len = checkStrLengths(userDesc, 500);
                    } else {
                        len = 0
                    }
                    var info = $(this).attr('data-id');
                    //显示字数
                    $(".zi1_" + info).html(len);
                });
                $(".virtues2").on('input propertychange', function () {
                    //获取输入内容
                    var userDesc = $(this).val();
                    var len;
                    //判断字数
                    if (userDesc) {
                        len = checkStrLengths(userDesc, 500);
                    } else {
                        len = 0
                    }
                    //显示字数
                    var info = $(this).attr('data-id');
                    $(".zi2_"+ info).html(len);
                });

            },
            error: function(data) {
                console.log(data)
            }
        })
        if(news_sessionId != ''){
            $(".forLike").removeAttr('href');
            $(".comm_reply").removeAttr('href');
            $(".comm_reply1").removeAttr('href');
            // $("#submit").removeAttr('href');
            $(".comm_snap").removeAttr('href');
            $(document).on("click", ".snap-uuip", function () {
                if($(".snap-uuip").hasClass("snap-uuip1")){
                    return
                }else{
                    $.ajax({
                        type: "POST",
                        url:'/newsCommentLike',
                        data: { sessionId: news_sessionId, newsId: $("#newsId").val()},
                        dataType: "json",
                        success: function (data) {
                            if(data.status == 1){
                                $(".snap-uuip").addClass("snap-uuip1");
                                var info = $(".snap-uuip").html();
                                if(info == '点赞'){
                                    $(".snap-uuip").html(1)
                                }else{
                                    $(".snap-uuip").html(parseInt(info) + 1);
                                }
                            }else{
                                alert("已赞，不可重复点赞");
                                window.location.reload();
                            }
                        }
                    });
                }
            });
            $(document).on("click", ".comm_snap", function () {
                var commentId = $(this).attr('data-id');
                if($("#comm_snap" + commentId).hasClass("comm_snap1")){
                    return
                }else{
                    $.ajax({
                        type: "POST",
                        url:'/addNewsCommentLike',
                        data: { sessionId: news_sessionId, commentId: commentId},
                        dataType: "json",
                        success: function (data) {
                            if(data.status == 1){
                                $("#comm_snap" + commentId).addClass("comm_snap1");
                                var info = $("#replyCount" + commentId).html();
                                $("#replyCount" + commentId).html(parseInt(info) + 1);
                            }else{
                                alert("已赞，不可重复点赞");
                                window.location.reload();
                            }
                        }
                    });
                }
            });
            $(document).on("click", ".comm_reply", function () {
                $(this).parent().next().slideToggle()
            });
            $(document).on("click", ".comm_reply1", function () {
                $(this).parent().next().slideToggle()
            });

            $(document).on("click", ".submit1", function () {
                var temp = $(this).attr('data-id');
                var replyDesc = $("#virtues1_" + temp).val();
                if ( replyDesc.length >= 10){
                    $.ajax({
                        type: "POST",
                        data: { sessionId:news_sessionId, commentId: temp,replyDesc:replyDesc},
                        url: "/addNewsReply",
                        success: function (data) {
                            if(data.status == 1){
                                alert("感谢您的回复，请等待审核");
                                $("#virtues1_" + temp).val('');
                                $(".zi1_" + temp).html("0")
                            }else{
                                alert("添加失败");
                                window.location.reload();
                            }
                        }
                    });
                }else {
                    alert("至少10个字");
                }
            });
            $(document).on("click", ".submit2", function () {
                var temp = $(this).attr('data-id');
                var info = temp.split('-');
                var replyDesc = $("#virtues2_" + info[1]).val();
                //验证
                if ( replyDesc.length >= 10){
                    $.ajax({
                        type: "POST",
                        data: { sessionId:news_sessionId, commentId: info[0],replyId : info[1] ,replyDesc:replyDesc},
                        url: "/addNewsReply",
                        success: function (data) {
                            if(data.status == 1){
                                alert("感谢您的回复，请等待审核");
                                $("#virtues2_" + info[1]).val('');
                                $(".zi2_" + info[1]).html("0")
                            }else{
                                alert("添加失败");
                                window.location.reload();
                            }
                        }
                    });
                }else {
                    alert("至少10个字");
                }
            });
        }

        $(document).on("click", "#submit", function () {
            //验证是否可提交
            var commentDesc = $("#virtues").val();
            if (commentDesc.length >= 10){
                $.ajax({
                    type: "POST",
                    url:'/addNewsComment',
                    data: { sessionId: news_sessionId, newsId: $("#newsId").val(), commentDesc: commentDesc},
                    dataType: "json",
                    success: function (data) {
                        if(data.status == 1){
                            alert("感谢您的评论，请等待审核");
                            $("#virtues").val('')
                            $("#zi").html("0")
                        }else{
                            alert("添加失败");
                            window.location.reload();
                        }
                    }
                });
            }else {
                alert("至少10个字");
            }
        });



    })
</script>

</body>
</html>