<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>小二说房_买房常识­_房产节目 - 房小二网</title>
    <meta name="keywords" content="小二说房,买房常识­,房产节目,买房小窍门" />
    <meta name="description" content="远离消费陷阱，剖析楼盘行情，小二说房，为你说说买房这点事。" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/video/tellHouse.css?v=20181225"/>
    <link rel="stylesheet" type="text/css" href="/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/video/audio.css?v=20180522">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/video/jplayer.blue.monday.css?v=20180522" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.jplayer.min.js"></script>
    <script th:inline="javascript">
        var audios = [[${audios}]];
        function firstPlay(index) {
            //给音频控制器付个id，用于同步两边的开始暂停按钮
            $("#jp_container_1 .jp-play").attr("id","audios"+index);
            //左侧图片上的开始暂停显示隐藏
            $(".audioList li img.stop").hide();
            $(".audiosPlayFirest").show();
            $(".audiosPlayContinue,.audiosPause").hide();
            $("#first"+index).hide();
            $("#pause"+index).show();
            //右侧图片上的开始暂停显示隐藏
            $(".rightAudiosPlay,.rightAudiosContinue").hide();
            $(".rightAudiosPause").show();
            //赋值，传id
            $(".rightAudiosContinue").attr("onclick","continuePlay("+index+")");
            $(".rightAudiosPause").attr("onclick","audiosPause("+index+")");
            //显示音频控制器
            $("#jp_container_1").show();
            //给右侧赋值
            $(".right_img .selectPic").attr("src",audios[index].audioPic);
            $(".right_img .selectPic").attr("alt",audios[index].Title);
            var htmlElements = "<h3>"+audios[index].description+"</h3>"
            htmlElements = htmlElements + "<p>"+audios[index].Summary+"</p>"
            if( audios[index].audioTitle !="" && audios[index].audioTitle.indexOf("#") != -1){
                var splistTitle = audios[index].audioTitle.split("#")
                if(splistTitle.length == 2){
                    htmlElements = htmlElements+"<p class='title_sun_1'>"+splistTitle[0]+"</p>"
                    htmlElements = htmlElements+"<p class='title_sun_2'>"+splistTitle[1]+"</p>"
                }else {
                    htmlElements = htmlElements+"<p class='title_sun_1'>"+audios[index].audioTitle+"</p>"
                }
            }else {
                htmlElements = htmlElements+"<p class='title_sun_1'>"+audios[index].audioTitle+"</p>"
            }
            $(".right-main").html(htmlElements);
//            $(".right-main h3").text(audios[index].description);
//            $(".right-main p:eq(0)").text(audios[index].audioTitle);
            //摧毁上一个音频
            $("#jquery_jplayer_1").jPlayer("destroy");
            //加载本次点击的音频
            $("#jquery_jplayer_1").jPlayer({
                ready: function () {
                    $(this).jPlayer("setMedia", {
                        title: audios[index].Title,
                        mp3: audios[index].AudioPath,
                    }).jPlayer("play");
                },
                //ready通俗来说就是用来存放媒体的链接、主题。
                swfPath: "../../dist/jplayer",//定义jPlayer 的Jplayer.swf文件的路径。
                supplied: "mp3",//媒体支持的格式
                wmode: "window",//窗口模式
                useStateClassSkin: true,//注释它后，发现音频不能中途暂停，只能让它播放结束后，再次点击播放，暂停功能失效。
                autoBlur: false,//点击之后自动失去焦点。删除后，对音频并无其他影响。该参数是可选项。
                smoothPlayBar: true,//将值设置为false，可以发现进度条是点击时，没有了过渡的过程，是直接到所点位置，体验并不好。
                keyEnabled: true,//通俗点就是是否允许键盘控制播放。
                remainingDuration: false,//false显示音频总时间，true显示音频剩余时间
                toggleDuration: true//true可以点击切换剩余时间或者总时间，false不能切换了
            });
            addNum(audios[index].ID);
        }
        function continuePlay(index) {
            $("#pause"+index).show();
            $("#continue"+index).hide();

            $(".rightAudiosPause").show();
            $(".rightAudiosContinue").hide();
            $("#jquery_jplayer_1").jPlayer("play");
        }
        function audiosPause(index) {
            $("#pause"+index).hide();
            $("#continue"+index).show();

            $(".rightAudiosContinue").show();
            $(".rightAudiosPause").hide();
            $("#jquery_jplayer_1").jPlayer("pause");
        }
        function audiosButton(index) {//点击银屏控制器开始暂停时，给左侧按钮做同步。
            var audiosNum = index.id.replace("audios","");
            if ($("#jp_container_1").hasClass("jp-state-playing")) {
                $("#pause"+audiosNum).hide();
                $("#continue"+audiosNum).show();

                $(".rightAudiosContinue").show();
                $(".rightAudiosPause").hide();
            }else {
                $("#pause"+audiosNum).show();
                $("#continue"+audiosNum).hide();

                $(".rightAudiosPause").show();
                $(".rightAudiosContinue").hide();
            }
        }


    </script>
</head>

<body style="background: #fff">
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=5"></div>
<div>
    <div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/news/">资讯</a> &gt; <a href="">小二说房</a></div>
    <div class="THbanner"></div>
    <div id="jquery_jplayer_1" class="jp-jplayer"></div><!--存放音频和视频源，绝对需要-->

    <!--左侧列表-->
    <div class="THmain">
        <div class="THmain_left">
            <ul class="audioList">
                <li th:each="audios,i:${audios}">
                    <!--<i class="audioList_icon"></i>-->
                    <img th:id="${i.index}" class="jqplay" th:src="${audios.audioPic}" th:title="${audios.Title}"/>
                    <b th:title="${audios.Title}"><th:block th:text="${#strings.toString(audios.Title).length() gt 12?#strings.substring(audios.Title,0,12)+'...':audios.Title}"></th:block></b>
                    <span th:if="${!#strings.isEmpty(audios.OrderId)}" class="OrderId"><th:block th:text="${'第'+audios.OrderId+'期'}"></th:block></span>
                    <!--<p th:id="${'first'+(i.index+1)}" class="audiosPlayFirest" th:onclick="${'firstPlay('+(i.index+1)+')'}">第1次播放</p>-->
                    <!--<p th:id="${'continue'+(i.index+1)}" class="audiosPlayContinue" th:onclick="${'continuePlay('+(i.index+1)+')'}" style="display: none;">继续播放</p>-->
                    <!--<p th:id="${'pause'+(i.index+1)}" class="audiosPause" th:onclick="${'audiosPause('+(i.index+1)+')'}" style="display: none;">暂停播放</p>-->
                    <img th:id="${'first'+(i.index)}" src="https://static.fangxiaoer.com/web/images/sy/video/page_xesf_btn_play_left.png" alt="" th:onclick="${'firstPlay('+(i.index)+')'}" class="play1 audiosPlayFirest">
                    <img th:id="${'continue'+(i.index)}" src="https://static.fangxiaoer.com/web/images/sy/video/page_xesf_btn_play_left.png" alt=""  th:onclick="${'continuePlay('+(i.index)+')'}" style="display: none;" class="play1 audiosPlayContinue">
                    <img th:id="${'pause'+(i.index)}" src="https://static.fangxiaoer.com/web/images/sy/video/page_xesf_btn_stop_left.png" alt="" th:onclick="${'audiosPause('+(i.index)+')'}" style="display: none;" class="stop audiosPause">
                    <p th:class="${'title_sun_'+i.count}" th:if="${#strings.contains(audios.audioTitle,'#')}" th:each="aut,i:${#strings.setSplit(audios.audioTitle,'#')}" th:text="${aut}" ></p>
                    <p class="title_sun_1" th:if="${!#strings.contains(audios.audioTitle,'#')}" th:text="${audios.audioTitle}"></p>
                </li>
            </ul>
            <div th:include="fragment/page :: page"></div>
        </div>
        <!--右侧最新或者点击显示-->
        <div class="THmain_right" th:each="audios,i:${audios}" th:if="${i.index eq 0}">
            <div class="right_title">
                <i><img src="https://static.fangxiaoer.com/web/images/ico/logo/tellIocn.png"/></i>
                <b>专辑里的声音</b>
            </div>
            <div class="right_img">
                <!--<i class="audioList_icon"></i>-->
                <img class="selectPic" th:src="${audios.audioPic}" th:alt="${audios.Title}"/>
                <div class="xesfRightBtn">
                    <img src="https://static.fangxiaoer.com/web/images/ico/logo/page_xesf_btn_stop_right.png" class="rightAudiosPlay" onclick="firstPlay(0)" alt="第一次播放" style="cursor: pointer">
                    <img src="https://static.fangxiaoer.com/web/images/ico/logo/page_xesf_btn_stop_right.png" class="rightAudiosContinue" onclick="continuePlay(0)" style="display: none;cursor: pointer" alt="继续播放">
                    <img src="https://static.fangxiaoer.com/web/images/ico/logo/page_xesf_btn_play_right.png" class="rightAudiosPause" onclick="audiosPause(0)" style="display: none;cursor: pointer" alt="暂停播放">
                </div>
            </div>
            <div id="jp_container_1" class="jp-audio" role="application" aria-label="media player" style="display: none"><!--播放器样式wrap-->
                <div class="jp-type-single">
                    <div class="jp-details"><!--音频的主题-->
                        <marquee direction="left" behavior="scroll" scrollamount="5" scrolldelay="0" loop="-1" width="250"  hspace="10" vspace="10">
                            <div class="jp-title" aria-label="title">&nbsp;</div>

                        </marquee>

                    </div>
                    <div class="jp-no-solution">
                        <span>Update Required</span>
                        To play the media you will need to either update your browser to a recent version or update your <a href="https://get.adobe.com/flashplayer/" target="_blank">Flash plugin</a>.
                    </div>
                    <div class="jp-gui jp-interface">
                        <div class="jp-controls"><!--播放和停止按钮-->
                            <button class="jp-play" role="button" tabindex="0" onclick="audiosButton(this)">play</button>
                            <!--<button class="jp-stop" role="button" tabindex="0">stop</button>-->
                            <!--<button class="jp-pause" role="button" tabindex="0">pause</button>-->
                        </div>
                        <div class="jp-progress"><!--进度条-->
                            <div class="jp-seek-bar">
                                <div class="jp-play-bar"></div>
                            </div>
                        </div>
                        <div class="jp-volume-controls"><!--音量控制键-->
                            <button class="jp-mute" role="button" tabindex="0">mute</button>
                            <button class="jp-volume-max" role="button" tabindex="0">max volume</button>
                            <div class="jp-volume-bar">
                                <div class="jp-volume-bar-value"></div>
                            </div>
                        </div>
                        <div class="jp-time-holder btnTime"><!--音频时间和重复播放按钮(以播放时长)-->
                            <div class="jp-current-time" role="timer" aria-label="time">&nbsp;</div>
                            <!--<div class="jp-duration" role="timer" aria-label="duration">&nbsp;</div>-->
                            <!--<div class="jp-toggles">-->
                                <!--<button class="jp-repeat" role="button" tabindex="0">repeat</button>-->
                            <!--</div>-->
                        </div>
                        <div class="jp-time-holder btnEndTime"><!--音频时间和重复播放按钮(总时长或者剩余时长)-->
                            <!--<div class="jp-current-time" role="timer" aria-label="time">&nbsp;</div>-->
                            <div class="jp-duration" role="timer" aria-label="duration">&nbsp;</div>
                            <!--<div class="jp-toggles">-->
                            <!--<button class="jp-repeat" role="button" tabindex="0">repeat</button>-->
                            <!--</div>-->
                        </div>
                    </div>
                </div>
            </div>
            <div class="right-main">
                <h3><th:block th:text="${audios.description}"></th:block></h3>
                <p> <th:block th:text="${audios.Summary}"></th:block></p>
                <p  th:if="${#strings.contains(audios.audioTitle,'#')}" th:each="aut,i:${#strings.setSplit(audios.audioTitle,'#')}" th:class="${'title_sun_'+i.count}" th:text="${aut}" ></p>
                <p class="title_sun_1" th:if="${!#strings.contains(audios.audioTitle,'#')}" th:text="${audios.audioTitle}"></p>
            </div>
        </div>
        <div class="cl"></div>
    </div>
</div>
<div class="cl"></div>


<div class="cl"></div>
<div th:include="fragment/fragment:: footer_list"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
<script>
    function addNum(audioId) {
        $.ajax({
            type: "POST",
            data: {audioId: audioId},
            url: "/addAudioNum",
            async: false,
            success: function (data) {
                if(data.status == 1){
                    console.log("success plus 1");
                }
            }
        });
    }
</script>
</body>
</html>