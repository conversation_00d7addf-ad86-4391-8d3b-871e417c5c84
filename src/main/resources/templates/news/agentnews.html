<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:if="${categoryId == '149' or #strings.isEmpty(categoryId)}">行业关注,沈阳房产行业关注 - 房小二网</title>
    <title th:if="${categoryId == '148'}">置业推荐,沈阳房产置业推荐 - 房小二网</title>
    <title th:if="${categoryId == '151'}">楼盘动态,沈阳房产楼盘动态 - 房小二网</title>
    <title th:if="${categoryId == '156'}">专题推荐,沈阳房产专题推荐 - 房小二网</title>
    <title th:if="${categoryId == '154'}">工程进度,沈阳房产工程进度 - 房小二网</title>
    <title th:if="${categoryId == '286'}">市场观点,沈阳房产市场观点 - 房小二网</title>
    <title th:if="${categoryId == '159'}">宣传进行时,沈阳房产宣传进行时 - 房小二网</title>
    <meta name="keywords" content="沈阳房产行业信息,沈阳楼市行业信息,楼市产行业资讯,房地产行业信息"/>
    <meta name="description" content="房小二网行业关注是房地产门户网站，覆盖楼市，土地，民生，政策，贷款，学区，房企，地铁等相关内容，提供最新的房地产行业信息，为您在寻找相关信息时提供最优价值参考" />
    <meta name="mobile-agent" th:if="${#strings.isEmpty(categoryId)}" content="format=html5;url=https://m.fangxiaoer.com/news.htm">
    <meta name="mobile-agent" th:if="${!#strings.isEmpty(categoryId)}" th:content="${'format=html5;url=https://m.fangxiaoer.com/news'+categoryId+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css"/>
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/news/default2.css?t=20170601" rel="stylesheet" type="text/css">
<!--    <link href="/css/default2.css?t=20170601" rel="stylesheet" type="text/css">-->

    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript"></script>

    <script type="text/javascript">
        $(function () {
            var url = window.location.href
            if (url.indexOf("keys=") != "-1") {
                url = url.substring(url.indexOf("keys=") + 5, url.lenght);
                $("#txtkeys").val(decodeURI(url));
                $(".warning i").html(decodeURI(url))
                var word = decodeURI(url)
                $(".news p").each(function () {
                    $(this).find("a").html($(this).find("a").html().replace(word, "<s>" + word+"</s>"))
                })
            }
        })
    </script>

    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
</head>

<body class="w1210">
<form name="Form1" method="post" action="" id="Form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
    <!--搜索栏-->
<!--    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=15"></div>-->


    <div class="container">
        <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt;
            <a href="/news/" target="_blank">资讯</a>
            <span id="xiaoyu"></span> <th:block th:if="${!#strings.isEmpty(categoryId)}">&gt;</th:block>
            <a th:if="${categoryId == '149'}" href="" style="cursor: pointer">行业关注</a>
            <a th:if="${categoryId == '148'}" href="" style="cursor: pointer">置业推荐</a>
            <a th:if="${categoryId == '151'}" href="" style="cursor: pointer">楼盘动态</a>
            <a th:if="${categoryId == '156'}" href="" style="cursor: pointer">专题推荐</a>
            <a th:if="${categoryId == '154'}" href="" style="cursor: pointer">工程进度</a>
            <a th:if="${categoryId == '286'}" href="" style="cursor: pointer">市场观点</a>
            <a th:if="${categoryId == '159'}" href="" style="cursor: pointer">宣传进行时</a>


        </div>
        <div class="row">
            <!--左侧资讯信息-->
            <div class="left-w span9" id="news">

                <div class=" news" th:each="n:${news}">
                    <p>
                        <a th:href="${'/news/'+n.id+'.htm'}"

                           target="_blank" th:text="${n.title}"></a><br>
                        <span>
                            <i th:text="${n.visitedTimes}"></i>
                            来源： <th:block th:text="${n.source}"></th:block>  |  <th:block th:text="${'编辑： '+n.author+' '}"></th:block>|  <th:block th:text="${n.updateTime}"></th:block>
                        </span>
                    </p>

                    <div class="left">
                        <a  th:href="${'/news/'+n.id+'.htm'}" target="_blank"><img th:src="${n.pic ne '' and n.pic ne null?n.pic:'https://static.fangxiaoer.com/web/images/agent/newsListImgNo.jpg'}" width="252" height="184" th:alt="${n.title}" /></a></div>
                    <div class="right">
                        <a  th:href="${'/news/'+n.id+'.htm'}" target="_blank">
                            全文>>
                        </a>
                        <a class="content"  th:href="${'/news/'+n.id+'.htm'}" target="_blank" th:text="${n.description}"></a>
                    </div>
                    <div class="cl"></div>
                </div>
            </div>

            <!--右侧帮你找房，房产快讯，最新视频-->
            <div id="right">
                <div class="zsfw">
                    <h1><span></span>帮您找房</h1>
                    <ul>
                        <li>
                            <span>意向区域</span>
                            <div>
                                <select id="region" >
                                    <option>沈河区</option>
                                    <option>大东区</option>
                                    <option>皇姑区</option>
                                    <option>和平区</option>
                                    <option>铁西区</option>
                                    <option>于洪区</option>
                                    <option>浑南区</option>
                                    <option>沈北新区</option>
                                    <option>苏家屯</option>
                                </select>
                            </div>
                        </li>
                        <li class="hx">
                            <span>意向户型</span>
                            <div>
                                <select id="new_huxing" >
                                    <option>一居</option>
                                    <option>二居</option>
                                    <option>三居</option>
                                    <option>四居</option>
                                    <option>五居及以上</option>
                                </select>
                            </div>
                        </li>
                        <li class="yx">
                            <span>预算价格</span>
                            <div>
                                <select id="new_yusuan">
                                    <option>35万以下</option>
                                    <option>35-50万</option>
                                    <option>50-80万</option>
                                    <option>80-100万</option>
                                    <option>100-120万</option>
                                    <option>120-150万</option>
                                    <option>150万以上</option>
                                </select>
                            </div>
                        </li>
                        <li>
                            <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
                        </li>
                        <li>
                            <span>手机号码</span>
                            <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
                            <input type="hidden" id="type" value="1">
                        </li>
                        <li>
                            <span>验证码</span>
                            <input type="tel" id="code" class="fxe_messageCode"  maxlength="6" style="width: 120px;" placeholder="请输入验证码"/>
                            <p id="hqyzm" class="fxe_ReSendValidateCoad" >获取验证码</p>
                            <p class="fxe_validateCode"></p>
                        </li>
                        <b class="btn" id="new_submit">提交</b>
                        <div class="checkagreeInput" style="margin: 0 auto 10px auto;">
                            <div style="font-size:12px;">提交即代表同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                        </div>
                    </ul>
                </div>
                <!--帮您找房-->
                <div th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}"><div id="newsAD1_guanggao" class="flash"><!--广告-->
                    <ul class="rotaion_list">
                        <li th:each="advert1:${advert1}">
                            <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                            <a th:href="${advert1.TargetUrl}" target="_blank">
                            <img th:src="${advert1.AdFilePath}" alt=""></a>
                        </li>
                    </ul>
                </div></div>

                <dl class="recommend" >
                    <dt><span></span>房产快讯</dt>
                    <div class="title">房产快讯</div>
                    <!--房产快讯-->
                    <div class="hot" th:include="fragment/fragment::news_flash"></div>
                </dl>

                <div class="cl"></div>

<!--                &lt;!&ndash;楼盘视频标题&ndash;&gt;
                <div class="title">楼盘视频</div>-->
                <!--楼盘视频信息-->
                <div  style="font-size:18px;">最新视频</div>
                <dl class="recommend" style="border:none" th:each="v:${video}">

                    <a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank"><i></i>
                        <img th:src="${v.videoPic}" th:alt="${v.videoTitle}" /></a>
                    <p> <a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank" th:text="${v.videoTitle}"></a></p>

                </dl>
               <div th:if="${advert2 ne null and #lists.toList(advert2).size() ne 0}"><div id="newsAD2_guanggao" class="flash"><!--广告-->
                    <ul class="rotaion_list">
                        <li th:each="advert2:${advert2}">
                            <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                            <a th:href="${advert2.TargetUrl}" target="_blank">
                            <img th:src="${advert2.AdFilePath}" alt=""></a>
                        </li>
                    </ul>
                </div></div>
            <div class="cl"></div>
            <div class="cl"></div>
        </div>
    </div>
    </div>
    <div class="cl"></div>

    <!--<div th:include="fragment/bottom_secondhouse :: bottom"></div>-->
    <!--分页-->
    <div class="cl"></div>
    <div class="page">
        <div id="Pager1">
            <div th:include="fragment/page :: page"></div>
        </div>
    </div>

    <!--专题页链接区-->
    <!--<div th:include="fragment/fragment:: activity_page"></div>-->
    <div class="cl"></div>
    <div class="cl"></div>
    <!--底部1-->
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div class="cl"></div>

    <script type="text/javascript">
        //<![CDATA[
        var theForm = document.forms['Form1'];
        if (!theForm) {
            theForm = document.Form1;
        }
        function __doPostBack(eventTarget, eventArgument) {
            if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                theForm.__EVENTTARGET.value = eventTarget;
                theForm.__EVENTARGUMENT.value = eventArgument;
                theForm.submit();
            }
        }
        //]]>
    </script>

</form>


<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/tool/login_tc.css">
<script type="text/javascript" src="https://static.fangxiaoer.com/js/fxe_confirm.js"></script>








<div class="modal" id="login">
    <input type="hidden" id="LoginUrl" value=""/>
    <a class="close" data-dismiss="modal" id="loginClose">×</a>
    <h1>账户登录</h1>
    <div  class="signup-form clearfix"  >
        <p>登录名：</p>
        <input autocomplete=off name="Txt_LoginName" id="Txt_LoginName" value="" class="fxe_mobile" placeholder="请输入手机号">
        <p>登录密码：</p>
        <input autocomplete="new-password" name="Txt_Password" id="Txt_Password" class="fxe_password" type="password" placeholder="请输入密码">
        <p style="color: red;margin-bottom:10px" id="error_info"><i class="ic_wrong error_info"></i></p>
        <input type="button" onclick="submitForm();return false;" name="type" class="button-blue reg" value="登录" data-action="regist" >
        <p style="text-align:right"><a href="https://my.fangxiaoer.com/register.aspx" target="_blank">免费注册</a>　<a href="https://my.fangxiaoer.com/RetrPassword.aspx" target="_blank">忘记密码</a></p>
        <div class="clearfix"></div>
        <a href="" class="various" target="_blank"  id="althref" ></a>
    </div>
</div>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/modal.js"></script>
<script>
    function submitForm() {
        function $Arg(id) { return document.getElementById(id); }
        var r = 0;
        if (fxeTime.mobile()) {
            if (fxeTime.password()) {
                if (confirm.mobile()) {
                    $.ajax({
                        type: "POST",
                        data: { number: $Arg("Txt_LoginName").value, passowrd: $Arg("Txt_Password").value },
                        url: "/action/userlogin.ashx",
                        async: false,
                        success: function (data) {
                            r = data;
                        }
                    });
                } else {
                    r=0
                    $(".error_info").text(hint[0]);
                }
            }
        }
        if (r == 1) {
            if ($("#LoginUrl").val() != "" && IsCommentList == 1) {
                window.location.href = $("#LoginUrl").val();
            } else {
                window.location.reload();
            }
            //  var url = '';
            //  var adiog = '';
            // if (adiog == "1") {
            //  $("#althref").trigger("click");
            //  $("#login").modal("hide");
            //  } else {
            // if (url == "") {
            //console.log(window.location.href = window.location.href)
            //   console.log("333333")
            //window.location.reload();
            //window.location.href = window.location.href;
            //  } else {
            //location.href = url;
            //     console.log("444444")
            //  }
            //  }
        } else {
            r = 0
            $(".error_info").text(hint[0]);
        }
    }

</script>
<script type="text/javascript">
    $(document).ready(function () {
        $("a.forgot").click(function () {
            $("#login-modal").modal("hide");
            $("#forgetform").modal({ show: !0 });
        });
        $("#login").modal("hide");
    });
    $(document).keypress(function (e) {
        // 回车键事件  
        if (e.which == 13) {
            submitForm();
            return false;
        }
    });
</script>

</body>
</html>

