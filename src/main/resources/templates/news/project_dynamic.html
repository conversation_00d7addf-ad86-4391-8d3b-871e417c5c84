<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
<meta charset="utf-8" />
<title th:text="${seoTitle + '销售动态_沈阳'+seoTitle+'降价_'+seoTitle+'折扣 - 房小二网'}"></title>
<meta name="keywords" th:content="${seoTitle +'销售动态,沈阳'+seoTitle+'优惠,'+seoTitle+'降价'}"/>
<meta name="description" th:content="${'房小二网为您提供沈阳'+seoTitle+'降价、折扣、礼包、全款优惠、礼品等各种最全最及时信息，为您选房、看房提供优质参考！'}"/>
<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css">
<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/news/house_dynamic_list.css" />
<style>
    .unstyled{ height: 143px; overflow: hidden; }
    .recommend dt a{
        font-size: 12px;
        color: #5097FF;
        cursor: pointer;
        user-select: none;
        position: absolute;
        bottom: -2px;
        right: 24px;}
    .recommend dt a:hover{ text-decoration: none;}
</style>
</head>
<body>
<form name="Form1" method="post" action="" id="Form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=5,listType=0"></div>

    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt;
        <a href="/projectnews" style="cursor: pointer">楼盘销售动态</a></div>
    <div id="option">
        <ul>
            <!--区域-->
            <li id="Search_zf" style="display: block;">
                <p>位置：</p>
                <a th:each="region:${region}" th:text="${#strings.toString(region.name).replaceAll('全部','不限')}" th:href="${region.url}" th:class="${region.selected}? 'hover':''"></a>
                <br>
            </li>
            <!-- 类型 -->
            <li id="Search_zf" style="display: block;">
                <p>类型：</p>
                <a th:each="projectType:${projectType}" th:text="${#strings.toString(projectType.name).replaceAll('全部','不限')}" th:href="${projectType.url}" th:class="${projectType.selected}? 'hover':''"></a>
            </li>
        </ul>
    </div>

    <!-- 列表主内容 -->
    <div class="w">
        <div id="left">
            <!--<div class="hdy-left-top"><span>2月15日-2月22日</span>沈阳共有<p>100</p>个楼盘有新销售动态</div>-->
            <ul class="hdy-list">
                <li th:each="dynamic:${dynamicList}" class="hdy-li">
                    <a class="hdy-a" th:href="${'/house/' + dynamic.projectId + '-' + dynamic.projectType + '/news.htm'}" target="_blank">
                        <div class="hdy-list-img">
                            <div class="vrs" th:if="${dynamic.pan ne null}"><i></i><b></b></div>
                            <img th:src="${dynamic.pic}">
                        </div>
                        <div class="hdy-list-msg">
                            <h4 class="hdy-msg-1" th:text="${dynamic.projectName}"></h4>
                            <div class="hdy-msg-2">
                                <p th:text="${dynamic.updateTime}"></p>
                                <span>动态</span>
                            </div>
                            <div class="hdy-msg-3" th:text="${dynamic.projectDesc}"></div>
                        </div>
                    </a>
                </li>
            </ul>
        </div>
        <div id="right">
            <!-- 小二精选楼盘 -->
            <div class="xe-house">
                <div class="recommend" style="margin-top: 0; margin-bottom: 0; border: unset; border-bottom: 1px solid #eaeaea;">
                    <dt style="border: unset;"><span></span>小二精选</dt>
                </div>
<!--                <h4 class="xe-house-title">小二精选</h4>-->
                <div class="childLtab">
                    <span class="chitabL">楼盘名称</span>
                    <span class="chitabJ">价格</span>
                    <span class="chitabQ" style="margin-left: 52px;">区域</span>
                </div>
                <div class="childlist">
                    <ul>
                        <li class="chilistLi" th:each="project, i : ${rankList}" th:if="${i.index lt 10}">
                            <div>
                                <div class="crankList" style="">
                                    <ul>
                                        <li th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}">华强商业金融中心</li>
                                        <li th:if="${project.mPrice eq null}">待定</li>
                                        <li th:if="${project.mPrice ne null}"><span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡</li>
                                        <li th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}">沈河区</li>
                                    </ul>
                                </div>
                                <a th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}" target="_blank">
                                    <div class="crankDetails" style="display: none;">
                                        <div class="crankDetailsdiv" style="position: relative">
                                            <img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
                                            <div class="vru" th:if="${project.pan ne null}"></div>
                                            <div class="crankDetailsdivR">
                                                <h2 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}">华强商业金融中心</h2>
                                                <p class="crankDetailsP" th:if="${project.mPrice eq null}" >待定</p>
                                                <p class="crankDetailsP" th:if="${project.mPrice ne null}">
                                                    <span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡
                                                    <span class="Ljun" th:text="${project.mPrice.priceType eq '起价'? '起' :'均'}"></span>
                                                </p>
                                                <p class="crankDetailsD">
                                                    <span th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}"></span>
                                                    <span class="lGeng" th:if="${!#maps.isEmpty(project.area)}"
                                                          th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))
                                                                    + '-' + #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}">
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="crankDetailsreason">
                                            推荐理由：<span th:text="${project.rankDesc}"></span>
                                        </div>
                                    </div>

                                </a>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 房产快讯 -->
            <dl class="recommend">
                <dt style="position: relative;"><span></span>房产快讯<a href="/news" target="_blank">更多>></a></dt>
                <div class="title">房产快讯</div>
                <div class="hot">
                    <ul class="unstyled">
                        <li th:each="project, i : ${flash}">
                            <a th:href="${'/news/'+project.id+'.htm'}" target="_blank" th:text="${project.titleShow}"></a>
                        </li>
                    </ul>
                </div>
            </dl>
        </div>
    </div>

    <!--分页-->
    <div class="cl"></div>
    <div class="page" th:unless="${isRecommend}">
        <div id="Pager1">
            <div th:include="fragment/page :: page"></div>
        </div>
    </div>
    <div class="cl"></div>
    <!--底部1-->
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div class="cl"></div>
    <script type="text/javascript">
        //<![CDATA[
        var theForm = document.forms['Form1'];
        if (!theForm) {
            theForm = document.Form1;
        }
        function __doPostBack(eventTarget, eventArgument) {
            if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                theForm.__EVENTTARGET.value = eventTarget;
                theForm.__EVENTARGUMENT.value = eventArgument;
                theForm.submit();
            }
        }
        //]]>
    </script>
</form>

<script>
    window.onload = function(){
        $(".crankList").eq(0).hide();
        $(".crankDetails").eq(0).show();
    }

    $(".crankList").hover(function() {
        // $(".crankList").show();
        $(this).parent().parent().parent().find(".crankList").show();
        $(this).hide();
        $(this).parent().parent().parent().find(".crankDetails").hide();
        $(this).parent().find(".crankDetails").show();
    })
</script>
</body>
</html>
