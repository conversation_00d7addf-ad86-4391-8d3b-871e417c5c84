<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title th:text="${v.title+'_沈阳'+v.title+'_'+v.title+'项目介绍 - 房小二网'}"></title>
    <meta name="keywords" th:content="${v.title+',沈阳'+v.title+','+v.title+'项目介绍,沈阳楼盘信息'}"/>
    <meta name="description"
          th:content="'房小二网为您提供海量的沈阳'+${v.title}+'信息，'+${v.title}+'房价，小区详情，周边配套，实景图，客观反映沈阳房价走势，为您选购沈阳二手房提供全方位参考。'"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/sub/'+subId+'.htm'}">
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css"/>
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/sale/subdistrict.css?t=20210507"/>
    <!--<link rel="stylesheet" href="/css/subdistrict.css">-->
    
    <link rel="Stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/sy/index/idangerous.swiper.css"/>
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/vilageImage.css" rel="stylesheet" type="text/css">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/sale/villageContent.css?v=20210507" rel="stylesheet" type="text/css">
    	<!--<link rel="stylesheet" href="/css/villageContent.css">-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/idangerous.swiper.min.js"></script>
    <script src="https://static.fangxiaoer.com/js/highcharts6.0.7.js"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/trendChart.css"/>
    <link href="https://static.fangxiaoer.com/js/tc/tc.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css"href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleHoseView2018.css?=20180420"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <script src="https://static.fangxiaoer.com/global/js/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/saleListRightBanner.css?t=20200110">
<!--    <link rel="stylesheet" type="text/css" href="/css/saleListRightBanner.css">-->
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
    <script type="text/javascript">
//        $(document).ready(function () {
//            $(".various2").fancybox({
//                'autoScale': false,
//                'transitionIn': 'elastic',
//                'transitionOut': 'elastic',
//                'type': 'image'
//            });
//        });
    </script>
    <style>
        .zsfw {
            margin-top: 0
        }

        .left {
            width: 874px !important;
            float: left;
            border: 1px solid #eaeaea;
            margin-right: 0px !important;
            overflow: auto !important;
        }
        .villageImage>ul {
            height: 205px;
        }
        .left{border: none !important;}
        .house2,.villageImage {
            width: 868px;
        }

        .photo{
            width: auto !important;
        }


        .trendCharMap{

            width:850px;
        }

        .sanjiao{
            position: absolute;
            width: 16px;
            height: 10px;
            top: 34px;
            left: 50%;
            margin-left: -8px;
            overflow: hidden;
            background-repeat: no-repeat !important;
        }
        .sanjiao1{ background: url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png);}
        .sanjiao2{ background: url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian7.png);}
        #memap a{ color: #fff;}
        #memap a:hover{ color: #fff; text-decoration: none;}
      
    </style>
</head>
<div>

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
    <div th:include="plot/fragment_plotMenu::plotenu" th:with="type=1"></div>
</div>
<body>

<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/second/swiper.min.css">
<script src="https://static.fangxiaoer.com/js/agent/swiper.min.js"></script>
<form name="form1" method="post" action="37" id="form1">
    <div class="w mian">
        <div class="villageContent">
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide" th:if="${#lists.isEmpty(v.pics)}">
                        <img  src="https://static.fangxiaoer.com/web/images/sy/sale/sitImg.jpg"
                             alt="">
                    </div>
                    <div class="swiper-slide"  th:if="${!#lists.isEmpty(v.pics)}" th:each="one:${v.pics}">
                        <!--<img  th:src="${one.photoUrl}"
                        	
                              alt="">-->
                              <div th:style="${'background: url('+one.photoUrl+') center no-repeat;background-size: cover;width:600px;height:400px'}"></div>
                        <p><span th:text="${one.photoName}"></span><i class="swiper-pagination"></i></p>
                    </div>

                </div>
                <span th:if="${!#lists.isEmpty(v.pics)  && #lists.size(v.pics) ne '0' }">
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </span>
            </div>
            <script type="text/javascript">
                var mySwiper = new Swiper('.swiper-container', {
                    autoplay: 5000,//可选选项，自动滑动
                    loop : true,
                    pagination: {
                        el: '.swiper-pagination',
                        type: 'fraction',
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    }
                })
            </script>
            <div class="villageRight">
                <dl>
                    <dt>

                    <div  th:if="${v.unitPrice} ne 0.0">
                        <ul>
                            <li class="min">
                            	<span class="avePrice">均价</span>
                            	<span><b th:if="${v.unitPrice}"
                                    th:text="${#strings.toString(v.unitPrice).contains('.') ?  #strings.toString(v.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : v.unitPrice}">8684</b></span>元/㎡
                            </li>
						
                        <!--<li>
                        	<span class="avePrice">环比上月</span>
                        <i th:if="${#strings.toString(v.increaseRate) ne null && #strings.toString(v.increaseRate) ne '0.0'} "
					   th:class="${#strings.toString(v.increaseRate).substring(0,1) eq '-' ? 'increaseRateColor' : 'colorRed'}" >
						<span></span>
                        <s th:text="${#strings.toString(v.increaseRate).replace('-','')+'%'}"></s>
                    </i>
                    <i th:if="${#strings.toString(v.increaseRate) eq '0.0'} ">
                    	<span style="margin-top: 3px;display: block;float: left;">&#45;&#45;&#45;&#45;</span>
                    </i>
                    </li> -->
                        </ul>
                   </div>

                    <div  th:if="${v.unitPrice} eq 0.0">
                        <ul>
                            <li class="min">暂无信息<span></span>
                            </li>
                            <li class="min" ><span th:text="----">2.3%</span></li>
                        </ul>
                    </div>

                    </dt>
                    <dd>
                        <ul>
                            <li><b>建筑类型：</b><span th:text="${v.buildTypeValue ne ''}?${v.buildTypeValue}:'暂无信息'">高层</span></li>
                            <li><b>建成年代：</b><span th:text="${v.buildDate ne null and v.buildDate ne ''}?${v.buildDate + '年'}:'暂无信息'"></span></li>
                            <li class="w100"><b>开发公司：</b><span th:text="${!#strings.isEmpty(v.developer) ? v.developer : '暂无信息'}"></span></li>
                            <li><b>容积率：</b><span th:text="${v.plotRatio ne ''}?${v.plotRatio}:'暂无信息'">1.6</span></li>
                            <li><b>绿化率：</b><span th:text="${v.greeningRate ne ''}?${v.greeningRate}:'暂无信息'">35%</span>
                            <li class="w100" th:if="${!#strings.isEmpty(v.propertyCom)}"><b>物业公司：</b><span th:text="${!#strings.isEmpty(v.propertyCom)}?${v.propertyCom}:'暂无信息'"></span></li>
                            <li th:if="${!#strings.isEmpty(v.propertyTel)}"><b>物业电话：</b><span th:text="${!#strings.isEmpty(v.propertyTel)}?${v.propertyTel}:'暂无信息'"></span></li>
                            <li th:if="${!#strings.isEmpty(v.propertyFee) && #strings.toString(v.propertyFee) ne '0.0' && !#strings.isEmpty(v.propertyUnit)}"><b>物业费：</b><span th:text="${#strings.toString(v.propertyFee) ne '0.0' and !#strings.isEmpty(v.propertyFee) ? v.propertyFee+' '+ v.propertyUnit : '暂无信息'}"></span></li>
                            <li th:if="${!#strings.isEmpty(v.normalParkingNum) && #strings.toString(v.normalParkingNum) ne '0'}"><b>地上车位数：</b><span th:text="${!#strings.isEmpty(v.normalParkingNum) ? v.normalParkingNum:'暂无信息'}">暂无信息</span></li>
                            <li th:if="${!#strings.isEmpty(v.normalParkingFee) && #strings.toString(v.normalParkingFee) ne '0'}"><b>地上车位管理费：</b><span th:text="${!#strings.isEmpty(v.normalParkingFee) ? v.normalParkingFee+'元/月':'暂无信息'}">暂无信息</span></li>
                            <li th:if="${!#strings.isEmpty(v.undergroundParkingNum) && #strings.toString(v.undergroundParkingNum) ne '0'}"><b>地下车位数：</b><span th:text="${!#strings.isEmpty(v.undergroundParkingNum) ? v.undergroundParkingNum:'暂无信息'}">暂无信息</span></li>
                            <li th:if="${!#strings.isEmpty(v.undergroundParkingFee) && #strings.toString(v.undergroundParkingFee) ne '0'}"><b>地下车位管理费：</b><span th:text="${!#strings.isEmpty(v.undergroundParkingFee) ? v.undergroundParkingFee+'元/月':'暂无信息'}">暂无信息</span></li>
                        </ul>
                    </dd>
                </dl>
                <div class="villageNum" th:if="${v.saleCount ne '0' and v.saleCount ne null}">
                    <th:block th:if="${v.saleCount ne '0' and v.saleCount ne null}">二手房房源：<a
                            th:href="${'/saleVillages/'+subId+'/plotSecondVillage/-v'+subId+'-r'+v.regionId}"><span th:text="${v.saleCount+'套'}">930套</span></a>
                    </th:block>
                    <th:block th:if="${v.rentCount ne '0' and v.rentCount ne null}">租房房源：<a
                            th:href="${'/saleVillages/'+subId+'/plotRentVillage/-v'+subId+'-r'+v.regionId}"><span th:text="${v.rentCount+'套'}">22套</span></a>
                    </th:block>
                </div>
            </div>
        </div>
        <script>
            //	根据经纪人的个数判断是否轮播
            $(document).ready(function () {
                var agentLiNum = $('.swiper-containervillageAgent>div>div').length;
                console.log(agentLiNum)
                if(agentLiNum <= 4 ){
                    $(".swiper-button-nextVil").hide()
                    $(".swiper-button-prevVil").hide()
                }
            });

            //	经纪人轮播
            var swiper = new Swiper('.swiper-containervillageAgent', {
                slidesPerView: 4,
                spaceBetween: 30,
                slidesPerGroup: 1,
                loop: false,
                loopFillGroupWithBlank: true,
                pagination: {
                    el: '.swiper-paginationVil',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });
            function changeColor(e) {
                $(e).css("background-color","#188CDE")
            }
            function changeColor2(e) {
                $(e).css("background-color","#32a3f2")
            }
        </script>

        <div class="cl"></div>
        <div th:include="secondhouse/fragment::QQMapZhouBianPeiTao1_mapDom_village"></div>
        <div class="cl"></div>
        <script th:inline="javascript">
            $(document).ready(function () {

                var id = [[${subId}]];
                var lng = [[${v.longitude}]];
                var lat = [[${v.latitude}]];
                var title = [[${v.title}]];
                var address = [[${v.address}]];
                var city = '沈阳';
                var content = "";
                $("#mapsubTitle").val(title);
                bdMap.init("memap", {
                    id: id,
                    houselng: lng,
                    houselat: lat,
                    radius: 2000,
                    suofa: 14,
                    bdtitle: title,
                    bdcontent: content,
                    address: address,
                    city: city
                });
            });
        </script>

        <div class="right">
            <div id="xiangsihouse" class="head recommend"
                 th:if="${similar ne null and #lists.toList(similar).size() ne 0}">
                <h1><i class="shugang"></i>同小区你可能感兴趣的房源</h1>
                <ul>
                    <li th:each="s:${similar}">
                        <a th:href="${s.houseId + '.htm'}" target="_blank">
                            <div>
                                <img th:src="${#strings.isEmpty(s.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.pic}"
                                     th:alt="${s.subName}"/>
                                <b th:text="${s.subName}"></b>
                                <p th:text="${s.room + '室' + s.hall + '厅'}"></p>
                                <span th:if="${!#strings.isEmpty(s.price) and #strings.toString(s.price) ne '0.0'}"
                                      th:text="${#strings.toString(s.price).contains('.')? #strings.toString(s.price).replaceAll('0+?$','').replaceAll('[.]$', '') : s.price}"></span><i>万</i>


                            </div>

                        </a>
                    </li>
                </ul>
            </div>

            <!--<div class="groomList" th:if="${!#lists.isEmpty(similarities)}" style="width: 246px;">
                <h4><i></i>相似小区</h4>
                <ul>
                    <li th:each="s,i:${similarities}" th:if="${i.count lt 3}">
                        <a th:href="${'/saleVillages/'+s.subId + '/index.htm'}" target="_blank">
                            <img th:src="${#strings.isEmpty(s.picUrl)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.picUrl}" alt="">
                            <div class="imgBottm">
                                <span th:if="${#strings.toString(s.subName) ne null && #strings.toString(s.subName) ne ''}" th:text="${s.subName}"></span>
                            </div>
                            <div class="oneH">
                                <span class="tuijian_price" style="color: #ff5200;">
                                    <span style="font-weight:bold" th:if="${!#strings.isEmpty(s.monthPrice) and  #strings.toString(s.monthPrice) ne '0.0'}">
                                        <th:block   th:text="${#strings.toString(s.monthPrice).contains('.')? #strings.toString(s.monthPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : s.monthPrice}">
                                        </th:block>元/㎡
                                    </span>
                                </span>
                                <span class="tuijian_price" style="color: #ff5200;" th:if="${#strings.toString(s.monthPrice) eq null || #strings.toString(s.monthPrice) eq '0.0' ||  #strings.toString(s.monthPrice) eq ''}" th:text="${'暂无信息'}"></span>
                                <span class="gaiare"  th:text="${s.regionName+'-'+s.plateName}" style="float: right"></span>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>-->
<!--            <div th:include="plot/fragment_plotMenu::wenda"></div>-->

        </div>
        <div class="left">


            <!--<div th:if="${v.asks ne null and #lists.toList(v.asks).size() ne 0}">
                <link href="https://static.fangxiaoer.com/web/styles/sy/sale/questions.css" rel="stylesheet"
                      type="text/css">
                <div class="questions house2">
                    <div class="house2Title"><span th:text="${v.title }"></span>小区问答<a th:href="${'/saleVillages/'+subId+'/plotAsk.htm'}">更多回答></a></div>
                    <ul>
                        <li th:each="a:${v.asks}">
                            <div>
                                <p class="questions">
                                    <a th:href="${'/saleVillages/'+a.id+'/getAskDetail.htm'}"><b
                                            th:text="${'【'+v.title+'】'+a.askContent}">【城建花园】这里的住户基本是什么职业呢？白领集中么？</b></a>
                                    <span th:if="${a.reply.replyTime}"
                                          th:text="${#strings.toString(a.reply.replyTime).replace('.','-')}"></span>
                                    <span th:if="${#maps.isEmpty(a.reply)}"
                                          th:text="${#strings.toString(a.askTime).replace('.','-')}"></span>
                                </p>
                                <p class="answers">
                                    <th:block th:if="${a.reply.validate eq '1'}">
                                        <a th:href="'/agent/ask/' + ${a.reply.memberId}"><span th:text="${a.reply.memberName}">杨彬：</span></a>
                                    </th:block>
                                    <th:block th:if="${a.reply.validate eq'0'}">
                                       <span th:text="${a.reply.memberName}">杨彬：</span>
                                    </th:block>
&lt;!&ndash;                                    <a><span th:text="${a.replyName}">杨彬：</span></a>&ndash;&gt;

                                    <th:block th:text="${a.reply.replyContent}"></th:block>
                                </p>
                            </div>
                            <a th:href="${'/saleVillages/'+a.id+'/getAskDetail.htm'}" class="replenish">我补充></a>
                        </li>
                    </ul>
                </div>
            </div>-->
            <!--<div th:if="${!#strings.equals(v.hasExpert, '0')}">-->
                <!--<link href="https://static.fangxiaoer.com/web/styles/sy/sale/specialist.css?t=20180420" rel="stylesheet"-->
                      <!--type="text/css">-->
                <!--<div class="specialist house2">-->
                    <!--<div class="house2Title"><span th:text="${v.title}"></span>专家解读 <a th:href="${'/saleVillages/'+subId+'/plotExpertInterpretation.htm'}">更多解读></a></div>-->
                    <!--<ul>-->
                        <!--<li>-->
                            <!--<div class="headPortrait">-->
                                <!--<div>-->
                                    <!--<a th:href="${'/agent/second/' + v.expertComment.memberId}">-->
                                        <!--<img th:if="${#strings.toString(v.expertComment.memberPic) ne null && #strings.toString(v.expertComment.memberPic) ne ''}" th:src="${v.expertComment.memberPic}" alt="">-->
                                        <!--<img th:if="${#strings.toString(v.expertComment.memberPic) eq null || #strings.toString(v.expertComment.memberPic) eq ''}"  src="https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png" alt="">-->
                                    <!--</a>-->
                                <!--</div>-->

                                <!--<p th:text="${v.expertComment.realName}">杨彬</p>-->
                            <!--</div>-->
                            <!--<div class="specialistContent">-->
                                <!--<dl>-->
                                    <!--<dt>-->
                                        <!--<span>特色</span>-->
                                        <!--<div>-->
                                            <!--<span th:if="${!#strings.isEmpty(v.expertComment.layout)}">【小区户型】</span>-->
                                            <!--<p th:if="${!#strings.isEmpty(v.expertComment.layout)}" th:text="${v.expertComment.layout}">目前在售高层产品，户型为44、83、104㎡，南北通透。</p>-->
                                            <!--<div class="cl"></div>-->
                                            <!--<span th:if="${!#strings.isEmpty(v.expertComment.matching)}">【生活配套】</span>-->
                                            <!--<p th:if="${!#strings.isEmpty(v.expertComment.matching)}" th:text="${v.expertComment.matching}">-->
                                                <!--目前在售高层产品，户型为44、83、104㎡，南北通透。</p>-->
                                            <!--<div class="cl"></div>-->
                                            <!--<span th:if="${!#strings.isEmpty(v.expertComment.facilities)}">【小区设施】</span>-->
                                            <!--<p th:if="${!#strings.isEmpty(v.expertComment.facilities)}" th:text="${v.expertComment.facilities}">-->
                                                <!--目前在售高层产品，户型为44、83、104㎡，南北通透。</p>-->
                                            <!--<div class="cl"></div>-->
                                            <!--<span th:if="${!#strings.isEmpty(v.expertComment.traffic)}">【交通情况】</span>-->
                                            <!--<p th:if="${!#strings.isEmpty(v.expertComment.traffic)}" th:text="${v.expertComment.traffic}">-->
                                                <!--</p>-->
                                            <!--<div class="cl"></div>-->
                                            <!--<span th:if="${!#strings.isEmpty(v.expertComment.school)}">【附近学校】</span>-->
                                            <!--<p th:if="${!#strings.isEmpty(v.expertComment.school)}" th:text="${v.expertComment.school}">目前在售高层产品，户型为44、83、104㎡，南北通透。</p>-->
                                            <!--<div class="cl"></div>-->
                                            <!--<span th:if="${!#strings.isEmpty(v.expertComment.other)}">【其他描述】</span>-->
                                            <!--<p th:if="${!#strings.isEmpty(v.expertComment.other)}" th:text="${v.expertComment.other}">目前在售高层产品，户型为44、83、104㎡，南北通透。</p>-->

                                        <!--</div>-->
                                    <!--<dd th:if="${!#strings.isEmpty(v.expertComment.insufficient)}">-->
                                        <!--<span>不足</span>-->
                                        <!--<div th:text="${v.expertComment.insufficient}">小区商业还未完善，配套不成熟。</div>-->
                                    <!--</dd>-->
                                    <!--</dt>-->
                                    <!--<dd th:if="${!#lists.isEmpty(v.expertComment.pics)}">-->
                                       <!--<img  th:each="pic:${v.expertComment.pics}" th:src="${pic.picUrl}" th:alt="${pic.picName}">-->
                                    <!--</dd>-->
                                <!--</dl>-->

                            <!--</div>-->
                        <!--</li>-->
                    <!--</ul>-->
                <!--</div>-->
            <!--</div>-->



            <!--二手房-->
            <!--<div class="villageImage house2" th:if="${!#lists.isEmpty(secondhouse)}" style="width: 870px">
                <div class="house2Title"><span th:text="${v.title }"></span>二手房<a th:if="${#lists.size(secondhouse) ge 4}" th:href="${'/saleHouses/-v'+subId+'-r'+v.regionId}"
                                               target="_blank">更多二手房></a></div>
                <ul>
                    <li th:each="second:${secondhouse}">
                        <div class="villageImages">
                            <a th:href="'/salehouse/'+${second.houseId}+'.htm'" target="_blank">
                                <img th:src="${#strings.isEmpty(second.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : second.pic}"
                                     th:alt="${second.subName}">
                            </a>
                        </div>
                        <div class="villageHouseInfo">
                            <div class="villageTitle"><a th:href="'/salehouse/'+${second.houseId}+'.htm'"
                                                         target="_blank"
                                                         th:text="${second.title}"></a></div>
                            <div class="vallageInfo">
                                <b th:text="${second.room+'室'+second.hall+'厅'+second.toilet+'卫'}"></b>
                               &lt;!&ndash; <span th:text="${second.price+'万元'}"></span>&ndash;&gt;
                                <span th:text="${second.price != '0'?second.price+'万元':'面议'}"></span>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>-->
            <!--租房-->
            <div class="villageImage house2" th:if="${!#lists.isEmpty(rent)}">
                <div class="house2Title"><span th:text="${v.title }"></span>租房<a th:if="${#lists.size(rent) ge 4}" th:href="${'/rents/-v'+subId+'-r'+v.regionId}" target="_blank">更多租房></a>
                </div>
                <ul>
                    <li th:each="rent:${rent}">
                        <div class="villageImages">
                            <a th:href="'/rent/'+${rent.houseId}+'.htm'" target="_blank">
                                <img th:src="${rent.pic eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':rent.pic}"
                                     th:alt="${rent.title}">
                            </a>
                        </div>
                        <div class="villageHouseInfo">
                            <div class="villageTitle"><a th:href="'/rent/'+${rent.houseId}+'.htm'" target="_blank"
                                                         th:text="${rent.title}"></a></div>
                            <div class="vallageInfo">
                                <b th:text="${rent.room+'室'+rent.hall+'厅'+rent.toilet+'卫'}"></b>
                                <span th:text="${rent.price != '0'?rent.price+'元/月':'面议'}"></span>
                             </div>
                        </div>
                    </li>
                </ul>
            </div>
            <th:block th:if="${isRound == 1}">
                <!--<div class="trendChar house2" id="zou" >
                    <div class="house2Title"><span th:text="${v.title }"></span>房价走势</div>
                    <div class="trendCharContent">
                        <dl class="kfokookk">
                            <dt>
                            <div class="trendChartTitle">
                            	<i  style="background: #75d88f;float:left;margin-top: 7px;"></i>
                            	<div id = "community_Name">城建花园</div>
                            </div>
                            <div class="trendChartPrice" th:if="${v.unitPrice ne 0.0}">
                            	          <b  th:text="${'￥'} +${#strings.toString(v.unitPrice).contains('.') ?  #strings.toString(v.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', '')+ '元/㎡' : v.unitPrice+ '元/㎡'}">￥8652元/㎡ </b>
                                <div class="avediv">环比上月</div>
                                <span  th:if="${#strings.toString(v.increaseRate) eq '0.0'}" th:text="${'&#45;&#45;&#45;&#45;'}" style="font-weight: normal"></span>
                                <span class="percent up"  th:if="${#strings.toString(v.increaseRate).substring(0,1) ne '-' and #strings.toString(v.increaseRate) ne '0.0'}"  style="font-weight: normal">
                                	<i></i>
                                	<span th:text="${v.increaseRate+'%'}"></span>
                                </span>
                                <span class="percent dis"  th:if="${#strings.toString(v.increaseRate).substring(0,1) eq '-'}">
                                	<i></i>
                                	<span th:text="${ #strings.toString(v.increaseRate).substring(1)+'%'}"></span>
                                </span>
                            </div>

                            <div class="trendChartPrice" th:if="${#strings.toString(v.unitPrice) eq '0.0'}"><b th:text="${'&#45;&#45;&#45;&#45;'}"></b>
                            </div>
                            </dt>
                            <dd>
                                <div class="trendChartTitle" style="overflow: hidden;">
                                	<i style="background: #8cc9d5;float: left;margin-top: 7px;"></i>
                                	<div id ="region_name" style="float: left">铁西</div>
                                </div>
                                <div class="trendChartPrice">
                                	<b>￥8652元/㎡ </b>
                                	<div class="avediv">环比上月 </div>
                                	<span class="percent">
                                		<i></i>
                                		<span></span>
                                	</span>
                                </div>
                            </dd>
                            <dd>
                                <div class="trendChartTitle" style="overflow: hidden;">
                                	<i style="float: left;margin-top: 7px;"></i>
                                	<div id = "city_name" style="float: left">沈阳</div>
                                </div>
                                <div class="trendChartPrice"><b>￥8652元/㎡ </b>
                                	<div class="avediv">环比上月 </div>
                                	<span class="percent">
                                		<i></i>
                                		<span></span>
                                	</span>  
                                </div>
                            </dd>
                        </dl>
                        <div class="vs">vs</div>
                    </div>
                    <ul class="trendCharTime">
                        &lt;!&ndash;<li class="hover">6个月</li>&ndash;&gt;
                        &lt;!&ndash;<li >1年</li>&ndash;&gt;
                        &lt;!&ndash;<li>3年</li>&ndash;&gt;
                    </ul>
                    <div class="cl"></div>
                    <ul class="trendCharMap">
                        <li>
                            <div id="container1"></div>
                        </li>
                        &lt;!&ndash;<li>&ndash;&gt;
                        &lt;!&ndash;<div id="container2"></div>&ndash;&gt;
                        &lt;!&ndash;</li>&ndash;&gt;
                        &lt;!&ndash;<li style="display: none;">&ndash;&gt;
                        &lt;!&ndash;<div id="container3"></div>&ndash;&gt;
                        &lt;!&ndash;</li>&ndash;&gt;
                    </ul>
                    <div class="infuse">注：本站“均价”数据统计来自经纪人发布信息</div>
                </div>-->
                <script th:inline="javascript">
                    var highChartsData = [[${data}]];
                    var msg1 = JSON.parse(highChartsData[0].highChartsData);
                    var msg2 = JSON.parse(highChartsData[1].highChartsData);
                    var msg3 = JSON.parse(highChartsData[2].highChartsData);
                    $("#community_Name").text(highChartsData[2].keyName);
                    /* if(msg3.status == 0 || msg3.length == 0){
                     $(".trendCharContent dl dt div b").text("----");
                    }else{       
                     $(".trendCharContent dl dt div b").text("￥ "+msg3[0].unitPrice +"元/m²");
                     }*/
                    //                $(".trendCharContent dl:eq(0) dt div:eq(1) b").text(msg3[0].unitPrice);
                    $("#region_name").text(highChartsData[1].keyName);
                    $(".trendCharContent dl dd:eq(0) div b").text("￥"+msg2[1].unitPrice+"元/m²");
                    $("#city_name").text(highChartsData[0].keyName);
                    $(".trendCharContent dl dd:eq(1) div b").text("￥"+msg1[0].unitPrice+"元/m²");
                    //                if (msg3[0].unitPrice - msg3[1].unitPrice > 0) {
                    //                    $(".trendCharContent dl:eq(0) dt div:eq(1) span").text("比本小区环比高" + ((msg3[0].unitPrice - msg3[1].unitPrice) / msg3[1].unitPrice * 100).toFixed(2) + "%");
                    //                } else {
                    //                    $(".trendCharContent dl:eq(0) dt div:eq(1) span").text("比本小区环比低" + (-(msg3[0].unitPrice - msg3[1].unitPrice) / msg3[1].unitPrice * 100).toFixed(2) + "%");
                    //                    $(".trendCharContent dl:eq(0) dt div:eq(1) span").addClass("dis");
                    //                }
                    if (msg2[0].unitPrice - msg2[1].unitPrice > 0) {
                        $(".trendCharContent dl dd:eq(0) .percent span").text(((msg2[0].unitPrice - msg2[1].unitPrice) / msg2[1].unitPrice * 100).toFixed(2) + "%");
                        $(".trendCharContent dl dd:eq(0) .percent").addClass("up");
                        
                    } else {
                        $(".trendCharContent dl dd:eq(0) .percent span").text( (-(msg2[0].unitPrice - msg2[1].unitPrice) / msg2[1].unitPrice * 100).toFixed(2) + "%");
                        $(".trendCharContent dl dd:eq(0) .percent").addClass("dis");
                    }
                    if (msg1[0].unitPrice - msg1[1].unitPrice > 0) {
                        $(".trendCharContent dl dd:eq(1) .percent span").text( ((msg1[0].unitPrice - msg1[1].unitPrice) / msg1[1].unitPrice * 100).toFixed(2) + "%");
                        $(".trendCharContent dl dd:eq(1) .percent").addClass("up");
                        
                    } else {
                        $(".trendCharContent dl dd:eq(1) .percent span").text((-(msg1[0].unitPrice - msg1[1].unitPrice) / msg1[1].unitPrice * 100).toFixed(2) + "%");
                        $(".trendCharContent dl dd:eq(1) .percent").addClass("dis");
                    }

                    var length = 0;
                    if(msg3.content != null ){
                        length = msg1.length <= msg3.length ? msg1.length : msg3.length;
                    }else if(msg3.length < msg1.length){
                        length = msg3.length;
                    }else{
                        length = msg1.length;
                    }
                    var length2 = msg2.length ;
                    var length1 = msg1.length ;
                    var categories = new Array();
                    var data1 = new Array();
                    var data2 = new Array();
                    var data3 = new Array();

                    var temp = '';
                    if(msg1.length != 0 && msg2.length != 0 && msg3.length != 0){

                        for (i = 0; i < length; i++) {
//                        if (i % 4 == 0) {
                            var inArray = Highcharts.dateFormat('%y年%m月', msg1[i].trendingTime);
                            if(temp != inArray) {
                                temp = inArray;
                                categories.push(inArray);
                                data1.push(msg1[i].unitPrice)
                                data2.push(msg2[i].unitPrice)
                                if(msg3.length > 0 && i < msg3.length){
                                    data3.push(msg3[i].unitPrice)
                                }
                            }
//                        }
                        }
                    }else if(msg1.length == 0 || msg2.length == 0 || msg3.length == 0){
                        for (i = 0; i <length1; i++) {
                            var trendTime = Highcharts.dateFormat('%y年%m月', msg1[i].trendingTime);
                            if(temp != trendTime){
                                temp = trendTime;
                                categories.push(trendTime);
                                data1.push(msg1[i].unitPrice);
                            }
                        }
                        for (i = 0; i <length2; i++) {
                            var trendTime = Highcharts.dateFormat('%y年%m月', msg2[i].trendingTime);
                            if(temp != trendTime){
                                temp = trendTime;
                                categories.push(trendTime);
                                data2.push(msg2[i].unitPrice);
                            }
                        }
                        for (i = 0; i <length; i++) {
                            var trendTime = Highcharts.dateFormat('%y年%m月', msg3[i].trendingTime);
                            if(temp != trendTime){
                                temp = trendTime;
                                categories.push(trendTime);
                                if(msg3.length > 0){
                                    data3.push(msg3[i].unitPrice);
                                }
                            }
                        }
                    }
                    console.log(data3)
                    var series = [{
                        name: highChartsData[0].keyName,
                        data: data1.reverse(),
                    }, {
                        name: highChartsData[1].keyName,
                        data: data2.reverse(),
                    }, {
                        name: highChartsData[2].keyName,
                        data: data3.reverse(),
                    }]
                    //            var categories=['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                    categories = categories.reverse();
                    highchart('container1', series, categories)
                    //            highchart('container2',series,categories)
                    //            highchart('container3',series,categories)


                    function highchart(id, series, categories) {
                        Highcharts.chart(id, {
                            title: {
                                text: false
                            },
                            yAxis: {
                                title: {
                                    text: false
                                },
                                labels: {
                                    formatter: function () {
                                        return this.value + '元/m²';
                                    }
                                }
                            },
                            xAxis: {
                                categories: categories
                            },
                            legend: {
                                enabled: false
                            },
                            tooltip: {
                                crosshairs: true,
                                shared: true,
                                pointFormat: '<tspan style="fill:{series.color}" x="8" dy="15">●</tspan> <b>{series.name}</b> {point.y} 元/m²<br>'
                            },
                            plotOptions: {
                                spline: {
                                    marker: {
                                        radius: 4,
                                        lineColor: '#666666',
                                        lineWidth: 1
                                    }
                                }
                            },
                            series: series,
                            responsive: {
                                rules: [{
                                    condition: {
                                        maxWidth: 500
                                    },
                                    chartOptions: {
                                        legend: {
                                            layout: 'horizontal',
                                            align: 'center',
                                            verticalAlign: 'bottom'
                                        }
                                    }
                                }]
                            },
                            colors: ['#fe7175', '#8cc9d5', '#75d88f']
                        });
                    }
                </script>
            </th:block>
            <div class="w">

<!--                <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
                <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
<!--                <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css"/>-->

                <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=I3ZBZ-5K5WL-77WPV-M7G3Q-3Z2ZK-DVFNX"></script>
            </div>

        </div>

    </div>

        <div class="bigImg">
            <div>
                <ul>

                    <li th:each="vp:${vp}">
                        <img th:src="${vp.photoUrl  eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':vp.photoUrl}"
                             th:alt="${v.title+'园区图片'}"/>
                    </li>

                </ul>
            </div>
            <dl>
                <dt><img class="prev" src="https://static.fangxiaoer.com/web/images/ico/sign/white_ico.png"
                         style="display:none"/></dt>
                <dd><img class="next" src="https://static.fangxiaoer.com/web/images/ico/sign/white_ico.png"
                         style="margin-left: -161px;"/></dd>
            </dl>
            <span></span>

        </div>

        <script>
            var num = 0, maxNum = $(".bigImg li").length;
            $(".pic a").on("click", function () {
                num = $(this).index();
                if (num <= 0) {
                    $(".prev").hide()
                } else {
                    $(".prev").show()
                }
                if (num >= maxNum - 1) {
                    $(".next").hide()
                } else {
                    $(".next").show()
                }
                $(".bigImg ul").animate({"margin-left": num * -600 + "px"}, 10)
                $(".bigImg").fadeIn()
                $(".bigImg ul li img").each(function () {
                    console.log(Math.floor(-0.5 * parseInt($(this).height())))
                    $(this).css("margin-top", Math.floor(-0.5 * parseInt($(this).height())) + "px")
                })
            })
            $(".bigImg span").click(function () {
                $(".bigImg").fadeOut()
            })
            $(".bigImg .next").click(function () {
                if (num < maxNum - 1) {
                    num++
                }

                if (num <= 0) {
                    $(".prev").hide()
                } else {
                    $(".prev").show()
                }
                if (num >= maxNum - 1) {
                    $(".next").hide()
                } else {
                    $(".next").show()
                }
                $(".bigImg ul").animate({"margin-left": num * -600 + "px"}, 500)
            })
            $(".bigImg .prev").click(function () {
                if (num > 0) {
                    num--
                }
                if (num <= 0) {
                    $(".prev").hide()
                } else {
                    $(".prev").show()
                }
                if (num >= maxNum - 1) {
                    $(".next").hide()
                } else {
                    $(".next").show()
                }

                $(".bigImg ul").animate({"margin-left": num * -600 + "px"}, 500)
            })


        </script>
    </div>
    </div>
    <script language="javascript">


        $(".carousel-inner .item:eq(0)").attr("class", "item active");
        var n = $(".carousel-inner .item").length;
        $(".totalnumber").html(n + "");
        $(".number").html("1");
        var item1 = document.getElementsByClassName("item");
        for (i = 1; i <= item1.length; i++) {
            item1[i - 1].index = i;
        }
        $("#myCarousel1").on('slid', function () {
            var item2 = document.getElementsByClassName("item active")[0];
            $(".number").html(item2.index + "");

        });
//        $("#myCarousel1").carousel({
//            interval: 5000
//        })

    </script>


    <div class="cl"></div>

    <div th:include="fragment/fragment:: footer_detail"></div>
    <div th:include="fragment/fragment::esfCommon_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="house/detail/fragment_login::login"></div>
    <div class="bigImgShow" style="display: none;">
        <div class="showImg">
            <ul style="margin-left: -1170px;">


                <li th:each="vp,vpState:${vp}" th:if="${vpState.index &lt; 4}" class="span4">
                    <img  th:src="${vp.photoUrl  eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/big/noimage375275.jpg':vp.photoUrl}"
                          th:alt="${vp.photoName}" onload="imgSize()" style="height: 308px; width: auto; margin-left: -247px; margin-top: -154px;">
                </li>
            </ul>
        </div>
        <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"></div>
        <div class="prev"></div>
        <div class="next"></div>
    </div>
    <script type="text/javascript">
        photo.init()


    </script>
</form>

</body>
</html>
