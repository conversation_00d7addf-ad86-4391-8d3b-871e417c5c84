<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="'沈阳' + ${seoTitle + seoSubName} + '二手房_沈阳' + ${seoTitle + seoSubName} + '二手房出售_沈阳二手房买卖信息 - 房小二网'"></title>
    <meta name="keywords"
          th:content="'沈阳' + ${seoTitle + seoSubName} + '二手房,沈阳' + ${seoTitle + seoSubName} + '二手房价格,沈阳二手房信息,二手房中介,沈阳二手房网'"/>
    <meta name="description"
          th:content="'房小二网沈阳二手房为您提供海量真实的沈阳' + ${seoTitle + seoSubName} + '二手房信息，沈阳二手房经纪人信息，二手房源信息，及时的二手房出售信息以及二手房中介信息，带来最佳二手房买卖体验。'"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang2/'+mobileAgent}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2018.css?v=20190425"/>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css"/>
<body>
<style>
    .zsfw{margin-top: 0}
    .school_detailphoto a{float: right;margin-top: 0 !important;}
    #replie>div:first-child {
        padding-top: 20px;
    }
</style>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
<div th:include="plot/fragment_plotMenu::plotenu" th:with="type=2"></div>

<div id="option">
    <ul>
        <li><p>总价：</p>
            <a th:each="p:${price}" th:href="${p.url}" onclick="showIndex(this)"
               th:text="${#strings.toString(p.name).replaceAll('全部','不限')}" th:id="'p'+${p.id} "
               th:class="${p.selected}?'hover':''">></a>
            <div id="Search_PriceDomOk">
                <label><input name="minPrice" id="minPrice" maxlength="4" type="text" th:value="${minPrice}"> - <input
                        name="maxPrice" id="maxPrice" maxlength="4" type="text" th:value="${maxPrice}"> 万 <input
                        onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1"
                        id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;"
                        type="button"></label>
            </div>
        </li>
        <li><p>户型：</p>
            <a th:each="l:${room}" th:href="${l.url}" onclick="showIndex(this)"
               th:text="${#strings.toString(l.name).replaceAll('全部','不限')}" th:id="'l'+${l.id}"
               th:class="${l.selected}?'hover':''">></a>
        </li>
        <!--</div>-->
    </ul>
</div>
<div id="main" class="w main">
    <div id="left">
        <div class="cl"></div>
        <!--<div class="content">小二为你找到<i th:text="${msg}"></i>个符合条件的房源</div>-->

        <div class="contentMain" id="saleCol">
            <div class="warning" th:if="${#lists.isEmpty(secondhouse)}">
                <p>
                    很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                    懒得搜索？！<a th:href="@{'/helpSearch?ids=2'}" rel="2" dir="1" target="_blank">点击免费发布购房服务方案>></a>
                </p>
            </div>
            <!--以下为list形式-->
            <div id=replie class="house_left">
                <div class="inf" th:each="sh:${secondhouse}">
                    <a th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank"
                       class="infLeft">
                        <!--<i  th:if="${sh.picNum ne null and #strings.toString(sh.picNum).length() gt 1 or sh.picNum gt '4'}" th:text="${sh.picNum}"></i>-->
                        <img th:src="${#strings.isEmpty(sh.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sh.pic}"
                             th:alt="${sh.title}"/>
                        <div class="" th:if="${sh.isXiQue eq '1'}">佣金95折</div>
                    </a>
                    <div class="infCtn">
                        <a class="newHouseListTitle" th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank" >
                            <div th:text="${sh.title}"></div>
                            <i class="listIconBidPrice" th:if="${#strings.toString(sh.auction) eq '1'}"></i>
                            <i class="listIconIstop" th:if="${#strings.toString(sh.auction) ne '1' && sh.stickOrder eq '-1'}"></i>
                        </a>
                        <div  class="fourSpan">
                            <span><th:block th:text="${sh.room+'室'+sh.hall+'厅'+sh.toilet+'卫'}"></th:block></span>
                            <span><th:block th:text="${#strings.toString(sh.area).contains('.')? #strings.toString(sh.area).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.area}+'m²'"></th:block></span>
                            <span><th:block th:text="${sh.floorDesc+'/'+sh.totalFloorNumber+'层'}"></th:block></span>
                            <span th:if="${!#strings.isEmpty(sh.forward)}"><th:block th:text="${sh.forward}"></th:block></span>
                            <span th:if="${sh.buildDate ne null and sh.buildDate ne ''}" th:text="${sh.buildDate+'年建筑'}"></span>

                            <span class="personShow">
                            <i class="personIcon"></i>
                            <th:block th:text="${sh.agency}"></th:block>&nbsp;&nbsp;
                            <th:block  th:if="${!#strings.isEmpty(sh.spanTime)}" th:text="${sh.spanTime+'更新'}"></th:block>
                        </span>
                        </div>
                        <p  class="houseAddress"  th:if="${!#strings.isEmpty(sh.regionName) and !#strings.isEmpty(sh.subName) and !#strings.isEmpty(sh.plantName) and !#strings.isEmpty(sh.address)}">
                            <s th:if="${ !#strings.isEmpty(sh.subName)}" class="houseAddressSpance">
                                <a th:href="${'/saleVillages/'+sh.subID+'/index.htm'}" target='_blank' th:text="${sh.subName}"></a>
                            </s>
                            <s th:if="${!#strings.isEmpty(sh.regionName) and !#strings.isEmpty(sh.plantName) and !#strings.isEmpty(sh.address)}">
                                <a th:href="${'/saleHouses/r'+sh.regionId}" th:text="${sh.regionName}"></a>-
                                <a th:href="${'/saleHouses/j'+sh.PlatId+'-r'+sh.regionId}" th:text="${sh.plantName}"></a>-
                                <th:block th:text="${sh.address}"></th:block>
                            </s>
                        </p>
                        <div  class="houseItemIcon">
                            <div class="isGoodHouseIcon" th:if="${sh.isGoodHouse eq '-1'}"></div>
                            <th:block th:if="${sh.houseTrait ne null and sh.houseTrait ne ''}">
                            <span th:class="${'tese_'+i.count}"
                                  th:each="item,i:${#strings.toString(sh.houseTrait).split(',')}"
                                  th:if="${i.count le 3}" th:text="${item}"></span>
                            </th:block>
                        </div>
                        <!--<div th:style="${sh.memberType eq '个人'}?'display:block':'display:none'">-->
                            <!--<span style="background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale/iconAuthentichouse.gif) no-repeat 9px 3px; padding-left: 76px; font-size: 16px; color: #E04545; position: absolute; left: 260px; bottom: 2px;"-->
                                  <!--&gt;</span>-->
                        <!--</div>-->
                    </div>
                    <div class="infRight">
                        <p class="infRightPriseM" th:if="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')}"
                           th:text='面议'></p>
                        <p  class="infRightPrise" th:if="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}">
                            <th:block th:text="${#strings.toString(sh.price).contains('.')? #strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.price}"></th:block>
                            <i th:text="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')?'':'万'}"></i>
                        </p>
                        <p th:if="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}"
                           th:text="${#strings.toString(sh.unitPrice).contains('.')?#strings.toString(sh.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):sh.unitPrice}+'元/m²'"
                           th:style="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}?'display:block':'display:none'">
                        </p>
                        <a class="checkHouse" target="_blank" th:href="'/dealSales/-v'+${sh.subID}">查看同小区成交房源&gt;</a>
                        <a class="checkHouse" target="_blank" th:href="'/saleHouses/-v'+${sh.subID}">查看同小区房源&gt;</a>
                    </div>
                </div>
            </div>
            <div class="cl"></div>
        <div class="cl"></div>
    </div>
        <div class="page">

            <!-- AspNetPager V7.2 for VS2005 & VS2008  Copyright:2003-2008 Webdiyer (www.webdiyer.com) -->
            <div th:include="fragment/page :: page"></div>
            <!-- AspNetPager V7.2 for VS2005 & VS2008 End -->

        </div>
    </div>
        <div id="right">
            <!--列表页右侧我要卖房、房价评估-->
<!--            <div th:include="secondhouse/fragment::rightselling"></div>-->
            <!--右侧求购-->
            <!--<div th:include="secondhouse/fragment::rightwantbuy"></div>-->
            <!--二手房推荐-->
            <!--<div th:include="secondhouse/fragment::secondrecommend"></div>-->
        </div>
    <script type="text/javascript">
        function price(priceIdName) {
            $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
            $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
            var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
            var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
            if (num1 == "" && num2 != "") {
                $("#" + priceIdName + " input").eq(0).val("0");
                $("#" + priceIdName + " input").eq(2).show();
            } else if (num2 == "" && num1 != "") {
                $("#" + priceIdName + " input").eq(2).show();
            } else if (num1 != "" || num2 != "") {
                $("#" + priceIdName + " input").eq(2).show();
            } else {
                $("#" + priceIdName + " input").eq(2).hide();
            }
        }

        price("Search_PriceDomOk");
        $("#Search_PriceDomOk input").keyup(function () {
            price("Search_PriceDomOk");
        })
        $("#Search_PriceDomOk").keydown(function (event) {
            if (event.keyCode == 13) {
                // $("#Search_Btn_Search1").click()
            }
        });

        function __doPostBack(pager1, page) {
            var url = window.location.pathname;
            if (pager1 == "Search$Btn_Search1") {
                var priceBegin = $("#minPrice").val();
                var priceEnd = $("#maxPrice").val();
                var ref1 = url.replace(/-k[0-9]\d*/,''); //k最小值
                var ref2 = ref1.replace(/-x[0-9]\d*/,'');  //x最大值
                if (parseInt(priceEnd) < parseInt(priceBegin)){
                    priceEnd = [priceBegin,priceBegin=priceEnd][0];
                }
                if(priceBegin != "" && priceBegin != 0)
                    ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-k" + priceBegin;
                if(priceEnd != "")
                    ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-x" + priceEnd;
                location.href = ref2;
            }
            if (pager1 == "Search$Btn_Searchs") {
                var areaBegin = $("#minArea").val();
                var areaEnd = $("#maxArea").val();
                var ref1 = url.replace(/-y[0-9]\d*/,''); //y最小值
                var ref2 = ref1.replace(/-e[0-9]\d*/,'');  //e最大值
                if (parseInt(areaEnd) < parseInt(areaBegin)){
                    areaEnd = [areaBegin,areaBegin=areaEnd][0];
                }
                if(areaBegin != "" && areaBegin != 0)
                    ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-y" + areaBegin;
                if(areaEnd != "")
                    ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-e" + areaEnd;
                location.href = ref2;
            }
        }
    </script>

</div>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>

</html>