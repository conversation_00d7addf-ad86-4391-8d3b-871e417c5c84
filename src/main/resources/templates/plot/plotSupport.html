<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${v.title}+'生活配套_沈阳'+${v.title}+'_'+${v.title}+' - 房小二网'"></title>
    <meta name="keywords" th:content="${v.title}+'生活配套,沈阳'+${v.title}+'周边,'+${v.title}+'交通,沈阳楼盘信息'"/>
    <meta name="description"
          th:content="'房小二网为您提供海量的沈阳'+${v.title}+'信息，'+${v.title}+'房价，小区详情，周边配套，实景图，客观反映沈阳房价走势，为您选购沈阳二手房提供全方位参考。'"/>
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css"/>
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="Stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/sy/sale/subdistrict.css?t=20170520"/>
    <link rel="Stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/sy/index/idangerous.swiper.css"/>
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/vilageImage.css" rel="stylesheet" type="text/css">
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/villageContent.css" rel="stylesheet" type="text/css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/idangerous.swiper.min.js"></script>
    <link href="https://static.fangxiaoer.com/js/tc/tc.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleHoseView2018.css"/>
</head>
<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--搜索栏-->
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
<div th:include="plot/fragment_plotMenu::plotenu" th:with="type=5"></div>
<!--<script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
<script type="text/javascript"
        src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
<link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css"/>
<script th:inline="javascript">
    $(document).ready(function () {

        var id = [[${subId}]];
        var lng = [[${v.longitude}]];
        var lat = [[${v.latitude}]];
        var title = [[${v.title}]];
        var address = [[${v.address}]];
        var city = '沈阳';
        var content = "";
        $("#mapsubTitle").val(title);
        bdMap.init("memap", {
            id: id,
            houselng: lng,
            houselat: lat,
            radius: 2000,
            suofa: 14,
            bdtitle: title,
            bdcontent: content,
            address: address,
            city: city
        });
    });
</script>
<style>
    .zsfw {
        margin-top: 0
    }

    .villageImage > ul {
        height: 205px;
    }
</style>
<div class="w mian">
    <div th:include="secondhouse/fragment::MapZhouBianPeiTao1_mapDom"></div>
    <div class="cl"></div>
    <!--<script type="text/javascript">-->
        <!--$(".mapBtn").hide()-->
        <!--$(".canteen").css("height", "88px")-->
        <!--$(".canteen").css("overflow", "hidden")-->

        <!--window.onload = function () {-->

            <!--var houseNum = $(".canteen>div").length-->
            <!--clickInfo = !clickInfo-->
            <!--if (houseNum > 2) {-->
                <!--$(".mapBtn").show()-->
            <!--}-->
            <!--var clickInfo = true-->
            <!--$(".mapBtn").click(function () {-->
                <!--if (clickInfo) {-->
                    <!--$(".canteen").css("height", "auto")-->
                    <!--$(this).removeClass("mapBtnF")-->
                    <!--$(this).addClass("mapBtnS")-->
                    <!--clickInfo = !clickInfo-->
                <!--} else {-->
                    <!--$(".canteen").css("height", "88px")-->
                    <!--$(".canteen").css("overflow", "hidden")-->
                    <!--$(this).removeClass("mapBtnS")-->
                    <!--$(this).addClass("mapBtnF")-->
                    <!--clickInfo = !clickInfo-->
                <!--}-->
            <!--})-->

        <!--}-->
    <!--</script>-->


    <!--二手房-->
   <!-- <div class="villageImage house2" th:if="${!#lists.isEmpty(secondhouse)}">
        <div class="house2Title">二手房<a th:href="${'/saleHouses/-v'+subId+'-r'+v.regionId}"
                                       target="_blank">更多二手房></a></div>
        <ul>
            <li th:each="second:${secondhouse}">
                <div class="villageImages">
                    <a th:href="'/salehouse/'+${second.houseId}+'.htm'" target="_blank">
                        <img th:src="${#strings.isEmpty(second.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : second.pic}"
                             alt="${second.subName}">
                    </a>
                </div>
                <div class="villageHouseInfo">
                    <div class="villageTitle"><a th:href="'/salehouse/'+${second.houseId}+'.htm'" target="_blank"
                                                 th:text="${second.subName}"></a></div>
                    <div class="vallageInfo"><b
                            th:text="${second.room+'室'+second.hall+'厅'+second.toilet+'卫'}"></b><span
                            th:text="${second.price+'万元'}"></span></div>
                </div>
            </li>
        </ul>
    </div>-->
    <!--租房-->
    <div class="villageImage house2" th:if="${!#lists.isEmpty(rent)}">
        <div class="house2Title">租房<a th:href="${'/rents/-v'+subId+'-r'+v.regionId}" target="_blank">更多租房></a></div>
        <ul>
            <li th:each="rent:${rent}">
                <div class="villageImages">
                    <a th:href="'/rent/'+${rent.houseId}+'.htm'" target="_blank">
                        <img th:src="${rent.pic eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':rent.pic}"
                             th:alt="${rent.title}">
                    </a>
                </div>
                <div class="villageHouseInfo">
                    <div class="villageTitle"><a th:href="'/rent/'+${rent.houseId}+'.htm'" target="_blank"
                                                 th:text="${rent.subName}"></a></div>
                    <div class="vallageInfo"><b th:text="${rent.room+'室'+rent.hall+'厅'+rent.toilet+'卫'}"></b>
                        <span th:if="${#strings.toString(rent.price) ne '0'}"  th:text="${#strings.toString(rent.price)+'元/月'}"></span>
                        <span th:if="${#strings.toString(rent.price) eq '0'}"  th:text="${'面议'}"></span>
                    </div>
                </div>
            </li>
        </ul>
    </div>

    <div class="cl"></div>
</div>

<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
</html>