<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title th:text="${v.title}+'实景图_沈阳'+${v.title}+'_'+${v.title}+' - 房小二网'"></title>
    <meta name="keywords" th:content="${v.title}+',沈阳'+${v.title}+','+${v.title}+'项目介绍,'+${v.title}+'实景图'"/>
    <meta name="description"
          th:content="'房小二网为您提供海量的沈阳'+${v.title}+'信息，'+${v.title}+'房价，小区详情，周边配套，实景图，客观反映沈阳房价走势，为您选购沈阳二手房提供全方位参考。'"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/sub/'+subId+'.htm'}">
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css"/>
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/sale/subdistrict.css?t=20210507"/>
    	<!--<link rel="stylesheet" href="/css/subdistrict.css">-->
    
    <link rel="Stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/sy/index/idangerous.swiper.css"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/idangerous.swiper.min.js"></script>
    <link href="https://static.fangxiaoer.com/js/tc/tc.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>

</head>
<style>
    .zsfw {
        margin-top: 0
    }
    .sanfooter{
        position: absolute;
        /*bottom: 0;*/
        width: 100%;
    }
    .bigImg .prev {
        position: absolute;
        top: 50%;
        left: 0;
        width: 60px !important;
        margin-top: -57px;
        height: 114px;
        background: url(https://static.fangxiaoer.com/global/imgs/ico/white_ico1.png)no-repeat top left;
        cursor: pointer;
    }
    .bigImg .next {
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -57px;
        width: 64px;
        height: 114px;
        background: url(https://static.fangxiaoer.com/global/imgs/ico/white_ico1.png)no-repeat top right;
        cursor: pointer;
    }
    .bigImg .close {
        position: fixed;
        top: 0;
        right: 0;
        width: 50px;
        cursor: pointer;
        height: 50px;
        opacity: 0.8;
    }
    .span4 div{
    width: 274px;
    height: 205px;
    }
</style>
<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
<div th:include="plot/fragment_plotMenu::plotenu" th:with="type=4"></div>
<div class="w main">
    <div class="row pic">
        <a class="span4 " th:each="vp:${vp}" >
            <div th:style="${'background: url('+vp.photoUrl+') center no-repeat; background-size: cover;background-position: center;;'}">
            	 <!--<img th:src="${vp.photoUrl  eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':vp.photoUrl}"
                 th:alt="${vp.photoName}"/>-->
            	<!--<span th:text="${vp.photoName}"></span>-->
            </div>
           
        </a>
    </div>
    <div class="bigImg">
        <div class="showImg">
            <ul>
                <li th:each="vp:${vp}">
                    <img th:src="${vp.photoUrl  eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':#strings.toString(vp.photoUrl).replace('middle','big')}"
                         th:alt="${v.title+'园区图片'}"/>
                </li>
            </ul>
        </div>
        <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"/></div>
        <div class="prev"></div>
        <div class="next"></div>
    </div>

</div>
<script>
    var num = 0, maxNum = $(".bigImg li").length;
    $(".pic a").on("click", function () {
        num = $(this).index();
        if (num <= 0) {
            $(".prev").hide()
        } else {
            $(".prev").show()
        }
        if (num >= maxNum - 1) {
            $(".next").hide()
        } else {
            $(".next").show()
        }
        $(".bigImg ul").animate({"margin-left": num * -600 + "px"}, 10)
        $(".bigImg").fadeIn()
        $(".bigImg ul li img").each(function () {
            console.log(Math.floor(-0.5 * parseInt($(this).height())))
            $(this).css("margin-top", Math.floor(-0.5 * parseInt($(this).height())) + "px")
        })
    })
    $(".bigImg span").click(function () {
        $(".bigImg").fadeOut()
    })
    $(".bigImg .next").click(function () {
        if (num < maxNum - 1) {
            num++
        }

        if (num <= 0) {
            $(".prev").hide()
        } else {
            $(".prev").show()
        }
        if (num >= maxNum - 1) {
            $(".next").hide()
        } else {
            $(".next").show()
        }
        $(".bigImg ul").animate({"margin-left": num * -600 + "px"}, 500)
    })
    $(".bigImg .prev").click(function () {
        if (num > 0) {
            num--
        }
        if (num <= 0) {
            $(".prev").hide()
        } else {
            $(".prev").show()
        }
        if (num >= maxNum - 1) {
            $(".next").hide()
        } else {
            $(".next").show()
        }

        $(".bigImg ul").animate({"margin-left": num * -600 + "px"}, 500)
    })

    $(".close").click(function () {
        $(".bigImg").hide();
    });

</script>
<script th:inline="javascript">
    $(document).ready(function () {
       var size = [[${vp}]] ;
       if (size.length<4){
           $(".footer").addClass("sanfooter");
       }
    });

</script>
<div class="cl"></div>


<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>

</body>
</html>