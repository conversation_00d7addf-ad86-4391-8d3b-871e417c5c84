<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleVillageList.css"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <title th:text="${v.title+'提问-房小二网'}"></title>
</head>
<style>
    .zsfw{margin-top: 0}
</style>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--搜索栏-->
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
<div th:include="plot/fragment_plotMenu::plotenu" th:with="type=6"></div>
<div class="myAsk w mian">
    <div class="left">
        <input type="hidden" id="subId" th:value="${subId}"/>
        <input type="hidden" id="subName" th:value="${v.title}"/>
        <input th:value="${#session?.getAttribute('sessionId')}" id="sessionId" type="hidden"/>
        <div class="myAsk-main">
            <h4>请描述您的问题</h4>
            <textarea name="" rows="" id="content" cols="" placeholder="尽可能简短描述您的问题，以便得到专业的回答！"></textarea>
            <span style="float: right;" id="text-count"></span>
            <a  th:if="${#session?.getAttribute('sessionId')}" data-toggle="modal" id="subAsk" class="tijBtn" th:href="@{'javascript:subAsk();'}">提交问题</a>
            <a  th:if="${#session?.getAttribute('sessionId') eq null}" class="tijBtn" data-toggle="modal" href="#login">提交问题</a>
        </div>
    </div>
    <div th:include="plot/fragment_plotMenu::wenda"></div>


    <div class="cl"></div>
<script type="text/javascript">

    /*字数限制*/
    $("#content").on("input propertychange", function() {
        var $this = $(this),
            _val = $this.val(),
            count = "";
        if (_val.length < 51 && _val.length > 9) {
//            aler
            $(".tijBtn").addClass("hover")
        }
        if (_val.length > 50) {
            $this.val(_val.substring(0, 50));
        }
        count = 50 - $this.val().length;
        $("#text-count").text('还可输入'+count+'字');

    });
    function subAsk() {
        var sessionId = $("#sessionId").val();
        var subId = $("#subId").val();
        var subName = $("#subName").val();
        var content = $("#content").val();
        if (content.length <10 || content.length >50){
            alert("请填写10-50字");
            return;
        }
        if (sessionId == null || sessionId == undefined || sessionId == ''){
            $("#login").show();
            //登录弹出框
        }else {
            $.ajax({
                type:"POST",
                data:{
                    subId:subId,
                    subName:subName,
                    content:content,
                    sessionId:sessionId,
                },
                url:"/addAskWithSub",
                success:function (date) {
                    if (date.status == 1.0){
                        $("#tw").show();
                        $(".villageHeibu ").show();
                        setTimeout(function () {
                            window.location.href="/saleVillages/"+subId+"/index.htm";
                        },5000);
                        //成功弹出窗
                    }else {
                        alert(date.msg);
                    }
                },
                error:function () {
                    alert("网络延迟，请稍后再试！");
                }


            });
        }

    }
</script>
</div>
<div class="cl"></div>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>


</html>