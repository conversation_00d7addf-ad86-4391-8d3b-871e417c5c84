<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleVillageList.css"/>

    <title th:text="${v.title+'专家解读-房小二网'}"></title>
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
</head>
<style>
    .zsfw {
        margin-top: 0
    }
</style>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
<div th:include="plot/fragment_plotMenu::plotenu" th:with="type=7"></div>
<div class="w main">
    <div class="agentjdList">
        <h4>专家解读</h4>
        <ul class="agentjdList-main">
            <li th:each="ei:${expertInterpretations}">
                <div class="imgmian">
                    <a th:if="${#strings.toString(ei.isEnd) ne '0'}" th:href="${'/agent/second/'+ei.memberId}">
                        <img th:if="${!#strings.isEmpty(ei.avatar)}" th:src="${ei.avatar}" th:alt="${ei.realName}"/>
                        <img th:if="${#strings.isEmpty(ei.avatar)}" src="https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png" th:alt="${ei.realName}"/>
                        <h5 th:text="${ei.realName}">张雨生</h5>
                    </a>
                    <a th:if="${#strings.toString(ei.isEnd) eq '0'}" >
                        <img th:if="${!#strings.isEmpty(ei.avatar)}" th:src="${ei.avatar}" th:alt="${ei.realName}"/>
                        <img th:if="${#strings.isEmpty(ei.avatar)}" src="https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png" th:alt="${ei.realName}"/>
                        <h5 th:text="${ei.realName}">张雨生</h5>
                    </a>
                    <p th:if="${!#strings.isEmpty(ei.brandIntermediary)}" th:text="${ei.brandIntermediary}">喜鹊不动产</p>
                    <p th:if="${!#strings.isEmpty(ei.intermediaryName)}" th:text="${ei.intermediaryName}">喜鹊不动产瑞家店</p>
                </div>
                <div class="jdright">
                    <div class="title">
                        <p>发布于<span
                                th:text="${#strings.toString(ei.AddTime).replace('.','-')}">2018年4月12日16:57:58</span>
                        </p>
                        <!--<span><i>1</i>人觉得很赞</span>-->
                    </div>
                    <ul>
                        <li>
                            <i class="tese1">特色</i>
                            <div class="tsseMain">
                                <p th:if="${!#strings.isEmpty(ei.layout)}" th:text="${'【小区户型】'+ei.layout}">目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                <div class="cl"></div>
                                <p th:if="${!#strings.isEmpty(ei.matching)}" th:text="${'【生活配套】'+ei.matching}">
                                    目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                <div class="cl"></div>
                                <p th:if="${!#strings.isEmpty(ei.facilities)}" th:text="${'【小区设施】'+ei.facilities}">
                                    目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                <div class="cl"></div>
                                <p th:if="${!#strings.isEmpty(ei.traffic)}" th:text="${'【交通情况】'+ei.traffic}">
                                    目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                <div class="cl"></div>
                                <p th:if="${!#strings.isEmpty(ei.school)}" th:text="${'【附近学校】'+ei.school}">目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                <div class="cl"></div>
                                <p th:if="${!#strings.isEmpty(ei.other)}" th:text="${'【其他描述】'+ei.other}">目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                            </div>

                        </li>
                        <li th:if="${!#strings.isEmpty(ei.insufficient)}">
                            <i class="tese2">不足</i>
                            <div>
                                <p th:text="${'&nbsp;&nbsp;'+ei.insufficient}">小区商业还未完善，配套不成熟。</p>
                            </div>

                        </li>
                        <li class="agentListVilageImg" th:if="${!#lists.isEmpty(ei.pic)}">
                            <div th:each="pic:${ei.pic}"><img th:src="${pic.picUrl}" th:alt="${pic.picName}"></div>


                        </li>
                    </ul>
                    <a th:if="${#strings.toString(ei.isEnd) ne '0'}" th:href="${'/agent/second/'+ei.memberId}">查看他的二手房源></a>
                </div>
            </li>
        </ul>
    </div>
</div>
<script>
    $(".tc_close").click(function () {
        $(".villageHeibu").hide();
        $(".expertTc").hide();
    });
    function toAddOrder() {
        $(".villageHeibu").show();
        $(".expertTc").show();
    }
    function addOrder() {
        var length = $("#orderMobile").val();
        if (length.length != 11){
            alert("请输入正确手机号");
            return ;
        }
        var params = {
            type:9,
            phone: $("#orderMobile").val(),
            area:"无验证码订单",
        }
        $.ajax({
            type: "post",
            url: "/saveHouseOrder",
            data: JSON.stringify(params),
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            success: function(data) {
                if (data.status == 1) {
                    $(".villageHeibu").hide();
                    $(".expertTc").hide();
                    alert("订阅成功");
                } else if (data.status == 0) {
                    alert(data.msg);
                } else {
                    alert("提交失败！")
                }
            },
            error: function(data) {
                console.log(data)
            }
        });
    }
</script>
<div class="cl"></div>
<div class="page">
    <div th:include="fragment/page :: page"></div>
</div>
<div class="cl"></div>
<div class="cl"></div>

<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>

</html>