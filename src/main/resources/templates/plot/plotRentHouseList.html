<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'沈阳' + seoTitle + seoSubName + '租房_沈阳' + seoTitle + seoSubName + '租房出售_沈阳房屋出租 - 房小二网'}"></title>
    <meta name="keywords" th:content="${'沈阳' + seoTitle + seoSubName + '租房,沈阳' + seoTitle + seoSubName + '中介,沈阳租房'}"/>
    <meta name="description"
          th:content="${'房小二网沈阳租房频道为您提供海量真实的沈阳' + seoTitle + seoSubName + '租房信息，沈阳' + seoTitle + seoSubName + '房源信息，100%真实审核。租好房,上房小二网。100%真实审核。租好房,上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang3/'+mobileAgent}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2018.css?v=20190425"/>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <!--<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/zufang/list.css"/>-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/fxe_confirm.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/AjaxforJquery.js"></script>
    <!--
        <script type="text/javascript" src="https://static.fangxiaoer.com/js/esf_fxe_bnzf2017.js" ></script>
    -->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        $(function () {
            var r_or_s = location.pathname;
            if (r_or_s.indexOf('k1') != -1) {
                $("#btn2").attr("class", "hover");
                $("#Search_ditie").css('display', 'block');
            }
            else {
                $("#btn1").attr("class", "hover");
                $("#Search_zf").css('display', 'block');
            }
        })
    </script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css"/>

<body>
<style>
    /*.agentShowImg{*/
    /*    display: none;*/
    /*}*/
   .agentShowImg {
        display: inline-block;
        float: left;
        overflow: hidden;
        border-radius: 18px;
        margin-top: 3px;
       margin-right: 5px;
    }
     .agentShowImg img {
        display: inline-block;
        width: 18px;
        height: 18px;
        float: left;
    }
    .zsfw{margin-top: 0}
    .school_detailphoto a{float: right;margin-top: 0 !important;}
</style>

<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=3"></div>
<div th:include="plot/fragment_plotMenu::plotenu" th:with="type=3"></div>

<div id="option">
    <!--网页位置-->
    <ul>
        <li>
            <p>租金：</p>
            <a th:each="p:${price}" th:href="${p.url}" th:text="${#strings.toString(p.name).replaceAll('全部','不限')}"
               th:id="'p'+${p.id}" th:class="${p.selected}? 'hover':''">></a>
        </li>
        <script>
            $(function () {
                //户型和厅室的处理
                var url_b = location.pathname;
                var num_b = url_b.indexOf("w2");
                if (num_b == -1) {
                    $("#Search_room").show();
                    $("#Search_Bedroom").hide();
                } else {
                    $("#Search_room").hide();
                    $("#Search_Bedroom").show();
                }

            })
        </script>
        <li id="Search_RentType">
            <p>方式：</p>
            <a th:each="w:${way}" th:href="${w.url}" th:id="'w'+${w.id}" th:class="${w.selected}? 'hover':''">
                <th:block th:text="${#strings.toString(w.name).replaceAll('全部','不限')}"></th:block>
            </a>
        </li>
        <li id="Search_room">
            <p>户型：</p>
            <a th:each="l:${layout}" th:href="${l.url}" th:text="${#strings.toString(l.name).replaceAll('全部','不限')}"
               th:id="'l'+${l.id}" th:class="${l.selected}? 'hover':''">></a>
        </li>
    </ul>
</div>


<div id="main" class="w main">
    <div id="left">
        <!--<div class="tese">-->
            <!--<script>-->
                <!--$(".tese a").click(function () {-->
                    <!--if ($(this).attr("class") == null) {-->
                        <!--var teseid = $(this).attr("id").replace("tese", "");-->
                        <!--var tid = teseid - 1;-->
                        <!--var ref = location.pathname;-->
                        <!--ref = ref.replace(/[-]*[n][0-9]?$/, "");-->
                        <!--$(this).attr("href", ref + "-t" + tid);-->
                    <!--}-->
                    <!--else {-->
                        <!--var ref = location.pathname;-->
                        <!--var outid = $(this).attr("id").replace("tese", "");-->
                        <!--var outId = outid - 1;-->
                        <!--var outref = ref.replace("-t" + outId, "");-->
                        <!--$(this).attr("href", outref);-->
                    <!--}-->
                <!--})-->
            <!--</script>-->
        <!--</div>-->

        <!--<div class="content">小二为你找到<i th:text="${msg}"></i>个符合条件的房源</div>-->

        <!--没有房源-->
        <div class="warning" th:if="${#lists.isEmpty(rents)}">
            <p>
                很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                懒得搜索？！<a th:href="@{'/helpSearch?ids=3'}" rel="3" dir="2" target="_blank">点击免费发布求租服务方案>></a>
            </p>
        </div>
        <!--租房列表行级-->
        <div id="replie" class="house_left" th:include="fragment/rents::rents_line"></div>
        <script type="text/javascript">
            $(".esfListClose").click(function () {
                $("#esfListEwmBanner").hide()
            })
        </script>
        <div class="cl"></div>
        <div class="page">
            <div th:include="fragment/page :: page"></div>
        </div>
    </div>
    <script>
        $(function () {
            for (var i=0 ; i<30 ; i++){
                var isRoom = $("#isroom"+i).val();
                if (isRoom == ""){
                    $("#room"+i).show();
                    $("#bedroom"+i).hide();
                }else {
                    $("#bedroom"+i).show();
                    $("#room"+i).hide();
                }
            }

        })
    </script>
    <div id="right">
        <!--列表页右侧我要出租-->
        <div th:include="rent/fragment::wantrent"></div>
        <!--求租-->
        <!--<div class="zsfw" th:include="fragment/fragment::help_search_rent"></div>-->
        <!--租房推荐-->
        <!--<div th:include="rent/fragment::rentrecommend"></div>-->
    </div>
</div>
<div class="cl"></div>


<script>
    var i = 0;
    for (i = 0; i < $("#option_other .select_info").length; i++) {
        if ($("#option_other .select_box").eq(i).find(".hover").length > 0) {
            $("#option_other .select_info").eq(i).text($("#option_other .select_box").eq(i).find(".hover").text())
        }
    }
</script>

<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>


</body>
</html>