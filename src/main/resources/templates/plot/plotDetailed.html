<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <title th:text="${v.title}+'咨询_沈阳'+${v.title}+'_'+${v.title}+' - 房小二网'"></title>
    <meta name="keywords" th:content="${v.title}+'咨询,沈阳'+${v.title}+'怎么样,'+${v.title}+'答疑,沈阳楼盘信息'" />
    <meta name="description" th:content="'房小二网为您提供海量的沈阳'+${v.title}+'信息，'+${v.title}+'房价，小区详情，周边配套，实景图，客观反映沈阳房价走势，为您选购沈阳二手房提供全方位参考。'" />

    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleVillageList.css?date=201806011715"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

</head>
<style>
    .zsfw{margin-top: 0}
</style>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
<div th:include="plot/fragment_plotMenu::plotenu" th:with="type=6"></div>
<div class="villageDetailed w main">
    <div class="left">
        <div class="askMain">
            <h4 th:text="${'【'+plotAskDetail.subName+'】 '+plotAskDetail.askContent}">【城建花园】这里的住户基本是什么职业呢？白领集中么？ </h4>
            <ul>
                <li th:text="${plotAskDetail.realName}">小小小希</li>
                <!--<li>2017-12-11</li>-->
                <li th:text="${#strings.toString(plotAskDetail.askTime).replace('.','-')}">2018年4月11日15:56:57</li>

                <li>来自：<th:block th:text="${plotAskDetail.city}"></th:block></li>

            </ul>
            <div class="meAskBtn">
                <a th:if="${#session?.getAttribute('sessionId')} and ${(isAskUser eq 0)}" data-toggle="modal">我来帮他回答<i></i></a>
                <a th:if="${#session?.getAttribute('sessionId') eq null}" data-toggle="modal" href="#login">我来帮他回答<i></i></a>
                </div>
            <input type="hidden" id="askId" th:value="${plotAskDetail.id}"/>
            <input th:value="${#session?.getAttribute('sessionId')}" id="sessionId" type="hidden"/>
            <div class="meAsk">
                <textarea name="" rows="" id="content" cols="" placeholder="请输入回答内容"></textarea>
                <span style="float: right;" id="text-count"></span>
                <a  th:if="${#session?.getAttribute('sessionId')}" data-toggle="modal" class="tijBtn" th:href="@{'javascript:subReply();'}">提交答案</a>
                <a  th:if="${#session?.getAttribute('sessionId') eq null}" data-toggle="modal" href="#login" class="tijBtn">提交答案</a>

            </div>
        </div>
        <!--采纳答案-->
        <div class="adoptAskList" th:if="${!#lists.isEmpty(plotAskDetail.replyIsAccept)}">
            <h4 class="adoptTitle"><i class="adoptIconImg"></i>采纳答案</h4>
            <ul>
                <li  th:each="r:${plotAskDetail.replyIsAccept}">
                    <a th:if="${#strings.toString(r.isEnd) ne '0'}" th:href="${r.hasHouseFlag eq '4' ? '/agent/second/'+r.memberId : (r.hasHouseFlag eq '2' ? '/agent/rents/'+r.memberId : (r.hasHouseFlag eq '3' ? '/agent/shops/'+r.memberId : ('/agent/second/'+r.memberId )))}" target="_blank"><img
                            th:src="${r.pic ne '' ? r.pic : 'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png'}" alt=""/></a>
                    <a th:if="${#strings.toString(r.isEnd) eq '0'}"><img
                            th:src="${r.pic ne '' ? r.pic : 'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png'}" alt=""/></a>
                    <div class="agentAskListR">
                        <div>
                            <p th:if="${!#strings.isEmpty(r.realName) && #strings.toString(r.realName).length() &gt; 2}" th:text="${r.memberType ne '2' ? #strings.toString(r.realName).substring(0,1)+'***'+ #strings.toString(r.realName).charAt(#strings.toString(r.realName).length() - 1) : r.realName}">老师的</p>
                            <p th:if="${!#strings.isEmpty(r.realName) && #strings.toString(r.realName).length() &lt; 3}" th:text="${r.memberType ne '2' ? #strings.toString(r.realName).substring(0,1)+'***': r.realName}">老师的</p>
                            <i th:if="${r.memberType eq '2'}" class="agentAskListI">经纪人</i>

                                <span th:text="${#strings.isEmpty(r.tel) ? r.mobile : r.tel}" th:if="${r.memberType eq '2'}"></span>

                            <!--<span th:if="${#strings.isEmpty(r.tel)}" th:text="${r.mobile}">************转12345</span>-->
                        </div>
                        <p th:text="${r.replyContent}">可以，刚开始卖车位的时候8万一个，现在应该涨了</p>
                        <p class="time" th:text="${#strings.toString(r.replyTime).replace('.','-')}">2017-12-11 09:25:14</p>
                    </div>
                    <div class="cl"></div>
                </li>

            </ul>
        </div>
        <script type="text/javascript">
            /*字数限制*/
            $("#content").on("input propertychange", function() {
                var $this = $(this),
                    _val = $this.val(),
                    count = "";
                if (_val.length < 101 && _val.length > 0) {
//                    aler
                    $(".tijBtn").addClass("tjhover")
                }
                if (_val.length > 100) {
                    $this.val(_val.substring(0, 100));
                }
                count = 100 - $this.val().length;
                $("#text-count").text('还可输入'+count+'字');

            });
            function subReply() {
                var sessionId = $("#sessionId").val();
                var subId = $("#subId").val();
                var askId = $("#askId").val();
                var content = $("#content").val();
                if (content.length <1 || content.length >100){
                    alert("请填写1-100字");
                    return;
                }
//                if (sessionId == null || sessionId == undefined || sessionId == ''){
//                    $("#loginzhezhao").show();
//                    $("#login").show();
//                    //登录弹出框
//                }
                else {
                    $.ajax({
                        type:"POST",
                        data:{
                            askId:askId,
                            content:content,
                            sessionId:sessionId,
                        },
                        url:"/addReply",
                        success:function (date) {
                            if (date.status == 1.0){
                                $("#hf").show();
                                $(".villageHeibu ").show();
                                setTimeout(function () {
                                    window.location.href="/saleVillages/"+subId+"/index.htm";
                                },5000);
                                //成功弹出窗
                            }else {
                                alert(date.msg);
                            }
                        },
                        error:function () {
                            alert("网络延迟，请稍后再试！");
                        }
                    });
                }

            }
        </script>

        <div class="agentAskList" th:if="${!#lists.isEmpty(plotAskDetail.reply)}">
            <h4><span th:text="${plotAskDetail.isAccept eq '1' ? ('其它' + #lists.size(plotAskDetail.reply) +'个') :('全部' + plotAskDetail.count+'个')}">2个</span>回答</h4>
            <ul>
                <li  th:each="r:${plotAskDetail.reply}">
                    <a th:if="${#strings.toString(r.isEnd) ne '0'}" th:href="${r.hasHouseFlag eq '4' ? '/agent/second/'+r.memberId : (r.hasHouseFlag eq '2' ? '/agent/rents/'+r.memberId : (r.hasHouseFlag eq '3' ? '/agent/shops/'+r.memberId : ('/agent/second/'+r.memberId )))}" target="_blank"><img
                            th:src="${r.pic ne '' ? r.pic : 'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png'}" alt=""/></a>
                    <a th:if="${#strings.toString(r.isEnd) eq '0'}"><img
                            th:src="${r.pic ne '' ? r.pic : 'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png'}" alt=""/></a>
                    <div class="agentAskListR">
                        <div>
                            <p th:text="${r.realName}">张雨生</p>
                            <i th:if="${r.memberType eq '2'}" class="agentAskListI">经纪人</i>

                                <span th:text="${#strings.isEmpty(r.tel) ? r.mobile : r.tel}" th:if="${r.memberType eq '2'}"></span>

                            <!--<span th:if="${#strings.isEmpty(r.tel)}" th:text="${r.mobile}">************转12345</span>-->
                        </div>
                        <p th:text="${r.replyContent}">可以，刚开始卖车位的时候8万一个，现在应该涨了</p>
                        <p class="time" th:text="${#strings.toString(r.replyTime).replace('.','-')}">2017-12-11 09:25:14</p>
                    </div>
                    <div class="adoptIconK" th:if="${isAskUser eq 1 && plotAskDetail.isAccept ne '1'}">
                        <a id="isAccept" th:onclick="${'toIsAcceptAnswer('+r.id+','+plotAskDetail.id+')'}">采纳</a>
                        <p id="acceptMsg" style="display: none" >每个问题只能采纳一个答案哦</p>
                    </div>
                    <div class="cl"></div>
                </li>
                <input type="hidden" id="replyId" name="replyId">

            </ul>
        </div>
    </div>
    <div th:include="plot/fragment_plotMenu::wenda"></div>
    <div class="cl"></div>
    <div class="page">
        <div th:include="fragment/page :: page"></div>
    </div>


</div>

<!--采纳提示弹窗-->
<div class="modal-backdrop" style="display: none"></div>
    <div class="adoptTcmsg"  style="display: none">
        <h4>提示<img  src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="×" class="adoptTcmsgClose"></h4>
        <p>您确定要采纳该答案么</p>
        <a onclick="acceptAnswer();" class="adoptTcmsgIcon" style="background:#ff5200;cursor:pointer;">确定</a>
        <a id="noAccept" class="adoptTcmsgIcon" style="cursor:pointer;">取消</a>
    </div>


    <div class="adoptTcmsg1"  style="display: none">
        <h4>采纳成功！<img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="×" class="adoptTcmsgClose"></h4>
        <p>感谢您采纳满意回答，您的下次提问也将得到完美解决</p>
    </div>

<script>
    $(".adoptTcmsgClose").click(function () {
        $(".modal-backdrop").hide();
        $(".adoptTcmsg").hide();
        $(".adoptTcmsg1").hide();
    });
    $("#noAccept").click(function () {
        $("#replyId").val('');
        $(".modal-backdrop").hide();
        $(".adoptTcmsg").hide();
    });

    $('.adoptIconK>a').hover(function() {
//        $("#isAccept").next().css('display', 'block');
        $(this).parent().parent().find(".adoptIconK>p").show();
    }, function() {
        $(this).parents().parent().find(".adoptIconK>p").hide();
    });
    function toIsAcceptAnswer(replyId) {
        $("#replyId").val(replyId);
        $(".modal-backdrop").show();
        $(".adoptTcmsg").show();
    }

</script>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
<script type="text/javascript">
    $(".meAskBtn").click(function () {
        $(".meAsk").toggle(700)
    })
    function acceptAnswer() {

        $.ajax({
            type:"post",
            data:{
                sessionId:$("#sessionId").val(),
                replyId:$("#replyId").val(),
                askId:$("#askId").val(),
            },
            url:"/acceptReplay",
            success:function (data) {
                if (data.status == 0){
                    alert(data.msg);
                }else {
                    $(".modal-backdrop").show();
                    $(".adoptTcmsg1").show();
                    setTimeout(function(){
                        window.location.reload();
                    },1000);

                }
            },
            error:function () {
                alert("网络延时，请稍后再试！")
            }
        });
    }
</script>

</html>