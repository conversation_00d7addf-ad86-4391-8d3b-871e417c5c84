<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        .zsfw{margin-top: 0}
    </style>
</head>
<body>
<div th:fragment="plotenu">
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleVillages/">小区找房</a>
        ><a th:if="${!#strings.isEmpty(v.title)}" th:text="${v.title }">城建花园</a>
        <div id="main">
            <input type="hidden" id="subId" th:value="${subId}"/>
            <div class="listHeader">
                <h4 th:if="${!#strings.isEmpty(v.title)}" th:text="${v.title }">城建花园</h4>
                <p th:text="${'[地址] '+v.address}">沈阳市浑南区白塔大街26号<a class="MapVillage" th:href=""></a></p><a class="seemap" th:href="${'/saleVillages/'+subId+'/plotSupport.htm'}"><b class="listIcon listIcon_map"></b>查看地图</a>

            </div>
            <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
            <link rel="stylesheet" type="text/css" href="/css/saleVillageList.css"/>
            <ul class="villageNav" style="margin-bottom: 20px">
                <li th:class="${type} == 1?'hover':''"><a th:href="${'/saleVillages/'+subId+'/index.htm'}">小区概况</a></li>
                <li th:if="${!#strings.isEmpty(v.saleCount) and v.saleCount ne '0'}" th:class="${type} == 2?'hover':''"><a
                        th:href="${'/saleVillages/'+subId+'/plotSecondVillage/'}">二手房</a></li>
                <li th:if="${!#strings.isEmpty(v.rentCount) and v.rentCount ne '0'}" th:class="${type} == 3?'hover':''"><a th:href="${'/saleVillages/'+subId+'/plotRentVillage/'}">租房</a>
                </li>
                <li th:if="${!#lists.isEmpty(v.pic)}" th:class="${type} == 4?'hover':''"><a th:href="${'/saleVillages/'+subId+'/plotRealImage.htm'}">小区实景图</a></li>
                <li th:class="${type} == 5?'hover':''"><a th:href="${'/saleVillages/'+subId+'/plotSupport.htm'}">生活配套</a></li>
<!--                <li th:class="${type} == 6?'hover':''"><a th:href="${'/saleVillages/'+subId+'/plotAsk.htm'}">小区问答</a></li>-->
                <!--<li th:if="${!#lists.isEmpty(v.expertComment) }" th:class="${type} == 7?'hover':''"><a-->
                        <!--th:href="${'/saleVillages/'+subId+'/plotExpertInterpretation.htm'}">专家解读</a></li>-->
               <!-- <div class="export-phone" style="float: right">
                    <a th:if="${isUnscramble}" href="javascript:toAddOrder();" style="border: 1px solid #ff5200;padding: 3px 8px;border-radius: 4px;margin-left: 10px;cursor: pointer;color: #333">新房源通知</a>
                    <a th:if="${isUnscramble}" href="javascript:toAddOrder();" style="border: 1px solid #ff5200;padding: 3px 8px;border-radius: 4px;margin-left: 10px;cursor: pointer;color: #333">变价通知</a>
                </div>-->
            </ul>

        </div>
    </div>

    <div class="villageHeibu">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleVillageList.css"/>

        <!--<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>-->

        <!--专家解读页弹窗-->
        <div class="expertTc">
            <div class="tc_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div class="tcTitle"><span>手机订阅</span><a href="https://my.fangxiaoer.com/register" th:if="${#session?.getAttribute('sessionId') eq null}" target="_blank" class="ljzc">立即注册</a></div>
            <p>请填写您接收“<th:block th:if="${!#strings.isEmpty(v.title)}" th:text="${v.title}"></th:block>”新房源通知的手机地址</p>
            <div class="tcMain">
                <label for="">手机号：</label>
                <input type="" name="" id="orderMobile" value="" placeholder="请输入手机号" maxlength="11"/>
                <a href="javascript:addOrder();">立即确认</a>
            </div>
        </div>

    </div>


    <!--我要提问提完成功弹窗-->
    <div class="askTc" id="tw">
        <!--<div class="tc_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>-->
        <h5>问题已提交</h5>
        <p>审核通过将有专家对您的问题解答</p>
        <p>在<a href="https://my.fangxiaoer.com/village/interlocution">个人中心</a>查看情况</p>
        <a th:href="${'/saleVillages/'+subId+'/index.htm'}"><span>5s</span>后 返回小区概况</a>
    </div>

    <!--回复成功弹窗-->
    <div class="askTc" id="hf">
        <!--<div class="tc_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>-->
        <h5>回复已提交等待审核通过</h5>


        <a th:href="${'/saleVillages/'+subId+'/index.htm'}"><span>5s</span>后 返回小区概况</a>
    </div>
</div>
<div th:fragment="wenda">
    <div th:include="house/detail/fragment_login::login"></div>
    <div class="villageAskK">
        <link rel="stylesheet" type="text/css"
              href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleVillageList.css"/>
        <h4>房产专家</h4>
        <p>解决“房”事难题</p>
        <div>
            <a  th:if="${#session?.getAttribute('sessionId')}" data-toggle="modal" th:href="${'/saleVillages/'+subId+'/forAddPlotAsk.htm'}" style="color: #fff;background: #FF5200;">我要提问</a>
            <a th:if="${#session?.getAttribute('sessionId') eq null}" data-toggle="modal" th:href="${'/saleVillages/'+subId+'/forAddPlotAsk.htm'}" style="color: #fff;background: #FF5200;">我要提问</a>
            <a th:href="${'/saleVillages/'+subId+'/getAskNoReply.htm'}">我要回答</a>
        </div>
    </div>
</div>


</body>
</html>