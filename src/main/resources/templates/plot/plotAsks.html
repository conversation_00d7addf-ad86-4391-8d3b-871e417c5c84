<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="UTF-8">
		<title th:text="${v.title}+'咨询_沈阳'+${v.title}+'_'+${v.title}+' - 房小二网'"></title>
		<meta name="keywords" th:content="${v.title}+'咨询,沈阳'+${v.title}+'怎么样,'+${v.title}+'答疑,沈阳楼盘信息'" />
		<meta name="description" th:content="'房小二网为您提供海量的沈阳'+${v.title}+'信息，'+${v.title}+'房价，小区详情，周边配套，实景图，客观反映沈阳房价走势，为您选购沈阳二手房提供全方位参考。'" />

		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleVillageList.css"/>
		<link rel="Stylesheet" type="text/css"  href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css"/>
		<link rel="Stylesheet" type="text/css"  href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css"/>
		<link rel="Stylesheet" type="text/css"  href="https://static.fangxiaoer.com/web/styles/sy/sale/subdistrict.css?t=20170520"/>
		<link rel="Stylesheet" type="text/css"  href="https://static.fangxiaoer.com/web/styles/sy/index/idangerous.swiper.css"/>
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/modal.js"></script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/bootstrap.min.js" > </script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js"></script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/idangerous.swiper.min.js"></script>
		<link href="https://static.fangxiaoer.com/js/tc/tc.css" rel="stylesheet" type="text/css" />
		<script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
	</head>
	<style>
		.zsfw{margin-top: 0}
	</style>
	<body>
	<!--引入头部导航栏-->
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
	<!--搜索栏-->
	<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2"></div>
	<div th:include="plot/fragment_plotMenu::plotenu" th:with="type=6"></div>
		<div class="w main villageAsk">
			<div class="left" style="margin-bottom: 30px">
				<div class="myQuestionList">
					<h4>我的问题	<a th:if="${#session?.getAttribute('sessionId')}" th:href="${#session?.getAttribute('memberType') ne  '2.0' ? 'https://my.fangxiaoer.com/village/interlocution' : 'https://agent.fangxiaoer.com/village/interlocution'}" style="float: right;color: #ff5200;margin-right: 15px">查看更多 ></a></h4>
					<div class="myQuestionListN" th:if="${isExist eq '0' || #session?.getAttribute('sessionId') eq null}">
						<div>
							<p>您在该小区暂无提问</p>
							<!--<a href="">我要提问</a>-->
							<a th:if="${#session?.getAttribute('sessionId')}" data-toggle="modal" th:href="${'/saleVillages/'+subId+'/forAddPlotAsk.htm'}">我要提问</a>
							<a th:if="${#session?.getAttribute('sessionId') eq null}" data-toggle="modal" th:href="${'/saleVillages/'+subId+'/forAddPlotAsk.htm'}">我要提问</a>
						</div>
					</div>
					<div class="myQuestionListY" th:if="${isExist eq '1'}">

						<ul>
							<li>
								<div class="Q-A">
								<!--<h4>【城建花园】这里的住户基本是什么职业呢？白领集中么？ </h4>-->
									<a th:href="${'/saleVillages/'+myPlotAskInfo.id+'/getAskDetail.htm'}"><h4 th:text="${'【'+myPlotAskInfo.subName+'】 '+myPlotAskInfo.askContent}"></h4></a>
								<!--<p><span>答：</span>年轻人比较多，旁边软件园3万人</p>-->
								<!--<span>2018年4月11日15:56:57</span>-->
									<span th:if="${myPlotAskInfo.askTime}" th:text="${#strings.toString(myPlotAskInfo.askTime).replace('.','-')}"></span>
								</div>
								<a th:href="${'/saleVillages/'+myPlotAskInfo.id+'/getAskDetail.htm'}" class="iconQ-A" th:text="${#strings.isEmpty(myPlotAskInfo.count) ? '0' : myPlotAskInfo.count}">12</a>
							</li>

						</ul>
					</div>
				</div>
			</div>
			<div th:include="plot/fragment_plotMenu::wenda"></div>
				<div  class="left">


				<h4>本小区问答</h4>
				<ul th:if="${!#lists.isEmpty(plotAskInfo)}">
					<li th:each="p:${plotAskInfo}">
						<div class="Q-A">
							<h4><a th:href="${'/saleVillages/'+p.id+'/getAskDetail.htm'}"><th:block th:text="${'【'+p.subName+'】 '+p.askContent}"></th:block></a><s class="iconSolve2 iconSolve" th:if="${p.isAccept eq '1'}">已解决</s><s class="iconSolve1 iconSolve" th:if="${p.isAccept ne '1'}">待解决</s></h4>
							<p th:if="${p.replyContent}"><span>答&nbsp;:&nbsp;</span><th:block th:text="${p.replyContent}"></th:block></p>
							<span th:if="${p.replyTime1}" th:text="${#strings.toString(p.replyTime1).replace('.','-')}">2018年4月11日15:56:57</span>
							<span th:if="${#strings.isEmpty(p.replyTime1)}" th:text="${#strings.toString(p.askTime1).replace('.','-')}">2018年4月11日15:56:57</span>
						</div>
						<a th:href="${'/saleVillages/'+p.id+'/getAskDetail.htm'}" class="iconQ-A" th:text="${#strings.isEmpty(p.answerCount) ? '0' : p.answerCount}">12</a>
					</li>
				</ul>
				<img  th:if="${#lists.isEmpty(plotAskInfo)}" src="https://static.fangxiaoer.com/web/images/sy/sale/askDefault.jpg" alt="" class="askDefault">
			</div>
			<div class="cl"></div>
			<div class="page">
				<div th:include="fragment/page :: page"></div>
			</div>

			<div class="cl"></div>
		</div>
	<div class="cl"></div>
	<div th:include="fragment/fragment:: footer_detail"></div>
	<div th:include="fragment/fragment::esfCommon_meiqia"></div>
	<div th:include="fragment/fragment::tongji"></div>


	</body>
</html>
