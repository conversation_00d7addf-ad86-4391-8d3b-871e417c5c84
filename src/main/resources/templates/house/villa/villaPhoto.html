<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${houseInfo.projectName}+'_沈阳别墅_沈阳'+${houseInfo.projectName}+'相册 - 房小二网'"></title>
    <meta name="keywords" th:content="${houseInfo.projectName}+','+${houseInfo.projectName}+'图片,沈阳'+${houseInfo.projectName}+','+${houseInfo.projectName}+'实景图'">
    <meta name="description" th:content="'房小二网为您提供沈阳'+${houseInfo.projectName}+'别墅的详细效果图，实景图，使购房者犹如身临其境的了解项目，为挑选合适的户型提供详尽的参考。买别墅，看别墅，就上房小二网。'">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/nphotos/'+projectId+'-xgt-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />
    <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->

<!--    <link href="https://static.fangxiaoer.com/web/styles/sy/house/photo.css?t=20180918" rel="stylesheet" type="text/css" />-->

    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/imgShow.js?t=20170904"></script>
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?v=20180522"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/housesRight.css?v=20190219"/>

    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
    <link type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/Villa_index.css?v=20190916" rel="stylesheet" />
    <!--<link type="text/css" href="/css/Villa_index.css" rel="stylesheet" />-->
    <script src="https://static.fangxiaoer.com/js/imgload/liveSwiper.min.css"></script>
    <script src="https://static.fangxiaoer.com/js/imgload/liveSwiper.min.js"></script>
    <link rel="stylesheet" type="text/css" href="/css/photo.css" />
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>

    <script  type="text/javascript" >
        $(document).ready(function(){
            $(".showImg").show()
            $(".showList").hide()
            $(".imgList").show()
            $(".highDefinition").hide()
        });
    </script>
    <style type="text/css">
        #search2017 .search{
            margin-top: -4px !important
        }
        #search2017 .search img{
            top:17px;
        }
    </style>

</head>
<body class="w1210">
<form name="form1" method="post" action="12_0" id="form1">

    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

    <div class="cl"></div>
    <div th:include="house/villa/fragment_menu::newMenu" th:with="type=4"></div>

    <div class="cl"></div>
    <div class="w photo_wei">
        <div class="leftImg">
            <div class="imgList">
                <div class="position_pbl 111">
                    <ul class="bigImg">

                        <li th:each="photo:${projectAlbum}">
                            <div>
                                <img th:src="${photo.url}" th:alt="${photo.title}"   >
                                <th:block th:if="${photo.photoType eq 'wxt'}">
                                    <span th:class="${'type_sun wait_'+photo.state}"><th:block th:text="${photo.state eq '3'?'售罄':(photo.state eq '2'?'待售':'在售')}"></th:block></span>
                                </th:block>
                            </div>
                            <p th:text="${photo.title}"></p>
                        </li>
                    </ul>
                </div>
                <div class="imgPage">
                    <div class="homePage">首页</div>
                    <div class="prev">上一页</div>
                    <ul>
                        <li class="hover">1</li>
                        <li>2</li>
                        <li>3</li>
                        <li>4</li>
                        <li>5</li>
                    </ul>
                    <div class="next">下一页</div>
                    <div class="trailerPage">尾页</div>
                </div>
            </div>
            <div class="highDefinition">
                <div class="highImg">
                    <ul>
                        <li th:each="photo:${projectAlbum}">
                            <a class="" th:if="${photo.photoType eq 'zbkf'}" target="_blank" th:href="${'/liveDetail/' + photo.photoId + '.htm'}">
                                <i class="liveBtn"><i></i></i>
                                <div>
                                    <img th:src="${photo.url}" th:alt="${photo.title}" onload="imgShow.imgSize(this)" >
                                </div>
                            </a>
                            <a th:if="${photo.photoType eq 'swqj'}" th:href="${'/house/'+projectId+'-'+projectType+'/pic720/'+photo.photoId+'.htm'}" target="_blank">
                                <div>
                                    <img th:src="${photo.url}" th:alt="${photo.title}" onload="imgShow.imgSize(this)" >
                                </div>
                            </a>
                            <div th:unless="${photo.photoType eq 'swqj'}">
                                <img th:src="${photo.url}" th:alt="${photo.title}" onload="imgShow.imgSize(this)" >
                            </div>
                            <p th:text="${photo.title ne '' ? photo.title:''}"  style="text-align: left;padding-left: 10px"></p>
                        </li>
                    </ul>
                    <div class="highImgPrev"></div>
                    <div class="highImgNext"></div>
                </div>
                <div class="highList">
                    <div class="highListShow">
                        <ul>

                            <li th:each="photo:${projectAlbum}">
                                <div>
                                    <i class="smallIcon" th:if="${photo.photoType eq 'zbkf'}"><i></i></i>
                                    <img th:src="${photo.url}"  th:alt="${photo.title}">
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="highListPrev"></div>
                    <div class="highListNext"></div>
                </div>
            </div>

        </div>
        <div class="right_wei">
            <div class="photoSearch">
                <p class="showList">
                    <img src="https://static.fangxiaoer.com/web/images/ico/sign/photo_list_sun.gif" alt="相册" />列表查看
                </p>
                <!--<p style="display: none" class="showImg">-->
                <!--<img src="https://static.fangxiaoer.com/web/images/ico/sign/gaoqing.png" alt="相册" />高清大图-->
                <!--</p>-->

                <a th:href="'/house/album/'+${projectId +'-'+ projectType} + '.htm'"  th:class = "${#strings.isEmpty(photoType)}? 'hover':''">全部<span ></span><img src='https://static.fangxiaoer.com/web/images/ico/sign/rt_jt.png' ></a>
                <a th:each="colum:${albumColumn}" th:class="${colum.photoType == photoType}?'hover':''" th:href="'/house/album/'+${projectId +'-'+ projectType+'-'+ colum.photoType} + '.htm'" th:data="${colum.photoType}"> <th:block th:text="${colum.name}"></th:block>
                    <span th:text="'('+${colum.num}+')'"></span><img src='https://static.fangxiaoer.com/web/images/ico/sign/rt_jt.png'></a>
            </div>
        </div>

        <div class="clearfix"></div>
    </div>

    <script>
        $(".housesRight .subscriber ul li").hover(function(){
            $(".housesRight .subscriber ul li").removeClass("hover")
            $(this).addClass("hover")
        })
        $(".housesRight .subscription a").click(function(){
            if($(this).hasClass("hover")){
                $(this).removeClass("hover")
            }else{
                $(this).addClass("hover")
            }
            var txt = ""
            $(".housesRight .subscription ul li").each(function(){
                if($(this).hasClass("hover")){
                    txt+=$(this).attr("rel")+","
                }
            })
            console.log(txt)
        })
        $(document).ready(function () {
            var url = window.location.href;
            if(url.indexOf("zbkf") != '-1'){
                $(".imgList").hide()
                $(".showList").hide()
                $(".highDefinition").show()
            }
        })
    </script>
    <script>
        imgPage.init();
        imgShow.init();
        switchover.init()
        //图片自适应
        imgShow.imgSize();
    </script>
    <div class="cl"></div>

    <div class="cl"></div>
    <div th:include="fragment/fragment::footer_detail"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div class="cl"></div>


</form>

<!--<div th:include="house/detail/fragment_login::login"></div>-->
<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.masonry.min.js"></script>
<div class="modal-backdrop  in" id="loginzhezhao" style="display: none;"></div>

<div th:include="fragment/fragment::commonFloat"></div>
<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::unuseCode"  ></div>
<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode"  ></div>
</body>
</html>
