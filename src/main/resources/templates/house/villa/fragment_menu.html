<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="menu">

    <script src="https://static.fangxiaoer.com/js/audio.min.js"></script>
    <script>
        audiojs.events.ready(function () {
            audiojs.createAll();

            var aAudiojs = getByClass(document, 'audiojs');
            for (var i = 0; i < aAudiojs.length; i++) {
                var aAudio = aAudiojs[i].getElementsByTagName('audio')[0];
                var oSpan = aAudiojs[i].getElementsByTagName('span')[0];
                var fxg = '/';
                var url = aAudio.src;
                url = url.replace(/\\/g, fxg);
                url = url.split("/");
                this.playname = url[url.length - 1];
                oSpan.innerHTML = decodeURI(this.playname);
            }
        });
    </script>
    <div class="banner" th:style="'background:url('+${houseInfo.bgImage}+') center;margin-top:113px;'">
        <div class="mp3"><audio th:src="${houseInfo.music}" preload="playing" autoplay="autoplay"></audio></div>
    </div>
    <input type="hidden" id="glzs" th:value="${session.sessionId}" />
    <script language="javascript">
        $(function () {
            //别墅导航
            nav = $(".villa_nav li").length;
            for (t = 1; t <= nav; t++) {
                $(".villa_nav li:eq(" + t + ")").css("left", 155 * t);
            }

        });

    </script>
    <div class="w villa_nav">
        <ul>
            <li ><a th:href="${'/house/'+projectId+ '-2.htm'}"><img th:src="'https://static.fangxiaoer.com/web/images/sy/villa/share/t1' + ${type  == 1 ? '_hover.png' : '.png'}"></a></li>
            <li ><a th:href="${'/house/info/'+projectId + '-2.htm'}"><img th:src="'https://static.fangxiaoer.com/web/images/sy/villa/share/t2' + ${type  == 2 ? '_hover.png' : '.png'}"></a></li>
            <li ><a th:href="'/villa/layout/'+${projectId}+'.htm'"><img th:src="'https://static.fangxiaoer.com/web/images/sy/villa/share/t3' + ${type  == 3 ? '_hover.png' : '.png'}"></a></li>
            <li ><a th:href="'/villa/photos/'+${projectId}+'.htm'"><img th:src="'https://static.fangxiaoer.com/web/images/sy/villa/share/t4' + ${type  == 4 ? '_hover.png' : '.png'}"></a></li>
            <li th:if="${houseInfo.videoId}"><a th:href="${'/video/' + houseInfo.videoId + '.htm'}" target="_blank"><img th:src="'https://static.fangxiaoer.com/web/images/sy/villa/share/t6' + ${type  == 6 ? '_hover.png' : '.png'}"></a></li>
            <li th:if="${#strings.toString(houseInfo.haveDynamic) eq '1'}"><a th:href="${'/house/news/'+projectId + '-2.htm'}"><img th:src="'https://static.fangxiaoer.com/web/images/sy/villa/share/t9' + ${type  == 9 ? '.png' : '_hover.png'}"></a></li>
            <li th:if="${houseInfo.isPan ne '0'}" ><a th:href="${'/pic720/'+projectId + '-2.htm'}" target="_blank"><img th:src="@{'https://static.fangxiaoer.com/web/images/sy/villa/share/t12.png' }"></a></li>
        </ul>
    </div>
</div>
<div th:fragment="newMenu">
    <script src="https://static.fangxiaoer.com/js/Villa/Villa_index.js"></script>
    <script>
        var projectId = [[${projectId}]]
        var projectType = [[${projectType}]]
    </script>
    <div class="banner_wei">
        <div class="left_wei">
            <img th:src="${houseInfo.villaPic2}" />
            <div class="hei"></div>
        </div>
        <div class="Middle_wei">
            <img th:src="${houseInfo.villaPic1}" />
            <div class="hei">
                <h1 th:utext="${#strings.toString(houseInfo.projectName).replaceAll('·','<span>·</span>')}"></h1>
                <h2 th:text="${houseInfo.headTitle}"></h2>
                <img src="https://static.fangxiaoer.com/web/images/Villa/decorate.png" class="decorate" />
            </div>
        </div>
        <div class="right_wei">
            <img th:src="${houseInfo.villaPic3}" />
            <div class="hei"></div>
        </div>
        <div class="clearfix"></div>
        <!--判定用户登录状态-->
        <input type="hidden" id="glzs" th:value="${session.sessionId}" />
        <div class="pro_name" style="display: none">
            <p th:text="${houseInfo.projectName}"></p>
        </div>
    </div>
    <div class="nav_wei">
        <ul>
            <li>
                <a th:href="${'/house/' + projectId + '-2.htm'}" th:class="${type == 1?'hover':''}">项目主页</a>
            </li>
            <li>
                <a th:href="${'/house/'+projectId + '-2/info.htm'}" th:class="${type == 2?'hover':''}">基本信息</a>
            </li>
            <li>
                <a th:if="${houseInfo.areaList ne null and #lists.toList(houseInfo.areaList).size() > 0}" th:href="${'/house/'+ projectId + '-2/layout.htm'}"  th:class="${type == 3?'hover':''}">户型</a>
            </li>
            <li>
                <a th:href="${'/house/'+ projectId + '-2/album.htm'}"  th:class="${type == 4?'hover':''}">相册</a>
            </li>
            <li th:if="${houseInfo.isPan ne '0'}">
                <a th:href="${'/house/'+projectId + '-2/pic720.htm'}"   th:class="${type == 6?'hover':''}">全景看房</a>
            </li>
            <li th:if="${houseInfo.videoId}" >
                <a  th:href="${'/video/' + houseInfo.videoId+'-'+projectId+ '.htm'}"  target="_blank" th:class="${type} == 7?'hover':''">
                    视频</a>
            </li>

            <li th:if="${#strings.toString(houseInfo.haveDynamic) eq '1' and houseInfo.projectStatus ne '3'}">
                <a th:href="${'/house/'+projectId + '-2/news.htm'}"   th:class="${type == 5?'hover':''}">楼盘动态</a>
            </li>

            <div class="clearfix"></div>
        </ul>
    </div>
    <div class="error_bieshu">
        <div class="error_button">纠错</div>
        <div class="error_tion" style="top:24px;right:0;">
            <div class="Corrected">
                <div class="tlt">
                    <p><span th:text="${houseInfo.projectName}"></span>信息纠错</p>
                    <div class="shut_down"></div>
                </div>
                <div class="error_conut">
                    <dl>
                        <dd>
                            <span>参考价格：</span>
                            <input class="seasoning_name" type="text" id="reference_price" maxlength="30">
                        </dd>
                        <dd>
                            <span>开盘时间：</span>
                            <input class="seasoning_name" type="text" id="opening_time" maxlength="30">
                        </dd>
                        <dd>
                            <span>项目地址：</span>
                            <input class="seasoning_name" type="text" id="project_address" maxlength="30">
                        </dd>
                        <dd>
                            <span>交通信息：</span>
                            <input class="seasoning_name" type="text" id="traffic_information" maxlength="30">
                        </dd>
                        <dd>
                            <span>户型信息：</span>
                            <input class="seasoning_name" type="text" id="apartment_information" maxlength="30">
                        </dd>
                        <dd>
                            <span>售楼电话：</span>
                            <input class="seasoning_name" type="text" id="sales_call" maxlength="30">
                        </dd>
                        <dd>
                            <span>其&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;他：</span>
                            <input class="seasoning_name" type="text" id="other" maxlength="30">
                        </dd>
                        <dd>
                            <span>我的姓名：</span>
                            <input class="seasoning_name" type="text" id="my_name" maxlength="30">
                        </dd>
                    </dl>
                </div>
                <div class="error_di">
                    <input type="button" id="error_submission" value="提交">
                    <input type="button" id="error_cancel" value="取消">
                </div>
            </div>
        </div>
    </div>
    <script>
        var x=0
        x=$(".nav_wei ul li").length
        var y=0
        for (var i=0;i<x;i++){
            y=y+=$(".nav_wei ul li").eq(i).outerWidth()


        }
        $(".nav_wei").width(y)

        $(".error_button").click(function () {
            $(".error_tion").show();
            var errname = $(".Corrected .tlt p").width();
            if ( errname > 266){
                $(".Corrected .tlt p span").addClass("evspan");
            }
        })
        $(".shut_down,#error_cancel").click(function () {
            $(".error_tion").hide();
            $("#reference_price").val("");
            $("#opening_time").val("");
            $("#project_address").val("");
            $("#traffic_information").val("");
            $("#apartment_information").val("");
            $("#sales_call").val("");
            $("#other").val("");
            $("#my_name").val("");
        })
        $("#error_submission").click(function () {
            var sessionId = $("#sessionId").val();
            var reference_price = $("#reference_price").val();
            var opening_time = $("#opening_time").val();
            var project_address = $("#project_address").val();
            var traffic_information = $("#traffic_information").val();
            var apartment_information = $("#apartment_information").val();
            var sales_call = $("#sales_call").val();
            var other = $("#other").val();
            var my_name = $("#my_name").val();
            var seasoning_name = "";
            $('.seasoning_name').each(function(){
                seasoning_name += $(this).val();
            })
            if(seasoning_name == ""){
                alert("至少填写一项！");
                return false;
            }else {
                $.ajax({
                    type: "POST",
                    data: {
                        projectId:projectId,
                        sessionId : sessionId,
                        memberName : my_name,
                        price : reference_price,
                        panTime : opening_time,
                        projectAddress : project_address,
                        trafficInfo : traffic_information,
                        layoutInfo : apartment_information,
                        sortTel : sales_call,
                        otherInfo : other
                    },
                    url: "/saveCollection",
                    success: function (data) {
                        if(data.status == 1){
                            alert("提交成功");
                            $("#reference_price").val("");
                            $("#opening_time").val("");
                            $("#project_address").val("");
                            $("#traffic_information").val("");
                            $("#apartment_information").val("");
                            $("#sales_call").val("");
                            $("#other").val("");
                            $("#my_name").val("");
                            $(".error_tion").hide()
                        }else{
                            // alert('提交失败');
                            alert(data.msg)
                        }
                    }
                });
            }
        })
    </script>
    <div class="line_wei" style="margin-top: 10px;"></div>
    <div class="play_box" th:if="${type != 6}">
        <div class="player">
            <h1>00:00</h1>
        </div>
        <div class="track_box">
            <div class="progress">
                <div class="spot"></div>
            </div>
        </div>
        <div class="time_wei">
            <h1>03:21</h1>
        </div>
        <audio th:src="${houseInfo.music}" id="media" autoplay="autoplay" loop="loop">

        </audio>
        <div class="suspend">
            <img src="https://static.fangxiaoer.com/web/images/Villa/play.png">
            <img src="https://static.fangxiaoer.com/web/images/Villa/suspend.png">
        </div>
        <div class="clearfix"></div>
    </div>
</div>

</body>


</html>