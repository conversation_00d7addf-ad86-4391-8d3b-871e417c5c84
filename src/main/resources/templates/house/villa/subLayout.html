<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"
      xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link rel="stylesheet" href="https://cdn.static.runoob.com/libs/bootstrap/3.3.7/css/bootstrap.min.css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/bootstrap.min.js"></script>
    <style>
        .carousel-controlLeft{
            position: absolute;
            top: 50%;
            left: 0;
            width: 60px;
            margin-top: -57px;
            height: 114px;
            background: url(https://static.fangxiaoer.com/global/imgs/ico/white_ico1.png)no-repeat top left;
            cursor: pointer;
        }
        .carousel-controlRight{
            position: absolute;
            right: 0;
            top: 50%;
            margin-top: -57px;
            width: 64px;
            height: 114px;
            background: url(https://static.fangxiaoer.com/global/imgs/ico/white_ico1.png)no-repeat top right;
            cursor: pointer;
        }
    </style>
</head>
<body>
<div id="myCarousel" class="carousel slide">
    <!-- 轮播（Carousel）指标 -->
    <ol class="carousel-indicators">
        <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
        <li data-target="#myCarousel" data-slide-to="1"></li>
        <li data-target="#myCarousel" data-slide-to="2"></li>
    </ol>
    <!-- 轮播（Carousel）项目 -->
    <div class="carousel-inner">
        <div class="item" th:each="item:${layout}">
            <img th:src="${item.imageUrl}" th:alt="${item.description}">
        </div>
    </div>
    <!-- 轮播（Carousel）导航 -->
    <a class="carousel-controlLeft" href="#myCarousel"
       data-slide="prev"></a>
    <a class="carousel-controlRight" href="#myCarousel"
       data-slide="next"></a>
</div>
<script>
    $(function () {
        $(".carousel-inner div:eq(0)").addClass("active");
    })
</script>
</body>
</html>