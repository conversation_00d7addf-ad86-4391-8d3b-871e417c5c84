<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"      xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title th:text="${houseInfo.projectName}+'_沈阳别墅_沈阳'+${houseInfo.projectName}+' - 房小二网'"></title>
		<meta name="keywords" th:content="${houseInfo.projectName}+',沈阳别墅,沈阳'+${houseInfo.projectName}+',沈阳在售别墅'" />
		<meta name="description" th:content="'房小二网为您提供详细的沈阳'+${houseInfo.projectName}+'别墅信息，户型图与实景图，售价与优惠政策等关键信息，买别墅，看别墅，就上房小二网。'" />
		<meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
		<link type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/Villa_indexs.css?v=2022" rel="stylesheet" />
		<!--<link type="text/css" href="/css/Villa_index.css" rel="stylesheet" />-->
		<script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />
		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/appointment.css?v=20180522" rel="stylesheet" type="text/css">
		<!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/default.css?v=20180522"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/userEvaluatePX.css?v=20180911" />
		<link href="https://static.fangxiaoer.com/web/styles/new_sy/villa/view.css?t=20180502" rel="stylesheet" type="text/css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
        <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
        <script src="/js/AjaxforJquery.js" type="text/javascript"></script>
		<script src="/js/jquery.cookie.js" type="text/javascript"></script>
		<script src="https://static.fangxiaoer.com/js/new_video/video.min.js"></script>
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/video-js.min.css">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/condoToursPrivate.css" />
		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/payment.css" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/main2017.css" />



		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynatown.css?v=20200417" rel="stylesheet" type="text/css" />
		<!--<script src="/js/house/detail/layoutCompare.js" type="text/javascript"></script>-->
		<!--<script src="https://cdn.bootcss.com/js-cookie/2.2.1/js.cookie.min.js"></script>-->
		<style type="text/css">
			.video-js .vjs-big-play-button .vjs-icon-placeholder:before,.vjs-icon-play:before{content:none!important}
			.video-js .vjs-big-play-button{background:0 0!important;width:100px!important;height:100px!important;margin-top:-50px!important;margin-left:-50px!important;border-radius:50%!important;background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png)!important;background-size:100%!important}
			.newHouseViewChunk{margin:28px auto;width:1200px}
			.deepAnalysis{margin-top:31px;margin-bottom:34px}
			.analysisTitle{height:53px;text-align:center}
			.analysisTitle h1{font-size:28px;color:#333}
			.analysisInfo{height:100%;padding:20px 47px 20px 50px;background-color:#fff}
			.analysisInfoImg{width:202px;height:100%;float:left}
			.analysisInfoTxt{width:901px;height:100%;float:right}
			.analysisInfoTxt h2{font-size:20px;font-weight:700;padding-top:8px}
			.analysisInfoTxt h2:hover{color:#ff5200}
			.analysisInfoTxt p{line-height:26px;font-size:14px;margin-top:18px;color:#666;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical}
			.analysisInfoTxt p:hover{color:#ff5200}
			#allText{float:right;color:#ff5200;background:#fff;margin-top:-19px;position:relative;width:96px;display:none}
			#allText span:first-child{color:#333}
			.analysisSquare{width:110px;height:33px;background-color:#f60;color:#fff;font-size:16px;text-align:center;line-height:33px}
			.content_box_title{width:1200px;margin:0 auto}
			.content_box_title .title_box h1{font-size:28px;color:#333;text-align:center;padding-top:34px}
			.sy-ZKclear{border:none;background:#fff;margin-top:33px}
			.sy-ZKclear-ararow{position:absolute;width:71px;height:28px;margin-left: 1132px;;margin-top:-15px;cursor:pointer}
			.sy-ZKclear-ararow span {background-image: url(https://static.fangxiaoer.com/web/images/sercoudo/v-left-arrow-hover.png);}
			.active-d {background-image: url(https://static.fangxiaoer.com/web/images/sercoudo/v-left-arrow.png)!important;}
			.w:last-child {border-bottom: 1px solid #ebebeb !important;}
			.sy-dynatown-ararow{position: absolute;right: 18px;top: 21px;}
			.sy-dynatown{background: #ffffff;}
			.w_cont{
				width: 1200px !important;
			}
			.n20 #close_C{width: 210px !important;}


			#vrK.liang .vrs{ width: 80px !important; height: 80px !important; position: absolute; top: 0 !important; bottom: 0 !important; left: 0 !important; right: 0 !important; margin: auto !important; z-index: 12; }
			#vrK.liang .vrs i{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 3; display: block; background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;}
			#vrK.liang .vrs b{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 1; display: block;  border-radius: 50%; background-color: rgba(0,0,0,0.5);}


			.vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
				background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}

		.newPhone{

			display: flex;
			align-items: center;
			width: 100% !important;
			padding-left: 0px !important;
			padding-right: 0px !important;
			left: 0px !important;
		}
			.iphcon{
				top: 23px !important;


				position: unset !important;
			}
			.newPhone p{
				margin-top: 0px !important;
			}

			.seeEwm{
				margin-bottom: 10px !important;
			}
			.sepp{
				position: relative !important;
				text-align: center !important;
				left: 0px !important;
			}
			.project_main{


				background: #FFFFFF;
			}


			.ph-note{

				font-size: 14px;
				font-family: Microsoft YaHei-Regular, Microsoft YaHei;
				font-weight: 400;
				color: #666666;
				line-height: 19px;

				margin-top: 3px;
				margin-left: 23.9px;
			}
			#liveK.liang .zbkfDetailBTn {
				width: 80px;
				height: 80px;
				display: block;
				position: absolute;
				left: 50%;
				top: 50%;
				margin-left: -40px;
				margin-top: -40px;
				background-image: url(https://static.fangxiaoer.com/images/coin/liveBtn1.gif);
				background-size: 60% 60%;
				border: 1px solid #ccc;
				background-color: rgb(0 0 0 / 50%);
				border-radius: 40px;
				background-position: center;
				background-repeat: no-repeat;
				z-index: 12;
			}

			.sy-dynatown .sy-single{
				width: 20% !important;

				display: block !important;
			}
			.new-sy-single:nth-child(4n) .news-name{
				border-right: 1px solid #eee !important;
			}
			.new-sy-single:nth-child(5n) .news-name{
				border-right: none;
			}
			.new-sy-single:nth-child(6n){
				display: none;
			}
			.sy-dynatown .news-name{
				width: 139px !important;
			}
			.news-touxiang{
				margin-left: 15px !important;
				margin-right: 15px !important;
			}
			.sy-dynatown .news-name>span{
				white-space: nowrap;
			}
			.new-sy-single:last-child .news-name {
				border-right: none !important;
			}
		</style>
		<link href="https://static.fangxiaoer.com/web/images/sy/selection/g_sharing.css" rel="stylesheet" type="text/css"/>
		<script src="https://static.fangxiaoer.com/web/images/sy/selection/stars.js"></script>
		<script>
			$(document).ready(function(){
				function jqrcodeFn(d,w,h){
					var qrWidth = w;
					var qrHeight = h;
					var logoQrWidth = qrWidth / 4;
					var logoQrHeight = qrHeight / 4;
					var g_a='#'+d;
					var g_b='#'+d+' canvas';
					var g_c='.'+d;
					var g_qurl='https://m.fangxiaoer.com' //上线后 改为m站小二甄选楼盘评测信息页
					$(g_a).qrcode({
						render: "canvas",    //设置渲染方式，有table和canvas
						text: g_qurl,
						width: qrWidth, //二维码的宽度
						height: qrHeight //二维码的高度
					})
					$(g_b)[0].getContext('2d').drawImage($("#g_clogo")[0], (qrWidth - logoQrWidth) / 2, (qrHeight - logoQrHeight) / 2, logoQrWidth, logoQrHeight);
					$(g_c).show()
				}
				jqrcodeFn('g_qrcode','64','67')
				jqrcodeFn('g_hcode','130','130')

				$(".g_dhv").mouseover(function () {
					$(".g_hdcode").show()
				})
				$(".g_dhv").mouseleave(function () {
					$(".g_hdcode").hide();
				})
			})
		</script>
		<script th:inline="javascript">
            var pcVideoTab = [[${houseInfo.videoTabId}]];

			var s = [[${session.muser}]];
			var sid = [[${session.sessionId}]]
			var sph = [[${session.phoneNum}]];

			function filterPhoneNumber(phoneNumber) {
				let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
				//校验手机号是否正确
				if (reg.test(phoneNumber)) {
					phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
					return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
				} else {
					return ''
				}
			}
		</script>
	</head>
	<body>
	<img src="https://static.fangxiaoer.com/web/images/sy/selection/logo.png" id="g_clogo" style="display: none;"/>

		<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
		<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>
		<div th:include="house/villa/fragment_menu::newMenu" th:with="type=1"></div>
		<div class="modal-backdrop  in" id="loginzhezhao" style="display: none"></div>
		<div class="information_wei">
			<div class="content_box">
				<div class="photo_wei">
					<div th:if="${houseInfo.hasLabel eq '1'}" class="instantDetail"><img th:src="${houseInfo.labelContent.advertisePC}" alt=""></div>
					<div class="photo">
						<th:block th:if="${houseInfo.liveInfo.isLive eq '1'}">
							<a class="liang" th:href="${'/liveDetail/' + houseInfo.liveInfo.liveId + '.htm'}" id="liveK">
								<div class="zbkfDetailBTn"></div>
								<img class="forImage" th:src="${houseInfo.liveInfo.livePic}" th:alt="直播看房"/>
							</a>
						</th:block>
						<!--全景-->
						<th:block th:if="${handlePics?.panPic ne null}">
							<a th:href="${'/house/'+projectId+'-2/pic720.htm'}" id="vrK">
								<div class="vrs" ><i></i><b></b></div>
								<img class="forImage" th:src="${handlePics.panPic?.url}" th:alt="全景"/>
							</a>
						</th:block>
						<!--视频-->
						<th:block th:if="${handlePics?.videoPic ne null}">
							<div class="forImage" id="player"
								 th:style="${'height:460px;background:url(' + houseInfo.defaultPic +')no-repeat;background-size:750px 460px;'
								            + 'opacity:' + (handlePics?.panPic ne null ? '0;' : '1;')}">
								<video id="my-video" class="video-js vjs-big-play-centered" controls preload="auto" width="750px;" height="408px"
									   th:poster="${houseInfo.defaultPic}" data-setup="{}">
									<source th:src="${houseInfo.mobileVideoUrl}">
								</video>
							</div>
						</th:block>

						<th:block th:if="${handlePics?.pics ne null}">
							<th:block th:each="showPics, i:${handlePics?.pics}">
								<img class="forImage" th:src="${showPics.url}" th:alt="${showPics.title}"
                                     th:style="${'opacity:' + (i.index eq 0 and handlePics?.panPic eq null and handlePics?.videoPic eq null ? '1;' : '0;')}"/>
							</th:block>
						</th:block>
					</div>
					<div class="layer">
						<div class="name">
							<a href="#" ></a>
						</div>
						<div class="btn">
							<ul>
								<li th:if="${houseInfo.liveInfo.isLive eq '1'}"></li>
								<li th:if="${houseInfo.isPan ne '0'}"></li>
								<li th:if="${houseInfo.videoTabId ne '0'}"></li>
								<li th:each="showPics:${houseInfo.showPics}"></li>
								<div class="clearfix"></div>
							</ul>
						</div>
						<div class="clearfix"></div>
					</div>
				</div>
				<div class="information_sun" style="position: relative;">
					<div class="name_box">
						<div class="name">
							<th:block th:unless="${#strings.toString(houseInfo.projectName).indexOf('·') == -1 ? #strings.toString(houseInfo.projectName).length() > 8: #strings.toString(houseInfo.projectName).length() > 7}">
								<h1 th:utext="${#strings.toString(houseInfo.projectName).replaceAll('·','<span>·</span>')}"></h1>
								<div th:class="${#strings.isEmpty(houseInfo.projectStatus)?'type type_1': 'type type_'+houseInfo.projectStatus}">
									<h2 th:text="${#strings.isEmpty(houseInfo.projectValue)?'':houseInfo.projectValue}">在售</h2>
								</div>
								<div class="type_sun">
									<h2>别墅</h2>
								</div>
								<div class="clearfix"></div>
							</th:block>
							<th:block th:if="${#strings.toString(houseInfo.projectName).indexOf('·') == -1 ? #strings.toString(houseInfo.projectName).length() > 8: #strings.toString(houseInfo.projectName).length() > 7}">
								<h1 th:utext="${#strings.toString(houseInfo.projectName).replaceAll('·','<span>·</span>')}"></h1>
								<div class="next_line">
									<div th:class="${#strings.isEmpty(houseInfo.projectStatus)?'type type_1': 'type type_'+houseInfo.projectStatus}">
										<h2 th:text="${#strings.isEmpty(houseInfo.projectValue)?'':houseInfo.projectValue}">在售</h2>
									</div>
									<div class="type_sun">
										<h2>别墅</h2>
									</div>
								</div>
								<div class="clearfix"></div>
							</th:block>
						</div>
					</div>
					<div th:if="!${#strings.toString(houseInfo.projectStatus) eq '3'}" class="Price">
						<div class="name">
							<h1>参考价格</h1>
						</div>
						<div class="text">
							<ul th:if="${houseInfo.price} == null or ${#arrays.length(houseInfo.price)} == 0">
								<li class="text"><span><i>待定</i></span>
								</li>
							</ul>
							<ul th:if="${houseInfo.price} != null and ${#arrays.length(houseInfo.price)} != 0">
								<li th:each="price:${houseInfo.price}">
									<div class="qi">
										<h1 th:text="${price.type eq '均价'? '均':'起'}">起</h1>
									</div>
									<div class="money">
										<h1><th:block  th:text="${price.name}"></th:block> <span th:text="${#strings.toString(price.showPrice).replace('万/套','').replace('元/㎡','')}" ></span><th:block th:text="${#strings.toString(price.showPrice).replaceAll('^\d+','')}"></th:block> </h1>
									</div>
									<div class="clearfix"></div>
								</li>
							</ul>
						</div>
						<div class="clearfix"></div>
					</div>
					<!---->
					<div th:if="${#strings.toString(houseInfo.projectStatus) eq '3'}" class="Price">
						<div class="name">
							<h1></h1>
						</div>
						<div class="text">
							<ul>
								<li>
									<div class="clearfix"></div>
								</li>
							</ul>
						</div>
						<div class="clearfix"></div>
					</div>
					<div th:if="${!#strings.isEmpty(houseInfo.brandId) or !#maps.isEmpty(houseInfo.rank)}" class="pinpai">
						<a th:if="${!#strings.isEmpty(houseInfo.brandId)}" th:href="${'/brandCompany/'+houseInfo.brandId+'.htm'}" class="pinpai1" target="_blank"><i class="barnd_icon"></i><span th:text="${#strings.length(houseInfo.brandName)  gt 10 ?'品牌展示区': houseInfo.brandName}" ></span></a>
						<a th:if="${!#maps.isEmpty(houseInfo.rank)}" th:href="${'/projectRank/'+houseInfo.rank.rankType}" class="bangdan"><i class="rank_icon"></i><span th:text="${houseInfo.rank.rankTypeName+'第'+houseInfo.rank.rankCount+'名'}"></span></a>
					</div>
					<div class="measure" th:if="${houseInfo.areaList ne null and #lists.toList(houseInfo.areaList).size() > 0}">
						<div class="name">
							<h1>户型面积</h1>
						</div>
						<div class="text">
							<h1><span th:each="areaList:${houseInfo.areaList}" th:text="${areaList.layoutArea+' ' }"></span></h1>

						</div>
						<div class="photo">
							<a th:href="${'/house/'+ projectId + '-2/layout.htm'}">全部户型图</a>
						</div>
						<div class="clearfix"></div>
						<div class="layer_sun" name="layer_sun">
							<div class="ms_sanjiao dt_sj"></div>
							<th:block  th:each="areaList:${houseInfo.areaList}" th:text="${areaList.layoutArea+' ' }"></th:block>
						</div>
					</div>
					<div class="measure" th:unless="${houseInfo.areaList ne null and #lists.toList(houseInfo.areaList).size() > 0}">
						<div class="name">
						<h1>户型面积</h1>
						</div>
						<div class="text">
							<p>暂无资料</p>
						</div>
						<div class="clearfix"></div>
						<div class="layer_sun" name="layer_sun">
							<div class="ms_sanjiao dt_sj"></div>
							暂无资料
						</div>
					</div>
					<div class="time_sun" th:if="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) ne 0}">
						<div class="name">
							<h1>最新开盘</h1>
						</div>
						<div class="text">
							<p style="color: #333;max-width: 260px" th:class="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) gt 1?'':'win'}">
								<th:block th:each="lic,licindex:${#strings.setSplit(houseInfo.openTime.get(0).dyTime,'.')}"  th:text="${licindex.index==0? lic+'年':(licindex.index==1?lic+'月':lic+'日'+houseInfo.openTime.get(0).dyDesc)}"></th:block>
							</p>
							<div class="layer_sun" name="layer_sun" th:if="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) eq 1}">
								<div class="ms_sanjiao dt_sj"></div>
								<th:block th:each="lic,licindex:${#strings.setSplit(houseInfo.openTime.get(0).dyTime,'.')}"  th:text="${licindex.index==0? lic+'年':(licindex.index==1?lic+'月':lic+'日'+houseInfo.openTime.get(0).dyDesc)}"></th:block>
							</div>
						</div>

						<a th:style="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) gt 1?'display: black':'display: none'}" class="infoIcontime"><s></s>开盘时间
							<table border="0" cellspacing="0" cellpadding="0" style="width: auto;">
								<tbody>
								<tr>
									<th colspan="" th:text="${houseInfo.projectName+'开盘时间'}"></th>
								</tr>
								<!--<tr>
									<td width="13%">开盘时间</td>
									<td width="37%">开盘详情</td>
								</tr>-->
								<tr th:each="e:${houseInfo.openTime}">
<!--									<td  th:text="${e.dyTime}"></td>-->
									<td  th:text="${e.dyDesc}"></td>
								</tr>
								</tbody>
							</table>
						</a>
						<div class="clearfix"></div>
					</div>
					<div class="time">
						<div class="name">
							<h1>交房时间</h1>
						</div>
						<div class="text">
							<p th:text="${houseInfo.payHouseDate}">2015年8月30日（现房）</p>

						</div>
						<div class="clearfix"></div>
						<div class="layer_sun" name="layer_sun">
							<div class="ms_sanjiao dt_sj"></div>
							<th:block  th:text="${houseInfo.payHouseDate}"></th:block>
						</div>
					</div>
					<div class="address">
						<div class="name">
							<h1>楼盘位置</h1>
						</div>
						<div class="text">
							<p th:text="${#strings.isEmpty(houseInfo.projectAdderss)?'':houseInfo.projectAdderss}">中央大街与葵松路交汇处（金宝台二手车市...</p>

						</div>
						<div class="clearfix"></div>
						<div class="layer_sun" name="layer_sun">
							<div class="ms_sanjiao dt_sj"></div>
							<th:block  th:text="${#strings.isEmpty(houseInfo.projectAdderss)?'':houseInfo.projectAdderss}"></th:block>
						</div>
					</div>
					<div class="configure">
						<div class="name">
							<h1>特殊配置</h1>
						</div>
						<div class="text">
							<p><th:block th:each="item,itemStat:${houseInfo.villaLable}" th:text="${itemStat.count eq itemStat.size?item.style:item.style+','}" ></th:block></p>

						</div>
						<div class="clearfix"></div>
						<div class="layer_sun" name="layer_sun">
							<div class="ms_sanjiao dt_sj"></div>
							<th:block  th:each="item,itemStat:${houseInfo.villaLable}" th:text="${itemStat.count eq itemStat.size?item.style:item.style+','}"></th:block>
						</div>
					</div>
					<div class="type_sun">
						<div class="name">
							<h1>建筑类型</h1>
						</div>
						<div class="text">
							<h1><span th:each="buildTypeItem:${#strings.setSplit(houseInfo.companyBuildInfo.buildType,',') }" th:text="${buildTypeItem + ' '}">独栋</span></h1>
						</div>
						<div class="photo">
							<a th:href="${'/house/'+projectId + '-2/info.htm'}">更多详细信息></a>
						</div>
						<div class="clearfix"></div>
						<div class="layer_sun" name="layer_sun">
							<div class="ms_sanjiao dt_sj"></div>
							<th:block th:each="buildTypeItem:${#strings.setSplit(houseInfo.companyBuildInfo.buildType,',') }" th:text="${buildTypeItem + ' '}"></th:block>
						</div>
					</div>
					<div class="newPhone" style="position: relative; left: 11px;" th:if="${houseInfo.projectStatus ne '3'}">
						<div class="iphcon"></div>
						<p>咨询电话:</p>
						<p th:utext="${#strings.toString( houseInfo.sortelTel).replaceAll('转','<span>转</span>')}"></p>
						<div class="Admission newAdmission" th:if="${houseInfo.projectStatus ne '3'}" style="position: absolute; bottom: 9px; right: 0;">
							<ul>
								<li class="seeEwm">
									<div class="sepp">扫码拨号</div>
									<div class="seii"></div>
									<div class="project_main">
										<i></i>
										<img id="project_image" src=""/>
										<p style="font-size: 12px">微信扫码打电话</p>
									</div>
								</li>
								<!--<li>
                                    <a href="javascript:showUsercode(1)">免费专车</a>
                                </li>-->
								<!--<li>
                                    <a href="javascript:showUsercode(2)" >免费通话</a>
                                </li>-->
								<div class="clearfix"></div>

							</ul>

						</div>
					</div>

					<div class="ph-note"> 如需帮助请拨打平台服务电话: ************</div>

					<div class="cl">
					</div>

					<script th:inline="javascript">
                        /*<![CDATA[*/
                        var scene = 'newTel,' +[[${projectId}]]+'-'+[[${projectType}]]+',1,' + [[${#strings.contains(houseInfo.sortelTel,'转')?#strings.replace(houseInfo.sortelTel,'转', ',') : houseInfo.sortelTel}]];
                        var img = "";
                        var sss;
                        $.ajax({
                            type: "GET",
                            async: false,
                            url:  "/getWxACode",
                            data:{"scene": scene},
                            dataType : 'json',
                            headers : {
                                'Content-Type' : 'application/json;charset=utf-8'
                            },
                            success: function (data) {
                                img = data.img;
                                sss = data;
                            }
                        });
                        $("#project_image").attr("src","data:text/html;base64,"+img);
                        /*]]>*/
					</script>
				</div>
				<div class="clearfix"></div>

			</div>

			<!--抢优惠-->
			<input type="hidden" th:value="${houseInfo.coupon.hasCoupon}" id="hasCoupon">
			<script th:inline="javascript">
				$(document).ready(function () {
					var hasCoupon = $("#hasCoupon").val()
					var hasCouponNum = parseInt($("#hasCouponNum").val())
					var robId ;
					if(hasCoupon == '1'){
						robId = $("#couponId").val()
						var robscene = 'pay,' + robId+','+hasCouponNum;

						$.ajax({
							type: "POST",
							async: false,
							url:  "/getNewWxCode",
							data:{
								"scene": robscene,
							},
							dataType : 'json',

							success: function (data) {
								console.log('图片数据')
								console.log(data)
								let robimg = data.img
								$("#getNewWxCode").attr('src',"data:text/html;base64,"+robimg);  // 给太阳码图片赋值
							}
						});
					}else{
						console.log(66666)
					}
				}) 

			</script>
			<div class="w coupons" style="margin-top: 20px;" th:if="${houseInfo.coupon.hasCoupon eq '1'}">
				<input type="hidden" th:value="${houseInfo.coupon.id}" id="couponId">
				<input type="hidden" th:value="${houseInfo.coupon.payAmount}" id="hasCouponNum">
				<div class="coupons_l">
					<div class="c_l_1">
						<h2 th:text="${houseInfo.projectName}"></h2>
						<h3>抢优惠</h3>
					</div>
					<div class="c_l_2">
						<p th:text="${houseInfo.coupon.payTitle}"></p>
						<p th:text="${'抢券时间：'+houseInfo.coupon.startTime+'-'+houseInfo.coupon.endTime}"></p>
					</div>
				</div>
				<div class="coupons_r">
					<img id="getNewWxCode" src="" alt="" style="border-radius:0">
					<p>扫码参与活动</p>
				</div>
			</div>


		</div>

		<script>

            for (var i=0; i<$(" .layer_sun[name=layer_sun]").length;i++){
                var text_x=$(" .layer_sun[name=layer_sun]").eq(i).height();
                if(text_x>50){
                    $(" .layer_sun[name=layer_sun]").eq(i).css("line-height","25px")
                    $(" .layer_sun[name=layer_sun]").eq(i).css("padding"," 7px")
                }

            }

		</script>

		<!--小二甄选-->
<!--		<div class="g_ezx" th:style="${#strings.toString(selectionInfo.selectionState) eq '1'}?'':'display: none'">-->
		<div class="g_ezx" style="display: none">
			<div class="g_elogo"></div>
			<div class="g_etag">
				<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i1.png"><span>品牌</span></div>
				<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i2.png"><span>交通</span></div>
				<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i3.png"><span>潜力</span></div>
				<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i4.png"><span>配套</span></div>
				<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i5.png"><span>环境</span></div>
				<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i6.png"><span>户型</span></div>
				<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i7.png"><span>物业</span></div>
				<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i8.png"><span>性价比</span></div>
			</div>
			<div class="g_etxt"><i>真实房源</i><i>扫码查看</i></div>
			<div class="g_qrcode"><i id="g_qrcode"></i><!--<em></em>--></div>
		</div>

		<!--置业顾问-->

		<div class="" th:if="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
			<div class="newHouseViewChunk">
				<div class="title">
					<P style="color: #333;">经纪人</P>

<!--					<div class="sy-dynatown-ararow">-->
<!--						<span id="up" style="transform: rotate(180deg)"></span>-->
<!--						<span id="down"></span>-->
<!--					</div>-->
				</div>
				<div class="w sy-dynatown">
					<div th:each="agent,h:${houseInfo.agents}"  th:if="${h.index lt 5}"     class="sy-single new-sy-single">
						<div class="news-touxiang">
							<img th:src="${agent.agentPic}" th:alt="${agent.memberName}"/>
							<div class="On-line">
								<p>在线</p>
								<i class="On-line-icon"></i>
							</div>
						</div>
						<div class="news-name">
							<span th:text="${agent.memberName}"></span>
							<div class="name-btn">
								<a th:href="${#session?.getAttribute('sessionId') ne null?'/im/'+ projectId + '-0-' +agent.memberId:('#login')}"  th:data-toggle="${#session?.getAttribute('sessionId') ne null?'':'modal'}" target="_blank">
									<span class="liaogbei-btn">咨询</span>
								</a>
								<div class="call-btn" th:data="${agent.memberId}">
									<span class="show-call">打电话</span>
									<div class="show-ewm">
										<img th:id="${'agent_mobile'+agent.memberId}"  >
										<p>微信扫码打电话</p>
										<p th:text="${#strings.replace(agent.unionTel,',','转')}"></p>
										<input th:id="${'agent_phone'+agent.memberId}"  type="hidden" th:value="${'tel,'+projectId+'-'+projectType+',1,'+agent.unionTel}"/>
									</div>
								</div>
							</div>
						</div>
					</div>

				</div>
			</div>
			<script src="https://static.fangxiaoer.com/js/new4-dynatown.js" type="text/javascript"></script>
		</div>
		<!--认购入口-->
		<div id="subscrip" th:include="activity/subscription::subscription"></div>
		<!--免费看房团-->
		<div class="houseKF" th:if="${!#maps.isEmpty(houseInfo.condor)}">
			<div class="checking"><p>免费看房团</p>
			</div>
			<div class="KFcontent">
				<ul>
					<li class="imgCar"><img src="/imagese/KFcar.png" alt=""></li>
					<li class="KFtimeLi">
						<h1 class="KFtimeH1">
                            <a th:href="@{'/houseKeeper2.htm'}" target="_blank">
                                <span th:text="${houseInfo.condor.condoTour}"></span>
                            </a>

						</h1>
						<div class="KFdetails">
							<ul>
								<li class="KFdetailsli">
									发车时间:
									<span th:text="${houseInfo.condor.sendTime}"></span>
								</li>
								<li class="KFdetailsli2">
									集合地点:
									<span th:text="${houseInfo.condor.colPlace}"></span>
								</li>
								<li class="KFdetailsli3">
									承诺服务:
									<span th:text="${houseInfo.condor.serviceInfo}"></span>
								</li>
							</ul>
						</div>
					</li>
					<li class="KFtimeLi2">
						<a  id="gratis-apply">免费报名</a>
						<span>已有<b th:text="${houseInfo.condor.projectId}"></b>人报名</span>
					</li>
				</ul>
			</div>
		</div>
		<!--抢优惠-->
<!--		<div th:include="house/detail/fragment_activity::activity"></div>-->
		<div th:include="house/detail/fragment_activity::viewActivity"></div>
		<div th:include="house/detail/fragment_activity::projectPay"></div>
		<div class="Bright_wei">
			<div class="content_box">
				<div class="title_box">
					<h1>项目亮点</h1>
				</div>
				<div class="content">
					<table cellspacing="0" border="0">
						<tr>
							<td>
								<div class="name">
									<h1 th:text="${houseInfo.descTitle}"></h1>
								</div>
								<div class="text">
									<p th:text="${houseInfo.projectDescription}"></p>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		<div class="dynamic_wei" th:if="${houseInfo.projectStatus ne '3'}">
			<div class="content_box">
				<div class="title_box">
					<h1>楼盘动态</h1>
					<a th:href="${'/house/'+projectId + '-2/news.htm'}" target="_blank">查看更多</a>
				</div>
				<div class="content"  th:each="saleInfo,iterStat:${saleInfo}"  th:if="${iterStat.index &lt; 1}">
					<div class="name">
						<h1 th:text="${saleInfo.addTime.year}+'-'+${saleInfo.addTime.month}+'-'+${saleInfo.addTime.day}">2018-06-06</h1>
					</div>
					<div class="text">
						<a th:href="${'/house/'+projectId + '-2/news.htm'}" target="_blank">
							<p th:text="${#strings.abbreviate(saleInfo.news,200) }"></p>
						</a>
					</div>
				</div>
			</div>
		</div>
<!--学区简介-->

		<div class="newHouseViewChunk" th:if="${houseInfo.schoolInfo ne null and #lists.size(houseInfo.schoolInfo) > 0}">
			<div class="title">
				<h1>学区简介</h1>
			</div>
			<div class="w w_cont" th:each="school,i:${houseInfo.schoolInfo}" th:style="${school ne ''}?'display:block':'display:none'">
				<div class="xqjj_con">
					<div class="xqjj_con_r">
						<div id='xqjj_0' class="carousel xqjj_caus">
							<!-- Carousel items -->
							<div class="carousel-inner xq_imgs">
								<div class="active item">
									<img th:src="${school.picUrl}" th:alt="${school.schoolAreaName}" >
									<div class="xqjj_item_bg"></div>
									<p th:text="${school.schoolAreaName}"></p>
								</div>
							</div>
							<!-- Carousel nav -->
							<!--<a class="carousel-control left" href="#xqjj_0" data-slide="prev"></a>-->
							<!--<a class="carousel-control right" href="#xqjj_0" data-slide="next"></a>-->
						</div>
					</div>
					<div class="xqjj_con_l">
						<div class="xqjj_name">
							<th:block th:text="${school.schoolAreaName}"></th:block>
							<span th:if="${school.schoolType}" th:text="${#strings.isEmpty(school.schoolType)? '': school.schoolType}"></span>
							<span th:if="${school.scale}" th:text="${#strings.isEmpty(school.scale)? '': school.scale}"></span>
						</div>
						<div class="xqjj_qy" th:utext="${school.description1}"></div>
						<div class="xqjj_address"><span class="xq_tel">地址：</span>
							<th:block th:text="${school.address}"></th:block>
						</div>
						<div class="xqjj_dir">
							<div class="show">
								<span class="xq_tel">概述：</span>
								<th:block th:utext="${school.description2}"></th:block>
								<div class="layer_sun layer_sun2">
									<div class="ms_sanjiao dt_sj"></div>
									<th:block th:utext="${school.description2}"></th:block>
								</div>
							</div>
						</div>
					</div>

					<div class="cl"></div>
				</div>
			</div>
		</div>

	<script th:inline="javascript">
		var g_starb=[[${selectionInfo.totalScore}]]
	</script>
		<!--楼盘价值测评-->
<!--		<div class="g_assess g_villa" th:style="${#strings.toString(selectionInfo.selectionState) eq '1'}?'':'display: none'">-->
		<div class="g_assess g_villa" style="display: none">
			<div class="g_at">
				<p th:text="${houseInfo.projectName + '楼盘价值测评'}"></p>
				<div class="g_layoutTypeR">
					<a class="g_dhv">查看报告</a>
				</div>
				<div class="g_hdcode">
					<div class="g_hcode" id="g_hcode"></div>
					<div class="g_ctx"><i>真实房源,扫码查看</i></div>
					<em></em>
				</div>
			</div>
			<div class="g_evalu">
				<div class="g_leva">
					<div class="g_leva_t">楼盘测评多维度分析</div>
					<div class="g_leva_m">
						<div class="show_number">
							<li>
								<span th:text="${selectionInfo.totalScore} + '分'"></span>
								<div class="atar_Show">
									<!--<p th:tip="${selectionInfo.totalScore}" th:style="'width:' +${selectionInfo.totalScore}+'px;'"></p>-->
									<div class="g_stara"></div>
									<div class="g_starb" th:style="'width:' +${selectionInfo.totalScore}+'px'"></div>
								</div>
							</li>
						</div>
					</div>
				</div>
				<div class="g_reva">
					<div class="g_rli">
						<div class="g_rf"><em>交通位置</em><em th:text="${selectionInfo.trafficScore}+'分'"></em></div>
						<div class="g_rf"><em>户型设计</em><em th:text="${selectionInfo.unitScore}+'分'"></em></div>
					</div>
					<div class="g_rli">
						<div class="g_rf"><em>外围配套</em><em th:text="${selectionInfo.externalResourceScore}+'分'"></em></div>
						<div class="g_rf"><em>开发品牌</em><em th:text="${selectionInfo.brandScore}+'分'"></em></div>
					</div>
					<div class="g_rli">
						<div class="g_rf"><em>内部配套</em><em th:text="${selectionInfo.internalResourceScore}+'分'"></em></div>
						<div class="g_rf"><em>价值潜力</em><em th:text="${selectionInfo.valueScore}+'分'"></em></div>
					</div>
					<div class="g_rli">
						<div class="g_rf"><em>园区环境</em><em th:text="${selectionInfo.environmentScore}+'分'"></em></div>
						<div class="g_rf"><em>装修品质 </em><em th:text="${selectionInfo.decorateScore}+'分'"></em></div>
					</div>
					<div class="g_rli">
						<div class="g_rf"><em>物业管理</em><em th:text="${selectionInfo.propertyScore}+'分'"></em></div>
						<div class="g_rf"><em>性价比</em><em th:text="${selectionInfo.costPerformanceScore}+'分'"></em></div>
					</div>
				</div>
				<div class="g_clbo"></div>
			</div>
		</div>


		<div class="huxing_wei" >
			<div class="content_box">
				<div class="title_box">
					<h1>在售户型</h1>
					<a th:if="${!#lists.isEmpty(houseInfo.villaLayout) and #lists.size(houseInfo.villaLayout) gt 3}" th:href="${'/house/'+ projectId + '-2/layout.htm'}">查看更多</a>
				</div>
				<div class="content">
					<ul>
						<li th:each="layout,i:${houseInfo.villaLayout}" th:if="${i.index &lt; 3}">
							<div class="photo">
								<a th:href="${'/house/' + projectId + '-2/layout/pid'+projectId+'-pt2-l'+ layout.layId+ '#layInfo'}"><img th:src="${layout.imageUrl}" th:alt="${houseInfo.projectName}+${layout.roomType}+'室'+${layout.hallType}+'厅'+${layout.guardType}+'卫建筑面积约为'+${layout.buildArea}+'㎡'"/></a>
							</div>

							<div class="newPos">
								<!--语音户型图按钮-->
								<span th:if="${layout.voice ne null and layout.voice ne ''}" class="voiceTabSy newVoiceTabSy" th:data-id="${projectId + '-' + layout.layId}">语音户型</span>
								<!--户型对比-->
								<div class="hxBtnNew" th:if="${houseInfo.projectStatus ne '3'}">
									<p class="hxBtnNew1 type-compare-btn1"  th:data-layId="${ layout.layId}" th:data-layName="${#numbers.formatInteger(layout.buildArea,2)+'㎡'+layout.roomType+'室'+layout.hallType+'厅'+layout.guardType+'卫'+ houseInfo.projectName}"><i></i>户型对比</p>
									<p class="hxBtnNew1 type-compare-btn2" th:data-layId="${ layout.layId}" th:data-layName="${#numbers.formatInteger(layout.buildArea,2)+'㎡'+layout.roomType+'室'+layout.hallType+'厅'+layout.guardType+'卫'+ houseInfo.projectName}" th:onclick="'deletelayoud2('+${ layout.layId}+')'"><i></i>取消对比</p>
								</div>
								<!--语音户型图按钮-->
								<div class="text"><a th:href="${'/house/' + projectId + '-2/layout/pid'+projectId+'-pt2-l'+ layout.layId+ '#layInfo'}">
									<h1  th:text="${layout.roomType}+'室'+${layout.hallType}+'厅'+${layout.guardType}+'卫建筑面积约为'+${layout.buildArea}+'㎡'">5室3厅4卫建筑面积约为465㎡</h1>
									<p><span>户型点评：</span ><th:block th:text="${layout.description}"></th:block></p></a>
								</div>
								<!--语音户型图二维码框-->
								<div th:if="${layout.voice ne null and layout.voice ne ''}"  th:id="${'vrcode' + layout.layId}" style="display:none" class="vrcodeall">
									<span></span>
									<img th:id="${'vqrcode' + layout.layId}" src=""/>
									<p>扫描二维码<br>听我的自我介绍</p>
									<span class="Triangle"></span>
								</div>
								<!--语音户型图二维码框-->
							</div>
						</li>
						<div class="clearfix"></div>
					</ul>
				</div>
			</div>
			<script>
				$(window).ready(function () {
				    if($(".huxing_wei .content li").length == 0){
				        $(".huxing_wei").hide();
					}
                })
			</script>
		</div>

		<!--深度解析-->
		<div class="newHouseViewChunk"  th:if="${!#maps.isEmpty(houseInfo.deepNews)}">
			<div class="deepAnalysis">

				<div class="analysisTitle">
					<h1>深度解析</h1>
				</div>

				<div class="analysisInfo">

					<div class="analysisInfoImg">
						<a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank" style="text-decoration:none">
							<img th:src="${#strings.isEmpty(houseInfo.deepNews.pic)?'':houseInfo.deepNews.pic}" th:alt="${#strings.isEmpty(houseInfo.deepNews.projectName)?'':houseInfo.deepNews.projectName}" width="180px" height="134px"/>
						</a>
					</div>

					<div class="analysisInfoTxt">
						<a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank" style="text-decoration:none">
							<h2 th:text="${#strings.isEmpty(houseInfo.deepNews.titleShow)?'':houseInfo.deepNews.titleShow}"></h2>
						</a>
						<a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank" style="text-decoration:none">
							<p th:text="${#strings.isEmpty(houseInfo.deepNews.description)?'':houseInfo.deepNews.description}">
							</p>
						</a>
					</div>
					<div class="cl"></div>
				</div>
			</div>
		</div>

		<div class="Album_wei" th:unless="${#lists.isEmpty(photosInfo)}">
			<div class="content_box">
				<div class="title_box">
					<h1>楼盘相册</h1>
					<a th:href="${'/house/'+ projectId + '-2/album.htm'}" >查看更多</a>
				</div>
				<div class="content">
					<div class="box_sun">
						<div class="left_wei">
							<img src="" />
						</div>
						<div class="right_wei">
							<div class="show">
								<div class="lunbo">
									<ul>
										<li th:each="photoInfo:${photosInfo}">
											<img th:src="${photoInfo.url}" alt="效果图"/>
										</li>
									</ul>
								</div>
								<div class="border"></div>
							</div>
							<div class="top_wei">
								<img src="https://static.fangxiaoer.com/web/images/Villa/top.png"/>
							</div>
							<div class="bottom_wei">
								<img src="https://static.fangxiaoer.com/web/images/Villa/bottom.png"/>
							</div>
						</div>
						<div class="clearfix"></div>
					</div>
				</div>
			</div>
		</div>
		<div class="nmaptitleleft weixiangying" >
			<a href="https://sy.fangxiaoer.com/house/housemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
			<i style="float: right;color: #eaeaea">丨</i>
			<a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
			<p >周边配套</p>
		</div>
		<div class="map_wei" th:include="house/detail/fragment_map::map2"></div>

		<!--项目点评-->
		<div class="newHouseComment" id="xmpj" th:style="${houseInfo.projectStatus ne '3' ? 'display:block' : 'display:none'}">
			<div th:include="house/detail/fragment_ask::xmpjForVilla"></div>
			<input type="hidden" id="sessionId" th:value="${session.sessionId}">
			<input type="hidden" id="memberType" th:value="${session.memberType}">
			<input type="hidden" id="projectType" th:value="${projectType}">
			<div th:include="house/detail/fragment_contactAgent::contact"></div>
		</div>
		<!--楼盘问答-->
		<div class="newHouseViewChunk" th:style="${houseInfo.projectStatus ne '3' ? 'display:block' : 'display:none'}">
			<div th:include="house/detail/fragment_order::forAsk"></div>
			<div th:include="house/detail/fragment_ask::xmzxForVilla"></div>
			<!--con_cm_1-->
			<script src="/js/house/AskListVilla.js"></script>
			<script src="/js/house/AskLjzPage.js"></script>
			<script type="text/javascript" th:inline="javascript">
                var Projectid = [[${projectId}]];
                $(document).ready(function () {
                    $(".houseAnswer").html("");
                    AskList.GetList(Projectid,0,'','');
                });
			</script>
		</div>
		<!--底部 周边楼盘 周边二手房-->
        <div th:include="house/detail/fragment_relasion_house::villa_periphery"></div>
		<!--有验证码订单-->
        <div  th:include="house/detail/fragment_order::useCode"  ></div>
        <div  th:include="house/detail/fragment_order::guideMessage"  ></div>
		<!--登录模块-->
<!--		<div th:include="house/detail/fragment_login::login"></div>-->
		<!--底部-->
		<div class="" th:include="fragment/fragment::footer_detail"></div>
		<!--统计-->
		<div th:include="fragment/fragment::tongji"></div>
		<!--右侧浮标-->
		<th:block th:if="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
			<div th:include="fragment/fragment::commonFloat" th:with="commonType=1"></div>
		</th:block>
		<th:block th:unless="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
			<div th:include="fragment/fragment::commonFloat"></div>
		</th:block>
        <!--登录引导-->
        <!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
		<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
		<img id="codelogo" style="display: none" src="https://imageicloud.fangxiaoer.com/event/2022/10/11/133502165.png" crossorigin="anonymous">
		<div id="qrcodeInfo" style="display: none"></div>
		<script type="text/javascript">
            /**该方法用来绘制一个有填充色的圆角矩形
             *@param cxt:canvas的上下文环境
             *@param x:左上角x轴坐标
             *@param y:左上角y轴坐标
             *@param width:矩形的宽度
             *@param height:矩形的高度
             *@param radius:圆的半径
             *@param fillColor:填充颜色
             **/
            function fillRoundRect(cxt, x, y, width, height, radius, /*optional*/ fillColor) {
                //圆的直径必然要小于矩形的宽高
                if (2 * radius > width || 2 * radius > height) { return false; }

                cxt.save();
                cxt.translate(x, y);
                //绘制圆角矩形的各个边
                drawRoundRectPath(cxt, width, height, radius);
                cxt.fillStyle = fillColor || "#000"; //若是给定了值就用给定的值否则给予默认值
                cxt.fill();
                cxt.restore();
            }
            function drawRoundRectPath(cxt, width, height, radius) {
                cxt.beginPath(0);
                //从右下角顺时针绘制，弧度从0到1/2PI
                cxt.arc(width - radius, height - radius, radius, 0, Math.PI / 2);

                //矩形下边线
                cxt.lineTo(radius, height);

                //左下角圆弧，弧度从1/2PI到PI
                cxt.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);

                //矩形左边线
                cxt.lineTo(0, radius);

                //左上角圆弧，弧度从PI到3/2PI
                cxt.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);

                //上边线
                cxt.lineTo(width - radius, 0);

                //右上角圆弧
                cxt.arc(width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);

                //右边线
                cxt.lineTo(width, height - radius);
                cxt.closePath();
            }

            $(".voiceTabSy").mouseover(function () {
                var value = $(this).attr("data-id");
                var infos = value.split('-');
                var voiceUrl = "https://m.fangxiaoer.com/fang1/"+infos[0]+"-2/layout/pid"+infos[0]+"-pt2-l" + infos[1];
                $('#qrcodeInfo').qrcode({
                    render : "canvas",
                    width: 160,
                    height: 160,
                    text: voiceUrl
                }).hide();
                var canvasinfo = $("#qrcodeInfo canvas")[0];
                //添加logo
                var codeImage = document.querySelector("#codelogo");
                codeImage.crossOrigin = "anonymous"
                var ctx = canvasinfo.getContext('2d')
                fillRoundRect(ctx, 59, 59, 42, 42 ,3, '#fff')
                ctx.drawImage(codeImage, 61, 61, 38, 38);
                $('#vqrcode' + infos[1]).attr('src', canvasinfo.toDataURL('image/png'));
                $("#vrcode" + infos[1]).show();
            });
            $(".voiceTabSy").mouseleave(function () {
                $("#qrcodeInfo").html('')
                var value = $(this).attr("data-id");
                var infos = value.split('-');
                $("#vrcode" + infos[1]).hide();
            });
		</script>
		<!--报名弹窗-->
		<div id="condo-apply">
			<div class="condo-apply-win">
				<h6>免费报名<span id="close"><img src="https://static.fangxiaoer.com/web/images/sercoudo/tour-close.png"></span></h6>
				<!--已登录用户报名-->
				<th:if th:if="${#session?.getAttribute('sessionId') ne null and #session?.getAttribute('sessionId') ne ''}">
					<div class="condo-apply-put">
						<input id="phone" type="tel" name="phone" placeholder="请输入手机号" readonly="readonly" th:value="${session.phoneNum}" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">
					</div>
					<div class="condo-apply-put">
						<input id="address" type="text" name="address" placeholder="请输入您的姓名">
						<span class="errormsg name" style="display: none">请输入您的姓名</span>
					</div>
					<div class="condo-apply-put condo-apply-subme">
						<p id="submit">立即提交</p>
					</div>
				</th:if>

				<!--已登录用户报名-->
				<!--未登录用户报名-->
				<th:if th:unless="${#session?.getAttribute('sessionId') ne null and #session?.getAttribute('sessionId') ne ''}">
					<div class="condo-apply-put">
						<input id="phone" type="tel" name="phone" placeholder="请输入手机号" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">

						<span class="errormsg cellphone" style="display: none">请输入正确的手机号码</span>
					</div>
					<div class="condo-apply-put" id="code-input">
						<input id="code" type="tel" name="code" style="width: 65%;border-right: 1px solid #dedede;" placeholder="请输入验证码" maxlength="6">
						<span id="code-pull">获取验证码</span>
						<span class="errormsg verifycode" style="display: none">请输入正确的验证码</span>
					</div>

					<div class="condo-apply-put">
						<input id="address" type="text" name="address" placeholder="请输入您的姓名">
						<span class="errormsg name" style="display: none">请输入您的姓名</span>
					</div>

					<div class="condo-apply-put condo-apply-subme">
						<p id="submit">立即提交</p>
					</div>
				</th:if>
			</div>
			<!--未登录用户报名-->
			<div id="success" style="display: none;">
				<span id="closeb"><img src="https://static.fangxiaoer.com/web/images/sercoudo/tour-close.png"></span>
				<p><img src="https://static.fangxiaoer.com/web/images/sercoudo/success.png"></p>
				<P>预约成功</P>
				<p>工作人员将尽快与您联系，免费为您提供专业服务</p>
			</div>

		</div>


		<script th:inline="javascript">
			$("#gratis-apply").click(function(){
				$("#condo-apply").show()
				$(".condo-apply-win").show();
				$("body").css("overflow","hidden")
			});

			$("#close").click(function() {
				$("#condo-apply").hide();
				$("#success").hide()
				$("body").css("overflow","auto")
			})

			$("#closeb").click(function() {
				clearTimeout(closeTime);
				$("#condo-apply").hide();
				$("#success").hide()
				$("body").css("overflow","auto")
			})



			var wait = 60;
			$("#code-pull").click(function() {
				var tel = $("#phone").val();//获取手机号
				if (tel.length == 11 && tel.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/) && wait == 60) {
					$.ajax({
						type: "POST",
						data: {
							mobile: tel
						},
						url: "/sendSmsCode",
						success: function (result) {
							if (result.status == 0) {
								alert("系统繁忙，请稍后重试!");
							} else {
								$("#code-pull").css({
									"border": "none",
									"color": "#ccc"
								});
								time(wait);
							}
						}
					});
				} else {
					$(".cellphone").html("请输入正确的手机号码").show();
					return false;
				}
			})

			function time(o) {
				if (wait == 0) {
					$("#code-pull").html("重新获取");
					wait = 60;
					$("#code-pull").css({
						"border": "none",
						"color": "#ff5200"
					});
				} else {
					$("#code-pull").html(wait + "秒后重发");
					wait--;
					setTimeout(function() {time(o); },
							1000);
				}
			}


			var closeTime ;
			var sessionId = [[${#session?.getAttribute('sessionId')}]];
			$("#submit").click(function () {
				var phone = "",
						code = "",
						carWhereId = "";
				phone = $("#phone").val(); //手机号
				code = $("#code").val(); //验证码
				carWhereId = $("#address").val(); //用户名
				if (sessionId == null || sessionId == "") {
					if (phone == "" || phone.length != 11 || !phone.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)) {
						$(".cellphone").show();
						setTimeout(function () {
							$(".cellphone").hide();
						}, 2000);
						// alert("请正确输入您的手机号码");
						return;
					} else {
						$(".cellphone").hide();
					}

					if (code == "" || code.length != 6) {
						$(".verifycode").show();
						setTimeout(function () {
							$(".verifycode").hide();
						}, 2000);
						// alert("请正确输入您的验证码");
						return;
					} else {
						$(".verifycode").hide();
					}
				}


				if(carWhereId == null || carWhereId == ""){
					$("span.name").html("请输入姓名");
					$("span.name").show()
					setTimeout(function () {
						$("span.name").hide();
					}, 2000);
					return
				}
				var params = {
					phone: phone,
					sessionId: sessionId,
					code: code,
					region: carWhereId,
					area: "看房团",
					type: 4
				};
				$.ajax({
					type: "POST",
					url: "/saveHouseOrder",
					data: JSON.stringify(params),
					headers: {
						'Content-Type': 'application/json;charset=utf-8'
					},
					success: function (data) {
						console.log(data)
						if (data.status == 1) {
							$(".condo-apply-win").hide();
							$("#success").show();
							$("#success").children()[0].style.display=" ";
							$("#success").children()[2].style.display=" ";
							if(sessionId == '' || sessionId == null || sessionId == undefined){
								$("#phone").val("");
							}
							$("#code").val(""); //验证码
							$("#address").val(""); //用户名
							closeTime =setTimeout(function () {
								$("#condo-apply").hide();
								$("#success").hide()
								$("body").css("overflow","auto")
							},3000)
						} else {
//                       $(".condo-apply-win").hide();
//                        $("#success").children()[0].style.display="none";
//                        $("#success").children()[2].style.display="none";
//                        $("#success").children()[1].html(data.msg);
							alert(data.msg+",请检查后重新提交")
						}
					}
				})
			})
			//获取验证码倒计时

			$("#player-b").click(function(){
				$("#condotour").get(0).play();
				$("#condotour").get(0).controls=true;
				$("#player-b").hide();
			});

			$("#condotour").bind("ended", function() {
				$("#player-b").show();
				$("#condotour").get(0).controls=false;
			});
			// $("#condotour").get(0).onpause=function () {
			//     $("#player-b").show();
			//     $("#condotour").get(0).pause();
			//     $("#condotour").get(0).controls=false;
			// }

		</script>
		<!--报名弹窗-->

	</body>
</html>
