<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head >
    <title th:text="${houseInfo.projectName}+'_沈阳别墅_沈阳'+${houseInfo.projectName}+'在售信息 - 房小二网'"></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="keywords" th:content="${houseInfo.projectName}+','+${houseInfo.projectName}+'在售,沈阳'+${houseInfo.projectName}+','+${houseInfo.projectName}+'开盘'" />
    <meta name="description" th:content="${'房小二网销售动态为您提供沈阳'+houseInfo.projectName+'别墅的详细在售信息，包括开盘情况，在售户型，优惠政策等购房者关心的内容。买别墅，看别墅，就上房小二网。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/saleInfo/'+projectId+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=201911112" />
    <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynamic.css?v=20181009" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/house/verify.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?v=20180522"/>
    <!--<link rel="stylesheet" type="text/css" href="/css/saleTrends.css" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/saleTrends.css?v=20191220" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
    <link type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/Villa_index.css?v=20220323" rel="stylesheet" />
    <!--<link type="text/css" href="/css/Villa_index.css" rel="stylesheet" />-->
    <style>
        .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
            background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    </style>
</head>
<body class="w1210">

<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>


<div class="cl"></div>
<div th:include="house/villa/fragment_menu::newMenu" th:with="type=5"></div>

<div class="main main_wei">
    <div class="saleTrends tab">
        <div class="quanbao">
            <div class="dynamic_head">
                <ul>
                    <div class="dynamic_head">
                        <ul>
                            <a th:href="${'/house/'+projectId+'-'+projectType+'/news/dy.htm'}"><li th:class="${#strings.toString(dyType) eq null or #strings.toString(dyType) eq ''} ?'focus':''"  >全部</li></a>
                            <th:block th:if="${#lists.size(dyTypeFilter) != 0}" th:each="filter,i:${dyTypeFilter}">
                                <a th:href="${'/house/'+projectId+'-'+projectType+'/news/dy'+filter.id+'.htm'}"><li th:classappend="${#strings.toString(dyType) eq filter.id}?'focus':''"  th:text="${filter.name}"></li></a>
                            </th:block>
                        </ul>
                    </div>
                </ul>
            </div>
            <div class="dynamic_mian">
                <div class="xian"></div>
                <ul>
                    <li th:each="info,i:${saleInfoNew}">
                        <div class="top_wei">
                            <i></i>
                            <span>-<b th:text="${info.dyTime}"></b>-</span>
                            <div th:if="${#strings.toString(dyType) eq null} or ${#strings.toString(dyType) eq ''}" th:class="${'type'+info.dyType}" th:text="${info.dyTypeName}"></div>
                        </div>
                        <div class="bottom_wei">
                            <p th:text="${info.dyDesc}"></p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <!--分页-->
        <div class="page" style="padding: 0;">
            <div th:include="fragment/page :: page "></div>
        </div>
    </div>
    <div th:include="house/detail/fragment_menu::freeCall" ></div>
            <div class="cl"></div>
        </ul>
        <ul class="saleTrends-main">
            <div th:each="s,j:${newSaleInfo}">
                <li th:rel="${j.index}" th:id="${'con_a_'+ j.index}"   th:each="d :${s.dynamicContent}">
                    <div th:each="n:${d.newsSeperate}">
                        <p th:if="${!#strings.isEmpty(n.label1)}" th:text="${n.label1+n.label2}"></p>
                        <!--<p th:if="${!#strings.isEmpty(n.label2)}" th:text="${n.label2}"></p>-->
                    </div>
                    <!--<p th:text="${#strings.toString(d.newsContent)}">【在售信息】学府LOFT全款27万起，日供50元起!买一层享两层。建筑面积70-130平四期新品，火爆热销中！建筑面积78-517㎡ 独栋现铺，即买即用，9600元/平起！</p>-->
                    <!--<p>【当前优惠】一次性98折，贷款99折。</p>-->
                    <!--<p>【其他信息】一环旁、地铁畔、双公园、邻学府、近商圈！</p>-->
                </li>
            </div>
            <!--<li rel="2"  id="con_a_2"  class="cc">-->
            <!--<p>222</p>-->
            <!---->
            <!--</li>-->
        </ul>
    </div>
<div class="clearfix"></div>
    <script type="text/javascript">
        $(document).ready(function(){
            $("#a0").addClass("hover");
            for (var i = 1; i <10; i++) {
                $("li#" + "con_a_"+i).hide();
            }
        });
        $(".dot").mouseover(function() {

            var dotRel = $(this).attr("rel");
            var dotparent = $(this).parent()
            $(".timeShort").hide()
            $(".timeLong").show()
            $(".dot").removeClass("hover")
            $(this).addClass("hover")
            dotparent.find(".timeShort").show()
            dotparent.find(".timeLong").hide()

            $("li#" + "con_a_"+ dotRel).show();
//                console.log(dotRel)
            for (var i = 0; i <10; i++) {
                if (dotRel != i)
                    $("li#" + "con_a_"+i).hide();
            }

        })

        $(".timeLong").mouseover(function() {

            var dotRel = $(this).parents("li").find(".dot").attr("rel");
            var dotparent = $(this).parents("li").find(".dot")
            $(".timeShort").hide()
            $(".timeLong").show()
            $(".dot").removeClass("hover")
            $(this).parents("li").find(".dot").addClass("hover")
            $(this).parents("li").find(".timeShort").show()
            $(this).parents("li").find(".timeLong").hide()

            $("li#" + "con_a_"+ dotRel).show();
//                console.log(dotRel)
            for (var i = 0; i <10; i++) {
                if (dotRel != i)
                    $("li#" + "con_a_"+i).hide();
            }

        })
    </script>

</div>
<div th:include="house/detail/fragment_relasion_house::villa_periphery"></div>
<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::unuseCode"  ></div>
<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode"  ></div>
</body>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
</html>