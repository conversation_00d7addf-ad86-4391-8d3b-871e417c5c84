<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml"
      xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${houseInfo.projectName}+'_沈阳别墅_沈阳'+${houseInfo.projectName}+'信息 - 房小二网'"></title>
    <meta name="keywords" th:content="${houseInfo.projectName}+',沈阳别墅,沈阳'+${houseInfo.projectName}+','+${houseInfo.projectName}+'信息'"/>
    <meta name="description"  th:content=" '房小二网为您提供沈阳'+${houseInfo.projectName}+'别墅的最详尽信息，包括基础资料，建筑信息，项目开发商介绍等全部购房须知信息。买别墅，看别墅，就上房小二网。'"  />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?t=20191112" rel="stylesheet" type="text/css" />
    <!--<link href="/css/main2017.css" rel="stylesheet" type="text/css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/villa/info.css?t=20180921" rel="stylesheet" type="text/css" />
    <link type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/Villa_index.css?v=20190916" rel="stylesheet" />
    <!--<link type="text/css" href="/css/Villa_index.css" rel="stylesheet" />-->
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/villa_information.css?v=20180918"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/Villa/villa_information.js"></script>

</head>
<body>
<style>
    .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
        background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
</style>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

<!--head end--><div class="cl"></div>
<div class="pro_name hid"><p th:text="${houseInfo.projectName}"></p></div>
<!--<div th:include="house/detail/fragment_activity::activity"></div>-->


<div th:include="house/villa/fragment_menu::newMenu" th:with="type=2"></div>
<div class="basic_box">
    <div class="content_box">
       <div class="title">
           <h1>基础资料</h1>
       </div>
        <ul class="one">
            <li><div class="name">区域板块</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.regionPlate)?'暂无资料':houseInfo.regionPlate}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">环线类型</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.schortType)?'暂无资料':houseInfo.baseInfo.schortType}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">特色标签</div><div class="text_sun"><span ><label th:each="villaLable:${houseInfo.villaLable}"  th:if="${villaLableStat.count le 5}" th:class="'t'+${villaLableStat.count}"   th:text="${villaLable.style}"></label></span></div><div class="clearfix"></div></li>
            <li><div class="name">开发商</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.developerName)?'暂无资料':#strings.abbreviate(houseInfo.companyBuildInfo.developerName,33) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.companyBuildInfo.developerName) gt 33}" style="display: none"><th:block th:text="${houseInfo.companyBuildInfo.developerName}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">施工单位</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.sgdw)?'暂无资料':#strings.abbreviate(houseInfo.companyBuildInfo.sgdw,33) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.companyBuildInfo.sgdw) gt 33}" style="display: none"><th:block th:text="${houseInfo.companyBuildInfo.sgdw}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">供暖方式</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.heatingType)?'暂无资料':houseInfo.baseInfo.heatingType}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">产权年限</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.propertyAge)?'暂无资料':houseInfo.baseInfo.propertyAge}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.tzs) and houseInfo.companyBuildInfo.tzs ne '暂无资料'}"><div class="name">投资商</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.tzs)?'暂无资料':#strings.abbreviate(houseInfo.companyBuildInfo.tzs,33) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.companyBuildInfo.tzs) gt 33}" style="display: none"><th:block th:text="${houseInfo.companyBuildInfo.tzs}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.HeatingUnits) and houseInfo.companyBuildInfo.HeatingUnits ne '暂无资料'}"><div class="name">供暖单位</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.HeatingUnits)?'暂无资料':#strings.abbreviate(houseInfo.companyBuildInfo.HeatingUnits,33) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.companyBuildInfo.HeatingUnits) gt 33}" style="display: none"><th:block th:text="${houseInfo.companyBuildInfo.HeatingUnits}"></th:block></div><div class="clearfix"></div></li>

            <div class="clearfix"></div>
        </ul>
        <ul class="two">
            <li><div class="name">总建筑面积</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.baseInfo.TotalBuildingArea)?'暂无资料':houseInfo.baseInfo.TotalBuildingArea+' ㎡'}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">总占地面积</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.FloorArea)?'暂无资料':houseInfo.baseInfo.FloorArea}+' ㎡'"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">容积率</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.baseInfo.PlotRatio)?'暂无资料':houseInfo.baseInfo.PlotRatio}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">绿化率</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.baseInfo.Greenery)?'暂无资料':houseInfo.baseInfo.Greenery}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">车位</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.garageInfo)?'暂无资料':#strings.abbreviate(houseInfo.companyBuildInfo.garageInfo,33) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.companyBuildInfo.garageInfo) gt 33}" style="display: none"><th:block th:text="${houseInfo.companyBuildInfo.garageInfo}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">物业公司</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.wygs)?'暂无资料':#strings.abbreviate(houseInfo.companyBuildInfo.wygs,33) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.companyBuildInfo.wygs) gt 33}" style="display: none"><th:block th:text="${houseInfo.companyBuildInfo.wygs}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">物业费</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.baseInfo.propertyFee)?'暂无资料':#strings.abbreviate(houseInfo.baseInfo.propertyFee,33) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.propertyFee) gt 33}" style="display: none"><th:block th:text="${houseInfo.baseInfo.propertyFee}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.totalNum) and houseInfo.totalNum ne '暂无资料'}"><div class="name">总户数</div><div class="text_sun"><th:block   th:text="${#strings.abbreviate(houseInfo.totalNum,33) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.totalNum) gt 33}" style="display: none"><th:block th:text="${houseInfo.totalNum}"></th:block></div><div class="clearfix"></div></li>
            <div class="clearfix"></div>
        </ul>
    </div>

</div>
<div class="architecture">
    <div class="content_box">
        <div class="title_sun">
            <h1>建筑信息</h1>
        </div>
        <ul>
            <li><div class="name">交付标准</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.isdecorate)?'暂无资料':houseInfo.isdecorate}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">建筑类型</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.buildType)?'暂无资料':houseInfo.companyBuildInfo.buildType}"></th:block></div><div class="clearfix"></div></li>
            <li><div class="name">建筑风格</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.DesingnStyle)?'暂无资料':houseInfo.companyBuildInfo.DesingnStyle}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.giving) and houseInfo.companyBuildInfo.giving ne '暂无资料'}" ><div class="name" >赠送面积</div><div class="text_sun"><th:block th:text="${#strings.abbreviate(houseInfo.companyBuildInfo.giving,34) }"></th:block></div><div class="content_pop" th:if="${#strings.length(houseInfo.companyBuildInfo.giving) gt 34}" style="display: none"><th:block th:text="${houseInfo.companyBuildInfo.giving}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.floorspace) and houseInfo.companyBuildInfo.floorspace ne '暂无资料'}" ><div class="name">楼间距信息</div><div class="text_sun"><th:block th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.floorspace)?'暂无资料':houseInfo.companyBuildInfo.floorspace+'米'}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.Interior) and houseInfo.companyBuildInfo.Interior ne '暂无资料'}"><div class="name">内墙</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.Interior)?'暂无资料':houseInfo.companyBuildInfo.Interior}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.exterior) and houseInfo.companyBuildInfo.exterior ne '暂无资料'}"><div class="name">外墙</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.exterior)?'暂无资料':houseInfo.companyBuildInfo.exterior}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.buildingStructure) and houseInfo.companyBuildInfo.buildingStructure ne '暂无资料'}"><div class="name">建筑结构</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.buildingStructure)?'暂无资料':houseInfo.companyBuildInfo.buildingStructure}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.windows) and houseInfo.companyBuildInfo.windows ne '暂无资料'}"><div class="name">门窗工艺</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.windows)?'暂无资料':houseInfo.companyBuildInfo.windows}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.jzsj) and houseInfo.companyBuildInfo.jzsj ne '暂无资料'}"><div class="name">建筑设计</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.jzsj)?'暂无资料':houseInfo.companyBuildInfo.jzsj}"></th:block></div><div class="clearfix"></div></li>
            <li th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.LandscapeDesign) and houseInfo.companyBuildInfo.LandscapeDesign ne '暂无资料'}"><div class="name">景观设计</div><div class="text_sun"><th:block  th:text="${#strings.isEmpty(houseInfo.companyBuildInfo.LandscapeDesign)?'暂无资料':houseInfo.companyBuildInfo.LandscapeDesign}"></th:block></div><div class="clearfix"></div></li>
            <div class="clearfix"></div>
        </ul>
    </div>

</div>
<div class="sale">
    <div class="content_box">
        <div class="title_sun">
            <h1>销售信息</h1>
        </div>
       <div class="content">
           <ul>
               <li class="adds_wei">售楼处地址：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.SaleAddress)?'暂无资料':houseInfo.baseInfo.SaleAddress}"></th:block></li>
               <li class="huxing_wei" th:if="${!#lists.isEmpty(layoutType)}">楼盘户型： <span th:each="layoutType:${layoutType}" th:text="${layoutType.name}" ></span> <a class="more_layout" th:if="${!#lists.isEmpty(houseInfo.areaList)}" th:href="${'/house/'+ projectId + '-2/layout.htm'}" target="_blank">查看户型 ></a></li>
               <li class="type_wei">付款方式：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.loans)?'暂无资料':houseInfo.baseInfo.loans}"></th:block></li>
               <li class="zhuangtai_wei">项目状态：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.saleState)?'暂无资料':houseInfo.baseInfo.saleState}"></th:block></li>
               <div class="clearfix"></div>
               <li class="time_wei"  th:if="${!#strings.isEmpty(houseInfo.payHouseDate) and houseInfo.payHouseDate ne '暂无资料'}" >交房时间：<th:block th:text="${#strings.isEmpty(houseInfo.payHouseDate)?'暂无资料':houseInfo.payHouseDate}"></th:block></li>
               <li th:if="${#lists.isEmpty(houseInfo.license)}">预售许可证： 暂无资料</li>
               <li class="table" th:if="${!#lists.isEmpty(houseInfo.license)}">预售许可证：
                   <!--<div th:if="${!#lists.isEmpty(houseInfo.license)}">-->
                    <table>
                        <thead>
                        <tr>
                            <td style=" width: 220px">预售许可证</td>
                            <td style=" width: 220px">发证时间</td>
                            <td style=" width: 634px">绑定楼栋</td>
                        </tr>
                        </thead>
                        <tbody>
                            <tr th:each="licenseItem:${houseInfo.license}">
                                <td style="text-align: center" th:text="${#strings.isEmpty(licenseItem.licenseId)?'暂无资料':licenseItem.licenseId}"></td>
                                <td style="text-align: center" th:text="${#strings.isEmpty(licenseItem.dyTime)?'暂无资料':#strings.toString(licenseItem.dyTime).replace('.','-')}"></td>
                                <td th:text="${#strings.isEmpty(licenseItem.dyDesc)? '暂无资料' : licenseItem.dyDesc }"></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="btn" th:if="${ #lists.size(houseInfo.license) gt 1}">
                        <h1>展开</h1>
                    </div>
               </li>
           </ul>
       </div>
    </div>


</div>
<div class="project">
    <div class="content_box">
        <div class="title">
            <h1>项目简介</h1>
        </div>
        <div class="content">
            <p th:text="${houseInfo.projectDescription}"></p>
        </div>
    </div>


</div>
<div class="developers" th:if="${!#strings.isEmpty(houseInfo.companyBuildInfo.developerDescription) and houseInfo.companyBuildInfo.developerDescription ne '暂无资料'}" >
   <div class="content_box" >
       <div class="title">
           <h1>开发商简介</h1>
       </div>
       <div class="content">
           <p th:text="${houseInfo.companyBuildInfo.developerDescription}"></p>
       </div>
   </div>
</div>
<div th:include="house/detail/fragment_relasion_house::villa_periphery"></div>
<div style="background-color: #fff; padding-top: 20px; width: 1200px; margin: 0 auto" th:unless="${!#strings.isEmpty(houseInfo.companyBuildInfo.developerDescription) and houseInfo.companyBuildInfo.developerDescription ne '暂无资料'}" ></div>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div class="cl"></div>

    <div th:include="fragment/fragment:: footer_detail"></div>

<div th:include="fragment/fragment::commonFloat"></div>
<div th:include="fragment/fragment::tongji"></div>
<script >

</script>
</body>
</html>