<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"      xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="UTF-8">
		<title th:text="${houseInfo.projectName}+'_沈阳别墅_沈阳'+${houseInfo.projectName}+'户型图 - 房小二网'"></title>
		<meta name="keywords" th:content="${houseInfo.projectName}+','+${houseInfo.projectName}+'户型图,沈阳'+${houseInfo.projectName}+','+${houseInfo.projectName}+'图片'"/>
		<meta name="description" th:content="'房小二网为您提供沈阳'+${houseInfo.projectName}+'别墅的全部户型图，为购房者挑选合适的户型提供详尽的参考。买别墅，看别墅，就上房小二网。'"/>
		<meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/nphotos/'+projectId+'-wxt-'+villaShipei+'.htm'}">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />
        <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
		<link type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/Villa_index.css?v=20190916" rel="stylesheet" />
		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/Freefone1.css?v=20180522" rel="stylesheet" type="text/css" />
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/default.css?v=20180522"/>
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/huxing.css?v=20190916"/>
		<!--<link type="text/css" href="/css/huxing.css" rel="stylesheet" />-->
		<!--<link type="text/css" href="/css/Villa_index.css" rel="stylesheet" />-->
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
		<script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
        <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
		<!--底部-->
		<script type="application/javascript" src="https://static.fangxiaoer.com/js/layout.js?t=20170904" charset="utf-8"></script>
	</head>
	<body>
	<script th:inline="javascript">
        /*<![CDATA[*/
        var projectId = [[${projectId}]];
        /*]]>*/
	</script>
	<script th:inline="javascript">
		var s = [[${session.muser}]];
		var sid = [[${session.sessionId}]]
		var sph = [[${session.phoneNum}]];

		function filterPhoneNumber(phoneNumber) {
			let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
			//校验手机号是否正确
			if (reg.test(phoneNumber)) {
				phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
				return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
			} else {
				return ''
			}
		}
	</script>
    <style>
        .n20 #close_C{width: 210px !important;}
    </style>
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
	<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>
	<div th:include="house/villa/fragment_menu::newMenu" th:with="type=3"></div>
    <input th:if="${#session?.getAttribute('sessionId') != null}" id="fxe_sessionId" type="hidden" th:value="${#session?.getAttribute('sessionId')}">

		<div class="big_wei">
			<div class="content_box">
				<div class="border">
					<div class="btn_box">
					<div class="left_wei" id="left_btn">
						<img src="https://static.fangxiaoer.com/web/images/Villa/left_arrow.png" />
					</div>
					<div class="btn">
						<div class="show">
						<div class="lunbo">
						<ul>
							<li th:each="roomDetail:${layouts}" th:id="'lay_' + ${roomDetail.layId}" th:class="${#strings.toString(layId) eq #strings.toString(roomDetail.layId)?'S':''}" >
								<div class="line_sun"></div>
								<a th:href="${baseUrl + '-l' + roomDetail.layId + '#layInfo'}">
									<div class="border_sun">
										<h1 th:text="${roomDetail.roomType+'室'+roomDetail.hallType+'厅'+roomDetail.guardType+'卫'}"></h1>
										<span th:if="${roomDetail.state eq '在售'}" class="type_1">在售</span>
										<span th:if="${roomDetail.state eq '待售'}" class="type_2">待售</span>
										<span th:if="${roomDetail.state eq '售罄'}" class="type_3">售罄</span>
										<div class="clearfix"></div>
										<h2 th:text="${'建筑面积：'+roomDetail.buildArea+'㎡'}"></h2>
								</div>
								</a>
							</li>
							<div class="clearfix"></div>
						</ul>
						</div>
						</div>
					</div>
					<div class="right_wei" id="right_btn">
						<img src="https://static.fangxiaoer.com/web/images/Villa/right_arrow.png" />
					</div>
					<div class="clearfix"></div>
					<div class="Select" id="layInfo">
						<h3 th:text="${layoutNow.roomType+'室'+layoutNow.hallType+'厅'+layoutNow.guardType+'卫'}"></h3>
						<span th:text="${layoutNow.state}"></span>
						<div class="clearfix"></div>
						<h4 th:text="${'建筑面积：'+layoutNow.buildArea+'㎡'}"></h4>
					</div>
				</div>
				</div>
				<div class="information" >
                    <h1 class="huxing_wei">户型图（<span class="index">1</span>/<span class="all">16</span>）</h1>
					<div class="photo">

						<div class="show_sun">
							<div class="lunbo">
								<img  th:each="layout:${layout}" th:src="${layout.imageUrl}"/>
								<div class="cl"></div>
							</div>
						</div>
						<div class="left_btn">
							<img src="https://static.fangxiaoer.com/web/images/Villa/left_btn.png" />
						</div>
						<div class="right_btn">
							<img src="https://static.fangxiaoer.com/web/images/Villa/right_btn.png" />
						</div>
						<div class="look"><h1>查看大图</h1></div>
					</div>
					<div class="information_sun">
						<div class="name_box">
							<h1 th:text="${layoutNow.title}"></h1>
							<span th:if="${layoutNow.state eq '在售'}" class="type_1" th:text="${layoutNow.state}"></span>
							<span th:if="${layoutNow.state eq '待售'}" class="type_2" th:text="${layoutNow.state}"></span>
							<span th:if="${layoutNow.state eq '售罄'}" class="type_3" th:text="${layoutNow.state}"></span>

							<div class="clearfix"></div>
						</div>

						<div class="huxing">
							<div class="name">
								<h1>户　　型</h1>
							</div>
							<div class="text">
								<h2 th:text="${layoutNow.roomType+'室'+layoutNow.hallType+'厅'+layoutNow.guardType+'卫'}"></h2>
							</div>
							<div class="clearfix"></div>
						</div>
						<div class="leix">
							<div class="name">
								<h1>类　　型</h1>
							</div>
							<div class="text">
								<h2 th:text="${layoutNow.houseTypeStr}"></h2>
							</div>
							<div class="clearfix"></div>
						</div>
                        <div class="hxBtnNewLayout" th:if="${!#lists.isEmpty(layouts) != null and #lists.size(layouts) > 0 and layoutNow.state ne '售罄'}">
                            <p class="hxBtnNewLayout1 type-compare-btn1"  th:data-layId="${ layoutNow.layId}" th:data-layName="${#numbers.formatInteger(layoutNow.buildArea,2)+'㎡'+layoutNow.roomType+'室'+layoutNow.hallType+'厅'+layoutNow.guardType+'卫'+ houseInfo.projectName}"><i></i>户型对比</p>
                            <p class="hxBtnNewLayout1 type-compare-btn2" th:data-layId="${ layoutNow.layId}" th:data-layName="${#numbers.formatInteger(layoutNow.buildArea,2)+'㎡'+layoutNow.roomType+'室'+layoutNow.hallType+'厅'+layoutNow.guardType+'卫'+ houseInfo.projectName}" th:onclick="'deletelayoud2('+${ layoutNow.layId}+')'"><i></i>取消对比</p>
                            <p class="hxBtnNewLayout2" data-toggle="modal"  th:if="${#session?.getAttribute('sessionId') == null}" href="#login"><i></i>户型收藏</p>
                            <p class="hxBtnNewLayout2" data-toggle="modal"  th:if="${#session?.getAttribute('sessionId') != null}"  id="hxShouc" th:data-layId="${ layoutNow.layId}"><i></i>户型收藏</p>
                            <input  id="fxe_layId" type="hidden" th:value="${layoutNow.layId}">
                        </div>
						<script type="application/javascript">
							$(document).ready(function () {
								var sessionId = $("#fxe_sessionId").val()
								var fxe_layId = $("#fxe_layId").val()

								console.log(sessionId)
								if(sessionId != ""){//页面加载登录后判断是否收藏此户型
									$.ajax({
										type:"POST",
										url:"/manageCollect",
										data:{
											layoutId:fxe_layId ,
											sessionId:sessionId,
											method:"checkFavorite",
										},
										success:function (data) {
											console.log(data)
											if(data.status == 1 ){
												if (data.content == 1){
													$("#hxShouc").addClass("newFavorite")
												}else if(data.content == 0){
													$("#hxShouc").addClass("cancelFavorite")

												}
											}
										},
									})
								}
								$('body').on('click', '.newFavorite', function() {
									$.ajax({
										type:"POST",
										url:"/manageCollect",
										data:{
											layoutId:fxe_layId,
											sessionId:sessionId,
											method:"cancelFavorite",
										},
										success:function (data) {
											$("#hxShouc").removeClass("newFavorite")
											$("#hxShouc").addClass("cancelFavorite")

										},
									})
								})

								$('body').on('click', '.cancelFavorite', function() {
//                                $(".cancelFavorite").click(function () {
									$.ajax({
										type:"POST",
										url:"/manageCollect",
										data:{
											layoutId:fxe_layId,
											sessionId:sessionId,
											method:"newFavorite",
										},
										success:function (data) {
											if(data.status == 1 ){
												$("#hxShouc").addClass("newFavorite")
												$("#hxShouc").removeClass("cancelFavorite")
											}

										},
									})
								})




							})


						</script>

						<div class="clearfix"></div>
						<div class="mianji">
							<div class="name">
								<h1>建筑面积</h1>
							</div>
							<div class="text">
								<h2 th:text="${layoutNow.buildArea+'㎡'}"></h2>
							</div>

						</div>
						<div class="zhengshong" th:unless="${#strings.isEmpty(layoutNow.freeArea)}">
							<div class="name">
								<h1>赠送面积</h1>
							</div>
							<div class="text">
								<h2 th:text="${layoutNow.freeArea}"></h2>
							</div>
							<div class="clearfix"></div>
						</div>
						<div class="clearfix"></div>
						<div class="dianping">
							<div class="name">
								<h1>户型解析</h1>
							</div>
							<div class="text">
								<h2 th:text="${layoutNow.description}"></h2>
							</div>
							<div class="clearfix"></div>
						</div>
						<div class="phone" style="padding-top: 20px;" th:if="${houseInfo.projectStatus ne '3'}">
							<h1 style="display: inline-block;float: left;width: 100%;">售楼处咨询电话</h1>
							<h2 th:utext="${#strings.toString(houseInfo.sortelTel).replace('转','<span>转</span>')}"></h2>
<!--							<button onclick="showUsercode(2) ">免费通话</button>-->
							<div class="clearfix"></div>
					</div>
						<div class="NewvoiceCode" id="voiceCode" style="display: none;" th:if="${layoutNow.voiceTab ne null and #strings.toString(layoutNow.voiceTab) eq '1' and !#lists.isEmpty(layoutNow.voiceInfo)
						and houseInfo.projectStatus ne '3'}">
							<div>
								<img src="" id="showVoice">
								<h6>扫描二维码<br>听我的自我介绍</h6>
							</div>
							<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
							<img id="codelogo" style="display: none" src="https://imageicloud.fangxiaoer.com/event/2022/10/11/133502165.png" crossorigin="anonymous">
							<div id="qrcodeInfo" style="display: none"></div>
							<script type="text/javascript" th:inline="javascript">
                                /*<![CDATA[*/
                                var layoutProject = [[${projectId}]]
                                var voiceLayId = [[${layoutNow.layId}]]
                                /**该方法用来绘制一个有填充色的圆角矩形
                                 *@param cxt:canvas的上下文环境
                                 *@param x:左上角x轴坐标
                                 *@param y:左上角y轴坐标
                                 *@param width:矩形的宽度
                                 *@param height:矩形的高度
                                 *@param radius:圆的半径
                                 *@param fillColor:填充颜色
                                 **/
                                function fillRoundRect(cxt, x, y, width, height, radius, /*optional*/ fillColor) {
                                    //圆的直径必然要小于矩形的宽高
                                    if (2 * radius > width || 2 * radius > height) { return false; }

                                    cxt.save();
                                    cxt.translate(x, y);
                                    //绘制圆角矩形的各个边
                                    drawRoundRectPath(cxt, width, height, radius);
                                    cxt.fillStyle = fillColor || "#000"; //若是给定了值就用给定的值否则给予默认值
                                    cxt.fill();
                                    cxt.restore();
                                }
                                function drawRoundRectPath(cxt, width, height, radius) {
                                    cxt.beginPath(0);
                                    //从右下角顺时针绘制，弧度从0到1/2PI
                                    cxt.arc(width - radius, height - radius, radius, 0, Math.PI / 2);

                                    //矩形下边线
                                    cxt.lineTo(radius, height);

                                    //左下角圆弧，弧度从1/2PI到PI
                                    cxt.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);

                                    //矩形左边线
                                    cxt.lineTo(0, radius);

                                    //左上角圆弧，弧度从PI到3/2PI
                                    cxt.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);

                                    //上边线
                                    cxt.lineTo(width - radius, 0);

                                    //右上角圆弧
                                    cxt.arc(width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);

                                    //右边线
                                    cxt.lineTo(width, height - radius);
                                    cxt.closePath();
                                }
                                var voiceUrl = "https://m.fangxiaoer.com/fang1/"+layoutProject+"-2/layout/pid"+layoutProject+"-pt2-l" + voiceLayId;
                                $('#qrcodeInfo').qrcode({
                                    render : "canvas",
                                    width: 160,
                                    height: 160,
                                    text: voiceUrl
                                }).hide();
                                var canvasinfo = $("#qrcodeInfo canvas")[0];
                                //添加logo
                                var codeImage = document.querySelector("#codelogo");
                                codeImage.crossOrigin = "anonymous"
                                var ctx = canvasinfo.getContext('2d')
                                fillRoundRect(ctx, 59, 59, 42, 42 ,3, '#fff')
                                ctx.drawImage(codeImage, 61, 61, 38, 38);
                                $('#showVoice').attr('src', canvasinfo.toDataURL('image/png'));
                                $("#voiceCode").show();
                                /*]]>*/
							</script>
						</div>



						<div class="clearfix"></div>
				</div>
				<div class="clearfix"></div>
			</div>
		</div>
	<!--电话-->
	<div  th:include="house/detail/fragment_order::useCode"  ></div>
	<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
		<div class="layer_box">
			<table>
				<tr>
					<td>
						<div class="layer">
							<h1>户型图（<span class="index">1</span>/<span class="all">16</span>）</h1>
							<div class="photo">
								<div class="lunbo">
									<ul>
										<li th:each="item:${layout}">
											<img th:src="${item.imageUrl}" th:alt="${item.description}" />
										</li>
										<div class="clearfix"></div>
									</ul>
								</div>
							</div>
							<div class="close">
								<img src="https://static.fangxiaoer.com/web/images/Villa/close.png" />
							</div>
							<div class="left_btn">
                                <img src="https://static.fangxiaoer.com/web/images/Villa/left_wei.png" class="left_wei" />
                                <img src="https://static.fangxiaoer.com/web/images/Villa/left_wei02.png" class="hover" />
							</div>
							<div class="right_btn">
                                <img src="https://static.fangxiaoer.com/web/images/Villa/right_wei.png" class="right_wei"/>
                                <img src="https://static.fangxiaoer.com/web/images/Villa/right_wei02.png" class="hover" />
							</div>
						</div>
					</td>
				</tr>
			</table>
		</div>
	<div th:include="fragment/fragment::common_meiqia"></div>
    <div class="footer" th:include="fragment/fragment::footer_detail"></div>
	<!--统计-->
	<div th:include="fragment/fragment::tongji"></div>
	<!--右侧浮标-->
	<div th:include="fragment/fragment::commonFloat"></div>
	<!--登录引导-->
	<div th:include="house/detail/fragment_login::login"></div>
	<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
	</body>
<script>


</script>
</html>
