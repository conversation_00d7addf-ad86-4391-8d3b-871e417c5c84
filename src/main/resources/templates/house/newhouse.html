<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="https://www.w3.org/1999/xhtml"
      xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:if="${titleType == null or titleType == '1' or titleType == '2' or titleType == '3'}" th:text="'沈阳' + ${seoTitle} + '楼盘_沈阳' + ${seoTitle} + '新楼盘信息_沈阳房价 - 房小二网'"></title>
    <meta th:if="${titleType == null or titleType == '1' or titleType == '2' or titleType == '3'}" name="keywords" th:content="'沈阳' + ${seoTitle} + '楼盘,沈阳' + ${seoTitle} + '房价,沈阳新楼盘信息,沈阳房价走势,沈阳房产信息'">
    <meta th:if="${titleType == null or titleType == '1' or titleType == '2' or titleType == '3'}" name="description" th:content="'房小二网为您提供最新沈阳' + ${seoTitle} + '房产信息,沈阳' + ${seoTitle} + '楼盘信息,沈阳房价走势以及沈阳新楼盘信息，地铁房，学区房新政策等最新资讯。'">
    <title th:if="${titleType == '4'}" th:text="'沈阳' + ${seoTitle} + '写字楼_沈阳' + ${seoTitle} + '写字间_沈阳新楼盘 - 房小二网'"></title>
    <meta th:if="${titleType == '4'}" name="keywords" th:content="'沈阳' + ${seoTitle} + '写字楼,沈阳' + ${seoTitle} + '写字间,沈阳写字楼出租,写字间出租,写字楼出售,写字楼出租,沈阳商业租房'">
    <meta th:if="${titleType == '4'}" name="description" th:content="'沈阳写字楼网为您提供专业的沈阳' + ${seoTitle} + '写字楼出租、沈阳' + ${seoTitle} + '写字楼出售、沈阳写字间出租等商业地产的出售与租赁信息，免费查找、发布沈阳公寓写字楼信息，请到房小二网。'">
    <title th:if="${titleType == '5'}" th:text="'沈阳' + ${seoTitle} + '别墅 专业的沈阳' + ${seoTitle} + '别墅信息网 - 房小二网'"></title>
    <meta th:if="${titleType == '5'}" name="keywords" th:content="'沈阳' + ${seoTitle} + '别墅,别墅新房,别墅价格,租赁别墅,别墅新闻,别墅,房小二网'">
    <meta th:if="${titleType == '5'}" name="description" th:content="'沈阳别墅网为您提供精美的' + ${seoTitle} + '别墅图片大全以及高端的别墅设计效果图，带给您美的享受。专业的沈阳别墅搜索，为你提供最准确的沈阳别墅信息。买别墅，看别墅，敬请关注房小二沈阳别墅网。'">
    <title th:if="${titleType == '6'}" th:text="'沈阳' + ${seoTitle} + '楼盘_沈阳房企_沈阳知名地产 - 房小二网'"></title>
    <meta th:if="${titleType == '6'}" name="keywords" th:content="'沈阳' + ${seoTitle} + '楼盘,沈阳房企,沈阳知名地产,沈阳房企,品牌房企'">
    <meta th:if="${titleType == '6'}" name="description" th:content="'房小二网品牌房企栏目，为您提供丰富全面的沈阳' + ${seoTitle} + '品牌房企新楼盘信息，定期举办购房优惠活动，为您购房提供更大的便利条件，详细关注沈阳品牌房企项目信息，尽在房小二网品牌房企。'">
    <title th:if="${titleType == '7'}" th:text="'沈阳' + ${seoTitle} + '地铁房_沈阳' + ${seoTitle} + '地铁沿线楼盘_沈阳地铁附近房源 - 房小二网'"></title>
    <meta th:if="${titleType == '7'}" name="keywords" th:content="'沈阳' + ${seoTitle} + '地铁房,沈阳' + ${seoTitle} + '地铁沿线楼盘,沈阳地铁附近房源'">
    <meta th:if="${titleType == '7'}" name="description" th:content="'房小二网沈阳地铁房栏目为您提供海量真实的沈阳' + ${seoTitle} + '地铁附近楼盘，地铁各号线房源信息，及时的地铁各号线房屋出售信息以及中介信息，带来最佳的沈阳各号线地铁房买卖体验。'">
    <title th:if="${titleType == '8'}">沈阳现房_沈阳现房楼盘_沈阳新楼盘 - 房小二网</title>
    <meta th:if="${titleType == '8'}" name="keywords" content="沈阳现房,现房入住,沈阳现房发售,现房发售,沈阳新楼盘,沈阳新房购买">
    <meta th:if="${titleType == '8'}" name="description" content="房小二网现房入住为您提供准确的沈阳在售现房楼盘信息，让您更快、更方便地了解沈阳在售现房房源信息，查找、购买沈阳现房楼盘，就在房小二网现房入住。">
    <title th:if="${titleType == '9'}">沈阳低首付楼盘_沈阳0首付楼盘_沈阳降价楼盘 - 房小二网</title>
    <meta th:if="${titleType == '9'}" name="keywords" content="沈阳低首付,沈阳低首付楼盘,沈阳降价楼盘,沈阳打折楼盘,沈阳买房">
    <meta th:if="${titleType == '9'}" name="description" content="房小二网为您提供沈阳在售低首付楼盘、沈阳低首付新楼盘信息，提供权威的房价走势分析，全面的沈阳低首付新楼盘，帮您快速找到适合您的低首付楼盘信息。">
    <meta name="mobile-agent" th:if="${titleType == '1' or titleType == '2' or titleType == '3'  or titleType == '4'  or titleType == '5' or titleType == '6' or titleType == '8' or titleType == null}"
          th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1l/'+mobileAgent}">
    <meta name="mobile-agent" th:if="${titleType == '7'}" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1dt/'+mobileAgent}">
    <meta name="mobile-agent" th:if="${titleType == '9'}" th:content="${'format=html5;url=https://m.fangxiaoer.com//fang1l/ilp1-'+mobileAgent}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20190112" rel="stylesheet" type="text/css" />
    <!--<link href="/css/main2017.css" rel="stylesheet" type="text/css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20240318" rel="stylesheet" type="text/css" />
<!--    <link href="/css/main2017.css?v=20190919" rel="stylesheet" type="text/css" />-->
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20190112">
<!--    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newHouse.css?t=20211123" rel="stylesheet" type="text/css">-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/maifang_right/newHouse.css?t=20211123" rel="stylesheet" type="text/css">
    <!--<link href="/css/newHouse.css" rel="stylesheet" type="text/css">-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/modal.js"></script>
    <!--<script src="https://static.fangxiaoer.com/js/bootstrap.min.js" type="text/javascript"> </script>-->
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/js/tc/tc.css?v=20190112" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/house/contrastHouse.js?v=20240510" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/swiper/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/swiper.min.css" rel="stylesheet" type="text/css">
<!--    <link rel="stylesheet" href="/css/newHouse.css">-->
    <style type="text/css">
        .houseInfo>.info>li .onsale{
            color: #fff !important;
            background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/onsale.png) no-repeat !important;
        }
        .houseInfo>.info>li .waitsale{
            color: #fff !important;
            background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/waitsale.png) no-repeat !important;
        }
        .houseInfo>.info>li .sellout{
            color: #fff !important;
            background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/sellout.png) no-repeat !important;
        }
    </style>
    <style>
        /*隐藏联系电话*/
        .search_tel{  display: none;  }
        .select_box{border: none}
        #option_other ul li div+div  ul{height:180px}
        .fxe-alert {position: absolute;background: url("https://static.fangxiaoer.com/global/imgs/ico/b60.png");color: #fff;font-size: 11pt;text-align: center;border-radius: 6px;padding: 10px 0;z-index: 1000000;}
        .sort{ float: right;margin-right: -80px}
        .sort #sortParamNew a.hover, .sort #sortParamNew a:hover{border: 1px solid #ff5200;}
        .sort #sortParamNew a{border: 1px solid #eaeaea;margin-left: -5px;line-height: 26px;font-size: 14px}
        .sort #sortParam a {border: none;background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/salePriseIcon_05.png) no-repeat;background-position: 65px 7px;border: none}
        .sort #sortParam a.sort_jg {background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/salePriseIcon_03.png) no-repeat;padding: 0 15px 0 16px;}
        .sort #sortParam a.down {background-position: 46px 7px;}
        .sort #sortParam a.down:hover{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/salePriseIcon_03.png) no-repeat;
            background-position: 46px 7px}
        .sort #sortParam a.up{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/salePriseIcon_033.png) no-repeat;}
        .sort #sortParam a.up:hover{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/salePriseIcon_033.png) no-repeat;
            background-position: 46px 7px}
        .sort #sortParam a+a {padding: 0 15px 0 16px;background-position: 46px 7px;}
        .sort #sortParam a.down,.sort #sortParam a.up{border:none;background-color:#fff;color: #ff5200;background-position: 46px 7px;}
        .sort #sortParam a:hover,.sort sortP#aram a.hover{background-position: 65px 7px;border: none;color: #ff5200;}
        .sort #sortParam a+a.hover,.sort #sortParam a+a:hover, .sort #sortParam a+a:hover,.sort #sortParam a+a+a+a+a:hover {background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/salePriseIcon_03.png) no-repeat;border: none;color: #ff5200;background-position: 46px 7px;padding: 0 15px 0 16px;}
        .sort  #sortParam a+a+a+a+a:hover,.sort  #sortParam a+a+a+a+a{background: none !important;}
        .sort #sortParam a.hover{background: none;color: #ff5200;border: 0}
        .sort #sortParam a:hover{background: none}
        .sort #sortParam a:nth-child(1){background: none}
        .sort {
            height: auto;
            line-height: 42px;
            background: #fff;
        }
        .lp_count {
            height: auto;
            padding: 0;
            border-left: 1px solid #ededed;
            border-right: 1px solid #ededed;
            border-bottom: 1px solid #ededed;
        }
        #sortParam{ margin-top: 11px}
        .panorama{
            float: left!important;
            width: 101px!important;
            border-right: 1px solid #eee;
            border-left: 1px solid #eee;
            padding-left: 20px;
            padding-right: 20px;
        }
        .panorama img {
            margin-top: 18px;
            margin-right: 10px;
        }
        .panorama .hover_sun {
            display: none;
        }
        .panoImg{
            width: 55px!important;
            height: 61px!important;
            position: absolute;
            left: 96.5px;
            top: 70.5px;
        }
        .panoImg1{
            width: 28px!important;
            height: 32px!important;
            position: absolute;
            top:auto;
            left: 26px;
            bottom: 29px;
        }
        .panorama:hover img {
            display: none;
        }
        .panorama:hover .hover_sun {
            display: inline-block;
        }
        .ranks_KFenter span:hover{
            color: #ff5200 !important;
        }
        .vrs{ width: 34px; height: 34px; position: absolute; left: 24px; bottom: 29px; z-index: 0; }
        .vrs i{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 3; display: block; background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;}
        .vrs b{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 1; display: block; background-color: rgba(0,0,0,0.5); border-radius: 50%;}

        /*小二甄选*/
        .g_eselect { float: left!important; width: 101px!important; border-right: 1px solid #eee; border-left: 1px solid #eee; padding-left: 20px; padding-right: 20px; color: #333333; text-align: center; }
        .g_eselect:hover { color: #ff5200 !important; }
        .listHeader .hover2 span { background-color: #FF3737; border-radius: 18px 0px 18px 0px; color: #fff !important; padding: 2px 14px; }
        .info i { background-color: #FF3737; border-radius: 18px 0px 18px 0px; color: #fff !important; padding: 2px 12px; font-style: italic; font-size: 12px; /*position: absolute; top: -2px; right: -81px;*/ vertical-align: top; margin-left: 8px; }
        .info li b { position: relative; }
        .g_fi{ right: -112px !important;}
        .listHeader a{ font-weight: bolder;}
        /*视频 全景看房*/
        .g_sqho{ display: inline-block; padding-left: 17px;}
        .g_sqho a{ color: #425571; font-size: 12px; display: inline-block;  margin-left: 1px; text-align: center; width: 78px; position: relative;}
        .g_sqho a:hover{ text-decoration: none !important; }
        .g_sqho a i{ background-image: url(https://static.fangxiaoer.com/web/styles/sy/buyhouse/ck5.png); background-size: 100%; background-repeat: no-repeat; background-position: center; width: 14px; height: 14px; position: absolute; top: 14px; left: -2px;}
        .g_sv i{ background-image: url(https://static.fangxiaoer.com/web/styles/sy/buyhouse/ck6.png) !important; }
        .listHeader .allNewHouses{    float: left;
            width: auto;
            margin-right: 56px;    font-weight: 400;}
        .listHeader .allNewHouses.hover,.listHeader .allNewHouses:hover{    border-bottom: 3px solid #ff5200;
            height: 54px;}
        .listHeader .allNewHouses .liveHuo{display: block;
            width: 18px;
            height: 22px;
            background: url(https://static.fangxiaoer.com/images/coin/liveIndex.png) top center;
            background-size: 100% 100%;
            float: right;
            margin-left: 3px;
            margin-top: 16px;}
    </style>
</head>
<!--新房列表页-->
<body>


<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<!--搜索框-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1,listType=0,searchTab=0"></div>
<!--当前网站位置信息-->
<div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/houses/">沈阳新房</a>
    <th:block th:if="${titleType == '' or titleType == null or titleType == '1' or titleType == '2' or titleType == '3' or titleType == '4'
     or titleType == '5'}">
        <th:block th:each="r:${region}" th:if="${r.selected and r.name ne '全部'}">
            &gt;<a  th:href=${"/houses/r"+r.id} th:text="${r.name + '新房'}">楼盘</a>
        </th:block>
    </th:block>
    <a th:if="${titleType == '6'}" href="/brands/">品牌房企</a>
    <a th:if="${titleType == '7'}" href="/subways/k1">地铁沿线</a>
    <a th:if="${titleType == '8'}" href="/exits/">现房</a>
</div>

<!--list用途未知-->
<div class="list"></div>
<!--list用途未知-->
<!--楼盘条件搜索 页面中用途未知-->
<div class="title w mt120">
    <p class="hover"><a href="/houses">楼盘搜索</a></p>
    <p><a href="/subways/">地铁沿线</a></p>
    <p><a href="/schools/">优质学区</a></p>
    <p><a href="/lowpays/">低首付楼盘</a></p>
    <p><a href="/exits/">现房入住</a></p>
</div>
<!--楼盘条件搜索-->
<input type="hidden" id="sessionId" th:value="${session.sessionId}">


<div class="main">
    <!--条件搜索-->
    <div id="option">
        <ul>
            <li class="fenlei">
                <p>位置：</p>
                <a id="btnRegion" th:each="r,i:${region}" th:if="${i.index eq 0 }" th:href="${#strings.toString(r.url).replace('subways','houses')}"><b class="listIcon listIcon_dw"></b>区域<i></i></a>
                <a id="btnSubway" th:each="k,i:${kinds}" th:if="${i.index eq 1 }" th:href="${#strings.toString(k.url).replace('houses','subways')}"><b class="listIcon listIcon_ditie"></b>地铁<i></i></a>
<!--                <a href="/schools/"><b class="listIcon listIcon_xq"></b>学区<i></i></a>-->
                <a href="/static/houseMap.htm"><b class="listIcon listIcon_map"></b>地图<i></i></a>
            </li>
            <!--区域-->
            <li id="Search_zf" class="leibie" style="display: none">
                <a th:each="region:${region}" th:text="${#strings.toString(region.name).replaceAll('全部','不限')}" th:href="${region.url}" th:class="${region.selected}? 'hover':''"></a>
                <!--<span th:if="${plate}">
                        <a th:each="j:${plate}" th:text="${j.name}"  onclick="showIndex(this)" th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
                </span>-->
            </li>
            <!--地铁-->
            <li id="Search_ditie" class="leibie" style="display: none">
                <a th:each="subway:${subway}" th:text="${#strings.toString(subway.name).replaceAll('全部','不限')}" th:href="${subway.url}" th:class="${subway.selected}? 'hover':''"></a>
                <br/>
                <span th:if="${subwayStation}">
                    <a th:each="station:${subwayStation}" th:text="${#strings.toString(station.name).replaceAll('全部','不限')}" th:href="${station.url}" th:class="${station.selected}? 'hover':''"></a>
                </span>
            </li>
            <li ><p>价格：</p>
                <th:block th:if="${HouseType eq 'small'}">
                    <a th:each="price:${price}" th:text="${#strings.toString(price.name).replaceAll('全部','不限')}" th:href="${price.url}" th:class="${price.selected}? 'hover':''"></a>
                </th:block>
                <th:block  th:if="${HouseType eq 'big'}">
                    <a th:each="price:${bigPrice}" th:text="${#strings.toString(price.name).replaceAll('全部','不限')}" th:href="${price.url}" th:class="${price.selected}? 'hover':''"></a>
                </th:block>
                <div id="Search_PriceDomOk">
                    <label>
                        <input name="minPrice" id="minPrice" maxlength="5" type="text" th:value="${minPrice}" >
                        -
                        <input name="maxPrice" id="maxPrice" maxlength="5" type="text" th:value="${maxPrice}" >
                        <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button">
                    </label>
                    <script type="text/javascript">
                        function __doPostBack(pager1, page) {
                            var url = window.location.pathname;
                            if (pager1 == "Search$Btn_Search1") {
                                var priceBegin = $("#minPrice").val();
                                var priceEnd = $("#maxPrice").val();
                                var ref1 = url.replace(/-lp[0-9]\d*/,''); //k最小值
                                var ref2 = ref1.replace(/-hp[0-9]\d*/,'');  //x最大值
                                if (parseInt(priceEnd) < parseInt(priceBegin)){
                                    priceEnd = [priceBegin,priceBegin=priceEnd][0];
                                }
                                if(priceBegin != "" && priceBegin != 0  && url.indexOf("bp") == -1 )
                                    ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-lp" + priceBegin;
                                if(priceEnd != ""  && url.indexOf("bp") == -1 )
                                    ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-hp" + priceEnd;
                                if(priceBegin != "" && priceBegin != 0 && url.indexOf("bp") != -1 )
                                    ref2 = ref2.replace(/-bp[0-9]\d*/,'').replace(/\/bp[0-9]\d*/,'\/') + "-lp" + priceBegin;
                                if(priceEnd != "" && url.indexOf("bp") != -1 )
                                    ref2 = ref2.replace(/-bp[0-9]\d*/,'').replace(/\/bp[0-9]\d*/,'\/') + "-hp" + priceEnd;
                                location.href = ref2;
                            }
                        }
                    </script>
                    <script type="text/javascript">
                        function price(priceIdName) {
                            $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
                            $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
                            var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
                            var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
                            if (num1 == "" && num2 != "") {
                                $("#" + priceIdName + " input").eq(0).val("0");
                                $("#" + priceIdName + " input").eq(2).show();
                            } else if (num2 == "" && num1 != "") {
                                $("#" + priceIdName + " input").eq(2).show();
                            } else if (num1 != "" || num2 != "") {
                                $("#" + priceIdName + " input").eq(2).show();
                            } else {
                                $("#" + priceIdName + " input").eq(2).hide();
                            }
                        }
                        price("Search_PriceDomOk");
                        $("#Search_PriceDomOk input").keyup(function () {
                            price("Search_PriceDomOk");
                        })
                        $("#Search_PriceDomOk").keydown(function (event) {
                            if (event.keyCode == 13) {
                                // $("#Search_Btn_Search1").click()
                            }
                        });
                    </script>
                </div>
            </li>
            <li th:each="projectType,i:${projectType}" th:if="${i.index eq 3 and !projectType.selected}"><p>户型：</p>
                <a th:each="layout:${layout}" th:text="${#strings.toString(layout.name).replaceAll('全部','不限')}" th:href="${layout.url}" th:class="${layout.selected}? 'hover':''"></a>
            </li>
            <li><p>面积：</p>
                <a th:each="areas:${areas}" th:text="${#strings.toString(areas.name).replaceAll('全部','不限')}" th:href="${areas.url}" th:class="${areas.selected}? 'hover':''"></a>
            </li>
            <li><p>类型：</p>
                <a th:each="projectType:${projectType}" th:text="${#strings.toString(projectType.name).replaceAll('全部','不限')}" th:href="${projectType.url}" th:class="${projectType.selected}? 'hover':''"></a>
            </li>
            <!--<li th:if="${titleType == '8'}"><p>入住：</p>-->
            <!--<a th:each="month:${month}" th:href="${month.url}" th:text="${#strings.toString(month.name).replaceAll('全部','不限')}" th:class="${month.selected}? 'hover':''"></a>-->
            <!--</li>-->
        </ul>
    </div>
    <!--搜索结束-->
    <div id="option_other">
        <ul>
            <li><p>更多：</p>
                <div class="select_box">
                    <div class="select_info">
                        <th:block th:each="ps:${saleStatus}" th:if="${ps.selected}" th:text="${#strings.toString(ps.name).replaceAll('全部','销售状态')}"></th:block>
                    </div>
                    <ul style="height: 90px">
                        <li>
                            <a th:each="ps,i:${saleStatus}" th:if="${i.index &gt; 0}" th:text="${#strings.toString(ps.name).replaceAll('全部','环线不限')}" th:href="${ps.url}" th:class="${ps.selected}? 'hover':''"></a>
                        </li>
                    </ul>
                </div>

                <div class="g_sqho">
                    <a th:class="${videoSelect.get(1).selected}? 'g_sv':''" th:if="${!#lists.isEmpty(videoSelect) and #lists.size(videoSelect) ge 2}"  th:href="${videoSelect.get(1).url}">
                        <i></i><th:block th:text="${videoSelect.get(1).name}"></th:block>
                    </a>
                    <a th:class="${videoSelect.get(2).selected}? 'g_sv':''" th:href="${videoSelect.get(2).url}">
                        <i></i><th:block th:text="${videoSelect.get(2).name}"></th:block>
                    </a>
                </div>

                <!--<div class="select_box">
                    <div class="select_info" id="frature"></div>
                    <ul style="height: 90px!important;">
                        &lt;!&ndash;<li> <a id="frature1" href=""  th:text="特色" ></a></li>&ndash;&gt;
                        &lt;!&ndash;<li> <a id="frature2" href=""  th:text="地铁房" ></a></li>&ndash;&gt;
                        &lt;!&ndash;<li> <a id="frature3" href=""  th:text="低首付" ></a></li>&ndash;&gt;
                        <li> <a id="frature4" href=""    th:text="现房" ></a></li>
                        <li> <a id="frature6" href=""    th:text="精装房" ></a></li>
                        <li> <a id="frature5" href=""   th:text="品牌房企" ></a></li>

                    </ul>
                </div>
                <div class="select_box">
                    <div class="select_info">
                        <th:block th:each="loop:${loop}" th:if="${loop.selected}" th:text="${#strings.toString(loop.name).replaceAll('全部','环线')}"></th:block>
                    </div>
                    <ul>
                        <li>
                            <a th:each="loop,i:${loop}" th:if="${i.index &gt; 0}" th:text="${#strings.toString(loop.name).replaceAll('全部','环线不限')}" th:href="${loop.url}" th:class="${loop.selected}? 'hover':''"></a>
                        </li>
                    </ul>
                </div>-->
                <script>
                    var nameUrl = location.pathname;
                    var newUrl = nameUrl.substring(nameUrl.indexOf("/",2));
                    newUrl = newUrl.replace(/-n\d+/,'').replace(/\/n\d+/,'\/');
                    //                            $("#frature1").attr("href","/houses"+newUrl.replace(/-e[0-9]\d*/,'').replace(/\/e[0-9]\d*/,'\/'))
                    $("#frature2").attr("href","/subways"+newUrl+"-k1")
                    $("#frature3").attr("href","/lowpays"+newUrl.replace(/-e[0-9]\d*/,'').replace(/\/e[0-9]\d*/,'\/'))
                    $("#frature4").attr("href","/exits"+newUrl)
                    $("#frature5").attr("href","/brands"+newUrl.replace(/-e[0-9]\d*/,'').replace(/\/e[0-9]\d*/,'\/'))
                    $("#frature6").attr("href","/decorations"+newUrl.replace(/-e[0-9]\d*/,'').replace(/\/e[0-9]\d*/,'\/'))
                    if (nameUrl.startsWith("/houses")) {$("#frature").text("特色");}
                    if (nameUrl.startsWith("/subways")){$("#frature").text("特色");}
                    if (nameUrl.startsWith("/lowpays")){$("#frature").text("低首付");}
                    if (nameUrl.startsWith("/exits"))  {$("#frature").text("现房");}
                    if (nameUrl.startsWith("/brands")) {$("#frature").text("品牌房企");}
                    var regorsub = nameUrl.indexOf("k1");
                    if (regorsub != -1){$("#Search_ditie").css("display","block");$("#btnSubway").attr("class","hover")}
                    else {$("#Search_zf").css("display","block");$("#btnRegion").attr("class","hover")}
                </script>
            </li>
        </ul>
    </div>
    <div id="option_info" style="display: none">
        <b >已选：</b>
        <!--区域-->
        <div th:each="r:${region}" th:if="${r.selected and r.name ne '全部'}">
            <span class="condition"><th:block th:text="${r.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'r'+r.id}"></i>
        </div>
        <!--地铁线-->
        <div th:each="l:${subway}" th:if="${l.selected and l.name ne '全部'}">
            <span class="condition"><th:block th:text="${l.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'l'+l.id}"></i>
        </div>
        <!--地铁站-->
        <div th:each="s:${subwayStation}" th:if="${s.selected and s.name ne '全部'}">
            <span class="condition"><th:block th:text="${s.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'s'+s.id}"></i>
        </div>
        <!--价格-->
        <div th:each="p:${price}" th:if="${p.selected and p.name ne '全部'}">
            <span class="condition"><th:block th:text="${p.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'p'+p.id}"></i>
        </div>
        <!--价格-->
        <div th:each="bp:${bigPrice}" th:if="${bp.selected and bp.name ne '全部'}">
            <span class="condition"><th:block th:text="${bp.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'bp'+bp.id}"></i>
        </div>
        <!--价格自填-->
        <div th:if="${!#strings.isEmpty(minPrice) or !#strings.isEmpty(maxPrice)}">
            <span class="condition">
                <th:block th:if="${#strings.isEmpty(minPrice)}" th:text="${'最大价格'+maxPrice}"></th:block>
                <th:block th:if="${#strings.isEmpty(maxPrice)}" th:text="${'最小价格'+minPrice}"></th:block>
                <th:block th:if="${!#strings.isEmpty(maxPrice) and !#strings.isEmpty(minPrice)}" th:text="${minPrice+'-'+maxPrice}"></th:block>
            </span>
            <i class="cleanUrl" th:if="${#strings.isEmpty(minPrice)}" th:value="${'hp'+maxPrice}"></i>
            <i class="cleanUrl" th:if="${#strings.isEmpty(maxPrice)}" th:value="${'lp'+minPrice}"></i>
            <i class="cleanUrl" th:if="${!#strings.isEmpty(maxPrice) and !#strings.isEmpty(minPrice)}" th:value="${'lp'+minPrice+'-'+'hp'+maxPrice}"></i>
        </div>
        <!--户型-->
        <div th:each="t:${layout}" th:if="${t.selected and t.name ne '全部'}">
            <span class="condition"><th:block th:text="${t.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'t'+t.id}"></i>
        </div>
        <!--面积-->
        <div th:each="t:${areas}" th:if="${t.selected and t.name ne '全部'}">
            <span class="condition"><th:block th:text="${t.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'ar'+t.id}"></i>
        </div>
        <!--类型-->
        <div th:each="pt:${projectType}" th:if="${pt.selected and pt.name ne '全部'}">
            <span class="condition"><th:block th:text="${#strings.toString(pt.name)}"></th:block></span>
            <i class="cleanUrl" th:value="${'pt'+pt.id}"></i>
        </div>
        <!--入住-->
        <!--<div th:each="e:${month}" th:if="${e.selected and e.name ne '全部'}">-->
        <!--<span class="condition"><th:block th:text="${e.name}"></th:block></span>-->
        <!--<i class="cleanUrl" th:value="${'e'+e.id}"></i>-->
        <!--</div>-->
        <!--销售状态-->
        <div th:each="ss:${saleStatus}" th:if="${ss.selected and ss.name ne '全部'}">
            <span class="condition"><th:block th:text="${ss.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'ps'+ss.id}"></i>
        </div>
        <!--特色-->
        <div th:if="${titleType == '6'} or ${titleType == '8'} or ${titleType == '9'}">
            <span class="condition"><th:block  th:if="${titleType == '6'}">品牌房企</th:block></span>
            <span class="condition"><th:block  th:if="${titleType == '8'}">现房</th:block></span>
            <!--<span class="condition"><th:block  th:if="${titleType == '9'}">低首付</th:block></span>-->
            <i class="cleanTeseUrl" th:if="${titleType == '6'}" value="/brands/"></i>
            <i class="cleanTeseUrl" th:if="${titleType == '8'}" value="/exits/"></i>
            <i class="cleanTeseUrl" th:if="${titleType == '9'}" value="/lowpays/"></i>
        </div>
        <script>
            $(".cleanTeseUrl").click(function () {
                var oldUrl = location.pathname;
                var oldUrl1 = $(this).attr("value");
                var newUrl = oldUrl.replace(oldUrl1,"/houses/");
                window.location.href = newUrl;
            })
        </script>
        <!--环线-->
        <div th:each="o:${loop}" th:if="${o.selected and o.name ne '全部'}">
            <span class="condition"><th:block th:text="${o.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'o'+o.id}"></i>
        </div>
        <a href="/houses/" class="clean">清除全部</a>
        <script>
            /*判断是否显示条件栏目*/
            $(function () {
                if($(".condition").text() != ""){
                    $("#option_info").css("display","block");
                }
            })
            /*去掉条件标签*/
            $(".cleanUrl").click(function () {
                var oldUrl = location.pathname;
                if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
                    var moreUrl ="-" + $(this).attr("value");
                }else{ var moreUrl = $(this).attr("value");}

                var newUrl = oldUrl.replace(moreUrl,"");
                window.location.href = newUrl;
                //跳到newUrl指定链接
            });
        </script>
    </div>

    <div class="cl"></div>
    <div class="w">
        <div id="left">
            <!--通栏广告-->
            <div th:include="fragment/fragment:: commom_horizontalBanner"></div>
            <div class="listHeader">
                <script th:inline="javascript">
                    console.log([[${videoSelect}]])
                    console.log([[${selectionState}]])
                </script>
<!--                <a href="" class="g_eselect hover2" ><span>小二甄选</span></a>-->
                <a th:class="${selectionState.get(0).selected and (#lists.isEmpty(videoSelect) or !videoSelect.get(3).selected) }? 'allNewHouses hover':'allNewHouses '" th:if="${!#lists.isEmpty(selectionState) and #lists.size(selectionState) gt 1}" href="/houses"  >全部楼盘</a>
<!--                <a th:class="${!#lists.isEmpty(videoSelect) and videoSelect.get(3).selected}? 'allNewHouses hover':'allNewHouses '" th:href="${videoSelect.get(3).url}">直播看房<s class="liveHuo"></s></a>-->
                <!--<a th:class="${selectionState.get(1).selected}? 'g_eselect hover2':'g_eselect'" th:href="${selectionState.get(1).url}" th:if="${!#lists.isEmpty(selectionState) and #lists.size(selectionState) ge 2}"><span>小二甄选</span></a>-->
                <a href="/static/houseMap.htm" class="map_sun"><i class="map"></i>地图</a>
                <div class="clearfix"></div>
                <!--                <a href="" class="">点评<i class="up"></i></a>
                                <a href="" class="hover">价格<i class="down"></i></a>
                                <a href="" style="padding-right: 14px;">综合排序</a>-->


            </div>
            <div class="lp_count">
                <div class="lp_con">小二为你找到<span class="search_count" th:text="${msg}"></span>个符合条件的楼盘</div>
                <div class="sort">
                    <p id="sortParam">
                        <a id="0" href="">默认排序</a>
                        <a id="1" href="">价格</a>
<!--                        <a id="3" href="">点评</a>-->
                        <script>
                            $(document).ready(function(){
                                var ref = location.pathname;
                                var y = "";
                                if (ref.startsWith("/houses")) {y = ref.split('/houses/');}
                                if (ref.startsWith("/subways")){y = ref.split('/subways/');}
                                if (ref.startsWith("/lowpays")){y = ref.split('/lowpays/');}
                                if (ref.startsWith("/exits"))  {y = ref.split('/exits/');}
                                if (ref.startsWith("/brands")) {y = ref.split('/brands/');}
                                var x = y.length > 1 ? y[1] : "";
                                if(x.indexOf("lo") != -1){
                                    x = x.replace("lo","");
                                }
                                if(x.indexOf('o') != -1){
                                    var num = x.split('o')[1].substring(0,1);
                                    if(num == 0 || num == 3){
                                        $("#sortParam a[id ='"+ num+"']").addClass("hover");
                                    }else{
                                        if(num%2 == 1){
                                            $("#sortParam a[id ='"+ num+"']").addClass("sort_jg");
                                            $("#sortParam a[id ='"+ num+"']").addClass("down");
                                            $("#sortParam a[id ='"+ num+"']").attr("id",parseInt(num)+1);
                                        }else{
                                            num = parseInt(num) -1;
                                            $("#sortParam a[id ='"+ num+"']").addClass("sort_jg");
                                            $("#sortParam a[id ='"+ num+"']").addClass("up");
                                        }
                                    }
                                }else{
                                    $("#sortParam a:eq(0)").addClass("hover");
                                }
                                var ref2 = ref.replace(/-o[0-9]/,'');
                                if(ref2.startsWith('/houses') && ref2.indexOf('/houses/') ==-1){
                                    ref2 = ref2.replace('/houses','/houses/');
                                }else if(ref2.startsWith('/subways') && ref2.indexOf('/subways/') ==-1){
                                    ref2 = ref2.replace('/subways','/subways/');
                                }else if(ref2.startsWith('/lowpays') && ref2.indexOf('/lowpays/') ==-1){
                                    ref2 = ref2.replace('/lowpays','/lowpays/');
                                }else if(ref2.startsWith('/exits') && ref2.indexOf('/exits/') ==-1){
                                    ref2 = ref2.replace('/exits','/exits/');
                                }else if(ref2.startsWith('/brands') && ref2.indexOf('/brands/') ==-1){
                                    ref2 = ref2.replace('/brands','/brands/');
                                }
                                $("#sortParam a").each(function () {
                                    var ids = $(this).attr("id");
                                    var ref3 = ref2 + '-o' + ids;
                                    $(this).attr("href",ref3);
                                });
                            });
                        </script>
                    </p>

                </div>
                <div class="cl"></div>
            </div>
            <div class="cl"></div>
            <div th:class="${#lists.isEmpty(recommentResult)?'list_wei list_wei_none':'tuijian_wei'}">
                <!--新房列表页房源信息-->
                <div class="houseInfo" th:each="house,i:${newhouse}" th:id="'house'+${house.projectId}" th:if="${i.index &lt; 7}"  th:onclick="${'window.open(''/house/'+house.projectId+'-'+ house.type +'.htm'')'}"   >
                    <div class="img">
                        <div th:if="${house.hasLabel eq '1'}" class="instantList"><img th:src="${house.labelContent.advertisePC}" alt=""></div>
                        <a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm'}" target="_blank">
                            <img th:src="${house.pic}" th:alt="${house.projectName}"/>
<!--                            <img th:if="${house.videoTab ne '0'}" class="centerImg" src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" alt="视频"/>-->
<!--                            <img th:if="${house.pan ne null and house.pan ne ''}" th:class="${house.videoTab ne '0' ?'panoImg1':'panoImg'}" src="https://static.fangxiaoer.com/web/images/ico/sign/panoImg.png" alt="全景">-->
                            <div th:if="${house.pan ne null and house.pan ne ''}" class="vrs"><i></i><b></b></div>
                        </a>
                    </div>
                    <ul class="info">
                        <li>
                            <b><a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm'}" target="_blank"  th:text="${titleType ne '5' and house.type eq '2'?' [别墅] ':' '}+${house.projectName}"></a><i th:if="${#strings.toString(house.selectionState) eq '1'}">小二甄选</i></b>
                            <!--<a th:href="${'/house/'+house.projectId+'-'+ house.type +'/appraise.htm'}" th:if="${house.projectStatus ne '3'}" target="_blank">
                                <th:block th:if="${!#lists.isEmpty(house.mediaCount)}" th:each="media:${house.mediaCount}" th:text="${'（'+media.commentNum+'条点评）'}"></th:block>
                            </a>-->
                            <span th:class="${house.vip eq '1'}?'vip':''"></span>
                            <span th:class="${house.activityId ne null and house.activityId ne ''}?'rob':''"></span>
                        </li>
                        <li>
                            <th:block th:each="projectType,i:${projectType}" th:if="${i.index eq 3 and !projectType.selected}">
                                <th:block th:each="layout,i:${house.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block><th:block th:each="layout,i:${house.layout}" th:if="${i.index &gt; 0}" th:text="${'&nbsp; '+layout.RoomType+'居'}"></th:block>
                                <th:block th:if="${!#lists.isEmpty(house.layout)}"><img class="imgG" src="/imagese/Geng.png" alt=""></th:block>
                            </th:block>
                            <!--<th:block th:if="${#lists.isEmpty(house.area) and #lists.isEmpty(house.layout)}" th:text="${'暂无资料'}"></th:block>-->
                            <span><th:block th:each="area:${house.area}" th:text="${#numbers.formatInteger(area.minArea,1)+'-'+#numbers.formatInteger(area.maxArea,1)+'㎡'}"></th:block></span>
                            <!--1居/2居/3居 - 57~83㎡-->
                        </li>
                        <li>
<!--                            <th:block th:text="${#strings.abbreviate('['+house.regionName+']'+house.projectAddress,26)}"></th:block>-->
                                <th:block th:text="${house.regionName}"></th:block><img class="imgG" src="/imagese/Geng.png" alt=""> <th:block th:text="${house.projectAddress}"></th:block>
                        </li>
                        <li>
                            <span th:class="${#strings.equals(house.projectStatus, 1) ? 'label onsale' : #strings.equals(house.projectStatus, 2) ? 'label waitsale' : 'label sellout'}"
                                  th:text="${house.projectValue}"></span>
                            <span class="label" th:each="features,i:${#strings.setSplit(house.features,' ')}" th:if="${i.index &lt; 4}" th:text="${features}"></span>
                        </li>
                    </ul>
                    <ul th:class="${house.mPrice == null?'prices discontain':'prices'}">
                        <th:block th:if="${house.projectStatus ne '3'}">
                            <li><th:block th:if="${house.mPrice != null}" th:text="${house.mPrice.priceType}"></th:block>
                                <span th:if="${house.mPrice != null}"><b>
                            <th:block  th:text="${#numbers.formatInteger(house.mPrice.priceMoney,1) }"></th:block>
                        </b >元/㎡</span>
                                <span th:if="${house.mPrice == null}"><b class="determined">待定</b></span>
                                <ul th:if="${#lists.size(house.price) == 1 and !#lists.isEmpty(house.price)}" class="one">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) == 2 and !#lists.isEmpty(house.price)}" class="min">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) &gt; 4 and !#lists.isEmpty(house.price)}" class="max">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) &gt; 2 and #lists.size(house.price) &lt; 5 and !#lists.isEmpty(house.price)}" class="middle">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                            </li>
                        </th:block>
                        <li th:if="${house.projectStatus eq '3'}">
                            <b class="solded">新房已售完</b>
                        </li>
                        <li class="phone" th:if="${house.projectStatus ne '3'}"><i class="icon_iphone"></i><th:block th:utext="${#strings.toString(house.sortelTel).replace('转','<b> 转 </b>')}"></th:block></li>
                        <li class="phone" th:if="${house.projectStatus eq '3'}">
                            <a th:href="${'/saleHouses/search='+house.searchSub}" target="_blank">请查看二手房源></a>
                        </li>
                        <li class="db-li">
                            <!--                 <a class="collect soucang" target="_blank" data-toggle="modal" href="#login">收藏</a>
                                             <input type="hidden" id="glzs" th:value="${session.sessionId}" />-->
                            <p class="contrast" th:value="${house.projectId}" >对比</p>
                            <p class="contrastNo" style="display: none;color: red;">对比</p>
                            <input type="hidden" class="cHouseId" th:value="${house.projectId}"/>
                            <input type="hidden" class="cHouseName" th:value="${house.projectName}"/>
                            <input type="hidden" class="cHouseType" value="1"/>
                        </li>
                        <!--<li th:if="${#strings.toString(house.isOptimum) eq '1'}" class="agent-people">-->
                        <!--<a class="consult agentUrl" target="_blank" data-toggle="modal"  onclick="contact()" href="#loginAgent">-->
                        <!--<input type="hidden" class="agentId" th:value="${house.agentId}"/>-->
                        <!--<input type="hidden" class="projectId" th:value="${house.projectId}"/>-->
                        <!--<div class="agentImg"><img th:src="${house.agentPic}" alt=""></div>-->
                        <!--<br>-->
                        <!--<div class="agentName"><span><th:block th:text="${#strings.isEmpty(house.agentName)?'经纪人':house.agentName}"></th:block></span></div>-->
                        <!--<div class="cl"></div>-->
                        <!--</a>-->
                        <!--</li>-->
                    </ul>
                    <div th:if="${#strings.toString(house.isOptimum) eq '1'}" class="agent-people">
                        <a class="consult agentUrl" target="_blank" data-toggle="modal"  onclick="contact()" href="#loginAgent">
                            <input type="hidden" class="agentId" th:value="${house.agentId}"/>
                            <input type="hidden" class="projectId" th:value="${house.projectId}"/>
                            <div class="agentImg"><img th:src="${house.agentPic}" alt=""></div>

                            <div class="agentName"><span><th:block th:text="${#strings.isEmpty(house.agentName)?'经纪人':house.agentName}"></th:block></span></div>
                            <div class="cl"></div>
                        </a>
                    </div>

                    <div class="cl"></div>
                    <!--新房列表页主信息排行榜入口品牌馆入口-->
                    <div class="rank_brand_enter" th:if="${!#strings.isEmpty(house.brandId) or !#maps.isEmpty(house.rank) or !#maps.isEmpty(house.condor)}">
                        <!--品牌馆入口-->
                        <div th:if="${!#strings.isEmpty(house.brandId)}" class="brand_info">
                            <a th:href="${'/brandCompany/'+house.brandId+'.htm'}" target="_blank">
                                <i class="barnd_icon barnd_icon_2024"></i>
                                <span th:text="${house.brandName}"></span>
                            </a>
                        </div>
                        <!--品牌馆入口-->
                        <!--排行榜入口-->
                        <div class="ranks_enter" th:if="${!#maps.isEmpty(house.rank)}">
                            <a th:href="${'/projectRank/'+house.rank.rankType}" target="_blank">
                                <i class="rank_icon rank_icon_2024"></i>
<!--                                <p>-->
                                    <span  th:text="${house.rank.rankTypeName+'第'+house.rank.rankCount+'名'}"></span>

<!--                                </p>-->
                            </a>
                        </div>

                        <!--排行榜入口-->
                    <!-- 看房团-->
                        <div class="ranks_KFenter" th:if="${!#maps.isEmpty(house.condor)}">
                            <a th:href="@{'/houseKeeper2.htm'}" target="_blank">
                                <i class="rank_KFicon"></i>
                                <span th:text="${house.condor.condoTour}"></span>
                            </a>
                        </div>
                    </div>
                    <!--新房列表页主信息排行榜入口品牌馆入口-->
                </div>
                <!--house end-->
                <div th:include="fragment/fragment :: list_bottom"></div>
            </div>
            <p th:if="${!#lists.isEmpty(recommentResult)}" class="tuijian">推荐楼盘</p>

            <!--推荐楼盘开始-->
            <div th:class="${#lists.isEmpty(recommentResult)?'tuijian_wei tuijian_wei_none':'tuijian_wei'}">

                <div class="houseInfo" th:each="house,i:${newhouse}" th:id="'house'+${house.projectId}" th:if="${i.index ge 7}" th:onclick="${'window.open(''/house/'+house.projectId+'-'+ house.type +'.htm'')'}" >
                    <div class="img">
                        <div th:if="${house.hasLabel eq '1'}" class="instantList"><img th:src="${house.labelContent.advertisePC}" alt=""></div>
                        <a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm'}" target="_blank">
                            <img th:src="${house.pic}" th:alt="${house.projectName}"/>
<!--                            <img th:if="${house.videoTab ne '0'}" class="centerImg" src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" alt="视频"/>-->
<!--                            <img th:if="${house.pan ne null and house.pan ne ''}" th:class="${house.videoTab ne '0' ?'panoImg1':'panoImg'}" src="https://static.fangxiaoer.com/web/images/ico/sign/panoImg.png" alt="全景">-->
                            <div class="vrs" th:if="${house.pan ne null and house.pan ne ''}"><i></i><b></b></div>
                        </a>
                        <!--                    <span class="images">1</span>
                                            <span class="video">1</span>-->
                    </div>
                    <ul class="info">
                        <li>
                            <b><a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm'}" target="_blank"  th:text="${titleType ne '5' and house.type eq '2'?' [别墅] ':' '}+${house.projectName}"></a><i th:if="${#strings.toString(house.selectionState) eq '1'}">小二甄选</i></b>
                            <!--<a th:href="${'/house/'+house.projectId+'-'+ house.type +'/appraise.htm'}" th:if="${house.projectStatus ne '3'}" target="_blank">
                                <th:block th:if="${!#lists.isEmpty(house.mediaCount)}" th:each="media:${house.mediaCount}" th:text="${'（'+media.commentNum+'条点评）'}"></th:block>
                            </a>-->
                            <span th:class="${house.vip eq '1'}?'vip':''"></span>
                            <span th:class="${house.activityId ne null and house.activityId ne ''}?'rob':''"></span>
                        </li>
                        <li>
                            <th:block th:each="projectType,i:${projectType}" th:if="${i.index eq 3 and !projectType.selected}">
                                <th:block th:each="layout,i:${house.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block><th:block th:each="layout,i:${house.layout}" th:if="${i.index &gt; 0}" th:text="${'&nbsp; '+layout.RoomType+'居'}"></th:block>
                                <th:block th:if="${!#lists.isEmpty(house.layout)}"><img class="imgG" src="/imagese/Geng.png" alt=""></th:block>
                            </th:block>
                            <!--<th:block th:if="${#lists.isEmpty(house.area) and #lists.isEmpty(house.layout)}" th:text="${'暂无资料'}"></th:block>-->
                            <th:block th:each="area:${house.area}" th:text="${#numbers.formatInteger(area.minArea,1)+'-'+#numbers.formatInteger(area.maxArea,1)+'㎡'}"></th:block>
                            <!--1居/2居/3居 - 57~83㎡-->
                        </li>
                        <li>
<!--                            <th:block th:text="${#strings.abbreviate('['+house.regionName+']'+house.projectAddress,34)}"></th:block>-->
                            <th:block th:text="${house.regionName}"></th:block><img class="imgG" src="/imagese/Geng.png" alt=""> <th:block th:text="${house.projectAddress}"></th:block>

                        </li>

                        <li>
                            <span th:class="${#strings.equals(house.projectStatus, 1) ? 'label onsale' : #strings.equals(house.projectStatus, 2) ? 'label waitsale' : 'label sellout'}"
                                  th:text="${house.projectValue}"></span>
                            <span class="label" th:each="features,i:${#strings.setSplit(house.features,' ')}" th:if="${i.index &lt; 4}" th:text="${features}"></span>
                        </li>
                    </ul>
                    <ul th:class="${house.mPrice == null?'prices discontain':'prices'}">
                        <th:block th:if="${house.projectStatus ne '3'}">
                            <li><th:block th:if="${house.mPrice != null}" th:text="${house.mPrice.priceType}"></th:block>
                                <span th:if="${house.mPrice != null}"><b>
                            <th:block  th:text="${#numbers.formatInteger(house.mPrice.priceMoney,1) }"></th:block>
                        </b >元/㎡</span>
                                <span th:if="${house.mPrice == null}"><b class="determined">待定</b></span>
                                <ul th:if="${#lists.size(house.price) == 1 and !#lists.isEmpty(house.price)}" class="one">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) == 2 and !#lists.isEmpty(house.price)}" class="min">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) &gt; 4 and !#lists.isEmpty(house.price)}" class="max">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) &gt; 2 and #lists.size(house.price) &lt; 5 and !#lists.isEmpty(house.price)}" class="middle">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                            </li>
                        </th:block>
                        <li th:if="${house.projectStatus eq '3'}">
                            <b class="solded">新房已售完</b>
                        </li>
                        <li class="phone" th:if="${house.projectStatus ne '3'}"><i class="icon_iphone"></i><th:block th:utext="${#strings.toString(house.sortelTel).replace('转','<b> 转 </b>')}"></th:block></li>
                        <li class="phone" th:if="${house.projectStatus eq '3'}">
                            <a th:href="${'/saleHouses/search='+house.searchSub}" target="_blank">请查看二手房源></a>
                        </li>
                        <li class="db-li">
                            <!--                 <a class="collect soucang" target="_blank" data-toggle="modal" href="#login">收藏</a>
                                             <input type="hidden" id="glzs" th:value="${session.sessionId}" />-->
                            <p class="contrast" th:value="${house.projectId}" >对比</p>
                            <p class="contrastNo" style="display: none;color: red;">对比</p>
                            <input type="hidden" class="cHouseId" th:value="${house.projectId}"/>
                            <input type="hidden" class="cHouseName" th:value="${house.projectName}"/>
                            <input type="hidden" class="cHouseType" value="1"/>
                        </li>
                        <!--<li th:if="${#strings.toString(house.isOptimum) eq '1'}" class="agent-people">-->
                        <!--<a class="consult agentUrl" target="_blank" data-toggle="modal"  onclick="contact()" href="#loginAgent">-->
                        <!--<input type="hidden" class="agentId" th:value="${house.agentId}"/>-->
                        <!--<input type="hidden" class="projectId" th:value="${house.projectId}"/>-->
                        <!--<div class="agentImg"><img th:src="${house.agentPic}" alt=""></div>-->
                        <!--<br>-->
                        <!--<div class="agentName"><span><th:block th:text="${#strings.isEmpty(house.agentName)?'经纪人':house.agentName}"></th:block></span></div>-->
                        <!--<div class="cl"></div>-->
                        <!--</a>-->
                        <!--</li>-->
                    </ul>
                    <div th:if="${#strings.toString(house.isOptimum) eq '1'}" class="agent-people">
                        <a class="consult agentUrl" target="_blank" data-toggle="modal"  onclick="contact()" href="#loginAgent">
                            <input type="hidden" class="agentId" th:value="${house.agentId}"/>
                            <input type="hidden" class="projectId" th:value="${house.projectId}"/>
                            <div class="agentImg"><img th:src="${house.agentPic}" alt=""></div>

                            <div class="agentName"><span><th:block th:text="${#strings.isEmpty(house.agentName)?'经纪人':house.agentName}"></th:block></span></div>
                            <div class="cl"></div>
                        </a>
                    </div>

                    <div class="cl"></div>
                    <div class="rank_brand_enter" th:if="${!#strings.isEmpty(house.brandId) or !#maps.isEmpty(house.rank) or !#maps.isEmpty(house.condor)}">
                        <!--品牌馆入口-->
                        <div th:if="${!#strings.isEmpty(house.brandId)}" class="brand_info">
                            <a th:href="${'/brandCompany/'+house.brandId+'.htm'}" target="_blank">
                                <i class="barnd_icon"></i>
                                <span th:text="${house.brandName}"></span>
                            </a>
                        </div>
                        <!--品牌馆入口-->
                        <!--排行榜入口-->
                        <div class="ranks_enter" th:if="${!#maps.isEmpty(house.rank)}">
                            <a th:href="${'/projectRank/'+house.rank.rankType}" target="_blank">
                                <i class="rank_icon"></i>
                                    <span th:text="${house.rank.rankTypeName+'第'+house.rank.rankCount+'名'}"></span>
                            </a>
                        </div>
                        <!--排行榜入口-->
                        <!-- 看房团-->
                        <div class="ranks_KFenter" th:if="${!#maps.isEmpty(house.condor)}">
                            <a th:href="@{'/houseKeeper2.htm'}" target="_blank">
                                <i class="rank_KFicon"></i>
                                <span th:text="${house.condor.condoTour}"></span>
                            </a>
                        </div>
                    </div>
                    </div>
                </div>

                <div th:if="${!#lists.isEmpty(recommentResult)}" class="houseInfo" th:each="house,i:${recommentResult}" th:id="'house'+${house.projectId}" th:onclick="${'window.open(''/house/'+house.projectId+'-'+ house.type +'.htm'')'}"  >

                    <div class="img">
                        <div th:if="${house.hasLabel eq '1'}" class="instantList"><img th:src="${house.labelContent.advertisePC}" alt=""></div>
                        <a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm'}" target="_blank">
                            <img th:src="${house.pic}" th:alt="${house.projectName}"/>
<!--                            <img th:if="${house.videoTab ne '0'}" class="centerImg" src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" alt="视频"/>-->
<!--                            <img th:if="${house.pan ne null and house.pan ne ''}" th:class="${house.videoTab ne '0' ?'panoImg1':'panoImg'}" src="https://static.fangxiaoer.com/web/images/ico/sign/panoImg.png" alt="全景">-->
                            <div class="vrs" th:if="${house.pan ne null and house.pan ne ''}"><i></i><b></b></div>
                        </a>
                        <!--                    <span class="images">1</span>
                                            <span class="video">1</span>-->
                    </div>
                    <ul class="info">
                        <li>
                            <b><a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm'}" target="_blank"  th:text="${titleType ne '5' and house.type eq '2'?' [别墅] ':' '}+${house.projectName}"></a><i th:if="${#strings.toString(house.selectionState) eq '1'}">小二甄选</i></b>
                            <!--<a th:href="${'/house/'+house.projectId+'-'+ house.type +'/appraise.htm'}" th:if="${house.projectStatus ne '3'}" target="_blank">
                                <th:block th:if="${!#lists.isEmpty(house.mediaCount)}" th:each="media:${house.mediaCount}" th:text="${'（'+media.commentNum+'条点评）'}"></th:block>
                            </a>-->
                            <span th:class="${house.vip eq '1'}?'vip':''"></span>
                            <span th:class="${house.activityId ne null and house.activityId ne ''}?'rob':''"></span>
                        </li>
                        <li>
                            <th:block th:each="projectType,i:${projectType}" th:if="${i.index eq 3 and !projectType.selected}">
                                <th:block th:each="layout,i:${house.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block><th:block th:each="layout,i:${house.layout}" th:if="${i.index &gt; 0}" th:text="${' &nbsp;'+layout.RoomType+'居'}"></th:block>
                                <th:block th:if="${!#lists.isEmpty(house.layout)}"><img src="/imagese/Geng.png" alt="" class="imgG"></th:block>
                            </th:block>
                            <th:block th:if="${#lists.isEmpty(house.area) and #lists.isEmpty(house.layout)}" th:text="${'暂无资料'}"></th:block>
                            <th:block th:each="area:${house.area}" th:text="${#numbers.formatInteger(area.minArea,1)+'-'+#numbers.formatInteger(area.maxArea,1)+'㎡'}"></th:block>
                            <!--1居/2居/3居 - 57~83㎡-->
                        </li>
                        <li>
<!--                            <th:block th:text="${#strings.abbreviate('['+house.regionName+']'+house.projectAddress,34)}"></th:block>-->
                            <th:block th:text="${house.regionName}"></th:block><img class="imgG" src="/imagese/Geng.png" alt=""> <th:block th:text="${house.projectAddress}"></th:block>

                        </li>
                        <li>
                            <span th:class="${#strings.equals(house.projectStatus, 1) ? 'label onsale' : #strings.equals(house.projectStatus, 2) ? 'label waitsale' : 'label sellout'}"
                                  th:text="${house.projectValue}"></span>
                            <span class="label" th:each="features,i:${#strings.setSplit(house.features,' ')}" th:if="${i.index &lt; 4}" th:text="${features}"></span>
                        </li>
                    </ul>
                    <ul th:class="${house.mPrice == null?'prices discontain':'prices'}">
                        <th:block th:if="${house.projectStatus ne '3'}">
                            <li><th:block th:if="${house.mPrice != null}" th:text="${house.mPrice.priceType}"></th:block>
                                <span th:if="${house.mPrice != null}"><b>
                            <th:block  th:text="${#numbers.formatInteger(house.mPrice.priceMoney,1) }"></th:block>
                        </b >元/㎡</span>
                                <span th:if="${house.mPrice == null}"><b class="determined">待定</b></span>
                                <ul th:if="${#lists.size(house.price) == 1 and !#lists.isEmpty(house.price)}" class="one">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) == 2 and !#lists.isEmpty(house.price)}" class="min">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) &gt; 4 and !#lists.isEmpty(house.price)}" class="max">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                                <ul th:if="${#lists.size(house.price) &gt; 2 and #lists.size(house.price) &lt; 5 and !#lists.isEmpty(house.price)}" class="middle">
                                    <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                        <th:block th:text="${price.name+price.showPrice}"></th:block>
                                    </li>
                                    <i></i>
                                </ul>
                            </li>
                        </th:block>
                        <li th:if="${house.projectStatus eq '3'}">
                            <b class="solded">新房已售完</b>
                        </li>
                        <li class="phone" th:if="${house.projectStatus ne '3'}"><i class="icon_iphone"></i><th:block th:utext="${#strings.toString(house.sortelTel).replace('转','<b> 转 </b>')}"></th:block></li>
                        <li class="phone" th:if="${house.projectStatus eq '3'}">
                            <a th:href="${'/saleHouses/search='+house.searchSub}" target="_blank">请查看二手房源></a>
                        </li>
                        <li class="db-li">
                            <!--                 <a class="collect soucang" target="_blank" data-toggle="modal" href="#login">收藏</a>
                                             <input type="hidden" id="glzs" th:value="${session.sessionId}" />-->
                            <p class="contrast" th:value="${house.projectId}" >对比</p>
                            <p class="contrastNo" style="display: none;color: red;">对比</p>
                            <input type="hidden" class="cHouseId" th:value="${house.projectId}"/>
                            <input type="hidden" class="cHouseName" th:value="${house.projectName}"/>
                            <input type="hidden" class="cHouseType" value="1"/>
                        </li>
                        <!--<li th:if="${#strings.toString(house.isOptimum) eq '1'}" class="agent-people">-->
                        <!--<a class="consult agentUrl" target="_blank" data-toggle="modal"  onclick="contact()" href="#loginAgent">-->
                        <!--<input type="hidden" class="agentId" th:value="${house.agentId}"/>-->
                        <!--<input type="hidden" class="projectId" th:value="${house.projectId}"/>-->
                        <!--<div class="agentImg"><img th:src="${house.agentPic}" alt=""></div>-->
                        <!--<br>-->
                        <!--<div class="agentName"><span><th:block th:text="${#strings.isEmpty(house.agentName)?'经纪人':house.agentName}"></th:block></span></div>-->
                        <!--<div class="cl"></div>-->
                        <!--</a>-->
                        <!--</li>-->
                    </ul>
                    <div th:if="${#strings.toString(house.isOptimum) eq '1'}" class="agent-people">
                        <a class="consult agentUrl" target="_blank" data-toggle="modal"  onclick="contact()" href="#loginAgent">
                            <input type="hidden" class="agentId" th:value="${house.agentId}"/>
                            <input type="hidden" class="projectId" th:value="${house.projectId}"/>
                            <div class="agentImg"><img th:src="${house.agentPic}" alt=""></div>

                            <div class="agentName"><span><th:block th:text="${#strings.isEmpty(house.agentName)?'经纪人':house.agentName}"></th:block></span></div>
                            <div class="cl"></div>
                        </a>
                    </div>

                    <div class="cl"></div>
                    <div class="rank_brand_enter" th:if="${!#strings.isEmpty(house.brandId) or !#maps.isEmpty(house.rank) or !#maps.isEmpty(house.condor)}">
                        <!--品牌馆入口-->
                        <div th:if="${!#strings.isEmpty(house.brandId)}" class="brand_info">
                            <a th:href="${'/brandCompany/'+house.brandId+'.htm'}" target="_blank">
                                <i class="barnd_icon"></i>
                                <span th:text="${house.brandName}"></span>
                            </a>
                        </div>
                        <!--品牌馆入口-->
                        <!--排行榜入口-->
                        <div class="ranks_enter" th:if="${!#maps.isEmpty(house.rank)}">
                            <a th:href="${'/projectRank/'+house.rank.rankType}" target="_blank">
                                <i class="rank_icon"></i>
<!--                                <p>-->
                                    <span th:text="${house.rank.rankTypeName+'第'+house.rank.rankCount+'名'}"></span>
<!--                                </p>-->
                            </a>
                        </div>
                        <!--排行榜入口-->
                        <!--看房团-->
                        <div class="ranks_KFenter" th:if="${!#maps.isEmpty(house.condor)}">
                            <a th:href="@{'/houseKeeper2.htm'}" target="_blank">
                                <i class="rank_KFicon"></i>
                                <span th:text="${house.condor.condoTour}"></span>
                            </a>
                        </div>
                    </div>
                    </div>
                </div>
        <div id="right">
<!--            <div th:include="fragment/fragment :: right_order"></div>-->
            <div th:include="fragment/fragment :: right_other"></div>

            <div>
                <div class="rightQzQgBtn">
                    <a th:href="@{'/helpSearch?ids=1'}" rel="2" target="_blank">我要买房</a>
                    <a href="/static/saleHouse/saleHouse.htm" target="_blank"><i class="rightQzQgBtn-icon1"></i>我要卖房</a>
                </div>
            </div>

            <div class="good" th:if="${decorationType!=1 and !#lists.isEmpty(recommendAdProjects)}" style="margin-bottom: 24px;">
                <h1><span></span>好房推荐</h1>
                <div class="content">
                    <ul>
                        <li th:each="projectAd:${recommendAdProjects}">
                            <a th:href="${projectAd.TargetUrl}" target="_blank">
                                <div class="pic">
                                    <div class="photo">
                                        <img th:src="${projectAd.AdFilePath}">
                                    </div>
                                    <div class="name">
                                        <h4 th:text="${#strings.isEmpty(projectAd.AdTitle)?'':projectAd.AdTitle}">首创光和城</h4>
                                    </div>
                                </div>
                                <div class="text">
                                    <div class="top_sun">
                                        <div class="left_sun">
                                            <th:block th:if="${#strings.toString(projectAd.Adproprice) eq '0'}">
                                                <h4><span>待定</span></h4>
                                            </th:block>
                                            <th:block th:unless="${#strings.toString(projectAd.Adproprice) eq '0'}">
                                                <h4><th:block th:text="${#strings.toString(projectAd.Number) eq '2'?'均价：':'起价：'}"></th:block><span th:text="${#strings.isEmpty(projectAd.Adproprice)?'':projectAd.Adproprice}">8600</span>元/㎡</h4>
                                            </th:block>
                                        </div>
                                        <div class="right_sun">
                                            <h4 th:text="${#strings.isEmpty(projectAd.Propertys)?'':projectAd.Propertys}">于洪区</h4>
                                        </div>
                                        <div class="cl"></div>
                                    </div>
                                    <div class="bottom_sun">
                                        <h4 th:text="${#strings.isEmpty(projectAd.AdPhrase)?'':projectAd.AdPhrase}">以租还贷 生态宜居</h4>
                                    </div>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!--最新视频-->
            <div class="video_box" style=" margin-bottom: 24px;">
                <div class="vtop"><span></span>最新视频<i onclick="window.open('/videos')">更多>></i></div>
                <dl class="recommend" style="border:none" th:each="v:${video}">
<!--                    <div class="npy"></div>-->
                    <a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank"><i></i>
                        <img th:src="${v.videoPic}" th:alt="${v.videoTitle}"/></a>
                    <p><a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank" th:text="${v.videoTitle}"></a></p>
                </dl>
            </div>
            <!-- 小二精选楼盘 -->
            <dl class="recommend" style="margin-top: 0;">
                <dt><span></span>小二精选</dt>
                <div class="hot">
                    <div style=""><!--padding: 18px 15px; box-sizing: border-box;-->

                        <div class="childlist">
                            <div class="childLtab">
                                <span class="chitabN">排名</span>
                                <span class="chitabL">楼盘名称</span>
                                <span class="chitabJ">价格</span>
                                <span class="chitabQ">区域</span>
                            </div>
                            <ul>
                                <li class="chilistLi pl0" th:each="project, i : ${rankList}" th:if="${i.index lt 10}">
                                    <div>
                                        <div th:class="${i.count le 3 ? 'chiListRank spcolor' : 'chiListRank'}" th:text="${i.count}"></div><!--前三个 加spcolor-->
                                        <div class="crankList" th:style="${i.count eq 1 ? 'display:none':'display:block'}">
                                            <ul>
                                                <li class="pl0" th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></li>
                                                <li class="pl0 DDlist" th:if="${project.mPrice eq null}">待定</li>
                                                <li class="pl0" th:if="${project.mPrice ne null}"><span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡</li>
                                                <li class="p00" th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}">沈北新区</li>
                                            </ul>
                                        </div>
                                        <a th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}" target="_blank">
                                            <div class="crankDetails" th:style="${i.count eq 1 ? 'display:block':'display:none'}">
                                                <div class="crankDetailsdiv" style="position: relative">
                                                    <img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
                                                    <div class="crankDetailsdivR">
                                                        <h2 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></h2>
                                                        <p class="crankDetailsP" th:if="${project.mPrice eq null}" >待定</p>
                                                        <p class="crankDetailsP" th:if="${project.mPrice ne null}">
                                                            <span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡
                                                            <span class="Ljun" th:text="${project.mPrice.priceType eq '起价'? '起' :'均'}"></span></p>
                                                        <p class="crankDetailsD">
                                                            <span th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}"></span>
                                                            <span class="lGeng" th:if="${!#maps.isEmpty(project.area)}"
                                                                  th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))
                                                          + '-' + #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}"></span>
                                                        </p>
                                                    </div>
                                                    <div class="vru" th:if="${project.pan ne null}"></div>
                                                </div>
                                                <div class="crankDetailsreason">
                                                    推荐理由：<span th:text="${project.rankDesc}"></span>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </li>
                            </ul>
                        </div>

                    </div>
                </div>
            </dl>



            <div class="cl"></div>
        </div>
    </div>

            <!--<script th:inline="javascript">-->
            <!--$(document).ready(function () {-->
            <!--var sta = [[${sta}]];-->
            <!--document.getElementById("EleId").style.display="none";-->
            <!--if (sta == 1) {-->
            <!--document.getElementById("EleId").style.display="inline";-->
            <!--}-->
            <!--});-->
            <!--</script>-->
            <script type="text/javascript">

                Array.prototype.forEach.call( document.getElementsByClassName("features"), function (t) {
                    if($(t).find(".tar_right").length > 1) {
                        $(t).find(".tar_right").hide();
                    }
                });
                $(".targer").mouseover(function () {
                    $(this.parentNode).find(".tar_right").hide();
                    $(this).find(".tar_right").show();
                })
            </script>
            <div class="cl"></div>
            <div class="page">
                <div th:include="fragment/page :: page"></div>
            </div>
            <div class="cl"></div>
        </div>
        <!--left end-->


    </div>
    <div th:include="house/detail/fragment_contactAgent::contact"></div>
    <div th:include="house/detail/fragment_contactAgent::loginAgent"></div>

<!--    <div th:include="fragment/fragment::commonFloat" th:with="commonType=1"></div>-->
<div th:include="fragment/fragment::newHouseFloat" th:with="commonType=1"></div>
    <div th:include="fragment/fragment:: footer_seo"></div>
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <!--<script src="/js/house/houseMap.js"></script>-->
</div>

<script>



</script>
<div class="yydk_heibu" style="display: none">
    <div class="yyDk" style="display: none">
        <div>
            <div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"/></div>
            <p>预约成功</p>
            <p>咨询师将尽快与您联系，免费为您提供专业服务</p>
        </div>
        <div class="yyDk_close"><img src="https://static.fangxiaoer.com/web/images/ico/sign/kpTc_07.png" /></div>

    </div>
</div>
<script type="text/javascript">
    $("a:not([data-toggle]),.contrast,.contrastNo").click(function() {

        event.stopPropagation();

    });
    $(".contrast").click(function(){
        window.location.href = "https://sy.fangxiaoer.com/gotoContrastHouse";
        // window.location.href = "http://************:8082/gotoContrastHouse";

    });
    $(".contrastNo").click(function(){
        window.location.href = "https://sy.fangxiaoer.com/gotoContrastHouse";
        // window.location.href = "http://************:8082/gotoContrastHouse";

    });
        //		一键订阅弹出框弹出隐藏
    sy_confirm.init(1,true);
    $(".yydk_heibu").hide();
    $(".yyDk").hide();
    $(".ljyyBtn").click(function(){
        if(sy_confirm.phone($("#houseKeeperPhone").val()) != true){
            alert(sy_confirm.phone($("#houseKeeperPhone").val()));
        }else {
            var params = {
                type:9,
                phone: $("#houseKeeperPhone").val(),
                area:"新房列表订单",
                region:'免费专车',
                budget:'免费专车',
                italy:'【Sy站】'
            }
            $.ajax({
                type: "post",
                url: "/saveHouseOrder",
                data: JSON.stringify(params),
                headers : {
                    'Content-Type' : 'application/json;charset=utf-8'
                },
                success: function(data) {
                    if (data.status == 1) {
                        fxe_alert("提交成功！")
                        $(".yydk_heibu").show();
                        $(".yyDk").show();
                    } else if (data.status == 0) {
                        fxe_alert(data.msg);
                    } else {
                        fxe_alert("提交失败！")
                    }
                },
                error: function(data) {
                    console.log(data)
                }
            });

        }
    })
    $(".yyDk_close").click(function(){
        $(".yydk_heibu").hide();
        $(".yyDk").hide();
        $("#houseKeeperPhone").attr("value","");
    })

    /*小二精选*/
    $(".crankList").hover(function() {
        // $(".crankList").show();
        $(this).parent().parent().parent().find(".crankList").show();
        $(this).hide();
        $(this).parent().parent().parent().find(".crankDetails").hide();
        $(this).parent().find(".crankDetails").show();
    })
</script>
</body>
</html>
