<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>戶型对比</title>
    <link href="//static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="//static.fangxiaoer.com/web/styles/main2017.css?v=20200430" />
    <link rel="stylesheet" type="text/css" href="//static.fangxiaoer.com/web/styles/new_sy/house/newHouse-Contrast.css?t=20180502" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="//static.fangxiaoer.com/js/angular.min.js"></script>

</head>
<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>
<input th:if="${#session?.getAttribute('sessionId') != null}" id="fxe_sessionId" type="hidden" th:value="${#session?.getAttribute('sessionId')}">
<div class="mian-kuang" ng-app="myApp"   ng-controller="scriptorium">
    <div class="db-left">
        <ul>
            <p class="db-title">户型对比</p>
            <li><a href="#houseName">户型概况</a></li>
            <li class="db-title2"><i class="left-icon"></i><a href="#housePrice">户型信息</a></li>
            <li><a href="#housePrice1">户型</a></li>
            <li><a href="#housePrice2">面积</a></li>
            <li><a href="#housePrice3">类型</a></li>
            <li><a href="#housePrice4">户型状态</a></li>
            <li class="db-title2"><i class="left-icon"></i><a href="#houseType">名称</a></li>
            <li><a href="#houseName1">楼盘名称</a></li>
            <li class="db-title2"><i class="left-icon"></i><a href="#houseInfo">基础信息</a></li>
            <li><a href="#houseInfo1">项目地址</a></li>
            <li><a href="#houseInfo2">项目特色</a></li>
            <li><a href="#houseInfo3">项目状态</a></li>
            <li><a href="#houseInfo6">建筑类型</a></li>
            <li><a href="#houseInfo7">装修状态</a></li>
            <li><a href="#houseInfo9">公摊</a></li>
            <li><a href="#houseInfo10">容积率</a></li>
            <li><a href="#houseInfo11">绿化率</a></li>
            <li><a href="#houseInfo8">物业费</a></li>
            <li class="db-title2"><i class="left-icon"></i><a href="#developer">开发商与物业公司</a></li>
            <li><a href="#developer1">开发商</a></li>
            <li><a href="#developer2">物业公司</a></li>
        </ul>
    </div>
    <div class="db-right">
        <div class="crumbs"><a href="/">房小二网</a> &gt; <a href="/houses/">沈阳新房</a> &gt;<span style="color: #999;">户型对比</span></div>
        <div class="db-content1">
            <p class="hxdb-p">查看该户型的用户还对比了如下户型</p>
            <ul class="hxdb">
                <li ng-repeat="houseList in houseList">
                    <div>
                        <a  ng-href="{{'/house/'+houseList.ProjectID+'-'+houseList.projectType+'/layout/pid'+houseList.ProjectID+'-pt'+houseList.projectType+'-l'+houseList.layId+''}}" target="_blank" class="houseListImgA">
                            <img ng-src="{{houseList.imageUrl}}" alt="" class="houseListImg">
                        </a>
                    </div>
                    <p class="houseListTitle">{{houseList.title}}</p>
                    <b class="houseListPrise">参考价：<i>{{(houseList.price*houseList.trueArea/10000| number:2) == 0.00 ? '待定' : (houseList.price*houseList.trueArea/10000| number:2 )+ '万'}}</i></b>
                    <a href="" ng-rel="{{houseList.regionID}}" ng-click="addHouse(houseList.layId)"><i>＋</i>对比</a>
                </li>
            </ul>
        </div>
        <div class="nFix">
            <!--            <span>户型对比</span>-->
            <ul>
                <li style="width: 103px;font-weight: normal">户型对比</li>
                <li ng-repeat="(key,house) in house" ng-attr-title="{{house.content.projectName}}">{{house.content.buildArea}}{{house.content.roomType}}{{house.content.hallType}}{{house.content.guardType}}</li>
            </ul>
        </div>
        <div class="db-content2">
            <table class="db-table"  cellpadding="0px"  cellspacing="0px" >
                <tr ><td class="db-title3" colspan="5"><i class="left-icon"></i>户型对比</td></tr>

                <tr class="db-lp" id="houseName">
                    <td>户型概况</td>
                    <td ng-repeat="(key,house) in house">
                        <div data-rel="{{house.content.layId}}" class="nameDiv">
                            <a ng-href="{{'/house/'+house.content.ProjectID+'-'+house.content.type+'/layout/pid'+house.content.ProjectID+'-pt'+house.content.type+'-l'+house.content.layId+''}}" target="_blank">
                                <img ng-src="{{house.content.imageUrl}}" ng-if="house.content.imageUrl!=undefined"/>
                                <p class="newRoomT">{{house.content.buildArea}}{{house.content.roomType}}{{house.content.hallType}}{{house.content.guardType}}</p>
                                <b class="newRoomP" ng-attr-title="{{house.content.referencePrice}}">{{house.content.referencePrice}}</b>
                            </a>
                            <div ng-if="house.content.projectName!=undefined" class="martop">
                                <a th:if="${#session?.getAttribute('sessionId') == null}" data-toggle="modal"  href="#login"><div class="db-iconx"></div></a>
                                <div th:if="${#session?.getAttribute('sessionId') != null}" class="db-iconx" ng-class="{ '1':'hover'}[house.content.favorite]" ng-click="cancelFavorite(house.content.layId)"></div>
                                <div class="db-iconc" ng-click="showHouseInfo(key)"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr id="housePrice" class="navde"></tr>

                <tr><td class="db-title3" colspan="5"><i class="left-icon"></i>户型信息</td></tr>
                <tr id="housePrice1" class="navde"></tr>

                <tr>
                    <td>户型</td>
                    <td ng-repeat="house in house"><p>{{house.content.roomType}}{{house.content.hallType}}{{house.content.guardType}}</p></td>
                </tr>
                <tr id="housePrice2" class="navde"></tr>
                <tr >
                    <td>面积</td>
                    <td ng-repeat="house in house"><p>{{house.content.buildArea}}</p></td>
                </tr>
                <tr id="housePrice3" class="navde"></tr>
                <tr>
                    <td>类型</td>
                    <td ng-repeat="house in house"><p>{{house.content.houseTypeStr}}</p></td>
                </tr>
                <tr id="housePrice4" class="navde"></tr>
                <tr >
                    <td>户型状态</td>
                    <td ng-repeat="house in house"><p>{{house.content.stateValue}}</p></td>
                </tr>
                <tr id="houseInfo" class="navde"></tr>
                <tr ><td class="db-title3" colspan="5"><i class="left-icon"></i>名称</td></tr>
                <tr id="houseName1" class="navde"></tr>

                <tr >
                    <td>楼盘名称</td>
                    <td ng-repeat="house in house" class="naddress"><p>{{house.content.projectName}}</p></td>
                </tr>
                <tr ><td class="db-title3" colspan="5"><i class="left-icon"></i>基础信息</td></tr>
                <tr id="houseInfo1" class="navde"></tr>

                <tr>
                    <td>项目地址</td>
                    <td ng-repeat="house in house" class="naddress"><p>{{house.content.projectAddress}}</p></td>
                </tr>
                <tr id="houseInfo2" class="navde"></tr>

                <tr class="db-ts" >
                    <td>项目特色</td>
                    <td ng-repeat="house in house"><span ng-repeat="features in house.content.features.split(' ')">{{features}}</span></td>
                </tr>
                <tr id="houseInfo3" class="navde"></tr>
                <tr>
                    <td>项目状态</td>
                    <td ng-repeat="house in house"><p>{{house.content.projectState}}</p></td>
                </tr>
                <tr id="houseInfo6" class="navde"></tr>

                <tr>
                    <td>建筑类型</td>
                    <td ng-repeat="house in house"><p>{{house.content.buildType}}</p></td>
                </tr>
                <tr id="houseInfo7" class="navde"></tr>

                <tr >
                    <td>交付标准</td>
                    <td ng-repeat="house in house"><p>{{house.content.resDecoration}}</p></p></td>
                </tr>
                <tr id="houseInfo9" class="navde"></tr>

                <tr >
                    <td>公摊</td>
                    <td ng-repeat="house in house"><p>{{house.content.layId != null && (house.content.pooled1 == null || house.content.pooled1 == 0 || house.content.pooled1 == '') ? '——':house.content.pooled1}}</p></td>
                </tr>
                <tr id="houseInfo10" class="navde"></tr>

                <tr>
                    <td>容积率</td>
                    <td ng-repeat="house in house"><p>{{house.content.platRatio}}</p></td>
                </tr>
                <tr id="houseInfo11" class="navde"></tr>

                <tr >
                    <td>绿化率</td>
                    <td ng-repeat="house in house"><p>{{house.content.greenery != null && (house.content.greenery == null || house.content.greenery == 0 || house.content.greenery == '') ? '——':house.content.greenery}}</p></td>
                </tr>
                <tr id="houseInfo8" class="navde"></tr>

                <tr >
                    <td>物业费</td>
                    <td ng-repeat="house in house"><p>{{house.content.PropertyFee}}</p></td>
                </tr>
                <tr id="houseType" class="navde"></tr>


                <tr id="developer"><td class="db-title3" colspan="5"><i class="left-icon"></i>开发商与物业公司</td></tr>
                <tr id="developer1" class="navde"></tr>
                <tr>
                    <td>开发商</td>
                    <td ng-repeat="house in house"><p>{{house.content.developerName}}</p></td>
                </tr>
                <tr id="developer2" class="navde"></tr>
                <tr >
                    <td>物业公司</td>
                    <td ng-repeat="house in house"><p>{{house.content.companyWYGS}}</p></td>
                </tr>
            </table>
        </div>
    </div>
</div>
<div class="cl"></div>
<div th:include="fragment/fragment::commonFloat"></div>
<div th:include="fragment/fragment:: footer_seo"></div>
<div th:include="fragment/fragment:: footer_list"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="house/detail/fragment_login::login"></div>
<script>
    var nameDivNum = "";
    var app = angular.module('myApp', []);
    app.controller('scriptorium', function($scope, $rootScope, $http,$timeout) {
        $scope.house = [{
            layId: '',
            content: {},
            favorite: ''
        }, {
            layId: '',
            content: {},
            favorite: ''
        }, {
            layId: '',
            content: {},
            favorite: ''
        }, {
            layId: '',
            content: {},
            favorite: ''
        }];
        if($("#fxe_sessionId").length > 0) {
            //元素存在时执行的代码
            $scope.sessionId=document.getElementById("fxe_sessionId").value;
        }

        var openUrl = getCookie("arrayLayId")
        txt = openUrl.split(',');
        if (txt != "") {
            for (var t = 0; t < txt.length; t++) {
                $scope.house[t].layId = txt[t];
            }
        }
        $scope.houseList = [];

        $scope.showHouseInfo = function (key) {
            if (key < 4) {//删除所选房源
                if ($scope.house[1].layId == '') {
                    alert("至少保留一个户型");
                    return
                } else {//删除户型=删除cookie同时刷新页面
                   /* $scope.house[key].layId = '';
                    $scope.house[key].content = {};*/
                    var openUrl = getCookie("arrayLayId")
                    txt = openUrl.split(',');
//                    function remove(txt,key) {
                        var result = "";
                        for (var i = 0 ; i < txt.length ; i ++){
                            if (i != key){
                                result = result == '' ? txt[i] : result + "," +txt[i]
                            }
                        }
//                        return result;
//                    }
                    setCookie("arrayLayId", result);
                    window.location.reload();

                }
                //保证每个房源紧挨着不会出现空一列的情况
                for (var k = 0; k < 3; k++) {
                    if ($scope.house[k].layId == "") {
                        for (var y = k + 1; y < 4; y++) {
                            if ($scope.house[y].layId != "") {
                                $scope.house[k].layId = $scope.house[y].layId;
                                $scope.house[k].content = $scope.house[y].content;
                                $scope.house[y].layId = "";
                                $scope.house[y].content = {};
                                break;
                            }
                        }
                    }
                }
                var nameDivNum2 = $(".nameDiv").eq(1).attr("data-rel")
                txt[0] = nameDivNum2
                console.log(nameDivNum2)
                $scope.showHouseList(nameDivNum2)
            }
            $scope.showHouseList()
            for (var i = 0; i < 4; i++) {
                if ($scope.house[i].layId != "") {
                    $http({
                        method: 'POST',
                        url: '/doContrastLayout',
                        data: "layId=" + $scope.house[i].layId,  // pass in data as strings
                        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
                    }).then(function successCallback(response) {
                        for (var j = 0; j < 4; j++) {
                            if (response.data.content.layId == $scope.house[j].layId) {
                                $scope.house[j].content = response.data.content;
                                if($scope.sessionId!=""){
                                    $scope.favorite($scope.house[j].layId)
                                }
                                if($scope.house[j].content.PropertyFee<=0){

                                    $scope.house[j].content.PropertyFee="暂无资料";
                                }
                            }
                        }
                    }, function errorCallback(response) {

                    });
                }
            }
        }

        $scope.favorite = function (layId) {//管理我的收藏
            var content=false;
            $http({
                method: 'POST',
                url: '/manageCollect',
                data: "layoutId=" +layId + "&&sessionId=" + $scope.sessionId + "&&method=checkFavorite",  // pass in data as strings
                headers: {'Content-Type': 'application/x-www-form-urlencoded'}
            }).then(function successCallback(data) {
                if (data.content == 1) {
                    content=true;
                    for(var d=0;d<4;d++){
                        if(layId==$scope.house[d].layId){
                            $scope.house[d].content.favorite=1;
                        }
                    }
                }
            }, function errorCallback(data) {
            });
        }


        $scope.cancelFavorite=function(layId){
            for(var d=0;d<4;d++){
                if(layId==$scope.house[d].layId){
                    var a = $scope.house[d].content.favorite == 1 ? 'a' : 'b';
//                    if($scope.house[d].favorite == "0" ){
                    if(a == "a" ){
                        $http({
                            method: 'POST',
                            url: '/manageCollect',
                            data: "layoutId=" +layId +"&&sessionId=" + $scope.sessionId + "&&method=cancelFavorite",
                            headers: {'Content-Type': 'application/x-www-form-urlencoded'}
                        }).then(function successCallback(data) {
                            if (data.data.status == 1) {
                                $scope.house[d].content.favorite=0;
                            } else {
                                alert("取消失败");
                            }
                        }, function errorCallback(data) {
                        });
                    }else{
                        $http({
                            method: 'POST',
                            url: '/manageCollect',
                            data: "layoutId=" +layId + "&&sessionId=" + $scope.sessionId + "&&method=newFavorite",
                            headers: {'Content-Type': 'application/x-www-form-urlencoded'}
                        }).then(function successCallback(data) {
                            if (data.data.status == 1) {
                                console.log(d+"aaa")
                                $scope.house[d].content.favorite=1;
                            } else {
                                $scope.house[d].content.favorite=0;
                                alert(data.data.msg);
                            }
                        }, function errorCallback(response) {
                        });
                    }
                    return
                }
            }

        }
        $scope.addHouse=function(layId){
            var i = 0;
            var arrayLayId = getCookie("arrayLayId") != "" &&getCookie("arrayLayId") != null &&getCookie("arrayLayId") != undefined ? getCookie("arrayLayId").split(","):[];
            if(arrayLayId.length >= 4){
                alert("最多对比4个户型")
                return;
            }else if( arrayLayId != "" ) {
                for (var i = 0 ; i < arrayLayId.length ; i++){
                    if (arrayLayId[i] == layId){
                        alert("您已添加此户型");
                        return;
                    }
                }

            }
            var hous = true
            var layName = "";
            for(var h=0;h<5;h++){
                if($scope.houseList[h].LayID==layId){
                    layName = $scope.houseList[h].title;
                    hous=false;
                    break;
                }
            }
            if(layId){
                txt.push(layId)
                var arrayLayName = getCookie("arrayLayName");
                arrayLayName = arrayLayName+','+layName;
                setCookie("arrayLayId", txt);
                setCookie("arrayLayName", arrayLayName);
                window.location.reload();
            }
        }
        $scope.showHouseList=function(){
            var nameDivNum = txt[0]
            $http({
                method  : 'POST',
                url     : '/getRecommendLayout',
                data: "layId=" + nameDivNum,    // pass in data as strings
                headers : { 'Content-Type': 'application/x-www-form-urlencoded' }
            }).then(function successCallback(response) {

                $scope.houseList=response.data.content

            }, function errorCallback(response) {});
        }
        $scope.showHouseInfo(111);
        $scope.showHouseList();



    })

    //户型对比浮窗
    window.onscroll = function() {
        var s = $(this).scrollTop();

        if (s >= 630) {
            $(".nFix").css("display","block");
        } else {
            $(".nFix").css("display","none");

        }
    }
/*    var projectId = "787";
    var area = "100";
    $.ajax({
        type: "POST",
        url: 'http://*************:8800/getRecommendLayout',
        data:{
            projectId:projectId,
            area:area,
        },
        success:function (data) {
        }
    })*/

</script>

</body>
</html>