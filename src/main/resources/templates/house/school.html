


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml"
      xmlns:th="https://www.thymeleaf.org">
<head>
    <title>沈阳学区房_沈阳学区所属楼盘_沈阳学区房源 - 房小二网</title>
    <meta name="keywords" content="沈阳学区房,沈阳学区所属楼盘,沈阳学区附近房源,沈阳学区划分,沈阳小区所属学区"/>
    <meta name="description" content="房小二网优质学区为您提供准确的沈阳在售学区房信息，让您更快、更方便地了解沈阳在售学区房房源信息，查找、购买沈阳学区房，就在房小二网沈阳学区房。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/fang1xq">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20190920" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/school.css?v=20190517" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/cnxh.js"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/fxe_bnzf.css?t=20180502" rel="stylesheet" type="text/css"  />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20181023">
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <!--<script src="https://static.fangxiaoer.com/js/fxe_bnzf.js" type="text/javascript"></script>-->
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/swiper/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/swiper.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/syranking/syranks.css?v=2022">
<!--    <link rel="stylesheet" href="/css/school.css">-->

    <style type="text/css">
        #option_info {
            border: 0;
            border-bottom: 1px dashed #ededed;
        }
        #option_info b{
            color: #91a0b6;
            font-size: 14px;
            margin-left: 20px;
            font-weight: bold;
        }
        /*隐藏联系电话*/
        .search_tel{
            display: none;
        }
        #option_info div {
            overflow: hidden;
            display: inline-block;
            line-height: 24px;
            border: 1px solid #91a0b6;
            padding-left: 8px;
            margin-right: 6px;
            margin-top: 8px;
            float: left;
        }
        .house{
            cursor: pointer;
        }
        /*.chiListRank{ margin: 9px 7px 0 7px !important;}*/
        .crankList{ margin-top: 0px !important;}

        .vru{ width: 22px; height: 22px; position: absolute; left: 9px; top: 41px;
            background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    </style>
</head>
<body>
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=4"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1,listType=0"></div>

    <div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/houses/">沈阳新房</a> &gt; <a href="/schools/">优质学区</a></div>
    <div class="w1210">
        <div class="cl">
        </div>
        <div class="title w mt120" id="toplinkList">
            <p class="">
                <a href="/houses/">楼盘搜索</a></p>
            <p class="">
                <a href="/subways/">地铁沿线</a></p>
            <p class="hover">
                <a href="/schools/">优质学区</a></p>
            <!--<p class="">-->
                <!--<a href="/lowpays/">低首付楼盘</a></p>-->
            <p class="">
                <a href="/exits/">现房入住</a></p>
        </div>
        <div class="w main" id="option">
            <ul>
                <li class="fenlei">
                    <p>位置：</p>
                    <a id="btnRegion" href="/houses/"   ><b class="listIcon listIcon_dw"></b>区域<i></i></a>
                    <a id="btnSubway" href="/subways/k1"><b class="listIcon listIcon_ditie"></b>地铁<i></i></a>
                    <a href="/schools/" class="hover"><b class="listIcon listIcon_xq"></b>学区</a>
                    <a href="/static/houseMap.htm?type=3"><b class="listIcon listIcon_map"></b>地图<i></i></a>
                </li>
                <div id="SearchAll1_xuexiao">

                    <li id="priceSearch">
                        <p>类别：</p>
                        <a th:each="school:${schoolType}" th:text="${#strings.toString(school.name).replaceAll('全部','不限')}" th:href="${school.url}" th:class="${school.selected}? 'hover':''"></a>
                    </li>
                </div>
                <div id="SearchAll1_quyu">

                    <li id="regionSearch">
                        <p>区域：</p>
                        <a th:each="region:${region}" th:text="${#strings.toString(region.name).replaceAll('全部','不限')}" th:href="${region.url}" th:class="${region.selected}? 'hover':''"></a>
                    </li>
                </div>
            </ul>
        </div>
    </div>
    <div id="option_info" style="display: none">
        <b >已选：</b>
        <!--户型-->
        <div th:each="s:${schoolType}" th:if="${s.selected and s.name ne '全部'}">
            <span class="condition"><th:block th:text="${s.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'s'+s.id}"></i>
        </div>
        <!--面积-->
        <div th:each="r:${region}" th:if="${r.selected and r.name ne '全部'}">
            <span class="condition"><th:block th:text="${r.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'r'+r.id}"></i>
        </div>
        <a href="/schools/"  class="clean">清除全部</a>
        <script>
            /*判断是否显示条件栏目*/
            $(function () {
                if($(".condition").text() != ""){
                    $("#option_info").css("display","block");
                }
            })
            /*去掉条件标签*/
            $(".cleanUrl").click(function () {
                var oldUrl = location.pathname;
                if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
                    var moreUrl ="-" + $(this).attr("value");
                }else{ var moreUrl = $(this).attr("value");}

            var newUrl = oldUrl.replace(moreUrl,"");
            window.location.href = newUrl;
        });
    </script>
</div>
    <style>

    </style>
<div class="w1210">
    <div class="w">
        <div class="left_box">
            <div th:include="fragment/fragment:: commom_horizontalBanner"></div>
            <div id="left">

                <div class="listHeader">
                    全部学校
                    <ul id="mapTab">
                        <li id="tabMap"><a href="/static/houseMap.htm?type=3"><i></i>地图</a></li>
                        <!--<li id="tabList" class="hover"><i></i>列表</li>-->
                    </ul>
                </div>
                <div class="lp_count" th:if="${msg eq '0'}">
                    <div class="lp_con">小二为你找到 <span id="SchoolCount" th:text="${msg}">47</span>个符合条件的学校</div>
                    <div class="none_box">
                        <p>
                            很抱歉，沈阳暂时没有符合您要求的学校，您可以更改条件重新搜索。<br>
                            懒得搜索？！<a th:href="@{'/helpSearch?ids=1'}" rel="2" dir="1" target="_blank">点击免费发布购房服务方案>></a>
                        </p>
                    </div>

                </div>
                <div class="house" th:each="schoolHouse:${schools}" th:onclick="${'window.open(''/schoolhouses/'+schoolHouse.schoolId+''')'}">
                    <div class="img">
                        <a th:href="${'/schoolhouses/' +schoolHouse.schoolId}" target="_blank" id="Rpt_House_ctl00_ImgUrl">
                            <img id="Rpt_House_ctl00_PicUrl" th:title="${schoolHouse.schoolName}" th:src="${schoolHouse.picUrl}" th:alt="${schoolHouse.schoolName}" style="border-width:0px;" />
                        </a>
                    </div>
                    <ul class="info">
                        <li><a class="info_tit"  th:href="${'/schoolhouses/'+schoolHouse.schoolId}" target="_blank" id="Rpt_House_ctl00_SchoolAreaName" th:text="${schoolHouse.schoolName}">朝阳一校沈水分校</a></li>
                        <li>
                            <th:block th:if="${schoolHouse.projectNum ne '0'}">新房楼盘(<i class="housesNum"><th:block th:text="${schoolHouse.projectNum}"></th:block></i>)</th:block>
                            <th:block th:if="${schoolHouse.secHouseNum ne '0'}">二手房房源(<i class="housesNum"><th:block th:text="${schoolHouse.secHouseNum}"></th:block></i>)</th:block>
                        </li>
                        <li>
<!--                            <th:block  th:text="${#strings.abbreviate('['+schoolHouse.regionName+']'+schoolHouse.address,26)}"></th:block>-->
                            <th:block th:text="${schoolHouse.regionName}"></th:block><img class="imgG" src="/imagese/Geng.png" alt=""> <th:block th:text="${schoolHouse.address}"></th:block>

                        </li>


                        <li class="label_wei">
                            <span class="zd"  th:text="${schoolHouse.schoolTypeValue}"></span>
                            <span class="zd" th:text="${schoolHouse.schoolScale}"></span>
                        </li>
                    </ul>
                    <ul class="prices">
                        <li th:each="sh,i:${schoolHouse.houseInfo}"  th:if="${i.count &lt; 4}">
                            <p>
                                <span th:each="pr,i:${sh.price}" th:if="${i.index eq 0}">
                                    <b>
                                        <th:block  th:text="${#numbers.formatInteger( pr.money,1)}"></th:block>
                                    </b>
                                    <th:block th:if="${i.index eq 0}">元/㎡</th:block>
                                </span>
                                <span th:if="${#lists.isEmpty(sh.price)}">
                                    <b class="daiding">待定</b>&nbsp;&nbsp;&nbsp;&nbsp;
                                </span>
                                <a th:href="${'/house/'+#numbers.formatInteger(sh.projectId,1)+'-'+ #numbers.formatInteger(sh.projectType,1)+'.htm'}" target="_blank">
                                    <th:block th:text="${#strings.toString(sh.projectType).indexOf('2') ne -1 ? #strings.abbreviate('[别墅]'+sh.projectName ,15) : #strings.abbreviate(sh.projectName,11)}"></th:block>
                                </a>
                            </p>
                        </li>
                        <li th:each="sh,i:${schoolHouse.houseInfo}">
                            <a class="rightText" th:if="${i.index eq 3}" th:href="${'/schoolhouses/'+#numbers.formatInteger(sh.schoolAreaID,1)}" target="_blank">查看更多></a>
                        </li>
                    </ul>
                    <div class="cl"></div>
                    <!--info end-->
                </div>

                <!--house end-->
                <div class="cl">
                </div>
                <div class="page"  th:if="${msg ne '0'}">
                    <div th:include="fragment/page :: page"></div>
                </div>
                <div class="cl">
                </div>
            </div>
            <div class="school" th:if="${!#lists.isEmpty(recommendSchool)}">
                <div class="title_sun">
                    <p>推荐学校</p>
                </div>
               <div class="content">
                   <div class="list_box" th:each="schoolHouse:${recommendSchool}" th:onclick="${'window.open(''/schoolhouses/'+schoolHouse.schoolId+''')'}">
                       <div class="img">
                           <a th:href="${'/schoolhouses/'+schoolHouse.schoolId}" target="_blank" id="Rpt_House_ctl00_ImgUrl">
                               <img id="Rpt_House_ctl00_PicUrl" th:title="${schoolHouse.schoolName}" th:src="${schoolHouse.picUrl}" th:alt="${schoolHouse.schoolName}" style="border-width:0px;" />
                           </a>
                       </div>
                       <ul class="info">
                           <li><a class="info_tit"  th:href="${'/schoolhouses/'+schoolHouse.schoolId}" target="_blank" id="Rpt_House_ctl00_SchoolAreaName" th:text="${schoolHouse.schoolName}">朝阳一校沈水分校</a></li>
                           <li>
                               <th:block th:if="${schoolHouse.projectNum ne '0'}">新房楼盘（<i class="housesNum"><th:block th:text="${schoolHouse.projectNum}"></th:block></i>)</th:block>
                               <th:block th:if="${schoolHouse.secHouseNum ne '0'}">二手房房源(<i class="housesNum"><th:block th:text="${schoolHouse.secHouseNum}"></th:block></i>)</th:block>
                           </li>
                           <li>
<!--                               <th:block  th:text="${#strings.abbreviate('['+schoolHouse.regionName+']'+schoolHouse.address,26)}"></th:block>-->
                               <th:block th:text="${schoolHouse.regionName}"></th:block><img class="imgG" src="/imagese/Geng.png" alt=""> <th:block th:text="${schoolHouse.address}"></th:block>

                           </li>
                           <li>
                               <span class="zd" th:text="${schoolHouse.schoolTypeValue}"></span>
                               <span class="zd" th:text="${schoolHouse.schoolScale}"></span>
                           </li>
                       </ul>
                       <ul class="prices">
                           <li th:each="sh,i:${schoolHouse.houseInfo}"  th:if="${i.count &lt; 4}">
                               <p>

                                <span th:each="pr,i:${sh.price}" th:if="${i.index eq 0}">
                                    <b>
                                        <th:block  th:text="${#numbers.formatInteger( pr.money,1)}"></th:block>
                                    </b>
                                    <th:block th:if="${i.index eq 0}">元/㎡</th:block>
                                </span>
                                   <span th:if="${#lists.isEmpty(sh.price)}">
                                    <b  class="daiding">待定</b>&nbsp;&nbsp;&nbsp;&nbsp;
                                </span>
                                   <a th:href="${'/house/'+#numbers.formatInteger(sh.projectId,1) +'-'+ #numbers.formatInteger(sh.projectType,1)+'.htm'}" target="_blank">
                                       <th:block th:text="${#strings.toString(sh.projectType).indexOf('2') ne -1 ? #strings.abbreviate('[别墅]' + sh.projectName,15) : #strings.abbreviate(sh.projectName,11)}"></th:block>
                                   </a>
                               </p>
                           </li>
                           <li th:each="sh,i:${schoolHouse.houseInfo}">
                               <a class="rightText" th:if="${i.index eq 3}" th:href="${'/schoolhouses/'+#numbers.formatInteger(sh.schoolAreaID,1)}">查看更多></a>
                           </li>
                       </ul>
                       <div class="cl"></div>
                   </div>
               </div>


            </div>
            <div class="cl"></div>
        </div>

        <!--left end-->
        <div id="right">
            <!--<div th:include="fragment/fragment :: right_order"></div>-->
            <div th:include="fragment/fragment :: right_school" th:with="schoolList=0"></div>
        </div>
        <div class="cl">
        </div>
    </div>
</div>
    <script >
        $("a,.contrast,.contrastNo").click(function() {
            event.stopPropagation();
        });
        for (var i=0;i<$(".Inventory .list ul li").length;i++){
            var h=$(".Inventory .list ul li").eq(i).find(" .metro .list_sun").height()
            $(".Inventory .list ul li").eq(i).find(".metro p").css("line-height",h+"px")
        }

        /*热搜榜*/
        $(".crankList").eq(0).hide();
        $(".crankDetails").eq(0).show();

        $(".crankList").hover(function () {
            // $(".crankList").show();
            $(this).parent().parent().parent().find(".crankList").show();
            $(this).hide();
            $(this).parent().parent().parent().find(".crankDetails").hide();
            $(this).parent().find(".crankDetails").show();
        })
    </script>
<div th:include="fragment/fragment::commonFloat" th:with="commonType=1"></div>
<div th:include="fragment/fragment:: footer_seo"></div>
<div th:include="fragment/fragment:: footer_list"></div>
<div th:include="fragment/fragment::tongji"></div>
<!--<div th:include="fragment/fragment::common_meiqia"></div>-->
</body>
<script src="/js/house/houseMap.js"></script>
</html>
