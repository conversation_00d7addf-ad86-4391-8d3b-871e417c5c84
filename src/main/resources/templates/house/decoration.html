<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳精装房_沈阳精装楼盘_沈阳高质楼盘 - 房小二网</title>
    <meta name="keywords" content="沈阳精装房,现房入住,沈阳现房发售, 沈阳精装楼盘,沈阳装修楼盘,沈阳新房购买"/>
    <meta name="description" content="房小二网精装房栏目，为您提供丰富全面的沈阳精装房新楼盘信息，帮您更准确地了解沈阳精装房房源信息，为您购房提供更大的便利条件，详细关注沈阳精装房项目信息，尽在房小二网沈阳精装房。"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/decorates/'+mobileAgent}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20181019" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/ljzf_default.css?v=20180517" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/ljzf_default2.css?v=20180517" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20180502">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/house/menu.js"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/js/tc/tc.css?t=20180502" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
<!--    <link rel="stylesheet" href="/css/ljzf_default.css">-->

</head>
<style>
    .houseInfo{
        cursor: pointer;
    }
</style>
<body class="w1210">
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=14,listType=0"></div>
<div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/houses/">沈阳新房</a> &gt;
    <a href="/decorations">精装房</a>
</div>
<div class="list"></div>
<div class="title w mt120">
    <p class="hover"><a href="/houses">楼盘搜索</a></p>
    <p><a href="/subways/">地铁沿线</a></p>
    <p><a href="/schools/">优质学区</a></p>
    <!--<p><a href="/lowpays/">低首付楼盘</a></p>-->
    <p><a href="/exits/">现房入住</a></p>
</div>
<div class="main">
    <input type="hidden" id="glzs" th:value="${#session?.getAttribute('sessionId')}"/>
    <div id="option">
        <ul>
            <li class="fenlei">
                <p>位置：</p>
                <a id="btnRegion"  href="/houses/" class="hover"><b class="listIcon listIcon_dw"></b>区域<i></i></a>
                <a id="btnRegion"  href="/subways/k1" class=""><b class="listIcon listIcon_ditie"></b>地铁<i></i></a>
                <!--<a id="btnRegion" th:each="r,i:${region}"  onclick="showIndex(this)" th:if="${i.index eq 0 }" th:href="${r.url}"  class=""><b class="listIcon listIcon_dw"></b>区域<i></i></a>-->
                <!--<a id="btnSubway"  th:each="r,i:${regionSubway}" th:href="${i.index == 1? r.url: '/subways/rs2'}"  th:if="${i.index != 0}"  th:class="${#strings.isEmpty(regionSubwayId) and i.index ==1?'hover':(r.selected? 'hover':'')}"><b th:class="${r.id eq '1'? 'listIcon listIcon_dw':'listIcon listIcon_ditie'}"></b><th:block th:text="${r.name}"></th:block> <i></i></a>-->
                <a href="/schools/"><b class="listIcon listIcon_xq"></b>学区<i></i></a>
                <a href="/salemap"><b class="listIcon listIcon_map"></b>地图<i></i></a>
            </li>
            <!--区域-->
            <li id="Search_zf" class="leibie" th:if="${regionSubwayId ne '2'}" >
                <a th:each="r,stat:${region}" th:href="${r.url}"  onclick="showIndex(this)" th:text="${stat.index==0?'不限':r.name}" th:id="'r'+${r.id}" th:class="${r.selected}? 'hover':''"></a>
            </li>
            <!--地铁-->
            <li id="Search_ditie" class="leibie"   th:if="${regionSubwayId eq '2'}" >
                <a th:each="b,stat:${subway}" th:href="${b.url}"  onclick="showIndex(this)" th:text="${stat.index==0?'不限':b.name}" th:id="'r'+${b.id}" th:class="${b.selected}? 'hover':''"></a><br>
                <span th:if="${subwayStation}">
                        <a th:each="q,stat:${subwayStation}"  onclick="showIndex(this)" th:text="${stat.index==0?'不限':q.name}" th:href="${q.url}" th:id="'j'+${q.id}" th:class="${q.selected}? 'hover':''"></a>
                    </span>
            </li>
            <li><p>户型：</p>
                <a th:each="layout,stat:${layout}" onclick="showIndex(this)"  th:text="${stat.index == 0? '不限': layout.name}" th:href="${layout.url}" th:class="${layout.selected}? 'hover':''"></a>
            </li>
            <li><p>面积：</p>
                <a th:each="area,stat:${area}" onclick="showIndex(this)"  th:text="${stat.index == 0?'不限':area.name}" th:href="${area.url}" th:class="${area.selected}? 'hover':''"></a>
            </li>
            <li><p>装修：</p>
                <a th:each="style,stat:${style}"  onclick="showIndex(this)" th:text="${stat.index == 0? '不限': style.name}" th:href="${style.url}" th:class="${style.selected}? 'hover':''"></a>
            </li>
        </ul>
    </div>
    <div id="option_other">
        <ul>
            <li><p>更多：</p>
                <div class="select_box" style="border: none">
                    <div class="select_info">精装房</div>
                    <ul>
                        <!--<li th:each="f:${forward}"> <a th:href="${f.url}" onclick="showIndex(this)" th:text="${f.id eq '' ? '朝向不限':f.name}" th:class="${f.selected}? 'hover':''"></a></li>-->
                        <!--<li> <a th:href="${#strings.toString(pageUrl).replaceAll('^/[a-z]+','/houses')} " th:text="新房" onclick="showIndex(this)" ></a></li>-->
                        <!--<li> <a th:href="${#strings.toString(pageUrl).replaceAll('^/[a-z]+','/subways')} " th:text="地铁房" onclick="showIndex(this)" ></a></li>-->
                        <!--<li> <a th:href="${#strings.toString(pageUrl).replaceAll('^/[a-z]+','/houses')} " th:text="特色" onclick="showIndex(this)" ></a></li>-->
                        <!--<li> <a th:href="${#strings.toString(pageUrl).replaceAll('^/[a-z]+','/lowpays')} " th:text="低首付" onclick="showIndex(this)" ></a></li>-->
                        <li> <a th:href="${#strings.toString(pageUrl).replaceAll('^/[a-z]+','/exits')} " th:text="现房" onclick="showIndex(this)" ></a></li>
                        <li> <a th:href="${#strings.toString(pageUrl).replaceAll('^/[a-z]+','/decorations')} " th:text="精装房" onclick="showIndex(this)" ></a></li>
                        <li> <a th:href="${#strings.toString(pageUrl).replaceAll('^/[a-z]+','/brands')} " th:text="品牌房企" onclick="showIndex(this)" ></a></li>
                    </ul>
                </div>
                <div class="select_box" style="border: none">
                    <div class="select_info" th:each="loop,i:${loop}" th:if="${loop.selected}" th:text="${i.index == 0 ? '环线':loop.name}">环线不限</div>
                    <ul>
                        <li th:each="f:${loop}"> <a th:href="${f.url}" onclick="showIndex(this)" th:text="${f.id eq '' ? '环线不限':f.name}" th:class="${f.selected}? 'hover':''"></a></li>
                    </ul>
                </div>
            </li>
        </ul>
    </div>
    <div id="option_info" style="display: none">
        <b >已选：</b>
        <!--区域-->
        <div th:each="r:${region}" th:if="${r.selected and !#strings.isEmpty(r.id)}" th:onclick="${'removeSelect('+1+')'}">
            <span class="condition"><th:block th:text="${r.name}"></th:block></span>
            <i class="cleanUrl" ></i>
        </div>
        <!--&lt;!&ndash;地铁线&ndash;&gt;-->
        <!--<div th:each="s:${subway}" th:if="${s.selected and  !#strings.isEmpty(s.id)}" th:onclick="${'removeSelect('+1+')'}" >-->
            <!--<span class="condition"><th:block th:text="${s.name}"></th:block></span>-->
            <!--<i class="cleanUrl"></i>-->
        <!--</div>-->
        <!--&lt;!&ndash;地铁站&ndash;&gt;-->
        <!--<div th:each="s:${subwayStation}" th:if="${s.selected and  !#strings.isEmpty(s.id)}" th:onclick="${'removeSpan('+1+')'}">-->
            <!--<span class="condition"><th:block th:text="${s.name}"></th:block></span>-->
            <!--<i class="cleanUrl"></i>-->
        <!--</div>-->
        <!--户型-->
        <div th:each="l:${layout}" th:if="${l.selected and !#strings.isEmpty(l.id)}" th:onclick="${'removeSelect('+2+')'}">
            <span class="condition"><th:block th:text="${l.name}"></th:block></span>
            <i class="cleanUrl" ></i>
        </div>
        <!--面积-->
        <div th:each="l:${area}" th:if="${l.selected and !#strings.isEmpty(l.id)}" th:onclick="${'removeSelect('+3+')'}">
            <span class="condition"><th:block th:text="${l.name}"></th:block></span>
            <i class="cleanUrl" ></i>
        </div>
        <!--装修风格-->
        <div th:each="l:${style}" th:if="${l.selected and !#strings.isEmpty(l.id)}" th:onclick="${'removeSelect('+4+')'}">
            <span class="condition"><th:block th:text="${l.name}"></th:block></span>
            <i class="cleanUrl" ></i>
        </div>
        <!--环线-->
        <div th:each="l:${loop}" th:if="${l.selected and !#strings.isEmpty(l.id)}" th:onclick="${'removeSelectClick('+1+')'}" >
            <span class="condition"><th:block th:text="${l.name}"></th:block></span>
            <i class="cleanUrl" ></i>
        </div>
        <!--精装房-->
        <div onclick="toNewHouseIndex()">
            <span class="condition">精装房</span>
            <i class="cleanUrl" ></i>
        </div>
    </div>
    <script>
        $(function () {
            $(".search_tel").hide();
            //条件区域加载渲染
            if($(".condition").text() != ""){
                $("#option_info").css("display","block");
            }
            loadCondition();

        });
        function loadCondition() {
            var isAdd = false;
            //非手填项渲染
//            selectPrice(isAdd);
            //清空全部按钮
            if($(".condition").text() != ""){
                $("#option_info").append("<a>清空全部</a> ");
                $("#option_info a:last").attr("href", "/houses/").attr("class", "clean");
            }
        }
        function removeSelect(index){
            $("#option ul li:eq("+index+") a:eq(0)").click();
        }
        function removeSelectClick(index){
            $('.select_box:eq('+index+') a:eq(0)').click();
        }
        function removeSpan(index){
            $("#option ul li:eq("+index+") span a:eq(0)").click();
        }
        function showIndex(obj) {
            obj.click();
        }
        function toNewHouseIndex() {
            var oldUrl = location.pathname;
            window.location.href = oldUrl.replace("decorations","houses");
        }
        function selectPrice(isAdd,names) {
            //总价、面积筛选条件渲染
            var ba = $("#option input[id='minArea']").val();
            var ea = $("#option input[id='maxArea']").val();
            if(ba != "" || ea != ""){
                if (!isAdd) {
                    $("#option_info").show();
                    isAdd = true;
                }
                var str = "";
                ba = ba == "" ? 0 : ba;
                if(ea == ""){
                    str = "大于等于" + ba;
                }else{
                    if(ba == ""){
                        $("#option input[id='ba']").val("0");
                    }
                    str = ba + "-" + ea;
                }
                $("#option_info").append("<div><span class='condition'>面积筛选：" + str + "㎡</span><i></i> </div>");
                $("#option_info div:last").attr("onclick", "removeClick(" + 3 + ");");
            }
        }
    </script>
    <div class="cl"></div>
    <div id="Search_PanSaleDom">

        <div class="search_more hid">
            <ul>
                <li><p>更多找房条件：</p>
                    <div class="select_box">
                        <div class="select_info">朝向不限</div>

                        <ul>

                        </ul>
                    </div>
                    <div class="select_box">
                        <div class="select_info">装修不限</div>
                        <ul>

                        </ul>
                    </div>
                    <div class="select_box">
                        <div class="select_info">楼层不限</div>
                        <ul>

                        </ul>
                    </div>
                </li>

            </ul>
        </div>
        <script type="text/javascript">
            //此段js用于控制 手动筛选框
            function price(priceIdName) {
                $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
                $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
                var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
                var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
                if (num1 == "" && num2 != "") {
                    $("#" + priceIdName + " input").eq(0).val("0");
                    $("#" + priceIdName + " input").eq(2).show();
                } else if (num2 == "" && num1 != "") {
                    $("#" + priceIdName + " input").eq(2).show();
                } else if (num1 != "" || num2 != "") {
                    $("#" + priceIdName + " input").eq(2).show();
                } else {
                    $("#" + priceIdName + " input").eq(2).hide();
                }
            }

//            price("Search_BuildAreaDomOk");
            $("#Search_BuildAreaDomOk input").keyup(function () {
                price("Search_BuildAreaDomOk");
            })
            $("#Search_BuildAreaDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                    $("#Search_Btn_Searchs").click()
                }
            });
        </script>

    </div>
    <div class="cl"></div>
    <div class="w">
        <div id="left">
            <div class="listHeader">全部楼盘
                <a href="/static/houseMap.htm"><i class="map"></i>地图</a>
                <!--<a href="" class="">点评<i class="up"></i></a>-->
                <!--<a href="" class="hover">价格<i class="down"></i></a>-->
                <!--<a href="" style="padding-right: 14px;">综合排序</a>-->
            </div>
            <div class="lp_count">
                <div class="lp_con">共有<span class="search_count"  th:text="${msg}">507</span>个符合条件的楼盘</div>
            </div>
            <div class="cl"></div>

            <div class="houseInfo" th:each="jz:${decoration}" th:onclick="${'window.open(''/decoration/'+jz.projectId+ '-' + jz.projectType +'.htm'')'}">
                <div class="img">
                    <a th:href="${'/decoration/'+jz.projectId+ '-' + jz.projectType +'.htm'}" target="_blank">
                        <img img th:src="${jz.pic}" th:alt="${jz.projectName}"/></a>
                    <span class="images"></span>
                    <span class="video"></span>
                </div>
                <ul class="info">
                    <li><a th:href="${'/decoration/'+jz.projectId+ '-' + jz.projectType +'.htm'}" target="_blank"> <b th:text="${'【'+jz.projectName+'】'+jz.title}">龙湖西府原著</b><span class="rob"></span></a></li>
                    <!--<li th:text="'楼盘：'+${jz.projectName}"></li>-->
                    <li th:if="${!#strings.isEmpty(jz.regionName)}" th:text="'区域：'+${jz.regionName}"></li>
                    <li th:if="${!#strings.isEmpty(jz.fenLei)}" th:text="'类型：'+${jz.fenLei}"></li>
                    <li th:if="${!#strings.isEmpty(jz.decor)}" th:text="'装修风格：'+${jz.decor}"></li>
                    <li class="decorateDesc" th:if="${!#strings.isEmpty(jz.decorDesc)}" th:text="'装修描述：'+${jz.decorDesc}"></li>
                </ul>
                <ul class="prices">
                    <li>精装标准&nbsp;<span><b th:utext="${#strings.isEmpty(jz.decorStandard)?'':#strings.toString(jz.decorStandard).replaceAll('元/㎡','<p>元/㎡</p>').replaceAll('以实际交付为准', '<s class=jzfwz >以实际交付为准</s>')}">6800</b></span></li>
                    <li class="phone" ><i class="icon_iphone"></i><th:block th:utext="${#strings.toString(jz.sortelTel).indexOf('转') == -1? jz.sortelTel:#strings.toString(jz.sortelTel).replace('转',' <s>转</s> ') }"></th:block></li>
                    <!--<li><a class="collect" href="#login">收藏</a></li>-->
                </ul>
                <div class="cl"></div>
            </div>

            <script type="text/javascript">
                $("a").click(function() {
                    event.stopPropagation();
                });


                Array.prototype.forEach.call( document.getElementsByClassName("features"), function (t) {
                    if($(t).find(".tar_right").length > 1) {
                        $(t).find(".tar_right").hide();
                    }
                });
                $(".targer").mouseover(function () {
                    $(this.parentNode).find(".tar_right").hide();
                    $(this).find(".tar_right").show();
                })
            </script>
            <!--house end-->
            <div th:include="fragment/fragment :: list_bottom"></div>
            <div class="cl"></div>
            <div class="page">
                <div th:include="fragment/page :: page"></div>
            </div>
            <div class="cl"></div>
        </div>
        <!--left end-->
        <div id="right">
            <div th:include="fragment/fragment :: right_order"></div>
            <div th:include="fragment/fragment :: right_other" th:with="decorationType=1"></div>
            <div class="cl"></div>
        </div>
    </div>
    <div th:include="fragment/fragment::commonFloat" th:with="commonType=1"></div>
    <div th:include="fragment/fragment:: footer_seo"></div>
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <script src="/js/house/houseMap.js"></script>
</body>
</html>
