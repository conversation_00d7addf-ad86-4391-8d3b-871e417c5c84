<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title>沈阳学区房，沈阳优质学区房 - 房小二网</title>
    <meta name="keywords" content="沈阳学区,沈阳优质学区,学区房,沈阳学区房,沈阳新楼盘,沈阳买房,沈阳房产网"/>
    <meta name="description" content="房小二网优质学区为您提供准确的沈阳在售学区房信息，让您更快、更方便地了解沈阳在售学区房房源信息，查找、购买沈阳学区房，就在房小二网沈阳学区房。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/fang1xq">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20190920" rel="stylesheet" type="text/css" />
    <!--<link href="/css/main2017.css" rel="stylesheet" type="text/css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/ljzf_school.css?t=20201016" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/cnxh.js"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/fxe_bnzf.css?t=20180502" rel="stylesheet" type="text/css"  />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20181023">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newHouse.css?t=20190326" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <!--<script src="https://static.fangxiaoer.com/js/fxe_bnzf.js" type="text/javascript"></script>-->
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/house/contrastHouse.js?v=20240510" type="text/javascript"></script>
    <link rel="stylesheet" href="/css/newHouse.css">
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/syranking/syranks.css?v=2022">

<!--        <link rel="stylesheet" href="/css/ljzf_default.css">-->

    <style>
        /*隐藏联系电话*/
        .search_tel{
            display: none;
        }
        .vrs{ width: 34px; height: 34px; position: absolute; left: 5px; bottom: 5px; z-index: 0; }
        .vrs i{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 3; display: block; background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;}
        .vrs b{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 1; display: block; background-color: rgba(0,0,0,0.5); border-radius: 50%;}
    </style>
</head>
<body>
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=4"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1,listType=0"></div>

    <div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/houses/">沈阳新房</a> &gt; <a href="/schools/">优质学区</a></div>
    <div class="w1210">
        <div class="cl">
        </div>
        <div class="title w mt120" id="toplinkList">
            <p class="">
                <a href="/houses/">楼盘搜索</a></p>
            <p class="">
                <a href="/subways/">地铁沿线</a></p>
            <p class="hover">
                <a href="/schools/">优质学区</a></p>
            <!--<p class="">-->
                <!--<a href="/lowpays/">低首付楼盘</a></p>-->
            <p class="">
                <a href="/exits/">现房入住</a></p>
        </div>
        <div class="w main" id="option">
            <ul>
                <li class="fenlei">
                    <p>位置：</p>
                    <a id="btnRegion" href="/houses/"   ><b class="listIcon listIcon_dw"></b>区域<i></i></a>
                    <a id="btnSubway" href="/subways/k1"><b class="listIcon listIcon_ditie"></b>地铁<i></i></a>
                    <a href="/schools/" class="hover"><b class="listIcon listIcon_xq"></b>学区</a>
                    <a href="/static/houseMap.htm?type=3"><b class="listIcon listIcon_map"></b>地图<i></i></a>
                </li>
                <div id="SearchAll1_xuexiao">
                    <li id="priceSearch">
                        <p>类别：</p>
                        <a th:each="school:${schoolType}" th:id="${'s'+school.id}"  th:text="${#strings.toString(school.name).replaceAll('全部','不限')}" th:href="${school.url}" th:class="${school.selected}? 'hover':''"></a>
                    </li>
                </div>
                <div id="SearchAll1_quyu">
                    <li id="regionSearch">
                        <p>区域：</p>
                        <a th:each="region:${region}" th:id="${'r'+region.id}" th:text="${#strings.toString(region.name).replaceAll('全部','不限')}" th:href="${region.url}" th:class="${region.selected}? 'hover':''"></a>
                    </li>
                </div>
            </ul>
        </div>
    </div>
    <div id="option_info">
        <b >已　　选：</b>
        <!--学校类型-->
        <div>
            <span id="xueiaoType" th:text="${schoolInfo.schoolType}"></span>
            <i id="xueiaoTypei" ></i>
        </div>
        <!--区域-->
        <div>
            <span id="quyu" th:text="${schoolInfo.regionName}" th:regionid="${schoolInfo.regionId}"></span>
            <i id="quyui" ></i>
        </div>
        <div>
            <span id="xuexiao" th:text="${schoolInfo.schoolName}"></span>
            <i id="xuexiaoi"></i>
        </div>
        <a href="/schools/"  class="clean">清除全部</a>
        <script>
            for (var i = 1 ; i < 20 ; i++){
                if ( $("#xueiaoType").text().trim() == $("#s"+i).text()) {
                    $("#s"+i).attr("class","hover");
                }
                if ( $("#quyu").text().trim() == $("#r"+i).text()) {
                    $("#r"+i).attr("class","hover");
                }
            }
            var schoolType ;
            if ( $("#xueiaoType").text().trim() == "中学") {
                schoolType = "s2";
            }else if($("#xueiaoType").text().trim() == "小学"){
                schoolType = "s1";
            }
            var region = "r"+$("#quyu").attr("regionid");
            $("#xueiaoTypei").click(function () {
                window.location.href="/schools/"+region;
            })
            $("#quyui").click(function () {
                window.location.href="/schools/"+schoolType;
            })
            $("#xuexiaoi").click(function () {
                window.location.href="/schools/"+schoolType+"-"+region;
            })
        </script>
    </div>
    <div class="w1210">
        <div class="w">

            <div id="left">
                <div class="school_wei" id="schoolMain"   th:if="${schoolInfo != null and !#maps.isEmpty(schoolInfo)}">
                    <div class="photo" style="float: left">
                        <img th:src="${#strings.isEmpty(schoolInfo.picUrl)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':schoolInfo.picUrl}">
                    </div>
                    <ul style="float: left">
                        <li class="name_box"> <span class="name"><th:block th:text="${#strings.isEmpty(schoolInfo.schoolName)?'暂无资料':schoolInfo.schoolName}"></th:block></span> <label th:if="${!#strings.isEmpty(schoolInfo.schoolType)}"><th:block th:text="${#strings.isEmpty(schoolInfo.schoolType)?'暂无资料':schoolInfo.schoolType}" ></th:block></label> <label th:if="${!#strings.isEmpty(schoolInfo.scale)}"><th:block th:text="${#strings.isEmpty(schoolInfo.scale)?'暂无资料':schoolInfo.scale}" ></th:block></label></li>
                        <li class="address" th:if="${!#strings.isEmpty(schoolInfo.address)}">
                            <span>地址：</span>
                            <th:block th:text="${#strings.isEmpty(schoolInfo.address)?'暂无资料':schoolInfo.address}" ></th:block>
                        </li>
                        <li class="address"  th:if="${!#strings.isEmpty(schoolInfo.telephone)}">
                            <span>电话：</span>
                            <th:block th:text="${#strings.isEmpty(schoolInfo.telephone)?'暂无资料':schoolInfo.telephone}" ></th:block>
                        </li>
                        <li class=""  th:if="${!#strings.isEmpty(schoolInfo.schoolCondition)}">
                            <div class="over address">
                                <span>入学条件：</span>
                                <th:block th:text="${#strings.isEmpty(schoolInfo.schoolCondition)?'暂无资料':schoolInfo.schoolCondition}" ></th:block>

                            </div>
                            <div class="layer_sun" style="display: none">
                                <div class="ms_sanjiao dt_sj"></div>
                                <span>入学条件：</span>
                                <th:block th:text="${#strings.isEmpty(schoolInfo.schoolCondition)?'暂无资料':schoolInfo.schoolCondition}" ></th:block>
                            </div>
                        </li>
                    </ul>
                    <ul class="bottomDec">
                        <li class=""  th:if="${!#strings.isEmpty(schoolInfo.streetAddress)}">
                            <div class="over address">
                                <span>对应社区/街道：</span>
                                <th:block th:text="${#strings.isEmpty(schoolInfo.streetAddress)?'暂无资料':schoolInfo.streetAddress}" ></th:block>
                            </div>
                            <div class="layer_sun" style="display: none">
                                <div class="ms_sanjiao dt_sj"></div>
                                <span>对应社区/街道：</span>
                                <th:block th:text="${#strings.isEmpty(schoolInfo.streetAddress)?'暂无资料':schoolInfo.streetAddress}" ></th:block>
                            </div>
                        </li>
                        <li class=""  th:if="${!#strings.isEmpty(schoolInfo.schoolDesc)}">
                            <div class="over address">
                                <span>学校介绍：</span>
                                <th:block th:text="${#strings.isEmpty(schoolInfo.schoolDesc)?'暂无资料':schoolInfo.schoolDesc}" ></th:block>
                            </div>
                            <div class="layer_sun" style="display: none">
                                <div class="ms_sanjiao dt_sj"></div>
                                <span>学校介绍：</span>
                                <th:block th:text="${#strings.isEmpty(schoolInfo.schoolDesc)?'暂无资料':schoolInfo.schoolDesc}" ></th:block>
                            </div>
                        </li>
                        <li class=""  th:if="${!#strings.isEmpty(schoolInfo.schoolFeature)}">
                            <div class="over address">
                                <span>学校特色：</span>
                                <th:block th:text="${#strings.isEmpty(schoolInfo.schoolFeature)?'暂无资料':schoolInfo.schoolFeature}" ></th:block>
                            </div>
                            <div class="layer_sun" style="display: none">
                                <div class="ms_sanjiao dt_sj"></div>
                                <span>学校特色：</span>
                                <th:block th:text="${#strings.isEmpty(schoolInfo.schoolFeature)?'暂无资料':schoolInfo.schoolFeature}" ></th:block>
                            </div>
                        </li>
                    </ul>
                    <div class="seeMoreBtn">
                        <div>
                            <span>查看详情</span><i></i>
                        </div>
                    </div>
                    <div class="cl"></div>
                </div>
                <script type="application/javascript">
                    $(".seeMoreBtn").click(function () {
                        if ( $("#schoolMain").hasClass("hover") == false){
                            $("#schoolMain").addClass("hover")
                            $(".seeMoreBtn span").text("详情收起")
                            $(".seeMoreBtn i").addClass("hover")
                        }else{
                            $("#schoolMain").removeClass("hover")
                            $(".seeMoreBtn span").text("查看详情")
                            $(".seeMoreBtn i").removeClass("hover")
                        }

                    })
                </script>
                <div class="listHeader" th:if="${schoolInfo.projects ne null and #lists.toList(schoolInfo.projects).size() ne 0}">新楼盘
                    <a href="/static/houseMap.htm"><i class="map"></i>地图</a>
                    <!--                    <a href="" class="">点评<i class="up"></i></a>
                                        <a href="" class="hover">价格<i class="down"></i></a>
                                        <a href="" style="padding-right: 14px;">综合排序</a>-->
                </div>
                <!--<div class="lp_count">
                    <div class="lp_con">小二为你找到<span class="search_count" th:text="${msg}">507</span>个符合条件的楼盘</div>
                </div>-->
                <div class="cl"></div>

                <div th:if="${schoolInfo.projects ne null and #lists.toList(schoolInfo.projects).size() ne 0}"
                        class="houseInfo" th:each="house:${schoolInfo.projects}" th:id="'house'+${house.projectId}" th:onclick="${'window.open(''/house/'+house.projectId+'-'+ house.type +'.htm'')'}" >
                    <div class="img">
                        <a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm'}" target="_blank">
                            <img th:src="${house.pic}" th:alt="${house.projectName}"/>
                            <div class="vrs" th:if="${house.pan ne null}"><i></i><b></b></div>
                        </a>
                        <!--                    <span class="images">1</span>
                                            <span class="video">1</span>-->
                    </div>
                    <ul class="info">
                        <li>
                            <b><a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm'}" target="_blank"  th:text="${titleType ne '5' and house.type eq '2'?' [别墅] ':' '}+${house.projectName}"></a></b>
                            <a th:href="${'/house/'+house.projectId+'-'+ house.type +'.htm#xmpj'}" target="_blank"  th:if="${!#lists.isEmpty(house.mediaCount)}">
                                <th:block th:if="${house.projectStatus ne '3'}" th:each="media:${house.mediaCount}" th:text="${'（'+media.commentNum+'条点评）'}"></th:block>
                            </a>
                            <span th:class="${house.vip eq '1'}?'vip':''"></span>
                            <span th:class="${house.activityId ne null and house.activityId ne ''}?'rob':''"></span>
                        </li>
                        <li>
                            <th:block th:each="layout,i:${house.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block>
                            <th:block th:each="layout,i:${house.layout}" th:if="${i.index &gt; 0}" th:text="${'&nbsp '+layout.RoomType+'居'}"></th:block><img class="imgG" src="/imagese/Geng.png" alt="">
                            <th:block th:each="area:${house.area}" th:text="${#numbers.formatInteger(area.minArea,1)+'-'+#numbers.formatInteger(area.maxArea,1)+'㎡'}"></th:block>
                            <th:block th:if="${#lists.isEmpty(house.area) and #lists.isEmpty(house.layout)}" th:text="${'暂无资料'}"></th:block>
                            <!--1居/2居/3居 - 57~83㎡-->
                        </li>
                        <li>
<!--                            <th:block th:text="${#strings.abbreviate('['+house.regionName+']'+house.projectAddress,34)}"></th:block>-->
                            <th:block th:text="${house.regionName}"></th:block><img class="imgG" src="/imagese/Geng.png" alt=""> <th:block th:text="${house.projectAddress}"></th:block>

                        </li>
                        <li>
                            <!--<span class="label sell">在售</span>-->
                            <!--<span class="label upcoming">待售</span>-->
                            <span class="label" th:each="features,i:${#strings.setSplit(house.features,' ')}" th:if="${i.index &lt; 4}" th:text="${features}"></span>
                        </li>
                    </ul>
                    <ul th:class="${house.mPrice == null ? 'prices discontain': 'prices'}">
                        <th:block th:if="${house.projectStatus ne '3'}">
                        <li><th:block th:if="${house.mPrice != null}" th:text="${house.mPrice.priceType}"></th:block>
                            <span th:if="${house.mPrice != null}"><b>
                                <th:block  th:text="${#numbers.formatInteger(house.mPrice.priceMoney,1) }"></th:block>
                            </b >元/㎡</span>
                            <span th:if="${house.mPrice == null}"><b class="determined">待定</b></span>
                            <ul th:if="${#lists.size(house.price) == 1 and !#lists.isEmpty(house.price)}" class="one">
                                <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                    <th:block th:text="${price.name+price.showPrice}"></th:block>
                                </li>
                                <i></i>
                            </ul>
                            <ul th:if="${#lists.size(house.price) == 2 and !#lists.isEmpty(house.price)}" class="min">
                                <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                    <th:block th:text="${price.name+price.showPrice}"></th:block>
                                </li>
                                <i></i>
                            </ul>
                            <ul th:if="${#lists.size(house.price) &gt; 4 and !#lists.isEmpty(house.price)}" class="max">
                                <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                    <th:block th:text="${price.name+price.showPrice}"></th:block>
                                </li>
                                <i></i>
                            </ul>
                            <ul th:if="${#lists.size(house.price) &gt; 2 and #lists.size(house.price) &lt; 5 and !#lists.isEmpty(house.price)}" class="middle">
                                <li th:each="price:${house.price}" th:class="${price.type eq '起价'?'rise':'uniform'}" >
                                    <th:block th:text="${price.name+price.showPrice}"></th:block>
                                </li>
                                <i></i>
                            </ul>
                        </li>
                        <li class="phone" th:if="${house.projectStatus ne '3'}"><i class="icon_iphone"></i><th:block th:utext="${#strings.toString(house.sortelTel).replace('转','<b> 转 </b>')}"></th:block></li>
                        <li>
 <!--                           <a class="collect soucang" target="_blank" data-toggle="modal" href="#login">收藏</a>
                            <input type="hidden" id="glzs" th:value="${session.sessionId}" />-->
                        </li>
                        </th:block>
                        <th:block th:if="${house.projectStatus eq '3'}">
                            <li >
                                <b class="solded">新房已售完</b>
                            </li>
                            <li class="phone">
                                <a th:href="${'/saleHouses/search='+house.projectName}" target="_blank">请查看二手房源></a>
                            </li>
                        </th:block>
                        <li class="db-li">
                            <!--                 <a class="collect soucang" target="_blank" data-toggle="modal" href="#login">收藏</a>
                                             <input type="hidden" id="glzs" th:value="${session.sessionId}" />-->
                            <p class="contrast" th:value="${house.projectId}" >对比</p>
                            <p class="contrastNo" style="display: none;color: red;">对比</p>
                            <input type="hidden" class="cHouseId" th:value="${house.projectId}"/>
                            <input type="hidden" class="cHouseName" th:value="${house.projectName}"/>
                            <input type="hidden" class="cHouseType" value="1"/>
                        </li>
                    </ul>
                    <div class="cl"></div>

                    <div class="rank_brand_enter" th:if="${!#strings.isEmpty(house.brandId) or !#maps.isEmpty(house.rank)}">
                        <!--品牌馆入口-->
                        <div th:if="${!#strings.isEmpty(house.brandId)}" class="brand_info">
                            <a th:href="${'/brandCompany/'+house.brandId+'.htm'}" target="_blank">
                                <i class="barnd_icon"></i>
                                <span th:text="${house.brandName}"></span>
                            </a>
                        </div>
                        <!--品牌馆入口-->
                        <!--排行榜入口-->
                        <div class="ranks_enter" th:if="${!#maps.isEmpty(house.rank)}">
                            <a th:href="${'/projectRank/'+house.rank.rankType}" target="_blank">
                                <i class="rank_icon"></i>
                                <p>
                                    <span th:text="${house.rank.rankTypeName+'第'+house.rank.rankCount+'名'}"></span>
                                </p>
                            </a>
                        </div>
                        <!--排行榜入口-->
                    </div>
                </div>
                <div class="cl"></div>
                <div class="contentMain schooolK" id="saleCol">
                    <div class="listHeader" th:if="${schoolInfo.secHouses ne null and #lists.toList(schoolInfo.secHouses).size() ne 0}">
                        二手房房源
                        <ul id="mapTab">
                            <!--<li id="tabMap"><a href="/static/houseMap.htm?type=3"><i></i>地图</a></li>-->
                            <!--<li id="tabList" class="hover"><i></i>列表</li>-->
                        </ul>
                    </div>
                    <!--以下为list形式-->
                    <div id="schoool-sec" class="house_left" th:if="${schoolInfo.secHouses ne null and #lists.toList(schoolInfo.secHouses).size() ne 0}">
                        <th:block th:each="sh,sequence:${schoolInfo.secHouses}">
                            <div class="inf" th:onclick="${'window.open(''/salehouse/'+sh.houseId+'.htm'')'}">
                                <a th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank"></a>
                                <a th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank" class="infLeft">
                                    <!--<i  th:if="${sh.picNum ne null and #strings.toString(sh.picNum).length() gt 1 or sh.picNum gt '4'}" th:text="${sh.picNum}"></i>-->
                                    <img th:src="${#strings.isEmpty(sh.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sh.pic}"
                                         th:alt="${sh.title}" />
                                    <!--VR and 视频都存在 -->
                                    <s class="listIconK" th:if="${#strings.toString(sh.PanID) ne null and #strings.toString(sh.mediaID) ne null }">
                                        <s class="vrListIcon" th:if="${#strings.toString(sh.PanID) ne null}"></s>
                                        <s class="videoListIcon" th:if="${sh.mediaID ne null}"></s>
                                    </s>
                                    <!--VR存在 -->
                                    <s class="listIconK" th:if="${#strings.toString(sh.PanID) ne null and #strings.toString(sh.mediaID) eq null }">
                                        <s class="vrListIcon" th:if="${#strings.toString(sh.PanID) ne null}"></s>
                                    </s>
                                    <!--视频存在 -->
                                    <s class="listIconK" th:if="${#strings.toString(sh.PanID) eq null and #strings.toString(sh.mediaID) ne null }">
                                        <s class="videoListIcon" th:if="${sh.mediaID ne null}"></s>
                                    </s>
                                </a>
                                <div class="infCtn">
                                    <a class="newHouseListTitle" th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank">
                                        <!--真实房源-->
                                        <div style="margin-top: 2px; margin-right: 8px" th:if="${#maps.containsKey(sh,'realEstateStatus') and #strings.equals(sh?.realEstateStatus,'100')}">
                                            <img src="https://static.fangxiaoer.com/web/images/sy/download/zxzs_icon.jpg" alt="">
                                        </div>
                                        <div th:text="${sh.title}" th:title="${sh.title}"></div>
                                        <!-- 增加安心好房icon -->
                                        <div class="anxin_icon" th:if="${#strings.equals(sh.anXuan,'-1')}">
                                            <div class="anxin_icon_l">安心</div>
                                            <div class="anxin_icon_r">好房</div>
                                        </div>

                                        <i class="listIconBidPrice" th:if="${#strings.toString(sh.auction) eq '1'}"></i>
                                        <i class="listIconIstop" th:if="${#strings.toString(sh.auction) ne '1' && #strings.toString(sh.stickOrder) eq '-1'}"></i>
                                    </a>
                                    <div class="fourSpan">
												<span>
													<th:block th:text="${sh.room+'室'+sh.hall+'厅'+sh.toilet+'卫'}"></th:block>
												</span>
                                        <span>
													<th:block th:text="${#strings.toString(sh.area).contains('.')? #strings.toString(sh.area).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.area}+'m²'"></th:block>
												</span>
                                        <span th:if="${sh.totalFloorNumber ne null and sh.totalFloorNumber ne '' }" th:text="${sh.floorDesc+'/'+sh.totalFloorNumber+'层'}">
												</span>
                                        <span>
													<th:block th:text="${sh.forward}"></th:block>
												</span>
                                        <span th:if="${sh.buildDate ne null and sh.buildDate ne ''}">
													<th:block th:text="${sh.buildDate+'年建筑'}"></th:block>
												</span>

                                    </div>
                                    <p class="houseAddress" th:if="${!#strings.isEmpty(sh.regionName) and !#strings.isEmpty(sh.subName) and !#strings.isEmpty(sh.plantName) and !#strings.isEmpty(sh.address)}">
                                        <s th:if="${ !#strings.isEmpty(sh.subName)}" class="houseAddressSpance">
                                            <a th:href="${'/saleVillages/'+sh.subID+'/index.htm'}" target='_blank' th:text="${sh.subName}"></a>
                                        </s>
                                        <i class="gang"></i>
                                        <s th:if="${!#strings.isEmpty(sh.regionName) and !#strings.isEmpty(sh.plantName) and !#strings.isEmpty(sh.address)}">
                                            <a th:href="${'/saleHouses/r'+sh.regionId}" th:text="${sh.regionName}"></a>-
                                            <a th:href="${'/saleHouses/j'+sh.PlatId+'-r'+sh.regionId}" th:text="${sh.plantName}"></a>-
                                            <th:block th:text="${sh.address}"></th:block>
                                        </s>
                                    </p>
                                    <div class="bottomtext">
                                        <div class="houseItemIcon">
                                            <div class="isGoodHouseIcon" th:if="${sh.isGoodHouse eq '-1'}"></div>
                                            <th:block th:if="${sh.houseTrait ne null and sh.houseTrait ne ''}">
														<span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(sh.houseTrait).split(',')}" th:if="${i.count le 3}"
                                                              th:text="${item}"></span>
                                            </th:block>
                                        </div>
                                        <span class="personShow">
                                                <div th:if="${sh.memberType eq '经纪人'}">
                                                    <div class="agentShowImg"><img th:if="${!#strings.isEmpty(sh.Avatar)}" th:src="${sh.Avatar}" alt=""></div>
                                                    <div class="agentShowImg"><img th:if="${#strings.isEmpty(sh.Avatar)}" src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png"
                                                                                   alt=""></div>
                                                    <i>
                                                        <th:block th:text="${sh.agency}"></th:block>
                                                        <th:block th:if="${!#strings.isEmpty(sh.spanTime)}" th:text="${sh.spanTime+'更新'}"></th:block>
                                                    </i>
                                                    <i>
                                                        <th:block th:if="${!#strings.isEmpty(sh.IntermediaryName)}" th:text="${sh.IntermediaryName}"></th:block>
                                                    </i>
                                                    <!-- 安心经纪人标识 -->
                                                    <i th:if="${#strings.equals(sh.anXuan,'-1')}">
                                                        <img src="https://static.fangxiaoer.com/web/images/sy/anxin/renzheng_iocn.png" style="width: 14.6px;height: 14.6px;">
                                                    </i>
                                                </div>
                                                <div th:if="${sh.memberType eq '个人'}">
                                                    <img src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
                                                    <i>
                                                        <th:block th:text="${sh.memberType}"></th:block>
                                                    </i>
                                                </div>
                                            </span>
                                    </div>
                                </div>
                                <div class="infRight">
                                    <p class="infRightPriseM" th:if="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')}"
                                       th:text='面议'></p>
                                    <p class="infRightPrise" th:if="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}">
                                        <th:block th:text="${#strings.toString(sh.price).contains('.')? #strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.price}"></th:block>
                                        <i th:text="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')?'':'万'}"></i>
                                    </p>
                                    <p th:if="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}" th:text="${#strings.toString(sh.unitPrice).contains('.')?#strings.toString(sh.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):sh.unitPrice}+'元/m²'"
                                       th:style="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}?'display:block':'display:none'">
                                    </p>
                                </div>
                            </div>
                        </th:block>
                    </div>

                    <div class="cl"></div>
                </div>
                <!--没有学校房源的时候-->
                <div class="noSchoolList" th:if="${(schoolInfo.secHouses eq null or #lists.toList(schoolInfo.secHouses).size() eq 0) and (schoolInfo.projects eq null or #lists.toList(schoolInfo.projects).size() eq 0)}">
                    <img class="noSchoolListImg" src="https://static.fangxiaoer.com/web/images/ico/noHousea.jpg" alt="">
                </div>
                <div th:include="fragment/fragment :: list_bottom"></div>
                <div class="cl">
                </div>
                </div>
            <!--left end-->
            <div id="right">
                <!--<div th:include="fragment/fragment :: right_order"></div>-->
                <div th:include="fragment/fragment :: right_school" th:with="schoolList= 1" ></div>
            </div>
            <div class="cl">
            </div>
            </div>

        </div>
    </div>
    <div th:include="fragment/fragment::commonFloat" th:with="commonType=1"></div>
    <div th:include="fragment/fragment:: footer_seo"></div>
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <!--<div th:include="fragment/fragment::common_meiqia"></div>-->
</body>
<script>
    $("a,.contrast,.contrastNo").click(function() {
        event.stopPropagation();
    });

    /*热搜榜*/
    $(".crankList").eq(0).hide();
    $(".crankDetails").eq(0).show();

    $(".crankList").hover(function () {
        // $(".crankList").show();
        $(this).parent().parent().parent().find(".crankList").show();
        $(this).hide();
        $(this).parent().parent().parent().find(".crankDetails").hide();
        $(this).parent().find(".crankDetails").show();
    })

</script>
<script src="/js/house/houseMap.js"></script>
</html>
