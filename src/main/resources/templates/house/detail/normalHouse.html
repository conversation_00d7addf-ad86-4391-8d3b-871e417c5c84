<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">

<head>
    <title th:text="${#strings.toString(houseInfo.projectName).replace('·','')+'_沈阳'+#strings.toString(houseInfo.projectName).replace('·','')+'楼盘详情 - 房小二网'}"></title>
    <meta name="keywords"
          th:content="${#strings.toString(houseInfo.projectName).replace('·','')+',沈阳'+#strings.toString(houseInfo.projectName).replace('·','')+','+houseInfo.regionName+#strings.toString(houseInfo.projectName).replace('·','')+','+#strings.toString(houseInfo.projectName).replace('·','')+'楼盘户型信息'}">
    <meta name="description"
          th:content="${'沈阳'+#strings.toString(houseInfo.projectName).replace('·','')+',房小二网为你提供'+#strings.toString(houseInfo.projectName).replace('·','')+'最新价格、地址、优惠、交通、户型、周边配套、实景图等楼盘信息。查找'+houseInfo.regionName+#strings.toString(houseInfo.projectName).replace('·','')+'房价走势、户型信息尽在房小二网!'}">
    <meta name="mobile-agent"
          th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112"/>-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/main2017.css" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/newSyViewPzs.css?v=2022">
    <!--    <link rel="stylesheet" href="/css/newSyViewPz.css?v=20190111">-->

    <!--sdk-->
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js?v=20180608"></script>
    <link href="https://static.fangxiaoer.com/js/tc/tc.css?t=20180502" rel="stylesheet" type="text/css"/>
    <script src="/js/AjaxforJquery.js" type="text/javascript"></script>
    <script src="/js/jquery.cookie.js" type="text/javascript"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/sy/formulate/jquery.datetimepicker.css?v=20180522"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/house/Freefone1.css?v=20180522"/>
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522"/>-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/indexBB.css?v=20180522"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/house/houseAnswer.css?v=20180522"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?v=20180522"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/house/userEvaluatePX.css?v=20180911"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/Housekeeper/condoToursPrivate.css"/>


    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/house/normalHouse.css?v=20190328"/>
    <!--<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/skin/functional.css">-->
    <!-- include flowplayer -->
    <!--<script src="https://static.fangxiaoer.com/js/new_video/flowplayer.min.js"></script>-->
    <script src="https://static.fangxiaoer.com/js/new_video/video.min.js"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/video-js.min.css">
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/depthresolution.css?v=20190219">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynatown.css?v=20200417" rel="stylesheet" type="text/css"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/appointment.css?v=20180522" rel="stylesheet" type="text/css">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/payment.css" rel="stylesheet" type="text/css"/>
    <script src="https://cdn.bootcss.com/js-cookie/2.2.1/js.cookie.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $(".various2").fancybox({
                'autoScale': false,
                'transitionIn': 'elastic',
                'transitionOut': 'elastic',
                'type': 'image'
            });
        });
    </script>
    <script type="text/javascript">
        //定位弹出
        $(function () {
            $(".drow").hover(function () {
                $(this).find(".i-list").stop(true, false).fadeTo('fast', 1);
                $(this).addClass("hover");
            }, function () {
                $(".i-list").fadeTo('fast', 0).hide(0);
                $(".drow").removeClass("hover");
            });
            $("#loginClose").click(function () {
                $("#login").hide();
                $("#loginzhezhao").hide();
                $(".error_info").text('')
                $("#txt_LoginName").val('')
            });
        })
        //右侧下拉判断
    </script>
    <script type="text/javascript">
        $(function () {
            $("input[type='radio'][name='ask'][value='1']").attr("checked", "checked");
            $(".zytc_pic").mouseover(function () {
                $(".various5").show();
            })
            $(".zytc_pic").mouseout(function () {
                $(".various5").hide();
            })
        });
    </script>
    <script language="javascript">
        $(function () {
            $(".tt1").html($("#htype-dropdown li:eq(0)").html());
            $("#currentChooseArea").val($("#htype-dropdown li:eq(0)").attr("data-code"))
            $('#totalHousePrice').attr('total-price', Math.round($("#currentBuildingPirce").val() * $("#htype-dropdown li:eq(0)").attr("data-code") / 10000))
        })
    </script>
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
    <link href="https://static.fangxiaoer.com/js/jsq/house/index.css?v=20180522" rel="stylesheet" type="text/css"/>
    <link href="https://static.fangxiaoer.com/js/jsq/house/framework.css?v=20180522" rel="stylesheet"/>
</head>
<style>
    .sy-ZKclear {
        border: none
    }

    .sy-ZKclear-ararow {
        position: absolute;
        width: 71px;
        height: 28px;
        margin-left: 1071px;
        margin-top: 13px;
        cursor: pointer
    }
    .title{position: relative}
    .sy-dynatown-ararow{
        position: absolute;
        right: 12px;
        top: 16px;}
    .btn_tc{width: 330px;line-height: 45px;display: block;margin: 0px auto 5px;background: #ff6600;
        color: #FFF;font-size: 20px;text-align: center;}
    .smore{ position: absolute; width: 122px; top: 33px; right: 18px;
        font-size: 14px; font-family: PingFang SC; font-weight: 500; color: #FF6600; cursor: pointer;}
    .smore:hover{ color: #0F5AFF !important;}
    .smore i{ width: 1rem; height: 1rem; display: inline-block; position: absolute; top: 0px; right: -8px;}

    .vrs{ width: 80px; height: 80px; position: absolute; top: 0; bottom: 0; left: 0; right: 0; margin: auto;}
    .vrs i{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 3; display: block; background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;}
    .vrs b{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 1; display: block;  border-radius: 50%; background-color: rgba(0,0,0,0.5);}
    .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
        background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    .infoa li{ margin-bottom: 25px !important;}
    .resethe{ margin-top: 25px;}
    .resethe2{ width: 380px; line-height: 50px; text-align: center; color: #FF5403; font-size: 16px; border: 1px solid #FF5403; border-radius: 5px; cursor: pointer; display: inline-block; padding-left: 26px;   box-sizing: border-box; position: relative;}
    .resethe2 i{ display: inline-block; width: 18px; height: 18px; background-image: url('https://static.fangxiaoer.com/web/images/brand/sell.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; position: absolute; top: 15px; left: 148px;}
    .resetatop{ top: -8px !important;}








     .houseSaleDT{
         border: 1px solid #ededed !important;
     }
    .normalHouseMain{
        width: 1170px !important;
        border: none !important;
    }
    .houseSaleDT .normalHouseMain .xstd{
        width: 1170px !important;
    }
    .project_main{
        bottom: 5px !important;
    }

    .info li a:hover table{width: auto;}
    .info li a:hover table td{padding-right: 10px}

    .sy-dynatown .sy-single{
        width: 20% !important;

        display: block !important;
    }
    .new-sy-single:nth-child(4n) .news-name{
        border-right: 1px solid #eee !important;
    }
    .new-sy-single:nth-child(5n) .news-name{
        border-right: none;
    }
    .new-sy-single:nth-child(6n){
        display: none;
    }
    .sy-dynatown .news-name{
        width: 139px !important;
    }
    .news-touxiang{
        margin-left: 15px !important;
        margin-right: 15px !important;
    }
    .sy-dynatown .news-name>span{
        white-space: nowrap;
    }
    .new-sy-single:last-child .news-name {
        border-right: none !important;
    }

</style>
<link href="https://static.fangxiaoer.com/web/images/sy/selection/g_sharing.css?v=2" rel="stylesheet" type="text/css"/>
<script src="https://static.fangxiaoer.com/web/images/sy/selection/stars.js"></script>
<script th:inline="javascript">
    $(document).ready(function(){
        function jqrcodeFn(d,w,h){
            var qrWidth = w;
            var qrHeight = h;
            var logoQrWidth = qrWidth / 4;
            var logoQrHeight = qrHeight / 4;
            var g_a='#'+d;
            var g_b='#'+d+' canvas';
            var g_c='.'+d;
            var g_qurl= [[${mobileSelectionUrl}]] //上线后 改为m站小二甄选楼盘评测信息页
            $(g_a).qrcode({
                render: "canvas",    //设置渲染方式，有table和canvas
                text: g_qurl,
                width: qrWidth, //二维码的宽度
                height: qrHeight //二维码的高度
            })
            $(g_b)[0].getContext('2d').drawImage($("#g_clogo")[0], (qrWidth - logoQrWidth) / 2, (qrHeight - logoQrHeight) / 2, logoQrWidth, logoQrHeight);
            $(g_c).show()
        }
        jqrcodeFn('g_qrcode','64','67')
        jqrcodeFn('g_hcode','130','130')

        $(".g_dhv").mouseover(function () {
            $(".g_hdcode").show()
        })
        $(".g_dhv").mouseleave(function () {
            $(".g_hdcode").hide();
        })
    })
</script>

<body class="w1210">
<img src="https://static.fangxiaoer.com/web/images/sy/selection/logo.png" id="g_clogo" style="display: none;"/>


<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>
<div class="cl"></div>
<input id="pigeSize" type="hidden" value="3"/>
<input type="hidden" id="sessionId" th:value="${session.sessionId}">
<input type="hidden" id="mobile" th:value="${session.phoneNum}">
<input type="hidden" id="memberType" th:value="${session.memberType}">
<input type="hidden" id="projectType" th:value="${projectType}">
<input type="hidden" id="videoTabId" th:value="${houseInfo.videoTabId}">
<div th:include="house/detail/fragment_menu::menu" th:with="type=1"></div>
<!--<div th:include="house/detail/fragment_activity::activity"></div>-->
<div th:include="house/detail/fragment_activity::viewActivity"></div>
<div th:include="house/detail/fragment_activity::projectPay"></div>

<div th:include="house/detail/fragment_activity::subsidyActivity"></div>
<div class="newHouseviewPz">
    <!--抢优惠-->
    <input type="hidden" th:value="${houseInfo.coupon.hasCoupon}" id="hasCoupon">
    <script th:inline="javascript">
        $(document).ready(function () {
            var hasCoupon = $("#hasCoupon").val()
            var hasCouponNum = parseInt($("#hasCouponNum").val())
            var robId ;
            if(hasCoupon == '1'){
                robId = $("#couponId").val()
                var robscene = 'pay,' + robId+','+hasCouponNum;
                $.ajax({
                    type: "POST",
                    async: false,
                    url:  "/getNewWxCode",
                    data:{
                        "scene": robscene,
                    },
                    dataType : 'json',

                    success: function (data) {
                        console.log('图片数据')
                        console.log(data)
                        let robimg = data.img
                        $("#getNewWxCode").attr('src',"data:text/html;base64,"+robimg);  // 给太阳码图片赋值
                    }
                });
            }else{
                console.log(66666)
            }
        })

    </script>
    <div class="w coupons" style="    margin-top: 20px;" th:if="${houseInfo.coupon.hasCoupon eq '1'}">
        <input type="hidden" th:value="${houseInfo.coupon.id}" id="couponId">
        <input type="hidden" th:value="${houseInfo.coupon.payAmount}" id="hasCouponNum">

        <div class="coupons_l">
            <div class="c_l_1">
                <h2 th:text="${houseInfo.projectName}"></h2>
                <h3>抢优惠</h3>
            </div>
            <div class="c_l_2">
                <p th:text="${houseInfo.coupon.payTitle}"></p>
                <p th:text="${'抢券时间：'+houseInfo.coupon.startTime+'-'+houseInfo.coupon.endTime}"></p>
            </div>
        </div>
        <div class="coupons_r">
            <img id="getNewWxCode" src="" alt="" style="border-radius:0">
            <p>扫码参与活动</p>
        </div>
    </div>

    <div class="w newHouseBasicInfo" th:fragment="baseInfo">
        <!--基本信息-->
        <div class="pic" id="PicSlide">
            <div th:if="${houseInfo.hasLabel eq '1'}" class="instantDetail"><img th:src="${houseInfo.labelContent.advertisePC}" alt=""></div>
            <ul class="img">
                <li th:each="p,pi:${houseInfo.pic}" th:if="${pi.index lt 5}" th:style="'display:' + ${pi.index eq 0 ? 'list-item;' : 'none'}">
                    <th:block th:if="${pi.index == 0 and houseInfo.liveInfo.isLive eq '1'}">
                        <a th:href="${'/liveDetail/' + houseInfo.liveInfo.liveId + '.htm'}">
                            <div class="zbkfDetailBTn"></div>
                            <img th:src="${houseInfo.liveInfo.livePic}" th:alt="${houseInfo.liveInfo.liveTitle}"/>
                        </a>
                    </th:block>
                    <th:block th:if="${houseInfo.isPan ne '0' and ((houseInfo.liveInfo.isLive eq '0' and pi.index == 0) or (houseInfo.liveInfo.isLive eq '1' and pi.index == 1))}">
                        <a th:href="${'/house/'+projectId + '-' + projectType + '/pic720.htm'}">
                            <img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
                            <div class="vrs" th:if="${p.title eq '全景'}"><i></i><b></b></div>
                        </a>
                    </th:block>
                    <th:block th:if="${houseInfo.videoTabId ne '0' and ((houseInfo.liveInfo.isLive eq '0' and houseInfo.isPan eq '0' and pi.index == 0) or
                     (houseInfo.liveInfo.isLive eq '1' and houseInfo.isPan eq '0' and pi.index == 1) or
                      (houseInfo.liveInfo.isLive eq '0' and houseInfo.isPan ne '0' and pi.index == 1) or
                       (houseInfo.liveInfo.isLive eq '1' and houseInfo.isPan ne '0' and pi.index == 2))}">
                        <div id="player" style="width: 600px;height: 400px">
                            <video id="my-video" class="video-js vjs-big-play-centered" controls preload="auto"
                                   width="600px;" height="400px" th:poster="${p.url}" data-setup="{}">
                                <source th:src="${houseInfo.mobileVideoUrl}">
                            </video>
                        </div>
                    </th:block>
                    <th:block th:if="${((houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId eq '0' and houseInfo.isPan eq '0') and (pi.index == 0 or pi.index == 1 or pi.index == 2))
                                    or (((houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId eq '0' and houseInfo.isPan ne '0') or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId eq '0' and houseInfo.isPan eq '0')
                                    or (houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0')) and (pi.index == 1 or pi.index == 2))
                                    or (((houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0') or
                                    (houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0')
                                    or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId eq '0' and houseInfo.isPan ne '0')) and (pi.index == 2))}">
                        <a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
                            <img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
                        </a>
                    </th:block>
                    <th:block th:if="${pi.index != 0 and pi.index != 1 and pi.index != 2}">
                        <a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
                            <img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
                        </a>
                    </th:block>
                </li>
            </ul>
            <div class="thumb">
                <ul>
                    <li th:each="p,pi:${houseInfo.pic}" th:if="${pi.index lt 5}">
                        <img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
                        <p th:text="${((houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0' and houseInfo.liveInfo.isLive eq '0' and pi.index == 0)
                        or (houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0' and houseInfo.liveInfo.isLive eq '1' and pi.index == 1) or
                         (houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0' and houseInfo.liveInfo.isLive eq '0' and pi.index == 1) or
                          (houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0' and houseInfo.liveInfo.isLive eq '1' and pi.index == 2)) ? '视频' : p.title}"></p>
                        <em></em>
                    </li>
                </ul>
                <div class="now-status" style="left: 0px;">
                </div>
            </div>
            <div class="cl">
            </div>
            <div th:if="${#lists.size(houseInfo.pic) gt 1}" id="PicslidePrev" style="z-index: 10000"></div>
            <div th:if="${#lists.size(houseInfo.pic) gt 1}" id="PicslideNext" style="z-index: 10000"></div>
            <script>
                // 视频对象
                var videoTabId = $("#videoTabId").val()
                if (videoTabId != 0 ){
                    var slideVideo = videojs("my-video");

                }

                // 小图hover切换
                $(".thumb li").hover(function () {
                    var dex = $(this).index();
                    $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                    $(".now-status").css("left", dex * 122 + "px");
                    slideVideo.pause();
                })

                $(function () {
                    // 下
                    $("#PicslideNext").click(function () {
                        var dex = $("#PicSlide").find(".img").find("li[style='display: list-item;']").index() + 1;
                        dex = dex >= $("#PicSlide .img>li").length ? 0 : dex;
                        $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                        $(".now-status").css("left", dex * 122 + "px");
                        slideVideo.pause();
                    })
                    // 上
                    $("#PicslidePrev").click(function () {
                        var dex = $("#PicSlide").find(".img").find("li[style='display: list-item;']").index() - 1;
                        dex = dex < 0 ? ($("#PicSlide .img>li").length - 1) : dex;
                        $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                        $(".now-status").css("left", dex * 122 + "px");
                        slideVideo.pause();
                    })
                })
            </script>
        </div>
        <!--左 end-->
        <div th:class="${#strings.toString(houseInfo.projectStatus) eq '3' ? 'right houseSellOut' : 'right'}">
            <div th:if="!${#strings.toString(houseInfo.projectStatus) eq '3'}" class="price">
                <p>参考价格</p>
                <ul th:if="${#lists.size(houseInfo.price) lt 4}" class="type-Btn">
                    <s th:if="${houseInfo.price} == null or ${#arrays.length(houseInfo.price)} == 0"
                       style="color:#ff5200">待定</s>
                    <li th:each="price:${houseInfo.price}" th:class="${price.type eq '均价'}?'jun jv':'qi'">
                        <span th:text="${price.name}"></span><i th:text="${#numbers.formatInteger( price.money,3)}"></i>
                        <th:block
                                th:text="${#strings.replace(price.showPrice, #numbers.formatInteger( price.money,3), '')}"></th:block>
                        <em></em>
                    </li>
<!--                    <p onclick="showUnuseCode(1)" value="" class="getChangePzBtn" th:if="${houseInfo.projectStatus ne '3'}" style="margin-top: 6px;"><i></i>获取最新价格变动</p>-->
                    <div class="cl"></div>
                </ul>
                <ul th:if="${#lists.size(houseInfo.price) gt 3}" class="type-Btn">
                    <s th:if="${houseInfo.price} == null or ${#arrays.length(houseInfo.price)} == 0">待定</s>
                    <li th:each="price,i:${houseInfo.price}" th:if="${i.index lt 1}"
                        th:class="${price.type eq '均价'}?'jun':'qi'">
                        <span th:text="${price.name}"></span><i th:text="${#numbers.formatInteger( price.money,3)}"></i>
                        <th:block
                                th:text="${#strings.replace(price.showPrice, #numbers.formatInteger( price.money,3), '')}"></th:block>
                        <em></em>
                        <em class="triangle"
                            style="background-image: url('https://static.fangxiaoer.com/web/images/sy/house/view/arrowxia.png');background-position: 0 10px"></em>
                    </li>
                    <div class="layer">
                        <li th:each="price,i:${houseInfo.price}" th:if="${i.index gt 0}"
                            th:class="${price.type eq '均价'}?'jun':'qi'">
                            <span th:text="${price.name}"></span><i
                                th:text="${#numbers.formatInteger( price.money,3)}"></i>
                            <th:block th:if="${houseInfo.projectStatus ne '3'}"
                                      th:text="${#strings.replace(price.showPrice, #numbers.formatInteger( price.money,3), '')}"></th:block>
                            <em></em>
                        </li>
                        <img src="" alt="">
                    </div>
<!--                    <p onclick="showUnuseCode(1)" value="" class="getChangePzBtn" th:if="${houseInfo.projectStatus ne '3'}" style="margin-top: 6px;"><i></i>获取最新价格变动</p>-->
                    <div class="cl"></div>
                    <script>
                        $(".price ul li:eq(0)").mouseenter(
                            function () {
                                $(".layer").show()
                            }
                        )
                        $(".price ul li:eq(0)").mouseleave(
                            function () {
                                $(".layer").hide()
                            }
                        )
                    </script>
                </ul>
                <div class="cl"></div>
            </div>
            <!--售罄-->
            <div th:if="${#strings.toString(houseInfo.projectStatus) eq '3'}" class="price">
                <div style="font-size: 22px; color: #FF5200; font-weight: bolder;">项目已售罄</div>
                <!--<p th:if="!${houseInfo.subPrice eq null} and !${#maps.isEmpty(houseInfo.subPrice)}">参考价格</p>
                <ul th:if="!${houseInfo.subPrice eq null} and !${#maps.isEmpty(houseInfo.subPrice)}">
                    <li class="jun"
                        th:if="${#strings.toString(#numbers.formatInteger( houseInfo.subPrice.price,0)) ne '0'}">
                        <span>二手房</span><i th:text="${#numbers.formatInteger( houseInfo.subPrice.price,0)}"></i>元/m²
                        &lt;!&ndash;                            <img src="https://static.fangxiaoer.com/web/images/ico/sign/price_2.gif" alt="价格">&ndash;&gt;
                        <em></em>
                        <a th:href="${'/saleVillages/'+houseInfo.subPrice.subId+'/index.htm'}" target="_blank">
                            <span class="priceCk">查看二手房源 ></span>
                        </a>
                    </li>
                    <li class="jun"
                        th:if="${#strings.toString(#numbers.formatInteger( houseInfo.subPrice.price,0)) eq '0'}">
                        <span>——</span><i></i>
                        <a th:href="${'/saleVillages/'+houseInfo.subPrice.subId+'/index.htm'}" target="_blank">
                            <span class="priceCk">查看二手房源 ></span>
                        </a>
                    </li>

                    <div class="cl"></div>
                </ul>
                <ul th:if="${houseInfo.subPrice eq null} or ${#maps.isEmpty(houseInfo.subPrice)}">
                    <li>
                        <i>新房已售完</i>
                        <em></em>
                    </li>
                    <div class="cl"></div>
                </ul>-->
                <div class="cl"></div>
            </div>
            <div class="cl"></div>
            <div th:class="${houseInfo.projectStatus eq '3'}?'info infoa':'info'">
                <ul>
                    <li>
                        <i> 主力户型</i>
                        <p th:each="layout,layindex:${houseInfo.mainLayout}" th:if="${layindex.index lt 3 }"
                           th:text="${layout.mainLayout + ' '}"></p>
                        <!--<p th:if="${!#lists.isEmpty(houseInfo.mainLayout) and #lists.size(houseInfo.mainLayout) gt 3 }">...</p>-->
                        <p th:if="${#lists.isEmpty(houseInfo.mainLayout)}">暂无主力户型</p>
                        <a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '.htm'}" class="infoIconHx"><s></s>全部户型图</a>
                    </li>
                    <div class="cl"></div>
                    <li th:if="${!#lists.isEmpty(houseInfo.openTime)}">
                        <i> 最新开盘</i>
                        <p style="color: #333;max-width: 260px"
                           th:class="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) gt 1?'':'win'}">
                            <th:block th:each="lic,licindex:${#strings.setSplit(houseInfo.openTime.get(0).dyTime,'.')}"
                                      th:text="${licindex.index==0? lic+'年':(licindex.index==1?lic+'月':lic+'日'+houseInfo.openTime.get(0).dyDesc)}"></th:block>
                        </p>
                        <div class="layer_sun" name="layer_sun"
                             th:if="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) eq 1}">
                            <div class="ms_sanjiao dt_sj"></div>
                            <th:block th:each="lic,licindex:${#strings.setSplit(houseInfo.openTime.get(0).dyTime,'.')}"
                                      th:text="${licindex.index==0? lic+'年':(licindex.index==1?lic+'月':lic+'日'+houseInfo.openTime.get(0).dyDesc)}"></th:block>
                        </div>
                        <th:block th:if="${houseInfo.projectStatus ne '3'}">
                        <a th:style="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) gt 1?'display: black':'display: none'}"
                           class="infoIcontime"><s></s>开盘时间
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody>
                                <tr>
                                    <th colspan="2" th:text="${houseInfo.projectName+'开盘时间'}"></th>
                                </tr>
                                <!--<tr>
                                    <td width="13%">开盘时间</td>
                                    <td width="37%">开盘详情</td>
                                </tr>-->
                                <tr th:each="e:${houseInfo.openTime}" style="width:400px">
<!--                                    <td th:text="${e.dyTime}"></td>-->
                                    <td th:text="${e.dyDesc}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </a>
                        <a th:unless="${houseInfo.resBrand.wayDes ne '' or (houseInfo.resBrand.Brand ne '' and houseInfo.resBrand.Brand ne '暂无资料')}"
                           th:style="${!#lists.isEmpty(houseInfo.existinfo) and #lists.size(houseInfo.existinfo) ne 0?'display: black':'display: none'}"
                           class="infoIcontime"><s></s>交房时间
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody>
                                <tr>
                                    <th colspan="2" th:text="${houseInfo.projectName+'交房时间'}"></th>
                                </tr>
                                <tr>
                                    <td width="13%">交房时间</td>
                                    <td width="37%">交房详情</td>
                                </tr>
                                <tr th:each="e:${houseInfo.existinfo}">
                                    <td th:text="${e.inTime}"></td>
                                    <td th:text="${e.description}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </a>
                        </th:block>
                        <th:block th:if="${houseInfo.projectStatus ne '3'}">
                            <a th:if="${#lists.isEmpty(houseInfo.openTime) or houseInfo.resBrand.wayDes ne '' or (houseInfo.resBrand.Brand ne '' and houseInfo.resBrand.Brand ne '暂无资料')}"
                               th:style="${!#lists.isEmpty(houseInfo.existinfo) and #lists.size(houseInfo.existinfo) ne 0?'display: black':'display: none'}"
                               class="infoIcontime"><s></s>交房时间
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tbody>
                                    <tr>
                                        <th colspan="2" th:text="${houseInfo.projectName+'交房时间'}"></th>
                                    </tr>
                                    <tr>
                                        <td width="13%">交房时间</td>
                                        <td width="37%">交房详情</td>
                                    </tr>
                                    <tr th:each="e:${houseInfo.existinfo}">
                                        <td th:text="${e.inTime}"></td>
                                        <td th:text="${e.description}"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </a>
                        </th:block>
                    </li>
                    <div class="cl"></div>
                   <!-- <li th:style="${houseInfo.resBrand.wayDes ne '' or (houseInfo.resBrand.Brand ne '' and houseInfo.resBrand.Brand ne '暂无资料')}?'display:block':'display:none'" class="view_xmts">-->
                    <li th:if="${(!#strings.isEmpty(houseInfo.baseInfo.stationName) and #strings.toString(houseInfo.baseInfo.stationName) ne '暂无资料') or (!#strings.isEmpty(houseInfo.resBrand.Brand) and #strings.toString(houseInfo.resBrand.Brand) ne '暂无资料')}" class="view_xmts">
                        <i>项目特色</i>
                        <span class="view_ts1"
                              th:style="${houseInfo.baseInfo.stationName ne '' and houseInfo.baseInfo.stationName ne '暂无资料'}?'display:block':'display:none'"><s></s>地铁
                            <div class="miaoshu dt_ms" th:if="${!#strings.isEmpty(houseInfo.resBrand.wayDes)}">
                                <div class="ms_sanjiao dt_sj"></div>
                                <th:block: th:text="${houseInfo.resBrand.wayDes}"></th:block:>
                            </div>
                            </span>
                        <span class="view_ts4"
                              th:style="${houseInfo.resBrand.Brand ne '' and houseInfo.resBrand.Brand ne '暂无资料'}?'display:block':'display:none'"><s></s>品牌
                            <div class="miaoshu pp_ms">
                                <div class="ms_sanjiao pp_sj"></div>
                             <th:block: th:text="${houseInfo.resBrand.Brand}"></th:block:>
                            </div>
                            </span>

                        <div style="clear: both"></div>
                        <div class="view_desc">
                        </div>
                    </li>
                    <li><i>项目地址</i>
                        <p th:text="${houseInfo.projectAdderss}" class="seeAdderss"></p><a href="#map"
                                                                                           class="btn_ckdt infoIconMap"><s></s>查看地图</a>
                        <div class="layer_sun" name="layer_sun">
                            <div class="ms_sanjiao dt_sj"></div>
                            <th:block th:text="${houseInfo.projectAdderss}"></th:block>
                        </div>
                    </li>
                    <div class="cl"></div>
                    <li><i>交通信息</i>
                        <p id="traffic">
                            <th:block th:text="${#strings.abbreviate(houseInfo.traffic, 39)}"></th:block>
                        </p>
                        <a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}" class="seemore"
                           th:if="${!#strings.isEmpty(houseInfo.brandId) or !#maps.isEmpty(houseInfo.rank)}">更多详细信息></a>
                        <div class="layer_sun " name="layer_sun" id="seemore">
                            <div class="ms_sanjiao dt_sj"></div>
                            <th:block th:text="${houseInfo.traffic}"></th:block>
                        </div>
                    </li>
                    <div class="cl"></div>
                    <div class="newHouseInfoRicn">
                        <a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}" class="seemore"
                           th:unless="${!#strings.isEmpty(houseInfo.brandId) or !#maps.isEmpty(houseInfo.rank)}">更多详细信息></a>
                        <div class="iconDiv"
                             th:if="${!#strings.isEmpty(houseInfo.brandId) or !#maps.isEmpty(houseInfo.rank)}">
                            <a th:if="${!#strings.isEmpty(houseInfo.brandId)}"
                               th:href="${'/brandCompany/'+houseInfo.brandId+'.htm'}" class="pinpai" target="_blank"><i
                                    class="barnd_icon barnd_icon_2024"></i><span th:text="${#strings.length(houseInfo.brandName)  gt 10 ?'品牌展示区': houseInfo.brandName}"></span></a>
                            <a th:if="${!#maps.isEmpty(houseInfo.rank)}"
                               th:href="${'/projectRank/'+houseInfo.rank.rankType}" class="bangdan"><i
                                    class="rank_icon rank_icon_2024"></i><span
                                    th:text="${houseInfo.rank.rankTypeName+'第'+houseInfo.rank.rankCount+'名'}"></span></a>
                        </div>
                        <a class="soucang" target="_blank" data-toggle="modal" href="#login" th:if="${houseInfo.projectStatus ne '3'}"><i></i>收藏</a>

                        <a class="soucang">
                            <script>
                                $(".seemore").mouseover(function () {
                                    $("#seemore").hide();
                                });
                                $("#traffic").mouseover(function () {
                                    $("#seemore").show();
                                })
                                $("#traffic").mouseleave(function () {
                                    $("#seemore").hide();
                                })

                                for (var i = 0; i < $(".info .layer_sun[name=layer_sun]").length; i++) {

                                    var text_x = $(".info .layer_sun[name=layer_sun]").eq(i).height();

                                    if (text_x > 50) {
                                        $(".info .layer_sun[name=layer_sun]").eq(i).css("line-height", "25px")
                                        $(".info .layer_sun[name=layer_sun]").eq(i).css("padding", " 7px")
                                    }

                                }
                            </script>
                            <!--<p class="contrastNo" style="display: none;color: red;">+对比</p>-->
                            <script>
                                $(function () {
                                    $(".contrast").click(function () {
                                        var txt = $(this).attr("value");
                                        var id = $(this).attr("data-id");
                                        $.ajax({
                                            type: "post",
                                            data: { houseId : id,houseName:txt, houseType : 1},
                                            url: "/addContrastHouse",
                                            //dataType: 'json',
                                            async: false,
                                            success: function (list) {
                                                window.open("https://sy.fangxiaoer.com/gotoContrastHouse")
                                                // window.open("http://*************:8082/gotoContrastHouse")

                                            },
                                        });
                                    })
                                })
                            </script>
                        </a>
                        <p class="contrast" th:value="${houseInfo.projectName}" th:data-id="${projectId}" th:if="${houseInfo.projectStatus ne '3'}">对比</p>
                    </div>
                </ul>
            </div>
            <div class="ourPhone newOurPhone" th:if="${houseInfo.projectStatus ne '3'}">
                <div class="iphcon"></div>
                <div class="tel ">
                    <div class="tel1">
                        <i>咨询电话:</i>
                        <th:block
                                th:utext="${#strings.contains(houseInfo.sortelTel,'转')?#strings.replace(houseInfo.sortelTel,'转','<b>转</b>') : houseInfo.sortelTel}"></th:block>&nbsp;
                        <em>如需帮助请拨打平台服务电话: ************</em>
                    </div>
                </div>
            </div>
            <div th:if="${houseInfo.projectStatus eq '3'}" style="position: relative;">
                <div th:class="${#lists.isEmpty(houseInfo.openTime)}?'resethe resethe2':'resethe2'"  onclick="location.href='/static/saleHouse/saleHouse.htm'">
                    <i></i>我要卖房
                </div>
                <a th:if="!${houseInfo.subPrice eq null} and !${#maps.isEmpty(houseInfo.subPrice)}"  th:class="${#lists.isEmpty(houseInfo.openTime)}?'resetatop':''" th:href="${'/saleVillages/'+houseInfo.subPrice.subId+'/index.htm'}" target="_blank" style="display: inline-block;position: absolute;right: -22px;top: 8px;">
                    <span class="priceCk">查看二手房源 ></span>
                </a>
            </div>
            <div class="cl"></div>
            <!--<div class="info_btn" th:if="${houseInfo.projectStatus ne '3'}">
                <a id='yykf'>
                    <input name='' type='button' onclick="showUsercode(1)" value='免费专车' class='button btn_1 newYykf'/>
                    &lt;!&ndash;<input name='' type='button' onclick="showUsercode(2)" value='免费通话' class='button btn_1'/>&ndash;&gt;
                </a>
            </div>-->
            <div class="cl"></div>
            <div class="project_main" th:if="${houseInfo.projectStatus ne '3'}">
                <img id="project_image" src=""/>
                <p style="font-size: 12px">微信扫码打电话</p>
            </div>
            <div class="cl">
            </div>
            <script th:inline="javascript">
                /*<![CDATA[*/
                var scene = 'newTel,' +[[${projectId}]]+'-'+[[${projectType}]]+',1,' + [[${#strings.contains(houseInfo.sortelTel,'转')?#strings.replace(houseInfo.sortelTel,'转', ',') : houseInfo.sortelTel}]];
                var img = "";
                var sss;
                $.ajax({
                    type: "GET",
                    async: false,
                    url:  "/getWxACode",
                    data:{"scene": scene},
                    dataType : 'json',
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success: function (data) {
                        img = data.img;
                        sss = data;
                    }
                });
                $("#project_image").attr("src","data:text/html;base64,"+img);
                /*]]>*/
            </script>
        </div>
        <div class="cl"></div>
    </div>
    <!--顶部end-->
    <!--小二甄选-->
    <div class="g_ezx" th:style="${#strings.toString(selectionInfo.selectionState) eq '1'}?'':'display: none'">
        <div class="g_elogo"></div>
        <div class="g_etag">
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i1.png"><span>品牌</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i2.png"><span>交通</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i3.png"><span>潜力</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i4.png"><span>配套</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i5.png"><span>环境</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i6.png"><span>户型</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i7.png"><span>物业</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i8.png"><span>性价比</span></div>
        </div>
        <div class="g_etxt"><i>真实房源</i><i>扫码查看</i></div>
        <div class="g_qrcode"><i id="g_qrcode"></i><!--<em></em>--></div>
    </div>
    <div class="cl" style="margin-top: 30px"></div>
    <!--置业顾问-->

    <div class="" th:if="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
        <div class="newHouseViewChunk">
            <div class="title">
                <P>经纪人</P>

<!--                <div class="sy-dynatown-ararow">-->
<!--                    <span id="up" style="transform: rotate(180deg)"></span>-->
<!--                    <span id="down"></span>-->
<!--                </div>-->
            </div>
            <div class="w sy-dynatown">
                <div th:each="agent,h:${houseInfo.agents}"  th:if="${h.index lt 5}"   class="sy-single new-sy-single">
                    <div class="news-touxiang">
                        <img th:src="${agent.agentPic}" th:alt="${agent.memberName}"/>
                        <div class="On-line">
                            <p>在线</p>
                            <i class="On-line-icon"></i>
                        </div>
                    </div>
                    <div class="news-name">
                        <span th:text="${agent.memberName}"></span>
                        <div class="name-btn">
                            <a th:href="${#session?.getAttribute('sessionId') ne null?'/im/'+ projectId + '-0-' +agent.memberId:('#login')}"  th:data-toggle="${#session?.getAttribute('sessionId') ne null?'':'modal'}" target="_blank">
                                <span class="liaogbei-btn">咨询</span>
                            </a>
                            <div class="call-btn" th:data="${agent.memberId}">
                                <span class="show-call">打电话</span>
                                <div class="show-ewm">
                                <img th:id="${'agent_mobile'+agent.memberId}"  >
                                <p>微信扫码打电话</p>
                                <p th:text="${#strings.replace(agent.unionTel,',','转')}"></p>
                                    <input th:id="${'agent_phone'+agent.memberId}"  type="hidden" th:value="${'tel,'+projectId+'-'+projectType+',1,'+agent.unionTel}"/>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <script src="https://static.fangxiaoer.com/js/new4-dynatown.js" type="text/javascript"></script>
    </div>
    <!--认购入口-->
    <div id="subscrip" th:include="activity/subscription::subscription"></div>
    <!--免费看房团-->
    <div class="houseKF" th:if="${!#maps.isEmpty(houseInfo.condor)}">
        <div class="checking">
            <p>免费看房团</p>

        </div>
        <div class="KFcontent">
            <ul>
                <li class="imgCar"><img src="/imagese/KFcar.png" alt=""></li>
                <li class="KFtimeLi">
                    <h1 class="KFtimeH1">
                        <a th:href="@{'/houseKeeper2.htm'}" target="_blank">
                            <span th:text="${houseInfo.condor.condoTour}"></span>
                        </a>
                    </h1>
                    <div class="KFdetails">
                        <ul>
                            <li class="KFdetailsli">
                                发车时间:
                                <span th:text="${houseInfo.condor.sendTime}"></span>
                            </li>
                            <li class="KFdetailsli2">
                                集合地点:
                                <span th:text="${houseInfo.condor.colPlace}"></span>
                            </li>
                            <li class="KFdetailsli3">
                                承诺服务:
                                <span th:text="${houseInfo.condor.serviceInfo}"></span>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="KFtimeLi2">
                    <a id="gratis-apply">免费报名</a>
                    <span>已有<b th:text="${houseInfo.condor.projectId}"></b>人报名</span>
                </li>
            </ul>
        </div>
    </div>
    <!--楼盘动态-->
    <div class="houseSaleDT" th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
        <div class="normalHouseMain">
            <div class="title">
                <p>楼盘动态</p><a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm'}" target="_blank"
                              class="more">查看更多 ></a>
            </div>
            <ul class="xstd">
                <li th:each="saleInfo,iterStat:${saleInfo}" th:if="${iterStat.count &lt; 3}">
                    <span th:text="${saleInfo.addTime.year}+'-'+${saleInfo.addTime.month}+'-'+${saleInfo.addTime.day}"></span>
                    <a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm#'+iterStat.index}"
                       target="_blank">
                        <p th:text="${saleInfo.news}"></p>
                    </a>
                </li>
            </ul>
        </div>

<!--        <div class="normalHouselpDy">-->
<!--            <div class="housesRight">-->
<!--                <div class="houseRight">-->
<!--                    <div class="houseRinghtTitle">楼盘订阅</div>-->
<!--                    <ul>-->
<!--                        <li rel="1"><b></b><span>最新动态</span></li>-->
<!--                        <li rel="2"><b></b><span>优惠通知</span></li>-->
<!--                        <li rel="3"><b></b><span>开盘通知</span></li>-->
<!--                        <li rel="4"><b></b><span>降价通知</span></li>-->
<!--                        <li rel="5"><b></b><span>看房团通知</span></li>-->
<!--                    </ul>-->
<!--                    <a onclick="showUnuseCode(2)" class="houseRightBtn" data_i=""-->
<!--                       style="cursor:pointer ; display: block;margin-top: 5px">立即订阅</a>-->
<!--                </div>-->
<!--            </div>-->
<!--            <script type="text/javascript">-->
<!--                //                 $(".houseSaleDT .houseRight ul li").hover(function(){-->
<!--                //                     $(".houseSaleDT .houseRight ul li").removeClass("hover")-->
<!--                //                     $(this).addClass("hover")-->
<!--                //                 })-->
<!--                $(".houseSaleDT .houseRight ul li").click(function () {-->
<!--                    if ($(this).hasClass("hover")) {-->
<!--                        $(this).removeClass("hover")-->
<!--                    } else {-->
<!--                        $(this).addClass("hover")-->
<!--                    }-->
<!--                    var txt = ""-->
<!--                    $(".houseSaleDT .houseRight ul li").each(function () {-->
<!--                        if ($(this).hasClass("hover")) {-->
<!--                            txt += $(this).attr("rel") + ","-->
<!--                        }-->
<!--                    })-->
<!--                    $(".houseRightBtn").attr("data_i",txt)-->
<!--                })-->
<!--            </script>-->
<!--        </div>-->
    </div>

    <script th:inline="javascript">
        var g_starb=[[${selectionInfo.totalScore}]]
        // console.log([[${selectionInfo}]])
        // console.log(11111111111)
    </script>
    <!--楼盘价值测评-->
    <div class="g_assess" th:style="${comparisonId ne null}?'':'display: none'">
        <div class="title">
            <p th:text="${houseInfo.projectName + '楼盘价值测评'}"></p>
            <div class="layoutTypeR">
                <a class="g_dhv">查看报告</a>
            </div>
            <div class="g_hdcode">
                <div class="g_hcode" id="g_hcode"></div>
                <div class="g_ctx"><i>真实房源,扫码查看</i></div>
                <em></em>
            </div>
        </div>
        <div class="g_evalu">
            <div class="g_leva">
                <div class="g_leva_t">楼盘测评多维度分析</div>
                <div class="g_leva_m">
                    <div class="show_number">
                        <li>
                            <span th:text="${selectionInfo.totalScore} + '分'"></span>
                            <div class="atar_Show">
                                <!--<p th:tip="${selectionInfo.totalScore}" th:style="'width:' +${selectionInfo.totalScore}+'px;'"></p>-->
                                <div class="g_stara"></div>
                                <div class="g_starb"></div>
                            </div>
                        </li>
                    </div>
                </div>
            </div>
            <div class="g_reva">
                <div class="g_rli">
                    <div class="g_rf"><em>交通位置</em><em th:text="${selectionInfo.trafficScore}+'分'"></em></div>
                    <div class="g_rf"><em>户型设计</em><em th:text="${selectionInfo.unitScore}+'分'"></em></div>
                </div>
                <div class="g_rli">
                    <div class="g_rf"><em>外围配套</em><em th:text="${selectionInfo.externalResourceScore}+'分'"></em></div>
                    <div class="g_rf"><em>开发品牌</em><em th:text="${selectionInfo.brandScore}+'分'"></em></div>
                </div>
                <div class="g_rli">
                    <div class="g_rf"><em>内部配套</em><em th:text="${selectionInfo.internalResourceScore}+'分'"></em></div>
                    <div class="g_rf"><em>价值潜力</em><em th:text="${selectionInfo.valueScore}+'分'"></em></div>
                </div>
                <div class="g_rli">
                    <div class="g_rf"><em>园区环境</em><em th:text="${selectionInfo.environmentScore}+'分'"></em></div>
                    <div class="g_rf"><em>装修品质 </em><em th:text="${selectionInfo.decorateScore}+'分'"></em></div>
                </div>
                <div class="g_rli">
                    <div class="g_rf"><em>物业管理</em><em th:text="${selectionInfo.propertyScore}+'分'"></em></div>
                    <div class="g_rf"><em>性价比</em><em th:text="${selectionInfo.costPerformanceScore}+'分'"></em></div>
                </div>
            </div>
            <div class="g_clbo"></div>
        </div>
    </div>
    <!--项目特色-->
    <div class="newHouseViewChunk" th:if="${houseInfo.schoolInfo ne null and #lists.size(houseInfo.schoolInfo) > 0}">
        <div class="title" th:if="${houseInfo.schoolInfo ne null and #lists.size(houseInfo.schoolInfo) > 0}">
            <p>学区简介</p>
        </div>
        <div class="w" th:each="school,i:${houseInfo.schoolInfo}"
             th:style="${school ne ''}?'display:block':'display:none'">
            <div class="xqjj_con">
                <div class="xqjj_con_r">
                    <div id='xqjj_0' class="carousel xqjj_caus">
                        <!-- Carousel items -->
                        <div class="carousel-inner xq_imgs">
                            <div class="active item">
                                <img th:src="${school.picUrl}" th:alt="${school.schoolAreaName}"/>
                                <div class="xqjj_item_bg"></div>
                                <p th:text="${school.schoolAreaName}"></p>
                            </div>
                        </div>
                        <!-- Carousel nav -->
                        <!--<a class="carousel-control left" href="#xqjj_0" data-slide="prev"></a>-->
                        <!--<a class="carousel-control right" href="#xqjj_0" data-slide="next"></a>-->
                    </div>
                </div>
                <div class="xqjj_con_l">
                    <div class="xqjj_name">
                        <th:block th:text="${school.schoolAreaName}"></th:block>
                        <span th:if="${school.schoolType}"
                              th:text="${#strings.isEmpty(school.schoolType)? '': school.schoolType}"></span>
                        <span th:if="${school.scale}"
                              th:text="${#strings.isEmpty(school.scale)? '': school.scale}"></span>
                        <!--<span class="xq_name_r">-->
                        <!--<span class="view_icon"></span>-->
                        <!--<span class="xq_tel">电话</span>-->
                        <!--<th:block th:text="${school.telephone}"></th:block>-->
                        <!--</span>-->
                    </div>
                    <div class="xqjj_qy" th:utext="${school.description1}"></div>
                    <div class="xqjj_address"><span class="xq_tel">地址：</span>
                        <th:block th:text="${school.address}"></th:block>
                    </div>
                    <div class="xqjj_dir">
                        <div class="show">
                            <span class="xq_tel">概述：</span>
                            <th:block th:utext="${school.description2}"></th:block>
                            <div class="layer_sun">
                                <div class="ms_sanjiao dt_sj"></div>
                                <th:block th:text="${school.description2}"></th:block>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cl"></div>
            </div>
        </div>
    </div>
    <!--楼盘户型-->
    <div class="newHouseViewChunk" th:if="${!#lists.isEmpty(houseInfo.mainLayoutInfo)}">
        <div id="js202" class="title" th:each="m,i:${houseInfo.mainLayoutInfo}" th:if="${i.count &lt; 2}"
             th:style="${m ne null}?'display:block':'display:none'">

            <p>楼盘户型</p>
            <div class="layoutTypeR">
                <a th:each="layoutType:${layoutType}" th:text="${layoutType.name}"
                   th:href="${'/house/layout/r'+layoutType.id+'-pid'+projectId + '-pt' + projectType+ '.htm'}"></a>
                <i>丨</i>
                <a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '.htm'}">查看更多</a>
            </div>
        </div>
        <div class="huxing">
            <ul>
                <li th:each="l,i:${houseInfo.mainLayoutInfo}" th:if="${i.count &lt; 3}">
                    <!--<a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '-l'+l.layId}">点击</a>-->
                    <a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '-l'+l.layId}" target="_blank"
                       class="newHouseHxImg">
                        <img th:src="${l.pic}"
                             th:alt="${#numbers.formatInteger(l.buildArea,2)+'㎡'+l.roomType+'室'+l.hallType+'厅'+l.guardType+'卫'}"></a>

                    <div class="newHouseHxMain">
                        <h4>
                            <a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '-l'+l.layId}"
                               target="_blank">
                                <th:block
                                        th:text="${#numbers.formatInteger(l.buildArea,2)+'㎡'+l.roomType+'室'+l.hallType+'厅'+l.guardType+'卫'}"></th:block>
                            </a>
                            <span th:class="${'type_sun wait_'+l.state}"><th:block
                                    th:text="${#strings.isEmpty(l.stateValue)?'在售':l.stateValue}"></th:block></span>
                            <!--语音户型图按钮-->
                            <span th:if="${l.voice ne null and l.voice ne ''}" class="voiceTabSy"
                                  th:data-id="${projectId + '-' + l.layId}">语音户型</span>
                            <!--语音户型图按钮-->
                            <div class="cl"></div>
                        </h4>
                        <div>
                            <p><span>建筑面积： </span>
                                <th:block
                                        th:text="${l.buildArea eq 0 or #strings.isEmpty(l.buildArea)? '暂无资料':l.buildArea+'㎡'}"></th:block>
                            </p>
                            <p th:unless="${#strings.isEmpty(l.freeArea)}"><span>赠送面积：  </span>
                                <th:block th:text="${l.freeArea}"></th:block>
                            </p>
                            </p>
                        </div>
                        <div>
                            <p><span>朝向： </span>
                                <th:block
                                        th:text="${#strings.isEmpty(l.forwardType) or #strings.isEmpty(l.forwardType)? '暂无资料' : l.forwardType}"></th:block>
                            </p>
                        </div>
                        <p><span>类型： </span>
                            <th:block
                                    th:text="${#strings.isEmpty(l.houseType) or #strings.isEmpty(l.houseType)? '暂无资料':l.houseType  }"></th:block>
                        </p>
                        <!--<span>户型解析： </span>-->
                        <!--<th:block th:text="${#strings.isEmpty(l.description)? '暂无资料' : l.description}"></th:block><br>-->

                        <!--<a  th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '-l'+l.layId}" class="huxing-seemore" target="_blank">查看全文></a>-->
                    </div>

                    <div class="newHouseHxRight">
                        <div class="hxPrise">

                            <div th:if="${houseInfo.projectStatus ne '3'}"><span class="yfyj"
                                       th:utext="${#strings.isEmpty(l.totalPrice) or  l.totalPrice eq 0? '<p>待定</p>' : (#strings.indexOf(l.totalPrice ,'.') eq -1 ? l.totalPrice :#strings.toString(l.totalPrice ).replaceAll('0+?$','').replaceAll('[.]$','')) }"></span>
                                <th:block
                                        th:text="${#strings.isEmpty(l.totalPrice) or   l.totalPrice eq 0?'':'万元/套'}"></th:block>
                            </div>
                            <span th:if="${houseInfo.projectStatus ne '3'}">参考总价： </span>
                            <span th:if="${houseInfo.projectStatus eq '3'}">项目已售罄 </span>
                        </div>
<!--                        <div class="huxing-btn type-Btn" th:if="${houseInfo.projectStatus ne '3'}">-->
<!--                            <p class="type-compare-btn1" th:data-layId="${ l.layId}" th:data-layName="${#numbers.formatInteger(l.buildArea,2)+'㎡'+l.roomType+'室'+l.hallType+'厅'+l.guardType+'卫'+ houseInfo.projectName}"><i></i>户型对比</p>-->
<!--                            <p class="type-compare-btn2" th:data-layId="${ l.layId}" th:data-layName="${#numbers.formatInteger(l.buildArea,2)+'㎡'+l.roomType+'室'+l.hallType+'厅'+l.guardType+'卫'+ houseInfo.projectName}" th:onclick="'deletelayoud2('+${ l.layId}+')'"><i></i>取消对比</p>-->
<!--                            &lt;!&ndash;<input type="button" onclick="showUnuseCode(1)" value="获取最新价格变动" class="getChangePzBtn"/>&ndash;&gt;-->
<!--&lt;!&ndash;                            <p onclick="showUnuseCode(1)" value="" class="getChangePzBtn"><i></i>获取最新价格变动</p>&ndash;&gt;-->
<!--                        </div>-->


                    </div>
                    <!--语音户型图二维码框-->
                    <div th:if="${l.voice ne null and l.voice ne ''}" th:id="${'vrcode' + l.layId}" style="display:none"
                         class="vrcodeall">
                        <span></span>
                        <img th:id="${'vqrcode' + l.layId}" src=""/>
                        <i>扫描二维码<br>听我的自我介绍</i>
                    </div>
                    <!--语音户型图二维码框-->
                </li>
            </ul>
        </div>
        <div class="cl"></div>
    </div>

    <!--深度解析-->
    <div class="newHouseViewChunk" th:if="${!#maps.isEmpty(houseInfo.deepNews)}">
        <div class="deepAnalysis">
            <div class="analysisTitle">
                <h1>深度解析</h1>
            </div>
            <div class="analysisInfo">
                <div class="analysisInfoImg">
                    <a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank">
                        <img th:src="${#strings.isEmpty(houseInfo.deepNews.pic)?'':houseInfo.deepNews.pic}"
                             th:alt="${#strings.isEmpty(houseInfo.deepNews.projectName)?'':houseInfo.deepNews.projectName}"
                             width="180px" height="134px"/>
                    </a>
                </div>
                <div class="analysisInfoTxt">
                    <a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank">
                        <h2 th:text="${#strings.isEmpty(houseInfo.deepNews.titleShow)?'':houseInfo.deepNews.titleShow}"></h2>
                    </a>
                    <a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank">
                        <p th:text="${#strings.isEmpty(houseInfo.deepNews.description)?'':houseInfo.deepNews.description}"></p>
                    </a>
                </div>
                <div class="cl"></div>
            </div>
        </div>
    </div>
    <!--深度解析结束-->


    <div class="newHouseViewChunk">
        <div th:include="house/detail/fragment_map::map2"></div>
    </div>
    <!--参考月供计算器-->
<!--    &lt;!&ndash;<th:block th:if="${houseInfo.projectStatus ne '3'}">-->
<!--        <div class="newHouseViewChunk"-->
<!--             th:if="${!(houseInfo.price == null or #arrays.length(houseInfo.price) == 0 or #lists.isEmpty(layoutDetail))}">-->
<!--            <div class="w" th:include="house/detail/fragment_calculator::calculator"></div>-->
<!--            <div class="cl"></div>-->
<!--        </div>-->
<!--    </th:block>&ndash;&gt;-->

    <!--2025年4月3日 通用房贷计算器-->
    <input type="hidden" id="generalCalculatorMoney">
    <script th:inline="javascript">
        $(document).ready(function () {
            var generalCalculatorLayout = [[${layoutDetail}]]
            var generalCalculatorArea =  generalCalculatorLayout[0].buildArea
            var generalCalculatorPrice = generalCalculatorLayout[0].price
            var generalCalculatorMoney = ((generalCalculatorArea*generalCalculatorPrice)/10000).toFixed(2);
            if (generalCalculatorMoney == '' || generalCalculatorMoney == 0 || generalCalculatorMoney == NaN || generalCalculatorMoney == null){
                generalCalculatorMoney = '100'
            }
            $("#generalCalculatorMoney").val(generalCalculatorMoney)
        });

    </script>
    <th:block th:if="${houseInfo.projectStatus ne '3'}">
        <div
             th:if="${!(houseInfo.price == null or #arrays.length(houseInfo.price) == 0 or #lists.isEmpty(layoutDetail))}">
            <div class="w" th:include="house/detail/fragment_calculator::generalCalculator"></div>
            <div class="cl"></div>
        </div>
    </th:block>
    <!--项目点评-->
    <div th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" id="xmpj" class="newHouseViewChunk">
        <div th:include="house/detail/fragment_ask::xmpjNew"></div>
        <div class="cl"></div>
    </div>
    <!--楼盘问答-->
    <div th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" class="newHouseViewChunk">
        <div th:include="house/detail/fragment_order::forAsk"></div>
        <div th:include="house/detail/fragment_ask::xmzx"></div>
    </div>
    <div class="cl"></div>
    <!--相关推荐-->
    <div class="newHouseViewChunk" style="margin-top: 30px;border: 0">
        <div th:include="house/detail/fragment_relasion_house::normalHouse_push"></div>
    </div>
    <div class="cl"></div>
</div>

<!--热门楼盘和新房推荐-->
<div th:include="fragment/fragment::footer_seo"></div>
<div class="disclaimer"><strong>免责声明</strong>： 楼盘信息由开发商提供，最终以政府部门登记备案为准，请谨慎核查。如该楼盘信息有误，您可以投诉或拔打举报电话：************。</div>
<div class="cl"></div>
</div>
<!--底部-->
<div class="footer" th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<th:block th:if="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
    <!--<div th:include="house/detail/fragment_login::login"></div>-->
    <div th:include="fragment/fragment::commonFloat" th:with="projectId=${projectId}, commonType=1"></div>
</th:block>
<th:block th:unless="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
    <div th:include="fragment/fragment::commonFloat"></div>
</th:block>

<div class="cl"></div>

<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}"
     th:include="house/detail/fragment_contactAgent::contact"></div>
<!--
<div th:include="house/detail/fragment_contactAgent::loginAgent"></div>
-->
<!--看房车-->
<!--<div th:include="house/detail/fragment_request::request"></div>-->
<!--无验证码订单-->
<div th:include="house/detail/fragment_order::unuseCode"></div>
<div th:include="house/detail/fragment_order::guideMessage"></div>
<!--有验证码订单-->
<div th:include="house/detail/fragment_order::useCode"></div>
<img id="codelogo" style="display: none" src="https://imageicloud.fangxiaoer.com/event/2022/10/11/133502165.png"
     crossorigin="anonymous">
<div id="qrcodeInfo" style="display: none"></div>

<script th:inline="javascript">

    var result = [[${houseInfo}]];
    console.log('打印结果',result)

</script>


<script type="text/javascript">
    /**该方法用来绘制一个有填充色的圆角矩形
     *@param cxt:canvas的上下文环境
     *@param x:左上角x轴坐标
     *@param y:左上角y轴坐标
     *@param width:矩形的宽度
     *@param height:矩形的高度
     *@param radius:圆的半径
     *@param fillColor:填充颜色
     **/
    function fillRoundRect(cxt, x, y, width, height, radius, /*optional*/ fillColor) {
        //圆的直径必然要小于矩形的宽高
        if (2 * radius > width || 2 * radius > height) {
            return false;
        }

        cxt.save();
        cxt.translate(x, y);
        //绘制圆角矩形的各个边
        drawRoundRectPath(cxt, width, height, radius);
        cxt.fillStyle = fillColor || "#000"; //若是给定了值就用给定的值否则给予默认值
        cxt.fill();
        cxt.restore();
    }

    function drawRoundRectPath(cxt, width, height, radius) {
        cxt.beginPath(0);
        //从右下角顺时针绘制，弧度从0到1/2PI
        cxt.arc(width - radius, height - radius, radius, 0, Math.PI / 2);

        //矩形下边线
        cxt.lineTo(radius, height);

        //左下角圆弧，弧度从1/2PI到PI
        cxt.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);

        //矩形左边线
        cxt.lineTo(0, radius);

        //左上角圆弧，弧度从PI到3/2PI
        cxt.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);

        //上边线
        cxt.lineTo(width - radius, 0);

        //右上角圆弧
        cxt.arc(width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);

        //右边线
        cxt.lineTo(width, height - radius);
        cxt.closePath();
    }

    $(".voiceTabSy").mouseover(function () {
        var value = $(this).attr("data-id");
        var infos = value.split('-');
        var voiceUrl = "https://m.fangxiaoer.com/fang1/" + infos[0] + "-1/layout/-pid" + infos[0] + "-pt1-l" + infos[1];
        $('#qrcodeInfo').qrcode({
            render: "canvas",
            width: 160,
            height: 160,
            text: voiceUrl
        }).hide();
        var canvasinfo = $("#qrcodeInfo canvas")[0];
        //添加logo
        var codeImage = document.querySelector("#codelogo");
        codeImage.crossOrigin = "anonymous"
        var ctx = canvasinfo.getContext('2d')
        fillRoundRect(ctx, 59, 59, 42, 42, 3, '#fff')
        ctx.drawImage(codeImage, 61, 61, 38, 38);
        $('#vqrcode' + infos[1]).attr('src', canvasinfo.toDataURL('image/png'));
        $("#vrcode" + infos[1]).show();
    });
    $(".voiceTabSy").mouseleave(function () {
        $("#qrcodeInfo").html('')
        var value = $(this).attr("data-id");
        var infos = value.split('-');
        $("#vrcode" + infos[1]).hide();
    });
</script>
<script type="text/javascript">
    $(document).ready(function () {
        //如果学区照片只有一张 去掉上一张下一张
        $(".xqjj_con_r").each(function () {
            var imgLength = $(this).find(".item img").length;
            if (imgLength < 2) {
                $(this).find(".carousel-control").hide()
            }
        })
    });
</script>
<!--项目特色-->
<script type="text/javascript">
    $(".view_xmts span").bind("mouseover", function () {
        $(".miaoshu").hide();
        $(this).find(".miaoshu").show();
    })
    $(".view_xmts span").bind("mouseleave", function () {
        $(".miaoshu").hide();
    })
</script>
<div class="modal-backdrop  in" id="loginzhezhao" style="display: none"></div>
<input type="hidden" id="fxe_status" value="normal"/>
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->

<!--报名弹窗-->
<div id="condo-apply">
    <div class="condo-apply-win">
        <h6>免费报名<span id="close"><img src="https://static.fangxiaoer.com/web/images/sercoudo/tour-close.png"></span></h6>
        <!--已登录用户报名-->
        <th:if th:if="${#session?.getAttribute('sessionId') ne null and #session?.getAttribute('sessionId') ne ''}">
            <div class="condo-apply-put">
                <input id="phone" type="tel" name="phone" placeholder="请输入手机号" readonly="readonly"
                       th:value="${session.phoneNum}" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')"
                       onafterpaste="this.value=this.value.replace(/\D/g,'')">
            </div>
            <div class="condo-apply-put">
                <input id="address" type="text" name="address" placeholder="请输入您的姓名">
                <span class="errormsg name" style="display: none">请输入您的姓名</span>
            </div>
            <div class="condo-apply-put" style="border: none;">
                <div class="checkagreeInput" style="margin: 3px auto 10px auto;">
                    <i id="checkagree1" class="checkimg checked cheimg9"></i>
                    <div style="font-size:12px;">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol"
                                                           target="_blank">《房小二网用户服务协议》</a>及
                        <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                </div>
            </div>
            <div class="condo-apply-put condo-apply-subme">
                <p id="submit">立即提交</p>
            </div>
        </th:if>

        <!--已登录用户报名-->
        <!--未登录用户报名-->
        <th:if th:unless="${#session?.getAttribute('sessionId') ne null and #session?.getAttribute('sessionId') ne ''}">
            <div class="condo-apply-put">
                <input id="phone" type="tel" name="phone" placeholder="请输入手机号" maxlength="11"
                       onkeyup="this.value=this.value.replace(/\D/g,'')"
                       onafterpaste="this.value=this.value.replace(/\D/g,'')">

                <span class="errormsg cellphone" style="display: none">请输入正确的手机号码</span>
            </div>
            <div class="condo-apply-put" id="code-input">
                <input id="code" type="tel" name="code" style="width: 65%;border-right: 1px solid #dedede;"
                       placeholder="请输入验证码" maxlength="6">
                <span id="code-pull">获取验证码</span>
                <span class="errormsg verifycode" style="display: none">请输入正确的验证码</span>
            </div>

            <div class="condo-apply-put">
                <input id="address" type="text" name="address" placeholder="请输入您的姓名">
                <span class="errormsg name" style="display: none">请输入您的姓名</span>
            </div>
            <div class="condo-apply-put" style="border: none;">
                <div class="checkagreeInput" style="margin: 3px auto 10px auto;">
                    <i id="checkagree1" class="checkimg checked cheimg9"></i>
                    <div style="font-size:12px;">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol"
                                                           target="_blank">《房小二网用户服务协议》</a>及
                        <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                </div>
            </div>
            <div class="condo-apply-put condo-apply-subme">
                <p id="submit">立即提交</p>
            </div>
        </th:if>
    </div>
    <!--未登录用户报名-->
    <div id="success" style="display: none;">
        <span id="closeb"><img src="https://static.fangxiaoer.com/web/images/sercoudo/tour-close.png"></span>
        <p><img src="https://static.fangxiaoer.com/web/images/sercoudo/success.png"></p>
        <P>预约成功</P>
        <p>工作人员将尽快与您联系，免费为您提供专业服务</p>
    </div>

</div>


<script th:inline="javascript">
    $(".cheimg9").click(function () {
        if ($(this).hasClass("checked")) {
            $(this).removeClass("checked")
        } else {
            $(this).addClass("checked")
        }
    })
    $("#gratis-apply").click(function () {
        $("#condo-apply").show()
        $(".condo-apply-win").show();
        $("body").css("overflow", "hidden")
    });

    $("#close").click(function () {
        $("#condo-apply").hide();
        $("#success").hide()
        $("body").css("overflow", "auto")
    })

    $("#closeb").click(function () {
        clearTimeout(closeTime);
        $("#condo-apply").hide();
        $("#success").hide()
        $("body").css("overflow", "auto")
    })


    var wait = 60;
    $("#code-pull").click(function () {
        var tel = $("#phone").val();//获取手机号
        if (tel.length == 11 && tel.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/) && wait == 60) {
            $.ajax({
                type: "POST",
                data: {
                    mobile: tel
                },
                url: "/sendSmsCode",
                success: function (result) {
                    if (result.status == 0) {
                        alert("系统繁忙，请稍后重试!");
                    } else {
                        $("#code-pull").css({
                            "border": "none",
                            "color": "#ccc"
                        });
                        time(wait);
                    }
                }
            });
        } else {
            $(".cellphone").html("请输入正确的手机号码").show();
            return false;
        }
    })

    function time(o) {
        if (wait == 0) {
            $("#code-pull").html("重新获取");
            wait = 60;
            $("#code-pull").css({
                "border": "none",
                "color": "#ff5200"
            });
        } else {
            $("#code-pull").html(wait + "秒后重发");
            wait--;
            setTimeout(function () {
                    time(o);
                },
                1000);
        }
    }


    var closeTime;
    var sessionId = [[${#session?.getAttribute('sessionId')}]];
    $("#submit").click(function () {
        var phone = "",
            code = "",
            carWhereId = "";
        phone = $("#phone").val(); //手机号
        code = $("#code").val(); //验证码
        carWhereId = $("#address").val(); //用户名
        if (sessionId == null || sessionId == "") {
            if (phone == "" || phone.length != 11 || !phone.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)) {
                $(".cellphone").show();
                setTimeout(function () {
                    $(".cellphone").hide();
                }, 2000);
                // alert("请正确输入您的手机号码");
                return;
            } else {
                $(".cellphone").hide();
            }

            if (code == "" || code.length != 6) {
                $(".verifycode").show();
                setTimeout(function () {
                    $(".verifycode").hide();
                }, 2000);
                // alert("请正确输入您的验证码");
                return;
            } else {
                $(".verifycode").hide();
            }
        }


        if (carWhereId == null || carWhereId == "") {
            $("span.name").html("请输入姓名");
            $("span.name").show()
            setTimeout(function () {
                $("span.name").hide();
            }, 2000);
            return
        }
        if ($(".cheimg9").hasClass("checked") == false) {
            alert("请仔细阅读并同意服务协议及隐私政策。");
            return
        }
        var params = {
            phone: phone,
            sessionId: sessionId,
            code: code,
            region: carWhereId,
            area: "看房团",
            type: 4,
            italy:'【Sy站】'
        };
        $.ajax({
            type: "POST",
            url: "/saveHouseOrder",
            data: JSON.stringify(params),
            headers: {
                'Content-Type': 'application/json;charset=utf-8'
            },
            success: function (data) {
                if (data.status == 1) {
                    $(".condo-apply-win").hide();
                    $("#success").show();
                    $("#success").children()[0].style.display = " ";
                    $("#success").children()[2].style.display = " ";
                    if (sessionId == '' || sessionId == null || sessionId == undefined) {
                        $("#phone").val("");
                    }
                    $("#code").val(""); //验证码
                    $("#address").val(""); //用户名
                    closeTime = setTimeout(function () {
                        $("#condo-apply").hide();
                        $("#success").hide()
                        $("body").css("overflow", "auto")
                    }, 3000)
                } else {
//                       $(".condo-apply-win").hide();
//                        $("#success").children()[0].style.display="none";
//                        $("#success").children()[2].style.display="none";
//                        $("#success").children()[1].html(data.msg);
                    alert(data.msg + ",请检查后重新提交")
                }
            }
        })
    })
    //获取验证码倒计时

    $("#player-b").click(function () {
        $("#condotour").get(0).play();
        $("#condotour").get(0).controls = true;
        $("#player-b").hide();
    });

    $("#condotour").bind("ended", function () {
        $("#player-b").show();
        $("#condotour").get(0).controls = false;
    });
    // $("#condotour").get(0).onpause=function () {
    //     $("#player-b").show();
    //     $("#condotour").get(0).pause();
    //     $("#condotour").get(0).controls=false;
    // }

/*    $(".yyDk").show();
    $(".coloio").show();
    success_Message(2)*/


</script>
<!--报名弹窗-->


</body>

</html>