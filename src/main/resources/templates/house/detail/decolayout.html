<!DOCTYPE html>

<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head><title>
    房小二
</title><meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180522" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/jz_view.css?v=20181228" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <meta name="copyright" content="房小二网版权所有" />
    <meta name="keywords" content="沈阳房产网,沈阳房产,沈阳新楼盘,沈阳新房,沈阳二手房,沈阳租房,沈阳日租房,沈阳厂房,沈阳门市,沈阳房价,沈阳房产新闻,沈阳房交会,沈阳房产信息,沈城楼市" />
    <meta name="description" content="房小二网专业的沈阳房产网,提供了沈阳房产信息,包括了沈阳新楼盘、沈阳二手房、沈阳租房、沈阳新房、沈阳厂房、沈阳门市、沈阳房价,提供了沈阳新楼盘信息、沈阳新房房源、沈阳二手房房源、沈阳租房房源、沈阳日租房房源、沈阳中介房源、沈阳厂房房源、沈阳门市等信息,同时也发布沈阳房产资讯和最新的房产新闻。" /></head>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
<body>
    <div class="open">
        <div class="openLeft">
            <div id="tFocus">
                <div class="prev" id="prev"></div>
                <div class="next" id="next"></div>
                <ul id="tFocus-pic">
                    <li th:each="pic:${detail.pic}"><img th:src="${pic.url}" th:alt="${pic.title}"/></li>
                </ul>
                <div id="tFocusBtn">
                    <a href="javascript:void(0);" id="tFocus-leftbtn">上一张</a>
                    <div id="tFocus-btn">
                        <ul>
                            <li th:each="pic:${detail.pic}"><img th:src="${pic.url}" th:alt="${pic.title}"/></li>
                        </ul>
                    </div>
                    <a href="javascript:void(0);" id="tFocus-rightbtn">下一张</a>
                </div>
            </div><!--tFocus end-->
        </div>
        <div class="openRight" th:if="${#lists.size(detail.layPic) > 0}">
            <h1 th:text="${detail.layPic[0].title}"></h1>
            <h3>房型描述：</h3>
            <p th:text="${detail.layPic[0].title}"></p>
            <img th:src="${detail.layPic[0].url}" width="87" height="57" th:alt="${detail.layPic[0].title}" />
            <p class="zhu">注：项目精装资料来源于售楼处，一切以售楼处为准！</p>
        </div>
    </div>
    <script>
        function addLoadEvent(func) {
            var oldonload = window.onload;
            if (typeof window.onload != 'function') {

            } else {
                window.onload = function () {
                    oldonload();
                    func();
                }
            }
        }
        function Focus() {
            function byid(id) {
                return document.getElementById(id);
            }
            function bytag(tag, obj) {
                return (typeof obj == 'object' ? obj : byid(obj)).getElementsByTagName(tag);
            }
            var timer = null;
            var oFocus = byid('tFocus');
            var oPic = byid('tFocus-pic');
            var oPicLis = bytag('li', oPic);
            var oBtn = byid('tFocus-btn');
            var oBtnLis = bytag('li', oBtn);
            var iActive = 0;
            function inlize() {
                oPicLis[0].style.filter = 'alpha(opacity:100)';
                oPicLis[0].style.opacity = 100;
                oPicLis[0].style.zIndex = 5;
            }
            for (var i = 0; i < oPicLis.length; i++) {
                oBtnLis[i].sIndex = i;
                oBtnLis[i].onclick = function () {
                    if (this.sIndex == iActive) return;
                    iActive = this.sIndex;
                    changePic();
                }
            }
            byid('tFocus-leftbtn').onclick = byid('prev').onclick = function () {
                iActive--;
                if (iActive == -1) {
                    iActive = oPicLis.length - 1;
                }
                changePic();
            };
            byid('tFocus-rightbtn').onclick = byid('next').onclick = function () {
                iActive++;
                if (iActive == oPicLis.length) {
                    iActive = 0;
                }
                changePic();
            };

            function changePic() {
                for (var i = 0; i < oPicLis.length; i++) {
                    doMove(oPicLis[i], 'opacity', 0);
                    oPicLis[i].style.zIndex = 0;
                    oBtnLis[i].className = '';
                }
                doMove(oPicLis[iActive], 'opacity', 100);
                oPicLis[iActive].style.zIndex = 5;
                oBtnLis[iActive].className = 'active';
                if (iActive == 0) {
                    doMove(bytag('ul', oBtn)[0], 'left', 0);
                } else if (iActive >= oPicLis.length - 4) {
                    doMove(bytag('ul', oBtn)[0], 'left', -(oPicLis.length - 4) * (oBtnLis[0].offsetWidth));
                } else {
                    doMove(bytag('ul', oBtn)[0], 'left', -(iActive - 0) * (oBtnLis[0].offsetWidth));
                }
            }
            function autoplay() {
                if (iActive >= oPicLis.length - 1) {
                    iActive = 0;
                } else {
                    iActive++;
                }
                changePic();
            }
            aTimer = setInterval(autoplay, 5000);
            inlize();
            function getStyle(obj, attr) {
                if (obj.currentStyle) {
                    return obj.currentStyle[attr];
                } else {
                    return getComputedStyle(obj, false)[attr];
                }
            }
            function doMove(obj, attr, iTarget) {
                clearInterval(obj.timer);
                obj.timer = setInterval(function () {
                            var iCur = 0;
                            if (attr == 'opacity') {
                                iCur = parseInt(parseFloat(getStyle(obj, attr)) * 100);
                            } else {
                                iCur = parseInt(getStyle(obj, attr));
                            }
                            var iSpeed = (iTarget - iCur) / 6;
                            iSpeed = iSpeed > 0 ? Math.ceil(iSpeed) : Math.floor(iSpeed);
                            if (iCur == iTarget) {
                                clearInterval(obj.timer);
                            } else {
                                if (attr == 'opacity') {
                                    obj.style.filter = 'alpha(opacity:' + (iCur + iSpeed) + ')';
                                    obj.style.opacity = (iCur + iSpeed) / 100;
                                } else {
                                    obj.style[attr] = iCur + iSpeed + 'px';
                                }
                            }
                        },
                        30)
            }
            byid('tFocus').onmouseover = function () {
                clearInterval(aTimer);
            };
            byid('tFocus').onmouseout = function () {
                aTimer = setInterval(autoplay, 5000);
            }
        }

        addLoadEvent(Focus());
    </script>
    <div th:include="fragment/fragment::tongji"></div>
    <!--<div th:include="fragment/fragment::common_meiqia"></div>-->
</body>
</html>