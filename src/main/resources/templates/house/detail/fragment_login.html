<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="login">
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/tool/login_tc.css?v=2022">-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/login_tc.css?v=2022">
    <!--<script type="text/javascript" src="https://static.fangxiaoer.com/js/fxe_confirm.js"></script>-->
<style>.d{ z-index: 999;}</style>
    <script type="text/javascript" src="/js/aliyunCaptcha.js"></script>
    <script src="/js/signatureUtils.js"></script>
    <script type="text/javascript" src="/js/house/verify.js"></script>
    <div class="modal" id="login" style="padding: 0; height: 330px;">
        <input type="hidden" id="LoginUrl" value=""/>
        <input type="hidden" id="LoginUrlAgent" value=""/>

        <div class="ic_wrong error_info eif" style="position: relative; padding: 0 49px 0 22px; top: 0; min-height: 30px;     line-height: 16px; padding-top: 10px;"></div>


        <img  data-dismiss="modal" id="loginClose" src="https://static.fangxiaoer.com/web/styles/sy/popup/close_cl.png" alt="×">
        <div  class="signup-form clearfix" style="position: relative; top: 0;">
            <h1 class="zgs" style="position: relative; top: 0; left: 0;">账户登录</h1>
            <div class="uip">
                <input autocomplete=off name="Txt_LoginName" id="txt_LoginName" value="" class="fxe_mobile_cs" placeholder="请输入手机号" maxlength="11"
                       onblur="btnShouFn()" onkeyup="btnShouFn()">
                <div class="d"></div>
                <div class="dt1"></div>
            </div>
            <div class="uip2">
                <input autocomplete="new-password"  name="Txt_Password" id="txt_Password" class="fxe_password_cs" type="password" placeholder="请输入密码或验证码"  onblur="psaawrodBlur()" onkeyup="psaawrodBlur()" style="padding-right: 115px;">
                <div class="d"></div>
                <div class="dt"></div>
                <b class="yzm fxe_ReSendValidateCoad yzmfxe_ReSendValidateCoad" style="top: 16px;">获取验证码</b>
                <b class="yzm fxe_validateCode" style="display: none; top: 16px;"></b>
                  <!-- 添加验证码容器 -->
                <div id="captchaContainer" style="display: none;"></div>
            </div>

            <div class="checkagreeInput" style="margin: 23px 0 0 0;">
                <input type="checkbox" id="protocol" name="protocol"  class="mind-login protocol_input" checked="" style="display: none;">
<!--                <i id="checkagree1" class="checkimg cheimg4"></i>-->
                <div class="fts">登录注册即表示同意
                    <a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《服务协议》</a>及<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《隐私政策》</a>
                    <div onclick="window.open('https://my.fangxiaoer.com/RetrPassword.aspx')" class="wji" style="left: 0;">忘记密码</div>
                    <div onclick="window.open('https://my.fangxiaoer.com/register.aspx')" class="wji" style="position: absolute; top: -25px; right: -11px; left: unset;">立即注册</div>
                </div>
            </div>
            <div class="three2">
                <!--微信 qq登陆-->
                <div class="hvx" onclick="WeiXurl()">
                    <span style="margin-right: 5px !important;margin-left:0 !important;"></span>微信
                </div>
                <i>|</i> <!--▏|︳-->
                <div class="hqq" onclick ="window.location.href='https://graph.qq.com/oauth2.0/authorize?client_id=101379955&amp;response_type=token&amp;scope=all&amp;redirect_uri=http%3a%2f%2fmy.fangxiaoer.com';">
                    <em></em>QQ
                </div>

            </div>



            <input type="button"  name="type" class="button-blue reg fa" value="登录" data-action="regist" style="margin-top:38px;margin-bottom:15px;">
            <input id="submitForm" type="button"  name="type" class="button-blue reg opc fb" value="登录" data-action="regist" style="margin-top:38px;margin-bottom:15px; display: none;">



            <div class="clearfix"></div>
            <a href="" class="various" target="_blank"  id="althref" ></a>
        </div>
    </div>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/modal.js"></script>
    <script>
        var _fxeTime = {
            //时间
            timeWait: function () {
                wait = 60;
                time();

                function time() {
                    if (wait == 0) {
                        $(".fxe_validateCode").hide();
                        $(".fxe_ReSendValidateCoad").show().html("重新获取");
                        wait = 60;
                    } else {
                        $(".fxe_validateCode").show().html("在" + wait + "秒后重发");
                        $(".fxe_ReSendValidateCoad").hide();
                        wait--;
                        setTimeout(function () {
                                time();
                            },
                            1000);
                    }
                }
            },
            r: 1, //手机验证码状态标识
            //验证用户名是否为空
            mobile1: function () {
                var Text = $("#txt_LoginName").val();
                if (Text == "") {
                    $(".error_info").text("请输入账户名");
                    return false;
                } else {
                    $(".error_info").text("");
                    return true;
                }
            },
            mobile2: function () {
                var mobile = $("#txt_LoginName").val();
                var reg = /^1[1,2,3,4,5,7,8,6,9,0]{1}[0-9]{1}[0-9]{8}$/;
                if (mobile == "") {
                    $(".fxe_mobile_info").text("请输入账户名");
                    return false;
                } else if (!reg.test(mobile)) {
                    $(".fxe_mobile_info").text("手机号码格式不正确，请重新输入!");
                    return false;
                } else {
                    return true;
                }
            },
            //验证密码是否为空
            password: function () {
                var Text = $("#txt_Password").val();
                if (Text == "") {
                    $(".error_info").text("请输入密码");
                    return false;
                } else {
                    return true;
                }
            }
        }
    </script>
    <script>
        var loginNeedCallBack = 0;
        $(".cheimg4").click(function () {
            if($(this).hasClass("checked")){
                $(this).removeClass("checked")
            }else{
                $(this).addClass("checked")
            }
        })
        $(".yzmfxe_ReSendValidateCoad").click(function () {
            $(".fxe-alert").hide()
        })
        sy_confirm.init(2,false)
        $(".yzmfxe_ReSendValidateCoad").click(function () {
            if(sy_confirm.phone($("#txt_LoginName").val())==true){
                $(".error_info").text("");
                $("#error_info i").text("")

                // 定义验证成功的回调函数
                const onSuccessCallback = function() {
                    console.log('验证码验证成功！');
                    sy_confirm.Code($("#txt_LoginName").val()).then(res => {
                        console.log('发送验证码成功！');
                        console.log(res);
                        if(res == true){
                            $(".fxe-alert").hide();
                            sy_confirm.timeWait();
                        }
                    }).catch(err => {
                        console.log('发送验证码失败！');
                        console.log(err);
                    });
                };
                
                // 调用阿里云验证码
                initAliCaptcha(onSuccessCallback).then(result => {
                    console.log('验证码初始化完成:', result);
                    captchaInstance.show();
                }).catch(error => {
                    console.error('验证码初始化失败:', error);
                });

                
                // if(sy_confirm.Code($("#txt_LoginName").val()) == true){
                //     $(".fxe-alert").hide()
                //     sy_confirm.timeWait()
                // }
            }else{
                $(".error_info").text("请输入手机号码");
                $(".fxe-alert").hide()
                $("#error_info i").text(sy_confirm.phone($("#txt_LoginName").val()))
            }
        })

        function submitForm(callback) {
            function $Arg(id) { return document.getElementById(id); }
            var r = 0;
            var goods = $("#goods").val() || 1; //先取给的值 如果没有 则设为1--普通用户
            if (_fxeTime.mobile1()) {
                if (_fxeTime.password()) {
                    if (_fxeTime.mobile2()) {
                        // if ($("#protocol").is(':checked') == false){
                        //     $(".error_info").text("请仔细阅读并同意服务协议及隐私政策。");
                        // }else{
                            $.ajax({
                                type: "POST",
                                data: { telNumber: $Arg("txt_LoginName").value, password: $Arg("txt_Password").value ,goods: goods
                                , registFromUrl: window.location.href},
                                url: "/login",
                                async: false,
                                success: function (data) {
                                    r = data;
                                }
                            });
                        // }
                    } else {
                        r=0
                        $(".error_info").text("账户名与密码不匹配，请重新输入!");
                    }
                }
            }
            if (r.status == 1) {
                if (typeof callback == "function") {
                    callback(r);
                    return;
                }
                if ($("#LoginUrl").val() != "" && IsCommentList == 1) {
                    // if ($("#protocol").is(':checked') == false) {
                    //     $(".error_info").text("请仔细阅读并同意服务协议及隐私政策。");
                    // } else {
                        if (r.content.memberType == 2 && r.content.memberType != null) {
                            window.location.href = $("#LoginUrlAgent").val();
                        } else{
                            window.location.href = $("#LoginUrl").val();
                        }
                    // }
                } else {
                    window.location.reload();
                }

            } else {
                $(".error_info").text(r.msg);
            }

        }

    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            $("a.forgot").click(function () {
                $("#login-modal").modal("hide");
                $("#forgetform").modal({ show: !0 });
            });
            $("#login").modal("hide");
        });
        $(document).keypress(function (e) {
            // 回车键事件
            if (e.which == 13) {
                if(loginNeedCallBack==1){
                    submitForm(login_callback);
                }else{
                    submitForm();
                }
                return false;
            }
        });
        $("#submitForm").click(function () {
            if(loginNeedCallBack==1){
                submitForm(login_callback);
            }else{
                submitForm();
            }
        })


        $(".uip input,.uip2 input").live({
            click: function () {
                $(this).css({ "border-bottom": "1px solid #FF5200"});
            },
            blur: function () {
                $(this).css({ "border-bottom": "1px solid #c0c0c0" });
            }
        })

        //微信三方登录
        function WeiXurl() {
            ThreeLoginWay = "10";
            location.href = "https://open.weixin.qq.com/connect/qrconnect?appid=wx8e12eabb878a01ba&redirect_uri=http%3a%2f%2fmy.fangxiaoer.com&response_type=code&scope=snsapi_login&state=F987dsd5fG52Vus";
        }

        $('.hvx,.hqq').hover(
            function () {
                $(this).addClass('f')// 鼠标移入
            },
            function () {
                $(this).removeClass('f')// 鼠标移出
            }
        )

        // $('#protocol').attr("checked",false);

        function btnShouFn(){
            let phe=$("#txt_LoginName").val()


                if(phe==''){
                    $(".error_info").text("请输入手机号码");
                    $(".fa").show()
                    $(".fb").hide()
                    return
                } else {
                    $(".error_info").text("");
                    $(".fa").hide()
                    $(".fb").show()
                    return true;
                }
            // }else{
            //     if(phe==''){
            //         $(".error_info").text("请输入手机号码");
            //         return
            //     } else {
            //         $(".error_info").text("");
            //         return true;
            //     }

        }
        function psaawrodBlur(){
            let phe=$("#txt_LoginName").val()
            let md=$("#txt_Password").val()
            if(phe==''){
                $(".error_info").text("请输入手机号码");
                $(".fa").show()
                $(".fb").hide()
                return
            } else if(md=='' || md==undefined){
                $(".error_info").text("请输入验证码");
                $(".fa").show()
                $(".fb").hide()
                return
            }else{
                $(".error_info").text("");
                $(".fa").hide()
                $(".fb").show()
                return true;
            }
        }

        $("#loginClose").click(function(){
            $(".error_info").text('')
            // location.reload()
            clearTimeout(getCheckTimeFn)
            sy_confirm.wait = 0
            $(".fxe_validateCode").text('').hide()
            $(".fxe_ReSendValidateCoad").show()
            $(".zgs").text('账户登录')
        })
    </script>
</div>

</body>
</html>