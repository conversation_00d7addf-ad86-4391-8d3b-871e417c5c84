<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title th:text="${#strings.toString(houseInfo.projectName).replace('·','')+'_沈阳'+#strings.toString(houseInfo.projectName).replace('·','')+'_' +#strings.toString(houseInfo.projectName).replace('·','')+'楼盘详情 - 房小二网'}"></title>
    <meta name="keywords"
          th:content="${#strings.toString(houseInfo.projectName).replace('·','')+',沈阳'+#strings.toString(houseInfo.projectName).replace('·','')+','+houseInfo.regionName+#strings.toString(houseInfo.projectName).replace('·','')+','+#strings.toString(houseInfo.projectName).replace('·','')+'楼盘户型信息'}"/>
    <meta name="description"
          th:content="${'沈阳'+#strings.toString(houseInfo.projectName).replace('·','')+', 房小二网提供'+#strings.toString(houseInfo.projectName).replace('·','')+'沈阳最新价格、地址、优惠、交通、户型、周边配套、实景图等楼盘信息。查找'+houseInfo.regionName+#strings.toString(houseInfo.projectName).replace('·','')+'最新动态。查找'+#strings.toString(houseInfo.projectName).replace('·','')+'房价走势、'+houseInfo.regionName+#strings.toString(houseInfo.projectName).replace('·','')+'户型信息尽在房小二网！'}"/>
    <meta name="mobile-agent"
          th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112"/>-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/main2017.css"/>
    <link href="https://static.fangxiaoer.com/js/jsq/house/index.css?v=20180522" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/sy/formulate/jquery.datetimepicker.css"/>
    <link href="https://static.fangxiaoer.com/js/jsq/house/vipFramework.css?v=20190415" rel="stylesheet"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/appointment.css?v=20180522" rel="stylesheet" type="text/css">

    <!--    <link href="/css/vipFramework.css" rel="stylesheet"/>-->

    <link href="https://static.fangxiaoer.com/web/styles/new_sy/vipHouse/view.css?v=20190416" rel="stylesheet" type="text/css">
<!--    <link href="/css/view.css?v=20190415" rel="stylesheet" type="text/css">-->

    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/dealer_view02.js?v=20180605" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript"></script>
    <script src="/js/jquery.cookie.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/Freefone1.css?v=20180522"/>
    <script src="https://static.fangxiaoer.com/js/swiper/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/swiper.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?v=20180522"/>
    <!--<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/skin/functional.css">-->
    <!-- include flowplayer -->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/condoToursPrivate.css" />
    <!--<script src="https://static.fangxiaoer.com/js/new_video/flowplayer.min.js"></script>-->
    <script src="https://static.fangxiaoer.com/js/new_video/video.min.js"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/video-js.min.css" >
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/addto.css?v=20190408">
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/depthresolution.css?v=20190219">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/payment.css" rel="stylesheet" type="text/css"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynatown.css?v=20200417" rel="stylesheet" type="text/css" />
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
</head>
<style>
    .sy-ZKclear{border:none}
    .sy-ZKclear-ararow{position:absolute;width:71px;height:28px;margin-left:1071px;margin-top:13px;cursor:pointer}
    .newHouseViewChunk {
        width: 1170px !important;
        border: 1px solid #ededed;
    padding-top: 0;
        margin-top: 28px;
}
    .title{position: relative}
    .sy-dynatown-ararow{position: absolute;
        right: 12px;
        top: 16px;}
    .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
        background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    .info_r{ height: 462px;}
    .project_main{ bottom: 27px;}
    .iphcon{ position: absolute; top: 1px; left: -26px; width: 18px; height: 22px; margin-right: 7px;
        background-image: url(https://static.fangxiaoer.com/web/images/sy/house/phonez.png); background-size: 100%; background-repeat: no-repeat; background-position: center bottom; }
    .tel em{ font-size: 12px; font-family: Microsoft YaHei;font-weight: 400; color: #999999; line-height: 23px; display: block; margin-top: 10px;}
    .tel{ position: absolute; top: 35px; left: 26px;}
    .base li a{ padding: 3px 0px 3px 20px;}
    .zbkfDetailBTn {
        width: 80px;
        height: 80px;
        display: block;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -40px;
        margin-top: -40px;
        background-image: url(https://static.fangxiaoer.com/images/coin/liveBtn1.gif);
        background-size: 60% 60%;
        border: 1px solid #ccc;
        background-color: rgb(0 0 0 / 50%);
        border-radius: 40px;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 12;
    }
    .pic {
        width: 600px;
        float: left;
        position: relative;
        height: 490px;
    }
    #PicSlide .img {
        height: 400px;
        overflow: hidden;
    }
    #PicSlide .img li {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
    }
    #PicSlide .img img {
        width: 598px;
        height: 398px;
        border: 1px solid #dfe1e0;
        margin: 0;
    }
    #PicSlide .thumb {
        top: 400px;
        width: 600px;
        left: 1px;
        position: absolute;
        overflow: hidden;
        _zoom: 1;
        height: 90px;
    }
    #PicSlide .thumb ul {
        z-index: 20;
        width: 400px;
        overflow: hidden;
        _zoom: 1;
        margin-top: 8px;
        width: 600px;
    }
    #PicSlide .thumb li {
        width: 113px;
        height: 79px;
        float: left;
        _display: inline;
        text-align: left;
        position: relative;
        margin-right: 7px;
    }
    #PicSlide .thumb li:last-child {margin-right: 0}
    #PicSlide .thumb li img {
        width: 113px;
        height: 80px;
        margin: 0;
    }
    #PicSlide .thumb li p {
        position: absolute;
        bottom: 0px;
        width: 100%;
        height: 24px;
        line-height: 24px;
        text-align: center;
        z-index: 101;
        left: 1px;
        color: #fff;
    }
    #PicSlide .thumb li em {
        position: absolute;
        bottom: 0px;
        width: 100%;
        height: 24px;
        background: rgb(0, 0, 0);
        opacity: 0.6;
        z-index: 100;
        left: 1px;
    }
    .now-status{    bottom: 80px;
        top: auto;
        background-size: 100% 100%;
        width: 115px;}
</style>
<link href="https://static.fangxiaoer.com/web/images/sy/selection/g_sharing.css" rel="stylesheet" type="text/css"/>
<script src="https://static.fangxiaoer.com/web/images/sy/selection/stars.js"></script>
<script>
    $(document).ready(function(){
        function jqrcodeFn(d,w,h){
            var qrWidth = w;
            var qrHeight = h;
            var logoQrWidth = qrWidth / 4;
            var logoQrHeight = qrHeight / 4;
            var g_a='#'+d;
            var g_b='#'+d+' canvas';
            var g_c='.'+d;
            var g_qurl='https://m.fangxiaoer.com' //上线后 改为m站小二甄选楼盘评测信息页
            $(g_a).qrcode({
                render: "canvas",    //设置渲染方式，有table和canvas
                text: g_qurl,
                width: qrWidth, //二维码的宽度
                height: qrHeight //二维码的高度
            })
            $(g_b)[0].getContext('2d').drawImage($("#g_clogo")[0], (qrWidth - logoQrWidth) / 2, (qrHeight - logoQrHeight) / 2, logoQrWidth, logoQrHeight);
            $(g_c).show()
        }
        jqrcodeFn('g_qrcode','64','67')
        jqrcodeFn('g_hcode','130','130')

        $(".g_dhv").mouseover(function () {
            $(".g_hdcode").show()
        })
        $(".g_dhv").mouseleave(function () {
            $(".g_hdcode").hide();
        })
    })
</script>
<body class="w1210">
<img src="https://static.fangxiaoer.com/web/images/sy/selection/logo.png" id="g_clogo" style="display: none;"/>

<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>
<input id="pigeSize" type="hidden" value="3"/>
<input type="hidden" id="sessionId" th:value="${session.sessionId}">
<input type="hidden" id="memberType" th:value="${session.memberType}">
<input type="hidden" id="projectType" th:value="${projectType}">
<div th:include="house/detail/fragment_menu::menuVipUp" th:with="type=1"></div>
<div class="bg"
     th:style="'background: url('+${houseInfo.backImage}+') top no-repeat fixed'">
<!--    <div th:include="house/detail/fragment_activity::activity"></div>-->
    <div th:include="house/detail/fragment_activity::viewActivity"></div>
    <div th:include="house/detail/fragment_activity::projectPay"></div>
    <div class="modal-backdrop  in" id="loginzhezhao" style="display: none"></div>

    <div th:include="house/detail/fragment_activity::subsidyActivity"></div>

    <!--基本信息-->
    <div class="info">
        <!--<div class="info_l" style="position: relative;">
            <th:block th:if="${houseInfo.liveInfo.isLive eq '1'}">
                <div id="showbox2" style="position: absolute;">
                    <a th:href="${'/liveDetail/' + houseInfo.liveInfo.liveId + '.htm'}" target="_blank">
                        <div class="zbkfDetailBTn"></div>
                        <img th:src="${houseInfo.liveInfo.livePic}" th:alt="${houseInfo.projectName} + ${houseInfo.liveInfo.liveTitle}" width="600" height="400"/>
                    </a>
                </div>
            </th:block>
            <th:block th:if="${houseInfo.videoTabId ne '0'}" >
                <h1 id="player" class="flowplayer"  style="width: 600px;height: 400px" >
                    <video id="my-video" class="video-js vjs-big-play-centered" controls preload="auto" width="600px;" height="400px"
                           th:poster="${houseInfo.pic.get(0).url}" data-setup="{}">
                        <source  th:src="${houseInfo.mobileVideoUrl}">
                    </video>
                </h1>
            </th:block>
            <div id="showbox">
                &lt;!&ndash;<em></em>&ndash;&gt;
                    <img th:each="pic:${houseInfo.pic}" th:src="${pic.url}" th:alt="${houseInfo.projectName} + ${pic.title}"
                         width="600" height="400"/>
            </div>
            <div id="showbox1">
                <a th:each="pic:${houseInfo.pic}" th:if="${pic.title eq '全景'}" th:href="${'/house/'+projectId+'-'+projectType+'/pic720.htm'}" target="_blank">
                    <img th:src="${pic.url}" th:alt="${houseInfo.projectName} + ${pic.title}" width="600" height="400"/>
                </a>
            </div>
            &lt;!&ndash;展示图片盒子&ndash;&gt;
            <div id="showsum" >
                <p>
                    &lt;!&ndash;<span th:if="${houseInfo.liveInfo.isLive eq '1'}" th:data-id="${houseInfo.liveInfo.liveTitle}">
                        <img th:src="${houseInfo.liveInfo.livePic}" th:alt="${houseInfo.projectName} + ${houseInfo.liveInfo.liveTitle}"/><i>直播看房</i>
                    </span>&ndash;&gt;
                    <span th:each="pic,pi:${houseInfo.pic}" th:data-id="${pic.title}">
                        <img th:src="${pic.url}" th:alt="${houseInfo.projectName} + ${pic.title}"/><i th:text="${pic.title}"></i>
                         <th:block th:if="${houseInfo.videoTabId ne '0' and pi.index == 0}">
                            <img src="https://static.fangxiaoer.com/web/images/sy/house/littleVideoIcon.png" class="player_btn">
                        </th:block>
                    </span>
                <div class="now-status" style="left: 0px;"></div>
                </p>
                <div style="clear:both;width:100%;"></div>

            </div>
            <script >
                &lt;!&ndash;控制视频播放时能否切换图片&ndash;&gt;
                var pcVideoTab = [[${houseInfo.videoTabId}]];
                var panTab = [[${houseInfo.isPan}]]
                var isLive = [[${houseInfo.liveInfo.isLive}]]
                var videoClickState = true;
                var api;
                $(document).ready(function(){
                    api = $(".flowplayer");
                    //视频点击执行方法
                    api.on("click.player", function(e, api) {
                        videoClickState = false;
                    });
                    $("#showsum .now-status").click(function () {
                        for (var i=0;i<$("#showsum span").length;i++){
                            if($("#showsum span").eq(i).hasClass("sel")){
                                if(pcVideoTab != 0){
                                    if(i!=0){
                                        videoClickState = true;
                                        $("#player").hide()
                                        if(panTab != 0 && i==1){
                                            $('#showbox1').show();
                                            $('#showbox>div').hide();
                                        }else{
                                            $('#showbox>div').show();
                                            $('#showbox1').hide();
                                        }
                                        videojs("my-video").load();
                                    }
                                }else{
                                    videoClickState = true;
                                    $("#player").hide()
                                    if(panTab != 0 && i==0){
                                        $('#showbox1').show();
                                        $('#showbox>div').hide();
                                    }else{
                                        $('#showbox>div').show();
                                        $('#showbox1').hide();
                                    }
                                }
                            }
                        }
                    });
                });
            </script>
        </div>
     -->
        <div class="pic" id="PicSlide">
            <div th:if="${houseInfo.hasLabel eq '1'}" class="instantDetail"><img th:src="${houseInfo.labelContent.advertisePC}" alt=""></div>
            <ul class="img">
                <li th:each="p,pi:${houseInfo.pic}" th:if="${pi.index lt 5}" th:style="'display:' + ${pi.index eq 0 ? 'list-item;' : 'none'}">
                    <th:block th:if="${pi.index == 0 and houseInfo.liveInfo.isLive eq '1'}">
                        <a th:href="${'/liveDetail/' + houseInfo.liveInfo.liveId + '.htm'}">
                            <div class="zbkfDetailBTn"></div>
                            <img th:src="${houseInfo.liveInfo.livePic}" th:alt="${houseInfo.liveInfo.liveTitle}"/>
                        </a>
                    </th:block>
                    <th:block th:if="${houseInfo.isPan ne '0' and ((houseInfo.liveInfo.isLive eq '0' and pi.index == 0) or (houseInfo.liveInfo.isLive eq '1' and pi.index == 1))}">
                        <a th:href="${'/house/'+projectId + '-' + projectType + '/pic720.htm'}">
                            <img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
                            <div class="vrs" th:if="${p.title eq '全景'}"><i></i><b></b></div>
                        </a>
                    </th:block>
                    <th:block th:if="${houseInfo.videoTabId ne '0' and ((houseInfo.liveInfo.isLive eq '0' and houseInfo.isPan eq '0' and pi.index == 0) or
                     (houseInfo.liveInfo.isLive eq '1' and houseInfo.isPan eq '0' and pi.index == 1) or
                      (houseInfo.liveInfo.isLive eq '0' and houseInfo.isPan ne '0' and pi.index == 1) or
                       (houseInfo.liveInfo.isLive eq '1' and houseInfo.isPan ne '0' and pi.index == 2))}">
                        <div id="player" style="width: 600px;height: 400px">
                            <video id="my-video" class="video-js vjs-big-play-centered" controls preload="auto"
                                   width="600px;" height="400px" th:poster="${p.url}" data-setup="{}">
                                <source th:src="${houseInfo.mobileVideoUrl}">
                            </video>
                        </div>
                    </th:block>
                    <th:block th:if="${((houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId eq '0' and houseInfo.isPan eq '0') and (pi.index == 0 or pi.index == 1 or pi.index == 2))
                                    or (((houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId eq '0' and houseInfo.isPan ne '0') or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId eq '0' and houseInfo.isPan eq '0')
                                    or (houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0')) and (pi.index == 1 or pi.index == 2))
                                    or (((houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0') or
                                    (houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0')
                                    or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId eq '0' and houseInfo.isPan ne '0')) and (pi.index == 2))}">
                        <a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
                            <img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
                        </a>
                    </th:block>
                    <th:block th:if="${pi.index != 0 and pi.index != 1 and pi.index != 2}">
                        <a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
                            <img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
                        </a>
                    </th:block>
                </li>
            </ul>
            <div class="thumb">
                <ul>
                    <li th:each="p,pi:${houseInfo.pic}" th:if="${pi.index lt 5}">
                        <img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
                        <p th:text="${((houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0' and houseInfo.liveInfo.isLive eq '0' and pi.index == 0)
                        or (houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0' and houseInfo.liveInfo.isLive eq '1' and pi.index == 1) or
                         (houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0' and houseInfo.liveInfo.isLive eq '0' and pi.index == 1) or
                          (houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0' and houseInfo.liveInfo.isLive eq '1' and pi.index == 2)) ? '视频' : p.title}"></p>
                        <em></em>
                    </li>
                </ul>
                <div class="now-status" style="left: 0px;">
                </div>
            </div>
            <div class="cl">
            </div>
            <div th:if="${#lists.size(houseInfo.pic) gt 1}" id="PicslidePrev" style="z-index: 10000"></div>
            <div th:if="${#lists.size(houseInfo.pic) gt 1}" id="PicslideNext" style="z-index: 10000"></div>
            <script>
                // 视频对象
                var videoTabId = $("#videoTabId").val()
                if (videoTabId != 0 ){
                    var slideVideo = videojs("my-video");

                }

                // 小图hover切换
                $(".thumb li").hover(function () {
                    var dex = $(this).index();
                    $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                    $(".now-status").css("left", dex * 120 + "px");
                    slideVideo.pause();
                })

                $(function () {
                    // 下
                    $("#PicslideNext").click(function () {
                        var dex = $("#PicSlide").find(".img").find("li[style='display: list-item;']").index() + 1;
                        dex = dex >= $("#PicSlide .img>li").length ? 0 : dex;
                        $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                        $(".now-status").css("left", dex * 122 + "px");
                        slideVideo.pause();
                    })
                    // 上
                    $("#PicslidePrev").click(function () {
                        var dex = $("#PicSlide").find(".img").find("li[style='display: list-item;']").index() - 1;
                        dex = dex < 0 ? ($("#PicSlide .img>li").length - 1) : dex;
                        $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                        $(".now-status").css("left", dex * 122 + "px");
                        slideVideo.pause();
                    })
                })
            </script>
        </div>

       <div class="info_r">
            <div th:if="!${#strings.toString(houseInfo.projectStatus) eq '3'}" class="price">
                <p>参考价格</p>
                <ul th:if="${#lists.size(houseInfo.price) lt 4}" class="type-Btn">
                    <s th:if="${houseInfo.price} == null or ${#arrays.length(houseInfo.price)} == 0" style="color:#ff5200">待定</s>
                    <li th:each="price:${houseInfo.price}" th:class="${price.type eq '均价'}?'jun':'qi'">
                        <span th:text="${price.name}"></span><i th:text="${#numbers.formatInteger( price.money,3)}"></i>
                        <th:block th:text="${#strings.replace(price.showPrice, #numbers.formatInteger( price.money,3), '')}"></th:block>
                        <em></em>
                    </li>
                    <p onclick="showUnuseCode(1)" value="" class="getChangePzBtn" th:if="${houseInfo.projectStatus ne '3'}" style="margin-top: 6px; right: 59px;"><i></i>获取最新价格变动</p>
                    <div class="cl"></div>
                </ul>
                <ul th:if="${#lists.size(houseInfo.price) gt 3}" class="type-Btn">
                    <s th:if="${houseInfo.price} == null or ${#arrays.length(houseInfo.price)} == 0">待定</s>
                    <li th:each="price,i:${houseInfo.price}" th:if="${i.index lt 1}" th:class="${price.type eq '均价'}?'jun':'qi'">
                        <span th:text="${price.name}"></span><i th:text="${#numbers.formatInteger( price.money,3)}"></i>
                        <th:block th:text="${#strings.replace(price.showPrice, #numbers.formatInteger( price.money,3), '')}"></th:block>
                        <em></em>
                        <em class="triangle" style="background-image: url('https://static.fangxiaoer.com/web/images/sy/house/view/arrowxia.png');background-position: 0 10px"></em>
                    </li>
                    <div class="layer">
                        <li th:each="price,i:${houseInfo.price}" th:if="${i.index gt 0}" th:class="${price.type eq '均价'}?'jun':'qi'">
                            <span th:text="${price.name}"></span><i th:text="${#numbers.formatInteger( price.money,3)}"></i>
                            <th:block
                                    th:text="${#strings.replace(price.showPrice, #numbers.formatInteger( price.money,3), '')}"></th:block>
                            <em></em>
                        </li>
                        <img src="" alt="">
                    </div>
                    <p onclick="showUnuseCode(1)" value="" class="getChangePzBtn" th:if="${houseInfo.projectStatus ne '3'}" style="margin-top: 6px;"><i></i>获取最新价格变动</p>
                    <div class="cl"></div>
                    <script>
                        $(".price ul li:eq(0)").mouseenter(
                            function(){
                                $(".layer").show()
                            }
                        )
                        $(".price ul li:eq(0)").mouseleave(
                            function () {
                                $(".layer").hide()
                            }
                        )
                    </script>
                </ul>
                <div class="cl"></div>
            </div>
            <!--售罄-->
            <div th:if="${#strings.toString(houseInfo.projectStatus) eq '3'} " class="price">
                <p th:if="!${#maps.isEmpty(houseInfo.subPrice)}" >参考价格：</p>
                <ul th:if="!${houseInfo.subPrice eq null} and !${#maps.isEmpty(houseInfo.subPrice)}">
                    <li  class="jun" th:if="${#strings.toString(#numbers.formatInteger( houseInfo.subPrice.price,0)) ne '0'}">
                        <span >二手房</span><i th:text="${#numbers.formatInteger( houseInfo.subPrice.price,0)}"></i>元/m²
                        <img src="https://static.fangxiaoer.com/web/images/ico/sign/price_2.gif" alt="价格">
                        <a th:href="${'/saleVillages/'+houseInfo.subPrice.subId+'/index.htm'}" target="_blank">
                            <span class="priceCk">查看二手房源 ></span>
                        </a>
                    </li>
                    <li  class="jun" th:if="${#strings.toString(#numbers.formatInteger( houseInfo.subPrice.price,0)) eq '0'}">
                        <span>——</span><i></i>
                        <a th:href="${'/saleVillages/'+houseInfo.subPrice.subId+'/index.htm'}" target="_blank">
                            <span class="priceCk">查看二手房源 ></span>
                        </a>
                    </li>

                    <div class="cl"></div>
                </ul>
                <ul th:if="${houseInfo.subPrice eq null} or ${#maps.isEmpty(houseInfo.subPrice)}">
                    <li>
                        <i>新房已售完</i>
                        <em></em>
                    </li>
                    <div class="cl"></div>
                </ul>
                <div class="cl"></div>
            </div>
            <div class="base">
                <ul>
                    <li><p>主力户型</p>
                        <th:block  th:if="${#lists.isEmpty(houseInfo.mainLayout)}">暂无主力户型</th:block>
                        <th:block th:each="mainLayout,i:${houseInfo.mainLayout}" th:text="${mainLayout.mainLayout}+' '" th:if="${i.index lt 3}"></th:block>
                        <a class="Huxing_sun"  th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '.htm'}">全部户型图</a>
                    </li>
                    <li th:if="${!#lists.isEmpty(houseInfo.openTime)}">
                        <i style="float: left; color: #999;margin-left: 18px;margin-right: 8px"> 最新开盘</i>
                        <p style="color: #333;max-width: 260px" th:class="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) gt 1?'':'win'}">
                            <th:block th:each="lic,licindex:${#strings.setSplit(houseInfo.openTime.get(0).dyTime,'.')}"  th:text="${licindex.index==0? lic+'年':(licindex.index==1?lic+'月':lic+'日'+houseInfo.openTime.get(0).dyDesc)}"></th:block>
                        </p>
                        <div class="layer_sun" name="layer_sun" th:if="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) eq 1}">
                            <div class="ms_sanjiao dt_sj"></div>
                            <th:block th:each="lic,licindex:${#strings.setSplit(houseInfo.openTime.get(0).dyTime,'.')}"  th:text="${licindex.index==0? lic+'年':(licindex.index==1?lic+'月':lic+'日'+houseInfo.openTime.get(0).dyDesc)}"></th:block>
                        </div>
                        <a th:style="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) gt 1?'display: black':'display: none'}" class="infoIcontime"><s></s>开盘时间
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody>
                                <tr>
                                    <th colspan="2" th:text="${houseInfo.projectName+'开盘时间'}"></th>
                                </tr>
                                <tr>
                                    <td width="13%">开盘时间</td>
                                    <td width="37%">开盘详情</td>
                                </tr>
                                <tr th:each="e:${houseInfo.openTime}">
                                    <td  th:text="${e.dyTime}"></td>
                                    <td  th:text="${e.dyDesc}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </a>
                        <a th:unless="${houseInfo.resBrand.wayDes ne '' or (houseInfo.resBrand.Brand ne '' and houseInfo.resBrand.Brand ne '暂无资料')}" th:style="${!#lists.isEmpty(houseInfo.existinfo) and #lists.size(houseInfo.existinfo) ne 0?'display: black':'display: none'}" class="infoIcontime"><s></s>交房时间
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody>
                                <tr>
                                    <th colspan="2" th:text="${houseInfo.projectName+'交房时间'}"></th>
                                </tr>
                                <tr>
                                    <td width="13%">交房时间</td>
                                    <td width="37%">交房详情</td>
                                </tr>
                                <tr th:each="e:${houseInfo.existinfo}">
                                    <td  th:text="${e.inTime}"></td>
                                    <td  th:text="${e.description}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </a>
                    </li>

                    <li th:style="${houseInfo.resBrand.wayDes ne '' or (houseInfo.resBrand.Brand ne '' and houseInfo.resBrand.Brand ne '暂无资料')}?'display:block':'display:none'" class="view_xmts">
                        <p>项目特色</p>
                        <!--<span class="view_ts2" th:style="${houseInfo.projectState ne ''?'display:block':'display:none'}"  th:text="${houseInfo.projectState}">在售-->
                            <!--<div class="miaoshu dt_ms" >-->
                                <!--<div class="ms_sanjiao dt_sj"></div>-->
                                <!--<th:block: th:text="${houseInfo.projectState}"></th:block:>-->
                            <!--</div>-->
                            <!--</span>-->
                        <span class="view_ts1"
                              th:style="${houseInfo.baseInfo.stationName ne '' and houseInfo.baseInfo.stationName ne '暂无资料'}?'display:block':'display:none'">地铁
                            <div class="miaoshu dt_ms" th:if="${!#strings.isEmpty(houseInfo.resBrand.wayDes)}">
                                <div class="ms_sanjiao dt_sj"></div>
                                <th:block: th:text="${houseInfo.resBrand.wayDes}"></th:block:>
                            </div>
                            </span>
                        <span class="view_ts4"
                              th:style="${houseInfo.resBrand.Brand ne '' and houseInfo.resBrand.Brand ne '暂无资料'}?'display:block':'display:none'">品牌
                            <div class="miaoshu pp_ms">
                                <div class="ms_sanjiao pp_sj"></div>
                             <th:block: th:text="${houseInfo.resBrand.Brand}"></th:block:>
                            </div>
                            </span>
                        <a th:if="${houseInfo.resBrand.wayDes ne '' or (houseInfo.resBrand.Brand ne '' and houseInfo.resBrand.Brand ne '暂无资料')}" th:style="${!#lists.isEmpty(houseInfo.existinfo) and #lists.size(houseInfo.existinfo) ne 0?'display: black':'display: none'}" class="infoIcontime"><s></s>交房时间
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody>
                                <tr>
                                    <th colspan="2" th:text="${houseInfo.projectName+'交房时间'}"></th>
                                </tr>
                                <tr>
                                    <td width="13%">交房时间</td>
                                    <td width="37%">交房详情</td>
                                </tr>
                                <tr th:each="e:${houseInfo.existinfo}">
                                    <td  th:text="${e.inTime}"></td>
                                    <td  th:text="${e.description}"></td>
                                </tr>
                                </tbody>
                            </table>
                        </a>
                        <div style="clear: both"></div>
                        <div class="view_desc">
                        </div>
                    </li>


                    <li><p>项目地址</p>
                        <span class="show"><th:block th:text="${houseInfo.projectAdderss}"></th:block></span>
                        <a href="#map" class="map_sun">查看地图</a>
                        <div class="layer_sun" name="layer_sun">
                            <div class="ms_sanjiao dt_sj"></div>
                            <th:block th:text="${houseInfo.projectAdderss}"></th:block>
                        </div>
                    </li>
                    <!--<li th:if="${#lists.isEmpty(houseInfo.openTime)}"><p>售楼地址</p>-->

                        <!--<span class="show"> <th:block th:text="${houseInfo.saleAddress}"></th:block></span>-->
                        <!--<div class="layer_sun" name="layer_sun">-->
                            <!--<div class="ms_sanjiao dt_sj"></div>-->
                            <!--<th:block th:text="${houseInfo.saleAddress}"></th:block>-->
                        <!--</div>-->
                    <!--</li>-->
                    <li><p>交通信息</p>
                        <span class="show show_sun" id="traffic">   <th:block th:text="${#strings.abbreviate(houseInfo.traffic, 47)}"></th:block></span>
                        <div class="layer_sun" name="layer_sun" id="seemore">
                            <div class="ms_sanjiao dt_sj"></div>
                            <th:block th:text="${houseInfo.traffic}"></th:block>
                        </div>
                        <a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}" class="seemore" th:if="${!#strings.isEmpty(houseInfo.brandId) or !#maps.isEmpty(houseInfo.rank)}">更多详细信息></a>
                    </li>
                    <div class="cl"></div>
                    <div class="newHouseInfoRicn">
                        <a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}" class="seemore" th:unless="${!#strings.isEmpty(houseInfo.brandId) or !#maps.isEmpty(houseInfo.rank)}">更多详细信息></a>
                        <div class="iconDiv" th:if="${!#strings.isEmpty(houseInfo.brandId) or !#maps.isEmpty(houseInfo.rank)}">
                            <a th:if="${!#strings.isEmpty(houseInfo.brandId)}" th:href="${'/brandCompany/'+houseInfo.brandId+'.htm'}" class="pinpai" target="_blank"><i class="barnd_icon barnd_icon_2024"></i><span th:text="${#strings.length(houseInfo.brandName)  gt 10 ?'品牌展示区': houseInfo.brandName}"></span></a>
                            <a th:if="${!#maps.isEmpty(houseInfo.rank)}" th:href="${'/projectRank/'+houseInfo.rank.rankType}" class="bangdan"><i class="rank_icon rank_icon_2024"></i><span th:text="${houseInfo.rank.rankTypeName+'第'+houseInfo.rank.rankCount+'名'}"></span></a>
                        </div>
                        <a class="soucang" target="_blank" data-toggle="modal" href="#login"><i></i>收藏</a>

                        <a class="soucang">

                            <!--<p class="contrastNo" style="display: none;color: red;">+对比</p>-->
                            <script>
                                $(".seemore").mouseover(function () {
                                    $("#seemore").hide();
                                });
                                $("#traffic").mouseover(function () {
                                    $("#seemore").show();
                                })
                                $("#traffic").mouseleave(function () {
                                    $("#seemore").hide();
                                })
                                $(function () {
                                    $(".contrast").click(function () {
                                        var txt = $(this).attr("value");
                                        var id = $(this).attr("data-id");
                                        console.log(txt)
                                        console.log(id)
                                        $.ajax({
                                            type: "post",
                                            data: { houseId : id,houseName:txt, houseType : 1},
                                            url: "/addContrastHouse",
                                            //dataType: 'json',
                                            async: false,
                                            success: function (list) {
                                                window.open("https://sy.fangxiaoer.com/gotoContrastHouse")
                                                // window.open("http://*************:8082/gotoContrastHouse")

                                            },
                                        });
                                    })
                                })
                            </script>
                        </a>
                        <p class="contrast"  th:value="${houseInfo.projectName}" th:data-id="${projectId}" style="float: right">对比</p>
                        <div class="cl"></div>
                    </div>
                </ul>
            </div>
            <div class="ourPhone newOurPhone" style="position: relative; height: 169px;">
                <div class="tel">
                    <div class="iphcon"></div>
                    <span class="telDesc" style="font-size:12px;color:#999999; padding-left: 0px; font-weight: normal" th:text="${houseInfo.projectStatus ne '3'? '咨询电话:':'咨询电话:'}">咨询电话:</span>
                    <!--<span th:text="${'更新时间 : '+saleInfo[0].addTime.year}+'-'+${saleInfo[0].addTime.month}+'-'+${saleInfo[0].addTime.day}"></span>-->
                    <!--<p th:text="${houseInfo.sortelTel}+'&nbsp;'"></p>-->
                    <span class="phonenum" th:utext="${#strings.contains(houseInfo.sortelTel,'转')?#strings.replace(houseInfo.sortelTel,'转','<b>转</b>') : houseInfo.sortelTel}"></span>
                    <em>如需帮助请拨打平台服务电话: ************</em>
                    <div class="cl"></div>
                </div>
                <!--<div class="tel2">联系房小二网客服<p>转11100</p></div>-->
                <!--<div class="Sales" th:if="${!#lists.isEmpty(houseInfo.openTime)}">-->
                    <!--<h4>售楼地址</h4>-->
                    <!--<div class="layer_sun">-->
                        <!--<div class="ms_sanjiao dt_sj"></div>-->
                        <!--<th:block th:text="${houseInfo.saleAddress}"></th:block>-->
                    <!--</div>-->
                <!--</div>-->
                <div class="cl"></div>
                <div class="project_main">
                    <img id="project_image" src=""/>
                    <p style="font-size: 12px">微信扫码打电话</p>
                </div>
                <div class="cl">
                </div>
                <script th:inline="javascript">
                    /*<![CDATA[*/
                    var scene = 'newTel,' +[[${projectId}]]+'-'+[[${projectType}]]+',1,' + [[${#strings.contains(houseInfo.sortelTel,'转')?#strings.replace(houseInfo.sortelTel,'转', ',') : houseInfo.sortelTel}]];
                    var img = "";
                    var sss;
                    $.ajax({
                        type: "GET",
                        async: false,
                        url:  "/getWxACode",
                        data:{"scene": scene},
                        dataType : 'json',
                        headers : {
                            'Content-Type' : 'application/json;charset=utf-8'
                        },
                        success: function (data) {
                            img = data.img;
                            sss = data;
                        }
                    });
                    $("#project_image").attr("src","data:text/html;base64,"+img);
                    /*]]>*/
                </script>
            </div>

            <!--<div class="base_btn" th:if="${houseInfo.projectStatus ne '3'}">
                <a id='yykf'>
                    <input name='' type='button' onclick="showUsercode(1)" value='免费专车' class='button btn_1 newYykf' />
                    &lt;!&ndash;<input name='' type='button' onclick="showUsercode(2)" value='免费通话' class='button btn_1' />&ndash;&gt;
                </a>
            </div>-->
        </div>
        <script>

            for (var i=0; i<$(".base .layer_sun[name=layer_sun]").length;i++){
                var text_x=$(".base .layer_sun[name=layer_sun]").eq(i).height();
                if(text_x>50){
                    $(".base .layer_sun[name=layer_sun]").eq(i).css("line-height","25px")
                    $(".info .layer_sun[name=layer_sun]").eq(i).css("padding"," 7px")
                }
            }
        </script>
        <div class="cl"></div>
    </div>

    <!--小二甄选-->
    <div class="g_ezx" th:style="${#strings.toString(selectionInfo.selectionState) eq '1'}?'':'display: none'">
        <div class="g_elogo"></div>
        <div class="g_etag">
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i1.png"><span>品牌</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i2.png"><span>交通</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i3.png"><span>潜力</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i4.png"><span>配套</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i5.png"><span>环境</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i6.png"><span>户型</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i7.png"><span>物业</span></div>
            <div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i8.png"><span>性价比</span></div>
        </div>
        <div class="g_etxt"><i>真实房源</i><i>扫码查看</i></div>
        <div class="g_qrcode"><i id="g_qrcode"></i><!--<em></em>--></div>
    </div>

    <!--置业顾问-->
    <div class="" th:if="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
        <div class="newHouseViewChunk">
            <div class="title">
                <P>经纪人</P>

                <div class="sy-dynatown-ararow">
                    <span id="up" style="transform: rotate(180deg)"></span>
                    <span id="down"></span>
                </div>
            </div>
            <div class="w sy-dynatown">
                <div th:each="agent:${houseInfo.agents}" class="sy-single new-sy-single">
                    <div class="news-touxiang">
                        <img th:src="${agent.agentPic}" th:alt="${agent.memberName}"/>
                        <div class="On-line">
                            <p>在线</p>
                            <i class="On-line-icon"></i>
                        </div>
                    </div>
                    <div class="news-name">
                        <span th:text="${agent.memberName}"></span>
                        <div class="name-btn">
                            <a th:href="${#session?.getAttribute('sessionId') ne null?'/im/'+ projectId + '-0-' +agent.memberId:('#login')}"  th:data-toggle="${#session?.getAttribute('sessionId') ne null?'':'modal'}" target="_blank">
                                <span class="liaogbei-btn">咨询</span>
                            </a>
                            <div class="call-btn" th:data="${agent.memberId}">
                                <span class="show-call">打电话</span>
                                <div class="show-ewm">
                                    <img th:id="${'agent_mobile'+agent.memberId}"  >
                                    <p>微信扫码打电话</p>
                                    <p th:text="${#strings.replace(agent.unionTel,',','转')}"></p>
                                    <input th:id="${'agent_phone'+agent.memberId}"  type="hidden" th:value="${'tel,'+projectId+'-'+projectType+',1,'+agent.unionTel}"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <script src="https://static.fangxiaoer.com/js/new4-dynatown.js" type="text/javascript"></script>
    </div>

    <script th:inline="javascript">
        var g_starb=[[${selectionInfo.totalScore}]]
    </script>
    <!--楼盘价值测评-->
    <div class="g_assess" th:style="${#strings.toString(selectionInfo.selectionState) eq '1'}?'':'display: none'">
        <div class="title">
            <p th:text="${houseInfo.projectName + '楼盘价值测评'}"></p>
            <div class="layoutTypeR">
                <a class="g_dhv">查看报告</a>
            </div>
            <div class="g_hdcode">
                <div class="g_hcode" id="g_hcode"></div>
                <div class="g_ctx"><i>真实房源,扫码查看</i></div>
                <em></em>
            </div>
        </div>
        <div class="g_evalu">
            <div class="g_leva">
                <div class="g_leva_t">楼盘测评多维度分析</div>
                <div class="g_leva_m">
                    <div class="show_number">
                        <li>
                            <span th:text="${selectionInfo.totalScore} + '分'"></span>
                            <div class="atar_Show">
                                <!--<p th:tip="${selectionInfo.totalScore}" th:style="'width:' +${selectionInfo.totalScore}+'px;'"></p>-->
                                <div class="g_stara"></div>
                                <div class="g_starb" th:style="'width:' +${selectionInfo.totalScore}+'px'"></div>
                            </div>
                        </li>
                    </div>
                </div>
            </div>
            <div class="g_reva">
                <div class="g_rli">
                    <div class="g_rf"><em>交通位置</em><em th:text="${selectionInfo.trafficScore}+'分'"></em></div>
                    <div class="g_rf"><em>户型设计</em><em th:text="${selectionInfo.unitScore}+'分'"></em></div>
                </div>
                <div class="g_rli">
                    <div class="g_rf"><em>外围配套</em><em th:text="${selectionInfo.externalResourceScore}+'分'"></em></div>
                    <div class="g_rf"><em>开发品牌</em><em th:text="${selectionInfo.brandScore}+'分'"></em></div>
                </div>
                <div class="g_rli">
                    <div class="g_rf"><em>内部配套</em><em th:text="${selectionInfo.internalResourceScore}+'分'"></em></div>
                    <div class="g_rf"><em>价值潜力</em><em th:text="${selectionInfo.valueScore}+'分'"></em></div>
                </div>
                <div class="g_rli">
                    <div class="g_rf"><em>园区环境</em><em th:text="${selectionInfo.environmentScore}+'分'"></em></div>
                    <div class="g_rf"><em>装修品质 </em><em th:text="${selectionInfo.decorateScore}+'分'"></em></div>
                </div>
                <div class="g_rli">
                    <div class="g_rf"><em>物业管理</em><em th:text="${selectionInfo.propertyScore}+'分'"></em></div>
                    <div class="g_rf"><em>性价比</em><em th:text="${selectionInfo.costPerformanceScore}+'分'"></em></div>
                </div>
            </div>
            <div class="g_clbo"></div>
        </div>
    </div>

    <!--认购入口-->
    <div id="subscrip" th:include="activity/subscription::subscription"></div>
    <!--广告切换-->
    <div class="swiper-container" style="width: 1200px">
        <div class="swiper-wrapper">
            <div class="swiper-slide"
                 th:style="'height: 400px;float:left;background: url('+${houseInfo.headPic1}+') no-repeat ; background-size:100% 100%'">
                <div><span>广告</span></div>
            </div>
            <div class="swiper-slide" th:if="${!#strings.isEmpty(houseInfo.headPic2)}"
                 th:style="'height: 400px;float:left;background: url('+${houseInfo.headPic2}+') no-repeat ;background-size:100% 100%'">
                <div><span>广告</span></div>
            </div>
        </div>
        <div class="swiper-pagination" th:if="${!#strings.isEmpty(houseInfo.headPic2)}"></div>
    </div>
    <script th:inline="javascript">
        var headPic2 = [[${houseInfo.headPic2}]];
        if (headPic2 != null && headPic2 != "") {
            var mySwiper = new Swiper('.swiper-container', {
                autoplay: 5000,//可选选项，自动滑动
                pagination: '.swiper-pagination',
            })
        }

        $(".seemore").mouseover(function () {
            $("#seemore").hide();
        });
        $("#traffic").mouseover(function () {
            $("#seemore").show();
        })
        $("#traffic").mouseleave(function () {
            $("#seemore").hide();
        })
    </script>
    <div class="newHouseViewChunk jieshao">
        <div th:include="house/detail/fragment_menu::menuVipDown"  th:with="type=1"></div>
    </div>





    <!--深度解析-->
    <div class="newHouseViewChunk" th:if="${!#maps.isEmpty(houseInfo.deepNews)}">
        <div class="deepAnalysis">

            <div class="analysisTitle">
                <h1>深度解析</h1>
            </div>

            <div class="analysisInfo">

                <div class="analysisInfoImg">
                    <a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank">
                        <img th:src="${#strings.isEmpty(houseInfo.deepNews.pic)?'':houseInfo.deepNews.pic}" th:alt="${#strings.isEmpty(houseInfo.deepNews.projectName)?'':houseInfo.deepNews.projectName}" width="180px" height="134px"/>
                    </a>
                </div>

                <div class="analysisInfoTxt">
                    <a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank">
                        <h2 th:text="${#strings.isEmpty(houseInfo.deepNews.titleShow)?'':houseInfo.deepNews.titleShow}" ></h2>
                    </a>
                    <a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank">
                        <p th:text="${#strings.isEmpty(houseInfo.deepNews.description)?'':houseInfo.deepNews.description}">
                        </p>
                    </a>
                </div>
                <div class="cl"></div>
            </div>
        </div>
    </div>

    <!--区位分析-->
    <div class="newHouseViewChunk quwei" th:if="${!#strings.isEmpty(houseInfo.sitePic)}">
        <div class="w" style="padding-bottom: 35px">
            <div class="title_sun">
                <h1 style="padding-left: 15px">区位分析</h1>
            </div>
            <div class="quwei_left">
                <img th:src="${houseInfo.sitePic}" >
            </div>
            <div class="quwei_right">
                <div class="quwei_name">
                    <h1>区位图介绍</h1>
                </div>
                <!--140-180字-->
                <p th:text="${#strings.isEmpty(houseInfo.siteDesc)?'':houseInfo.siteDesc }"></p>
            </div>
            <div class="cl"></div>
        </div>
    </div>
    <div class="newHouseViewChunk xuanqu" th:if="${houseInfo.schoolInfo ne null and #lists.size(houseInfo.schoolInfo) > 0}">

        <div  class="w"  th:style="${school ne ''}?'display:block':'display:none'">
            <div class="title" th:if="${houseInfo.schoolInfo ne null and #lists.size(houseInfo.schoolInfo) > 0}">
                <p>学区简介</p>
            </div>
            <ul>
                <th:block th:each="school,i:${houseInfo.schoolInfo}">
                    <li>
                        <div class="xqjj_con">
                            <div class="xqjj_con_r">
                                <div id='xqjj_0' class="carousel xqjj_caus">
                                    <!-- Carousel items -->
                                    <div class="carousel-inner xq_imgs">
                                        <div class="active item">
                                            <img th:src="${school.picUrl}" th:alt="${school.schoolAreaName}"/>
                                            <div class="xqjj_item_bg"></div>
                                            <p th:text="${school.schoolAreaName}"></p>
                                        </div>
                                    </div>
                                    <!-- Carousel nav -->
                                    <!--<a class="carousel-control left" href="#xqjj_0" data-slide="prev"></a>-->
                                    <!--<a class="carousel-control right" href="#xqjj_0" data-slide="next"></a>-->
                                </div>
                            </div>
                            <div class="xqjj_con_l">
                                <div class="xqjj_name">
                                    <th:block th:text="${school.schoolAreaName}"></th:block>
                                    <span th:if="${school.schoolType}"
                                          th:text="${#strings.isEmpty(school.schoolType)? '': school.schoolType}"></span>
                                    <span th:if="${school.scale}"
                                          th:text="${#strings.isEmpty(school.scale)? '': school.scale}"></span>
                                    <!--<span class="xq_name_r">-->
                                    <!--<span class="view_icon"></span>-->
                                    <!--<span class="xq_tel">电话</span>-->
                                    <!--<th:block th:text="${school.telephone}"></th:block>-->
                                    <!--</span>-->
                                </div>
                                <div class="xqjj_qy" th:utext="${school.description1}"></div>
                                <div class="xqjj_address"><span class="xq_tel">地址：</span>
                                    <th:block th:text="${school.address}"></th:block>
                                </div>
                                <div class="xqjj_dir"><span class="show"><span class="xq_tel">概述：</span>
                                <th:block th:utext="${school.description2}"></th:block>
                                 <div class="layer_sun">
                                    <div class="ms_sanjiao dt_sj"></div>
                                    <th:block th:text="${school.description2}"></th:block>
                                </div>
                            </span>

                                </div>
                            </div>
                        </div>
                    </li>

                </th:block>
            </ul>
        </div>
    </div>
    <!--学区JS-->
    <script>
        $(".xqjj_dir .show").mouseenter(
            function(){
                $(this).find(".layer_sun").show();
            }
        )
        $(".xqjj_dir .show").mouseleave(
            function(){
                $(this).find(".layer_sun").hide();
            }
        )
    </script>
    <!--项目优势-->
    <div class="main "
         th:style="${houseInfo.advantage ne null and houseInfo.advantage ne ''}?'display: block':'display: none'">
        <div class="w">
            <div class="title"><p>项目优势</p></div>
            <div class="pic" th:utext="${houseInfo.advantage}"></div>
        </div>
    </div>
    <!--在线选房+周边配套+参与月供-->
    <div class="main ">
        <!--在线选房-->
        <!--<div th:include="house/detail/fragment_online::online"></div>-->
        <!--周边配套-->
        <div th:include="house/detail/fragment_map::map2"></div>
        <!--参与月供-->
        <script>
            $(function () {
                if ($(".pro_name p").html() == "汇置尚都") {
                    $(".ttt,.yinchang").hide()
                }
            })
        </script>
        <div class="cl"></div>
    </div>
    <div  class="main " th:if="${!(houseInfo.price == null or #arrays.length(houseInfo.price) == 0 or #lists.isEmpty(layoutDetail))}">
        <div class="w"  th:include="house/detail/fragment_calculator::calculator"></div>
    </div>
    <!--项目点评+楼盘问答-->
    <div class="main " id="xmpj" style="overflow: hidden;" th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
        <!--项目点评-->
        <div th:include="house/detail/fragment_ask::xmpjNew"></div>

    </div>
    <div class="main " style="overflow: hidden;" th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
        <!--楼盘问答-->
        <div th:include="house/detail/fragment_ask::xmzx"></div>
        <div th:include="house/detail/fragment_order::forAsk"></div>
        <div class="cl"></div>
    </div>
    <div class="main">
            <div th:include="house/detail/fragment_relasion_house::normalHouse_push"></div>
    </div>
    <!--底部-->
    <div th:include="fragment/fragment::footer_detail"></div>
    <!--统计-->
    <div th:include="fragment/fragment::tongji"></div>
    <!--右侧浮标-->
    <th:block th:if="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
        <div th:include="fragment/fragment::commonFloat" th:with="commonType=1"></div>
    </th:block>
    <th:block th:unless="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
        <div th:include="fragment/fragment::commonFloat"></div>
    </th:block>
    <div class="cl"></div>
</div>

<script type="text/javascript">


    $(".view_xmts span").bind("mouseover", function () {
        $(".miaoshu").hide();
        $(this).find(".miaoshu").show();
    })
    $(".view_xmts span").bind("mouseleave", function () {
        $(".miaoshu").hide();
    })

    $(document).ready(function () {
        var showproduct = {
            "boxid": "showbox",
            "sumid": "showsum",
            "boxw": 600,//宽度,该版本中请把宽高填写成一样
            "boxh": 400,//高度,该版本中请把宽高填写成一样
            "sumw": 80,//列表每个宽度,该版本中请把宽高填写成一样
            "sumh": 80,//列表每个高度,该版本中请把宽高填写成一样
            "sumi": 0,//列表间隔
            "sums": 5,//列表显示个数
            "sumsel": "sel",
            "sumborder": 0,//列表边框，没有边框填写0，边框在css中修改
            "lastid": "showlast",
            "nextid": "shownext"
        };//参数定义
        $.ljsGlasses.pcGlasses(showproduct);//方法调用，务必在加载完后执行
        if(pcVideoTab !=0){
            $("#showbox div").hide();
            $("#showbox1").hide();
            $("#player").show();
        }else if(pcVideoTab == 0 && panTab != 0){
            $("#showbox div").hide();
            $("#showbox1").show();
            $("#player").hide();
        }else{
            $("#player").hide();
            $("#showbox1").hide();
            $("#showbox div").show();
        }
    });
</script>
<script>
    $("#showsum span").mouseenter(
        function(){
            var x=$("#showsum span").index(this);
            $(".now-status").css("left",122*x)
        }
    )
</script>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:include="house/detail/fragment_contactAgent::contact"></div>
<!--
    <div th:include="house/detail/fragment_contactAgent::loginAgent"></div>
-->
<!--<div th:include="house/detail/fragment_request::request"></div>-->
<input type="hidden" id="fxe_status" value="vip"/>
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
<!--无验证码订单-->
<div th:include="house/detail/fragment_order::unuseCode"></div>
<div th:include="house/detail/fragment_order::guideMessage"></div>
<!--有验证码订单-->
<div th:include="house/detail/fragment_order::useCode"></div>
<!--报名弹窗-->
<div id="condo-apply">
    <div class="condo-apply-win">
        <h6>免费报名<span id="close"><img src="https://static.fangxiaoer.com/web/images/sercoudo/tour-close.png"></span></h6>
        <!--已登录用户报名-->
        <th:if th:if="${#session?.getAttribute('sessionId') ne null and #session?.getAttribute('sessionId') ne ''}">
            <div class="condo-apply-put">
                <input id="phone" type="tel" name="phone" placeholder="请输入手机号" readonly="readonly" th:value="${session.phoneNum}" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">
            </div>
            <div class="condo-apply-put">
                <input id="address" type="text" name="address" placeholder="请输入您的姓名">
                <span class="errormsg name" style="display: none">请输入您的姓名</span>
            </div>
            <div class="condo-apply-put condo-apply-subme">
                <p id="submit">立即提交</p>
            </div>
        </th:if>

        <!--已登录用户报名-->
        <!--未登录用户报名-->
        <th:if th:unless="${#session?.getAttribute('sessionId') ne null and #session?.getAttribute('sessionId') ne ''}">
            <div class="condo-apply-put">
                <input id="phone" type="tel" name="phone" placeholder="请输入手机号" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">

                <span class="errormsg cellphone" style="display: none">请输入正确的手机号码</span>
            </div>
            <div class="condo-apply-put" id="code-input">
                <input id="code" type="tel" name="code" style="width: 65%;border-right: 1px solid #dedede;" placeholder="请输入验证码" maxlength="6">
                <span id="code-pull">获取验证码</span>
                <span class="errormsg verifycode" style="display: none">请输入正确的验证码</span>
            </div>

            <div class="condo-apply-put">
                <input id="address" type="text" name="address" placeholder="请输入您的姓名">
                <span class="errormsg name" style="display: none">请输入您的姓名</span>
            </div>

            <div class="condo-apply-put condo-apply-subme">
                <p id="submit">立即提交</p>
            </div>
        </th:if>
    </div>
    <!--未登录用户报名-->
    <div id="success" style="display: none;">
        <span id="closeb"><img src="https://static.fangxiaoer.com/web/images/sercoudo/tour-close.png"></span>
        <p><img src="https://static.fangxiaoer.com/web/images/sercoudo/success.png"></p>
        <P>预约成功</P>
        <p>工作人员将尽快与您联系，免费为您提供专业服务</p>
    </div>

</div>


<script th:inline="javascript">
    $("#gratis-apply").click(function(){
        $("#condo-apply").show()
        $(".condo-apply-win").show();
        $("body").css("overflow","hidden")
    });

    $("#close").click(function() {
        $("#condo-apply").hide();
        $("#success").hide()
        $("body").css("overflow","auto")
    })

    $("#closeb").click(function() {
        clearTimeout(closeTime);
        $("#condo-apply").hide();
        $("#success").hide()
        $("body").css("overflow","auto")
    })



    var wait = 60;
    $("#code-pull").click(function() {
        var tel = $("#phone").val();//获取手机号
        if (tel.length == 11 && tel.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/) && wait == 60) {
            $.ajax({
                type: "POST",
                data: {
                    mobile: tel
                },
                url: "/sendSmsCode",
                success: function (result) {
                    if (result.status == 0) {
                        alert("系统繁忙，请稍后重试!");
                    } else {
                        $("#code-pull").css({
                            "border": "none",
                            "color": "#ccc"
                        });
                        time(wait);
                    }
                }
            });
        } else {
            $(".cellphone").html("请输入正确的手机号码").show();
            return false;
        }
    })

    function time(o) {
        if (wait == 0) {
            $("#code-pull").html("重新获取");
            wait = 60;
            $("#code-pull").css({
                "border": "none",
                "color": "#ff5200"
            });
        } else {
            $("#code-pull").html(wait + "秒后重发");
            wait--;
            setTimeout(function() {time(o); },
                1000);
        }
    }


    var closeTime ;
    var sessionId = [[${#session?.getAttribute('sessionId')}]];
    $("#submit").click(function () {
        var phone = "",
            code = "",
            carWhereId = "";
        phone = $("#phone").val(); //手机号
        code = $("#code").val(); //验证码
        carWhereId = $("#address").val(); //用户名
        if (sessionId == null || sessionId == "") {
            if (phone == "" || phone.length != 11 || !phone.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)) {
                $(".cellphone").show();
                setTimeout(function () {
                    $(".cellphone").hide();
                }, 2000);
                // alert("请正确输入您的手机号码");
                return;
            } else {
                $(".cellphone").hide();
            }

            if (code == "" || code.length != 6) {
                $(".verifycode").show();
                setTimeout(function () {
                    $(".verifycode").hide();
                }, 2000);
                // alert("请正确输入您的验证码");
                return;
            } else {
                $(".verifycode").hide();
            }
        }


        if(carWhereId == null || carWhereId == ""){
            $("span.name").html("请输入姓名");
            $("span.name").show()
            setTimeout(function () {
                $("span.name").hide();
            }, 2000);
            return
        }
        var params = {
            phone: phone,
            sessionId: sessionId,
            code: code,
            region: carWhereId,
            area: "看房团",
            type: 4,
            italy:'【Sy站】',
        };
        $.ajax({
            type: "POST",
            url: "/saveHouseOrder",
            data: JSON.stringify(params),
            headers: {
                'Content-Type': 'application/json;charset=utf-8'
            },
            success: function (data) {
                console.log(data)
                if (data.status == 1) {
                    $(".condo-apply-win").hide();
                    $("#success").show();
                    $("#success").children()[0].style.display=" ";
                    $("#success").children()[2].style.display=" ";
                    if(sessionId == '' || sessionId == null || sessionId == undefined){
                        $("#phone").val("");
                    }
                    $("#code").val(""); //验证码
                    $("#address").val(""); //用户名
                    closeTime =setTimeout(function () {
                        $("#condo-apply").hide();
                        $("#success").hide()
                        $("body").css("overflow","auto")
                    },3000)
                } else {
//                       $(".condo-apply-win").hide();
//                        $("#success").children()[0].style.display="none";
//                        $("#success").children()[2].style.display="none";
//                        $("#success").children()[1].html(data.msg);
                    alert(data.msg+",请检查后重新提交")
                }
            }
        })
    })
    //获取验证码倒计时

    $("#player-b").click(function(){
        $("#condotour").get(0).play();
        $("#condotour").get(0).controls=true;
        $("#player-b").hide();
    });

    $("#condotour").bind("ended", function() {
        $("#player-b").show();
        $("#condotour").get(0).controls=false;
    });
    // $("#condotour").get(0).onpause=function () {
    //     $("#player-b").show();
    //     $("#condotour").get(0).pause();
    //     $("#condotour").get(0).controls=false;
    // }

</script>
<!--报名弹窗-->

</body>

</html>