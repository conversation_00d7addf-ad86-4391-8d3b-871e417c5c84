<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${houseInfo.projectName+'楼盘信息_沈阳'+houseInfo.projectName+'户型信息_'+houseInfo.projectName+'基本信息 - 房小二网'}"></title>
    <meta name="keywords" th:content="${houseInfo.projectName+'楼盘信息,沈阳'+houseInfo.projectName+'户型信息,'+houseInfo.regionName+houseInfo.projectName+'供暖,'+houseInfo.projectName+'物业信息,'+houseInfo.projectName+'公摊'}" >
    <meta name="description" th:content="${'沈阳'+houseInfo.projectName+',房小二网为你提供'+houseInfo.projectName+'最详细楼盘信息，查找'+houseInfo.regionName+houseInfo.projectName+'户型信息，物业公摊，开发供暖等基本信息尽在房小二网!'}">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" rel="stylesheet" type="text/css" />
    <!--<link href="/css/main2017.css" rel="stylesheet" type="text/css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/info.css?v=20180921" rel="stylesheet" type="text/css" />

    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/project_information.js"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/housesParticulars.css?v=20180608"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/project_informations.css?v=20230518" rel="stylesheet" type="text/css" />
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]];
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
    <style type="">
        .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
            background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}

        .p-right{
            width: 253px;
            height: 139px;
            float: right;
            border: 1px solid #ededed;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .right-top{
            font-size: 14px;
            line-height: 24px;
            font-family: "微软雅黑";
            color: #333;

            margin-left: 20px;
        }
        .right-bom{
            color: #ff5200;
            font-size: 20px;
            line-height: 24px;
            margin-left: 20px;
            font-family: dinot-bold;
            font-weight: normal;
            margin-top: 16px;
        }
        .bom-text{
            font-size: 14px;
            margin-left: 2px;
            margin-right: 2px;
        }


    </style>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

<div class="cl"></div>

<div th:include="house/detail/fragment_menu::menu" th:with="type=2"></div>
<div class="w" style="width: 1170px !important;">
    <div class="housesLeft">
        <div class="Basics_box">
            <div class="title_sun">
                <h1>基本信息</h1>
            </div>
            <div class="content">
                <ul>
                    <div class="basics_main">
                        <div class="basics_left">
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.regionPlate) and #strings.toString(houseInfo.baseInfo.regionPlate) ne '暂无资料'}">
                                区域板块：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.regionPlate}"></th:block></div>
                            </li>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.businessCircleName) and #strings.toString(houseInfo.baseInfo.businessCircleName) ne '暂无资料'}">
                                所属商圈：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.businessCircleName}"></th:block></div>
                            </li>
                            <li th:if="${!#strings.isEmpty(houseInfo.projectFeaturesTotal) and #strings.toString(houseInfo.projectFeaturesTotal) ne '暂无资料'}">项目特色：
                                <div class="verligns">
                                    <th:block th:text="${houseInfo.projectFeaturesTotal}" ></th:block>
                                </div>
                            </li>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.kfs) and #strings.toString(houseInfo.baseInfo.kfs) ne '暂无资料'}">
                                开发商：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.kfs}"></th:block></div>
                                <!--<div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.kfs) gt 25}" style="display: none">
                                    <th:block th:text="${houseInfo.baseInfo.kfs}"></th:block>
                                    <div class="ms_sanjiao dt_sj"></div>
                                </div>-->
                            </li>
                        </div>
                        <div class="basics_right">
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.schortType) and #strings.toString(houseInfo.baseInfo.schortType) ne '暂无资料'}">环线类型：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.schortType}"></th:block></div> </li>
                            <li th:if="${!#strings.isEmpty(houseInfo.pzInfo.resBuildType) and #strings.toString(houseInfo.pzInfo.resBuildType) ne '暂无资料'}">建筑类型：<div class="verligns"><th:block th:text="${houseInfo.pzInfo.resBuildType}"></th:block></div></li>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.stationName) and #strings.toString(houseInfo.baseInfo.stationName) ne '暂无资料'}">
                                地铁站点：<div class="verligns"><th:block  th:text="${houseInfo.baseInfo.stationName}"></th:block></div>
                                <!--<div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.stationName) gt 25}" style="display: none">
                                    <th:block th:text="${houseInfo.baseInfo.stationName}"></th:block>
                                    <div class="ms_sanjiao dt_sj"></div>
                                </div>-->
                            </li>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.tzs) and #strings.toString(houseInfo.baseInfo.tzs) ne '暂无资料'}">
                                投资商：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.tzs}"></th:block></div>
                                <!--<div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.tzs) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.tzs}"></th:block>
                                    <div class="ms_sanjiao dt_sj"></div>
                                </div>-->
                            </li>
                        </div>
                    </div>


                    <!--<br>
                    <div class="top_wei">
                        <li>区域板块：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.regionPlate) ? '其他': houseInfo.baseInfo.regionPlate}"></th:block> </li>
                        <li>环线类型：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.schortType)? '暂无资料' : houseInfo.baseInfo.schortType}"></th:block> </li>
                        <li>所属商圈：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.businessCircleName)?  '暂无资料' :houseInfo.baseInfo.businessCircleName}"></th:block> </li>
                        <li>开发商：<th:block th:text="${houseInfo.baseInfo.kfs ne ''}?${ #strings.abbreviate(houseInfo.baseInfo.kfs,25)}:'暂无资料'"></th:block> <div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.kfs) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.kfs}"></th:block>
                            <div class="ms_sanjiao dt_sj"></div>
                        </div></li>
                        <li>施工单位：<th:block th:text="${houseInfo.baseInfo.sgdw ne ''? #strings.abbreviate(houseInfo.baseInfo.sgdw,25) :'暂无资料'}"></th:block> <div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.sgdw) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.sgdw}"></th:block>
                            <div class="ms_sanjiao dt_sj"></div>
                        </div> </li>
                        <li>供暖方式：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.heatingType) ? '暂无资料': houseInfo.baseInfo.heatingType}"></th:block> </li>
                        <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.stationName) and #strings.toString(houseInfo.baseInfo.stationName) ne '暂无资料'}">地铁站点：<th:block  th:text="${#strings.toString(houseInfo.baseInfo.stationName) eq '暂无资料'? '无' :#strings.abbreviate(houseInfo.baseInfo.stationName,25)}"></th:block> <div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.stationName) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.stationName}"></th:block>
                            <div class="ms_sanjiao dt_sj"></div>
                        </div></li>
                        <li th:if="${houseInfo.baseInfo.tzs ne '' and houseInfo.baseInfo.tzs ne '暂无资料'}">投资商：<th:block  th:text="${#strings.abbreviate(houseInfo.baseInfo.tzs,25)}"></th:block> <div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.tzs) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.tzs}"></th:block>
                            <div class="ms_sanjiao dt_sj"></div>
                        </div></li>
                        <li  th:if="${houseInfo.baseInfo.heatingUnits ne '' and houseInfo.baseInfo.heatingUnits ne '暂无资料'}">供暖单位：<th:block th:text="${#strings.abbreviate(houseInfo.baseInfo.heatingUnits,25)}"></th:block> <div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.heatingUnits) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.heatingUnits}"></th:block>
                            <div class="ms_sanjiao dt_sj"></div>
                        </div></li>
                        <div class="clearfix"></div>
                    </div>

                  <div class="bottom_wei">
                      <li>建筑面积：<th:block th:text="${houseInfo.baseInfo.buildingArea eq '0 ㎡' ? '暂无资料':houseInfo.baseInfo.buildingArea}"></th:block> </li>
                      <li>占地面积：<th:block th:text="${houseInfo.baseInfo.floorArea eq '0 ㎡' ? '暂无资料':houseInfo.baseInfo.floorArea}"></th:block> </li>
                      <li>容积率：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.plotRatio)? '暂无资料' : houseInfo.baseInfo.plotRatio}"></th:block> </li>
                      <li>绿化率：<th:block th:text="${houseInfo.baseInfo.greenery eq '0 %' ? '暂无资料':houseInfo.baseInfo.greenery}"></th:block> </li>
                      <li>物业公司：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.wygs)? '暂无资料': #strings.abbreviate(houseInfo.baseInfo.wygs,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.wygs) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.wygs}"></th:block>
                          <div class="ms_sanjiao dt_sj"></div>
                      </div> </li>
                      <li>车位信息：<th:block  th:text="${#strings.isEmpty(houseInfo.baseInfo.garageInfo) ? '暂无资料': #strings.abbreviate(houseInfo.baseInfo.garageInfo,25) }"></th:block> <div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.garageInfo) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.garageInfo}"></th:block></div></li>
                      <li style="width: 100%">物业费：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.maintenace) ? '暂无资料' : houseInfo.pzInfo.maintenace}"></th:block>  </li>
                      <div class="clearfix"></div>
                  </div>-->

                </ul>
            </div>
        </div>

        <div class="Mansion">
            <div class="title_sun">
                <h1>小区规划</h1>
            </div>
            <div class="content">
                <ul>
                    <div class="basics_main">
                        <div class="basics_left">
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.floorArea) and #strings.toString(houseInfo.baseInfo.floorArea) ne '暂无资料'
                             and #strings.toString(houseInfo.baseInfo.floorArea) ne '0 ㎡' and #strings.toString(houseInfo.baseInfo.floorArea) ne '0'}">占地面积：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.floorArea}"></th:block></div> </li>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.plotRatio) and #strings.toString(houseInfo.baseInfo.plotRatio) ne '暂无资料'
                             and #strings.toString(houseInfo.baseInfo.plotRatio) ne '0'}">容积率：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.plotRatio}"></th:block></div> </li>
                            <li th:if="${!#strings.isEmpty(houseInfo.propertyAgeTotal) and #strings.toString(houseInfo.propertyAgeTotal) ne '暂无资料'}">产权年限：<div class="verligns"><th:block  th:text="${houseInfo.propertyAgeTotal}"></th:block></div></li>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.wygs) and #strings.toString(houseInfo.baseInfo.wygs) ne '暂无资料'}">物业公司：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.wygs}"></th:block></div>
                                <!--<div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.wygs) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.wygs}"></th:block>
                                    <div class="ms_sanjiao dt_sj"></div>
                                </div> -->
                            </li>
                            <th:block th:if="${!#strings.isEmpty(houseInfo.pzInfo.projectPz)
                             and (#strings.toString(houseInfo.pzInfo.projectPz) eq '10' or #strings.toString(houseInfo.pzInfo.projectPz) eq '11' or #strings.toString(houseInfo.pzInfo.projectPz) eq '12' or #strings.toString(houseInfo.pzInfo.projectPz) eq '13')}">
                                <li th:if="${!#strings.isEmpty(houseInfo.pzInfo.shoopArea) and #strings.toString(houseInfo.pzInfo.shoopArea) ne '暂无资料'
                                 and #strings.toString(houseInfo.pzInfo.shoopArea) ne '0'}">商铺面积：<div class="verligns"><th:block  th:text="${houseInfo.pzInfo.shoopArea}"></th:block></div></li>
                            </th:block>
                            <li th:if="${!#strings.isEmpty(houseInfo.pzInfo.pooledTotal) and #strings.toString(houseInfo.pzInfo.pooledTotal) ne '暂无资料'}">公摊：<div class="verligns"><th:block th:text="${houseInfo.pzInfo.pooledTotal}"></th:block></div></li>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.garageInfo) and #strings.toString(houseInfo.baseInfo.garageInfo) ne '暂无资料'}">车位信息：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.garageInfo}"></th:block> </div>
                                <!--<div class="content_pop" th:if="${#strings.length(houseInfo.baseInfo.garageInfo) gt 25}" style="display: none"><th:block th:text="${houseInfo.baseInfo.garageInfo}"></th:block></div>-->
                            </li>
                        </div>
                        <div class="basics_right">
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.buildingArea) and #strings.toString(houseInfo.baseInfo.buildingArea) ne '暂无资料'
                             and #strings.toString(houseInfo.baseInfo.buildingArea) ne '0 ㎡' and #strings.toString(houseInfo.baseInfo.buildingArea) ne '0'}">建筑面积：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.buildingArea}"></th:block> </div></li>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.greenery) and #strings.toString(houseInfo.baseInfo.greenery) ne '暂无资料'
                             and #strings.toString(houseInfo.baseInfo.greenery) ne '0 %' and #strings.toString(houseInfo.baseInfo.greenery) ne '0'}">绿化率：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.greenery}"></th:block></div> </li>
                            <li th:if="${!#strings.isEmpty(houseInfo.pzInfo.decorationTotal) and #strings.toString(houseInfo.pzInfo.decorationTotal) ne '暂无资料'}">交付标准：<div class="verligns"><th:block th:text="${houseInfo.pzInfo.decorationTotal}"></th:block></div></li>
                            <li style="width: 100%" th:if="${!#strings.isEmpty(houseInfo.pzInfo.maintenace) and #strings.toString(houseInfo.pzInfo.maintenace) ne '暂无资料'}">物业费：<div class="verligns"><th:block  th:text="${houseInfo.pzInfo.maintenace}"></th:block> </div> </li>
                            <th:block th:if="${!#strings.isEmpty(houseInfo.pzInfo.projectPz)
                             and (#strings.toString(houseInfo.pzInfo.projectPz) eq '10' or #strings.toString(houseInfo.pzInfo.projectPz) eq '11' or #strings.toString(houseInfo.pzInfo.projectPz) eq '12' or #strings.toString(houseInfo.pzInfo.projectPz) eq '13')}">
                                <li th:if="${!#strings.isEmpty(houseInfo.pzInfo.shoopSalesType) and #strings.toString(houseInfo.pzInfo.shoopSalesType) ne '暂无资料'}">商铺出售：<div class="verligns"><th:block  th:text="${houseInfo.pzInfo.shoopSalesType}"></th:block></div></li>
                                <li th:if="${!#strings.isEmpty(houseInfo.pzInfo.shoopFloor) and #strings.toString(houseInfo.pzInfo.shoopFloor) ne '暂无资料'}">商铺地上层数：<div class="verligns" style="width:300px;"><th:block  th:text="${houseInfo.pzInfo.shoopFloor}"></th:block></div></li>
                            </th:block>
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.heatingType) and #strings.toString(houseInfo.baseInfo.heatingType) ne '暂无资料'}">供暖方式：<div class="verligns"><th:block th:text="${houseInfo.baseInfo.heatingType}"></th:block></div> </li>
                        </div>
                    </div>
                </ul>
            </div>
        </div>




        <!-- <div class="Mansion" th:if="${#strings.toString(houseInfo.pzInfo.projectPz) eq '1' or #strings.toString(houseInfo.pzInfo.projectPz) eq '11' or #strings.toString(houseInfo.pzInfo.projectPz) eq '13' or #strings.toString(houseInfo.pzInfo.projectPz) eq '3'}">
              <div class="title_sun">
                  <h1>普宅信息</h1>
              </div>
             <div class="content">
                 <ul>
                     <li>普宅标签：<th:block th:text="${#strings.isEmpty(houseInfo.resProjectFeatures)?'暂无资料':houseInfo.resProjectFeatures}" ></th:block> </li>
                     <li>建筑类型：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.resBuildType)?'暂无资料':houseInfo.pzInfo.resBuildType}"></th:block></li>
                     <li>装修状况：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.resDecoration)?'暂无资料':houseInfo.pzInfo.resDecoration}"></th:block></li>
                     <li>楼间距：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.resFloorspace)?'暂无资料':houseInfo.pzInfo.resFloorspace}"></th:block></li>
                     <li>物业费：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.resMaintenance)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.resMaintenance,25)}"></th:block> <div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.resMaintenance) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.resMaintenance}"></th:block>
                         <div class="ms_sanjiao dt_sj"></div>
                     </div></li>
                     <li>电梯费：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.resElevatorfees)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.resElevatorfees,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.resElevatorfees) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.resElevatorfees}"></th:block>
                         <div class="ms_sanjiao dt_sj"></div>
                     </div></li>
                     <li>公摊：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.resPooled)? '暂无资料':houseInfo.pzInfo.resPooled}"></th:block></li>
                     <li>产权年限：<th:block  th:text="${#strings.isEmpty(houseInfo.propertyAge)? '暂无资料':houseInfo.propertyAge}"></th:block></li>
                     <li th:if="${houseInfo.pzInfo.resInterior ne '' and houseInfo.pzInfo.resInterior ne '暂无资料'}">内墙：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.resInterior)? '暂无资料':houseInfo.pzInfo.resInterior}"></th:block></li>
                     <li th:if="${houseInfo.pzInfo.resExterior ne '' and houseInfo.pzInfo.resExterior ne '暂无资料'}">外墙：<th:block   th:text="${#strings.isEmpty(houseInfo.pzInfo.resExterior)? '暂无资料':houseInfo.pzInfo.resExterior}"></th:block></li>
                     <li th:if="${houseInfo.pzInfo.resBuildingStructure ne '' and houseInfo.pzInfo.resBuildingStructure ne '暂无资料'}">建筑结构：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.resBuildingStructure)? '暂无资料':houseInfo.pzInfo.resBuildingStructure}"></th:block></li>
                     <li th:if="${houseInfo.pzInfo.resElevatorBrand ne '' and houseInfo.pzInfo.resElevatorBrand ne '暂无资料'}">电梯品牌：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.resElevatorBrand)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.resElevatorBrand,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.resElevatorBrand) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.resElevatorBrand}"></th:block>
                         <div class="ms_sanjiao dt_sj"></div>
                     </div></li>
                     <li th:if="${houseInfo.pzInfo.resWindows ne '' and houseInfo.pzInfo.resWindows ne '暂无资料'}">门窗材料：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.resWindows)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.resWindows,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.resWindows) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.resWindows}"></th:block>
                         <div class="ms_sanjiao dt_sj"></div>
                     </div></li>
                     <div class="clearfix"></div>
                 </ul>
             </div>

         </div>
         <div class="Villa" th:if="${#strings.toString(houseInfo.pzInfo.projectPz) eq '2' or #strings.toString(houseInfo.pzInfo.projectPz) eq '12' or #strings.toString(houseInfo.pzInfo.projectPz) eq '13' or #strings.toString(houseInfo.pzInfo.projectPz) eq '3'}">
             <div class="title_sun">
                 <h1>洋房信息</h1>
             </div>
              <div class="content">
                  <ul>
                      <li>洋房标签：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.goodProjectFeatures)? '暂无资料':#strings.toString(houseInfo.pzInfo.goodProjectFeatures).replace(',',' ')}"></th:block></li>
                      <li>装修状况：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.goodDecoration)? '暂无资料':houseInfo.pzInfo.goodDecoration}"></th:block></li>
                      <li>楼间距：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.goodFloorspace)? '暂无资料':houseInfo.pzInfo.goodFloorspace}"></th:block></li>
                      <li>物业费：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.goodMaintenance)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.goodMaintenance,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.goodMaintenance) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.goodMaintenance}"></th:block>
                          <div class="ms_sanjiao dt_sj"></div>
                      </div></li>
                      <li>电梯费：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.goodElevatorfees)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.goodElevatorfees,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.goodElevatorfees) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.goodElevatorfees}"></th:block>
                          <div class="ms_sanjiao dt_sj"></div>
                      </div></li>
                      <li>公摊：<th:block th:text="${#strings.isEmpty(houseInfo.pzInfo.goodPooled)? '暂无资料':houseInfo.pzInfo.goodPooled}"></th:block></li>
                      <li>产权年限：<th:block th:text="${#strings.isEmpty(houseInfo.propertyAge)? '暂无资料':houseInfo.propertyAge}"></th:block></li>
                      <li th:if="${houseInfo.pzInfo.goodInterior ne '' and houseInfo.pzInfo.goodInterior ne '暂无资料'}">内墙：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.goodInterior)? '暂无资料':houseInfo.pzInfo.goodInterior}"></th:block></li>
                      <li th:if="${houseInfo.pzInfo.goodExterior ne '' and houseInfo.pzInfo.goodExterior ne '暂无资料'}">外墙：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.goodExterior)? '暂无资料':houseInfo.pzInfo.goodExterior}"></th:block></li>
                      <li th:if="${houseInfo.pzInfo.goodBuildingStructure ne '' and houseInfo.pzInfo.goodBuildingStructure ne '暂无资料'}">建筑结构：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.goodBuildingStructure)? '暂无资料':houseInfo.pzInfo.goodBuildingStructure}"></th:block></li>
                      <li th:if="${houseInfo.pzInfo.goodElevatorBrand ne '' and houseInfo.pzInfo.goodElevatorBrand ne '暂无资料'}">电梯品牌：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.goodElevatorBrand)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.goodElevatorBrand,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.goodElevatorBrand) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.goodElevatorBrand}"></th:block></div></li>
                      <li th:if="${houseInfo.pzInfo.goodWindows ne '' and houseInfo.pzInfo.goodWindows ne '暂无资料'}">门窗材料：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.goodWindows)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.goodWindows,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.goodWindows) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.goodWindows}"></th:block></div></li>
                      <div class="clearfix"></div>
                  </ul>
              </div>

         </div>
         <div class="shops"  th:if="${#strings.toString(houseInfo.pzInfo.projectPz) eq '10' or #strings.toString(houseInfo.pzInfo.projectPz) eq '11' or #strings.toString(houseInfo.pzInfo.projectPz) eq '12' or #strings.toString(houseInfo.pzInfo.projectPz) eq '13'}">
              <div class="title_sun">
                  <h1>商铺信息</h1>
              </div>
             <div class="content">
                 <ul>
                     <li>出售类型：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.shoopSalesType)? '暂无资料':houseInfo.pzInfo.shoopSalesType}"></th:block></li>
                     <li>净高：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.shoopClearHeight)? '暂无资料':houseInfo.pzInfo.shoopClearHeight}"></th:block></li>
                     <li>产权年限：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.shopPropertyAge)? '暂无资料':houseInfo.pzInfo.shopPropertyAge}"></th:block></li>
                     <li>面积段：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.shoopArea)? '暂无资料':houseInfo.pzInfo.shoopArea}"></th:block></li>
                     <li>物业费：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.shoopMaintenance)? '暂无资料':houseInfo.pzInfo.shoopMaintenance}"></th:block></li>
                     <li>公摊：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.shoopPooled)? '暂无资料':houseInfo.pzInfo.shoopPooled}"></th:block></li>
                     <li th:if="${houseInfo.pzInfo.shoopFloor ne '' and houseInfo.pzInfo.shoopFloor ne '暂无资料'}">地上层数：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.shoopFloor)? '暂无资料':houseInfo.pzInfo.shoopFloor}"></th:block></li>
                     <li th:if="${houseInfo.pzInfo.shoopBasement ne '' and houseInfo.pzInfo.shoopBasement ne '暂无资料'}">地下室信息：<th:block  th:text="${#strings.isEmpty(houseInfo.pzInfo.shoopBasement)? '暂无资料':#strings.abbreviate(houseInfo.pzInfo.shoopBasement,25)}"></th:block><div class="content_pop" th:if="${#strings.length(houseInfo.pzInfo.shoopBasement) gt 25}" style="display: none"><th:block th:text="${houseInfo.pzInfo.shoopBasement}"></th:block></div></li>
                      <div class="clearfix"></div>
                 </ul>
             </div>

         </div>-->

        <div class="sale">
            <div class="title_sun">
                <h1>销售信息</h1>
            </div>
            <div class="content">
                <ul>
                    <div class="basics_main">
                        <div class="basics_left">
                            <li th:if="${!#strings.isEmpty(houseInfo.saleAddress) and #strings.toString(houseInfo.saleAddress) ne '暂无资料'}">售楼处地址：<div class="verligns2"><th:block th:text="${houseInfo.saleAddress}"></th:block></div></li>
                            <li th:if="${!#lists.isEmpty(layoutType)}">在售户型： <a target="_blank" tyle="margin-right: 10px" th:each="layoutType:${layoutType}" th:text="${layoutType.name}" th:href="${'/house/layout/r'+layoutType.id+'-pid'+projectId + '-pt' + projectType+ '.htm'}"></a></li>
                            <!--                           <li>贷款方式：<th:block th:text="${houseInfo.baseInfo.loans ne ''}?${houseInfo.baseInfo.loans}:'暂无资料'"></th:block></li>-->
                            <li th:if="${!#lists.isEmpty(houseInfo.openTime) and #lists.size(houseInfo.openTime) ne 0}">
                                开盘时间：<div class="verligns2"><th:block th:each="lic,licindex:${#strings.setSplit(houseInfo.openTime.get(0).dyTime,'.')}"
                                                                     th:text="${licindex.index==0? lic+'年':(licindex.index==1?lic+'月':lic+'日'+houseInfo.openTime.get(0).dyDesc)}"></th:block></div>
                            </li>
                            <li th:if="${!#lists.isEmpty(houseInfo.existinfo) and #lists.size(houseInfo.existinfo) ne 0}">交房时间：<div class="verligns2"><th:block  th:text="${houseInfo.existinfo.get(0).description}"></th:block></div></li>
                            <!--                   <li th:if="${#lists.isEmpty(houseInfo.license)}">预售许可证： 暂无资料</li>-->

                        </div>
                        <div class="basics_right">
                            <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.projectState) and #strings.toString(houseInfo.baseInfo.projectState) ne '暂无资料'}">项目状态：<th:block th:text="${houseInfo.baseInfo.projectState}"></th:block></li>
                        </div>
                    </div>
                    <li th:if="${!#lists.isEmpty(houseInfo.license)}">
                        <!--                       预售许可证：-->
                        <table>
                            <thead>
                            <tr>
                                <td style="width: 210px">预售许可证</td>
                                <td style="width: 210px">发证时间</td>
                                <td s>绑定楼栋</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="licenseItem:${houseInfo.license}">
                                <td th:if="${!#strings.isEmpty(licenseItem.licenseId) and #strings.toString(licenseItem.licenseId) ne '暂无资料'}" style="text-align: center" th:text="${#strings.isEmpty(licenseItem.licenseId)?'':licenseItem.licenseId}"></td>
                                <td th:if="${!#strings.isEmpty(licenseItem.licenseId) and #strings.toString(licenseItem.licenseId) ne '暂无资料'}" style="text-align: center" th:text="${#strings.isEmpty(licenseItem.dyTime)?'':#strings.toString(licenseItem.dyTime).replace('.','-')}"></td>
                                <td th:if="${!#strings.isEmpty(licenseItem.licenseId) and #strings.toString(licenseItem.licenseId) ne '暂无资料'}" th:text="${#strings.isEmpty(licenseItem.dyDesc)? '' : licenseItem.dyDesc }"></td>
                            </tr>
                            </tbody>
                        </table>
                        <div class="btn" th:if="${ #lists.size(houseInfo.license) gt 1}">
                            <h1>展开</h1>
                        </div>
                    </li>


                </ul>

            </div>
        </div>
        <div class="project" th:if="${!#strings.isEmpty(houseInfo.baseInfo.projectDescription) and #strings.toString(houseInfo.baseInfo.projectDescription) ne '暂无资料'}">
            <div class="title_sun">
                <h1>项目简介</h1>
            </div>
            <div class="content">
                <p th:text="${houseInfo.baseInfo.projectDescription}"></p>
            </div>
        </div>
        <div class="Developers" th:if="${!#strings.isEmpty(houseInfo.baseInfo.developerDescription) and #strings.toString(houseInfo.baseInfo.developerDescription) ne '暂无资料'}">
            <div class="title_sun">
                <h1>开发商简介</h1>
            </div>
            <div class="content">
                <p th:text="${houseInfo.baseInfo.developerDescription}"></p>
            </div>

        </div>

    </div>


    <!--    <div class="p-right">-->
    <!--        <div class="right-top">售楼处电话:</div>-->
    <!--        <div class="right-bom">4008-606-313<span class="bom-text">转</span></b>15772</div>-->
    <!--    </div>-->


    <div th:include="house/detail/fragment_menu::freeCall" ></div>
    <div class="clearfix"></div>
    <div class="newHouseViewChunk" style="margin-top: 30px;border: 0">
        <div th:include="house/detail/fragment_relasion_house::normalHouse_push"></div>
    </div>
</div>
<script>


</script>


<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div class="cl"></div>

<div th:include="fragment/fragment:: footer_detail"></div>

<div th:include="fragment/fragment::commonFloat"></div>
<div th:include="fragment/fragment::tongji"></div>
<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::unuseCode"  ></div>
<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode"  ></div>
</body>
</html>
