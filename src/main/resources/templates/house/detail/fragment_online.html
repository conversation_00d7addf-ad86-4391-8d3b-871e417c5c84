<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="online">
    <style type="text/css">
        /* mappy */
        #worldMap{ position:relative;cursor: all-scroll;}
        #worldMap p{ position:absolute; width:20px;height:20px; background:red; top:10px;left:10px;}
        .mappy{float:left;position:relative;}
    </style>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/vip_dingwei.js"></script>
    <script src="/js/dealer_diwei.js" type="text/javascript" charset="utf-8"></script>

    <script>
        $(document).ready(function () {
            $(".cha").click(function () { $(".main2").hide(); $(".tcbg2").hide(); });
            $("#myScroll").niceScroll();
            $("#worldMap li").each(function() {
                $(this).css("left",$(this).position().left);
                $(this).css("top",$(this).position().top);
            });
        })
        window.onload = function() {
            var map = new SpryMap({
                id: "worldMap",
                height: 400,
                width: 700,
                startX: 0,
                startY: 0,
                cssClass: "mappy"
            });
        };
    </script>
    <div id="online" th:if="${houseInfo.isOnline ne '0' and saleHouse ne null}" style="display: none">
        <div class="title"><p>楼栋信息</p></div>
        <div class="zsxx_list">
            <ul>
            </ul>
        </div>
        <!--楼位图-->
        <div class="mappy">
            <div id="worldMap" class="zxxf_l picbox" style="position: absolute; left: 0px; top: 0px;">
                <img th:src="'https://images.fangxiaoer.com/sy/xgt/nktv/big/'+${saleHouse.url}" width="750" height="550"/>
                <ul>
                    <li th:each="hInfo:${saleHouse.buildInfo}" th:showid="${#numbers.formatInteger( hInfo.showid,3)}"
                        class="drow hot2" th:style="${hInfo.styleXY}">
                        <div><span th:text="${hInfo.buildName}"></span></div>
                    </li>
                </ul>
            </div>
            <div class="mappy_info">
                <span class="mappy_close"></span>
                <div class="mappy_t hid" th:text="${houseInfo.projectName}"><span></span></div>
                <ul class="mappy_xx">
                    <li class="n1"></li>
                    <li class="n2"></li>
                    <li class="n3"></li>
                    <li class="n4"></li>
                    <li class="n5"></li>
                    <li class="n6"></li>
                </ul>
                <p class="mappy_hx"></p>

                <div class="scroll_out">
                    <div class="scroll">
                        <div class="txtblock">
                            <div id="myScroll">
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--户型图-->
        <div class="zxxf_r">
            <h2><a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType+'.htm'}" class="more"> 更多丨more ></a>
                <th:block th:text="${houseInfo.projectName+'户型列表'}"></th:block>
            </h2>
            <div class="xiala">
            </div>
        </div>

        <div class="cl"></div>
    </div>
</div>
</body>
</html>