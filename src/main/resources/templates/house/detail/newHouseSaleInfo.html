


<!DOCTYPE html>

<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head >
	<title th:text="${houseInfo.projectName+'销售动态 - 房小二网'}"></title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta name="keywords" th:content="${houseInfo.projectName+'楼盘动态,'+houseInfo.projectName+'销售动态,'+houseInfo.projectName+'最新消息,'+houseInfo.projectName+'楼盘新闻'}" />
	<meta name="description" th:content="${'房小二网销售动态为您提供'+houseInfo.projectName+'楼盘动态信息，包括楼盘新闻，开盘时间信息、户型房源价格等实时信息，为您选房、看房提供优质参考！'}"/>
	<meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/saleInfo/'+projectId+'.htm'}">
	<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />
	<!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->
	<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynamic.css?v=20190111" rel="stylesheet" type="text/css" />
	<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
	<script src="/js/house/verify.js" type="text/javascript"></script>
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?v=20180522"/>
	<!--<link rel="stylesheet" type="text/css" href="/css/saleTrends.css" />-->
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/saleTrends.css?v=20191220" />
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
	<link href="https://static.fangxiaoer.com/web/styles/new_sy/office/view.css?v=20180608" rel="stylesheet" type="text/css">
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
    <style>
        .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
            background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    </style>
</head>
<body class="w1210">

<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>


<div class="cl"></div>
<div th:include="house/detail/fragment_menu::menu" th:with="type=18"></div>

<div class="main">
        <div class="saleTrends tab">
            <div class="quanbao">
                <div class="dynamic_head">
                    <ul>
                        <a th:href="${'/house/'+projectId+'-'+projectType+'/news/dy.htm'}"><li th:class="${#strings.toString(dyType) eq null or #strings.toString(dyType) eq ''} ?'focus':''"  >全部</li></a>
                        <th:block th:if="${#lists.size(dyTypeFilter) != 0}" th:each="filter,i:${dyTypeFilter}">
                            <a th:href="${'/house/'+projectId+'-'+projectType+'/news/dy'+filter.id+'.htm'}"><li th:classappend="${#strings.toString(dyType) eq filter.id}?'focus':''"  th:text="${filter.name}"></li></a>
                        </th:block>
                    </ul>
                </div>
                <div class="dynamic_mian">
                    <div class="xian"></div>
                    <ul>
                        <li th:each="info,i:${saleInfoNew}">
                            <div class="top_wei">
                                <i></i>
                                <span>-<b th:text="${info.dyTime}"></b>-</span>
                                <div th:if="${#strings.toString(dyType) eq null} or ${#strings.toString(dyType) eq ''}" th:class="${'type'+info.dyType}" th:text="${info.dyTypeName}"></div>
                            </div>
                            <div class="bottom_wei">
                                <p th:text="${info.dyDesc}"></p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <!--分页-->
            <div class="page" style="margin-top: 0;">
                <div th:include="fragment/page :: page "></div>
            </div>
        </div>
	<div th:include="house/detail/fragment_menu::freeCall" ></div>
	<div class="cl"></div>
	<div class="newHouseViewChunk" style="margin-top: 30px;border: 0">
		<div th:include="house/detail/fragment_relasion_house::normalHouse_push"></div>
	</div>
		<script type="text/javascript">
            $(document).ready(function(){
                var urlPath = window.location.href;
                if(urlPath.indexOf("#") != -1){
                	var currentIndex =  urlPath.split("#")[1];
                    $("#a"+currentIndex).addClass("hover");
                    $(".timeShort").hide()
                    $(".timeLong").show()
                    $("#a"+currentIndex).parent().find(".timeShort").show();
                    $("#a"+currentIndex).parent().find(".timeLong").hide();
                    for (var i = 0; i <10; i++) {
                        if(i!=currentIndex){
                            $("li#" + "con_a_"+i).hide();
                        }
                    }
				}else {
                    $("#a0").addClass("hover");
                    for (var i = 1; i <10; i++) {
                        $("li#" + "con_a_"+i).hide();
                    }
				}
			});
            $(".dot").mouseover(function() {

                var dotRel = $(this).attr("rel");
                var dotparent = $(this).parent()
                $(".timeShort").hide()
                $(".timeLong").show()
                $(".dot").removeClass("hover")
                $(this).addClass("hover")
                dotparent.find(".timeShort").show()
                dotparent.find(".timeLong").hide()

                $("li#" + "con_a_"+ dotRel).show();
//                console.log(dotRel)
                for (var i = 0; i <10; i++) {
                    if (dotRel != i)
                        $("li#" + "con_a_"+i).hide();
                }

            })
            $(".timeLong").mouseover(function() {

                var dotRel = $(this).parents("li").find(".dot").attr("rel");
                var dotparent = $(this).parents("li").find(".dot")
                $(".timeShort").hide()
                $(".timeLong").show()
                $(".dot").removeClass("hover")
                $(this).parents("li").find(".dot").addClass("hover")
                $(this).parents("li").find(".timeShort").show()
                $(this).parents("li").find(".timeLong").hide()

                $("li#" + "con_a_"+ dotRel).show();
//                console.log(dotRel)
                for (var i = 0; i <10; i++) {
                    if (dotRel != i)
                        $("li#" + "con_a_"+i).hide();
                }

            })
		</script>

</div>

<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::unuseCode"  ></div>
<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode"  ></div>
</body>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
</html>