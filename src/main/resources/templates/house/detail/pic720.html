

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${display.ProjectName+'全景_沈阳'+display.ProjectName+'720度全景图 - 房小二网'}"></title>
    <meta name="keywords" th:content="${display.ProjectName +'全景,沈阳' +display.ProjectName + '全景图,'+display.ProjectName+'720度视频,'+display.ProjectName+'3D图'}">
    <meta name="description" th:content="${'房小二网' +display.ProjectName + '全景图,为您提供' +display.ProjectName + '720度全景图，无死角的展示' +display.ProjectName + '的户型，布局，结构等各种细节。最全面的了解' +display.ProjectName + '信息，尽在房小二网。'}">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'/pic720/'+display.PanID+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />
    <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css?v=20191112" />-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/picture/720.css?v=20181228" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
</head>

<body class="w1210">
<form name="form1" method="post" action="268" id="form1">
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

    <div class="cl"></div>
    <div  th:if="${projectType ne '2'}"th:include="house/detail/fragment_menu::menu" th:with="type=6"></div>
    <div  th:if="${projectType eq '2'}"th:include="house/detail/fragment_menu::menu" th:with="type=5"></div>

    <div class="banner" style="background:url(https://static.fangxiaoer.com/web/images/sy/villa/view/banner.jpg) center">
    </div>
    <div class="window">
        <div class="middle">
            <div class="play">
                <iframe th:src="${display.frameUrl}" frameborder="0" scrolling="no" width="956px" height="491" id="pp"></iframe>
                <div class="video_t">&nbsp;</div>
            </div>
            <DIV id=vertical class="scrollbox clearfix">
                <DIV class="slyWrap example2">
                    <DIV class=scrollbar>
                        <DIV class=handle></DIV></DIV>
                    <DIV class=sly data-options='{ "scrollBy": 84, "startAt": 0 }'>

                        <ul>

                            <li th:each="item : ${panList}" class="hover" th:title="${item.frameUrl}">
                                <a th:href="${'/pic720/' + projectId + '-' + projectType + '-' + item.PanID + '.htm'}">
                                    <img th:src="${item.PanImageUrl}" th:alt="${#strings.toString(item.tab) eq '0' ? item.ProjectName + ' ' + item.LayName : item.LayName}"/>
                                    <p>

                                        <th:block th:text="${item.LayName}"/>
                                        <th:block th:if="${#strings.toString(item.tab) eq '0'}">
                                            </br><th:block th:text="${item.BuildArea + 'm²' + item.BuildType}"/>
                                        </th:block>
                                    </p>
                                </a>
                            </li>

                        </ul>

                    </DIV>
                </DIV>
                <div class="cl"></div>
            </div>
        </div>
    </div>
    <div class="cl"></div>
    <div class="w info">
        <p class="qjxx">全景信息</p>
        <th:block th:if="${display.tab eq '0'}">
        <p class="qjxxnav">
            户型：<th:block th:text="${display.LayName}"/><br>
            面积：<th:block th:text="${display.BuildArea + 'm²'}"/><br>
            类型：<th:block th:text="${display.ProjectType eq '1'?'普宅':(display.ProjectType eq '2'?'别墅':'公寓')}"/><br>
            <span th:text="${display.Description}"></span>简介：
        <div class="cl"></div>
        </p></th:block>
        <th:block th:unless="${display.tab eq '0'}">
        <p class="qjxxnav">
            <p th:text="${display.LayName}" style="color: #333333;"></p>
            简介：<span th:text="${display.Description}"></span>
        <div class="cl"></div>
        </p>
        </th:block>
    </div>


    <div class="w">
        <div class="con_l">




        </div>
        <div class="con_r"></div>
    </div>

    </div>



<!--    <div th:include="house/detail/fragment_login::login"></div>-->
    <!--无验证码订单-->
    <div  th:include="house/detail/fragment_order::unuseCode"  ></div>
    <div  th:include="house/detail/fragment_order::guideMessage"  ></div>
    <!--有验证码订单-->
    <div  th:include="house/detail/fragment_order::useCode"  ></div>

    <div class="cl"></div>
    <div  th:include="fragment/fragment::footer_detail"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::commonFloat"></div>
</form>
</body>
</html>


