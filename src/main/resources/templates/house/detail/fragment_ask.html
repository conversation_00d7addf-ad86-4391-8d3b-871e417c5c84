<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <div th:fragment="xmpj">
        <input type="hidden" id="ProjectId" th:value="${projectId}">
        <input type="hidden" id="ProjectType" th:value="${projectType}">
        <div class="w" id="xmpj">
            <div class="title">
                <span class="xmpj_more" >
                    <b>&emsp;&emsp;&emsp;偷偷告诉你：工作日8：00-17：00、节假日9：00-16：00，在这个时间段内点评，我们会极速审核哟！</b>
                </span>
                <p>用户点评</p>
            </div>

            <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/default.css?v=20181009"/>
            <style type="text/css">
                .remark_r{
                    width: 910px;
                }
                .remark_cont{
                    width: 1170px;
                }
            </style>
            <!--<script src="https://static.fangxiaoer.com/js/bootstrap.min.js" type="text/javascript"> </script>-->
            <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
            <script src="/js/house/CommentList.js"></script>
            <script src="/js/house/LjzPage.js"></script>
            <script th:inline="javascript">
                var Projectid = [[${projectId}]];
                var projectType = [[${projectType}]];
                var MemberID = 0;
                var Phone = "";
                var AddUrl = "#login";
                var UserName = "";
                var IsCommentList = 0;
                var sessionMemberType = $("#memberType").val();
                $(document).ready(function () {
                    $("#CommentListAdd").click(function() {
                        IsCommentList = 1;
                    });
                    if ($("#LoginUrl")) {
                        $("#LoginUrl").val("/comment/"+Projectid+"/"+projectType);
                    }
                    if ($("#LoginUrlAgent")) {
                        $("#LoginUrlAgent").val("/commentAgent/"+Projectid+"/"+projectType);
                    }
                    var sessionId = $("#glzs").val();
                    if(sessionId == null || sessionId == undefined || sessionId == ''){
                        $("#commitAsk").attr("href",AddUrl);
                        $("#CommentListAdd").attr("href",AddUrl);
                    }else {
                        if (sessionMemberType == 1) {
                            $("#CommentListAdd").attr("href","/comment/"+Projectid+"/"+projectType);
                            $("#commitAsk").attr("href","#");
                            $("#commitAsk").click(function () {
                                addAsk();
                            });
                        } else if (sessionMemberType == 2) {
                            $("#CommentListAdd").attr("href","/commentAgent/"+Projectid+"/"+projectType);
                            $("#commitAsk").attr("href","#");
                            $("#commitAsk").click(function () {
                                addAsk();
                            });
                        }
                    }
                    CommentList.BindFenXiang();
                    CommentList.SetCount(Projectid);
//                    CommentList.GetList(Projectid,0,'','');
                });
                $(window).load(function () {
                    CommentList.GetList(Projectid,0,'','');
                });
            </script>
            <style>
                .ck_pic{width:100%;height:80px;margin-top:25px;margin-bottom: 10px;overflow: hidden;}
                .ck_pic ul li{float:left;margin-right:10px;}
                .ck_pic ul li img{width:100px;height: 75px;}
                .ck_pic span{display: block;float:left;margin-top:52px;}
                .blackBg{background: #000;filter: alpha(opacity=78);opacity: 0.78;position: fixed;width: 100%;height: 100%;z-index: 10000;top: 0;left: 0;display: none;}
                .tanPic{width: 800px;height: 500px;top: 50%;left: 50%;margin-left: -400px;margin-top: -250px;  z-index:10001;position: fixed;text-align: center;display: none;}
                .bigPic{width: 800px;top: 50%;left: 50%;margin-left: -400px;margin-top: -250px;z-index: 2000;position: fixed;text-align: center;}
                .bigPic img {max-width: 800px; *height: 500px;max-height: 500px;}
                .bigPicClose{position: absolute;top: 0px;left: 804px;z-index: 2000;width: 30px;height: 30px;
                    cursor: pointer;background: url("https://static.fangxiaoer.com/web/images/ico/sign/fy_close.png");}
                .picPre{width: 46px;height: 74px;background: url("https://static.fangxiaoer.com/web/images/ico/sign/white_ico.png") 1px 0px;position: absolute;top: 210px; left: -80px;z-index: 2000;cursor: pointer;}
                .picNext{background: url("https://static.fangxiaoer.com/web/images/ico/sign/white_ico.png") -155px 0px;width: 46px;height: 74px;position: absolute;top: 210px;left: 824px;z-index: 2000;cursor: pointer;}
            </style>
            <input type="hidden" id="IsYezhu" value="0" class="IsAll"/>
            <input type="hidden" id="IsPic" value="0" class="IsAll"/>
            <input type="hidden" id="IsRatio" value="0" class="filter"/>
            <input type="hidden" id="IsSupporting" value="0" class="filter"/>
            <input type="hidden" id="IsSpace" value="0" class="filter"/>
            <input type="hidden" id="IsProperty" value="0" class="filter"/>
            <input type="hidden" id="IsHuxing" value="0" class="filter"/>
            <input type="hidden" id="IsFlaws" value="0" class="filter"/>
            <input type="hidden" id="IsVirtues" value="0" class="filter"/>
            <div class="w remark">
                <div class="remark_classify">
                    <span class="remark_classify_title">可选分类：</span>
                    <div class="filterUl">
                        <ul>
                            <li class="unlimited" id="filterAll">不限</li>
                            <li>
                                <input type="checkbox" id="checkbox_a1" class="chk_1" data-id="IsVirtues"/>
                                <label for="checkbox_a1"></label>
                                <label for="checkbox_a1">综合</label>
                            </li>
                            <li>
                                <input type="checkbox" id="checkbox_a3" class="chk_1" data-id="IsFlaws"/>
                                <label for="checkbox_a3"></label>
                                <label for="checkbox_a3">缺点</label>
                            </li>
                            <li>
                                <input type="checkbox" id="checkbox_a4" class="chk_1" data-id="IsHuxing"/>
                                <label for="checkbox_a4"></label>
                                <label for="checkbox_a4">户型</label>
                            </li>
                            <li>
                                <input type="checkbox" id="checkbox_a5" class="chk_1" data-id="IsProperty"/>
                                <label for="checkbox_a5"></label>
                                <label for="checkbox_a5">物业</label>
                            </li>
                            <li>
                                <input type="checkbox" id="checkbox_a6" class="chk_1" data-id="IsSpace"/>
                                <label for="checkbox_a6"></label>
                                <label for="checkbox_a6">车位</label>
                            </li>
                            <li>
                                <input type="checkbox" id="checkbox_a7" class="chk_1" data-id="IsSupporting"/>
                                <label for="checkbox_a7"></label>
                                <label for="checkbox_a7">配套</label>
                            </li>
                            <li>
                                <input type="checkbox" id="checkbox_a8" class="chk_1" data-id="IsRatio"/>
                                <label for="checkbox_a8"></label>
                                <label for="checkbox_a8">性价比</label>
                            </li>
                        </ul>
                    </div>

                    <!--<div class="remark_btn">-->
                        <!---->
                        <!--<a class="" target="_blank" style="cursor: pointer" data-toggle="modal" id="CommentListAdd">我要点评</a>-->


                    <!--</div>-->
                    <div class="synthesizeRight">
                        <a href="https://sy.fangxiaoer.com/news/51408.htm" target="_blank">点评赚取积分规则</a>
                        <a  data-toggle="modal" id="CommentListAdd" href="#login">我要点评</a>
                    </div>
                </div>
                <div class="remark_cont">
                    <div class="remark_nav">
                        <ul>
                            <li id="dianping1" class="hover"></li>
                            <li id="dianping5" data-id="IsYezhu" data-val="3"></li>
                            <li id="dianping2" data-id="IsYezhu" data-val="1"></li>
                            <li id="dianping3" data-id="IsYezhu" data-val="2"></li>
                            <li id="dianping4" data-id="IsPic" data-val="1"></li>
                        </ul>
                    </div>
                    <div class="remark_all" id="dianpingDom">
                        <!--全部评论-->
                        <div id="con_dianping_1">
                            <div id="listDom"></div>
                        </div>
                    </div>
                    <div class="page">
                        <div id="LjzPage"></div>
                    </div>
                </div>
            </div>
            <div class="blackBg"></div>
            <div class="tanPic">
                <div class="bigPic"></div>
                <div class="bigPicClose"></div>
                <div class="picPre"></div>
                <div class="picNext"></div>
            </div>
            <script>
                if ((navigator.userAgent.indexOf('MSIE') >= 0) && (navigator.userAgent.indexOf('Opera') < 0)) {
                    $(".remark_classify label:even").hide();
                    $(".remark_classify input").show()
                }
                $(".replay_btn").live("click",function () {
                    if ($(this).parent().find('.remark_reply').css("display") == "none") {
                        $('.remark_reply').slideUp();
                        $(this).parent().find('.remark_reply').slideDown();
                    } else {
                        $(this).parent().find('.remark_reply').slideUp();
                    }
                })
                $(".remark_classify label").click(function () {
                    $(".unlimited").css({ "border-color": "#fff", "color": "#666" });
                })
                $(".remark_classify input").click(function() {
                    if ($(this).is(':checked')) {
                        $("#" + $(this).attr("data-id")).val(1);
                    } else {
                        $("#" + $(this).attr("data-id")).val(0);
                    }
                    CommentList.SetCount(Projectid);
                    CommentList.GetList(Projectid,MemberID,Phone,UserName);
                });
                $(".unlimited").click(function() {
                    $(".remark_classify input").removeAttr("checked");
                    $(this).css({ "border-color": "#ff5200", "color": "#ff5200" });
                });
            </script>
            <script>
                $("#filterAll").click(function() {
                    $(".filter").val("0");
                    CommentList.SetCount(Projectid);
                    CommentList.GetList(Projectid,MemberID,Phone,UserName);
                });
                $(".filter").click(function () {
                    $("#filterAll").val("");
                    CommentList.SetCount(Projectid);
                    CommentList.GetList(Projectid,MemberID,Phone,UserName);
                });
                $(".remark_nav ul li").click(function() {
                    $(".remark_nav ul li").removeClass("hover");
                    $(this).addClass("hover");
                    $(".IsAll").val(0);
                    $("#" + $(this).attr("data-id")).val($(this).attr("data-val"));
                    CommentList.GetList(Projectid,MemberID,Phone,UserName);
                });
            </script>

            <script type="text/javascript">
                function words_deal() {
                    var curLength = $("#AskList_txt").val().length;
                    if (curLength > 300) {
                        var num = $("#AskList_txt").val().substr(0, 300);
                        $("#AskList_txt").val(num);
                        alert("超过字数限制，多出的字将被截断！");
                    }
                    else {
                        $("#textCount").text(300 - $("#AskList_txt").val().length);
                    }
                }
            </script>


        </div>
        <script th:inline="javascript">
            function words_deal() {
                var curLength = $("#AskList_txt").val().length;
                if (curLength > 300) {
                    var num = $("#AskList_txt").val().substr(0, 300);
                    $("#AskList_txt").val(num);
                    alert("超过字数限制，多出的字将被截断！");
                }
                else {
                    $("#textCount").text(300 - $("#AskList_txt").val().length);
                }
            }
            function addAsk() {
                var sessionId = $("#glzs").val();
                var projectType_Ask = [[${projectType}]];
                var projectName = [[${houseInfo.projectName}]];
                var askType = 1;
                var content = $("#AskList_txt").val();
                if(projectType_Ask  == null || projectType_Ask == undefined || projectType_Ask == ''){
                    projectType_Ask = "1";
                }
                if (!sessionId) {
                    alert("请先登录");
                }else if(!askType) {
                    alert("请选择咨询类型");
                }else if(!content){
                    alert("请填写提问内容");
                }else {
                    var params = {
                        sessionId: sessionId,
                        projectId: Projectid,
                        askType: askType,
                        projectName: projectName,
                        content: content,
                        projectType: projectType_Ask
                    };
                    $.ajax({
                        type: "POST",
                        data: JSON.stringify(params),
                        url: "/addAsk",
                        dataType: "json",
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        success: function (data) {
                            if (data.status == 1) {
                                alert("咨询成功提交，等待审核");
                                window.location.reload();
                            } else {
                                alert(data.msg);
                            }
                        }
                    });
                }
            }
        </script>
        <div class="cl"></div>
    </div>
    <div th:fragment="xmzxOld">
        <!--<div class="cl"></div>-->
        <div class="w title" id="xmzx">
            <p>楼盘问答</p>
        </div>
        <div id="comment" class="w">
            <div id="con_cm_1" style="display: none">
                <div class="cl">
                </div>
                <div id="comments-list" class="m" clstag="shangpin|keycount|product|comment">
                    <div class="mt">
                        <div class="mt-inner m-tab-trigger-wrap clearfix">
                            <ul class="m-tab-trigger">
                                <li class="ui-switchable-item trig-item curr"><a href="javascript:;">全部点评<em> (0)</em></a></li>
                                <li class="ui-switchable-item trig-item"><a href="javascript:;">好评<em>(0)</em></a></li>
                                <li class="ui-switchable-item trig-item"><a href="javascript:;">中评<em>(0)</em></a></li>
                                <li class="ui-switchable-item trig-item"><a href="javascript:;">差评<em>(0)</em></a></li>
                            </ul>
                        </div>
                    </div>
                    <div id="comment-0" class="mc ui-switchable-panel comments-table ui-switchable-panel-selected"
                         style="display: block;">
                        <div class="com-table-header">
                            <span class="item column1">点评心得</span> <span class="item column2">购房满意度</span> <span
                                class="item column5">点评者</span>
                        </div>
                        <div class="com-table-main">

                            <div class="cl">
                            </div>
                        </div>
                        <div class="page hid">
                            1 2 3 4 5 6 7 8 9
                        </div>
                    </div>
                </div>
            </div>

            <!--con_cm_1-->
            <script src="/js/house/AskList.js"></script>
            <script src="/js/house/AskLjzPage.js"></script>
            <script type="text/javascript" th:inline="javascript">
                var Projectid = [[${projectId}]];
                $(document).ready(function () {
                    CommentList.BindFenXiang();
                    AskList.SetCount(Projectid);
                    AskList.GetList(Projectid,0,'','');
                });
            </script>
            <input type="hidden" id="askType" value="0" class="askType"/>
            <div id="con_cm_2">
                <div class="cm_2_tit">
                    <ul>
                        <li id="zx1" data-id="askType" class="hover"></li>
                        <li id='zx2' data-id="askType" data-val="1"></li>
                        <li id='zx3' data-id="askType" data-val="2" ></li>
                        <li id='zx4' data-id="askType" data-val="3" ></li>
                        <li id='zx5' data-id="askType" data-val="4" ></li>
                    </ul>
                </div>
                <script type="text/javascript">
                    $(".cm_2_tit ul li").click(function(){
                        $(".cm_2_tit ul li").removeClass("hover");
                        $(this).addClass("hover");
                        $("#askType").val(0);
                        $("#" + $(this).attr("data-id")).val($(this).attr("data-val"));
                        AskList.GetList(Projectid,MemberID,Phone,UserName);
                    })
                </script>

                <div id="con_zx_1" class="zx">
                    <div class="zx_txt">
                        <a href="#zxun"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/zixun.gif" /></a>
<!--
                        <p>提示：因每位咨询者购房情况、提问时间等不同或遇开发商调价等行为，以下回复仅对提问者3天内有效，其它网友仅供参考！由此给您带来的不便请多多谅解，谢谢！</p>
-->
                    </div>
                    <div id="AskList_UpdatePanel2">

                    </div>
                    <div class="page" style="margin-bottom: 30px;">
                        <div id="AskList_Pager1"></div>
                    </div>
                </div>
                <div id="zxun" class="question">
                    <div class="question_l">
                        <div class="question_tit" th:text="'提问：' + ${houseInfo.projectName}"></div>
                        <div class="login ">
                            登录后才可以发表咨询，<a href="https://my.fangxiaoer.com" target="_blank">立即登录</a>
                        </div>
                        <div class="ask_type">
                            <ul>
                                <!--<li><b>提问类型：</b>

                                    <label for="ask1">
                                        <input name="ask" class="validate[rquired] radio" type="radio" id="ask1"
                                               value='1' checked='checked' />房源提问</label>

                                    <label for="ask2">
                                        <input name="ask" class="validate[rquired] radio" type="radio" id="ask2"
                                               value='2'  />促销活动</label>

                                    <label for="ask3">
                                        <input name="ask" class="validate[rquired] radio" type="radio" id="ask3"
                                               value='3'  />支付及退款</label>

                                    <label for="ask4">
                                        <input name="ask" class="validate[rquired] radio" type="radio" id="ask4"
                                               value='4'  />房仔服务</label>
-->
                                <li><b>提问内容</b><textarea name="AskList$txt" rows="2" cols="20" id="AskList_txt" onkeyup="words_deal();"></textarea>
                                </li>
                                <li><b>&nbsp;</b>
                                    <div style="float: right; margin-right: 50px">
                                        剩余<span id="textCount">300</span>个字
                                    </div>
                                    <a class=""data-toggle="modal"  id="commitAsk">提交问题</a>
                                    <!--<a class=""  data-toggle="modal" href="#login">提交问题</a>-->
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="question_r">
                        <b>问答说明：</b><p>
                        1、楼盘问答是房小二网为您提供的疑难问题解答板块，在这里你可以与工作人员直接交流。<br>
                        2、因提问的用户较多，可能无法及时进行回复，我们将尽力在最快的时间内为您解答。<br>
                        3、当涉及广告、不实评论、恶意评论、爆粗口、危害国家安全等不当言论时，房小二网有权实施封号等管理行为。
                    </p>
                    </div>
                    <div class="cl">
                    </div>
                </div>
            </div>
            <!--con_cm_2 end-->
        </div>
    </div>
    <div th:fragment="xmzx">
        <div class="housesLeft" style="border-left:1px solid #ededed;border-right:1px solid #ededed">
            <div class="title" id="xmzx">
                <p style="padding-left: 15px;">楼盘问答 </p>
                <a th:if="${!#lists.isEmpty(askList) && #lists.size(askList) != 0}" th:href="${'/house/ask/'+projectId + '-' + projectType+ '.htm'}" target="_blank" class="seeMoreAsk">查看更多</a>
            </div>
            <div th:if="${!#lists.isEmpty(askList) && #lists.size(askList) != 0}">
            <div class="titleAskbtn" >
                <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') == null}" >
                    <a th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login">我要提问</a>
                </div>
                <span  th:if="${#session?.getAttribute('sessionId') != null}" onclick="javascrpit:addProjectAsk();">我要提问</span>
                <p>说出您对楼盘的疑问，我们帮您答疑解惑！</p>
                <!--<a href="">我要提问</a>-->
            </div>

            </div>
            <!--<span  onclick="javascrpit:addProjectAsk();">我要提问</span>-->
            <div class="border_wei">
                <div class="houseAskNoList" th:if="${#lists.isEmpty(askList) || #lists.size(askList) == 0}">
                    <p>说出您对楼盘的疑问，我们帮您答疑解惑！</p>
                    <!--<a href="" target="_blank">我要提问</a>-->
                    <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') == null}" >
                        <a th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login">我要提问</a>
                    </div>
                    <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') != null}" >
                        <a th:if="${#session?.getAttribute('sessionId') != null}" onclick="javascrpit:addProjectAsk();">我要提问</a>
                    </div>
                </div>
            </div>


            <ul class="houseAnswer">
                <li th:each="ask:${askList}">
                    <div class="houseAnswerTitle" th:text="${ask.content}">房子户型，性价比，地段怎么样?</div>
                    <div class="houseAnswerPhone"><b th:text="${ask.tel}">157****5676</b><span th:text="${ask.askTime}">2018-03-15 12:44:15</span>
                    </div>
                    <div class="houseAnswerContent"><span>房小二网客服回复：</span>
                        <th:block th:text="${#strings.isEmpty(ask.replyContent) ? '等待回复' : ask.replyContent}"></th:block>
                    </div>
                </li>
            </ul>
            <div class="question_r">
                <b>问答说明：</b><p>
                1、楼盘问答是房小二网为您提供的疑难问题解答板块，在这里你可以与工作人员直接交流。<br>
                2、因提问的用户较多，可能无法及时进行回复，我们将尽力在最快的时间内为您解答。<br>
                3、当涉及广告、不实评论、恶意评论、爆粗口、危害国家安全等不当言论时，房小二网有权实施封号等管理行为。
            </p>
            </div>
        </div>
        <script type="text/javascript">
            function addProjectAsk() {
                $("#iNeedAsk").show();
            }
        </script>
    </div>
    <div th:fragment="xmpjNew">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/default.css?v=20181009"/>
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css?v=20180522"/>

        <script src="/js/house/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
        <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
        <style>
            .bigImgShow .showImg ul li img {
                margin-top: -220px;
                margin-left: -250px;
                width: 500;
            }
            .tanChuBeiJing{
                z-index: 999999;
            }
        </style>
        <input type="hidden" id="memberType" th:value="${session.memberType}">
        <div class="housesLeft" th:if="${!#lists.isEmpty(comments)}">
            <div class="synthesize">
                <div class="title">
                    <p>用户点评</p>
                    <a th:href="${'/house/appraise/'+projectId+'-'+projectType+'.htm'}" style="color: #666;float: right;font-size: 14px;">查看更多</a>
                </div>

                <div class="synthesizeLeft" th:if="${state eq '1'}">
                    <b>综合评分
                        <span><th:block th:text="${totalScore.score}"></th:block></span>
                        <i th:if="${!#strings.isEmpty(totalScore.star_grade)}" th:class="${#strings.isEmpty(totalScore.star_grade)?'': 'star'+totalScore.star_grade}">
                            <div class="red">

                            </div>
                        </i>
                    </b>
                    <ul>
                        <li>地段 <span><th:block th:text="${totalScore.scoreSection}"></th:block></span></li>
                        <li>交通 <span><th:block th:text="${totalScore.scoreTraffic}"></th:block></span></li>
                        <li>配套 <span><th:block th:text="${totalScore.scoreSupporting}"></th:block></span></li>
                        <li>环境 <span><th:block th:text="${totalScore.scoreEnvironment}"></th:block></span></li>
                        <li>性价比 <span><th:block th:text="${totalScore.scoreSatisfaction}"></th:block></span></li>
                    </ul>
                </div>
                <div class="synthesizeRight" style="background: none;">
                    <a  data-toggle="modal" id="CommentListAdd" href="#login">我要点评</a>
                </div>
                <!--判断我要点评的是经纪人还是个人，继而改变链接-->
                <script th:inline="javascript">

                    var memberType = $("#memberType").val();
                    var projectId = [[${projectId}]];
                    var projectType = [[${projectType}]];
                    $("#CommentListAdd").click(function () {
                        var sessionId = $("#glzs").val();
                        if (sessionId != null) {
                            if (memberType == 1) {
                                $("#CommentListAdd").attr("href","/comment/"+projectId+"/"+projectType);
                            }else if (memberType == 2) {
                                $("#CommentListAdd").attr("href","/commentAgent/"+projectId+"/"+projectType);
                            }
                        }
                    })
                </script>
            </div>
            <ul class="userSynthesize" th:if="${!#lists.isEmpty(comments)}">
                <li th:each="comments,i:${comments}" th:if="${i.index lt 4}">
                    <!--左侧头像-->

                    <!--右侧点评-->

                    <table>
                        <tr>
                            <td>
                                <div class="contentRight">
                                    <th:block th:if="${comments.isYezhu ne '3'}"><!--业主与非业主头像-->
                                        <div class="headerImg hover">
                                            <div class="headerImges">
                                                <img th:if="${comments.isNiming eq '1'}" th:src="${comments.sex eq '0' ? 'https://static.fangxiaoer.com/web/images/ico/sign/men.gif' : 'https://static.fangxiaoer.com/web/images/ico/sign/women.gif'}">
                                                <img th:if="${comments.isNiming ne '1'}" th:src="${#strings.isEmpty(comments.agentPic) || #strings.indexOf(comments.agentPic,'noagent.png')!= -1?(comments.sex eq '0' ? 'https://static.fangxiaoer.com/web/images/ico/sign/men.gif' : 'https://static.fangxiaoer.com/web/images/ico/sign/women.gif'):comments.agentPic}">
                                            </div>
                                            <div class="ueserName"><th:block th:text="${comments.isNiming eq '1'? '匿名':comments.memberTel}"></th:block></div>
                                        </div>
                                        <ul class="houseInfo" th:if="${comments.isYezhu eq '1'}">
                                            <li th:if="${!#strings.isEmpty(comments.projectName)}"><th:block th:text="${'购买项目：'+comments.projectName}"></th:block></li>
                                            <li th:if="${!#strings.isEmpty(comments.price)}"><th:block th:text="${'购买价格：'+(#strings.toString(comments.price).contains('.') ?  #strings.toString(comments.price).replaceAll('0+?$','').replaceAll('[.]$', '')+'万' : comments.price+'万')}"></th:block></li>
                                            <li th:if="${!#strings.isEmpty(comments.area)}"><th:block th:text="${'购买面积：'+(#strings.toString(comments.area).contains('.') ?  #strings.toString(comments.area).replaceAll('0+?$','').replaceAll('[.]$', '')+'m²' : comments.area+'m²')}"></th:block></li>
                                            <li th:if="${!#strings.isEmpty(comments.cyear) and !#strings.isEmpty(comments.cmonth)}"><th:block th:text="${'购买时间：'+comments.cyear+'-'+comments.cmonth}"></th:block></li>
                                            <li th:if="${!#strings.isEmpty(comments.propertyType)}"><th:block th:text="${'物业类型：'+comments.propertyType}"></th:block></li>
                                        </ul>
                                    </th:block>
                                    <th:block th:if="${comments.isYezhu eq '3'}"><!--经纪人头像-->
                                        <div class="headerImg hover">
                                            <div class="headerImges">
                                                <img th:src="${comments.agentPic}" alt="" />
                                            </div>
                                            <div class="ueserName"><th:block th:text="${comments.memberType eq '2'? '经纪人 '+comments.agentName : comments.agentName}"></th:block></div>
                                            <img th:if="${comments.IsOptimum eq '1'}" src="https://static.fangxiaoer.com/web/images/sy/comment/bestdianping.png" alt="" class="bestAgent" />
                                        </div>
                                    </th:block>
                                </div>
                            </td>
                            <td>
                                <div class="contentLeft">
                                    <a th:if="${comments.isYezhu eq '3' and comments.memberType eq '2'}" class=' agentUrl agent'  target='_blank' data-toggle='modal'  href='#login'>
                                        <img src="https://static.fangxiaoer.com/web/images/sy/comment/wyzx.png"/>
                                        <input type='hidden' class='agentId' th:value="${comments.memberID}">
                                        <input type='hidden' class='projectId' th:value="${comments.projectId}">
                                    </a>
                                    <div class="img">
                                        <!--精华标签-->
                                        <img th:if="${comments.isJinghua eq '1'}"  src="https://static.fangxiaoer.com/web/images/sy/comment/jinghua.png" class="bestYnthesize"/>
                                        <!--置顶标签-->
                                        <img th:if="${comments.isZhiding eq '1'}" src='https://static.fangxiaoer.com/web/images/sy/comment/zhiding.png'/>
                                    </div>
                                    <div class="contentStar">
                                        <i th:if="${!#strings.isEmpty(comments.star_grade)}" th:class="${#strings.isEmpty(comments.star_grade)?'': 'star'+comments.star_grade}" >
                                            <div class="red">

                                            </div>
                                        </i>
                                        <!--<ul th:if="${!#strings.isEmpty(comments.score)}">-->
                                            <!--<li><th:block th:text="${'地段：'+comments.scoreSection}"></th:block></li>-->
                                            <!--<li><th:block th:text="${'交通：'+comments.scoreTraffic}"></th:block></li>-->
                                            <!--<li><th:block th:text="${'配套：'+comments.scoreSupporting}"></th:block></li>-->
                                            <!--<li><th:block th:text="${'环境：'+comments.scoreEnvironment}"></th:block></li>-->
                                            <!--<li><th:block th:text="${'性价比：'+comments.scoreSatisfaction}"></th:block></li>-->
                                        <!--</ul>-->
                                    </div>
                                    <ul th:class="${comments.isYezhu eq '3'?'contentInfoMmore contentInfoMmore_sun':'contentInfoMmoreLong contentInfoMmore_sun'}" th:style="${#strings.isEmpty(comments.score)?'padding-top: 40px;':''}">
                                        <li th:if="${!#strings.isEmpty(comments.virtues)}" >
                                            <span th:if="${!#strings.isEmpty(comments.flaws) or !#strings.isEmpty(comments.huxing)or !#strings.isEmpty(comments.property) or !#strings.isEmpty(comments.Space) or !#strings.isEmpty(comments.supporting) or !#strings.isEmpty(comments.ratio)}" style="color: #f05050;">综合：</span>
                                            <th:block th:text="${comments.virtues}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.flaws)}"><span style="color: #64a014">缺点：</span>
                                            <th:block th:text="${comments.flaws}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.huxing)}"><span>户型：</span>
                                            <th:block th:text="${comments.huxing}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.property)}"><span>物业：</span>
                                            <th:block th:text="${comments.property}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.Space)}"><span>车位：</span>
                                            <th:block th:text="${comments.Space}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.supporting)}"><span>配套：</span>
                                            <th:block th:text="${comments.supporting}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.ratio)}"><span>性价比：</span>
                                            <th:block th:text="${comments.ratio}"></th:block></li>
                                    </ul>
                                    <div class="contentInfo"></div>
                                    <ul class="contentInfoImg" th:if="${!#lists.isEmpty(comments.pic)}">
                                        <li th:each="pic:${comments.pic}"><img th:src="${pic.smallImageUrl}"></li>
                                        <span>共<i><th:block th:text="${#lists.size(comments.pic)}"></th:block></i>张图片</span>
                                    </ul>
                                    <div class="bigImgShow" style="display: none;">
                                        <div class="showImg">
                                            <ul style="margin-left: 0px;">
                                                <li th:each="pic:${comments.pic}">
                                                    <img th:src="${pic.smallImageUrl}" onload="imgSize()" >
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"></div>
                                        <div class="prev"></div>
                                        <div class="next"></div>
                                    </div>
                                    <div class="contentBottom">
                                        <div class="contentReply" style="cursor:pointer"><th:block th:text="${'回复（'+#lists.size(comments.reply)+'）'}"></th:block></div>
                                        <!--<span class="bdsharebuttonbox" style="float: right;"><a href="javascript:void(0);" class="bds_more contentShare" data-cmd="more" th:onclick="'javascript:sharedetailclick(\'https://m.fangxiaoer.com/fang1/'+${projectId} + '-'+ ${projectType} +'.htm\')'">分享</a></span>-->
                                        <div th:class="${'contentShow '+
                        (#strings.isEmpty(comments.flaws)
                    and #strings.isEmpty(comments.huxing)
                    and #strings.isEmpty(comments.property)
                    and #strings.isEmpty(comments.Space)
                    and #strings.isEmpty(comments.supporting)
                    and #strings.isEmpty(comments.ratio)
                    ?'openAndClose':'')}">查看全文</div>
                                        <!--and #strings.length(comments.virtues) eq 108-->
                                        <div class="contentTime"><th:block th:text="${comments.addTime}"></th:block></div>
                                        <input type="hidden" id="commentId" th:value="${comments.commentId}">
                                        <input type="hidden" id="projectId" th:value="${projectId}">
                                        <input type="hidden" id="projectType" th:value="${projectType}">
                                        <!--<script src="/js/share.js"></script>-->
                                        <!--<script src="/js/personal_share.js"></script>-->
                                    </div>
                                    <div class="reply">
                                        <textarea name="" rows="" cols="" placeholder="回复："></textarea>
                                        <a th:if="${#session?.getAttribute('sessionId') == null}" target='_blank' data-toggle='modal'  href='#login' class="SubmitReply" >提交回复</a>
                                        <a th:if="${#session?.getAttribute('sessionId') != null}" class="SubmitReply" th:commentId="${comments.commentId}">提交回复</a>
                                        <img src="https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg">
                                    </div>
                                    <!--点评回复列表-->
                                    <div class="commentReply" style="display: none">
                                        <div th:each="reply:${comments.reply}">
                                            <span><th:block th:text="${reply.phone}"></th:block></span><th:block th:text="${reply.replyContent}"></th:block><span><th:block th:text="${reply.addTime}"></th:block></span>
                                            <p class='replay_btn'>回复</p>
                                            <div class="reply">
                                                <textarea name="" rows="" cols="" placeholder="回复："></textarea>
                                                <a th:if="${#session?.getAttribute('sessionId') == null}" target='_blank' data-toggle='modal'  href='#login' class="SubmitReply" >提交回复</a>
                                                <a th:if="${#session?.getAttribute('sessionId') != null}" class="SubmitReply" th:commentId="${comments.commentId}">提交回复</a>
                                                <img src="https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg" data-bd-imgshare-binded="1">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </li>
            </ul>
        </div>
        <div class="housesLeft"  th:if="${#lists.isEmpty(comments) || #lists.size(comments) == 0}">
            <div class="title">
                <p>用户点评</p>
                <!--<a th:href="${'/house/appraise/'+projectId+'-'+projectType+'.htm'}" style="color: #666;float: right;font-size: 14px;margin-right: -14px;">查看更多 ></a>-->
            </div>
            <div class="border_wei">
            <div class="houseAskNoList">
                <p>暂无用户点评，快来点评一下吧~~~</p>
                <!--<a href="" target="_blank">我要提问</a>-->
                <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') == null}" >
                    <a th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login">我要点评</a>
                </div>
                <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') != null}" >
                    <a id="toAddComment" th:if="${#session?.getAttribute('sessionId') != null}">我要点评</a>
                </div>
                <script>
                    var memberType = $("#memberType").val();
                    $("#toAddComment").click(function () {
                        if (memberType == 1) {
                            $("#toAddComment").attr("href","/comment/"+projectId+"/"+projectType);
                        }else if (memberType == 2) {
                            $("#toAddComment").attr("href","/commentAgent/"+projectId+"/"+projectType);
                        }
                    })
                </script>
            </div>
            </div>
        </div>
        <script>
            /*点击我要咨询的js*/
            $(".agentUrl").live("click",function(){
                var agentId = $(this).find(".agentId").val();
                if (agentId == undefined) {
                }
                if($("#glzs").val()=="" || $("#glzs").val()== null || $("#glzs").val()== undefined){
                }else{
                    $.ajax({
                        type: "post",
                        url: "/getAgentInfo",
                        data: {agentId:agentId},
                        scriptCharset: 'utf-8',
                        dataType: "json",
                        success: function (data) {
                            /*var data = eval(data);*/
                            var content = data.content;
                            if (data.status == 1) {
                                //弹出框复制
                                $("#AgentId").text(content.MemberID);
                                $("#AgentPic").attr("src",content.agentPic);
                                $("#AgentName").text(content.agentName);
                                $("#AgentAskCount").text(content.askCount);
                                $("#AgentShop").text(content.IntermediaryName);
                                //显示弹出框
                                $(".consultAlert").show();
                            }
                            else {
                            }
                        }
                    });
                }
            })
        /*关于回复的js*/
            $(".SubmitReply").live("click" ,function () {
                var sessionId = $("#glzs").val();
                if(sessionId != null && sessionId != undefined && sessionId != ''){
                    var text = $(this).parent().find("textarea").val();
                    var params  = {
                        content: text,
                        commentId: $(this).attr("commentId"),
                        sessionId:sessionId
                    };
                    $.ajax({
                        type: "POST",
                        url: "/saveReply",
                        data: JSON.stringify(params),
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        dataType: "json",
                        success: function (data) {
                            if (data && data.status == 1) {
                                Alertljz.ShowReplyAlert("回复成功，请耐心等待审核！", "https://my.fangxiaoer.com/reply");
                            } else {
                                Alertljz.ShowReplyAlert(data.msg);
                            }

                        }
                    });
                } else {
//                    $(".modal-backdrop").show();
//                    $("#login").show();
                    //Alertljz.ShowAlert("登录以后才可以回复哦!");
                }
            });
            photo.init()
            $(".userSynthesize>li").each(function(){
                $(".contentInfoMmoreLong ").addClass("show_sun")
                if((parseInt($(this).find(".contentInfoMmore_sun").height())>75)&&($(this).find(".contentShow").hasClass("openAndClose"))){
                    // $(this).find(".contentInfoMmore_sun").css("max-height","75px")
                    $(this).find(".contentShow").show()
                    $(this).find(".contentShow").addClass("open")
                }
                if($(this).find(".contentInfoImg li").length>4){
                    $(this).find(".contentShow").show()
                    $(this).find(".contentShow").addClass("open")
                    $(this).find(" .contentLeft .contentInfoImg li").hide()
                    $(this).find(" .contentLeft .contentInfoImg li").eq(0).show()
                    $(this).find(" .contentLeft .contentInfoImg li").eq(1).show()
                    $(this).find(" .contentLeft .contentInfoImg li").eq(2).show()
                    $(this).find(" .contentLeft .contentInfoImg li").eq(3).show()
                }
            })
            $(".contentShow").click(function(){
                if($(this).hasClass("open")){
                    $(this).removeClass("open")
                    //$(this).parent().parent().find(".contentInfoMmore_sun").css("max-height","99999999px");
                    $(this).parent().parent().find(".contentInfoImg li").show()
                    $(this).parent().parent().find(".contentInfoImg span").hide()
                    $(this).text("收起全文")
                }else{
                    $(this).text("查看全文")
                    $(this).addClass("open")
                  if( $(this).parent().parent().find(".contentInfoMmore_sun").hasClass("openAndClose")){
                     // $(this).parent().parent().find(".contentInfoMmore_sun").css("max-height","75px")
                  }
                    $(this).parent().parent().find(".contentInfoImg li").hide()
                    $(this).parent().parent().find(".contentInfoImg li").eq(0).show()
                    $(this).parent().parent().find(".contentInfoImg li").eq(1).show()
                    $(this).parent().parent().find(".contentInfoImg li").eq(2).show()
                    $(this).parent().parent().find(".contentInfoImg li").eq(3).show()
                    $(this).parent().parent().find(".contentInfoImg span").show()
                }
            })
            $(".contentReply").click(function(){
                if($(this).parent().parent().find(".reply").eq(0).css("display")=="none"){
                    $(".reply").slideUp()
                    $(this).parent().parent().find(".reply").eq(0).slideDown()
                    $(this).parent().parent().find(".commentReply").show();//回复列表展示
                }else{
                    $(".reply").slideUp()
                    $(this).parent().parent().find(".commentReply").hide();//回复列表隐藏
                }

            })
            $(".replay_btn").click(function(){
                if($(this).parent().find(".reply").css("display")=="none"){
                    $(".reply").slideUp()
                    $(this).parent().find(".reply").slideDown()
                }else{
                    $(".reply").slideUp()
                }
            })
            $(".userSynthesize .contentLeft .contentInfoImg li img").click(function(){
                photo.maxIndex=$(this).parent().parent().parent().find(".bigImgShow li").length
                photo.ind=$(this).parent().index()
                photo.showInd=photo.ind
                $(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
                console.log(photo.maxIndex)
                console.log(photo.ind)
                $(this).parent().parent().parent().find(".bigImgShow").show()
            })
            $("#alertClose").live("click",function(){
                window.location.reload()
            })
            
            
            for (var i=0; i<$(".userSynthesize").children("li").length;i++) {
                $(".userSynthesize").children("li").eq(i)
                if( $(".userSynthesize").children("li").eq(i).find(".contentInfoImg").length<1){
                    $(".userSynthesize").children("li").eq(i).find(".contentBottom").css("margin-top","20px")
                }
            }
        </script>
    </div>
    <div th:fragment="xmzxForVilla">
        <div class="housesLeft" style="border-left:1px solid #ededed;border-right:1px solid #ededed">
            <div class="title" id="xmzx">
                <p style="padding-left: 15px;">楼盘问答 </p>
                <!--<a  id="view_more_ask" th:href="${'/house/ask/'+projectId + '-' + projectType+ '.htm'}" target="_blank" class="seeMoreAsk">查看更多</a>-->
            </div>
            <div  id="current_asklist_isNotEmpty">
                <div class="titleAskbtn" >
                    <div class="remark_btn"  >
                        <a  target="_blank" data-toggle="modal" href="#login">我要提问</a>
                    </div>
                    <span   onclick="javascrpit:addProjectAsk();">我要提问</span>
                    <p>说出您对楼盘的疑问，我们帮您答疑解惑！</p>
                    <div class="clearfix"></div>
                    <!--<a href="">我要提问</a>-->
                </div>

            </div>
            <!--<span  onclick="javascrpit:addProjectAsk();">我要提问</span>-->
            <div class="border_wei">
                <div class="houseAskNoList" id="current_asklist_isEmpty">
                    <p>说出您对楼盘的疑问，我们帮您答疑解惑！</p>
                    <!--<a href="" target="_blank">我要提问</a>-->
                    <div class="remark_btn"  >
                        <a  target="_blank" data-toggle="modal" href="#login">我要提问</a>
                    </div>
                    <div class="remark_btn" >
                        <a  onclick="javascrpit:addProjectAsk();">我要提问</a>
                    </div>
                    <div class="clearfix"></div>
                </div>
            </div>

            <ul class="houseAnswer">
                <!--<li th:each="ask:${askList}">-->
                    <!--<div class="houseAnswerTitle" th:text="${ask.content}">房子户型，性价比，地段怎么样?</div>-->
                    <!--<div class="houseAnswerPhone"><b th:text="${ask.tel}">157****5676</b><span th:text="${ask.askTime}">2018-03-15 12:44:15</span>-->
                    <!--</div>-->
                    <!--<div class="houseAnswerContent"><span>房小二网客服回复：</span>-->
                        <!--<th:block th:text="${#strings.isEmpty(ask.replyContent) ? '等待回复' : ask.replyContent}"></th:block>-->
                    <!--</div>-->
                <!--</li>-->
            </ul>
            <div class="page" style="margin-bottom: 30px" >
                <div id="AskList_Pager1"></div>
            </div>
            <div class="question_r">
                <b>问答说明：</b><p>
                1、楼盘问答是房小二网为您提供的疑难问题解答板块，在这里你可以与工作人员直接交流。<br>
                2、因提问的用户较多，可能无法及时进行回复，我们将尽力在最快的时间内为您解答。<br>
                3、当涉及广告、不实评论、恶意评论、爆粗口、危害国家安全等不当言论时，房小二网有权实施封号等管理行为。
            </p>
            </div>
        </div>
        <script type="text/javascript">
            function addProjectAsk() {
                $("#iNeedAsk").show();
            }
            //删除多余标签
            function deleteDisableElement() {
                if($("#glzs").val()=="" || $("#glzs").val()== null || $("#glzs").val()== undefined){
                    $(".titleAskbtn span").remove();
                    $("#current_asklist_isEmpty .remark_btn:eq(1)").remove();
                }else {
                    $(".titleAskbtn .remark_btn").remove();
                    $("#current_asklist_isEmpty .remark_btn:eq(0)").remove();
                }
            }
        </script>
    </div>
    <div th:fragment="xmpjForVilla">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/default.css?v=20181009"/>
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css?v=20180522"/>

        <script src="/js/house/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
        <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
        <style>
            .bigImgShow .showImg ul li img {
                margin-top: -220px;
                margin-left: -250px;
                width: 500px;
            }
            .tanChuBeiJing{
                z-index: 999999;
            }


        </style>
        <script>


        </script>
        <input type="hidden" id="memberType" th:value="${session.memberType}">
        <div class="housesLeft" th:if="${!#lists.isEmpty(comments)}">
            <div class="synthesize">
                <div class="title">
                    <p>用户点评</p>
               </div>
                <div class="synthesizeLeft" th:if="${state eq '1'}">
                    <b>综合评分
                        <span><th:block th:text="${totalScore.score}"></th:block></span>
                        <i th:if="${!#strings.isEmpty(totalScore.star_grade)}" th:class="${#strings.isEmpty(totalScore.star_grade)?'': 'star'+totalScore.star_grade}"><div class="red"></div></i>
                    </b>
                    <ul>
                        <li>地段 <span><th:block th:text="${totalScore.scoreSection}"></th:block></span></li>
                        <li>交通 <span><th:block th:text="${totalScore.scoreTraffic}"></th:block></span></li>
                        <li>配套 <span><th:block th:text="${totalScore.scoreSupporting}"></th:block></span></li>
                        <li>环境 <span><th:block th:text="${totalScore.scoreEnvironment}"></th:block></span></li>
                        <li>性价比 <span><th:block th:text="${totalScore.scoreSatisfaction}"></th:block></span></li>
                    </ul>
                </div>
                <div class="synthesizeRight" style="background: none;">
                    <a  data-toggle="modal" id="CommentListAdd" href="#login">我要点评</a>
                </div>
                <!--判断我要点评的是经纪人还是个人，继而改变链接-->
                <script th:inline="javascript">
                    var sessionId = $("#glzs").val();
                    var memberType = $("#memberType").val();
                    var projectId = [[${projectId}]];
                    var projectType = [[${projectType}]];
                    $("#CommentListAdd").click(function () {
                        if (sessionId != null) {
                            if (memberType == 1) {
                                $("#CommentListAdd").attr("href","/comment/"+projectId+"/"+projectType);
                            }else if (memberType == 2) {
                                $("#CommentListAdd").attr("href","/commentAgent/"+projectId+"/"+projectType);
                            }
                        }
                    })
                </script>
            </div>
            <ul class="userSynthesize" th:if="${!#lists.isEmpty(comments)}">
                <li th:each="comments,i:${comments}">
                    <!--左侧头像-->

                    <!--右侧点评-->

                    <table>
                        <tr>
                            <td>
                                <div class="contentRight">
                                    <th:block th:if="${comments.isYezhu ne '3'}"><!--业主与非业主头像-->
                                        <div class="headerImg hover">
                                            <div class="headerImges">
                                                <img th:if="${comments.isNiming eq '1'}" th:src="${comments.sex eq '0' ? 'https://static.fangxiaoer.com/web/images/ico/sign/men.gif' : 'https://static.fangxiaoer.com/web/images/ico/sign/women.gif'}">
                                                <img th:if="${comments.isNiming ne '1'}" th:src="${#strings.isEmpty(comments.agentPic) || #strings.indexOf(comments.agentPic,'noagent.png')!= -1?(comments.sex eq '0' ? 'https://static.fangxiaoer.com/web/images/ico/sign/men.gif' : 'https://static.fangxiaoer.com/web/images/ico/sign/women.gif'):comments.agentPic}">
                                            </div>
                                            <div class="ueserName"><th:block th:text="${comments.isNiming eq '1'? '匿名':comments.memberTel}"></th:block></div>
                                        </div>
                                        <ul class="houseInfo" th:if="${comments.isYezhu eq '1'}">
                                            <li th:if="${!#strings.isEmpty(comments.projectName)}"><th:block th:text="${'购买项目：'+comments.projectName}"></th:block></li>
                                            <li th:if="${!#strings.isEmpty(comments.price)}"><th:block th:text="${'购买价格：'+(#strings.toString(comments.price).contains('.') ?  #strings.toString(comments.price).replaceAll('0+?$','').replaceAll('[.]$', '')+'万' : comments.price+'万')}"></th:block></li>
                                            <li th:if="${!#strings.isEmpty(comments.area)}"><th:block th:text="${'购买面积：'+(#strings.toString(comments.area).contains('.') ?  #strings.toString(comments.area).replaceAll('0+?$','').replaceAll('[.]$', '')+'m²' : comments.area+'m²')}"></th:block></li>
                                            <li th:if="${!#strings.isEmpty(comments.cyear) and !#strings.isEmpty(comments.cmonth)}"><th:block th:text="${'购买时间：'+comments.cyear+'-'+comments.cmonth}"></th:block></li>
                                            <li th:if="${!#strings.isEmpty(comments.propertyType)}"><th:block th:text="${'物业类型：'+comments.propertyType}"></th:block></li>
                                        </ul>
                                    </th:block>
                                    <th:block th:if="${comments.isYezhu eq '3'}"><!--经纪人头像-->
                                        <div class="headerImg hover">
                                            <div class="headerImges">
                                                <img th:src="${comments.agentPic}" alt="" />
                                            </div>
                                            <div class="ueserName"><th:block th:text="${comments.memberType eq '2'? '经纪人 '+comments.agentName : comments.agentName}"></th:block></div>
                                            <img th:if="${comments.IsOptimum eq '1'}" src="https://static.fangxiaoer.com/web/images/sy/comment/bestdianping.png" alt="" class="bestAgent" />
                                        </div>
                                    </th:block>
                                </div>
                            </td>
                            <td>
                                <div class="contentLeft">
                                    <a th:if="${comments.isYezhu eq '3' and comments.memberType eq '2'}" class=' agentUrl agent'  target='_blank' data-toggle='modal'  href='#login'>
                                        <img src="https://static.fangxiaoer.com/web/images/sy/comment/wyzx.png"/>
                                        <input type='hidden' class='agentId' th:value="${comments.memberID}">
                                        <input type='hidden' class='projectId' th:value="${comments.projectId}">
                                    </a>
                                    <div class="img">
                                        <!--精华标签-->
                                        <img th:if="${comments.isJinghua eq '1'}"  src="https://static.fangxiaoer.com/web/images/sy/comment/jinghua.png" class="bestYnthesize"/>
                                        <!--置顶标签-->
                                        <img th:if="${comments.isZhiding eq '1'}" src='https://static.fangxiaoer.com/web/images/sy/comment/zhiding.png'/>
                                    </div>
                                    <div class="contentStar">
                                        <i th:if="${!#strings.isEmpty(comments.star_grade)}" th:class="${#strings.isEmpty(comments.star_grade)?'': 'star'+comments.star_grade}" ><div class="red"></div></i>
                                        <!--<ul th:if="${!#strings.isEmpty(comments.score)}">-->
                                            <!--<li><th:block th:text="${'地段：'+comments.scoreSection}"></th:block></li>-->
                                            <!--<li><th:block th:text="${'交通：'+comments.scoreTraffic}"></th:block></li>-->
                                            <!--<li><th:block th:text="${'配套：'+comments.scoreSupporting}"></th:block></li>-->
                                            <!--<li><th:block th:text="${'环境：'+comments.scoreEnvironment}"></th:block></li>-->
                                            <!--<li><th:block th:text="${'性价比：'+comments.scoreSatisfaction}"></th:block></li>-->
                                        <!--</ul>-->
                                    </div>
                                    <ul th:class="${comments.isYezhu eq '3'?'contentInfoMmore contentInfoMmore_sun':'contentInfoMmoreLong contentInfoMmore_sun'}" th:style="${#strings.isEmpty(comments.score)?'padding-top: 40px;':''}">
                                        <li th:if="${!#strings.isEmpty(comments.virtues)}" >
                                            <span th:if="${!#strings.isEmpty(comments.flaws) or !#strings.isEmpty(comments.huxing)or !#strings.isEmpty(comments.property) or !#strings.isEmpty(comments.Space) or !#strings.isEmpty(comments.supporting) or !#strings.isEmpty(comments.ratio)}" style="color: #f05050;">综合：</span>
                                            <th:block th:text="${comments.virtues}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.flaws)}"><span style="color: #64a014">缺点：</span>
                                            <th:block th:text="${comments.flaws}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.huxing)}"><span>户型：</span>
                                            <th:block th:text="${comments.huxing}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.property)}"><span>物业：</span>
                                            <th:block th:text="${comments.property}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.Space)}"><span>车位：</span>
                                            <th:block th:text="${comments.Space}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.supporting)}"><span>配套：</span>
                                            <th:block th:text="${comments.supporting}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.ratio)}"><span>性价比：</span>
                                            <th:block th:text="${comments.ratio}"></th:block></li>
                                    </ul>
                                    <div class="contentInfo"></div>
                                    <ul class="contentInfoImg" th:if="${!#lists.isEmpty(comments.pic)}">
                                        <li th:each="pic:${comments.pic}"><img th:src="${pic.smallImageUrl}"></li>
                                        <span>共<i><th:block th:text="${#lists.size(comments.pic)}"></th:block></i>张图片</span>
                                    </ul>
                                    <div class="bigImgShow" style="display: none;">
                                        <div class="showImg">
                                            <ul style="margin-left: 0px;">
                                                <li th:each="pic:${comments.pic}">
                                                    <img th:src="${pic.smallImageUrl}" onload="imgSize()" >
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"></div>
                                        <div class="prev"></div>
                                        <div class="next"></div>
                                    </div>
                                    <div class="contentBottom">
                                        <div class="contentReply" style="cursor:pointer"><th:block th:text="${'回复（'+#lists.size(comments.reply)+'）'}"></th:block></div>
                                        <!--<span class="bdsharebuttonbox" style="float: right;"><a href="javascript:void(0);"  class="bds_more contentShare" data-cmd="more" th:onclick="'javascript:sharedetailclick(\'https://m.fangxiaoer.com/fang1/'+${projectId} + '-'+ ${projectType} +'.htm\')'">分享</a></span>-->
                                        <div th:class="${'contentShow '+
                        (#strings.isEmpty(comments.flaws)
                    and #strings.isEmpty(comments.huxing)
                    and #strings.isEmpty(comments.property)
                    and #strings.isEmpty(comments.Space)
                    and #strings.isEmpty(comments.supporting)
                    and #strings.isEmpty(comments.ratio)
                    ?'openAndClose':'')}">查看全文</div>
                                        <!--and #strings.length(comments.virtues) eq 108-->
                                        <div class="contentTime"><th:block th:text="${comments.addTime}"></th:block></div>
                                        <input type="hidden" id="commentId" th:value="${comments.commentId}">
                                        <input type="hidden" id="projectId" th:value="${projectId}">
                                        <input type="hidden" id="projectType" th:value="${projectType}">
                                        <!--<script src="/js/share.js"></script>-->
                                        <!--<script src="/js/personal_share.js"></script>-->
                                    </div>
                                    <div class="reply">
                                        <textarea name="" rows="" cols="" placeholder="回复："></textarea>
                                        <a th:if="${#session?.getAttribute('sessionId') == null}" target='_blank' data-toggle='modal'  href='#login' class="SubmitReply" >提交回复</a>
                                        <a th:if="${#session?.getAttribute('sessionId') != null}" class="SubmitReply" th:commentId="${comments.commentId}">提交回复</a>
                                        <img src="https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg">
                                    </div>
                                    <!--点评回复列表-->
                                    <div class="commentReply" style="display: none">
                                        <div th:each="reply:${comments.reply}">
                                            <span><th:block th:text="${reply.phone}"></th:block></span><th:block th:text="${reply.replyContent}"></th:block><span><th:block th:text="${reply.addTime}"></th:block></span>
                                            <p class='replay_btn'>回复</p>
                                            <div class="reply">
                                                <textarea name="" rows="" cols="" placeholder="回复："></textarea>
                                                <a th:if="${#session?.getAttribute('sessionId') == null}" target='_blank' data-toggle='modal'  href='#login' class="SubmitReply" >提交回复</a>
                                                <a th:if="${#session?.getAttribute('sessionId') != null}" class="SubmitReply" th:commentId="${comments.commentId}">提交回复</a>
                                                <img src="https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg" data-bd-imgshare-binded="1">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </li>
            </ul>
            <div class="page" th:if="${totalPages gt 1}">
                <!--分页-->
                <div id="Pager1">
                    <!--<a th:if="${number eq 1}" disabled="disabled" style="margin-right:5px;cursor: pointer">首页</a>-->
                    <a th:if="${number gt 1}" th:href="@{${pageUrl} + '/1' + ${tab}}"
                       style="margin-right:5px;cursor: pointer;">首页</a>
                    <!--<a th:if="${number eq 1}" disabled="disabled" style="margin-right:5px;cursor: pointer;">上一页</a>-->
                    <a th:if="${number gt 1}" th:href="@{${pageUrl} + '/' + ${number-1} + ${tab}}"
                       style="margin-right:5px;cursor: pointer;">上一页</a>
                    <a th:if="${minPage gt 10}" th:href="@{${pageUrl} + '/' +${minPage-1} + ${tab}}"
                       style="margin-right:5px;cursor: pointer;">...</a>
                    <th:block th:if="${number ne 1 and number gt minPage}">
                        <a th:if="${number ge minPage}" th:each="i : ${#numbers.sequence( minPage, number-1)}"
                           th:href="@{${pageUrl} + '/'+${i}+ ${tab}}" th:text="${i}"
                           style="margin-right:5px;"></a>
                    </th:block>
                    <span style="margin-right:5px;font-weight:Bold;color:red;" th:text="${number}"></span>
                    <th:block th:if="${number+1 le toPage}">
                        <a th:each="i : ${#numbers.sequence( number+1, toPage)}"
                           th:href="@{${pageUrl} + '/' + ${i}+ ${tab}}" th:text="${i}"
                           style="margin-right:5px;"></a>
                    </th:block>
                    <a th:if="${toPage lt totalPages}" th:href="@{${pageUrl} + '/'+${toPage +1}+ ${tab}}"
                       style="margin-right:5px;">...</a>
                    <a th:if="${number lt totalPages}" th:href="@{${pageUrl} + '/' + ${number+1}+ ${tab}}"
                       style="margin-right:5px;">下一页</a>
                    <!--<a th:if="${number eq totalPages}" disabled="disabled" style="margin-right:5px;">下一页</a>-->
                    <a th:if="${number lt totalPages and pageType ne null}" th:href="@{${pageUrl} + '/' + ${totalPages}+ ${tab}}" style="margin-right:5px;">尾页</a>
                    <!--<a th:if="${number lt totalPages}" th:href="@{'javascript:__doPostPage(\'Pager\','+${totalPages}+')'}"-->
                    <!--style="margin-right:5px;">尾页</a>-->
                    <!--<a th:if="${number eq totalPages}" disabled="disabled" style="margin-right:5px;">尾页</a>-->
                </div>
            </div>
        </div>
        <div class="housesLeft"  th:if="${#lists.isEmpty(comments) || #lists.size(comments) == 0}">
            <div class="title">
                <p>用户点评</p>
                <!--<a th:href="${'/house/appraise/'+projectId+'-'+projectType+'.htm'}" style="color: #666;float: right;font-size: 14px;margin-right: -14px;">查看更多 ></a>-->
            </div>
            <div class="border_wei">
                <div class="houseAskNoList">
                    <p>暂无用户点评，快来点评一下吧~~~</p>
                    <!--<a href="" target="_blank">我要提问</a>-->
                    <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') == null}" >
                        <a th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login">我要点评</a>
                    </div>
                    <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') != null}" >
                        <a id="toAddComment" th:if="${#session?.getAttribute('sessionId') != null}">我要点评</a>
                    </div>
                    <script th:inline="javascript">
                        var memberType = $("#memberType").val();
                        var projectId = [[${projectId}]];
                        var projectType = [[${projectType}]];
                        $("#toAddComment").click(function () {
                            if (memberType == 1) {
                                $("#toAddComment").attr("href","/comment/"+projectId+"/"+projectType);
                            }else if (memberType == 2) {
                                $("#toAddComment").attr("href","/commentAgent/"+projectId+"/"+projectType);
                            }
                        })
                    </script>
                </div>
            </div>

        </div>
        <script>
            /*点击我要咨询的js*/
            $(".agentUrl").live("click",function(){
                var agentId = $(this).find(".agentId").val();
                if (agentId == undefined) {
                }
                if($("#glzs").val()=="" || $("#glzs").val()== null || $("#glzs").val()== undefined){
                }else{
                    $.ajax({
                        type: "post",
                        url: "/getAgentInfo",
                        data: {agentId:agentId},
                        scriptCharset: 'utf-8',
                        dataType: "json",
                        success: function (data) {
                            /*var data = eval(data);*/
                            var content = data.content;
                            if (data.status == 1) {
                                //弹出框复制
                                $("#AgentId").text(content.MemberID);
                                $("#AgentPic").attr("src",content.agentPic);
                                $("#AgentName").text(content.agentName);
                                $("#AgentAskCount").text(content.askCount);
                                $("#AgentShop").text(content.IntermediaryName);
                                //显示弹出框
                                $(".consultAlert").show();
                            }
                            else {
                            }
                        }
                    });
                }
            })
            /*关于回复的js*/
            $(".SubmitReply").live("click" ,function () {
                var sessionId = $("#glzs").val();
                if(sessionId != null && sessionId != undefined && sessionId != ''){
                    var text = $(this).parent().find("textarea").val();
                    var params  = {
                        content: text,
                        commentId: $(this).attr("commentId"),
                        sessionId:sessionId
                    };
                    $.ajax({
                        type: "POST",
                        url: "/saveReply",
                        data: JSON.stringify(params),
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        dataType: "json",
                        success: function (data) {
                            if (data && data.status == 1) {
                                Alertljz.ShowReplyAlert("回复成功，请耐心等待审核！", "https://my.fangxiaoer.com/reply");
                            } else {
                                Alertljz.ShowReplyAlert(data.msg);
                            }

                        }
                    });
                } else {
//                    $(".modal-backdrop").show();
//                    $("#login").show();
                    //Alertljz.ShowAlert("登录以后才可以回复哦!");
                }
            });
            photo.init()
            $(".userSynthesize>li").each(function(){
                if((parseInt($(this).find(".contentInfoMmore_sun").height())>75)&&($(this).find(".contentShow").hasClass("openAndClose"))){
                    $(this).find(".contentInfoMmore_sun").css("max-height","75px")
                    $(this).find(".contentShow").show()
                    $(this).find(".contentShow").addClass("open")
                }
                if($(this).find(".contentInfoImg li").length>4){
                    $(this).find(".contentShow").show()
                    $(this).find(".contentShow").addClass("open")
                    $(this).find(" .contentLeft .contentInfoImg li").hide()
                    $(this).find(" .contentLeft .contentInfoImg li").eq(0).show()
                    $(this).find(" .contentLeft .contentInfoImg li").eq(1).show()
                    $(this).find(" .contentLeft .contentInfoImg li").eq(2).show()
                    $(this).find(" .contentLeft .contentInfoImg li").eq(3).show()
                }
            })
            $(".contentShow").addClass("open")
            $(".contentShow").on("click",function(){
                if($(this).hasClass("open")){
                    $(this).removeClass("open")
                    $(this).parent().parent().find(".contentInfoMmore_sun").css("max-height","99999999px");
                    $(this).parent().parent().find(".contentInfoImg li").show()
                    $(this).parent().parent().find(".contentInfoImg span").hide()
                    $(this).text("收起全文")
                }else{
                    $(this).text("查看全文")
                    $(this).addClass("open")
                    $(this).parent().parent().find(".contentInfoMmore_sun").css("max-height","75px")
                    $(this).parent().parent().find(".contentInfoImg li").hide()
                    $(this).parent().parent().find(".contentInfoImg li").eq(0).show()
                    $(this).parent().parent().find(".contentInfoImg li").eq(1).show()
                    $(this).parent().parent().find(".contentInfoImg li").eq(2).show()
                    $(this).parent().parent().find(".contentInfoImg li").eq(3).show()
                    $(this).parent().parent().find(".contentInfoImg span").show()
                }
            })
            $(".contentReply").click(function(){
                if($(this).parent().parent().find(".reply").eq(0).css("display")=="none"){
                    $(".reply").slideUp()
                    $(this).parent().parent().find(".reply").eq(0).slideDown()
                    $(this).parent().parent().find(".commentReply").show();//回复列表展示
                }else{
                    $(".reply").slideUp()
                    $(this).parent().parent().find(".commentReply").hide();//回复列表隐藏
                }

            })
            $(".replay_btn").click(function(){
                if($(this).parent().find(".reply").css("display")=="none"){
                    $(".reply").slideUp()
                    $(this).parent().find(".reply").slideDown()
                }else{
                    $(".reply").slideUp()
                }
            })
            $(".userSynthesize .contentLeft .contentInfoImg li img").click(function(){
                photo.maxIndex=$(this).parent().parent().parent().find(".bigImgShow li").length
                photo.ind=$(this).parent().index()
                photo.showInd=photo.ind
                $(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
                console.log(photo.maxIndex)
                console.log(photo.ind)
                $(this).parent().parent().parent().find(".bigImgShow").show()
            })
            $("#alertClose").live("click",function(){
                window.location.reload()
            })


            for (var i=0; i<$(".userSynthesize").children("li").length;i++) {
                $(".userSynthesize").children("li").eq(i)
                if( $(".userSynthesize").children("li").eq(i).find(".contentInfoImg").length<1){
                    $(".userSynthesize").children("li").eq(i).find(".contentBottom").css("margin-top","20px")
                }
            }
        </script>
    </div>
</body>
</html>
