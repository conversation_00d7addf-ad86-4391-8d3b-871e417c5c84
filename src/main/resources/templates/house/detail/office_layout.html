

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${houseInfo.projectName+'户型图_沈阳'+houseInfo.projectName+'户型信息_'+houseInfo.projectName+'户型结构 - 房小二网'}"></title>
    <meta name="keywords" th:content="${houseInfo.projectName+'户型图,沈阳'+houseInfo.projectName+'户型信息,'+houseInfo.regionName+houseInfo.projectName+'户型类型,'+houseInfo.projectName+'户型平面图'}" >
    <meta name="description" th:content="${'沈阳'+houseInfo.projectName+',房小二网为你提供'+houseInfo.projectName+'详细户型图，查找'+houseInfo.regionName+houseInfo.projectName+'户型结构，户型种类，公摊面积等基本信息尽在房小二网!'}">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/nphotos/'+projectId+'-wxt-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />
    <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/housestyle1.css?v=20190507" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/Freefone1.css?v=20180522" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/new_house.js?v=20180904"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript"></script>
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
    <style>
        .Freefone{display: none}
        .FreefonePopup input{border: none}
        .FreefonePopup dl {
            width: 380px;
            padding: 10px 40px;
            margin-left: -190px;
        }
        .FreefonePopup dl dd>h2{width: 100%}
        .titleTypeR{
            position: unset;
            width: 38px;
            display: inline-block;
            height: 20px;
            font-weight: normal;
        }


    </style>
</head>
<body>
<!--户型详情-->
<div class="w1210">
    <input th:if="${#session?.getAttribute('sessionId') != null}" id="fxe_sessionId" type="hidden" th:value="${#session?.getAttribute('sessionId')}">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>


    <div class="cl"></div>
    <div th:include="house/detail/fragment_menu::menu" th:with="type=3"></div>

    <div class="housetype"></div>
    <div class="houseTypeYes" th:if="${!#lists.isEmpty(layouts) || #lists.size(layouts) != 0}">
        <div class="forsearch">
            <ul>
                <div id="huxingid">
                    <li><p>户　　型：</p>
                        <a th:each="roomType:${roomType}" th:text="${roomType.name}" th:href="${roomType.url}" th:class="${roomType.selected}? 'hover':''"></a>
                        <div class="cl"></div>
                    </li>
                </div>
            </ul>
        </div>

        <div class="search">
            <a class="btn_left before"></a>
            <a class="btn_right next"></a>
            <div class="search_gd" >
                <ul>
                    <li th:id="'lay_' + ${roomDetail.layId}" th:each="roomDetail:${layouts}" >
                        <a th:href="${baseUrl + '-l' + roomDetail.layId}"><th:block th:text="${roomDetail.roomType}+'室'+${roomDetail.hallType}+'厅'+${roomDetail.guardType}+'卫'"></th:block><br>建筑面积：<th:block th:text="${roomDetail.buildArea}+'㎡'"></th:block>
                            <i th:if="${roomDetail.state eq '在售'}" class="saleTypeIcon saleTypeIcon1" >在售</i>
                            <i th:if="${roomDetail.state eq '待售'}" class="saleTypeIcon saleTypeIcon2" >待售</i>
                            <i th:if="${roomDetail.state eq '售罄'}" class="saleTypeIcon saleTypeIcon3" >售罄</i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="cl"></div>
        <div th:each="roomOne,iterStat:${layouts}" th:if="${layId == roomOne.layId}">
            <div class="w huxingMain" ><!--基本信息-->
                <div class="number">
                    <h1>户型图（<span class="index">1</span>/<span class="all">16</span>）</h1>
                </div>
                <div class="pic">

                    <div class="show">
                        <div class="lunbo">
                            <a th:each="layout:${layout}"  class="various1" target="_blank"><img th:lang="'lay_' + ${layout.layId}" th:src="${layout.imageUrl}" th:alt="${layout.description}"></a>
                        </div>
                    </div>
                    <div class="left_btn">
                        <img src="https://static.fangxiaoer.com/web/images/Villa/left_btn.png" alt=""/>
                    </div>
                    <div class="right_btn">
                        <img src="https://static.fangxiaoer.com/web/images/Villa/right_btn.png" alt=""/>
                    </div>
                    <div class="look"><h1>查看大图</h1></div>
                </div>
                <div class="right">
                    <div class="style" style="margin: 0">
                        <!--<p th:text="${roomOne.houseTypeStr}"></p>-->
                        <th:block th:text="${roomOne.title}"></th:block>
                        <!--<i th:text="${roomOne.state}" >
                        </i>-->
                        <s  th:if="${roomOne.state eq '在售'}" class="saleTypeIcon saleTypeIcon1 titleTypeR" >在售</s>
                        <s  th:if="${roomOne.state eq '待售'}" class="saleTypeIcon saleTypeIcon2 titleTypeR" >待售</s>
                        <s  th:if="${roomOne.state eq '售罄'}" class="saleTypeIcon saleTypeIcon3 titleTypeR" >售罄</s>
                        <i th:text="${roomOne.houseTypeStr}" style="color:#48add6;background: #eff3fd;border: none"></i>
                    </div>



                    <div id="voiceCode" style="display: none;" th:if="${roomOne.voiceTab ne null and #strings.toString(roomOne.voiceTab) eq '1' and !#lists.isEmpty(roomOne.voiceInfo)}">
                        <div>
                            <img src="" id="showVoice">
                            <h6>扫描二维码<br>听我的自我介绍</h6>
                        </div>
                        <img id="codelogo" style="display: none" src="https://imageicloud.fangxiaoer.com/event/2022/10/11/133502165.png" crossorigin="anonymous">
                        <div id="qrcodeInfo" style="display: none"></div>
                        <script type="text/javascript" th:inline="javascript">
                            /*<![CDATA[*/
                            var layoutProject = [[${projectId}]]
                            var voiceLayId = [[${roomOne.layId}]]
                            /**该方法用来绘制一个有填充色的圆角矩形
                             *@param cxt:canvas的上下文环境
                             *@param x:左上角x轴坐标
                             *@param y:左上角y轴坐标
                             *@param width:矩形的宽度
                             *@param height:矩形的高度
                             *@param radius:圆的半径
                             *@param fillColor:填充颜色
                             **/
                            function fillRoundRect(cxt, x, y, width, height, radius, /*optional*/ fillColor) {
                                //圆的直径必然要小于矩形的宽高
                                if (2 * radius > width || 2 * radius > height) { return false; }

                                cxt.save();
                                cxt.translate(x, y);
                                //绘制圆角矩形的各个边
                                drawRoundRectPath(cxt, width, height, radius);
                                cxt.fillStyle = fillColor || "#000"; //若是给定了值就用给定的值否则给予默认值
                                cxt.fill();
                                cxt.restore();
                            }
                            function drawRoundRectPath(cxt, width, height, radius) {
                                cxt.beginPath(0);
                                //从右下角顺时针绘制，弧度从0到1/2PI
                                cxt.arc(width - radius, height - radius, radius, 0, Math.PI / 2);

                                //矩形下边线
                                cxt.lineTo(radius, height);

                                //左下角圆弧，弧度从1/2PI到PI
                                cxt.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);

                                //矩形左边线
                                cxt.lineTo(0, radius);

                                //左上角圆弧，弧度从PI到3/2PI
                                cxt.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);

                                //上边线
                                cxt.lineTo(width - radius, 0);

                                //右上角圆弧
                                cxt.arc(width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);

                                //右边线
                                cxt.lineTo(width, height - radius);
                                cxt.closePath();
                            }
                            var voiceUrl = "https://m.fangxiaoer.com/fang1/"+layoutProject+"-1/layout/-pid"+layoutProject+"-pt1-l" + voiceLayId;
                            $('#qrcodeInfo').qrcode({
                                render : "canvas",
                                width: 160,
                                height: 160,
                                text: voiceUrl
                            }).hide();
                            var canvasinfo = $("#qrcodeInfo canvas")[0];
                            //添加logo
                            var codeImage = document.querySelector("#codelogo");
                            codeImage.crossOrigin = "anonymous"
                            var ctx = canvasinfo.getContext('2d')
                            fillRoundRect(ctx, 59, 59, 42, 42 ,3, '#fff')
                            ctx.drawImage(codeImage, 61, 61, 38, 38);
                            $('#showVoice').attr('src', canvasinfo.toDataURL('image/png'));
                            $("#voiceCode").show();
                            /*]]>*/
                        </script>
                    </div>

                    <div class="info">
                        <ul class="type_wei_wun" style="border-bottom: 1px solid #eee;margin-bottom: 20px;padding-bottom: 28px;">
                            <li class="huxing_wei" ><label>户　　型</label><p th:text="${roomOne.roomType}+'室'+${roomOne.hallType}+'厅'+${roomOne.guardType}+'卫'"></p></li>

                            <li class="type_wei"><label>类　　型</label><p th:text="${roomOne.houseTypeStr}"></p></li>
                            <div class="cl"></div>
                            <li class="jian_wei"><label>建筑面积</label><p th:text="${roomOne.trueArea}+'㎡'"></p></li>

                            <li class="zhengf_wei" th:unless="${#strings.isEmpty(roomOne.freeArea)}"><label>赠送面积</label><p th:text="${roomOne.freeArea}"></p></li>
                            <div class="cl"></div>
                        </ul>
                        <div class="price"><label>参考价格</label>
                            <ul class="type-Btn">
                                <li th:unless="${roomOne.price} == 0"><i th:text="${roomOne.price}"></i>元/㎡ </li>
                                <li th:if="${roomOne.price} == 0"><i>待定</i></li>
<!--                                <a onclick="showUnuseCode(1)" value="" class="getChangePzBtn"><i></i>获取最新价格变动</a>-->

                                <div class="hxBtnNew" th:if="${!#lists.isEmpty(layouts) != null and #lists.size(layouts) > 0 and roomOne.state ne '售罄'}">
                                    <p class="hxBtnNew1 type-compare-btn1"  th:data-layId="${ roomOne.layId}" th:data-layName="${#numbers.formatInteger(roomOne.buildArea,2)+'㎡'+roomOne.roomType+'室'+roomOne.hallType+'厅'+roomOne.guardType+'卫'+houseInfo.projectName}"><i></i>户型对比</p>
                                    <p class="hxBtnNew1 type-compare-btn2" th:data-layId="${roomOne.layId}" th:data-layName="${#numbers.formatInteger(roomOne.buildArea,2)+'㎡'+roomOne.roomType+'室'+roomOne.hallType+'厅'+roomOne.guardType+'卫'}" th:onclick="'deletelayoud2('+${roomOne.layId}+')'"><i></i>取消对比</p>
                                    <p class="hxBtnNew2" data-toggle="modal"  th:if="${#session?.getAttribute('sessionId') == null}" href="#login"><i></i>户型收藏</p>
                                    <p class="hxBtnNew2" data-toggle="modal"  th:if="${#session?.getAttribute('sessionId') != null}"  id="hxShouc" th:data-layId="${ roomOne.layId}"><i></i>户型收藏</p>
                                    <input  id="fxe_layId" type="hidden" th:value="${roomOne.layId}">
                                </div>
                            </ul>
                        </div>
                        <script type="application/javascript">
                            $(document).ready(function () {
                                var sessionId = $("#fxe_sessionId").val()
                                var fxe_layId = $("#fxe_layId").val()

                                console.log(sessionId)
                                if(sessionId != ""){//页面加载登录后判断是否收藏此户型
                                    $.ajax({
                                        type:"POST",
                                        url:"/manageCollect",
                                        data:{
                                            layoutId:fxe_layId ,
                                            sessionId:sessionId,
                                            method:"checkFavorite",
                                        },
                                        success:function (data) {
                                            console.log(data)
                                            if(data.status == 1 ){
                                                if (data.content == 1){
                                                    $("#hxShouc").addClass("newFavorite")
                                                }else if(data.content == 0){
                                                    $("#hxShouc").addClass("cancelFavorite")

                                                }
                                            }
                                        },
                                    })
                                }
                                $('body').on('click', '.newFavorite', function() {
                                    $.ajax({
                                        type:"POST",
                                        url:"/manageCollect",
                                        data:{
                                            layoutId:fxe_layId,
                                            sessionId:sessionId,
                                            method:"cancelFavorite",
                                        },
                                        success:function (data) {
                                            $("#hxShouc").removeClass("newFavorite")
                                            $("#hxShouc").addClass("cancelFavorite")

                                        },
                                    })
                                })

                                $('body').on('click', '.cancelFavorite', function() {
//                                $(".cancelFavorite").click(function () {
                                    $.ajax({
                                        type:"POST",
                                        url:"/manageCollect",
                                        data:{
                                            layoutId:fxe_layId,
                                            sessionId:sessionId,
                                            method:"newFavorite",
                                        },
                                        success:function (data) {
                                            if(data.status == 1 ){
                                                $("#hxShouc").addClass("newFavorite")
                                                $("#hxShouc").removeClass("cancelFavorite")
                                            }

                                        },
                                    })
                                })




                            })


                        </script>
                        <div class="price" style="margin-top:0px;display: block;border-bottom: 1px solid #eee;margin-bottom: 10px;padding-bottom: 12px;" th:unless="${roomOne.price} == 0"><label>参考总价</label>
                            <ul><li><i th:text="${roomOne.totalPrice}"></i>万元/套[参考总价=参考均价*建筑面积]</li>
                            </ul></div>
                        <ul>
                            <li class="hid"><label>项目特色</label><span class="t1">地铁房</span><span class="t2">刚需房</span><span class="t3">电梯洋房</span><span class="t1">墅区洋房</span><span class="t2"></span><span class="t3"></span></li>
                            <div class="cl"></div>
                            <li th:if="${!#strings.isEmpty(roomOne.building)}"><label>分布楼栋</label><p th:text="${roomOne.building}"></p></li>
                            <li th:if="${!#strings.isEmpty(roomOne.description)}" style="overflow: hidden;"><label>户型解析</label><p class="huxing_p" th:text="${roomOne.description}" ></p></li>
                        </ul>
                    </div>
                    <!--<div class="tel ">-->
                    <!--   <div class="tel1" style="line-height: 33px;margin-top: 24px;"><i>售楼处咨询电话</i><b> <th:block th:text="${houseInfo.sortelTel}"></th:block>&nbsp;&nbsp;</b> </div>-->
                    <div class="tel1" style="line-height: 33px;margin-top: 24px;"><i>售楼处咨询电话</i><b><th:block th:utext="${#strings.toString(houseInfo.sortelTel).replace('转','<b>转</b>')}"></th:block>&nbsp;&nbsp;</b> </div>
<!--                    <div class="tel2 " style="display: block;margin-right: 8px !important;">-->
<!--                        <a onclick="showUsercode(2) " style="cursor:pointer ; display: block;margin-top: 5px;" >免费通话</a>-->
<!--                    </div>-->

                    <div></div>
                    <!-- </div>-->
                </div>
                <div class="cl"></div>
            </div>
        </div>
    </div>

    <!--户型暂无时提示-->
    <div class="newHouseTypeNo" th:if="${#lists.isEmpty(layouts) || #lists.size(layouts) == 0}">
        <img src="https://static.fangxiaoer.com/web/images/sy/house/house/newHouseTypeNo.jpg" alt="">
        <div>
            <p><span th:text="${houseInfo.projectName}"></span>的户型正在完善中...</p>
            <p>获取更多信息请咨询免费电话：<span style="color: #ff5200">************</span></p>
        </div>

    </div>

    <!--小二管家免费电话-->
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
    <div class="w ">
        <div th:include="house/detail/fragment_freefone::Freefone"></div>
        <div class="cl"></div>
    </div>

    <div class="cl"></div>
    <!--户型弹窗-->
    <div class="layer_box">
        <table>
            <tr>
                <td>
                    <div class="layer">
                        <h1>户型图（<span class="index">1</span>/<span class="all">16</span>）</h1>
                        <div class="photo">
                            <div class="lunbo">
                                <ul>
                                    <li th:each="item:${layout}">
                                        <img th:src="${item.imageUrl}" th:alt="${item.description}" />
                                    </li>
                                    <div class="clearfix"></div>
                                </ul>
                            </div>
                        </div>
                        <div class="close">
                            <img src="https://static.fangxiaoer.com/web/images/Villa/close.png" />
                        </div>
                        <div class="left_btn">
                            <img src="https://static.fangxiaoer.com/web/images/Villa/left_wei.png" class="left_wei" />
                            <img src="https://static.fangxiaoer.com/web/images/Villa/left_wei02.png" class="hover" />
                        </div>
                        <div class="right_btn">
                            <img src="https://static.fangxiaoer.com/web/images/Villa/right_wei.png" class="right_wei"/>
                            <img src="https://static.fangxiaoer.com/web/images/Villa/right_wei02.png" class="hover" />
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
    <div class="cl"></div>

    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::commonFloat"></div>
    <div class="cl"></div>
<!--    <div th:include="house/detail/fragment_login::login"></div>-->
    <!--无验证码订单-->
    <div  th:include="house/detail/fragment_order::unuseCode"  ></div>
    <div  th:include="house/detail/fragment_order::guideMessage"  ></div>
    <!--有验证码订单-->
    <div  th:include="house/detail/fragment_order::useCode"  ></div>


    <script language="javascript">
        $(function () {
            id = $(".pic img").attr("lang")
            $("#" + id).addClass("hover")

            var n = $(".search_gd li").length
            if ($(".search_gd li.hover").index() < 1) {
                $(".btn_left").removeClass("before")
                $(".btn_left").addClass("zbefore")
            }
            if ($(".search_gd li.hover").index() > n - 2) {
                $(".btn_right").removeClass("next")
                $(".btn_right").addClass("znext")
            }

            tt = ($(".search_gd li.hover").index() - 7) * 142
            //if (tt > 0) { $(".search_gd ul").css("left", -tt) }
            //向右滑动
            $(".btn_right").click(function () {
                if (num1 < num0) {
                    num1++;
                    $(".search_gd ul").animate({ marginLeft: num1 * -1105 + "px" }, 1000);
                    $(".btn_left").removeClass("zbefore");

                } else if (num1 == num0) {
                    $(".search_gd ul").animate({ marginLeft: num1 * -1105 + "px" }, 1000);
                }
                if (num1 == num0) {
                    $(".btn_right").addClass("znext")
                }
                console.log(num1);

            })
            //向左滑动
            $(".btn_left").click(function () {
                if (num1 > 0) {
                    num1--;
                    $(".search_gd ul").animate({ marginLeft: num1 * -1105 + "px" }, 500);
                    $(".btn_right").removeClass("znext");
                } else if (num1 == 0) {
                    $(".search_gd ul").animate({ marginLeft: num1 * -1105 + "px" }, 500);
                }
                if (num1 == 0) {
                    $(".btn_left").addClass("zbefore");
                }
            })
            var ind = $(".search_gd ul").children().length;//
            //var num = Math.floor(ind + 1 / 8);
            var num0 = Math.floor(ind / 7);
            var num1 = Math.floor($(".search_gd .hover").index() / 7);//
            //如果不足8个不加箭头
            if (ind <= 7) {
                $(".searh li").css("margin-right", "26px");
                $(".search_gd").width("1170px");
                $(".btn_right").css("display", "none");
                $(".btn_left").css("display", "none");
            }
            //如果没有选中默认选中第一个
            if (num1 == -1) {
                num1 = 0;
                $(".search_gd ul li").eq(0).addClass("hover");
            }
            //设置初始位置
            $(".search_gd ul").css({ "margin-left": num1 * -1105 + "px" });
            if (num1 == 0) {
                $(".btn_left").addClass("zbefore");
            }
            if (num1 == num0) {
                $(".btn_right").addClass("znext")
            }
        })


    </script>
    <div class="modal-backdrop  in" id="loginzhezhao" style="display: none;"></div>
    <div th:include="fragment/fragment::footer_detail"></div>
</div>
</body>
</html>
