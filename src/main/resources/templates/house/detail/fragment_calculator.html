<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
</head>
<body>
<div th:fragment="calculator">
    <div class="title ttt"><p>参考月供</p></div>
    <div id="js201" class="yinchang">
        <div class="hj-col-lg-22">
            <div class="hj-row">
                <div class="jsq_l" style="position: relative;">
                    <a href="/static/business.htm" target="_blank"><div class="smore">查看更多贷款方式<i>></i></div></a>
                    <div class="month-pay" js="month-pay">
                        <h4>计算条件</h4>
                        <div class="mp-form-group" id="huxing">
                            <div class="mp-label">
                                选择户型
                            </div>
                            <div class="mp-form-control">
                                <div class="mp-select-mask tt1"></div>
                                <ul tabindex="5000" class="optgroup" id="htype-dropdown" style="overflow-y: auto;">
                                    <li th:each="layout:${layoutDetail}"  th:data-code='${#numbers.formatInteger(layout.buildArea,2)}' th:data-glzs='${layout.houseTypeStr}' th:text="${layout.roomType}+'室'+${layout.hallType}+'厅'+${layout.guardType}+'卫'+${#numbers.formatInteger(layout.buildArea,2)}+'㎡'">
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="mp-form-group">
                            <div class="mp-label">
                                估算总价
                            </div>
                            <div class="mp-form-control">
                                <input maxlength="5" class="mp-input mp-input-md" id="totalHousePrice" type="text" total-price="101"  placeholder="请输入预算" />
                                <span class="mp-unit">万元</span>
                            </div>
                        </div>
                        <div class="mp-form-group">
                            <div class="mp-label">
                                按揭成数
                            </div>
                            <div class="mp-form-control">
                                <div class="mp-select-mask" id="swf">
                                    7成
                                </div>
                                <ul tabindex="5001" class="optgroup" id="percent-dropdown" style="overflow-y: auto;">
                                    <li data-code="0.1">1成</li>
                                    <li data-code="0.2">2成</li>
                                    <li data-code="0.3">3成</li>
                                    <li data-code="0.4">4成</li>
                                    <li data-code="0.5">5成</li>
                                    <li data-code="0.6">6成</li>
                                    <li data-code="0.7">7成</li>
                                    <li data-code="0.8">8成</li>
                                    <li data-code="0.9">9成</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mp-form-group">
                            <div class="mp-label">
                                贷款类别
                            </div>
                            <div class="mp-form-control">
                                <div class="mp-select-mask">
                                    商业贷款
                                </div>
                                <ul class="optgroup" id="daikuan_type_list">
                                    <li data-code="1">商业贷款</li>
                                    <li data-code="2">公积金贷款</li>
                                    <li data-code="3">组合贷款</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mp-form-group" id="group-dk-area" style="display: none;">
                            <div class="mp-label">
                                贷款金额
                            </div>
                            <div class="mp-form-control">
                                <span class="mp-loan-total">(贷款总额<i id="group-dk-total"></i>万)</span>
                                <div class="sec-input">
                                    <span class="type-name">公积金</span>
                                    <input class="mp-input mp-input-md" id="total-price-gjj" type="text" />
                                    <span class="mp-unit">万元</span>
                                </div>
                                <div class="sec-input">
                                    <span class="type-name">商贷</span>
                                    <input class="mp-input mp-input-md" id="total-price-sy" type="text" />
                                    <span class="mp-unit">万元</span>
                                </div>
                            </div>
                        </div>
                        <div class="mp-form-group">
                            <div class="mp-label">
                                贷款时间
                            </div>
                            <div class="mp-form-control">
                                <div class="mp-select-mask" id="shijian">
                                    20年(240个月)
                                </div>
                                <ul tabindex="5002" class="optgroup" id="time-dropdown" style="overflow-y: auto;">
                                    <li data-code="1">1年(12个月)</li>
                                    <li data-code="2">2年(24个月)</li>
                                    <li data-code="3">3年(36个月)</li>
                                    <li data-code="4">4年(48个月)</li>
                                    <li data-code="5">5年(60个月)</li>
                                    <li data-code="6">6年(72个月)</li>
                                    <li data-code="7">7年(84个月)</li>
                                    <li data-code="8">8年(96个月)</li>
                                    <li data-code="9">9年(108个月)</li>
                                    <li data-code="10">10年(120个月)</li>
                                    <li data-code="11">11年(132个月)</li>
                                    <li data-code="12">12年(144个月)</li>
                                    <li data-code="13">13年(156个月)</li>
                                    <li data-code="14">14年(168个月)</li>
                                    <li data-code="15">15年(180个月)</li>
                                    <li data-code="16">16年(192个月)</li>
                                    <li data-code="17">17年(204个月)</li>
                                    <li data-code="18">18年(216个月)</li>
                                    <li data-code="19">19年(228个月)</li>
                                    <li data-code="20">20年(240个月)</li>
                                    <li data-code="21">21年(252个月)</li>
                                    <li data-code="22">22年(264个月)</li>
                                    <li data-code="23">23年(276个月)</li>
                                    <li data-code="24">24年(288个月)</li>
                                    <li data-code="25">25年(300个月)</li>
                                    <li data-code="26">26年(312个月)</li>
                                    <li data-code="27">27年(324个月)</li>
                                    <li data-code="28">28年(336个月)</li>
                                    <li data-code="29">29年(348个月)</li>
                                    <li data-code="30">30年(360个月)</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mp-form-group" style="height: 8px;">
                            <div class="mp-label" style="height: 1px;">
                            </div>
                            <div class="mp-form-control">
                                <input id="currentChooseArea" type="hidden" value="1" />
                                <input id="currentBuildingPirce" type="hidden" value='6300' />
                                <input id="daikuan_type" type="hidden" value="1" />
                                <input id="daikuan_total_price" type="hidden" value="1" />
                                <input id="years" type="hidden" value="20" />
                                <input id="lilv" type="hidden" value="1.9" />
                                <input id="anjie" type="hidden" value="0.7" />
                                <div class="jsq_bg">
                                    <img src="https://static.fangxiaoer.com/js/jsq/house//jsq_bg.gif" alt="计算器">
                                </div>
                                <button class="hj-btn hj-btn-md hj-btn-red" id="mortgageCalculation" type="button">
                                    开始计算</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hj-col-lg-9 referesult">
                    <h4 class="referesult-title"><a href="/static/business.htm" target="_blank">查看更多</a>参考结果</h4>

                    <div id="pieChart">
                    </div>
                    <div id="priceLineChart" style="height: 16px;">
                    </div>
                    <span class="total-price" style="left: 206.5px; top: 270.5px;">总价 <strong><i id="ht-total-price"></i><span>万</span> </strong></span>
                    <ul>
                        <li style="margin-bottom: 10px;"><span class="fieldt">月均还款</span><i id="average_month_pay"></i></li>
                        <li><span class="square orange-square-std"></span><span class="field">参考首付</span><span
                                id="first_pay"></span></li>
                        <li><span class="square aqua-square"></span><span class="field">贷款金额</span><span
                                id="daikuan_total_price2"></span></li>
                        <li><span class="square purple-square"></span><span class="field">支付利息</span><span
                                id="pay_lixi"></span></li>
                        <li class="lprm"></li>
                    </ul>
                    <div class="result-tips">
                        备注：以等额本息计算结果，数据仅供参考
                    </div>
                </div>
            </div>
        </div>


        <script>
            if (!!window.ActiveXObject || "ActiveXObject" in window)
            {
                document.write('<script src="https://static.fangxiaoer.com/web/styles/sy/lpr/fangdai.js"><\/script>')//ie
            }else{
                document.write('<script src="https://static.fangxiaoer.com/web/styles/sy/lpr/fangdai1.js"><\/script>')
            }
        </script>
        <!--[if gt IE 8]>
        <style type="text/css">.yinchang{display:block !important}</style>
        <script src="https://static.fangxiaoer.com/js/jsq/house/index.js"></script>
        <script src="https://static.fangxiaoer.com/js/jsq/house/echarts-all.js"></script>
        <![endif]-->
        <!--[if !IE]><!-->
        <script src="https://static.fangxiaoer.com/js/jsq/house/index.js"></script>
        <script src="https://static.fangxiaoer.com/js/jsq/house/echarts-all.js"></script>
        <script>
            //计算器 默认 类型
            $("#htype-dropdown").attr("data-glzs", $("#htype-dropdown li").eq(0).attr("data-glzs"));
            $("#htype-dropdown li").each(function () {
                $(this).append($(this).attr("data-glzs"));
                var flzs = $(this).attr("data-glzs");
                var thisLi = $(this)
                $(".price li").each(function () {
                    //动态获取正则表达式
                    var reg1 = thisLi.attr("data-glzs");
                    var reg = $(this).html().split("<i>")[0] == "底商" ? "商铺" : $(this).html().split("<i>")[0];//存在 给出'底商'价格房源类型却是'商铺' 的问题   手动改'底商'为'商铺'
                    if (reg1 == reg) {
                        var price = $(this).find("i").text();
                        thisLi.attr("data-glzs", price)//修改为对应的价格
                    }
                })
            })
            //如果没有价格默认为第一个的价格
            $("#htype-dropdown li").each(function(){
                console.log(isNaN(parseInt($(this).attr("data-glzs"))))
                if(isNaN(parseInt($(this).attr("data-glzs")))){
                    $(this).attr("data-glzs", $(".price li i").eq(0).text());
                }
            })

        </script>
        <!--<![endif]-->

        <script>
            var monthLpr = parseInt($('#shijian').text()) * 12;

            getLPRLoanRate()//获取利率
            $("#loan_time_a").find('a').click(()=>{
                setTimeout(()=>{
                    monthLpr = parseInt($('#shijian').text()) * 12;
                    getLPRLoanRate()
                },500)
            })
            function getLPRLoanRate(){
                $.ajax({
                    type: "POST",
                    url: "/viewLoanFilter",
                    data: {'month': monthLpr,'discount':1},
                    success: function (data) {
                        // console.log(data)
                        $(".lprm").text('（利率: 公积金'+data.content.rate2+'%、商贷'+data.content.rate1+'%）')
                        $(".lprm").attr({'rate-sy':data.content.rate1,'rate-gjj':data.content.rate2})
                    }
                })
            }

        </script>

    </div>
    <!--计算器结束-->
</div>
<div th:fragment="generalCalculator">
    <link href="https://static.fangxiaoer.com/web/styles/generalCalculator.css" rel="stylesheet" type="text/css">
    <div class="generalCalculator">
        <div class="calculatorTitle"><p>房贷计算器</p></div>
        <div class="mainCalculator">
            <!-- 左侧表单 -->
            <div class="form-container">
                <div class="form-group">
                    <label for="houseTypeNum">房屋套数</label>
                    <select id="houseTypeNum">
                        <option value="首套">首套</option>
                        <option value="二套">二套</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="totalPrice">房屋总价</label>
                    <div class="input-with-unit">
                        <input
                                type="number"
                                id="totalPrice"
                                value=""
                                min="0"
                                max="999999"
                                oninput="this.value = this.value.length > 8 ? Math.min(99999999, this.value.slice(0,8)) : Math.max(0, this.value)"
                        />
                        <span class="unit-label-inside">万元</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="loanType">贷款类型</label>
                    <select id="loanType">
                        <option value="商业贷款">商业贷款</option>
                        <option value="公积金贷款">公积金贷款</option>
                        <option value="组合贷款">组合贷款</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="loanRatio">贷款比例</label>
                    <select id="loanRatio">
                        <option value="0.85">85%</option>
                        <option value="0.8">80%</option>
                        <option value="0.75">75%</option>
                        <option value="0.7">70%</option>
                        <option value="0.65">65%</option>
                        <option value="0.6">60%</option>
                        <option value="0.55">55%</option>
                        <option value="0.5">50%</option>
                        <option value="0.45">45%</option>
                        <option value="0.4">40%</option>
                        <option value="0.35">35%</option>
                        <option value="0.3">30%</option>
                        <option value="0.25">25%</option>
                        <option value="0.2">20%</option>
                        <option value="0.15">15%</option>
                        <option value="0.1">10%</option>
                        <option value="0.05">5%</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="loanYears">贷款年限</label>
                    <select id="loanYears"></select>
                </div>

                <div id="commercialRateGroup" class="form-group">
                    <label for="commercialRate">商贷利率</label>
                    <div class="input-with-unit">
                        <input type="number" id="commercialRate" step="0.001" value="3.95" />
                        <span class="unit-label-inside">%</span>
                    </div>
                </div>

                <div id="commercialAmountGroup" class="form-group hidden">
                    <label for="commercialAmount">商贷金额</label>
                    <div class="input-with-unit">
                        <input type="number" id="commercialAmount" value="0" step="0.01" />
                        <span class="unit-label-inside">万元</span>
                    </div>
                </div>

                <div id="fundRateGroup" class="form-group hidden">
                    <label for="fundRate">公积金利率</label>
                    <div class="input-with-unit">
                        <input type="number" id="fundRate" step="0.001" value="3.25" />
                        <span class="unit-label-inside">%</span>
                    </div>
                </div>

                <div id="fundAmountGroup" class="form-group hidden">
                    <label for="fundAmount" class="label2">公积金贷款金额</label>
                    <div class="input-with-unit">
                        <input type="number" id="fundAmount" value="0" step="0.01" />
                        <span class="unit-label-inside">万元</span>
                    </div>
                </div>
                <div id="calculateButton" class="calculate-button">开始计算</div>
                <div id="warning" class="warning hidden">
                    警告：贷款总额超过了最大贷款额 <span id="maxLoanAmount"></span>
                </div>
            </div>

            <!-- 右侧计算结果 -->
            <div class="result-container">
                <div class="resultTitle"><i class="resultTitleIcon"></i><span>计算结果</span></div>
                <div id="equalPrincipalInterestSection" class="result-section hidden">
                    <h3 class="resultTitle2">等额本息还款</h3>
                    <div class="result-block result-total">
                        <p>月供：</p>
                        <p><span class="showNum" id="equalPrincipalInterest_total_monthlyPayment"></span></p>
                        <p>总还款额：<span id="equalPrincipalInterest_total_totalPayment"></span></p>
                        <p>总利息：<span id="equalPrincipalInterest_total_totalInterest"></span></p>
                    </div>

                    <div id="equalPrincipalInterest_commercial" class="result-block result-commercial hidden">
                        <p class="resultTitle3">商业贷款部分</p>
                        <p>月供：</p>
                        <p><span class="showNum" id="equalPrincipalInterest_commercial_monthlyPayment"></span></p>
                        <p>总还款额：<span id="equalPrincipalInterest_commercial_totalPayment"></span></p>
                        <p>总利息：<span id="equalPrincipalInterest_commercial_totalInterest"></span></p>
                    </div>

                    <div id="equalPrincipalInterest_fund" class="result-block result-fund hidden">
                        <p class="resultTitle4">公积金贷款部分</p>
                        <p>月供：</p>
                        <p><span class="showNum"  id="equalPrincipalInterest_fund_monthlyPayment"></span></p>
                        <p>总还款额：<span id="equalPrincipalInterest_fund_totalPayment"></span></p>
                        <p>总利息：<span id="equalPrincipalInterest_fund_totalInterest"></span></p>
                    </div>
                </div>

                <div id="equalPrincipalSection" class="result-section hidden">
                    <h3 class="resultTitle2">等额本金还款</h3>
                    <div class="result-block result-total">
                        <div class="showNumtwo">
                            <div>
                                <p class="">首月月供：</p>
                                <p><span class="showNum" id="equalPrincipal_total_firstMonthPayment"></span></p>
                            </div>
                            <div>
                                <p>末月月供：</p>
                                <p><span class="showNum" id="equalPrincipal_total_lastMonthPayment"></span></p>
                            </div>
                        </div>
                        <p>总还款额：<span id="equalPrincipal_total_totalPayment"></span></p>
                        <p>总利息：<span id="equalPrincipal_total_totalInterest"></span></p>
                    </div>

                    <div id="equalPrincipal_commercial" class="result-block result-commercial hidden">
                        <p class="resultTitle3">商业贷款部分</p>
                        <div class="showNumtwo">
                            <div>
                                <p>首月月供：</p>
                                <p><span class="showNum" id="equalPrincipal_commercial_firstMonthPayment"></span></p>
                            </div>
                            <div>
                                <p>末月月供：</p>
                                <p><span class="showNum" id="equalPrincipal_commercial_lastMonthPayment"></span></p>
                            </div>
                        </div>
                        <p>月供递减：<span id="equalPrincipal_commercial_monthlyDecrease"></span></p>
                        <p>总还款额：<span id="equalPrincipal_commercial_totalPayment"></span></p>
                        <p>总利息：<span id="equalPrincipal_commercial_totalInterest"></span></p>
                    </div>

                    <div id="equalPrincipal_fund" class="result-block result-fund hidden">
                        <p class="resultTitle4">公积金贷款部分</p>
                        <div class="showNumtwo">
                            <div>
                                <p>首月月供：</p>
                                <p><span class="showNum" id="equalPrincipal_fund_firstMonthPayment"></span></p>
                            </div>
                            <div>
                                <p>末月月供：</p>
                                <p><span class="showNum" id="equalPrincipal_fund_lastMonthPayment"></span></p>
                            </div>
                        </div>
                        <p>月供递减：<span id="equalPrincipal_fund_monthlyDecrease"></span></p>
                        <p>总还款额：<span id="equalPrincipal_fund_totalPayment"></span></p>
                        <p>总利息：<span id="equalPrincipal_fund_totalInterest"></span></p>
                    </div>
                </div>
                <p class="color999">备注：本次计算仅作为购房参考，不能作为最终的购房依据。了解更准确的方案，建议咨询贷款银行</p>
            </div>

        </div>

    </div>
    <script>
        // DOM 元素
        const generalCalculatorMoneyshow = document.getElementById("generalCalculatorMoney");
        const houseTypeSelect = document.getElementById("houseTypeNum");
        const totalPriceInput = document.getElementById("totalPrice");
        const loanTypeSelect = document.getElementById("loanType");
        const loanRatioSelect = document.getElementById("loanRatio");
        const loanYearsSelect = document.getElementById("loanYears");
        const commercialRateInput = document.getElementById("commercialRate");
        const commercialAmountInput = document.getElementById("commercialAmount");
        const fundRateInput = document.getElementById("fundRate");
        const fundAmountInput = document.getElementById("fundAmount");
        const calculateButton = document.getElementById("calculateButton");
        const warningElement = document.getElementById("warning");
        const maxLoanAmountSpan = document.getElementById("maxLoanAmount");

        const commercialRateGroup = document.getElementById(
            "commercialRateGroup"
        );
        const commercialAmountGroup = document.getElementById(
            "commercialAmountGroup"
        );
        const fundRateGroup = document.getElementById("fundRateGroup");
        const fundAmountGroup = document.getElementById("fundAmountGroup");

        const equalPrincipalInterestSection = document.getElementById(
            "equalPrincipalInterestSection"
        );
        const equalPrincipalSection = document.getElementById(
            "equalPrincipalSection"
        );

        // 更新贷款利率的方法
        function updateLoanRates() {
            const houseType = houseTypeSelect.value;
            const loanType = loanTypeSelect.value;
            const loanYears = parseInt(loanYearsSelect.value) || 0;
            const isShortTerm = loanYears <= 5;

            if (loanType === "商业贷款") {
                if (houseType === "首套") {
                    commercialRateInput.value = isShortTerm ? 2.6 : 3.1;
                } else if (houseType === "二套") {
                    commercialRateInput.value = isShortTerm ? 3.3 : 3.3;
                }
            } else if (loanType === "公积金贷款") {
                if (houseType === "首套") {
                    fundRateInput.value = isShortTerm ? 2.1 : 2.6;
                } else if (houseType === "二套") {
                    fundRateInput.value = isShortTerm ? 2.53 : 3.08;
                }
            } else if (loanType === "组合贷款") {
                if (houseType === "首套") {
                    commercialRateInput.value = isShortTerm ? 2.6 : 3.1;
                    fundRateInput.value = isShortTerm ? 2.1 : 2.6;
                } else if (houseType === "二套") {
                    commercialRateInput.value = isShortTerm ? 3.3 : 3.3;
                    fundRateInput.value = isShortTerm ? 2.53 : 3.08;
                }
            }
        }

        // 初始化年份选择器
        function initYearSelect() {
            for (let i = 30; i >= 1; i--) {
                const option = document.createElement("option");
                option.value = i;
                option.textContent = i + "年";
                if (i === 30) {
                    option.selected = true;
                }
                loanYearsSelect.appendChild(option);
            }
        }

        // 根据贷款类型显示/隐藏相关字段
        function updateFieldsVisibility() {
            const loanType = loanTypeSelect.value;
            // 重置隐藏所有特定贷款类型字段
            commercialRateGroup.classList.add("hidden");
            commercialAmountGroup.classList.add("hidden");
            fundRateGroup.classList.add("hidden");
            fundAmountGroup.classList.add("hidden");
            if (loanType === "商业贷款") {
                commercialRateGroup.classList.remove("hidden");
                fundRateGroup.classList.add("hidden");
            } else if (loanType === "公积金贷款") {
                console.log(123)
                fundRateGroup.classList.remove("hidden");
                commercialRateGroup.classList.add("hidden");
            } else if (loanType === "组合贷款") {
                commercialRateGroup.classList.remove("hidden");
                commercialAmountGroup.classList.remove("hidden");
                fundRateGroup.classList.remove("hidden");
                fundAmountGroup.classList.remove("hidden");
                const totalPrice = parseFloat(totalPriceInput.value) || 0;
                const loanRatio = parseFloat(loanRatioSelect.value) || 0;
                commercialAmountInput.value = (totalPrice*loanRatio).toFixed(2);
                fundAmountInput.value = 0
            }

            updateLoanAmounts();
            updateLoanRates()
        }


        function houseTypeChangeProportion() {
            const loanType = loanTypeSelect.value;
            const houseType = houseTypeSelect.value;
            const totalPrice = parseFloat(totalPriceInput.value) || 0;
            if (houseType === "首套") {
                for (var i = 0; i < loanRatioSelect.options.length; i++) {
                    if (loanRatioSelect.options[i].value === '0.85') {
                        loanRatioSelect.selectedIndex = i;
                        break;
                    }
                }
            } else if (houseType === "二套") {
                for (var e = 0; e < loanRatioSelect.options.length; e++) {
                    if (loanRatioSelect.options[e].value === '0.75') {
                        loanRatioSelect.selectedIndex = e;
                        break;
                    }
                }
            }
            if(loanType === "组合贷款"){
                const loanRatio = parseFloat(loanRatioSelect.value) || 0;

                const maxLoanAmount = totalPrice * loanRatio;
                commercialAmountInput.value = maxLoanAmount.toFixed(2);
            }
            updateLoanAmounts()
            updateLoanRates()
        }



        // 更新贷款金额
        function updateLoanAmounts() {
            const totalPrice = parseFloat(totalPriceInput.value) || 0;
            const loanRatio = parseFloat(loanRatioSelect.value) || 0;
            const loanType = loanTypeSelect.value;

            // 最大贷款额（万元）
            const maxLoanAmount = totalPrice * loanRatio;
            if (loanType === "商业贷款") {
                commercialAmountInput.value = maxLoanAmount.toFixed(2);
                fundAmountInput.value = "0";
            } else if (loanType === "公积金贷款") {
                commercialAmountInput.value = "0";
                fundAmountInput.value = maxLoanAmount.toFixed(2);
            } else if (loanType === "组合贷款") {
                // 初始化组合贷款，商贷为全部贷款，公积金默认0元,
                commercialAmountInput.value = maxLoanAmount
                fundAmountInput.value = 0
            }

            validateCombinedLoan();
        }

        // 验证组合贷款金额
        function validateCombinedLoan() {
            const totalPrice = parseFloat(totalPriceInput.value) || 0;
            const loanRatio = parseFloat(loanRatioSelect.value) || 0;
            const loanType = loanTypeSelect.value;
            const commercialAmount = parseFloat(commercialAmountInput.value) || 0;
            const fundAmount = parseFloat(fundAmountInput.value) || 0;

            const maxLoanAmount = totalPrice * loanRatio;
            maxLoanAmountSpan.textContent = formatCurrency(maxLoanAmount * 10000); // 转为元显示

            if (
                loanType === "组合贷款" &&
                commercialAmount + fundAmount > maxLoanAmount + 0.01
            ) {
                // 添加0.01的误差容忍
                warningElement.classList.remove("hidden");
                return false;
            } else {
                warningElement.classList.add("hidden");
                return true;
            }
        }

        // 更新组合贷款金额，保持总额不变
        function updateCombinedLoanAmount(sourceInput) {
            if (loanTypeSelect.value !== "组合贷款") return;

            const totalPrice = parseFloat(totalPriceInput.value) || 0;
            const loanRatio = parseFloat(loanRatioSelect.value) || 0;
            const maxLoanAmount = totalPrice * loanRatio;

            if (sourceInput === commercialAmountInput) {
                const commercialAmount = parseFloat(commercialAmountInput.value) || 0;
                const newFundAmount = Math.max(0, maxLoanAmount - commercialAmount);
                fundAmountInput.value = newFundAmount.toFixed(2);
            } else if (sourceInput === fundAmountInput) {
                const fundAmount = parseFloat(fundAmountInput.value) || 0;
                const newCommercialAmount = Math.max(0, maxLoanAmount - fundAmount);
                commercialAmountInput.value = newCommercialAmount.toFixed(2);
            }

            validateCombinedLoan();
        }

        // 等额本息计算
        function calculateEqualPrincipalInterest() {
            // 将万元转换为元进行计算
            const commercialAmount =  (parseFloat(commercialAmountInput.value) || 0) * 10000;
            const fundAmount = (parseFloat(fundAmountInput.value) || 0) * 10000;
            const commercialRate = parseFloat(commercialRateInput.value) / 100 || 0;
            const fundRate = parseFloat(fundRateInput.value) / 100 || 0;
            const loanYears = parseInt(loanYearsSelect.value) || 30;

            let result = {
                commercial: { monthlyPayment: 0, totalPayment: 0, totalInterest: 0 },
                fund: { monthlyPayment: 0, totalPayment: 0, totalInterest: 0 },
                total: { monthlyPayment: 0, totalPayment: 0, totalInterest: 0 },
            };

            // 商业贷款计算
            if (commercialAmount > 0) {
                const monthlyRate = commercialRate / 12;
                const months = loanYears * 12;
                const monthlyPayment =
                    (commercialAmount *
                        monthlyRate *
                        Math.pow(1 + monthlyRate, months)) /
                    (Math.pow(1 + monthlyRate, months) - 1);
                const totalPayment = monthlyPayment * months;
                const totalInterest = totalPayment - commercialAmount;

                result.commercial = {
                    monthlyPayment,
                    totalPayment,
                    totalInterest,
                };
            }else if (commercialAmount == 0){

                const monthlyRate = 0;
                const months = 0;
                const monthlyPayment = 0;
                const totalPayment = 0;
                const totalInterest = 0;

                result.commercial = {
                    monthlyPayment,
                    totalPayment,
                    totalInterest,
                };
            }


            if (fundAmount > 0) {
                const monthlyRate = fundRate / 12;
                const months = loanYears * 12;
                const monthlyPayment =
                    (fundAmount * monthlyRate * Math.pow(1 + monthlyRate, months)) /
                    (Math.pow(1 + monthlyRate, months) - 1);
                const totalPayment = monthlyPayment * months;
                const totalInterest = totalPayment - fundAmount;

                result.fund = {
                    monthlyPayment,
                    totalPayment,
                    totalInterest,
                };
            }

            // 合计
            result.total = {
                monthlyPayment:
                    result.commercial.monthlyPayment + result.fund.monthlyPayment,
                totalPayment:
                    result.commercial.totalPayment + result.fund.totalPayment,
                totalInterest:
                    result.commercial.totalInterest + result.fund.totalInterest,
            };

            return result;
        }

        // 等额本金计算
        function calculateEqualPrincipal() {
            // 将万元转换为元进行计算
            const commercialAmount =
                (parseFloat(commercialAmountInput.value) || 0) * 10000;
            const fundAmount = (parseFloat(fundAmountInput.value) || 0) * 10000;
            const commercialRate = parseFloat(commercialRateInput.value) / 100 || 0;
            const fundRate = parseFloat(fundRateInput.value) / 100 || 0;
            const loanYears = parseInt(loanYearsSelect.value) || 30;

            let result = {
                commercial: {
                    firstMonthPayment: 0,
                    lastMonthPayment: 0,
                    monthlyDecrease: 0,
                    totalPayment: 0,
                    totalInterest: 0,
                },
                fund: {
                    firstMonthPayment: 0,
                    lastMonthPayment: 0,
                    monthlyDecrease: 0,
                    totalPayment: 0,
                    totalInterest: 0,
                },
                total: {
                    firstMonthPayment: 0,
                    lastMonthPayment: 0,
                    totalPayment: 0,
                    totalInterest: 0,
                },
            };

            // 商业贷款计算
            if (commercialAmount > 0) {
                const monthlyRate = commercialRate / 12;
                const months = loanYears * 12;
                const monthlyPrincipal = commercialAmount / months;
                const firstMonthPayment =
                    monthlyPrincipal + commercialAmount * monthlyRate;
                const monthlyDecrease = monthlyPrincipal * monthlyRate;
                const lastMonthPayment =
                    monthlyPrincipal + monthlyRate * monthlyPrincipal;

                // 计算总利息
                let totalInterest = 0;
                for (let i = 0; i < months; i++) {
                    const remainingPrincipal = commercialAmount - monthlyPrincipal * i;
                    totalInterest += remainingPrincipal * monthlyRate;
                }

                const totalPayment = commercialAmount + totalInterest;

                result.commercial = {
                    firstMonthPayment,
                    lastMonthPayment,
                    monthlyDecrease,
                    totalPayment,
                    totalInterest,
                };
            }else if (commercialAmount == 0 ){
                console.log(666)
                const firstMonthPayment =0;
                const monthlyDecrease = 0;
                const lastMonthPayment =0;

                // 计算总利息
                let totalInterest = 0;


                const totalPayment = 0

                result.commercial = {
                    firstMonthPayment,
                    lastMonthPayment,
                    monthlyDecrease,
                    totalPayment,
                    totalInterest,
                };
            }

            // 公积金贷款计算
            if (fundAmount > 0) {
                const monthlyRate = fundRate / 12;
                const months = loanYears * 12;
                const monthlyPrincipal = fundAmount / months;
                const firstMonthPayment = monthlyPrincipal + fundAmount * monthlyRate;
                const monthlyDecrease = monthlyPrincipal * monthlyRate;
                const lastMonthPayment =
                    monthlyPrincipal + monthlyRate * monthlyPrincipal;

                // 计算总利息
                let totalInterest = 0;
                for (let i = 0; i < months; i++) {
                    const remainingPrincipal = fundAmount - monthlyPrincipal * i;
                    totalInterest += remainingPrincipal * monthlyRate;
                }

                const totalPayment = fundAmount + totalInterest;

                result.fund = {
                    firstMonthPayment,
                    lastMonthPayment,
                    monthlyDecrease,
                    totalPayment,
                    totalInterest,
                };
            }

            // 合计
            result.total = {
                firstMonthPayment: result.commercial.firstMonthPayment + result.fund.firstMonthPayment,
                lastMonthPayment: result.commercial.lastMonthPayment + result.fund.lastMonthPayment,
                totalPayment: result.commercial.totalPayment + result.fund.totalPayment,
                totalInterest: result.commercial.totalInterest + result.fund.totalInterest,
            };

            return result;
        }

        // 处理计算按钮点击
        function handleCalculate() {
            if (validateCombinedLoan()) {
                const equalPrincipalInterestResult = calculateEqualPrincipalInterest();
                const equalPrincipalResult = calculateEqualPrincipal();

                displayEqualPrincipalInterestResults(equalPrincipalInterestResult);
                displayEqualPrincipalResults(equalPrincipalResult);
            }
        }

        // 显示等额本息结果
        function displayEqualPrincipalInterestResults(result) {
            const loanType = loanTypeSelect.value;
            // 显示结果区域
            equalPrincipalInterestSection.classList.remove("hidden");

            // 显示总计结果
            document.getElementById("equalPrincipalInterest_total_monthlyPayment").textContent = formatCurrency(result.total.monthlyPayment);
            document.getElementById("equalPrincipalInterest_total_totalPayment").textContent = formatCurrency(result.total.totalPayment);
            document.getElementById("equalPrincipalInterest_total_totalInterest").textContent = formatCurrency(result.total.totalInterest);

            // 显示商业贷款结果
            const commercialResultElement = document.getElementById("equalPrincipalInterest_commercial");
            if (result.commercial.monthlyPayment > 0) {
                document.getElementById("equalPrincipalInterest_commercial_monthlyPayment").textContent = formatCurrency(result.commercial.monthlyPayment);
                document.getElementById("equalPrincipalInterest_commercial_totalPayment").textContent = formatCurrency(result.commercial.totalPayment);
                document.getElementById("equalPrincipalInterest_commercial_totalInterest").textContent = formatCurrency(result.commercial.totalInterest);
                commercialResultElement.classList.remove("hidden");
            }else if (result.commercial.monthlyPayment == 0 && loanType != '公积金贷款') {
                document.getElementById("equalPrincipalInterest_commercial_monthlyPayment").textContent = formatCurrency(result.commercial.monthlyPayment);
                document.getElementById("equalPrincipalInterest_commercial_totalPayment").textContent = formatCurrency(result.commercial.totalPayment);
                document.getElementById("equalPrincipalInterest_commercial_totalInterest").textContent = formatCurrency(result.commercial.totalInterest);
                commercialResultElement.classList.remove("hidden");
            }else {
                commercialResultElement.classList.add("hidden");
            }
            // 公积金贷款计算


            // 显示公积金贷款结果
            const fundResultElement = document.getElementById("equalPrincipalInterest_fund");
            if (result.fund.monthlyPayment > 0) {
                document.getElementById("equalPrincipalInterest_fund_monthlyPayment").textContent = formatCurrency(result.fund.monthlyPayment);
                document.getElementById("equalPrincipalInterest_fund_totalPayment").textContent = formatCurrency(result.fund.totalPayment);
                document.getElementById("equalPrincipalInterest_fund_totalInterest").textContent = formatCurrency(result.fund.totalInterest);
                fundResultElement.classList.remove("hidden");
            }else if(loanType != '商业贷款' && result.fund.monthlyPayment == '0'){
                document.getElementById("equalPrincipalInterest_fund_monthlyPayment").textContent = '0'
                document.getElementById("equalPrincipalInterest_fund_totalPayment").textContent = '0'
                document.getElementById("equalPrincipalInterest_fund_totalInterest").textContent = '0'
                fundResultElement.classList.remove("hidden");
            } else {
                fundResultElement.classList.add("hidden");
            }
        }

        // 显示等额本金结果
        function displayEqualPrincipalResults(result) {
            // 显示结果区域
            equalPrincipalSection.classList.remove("hidden");

            // 显示总计结果
            document.getElementById("equalPrincipal_total_firstMonthPayment").textContent = formatCurrency(result.total.firstMonthPayment);
            document.getElementById("equalPrincipal_total_lastMonthPayment").textContent = formatCurrency(result.total.lastMonthPayment);
            document.getElementById("equalPrincipal_total_totalPayment").textContent = formatCurrency(result.total.totalPayment);
            document.getElementById("equalPrincipal_total_totalInterest").textContent = formatCurrency(result.total.totalInterest);
            const loanType = loanTypeSelect.value;
            // 显示商业贷款结果
            const commercialResultElement = document.getElementById(
                "equalPrincipal_commercial"
            );
            if (result.commercial.firstMonthPayment > 0 ) {
                document.getElementById("equalPrincipal_commercial_firstMonthPayment").textContent = formatCurrency(result.commercial.firstMonthPayment);
                document.getElementById("equalPrincipal_commercial_monthlyDecrease").textContent = formatCurrency(result.commercial.monthlyDecrease);
                document.getElementById("equalPrincipal_commercial_lastMonthPayment").textContent = formatCurrency(result.commercial.lastMonthPayment);
                document.getElementById("equalPrincipal_commercial_totalPayment").textContent = formatCurrency(result.commercial.totalPayment);
                document.getElementById("equalPrincipal_commercial_totalInterest").textContent = formatCurrency(result.commercial.totalInterest);
                commercialResultElement.classList.remove("hidden");
            }else if(result.commercial.firstMonthPayment == 0 && loanType != '公积金贷款') {
                document.getElementById("equalPrincipal_commercial_firstMonthPayment").textContent = formatCurrency(result.commercial.firstMonthPayment);
                document.getElementById("equalPrincipal_commercial_monthlyDecrease").textContent = formatCurrency(result.commercial.monthlyDecrease);
                document.getElementById("equalPrincipal_commercial_lastMonthPayment").textContent = formatCurrency(result.commercial.lastMonthPayment);
                document.getElementById("equalPrincipal_commercial_totalPayment").textContent = formatCurrency(result.commercial.totalPayment);
                document.getElementById("equalPrincipal_commercial_totalInterest").textContent = formatCurrency(result.commercial.totalInterest);
                commercialResultElement.classList.remove("hidden");
            } else {
                commercialResultElement.classList.add("hidden");
            }

            // 显示公积金贷款结果
            const fundResultElement = document.getElementById(
                "equalPrincipal_fund"
            );
            if (result.fund.firstMonthPayment > 0) {
                document.getElementById("equalPrincipal_fund_firstMonthPayment").textContent = formatCurrency(result.fund.firstMonthPayment);
                document.getElementById("equalPrincipal_fund_monthlyDecrease").textContent = formatCurrency(result.fund.monthlyDecrease);
                document.getElementById("equalPrincipal_fund_lastMonthPayment").textContent = formatCurrency(result.fund.lastMonthPayment);
                document.getElementById("equalPrincipal_fund_totalPayment").textContent = formatCurrency(result.fund.totalPayment);
                document.getElementById("equalPrincipal_fund_totalInterest").textContent = formatCurrency(result.fund.totalInterest);
                fundResultElement.classList.remove("hidden");
            }else if(loanType != '商业贷款' && result.fund.firstMonthPayment == '0'){
                document.getElementById("equalPrincipal_fund_firstMonthPayment").textContent = '0'
                document.getElementById("equalPrincipal_fund_monthlyDecrease").textContent = '0'
                document.getElementById("equalPrincipal_fund_lastMonthPayment").textContent = '0'
                document.getElementById("equalPrincipal_fund_totalPayment").textContent = '0'
                document.getElementById("equalPrincipal_fund_totalInterest").textContent = '0'
                fundResultElement.classList.remove("hidden");
            } else {
                fundResultElement.classList.add("hidden");
            }
        }

        // 格式化金额显示
        function formatCurrency(amount) {
            return new Intl.NumberFormat("zh-CN", {
                style: "currency",
                currency: "CNY",
            }).format(amount);
        }

        // 初始化
        function init() {
            // 初始化年份选择器
            initYearSelect();
            totalPriceInput.value =  generalCalculatorMoneyshow.value

            // 根据贷款类型显示/隐藏相关字段
            updateFieldsVisibility();

            // 事件监听
            loanTypeSelect.addEventListener("change", updateFieldsVisibility);
            totalPriceInput.addEventListener("input", updateLoanAmounts);
            loanRatioSelect.addEventListener("change", updateLoanAmounts);
            houseTypeSelect.addEventListener("change", houseTypeChangeProportion);
            loanYearsSelect.addEventListener("change", updateLoanRates);

            // 组合贷款金额自动调整
            commercialAmountInput.addEventListener("input", function() {
                updateCombinedLoanAmount(commercialAmountInput);
            });

            fundAmountInput.addEventListener("input", function() {
                updateCombinedLoanAmount(fundAmountInput);
            });

            calculateButton.addEventListener("click", handleCalculate);
            handleCalculate()
        }

        // 页面加载完成后初始化
        document.addEventListener("DOMContentLoaded", init);
    </script>

</div>
</body>
</html>