<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${houseInfo.projectName+'在线选房_沈阳小区名册房源_'+houseInfo.projectName+'现房信息 - 房小二网'}"></title>
    <meta name="keywords" th:content="${houseInfo.projectName+'在线选房,沈阳'+houseInfo.projectName+'现房信息,'+houseInfo.projectName+'房源,'+houseInfo.projectName+'楼盘'}" >
    <meta name="description" th:content="${'沈阳'+houseInfo.projectName+',房小二网为你提供'+houseInfo.projectName+'房源信息，可购买楼盘与户型，特价房与优惠活动信息，方便您选择最经济实惠的'+houseInfo.projectName+'的购房方案。'}" >
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/online.css?t=20180502" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?t=20180502"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/view.css?t=20180921" rel="stylesheet" type="text/css" />
</head>
<body class="w1210">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

    <div class="cl"></div>

    <div th:include="house/detail/fragment_menu::menu" th:with="type=4"></div>

    <div class="w jczl">
        <div class="con_left">
            <div class="title"><p>在线选房</p>
                <span>房型：
                    <a th:each="roomType:${roomType}"  th:text="${roomType.name}" th:href="${roomType.url}" th:class="${roomType.selected}? 'hover':''"></a>
</span></div>
            <div class="list">

                <div class="house" th:each="oli:${onlineinfo}">
                    <a th:href="${'/house/online/'+projectId+'-'+projectType+'-'+oli.roomId + '.htm'}">
                        <i th:if="${#strings.toString(oli.activityState) eq '99'}"></i>
                        <img th:src="${oli.pic}" class='img_l' th:alt="${houseInfo.projectName+oli.title}">
                    </a>
                    <div class="house_r">
                        <h2><a  th:href="${'/house/online/'+projectId+'-'+projectType+'-'+oli.roomId + '.htm'}" th:text="${oli.title}"></a></h2>
                        <ul>
                            <li th:text="${'在售户型：'+oli.roomType+'室'+oli.hallType+'厅'+oli.guardType+'卫'}"></li>
                            <li th:text="${'建筑类型：'+oli.houseType}"></li>
                            <li th:text="${'位置：'+(#strings.isEmpty(oli.RoomLocation)? '暂无资料': oli.RoomLocation)}"></li>
                            <li th:text="${'在售面积：'+#numbers.formatInteger(oli.Area,1)+'m²'}"></li>
                            <li th:text="${'装修类型：'+oli.decoration}"></li>
                            <li th:text="${'朝向：'+oli.toward}"></li>
                        </ul>
                        <div class="cl"></div>

                        <div class="price"><i th:text="${(#strings.toString(oli.price) eq '0' or #strings.isEmpty(oli.price) or #strings.toString(oli.price) eq '0.0') ? '待定':oli.price}"></i>万元/套</div>
                    </div><!--house_r end-->
                    <a class="button" th:href="${'/house/online/'+projectId+'-'+projectType+'-'+oli.roomId + '.htm'}" target="_blank">查看详情</a>
                </div><!--house end-->

            </div><!--list end-->
            <div class="cl"></div>
            <div th:include="fragment/page :: page"></div>
            <div class="cl"></div>
        </div><!--con_left end-->
        <div class="con_right">
            <!--dianping end-->
        </div>
    </div>
    <div class="cl"></div>


    <div class="cl"></div>
    <!--底部2-->
    <div  th:include="fragment/fragment::footer_detail" ></div>
     <!-- End Alexa Certify Javascript -->
    <div class="cl"></div>

    <div th:include="house/detail/fragment_login::login" ></div>
    <div th:include="fragment/fragment::commonFloat"></div>
    <div th:include="fragment/fragment::tongji"></div>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
<div class="modal-backdrop  in" id="loginzhezhao" style="display: none;"></div>
</body>
</html>
