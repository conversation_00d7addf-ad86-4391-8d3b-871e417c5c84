<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head >
    <title th:text="${#strings.toString(album.ProjectName).replace('·','')+'精装房 - 房小二网'}"></title>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="keywords" th:content="${#strings.toString(album.ProjectName).replace('·','')+'精装房.'+#strings.toString(album.ProjectName).replace('·','')+'精装修,'+'沈阳精装房,沈阳新楼盘,沈阳新房'}" />
    <meta name="description" th:content="${'房小二网精装房栏目，为您提供丰富全面的'+#strings.toString(album.ProjectName).replace('·','')+'精装房信息，帮您更准确地了解沈阳精装房房源信息，为您购房提供更大的便利条件，详细关注'+#strings.toString(album.ProjectName).replace('·','')+'精装房项目信息，尽在房小二网沈阳精装房。'}"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/jz_view.css?v=20181228" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="/js/house/decoration.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jingzhuang.js"></script>
    <!--<link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" rel="stylesheet" type="text/css" />-->
    <link href="/css/main2017.css" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
</head>
<body class="w1210">
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/keywords.js"></script>
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

    <div th:include="house/detail/fragment_menu::menu" th:with="type=12"></div>

    </div>

    <div class="w ">
        <div class="content">
            <div class="description">
                <div class="title">
                    <p>装修描述</p>
                </div>
                <div class="description_con">
                    <i th:if="${!#strings.isEmpty(brief.decor) or !#strings.isEmpty(brief.decorStandard)}"><th:block th:if="${!#strings.isEmpty(brief.decor)}">装修风格：<strong th:text="${brief.decor}"></strong></th:block> <th:block th:if="${!#strings.isEmpty(brief.decorStandard)}"> 精装标准：<strong th:text="${brief.decorStandard}"></strong></th:block> </i><br th:if="${!#strings.isEmpty(brief.decor) or !#strings.isEmpty(brief.decorStandard)}"  />
                    <h5 th:if="${!#strings.isEmpty(brief.decorDesc)}" class="description_con_h5">装修描述：<span class="description_con_span" th:text="${brief.decorDesc}"></span></h5>

                </div>
            </div>
            <div class="cl"></div>
            <div class="size">
                <div class="title">
                    <p>样板间展示</p>
                </div>
                <ul class="sizeWidth">
                    <li th:each="layout:${layouts}">
                        <div>
                            <h5  th:text="${layout.decor}"></h5>
                            <p th:text="${layout.roomType+'居室'}"> </p>
                            <p class="measure"><i th:text="${#strings.toString(layout.buildArea).contains('.')? #strings.toString(layout.buildArea).replaceAll('0+?$','').replaceAll('[.]$', '') : jz.buildArea}"></i>㎡</p>
                            <a class="houseMore" href="javascript:void(0)" th:data-id="${layout.layId}" >查看详情</a>
                        </div>
                        <img th:src="${layout.pic}" th:data-id="${layout.layId}" class="houseMore" href="javascript:void(0)"/>
                     <div class="houseMore_box" th:if="${layout.saleState eq '3'}">
                         <img  class="houseMore_sun" src="//static.fangxiaoer.com/web/images/sy/house/saleout.png" />
                     </div>
                    </li>
                </ul>
            </div>
            <div class="picture" th:if="${!#lists.isEmpty(album.list)}">
                <div class="title">
                    <p>精装标准图</p>
                </div>
                <ul class="pictureWidth">
                    <li th:each="list:${album.list}">
                        <img th:src="${list.url}" th:alt="${list.title}" /><p th:text="${list.title}"></p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="buttomNav"></div>
    <div th:include="house/detail/fragment_order::useCode"></div>
    <div th:include="house/detail/fragment_order::guideMessage"></div>
    <div th:include="fragment/fragment :: footer_detail"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="house/detail/fragment_login::login"></div>
    <div class="layout_detail">
        <div class="tanIframe">
        </div>
        <div class="openClose"></div>
        <div class="tanPic">
            <div class="bigPic"></div>
            <div class="bigPicClose"></div>
            <div class="picPre"></div>
            <div class="picNext"></div>
        </div>
        <div class="blackBg"></div>
    </div>
    <div class="layer_box"  th:if="${!#lists.isEmpty(album.list)}">
        <table>
            <tr>
                <td>
                    <div class="layer">
                        <div class="show">
                            <div class="lunbo">
                                <ul>
                                    <li th:each="list:${album.list}">
                                        <table>
                                            <tr>
                                                <td>
                                                    <img th:if="${!#strings.isEmpty(list.url)}" th:src="${#strings.replace(list.url,'middle','big')}" th:alt="${list.title}">
                                                </td>
                                            </tr>
                                        </table>
                                    </li>
                                    <div class="cl"></div>
                                </ul>
                            </div>

                        </div>
                        <div class="close">
                            <img src="https://static.fangxiaoer.com/web/images/sy/house/close.png">
                        </div>
                        <div class="left">
                            <img src="https://static.fangxiaoer.com/web/images/sy/house/left.png">
                            <img src="https://static.fangxiaoer.com/web/images/sy/house/left02.png" class="hover">
                        </div>
                        <div class="right">
                            <img src="https://static.fangxiaoer.com/web/images/sy/house/right.png">
                            <img src="https://static.fangxiaoer.com/web/images/sy/house/right02.png" class="hover">
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>


</body>
</html>
