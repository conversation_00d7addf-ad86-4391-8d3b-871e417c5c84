<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${houseInfo.projectName+'楼盘信息_沈阳'+houseInfo.projectName+'户型信息_'+houseInfo.projectName+'基本信息 - 房小二网'}"></title>
    <meta name="keywords" th:content="${houseInfo.projectName+'楼盘信息,沈阳'+houseInfo.projectName+'户型信息,'+houseInfo.regionName+houseInfo.projectName+'供暖,'+houseInfo.projectName+'物业信息,'+houseInfo.projectName+'公摊'}" >
    <meta name="description" th:content="${'沈阳'+houseInfo.projectName+',房小二网为你提供'+houseInfo.projectName+'最详细楼盘信息，查找'+houseInfo.regionName+houseInfo.projectName+'户型信息，物业公摊，开发供暖等基本信息尽在房小二网!'}">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" rel="stylesheet" type="text/css" />
    <!--<link href="/css/main2017.css" rel="stylesheet" type="text/css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/info.css?v=20180921" rel="stylesheet" type="text/css" />

    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/project_information.js"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/housesParticulars.css?v=20180608"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/project_information.css?v=20220124" rel="stylesheet" type="text/css" />
    <style type="">
        .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
            background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    </style>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

<div class="cl"></div>

<div th:include="house/detail/fragment_menu::menu" th:with="type=2"></div>
<div class="w" style="width: 1170px !important;">
   <div class="housesLeft">
       <div class="Basics_box" style=" padding-bottom: 34px;">
          <div class="title_sun">
              <h1>基本信息</h1>
          </div>
            <div class="content">
             <ul>
                 <div class="top_wei">
                     <li>区域板块：<th:block th:text="${#strings.isEmpty(houseInfo.regionPlate) ? '其他': houseInfo.regionPlate}"></th:block> </li>
                     <li>环线类型：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.schortType)? '暂无资料' : houseInfo.baseInfo.schortType}"></th:block> </li>
                     <li>所属商圈：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.businessCircleName)?  '暂无资料' :houseInfo.baseInfo.businessCircleName}"></th:block> </li>
                     <li>开发商：<th:block th:text="${#strings.isEmpty(houseInfo.companyInfo.developerName) or #strings.toString(houseInfo.companyInfo.developerName) eq '0' ? '暂无资料':#strings.abbreviate(houseInfo.companyInfo.developerName,25)}"></th:block>
                         <div class="content_pop" th:if="${#strings.length(houseInfo.companyInfo.developerName) gt 25}" style="display: none"><th:block th:text="${houseInfo.companyInfo.developerName}"></th:block>
                         <div class="ms_sanjiao dt_sj"></div></div></li>
                     <li>售楼处地址：<th:block th:text="${#strings.isEmpty(houseInfo.projectAdderss) ? '暂无资料':houseInfo.projectAdderss}"></th:block></li>


                     <div class="clearfix"></div>
                 </div>

               <div class="bottom_wei">
                   <li>占地面积：<th:block th:text="${houseInfo.baseInfo.floorArea eq '0 ㎡' ? '暂无资料':houseInfo.baseInfo.floorArea}"></th:block> </li>
                   <li>建筑面积：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.totalBuildingArea) ? '暂无资料':houseInfo.baseInfo.totalBuildingArea+'㎡'}"></th:block> </li>
                   <li>产权年限：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.propertyAge) ? '暂无资料':houseInfo.baseInfo.propertyAge}"></th:block></li>
                   <li>标准层面积： <th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.standardArea) or houseInfo.baseInfo.standardArea eq '0' ? '暂无资料':houseInfo.baseInfo.standardArea+'㎡'}"></th:block></li>
                   <li>租售类型：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.salesType) ? '暂无资料':houseInfo.baseInfo.salesType}"></th:block></li>
                   <li>公摊：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.pooled) ? '暂无资料':houseInfo.baseInfo.pooled}"></th:block></li>
                   <li>总层数：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.totalHeight) ? '暂无资料':(#strings.toString(houseInfo.baseInfo.totalHeight).replaceAll('0+?$','').replaceAll('[.]$','')+'层')}"></th:block></li>
                   <li>层高：<th:block th:text="${#strings.isEmpty(houseInfo.buildingHeight)?'暂无资料':(#strings.indexOf(houseInfo.buildingHeight,'.') eq -1 ? houseInfo.buildingHeight+'米':#strings.toString(houseInfo.buildingHeight).replaceAll('0+?$','').replaceAll('[.]$','')+'米')}"></th:block></li>
                   <li>物业费：<th:block th:text="${houseInfo.buildSet.propertyFee == null or #strings.toString(houseInfo.buildSet.propertyFee) eq '0.0'? '暂无资料':houseInfo.buildSet.propertyFee +'元/平/月'}">/th:block>  </li>
                   <li>交付标准：<th:block th:text="${#strings.isEmpty(houseInfo.baseInfo.isdecorate) ? '暂无资料':houseInfo.baseInfo.isdecorate}"></th:block></li>
                   <li style="width: 100%;">交房时间：<th:block th:text="${#strings.isEmpty(houseInfo.payHouseDate) ? '暂无资料':houseInfo.payHouseDate}"></th:block></li>
                   <li style="width: 100%;">交通：<th:block th:text="${#strings.isEmpty(houseInfo.traffic) ? '暂无资料':houseInfo.traffic}"></th:block></li>
                   <li th:if="${#lists.isEmpty(houseInfo.license)}">预售许可证： 暂无资料</li>
                   <div class="clearfix"></div>
                   <!--<div class="sale">
                       <div class="content">
                           <ul>
                               <li>
                                   <div class="btn">
                                       <h1>展开</h1>
                                   </div>
                               </li>
                           </ul>
                       </div>
                   </div>-->
                   <div class="xk" th:if="${!#lists.isEmpty(houseInfo.license)}" style="float:unset; width: 100%;">
                       预售许可证：
                       <div class="xkm">
                           <table>
                               <thead>
                               <tr>
                                   <td style=" width: 220px">预售许可证</td>
                                   <td style=" width: 220px">发证时间</td>
                                   <td style=" width: 634px">绑定楼栋</td>
                               </tr>
                               </thead>
                               <tbody>
                               <tr th:each="licenseItem:${houseInfo.license}">
                                   <td style="text-align: center" th:text="${#strings.isEmpty(licenseItem.licenseId)?'暂无资料':licenseItem.licenseId}"></td>
                                   <td style="text-align: center" th:text="${#strings.isEmpty(licenseItem.dyTime)?'暂无资料':#strings.toString(licenseItem.dyTime).replace('.','-')}"></td>
                                   <td th:text="${#strings.isEmpty(licenseItem.dyDesc)? '暂无资料' : licenseItem.dyDesc }"></td>
                               </tr>
                               </tbody>
                           </table>
                       </div>
                       <div class="btn xkbt" th:if="${ #lists.size(houseInfo.license) gt 1}">
                           <h1>展开</h1>
                       </div>
                   </div>
               </div>
             </ul>
         </div>
       </div>
       <div class="Mansion" >
            <div class="title_sun">
                <h1>建筑配套</h1>
            </div>
           <div class="content">
               <ul>
                	<li>客梯数量：<th:block th:text="${#strings.isEmpty(houseInfo.buildSet.passengerNum) ? '暂无资料':houseInfo.buildSet.passengerNum}"></th:block></li>
                	<li>电梯配比：<th:block th:text="${#strings.isEmpty(houseInfo.buildSet.RatioLift) ? '暂无资料':houseInfo.buildSet.RatioLift}"></th:block></li>
                	<li>货梯个数：<th:block th:text="${#strings.isEmpty(houseInfo.buildSet.freightNum) ? '暂无资料':houseInfo.buildSet.freightNum}"></th:block></li>
<!--                	<li>附属设施：<th:block th:text="${#strings.isEmpty(houseInfo.buildSet.airConditioningSystem) ? '暂无资料':houseInfo.buildSet.airConditioningSystem}"></th:block></li>-->
                	<li>供水：<th:block th:text="${#strings.isEmpty(houseInfo.buildSet.waterSupply) ? '暂无资料':houseInfo.buildSet.waterSupply}"></th:block></li>
                	<li>供电：<th:block th:text="${#strings.isEmpty(houseInfo.buildSet.powerSupply) ? '暂无资料':houseInfo.buildSet.powerSupply}"></th:block></li>
                	<li>供暖方式：<th:block th:text="${#strings.isEmpty(houseInfo.buildSet.heatingType) ? '暂无资料':houseInfo.buildSet.heatingType}"></th:block></li>
<!--                	<li>建筑结构：<th:block th:text="${#strings.isEmpty(houseInfo.buildSet.bStructure) ? '暂无资料':houseInfo.buildSet.bStructure}"></th:block></li>-->
                   <div class="clearfix"></div>
               </ul>
           </div>

       </div>

      <div class="project">
           <div class="title_sun">
               <h1>项目简介</h1>
           </div>
           <div class="content">
               <p th:text="${#strings.isEmpty(houseInfo.description)?'暂无资料':houseInfo.description}"></p>
           </div>
       </div>
       <div class="Developers" th:unless="${#strings.isEmpty(houseInfo.developerDescription)}">
           <div class="title_sun">
               <h1>开发商简介</h1>
           </div>
          <div class="content">
              <p th:text="${houseInfo.developerDescription}"></p>
          </div>

   </div>

   </div>
    <div th:include="house/detail/fragment_menu::freeCall" ></div>
    <div class="clearfix"></div>
    <div class="newHouseViewChunk" style="margin-top: 30px;border: 0">
        <div th:include="house/detail/fragment_relasion_house::normalHouse_push"></div>
    </div>
</div>
<script>
$(".xkbt").toggle(function(){
    $(".xkm").css('height','100%')
    $('.xkbt h1').text('收起')
    $(".xkbt h1").css('background-image','url("https://static.fangxiaoer.com/web/images/Villa/op_sun01.png")')
},function(){
    $(".xkm").css('height','116px')
    $('.xkbt h1').text('展开')
    $(".xkbt h1").css('background-image','url("https://static.fangxiaoer.com/web/images/Villa/op_sun02.png")')
})
</script>


<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div class="cl"></div>

<div th:include="fragment/fragment:: footer_detail"></div>

<div th:include="fragment/fragment::commonFloat"></div>
<div th:include="fragment/fragment::tongji"></div>
<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::unuseCode"  ></div>
<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode"  ></div>
</body>
</html>