<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="menu">
    <!--右侧浮标-->
    <div class="suspensionIcon">
        <a class="n1" href="https://download.fangxiaoer.com/" target="_blank" rel="31">
            <i></i>
				<span>
        			<img src="https://static.fangxiaoer.com/web/images/download/appErweima142.jpg" />
        			<p>房小二网  APP<br>手机看房更方便</p>
        			<em></em>
				</span>
        </a>
        <a class="n2" href="https://sy.fangxiaoer.com/UHouse/sale/SaleHouse.aspx" target="_blank" rel="100">
            <i></i>
            <span>在线卖房</span>
        </a>
        <a class="n3" href="https://sy.fangxiaoer.com/formulate/house.aspx" target="_blank" rel="100">
            <i></i>
            <span>帮您找房</span>
        </a>
        <a class="n4" href="https://sy.fangxiaoer.com/tool/jsq/shangyedaikuan.aspx" target="_blank" rel="88">
            <i></i>
            <span>计算器</span>
        </a>
        <a class="n5" href="javascript:void(0)" onclick="_MEIQIA('showPanel')" rel="76">
            <i></i>
            <span>客服</span>
        </a>
        <a class="n6">
            <i></i>
            <span>400-893-9709</span>
        </a>
        <a class="n7">
            <i></i>
            <span>顶部</span>
        </a>
    </div>
    <script type="text/javascript">
        $(".suspensionIcon a").hover(function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐显示
                $(this).find("span").stop().fadeIn(200)
            } else {
                $(this).find("span").stop().show(300)
            }
        }, function () {
            if ($(this).hasClass("n1")) {
                //二维码渐隐消失
                $(this).find("span").stop().fadeOut(200)
            } else {
                $(this).find("span").stop().hide(300)
            }
        })
        $(".suspensionIcon .n7").click(function () {
            $("body").animate({ scrollTop: 0 }, 300)
        })
    </script>
</div>
</body>
</html>