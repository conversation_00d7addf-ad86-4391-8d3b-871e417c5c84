


<!DOCTYPE html>

<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head >
    <title th:text="${houseInfo.projectName+'_'+houseInfo.projectName+'楼盘问答_'+houseInfo.projectName+'问答 - 房小二网'}"></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="keywords" th:content="${houseInfo.projectName+','+houseInfo.projectName+'楼盘问答,'+houseInfo.projectName+'问答,'+houseInfo.projectName+houseInfo.sortelTel}" />
    <meta name="description" th:content="${'房小二网为您提供'+houseInfo.projectName+'沈阳最新用户点评，官方咨询热线：'+houseInfo.sortelTel+'，以及快捷的'+houseInfo.projectName+'项目详情咨询服务，专人为您第一时间解答'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/saleInfo/'+projectId+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />
    <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->
    <!--<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynamic.css" rel="stylesheet" type="text/css" />-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?v=20180522"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/view.css?v=20180921" rel="stylesheet"
          type="text/css" />
</head>
<body class="w1210">

<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>


<div class="cl"></div>
<div th:include="house/detail/fragment_menu::menu" th:with="type=16"></div>

    <div class="w" id="xmpj">
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/default.css?v=20181009"/>
        <!--<script src="https://static.fangxiaoer.com/js/bootstrap.min.js" type="text/javascript"> </script>-->
        <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
        <script src="/js/house/CommentList.js"></script>
        <script src="/js/house/LjzPage.js"></script>
        <script th:inline="javascript">
            var Projectid = [[${projectId}]];
            var projectType = [[${projectType}]];
            var MemberID = 0;
            var Phone = "";
            var AddUrl = "#login";
            var UserName = "";
            var IsCommentList = 0;
            $(document).ready(function () {
                $("#CommentListAdd").click(function() {
                    IsCommentList = 1;
                });
                if ($("#LoginUrl")) {
                    $("#LoginUrl").val("/comment/"+Projectid+"/"+projectType);
                }
                var sessionId = $("#glzs").val();
                if(sessionId == null || sessionId == undefined || sessionId == ''){
                    $("#commitAsk").attr("href",AddUrl);
                    $("#CommentListAdd").attr("href",AddUrl);
                }else {
                    $("#CommentListAdd").attr("href","/comment/"+Projectid+"/"+projectType);
                    $("#commitAsk").attr("href","#");
                    $("#commitAsk").click(function () {
                        addAsk();
                    });
                }
                CommentList.BindFenXiang();
                CommentList.SetCount(Projectid);
//                    CommentList.GetList(Projectid,0,'','');
            });
            $(window).load(function () {
                CommentList.GetList(Projectid,0,'','');
            });
        </script>
        <style>
            .ck_pic{width:100%;height:80px;margin-top:25px;margin-bottom: 10px;overflow: hidden;}
            .ck_pic ul li{float:left;margin-right:10px;}
            .ck_pic ul li img{width:100px;height: 75px;}
            .ck_pic span{display: block;float:left;margin-top:52px;}
            .blackBg{background: #000;filter: alpha(opacity=78);opacity: 0.78;position: fixed;width: 100%;height: 100%;z-index: 10000;top: 0;left: 0;display: none;}
            .tanPic{width: 800px;height: 500px;top: 50%;left: 50%;margin-left: -400px;margin-top: -250px;  z-index:10001;position: fixed;text-align: center;display: none;}
            .bigPic{width: 800px;top: 50%;left: 50%;margin-left: -400px;margin-top: -250px;z-index: 2000;position: fixed;text-align: center;}
            .bigPic img {max-width: 800px; *height: 500px;max-height: 500px;}
            .bigPicClose{position: absolute;top: 0px;left: 804px;z-index: 2000;width: 30px;height: 30px;
                cursor: pointer;background: url("https://static.fangxiaoer.com/web/images/ico/sign/fy_close.png");}
            .picPre{width: 46px;height: 74px;background: url("https://static.fangxiaoer.com/web/images/ico/sign/white_ico.png") 1px 0px;position: absolute;top: 210px; left: -80px;z-index: 2000;cursor: pointer;}
            .picNext{background: url("https://static.fangxiaoer.com/web/images/ico/sign/white_ico.png") -155px 0px;width: 46px;height: 74px;position: absolute;top: 210px;left: 824px;z-index: 2000;cursor: pointer;}
        </style>
        <input type="hidden" id="IsYezhu" value="0" class="IsAll"/>
        <input type="hidden" id="IsPic" value="0" class="IsAll"/>
        <input type="hidden" id="IsRatio" value="0" class="filter"/>
        <input type="hidden" id="IsSupporting" value="0" class="filter"/>
        <input type="hidden" id="IsSpace" value="0" class="filter"/>
        <input type="hidden" id="IsProperty" value="0" class="filter"/>
        <input type="hidden" id="IsHuxing" value="0" class="filter"/>
        <input type="hidden" id="IsFlaws" value="0" class="filter"/>
        <input type="hidden" id="IsVirtues" value="0" class="filter"/>
        <!--<div class="w remark">-->
            <!--<div class="remark_classify">-->
                <!--<span class="remark_classify_title">可选分类：</span>-->
                <!--<div class="filterUl">-->
                    <!--<ul>-->
                        <!--<li class="unlimited" id="filterAll">不限</li>-->
                        <!--<li>-->
                            <!--<input type="checkbox" id="checkbox_a1" class="chk_1" data-id="IsVirtues"/>-->
                            <!--<label for="checkbox_a1"></label>-->
                            <!--<label for="checkbox_a1">优点</label>-->
                        <!--</li>-->
                        <!--<li>-->
                            <!--<input type="checkbox" id="checkbox_a3" class="chk_1" data-id="IsFlaws"/>-->
                            <!--<label for="checkbox_a3"></label>-->
                            <!--<label for="checkbox_a3">缺点</label>-->
                        <!--</li>-->
                        <!--<li>-->
                            <!--<input type="checkbox" id="checkbox_a4" class="chk_1" data-id="IsHuxing"/>-->
                            <!--<label for="checkbox_a4"></label>-->
                            <!--<label for="checkbox_a4">户型</label>-->
                        <!--</li>-->
                        <!--<li>-->
                            <!--<input type="checkbox" id="checkbox_a5" class="chk_1" data-id="IsProperty"/>-->
                            <!--<label for="checkbox_a5"></label>-->
                            <!--<label for="checkbox_a5">物业</label>-->
                        <!--</li>-->
                        <!--<li>-->
                            <!--<input type="checkbox" id="checkbox_a6" class="chk_1" data-id="IsSpace"/>-->
                            <!--<label for="checkbox_a6"></label>-->
                            <!--<label for="checkbox_a6">车位</label>-->
                        <!--</li>-->
                        <!--<li>-->
                            <!--<input type="checkbox" id="checkbox_a7" class="chk_1" data-id="IsSupporting"/>-->
                            <!--<label for="checkbox_a7"></label>-->
                            <!--<label for="checkbox_a7">配套</label>-->
                        <!--</li>-->
                        <!--<li>-->
                            <!--<input type="checkbox" id="checkbox_a8" class="chk_1" data-id="IsRatio"/>-->
                            <!--<label for="checkbox_a8"></label>-->
                            <!--<label for="checkbox_a8">性价比</label>-->
                        <!--</li>-->
                    <!--</ul>-->
                <!--</div>-->

                <!--<div class="remark_btn">-->
                    <!--<a class="" target="_blank" style="cursor: pointer" data-toggle="modal" id="CommentListAdd">我要点评</a>-->


                <!--</div>-->
            <!--</div>-->
            <!--<div class="remark_cont">-->
                <!--<div class="remark_nav">-->
                    <!--<ul>-->
                        <!--<li id="dianping1" class="hover"></li>-->
                        <!--<li id="dianping2" data-id="IsYezhu" data-val="1"></li>-->
                        <!--<li id="dianping3" data-id="IsYezhu" data-val="2"></li>-->
                        <!--<li id="dianping4" data-id="IsPic" data-val="1"></li>-->
                    <!--</ul>-->
                <!--</div>-->
                <!--<div class="remark_all" id="dianpingDom">-->
                    <!--&lt;!&ndash;全部评论&ndash;&gt;-->
                    <!--<div id="con_dianping_1">-->
                        <!--<div id="listDom"></div>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="page">-->
                    <!--<div id="LjzPage"></div>-->
                <!--</div>-->
            <!--</div>-->
        <!--</div>-->
        <!--<div class="blackBg"></div>-->
        <!--<div class="tanPic">-->
            <!--<div class="bigPic"></div>-->
            <!--<div class="bigPicClose"></div>-->
            <!--<div class="picPre"></div>-->
            <!--<div class="picNext"></div>-->
        <!--</div>-->
        <script>
            if ((navigator.userAgent.indexOf('MSIE') >= 0) && (navigator.userAgent.indexOf('Opera') < 0)) {
                $(".remark_classify label:even").hide();
                $(".remark_classify input").show()
            }
            $(".replay_btn").live("click",function () {
                if ($(this).parent().find('.remark_reply').css("display") == "none") {
                    $('.remark_reply').slideUp();
                    $(this).parent().find('.remark_reply').slideDown();
                } else {
                    $(this).parent().find('.remark_reply').slideUp();
                }
            })
            $(".remark_classify label").click(function () {
                $(".unlimited").css({ "border-color": "#fff", "color": "#666" });
            })
            $(".remark_classify input").click(function() {
                if ($(this).is(':checked')) {
                    $("#" + $(this).attr("data-id")).val(1);
                } else {
                    $("#" + $(this).attr("data-id")).val(0);
                }
                CommentList.SetCount(Projectid);
                CommentList.GetList(Projectid,MemberID,Phone,UserName);
            });
            $(".unlimited").click(function() {
                $(".remark_classify input").removeAttr("checked");
                $(this).css({ "border-color": "#ff5200", "color": "#ff5200" });
            });
        </script>
        <script>
            $("#filterAll").click(function() {
                $(".filter").val("0");
                CommentList.SetCount(Projectid);
                CommentList.GetList(Projectid,MemberID,Phone,UserName);
            });
            $(".filter").click(function () {
                $("#filterAll").val("");
                CommentList.SetCount(Projectid);
                CommentList.GetList(Projectid,MemberID,Phone,UserName);
            });
            $(".remark_nav ul li").click(function() {
                $(".remark_nav ul li").removeClass("hover");
                $(this).addClass("hover");
                $(".IsAll").val(0);
                $("#" + $(this).attr("data-id")).val($(this).attr("data-val"));
                CommentList.GetList(Projectid,MemberID,Phone,UserName);
            });
        </script>
        <!--<div class="w title" id="xmzx">-->
            <!--<p>楼盘问答</p>-->
        <!--</div>-->
        <div id="comment" class="w">
            <div id="con_cm_1" style="display: none">
                <div class="cl">
                </div>
                <div id="comments-list" class="m" clstag="shangpin|keycount|product|comment">
                    <div class="mt">
                        <div class="mt-inner m-tab-trigger-wrap clearfix">
                            <ul class="m-tab-trigger">
                                <li class="ui-switchable-item trig-item curr"><a href="javascript:;">全部点评<em> (0)</em></a></li>
                                <li class="ui-switchable-item trig-item"><a href="javascript:;">好评<em>(0)</em></a></li>
                                <li class="ui-switchable-item trig-item"><a href="javascript:;">中评<em>(0)</em></a></li>
                                <li class="ui-switchable-item trig-item"><a href="javascript:;">差评<em>(0)</em></a></li>
                            </ul>
                        </div>
                    </div>
                    <div id="comment-0" class="mc ui-switchable-panel comments-table ui-switchable-panel-selected"
                         style="display: block;">
                        <div class="com-table-header">
                            <span class="item column1">点评心得</span> <span class="item column2">购房满意度</span> <span
                                class="item column5">点评者</span>
                        </div>
                        <div class="com-table-main">

                            <div class="cl">
                            </div>
                        </div>
                        <div class="page hid">
                            1 2 3 4 5 6 7 8 9
                        </div>
                    </div>
                </div>
            </div>

            <!--con_cm_1-->
            <script src="/js/house/AskList.js"></script>
            <script src="/js/house/AskLjzPage.js"></script>
            <script type="text/javascript" th:inline="javascript">
                var Projectid = [[${projectId}]];
                $(document).ready(function () {
                    //CommentList.BindFenXiang();
                    AskList.SetCount(Projectid);
                    AskList.GetList(Projectid,0,'','');
                });
            </script>
            <input type="hidden" id="askType" value="0" class="askType"/>
            <div id="con_cm_2">
                <div class="cm_2_tit">
                    <ul>
                        <li id="zx1" data-id="askType" class="hover"></li>
                        <li id='zx2' data-id="askType" data-val="1"></li>
                        <li id='zx3' data-id="askType" data-val="2" ></li>
                        <li id='zx4' data-id="askType" data-val="3" ></li>
                        <li id='zx5' data-id="askType" data-val="4" ></li>
                    </ul>
                </div>
                <script type="text/javascript">
                    $(".cm_2_tit ul li").click(function(){
                        $(".cm_2_tit ul li").removeClass("hover");
                        $(this).addClass("hover");
                        $("#askType").val(0);
                        $("#" + $(this).attr("data-id")).val($(this).attr("data-val"));
                        AskList.GetList(Projectid,MemberID,Phone,UserName);
                    })
                </script>

                <div id="con_zx_1" class="zx">
                    <div class="zx_txt">
                        <a href="#zxun"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/zixun.gif" /></a>
<!--
                        <p>提示：因每位咨询者购房情况、提问时间等不同或遇开发商调价等行为，以下回复仅对提问者3天内有效，其它网友仅供参考！由此给您带来的不便请多多谅解，谢谢！</p>
-->
                    </div>
                    <div id="AskList_UpdatePanel2">

                    </div>
                    <div class="page" style="margin-bottom: 30px;">
                        <div id="AskList_Pager1"></div>
                    </div>
                </div>
                <div id="zxun" class="question">
                    <div class="question_l">
                        <div class="question_tit" th:text="'提问：' + ${houseInfo.projectName}"></div>
                        <div class="login ">
                            登录后才可以发表咨询，<a href="https://my.fangxiaoer.com" target="_blank">立即登录</a>
                        </div>
                        <div class="ask_type">
                            <ul>
                                <!--<li><b>提问类型：</b>

                                    <label for="ask1">
                                        <input name="ask" class="validate[rquired] radio" type="radio" id="ask1"
                                               value='1' checked='checked' />房源提问</label>

                                    <label for="ask2">
                                        <input name="ask" class="validate[rquired] radio" type="radio" id="ask2"
                                               value='2'  />促销活动</label>

                                    <label for="ask3">
                                        <input name="ask" class="validate[rquired] radio" type="radio" id="ask3"
                                               value='3'  />支付及退款</label>

                                    <label for="ask4">
                                        <input name="ask" class="validate[rquired] radio" type="radio" id="ask4"
                                               value='4'  />房仔服务</label>
-->
                                <li><b>提问内容</b><textarea name="AskList$txt" rows="2" cols="20" id="AskList_txt" onkeyup="words_deal();"></textarea>
                                </li>
                                <li><b>&nbsp;</b>
                                    <div style="float: right; margin-right: 50px">
                                        剩余<span id="textCount">300</span>个字
                                    </div>
                                    <a class=""data-toggle="modal"  id="commitAsk">提交问题</a>
                                    <!--<a class=""  data-toggle="modal" href="#login">提交问题</a>-->
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="question_r">
                        <b>问答说明：</b><p>
                        1、楼盘问答是房小二网为您提供的疑难问题解答板块，在这里你可以与工作人员直接交流。<br>
                        2、因提问的用户较多，可能无法及时进行回复，我们将尽力在最快的时间内为您解答。<br>
                        3、当涉及广告、不实评论、恶意评论、爆粗口、危害国家安全等不当言论时，房小二网有权实施封号等管理行为。
                    </p>
                    </div>
                    <div class="cl">
                    </div>
                </div>
            </div>
            <!--con_cm_2 end-->
        </div>

        <script type="text/javascript">
            function words_deal() {
                var curLength = $("#AskList_txt").val().length;
                if (curLength > 300) {
                    var num = $("#AskList_txt").val().substr(0, 300);
                    $("#AskList_txt").val(num);
                    alert("超过字数限制，多出的字将被截断！");
                }
                else {
                    $("#textCount").text(300 - $("#AskList_txt").val().length);
                }
            }
        </script>


    </div>
    <script th:inline="javascript">
        function words_deal() {
            var curLength = $("#AskList_txt").val().length;
            if (curLength > 300) {
                var num = $("#AskList_txt").val().substr(0, 300);
                $("#AskList_txt").val(num);
                alert("超过字数限制，多出的字将被截断！");
            }
            else {
                $("#textCount").text(300 - $("#AskList_txt").val().length);
            }
        }
        function addAsk() {
            var sessionId = $("#glzs").val();
            var projectType_Ask = [[${projectType}]];
            var projectName = [[${houseInfo.projectName}]];
            var askType = 1;
            var content = $("#AskList_txt").val();
            if(projectType_Ask  == null || projectType_Ask == undefined || projectType_Ask == ''){
                projectType_Ask = "1";
            }
            if (!sessionId) {
                alert("请先登录");
            }else if(!askType) {
                alert("请选择咨询类型");
            }else if(!content){
                alert("请填写提问内容");
            }else {
                var params = {
                    sessionId: sessionId,
                    projectId: Projectid,
                    askType: askType,
                    projectName: projectName,
                    content: content,
                    projectType: projectType_Ask
                };
                $.ajax({
                    type: "POST",
                    data: JSON.stringify(params),
                    url: "/addAsk",
                    dataType: "json",
                    headers: {
                        'Content-Type': 'application/json;charset=utf-8'
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            alert("咨询成功提交，等待审核");
                            window.location.reload();
                        } else {
                            alert(data.msg);
                        }
                    }
                });
            }
        }
    </script>


<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
<div th:include="house/detail/fragment_login::login"></div>
</body>
</html>
