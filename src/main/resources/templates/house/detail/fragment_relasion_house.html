<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="normalHouse_push">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/zhoubian.css?v=20181009"/>
    <div id="relantionShipHouse">
        <div class="loupan">
            <div class="btn_box">
                <ul>
                    <li>
                        <h1>周边楼盘</h1>
                    </li>
                    <li>
                        <h1 th:if="${houseInfo?.projectStatus ne '3'}">同价位楼盘</h1>
                    </li>
                    <div class="cl"></div>
                </ul>
            </div>
          <div class="content_wei">
              <ul id="aroundHouse">
              </ul>
              <ul id="samePrice">

              </ul>
          </div>
        </div>

       <div class="ershoufang">
           <div class="title_sun">
               <h1>周边二手房</h1>
           </div>
           <ul id="aroundSaleHouse">
           </ul>
       </div>
    </div>
    <script th:inline="javascript">
        $(document).ready(function () {
            var pId = [[${projectId}]];
            var lng = [[${houseInfo.longitude}]];
            var lat = [[${houseInfo.latitude}]];

            if(pId != undefined && lng != undefined && lat != undefined && pId != '787') {
                $.ajax({
                    data: {projectId: pId, latitude: lat, longitude: lng},
                    type: 'post',
                    dataType: 'json',
                    url:'/house/relasionHousePush',
                    async: true,
                    success: function (data) {
                        if (data.status == 1) {
                            data = data.content;
                            var byDistance = data.byDistance;
                            var byPrice = data.byPrice;
                            var saleHouse = data.houseByDistance;
                            //同价位
                            if(byPrice != ''&& byPrice.length >0){
                                var htmlElements = '';
                                for(var i=0;i<byPrice.length;i++){
                                    var imgElement = byPrice[i].pic;
                                    if(imgElement != null && imgElement != ''&& imgElement != undefined){
                                        imgElement = '<img src="'+byPrice[i].pic+'">'
                                    }
                                    htmlElements = htmlElements +'<a href="/house/'+byPrice[i].projectId+'-'+byPrice[i].projectType+'.htm" target="_blank"><li><div class="photo">'+imgElement+'</div>';
                                    /*if(byPrice[i].pan != null && byPrice[i].videoTab != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play"><img src="/img/panoImg.png"  style="width: 32px;height: auto;position: absolute;top: 113px;left: 7px;"class="play1">'
                                    }
                                    if(byPrice[i].pan == null && byPrice[i].videoTab != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play">'
                                    }
                                    if(byPrice[i].pan != null && byPrice[i].videoTab == 0){
                                        htmlElements = htmlElements +'<img src="/img/panoImg.png" style="width: 62px;height: auto;position: absolute;top: 43px;left: 39%;" class="play1">'
                                    }*/
                                    if (byPrice[i].pan != null) {
                                        htmlElements = htmlElements +'<div class="vru"></div>'
                                    }

                                    var projectName = byPrice[i].projectName;
                                    if(projectName.length >9){
                                        projectName = projectName.substring(0,9)+'...'
                                    }
                                    htmlElements = htmlElements+'<div class="text"><span class="name">'+projectName+'</span>';
                                    if(byPrice[i].projectType == 2){
                                        htmlElements = htmlElements +'<span class="type">别墅</span>'
                                    }else if(byPrice[i].projectType == 3){
                                        htmlElements = htmlElements +'<span class="type">写字楼</span>'
                                    }
                                    var pPrice =byPrice[i].projectPrice;
                                    if(pPrice !=''&& pPrice != null&& pPrice!=undefined && pPrice!='待定'){
                                        pPrice = pPrice +'<label>元/㎡</label>'
                                    }else {
                                        pPrice = '<div class="font_wei">待定</div>';
                                    }
                                    htmlElements = htmlElements+'<div class="cl"></div><span class="Price">'+pPrice+'</span></div></li></a>';
                                }
                                $("#samePrice").append(htmlElements);
                                $("#samePrice").append("<div class='cl'></div>");
                            }else {
                                $("#relantionShipHouse .loupan .btn_box ul li:eq(1)").hide()
                                $("#relantionShipHouse .loupan .btn_box ul li").css("cursor","auto")
                                $("#relantionShipHouse .loupan .btn_box ul li").css("pointer-events","none")
                                $(".content_wei ul:eq(1)").hide()
                                $(".content_wei ul:eq(0)").show()
                                $("#samePrice").hide();
                            }
                            //附近楼盘
                            if(byDistance != ''&& byDistance.length >0){
                                var htmlElements = '';
                                for( var i= 0;i< byDistance.length;i++){
                                    var imgElement = byDistance[i].pic;
                                    if(imgElement != null && imgElement != ''&& imgElement != undefined){
                                        imgElement = '<img src="'+byDistance[i].pic+'">'
                                    }
                                    htmlElements =  htmlElements +'<a href="/house/'+byDistance[i].projectId+'-'+byDistance[i].projectType+'.htm" target="_blank"><li><div class="photo">'+imgElement+'</div>';
                                    /*if(byDistance[i].pan != null && byDistance[i].videoTab != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play"><img src="/img/panoImg.png"  style="width: 32px;height: auto;position: absolute;top: 113px;left: 7px;"class="play1">'
                                    }
                                    if(byDistance[i].pan == null && byDistance[i].videoTab != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play">'
                                    }
                                    if(byDistance[i].pan != null && byDistance[i].videoTab == 0){
                                        htmlElements = htmlElements +'<img src="/img/panoImg.png" style="width: 62px;height: auto;position: absolute;top: 43px;left: 39%;" class="play1">'
                                    }*/
                                    if (byDistance[i].pan != null) {
                                        htmlElements = htmlElements +'<div class="vru"></div>'
                                    }

                                    var projectName = byDistance[i].projectName;
                                    if(projectName.length >9){
                                        projectName = projectName.substring(0,9)+'...'
                                    }
                                    htmlElements = htmlElements+'<div class="text"><span class="name">'+projectName+'</span>';
                                    if(byDistance[i].projectType == 2){
                                        htmlElements = htmlElements +'<span class="type">别墅</span>'
                                    }else if(byDistance[i].projectType == 3){
                                        htmlElements = htmlElements +'<span class="type">写字楼</span>'
                                    }
                                    var pPrice =byDistance[i].price;

                                    if(pPrice !=''&& pPrice != null&& pPrice!=undefined && pPrice!='待定'){
                                        pPrice = pPrice.replace('元/㎡','') +'<label>元/㎡</label>'
                                    }else {
                                        pPrice = '<div class="font_wei">待定</div>';
                                    }
                                    htmlElements = htmlElements+'<div class="cl"></div><span class="Price">'+pPrice+'</span></div></li></a>';
                                }
                                $("#aroundHouse").append(htmlElements);
                                $("#aroundHouse").append("<div class='cl'></div>");
                            }
                            //附近二手房
                            if(saleHouse != ''&& saleHouse.length >0){
                                var htmlElements = '';
                                for(var i=0;i<saleHouse.length;i++){
                                    var imgElement = saleHouse[i].pic;
                                    if(imgElement != null && imgElement != ''&& imgElement != undefined){
                                        imgElement = '<img src="'+saleHouse[i].pic+'">'
                                    }
                                    htmlElements = htmlElements +'<a href="/salehouse/'+saleHouse[i].houseId+'.htm" target="_blank"><li><div class="photo">'+imgElement+'</div>';
                                    if(saleHouse[i].isVideo != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play">'
                                    }
                                    // htmlElements = htmlElements+'<div class="vru"></div>'

                                    var sPrice = saleHouse[i].price;
                                    if(sPrice == '0.0' || sPrice == '0'||sPrice == ''||sPrice==null||sPrice==undefined ||sPrice=='面议'){
                                        sPrice = '<div class="font_wei">面议</div>';
                                    }else {
                                        sPrice = sPrice + '<label>万</label>';
                                    }
                                    var projectName = saleHouse[i].subName;
                                    if(projectName.length >9){
                                        projectName = projectName.substring(0,9)+'...'
                                    }
                                    htmlElements = htmlElements+'<div class="text"><span class="name">'+projectName+'</span><span class="Price">'+sPrice+'</span><div class="cl"></div>';
                                    htmlElements = htmlElements+'<span class="area">'+saleHouse[i].room+'室'+saleHouse[i].hall+'厅'+saleHouse[i].toilet+'卫 / '+saleHouse[i].area+'㎡</span></div></li></a>';
                                }
                                $("#aroundSaleHouse").append(htmlElements);
                                $("#aroundSaleHouse").append("<div class='cl'></div>");
                            }
                        } else {
                            $("#relantionShipHouse").hide();
                            $("#relantionShipHouse .loupan .btn_box ul li:eq(0)").hide()
                            $("#relantionShipHouse .loupan .btn_box ul li").css("cursor","auto")
                            $("#relantionShipHouse .loupan .btn_box ul li").css("pointer-events","none")
                            $(".content_wei ul:eq(0)").hide()
                            $(".content_wei ul:eq(1)").show()
                        }
                    },
                    error: function () {
                        $("#relantionShipHouse").hide();
                    }
                })
            }else {
                $("#relantionShipHouse").hide();
            }
        });




        $(".loupan .content_wei ul").hide()
        $(".loupan .content_wei ul:eq(0)").show()
        $("#relantionShipHouse .loupan .btn_box ul li:eq(0)").addClass("color")
       $("#relantionShipHouse .loupan .btn_box ul li").click(
           function () {
               var x= $("#relantionShipHouse .loupan .btn_box ul li").index(this)
               $(".loupan .content_wei ul").hide()
               $(".loupan .content_wei ul").eq(x).show()
               $("#relantionShipHouse .loupan .btn_box ul li").removeClass("color")
               $(this).addClass("color")
           }
       )


    </script>
</div>
<div th:fragment="villa_periphery">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/zhoubian.css?v=20181009"/>
    <style>
        .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
            background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    </style>
    <div class="loupan_box">
        <div class="content_box">
            <div class="btn_wei">
                <ul>
                    <li>
                        <h1>周边楼盘</h1>
                    </li>
                    <li th:if="${houseInfo?.projectStatus ne '3'}">
                        <h1>同价位楼盘</h1>
                    </li>
                    <div class="cl"></div>
                </ul>
            </div>
            <div class="content">
                <ul id="aroundHouse">

                </ul>
                <ul id="samePrice">

                </ul>
            </div>
        </div>
    </div>
    <div class="ershouf_box">
        <div class="content_box">
            <div class="btn_wei">
                <ul>
                    <li>
                        <h1>周边二手房</h1>
                    </li>

                    <div class="cl"></div>
                </ul>
            </div>
            <div class="content">
                <ul id="aroundSaleHouse">

                </ul>

            </div>
        </div>
    </div>
    <script th:inline="javascript">
        var btn_x=$(".loupan_box .content_box .btn_wei ul li").length
        if(btn_x<2){
            $(".loupan_box .content_box ul li").css("float","none")
            $(".loupan_box .content_box ul li").css("margin-right","0")
        }
        $(".loupan_box .content_box .content ul ").hide()
        $(".loupan_box .content_box .content ul:eq(0) ").show()
        $(".loupan_box .content_box .btn_wei ul li:eq(0)").addClass("color")
        $(".loupan_box .content_box .btn_wei ul li").click(
            function () {
                $(".loupan_box .content_box .btn_wei ul li").removeClass("color")
                $(this).addClass("color")
                var x=$(".loupan_box .content_box .btn_wei ul li").index(this)
                $(".loupan_box .content_box .content ul ").hide()
                $(".loupan_box .content_box .content ul ").eq(x).show()

            }
        )

        $(document).ready(function () {
            var pId = [[${projectId}]];
            var lng = [[${houseInfo.longitude}]];
            var lat = [[${houseInfo.latitude}]];

            if(pId != undefined && lng != undefined && lat != undefined && pId != '818') {
                $.ajax({
                    data: {projectId: pId, latitude: lat, longitude: lng},
                    type: 'post',
                    dataType: 'json',
                    url:'/house/relasionHousePush',
                    async: true,
                    success: function (data) {
                        if (data.status == 1) {
                            data = data.content;
                            var byDistance = data.byDistance;
                            var byPrice = data.byPrice;
                            var saleHouse = data.houseByDistance;
                            //同价位
                            if(byPrice != ''&& byPrice.length >0){
                                var htmlElements = '';
                                var htmlElements1 = '';
                                for(var i=0;i<byPrice.length;i++){
                                    var imgElement = byPrice[i].pic;
                                    if(imgElement != null && imgElement != ''&& imgElement != undefined){
                                        imgElement = '<img src="'+byPrice[i].pic+'">'
                                    }
                                    htmlElements = htmlElements +'<a href="/house/'+byPrice[i].projectId+'-'+byPrice[i].projectType+'.htm" target="_blank"><li><div class="photo">'+imgElement+'</div>';
                                    /*if(byPrice[i].pan != null && byPrice[i].videoTab != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play"><img src="/img/panoImg.png"  style="width: 32px;height: auto;position: absolute;top: 113px;left: 7px;"class="play1">'
                                    }
                                    if(byPrice[i].pan == null && byPrice[i].videoTab != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play">'
                                    }
                                    if(byPrice[i].pan != null && byPrice[i].videoTab == 0){
                                        htmlElements = htmlElements +'<img src="/img/panoImg.png" style="width: 62px;height: auto;position: absolute;top: 43px;left: 39%;" class="play1">'
                                    }*/
                                    if(byPrice[i].pan) {
                                        htmlElements = htmlElements +'<div class="vru"></div>'
                                    }
                                    var projectName = byPrice[i].projectName;
                                    if(projectName.length >9){
                                        projectName = projectName.substring(0,9)+'...'
                                    }
                                    htmlElements = htmlElements+'<div class="text_wei"><span class="name">'+projectName+'</span>';
                                    if(byPrice[i].projectType == 2){
                                        htmlElements = htmlElements +'<span class="type">别墅</span>'
                                    }else if(byPrice[i].projectType == 3){
                                        htmlElements = htmlElements +'<span class="type">写字楼</span>'
                                    }
                                    var pPrice =byPrice[i].projectPrice;
                                    if(pPrice !=''&& pPrice != null&& pPrice!=undefined && pPrice!='待定'){
                                        pPrice = pPrice +'<label>元/㎡</label>'
                                    }else {
                                        pPrice = '<div class="font_wei">待定</div>';
                                    }
                                    htmlElements = htmlElements+'<div class="cl"></div><span class="Price">'+pPrice+'</span></div></li></a>';
                                }
                                $("#samePrice").append(htmlElements,htmlElements1);
                                $("#samePrice").append("<div class='cl'></div>");
                            }else {
                                $(".loupan_box .content_box .btn_wei ul li:eq(0)").hide()
                                $(".loupan_box .content_box .btn_wei ul li").css("float","none")
                                $(".loupan_box .content_box .btn_wei ul li").css("pointer-events","none")
                                $(".loupan_box .content_box .content ul:eq(0)").hide()
                                $(".loupan_box .content_box .content ul:eq(1)").show()
                                $("#samePrice").hide();
                            }
                            //周边楼盘
                            if(byDistance != ''&& byDistance.length >0){
                                var htmlElements = '';
                                for( var i= 0;i< byDistance.length;i++){
                                    var imgElement = byDistance[i].pic;
                                    if(imgElement != null && imgElement != ''&& imgElement != undefined){
                                        imgElement = '<img src="'+byDistance[i].pic+'">'
                                    }
                                    htmlElements =  htmlElements +'<a href="/house/'+byDistance[i].projectId+'-'+byDistance[i].projectType+'.htm" target="_blank"><li><div class="photo">'+imgElement+'</div>';
                                    /*if(byDistance[i].pan != null && byDistance[i].videoTab != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play"><img src="/img/panoImg.png"  style="width: 32px;height: auto;position: absolute;top: 113px;left: 7px;"class="play1">'
                                    }
                                    if(byDistance[i].pan == null && byDistance[i].videoTab != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play">'
                                    }
                                    if(byDistance[i].pan != null && byDistance[i].videoTab == 0){
                                        htmlElements = htmlElements +'<img src="/img/panoImg.png" style="width: 62px;height: auto;position: absolute;top: 43px;left: 39%;" class="play1">'
                                    }*/
                                    if(byDistance[i].pan) {
                                        htmlElements = htmlElements +'<div class="vru"></div>'
                                    }
                                    var projectName = byDistance[i].projectName;
                                    if(projectName.length >9){
                                        projectName = projectName.substring(0,9)+'...'
                                    }
                                    htmlElements = htmlElements+'<div class="text_wei"><span class="name">'+projectName+'</span>';
                                    if(byDistance[i].projectType == 2){
                                        htmlElements = htmlElements +'<span class="type">别墅</span>'
                                    }else if(byDistance[i].projectType == 3){
                                        htmlElements = htmlElements +'<span class="type">写字楼</span>'
                                    }
                                    var pPrice =byDistance[i].price;

                                    if(pPrice !=''&& pPrice != null&& pPrice!=undefined && pPrice!='待定'){
                                        pPrice = pPrice.replace('元/㎡','') +'<label>元/㎡</label>'
                                    }else {
                                        pPrice = '<div class="font_wei">待定</div>';
                                    }
                                    htmlElements = htmlElements+'<div class="cl"></div><span class="Price">'+pPrice+'</span></div></li></a>';
                                }
                                $("#aroundHouse").append(htmlElements);
                                $("#aroundHouse").append("<div class='cl'></div>");
                            }
                            //周边二手房
                            if(saleHouse != ''&& saleHouse.length >0){
                                var htmlElements = '';
                                for(var i=0;i<saleHouse.length;i++){
                                    var imgElement = saleHouse[i].pic;
                                    if(imgElement != null && imgElement != ''&& imgElement != undefined){
                                        imgElement = '<img src="'+saleHouse[i].pic+'">'
                                    }
                                    htmlElements = htmlElements +'<a href="/salehouse/'+saleHouse[i].houseId+'.htm" target="_blank"><li><div class="photo">'+imgElement+'</div>';
                                    if(saleHouse[i].pan != null && saleHouse[i].isVideo != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play"><img src="/img/panoImg.png"  style="width: 32px;height: auto;position: absolute;top: 113px;left: 7px;"class="play1">'
                                    }
                                    if(saleHouse[i].pan == null && saleHouse[i].isVideo != 0){
                                        htmlElements = htmlElements +'<img src="https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png" class="play">'
                                    }
                                    if(saleHouse[i].pan != null && saleHouse[i].isVideo == 0){
                                        htmlElements = htmlElements +'<img src="/img/panoImg.png" style="width: 62px;height: auto;position: absolute;top: 43px;left: 39%;" class="play1">'
                                    }
                                    // htmlElements = htmlElements+'<div class="vru"></div>'
                                    var sPrice = saleHouse[i].price;
                                    if(sPrice == '0.0' || sPrice == '0'||sPrice == ''||sPrice==null||sPrice==undefined ||sPrice=='面议'){
                                        sPrice = '<div class="font_wei">面议</div>';
                                    }else {
                                        sPrice = sPrice + '<label>万</label>';
                                    }
                                    var projectName = saleHouse[i].subName;
                                    if(projectName.length >9){
                                        projectName = projectName.substring(0,9)+'...'
                                    }
                                    htmlElements = htmlElements+'<div class="text_wei"><span class="name">'+projectName+'</span><span class="Price">'+sPrice+'</span><div class="cl"></div>';
                                    htmlElements = htmlElements+'<span class="area">'+saleHouse[i].room+'室'+saleHouse[i].hall+'厅'+saleHouse[i].toilet+'卫 / '+saleHouse[i].area+'㎡</span></div></li></a>';
                                }
                                $("#aroundSaleHouse").append(htmlElements);
                                $("#aroundSaleHouse").append("<div class='cl'></div>");
                            }
                        } else {

                            $(".ershouf_box .content_box .btn_wei ul li:eq(0)").hide()
                            $(".ershouf_box .content_box .btn_wei ul li").css("float","auto")
                            $(".ershouf_box .content_box .btn_wei ul li").css("cursor","auto")

                            $(".ershouf_box .content_box .content ul:eq(1)").hide()
                            $(".ershouf_box .content_box .content ul:eq(0)").show()
                        }
                    },
                    error: function () {
                        $(".loupan_box").hide();
                        $(".ershouf_box").hide();
                    }
                })
            }else {
                $(".loupan_box").hide();
                $(".ershouf_box").hide();
            }
        });
    </script>
</div>

</body>
</html>