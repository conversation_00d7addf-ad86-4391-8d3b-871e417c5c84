<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${houseInfo.projectName+'_'+houseInfo.projectName+'楼盘问答_'+houseInfo.projectName+'问答 - 房小二网'}"></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="keywords"
          th:content="${houseInfo.projectName+','+houseInfo.projectName+'楼盘问答,'+houseInfo.projectName+'问答,'+houseInfo.projectName+houseInfo.sortelTel}"/>
    <meta name="description"
          th:content="${'房小二网为您提供'+houseInfo.projectName+'沈阳最新用户点评，官方咨询热线：'+houseInfo.sortelTel+'，以及快捷的'+houseInfo.projectName+'项目详情咨询服务，专人为您第一时间解答'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/saleInfo/'+projectId+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112"/>
    <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css"/>-->
    <!--<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynamic.css" rel="stylesheet" type="text/css" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?v=20180522"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/view.css?v=20180921" rel="stylesheet"
          type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/houseAnswer.css?v=20180522"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/default.css?v=20181009"/>

    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://sy.fangxiaoer.com/js/house/verify.js" type="text/javascript"></script>
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/indexBB.css?v=20180522" />
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/office/view.css?v=20180608" rel="stylesheet" type="text/css">
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
    <style>
        .kfzc_heibu {}
        .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
            background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    </style>
</head>
<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>


<div class="cl"></div>
<div th:include="house/detail/fragment_menu::menu" th:with="type=16"></div>
<div th:include="house/detail/fragment_order::forAsk"></div>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div class="w">
    <div class="con_left">
        <div class="housesLeft">
            <div class="housesTitle" th:if="${!#lists.isEmpty(askList) && #lists.size(askList) &gt; 0}">关于
                <th:block th:text="${houseInfo.projectName}"></th:block>
                的
                <th:block th:text="${msg}"></th:block>
                个问题
                <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') == null}" >
                    <a th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login">我要提问</a>
                </div>
                <span  th:if="${#session?.getAttribute('sessionId') != null}" onclick="javascrpit:addProjectAsk();">我要提问</span></div>
            <ul class="houseAnswer" th:if="${!#lists.isEmpty(askList) && #lists.size(askList) &gt; 0}">
                <li th:each="ask:${askList}">
                    <div class="houseAnswerTitle" th:text="${ask.content}">房子户型，性价比，地段怎么样?</div>
                    <div class="houseAnswerPhone"><b th:text="${ask.tel}">157****5676</b><span th:text="${ask.askTime}">2018-03-15 12:44:15</span>
                    </div>
                    <div class="houseAnswerContent"><span>房小二网客服回复：</span>
                        <th:block
                                th:text="${#strings.isEmpty(ask.replyContent) ? '等待回复ing' : ask.replyContent}"></th:block>
                    </div>
                </li>
            </ul>
            <div class="houseAskNoList" th:if="${#lists.isEmpty(askList) || #lists.size(askList) == 0}">
                <p>说出您对楼盘的疑问，我们帮您答疑解惑！</p>
                <!--<a href="" target="_blank">我要提问</a>-->
                <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') == null}" >
                    <a th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login">我要提问</a>
                </div>
                <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') != null}" >
                <a th:if="${#session?.getAttribute('sessionId') != null}" onclick="javascrpit:addProjectAsk();">我要提问</a>
                </div>
            </div>
            <div class="cl"></div>
            <div th:include="fragment/page :: page"></div>
            <div class="cl"></div>
            <div  class="shuoming" style="line-height: 30px;"th:if="${!#lists.isEmpty(askList) && #lists.size(askList) &gt; 0}">
                <b>问答说明：</b>
                <p>
                    1、楼盘问答是房小二网为您提供的疑难问题解答板块，在这里你可以与工作人员直接交流。<br>
                    2、因提问的用户较多，可能无法及时进行回复，我们将尽力在最快的时间内为您解答。<br>
                    3、当涉及广告、不实评论、恶意评论、爆粗口、危害国家安全等不当言论时，房小二网有权实施封号等管理行为。
                </p>
            </div>
        </div>
        <div th:include="house/detail/fragment_menu::freeCall" ></div>

    </div>
    <script type="text/javascript">
        function addProjectAsk() {
            $("#iNeedAsk").show();
        }
    </script>
    <div class="cl"></div>
    <div class="newHouseViewChunk" style="margin-top: 30px;border: 0">
        <div th:include="house/detail/fragment_relasion_house::normalHouse_push"></div>
    </div>
</div>

</body>
<div class="cl"></div>
<div th:include="fragment/fragment::footer_detail"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::unuseCode"  ></div>
<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode"  ></div>
</html>
