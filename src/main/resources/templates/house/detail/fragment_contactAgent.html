<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="contact">
    <div id="contact" class="consultAlert">
        <div class="consultAlertContent">
            <img src="https://static.fangxiaoer.com/web/images/sy/house/consultX.png" alt="">
            <ul>
                <input type="hidden" id="AgentId">
                <li><img id="AgentPic" src="" alt=""></li>
                <li><b id="AgentName">李实堤</b></li>
                <li><b id="AgentShop">喜鹊不动产太湖店</b>&emsp;已为 <span id="AgentAskCount">86</span>人提供咨询服务</li>
                <li class="consulPhone" id="AgentTel"><i></i></li>
                <li><input id="messageToAgent" type="hidden" placeholder="请输入联系方式" maxlength="11" readonly th:value="${session.phoneNum}"></li>
                <li><a onclick="submitToAgent()">立即咨询</a></li>
            </ul>
        </div>
    </div>
    <script type="text/javascript">
        if(!($("#sessionId").val()=="" || $("#sessionId").val()== null || $("#sessionId").val()== undefined)){
            $(".agentUrl").removeAttr("href")
        }
        var agentId = "";
        var agentProjectId ="";
        var agentProjectType = "";
        $(".agentUrl").live("click",function(){
            agentId = $(this).find(".agentId").val();
            agentProjectId = $(this).find(".projectId").val();
            agentProjectType = $(this).find(".projectType").val();
            if (agentId == undefined) {

            }
            if($("#sessionId").val()=="" || $("#sessionId").val()== null || $("#sessionId").val()== undefined){
//                window.location.href=window.location.href+"#login"
            }else{
                $.ajax({
                    type: "post",
                    url: "/getAgentInfo",
                    data: {
                        agentId:agentId,
                    },
                    scriptCharset: 'utf-8',
                    dataType: "json",
                    success: function (data) {
                        /*var data = eval(data);*/
                        var content = data.content;
                        if (data.status == 1) {
                            //弹出框复制
                            $("#AgentId").text(content.MemberID);
                            $("#AgentPic").attr("src",content.agentPic);
                            $("#AgentName").text(content.agentName);
                            $("#AgentAskCount").text(content.askCount);
                            $("#AgentShop").text(content.IntermediaryName);
                            $("#AgentTel").text(content.unionTel);
                            //显示弹出框
                            $(".consultAlert").show();
                        }
                        else {
                        }
                    }
                });
            }
        })
        $(".consultAlert .consultAlertContent>img").live("click",function(){
            $(".consultAlert").hide()
        })
        function submitToAgent() {
            if(sy_confirm.phone($(".consultAlert .consultAlertContent>ul li input").val())!=true){
                alert(sy_confirm.phone($(".consultAlert .consultAlertContent>ul li input").val()))
            }else{
                var sessionId = $("#sessionId").val();
                var params = {
                    sessionId: sessionId,
                    projectId: agentProjectId,
                    type: 1,
                    mobile: $("#messageToAgent").val(),
                    agentId:agentId,
                    comment:"咨询经纪人",
                };
                $.ajax({
                    type: "POST",
                    data: JSON.stringify(params),
                    url: "/addMessageToAgent",
                    dataType: "json",
                    headers: {
                        'Content-Type': 'application/json;charset=utf-8'
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            $(".consultAlert").hide();
                            setTimeout(alert("咨询成功提交，请等待回访"),100)
                            window.location.reload();
                        } else {
                            alert(data.msg);
                        }
                    }
                });
            }
        }
    </script>
</div>
<div th:fragment="loginAgent">
   <!-- <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/tool/login_tc.css?v=20190226">-->
    <!--<script type="text/javascript" src="https://static.fangxiaoer.com/js/fxe_confirm.js"></script>-->
     <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/tool/login_tc.css?v=20180522">
    <script type="text/javascript" src="/js/house/verify.js"></script>
    <div class="modal newTcLogin" id="loginAgent">
        <input type="hidden" id="LoginUrl" value=""/>
        <input type="hidden" id="LoginUrlAgent" value=""/>
        <!--<a class="close" data-dismiss="modal" id="loginClose"></a>-->
        <h1>账户登录<img  data-dismiss="modal" id="loginClose" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="×"></h1>
        <div  class="signup-form clearfix"  >
            <!--<p>登录名：</p>-->
            <input autocomplete=off name="Txt_LoginName" id="Txt_LoginName" value="" class="fxe_mobile" placeholder="请输入手机号" maxlength="11">
            <!--<p>密码/验证码：</p>-->
            <input autocomplete="new-password" name="Txt_Password" id="Txt_Password" class="fxe_password" type="password" placeholder="请输入密码/验证码" maxlength="20">
            <p style="color: red;margin-bottom:10px" id="error_info"><i class="ic_wrong error_info"></i></p>
            <!--<p style="text-align:right;    margin: -24px 7px 16px 0;"><a href="https://my.fangxiaoer.com/RetrPassword.aspx" target="_blank">忘记密码</a></p>-->
            <p style="text-align:right;"><a href="https://my.fangxiaoer.com/RetrPassword.aspx" target="_blank">忘记密码</a></p>
            <input type="button" onclick="submitFormAgent();return false;" name="type" class="button-blue reg newTcLoginBtn" value="登录" data-action="regist">
            <b class="yzm fxe_ReSendValidateCoadAgent ">获取验证码</b>
            <b class="yzm fxe_validateCode " style="display: none"></b>

            <p style="text-align:center;    margin-top: 75px;">没有房小二通行证？ <a href="https://my.fangxiaoer.com/register.aspx" target="_blank" style="font-size: 12px;color: #ff5200;">马上注册一个&gt;</a>　</p>  <div class="clearfix"></div>
            <a href="" class="various" target="_blank"  id="althref" ></a>
        </div>
    </div>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/modal.js"></script>
    <script>
        sy_confirm.init(1,false)
        $(".fxe_ReSendValidateCoadAgent").click(function () {
            if(sy_confirm.phone($("#Txt_LoginName").val())==true){
                $("#error_info i").text("")
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#Txt_LoginName").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                     $(".fxe_ReSendValidateCoadAgent").hide()
                     sy_confirm.timeWait()
                    }else{
                     $("#error_info i").text(res)
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                    $("#error_info i").text(err)
                })
            }else{
                $("#error_info i").text(sy_confirm.phone($("#Txt_LoginName").val()))
            }
        })

        function submitFormAgent() {
            function $Arg(id) { return document.getElementById(id); }
            var r = 0;
            var goods = $("#goods").val();
            if (sy_confirm.phone($("#Txt_LoginName").val())==true) {
                if ($("#Txt_Password").val()!="") {
                    $.ajax({
                        type: "POST",
                        data: { telNumber: $Arg("Txt_LoginName").value, password: $Arg("Txt_Password").value ,goods: goods, registFromUrl: window.location.href},
                        url: "/login",
                        async: false,
                        success: function (data) {
                            r = data;
                        }
                    });
                }else{
                    $("#error_info i").text("请输入验证码/密码")
                }
            }else{
                $("#error_info i").text(sy_confirm.phone($("#Txt_LoginName").val()))
            }
            if (r.status == 1) {
                if ($("#LoginUrl").val() != "" && IsCommentList == 1) {
                    if (r.content.memberType == 2 && r.content.memberType != null) {
                        window.location.href = $("#LoginUrlAgent").val();
                    } else{
                        window.location.href = $("#LoginUrl").val();
                    }
                } else {
                    window.location.reload();
                }

            } else {
                $(".error_info").text(r.msg);
            }
        }

    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            $("a.forgot").click(function () {
                $("#login-modal").modal("hide");
                $("#forgetform").modal({ show: !0 });
            });
            $("#login").modal("hide");
        });
        $(document).keypress(function (e) {
            // 回车键事件
            if (e.which == 13) {
                submitFormAgent();
                return false;
            }
        });
    </script>
</div>
</body>
</html>