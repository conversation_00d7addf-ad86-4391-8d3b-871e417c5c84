<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:if="${houseInfo}" th:text="${'我要点评，发布'+houseInfo.projectName+'楼盘点评 - 房小二网'}"></title>
    <meta th:if="${houseInfo}" name="keywords" th:content="${houseInfo.projectName+',楼盘点评,业主点评,业主点评,楼盘点评'}"/>
    <meta th:if="${houseInfo}" name="description" th:content="${'房小二网我要点评栏目为您提供全面的'+houseInfo.projectName+'楼盘点评信息，为您在沈阳买房提供真实可靠的参考意见，让您更清晰地了解所属楼盘。同时你也可以将自己对楼盘客观真实的楼盘点评提交上来，供广大购房者参考。一手掌握沈阳各大楼盘点评信息，就来房小二网站。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/pcomment/'+projectId+'-'+projectType+'.htm'}">
    <title th:if="${!#strings.isEmpty(commentInfo.projectName)}" th:text="${'我要点评，发布'+commentInfo.projectName+'楼盘点评 - 房小二网'}"></title>
    <meta name="keywords" th:if="${!#strings.isEmpty(commentInfo.projectName)}" th:content="${commentInfo.projectName+',楼盘点评,业主点评,业主点评,楼盘点评'}"/>
    <meta name="description" th:if="${!#strings.isEmpty(commentInfo.projectName)}" th:content="${'房小二网我要点评栏目为您提供全面的'+commentInfo.projectName+'楼盘点评信息，为您在沈阳买房提供真实可靠的参考意见，让您更清晰地了解所属楼盘。同时你也可以将自己对楼盘客观真实的楼盘点评提交上来，供广大购房者参考。一手掌握沈阳各大楼盘点评信息，就来房小二网站。'}"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180522" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/form.css?v=20180911" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
    <style>
        #alertClose{display: none}
        #fileImg>div{
            margin-left: 100px;
            top: 25px;
        }
        #fileImg{
            position: relative;
            height: 200px!important;
            width: 860px;
        }
    </style>
</head>
<body>
<form name="form1" method="post" action="" id="form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <!--搜索栏-->
    <!--<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>-->
    <div class="crumbs" style="    padding-left: 210px;margin-top:41px">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/houses/">沈阳新房</a>&gt;
        <a th:if="${houseInfo}" th:href="${'/house/'+projectId+'-'+projectType+'.htm'}" th:text="${houseInfo.projectName}">金地旭辉·九韵风华</a>
        <a th:if="${!#strings.isEmpty(commentInfo.projectName)}" th:href="${'/house/'+projectId+'-'+commentInfo.projectType+'.htm'}" th:text="${commentInfo.projectName}">金地旭辉·九韵风华</a>
        &gt;
        <span>我要点评</span></div>
    <input  th:value="${#session?.getAttribute('sessionId')}" name = "sessionId" type="hidden">
    <input type="hidden" id="memberType" th:value="${session.memberType}">
    <div class="container">
        <h1  class="containerHeader"><span>我要点评</span>被评为精华点评，可以额外获得大量积分哦！</h1>
        <div class="form">
            <img id="Img_SYXgtPic" th:if="${houseInfo}" th:src="${#strings.isEmpty(houseInfo.defaultPic)? '':houseInfo.defaultPic }" style="border-width:0px;" />
            <img id="Img_SYXgtPic" th:if="${!#strings.isEmpty(commentInfo.projectPic)}" th:src="${#strings.isEmpty(commentInfo.projectPic)? '':commentInfo.projectPic }" style="border-width:0px;" />
            <div class="biaodan">
                <ul>
                    <li rel="">
                        <div class="estimateTitle">地   段</div>
                        <div class="star"><span></span><span></span><span></span><span></span><span></span></div>
                        <div class="estimateDescribe">暂无评分</div>
                        <div class="describe">项目所处地理位置情况</div>
                    </li>
                    <li rel="">
                        <div class="estimateTitle">交   通</div>
                        <div class="star"><span></span><span></span><span></span><span></span><span></span></div>
                        <div class="estimateDescribe">暂无评分</div>
                        <div class="describe">项目到交通实际距离远近</div>
                    </li>
                    <li rel="">
                        <div class="estimateTitle">配   套</div>
                        <div class="star"><span></span><span></span><span></span><span></span><span></span></div>
                        <div class="estimateDescribe">暂无评分</div>
                        <div class="describe">配套与项目的距离及规模大小</div>
                    </li>
                    <li rel="">
                        <div class="estimateTitle">环   境</div>
                        <div class="star"><span></span><span></span><span></span><span></span><span></span></div>
                        <div class="estimateDescribe">暂无评分</div>
                        <div class="describe">项目内绿化及周边污染情况</div>
                    </li>
                    <li rel="">
                        <div class="estimateTitle">性价比</div>
                        <div class="star"><span></span><span></span><span></span><span></span><span></span></div>
                        <div class="estimateDescribe">暂无评分</div>
                        <div class="describe">项目总体性价比情况</div>
                    </li>
                </ul>
            </div>
        </div>
        <input type="hidden" id="star">
        <input type="hidden" id="scoreSatisfaction" th:value="${#strings.isEmpty(commentInfo.scoreSatisfaction) ? 0 :commentInfo.scoreSatisfaction}">
        <input type="hidden" id="scoreSection" th:value="${#strings.isEmpty(commentInfo.scoreSection) ? 0 :commentInfo.scoreSection}">
        <input type="hidden" id="scoreTraffic" th:value="${#strings.isEmpty(commentInfo.scoreTraffic) ? 0 :commentInfo.scoreTraffic}">
        <input type="hidden" id="scoreSupporting" th:value="${#strings.isEmpty(commentInfo.scoreSupporting) ? 0 :commentInfo.scoreSupporting}">
        <input type="hidden" id="scoreEnvironment" th:value="${#strings.isEmpty(commentInfo.scoreEnvironment) ? 0 :commentInfo.scoreEnvironment}">
        <script type="text/javascript">

            //星星
            var star=[parseInt($("#scoreSection").val()),parseInt($("#scoreTraffic").val()),parseInt($("#scoreSupporting").val()),parseInt($("#scoreEnvironment").val()),parseInt($("#scoreSatisfaction").val()) ];
            var starNum=0,starI=0;
            var estimateDescribe=[
                "暂无评分",
                "1星 很差",
                "2星 一般",
                "3星 还行",
                "4星 不错",
                "5星 很好"
            ];
            for(starI=0;starI<=$(".biaodan>ul li").length;starI++){
                for(starNum=0;starNum<star[starI];starNum++){
                    $(".biaodan>ul li").eq(starI).find("span").eq(starNum).addClass("hover")
                }
                $(".biaodan>ul li").eq(starI).find(".estimateDescribe").text(estimateDescribe[star[starI]])
            }

            $(".biaodan>ul li .star span").click(function(){
                var score = 0;
                var scoreSection= 0;
                var scoreTraffic= 0;
                var scoreSupporting= 0;
                var scoreEnvironment= 0;
                var scoreSatisfaction = 0;
                var ind = $(this).index()+1;
                $(this).parent().find("span").removeClass("hover");
                var stari = 0;
                if($(this).parent().parent().attr("rel")==ind){//如果再次选择一样的星数 则取消所有的星星
                    $(this).parent().parent().attr("rel","0");
                    ind=0;
                }else{//正常增加删减星星
                    $(this).parent().parent().attr("rel",ind);
                    while(stari<ind){
                        console.log(stari);
                        $(this).parent().find("span").eq(stari).addClass("hover");
                        stari++
                    }
                }
                $(this).parent().parent().find(".estimateDescribe").text(estimateDescribe[ind])
                star[$(this).parent().parent().index()]=ind;
                console.log(star)
                for (var i = 0 ; i < star.length ; i++) {
                    score +=  star[i];
                }
                scoreSection = star[0];
                scoreTraffic = star[1];
                scoreSupporting = star[2];
                scoreEnvironment = star[3];
                scoreSatisfaction = star[4];
                $("#scoreSection").attr("value",score);
                $("#scoreTraffic").attr("value",score);
                $("#scoreSupporting").attr("value",score);
                $("#scoreEnvironment").attr("value",score);
                $("#scoreSatisfaction").attr("value",score);
                $("#star").attr("value",score);
                console.log(score)
                console.log($("#star").val())
            })

        </script>
        <input name="houseImpression" type="text" id="HouseImpression" class="select_input" th:value="${commentInfo.houseImpression}" />
        <div class="xiangxi">
            <div class="left">
                <i>*</i>详细点评：
            </div>
            <div class="bottom">
                <p>已输入（<i><s class="zi">0</s>字</i>）至少50-500字</p>
                <div class="warm dp_y"></div>
            </div>
            <div class="right">
                <div class="biao xiangxi">
                    <span>综合：</span>
                    <input name="virtues" type="text" th:value="${commentInfo.virtues}" id="Virtues" placeholder="xxx的园林设计了四大花园主题，法式水景游泳池等，整体社区的规划相对于周边的项目还是比较舒适的" />
                </div>
                <div class="biao">
                    <span>缺点：</span>
                    <input name="flaws" type="text" th:value="${commentInfo.flaws}" id="Flaws" placeholder="xxx东南角处为加油站，这对于长期居住的业主来说是非常不利的" />
                </div>
                <div class="biao">
                    <span>户型：</span>
                    <input name="huxing" type="text" th:value="${commentInfo.huxing}" id="Huxing" placeholder="xxx户型的赠送面积足，且都是南北的" />
                </div>
                <div class="biao">
                    <span>物业：</span>
                    <input name="property" type="text" th:value="${commentInfo.property}" id="Property" placeholder="xxx物业在业界得到一定的口碑和认可，服务态度好" />
                </div>
                <div class="biao">
                    <span>车位：</span>
                    <input name="space" type="text" th:value="${commentInfo.Space}" id="Space" placeholder="车位配比较为合理，进出通道设计便捷" />
                </div>
                <div class="biao">
                    <span>配套：</span>
                    <input name="supporting" type="text" th:value="${commentInfo.supporting}" id="Supporting" placeholder="xxx配套齐全，居住还是比较适宜居住的，如对区域抗性不大，还是可以购买的。" />
                </div>
                <div class="biao">
                    <span>性价比：</span>
                    <input name="ratio" type="text" th:value="${commentInfo.ratio}" id="Ratio" placeholder="我是刚开盘的时候买的，有一系列的优惠，价格不高，对得起我这房子" />
                </div>
            </div>
            <div class="cl"></div>
            <div class="xiangxiTishi"><p>提示：完整发表您的心得体会，不仅提高审核的通过率，还有机会成为精华点评。</p></div>
        </div>

        <div class="photoTitle"> <p>上传图片</p></div>

        <div class="pr" style="position: relative; margin: 0 auto;min-height: 216px;">
            <div style="width: 650px; display: inline-block;margin-left: 50px;">
                <link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/diyUpload.css?t=20220902"/>
                <link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/webuploader.css"/>

                <script type="text/javascript" charset="utf-8" src="https://static.fangxiaoer.com/js/webuploader.js"></script>
                <script type="text/javascript" charset="utf-8" src="/ImgUpLoad/Script/diyCommonUpload.js"></script>
                <script type="text/javascript">
                    $(function () {
                        $('#fileImg').diyUpload({
                            url: '/uploadNewCommentPic',
                            success: function (file, data) {
                                console.log(data)
                                var files = $("#ImgUpLoad1_imgValue").val();
                                if (file._info.width > file._info.height && file._info.width >= 450 && file._info.height >= 450) {
                                    if (data) {
                                        if (files == "") {
                                            $(".fileBoxUl li").eq(0).find(".shoutu").show();
                                            $("#ImgUpLoad1_imgValue").val(file.id + "|" + data._raw);
                                        } else {
                                            $("#ImgUpLoad1_imgValue").val(files + "," + file.id + "|" + data._raw);
                                        }
                                    }
                                }
                                //else if (file._info.width <= file._info.height) {
                                //        t = "请上传横版图片"
                                //        $("#fileBox_" + file.id).find(".diyCancel").click()

                                //}
                                else if (file._info.width < 450 || file._info.height < 450) {
                                    t = "请上传大于450*450px的图片"
                                    $("#fileBox_" + file.id).find(".diyCancel").click()

                                }
                                $("#zhaopian").html(t)
                            },
                            error: function (err) {
                                console.log(err);
                            }
                        });
                        //SetImg();
                    });

                </script>
                <div>
                    <input type="hidden" name="showtuindex" id="ImgUpLoad1_showtuIndex" th:value="${showtuIndex}"/>
                    <input type="hidden" name="imglist" id="ImgUpLoad1_imgList" th:value="${imgurl}"/>
                    <input type="hidden" name="pic" id="ImgUpLoad1_imgValue" th:value="${imgurl}"/>
                </div>
                <div id="fileImg">

                </div>
                添加几张照片，让您的信息更受欢迎（最多上传<i style="color:#ff5200">15</i>张，每张最大<i style="color:#ff5200">5M</i>，图片为<i style="color:#ff5200">jpg</i>格式）
            </div>
            <div class="xiaotieshi" style="left: 383px;">
                <h3>小贴士</h3>
                1.带图片的房源排位更靠前，被看到的概率更大；<br>
                2.您上传的首张图片将作为封面图，建议选择室内图片；<br>
                3.为了更好展示您的房源，请选择大于450x450像素的图片。
            </div>
        </div>
        <!--<div class="errorBox"><label id="zhaopian" class="error"></label></div>-->
        <input type="hidden" id="projectId" name="projectId" th:value="${projectId}">
        <input type="hidden" id="commentId" name="commentId" th:value="${commentId}">
        <div class="tijiao">
            <input type="button" name="submit" value="提交点评" id="submit" class="tijiao_btn" />
        </div>
    </div>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
    <script>
        //验证
        var t = 0;
        var tt = 0;
        var gd = 0
        var isYeZhu = 2;
        var isNiMing = 2;
        $(".tijiao_btn").click(function () {
            gd = 0;
            t = 0;
            $(".select_input").val("")
            if ($('.yezhu').is(':checked')) {
                zj();
                mj();
                rq();
                lx();
                qd();
                isYeZhu = 1;
            }
            for (i = 0; i < $(".select span").length; i++) {
                if ($(".select span:eq(" + i + ")").hasClass("focus")) {
                    $(".select_input").val($(".select span:eq(" + i + ")").html() + "," + $(".select_input").val());
                }
            }
           /* if ($(".select_input").val() == "") {
                $(".yx_y").html("请填写楼盘印象！")
                if (gd == "0") {
//                    $("body").animate({ scrollTop: 546 }, 1000);
                    $(document).scrollTop(" 200");
                    gd = 1;
                }
                t++;
            } else {
                $(".yx_y").html("")
            }*/
            if ($(".zi").html() < 50 || $(".zi").html() > 500) {
                $(".dp_y").html("请填写详细点评！")
                if (gd == "0") {
                    $(document).scrollTop(" 685");
                    gd = 1;
                }
                t++;
            } else {
                $(".dp_y").html("")
            }
            if (t != "0") {
                return false;
            }
            var formData = $("#form1").serializeObject();

            formData.isYeZhu =isYeZhu;
            if ($('#cb_IsNiming').is(':checked')) {
                isNiMing =1;
            }
            var picurls = "";
            var pics =  formData.pic;
            if(pics != null && pics != undefined && pics != ''){
                pics = pics.split(",");
                for(var i =0;i<pics.length;i++){
                    var pic =  pics[i].split("|");
                    if(pic.length == 1) {
                        picurls = picurls != "" ? picurls + "," + pic[0].replace("http://images.fangxiaoer.com/sy/qt/middle/","").replace("https://images.fangxiaoer.com/sy/qt/middle/", "").replace("https://imageicloud.fangxiaoer.com/middle/","")
                            : pic[0].replace("http://images.fangxiaoer.com/sy/qt/middle/","").replace("https://images.fangxiaoer.com/sy/qt/middle/", "").replace("https://imageicloud.fangxiaoer.com/middle/","");
                    }else {
                        picurls = picurls!=""? picurls+","+pic[1]: pic[1];
                    }

                }
            }
            /*scoreSatisfaction = star[0];
            scoreSection = star[1];
            scoreTraffic = star[2];
            scoreSupporting = star[3];
            scoreEnvironment = star[4];*/
            formData.pic = picurls;
            formData.isNiMing =isNiMing;
            formData.score = star[0]+star[1]+star[2]+star[3]+star[4];
            formData.projectId = $("#projectId").val();
            formData.commentId = $("#commentId").val();
            formData.isYeZhu = 3;
            formData.scoreSection = star[0];
            formData.scoreTraffic = star[1];
            formData.scoreSupporting = star[2];
            formData.scoreEnvironment = star[3];
            formData.scoreSatisfaction = star[4];
            if (formData.scoreSection == 0 || formData.scoreTraffic == 0 || formData.scoreSupporting == 0 || formData.scoreEnvironment == 0 || formData.scoreSatisfaction == 0) {
                alert("请对所有类别进行星级评定。")
            } else {
                $.ajax({
                    type: "POST",
                    data: JSON.stringify(formData),
                    url: "/saveCommentAgent",
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    dataType: "json",
                    success: function(data) {
                        if(data.status ==1 ){
                            alert("点评提交成功");
                            window.location.href= "https://agent.fangxiaoer.com/comment";
                        }else {
                            alert(data.msg);
                        }
                    }
                });
            }



        });


        //form 表单转json
        $.fn.serializeObject = function () {
            var o = {};
            var a = this.serializeArray();
            $.each(a, function () {
                if (o[this.name]) {
                    if (!o[this.name].push) {
                        o[this.name] = [o[this.name]];
                    }
                    o[this.name].push(this.value || '');
                } else {
                    o[this.name] = this.value || '';
                }
            });
            return o;
        };


        $(".zj").blur(function () { zj(); });
        $(".mj").blur(function () { mj(); });
        $(".my_xl_list li").click(function () {
            if ($(this).parent().parent().find("input").attr("class") == "my_xl_input nf" || $(this).parent().parent().find("input").attr("class") == "my_xl_input yf") {
                if ($(".rq_y").html() != "") { rq(); }
            }
        })
        $(".my_xl_list li").click(function () {
            if ($(this).parent().parent().find("input").attr("class") == "my_xl_input lx") {
                rq();
            }
        });
        function zj() {
            if ($(".zj").val() == "") {
                $(".zj_y").html("请填写总价！");
                if (gd == "0") { $("body").animate({ scrollTop: 230 }, 1000); gd = 1; }
                t++;
            } else {
                $(".zj_y").html("");
            }
        }
        function mj() {
            if ($(".mj").val() == "") {
                $(".mj_y").html("请填写面积！");
                if (gd == "0") { $("body").animate({ scrollTop: 230 }, 1000); gd = 1; }
                t++;
            } else {
                $(".mj_y").html("");
            }
        }
        function rq() {
            if ($(".nf").val() == "0" || $(".yf").val() == "0") {
                $(".rq_y").html("请填写购房日期！");
                if (gd == "0") { $("body").animate({ scrollTop: 230 }, 1000); gd = 1; }
                t++;
            } else {
                $(".rq_y").html("");
            }
        }
        function lx() {
            if ($(".lx").val() == "") {
                $(".lx_y").html("请填写物业类型！");
                if (gd == "0") { $("body").animate({ scrollTop: 230 }, 1000); gd = 1; }
                t++;
            } else {
                $(".lx_y").html("");
            }
        }
        function qd() {
            if ($(".qd").val() == "") {
                $(".qd_y").html("请填写购买渠道！");
                if (gd == "0") { $("body").animate({ scrollTop: 230 }, 1000); gd = 1; }
                t++;
            } else {
                $(".qd_y").html("");
            }
        }


        $(".xiangxi input").keyup(function () {
            tt = 0;
            $(".xiangxi input").each(function () {
                tt = tt + $(this).val().length
            })
            $(".zi").html(tt)
        })

        /*下拉赋值*/




        /*印象*/
        $(".select span").live("click", function () {
            if ($(this).hasClass("focus")) {
                $(this).removeClass("focus");
            } else if ($(".select span.focus").length < 5) {
                $(this).addClass("focus");
            }
        })
        $(".hand input").keyup(function (e) {
            $("#submit").attr("disabled", "disabled");
            var curKey = e.which;
            if (e.which == 13) {
                //$("#submit").attr("disabled", "disabled");
                var handval = $(".hand input").val();
                if (handval != "") {
                    $(".select").append("<span>" + handval.substr(0, 6) + "</span>");
                    $(".hand input").val("");

                }
                $("#submit").removeAttr("disabled");
            }

        });


        /*************我是业主*********/
        $(function () {
            $(".yezhu").click(function () {
                var ch = $(this).attr("checked");
                if (ch == "checked") {
                    $("#cbIsYezhu").attr("checked", "checked");
                    $(".mengban").hide();
                    $(".form .biao").css("color", "#666");
                    $(".host p").css("background-position", "0 0");
                } else {
                    $("#cbIsYezhu").removeAttr("checked");
                    $(".mengban").show();
                    $(".form .biao").css("color", "#999");
                    $(".host p").css("background-position", "0 -25px");
                }
            });
            $(".tijiao_box").click(function () {
                var ch = $(this).attr("checked");
                if (ch == "checked") {
                    $(".tijiao span").css("background-position", "0 4px");
                } else {
                    $(".tijiao span").css("background-position", "0 -19px");
                }
            });
            $(".host a").mouseover(function () {
                $(".host_box").fadeIn();
            });
            $(".host a").mouseout(function () {
                $(".host_box").fadeOut();
            });
        });
        /*************支持placeholder*********/
        $(function () {
            if (!isSupportPlaceholder()) {
                $('input').not("input[type='password']").each(
                        function () {
                            var self = $(this);
                            var val = self.attr("placeholder");
                            input(self, val);
                        }
                );
                $('input[type="password"]').each(
                        function () {
                            var pwdField = $(this);
                            var pwdVal = pwdField.attr('placeholder');
                            var pwdId = pwdField.attr('id');
                            // 重命名该input的id为原id后跟1
                            pwdField.after('<input id="' + pwdId + '1" type="text" value=' + pwdVal + ' autocomplete="new-password" />');
                            var pwdPlaceholder = $('#' + pwdId + '1');
                            pwdPlaceholder.show();
                            pwdField.hide();
                            pwdPlaceholder.focus(function () {
                                pwdPlaceholder.hide();
                                pwdField.show();
                                pwdField.focus();
                            });
                            pwdField.blur(function () {
                                if (pwdField.val() == '') {
                                    pwdPlaceholder.show();
                                    pwdField.hide();
                                }
                            });
                        }
                );
            }
        });
        // 判断浏览器是否支持placeholder属性
        function isSupportPlaceholder() {
            var input = document.createElement('input');
            return 'placeholder' in input;
        }
        // jQuery替换placeholder的处理
        function input(obj, val) {
            var $input = obj;
            var val = val;
            $input.attr({ value: val });
            $input.focus(function () {
                if ($input.val() == val) {
                    $(this).attr({ value: "" });
                }
            }).blur(function () {
                if ($input.val() == "") {
                    $(this).attr({ value: val });
                }
            });
        }

        //回值
        $(function () {
            var huizhi;
            $(".my_xl_input").each(function () {
                if ($(this).val() != "0") {
                    huizhi = $(this).val();
                    $($(this).parent().find("li")).each(function () {
                        if (huizhi == $(this).attr("value")) {
                            $(this).parent().parent().find(".my_xl_txt_other").html($(this).html());
                        }
                    });
                }
            });

            var ary = new Array();
            var spant;
            var str = $("#HouseImpression").val();
            ary = str.split(",");
            for (aa = 0; aa < ary.length - 1; aa++) {
                spant = 0;
                for (i = 0; i < $(".select span").length; i++) {
                    if ($(".select span").eq(i).html() == ary[aa]) {
                        $(".select span").eq(i).addClass("focus");
                        spant = 1;
                    }
                }
                if (spant == "0") {
                    $(".select").after("<span class='focus'>" + ary[aa] + "</span>")
                }
            }

            if ($("#cbIsYezhu").is(":checked")) {
                $("#cbIsYezhu").attr("checked", "checked");
                $(".mengban").hide();
                $(".form .biao").css("color", "#666");
                $(".host p").css("background-position", "0 0");
            } else {
                $("#cbIsYezhu").removeAttr("checked");
                $(".mengban").show();
                $(".form .biao").css("color", "#999");
                $(".host p").css("background-position", "0 -25px");
            }


            $(".xiangxi input").each(function () {
                tt = tt + $(this).val().length
            })
            $(".zi").html(tt)

        });
    </script>

    <div class="cl"></div>
    <div th:include="fragment/fragment :: footer_detail"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::commonFloat"></div>
</form>
</body>
</html>

