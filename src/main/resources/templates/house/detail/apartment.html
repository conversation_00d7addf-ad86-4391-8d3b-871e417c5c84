<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="https://www.thymeleaf.org">

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<title th:text="${houseInfo.projectName+'_'+(#strings.isEmpty(houseInfo.baseInfo.regionName)?'沈阳':houseInfo.baseInfo.regionName)+'写字间出售_沈阳写字楼出售 - 房小二网'}"></title>
		<meta name="keywords" th:content="${houseInfo.projectName+',沈阳写字楼出售,'+(#strings.isEmpty(houseInfo.baseInfo.regionName)?'沈阳':houseInfo.baseInfo.regionName)+'写字楼转让,写字间出兑'}">
		<meta name="description" th:content="${'房小二网沈阳写字楼频道为你提供'+houseInfo.projectName+'，以及'+(#strings.isEmpty(houseInfo.baseInfo.regionName)?'沈阳':houseInfo.baseInfo.regionName)+'写字楼与沈阳其他写字间，门市的出兑，出租，出售与转让信息，购买和发布沈阳写字楼出租出售信息首选房小二网。'}">
		<meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />

		
		<!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->
		<!--<link href="https://static.fangxiaoer.com/web/styles/new_sy/office/view.css?v=20191227" rel="stylesheet" type="text/css" />-->
    	<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/apartment.css?v=20201103">
		
		<!--    <link href="/css/view.css?v=20180608" rel="stylesheet" type="text/css" />-->

		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/userEvaluatePX.css?v=201800911" />
		<script src="https://static.fangxiaoer.com/js/forbiddenCopy.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/search_qh.js"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<script src="/js/AjaxforJquery.js" type="text/javascript"></script>
		<script src="/js/jquery.cookie.js" type="text/javascript"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/Freefone.css?v=20180522" />
		<script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js?v=20180608"></script>
		<link href="https://static.fangxiaoer.com/js/tc/tc.css?v=20180522" rel="stylesheet" type="text/css" />
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
		<script src="https://static.fangxiaoer.com/js/new_video/video.min.js"></script>
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/video-js.min.css">
		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/appointment.css?v=20180522" rel="stylesheet" type="text/css">

		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/Brandpavilion.css?v=20190229">
		<!--    <link rel="stylesheet" href="/css/Brandpavilion.css?v=20190219">-->
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/depthresolution.css?v=20190219">

		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynatown.css?v=20200417" rel="stylesheet" type="text/css" />
		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/payment.css" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/main2017.css" />

		<!--<script type="text/javascript" src="https://static.fangxiaoer.com/js/ychd.js"></script>右侧滑动-->
		<script th:inline="javascript">
			var s = [[${session.muser}]];
			var sid = [[${session.sessionId}]]
			var sph = [[${session.phoneNum}]];

			function filterPhoneNumber(phoneNumber) {
				let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
				//校验手机号是否正确
				if (reg.test(phoneNumber)) {
					phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
					return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
				} else {
					return ''
				}
			}
		</script>
		<script type="text/javascript">
			$(document).ready(function() {
				$(".various4").fancybox({
					'autoScale': false,
					'transitionIn': 'elastic',
					'transitionOut': 'elastic',
					'type': 'iframe',
					'width': 480,
					'height': 320,
				}); //电话弹出
				$(".various").fancybox({
					'autoScale': false,
					'transitionIn': 'elastic',
					'transitionOut': 'elastic',
					'type': 'iframe',
					'width': 600,
					'height': 450,
				}); //电话弹出
			});
		</script>
		<style type="text/css">
	#PicSlide .thumb li img {
    width: 103px;
}.newOurPhone {
    overflow: hidden;
   	margin-top: 0;
}
	.vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
		background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}

	.vrs{ width: 80px !important; height: 80px !important; position: absolute; top: 0 !important; bottom: 0 !important; left: 0 !important; right: 0 !important; margin: auto !important; z-index: 11; }
	.vrs i{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 3; display: block; background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;}
	.vrs b{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 1; display: block;  border-radius: 50%; background-color: rgba(0,0,0,0.5);}
	.iphcon{ position: absolute; top: 5px; left: -26px; width: 18px; height: 22px; margin-right: 7px;
		background-image: url(https://static.fangxiaoer.com/web/images/sy/house/phonez.png); background-size: 100%; background-repeat: no-repeat; background-position: center bottom; }
	.tel1 em{ font-size: 12px; font-family: Microsoft YaHei;font-weight: 400; color: #999999; line-height: 23px; display: block; margin-top: 10px;}
	.tel{ position: absolute; left: 27px; top: 13px;}
	.zbkfDetailBTn {
		width: 80px;
		height: 80px;
		display: block;
		position: absolute;
		left: 50%;
		top: 50%;
		margin-left: -40px;
		margin-top: -40px;
		background-image: url(https://static.fangxiaoer.com/images/coin/liveBtn1.gif);
		background-size: 60% 60%;
		border: 1px solid #ccc;
		background-color: rgb(0 0 0 / 50%);
		border-radius: 40px;
		background-position: center;
		background-repeat: no-repeat;
	}


	.sy-dynatown .sy-single{
		width: 20% !important;

		display: block !important;
	}
	.new-sy-single:nth-child(4n) .news-name{
		border-right: 1px solid #eee !important;
	}
	.new-sy-single:nth-child(5n) .news-name{
		border-right: none;
	}
	.new-sy-single:nth-child(6n){
		display: none;
	}
	.sy-dynatown .news-name{
		width: 139px !important;
	}
	.news-touxiang{
		margin-left: 15px !important;
		margin-right: 15px !important;
	}
	.sy-dynatown .news-name>span{
		white-space: nowrap;
	}
	.new-sy-single:last-child .news-name {
		border-right: none !important;
	}

		</style>
		<link href="https://static.fangxiaoer.com/web/images/sy/selection/g_sharing.css" rel="stylesheet" type="text/css"/>
		<script src="https://static.fangxiaoer.com/web/images/sy/selection/stars.js"></script>
		<script>
			$(document).ready(function(){
				function jqrcodeFn(d,w,h){
					var qrWidth = w;
					var qrHeight = h;
					var logoQrWidth = qrWidth / 4;
					var logoQrHeight = qrHeight / 4;
					var g_a='#'+d;
					var g_b='#'+d+' canvas';
					var g_c='.'+d;
					var g_qurl='https://m.fangxiaoer.com' //上线后 改为m站小二甄选楼盘评测信息页
					$(g_a).qrcode({
						render: "canvas",    //设置渲染方式，有table和canvas
						text: g_qurl,
						width: qrWidth, //二维码的宽度
						height: qrHeight //二维码的高度
					})
					$(g_b)[0].getContext('2d').drawImage($("#g_clogo")[0], (qrWidth - logoQrWidth) / 2, (qrHeight - logoQrHeight) / 2, logoQrWidth, logoQrHeight);
					$(g_c).show()
				}
				jqrcodeFn('g_qrcode','64','67')
				jqrcodeFn('g_hcode','130','130')

				$(".g_dhv").mouseover(function () {
					$(".g_hdcode").show()
				})
				$(".g_dhv").mouseleave(function () {
					$(".g_hdcode").hide();
				})
  			})
		</script>
	</head>
	<script th:inline="javascript">

		var result = [[${houseInfo}]];
		console.log('打印结果',result)

	</script>

	<body class="w1210">


	<img src="https://static.fangxiaoer.com/web/images/sy/selection/logo.png" id="g_clogo" style="display: none;"/>

		<form name="form1" method="post" action="334" id="form1">
			<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
			<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>
			<div class="cl"></div>
			<div th:include="house/detail/fragment_menu::menu" th:with="type=1"></div>
			<!--    <div th:include="house/detail/fragment_activity::activity"></div>-->
			<div th:include="house/detail/fragment_activity::viewActivity"></div>
			<div th:include="house/detail/fragment_activity::projectPay"></div>
			<input type="hidden" id="memberType" th:value="${session.memberType}">
		<div class="newHouseviewPz">

			<!--抢优惠-->
			<input type="hidden" th:value="${houseInfo.coupon.hasCoupon}" id="hasCoupon">
			<script th:inline="javascript">
				$(document).ready(function () {
					var hasCoupon = $("#hasCoupon").val()
					var hasCouponNum = parseInt($("#hasCouponNum").val())
					var robId ;
					if(hasCoupon == '1'){
						robId = $("#couponId").val()
						var robscene = 'pay,' + robId+','+hasCouponNum;

						$.ajax({
							type: "POST",
							async: false,
							url:  "/getNewWxCode",
							data:{
								"scene": robscene,
							},
							dataType : 'json',

							success: function (data) {
								console.log('图片数据')
								console.log(data)
								let robimg = data.img
								$("#getNewWxCode").attr('src',"data:text/html;base64,"+robimg);  // 给太阳码图片赋值
							}
						});
					}else{
						console.log(66666)
					}
				})

			</script>
			<div class="w coupons" style="    margin-top: 20px;" th:if="${houseInfo.coupon.hasCoupon eq '1'}">
				<input type="hidden" th:value="${houseInfo.coupon.id}" id="couponId">
				<input type="hidden" th:value="${houseInfo.coupon.payAmount}" id="hasCouponNum">
				<div class="coupons_l">
					<div class="c_l_1">
						<h2 th:text="${houseInfo.projectName}"></h2>
						<h3>抢优惠</h3>
					</div>
					<div class="c_l_2">
						<p th:text="${houseInfo.coupon.payTitle}"></p>
						<p th:text="${'抢券时间：'+houseInfo.coupon.startTime+'-'+houseInfo.coupon.endTime}"></p>
					</div>
				</div>
				<div class="coupons_r">
					<img id="getNewWxCode" src="" alt="" style="border-radius:0">
					<p>扫码参与活动</p>
				</div>
			</div>




			<div class="w" th:fragment="baseInfo">
			<div class="pic" id="PicSlide">
				<div th:if="${houseInfo.hasLabel eq '1'}" class="instantDetail"><img th:src="${houseInfo.labelContent.advertisePC}" alt=""></div>
				<ul class="img">
					<li th:each="p,pi:${houseInfo.pic}" th:if="${pi.index lt 5}" th:style="'display:' + ${pi.index eq 0 ? 'list-item;' : 'none'}">
						<th:block th:if="${pi.index == 0 and houseInfo.liveInfo.isLive eq '1'}">
							<a th:href="${'/liveDetail/' + houseInfo.liveInfo.liveId + '.htm'}">
								<div class="zbkfDetailBTn"></div>
								<img th:src="${houseInfo.liveInfo.livePic}" th:alt="${houseInfo.liveInfo.liveTitle}"/>
							</a>
						</th:block>
						<th:block th:if="${houseInfo.isPan ne '0' and ((houseInfo.liveInfo.isLive eq '0' and pi.index == 0) or (houseInfo.liveInfo.isLive eq '1' and pi.index == 1))}">
							<a th:href="${'/house/'+projectId + '-' + projectType + '/pic720.htm'}">
								<img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
								<div class="vrs" th:if="${p.title eq '全景'}"><i></i><b></b></div>
							</a>
						</th:block>
						<th:block th:if="${houseInfo.videoTabId ne '0' and ((houseInfo.liveInfo.isLive eq '0' and houseInfo.isPan eq '0' and pi.index == 0) or
                     (houseInfo.liveInfo.isLive eq '1' and houseInfo.isPan eq '0' and pi.index == 1) or
                      (houseInfo.liveInfo.isLive eq '0' and houseInfo.isPan ne '0' and pi.index == 1) or
                       (houseInfo.liveInfo.isLive eq '1' and houseInfo.isPan ne '0' and pi.index == 2))}">
							<div id="player" style="width: 600px;height: 400px">
								<video id="my-video" class="video-js vjs-big-play-centered" controls preload="auto"
									   width="600px;" height="400px" th:poster="${p.url}" data-setup="{}">
									<source th:src="${houseInfo.mobileVideoUrl}">
								</video>
							</div>
						</th:block>
						<th:block th:if="${((houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId eq '0' and houseInfo.isPan eq '0') and (pi.index == 0 or pi.index == 1 or pi.index == 2))
                                    or (((houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId eq '0' and houseInfo.isPan ne '0') or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId eq '0' and houseInfo.isPan eq '0')
                                    or (houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId eq '1' and houseInfo.isPan eq '0')) and (pi.index == 1 or pi.index == 2))
                                    or (((houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId eq '1' and houseInfo.isPan eq '0') or
                                    (houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId eq '1' and houseInfo.isPan ne '0')
                                    or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId eq '0' and houseInfo.isPan ne '0')) and (pi.index == 2))}">
							<a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
								<img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
							</a>
						</th:block>
						<th:block th:if="${pi.index != 0 and pi.index != 1 and pi.index != 2}">
							<a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
								<img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
							</a>
						</th:block>
					</li>
				</ul>
				<div class="thumb">
					<ul>
						<li th:each="p,pi:${houseInfo.pic}" th:if="${pi.index lt 5}">
							<img th:src="${p.url}" th:alt="${houseInfo.projectName+p.title}"/>
							<th:block th:if="${(houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0' and pi.index == 0)
                        or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0' and pi.index == 1)
                        or (houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0' and pi.index == 1)
                        or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0' and pi.index == 2)}">
								<p th:text="视频"></p>
							</th:block>
							<th:block th:unless="${(houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0' and pi.index == 0)
                        or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId ne '0' and houseInfo.isPan eq '0' and pi.index == 1)
                        or (houseInfo.liveInfo.isLive eq '0' and houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0' and pi.index == 1)
                        or (houseInfo.liveInfo.isLive eq '1' and houseInfo.videoTabId ne '0' and houseInfo.isPan ne '0' and pi.index == 2)}">
								<p th:text="${p.title}"></p>
							</th:block>
							<em></em>
						</li>
					</ul>
					<div class="now-status" style="left: 0px;">
					</div>
				</div>
            <div class="cl">
            </div>
			<div th:if="${#lists.size(houseInfo.pic) gt 1}" id="PicslidePrev" style="z-index: 10000"></div>
			<div th:if="${#lists.size(houseInfo.pic) gt 1}" id="PicslideNext" style="z-index: 10000"></div>
            <script th:inline="javascript">
                <!--控制视频播放时能否切换图片-->
                var videoClickState = true;
                var pcVideoTab = [[${houseInfo.videoTabId}]];

                //视频点击执行方法
				$("#player").on("click", function (e) {
					// videoClickState = false;
					if ($(this).index() != 0) {
						videoClickState = true;
						if (pcVideoTab != 0) {
							videojs("my-video").load();
						}
					}
					e.stopPropagation();//阻止冒泡
				})
                /*$("#PicSlide .img>li").click(function (e) {
                    if ($(this).index() != 0) {
                        videoClickState = true;
                        if (pcVideoTab != 0) {
                            videojs("my-video").load();
                        }
                    }
					e.stopPropagation();//阻止冒泡
                });*/

                var dex
                $(".thumb li").hover(function () {
                    dex = $(this).index();
                    if (videoClickState) {
                        $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                    }
                    $(".now-status").css("left", dex * 109 + "px");
					videojs("my-video").pause();//暂停视频播放
                })
                $(document).on("click", "#PicSlide .thumb .now-status", function () {
                    if (dex > 0) {
                        videoClickState = true;
                        if (pcVideoTab != 0) {
                            videojs("my-video").load();
                        }
                        $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                        $(".now-status").css("left", dex * 109 + "px");
                    }
                })
                $(function () {
                    $("#PicSlide .img>li").attr("style", "display:none;").eq("0").attr("style", "display: list-item;");
                    $("#PicslideNext").click(function () {
                        videoClickState = true;
                        var dex = $("#PicSlide").find(".img").find("li[style='display: list-item;']").index() + 1;
                        if (dex == 1 && pcVideoTab != 0) {
                            videojs("my-video").load();
                        }
                        if (dex > $("#PicSlide .img>li").length || dex == $("#PicSlide .img>li").length) {
                            dex = 0;
                        }
                        $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                        $(".now-status").css("left", dex * 109 + "px");
                    })

                    $("#PicslidePrev").click(function () {
                        videoClickState = true;
                        var dex = $("#PicSlide").find(".img").find("li[style='display: list-item;']").index() - 1;
                        if (dex < 0) {
                            dex = $("#PicSlide .img>li").length - 1;
                            if (pcVideoTab != 0) {
                                videojs("my-video").load();
                            }
                        }
                        $("#PicSlide .img>li").attr("style", "display:none;").eq(dex).attr("style", "display: list-item;");
                        $(".now-status").css("left", dex * 109 + "px");
                    })
                })
            </script>
        </div>
			<div class="right" style="width: 548px; height: 500px;">
					<div th:if="!${#strings.toString(houseInfo.projectStatus) eq '3'}" class="price">
						<p>参考价格</p>
						<ul class="type-Btn">
							<li th:each="p:${houseInfo.priceList}">
							<span th:text="${p.name}"></span><i th:text="${#numbers.formatInteger(p.money,3)}"></i>元/㎡
								<span style="width: 20px;height: 20px;padding: 0px 3px;border: 1px solid #e5e5e5;margin-left: 10px;" th:text="${p.type eq '均价'}? '均' :'起'"></span>
							</li>
							<li th:if="${#lists.isEmpty(houseInfo.priceList)}">
								<i>待定</i>
							</li>
							</ul>
						<div class="cl"></div>
							<div style="width: 100%; font-size:14px ;" th:if="${!#strings.isEmpty(houseInfo.rentPrice)}">
								<p>出租价格</p>
							<span th:text="${(#strings.isEmpty(houseInfo.rentPrice)?'暂无资料':(#strings.indexOf(houseInfo.rentPrice,'.') eq -1 ? houseInfo.rentPrice+'元/㎡·天':#strings.toString(houseInfo.rentPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/㎡·天'))}"> </span>
							</div>
						<div class="cl"></div>
					</div>
					<!--售罄-->
					<div th:if="${#strings.toString(houseInfo.projectStatus) eq '3'} " class="price">
						<p th:if="!${#maps.isEmpty(houseInfo.subPrice)}">参考价格</p>
						<ul th:if="!${houseInfo.subPrice eq null} and !${#maps.isEmpty(houseInfo.subPrice)}">
							<li class="jun" th:if="${#strings.toString(#numbers.formatInteger( houseInfo.subPrice.price,0)) ne '0'}">
								<span>二手房</span><i th:text="${#numbers.formatInteger( houseInfo.subPrice.price,0)}"></i>元/m²
								<img src="https://static.fangxiaoer.com/web/images/ico/sign/price_2.gif" alt="价格">
								<a th:href="${'/saleVillages/'+houseInfo.subPrice.subId+'/index.htm'}" target="_blank">
									<span class="priceCk">查看二手房源 ></span>
								</a>
							</li>
							<li class="jun" th:if="${#strings.toString(#numbers.formatInteger( houseInfo.subPrice.price,0)) eq '0'}">
								<span>——</span><i></i>
								<a th:href="${'/saleVillages/'+houseInfo.subPrice.subId+'/index.htm'}" target="_blank">
									<span class="priceCk">查看二手房源 ></span>
								</a>
							</li>
							<div class="cl"></div>
						</ul>
						<ul th:if="${houseInfo.subPrice eq null} or ${#maps.isEmpty(houseInfo.subPrice)}" class="remBg">
							<li>
								<i style="line-height: 38px;">新房已售完</i>
								<em></em>
							</li>
							<div class="cl"></div>
						</ul>
						<div class="cl"></div>
					</div>
					<div class="cl"></div>
					<div class="info">
						<ul>
							<li th:if="${houseInfo.area ne null and #lists.toList(houseInfo.area).size() ne 0}">
                        <i style="letter-spacing: 6px;"> 面积段</i>
						<p><th:block th:each="area:${houseInfo.area}" th:text="${#numbers.formatInteger(area.minArea,1)+'-'+#numbers.formatInteger(area.maxArea,1)+'㎡'}"></th:block></p>
                        <a th:href="${'/house/'+projectId+'-'+projectType+'/layout/pid'+projectId+'-pt' +projectType}" target="_blank" class="infoIconHx"><s></s>全部户型图</a>
						<div class="cl"></div>
                    	</li>
                    		<li>
                    			<i>所属商圈</i>
							<p th:text="${(#strings.isEmpty(houseInfo.baseInfo.businessCircleName) ? '无':houseInfo.baseInfo.businessCircleName)}"> </p>
							<div class="cl"></div>
                    		</li>
                    		<li>
                    			<i>项目特色</i>
                                	<span th:class="${'view_ts' + i.count}" th:each="features,i:${#strings.setSplit(houseInfo.features,' ')}" th:if="${i.index &lt; 4}" th:text="${features}"></span>
							<div class="cl"></div>
                    		</li>
							<li >
								<i>项目地址</i>
                        		<p th:text="${houseInfo.projectAdderss}" class="seeAdderss"></p>
								<a href="#map"class="btn_ckdt infoIconMap"><s></s>查看地图</a>
								  <div class="layer_sun" name="layer_sun">
                            	<div class="ms_sanjiao dt_sj"></div>
                            	<th:block th:text="${houseInfo.projectAdderss}"></th:block>
                        		</div>
                        	<div class="cl"></div>
							</li>
							<li >
								<i>公交路线</i>
								<p th:text="${houseInfo.traffic}"></p>
								<a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}" class="seemore" style="float: right;">更多详细信息></a>
                        <div class="layer_sun " name="layer_sun" id="seemore">
                            <div class="ms_sanjiao dt_sj"></div>
                            <th:block th:text="${houseInfo.traffic}"></th:block>
                        </div>
                        	<div class="cl"></div>
                        
							</li>
						</ul>
						
						<div class="newHouseInfoRicn">
							<div th:if="${!#strings.isEmpty(houseInfo.brandId) or !#maps.isEmpty(houseInfo.rank)}"  style="float: left;">
								<div class="pinpaibang">
									<a th:if="${!#strings.isEmpty(houseInfo.brandId)}" th:href="${'/brandCompany/'+houseInfo.brandId+'.htm'}" target="_blank" class="pinpai1"><i class="barnd_icon barnd_icon_2024"></i><span th:text="${#strings.length(houseInfo.brandName)  gt 10 ?'品牌展示区': houseInfo.brandName}"></span></a>
									<a th:if="${!#maps.isEmpty(houseInfo.rank)}" th:href="${'/projectRank/'+houseInfo.rank.rankType}" class="bangdan"><i class="rank_icon rank_icon_2024"></i><span th:text="${houseInfo.rank.rankTypeName+'第'+houseInfo.rank.rankCount+'名'}"></span></a>
								</div>
							</div>
							<div class="newHouseInfoRicn" style="width: 120px; float: right; border-bottom: 0; margin-top: -5px;">
						 		<a class="soucang" target="_blank" data-toggle="modal" href="#login"><i></i>收藏</a>

                        		<a class="soucang">
                            <script>
                                $(".seemore").mouseover(function () {
                                    $("#seemore").hide();
                                });
                                $("#traffic").mouseover(function () {
                                    $("#seemore").show();
                                })
                                $("#traffic").mouseleave(function () {
                                    $("#seemore").hide();
                                })

                                for (var i = 0; i < $(".info .layer_sun[name=layer_sun]").length; i++) {

                                    var text_x = $(".info .layer_sun[name=layer_sun]").eq(i).height();

                                    if (text_x > 50) {
                                        $(".info .layer_sun[name=layer_sun]").eq(i).css("line-height", "25px")
                                        $(".info .layer_sun[name=layer_sun]").eq(i).css("padding", " 7px")
                                    }

                                }
                            </script>
                            <!--<p class="contrastNo" style="display: none;color: red;">+对比</p>-->
                            <script>
                                $(function () {
                                    $(".contrast").click(function () {
                                        var txt = $(this).attr("value");
                                        var id = $(this).attr("data-id");
                                        console.log(txt)
                                        console.log(id)
                                        $.ajax({
                                            type: "post",
                                            data: { houseId : id,houseName:txt, houseType : 1},
                                            url: "/addContrastHouse",
                                            //dataType: 'json',
                                            async: false,
                                            success: function (list) {
                                                window.open("https://sy.fangxiaoer.com/gotoContrastHouse")
                                                // window.open("http://*************:8082/gotoContrastHouse")

                                            },
                                        });
                                    })
                                })
                            </script>
                        </a>
                        	<p class="contrast" th:value="${houseInfo.projectName}" th:data-id="${projectId}">对比</p>
                        	<div class="cl">
							</div>
						</div>
					<div class="cl">
					</div>
						</div>
					
					
					</div>
					<div class="ourPhone newOurPhone" style="position: relative; height: 142px;">
						<div class="tel" style="margin-top: 15px;">
							<div class="iphcon"></div>
							<div class="tel1"><i>咨询电话:</i>
								<th:block th:text="${houseInfo.sortelTel}"></th:block>&nbsp;&nbsp;
								<em>如需帮助请拨打平台服务电话: ************</em>
							</div>
							<!--<div class="tel2 hid "><a target="_blank" style="cursor: pointer; display: block;" class="various4">免费通话</a></div>-->
						</div>
						<div class="cl"></div>
						<div class="project_main">
							<img id="project_image" src="" />
							<p style="font-size: 12px">微信扫码打电话</p>
						</div>
						<div class="cl">
						</div>
						<script th:inline="javascript">
							/*<![CDATA[*/
                    		var scene = 'newTel,' +[[${projectId}]]+'-'+[[${projectType}]]+',1,' + [[${#strings.contains(houseInfo.sortelTel,'转')?#strings.replace(houseInfo.sortelTel,'转', ',') : houseInfo.sortelTel}]];
							var img = "";
							var sss;
							$.ajax({
								type: "GET",
								async: false,
								url: "/getWxACode",
								data: {
									"scene": scene
								},
								dataType: 'json',
								headers: {
									'Content-Type': 'application/json;charset=utf-8'
								},
								success: function(data) {
									img = data.img;
									sss = data;
								}
							});
							$("#project_image").attr("src", "data:text/html;base64," + img);
							/*]]>*/
						</script>
						<!--<div class="tel0 ">联系房小二网客服<p>转11100</p></div>-->
					</div>

				<!--<div class="info_btn" th:if="${houseInfo.projectStatus ne '3'}">
					<a id='yykf'>
						<input name='' type='button' onclick="showUsercode(1)" value='免费专车' class='button btn_1 newYykf'/>
						&lt;!&ndash;<input name='' type='button' onclick="showUsercode(2)" value='免费通话' class='button btn_1'/>&ndash;&gt;
					</a>
				</div>-->
            		 <div class="info_btn" th:if="${houseInfo.projectStatus eq '3'}">
               			<div style="height: 74px; width: 352px;"></div>
            </div>

				</div>
				<!--right end-->
			</div>
-
<!--			<div th:include="house/detail/fragment_login::login"></div>-->

			<div class="cl"></div>

			<!--小二甄选-->
<!--			<div class="g_ezx" th:style="${#strings.toString(selectionInfo.selectionState) eq '1'}?'':'display: none'">-->
			<div class="g_ezx" style="display: none">
				<div class="g_elogo"></div>
				<div class="g_etag">
					<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i1.png"><span>品牌</span></div>
					<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i2.png"><span>交通</span></div>
					<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i3.png"><span>潜力</span></div>
					<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i4.png"><span>配套</span></div>
					<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i5.png"><span>环境</span></div>
					<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i6.png"><span>户型</span></div>
					<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i7.png"><span>物业</span></div>
					<div class="g_emi"><img src="https://static.fangxiaoer.com/web/images/sy/selection/i8.png"><span>性价比</span></div>
				</div>
				<div class="g_etxt"><i>真实房源</i><i>扫码查看</i></div>
				<div class="g_qrcode"><i id="g_qrcode"></i><!--<em></em>--></div>
			</div>

			<!--置业顾问-->
			<div class="" th:if="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
				<div class="newHouseViewChunk">
					<div class="title">
						<P>经纪人</P>

<!--						<div class="sy-dynatown-ararow">-->
<!--							<span id="up" style="transform: rotate(180deg)"></span>-->
<!--							<span id="down"></span>-->
<!--						</div>-->
					</div>
					<div class="w sy-dynatown">
						<div th:each="agent,h:${houseInfo.agents}"  th:if="${h.index lt 5}" class="sy-single new-sy-single">
							<div class="news-touxiang">
								<img th:src="${agent.agentPic}" th:alt="${agent.memberName}" />
								<div class="On-line">
									<p>在线</p>
									<i class="On-line-icon"></i>
								</div>
							</div>
							<div class="news-name">
								<span th:text="${agent.memberName}"></span>
								<div class="name-btn">
									<a th:href="${#session?.getAttribute('sessionId') ne null?'/im/'+ projectId + '-0-' +agent.memberId:('#login')}" th:data-toggle="${#session?.getAttribute('sessionId') ne null?'':'modal'}" target="_blank">
										<span class="liaogbei-btn">咨询</span>
									</a>
									<div class="call-btn" th:data="${agent.memberId}">
										<span class="show-call">打电话</span>
										<div class="show-ewm">
											<img th:id="${'agent_mobile'+agent.memberId}">
											<p>微信扫码打电话</p>
											<p th:text="${#strings.replace(agent.unionTel,',','转')}"></p>
											<input th:id="${'agent_phone'+agent.memberId}" type="hidden" th:value="${'tel,'+projectId+'-'+projectType+',1,'+agent.unionTel}" />
										</div>
									</div>
								</div>
							</div>
						</div>

					</div>
				</div>
				<script src="https://static.fangxiaoer.com/js/new4-dynatown.js" type="text/javascript"></script>
			</div>
			<!--认购入口-->
			<div id="subscrip" th:include="activity/subscription::subscription"></div>
			
			
		<div class="houseSaleDT" style="height: auto;" th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
        <div class="normalHouseMain">
            <div class="title">
                <p>楼盘动态</p><a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm'}" target="_blank"
                              class="more">查看更多 ></a>
            </div>
            <ul class="xstd">
                <li th:each="saleInfo,iterStat:${saleInfo}" th:if="${iterStat.count &lt; 3}">
                    <span th:text="${saleInfo.addTime.year}+'-'+${saleInfo.addTime.month}+'-'+${saleInfo.addTime.day}"></span>
                    <a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm#'+iterStat.index}"
                       target="_blank">
                        <p th:text="${saleInfo.news}"></p>
                    </a>
                </li>
            </ul>
        </div>
    	</div>


			<script th:inline="javascript">
				var g_starb=[[${selectionInfo.totalScore}]]
			</script>
			<!--楼盘价值测评-->
<!--			<div class="g_assess" th:style="${#strings.toString(selectionInfo.selectionState) eq '1'}?'':'display: none'">-->
			<div class="g_assess" style="display: none">
				<div class="title">
					<p th:text="${houseInfo.projectName + '楼盘价值测评'}"></p>
					<div class="layoutTypeR">
						<a class="g_dhv">查看报告</a>
					</div>
					<div class="g_hdcode">
						<div class="g_hcode" id="g_hcode"></div>
						<div class="g_ctx"><i>真实房源,扫码查看</i></div>
						<em></em>
					</div>
				</div>
				<div class="g_evalu">
					<div class="g_leva">
						<div class="g_leva_t">楼盘测评多维度分析</div>
						<div class="g_leva_m">
							<div class="show_number">
								<li>
									<span th:text="${selectionInfo.totalScore} + '分'"></span>
									<div class="atar_Show">
										<!--<p th:tip="${selectionInfo.totalScore}" th:style="'width:' +${selectionInfo.totalScore}+'px;'"></p>-->
										<div class="g_stara"></div>
										<div class="g_starb" th:style="'width:' +${selectionInfo.totalScore}+'px'"></div>
									</div>
								</li>
							</div>
						</div>
					</div>
					<div class="g_reva">
						<div class="g_rli">
							<div class="g_rf"><em>交通位置</em><em th:text="${selectionInfo.trafficScore}+'分'"></em></div>
							<div class="g_rf"><em>户型设计</em><em th:text="${selectionInfo.unitScore}+'分'"></em></div>
						</div>
						<div class="g_rli">
							<div class="g_rf"><em>外围配套</em><em th:text="${selectionInfo.externalResourceScore}+'分'"></em></div>
							<div class="g_rf"><em>开发品牌</em><em th:text="${selectionInfo.brandScore}+'分'"></em></div>
						</div>
						<div class="g_rli">
							<div class="g_rf"><em>内部配套</em><em th:text="${selectionInfo.internalResourceScore}+'分'"></em></div>
							<div class="g_rf"><em>价值潜力</em><em th:text="${selectionInfo.valueScore}+'分'"></em></div>
						</div>
						<div class="g_rli">
							<div class="g_rf"><em>园区环境</em><em th:text="${selectionInfo.environmentScore}+'分'"></em></div>
							<div class="g_rf"><em>装修品质 </em><em th:text="${selectionInfo.decorateScore}+'分'"></em></div>
						</div>
						<div class="g_rli">
							<div class="g_rf"><em>物业管理</em><em th:text="${selectionInfo.propertyScore}+'分'"></em></div>
							<div class="g_rf"><em>性价比</em><em th:text="${selectionInfo.costPerformanceScore}+'分'"></em></div>
						</div>
					</div>
					<div class="g_clbo"></div>
				</div>
			</div>

			<div class="houseSaleDT" th:if="${#lists.size(houseInfo.pmtPhotos) > 0}">
				<div class="houseImgpc">
					<div class="title">
						<p>平层图</p>
					</div>
					<div class="imgpc" th:each="p:${houseInfo.pmtPhotos}" style="margin-top: 40px;">
						<img th:src="${p.imageUrl}" th:alt="${p.Title}" style="display: block;margin: 0 auto; width: 866px; height: 528px;" />
						<div style="width: 100%; text-align: center; margin: 40px 0; font-size: 20px; color: #333;" th:text="${p.Title}"></div>	
					</div>
									
				</div>	
			</div>
			
<!--			<div class="houseSaleDT">-->
<!--				<div class="houseHuxing">-->
<!--					<div class="title">-->
<!--						<p>楼盘户型</p>-->
<!--					</div>-->
<!--					<ul class="huxing_ul" th:each="p:${houseInfo.pmtPhotos}">-->
<!--            			<li >-->
<!--            				<img th:src="${p.imageUrl}"  />-->
<!--            				<div class="huxing_area" >62.47平1室1厅1卫U户型</div>-->
<!--            			</li>-->
<!--            			<li>-->
<!--            				<img th:src="${p.imageUrl}"  />-->
<!--            				<div class="huxing_area">66.47平1室1厅1卫N户型</div>-->
<!--            			</li>-->
<!--            			<li style="margin-right: 0px;">-->
<!--            				<img th:src="${p.imageUrl}" />-->
<!--            				<div class="huxing_area">66.57平1室1厅1卫O户型</div>-->
<!--            			</li>-->
<!--            			<div class="cl"></div>-->
<!--            		</ul>-->
<!--				</div>	-->
<!--			</div>-->


			<!--深度解析-->
			<div class="w" th:if="${!#maps.isEmpty(houseInfo.deepNews)}">
				
				<div class="deepAnalysis">

					<div class="analysisTitle">
						<div class="analysisSquare">
							深度解析
						</div>
					</div>

					<div class="analysisInfo">

						<div class="analysisInfoImg">
							<a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank" style="text-decoration:none">
								<img th:src="${#strings.isEmpty(houseInfo.deepNews.pic)?'':houseInfo.deepNews.pic}" th:alt="${#strings.isEmpty(houseInfo.deepNews.projectName)?'':houseInfo.deepNews.projectName}" width="180px" height="134px" />
							</a>
						</div>

						<div class="analysisInfoTxt">
							<a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank" style="text-decoration:none">
								<h2 th:text="${#strings.isEmpty(houseInfo.deepNews.titleShow)?'':houseInfo.deepNews.titleShow}"></h2>
							</a>
							<a th:href="${'/news/'+houseInfo.deepNews.id+'.htm'}" target="_blank" style="text-decoration:none">
								<p th:text="${#strings.isEmpty(houseInfo.deepNews.description)?'':houseInfo.deepNews.description}"></p>
							</a>
						</div>
						<div class="cl"></div>
					</div>

				</div>
			</div>

			<div class="cl"></div>
			<div class="newHouseViewChunk">
				<div th:include="house/detail/fragment_map::map2"></div>
        <div class="cl"></div>
				
			</div>
			
			<!--项目点评-->
			<div class="newHouseViewChunk" style="border-bottom: 0;">
				<div th:include="house/detail/fragment_ask::xmpjNew"></div>
        		<div class="cl"></div>
				
			</div>
			<!--楼盘问答-->
			<div class="newHouseViewChunk" style="border-left: 0;border-right: 0;border-bottom: 0;">
				<div th:include="house/detail/fragment_ask::xmzx"></div>
        		<div class="cl"></div>
				
			</div>
			<div class="newHouseViewChunk" style="border: 0;">
				<div th:include="house/detail/fragment_order::forAsk"></div>
			</div>

			<div class="cl"></div>
			<div class="main" style="height: auto; margin-bottom: 20px; margin-top: 0px; padding-top: 0;">
				<div th:include="house/detail/fragment_relasion_house::normalHouse_push"></div>
			</div>
			
			<div class="disclaimer" style="margin-bottom: 36px;"><strong>免责声明</strong>： 楼盘信息由开发商提供，最终以政府部门登记备案为准，请谨慎核查。如该楼盘信息有误，您可以投诉或拔打举报电话：************。
			</div>
			<div class="cl"></div>
			</div>
			<!--底部2-->
			<div class="footer" th:include="fragment/fragment::footer_detail"></div>
			<div th:include="fragment/fragment::tongji"></div>
			<th:block th:if="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
				<div th:include="fragment/fragment::commonFloat" th:with="commonType=1"></div>
			</th:block>
			<th:block th:unless="${houseInfo.agents ne null and #lists.toList(houseInfo.agents).size() ne 0}">
				<div th:include="fragment/fragment::commonFloat"></div>
			</th:block>
			<script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
		</form>
		<div class="modal-backdrop  in" id="loginzhezhao" style="display: none"></div>
		<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
		<!--无验证码订单-->
		<div th:include="house/detail/fragment_order::unuseCode"></div>
		<div th:include="house/detail/fragment_order::guideMessage"></div>
		<!--有验证码订单-->
		<div th:include="house/detail/fragment_order::useCode"></div>
	</body>

</html>