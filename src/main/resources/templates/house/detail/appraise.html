<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head >
    <title th:text="${houseInfo.projectName+'_'+houseInfo.projectName+'点评'+'_'+houseInfo.projectName+'用户点评 - 房小二网'}"></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="keywords" th:content="${houseInfo.projectName+','+houseInfo.projectName+'用户点评,'+houseInfo.projectName+'点评,'+houseInfo.projectName+'口碑'}" />
    <meta name="description" th:content="${'房小二网为您提供'+houseInfo.projectName+'沈阳最新业主点评，真实的'+houseInfo.projectName+'口碑，并且可以畅所欲言，表达您对'+houseInfo.projectName+'的真实看法。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/saleInfo/'+projectId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602"  />
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/main2017.css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/view.css?v=20190112"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/default.css?v=20181009"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/userEvaluate.css?v=20200303" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css?v=20180522"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/office/view.css?v=20190111" rel="stylesheet" type="text/css">
    <script src="/js/house/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
    <style>
        .bigImgShow .showImg ul li img {
            margin-top: -250px;
            margin-left: -250px;
            max-width: 500px;
            height: 100%;
        }
        .tanChuBeiJing{
            z-index: 999999;
        }
        .vru{ width: 34px; height: 34px; position: absolute; left: 5px; top: 115px;
            background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
    </style>
</head>
<body class="w1210">

<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>
<div class="cl"></div>
<div th:include="house/detail/fragment_menu::menu" th:with="type=14"></div>
<div class="w">
    <input type="hidden" id="sessionId" th:value="${session.sessionId}">
    <input type="hidden" id="memberType" th:value="${session.memberType}">
    <div class="housesLeft" th:if="${!#lists.isEmpty(comments)}">
        <div class="synthesize">
            <div class="synthesizeLeft" th:if="${state eq '1'}">
                <b>综合评分
                    <span><th:block th:text="${totalScore.score}"></th:block></span>
                    <i th:if="${!#strings.isEmpty(totalScore.star_grade)}" th:class="${#strings.isEmpty(totalScore.star_grade)?'': 'star'+totalScore.star_grade}" ><div class="red"></div></i>
                </b>
                <ul>
                    <li>地段 <span><th:block th:text="${totalScore.scoreSection}"></th:block></span></li>
                    <li>交通 <span><th:block th:text="${totalScore.scoreTraffic}"></th:block></span></li>
                    <li>配套 <span><th:block th:text="${totalScore.scoreSupporting}"></th:block></span></li>
                    <li>环境 <span><th:block th:text="${totalScore.scoreEnvironment}"></th:block></span></li>
                    <li>性价比 <span><th:block th:text="${totalScore.scoreSatisfaction}"></th:block></span></li>
                </ul>
            </div>
            <div class="synthesizeRight" style="background: none;">
                <a  data-toggle="modal" id="CommentListAdd" href="#login">我要点评</a>
            </div>
            <!--判断我要点评的是经纪人还是个人，继而改变链接-->
            <script th:inline="javascript">
                var _sessionId = $("#sessionId").val();
                var memberType = $("#memberType").val();
                var projectId = [[${projectId}]];
                var projectType = [[${projectType}]];
                $("#CommentListAdd").click(function () {
                    if (_sessionId != null) {
                        if (memberType == 1) {
                            $("#CommentListAdd").attr("href","/comment/"+projectId+"/"+projectType);
                        }else if (memberType == 2) {
                            $("#CommentListAdd").attr("href","/commentAgent/"+projectId+"/"+projectType);
                        }
                    }
                })
            </script>
        </div>
        <ul class="userSynthesize" th:if="${!#lists.isEmpty(comments)}">
            <li th:each="comments:${comments}">
                <table cellspacing="0" cellpadding="0">
                    <tr>
                        <td>
                            <div class="contentRight">
                                <th:block th:if="${comments.isYezhu ne '3'}"><!--业主与非业主头像-->
                                    <div class="headerImg hover">
                                        <div class="headerImges">
                                            <img th:if="${comments.isNiming eq '1'}" th:src="${comments.sex eq '0' ? 'https://static.fangxiaoer.com/web/images/ico/sign/men.gif' : 'https://static.fangxiaoer.com/web/images/ico/sign/women.gif'}">
                                            <img th:if="${comments.isNiming ne '1'}" th:src="${#strings.isEmpty(comments.agentPic) || #strings.indexOf(comments.agentPic,'noagent.png')!=-1 ?(comments.sex eq '0' ? 'https://static.fangxiaoer.com/web/images/ico/sign/men.gif' : 'https://static.fangxiaoer.com/web/images/ico/sign/women.gif'):comments.agentPic}">
                                        </div>
                                        <div class="ueserName"><th:block th:text="${comments.isNiming eq '1'? '匿名':comments.memberTel}"></th:block></div>
                                    </div>
                                    <ul class="houseInfo" th:if="${comments.isYezhu eq '1'}">
                                        <li th:if="${!#strings.isEmpty(comments.projectName)}"><th:block th:text="${'购买项目：'+comments.projectName}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.price)}"><th:block th:text="${'购买价格：'+(#strings.toString(comments.price).contains('.') ?  #strings.toString(comments.price).replaceAll('0+?$','').replaceAll('[.]$', '')+'万' : comments.price+'万')}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.area)}"><th:block th:text="${'购买面积：'+(#strings.toString(comments.area).contains('.') ?  #strings.toString(comments.area).replaceAll('0+?$','').replaceAll('[.]$', '')+'m²' : comments.area+'m²')}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.cyear) and !#strings.isEmpty(comments.cmonth)}"><th:block th:text="${'购买时间：'+comments.cyear+'-'+comments.cmonth}"></th:block></li>
                                        <li th:if="${!#strings.isEmpty(comments.propertyType)}"><th:block th:text="${'物业类型：'+comments.propertyType}"></th:block></li>
                                    </ul>
                                </th:block>
                                <th:block th:if="${comments.isYezhu eq '3'}"><!--经纪人头像-->
                                    <div class="headerImg hover">
                                        <div class="headerImges">
                                            <img th:src="${comments.agentPic}" alt="" />
                                        </div>
                                        <div class="ueserName"><th:block th:text="${comments.memberType eq '2'? '经纪人 '+comments.agentName : comments.agentName}"></th:block></div>
                                        <img th:if="${comments.IsOptimum eq '1'}" src="https://static.fangxiaoer.com/web/images/sy/comment/bestdianping.png" alt="" class="bestAgent" />
                                    </div>
                                </th:block>
                            </div>
                        </td>
                        <td>
                            <div class="contentLeft">
                                <a th:if="${comments.isYezhu eq '3' and comments.memberType eq '2'}" class=' agentUrl agent'  target='_blank' data-toggle='modal'  href='#login'>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/comment/wyzx.png"/>
                                    <input type='hidden' class='agentId' th:value="${comments.memberID}">
                                    <input type='hidden' class='projectId' th:value="${comments.projectId}">
                                </a>
                                <div class="img">
                                    <!--精华标签-->
                                    <img th:if="${comments.isJinghua eq '1'}"  src="https://static.fangxiaoer.com/web/images/sy/comment/jinghua.png" class="bestYnthesize"/>
                                    <!--置顶标签-->
                                    <img th:if="${comments.isZhiding eq '1'}" src='https://static.fangxiaoer.com/web/images/sy/comment/zhiding.png'/>
                                </div>
                                <div class="contentStar">
                                    <i th:if="${!#strings.isEmpty(comments.star_grade)}" th:class="${#strings.isEmpty(comments.star_grade)?'': 'star'+comments.star_grade}" >
                                        <div class="red"></div>
                                    </i>
                                    <!--<ul th:if="${!#strings.isEmpty(comments.score)}">-->
                                        <!--<li><th:block th:text="${'地段：'+comments.scoreSection}"></th:block></li>-->
                                        <!--<li><th:block th:text="${'交通：'+comments.scoreTraffic}"></th:block></li>-->
                                        <!--<li><th:block th:text="${'配套：'+comments.scoreSupporting}"></th:block></li>-->
                                        <!--<li><th:block th:text="${'环境：'+comments.scoreEnvironment}"></th:block></li>-->
                                        <!--<li><th:block th:text="${'性价比：'+comments.scoreSatisfaction}"></th:block></li>-->
                                    <!--</ul>-->
                                </div>
                                <ul class="contentInfoMmore" th:style="${#strings.isEmpty(comments.score)?'padding-top: 20px;':''}">
                                    <li th:if="${!#strings.isEmpty(comments.virtues)}" >
                                        <span th:if="${!#strings.isEmpty(comments.flaws) or !#strings.isEmpty(comments.huxing)or !#strings.isEmpty(comments.property) or !#strings.isEmpty(comments.Space) or !#strings.isEmpty(comments.supporting) or !#strings.isEmpty(comments.ratio)}" style="color: #f05050;">综合：</span>
                                        <th:block th:text="${comments.virtues}"></th:block></li>
                                    <li th:if="${!#strings.isEmpty(comments.flaws)}"><span style="color: #64a014">缺点：</span>
                                        <th:block th:text="${comments.flaws}"></th:block></li>
                                    <li th:if="${!#strings.isEmpty(comments.huxing)}"><span>户型：</span>
                                        <th:block th:text="${comments.huxing}"></th:block></li>
                                    <li th:if="${!#strings.isEmpty(comments.property)}"><span>物业：</span>
                                        <th:block th:text="${comments.property}"></th:block></li>
                                    <li th:if="${!#strings.isEmpty(comments.Space)}"><span>车位：</span>
                                        <th:block th:text="${comments.Space}"></th:block></li>
                                    <li th:if="${!#strings.isEmpty(comments.supporting)}"><span>配套：</span>
                                        <th:block th:text="${comments.supporting}"></th:block></li>
                                    <li th:if="${!#strings.isEmpty(comments.ratio)}"><span>性价比：</span>
                                        <th:block th:text="${comments.ratio}"></th:block></li>
                                </ul>
                                <div class="contentInfo"></div>
                                <ul class="contentInfoImg" th:if="${!#lists.isEmpty(comments.pic)}">
                                    <li th:each="pic:${comments.pic}"><img th:src="${pic.smallImageUrl}"></li>
                                    <span>共<i><th:block th:text="${#lists.size(comments.pic)}"></th:block></i>张图片</span>
                                </ul>
                                <div class="bigImgShow" style="display: none;">
                                    <div class="showImg">
                                        <ul style="margin-left: 0px;">
                                            <li th:each="pic:${comments.pic}">
                                                <img th:src="${pic.smallImageUrl}" onload="imgSize()" >
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"></div>
                                    <div class="prev"></div>
                                    <div class="next"></div>
                                </div>
                                <div class="contentBottom">
                                    <div class="contentReply" style="cursor:pointer"><th:block th:text="${'回复（'+#lists.size(comments.reply)+'）'}"></th:block></div>
                                    <!--<span class="bdsharebuttonbox" style="float: right;"><a href="javascript:void(0);" class="bds_more contentShare" data-cmd="more" th:onclick="'javascript:sharedetailclick(\'https://m.fangxiaoer.com/fang1/'+${projectId} + '-'+ ${projectType} +'.htm\')'">分享</a></span>-->
                                    <div th:class="${'contentShow '+
                        (#strings.isEmpty(comments.flaws)
                    and #strings.isEmpty(comments.huxing)
                    and #strings.isEmpty(comments.property)
                    and #strings.isEmpty(comments.Space)
                    and #strings.isEmpty(comments.supporting)
                    and #strings.isEmpty(comments.ratio)
                    ?'openAndClose':'')}">查看全文</div>
                                    <!--and #strings.length(comments.virtues) eq 108-->
                                    <div class="contentTime"><th:block th:text="${comments.addTime}"></th:block></div>
                                    <input type="hidden" id="commentId" th:value="${comments.commentId}">
                                    <input type="hidden" id="projectId" th:value="${projectId}">
                                    <input type="hidden" id="projectType" th:value="${projectType}">
                                    <!--<script src="/js/share.js"></script>-->
                                    <!--<script src="/js/personal_share.js"></script>-->
                                </div>
                                <div class="reply">
                                    <textarea name="" rows="" cols="" placeholder="回复："></textarea>
                                    <a th:if="${#session?.getAttribute('sessionId') == null}" target='_blank' data-toggle='modal'  href='#login' class="SubmitReply" >提交回复</a>
                                    <a th:if="${#session?.getAttribute('sessionId') != null}" class="SubmitReply" th:commentId="${comments.commentId}">提交回复</a>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg">
                                </div>
                                <!--点评回复列表-->
                                <div class="commentReply" style="display: none">
                                    <div th:each="reply:${comments.reply}">
                                        <span><th:block th:text="${reply.phone}"></th:block></span><th:block th:text="${reply.replyContent}"></th:block><span><th:block th:text="${reply.addTime}"></th:block></span>
                                        <p class='replay_btn'>回复</p>
                                        <div class="reply">
                                            <textarea name="" rows="" cols="" placeholder="回复："></textarea>
                                            <a th:if="${#session?.getAttribute('sessionId') == null}" target='_blank' data-toggle='modal'  href='#login' class="SubmitReply" >提交回复</a>
                                            <a th:if="${#session?.getAttribute('sessionId') != null}" class="SubmitReply" th:commentId="${comments.commentId}">提交回复</a>
                                            <img src="https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg" data-bd-imgshare-binded="1">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
                <!--左侧头像-->

                <!--右侧点评-->

            </li>
        </ul>

        <div th:include="fragment/page :: pageContainParams"></div>
    </div>
    <div class="housesLeft"  th:if="${#lists.isEmpty(comments) || #lists.size(comments) == 0}">
        <div class="houseAskNoList">
            <p>暂无用户点评，快来点评一下吧~~~</p>
            <!--<a href="" target="_blank">我要提问</a>-->
            <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') == null}" >
                <a th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login">我要点评</a>
            </div>
            <div class="remark_btn" th:if="${#session?.getAttribute('sessionId') != null}" >
                <a id="toAddComment" th:if="${#session?.getAttribute('sessionId') != null}"  onclick="javascrpit:addProjectAsk();">我要点评</a>
            </div>
            <script>
                var memberType = $("#memberType").val();
                $("#toAddComment").click(function () {
                    if (memberType == 1) {
                        $("#toAddComment").attr("href","/comment/"+projectId+"/"+projectType);
                    }else if (memberType == 2) {
                        $("#toAddComment").attr("href","/commentAgent/"+projectId+"/"+projectType);
                    }
                })
            </script>
        </div>
    </div>

    <div th:include="house/detail/fragment_menu::freeCall" ></div>

    <div class="cl"></div>
    <div class="newHouseViewChunk" style="margin-top: 30px;border: 0">
        <div th:include="house/detail/fragment_relasion_house::normalHouse_push"></div>
    </div>
</div>
<!--点击我要咨询的js-->
<script>
    $(".agentUrl").live("click",function(){
        var agentId = $(this).find(".agentId").val();
        if (agentId == undefined) {
        }
        if($("#sessionId").val()=="" || $("#sessionId").val()== null || $("#sessionId").val()== undefined){
        }else{
            $.ajax({
                type: "post",
                url: "/getAgentInfo",
                data: {agentId:agentId,},
                scriptCharset: 'utf-8',
                dataType: "json",
                success: function (data) {
                    /*var data = eval(data);*/
                    var content = data.content;
                    if (data.status == 1) {
                        //弹出框复制
                        $("#AgentId").text(content.MemberID);
                        $("#AgentPic").attr("src",content.agentPic);
                        $("#AgentName").text(content.agentName);
                        $("#AgentAskCount").text(content.askCount);
                        $("#AgentShop").text(content.IntermediaryName);
                        //显示弹出框
                        $(".consultAlert").show();
                    }
                    else {
                    }
                }
            });
        }
    })
</script>
<!--关于回复的js-->
<script>
    $(".SubmitReply").click(function () {
        var sessionId = $("#sessionId").val();
        if(sessionId != null && sessionId != undefined && sessionId != ''){
            var text = $(this).parent().find("textarea").val();
            var params  = {
                content: text,
                commentId: $(this).attr("commentId"),
                sessionId:sessionId
            };
            $.ajax({
                type: "POST",
                url: "/saveReply",
                data: JSON.stringify(params),
                headers: {
                    'Content-Type': 'application/json;charset=utf-8'
                },
                dataType: "json",
                success: function (data) {
                    if (data && data.status == 1) {
                        Alertljz.ShowAlert("回复成功，请耐心等待审核！", "https://my.fangxiaoer.com/reply");
                    } else {
                        Alertljz.ShowAlert(data.msg);
                    }

                }
            });
        } else {
            $(".modal-backdrop").show();
            $("#login").show();
            //Alertljz.ShowAlert("登录以后才可以回复哦!");
        }
    });
</script>
<script>
    photo.init()
    $(".userSynthesize>li").each(function(){
        if((parseInt($(this).find(".contentInfoMmore").height())>90)&&($(this).find(".contentShow").hasClass("openAndClose"))){
            $(this).find(".contentInfoMmore").css("max-height","90px")
            $(this).find(".contentShow").show()
            $(this).find(".contentShow").addClass("open")
        }
        if($(this).find(".contentInfoImg li").length>4){
            $(this).find(".contentShow").show()
            $(this).find(".contentShow").addClass("open")
            $(this).find(" .contentLeft .contentInfoImg li").hide()
            $(this).find(" .contentLeft .contentInfoImg li").eq(0).show()
            $(this).find(" .contentLeft .contentInfoImg li").eq(1).show()
            $(this).find(" .contentLeft .contentInfoImg li").eq(2).show()
            $(this).find(" .contentLeft .contentInfoImg li").eq(3).show()
        }
    })
    $(".contentShow").click(function(){
        if($(this).hasClass("open")){
            $(this).removeClass("open")
            $(this).parent().parent().find(".contentInfoMmore").css("max-height","99999999px");
            $(this).parent().parent().find(".contentInfoImg li").show()
            $(this).parent().parent().find(".contentInfoImg span").hide()
            $(this).text("收起全文")
        }else{
            $(this).text("查看全文")
            $(this).addClass("open")
            $(this).parent().parent().find(".contentInfoMmore").css("max-height","90px")
            $(this).parent().parent().find(".contentInfoImg li").hide()
            $(this).parent().parent().find(".contentInfoImg li").eq(0).show()
            $(this).parent().parent().find(".contentInfoImg li").eq(1).show()
            $(this).parent().parent().find(".contentInfoImg li").eq(2).show()
            $(this).parent().parent().find(".contentInfoImg li").eq(3).show()
            $(this).parent().parent().find(".contentInfoImg span").show()
        }
    })
    $(".contentReply").click(function(){
        if($(this).parent().parent().find(".reply").eq(0).css("display")=="none"){
            $(".reply").slideUp()
            $(this).parent().parent().find(".reply").eq(0).slideDown()
            $(this).parent().parent().find(".commentReply").show();//回复列表展示
        }else{
            $(".reply").slideUp()
            $(this).parent().parent().find(".commentReply").hide();//回复列表隐藏
        }

    })
    $(".replay_btn").click(function(){
        if($(this).parent().find(".reply").css("display")=="none"){
            $(".reply").slideUp()
            $(this).parent().find(".reply").slideDown()
        }else{
            $(".reply").slideUp()
        }
    })
    $(".userSynthesize .contentLeft .contentInfoImg li img").click(function(){
        photo.maxIndex=$(this).parent().parent().parent().find(".bigImgShow li").length
        photo.ind=$(this).parent().index()
        photo.showInd=photo.ind
        $(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
        console.log(photo.maxIndex)
        console.log(photo.ind)
$(this).parent().parent().parent().find(".bigImgShow").show()
    })
    $("#alertClose").live("click",function(){
        window.location.reload()
    })
</script>
<script>

    if($(".synthesize").children(".synthesizeLeft").length<1){
        $(".synthesize").children(".synthesizeRight").css("padding-top",0)

        $(".synthesize").children(".synthesizeRight").css("background-position-y",8)
    }
</script>
<div class="cl"></div>
<div th:include="house/detail/fragment_contactAgent::contact"></div>
<div th:include="fragment/fragment:: footer_detail"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::unuseCode"  ></div>
<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode"  ></div>
</body>
</html>
