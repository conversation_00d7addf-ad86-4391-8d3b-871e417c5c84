<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<!--新房详情地图-->
<div th:fragment="map">
    <div class="w map" >
        <s class="dummyMap" id="map"></s>
<!--        <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
        <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
        <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css?v=20180522" />
        <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newhouse_perimeter.css?v=20190401" rel="stylesheet" type="text/css"/>
        <script src="/js/house/SelectZhoubian.js?v=20230605"></script>
        <style type="text/css">
            /*全屏*/
            .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
            .full .nmaptitleleft{margin-top:0}
            .full .symap{height:100%}
            .full .dt1{height:100%;}
            .full #memap{height: 100% !important;}
            .full .mapsele{height:660px !important}
            .nmaptitleleft a {
                float: right;
                padding-left: 7px;
            }
            /*background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg)*/
            .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:6px;}
            .nmaptitleleft a.map_house{background-position:0px 12px}
        </style>
        <style>
            .map_lpcon {
                padding:0!important;
                margin:0!important;
            }
            .map_lp{
                display:block!important;
                padding: 0!important;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
                z-index: 3;
                padding: 16px 0px;
            }
            .BMapLib_SearchInfoWindow {
                font: 12px arial,宋体,sans-serif;
                position: absolute;
                background-color: #fff;
                cursor: default;
                border-radius: 10px;
                padding: 0 10px;
            }
            .BMapLib_SearchInfoWindow img {
                border: 0;
                margin-top: 2px!important;
                top: auto !important;
            }
            .BMapLib_bubble_content p{white-space: normal !important;}
            .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
                border-bottom: 1px solid #ccc;
                height: 40px!important;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
                line-height: 40px!important;
                background-color: #fff!important;
                overflow: hidden;
                height: 40px!important;
                adding: 0 5px;
                font-size: 14px!important;
            }
            .BMapLib_SearchInfoWindow {
                font: 12px arial,宋体,sans-serif;
                position: absolute;
                border: 0!important;
                background-color: #fff;
                cursor: default;
                box-shadow: 0px 0px 10px #999;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
                float: right;
                height: 30px;
                width: 22px;
                cursor: pointer;
                background-color: #fff!important;
                padding-top: 8px!important;
            }
            .full{z-index: 1000000;}
        </style>
        <script th:inline="javascript">
            $(document).ready(function () {
                var id = [[${projectId}]];
                var lng = [[${houseInfo.longitude}]];
                var lat = [[${houseInfo.latitude}]];
                var title = [[${houseInfo.projectName}]];
                var address = [[${houseInfo.projectAdderss}]];
                var imgurl = [[${houseInfo.defaultPic}]];
                var type = [[${projectType}]];
                var tel = [[${houseInfo.sortelTel}]];
                var city = '沈阳';
                var content = "<div class='tc_mes_xx'>" +
                            "<div class='fl'>" +
                            "<ul>" +
                            "<li><b class='cn_ff f20 vm'>" + tel + "</b></li>" +
                            "<li>位置：<a style='color: #333;'>" + address + "</a></li>" +
                            "<li>" +
                            "<a href='https://sy.fangxiaoer.com/house/news/" + id + "-" + type + ".htm' target='_blank'>楼盘动态</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
                            "</li>" +
                            "</ul>" +
                            "</div>" +
                            "<img src='" + imgurl + "' width='120' height='80' alt=''>" +
                            "</div>";
                bdMap.init("memap", { housetype: type, id: id, houselng: lng, houselat: lat, radius: 2000, suofa: 15, bdtitle: title, bdcontent: content, address: address, city: city });
            });
        </script>
        <div id="NewHouseMapZhouBianPeiTao1_mapDom">
            <div class="nmaptitle" th:style="${projectType eq '2'?'display:none':'display:block'}">

                <div class="nmaptitleleft" >
                    <a href="https://sy.fangxiaoer.com/house/housemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                    <i style="float: right;color: #eaeaea">丨</i>
                    <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                    <p >周边配套</p>
                </div>
            </div>
            <div class="symap">
                <div class="dt1">
                    <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                    </div>
                    <div class="mapsele" id="mapsele" style="height: 350px; margin-right: 20px; position: absolute; z-index: 997; right: 0">
                        <!--<div class="msclose" id="msclose"></div>-->
                        <div class="mapTab" id="mapTab">
                            <ul>
                                <li style="padding-left: 22px;padding-right: 22px;" class="hover" onclick="bdMap.searechHouse()"><a href="javascript:void(0)">周边楼盘</a></li>
                                <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                                <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                                <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                                <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                                <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                            </ul>
                        </div>
                        <div class="map_lp">
                            <div id="hs_wrap">
                                <!--<div class="map_tit"><strong><i class="searechMapTitle"></i></strong></div>-->
                                <div class="map_lpcon" style="height: 288px;" id="r-result">
                                    <div class="map_dl">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div id="ful" data_w="" data_h="" style="display: none;"></div>
        </div>
        <script>
            $(function () {
                $(".mapTab ul li ").click(function () {
                    $(".mapTab ul li ").removeClass("hover")
                    $(this).addClass("hover")
                });
                $(".map_dl a").mouseover(function () {
                    $(this).css("background-color", "#e8f4ff");
                });
                $(".map_dl a").mouseout(function () {
                    $(this).css("background-color", "#fff");
                });
                bdMap.bdmap.disableScrollWheelZoom();
            });
            function Full() {
                var type_for_villaMap = [[${projectType}]];
                if (($("#NewHouseMapZhouBianPeiTao1_mapDom").attr("class") == "full")||($("#NewHouseMapZhouBianPeiTao_mapDom").attr("class") == "full")) {
                    $("#NewHouseMapZhouBianPeiTao1_mapDom").removeClass("full")
                    $("#NewHouseMapZhouBianPeiTao_mapDom").removeClass("full")
                    $(".map_lpcon").height("288px")
                    $(".map_full").html("全屏")
                    bdMap.bdmap.disableScrollWheelZoom();
                    var wjg1 = $("#ful").attr("data_w");
                    var hjg1 = $("#ful").attr("data_h");
                    bdMap.bdmap.panBy(-wjg1, -hjg1);
                    if(type_for_villaMap == 2){
                        $(".nmaptitle").hide()
                    }
                    $("body").css("overflow","auto")
                } else {
                    $("#NewHouseMapZhouBianPeiTao_mapDom").addClass("full")
                    $("#NewHouseMapZhouBianPeiTao1_mapDom").addClass("full")
                    $(".map_lpcon").height("100%")
                    $(".map_full").html("取消全屏")
                    $("body").css("overflow","hidden")
//                    bdMap.bdmap.enableScrollWheelZoom();
                    var left = $(".kanfang").offset().left;
                    var htop = -($(".kanfang").position("#memap").top - 50);
                    var wful = $("#memap").width();
                    var hful = $("#memap").height();
                    var nowful = $(".kanfang").width();
                    var nohful = $(".kanfang").height();
                    var wjg = (wful-nowful)/2-left;
                    var hjg = (hful-nohful)/2-htop;
                    $("#ful").attr("data_w",wjg);
                    $("#ful").attr("data_h",hjg);
                    bdMap.bdmap.panBy(wjg, hjg);
                    if(type_for_villaMap == 2){
                        $(".nmaptitle").show()
                    }
                }
            }

        </script>
    </div>
</div>

<div th:fragment="map2">
    <div id="allmap" style="position: relative;">
        <div class="nmaptitle">
            <div class="nmaptitleleft" >
                <a href="https://sy.fangxiaoer.com/house/housemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                <i style="float: right;color: #eaeaea">丨</i>
                <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
            </div>
        </div>
        <div class="mapsele" id="mapsele" style="height: 350px; margin-right: 20px; position: absolute; z-index: 997; right: 0">
            <div class="mapTab" id="mapTab">
                <ul>
                    <li style="padding-left: 22px;padding-right: 22px;" class="hover" onclick="MapsearechMap('',0)"><a href="javascript:void(0)">周边楼盘</a></li>
                    <li onclick="MapsearechMap('公交',1)"><a href="javascript:void(0)">公交</a></li>
                    <li onclick="MapsearechMap('地铁',2)"><a href="javascript:void(0)">地铁</a></li>
                    <li onclick="MapsearechMap('学校',3)"><a href="javascript:void(0)">学校</a></li>
                    <li onclick="MapsearechMap('超市',4)"><a href="javascript:void(0)">超市</a></li>
                    <li onclick="MapsearechMap('医院',5)"><a href="javascript:void(0)">医院</a></li>
                </ul>
            </div>
            <div class="map_lp">
                <div id="hs_wrap">
                    <!--<div class="map_tit"><strong><i class="searechMapTitle"></i></strong></div>-->
                    <div class="map_lpcon" style="height: 288px;" id="r-result">
                        <div class="map_dl map_dl_0"></div>
                        <div class="map_dl map_dl_1"></div>
                        <div class="map_dl map_dl_2"></div>
                        <div class="map_dl map_dl_3"></div>
                        <div class="map_dl map_dl_4"></div>
                        <div class="map_dl map_dl_5"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css?v=2025" />
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newhouse_perimeter.css?v=2025" rel="stylesheet" type="text/css"/>
    <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=I3ZBZ-5K5WL-77WPV-M7G3Q-3Z2ZK-DVFNX"></script>
    <link href="/js/house/newHouseZhoubian2025.css?v=2025" rel="stylesheet" type="text/css"/>
    <!--  <script src="/js/house/newHouseZhoubian2025.js"></script>-->

    <script th:inline="javascript">
            var id = [[${projectId}]];
            var mylat = [[${houseInfo.longitude}]];
            var centerlat = [[${houseInfo.longitude}+0.02]];
            var centerlat2 = [[${houseInfo.longitude}+0.01]];
            var mylng = [[${houseInfo.latitude}]];
            var title = [[${houseInfo.projectName}]];
            var address = [[${houseInfo.projectAdderss}]];
            var imgurl = [[${houseInfo.defaultPic}]];
            var thisProjectStatus = [[${houseInfo.projectStatus}]];
            var thisProjectName = [[${houseInfo.projectName}]];
            var type = [[${projectType}]];
            var tel = [[${houseInfo.sortelTel}]];
            var city = '沈阳';
            var allLabels = [];


        // 创建地图
        var center = new qq.maps.LatLng(mylng,centerlat);
        var map = new qq.maps.Map(document.getElementById('allmap'),{
            center: center,
            zoom: 14,
            disableDefaultUI: true,
            scrollwheel: true,
            minZoom:10,
            maxZoom:16,
        });

        function MapsearechMap(typeName,index){
            // var index = $(this).index()
            console.log(typeName)
            console.log(index)
            clearAllLabels ()
            $('.mapTab ul li').removeClass('hover')
            $('.mapTab ul li').eq(index).addClass('hover')
            $(".map_dl").hide()
            $(".map_dl_"+index).show()
            if (index == 0){
                addProjectLabel(mapData_0)
                map.setCenter(new qq.maps.LatLng(mylng,centerlat));

            }else{
                nearOther(keywordData['mapData_' + index],index)
                thisProject()
            }


        }
        // 周边配套建筑打点
        function nearOther (nearData,index){
            nearData.forEach(function(item) {
                var label = new qq.maps.Label({
                    position: new qq.maps.LatLng(item.location.lat,item.location.lng),
                    map: map,
                    content: `<i class="mapicon map_icon_${index}"><div class="nearOther otherInfo_${item.id}" name="${item.title}"><div class="closeInfo" onclick="closeInfo()"></div><h4>${item.title}</h4><p>${item.address}</p><i class="otherInfoIcon"></i></div></i>`,
                    style: {
                        border: 'none',
                        backgroundColor: 'none',
                        width: '30px',
                        height: '36px',
                    }
                });
                allLabels.push(label);
                // 鼠标划过标记时触发
                qq.maps.event.addListener(label, 'mouseover', function() {
                    console.log(item)
                    $(".otherInfo_"+item.id).addClass('hover')
                    $(".nearOther").hide();
                    $(".otherInfo_" + item.id).show();

                });
                // 鼠标划过标记时触发
                qq.maps.event.addListener(label, 'mouseout', function() {
                    $(".otherInfo_"+item.id).removeClass('hover')
                  $(".nearOther").hide();;
                });
            })
        }
        // 当前项目展示
        function thisProject(){
            map.setCenter(new qq.maps.LatLng(mylng,centerlat2));
            var label = new qq.maps.Label({
                position: new qq.maps.LatLng(mylng,mylat),
                map: map,
                content: `<p class="labelSecHouse Status_${thisProjectStatus} labelSecHouse_${projectId}">${thisProjectName}<i class="showHouseJiao"></i></p>`,
                style: {
                    color: '#FFF',
                    fontSize: '12px',
                    backgroundColor: 'none',
                    border: 'none',
                    textAlign: 'center',
                    alignment: 'center',
                }
            });
        }

        // 获取周边小区
        function fetchNearByProject(){
            $.ajax({
                type: 'post',
                url: "/fetchNearByProject",
                data: {
                    firstId: id,
                    distance: 3,
                    longitude: mylat,
                    latitude:mylng
                },
                success: function (data) {
                    var data = data.content
                    var html = "";
                    addProjectLabel(data)

                    mapData_0 = data
                    for(var i =0 ;i<data.length;i++){
                        if(i == 0){
                            if(data.length<=1){
                                html = '<div class="mao_null"> <p>暂时没有相关信息，看看其他内容吧</p></div>'
                            }else {
                                continue;
                            }
                        }else{
                            var mapProjectType = "";
                            if(data[i].projectType == 2){
                                mapProjectType = "[别墅]";
                            }
                            html += "<dd onclick='showHouseId(this)' data-la='"+ data[i].latitude+"' data-lo='"+ data[i].longitude+"' data-id='"+ data[i].projectId+"'><a title='" + data[i].projectName + mapProjectType+"'><i class='map_icon map_icon_0'></i><span>"+data[i].distance+"米</span>" + data[i].projectName + mapProjectType+"</a></dd>";
                        }
                        if(i>=10){
                            break;
                        }
                    }
                    $(".map_dl_0").html(html);
                    $(".map_dl_0").show()
                }
            })
        }
        fetchNearByProject()

        var TxMapKey = 'I3ZBZ-5K5WL-77WPV-M7G3Q-3Z2ZK-DVFNX'
        var keywordData = [
            {
                'keywordName':'公交',
                'perID':'1',
                'mapData':[],
            },{
                'keywordName':'地铁',
                'perID':'2',
                'mapData':[],
            },{
                'keywordName':'学校',
                'perID':'3',
                'mapData':[],
            },{
                'keywordName':'超市',
                'perID':'4',
                'mapData':[],
            },{
                'keywordName':'医院',
                'perID':'5',
                'mapData':[],
            },
        ]
        keywordData.forEach(function(item) {
            $.ajax({
                type: 'get',
                url: `https://apis.map.qq.com/ws/place/v1/search?key=`+TxMapKey+`&keyword=`+item.keywordName+`&boundary=nearby(`+mylng+`,`+mylat+`,1000,1)&orderby=_distance&output=jsonp`,
                async: false,
                dataType: 'jsonp',
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET,POST',
                },
                success: function (data) {
                    data = data.data
                    keywordData['mapData_' + item.perID] = data;
                    if(data.length >0){
                        for (let i = 0; i < 10 && i < data.length; i++) {
                            // 使用模板字符串和清晰的变量引用
                            const listItem = `<dd onclick='openInfoWindow(this,"${data[i].title}")' data-la="${data[i].location.lat}" data-lo="${data[i].location.lng}">
                                                        <a title="${data[i].title}">
                                                        <i class="map_icon map_icon_${item.perID} showIcon_${data[i].id}"></i>
                                                        <span>${data[i]._distance.toFixed(2)}米</span>
                                                        ${data[i].title}
                                                        </a></dd>`;
                            // 将构建的列表项附加到页面上
                            $(".map_dl_"+item.perID).append(listItem);
                        }
                    }else {
                        console.log('没有值')
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.error('请求失败:', textStatus, errorThrown);
                }
            });
        })

        function showHouseId(e){
            var showLa = $(e).attr('data-la')
            var showLo = $(e).attr('data-lo')
            var showId = $(e).attr('data-id')

            var clickLon = Number(showLo)+0.02
            map.setCenter(new qq.maps.LatLng(showLa, clickLon));
            $(".labelSecHouse").removeClass('hover')
            $(".labelSecHouse_"+showId).addClass('hover')
            $(".projectInfo").hide()
            $(".projectInfo_"+showId).show()
        }

        // 点击列表周边配套展示信息窗
        function openInfoWindow(info,f) {
            var InfoLa = $(info).attr('data-la')
            var InfoLo = $(info).attr('data-lo')

            var clickInfoLon = Number(InfoLo)+0.02
            map.setCenter(new qq.maps.LatLng(InfoLa, clickInfoLon));

            $(".nearOther").hide();
            $(`.nearOther[name="${f}"]`).show();
        }

        function closeInfo(){
            $(".nearOther").hide();
            $(".projectInfo").hide();
        }

        // 在地图上打出项目点
        function addProjectLabel(ProjectData){
            ProjectData.forEach(function(item) {
                var prType = item.prType;
                var projectStatus = item.projectStatus;
                var Price=item.mPrice

                var showThis = ''
                if (item.projectId == id){
                    showThis = 'hover'
                }

                if(projectStatus == 3){
                    Price='已售罄'
                }else{
                    Price=item.price

                }
                var showPrice;
                if(projectStatus!=3){
                    if(item.price=="待定"){
                        showPrice="<span>待定</span>"
                    }else{
                        showPrice="<span>"+parseInt(item.price)+"</span>元/㎡"
                    }
                }else{
                    showPrice="<span style='font-size: 16px;'>项目已售罄</span>"
                }

                var label = new qq.maps.Label({
                    position: new qq.maps.LatLng(item.latitude,item.longitude),
                    map: map,
                    content: `<div class="map-a">
                                    <p class="labelSecHouse Status_${projectStatus} ${showThis} labelSecHouse_${item.projectId}" onclick="clickFun(this)"  data-la='${item.latitude}' data-lo='${item.longitude}' data-id='${item.projectId}'>${item.projectName} ${Price}<i class="showHouseJiao"></i></p>
                                    <div class="projectInfo projectInfo_${item.projectId}">
                                        <h4><a href="https://sy.fangxiaoer.com/house/${item.projectId}-${item.projectType}.htm" target="_blank">${item.projectName}</a><span class="spanType spanType_${item.projectStatus}">${item.projectValue}</span><div class="closeInfo" onclick="closeInfo()"></div></h4>
                                        <div class="InfoMain">
                                            <a href="https://sy.fangxiaoer.com/house/${item.projectId}-${item.projectType}.htm" target="_blank">
                                                <img src="${item.ImageUrl}" alt="" class="InfoImg">
                                                <div class="InfoMainR">
                                                    <p class="InfoP InfoP1">${showPrice}</p>
                                                    <p class="InfoP InfoP2">${item.layout}-${item.minArea}~${item.minArea}m²</p>
                                                    ${projectStatus != 3 ? `<p class="InfoP InfoP3">咨询：<span>${item.sortelTel}</span></p>` : ''}
                                                </div>
                                            </a>
                                        </div>
                                        <div class="sj"></div>
                                    </div>

                               </div>`,
                    style: {
                        // 设置文本样式
                        color: '#FFF',
                        fontSize: '12px',
                        backgroundColor: 'none',
                        border: 'none',
                        textAlign: 'center',
                        alignment: 'center',
                    }
                });
                allLabels.push(label);
                /*qq.maps.event.addListener(label, 'click', function() {
                    clickFun(item)
                });*/
                // 鼠标划过标记时触发
                qq.maps.event.addListener(label, 'mouseover', function() {
                    console.log(item.projectId)
                    $(".labelSecHouse_"+item.projectId).addClass('hover')
                    $(".projectInfo").hide();
                    $(".projectInfo_" + item.projectId).show();

                });
                // 鼠标划过标记时触发
                qq.maps.event.addListener(label, 'mouseout', function() {
                    $(".labelSecHouse_"+item.projectId).removeClass('hover')
                    $(".projectInfo").hide();
                });
            });
        }

        //点击地图上的项目点
        function clickFun(clickItem){
            // 将项目移到中心
            var clickItemLa = $(clickItem).attr('data-la')
            var clickItemLo = $(clickItem).attr('data-lo')
            var clickItemID = $(clickItem).attr('data-id')
            var clickInfoLon = Number(clickItemLo)+0.02
            map.setCenter(new qq.maps.LatLng(clickItemLa, clickInfoLon));
            $(".projectInfo").hide();
            $(".projectInfo_" + clickItemID).show();
            map.setCenter(new qq.maps.LatLng(clickItemLa, Number(clickItemLo)+0.02));
        }

            // 监听地图的 tilesloaded 事件，即加载完成
            qq.maps.event.addListener(map, 'tilesloaded', function() {
                $(".projectInfo").hide()
                $(".projectInfo_" + id).show();
                console.log(id)
            });


        // 清除页面内所有坐标点
        function clearAllLabels (){
            allLabels.forEach(function(label) {
                label.setMap(null);
            });
            allLabels = [];
        }
    </script>
</div>



<!--品牌详情地图-->
<dvi th:fragment="brandMap">
    <div class="w map" >
        <s class="dummyMap" id="map"></s>
<!--        <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
        <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
        <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css?v=20180522" />
        <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" />
        <script src="/js/house/brandMap.js?v=20190927"></script>
        <style type="text/css">
            /*全屏*/
            .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
            .full .nmaptitleleft{margin-top:0}
            .full .symap{height:100%}
            .full .dt1{height:100%;}
            .full #memap{height: 100% !important;}
            .full .mapsele{height:660px !important}
            .nmaptitleleft a {
                float: right;
                padding-left: 7px;
            }
            /*background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg)*/
            .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:6px;}
            .nmaptitleleft a.map_house{background-position:0px 12px}
        </style>
        <style>
            .map_lpcon {
                padding:0!important;
                margin:0!important;
            }
            .map_lp{
                display:block!important;
                padding: 0!important;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
                z-index: 3;
                padding: 16px 0px;
            }
            .BMapLib_SearchInfoWindow {
                font: 12px arial,宋体,sans-serif;
                position: absolute;
                background-color: #fff;
                cursor: default;
                border-radius: 10px;
                padding: 0 10px;
            }
            .BMapLib_SearchInfoWindow img {
                border: 0;
                margin-top: 2px!important;
                top: auto !important;
            }
            .BMapLib_bubble_content p{white-space: normal !important;}
            .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
                border-bottom: 1px solid #ccc;
                height: 40px!important;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
                line-height: 40px!important;
                background-color: #fff!important;
                overflow: hidden;
                height: 40px!important;
                adding: 0 5px;
                font-size: 14px!important;
            }
            .BMapLib_SearchInfoWindow {
                font: 12px arial,宋体,sans-serif;
                position: absolute;
                border: 0!important;
                background-color: #fff;
                cursor: default;
                box-shadow: 0px 0px 10px #999;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
                float: right;
                height: 30px;
                width: 22px;
                cursor: pointer;
                background-color: #fff!important;
                padding-top: 8px!important;
            }
        </style>
        <script th:inline="javascript">
            $(document).ready(function () {
                var id = [[${companyInfo.id}]];
                var lng = [[${companyInfo.longitude}]];
                var lat = [[${companyInfo.latitude}]];
                var suofa = [[${companyInfo.zoomLevel}]];
                var title = "";
                var imgurl = "";
                var type ="";
                var tel = "";
                var city = '沈阳';
                if(checkIsNull(lat)){
                    lat = '41.798756'
                }
                if(checkIsNull(lng)){
                    lng = '123.438973'
                }
                if(checkIsNull(suofa)){
                    suofa = '12'
                }
//                var content = "<div class='tc_mes_xx'>" +
//                    "<div class='fl'>" +
//                    "<ul>" +
//                    "<li><b class='cn_ff f20 vm'>" + tel + "</b></li>" +
//                    "<li>位置：<a style='color: #333;'>" + address + "</a></li>" +
//                    "<li>" +
//                    "<a href='https://sy.fangxiaoer.com/house/news/" + id + "-" + type + ".htm' target='_blank'>销售动态</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
//                    "</li>" +
//                    "</ul>" +
//                    "</div>" +
//                    "<img src='" + imgurl + "' width='120' height='80' alt=''>" +
//                    "</div>";
                bdMap.init("memap", { id: id, houselng: lng, houselat: lat, radius: 2000, suofa: suofa, city: city });
            });
            function checkIsNull(e) {
                if(e == null || e == ''||e == undefined){
                    return true;
                }else {
                    return false;
                }
            }
        </script>
        <div id="NewHouseMapZhouBianPeiTao1_mapDom">
            <div class="nmaptitle">

                <div class="nmaptitleleft" >
                    <a href="https://sy.fangxiaoer.com/house/housemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                    <i style="float: right;color: #eaeaea">丨</i>
                    <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                </div>
            </div>
            <div class="symap">
                <div class="dt1">
                    <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                    </div>

                </div>
            </div>
        </div>
        <script>
            $(function () {
                $(".mapTab ul li ").click(function () {
                    $(".mapTab ul li ").removeClass("hover")
                    $(this).addClass("hover")
                });
                $(".map_dl a").mouseover(function () {
                    $(this).css("background-color", "#e8f4ff");
                });
                $(".map_dl a").mouseout(function () {
                    $(this).css("background-color", "#fff");
                });
            });
            function Full() {
                if (($("#NewHouseMapZhouBianPeiTao1_mapDom").attr("class") == "full")||($("#NewHouseMapZhouBianPeiTao_mapDom").attr("class") == "full")) {
                    $("#NewHouseMapZhouBianPeiTao1_mapDom").removeClass("full")
                    $("#NewHouseMapZhouBianPeiTao_mapDom").removeClass("full")
                    $(".map_lpcon").height("288px")
                    $(".map_full").html("全屏");
                    bdMap.bdmap.disableScrollWheelZoom();
                } else {
                    $("#NewHouseMapZhouBianPeiTao_mapDom").addClass("full")
                    $("#NewHouseMapZhouBianPeiTao1_mapDom").addClass("full")
                    $(".map_lpcon").height("100%")
                    $(".map_full").html("取消全屏")
                    $(".nmaptitle").show()
                    bdMap.bdmap.enableScrollWheelZoom();
                }
            }
        </script>
    </div>
</dvi>


<dvi th:fragment="brandQQMap">
    <div class="w map" >
        <s class="dummyMap" id="map"></s>
<!--        <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
        <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=I3ZBZ-5K5WL-77WPV-M7G3Q-3Z2ZK-DVFNX"></script>

<!--        <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>-->
        <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css?v=20180522" />
        <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" />
        <script src="/js/QQMap/brandMap.js?v=22"></script>
        <style type="text/css">
            /*全屏*/
            .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
            .full .nmaptitleleft{margin-top:0}
            .full .symap{height:100%}
            .full .dt1{height:100%;}
            .full #memap{height: 100% !important;}
            .full .mapsele{height:660px !important}
            .nmaptitleleft a {
                float: right;
                padding-left: 7px;
            }
            /*background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg)*/
            .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:6px;}
            .nmaptitleleft a.map_house{background-position:0px 12px}
        </style>
        <style>
            .map_lpcon {
                padding:0!important;
                margin:0!important;
            }
            .map_lp{
                display:block!important;
                padding: 0!important;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
                z-index: 3;
                padding: 16px 0px;
            }
            .BMapLib_SearchInfoWindow {
                font: 12px arial,宋体,sans-serif;
                position: absolute;
                background-color: #fff;
                cursor: default;
                border-radius: 10px;
                padding: 0 10px;
            }
            .BMapLib_SearchInfoWindow img {
                border: 0;
                margin-top: 2px!important;
                top: auto !important;
            }
            .BMapLib_bubble_content p{white-space: normal !important;}
            .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
                border-bottom: 1px solid #ccc;
                height: 40px!important;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
                line-height: 40px!important;
                background-color: #fff!important;
                overflow: hidden;
                height: 40px!important;
                adding: 0 5px;
                font-size: 14px!important;
            }
            .BMapLib_SearchInfoWindow {
                font: 12px arial,宋体,sans-serif;
                position: absolute;
                border: 0!important;
                background-color: #fff;
                cursor: default;
                box-shadow: 0px 0px 10px #999;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
                float: right;
                height: 30px;
                width: 22px;
                cursor: pointer;
                background-color: #fff!important;
                padding-top: 8px!important;
            }

            .sanjiao{
                position: absolute;
                width: 16px;
                height: 10px;
                top: 34px;
                left: 50%;
                margin-left: -8px;
                overflow: hidden;
                background-repeat: no-repeat !important;
            }
            .sanjiao1{ background: url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png);}
            .sanjiao2{ background: url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png);}
            .sanjiao3{ background-image: url(https://static.fangxiaoer.com/web/images/brand/Gray.png);}
            .sanjiao4{ background-image: url(https://static.fangxiaoer.com/web/images/brand/black.png);}
            .kanfang{
                display: none;
                z-index: 99;
            }
            .text_left{
                text-align: left;
            }
        </style>
        <script th:inline="javascript">
            $(document).ready(function () {
                var id = [[${companyInfo.id}]];
                var lng = [[${companyInfo.longitude}]];
                var lat = [[${companyInfo.latitude}]];
                var suofa = [[${companyInfo.zoomLevel}]];
                var title = "";
                var imgurl = "";
                var type ="";
                var tel = "";
                var city = '沈阳';
                if(checkIsNull(lat)){
                    lat = '41.798756'
                }
                if(checkIsNull(lng)){
                    lng = '123.438973'
                }
                if(checkIsNull(suofa)){
                    suofa = 12
                }
//                var content = "<div class='tc_mes_xx'>" +
//                    "<div class='fl'>" +
//                    "<ul>" +
//                    "<li><b class='cn_ff f20 vm'>" + tel + "</b></li>" +
//                    "<li>位置：<a style='color: #333;'>" + address + "</a></li>" +
//                    "<li>" +
//                    "<a href='https://sy.fangxiaoer.com/house/news/" + id + "-" + type + ".htm' target='_blank'>销售动态</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
//                    "</li>" +
//                    "</ul>" +
//                    "</div>" +
//                    "<img src='" + imgurl + "' width='120' height='80' alt=''>" +
//                    "</div>";
                bdMap.init("memap", { id: id, houselng: lng, houselat: lat, radius: 2000, suofa: suofa, city: city });
            });
            function checkIsNull(e) {
                if(e == null || e == ''||e == undefined){
                    return true;
                }else {
                    return false;
                }
            }
        </script>
        <div id="NewHouseMapZhouBianPeiTao1_mapDom">
            <div class="nmaptitle">

                <div class="nmaptitleleft" >
                    <a href="https://sy.fangxiaoer.com/house/housemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                    <i style="float: right;color: #eaeaea">丨</i>
                    <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                </div>
            </div>
            <div class="symap">
                <div class="dt1">
                    <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                    </div>

                </div>
            </div>
        </div>
        <script>
            $(function () {
                $(".mapTab ul li ").click(function () {
                    $(".mapTab ul li ").removeClass("hover")
                    $(this).addClass("hover")
                });
                $(".map_dl a").mouseover(function () {
                    $(this).css("background-color", "#e8f4ff");
                });
                $(".map_dl a").mouseout(function () {
                    $(this).css("background-color", "#fff");
                });
            });
            function Full() {
                if (($("#NewHouseMapZhouBianPeiTao1_mapDom").attr("class") == "full")||($("#NewHouseMapZhouBianPeiTao_mapDom").attr("class") == "full")) {
                    $("#NewHouseMapZhouBianPeiTao1_mapDom").removeClass("full")
                    $("#NewHouseMapZhouBianPeiTao_mapDom").removeClass("full")
                    $(".map_lpcon").height("288px")
                    $(".map_full").html("全屏");
                    bdMap.bdmap.disableScrollWheelZoom();
                } else {
                    $("#NewHouseMapZhouBianPeiTao_mapDom").addClass("full")
                    $("#NewHouseMapZhouBianPeiTao1_mapDom").addClass("full")
                    $(".map_lpcon").height("100%")
                    $(".map_full").html("取消全屏")
                    $(".nmaptitle").show()
                    bdMap.bdmap.enableScrollWheelZoom();
                }
            }
        </script>
    </div>
</dvi>
</body>
</html>


