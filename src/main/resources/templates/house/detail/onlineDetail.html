<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${houseInfo.projectName+'沈阳新楼盘 - 房小二网'}"></title>
    <meta name="keywords" th:content="${houseInfo.projectName+',沈阳新楼盘,沈阳新房,沈阳楼盘,沈阳买房,沈阳洋房,洋房,沈阳商铺,商铺,沈阳房产,楼盘地图,沈阳房产网,房小二网'}" />
    <meta name="description" content="房小二网是沈阳最大的房地产新楼盘数据库,搜集沈阳市最新楼盘,普宅、洋房、商铺楼盘展示,是沈阳最全的新房房源信息平台，在这里有大量的沈阳普宅、洋房、商铺信息供您查询，提供沈阳新房楼盘信息、户型图库、购房资讯、房价动态、房屋买卖指南,让您及时了解沈阳新楼盘最新动态,沈阳新房房价,沈阳新房打折销售等相关内容。"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/online.css?t=20180502" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/house.css?t=20180502" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/view.css?t=20180921" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/js/tc/tc.css?t=20180502" rel="stylesheet" type="text/css" />
    <script src="/js/AjaxforJquery.js" type="text/javascript"></script>
    <!--[if IE]>
    <script src="ttp://static.fangxiaoer.com/js/jsq/house/html5shiv.js"></script>
    <![endif]-->

    <script type="text/javascript">
        $(document).ready(function () {

            $(".view_xmts span").bind("mouseover", function () {
                $(".miaoshu").hide();
                $(this).find(".miaoshu").show();
            })
            $(".view_xmts span").bind("mouseleave", function () {
                $(".miaoshu").hide();
            })

        });

    </script>
    <script language="javascript">
        $(function () {
            $(".tt1").html($("#htype-dropdown li:eq(0)").html());
            $("#currentChooseArea").val($("#htype-dropdown li:eq(0)").attr("data-code"))
            $('#totalHousePrice').attr('total-price', Math.round($("#currentBuildingPirce").val() * $("#htype-dropdown li:eq(0)").attr("data-code") / 10000))
        })
    </script>
    <link href="https://static.fangxiaoer.com/js/jsq/house/index.css" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/js/jsq/house/framework.css" rel="stylesheet" />

    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/appointment.css" rel="stylesheet" type="text/css">
<style>
    .btn_ckdt{display: none}
</style>
</head>

<body class="w1210">
<form>

    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

    <div class="cl"></div>
    <div th:include="house/detail/fragment_menu::menu" th:with="type=4"></div>

    <div class="w">
        <div class="con_left">
            <div class="pic1" id="PicSlide1">
                <i th:if="${#strings.toString(roominfo.activityState) eq '99'}"></i>
                <ul class="img">

                    <li style="display:list-item;" th:each="xgt:${roominfo.xgtPic}">
                        <img th:src="${xgt.pic}"  alt="房源图片"/></li>
                    <li style="display:list-item;">
                        <img th:src="${roominfo.pic}"  alt="房源图片"/></li>
                </ul>
                <div class="thumb">
                    <ul>

                        <li th:each="xgt:${roominfo.xgtPic}" ><img th:src="${xgt.pic}" alt="房源图片" /></li>
                        <li><img th:src="${roominfo.pic}" alt="房源图片" /></li>


                    </ul>
                    <div class="now-status" style="left:0px;"></div>
                </div><div class="cl"></div>
            </div>
            <div class="right_house">
                <div class="price_house">
                    <th:block th:if="${#strings.toString(roominfo.prePrice) ne '0.0' and !#strings.isEmpty(roominfo.prePrice)}">
                        特　　价：<i th:text="${roominfo.prePrice}"></i>万元/套
                        <s th:text="${ roominfo.prePrice ge roominfo.price?'':'原价'+roominfo.price+'万元'}">原价89.0万元</s>
                        <th:block th:text="${roominfo.prePrice lt roominfo.price?'共省'+#numbers.formatInteger ((roominfo.price - roominfo.prePrice)*10000,1)+'元':''}"></th:block>
                    </th:block>
                    <th:block th:if="${#strings.toString(roominfo.prePrice) eq '0.0' or #strings.isEmpty(roominfo.prePrice)}">
                        原　　价：<i th:text="${(#strings.toString(roominfo.price) eq '0' or #strings.isEmpty(roominfo.price))?'待定':roominfo.price}"></i>万元/套
                    </th:block>
                </div>
                <ul class="info_house">
                    <li><p th:text="${'户　　型：'+roominfo.roomType+'室'+roominfo.hallType+'厅'+roominfo.guardType+'卫'}"></p><th:block th:text="${'装　　修：'+roominfo.decoration}"></th:block></li>
                    <li><p th:text="${'面　　积：'+#numbers.formatInteger(roominfo.Area,1)+'m²'}"></p><th:block th:text="${'供暖类型：'+roominfo.heating}"></th:block></li>
                    <li><p th:text="${'建筑类型：'+roominfo.projectTypeStr}"></p><th:block th:text="${'房源状态：'+roominfo.state}"></th:block></li>
                </ul>
                <ul class="info_house"><!--活动房源显示-->
                    <li><p th:text="${'房源位置：'+roominfo.roomLocation}"></p><th:block th:text="${'楼　　盘：'+houseInfo.projectName}"></th:block><i style="display: none">（<a href="#">房源</a>　<a href="#">相册</a>）</i></li>
                    <li><p th:text="${'朝　　向：'+roominfo.toward}"></p></li>

                </ul>
                <div class="cl"></div>
                <div class="ico"><a href="#dk" class="jsq">房贷计算器</a></div>
                <div class="cl"></div>

                <div class="tel ">
                    <div class="tel1"><i>售楼处咨询电话　　更新时间　
                       <th:block th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd')}"></th:block>
                    </i><br><th:block th:text="${houseInfo.sortelTel}"></th:block>&nbsp;&nbsp; </div>
                </div>
                <div class="cl"></div>
                <div class="info_btn_house"><a href="javascript:void(0)" class="button btn_2 " onclick="window.open('http://v2.live800.com/live800/chatClient/chatbox.jsp?companyID=487285&configID=124543&jid=6535502194&skillId=6680','_blank','scrollbars=0,resizable=0,width=920,height=620;');return false">在线咨询</a></div>
            </div><!--right-->
            <div class="cl"></div>

            <div class="title">
                <ul>
                    <li class="hover">房源卖点</li>
                    <li><a href="#fyzp">房源照片</a></li>
                    <li id="luopanxinxi"><a href="#lpxx">楼盘信息</a></li>
                    <li class="hid"><a href="#shpt">生活配套</a></li>
                </ul>
            </div>

            <div class="maidian">
                <ul>
                    <li><p>房源位置</p><th:block th:text="${roominfo.locationOk}"></th:block></li>
                    <li><p>去化情况</p><th:block th:text="${roominfo.saleOk}"></th:block></li>
                </ul>
                <ul>
                    <li><p>优惠活动</p><th:block th:text="${roominfo.activityOk}"></th:block></li>
                    <li><p>赠送信息</p><th:block th:text="${roominfo.givingOk}"></th:block></li>
                </ul>
                <ul>
                    <li><p>价格优势</p><th:block th:text="${roominfo.priceOk}"></th:block></li>
                    <li><p>其他卖点</p><th:block th:text="${roominfo.otherOk}"></th:block></li>
                </ul>
            </div>
            <div class="cl"></div>

            <div class="title" id="fyzp"><ul><li class="hover">房源照片</li></ul></div>
            <div class="photo"><img th:src="${roominfo.pic}" alt="房源图片">
                <p th:text="${roominfo.Area+'m²'+roominfo.roomType+'室'+roominfo.hallType+'厅'+roominfo.guardType+'卫'}"></p></div>

            <div class="photo" th:each="lp:${roominfo.lwtPic}">
                <img th:src="${lp.pic}" alt="房源图片">
                <p th:text="${lp.title}"></p></div>





        </div><!--con_left end-->
        <div class="con_right">
            <img th:style="${roominfo.logo eq '' or roominfo.logo eq null}?'display:none':'display:block'" th:src="${'https://images.fangxiaoer.com/sy/qt/big/'+roominfo.logo}" alt='认证房源'><br>
            <img src="https://static.fangxiaoer.com/web/images/sy/house/house/renzheng.gif" alt="认证房源"></div><!--con_right end-->
    </div><!--w end -->

    <div class="cl"></div>

    <div id="lpxx" class="w title"><ul><li class="hover">楼盘信息</li></ul></div>



    <div class="w" th:if="${houseInfo.projectType == '1'}" th:include="house/detail/normalHouse::baseInfo"></div>
    <div class="w" th:if="${houseInfo.projectType == '3'}" th:include="house/detail/apartment::baseInfo"></div>


    <div class="cl"></div>



    <!--计算器-->
    <div class="w" id="dk" th:include="house/detail/fragment_calculator::calculator"></div>

    <div class="cl"></div>

    <div class="cl"></div>
    <div class="footer" th:include="fragment/fragment::footer_detail"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::commonFloat"></div>
    <div class="cl"></div>

    <div th:include="house/detail/fragment_request::request"></div>
</form>

<script type="text/javascript" src="https://static.fangxiaoer.com/js/pic_qh.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        //幻灯片调用
        slider.init({
            'id': $('#PicSlide'),
            way: 'left',
            interval: 4000
        });

        slider1.init({
            'id': $('#PicSlide1'),
            way: 'left',
            interval: 4000
        });

    });
</script>
<script language="javascript">
    $(function () {
        var v;
        len = $(".loucen a").length
        if (len >= 10) {
            $(".loucen p").show();
            $(".loucen").hover(function () {
                $(".loucen a").show();
                $(".loucen span").css("height", "auto");
                $(".loucen p").hide();
                $(".info_house a").click(function () {
                    $(".info_house a").removeClass("hover")
                    $(this).addClass("hover")
                    v = $(this).index()
                })
            }, function () {
                $(".loucen span").css("height", 27);
                $(".loucen p").show();
                if (v > 9) {
                    t = parseInt(v / 9)
                    t = t * 9
                    $(".loucen a:lt(" + t + ")").hide();
                }
            });
        }
    });
</script>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/time.js"></script>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/house-pic-qh.js"></script>
</body>
</html>