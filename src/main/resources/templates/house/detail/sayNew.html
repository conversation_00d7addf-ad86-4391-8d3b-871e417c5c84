<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${houseInfo.projectName+'新闻_沈阳'+houseInfo.projectName+'资讯 - 房小二网'}"></title>
    <meta name="keywords" th:content="${houseInfo.projectName+'新闻,沈阳'+houseInfo.regionName+houseInfo.projectName+'资讯'}">
    <meta name="description" th:content="${'房小二网为您提供'+houseInfo.projectName+'动态，包括'+houseInfo.regionName+houseInfo.projectName+'楼盘新闻，开盘时间信息、户型房源价格等实时信息，为您选房、看房提供优质参考！'}" >
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang1/'+projectId+'-'+projectType+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112" />
    <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/say.css?t=20180921" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/time_today.js"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css?v=20180522"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/office/view.css?v=201890111" rel="stylesheet" type="text/css">
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
        var sph = [[${session.phoneNum}]];

        function filterPhoneNumber(phoneNumber) {
            let reg = /^1[3456789]{1}\d{9}$/	//必须是以1开头，第二位必须是3-9中的任意一个数，后面9位必须是数字
            //校验手机号是否正确
            if (reg.test(phoneNumber)) {
                phoneNumber = phoneNumber.toString()	//先强制转换成字符串类型
                return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
            } else {
                return ''
            }
        }
    </script>
</head>

<body class="w1210">
<form name="form1" method="post" action="268" id="form1">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>


    <div class="cl"></div>
    <div th:include="house/detail/fragment_menu::menu" th:with="type=8"></div>
    <div class="w">
        <div class="con_left">
            <div class="title"><p>楼盘说说</p></div>
            <div class="ico_time"></div>

            <div class="say" th:each="news:${news}"><p><img src="https://static.fangxiaoer.com/web/images/sy/house/say/say_top.gif" alt="说说边框"></p>
                <div class="say_txt" th:text="${houseInfo.projectName + '，' + news.description}"></div>
                <div class="news_x"><i><a th:href="${'/news/' + news.id + '.htm'}" target="_blank">详细信息>></a></i></div>

                <p><img src="https://static.fangxiaoer.com/web/images/sy/house/say/say_bottom.gif" alt="说说边框"></p>
            </div>
            <div class="ico_load"></div>
        </div>
        <div class="con_right">
            <!--dianping end-->
        </div>
    </div>
    <div class="cl"></div>
    <div th:include="fragment/page :: page"></div>
    <div class="cl"></div>
    <div th:include="fragment/fragment:: footer_detail"></div>
</form>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::unuseCode"  ></div>
<div  th:include="house/detail/fragment_order::guideMessage"  ></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode"  ></div>
<div class="modal-backdrop  in" id="loginzhezhao" style="display: none;"></div>
</body>
</html>