<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="request">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/formulate/jquery.datetimepicker.css" />
    <script src="https://static.fangxiaoer.com/js/jquery.datetimepicker.js"></script>

    <style type="text/css">
        .fxe_background {
            width:100%;
            height: 100%;
            background: url(https://static.fangxiaoer.com/web/images/ico/sign/b60.png);
            position: fixed;
            top: 0;
            left: 0;
            z-index: 2000;
            display:none;
        }
        .fxe_background div {
            background:#fff;
            color:#000;
            width: 420px;
            padding: 0 0 20px 0;
            border-radius: 5px;
            position:absolute;
            top:50%;
            left:50%;
            margin-top:-80px;
            margin-left:-210px;
        }
        .fxe_background a {
            width: 250px;
            background: #F60;
            display: inline-block;
            text-align: center;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-size: 16px;
            margin-left:50px;
            margin-left: 76px;
            cursor:pointer;
            text-decoration:none;
            border:1px solid #F60;
        }
        .fxe_background a:hover {
            background:#fff;
            color:#f60;
        }
        .fxe_background p {
            font-size: 16px;
            text-align: center;
            padding-top: 40px;
            padding-bottom: 10px;
        }
        .fxe_background span {
            display: block;
            text-align: center;
            padding-bottom: 30px;
            color: #bbb;
            font-size:16px;
        }
    </style>
    <div class="tcbg2" style="opacity: 0.7; background-color: #000"></div>
    <div class="main2" id="lc1">
        <div class="cha">
            <img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="关闭" />
        </div>
        <div class="title2">
            <p th:text="${houseInfo.projectName}"></p>
        </div>
        <div class="info2">填写您的联系电话，以便工作人员为您提供看房服务</div>
        <div class="tbForm">
            <ul>
                <li>
                    <div class='my_xl xx' style="z-index: 2000">
                        <input name="SaleType" type="hidden" id="SaleType" tabindex="-1" class="my_xl_input" />
                        <div class='my_xl_txt' id="kpaddress">请选择上车地点</div>
                        <div class='my_xl_btn'></div>
                        <ul class='my_xl_list ad'>
                            <li value="1">三台子地铁B出口</li>
                            <li value="2">奥体中心地铁A出口</li>
                            <li value="3">铁西广场苏宁电器门前</li>
                        </ul>
                    </div>

                </li>
                <li>
                    <div class="timename" style="z-index:1000">请选择上车时间</div>
                    <input name="kf_sj" type="text" value="" class="hf_input bgjiantou" id="datetimepicker_mask" />

                </li>
                <li>
                    <label>
                        <input class="txt_reg inputStyle" type="text" maxlength="11" name="tel_newh" id="phone2" placeholder="请输入手机号" value="" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')" /></label>

                </li>

                <li>
                    <label>
                        <input name="yzm" id="yzm3" type="text" class="inputStyle widthMd" placeholder="请输入验证码" value="" /></label>
                    <p class="yz">
                        <span class="yzm_btn3" id="Span1" sytle="display:none;"></span>
                        <a class="yzm_btn3" id="xf_hqyzm2">获取验证码</a>
                        <i id="I1" class="ljz" style="display: none;"></i>
                    </p>

                </li>
            </ul>
        </div>
        <div class="cl"></div>
        <div style="overflow: hidden;height: 30px;">
            <div class="error eaddress">请选择上车地点</div>
            <div class="error etime">请选择上车时间</div>
            <div class="error emobile">请输入正确手机号</div>
            <div class="error eyz">请输入正确验证码</div>
        </div>
        <div class="goufangxieyi">
            <span class="cb checked" checked="checked"><img src="https://static.fangxiaoer.com/web/images/sy/house/FreefoneSucceed.png"></span>我已阅读并接受<a href="https://info.fangxiaoer.com/About/protocol" target="_blank"><font color="#ff5200">《房小二网用户服务协议》</font></a>

        </div>
        <a class="btn " onclick="submitexp();return false;" style="cursor: pointer">预约看房</a>

    </div>

    <div class="fxe_background fxe_background1">
        <div>
            <p>预约成功!</p>
            <span></span>
            <a onclick="return false;">确定</a>
        </div>
    </div>
    <script>
        $(".fxe_background a").click(function () {
            $(".fxe_background1").hide()
        })
    </script>
    <div class="fxe_background fxe_background4">
        <div>
            <p>系统繁忙！请稍后重试。</p>
            <span></span>
            <a onclick="return false;">确定</a>
        </div>
    </div>
    <div class="modal-backdrop  in" id="loginzhezhao" style="display: none;"></div>
    <script>
        $(".fxe_background a").click(function () {
            $(".fxe_background4").hide()
        })
    </script>

    <script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
    <script type="text/javascript">
        //预约看房表单验证
$(".cb").click(function () {
    var cb = $(".cb").attr("checked");
    if (cb != "checked") {
        $(".cb").attr("checked","checked")
        $(".cb").addClass("checked")
    }else {
        $(".cb").removeAttr("checked")
        $(".cb").removeClass("checked")
    }
})
        var bs = 1;
        function submitexp() {
            var a11 = 0; var a12 = 0; var a13 = 0; var a14 = 0; var a15 = 0;
            var address = $("#kpaddress").text();
            if (address == "请选择上车地点") { $(".eaddress").fadeIn(); }
            else { $(".eaddress").fadeOut(); a11 = 1; }
            var timevalue = $("#datetimepicker_mask").val();
            if (timevalue == "") { $(".etime").fadeIn(); }
            else { $(".etime").fadeOut(); a12 = 1; }
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            var inputmobile = $.trim($("#phone2").val());
            if (inputmobile == "" || (!reg.test(inputmobile)))
            { $(".emobile").fadeIn(); }
            else { $(".emobile").fadeOut(); a13 = 1; }
            var inputyz = $("#yzm3").val();
            if (inputyz == "") { $(".eyz").fadeIn(); }
            else {
                $.ajax({
                    type: "POST",
                    data: { mobile: inputmobile, code: inputyz },
                    url: "https://ltapi.fangxiaoer.com/apiv1/base/verifySmsCode",
                    async: false,
                    success: function (data) {
                        if (data.status == "1") {
                            a14 = 1
                        }
                        else {
                            $(".eyz").fadeIn();
                        }
                    }
                });


            }
            var cb = $(".cb").attr("checked");
            if (cb != "checked") { alert("您未勾选同意选项"); }
            else { a15 = 1; }


            if (a11 == 1 && a12 == 1 && a13 == 1 && a14 == 1 && a15 == 1) {
                var nameztc = $(".pro_name p").text();
                var kfms = "项目详细页";
                var phoneztc = $.trim($("#phone2").val());
                var carztc = $("#kpaddress").text();
                var timeztc = $("#datetimepicker_mask").val();
                var params = {phone:phoneztc,code:inputyz, area: nameztc,region:carztc,buyTime:timeztc, italy:kfms, type:4}
                fangxiaoer.ajax("upguide", params, function (data) {
                    if (data.status == "1") {
                        $("#SaleType").val("");
                        $("#kpaddress").text("请选择上车地点");
                        $("#datetimepicker_mask").val("");
                        $("#phone2").val("");
                        $("#yzm3").val("");
                        $(".timename").show();
                        $("#lc1").hide();
                        $(".tcbg2").hide();
                        $(".fxe_background1").show();
                        time2();
                        wait = 0;

                    }
                });
            }
        }
        $(".cb").click(function () {
            var cb = $(this).attr("checked");
            if (cb == "checked") { $(".btn").css("background", "#ff6600"); }
            else { $(".btn").css("background", "#666"); }
        });
        $("#datetimepicker_mask").blur(function () {
            var timevalue = $(this).val();
            if (timevalue != "") { $(".etime").fadeOut(); }
        });
        $(".tbForm #phone2").blur(function () {
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            var inputmobile = $.trim($(".tbForm #phone2").val());
            if (inputmobile != "" && (reg.test(inputmobile)))
            { $(".emobile").fadeOut(); }
        });
        $("#yzm3").blur(function () {
            var inputyz = $("#yzm3").val();
            if (inputyz != "") { $(".eyz").fadeOut(); }
        });
        $(".cha").click(function () { $(".main2").hide(); $(".tcbg2").hide(); });

        $(".my_xl_list li").click(function () {
            $(".my_xl_txt").css("color", "#666");
            var address = $(".my_xl_txt").text();
            if (address != "请选择上车时间") { $(".eaddress").fadeOut(); }
        });
        $("#datetimepicker_mask").focus(function () {
            $(".timename").hide();
        });
        //获取验证码按钮事件
        $("#xf_hqyzm2").click(function () {
            var tmobile = $.trim($("#phone2").val());
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            if (!reg.test(tmobile) || tmobile == "") {
                $(".phone2").show()
                $(".emobile").fadeIn();
                return false;
            } else {
                var r = ajaxSendCode(tmobile).status; //发送短信验证码
                if (r == 0) {
                    $("#SaleType").val("");
                    $("#kpaddress").text("请选择上车地点");
                    $("#datetimepicker_mask").val("");
                    $("#phone2").val("");
                    $("#yzm3").val("");
                    $(".timename").show();
                    $("#lc1").hide();
                    $(".tcbg2").hide();
                    $(".fxe_background4").show();
                    time2();
                } else {
                    $(".eyz").fadeOut();
                    time();
                }
            }
        })


        //发送验证码
        function ajaxSendCode(mobile) {
                try {
                    var r = 0;
                    $.ajax({
                        type: "POST",
                        data: {mobile: mobile },
                        url: "/sendSmsCode",
                        async: false,
                        success: function (data) {
                            r = data;
                        }
                    });
                } catch (e) {
                    console.log(e.message);
                }
                return r;
        };


        //验证码倒计时
        var wait = 60;
        function time() {
            if (wait == 0) {
                $(".yzm_btn3").show().html("重新获取");
                $("#Span1").hide();
                wait = 60;
            } else {
                $(".yzm_btn3").hide();
                $(".yzm_btn3").prev().show().html("在" + wait + "秒后重发");
                wait--;
                setTimeout(function () {
                    time();
                }, 1000);
            }
        }
        var wait2 = 3;
        function time2() {
            if (wait2 < 0) {
                $(".fxe_background").hide();
                $(".fxe_background").find("b").html("");
                wait2 = 3;
            } else {
                $(".fxe_background").find("span").html("将在" + wait2 + "秒后自动关闭");
                wait2--;
                setTimeout(function () {
                    time2();
                }, 1000);
            }
        }
        var btn_1=$(".title2 p").text()
        $(".btn_1").click(function () {
            $(".title2 p").text(btn_1)
            $(".main2").fadeIn();
            $(".tcbg2").fadeIn();
            $("#xf_hqyzm2").html("获取验证码");
        });

    </script>
    <script>
        $('#datetimepicker_mask').datetimepicker({
            //    mask: '9999/19/39 29:59'
        });
    </script>
</div>
</body>
</html>