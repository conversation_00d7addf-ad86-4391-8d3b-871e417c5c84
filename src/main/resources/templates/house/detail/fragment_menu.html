<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<!--jdlk-->
<body>
<div th:fragment="menu">
    <div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a th:if="${projectType !='2'}" href="/houses/">沈阳新房</a><a th:if="${projectType=='2'}" href="/houses5/">沈阳别墅</a> &gt;
        <a th:href="${'/houses/r' + houseInfo.regionID}" th:text="${houseInfo.regionName + '新房'}"></a> &gt; <a th:href="${'/house/'+projectId+ '-' + projectType + '.htm'}" th:text="${houseInfo.projectName}"></a></div>
    <a th:if="${houseInfo.advise ne null}" th:href="${#strings.toString(houseInfo.advise.adUrl).indexOf('http') ne -1 ? houseInfo.advise.adUrl : 'javascript:void(0);'}" style="display:block;width: 1170px;height: 130px;margin: 0 auto;margin-bottom: 20px;">
        <img th:src="${houseInfo.advise.adPic}" alt="" style="width:100%;height:100%;">
    </a>
    <div class="w newHouseViewpro_name pro_name" style="border: 0px">
        <div id="qrcode">
            <div class="layer_wei">
                <h1>手机看房</h1>
                <h2>更方便</h2>
            </div>
        </div>
        <div class="box_sun">
            <p th:text="${houseInfo.projectName}"></p><span th:class="${'type_sun wait_'+houseInfo.projectStatus}" th:text="${#strings.isEmpty(houseInfo.projectValue)?'在售': houseInfo.projectValue}"></span>
            <div class="cl"></div>
            <a th:if="${houseInfo.pzid} != '0'" id="type1" th:href="'/house/' + ${houseInfo.pzid} + '-1.htm'">普宅</a>
            <a th:if="${houseInfo.yfid} != '0'" id="type1" th:href="'/house/' + ${houseInfo.yfid} + '-1.htm'">洋房</a>
            <a th:if="${houseInfo.spid} != '0'" id="type1" th:href="'/house/' + ${houseInfo.spid} + '-1.htm'">商铺</a>
            <a th:if="${houseInfo.xzjid} != '0'" id="type3" th:href="'/house/' + ${houseInfo.xzjid} + '-3.htm'">写字间</a>
            <a th:if="${houseInfo.gyid} != '0'" id="type3" th:href="'/house/' + ${houseInfo.gyid} + '-3.htm'">公寓</a>
            <a th:if="${houseInfo.bsid} != '0'" id="type2" th:href="'/house/' + ${houseInfo.bsid} + '-2.htm'">别墅</a>
        </div>
        <img th:if="${houseInfo.vip} == '1' and ${type} == 1" th:src="${houseInfo.logo}">
        <span class="s_time" th:if="${projectType!='2' and projectType!='3'}" th:text="${houseInfo.upTime +'更新'}"></span>
        <span class="error_corrtion">纠错</span>
        <div class="error_tion">
            <div class="Corrected">
                <div class="tlt">
                    <p><span th:text="${houseInfo.projectName}"></span>信息纠错</p>
                    <div class="shut_down"></div>
                </div>
                <div class="error_conut">
                    <dl>
                        <dd>
                            <span>参考价格：</span>
                            <input class="seasoning_name" type="text" id="reference_price" maxlength="30">
                        </dd>
                        <dd>
                            <span>开盘时间：</span>
                            <input class="seasoning_name" type="text" id="opening_time" maxlength="30">
                        </dd>
                        <dd>
                            <span>项目地址：</span>
                            <input class="seasoning_name" type="text" id="project_address" maxlength="30">
                        </dd>
                        <dd>
                            <span>交通信息：</span>
                            <input class="seasoning_name" type="text" id="traffic_information" maxlength="30">
                        </dd>
                        <dd>
                            <span>户型信息：</span>
                            <input class="seasoning_name" type="text" id="apartment_information" maxlength="30">
                        </dd>
                        <dd>
                            <span>售楼电话：</span>
                            <input class="seasoning_name" type="text" id="sales_call" maxlength="30">
                        </dd>
                        <dd>
                            <span>其&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;他：</span>
                            <input class="seasoning_name" type="text" id="other" maxlength="30">
                        </dd>
                        <dd>
                            <span>我的姓名：</span>
                            <input class="seasoning_name" type="text" id="my_name" maxlength="30">
                        </dd>
                    </dl>
                </div>
                <div class="error_di">
                    <input type="button" id="error_submission" value="提交">
                    <input type="button" id="error_cancel" value="取消">
                </div>
            </div>
        </div>
    </div>
    <script>
        var projectId = [[${projectId}]]
        var projectType = [[${projectType}]]

        $(".error_corrtion").click(function () {
            $(".error_tion").show();
            var errname = $(".Corrected .tlt p").width();
            if ( errname > 266){
                $(".Corrected .tlt span").addClass("evspan");
            }
        })
        $(".shut_down,#error_cancel").click(function () {
            $(".error_tion").hide();
            $("#reference_price").val("");
            $("#opening_time").val("");
            $("#project_address").val("");
            $("#traffic_information").val("");
            $("#apartment_information").val("");
            $("#sales_call").val("");
            $("#other").val("");
            $("#my_name").val("");
        })
        $("#error_submission").click(function () {
            var sessionId = $("#sessionId").val();
            var reference_price = $("#reference_price").val();
            var opening_time = $("#opening_time").val();
            var project_address = $("#project_address").val();
            var traffic_information = $("#traffic_information").val();
            var apartment_information = $("#apartment_information").val();
            var sales_call = $("#sales_call").val();
            var other = $("#other").val();
            var my_name = $("#my_name").val();
            var seasoning_name = "";
            $('.seasoning_name').each(function(){
                seasoning_name += $(this).val();
            })
            if(seasoning_name == ""){
                alert("至少填写一项！");
                return false;
            }else {
                $.ajax({
                    type: "POST",
                    data: {
                        projectId:projectId,
                        sessionId : sessionId,
                        memberName : my_name,
                        price : reference_price,
                        panTime : opening_time,
                        projectAddress : project_address,
                        trafficInfo : traffic_information,
                        layoutInfo : apartment_information,
                        sortTel : sales_call,
                        otherInfo : other
                    },
                    url: "/saveCollection",
                    success: function (data) {
                        if(data.status == 1){
                            alert("提交成功");
                            $("#reference_price").val("");
                            $("#opening_time").val("");
                            $("#project_address").val("");
                            $("#traffic_information").val("");
                            $("#apartment_information").val("");
                            $("#sales_call").val("");
                            $("#other").val("");
                            $("#my_name").val("");
                            $(".error_tion").hide()
                        }else{
                            // alert('提交失败');
                            alert(data.msg)
                        }
                    }
                });
            }
        })
    </script>
    <!--分栏-->
    <script src="https://static.fangxiaoer.com/js/scroll.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/house/menu.js" type="text/javascript"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newHousefloatNav.css?v=20180921" rel="stylesheet" type="text/css" />
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/indexBB.css?v=20180522" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/fragment_menu.css?v=20190328" />
    <!--vip页的头部滚动在组件也在此页中，与普宅页的结构类名 id名相同 具体位置请搜索-->
    <div  class="w nav_house" th:if="${projectType!='2'}">

        <input type="hidden" id="glzs" th:value="${session.sessionId}" />
        <div class="house_move_act">
            <ul>
                <p style="display:none">新房</p>
                <li  th:class="${type} == 1?'hover':''"><a th:href="${'/house/'+projectId+ '-' + projectType + '.htm'}">
                    楼盘首页</a></li>
                <li th:class="${type} == 2?'hover':''"><a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}">
                    楼盘详情</a></li>

                <li th:class="${type} == 3?'hover':''" th:if="${houseInfo.layout ne null and  !#maps.isEmpty(houseInfo.layout) }"><a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '.htm'}">
                    户型</a></li>
                <!--<li th:if="${houseInfo.isOnline ne '0'}" th:class="${type} == 4?'hover':''"><a th:href="${'/house/onlines/pid'+projectId + '-pt' + projectType}">-->
                <!--在线选房</a></li>-->

                <li th:class="${type} == 5?'hover':''"><a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
                    相册</a></li>

                <li th:if="${houseInfo.isPan ne '0'}" th:class="${type} == 6?'hover':''"><a th:href="${'/pic720/'+projectId + '-' + projectType +'.htm'}">
                    全景看房</a></li>

                <li th:if="${houseInfo.videoId}" th:class="${type} == 7?'hover':''"><a  th:href="${'/video/' + houseInfo.videoId+'-'+projectId+ '.htm'}"  target="_blank">
                    视频</a></li>
                <li th:if="${houseInfo.isJingzhuang ne '0'}" th:class="${type} == 12?'hover':''"><a th:href="${'/decoration/'+projectId + '-' + projectType +  '.htm'}">
                    精装房</a></li>
                <li th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:class="${type} == 14?'hover':''"><a th:href="${'/house/appraise/'+projectId + '-' + projectType+ '.htm'}">用户点评</a></li>
                <li th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:class="${type} == 16?'hover':''"><a th:href="${'/house/ask/'+projectId + '-' + projectType+ '.htm'}">楼盘问答</a></li>
                <th:block th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
                    <li th:if="${#strings.toString(houseInfo.haveDynamic) eq '1'}" th:class="${type} == 18?'hover':''"><a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm'}">楼盘动态</a></li>
                </th:block>
                <li th:if="${houseInfo.isSay ne '0'}" th:class="${type} == 8?'hover':''"><a th:href="${'/house/say/'+projectId + '-' + projectType + '.htm'}">
                    楼盘说说</a></li>
            </ul>
        </div>
    </div>

    <!--页面滚动时出现的头部定位-->
    <div class="bkNavFloat fixbkb" th:if="${projectType ne '2'}">
        <div class="container">
            <div class="unstyled" style="width: 1170px;margin: 0 auto;">
                <div class="floatNavL">
                    <div id="qrcodes" class="rolling">
                        <div class="layer_wei">
                            <h1>手机看房</h1>
                            <h2>更方便</h2>
                        </div>
                    </div>
                    <div class="wei">
                        <h4 th:text="${houseInfo.projectName}"></h4>
                        <span th:class="${'type_sun wait_'+houseInfo.projectStatus}" th:text="${#strings.isEmpty(houseInfo.projectValue)?'在售': houseInfo.projectValue}"></span>
                        <div class="cl"></div>
                        <div class="Price_sun" th:if="${houseInfo.projectStatus ne '3'}">
                            <span>参考价格：</span>
                            <th:block th:if="${projectType == '1'}">
                                <p th:if="${houseInfo.price} == null or ${#arrays.length(houseInfo.price)} == 0"  class="daiding">待定</p>
                                <p th:each="price,i:${houseInfo.price}" th:if="${i.index lt 3}">
                                    <!--<th:block th:text="${price.name}" ></th:block>-->
                                    <th:block th:text="${price.name+price.type}"></th:block><i th:text="${#numbers.formatInteger( price.money,3)}"></i>元/m²
                                    <!-- <img th:src="${price.type eq '均价'}?@{https://static.fangxiaoer.com/web/images/ico/sign/price_2.gif}:@{https://static.fangxiaoer.com/web/images/ico/sign/price_1.gif}" alt="价格">-->
                                </p>
                            </th:block>
                            <!--projectType为公寓、写字楼时参考价格显示-->
                            <th:block th:if="${projectType == '3'}">
                                <p th:if="${houseInfo.priceList} == null or ${#arrays.length(houseInfo.priceList)} == 0" class="daiding">待定</p>
                                <p th:each="p,i:${houseInfo.priceList}" th:if="${i.index lt 3}">
                                    <th:block th:text="${p.name+p.type}"></th:block><i th:text="${#numbers.formatInteger( p.money,3)}"></i>元/m²
                                </p>
                            </th:block>
                        </div>
                    </div>
                </div>
                <div class="floatNavR" th:if="${houseInfo.projectStatus ne '3'}">

<!--                    <a  onclick="showUsercode(2)" class="houseRightBtn" style="cursor:pointer ; display: block;margin-top: 5px">免费通话</a>-->
                    <p>免费咨询售楼处:<span><th:block th:utext="${#strings.toString(houseInfo.sortelTel).replace('转','<b>转</b>')}"></th:block></span></p>

                </div>
            </div>
            <div class="house_move_actFloat">
                <ul>
                    <!-- <p style="display:none">新房</p>-->
                    <li  th:class="${type} == 1?'hover':''"><a th:href="${'/house/'+projectId+ '-' + projectType + '.htm'}">
                        楼盘首页</a></li>
                    <li th:class="${type} == 2?'hover':''"><a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}">
                        楼盘详情</a></li>

                    <li th:class="${type} == 3?'hover':''" th:if="${houseInfo.layout ne null and  !#maps.isEmpty(houseInfo.layout) }"><a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '.htm'}">
                        户型</a></li>

                    <li th:class="${type} == 5?'hover':''"><a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
                        相册</a></li>

                    <li th:if="${houseInfo.isPan ne '0'}" th:class="${type} == 6?'hover':''"><a th:href="${'/pic720/'+projectId + '-' + projectType +'.htm'}">
                        全景看房</a></li>

                    <li th:if="${houseInfo.videoId}" th:class="${type} == 7?'hover':''"><a th:href="${'/video/' + houseInfo.videoId+'-'+projectId+ '.htm'}" target="_blank">
                        视频</a></li>

                    <li th:if="${houseInfo.isJingzhuang ne '0'}" th:class="${type} == 12?'hover':''"><a th:href="${'/decoration/'+projectId + '-' + projectType +  '.htm'}">
                        精装房</a></li>

                    <li th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:class="${type} == 14?'hover':''"><a th:href="${'/house/appraise/'+projectId + '-' + projectType+ '.htm'}">用户点评</a></li>
                    <li th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:class="${type} == 16?'hover':''"><a th:href="${'/house/ask/'+projectId + '-' + projectType+ '.htm'}">楼盘问答</a></li>
                    <!--<li  th:if="${!#lists.isEmpty(saleInfo) || !#lists.isEmpty(saleHouse)}" th:class="${type} == 18?'hover':''"><a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm'}">销售动态</a></li>-->
                    <th:block th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
                        <li th:if="${#strings.toString(houseInfo.haveDynamic) eq '1'}" th:class="${type} == 18?'hover':''"><a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm'}">楼盘动态</a></li>
                    </th:block>
                    <li th:if="${houseInfo.isSay ne '0'}" th:class="${type} == 8?'hover':''"><a th:href="${'/house/say/'+projectId + '-' + projectType + '.htm'}">
                        楼盘说说</a></li>
                </ul>
            </div>
        </div>



    </div>
    <!--下拉漂浮-->
    <script>
        $(document).ready(function() {
            $(".bkNavFloat").hide()
            $(".bkNavFloat").removeClass("fixbkb");
            $(window).scroll(function() {
                var s = $(this).scrollTop();
                if (s >= 500) {
                    $(".bkNavFloat").show().addClass("fixbkb");
                } else {
                    $(".bkNavFloat").removeClass("fixbkb");
                    $(".bkNavFloat").hide()
                }

            })
        });



        function classon(index) {
            var offsetTop = $("#h" + index).offset().top;
            var scrollTop = $(document).scrollTop();
            var w_height = $(window).height();
            var load_height = $("#h" + index).height();

            //if (scrollTop > offsetTop - w_height && scrollTop < offsetTop + load_height) {
            if (scrollTop > offsetTop - 50) {
                $(".bkNavFloat").find("li").removeClass("on");
                $(".bkNavFloat").find("li:eq(" + index + ")").addClass("on");
            }
        }
        $(".bkNavFloat").find("li").click(function(e) {
            $(".bkNavFloat").find("li").removeClass("on");
            $(this).addClass("on");
        })
        //$(".bkNavFloat a").click(function (e) {
        //    e.preventDefault();
        //    $('html,body').scrollTo(this.hash, this.hash);
        //});


    </script>
    <!--项目二维码-->
    <script>
        $(function () {
            $('#qrcode').qrcode(
                {
                    width: 60,
                    height: 60,
                    text: "https://m.fangxiaoer.com/fang1/"+[[${projectId}]] + "-" + [[${projectType}]] + ".htm"
                });
            $('#qrcode1').qrcode(
                {
                    width: 60,
                    height: 60,
                    text: "https://m.fangxiaoer.com/fang1/"+[[${projectId}]] + "-" + [[${projectType}]] + ".htm"
                });
        });
    </script>
    <!--项目二维码-->
    <script>
        $(function () {
            $('#qrcodes').qrcode(
                {
                    width: 60,
                    height: 60,
                    text: "https://m.fangxiaoer.com/fang1/"+[[${projectId}]] + "-" + [[${projectType}]] + ".htm"
                });
        });
    </script>
</div>
<div th:fragment="freeCall">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/housesRight.css?v=20190104"/>
    <div class="housesRight" th:if="${houseInfo.projectStatus ne '3'}">
        <div class="salesOffice houseRight">
            <p style="margin-top: 46px !important;">售楼处电话：</p>
            <span><b><th:block th:utext="${#strings.toString(houseInfo.sortelTel).replace('转','<b>转</b>')}"></th:block></b></span>
            <!-- <span><b><th:block th:text="${houseInfo.sortelTel}"></th:block></b></span>-->
<!--            <a  onclick="showUsercode(2)" class="houseRightBtn" style="cursor:pointer ; display: block;margin-top: 5px">免费通话</a>-->
        </div>
<!--        <div class="houseRight subscription">-->
<!--            <div class="houseRinghtTitle">楼盘订阅</div>-->
<!--            <ul>-->
<!--                <li rel="1"><b></b><span>最新动态</span></li>-->
<!--                <li rel="2"><b></b><span>优惠通知</span></li>-->
<!--                <li rel="3"><b></b><span>开盘通知</span></li>-->
<!--                <li rel="4"><b></b><span>降价通知</span></li>-->
<!--                <li rel="5"><b></b><span>看房团通知</span></li>-->
<!--            </ul>-->
<!--            <a onclick="showUnuseCode(2)" class="houseRightBtn" data_i="" style="cursor:pointer ; display: block;margin-top: 5px">立即订阅</a>-->
<!--            -->
<!--        </div>-->
    </div>
    <script type="text/javascript">
        // $(".housesRight .subscriber ul li").hover(function(){
        //     $(".housesRight .subscriber ul li").removeClass("hover")
        //     $(this).addClass("hover")
        // })
        // $(".housesRight .subscription ul li").click(function(){
        //     if($(this).hasClass("hover")){
        //         $(this).removeClass("hover")
        //     }else{
        //         $(this).addClass("hover")
        //     }
        //     var txt = ""
        //     $(".housesRight .subscription ul li").each(function(){
        //         if($(this).hasClass("hover")){
        //             txt+=$(this).attr("rel")+","
        //         }
        //     })
        //     console.log(txt)
        //     //修改bug15898
        //      $(".houseRightBtn").attr("data_i",txt)
        // })

    </script>
</div>
<div th:fragment="menuVipUp">
    <div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a th:if="${projectType !='2'}" href="/houses/">沈阳新房</a><a th:if="${projectType=='2'}" href="/houses5/">沈阳别墅</a> &gt;
        <a th:href="${'/houses/r' + houseInfo.regionID}" th:text="${houseInfo.regionName}"></a> &gt; <a th:href="${'/house/'+projectId+ '-' + projectType + '.htm'}" th:text="${houseInfo.projectName}"></a></div>
    <a th:if="${houseInfo.advise ne null}" th:href="${#strings.toString(houseInfo.advise.adUrl).indexOf('http') ne -1 ? houseInfo.advise.adUrl : 'javascript:void(0);'}" style="display:block;width: 1170px;height: 130px;margin: 0 auto;margin-bottom: 20px;">
        <img th:src="${houseInfo.advise.adPic}" alt="" style="width:100%;height:100%;">
    </a>
    <div class="w newHouseViewpro_name pro_name" style="border: 0px">
        <div id="qrcode">
            <div class="layer_wei">
                <h1>手机看房</h1>
                <h2>更方便</h2>
            </div>
        </div>
        <div class="box_sun">
            <p th:text="${houseInfo.projectName}"></p><span th:class="${'type_sun wait_'+houseInfo.projectStatus}" th:text="${#strings.isEmpty(houseInfo.projectValue)?'在售': houseInfo.projectValue}"></span>
            <div class="cl"></div>
            <a th:if="${houseInfo.pzid} != '0'" id="type1" th:href="'/house/' + ${houseInfo.pzid} + '-1.htm'">普宅</a>
            <a th:if="${houseInfo.yfid} != '0'" id="type1" th:href="'/house/' + ${houseInfo.yfid} + '-1.htm'">洋房</a>
            <a th:if="${houseInfo.spid} != '0'" id="type1" th:href="'/house/' + ${houseInfo.spid} + '-1.htm'">商铺</a>
            <a th:if="${houseInfo.xzjid} != '0'" id="type3" th:href="'/house/' + ${houseInfo.xzjid} + '-3.htm'">写字间</a>
            <a th:if="${houseInfo.gyid} != '0'" id="type3" th:href="'/house/' + ${houseInfo.gyid} + '-3.htm'">公寓</a>
            <a th:if="${houseInfo.bsid} != '0'" id="type2" th:href="'/house/' + ${houseInfo.bsid} + '-2.htm'">别墅</a>
        </div>
        <img th:if="${houseInfo.vip} == '1' and ${type} == 1" th:src="${houseInfo.logo}">
        <span class="s_time" th:if="${projectType!='2' and projectType!='3'}" th:text="${houseInfo.upTime +'更新'}"></span>
        <span class="error_corrtion">纠错</span>
        <div class="error_tion">
            <div class="Corrected">
                <div class="tlt">
                    <p><span th:text="${houseInfo.projectName}"></span>信息纠错</p>
                    <div class="shut_down"></div>
                </div>
                <div class="error_conut">
                    <dl>
                        <dd>
                            <span>参考价格：</span>
                            <input class="seasoning_name" type="text" id="reference_price" maxlength="30">
                        </dd>
                        <dd>
                            <span>开盘时间：</span>
                            <input class="seasoning_name" type="text" id="opening_time" maxlength="30">
                        </dd>
                        <dd>
                            <span>项目地址：</span>
                            <input class="seasoning_name" type="text" id="project_address" maxlength="30">
                        </dd>
                        <dd>
                            <span>交通信息：</span>
                            <input class="seasoning_name" type="text" id="traffic_information" maxlength="30">
                        </dd>
                        <dd>
                            <span>户型信息：</span>
                            <input class="seasoning_name" type="text" id="apartment_information" maxlength="30">
                        </dd>
                        <dd>
                            <span>售楼电话：</span>
                            <input class="seasoning_name" type="text" id="sales_call" maxlength="30">
                        </dd>
                        <dd>
                            <span>其&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;他：</span>
                            <input class="seasoning_name" type="text" id="other" maxlength="30">
                        </dd>
                        <dd>
                            <span>我的姓名：</span>
                            <input class="seasoning_name" type="text" id="my_name" maxlength="30">
                        </dd>
                    </dl>
                </div>
                <div class="error_di">
                    <input type="button" id="error_submission" value="提交">
                    <input type="button" id="error_cancel" value="取消">
                </div>
            </div>
        </div>
    </div>
    <script>
        var projectId = [[${projectId}]]
        var projectType = [[${projectType}]]

        $(".error_corrtion").click(function () {
            $(".error_tion").show();
            var errname = $(".Corrected .tlt p").width();
            if ( errname > 266){
                $(".Corrected .tlt p span").addClass("evspan");
            }
        })
        $(".shut_down,#error_cancel").click(function () {
            $(".error_tion").hide();
            $("#reference_price").val("");
            $("#opening_time").val("");
            $("#project_address").val("");
            $("#traffic_information").val("");
            $("#apartment_information").val("");
            $("#sales_call").val("");
            $("#other").val("");
            $("#my_name").val("");
        })
        $("#error_submission").click(function () {
            var sessionId = $("#sessionId").val();
            var reference_price = $("#reference_price").val();
            var opening_time = $("#opening_time").val();
            var project_address = $("#project_address").val();
            var traffic_information = $("#traffic_information").val();
            var apartment_information = $("#apartment_information").val();
            var sales_call = $("#sales_call").val();
            var other = $("#other").val();
            var my_name = $("#my_name").val();
            var seasoning_name = "";
            $('.seasoning_name').each(function(){
                seasoning_name += $(this).val();
            })
            if(seasoning_name == ""){
                alert("至少填写一项！");
                return false;
            }else {
                $.ajax({
                    type: "POST",
                    data: {
                        projectId:projectId,
                        sessionId : sessionId,
                        memberName : my_name,
                        price : reference_price,
                        panTime : opening_time,
                        projectAddress : project_address,
                        trafficInfo : traffic_information,
                        layoutInfo : apartment_information,
                        sortTel : sales_call,
                        otherInfo : other
                    },
                    url: "/saveCollection",
                    success: function (data) {
                        if(data.status == 1){
                            alert("提交成功");
                            $("#reference_price").val("");
                            $("#opening_time").val("");
                            $("#project_address").val("");
                            $("#traffic_information").val("");
                            $("#apartment_information").val("");
                            $("#sales_call").val("");
                            $("#other").val("");
                            $("#my_name").val("");
                            $(".error_tion").hide()
                        }else{
                            // alert('提交失败');
                            alert(data.msg)
                        }
                    }
                });
            }
        })
    </script>
    <!--分栏-->
    <script src="https://static.fangxiaoer.com/js/scroll.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/house/menu.js" type="text/javascript"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newHousefloatNav.css?v=20180921" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css" />
    <!--<link rel="stylesheet" type="text/css" href="css/housesParticulars.css"/>-->
    <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/housesParticulars.css"/>-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/fragment_menu.css?v=20190328" />

    <input type="hidden" id="glzs" th:value="${session.sessionId}" />
    <!--页面滚动时出现的头部定位-->
    <div class="bkNavFloat fixbkb">
        <div class="container">
            <div class="unstyled" style="width: 1170px;margin: 0 auto;">
                <div class="floatNavL">
                    <div id="qrcodes" class="rolling">
                        <div class="layer_wei">
                            <h1>手机看房</h1>
                            <h2>更方便</h2>
                        </div>
                    </div>
                    <div class="wei">
                        <h4 th:text="${houseInfo.projectName}"></h4>
                        <span th:class="${'type_sun wait_'+houseInfo.projectStatus}" th:text="${#strings.isEmpty(houseInfo.projectValue)?'在售': houseInfo.projectValue}"></span>
                        <div class="cl"></div>
                        <div>
                            <span>参考价格：</span>
                            <th:block th:if="${projectType == '1'}">
                                <p th:if="${houseInfo.price} == null or ${#arrays.length(houseInfo.price)} == 0" class="daiding">待定</p>
                                <p th:each="price,i:${houseInfo.price}" th:if="${i.index lt 3}">
                                    <!--<th:block th:text="${price.name}" ></th:block>-->
                                    <th:block th:text="${price.name+price.type}"></th:block><i th:text="${#numbers.formatInteger( price.money,3)}"></i>元/m²
                                    <!-- <img th:src="${price.type eq '均价'}?@{https://static.fangxiaoer.com/web/images/ico/sign/price_2.gif}:@{https://static.fangxiaoer.com/web/images/ico/sign/price_1.gif}" alt="价格">-->
                                </p>
                            </th:block>
                            <!--projectType为公寓、写字楼时参考价格显示-->
                            <th:block th:if="${projectType == '3'}">
                                <p th:if="${houseInfo.priceList} == null or ${#arrays.length(houseInfo.priceList)} ==0" class="daiding">待定</p>
                                <p th:each="p,i:${houseInfo.priceList}" th:if="${i.index lt 3}">
                                    <th:block th:text="${p.name+p.type}"></th:block><i th:text="${#numbers.formatInteger( p.money,3)}"></i>元/m²
                                </p>
                            </th:block>
                        </div>
                    </div>
                </div>
                <div class="floatNavR">

<!--                    <a  onclick="showUsercode(2)" class="houseRightBtn" style="cursor:pointer ; display: block;margin-top: 5px">免费通话</a>-->
                    <p>免费咨询售楼处:<span><th:block th:utext="${#strings.toString(houseInfo.sortelTel).replace('转','<b>转</b>')}"></th:block></span></p>

                </div>


            </div>
            <div class="house_move_actFloat">
                <ul>
                    <!-- <p style="display:none">新房</p>-->
                    <li  th:class="${type} == 1?'hover':''"><a th:href="${'/house/'+projectId+ '-' + projectType + '.htm'}">
                        楼盘首页</a></li>
                    <li th:class="${type} == 2?'hover':''"><a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}">
                        楼盘详情</a></li>

                    <li th:class="${type} == 3?'hover':''" th:if="${houseInfo.layout ne null and  !#maps.isEmpty(houseInfo.layout) }"><a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '.htm'}">
                        户型</a></li>

                    <li th:class="${type} == 5?'hover':''"><a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
                        相册</a></li>

                    <li th:if="${houseInfo.isPan ne '0'}" th:class="${type} == 6?'hover':''"><a th:href="${'/pic720/'+projectId + '-' + projectType +'.htm'}">
                        全景看房</a></li>

                    <li th:if="${houseInfo.videoId}" th:class="${type} == 7?'hover':''"><a  th:href="${'/video/' + houseInfo.videoId+'-'+projectId+ '.htm'}"  target="_blank">
                        视频</a></li>

                    <li th:if="${houseInfo.isJingzhuang ne '0'}" th:class="${type} == 12?'hover':''"><a th:href="${'/decoration/'+projectId + '-' + projectType +  '.htm'}">
                        精装房</a></li>

                    <li th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:class="${type} == 14?'hover':''"><a th:href="${'/house/appraise/'+projectId + '-' + projectType+ '.htm'}">用户点评</a></li>
                    <li th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:class="${type} == 16?'hover':''"><a th:href="${'/house/ask/'+projectId + '-' + projectType+ '.htm'}">楼盘问答</a></li>
                    <!--<li  th:if="${!#lists.isEmpty(saleInfo) || !#lists.isEmpty(saleHouse)}" th:class="${type} == 18?'hover':''"><a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm'}">销售动态</a></li>-->
                    <th:block th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
                    <li th:if="${#strings.toString(houseInfo.haveDynamic) eq '1'}" th:class="${type} == 18?'hover':''"><a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm'}">楼盘动态</a></li>
                    </th:block>
                    <li th:if="${houseInfo.isSay ne '0'}" th:class="${type} == 8?'hover':''"><a th:href="${'/house/say/'+projectId + '-' + projectType + '.htm'}">
                        楼盘说说</a></li>
                </ul>
            </div>

        </div>



    </div>
    <!--下拉漂浮-->
    <script>
        $(document).ready(function() {
            $(".bkNavFloat").hide()
            $(".bkNavFloat").removeClass("fixbkb");
            $(window).scroll(function() {
                var s = $(this).scrollTop();
                if (s >= 1336) {
                    $(".bkNavFloat").show().addClass("fixbkb");
                } else {
                    $(".bkNavFloat").removeClass("fixbkb");
                    $(".bkNavFloat").hide()
                }

            })
        });



        function classon(index) {
            var offsetTop = $("#h" + index).offset().top;
            var scrollTop = $(document).scrollTop();
            var w_height = $(window).height();
            var load_height = $("#h" + index).height();

            //if (scrollTop > offsetTop - w_height && scrollTop < offsetTop + load_height) {
            if (scrollTop > offsetTop - 50) {
                $(".bkNavFloat").find("li").removeClass("on");
                $(".bkNavFloat").find("li:eq(" + index + ")").addClass("on");
            }
        }
        $(".bkNavFloat").find("li").click(function(e) {
            $(".bkNavFloat").find("li").removeClass("on");
            $(this).addClass("on");
        })
        //$(".bkNavFloat a").click(function (e) {
        //    e.preventDefault();
        //    $('html,body').scrollTo(this.hash, this.hash);
        //});


    </script>
    <!--项目二维码-->
    <script>
        $(function () {
            $('#qrcode').qrcode(
                {
                    width: 60,
                    height: 60,
                    text: "https://m.fangxiaoer.com/fang1/"+[[${projectId}]] + "-" + [[${projectType}]] + ".htm"
                });
        });
    </script>
    <!--项目二维码-->
    <script>
        $(function () {
            $('#qrcodes').qrcode(
                {
                    width: 60,
                    height: 60,
                    text: "https://m.fangxiaoer.com/fang1/"+[[${projectId}]] + "-" + [[${projectType}]] + ".htm"
                });
        });
    </script>
</div>
<div th:fragment="menuVipDown">
    <div class="nav_house" style="width: 100%" >
        <div class="house_move_act">
            <ul>
                <p style="display:none">新房</p>
                <li  th:class="${type} == 1?'hover':''"><a th:href="${'/house/'+projectId+ '-' + projectType + '.htm'}">
                    楼盘首页</a></li>
                <li th:class="${type} == 2?'hover':''"><a th:href="${'/house/info/'+projectId + '-' + projectType + '.htm'}">
                    楼盘详情</a></li>

                <li th:class="${type} == 3?'hover':''" th:if="${houseInfo.layout ne null and  !#maps.isEmpty(houseInfo.layout) }"><a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '.htm'}">
                    户型</a></li>

                <!--<li th:if="${houseInfo.isOnline ne '0'}" th:class="${type} == 4?'hover':''"><a th:href="${'/house/onlines/pid'+projectId + '-pt' + projectType}">-->
                <!--在线选房</a></li>-->

                <li th:class="${type} == 5?'hover':''"><a th:href="${'/house/album/'+projectId + '-' + projectType + '.htm'}">
                    相册</a></li>

                <li th:if="${houseInfo.isPan ne '0'}" th:class="${type} == 6?'hover':''"><a th:href="${'/pic720/'+projectId + '-' + projectType +'.htm'}">
                    全景看房</a></li>

                <li th:if="${houseInfo.videoId}" th:class="${type} == 7?'hover':''"><a  th:href="${'/video/' + houseInfo.videoId+'-'+projectId+ '.htm'}"  target="_blank">
                    视频</a></li>
                <li th:if="${houseInfo.isJingzhuang ne '0'}" th:class="${type} == 12?'hover':''"><a th:href="${'/decoration/'+projectId + '-' + projectType +  '.htm'}">
                    精装房</a></li>
                <li th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:class="${type} == 14?'hover':''"><a th:href="${'/house/appraise/'+projectId + '-' + projectType+'.htm'}">用户点评</a></li>
                <li th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}" th:class="${type} == 16?'hover':''"><a th:href="${'/house/ask/'+projectId + '-' + projectType+ '.htm'}">楼盘问答</a></li>
                <th:block th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
                    <li th:if="${#strings.toString(houseInfo.haveDynamic) eq '1'}" th:class="${type} == 18?'hover':''"><a th:href="${'/house/'+projectId + '-' + projectType + '/news/dy.htm'}">楼盘动态</a></li>
                </th:block>
                <li th:if="${houseInfo.isSay ne '0'}" th:class="${type} == 8?'hover':''"><a th:href="${'/house/say/'+projectId + '-' + projectType + '.htm'}">
                    楼盘说说</a></li>
            </ul>
        </div>
    </div>
    <!--基础资料-->

    <div class="w jczl">
        <div class="con_left">
            <div class="top_wei">
                <ul>
                    <li style="border-top: 0px">
                        <div class="name">
                            <h1>区域板块</h1>
                        </div>
                        <div class="text"  th:text="${#strings.isEmpty(houseInfo.baseInfo.regionPlate) ? '其他': houseInfo.baseInfo.regionPlate}"></div>
                        <div class="cl"></div>
                    </li>
                    <li style="border-top: 0px">
                        <div class="name">
                            <h1>环线类型</h1>
                        </div>
                        <div class="text"  th:text="${#strings.isEmpty(houseInfo.baseInfo.schortType)? '暂无资料' : houseInfo.baseInfo.schortType}"></div>
                        <div class="cl"></div>
                    </li>
                    <li style="border-top: 0px">
                        <div class="name">
                            <h1>所属商圈</h1>
                        </div>
                        <div class="text"   th:text="${#strings.isEmpty(houseInfo.baseInfo.businessCircleName)?  '暂无资料' :houseInfo.baseInfo.businessCircleName}"></div>
                        <div class="cl"></div>
                    </li>
                    <li>
                        <div class="name">
                            <h1>开发商</h1>
                        </div>
                        <div class="text"   th:text="${#strings.isEmpty(houseInfo.baseInfo.kfs)? '暂无资料':houseInfo.baseInfo.kfs}"></div>
                        <div class="cl"></div>
                    </li>
                    <li>
                        <div class="name">
                            <h1>施工单位</h1>
                        </div>
                        <div class="text" th:text="${#strings.isEmpty(houseInfo.baseInfo.sgdw) ?'暂无资料':houseInfo.baseInfo.sgdw}"></div>
                        <div class="cl"></div>
                    </li>
                    <li>
                        <div class="name">
                            <h1>供暖方式</h1>
                        </div>
                        <div class="text"   th:text="${#strings.isEmpty(houseInfo.baseInfo.heatingType) ? '暂无资料': houseInfo.baseInfo.heatingType}"></div>
                        <div class="cl"></div>
                    </li>
                    <li th:if="${!#strings.isEmpty(houseInfo.baseInfo.stationName) and #strings.toString(houseInfo.baseInfo.stationName) ne '暂无资料'}">
                        <div class="name">
                            <h1>地铁站点</h1>
                        </div>
                        <div class="text" th:text="${houseInfo.baseInfo.stationName}"></div>
                        <div class="cl"></div>
                    </li>
                    <li th:if="${houseInfo.baseInfo.tzs ne '' and houseInfo.baseInfo.tzs ne '暂无资料'}">
                        <div class="name">
                            <h1>投资商</h1>
                        </div>
                        <div class="text"  th:text="${houseInfo.baseInfo.tzs}"></div>
                        <div class="cl"></div>
                    </li>
                    <li th:if="${houseInfo.baseInfo.heatingUnits ne '' and houseInfo.baseInfo.heatingUnits ne '暂无资料'}">
                        <div class="name">
                            <h1>供暖单位</h1>
                        </div>
                        <div class="text"  th:text="${houseInfo.baseInfo.heatingUnits}"></div>
                        <div class="cl"></div>
                    </li>
                    <div class="cl"></div>
                </ul>
            </div>
            <div class="bottom_wei">
                <ul>
                    <li style="border-top: 0px">
                        <div class="name">
                            <h1>建筑面积</h1>
                        </div>
                        <div class="text" th:text="${houseInfo.baseInfo.buildingArea eq '0 ㎡' ? '暂无资料':houseInfo.baseInfo.buildingArea}"></div>
                        <div class="cl"></div>
                    </li>
                    <li style="border-top: 0px">
                        <div class="name">
                            <h1>占地面积</h1>
                        </div>
                        <div class="text" th:text="${houseInfo.baseInfo.floorArea eq '0 ㎡' ? '暂无资料':houseInfo.baseInfo.floorArea}"></div>
                        <div class="cl"></div>
                    </li>
                    <li style="border-top: 0px">
                        <div class="name">
                            <h1>容积率</h1>
                        </div>
                        <div class="text" th:text="${#strings.toString(houseInfo.baseInfo.plotRatio) eq '0.0' ? '暂无资料' : houseInfo.baseInfo.plotRatio}"></div>
                        <div class="cl"></div>
                    </li>
                    <li >
                        <div class="name">
                            <h1>绿化率</h1>
                        </div>
                        <div class="text" th:text="${houseInfo.baseInfo.greenery eq '0 %' ? '暂无资料':houseInfo.baseInfo.greenery}"></div>
                        <div class="cl"></div>
                    </li>
                    <li >
                        <div class="name">
                            <h1>物业公司</h1>
                        </div>
                        <div class="text" th:text="${#strings.isEmpty(houseInfo.baseInfo.wygs)? '暂无资料': houseInfo.baseInfo.wygs}"></div>
                        <div class="cl"></div>
                    </li>
                    <li >
                        <div class="name">
                            <h1>车位信息</h1>
                        </div>
                        <div class="text" th:text="${#strings.isEmpty(houseInfo.baseInfo.garageInfo)? '暂无资料':#strings.abbreviate(houseInfo.baseInfo.garageInfo,24)}"></div>
                        <div class="carinfo_pop" style="display: none;" th:if="${!#strings.isEmpty(houseInfo.baseInfo.garageInfo) and #strings.length(houseInfo.baseInfo.garageInfo) gt 24}" >
                            <div class="ms_sanjiao dt_sj"></div>
                            <th:block th:text="${houseInfo.baseInfo.garageInfo}"></th:block>
                        </div>
                        <div class="cl"></div>
                    </li>
                    <li >
                        <div class="name">
                            <h1>物业费</h1>
                        </div>
                        <div class="text" th:text="${#strings.isEmpty(houseInfo.pzInfo.maintenace)? '暂无资料': houseInfo.pzInfo.maintenace}"></div>
                        <div class="cl"></div>
                    </li>
                    <div class="cl"></div>
                </ul>
            </div>

        </div>
        <div class="con_right">
        </div>
    </div>
    <script>
      $(document).ready(
          function () {
              var tx=$(".con_left .top_wei ul li").length
              if($(".con_left .top_wei ul li").length%3==1){
                  $(".con_left .top_wei ul li").eq(tx-1).css("width","100%")

              }else if ($(".con_left .top_wei ul li").length%3==2) {
                  $(".con_left .top_wei ul li").eq(tx-1).css("width","66.6%")

              }

              var bx=$(".con_left .bottom_wei ul li").length
              if($(".con_left .bottom_wei ul li").length%3==1){
                  $(".con_left .bottom_wei ul li").eq(bx-1).css("width","100%")

              }else if ($(".con_left .bottom_wei ul li").length%3==2) {
                  $(".con_left .bottom_wei ul li").eq(bx-1).css("width","66.6%")

              }
          }
      )
    </script>
    <!--楼盘动态-->
    <div class="houseSaleDT" th:if="${projectType eq '3' or houseInfo.projectStatus ne '3'}">
        <div class="normalHouseMain">
            <div class="title"><p>楼盘动态</p><a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm'}" target="_blank" class="more">查看更多 ></a>
            </div>
            <ul class="xstd">
                <li th:each="saleInfo,iterStat:${saleInfo}"  th:if="${iterStat.count &lt; 3}">
                    <span th:text="${saleInfo.addTime.year}+'-'+${saleInfo.addTime.month}+'-'+${saleInfo.addTime.day}"></span>
                    <a th:href="${'/house/news/'+projectId + '-' + projectType + '.htm#'+iterStat.index}" target="_blank">
                        <p th:text="${saleInfo.news}"></p>
                    </a>

                </li>

            </ul>
        </div>
        <div class="normalHouselpDy">
            <div class="housesRight">
                <div class="houseRight">
                    <div class="houseRinghtTitle">楼盘订阅</div>
                    <ul>
                        <li rel="1"><b></b><span>最新动态</span></li>
                        <li rel="2"><b></b><span>优惠通知</span></li>
                        <li rel="3"><b></b><span>开盘通知</span></li>
                        <li rel="4"><b></b><span>降价通知</span></li>
                        <li rel="5"><b></b><span>看房团通知</span></li>
                        <div class="cl"></div>
                    </ul>
                    <a onclick="showUnuseCode(2)" class="houseRightBtn" data_i="" style="cursor:pointer ; display: block;margin-top: 5px">立即订阅</a>
                </div>
            </div>
            <script type="text/javascript">
                //                 $(".houseSaleDT .houseRight ul li").hover(function(){
                //                     $(".houseSaleDT .houseRight ul li").removeClass("hover")
                //                     $(this).addClass("hover")
                //                 })
                $(".houseSaleDT .houseRight ul li").click(function(){
                    if($(this).find("b").hasClass("hover")){
                        $(this).find("b").removeClass("hover")
                    }else{
                        $(this).find("b").addClass("hover")
                    }
                    var txt = ""
                    $(".houseSaleDT .houseRight ul li").each(function(){
                        if($(this).find("b").hasClass("hover")){
                            txt+=$(this).attr("rel")+","
                        }
                    })
                    console.log(txt)
                    $(".houseRightBtn").attr("data_i",txt);
                })
            </script>
        </div>
        <div class="cl"></div>
    </div>
    <!--楼盘户型-->
    <div th:if="${!#lists.isEmpty(houseInfo.mainLayoutInfo)}" style=" margin-top: 24px; padding-bottom: 20px">
        <div class="w">
            <div id="js202" class="title" th:each="m,i:${houseInfo.mainLayoutInfo}" th:if="${i.count &lt; 2}" th:style="${m ne null}?'display:block':'display:none'">

                <p>户型展示</p>
                <div class="layoutTypeR">
                    <a th:each="layoutType:${layoutType}" th:text="${layoutType.name}" th:href="${'/house/layout/r'+layoutType.id+'-pid'+projectId + '-pt' + projectType+ '.htm'}"></a>
                    <i>丨</i>
                    <a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '.htm'}">查看更多 </a>
                </div>
                <div class="cl"></div>
            </div>
            <div class="huxing" >
                <ul>
                    <li th:each="l,i:${houseInfo.mainLayoutInfo}" th:if="${i.count &lt; 3}">
                        <!--<a th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '-l'+l.layId}">点击</a>-->
                        <a  th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '-l'+l.layId}"
                            target="_blank" class="newHouseHxImg">
                            <img th:src="${l.pic}" th:alt="${#numbers.formatInteger(l.buildArea,2)+'㎡'+l.roomType+'室'+l.hallType+'厅'+l.guardType+'卫'}"></a>

                        <div class="newHouseHxMain">
                            <h4>
                                <a  th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '-l'+l.layId}" target="_blank">
                                    <th:block th:text="${#numbers.formatInteger(l.buildArea,2)+'㎡'+l.roomType+'室'+l.hallType+'厅'+l.guardType+'卫'}"></th:block></a>
                                <span th:class="${'type_sun wait_'+l.state}"><th:block th:text="${#strings.isEmpty(l.stateValue)?'在售':l.stateValue}"></th:block></span>
                                <!--语音户型按钮-->
                                <span th:if="${l.voice ne null and l.voice ne ''}" class="voiceTabSy" th:data-id="${projectId + '-' + l.layId}">语音户型</span>
                                <!--语音户型按钮-->
                                <div class="cl"></div>
                            </h4>
                            <div>
                                <p><span>建筑面积： </span><th:block th:text="${l.buildArea eq 0 or #strings.isEmpty(l.buildArea)? '暂无资料':l.buildArea+'㎡'}"></th:block> </p>
                                <p th:unless="${#strings.isEmpty(l.freeArea)}"> <span>赠送面积：  </span> <th:block th:text="${l.freeArea}"></th:block> </p></p>
                                <div class="cl"></div>
                            </div>
                            <div>
                                <p> <span>朝向： </span> <th:block th:text="${#strings.isEmpty(l.forwardType) or #strings.isEmpty(l.forwardType)? '暂无资料' : l.forwardType}"></th:block></p>
                                <div class="cl"></div>
                            </div>
                            <p><span>类型： </span> <th:block th:text="${#strings.isEmpty(l.houseType) or #strings.isEmpty(l.houseType)? '暂无资料':l.houseType  }"></th:block></p>
                            <!--<span>户型解析： </span>-->
                            <!--<th:block th:text="${#strings.isEmpty(l.description)? '暂无资料' : l.description}"></th:block><br>-->

                            <!--<a  th:href="${'/house/layout/pid'+projectId + '-pt' + projectType + '-l'+l.layId}" class="huxing-seemore" target="_blank">查看全文></a>-->
                        </div>
                        <div class="newHouseHxRight">
                            <div class="hxPrise">

                                <div><span class="yfyj" th:utext="${#strings.isEmpty(l.totalPrice) or  l.totalPrice eq 0? '<p>待定</p>' : (#strings.indexOf(l.totalPrice ,'.') eq -1 ? l.totalPrice :#strings.toString(l.totalPrice ).replaceAll('0+?$','').replaceAll('[.]$','')) }"></span>
                                    <th:block  th:text="${#strings.isEmpty(l.totalPrice) or   l.totalPrice eq 0?'':'万元/套'}"></th:block></div>
                                <span>参考总价： </span>
                            </div>
                            <div class="huxing-btn type-Btn">
                                <p class="type-compare-btn1" th:data-layId="${ l.layId}" th:data-layName="${#numbers.formatInteger(l.buildArea,2)+'㎡'+l.roomType+'室'+l.hallType+'厅'+l.guardType+'卫'+ houseInfo.projectName}"><i></i>户型对比</p>
                                <p class="type-compare-btn2" th:data-layId="${ l.layId}" th:data-layName="${#numbers.formatInteger(l.buildArea,2)+'㎡'+l.roomType+'室'+l.hallType+'厅'+l.guardType+'卫'+ houseInfo.projectName}" th:onclick="'deletelayoud2('+${ l.layId}+')'"><i></i>取消对比</p>
                                <p onclick="showUnuseCode(1)" value="" class="getChangePzBtn"><i></i>获取最新价格变动</p>
                            </div>
                        </div>
                        <!--语音户型二维码-->
                        <div th:if="${l.voice ne null and l.voice ne ''}"  th:id="${'vrcode' + l.layId}" style="display:none" class="vrcodeall">
                            <span></span>
                            <img th:id="${'vqrcode' + l.layId}" src=""/>
                            <i>扫描二维码<br>听我的自我介绍</i>
                        </div>
                        <div class="cl"></div>
                        <!--语音户型按钮二维码-->
                    </li>
                </ul>
            </div>
            <div class="cl"></div>
        </div>
    </div>
    <img id="codelogo" style="display: none" src="https://imageicloud.fangxiaoer.com/event/2022/10/11/133502165.png" crossorigin="anonymous">
    <div id="qrcodeInfo" style="display: none"></div>
    <script type="text/javascript">
        /**该方法用来绘制一个有填充色的圆角矩形
         *@param cxt:canvas的上下文环境
         *@param x:左上角x轴坐标
         *@param y:左上角y轴坐标
         *@param width:矩形的宽度
         *@param height:矩形的高度
         *@param radius:圆的半径
         *@param fillColor:填充颜色
         **/
        function fillRoundRect(cxt, x, y, width, height, radius, /*optional*/ fillColor) {
            //圆的直径必然要小于矩形的宽高
            if (2 * radius > width || 2 * radius > height) { return false; }

            cxt.save();
            cxt.translate(x, y);
            //绘制圆角矩形的各个边
            drawRoundRectPath(cxt, width, height, radius);
            cxt.fillStyle = fillColor || "#000"; //若是给定了值就用给定的值否则给予默认值
            cxt.fill();
            cxt.restore();
        }
        function drawRoundRectPath(cxt, width, height, radius) {
            cxt.beginPath(0);
            //从右下角顺时针绘制，弧度从0到1/2PI
            cxt.arc(width - radius, height - radius, radius, 0, Math.PI / 2);

            //矩形下边线
            cxt.lineTo(radius, height);

            //左下角圆弧，弧度从1/2PI到PI
            cxt.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);

            //矩形左边线
            cxt.lineTo(0, radius);

            //左上角圆弧，弧度从PI到3/2PI
            cxt.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);

            //上边线
            cxt.lineTo(width - radius, 0);

            //右上角圆弧
            cxt.arc(width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);

            //右边线
            cxt.lineTo(width, height - radius);
            cxt.closePath();
        }

        $(".voiceTabSy").mouseover(function () {
            var value = $(this).attr("data-id");
            var infos = value.split('-');
            var voiceUrl = "https://m.fangxiaoer.com/fang1/"+infos[0]+"-1/layout/-pid"+infos[0]+"-pt1-l" + infos[1];
            $('#qrcodeInfo').qrcode({
                render : "canvas",
                width: 160,
                height: 160,
                text: voiceUrl
            }).hide();
            var canvasinfo = $("#qrcodeInfo canvas")[0];
            //添加logo
            var codeImage = document.querySelector("#codelogo");
            codeImage.crossOrigin = "anonymous"
            var ctx = canvasinfo.getContext('2d')
            fillRoundRect(ctx, 59, 59, 42, 42 ,3, '#fff')
            ctx.drawImage(codeImage, 61, 61, 38, 38);
            $('#vqrcode' + infos[1]).attr('src', canvasinfo.toDataURL('image/png'));
            $("#vrcode" + infos[1]).show();
        });
        $(".voiceTabSy").mouseleave(function () {
            $("#qrcodeInfo").html('')
            var value = $(this).attr("data-id");
            var infos = value.split('-');
            $("#vrcode" + infos[1]).hide();
        });
    </script>
</div>
</body>
</html>