<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
</head>

<body>
<!--有验证码的弹窗-->
<div th:fragment="useCode">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/popup/indexBB.css" />
    <style>.checkagreeInput a {color:#ff5200 !important;text-decoration:none !important;display:inline !important;background:rgba(255, 255, 255, 0) !important;line-height:20px !important;}</style>
    <div class="kfzc_heibu newHouseFreeCar_heibu" style="display: none;">
        <div class="kfzc newHouseFreeCar" style="display: none;">
            <h1 id="tc_content" class="zgs g_zs">房价时涨时落，掌握最新楼盘价格趋势，帮您找准买房最佳时机！</h1>
            <img src="https://static.fangxiaoer.com/web/styles/sy/popup/close_cl.png" />

            <div class="g_dws">
                <div class="uip">
                    <input type="" name="" placeholder="请输入手机号" id="houseKeeperPhone" maxlength="11" onblur="gdaFn()" onkeyup="gdaFn()" style="margin-top: 0"/>
<!--                    <input type="" name="" placeholder="请输入手机号" id="houseKeeperPhone" th:value="${#session?.getAttribute('phoneNum')}" maxlength="11"/>-->
                    <div class="d"></div>
                    <div class="dt155"></div>
                </div>
                <div class="uip2">
                    <input class="fxe_password_cs" type="password" placeholder="请输入验证码" id="houseKeeperCode" onblur="gdaFn()" onkeyup="gdaFn()" style="margin-top: 0; padding-right: 110px;">
                    <div class="d"></div>
                    <div class="dt"></div>
                    <b class="yzm g_m fmFn fxe_ReSendValidateCoad">获取验证码</b>
                    <b class="g_m fxe_validateCode" style="display: none"></b>
                </div>

                <div class="errorText ic_wrong" style="font-size: 14px;"></div>
                <div class="checkagreeInput" style="margin: 23px 0 0 0;">
                    <input type="checkbox" id="g_pg2"  class="mind-login protocol_input kfbb" checked="" style="padding-left: 0; margin-top: 3px; width: 14px; border-top: 1px solid #888; border-left: 1px solid #888; border-right: 1px solid #888;">
                    <!--                <i id="checkagree1" class="checkimg cheimg4"></i>-->
                    <div class="fts">我已阅读并同意
                        <a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《服务协议》</a>及<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《隐私政策》</a>
                    </div>
                </div>
                <div class="g_suo da">立即订阅</div>
                <div id="yjdy_btn" class="g_suo opc db" style="display: none;">立即订阅</div>
            </div>


        </div>
    </div>

    <div class="kpTc_heibu">
        <div class="kpTc">
            <div class="kpTc_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></div>
            <div>
                <div class="kpTc_icon"><img src="https://static.fangxiaoer.com/web/images/ico/sign/kpTc_03.png" /></div>
                <div class="kpTc_form">
                    <div><span>验证码已经发送到</span><input type="text" id="KeeperPhone" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" />，请注意查收</div>
                    <div>
<!--                        <input type="text" id="houseKeeperCode" maxlength="6" onkeyup="this.value=this.value.replace(/\D/g,'')" placeholder="输入您的验证码" />-->
                        <b class="fxe_ReSendValidateCoadAgent ReSendCode">获取验证码</b><b class="fxe_validateCode"></b>
                    </div>
                    <div class="errorText"></div>
                    <section style="overflow: hidden;display: none;">
                        <input type="checkbox"  th:checked="true"  id="be_user" style="float: left;margin: 5px;">
                        <p style="float: left">我已阅读并接受
                            <a href="https://info.fangxiaoer.com/About/protocol" target="_blank"><font color="#ff333">《房小二网用户服务协议》</font></a>及
                            <a href="https://info.fangxiaoer.com/About/policy" target="_blank"><font color="#ff333">《房小二网隐私政策》</font></a>
                        </p>
                    </section>
                    <div class="kpTc_btn"><a>立即预约</a></div>
                </div>
            </div>
        </div>
        <input  value="免费预约专车" id="guide_Content" type="hidden">
        <input  value="4" id="guide_Type" type="hidden">
        <input  th:value="${houseInfo?.projectName}" id="projectName_for_freeCar" type="hidden">
    </div>

    <script th:inline="javascript" >
        $("#g_pg2").attr("checked",false);
        $(".cheimg2").click(function () {
            if($(this).hasClass("checked")){
                $(this).removeClass("checked")
            }else{
                $(this).addClass("checked")
            }
        })
        sy_confirm.init(2,false);
        $(".newHouseFreeCar_heibu").hide();
        $(".newHouseFreeCar").hide();
        $(".kpTc_heibu").hide();
        $(".kpTc").hide();
        var loginGuideType = "";
        var coph=null

        if(s!=null){
            $(".uip2").hide()
            $(".uip").css('margin-top','31px')
            $("#houseKeeperPhone").val(filterPhoneNumber(sph))
            $(".da").hide()
            $(".db").show()
            coph=sph
        }else{
            coph=$("#houseKeeperPhone").val()
        }

        //input 校验
        function gdaFn(){
            var gup=$("#houseKeeperPhone").val()
            var gdv=$("#houseKeeperCode").val()
            $(".errorText").text('')
            console.log('---------------------')
            console.log(gup)
            console.log(filterPhoneNumber(sph))
            console.log('---------------------')
            if(s!=null){
                //已登录
                if(gup=='' || gdv.length<6 || gup.length<11){
                    $(".da").show()
                    $(".db").hide()
                    $(".uip2").show()
                    $(".uip").css('margin-top','5px')
                    $(".g_suo").text('免费通话')
                }else{
                    $(".da").hide()
                    $(".db").show()
                    $(".uip2").hide()
                    $(".uip").css('margin-top','31px')
                }
                if(gup==filterPhoneNumber(sph) || gup==sph){
                    $(".da").hide()
                    $(".db").show()
                    $(".uip2").hide()
                    $(".uip").css('margin-top','31px')
                }
            }else{
                if(gup=='' || gdv.length<6 || gup.length<11){
                    $(".da").show()
                    $(".db").hide()
                }else{
                    $(".da").hide()
                    $(".db").show()
                }
            }
        }


        $("#yjdy_btn").click(function(){
            var session_phoneNum =[[${#session?.getAttribute('phoneNum')}]];
            if(session_phoneNum == coph){
                //登陆
                if(sy_confirm.phone(coph) == true){
                    if ($("#g_pg2").is(':checked') == false){
                        $(".errorText").text("请仔细阅读并同意服务协议及隐私政策。")
                    }else {
                        loginGuideType = 1;
                        submitGuideParams();
                    }
                }else{
                    $(".errorText").text(sy_confirm.phone(coph))
                }
            }else {
                coph=$("#houseKeeperPhone").val()
                //未登陆
                if(sy_confirm.phone(coph)==true){
                    if (sy_confirm.phone(coph) != true) {
                        $(".errorText").text(sy_confirm.phone(coph))
                    } else if (sy_confirm.code($("#houseKeeperCode").val()) != true) {
                        $(".errorText").text(sy_confirm.code($("#houseKeeperCode").val()) )
                    } else if ($("#g_pg2").is(':checked') == false){
                        $(".errorText").text("请仔细阅读并同意服务协议及隐私政策。")
                    }else {
                        /*$(".errorText").text("");
                        $("#KeeperPhone").val($("#houseKeeperPhone").val().replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'));
                        $(".kpTc_heibu").show();
                        $(".kpTc").show();
                        $(".newHouseFreeCar_heibu").hide();
                        $(".newHouseFreeCar").hide();*/

                        submitGuideParams();

                    }
                }else{
                    $(".errorText").text(sy_confirm.phone(coph))
                }
                loginGuideType = 2;
            }
        })

        //发验证码
        $(".fmFn").click(function(){
            console.log('新验证码验证成功！');
            sy_confirm.Code($("#houseKeeperPhone").val()).then(res => {
                console.log('发送新验证码验证成功！');
                console.log(res);
                if (res == true) {
                    sy_confirm.timeWait();
                    $(".ReSendCode").hide();
                    $(".errorText").text("")
                }else{
                    $(".errorText").text("请输入手机号")
                }
            }).catch(err => {
                console.log('发送新验证码验证失败！');
                console.log(err)
                $(".errorText").text("请输入手机号")
            })
        })

        $(".ReSendCode").click(function(){
            if(sy_confirm.phone($("#houseKeeperPhone").val())==true){
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#houseKeeperPhone").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                        sy_confirm.timeWait();
                        $(".ReSendCode").hide();
                        setTimeout('$(".ReSendCode").show().html("重新获取")',60000)
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }else{
                $(".errorText").text(sy_confirm.phone($("#houseKeeperPhone").val()))
            }
        })
        $(".kpTc_close").click(function () {
            $("#houseKeeperCode").attr("value","");
//            $("#houseKeeperPhone").attr("value","");
            $(".kpTc_heibu").hide();
            $(".kpTc").hide();
        });
        $(".newHouseFreeCar img").click(function () {
//            $("#houseKeeperCode").attr("value","");
//            $("#houseKeeperPhone").attr("value","");
            $(".newHouseFreeCar_heibu").hide();
            $(".newHouseFreeCar").hide();
            // location.reload();
            clearTimeout(getCheckTimeFn)
            sy_confirm.wait = 0
            $(".fxe_validateCode").text('').hide()
            $(".fxe_ReSendValidateCoad").show()
            $(".zgs").text('账户登录')
        });

        $(".kpTc_btn a").click(function() {
            if (sy_confirm.phone($("#houseKeeperPhone").val()) != true) {
                $(".errorText").text(sy_confirm.phone($("#houseKeeperPhone").val()))
            } else if (sy_confirm.code($("#houseKeeperCode").val()) != true) {
                $(".errorText").text(sy_confirm.code($("#houseKeeperCode").val()) )
            }else if(!$("#be_user").is(":checked")){
                $(".errorText").text("请勾选房小二网用户服务协议");
            } else {
                submitGuideParams();
            }
        })

        function submitGuideParams() {
            var type = $("#guide_Type").val()
            var Projectid = [[${projectId}]];
            if(type == 4){
                var project_name_temp = [[${houseInfo?.projectName}]];
                project_name_temp = project_name_temp == null||project_name_temp==''?'未知项目':project_name_temp;
                if(project_name_temp != $("#projectName_for_freeCar").val()){
                    project_name_temp = $("#projectName_for_freeCar").val();
                }
                var params = "";
                if(loginGuideType == 1){
                    params = {sessionId:$("#glzs").val(), area: project_name_temp,budget:$("#guide_Content").val(),region: $("#guide_Content").val(),italy:$("#guide_Content").val()+'【Sy站】', type:$("#guide_Type").val()}
                }else {
                    params = {phone:$("#houseKeeperPhone").val(),code:$("#houseKeeperCode").val(), area: $("#guide_Content").val(),budget:project_name_temp,region: $("#guide_Content").val(),italy:$("#guide_Content").val()+'【Sy站】', type:$("#guide_Type").val()}
                }
                fangxiaoer.ajax("upguide", params, function (data) {
                    if (data.status == "1") {
                        if(loginGuideType == 2){
                            $("#houseKeeperPhone").val("");
                        }
                        $("#houseKeeperCode").val("");
                        $(".kpTc_heibu").hide();
                        $(".kfzc_heibu").hide();
                        $(".yydk_heibu").show();
                        $(".yyDk").show();
                        sy_confirm.wait = 0;
                    }else {
                        $(".errorText").text(data.msg);
                    }
                });
            }else {
                var data = "";
                if(loginGuideType == 1){
                    data = {sessionId:$("#glzs").val(),type:$("#guide_Type").val(),projectId:projectId,comment:$("#guide_Content").val()};
                }else {
                    data = {mobile:$("#houseKeeperPhone").val(),code:$("#houseKeeperCode").val(),type:$("#guide_Type").val(),projectId:projectId,comment:$("#guide_Content").val()};
                }
                $.ajax({
                    type:"post",
                    url:"/orderGuide",
//						async:false,
                    data:data,
                    success:function(data){
                        if(data.status==1){
                            if(loginGuideType == 2){
                                $("#houseKeeperPhone").val("");
                            }
                            $("#houseKeeperCode").val("");
                            $(".kpTc_heibu").hide();
                            $(".kfzc_heibu").hide();
                            $(".yydk_heibu").show();
                            $(".yyDk").show();
                            sy_confirm.wait = 0;
                        }else{
                            $(".errorText").text(data.msg);
                        }
                    },
                    error:function(data){
                        alert("服务器繁忙请稍后重试")
                        console.log(data)
                    }
                });
            }
        }
        function showUsercode(type) {
            $(".errorText").text("")
            if(type == 1||type == 3){//type3专门为地图点击的看房车执行切换项目
                $("#guide_Type").val("4");
                $("#guide_Content").val("免费预约专车");
                $("#tc_content").text("房小二网专车1对1带看便捷省心不受累");
                $("#yjdy_btn").text("预约专车");
                $(".kpTc_btn a").text("立即预约");
                $(".kpTc_icon img").attr("src","https://static.fangxiaoer.com/web/images/ico/sign/kpTc_03.png");
                $("#free_Call").css("display","block");
            }else {
                $("#guide_Type").val("2");
                $("#guide_Content").val("免费通话");
                $("#tc_content").text("房小二网将为您严格保密电话号码，请放心通话");
                $("#yjdy_btn").text("免费通话");
                $(".kpTc_btn a").text("立即通话");
                $(".kpTc_icon img").attr("src","https://static.fangxiaoer.com/web/images/ico/sign/kpTc_02.png");
                $("#free_Call").css("display","none");
            }
            // $("#houseKeeperPhone").val($("#loginUserPhone").val());
            $(".newHouseFreeCar_heibu").show();
            $(".newHouseFreeCar").show();
            success_Message(type+2);
        }
    </script>
</div>
<div th:fragment="useTouristCode">
    <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css" />-->
    <style>.checkagreeInput a {color:#ff5200 !important;text-decoration:none !important;display:inline !important;background:rgba(255, 255, 255, 0) !important;line-height:20px !important;}</style>
    <div class="kfzc_heibu newHouseFreeCar_heibu" style="display: none;">
        <div class="kfzc newHouseFreeCar" style="display: none;">
            <h1 id="tc_content">房价时涨时落，掌握最新楼盘价格趋势，帮您找准买房最佳时机！</h1>
            <input type="" name="" placeholder="输入您的手机号" id="houseKeeperPhone" th:value="${#session?.getAttribute('phoneNum')}" maxlength="11"/>
            <input type="hidden" id="glzs" th:value="${#session?.getAttribute('sessionId')}">
            <div class="errorText" style="font-size:14px;">123123132</div>
            <div class="checkagreeInput" style="margin: 0px auto 10px auto;">
                <i id="checkagree" class="checkimg checked cheimg7"></i><div>我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>及
                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
            </div>
            <a id="yjdy_btn">立即订阅</a>
            <img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" />
        </div>
    </div>

    <div class="kpTc_heibu">
        <div class="kpTc">
            <div class="kpTc_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></div>
            <div>
                <div class="kpTc_icon"><img src="https://static.fangxiaoer.com/web/images/ico/sign/kpTc_03.png" /></div>
                <div class="kpTc_form">
                    <div><span>验证码已经发送到</span><input type="text" id="KeeperPhone" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" />，请注意查收</div>
                    <div>
                        <input type="text" id="houseKeeperCode" maxlength="6" onkeyup="this.value=this.value.replace(/\D/g,'')" placeholder="输入您的验证码" />
                        <input type="hidden" id="glzs" th:value="${#session?.getAttribute('sessionId')}">
                        <b class="fxe_ReSendValidateCoadAgent ReSendCode">获取验证码</b><b class="fxe_validateCode"></b>
                    </div>
                    <div class="errorText"></div>
                    <section style="overflow: hidden;display: none;">
                        <input type="checkbox"  th:checked="true"  id="be_user" style="float: left;margin: 5px;">
                        <p style="float: left">我已阅读并接受 <a href="https://info.fangxiaoer.com/About/protocol" target="_blank"><span style="color: #ff5200;cursor: pointer">《房小二网用户服务协议》</span></a></p>
                    </section>
                    <div class="kpTc_btn"><a>立即预约</a></div>
                    <div id="free_Call">
                        <i class="kbTc_txt1">免费咨询电话：************</i	>
                        <a class="kbTc_txt2" onclick="_MEIQIA('showPanel')">在线咨询</a>
                    </div>
                </div>

            </div>
        </div>
        <input  value="4" id="guide_Type" type="hidden">
        <input  th:value="${houseInfo?.projectName}" id="projectName_for_freeCar" type="hidden">
    </div>

    <script th:inline="javascript" >
        $(".cheimg7").click(function () {
            if($(this).hasClass("checked")){
                $(this).removeClass("checked")
            }else{
                $(this).addClass("checked")
            }
        })
        sy_confirm.init(2,false);
        $(".newHouseFreeCar_heibu").hide();
        $(".newHouseFreeCar").hide();
        $(".kpTc_heibu").hide();
        $(".kpTc").hide();
        var loginGuideType = "";
        $("#yjdy_btn").click(function(){
            var session_phoneNum =[[${#session?.getAttribute('phoneNum')}]];
            if(session_phoneNum == $("#houseKeeperPhone").val()){
                if(sy_confirm.phone($("#houseKeeperPhone").val()) == true){
                    if ($(".cheimg7").hasClass("checked") == false){
                        $(".errorText").text("请仔细阅读并同意服务协议及隐私政策。")
                    }else {
                        loginGuideType = 1;
                        submitGuideParams();
                    }
                }else{
                    $(".errorText").text(sy_confirm.phone($("#houseKeeperPhone").val()))

                }
            }else {
                if(sy_confirm.phone($("#houseKeeperPhone").val())==true){
                    if ($(".cheimg7").hasClass("checked") == false){
                        $(".errorText").text("请仔细阅读并同意服务协议及隐私政策。")
                    }else {
                        $(".errorText").text("");
                        $("#KeeperPhone").val($("#houseKeeperPhone").val().replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'));
                        $(".kpTc_heibu").show();
                        $(".kpTc").show();
                        $(".newHouseFreeCar_heibu").hide();
                        $(".newHouseFreeCar").hide();
                        console.log('新验证码验证成功！');
                        sy_confirm.Code($("#houseKeeperPhone").val()).then(res => {
                            console.log('发送新验证码验证成功！');
                            console.log(res);
                            if (res == true) {
                                sy_confirm.timeWait();
                                $(".ReSendCode").hide();
                            }
                        }).catch(err => {
                            console.log('发送新验证码验证失败！');
                            console.log(err)
                        })
                    }
                }else{
                    $(".errorText").text(sy_confirm.phone($("#houseKeeperPhone").val()))
                }
                loginGuideType = 2;
            }
        })

        $(".ReSendCode").click(function(){
            if(sy_confirm.phone($("#houseKeeperPhone").val())==true){
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#houseKeeperPhone").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                     sy_confirm.timeWait();
                     $(".ReSendCode").hide();
                     setTimeout('$(".ReSendCode").show().html("重新获取")',60000)
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }else{
                $(".errorText").text(sy_confirm.phone($("#houseKeeperPhone").val()))
            }
        })
        $(".kpTc_close").click(function () {
            $("#houseKeeperCode").attr("value","");
//            $("#houseKeeperPhone").attr("value","");
            $(".kpTc_heibu").hide();
            $(".kpTc").hide();
        });
        $(".newHouseFreeCar img").click(function () {
//            $("#houseKeeperCode").attr("value","");
//            $("#houseKeeperPhone").attr("value","");
            $(".newHouseFreeCar_heibu").hide();
            $(".newHouseFreeCar").hide();
            location.reload()
        });

        $(".kpTc_btn a").click(function() {
            if (sy_confirm.phone($("#houseKeeperPhone").val()) != true) {
                $(".errorText").text(sy_confirm.phone($("#houseKeeperPhone").val()))
            } else if (sy_confirm.code($("#houseKeeperCode").val()) != true) {
                $(".errorText").text(sy_confirm.code($("#houseKeeperCode").val()) )
            }else if(!$("#be_user").is(":checked")){
                $(".errorText").text("请勾选房小二用户服务协议");
            } else {
                submitGuideParams();
            }
        })

        function submitGuideParams() {
            var data = "";
            if(loginGuideType == 1){
                data = {sessionId:$("#glzs").val(),type:$("#guide_Type").val(),projectId:projectId,projectName:projectName};
            }else {
                data = {phone:$("#houseKeeperPhone").val(),code:$("#houseKeeperCode").val(),type:$("#guide_Type").val(),projectId:projectId,projectName:projectName};
            }
            $.ajax({
                type:"post",
                url:"/addTouristOrder",
//						async:false,
                data:data,
                success:function(data){
                    if(data.status==1){
                        if(loginGuideType == 2){
                            $("#houseKeeperPhone").val("");
                        }
                        $("#houseKeeperCode").val("");
                        $(".kpTc_heibu").hide();
                        $(".kfzc_heibu").hide();
                        $(".coloio").show();
                        $(".yyDk").show();
                        sy_confirm.wait = 0;
                    }else{
                        $(".errorText").text(data.msg);
                    }
                },
                error:function(data){
                    alert("服务器繁忙请稍后重试")
                    console.log(data)
                }
            });
        }
        function showUsercode(type) {
            $(".errorText").text("")
            if(type == 1||type == 3){//type3专门为地图点击的看房车执行切换项目
                $("#guide_Type").val("4");
                $("#guide_Content").val("免费预约专车");
                $("#tc_content").text("房小二网专车1对1带看便捷省心不受累");
                $("#yjdy_btn").text("预约专车");
                $(".kpTc_btn a").text("立即预约");
                $(".kpTc_icon img").attr("src","https://static.fangxiaoer.com/web/images/ico/sign/kpTc_03.png");
                $("#free_Call").css("display","block");
            }else {
                $("#askSuccess").hide();
                $("#guide_Type").val("2");
                $("#guide_Content").val("免费通话");
                $("#tc_content").text("房小二网将为您严格保密电话号码，请放心通话");
                $("#yjdy_btn").text("免费通话");
                $(".kpTc_btn a").text("立即通话");
                $(".kpTc_icon img").attr("src","https://static.fangxiaoer.com/web/images/ico/sign/kpTc_02.png");
                $("#free_Call").css("display","none");
            }
            $("#houseKeeperPhone").val($("#loginUserPhone").val());
            $(".newHouseFreeCar_heibu").show();
            $(".newHouseFreeCar").show();
            success_Message(type+2);
        }
    </script>
</div>
<div th:fragment="unuseCode">
    <style>.checkagreeInput a {color:#ff5200 !important;text-decoration:none !important;display:inline !important;background:rgba(255, 255, 255, 0) !important;line-height:20px !important;}</style>
    <div class="kfzc_heibu newHouseChange_heibu" style="display: none;">
        <div class="kfzc newHouseChange" style="display: none;">
            <h1 class="zgs g_zs" >订阅楼盘，获得最新动态、优惠、开盘、降价消息，楼盘一手情况抢先知道！</h1>
            <img src="https://static.fangxiaoer.com/web/styles/sy/popup/close_cl.png" class="zgFns">
<!--            <h1>房价时涨时落，掌握最新楼盘价格趋势，帮您找准买房最佳时机！</h1>-->
            <div class="g_dws">
                <div class="uip">
                    <input type="" name="" placeholder="请输入手机号"  id="kfzcPhone" maxlength="11" onblur="g_dFn()" onkeyup="g_dFn()" style="margin-top: 0"/>
                    <div class="d"></div>
                    <div class="dt1"></div>
                </div>
                <div class="uip2">
                    <input class="fxe_password_cs" type="password" placeholder="请输入验证码" id="g_cod" onblur="g_dFn()" onkeyup="g_dFn()" style="margin-top: 0;padding-right: 105px;">
                    <div class="d"></div>
                    <div class="dt"></div>
                    <b class="yzm g_m gmFn fxe_ReSendValidateCoad">获取验证码</b>
                    <b class="g_m fxe_validateCode" style="display: none"></b>
                </div>


                <div class="errorText ic_wrong"></div>
                <!--<div class="checkagreeInput" style="margin: 0px auto 10px auto;">
                    <i id="checkagree" class="checkimg checked cheimg1"></i><div>我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《服务协议》</a>及<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《隐私政策》</a></div>
                </div>-->
                <div class="checkagreeInput" style="margin: 23px 0 0 0;">
                    <input type="checkbox" id="g_pg"  class="mind-login protocol_input" checked="" style="padding-left: 0; margin-top: 3px; width: 14px; border: 1px solid #888;">
                    <!--                <i id="checkagree1" class="checkimg cheimg4"></i>-->
                    <div class="fts">我已阅读并同意
                        <a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《服务协议》</a>及<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《隐私政策》</a>
                    </div>
                </div>

                <div class="g_suo ha">立即订阅</div>
                <div id="submitGuide" class="g_suo opc hb" style="display: none;">立即订阅</div>
                <input type="hidden" id="noCode_guide" value="价格变动">
            </div>
        </div>
    </div>
    <script th:inline="javascript">
        $("#g_pg").attr("checked",false);

        $(".cheimg1").click(function () {
            if($(this).hasClass("checked")){
                $(this).removeClass("checked")
            }else{
                $(this).addClass("checked")
            }
        })
        // $(".newHouseChange_heibu").hide();
        // $(".newHouseChange").hide();



        $("#submitGuide").click(function(){
            var getod=$("#g_cod").val()
            var ddh=null
            var durl=null
            if($("#kfzcPhone").val()==filterPhoneNumber(sph) && s!=null){
                ddh=sph
            }else{
                ddh=$("#kfzcPhone").val()
            }
            if(sy_confirm.phone(ddh) != true){
                $(".errorText").text(sy_confirm.phone($("#kfzcPhone").val()))
            }else if ($("#g_pg").is(':checked') == false){
                $(".errorText").text("请仔细阅读并同意服务协议及隐私政策。")
            }else {
                var project_name_temp = [[${houseInfo.projectName}]];
                project_name_temp = project_name_temp == null||project_name_temp==''?'未知项目':project_name_temp;
                var params = {
                    type:9,
                    phone: ddh,//$("#kfzcPhone").val(),
                    code: getod,
                    area:"无验证码订单",
                    budget:project_name_temp,
                    region:$("#noCode_guide").val(),
                    italy:'【Sy站】',
                }
                if(s!=null){
                    durl='/saveHouseOrder'//已登录
                }else{
                    durl='/saveGuide'
                }
                $.ajax({
                    type: "post",
                    url: durl,//saveGuide  saveHouseOrder
                    data: JSON.stringify(params),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success: function(data) {
                        if (data.status == 1) {
                            $(".newHouseChange_heibu").hide();
                            $(".newHouseChange").hide();
                            $(".yyDk").show();
                            $(".coloio").show();
                        } else if (data.status == 0) {
                            $(".errorText").text(data.msg);
                        } else {
                            $(".errorText").text("提交失败！")
                        }
                        sy_confirm.wait = 0;
                    },
                    error: function(data) {
                        console.log(data)
                    }
                });

            }
        })
        $(".newHouseFreeCar img").click(function(){
            $(".newHouseFreeCar").hide();
            $(".newHouseFreeCar_heibu").hide();
            clearTimeout(getCheckTimeFn)
            sy_confirm.wait = 0
            $(".fxe_validateCode").text('').hide()
            $(".fxe_ReSendValidateCoad").show()
            $(".zgs").text('账户登录')
//            $("#kfzcPhone").attr("value","");
//            $("#houseKeeperPhone").attr("value","");
        })
        $(".newHouseChange img").click(function(){
            $(".newHouseChange_heibu").hide();
            $(".newHouseChange").hide();
            clearTimeout(getCheckTimeFn)
            sy_confirm.wait = 0
            $(".fxe_validateCode").text('').hide()
            $(".fxe_ReSendValidateCoad").show()
            $(".zgs").text('账户登录')

//            $("#kfzcPhone").attr("value","");
//            $("#houseKeeperP hone").attr("value","");
        })


        function showUnuseCode(type) {
            $(".errorText").text("")
            if(type == 1){
                $(".newHouseChange h1").text("房价时涨时落，掌握最新楼盘价格趋势，帮您找准买房最佳时机！");
                $("#noCode_guide").val("获取最新价格变动");
                $(".newHouseChange_heibu").show();
                $(".newHouseChange").show();

                if(s!=null){
                    //已登录
                    $(".uip2").hide()
                    $(".uip").css('margin-top','31px')
                    $("#kfzcPhone").val(filterPhoneNumber(sph));
                    $(".ha").hide()
                    $(".hb").show()
                }else{

                }
            }else {
                console.log(s)
                if($(".houseRightBtn").attr("data_i") != "" && $(".houseRightBtn").attr("data_i") != undefined){
                    if(s!=null){
                        //已登录
                        $(".uip2").hide()
                        $(".uip").css('margin-top','31px')
                        $("#kfzcPhone").val(filterPhoneNumber(sph));
                        $(".ha").hide()
                        $(".hb").show()


                        $(".newHouseChange h1").html("订阅楼盘，获得最新动态、优惠、 开盘、降价消息，楼盘一手情况抢先知道！");
                        $("#noCode_guide").val("楼盘订阅");
                        $(".newHouseChange_heibu").show();
                        $(".newHouseChange").show();
                    }else{
                        //未登录
                        $(".newHouseChange h1").html("订阅楼盘，获得最新动态、优惠、 开盘、降价消息，楼盘一手情况抢先知道！");
                        $("#noCode_guide").val("楼盘订阅");
                        $(".newHouseChange_heibu").show();
                        $(".newHouseChange").show();


                    }
                }else {
                    alert("请至少选择一项")
                }
            }
            success_Message(type)
            // $("#kfzcPhone").val($("#loginUserPhone").val());
            $("#g_cod").val('')
            $("#g_pg").attr("checked",false);
//            $(".kfzc_heibu").show();
//            $(".kfzc").show();
        }

        //获取验证码
        $(".gmFn").click(function(){
            if(sy_confirm.phone($("#kfzcPhone").val())==true){
                $(".errorText").text("");
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#kfzcPhone").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                      $(".fmFn").hide()
                      sy_confirm.timeWait()   
                    }else{
                        alert('操作过于频繁, 请稍后')
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                    alert('操作过于频繁, 请稍后')
                })
            }else{
                $(".errorText").text("请输入手机号码");
            }
        })
        //input 校验
        function g_dFn(){
            let gh=$("#kfzcPhone").val()
            let gd=$("#g_cod").val()
            if(gh==''){
                $(".errorText").text("请输入手机号码");
                $(".ha").show()
                $(".hb").hide()
                return
            } else if(gd.length>=6) {
                $(".errorText").text("");
                $(".ha").hide()
                $(".hb").show()
                return true
            }
            if(s!=null && gh.length<11){
                $(".uip").css('margin-top','0px')
                $(".uip2").show()
                $(".ha").show()
                $(".hb").hide()
            }else if(gh==filterPhoneNumber(sph)){
                $(".uip2").hide()
                $(".ha").hide()
                $(".hb").show()
            }
        }
    </script>
</div>
<div th:fragment="guideMessage">
    <input type="hidden" id="loginUserPhone" th:value="${#session?.getAttribute('phoneNum')}">
    <div class="yydk_heibu coloio" style="display: none;">
        <div class="yyDk" style="display: none;">
            <div class="yyDk_close"><img src="https://static.fangxiaoer.com/web/styles/sy/popup/close_cl.png"></div>
            <div>
                <div class="yyDkImg">
                    <img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg">
                    <div id="guide_result" class="yyt">预约成功</div>
                </div>
                <div class="g_gf">
                    <p>感谢您对房小二网的关注</p>
                    <p>稍后将有工作人员为您提供服务。</p>
                    <img src="https://static.fangxiaoer.com/web/styles/sy/popup/emd.png">
                </div>
            </div>
        </div>
    </div>
    <script>
        sy_confirm.init(1,true);
//        $(".yyDk").hide();
        $(".yydk_heibu").hide();
        $(".yyDk_close").click(function () {
            $(".kfzc_heibu").hide();
            $(".yyDk").hide();
            $(".yydk_heibu").hide();
//            $("#kfzcPhone").attr("value","");
//            $("#KeeperPhone").attr("value","");
//            $("#houseKeeperPhone").attr("value","");
        });
        function success_Message(type) {
            switch (type){
                case 1: //价格变动
                    $("#guide_result").text("您已订阅成功");
                    $(".zgs").html('掌握最新楼盘价格趋势<br>房小二网帮您找准买房最佳时机！')
                    $(".zgs").css("width","360px")
                    break;
                case 2://立即订阅
                    $("#guide_result").text("您已订阅成功");
                    $(".zgs").html('订阅楼盘，获得最新动态、优惠、<br>开盘、降价消息，楼盘一手情况抢先知道！')
                    break;
                case 3: //免费专车
                    $("#guide_result").text("您已预约成功");
                    break;
                case 4: //免费通话
                    $("#guide_result").text("已收到您的信息");
                    $(".zgs").html('房小二网将严格保密您的电话<br>请放心通话')
                    $(".zgs").css("width","360px")
                    $(".g_suo").text('免费通话')
                    break;
                default:
                    $("#guide_result").text("您已订阅成功");
                    $(".zgs").text('账户登录')
                    break;
            }
        }
    </script>
</div>
<div th:fragment="forAsk">
    <div class="kfzc_heibu" style="display: none;" id="iNeedAsk">
        <div class="kfzc" style="display: block;">
            <h1 th:text="${'提问：'+houseInfo.projectName}">提问：保利·茉莉公馆</h1>
            <textarea name="" id="instantlyAsk" rows="" cols="" onkeyup="words_deal();"></textarea>
            <div style="float: inherit;">
                <span id="textCount">0</span><th:block>/300</th:block>
            </div>
            <a href="javascript:addProjectInfoAsk();">立即提问</a>
            <img id="closeBut" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" />
        </div>
    </div>
    <div class="yydk_heibu" style="display: none;" id="askSuccess">
        <div class="yyDk">
            <div class="yyDk_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div>
                <div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"></div>
                <p>提交成功</p>
                <p>我们会尽快审核您的问题，及时为您解答</p>
            </div>

        </div>
    </div>
    <script type="text/javascript">
        $("#closeBut").click(function () {
            $("#iNeedAsk").hide();
        });
        function words_deal() {
            var curLength = $("#instantlyAsk").val().length;
            if (curLength > 300) {
                var num = $("#instantlyAsk").val().substr(0, 300);
                $("#instantlyAsk").val(num);
                alert("超过字数限制，多出的字将被截断！");
            }
            else {
                $("#textCount").text($("#instantlyAsk").val().length);
            }
        }
        function addProjectInfoAsk() {
            var Projectid = [[${projectId}]];
            var sessionId = $("#glzs").val();
            var projectType_Ask = [[${projectType}]];
            var projectName = '[[${houseInfo.projectName}]]';
            var askType = 1;
            var content = $("#instantlyAsk").val();

            if(projectType_Ask  == null || projectType_Ask == undefined || projectType_Ask == ''){
                projectType_Ask = "1";
            }
            if (!sessionId) {
                alert("请先登录");
            }else if(!askType) {
                alert("请选择咨询类型");
            }else if(!content){
                alert("请填写提问内容");
            }else {
                var params = {
                    sessionId: sessionId,
                    projectId: Projectid,
                    askType: askType,
                    projectName: projectName,
                    content: content,
                    projectType: projectType_Ask
                };
                $.ajax({
                    type: "POST",
                    data: JSON.stringify(params),
                    url: "/addAsk",
                    dataType: "json",
                    headers: {
                        'Content-Type': 'application/json;charset=utf-8'
                    },
                    success: function (data) {
                        if (data.status == 1) {
                            $("#iNeedAsk").hide();
                            $("#askSuccess").show();
//                            window.location.reload();
                        } else {
                            alert(data.msg);
                        }
                    }
                });
            }
        }
    </script>
</div>
<div th:fragment="index_HelpSearch_popup">
        <div class="bnzfTC_heibu" style="display: none;">
            <!--底部广告一键订阅弹窗-->
            <div class="bnzfTC" style="display: none;">

                <div class="bnzfTC_box">
                    <div class="bnzfTC_form">
                        <div class="Verification"><span>验证码已经发送到</span><input type="text" id="bnzfTCPhone" readonly  unselectable="on"  maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" />，请注意查收 <b class="fxe_ReSendValidateCoadAgent bnzfTCSendCode">获取验证码</b><b class="fxe_validateCode"></b><div class="clearfix"></div></div>
                        <div>
                            <input type="text" id="bnzfTCCode" maxlength="6" onkeyup="this.value=this.value.replace(/\D/g,'')" placeholder="输入您的验证码" />
                            <div class="Prompt">
                            <span class="helpsearch_code_error" style="display: none"></span>
                            </div>
                        </div>
                        <div class="Agreement">
                            <i></i>
                            <input type="checkbox">
                            <h1>我已阅读并接受<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a></h1>
                        </div>
                        <div class="bnzfTC_btn" style="cursor:pointer "><a>帮我找房</a></div>
                        <div class="bnzfTC_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bnzfTCss_heibu" style="display: none;">
            <div class="bnzfTCss" style="display: none;">
                <div class="bnzfTCss_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
                <div>
                    <div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"></div>
                    <p id="guide_result">预约成功</p>
                    <p>客服人员将尽快与您联系，免费为您提供专业服务</p>
                </div>
            </div>
        </div>
        <script>
            $("#submitPhoneNum").click(function(){
                var help_phone_msg = sy_confirm.phone($("#userPhoneNum").val());
                if(help_phone_msg==true){
                    $("#bnzfTCPhone").val($("#userPhoneNum").val().replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'));
                    $(".bnzfTC_heibu").show();
                    $(".bnzfTC").show();
                    console.log('新验证码验证成功！');
                    sy_confirm.Code($("#userPhoneNum").val()).then(res => {
                        console.log('发送新验证码验证成功！');
                        console.log(res);
                        if (res == true) {
                            sy_confirm.timeWait();
                            $(".ReSendCode").hide();
                        }
                    }).catch(err => {
                        console.log('发送新验证码验证失败！');
                        console.log(err)
                    })
                }else {
                    fxe_alert_index(help_phone_msg);
                }
            })
            $(".bnzfTCSendCode").click(function(){
                var help_phone_msg = sy_confirm.phone($("#userPhoneNum").val());
                if( help_phone_msg==true){
                    console.log('新验证码验证成功！');
                    sy_confirm.Code($("#userPhoneNum").val()).then(res => {
                        console.log('发送新验证码验证成功！');
                        console.log(res);
                        if (res == true) {
                            sy_confirm.timeWait();
                            $(".ReSendCode").hide();
                            setTimeout('$(".ReSendCode").show().html("重新获取")',60000)
                        }
                    }).catch(err => {
                        console.log('发送新验证码验证失败！');
                        console.log(err)
                    })
                }else {
                    $(".helpsearch_code_error").text(help_phone_msg);
                    $(".helpsearch_code_error").show();
                }
            })
            $(".bnzfTC_btn a").click(function() {
                if($(".bnzfTC_box .Agreement input").is(":checked")){
                    var help_phone_msg1 =  sy_confirm.phone($("#userPhoneNum").val());
                    var help_phone_msg2 =  sy_confirm.code($("#bnzfTCCode").val());
                    if (help_phone_msg1 != true) {
                        $(".helpsearch_code_error").text(help_phone_msg1);
                        $(".helpsearch_code_error").show();
                    } else if ( help_phone_msg2!= true) {
                        $(".helpsearch_code_error").text(help_phone_msg2);
                        $(".helpsearch_code_error").show();
                    } else {
                        var params = {
                            type:1,
                            phone: $("#userPhoneNum").val(),
                            code: $("#bnzfTCCode").val(),
                            region:'首页帮您找房',
                            budget:'首页帮您找房',
                            area:"首页帮您找房",
                            italy:'【Sy站】',
                        }
                        $.ajax({
                            type: "post",
                            url: "/saveHouseOrder",
                            data: JSON.stringify(params),
                            headers : {
                                'Content-Type' : 'application/json;charset=utf-8'
                            },
                            success: function(data) {
                                if (data.status == 1) {
//                            fxe_alert("提交成功！")

                                    $(".bnzfTC_heibu").hide();
                                    $(".bnzfTC").hide();
                                    $(".bnzfTCss_heibu").show()
                                    $(".bnzfTCss").show()
                                    $("#userPhoneNum").attr("value","");
                                    $("#bnzfTCCode").attr("value","");
                                    sy_confirm.wait = 0;
                                    $(".helpsearch_code_error").hide();
                                    setTimeout(function(){
                                        $(".bnzfTCss_heibu").hide()
                                    },1500)
                                } else if (data.status == 0) {
                                    $(".helpsearch_code_error").text(data.msg);
                                    $(".helpsearch_code_error").show();
                                } else {
                                    $(".helpsearch_code_error").text("提交失败！");
                                    $(".helpsearch_code_error").show();
                                }
                            },
                            error: function(data) {
                                console.log(data)
                            }
                        });
                    }
                }else {
                    fxe_alert("请点击同意服务协议");
                }


            })
            $(".bnzfTC_close").click(function(){
                $(".bnzfTC_heibu").hide();
                $(".bnzfTC").hide();
                $("#bnzfTCCode").val("")
                $(".helpsearch_code_error").hide();
            })
            $(".bnzfTCss_close").click(function(){
                $(".bnzfTCss_heibu").hide();
                $(".bnzfTCss").hide();
                $(".helpsearch_code_error").hide();
            })
            $(".bnzfTC_box .Agreement i").addClass("bd")
            $(".bnzfTC_box .Agreement input").attr("checked", true);
            $(".bnzfTC_box .Agreement i").click(
                function(){
                    $(this).next("input").click()
                    if($(this).hasClass("bd")){
                        $(this).removeClass("bd")
                    }else{
                        $(this).addClass("bd")
                    }
                }
            )
            $("fxe_ReSendValidateCoadAgent").click(
                function () {
                    $("fxe_validateCode").css("display","inline-block")
                }
            )
        </script>
    </div>

<div th:fragment="search_housePrice_popup">
    <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/checkHR.css" />-->

    <div class="bnzfTC_heibu" style="display: none;">
        <!--精准评估弹窗-->
        <div class="bnzfTC" style="display: none;">

            <div class="bnzfTC_box">
                <div class="bnzfTC_form">
                    <div class="Verification"><span>验证码已经发送到</span><input type="text" id="bnzfTCPhone" readonly  unselectable="on"  maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" />，请注意查收 <b class="fxe_ReSendValidateCoadAgent bnzfTCSendCode">获取验证码</b><b class="fxe_validateCode"></b><div class="clearfix"></div></div>
                    <div>
                        <input type="text" id="bnzfTCCode" maxlength="6" onkeyup="this.value=this.value.replace(/\D/g,'')" placeholder="输入您的验证码" />
                        <div class="Prompt">
                            <span class="helpsearch_code_error" style="display: none"></span>
                        </div>
                    </div>
                    <div class="Agreement">
                        <i></i>
                        <input type="checkbox">
                        <p>我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                            及<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></p>
                    </div>

<!--                    <div th:include="fragment/freepublish::terms"></div>-->
                    <div class="bnzfTC_btn" style="cursor:pointer "><a>精准评估</a></div>
                    <div class="phone_sun">
                        <i class="kbTc_txt1">免费咨询电话：************</i	>
                        <a class="kbTc_txt2" onclick="_MEIQIA('showPanel')">在线咨询</a>
                        <div class="clearfix"></div>
                    </div>
                    <div class="bnzfTC_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></div>
                </div>
            </div>
        </div>
    </div>
    <div class="bnzfTCss_heibu" style="display: none;">
        <div class="bnzfTCss" style="display: none;">
            <div class="bnzfTCss_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div>
                <div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"></div>
                <p id="guide_result">提交成功</p>
                <p>工作人员将尽快与您联系，免费为您提供专业服务</p>
            </div>
        </div>
    </div>
    <script>
        sy_confirm.init(2,true);
        $("#submitPhoneNum").click(function(){
            var help_phone_msg = sy_confirm.phone($("#userPhoneNum").val());
            if(help_phone_msg==true){
                $("#bnzfTCPhone").val($("#userPhoneNum").val().replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'));
                $(".bnzfTC_heibu").show();
                $(".bnzfTC").show();
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#userPhoneNum").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                     sy_confirm.timeWait();
                     $(".ReSendCode").hide();      
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }else {
                $(".help_search_house_error").text(help_phone_msg);
                $(".help_search_house_error").show();
            }
        })
        $(".bnzfTCSendCode").click(function(){
            var help_phone_msg = sy_confirm.phone($("#userPhoneNum").val());
            if( help_phone_msg==true){
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#userPhoneNum").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                     sy_confirm.timeWait();
                     $(".ReSendCode").hide();
                     setTimeout('$(".ReSendCode").show().html("重新获取")',60000)   
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }else {
                $(".helpsearch_code_error").text(help_phone_msg);
                $(".helpsearch_code_error").show();
            }
        })
        $(".bnzfTC_btn a").click(function() {
            if($(".bnzfTC_box .Agreement input").is(":checked")){
                var help_phone_msg1 =  sy_confirm.phone($("#userPhoneNum").val());
                var help_phone_msg2 =  sy_confirm.code($("#bnzfTCCode").val());
                var param = $("#parameter").val();
                if (help_phone_msg1 != true) {
                    $(".helpsearch_code_error").text(help_phone_msg1);
                    $(".helpsearch_code_error").show();
                } else if ( help_phone_msg2!= true) {
                    $(".helpsearch_code_error").text(help_phone_msg2);
                    $(".helpsearch_code_error").show();
                } else {
                    var params = {
                        type:2,
                        phone: $("#userPhoneNum").val(),
                        code: $("#bnzfTCCode").val(),
                        region:'精准评估',
                        budget:'精准评估',
                        area:"免费估价",
                        italy:param+'【Sy站】'
                    }
                    $.ajax({
                        type: "post",
                        url: "/saveHouseOrder",
                        data: JSON.stringify(params),
                        headers : {
                            'Content-Type' : 'application/json;charset=utf-8'
                        },
                        success: function(data) {
                            if (data.status == 1) {
//                            fxe_alert("提交成功！")

                                $(".bnzfTC_heibu").hide();
                                $(".bnzfTC").hide();
                                $(".bnzfTCss_heibu").show()
                                $(".bnzfTCss").show()
                                $("#userPhoneNum").attr("value","");
                                $("#bnzfTCCode").attr("value","");
                                sy_confirm.wait = 0;
                                $(".helpsearch_code_error").hide();
                                setTimeout(function(){
                                    $(".bnzfTCss_heibu").hide()
                                },1500)
                            } else if (data.status == 0) {
                                $(".helpsearch_code_error").text(data.msg);
                                $(".helpsearch_code_error").show();
                            } else {
                                $(".helpsearch_code_error").text("提交失败！");
                                $(".helpsearch_code_error").show();
                            }
                        },
                        error: function(data) {
                            console.log(data)
                        }
                    });
                }
            }else {
                fxe_alert("请仔细阅读并同意服务协议及隐私政策");
            }


        })
        $(".bnzfTC_close").click(function(){
            $(".bnzfTC_heibu").hide();
            $(".bnzfTC").hide();
            $("#bnzfTCCode").val("")
            $(".helpsearch_code_error").hide();
        })
        $(".bnzfTCss_close").click(function(){
            $(".bnzfTCss_heibu").hide();
            $(".bnzfTCss").hide();
            $(".helpsearch_code_error").hide();
        })
        $(".bnzfTC_box .Agreement i").addClass("bd")
        $(".bnzfTC_box .Agreement input").attr("checked", true);
        $(".bnzfTC_box .Agreement i").click(
                function(){
                    $(this).next("input").click()
                    if($(this).hasClass("bd")){
                        $(this).removeClass("bd")
                    }else{
                        $(this).addClass("bd")
                    }
                }
        )
        $("fxe_ReSendValidateCoadAgent").click(
                function () {
                    $("fxe_validateCode").css("display","inline-block")
                }
        )
    </script>
</div>
<div th:fragment="houseKeeper_freeCall">
    <style>.checkagreeInput a {color:#ff5200 !important;text-decoration:none !important;display:inline !important;background:rgba(255, 255, 255, 0) !important;line-height:20px !important;}</style>
    <input type="hidden" id="glzs" th:value="${#session?.getAttribute('sessionId')}">
    <input type="hidden" id="loginUserPhone" th:value="${#session?.getAttribute('phoneNum')}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/layer.css" />
    <script type="text/javascript" src="/js/house/verify.js"></script>
    <div class="kfzc_heibu newHouseFreeCar_heibu" style="display: none;">
        <div class="kfzc newHouseFreeCar" style="display: none;">
            <h1 id="tc_content">房小二网将为您严格保密电话号码，请放心通话</h1>
            <input type="" name="" placeholder="输入您的手机号" id="houseKeeperPhone" th:value="${#session?.getAttribute('phoneNum')}" maxlength="11"/>
            <div class="errorText" style="display: none">123123132</div>
            <div class="checkagreeInput" style="margin: 0px auto 10px auto;">
                <i id="checkagree" class="checkimg checked cheimg3"></i><div>我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>及
                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
            </div>
            <a id="yjdy_btn">免费通话</a>
            <img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" />
        </div>
    </div>

    <div class="kpTc_heibu" style="display: none;">
        <div class="kpTc" style="display: none;">
            <div class="kpTc_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></div>
            <div>
                <div class="kpTc_icon"><img src="https://static.fangxiaoer.com/web/images/ico/sign/kpTc_03.png" /></div>
                <div class="kpTc_form">
                    <div><span>验证码已经发送到</span><input type="text" id="bnzfTCPhone" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" />，请注意查收</div>
                    <div>
                        <input type="text" id="bnzfTCCode" maxlength="6" onkeyup="this.value=this.value.replace(/\D/g,'')" placeholder="输入您的验证码" />
                        <b class="fxe_ReSendValidateCoadAgent ReSendCode">获取验证码</b><b class="fxe_validateCode"></b>
                    </div>
                    <div class="helpsearch_code_error"  style="display: none"></div>
                    <section style="overflow: hidden;display: none;">
                        <input type="checkbox"  th:checked="true"  id="be_user" style="float: left;margin: 5px;">
                        <p style="float: left">我已阅读并接受 <a href="https://info.fangxiaoer.com/About/protocol" target="_blank"><span style="color: #ff5200;cursor: pointer">《房小二网用户服务协议》</span></a></p>
                    </section>
                    <div class="kpTc_btn"><a>立即预约</a></div>
                </div>

            </div>
        </div>
        <input  value="免费预约专车" id="guide_Content" type="hidden">
        <input  value="4" id="guide_Type" type="hidden">
    </div>


    <div class="bnzfTCss_heibu" style="display: none;">
        <div class="bnzfTCss" style="display: none;">
            <div class="bnzfTCss_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div>
                <div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"></div>
                <p id="guide_result">预留成功</p>
                <p id="success_pop_guide">工作人员将尽快与您联系，免费为您提供专业服务</p>
            </div>
        </div>
    </div>
    <script th:inline="javascript">
        $(".cheimg3").click(function () {
            if($(this).hasClass("checked")){
                $(this).removeClass("checked")
            }else{
                $(this).addClass("checked")
            }
        })
        sy_confirm.init(2,true);
        var loginGuideType = "";
        $("#yjdy_btn").click(function(){
            var session_phoneNum =[[${#session?.getAttribute('phoneNum')}]];
            if(session_phoneNum == $("#houseKeeperPhone").val()){
                if(sy_confirm.phone($("#houseKeeperPhone").val()) == true){
                    if ($(".cheimg3").hasClass("checked") == false){
                        $(".errorText").text("请仔细阅读并同意服务协议及隐私政策。");
                    }else{
                        loginGuideType = 1;
                        submitGuideParams();
                    }
                }else{
                    $(".errorText").text(sy_confirm.phone($("#houseKeeperPhone").val()))
                }
            }else {
                var help_phone_msg = sy_confirm.phone($("#houseKeeperPhone").val());
                if(help_phone_msg == true){
                    if ($(".cheimg3").hasClass("checked") == false){
                        $(".errorText").text("请仔细阅读并同意服务协议及隐私政策。");
                    }else{
                        $("#bnzfTCPhone").val($("#houseKeeperPhone").val().replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'));
                        $(".newHouseFreeCar_heibu").hide();
                        $(".newHouseFreeCar").hide();
                        $(".kpTc_heibu").show();
                        $(".kpTc").show();
                        console.log('新验证码验证成功！');
                        sy_confirm.Code($("#houseKeeperPhone").val()).then(res => {
                            console.log('发送新验证码验证成功！');
                            console.log(res);
                            if (res == true) {
                                sy_confirm.timeWait();
                                $(".ReSendCode").hide();
                            }
                        }).catch(err => {
                            console.log('发送新验证码验证失败！');
                            console.log(err)
                        })
                    }
                }else{
                    $(".errorText").text(help_phone_msg);
                    $(".errorText").show();
                }
                loginGuideType = 2;
            }
        })
        function submitGuideParams() {
            var guideType = $("#guide_Type").val()
            if(guideType == 4){//guideType 4是免费专车 1是免费通话
                var params = "";
                if(loginGuideType == 1){
                    params = {sessionId:$("#glzs").val(), area: $("#guide_Content").val(),budget:'免费预约专车',region: $("#guide_Content").val(),italy:freeServiceType+'【Sy站】', type:$("#guide_Type").val()}
                }else {
                    params = {phone:$("#houseKeeperPhone").val(),code:$("#bnzfTCCode").val(), area: $("#guide_Content").val(),budget:'免费预约专车',region: $("#guide_Content").val(),italy:freeServiceType+'【Sy站】', type:$("#guide_Type").val()}
                }
                $.ajax({
                    type:"post",
                    url:"/saveHouseOrder",
//						async:false,
                    data:JSON.stringify(params),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success:function(data){
                        if (data.status == 1) {
//                            fxe_alert("提交成功！")

                            $(".kpTc_heibu").hide();
                            $(".kpTc").hide();
                            $(".bnzfTCss_heibu").show()
                            $(".bnzfTCss").show()
                            $("#houseKeeperPhone").attr("value","");
                            $("#bnzfTCCode").attr("value","");
                            sy_confirm.wait = 0;
                            $(".helpsearch_code_error").hide();

                            $(".newHouseFreeCar_heibu").hide();
                            $(".newHouseFreeCar").hide();
                            $(".errorText").hide();
                            setTimeout(function(){
                                $(".bnzfTCss_heibu").hide()
                            },2000)
                        } else if (data.status == 0) {
                            $(".helpsearch_code_error").text(data.msg);
                            $(".helpsearch_code_error").show();
                        } else {
                            $(".helpsearch_code_error").text("提交失败！");
                            $(".helpsearch_code_error").show();
                        }
                    },
                    error:function(data){
                        alert("服务器繁忙请稍后重试")
                        console.log(data)
                    }
                });
            }else if(guideType == 1){
                var data = "";
                if(loginGuideType == 1){
                    data = {sessionId:$("#glzs").val(),type:$("#guide_Type").val(), region:$("#guide_Content").val(),  budget:'免费通话', area:$("#guide_Content").val(),italy:freeServiceType+'【Sy站】'};
                }else {
                    data = {phone:$("#houseKeeperPhone").val(),code:$("#bnzfTCCode").val(),type:$("#guide_Type").val(), region:$("#guide_Content").val(),  budget:'免费通话', area:$("#guide_Content").val(),italy:freeServiceType+'【Sy站】'};
                }
                $.ajax({
                    type:"post",
                    url:"/saveHouseOrder",
//						async:false,
                    data:JSON.stringify(data),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success:function(data){
                        if (data.status == 1) {
//                            fxe_alert("提交成功！")

                            $(".kpTc_heibu").hide();
                            $(".kpTc").hide();
                            $(".bnzfTCss_heibu").show()
                            $(".bnzfTCss").show()
                            $("#houseKeeperPhone").attr("value","");
                            $("#bnzfTCCode").attr("value","");
                            sy_confirm.wait = 0;
                            $(".helpsearch_code_error").hide();

                            $(".newHouseFreeCar_heibu").hide();
                            $(".newHouseFreeCar").hide();
                            $(".errorText").hide();
                            setTimeout(function(){
                                $(".bnzfTCss_heibu").hide()
                            },2000)
                        } else if (data.status == 0) {
                            $(".helpsearch_code_error").text(data.msg);
                            $(".helpsearch_code_error").show();
                        } else {
                            $(".helpsearch_code_error").text("提交失败！");
                            $(".helpsearch_code_error").show();
                        }
                    },
                    error:function(data){
                        alert("服务器繁忙请稍后重试")
                        console.log(data)
                    }
                });
            }
        }

        $(".ReSendCode").click(function(){
            var help_phone_msg = sy_confirm.phone($("#houseKeeperPhone").val());
            if( help_phone_msg==true){
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#houseKeeperPhone").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                     sy_confirm.timeWait();
                     $(".ReSendCode").hide();
                     setTimeout('$(".ReSendCode").show().html("重新获取")',60000)   
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }else {
                $(".helpsearch_code_error").text(help_phone_msg);
                $(".helpsearch_code_error").show();
            }
        })

        $(".kpTc_btn a").click(function() {
            var confirm_phone_verify= sy_confirm.phone($("#houseKeeperPhone").val());
            var confirm_code_verify =  sy_confirm.code($("#bnzfTCCode").val());
            if (confirm_phone_verify != true) {
                $(".helpsearch_code_error").text(confirm_phone_verify);
                $(".helpsearch_code_error").show();
            } else if (confirm_code_verify != true) {
                $(".helpsearch_code_error").text(confirm_code_verify);
                $(".helpsearch_code_error").show();
            }else if(!$("#be_user").is(":checked")){
                $(".helpsearch_code_error").text("请勾选房小二用户服务协议");
                $(".helpsearch_code_error").show();
            } else {
                submitGuideParams();
            }
        })
        $(".newHouseFreeCar img").click(function () {
            $(".newHouseFreeCar_heibu").hide();
            $(".newHouseFreeCar").hide();
            $(".errorText").hide();
        });
        $(".kpTc_close img").click(function(){
            $(".kpTc_heibu").hide();
            $(".kpTc").hide();
            $("#bnzfTCCode").val("");
            $(".helpsearch_code_error").hide();
            $(".errorText").hide();
        })
        $(".bnzfTCss_close").click(function(){
            $(".bnzfTCss_heibu").hide();
            $(".bnzfTCss").hide();
            $(".helpsearch_code_error").hide();
            $(".errorText").hide();
        })
//        $(".bnzfTC_box .Agreement i").addClass("bd")
//        $(".bnzfTC_box .Agreement input").attr("checked", true);
//        $(".bnzfTC_box .Agreement i").click(
//            function(){
//                $(this).next("input").click()
//                if($(this).hasClass("bd")){
//                    $(this).removeClass("bd")
//                }else{
//                    $(this).addClass("bd")
//                }
//            }
//        )
        $("fxe_ReSendValidateCoadAgent").click(
            function () {
                $("fxe_validateCode").css("display","inline-block")
            }
        );

        //弹出输入手机号界面（用于不关联项目id的新房免费通话/专车）
        var freeServiceType ='';
        function showPhonePop(type) {//type 1为小二管家免费专车 2为小二管家免费通话 3为普通免费专车
            if(type == 1){
                $("#guide_Type").val("4");
                $("#guide_Content").val("免费专车");
                $("#tc_content").text("房小二网专车1对1带看便捷省心不受累");
                $("#yjdy_btn").text("预约专车");
                $(".kpTc_btn a").text("立即预约");
                $("#guide_result").text("预约成功");
                $("#success_pop_guide").text("工作人员将尽快与您联系，免费为您提供专业服务");
                $(".kpTc_icon img").attr("src","https://static.fangxiaoer.com/web/images/ico/sign/kpTc_03.png");
                $("#free_Call").css("display","block");
                freeServiceType="免费专车";
                //判断是普通免费专车还是小二管家专车
                var freeCarCommon = $("#freeCarCommon").val();
                if(freeCarCommon ==1){
                    $("#guide_Content").val("免费预约专车");
                    freeServiceType="免费专车";
                }
            }else if(type == 2) {
                $("#guide_Type").val(1);
                $("#guide_Content").val("免费通话");
                $("#tc_content").text("房小二网将为您严格保密电话号码，请放心通话");
                $("#yjdy_btn").text("免费通话");
                $(".bnzfTC_btn a").text("立即通话");
                $("#guide_result").text("预留成功");
                $("#success_pop_guide").text("工作人员将尽快与您联系，免费为您提供专业服务");
                $(".kpTc_icon img").attr("src","https://static.fangxiaoer.com/web/images/ico/sign/kpTc_02.png");
                $("#free_Call").css("display","none");
                freeServiceType="免费通话";
            }
            $(".errorText").text("");
            $(".helpsearch_code_error").text("");
            $("#houseKeeperPhone").val($("#loginUserPhone").val());
            $(".newHouseFreeCar_heibu").show();
            $(".newHouseFreeCar").show();
        }

    </script>
</div>
</body>
</html>