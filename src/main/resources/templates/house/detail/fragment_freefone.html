<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="utf-8" />
	</head>
	<body>
	<div th:fragment="Freefone">
		<div class="Freefone w">
			<img src="https://static.fangxiaoer.com/web/images/sy/house/Freefone.jpg"/>
		</div>
		<div class="FreefonePopup phoneForm">
			<dl>
				<dt>
					<h1>免费通话<img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"/></h1>
					<p>请填写您的手机号码，以便客服人员联系您看房。</p>
				</dt>
				<dd>
					<div>
						<input type="tel" id="FreefonePhone" value="" maxlength="11" placeholder="请输入手机号码"/>
					</div>
					<s><img src="https://static.fangxiaoer.com/web/images/sy/house/FreefonePhone.png"/></s>
				</dd>
				<dd style="height: 70px;">
					<div class="code">
						<input type="tel" id="FreefoneCode" maxlength="6" placeholder="请输入验证码"/>
					</div>
					<b class="fxe_ReSendValidateCoad FreefoneBtn">获取验证码</b>
					<b style="display: none;" class="fxe_validateCode"></b>
					<h1 class="cl"></h1>
					<span class="error"></span>
					<h1 class="cl"></h1>
				</dd>
				<dd class="read">
					<p class="hover">
						<img src="https://static.fangxiaoer.com/web/images/sy/house/FreefoneSucceed.png"/>
					</p>
					<span>我已阅读并接受</span>
					<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
				</dd>
				<dd><h2>立即通话</h2></dd>
			</dl>
		</div>
		<div class="FreefonePopup phoneFormNext">
			<dl>
				<dt>
					<h1>免费通话<img src="https://static.fangxiaoer.com/web/images/sy/house/FreefoneX.png"/></h1>
				</dt>
				<dd>
					<h4>稍后我们客服人员会回复您！请保持电话畅通！</h4>
					<h5>提示：您将会接到客服人员的转接来电，不收取任何费用，请放心接听。</h5>
				</dd>
			</dl>
		</div>
		<script>
			sy_confirm.init(2,false)
			//勾选已阅读
			$(".FreefonePopup dl dd>p").click(function(){
				if($(this).hasClass("hover")){
					$(this).removeClass("hover");
				}else{
					$(this).addClass("hover");
				}
			})
			//手机号
			$("#FreefonePhone").blur(function(){
				if(sy_confirm.phone($("#FreefonePhone").val())!=true){
					$(".FreefonePopup dl dd .error").text(sy_confirm.phone($("#FreefonePhone").val()));
				}
			})
			//验证码
			$("#FreefoneCode").blur(function(){
				if(sy_confirm.code($("#FreefoneCode").val())!=true){
					$(".FreefonePopup dl dd .error").text(sy_confirm.code($("#FreefoneCode").val()));
				}
			})
			//清除错误提示
			$(".FreefonePopup dl dd>div input").focus(function(){
				$(".FreefonePopup dl dd .error").text("");	
			})
			//弹出框
			$(".Freefone").click(function(){
				$("#FreefonePhone").val("");
				$("#FreefoneCode").val("");
				$(".FreefonePopup dl dd .error").text("");
				$(".phoneForm").fadeIn();
			})
			//关闭
			$(".FreefonePopup dl dt h1 img").click(function(){
				$(".FreefonePopup").fadeOut();
			})
			//获取验证码
			$(".FreefoneBtn").click(function(){
				if(sy_confirm.phone($("#FreefonePhone").val())!=true){
					$(".FreefonePopup dl dd .error").text(sy_confirm.phone($("#FreefonePhone").val()));
				}else{
					console.log('新验证码验证成功！');
					sy_confirm.Code($("#FreefonePhone").val()).then(res => {
						console.log('发送新验证码验证成功！');
						console.log(res);
						if (res == true) {
							sy_confirm.timeWait();
						}else{
							$(".FreefonePopup dl dd .error").text(res);
						}
					}).catch(err => {
						console.log('发送新验证码验证失败！');
						console.log(err)
						$(".FreefonePopup dl dd .error").text(err);
					})
				}
			})
			//提交立即通话
			$(".FreefonePopup dl dd>h2").click(function(){
				$(".FreefonePopup dl dd .error").text("");
				if(!$(".FreefonePopup dl dd>p").hasClass("hover")){
					$(".FreefonePopup dl dd .error").text("请阅读并勾选服务协议");
				}else if(sy_confirm.phone($("#FreefonePhone").val())!=true){
					$(".FreefonePopup dl dd .error").text(sy_confirm.phone($("#FreefonePhone").val()));
				}else if(sy_confirm.code($("#FreefoneCode").val())!=true){
					$(".FreefonePopup dl dd .error").text(sy_confirm.code($("#FreefoneCode").val()));
				}else{
					$.ajax({
						type:"post",
						url:"https://ltapi.fangxiaoer.com/apiv1/active/orderHouseGuide",
//						async:false,
						data:{mobile:$("#FreefonePhone").val(),code:$("#FreefoneCode").val(),type:2,projectId:projectId,comment:"免费电话"},
						success:function(data){
							if(data.status==1){
								$(".phoneForm").hide();
								$(".phoneFormNext").show();
							}else{
								$(".FreefonePopup dl dd .error").text(data.msg);
							}
						},
						error:function(data){
							alert("服务器繁忙请稍后重试")
							console.log(data)
						}
					});





				}
			})
		</script>
		</div>
	</body>
</html>
