<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="activity">
    <!--抢优惠-->
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/appointment.css?v=20180522" rel="stylesheet" type="text/css">
    <style type="text/css">

        @font-face {
            font-family: 'dinot-bold';   /*字体名称*//*/font/*/
            src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');       /*字体源文件*/
        }


        .w1210 .w
        {
            width:1170px;
        }
        .privilege
        {
            height: 170px;
            position: relative;
            color: #fff;
        }
        .pri_left
        {
            width: 325px;
            height: 100%;
            float: left;
        }
        .pri_center
        {
            width: 600px;
            margin-top: 28px;
            float: left;
        }
        .pri_center p
        {
            font-size: 16px;
            line-height: 32px;
            width: 590px;
            font-weight: bold;
            overflow: hidden;
        }
        .pri_center p b{
            display: block;
            float: left;
            width: 475px;
        }
        .pri_center p span
        {
            font-weight: bold;
            color: #fecb00;
            font-size: 20px;
            display: block;
            float: left;
        }
        .miaoshaDom .view_lqyhq {
            float: left;
            width: 320px;
            display: block;
            height: 170px;
            text-align: right;
        }
        .tehuiDom .view_lqyhq
        {
            float: left;
            width: 216px;
            margin: 49px 0 0 12px;
            height: 52px;
            background: #fecb00;
            font-size: 24px;
            text-align: center;
            line-height: 52px;
            color: #5a0000;
        }
        .xsmssm
        {
            position: absolute;
            right: 30px;
            top: 28px;
            font-size: 14px;
        }
        .miaoshaDom .yhqsygz
        {
            position: absolute;
            right: 40px;
            bottom: 25px;
            font-size: 16px;
            color: #fff;
        }
        .tehuiDom .yhqsygz
        {
            position: absolute;
            right: 122px;
            bottom: 38px;
            font-size: 16px;
            color: #fff;
        }
        .yhqsygz:hover
        {
            color: #fff;
            text-decoration: underline;
        }
        .pri_xsms, .pri_xsms .yhqsygz
        {
            color: #ffff54;
        }
        .pri_xsms .yhqsygz:hover
        {
            color: #ffff54;
        }
        .vfloat_yh span
        {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 13px;
            bottom: 8px;
        }
        .title_tc{font-size: 26px;text-align: center;}
        .tbForm input.inputStyle{ width: 235px;}
        .btn_tc{width: 330px;line-height: 45px;display: block;margin: 0px auto 5px;background: #ff6600;
            color: #FFF;font-size: 20px;text-align: center;}
        .vfloat_yh
        {
            position: fixed;
            left: 50%;
            margin-left: 460px;
            top: 310px;
            z-index: 99;
            display: none;
        }
        .libao .pri_left
        {
            width:516px;
        }
        .libao .pri_center
        {
            margin-top:13px;
        }
        .libao .pri_center p
        {
            font-size:16px;
            line-height:50px;
            margin:0;
        }
        .libao .pri_center span
        {
            font-size:16px;
        }
        .libao .view_lqyhq
        {
            margin:44px 21px 0 0;
            float:right;
        }
        .libao .yhqsygz
        {
            font-size:16px;
            color:#ffde00;
            margin:7px 40px 0 0;
            float:right;
        }
        .villa_libao
        {
            position: fixed;
            right: 0;
            z-index: 100;
            top: 50%;
            margin-top: -100px;
        }
        .favorable{
            background: url(https://static.fangxiaoer.com/web/images/qiangyouhui/zigebd.png) no-repeat;
            padding-left: 0;
            width: 1170px;
            margin: 0 auto;
            color: #fff;
            height: 125px;
            box-shadow: 3px 4px 10px rgba( 154, 31, 2,0.5 );

        }
        .favorable>ul{
            float: left;
            width: 398px;
            /* padding-top: 30px; */
            padding-left: 95px;
            height: 100%;
        }
        .favorable>ul li{
            font-size: 18px;
            line-height: 28px !important;
            font-family: dinot-bold;
        }
        .favorable>ul .discount{
            /*font-weight: bold;*/
            font-size: 18px;
        }
        .favorable>div{
            float: left;
            padding-left:50px;
            width: 368px;
            margin-top: 35px;
            position: relative;
        }
        .favorable>div>p {
            color: #fff;
            font-size: 18px;
            line-height: 25px;
            margin: 0 auto;
            /* font-weight: bold; */
            /* width: 130px; */
            float: left;
        }
        .favorable>div p span{ color: #ffe59b;
            display: inline-block;
            font-size: 22px;
            margin-bottom: 3px;
            font-weight: bold;}
        .favorable>div .prices{,
        color: #fff;
            width: auto;
            line-height: 10px;
        }
        .favorable>div .prices span{
            font-size:58px;
            font-weight: bold;
            font-family: dinot-bold;
            color: #fff;
            margin-top: 20px;
        }
        .favorable a {
            width: 150px;
            height: 50px;
            line-height: 50px;
            display: block;
            text-align: center;
            color: #f2243d;
            font-size: 20px;
            cursor: pointer;
            overflow: hidden;
            border-radius: 25px;
            /* padding-top: 15px; */
            float: right;
            margin: 40px 56px 0 0;
            background-color: #fff;
            box-shadow: 0px 3px 13px rgba( 154, 31, 2,0.5 );
        }
        .bsyhja {
            padding: 20px;background: #f6e2c1;width: 1170px;margin: 0 auto;height: 130px;
        }

        .activity_vertify .border {
            position: relative;
            border: 1px solid #fff;
            padding-left: 0;
            width: 283px;
            margin-left: 72px;
            position: relative;
            height: 40px;
            margin-top: 42px;
        }
        .activity_vertify>div p span{
            font-weight:normal;
        }
        .activity_vertify .border p {

            text-align: center;



            font-weight: bold;
            font-size: 30px;
            padding-left: 15px;
            padding-right: 15px;
            float: none;
        }
        .activity_vertify>div p .name_wei{display: inline-block;
            background-color: #f43139;
            width: auto;
            position: relative;
            top: -13px;
            padding-left: 15px;
            padding-right: 15px;
            min-width: 140px;
            color: #fff;
            font-size: 20px;
        }
        .activity_vertify>div p .zige_wei{display: inline-block;
            background-color: #f43139;
            width: auto;
            font-size: 30px;
            color: #ffe59b;
            font-weight: bold;
            position: relative;
            top: -4px;
            padding-left: 15px;
            padding-right: 15px;
            min-width: 140px;}
        .activity_vertify>ul{padding-left: 172px; width: 420px}
        .favorable{ position: relative}
        .favorable table{ width: 100%; height: 100%;}
        .favorable .border_bottom {
            border-radius: 14px 14px 0 0;
            width: 26px;
            height: 17px;
            position: absolute;
            bottom: -1px;
            left: 431px;
            margin: 0;
            background-color: #fff;
            padding: 0;
            box-shadow: 2px 7px 10px rgba( 154, 31, 2,0.5 )inset, 1px 8px 10px rgba( 255, 255, 255,1 );
        }
        .favorable .border_top{border-radius:0px 0px 7px 7px;  width: 26px; height: 14px; position: absolute;
            top: 0; left:431px;margin: 0; padding: 0 }
        .favorable .border_top {
            border-radius: 0px 0px 12px 12px;
            width: 26px;
            height: 14px;
            position: absolute;
            top: 0px;
            left: 431px;
            margin: 0;
            padding: 0;
            background-color: #fff;

        }
        .nope{background: url(https://static.fangxiaoer.com/web/images/qiangyouhui/none.png) !important;}
    </style>

    <script type="text/javascript">
        var sessionId = '[[${#session?.getAttribute('sessionId')}]]';
        var phone = '[[${#session?.getAttribute('phoneNum')}]]';
        var userName= '[[${#session?.getAttribute('userName')}]]';
        var houseId = [[${projectId}]];
        var projectType= [[${projectType}]];
        var isOrder = false;
        $(document).ready(function () {
//            var a = $(".newHouseViewpro_name p").val();
//            alert(a);
            $("#ProjectName-Title").html($(".pro_name p").html())
            $("#pwdli").hide();
            $("#yz").val("")
            $(".mk").hide();$(".tcbg").hide();
            GetData(houseId,projectType);
            // IsRegister();
            //关闭登陆弹出框20160726
            $("#loginClose").click(function () {
                $("#login").hide();
                $("#loginzhezhao").hide();
            });
        });
        function ShowMiaosha() {
            if ($(".miaoshaDom")) {
                $(".miaoshaDom").show();
            }
            if ($(".tehuiDom")) {
                $(".tehuiDom").hide();
            }
        }

        function GetData(houseId,projectType) {
            console.log(projectType)
            $.ajax({
                type: "POST",
                url: "/viewActivity2",
                data: {sessionId: sessionId, projectId: houseId,projectType:projectType,activityType:11},
                dataType: "json",
                success: function (data) {
                    msg = data.msg
                    data=data.content
                    if (data.length > 0) {
                        if(data.state != "0") {
                            isOrder = true;
                        }
                        BindHuoDong(data,msg);
                    }else {
                        $("#huodongDom").hide();
                    }
                }
            });
        }
        function timer(intDiff, timei) {
            window.setInterval(function () {
                var day = 0,
                    hour = 0,
                    minute = 0,
                    second = 0; //时间默认值
                if (intDiff > 0) {
                    day = Math.floor(intDiff / (60 * 60 * 24));
                    hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
                    minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
                    second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
                }
                if (minute <= 9) minute = '0' + minute;
                if (second <= 9) second = '0' + second;
                var timeing = "<em>" + day + "</em>天<em>" + hour + "</em>时<em>" + minute + "</em>分<em>" + second + "</em>秒"
                $("#timeing" + timei).html(timeing);
                intDiff--;
            }, 1000);
        }
        function timer_login(){
            var intDiff;
            for (i = 1; i <= $(".time").length; i++) {
                intDiff = $("#endTime" + i).val();
                timer(intDiff, i);
            }
        }
        function GetDatetime() {
            var d = new Date(), str = '';
            str += d.getFullYear() + '-';
            str += d.getMonth() + 1 + '-';
            str += d.getDate() + '-';
            str += d.getHours() + ':';
            str += d.getMinutes() + ':';
            str += d.getSeconds();
            //            var str="2015-9-23 13:54:11";
            return str;
        }

        function getcookie(objname) {//获取指定名称的cookie的值
            var val = false;
            var arrstr = document.cookie;
            if (arrstr.indexOf(objname) >= 0) {
                val = true;
            }

            return val;
        }
        function getCookieValue(cookie_name)
        {
            var allcookies = document.cookie;
            var cookie_pos = allcookies.indexOf(cookie_name);   //索引的长度

            // 如果找到了索引，就代表cookie存在，
            // 反之，就说明不存在。
            if (cookie_pos != -1)
            {
                // 把cookie_pos放在值的开始，只要给值加1即可。
                cookie_pos += cookie_name.length + 1;      //这里容易出问题，所以请大家参考的时候自己好好研究一下
                var cookie_end = allcookies.indexOf(";", cookie_pos);

                if (cookie_end == -1)
                {
                    cookie_end = allcookies.length;
                }

                var value = unescape(allcookies.substring(cookie_pos, cookie_end));         //这里就可以得到你想要的cookie的值了。。。
            }
            return value;
        }

        function comptime(beginTime,endTime) {
            var beginTimes = beginTime.substring(0, 10).split('-');
            var endTimes = endTime.substring(0, 10).split('-');
            beginTime = beginTimes[1] + '-' + beginTimes[2] + '-' + beginTimes[0] + ' ' + beginTime.substring(10, 19);
            endTime = endTimes[1] + '-' + endTimes[2] + '-' + endTimes[0] + ' ' + endTime.substring(10, 19);
            alert(beginTime + "-" + endTime);
            alert(Date.parse(endTime));
            alert(Date.parse(beginTime));
            var a = (Date.parse(endTime) - Date.parse(beginTime)) / 3600 / 1000;
            if (a < 0) {
                return 1;
            } else if (a > 0) {
                return 0;
            } else if (a == 0) {
                return 1;
            } else {
                return 'exception';
            }
        }
        function BindHuoDong(data, msg) {
            var miaosha = "";
            var tehui = "";
            var libao=""
            var isshow = 0;
            var j = 0;
            for (var i = 0; i < data.length; i++) {
                if (data[i].ActivityType == "7") {
                    isshow = data[i].isShow;
                    miaosha = "<input type='hidden' id='endTime1' value='"+data[i].datetime+"'>" +
                        "<input type='hidden' id='beginTime' value='"+data[i].isShow+"'>" +
                        "<div class='w privilege pri_xsms miaoshaDom' style='background: url(https://static.fangxiaoer.com/web/images/sy/house/view/view_xsms.png);'>" +
                        " <div class='pri_left'></div><div class='pri_center'>" +
                        "<p>" +
                        "<span>现场折扣：</span>" + data[i].PropreDescription + "</p>" +
                        "<p>" +
                        "<span style='padding-top: 27px;'>独家补贴：</span>" + data[i].ProDescription + "</p>" +
                        "<p>" +
                        "<span>抢券时间：</span>" + data[i].VoStarTime + "--" + data[i].VoEndTime + "</p>" +
                        "</div>" +
                        "<a href='javascript:void(0)' data-href='"+data[i].ActivityID+"' class='view_lqyhq a-login-href' title='领取优惠券'></a>" +
                        " <div class='xsmssm time'><span>距开始还有：</span><span id='timeing1'><em></em>天<em></em>时<em></em>分<em></em>秒</span></div>" +
                        "<a href='https://event.fangxiaoer.com/20150930.htm' target='_blank' class='yhqsygz'>优惠券使用规则</a></div>";
                }
                else if (data[i].ActivityType == "11"&& j ==0) {
                    j=1
                    var htou = "";
                    var favorableTxt=  data[i].PropreDescription.split(" ")
                    var favorableClass=""
                    if(favorableTxt.length>1){
                        favorableTxt=favorableTxt[0]+"<br>"+favorableTxt[1]
                    }else{
                        favorableTxt=favorableTxt[0]
                        favorableClass="style='line-height:52px'";
                    }
                    var backGround=""
                    var fallprice = data[i].FaceValue;
                    if (fallprice == 0 || fallprice == null) {
                        fallprice = 5000;
                    }
                    if($("#fxe_status").val()=="vip"){
                        backGround="style='background: url(https://static.fangxiaoer.com/web/images/qiangyouhui/zigebd.png) no-repeat;width:1170px;padding-left: 0px;'"
                    }else if($("#fxe_status").val()=="normal"){
                        backGround=""
                    }
                    var projectTypeClass = "";
                    var descs = '';
                    var projectN = data[i].projectName;
                    //判断资格或者钱
                    var favClass ;
                    if(fallprice == -1){
                        descs='<p><span class="name_wei">'+projectN+'</span><span class="zige_wei">优惠资格</span></p>';
                        favClass="zige"
                    }else{
                        descs = '<p><span style=\'padding-top: 27px;\'>独家补贴</span><br></p>'+"<p class='prices'>￥<span>"+fallprice+"</span></p>";
                        favClass="dujia"
                    }
                    //判断是否领取过
                    var orderStatus
                    var nope=""
                    if(data[i].state ==0){
                        orderStatus=  "<a href='javascript:void(0)' data-href='"+data[i].ActivityID+"' class='view_lqyhq a-login-href-tehui' >立即<b>开抢</b></a>";
                    }else {
                        nope = "nope"
                        // orderStatus ="<a href='javascript:void(0)'>已领取</a>"
                        orderStatus =""
                    }
                    var divinfo = "<div class='favorable "+nope+"' "+backGround+">"
                    if(fallprice == -1){
                        divinfo = "<div class='favorable activity_vertify "+nope+"' "+backGround+">"
                    }
                    tehui = htou +
                        "<div class='"+projectTypeClass+"'>"+ divinfo +
                        "<div class='border_top'></div>"+
                        "<div class='border_bottom'></div>"+
                        "<div class='border'>"+
                        descs +
                        "</div>"+
                        "<ul>"+
                        //                            "<li></li>"+
                        "<table>" +
                        "<tr>" +
                        "<td>" +
                        "<li class='discount' "+favorableClass+">"+favorableTxt+"</li>"+
                        "<li>抢券时间：" + data[i].StarTime + "-" + data[i].EndTime + "</li>"+
                        "</td>"+
                        "</tr>"   +
                        "</table>"+

                        "</ul>"+
                        orderStatus
                    "</div>"+
                    "</div>"

                }
                else if (data[i].ActivityType == "13" && projectType!="2") {
                    libao = " <div class='w privilege  libao' style='background: url(https://static.fangxiaoer.com/web/images/sy/house/view/view_youhui.jpg);'>" +
                        " <div class='pri_left'></div><div class='pri_center'>" +
                        "<p>" +
                        "<span>项目特色：</span>" + data[i].PropreDescription + "</p>" +
                        "<p>" +
                        "<span>超值优惠：</span> " + data[i].ProDescription + "</p>" +
                        "<p>" +
                        "<span>抢券时间：</span>" + data[i].StarTime + "--" + data[i].EndTime + "</p>" +
                        "</div>" +
                        "<a href='javascript:void(0)' data-href='"+data[i].ActivityID+"' class='view_lqyhq a-login-href-tehui' title='领取礼包'><img src='https://static.fangxiaoer.com/web/images/sy/house/view/btn_youhui.jpg'></a><br>" +
                        "<a href='https://event.fangxiaoer.com/20150930.htm' class='yhqsygz' target='_blank'>【优惠券使用规则】</a></div>";
                }
                else if (data[i].ActivityType == "13" && projectType=="2") {
                    libao ="<a href='javascript:void(0)' data-href='"+data[i].ActivityID+"' class='villa_libao a-login-href-tehui' title='领取礼包'><img src='https://static.fangxiaoer.com/web/images/sy/villa/view/villa_libao.png'></a></div>";
                }
                else if (data[i].ActivityType == "14" && ("-1"=="2" ||"-1"=="-1")) {
                    libao = " <div class='w privilege  libao' style='background: url(https://static.fangxiaoer.com/web/images/sy/house/view/view_yh.jpg);'>" +
                        " <div class='pri_left'></div><div class='pri_center'>" +
                        "<p>"
                        + data[i].PropreDescription + "</p>" +
                        "<p>"
                        +data[i].ProDescription+"</p>" +
                        "<p>" +
                        "使用期限："+ data[i].StarTime + "--" + data[i].EndTime +"</p>" +
                        "</div>" +
                        "<a href='/house/giftIndent.aspx?projectid="+843+"&activityid="+data[i].ActivityID+"'  class='view_lqyhq a-login-href-tehui a-tehui' title='领取礼包' target='_blank'><img src='https://static.fangxiaoer.com/web/images/sy/house/view/btn_libao.png'></a><br>" +
                        "<a href='https://sy.fangxiaoer.com/event/2017/0213/' class='yhqsygz' target='_blank'>【抢礼包使用规则】</a></div>";
                }


            }

            if(libao!=""){
                $("#huodongDom").html(libao);
                $("#xiaoerDom").hide();
            }else if(miaosha!=""){
                $("#huodongDom").html(miaosha);
                $("#xiaoerDom").hide();
            }else if(tehui!=""){
                $("#huodongDom").html(tehui);
                $("#xiaoerDom").hide();
            }else{
                $("#huodongDom").hide();
            }

            if ($(".miaoshaDom")) {
                timer_login();
            }

            if($("#beginTime").val()=="1"){
                $(".a-login-href").click(function () {
                    activityId=$(this).attr("data-href");
                    IsAddActivity(function() {
                        LoginClick();
                        $(".tcbg").show();
                    });

                });
            }
            $(".a-login-href-tehui").click(function () {
                if ($(this).hasClass("a-tehui")) {
                } else {
                    $(".error_libao").html("")
                    activityId=$(this).attr("data-href");
                    IsAddActivity(function() {
                        if(sessionId!=null &&sessionId!=""){
                            $("#mobile").val(phone)
                            $('#mobile').attr('readonly','readonly');
                            $("#username").val(userName);
                            $("#validateCodeli").hide();
                        }
                        $(".tcbg").show();
                    });
                }

            });

            $(".vfloat_yh_close").click(function() {
                $(this).parents(".vfloat_yh").hide();
            });
            $("#loginClose").click(function () {
                $("#login").hide();
                $("#loginzhezhao").hide();
            });
            $(window).scroll(function() {
                if ($(window).scrollTop() > 200) {
                    $(".vfloat_yh").show();
                } else {
                    $(".vfloat_yh").hide();
                }
            });

        }

        function LoginClick() {
            phone = "";
            if (phone&&phone!="") {
                $("#mobile").val(phone);

                $("#validateCodeli").hide();
                document.getElementById('mobile').readOnly=true;
            }
        }
        function IsRegister() {
            $("#mobile").blur( function() {
                var mobile = $("#mobile").val();
                if (mobile&&mobile.length==11) {
                    $.ajax({
                        type: "POST",
                        url: "https://ltapi.fangxiaoer.com/apiv1/base/checkIsMember",
                        data: { mobile: $("#mobile").val() },
                        dataType: "json",
                        success: function (data) {
                            if (data.status=="1") {
                                $("#pwdli").show();
                                $("#validateCodeli").hide()
                                console.log($(".fxe_validateCodeli").hide())
                            }
                        }
                    });
                }
            } );

        }
        var loginType ="";
        $(function(){
            $("#shenqinghuodong").click(function () {
                if(sessionId!=null &&sessionId != ''){
                    var cb=$(".cbed").attr("checked");
                    if (cb != "checked") {
                        alert("您未勾选同意选项");
                        return false
                    }
                    var inputname = $("#username").val();
                    if (inputname == "") {
                        $(".ename").fadeIn();
                    } else{
                        $(".ename").fadeOut();
                        $.ajax({
                            type: "POST",
                            url: "/orderActivity",
                            data: { sessionId: sessionId, userName: $("#username").val(), activityId: activityId, code: "" },
                            dataType: "json",
                            success: function (data) {
                                switch (data.status) {
                                    case 1:
                                        $(".chenggong").show();
                                        $("#mkdom").hide();
                                        $(".error_libao").html("");
                                        break;
                                    case 0:
                                        $(".error_libao").html(data.msg);
                                        $(".error_libao").show()
                                        $(".cha").click(function(){
                                            location.reload()
                                        });
                                        break;
                                }

                            }
                        });}
                }else {
                    if (!phone || phone == "") {
                        phone = $("#mobile").val();
                    }
                    var back = submitapply();
                    if (back == 1) {
                        $.ajax({
                            type: "POST",
                            url: "/login",
                            data: {
                                telNumber: phone, //电话
                                password: $("#yz").val(), //电话
                                goods:'1',
                                registFromUrl: window.location.href
                            },
                            async: false,
                            success: function (data) {
                                if(data.status == 1){
                                    loginType = data.status;
                                    sessionId = data.content.sessionId;
                                    $(".error_libao").html("");
                                    $(".cha").click(function(){
                                        location.reload()
                                    });
                                }else{
                                    if(data.msg =='请使用经纪人专享的系统/APP进行操作'){
                                        $(".error_libao").html('经纪人账号不可领取优惠');
                                    }
                                    else {
                                        $(".error_libao").html(data.msg);
                                    }
                                    $(".error_libao").show()
                                    $(".cha").click(function(){
                                        location.reload()
                                    });
                                }
                            }
                        });
                        if (loginType == 1) {
                            $.ajax({
                                type: "POST",
                                url: "/orderActivity",
                                data: { sessionId: sessionId, userName: $("#username").val(), activityId: activityId, code: "" },
                                dataType: "json",
                                success: function (data) {
                                    switch (data.status) {
                                        case 1:
                                            $(".chenggong").show();
                                            $("#mkdom").hide();
                                            $(".error_libao").html("");
                                            break;
                                        case 0:
                                            $(".error_libao").html(data.msg);
                                            $(".error_libao").show()
                                            break;
                                    }

                                }
                            });
                        }
                    }
                }
            });
        })
        function IsAddActivity(callback) {
            if (isOrder) {
                $("#mkdom").show();
                $(".shibai").hide();
            } else {
                $("#mkdom").show();
                //$("#mkdom").hide();//抢礼包修改
                //$(".shibai").show();
            }
            callback.call();
        }
    </script>
    <div id="huodongDom">
    </div>
    <div class="mk" style="display: none" id="mkdom">
        <div class="main"  style="width: 320px !important;">
            <div class="cha">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" />
            </div>
            <div class="title_tc" id="ProjectName-Title">

            </div>
            <div class="tbForm">
                <ul>
                    <li><label><input name="" id="username" type="text" class="inputStyle" placeholder="请输入真实姓名" value="" /></label></li>
                    <li><label><input class="txt_reg inputStyle" type="text" maxlength="11" name="mobile" id="mobile" placeholder="请输入手机号" value="" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')" /></label></li>
                    <li id="validateCodeli" class="fxe_validateCodeli"><label><input name="imgCode" style="width:135px;" id="yz" type="text" class="inputStyle widthMd" placeholder="请输入验证码" value="" /></label> <p class="yz">
                        <a href="javascript:void(0);" class="requ_btn" id="validateCode" style="display: none;"></a>
                        <a href="javascript:void(0);" class="requ_btn" id="ReSendValidateCoad"> 获取验证码 </a> </p> </li>
                    <li id="pwdli"><p>密　码</p><label><input name="" autocomplete="new-password" id="userpassword" type="password" class="inputStyle widthBd" placeholder="输入密码" value="" /></label><p class="mima"><a href="https://my.fangxiaoer.com/RetrPassword.aspx">忘记密码</a></p> </li>
                </ul>
            </div>
            <div class="cl"></div>
            <div style="overflow: hidden;height: 30px;">
                <div class="error ename" style="display: none;">请输入真实姓名</div>
                <div class="error emobile" style="display: none;">请输入正确手机号</div>
                <div class="error eyz"></div>
                <div class="error epassword" style="display: none;">请输入密码</div>

                <div class="error  error_libao" style="display: none"></div>
            </div>
            <a class="btn_tc" style="cursor:pointer" id="shenqinghuodong">立即申请</a>
            <div class="youhuixieyi">
                <span class="cbed checked" checked="checked"><img src="https://static.fangxiaoer.com/web/images/sy/house/FreefoneSucceed.png"></span>我已阅读并接受
                <a href="https://info.fangxiaoer.com/About/protocol" target="_blank"><font color="#ff333">《房小二网用户服务协议》</font></a>及
                <a href="https://info.fangxiaoer.com/About/policy" target="_blank"><font color="#ff333">《房小二网隐私政策》</font></a>
            </div>
        </div>
    </div>

    <div class="chenggong mk" style="margin-top:-195px; display: none">
        <div class="main" style="height:230px; width: 320px;">
            <div class="cha" style="top: 22px; right: 22px;"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div class="title_tc" style="text-align: center;line-height: 40px; padding-top: 36px;" th:text="${houseInfo.projectName}"></div>
            <div style="font-size:20px;margin-top:20px; color:#ff5200; text-align: center">恭喜您，领取成功</div>
            <a href="https://my.fangxiaoer.com/index.aspx" style="width: 320px;height:40px; line-height: 40px;margin:0 auto; background: #ff5200; color:#fff; font-size: 16px;display: block;text-align: center;border-radius: 3px;margin-top: 30px;">进入订单管理</a>
        </div>
    </div>


    <div class="shibai mk" style="margin-top:-115px; display: none">
        <div class="main" style="height:230px;">
            <div class="cha"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div class="title_tc" th:text="${houseInfo.projectName}"></div>
            <div style="font-size:26px;margin-top:20px; color:#ff5200; text-align: center">您已参加过此活动</div>
            <a href="https://my.fangxiaoer.com/index.aspx" style="width: 320px;height:40px; line-height: 40px;margin:0 auto; background: #ff5200; color:#fff; font-size:16px;display: block;text-align: center;margin-top: 30px;">进入订单管理</a>
        </div>
    </div>



    <div class="tcbg" style="opacity: 0.7; cursor: pointer; z-index:99999; height: 100%; background-color: rgb(0, 0, 0); position:fixed"></div>
    <script type="text/javascript">
        $("#ReSendValidateCoad").click(function() {
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            mobile = $("#mobile").val();
            if (mobile == "" || (!reg.test(mobile))) {
                $(".emobile").fadeIn();
            } else {
                $(".emobile").fadeOut();
                var r = 0;
                $.ajax({
                    type: "POST",
                    data: { mobile: mobile },
                    url: "/sendSmsCode",
                    async: false,
                    success: function (data) {
                        r = data;
                    }
                });


                times1();
            }
        });

        //验证码倒计时
        var wait = 60;
        function times1() {
            if (wait == 0) {
                $("#validateCode").hide();
                $("#ReSendValidateCoad").show().html("重新获取");
                wait = 60;
            } else {
                $("#validateCode").show().html("在" + wait + "秒后重发");
                $("#ReSendValidateCoad").hide();
                wait--;
                setTimeout(function () {
                        times1();
                    },
                    1000);
            }
        }

        $(".cbed").click(function () {
            var cb = $(".cbed").attr("checked");
            if (cb != "checked") {
                $(".cbed").attr("checked","checked")
                $(".cbed").addClass("checked")
            }else {
                $(".cbed").removeAttr("checked")
                $(".cbed").removeClass("checked")
            }
        })
        //提交表单验证
        function submitapply() {
            var back = 1;
            var inputname = $("#username").val();
            if (inputname == "") { $(".ename").fadeIn(); back=0 }
            else{$(".ename").fadeOut();}
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            inputmobile= $.trim($("#mobile").val());
            if (inputmobile=="" || (!reg.test(inputmobile)))
            { $(".emobile").fadeIn(); back = 0 }
            else{$(".emobile").fadeOut();}
            var inputpassword = $("#userpassword").val();
            $(".eyz").hide();
            $(".eyz").text("请输入验证码");
            var inputyz = $("#yz").val();
            if ($("#validateCodeli").css("display") != "none") {
                $(".epassword").fadeOut();
                if (inputyz == "") { $(".eyz").fadeIn(); back = 0 }
            }
            var cb=$(".cbed").attr("checked");
            if (cb != "checked") { alert("您未勾选同意选项"); return 0;}
            if (back == 1) {
                return 1;
            } else {
                return 0;
            }
        }
        $("#username,#mobile").focus(function () {
//            $(this).parents("li").next().fadeOut();
        })
        $("#yz,#userpassword").focus(function () {
            $(".eyz,.epassword").fadeOut();
        })
        $(".cbed").click(function(){
            var cb=$(this).attr("checked");
            if(cb=="checked"){$(".btn").css("background","#ff6600");}
            else{$(".btn").css("background","#666");}
        });
        $(".cha").click(function(){$(".mk").hide();$(".tcbg").hide();
            $(".mk input").val("");
            $(".error").hide();
            $("#pwdli").hide();
            $("#validateCodeli").show();
        })
    </script>
    <div class="modal-backdrop  in" id="loginzhezhao" style="display: none;"></div>
    <script>


    </script>
</div>
<div th:fragment="orderPrivActivity">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/layer.css" />
    <div class="mk" style="display: none" id="mkdom">
        <div class="main" >
            <div class="cha">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" />
            </div>
            <div class="title_tc" id="ProjectName-Title">

            </div>
            <div class="tbForm">
                <ul><input name="" id="thisActivID" type="hidden" value="" />
                    <li><label><input name="" id="username" type="text" class="inputStyle" placeholder="请输入真实姓名" value="" /></label></li>
                    <li><label><input class="txt_reg inputStyle" type="text" maxlength="11" name="mobile" id="mobile" placeholder="请输入手机号" value="" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')" /></label></li>
                    <li id="validateCodeli" class="fxe_validateCodeli"><label><input name="imgCode" style="width:135px;" id="yz" type="text" class="inputStyle widthMd" placeholder="请输入验证码" value="" /></label> <p class="yz">
                        <a href="javascript:void(0);" class="requ_btn" id="validateCode" style="display: none;"></a>
                        <a href="javascript:void(0);" class="requ_btn" id="ReSendValidateCoad"> 获取验证码 </a> </p> </li>
                    <!--<li id="pwdli"><p>密　码</p><label><input name="" id="userpassword" type="password" class="inputStyle widthBd" placeholder="输入密码" value="" /></label><p class="mima"><a href="https://my.fangxiaoer.com/RetrPassword.aspx">忘记密码</a></p> </li>-->
                </ul>
            </div>
            <div class="cl"></div>
            <div style="overflow: hidden;height: 30px;">
                <div class="error ename" style="display: none;">请输入真实姓名</div>
                <div class="error emobile" style="display: none;">请输入正确手机号</div>
                <div class="error eyz"></div>
                <div class="error epassword" style="display: none;">请输入密码</div>

                <div class="error  error_libao" style="display: none"></div>
            </div>
            <a class="btn_tc" style="cursor:pointer" id="shenqinghuodong">立即申请</a>
            <div class="youhuixieyi">
                <span class="cbed checked" checked="checked"><img src="https://static.fangxiaoer.com/web/images/sy/house/FreefoneSucceed.png"></span>我已阅读并接受
                <a href="https://info.fangxiaoer.com/About/protocol" target="_blank"><font color="#ff333">《房小二网用户服务协议》</font></a>及
                <a href="https://info.fangxiaoer.com/About/policy" target="_blank"><font color="#ff333">《房小二网隐私政策》</font></a>
            </div>
        </div>
    </div>
    <div class="tcbg" style="opacity: 0.7; cursor: pointer; z-index:99999; height: 100%; background-color: rgb(0, 0, 0); position:fixed"></div>

    <div class="chenggong mk" style="margin-top:-195px; display: none">
        <div class="main" style="height:230px; width: 320px;">
            <div class="cha" style="top: 22px; right: 22px;"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div class="title_tc" style="text-align: center;line-height: 40px; padding-top: 36px;" >
            </div>
            <div style="font-size:20px;margin-top:20px; color:#ff5200; text-align: center">恭喜您，领取成功</div>
            <a href="https://my.fangxiaoer.com/index.aspx" style="width: 320px;height:40px; line-height: 40px;margin:0 auto; background: #ff5200; color:#fff; font-size: 16px;display: block;text-align: center;border-radius: 3px;margin-top: 30px;">进入订单管理</a>
        </div>
    </div>
    <script>
        //获取用户数据
        var sessionId = '[[${#session?.getAttribute('sessionId')}]]';
        var phone = '[[${#session?.getAttribute('phoneNum')}]]';
        var userName= '[[${#session?.getAttribute('userName')}]]';
        //可领取的点击事件
        $(".canClick").click(
            function () {
                //判断活动是否开启
                var convenient =$(this).parent().find("#ActAviliable").val();
                if(convenient ==0){
                    alert("活动尚未开启，敬请期待")
                    return false;
                }
                //判断登录自带名字和手机号
                if(sessionId!=null &&sessionId!=""){
                    $("#mobile").val(phone)
                    $('#mobile').attr('readonly','readonly');
                    $("#username").val(userName);
                    $("#validateCodeli").hide();
                }
                $(".tcbg").show();
                $("#mkdom").show();
                var activID =$(this).parent().find("#ActivityID").val();
                $("#thisActivID").val(activID);
                var activName = $(this).parent().parent().find(".information").find(".name").find("h1").html();
                $(".title_tc").html(activName);
            }
        )

        //验证码倒计时
        var wait = 60;
        function times1() {
            if (wait == 0) {
                $("#validateCode").hide();
                $("#ReSendValidateCoad").show().html("重新获取");
                wait = 60;
            } else {
                $("#validateCode").show().html("在" + wait + "秒后重发");
                $("#ReSendValidateCoad").hide();
                wait--;
                setTimeout(function () {
                        times1();
                    },
                    1000);
            }
        }
        //提交表单验证
        function submitapply() {
            var back = 1;
            var inputname = $("#username").val();
            if (inputname == "") { $(".ename").fadeIn(); back=0 }
            else{$(".ename").fadeOut();}
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            inputmobile= $.trim($("#mobile").val());
            if (inputmobile=="" || (!reg.test(inputmobile)))
            { $(".emobile").fadeIn(); back = 0 }
            else{$(".emobile").fadeOut();}
            var inputpassword = $("#yz").val();
            $(".eyz").hide();
            $(".eyz").text("请输入验证码");
            var inputyz = $("#yz").val();
            if ($("#validateCodeli").css("display") != "none") {
                $(".epassword").fadeOut();
                if (inputyz == "") { $(".eyz").fadeIn(); back = 0 }
            }
            var cb=$(".cbed").attr("checked");
            if (cb != "checked") { alert("您未勾选同意选项"); return 0;}
            if (back == 1) {
                return 1;
            } else {
                return 0;
            }
        }
        //隐藏错误提示
        $("#yz,#userpassword").focus(function () {
            $(".eyz,.epassword").fadeOut();
        })
        //勾选协议
        $(".cbed").click(function(){
            var cb=$(this).attr("checked");
            // if(cb=="checked"){$(".btn").css("background","#ff6600");}
            // else{$(".btn").css("background","#666");}
        });
        $(".cbed").click(function () {
            var cb = $(".cbed").attr("checked");
            if (cb != "checked") {
                $(".cbed").attr("checked","checked")
                $(".cbed").addClass("checked")
            }else {
                $(".cbed").removeAttr("checked")
                $(".cbed").removeClass("checked")
            }
        })
        //关闭按钮
        $(".cha").click(function(){$(".mk").hide();$(".tcbg").hide();
            $(".mk input").val("");
            $(".error").hide();

            $("#pwdli").hide();
            $("#validateCodeli").show();
        })
        //验证码发送
        $("#ReSendValidateCoad").click(function() {
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            mobile = $("#mobile").val();
            if (mobile == "" || (!reg.test(mobile))) {
                $(".emobile").fadeIn();
            } else {
                $(".emobile").fadeOut();
                var r = 0;
                $.ajax({
                    type: "POST",
                    data: { mobile: mobile },
                    url: "/sendSmsCode",
                    async: false,
                    success: function (data) {
                        r = data;
                    }
                });
                times1();
            }
        });
        //申请事件
        var loginType ="";
        $(function(){
            $("#shenqinghuodong").click(function () {
                var activityId = $("#thisActivID").val();
                if(sessionId!=null &&sessionId != ''){
                    var cb=$(".cbed").attr("checked");
                    if (cb != "checked") {
                        alert("您未勾选同意选项");
                        return false
                    }
                    var inputname = $("#username").val();
                    if (inputname == "") {
                        $(".ename").fadeIn();
                    } else{
                        $(".ename").fadeOut();
                        $.ajax({
                            type: "POST",
                            url: "/orderActivity",
                            data: { sessionId: sessionId, userName: $("#username").val(), activityId: activityId, code: "" },
                            dataType: "json",
                            success: function (data) {
                                switch (data.status) {
                                    case 1:
                                        $(".chenggong").show();
                                        $("#mkdom").hide();
                                        $(".error_libao").html("");
                                        location.reload();
                                        break;
                                    case 0:
                                        $(".error_libao").html(data.msg);
                                        $(".error_libao").show()
                                        $(".cha").click(function(){
                                            location.reload()
                                        });
                                        break;
                                }

                            }
                        });}
                }else {
                    if (!phone || phone == "") {
                        phone = $("#mobile").val();
                    }
                    var back = submitapply();
                    if (back == 1) {
                        $.ajax({
                            type: "POST",
                            url: "/login",
                            data: {
                                telNumber: phone, //电话
                                password: $("#yz").val(), //电话
                                goods:'1',
                                registFromUrl: window.location.href
                            },
                            async: false,
                            success: function (data) {
                                if(data.status == 1){
                                    loginType = data.status;
                                    sessionId = data.content.sessionId;
                                    $(".error_libao").html("");
                                    $(".cha").click(function(){
                                        location.reload()
                                    });
                                }else{
                                    if(data.msg =='请使用经纪人专享的系统/APP进行操作'){
                                        $(".error_libao").html('经纪人账号不可领取优惠');
                                    } else {
                                        $(".error_libao").html(data.msg);
                                    }
                                    $(".error_libao").show()
                                    $(".cha").click(function(){
                                        location.reload()
                                    });
                                }
                            }
                        });
                        if (loginType == 1) {
                            $.ajax({
                                type: "POST",
                                url: "/orderActivity",
                                data: { sessionId: sessionId, userName: $("#username").val(), activityId: activityId, code: "" },
                                dataType: "json",
                                success: function (data) {
                                    switch (data.status) {
                                        case 1:
                                            $(".chenggong").show();
                                            $("#mkdom").hide();
                                            $(".error_libao").html("");
                                            break;
                                        case 0:
                                            $(".error_libao").html(data.msg);
                                            $(".error_libao").show()
                                            break;
                                    }

                                }
                            });
                        }
                    }
                }
            });
        })


    </script>
</div>
<!--在线支付-->
<div th:fragment="projectPay">
    <div th:if="${projectPay ne null}" class="favorable zhifu">
        <div class="payL">
            <p class="payLp1" th:text="${houseInfo.projectName}"></p>
            <p class="payLp2">线上支付</p>
        </div>
        <div class="payR">
            <div class="payC">
                <p class="payCp1" style="margin-bottom: 8px;margin-top: 28px;overflow: hidden;">
                    <span class="payCp1Net" th:text="${projectPay.payDesc+((projectPay.payBrief eq null or projectPay.payBrief eq '')?'':('('+projectPay.payBrief+')'))}"></span>
                </p>
                <p class="payCp2" style="position:static;bottom: 0;">
                    <span>抢券时间：</span>
                    <span class="payCp2Time" th:text="${projectPay.startTime+'-'+projectPay.endTime}"></span>
                </p>
            </div>
            <a href="javaspript:(0)" onclick="topay()">
                <div class="payCy">
                    <img class="payCyImg" src="https://static.fangxiaoer.com/web/images/sy/house/payCy.png" alt="">
                </div>
            </a>
        </div>
    </div>
    <!--报名弹窗-->
    <!--线上支付立即参与开始-->
    <!--    弹窗-->
    <div class="tcbg" style="opacity: 0.7; cursor: pointer; z-index:99999; height: 100%; background-color: rgb(0, 0, 0); position:fixed"></div>
<!--    支付前验证-->
    <div  class="popPay">
            <div  th:if="${projectPay ne null}">
                <img class="payClose" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="" data-type="0" onclick="payClose(this)">
                <div class="popPayP1" th:text="${houseInfo.projectName}"></div>
                <div class="popPayP2" th:text="${'【'+projectPay.payDesc+'】'}"></div>
                <!--<div class="popPayP3" th:if="${projectPay.payBrief eq null or projectPay.payBrief eq ''}" th:text="${'('+projectPay.payBrief+')'}"></div>-->
                <div class="popPayP3" th:text="${projectPay.payBrief eq null or projectPay.payBrief eq ''? '&nbsp;':'('+projectPay.payBrief+')'}"></div>
            </div>
            <div class="popCet" th:if="${projectPay ne null}">
                <input id="activityId" type="hidden" th:value="${projectPay.id}">
                <input type="hidden" name="body" id="body" th:value="${projectPay.payDesc + '（'+projectPay.payBrief+'）'}">
                <input id="memberName" type="text" placeholder="输入您的姓名">
                <input id="pay_phone" readonly unselectable='on' type="text" value="ph">
                <input type="button" th:value="${'确认支付'+( #strings.toString(projectPay.payPrice).contains('.')? #strings.toString(projectPay.payPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : projectPay.payPrice)+'元'}" class="popSure" data-type="3" onclick="goParticipate(this)">
                <div>
                    <div class="checkagreeInput" style="margin: 3px auto 10px auto;width: 410px">
                        <i id="checkagree1" class="checkimg checked cheimg4"></i><div>
                        我已阅读并同意
                        <th:block th:if="${projectPay.payContent ne null and projectPay.payContent ne ''}" >
                            <span data-type='1' onclick='goParticipate(this)'>《房小二网线上活动须知》</span>及
                        </th:block>
                        <span data-type="2" onclick="goParticipate(this)">《房小二网优惠服务协议》</span></div>
                    </div>
                </div>
            </div>
        </div>
<!--    房小二协议-->
        <div class="popPay" >
            <div th:if="${projectPay ne null}">
                <img class="payClose" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="" data-type="1" onclick="payClose(this)">

                <p class="popPayP1">房小二网线上活动须知</p>
            </div>
            <div class="popMess" th:if="${projectPay ne null}" style="font-size: 14px;color: #333;padding: 0px 20px;text-align: left;height: 265px;overflow: auto;margin-top: 20px;">
                <p th:utext="${projectPay.payContent}"></p>
            </div>
        </div>
<!--    房小二协议-->
        <div class="popPay">
            <div th:if="${projectPay ne null}">
                <img class="payClose" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="" data-type="2" onclick="payClose(this)">
                <p class="popPayP1">房小二网优惠服务协议</p>
            </div>
            <div th:if="${projectPay ne null}" class="div-text" style="font-size: 14px;color: #333;padding: 0px 20px;text-align: left;height: 265px;overflow: auto;margin-top: 20px;">
                <p>在房小二网平台（fangxiaoer.com）依据《用户注册协议》注册的用户，在同意本协议以下全部条款后，方有资格享受房小二网平台（以下简称“平台”）第三方商家提供的房小二网销售服务（以下简称“服务”）。您使用商家提供的服务即意味着同意受本协议约束，在您下单支付前请认真阅读本协议。</p>
                <p style="margin-top: 8px;"> 一、免责声明</p>
                <p>房小二网平台仅向用户提供技术服务平台，以便用户与平台入驻的第三方商家之间达成相关交易，商家通过房小二网平台向用户提供房产销售服务，房小二网平台并非交易的参与方，不对商家的任何口头、书面陈述或者向上传的商品信息及房产的真实性、合法性做任何明示或暗示的担保，或对此承担任何责任。除购房诚基金外，房产购买（如签约、支付房款、交房）等流程皆由用户线下与第三方商家联系完成，所涉款项不通过房小二网平台支付，房小二网平台亦不对此承担任何义务与法律责任。如因房产交易产生纠纷的，本平台亦不承担任何责任与义务。</p>
                <p style="margin-top: 8px;">二、订单显示用户等信息提示</p>
                <p>用户同意将其在向房小二网平台提供的（有效证件包含身份证、护照、港澳居民回乡证、台湾居民台胞证、姓名、手机号等）信息在订单中体现，以便用于线下向用户退还诚意金时核对身份信息使用，请用户确保证件号等信息的真实性，避免在线下退款时因无法提供相应的身份证明而影响退款。</p>
                <p style="margin-top: 8px;"> 三、其他</p>
                <p>1、退款政策：为维护房小二网用户及第三方权益，房小二网平台针对未完成购房的用户提供诚意金支付成功后退款政策，经第三方确认后经由房小二网平台退款。</p>
                <p>2、商品详情页显示的房产价格因单位及汇率等因素，仅供参考，实际房产价格以第三方线下实际销售价格为准。</p>
            </div>
        </div>
<!--    二维码-->
        <div class="popPay" >
            <div th:if="${projectPay ne null}">
                <img class="payClose" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="" data-type="3" onclick="payClose(this)">
                <p class="popPayP1">请选择支付方式</p>
                <p class="popPayP4">您将支付<span style="color: #ff5200;" th:text="${'￥'+(#strings.toString(projectPay.payPrice).contains('.')? #strings.toString(projectPay.payPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : projectPay.payPrice)}"></span></p>
                <p class="popPayP5">此窗口将于<span style="color: #ff5200;"></span>秒后关闭，请您尽快完成支付</p>
            </div>
            <div class="popCode" th:if="${projectPay ne null}">
                <div id="wxPayCode" class="item-left"></div>
                <div  id="aliPayCode" class="item-right"></div>
                <div class="left-img"><img src="https://static.fangxiaoer.com/web/images/sy/house/wxpay.png" style="width: 26px;height: 24px;margin-right: 7px;"/>微信扫码支付</div>
                <div class="right-img"><img src="https://static.fangxiaoer.com/web/images/sy/house/zfbpay.png" style="width: 26px;height: 26px;margin-right: 7px;"/>支付宝扫码支付</div>
            </div>
        </div>
        <!--支付成功-->
        <div class="popPay" style="display: none;height: 300px;">
            <div th:if="${projectPay ne null}">
                <img class="payClose" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt="" data-type="4" onclick="payClose(this)">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/pay_success.png" style="width: 44px;height: 44px;display: block;margin: 35px auto 5px auto;"/>
                <div style="color: #FF5200;font-style: 16px;width: 100%;text-align: center;">支付成功</div>
                <div class="orderDesc" style=" width:85%;font-weight: bolder;margin: 23px auto 0px auto;font-size: 16px; color: #333;"></div>
                <div style="color: #999;margin: 13px auto 20px auto;font-size: 14px;">注：支付平台会有延迟，请耐心等待</div>
                <a href="https://my.fangxiaoer.com/viewPayOrderByMember" target="_blank">
                    <input  type="button" value="查看订单" style="width: 130px; height: 42px;background-color: #FF5200;text-align: center;font-size: 14px;color: #FFF;">
                </a>
            </div>
        </div>

    <script type="text/javascript" th:inline="javascript">
        var sessionId = [[${#session?.getAttribute('sessionId')}]];
        var phone = [[${#session?.getAttribute('phoneNum')}]];
        var userName= [[${#session?.getAttribute('userName')}]];
        var houseId = [[${projectId}]];
        var projectType= [[${projectType}]];
        var pay_time ;
        let width = $(".payScroll-item-b").width();
        $(".payScroll-b").width(width*2)
        function goParticipate(e) {
            let type = parseInt($(e).attr("data-type"));
//            $("#loginzhezhao").show();
            if(type==3){
                var wxPayCode = $.cookie(sessionId+"_wxPayCode");
                var aliPayCode = $.cookie(sessionId+"_aliPayCode");
                var orderId = $.cookie(sessionId+"_orderId");
                pay_time=120 ;//120秒
                var memberName = $("#memberName").val();
                var activityId = $("#activityId").val();
                var body_content = $("#body").val();
                if(memberName!=null&&""!=memberName){
                    if(activityId!=null&&""!=activityId&&body_content!=null&&""!=body_content){
                        $(".popPay").eq(type).show();
                        if(wxPayCode==null||aliPayCode==null||orderId==null){
                            $.ajax({
                                type: "POST",
                                url: "/pay_getQR",
                                data: {
                                    payMoney:[[${projectPay ne null?projectPay.payPrice:0}]],
                                    payType: "99",
                                    memberName:memberName,
                                    activityId:activityId,
                                    body:body_content,
                                    mobile:phone,
                                    sessionId:sessionId
                                },
                                async:false,
                                dataType: "json",
                                success: function (data) {
                                    console.log(data);
                                    if(data.status==1){
                                        var date = new Date();
                                        date.setTime(date.getTime()+100*1000);//100秒
                                        QR_data = data;
                                        wxPayCode =data.content.wxPayCode;//微信
                                        aliPayCode =data.content.aliPayCode;//支付宝
                                        orderId = data.content.orderId;//订单id
                                        $.cookie(sessionId+"_wxPayCode", wxPayCode, { expires: date, path: '/' });
                                        $.cookie(sessionId+"_aliPayCode",aliPayCode, { expires: date, path: '/' });
                                        $.cookie(sessionId+"_orderId",orderId, { expires: date, path: '/' });
                                        parseQR(wxPayCode,aliPayCode);
                                        pay_online_timer(type,orderId);
                                        if(data.msg!=null&&"error"==data.msg){
                                            $.removeCookie(sessionId+"_wxPayCode");
                                            $.removeCookie(sessionId+"_aliPayCode");
                                            $.removeCookie(sessionId+"_orderId");
                                            goParticipate(e);
                                        }
                                    } else {
                                        alert(data.msg);
                                        $("#loginzhezhao").hide();
                                        $(".popPay").eq(type).hide();
                                    }
                                }
                            });
                        }else{
                            parseQR(wxPayCode,aliPayCode);
                            pay_online_timer(type,orderId);
                        }
                    }else{
                        alert("参数错误");
                        return;
                    }
                }else{
                    alert("姓名不能为空");
                    return;
                }
            }
            if(type!=3){
                $(".popPay").eq(type).show();
            }
            $(".popPay").eq(type).siblings(".popPay").hide();
        }
        //转二维码
        var parseQR = function (wxPayCode,aliPayCode) {
            $('#wxPayCode').empty();
            $('#aliPayCode').empty();
            $('#wxPayCode').qrcode(
                {
                    width: 120,
                    height: 120,
                    text: wxPayCode
                });
            $('#aliPayCode').qrcode(
                {
                    width: 120,
                    height: 120,
                    text: aliPayCode
                });
        }
        //通用关闭弹窗
        function payClose(e) {
            let type = parseInt($(e).attr("data-type"));
            $(".popPay").hide();
            if(type==1||type==2){
                $(".popPay:eq(0)").show().siblings(".popPay").hide();
            }else{
                $("#loginzhezhao").hide();
            }
        }
        //登陆后的回调请求
        var login_callback = function (r) {
            if(r.status==1){
                 sessionId = r.content.sessionId ;
                 phone = r.content.mobile;
                 userName= r.content.username;
            }
            //登陆弹窗隐藏
            $("#login").hide();
            //支付验证弹窗弹出
            $("#pay_phone").attr("value",phone);
            $(".popPay:eq(0)").show();
        }
        //在线支付立即参与
       var topay = function(){
           // var sessionId = $.session.get('sessionId');
           if(sessionId==null||""==sessionId){
               loginNeedCallBack = 1;
               $("#loginzhezhao").show();
               $("#login").show();
           }else{
               $("#loginzhezhao").show();
               $("#pay_phone").attr("value",parseInt( phone));
               $(".popPay:eq(0)").show();
           }
       };
        //二维码窗口时间
        var pay_online_timer = function (type,orderId) {
            pay_success(orderId,type);
            if(pay_time==0){
                $(".popPay").hide();//倒计时结束，所有窗口均关闭
                $("#loginzhezhao").hide();
                return;
            }
            $(".popPayP5 span").html(pay_time);
            pay_time--;
            setTimeout(function () {
                if($(".popPay:eq("+type+")").is(':hidden')){
                    return ;
                }else{
                    pay_online_timer(type,orderId);
                }
            },1000);
        }
        //是否支付成功心跳检测
        var pay_success = function (orderId,type) {
            $.ajax({
                type:"POST",
                data:{orderId:orderId},
                url:"/orderPaySuccess",
                async:false,
                success:function (data) {
                    console.log(data);
                    if(data.content.status==1){//支付成功
                        $(".popPay").hide();//支付成功，所有窗口均关闭
                        $(".popPay:eq(4) .orderDesc").html(data.content.orderDesc);
                        $(".popPay:eq(4)").show();
                    }
                }
            });
        }
        var plenght = $(".zhifu .payCp1Net").text().length;
        if( plenght <= 28 ){
            $(".zhifu .payCp1Net").css({
                'display':'block',
                'margin-top':'12px'
            })
        }
    </script>
    <!--线上支付立即参与结束-->
</div>

<!--抢优惠活动-->
<div th:fragment="viewActivity">
    <th:block th:if="${viewActivity ne null} and ${#lists.size(viewActivity) gt 0 }" th:each="one:${viewActivity}">
        <!--<div  th:class="${one.state eq '0' ? 'payment':'has-get'}" >-->
        <!---->
        <div th:if="${one.FaceValue ne null and one.FaceValue ne '-1'}"  class="favorable-price tial" >
            <div class="border"><p><span style='padding-top: 27px;'>独家补贴</span><br></p><p class="prices">￥<span th:text="${one.FaceValue}"></span></p></div>
            <div class="payR">
                <div class="payC">
                    <p class="payCp1" style="margin-bottom: 8px;margin-top: 28px;overflow:hidden;">
                        <span class="payCp1Net" th:text="${ ''+(one.PropreDescription)}"></span>
                    </p>
                    <div class="payCp2" style="position: static;bottom: 0;">
                        <span>抢券时间：</span>
                        <span class="payCp2Time" th:text="${one.StarTime+'-'+one.EndTime}"></span>
                    </div>
                </div>
                <div th:if="${one.state eq '0'}" class="payStar">
                    <a  href='javascript:void(0)' th:data-href="${one.ActivityID}" class='view_lqyhq a-login-href-tehui'>
                        <img alt="" class="payStarImg" style="margin-top:15px;" src="https://static.fangxiaoer.com/web/images/sy/house/payKq.png">
                    </a>
<!--                    <p>已有<span th:text="${one.signNum}"></span>人报名团购</p>-->
                </div>
                <div th:if="${one.state ne '0'}" class="payStar">
                    <img alt="" class="payStarImg" src="https://static.fangxiaoer.com/web/images/sy/house/qiangyouhui_none.png" style="margin-left: 25px;">
                    <!--<p>已有<span th:text="${one.signNum}">12</span>份被领取</p>-->
                </div>
            </div>
        </div>
        <script>
            var plenght = $(".tial .payCp1Net").text().length;
            if( plenght <= 25 ){
                $(".tial .payCp1Net").css({
                    'display':'block',
                    'margin-top':'12px'
                })
            }
        </script>
        <div th:if="${one.FaceValue eq null or one.FaceValue eq '-1'}" class="favorable tial" >
                <div class="payL">
                    <div class="payLp1" th:text="${one.projectName}"></div>
                    <div class="payLp2">优惠资格</div>
                </div>
                <div class="payR">
                    <div class="payC">
                        <p class="payCp1" style="margin-bottom: 8px;margin-top: 28px;overflow:hidden;">
                            <span class="payCp1Net" th:text="${''+(one.PropreDescription)}"></span>
                        </p>
                        <div class="payCp2" style="position: static;bottom: 0;">
                            <span>抢券时间：</span>
                            <span class="payCp2Time" th:text="${one.StarTime+'-'+one.EndTime}"></span>
                        </div>
                    </div>
                    <div th:if="${one.state eq '0'}" class="payStar">
                        <a  href='javascript:void(0)' th:data-href="${one.ActivityID}" class='view_lqyhq a-login-href-tehui'>
                            <img alt="" class="payStarImg" style="margin-top:15px;" src="https://static.fangxiaoer.com/web/images/sy/house/payKq.png">
                        </a>
<!--                        <p>已有<span th:text="${one.signNum}"></span>人报名团购</p>-->
                    </div>
                    <div th:if="${one.state ne '0'}" class="payStar">
                        <img alt="" class="payStarImg" src="https://static.fangxiaoer.com/web/images/sy/house/qiangyouhui_none.png" style="margin-left: 25px;">
                        <!--<p>已有<span th:text="${one.signNum}">12</span>份被领取</p>-->
                    </div>
                </div>
        </div>
        <script>
            var plenght = $(".tial .payCp1Net").text().length;
            if( plenght <= 25 ){
                $(".tial .payCp1Net").css({
                    'display':'block',
                    'margin-top':'12px'
                })
            }
        </script>

    </th:block>

    <div class="mk" style="display: none;z-index:9999999;" id="mkdom">
        <div class="main" id="" style="width: 320px !important;">
            <div class="cha">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" />
            </div>
            <div class="title_tc" id="ProjectName-Title">

            </div>
            <div class="tbForm">
                <ul>
                    <li><label><input name="" id="username" type="text" class="inputStyle" placeholder="请输入真实姓名" value="" /></label></li>
                    <li><label><input class="txt_reg inputStyle" type="text" maxlength="11" name="mobile" id="mobile1" placeholder="请输入手机号" value="" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')" /></label></li>
                    <li id="validateCodeli" class="fxe_validateCodeli"><label><input name="imgCode" style="width:135px;" id="yz" type="text" class="inputStyle widthMd" placeholder="请输入验证码" value="" /></label> <p class="yz">
                        <a href="javascript:void(0);" class="requ_btn" id="validateCode" style="display: none;"></a>
                        <a href="javascript:void(0);" class="requ_btn" id="ReSendValidateCoad"> 获取验证码 </a> </p> </li>
                    <li id="pwdli"><p>密　码</p><label><input name="" autocomplete="new-password" id="userpassword" type="password" class="inputStyle widthBd" placeholder="输入密码" value="" /></label><p class="mima"><a href="https://my.fangxiaoer.com/RetrPassword.aspx">忘记密码</a></p> </li>
                </ul>
            </div>
            <div class="cl"></div>
            <div style="overflow: hidden;height: 30px;">
                <div class="error ename" style="display: none;">请输入真实姓名</div>
                <div class="error emobile" style="display: none;">请输入正确手机号</div>
                <div class="error eyz"></div>
                <div class="error epassword" style="display: none;">请输入密码</div>

                <div class="error  error_libao" style="display: none"></div>
            </div>
            <a class="btn_tc" style="cursor:pointer" id="shenqinghuodong">立即申请</a>
            <div class="youhuixieyi">
                <span class="cbed checked" checked="checked"><img src="https://static.fangxiaoer.com/web/images/sy/house/FreefoneSucceed.png"></span>我已阅读并接受
                <a href="https://info.fangxiaoer.com/About/protocol" target="_blank"><font color="#ff333">《房小二网用户服务协议》</font></a>及
                <a href="https://info.fangxiaoer.com/About/policy" target="_blank"><font color="#ff333">《房小二网隐私政策》</font></a>
            </div>
        </div>
    </div>
    <div class="shibai mk" style="margin-top:-115px; display: none">
        <div class="main" style="height:230px;">
            <div class="cha"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div class="title_tc" th:text="${houseInfo.projectName}"></div>
            <div style="font-size:26px;margin-top:20px; color:#ff5200; text-align: center">您已参加过此活动</div>
            <a href="https://my.fangxiaoer.com/index.aspx" style="width: 320px;height:40px; line-height: 40px;margin:0 auto; background: #ff5200; color:#fff; font-size:16px;display: block;text-align: center;margin-top: 30px;">进入订单管理</a>
        </div>
    </div>
    <div class="tcbg" style="opacity: 0.7; cursor: pointer; z-index:99999; height: 100%; background-color: rgb(0, 0, 0); position: fixed; display: none;"></div>
    <div class="chenggong mk" style="margin-top:-195px; z-index:9999999;display: none">
        <div class="main" style="height:230px; width: 320px;">
            <div class="cha" style="top: 22px; right: 22px;"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
            <div class="title_tc" style="text-align: center;line-height: 40px; padding-top: 36px;" th:text="${houseInfo.projectName}"></div>
            <div style="font-size:20px;margin-top:20px; color:#ff5200; text-align: center">恭喜您，领取成功</div>
            <a href="https://my.fangxiaoer.com/index.aspx" style="width: 320px;height:40px; line-height: 40px;margin:0 auto; background: #ff5200; color:#fff; font-size: 16px;display: block;text-align: center;border-radius: 3px;margin-top: 30px;">进入订单管理</a>
        </div>
    </div>
    <script type="text/javascript">
        var sessionId = '[[${#session?.getAttribute('sessionId')}]]';
        var phone = '[[${#session?.getAttribute('phoneNum')}]]';
        var userName= '[[${#session?.getAttribute('userName')}]]';
        var houseId = [[${projectId}]];
        var projectType= [[${projectType}]];
        var isOrder = false;
        $(document).ready(function () {
            $("#ProjectName-Title").html($(".pro_name p").html())
            $("#pwdli").hide();
            $("#yz").val("")
            $(".mk").hide();
            $("#loginzhezhao").hide();
            $("#loginClose").click(function () {
                $("#login").hide();
                $("#loginzhezhao").hide();
            });
            let width = $(".payScroll-item-a").width();
            console.log(width)
            $(".payScroll-a").width(width*2+1)
        });
        function timer(intDiff, timei) {
            window.setInterval(function () {
                var day = 0,
                    hour = 0,
                    minute = 0,
                    second = 0; //时间默认值
                if (intDiff > 0) {
                    day = Math.floor(intDiff / (60 * 60 * 24));
                    hour = Math.floor(intDiff / (60 * 60)) - (day * 24);
                    minute = Math.floor(intDiff / 60) - (day * 24 * 60) - (hour * 60);
                    second = Math.floor(intDiff) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60);
                }
                if (minute <= 9) minute = '0' + minute;
                if (second <= 9) second = '0' + second;
                var timeing = "<em>" + day + "</em>天<em>" + hour + "</em>时<em>" + minute + "</em>分<em>" + second + "</em>秒"
                $("#timeing" + timei).html(timeing);
                intDiff--;
            }, 1000);
        }
        var loginType ="";
        $(function(){
            //关闭按钮
            $(".cha").click(function(){
                $(".mk").hide();
                $("#loginzhezhao").hide();
                $(".mk input").val("");
                $(".error").hide();

                $("#pwdli").hide();
                $("#validateCodeli").show();
            })
            $("#shenqinghuodong").click(function () {
                if(sessionId!=null &&sessionId != ''){
                    var cb=$(".cbed").attr("checked");
                    if (cb != "checked") {
                        alert("您未勾选同意选项");
                        return false
                    }
                    var inputname = $("#username").val();
                    if (inputname == "") {
                        $(".ename").fadeIn();
                    } else{
                        $(".ename").fadeOut();
                        $.ajax({
                            type: "POST",
                            url: "/orderActivity",
                            data: { sessionId: sessionId, userName: $("#username").val(), activityId: activityId, code: "" },
                            dataType: "json",
                            success: function (data) {
                                switch (data.status) {
                                    case 1:
                                        $(".chenggong").show();
                                        $("#mkdom").hide();
                                        $(".error_libao").html("");
                                        $(".cha").click(function(){
                                            location.reload()
                                        });
                                        break;
                                    case 0:
                                        $(".error_libao").html(data.msg);
                                        $(".error_libao").show()
                                        $(".cha").click(function(){
                                            location.reload()
                                        });
                                        break;
                                }

                            }
                        });}
                }else {
                    if (!phone || phone == "") {
                        phone = $("#mobile1").val();
                    }
                    var back = submitapply();
                    if (back==1) {
                        $.ajax({
                            type: "POST",
                            url: "/login",
                            data: {
                                telNumber: phone, //电话
                                password: $("#yz").val(), //电话
                                goods:'1',
                                registFromUrl: window.location.href
                            },
                            async: false,
                            success: function (data) {
                                if(data.status == 1){
                                    loginType = data.status;
                                    sessionId = data.content.sessionId;
                                    $(".error_libao").html("");
                                    $(".cha").click(function(){
                                        location.reload()
                                    });
                                }else{
                                    if(data.msg =='请使用经纪人专享的系统/APP进行操作'){
                                        $(".error_libao").html('经纪人账号不可领取优惠');
                                    }
                                    else {
                                        $(".error_libao").html(data.msg);
                                    }
                                    $(".error_libao").show()
                                    $(".cha").click(function(){
                                        location.reload()
                                    });
                                }
                            }
                        });
                        if (loginType == 1) {
                            $.ajax({
                                type: "POST",
                                url: "/orderActivity",
                                data: { sessionId: sessionId, userName: $("#username").val(), activityId: activityId, code: "" },
                                dataType: "json",
                                success: function (data) {
                                    switch (data.status) {
                                        case 1:
                                            $(".chenggong").show();
                                            $("#mkdom").hide();
                                            $(".error_libao").html("");
                                            $(".cha").click(function(){
                                                location.reload()
                                            });
                                            break;
                                        case 0:
                                            $(".error_libao").html(data.msg);
                                            $(".error_libao").show();
                                            break;
                                    }

                                }
                            });
                        }
                    }
                }
            });
        });
        function IsAddActivity(callback) {
            if (isOrder) {
                $("#mkdom").show();
                $(".shibai").hide();
            } else {
                $("#mkdom").show();
                //$("#mkdom").hide();//抢礼包修改
                //$(".shibai").show();
            }
            callback.call();
        }
        //抢优惠点击事件
        $(".a-login-href-tehui").click(function () {
            if ($(this).hasClass("a-tehui")) {
            } else {
                $(".error_libao").html("")
                activityId=$(this).attr("data-href");
                IsAddActivity(function() {
                    if(sessionId!=null &&sessionId!=""){
                        $("#mobile1").val(phone);
                        $('#mobile1').attr('readonly','readonly');
                        $("#username").val(userName);
                        $("#validateCodeli").hide();
                    }
                    $("#loginzhezhao").show();
                });
            }

        });
        //提交表单验证
        function submitapply() {
            var back = 1;
            var inputname = $("#username").val();
            if (inputname == "") { $(".ename").fadeIn(); back=0 }
            else{$(".ename").fadeOut();}
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            inputmobile= $.trim($("#mobile1").val());
            if (inputmobile=="" || (!reg.test(inputmobile)))
            { $(".emobile").fadeIn(); back = 0 }
            else{$(".emobile").fadeOut();}
            var inputpassword = $("#userpassword").val();
            $(".eyz").hide();
            $(".eyz").text("请输入验证码");
            var inputyz = $("#yz").val();
            if ($("#validateCodeli").css("display") != "none") {
                $(".epassword").fadeOut();
                if (inputyz == "") { $(".eyz").fadeIn(); back = 0 }
            }
            var cb=$(".cbed").attr("checked");
            if (cb != "checked") { alert("您未勾选同意选项"); return 0;}
            if (back == 1) {
                return 1;
            } else {
                return 0;
            }
        }
        //发送短信
        $("#ReSendValidateCoad").click(function() {
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            mobile = $("#mobile1").val();
            if (mobile == "" || (!reg.test(mobile))) {
                $(".emobile").fadeIn();
            } else {
                $(".emobile").fadeOut();
                var r = 0;
                $.ajax({
                    type: "POST",
                    data: { mobile: mobile },
                    url: "/sendSmsCode",
                    async: false,
                    success: function (data) {
                        r = data;
                    }
                });


                times1();
            }
        });
        //短信验证码倒计时
        var wait = 60;
        function times1() {
            if (wait == 0) {
                $("#validateCode").hide();
                $("#ReSendValidateCoad").show().html("重新获取");
                wait = 60;
            } else {
                $("#validateCode").show().html("在" + wait + "秒后重发");
                $("#ReSendValidateCoad").hide();
                wait--;
                setTimeout(function () {
                        times1();
                    },
                    1000);
            }
        }
    </script>
</div>


<!--2021.9.19--抢优惠活动-->
<div th:fragment="subsidyActivity">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/images/aliplayer/coupon.css" />
    <style>
        /*input type:number 去掉箭头*/
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }
        input[type="number"]{
            -moz-appearance: textfield;
        }
    </style>

    <div class="scont">
        <div class="coup">
            <div class="stit">万科浑南新都心专享</div>
            <div class="tar">5999</div>
            <div class="surplus">还剩235张</div>
            <a href="/static/subsidyAgreement.htm" target="_blank"><div class="dmore">了解活动详情</div></a>
            <div class="receive"></div>
        </div>
<!--        <div th:text="${houseInfo.projectName}" class="cc"></div>-->
    </div>

    <!--领取成功-->
    <div class="succeed hide">
        <div class="sccm">
            <img src="https://static.fangxiaoer.com/web/images/aliplayer/coupon/checks.png" class="simg"/>
            <div class="sh1">领取购房补贴券成功</div>
            <div class="tx1">购房顾问将通过加密专线与您联系，为您详细介绍购房补贴活动须知；</div>
            <div class="tx2">扫码下载房小二网APP，看房成交，使用补贴券</div>
            <div class=""><img src="https://static.fangxiaoer.com/web/images/aliplayer/coupon/bigewm_fxe.png"/></div>
            <div class="tx3">立即扫码下载</div>
            <div class="subtn">确定</div>

            <div class="careful">
                <div class="ch1">活动注意事项：</div>
                <p>1.购房补贴券仅限对应楼盘项目使用，不可交叉使用。</p>
                <p>2.每套房产仅可领取一次购房补贴，不可叠加使用。</p>
                <p>3.补贴券领取必须早于售楼处首访，且需要在房小二网购房顾问的协助下完成楼盘项目的首访才能在成交后领取购房补贴。</p>
                <p>4.签约备案合同手机号、售楼处到访手机号、领券手机号必须一致，任意不一致将无法领取购房补贴。</p>
                <p>5.购房补贴为房小二网赞助，与楼盘优惠活动无关。</p>
            </div>
        </div>
    </div>


    <!--弹窗-->
    <div class="popup hide"></div>
    <!--补贴券抢光-->
    <div class='nullm'>
        <div class="nutop">诶呀，补贴券都被抢光了...</div>
        <div class="nubtn">确定</div>
    </div>
     <!--已登录-->
    <div class="take hide lo">
            <div class="tmm">
                <div class="th1">购房补贴券</div>
                <div class="th2"><i>￥</i><em class="tem">1119</em></div>
                <div class="th3">龙湖中德开·春江悦茗专享</div>
                <div class="fm">
                    <div class="fip"><input placeholder="请输入购房者姓名" class="gname useGame" type="text" oninput="phleng(value,'.useGame','7')"/></div>
                    <div class="fip"><input placeholder="请输入购房联系电话" class="gphone" type="number" disabled></div>
                    <div class="subbtn takes">立即领取</div>
                    <div class="agre">
                        <div class="aggh"></div>
                        <div class="ame">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>及<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                    </div>
                </div>
                <div class="tclose"></div>
            </div>
            <div class="bgt"></div>
        </div>
    <!--未登录-->
    <div class="take hide nlo" style="height: 703px;">
            <div class="tmm">
                <div class="th1">购房补贴券</div>
                <div class="th2"><i>￥</i><em class="tem">1119</em></div>
                <div class="th3">龙湖中德开·春江悦茗专享</div>
                <div class="fm">
                    <div class="fip"><input type="text" placeholder="请输入购房者姓名" class="uname useName" oninput="phleng(value,'.useName','7')"/></div>
                    <div class="fip" style="margin-bottom: 18px;"><input type="number" placeholder="请输入购房联系电话" class="uphone" oninput="phleng(value,'.uphone','11')" ></div>
                    <div class="fip2">
                        <input placeholder="请输入验证码" class="ucode udma" type="number" oninput="phleng(value,'.udma','6')">
                        <div class="ycode co1">获取验证码</div>
                        <div class="ycode co2 hide">获取验证码</div>
                    </div>
                    <div class="subbtn takeing">立即领取</div>
                    <div class="agre">
                        <div class="aggh"></div>
                        <div class="ame">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>及<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                    </div>
                </div>
                <div class="tclose"></div>
            </div>
            <div class="bgt"></div>
        </div>

    <script th:inline="javascript">
        var msg = [[${houseInfo}]];
        var sessionId = [[${#session?.getAttribute('sessionId')}]];
        var phone = [[${#session?.getAttribute('phoneNum')}]];
        var aggh_check=false
        var isWhetherLimit,isDayEnd,isLimitNum


        //获取补贴项目情况
        $.ajax({
            type: "POST",
            url: "/viewSubsidyForProject",
            data: { projectId: msg.pzid},
            dataType: "json",
            success: function (data) {
                console.log(data)
                if(data.status==1){
                    if(data.content==null || data.content==''){
                        $(".coup").hide()
                        return
                    }else if(data.content.dayEnd<=0){
                        $(".coup").hide()
                        return;
                    }else{
                        $(".stit").text(msg.projectName+'专享')//项目名
                        $(".tar").text(parseInt(data.content.subsidy))//价格
                        $(".coup").attr('id',data.content.id)//活动id
                        $(".coup").attr('pid',msg.pzid)//项目id
                        $(".coup").show()//显示购房补贴券

                        isWhetherLimit=data.content.whetherLimit//是否限量
                        isDayEnd=data.content.dayEnd//活动剩余天数
                        isLimitNum=data.content.limitNum//限量状态 剩余张数

                        //whetherLimit  0不限量 1限量
                        if(data.content.whetherLimit==0){
                            $(".surplus").text('还剩'+data.content.dayEnd+'天结束')//不限量，显示剩余天数
                        }else{
                            $(".surplus").text('还剩'+data.content.limitNum+'张')//限量，显示剩余张数
                        }
                    }
                }else{
                    alert(data.msg)
                    $(".coup").hide()
                }
            }
        });


        //点击banner 立即领取
        $(".receive").click(function(){
            /*console.log(sessionId)
            console.log(isWhetherLimit)
            console.log(isDayEnd+'天')
            console.log(isLimitNum+'张')*/

            //限量状态 0张，提示补贴券领光
            if(isWhetherLimit==1 && isLimitNum==0){
                // alert('诶呀，补贴券都被抢光了...')
                $(".popup,.nullm").show()
                return
            }

            let tar=$(".tar").text()
            if(sessionId!=null && sessionId!=""){
                //已登录
                if(phone!=null){
                    $(".gphone").val(phone)//带出手机号
                }
                $(".lo .tem").text(tar)
                $(".lo .th3").text(msg.projectName)
                $(".popup,.lo").show()
            }else{
                //未登录
                $(".nlo .tem").text(tar)
                $(".nlo .th3").text(msg.projectName)
                $(".popup,.nlo").show()
            }
            $(".ucode,.uname,.gname").val('')
            $('.aggh').css({'background-image':'url(https://static.fangxiaoer.com/web/images/aliplayer/coupon/img_nsel.png)'})
            aggh_check=false
        })

        //选择协议
        $(".aggh").toggle(function(){
            $(this).css({'background-image':'url(https://static.fangxiaoer.com/web/images/aliplayer/coupon/img_sel.png)'})
            aggh_check=true
        },function(){
            $(this).css({'background-image':'url(https://static.fangxiaoer.com/web/images/aliplayer/coupon/img_nsel.png)'})
            aggh_check=false
        })

        //获取验证码
        $(".co1").click(function(){
            let reg = new RegExp("^1[0-9]{10}$", "ig");
            let mph = $(".uphone").val();
            if($(".uname").val()==''){
                alert('请输入购房者姓名！')
                return;
            }else if (mph == "" || (!reg.test(mph))) {
                alert('请输入正确的手机号码!')
                return
            } else {
                var r = 0;
                $.ajax({
                    type: "POST",
                    data: { mobile: mph },
                    url: "/sendSmsCode",
                    async: false,
                    success: function (data) {
                        r = data;
                    }
                });
                times2();//倒计时
            }

        })
        //倒计时
        var wtt = 60;
        function times2() {
            if (wtt == 0) {
                $(".co2").hide()
                $(".co1").show().html("获取验证码");
                wtt = 60;
            } else {
                $(".co1").hide()
                $(".co2").show().html("重新获取(" + wtt + "s)");
                wtt--;
                setTimeout(function () {
                        times2();
                    },
                    1000);
            }
        }

        //关闭弹窗
        $(".tclose").click(function(){
            $(".popup,.lo,.nlo").hide()
            wtt=0//倒计时清零
            //清空输入值
            $(".ucode,.uname,.gname,.uphone").val('')
            $('.aggh').css({'background-image':'url(https://static.fangxiaoer.com/web/images/aliplayer/coupon/img_nsel.png)'})
            aggh_check=false
        })
        $(".nubtn").click(function(){
            $(".popup,.nullm").hide()
        })

        //未登陆 - 立即领取
        $('.takeing').click(function(){
            console.log($(".uname").val())
            reg = /[^\u4E00-\u9FA5]/g;
            if($(".uname").val()==''){
                alert('请输入购房者姓名！')
                return;
            }else if(reg.test($(".uname").val())){
                alert('请输入中文购房者姓名！')
                return;
            }else if(!$(".uphone").val().match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)){
                alert('请输入正确的手机号码!')
                return;
            }else if($(".ucode").val()==''){
                alert('请输入正确的验证码!')
                return;
            }else if(!aggh_check){
                alert("请先阅读并勾选同意《房小二网用户服务协议》与《房小二网隐私政策》");
                return;
            }

            var uphone=$(".uphone").val()
            var ucode=$(".ucode").val()
            //登陆
            $.ajax({
                type: "POST",
                url: "/login",
                data: { telNumber: uphone,code:ucode},
                async: false,
                success: function (res) {
                    if(res.status==0){
                        alert('请输入正确的验证码!')//res.msg
                        return;
                    }else{
                        var seid=res.content.sessionId
                        var uname=$(".uname").val()
                        var uid=$(".coup").attr('id')

                        //领取补贴
                        $.ajax({
                            type: "POST",
                            url: "/fetchSubsidyForProject",
                            data: { sessionId: seid,subsidyId: uid,memberName: uname,whetherChoose:0},
                            async: false,
                            success: function (ret) {
                                console.log(ret)
                                if(ret.status==1){
                                    console.log('领取成功')
                                    let pid=$(".coup").attr('pid')
                                    //更新优惠券条数
                                    $.ajax({
                                        type: "POST",
                                        url: "/viewSubsidyForProject",
                                        data: { projectId: pid},
                                        dataType: "json",
                                        success: function (ref) {
                                            console.log(ref)
                                            if(ref.status==1){
                                                if(ref.content.whetherLimit==0){
                                                    $(".surplus").text('还剩'+ref.content.dayEnd+'天结束')//不限量，显示剩余天数
                                                }else{
                                                    $(".surplus").text('还剩'+ref.content.limitNum+'张')//限量，显示剩余张数
                                                }
                                                // $(".popup,.lo,.nlo").hide()
                                                //修改 页面样式
                                                $(".popup,.lo,.nlo").hide()
                                                $(".newHouseViewpro_name,.nav_house,.scont,.newHouseviewPz,.container,.disclaimer").hide()
                                                $(".suspensionIcon,.talk_btn").hide()//在线客服
                                                $(".bkNavFloat,.fixbkb").hide()
                                                $(".favorable-price,.tial").hide()
                                                // $(".footer").hide()//底部
                                                $(".succeed").show()
                                            }else{
                                                alert(ref.msg)//提示错误
                                                $(".popup,.lo,.nlo").hide()//关闭弹窗
                                            }
                                        }
                                    });

                                }else if(ret.status==0){
                                    alert(ret.msg)//ret.msg此手机号码已领取过补贴券，不能重复领取
                                    window.location.reload()
                                    $(".uphone").val('')
                                    $(".popup,.lo,.nlo").hide()//修改 页面样式
                                }
                            }
                        });
                    }
                }
            });
        })


        //已登录 - 立即领取
        $(".takes").click(function(){
            reg = /[^\u4E00-\u9FA5]/g;
            if($(".gname").val()==''){
                alert('请输入购房者姓名！')
                return;
            }else if(reg.test($(".gname").val())){
                alert('请输入中文购房者姓名！')
                return;
            }else if(!$(".gphone").val().match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)){
                alert('请输入正确的手机号码!')
                return;
            }else if(!aggh_check){
                alert("请先阅读并勾选同意《房小二网用户服务协议》与《房小二网隐私政策》");
                return;
            }
            let geid=sessionId
            let gname=$(".gname").val()
            let gid=$(".coup").attr('id')

            //领取补贴
            $.ajax({
                type: "POST",
                url: "/fetchSubsidyForProject",
                data: { sessionId: geid,subsidyId: gid,memberName: gname,whetherChoose:0},
                success: function (ret) {
                    // console.log(ret)
                    if(ret.status==1){
                        console.log('领取成功')
                        let pid=$(".coup").attr('pid')
                        //更新优惠券条数
                        $.ajax({
                            type: "POST",
                            url: "/viewSubsidyForProject",
                            data: { projectId: pid},
                            dataType: "json",
                            success: function (ref) {
                                if(ref.status==1){
                                    if(ref.content.whetherLimit==0){
                                        $(".surplus").text('还剩'+ref.content.dayEnd+'天结束')//不限量，显示剩余天数
                                    }else{
                                        $(".surplus").text('还剩'+ref.content.limitNum+'张')//限量，显示剩余张数
                                    }
                                    $(".popup,.lo,.nlo").hide()
                                    //修改 页面样式
                                    $(".popup,.lo,.nlo").hide()
                                    $(".newHouseViewpro_name,.nav_house,.scont,.newHouseviewPz,.container,.disclaimer").hide()
                                    $(".suspensionIcon,.talk_btn").hide()//在线客服
                                    $(".bkNavFloat,.fixbkb").hide()
                                    $(".favorable-price,.tial").hide()
                                    // $(".footer").hide()//底部
                                    $(".succeed").show()
                                }else{
                                    alert(ref.msg)//提示错误
                                    $(".popup,.lo,.nlo").hide()//关闭弹窗
                                }
                            }
                        });

                    }else if(ret.status==0){
                        alert(ret.msg)//'此手机号码已领取过补贴券，不能重复领取'
                        $(".popup,.lo,.nlo").hide()//修改 页面样式
                    }
                }
            });
        })

        //•点击"确定"按钮,页面跳转回项目详情页
        $(".subtn").click(function (){
            if(sessionId!=null && sessionId!=""){
                //已登录
                $(".newHouseViewpro_name,.nav_house,.scont,.newHouseviewPz,.container,.disclaimer").show()
                $(".suspensionIcon,.talk_btn").show()//在线客服
                $(".succeed").hide()
            }else{
                //未登陆
                window.location.reload()
            }
        })

        //用户名限制7位 只能输入汉字
        function checkUserName(val,dom){
            reg = /[^\u4E00-\u9FA5]/g;
            if(!reg.test(val)){
                if(val.length<7){
                    $(dom).val(val)
                }else{
                    let dd=val.substring(0,7)
                    $(dom).val(dd)
                }
            }else{
                let uu=val.replace(/[^\u4E00-\u9FA5]/g,'')
                $(dom).val(uu)
            }
        }


        //限制长度
        function phleng(val,dom,lth){
            if(val.length>lth){
                let phv=val.substring(0,lth)
                $(dom).val(phv)
            }
        }


    </script>
</div>


</body>
</html>