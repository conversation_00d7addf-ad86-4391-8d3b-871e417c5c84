<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml"
      xmlns:th="https://www.thymeleaf.org">
<head>
    <title>抢礼包，沈阳特价房任你选 - 房小二网</title>
    <meta name="keywords" content="房产特惠,沈阳特价房,优质楼盘,低价楼盘,优惠楼盘,热销楼盘" />
    <meta name="description" content="房小二网开启的抢优惠专区，为您提供沈阳全境众多优质特价好房源，让您在购房过程中享受到实实在在的优惠。" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Bootstrap -->
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet"
          type="text/css">
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet"
          type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/default/default.css?t=20181225" rel="Stylesheet"
          type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/marketing/gift.css?t=20180502" rel="Stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/bootstrap.min.js" type="text/javascript"> </script>
    <script src="https://static.fangxiaoer.com/js/default.js"></script>
    <script src="https://static.fangxiaoer.com/js/time.js"></script>
    <!--   <script src="../js/thzq.js"></script>-->
</head>
<body class="w1210">
<form name="form1" method="post" action="default.aspx" id="form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
    <!--搜索栏-->
    <div id="search2017">
        <div class="w">
            <div class="search">
                <input id="txtkeys" type="text" name="4" placeholder="请输入楼盘名称开始找房" class="ac_input">
                <input type="button" value="搜 索" class="btn_search search_btn">
            </div>
            <div class="map"><a href="/house/housemap">地图找房</a></div>
            <div class="search_tel">专家咨询电话：<i>************</i></div>
        </div>
    </div>

    <div id="myCarousel" class="carousel slide thzq_gg">
        <ol class="carousel-indicators">
            <li data-target="#myCarousel" data-slide-to="0"      class='active'></li>
            <li data-target="#myCarousel" data-slide-to="1"     ></li>
        </ol>
        <!-- Carousel items -->
        <div class="carousel-inner">
            <a href="https://sy.fangxiaoer.com/house/view/45" target="_blank" class="active item" style="background: url('https://images.fangxiaoer.com/sy/ad/2017/04/24/58fd615bbb9bb.jpg') no-repeat center;"><span>广告：华府丹郡·悦湖</span></a>
            <a href="https://sy.fangxiaoer.com/house/view/140" target="_blank" class="item" style="background: url('https://images.fangxiaoer.com/sy/ad/2017/04/16/58f324d903c72.jpg') no-repeat center;"><span>广告：长堤湾</span></a>
        </div>
    </div>
    <div class="container thzq_zf_dl">
        <dl id="avtive_1">
            <dt>区域找房：</dt>
            <dd th:each="region:${region}" th:id="'r'+${region.id}">
                <a  th:text="${region.name}" th:href="${region.url}" ></a>
            </dd>
        </dl>
        <dl>
            <dt>新房类型：</dt>
            <dd id="h1">
                <a href="/marketing/h1" >普宅</a></dd>
            <dd id="h2">
                <a href="/marketing/h2" >别墅</a></dd>
            <dd id="h3">
                <a href="/marketing/h3" >公寓</a></dd>
        </dl>
    </div>
    <div class="container thzq">
        <!--限时秒杀-->
        <div class="xsms_div">
        </div>
        <div class="thzq_lqyh" th:each="activity:${activities}">
            <img th:src="'https://images.fangxiaoer.com/sy/qt/big/'+${activity.ActPic}" th:alt="${activity.ProjectName}">
            <div class="thzq_lqyh_right">
                <div class="bg_list">
                    <img src="https://static.fangxiaoer.com/web/images/sy/picture/bg_marketing_list.png" alt="锯齿图标" /></div>
                <div class="thzq_title">
                    <a target="_blank" th:href="${'/house/detail/'+activity.ProjectID+'.htm'}" th:text="${activity.ProjectName}"></a>
                </div>
                <div class="thzq_content" th:text="${activity.Slogan}">
                </div>
                <a target="_blank" th:href="${'/house/detail/'+activity.ProjectID+'.htm'}" class="thzq_lqyh_btn text-center">抢礼包</a>
            </div>
            <i><img src="https://static.fangxiaoer.com//web/images/sy/marketing/gift.png" /></i>
        </div>
        <dl class="float_left">
            <dt>区域找房</dt>
            <dd >
                <a href="/act/default/10_-1_-1">浑南</a></dd>
            <dd >
                <a href="/act/default/3_-1_-1">皇姑</a></dd>
            <dd >
                <a href="/act/default/9_-1_-1">于洪</a></dd>
            <dd >
                <a href="/act/default/5_-1_-1">铁西</a></dd>
            <dd >
                <a href="/act/default/4_-1_-1">和平</a></dd>
            <dd >
                <a href="/act/default/11_-1_-1">沈北</a></dd>
            <dd >
                <a href="/act/default/2_-1_-1">大东</a></dd>
            <dd >
                <a href="/act/default/1_-1_-1">沈河</a></dd>
            <dd >
                <a href="/act/default/12_-1_-1">苏家屯</a></dd>
        </dl>
    </div>
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::common_meiqia"></div>
</form>
<script>
    $(document).ready(function(){
        var urlpath = window.location.pathname;
        var index = urlpath.lastIndexOf("\/");
        urlpath = urlpath.substring(index+1,urlpath.length);
        urlpaths = urlpath.split("-");
        for(var i = 0 ;i < urlpaths.length;i++){
            $("#"+urlpaths[i]).addClass("avtive").siblings().removeClass();
            if(urlpath.indexOf("r")<0||urlpath.indexOf("r25")>=0){
                $("#avtive_1 dd").first().addClass("avtive");
            }else{
                $("#avtive_1 dd").first().removeClass("avtive");
            }
        }
    })
</script>
</body>
</html>
