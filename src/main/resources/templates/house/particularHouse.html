<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml"
      xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="utf-8" />
    <title>沈阳特价房地图_沈阳新房价格_房价地图 - 房小二网</title>
    <meta name="keywords" content="沈阳特价房地图,沈阳新房价格,房价地图,沈阳二手房" />
    <meta name="description" content="房小二网沈阳特价房地图为您提供最新最全的沈阳特价房房源大全，一房一价的唯一价格，全市最低优惠幅度，让你把握每一次新房，二手房房价优惠机会，买房买房，就上房小二网。" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/tjf">
     <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css?t=20180502" />
    <script src="https://static.fangxiaoer.com/js/baiduMap/ajax.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/baiduMap/baiduMap1.css?t=20180502" />
    <script src="https://static.fangxiaoer.com/js/baiduMap/vue.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/particular.css?t=20180502"/>

    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
</head>
<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>

<input type="hidden" id="sessionId" th:value="${session.sessionId}">

<div id="map" v-bind:style="{height:height+'px'}">
    <div id="baiduMap">

    </div>
    <div id="heloYouFindRoom">
        <img src="https://static.fangxiaoer.com/web/images/sy/house/map.jpg" alt="" />
        <ul>
            <li><h1>价格不满意，地点不称心？</h1></li>
            <li><span>您距离您私人定制的特价房仅剩两步：</span></li>
            <li>
                <select id="budget">
                    <option>请选择您的大概预算</option>
                    <option>35万以下</option>
                    <option>35-50万</option>
                    <option>50-80万</option>
                    <option>80-100万</option>
                    <option>100-120万</option>
                    <option>120-150万</option>
                    <option>150万以上</option>
                </select>
            </li>
            <li>
                <input type="" name="" id="phone" value="" placeholder="请输入您的联系方式"/>
            </li>
            <li>
                <b onclick="baiduMap.saveGuide()">帮您找房</b>
                <a href="https://sy.fangxiaoer.com/houseKeeper1.htm" target="_blank">我要问价</a>
            </li>
            <li><p>免费热线：<span></span></p></li>
        </ul>
    </div>
    <div id="houseInfo">
        <div class="houseInfo">
            <img v-bind:src="houseImg"/>
            <div>
                <h1 id="houseName"></h1>
                <p><span>{{houseDis}}</span></p>
                <p>{{houseAdd}}</p>
            </div>
        </div>
        <dl>
            <dt>特价房源<span>（一房一价，售完为止）</span></dt>
            <div>
                <dd v-for="house in houseInfo">
                    <div class="t1"><a target="_blank">{{house.BuildingNumber+" "+house.Layout+" "+house.BuildArea+"㎡"}}</a></div>
                    <div class="t2"><span>{{house.Price}}万</span></div>
                    <div class="t3"><span>{{house.UnitPrice}}元/㎡</span></div>
                    <div class="cl"></div>
                    <div class="t4">咨询电话: <span>{{house.Phone}}</span></div>
                    <div class="t5">{{house.NormalPrice}}万</div>
                    <div class="t6">{{house.UnitNormalPrice}}元/㎡</div>
                </dd>
            </div>
        </dl>
    </div>
    <ul id="priceInfo">
        <li>
            <div class="t1"></div>
            <span>5000元/㎡以下</span>
        </li>
        <li>
            <div class="t2"></div>
            <span>5000-7000元/㎡</span>
        </li>
        <li>
            <div class="t3"></div>
            <span>7000-9000元/㎡</span>
        </li>
        <li>
            <div class="t4"></div>
            <span>9000元/㎡以上</span>
        </li>
    </ul>
</div>
<div class="hint" id="hint" style="display: none;">
    <dl>
        <dd><p>您的浏览器版本过低，<br>建议升级浏览器！</p></dd>
        <dd>
            <img src="https://static.fangxiaoer.com/web/images/sy/map/info.png"/>
        </dd>
        <dd>
            <a href="https://pc.uc.cn/" target="_blank" style="color: #ff5200;" id="Dherf">升级浏览器</a>
        </dd>
    </dl>
</div>
<script>
    var type=1;
    var navType=0;
    if(navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > -1 && !(navigator.userAgent.indexOf("Opera") > -1) ){
        document.getElementById("hint").style.display="block";
    }
    //扩展方法获取url参数  
    (function($){
        $.getUrlParam = function(name) {
            var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r!=null)
                return decodeURI(r[2]); // return decodeURIComponent(r[2]);均为解码  
            return null;
        }
    })(jQuery);
    var ly = $.getUrlParam('ly');
    $("#map #heloYouFindRoom>ul li p span").text("************")
</script>
<script src="https://static.fangxiaoer.com/js/particular.js" type="text/javascript" charset="utf-8"></script>

</body>
</html>