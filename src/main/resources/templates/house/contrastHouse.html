<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="utf-8" />
    <title>楼盘对比</title>
    <link href="//static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="//static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="//static.fangxiaoer.com/web/styles/new_sy/house/newHouse-Contrast.css?t=20180502" />
<!--    <link rel="stylesheet" type="text/css" href="/css/newHouse-Contrast.css?t=20180502" />-->

    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="//static.fangxiaoer.com/js/angular.min.js"></script>
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <!--<script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js" type="text/javascript" charset="utf-8"></script>-->
    <style>
        .ng-binding{
            cursor: default !important;
        }
        .default-box{
            /*border: 1px solid !important;*/
            display: flex !important;
            /*flex-direction: column !important;*/
            align-items: center !important;
            margin: 0px !important;
            width: 224px !important;


            height: 208px !important;
            justify-content: center;
            border-left: 1px solid #dddddd;
        }
        .default-box2{
            /*border: 1px solid !important;*/
            display: flex !important;
            /*flex-direction: column !important;*/
            align-items: center !important;
            margin: 0px !important;
            width: 224px !important;


            height: 208px !important;
            justify-content: center;
            /*border-left: 1px solid #dddddd;*/
        }
        .left-box{
            width: 223px !important;
        }
        .right-line{
            height: 208px !important;
            width: 0.6px !important;
            background: #DDDDDD;
        }
        .default-img{
            width: 102px !important;
            height: 106px !important;
        }
        .default-text{
            font-size: 14px;
            font-family: Microsoft YaHei;
            color: #FF5200;


        }
        .default-blank{
            border: 1px solid !important;
            display: flex !important;
            /*flex-direction: column !important;*/
            align-items: center !important;
            margin: 0px !important;
            width: 226px !important;


            height: 208px !important;
            justify-content: center;
        }
        .new-td{
            width: 899px;
            display: flex;
            align-items: center;
            margin-left: -1px;
        }

        .new-border{
            width: 0px !important;

        }
        .new-box{
            display: flex;
            flex-direction: column;
            margin: 0px !important;
            /*border: 1px solid;*/
            padding-right: 9.5px;

            padding-left: 9.5px;
            height: 208px !important;

            /*width: 225px !important;*/
            justify-content: center;
            /*border-right: 1px solid #dddddd;*/

        }
        .new-con{
            display: flex;

            margin: 0px !important;
            height: 208px !important;
            justify-content: center;
            /*border-right: 1px solid #dddddd;*/
        }
        /*.new-con>.new-line:nth-child(3n) {*/
        /*    border-right: none !important;*/
        /*}*/


        .new-img6{
            width: 140px !important;
            height: 103px !important;
        }
        .new-b{
            margin-left: 18px !important;
            width: 160px;
            text-align: left;
        }
        .new-img{
            margin-left: 12px;
            margin-right: 12px;
        }
        .new-line{
            height: 208px !important;
            width: 0.6px !important;
            background: #DDDDDD;
        }
        .testBtn{
            text-decoration: none;
            color: #FF5200;
            cursor: pointer !important;
        }
        .testBtn:hover{
             text-decoration: none;
        }
        .btnText{
            cursor: pointer !important;
        }


    </style>
</head>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>
<input th:if="${#session?.getAttribute('sessionId') == null}" id="fxe_sessionId" type="hidden" value="" >
<input th:if="${#session?.getAttribute('sessionId') != null}" id="fxe_sessionId" type="hidden" th:value="${#session?.getAttribute('sessionId')}">



<!--定义angular控制器 和vue一样原理-->
<div class="mian-kuang"  ng-app="myApp"  ng-controller="scriptorium">

    <!--  表格最左边那个标题导航 点击后右边跳转右边对应区域  -->
    <div class="db-left">
        <ul>
            <!--   大分类标题-->
            <p class="db-title">楼盘对比</p>
            <!--    子项-->
            <li><a href="#houseName">楼盘名称</a></li>
            <li class="db-title2"><i class="left-icon"></i><a href="#housePrice">价格</a></li>
            <li><a href="#housePrice1">楼盘价格</a></li>
            <li class="db-title2"><i class="left-icon"></i><a href="#houseInfo">基本信息</a></li>
            <li><a href="#houseInfo1">项目地址</a></li>
            <li><a href="#houseInfo2">项目特色</a></li>
            <li><a href="#houseInfo3">项目状态</a></li>
            <li><a href="#houseInfo6">建筑类型</a></li>
            <li><a href="#houseInfo7">装修状态</a></li>
            <li><a href="#houseInfo9">公摊</a></li>
            <li><a href="#houseInfo10">容积率</a></li>
            <li><a href="#houseInfo11">绿化率</a></li>
            <li><a href="#houseInfo8">物业费</a></li>
            <li class="db-title2"><i class="left-icon"></i><a href="#houseType">户型信息</a></li>
            <li><a href="#houseType1">户型</a></li>
            <li><a href="#houseType2">面积</a></li>

            <li class="db-title2"><i class="left-icon"></i><a href="#houseReview">楼盘测评</a></li>
            <li><a href="#houseReview1">综合评分</a></li>
            <li><a href="#houseReview2">开发品牌</a></li>
            <li><a href="#houseReview3">交通位置</a></li>
            <li><a href="#houseReview4">价值潜力</a></li>
            <li><a href="#houseReview5">外围配套</a></li>
            <li><a href="#houseReview6">内部配套</a></li>
            <li><a href="#houseReview7">园区环境</a></li>


            <li><a href="#houseReview8">户型设计</a></li>
            <li><a href="#houseReview9">装修品质</a></li>
            <li><a href="#houseReview10">物业管理</a></li>
            <li><a href="#houseReview11">性价比</a></li>

            <li class="db-title2"><i class="left-icon"></i><a href="#developer">开发商与物业公司</a></li>
            <li><a href="#developer1">开发商</a></li>
            <li><a href="#developer2">物业公司</a></li>
        </ul>
    </div>
    <!--右边表格内容-->
    <div class="db-right">
        <div class="crumbs"><a href="/">房小二网</a> &gt; <a href="/houses/">沈阳新房</a> &gt;<span style="color: #999;">楼盘对比（{{house[0].content.projectName}} 怎么样？）</span></div>
        <div class="db-content1">
            <p ng-if="houseList.length > 0">温馨提示 : 为您推荐与<a href="" target="_blank">{{house[0].content.projectName}}</a>对比次数最多的楼盘</p>
            <p ng-if="houseList.length == 0" style="margin-top: 12px;">温馨提示：您所对比的楼盘暂无推荐，请选择其他楼盘对比！</a></p>
            <ul>
                <li ng-repeat="houseList in houseList">
                    <p>
                        <a ng-href="{{'/house/'+houseList.ProjectID+'-'+houseList.projectType+'.htm'}}" target="_blank">
                            <img class="new-img6" ng-src="{{houseList.ImageUrl}}"/>
                        </a>
                    </p>
                    <div>
                        <a ng-href="{{'/house/'+houseList.ProjectID+'-'+houseList.projectType+'.htm'}}" target="_blank" ng-attr-title="{{houseList.projectName}}">{{houseList.projectName}}</a>
                        <span>{{houseList.regionName}}</span>
                    </div>
                    <a href="" ng-rel="{{houseList.regionID}}" ng-click="addHouse(houseList)"><i>＋</i>对比</a>
                </li>
            </ul>
        </div>
        <div class="nFix">
<!--            <span>楼盘对比</span>-->
            <ul>
                <li style="width: 103px;font-weight: normal">楼盘对比</li>
                <li ng-repeat="(key,house) in house" ng-attr-title="{{house.content.projectName}}">{{house.content.projectName}}</li>
            </ul>
        </div>
        <div class="db-content2">
            <table class="db-table"  cellpadding="0px"  cellspacing="0px"  style="table-layout: fixed">
                <tr>
                    <th><i class="left-icon"></i>楼盘对比</th>
                    <th colspan="4">
                        <input type="text" class="serachHouseInfo" placeholder="请输入楼盘名称"/><label>＋对比</label>

                    </th>
                </tr>


                <!--  和vue语法差不多，v-什么 换成ng-什么-->
                <tr class="db-lp" id="houseName">

                    <td style="border-right: none !important;" >楼盘名称</td>
                    <td   class="new-td" >
                        <!--  div这是一个整体  house是一个数据大合集  里面是各个便利子数据  这一项house.content才是数据-->
                        <!--   可以试试三元表达式的方式解决样式问题-->
                        <div class="new-con" ng-repeat="(key,house) in house">

                            <div class="new-line" ng-if="house.content.projectName!=undefined "
                            ></div>

                            <div class="new-box" ng-if="house.content.projectName!=undefined">
                                <a ng-href="{{'/house/'+house.content.ProjectID+'-'+house.content.type+'.htm'}}" target="_blank">

                                    <img class="new-img" ng-src="{{house.content.ImageUrl}}" ng-if="house.content.ImageUrl!=undefined"/>

                                </a>
                                <div ng-if="house.content.projectName!=undefined">

                                    <a ng-href="{{'/house/'+house.content.ProjectID+'-'+house.content.type+'.htm'}}" target="_blank">
                                        <b  class="new-b"     ng-attr-title="{{house.content.projectName}}">{{house.content.projectName}}</b>
                                    </a>
                                    <a th:if="${#session?.getAttribute('sessionId') == null}" data-toggle="modal"  href="#login"><div class="db-iconx"></div></a>
                                    <div th:if="${#session?.getAttribute('sessionId') != null}" class="db-iconx" ng-class="{true:'hover'}[house.favorite]" ng-click="cancelFavorite(key)"></div>

                                    <!--                                     是否展示当前楼盘-->
                                    <div class="db-iconc" ng-click="showHouseInfo(key)"></div>
                                </div>
                            </div>

                        </div>


                        <!--一个四个楼盘，小于等于3，就展示缺省图-->
                        <div class="default-box"  ng-if="houseArr.length <=3">
                            <div class="left-box">
                                <img class="default-img" src="/images/default-img.png"  />
                                <div class="default-text"> 请在“楼盘对比”搜索楼盘</div>
                            </div>

                            <div class="right-line" ng-if="houseArr.length <3" ></div>
                        </div>

                        <!--  空占位框-->
                        <div class="default-box2"  ng-if="houseArr.length <=2">
                            <div class="left-box">

                            </div>

                            <div class="right-line" ng-if="houseArr.length <2" ></div>
                        </div>
                        <!--  空占位框-->
                        <div class="default-box2"  ng-if="houseArr.length <=1">
                            <div class="left-box">
                            </div>

                            <div class="right-line" ng-if="houseArr.length <1" ></div>
                        </div>


                    </td>

                </tr>


                <!--  原版-->
<!--                <tr class="db-lp" id="houseName">-->
<!--                    <td>楼盘名称</td>-->
<!--                    <td ng-repeat="(key,house) in house">-->
<!--                        <div>-->
<!--                            <a ng-href="{{'/house/'+house.content.ProjectID+'-'+house.content.type+'.htm'}}" target="_blank">-->
<!--                                <img ng-src="{{house.content.ImageUrl}}" ng-if="house.content.ImageUrl!=undefined"/>-->
<!--                                <b ng-attr-title="{{house.content.projectName}}">{{house.content.projectName}}</b>-->
<!--                            </a>-->
<!--                                <div ng-if="house.content.projectName!=undefined">-->
<!--                                    <a th:if="${#session?.getAttribute('sessionId') == null}" data-toggle="modal"  href="#login"><div class="db-iconx"></div></a>-->
<!--                                    <div th:if="${#session?.getAttribute('sessionId') != null}" class="db-iconx" ng-class="{true:'hover'}[house.favorite]" ng-click="cancelFavorite(key)"></div>-->
<!--                                    <div class="db-iconc" ng-click="showHouseInfo(key)"></div>-->
<!--                                </div>-->
<!--                        </div>-->
<!--                    </td>-->
<!--                </tr>-->



                <tr id="housePrice" class="navde"></tr>

                <tr ><td class="db-title3" colspan="5"><i class="left-icon"></i>价格</td></tr>
                <tr id="housePrice1" class="navde"></tr>

                <tr >
                    <td>楼盘价格</td>
                    <td ng-repeat="house in house">
                    	<p ng-if="house.content.price[0] != undefined"
                           ng-repeat="price in house.content.price">{{ price.name+price.showPrice }}</p>
                        <p ng-if="house.content.price[0] == undefined && house.content.projectName != undefined">
                            待定
                        </p>
                    </td>
                </tr>
                <tr id="houseInfo" class="navde"></tr>

                <tr ><td class="db-title3" colspan="5"><i class="left-icon"></i>基本信息</td></tr>
                <tr id="houseInfo1" class="navde"></tr>

                <tr>
                    <td>项目地址</td>
                    <td ng-repeat="house in house" class="naddress"><p>{{house.content.projectAddress}}</p></td>
                </tr>
                <tr id="houseInfo2" class="navde"></tr>

                <tr class="db-ts" >
                    <td>项目特色</td>
                    <td ng-repeat="house in house"><span ng-repeat="features in house.content.features.split(' ')">{{features}}</span></td>
                </tr>
                <tr id="houseInfo3" class="navde"></tr>
                <tr>
                    <td>项目状态</td>
                    <td ng-repeat="house in house"><p>{{house.content.projectState}}</p></td>
                </tr>
                <tr id="houseInfo6" class="navde"></tr>

                <tr>
                    <td>建筑类型</td>
                    <td ng-repeat="house in house"><p>{{house.content.buildType}}</p></td>
                </tr>
                <tr id="houseInfo7" class="navde"></tr>

                <tr >
                    <td>装修状态</td>
                    <td ng-repeat="house in house"><p>{{house.content.resDecoration}}</p></p></td>
                </tr>
                <tr id="houseInfo9" class="navde"></tr>

                <tr >
                    <td>公摊</td>
                    <td ng-repeat="house in house"><p>{{house.content.ProjectID != null && (house.content.pooled == null || house.content.pooled == 0 || house.content.pooled == '') ? '——':house.content.pooled}}</p></td>
                </tr>
                <tr id="houseInfo10" class="navde"></tr>

                <tr>
                    <td>容积率</td>
                    <td ng-repeat="house in house"><p>{{house.content.platRatio}}</p></td>
                </tr>
                <tr id="houseInfo11" class="navde"></tr>

                <tr >
                    <td>绿化率</td>
                    <td ng-repeat="house in house"><p>{{house.content.greenery != null && (house.content.greenery == null || house.content.greenery == 0 || house.content.greenery == '') ? '——':house.content.greenery}}</p></td>
                </tr>
                <tr id="houseInfo8" class="navde"></tr>

                <tr >
                    <td>物业费</td>
                    <td ng-repeat="house in house"><p>{{house.content.PropertyFee}}</p></td>
                </tr>
                <tr id="houseType" class="navde"></tr>

                <tr ><td class="db-title3" colspan="5"><i class="left-icon"></i>户型信息</td></tr>
                <tr id="houseType1" class="navde"></tr>

                <tr>
                    <td>户型</td>
                    <td ng-repeat="house in house">
                        <p><s ng-repeat="layout in house.content.mLayout">{{$index == house.content.mLayout.length - 1 ? layout.RoomType + '居' : layout.RoomType + '居、'}}</s></p>
                    </td>
                </tr>
                <tr id="houseType2" class="navde"></tr>

                <tr >
                    <td>面积</td>
                    <td ng-repeat="house in house">
                        <p ng-if="house.content.areaSection != null">{{house.content.areaSection.length!=0 ? house.content.areaSection[0].minArea +'㎡ ~ '+ house.content.areaSection[0].maxArea+'㎡' : ''}}</p>
                        <p ng-if="house.content.areaSection == null"></p>
                    </td>
                </tr>


                <!--   新增部分开始-->
                <tr id="houseReview" class="navde"></tr>

                <tr ><td class="db-title3" colspan="5"><i class="left-icon"></i>楼盘测评</td></tr>
                <tr id="houseReview1" class="navde"></tr>
                <tr style="font-weight: bold">
                    <td>综合评分</td>
                    <td ng-repeat="house in house" class="naddress"><p>{{house.content.reviewNum ? house.content.reviewNum : ''}}</p></td>
                </tr>

                <tr id="houseReview2" class="navde"></tr>
                <tr>
                    <td>开发品牌</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.brandScore ? house.content.comparison.brandScore : ''}}</p></td>
                </tr>
                <tr id="houseReview3" class="navde"></tr>

                <tr>
                    <td>交通位置</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.trafficScore ? house.content.comparison.trafficScore : ''}}</p></td>
                </tr>
                <tr id="houseReview4" class="navde"></tr>

                <tr >
                    <td>价值潜力</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.valueScore ? house.content.comparison.valueScore : ''}}</p></td>
                </tr>
                <tr id="houseReview5" class="navde"></tr>

                <tr >
                    <td>外围配套</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.externalResourceScore ? house.content.comparison.externalResourceScore : ''}}</p></td>
                </tr>
                <tr id="houseReview6" class="navde"></tr>

                <tr>
                    <td>内部配套</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.internalResourceScore ? house.content.comparison.internalResourceScore : ''}}</p></td>
                </tr>
                <tr id="houseReview7" class="navde"></tr>

                <tr >
                    <td>园区环境</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.environmentScore ? house.content.comparison.environmentScore : ''}}</p></td>
                </tr>
                <tr id="houseReview8" class="navde"></tr>

                <tr >
                    <td>户型设计</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.unitScore ? house.content.comparison.unitScore : ''}}</p></td>
                </tr>
                <tr id="houseReview9" class="navde"></tr>

                <tr >
                    <td>装修品质</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.decorateScore ? house.content.comparison.decorateScore : ''}}</p></td>
                </tr>
                <tr id="houseReview10" class="navde"></tr>

                <tr >
                    <td>物业管理</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.propertyScore ? house.content.comparison.propertyScore : ''}}</p></td>
                </tr>
                <tr id="houseReview11" class="navde"></tr>

                <tr >
                    <td>性价比</td>
                    <td ng-repeat="house in house"><p>{{house.content.comparison.costPerformanceScore ? house.content.comparison.costPerformanceScore : ''}}</p></td>
                </tr>
<!--               暂时隐藏-->
<!--                <tr  style="color: #FF5200;height: 68px;">-->
<!--                    <td></td>-->

<!--                    <td ng-repeat="house in house">-->
<!--                        <a ng-if="house.content.comparison != undefined && house.content.comparison != null && house.content.comparison != ''"    ng-href="{{'/house/'+house.content.ProjectID+'-'+house.content.type+'.htm'}}" target="_blank" class="testBtn">-->
<!--                        <p class="btnText"> {{house.content.projectName!=undefined ? '查看评测' : ''}} </p>-->
<!--                        </a>-->
<!--                    </td>-->

<!--                </tr>-->

                <!--  新增部分-->




                <tr id="developer"><td class="db-title3" colspan="5"><i class="left-icon"></i>开发商与物业公司</td></tr>
                <tr id="developer1" class="navde"></tr>
                <tr>
                    <td>开发商</td>
                    <td ng-repeat="house in house"><p>{{house.content.developerName}}</p></td>
                </tr>
                <tr id="developer2" class="navde"></tr>
                <tr >
                    <td>物业公司</td>
                    <td ng-repeat="house in house"><p>{{house.content.companyWYGS}}</p></td>
                </tr>
            </table>
        </div>
    </div>
</div>
    <div class="cl"></div>
<div th:include="fragment/fragment::commonFloat"></div>
<div th:include="fragment/fragment:: footer_seo"></div>
<div th:include="fragment/fragment:: footer_list"></div>
<div th:include="fragment/fragment::tongji"></div>

<div th:include="house/detail/fragment_login::login"></div>
	<script>

    var app = angular.module('myApp', []);
      app.controller('scriptorium', function($scope, $rootScope, $http,$timeout) {
          $scope.house = [{
              houseId: '',
              content: {},
              favorite: ''
          }, {
              houseId: '',
              content: {},
              favorite: ''
          }, {
              houseId: '',
              content: {},
              favorite: ''
          }, {
              houseId: '',
              content: {},
              favorite: ''
          }];
          $scope.item={}


          // $scope.reviewNum=''   // 评测综合评分  相当于vue data里定义变量
          $scope.sessionId=document.getElementById("fxe_sessionId").value;
          // var txt = window.location.href;
          // if (txt.indexOf("?") > 0) {
          //     txt = txt.split("?")[1].split("&")
          //     for (var i = 0; i < txt.length; i++) {
          //         var num = txt[i].split("houseId=").length;
          //         if (num > 1) {
          //             txt = txt[i].split("houseId=")[1];
          //             txt = txt.split(',');
          //             break;
          //         }
          //     }
          // console.log(txt)
          //     for (var t = 0; t < txt.length; t++) {
          //         $scope.house[t].houseId = txt[t];
          //     }
          //     console.log($scope.house)
          // }
          //2020/7/30日优化修改zwy
          //初始化对比列表id
          $.ajax({
              type: "post",
              url: "/getContrastHouse",
              async: false,
              success: function (data) {
                  console.log('进入方法')
                  console.log(data)
                  $scope.houseArr = data;   //用于判断 是否显示占位符和线

                  console.log($scope.houseArr)
                  console.log('2')

                  // 把获取到的楼盘id添加到house大数组里面
                  for (var t = 0; t < data.length; t++) {
                      $scope.house[t].houseId = data[t].houseId;
                  }
              }
          });

          // 监听楼盘对比数据  这里得修改
          // $scope.$watch("house",function(newVal,oldVal){
          //
          //    console.log('进入监控方法')
          //     if(newVal.length<=3){
          //         console.log('显示图片')
          //     }
          //
          // });

          $scope.houseList = [];   // 最上面温馨提示楼盘对比数据
          // 是否展现楼盘的方法  删除小图标那个按钮   直接这样写函数
          $scope.showHouseInfo = function (key) {
              if (key < 4) {
                  //删除所选房源
                  if ($scope.house[1].houseId == '') {
                      alert("至少保留一个房源");
                  } else {
                      $.ajax({
                          type:"post",
                          url:"/deleteContrastHouse",
                          data: { houseId : $scope.house[key].houseId},
                          async: false,
                          success: function (list) {

                              $scope.houseArr = list;   //用于判断
                              $scope.house[key].houseId = '';
                              $scope.house[key].content = {};
                              //保证每个房源紧挨着不会出现空一列的情况
                              for (var k = 0; k < 3; k++) {
                                  if ($scope.house[k].houseId == "") {
                                      for (var y = k + 1; y < 4; y++) {
                                          if ($scope.house[y].houseId != "") {
                                              $scope.house[k].houseId = $scope.house[y].houseId;
                                              $scope.house[k].content = $scope.house[y].content;
                                              $scope.house[y].houseId = "";
                                              $scope.house[y].content = {};
                                              break;
                                          }
                                      }
                                  }
                              }
                          }
                      })
                  }
              }
              $scope.showHouseList()
              for (var i = 0; i < 4; i++) {
                  if ($scope.house[i].houseId != "") {
                      $http({
                          method: 'POST',
                          url: '/viewSimilarProjectDetail',
                          data: "projectId=" + $scope.house[i].houseId,  // pass in data as strings
                          headers: {'Content-Type': 'application/x-www-form-urlencoded'}
                      }).then(function successCallback(response) {
                          console.log('打印房源数据6')
                          console.log(response)

                          // let total1= response.data.content
                          // let total2 = total1.comparison
                          //
                          // if(total2 != null && total2 != '' && total2 != undefined){
                          //     $scope.reviewNum = eval(Object.values(total2).join("+"))
                          //     console.log('打印啊',$scope.reviewNum)
                          //
                          // }

                          for (var j = 0; j < 4; j++) {
                              if (response.data.content.ProjectID == $scope.house[j].houseId ) {
                                  $scope.house[j].content = response.data.content;
                                  // 自定义个楼盘评测和 筛进数据里
                                  let total2 = $scope.house[j].content.comparison
                                  if(total2 != null && total2 != '' && total2 != undefined){
                                      $scope.house[j].content.reviewNum = eval(Object.values(total2).join("+"))
                                      console.log('打印啊',$scope.house[j].content.reviewNum)

                                  }

                                  if($scope.sessionId!=""){
                                      $scope.favorite($scope.house[j].houseId)
                                  }
                                  if($scope.house[j].content.PropertyFee<=0){

                                      $scope.house[j].content.PropertyFee="暂无资料";
                                  }
                              }
                          }
                      }, function errorCallback(response) {
                      });
                  }
              }
          }


          // 点击喜欢当前楼盘
          $scope.favorite = function (houseId) {
              var content=false;
              $http({
                  method: 'POST',
                  url: '/manageMyCollection',
                  data: "sessionId=" + $scope.sessionId + "&&houseId="+ houseId+"&&type=4&&methodName=checkFavorite",  // pass in data as strings
                  headers: {'Content-Type': 'application/x-www-form-urlencoded'}
              }).then(function successCallback(response) {
                  if (response.data.content == 1) {
                      content=true;
                      for(var d=0;d<4;d++){
                          if(houseId==$scope.house[d].houseId){
                              $scope.house[d].favorite=true;
                          }
                      }
                  }
              }, function errorCallback(response) {
              });
          }
          // 取消喜欢楼盘方法
          $scope.cancelFavorite=function(houseId){
              if($scope.house[houseId].favorite==true){
                  $http({
                      method: 'POST',
                      url: '/manageMyCollection',
                      data: "sessionId=" + $scope.sessionId + "&&houseId="+ $scope.house[houseId].houseId+"&&type=4&&methodName=cancelFavorite",
                      headers: {'Content-Type': 'application/x-www-form-urlencoded'}
                  }).then(function successCallback(response) {
                      if (response.data.status == 1) {
                          $scope.house[houseId].favorite=false;
                      } else {
                          alert("取消失败");
                      }
                  }, function errorCallback(response) {
                  });
              }else{
                  $http({
                      method: 'POST',
                      url: '/manageMyCollection',
                      data: "sessionId=" + $scope.sessionId + "&&houseId="+ $scope.house[houseId].houseId+"&&type=4&&methodName=newFavorite",
                      headers: {'Content-Type': 'application/x-www-form-urlencoded'}
                  }).then(function successCallback(response) {
                      if (response.data.status == 1) {
                          $scope.house[houseId].favorite=true;
                      } else {
                          alert("收藏失败");
                      }
                  }, function errorCallback(response) {
                  });
              }
          }
          // 添加楼盘方法
      	$scope.addHouse=function(item){
      		var i = 0;
      		if($scope.house[3].houseId!=""){
      			alert("最多对比4个房源")
      		}else{
                var hous = true
                for(var h=0;h<4;h++){
                    if($scope.house[h].houseId==item.ProjectID){
                        hous=false;
                        alert("此房源已经加入对比")
                    }
                }
                if(hous){
                    $.ajax({
                        type: "post",
                        data: { houseId : item.ProjectID,houseName:item.projectName, houseType : item.projectType},
                        url: "/addContrastHouse",
                        //dataType: 'json',
                        async: false,
                        success: function (list) {
                            console.log('顶部添加楼盘')
                            console.log(list)
                            $scope.houseArr = list;   //用于判断
                            for(i=0 ;i<4;i++){
                                if($scope.house[i].houseId==""){
                                    $scope.house[i].houseId=item.ProjectID;
                                    $scope.showHouseInfo(111)
                                    break;
                                }
                            }
                        },
                    });

                }

      		}
      	}
         // 最顶部匹配的房源
      	$scope.showHouseList=function(){
      		$http({
	            method  : 'POST',
	            url     : '/viewContrastProjects',
	            data    : "projectId="+$scope.house[0].houseId,  // pass in data as strings
	            headers : { 'Content-Type': 'application/x-www-form-urlencoded' }
				}).then(function successCallback(response) {


                console.log('最顶部')
                console.log(response)
					$scope.houseList=response.data.content
		   		}, function errorCallback(response) {});
      	}

		$scope.showHouseInfo(111);
      	$scope.showHouseList();
          $(".serachHouseInfo").click(function () {
              clearTimeout(quene());
              quene();
          });
          function quene() {
              setTimeout(function() {serachHouseInfo(1);},500);
          }
          // 搜索楼盘方法
          function serachHouseInfo(method) {
              $(".serachHouseInfo").autocomplete({
                  source: function( request, response ) {
                      var searchInfo = request.term;
                      searchInfo = encodeURI(searchInfo);
                      $.ajax({
                          url: "/searchNewHouse",
                          dataType: "json",
                          data: {key: 1,serachInfo: searchInfo},
                          success: function (data) {
                              console.log('搜索楼盘')
                              console.log(data)
                              $scope.houseArr = data;   //用于判断

                              if (data.length != 0) {
                                  response($.map(data, function (item) {
                                      var highLightTitle = item.title;
                                      highLightTitle = highLightTitle.replace(
                                              new RegExp(
                                                      "(?![^&;]+;)(?!<[^<>]*)(" +
                                                      $.ui.autocomplete.escapeRegex(request.term) +
                                                      ")(?![^<>]*>)(?![^&;]+;)", "gi"
                                              ), "<strong>$1</strong>");
                                      return {
                                          label: "<i style='float:right'></i>" + highLightTitle + (item.projectType == 2 ? '(别墅)' : item.projectType == 3 ? '(写字间/公寓)' : ''),
                                          tableId: item.tableId,
                                          projectType: item.projectType,
                                          projectName: item.title,
                                          value: item.title,
                                      };
                                  }));
                              }
                          }
                      })
                  },
                  select: function( event, ui ) {
                      var houseId = ui.item.tableId;
                      var houseName = ui.item.projectName;
                      var houseType = 1;

                      var item ={};
                      item.ProjectID = ui.item.tableId;
                      item.projectName = ui.item.projectName;
                      item.projectType = 1;
                      // 调用函数方法
                      $scope.addHouse(item)
                  }
              });
          }
	})
    //楼盘对比浮窗
    window.onscroll = function() {
        var s = $(this).scrollTop();

        if (s >= 630) {
            $(".nFix").css("display","block");
        } else {
   $(".nFix").css("display","none");

        }
    }
	</script>
</body>

</html>