<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${companyInfo.brand_name+'_品牌展示区 - 房小二网 '}"></title>
    <meta name="keywords"
          th:content="${companyInfo.brand_name+',沈阳'+#strings.toString(companyInfo.brand_name).replaceAll('旗舰店','')+',沈阳'+#strings.toString(companyInfo.brand_name).replaceAll('旗舰店','')+'楼盘，沈阳'+#strings.toString(companyInfo.brand_name).replaceAll('旗舰店','')+'项目'}"/>
    <meta name="description"
          th:content="${companyInfo.brand_name+'为您提供 沈阳'+#strings.toString(companyInfo.brand_name).replaceAll('旗舰店','')+'的全部项目位置，在售情况，报价等各种详情，让您轻松找房。买房卖房，就上房小二网。'}"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/brand/branddetails.css?v=20181120" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20181030">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/Housekeeper/Housekeeper.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
</head>
<style>
    .vrs{ width: 34px; height: 34px; position: absolute; left: 5px; bottom: 5px; z-index: 0; }
    .vrs i{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 3; display: block; background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;}
    .vrs b{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 1; display: block; background-color: rgba(0,0,0,0.5); border-radius: 50%;}
</style>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<input type="hidden" id="glzs" th:value="${session.sessionId}" />
<div class="crumb">
    <div class="content_box">
        <h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> > <a th:href="${'/houses/'}">沈阳新房</a> > <a th:href="${'/brandCompany/'}">品牌展示区</a> > <th:block th:text="${companyInfo.brand_name}"></th:block></h1>
    </div>
</div>
<input type="hidden" th:value="${companyInfo.id}" id="bId">
<div class="banner" th:style="${'background-image:url('+companyInfo.pc_pic+')'}">
    <img th:src="${#strings.isEmpty(companyInfo.logo)?'':companyInfo.logo}">
    <div class="text" th:if="${!#strings.isEmpty(companyInfo.brand_slogan)}" >
        <span th:text="${companyInfo.brand_slogan}">城乡建设和生活服务商</span>
    </div>
    <div class="bd"></div>
</div>
<div class="introduce">
    <div class="content_box">
        <div class="title">
            <h1>品牌介绍</h1>
           <img src="https://static.fangxiaoer.com/web/images/brand/title02.png">
        </div>
        <div class="content" th:unless="${#strings.isEmpty(companyInfo.brand_description)}">
            <p th:text="${companyInfo.brand_description}">万科企业股份有限公司成立于1984年，经过三十余年的发展，已成为国内领先的城乡建设与生活服务商。沈阳万科深耕城市25载，以“为普通人盖好房子，盖有人用的房子”的理念，为10万余户家庭带来优质居住体验。从关注人居品质到人的需求，万科不断迭代企业理念，践行企业的社会责任，竭力构建美好城市生活。</p>
        </div>
    </div>
</div>
<div class="map_box">
    <div class="content_box">
        <div class="title">
            <div class="line"></div>
           <div class="name">
               <h1>项目布局</h1>
           </div>
            <div class="line"></div>
            <div class="cl"></div>
            <img src="https://static.fangxiaoer.com/web/images/brand/title03.png">
        </div>
        <div class="content">
            <div th:include="house/detail/fragment_map::brandQQMap"></div>
        </div>
    </div>
</div>
<div class="properties" th:if="${companyInfo.projectInfos != null and !#lists.isEmpty(companyInfo.projectInfos)}">
    <div class="content_box">
            <div class="title">
                <h1>在售楼盘</h1>
                <img src="https://static.fangxiaoer.com/web/images/brand/title04.png">
            </div>
        <div class="content">
            <ul >
                <li th:each="project,pIndex:${companyInfo.projectInfos}">
                    <a th:href="${'/house/'+project.projectId+'-'+project.type+'.htm'}" target="_blank">
                <div class="photo" style="position: relative;">
                    <img th:src="${#strings.isEmpty(project.pic)?'':project.pic}">
                    <div class="vrs" th:if="${project.hasPan eq '1'}"><i></i></div>
                </div>
                <div class="info">
                    <div class="name">
                        <h1 ><th:block th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></th:block><label class="pType" th:text="${#strings.toString(project.type) eq '2' ?'[别墅]':''}"></label></h1>
                    </div>
                    <div class="huxing">
                        <p>
                            <span th:if="${!#lists.isEmpty(project.layout) and project.layout !=null}" ><th:block th:each="layout,i:${project.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block><th:block th:each="layout,i:${project.layout}" th:if="${i.index &gt; 0}" th:text="${'/'+layout.RoomType+'居'}"></th:block></span>
                            <span th:if="${!#lists.isEmpty(project.area) and project.area !=null}">
                                <th:block th:if="${#lists.isEmpty(project.area) and #lists.isEmpty(project.area)}" th:text="${'暂无资料'}"></th:block>
                                <th:block th:each="area:${project.area}" th:text="${#numbers.formatInteger(area.minArea,1)+'~'+#numbers.formatInteger(area.maxArea,1)+'㎡'}"></th:block>
                            </span>
                            <span th:if="${#lists.isEmpty(project.area) and #lists.isEmpty(project.layout)}">暂无资料</span>
                        </p>
                    </div>
                    <div class="address">
                       <p> <span th:text="${#strings.isEmpty(project.regionName)?'暂无资料':project.regionName}">铁西区</span><span th:text="${#strings.isEmpty(project.projectAddress)?'暂无资料':project.projectAddress}" th:title="${#strings.isEmpty(project.projectAddress)?'暂无资料':project.projectAddress}">铁西区昆明湖街39号铁西区昆明湖街39号</span></p>
                    </div>
                    <div class="price">
                        <th:block th:if="${project.projectStatus ne '3'}">
                            <p>
                            <span class="type" th:if="${project.mPrice != null}" th:text="${project.mPrice.priceType}"></span>
                            <span th:if="${project.mPrice != null}"  class="number">
                                    <th:block  th:text="${#numbers.formatInteger(project.mPrice.priceMoney,1) }"></th:block>
                            </span>
                            <span class="Company" th:if="${project.mPrice != null}">元/㎡</span>
                            <span th:if="${project.mPrice == null}"><b>待定</b></span>
                            </p>
                        </th:block>
                        <p th:if="${project.projectStatus eq '3'}" >
                             <span class="solded">新房已售完</span>
                        </p>
                        <!--<p><span class="type">均价</span><span class="number">2300</span><span class="Company">元/㎡</span></p>-->
                    </div>
                    <div class="label" th:if="${project.features != null and !#strings.isEmpty(project.features)}">
                        <span th:each="features,i:${#strings.setSplit(project.features,' ')}" th:if="${i.index &lt; 3}" th:text="${features}">生态田园</span>
                    </div>
                </div>
                    </a>
                <div class="cl"></div>
                </li>
                <div class="cl"></div>
            </ul>
            <div class="btn" th:if="${companyInfo.projectInfos != null and !#lists.isEmpty(companyInfo.projectInfos) and #lists.size(companyInfo.projectInfos) gt 8}">
                <span>查看全部</span>
            </div>
        </div>
    </div>
</div>
<div class="dynamic">
    <div class="content_box">
        <div class="title">
            <h1>最新动态</h1>
            <img src="https://static.fangxiaoer.com/web/images/brand/title05.png">
        </div>
        <div class="content">
            <div class="left_box" th:if="${brandDynamic != null and !#lists.isEmpty(brandDynamic)}">
                <ul>
                    <li th:each="dynamic,dIndex:${brandDynamic}">
                        <div class="title_sun">
                            <a target="_blank"  th:href="${'/house/'+dynamic.id + '-' + dynamic.type + '.htm'}" >
                                    <span ><th:block th:text="${#strings.isEmpty(dynamic.name)?'暂无资料':(dynamic.name)}"></th:block><label class="pType" th:text="${#strings.toString(dynamic.type) eq '2' ?'[别墅]':''}"></label> </span>
                            </a>
                        </div>
                        <div class="content_sun" th:if="${dynamic.value != null and !#lists.isEmpty(dynamic.value)}">
                            <th:block th:each="dvItem:${dynamic.value}">
                                <div class="Make" th:if="${dvItem.dyType eq '2' and !#strings.isEmpty(dvItem.dyTime) and !#strings.isEmpty(dvItem.dyDesc)}">
                                    <h1 th:text="${#strings.isEmpty(dvItem.dyTime)?'暂无资料':(dvItem.dyTime)}">2019.12.31</h1>
                                    <p th:text="${#strings.isEmpty(dvItem.dyDesc)?'暂无资料':(dvItem.dyDesc)}">该项目为期房，预计2019年10月份交房。</p>
                                    <img src="https://static.fangxiaoer.com/web/images/brand/Make.png">
                                </div>
                                <div class="dynamic_sun" th:if="${dvItem.dyType eq '1'and !#strings.isEmpty(dvItem.dyTime) and !#strings.isEmpty(dvItem.dyDesc)}">
                                    <h1  th:text="${#strings.isEmpty(dvItem.dyTime)?'暂无资料':dvItem.dyTime}">2019.12.31</h1>
                                    <p><th:block th:text="${#strings.isEmpty(dvItem.dyDesc)?'暂无资料': #strings.abbreviate(dvItem.dyDesc,100)}"></th:block> <a th:href="${'/house/news/'+dvItem.projectId + '-' + dvItem.projectType + '.htm'}" target="_blank">【查看全部】</a></p>
                                    <img src="https://static.fangxiaoer.com/web/images/brand/dynamic.png">
                                </div>
                                <div class="quotation" th:if="${dvItem.dyType eq '3'and !#strings.isEmpty(dvItem.dyTime) and !#strings.isEmpty(dvItem.dyDesc)}">
                                    <h1 th:text="${#strings.isEmpty(dvItem.dyTime)?'暂无资料':dvItem.dyTime}">2019.12.31</h1>
                                    <p th:text="${#strings.isEmpty(dvItem.dyDesc)?'暂无资料':dvItem.dyDesc}">该项目为期房，预计2019年10月份交房。</p>
                                    <img src="https://static.fangxiaoer.com/web/images/brand/quotation.png">
                                </div>
                            </th:block>
                        </div>
                    </li>
                </ul>
                <div class="btn" th:if="${companyInfo.projectInfos != null and !#lists.isEmpty(companyInfo.projectInfos) and #lists.size(companyInfo.projectInfos) gt 4}">
                    <h1>查看全部</h1>
                </div>
            </div>
            <div class="right_box">
                <div class="title_sun">
                    <h1>最新资讯</h1>
                    <a href="javascript:void(0)" onclick="nextList()">换一批</a>
                    <div class="cl"></div>
                </div>
                <div class="content_sun">
                    <ul id="currentNews">
                    </ul>
                    <div class="noList" style="display: none;">
                        <h1>我们正在努力完善该楼盘资讯，<br>
                            敬请期待！</h1>
                    </div>
                </div>
            </div>
            <div class="cl"></div>
        </div>
    </div>
</div>
<script>
$(".properties .content_box .content ul").addClass("over")
    $(".properties .content_box .content .btn").click(
        function () {
           if($(".properties .content_box .content ul").hasClass("over")){
               $(".properties .content_box .content ul").removeClass("over")
               $(this).find("span").text("收起")
               $(this).find("span").css("background-image","url(https://static.fangxiaoer.com/web/images/brand/up.png)")
           }else{
               $(".properties .content_box .content ul").addClass("over")
               $(this).find("span").text("查看全部")
               $(this).find("span").css("background-image","url(https://static.fangxiaoer.com/web/images/brand/down.png)")
           }
        }
    )
    var page = 1;
    var pageSize = 6;
    var totalNumber = 0;
    var totalPage = 5;
    var brandId = $("#bId").val();
    function loadNews() {
        $("#currentNews").html("");
        $.ajax({
            type: "POST",
            async: false,
            data: { brandId: brandId, page: page, pageSize: pageSize },
            url: "/brandCompanyNews",
            success: function (data) {
                if (data.status == "1") {
                    var newsContent = data.content;

                    if(data.length == 0){
                        $("#currentNews").remove();
                        $(".noList").show();
                    }else {
                        var newsHtmels = "";
                        for(var i=0;i<newsContent.length;i++){
                            newsHtmels = newsHtmels+' <li><a href="/news/'+newsContent[i].id+'.htm" target="_blank">'+newsContent[i].titleShow+'</a></li>';
                        }
                        $("#currentNews").append(newsHtmels);
                        totalNumber = data.msg;
                        totalPage = parseInt(totalNumber/pageSize) + (totalNumber%pageSize==0? 0:1);
                    }
                }else {
                    console.log(data.msg)
                    $("#currentNews").remove();
                    $(".noList").show();
                }
            },
            error: function (error) {
                console.log(error);
            }
        });
    }
    $(function () {
        loadNews();
    })
    function nextList() {
        if(page < totalPage){
            page = page+1;
        }else {
            page = 1;
        }
        loadNews();
    }
var lh=0

if( $(".dynamic .content_box .content .left_box ul li").length>5){
    for (var i=0;i<5;i++){
        $(".dynamic .content_box .content .left_box ul li").eq(i).height()
        lh=lh+$(".dynamic .content_box .content .left_box ul li").eq(i).height()
        $(".dynamic .content_box .content .left_box ul").height(lh)
    }
}else{
    $(".dynamic .content_box .content .left_box ul").height("auto")
}
$(".dynamic .content_box .content .left_box ul").addClass("hover")

    $(".dynamic .content_box .content .left_box .btn").click(
        function () {
           if($(".dynamic .content_box .content .left_box ul").hasClass("hover")){
               $(".dynamic .content_box .content .left_box .hover").height("auto")
               $(".dynamic .content_box .content .left_box ul").removeClass("hover")
               $(".dynamic .content_box .content .left_box .btn h1").text("收起")
           }else{

               $(".dynamic .content_box .content .left_box ul").addClass("hover")
               $(".dynamic .content_box .content .left_box .hover").height(lh)
               $(".dynamic .content_box .content .left_box .btn h1").text("查看全部")
           }
        }
    )
    var ww=$(".banner img").width()
$(".banner img").css("margin-left",-(ww/2)-30);
    if($(".properties .content_box .content ul li").length%2!=0){
        $(".properties .content_box .content ul li:last").after("<li><img src='https://static.fangxiaoer.com/web/images/brand/three.jpg'></li>")
    }
var dl;
var dh
$(window).resize(function(){
     dl=$(".dynamic .content_box .content .right_box").offset().left;
     dh=$(".dynamic .content_box .content").offset().top-$(window).scrollTop();
    if(dh<=10){
        $(".dynamic .content_box .content .right_box").css("position","absolute")
        $(".dynamic .content_box .content .right_box").css("right",0)
        $(".dynamic .content_box .content .right_box").css("top",-dh+10)
    }else{
        $(".dynamic .content_box .content .right_box").css("position","absolute")
        $(".dynamic .content_box .content .right_box").css("left","auto")
        $(".dynamic .content_box .content .right_box").css("right","0")
        $(".dynamic .content_box .content .right_box").css("top","0")
    }

})

$(window).scroll(function() {
    dl=$(".dynamic .content_box .content .right_box").offset().left;
    dh=$(".dynamic .content_box .content").offset().top-$(window).scrollTop();
    if(dh<=10){
        $(".dynamic .content_box .content .right_box").css("position","absolute")
        $(".dynamic .content_box .content .right_box").css("right",0)
        $(".dynamic .content_box .content .right_box").css("top",-dh+10)
    }else{
        $(".dynamic .content_box .content .right_box").css("position","absolute")
        $(".dynamic .content_box .content .right_box").css("left","auto")
        $(".dynamic .content_box .content .right_box").css("right","0")
        $(".dynamic .content_box .content .right_box").css("top","0")
    }
})
</script>
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<!--有验证码订单-->
<div  th:include="house/detail/fragment_order::useCode" ></div>
<!--无验证码订单-->
<div  th:include="house/detail/fragment_order::guideMessage" ></div>

</body>
</html>