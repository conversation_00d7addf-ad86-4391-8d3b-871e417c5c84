<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>品牌展示区 - 房小二网</title>
    <meta name="keywords" content="品牌展示区,新房品牌展示区,沈阳新房品牌展示区,房小二网品牌展示区" />
    <meta name="description" content="线上优选新房，沈阳新房线上在售项目，品牌展示区  足不出户云看房，尽在房小二网" />
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/brand/brandIndex.css?v=20230115" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

</head>
<style>

</style>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<div class="banner" style="background-image: url('https://static.fangxiaoer.com/web/images/sytop3.jpg')"></div>
<div class="crumb">
    <div class="content_box">
        <h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> > <a th:href="${'/houses/'}">沈阳新房</a> > 品牌展示区</h1>
    </div>
</div>
<div class="reading">
    <div class="content_box">
       <div class="title">
           <img src="https://static.fangxiaoer.com/web/images/brand/title.png">
       </div>
        <div class="content">
            <p>品牌的魅力，在于很多方面。品质，口碑，形象，物业，设计，每一个细节都透露出大品牌深耕多年的实力和凝聚力。<br>时代的进步，城市的发展，人们的品牌意识也普遍增强，也更加相信品牌带给大众独有的居住体验。</p>
        </div>
    </div>
</div>
<div class="list_box">
    <div class="content_box">
        <ul th:if="${companies != null and !#lists.isEmpty(companies)}">
            <li th:each="company,cIndex:${companies}" th:if="${!cIndex.last}" th:class="${cIndex.index %3 ==1 ?'hover':''}">
                <a th:href="${'/brandCompany/'+company.id+'.htm'}" target="_blank">
                    <div class="photo">
                        <div class="photo_sun"  th:style="${'background-image:url('+company.pic+')'}">
                            <div class="bd"></div>
                        </div>
                        <div class="layer">
                            <h1 th:text="${#strings.isEmpty(company.brandName)?'暂无资料':company.brandName}">万科品牌旗舰店</h1>
                            <h2 th:text="${#strings.isEmpty(company.slogan)?'暂无资料':company.slogan}" >城乡建设和生活服务商</h2>
                        </div>
                    </div>
                </a>
                <div class="logo_sun">
                   <table>
                       <tr>
                           <td>
                               <img th:src="${#strings.isEmpty(company.logo)?'':company.logo}">
                           </td>
                       </tr>
                   </table>
                </div>
            </li>
            <th:block th:if="${cIndex.last}" th:each="company,cIndex:${companies}">
                <th:block th:if="${cIndex.index % 3 == 0}">
                    <li>
                        <a th:href="${'/brandCompany/'+company.id+'.htm'}" target="_blank">
                            <div class="photo">
                                <div class="photo_sun"  th:style="${'background-image:url('+company.pic+')'}">
                                    <div class="bd"></div>
                                </div>
                                <div class="layer">
                                    <h1 th:text="${#strings.isEmpty(company.brandName)?'暂无资料':company.brandName}">万科品牌旗舰店</h1>
                                    <h2 th:text="${#strings.isEmpty(company.slogan)?'暂无资料':company.slogan}" >城乡建设和生活服务商</h2>
                                </div>
                            </div>
                        </a>
                        <div class="logo_sun">
                            <table>
                                <tr>
                                    <td>
                                        <img th:src="${#strings.isEmpty(company.logo)?'':company.logo}">
                                    </td>
                                </tr>
                            </table>

                        </div>
                    </li>
                    <li class="longImage">
                        <img src="https://static.fangxiaoer.com/web/images/brand/two.jpg">
                    </li>
                </th:block>
                <th:block th:if="${cIndex.index % 3 == 1}">
                    <li class="hover">
                        <a th:href="${'/brandCompany/'+company.id+'.htm'}" target="_blank">
                            <div class="photo" >
                                <div class="photo_sun" th:style="${'background-image:url('+company.pic+')'}">
                                    <div class="bd"></div>
                                </div>
                                <div class="layer">
                                    <h1 th:text="${#strings.isEmpty(company.brandName)?'暂无资料':company.brandName}">万科品牌旗舰店</h1>
                                    <h2 th:text="${#strings.isEmpty(company.slogan)?'暂无资料':company.slogan}" >城乡建设和生活服务商</h2>
                                </div>
                            </div>
                        </a>
                        <div class="logo_sun">
                            <table>
                                <tr>
                                    <td>
                                        <img th:src="${#strings.isEmpty(company.logo)?'':company.logo}">
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </li>
                    <li>
                        <img src="https://static.fangxiaoer.com/web/images/brand/one.jpg">
                    </li>
                </th:block>
                <th:block th:if="${cIndex.index % 3 == 2}">
                    <li>
                        <a th:href="${'/brandCompany/'+company.id+'.htm'}" target="_blank">
                            <div class="photo" >
                                <div class="photo_sun" th:style="${'background-image:url('+company.pic+')'}">
                                    <div class="bd"></div>
                                </div>
                                <div class="layer">
                                    <h1 th:text="${#strings.isEmpty(company.brandName)?'暂无资料':company.brandName}">万科品牌旗舰店</h1>
                                    <h2 th:text="${#strings.isEmpty(company.slogan)?'暂无资料':company.slogan}" >城乡建设和生活服务商</h2>
                                </div>
                            </div>
                        </a>
                        <div class="logo_sun">
                            <table>
                                <tr>
                                    <td>
                                        <img th:src="${#strings.isEmpty(company.logo)?'':company.logo}">
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </li>
                </th:block>
            </th:block>

            <div class="cl"></div>
        </ul>
    </div>
</div>

<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::newHouseFloat"></div>
</body>
</html>