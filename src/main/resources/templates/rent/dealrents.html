<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'沈阳' + seoTitle + seoSubName + '租房_沈阳' + seoTitle + seoSubName + '租房出售_沈阳房屋出租-房小二网'}"></title>
    <meta name="keywords" th:content="${'沈阳' + seoTitle + seoSubName + '租房,沈阳' + seoTitle + seoSubName + '中介,沈阳租房'}" />
    <meta name="description" th:content="${'房小二网沈阳租房频道为您提供海量真实的沈阳' + seoTitle + seoSubName + '租房信息，沈阳' + seoTitle + seoSubName + '房源信息，100%真实审核。租好房,上房小二网。100%真实审核。租好房,上房小二网。'}" />
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/fang3">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2017.css" />
    <link rel="stylesheet"                 href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <!--<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/zufang/list.css" />-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/fxe_confirm.js" ></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/AjaxforJquery.js" ></script>
    <!--
        <script type="text/javascript" src="https://static.fangxiaoer.com/js/esf_fxe_bnzf2017.js" ></script>
    -->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        $(function () {
            var r_or_s = location.pathname;
            if (r_or_s.indexOf('k1') != -1) {
                $("#btn2").attr("class","hover");
                $("#Search_ditie").css('display','block');
            }
            else {
                $("#btn1").attr("class","hover");
                $("#Search_zf").css('display','block');
            }
        })
    </script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/contractList.css"/>
<body>
<form name="form1" method="post" action="" id="form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=11"></div>

    <div class="main">
        <!--网页位置-->
        <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/rents/" target="_blank">沈阳租房</a> &gt; <a href="/dealRents/">成交房源</a></div>
        <div id="option">
            <ul>
                <li class="fenlei">
                    <p>位置：</p>
                    <a id="btn1" th:each="r,i:${region}" th:if="${i.index eq 0 }" th:href="${r.url}"  class=""><b class="listIcon listIcon_dw"></b>区域<i></i></a>
                    <a id="btn2" th:each="k,i:${kinds}"  th:if="${i.index eq 1 }" th:href="${k.url}"  class=""><b class="listIcon listIcon_ditie"></b>地铁<i></i></a>
                    <a href="/saleVillages/"><b class="listIcon listIcon_esf"></b>小区<i></i></a>
                    <a href="/static/rentmap.htm"><b class="listIcon listIcon_map"></b>地图<i></i></a>
                </li>
                <!--区域-->
                <li id="Search_zf" class="leibie" style="display: none">
                    <a th:each="r:${region}" th:href="${r.url}" th:text="${#strings.toString(r.name).replaceAll('全部','不限')}" th:id="'r'+${r.id}" th:class="${r.selected}? 'hover':''"></a>
                </li>
                <!-- 地铁-->
                <li id="Search_ditie" class="leibie" style="display: none">
                    <a th:each="s:${subway}" th:href="${s.url}" th:text="${#strings.toString(s.name).replaceAll('全部','不限')}" th:id="'r'+${s.id}" th:class="${s.selected}? 'hover':''">></a>
                </li>

                <li>
                    <p>租金：</p>
                    <a th:each="p:${price}" th:href="${p.url}" th:text="${#strings.toString(p.name).replaceAll('全部','不限')}" th:id="'p'+${p.id}" th:class="${p.selected}? 'hover':''">></a>
                </li>
                <script>p
                $(function () {
                    //户型和厅室的处理
                    var url_b = location.pathname;
                    var num_b = url_b.indexOf("w2");
                    if (num_b == -1) {
                        $("#Search_room").show();
                        $("#Search_Bedroom").hide();
                    }else {
                        $("#Search_room").hide();
                        $("#Search_Bedroom").show();
                    }

                })
                </script>
                <li id="Search_RentType">
                    <p>方式：</p>
                    <a th:each="w:${way}" th:href="${w.url}"  th:id="'w'+${w.id}" th:class="${w.selected}? 'hover':''">
                        <th:block th:text="${#strings.toString(w.name).replaceAll('全部','不限')}"></th:block>
                    </a>
                </li>
                <li id="Search_room">
                    <p>户型：</p>
                    <a th:each="l:${layout}" th:href="${l.url}" th:text="${#strings.toString(l.name).replaceAll('全部','不限')}" th:id="'l'+${l.id}" th:class="${l.selected}? 'hover':''">></a>
                </li>
                <li id="Search_Bedroom">
                    <p>厅室：</p>
                    <a th:each="b:${bedroom}" th:href="${b.url}" th:text="${#strings.toString(b.name).replaceAll('全部','不限')}" th:id="'b'+${b.id}" th:class="${b.selected}? 'hover':''">></a>
                </li>
            </ul>
        </div>
        <div id="option_other">
            <ul>
                <li><p>更多：</p>
                    <div class="select_box">
                        <div class="select_info">装修不限</div>
                        <ul>
                            <li th:each="d:${decorate}">  <a th:href="${d.url}" th:text="${d.id eq '' ? '装修不限':d.name}" th:class="${d.selected}? 'hover':''">></a></li>
                        </ul>
                    </div>
                    <!--<div class="select_box">
                        <div class="select_info">来源不限</div>
                        <ul>
                            <li th:each="g:${grigon}">
                                <a th:href="${g.url}" th:if="${g.name eq '全部'}" th:text="来源不限" th:class="${g.selected}? 'hover':''">
                                </a>
                                <a th:href="${g.url}" th:if="${g.name eq '个人'}" th:text="个人真房源" th:class="${g.selected}? 'hover':''">
                                </a>
                                <a th:href="${g.url}" th:if="${g.name eq '经纪人'}" th:text="经纪人房源" th:class="${g.selected}? 'hover':''">
                                </a>
                            </li>
                        </ul>
                    </div>-->
                </li>
            </ul>
        </div>
        <div id="option_info" style="display: none">
            <b >已选：</b>
            <!--小区名-->
            <div th:if="${!#strings.isEmpty(subId) and !#strings.isEmpty(sub.title)}">
                <span class="condition"><th:block th:text="${sub.title}"></th:block></span>
                <i class="cleanUrl" th:value="${'-v'+subId}"></i>
            </div>
            <!--区域-->
            <div th:each="r:${region}" th:if="${r.selected and r.name ne '全部'}">
                <span class="condition"><th:block th:text="${r.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'r'+r.id}"></i>
            </div>
            <!--地铁-->
            <div th:each="s:${subway}" th:if="${s.selected and s.name ne '全部'}">
                <span class="condition"><th:block th:text="${s.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'s'+s.id}"></i>
            </div>
            <!--价格-->
            <div th:each="p:${price}" th:if="${p.selected and p.name ne '全部'}">
                <span class="condition"><th:block th:text="${p.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'p'+p.id}"></i>
            </div>
            <!--方式-->
            <div th:each="w:${way}" th:if="${w.selected and w.name ne '全部'}">
                <span class="condition"><th:block th:text="${w.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'w'+w.id}"></i>
            </div>
            <!--户型-->
            <div th:each="l:${layout}" th:if="${l.selected and l.name ne '全部'}">
                <span class="condition"><th:block th:text="${l.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'l'+l.id}"></i>
            </div>
            <!--主次卧-->
            <div th:each="b:${bedroom}" th:if="${b.selected and b.name ne '全部'}">
                <span class="condition"><th:block th:text="${b.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'b'+b.id}"></i>
            </div>
            <!--装修-->
            <div th:each="d:${decorate}" th:if="${d.selected and d.name ne '全部'}">
                <span class="condition"><th:block th:text="${d.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'d'+d.id}"></i>
            </div>
            <!--来源-->
            <div th:each="g:${grigon}" th:if="${g.selected and g.name ne '全部'}">
                <span class="condition"><th:block th:text="${g.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'g'+g.id}"></i>
            </div>
            <script>
                /*判断是否显示条件栏目*/
                $(function () {
                    if($(".condition").text() != ""){
                        $("#option_info").css("display","block");
                    }
                })
                /*去掉条件标签*/
                $(".cleanUrl").click(function () {
                    var oldUrl = location.pathname;
                    if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
                        var moreUrl ="-" + $(this).attr("value");
                    }else{ var moreUrl = $(this).attr("value");}

                    var newUrl = oldUrl.replace(moreUrl,"");
                    window.location.href = newUrl;
                    //跳到newUrl指定链接
                });
            </script>
            <a href="/dealRents/" class="clean">清空筛选条件</a>
        </div>
        <div id="main">
            <div id="left">
                <div class="sort">
                    <p id="sortParam">
                        <a id="0" href="/rents/o0">综合排序</a>
                        <a id="3" href='/rents/o3' >租金</a>
                        <a id="5" href='/rents/o5' >面积</a>
                        <a id="1" href='/rents/o1' >最新</a>
                        <script>
                            $(document).ready(function(){
                                var ref = location.pathname;
                                if(ref.indexOf('dealRents/') == -1) ref +='/';
                                var ref2 = ref.replace(/-o[0-9]/,'');
                                var num = ref.indexOf("o");
                                var o = ref.substring(num+1,num+2);
                                if(num == -1){
                                    $("#0").attr("class","hover");
                                }
                                else{
                                    if (o == 0){
                                        $("#0").attr("class","hover");
                                    }
                                    if (o == 1){
                                        $("#1").attr("class","hover");
                                    }
                                    if (o == 2 ){
                                        $("#3").attr("class","sort_jg up");
                                    }
                                    if (o == 3){
                                        $("#3").attr("class","sort_jg down");
                                    }
                                    if (o == 4){
                                        $("#5").attr("class","sort_jg up");
                                    }
                                    if (o == 5){
                                        $("#5").attr("class","sort_jg down");
                                    }
                                }
                                $("#0").attr("href",ref2+"-o0");
                                $("#3").attr("href",ref2+"-o3");
                                $("#5").attr("href",ref2+"-o5");
                                $("#1").attr("href",ref2+"-o1");
                            });
                        </script>
                        <script >
                            $("#sortParam a").click(function () {
                                var ref = location.pathname;
                                if(ref.indexOf('dealRents/') == -1) ref +='/';
                                var ref2 = ref.replace(/-o[0-9]/,'');
                                var num = ref.indexOf("o");
                                var o = ref.substring(num+1,num+2);
                                var to = $(this).attr("id");
                                if (o == to && o ==3){
                                    var o2 = o-1;
                                    $(this).attr("href",ref2+"-o"+o2);
                                }else if (o == to && o ==5) {
                                    var o2 = o-1;
                                    $(this).attr("href",ref2+"-o"+o2);
                                }
                            })
                        </script>
                    </p>
                    <p id="sort_qh">
                        <a id="line"  href=''  class=''></a>
                        <!--<a id="piece" th:each="pi,i:${piece}" th:if="${i.index eq 1 }" th:href="${pi.url}"    class=''></a>-->
                    </p>
                    <script>
                        $(function () {
                            var p_or_l = location.pathname;
                            if (p_or_l.indexOf("h1") == -1){
                                $("#replie").show();
                                $("#reprow").hide();
                                $("#piece").attr("class","");
                                $("#line").attr("class","hover");
                            }else {
                                $("#replie").hide();
                                $("#reprow").show();
                                $("#piece").attr("class","hover");
                                $("#line").attr("class","");
                            }
                        })
                        $("#line").click(function () {
                            var url = location.pathname;
                            if(url.indexOf("-h1") != -1){
                                $("#line").attr("href",url.replace("-h1",""));
                            }
                            else {
                                $("#line").attr("href",url.replace("h1",""));
                            }
                        })
                    </script>
                </div>
                <div class="tese" >
                    <p>特色:</p>
                    <div>
                        <a th:id="${'tese'+i.count}" th:each="t,i:${trait}" th:if="${t.name ne '全部' and t.name ne '三包'}" href="" th:text="${t.name}"th:class="${t.selected}? 'hover':''"></a>
                        <script >
                            $(".tese a").click(function () {
                                if ($(this).attr("class") == null) {
                                    var teseid = $(this).attr("id").replace("tese", "");
                                    var tid = teseid-1;
                                    var ref = location.pathname;
                                    if(ref.indexOf('dealRents/') == -1) ref +='/';
                                    ref = ref.replace(/[-]*[n][0-9]?$/, "");
                                    $(this).attr("href", ref + "-t" + tid);
                                }
                                else {
                                    var ref = location.pathname;
                                    if(ref.indexOf('dealRents/') == -1) ref +='/';
                                    var outid = $(this).attr("id").replace("tese", "");
                                    var outId = outid-1;
                                    var outref = ref.replace("-t"+outId,"");
                                    $(this).attr("href", outref);
                                }
                            })
                        </script>
                    </div>
                </div>
                <div class="cl"></div>
                <!--<div class="content">小二为你找到<i th:text="${msg}"></i>个符合条件的房源</div>-->

                <!--没有房源-->
                <div class="warning" th:if="${#lists.isEmpty(rents)}">
                    <p>
                        很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。
                        <br> 请重试<!--<a th:href="@{/helpSearch}" target="_blank">点击免费体验购房服务方案>></a>-->
                    </p>
                </div>
                <!--租房列表行级-->
                <div id="replie" class="house_left">
                    <ul class="bargainHouseList" th:each="sale:${rents}">
                        <li>
                            <div class="houseImg">
                                <a th:if="${sale.spanTime &lt; 31 and #session?.getAttribute('sessionId') == null}" target="_blank"  data-toggle="modal" href="#loginAgent">
                                    <img th:src="${#strings.isEmpty(sale.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sale.pic}" th:alt="${sale.titile}">
                                </a>
                                <a th:if="${sale.spanTime &gt; 31 or #session?.getAttribute('sessionId') != null}" th:href="${'/dealRent/'+sale.houseId+'.htm'}" target="_blank">
                                    <img th:src="${#strings.isEmpty(sale.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sale.pic}" th:alt="${sale.titile}">
                                </a>
                            </div>
                            <div class="houseInfo">
                                <div class="houseTitle">
                                    <a th:if="${sale.spanTime &lt; 31 and #session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#loginAgent">
                                        <th:block th:text="${sale.titile}"></th:block>
                                    </a>
                                    <a th:if="${sale.spanTime &gt; 31 or #session?.getAttribute('sessionId') != null}" th:href="${'/dealRent/'+sale.houseId+'.htm'}" target="_blank">
                                        <th:block th:text="${sale.titile}"></th:block>
                                    </a>
                                </div>
                                <div class="houseInfoLeft">
                                    <p><th:block th:text="${#strings.isEmpty(sale.forward)?'':sale.forward}"></th:block><span th:if="${!#strings.isEmpty(sale.forward)}">|</span><th:block th:text="${#strings.isEmpty(sale.fitmentTypeName)?'':sale.fitmentTypeName}"></th:block></p>
                                    <p><th:block th:if="${!#strings.isEmpty(sale.floorDesc) and !#strings.isEmpty(sale.totalFloorNumber)}" th:text="${sale.floorDesc+'/'+sale.totalFloorNumber}"></th:block><span th:if="${!#strings.isEmpty(sale.floorDesc) and !#strings.isEmpty(sale.totalFloorNumber)}">|</span>付款方式：<th:block th:text="${#strings.isEmpty(sale.paymentName)?'暂无资料':sale.paymentName}"></th:block></p>
                                    <p><th:block th:text="${#strings.isEmpty(sale.rentPrice)?'':'租赁标价'+sale.rentPrice+'元/月'}"></th:block><span th:if="${!#strings.isEmpty(sale.rentPrice)}">|</span><th:block th:text="${#strings.isEmpty(sale.cycle)?'':'成交周期'+sale.cycle+'天'}"></th:block></p>
                                    <p class="agentName">经纪人： <th:block th:text="${#strings.isEmpty(sale.agency)?'暂无资料':sale.agency}"></th:block></p>
                                </div>
                                <div class="houseInfoMiddle">
                                    <div th:if="${sale.spanTime &lt; 31}"><p>近<span>30</span>天内成交</p></div>
                                    <div th:unless="${sale.spanTime &lt; 31}"><p><span><th:block th:text="${#strings.isEmpty(sale.dealTime)?'暂无资料':sale.dealTime}"></th:block></span></p></div>
                                    <div><th:block th:text="${#strings.isEmpty(sale.intermediaryName)?'暂无资料':sale.intermediaryName+'成交'}"></th:block></div>
                                </div>
                                <div class="houseInfoRight" th:if="${sale.spanTime &lt; 31}">
                                    <div><span><th:block th:text="${sale.dealPrice}"></th:block></span>元/月</div>
                                    <div>
                                        <a href="https://download.fangxiaoer.com/" target="_blank">下载APP查看成交></a>
                                        <div class="recode">
                                            <img src="https://static.fangxiaoer.com/web/images/sy/house/bargainHouseRecode.png"/>
                                        </div>
                                    </div>
                                    <a class="checkHouse" th:if="${#session?.getAttribute('sessionId') == null}"  target="_blank" style="cursor: pointer;margin-top: 0" data-toggle="modal" id="CommentListAdd" href="#loginAgent">查看成交价格&gt;</a>
                                    <a class="checkHouse" style="margin-top: 0" th:if="${#session?.getAttribute('sessionId') != null}"  th:href="${'/dealRent/'+sale.houseId+'.htm'}" target="_blank">查看成交价格&gt;</a>
                                    <a class="checkHouse" style="margin-top: 0" target="_blank" th:href="'../rents/-v'+${sale.SubID}">查看同小区房源 &gt;</a>
                                </div>
                                <div class="houseInfoRight" th:unless="${sale.spanTime &lt; 31}">
                                    <div><span><th:block th:text="${#strings.indexOf(sale.dealPrice,'.') eq -1 ? sale.dealPrice:#strings.toString(sale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$','')}"></th:block></span>元/月</div>
                                    <a class="checkHouse" target="_blank" th:href="'/rents/-v'+${sale.SubID}">查看同小区房源 &gt;</a>
                                </div>
                            </div>
                            <div class="cl"></div>
                        </li>
                    </ul>
                </div>
                <script>
                    $(function () {
                        for (var i=0 ; i<30 ; i++){
                            var isRoom = $("#isroom"+i).val();
                            if (isRoom == ""){
                                $("#room"+i).show();
                                $("#bedroom"+i).hide();
                            }else {
                                $("#bedroom"+i).show();
                                $("#room"+i).hide();
                            }
                        }

                    })
                </script>

                <div class="cl"></div>
                <div class="page">
                    <div th:include="fragment/page :: page"></div>
                </div>
            </div>
            <div id="right">
                <div class="saleHouse">
                    <a href="/rentwhole" target="_blank">我要租房</a>
                </div>
                <div class="zsfw" th:include="fragment/fragment::help_search_rent"></div>
                <div>
                    <a href="https://event.fangxiaoer.com/20170916.htm" target="_blank"><img width="100%" src="https://static.fangxiaoer.com/web/images/sy/sale/ad17.jpg" alt=""></a>
                </div>

                <!--租房推荐-->
                <dl class="recommend" th:if="${!#lists.isEmpty(advert.command)}" >
                    <dt><span></span>租房推荐</dt>
                    <dd th:each="command,i:${advert.command}"><div>
                        <a th:href="${command.url}" target="_blank">
                            <p th:class="${i.index &lt; 3?'hover':''}"><th:block th:text="${'0'+(i.index+1)}"></th:block></p>
                            <span><th:block th:text="${command.projectName}"></th:block></span>
                            <b style="width: 75px;"><th:block th:utext="${command.price}"></th:block></b>
                        </a>
                    </div></dd>
                </dl>
                <!--画中画广告-->
                <div th:if="${!#lists.isEmpty(advert.focus)}">
                    <a th:each="focus,i:${advert.focus}" th:href="${focus.url}" target="_blank">
                        <img width="100%" th:src="${focus.image}" th:alt="${focus.projectName}">
                    </a>
                </div>
            </div>
        </div>
        <div class="cl"></div>
        <div class="gxq hid">
            <h3>您可能感兴趣的房源</h3>
            <ul>
                <li>
                    <a href="" target="_blank"><img src="https://images.fangxiaoer.com/sy/esf/fy/middle/2017/07/03/59599f4d2858e.jpg" alt=""></a>
                    <a href="" target="_blank">投资首选万科明天广场 1室 1厅 1卫 46㎡精装修拎包即住</a>
                    <p><span>820<i>万</i></span>4室3厅</p>
                </li>
            </ul>
        </div>
        <div class="gxqxq" th:if="${interest ne null and #lists.toList(interest).size() ne 0}">
            <h3><span></span>各区域您可能感兴趣的小区</h3>
            <ul>

                <li th:each="interest:${interest}">
                    <a th:href="${'/saleVillages/'+interest.SubID+'.htm'}" target="_blank">
                        <img th:src="${interest.pic eq null ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : interest.pic}" alt=""></a>
                    <p th:text="${interest.regionName}"></p>
                    <p th:text="${interest.Name}"></p>
                    <div class="cl"></div>
                    <p th:if="${interest.salecount ne 0 and interest.salecount ne null}">二手房<i th:text="${interest.salecount}"></i>套</p>
                    <p th:if="${interest.rentcount ne 0 and interest.rentcount ne null}">租房<i th:text="${interest.rentcount}"></i>套</p>
                </li>

            </ul>
        </div>


        <script>
            var i = 0;
            for (i = 0; i < $("#option_other .select_info").length; i++) {
                if ($("#option_other .select_box").eq(i).find(".hover").length > 0) {
                    $("#option_other .select_info").eq(i).text($("#option_other .select_box").eq(i).find(".hover").text())
                }
            }
        </script>

        <div class="cl"></div>
        <!--底部1-->
        <div th:include="fragment/fragment:: footer_list"></div>
        <div th:include="fragment/fragment::tongji"></div>
        <!-- End Alexa Certify Javascript -->
    </div>
</form>
<div th:include="house/detail/fragment_contactAgent::loginAgent"></div>
<div th:include="fragment/fragment::commonFloat"></div>


</body>
</html>