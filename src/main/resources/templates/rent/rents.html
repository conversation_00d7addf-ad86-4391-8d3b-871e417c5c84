<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'沈阳' + seoTitle + seoSubName + '租房_沈阳' + seoTitle + seoSubName + '租房出售_沈阳房屋出租 - 房小二网'}"></title>
    <meta name="keywords" th:content="${'沈阳' + seoTitle + seoSubName + '租房,沈阳' + seoTitle + seoSubName + '中介,沈阳租房'}" />
    <meta name="description" th:content="${'房小二网沈阳租房频道为您提供海量真实的沈阳' + seoTitle + seoSubName + '租房信息，沈阳' + seoTitle + seoSubName + '房源信息，100%真实审核。租好房,上房小二网。100%真实审核。租好房,上房小二网。'}" />
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang3/'+mobileAgent}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20190718" />
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2018.css?v=20200212" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/maifang_right/default2018.css?v=20200212" />
    <link rel="stylesheet"                 href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20190308">
    <!--<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/zufang/list.css" />-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/fxe_confirm.js" ></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/AjaxforJquery.js" ></script>
    <!--
        <script type="text/javascript" src="https://static.fangxiaoer.com/js/esf_fxe_bnzf2017.js" ></script>
    -->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        $(function () {
            var r_or_s = location.pathname;
            if (r_or_s.indexOf('k1') != -1) {
                $("#btn2").attr("class","hover");
                $("#Search_ditie").css('display','block');
            }
            else {
                $("#btn1").attr("class","hover");
                $("#Search_zf").css('display','block');
            }
        })
    </script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css" />
    <style>
        /*.sort #sortParam a+a+a+a:hover, .sort #sortParam a+a+a+a {
            background: none !important;
        }*/
        .renlistBanner{display: block;margin-bottom: 20px;}
        .bannerLeft{float: left;overflow: hidden}
        .rentListTese .tese div a{    padding: 0 5px 0 20px !important;}
        .listIcon_subway{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_subway.png) no-repeat}
		/* 增加安心好房样式修改 */
		.sort_new {
			border-bottom: 2px solid #ff5200;
		}
		
		.new_label {
			padding: 0 30px;
			text-align: center;
			line-height: 45px;
			font-size: 16px;
			display: inline-block;
		}
		
		#sortParamNewest {
			margin: 0;
		}
		
		#sortParamNewest>a:hover {
			/* background: #333; */
			color: #333;
			text-decoration: none;
		}
		
		.new_hover {
			background: #ff5200;
			color: #fff;
		}
		.new_hover:hover{
			color: #fff !important;
		}
		.anxin_icon{
			display: flex;
			line-height: 21px;
			font-size: 13px;
			margin-left: 10px;
		}
		.anxin_icon_l{
			width:33px;
			height:21px;
			color: #fff;
			text-align: center;
			background:linear-gradient(90deg,rgba(255,144,0,1) 0%,rgba(255,82,0,1) 100%);
		}
		.anxin_icon_r{
			width: 33px;
			height: 19px;
			line-height: 19px;
			color: #ff5200;
			text-align: center;
			border: 1px solid #ff5200;
		}
		/* 增加安心好房样式修改 */

        .sub{ position: absolute; top: 0px; right: 0px; z-index: 10;}/*增加广告图标*/
    </style>


<body>
<!--<form name="form1" method="post" action="" id="form1">-->
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=3,listType=0"></div>

    <div class="main">
        <!--网页位置-->
        <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/rents/" target="_blank">沈阳租房</a>
            <th:block th:each="r:${region}" th:if="${r.selected and r.name ne '全部'}">
                &gt;
                <a th:href="${'/rents/r' + r.id}"  th:text="${r.name + '租房'}"></a>
                <th:block th:if="${plate}">
                    <th:block th:each="j:${plate}"  th:if="${j.selected and j.name ne '全部'}">
                        &gt;
                        <a th:href="${'/rents/r' + r.id + '-j' + j.id}"  th:text="${j.name + '租房'}"></a>
                    </th:block>
                </th:block>
            </th:block>
        </div>
        <!--小区信息展示-->
        <div th:include="secondhouse/fragment::viewRentplotinformation"></div>
        <div id="option">
            <ul>
                <li class="fenlei">
                    <p>位置：</p>
                    <a id="btn1" th:each="r,i:${region}" th:if="${i.index eq 0 }" th:href="${r.url}"  class=""><b class="listIcon listIcon_dw"></b>区域<i></i></a>
                    <a id="btn2" th:each="k,i:${kinds}"  th:if="${i.index eq 1 }" th:href="${k.url}"  class=""><b class="listIcon listIcon_ditie"></b>地铁<i></i></a>
                    <a href="/villages/"><b class="listIcon listIcon_esf"></b>小区<i></i></a>
                    <a href="/static/rentmap.htm"><b class="listIcon listIcon_map"></b>地图<i></i></a>
                    <a th:href="${'/static/rentmap.htm'+'?subway'}"><b class="listIcon listIcon_subway"></b>地铁沿线<i></i></a>
                </li>
                <!--区域-->
                <li id="Search_zf" class="leibie" style="display: none">
                    <a th:each="r:${region}" th:href="${r.url}" th:text="${#strings.toString(r.name).replaceAll('全部','不限')}" th:id="'r'+${r.id}" th:class="${r.selected}? 'hover':''"></a><br>
                    <span th:if="${plate}">
                        <a th:each="j:${plate}" th:text="${#strings.toString(j.name).replaceAll('全部','不限')}"  onclick="showIndex(this)" th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
                    </span>
                </li>
                <!-- 地铁-->
                <li id="Search_ditie" class="leibie" style="display: none">
                    <a th:each="s:${subway}" th:href="${s.url}" th:text="${#strings.toString(s.name).replaceAll('全部','不限')}" th:id="'r'+${s.id}" th:class="${s.selected}? 'hover':''">></a>
                </li>

                <li>
                    <p>租金：</p>
                    <a th:each="p:${price}" th:href="${p.url}" th:text="${#strings.toString(p.name).replaceAll('全部','不限')}" th:id="'p'+${p.id}" th:class="${p.selected}? 'hover':''">></a>
                </li>
               <!-- <script>
                $(function () {
                    //户型和厅室的处理
                    var url_b = location.pathname;
                    var num_b = url_b.indexOf("w2");
                    if (num_b == -1) {
                        $("#Search_room").show();
                        $("#Search_Bedroom").hide();
                    }else {
                        $("#Search_room").hide();
                        $("#Search_Bedroom").show();
                    }

                })
                </script>-->
                <li id="Search_RentType">
                    <p>方式：</p>
                    <a th:each="w:${way}" th:href="${w.url}"  th:id="'w'+${w.id}" th:class="${w.selected}? 'hover':''">
                        <th:block th:text="${#strings.toString(w.name).replaceAll('全部','不限')}"></th:block>
                    </a>
                </li>
                <li id="Search_room">
                    <p>户型：</p>
                    <a th:each="l:${layout}" th:href="${l.url}" th:text="${#strings.toString(l.name).replaceAll('全部','不限')}" th:id="'l'+${l.id}" th:class="${l.selected}? 'hover':''">></a>
                </li>
               <!-- <li id="Search_Bedroom">
                    <p>厅室：</p>
                    <a th:each="b:${bedroom}" th:href="${b.url}" th:text="${#strings.toString(b.name).replaceAll('全部','不限')}" th:id="'b'+${b.id}" th:class="${b.selected}? 'hover':''">></a>
                </li>-->
                <li class="rentListTese">
                    <div class="tese" style="border: none">
                        <p>特色：</p>
                        <div>
                            <a th:id="${'tese'+i.count}" th:each="t,i:${trait}" th:if="${t.name ne '全部' and t.name ne '三包'}" href="" th:text="${t.name}"th:class="${t.selected}? 'hover':''"></a>
                            <script >
                                $(".tese a").click(function () {
                                    if ($(this).attr("class") == null) {
                                        var teseid = $(this).attr("id").replace("tese", "");
                                        var tid = teseid-1;
                                        var ref = location.pathname;
                                        if(ref.indexOf('rents/') == -1) ref +='/';
                                        ref = ref.replace(/[-]*[n][0-9]?$/, "");
                                        $(this).attr("href", ref + "-t" + tid);
                                    }
                                    else {
                                        var ref = location.pathname;
                                        if(ref.indexOf('rents/') == -1) ref +='/';
                                        var outid = $(this).attr("id").replace("tese", "");
                                        var outId = outid-1;
                                        var outref = ref.replace("-t"+outId,"");
                                        $(this).attr("href", outref);
                                    }
                                })
                            </script>
                        </div>
                    </div>
                </li>

            </ul>
        </div>

        <div id="option_other">
            <ul>
                <li><p>更多：</p>
                    <div class="select_box">
                        <div class="select_info">装修不限</div>
                        <ul>
                            <li th:each="d:${decorate}">  <a th:href="${d.url}" th:text="${d.id eq '' ? '装修不限':d.name}" th:class="${d.selected}? 'hover':''">></a></li>
                        </ul>
                    </div>
                    <div class="select_box">
                        <div class="select_info">来源不限</div>
                        <ul>
                            <li th:each="g:${grigon}">
                                <a th:href="${g.url}" th:text="${g.id eq '' ? '来源不限':g.name}" th:class="${g.selected}? 'hover':''">
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
        <div id="option_info" style="display: none">
            <input type="hidden" id="houseName" th:value="${sub?.title}" />
            <b >已选：</b>
            <!--小区名-->
            <div th:if="${!#strings.isEmpty(subId) and !#strings.isEmpty(sub?.title)}">
                <span class="condition"><th:block th:text="${sub.title}"></th:block></span>
                <i class="cleanUrl" th:value="${'-v'+subId}"></i>
            </div>
            <!--头部搜索-->
            <div th:if="${!#strings.isEmpty(searchKey) and !#strings.isEmpty(searchKey)}">
                <span class="condition"><th:block th:text="${searchKey}"></th:block></span>
                <i class="cleanUrl" id="clearSearchKey" th:value="${searchKey}"></i>
            </div>
            <script>

                var houseName = $("#houseName").val();
                if(houseName != "" || houseName != undefined || houseName != null){
                    if(!$("#txtkeys").val()){
                        $("#txtkeys").val(houseName);
                    }
                }
            </script>
            <!--区域-->
            <div th:each="r:${region}" th:if="${r.selected and r.name ne '全部'}">
                <span class="condition"><th:block th:text="${r.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'r'+r.id}"></i>
            </div>
            <!--板块-->
            <div th:each="p:${plate}" th:if="${p.selected and  !#strings.isEmpty(p.id)}" th:onclick="${'removeSpan('+1+')'}">
                <span class="condition"><th:block th:text="${p.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
            <!--地铁-->
            <div th:each="s:${subway}" th:if="${s.selected and s.name ne '全部'}">
                <span class="condition"><th:block th:text="${s.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'s'+s.id}"></i>
            </div>
            <!--价格-->
            <div th:each="p:${price}" th:if="${p.selected and p.name ne '全部'}">
                <span class="condition"><th:block th:text="${p.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'p'+p.id}"></i>
            </div>
            <!--方式-->
            <div th:each="w:${way}" th:if="${w.selected and w.name ne '全部'}">
                <span class="condition"><th:block th:text="${w.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'w'+w.id}"></i>
            </div>
            <!--户型-->
            <div th:each="l:${layout}" th:if="${l.selected and l.name ne '全部'}">
                <span class="condition"><th:block th:text="${l.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'l'+l.id}"></i>
            </div>
            <!--主次卧-->
            <div th:each="b:${bedroom}" th:if="${b.selected and b.name ne '全部'}">
                <span class="condition"><th:block th:text="${b.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'b'+b.id}"></i>
            </div>
            <!--装修-->
            <div th:each="d:${decorate}" th:if="${d.selected and d.name ne '全部'}">
                <span class="condition"><th:block th:text="${d.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'d'+d.id}"></i>
            </div>
            <!--来源-->
            <div th:each="g:${grigon}" th:if="${g.selected and g.name ne '全部'}">
                <span class="condition"><th:block th:text="${g.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'g'+g.id}"></i>
            </div>
            <script>
                /*判断是否显示条件栏目*/
                $(function () {
                    if($(".condition").text() != ""){
                        $("#option_info").css("display","block");
                    }
                })
                /*去掉条件标签*/
                $(".cleanUrl").click(function () {
                    var oldUrl = location.pathname;
                    if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
                        var moreUrl ="-" + $(this).attr("value");
                    }else{ var moreUrl = $(this).attr("value");}

                    var newUrl = oldUrl.replace(moreUrl,"");
                    window.location.href = newUrl;
                    //跳到newUrl指定链接
                });
                //清除头部搜索key
                $("#clearSearchKey").click(function () {
                    var nowUrl = location.pathname;
                    var searchTitle = nowUrl.indexOf('search');//只有手输内容搜索
                    var newUrl = nowUrl.substring(0,searchTitle);
                    window.location.href = newUrl;
                });
            </script>
            <a href="/rents/" class="clean">清空筛选条件</a>
        </div>
        <div class="cl"></div>
        <div id="main">
            <div class="bannerLeft">
                <!--租房地图广告位-->
<!--                <a class="renlistBanner" target="_blank" href="/static/rentmap.htm" ><img src="https://static.fangxiaoer.com/web/images/sy/map/renlistBanner.jpg" alt=""></a>-->
                <!--列表页新增广告-->
<!--                <div th:include="secondhouse/fragment::advert_recommand"></div>-->
                <div id="left" class="longLeft">
                    <div class="sort sort_new" >
                        <!-- 建立一个新的查询条件区域 -->
                        <p id="sortParamNewest">
                            <a id="2" href="" class="new_label ">全部</a>
							<a th:if="${#strings.equals(forAnxuan, '1')}" id="8" href="" class="new_label ">安心好房</a>
                            <!--<a id="4" href="" >视频看房</a>-->
                            <script>
                                $(document).ready(function(){
                                    var ref = location.pathname;
                                    if(ref.indexOf('rents/') == -1) ref +='/';
                                    var y = ref.split('/rents/');
                                    var x = y.length > 1 ? y[1] : "";
                                    if(x.indexOf('W') != -1 || x.indexOf('S') != -1 ){
                                        if(x.indexOf('S') != -1 ){
                                            $("#sortParamNewest a[id='8']").addClass("new_hover");
                                        }else{
                                            var num = x.split('W')[1].substring(0,1);
                                            if(num == 2 || num == 4){
                                                $("#sortParamNewest a[id ='"+ num+"']").addClass("new_hover");
                                            }else{
                                                if(num%2 == 1){
                                                    $("#sortParamNewest a[id ='"+ num+"']").addClass("new_hover");
                                                    $("#sortParamNewest a[id ='"+ num+"']").attr("id",num-1);
                                                }else{
                                                    num = parseInt(num) +1;
                                                    $("#sortParamNewest a[id ='"+ num+"']").addClass("new_hover");
                                                }
                                            }
                                        }
                                    }else{
                                        $("#sortParamNewest a:eq(0)").addClass("new_hover");
                                    }
                                    var ref2 = ref.replace(/-W[0-5]/,'').replace(/-S8/,'');
                                    $("#sortParamNewest a").each(function () {
                                        var ids = $(this).attr("id");
                                        var ref3 = ref2 + '-W' + ids;
                                        if(ids == 8){
                                            ref3 = ref2 + '-S8';
                                        }
                                        $(this).attr("href",ref3);
                                    });
                                });
                            </script>
                        </p>

                        <p id="screening_Items" style="margin-top: 12px;">
                            <a id="0" href="/rents/o0">综合排序</a>
                            <a id="3" href='/rents/o3' >租金</a>
                            <a id="5" href='/rents/o5' >面积</a>
                            <a id="1" href='/rents/o1' style="background: none">最新</a>
                            <script>
                                $(document).ready(function(){
                                    var ref = location.pathname;
                                    if(ref.indexOf('rents/') == -1) ref +='/';
                                    var ref2 = ref.replace(/-o[0-9]/,'');
                                    var num = ref.indexOf("o");
                                    var o = ref.substring(num+1,num+2);
                                    if(num == -1){
                                        $("#0").attr("class","hover");
                                    }
                                    else{
                                        if (o == 0){
                                            $("#0").attr("class","hover");
                                        }
                                        if (o == 1){
                                            $("#1").attr("class","hover");
                                        }
                                        if (o == 2 ){
                                            $("#3").attr("class","sort_jg up");
                                        }
                                        if (o == 3){
                                            $("#3").attr("class","sort_jg down");
                                        }
                                        if (o == 4){
                                            $("#5").attr("class","sort_jg up");
                                        }
                                        if (o == 5){
                                            $("#5").attr("class","sort_jg down");
                                        }
                                    }
                                    $("#0").attr("href",ref2+"-o0");
                                    $("#3").attr("href",ref2+"-o3");
                                    $("#5").attr("href",ref2+"-o5");
                                    $("#1").attr("href",ref2+"-o1");
                                });
                            </script>
                            <script >
                                $("#screening_Items a").click(function () {
                                    var ref = location.pathname;
                                    if(ref.indexOf('rents/') == -1) ref +='/';
                                    var ref2 = ref.replace(/-o[0-9]/,'');
                                    var num = ref.indexOf("o");
                                    var o = ref.substring(num+1,num+2);
                                    var to = $(this).attr("id");
                                    if (o == to && o ==3){
                                        var o2 = o-1;
                                        $(this).attr("href",ref2+"-o"+o2);
                                    }else if (o == to && o ==5) {
                                        var o2 = o-1;
                                        $(this).attr("href",ref2+"-o"+o2);
                                    }
                                })
                            </script>
                        </p>



                        <!--<p class="dealbiao"><a href="/dealRents/">成交房源></a></p>-->
                        <!--                    <p class="wantbuy"><a href="/needRents/"><i class="wantbuyIcon"></i>求租信息</a></p>-->
                        <script>
                            $(function () {
                                var p_or_l = location.pathname;
                                if (p_or_l.indexOf("h1") == -1){
                                    $("#replie").show();
                                    $("#reprow").hide();
                                    $("#piece").attr("class","");
                                    $("#line").attr("class","hover");
                                }else {
                                    $("#replie").hide();
                                    $("#reprow").show();
                                    $("#piece").attr("class","hover");
                                    $("#line").attr("class","");
                                }
                            })
                            //                        $("#line").click(function () {
                            function lineShow(){
                                var url = location.pathname;
                                if(url.indexOf("-h1") != -1){
                                    $("#line").attr("href",url.replace("-h1",""));
                                }
                                else {
                                    $("#line").attr("href",url.replace("h1",""));
                                }
                            }
                            //                        })
                        </script>
                    </div>
                    <!--没有房源-->
                    <div class="warning" th:if="${#lists.isEmpty(rents)}">
                        <p>
                            很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                            懒得搜索？！<a th:href="@{'/helpSearch?ids=3'}" rel="3" dir="2" target="_blank">点击免费发布求租服务方案>></a>
                        </p>
                    </div>
                    <!--租房列表行级-->
                    <div id="replie" class="house_left" th:include="fragment/rents::rents_line"></div>
                    <!--租房列表块级-->
                  <!--  <div id="reprow" class="list rent" th:include="fragment/rents::rents_piece"></div>-->
                    <script type="text/javascript">
                        $(".esfListClose").click(function () {
                            $("#esfListEwmBanner").hide()
                        })
                    </script>
                    <div class="cl"></div>
                    <div class="page">
                        <div th:include="fragment/page :: page"></div>
                    </div>
                </div>
            </div>

            <div id="right" class="shortRight">
                <!--小区专家-->
                <div th:include="secondhouse/fragment::plotexpert"></div>

                <!--房产快搜-->
                <!--<div class="gksou">-->
                    <!--<div class="gktp"><span></span>房产快搜 <a href="/fastSeek" target="_blank">详情>></a></div>-->
                    <!--<div class="gkmn">-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94587.htm" target="_blank"><i>1</i><em>房产过户</em></a>-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94586.htm" target="_blank"><i>2</i><em>购房流程</em></a>-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94572.htm" target="_blank"><i>3</i><em>征信查询</em></a>-->
                        <!--&lt;!&ndash;<a href="/fastSeek#edu" target="_blank"><i>4</i><em>沈阳推荐小学</em></a>-->
                        <!--<a href="/fastSeek#edu" target="_blank"><i>5</i><em>沈阳推荐中学</em></a>&ndash;&gt;-->
                    <!--</div>-->
                <!--</div>-->
                <!--&lt;!&ndash;列表页右侧我要出租&ndash;&gt;-->
<!--                <div th:include="rent/fragment::wantrent"></div>-->
                <div class="rightQzQgBtn">
                    <a href="/rentwhole" target="_blank" style="margin-bottom: 14px"><i class="rightQzQgBtn-icon3"></i>我要出租</a>
<!--                    <a th:href="@{'/helpSearch?ids=3'}" rel="3" dir="2" target="_blank"><i class="rightQzQgBtn-icon2"></i>求租</a>-->
                </div>
                <!--地图指示-->
                <a th:href="${'/static/rentmap.htm'+'?subway'}" target="_blank"><div class="boomMap"><img src="https://static.fangxiaoer.com/web/images/sy/sale/boomMapIcon.png" alt=""></div></a>
                <!--求租-->
<!--                <div class="zsfw" th:include="fragment/fragment::help_search_rent"></div>-->
                <!--租房推荐-->
<!--                <div th:include="rent/fragment::rentrecommend"></div>-->
                <!--画中画广告-->
                <div th:include="rent/fragment::rentadvert"></div>
            </div>
            <!-- 展示推荐内容 -->
            <div id="recommond" class="house_left" th:include="fragment/rents::recommond_rents"></div>

        </div>
        <div class="cl"></div>
        <div class="gxq hid">
            <h3>您可能感兴趣的房源</h3>
            <ul>
                <li>
                    <a href="" target="_blank"><img src="https://images.fangxiaoer.com/sy/esf/fy/middle/2017/07/03/59599f4d2858e.jpg" alt=""></a>
                    <a href="" target="_blank">投资首选万科明天广场 1室 1厅 1卫 46㎡精装修拎包即住</a>
                    <p><span>820<i>万</i></span>4室3厅</p>
                </li>
            </ul>
        </div>
        <div class="gxqxq" th:if="${interest ne null and #lists.toList(interest).size() ne 0}">
            <h3><span></span>各区域您可能感兴趣的小区</h3>
            <ul>

                <li th:each="interest:${interest}">
                    <a th:href="${'/saleVillages/'+interest.SubID+'/index.htm'}" target="_blank">
                        <img th:src="${interest.pic eq null ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : interest.pic}" alt=""></a>
                    <p th:text="${interest.regionName}"></p>
                    <p th:text="${interest.Name}"></p>
                    <div class="cl"></div>
                    <p th:if="${!#strings.isEmpty(interest.salecount) and interest.salecount ne '0'}">二手房<i th:text="${interest.salecount}"></i>套</p>
                    <p th:if="${!#strings.isEmpty(interest.rentcount) and interest.rentcount ne '0'}">租房<i th:text="${interest.rentcount}"></i>套</p>
                </li>

            </ul>
        </div>


        <script>
            var i = 0;
            for (i = 0; i < $("#option_other .select_info").length; i++) {
                if ($("#option_other .select_box").eq(i).find(".hover").length > 0) {
                    $("#option_other .select_info").eq(i).text($("#option_other .select_box").eq(i).find(".hover").text())
                }
            }
        </script>

        <div class="cl"></div>

        <div th:include="fragment/fragment::tongji"></div>
        <!-- End Alexa Certify Javascript -->
    </div>
<!--</form>-->
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
<!--底部1-->
<div th:include="fragment/fragment:: footer_list"></div>

</body>
</html>