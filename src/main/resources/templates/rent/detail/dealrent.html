<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${info.title+'_'+info.subName+'房屋租赁_'+info.subName+'出租 - 房小二网'}"></title>
    <meta name="keywords" th:content="${info.subName+'租房,'+info.subName+'房屋租赁,'+info.regionName+'租房,沈阳租房,'+info.subName+'出租'}" />
    <meta name="description" th:content="'房小二网沈阳租房为您提供'+${info.title}+'的租房信息,以及其他'+${info.subName}+'租房信息,'+${info.subName}+'现房,以及最全面、最真实的沈阳房屋租赁信息。'" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang3/'+houseId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/view2017.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/global/js/tab.js"></script>
    <script src="https://static.fangxiaoer.com/global/js/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/md5.js"></script>
    <script src="/js/indent.js"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/saleHouseIndent.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>

    <script>
        //图片自适应大小
        function imgSize5() {
            //滚动大图
            var imgWidthHeght2 = $(".imgMax1 ul li").eq(0).width() / $(".imgMax1 ul li").eq(0).height()
            $(".imgMin ul li img").each(function () {
                if (parseInt($(this).width()) <= parseInt($(this).height())) {
                    $(this).css({"height": "100%", "width": "auto"})
                    $(this).css({
                        "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                        "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                    })
                } else {
                    $(this).css({"width": "100%", "height": "auto"})
                    $(this).css({
                        "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                        "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                    })
                }
            })
        }

        $(function () {
            //更多相册
            var len = $(".photo li").length;
            if (len > 8) {
                $(".photo li:gt(5)").hide();
                $(".photo ul").after("<span>查看全部照片(" + len + "张)</span>")
            }
            $(".photo span").live("click", function () {
                $(this).hide();
                $(".photo li").show();
            })
        })
    </script>
</head>
<body>
<style>
    .noyongjin dt {
        margin-top: 8px !important;
    }
    .license{position: absolute;width: 260px;height: 370px;border: 1px solid #ccc;display: none;
        border-radius: 5px;z-index: 99;background-color: #FFF;bottom: 40px;right: 30px;padding: 5px;}
    .license_img{
        width: 100% !important;
        height: 100% !important;
        margin: 0px !important;
        border-radius: 0px !important;
    }
    .map_lpcon{margin-top: 0}
    .map_lp{padding: 0}
    .map_lpcon{height: 280px !important;}
</style>

<input type="hidden" id="Idhouse" th:value="${houseId}"/>
<input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}"/>
<form name="form1" method="post" action="14238" id="form1">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=3"></div>

    <input type="hidden" id="houseType" value="2" />
    <!--面包屑-->
    <div class="w crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/rents/" target="_blank">沈阳租房</a> &gt; <a href="/dealRents/" target="_blank">成交房源</a> &gt; <a th:href="${'/dealRents/r'+info.regionId}" target="_blank" th:text="${info.regionName}"></a> &gt; <a th:href="${'/saleVillages/'+info.subId+'/index.htm'}" target='_blank' th:text="${info.subName}"></a></div>

    <!--基本信息-->
    <div class="w main">
        <div  class="header_sale">
            <h1  class="title1" th:text="${info.title}"></h1>
            <ul>
                <li th:if="${!#strings.isEmpty(dealSale.dealTime)}"  th:text="${'成交时间：'+#strings.toString(dealSale.dealTime).replace('.','-')}"></li>
            </ul>
            <!--分享标签-->
            <div th:include="fragment/fragment:: shareIcon"></div>
            <div class="photoAlbum">
                <div class="imgMax">
                    <ul>
                        <li th:if="${#lists.isEmpty(info.pic)}">
                            <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg"  alt=""  onload="imgSize()"  />
                        </li>
                        <li th:if="${!#lists.isEmpty(info.pic)}" th:each="pic:${info.pic}">
                            <img th:src="${#strings.toString(pic.url).replace('middle','big')}"  alt=""  onload="imgSize()"  />
                        </li>
                    </ul>
                </div>
                <div class="imgMin">
                    <span class="hover"><s></s></span>
                    <div>
                        <ul>
                            <li th:if="${#lists.isEmpty(info.pic)}">
                                <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg"  alt=""  onload="imgSize()"  />
                            </li>
                            <li th:if="${!#lists.isEmpty(info.pic)}" th:each="pic:${info.pic}">
                                <img th:src="${#strings.toString(pic.url).replace('middle','big')}"  alt=""  onload="imgSize()"  />
                            </li>
                        </ul>
                    </div>
                    <b><s></s></b>
                </div>
            </div>
            <div class="mainContent">
                <div class="price"><span><b th:text="${dealSale.dealPrice eq '0' ? '面议':(#strings.toString(dealSale.dealPrice).contains('.') ?  #strings.toString(dealSale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : dealSale.dealPrice)}"></b><th:block th:if="${dealSale.dealPrice ne '0'}">元/月</th:block></span>
                </div>
                <div class="type">
                    <ul style="margin-left: 0">
                        <li style="width: 120px">
                            <b th:text="${#strings.toString(dealSale.RentPrice).contains('.') ?  #strings.toString(dealSale.RentPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):dealSale.RentPrice}"></b>
                            <p>租赁标价（元/月）</p>
                        </li>
                        <li style="width: 110px">
                            <b th:text="${dealSale.cycle}"></b>
                            <p>成交周期（天）</p>                    </li>
                        <li style="width: 110px">
                            <b th:text="${dealSale.totalVisitedNum}"></b>
                            <p>浏览（次）</p>                    </li>
                        <li style="width: 110px">
                            <b th:text="${dealSale.favorite}"></b>
                            <p>关注（次）</p>
                        </li>
                    </ul>
                </div>
                <div class="type ">
                    <ul>
                        <li>
                            <b th:text="${info.room+'室'+info.hall+'厅'+info.toilet+'卫'}"></b>
                            <p th:text="${info.floorDesc+'/'+info.totalFloorNumber+'层'}"></p>
                        </li>
                        <li>
                            <b th:text="${info.area+'m²'}"></b>
                            <p>建筑面积</p>
                        </li>
                        <li>
                            <b th:text="${info.forward}"></b>
                            <p th:text="${info.buildDate}"></p>
                        </li>
                    </ul>
                </div>

                <div th:if="${!#strings.isEmpty(info.memberType) and info.memberType eq '经纪人'}" class="card jjr" style="position: relative;overflow: inherit;">
                    <p>
                        <a th:href="${#strings.isEmpty(info.agencyId) ? '' :'/agent/dealRents/'+info.agencyId}" target="_blank">
                            <img th:src="${info.keeperPic2}"/><br>
                        </a>
                        <a th:href="${#strings.isEmpty(info.agencyId) ? '' :'/agent/dealRents/'+info.agencyId}" target="_blank">进入Ta的店铺</a>
                    </p>
                    <dl id="people" class="noyongjin">
                        <dt>
                            <a th:href="${#strings.isEmpty(info.agencyId) ? '' :'/agent/dealRents/'+info.agencyId}" target="_blank">
                                <b th:text="${info.keeperName2}"></b>
                            </a>
                            <span th:text="${info.intermediaryName}"></span>
                        </dt>
                        <dd th:text="${info.sortTel == null or #strings.toString(info.sortTel) eq '' ? info.keeperTel2:info.sortTel}"> </dd>
                        <ddd><th:block th:text="${#strings.isEmpty(dealSale.dealCount)?'':'成交'+dealSale.dealCount+'套&nbsp;&nbsp;'}"></th:block><th:block th:text="${#strings.isEmpty(dealSale.dealCycle)?'':'平均成交周期'+dealSale.dealCycle+'天'}"></th:block></ddd>
                        <div style="clear: both;"></div>
                        <span style="cursor: pointer;padding-top: 20px;" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                            公司执照编码：
                            <span style="color:#999;">
                                <th:block th:text="${info.agentBusinessNum}"></th:block>
                            </span>
                        </span>
                        <div class="license" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                            <img th:src="${info.agentBusinessCardForPc}" class="license_img">
                        </div>
                        <div style="clear: both;"></div>
                    </dl>
                    <div style="clear: both;"></div>
                    <script>
                        function showLicense(e) {
                            $(".license").show();
                        }
                        function hideLicense(e) {
                            $(".license").hide();
                        }
                        $(function () {
                            var isNo = [[${info.isXiQue}]];
                            if (isNo != 1){
                                $("#people").attr("class","noyongjin");
                            }
                        })
                    </script>
                </div>

                <!--            <div th:if="${!#strings.isEmpty(info.memberType) and info.memberType eq '个人'}" class="card gr">
                                <div><img th:src="${info.keeperPic2 == null ?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':info.keeperPic2}" /></div>
                                <dl class="noyongjin">
                                    <dt><b th:text="${info.keeperName2}"></b></dt>
                                    <dd th:text="${info.keeperTel2}"> </dd>
                                </dl>
                            </div>-->
                <div th:if="${!#strings.isEmpty(info.memberType) and info.memberType eq '个人'}"  class="card jjr">
                    <p>
                        <img th:src="${info.keeperPic2 == null ?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':info.keeperPic2}" /><br><a style="display:none" href="/agent/second/98907" target="_blank">进入Ta的店铺</a>
                    </p>
                    <dl class="noyongjin">
                        <dt><b th:text="${info.keeperName2}"></b></dt>
                        <dd th:text="${info.keeperTel2}"> </dd>
                    </dl>
                </div>

            </div>
        </div>
        <div class="w" style="overflow: hidden;">
            <div class="left">
                <div class="allocation"  th:style="${info.otherEstablish ne '' and info.otherEstablish ne null}?'display:block':'display:none'">
                    <div class="head" style="display:">房屋配置</div>
                    <ul>
                        <li th:each="other:${other}">
                            <b th:class="${other.value}"></b>
                            <p th:text="${other.key}"></p>
                        </li>
                    </ul>
                </div>
                <div class="details">
                    <div class="head">房源描述</div>
                    <ul>
                        <li>
                            <span>小区详情</span>
                            <div>
                                <ul>
                                    <li>
                                        <span>小区名称：</span>
                                        <p th:text="${info.subName}"></p>
                                    </li>
                                    <li>
                                        <span>物&ensp;业&ensp;费：</span>
                                        <p th:text="${#strings.toString(info.propertyFee).equals('0.0') ?'暂无资料':info.propertyFee+'元/㎡月'}"></p>
                                    </li>
                                    <li>
                                        <span>物业公司：</span>
                                        <p th:text="${info.propertyCom ne '' and info.propertyCom ne null}?${info.propertyCom}:'暂无资料'"></p>
                                    </li>
                                    <li>
                                        <span>租&emsp;&emsp;房：</span>
                                        <p th:text="${info.rentCount ne '' and info.rentCount ne null}?${info.rentCount+'套'}:'暂无资料'"> </p>
                                    </li>
                                    <li>
                                        <span>绿&ensp;化&ensp;率：</span>
                                        <p th:text="${info.landScaping ne '' and info.landScaping ne null}?${info.landScaping}:'暂无资料'"></p>
                                    </li>
                                    <li>
                                        <span>二&ensp;手&ensp;房：</span>
                                        <p th:text="${info.saleCount ne '' and info.saleCount ne null}?${info.saleCount+'套'}:'暂无资料'"></p>
                                    </li>
                                </ul>
                            </div>
                        </li>
                        <li>
                            <span>房源描述</span>
                            <div class="fyms" th:utext="${info.description}">
                            </div>
                        </li>
                        <li class="" th:if="${!#strings.isEmpty(info.traffic) || !#strings.isEmpty(info.education) || !#strings.isEmpty(info.market)
         || !#strings.isEmpty(info.hospital) || !#strings.isEmpty(info.bank) || !#strings.isEmpty(info.othersettings)}">
                            <span>小区配套</span>
                            <div  style="border:0" class="canteen">
                                <div th:if="${!#strings.isEmpty(info.traffic)}"><span>交通：</span><p th:text="${info.traffic}"></p></div><!-- traffic-->
                                <div th:if="${!#strings.isEmpty(info.education)}"><span>学校：</span><p th:text="${info.education}"></p></div><!-- education -->
                                <div th:if="${!#strings.isEmpty(info.market)}"><span>商场：</span><p th:text="${info.market}"></p></div><!-- market -->
                                <div th:if="${!#strings.isEmpty(info.hospital)}"><span>医院：</span><p th:text="${info.hospital}"></p></div><!-- hospital -->
                                <div th:if="${!#strings.isEmpty(info.bank)}"><span>银行：</span><p th:text="${info.bank}"></p></div><!-- bank -->
                                <div th:if="${!#strings.isEmpty(info.othersettings)}"><span>其他：</span><p th:text="${info.othersettings}"></p></div><!-- othersettings -->
                            </div>
                        </li>
                    </ul>

                </div>
                <div class="w photo">
                    <div th:if="${!#lists.isEmpty(info.pic)}" class="head" style="display:">房源相册</div>
                    <ul>
                        <li th:each="pic:${info.pic}">
                            <img th:src="${pic.url}"  alt="更多房源图片" />
                            <p style="display:" th:text="${info.subName+' '+info.area+'m²'}"></p>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="right">
                <div th:if="${!#lists.isEmpty(same)}" id="xiangsihouse" class="head recommend">
                    <h1 >同小区您可能感兴趣的房源</h1>
                    <ul>
                        <li class="" th:each="s:${same}">
                            <a th:href="'/rent/'+${s.houseId}+'.htm'" >
                                <div>
                                    <img th:src="${#strings.isEmpty(s.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.pic}" th:alt="${s.subName}" />
                                    <span th:block th:text="${#strings.toString(s.price).substring(0,#strings.toString(s.price).length()-2) eq '0' ? '面议':#strings.toString(s.price).substring(0,#strings.toString(s.price).length()-2)}"> </span><i>元/月</i>
                                    <p th:text="${s.room+'室'+s.hall+'厅'}"></p>
                                    <b th:text="${s.subName}"></b>
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>


        <div class="w">

<!--            <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
            <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
            <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
            <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>
            <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
            <script th:inline="javascript">
                $(document).ready(function () {

                    var id = [[${info.subId}]];
                    var lng = [[${info.longitude}]];
                    var lat = [[${info.latitude}]];
                    var title = [[${info.subName}]];
                    var address = [[${info.address}]];
                    var city = '沈阳';
                    var content = "";
                    $("#mapsubTitle").val(title);
                    bdMap.init("memap", { id: id, houselng: lng, houselat: lat, radius: 2000, suofa: 14, bdtitle: title, bdcontent: content, address: address, city: city });
                });
            </script>
            <div id="MapZhouBianPeiTao1_mapDom">
                <div class="nmaptitle">
                    <div class="nmaptitleleft">
                        <p>周边配套</p>
                    </div>
                </div>
                <div class="symap">
                    <div class="dt1">
                        <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                        </div>
                        <div class="mapsele" id="mapsele" >
                            <!--<div class="msclose" id="msclose"></div>-->
                            <div class="mapTab" id="mapTab">
                                <ul>
                                    <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                                    <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                                    <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                                    <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                                    <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                                    <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                                    <!--<li onclick="bdMap.searechMap('银行')"><a href="javascript:void(0)">银行</a></li>-->
                                </ul>
                            </div>
                            <div class="map_lp" style="display: block">
                                <div id="hs_wrap">
                                    <!--<div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>-->
                                    <div class="map_lpcon"  id="r-result">
                                        <div class="map_dl">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <script>
                        $(function () {
                            $(".mapTab ul li ").click(function () {
                                $(".mapTab ul li ").removeClass("hover")
                                $(this).addClass("hover")
                            });
                            $(".map_dl a").mouseover(function () {
                                $(this).css("background-color", "#e8f4ff");
                            });
                            $(".map_dl a").mouseout(function () {
                                $(this).css("background-color", "#fff");
                            });
                        });
                        function Full() {
                            if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                                $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                                $(".map_lpcon").height("288px")
                                $(".map_full").html("全屏")
                            } else {
                                $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                                $(".map_lpcon").height("100%")
                                $(".map_full").html("取消全屏")
                            }
                        }
                    </script>
                </div>

            </div>

        </div>


        <div class="saleHouseIndent">
            <div class="page1">
                <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <h1>在线支付享优惠</h1>
                <ul>
                    <li><span>购房人</span><input id="name" placeholder="请填写姓名" type="text" maxlength="20" /></li>
                    <li><span>手机号码</span><input type="text" id="phone" placeholder="请填写手机号码" maxlength="11" value="" /></li>
                    <li class="yzm" style="border-bottom: 1px solid #ccc;"><span>请输入</span><input type="password" id="code" autocomplete="new-password" placeholder="验证码/密码" maxlength="20" style="width: 129px;" /><b class="fxe_ReSendValidateCoad">获取验证码</b><b class="fxe_validateCode" style="display: none;"></b></li>
                    <!--
                                    <li class="mm" style="display: none; border-bottom: 1px solid #ccc;"><span>密码</span><input type="password" id="pwd" placeholder="请填写密码" maxlength="20" /></li>
                    -->
                    <input type="hidden" id="userType" value="">
                </ul>
                <a>佣金95折</a>
                <dl>
                    <dt></dt>
                    <dd>我已经看过并同意<a href="https://info.fangxiaoer.com/Help/remission" target="_blank">《佣金减免说明书》</a></dd>
                </dl>
                <!--<span class="x"></span>-->
            </div>
            <div class="page2">
                <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <h1 >在线支付</h1>
                <p  th:text="${info.title}" >国瑞城 1室 0厅 1卫 </p>
                <ul>
                    <li><span>在线支付20元，约折佣340元</span></li>
                    <li  th:text="${'地址：'+info.address}">地址： 大东区东北大马路398号</li>
                    <li th:text="${'经纪人：'+ (info.keeperName2 ne null and info.keeperName2 ne ''? info.keeperName2:'') + ' '+ (info.sortTel == null or #strings.toString(info.sortTel) eq '' ? info.keeperTel2:info.sortTel)}">经纪人： 常先生 13840383296</li>
                </ul>
                <dl>
                    <dt>支付方式： </dt>
                    <dd class="weixin hover">
                        <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/weixin.png" alt="微信" /><i></i></dd>
                    <dd class="alipay">
                        <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/alipay.png" alt="支付宝" /><i></i></dd>
                </dl>
                <span>支付金额：20元</span>
                <a>去支付</a>
                <!--<span class="x"></span>-->
            </div>
            <div class="page3">
                <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <h1>请使用<span>支付宝</span>扫描下面二维码</h1>
                <img src="" alt="二维码获取失败" />
                <!--<span class="x1"></span>-->
            </div>
            <div class="page4">
                <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/success.png" />
                <b>支付成功</b>
                <a href="https://my.fangxiaoer.com/payOrder">查看订单</a>
                <!--<span class="x1"></span>-->
            </div>

        </div>
        <div class="bigImgShow">
            <div class="showImg">
                <ul>
                    <li th:if="${#lists.isEmpty(info.pic)}">
                        <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg"  alt=""  onload="imgSize()"  />
                    </li>
                    <li th:if="${!#lists.isEmpty(info.pic)}" th:each="pic:${info.pic}">
                        <img th:src="${#strings.toString(pic.url).replace('middle','big')}" alt=""  onload="imgSize()"/>
                    </li>


                </ul>
            </div>
            <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"/></div>
            <div class="prev"></div>
            <div class="next"></div>
        </div>

        <script type="text/javascript">
            pay.init()

        </script>
        <script src="https://static.fangxiaoer.com/js/huanxinjs/js/rentsaleim.js"></script>


        <script>
            var islogin
            $(document).ready(function () {
                islogin = $("#sessionId").val();
            });
            $("#sayWordsUL li").live("click", function () {
                turnoffwords_box();
                var val = $("#talkInputId").val() + $(this).html();
                $("#talkInputId").val(val);
            });

            function showWordsBox() {
                $(".wl_faces_box").hide();
                $(".wl_words_box").fadeIn("slow");
            }
            function turnoffwords_box() {
                $(".wl_faces_box").hide();
                $(".wl_words_box").fadeOut("slow");
            }
            photo.init(5);

            calculate();

        </script>
        <div class="disclaimer">
            <strong>免责声明：</strong>房源信息由网站用户提供，其真实性、合法性由信息提供者负责，最终以政府部门登记备案为准。本网站不声明或保证内容之正确性和可靠性，购买该房屋时，请谨慎核查。如该房源信息有误，您可以投诉此房源信息或<strong>拔打举报电话：400-893-9709</strong>。
        </div>

        <div class="cl"></div>
        <div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee"><div th:include="fragment/fragment:: footer_detail" ></div>
            <div th:include="fragment/fragment::tongji"></div>
       </div> </div>
</form>
<div th:include="fragment/fragment::commonFloat"></div>
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
<div th:include="house/detail/fragment_login::login"></div>
</body>
</html>

