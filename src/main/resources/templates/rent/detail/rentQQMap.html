
<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="x-ua-compatible" content="IE=edge" >
    <title th:text="${info.title+'_'+info.subName+'房屋租赁_'+info.subName+'出租 - 房小二网'}"></title>
    <meta name="keywords" th:content="${info.subName+'租房,'+info.subName+'房屋租赁,'+info.regionName+'租房,沈阳租房,'+info.subName+'出租'}" />
    <meta name="description" th:content="'房小二网沈阳租房为您提供'+${info.title}+'的租房信息,以及其他'+${info.subName}+'租房信息,'+${info.subName}+'现房,以及最全面、最真实的沈阳房屋租赁信息。'" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang3/'+houseId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?t=20220126" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleHoseView2018.css?t=20190604" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/view2017.css?t=20220126" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/global/js/tab.js"></script>
    <script src="/js/md5.js"></script>
    <script src="/js/indent.js"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/saleHouseIndent.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <link rel="stylesheet" href="//g.alicdn.com/de/prismplayer/2.6.0/skins/default/aliplayer-min.css" />
    <script type="text/javascript" src="//g.alicdn.com/de/prismplayer/2.6.0/aliplayer-min.js"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
    <script src="/js/house/detail/details_collection.js" type="text/javascript"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/houseAlbum.css">
    <script src="https://static.fangxiaoer.com/js/houseAlbum.js"></script>
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
    <style>
        a {
            text-decoration: none !important;
        }
        .imgMax .prism-player{
            width:  620px !important;
            height: 425px  !important;
            z-index: 9;
        }
        .imgMax .prism-player .prism-big-play-btn{
            bottom: 58px !important;
            left: 28px !important;
        }
        .sort #sortParamNew a.hover, .sort #sortParamNew a:hover,.sort_jg {
            background: #ff5200;
            color: #FFF;
            text-decoration: none;
        }
        .prism-fullscreen-btn,.prism-animation,.current-speed-selector{display: none}
        .details,.details>ul>li>div{width: 830px}
        .canteen div p{width: 660px}
        .photo>ul{
            height: auto !important;
            width: 832px !important;
        }
        .photo ul li:nth-child(4n){ margin-right: 0;}
        .mapsele{top: 0;width: 350px;}
        .mapTab li{padding: 13px 11px;}
        .license{position: absolute;width: 260px;height: 370px;border: 1px solid #ccc;display: none;
            border-radius: 5px;z-index: 99;background-color: #FFF;bottom: 40px;right: 30px;padding: 5px;}
        .license_img{
            width: 100% !important;
            height: 100% !important;
            margin: 0px !important;
            border-radius: 0px !important;
        }
        /* 安心好房样式 */
        .anin_info{
            font-size: 14px;
            color: #333;

        }
        .anin_interval{
            color: #999;
            margin-left: 4px;
            margin-right: 8px;
        }
        .anxin_logo{
            width: 77px;
            height: 18px;
            vertical-align: middle;
        }
        .anin_renzheng{
            vertical-align: middle;
            margin-left: 14px;
        }
        .anxin_color{
            color: #FF5200;
            margin-right: 22px;
            cursor: pointer;

        }
        .anxin_color:hover .xsimg{
            display:block;
        }
        .bubble{
            padding-bottom: 10px;
            position: absolute;
            bottom: 40px;
            left: 170px;
            display: none;
        }
        .xszimg {
            cursor: pointer;
            padding: 0 13px;
            /* width:387px; */
            height:31px;
            line-height: 15px;
            background:rgba(255,255,255,1);
            border:1px solid rgba(230,230,230,1);
            box-shadow:0px 3px 7px 0px rgba(38,39,39,0.15);
        }

        .xszimg::before {
            content: "";
            position: relative;
            bottom: -31px;
            left: 22px;
            width: 0;
            height: 0;
            display: block;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 8px solid #fff;
        }
        .soucang{
            left: 0;
            position: relative;
        }

        /*风险提醒*/
        .risk{ display: inline-block; margin-left: 35px; font-size: 14px; color: #169BD5; cursor: pointer; user-select: none;}
        .riskm{ position: fixed; width: 55vw; min-width: 771px; height: 590px; top: 0; bottom: 0; left: 0; right: 0; margin: auto; background-color: #fff; border-radius: 5px; z-index: 9999999999; display: none; padding: 45px;}
        .rkm{ width: 100%; font-size: 14px;}
        .rkm h2{ font-size: 18px !important; margin-bottom: 10px;}
        .rkm h3{ margin-top: 5px !important; font-size: 14px;}
        .rclose{ position: absolute; top: 7px; right: 7px; width: 27px; height: 27px; border-radius: 50%; border: 2px solid #585858;
            background-image: url('https://static.fangxiaoer.com/m/static/images/search/close.png'); background-size: 60%; background-repeat: no-repeat; background-position: center; cursor: pointer; user-select: none;}
        .isCare{ background-image: url('https://static.fangxiaoer.com/m/static/images/Collection/selected.png') !important; background-size: 15px !important; background-repeat: no-repeat !important; background-position: center !important;}

        .p-note{

            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 19px;

            margin-top: 3px;
        }

        .newCardTels{

            float: right !important;
        }
        .peo-text{
            margin-right: 0px !important;
        }
        .peo-b{
            font-size: 14px !important;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400 !important;
            color: #111111 !important;
            margin-top: 2px;
        }
        .people-img{

            margin-top: 13px;
        }




        .newCardTels{
            width: 104px !important;
            height: 37px !important;
            background: #FF6102 !important;
            border-radius: 6px !important;
            padding: unset !important;
            box-sizing: border-box;
            margin-top: -9px;
            display: flex;
            align-items: center;
        }
        .newCardTels>i{
            display: inline-block;
            width: 18px !important;
            height: 18px !important;
            margin: 0 !important;
            border-radius: initial !important;

            float: left;
            padding-left: 10px;
        }

        .ico-name-attestation{
            margin-left: 0px !important;
        }
        .main .mainContent img{
            margin-top: 13px;
        }



        .people-name{
            margin-bottom: 20px !important;
        }
        .ico-attestation{
            margin: 10px 0 0 0px !important;
        }
        .agent-text{
            margin-top: -12px !important;
        }
        .sddti{
            height: 37px !important;
            line-height: 37px !important;
        }
        .ico-name-attestation{
            margin-left: 0px !important;
        }
        .agent-text2{

        }
        .agent-vip{
            width: 20px !important;
            height: 20px !important;
            margin-top: 5px !important;

        }
        #houseDetailOwner{
            margin-right: 0px !important;
        }
        .people-img img{
            width: 100px !important;
            height: 100px !important;
            border-radius: 50% !important;
        }
        .pe-agent{
            float: left !important;
            margin-right: 0px !important;
        }







        #people{

            width: 351px !important;
        }
        #agent-text1{
            margin-top: -6px !important;
        }


        .sddtj{
            color: #999999 !important;
            width: 169px !important;

            white-space: nowrap !important;
            text-overflow: ellipsis !important;
            overflow: hidden;
        }

        .noHover:hover{
            cursor: auto !important;
        }


        .sanjiao{
            position: absolute;
            width: 16px;
            height: 10px;
            top: 34px;
            left: 50%;
            margin-left: -8px;
            overflow: hidden;
            background-repeat: no-repeat !important;
        }
        .sanjiao1{ background: url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png);}
        .sanjiao2{ background: url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian7.png);}
        #memap a{ color: #fff;}
        #memap a:hover{ color: #fff; text-decoration: none;}

    </style>
    <script>
        //图片自适应大小
        function imgSize() {
            //滚动大图
            var imgWidthHeght2 = $(".imgMax1 ul li").eq(0).width() / $(".imgMax1 ul li").eq(0).height()
            $(".imgMin ul li img").each(function () {
                if (parseInt($(this).width()) <= parseInt($(this).height())) {
                    $(this).css({"height": "100%", "width": "auto"})
                    $(this).css({
                        "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                        "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                    })
                } else {
                    $(this).css({"width": "100%", "height": "auto"})
                    $(this).css({
                        "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                        "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                    })
                }
            })
        }


        $(function () {
            //更多相册
            var len = $(".photo li").length;
            if (len > 8) {
                $(".photo li:gt(7)").hide();
                $(".photo ul").after("<span>查看全部照片(" + len + "张)</span>")
            }
            $(".photo span").live("click", function () {
                $(this).hide();
                $(".photo li").show();
            })
        })
    </script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/deleteListings.css">
</head>

<body>
<!-- 租房房详情页 -->
<input type="hidden" id="Idhouse" th:value="${houseId}"/>
<input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}"/>
<input type="hidden" id="type" th:value="${'4'}">
<input type="hidden" id="projectId" th:value="${houseId}">
<input type="hidden" id="mediaId" th:value="${info?.houseVideo?.mediaID}">
<input type="hidden" id="videoPath" th:value="${videoPath}">
<input type="hidden" id="videoImgPath" th:value="${videoImgPath}">
<input type="hidden" id="housePan" th:value="${info.housePan}">
<input type="panUrl" id="panUrl" th:value="${panUrl_VR}" style="display:none">
<input type="panUrl" id="panUrl_VRImg" th:value="${panUrl_VRImg}" style="display:none">
<input type="hidden" value="2" id="collectType"/>
<form name="form1" method="post" action="14238" id="form1">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=3"></div>

    <input type="hidden" id="houseType" value="2" />
    <input type="hidden" id="goods" value="1" />
    <!--面包屑-->
    <div class="w crumbs">您的位置：
        <a href="/" target="_blank">沈阳房产网</a> &gt;
        <a href="/rents/" target="_blank">沈阳租房</a> &gt;
        <a th:href="${'../rents/r'+info.regionId}" target="_blank"><th:block th:text="${info.regionName + '租房'}"></th:block></a> &gt;
        <a th:href="${'../rents/r'+info.regionId+'-j' + info.plateId}" target='_blank'   th:text="${info.plateName + '租房'}"> </a> &gt;
        <a th:href="${'../saleVillages/'+info.subId+'/index.htm'}" target='_blank'   th:text="${info.subName}"> </a></div>
    <!--基本信息-->
    <th:block th:if="${#strings.toString(info.isDel) ne '1'}">
        <div class="w main">
            <div  class="header_sale" style="position: relative;">
                <h1  class="title1" th:text="${info.title}"></h1>
                <!-- 安心好房气泡 -->
                <div class="bubble">
                    <div class="xszimg">
                        本房源享受安心房源保障，最高保额25.6万元，<a target="_blank" href="/static/rents_guarantee_info.htm" style="color: #2B61CC;">了解保障规则></a>
                    </div>
                </div>
                <ul class="axinfo">
                    <li class="anin_info" style="margin-right: 35px" th:if="${#strings.equals(info.anXuan,'-1')}">
				   	<span class="bubble_none">
				   		<img src="https://static.fangxiaoer.com/web/images/sy/anxin/anxin_logo.png" class="anxin_logo">
				   		<span class="anin_interval">|</span>真实在售
				   		<img src="https://static.fangxiaoer.com/web/images/sy/anxin/renzheng.png" class="anin_renzheng">
				   	</span>
                        <span class="anxin_color jiaoyi">安心交易</span>

                    </li>
                    <li th:if="${!#strings.isEmpty(info.schoolAreaMIDStr)}" th:text="${'审核时间：'+info.schoolAreaMIDStr}" style="margin-right: 20px;"></li>
                    <li th:if="${!#strings.equals(info.anXuan,'-1')}" th:text="${info.visitNum+'人已浏览'}"></li>
                    <a class="soucang" data-toggle="modal" href="#login"><i style="margin: 6px 0px 0 0;"></i>收藏</a>
                    <!--虚假举报-->
                    <div th:include="fragment/falsityreport:: falsityreport"></div>
                </ul>
                <!--分享标签-->
                <div th:include="fragment/fragment:: share_code"></div>
                <div class="houseAlbum" id="houseAlbum">
                    <div class="big_photo" id="big_photo">
                        <ul>
                            <li th:if="${!#lists.isEmpty(info.housePan)}" class="noAClick">
                                <a class='panUrl' target='_blank' th:href="${info.housePan.panUrl}">
                                    <img onload='imgSize()' style='width: 100%; height: auto;margin-left: -35px;' class='panUrl_VRImg' th:src="${#strings.isEmpty(info.housePan.panImageUrl)?'':#strings.toString(info.housePan.panImageUrl).replaceAll('middle','big')}" >
                                </a>
                            </li>
                            <li class="prism-player2 noAClick " id="J_prismPlayer" th:if="${info?.houseVideo?.mediaID}" style="height: 417px !important;"></li>
                            <li th:if="${#lists.isEmpty(info.pic)}">
                                <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg"  alt=""  onload="imgSize()"  />
                            </li>
                            <li th:if="${!#lists.isEmpty(info.pic)}" th:each="pic,p:${info.pic}"  class="playerPause">
                                <img th:src="${#strings.toString(pic.url).replace('middle','big')}"  alt=""  onload="imgSize()"  />
                            </li>
                        </ul>
                        <div  th:if="${!#lists.isEmpty( info.pic)}" th:each="pic,p:${info.pic}" style="display: none;">
                            <input id="indexPic" th:if="${p.index == 0}" th:value="${#lists.toList(info.pic).get(0).url}" style="display: none;"/>
                        </div>
                    </div>
                    <div class="min_photo">
                        <div class="min_photoMain clearfix" id="min_photoMain">
                            <ul>
                                <li th:if="${!#lists.isEmpty(info.housePan)}">
                                    <img onload='imgSize()' class='panUrl_VRImg' th:src="${#strings.isEmpty(info.housePan.panImageUrl)?'':#strings.toString(info.housePan.panImageUrl).replaceAll('middle','big')}" >
                                </li>
                                <li th:if="${info?.houseVideo?.mediaID}">
                                    <i class='videoIcon'></i>
                                    <img th:src="${videoImgPath}">
                                </li>
                                <li  th:if="${!#lists.isEmpty( info.pic)}" th:each="pic,p:${info.pic}" class="playerPause">
                                    <img th:src="${#strings.toString(pic.url).replace('middle','big')}"  alt=""  onload="imgSize()"  id="imgssss" />
                                </li>
                                <li th:if="${#lists.isEmpty( info.pic)}">
                                    <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" />
                                </li>
                            </ul>
                        </div>
                        <div class="min_prev_btn1 playerPause" id="min_prev_btn1"><s></s></div>
                        <div class="min_next_btn1 playerPause" id="min_next_btn1"><s></s></div>
                    </div>
                </div>
                <!--弹窗轮播大图-->
                <div class="photo_Popup"></div>
                <div class="large_photo" id="large_photo">
                    <div class="photo_Popup_xx"></div>
                    <div class="large_photoMain" id="large_photoMain">
                        <ul>
                            <li th:if="${!#lists.isEmpty(info.housePan)}">
                                <a class='' target='_blank' th:href="${info.housePan.panUrl}">
                                    <img onload='imgSize()' class='panUrl_VRImg' th:src="${#strings.isEmpty(info.housePan.panImageUrl)?'':#strings.toString(info.housePan.panImageUrl).replaceAll('middle','big')}" >
                                </a>
                            </li>
                            <li class="prism-player2" id="J_prismPlayer2" th:if="${info?.houseVideo?.mediaID}" style="width: 1170px;height: 100%;"></li>
                            <li th:if="${#lists.isEmpty( info.pic)}" class="playerPause">
                                <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" />
                            </li>
                            <li th:if="${!#lists.isEmpty( info.pic)}" th:each="pic,p:${info.pic}">
                                <img  th:src="${#strings.toString(pic.url).replace('middle','big')}" alt="" />
                            </li>
                        </ul>
                    </div>
                    <div class="large_prev_btn1 playerPause" id="large_prev_btn1"></div>
                    <div class="large_next_btn1 playerPause" id="large_next_btn1"></div>
                </div>
                <script>
                    $(document).ready(function () {
                        var videoVal = $("#mediaId").val();
//                        var housePan = $("#housePan").val();
//                        var panUrl = $("#panUrl").val();
//                        var panUrl_VRImg = $("#panUrl_VRImg").val();
                        var videoMinImg = $("#indexPic").val()
                        if( videoVal !=""){
                            //改为调接口获取视频地址（2018.10.20）
                            player = new Aliplayer({
                                id: 'J_prismPlayer',
                                autoplay: false,
                                //支持播放地址播放,此播放优先级最高
                                source: $("#videoPath").val(),
                                cover: $("#videoImgPath").val()
                            }, function (player) {
                                player.on("ended", endedHandle);
                                console.log('播放器创建好了。')
                            });
                            function endedHandle() {
                                player.dispose(); //销毁
                                $('#J_prismPlayer').empty();
                                //重新创建
                                player = new Aliplayer({
                                    id: 'J_prismPlayer',
                                    autoplay: false,
                                    //支持播放地址播放,此播放优先级最高
                                    source: $("#videoPath").val(),
                                    cover: $("#videoImgPath").val()
                                });
                            };
                            player2 = new Aliplayer({
                                id: 'J_prismPlayer2',
                                autoplay: false,
                                //支持播放地址播放,此播放优先级最高
                                source: $("#videoPath").val(),
                                cover: $("#videoImgPath").val()
                            }, function (player2) {
                                player2.on("ended", endedHandle);
                                console.log('播放器创建好了。')
                            });
                            function endedHandle() {
                                player2.dispose(); //销毁
                                $('#J_prismPlayer2').empty();
                                //重新创建
                                player2 = new Aliplayer({
                                    id: 'J_prismPlayer2',
                                    autoplay: false,
                                    //支持播放地址播放,此播放优先级最高
                                    source: $("#videoPath").val(),
                                    cover: $("#videoImgPath").val()
                                });
                            }
                            $(".playerPause").click(function () {
                                player.pause();
                                player2.pause();
                            })
                        }

//                头部详情房源相册轮播
                        $('#demo1').banqh({
                            box:"#houseAlbum",//总框架
                            pic:"#big_photo",//大图框架
                            pnum:"#min_photoMain",//小图框架
                            prev_btn:"#min_prev_btn1",//小图左箭头
                            next_btn:"#min_next_btn1",//小图右箭头
                            pop_prev:"#large_prev_btn1",//弹出框左箭头
                            pop_next:"#large_next_btn1",//弹出框右箭头
                            pop_div:"#large_photo",//弹出框框架
                            pop_pic:"#large_photoMain",//弹出框图片框架
                            pop_xx:".photo_Popup_xx",//关闭弹出框按钮
                            mhc:".photo_Popup",//朦灰层
                            autoplay:false,//是否自动播放
                            interTime:5000,//图片自动切换间隔
                            delayTime:400,//切换一张图片时间
                            pop_delayTime:400,//弹出框切换一张图片时间
                            order:0,//当前显示的图片（从0开始）
                            picdire:true,//大图滚动方向（true为水平方向滚动）
                            mindire:true,//小图滚动方向（true为水平方向滚动）
                            min_picnum:4,//小图显示数量
                            pop_up:true//大图是否有弹出框
                        })
//                房源详情模块相册点击弹出
                        $('#demo2').banqh({
                            box:"#houseAlbum2",//总框架
                            pic:"#big_photo2",//大图框架
                            pnum:"#min_photoMain2",//小图框架
                            prev_btn:"#min_prev_btn12",//小图左箭头
                            next_btn:"#min_next_btn12",//小图右箭头
                            pop_prev:"#large_prev_btn12",//弹出框左箭头
                            pop_next:"#large_next_btn12",//弹出框右箭头
                            pop_div:"#large_photo2",//弹出框框架
                            pop_pic:"#large_photoMain2",//弹出框图片框架
                            pop_xx:".photo_Popup_xx",//关闭弹出框按钮
                            mhc:".photo_Popup",//朦灰层
                            autoplay:false,//是否自动播放
                            interTime:5000,//图片自动切换间隔
                            delayTime:400,//切换一张图片时间
                            pop_delayTime:400,//弹出框切换一张图片时间
                            order:0,//当前显示的图片（从0开始）
                            picdire:true,//大图滚动方向（true为水平方向滚动）
                            mindire:true,//小图滚动方向（true为水平方向滚动）
                            min_picnum:4,//小图显示数量
                            pop_up:true//大图是否有弹出框
                        })
                        $(".noAClick").click(function () {
                            $(".photo_Popup").hide();
                            $(".large_photo").hide();
                        })

                    })

                </script>

                <div th:class="${#strings.toString(info.isDel) ne '1' ?'mainContent':(#strings.toString(info.rentState) eq '4' and #strings.toString(info.state ) eq '3' ?'mainContent sellState2':'mainContent sellState3')}">
                    <div class="price"><span><b th:text="${info.price eq '0' ? '面议':info.price}"></b>
                <th:block th:text="${info.price eq '0' ? '':'元/月'}"></th:block> &nbsp;
                <th:block th:text="${info.rentTypeName}+'　'+${info.paymentName}"></th:block></span>
                    </div>
                    <div class="basic">
                        <ul>
                            <!--                    <li class="" >
                                                    <span>租住要求</span>
                                                    <p th:text="${info.roomRentName}"></p>
                                                </li>-->
                            <li>
                                <span>小区名称</span>
                                <p><a th:href="${'../rents/r'+info.regionId}" target="_blank" th:text="${info.regionName}"></a>
                                    - <a th:href="${'../rents/j'+info.plateId + 'r' + info.regionId}" target="_blank" th:text="${info.plateName}"></a>
                                    - <a th:href="${'../saleVillages/'+info.subId+'/index.htm'}" target='_blank'  th:text="${info.subName}"></a></p>
                            </li>
                            <li class="" >
                                <span>装修情况</span>
                                <p th:text="${info.fitmentType}" style=" display: inline-block; width: unset;"></p>
                                <div class="risk">《风险提醒》</div>
                            </li>
                            <li>
                                <span>所在地址</span>
                                <p><th:block th:text="${info.address}"></th:block> <a id="MapPeiTao"><s></s></a></p>
                            </li>
                            <script type="text/javascript">
                                $("#MapPeiTao").click(function() {
                                    $("html, body").animate({
                                        scrollTop: $("#MapZhouBianPeiTao1_mapDom").offset().top }, {duration: 500,easing: "swing"});
                                    return false;
                                });
                            </script>
                        </ul>
                    </div>
                    <div class="type ">
                        <ul>
                            <li>
                                <b th:text="${info.room+'室'+info.hall+'厅'+info.toilet+'卫'}"></b>
                                <p th:text="${info.floorDesc+'/'+info.totalFloorNumber+'层'}"></p>
                            </li>
                            <li>
                                <b th:text="${info.area+'m²'}"></b>
                                <p>建筑面积</p>
                            </li>
                            <li>
                                <b th:text="${info.forward}"></b>
                                <!--                                <p th:text="${info.buildDate}"></p>-->
                                <p th:text="${#strings.isEmpty(info.buildDate)}?'':${info.buildDate + '年建筑'}"></p>
                            </li>
                        </ul>
                    </div>

                    <div th:if="${!#strings.isEmpty(info.memberType) and info.memberType eq '经纪人' and #strings.toString(info.isDel) ne '1' }" class="card jjr" style="position: relative;overflow: inherit;">
                        <div class="people-img">
                            <a class="people-img-join" th:if="${info.keeperLevel2 ne '1' and info.agentState eq '1'}"
                               th:href="${#strings.isEmpty(info.agencyId) ? '' :'/agent/rents/'+info.agencyId}" target="_blank">
                                <img  th:src="${info.keeperPic2 ne null and info.keeperPic2 ne''}?${info.keeperPic2}:@{https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png}"  />
                            </a>
                            <img th:if="${info.keeperLevel2 ne '1' and info.agentState ne '1'}"
                                 th:src="${info.keeperPic2 ne null and info.keeperPic2 ne''}?${info.keeperPic2}:@{https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png}"/>
                            <img th:if="${info.keeperLevel2 eq '1'}"
                                 th:src="${info.keeperPic2 ne null and info.keeperPic2 ne''}?${info.keeperPic2}:@{https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png}"  />
                            <!--                            <a th:href="${#strings.isEmpty(info.agencyId) ? '' :'/agent/rents/'+info.agencyId}" target="_blank" class="join-Shop">进入Ta的店铺</a>-->
                        </div>



                        <dl     th:class="${info.keeperLevel2} eq '1' ? 'normolTel':'agentTel'" id="people" >
                            <dt class="people-name">
                                <a  th:if="${info.agentState eq '1'}"   th:href="${#strings.isEmpty(info.agencyId) ? '' :'/agent/rents/'+info.agencyId}" target="_blank">
                                    <b  class="pe-agent" th:text="${info.keeperName2}"></b>
                                    <img  th:if="${info.intermediaryState eq '1'}"   class="agent-vip" src="https://static.fangxiaoer.com/web/images/sy/house/agent-vip.png" >
                                </a>

                                <a  th:if="${info.agentState ne '1'}"   >
                                    <b  class="pe-agent noHover" th:text="${info.keeperName2}"  ></b>

                                </a>



                                <!--                                <span th:text="${info.intermediaryName}"></span>-->

                            </dt>
                            <th:block th:if="${info.keeperLevel2 ne '1'}">
                                <!--新增经纪人相关认证-->
                                <div >
                                    <!--                                    <div style="float: left"  class="agengt-ico-new" th:if="${info.agentBusinessCardForPc eq null or info.agentBusinessCardForPc eq ''}"></div>-->
                                    <div class="ico-attestation "    id="agent-text1">
                                        <!--<div class="ico-house-attestation"><i></i><span>房本认证</span></div>-->
                                        <div class="ico-name-attestation agent-text2" th:if="${info.hasIdCard eq '1'}"><i></i><span>实名认证</span></div>

                                        <th:block th:if="${info.agentState eq '1'}">
                                            <div class="sddtj"><lable th:text="${info.agentIntermediaryAlias}"></lable></div>
                                        </th:block>
                                        <th:block th:if="${info.agentState ne '1'}">
                                            <div class="sddtj">经纪人</div>
                                        </th:block>
                                    </div>
                                </div>
                                <!--<span  class="encoded" th:unless="${info.agentBusinessCardForPc eq null or info.agentBusinessCardForPc eq ''}"
                                       onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                                     <i>公司执照编码：</i>
                                     <span style="color:#999;">
                                         <th:block th:text="${info.agentBusinessNum}"></th:block>
                                     </span>
                                 </span>-->
                                <!--                                <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal">-->
                                <!--                                    <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" class="newChat">-->
                                <!--                                        <img src="https://static.fangxiaoer.com/im_ziyuan/im/images/liaobei.png" style="width: 20px;height: 18px;margin-top: 10px;margin-left: 8px;margin-right: 0px;">-->
                                <!--                                        <text>在线聊呗</text>-->
                                <!--                                    </div>-->
                                <!--                                </a>-->
                                <!--                                <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/im/'+houseId+'-2'}" target="_blank">-->
                                <!--                                    <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" class="newChat">-->
                                <!--                                        <img src="https://static.fangxiaoer.com/im_ziyuan/im/images/liaobei.png" style="width: 20px;height: 18px;margin-top: 10px;margin-left: 8px;margin-right: 0px;">-->
                                <!--                                        <text>在线聊呗</text>-->
                                <!--                                    </div>-->
                                <!--                                </a>-->
                                <dd class="newCardTels">
                                    <i><img src="https://static.fangxiaoer.com/web/images/sy/sale/s_6.png" alt=""></i>
                                    <span>电话咨询</span>
                                    <div class="show-CardTels">
                                        <img id="rent1" src="" alt="">
                                        <p>微信扫码拨号</p>
                                        <p><i>快速获取经纪人联系方式</i></p>
                                    </div>
                                </dd>
                                <div style="clear: both;"></div>
                                <div class="p-note"> 如在沟通或交易过程中遇到任何问题，均可致电房小二网平台服务电话：************</div>
                                <div style="clear: both;"></div>
                                <!--<div class="license" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                                    <img th:src="${info.agentBusinessCardForPc}" class="license_img">
                                </div>
                                <div style="clear: both;"></div>-->
                            </th:block>
                        </dl>
                        <div style="clear: both;"></div>
                        <script src="/js/appointmentLook.js" type="text/javascript"></script>
                        <script>
                            function showLicense(e) {
                                $(".license").show();
                            }
                            function hideLicense(e) {
                                $(".license").hide();
                            }
                            /*$(function () {
                                var isNo = [[${info.isXiQue}]];
                                if (isNo != 1){
                                    $("#people").attr("class","noyongjin");
                                }
                            })*/
                            function changeColor(e) {
                                $(e).css("background-color","#188CDE")
                            }
                            function changeColor2(e) {
                                $(e).css("background-color","#32a3f2")
                            }
                        </script>
                    </div>

                    <!--            <div th:if="${!#strings.isEmpty(info.memberType) and info.memberType eq '个人'}" class="card gr">
                                    <div><img th:src="${info.keeperPic2 == null ?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':info.keeperPic2}" /></div>
                                    <dl class="noyongjin">
                                        <dt><b th:text="${info.keeperName2}"></b></dt>
                                        <dd th:text="${info.keeperTel2}"> </dd>
                                    </dl>
                                </div>-->
                    <div th:if="${!#strings.isEmpty(info.memberType) and info.memberType eq '个人' and #strings.toString(info.isDel) ne '1'}"  class="card jjr">
                        <p>
                            <img th:src="${#strings.isEmpty(info.keeperPic2)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':info.keeperPic2}" /><br>
                        </p>
                        <dl class="normolTel" style="width: 340px">
                            <!--                            <dt><b th:text="${info.keeperName2}"></b></dt>-->
                            <dt class="people-name"><b id="rentKeeperName2"></b> <b class="peo-b">（个人）</b> </dt>

                            <!--认证icon-->
                            <div class="ico-attestation" style="margin: 12px 0 0 0px; height: 35px; width: unset;">
                                <div class="ico-name-attestation" th:if="${info.hasIdCard eq '1'}" style="margin-right: 13px; margin-left: 7px;"><i></i><span>实名认证</span></div>
                                <div class="ico-name-attestation" th:if="${info.realEstateStatus eq '100'}" style="width: 104px; margin-right: 20px; margin-left: 7px;"><i class="isCare"></i><span>已上传房证</span></div>
                            </div>

                            <dd class="newCardTels" style="width: 154px;">
                                <i ><img src="https://static.fangxiaoer.com/web/images/sy/sale/s_6.png" alt=""></i>
                                <span>电话咨询</span>
                                <div class="show-CardTels">
                                    <img id="rent1" src="" alt="">
                                    <p>微信扫码拨号</p>
                                </div>
                            </dd>
                            <div style="clear: both;"></div>
                            <div class="p-note"> 如在沟通或交易过程中遇到任何问题，均可致电房小二网平台服务电话：************</div>

                        </dl>


                    </div>
                </div>
            </div>
            <div class="w" style="overflow: hidden;">
                <div class="left">
                    <div class="allocation"  th:style="${info.otherEstablish ne '' and info.otherEstablish ne null}?'display:block':'display:none'">
                        <div class="head">房屋配置</div>
                        <ul>
                            <li th:each="other:${other}">
                                <b th:class="${other.value}"></b>
                                <p th:text="${other.key}"></p>
                            </li>
                        </ul>
                    </div>
                    <div class="details">
                        <div class="head">房源描述</div>
                        <ul>
                            <li>
                                <span>小区详情</span>
                                <div>
                                    <ul>
                                        <li>
                                            <span>小区名称：</span>
                                            <p th:text="${info.subName}"></p>
                                        </li>
                                        <li>
                                            <span>绿&ensp;化&ensp;率：</span>
                                            <p th:text="${info.landScaping ne '' and info.landScaping ne null}?${info.landScaping}:'暂无资料'"></p>
                                        </li>
                                        <li>
                                            <span>物业公司：</span>
                                            <p th:text="${info.propertyCom ne '' and info.propertyCom ne null}?${info.propertyCom}:'暂无资料'"></p>
                                        </li>
                                        <li>
                                            <span>物&ensp;业&ensp;费：</span>
                                            <p th:text="${#strings.isEmpty(info.propertyFee) || #strings.toString(info.propertyFee).equals('0.0') ?'暂无资料':info.propertyFee+'元/㎡月'}"></p>
                                        </li>
                                        <!-- <li>
                                             <span>租&emsp;&emsp;房：</span>
                                             <a th:href="${'/rents/-v'+info.subId+'-r'+info.regionId}" target="_blank">
                                                 <p th:text="${info.rentCount ne '' and info.rentCount ne null}?${info.rentCount+'套'}:'暂无资料'"  th:style="${info.saleCount ne '' and info.saleCount ne null ? 'color:#2f51b3;':''}"> </p>
                                             </a>
                                         </li>-->
                                        <!--<li>-->
                                        <!--<span>租&emsp;&emsp;房：</span>-->
                                        <!--<a th:if="${info.rentCount ne '' and info.rentCount ne null}" th:href="${'/rents/-v'+info.subId+'-r'+info.regionId}" target="_blank">-->
                                        <!--<p th:text="${info.rentCount+'套'}" style="color:#2f51b3;"> </p>-->
                                        <!--</a>-->
                                        <!--<p th:if="${info.rentCount eq '' or info.rentCount eq null}" th:text="${'暂无资料'}"></p>-->
                                        <!--</li>-->
                                        <!--<li>
                                            <span>二&ensp;手&ensp;房：</span>
                                            <a th:href="${'/saleHouses/-v'+info.subId+'-r'+info.regionId}" target="_blank">
                                                <p th:text="${info.saleCount ne '' and info.saleCount ne null}?${info.saleCount+'套'}:'暂无资料'" th:style="${info.saleCount ne '' and info.saleCount ne null ? 'color:#2f51b3;':''}"></p>
                                            </a>
                                        </li>-->
                                        <!--<li>-->
                                        <!--<span>二&ensp;手&ensp;房：</span>-->
                                        <!--<a th:if="${info.saleCount ne '' and info.saleCount ne null}" th:href="${'/saleHouses/-v'+info.subId+'-r'+info.regionId}" target="_blank">-->
                                        <!--<p th:text="${info.saleCount+'套'}" style="color:#2f51b3;"> </p>-->
                                        <!--</a>-->
                                        <!--<p th:if="${info.saleCount eq '' or info.saleCount eq null}" th:text="${'暂无资料'}"></p>-->
                                        <!--</li>-->
                                    </ul>
                                </div>
                            </li>
                            <li>
                                <span>核心卖点</span>
                                <div class="fyms" th:utext="${info.description}">
                                </div>
                            </li>
                            <!--<li class="" th:if="${!#strings.isEmpty(info.serviceIntro)}">
                                <span>服务介绍</span>
                                <div class="fyms" th:utext="${info.serviceIntro}"></div>
                                &lt;!&ndash;<div></div>&ndash;&gt;
                            </li>-->
                            <li class="" th:if="${!#strings.isEmpty(info.serviceIntro) || !#strings.isEmpty(agentLabel)}">
                                <section th:if="${!#strings.isEmpty(info.serviceIntro)}">
                                    <span>服务介绍</span>
                                    <div class="fyms" th:utext="${info.serviceIntro}"></div>
                                </section>
                                <!--经纪人服务标签-->
                                <section th:if="${!#strings.isEmpty(agentLabel)}">
                                    <span>服务特色</span>
                                    <div class="grfw">
                                        <span  th:each="agentLabel:${agentLabel}"  th:text="${agentLabel.name}"></span>
                                    </div>
                                </section>
                            </li>
                            <li class="" th:if="${!#strings.isEmpty(info.traffic) || !#strings.isEmpty(info.education) || !#strings.isEmpty(info.market)
         || !#strings.isEmpty(info.hospital) || !#strings.isEmpty(info.bank) || !#strings.isEmpty(info.othersettings)}">
                                <span>小区配套</span>
                                <!--<div th:text="${info.subDescription ne '' and info.subDescription ne null}?${info.subDescription}:'暂无资料'"></div>-->
                                <div  style="border:0" class="canteen">
                                    <div th:if="${!#strings.isEmpty(info.traffic)}"><span>交通：</span><p th:text="${info.traffic}"></p></div><!-- traffic-->
                                    <div th:if="${!#strings.isEmpty(info.education)}"><span>学校：</span><p th:text="${info.education}"></p></div><!-- education -->
                                    <div th:if="${!#strings.isEmpty(info.market)}"><span>商场：</span><p th:text="${info.market}"></p></div><!-- market -->
                                    <div th:if="${!#strings.isEmpty(info.hospital)}"><span>医院：</span><p th:text="${info.hospital}"></p></div><!-- hospital -->
                                    <div th:if="${!#strings.isEmpty(info.bank)}"><span>银行：</span><p th:text="${info.bank}"></p></div><!-- bank -->
                                    <div th:if="${!#strings.isEmpty(info.othersettings)}"><span>其他：</span><p th:text="${info.othersettings}"></p></div><!-- othersettings -->
                                </div>
                            </li>
                        </ul>

                    </div>
                    <div class="details details2" th:if="${!#strings.isEmpty(info.weAre) or !#strings.isEmpty(info.serviceProcess) or !#strings.isEmpty(info.servicePromise) or !#strings.isEmpty(info.suggestion)}">
                        <div class="head">服务保障</div>
                        <ul>
                            <li th:if="${!#strings.isEmpty(info.weAre)}">
                                <i class="serve serve-1"></i>
                                <span>我们是谁？</span>
                                <div class="fyms" th:utext="${info.weAre}"></div>
                            </li>
                            <li th:if="${!#strings.isEmpty(info.servicePromise)}">
                                <i class="serve serve-2"></i>
                                <span>我的承诺</span>
                                <div class="fyms" th:utext="${info.servicePromise}"></div>
                            </li>
                            <li th:if="${!#strings.isEmpty(info.serviceProcess)}">
                                <i class="serve serve-3"></i>
                                <span>服务流程</span>
                                <div class="fyms" th:utext="${info.serviceProcess}"></div>
                            </li>
                            <li th:if="${!#strings.isEmpty(info.suggestion)}">
                                <i class="serve serve-4"></i>
                                <span>服务建议</span>
                                <div class="fyms" th:utext="${info.suggestion}"></div>
                            </li>
                        </ul>
                    </div>
                    <!--安心好房模块-->
                    <div  th:if="${#strings.equals(info.anXuan,'-1')}" class="w photo" style="margin-bottom: 30px;height: 70px; display: flex;align-items: center;font-size: 14px;color: #333;font-weight: 700;">
                        <img src="https://static.fangxiaoer.com/web/images/sy/anxin/anxin_logo.png" style="margin-right: 14px;">
                        该经纪人已加入“安心交易”保障服务，最高保额25.6万，<a target="_blank"  href="/static/rents_guarantee_info.htm" style="color: #2B61CC;font-weight: 500;">了解保障规则></a>
                    </div>
                    <div class="w photo" th:if="${!#lists.isEmpty(info.picsOrange)}" id="big_photo2">
                        <div th:if="${!#lists.isEmpty(info.picsOrange)}" class="head">房源相册</div>
                        <!--房源相册模块-->
                        <ul>
                            <li th:each="pic:${info.picsOrange}">
                                <img th:src="${pic.url}"  alt="更多房源图片" />
                                <p  th:text="${info.subName+' '+info.area+'m²'}"></p>
                            </li>
                        </ul>
                    </div>
                    <!--房源相册弹窗轮播大图-->
                    <div class="large_photo" id="large_photo2">
                        <div class="photo_Popup_xx"></div>
                        <div class="large_photoMain" id="large_photoMain2">
                            <ul>
                                <li th:if="${!#lists.isEmpty( info.picsOrange)}" th:each="pic,p:${info.picsOrange}">
                                    <img  th:src="${#strings.toString(pic.url).replace('middle','big')}" alt="" />
                                </li>

                            </ul>
                        </div>
                        <div class="large_prev_btn1 playerPause" id="large_prev_btn12"></div>
                        <div class="large_next_btn1 playerPause" id="large_next_btn12"></div>
                    </div>


                    <!--您可能感兴趣的房源-->
                    <div class="interest_house"  style="width: 870px" th:if="${!#lists.isEmpty(interestHouse)  and #lists.size(interestHouse) ge 5}">
                        <div class="interest_house_title">您可能感兴趣的房源</div>
                        <ul>
                            <li th:each="rent,i:${interestHouse}" th:if="${ #strings.toString(rent.houseId) != #strings.toString(houseId)}">
                                <div class="interest_house_images">
                                    <a th:href="'/rent/'+${rent.houseId}+'.htm'" target="_blank">
                                        <img th:src="${#strings.isEmpty(rent.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : rent.pic}"
                                             th:alt="${rent.subName}">
                                        <span th:text="${rent.subName}"></span>
                                    </a>
                                </div>
                                <div class="interest_house_info">
                                    <a th:href="'/rent/'+${rent.houseId}+'.htm'" target="_blank">
                                        <span class="inter_house_title" th:text="${rent.title}"></span>
                                    </a>
                                    <div class="interest_house_details">
                                        <span th:if="${#strings.isEmpty(rent.price) or (#strings.toString(rent.price) eq '0.0')}"  th:text='面议'></span>
                                        <span th:if= "${!#strings.isEmpty(rent.price) and #strings.toString(rent.price) ne '0.0'}" >
                                    <th:block th:text="${#strings.toString(rent.price).contains('.')? #strings.toString(rent.price).replaceAll('0+?$','').replaceAll('[.]$', '') : rent.price}"></th:block>
                                    <i th:text="${#strings.isEmpty(rent.price) or (#strings.toString(rent.price) eq '0.0')?'':'元'}"></i>
                                </span>
                                        <s  th:text="${#strings.toString(rent.area).contains('.')? #strings.toString(rent.area).replaceAll('0+?$','').replaceAll('[.]$', '') : rent.area}+'m²'"></s>
                                        <b th:text="${rent.room+'室'+rent.hall+'厅'+rent.toilet+'卫'}"></b>

                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="right">
                    <div th:if="${!#lists.isEmpty(same)}" id="xiangsihouse" class="head recommend">
                        <h1 >同小区您可能感兴趣的房源</h1>
                        <ul>
                            <li class="" th:each="s:${same}">
                                <a th:href="'/rent/'+${s.houseId}+'.htm'" >
                                    <div>
                                        <img th:src="${#strings.isEmpty(s.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.pic}" th:alt="${s.subName}" />
                                        <span th:block th:text="${#strings.toString(s.price).substring(0,#strings.toString(s.price).length()-2) eq '0' ? '面议':#strings.toString(s.price).substring(0,#strings.toString(s.price).length()-2)+'元/月'}"> </span>
                                        <p th:text="${s.room+'室'+s.hall+'厅'}"></p>
                                        <b th:text="${s.subName}"></b>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>


            <div class="w">

<!--                <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
                <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
<!--                <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />-->
<!--                <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>-->
                <script src="/js/QQMap/newBaiduMap_SelectZhoubian.js?t=55"></script>

                <script charset="utf-8" src="https://map.qq.com/api/js?v=2.exp&key=I3ZBZ-5K5WL-77WPV-M7G3Q-3Z2ZK-DVFNX"></script>
                <script th:inline="javascript">
                    $(document).ready(function () {

                        var id = [[${info.subId}]];
                        var lng = [[${info.longitude}]];
                        var lat = [[${info.latitude}]];
                        var title = [[${info.subName}]];
                        var address = [[${info.address}]];
                        var city = '沈阳';
                        var content = "";
                        $("#mapsubTitle").val(title);
                        bdMap.init("memap", { id: id, houselng: lng, houselat: lat, radius: 2000, suofa: 14, bdtitle: title, bdcontent: content, address: address, city: city });

                    });
                </script>
            </div>


            <div>
                <!--<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20181214" rel="stylesheet" type="text/css">-->
                <style>
                    .map_lpcon {
                        padding:0!important;
                        margin:0!important;
                    }
                    .map_lp{
                        display:block!important;
                        padding: 0!important;
                    }
                    .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
                        z-index: 3;
                        padding: 16px 0px;
                    }
                    .BMapLib_SearchInfoWindow {
                        font: 12px arial,宋体,sans-serif;
                        position: absolute;
                        background-color: #fff;
                        cursor: default;
                        border-radius: 10px;
                        padding: 0 10px;
                    }
                    .BMapLib_SearchInfoWindow img {
                        border: 0;
                        margin-top: 2px!important;
                        top: auto !important;
                    }
                    /*.BMapLib_bubble_content p{white-space: normal !important;}*/
                    .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
                        border-bottom: 1px solid #ccc;
                        height: 40px!important;
                    }
                    .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
                        line-height: 40px!important;
                        background-color: #fff!important;
                        overflow: hidden;
                        height: 40px!important;
                        adding: 0 5px;
                        font-size: 14px!important;
                    }
                    .BMapLib_SearchInfoWindow {
                        font: 12px arial,宋体,sans-serif;
                        position: absolute;
                        border: 0!important;
                        background-color: #fff;
                        cursor: default;
                        box-shadow: 0px 0px 10px #999;
                    }
                    .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
                        float: right;
                        height: 30px;
                        width: 22px;
                        cursor: pointer;
                        background-color: #fff!important;
                        padding-top: 8px!important;
                    }
                    /*全屏*/
                    .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
                    .full .nmaptitleleft{margin-top:0}
                    .full .symap{height:100%}
                    .full .dt1{height:100%;}
                    .full #memap{height: 100% !important;}
                    .full .mapsele{height:660px !important}
                    .full{z-index: 100003 !important;}
                    .nmaptitleleft a{float:right;background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg) no-repeat;padding-left:23px}
                    .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:20px;}
                    .nmaptitleleft a.map_house{background-position:0px 12px}
                </style>
                <div id="MapZhouBianPeiTao1_mapDom">
                    <div class="nmaptitle">
                        <div class="nmaptitleleft">
                            <a href="/static/rentmap.htm" target="_blank" class="map_house">沈阳楼盘地图</a>
                            <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                            <p>周边配套</p>
                        </div>
                    </div>
                    <div class="symap">
                        <div class="dt1">
                            <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                            </div>
                            <div class="mapsele" id="mapsele" >
                                <!--<div class="msclose" id="msclose"></div>-->
                                <div class="mapTab" id="mapTab">
                                    <ul>
                                        <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                                        <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                                        <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                                        <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                                        <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                                        <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                                        <!--<li onclick="bdMap.searechMap('银行')"><a href="javascript:void(0)">银行</a></li>-->
                                    </ul>
                                </div>
                                <div class="map_lp">
                                    <div id="hs_wrap">
                                        <!--<div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>-->
                                        <div class="map_lpcon"  id="r-result">
                                            <div class="map_dl">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="symap">
                            <div style="border:0" class="canteen" id="peitao">
                                <div th:if="${!#strings.isEmpty(info.traffic)}"><span>交通：</span>
                                    <p th:text="${info.traffic}"></p></div><!-- traffic-->
                                <div th:if="${!#strings.isEmpty(info.education)}"><span>学校：</span>
                                    <p th:text="${info.education}"></p></div><!-- education -->
                                <div th:if="${!#strings.isEmpty(info.market)}"><span>商场：</span>
                                    <p th:text="${info.market}"></p></div><!-- market -->
                                <div th:if="${!#strings.isEmpty(info.hospital)}"><span>医院：</span>
                                    <p th:text="${info.hospital}"></p></div><!-- hospital -->
                                <div th:if="${!#strings.isEmpty(info.bank)}"><span>银行：</span>
                                    <p th:text="${info.bank}"></p></div><!-- bank -->
                                <div th:if="${!#strings.isEmpty(info.othersettings)}"><span>其他：</span>
                                    <p th:text="${info.othersettings}"></p></div><!-- othersettings -->

                            </div>
                            <!--<i class="mapBtn mapBtnF"></i>-->
                        </div>
                        <script>
                            $(function () {
                                $(".mapTab ul li ").click(function () {
                                    $(".mapTab ul li ").removeClass("hover")
                                    $(this).addClass("hover")
                                });
                                $(".map_dl a").mouseover(function () {
                                    $(this).css("background-color", "#e8f4ff");
                                });
                                $(".map_dl a").mouseout(function () {
                                    $(this).css("background-color", "#fff");
                                });
                            });
                            function Full() {
                                if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                                    $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                                    $(".map_lpcon").height("288px")
                                    $(".map_full").html("全屏")
                                } else {
                                    $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                                    $(".map_lpcon").height("100%")
                                    $(".map_full").html("取消全屏")
                                }
                            }
                        </script>
                    </div>
                </div>

            </div>



            <div class="saleHouseIndent">
                <div class="page1">
                    <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                    <h1>在线支付享优惠</h1>
                    <ul>
                        <li><span>购房人</span><input id="name" placeholder="请填写姓名" type="text" maxlength="20" /></li>
                        <li><span>手机号码</span><input type="text" id="phone" placeholder="请填写手机号码" maxlength="11" value="" /></li>
                        <li class="yzm" style="border-bottom: 1px solid #ccc;"><span>请输入</span><input type="password" autocomplete="new-password" id="code" placeholder="验证码/密码" maxlength="20" style="width: 129px;" /><b class="fxe_ReSendValidateCoad">获取验证码</b><b class="fxe_validateCode" style="display: none;"></b></li>
                        <!--
                                        <li class="mm" style="display: none; border-bottom: 1px solid #ccc;"><span>密码</span><input type="password" id="pwd" placeholder="请填写密码" maxlength="20" /></li>
                        -->
                        <input type="hidden" id="userType" value="">
                    </ul>
                    <a>佣金95折</a>
                    <dl>
                        <dt></dt>
                        <dd>我已经看过并同意<a href="/Help/remission" target="_blank">《佣金减免说明书》</a></dd>
                    </dl>
                    <!--<span class="x"></span>-->
                </div>
                <div class="page2">
                    <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                    <h1 >在线支付</h1>
                    <p  th:text="${info.title}" >国瑞城 1室 0厅 1卫 </p>
                    <ul>
                        <li><span>在线支付20元，约折佣340元</span></li>
                        <li  th:text="${'地址：'+info.address}">地址： 大东区东北大马路398号</li>
                        <li th:text="${'经纪人：'+ (info.keeperName2 ne null and info.keeperName2 ne ''? info.keeperName2:'') + ' '+ (info.sortTel == null or #strings.toString(info.sortTel) eq '' ? info.keeperTel2:info.sortTel)}">经纪人： 常先生 13840383296</li>
                    </ul>
                    <dl>
                        <dt>支付方式： </dt>
                        <dd class="weixin hover">
                            <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/weixin.png" alt="微信" /><i></i></dd>
                        <dd class="alipay">
                            <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/alipay.png" alt="支付宝" /><i></i></dd>
                    </dl>
                    <span>支付金额：20元</span>
                    <a>去支付</a>
                    <!--<span class="x"></span>-->
                </div>
                <div class="page3">
                    <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                    <h1>请使用<span>支付宝</span>扫描下面二维码</h1>
                    <img src="" alt="二维码获取失败" />
                    <span class="x1"></span>
                </div>
                <div class="page4">
                    <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                    <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/success.png" />
                    <b>支付成功</b>
                    <a href="https://my.fangxiaoer.com/payOrder">查看订单</a>
                    <!--<span class="x1"></span>-->
                </div>

            </div>
            <!--          <script type="text/javascript">
                            pay.init()

                        </script>-->
            <!--  <script src="https://static.fangxiaoer.com/js/huanxinjs/js/rentsaleim.js"></script>-->


            <!--页面滚动时出现的头部定位-->
            <div class="bkNavFloat fixbkb">
                <div class="container">
                    <div class="unstyled" style="width: 1170px;margin: 0 auto;">
                        <h4 th:text="${info.title}">城建花园最好户型，价格便宜 4室2厅2卫167㎡</h4>
                        <p th:text="${info.area+'m²， '+(info.fitmentType ne null and info.fitmentType ne '' ? info.fitmentType+'， ' : ' ')+(info.price ne '0' ? info.price+'元/月' : '面议')}">2室2厅1卫，167㎡，90万</p>
                        <!--微信扫码拨号-->
                        <div class="fix-cardTels">
                            <s></s>
                            <span>电话咨询</span>
                            <div class="show-topCardTels">
                                <img id="rent2" src="" alt="">
                                <p>微信扫码拨号</p>
                                <p th:if="${info.keeperLevel2 ne '1'}"><i>快速获取经纪人联系方式</i></p>
                            </div>
                        </div>
                        <!--<div>
                            <i></i>
&lt;!&ndash;                            <p th:text="${info.keeperName2+'  '+((info.sortTel ne '' and info.sortTel ne null) ? info.sortTel : info.keeperTel2)}"></p>&ndash;&gt;
                            <p id="rentHouseOwner"></p>

                        </div>-->
                    </div>
                </div>
            </div>

            <script th:inline="javascript">
                var result = [[${info}]];
                console.log('打印结果',result)

            </script>


            <!--页面向下滚动时 头部出现定位条-->
            <!--<script src="static/2015/0321/js/verify.js" type="text/javascript"></script>-->
            <script th:inline="javascript">
                $(document).ready(function() {
                    $("#peitao").hide();
                    $(".bkNavFloat").removeClass("fixbkb");
                    $(window).scroll(function() {
                        var s = $(this).scrollTop();
                        if (s >= 800) {
                            $(".bkNavFloat").addClass("fixbkb");
                        } else {
                            $(".bkNavFloat").removeClass("fixbkb");
                        }

                    })
                    var sortTel = [[${info.sortTel}]];
                    var keeperTel2 = [[${info.keeperTel2}]];
                    var keeperName2 = [[${info.keeperName2}]];
                    console.log("sortTel"+keeperTel2);
                    keeperName2 += "  "
                    if (sortTel != "" && null != sortTel){
                        keeperName2 += sortTel;
                    }else {
                        keeperName2 += keeperTel2
                    }
                    $("#rentHouseOwner").text(keeperName2)
                    $("#rentKeeperName2").text([[${info.keeperName2}]])
                    $("#rentHouseOwner").css("white-space","pre")
                });

                function classon(index) {
                    var offsetTop = $("#h" + index).offset().top;
                    var scrollTop = $(document).scrollTop();
                    var w_height = $(window).height();
                    var load_height = $("#h" + index).height();

                    //if (scrollTop > offsetTop - w_height && scrollTop < offsetTop + load_height) {
                    if (scrollTop > offsetTop - 50) {
                        $(".bkNavFloat").find("li").removeClass("on");
                        $(".bkNavFloat").find("li:eq(" + index + ")").addClass("on");
                    }
                }
                $(".bkNavFloat").find("li").click(function(e) {
                    $(".bkNavFloat").find("li").removeClass("on");
                    $(this).addClass("on");
                })
                //$(".bkNavFloat a").click(function (e) {
                //    e.preventDefault();
                //    $('html,body').scrollTo(this.hash, this.hash);
                //});
            </script>
            <script>
                var islogin
                $(document).ready(function () {
                    islogin = $("#sessionId").val();
                });
                $("#sayWordsUL li").live("click", function () {
                    turnoffwords_box();
                    var val = $("#talkInputId").val() + $(this).html();
                    $("#talkInputId").val(val);
                });

                function showWordsBox() {
                    $(".wl_faces_box").hide();
                    $(".wl_words_box").fadeIn("slow");
                }
                function turnoffwords_box() {
                    $(".wl_faces_box").hide();
                    $(".wl_words_box").fadeOut("slow");
                }



            </script>


            <div class="disclaimer">
                <strong>免责声明：</strong>房源信息由网站用户提供，其真实性、合法性由信息提供者负责，最终以政府部门登记备案为准。本网站不声明或保证内容之正确性和可靠性，购买该房屋时，请谨慎核查。如该房源信息有误，您可以投诉此房源信息或<strong>拔打举报电话：************</strong>。
            </div>


        </div>
    </th:block>
    <th:block th:if="${#strings.toString(info.isDel) eq '1'}">
        <div class="delList">
            <div class="delListMsg">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/deleteListingsIcon.png" alt="">
                <div>
                    <h4>抱歉，您查看的信息已过期!</h4>
                    <p>建议您继续访问其他页面<a href="/rents/">查看更多租房房源 ></a></p>
                </div>
            </div>
            <div class="delListContList" th:if="${!#lists.isEmpty(same) and same.size() ge 4}">
                <h4>同小区您可能感兴趣的房源</h4>
                <ul>
                    <li th:each="s,i:${same}" th:if="${i.index lt 5}">
                        <a th:href="${'/rent/'+s.houseId+'.htm'}" target="_blank">
                            <img th:src="${#strings.isEmpty(s.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.pic}" th:alt="${s.subName}" />
                            <p th:text="${s.subName}">恒大御峰</p>
                            <div>
                                <p th:if= "${!#strings.isEmpty(s.price) and #strings.toString(s.price) ne '0.0'}">
                                    <span th:text="${#strings.toString(s.price).contains('.')? #strings.toString(s.price).replaceAll('0+?$','').replaceAll('[.]$', '') : s.price}">666</span>元/月
                                </p>
                                <p th:if= "${#strings.isEmpty(s.price) or #strings.toString(s.price) eq '0.0'}"><span>面议</span></p>
                                <span th:text="${#strings.toString(s.BuildArea).contains('.')? #strings.toString(s.BuildArea).replaceAll('0+?$','').replaceAll('[.]$', '')+'㎡' : s.BuildArea+'㎡'}">92m²</span>
                                <span th:text="${s.room+'室'+s.hall+'厅'+s.toilet+'卫'}">2室2厅1卫</span>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="delListContList" th:if="${!#lists.isEmpty(interestHouse) and interestHouse.size() ge 4}">
                <h4>您可能感兴趣的房源</h4>
                <ul>
                    <li th:each="rent,i:${interestHouse}" th:if="${ #strings.toString(rent.houseId) != #strings.toString(houseId) and i.index lt 5}">
                        <a th:href="'/rent/'+${rent.houseId}+'.htm'" target="_blank">
                            <img th:src="${#strings.isEmpty(rent.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : rent.pic}" th:alt="${rent.subName}">
                            <p th:text="${rent.subName}">恒大御峰</p>
                            <div>
                                <p th:if="${#strings.isEmpty(rent.price) or (#strings.toString(rent.price) eq '0.0')}"><span>面议</span></p>
                                <p th:if= "${!#strings.isEmpty(rent.price) and #strings.toString(rent.price) ne '0.0'}">
                                    <span th:text="${#strings.toString(rent.price).contains('.')? #strings.toString(rent.price).replaceAll('0+?$','').replaceAll('[.]$', '') : rent.price}"></span>元/月
                                </p>
                                <span th:text="${#strings.toString(rent.area).contains('.')? #strings.toString(rent.area).replaceAll('0+?$','').replaceAll('[.]$', '')+'㎡' : rent.area+'㎡'}">92m²</span>
                                <span th:text="${rent.room+'室'+rent.hall+'厅'+rent.toilet+'卫'}">2室2厅1卫</span>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </th:block>
    <div class="cl"></div>
    <div th:include="fragment/fragment:: footer_detail" ></div>
    <div th:include="house/detail/fragment_login::login"></div>
    <div th:include="fragment/fragment::tongji"></div>
</form>



<!--风险提醒-->
<div class="riskm">
    <div class="rclose"></div>
    <div class="rkm">
        <h2>风险提醒：</h2>

        <p>本站旨在为广大用户提供更丰富的信息，部分信息由第三方提供，我们持续通过管理手段提升信息的准确度，但<strong>我们无法确保信息的准确性和完整性。房产交易滋事体大，本站信息不应作为您买卖决策的依据，您决策前应与房源业主核实相关信息、并亲自到房屋中核验信息，或以产权证明、政府类网站发布的官方信息为准。本站不对您交易过程中对本网站信息产生的依赖承担任何明示或默示的担保责任或任何责任。</strong></p>

        <h3>请您详细阅读如下声明：</h3>

        <h3>1、关于参考户型图</h3>
        <p>本网呈现的户型图为平台根据第三方提供的内容/数据而非标准的参考户型图，其中户型结构及房屋面积并非按国家标准进行的测绘专业活动取得，户型图与真实现状一定存在差异，我们无法保障户型图准确性和差异率，<strong>户型图仅供参考，不应作为您交易的决策依据，房屋面积的准确信息请您与房源业主核实，并请以产权证明或您委托的专业机构测量结果为准。</strong></p>

        <h3>2、关于房屋装修情况</h3>
        <p>本网房源图片、VR效果图、视频等呈现出的房屋装修情况可能因为拍摄时间、拍摄角度等原因和实际场景存在出入,仅供参考，不应作为您交易的决策依据，请以您在看房时房源的实际装修情况为准。</p>

        <h3>3、关于房屋情况</h3>
        <p>本网展示的房源信息（包括但不限于房屋面积、所在楼层、房屋朝向、房屋用途、建成年代、建筑结构、供暖方式、抵押信息、交易权属）由经纪人/个人提供，<strong>仅供参考不应作为您交易的决策依据，房源的准确信息请您与房源业主核实，并以房本信息、房屋实际情况、您签订房屋买卖合同中披露的信息为准。</strong></p>

        <h3>4、关于房屋周边配套等</h3>
        <p>房源介绍中的周边配套、在建设施、规划设施、地铁信息、绿化率、得房率等内容均系第三方提供，<strong>仅供参考不应作为您交易的决策依据，房屋周边配套请您与房源业主及主管部门核实，并以房本信息、房屋实际情况、您签订房屋买卖合同中披露的信息为准。</strong></p>

        <h3>5、关于距离</h3>
        <p>房源介绍中与距离相关的数据均来源于百度地图。</p>
    </div>
</div>


<script th:inline="javascript">
    /*<![CDATA[*/
    var sortTel = [[${info.sortTel}]];
    var keeperTel2 = [[${info.keeperTel2}]];
    var isDel = [[${info.isDel}]];
    var rentState = [[${info.rentState}]];
    var state = [[${info.state}]];
    $(function () {
        var tels="";
        if(sortTel != null && sortTel != ''){
            tels =sortTel;
        }else{
            if (isDel == 1 || (rentState == 4 && state == 3)) {
                tels = keeperTel2.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
            }else {
                tels = keeperTel2;
            }
        }

        var sessionId = $("#sessionId").val()
        var projectId = $("#projectId").val()
        var linksessionId = '';
        if (sessionId!= ''){
            var pagekey = 'https://sy.fangxiaoer.com/rent/'+projectId+'.htm?sessionId='+sessionId
            $.ajax({
                type: "POST",
                async: false,
                url:  "https://tools.fangxiaoer.com/url/encode",
                data:{'url': pagekey},
                dataType : 'json',
                success: function (data) {
                    console.log(data)
                    linksessionId = (data.content).replace("/",'');
                    console.log(linksessionId)

                }
            });
        }

        var scene = 'tel,' +[[${houseId}]]+',3,' +linksessionId;
        var img = "";
        var sss;
        $.ajax({
            type: "GET",
            async: false,
            url:  "/getWxACode",
            data:{"scene": scene},
            dataType : 'json',
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            success: function (data) {
                img = data.img;
                sss = data;
            }
        });
        $("#rent1").attr("src","data:text/html;base64,"+img);
        $("#rent2").attr("src","data:text/html;base64,"+img);
    });
</script>
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
<script>
    //登录关闭X
    $("#loginClose").click(function () {
        $("#login").hide();
        $(".reportPopupHb").hide();
    });
</script>
<!--2020-07-24 安心好房事件 -->
<script>
    $('.anin_info ,.bubble').mouseenter(function() {
        $('.bubble').show()
    })
    $('.bubble_none').mouseenter(function() {
        $('.bubble').hide()
    })
    $('.anin_info,.bubble').mouseleave(function() {
        $('.bubble').hide()
    })



    //风险提醒
    $(".risk").click(function(){
        $('.reportPopupHb,.riskm').show()

    })
    $(".rclose").click(function(){
        $('.reportPopupHb,.riskm').hide()
    })
</script>
</body>
</html>
