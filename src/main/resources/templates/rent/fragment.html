<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
    <!--列表页右侧我要出租-->
    <div th:fragment="wantrent">
        <div class="rightQzQgBtn">
            <a href="/rentwhole/" target="_blank" style="margin-bottom: 14px"><i></i>我要出租</a>
        <a th:href="@{'/helpSearch?ids=3'}" rel="3" dir="2" target="_blank"><i></i>求租</a>
            <style>
                .rightQzQgBtn a{
                    padding-left: 0 !important;
                }
            </style>
    </div>
    </div>
    <!--租房推荐-->
    <div th:fragment="rentrecommend">
       <!-- <dl class="recommend" th:if="${!#lists.isEmpty(advert.command)}" >
            <dt><span></span>租房推荐</dt>
            <dd th:each="command,i:${advert.command}">
                &lt;!&ndash;判断第一个&ndash;&gt;
                <a th:if="${i.count == 1}">
                    <div class="hideD" style="display: none;">
                        <a th:href="${command.url}" target="_blank">
                            <p th:class="hover"><th:block th:text="${i.index+1}"></th:block></p>
                            <span><th:block th:text="${command.region + command.projectName}"></th:block></span>
                            <b style="width: 75px;"><th:block th:utext="${command.price}"></th:block></b>
                        </a>
                    </div>
                    <a class="hideA" style="display: block" th:href="${command.url}" target="_blank">
                        <img th:src="${command.housePic}" alt="">
                        <span class="hideATitle"><th:block th:text="${command.subName}"></th:block></span>
                        <span><i><th:block th:utext="${command.price}"></th:block></i></span>
                        <p>
                            <span><th:block th:text="${command.room+'室'+command.hall+'厅'+command.toilet+'卫'}"></th:block></span>
                            <span><th:block th:text="${#strings.toString(command.area).contains('.')? #strings.toString(command.area).replaceAll('0+?$','').replaceAll('[.]$', '') : command.area}+'m²'"></th:block></span>
                        </p>

                    </a>
                </a>
            </dd>

            &lt;!&ndash;判断除第一个以外的其他&ndash;&gt;
            <dd th:each="command,i:${advert.command}">
                <a th:if="${i.count &gt; 1}" th:href="${command.url}" target="_blank">
                    <div class="hideD">
                        <a th:href="${command.url}" target="_blank">
                            <p th:class="${i.index &lt; 3?'hover':''}"><th:block th:text="${i.index+1}"></th:block></p>
                            <span><th:block th:text="${command.projectName}"></th:block></span>
                            <b style="width: 75px;"><th:block th:utext="${command.price}"></th:block></b>
                        </a>
                    </div>
                    <a class="hideA" th:href="${command.url}" target="_blank">
                        <img th:src="${command.housePic}" alt="">
                        <span class="hideATitle"><th:block th:text="${command.subName}"></th:block></span>
                        <span><i><th:block th:utext="${command.price}"></i></span>
                        <p><span><th:block th:text="${command.room+'室'+command.hall+'厅'+command.toilet+'卫'}"></th:block></span>
                            <span><th:block th:text="${#strings.toString(command.area).contains('.')? #strings.toString(command.area).replaceAll('0+?$','').replaceAll('[.]$', '') : command.area}+'m²'"></th:block></span>
                        </p>

                    </a>
                </a>
            </dd>
        </dl>
        <script>
            $(".recommend dd").mousemove(function () {
                $(".hideA").hide()
                $(this).find(".hideD").hide()
                $(this).find(".hideA").show()
            })
            $(".recommend dd").mouseleave(function () {
                $(".hideD").show()
                $(this).find(".hideA").show()
            })
        </script>-->
        <div class="groomList" th:if="${!#lists.isEmpty(advert.command)}">
            <h4><i></i>租房推荐</h4>
            <ul>
                <li th:each="command,i:${advert.command}">
                    <a th:href="${command.url}" target="_blank">
                        <img th:src="${command.housePic}" alt="">
                        <div class="imgBottm">
                            <span th:if="${#strings.toString(command.realName) ne null && #strings.toString(command.realName) ne ''}" th:text="${command.realName}">小明</span>
                            <span th:if="${#strings.toString(command.mobile) ne null && #strings.toString(command.mobile) ne ''}" th:text="${command.mobile}">12698456544</span>
                        </div>
                        <div class="oneH">
                            <span th:if="${#strings.toString(command.regionName) ne null && #strings.toString(command.regionName) ne ''}" th:text="${command.regionName}">沈北新区</span>
                            <p th:if="${#strings.toString(command.subName) ne null && #strings.toString(command.subName) ne ''}" th:text="${command.subName}">华强城</p>
                        </div>
                        <div class="twoH">
                            <p><span th:text="${command.room+'室'+command.hall+'厅'+command.toilet+'卫'}">2室2厅1卫</span><span th:text="${#strings.toString(command.area).contains('.')? #strings.toString(command.area).replaceAll('0+?$','').replaceAll('[.]$', '') : command.area}+'m²'"> 86.6㎡</span></p>
                            <div><span th:utext="${command.price}">555</span><i></i></div>
                        </div>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <!--画中画广告-->
    <div th:fragment="rentadvert">
        <div th:if="${!#lists.isEmpty(advert.focus)}"  class="picture_advert">
            <a th:each="focus,i:${advert.focus}" th:href="${focus.url}" target="_blank" class="shop_ad_pic_url">
                <img width="100%" th:src="${focus.image}" th:alt="${focus.projectName}" style="margin-bottom: 20px">
            </a>
        </div>
    </div>
</body>