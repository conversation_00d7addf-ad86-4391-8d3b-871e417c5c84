<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${subMath.subName}+${seoTitle } + '房价_沈阳房价_'+ ${subMath.subName}+ ${seoTitle } + '房价查询 - 房小二网'"></title>
    <meta name="keywords" th:content="${subMath.subName}+ '房价走势,' + ${subMath.subName} + '房价,沈阳房价查询,'+${subMath.subName}+'房价评估'"/>
    <meta name="description"
          th:content="'房小二网为您提供精准的沈阳' + ${subMath.subName} +'房价评估，援引房小二网沈阳房源大数据，实时更新，楼盘全面，您也可以拨打客服热线获得房产专家的直接指导评估，联系电话：400-893-9709'"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/checkHR.css?v=20200324" />
<!--    <link rel="stylesheet" type="text/css" href="/css/checkHR.css"/>-->
    <script src="https://static.fangxiaoer.com/js/highcharts6.0.7.js"></script>
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/trendChart.css"/>
</head>


<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=20,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2018" th:include="fragment/fragment::housingpriceSearch" th:with="type=7"></div>
<div class="checkHR">
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/housingprice/"target="_blank">沈阳房价</a></div>
    <!--筛选结果小区-->
    <div class="filterResult">
            <div class="filterResultL" th:if="${!#strings.isEmpty(subMath)}">
            <h1><span id="subNames" th:text="${subMath.subName}">沈阳</span><p th:text="${subMath.room+' '+'/'+' '+subMath.area+'m²'+' '+'/'+' '+subMath.forwardType+' '+'/'+' '+subMath.currentFloor+'层'+' '+'(共'+subMath.totalFloor+'层)'}">2室   /  100m2   /  南北   /  20层(共28层)</p></h1>
            <ul>
                <li>
                    <p><b th:text="${#strings.toString(subMath.totalPrice) eq '0' ? '' : subMath.totalPrice}">8421</b><th:block th:text="${#strings.toString(subMath.totalPrice) eq '0' ? '暂无数据' : '万'}"></th:block></p>
                </li>
                <li>
                    <p><b th:text="${#strings.toString(subMath.calPrice) eq '0' ? '' : subMath.calPrice}">8421</b><th:block th:text="${#strings.toString(subMath.calPrice) eq '0' ? '暂无数据' : '/m²'}"></th:block></p>
                </li>
            </ul>
<!--            <div class="newOrder">-->
<!--                <p>如需了解更多精准的评估价格，请您留下您的号码，稍后评估专家回复您，免费提供评估服务</p>-->
<!--                <div>-->
<!--                    <label for="">手机号码</label>-->
<!--                    <input type="text" placeholder="输入手机号码" id="userPhoneNum"  maxlength="11" oninput="displayErrorInfo()"/>-->
<!--                    <input type="button" name=""  value="精准评估" class="newOrderBtn" id="submitPhoneNum"/>-->
<!--                    <input type="hidden" name=""  th:value="${subMath.subName+subMath.room+subMath.area+'m²'+subMath.forwardType}"  id="parameter"/>-->
<!--                </div>-->
<!--                <p>*为了您的权益，您的隐私将被严格保密</p>-->
<!--            </div>-->
            <span id = "inputerror" class="help_search_house_error" style="display:none"></span>
<!--                <p class="filPrice">-->
<!--                    <span>-->
<!--                        <i >37</i>万-->
<!--                    </span>-->
<!--                    <span>-->
<!--                        <i>8280</i>m²-->
<!--                    </span>-->
<!--                </p>-->
        </div>
        <div class="filterResultR">
            <a th:href="${'/static/saleHouse/saleHouse.htm'}" class="resultRIcon resultRIcon1" target="_blank">我要卖房</a>
            <!--<a href="javascript:publishSecon();" class="resultRIcon resultRIcon1" >我要卖房</a>-->

            <a th:href="${'/helpSearch?ids=1'}" class="resultRIcon resultRIcon2" target="_blank">我要买房</a>
<!--            <a th:href="${'/saleVillages/'+subId+'/plotSecondVillage/-v'+subId}" class="resultRIcon resultRIcon2" target="_blank">我要买房</a>-->
            <a th:href="${'/saleVillages/'+subId+'/index.htm'}" class="resultRIcon resultRIcon3" target="_blank">小区详情</a>
        </div>
    </div>
    <script th:inline="javascript">
        function publishSecon(){
            var subNames = $("#subNames").text();
            var subId = [[${subId}]];
            var area = [[${subMath.area}]];
            var totalPrice = [[${subMath.totalPrice}]];
            var currentFloor = [[${subMath.currentFloor}]];
            var totalFloor = [[${subMath.totalFloor}]];

            url="/secondPublish?subNames="+subNames +"&area="+area +"&totalPrice="+totalPrice
                    +"&currentFloor="+currentFloor+"&totalFloor="+totalFloor+"&subId="+subId;
            window.open (url);
        }
    </script>
    <!--房价走势图-->
     <div class="areaChartimg">
        <h4 class="checkTitle" th:text="${subMath.subName+'房价走势'}">皇寺广场二手房房价走势图</h4>
         <div class="trendCharContent">
             <dl>
                 <dt>
                 <div class="trendChartTitle"><i  style="background: #75d88f;float:left;margin-top: 7px;"></i><div id = "community_Name">城建花园</div></div>
                 <div class="trendChartPrice" th:if="${v.unitPrice ne 0.0}"><b  th:text="${'￥ '} +${#strings.toString(v.unitPrice).contains('.') ?  #strings.toString(v.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', '')+ '元/㎡' : v.unitPrice+ '元/㎡'}">￥8652元/㎡ </b>
                     <span  th:if="${#strings.toString(v.increaseRate) eq '0.0'}" th:text="${'----'}" style="font-weight: normal"></span>
                     <span  th:if="${#strings.toString(v.increaseRate).substring(0,1) ne '-' and #strings.toString(v.increaseRate) ne '0.0'}" th:text="${'比本小区环比高'+v.increaseRate+'%'}" style="font-weight: normal">比本小区环比高0.38%</span>
                     <span class="dis"  th:if="${#strings.toString(v.increaseRate).substring(0,1) eq '-'}" th:text="${'比本小区环比低' + #strings.toString(v.increaseRate).substring(1)+'%'}">比本小区环比高0.38%</span>
                 </div>

                 <div class="trendChartPrice" th:if="${v.unitPrice eq 0.0}"><b th:text="----"></b>
                 </div>
                 </dt>
                 <dd>
                     <div class="trendChartTitle" style="overflow: hidden;"><i style="background: #8cc9d5;float: left;margin-top: 7px;"></i><div id ="region_name" style="float: left">铁西</div> </div>
                     <div class="trendChartPrice"><b>￥8652元/㎡ </b><span>比本小区环比低0.38%</span></div>
                 </dd>
                 <dd>
                     <div class="trendChartTitle" style="overflow: hidden;"><i style="float: left;margin-top: 7px;"></i><div id = "city_name" style="float: left">沈阳</div> </div>
                     <div class="trendChartPrice"><b>￥8652元/㎡ </b><span>比本小区环比高0.38%</span></div>
                 </dd>
             </dl>
             <div class="vs">vs</div>
         </div>
    <ul class="trendCharTime">
    </ul>
    <div class="cl"></div>
    <ul class="trendCharMap">
        <li>
            <div id="areaHighCharts"  style="width:1100px;height:400px;margin:  0 auto"></div>
        </li>
    </ul>
</div>

    <script th:inline="javascript">
        var highChartsData = [[${data}]];
        console.log(highChartsData);
        var msg1 = JSON.parse(highChartsData[0].highChartsData);
        var msg2 = JSON.parse(highChartsData[1].highChartsData);
        var msg3 = JSON.parse(highChartsData[2].highChartsData);
        $("#community_Name").text(highChartsData[2].keyName);
        if(msg3.status == 0 || msg3.length == 0){
            $(".trendCharContent dl dt div b").text("暂无资料");
        }else{
            $(".trendCharContent dl dt div b").text("￥ "+msg3[0].unitPrice +"元/m²");
        }
        $("#region_name").text(highChartsData[1].keyName);
        if(null != msg2 && msg2.length == 0) {
            $(".trendCharContent dl dd:eq(0) div b").text("暂无资料");
            $(".trendCharContent dl dd:eq(0) div span").text("");
        }else{
            $(".trendCharContent dl dd:eq(0) div b").text("￥ " + msg2[1].unitPrice + "元/m²");
            if (null != msg2 && msg2[0].unitPrice - msg2[1].unitPrice > 0) {
                $(".trendCharContent dl dd:eq(0) div span").text("环比上周高" + ((msg2[0].unitPrice - msg2[1].unitPrice) / msg2[1].unitPrice * 100).toFixed(2) + "%");
            } else {
                $(".trendCharContent dl dd:eq(0) div span").text("环比上周低" + (-(msg2[0].unitPrice - msg2[1].unitPrice) / msg2[1].unitPrice * 100).toFixed(2) + "%");
                $(".trendCharContent dl dd:eq(0) div span").addClass("dis");
            }
        }
        $("#city_name").text(highChartsData[0].keyName);
        $(".trendCharContent dl dd:eq(1) div b").text("￥ "+msg1[0].unitPrice+"元/m²");
        if (msg1[0].unitPrice - msg1[1].unitPrice > 0) {
            $(".trendCharContent dl dd:eq(1) div span").text("环比上周高" + ((msg1[0].unitPrice - msg1[1].unitPrice) / msg1[1].unitPrice * 100).toFixed(2) + "%");
        } else {
            $(".trendCharContent dl dd:eq(1) div span").text("环比上周低" + (-(msg1[0].unitPrice - msg1[1].unitPrice) / msg1[1].unitPrice * 100).toFixed(2) + "%");
            $(".trendCharContent dl dd:eq(1) div span").addClass("dis");
        }
        var length = 0;
        if(msg3.content != null ){
            length = msg1.length <= msg3.length ? msg1.length : msg3.length;
        }else if(msg3.length < msg1.length){
            length = msg3.length;
        }else{
            length = msg1.length;
        }
        var length2 = msg2.length ;
        var length1 = msg1.length ;
        var categories = new Array();
        var data1 = new Array();
        var data2 = new Array();
        var data3 = new Array();
        var temp = '';
        if(msg1.length != 0 && msg2.length != 0 && msg3.length != 0){
            for (i = 0; i < length; i++) {
//                if (i % 4 == 0) {
                    var inArray = Highcharts.dateFormat('%y年%m月', msg1[i].trendingTime);
                    if(temp != inArray) {
                        temp = inArray;
                        categories.push(inArray);
                        data1.push(msg1[i].unitPrice);
                        if(null != msg2[i]){
                            data2.push(msg2[i].unitPrice);
                        }
                        if(msg3.length > 0 && i < msg3.length){
                            data3.push(msg3[i].unitPrice)
                        }
                    }
//                }
            }
        }else if(msg1.length == 0 || msg2.length == 0 || msg3.length == 0){
            for (i = 0; i <length1; i++) {
                var trendTime = Highcharts.dateFormat('%y年%m月', msg1[i].trendingTime);
                if(temp != trendTime){
                    temp = trendTime;
                    categories.push(trendTime);
                    data1.push(msg1[i].unitPrice);
                }
            }
            for (i = 0; i <length2; i++) {
                var trendTime = Highcharts.dateFormat('%y年%m月', msg2[i].trendingTime);
                if(temp != trendTime){
                    temp = trendTime;
                    categories.push(trendTime);
                    data2.push(msg2[i].unitPrice);
                }
            }
            for (i = 0; i <length; i++) {
                var trendTime = Highcharts.dateFormat('%y年%m月', msg3[i].trendingTime);
                if(temp != trendTime){
                    temp = trendTime;
                    categories.push(trendTime);
                    data3.push(msg3[i].unitPrice);
                }
            }
        }



        var series = [{
            name: highChartsData[0].keyName,
            data: data1.reverse(),
        }, {
            name: highChartsData[1].keyName,
            data: data2.reverse(),
        }, {
            name: highChartsData[2].keyName,
            data: data3.reverse(),
        }]
        //            var categories=['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        categories = categories.reverse();
        highchart('areaHighCharts', series, categories)
        //            highchart('container2',series,categories)
        //            highchart('container3',series,categories)


        function highchart(id, series, categories) {
            Highcharts.chart(id, {
                title: {
                    text: false
                },
                yAxis: {
                    title: {
                        text: false
                    },
                    labels: {
                        formatter: function () {
                            return this.value + '元/m²';
                        }
                    }
                },
                xAxis: {
                    categories: categories
                },
                legend: {
                    enabled: false
                },
                tooltip: {
                    crosshairs: true,
                    shared: true,
                    pointFormat: '<tspan style="fill:{series.color}" x="8" dy="15">●</tspan> <b>{series.name}</b> {point.y} 元/m²<br>'
                },
                plotOptions: {
                    spline: {
                        marker: {
                            radius: 4,
                            lineColor: '#666666',
                            lineWidth: 1
                        }
                    }
                },
                series: series,
                responsive: {
                    rules: [{
                        condition: {
                            maxWidth: 500
                        },
                        chartOptions: {
                            legend: {
                                layout: 'horizontal',
                                align: 'center',
                                verticalAlign: 'bottom'
                            }
                        }
                    }]
                },
                colors: ['#fe7175', '#8cc9d5', '#75d88f']
            });
        }
    </script>

    <!--在售房源-->
    <div class="hotVillage"  th:if="${!#lists.isEmpty(subMath)  and #lists.size(subMath.houses) > 0}">
        <h4 class="checkTitle" th:text="${subMath.subName+'在售房源'}"></h4>
        <ul>
            <div class="hotVillageTitle">
                <p><a th:href="${'/saleVillages/'+subId+'/index.htm'}" target="_blank"  th:text="${subMath.subName}">华润置地凯旋门</a></p>
                <p th:if= "${#strings.toString(subMath.unitPrice) eq null || #strings.toString(subMath.unitPrice) eq '0' || #strings.toString(subMath.unitPrice) eq ''}" th:text="${'暂无数据'}"></p>
                <p th:if= "${!#strings.isEmpty(subMath.unitPrice) and #strings.toString(subMath.unitPrice) ne '0'}" th:text="${#strings.toString(subMath.unitPrice).contains('.')?#strings.toString(subMath.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):subMath.unitPrice}+'元/m²'"
                   th:style="${!#strings.isEmpty(subMath.unitPrice) and #strings.toString(subMath.unitPrice) ne '0.0'}?'display:block':'display:none'">
                </p>
                <p th:if="${#strings.toString(subMath.rate) ne '0.0'}">
                    环比上周
                    <i class="ringRatioNumRed" th:class="${#strings.toString(subMath.rate).substring(0,1) eq '-' ? 'green' : 'red'}" >
                        <em th:text="${#strings.toString(subMath.rate).substring(0,1) eq '-' ? '  ↓  ' : '  ↑  '}"></em>
                        <span th:text="${#strings.toString(subMath.rate).replace('-','')+'%'}"></span>
                    </i >
                </p >
                <a th:if="${#lists.size(subMath.houses) == 4}" th:href="${'/saleHouses/-v'+subId}" class="seeMoreBtn" target="_blank">更多房源></a>
            </div>
            <li th:if="${!#strings.isEmpty(subMath.houses)}" th:each="house:${subMath.houses}">
                <a th:href="${'/salehouse/'+house.houseId+'.htm'}" target="_blank">
                    <img th:src="${#strings.isEmpty(house.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':house.pic}" th:alt="${house.title}"/>
                    <!--<div class="" th:if="${house.isXiQue eq '1'}">佣金95折</div>-->
                </a>
                <div>
                    <h4><a th:href="${'/salehouse/'+house.houseId+'.htm'}" target="_blank"  th:text="${'['+house.regionName+'-'+house.plateName+']'+house.subName}">[和平 - 太原街]金城花园</a></h4>
                    <p>
                    <th:block th:text="${house.room+'室'+house.hall+'厅'+house.toilet+'卫'}"></th:block>
                    <th:block th:text="${#strings.toString(house.area).contains('.')? #strings.toString(house.area).replaceAll('0+?$','').replaceAll('[.]$', '') : house.area}+'m²'"></th:block>
                    </p>
                </div>
                <p th:if="${#strings.isEmpty(house.price) or (#strings.toString(house.price) eq '0.0')}"  th:text="${'面议'}"></p>
                <p th:if= "${!#strings.isEmpty(house.price) and #strings.toString(house.price) ne '0.0'}" >
                    <th:block th:text="${#strings.toString(house.price).contains('.')? #strings.toString(house.price).replaceAll('0+?$','').replaceAll('[.]$', '') : house.price}"></th:block>
                    <i th:text="${#strings.isEmpty(house.price) or (#strings.toString(house.price) eq '0.0')?'':'万'}"></i>
                </p>
            </li>
        </ul>
    </div>
</div>
<script>
    function displayErrorInfo(){
        document.getElementById("inputerror").style.display="none";
    }


</script>
<!--点击精准评估后的弹窗-->
<div th:include="house/detail/fragment_order::search_housePrice_popup"></div>

<!--右侧浮标-->
<div th:include="fragment/fragment::esfCommonFloat"></div>
<!--底部-->
<div class="footer" th:include="fragment/fragment::footer_detail"></div>
<div th:include="fragment/fragment::tongji"></div>
<style>
    .Agreement{
        color: #999999;
    }
    .bnzfTC_box .Agreement i {
        display: block;
        width: 20px;
        height: 20px;
        border: 0px solid #ededed;
        display: block;
        cursor: pointer;
        float: left;
        margin-left: 43px;
        margin-right: 7px;
        display: block;
        background-size: 12px 12px;
        width: 12px;
        height: 12px;
        border-radius: 12px;
        margin-top: 6px;
        margin-right: 10px;
        cursor: pointer;
        background: url(https://static.fangxiaoer.com/m/static/images/checkimgYuan.png) top center;
        background-size: 100% 100%;
    }
    .bd {
        background: url(https://static.fangxiaoer.com/web/images/ico/sign/select.png)no-repeat;
        background-size: 100% 100%;
        background: url(https://static.fangxiaoer.com/m/static/images/checkimgChecked.png) top center !important;
        background-size: 100% 100% !important;
        border: 0 !important;
    }
</style>
</body>
</html>