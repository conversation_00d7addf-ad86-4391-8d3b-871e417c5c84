<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${seoTitle } + '房价_沈阳房价_沈阳' + ${seoTitle } + '房价查询 - 房小二网'"></title>
    <meta name="keywords" th:content=" ${seoTitle } + '楼盘房价,沈阳' + ${seoTitle } + '房价,沈阳房价查询,小区房价评估'"/>
    <meta name="description"
          th:content="'房小二网为您提供精准的沈阳' + ${seoTitle } +'房价评估，援引房小二网沈阳房源大数据，实时更新，楼盘全面，您也可以拨打客服热线获得房产专家的直接指导评估，联系电话：400-893-9709'"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
    <script src="https://static.fangxiaoer.com/js/baiduMap/ajax.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/baiduMap/baiduMap1.css" />
    <script src="https://static.fangxiaoer.com/js/baiduMap/vue.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/baiduMap/subWayLine.js" type="text/javascript" charset="utf-8"></script>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>

    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/trendChart.css"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/checkHR.css?v=20180628"/>
    <script src="https://static.fangxiaoer.com/js/highcharts6.0.7.js"></script>

    <style>
        #option li.leibie {
            margin-bottom: 20px;
            width: 1100px;
            margin-left: 10px
        }

        #option .fenlei a {
            margin-right: 0px
        }

        #option li.leibie a {
            margin-right: 5px
        }

        /*搜索下拉*/

        /*.ac_results{width: 347px !important;}*/
        .ac_results ul {
            width: 100%;
            list-style-position: outside;
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .ac_results {
            padding: 0px;
            border: 1px solid #c1c1c1;
            background-color: white;
            overflow: hidden;
            z-index: 99999;
            width: 347px !important;
        }
        .ac_results li {
            margin: 0px;
            padding: 0px 5px;
            cursor: default;
            display: block;
            text-align: left;
            font: menu;
            line-height: 40px;
            overflow: hidden;
            background: #fff;
            font-size: 14px;
        }

        .ac_loading {
            background: white url('/images/indicator.gif') right center no-repeat;
        }

        .ac_odd {
            background-color: #eee;
        }

        .ac_over {
            background-color: #f3f3f3 !important;
            color: #333;
        }
        .showHouse b, .showsubWay b{
            display: block;}
        .showHouse{display: block;padding: 5px 10px !important;height: 40px}
        .showHouse div, .showsubWay div{background: none}
        .showPlat{width: 80px !important;height: 76px !important;padding-top: 4px !important;}
        .showPlat span, .showRegion span{width: 70px !important;margin-left: 0 !important;white-space: pre-line; padding: 7px 5px 0 5px;overflow: hidden;white-space: pre;text-overflow: ellipsis;}
        .showRegion{padding-top: 7px;height: 73px}
        .showPlat b, .showRegion b{width: 56px;display: block;margin: 0 auto;line-height: 15px;font-size: 14px;font-weight: normal;}
        .footer{ margin-top: 0 !important;}
    </style>
<body>

<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=20,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2018" th:include="fragment/fragment::housingpriceSearch" th:with="type=7"></div>
<div class="main">
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/housingprice/"target="_blank">沈阳房价</a>

    </div>
    <!--区域筛选-->
    <div id="option">
        <ul>
            <li class="fenlei">
                <a id="btnRegion" th:each="r,i:${region}" onclick="showIndex(this)" th:if="${i.index eq 0 }"
                   th:href="${r.url}" class="">
                    <b class="listIcon listIcon_dw"></b>区域<i></i></a>
                <!--区域-->
                <!--<li id="Search_zf" class="leibie" style="display: none">-->
                <a th:if="${r.id ne '16'}" th:each="r:${region}" th:href="${r.url}" onclick="showIndex(this)" th:id="'r'+${r.id}"
                   th:class="${r.selected}? 'hover':''">
                    <th:block th:text="${#strings.toString(r.name).replaceAll('全部','不限')}"></th:block>
                    <i></i></a><br>
            </li>
            <!--<li th:if="${plate}" class="leibie">
                <a th:each="j:${plate}" th:text="${#strings.toString(j.name).replaceAll('全部','不限')}"
                   onclick="showIndex(this)" th:href="${j.url}" th:id="'j'+${j.id}"
                   th:class="${j.selected}? 'hover':''"><i></i></a>
            </li>-->
            <!--</li>-->
            </li>
        </ul>
    </div>
    <div class="checkHR">
        <div class="areaFilter">
            <div class="areaFilterL">
                <span th:if="${!#strings.isEmpty(housePriceList) and !#lists.isEmpty(housePriceList)}"><h1 th:text="${housePriceList.name}"></h1></span>
                <ul>
                    <li>
                        <h4>本周二手房均价</h4>
                        <p><b class="thisWeek"></b></p>
                        <p class="jian"><i class="rate"></i></p>
                    </li>
                    <li>
                        <h4>上周二手房均价</h4>
                        <p class="lastUnit"><b class="lastWeek"></b></p>
                        <p>更多 <a th:href="${'/saleHouses/'+pathParm}" target="_blank">二手房房源</a>更多<a th:href="${'/rents/'+pathParm}" target="_blank">租房房源</a></p>
                    </li>
                </ul>
            </div>
            <div class="areaFilterR">
                <!--<form name="form" action="/housingpriceDetail" method="post" target="_blank" id="subForm">-->
                    <h4>请填写您的房产信息进行评估</h4>
                    <ul>
                        <li>
                            <label for="">小区名称</label>
                            <input type="hidden" id="SubID" name="subId"/>
                            <input type="text" id="SubName" placeholder="输入您的小区名称" class="serachHouseInfo"/>
                            <input type="hidden" id="SubNameReally"/>
                            <i id="SubName123" class="ljz"></i>
                        </li>
                        <li>
                            <label for="">房屋面积</label>
                            <input type="text"  placeholder="输入房屋面积" name="area" id="area" />
                            <i>m²</i>
                        </li>
                        <li>
                            <div class="w50">
                                <label for="">户&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;型</label>
                                <select id="roomId" name="roomId">
                                    <option th:value="''">请选择</option>
                                    <option th:each="layout : ${layout}" th:value="${layout.id}"
                                            th:text="${layout.name}">
                                    </option>
                                </select>

                            </div>
                            <div class="w50">
                                <label for="">朝&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;向</label>
                                <select name="forwardType" id="forwardType">
                                    <option th:value="''">请选择</option>
                                    <option th:each="forward: ${forward}"  th:value="${forward.id}"
                                            th:text="${forward.name}">
                                    </option>
                                </select>

                            </div>
                        </li>
                        <li>
                            <div class="w50">
                                <label for="">所在楼层</label>
                                <input type="text" placeholder="请输入" name="currentFloor" id="currentFloor" onkeyup="value=value.replace(/[^\- \d]/g ,'')"/>
                                <i>层</i>
                            </div>
                            <div class="w50">
                                <label for="">总楼层数</label>
                                <input type="text"  placeholder="请输入" name="totalFloor" id="totalFloor" onkeyup="value=value.replace(/[^\d]/g,'')"/>
                                <i>层</i>
                            </div>
                        </li>
                        <a href="javascript:checkForm();" class="checkBtn">免费评估</a>
                    </ul>
                <!--</form>-->
            </div>
        </div>
        <script type="text/javascript">
            $("#r i").hide();
            function checkForm() {
                var subId = $("#SubID").val();
                var SubName = $("#SubName").val();
                var SubNameReally = $("#SubNameReally").val();
                var currentFloor = $("#currentFloor").val();
                var totalFloor = $("#totalFloor").val();
                var area = $("#area").val();
                var roomId = $("#roomId").val();
                var forwardType = $("#forwardType").val();
                var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;//验证数字（保留小数点后两位）
                if(subId == '' || subId == null || subId == undefined || subId == 0){
                    alert("请输入小区名称");
                    return;
                }
                if(SubName == '' || SubName == null ||SubName == undefined){
                    alert("请输入小区名称");
                    return;
                }
                if(!area){
                    alert("请输入房屋面积");
                    return;
                }else if(!reg.test(area)){
                    alert("请输入正确房屋面积");
                    return;
                }else if(eval(area) < 1 || eval(area) > 9999){
                    alert("房屋面积区间为1—9999");
                    return;
                }
                if(!roomId || !forwardType){
                    alert("请选择户型或朝向");
                    return;
                }
                if(eval(currentFloor) < 1|| eval(currentFloor) > 99 || eval(currentFloor) == 0) {
                    alert("所在楼层区间为1—99(不能为0)");
                    return ;
                }
                if(eval(totalFloor) < 1 || eval(totalFloor) > 99) {
                    alert("总楼层数区间为1—99");
                    return ;
                }
                if(!currentFloor){
                    alert("请输入所在楼层");
                    return;
                }
                if(!totalFloor){
                    alert("请输入总楼层数");
                    return;
                }
                if (parseInt(currentFloor) > parseInt(totalFloor)){
                    alert("当前楼层不能大于总楼层");
                    return ;
                }
                if(SubNameReally != SubName){
                    alert("未选择匹配小区，请重新输入");
                    return ;
                }
              //  $("#subForm").submit();
                url="/housingpriceDetail?subId="+subId +"&area="+area +"&roomId="+roomId
                        +"&forwardType="+forwardType+"&currentFloor="+currentFloor+"&totalFloor="+totalFloor;
                window.location.href = url;
            }
        </script>
        <!--<script type="text/javascript" src="/js/jquery.autocomplete.js"></script>-->
        <!--搜索框自动提示-->
        <script th:inline="javascript">
            $(function () {
                autoSearch("SubName");

                function autoSearch(searchId) {
                    $("#" + searchId).autocomplete("/searchs", {
                        multiple: false,
                        max: 15,
                        delay:800, //延迟800毫秒
                        parse: function (data) {
                            return $.map(eval(data), function (row) {
                                return {
                                    data: row,
                                    value: row.subName,
                                    id: row.subId,
                                    result: row.subName
                                };
                            });
                        },
                        formatItem: function (item) {
                            return item.subName;
                        }
                    }).result(function (e, item) {
                        $("#SubID").attr("value", item.subId);
                        $("#SubNameReally").attr("value", item.subName);
                    });
                }
            });
        </script>

        <input id="longtitude" th:value="${housePriceList.longtitude}" type="hidden">
        <input id="latitude" th:value="${housePriceList.latitude}" type="hidden">
        <input id="zoomLevel" th:value="${#strings.toString(housePriceList.zoomLevel).replaceAll('.0','')}" type="hidden">
        <!--地图-->
      <!--  <div class="areaMap" style="position: relative;">
            &lt;!&ndash;地图描点&ndash;&gt;
            <i id="areaMap" style="position: absolute;top: -360px;"></i>
            <h4 class="checkTitle" th:text="${housePriceList.name +'二手房房价地图'}"></h4>
            <form name="form1" method="post" action="" id="form1">
                <div id="map" v-bind:style="{height: height+'px',width:windowWidth+'px'}">
                    <div id="baiduMap" v-bind:style="{width:mapWidth+'px'}">
                    </div>
                </div>
                <div class="hint" id="hint" style="display: none;">
                    <dl>
                        <dd><p>您的浏览器版本过低，<br>建议升级浏览器！</p></dd>
                        <dd>
                            <img src="https://static.fangxiaoer.com/web/images/sy/map/info.png"/>
                        </dd>
                        <dd>
                            <a href="/static/oldSaleMap.htm">查看低版本地图</a>
                            <a href="https://pc.uc.cn/" target="_blank" style="color: #ff5200;" id="Dherf">升级浏览器</a>
                        </dd>
                    </dl>
                </div>
                <script>
                    if(navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > -1 && !(navigator.userAgent.indexOf("Opera") > -1) ){
                        document.getElementById("hint").style.display="block";
                    }
                </script>
                <script src="/js/housingprice/baiduMapForPrice.js" type="text/javascript" charset="utf-8"></script>
            </form>
        </div>
-->
        <!--房价走势图-->
        <div class="areaChartimg" style="display: none">
            <h4 class="checkTitle" th:text="${housePriceList.name +'房价走势'}"></h4>
            <div id="areaHighCharts" style="max-width:1100px;height:400px;margin:  0 auto"></div>
            <input type="hidden" id="names" th:value="${housePriceList.name}">
            <input type="hidden" id="chooseType" th:value="${chooseType}">
            <script>
                var names = $("#names").val();
                var chooseType = $("#chooseType").val();
                var city = '';
                var region = '';
                var innerRegion = '';
                if (chooseType == 3) {
                    innerRegion = names;
                } else if (chooseType == 2) {
                    region = names;
                } else {
                    city = names;
                }

                $.ajax({
                    type: "POST",
                    data: {innerRegion: innerRegion, region: region, city: city},
                    url: "/bigdata",
                    success: function (data) {
                        //console.log(data);
                        chooseName = data.keyName;
                        if (chooseName == null || chooseName == '') {
                            chooseName = names;
                        }
                        if (data) {
                            var charResult = JSON.parse(data.highChartsData);
                            var data1 = new Array();
                            var categories = new Array();
                            var temp = '';
                            for (i = 0; i < charResult.length; i++) {
                                var trendTime = Highcharts.dateFormat('%y年%m月', charResult[i].trendingTime);
                                if(temp != trendTime){
                                    temp = trendTime;
                                    categories.push(trendTime);
                                    data1.push(charResult[i].unitPrice);
                                }
                            }
                            data1.reverse();
                            var series = [{
                                name: chooseName,
                                data: data1
                            }]
                            //环比上周百分比
                            var thWeek = '----';
                            var laWeek = '----';
                            var rat = '----';
                           //价格与环比无数据时显示内容
                            if(data1.length == 0 ){
                              $(".thisWeek").text(" ").append("<p>暂无资料</p>");
                              $(".lastWeek").text(" ").append("<p>暂无资料</p>");
                              $(".rate").prepend("<p>----</p>").append(" ");
                              $(".areaChartimg").text(" ");
                            }else{
                                thWeek = charResult[1].unitPrice;//本周二手房均价
                                laWeek = charResult[2].unitPrice;//data1[data1.length - 2];//上周均价
                                rat = ((thWeek - laWeek) / laWeek * 100).toFixed(2);
                                $(".thisWeek").text(thWeek);
                                $(".lastWeek").text(laWeek);
                                // $(".rate").text(Math.abs(rat) + '%');
                                $(".thisWeek").append("<p>元/m²</p>");
                                $(".lastWeek").append("<p>元/m²</p>");
                                // $(".rate").prepend("<p>环比上周</p>");
                            }
//                            console.log(data1[data1.length - 1]);
//                            console.log(data1[data1.length - 2]);
//                            console.log(data1.slice(-2));//获取数组倒数两个元素
                            $(document).ready(function (){
                                if(eval(thWeek) > eval(laWeek)){
                                    // $(".rate ").prepend("  ↑  ");
                                    // $(".rate").addClass("red");

                                }else if(eval(thWeek) < eval(laWeek)){
                                    $(".rate").prepend("  ↓  ");
                                    $(".rate").addClass("green");

                                }else if(eval(thWeek) - eval(laWeek) == 0){
                                    $(".rate").text("");
                                    $(".rate").prepend("<p>----</p>");
                                }else{
                                    $(".rate").prepend("");
                                }
                            });
                            var chart = Highcharts.chart('areaHighCharts', {
                                title: {
                                    text: ' '
                                },
                                subtitle: {
                                    text: ' '
                                },
                                xAxis: {
                                    type: 'datetime',
                                    dateTimeLabelFormats: {
                                        day: '%Y-%m'
                                    },
                                    categories: categories.reverse()
                                },
                                yAxis: {
                                    title: {
                                        text: ''
                                    },
                                    labels: {
                                        formatter: function () {
                                            return this.value + '元/m²';
                                        }
                                    }
                                },
                                legend: {
                                    layout: 'vertical',
                                    align: 'right',
                                    verticalAlign: 'middle',
                                    enabled: false  //右侧显示按钮
                                },
                                tooltip: {
                                    shared: true,
                                    crosshairs: true,
                                    pointFormat: '<tspan style="fill:{series.color}" x="8" dy="15"></tspan> <b>{series.name}</b><br> 参考均价:{point.y} 元/m²<br>',
                                    dateTimeLabelFormats: {
                                        day: '%Y-%m'
                                    }
                                },
                                series: series,
                                colors:['#FF5200'],

                                responsive: {
                                    rules: [{
                                        condition: {
                                            maxWidth: 500
                                        },
                                        chartOptions: {
                                            legend: {
                                                layout: 'horizontal',
                                                align: 'center',
                                                verticalAlign: 'bottom'
                                            }
                                        }
                                    }]
                                }
                            });
                        }

                    }
                });
            </script>
        </div>
    </div>
</div>

<!--热门小区-->
<div class="hotVillage" th:if="${!#lists.isEmpty(housePriceList.subList)  and #lists.size(housePriceList.subList) > 0 and !#lists.toList(#lists.toList(housePriceList.subList).get(0).houses).isEmpty() }">
    <!--<div class="hotVillage" th:if="${!#strings.isEmpty(housePriceList.subList)  and !#lists.toList(#lists.toList(housePriceList.subList).get(0).houses).isEmpty() }">-->
    <h4 class="checkTitle" th:text="${housePriceList.name +'热门小区'}"></h4>
    <div th:each="hp:${housePriceList.subList}">
        <ul th:each="houses,h:${hp.houses}" th:if="${h.index lt 1}">
            <div class="hotVillageTitle">
                <p><a th:href="${'/saleVillages/'+hp.subId+'/index.htm'}" target="_blank" th:text="${houses.subName}">华润置地凯旋门</a></p>
                <p th:if="${!#strings.isEmpty(hp.unitPrice) and #strings.toString(hp.unitPrice) ne '0.0'}"
                   th:text="${#strings.toString(hp.unitPrice).contains('.')?#strings.toString(hp.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):hp.unitPrice}+'元/m²'">
                    13826元/m2
                </p>
                <!--<p th:if="${#strings.toString(hp.increaseRate) ne '0.0'}">
                    环比上周
                    <i class="ringRatioNumRed" th:class="${#strings.toString(hp.increaseRate).substring(0,1) eq '-' ? 'green' : 'red'}" ><em th:text="${#strings.toString(hp.increaseRate).substring(0,1) eq '-' ? '  ↓  ' : '  ↑  '}"></em><span th:text="${#strings.toString(hp.increaseRate).replace('-','')+'%'}"></span></i>

                </p>-->
                <a th:if="${#lists.size(hp.houses) == 4}" th:href="${'/saleHouses/-v'+houses.subId}" class="seeMoreBtn" target="_blank">更多房源></a>
            </div>
            <li th:each="sub:${hp.houses}">
               <a th:href="${'/salehouse/'+sub.houseId+'.htm'}" target="_blank">
                    <img th:src="${#strings.isEmpty(sub.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sub.pic}" th:alt="${sub.title}"/>
                </a>
                <div>
                    <h4><a th:href="${'/salehouse/'+sub.houseId+'.htm'}" target="_blank" th:text="${'['+sub.regionName+'-'+sub.plateName+']'+sub.subName}">[和平 - 太原街]金城花园</a></h4>
                    <p>
                        <th:block th:text="${sub.room+'室'+sub.hall+'厅'+sub.toilet+'卫'}"></th:block>&nbsp;/
                        <th:block th:text="${#strings.toString(sub.area).contains('.')? #strings.toString(sub.area).replaceAll('0+?$','').replaceAll('[.]$', '') : sub.area}+'m²'"></th:block>
                    </p>
                </div>
                <p th:if="${#strings.isEmpty(sub.price) or (#strings.toString(sub.price) eq '0.0')}"  th:text='面议'></p>
                <p th:if= "${!#strings.isEmpty(sub.price) and #strings.toString(sub.price) ne '0.0'}" >
                    <th:block th:text="${#strings.toString(sub.price).contains('.')? #strings.toString(sub.price).replaceAll('0+?$','').replaceAll('[.]$', '') : sub.price}"></th:block>
                    <i th:text="${#strings.isEmpty(sub.price) or (#strings.toString(sub.price) eq '0.0')?'':'万'}"></i>
                </p>

            </li>

        </ul>
    </div>
</div>

<!--排行榜-->
<div class="rankingList">
    <h4 class="checkTitle">排行榜</h4>
    <div class="rankingListK">
        <h5>沈阳小区涨跌排行榜</h5>
        <!--<div class="ranklistBtn">
            <a class="rankingBtnR rankingBtn"><img
                    src="https://static.fangxiaoer.com/web/images/sy/sale/list/iconBtnRed.jpg" alt=""/></a>
            <a class="rankingBtnG rankingBtn"><img
                    src="https://static.fangxiaoer.com/web/images/sy/sale/list/iconBtnGreen.jpg" alt=""/></a>
        </div>-->
        <ul class="rankingListMain rankingListMainUp" th:if="${!#strings.isEmpty(subUpList)}">
            <li th:each="su,s:${subUpList}" style="min-height: 57px;">
                <em th:text="${s.index+1}">1</em>
                <div class="rankingLM1">
                    <p><a th:href="${'/saleVillages/'+su.subId+'/index.htm'}" target="_blank" th:text="${su.title}"></a></p>
                    <p th:if="${!#strings.isEmpty(su.unitPrice) and #strings.toString(su.unitPrice) ne '0.0'}"><b
                            th:text="${#strings.toString(su.unitPrice).contains('.')?#strings.toString(su.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):su.unitPrice}+'元/m²'">1175</b>
                    </p>
                    <p th:if="${#strings.isEmpty(su.unitPrice) || #strings.toString(su.unitPrice) eq '0.0'}"><b
                            th:text="${'暂无资料'}"></b>
                    </p>
                </div>
                <div class="rankingLM2">
                    <!--<p>环比上周</p>
                    <p th:if="${#strings.toString(su.increaseRate) ne null && #strings.toString(su.increaseRate) ne '0.0'}">
                        <i class="red">↑</i>
                        <span  th:text="${#strings.toString(su.increaseRate).replace('-','')+'%'}"></span>
                    </p>-->
                    <p th:if="${#strings.toString(su.increaseRate) eq '0.0' || #strings.toString(su.increaseRate) eq null}">
                        <span  th:text="${'----'}"></span>
                    </p>
                </div>
            </li>

        </ul>
        <ul class="rankingListMain rankingListMainDown" th:if="${!#strings.isEmpty(subDownList)}">
            <li th:each="sud,d:${subDownList}">
                <em th:text="${d.index+1}">1</em>
                <div class="rankingLM1">
                    <p><a th:href="${'/saleVillages/'+sud.subId+'/index.htm'}" target="_blank" th:text="${sud.title}"></a></p>
                    <p th:if="${!#strings.isEmpty(sud.unitPrice) and #strings.toString(sud.unitPrice) ne '0.0'}"><b
                            th:text="${#strings.toString(sud.unitPrice).contains('.')?#strings.toString(sud.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):sud.unitPrice}+'元/m²'">1175</b>
                    </p>
                    <p th:if="${#strings.isEmpty(sud.unitPrice) || #strings.toString(sud.unitPrice) eq '0.0'}"><b
                            th:text="${'暂无资料'}"></b>
                    </p>
                </div>
                <div class="rankingLM2">
                    <!--<p>环比上周</p>
                    <p th:if="${#strings.toString(sud.increaseRate) ne null && #strings.toString(sud.increaseRate) ne '0.0'}">
                        <i class="green">↓</i>
                        <span th:text="${#strings.toString(sud.increaseRate).replace('-','')+'%'}"></span>
                    </p>-->
                    <p th:if="${#strings.toString(sud.increaseRate) eq null || #strings.toString(sud.increaseRate) eq '0.0'}">
                        <span th:text="${'----'}"></span>
                    </p>
                </div>
            </li>

        </ul>
    </div>
    <div class="rankingListK rankingListK2" th:if="${!#strings.isEmpty(hotSubList)}">
        <h5>沈阳小区热搜排行榜</h5>
        <ul class="rankingListMain">
            <li th:each="hot,h:${hotSubList}">
                <em th:text="${h.index+1}">1</em>
                <div class="rankingLM1">
                    <p><a th:href="${'/saleVillages/'+hot.subId+'/index.htm'}" target="_blank" th:text="${hot.subName}"></a></p>
                    <p><b th:text="${'在售'+hot.saleCount+'套'}">在售367套</b></p>
                </div>
                <div class="rankingLM2">
                    <p>参考均价</p>
                    <p th:if="${!#strings.isEmpty(hot.price) and #strings.toString(hot.price) ne '0.0'}"
                       th:text="${#strings.toString(hot.price).contains('.')?#strings.toString(hot.price).replaceAll('0+?$','').replaceAll('[.]$', ''):hot.price}+'元/m²'">
                        1751元/m²</p>
                </div>
            </li>

        </ul>
    </div>

    <a class="rankingListSeeMore">展开更多</a>
</div>

</div>


</div>
<script type="text/javascript">
    $(".rankingListMainDown").hide();
    $(".rankingBtnG").hide();

    $(".rankingBtnR").click(function () {
        $(this).hide();
        $(".rankingBtnG").show();
        $(".rankingListMainUp").hide();
        $(".rankingListMainDown").show();
    })
    $(".rankingBtnG").click(function () {
        $(this).hide();
        $(".rankingBtnR").show();
        $(".rankingListMainUp").show();
        $(".rankingListMainDown").hide();
    })


    $(document).ready(function () {
        $(".rankingListMain li+li+li+li+li+li").hide()
        $(".rankingListSeeMore").click(function () {
            $(".rankingListMain li+li+li+li+li+li").show();
            $(this).hide();
        })
    });

</script>
<!--右侧浮标-->
<div th:include="fragment/fragment::esfCommonFloat"></div>
<!--底部-->
<div class="footer" th:include="fragment/fragment::footer_detail"></div>
<div th:include="fragment/fragment::tongji"></div>


</body>
</html>
