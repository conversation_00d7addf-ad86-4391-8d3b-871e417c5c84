<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div th:fragment="serviceOrder">
    <script type="text/javascript" src="/js/house/verify.js"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/freeService/layer_sun.css" />
    <div class="layer_box">
        <table>
            <tr>
                <td>
                    <div class="layer">
                        <div class="title">
                            <h1>免费预约</h1>
                        </div>
                        <div class="content">
                            <div class="phone">
                                <input id="freeServicePhone" type="text" maxlength="11" placeholder="输入您的手机号">
                            </div>
                            <div class="Verification">
                                <input id="freeServiceVerifyCode" type="text" maxlength="6" placeholder="输入验证码">
                                <button id="getRegCode" class="fxe_ReSendValidateCoad">获取验证码</button>
                                <span class="fxe_validateCode" style="display: none"></span>
                            </div>
                            <div class="type">
                                <div class="type_sun">
                                    <div class="text">
                                        <h1>请选择服务类型</h1>
                                    </div>
                                    <div class="btn">
                                        <img src="https://static.fangxiaoer.com/web/images/freeService/up_sun.png">
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="open">
                                    <div class="text">
                                        <h1>展开</h1>
                                    </div>
                                    <div class="btn">
                                        <img src="https://static.fangxiaoer.com/web/images/freeService/up_sun.png">
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="clearfix"></div>
                                <div class="type_layer">
                                    <ul id="mainServiceList">
                                        <li><h1 dir ="1">装修攻略</h1></li>
                                        <li><h1 dir ="2">家居保洁</h1></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="accurate">
                                <div class="type_sun">
                                    <div class="text">
                                        <h1>请选择精准服务</h1>
                                    </div>
                                    <div class="btn">
                                        <img src="https://static.fangxiaoer.com/web/images/freeService/up_sun.png">
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="clearfix"></div>
                                <div class="type_layer">
                                    <ul id="childServiceList">
                                        <li><h1 dir="1">不限</h1></li>
                                        <li><h1 dir="3">高端设计</h1></li>
                                        <li><h1 dir="4">环保施工</h1></li>
                                        <li><h1 dir="5">一站服务</h1></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="introduce">
                                <p>服务简介：林凤装饰集团创建于1997年，是辽沈地区成立最早的家装企业，集团总部现位于沈阳市浑南区林凤全案体验中心。林凤坚持以客户满意作为服务的标准。
                                    优惠：到店即送</p>
                            </div>
                            <div style="height: 24px;margin-top: 5px">
                                <p class="helpsearch_code_error"></p>
                            </div>
                            <div class="Remarks">
                                <p>注: 请留下您的联系方式，我们将通知商户尽快与您联系，请保证电话真实通畅</p>
                            </div>
                            <div class="Agreement">
                                <div class="btn">
                                    <img src="https://static.fangxiaoer.com/web/images/freeService/Choice.png">
                                </div>
                                <input id="serviceAgreement" type="checkbox">
                                <h1>我已阅读并接受<a  href="https://info.fangxiaoer.com/About/disclaimer" target="_blank">《房小二网用户服务免责声明》</a><a  href="https://event.fangxiaoer.com/20180803_ms.htm" target="_blank">《房小二网家居服务协议》</a></h1>
                                <div class="clearfix"></div>
                            </div>
                            <div class="Submission">
                                <button>立即提交</button>
                            </div>
                        </div>
                        <div class="close">
                            <img src="https://static.fangxiaoer.com/web/images/freeService/close.png">
                        </div>
                    </div>
                    <input type="hidden" id="currentMainId">
                    <input type="hidden" id="currentChildId">
                    <input type="hidden" id="currentCompanyId">
                </td>
            </tr>
        </table>

        <script>
            $(".layer_box table .layer .content .type .type_sun").click(
                function(e){

                    $(".layer_box table .layer .content .accurate .type_layer").hide()
                    $(".layer_box table .layer .content .type .type_layer").removeClass("strip")
                    if($(".layer_box table .layer .content .type .type_layer").height()==224){
                        $(".layer_box table .layer .content .type .type_layer").addClass("strip")
                    }
                    e.stopPropagation();
                    if($(this).parent(".type").find(".type_layer").is(":hidden")){
                        $(this).parent(".type").find(".type_layer").show()
                    }else{
                        $(this).parent(".type").find(".type_layer").hide()

                    }
                }
            )

            $(".layer").click(function (e) {
                e.stopPropagation();
                $(this).find(".type_layer").hide();
            });

            var judge=true;
            $(".layer_box table .layer .content .type .open").click(
                function(e){

                    e.stopPropagation();
                    $("#currentChildId").val("")
                    if(judge==false){
                        judge=true;
                        $(this).find("h1").text("展开")
                        $(".layer_box table .layer .content .accurate ").hide()
                        $(".layer_box table .layer .content .accurate .type_sun .text h1").text("请选择精准服务")
                        $(".layer_box table .layer .content .introduce ").hide()
                        $(".layer_box table .layer .content .type .open .btn img").removeClass("rotate_sun")
                    }else{
                        judge=false
                        $(this).find("h1").text("收起")
                        $(".layer_box table .layer .content .accurate ").show()
                        $(".layer_box table .layer .content .type .open .btn img").addClass("rotate_sun")

                    }

                }
            );

            $(".layer_box table .layer .content .accurate .type_sun").click(
                function(e){
                    $(".layer_box table .layer .content .accurate .type_layer").removeClass("strip")
                        if($(".layer_box table .layer .content .accurate .type_layer").height()==224){
                            $(".layer_box table .layer .content .accurate .type_layer").addClass("strip")
                        }

                    e.stopPropagation()
                    if($(this).parent(".accurate").find(".type_layer").is(":hidden")){
                        $(this).parent(".accurate").find(".type_layer").show();

                    }else{
                        $(this).parent(".accurate").find(".type_layer").hide();
                    }
                }
            )
            $(".layer_box table .layer .content .accurate .type_layer ul").on("click","li",function (e) {
                e.stopPropagation()
                var text=$(this).find("h1").text()
                $(".layer_box table .layer .content .accurate .open").show()
                $(".layer_box table .layer .content .accurate .type_sun .text h1").text(text)
                $(".layer_box table .layer .content .accurate .type_sun .text h1").css("color","#333")
                $(".type_layer").hide()
                $(".layer_box table .layer .content  .introduce p").text($(this).find("p").text());
                $("#currentChildId").val($(this).find("h1").attr("dir"));
                $(".layer_box table .layer .content  .introduce").show();
                $(".layer_box table .layer .content  .introduce").removeClass("strip")
                if($(".layer_box table .layer .content  .introduce").height()==82){
                    $(".layer_box table .layer .content  .introduce").addClass("strip")
                }
               var text_sun= $(this).find(".text_wei").html()
                $(".layer_box table .layer .content .introduce").html("")
                $(".layer_box table .layer .content .introduce").prepend(text_sun)
                $(".layer_box table .layer .content .introduce p").show()

            });

            <!--房小二网服务协议-->
            $(".layer_box table .layer .content .Agreement input").attr("checked","checked");
            $(".layer_box table .layer .content .Agreement .btn").click(
                function(){
                    if($(this).hasClass("color")){
                        $(this).removeClass("color")
                        $(this).find("img").show()
                        $(this).next("input").prop("checked","checked")

                    }else{
                        $(this).addClass("color")
                        $(this).find("img").hide()
                        $(this).next("input").prop("checked",false)

                    }
                }
            )
            <!--免费服务点击弹出弹框-->
            $(".information_box .content_box .information .btn button").click(
                function () {
                    initPop($("#companyId").val());
                }
            );
            <!--调用弹窗方法-->
            function initPop(companyId) {
                initServiceTypes(companyId,"");
                $("#freeServicePhone").val("");
                $("#freeServiceVerifyCode").val("");
                $(".layer_box table .layer .content .Verification button").prop('disabled',false)
                $(".layer_box table .layer .content .Verification button").text("获取验证码")
                $(".event_xqdz_validateCode").hide();
                $("#currentCompanyId").val(companyId);
                $(".layer_box").show();
            }


            $(".layer_box table .layer .close").click(
                function () {
                    $(".layer_box ").hide()
                    $(".layer_box table .layer .content .Verification button").text("获取验证码");
                    sy_confirm.wait = 1;

                    $(".layer_box table .layer .content .type .open").hide()
                    $(".layer_box table .layer .content .type .type_sun").width(100+"%")
                    $(".layer_box table .layer .content .type .open .text h1").text("展开")
                    $(".layer_box table .layer .content .type .type_sun .text h1").text("请选择服务类型")
                    $(".layer_box table .layer .content .accurate .type_sun .text h1").text("请选择精准服务")
                    $(".layer_box table .layer .content .introduce").hide()
                    $(".layer_box table .layer .content .accurate ").hide()
                    judge=true
                    $(".helpsearch_code_error").text("");
                    $("#currentChildId").val("");
                    $("#currentMainId").val("");
                }
            )



            sy_confirm.init(2,true);
            <!--提交订单成功则展示成功-->
            $(".layer_box table .layer .content .Submission button").click(function() {
                var confirm_phone_verify= sy_confirm.phone($("#freeServicePhone").val());
                var confirm_code_verify =  sy_confirm.code($("#freeServiceVerifyCode").val());
                var companyMainId = $("#currentMainId").val();
              if (confirm_phone_verify != true) {
                    $(".helpsearch_code_error").text(confirm_phone_verify);
                    $(".helpsearch_code_error").show();
                } else if (confirm_code_verify != true) {
                    $(".helpsearch_code_error").text(confirm_code_verify);
                    $(".helpsearch_code_error").show();
                } else if(companyMainId =="" || companyMainId ==null || companyMainId == undefined){
                    $(".helpsearch_code_error").text("请选择服务类型");
                    $(".helpsearch_code_error").show();
                } else if(!$("#serviceAgreement").is(":checked")){
                    $(".helpsearch_code_error").text("请勾选房小二网用户服务协议");
                    $(".helpsearch_code_error").show();
                } else {
                    submitGuide();
                    $(".helpsearch_code_error").hide()
                }
            })
//            发送yzm
            $(".layer_box table .layer .content .Verification button").click(function(){
                var help_phone_msg = sy_confirm.phone($("#freeServicePhone").val());
                if( help_phone_msg==true){
                    console.log('新验证码验证成功！');
                    sy_confirm.Code($("#freeServicePhone").val()).then(res => {
                        console.log('发送新验证码验证成功！');
                        console.log(res);
                        if (res == true) {
                          sy_confirm.timeWait();
                          $(".helpsearch_code_error").hide();
                        }
                    }).catch(err => {
                        console.log('发送新验证码验证失败！');
                        console.log(err)
                    })
                }else {
                    $(".helpsearch_code_error").text(help_phone_msg);
                    $(".helpsearch_code_error").show();
                }
            });
            <!--提交订单-->
            function submitGuide() {
                var params = {mobile:$("#freeServicePhone").val(),code:$("#freeServiceVerifyCode").val(), companyId: $("#currentCompanyId").val(),mainId: $("#currentMainId").val(),childId: $("#currentChildId").val()}
                $.ajax({
                    type:"post",
                    url:"/serviceGuide",
//						async:false,
                    data:JSON.stringify(params),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success:function(data){
                        if (data.status == 1) {
                            $("#freeServicePhone").attr("value","");
                            $("#freeServiceVerifyCode").attr("value","");
                            sy_confirm.wait = 1;
                            $(".layer_box ").hide()
                            $(".layer_boxsun ").show()
                            setTimeout(
                                function () {
                                    $(".layer_boxsun ").hide()
                                },3000
                            );
                            $(".layer_box table .layer .content .Verification button").prop('disabled',false);
                            $(".layer_box table .layer .content .Verification button").text("获取验证码");
                            $("#currentMainId").attr("value","");
                            $("#currentChildId").attr("value","");
                            $("#currentCompanyId").attr("value","");
                            $(".layer_box table .layer .content .type .open").hide()
                            $(".layer_box table .layer .content .type .type_sun").width(100+"%")
                            $(".layer_box table .layer .content .type .type_sun .text h1").text("请选择服务类型")
                            $(".layer_box table .layer .content .accurate .type_sun .text h1").text("请选择精准服务")
                            $(".layer_box table .layer .content .introduce").hide()
                            $(".layer_box table .layer .content .accurate ").hide()
                            judge=true;
                            $(".layer_box table .layer .content .type .open").hide()
                            $(".layer_box table .layer .content .type .type_sun").width(100+"%")
                            $(".layer_box table .layer .content .type .open .text h1").text("展开")
                        } else if (data.status == 0) {
                            $(".helpsearch_code_error").text(data.msg);
                            $(".helpsearch_code_error").show();
                        } else {
                            $(".helpsearch_code_error").text("提交失败！");
                            $(".helpsearch_code_error").show();
                        }
                    },

                    error:function(data){
                        alert("服务器繁忙请稍后重试");
                        console.log(data)
                    }
                });
            }

            <!--初始化下拉菜单-->
            function initServiceTypes(companyId,mainId) {
                var mainState  = mainId == ""||mainId == null?1:2;
                var params = {companyId: companyId,mainId: mainId}
                $.ajax({
                    type:"post",
                    url:"/serviceTypes",
//						async:false,
                    data:JSON.stringify(params),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success:function(data){
                        if (data.status == 1) {
                            var appendElement = "";
                            for(var i=0;i<data.content.length;i++){
                                appendElement = appendElement+  "<li><h1 dir='"+data.content[i].id+"'>"+data.content[i].name+"</h1>" +
                                    "<div class='text_wei'><p style='display: none'>服务简介："+(data.content[i].serviceSummary == null?'暂无资料':data.content[i].serviceSummary)+"</p><p style='display: none'>优惠："+(data.content[i].serviceDiscount == null?'暂无资料':data.content[i].serviceDiscount)+"</p></div></li>";
                            }
                            if(mainState ==1){
                                $("#mainServiceList").html("");
                                $("#mainServiceList").append(appendElement);
                            }else if(mainState == 2){
                                $("#childServiceList").html("");
                                $("#childServiceList").append(appendElement);
                            }
                        } else if (data.status == 0) {
                            $(".helpsearch_code_error").text(data.msg);
                            $(".helpsearch_code_error").show();
                        } else {
                            $(".helpsearch_code_error").text("提交失败！");
                            $(".helpsearch_code_error").show();
                        }
                    },
                    error:function(data){
                        alert("服务器繁忙请稍后重试");
                        console.log(data)
                    }
                });
            }

            $("#mainServiceList").on("click","li",function (e) {
                var currentMainId = $(this).find("h1").attr("dir");
                $(".layer_box table .layer .content .accurate .type_sun .text h1").text("请选择精准服务")
                $(".layer_box table .layer .content .introduce").hide()
                $("#currentMainId").val(currentMainId);
                initServiceTypes($("#currentCompanyId").val(),currentMainId);
                e.stopPropagation();
                var text=$(this).find("h1").text();
                $(".layer_box table .layer .content .type .open").show();
                $(".layer_box table .layer .content .type .type_sun .text h1").text(text);
                $(".layer_box table .layer .content .type .type_sun .text h1").css("color","#333")
                $(".layer_box table .layer .content .type .type_sun ").css("width","298px");
                $(".type_layer").hide();
                $("#currentChildId").val("")
            })

        </script>
    </div>
    <div class="layer_boxsun">
        <table>
            <tr>
                <td>
                    <div class="layer">
                        <img src="https://static.fangxiaoer.com/web/images/freeService/Success.png" class="Success">
                        <h1>预约成功</h1>
                        <h2>我们将通知商户尽快与您联系</h2>
                        <div class="close" id="clp">
                            <img src="https://static.fangxiaoer.com/web/images/freeService/close.png">
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
    <script>

        $("#clp").click(
            function () {
               $(".layer_boxsun").hide()
            }
        )
    </script>
</div>



</body>
</html>