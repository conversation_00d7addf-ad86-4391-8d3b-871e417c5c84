<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${#strings.isEmpty(companyInfo.title)?'':companyInfo.title+'_'+companyInfo.platform+'_便民服务 - 房小二网'}">林凤装饰_装修服务_便民服务 - 房小二网</title>
    <meta name="keywords" th:content="${#strings.isEmpty(companyInfo.title)?'':companyInfo.title+','+#strings.toString(companyInfo.serviceTab).replaceAll('/',',')}"/>
    <meta name="description" th:content="${#strings.isEmpty(companyInfo.title)?'':'房小二网'+companyInfo.title+companyInfo.platform+'为您提供专属优惠，用户只需在房小二网进行预约，即可尊享'+companyInfo.title+'房小二网独家到店即送优惠服务。'}"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/freeService/service_details.css" />

    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/freeService/freeService.js" type="text/javascript" charset="utf-8"></script>
</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<div class="position">
   <div class="content_box">
       <h1>您的位置 : <a th:href="@{'/'}">沈阳房产网</a> > <a th:href="@{/freeServiceIndex}">服务</a></h1>
   </div>
</div>
<input th:value="${companyId}" id="companyId" type="hidden">
<div class="information_box">
    <div class="content_box">
        <div class="photo_box">
            <div class="photo">
                <img src="">
            </div>
            <div class="btn">
                <div class="left">
                    <img src="https://static.fangxiaoer.com/web/images/freeService/left.png" >
                    <img src="https://static.fangxiaoer.com/web/images/freeService/left02.png" class="hover">
                </div>
                <div class="show">
                    <div class="lunbo">
                        <ul th:if="${!#lists.isEmpty(companyInfo.photos)}">
                            <li th:each="photo:${companyInfo.photos}">
                                <img th:src="${photo.photoUrl}">
                                <div class="border"></div>
                            </li>

                            <div class="clearfix"></div>
                        </ul>
                    </div>
                </div>
                <div class="right">
                    <img src="https://static.fangxiaoer.com/web/images/freeService/right.png" >
                    <img src="https://static.fangxiaoer.com/web/images/freeService/right02.png" class="hover">
                </div>
                <div class="clearfix"></div>
            </div>
        </div>
        <div class="information">
            <div class="name">
                <h1 th:text="${#strings.isEmpty(companyInfo.title)?'':companyInfo.title}">林凤装饰</h1>
            </div>
            <div class="Label" th:if="${!#strings.isEmpty(companyInfo.serviceTab)}">
                <ul>
                    <li th:each="serviceTab,si:${#strings.setSplit(companyInfo.serviceTab,'/')}" th:if="${si.index lt 3}">
                        <h1 th:text="${#strings.isEmpty(serviceTab)?'':serviceTab}">甲醛治理</h1>
                    </li>
                    <div class="clearfix"></div>
                </ul>
            </div>
            <div class="introduce">
                <h1>公司简介</h1>
                <p th:text="${#strings.isEmpty(companyInfo.description)?'':companyInfo.description}">辽宁林凤装饰装修工程有限，千余个顶级家居装饰材料品牌，开创极具家居装修前沿时尚特征的整体家居服务，让客户对未来“家”的憧憬和幻想成为现实。</p>
            </div>
            <div class="address">
                <h1>公司地址</h1>
                <p th:text="${#strings.isEmpty(companyInfo.address)?'':companyInfo.address}">铁西兴华街北一路千缘财富商汇B座5层</p>
                <!--<a id="contactMap" th:href="${'javascript:fnOpenMiniMap('''+companyInfo.title+''','''+companyInfo.address+''',''沈阳'','''+companyInfo.latitude+''','''+companyInfo.longitude+''')'}">查看地图</a>-->
                <div class="clearfix"></div>
            </div>
            <div class="btn" id="order">
                <button>免费预约</button>
            </div>
        </div>
        <div class="clearfix"></div>
        <div class="layer_wei" ><th:block th:text="${#strings.isEmpty(companyInfo.address)?'':companyInfo.address}"></th:block><div class="ms_sanjiao dt_sj"></div></div>
    </div>
</div>
<script>

    $(".information_box .content_box .information .address p").mouseenter(
        function(){
            if($(this).width()==374){
                $(".layer_wei").show()
            }
        }
    )
    $(".information_box .content_box .information .address p").mouseleave(
        function(){
            $(".layer_wei").hide()
        }
    )
  $(document).ready(
      function () {

          for (var x=0;x<$(".details_box .content_box .left_box .list ").length;x++){
              $(".details_box .content_box .left_box .list ").eq(x).find("ul li:first").css("border",0)
          }
      }
  )
</script>
<div class="details_box">
    <div class="content_box">
        <div class="left_box" th:if="${!#lists.isEmpty(companyInfo.service)}">
            <div class="list" th:each="servicies:${companyInfo.service}">
                <div class="name">
                    <h1 th:text="${#strings.isEmpty(servicies.name)?'':servicies.name}">装修攻略</h1>
                </div>
                <div class="text" th:if="${!#lists.isEmpty(servicies.childservices)}">
                    <ul>
                        <li th:each="service:${servicies.childservices}"  th:class="${service.id eq serviceId ? 'currentSelect':''}">
                            <a th:href="${'/freeService/'+companyId+'-'+service.id+'.htm#order'}">
                                <h1 th:text="${#strings.isEmpty(service.name) ?'':service.name}">高端设计</h1>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="right_box" th:if="${#maps.size(companyInfo.serviceInfo) ne 0}">
            <div class="brief_introduction">
                <div class="name">
                    <h1>服务简介</h1>
                </div>
                <div class="text">
                    <p th:text="${#strings.isEmpty(companyInfo.serviceInfo.serviceSummary)?'':companyInfo.serviceInfo.serviceSummary}">林凤装饰集团创建于1997年，是辽沈地区成立最早的家装企业，集团总部现位于沈阳市浑南区林凤全案整装（东北）体验中心。林凤现有员工1300余人，截止目前，林凤累计为辽沈地区超过10万户业主匠著+、品质为先”的经营理念，坚持以客户满意作为服务的标准。</p>
                </div>
            </div>
            <div class="Discount">
                <div class="name">
                    <h1>优惠</h1>
                </div>
                <div class="text">
                    <p th:text="${#strings.isEmpty(companyInfo.serviceInfo.serviceDiscount)?'':companyInfo.serviceInfo.serviceDiscount}">林凤装饰集团创建于1997年，是辽沈地区成立最早的家装企业，集团总部现位于沈阳市浑南区林凤全案整装（东北）体验中心。林凤现有员工1300余人，截止目前，林凤累计为辽沈地区超过10万户业主匠著+、品质为先”的经营理念，坚持以客户满意作为服务的标准。</p>
                </div>
            </div>
            <div class="service_content">
                <div class="name">
                    <h1>服务内容</h1>
                </div>
                <div class="text" th:utext="${companyInfo.serviceInfo.serviceDesc}">

                </div>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>
</div>

<div th:include="freeService/fragmentOrder::serviceOrder"></div>

<!--通用详情底-->
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
</body>
<script>


    var lw=$(".information_box .content_box .photo_box .btn ul li").width()*$(".information_box .content_box .photo_box .btn ul li").length+$(".information_box .content_box .photo_box .btn ul li").length*10

    $(".information_box .content_box .photo_box .btn .lunbo").width(lw)
    var x;
    if($(".information_box .content_box .photo_box .btn ul li").length%5==0){
        x=$(".information_box .content_box .photo_box .btn ul li").length/5
    }else{
        x=parseInt($(".information_box .content_box .photo_box .btn ul li").length/5)+1
    }
    var y=0
$(".information_box .content_box .photo_box .btn .right").click(
    function(){
        if(y<x-1){
            y++
            $(".information_box .content_box .photo_box .btn .lunbo").animate({left:-y*590},500)
        }
    }
)
$(".information_box .content_box .photo_box .btn .right").mouseenter(
    function(){

        if(y<x-1){
            $(this).find("img").hide()
            $(this).find(".hover").show()

        }
    }
)
    $(".information_box .content_box .photo_box .btn .right").mouseleave(
        function(){
            $(this).find("img").show()
            $(this).find(".hover").hide()
        }
    )





    $(".information_box .content_box .photo_box .btn .left").mouseenter(
        function(){

            if(y>0){
                $(this).find("img").hide()
                $(this).find(".hover").show()

            }
        }
    )
    $(".information_box .content_box .photo_box .btn .left").mouseleave(
        function(){
            $(this).find("img").show()
            $(this).find(".hover").hide()
        }
    )

    $(".information_box .content_box .photo_box .btn .left").click(
        function(){
            if(y>0){
                y--
                $(".information_box .content_box .photo_box .btn .lunbo").animate({left:-y*590},500)
            }
        }
    )
    function fnOpenMiniMap(locName,locAddr,locCity,locLat,locLon){
        var url = "https://my.zhaopin.com/map/minimap.htm?ln=" + escape(locName) + "&la=" + escape(locAddr) + "&lc=" + escape(locCity) + "&lla=" + escape(locLat) + "&llo=" + escape(locLon);
        window.open(url,"minimap","height=506,width=906,status=yes,toolbar=no,menubar=no,location=no");
    }
    <!--默认首选第一个服务-->
    function initFocus() {
        var hasForcus = true;
        $(".left_box li").each(function () {
            if($(this).hasClass("currentSelect")){
                hasForcus = false;
            }
        })
        if(hasForcus){
            $(".left_box li:eq(0)").addClass("currentSelect");
        }
    }
    initFocus();

</script>
</html>