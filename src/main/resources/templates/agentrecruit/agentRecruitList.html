<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="'房产直聘_房小二网招聘_沈阳招聘 - 房小二网'"></title>
    <meta name="keywords" th:content="'房产直聘,房小二网招聘,沈阳招聘,沈阳经纪人招聘'" />
    <meta name="description" th:content="'房小二网房产直聘，为您提供沈阳稀缺房产人才与招聘信息，找人才，就上房小二网！'" />
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <!--<meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang2/'+mobileAgent}">-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20181019" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css"  href="https://static.fangxiaoer.com/web/styles/second/agentRecruit.css?v=20210409"/>
    <link rel="stylesheet" type="text/css"  href="https://static.fangxiaoer.com/web/styles/cs-select.css?v=20210409"/>
    <!--<link href="/css/agentRecruit.css" type="text/css" rel="stylesheet" />-->
    <!--<link href="/css/cs-select.css" type="text/css" rel="stylesheet" />-->
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/agentrecruit.js?v=20210409"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/classie.js?v=20210409"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/selectFx.js?v=20210409"></script>
    
    <!--<script src="/js/agentrecruit/agentrecruit.js" type="text/javascript"></script>-->
    <!--<script src="/js/classie.js"></script>
	<script src="/js/selectFx.js"></script>-->
	<link href="https://static.fangxiaoer.com/m/static/css/video/animate.css" type="text/css" rel="stylesheet">
    
</head>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--搜索框-->
<div style="background: #fdfdfd; padding: 14px 0 5px 0;" th:include="fragment/fragmentrecruit::search_information"></div>
<div class="main">


<!--    <div class="logoBanner">-->
<!--        <ul class="smallBanner">-->
<!--            <li th:each="company:${agentCompanys}" th:if="${!#strings.isEmpty(company.companyLogo)}">-->
<!--                <a  th:href="${'/companyDetail'+'/'+company.companyId+'.htm'}" target="_blank"><img  th:src="${'https://imageicloud.fangxiaoer.com/'+company.companyLogo}" /></a>-->
<!--            </li>-->
<!--        </ul>-->
<!--    </div>-->
<div class="w100_option">
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleHouses/" target="_blank">沈阳二手房</a> &gt;<a href="/agentRecruit">房产直聘</a></div>
	<div class="option">
		<div class="clear_option">
			全部
		</div>
		
		 <!--职位-->
		<select class="cs-select cs-skin-border" id="jid">
			<option th:each="j,job:${jobType}" th:nonce="1" th:text="${job.index == 0?'不限':j.name}" th:id="${j.id}" ></option>
		</select>
		 <!--区域-->
		<select class="cs-select cs-skin-border" id="rid" >
			<option th:each="r,ri:${region}" th:nonce="2"  th:if="${r.id ne '10'and r.id ne '11'}" th:text="${ri.index == 0?'不限':r.name}" th:id="${r.id}"></option>
		</select>
		<!--薪资-->
		<select class="cs-select cs-skin-border" id="sid" >
			<option th:each="s,stat:${salary}" th:nonce="3"  th:text="${stat.index ==0?'不限': s.name}" th:id="${s.id}" ></option>
		</select>
		<!--工作经验-->
		<select class="cs-select cs-skin-border" id="eid" >
			<option th:each="e,stat:${experience}" th:nonce="4"  th:text="${stat.index ==0?'不限': e.name}" th:id="${e.id}" ></option>
		</select>
		   <!--福利-->
		<select class="cs-select cs-skin-border" id="wid" >
			<option th:each="w,stat:${welfare}" th:nonce="5"  th:text="${stat.index ==0?'不限': w.name}" th:id="${w.id}" ></option>
		</select>
	</div>
	
</div>
<script>
	//加载自定义下拉列表样式
	(function() {
				[].slice.call( document.querySelectorAll( 'select.cs-select' )).forEach( function(el) {	
					new SelectFx(el);
				} );
			})();
</script>



    <!--<div id="option">
        <ul>
            <li class="jopTypeLi"><p>职位：</p>
                <a th:each="j,job:${jobType}" onclick="showIndex(this);" th:href="${j.url}" th:text="${job.index == 0?'不限':j.name}" th:id="${j.id}"  th:class="${j.selected}? 'hover':''"></a>
            </li>
            <li class=""><p>地点：</p>
                <a th:each="r,ri:${region}" onclick="showIndex(this);" th:href="${r.url}" th:id="${r.id}" th:if="${r.id ne '10'and r.id ne '11'}" th:class="${r.selected}? 'hover':''">
                    <th:block  th:text="${ri.index == 0?'不限':r.name}"></th:block>
                    <i th:if="${!#strings.isEmpty(r.id)}"></i> </a><br>
            </li>
            <li><p>薪资：</p>
                <a th:each="s,stat:${salary}" onclick="showIndex(this);" th:text="${stat.index ==0?'不限': s.name}" th:href="${s.url}" th:id="${s.id}" th:class="${s.selected}? 'hover':''"></a>
            </li>
            <li><p>工作经验：</p>
                <a  th:each="e,stat:${experience}" onclick="showIndex(this);"  th:text="${stat.index ==0?'不限': e.name}" th:href="${e.url}" th:id="${e.id}" th:class="${e.selected}? 'hover':''"></a>
            </li>
            <li class="welfare"><p>福利：</p>
                <a th:each="w,stat:${welfare}" onclick="showIndex(this);" th:text="${stat.index ==0?'不限': w.name}" th:href="${w.url}" th:id="${w.id}" th:class="${w.selected}? 'hover':''"></a>
            </li>
        </ul>
    </div>-->
 
   <div class="agentRecruitList">
        <div class="arlLeft arrEach" th:if="${!#strings.isEmpty(recruitList)}">
            <!--<div class="arlLeftTitle">
               
            </div>-->
            <ul>
<!--                th:onclick="${'window.open(''/positionDetail/'+recruit.jobAdId+'.htm'')'}"-->
                <li th:each="recruit:${recruitList}" >
                    <input type="hidden" th:value="${recruit.jobAdId}">
                        <h4 class="padding24 clearfix">
                            <a th:href="${'/positionDetail'+'/'+recruit.jobAdId+'.htm'}" target="_blank">
                            <!--<span th:text="${recruit.plateName}"></span> <i>|</i> -->
                            <!--修改显示职位名称，不显示标题-->
                            <span th:text="${recruit.typeName}"></span>
                            <!--聊呗-->
                    		</a>

                    	
                      </h4>
                    <a onfocus="this.blur();" th:href="${'/positionDetail'+'/'+recruit.jobAdId+'.htm'}" target="_blank" style="overflow: hidden">

                    <div class="arlLeftLiL padding24">
                            <div class="arlLeftLiL-Txt">
                                <p th:if="${#strings.toString(recruit.jobSalaryRangeMin) ne '0' && #strings.toString(recruit.jobSalaryRangeMax) ne '0'}" th:text="${recruit.jobSalaryRangeMin.replaceAll('.0','')+'-'+recruit.jobSalaryRangeMax.replaceAll('.0','')+'/月'}">10000-12000元 </p>
                                <p th:if="${(#strings.isEmpty(recruit.jobSalaryRangeMin)  || #strings.toString(recruit.jobSalaryRangeMin) eq '0') && !#strings.isEmpty(recruit.jobSalaryRangeMax || #strings.toString(recruit.jobSalaryRangeMax) eq '0')}" th:text="${'面议'}"></p>
                                <div class="agentSpan">
                                    <!--2020-11-20 Bowen 改版为工作区域、工作经验要求、学历要求、入职类型-->
                                    <span>沈阳</span>
                                    <span th:if="${!#strings.isEmpty(recruit.regionName)}" th:text="${' - ' + recruit.regionName}"></span>
                                    <span th:if="${!#strings.isEmpty(recruit.plateName)}" th:text="${' - ' + recruit.plateName}"></span>
                                    <th:block th:each="item,state : ${workingLife}" th:if=" ${item.id eq recruit.jobWorkingLifeType and #strings.toString(recruit.jobWorkingLifeType) ne '5'}">
                                        <i>丨</i>
                                        <span th:text="${item.name}">工作经验要求</span>
                                    </th:block>
                                    <th:block th:each="item,state : ${education}" th:if=" ${item.id eq recruit.education}">
                                        <i>丨</i>
                                        <span th:text="${item.name}">学历要求</span>
                                    </th:block>
                                    <th:block th:each="item,state : ${induction}" th:if="${item.id eq recruit.induction}">
                                        <i>丨</i>
                                        <span th:text="${item.name}">入职类型</span>
                                    </th:block>
                                    <!--2020-11-20 End-->
                                </div>
                            </div>
                        </div>
                          
                    </a>
                    <p class="fldy padding24 clearfix" th:if="${recruit._transJobWelfare ne null and recruit._transJobWelfare ne ''}">
                                <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(recruit._transJobWelfare).split(',')}"  th:text="${item}" ></span>
                            </p>

                    <div class="arlLeftLiR">
                           <a th:href="${'/companyDetail'+'/'+recruit.companyId+'.htm'}" target="_blank"><h3 th:text="${recruit.companyName}">沈阳瑞家坚果中介</h3></a>
                    </div>
                    
                    <div class="arrSq">
                    	<div class="lbdiv" th:include="fragment/fragmentrecruit::new_liaobei"></div>
                    	 <a th:if="${#session?.getAttribute('sessionId') == null}"  href="#login" target="_blank" data-toggle="modal"class="toGetRecruit" >申请职位</a>
                        <a th:if="${#session?.getAttribute('sessionId') != null}" class="arlLeftBTn toGetRecruit">申请职位</a>
                    </div>

               </li>
            </ul>
            <div class="cl"></div>
            <div class="page">
                <div th:include="fragment/page :: page"></div>
            </div>
        </div>
        <div class="arlLeft" th:if="${#strings.isEmpty(recruitList)}">
            <span class="NoListMsg">抱歉，暂时没有符合该条件的职位，请重新修改搜索条件后再搜索、筛选</span>
        </div>
        <div class="arlRight">
        	<div class="topdiv">
        		 <span>招聘效果，请致电：</span>
                <div>400-893-9709</div>
        	</div>
        	
            <ul class="urgentWant" th:if="${ !#strings.isEmpty(agentUrgent)}">
                <h4><i></i>岗位急聘</h4>
                <li  th:each="urgent,i:${agentUrgent}" th:if="${i.index lt 6}">
                    <a th:href="${'/positionDetail'+'/'+urgent.jobAdId+'.htm'}" target="_blank">
                        <h5 th:text="${urgent.jobTitle}"></h5>
                        <p class="agentRecruitList">
                            <span th:if="${!#strings.isEmpty(urgent.typeName)}" th:text="${urgent.typeName}">中介经纪人</span>
<!--                            <i>丨</i>-->
<!--                            <span>招聘人数：<i th:text="${urgent.jobNeedPersonNumber+'人'}">5</i></span>-->
                        </p>
                        <p th:if="${#strings.toString(urgent.jobSalaryRangeMin) ne '0' && #strings.toString(urgent.jobSalaryRangeMax) ne '0'}" th:text="${urgent.jobSalaryRangeMin+'-'+urgent.jobSalaryRangeMax+'元'}">10000-12000元 </p>
                        <p th:if="${(#strings.isEmpty(urgent.jobSalaryRangeMin)  || #strings.toString(urgent.jobSalaryRangeMin) eq '0') && !#strings.isEmpty(urgent.jobSalaryRangeMax || #strings.toString(urgent.jobSalaryRangeMax) eq '0')}" th:text="${'面议'}"></p>
                    </a>
                </li>
            </ul>
            <div class="arlRightBanner" th:if="${ !#strings.isEmpty(advertisement)}" th:each="adv,i:${advertisement}">
                <a th:if="${!#strings.isEmpty(adv.targetUrl)}"   th:href="${adv.targetUrl}">
                    <img th:if="${!#strings.isEmpty(adv.pic)}" th:src="${#strings.toString(adv.pic)}" />
                </a>
            </div>
        </div>
    </div>
    <div class="cl"></div>
    <!--底部1-->
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="house/detail/fragment_login::login"></div>
</div>
<!--申请职位-->
<div th:include="fragment/fragmentrecruit::apply_position"></div>
<script>
    function showIndex(obj) {
        obj.click();
    }

    // $("a,.liaobeiICon,.arlLeftBTn").click(function() {
    //     event.stopPropagation();
    // });
</script>
</body>
</html>