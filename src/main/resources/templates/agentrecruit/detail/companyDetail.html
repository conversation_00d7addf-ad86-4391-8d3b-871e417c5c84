<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${companyDetail?.companyName} + '招聘_'+${companyDetail?.companyName}+'_沈阳房产招聘 - 房小二网'"></title>
    <meta name="keywords" th:content="${companyDetail?.companyName} + '招聘,'+${companyDetail?.companyName}+'沈阳房产招聘,沈阳经纪人招聘'" />
    <meta name="description" th:content="${companyDetail?.companyName} + '人才招聘，'+${companyDetail?.companyDesc}" />
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20181019" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css"  href="https://static.fangxiaoer.com/web/styles/new_sy/comment/agentRecruit.css"/>
    <script src="/js/agentrecruit/agentrecruit.js" type="text/javascript"></script>
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <style>
        .hasResume{display: none}
    </style>
</head>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<body>
<div class="main">
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleHouses/" target="_blank">沈阳二手房</a> &gt;<a href="/agentRecruit" target="_blank">房产直聘</a> &gt; <a href=""><span th:text="${companyDetail.companyName}"></span></a></div>
    <div class="companyMain" th:if="${!#strings.isEmpty(companyDetail)}">
        <div class="companyBanner"><h4 th:text="${companyDetail.companyName}">辽宁易玛科技有限公司</h4></div>
        <div class="companyintroduce">
            <div class="L">
                <h4>公司介绍</h4>
                <p th:utext="${companyDetail.companyDesc}">公司秉承“简单、直率、结果导向、团队合作、鼓励竞争、分享成功”的企业文化，坚持“专业解决中国房地产问题，为客户创造价值”的企业
                    理念，不断降低成本，提高性价比，促进人们生活水平的提高，顺应社会发展潮流，使企业快速发展。凭借独特的营销模式和高进取心的专业团队，历经了中国房地产行业发展的高峰和低谷的洗礼，已经成为北京极具影响力的地产公司，赢得客户的赞誉、同行的尊敬
                </p>
            </div>
            <div class="R" th:if="${!#strings.isEmpty(companyDetail.companyLogo)}"><img th:src="${'https://imageicloud.fangxiaoer.com/'+companyDetail.companyLogo}"/></div>
            <div class="R" th:if="${#strings.isEmpty(companyDetail.companyLogo)}"><img src="/agentrecruitimg/nologo.jpg" /></div>
        </div>
    </div>
    <div class="agentRecruitList">
        <div class="arlLeft" th:if="${!#strings.isEmpty(jobList) && #lists.toList(jobList).size() ne 0}">
            <div class="arlLeftTitle">
                <p>招聘职位</p>
            </div>
            <ul>
                <li th:each="recruit:${jobList}">
                    <input type="hidden" th:value="${recruit.jobAdId}">
                    <a th:href="${'/positionDetail'+'/'+recruit.jobAdId+'.htm'}" target="_blank">
                        <h4>
                            <span id="plateName" th:text="${recruit.plateName}"></span> <i>|</i> <span th:text="${recruit.jobTitle}"></span>
                            <!--聊呗-->
                            <div th:include="fragment/fragmentrecruit::liaobei"></div>
                        </h4>
                    <div class="arlLeftLiL">

                        <p th:if="${#strings.toString(recruit.jobSalaryRangeMin) ne '0' && #strings.toString(recruit.jobSalaryRangeMax) ne '0'}" th:text="${recruit.jobSalaryRangeMin+'-'+recruit.jobSalaryRangeMax+'元'}">10000-12000元 </p>
                        <p th:if="${(#strings.isEmpty(recruit.jobSalaryRangeMin)  || #strings.toString(recruit.jobSalaryRangeMin) eq '0') && !#strings.isEmpty(recruit.jobSalaryRangeMax || #strings.toString(recruit.jobSalaryRangeMax) eq '0')}" th:text="${'面议'}"></p>
                        <p class="fldy" th:if="${recruit._transJobWelfare ne null and recruit._transJobWelfare ne ''}">
                            <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(recruit._transJobWelfare).split(',')}"  th:text="${item}" ></span>
                        </p>
                    </div>
                    <div class="arlLeftLiR">
                        <a th:href="${'/companyDetail'+'/'+recruit.companyId+'.htm'}"><h3 th:text="${recruit.companyName}">沈阳瑞家坚果中介</h3></a>
                        <p class="agentRecruitListp">
                            <span th:if="${!#strings.isEmpty(recruit.typeName)}" th:text="${recruit.typeName}">中介经纪人</span>
                            <i>丨</i>
                            <span>招聘人数：<i th:text="${recruit.jobNeedPersonNumber+'人'}">5</i></span>
                        </p>
                        <a class="arlLeftBTn">申请职位</a>
                    </div>
                    </a>
                </li>
            </ul>
        </div>
        <div class="arlRight">
            <div class="arlRightMap">
                <div>
                    <h4 >公司地址</h4>
                    <p th:text="${companyDetail.companyAddress}" id="companyAddress"></p>
                </div>
                <!--地图显示公司地址-->
                <div th:include="fragment/fragmentrecruit::company_address_map"></div>
        </div>
    </div>

</div>
    <div th:include="house/detail/fragment_login::login"></div>
<!--申请职位-->
<div th:include="fragment/fragmentrecruit::apply_position"></div>
</div>
<div class="cl"></div>
<!--底部1-->
<div th:include="fragment/fragment:: footer_list"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>

</html>