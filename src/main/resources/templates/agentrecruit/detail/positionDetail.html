<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${jobDetail?.jobTitle}+'- 房小二网'"></title>
    <meta name="keywords" th:content="${companyDetail?.companyName}+'招聘,'+${companyDetail?.companyName}+',沈阳房产招聘,沈阳经纪人招聘'" />
    <meta name="description" th:content="${companyDetail?.companyName}+'人才招聘，'+${jobDetail?.jobDesc}" />
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20181019" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/agentRecruit2.css?t=20210409" />
    <!--<link href="/css/agentRecruit2.css" type="text/css" rel="stylesheet" />-->
	<link href="https://static.fangxiaoer.com/m/static/css/video/animate.css" type="text/css" rel="stylesheet">
	 <script type="text/javascript" src="https://static.fangxiaoer.com/js/agentrecruit.js?v=20210409"></script>
    <!--<script src="/js/agentrecruit/agentrecruit.js" type="text/javascript"></script>-->
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
</head>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<body>
<div class="main">
    <div class="topBg">
        <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleHouses/" target="_blank">沈阳二手房</a> &gt;<a href="/agentRecruit" target="_blank">房产直聘</a> &gt; <a th:href="${'/agent/intermediary/home/'+companyDetail?.companyId}"><span th:text="${companyDetail.companyName}"></span></a>&gt;<a
                href=""><span th:text="${jobDetail.typeName}"></span></a></div>
        <div class="agentTop">
            <input type="hidden" th:value="${jobDetail.jobAdId}" id="jobAdId">
            <div class="L" th:if="${!#strings.isEmpty(jobDetail)}">
                <div class="topL-title">
                    <h4>
                    	<span th:text="${jobDetail.typeName}"></span>

                    </h4>
                	<div class="topPrise" th:if="${#strings.toString(jobDetail.jobSalaryRangeMin) ne '0' && #strings.toString(jobDetail.jobSalaryRangeMax) ne '0'}" th:text="${jobDetail.jobSalaryRangeMin.replaceAll('.0','')+'-'+jobDetail.jobSalaryRangeMax.replaceAll('.0','')+'/月'}">10000-12000元 </div>
                	<div class="topPrise" th:if="${(#strings.isEmpty(jobDetail.jobSalaryRangeMin)  || #strings.toString(jobDetail.jobSalaryRangeMin) eq '0') && !#strings.isEmpty(jobDetail.jobSalaryRangeMax || #strings.toString(jobDetail.jobSalaryRangeMax) eq '0')}" th:text="${'面议'}"></div>
                    
                    
                </div>
                    <div class="job_subT" th:if="${#strings.toString(jobDetail.jobTitle) ne null}" th:text="${jobDetail.jobTitle}"></div>

                <div class="topArea">
                    <!--2020-11-20 Bowen 改版为工作区域、工作经验要求、学历要求、入职类型-->
                    <span>沈阳</span>
                    <span th:if="${!#strings.isEmpty(jobDetail.regionName)}" th:text="${' - ' + jobDetail.regionName}"></span>
                    <span th:if="${!#strings.isEmpty(jobDetail.plateName)}" th:text="${' - ' + jobDetail.plateName}"></span>
                    <th:block th:each="item,state : ${workingLife}" th:if=" ${item.id eq jobDetail.jobWorkingLifeType}">
                        <i>丨</i>
                        <span th:text="${item.name}">工作经验要求</span>
                    </th:block>
                    <th:block th:each="item,state : ${education}" th:if=" ${item.id eq jobDetail.education}">
                        <i>丨</i>
                        <span th:text="${item.name}">学历要求</span>
                    </th:block>
                    <th:block th:each="item,state : ${induction}" th:if="${item.id eq jobDetail.induction}">
                        <i>丨</i>
                        <span th:text="${item.name}">入职类型</span>
                    </th:block>
                    <!--2020-11-20 End-->
                </div>


            </div>
        <div class="topR">
            <a th:if="${#session?.getAttribute('sessionId') == null}" class="topR-login" href="#login" target="_blank" data-toggle="modal">申请职位</a>
            <a th:if="${#session?.getAttribute('sessionId') != null}" class="topR-login arlLeftBTn" style="float: none;border-radius:0;margin: 0;border: none;">申请职位</a>
            <a class="topR-liaobeiICon" th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/imRecruit/'+jobDetail.jobAdId}"  target="_blank">
                <div>
                    <i class="liaobeiIcon"></i>
                    <text>聊呗</text>
                </div>
            </a>
            <!--聊呗-->
            <!--  <span th:include="fragment/fragmentrecruit::liaobei"></span>-->
            <s class="topR-liaobeiICon" th:if="${#session?.getAttribute('sessionId') == null}" >
                <a href="#login" target="_blank" data-toggle="modal">
                    <div>
                        <i class="liaobeiIcon"></i>
                        <text>聊呗</text>
                    </div>
                </a>

            </s>

        </div>
    </div>

    </div>
        <div class="agentRecruitList">
        <!--<div class="arlRight" >
            <div class="companyDesc">  
                <div class="txtMain">
                    <h5>企业概况<i class="titleIco"></i></h5>
                </div>
                <a th:href="${'/agent/intermediary/home/'+companyDetail?.companyId}">
                    <div class="companyDesc-name">
                        
                    </div>
                    <div class="companyDesc-logo">

                    </div>
                    <div class="companyDesc-txt" th:if="${!#strings.isEmpty(companyDetail.linkmanMobile)}">
                        <label >企业电话:</label>
                        <p th:text="${companyDetail.linkmanMobile}"></p>
                    </div>
                    <div class="companyDesc-txt" th:if="${!#strings.isEmpty(companyDetail.companyDesc)}">
                        <label>企业介绍:</label>
                        <p th:text="${companyDetail.companyDesc}"></p>
                    </div>
                </a>
            </div>

        </div>-->
        <div class="bottomListL">
            <div class="bottomListLcont">
                <div class="txtMain">
                    <h5>职位描述<i class="titleIco"></i></h5>
                    <p th:utext="${jobDetail.jobDesc}" class="bottomListLcontP"></p>
                    <div class="seeMoreBtn">查看全部</div>
                    <div class="nojobDesc">信息更新中..</div>
                </div>
            </div>
            
            <div class="bottomListLcont">
                <div class="txtMain">
                    <h5>公司介绍<i class="titleIco"></i></h5>
                    
                    <div class="company">
                    	<a th:href="${'/agent/intermediary/home/'+companyDetail?.companyId}">
                    	<div class="companyRLogo" th:if="${!#strings.isEmpty(companyDetail?.companyLogo)}">
                            <img th:src="${'https://imageicloud.fangxiaoer.com/'+companyDetail?.companyLogo}" /></div>
                       	<div class="companyRLogo" th:if="${#strings.isEmpty(companyDetail?.companyLogo)}">
                            <img src="https://static.fangxiaoer.com/web/images/agent/noShopImg2.png" />
                        </div>
                       </a>
                       <a class="com_a" th:href="${'/agent/intermediary/home/'+companyDetail?.companyId}">
                       	<span class="companyName" th:text="${companyDetail.companyName}"></span>
                       </a>
                       <div class="companyDesc-area">
                            <label>主营区域:</label>
                            <span th:each="item : ${plateList}" th:if="${companyDetail.companyPlate1 eq item.id}" th:text="${item.name}"></span>
                            <span th:each="item : ${plateList}" th:if="${companyDetail.companyPlate2 eq item.id}" th:text="${'、' + item.name}"></span>
                            <span th:each="item : ${plateList}" th:if="${companyDetail.companyPlate3 eq item.id}" th:text="${'、' + item.name}"></span>
                       </div>
                       <div class="companyPhone" th:if="${!#strings.isEmpty(companyDetail.linkmanMobile)}">
                       	<label>企业电话:</label>
                       	<span th:text="${companyDetail.linkmanMobile}"></span>
                       </div>
                    </div>
                    
                    <div class="company_info" th:if="${!#strings.isEmpty(companyDetail.companyDesc)}" th:text="${companyDetail.companyDesc}"></div>
                </div>
            </div>
            
            
            <div class="bottomListLcont">
                <div class="txtMain">
                    <h5>职位福利<i class="titleIco"></i></h5>
                
                <ul class="fldyLi" th:if="${jobDetail._transJobWelfare ne null and jobDetail._transJobWelfare ne ''}">
                    <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(jobDetail._transJobWelfare).split(',')}"  th:text="${item}" ></span>
                </ul>
                </div>
            </div>
            <div class="bottomListLcont">
                <div class="bottomListMap">
                    <div class="txtMain">
                        <h5>工作地点<i class="titleIco"></i></h5>
                    </div>
                    <div class="showMap">
                        <p  id="companyAddress"><i class="mapIcoico"></i><th:block th:text="${companyDetail?.companyAddress}"></th:block></p>
                        <!--地图显示公司地址-->
                        <div th:include="fragment/fragmentrecruit::companyDetail_map"></div>
                    </div>
                </div>
            </div>
           <!-- <div class="bottomListLcont">
                <div class="txtMain">
                    <h5>公司概况</h5>
                    <p th:utext="${companyDetail.companyDesc}"></p>
                </div>
            </div>-->
            <div class="bottomListLcont arrEach" th:if="${#lists.toList(otherJob).size() ne 0}">
                <div class="arlLeftTitle" >
                    <div class="txtMain">
                        <h5>招聘职位<i class="titleIco"></i></h5>
                    </div>
                </div>
                <ul class="otherJob">
                    <li class="otherJobLi" th:each="recruit:${otherJob}">
                        <input type="hidden" th:value="${recruit.jobAdId}">
                        <a th:href="${'/positionDetail'+'/'+recruit.jobAdId+'.htm'}" target="_blank">
                            <h4 class="otherJobH4 padding24">
                                <span class="otherJobH4Span" th:text="${recruit.plateName}"></span> <i>|</i> <span th:text="${recruit.jobTitle}"></span>
                             
                            </h4>
                            <div class="otherJobLiL  padding24">
                                <div class="otherJobLiL-txt">
                                    <div class="otherJobLiL-Prise" th:if="${#strings.toString(recruit.jobSalaryRangeMin) ne '0' && #strings.toString(recruit.jobSalaryRangeMax) ne '0'}" th:text="${recruit.jobSalaryRangeMin.replaceAll('.0','')+'-'+recruit.jobSalaryRangeMax.replaceAll('.0','')+'/月'}">10000-12000元 </div>
                                    <div class="otherJobLiL-Prise" th:if="${(#strings.isEmpty(recruit.jobSalaryRangeMin)  || #strings.toString(recruit.jobSalaryRangeMin) eq '0') && !#strings.isEmpty(recruit.jobSalaryRangeMax || #strings.toString(recruit.jobSalaryRangeMax) eq '0')}" th:text="${'面议'}"></div>
                                    <p class="agentRecruitListp">
                                        <!--2020-11-20 Bowen 改版为工作区域、工作经验要求、学历要求、入职类型-->
                                        <span>沈阳</span>
                                        <span th:if="${!#strings.isEmpty(recruit.regionName)}" th:text="${' - ' + recruit.regionName}"></span>
                                        <span th:if="${!#strings.isEmpty(recruit.plateName)}" th:text="${' - ' + recruit.plateName}"></span>
                                        <th:block th:each="item,state : ${workingLife}" th:if=" ${item.id eq recruit.jobWorkingLifeType and #strings.toString(recruit.jobWorkingLifeType) ne '5'}">
                                            <i>丨</i>
                                            <span th:text="${item.name}">工作经验要求</span>
                                        </th:block>
                                        <th:block th:each="item,state : ${education}" th:if=" ${item.id eq recruit.education}">
                                            <i>丨</i>
                                            <span th:text="${item.name}">学历要求</span>
                                        </th:block>
                                        <th:block th:each="item,state : ${induction}" th:if="${item.id eq recruit.induction}">
                                            <i>丨</i>
                                            <span th:text="${item.name}">入职类型</span>
                                        </th:block>
                                        <!--2020-11-20 End-->
                                    </p>
                                </div>
                                <div class="agentSpan" th:if="${recruit._transJobWelfare ne null and recruit._transJobWelfare ne ''}">
                                    <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(recruit._transJobWelfare).split(',')}"  th:text="${item}" ></span>
                                </div>
                            </div>
                            
                            
                            <div class="arlLeftLiR">
                                <a th:href="${'/agent/intermediary/home/'+companyDetail?.companyId}"><h3 th:text="${recruit.companyName}">沈阳瑞家坚果中介</h3></a>
                                
                            </div>
                            
                             <div class="arrSq">
                    			<div class="lbdiv" th:include="fragment/fragmentrecruit::new_liaobei"></div>
                    			<a th:if="${#session?.getAttribute('sessionId') == null}"  class="toGetRecruit " href="#login" target="_blank" data-toggle="modal">申请职位</a>
                                <a th:if="${#session?.getAttribute('sessionId') != null}" class="toGetRecruit">申请职位</a>
                    </div>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        </div>
    <div class="bottomMsg" style="width: 1170px;color: #999;margin: 30px auto;"><span style="color: #ff5200;">房小二网提醒您：</span>以担保或任何理由索取财物，抵押证照，均涉嫌违法，请提高警惕！</div>
    <div class="cl"></div>
    <script>
        var rowNum=Math.round($(".bottomListLcontP").height()/parseFloat($(".bottomListLcontP").css('line-height')));
        console.log(rowNum)
        if(rowNum>20){
            $(".bottomListLcontP").addClass("hideMore")
            $(".seeMoreBtn").show()
            $(".nojobDesc").hide()
        }else if(rowNum = 0){
            $(".nojobDesc").show()
        }

        $(".seeMoreBtn").click(function () {
            $(".bottomListLcontP").removeClass("hideMore")
            $(".seeMoreBtn").hide()
        })
    </script>
    <!--底部1-->
    <div th:include="fragment/fragment:: footer_list"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="house/detail/fragment_login::login"></div>
</div>
<!--申请职位-->
<div th:include="fragment/fragmentrecruit::apply_position"></div>
<!--页面滚动浮动信息条-->
<div th:include="fragment/fragmentrecruit::infomation_float"></div>
</body>
</html>