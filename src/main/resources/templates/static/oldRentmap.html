<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"  xmlns:th="https://www.thymeleaf.org">
<head>
    <title>沈阳地图找租房，沈阳租房房产地图，沈阳租房地图 - 房小二网</title>
    <meta name="keywords" content="沈阳租房,沈阳租房地图,沈阳地图找房,沈阳房产地图,沈阳楼市地图"/>
    <meta name="description" content="房小二网地图找房为您提供更新的沈阳租房地图信息，全面的沈阳租房房源位置及沈阳租房相关信息。通过简单方便的地图找房功能，使您更加方便地找到目标区域的沈阳租房信息，为您创造更好的买房体验，查找沈阳租房房源，就来房小二网地图找房。"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/housemap3.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/mapZuFang.css" rel="stylesheet" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=1.5&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
    <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
    <script src="/js/oldHouseMap//BaiduMap_dituzhaofang_zufang.js"></script>
    <script src="/js/oldHouseMap//jquery.nicescroll.plus.js"></script>
    <style>
        .fxe_xl_list {
            background: #f3f3f3;
            position: absolute;
            min-width: 127px;
            height: 271px;
            left: 132px;
            top: 30px;
            overflow: auto;
            display: none;
            z-index: 1000;
            border: 1px #ddd solid;
        }
        .fxe_xl_list li:hover {
            color: #ff5200;
            background: #fff;
        }
        .login{
            width: 70px;
            position: absolute;
            right: 0;
            top: 0;
        }
    </style>
</head>
<body>

<!--引入头部导航栏-->

<form name="form1" method="post" action="" id="form1">
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
    <div class="bg">
        <div class="header">
        </div>
        <div class="contain">
            <div class="rightmap">
                <div class="rightmap_main">
                    <div class="rightmap_left">
                        <div class="text">
                            <ul>
                                <li id="Li1" onclick="setTabText('qyh',1,3);" class="hover"><i class="img1"></i>整租</li>
                                <li id="Li2" onclick="setTabText('qyh',2,3);" class=""><i class="img3"></i>合租</li>
                                <!--<li id="Li3" onclick="setTabText('qyh',3,3);" class=""><i class="img4"></i>单间</li>-->
                            </ul>
                            <div class="gray"></div>
                        </div>
                    </div>

                    <div class="rightmap_right">
                        <div class="rightleftmap show" id="con_qyh_1">
                            <div class="rightmap_right_top">
                                <div class="xl_special my_xl " style="display:none">
                                    <input name="Mileage" type="hidden" class="my_xl_input" value="售价" />
                                    <div class="my_xl_txt" id="huxing">户型</div>
                                    <div class="my_xl_btn"></div>
                                    <ul class="my_xl_list">

                                        <li data-key="roomType" data-id="0">全部</li>

                                        <li data-key="roomType" data-id="1">一居</li>

                                        <li data-key="roomType" data-id="2">二居</li>

                                        <li data-key="roomType" data-id="3">三居</li>

                                        <li data-key="roomType" data-id="4">四居</li>

                                        <li data-key="roomType" data-id="5">五居以上</li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="rightleftmap" id="con_qyh_2">
                            <input name="Mileage" type="hidden" class="my_xl_input" value="售价" />
                            <div class="rightmap_right_top">
                                <div class="xl_special my_xl" style="display:none">
                                    <div class="my_xl_txt_other my_xl_txt" id="tingshi">厅室</div>
                                    <div class="my_xl_btn"></div>
                                    <ul class="my_xl_list">

                                        <li data-key="bedroom" data-id="0">全部</li>

                                        <li data-key="bedroom" data-id="1">主卧</li>

                                        <li data-key="bedroom" data-id="2">次卧</li>

                                        <li data-key="bedroom" data-id="3">隔断</li>

                                        <li data-key="bedroom" data-id="4">床位</li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="rightleftmap" id="con_qyh_3">
                            <div class="rightmap_right_top">
                            </div>
                        </div>
                        <div class="rightmap_right_cen">
                            <div class="xiaoQu">

                            </div>
                            <div class="txtblock">

                                <div id="divexample1">
                                    <ul id="ulList">
                                    </ul>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="my_xl area">
                        <input name="Mileage" type="hidden" class="my_xl_input" value="售价" />
                        <div class=" my_xl_txt" id="quyu">区域</div>
                        <div class="my_xl_btn"></div>
                        <ul class="my_xl_list my_xl_list_quyu fxe_xl_list_quyu" identify="0">

                            <li data-key="regionId" data-id="0" lng="0" lat="0">不限</li>

                            <li data-key="regionId" data-id="1" lng="123.464988" lat="41.802225">沈河区</li>

                            <li data-key="regionId" data-id="2" lng="123.427224" lat="41.79535">和平区</li>

                            <li data-key="regionId" data-id="3" lng="123.424888" lat="41.865727">皇姑区</li>

                            <li data-key="regionId" data-id="4" lng="123.476486" lat="41.811581">大东区</li>

                            <li data-key="regionId" data-id="5" lng="123.382991" lat="41.809242">铁西区</li>

                            <li data-key="regionId" data-id="6" lng="123.456348" lat="41.720868">浑南区</li>

                            <li data-key="regionId" data-id="7" lng="123.312348" lat="41.799993">于洪区</li>

                            <li data-key="regionId" data-id="8" lng="123.415976" lat="41.912125">沈北新区</li>

                            <li data-key="regionId" data-id="9" lng="123.350641" lat="41.671104">苏家屯区</li>

                        </ul>
                        <ul class="fxe_xl_list fxe_xl_list_quyu2"></ul>

                    </div>
                    <div class="my_xl money" style="display:none;">
                        <input name="Mileage" type="hidden" class="my_xl_input" value="" />
                        <div class="my_xl_txt" id="zujin">租金</div>
                        <div class="my_xl_btn"></div>
                        <ul class="my_xl_list">

                            <li data-key="PriceBase" data-id="0">全部</li>

                            <li data-key="PriceBase" data-id="1">500元以下</li>

                            <li data-key="PriceBase" data-id="2">500-1000元</li>

                            <li data-key="PriceBase" data-id="3">1000-2000元</li>

                            <li data-key="PriceBase" data-id="4">2000-3000元</li>

                            <li data-key="PriceBase" data-id="5">3000元以上</li>

                        </ul>
                    </div>
                </div>
                <div class="rightmap_btn"></div>
            </div>
            <div class="leftmap">
                <div class="symap">
                    <div class="dt1">
                        <div id="bdmap">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/my_xiala.js"></script>
    <script>
        /*****切换*****/
        function setTabText(name, cursel, n) {
            //dituzhaofang.QieHuan(cursel);
            //for (i = 1; i <= n; i++) {
            //    var menu = document.getElementById(name + i);
            //    var con = document.getElementById("con_" + name + "_" + i);
            //    menu.className = i == cursel ? "hover" : "";
            //    con.style.display = i == cursel ? "block" : "none";
            //}
        }
        /*****租金切换*****/
        function money() {
            //var cn = $("#qyh3").attr('class');
            //if (cn != "hover") {
            //    $(".money").show();
            //} else {
            //    $(".money").hide();
            //}
        }
        $(".rightmap_left li").click(function () {
            $(this).addClass("hover").siblings().removeClass("hover")
            $("#rentType").val($(this).index() + 1)
            dituzhaofang.QieHuan($(this).index() + 1);
        })
        /*********点击收回左侧***********/
        $(function () {
            $(".rightmap_btn").click(function () {
                var dd = $(".rightmap_main").css("display");
                if (dd == "none") {
                    $(".rightmap_main").show();
                    $(this).css("background-position", "-7px -212px").css("left", "530px");
                    $(".leftmap").css("margin-left", "531px");
                    $(".leftmap").css("min-width", "552px");

                } else {
                    $(".rightmap_main").hide();
                    $(this).css("background-position", "6px -212px").css("left", "0px");
                    $(".leftmap").css("margin-left", "0px");
                    $(".leftmap").css("min-width", "1170px");
                }
            });
        });

        /******获取地图高度和地铁列表******/
        change();
        $(window).resize(function () {
            change();
        });
        function change() {
            var h = $(window).height() - 50;
            var height = h + "px";
            var ditie = h - 83 + "px";
            var cen1 = h - 74 + "px";
            $("#bdmap,.leftmap,.rightmap_left").css("height", height);
            $(".station_border,.station ul").css("height", ditie);
            $(".rightmap_right_cen,#divexample1").css("height", cen1);
        }
        /******导航滑动效果******/
        $(function () {
            $(".rightmap_left ul li").mouseover(function () {
                var num = $(this).index();
                switch (num) {
                    case 0:
                        $(".gray").stop().animate({ top: "0" }, 200);
                        break;
                    case 1:
                        $(".gray").stop().animate({ top: "85px" }, 200);
                        break;
                    case 2:
                        $(".gray").stop().animate({ top: "170px" }, 200);
                        break;
                }
            });
            $(".rightmap_left .text").mouseout(function () {
                var id = $(".rightmap_left .hover").attr("id");
                var y = id.substr(2, 2) - 1;
                var position = y * 85 + "px";
                $(".gray").stop().animate({ top: position }, 200);
            });
        });

        /*********获取滚动条*********/
        $(document).ready(function () {
            $("#divexample1").niceScroll();
        });
    </script>
</form>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
</body>
</html>
