<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>公积金贷款计算器 - 房小二网</title>
    <meta name="keywords" content="沈阳贷款,房贷,沈阳公积金贷款,沈阳商业贷款,提前还款，购房能力评估"/>
    <meta name="description" th:content="'房小二网购房工具为购房者提供'+${yearTime}+'年房贷计算器及各种在线购房计算工具。包括房贷计算器，公积金贷款计算器，商业贷款计算器，组合贷款计算器，房贷提前还贷计算器，税费计算器，购房能力评估，公积金贷款额度评估等服务。'"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/static/ggjdk.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/lpr/jsq.css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
</head>

<body class="w1210">

<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav"></div>
<!--页面位置-->
<div class="w place">您的位置：<a href="/">沈阳房产网</a> &gt;  公积金贷款计算器</div>

<div class="w wrap_nav">
    <a href="business.htm">商业贷款计算器</a>
    <a href="fund.htm" class="hover">公积金贷款计算器</a>
    <a href="assemble.htm">组合贷款计算器</a>
    <!--<a href="advance.htm">提前还款计算器</a>
    <a href="tallage.htm">税费计算器</a>
    <a href="ability.htm">购房能力评估</a>
    <a href="fundAssess.htm">公积金贷款额度评估</a>-->
</div>
<div class="cl"></div>



<div class="focus_counter clear">
    <div class="counter_left">
        <s class="arrows_ico"></s>
        <div class="counter_left_tt counter_moudle_tt">
            <h3>填写信息</h3>
        </div>
        <dl class="clear">
            <dt>计算方式：</dt>
            <dd class="fangshi">
                <label>
                    <input name="formula_mode" id="area_cal" type="radio" value="1" checked="checked">按面积算</label>
                <label>
                    <input name="formula_mode" id="num_cal" type="radio" value="2" checked="checked">按贷款额度算</label>
            </dd>
        </dl>
        <div class="fanshi_bd no_margin" style="display: block;">
            <form id="fund_area_a">
                <dl class="clear">
                    <dt>单价：</dt>
                    <dd class="guding">
                        <input type="text" class="txt" data-type="number" name="danjia" id="danjia" maxlength="9">
                        <em>元/m²</em>
                        <div class="error_msg" data-for="danjia">请输入正确的房屋单价</div>
                    </dd>
                </dl>
                <dl class="clear no_margin">
                    <dt>面积：</dt>
                    <dd class="guding">
                        <input type="text" class="txt" data-type="number" name="mianji" id="mianji" maxlength="9">
                        <em>m²</em>
                        <div class="error_msg" data-for="mianji">请输入正确的房屋面积</div>
                    </dd>
                </dl>
                <dl class="clear no_margin" style="z-index:10">
                    <dt>购房性质：</dt>
                    <dd class="xingzhi">
                        <label>
                            <input name="goufangxingzhi" type="radio" checked="" id="first_house" value="1">首套房 </label>
                        <label>
                            <input name="goufangxingzhi" type="radio" id="two_house" value="2"> 二套房</label>
                        <span class="tips_mark">
                                <i></i>
                                <div class="tips_div" style="display: none;">
                                    <div class="tips_div_cen">
                                        <s></s>
                                        <div class="tips_div_tt">二套房界定标准：</div>
                                        <p>
1.个人已通过贷款购买过一套（及以上）住房，且至少有一笔贷款尚未还清时，再次申请贷款购买住房的。<br>
2.夫妻两人,其中一人婚前购房贷款未还清时，婚后两人想要以夫妻名义共同贷款购买住房的。</p>
                                    </div>
                                </div>
                            </span>
                    </dd>
                </dl>
                <dl class="clear" style="z-index:9;">
                    <dt>首付：</dt>
                    <dd class="guding">
<!--                        <input type="text"  class="txt" name="shoufu" id="shoufu" maxlength="5" oninput="this.value = this.value.match(/^(\d{1,2})(\.(\d{0,2}))?$/) ? this.value : ''">-->
                        <input type="text" class="txt" name="shoufu" id="shoufu" oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>10)value=10;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);">
                        <em>成</em>
                        <div class="error_msg" data-for="mianji">请输入正确的首付</div>
                    </dd>
                </dl>

                <dl class="clear" style="z-index:8">
                    <dt>贷款利率：</dt>
                    <dd>
                        <div class="lilv item_drop v5" style="z-index:2;">
                            <span id="jil" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新基准利率'}"></span>
                            <div class="hide_box" style="display: none;">
                                <ul id="original_rate">
                                 <li><a href="javascript:;" data-date="20151024" data-discount="1" class="selected_drop_item" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新基准利率'}"></a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="shoudong">
                                    <span class="inline_block guding">
                                    <b class="lilu_box"><input type="text" id="rate_a" data-type="number" name="lilu_a" value="3.25" maxlength="4" oninput="if(value.length>3)value=value.slice(0,4)">
                                    <em>%</em></b>                                
                                </span>
                            <span class="hint_text">当时利率，可以修改</span>
                            <div class="error_msg" data-for="rate_a">请输入正确的贷款利率</div>
                        </div>
                    </dd>
                </dl>
                <dl class="clear no_margin" style="z-index:7">
                    <dt>贷款年限：</dt>
                    <dd>
                        <div class="loan_year item_drop v6">
                            <span id="loan_time_select_a">30年（360期）</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="loan_time_a">
                                    <li><a href="javascript:;">1年（12期）</a>
                                    </li>
                                    <li><a href="javascript:;">2年（24期）</a>
                                    </li>
                                    <li><a href="javascript:;">3年（36期）</a>
                                    </li>
                                    <li><a href="javascript:;">4年（48期）</a>
                                    </li>
                                    <li><a href="javascript:;">5年（60期）</a>
                                    </li>
                                    <li><a href="javascript:;">6年（72期）</a>
                                    </li>
                                    <li><a href="javascript:;">7年（84期）</a>
                                    </li>
                                    <li><a href="javascript:;">8年（96期）</a>
                                    </li>
                                    <li><a href="javascript:;">9年（108期）</a>
                                    </li>
                                    <li><a href="javascript:;">10年（120期）</a>
                                    </li>
                                    <li><a href="javascript:;">11年（132期）</a>
                                    </li>
                                    <li><a href="javascript:;">12年（124期）</a>
                                    </li>
                                    <li><a href="javascript:;">13年（156期）</a>
                                    </li>
                                    <li><a href="javascript:;">14年（168期）</a>
                                    </li>
                                    <li><a href="javascript:;">15年（180期）</a>
                                    </li>
                                    <li><a href="javascript:;">16年（192期）</a>
                                    </li>
                                    <li><a href="javascript:;">17年（204期）</a>
                                    </li>
                                    <li><a href="javascript:;">18年（216期）</a>
                                    </li>
                                    <li><a href="javascript:;">19年（228期）</a>
                                    </li>
                                    <li><a href="javascript:;">20年（240期）</a>
                                    </li>
                                    <li><a href="javascript:;">21年（252期）</a>
                                    </li>
                                    <li><a href="javascript:;">22年（264期）</a>
                                    </li>
                                    <li><a href="javascript:;">23年（276期）</a>
                                    </li>
                                    <li><a href="javascript:;">24年（288期）</a>
                                    </li>
                                    <li><a href="javascript:;">25年（300期）</a>
                                    </li>
                                    <li><a href="javascript:;">26年（312期）</a>
                                    </li>
                                    <li><a href="javascript:;">27年（324期）</a>
                                    </li>
                                    <li><a href="javascript:;">28年（336期）</a>
                                    </li>
                                    <li><a href="javascript:;">29年（348期）</a>
                                    </li>
                                    <li><a href="javascript:;" class="selected_drop_item">30年（360期）</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <p class="your_want">您期望偿还贷款的年限</p>
                    </dd>
                </dl>
                <dl class="clear">
                    <dt class="h0"></dt>
                    <dd class="jisuan">
                        <input type="button" value="开始计算" class="Start_calculation_area">
                        <a href="javascript:;" id="reset_area">清空重填</a>
                    </dd>
                </dl>
            </form>
        </div>

        <div class="fanshi_bd" style="display: none;">
            <form id="fund_area_b">
                <dl class="clear">
                    <dt>贷款总额：</dt>
                    <dd class="guding">
                        <input type="text" class="txt" name="zonge" id="loan_money" maxlength="9" data-type="number">
                        <em>万元</em>
<!--                        <span id="fund_link"><a href="fundAssess.htm" target="_blank">公积金贷款额度评估</a></span>-->
                        <div class="error_msg" data-for="loan_money">请输入正确的贷款总额</div>
                    </dd>
                </dl>
                <dl class="clear no_margin" style="z-index:8">
                    <dt>贷款利率：</dt>
                    <dd>
                        <div class="lilv item_drop v7" style="z-index:2;">
                            <span class="dow" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新基准利率'}"></span>
                            <div class="hide_box" style="display: none;">
                                <ul id="original_rate_b">
                                    <!--<li><a href="javascript:;" data-date="20151024" data-discount="1.1">2017最新利率1.1倍</a></li>-->
                                    <li><a href="javascript:;" data-date="20151024" data-discount="1" class="selected_drop_item" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新基准利率'}"></a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="shoudong">
                                    <span class="inline_block guding">
                                    <b class="lilu_box">
<!--                                        <input type="text" id="rate_b" data-type="number" name="lilu_b" value="3.25" oninput="value=value.replace(/[^\d]/g,'');">-->
                                        <input type="text" id="rate_b" data-type="number" name="lilu_b" value="3.25" oninput="if(value.length>3)value=value.slice(0,4)">
                                    <em>%</em></b>
                                </span>
                            <span class="hint_text">当时利率，可以修改</span>
                            <div class="error_msg" data-for="rate_b">请输入正确的贷款利率</div>
                        </div>
                    </dd>
                </dl>
                <dl class="clear no_margin" style="z-index:7">
                    <dt>贷款年限：</dt>
                    <dd>
                        <div class="loan_year item_drop v8">
                            <span class="dow" id="loan_time_select_b">30年（360期）</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="loan_time_b">
                                    <li><a href="javascript:;">1年（12期）</a>
                                    </li>
                                    <li><a href="javascript:;">2年（24期）</a>
                                    </li>
                                    <li><a href="javascript:;">3年（36期）</a>
                                    </li>
                                    <li><a href="javascript:;">4年（48期）</a>
                                    </li>
                                    <li><a href="javascript:;">5年（60期）</a>
                                    </li>
                                    <li><a href="javascript:;">6年（72期）</a>
                                    </li>
                                    <li><a href="javascript:;">7年（84期）</a>
                                    </li>
                                    <li><a href="javascript:;">8年（96期）</a>
                                    </li>
                                    <li><a href="javascript:;">9年（108期）</a>
                                    </li>
                                    <li><a href="javascript:;">10年（120期）</a>
                                    </li>
                                    <li><a href="javascript:;">11年（132期）</a>
                                    </li>
                                    <li><a href="javascript:;">12年（124期）</a>
                                    </li>
                                    <li><a href="javascript:;">13年（156期）</a>
                                    </li>
                                    <li><a href="javascript:;">14年（168期）</a>
                                    </li>
                                    <li><a href="javascript:;">15年（180期）</a>
                                    </li>
                                    <li><a href="javascript:;">16年（192期）</a>
                                    </li>
                                    <li><a href="javascript:;">17年（204期）</a>
                                    </li>
                                    <li><a href="javascript:;">18年（216期）</a>
                                    </li>
                                    <li><a href="javascript:;">19年（228期）</a>
                                    </li>
                                    <li><a href="javascript:;">20年（240期）</a>
                                    </li>
                                    <li><a href="javascript:;">21年（252期）</a>
                                    </li>
                                    <li><a href="javascript:;">22年（264期）</a>
                                    </li>
                                    <li><a href="javascript:;">23年（276期）</a>
                                    </li>
                                    <li><a href="javascript:;">24年（288期）</a>
                                    </li>
                                    <li><a href="javascript:;">25年（300期）</a>
                                    </li>
                                    <li><a href="javascript:;">26年（312期）</a>
                                    </li>
                                    <li><a href="javascript:;">27年（324期）</a>
                                    </li>
                                    <li><a href="javascript:;">28年（336期）</a>
                                    </li>
                                    <li><a href="javascript:;">29年（348期）</a>
                                    </li>
                                    <li><a href="javascript:;" class="selected_drop_item">30年（360期）</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <p class="your_want">您期望偿还贷款的年限</p>
                    </dd>
                </dl>
                <dl class="clear">
                    <dt class="h0"></dt>
                    <dd class="jisuan">
                        <input type="button" value="开始计算" class="Start_calculation_rate">
                        <a href="javascript:;" id="reset_rate">清空重填</a>
                    </dd>
                </dl>
            </form>
        </div>
    </div>
    <div class="counter_right">
        <div class="counter_right_tt counter_moudle_tt">
            <h3>计算结果</h3>
        </div>
        <div class="counter_gongjijin clear">
            <div class="c_gjj_a c_gjj_moudle">
                <h3>等额本息还款
                    <span class="tips_mark">
                            <i></i>
                            <div class="tips_div" style="display: none;">
                                <div class="tips_div_cen">
                                    <s></s>
                                    <p>指借款人每月按相等的金额偿还贷款本息，其中每月贷款利息按月初剩余贷款本金计算并逐月结清。<a href="/news/view/46498" target="_blank">详细信息&gt;&gt;</a></p>
                                </div>
                            </div>
                        </span>
                </h3>
                <div class="c_gjj_moudle_bd">
                    <dl class="clear">
                        <dt>房屋总价</dt>
                        <dd class="guding"><span id="total_price_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>贷款总额</dt>
                        <dd class="guding"><span id="total_loan_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>还款总额</dt>
                        <dd class="guding"><span id="total_pay_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>支付利息</dt>
                        <dd class="guding"><span id="interest_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>首期付款</dt>
                        <dd class="guding"><span id="first_pay_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>贷款月数</dt>
                        <dd class="guding"><span id="loan_month_bx"></span><em>月</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>月均还款</dt>
                        <dd class="guding"><span id="average_pay_bx"></span><em>元</em>
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="c_gjj_b c_gjj_moudle">
                <h3>等额本金还款
                    <span class="tips_mark">
                            <i></i>
                            <div class="tips_div" style="display: none;">
                                <div class="tips_div_cen">
                                    <s></s>
                                    <p>指本金保持相同，利息逐月递减，月还款数递减；由于每月的还款本金额固定，而利息越来越少，贷款人起初还款压力较大，但是随时间的推移每月还款数也越来越少。<a href="/news/view/46498" target="_blank">详细信息&gt;&gt;</a></p>
                                </div>
                            </div>
                        </span>
                </h3>
                <div class="c_gjj_moudle_bd">
                    <dl class="clear">
                        <dt>房屋总价</dt>
                        <dd class="guding"><span id="total_price_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>贷款总额</dt>
                        <dd class="guding"><span id="total_loan_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>还款总额</dt>
                        <dd class="guding"><span id="total_pay_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>支付利息</dt>
                        <dd class="guding"><span id="interest_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>首期付款</dt>
                        <dd class="guding"><span id="first_pay_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>贷款月数</dt>
                        <dd class="guding"><span id="loan_month_bj"></span><em>月</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>首月还款</dt>
                        <dd class="guding meiyue_huankuan"><span id="average_pay_bj"></span><a href="javascript:;" class="huankuan_mingxi">明细</a>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="beizhu">
            <h3><span>备注：</span>（以上结果仅供参考）</h3>
            <p>目前公积金贷款执行首套、二套差别化贷款原则；</p>
            <p>购买首套房时，面积小于90（含）时，首付最低为2成，利率为基本利率 ；面积大于90时，首付最低为3成，利率不变。选择二套房时，首付最低为4成，利率上浮1.1倍。</p>
        </div>
    </div>
</div>
</div>
<div class="popup" id="popup_one">
    <div class="popBox">
        <div class="popup_hd clear">
            <h5>等额本金每月还款明细</h5>
            <a class="close_ico" href="javascript:;"></a>
        </div>
        <div class="popup_bd">
            <table cellpadding="0" cellspacing="0" border="0" class="popup_table">
                <tbody>
                <tr>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                </tr>
                <tr class="td_bg">
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                </tr>
                <tr>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                </tr>
                <tr class="td_bg">
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                </tr>
                <tr>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                </tr>
                <tr class="td_bg">
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                </tr>
                <tr>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                    <td>第1期，10000元</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="mask"></div>
</div>



<!--<script src="https://static.fangxiaoer.com/js/jsq/common.js"></script>-->
<script src="https://static.fangxiaoer.com/web/styles/sy/lpr/common.js"></script>
<script>
    var monthLpr = parseInt($('#loan_time_select_a').text()) * 12;

    getLPRLoanRate()//获取利率
    $("#loan_time_a").find('a').click(()=>{
        setTimeout(()=>{
            monthLpr = parseInt($('#loan_time_select_a').text()) * 12;
            getLPRLoanRate()
        },500)
    })
    function getLPRLoanRate(){
        $.ajax({
            type: "POST",
            url: "/viewLoanFilter",
            data: {'month': monthLpr,'discount':1},
            success: function (data) {
                console.log(data)
                $("#rate_a").text(data.content.rate2)
                $("#rate_a").attr('rt1',data.content.rate2)
            }
        })
    }



    //修改箭头方向
    $(document).bind("click",function(e){
        $("#shoufu_rate").css('background-position','232px 14px')
        $("#loan_time_select_a").css('background-position','232px 14px')
        $("#jil").css('background-position','232px 14px')
        $(".dow").css('background-position','232px 14px')
    });
    $(".hide_box li a").click(function(event){
        $(".v6").find('span').css('background-position','232px 14px')
        $(".v5").find('span').css('background-position','232px 14px')
    })

    $(function () {

        //公积金贷款按面积
        (function () {
            var rate_input = $('#rate_a'),
                    fund_rate_data = {
                        date_20151024: [2.75, 3.25]
                    };

            //计算利率
            function setRate() {
                var $current = $('#original_rate').find('.selected_drop_item');
                var year = parseInt($('#loan_time_select_a').text()),
                        rate_result;
                if (year <= 5) {
                    rate_result = fund_rate_data['date_' + $current.attr('data-date')][0];
                } else {
                    rate_result = fund_rate_data['date_' + $current.attr('data-date')][1];
                }
                rate_input.val((rate_result * $current.attr('data-discount')).toFixed(2));//小数点后2位
            }

            //利率联动
            $('#original_rate, #loan_time_a').bind('select', function (event, current) {
                setRate();
            });

            //页面加载绑定第一个单选框
            $('#area_cal').trigger('click');
            if ($('#area_cal').prop('checked')) {
                setRate();
            }

            var applyUtil = {
                checkDanJia: function (danjia) {
                    danjia = $.trim(danjia);
                    if (getBytesLength(danjia) > 9 || getBytesLength(danjia) < 1 || isNaN(danjia) || parseFloat(danjia) > 999999) {
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $('#fund_area_a input[name=danjia]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_a input[name=danjia]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkMianJi: function (mianji) {
                    mianji = $.trim(mianji);
                    if (getBytesLength(mianji) > 9 || getBytesLength(mianji) < 1 || isNaN(mianji) || parseFloat(mianji) > 9999) {
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $('#fund_area_a input[name=mianji]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_a input[name=mianji]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkLiLuA: function (lilu_a) {
                    lilu_a = $.trim(lilu_a);
                    if (isNaN(lilu_a) || lilu_a == '' || lilu_a < 0 || parseInt(lilu_a) >= 100) {
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $('.error_msg[data-for="rate_a"]').css('visibility', 'visible');
                        //$('#fund_area_a input[name=lilu_a]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_a input[name=lilu_a]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkshoufu: function(shoufu) {
                    shoufu = $.trim(shoufu);
                    console.log(shoufu)
                    console.log(getBytesLength(shoufu))
                    const regex = /^\d+(\.\d{1,2})?$/;
                    if (!regex.test(shoufu) || getBytesLength(shoufu) < 1 || isNaN(shoufu) || parseFloat(shoufu) > 10 || parseFloat(shoufu) < 0) {
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $('#fund_area_a input[name=shoufu]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_a input[name=shoufu]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
            };

            $('.txt[data-type="number"]').focus(function () {
                $(this).removeClass('error_txt');
                $(this).parent('.guding').children('.error_msg').css('visibility', 'hidden');
            }).blur(function () {
                if ($(this).val() == '') {
                    $(this).addClass('error_txt');
                    $(this).parent('.guding').children('.error_msg').css('visibility', 'visible');
                }
            })

            function setSelectedItem(ul, selectedItem) {
                ul.children().each(function () {
                    if ($.trim($(this).text()) == selectedItem) {
                        $(this).children().trigger('click');
                        return false;
                    }
                });
            }

            $('.fangshi label').click(function () {
                if ($('.counter_right').css('display') != 'none') {
                    $('#fund_link').remove();
                }
            });
            var date=new Date;
            var year2=date.getFullYear();
            //清空重填
            $('#reset_area').click(function () {
                $('#first_house').attr('checked', 'checked');
                $('#fund_area_a input[name=danjia]').val('');
                $('#fund_area_a input[name=mianji]').val('');
                $('#fund_area_a input[name=lilu_a]').attr('value', '3.25');
                $('#shoufu').val('1.5')
                setSelectedItem($('#original_rate'), year2 +'最新基准利率');
                setSelectedItem($('#loan_time_a'), '30年（360期）');
                $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                $('[data-type="number"]').each(function () {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });
            });

            //北京市首付及利率

            function firstHousePlace() {
                //根据房屋面积判断
                $('input[name=mianji]').blur(function () {
                    var mianji = $(this).val();
                    if (parseInt(mianji) <= 90) {
                        setSelectedItem($('#shouFu'), '2成');
                        setSelectedItem($('#original_rate'), year2 +'最新基准利率');
                    }

                    if (parseInt(mianji) > 90) {
                        $('#shouFu li a').click(function () {
                            if ($(this).html() == '2成') {
                                setSelectedItem($('#shouFu'), '3成');
                            }
                        });
                    }
                });
            }

            //首套房限制
            if ($('input[name=goufangxingzhi]').val() == "1") {
                firstHousePlace();
            }
            $('.xingzhi #first_house').click(function () {
                $('#shoufu').val('3')
                setSelectedItem($('#original_rate'), year2 + '最新基准利率');
                firstHousePlace();
            });


            //二套房限制
//            $('.xingzhi #two_house').click(function () {
//                setSelectedItem($('#shouFu'), '4成');
//                setSelectedItem($('#original_rate'), '2017最新利率1.1倍');
//            });

            //点击计算按钮之后
            $('.Start_calculation_area').mousedown(function () {
                var danjia = $("#fund_area_a").find('input[name=danjia]').val();
                var mianji = $("#fund_area_a").find('input[name=mianji]').val();
                var lilu_a = $("#fund_area_a").find('input[name=lilu_a]').val();
                var shoufu = $("#fund_area_a").find('input[name=shoufu]').val();//首付

                // return false;
                if (!applyUtil.checkDanJia(danjia) || !applyUtil.checkMianJi(mianji)) {
                    return false;
                }
                if (!applyUtil.checkLiLuA(lilu_a)) {
                    return false;
                }
                //首付
                if (!applyUtil.checkshoufu(shoufu)){
                    return false;
                }

                $('#fund_link').remove();
                $('[data-type="number"]').each(function () {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });

                provident_area();

                if ($('.counter_right').css('display') === 'none') {
                    if (!$(this).parents().find(".counter_left").is(":animated")) {
                        $('.counter_left').animate({
                            'margin-left': '0px'
                        }, 500, function () {
                            $('.arrows_ico').show();
                            $('.counter_right').fadeIn();
                            $('.focus_counter').addClass('focus_counter_ed');
                        });
                    };
                };
                //添加统计正确计算结果的代码
                // deleted by sunlong
                // addRecord(2);
            });

            //在GBK编码里，除了ASCII字符，其它都占两个字符宽
            function getBytesLength(str) {
                return str.replace(/[^\x00-\xff]/g, 'xx').length;
            };

            function setBytesLength(str, num) {
                var len = 0;
                for (var i = 0; i < str.length; i++) {
                    if (str.charCodeAt(i) > 128) {
                        len = len + 2;
                    } else {
                        len = len + 1;
                    }
                    if (len == num) {
                        i = i + 1;
                        return i;
                    }
                }
            };
        })()
    });

    function provident_area() {
        var month = parseInt($('#loan_time_select_a').text()) * 12;
        var price = parseFloat($('#danjia').val());
        var area = parseFloat($('#mianji').val());
        // var shoufu_rate = parseInt($('#shoufu').val());//首付 默认3
        var shoufu_rate = $('#shoufu').val();//首付 默认3
        var lilv = $('#rate_a').val() / 100; //得到利率
        console.log(lilv)

        //------------ 根据单价面积计算

        //房款总额
        var fangkuan_total = Math.round(price * area * 100) / 100;
        $('#total_price_bx, #total_price_bj').text(fangkuan_total);
        //贷款总额
        var daikuan_total = fangkuan_total * (1 - shoufu_rate / 10);
        $('#total_loan_bx, #total_loan_bj').text(Math.round(daikuan_total * 100) / 100);
        //首期付款
        var money_first = fangkuan_total - daikuan_total;
        $('#first_pay_bx, #first_pay_bj').text(Math.round(money_first * 100) / 100);
        $('#loan_month_bx, #loan_month_bj').text(month);

        //1.本金还款
        //月还款
        var first_pay = 0;
        var all_total2 = 0;
        var month_money2 = "<tr>";
        var mingxi = '<table cellpadding="0" cellspacing="0" border="0" class="popup_table" ><tbody>';
        for (j = 0; j < month; j++) {
            //调用函数计算: 本金月还款额
            huankuan = getMonthMoney2(lilv, daikuan_total, month, j);
            all_total2 += huankuan;
            if (j == 0)
                first_pay += huankuan;
            huankuan = Math.round(huankuan * 100) / 100;
            //month_money2 += (j+1) +"月," + huankuan + "(元)\n";
            month_money2 += '<td>第' + (j + 1) + '期<br>' + huankuan + '元</td>';
            if ((j + 1) % 4 == 0) {
                if (j == month - 1) month_money2 += '</tr>';
                else month_money2 += '</tr><tr>';
            }
        }
        mingxi += month_money2 + '</tbody></table>';
        $('#average_pay_bj').text(Math.round(first_pay * 100) / 100 + "元");
        //还款总额
        $('#total_pay_bj').text(Math.round(all_total2 * 100) / 100);
        //支付利息款
        $('#interest_bj').text(Math.round((all_total2 - daikuan_total) * 100) / 100);
        $('#popup_one').children().children('.popup_bd').html(mingxi);


        //2.本息还款
        //月均还款
        var month_money1 = getMonthMoney1(lilv, daikuan_total, month); //调用函数计算
        $('#average_pay_bx').text(Math.round(month_money1 * 100) / 100);
        //还款总额
        var all_total1 = month_money1 * month;
        $('#total_pay_bx').text(Math.round(all_total1 * 100) / 100);
        //支付利息款
        $('#interest_bx').text(Math.round((all_total1 - daikuan_total) * 100) / 100);
    }

    //本金还款的月还款额(参数: 年利率 / 贷款总额 / 贷款总月份 / 贷款当前月0～length-1)
    function getMonthMoney2(lilv, total, month, cur_month) {
        var lilv_month = lilv / 12; //月利率
        var benjin_money = total / month;
        return (total - benjin_money * cur_month) * lilv_month + benjin_money;

    }

    //本息还款的月还款额(参数: 年利率/贷款总额/贷款总月份)
    function getMonthMoney1(lilv, total, month) {
        var lilv_month = lilv / 12; //月利率
        return total * lilv_month * Math.pow(1 + lilv_month, month) / (Math.pow(1 + lilv_month, month) - 1);
    }

    $(function () {
        //公积金贷款按贷款额度
        (function () {
            var rate_input = $('#rate_b'),
                    fund_rate_data = {
                        date_20101020: [3.5, 4.05],
                        date_20101226: [3.75, 4.3],
                        date_20110209: [4, 4.5],
                        date_20110406: [4.2, 4.7],
                        date_20110707: [4.45, 4.9],
                        date_20120608: [4.2, 4.7],
                        date_20120706: [4, 4.5],
                        date_20141122: [3.75, 4.25],
                        date_20150301: [3.5, 4.05],
                        date_20151024: [2.75, 3.25]
                    };
            //计算利率
            function setRate() {
                var $current = $('#original_rate_b').find('.selected_drop_item');
                var year = parseInt($('#loan_time_select_b').text()),
                        rate_result;
                if (year <= 5) {
                    rate_result = fund_rate_data['date_' + $current.attr('data-date')][0];
                } else {
                    rate_result = fund_rate_data['date_' + $current.attr('data-date')][1];
                }
                rate_input.val((rate_result * $current.attr('data-discount')).toFixed(2));
            }

            //利率联动
            $('#original_rate_b, #loan_time_b').bind('select', function (event, current) {
                setRate();
            });

            var applyUtil_b = {
                checkzongE: function (zonge) {
                    zonge = $.trim(zonge);
                    if (getBytesLength(zonge) > 9 || getBytesLength(zonge) < 1 || isNaN(zonge) || parseFloat(zonge) > 99999) {
                        $('#fund_area_b').find('.error_msg').css('visibility', 'hidden');
                        $('#fund_area_b input[name=zonge]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_b input[name=zonge]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkLiLuB: function (lilu_b) {
                    lilu_b = $.trim(lilu_b);
                    if (isNaN(lilu_b) || lilu_b == '' || lilu_b < 0 || parseInt(lilu_b) >= 100) {
                        $('#fund_area_b').find('.error_msg').css('visibility', 'hidden');
                        $('.error_msg[data-for="rate_b"]').css('visibility', 'visible');
                        //$('#fund_area_b input[name=lilu_b]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_b input[name=lilu_b]').addClass('error_txt');
                        return false;
                    }
                    return true;
                }
            };

            $('.txt[data-type="number"]').focus(function () {
                $(this).removeClass('error_txt');
                $(this).parent('.guding').children('.error_msg').css('visibility', 'hidden');
            }).blur(function () {
                if ($(this).val() == '') {
                    $(this).addClass('error_txt');
                    $(this).parent('.guding').children('.error_msg').css('visibility', 'visible');
                }
            })

            function setSelectedItem(ul, selectedItem) {
                ul.children().each(function () {
                    if ($.trim($(this).text()) == selectedItem) {
                        $(this).children().trigger('click');
                        return false;
                    }
                });
            }

            //清空重填
            $('#reset_rate').click(function () {
                var date=new Date;
                var year2=date.getFullYear();
                $('#fund_area_b input[name=zonge]').val('');
                $('#fund_area_b input[name=lilu_b]').attr('value', '3.25');
                setSelectedItem($('#original_rate_b'), year2 + '最新基准利率');
                setSelectedItem($('#loan_time_b'), '30年（360期）');
                $('#shoufu').val('1.5')
                $('[data-type="number"]').each(function () {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });
            });

            $('.fangshi label').click(function () {
                if ($('.counter_right').css('display') != 'none') {
                    $('#fund_link').remove();
                }
            });

            //点击计算按钮之后
            $('.Start_calculation_rate').mousedown(function () {
                var zonge = $("#fund_area_b").find('input[name=zonge]').val();
                var lilu_b = $("#fund_area_b").find('input[name=lilu_b]').val();

                // return false;
                if (!applyUtil_b.checkzongE(zonge)) {
                    return false;
                }
                if (!applyUtil_b.checkLiLuB(lilu_b)) {
                    return false;
                }

                $('.ping_gu').hide();
                $('#fund_link').remove();
                $('[data-type="number"]').each(function () {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });

                provident_num();

                if ($('.counter_right').css('display') === 'none') {
                    if (!$(this).parents().find(".counter_left").is(":animated")) {
                        $('.counter_left').animate({
                            'margin-left': '0px'
                        }, 500, function () {
                            $('.arrows_ico').show();
                            $('.counter_right').fadeIn();
                            $('.focus_counter').addClass('focus_counter_ed02');
                        });
                    };
                };
                //添加统计正确计算结果的代码
                // deleted by sunlong
                // addRecord(2);
            });

            //在GBK编码里，除了ASCII字符，其它都占两个字符宽
            function getBytesLength(str) {
                return str.replace(/[^\x00-\xff]/g, 'xx').length;
            };

            function setBytesLength(str, num) {
                var len = 0;
                for (var i = 0; i < str.length; i++) {
                    if (str.charCodeAt(i) > 128) {
                        len = len + 2;
                    } else {
                        len = len + 1;
                    }
                    if (len == num) {
                        i = i + 1;
                        return i;
                    }
                }
            };
        })()
    });

    function provident_num() {
        var month = parseInt($('#loan_time_select_b').text()) * 12;
        var lilv = $('#rate_b').val() / 100; //得到利率
        //------------ 根据贷款总额计算

        //房款总额
        $('#total_price_bx, #total_price_bj, #first_pay_bx, #first_pay_bj').parent().parent().remove();
        //贷款总额
        //var daikuan_total = fmobj.daikuan_total000.value;
        var daikuan_total = parseFloat($('#loan_money').val()) * 10000;
        $('#total_loan_bx, #total_loan_bj').text(Math.round(daikuan_total * 100) / 100);
        $('#loan_month_bx, #loan_month_bj').text(month);

        //1.本金还款
        //月还款
        var first_pay = 0;
        var all_total2 = 0;
        var month_money2 = "<tr>";
        var mingxi = '<table cellpadding="0" cellspacing="0" border="0" class="popup_table" ><tbody>';
        for (j = 0; j < month; j++) {
            //调用函数计算: 本金月还款额
            huankuan = getMonthMoney2(lilv, daikuan_total, month, j);
            all_total2 += huankuan;
            if (j == 0)
                first_pay += huankuan;
            huankuan = Math.round(huankuan * 100) / 100;
            //month_money2 += (j+1) +"月," + huankuan + "(元)\n";
            month_money2 += '<td>第' + (j + 1) + '期<br>' + huankuan + '元</td>';
            if ((j + 1) % 4 == 0) {
                if (j == month - 1) month_money2 += '</tr>';
                else month_money2 += '</tr><tr>';
            }
        }
        mingxi += month_money2 + '</tbody></table>';
        $('#average_pay_bj').text(Math.round(first_pay * 100) / 100 + "元");
        //还款总额
        $('#total_pay_bj').text(Math.round(all_total2 * 100) / 100);
        //支付利息款
        $('#interest_bj').text(Math.round((all_total2 - daikuan_total) * 100) / 100);
        $('#popup_one').children().children('.popup_bd').html(mingxi);


        //2.本息还款
        //月均还款
        var month_money1 = getMonthMoney1(lilv, daikuan_total, month); //调用函数计算
        $('#average_pay_bx').text(Math.round(month_money1 * 100) / 100);
        //还款总额
        var all_total1 = month_money1 * month;
        $('#total_pay_bx').text(Math.round(all_total1 * 100) / 100);
        //支付利息款
        $('#interest_bx').text(Math.round((all_total1 - daikuan_total) * 100) / 100);
    }

    //本金还款的月还款额(参数: 年利率 / 贷款总额 / 贷款总月份 / 贷款当前月0～length-1)
    function getMonthMoney2(lilv, total, month, cur_month) {
        var lilv_month = lilv / 12; //月利率
        var benjin_money = total / month;
        return (total - benjin_money * cur_month) * lilv_month + benjin_money;

    }

    //本息还款的月还款额(参数: 年利率/贷款总额/贷款总月份)
    function getMonthMoney1(lilv, total, month) {
        var lilv_month = lilv / 12; //月利率
        if(lilv==0){
            return total / month
        }else{
            return total * lilv_month * Math.pow(1 + lilv_month, month) / (Math.pow(1 + lilv_month, month) - 1);
        }
    }
</script>

<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>

<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>

</html>
