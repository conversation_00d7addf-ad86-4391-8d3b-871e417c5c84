<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳地图找房，沈阳房产地图，沈阳楼盘地图 - 房小二网</title>
    <meta name="keywords" content="沈阳楼盘地图,沈阳地图找房,沈阳房产地图,沈阳楼市地图"/>
    <meta name="description" content="房小二网地图找房为您提供更新的沈阳楼盘地图信息，全面的沈阳新楼盘位置及沈阳新房出售相关信息。通过简单方便的地图找房功能，使您更加方便地找到目标区域的新楼盘信息，为您创造更好的买房体验，查找沈阳新楼盘，就来房小二网地图找房。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/housemap1.htm">
    <link rel="stylesheet" type="text/css" href="//static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
<!--    <script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <link rel="stylesheet" href="//api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <script src="//static.fangxiaoer.com/js/baiduMap/ajax.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/baiduMap/baiduMap1.css?v=20230512" />
    <!--<link rel="stylesheet" type="text/css" href="/css/baiduMap1.css?v=20180605" />-->
   <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/map/map_wei.css?v=20230512" />
    <script src="//static.fangxiaoer.com/js/baiduMap/vue.js" type="text/javascript" charset="utf-8"></script>
    <script src="//static.fangxiaoer.com/js/baiduMap/subWayLine.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
</head>
<style>
.showRegion span {
    width: 60px;
    margin-left: 0;
    margin: auto;
}
#region option{
	color: #000 !important;
}
.mapList .mapSelect>div>ul li {
    background-color: #fff;
    width: 94px;
    padding: 0 5px !important;
    line-height: 34px !important;
    height: 34px !important;
    padding-left: 8px !important;
}

.Choice {
    padding-top: 10px;
    border-bottom: 1px solid #ededed;
    /*width: 477px;*/
    background: #fff;
}
.Choice p {
    float: left;
    line-height: 24px;
    border: 1px solid #e6e5e5;
    padding-right: 1px;
    margin-right: 8px;
    width: 86px;
    margin-bottom: 10px;
}
.Choice p img {
    margin-left: 5px;
    padding-top: 1px;
    display: block;
    float: left;
    cursor: pointer;
}
.Choice .box {
    float: left;
    width: 426px;
}
    /*登录弹出窗口 用户名输入框*/
    input#Txt_LoginName.fxe_mobile{
        width:275px!important;
    }
    .signup-form .yzm{
        border: 0;
        border-left: 1px solid #ccc;
        width: auto;
        background-color: #fff;
    }
    .showRegion span{
    	width: auto;
    	    margin-left: 0;
    }
    .vru{ width: 34px !important; height: 34px; position: absolute; left: 15px; top: 66px;
        background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0; padding-left: unset !important; margin-top: unset !important;}
</style>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<div id="map"  v-bind:style="{height: height+'px',width:windowWidth+'px'}">

    <div class="mapNav">
        <ul>
            <li v-bind:class="{hover: navIsActive==0 }" @click="navLeft(0)"><i class="img1"></i>找2楼盘</li>
            <!--<li v-bind:class="{hover: navIsActive==1 }" @click="navLeft(1)"><i class="img2"></i>低首付</li>-->
            <li v-bind:class="{hover: navIsActive==2 }" @click="navLeft(2)"><i class="img3"></i>现房</li>
            <li v-bind:class="{hover: navIsActive==3 }" @click="navLeft(3)"><i class="img4"></i>学区</li>
            <li v-bind:class="{hover: navIsActive==4 }" @click="navLeft(4)"><i class="img5"></i>地铁</li>
        </ul>
    </div>

    <div class="mapList" v-bind:style="{width:listwidth+'px'}" style="overflow: visible">
        <div id="search2017">
            <!--搜索栏-->
            <input id="txtkeys"  type="text" v-if="navIsActive!=3" v-bind:name="navIsActive==0 ? '':(navIsActive==2?1:(navIsActive==3?2:1))"  placeholder="请输入区域/楼盘名称" class="ac_input">
           <input id="txtkeys"  type="text" v-if="navIsActive==3"  placeholder="请输入楼盘名称" class="ac_input">
            <img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png" @click="deleteSearchValue()" alt="x" style="display: none">
            <input type="button" value="搜 索" class="btn_search search_btn">
            <div class="cl"></div>
        </div>
        <div class="mapSelect">
            <div class="region"   @mouseover="showSelectList=0;"  @mouseout="showSelectList=-1;">
                <p >{{ regionList.ActiveText }}</p>
                <ul v-bind:style="{display: (showSelectList==0 ? 'block':'none')}" >
                    <li @click="navNormal(regionList,'区域不限')">不限</li>
                    <li v-for="vregion in regionList.content" @click="navSelect(regionList,vregion.regionName,vregion.regionId);showHouseForSearch(2)">{{ vregion.regionName }}</li>
                </ul>
            </div>

            <div class="type" v-if="navIsActive!=3"  @mouseover="showSelectList=1;"  @mouseout="showSelectList=-1;">
                <p>{{ typeList.ActiveText.replace("商业","商铺").replace("公寓","写字楼") }}</p>
                <ul v-bind:style="{display: (showSelectList==1 ? 'block':'none')}">
                    <li @click="navNormal(typeList,'类型不限')">不限</li>
                    <li v-for="vtype in typeList.content" @click="navSelect(typeList,vtype.name,vtype.id);showHouseForSearch(2)">{{ vtype.name.replace("商业","商铺").replace("公寓","写字楼") }}</li>
                </ul>
            </div>
            <div class="type" v-if="navIsActive==3"  @mouseover="showSelectList=1;"  @mouseout="showSelectList=-1;">
                <p>{{ typeList.ActiveText.replace("商业","商铺") }}</p>
                <ul v-bind:style="{display: (showSelectList==1 ? 'block':'none')}">
                    <li @click="navNormal(typeList,'类型不限')">不限</li>
                    <li v-for="vtype in typeList.content" v-if="vtype.name!='写字楼'" @click="navSelect(typeList,vtype.name,vtype.id);showHouseForSearch(2)">{{ vtype.name.replace("商业","商铺").replace("公寓","写字楼") }}</li>
                </ul>
            </div>
              <div class="school" v-if="navIsActive==3" @mouseover="showSelectList=5;"  @mouseout="showSelectList=-1;">
                <p>{{ schoolList.ActiveText }}</p>
                <ul v-bind:style="{display: (showSelectList==5 ? 'block':'none')}" >
                    <li @click="navNormal(schoolList,'类别不限')">不限</li>
                    <li v-for="vschool in schoolList.content" @click="navSelect(schoolList,vschool.name,vschool.id);showHouseForSearch(2)">{{ vschool.name }}</li>
                </ul>
            </div>
             <div class="selectSchool" style="display: none;" v-if="navIsActive==3" >
                <p>{{ selectSchool.schoolName }}</p>

            </div>
            <div class="price" v-if="navIsActive==0||navIsActive==1||navIsActive==2"  @mouseover="showSelectList=2;"  @mouseout="showSelectList=-1;">
                <p >{{ priceList.ActiveText }}</p>
                <ul v-bind:style="{display: (showSelectList==2 ? 'block':'none')}" style="overflow: auto;">
                    <li style="width: 120px;" @click="navNormal(priceList,'价格不限')">不限</li>
                    <li style="width: 120px;" v-for="vprice in priceList.content" @click="navSelect(priceList,vprice.name,vprice.id);showHouseForSearch(2)">{{ vprice.name }}</li>
                </ul>
            </div>
            <!--销售状态-->
            <div class="salesStatus" v-if="navIsActive==0||navIsActive==1||navIsActive==2||navIsActive==3" @mouseover="showSelectList=7;"  @mouseout="showSelectList=-1;">
                <p>{{ salesStatus.ActiveText }}</p>
                <ul v-bind:style="{display: (showSelectList==7 ? 'block':'none')}">
                    <li @click="navNormal(salesStatus,'状态','0')">不限</li>
                    <li v-for="vstatus in salesStatus.content" @click="navSelect(salesStatus,vstatus.name,vstatus.id,'0');showHouseForSearch(2)">{{ vstatus.name }}</li>
                </ul>
            </div>

            <div class="subWay" v-if="navIsActive==4" @mouseover="showSelectList=6;"  @mouseout="showSelectList=-1;">
                <p >{{ subWayList.ActiveText }}</p>
                <ul v-bind:style="{display: (showSelectList==6 ? 'block':'none')}" >
                    <li @click="navNormal(subWayList,'地铁不限')">不限</li>
                    <li v-for="vsubWay in subWayList.content" @click="navSelect(subWayList,vsubWay.subWayName,vsubWay.subWayId);showHouseForSearch(2)">{{ vsubWay.subWayName }}</li>
                </ul>
            </div>
            <div class="cl"></div>
        </div>
        <div class="Choice" v-if="subWayList.ActiveId != ''||schoolList.ActiveId !=''||priceList.ActiveId != ''||typeList.ActiveId != ''||regionList.ActiveId != ''||searchValue != ''||salesStatus.ActiveId != ''">
            <h1>已选：</h1>
            <div class="box">
                <p @click="navNormal(salesStatus,'状态','0')" v-if="(navIsActive==0||navIsActive==1||navIsActive==2||navIsActive==3) && salesStatus.ActiveId != ''">
                    <label v-bind:title="  salesStatus.ActiveText ">{{ salesStatus.ActiveText}}</label>
                    <img src="https://static.fangxiaoer.com/web/images/ico/map/close.png">
                </p>
                <p @click="deleteSearchValue();" v-if="searchValue != ''"><label v-bind:title="  searchValue ">{{searchValue}}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <p @click="navNormal(subWayList,'地铁')" v-if="navIsActive==4 && subWayList.ActiveId != ''"><label v-bind:title="  subWayList.ActiveText ">{{ subWayList.ActiveText}}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <p  @click="navNormal(schoolList,'类别')" v-if="navIsActive==3 && schoolList.ActiveId !=''"><label v-bind:title="  schoolList.ActiveText ">{{  schoolList.ActiveText }}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <p  @click="navNormal(priceList,'价格')" v-if="(navIsActive==0||navIsActive==1||navIsActive==2)&& priceList.ActiveId != ''"><label v-bind:title=" priceList.ActiveText.replace('元/m²','')  ">{{ priceList.ActiveText.replace("元/m²","") }}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <p  @click="navNormal(typeList,'类型')" v-if="typeList.ActiveId != ''"><label v-bind:title=" typeList.ActiveText.replace('商业','商铺').replace('公寓','写字楼')  ">{{ typeList.ActiveText.replace("商业","商铺").replace("公寓","写字楼") }}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <p @click="navNormal(regionList,'区域')" v-if="regionList.ActiveId != ''"><label v-bind:title=" regionList.ActiveText  ">{{  regionList.ActiveText  }}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <p @click="navNormal(selectSchool,'学校名')" v-if="selectSchool != '' && selectSchool != null"><label v-bind:title=" selectSchool.schoolName  ">{{  selectSchool.schoolName  }}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <div class="cancelAll" @click="deleteAllValue();">清空</div>
            </div>
            <div class="cl"></div>
        </div>
<!--        <div v-bind:style="{height: houseListheigth-106+'px'}" class="mapHouseList  HouseList">-->
        <div v-bind:style="{height: salesStatus.ActiveId != ''?(houseListheigth-153+'px'):(houseListheigth-106+'px')}" class="mapHouseList  HouseList test-2">
<!--    新地图-->
            <ul>
                <li class="jump" v-for="vnewHousList in newHousList" v-on:mouseover="relasionShow(vnewHousList.projectId,1)"  v-on:mouseout="relasionShow(vnewHousList.projectId,2)" v-cloak="jump">
                    <a v-if="map.getZoom()-gapZoom<=14" v-bind:href="'https://sy.fangxiaoer.com/house/'+ vnewHousList.projectId +'-'+vnewHousList.projectType+'.htm'" target="_blank" style="display: block; height: 100%; position: relative;">
                        <img v-bind:src="vnewHousList.ImageUrl" alt="" />
                        <div >
                            <h1>{{vnewHousList.projectName}}</h1>
                            <em class="ics1" v-if="vnewHousList.projectStatus==1"></em>
                            <em class="ics2" v-if="vnewHousList.projectStatus==2"></em>
                            <em class="ics3" v-if="vnewHousList.projectStatus==3"></em>
<!--                            <span v-if="vnewHousList.projectStatus==3" class="solded">售罄</span>-->
                            <div class="bw"  style="clear: both"> </div>
                            <p v-bind:title="vnewHousList.projectAddress">{{vnewHousList.projectAddress}}</p>
                            <b v-if="navIsActive!=3 && vnewHousList.projectStatus!=3 && vnewHousList.projectPrice != undefined && vnewHousList.projectPrice.length!=0" v-for="price in vnewHousList.projectPrice">{{price.name}}{{price.type}}：<span>{{price.showPrice}}</span></b>
                            <b v-if="navIsActive!=3 && vnewHousList.projectStatus!=3 && (vnewHousList.projectPrice == undefined || vnewHousList.projectPrice.length==0)"><span>待定</span></b>
                            <b v-if="navIsActive!=3 && vnewHousList.projectStatus==3 &&  vnewHousList.projectType != 2  && vnewHousList.subPrice != undefined && vnewHousList.subPrice!=null&&vnewHousList.subPrice.price!=0" >二手房均价：<span>{{vnewHousList.subPrice.price}}元/㎡</span></b>

                        	<b v-if="navIsActive==3 && vnewHousList.projectStatus!=3 && vnewHousList.price != undefined && vnewHousList.price.length!=0 && vnewHousList.price!=null" v-for="price in vnewHousList.price">{{price.name}}{{price.type}}：<span>{{price.showPrice}}</span></b>
                            <b v-if="navIsActive==3 && vnewHousList.projectStatus!=3 && (vnewHousList.price == undefined || vnewHousList.price.length==0)"><span>待定</span></b>
                            <b v-if="navIsActive==3 && vnewHousList.projectStatus==3 &&  vnewHousList.projectType != 2  && vnewHousList.subPrice != undefined && vnewHousList.subPrice!=null&&vnewHousList.subPrice.price!=0" >二手房均价：<span>{{vnewHousList.subPrice.price}}元/㎡</span></b>

                        </div>
                        <div class="vru" v-if="vnewHousList.hasPan == '1'"></div>
                    </a>
                    <a v-if="map.getZoom()-gapZoom>14" v-bind:href="'https://sy.fangxiaoer.com/house/'+ vnewHousList.projectId +'-'+vnewHousList.projectType+'.htm'" target="_blank">
                        <img v-bind:src="vnewHousList.ImageUrl" alt="" />
                        <div>
                            <h1>{{vnewHousList.projectName}}</h1>
                            <em class="ics1" v-if="vnewHousList.projectStatus==1"></em>
                            <em class="ics2" v-if="vnewHousList.projectStatus==2"></em>
                            <em class="ics3" v-if="vnewHousList.projectStatus==3"></em>
<!--                            <span v-if="vnewHousList.projectStatus==3" class="solded">售罄</span>-->
                            <div class="bw"  style="clear: both"> </div>
                            <p v-bind:title="vnewHousList.projectAddress">{{vnewHousList.projectAddress}}</p>
                            <b v-if="navIsActive!=3 && vnewHousList.projectStatus!=3 && vnewHousList.projectPrice != undefined && vnewHousList.projectPrice.length!=0" v-for="price in vnewHousList.projectPrice">{{price.name}}{{price.type}}：<span>{{price.showPrice}}</span></b>
                            <b v-if="navIsActive!=3 && vnewHousList.projectStatus!=3 && (vnewHousList.projectPrice == undefined || vnewHousList.projectPrice.length==0)"><span>待定</span></b>
                            <b v-if="navIsActive!=3 && vnewHousList.projectStatus==3 &&  vnewHousList.projectType != 2  && vnewHousList.subPrice != undefined && vnewHousList.subPrice!=null &&vnewHousList.subPrice.price!=0" >二手房均价：<span>{{vnewHousList.subPrice.price}}元/㎡</span></b>

							<b v-if="navIsActive==3 && vnewHousList.projectStatus!=3 && vnewHousList.price != undefined && vnewHousList.price.length!=0 && vnewHousList.price!=null" v-for="price in vnewHousList.price">{{price.name}}{{price.type}}：<span>{{price.showPrice}}</span></b>
                            <b v-if="navIsActive==3 && vnewHousList.projectStatus!=3 && (vnewHousList.price == undefined || vnewHousList.price.length==0 )"><span>待定</span></b>
                            <b v-if="navIsActive==3 && vnewHousList.projectStatus==3 &&  vnewHousList.projectType != 2  && vnewHousList.subPrice != undefined && vnewHousList.subPrice!=null &&vnewHousList.subPrice.price!=0" >二手房均价：<span>{{vnewHousList.subPrice.price}}元/㎡</span></b>

                        </div>
                    </a>
                </li>
            </ul>
            <div id="recommentOrder" v-bind:style="{display: recommentSwitch}" >
                <p id="recommentT1"></p>
                <p id="recommentT2"></p>
                <div class="zsfw">
                    <h1><span></span>帮您找房</h1>
                    <ul>
                        <li>
                            <span><label>*</label>您想在哪里买</span>
                            <div>
                                <select id="region" >
                                    <option >请选择区域</option>
                                    <option v-for="vregion in regionList.content" v-bind:selected="vregion.regionName == regionList.ActiveText">{{ vregion.regionName }}</option>
                                </select>
                            </div>
                            <div class="cl"></div>
                        </li>
                        <li class="new_huxing">
                            <span><label>*</label>您的价格预算</span>
                            <div>
                                <select id="new_yusuan">
                                    <option>请选择价格</option>
                                    <option>35万以下</option>
                                    <option>35-50万</option>
                                    <option>50-80万</option>
                                    <option>80-100万</option>
                                    <option>100-120万</option>
                                    <option>120-150万</option>
                                    <option>150万以上</option>
                                </select>
                            </div>

                            <div class="cl"></div>
                        </li>
                        <li class="new_yusuan">
                            <span><label>*</label>您想买几居室</span>
                            <div>
                                <select id="new_huxing" >
                                    <option>请选择户型</option>
                                    <option>一居</option>
                                    <option>二居</option>
                                    <option>三居</option>
                                    <option>四居</option>
                                    <option>五居及以上</option>
                                </select>
                            </div>
                            <div class="cl"></div>
                        </li>
                        <li>

                            <span><label>*</label>您的手机号码</span>
                            <input type="tel" id="phone" class="fxe_mobile" placeholder="请输入手机号" maxlength="11" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') ">
                            <input type="hidden" id="type" value="1">
                        </li>
                        <li>
                            <input type="tel" id="code" class="fxe_messageCode"  maxlength="6" style="width: 120px;" placeholder="请输入验证码"/>
                            <p id="hqyzm" class="fxe_ReSendValidateCoad" >获取验证码</p>
                            <p class="fxe_validateCode"></p>
                        </li>
                        <li class="describe">
                            <span>描&nbsp;&nbsp;&nbsp;述</span>
                            <textarea id="describe" placeholder="补充说点什么吧······"></textarea>
                        </li>


                        <b class="btn" id="map_submit">提交</b>
                        <!--<li style="color: #999;text-align:left;font-size: 12px;">若正确提交即同意接受以电话、短信等方式推荐我司业务相关的信息</li>-->
                        <div class="checkagreeInput" style="width: 92%;margin: 10px auto 10px auto;">
                            <div style="font-size:12px;">提交即代表同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                        </div>
                    </ul>
                </div>

            </div>
        </div>
        <img id="loading" src="https://static.fangxiaoer.com/web/images/sy/map/loading.gif" alt="加载中……" />
    </div>
    <div id="baiduMap" v-bind:style="{width:mapWidth+'px'}">
    </div>
</div>
<div class="hint" id="hint" style="display: none;">
    <dl>
        <dd><p>您的浏览器版本过低，<br>建议升级浏览器！</p></dd>
        <dd>
            <img src="https://static.fangxiaoer.com/web/images/sy/map/info.png"/>
        </dd>
        <dd>
            <a href="/static/oldHouseMap.htm">查看低版本地图</a>
            <a href="https://pc.uc.cn/" target="_blank" style="color: #ff5200;" id="Dherf">升级浏览器</a>
        </dd>
    </dl>
</div>
<script>
    var type=1;
    var navType=0;
    //如果是safari浏览器显示不支持
    //    if(navigator.userAgent.indexOf("Safari") > -1 && navigator.userAgent.indexOf("Chrome") < 1){
    //        document.getElementById("hint").style.display="block";
    //    }
    //如果是ie显示不支持
    if(navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > -1 && !(navigator.userAgent.indexOf("Opera") > -1) ){
        document.getElementById("hint").style.display="block";
    }
</script>
<!--<script src="//static.fangxiaoer.com/js/baiduMap/baiduMap1.js?v=20180605" type="text/javascript" charset="utf-8"></script>-->
<!--生产环境-->
<script src="https://static.fangxiaoer.com/js/map/js/baiduNewHouswMap.js?v=20230512" type="text/javascript" charset="utf-8"></script>
<!--本地测试-->
<!--<script src="/js/baiduNewHouswMap.js" type="text/javascript" charset="utf-8"></script>-->
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<script>
    //html获取链接上面的参数
    function getQueryString(name){
        var currentType = "";
        var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r!=null) currentType= r[2];
        if(currentType != ""){
            baiduMap.navLeft(parseInt(currentType));
        }
    }
    window.onload =  getQueryString("type")
</script>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/map/js/searchForMap.js?v=20230512"></script>
</body>
<style>
</style>
<script>
    var unSelected = "#999";
    var selected = "#333";

        $("select").css("color", unSelected);
        $("option").css("color", selected);
        $("select").change(function () {
            var selItem = $(this).val();
            if (selItem == $(this).find('option:first').val()) {
                $(this).css("color", unSelected);
            } else {
                $(this).css("color", selected);
            }
        });

function  jump() {
    alert(111)
}
</script>
</html>