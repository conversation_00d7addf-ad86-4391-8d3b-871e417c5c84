<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>税费计算器 - 房小二网</title>
    <meta name="keywords" content="沈阳贷款,房贷,沈阳公积金贷款,沈阳商业贷款,提前还款，购房能力评估"/>
    <meta name="description" th:content="'房小二网购房工具为购房者提供'+${yearTime}+'年房贷计算器及各种在线购房计算工具。包括房贷计算器，公积金贷款计算器，商业贷款计算器，组合贷款计算器，房贷提前还贷计算器，税费计算器，购房能力评估，公积金贷款额度评估等服务。'"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link type="text/css" rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/tool/jsq.css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
</head>

<body class="w1210">
<form name="Form1" method="post" action="shuifei.aspx" id="Form1">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav"></div>
    <!--页面位置-->
    <div class="w place">您的位置：<a href="/">沈阳房产网</a> &gt;  税费计算器</div>

    <div class="w wrap_nav">
        <a href="business.htm">商业贷款计算器</a>
        <a href="fund.htm" >公积金贷款计算器</a>
        <a href="assemble.htm">组合贷款计算器</a>
        <a href="advance.htm">提前还款计算器</a>
        <a href="tallage.htm" class="hover">税费计算器</a>
        <a href="ability.htm" >购房能力评估</a>
        <a href="fundAssess.htm">公积金贷款额度评估</a>
    </div>

    <div class="focus_counter clear">
        <div class="counter_left">
            <s class="arrows_ico"></s>
            <div class="counter_left_tt counter_moudle_tt">
                <h3>填写信息</h3>
            </div>
            <dl class="clear" style="z-index:3">
                <dt>住宅类型：</dt>
                <dd>
                    <input name="house_type" id="normal_house" type="radio" checked="checked" value="1">
                    <label for="normal_house" class="first_radio">普通住宅</label>
                    <input name="house_type" id="unnormal_house" type="radio" value="2">
                    <label for="unnormal_house">非普通住宅
                        <span class="tips_mark">
                            <i></i>
                            <div class="tips_div" style="display: none;">
                                <div class="tips_div_cen">
                                    <s></s>
                                    <div class="tips_div_tt">普通住宅的认定应具备以下三个条件：</div>
                                    <p>1.住宅小区建筑容积率在1.0（含）以上。<br>
                                        2.单套建筑面积在140m²（含）以下。<br>
                                        3.实际成交价低于同区域享受优惠政策住房平均交易价格的1.2倍
                                    </p>
                                </div>
                            </div>
                        </span>
                    </label>
                </dd>
            </dl>
            <!--购房性质-->
            <dl class="clear radio_wrapper" style="z-index:2;">
                <dt>购房性质：</dt>
                <dd>
                    <input name="purchase_type" id="first_house" type="radio" checked="checked" value="1">
                    <label for="first_house" class="first_radio">首套房</label>
                    <input name="purchase_type" id="second_house" type="radio" value="2">
                    <label for="second_house">二套房
                        <span class="tips_mark">
                                <i></i>
                                <div class="tips_div" style="display: none;">
                                    <div class="tips_div_cen">
                                        <s></s>
                                        <div class="tips_div_tt">二套房界定标准：</div>
                                        <p>
1.个人已通过贷款购买过一套（及以上）住房，且至少有一笔贷款尚未还清时，再次申请贷款购买住房的。<br>
2.夫妻两人,其中一人婚前购房贷款未还清时，婚后两人想要以夫妻名义共同贷款购买住房的。</p>
                                    </div>
                                </div>
                            </span>
                    </label>
                </dd>
            </dl>

            <div class="fanshi_bd">
                <!--房屋单价-->
                <dl class="clear margin_top_20">
                    <dt>房屋单价：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="house_price" maxlength="9" data-type="number" class="txt">
                        <em>元/m²</em>
                        <div class="error_msg" data-for="house_price">请输入正确的房屋单价</div>
                    </dd>

                </dl>
                <!--房屋面积-->
                <dl class="clear no_margin">
                    <dt>房屋面积：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="house_size" maxlength="9" data-type="number" class="txt">
                        <em>m²</em>
                        <div class="error_msg" data-for="house_size">请输入正确的房屋面积</div>
                    </dd>

                </dl>
                <!--计算结果-->
                <dl class="clear">
                    <dt></dt>
                    <dd class="jisuan">
                        <input type="button" class="Start_calculation" value="开始计算">
                        <a href="javascript:;" id="reset">清空重填</a>
                    </dd>
                </dl>
            </div>
        </div>
        <div class="counter_right">
            <div class="counter_right_tt counter_moudle_tt">
                <h3>计算结果</h3>
            </div>
            <div class="counter_gongjijin clear">
                <div class="c_gjj_a c_gjj_moudle">
                    <div class="c_gjj_moudle_bd">
                        <dl class="clear">
                            <dt>房屋总价</dt>
                            <dd class="result"><span id="total_price"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>契税</dt>
                            <dd class="result"><span id="deed_tax"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>印花税</dt>
                            <dd class="result"><span id="stamp_tax"></span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>税费总计</dt>
                            <dd class="result"><span id="total_tax"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                    </div>
                </div>

            </div>
            <div class="beizhu">
            </div>
        </div>
    </div>
    </div>
</form>




<script src="https://static.fangxiaoer.com/js/jsq/common.js"></script>
<script>
    $(function () {
        var inputs = $('.txt[data-type="number"]');

        function isTextValid() {
            var valid = true;
            inputs.each(function () {
                var $this = $(this);
                if ($this.is(':enabled') && ($.trim($(this).val()) === '' || isNaN($(this).val()))) {
                    $this.addClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'visible');
                    valid = false;
                }
                if (($this.attr('id') == 'house_price' && parseFloat($(this).val()) > 999999) || ($this.attr('id') == 'house_size' && parseFloat($(this).val()) > 9999)) {
                    $this.addClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'visible');
                    valid = false;
                }
            });
            return valid;
        }

        //清空重填
        $('#reset').click(function () {
            $('#normal_house, #first_house').attr('checked', 'checked');
            inputs.val('');
            $('[data-type="number"]').each(function () {
                var $this = $(this);
                $this.removeClass('error_txt');
                $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
            });
        });

        //开始计算
        $('.Start_calculation').mousedown(function () {
            var valid = true;
            if (!isTextValid()) {
                valid = false;
            }

            if (valid) {
                var deed_tax;
                var stamp_tax;
                var total_tax;
                var house_price = parseFloat($('#house_price').val());
                var house_size = parseFloat($('#house_size').val());
                var total_price = Math.round(house_price * house_size * 100) / 100;

                if ($('#first_house').is(':checked')) {
                    if ($('#normal_house').is(':checked')) {
                        if (house_size <= 90) deed_tax = total_price * 0.01;
                        else deed_tax = total_price * 0.015;
                    }
                    if ($('#unnormal_house').is(':checked')) {
                        deed_tax = total_price * 0.03;
                    }
                }
                if ($('#second_house').is(':checked')) {
                    if ($('#normal_house').is(':checked')) {
                        deed_tax = total_price * 0.015;
                    }
                    if ($('#unnormal_house').is(':checked')) {
                        deed_tax = total_price * 0.03;
                    }
                }

                $('#total_price').text(total_price);
                $('#deed_tax, #total_tax').text(Math.round(deed_tax * 100) / 100);
                $('#stamp_tax').text('免征');

                if ($('#prepay_all').prop('checked')) {
                    $('.c_gjj_b').hide();
                    $('.c_gjj_a h3').hide();
                } else {
                    $('.c_gjj_b').show();
                    $('.c_gjj_a h3').show();
                }
                if ($('.counter_right').css('display') === 'none') {
                    if (!$(this).parents().find(".counter_left").is(":animated")) {
                        $('.counter_left').animate({
                            'margin-left': '0px'
                        }, 500, function () {
                            $('.arrows_ico').show();
                            $('.counter_right').fadeIn();
                            $('.focus_counter').addClass('focus_counter_ed');
                        });
                    };
                };
                //添加统计正确计算结果的代码
                addRecord(5);
            }
        });
    });
</script>

<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
</body>

</html>

