<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta http-equiv="Pragma" content="no-cache">
		<title>沈阳二手房安心交易规则 - 房小二网</title>
		<meta name="keywords" content="二手房信息发布,个人房源,沈阳二手房信息,二手房中介,免费信息发布">
		<meta name="description" content="房小二网为您提供专业贴心的二手房服务，为您提供免费二手房信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。">
	</head>
	<style>
		* {
			margin: 0;
			padding: 0;
		}

		body {
			font-family: 'MicrosoftYaHei';
			height: 100%;
			width: 100%;
		}

		html {
			height: 100%;
		}

		a {
			text-decoration: none;
		}

		.main {
			width: 1280px;
			margin: 20px auto 0px auto;
			background: linear-gradient(0deg, rgba(252, 174, 5, 1) 0%, rgba(255, 114, 0, 1) 100%);
		}

		.modular {
			position: relative;
			width: 1000px;
			height: 434px;
			margin: 0 auto;
			margin-bottom: 90px;
			background: rgba(255, 255, 255, 1);
			box-shadow: 0px 0px 22px 2px rgba(157, 76, 3, 0.41);
		}

		.title_back {
			position: absolute;
			top: -30px;
			left: 0;
			right: 0;
			margin: 0 auto;
			width: 307px;
			height: 60px;
			background: linear-gradient(0deg, rgba(9, 9, 9, 1), rgba(96, 95, 95, 1));
			border-radius: 10px;
			transform: skew(-10deg);
			font-size: 34px;
			text-align: center;
			line-height: 60px;
			color: #fff;
		}

		.title_back .title {
			transform: skew(10deg);
		}

		.info {
			width: 766px;
			margin: 0 auto;
		}

		.info_title {
			padding-top: 80px;
			color: #222222;
			font-size: 18px;
			line-height: 34px;
			font-weight: Bold;
			text-align: center;


		}

		.info_list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-wrap: wrap;
			color: #333333;
			margin-top: 50px;
		}

		.info_item {
			width: 330px;
			font-size: 19px;
			margin-bottom: 24px;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.info_item_l {
			display: flex;
			align-items: center;
		}

		.chek {
			width: 20px;
			height: 20px;
			background: rgba(255, 118, 0, 1);
			border-radius: 50%;
			position: relative;
			margin-right: 20px;
		}

		.chek::after {
			position: absolute;
			content: "";
			width: 5px;
			height: 10px;
			top: 2px;
			left: 8px;
			border: 2px solid #fff;
			border-top: none;
			border-left: none;
			transform: rotate(45deg);
		}

		.font_bold {
			font-weight: Bold;
		}

		.baozhangBtn {
			width: 130px;
			height: 40px;
			background: rgba(234, 59, 64, 1);
			font-size: 16px;
			font-family: Microsoft YaHei;
			text-align: center;
			font-weight: bold;
			color: rgba(255, 255, 255, 1);
			line-height: 40px;
			display: block;
			margin: 0 auto;
		}

		.guide {
			font-size: 16px;
			color: #333;
			text-align: center;
			margin-top: 20px;
		}

		.bottom_item {
			margin-bottom: 36px;
			font-size: 19px;
			color: #42464C;
		}

		.logo_fangxiaoer {
			width: 145px;
			height: 53px;
			margin: 0 auto;
			padding: 34px 0;
		}
		.bottom_btn{
			padding-top:14px;
			padding-bottom: 34px;
			width: 100%;
			text-align: center;
			font-size: 14px;
			color: #000000;
		}
		.bottom_btn a{
			text-decoration: initial;
			color: #0000EE;
		}
	</style>
	<body>
		<div class="main">
			<div style="height: 500px;">
				<img src="https://static.fangxiaoer.com/web/images/sy/anxin/top_back.png" >
			</div>
			<!-- 模块一 -->
			<div class="modular">
				<div class="title_back">
					<div class="title">安心租房<span style="color: #FBCC2A;">保障</span></div>
				</div>
				<div class="info">
					<div class="info_title">
						<p>与安心经纪人交易，将由中国人民财产保险有限公司提供以下保障，发生事故后，</p>
						<p>若符合理赔标准由中国人民财产保险股份有限公司为您提供理赔服务。</p>
					</div>
					<div class="info_list">
						<div class="info_item">
							<div class="info_item_l">
								<div class="chek"></div>线下看房 保安全
							</div>
							<div><span class="font_bold">20</span>万元</div>
						</div>
						<div class="info_item">
							<div class="info_item_l">
								<div class="chek"></div>租金不涨 保租金
							</div>
							<div><span class="font_bold">1</span>万元</div>
						</div>
						<div class="info_item">
							<div class="info_item_l">
								<div class="chek"></div>押金损失 保资金 
							</div>
							<div><span class="font_bold">1</span>万元</div>
						</div>
						<div class="info_item">
							<div class="info_item_l">
								<div class="chek"></div>按时入住 保入住
							</div>
							<div><span class="font_bold">1</span>万元</div>
						</div>
						<div class="info_item">
							<div class="info_item_l">
								<div class="chek"></div>家庭财产 全保障
							</div>
							<div><span class="font_bold">1.4</span>万元</div>
						</div>
						<div class="info_item">
							<div class="info_item_l">
								<div class="chek"></div>物业欠费 保损失
							</div>
							<div><span class="font_bold">1</span>万元</div>
						</div>
						<div class="info_item">
							<div class="info_item_l">
								<div class="chek"></div>空气质量 保健康
							</div>
							<div><span class="font_bold">1</span>万元</div>
						</div>
						<div class="info_item">
							<div class="info_item_l">
								<div class="chek"></div>法律纠纷 保咨询
							</div>
							<div><span class="font_bold"></span>免费咨询</div>
						</div>
					</div>
				</div>
			</div>
			<!-- 模块二 -->
			<div class="modular" style="height: 550px;">
				<div class="title_back">
					<div class="title">如何领取<span style="color: #FBCC2A;">保障</span></div>
				</div>
				<div class="info" style="width: 810px;">
					<div class="info_title">
						<p>您需要在房小二网APP领取报账单，以获得安心无忧交易保障</p>
					</div>
					<div class="info_list">
						<img src="https://static.fangxiaoer.com/web/images/sy/anxin/zufangbaozhang.png" style="margin-left: -43px;">
					</div>
				</div>
			</div>
			<!-- 模块三 -->
			<div class="modular" style="height: 300px; ">
				<div class="title_back">
					<div class="title">理赔<span style="color: #FBCC2A;">保障</span></div>
				</div>
				<div class="info">
					<div class="info_title">
						<img src="https://static.fangxiaoer.com/web/images/sy/anxin/liucheng.png" width="100%">
					</div>
				</div>
			</div>
			<!-- 模块四 -->
			<div class="modular" style="height: 544px;margin-bottom: 0px;">
				<div class="title_back">
					<div class="title">常见<span style="color: #FBCC2A;">问题</span></div>
				</div>
				<div class="info" style="width: 820px;padding-top: 90px;">
					<div class="bottom_item">
						<p class="font_bold"><span style="color: #FF7600;"> Q：</span>
							无忧保障卡的有效期是多少？
						</p>
						<p style="margin-top: 20px;"><span class="font_bold"> A：</span>
							无忧保障卡的有效期为30天，自领取之日生效，过期作废，需要重新领取。
						</p>
					</div>
					<div class="bottom_item">
						<p class="font_bold"><span style="color: #FF7600;"> Q：</span>
							什么样的房子是可理赔房源？
						</p>
						<p style="margin-top: 20px;"><span class="font_bold"> A：</span>
							浏览房源详情页，图片下方的淡黄色横条提示“保障中“的房子，可进行理赔；
						</p>
					</div>
					<div class="bottom_item">
						<p class="font_bold"><span style="color: #FF7600;"> Q：</span>
							哪些情况下申请理赔不予赔付？
						</p>
						<p style="margin-top: 20px;"><span class="font_bold"> A：</span>
							用户提供的相关信息虚构、描述不准确、理赔证据不足、房源保障已过期或不符合理赔条件；
						</p>
					</div>
					<div class="bottom_item">
						<p class="font_bold"><span style="color: #FF7600;"> Q：</span>
							申请理赔后多久会收到保险公司反馈？
						</p>
						<p style="margin-top: 20px;"><span class="font_bold"> A：</span>
							人保公司会在您申请理赔的3-5个工作日内联系您，向您反馈申请结果。
						</p>
					</div>
				</div>
			</div>
			<div class="logo_fangxiaoer">
				<img src="https://static.fangxiaoer.com/web/images/sy/anxin/logo_fangxiaoer.png" width="100%">
			</div>
		</div>
		<div class="bottom_btn">《安心交易保障权益》请点击：<a href="https://info.fangxiaoer.com/Help/sercurity">https://info.fangxiaoer.com/Help/sercurity</a></div>
	</body>
</html>
