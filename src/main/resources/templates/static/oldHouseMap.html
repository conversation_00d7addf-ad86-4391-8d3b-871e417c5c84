<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>沈阳地图找房，沈阳房产地图，沈阳楼盘地图 - 房小二网</title>
	<meta name="keywords" content="沈阳楼盘地图,沈阳地图找房,沈阳房产地图,沈阳楼市地图"/>
	<meta name="description" content="房小二网地图找房为您提供更新的沈阳楼盘地图信息，全面的沈阳新楼盘位置及沈阳新房出售相关信息。通过简单方便的地图找房功能，使您更加方便地找到目标区域的新楼盘信息，为您创造更好的买房体验，查找沈阳新楼盘，就来房小二网地图找房。"/>
	<meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/housemap1.htm">
	<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
	<link href="https://static.fangxiaoer.com/web/styles/sy/house/map.css" rel="stylesheet" />
	<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
<!--	<script type="text/javascript" src="https://api.map.baidu.com/api?v=1.5&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
	<script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
	<link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
	<script>
		$(function () {
			$("#tabList").click(function () {
				var hover = $(".rightmap_left ul").find(".hover").attr("id").substring(4);
				var qy = $("#regionId").val();
				var lx = $("#xmid").val();
				var jg = $("#priceId").val();
				var hx = $("#SchortType").val();
				var sj = $("#StayIn").val();
				var xq = $("#SchoolType").val();
				var dt = $("#subId").val();
				var dt1 = $("#stationId").val();
				if (lx == "") {lx = "1"}
				if (dt == "0") {
					$(".station li").each(function () {
						if (dt1 == $(this).attr("data-id")) {
							if ($(this).parent().attr("class") == "stationInfo1") {
								dt = 1
							} else {
								dt=2
							}
						}
					})
				}
				if (dt1 == "0") {
					dt1="-1"
				}
				var url;
				switch (hover) {
					case "1":
						if (lx == "1" || lx == "2" || lx == "10") {
							url = "/house/1_"+qy+"_-1_"+lx+"_-1_-1_0_"+jg+"_-1_0_0__1"
						}else if (lx == "-1") {
							url = "/office/1_" + qy + "_0_0_0_0__1"
						} else if (lx == "-2") {
							url = "/villa/"
						}
						break;
					case "2":
						url = "/logpay/1_"+qy+"_-1_-1_-1_-1_0_-1_-1_"+hx+"_-1__1"
						break;
					case "3":
						url = "/exits/1_"+qy+"_-1_-1_-1_-1_-1_-1_-1_-1_"+sj+"__1"
						break;
					case "4":
						url = "/school/1_"+qy+"_-1_-1_-1_-1_"+xq+"_-1_-1_-1_-1__1"
						break;
					case "5":
						url = "/subway/1_-1_" + dt + "_-1_" + dt1 + "_-1_0_"+jg+"_-1_0_0__1"
						break;
				}
				window.location.href=url
			})
		})
	</script>
</head>
<body>
<form name="form1" method="post" action="" id="form1">

	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>

	<input type="hidden" id="regionId" value="0" class="hiddenList" />
	<input type="hidden" id="xmid" value="1" class="hiddenList" />
	<input type="hidden" id="priceId" class="hiddenList" />
	<input type="hidden" id="SchortType" class="hiddenList" />
	<input type="hidden" id="StayIn" value="100" class="hiddenList" />
	<input type="hidden" id="SchoolType" class="hiddenList" />
	<input type="hidden" id="subId" value="0" class="hiddenList"/>
	<input type="hidden" id="stationId" value="0" class="hiddenList"/>
	<div class="bg">
		<div class="header">
		</div>
		<div class="contain">
			<div class="rightmap">
				<div class="rightmap_main">
					<div class="rightmap_left">
						<div class="text">
							<ul>
								<li id="1qyh1" onclick="setTabText('1qyh',1,5,1);price()" class="hover"><i class="img1" onmousedown=""></i>找楼盘</li>
								<!--<li id="1qyh2" onclick="setTabText('1qyh',2,5,1);price()" class=""><i class="img3"></i>低首付</li>-->
								<li id="1qyh3" onclick="setTabText('1qyh',3,5,1);price()" class=""><i class="img4"></i>现房</li>
								<li id="1qyh4" onclick="setTabText('1qyh',4,5,1);price()" class=""><i class="img5"></i>学区</li>
								<li id="1qyh5" onclick="setTabText('1qyh',5,5,1);price()" class=""><i class="img2"></i>地铁</li>
							</ul>
							<div class="gray"></div>
						</div>
					</div>
					<div class="my_xl_other my_xl price">
						<input name="Mileage" type="hidden" class="my_xl_input" value="售价" />
						<div class="my_xl_txt_other my_xl_txt djg">价格</div>
						<div class="my_xl_btn"></div>
						<ul class="my_xl_list PriceInfo">

							<li data-key="priceId" data-id="0">不限</li>

							<li data-key="priceId" data-id="1">4000以下</li>

							<li data-key="priceId" data-id="2">4000-5000</li>

							<li data-key="priceId" data-id="3">5000-6000</li>

							<li data-key="priceId" data-id="4">6000-8000</li>

							<li data-key="priceId" data-id="5">8000以上</li>

						</ul>
					</div>

					<div class="my_xl_other my_xl quyu" style="top:50px;position:absolute;left:260px;display:none">
						<input name="Mileage" type="hidden" class="my_xl_input" value="区域" />
						<div class="my_xl_txt qy">区域</div>
						<div class="my_xl_btn"></div>
						<ul class="my_xl_list zlpqy">
							<li data-key="regionId" data-id="0" lng="0" lat="0" class="">不限</li>

							<li data-key="regionId" data-id="10" lng="123.456348" lat="41.720868" class="">浑南区</li>

							<li data-key="regionId" data-id="3" lng="123.424888" lat="41.865727" class="">皇姑区</li>

							<li data-key="regionId" data-id="9" lng="123.32399" lat="41.832197">于洪区</li>

							<li data-key="regionId" data-id="5" lng="123.382991" lat="41.809242">铁西区</li>

							<li data-key="regionId" data-id="4" lng="123.427224" lat="41.79535">和平区</li>

							<li data-key="regionId" data-id="11" lng="123.415976" lat="41.912125">沈北新区</li>

							<li data-key="regionId" data-id="2" lng="123.476486" lat="41.811581">大东区</li>

							<li data-key="regionId" data-id="1" lng="123.464988" lat="41.802225">沈河区</li>

							<li data-key="regionId" data-id="12" lng="123.350641" lat="41.671104">苏家屯</li>

							<li data-key="regionId" data-id="18" lng="123.08003" lat="41.864829">新民市</li>

							<li data-key="regionId" data-id="19" lng="124.058967" lat="41.345331">其它</li>
						</ul>
					</div>



					<div class="rightmap_right">
						<div class="rightleftmap show" id="1con_1qyh_1">
							<div class="rightmap_right_top">
								<div class="my_xl">
									<input name="Mileage" type="hidden" class="my_xl_input" value="区域" />
									<div class="my_xl_txt qy">区域</div>
									<div class="my_xl_btn"></div>
									<ul class="my_xl_list zlpqy">

										<li data-key="regionId" data-id="0" lng="0" lat="0">不限</li>

										<li data-key="regionId" data-id="10" lng="123.456348" lat="41.720868">浑南区</li>

										<li data-key="regionId" data-id="3" lng="123.424888" lat="41.865727">皇姑区</li>

										<li data-key="regionId" data-id="9" lng="123.32399" lat="41.832197">于洪区</li>

										<li data-key="regionId" data-id="5" lng="123.382991" lat="41.809242">铁西区</li>

										<li data-key="regionId" data-id="4" lng="123.427224" lat="41.79535">和平区</li>

										<li data-key="regionId" data-id="11" lng="123.415976" lat="41.912125">沈北新区</li>

										<li data-key="regionId" data-id="2" lng="123.476486" lat="41.811581">大东区</li>

										<li data-key="regionId" data-id="1" lng="123.464988" lat="41.802225">沈河区</li>

										<li data-key="regionId" data-id="12" lng="123.350641" lat="41.671104">苏家屯</li>

										<li data-key="regionId" data-id="18" lng="123.08003" lat="41.864829">新民市</li>

										<li data-key="regionId" data-id="19" lng="124.058967" lat="41.345331">其它</li>

									</ul>
								</div>
								<div class="my_xl">
									<input name="Mileage" type="hidden" class="my_xl_input" value="普宅" />
									<div class="my_xl_txt zzlx">普宅</div>
									<div class="my_xl_btn"></div>
									<ul class="my_xl_list xmIDInfo">

										<li data-key="xmid" data-id="1">普宅</li>

										<li data-key="xmid" data-id="2">洋房</li>

										<li data-key="xmid" data-id="10">商铺</li>

										<li data-key="xmid" data-id="-2">别墅</li>

										<li data-key="xmid" data-id="-1">公寓</li>

									</ul>
								</div>
								<div class="my_xl">
									<input name="Mileage" type="hidden" class="my_xl_input" value="价格" />
									<div class="my_xl_txt jg">价格</div>
									<div class="my_xl_btn"></div>
									<ul class="my_xl_list jgul">

										<li data-key="priceId" data-id="0">不限</li>

										<li data-key="priceId" data-id="1">4000以下</li>

										<li data-key="priceId" data-id="2">4000-5000</li>

										<li data-key="priceId" data-id="3">5000-6000</li>

										<li data-key="priceId" data-id="4">6000-8000</li>

										<li data-key="priceId" data-id="5">8000以上</li>

									</ul>
								</div>
							</div>
						</div>


						<div class="rightleftmap" id="1con_1qyh_2">
							<div class="rightmap_right_top">
								<div class="my_xl_other my_xl">
									<input name="Mileage" type="hidden" class="my_xl_input" value="环线" />
									<div class="my_xl_txt_other my_xl_txt hx">环线</div>
									<div class="my_xl_btn"></div>
									<ul class="my_xl_list SchortTypeLineInfo">

										<li data-key="SchortType" data-id="0">不限</li>

										<li data-key="SchortType" data-id="1">一环内</li>

										<li data-key="SchortType" data-id="2">一环至二环</li>

										<li data-key="SchortType" data-id="3">二环至三环</li>

										<li data-key="SchortType" data-id="4">三环至四环</li>

									</ul>
								</div>

							</div>
						</div>
						<div class="rightleftmap" id="1con_1qyh_3">
							<div class="rightmap_right_top">
								<div class="my_xl_other my_xl">
									<input name="Mileage" type="hidden" class="my_xl_input" value="入住月份" />
									<div class="my_xl_txt_other my_xl_txt yf">入住月份</div>
									<div class="my_xl_btn"></div>
									<ul class="my_xl_list yflist">

										<li data-key="StayIn" data-id="100">全部</li>

										<li data-key="StayIn" data-id="109">即刻入住</li>

										<li data-key="StayIn" data-id="9">9月</li>

										<li data-key="StayIn" data-id="10">10月</li>

										<li data-key="StayIn" data-id="11">11月</li>

										<li data-key="StayIn" data-id="12">12月</li>

										<li data-key="StayIn" data-id="1">2018年1月</li>

										<li data-key="StayIn" data-id="2">2月</li>

										<li data-key="StayIn" data-id="3">3月</li>

										<li data-key="StayIn" data-id="4">4月</li>

										<li data-key="StayIn" data-id="5">5月</li>

										<li data-key="StayIn" data-id="6">6月</li>

										<li data-key="StayIn" data-id="7">7月</li>

										<li data-key="StayIn" data-id="8">8月</li>

									</ul>
								</div>

							</div>
						</div>
						<div class="rightleftmap" id="1con_1qyh_4">
							<div class="rightmap_right_top">
								<div class="my_xl_other my_xl">
									<input name="Mileage" type="hidden" class="my_xl_input" value="类别" />
									<div class="my_xl_txt_other my_xl_txt xq">类别</div>
									<div class="my_xl_btn"></div>
									<ul class="my_xl_list xqlist">

										<li data-key="SchoolType" data-id="0">不限</li>

										<li data-key="SchoolType" data-id="1">小学</li>

										<li data-key="SchoolType" data-id="2">中学</li>

									</ul>
								</div>
							</div>
						</div>
						<div class="rightleftmap" id="1con_1qyh_5">
							<div class="rightmap_right_top">
								<div class="test_xl">
									<div class="test_xl_text">
										<span>地铁</span>
									</div>
									<div class="subway_xl">
										<ul class="subway_line">

											<li data-key="subId" data-id="1" class="subwayInfo"><a>地铁1号线</a></li>

											<li data-key="subId" data-id="2" class="subwayInfo"><a>地铁2号线</a></li>

											<li data-key="subId" data-id="6" class="subwayInfo"><a>地铁4号线（暂未开通）</a></li>

											<li data-key="subId" data-id="4" class="subwayInfo"><a>地铁9号线（暂未开通）</a></li>

											<li data-key="subId" data-id="7" class="subwayInfo"><a>地铁10号线（暂未开通）</a></li>

										</ul>
										<div class="station">
											<ul class="stationInfo1">

												<li data-lng="123.510752" data-lat="41.814326" data-key="stationId" data-id="1"><a>黎明广场站</a></li>

												<li data-lng="123.494861" data-lat="41.815022" data-key="stationId" data-id="2"><a>滂江街站</a></li>

												<li data-lng="123.477636" data-lat="41.809329" data-key="stationId" data-id="3"><a>东中街站</a></li>

												<li data-lng="123.468114" data-lat="41.807632" data-key="stationId" data-id="4"><a>中街站</a></li>

												<li data-lng="123.454213" data-lat="41.803149" data-key="stationId" data-id="5"><a>怀远门站</a></li>

												<li data-lng="123.439161" data-lat="41.797956" data-key="stationId" data-id="6"><a>青年大街站</a></li>

												<li data-lng="123.426608" data-lat="41.794988" data-key="stationId" data-id="7"><a>南市场站</a></li>

												<li data-lng="123.413115" data-lat="41.795432" data-key="stationId" data-id="8"><a>太原街站</a></li>

												<li data-lng="123.403396" data-lat="41.799694" data-key="stationId" data-id="9"><a>沈阳站站</a></li>

												<li data-lng="123.387028" data-lat="41.802625" data-key="stationId" data-id="10"><a>云峰北街站</a></li>

												<li data-lng="123.377093" data-lat="41.800487" data-key="stationId" data-id="11"><a>铁西广场站</a></li>

											</ul>
											<ul class="stationInfo1">

												<li data-lng="123.362128" data-lat="41.806819" data-key="stationId" data-id="12"><a>保工街站</a></li>

												<li data-lng="123.349389" data-lat="41.810045" data-key="stationId" data-id="13"><a>启工街站</a></li>

												<li data-lng="123.338089" data-lat="41.810986" data-key="stationId" data-id="14"><a>重工街站</a></li>

												<li data-lng="123.316062" data-lat="41.807263" data-key="stationId" data-id="15"><a>迎宾路站</a></li>

												<li data-lng="123.312433" data-lat="41.799949" data-key="stationId" data-id="16"><a>于洪广场站</a></li>

												<li data-lng="123.311966" data-lat="41.786692" data-key="stationId" data-id="17"><a>开发大道站</a></li>

												<li data-lng="123.300468" data-lat="41.777333" data-key="stationId" data-id="18"><a>张士站</a></li>

												<li data-lng="123.283759" data-lat="41.771859" data-key="stationId" data-id="19"><a>四号街站</a></li>

												<li data-lng="123.266045" data-lat="41.771536" data-key="stationId" data-id="20"><a>七号街站</a></li>

												<li data-lng="123.248797" data-lat="41.771818" data-key="stationId" data-id="21"><a>中央大街站</a></li>

												<li data-lng="123.236742" data-lat="41.771751" data-key="stationId" data-id="22"><a>十三号街站</a></li>

											</ul>
											<ul class="stationInfo2">

												<li data-lng="123.409952" data-lat="41.923326" data-key="stationId" data-id="141"><a>航空航天大学站</a></li>

												<li data-lng="123.416025" data-lat="41.912886" data-key="stationId" data-id="140"><a>师范大学站</a></li>

												<li data-lng="123.422278" data-lat="41.891921" data-key="stationId" data-id="44"><a>医学院站</a></li>

												<li data-lng="123.423787" data-lat="41.874117" data-key="stationId" data-id="23"><a>三台子站</a></li>

												<li data-lng="123.424793" data-lat="41.861332" data-key="stationId" data-id="24"><a>陵西站</a></li>

												<li data-lng="123.425621" data-lat="41.853354" data-key="stationId" data-id="25"><a>新乐遗址站</a></li>

												<li data-lng="123.430722" data-lat="41.845052" data-key="stationId" data-id="26"><a>北陵公园站</a></li>

												<li data-lng="123.435645" data-lat="41.839597" data-key="stationId" data-id="27"><a>中医药大学站</a></li>

												<li data-lng="123.435932" data-lat="41.828579" data-key="stationId" data-id="28"><a>岐山路站</a></li>

												<li data-lng="123.442612" data-lat="41.823042" data-key="stationId" data-id="29"><a>沈阳北站</a></li>

												<li data-lng="123.444555" data-lat="41.817371" data-key="stationId" data-id="30"><a>金融中心站</a></li>

											</ul>
											<ul class="stationInfo2">

												<li data-lng="123.440653" data-lat="41.810529" data-key="stationId" data-id="31"><a>市府广场站</a></li>

												<li data-lng="123.439557" data-lat="41.798044" data-key="stationId" data-id="32"><a>青年大街站</a></li>

												<li data-lng="123.442701" data-lat="41.790509" data-key="stationId" data-id="33"><a>青年公园站</a></li>

												<li data-lng="123.441789" data-lat="41.779126" data-key="stationId" data-id="34"><a>工业展览馆站</a></li>

												<li data-lng="123.442652" data-lat="41.765954" data-key="stationId" data-id="35"><a>市图书馆站</a></li>

												<li data-lng="123.447646" data-lat="41.758609" data-key="stationId" data-id="36"><a>五里河站</a></li>

												<li data-lng="123.460151" data-lat="41.749057" data-key="stationId" data-id="37"><a>奥体中心站</a></li>

												<li data-lng="123.462917" data-lat="41.73766" data-key="stationId" data-id="38"><a>营盘街站</a></li>

												<li data-lng="123.468307" data-lat="41.725991" data-key="stationId" data-id="39"><a>世纪大厦站</a></li>

												<li data-lng="123.477757" data-lat="41.715707" data-key="stationId" data-id="40"><a>白塔河路站</a></li>

												<li data-lng="123.489076" data-lat="41.713499" data-key="stationId" data-id="41"><a>全运路站</a></li>

											</ul>
										</div>
									</div>
								</div>

							</div>
						</div>
						<div class="rightmap_right_cen">
							<div class="txtblock">
								<div id="divexample1">
									<ul id="ulList">
									</ul>
								</div>
							</div>
						</div>
					</div>

				</div>
				<div class="rightmap_btn"></div>
			</div>
			<div class="leftmap">
				<ul id="mapTab">
					<li id="tabMap" class="hover" onclick="checkCookie()"><i></i>地图</li>
					<li id="tabList"><i></i>列表</li>
				</ul>
				<div class="symap">
					<div class="dt1">
						<div id="bdmap">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script type="text/javascript" src="https://static.fangxiaoer.com/js/my_xiala.js"></script>
	<script>
		/*********地铁下拉***********/
		$(function () {
			t = 0;
			$("body").click(function () {
				if ($(".subway_line").css("display") != "none" && t == 0) {
					$(".subway_line,.stationInfo1,.stationInfo2").hide();
				} else {
					t=0
				}

			})
			$(".my_xl_txt_other").click(function () {
				$(".subway_line,.stationInfo1,.stationInfo2").hide();
			})
			$(".test_xl").click(function () {
				var subway = $(".subway_line").css("display");
				if (subway == "none") {
					$(".subway_line").show();
					t = 1;
				} else {
					$(".subway_line").hide();
				}
			});
			$(".subway_line li").click(function () {
				var txt = $(this).find("a").html();
				$(".test_xl_text span").html(txt);
			});
			$(".station li").click(function () {
				var txt = $(this).find("a").html();
				$(".test_xl_text span").html(txt);
				$(".stationInfo1,.stationInfo2").hide();
			});
			SetSubwayInfo();
		});

		function SetSubwayInfo() {
			$(".station").find("ul").hide();
			stationShow();
			stationHide();
		}

		function stationShow() {
			$(".subwayInfo").mouseover(function () {
				$(this).addClass("subway_line_hover");
				var stationid = $(this).attr("data-id");
				$(".stationInfo" + stationid).show();
				$(".station").addClass("station_border");

			});
			$(".station").find("ul").mouseover(function () {
				$("." + $(this).attr("class")).show();
				$(".station").addClass("station_border");
			});
		}

		function stationHide() {
			$(".subwayInfo").mouseout(function () {
				$(this).removeClass("subway_line_hover");
				var stationid = $(this).attr("data-id");
				$(".stationInfo" + stationid).hide();
				$(".station").removeClass("station_border");
			});
			$(".station").find("ul").mouseout(function () {
				$("." + $(this).attr("class")).hide();
				$(".station").removeClass("station_border");
			});
		}



		/*********价格下拉***********/
		function price() {
			if ($("#1qyh5").attr('class') == "hover") {
				$(".price").show();
				$(".quyu").hide();
			} else {
				$(".price").hide();
				$(".quyu").show();
			}
			if ($("#1qyh1").attr('class') == "hover") {
				$(".price").hide();
				$(".quyu").hide();
			}
		}
		/*****切换*****/
		function setTabText(name, cursel, n, text) {
			dituzhaofang.QieHuan(cursel);
			for (i = 1; i <= n; i++) {
				var menu = document.getElementById(name + i);
				var con = document.getElementById(text + "con_" + name + "_" + i);
				menu.className = i == cursel ? "hover" : "";
				con.style.display = i == cursel ? "block" : "none";
			}
		}

		/*********点击收回左侧***********/
		$(function () {
			$(".rightmap_btn").click(function () {
				var dd = $(".rightmap_main").css("display");
				if (dd == "none") {
					$(".rightmap_main").show();
					$(this).css("background-position", "-7px -212px").css("left", "480px");
					$(".leftmap").css("margin-left", "481px");
					$(".leftmap").css("min-width", "709px");

				} else {
					$(".rightmap_main").hide();
					$(this).css("background-position", "6px -212px").css("left", "0px");
					$(".leftmap").css("margin-left", "0px");
					$(".leftmap").css("min-width", "1170px");
				}
			});
		});

		/******获取地图高度和地铁列表******/
		change();
		$(window).resize(function () {
			change();
		});
		function change() {
			var h = $(window).height() - 50;
			var height = h + "px";
			var ditie = h - 83 + "px";
			var cen = h - 74 + "px";
			$("#bdmap,.leftmap,.rightmap_left").css("height", height);
			$(".station_border,.station ul").css("height", ditie);
			$(".rightmap_right_cen,#divexample1").css("height", cen);
		}

		/******导航滑动效果******/
		$(function () {
			$(".rightmap_left ul li").mouseover(function () {
				var num = $(this).index();
				switch (num) {
					case 0:
						$(".gray").stop().animate({ top: "0" }, 200);
						break;
					case 1:
						$(".gray").stop().animate({ top: "85px" }, 200);
						break;
					case 2:
						$(".gray").stop().animate({ top: "170px" }, 200);
						break;
					case 3:
						$(".gray").stop().animate({ top: "255px" }, 200);
						break;
					case 4:
						$(".gray").stop().animate({ top: "340px" }, 200);
						break;
				}
			});
			$(".rightmap_left .text").mouseout(function () {
				var id = $(".rightmap_left .hover").attr("id");
				var y = id.substr(4, 1) - 1;
				var position = y * 85 + "px";
				$(".gray").stop().animate({ top: position }, 200);
			});
		});

		/*********获取滚动条*********/

		$(document).ready(function () {
			$("#divexample1").niceScroll();
		});
	</script>

</form>
<script>

	function GetCookieValue(name) {
		var cookieValue = null;
		if (document.cookie && document.cookie != '') {
			var cookies = document.cookie.split(';');
			for (var i = 0; i < cookies.length; i++) {
				var cookie = jQuery.trim(cookies[i]);
				if (cookie.substring(0, name.length + 1) == (name + '=')) {
					cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
					break;
				}
			}
		}
		return cookieValue;
	}
	function DelCookie(name,newname) {
		var date = new Date();
		date.setTime(date.getTime() + 24 * 60 * 60 * 1000);
		document.cookie = name + "=" + newname + ";path=/; expires=" + date.toGMTString();
	}

	$(function () {
		var mapnum = GetCookieValue("MAP");
		if (mapnum != "" && mapnum != null) {
			toMapnav(mapnum.split('a')[0]);
		}
	})
	function toMapnav(index) {
		$(".rightleftmap").removeClass("show");
		$("#1con_1qyh_" + index + ",.price").show();

		if (index == "5") {
			$(".price").show();
			$(".quyu").hide();
		} else {
			$(".price").hide();
			$(".quyu").show();
		}
		if (index == "1") {
			$(".price").hide();
			$(".quyu").hide();
		}
		$(".rightmap_left ul li").removeClass("hover")
		$(".gray").css("top", (index-1)*85+"px")
		$("#1qyh" + index).addClass("hover").click();
	}
</script>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
</body>
</html>
