<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"  xmlns:th="https://www.thymeleaf.org">
<head>
    <title>沈阳地图找二手房，沈阳二手房产地图，沈阳二手房地图 - 房小二网</title>
    <meta name="keywords" content="沈阳二手房,沈阳二手房地图,沈阳地图找房,沈阳房产地图,沈阳楼市地图" />
    <meta name="description" content="房小二网地图找房为您提供更新的沈阳二手房楼盘地图信息，全面的沈阳二手房位置及沈阳二手房出售相关信息。通过简单方便的地图找房功能，使您更加方便地找到目标区域的二手房信息，为您创造更好的二手房买房体验，查找沈阳二手房，就来房小二网二手房地图找房。" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/housemap2.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/house/mapErShouFang.css" rel="stylesheet" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=1.5&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
    <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
    <script src="/js/oldHouseMap/jquery.nicescroll.plus.js"></script>
    <script src="/js/oldHouseMap//BaiduMap_dituzhaofang_ershoufang.js"></script>

    <style>
        .fxe_xl_list {
            background: #f3f3f3;
            position: absolute;
            min-width: 127px;
            height: 301px;
            left: 112px;
            top: 33px;
            overflow: auto;
            display: none;
            z-index: 1000;
            border: 1px #ddd solid;
        }
        .fxe_xl_list li:hover {
            color: #ff5200;
            background: #fff;
        }
        .login{
            width: 70px;
            position: absolute;
            right: 0;
            top: 0;
        }
    </style>
</head>
<body>


<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<form name="form1" method="post" action="" id="form1">

    <input type="hidden" id="subid" value="0" class="hiddenList" />
    <input type="hidden" id="regionId2" value="0" class="hiddenList" />
    <input type="hidden" id="regionId" value="0" class="hiddenList" />
    <input type="hidden" id="PriceBase" value="0" class="hiddenList" />
    <input type="hidden" id="BuildArea" value="0" class="hiddenList" />
    <input type="hidden" id="roomType" value="0" class="hiddenList" />
    <input type="hidden" id="SaleHousesCount" value="0" class="hiddenList" />

    <div class="bg">
        <div class="header">
        </div>
        <div class="contain">
            <div class="rightmap">
                <div class="rightmap_main">
                    <div class="rightmap_right">
                        <div class="rightleftmap show">
                            <div class="rightmap_right_top">
                                <div class="my_xl">
                                    <input name="Mileage" type="hidden" class="my_xl_input" value="售价" />
                                    <div class="my_xl_txt" id="quyu">区域</div>
                                    <div class="my_xl_btn"></div>
                                    <ul class="my_xl_list my_xl_list_quyu" identify="0">

                                        <li data-key="regionId" type="a" data-id="0" lng="0" lat="0">不限</li>

                                        <li data-key="regionId" type="a" data-id="1" lng="123.464988" lat="41.802225">沈河区</li>

                                        <li data-key="regionId" type="a" data-id="2" lng="123.427224" lat="41.79535">和平区</li>

                                        <li data-key="regionId" type="a" data-id="3" lng="123.424888" lat="41.865727">皇姑区</li>

                                        <li data-key="regionId" type="a" data-id="4" lng="123.476486" lat="41.811581">大东区</li>

                                        <li data-key="regionId" type="a" data-id="5" lng="123.382991" lat="41.809242">铁西区</li>

                                        <li data-key="regionId" type="a" data-id="6" lng="123.456348" lat="41.720868">浑南区</li>

                                        <li data-key="regionId" type="a" data-id="7" lng="123.312348" lat="41.799993">于洪区</li>

                                        <li data-key="regionId" type="a" data-id="8" lng="123.415976" lat="41.912125">沈北新区</li>

                                        <li data-key="regionId" type="a" data-id="9" lng="123.350641" lat="41.671104">苏家屯区</li>

                                    </ul>
                                    <ul class="fxe_xl_list fxe_xl_list_quyu2"></ul>
                                </div>
                                <div class="my_xl">
                                    <input name="Mileage" type="hidden" class="my_xl_input" value="售价" />
                                    <div class="my_xl_txt" id="zongjia">总价</div>
                                    <div class="my_xl_btn"></div>
                                    <ul class="my_xl_list">

                                        <li data-key="PriceBase" data-id="0">全部</li>

                                        <li data-key="PriceBase" data-id="1">50万以下</li>

                                        <li data-key="PriceBase" data-id="2">50-80万</li>

                                        <li data-key="PriceBase" data-id="3">80-100万</li>

                                        <li data-key="PriceBase" data-id="4">100-120万</li>

                                        <li data-key="PriceBase" data-id="5">120-150万</li>

                                        <li data-key="PriceBase" data-id="6">150-200万</li>

                                        <li data-key="PriceBase" data-id="7">200-300万</li>

                                        <li data-key="PriceBase" data-id="8">300万以上</li>

                                    </ul>
                                </div>
                                <div class="my_xl">
                                    <input name="Mileage" type="hidden" class="my_xl_input" value="售价" />
                                    <div class="my_xl_txt" id="mianji">面积</div>
                                    <div class="my_xl_btn"></div>
                                    <ul class="my_xl_list">

                                        <li data-key="BuildArea" data-id="0">全部</li>

                                        <li data-key="BuildArea" data-id="1">50m²以下</li>

                                        <li data-key="BuildArea" data-id="2">50-70m²</li>

                                        <li data-key="BuildArea" data-id="3">70-90m²</li>

                                        <li data-key="BuildArea" data-id="4">90-110m²</li>

                                        <li data-key="BuildArea" data-id="5">110-130m²</li>

                                        <li data-key="BuildArea" data-id="6">130-150m²</li>

                                        <li data-key="BuildArea" data-id="7">150-200m²</li>

                                        <li data-key="BuildArea" data-id="8">200-300m²</li>

                                        <li data-key="BuildArea" data-id="9">300m²以上</li>

                                    </ul>
                                </div>
                                <div class="my_xl">
                                    <input name="Mileage" type="hidden" class="my_xl_input" value="售价" />
                                    <div class="my_xl_txt" id="huxing">户型</div>
                                    <div class="my_xl_btn"></div>
                                    <ul class="my_xl_list">

                                        <li data-key="roomType" data-id="0">全部</li>

                                        <li data-key="roomType" data-id="1">一居</li>

                                        <li data-key="roomType" data-id="2">二居</li>

                                        <li data-key="roomType" data-id="3">三居</li>

                                        <li data-key="roomType" data-id="4">四居</li>

                                        <li data-key="roomType" data-id="5">五居以上</li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="rightmap_right_cen hid">
                            <div class="xiaoQu" style="display: none">
                            </div>
                            <div class="txtblock">
                                <div id="divexample1">
                                    <ul id="ulList">
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="leftmap">
                <div class="symap">
                    <div class="dt1">
                        <div id="bdmap">
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/my_xiala.js"></script>
    <script>

        /*********点击收回左侧***********/
        $(function () {
            $(".rightmap_btn").click(function () {
                var dd = $(".rightmap_main").css("display");
                if (dd == "none") {
                    $(".rightmap_main").show();
                    $(this).css("background-position", "-7px -212px").css("left", "530px");
                    $(".leftmap").css("margin-left", "530px");
                    $(".leftmap").css("min-width", "709px");
                } else {
                    $(".rightmap_main").hide();
                    $(this).css("background-position", "6px -212px").css("left", "0px");
                    $(".leftmap").css("margin-left", "0px");
                    $(".leftmap").css("min-width", "1170px");
                }
            });
        });

        /******获取地图高度和地铁列表******/
        change();
        $(window).resize(function () {
            change();
        });
        function change() {
            var h = $(window).height() - 50;
            var height = h + "px";
            var ditie = h - 83 + "px";
            var cen1 = h - 74 + "px";
            var cen2 = h - 129 + "px";
            var xiaoQu = $(".xiaoQu").css("display");
            $("#bdmap,.leftmap,.rightmap_left").css("height", height);
            $(".station_border,.station ul").css("height", ditie);
            if (xiaoQu == "none") {
                $(".rightmap_right_cen,#divexample1").css("height", cen1);
            } else {
                $(".rightmap_right_cen,#divexample1").css("height", cen2);
            }

        }
        /*********获取滚动条*********/
        $(document).ready(function () {
            $("#divexample1").niceScroll();
        });
    </script>
</form>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
</body>
</html>
