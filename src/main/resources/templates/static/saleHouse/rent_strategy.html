<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
	<head>
		<meta charset="utf-8" />
		<title>租房攻略</title>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/saleHouse03.css"/>
		<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
	</head>
<body>
<div id="head2017" th:include = "fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<div class="w1170 IWSH_txt">
		<div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/static/saleHouse/saleHouse.htm" target="_blank">我要卖房</a>&gt;<a href="/static/saleHouse/rent_strategy.htm">租房攻略</a></div>
	<div class="IWSH_txt_left">
		<h3>租房攻略<i class="title_icon title_icon1"></i></h3>
		<p  class="txt_top">房小二网是<b>房产信息服务平台，</b>不是传统的中介模式，我们利用<b>“互联网”</b>的力量来帮助大家出租或求租。可以在房小二网<b>网页版</b>或<b>下载房小二网APP，</b>进行挂牌或找房操作。建议仔细阅读以下内容，可以了解如何<b>高效利用</b>房小二网平台完成租房。</p>
		<h4>1、发布挂牌&在线找房</h4>
		<h5>1） 房东发布房源信息</h5>
		<p>房东登录房小二网进入租房界面，点击<b>“我要出租”，</b>便可进行房源发布。按照界面提示输入房源基本信息、租金要求等信息，还可以<b>编辑房源描述内容，</b>在如实描述的基础上，应该尽量突出房子的优点，引起潜在租客的兴趣。房小二网提供了简便易用的业主管理后台，房东只需用手机号登录房小二网，点击进入<b>“管理租房”，</b>便可随时随地自助在线操作修改挂牌信息。</p>
		<h5>2） 房源信息验真核实</h5>
		<p>房小二网始终把安全放在第一位，平台对挂牌信息及房东的真实性进行严格审核。个人房东在房小二网发布房子需上传本人身份证和房产证，通过<b>真实性审核</b>后方可上线。对于房东上传的相关材料，房小二网平台仅用于真实性审核，并严格按照国家信息系统<b>安全等保三级认证</b>的建设标准进行存储及监管，确保信息不会外泄。</p>
		<h5>3） 租客在线选房</h5>
		<p>搜集租房信息，最高效手段莫过于使用互联网，通过房小二网的租房列表，租客可以在线搜寻符合条件的出租房。如果看到感兴趣的房子。</p>


		<h4>2、在线预约&实地看房</h4>
		<h5>1） 在线预约看房与确认</h5>
		<p>租客通过房小二网平台发现心仪的房子后，建议尽快去实地看一看。好房不等人，尤其是出租房源，第一时间发起预约看房，可以大大增加签约租房的可能性！房小二网为租客提供了最简单的方式——无论何时何地，也无需电话，只要在任意<b>房小二网</b>的挂牌房源界面上点击<b>“预约看房”</b>按钮，就可在线预约看房。预约时请留意房东写的看房<b>备注说明，</b>并选择合适的上门时间。</p>
		<h5>2） 租客实地看房</h5>
		<p>看房当天，请务必在约定的时间出现在看房地点。守时的租客，会给房东留下良好的第一印象，从而为自己的后续租房谈判加分。实地看房时，除了房子本身的户型、朝向、楼层、采光等，还要仔细查看房子<b>周围情况</b>——小区环境、周边配套怎么样。</p>

		<h4>3、双方协商和签约</h4>
		<h5>1） 双方沟通租房明细</h5>
		<p>如果租客经过带看之后对房子满意，那么就进入下一阶段，<b>面对面签约</b>并进一步了解信息，其中须重点了解：起租日期、租金金额、支付方式（付几押几）、最小或最大签约周期等等。房小二网会<b>协助磋商，</b>请房东亲自到场，向其本人了解清楚各项情况。达成租房合意，房小二网管家会就租房细节问题与房东租客进行<b>公开、透明</b>的沟通，让租客在签约前对房子有更准确的了解。</p>
		<h5>2） 双方进行价格谈判</h5>
		<p>价格往往是房东租客谈判中的关键，房小二网鼓励双方进行直32接面对面的磋商。房小二网不是中介，<b>一切收费按劳取酬，</b>所以在价格谈判过程中，房小二网社区顾问志愿者会站在中立角度，不会因为传统中介的按比例提成而抬高租金价格。房小二网只按所提供的<b>服务透明收费。</b></p>
		<h5>3） 租赁合同签约</h5>
		<p>在房东租客协商一致并同意各项交易条件后，社区顾问将作为见证人，见证双方的签订合同。社区顾问在签约开始会配合租客对房东身份的再次核验，确保租赁安全可靠。房小二网平台为房东和租客提供了《居住租赁合同》，社区顾问将就协商确认信息在租赁合同上进行填写，包括租金及期限、房屋出租过程中的设备清单、生活缴费双方承担部分以及交割数据等。合同一式三份，并各自保留一份，三份合同均具有<b>同等法律效应。</b></p>

		<h4>4、TIPS</h4>
		<h5>1） 保证信息真实完整</h5>
		<p>通过房小二网线上预约来看房的一般都是很有<b>诚意的潜在租客。</b>务必确保房东发布房源的在线信息是<b>完整且准确的，</b>比如楼层、房间设备等，亦或是看房时间、租客偏好等有要求的话，应该都一一写明，让租客上门前就可以有基本的判断，后续将大大节约用在沟通和谈判上的时间与精力。</p>
		<h5>2） 通过手机及时掌握信息</h5>
		<p>房小二网通过技术把租房中的房东租客连接起来。不同于中介，房小二网如非必要不会打电话给用户，我们采用技术手段为上下家提供更加<b>及时</b>的信息服务，同时也<b>减少用户被占用的时间。</b></p>
		<h5>3） 房小二网不会收取额外费用</h5>
		<p><b>房小二网不是中介，</b>我们认为<b>信息真实</b>和<b>交易安全</b>是最基本的服务承诺。如果发现有人以房小二网的名义收取额外费用，或主动索要好处费、红包等，请向我们举报。</p>
	</div>

	<div class="IWSH_txt_right">
		<ul class="IWSH_txt_right_ul">
			<h4><i></i>攻略指南</h4>
			<li><a href="/static/saleHouse/sale_strategy.htm"><i>1</i>卖房攻略</a></li>
			<li><a href="/static/saleHouse/shop_guide.htm"><i>2</i>房产交易指南</a></li>
			<li><a href="/static/saleHouse/tax_policy.htm"><i>3</i>税费政策</a></li>
<!--			<li><a href="/static/saleHouse/service_charge.htm"><i>4</i>服务收费</a></li>-->
			<li><a href="/static/saleHouse/school.htm"><i>4</i>学区政策</a></li>
			<li><a href="/static/saleHouse/job_opportunities.htm"><i>5</i>工作机会</a></li>
		</ul>
		<div class="rightQzQgBtn">
			<a th:href="@{'/helpSearch?ids=1'}" rel="2" target="_blank">我要买房</a>
			<a href="/static/saleHouse/saleHouse.htm" target="_blank"><i class="rightQzQgBtn-icon1"></i>我要卖房</a>
		</div>
	</div>


</div>
<!--页面底部-->
<div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
</body>
</html>
