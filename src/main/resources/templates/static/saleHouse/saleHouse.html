<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
	<head>
		<meta charset="utf-8" />
		<title>我要卖房</title>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/saleHouse03.css?=2"/>
		<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
		<script src="https://static.fangxiaoer.com/js/tc/weui_v1.2.8.js"></script>
	</head>
<style>
	.cont_5_ul{ margin-left: 48px;}
</style>
<body>

<div id="head2017" th:include = "fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
		<!-- 顶部搜索 -->
		<div class="w1170 cont_1">
			<h4>房主直卖</h4>
			<div class="cont1_btn_a">
				<a class="cont_1_btn cont_1_btn1" id="typeBtn1"  href="/new_secondPublish" target="_blank">我自己卖</a>
				<a class="cont_1_btn cont_1_btn2"  href="/secondhouse/entrustReleaseHouse" target="_blank">委托卖房</a>
			</div>
			<!--<h4>二手房直卖平台</h4>
			<div class="cont_1_main">
				<div class="cont_1_input">
					<input type="text" name="" id="secondPublish" value=""  maxlength="11"  placeholder="请输入手机号，专属管家为您服务"/>

				</div>
				<div class="cont_1_msg">
					<span class="check_span check_span2"></span>
					<p>我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol"  target="_blank">《用户服务协议》</a>及<a href="https://info.fangxiaoer.com/About/policy"  target="_blank">《隐私政策》</a></p>
				</div>
			</div>-->
		</div>

		<!-- 租房商铺写字楼 -->
		<div class="w1170 cont_2"> 
			<ul class="cont_2_ul">
				<li class="cont_2_li"><a id="typeBtn2" href="/rentwhole?t=1" target="_blank"  class="cont_2_icon cont_2_icon1"></a></li>
				<li class="cont_2_li"><a id="typeBtn3" href="/shopsell" target="_blank" class="cont_2_icon cont_2_icon2"></a></li>
				<li class="cont_2_li"><a id="typeBtn4" href="/officeRent" target="_blank" class="cont_2_icon cont_2_icon3"></a></li>
			</ul>
		</div>
		<!-- 小二服务 -->
		<div class="w1170 cont_3">
			<div class="cont_title"><i class="icon1"></i>小二服务<i class="icon2"></i></div>
			<ul class="cont_3_ul">
				<li class="cont_3_li"><i class="cont_3_icon cont_3_icon_1"></i><h4>省心卖</h4><p>自己卖或管家帮你卖</p></li>
				<li class="cont_3_li"><i class="cont_3_icon cont_3_icon_2"></i><h4>快速卖</h4><p>全平台推广，海量曝光房源</p></li>
				<li class="cont_3_li"><i class="cont_3_icon cont_3_icon_3"></i><h4>卖的多</h4><p>无中间抽佣，0中介费</p></li>
				<li class="cont_3_li"><i class="cont_3_icon cont_3_icon_4"></i><h4>免费咨询电话</h4><p>400-893-9709</p></li>
			</ul>
		</div>
		<!-- 最新成交 -->
		<div class="cont_4">
			<a class="cont_4_bnt" href=""></a>
		</div>
		<!-- 小二问答 -->
		<div class="w1170 cont_5">
			<div class="cont_title"><i class="icon1"></i>小二问答<i class="icon2"></i></div>
			<ul class="cont_5_ul">
				<div class="con5m con51">
					<li class="cont_5_li">
						<div class="cont_5_Q"><i class="cont_5_Q_icon"></i>怎样帮你卖房？</div>
						<div class="cont_5_A">
							<i class="cont_5_A_icon"></i>
							<div>
								<p>1.自主发布：房主自己拍摄照片和视频，在房小二网发布房源数据前台展示自己电话，自 己接访，自己谈价，自己和客户签合同，过户，交割物业(为买卖双方免费搭桥或签约时 找我们房小二网进行第三方见证)。</p>
								<p>2.委托发布：管家帮你全程卖房，策划出售方案，房源实勘包装，多渠道推广，短视频推 荐让你远离烦忧，防止信息外泄骚扰，省心轻松卖房（带看、签约、过户、银行、交割等 一站式服务）。</p>
							</div>
						</div>
					</li>
					<li class="cont_5_li">
						<div class="cont_5_Q"><i class="cont_5_Q_icon"></i>卖房前需要准备什么？</div>
						<div class="cont_5_A">
							<i class="cont_5_A_icon"></i>
							<div>
								<p>1.了解自己所要出售房源与小区同等房源的出售情况，价格情况，(研究一下竞争对手)。</p>
								<p>2.研究自己所要出售房子优势和劣势，根据具体情况来简单修饰下自己的房子，让房子优 势更突出。</p>
								<p>3.想好所出售房子的底价，不要让他人知道，了解下市场中介卖房的方法(快速卖 等着卖) 同时也要了解购房市场情况（是买方市场还是卖方市场）。</p>
							</div>
						</div>
					</li>
				</div>

				<div class="con5m con52">
					<li class="cont_5_li">
						<div class="cont_5_Q"><i class="cont_5_Q_icon"></i>不交中介费，没有线下的中介人员服务，是不是风险更大了?</div>
						<div class="cont_5_A">
							<i class="cont_5_A_icon"></i>
							<div>
								<p>以沈阳为例，每年成交大概10万套左右的二手房，98%以上都是健康、无纠纷的房源，
									有纠纷的二手房是极少数。中介是以促成交易为导向的，会过滤一些不利于交易达成的
									负面因素，比如房屋质量缺陷、居住历史、买家卖房原因等。
									</p>
								<p>房小二网二手房平台为买卖双方提供一个更加透明、开放的平台，促进买卖双方坦诚交
									流，以达成满意的交易。房屋过户及贷款手续并不复杂，平台也有完善的教程及现场服
									务人员协助完成，同时，平台也提供完善的风险预警机制保证房源的真实性，最大程度
									帮您规避风险、让您买的放心，卖的安心。</p>
							</div>
						</div>
					</li>
					<li class="cont_5_li">
						<div class="cont_5_Q"><i class="cont_5_Q_icon"></i>房产过户的大概流程怎样的?</div>
						<div class="cont_5_A">
							<i class="cont_5_A_icon"></i>
							<div>
								<p>房产过户的手续并不复杂，买卖双方达成意向后完全可以自主完成各项手续，大体流程
									如下:达成意向、核验房源、支付定金、网签、过户、办理贷款等，房小二网二手房也为
									买卖双方提供线下代办服务。</p>
							</div>
						</div>
					</li>
				</div>

				<div class="con5m con53">
					<li class="cont_5_li con61">
						<div class="cont_5_Q"><i class="cont_5_Q_icon"></i>没有线下门店，出了问题找谁?</div>
						<div class="cont_5_A">
							<i class="cont_5_A_icon"></i>
							<div>
								<p>房产交易过程中的任何问题，都可通过我们官方客服反馈，我们将会第一时间为你答复。</p>
							</div>
						</div>
					</li>
					<li class="cont_5_li con62">
						<div class="cont_5_Q"><i class="cont_5_Q_icon"></i>如何知道房源是否存在抵押、是否查封等风险?</div>
						<div class="cont_5_A">
							<i class="cont_5_A_icon"></i>
							<div>
								<p>买方可以直接要求卖方下载沈阳政务APP，通过点击住房租房栏目里的登记簿证明按钮
									，查询自己所卖房屋的各类状态。在卖方要求你交定金前，你查看下，同时在房产局办
									理房产交易时，房产局办公人员也会调档查询所卖房屋的房产状态，以防出现房产交易
									风险。</p>
							</div>
						</div>
					</li>
				</div>

				<div class="con5m con54">
					<li class="cont_5_li">
						<div class="cont_5_Q"><i class="cont_5_Q_icon"></i>后续的贷款手续怎么办?</div>
						<div class="cont_5_A">
							<i class="cont_5_A_icon"></i>
							<div>
								<p>房小二网二手房和多家银行合作，有银行贷款的工作人员为您办理房屋贷款手续，安全
									、专业、高效、全程保证交易资金的安全性。</p>
							</div>
						</div>
					</li>
					<li class="cont_5_li">
						<div class="cont_5_Q"><i class="cont_5_Q_icon"></i>平台对买家、卖家违约的情况有约束吗?</div>
						<div class="cont_5_A">
							<i class="cont_5_A_icon"></i>
							<div>
								<p>违约风险可根据买卖双方自主签订的交易合同来明确，房小二网二手房平台仅提供房源
									信息与联系方式，买方可以根据房小二网二手房平台提供的房屋核验注意事项清单逐条
									核对当前房源状态，自主判断房屋的各项问题能否接受，以决定是否达成交易。</p>
							</div>
						</div>
					</li>
				</div>

			</ul>
		</div>
		<!-- 为什么选择房小二 -->
		<div class="w1170 cont_6">
			<!--<div class="cont_title"><i class="icon1"></i>为什么选择房小二<i class="icon2"></i></div>
			<ul class="cont_6_1">
				<li class="cont_6_1_li">
					<a href="/static/saleHouse/job_opportunities.htm" target="_blank">
						<img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/16.png" >
						<h5>邻里互助</h5>
						<p>房小二网并不是房产中介，没有门店和业务员。我们邀请街坊邻里作为社区顾问，带你看房的也许就是您未来的邻居。</p>
					</a>
				</li>
				<li class="cont_6_1_li">
					<a href="/static/saleHouse/service_charge.htm" target="_blank">
						<img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/15.png" >
						<h5>服务收费</h5>
						<p>房小二网是开放式服务平台，没有中介费。买家卖家自由磋商交易，平台提供服务保障交易安全只按服务内容收取固定服务费，绝无隐形收费。</p>
					</a>
				</li>
				<li class="cont_6_1_li">
					<a href="/static/saleHouse/assured_service.htm" target="_blank">
						<img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/reassurance.png" >
						<h5>安心保障</h5>
						<p>房小二网与政府机构、律所及银行等紧密合作，提供上门拍照到签约过户的全程周到服务。保障买卖房产更安全。</p>
					</a>
				</li>
				<li class="cont_6_1_li">
					<a href="/saleHouses/" target="_blank">
						<img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/13.png" >
						<h5>真实房源</h5>
						<p>拒绝中介广告，只接受真实在售房源，多重审核确保真实可靠。此外，房小二网房产搜索引擎通过技术精准匹配，房源直达买家。</p>
					</a>
				</li>
			</ul>
				</li>
			</ul>-->
			<!--<ul class="cont_6_2">
				<li class="cont_6_2_li"><a alt="卖房攻略" href="/static/saleHouse/sale_strategy.htm" target="_blank"><img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/17.png"></a></li>
				<li class="cont_6_2_li"><a alt="租房攻略" href="/static/saleHouse/rent_strategy.htm" target="_blank"><img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/18.png"></a></li>
				<li class="cont_6_2_li"><a alt="房产交易指南" href="/static/saleHouse/shop_guide.htm" target="_blank"><img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/19.png"></a></li>
				<li class="cont_6_2_li"><a alt="税费政策" href="/static/saleHouse/tax_policy.htm" target="_blank"><img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/20.png"></a></li>
				<li class="cont_6_2_li"><a alt="学区政策" href="/static/saleHouse/school.htm" target="_blank"><img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/school.png"></a></li>
				<li class="cont_6_2_li"><a alt="工作机会" href="/static/saleHouse/job_opportunities.htm" target="_blank"><img src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/22.png"></a></li>
			</ul>-->
		</div>

		<!--页面弹窗-->
		<div class="tc_realname">
			<h4>实名认证</h4>
			<h5>根据相关部门要求，发布房源之前需要实名认证</h5>
			<div class="realname_main">
				<ul class="realname_ul">
					<li class="err1" data-id="err1">
						<span class="span1"><i>*</i>上传身份证件</span>
						<div class="errorBox"></div>
						<div class="real_Id_img">
							<div id="uploader1">
								<i>不对外展示</i>
								<p>上传身份证正面</p>
								<div class="img_item" id='addimg1'>
									<input id="uploaderInput1" class="uploader_input" type="file" accept="image/*"  multiple="" />
									<img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png" class="img_add1">
								</div>
							</div>
							<div id="uploader2">
								<i>不对外展示</i>
								<p>上传身份证反面</p>
								<div class="img_item" id='addimg2'>
									<input id="uploaderInput2" class="uploader_input" type="file" accept="image/*"  multiple="" />
									<img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png" class="img_add2">
								</div>
							</div>
							<p class="idMsg">水印内容：仅限房小二网房源登记</p>

						</div>
						<div class="cl"></div>
					</li>
					<li class="inputid err2" data-id="err2">
						<span><i>*</i>姓名</span>
						<input type="text" id="idName" placeholder="请输入姓名">
						<div class="errorBox"></div>
						<div class="cl"></div>
					</li>
					<li class="inputid err3" data-id="err3">
						<span><i>*</i>身份证号</span>
						<input type="text" id="idNum" placeholder="请输入身份证号" >
						<div class="errorBox"></div>
						<div class="cl"></div>
					</li>
					<li class="inputid err4" data-id="err4">
						<span><i>*</i>身份证有效期</span>
						<input type="text" placeholder="" id="startime" class="idtime">
						<s>--</s>
						<input type="text" placeholder="" id="endtime" class="idtime">
						<div class="errorBox"></div>
						<div class="cl"></div>
					</li>

				</ul>

			</div>
			<p class="errMsg">这是一条错误提醒</p>
			<div class="realname_btn" id="realname_btn">提交审核</div>
		</div>

		<div class="idErr">
			<p>您的身份证信息和手机号不一致，是否继续发布？</p>
			<div class="idErr-btn">
				<a class="idErr-btn-yes">是</a>
				<a class="idErr-btn-no">否</a>
			</div>
		</div>
		<div class="tc_full"></div>
<input type="hidden" id="idTextType">
<!--上传身份证weui 正面-->

<!--
<script th:inline="javascript">
	var sessionId1 = [[${session.muser}]]; // 有值-登录了  没值-没登录
	var authenticationStatus = [[${session.authenticationStatus}]];  //0-未认证 1-认证成功  2-认证失败
	var nowpageurl = (window.location.href).split("?dialogType=")[1]
	var whoUrl;
	console.log(sessionId1,authenticationStatus,nowpageurl)
	// 根据账户状态来判定我自己卖跳转打开页面
	if(sessionId1 == null){//没登录 打开登录弹窗
		$("#typeBtn1,#typeBtn2,#typeBtn3,#typeBtn4").attr("href","#login")
	}else if(sessionId1 != null && authenticationStatus == 1){//已登录 有实名认证 直接到新页面
		$("#typeBtn1").attr({"href":"/new_secondPublish","dat":"/new_secondPublish"})
		$("#typeBtn2").attr({"href":"/rentwhole","dat":"/rentwhole"})
		$("#typeBtn3").attr({"href":"/shopsell","dat":"/shopsell"})
		$("#typeBtn4").attr({"href":"/officeRent","dat":"/officeRent"})
	}else{
		$("#typeBtn1,#typeBtn2,#typeBtn3,#typeBtn4").addClass("newPage")
		$("#typeBtn1").attr("dat","/new_secondPublish")
		$("#typeBtn2").attr("dat","/rentwhole")
		$("#typeBtn3").attr("dat","/shopsell")
		$("#typeBtn4").attr("dat","/officeRent")
	}

	$(document).ready(function () {
		if(nowpageurl == 1 && sessionId1 == null){
			$("#login").show()
			$(".tc_full").show()
		}else if(nowpageurl == 2 && sessionId1 == null){
			$(".tc_realname").show()
			$(".tc_full").show()
		}

		$(document).on('click','#loginClose',function(){
			$("#login").hide()
			$(".tc_full").hide()
		})


		// 已登录且有实名状态 点击自主卖房直接进入免费 发布页面
		$(document).on('click','.newPage',function(){
			whoUrl = $(this).attr("dat")
			$(".tc_realname").show()
			$(".tc_full").show()
		})
		// 是否勾选协议
		$(document).on('click','.check_span',function(){
			$('.check_span').toggleClass('check_span2');
		})

		// 上传身份证weui 正
		var uploadCount1 =0;
		$(".inputid input").attr("readOnly",true);
		weui.uploader('#uploader1', {
			url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
			auto: true,
			type: 'file',
			fileVal: 'file',
			compress: false,
			onBeforeQueued: function(files) {
				var uploadCount1 = 0;
				// `this` 是轮询到的文件, `files` 是所有文件
				if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
					alert('请上传图片');
					return false; // 阻止文件添加
				}
				if (this.size > 5 * 1024 * 1024) {
					alert('请上传不超过5M的图片');
					return false;
				}
				if (files.length > 1) { // 防止一下子选择过多文件
					alert('最多只能上传1张图片，请删除后再上传');
					return false;
				}
				if (uploadCount1 + 1 > 1) {
					alert('最多只能上传1张图片，请删除后再上传');
					return false;
				}
				++uploadCount1;

				// return true; // 阻止默认行为，不插入预览图的框架
			},
			onSuccess: function(ret) {
				if (ret.msg == 'success') {
					var data = ret.content;
					$("#uploader1 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png")
					$("#addimg1").before(
							'<div class="img_item">' +
							'<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del1" alt="">' +
							'<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info1"></div>');
					idOcr("https://images1.fangxiaoer.com/"+data,"FRONT")
					imgdata1(); //数据处理
				} else {
					var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
					alert(msg);
					imgdata1(); //数据处理
					return false;
				}
				return true; // 阻止默认行为，不使用默认的成功态
			},
			onError: function(err) {
				// alert("上传失败");
				imgdata1(); //数据处理
				// return true; // 阻止默认行为，不使用默认的失败态
			}
		});

		// 身份证正面 删除
		$("#uploader1").on('click', '.img_del1', function() {
			$(this).parent().remove();
			$("#idName").val("")
			$("#idNum").val("")
			imgdata1(); //数据处理
		});

		$("#uploader1").on('click', '.img_info', function() {
			weui.gallery(this.src);
		})

		function imgdata1() {
			var imgHeight = $('.img_item').width();
			$('.img_item').css('height', imgHeight + 'px')

			$('#uploadCount1').text($(".info1").length) //小区图数量
			uploadCount1 = $(".info1").length
			if($(".info1").length>=1){
				$('#addimg1').hide();
			}else{
				$('#addimg1').show();
			}
			var result = '';
			// |www.baidu.com;255, |www.baidu.com;256,
			$(".info1").each(function() {
				result = result + $(this).attr("datasearchSubName-id") + ',';
			});
			result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
			// $("#pic").val(result); //室内实拍图片赋值
		}

		&lt;!&ndash;上传身份证weui 反面&ndash;&gt;
		var uploadCount2 =0;
		weui.uploader('#uploader2', {
			url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
			auto: true,
			type: 'file',
			fileVal: 'file',
			compress: false,
			onBeforeQueued: function(files) {
				var uploadCount2 = 0;
				console.log(this)
				// `this` 是轮询到的文件, `files` 是所有文件
				if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
					alert('请上传图片');
					return false; // 阻止文件添加
				}
				if (this.size > 5 * 1024 * 1024) {
					alert('请上传不超过5M的图片');
					return false;
				}
				if (files.length > 1) { // 防止一下子选择过多文件
					alert('最多只能上传1张图片，请删除后再上传');
					return false;
				}
				if (uploadCount2 + 1 > 1) {
					alert('最多只能上传1张图片，请删除后再上传');
					return false;
				}
				++uploadCount2;

				// return true; // 阻止默认行为，不插入预览图的框架
			},
			onSuccess: function(ret) {
				if (ret.msg == 'success') {
					var data = ret.content;
					$("#uploader2 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png")
					$("#addimg2").before(
							'<div class="img_item">' +
							'<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del2" alt="">' +
							'<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info2"></div>');
					idOcr("https://images1.fangxiaoer.com/"+data,"BACK")
					imgdata2(); //数据处理

				} else {
					var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
					alert(msg);
					imgdata1(); //数据处理
					return false;
				}
				return true; // 阻止默认行为，不使用默认的成功态
			},
			onError: function(err) {
				// alert("上传失败");
				imgdata1(); //数据处理
				// return true; // 阻止默认行为，不使用默认的失败态
			}
		});

		// 身份证反面 删除
		$("#uploader2").on('click', '.img_del2', function() {
			$(this).parent().remove();
			$("#startime").val("")
			$("#endtime").val("")
			imgdata2(); //数据处理
		});
		$("#uploader2").on('click', '.img_info', function() {
			weui.gallery(this.src);
		})
		function imgdata2() {
			var imgHeight = $('.img_item').width();
			$('.img_item').css('height', imgHeight + 'px')

			$('#uploadCount2').text($(".info2").length) //小区图数量
			uploadCount2 = $(".info2").length
			if($(".info2").length>=1){
				$('#addimg2').hide();
			}else{
				$('#addimg2').show();
			}
			var result = '';
			// |www.baidu.com;255, |www.baidu.com;256,
			$(".info2").each(function() {
				result = result + $(this).attr("data-id") + ',';
			});
			result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
			// $("#pic").val(result); //室内实拍图片赋值
		}

		// 身份证ocr认证
		function idOcr(imageUrl,side) {
			var timeMillis =  Date.parse(new Date())
			console.log(imageUrl)
			$.ajax({
				url:"/checkOCRIDCard",
				data: {
					imageUrl:imageUrl,
					side:side,
					timeMillis:timeMillis
				},
				dataType: 'json',
				type: 'POST',
				success: function (data) {
					console.log(data)
					if(side == "FRONT"){//正面传好
						$("#idName").attr("readonly",false)
						$("#idNum").attr("readonly",false)
					}else{
						$("#startime").attr("readonly",false)
						$("#endtime").attr("readonly",false)
					}
					if (data.status == 1) {
						data = data.content
						// console.log(data)
						if(side == "FRONT"){
							$("#idName").val(data.PERSON_NAME)
							$("#idNum").val(data.PERSON_ID)
							$("#idTextType").val("1")
							$("#idTextType").attr("data-z","1")
						}else{
							var idTime = data.TIME_ZONE.split("-")
							$("#startime").val(idTime[0])
							$("#endtime").val(idTime[1])
							$("#idTextType").val("2")
							$("#idTextType").attr("data-f","1")
						}
						$(".realname_btn").addClass("hover")
					}
				}
			})
		}
		// 错误提醒
		function errorMsg(idname,errorMsg){
			$('.'+idname).addClass("errorMsg")//外边的div 添加
			$('.'+idname).find(".errorBox").html('<label class="error">'+errorMsg+'</label>')
			$('.'+idname).find(".errorBox").css("visibility","initial")
		}
		// 移除错误提醒
		function moveErrorMsg(idname){
			$('.'+idname).removeClass("errorMsg")//外边的div 添加
			$('.'+idname).find(".errorBox").html('')
			$('.'+idname).find(".errorBox").css("visibility","hidden")
		}

		// 点击提交实名审核
		$(document).on('click','#realname_btn.hover',function(){
			// console.log(sessionId1)
			var idCardFontPic = $(".info1").attr("data-id")
			var idCardBackPic = $(".info2").attr("data-id")
			var rName = $("#idName").val()
			var idCard = $("#idNum").val()
			var idCardStartDateStr = $("#startime").val().replace(".","-").replace(".","-")
			var idCardEndDateStr = $("#endtime").val().replace(".","-").replace(".","-")
			if(idCardFontPic == "" || idCardFontPic == undefined){
				errorMsg("err1","上传身份证正面")
			}else if(idCardBackPic == "" || idCardBackPic == undefined){
				errorMsg("err1","上传身份证反面")
			}else if(rName == ""){
				errorMsg("err2","请输入姓名")
			}else if(idCard == ""){
				errorMsg("err3","请输入身份证号")
			}else if(idCardStartDateStr == ""){
				errorMsg("err4","请输入身份证有效期&#45;&#45;起始日期")
			}else if(idCardEndDateStr == ""){
				errorMsg("err4","身份证有效期&#45;&#45;截至日期")
			}else{
				$.ajax({
					url:"/memberIdCardAuthentication",
					data: {
						sessionId:sessionId1,
						idCardFontPic:idCardFontPic,
						idCardBackPic:idCardBackPic,
						rName:rName,
						idCard:idCard,
						idCardStartDateStr:idCardStartDateStr,
						idCardEndDateStr:idCardEndDateStr
					},
					dataType: 'json',
					type: 'POST',
					success: function (data) {
						console.log(data)
						if (data.status == 1 ) {
							if(data.msg == "error"){// 认证失败
								$(".idErr").show()
								$(".tc_full").show()
								$(".idErr p").html(data.content+",是否继续发布？")
								$(".tc_realname").hide()
							}else{//认证成功
								$(".idSuc").show()
								$(".tc_full").show()
								$(".tc_realname").hide()
							}

						}else if(data.status == 0 ){// 认证三次后 失败
							$(".errMsg").show()
							$(".errMsg").html(data.msg)
							$(".realname_btn").removeClass("hover")
						}

					},
					error:function (data) {//认证失败
						console.log(data)
						$(".errMsg").show()
						$(".errMsg").html(data.msg)
						$(".realname_btn").removeClass("hover")
					}
				})
			}
		})

		$("input").focus(function () {
			var idName = $(this).parent().attr("data-id")
			moveErrorMsg(idName)
		})

		// 身份证信息和手机号不一致，点击 是 继续 继续发布
		$(document).on('click','.idErr-btn-yes',function(){
			$.ajax({
				url:"/confirmAuthenticationInfo",
				data: {
					sessionId:sessionId1
				},
				dataType: 'json',
				type: 'POST',
				success: function (data) {
					if (data.status == 1) {
						window.location.href=whoUrl
					}
				}
			})
		})
		// 身份证信息和手机号不一致，点击 否 继续弹出认证弹窗
		$(document).on('click','.idErr-btn-no',function(){
			$(".tc_realname").show()
			$(".idErr").hide()
			$(".tc_full").show()
		})

	})




</script>
-->

<!--页面底部-->
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
	</body>
</html>
