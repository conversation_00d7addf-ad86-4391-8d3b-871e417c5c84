<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
	<head>
		<meta charset="utf-8" />
		<title>安心保障</title>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/saleHouse03.css?=2"/>
		<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
	</head>
<body>
<div id="head2017" th:include = "fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<div class="w1170 IWSH_txt">
	<div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/static/saleHouse/saleHouse.htm" target="_blank">我要卖房</a>&gt;<a href="/static/saleHouse/assured_service.htm">安心保障</a></div>
	<div class="IWSH_txt_left">
		<h3>安心保障<i class="title_icon title_icon1"></i></h3>
		<p class="txt_top">房小二网知道，你在买卖房子时最担心的是安全问题，所以我们花了非常多的心思，希望设计出一套更加安全的交易制度。经过苦苦思考和不断实践，我们摸索出一套<b>完全不同于中介居间模式</b>的方法来帮助大家买卖房子。房小二模式的制度性安全保障，主要体现在以下几方面：</p>

		<h4>1、为什么房小二网帮你买卖房子更安全？</h4>
		<h5>1） 房屋查封补损失保障</h5>
		<p>若在房屋交易签约前存在被司法机关查封等情况，房产管家未对信息到官方渠道进行核实及告知，我们承诺将先行垫付购房者已支付的购房款</p>
		<h5>2）物业欠费先垫保障</h5>
		<p>房产管家有义务须协助买卖双方缴清物业欠费，若在房屋在收割后，发现存在未缴清物业欠费问题，我们承诺先行垫付。</p>
		<h5>3） 交易不成退佣保障</h5>
		<p>若居间成交的二手房交易未能完成过户手续，我们承诺将向交佣方退还所交佣金。</p>
		<h5>4） 过户枉跑补路费保障</h5>
		<p>若在办理缴税过户时，由于房产管家原因未能为买卖双方一次性办理完成，将向无责方提供往返路费赔偿。</p>
		<h5>5） 交易透明无加价保障</h5>
		<p>在房屋买卖过程中，全程交易透明公开，若出现房产管家赚取房屋差价的行为，我们将按照所赚取的差价返还。</p>
		<!--<h5 style="margin-top: 20px">经营企业有两种办法：</h5>
		<p>一种是想办法说服客户支付尽可能高的费用，另一种是努力地获得让客户支付尽可能少的费用的能力。前者需要投入和消耗巨大的资本，后者需要尽可能地提高企业内部效率降低成本。两者都可获得成功，房小二网坚定地选择后者的道路。我们不会通过开设大量门店、招募无数业务员等手段来塑造一个昂贵的品牌形象。在房小二网，我们努力开创一个安全高效的信息开放体系，以让用户付出更少的成本的同时获得更多价值。而在房小二网为用户创造的诸多价值中，安全一直是我们最为关注的核心，这也是为什么房小二网服务虽然收费比中介合理很多，但安全性却远远高于中介模式的原因！</p>-->


	</div>
	<div class="IWSH_txt_right">
		<ul class="IWSH_txt_right_ul">
			<h4><i></i>攻略指南</h4>
			<li><a href="/static/saleHouse/sale_strategy.htm"><i>1</i>卖房攻略</a></li>
			<li><a href="/static/saleHouse/rent_strategy.htm"><i>2</i>租房攻略</a></li>
			<li><a href="/static/saleHouse/shop_guide.htm"><i>3</i>房产交易指南</a></li>
			<li><a href="/static/saleHouse/tax_policy.htm"><i>4</i>税费政策</a></li>
			<li><a href="/static/saleHouse/service_charge.htm"><i>5</i>服务收费</a></li>
			<li><a href="/static/saleHouse/job_opportunities.htm"><i>6</i>工作机会</a></li>
		</ul>
		<div class="rightQzQgBtn">
			<a th:href="@{'/helpSearch?ids=1'}" rel="2" target="_blank">我要买房</a>
			<a href="/static/saleHouse/saleHouse.htm" target="_blank"><i class="rightQzQgBtn-icon1"></i>我要卖房</a>
		</div>
	</div>
	</div>

<!--页面底部-->
<div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::commonFloat"></div>
	</body>
</html>
