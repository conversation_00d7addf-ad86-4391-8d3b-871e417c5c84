<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">


<head>
    <meta charset="UTF-8">
    <title>购房能力评估 - 房小二网</title>
    <meta name="keywords" content="沈阳贷款,房贷,沈阳公积金贷款,沈阳商业贷款,提前还款，购房能力评估"/>
    <meta name="description" th:content="'房小二网购房工具为购房者提供'+${yearTime}+'年房贷计算器及各种在线购房计算工具。包括房贷计算器，公积金贷款计算器，商业贷款计算器，组合贷款计算器，房贷提前还贷计算器，税费计算器，购房能力评估，公积金贷款额度评估等服务。'"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link type="text/css" rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/tool/jsq.css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <style>
        .counter_left {
            width: 390px;
            padding: 35px 15px 55px 10px;
        }
        .counter_left dt {
            width: 150px;
        }
        .counter_left dd .txt {
            width: 160px;
            color: #333;
            font-size: 16px;
            vertical-align: top;
            padding-right: 40px;
        }
        .item_drop span {
            display: block;
            height: 32px;
            width: 180px;
            padding-left: 10px;
            background: #fff url(https://static.fangxiaoer.com/web/images/sy/tool/jsq/icon_bg01.gif) 190px 14px no-repeat;
            border: 1px solid #dfdfdf;
            color: #333;
            overflow: hidden;
            padding-right: 20px;
        }
        .item_drop .hide_box {
            z-index: 100;
            max-height: 300px;
            width: 210px;
            overflow: auto;
        }
        .result {
            font-size: 14px;
            color: #333;
            margin-bottom: 20px;
        }
        .sperator {
            border: none;
            border-bottom: 1px dotted #ccc;
            margin: 15px 10px 15px 0;
        }
        .suggest_house {
            color: #999999;
            font-size: 14px;
        }
        .suggest_item {
            display: inline-block;
            *display: inline;
            width: 272px;
            height: 90px;
            color: #333333;
            font-size: 14px;
            float: left;
            margin-top: 20px;
            text-decoration: none;
            cursor: pointer;
            zoom: 1;
        }
        .suggest_item:hover {
            color: #333333;
        }
        .suggest_img {
            width: 120px;
            height: 90px;
            float: left;
        }
        .suggest_description {
            float: left;
            margin-top: 5px;
            margin-left: 10px;
            line-height: 25px;
        }
        .price {
            font-style: normal;
            color: #ff0000;
        }
        .suggest_description_title {
            font-size: 16px;
        }
        .item_drop .hide_box {
            z-index: 100;
            max-height: 300px;
            width: 210px;
            overflow: auto;
        }
        .jisuan {
            margin-top: 10px;
        }
    </style>
</head>

<body class="w1210">
<form name="ctl00" method="post" action="goufangnenglipinggu.aspx" id="ctl00">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav"></div>
    <!--页面位置-->
    <div class="w place">您的位置：<a href="/">沈阳房产网</a> &gt;  购房能力评估</div>

    <div class="w wrap_nav">
        <a href="business.htm">商业贷款计算器</a>
        <a href="fund.htm" >公积金贷款计算器</a>
        <a href="assemble.htm">组合贷款计算器</a>
        <a href="advance.htm">提前还款计算器</a>
        <a href="tallage.htm">税费计算器</a>
        <a href="ability.htm" class="hover">购房能力评估</a>
        <a href="fundAssess.htm">公积金贷款额度评估</a>
    </div>
    <div class="cl"></div>

    <div class="focus_counter clear">
        <div class="counter_left">
            <s class="arrows_ico"></s>
            <div class="counter_left_tt counter_moudle_tt">
                <h3>填写信息</h3>
            </div>
            <div class="fanshi_bd">
                <!--现有购房资金-->
                <dl class="clear margin_top_20">
                    <dt>现有首付金额：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="current_money" maxlength="9" data-type="number" class="txt">
                        <em>万元</em>
                        <div class="error_msg" data-for="current_money">请输入正确的购房资金</div>
                    </dd>

                </dl>
                <!--每月购房支出-->
                <dl class="clear no_margin">
                    <dt>每月购房支出：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="expense" maxlength="9" data-type="number" class="txt">
                        <em>元</em>
                        <div class="error_msg" data-for="expense">请输入正确的购房支出</div>
                    </dd>

                </dl>
                <!--贷款年限-->
                <dl class="clear no_margin" style="z-index:3">
                    <dt>贷款年限：</dt>
                    <dd>
                        <div class="loan_year item_drop">
                            <span id="loan_time_select">30年(360期)<s></s></span>
                            <div class="hide_box" style="display: none;">
                                <ul id="loan_time">
                                    <li><a href="javascript:;">1年(12期)</a>
                                    </li>
                                    <li><a href="javascript:;">2年(24期)</a>
                                    </li>
                                    <li><a href="javascript:;">3年(36期)</a>
                                    </li>
                                    <li><a href="javascript:;">4年(48期)</a>
                                    </li>
                                    <li><a href="javascript:;">5年(60期)</a>
                                    </li>
                                    <li><a href="javascript:;">6年(72期)</a>
                                    </li>
                                    <li><a href="javascript:;">7年(84期)</a>
                                    </li>
                                    <li><a href="javascript:;">8年(96期)</a>
                                    </li>
                                    <li><a href="javascript:;">9年(108期)</a>
                                    </li>
                                    <li><a href="javascript:;">10年(120期)</a>
                                    </li>
                                    <li><a href="javascript:;">11年(132期)</a>
                                    </li>
                                    <li><a href="javascript:;">12年(144期)</a>
                                    </li>
                                    <li><a href="javascript:;">13年(156期)</a>
                                    </li>
                                    <li><a href="javascript:;">14年(168期)</a>
                                    </li>
                                    <li><a href="javascript:;">15年(180期)</a>
                                    </li>
                                    <li><a href="javascript:;">16年(182期)</a>
                                    </li>
                                    <li><a href="javascript:;">17年(204期)</a>
                                    </li>
                                    <li><a href="javascript:;">18年(216期)</a>
                                    </li>
                                    <li><a href="javascript:;">19年(228期)</a>
                                    </li>
                                    <li><a href="javascript:;">20年(240期)</a>
                                    </li>
                                    <li><a href="javascript:;">21年(252期)</a>
                                    </li>
                                    <li><a href="javascript:;">22年(264期)</a>
                                    </li>
                                    <li><a href="javascript:;">23年(276期)</a>
                                    </li>
                                    <li><a href="javascript:;">24年(288期)</a>
                                    </li>
                                    <li><a href="javascript:;">25年(300期)</a>
                                    </li>
                                    <li><a href="javascript:;">26年(312期)</a>
                                    </li>
                                    <li><a href="javascript:;">27年(324期)</a>
                                    </li>
                                    <li><a href="javascript:;">28年(336期)</a>
                                    </li>
                                    <li><a href="javascript:;">29年(348期)</a>
                                    </li>
                                    <li><a href="javascript:;" class="selected_drop_item">30年(360期)</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <span class="hint_text">您期望偿还贷款的年限</span>
                    </dd>
                </dl>

                <!--期望购买房屋面积-->
                <dl class="clear margin_top_20">
                    <dt>期望购买房屋面积：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="house_size" maxlength="9" data-type="number" class="txt">
                        <em>m²</em>
                        <div class="error_msg" data-for="house_size">请输入正确的房屋面积</div>
                    </dd>

                </dl>
                <!--期望购房区域-->
                <dl class="clear no_margin" style="z-index:2">
                    <dt>期望购房区域：</dt>
                    <dd>
                        <div class="loan_year item_drop">
                            <span id="expect_house_area" region_id="0">不限<s></s></span>
                            <div class="hide_box" style="display: none;">
                                <ul id="region">
                                    <li><a region_id="0" href="javascript:;" class="selected_drop_item">不限</a>
                                    </li>
                                    <li><a region_id="1" href="javascript:;">和平区</a>
                                    </li>
                                    <li><a region_id="2" href="javascript:;">大东区</a>
                                    </li>
                                    <li><a region_id="3" href="javascript:;">皇姑区</a>
                                    </li>
                                    <li><a region_id="4" href="javascript:;">沈河区</a>
                                    </li>
                                    <li><a region_id="5" href="javascript:;">铁西区</a>
                                    </li>

                                    <li><a region_id="7" href="javascript:;">于洪区</a>
                                    </li>
                                    <li><a region_id="8" href="javascript:;">苏家屯区</a>
                                    </li>
                                    <li><a region_id="9" href="javascript:;">浑南区</a>
                                    </li>
                                    <li><a region_id="10" href="javascript:;">沈北新区</a>
                                    </li>
                                    <li><a region_id="11" href="javascript:;">其他</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </dd>
                </dl>
                <!--计算结果-->
                <dl class="clear">
                    <dt></dt>
                    <dd class="jisuan">
                        <input type="button" class="Start_calculation" value="开始计算">
                        <a href="javascript:;" id="reset">清空重填</a>
                    </dd>
                </dl>
            </div>
        </div>
        <div class="counter_right">
            <div class="counter_right_tt counter_moudle_tt">
                <h3>计算结果</h3>
            </div>
            <div class="counter_gongjijin clear">
                <p class="result">您可购买的房屋总价：<span class="highlight_result" id="total_price"></span>万元</p>
                <p class="result">您可购买的房屋单价：<span class="highlight_result" id="unit_price"></span>元/m²</p>
            </div>
            <hr class="sperator">
            <div class="beizhu">
                <h3><span>备注：</span>（以上结果仅供参考）</h3>
            </div>
        </div>
    </div>
    </div>
</form>


<script src="https://static.fangxiaoer.com/js/jsq/common.js"></script>
<script>
    $(function () {
        var inputs = $('.txt[data-type="number"]');

        function setSelectedItem(ul, selectedItem) {
            ul.children().each(function () {
                if ($.trim($(this).text()) == selectedItem) {
                    $(this).children().trigger('click');
                    return false;
                }
            });
        }

        function isTextValid() {
            var valid = true;
            inputs.each(function () {
                var $this = $(this);
                if ($this.is(':enabled') && ($.trim($(this).val()) === '' || isNaN($(this).val()))) {
                    $this.addClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'visible');
                    valid = false;
                }
            });
            return valid;
        }

        //清空重填
        $('#reset').click(function () {
            $('#normal_house, #first_house').attr('checked', 'checked');
            inputs.val('');
            setSelectedItem($('#loan_time'), '30年(360期)');
            setSelectedItem($('#region'), '不限');
            $('[data-type="number"]').each(function () {
                var $this = $(this);
                $this.removeClass('error_txt');
                $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
            });
        });

        //开始计算
        $('.Start_calculation').mousedown(function () {
            var valid = true;
            if (!isTextValid()) {
                valid = false;
            }

            if (valid) {
                var month = parseInt($('#loan_time_select').text()) * 12;
                var year = parseInt(month / 12);
                /*var lilu=0.00576;
                 if(year>5)
                 lilu=0.00594;*/
                if (year == 1) lilu = 0.0600;
                else if (year >= 2 && year <= 3) lilu = 0.0615;
                else if (year >= 4 && year <= 5) lilu = 0.0640;
                else lilu = 0.0655;

                lilu = lilu / 12;

                var current_money = parseFloat($('#current_money').val()); //现持有
                var income = parseFloat($('#income').val()); //月收入
                var expense = parseFloat($('#expense').val()); //月支出
                var house_size = parseFloat($('#house_size').val()); //面积

                if (current_money < 4.7) {
                    alert("--您确定现有购房资金是" + current_money + "万元?--" + "\n\n" + "那么您目前尚不具备购房能力，" + "\n\n" + "建议积攒积蓄或筹集更多的资金。");
                    return false;
                } else if (current_money > 10000) {
                    alert("您确定拥有超过一亿元的购房资金？");
                    return false;
                }

                current_money *= 10000;
                var d1 = expense;
                var d2 = Math.pow(1 + lilu, month) - 1;
                var d3 = lilu * Math.pow(1 + lilu, month);
                var total_price = ((d1 * d2) / d3) + current_money;
                $('#total_price').text(Math.round(total_price / 10000 * 100) / 100);
                $('#unit_price').text(Math.round((parseFloat(total_price) / house_size) * 100) / 100);
                // deleted by sunlong
                // get_loupan();

                if ($('.counter_right').css('display') === 'none') {
                    if (!$(this).parents().find(".counter_left").is(":animated")) {
                        $('.counter_left').animate({
                            'margin-left': '0px'
                        }, 500, function () {
                            $('.arrows_ico').show();
                            $('.counter_right').fadeIn();
                            $('.focus_counter').addClass('focus_counter_ed');
                        });
                    };
                };
                //添加统计正确计算结果的代码
                // deleted by sunlong
                // addRecord(6);
            }
        });
    });

    function get_loupan() {
        var price = Math.round($('#unit_price').text());
        var region = $('#expect_house_area').attr('region_id');

        $.ajax({
            type: 'get',
            url: '/common/modules/housemarket/new_cal/get_loupan.php?region=' + region + '&price=' + price,
            dataType: 'json',
            success: function (msg) {
                var num = msg.num;
                var data = msg.data;
                var list = '';

                for (var i = 0; i < num; i++) {
                    list += '<a href="/votehouse/' + data[i].proj_id + '.html" target="_blank" class="suggest_item clear"><img src="' + data[i].photo + '" alt="' + data[i].proj_name + '" class="suggest_img" /><div class="suggest_description"><p class="suggest_description_title">' + data[i].proj_name + '</p><p>' + data[i].price + '<p>' + data[i].proj_address + '</p></div></a>';
                }

                $('#suggest_house_num').text(num);
                $('#suggest_house_list').html(list);
            }
        });
    }
</script>
<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>

</html>
