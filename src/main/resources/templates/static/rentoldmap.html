<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"  xmlns:th="https://www.thymeleaf.org">
<head>
    <title>沈阳地图找租房，沈阳租房房产地图，沈阳租房地图 - 房小二网</title>
    <meta name="keywords" content="沈阳租房,沈阳租房地图,沈阳地图找房,沈阳房产地图,沈阳楼市地图"/>
    <meta name="description" content="房小二网地图找房为您提供更新的沈阳租房地图信息，全面的沈阳租房房源位置及沈阳租房相关信息。通过简单方便的地图找房功能，使您更加方便地找到目标区域的沈阳租房信息，为您创造更好的买房体验，查找沈阳租房房源，就来房小二网地图找房。"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/housemap3.htm">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
    <script src="https://static.fangxiaoer.com/js/baiduMap/ajax.js" type="text/javascript" charset="utf-8"></script>
    <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/baiduMap/baiduMap1.css" />-->
    <script src="https://static.fangxiaoer.com/js/baiduMap/vue.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="/css/rentbaidumap/rentmap.css" />
    <script src="https://static.fangxiaoer.com/js/baiduMap/subWayLine.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="/js/jquery.autocomplete.js"></script>
    <style type="text/css">
        .mapList .mapSelect>div>p{
            width:112px
        }
        .mapSaleHouseList ul li>a>div h1{
            width: auto;
        }
        .ac_results{
            width: 435px !important;
            border-top: none !important;
            top: 96px !important;
            z-index: 9999;
        }
    </style>
</head>
<body>

<!--引入头部导航栏-->


<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=4,subNavIndex=1"></div>
<div id="map" v-bind:style="{height: height+'px',width:windowWidth+'px'}">
    <div class="mapNav">
        <ul>
            <li v-bind:class="{hover: navIsActive==5 }" @click="navLeft(5)"><i class="img6"></i>整租</li>
            <li v-bind:class="{hover: navIsActive==6 }" @click="navLeft(6)"><i class="img7"></i>合租</li>
        </ul>
    </div>
    <div class="mapList" v-bind:style="{width:listwidth+'px'}">
        <!--搜索框-->
        <div class="searchEsfMap" id="searchEsfMap">
            <input type="text" id="searchNames" placeholder="请输入区域、小区名称或地标名开始找房" class="searchMapInput" />
            <input  type="button" class="searchMapBtn" value="搜索" >
            <img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png" id="deleteButton" alt="x" style="display: none !important;">
        </div>
        <!--
        <div class="mapSelect">
            <div class="region">
                <p @click="showSelectList=0;">{{ regionList.ActiveText }}</p>
                <ul v-if="showSelectList==0">
                    <li @click="navNormal(regionList,'区域不限')">不限</li>
                    <li v-for="vregion in regionList.content" @click="navSelect1(regionList,vregion.name,vregion.id);SetMap2(vregion.latitude,vregion.longitude,maxRegion)">{{ vregion.name }}</li>
                </ul>
            </div>
        </div>
        -->
        <div class="Choice" v-if="regionList.ActiveId != ''|| searchTitle != ''">
            <h1>已选：</h1>
            <div class="box">
                <p @click="deleteSearchValue();" v-if="searchTitle != ''"><label v-bind:title="  searchTitle ">{{searchTitle}}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <p @click="navNormal(regionList,'区域');deleteRegion()" v-if="regionList.ActiveId != ''"><label v-bind:title=" regionList.ActiveText  ">{{  regionList.ActiveText  }}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                <div class="cancelAll" @click="deleteAllValue();">清空</div>
            </div>
            <div class="cl"></div>
        </div>
        <div class="noHouseMap" v-bind:style="{display: recommentSwitch}" style="display: none;">
            <h4>地图范围内没有找到房源</h4>
            <p>建议您：拖动地图更改位置或 <a href="/helpSearch?ids=3">填写表单，专属定制找房</a></p>
        </div>
        <div v-bind:style="{height: height-74 + 'px'}" class="mapSaleHouseList HouseList">
            <ul>
                <li v-for="vnewHousList in newHousList">
                    <a v-bind:href="'https://sy.fangxiaoer.com/rent/'+vnewHousList.houseId+'.htm'" target="_blank">
                        <img v-if="vnewHousList.pic!=''" v-bind:src="vnewHousList.pic"/>
                        <img v-if="vnewHousList.pic==''" src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg"/>
                        <div>
                            <h1><p>{{vnewHousList.title}}</p><i></i></h1>
                            <div><p>{{vnewHousList.regionName}}-{{vnewHousList.subName}}租房</p><h4><span><h6 class="move" v-on:click="SetMap1(vnewHousList.latitude,vnewHousList.longitude,maxPlat);" onclick="return false;" ></h6></span></h4></div>
                            <!--<div><p>{{vnewHousList.paymentName}}   {{vnewHousList.fitment}}　{{vnewHousList.floor}}/{{vnewHousList.totalFloorNumber}}层　{{vnewHousList.forward}} {{vnewHousList.forwardTypeName}}</p><span>{{vnewHousList.price}}元/月</span></div>-->
                            <div v-if="vnewHousList.price != 0"><p>{{vnewHousList.paymentName}}   {{vnewHousList.fitment}}　{{vnewHousList.floor}}/{{vnewHousList.totalFloorNumber}}层　{{vnewHousList.forward}} {{vnewHousList.forwardTypeName}}</p ><span>{{vnewHousList.price}}元/月</span></div>
                            <div v-if="vnewHousList.price == 0"><p>{{vnewHousList.paymentName}}   {{vnewHousList.fitment}}　{{vnewHousList.floor}}/{{vnewHousList.totalFloorNumber}}层　{{vnewHousList.forward}} {{vnewHousList.forwardTypeName}}</p ><span>面议</span></div>
                            <div>
                                <div v-if="(vnewHousList.houseTrait!='')&&(index<4)" v-for="(trait,index) in vnewHousList.houseTrait.split(',')" v-bind:class="['tese_'+(index+1)]">{{ trait }}</div>
                                <span>{{vnewHousList.area}}㎡</span>
                            </div>
                        </div>
                    </a>
                </li>
            </ul>
        </div>
        <img id="loading" src="https://static.fangxiaoer.com/web/images/sy/map/loading.gif" alt="加载中……" />
    </div>
    <div id="baiduMap" v-bind:style="{width:mapWidth+'px'}">
    </div>
</div>
<div class="hint" id="hint" style="display: none;">
    <dl>
        <dd><p>您的浏览器版本过低，<br>建议升级浏览器！</p></dd>
        <dd>
            <img src="https://static.fangxiaoer.com/web/images/sy/map/info.png"/>
        </dd>
        <dd>
            <a href="/static/oldRentmap.htm">查看低版本地图</a>
            <a href="https://pc.uc.cn/" target="_blank" style="color: #ff5200;" id="Dherf">升级浏览器</a>
        </dd>
    </dl>
</div>
<script>
    var type=3;
    var navType=5;
    //如果是safari浏览器显示不支持
    //        if(navigator.userAgent.indexOf("Safari") > -1 && navigator.userAgent.indexOf("Chrome") < 1){
    //            document.getElementById("hint").style.display="block";
    //        }
    //如果是ie显示不支持
    if(navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > -1 && !(navigator.userAgent.indexOf("Opera") > -1) ){
        document.getElementById("hint").style.display="block";
    }
</script>
<!--<script src="https://static.fangxiaoer.com/js/baiduMap/baiduMap1.js" type="text/javascript" charset="utf-8"></script>-->
<script src="/js/baiDuMapUtils/baiduRentMap.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" src="/js/rentbaidumap/rentmap.js"></script>

<div th:include="fragment/fragment::tongji"></div>
<!--<div th:include="fragment/fragment::esfCommon_meiqia"></div>-->
</body>
</html>
