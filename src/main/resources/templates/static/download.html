<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache">
    <title>沈阳二手房信息发布_免费信息发布_个人房源 - 房小二网</title>
    <meta name="keywords" content="二手房信息发布,个人房源,沈阳二手房信息,二手房中介,免费信息发布">
    <meta name="description" content="房小二网为您提供专业贴心的二手房服务，为您提供免费二手房信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
</head>
<style>
    * {
        margin: 0;
        padding: 0;
    }

    body {
        font-family: 'MicrosoftYaHei';
        min-width: 1385px;
        min-height: 790px;
        height: 100%;
        width: 100%;
        background-color: #ffffff;
    }

    html {
        height: 100%;
    }

    a {
        text-decoration: none;
    }

    .main {
        height: 100%;
        width: 100%;
        /*background: url(https://static.fangxiaoer.com/web/images/sy/download/bg2.jpg);*/
        /*background-position: center center;*/
        /*background-repeat: no-repeat;*/
        background-size: 100% 100%;
    }

    /*.info {*/
    /*	!*width: 1170px;*!*/
    /*	!*min-width: 1170px;*!*/
    /*	!*padding-top: 163px;*!*/
    /*	!*padding-left: 91px;*!*/
    /*	!*margin: 0 auto;*!*/
    /*	!*align-items: center;*!*/
    /*}*/

    /*.info .phone {*/
    /*	width: 346px;*/
    /*	height: 539px;*/
    /*	margin-right: 202px;*/
    /*	float: left;*/
    /*}*/

    /*.info_r {float: left;margin-top: 31px}*/

    /*.info_r .title {*/
    /*	height: 50px;*/
    /*	font-size: 50px;*/
    /*	font-family: Microsoft YaHei;*/
    /*	font-weight: bold;*/
    /*	color: #FFFFFF;*/
    /*	line-height: 50px;*/
    /*}*/

    /*.info_r .desc {*/
    /*	height: 32px;*/
    /*	font-size: 32px;*/
    /*	font-family: Microsoft YaHei;*/
    /*	font-weight: 400;*/
    /*	color: #FFFFFF;*/
    /*	line-height: 32px;*/
    /*	margin-top: 21px;*/
    /*}*/
    /*.info_r p{*/
    /*	height: 20px;*/
    /*	font-size: 20px;*/
    /*	font-family: Microsoft YaHei;*/
    /*	font-weight: 400;*/
    /*	color: #FFFFFF;*/
    /*	line-height: 20px;*/
    /*	opacity: 0.6;*/
    /*	margin-top: 29px;*/
    /*}*/
    /*.qr_info {*/
    /*	align-items: center;*/
    /*	font-size: 24px;*/
    /*	color: #fff;*/
    /*	line-height: 40px;*/
    /*	float: left;*/
    /*}*/

    /*.qr_info img {*/
    /*	width: 140px;*/
    /*	height: 140px;*/
    /*	margin-bottom: 25px;*/
    /*}*/

    /*.bottom_btn {*/
    /*	margin-top: 81px;*/
    /*	color: #fff;*/
    /*	font-size: 18px;*/
    /*	display: block;*/
    /*}*/

    /*.bottom_btn img {*/
    /*	width: 20px;*/
    /*	height: 20px;*/
    /*	margin-left: 8px;*/
    /*	vertical-align: sub;*/
    /*}*/
    /*.Showimg{    margin-top: 75px;}*/
    /*.Showimg>div{}*/
    /*.Showimg>div+div{margin-left: 100px;}*/
    /*.qr_info p{    height: 24px;*/
    /*	font-size: 24px;*/
    /*	font-family: Microsoft YaHei;*/
    /*	font-weight: 400;*/
    /*	color: #FFF;*/
    /*	line-height: 24px;*/
    /*	margin-bottom: 10px;*/
    /*	text-align: center;*/
    /*	opacity: 1;*/
    /*	margin-top: 0;*/
    /*}*/

    /*new*/
    .info {
        flex-direction: column;
        justify-content: center;

    }

    .info_r_title {
        position: relative;
        height: 70px;
        width: 488px;
        margin: 81px auto 0px;
    }

    .info_r_title_image {
        height: 100%;
        width: 100%;
    }

    .info_r_title_text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: inline-block;
        font-size: 36px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #0B0F12;
        text-align: center;
    }

    .info_r_title_text {
        width: 100%;
    }

    .info_r_main {
        display: flex;
        margin: 52px auto 0 ;
        width: 1030px;
    }

    .info_r_main_phone {
        height: 555px;
        width: 450px;
    }

    .info_r_main_des {
        margin-left: 43px;
        margin-top: 70px;

    }

    .info_r_main_des_text {
        font-size: 30px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #0B0F12;

    }

    .info_r_main_des_text:nth-child(2) {
        margin-top: 42px;
    }

    .info_r_main_des_scan {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 46px;
        width: 454px;
        height: 262px;
        background: #FFFFFF;
        box-shadow: 0px 0px 11px 2px rgba(0, 0, 44, 0.05);
    }

    .info_r_main_des_scan_image {
        height: 138px;
        width: 138px;
    }

    .info_r_main_des_scan_box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .info_r_main_des_scan_box_f {
        margin-top: 19px;
        font-family: Microsoft YaHei;
        font-size: 22px;
        font-weight: 400;
        color: #0B0F12;
    }

    .info_r_main_des_scan_box_s {
        margin-top: 13px;
        font-size: 26px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #0B0F12;
    }


</style>
<body>
<!--引入头部导航栏-->
<div class="main">
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=8"></div>
    <div class="info">
        <div class="info_r_title">
            <img class="info_r_title_image" src="/img/bg1.png" alt=""/>
            <div class="info_r_title_text">免费发布房产信息</div>
        </div>
        <div class="info_r_main">
            <img class="info_r_main_phone" src="/img/phone.png" alt=""/>
            <div class="info_r_main_des">
                <div class="info_r_main_des_text">卖二手房，使用小程序，无需下载</div>
                <div class="info_r_main_des_text">下载APP 免费发布租房 · 商铺 · 写字楼</div>

                <div class="info_r_main_des_scan">
                    <div class="info_r_main_des_scan_box">
                        <img class="info_r_main_des_scan_image" src="/img/wechat.png" alt=""/>
                        <span class="info_r_main_des_scan_box_f">微信扫一扫</span>
                        <span class="info_r_main_des_scan_box_s">开始卖房</span>
                    </div>
                    <div class="info_r_main_des_scan_box">
                        <img class="info_r_main_des_scan_image" src="https://static.fangxiaoer.com/web/images/download/appErweima142.jpg" alt=""/>
                        <span class="info_r_main_des_scan_box_f">手机扫一扫</span>
                        <span class="info_r_main_des_scan_box_s">下载APP</span>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<div class="cl"></div>
<div th:include="fragment/fragment::footer_detail"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
</html>
