<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>提前还款计算器 - 房小二网</title>
    <meta name="keywords" content="沈阳贷款,房贷,沈阳公积金贷款,沈阳商业贷款,提前还款，购房能力评估"/>
    <meta name="description" th:content="'房小二网购房工具为购房者提供'+${yearTime}+'年房贷计算器及各种在线购房计算工具。包括房贷计算器，公积金贷款计算器，商业贷款计算器，组合贷款计算器，房贷提前还贷计算器，税费计算器，购房能力评估，公积金贷款额度评估等服务。'"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link type="text/css" rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/tool/jsq.css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <style>
        /* 提前还款 */

        .counter_left {
            width: 390px;
            padding: 35px 15px 55px 10px;
            /*border-right: 1px solid #DFDFDF;*/
        }
        .counter_left_tt {
            width: 373px;
            margin-left: 15px;
        }
        .counter_left dt {
            width: 128px;
        }
        .counter_left dd .txt {
            width: 205px;
            color: #333;
            font-size: 16px;
            vertical-align: top;
            padding-right: 40px;
        }
        #fund_loan {
            margin-left: 15px;
        }
        .c_gjj_a {
            margin-right: 10px;
        }
        .item_drop span {
            display: block;
            height: 32px;
            width: 225px;
            padding-left: 10px;
            background: #fff url(https://static.fangxiaoer.com/web/images/sy/tool/jsq/icon_bg01.gif) 235px 14px no-repeat;
            border: 1px solid #dfdfdf;
            color: #333;
            overflow: hidden;
            padding-right: 20px;
            word-break: break-all;
        }
        .counter_left dd .small_text {
            width: 40px;
            height: 19px;
            color: #333;
            font-size: 16px;
            padding-left: 0px;
            padding-right: 0px;
        }
        .counter_left dd #rate {
            width: 49px;
        }
        .supplement {
            margin-top: 10px;
        }
        .small_item_drop {
            display: inline-block;
            *display: inline;
            margin-right: 5px;
            height: 34px;
            line-height: 34px;
            vertical-align: middle;
            *zoom: 1;
        }
        .small_item_drop span {
            width: 50px;
            background: #fff url(https://static.fangxiaoer.com/web/images/sy/tool/jsq/icon_bg01.gif) 60px 14px no-repeat;
        }
        .sperator {
            border: none;
            border-bottom: 1px dotted #ccc;
            margin: 0;
            margin-left: 15px;
        }
        .c_gjj_moudle_bd dt {
            width: 120px;
        }
        .c_gjj_b dd {
            width: 125px;
        }
        .c_gjj_a dd {
            width: 125px;
        }
        .result {
            font-size: 14px;
            color: #666;
        }
        .c_gjj_moudle_bd dd {
            padding-right: 10px;
        }
        .item_drop .hide_box {
            z-index: 100;
            max-height: 300px;
            width: 255px;
            overflow: auto;
        }
        .small_item_drop .hide_box {
            width: 80px;
        }
        .tips_div_cen {
            width: 110px;
            text-align: center;
            padding-top: 10px;
        }
        .tips_div {
            margin: 0 0px 0px -62px;
            z-index: 101;
        }
    </style>
</head>
<body class="w1210">
<form name="ctl00" method="post" action="tiqianhuankuan.aspx" id="ctl00">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav"></div>
    <!--页面位置-->
    <div class="w place">您的位置：<a href="/">沈阳房产网</a> &gt;  提前还款计算器</div>

    <div class="w wrap_nav">
        <a href="business.htm">商业贷款计算器</a>
        <a href="fund.htm">公积金贷款计算器</a>
        <a href="assemble.htm" >组合贷款计算器</a>
        <a href="advance.htm" class="hover">提前还款计算器</a>
        <a href="tallage.htm">税费计算器</a>
        <a href="ability.htm">购房能力评估</a>
        <a href="fundAssess.htm">公积金贷款额度评估</a>
    </div>
    <div class="cl"></div>
    <div class="focus_counter clear">
        <div class="counter_left">
            <s class="arrows_ico"></s>
            <div class="counter_left_tt counter_moudle_tt">
                <h3>填写信息</h3>
            </div>
            <dl class="clear">
                <dt>贷款类型：</dt>
                <dd>
                    <input name="loan" id="business_loan" type="radio" checked="checked" value="1">
                    <label for="business_loan">商业贷款</label>
                    <input name="loan" id="fund_loan" type="radio" value="2">
                    <label for="fund_loan">公积金贷款</label>
                </dd>
            </dl>
            <div class="fangshi_bd">
                <!--原贷款总额-->
                <dl class="clear">
                    <dt>原贷款总额：</dt>
                    <dd class="guding">
                        <input type="text" id="total_money" maxlength="9" data-type="number" class="txt">
                        <em>万元</em>
                        <div class="error_msg" data-for="total_money">请输入正确的原贷款总额</div>
                    </dd>
                </dl>
                <!--原贷款年限-->
                <dl class="clear no_margin" style="z-index:10">
                    <dt>原贷款年限：</dt>
                    <dd>
                        <div class="loan_year item_drop">
                            <span id="loan_time_select">30年(360期)</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="load_time">
                                    <li><a href="javascript:;">1年(12期)</a>
                                    </li>
                                    <li><a href="javascript:;">2年(24期)</a>
                                    </li>
                                    <li><a href="javascript:;">3年(36期)</a>
                                    </li>
                                    <li><a href="javascript:;">4年(48期)</a>
                                    </li>
                                    <li><a href="javascript:;">5年(60期)</a>
                                    </li>
                                    <li><a href="javascript:;">6年(72期)</a>
                                    </li>
                                    <li><a href="javascript:;">7年(84期)</a>
                                    </li>
                                    <li><a href="javascript:;">8年(96期)</a>
                                    </li>
                                    <li><a href="javascript:;">9年(108期)</a>
                                    </li>
                                    <li><a href="javascript:;">10年(120期)</a>
                                    </li>
                                    <li><a href="javascript:;">11年(132期)</a>
                                    </li>
                                    <li><a href="javascript:;">12年(144期)</a>
                                    </li>
                                    <li><a href="javascript:;">13年(156期)</a>
                                    </li>
                                    <li><a href="javascript:;">14年(168期)</a>
                                    </li>
                                    <li><a href="javascript:;">15年(180期)</a>
                                    </li>
                                    <li><a href="javascript:;">16年(182期)</a>
                                    </li>
                                    <li><a href="javascript:;">17年(204期)</a>
                                    </li>
                                    <li><a href="javascript:;">18年(216期)</a>
                                    </li>
                                    <li><a href="javascript:;">19年(228期)</a>
                                    </li>
                                    <li><a href="javascript:;">20年(240期)</a>
                                    </li>
                                    <li><a href="javascript:;">21年(252期)</a>
                                    </li>
                                    <li><a href="javascript:;">22年(264期)</a>
                                    </li>
                                    <li><a href="javascript:;">23年(276期)</a>
                                    </li>
                                    <li><a href="javascript:;">24年(288期)</a>
                                    </li>
                                    <li><a href="javascript:;">25年(300期)</a>
                                    </li>
                                    <li><a href="javascript:;">26年(312期)</a>
                                    </li>
                                    <li><a href="javascript:;">27年(324期)</a>
                                    </li>
                                    <li><a href="javascript:;">28年(336期)</a>
                                    </li>
                                    <li><a href="javascript:;">29年(348期)</a>
                                    </li>
                                    <li><a href="javascript:;" class="selected_drop_item">30年(360期)</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </dd>
                </dl>
                <!--原贷款利率-->
                <dl class="clear" style="z-index:9">
                    <dt>原贷款利率：</dt>
                    <dd>
                        <div class="loan_year item_drop" style="z-index:3">
                            <span>2018年最新基准利率</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="original_rate">
                                    <li><a href="javascript:;" data-date="20151024" data-discount="1.1">2018年最新利率1.1倍</a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20151024" data-discount="1" class="selected_drop_item">2018年最新基准利率</a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20151024" data-discount="0.95">2018年最新利率9.5折</a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20151024" data-discount="0.9">2018年最新利率9折</a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20151024" data-discount="0.85">2018年最新利率8.5折</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="supplement">
                                <span class="guding">
                                <b class="lilu_box"><input type="text" id="rate" data-type="number" value="4.90">
                                <em>%</em></b>                                
                            </span>
                            <span class="hint_text">当时利率，可以修改</span>
                            <div class="error_msg" data-for="rate">请输入正确的原贷款利率</div>
                        </div>
                    </dd>
                </dl>
                <!--第一次还款时间-->
                <dl class="clear no_margin" style="z-index:8">
                    <dt>第一次还款时间：</dt>
                    <dd>
                        <div class="loan_year item_drop small_item_drop">
                            <span>2012<s></s></span>
                            <div class="hide_box" style="display: none;">
                                <ul id="first_year">
                                    <li><a href="javascript:;">1997</a>
                                    </li>
                                    <li><a href="javascript:;">1998</a>
                                    </li>
                                    <li><a href="javascript:;">1999</a>
                                    </li>
                                    <li><a href="javascript:;">2000</a>
                                    </li>
                                    <li><a href="javascript:;">2001</a>
                                    </li>
                                    <li><a href="javascript:;">2002</a>
                                    </li>
                                    <li><a href="javascript:;">2003</a>
                                    </li>
                                    <li><a href="javascript:;">2004</a>
                                    </li>
                                    <li><a href="javascript:;">2005</a>
                                    </li>
                                    <li><a href="javascript:;">2006</a>
                                    </li>
                                    <li><a href="javascript:;">2007</a>
                                    </li>
                                    <li><a href="javascript:;">2008</a>
                                    </li>
                                    <li><a href="javascript:;">2009</a>
                                    </li>
                                    <li><a href="javascript:;">2010</a>
                                    </li>
                                    <li><a href="javascript:;">2011</a>
                                    </li>
                                    <li><a href="javascript:;">2012</a>
                                    </li>
                                    <li><a href="javascript:;">2013</a>
                                    </li>
                                    <li><a href="javascript:;">2014</a>
                                    </li>
                                    <li><a href="javascript:;">2015</a>
                                    </li>
                                    <li><a href="javascript:;">2016</a>
                                    </li>
                                    <li><a href="javascript:;">2017</a>
                                    </li>
                                    <li><a href="javascript:;">2018</a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="loan_year item_drop small_item_drop">
                            <span style="background-position: 62px 14px;">1</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="first_month">
                                    <li><a href="javascript:;" class="selected_drop_item">1</a>
                                    </li>
                                    <li><a href="javascript:;">2</a>
                                    </li>
                                    <li><a href="javascript:;">3</a>
                                    </li>
                                    <li><a href="javascript:;">4</a>
                                    </li>
                                    <li><a href="javascript:;">5</a>
                                    </li>
                                    <li><a href="javascript:;">6</a>
                                    </li>
                                    <li><a href="javascript:;">7</a>
                                    </li>
                                    <li><a href="javascript:;">8</a>
                                    </li>
                                    <li><a href="javascript:;">9</a>
                                    </li>
                                    <li><a href="javascript:;">10</a>
                                    </li>
                                    <li><a href="javascript:;">11</a>
                                    </li>
                                    <li><a href="javascript:;">12</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="error_msg" style="margin-top: -5px;*margin-top: 0px" data-for="first_pay_time">请选择正确的第一次还款时间</div>
                    </dd>
                </dl>
            </div>
            <hr class="sperator">
            <div class="fanshi_bd">
                <!--预计提前还款时间-->
                <dl class="clear margin_top_20" style="z-index:7">
                    <dt>预计提前还款时间：</dt>
                    <dd>
                        <div class="loan_year item_drop small_item_drop">
                            <span style="background-position: 62px 14px;">2015</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="estimate_year">
                                    <li><a href="javascript:;">2002</a>
                                    </li>
                                    <li><a href="javascript:;">2003</a>
                                    </li>
                                    <li><a href="javascript:;">2004</a>
                                    </li>
                                    <li><a href="javascript:;">2005</a>
                                    </li>
                                    <li><a href="javascript:;">2006</a>
                                    </li>
                                    <li><a href="javascript:;">2007</a>
                                    </li>
                                    <li><a href="javascript:;">2008</a>
                                    </li>
                                    <li><a href="javascript:;">2009</a>
                                    </li>
                                    <li><a href="javascript:;">2010</a>
                                    </li>
                                    <li><a href="javascript:;">2011</a>
                                    </li>
                                    <li><a href="javascript:;">2012</a>
                                    </li>
                                    <li><a href="javascript:;">2013</a>
                                    </li>
                                    <li><a href="javascript:;">2014</a>
                                    </li>
                                    <li><a href="javascript:;">2015</a>
                                    </li>
                                    <li><a href="javascript:;">2016</a>
                                    </li>
                                    <li><a href="javascript:;" class="selected_drop_item">2017</a>
                                    </li>
                                    <li><a href="javascript:;">2018</a>
                                    </li>
                                    <li><a href="javascript:;">2019</a>
                                    </li>
                                    <li><a href="javascript:;">2020</a>
                                    </li>
                                    <li><a href="javascript:;">2021</a>
                                    </li>
                                    <li><a href="javascript:;">2022</a>
                                    </li>
                                    <li><a href="javascript:;">2023</a>
                                    </li>
                                    <li><a href="javascript:;">2024</a>
                                    </li>
                                    <li><a href="javascript:;">2025</a>
                                    </li>
                                    <li><a href="javascript:;">2026</a>
                                    </li>
                                    <li><a href="javascript:;">2027</a>
                                    </li>
                                    <li><a href="javascript:;">2028</a>
                                    </li>
                                    <li><a href="javascript:;">2029</a>
                                    </li>
                                    <li><a href="javascript:;">2030</a>
                                    </li>
                                    <li><a href="javascript:;">2031</a>
                                    </li>
                                    <li><a href="javascript:;">2032</a>
                                    </li>
                                    <li><a href="javascript:;">2033</a>
                                    </li>
                                    <li><a href="javascript:;">2034</a>
                                    </li>
                                    <li><a href="javascript:;">2035</a>
                                    </li>
                                    <li><a href="javascript:;">2036</a>
                                    </li>
                                    <li><a href="javascript:;">2037</a>
                                    </li>
                                    <li><a href="javascript:;">2038</a>
                                    </li>
                                    <li><a href="javascript:;">2039</a>
                                    </li>
                                    <li><a href="javascript:;">2040</a>
                                    </li>
                                    <li><a href="javascript:;">2041</a>
                                    </li>
                                    <li><a href="javascript:;">2042</a>
                                    </li>
                                    <li><a href="javascript:;">2043</a>
                                    </li>
                                    <li><a href="javascript:;">2044</a>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="loan_year item_drop small_item_drop">
                            <span style="background-position: 62px 14px;">1</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="estimate_month">
                                    <li><a href="javascript:;" class="selected_drop_item">1</a>
                                    </li>
                                    <li><a href="javascript:;">2</a>
                                    </li>
                                    <li><a href="javascript:;">3</a>
                                    </li>
                                    <li><a href="javascript:;">4</a>
                                    </li>
                                    <li><a href="javascript:;">5</a>
                                    </li>
                                    <li><a href="javascript:;">6</a>
                                    </li>
                                    <li><a href="javascript:;">7</a>
                                    </li>
                                    <li><a href="javascript:;">8</a>
                                    </li>
                                    <li><a href="javascript:;">9</a>
                                    </li>
                                    <li><a href="javascript:;">10</a>
                                    </li>
                                    <li><a href="javascript:;">11</a>
                                    </li>
                                    <li><a href="javascript:;">12</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="error_msg" style="margin-top: -5px;*margin-top: 0px;" data-for="estimate_time">请选择正确的提前还款时间</div>
                    </dd>
                </dl>
                <dl class="clear no_margin">
                    <dt>提前还款方式：</dt>
                    <dd>
                        <input type="radio" name="prepayment_mode" id="prepay_all" checked="checked">
                        <label for="prepay_all">一次性提前还清</label>
                        <br>
                        <input type="radio" name="prepayment_mode" id="prepay_part">
                        <label for="prepay_part">部分提前还清</label>
                        <span class="guding inline_block">
                            <b class="lilu_box" style="background:#ebebe4;"><input type="text" id="part_money" data-type="number" class="small_text" maxlength="9" style="display:none"></b>
						    <em style="right:24px;">万元</em>                            
                        </span>
                        <div class="error_msg" data-for="part_money">请输入正确的提前还款金额</div>
                    </dd>
                </dl>
            </div>
            <dl class="clear no_margin">
                <dt></dt>
                <dd class="jisuan">
                    <input type="button" class="Start_calculation" value="开始计算">
                    <a href="javascript:;" id="reset">清空重填</a>
                </dd>
            </dl>
        </div>
        <div class="counter_right">
            <div class="counter_right_tt counter_moudle_tt">
                <h3>计算结果</h3>
            </div>
            <div class="counter_gongjijin clear">
                <div class="c_gjj_a c_gjj_moudle">
                    <h3>缩短还款年限
                        <span class="tips_mark">
							<i></i>
							<div class="tips_div" style="display: none;">
								<div class="tips_div_cen">
									<s></s>
									<p>缩短还款年限，月还款额基本不变</p>
								</div>
							</div>
						</span>
                    </h3>
                    <div class="c_gjj_moudle_bd">
                        <dl class="clear">
                            <dt>原月还款额</dt>
                            <dd class="result"><span id="yyhke"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>原最后还款期</dt>
                            <dd class="result"><span id="yzhhkq"></span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>已还款总额</dt>
                            <dd class="result"><span id="yhkze"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>该月一次还款额</dt>
                            <dd class="result"><span id="gyyihke"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>下月起月还款额</dt>
                            <dd class="result"><span id="xyqyhke"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>节约利息支出</dt>
                            <dd class="result"><span id="jslxzc"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>新的最后还款期</dt>
                            <dd class="result"><span id="xdzhhkq"></span>
                            </dd>
                        </dl>
                    </div>
                </div>
                <div class="c_gjj_b c_gjj_moudle">
                    <h3>减少月还款额
                        <span class="tips_mark">
							<i></i>
							<div class="tips_div" style="display: none;">
								<div class="tips_div_cen">
									<s></s>
									<p>减少月还款额，还款期不变</p>
								</div>
							</div>
						</span>
                    </h3>
                    <div class="c_gjj_moudle_bd">
                        <dl class="clear">
                            <dt>原月还款额</dt>
                            <dd class="result"><span id="yyhke_money"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>原最后还款期</dt>
                            <dd class="result"><span id="yzhhkq_money"></span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>已还款总额</dt>
                            <dd class="result"><span id="yhkze_money"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>该月一次还款额</dt>
                            <dd class="result"><span id="gyyihke_money"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>下月起月还款额</dt>
                            <dd class="result"><span id="xyqyhke_money"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>节约利息支出</dt>
                            <dd class="result"><span id="jslxzc_money"></span><span class="unit">元</span>
                            </dd>
                        </dl>
                        <dl class="clear">
                            <dt>新的最后还款期</dt>
                            <dd class="result"><span id="xdzhhkq_money"></span>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="beizhu">
                <h3><span>备注：</span>（以上结果仅供参考）</h3>
            </div>
        </div>
    </div>
    </div>
</form>


<script src="https://static.fangxiaoer.com/js/jsq/common.js"></script>
<script>
    // 提前还款
    $(function () {
        var first_time_err_msg = $('.error_msg[data-for="first_pay_time"]'),
                estimate_time_err_msg = $('.error_msg[data-for="estimate_time"]'),
                inputs = $('.txt[data-type="number"]'),
                rate_input = $('#rate'),
                business_rate_data = {
                    date_20151024: [4.35, 4.75, 4.75, 4.90]
                },
                fund_rate_data = {
                    date_20151024: [2.75, 3.25]
                };

        //第一次还款时间 和 预计还款时间设置为当年当月
        function setSelectedItem(ul, selectedItem) {
            ul.children().each(function () {
                if ($.trim($(this).text()) == selectedItem) {
                    $(this).children().trigger('click');
                    return false;
                }
            });
        }

        //验证方法
        function isFirstTimeValid() {
            var first_year = $('#first_year').find('.selected_drop_item').text(),
                    first_month = $('#first_month').find('.selected_drop_item').text(),
                    estimate_year = $('#estimate_year').find('.selected_drop_item').text(),
                    estimate_month = $('#estimate_month').find('.selected_drop_item').text();
            if (first_year != estimate_year) {
                return first_year < estimate_year;
            } else {
                return parseInt(first_month) <= parseInt(estimate_month);
            }
        }

        function isEstimateTimeValid() {
            var total_year = parseInt($('#first_year').find('.selected_drop_item').text()) + parseInt($('#load_time').find('.selected_drop_item').text()),
                    total_month = $('#first_month').find('.selected_drop_item').text(),
                    estimate_year = $('#estimate_year').find('.selected_drop_item').text(),
                    estimate_month = $('#estimate_month').find('.selected_drop_item').text(),
                    valid = false;
            if (total_year != estimate_year) {
                return total_year > estimate_year;
            } else {
                return total_month >= estimate_month;
            }
        }

        function isTextValid() {
            var valid = true;
            inputs.each(function () {
                var $this = $(this);
                if ($this.is(':enabled') && ($.trim($(this).val()) === '' || isNaN($(this).val()))) {
                    $this.addClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'visible');
                    valid = false;
                }
            });
            return valid;
        }

        //计算利率
        function setRate() {
            var $current = $('#original_rate').find('.selected_drop_item');
            if ($('#business_loan').prop('checked')) {
                var year = parseInt($('#loan_time_select').text()),
                        rate_result;
                if (year == 1) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][0];
                } else if (year <= 3) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][1];
                } else if (year <= 5) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][2];
                } else {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][3];
                }
                rate_input.val((rate_result * $current.attr('data-discount')).toFixed(4));
            } else {
                var year = parseInt($('#loan_time_select').text()),
                        rate_result;
                if (year <= 5) {
                    rate_result = fund_rate_data['date_' + $current.attr('data-date')][0];
                } else {
                    rate_result = fund_rate_data['date_' + $current.attr('data-date')][1];
                }
                rate_input.val((rate_result * $current.attr('data-discount')).toFixed(4));
            }
        }

        var date = new Date(),
                year = date.getFullYear(),
                month = date.getMonth() + 1;
        setSelectedItem($('#estimate_year'), year);
        setSelectedItem($('#estimate_month'), month);
        setSelectedItem($('#first_year'), year);
        setSelectedItem($('#first_month'), month);

        //验证利率输入是否正确
        rate_input.blur(function () {
            var $this = $(this),
                    value = $this.val();
            if ($.trim(value) === '' || parseFloat(value) >= 100) {
                $this.addClass('error_txt');
                $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'visible');
            }
        })
                .focus(function () {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });

        //验证第一次还款时间
        $('#first_year,#first_month').bind('select', function (event, current) {
            if (isFirstTimeValid()) {
                first_time_err_msg.css('visibility', 'hidden');
            } else {
                first_time_err_msg.css('visibility', 'visible');
            }
        });

        //验证预计还款时间
        $('#estimate_year,#estimate_month').bind('select', function (event, current) {
            if (isFirstTimeValid() && isEstimateTimeValid()) {
                estimate_time_err_msg.css('visibility', 'hidden');
            } else {
                estimate_time_err_msg.css('visibility', 'visible');
            }
        });

        $('#prepay_part').click(function () {
            //$('#part_money').removeAttr('disabled');
            $('#part_money').show();
            $('#part_money').parent().css({
                "background": "#fff"
            });
        });

        $('#prepay_all').click(function () {
            //$('#part_money').removeClass('error_txt').attr('disabled', 'disabled');
            $('#part_money').hide();
            $('#part_money').parent().css({
                "background": "#ebebe4"
            });
        });

        //利率联动
        $('#original_rate, #load_time').bind('select', function (event, current) {
            setRate();
        });

        $('#business_loan, #fund_loan').change(function () {
            setRate();
        });

        $('#business_loan').trigger('click');
        if ($('#business_loan').prop('checked')) {
            setRate();
        }

        //清空重填
        $('#reset').click(function () {
            $('#business_loan, #prepay_all').attr('checked', 'checked');
            inputs.val('');
            setSelectedItem($('#load_time'), '30年(360期)');
            setSelectedItem($('#original_rate'), '14年11月22日基准利率');
            setSelectedItem($('#estimate_year'), year);
            setSelectedItem($('#estimate_month'), month);
            setSelectedItem($('#first_year'), year);
            setSelectedItem($('#first_month'), month);
            $('#part_money').val('');
            //$('#part_money').attr('disabled', 'disabled');
            $('#part_money').hide();
            $('#part_money').parent().css({
                "background": "#ebebe4"
            });
            $('[data-type="number"]').each(function () {
                var $this = $(this);
                $this.removeClass('error_txt');
                $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
            });
            $('.error_msg[data-for="estimate_time"]').css('visibility', 'hidden');
        });

        //开始计算
        $('.Start_calculation').mousedown(function () {
            var valid = true;
            if (!isTextValid()) {
                valid = false;
            }
            if (parseFloat($('#total_money').val()) > 99999) {
                $('#total_money').addClass('error_txt');
                $('.error_msg[data-for="total_money"]').css('visibility', 'visible');
                valid = false;
            }
            if (isFirstTimeValid()) {
                first_time_err_msg.css('visibility', 'hidden');
            } else {
                first_time_err_msg.css('visibility', 'visible');
                valid = false;
            }
            if (isEstimateTimeValid()) {
                estimate_time_err_msg.css('visibility', 'hidden');
            } else {
                estimate_time_err_msg.css('visibility', 'visible');
                valid = false;
            }

            if (parseFloat(rate_input.val()) >= 100 || rate_input.val() <= 0 || isNaN(rate_input.val()) || $.trim(rate_input.val()) == '') {
                $('#rate').addClass('error_txt');
                $('.error_msg[data-for="rate"]').css('visibility', 'visible');
                valid = false;
            } else {
                $('#rate').removeClass('error_txt');
                $('.error_msg[data-for="rate"]').css('visibility', 'hidden');
            }

            if ($('#part_money').is(':visible')) {
                if ($.trim($('#part_money').val()) == '' || $.trim($('#part_money').val()) == 0 || isNaN($('#part_money').val())) {
                    $('#part_money').addClass('error_txt');
                    $('.error_msg[data-for="part_money"]').css('visibility', 'visible');
                    valid = false;
                } else {
                    $('#part_money').removeClass('error_txt');
                    $('.error_msg[data-for="part_money"]').css('visibility', 'hidden');
                }
            } else {
                $('#part_money').removeClass('error_txt');
                $('.error_msg[data-for="part_money"]').css('visibility', 'hidden');
            }

            if (valid) {
                if (!count()) return false;

                if ($('#prepay_all').prop('checked')) {
                    $('.c_gjj_b').hide();
                    $('.c_gjj_a h3').hide();
                } else {
                    $('.c_gjj_b').show();
                    $('.c_gjj_a h3').show();
                }
                if ($('.counter_right').css('display') === 'none') {
                    if (!$(this).parents().find(".counter_left").is(":animated")) {
                        $('.counter_left').animate({
                            'margin-left': '0px'
                        }, 500, function () {
                            $('.arrows_ico').show();
                            $('.counter_right').fadeIn();
                            $('.focus_counter').addClass('focus_counter_ed');
                        });
                    };
                };
                //添加统计正确计算结果的代码
                // deleted by sunlong
                // addRecord(4);
            }
        });
    });

    function count() {
        dkzys = parseFloat($('#total_money').val()) * 10000; //贷款总额数，单位元
        s_yhkqs = parseInt($('#loan_time_select').text()) * 12; //原贷款年限，换算成月

        //月利率
        dklv = $('#rate').val() / (100 * 12);

        var first_year = $('#first_year').parent().siblings('span').text();
        var first_month = $('#first_month').parent().siblings('span').text();
        var estimate_year = $('#estimate_year').parent().siblings('span').text();
        var estimate_month = $('#estimate_month').parent().siblings('span').text();

        //已还贷款期数        预计提前还款时间年*12+预计提前还款时间月-（第一次还款时间年*12+第一次还款时间月）
        yhdkqs = (parseInt(estimate_year) * 12 + parseInt(estimate_month)) - (parseInt(first_year) * 12 + parseInt(first_month));
        yhk = dkzys * (dklv * Math.pow((1 + dklv), s_yhkqs)) / (Math.pow((1 + dklv), s_yhkqs) - 1); //原月还款额
        yhkjssj = Math.floor((parseInt(first_year) * 12 + parseInt(first_month) + s_yhkqs - 2) / 12) + '年' + ((parseInt(first_year) * 12 + parseInt(first_month) + s_yhkqs - 2) % 12 + 1) + '月'; //原最后还款期限=(第一次还款日期年*12+第一次还款日期月+原还款年数-2)/12年  （第一次还款日期年*12+第一次还款日期月+原还款期数-2）%12+1月
        yhdkys = yhk * yhdkqs; //已还款总额=原月还款额*已还贷期数

        yhlxs = 0;
        yhbjs = 0;
        for (i = 1; i <= yhdkqs; i++) { //循环已还贷款期数
            yhlxs = yhlxs + (dkzys - yhbjs) * dklv; //已还利息数=已还利息数+（贷款总额数-已还本金数）*贷款利率
            yhbjs = yhbjs + yhk - (dkzys - yhbjs) * dklv; //已还本金数=已还本金数+原月还款额-（贷款总额数-已还本金数）*贷款利率
        }

        remark = '';
        if ($('#prepay_part').attr('checked') == 'checked') { //提前还款方式为：部分提前还清
            tqhkys = parseFloat($('#part_money').val()) * 10000; //部分提前还清的数额
            //if(isNaN(tqhkys)){	return false;}
            if (tqhkys + yhk >= (dkzys - yhbjs) * (1 + dklv)) {
                remark = '您的提前还款额已足够还清所欠贷款！';
                alert(remark);
                return false;
            } else {
                yhbjs = yhbjs + yhk;
                byhk = yhk + tqhkys; //该月一次还款额=原月还款额+提前还款数额（选择部分提前还清时填入的数额）
                //处理方式：缩短年限
                yhbjs_temp = yhbjs + tqhkys; //已还本金数+部分提前还清数
                for (xdkqs = 0; yhbjs_temp <= dkzys; xdkqs++) yhbjs_temp = yhbjs_temp + yhk - (dkzys - yhbjs_temp) * dklv;
                xdkqs = xdkqs - 1;
                xyhk_time = (dkzys - yhbjs - tqhkys) * (dklv * Math.pow((1 + dklv), xdkqs)) / (Math.pow((1 + dklv), xdkqs) - 1); //下月还款额
                jslx_time = yhk * s_yhkqs - yhdkys - byhk - xyhk_time * xdkqs; //节省利息=原月还款额*原还款期数-已还款总额-该月一次还款额-下月还款额*xdkqs
                xdkjssj_time = Math.floor((parseInt(estimate_year) * 12 + parseInt(estimate_month) + xdkqs - 2) / 12) + '年' + ((parseInt(estimate_year) * 12 + parseInt(estimate_month) + xdkqs - 2) % 12 + 1) + '月'; //新的最后还款期
                //处理方式：减少月还款额
                xyhk_money = (dkzys - yhbjs - tqhkys) * (dklv * Math.pow((1 + dklv), (s_yhkqs - yhdkqs))) / (Math.pow((1 + dklv), (s_yhkqs - yhdkqs)) - 1); //下月还款额
                jslx_money = yhk * s_yhkqs - yhdkys - byhk - xyhk_money * (s_yhkqs - yhdkqs); //节省利息
                xdkjssj_money = yhkjssj;

                //将结果赋值到相应的显示框
                $('#yyhke, #yyhke_money').text(Math.round(yhk * 100) / 100);
                $('#yzhhkq, #yzhhkq_money').text(yhkjssj);
                $('#yhkze, #yhkze_money').text(Math.round(yhdkys * 100) / 100);
                $('#gyyihke, #gyyihke_money').text(Math.round(byhk * 100) / 100);

                $('#xyqyhke').text(Math.round(xyhk_time * 100) / 100);
                $('#jslxzc').text(Math.round(jslx_time * 100) / 100);
                $('#xdzhhkq').text(xdkjssj_time);
                $('#xyqyhke_money').text(Math.round(xyhk_money * 100) / 100);
                $('#jslxzc_money').text(Math.round(jslx_money * 100) / 100);
                $('#xdzhhkq_money').text(xdkjssj_money);
            }
        }

        if ($('#prepay_all').attr('checked') == 'checked' || remark != '') { //提前还款方式为：一次提前还清
            byhk = (dkzys - yhbjs) * (1 + dklv); //该月一次还款额=（贷款总额数-已还本金数）*（1+贷款利率）
            xyhk = 0;
            jslx = yhk * s_yhkqs - yhdkys - byhk; //节省利息=原月还款额*原还款期数-已还款总额-该月一次还款额
            xdkjssj = estimate_year + '年' + estimate_month + '月';

            $('#yyhke').text(Math.round(yhk * 100) / 100);
            $('#yzhhkq').text(yhkjssj);
            $('#yhkze').text(Math.round(yhdkys * 100) / 100);
            $('#gyyihke').text(Math.round(byhk * 100) / 100);
            $('#xyqyhke').text(Math.round(xyhk * 100) / 100);
            $('#jslxzc').text(Math.round(jslx * 100) / 100);
            $('#xdzhhkq').text(xdkjssj);
        }

        return true;
    }
</script>

<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>

</html>
