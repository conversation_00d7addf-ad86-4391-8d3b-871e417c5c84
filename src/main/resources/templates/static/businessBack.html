<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>商业贷款计算器 - 房小二网</title>
    <meta name="keywords" content="沈阳贷款,房贷,沈阳公积金贷款,沈阳商业贷款,提前还款，购房能力评估"/>
    <meta name="description" th:content="'房小二网购房工具为购房者提供'+${yearTime}+'年房贷计算器及各种在线购房计算工具。包括房贷计算器，公积金贷款计算器，商业贷款计算器，组合贷款计算器，房贷提前还贷计算器，税费计算器，购房能力评估，公积金贷款额度评估等服务。'"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/static/sydk.htm">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/lpr/jsq.css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <style>
        .mf{ width: 326px; height: 34px; background-color: rgba(251, 236, 226, 1); position: relative;}
        .mfd{ width: 46px; height: 18px; background: #FFA162; border-radius: 4px; position: absolute; left: 12px; top: 8px;
            font-size: 10px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #FFFEFE;
            line-height: 18px; text-align: center;}
        .mfv{ font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            line-height: 18px; position: absolute; left: 67px; top: 8px;}
        .ptb{ width: 100%; margin-top: 13px; border-top: 1px solid #999999; border-left: 1px solid #999999; box-sizing: border-box; padding-bottom: 3px;}
        .pth{ width: 100%; height: 28px; background-color: #FAF1EB; text-align: center; line-height: 28px;}
        .pthd{ width: 33%; float: left; height: 28px; font-size: 12px; border-bottom: 1px solid #999999; border-right: 1px solid #999999;}
        .ptr{ width: 100%; height: 28px; background-color: #F8F8F8; text-align: center; line-height: 28px; font-size: 12px;}
        .dvk{ display: inline-block; margin-right: 6px;}
        .tips_div_cen{ padding: 22px 22px !important;}
    </style>
</head>
<body class="w1210">
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav"></div>
<!--页面位置-->
<div class="w place">您的位置：<a href="/">沈阳房产网</a> &gt;  商业贷款计算器</div>

<div class="w wrap_nav">
    <a href="business.htm" class="hover">商业贷款计算器</a>
    <a href="fund.htm">公积金贷款计算器</a>
    <a href="assemble.htm">组合贷款计算器</a>
    <!--<a href="advance.htm">提前还款计算器</a>
    <a href="tallage.htm">税费计算器</a>
    <a href="ability.htm">购房能力评估</a>
    <a href="fundAssess.htm">公积金贷款额度评估</a>-->
</div>
<div class="cl"></div>

<div class="focus_counter clear">
    <div class="counter_left">
        <s class="arrows_ico"></s>
        <div class="counter_left_tt counter_moudle_tt">
            <h3>填写信息</h3>
        </div>
        <dl class="clear">
            <dt>计算方式：</dt>
            <dd class="fangshi">
                <label>
                    <input name="formula_mode" type="radio" id="area_cal" value="1" checked="checked">按面积算</label>
                <label>
                    <input name="formula_mode" type="radio" id="num_cal" value="2" checked="checked">按贷款额度算</label>
            </dd>
        </dl>
        <div class="fanshi_bd no_margin" style="display: block;">
            <form id="fund_area_a">
                <dl class="clear">
                    <dt>单价：</dt>
                    <dd class="guding">
                        <input type="text" class="txt" data-type="number" name="danjia" id="danjia" maxlength="9">
                        <em>元/m²</em>
                        <div class="error_msg" data-for="danjia">请输入正确的房屋单价</div>
                    </dd>
                </dl>
                <dl class="clear no_margin">
                    <dt>面积：</dt>
                    <dd class="guding">
                        <input type="text" class="txt" data-type="number" name="mianji" id="mianji" maxlength="9">
                        <em>m²</em>
                        <div class="error_msg" data-for="mianji">请输入正确的房屋面积</div>
                    </dd>
                </dl>
                <dl class="clear no_margin" style="z-index:11">
                    <dt>购房性质：</dt>
                    <dd class="xingzhi">
                        <label>
                            <input name="goufangxingzhi" type="radio" checked="" id="first_house" value="1">首套房 </label>
                        <label>
                            <input name="goufangxingzhi" type="radio" id="two_house" value="2"> 二套房</label>
                        <span class="tips_mark">
                                <i></i>
                                <div class="tips_div">
                                    <div class="tips_div_cen">
                                        <s></s>
                                        <div class="tips_div_tt">二套房界定标准：</div>
                                        <p>
1.个人已通过贷款购买过一套（及以上）住房，且至少有一笔贷款尚未还清时，再次申请贷款购买住房的。<br>
2.夫妻两人,其中一人婚前购房贷款未还清时，婚后两人想要以夫妻名义共同贷款购买住房的。</p>
                                    </div>
                                </div>
                            </span>
                    </dd>
                </dl>
                <dl class="clear" style="z-index:8;">
                    <dt>首付：</dt>
                    <dd class="guding">
<!--                        <input type="text" class="txt" data-type="number" name="shoufu" id="shoufu" oninput="this.value = this.value.match(/^(\d{1,2})(\.(\d{0,2}))?$/) ? this.value : ''">-->
                        <!--<input type="text" class="txt" data-type="number" name="shoufu" id="shoufu" oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>10)value=10;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);">-->

                        <input type="text" class="txt" name="shoufu" id="shoufu" oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>10)value=10;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);">
                        <em>成</em>
                        <div class="error_msg" data-for="mianji">请输入正确的首付</div>
                    </dd>
                </dl>

                <!--lpr-->
                <dl class="clear" style="z-index:7;">
                    <dt>利率方式：</dt>
                    <dd>
                        <div class="">
                            <div class="nwp">
                                <span>最新LPR</span>
                                <span>LPR：<em class="newp np"></em>%</span>
                            </div>
                        </div>
                    </dd>
                </dl>

                <dl class="clearBoth"></dl>

                <dl class="clear no_margin" style="z-index:7;" id="indm">
                    <div class="mf">
                        <div class="mfd"></div>
                        <div class="mfv">
                            <div class="dvk">LPR：一年期<em class="newq">3.85</em>%，5年期以上<em class="newp"></em>%</div>
                            <span class="tips_mark">
                                <i id="ind"></i>
                                <div class="tips_div">
                                    <div class="tips_div_cen w324" id="ind2">
                                        <s></s>
                                        <div class="tips_div_tt">LPR(贷款市场报价利率)：</div>
                                        <p>自2019年10月8日起，新发放的商业住房贷款的贷款利率由“贷款基准利率(4.9%)”转换为“贷款市场报价利率（LPR）”。在LPR的基础上增加基点来确定最终的商贷利率；LPR每月20日（遇节假日顺延）报价一次，可在中国人民银行网站查询。</p>
                                        <div class="spmd hide2">最新LPR：一年期<em class="newq">3.85</em>% 五年期<em class="newp"></em>%</div>
                                        <div class="ptb">
                                            <div class="pth">
                                                <div class="pthd">发布时间</div>
                                                <div class="pthd">一年期</div>
                                                <div class="pthd">五年期以上</div>
                                            </div>
                                            <div class="ptr sameMonth">
                                                <div class="pthd"></div>
                                                <div class="pthd"></div>
                                                <div class="pthd"></div>
                                            </div>
                                            <div class="ptr lastMonth">
                                                <div class="pthd"></div>
                                                <div class="pthd"></div>
                                                <div class="pthd"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </span>
                        </div>
                    </div>
                </dl>
                <dl class="clearBoth"></dl>

                <dl class="clear no_margin" style="z-index:7; ">
                    <dt class="setjd">基点：</dt>
                    <dd class="guding w87">
                        <input type="text" class="txt w87 wum2" name="input_lpr" maxlength="3" value="-20" oninput="value=value.replace(/[^\d]/g,'');if(value.length>3)value=value.slice(0,3)"  onkeyup="newLpr($('.newp').attr('rt1'),value)" disabled="disabled"/>
                        <em>‱</em>
                        <div class="error_msg erm" data-for="jidian">请输入正确的基点</div>
                    </dd>
                    <span class="tips_mark pjd">
                                <i></i>
                                <div class="tips_div" style="top: -91px;">
                                    <div class="tips_div_cen w324">
                                        <s></s>
                                        <div class="tips_div_tt">基点(浮动值)：</div>
                                        <p>自2019年10月8日起，商业贷款利率的计算方式由“贷款基准利率（4.9）*上浮或下浮比例”改为“LPR+基点（1基点=0.01%）”</p>
                                        <p><b>提醒</b>：如果是存量贷款的固定利率转LPR，则基点=（老利率-4.8%）*10000</p>
                                        <p><b>举例</b>：当前贷款利率4.9%，下浮10%，则：老利率为4.9%*（1-10%）=4.41%基点=（4.41%-4.8%）*10000=-39</p>
                                    </div>
                                </div>
                            </span>
                </dl>

                <dl class="clear" style="z-index:0;">
                    <dt>商贷利率：</dt>
                    <dd>
                        <div class="">
                            <div class="nwp mpm"></div>
                        </div>
                    </dd>
                </dl>

                <dl class="clearBoth"></dl>


                <dl class="clear hide2" style=" z-index: 8;">
                    <dt>贷款利率：</dt>
                    <dd>
                        <div class="lilv item_drop" style="z-index:2;">
                            <span th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新基准利率'}"></span>
                            <div class="hide_box" style="display: none;">
                                <ul id="original_rate">
                                    <li><a href="javascript:;" data-date="20150301" data-discount="1.1" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新利率1.1倍'}"></a></li>
                                    <li><a href="javascript:;" data-date="20150301" data-discount="1" class="selected_drop_item" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新基准利率'}"></a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20150301" data-discount="0.95" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新利率9.5折'}"></a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20150301" data-discount="0.9" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新利率9折'}"></a></li>
                                    <li><a href="javascript:;" data-date="20150301" data-discount="0.85" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新利率8.5折'}"></a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="shoudong">
                                    <span class="inline_block guding">
                                    <b class="lilu_box"><input type="text" id="rate_a" data-type="number" name="lilu_a" value="4.9">
                                    <em>%</em></b>                                
                                </span>
                            <span class="hint_text">当时利率，可以修改</span>
                            <div class="error_msg" data-for="rate_a">请输入正确的贷款利率</div>
                        </div>
                    </dd>
                </dl>
                <dl class="clear no_margin" style="z-index:1">
                    <dt>贷款年限：</dt>
                    <dd>
                        <div class="loan_year item_drop v2">
                            <span id="loan_time_select_a">30年（360期）</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="loan_time_a">
                                    <li><a href="javascript:;">1年（12期）</a>
                                    </li>
                                    <li><a href="javascript:;">2年（24期）</a>
                                    </li>
                                    <li><a href="javascript:;">3年（36期）</a>
                                    </li>
                                    <li><a href="javascript:;">4年（48期）</a>
                                    </li>
                                    <li><a href="javascript:;">5年（60期）</a>
                                    </li>
                                    <li><a href="javascript:;">6年（72期）</a>
                                    </li>
                                    <li><a href="javascript:;">7年（84期）</a>
                                    </li>
                                    <li><a href="javascript:;">8年（96期）</a>
                                    </li>
                                    <li><a href="javascript:;">9年（108期）</a>
                                    </li>
                                    <li><a href="javascript:;">10年（120期）</a>
                                    </li>
                                    <li><a href="javascript:;">11年（132期）</a>
                                    </li>
                                    <li><a href="javascript:;">12年（124期）</a>
                                    </li>
                                    <li><a href="javascript:;">13年（156期）</a>
                                    </li>
                                    <li><a href="javascript:;">14年（168期）</a>
                                    </li>
                                    <li><a href="javascript:;">15年（180期）</a>
                                    </li>
                                    <li><a href="javascript:;">16年（192期）</a>
                                    </li>
                                    <li><a href="javascript:;">17年（204期）</a>
                                    </li>
                                    <li><a href="javascript:;">18年（216期）</a>
                                    </li>
                                    <li><a href="javascript:;">19年（228期）</a>
                                    </li>
                                    <li><a href="javascript:;">20年（240期）</a>
                                    </li>
                                    <li><a href="javascript:;">21年（252期）</a>
                                    </li>
                                    <li><a href="javascript:;">22年（264期）</a>
                                    </li>
                                    <li><a href="javascript:;">23年（276期）</a>
                                    </li>
                                    <li><a href="javascript:;">24年（288期）</a>
                                    </li>
                                    <li><a href="javascript:;">25年（300期）</a>
                                    </li>
                                    <li><a href="javascript:;">26年（312期）</a>
                                    </li>
                                    <li><a href="javascript:;">27年（324期）</a>
                                    </li>
                                    <li><a href="javascript:;">28年（336期）</a>
                                    </li>
                                    <li><a href="javascript:;">29年（348期）</a>
                                    </li>
                                    <li><a href="javascript:;" class="selected_drop_item">30年（360期）</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <p class="your_want">您期望偿还贷款的年限</p>
                    </dd>
                </dl>
                <dl class="clear">
                    <dt class="h0"></dt>
                    <dd class="jisuan">
                        <input type="button" value="开始计算" class="Start_calculation_area">
                        <a href="javascript:;" id="reset_area">清空重填</a>
                    </dd>
                </dl>
            </form>
        </div>

        <div class="fanshi_bd" style="display: none;">
            <form id="fund_area_b">
                <dl class="clear">
                    <dt>贷款总额：</dt>
                    <dd class="guding">
                        <input type="text" class="txt" name="zonge" maxlength="9" data-type="number" id="loan_money">
                        <em>万元</em>
                        <div class="error_msg" data-for="loan_money">请输入正确的贷款总额</div>
                    </dd>
                </dl>



                <dl class="clear no_margin" style="z-index:0;">
                    <dt>利率方式：</dt>
                    <dd>
                        <div class="">
                            <div class="nwp">
                                <span>最新LPR</span>
                                <span>LPR：<em class="newp np"></em>%</span>
                            </div>
                        </div>
                    </dd>
                </dl>

                <dl class="clearBoth"></dl>

                <dl class="clear no_margin" style="z-index:7;" id="indm3">
                    <div class="mf">
                        <div class="mfd"></div>
                        <div class="mfv">
                            <div class="dvk">LPR：一年期<em class="newq">3.85</em>%，5年期以上<em class="newp"></em>%</div>
                            <span class="tips_mark">
                                <i id="ind3"></i>
                                <div class="tips_div">
                                    <div class="tips_div_cen w324" id="ind4">
                                        <s></s>
                                        <div class="tips_div_tt">LPR(贷款市场报价利率)：</div>
                                        <p>自2019年10月8日起，新发放的商业住房贷款的贷款利率由“贷款基准利率(4.9%)”转换为“贷款市场报价利率（LPR）”。在LPR的基础上增加基点来确定最终的商贷利率；LPR每月20日（遇节假日顺延）报价一次，可在中国人民银行网站查询。</p>
                                        <div class="spmd hide2">最新LPR：一年期<em class="newq">3.85</em>% 五年期<em class="newp"></em>%</div>
                                        <div class="ptb">
                                            <div class="pth">
                                                <div class="pthd">发布时间</div>
                                                <div class="pthd">一年期</div>
                                                <div class="pthd">五年期以上</div>
                                            </div>
                                            <div class="ptr sameMonth2">
                                                <div class="pthd"></div>
                                                <div class="pthd"></div>
                                                <div class="pthd"></div>
                                            </div>
                                            <div class="ptr lastMonth2">
                                                <div class="pthd"></div>
                                                <div class="pthd"></div>
                                                <div class="pthd"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </span>
                        </div>
                    </div>
                </dl>
                <dl class="clearBoth"></dl>


                <dl class="clear no_margin">
                    <dt>基点：</dt>
                    <dd class="guding w87">
                        <input type="text" class="txt w87 wum" name="input_lpr2" maxlength="3" value="-20" oninput="value=value.replace(/[^\d]/g,'');if(value.length>3)value=value.slice(0,3)"  onkeyup="newLpr($('.newp').attr('rt1'),value)" disabled="disabled"/>
                        <em>‱</em>
                        <div class="error_msg erm" data-for="jidian2">请输入正确的基点</div>
                    </dd>
                    <span class="tips_mark pjd">
                                <i></i>
                                <div class="tips_div" style="top: -91px;">
                                    <div class="tips_div_cen w324">
                                        <s></s>
                                        <div class="tips_div_tt">基点(浮动值)：</div>
                                        <p>自2019年10月8日起，商业贷款利率的计算方式由“贷款基准利率（4.9）*上浮或下浮比例”改为“LPR+基点（1基点=0.01%）”</p>
                                        <p><b>提醒</b>：如果是存量贷款的固定利率转LPR，则基点=（老利率-4.8%）*10000</p>
                                        <p><b>举例</b>：当前贷款利率4.9%，下浮10%，则：老利率为4.9%*（1-10%）=4.41%基点=（4.41%-4.8%）*10000=-39</p>
                                    </div>
                                </div>
                            </span>
                </dl>

                <dl class="clear" style="z-index:0;">
                    <dt>商贷利率：</dt>
                    <dd>
                        <div class="">
                            <div class="nwp mpm"></div>
                        </div>
                    </dd>
                </dl>

                <dl class="clearBoth"></dl>




                <dl class="clear no_margin hide2" style="z-index:8">
                    <dt>贷款利率：</dt>
                    <dd>
                        <div class="lilv item_drop" style="z-index:2;">
                            <span th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新基准利率'}"></span>
                            <div class="hide_box" style="display: none;">
                                <ul id="original_rate_b">
                                    <li><a href="javascript:;" data-date="20150301" data-discount="1.1" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新利率1.1倍'}"></a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20150301" data-discount="1" class="selected_drop_item" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新基准利率'}"></a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20150301" data-discount="0.95" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新利率9.5折'}"></a>
                                    </li>
                                    <li><a href="javascript:;" data-date="20150301" data-discount="0.9" th:text="${#dates.format(new java.util.Date().getTime(), 'yyyy') + '最新利率9折'}"></a>
                                    </li>

                                </ul>
                            </div>
                        </div>
                        <div class="shoudong">
                                    <span class="inline_block guding">
                                    <b class="lilu_box"><input type="text" id="rate_b" data-type="number" name="lilu_b" value="4.9">
                                    <em>%</em></b>                                
                                </span>
                            <span class="hint_text">当时利率，可以修改</span>
                            <div class="error_msg" data-for="rate_b">请输入正确的贷款利率</div>
                        </div>
                    </dd>
                </dl>
                <dl class="clear no_margin" style="z-index:3">
                    <dt>贷款年限：</dt>
                    <dd>
                        <div class="loan_year item_drop v3">
                            <span id="loan_time_select_b">30年（360期）</span>
                            <div class="hide_box" style="display: none;">
                                <ul id="loan_time_b">
                                    <li><a href="javascript:;">1年（12期）</a>
                                    </li>
                                    <li><a href="javascript:;">2年（24期）</a>
                                    </li>
                                    <li><a href="javascript:;">3年（36期）</a>
                                    </li>
                                    <li><a href="javascript:;">4年（48期）</a>
                                    </li>
                                    <li><a href="javascript:;">5年（60期）</a>
                                    </li>
                                    <li><a href="javascript:;">6年（72期）</a>
                                    </li>
                                    <li><a href="javascript:;">7年（84期）</a>
                                    </li>
                                    <li><a href="javascript:;">8年（96期）</a>
                                    </li>
                                    <li><a href="javascript:;">9年（108期）</a>
                                    </li>
                                    <li><a href="javascript:;">10年（120期）</a>
                                    </li>
                                    <li><a href="javascript:;">11年（132期）</a>
                                    </li>
                                    <li><a href="javascript:;">12年（124期）</a>
                                    </li>
                                    <li><a href="javascript:;">13年（156期）</a>
                                    </li>
                                    <li><a href="javascript:;">14年（168期）</a>
                                    </li>
                                    <li><a href="javascript:;">15年（180期）</a>
                                    </li>
                                    <li><a href="javascript:;">16年（192期）</a>
                                    </li>
                                    <li><a href="javascript:;">17年（204期）</a>
                                    </li>
                                    <li><a href="javascript:;">18年（216期）</a>
                                    </li>
                                    <li><a href="javascript:;">19年（228期）</a>
                                    </li>
                                    <li><a href="javascript:;">20年（240期）</a>
                                    </li>
                                    <li><a href="javascript:;">21年（252期）</a>
                                    </li>
                                    <li><a href="javascript:;">22年（264期）</a>
                                    </li>
                                    <li><a href="javascript:;">23年（276期）</a>
                                    </li>
                                    <li><a href="javascript:;">24年（288期）</a>
                                    </li>
                                    <li><a href="javascript:;">25年（300期）</a>
                                    </li>
                                    <li><a href="javascript:;">26年（312期）</a>
                                    </li>
                                    <li><a href="javascript:;">27年（324期）</a>
                                    </li>
                                    <li><a href="javascript:;">28年（336期）</a>
                                    </li>
                                    <li><a href="javascript:;">29年（348期）</a>
                                    </li>
                                    <li><a href="javascript:;" class="selected_drop_item">30年（360期）</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <p class="your_want">您期望偿还贷款的年限</p>
                    </dd>
                </dl>
                <dl class="clear">
                    <dt class="h0"></dt>
                    <dd class="jisuan">
                        <input type="button" value="开始计算" class="Start_calculation_rate">
                        <a href="javascript:;" id="reset_rate">清空重填</a>
                    </dd>
                </dl>
            </form>
        </div>
    </div>
    <div class="counter_right">
        <div class="counter_right_tt counter_moudle_tt">
            <h3>计算结果</h3>
        </div>
        <div class="counter_gongjijin clear">
            <div class="c_gjj_a c_gjj_moudle">
                <h3>等额本息还款
                    <span class="tips_mark">
                            <i></i>
                            <div class="tips_div" style="display: none;">
                                <div class="tips_div_cen">
                                    <s></s>
                                    <p>指借款人每月按相等的金额偿还贷款本息，其中每月贷款利息按月初剩余贷款本金计算并逐月结清。<a href="/news/view/46498" target="_blank">详细信息&gt;&gt;</a></p>
                                </div>
                            </div>
                        </span>
                </h3>
                <div class="c_gjj_moudle_bd">
                    <dl class="clear">
                        <dt>房屋总价</dt>
                        <dd class="guding"><span id="total_price_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>贷款总额</dt>
                        <dd class="guding"><span id="total_loan_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>还款总额</dt>
                        <dd class="guding"><span id="total_pay_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>支付利息</dt>
                        <dd class="guding"><span id="interest_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>首期付款</dt>
                        <dd class="guding"><span id="first_pay_bx"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>贷款月数</dt>
                        <dd class="guding"><span id="loan_month_bx"></span><em>月</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>月均还款</dt>
                        <dd class="guding"><span id="average_pay_bx"></span><em>元</em>
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="c_gjj_b c_gjj_moudle">
                <h3>等额本金还款
                    <span class="tips_mark">
                            <i></i>
                            <div class="tips_div" style="display: none;">
                                <div class="tips_div_cen">
                                    <s></s>
                                    <p>指本金保持相同，利息逐月递减，月还款数递减；由于每月的还款本金额固定，而利息越来越少，贷款人起初还款压力较大，但是随时间的推移每月还款数也越来越少。<a href="/news/view/46498" target="_blank">详细信息&gt;&gt;</a></p>
                                </div>
                            </div>
                        </span>
                </h3>
                <div class="c_gjj_moudle_bd">
                    <dl class="clear">
                        <dt>房屋总价</dt>
                        <dd class="guding"><span id="total_price_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>贷款总额</dt>
                        <dd class="guding"><span id="total_loan_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>还款总额</dt>
                        <dd class="guding"><span id="total_pay_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>支付利息</dt>
                        <dd class="guding"><span id="interest_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>首期付款</dt>
                        <dd class="guding"><span id="first_pay_bj"></span><em>元</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>贷款月数</dt>
                        <dd class="guding"><span id="loan_month_bj"></span><em>月</em>
                        </dd>
                    </dl>
                    <dl class="clear">
                        <dt>首月还款</dt>
                        <dd class="guding meiyue_huankuan"><span id="average_pay_bj"></span><a href="javascript:;" class="huankuan_mingxi">明细</a>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="beizhu">
            <h3><span>备注：</span>（以上结果仅供参考）</h3>
            <p>目前商贷执行首套、二套差别化贷款原则；</p>
            <p>购买首套房时，最低首付为3成，利率为基准利率 ；</p>
            <p>购买二套房时，最低首付为4成，利率上浮1.1倍；</p>
        </div>
    </div>
</div>
</div>
<div class="popup" id="popup_one">
    <div class="popBox">
        <div class="popup_hd clear">
            <h5>等额本金每月还款明细</h5>
            <a class="close_ico" href="javascript:;"></a>
        </div>
        <div class="popup_bd">
            <div class="onemain">
                <div class="omi"><em>第1期</em><em>4802.78元</em></div>
            </div>
            <table cellpadding="0" cellspacing="0" border="0" class="popup_table" style="display: none">

            </table>
        </div>
    </div>
    <div class="mask"></div>
</div>

<!--<script src="https://static.fangxiaoer.com/js/jsq/common.js"></script>-->
<script src="https://static.fangxiaoer.com/web/styles/sy/lpr/common.js"></script>
<script>
    var monthLpr = parseInt($('#loan_time_select_a').text()) * 12;
    //
    // getLPRLoanRate(monthLpr)//获取利率 五年期
    // getLPRLoanRate(12)//获取利率 一年期


    $("#loan_time_a").find('a').click(()=>{
        setTimeout(()=>{
            monthLpr = parseInt($('#loan_time_select_a').text()) * 12;
            // getLPRLoanRate(monthLpr)
        },500)
    })

    function getLPRLoanRate(m){
        $.ajax({
            type: "POST",
            url: "/viewLoanFilter",
            data: {'month': m,'discount':1},
            success: function (data) {
                console.log(data)
                if(m==12){
                    // $(".newq").text(data.content.rate1)
                    // $(".newq").attr('rt2',data.content.rate1)
                }else{
                    // $(".newp").text(data.content.rate1)
                    // $(".newp").attr('rt1',data.content.rate1)
                    newLpr(data.content.rate1,$(".wum2").val())
                }
            }
        })
    }
    $.ajax({
        type: "POST",
        url: "/LPRHistoryFilter",
        success: function (data) {
            var data1 = data.content[0]
            var data2 = data.content[1]
            //当月sameMonth
            $(".sameMonth div").eq(0).text(data1.pubdate)
            $(".sameMonth div").eq(1).text(data1.lprOne)
            $(".sameMonth div").eq(2).text(data1.lprFive)
            $(".sameMonth2 div").eq(0).text(data1.pubdate)
            $(".sameMonth2 div").eq(1).text(data1.lprOne)
            $(".sameMonth2 div").eq(2).text(data1.lprFive)
            //上个月lastMonth
            $(".lastMonth div").eq(0).text(data2.pubdate)
            $(".lastMonth div").eq(1).text(data2.lprOne)
            $(".lastMonth div").eq(2).text(data2.lprFive)
            $(".lastMonth2 div").eq(0).text(data2.pubdate)
            $(".lastMonth2 div").eq(1).text(data2.lprOne)
            $(".lastMonth2 div").eq(2).text(data2.lprFive)

            //设置当前月
            var pubdate=data1.pubdate.split('-')[1] + '.' + data1.pubdate.split('-')[2]
            $(".mfd").text(pubdate)

            $(".newq").text(data1.lprOne)
            $(".newq").attr('rt2',data1.lprOne)
            $(".newp").text(data1.lprOne)
            $(".newp").attr('rt1',data1.lprFive)

            $(".setjd").attr('jd',data1.basicPoint)


            $(".wum2").val(data1.basicPoint)//基点
            $(".wum").val(data1.basicPoint)//基点
            $(".newp").text(data1.lprFive)
            $(".newp").attr('rt1',data1.lprFive)
            newLpr(data1.lprFive,$(".wum2").val())

            $("#loan_time_a a,#loan_time_b a").click(function(){
                var ckyear=$(this).text().split('年')[0]
                if(ckyear<=5){
                    $(".np").text(data1.lprOne)
                    $(".np").attr('rt1',data1.lprOne)
                    newLpr(data1.lprOne,$(".wum2").val())
                }else{
                    $(".np").text(data1.lprFive)
                    $(".np").attr('rt1',data1.lprFive)
                    newLpr(data1.lprFive,$(".wum2").val())
                }
            })

            //切换按面积、按贷款额度
            $(".fangshi label").click(function(){
                var a=$('#loan_time_select_a').text().split('年')[0]
                var b=$('#loan_time_select_b').text().split('年')[0]
                var d=$(this).find('input').attr('id')
                if(d=='num_cal'){
                    //按贷款额度
                    console.log(2)
                    if(b<=5){
                        $(".np").text(data1.lprOne)
                        $(".np").attr('rt1',data1.lprOne)
                        newLpr(data1.lprOne,$(".wum2").val())
                    }else{
                        $(".np").text(data1.lprFive)
                        $(".np").attr('rt1',data1.lprFive)
                        newLpr(data1.lprFive,$(".wum2").val())
                    }
                }else{
                    //按面积
                    console.log(b)
                    if(a<=5){
                        $(".np").text(data1.lprOne)
                        $(".np").attr('rt1',data1.lprOne)
                        newLpr(data1.lprOne,$(".wum2").val())
                    }else{
                        $(".np").text(data1.lprFive)
                        $(".np").attr('rt1',data1.lprFive)
                        newLpr(data1.lprFive,$(".wum2").val())
                    }
                }
            })

        }
    })

    //修改箭头方向
    $(document).bind("click",function(e){
        $("#shoufu_rate,#loan_time_select_b,#loan_time_select_a").css('background-position','232px 14px')
    });
    $(".hide_box li a").click(function(event){
        $(".v1").find('span').css('background-position','232px 14px')
        $(".v2").find('span').css('background-position','232px 14px')
    })

    //修改index层级 遮挡问题
    $("#ind,#ind2").mouseover(function(){
        $("#indm").css("z-index","100");
    });
    $("#ind,#ind2").mouseout(function(){
        $("#indm").css("z-index","7");
    });
    $("#ind3,#ind4").mouseover(function(){
        $("#indm3").css("z-index","100");
    });
    $("#ind3,#ind4").mouseout(function(){
        $("#indm3").css("z-index","7");
    });

    //设置当前月
    /*var myDate = new Date();
    if(myDate.getDay() > 20){
        $(".mfd").text(appendZero(myDate.getMonth() - 1)+'.20')
    }else{
        $(".mfd").text(appendZero(myDate.getMonth())+'.20')
    }
    function appendZero(obj){
        if(obj < 10){
            return '0' + obj
        }else{
            return obj
        }
    }*/

    //商贷利率
    function newLpr(sy,val){
        if(val=='' || val==undefined){
            val=0
        }
        var typeval3=parseFloat(sy)+val*0.01
        typeval3 = typeval3.toFixed(2)
        $(".mpm").empty()
        $(".mpm").append(sy+'%+'+val+'‱='+typeval3+'%')
    }


    $(function() {

        //公积金贷款按面积
        (function() {

            var rate_input = $('#rate_a'),
                business_rate_data = {
                    date_20150301: [4.35, 4.75, 4.75, 4.90]
                };

            //计算利率
            function setRate() {
                var $current = $('#original_rate').find('.selected_drop_item');
                var year = parseInt($('#loan_time_select_a').text()),
                    rate_result;
                if (year == 1) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][0];
                } else if (year <= 3) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][1];
                } else if (year <= 5) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][2];
                } else {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][3];
                }
                rate_input.val((rate_result * $current.attr('data-discount')).toFixed(4));
            }

            //利率联动
            $('#original_rate, #loan_time_a').bind('select', function(event, current) {
                setRate();
            });

            var applyUtil = {
                checkDanJia: function(danjia) {
                    danjia = $.trim(danjia);
                    if (getBytesLength(danjia) > 9 || getBytesLength(danjia) < 1 || isNaN(danjia) || parseFloat(danjia) > 999999) {
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $('#fund_area_a input[name=danjia]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_a input[name=danjia]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkMianJi: function(mianji) {
                    mianji = $.trim(mianji);
                    if (getBytesLength(mianji) > 9 || getBytesLength(mianji) < 1 || isNaN(mianji) || parseFloat(mianji) > 9999) {
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $('#fund_area_a input[name=mianji]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_a input[name=mianji]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkLiLuA: function(lilu_a) {
                    lilu_a = $.trim(lilu_a);
                    if (isNaN(lilu_a) || lilu_a == '' || lilu_a <= 0 || parseInt(lilu_a) >= 100) {
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $('.error_msg[data-for="rate_a"]').css('visibility', 'visible');
                        //$('#fund_area_a input[name=lilu_a]').parent().parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_a input[name=lilu_a]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkLpr: function(input_lpr,ji){
                    input_lpr = $.trim(input_lpr);
                    if (isNaN(input_lpr) || input_lpr == '' ) {
                        var jidain='.error_msg[data-for="'+ji+'"]'
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $(jidain).css('visibility', 'visible');
                        $('#fund_area_a input[name=input_lpr]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkshoufu: function(shoufu) {
                    shoufu = $.trim(shoufu);
                    const regex = /^\d+(\.\d{1,2})?$/;
                    if (!regex.test(shoufu) || getBytesLength(shoufu) < 1 || isNaN(shoufu) || parseFloat(shoufu) > 10 || parseFloat(shoufu) < 0) {
                        $('#fund_area_a').find('.error_msg').css('visibility', 'hidden');
                        $('#fund_area_a input[name=shoufu]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_a input[name=shoufu]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
            };

            $('.txt[data-type="number"]').focus(function() {
                $(this).removeClass('error_txt');
                $(this).parent('.guding').children('.error_msg').css('visibility', 'hidden');
            }).blur(function() {
                if ($(this).val() == '') {
                    $(this).addClass('error_txt');
                    $(this).parent('.guding').children('.error_msg').css('visibility', 'visible');
                }
            })

            function setSelectedItem(ul, selectedItem) {
                ul.children().each(function() {
                    if ($.trim($(this).text()) == selectedItem) {
                        $(this).children().trigger('click');
                        return false;
                    }
                });
            }

            $('#area_cal').trigger('click');
            if ($('#area_cal').prop('checked')) {
                setRate();
            }
            var date=new Date;
            var year2=date.getFullYear();
            //清空重填
            $('#reset_area').click(function() {
                $('#first_house').attr('checked', 'checked');
                $('#fund_area_a input[name=danjia]').val('');
                $('#fund_area_a input[name=mianji]').val('');
                $(".wum2").val($(".setjd").attr('jd'))
                // $(".wum2").val('75')
                $(".wum2").siblings('.error_msg').css('visibility', 'hidden')
                // newLpr($('.np').text())
                $('#shoufu').val('1.5')
                setSelectedItem($('#original_rate'), year2 +'最新基准利率');
                setSelectedItem($('#loan_time_a'), '30年（360期）');
                $('[data-type="number"]').each(function() {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });
            });

            //北京市首付及利率
            //首套房限制
            $('.xingzhi #first_house').click(function() {
                $('#shoufu').val('3')
                setSelectedItem($('#original_rate'), year2 +'最新基准利率');
            });


            //二套房限制
            $('.xingzhi #two_house').click(function() {
                $('#shoufu').val('4')
                setSelectedItem($('#original_rate'), year2 +'最新利率1.1倍');
            });

            //点击计算按钮之后
            $('.Start_calculation_area').mousedown(function() {
                var danjia = $("#fund_area_a").find('input[name=danjia]').val();
                var mianji = $("#fund_area_a").find('input[name=mianji]').val();
                var lilu_a = $("#fund_area_a").find('input[name=lilu_a]').val();
                var input_lpr = $("#fund_area_a").find('input[name=input_lpr]').val();//基点
                var shoufu = $("#fund_area_a").find('input[name=shoufu]').val();//首付


                // return false;
                if (!applyUtil.checkDanJia(danjia) || !applyUtil.checkMianJi(mianji)) {
                    return false;
                }
                if (!applyUtil.checkLiLuA(lilu_a)) {
                    return false;
                }
                //基点
                if (!applyUtil.checkLpr(input_lpr,'jidian')){
                    return false;
                }

                //首付
                if (!applyUtil.checkshoufu(shoufu)){
                    return false;
                }

                //关闭错误提示
                $('[data-type="number"]').each(function() {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });

                business_area();//计算

                //显示计算结果
                if ($('.counter_right').css('display') === 'none') {
                    if (!$(this).parents().find(".counter_left").is(":animated")) {
                        $('.counter_left').animate({
                            'margin-left': '0px'
                        }, 500, function() {
                            $('.arrows_ico').show();
                            $('.counter_right').fadeIn();
                            $('.focus_counter').addClass('focus_counter_ed');
                        });
                    };
                };
                //添加统计正确计算结果的代码
                // deteled by sunlong
                // addRecord(1);
            });

            //在GBK编码里，除了ASCII字符，其它都占两个字符宽
            function getBytesLength(str) {
                return str.replace(/[^\x00-\xff]/g, 'xx').length;
            };

            function setBytesLength(str, num) {
                var len = 0;
                for (var i = 0; i < str.length; i++) {
                    if (str.charCodeAt(i) > 128) {
                        len = len + 2;
                    } else {
                        len = len + 1;
                    }
                    if (len == num) {
                        i = i + 1;
                        return i;
                    }
                }
            };
        })()
    });

    function business_area() {
        var month = parseInt($('#loan_time_select_a').text()) * 12;//商贷月份
        var price = parseFloat($('#danjia').val());//单价
        var area = parseFloat($('#mianji').val());//面积
        // var shoufu_rate = parseInt($('#shoufu').val());//首付 默认3
        var shoufu_rate = $('#shoufu').val();//首付 默认3
        var lilv = $('#rate_a').val() / 100; //得到利率
        var jidian= $("#fund_area_a").find('input[name=input_lpr]').val()//基点

        console.log(month,price,area,shoufu_rate,lilv,jidian)
        //房款总额
        var fangkuan_total = Math.round(price * area * 100) / 100;
        $('#total_price_bx, #total_price_bj').text(fangkuan_total);
        //贷款总额
        var daikuan_total = fangkuan_total * (1 - shoufu_rate / 10);
        $('#total_loan_bx, #total_loan_bj').text(Math.round(daikuan_total * 100) / 100);
        //首期付款
        var money_first = fangkuan_total - daikuan_total;
        $('#first_pay_bx, #first_pay_bj').text(Math.round(money_first * 100) / 100);
        $('#loan_month_bx, #loan_month_bj').text(month);


        //显示计算结果
        daikuan_total=daikuan_total/10000
        loanWithLPR(daikuan_total,'',month,'',1,'',1,jidian)//本息
        loanWithLPR(daikuan_total,'',month,'',1,'',2,jidian)//本金
    }
    //新版LPR贷款计算器
    function loanWithLPR(a,b,c,d,e,f,g,h){
        $.ajax({
            type: "POST",
            url: "/calForLoan",
            data: {
                priceA:a,//商贷金额
                priceB:b,//公积金金额
                month1:c,//商贷月份
                month2:d,//公积金月份
                loanType:e,//贷款类型（1.商贷，2.公积金，3.组合贷）
                discount:f,//公积金倍率
                countMethod:g,//计算方式（1.等额本息，2.等额本金）
                basePoint:h,//基点
            },
            headers : {
                'Content-Type' : 'application/x-www-form-urlencoded'
            },
            success: function (data) {
                console.log(data.content)
                if(data.status==1){
                    if(g==1){
                        //本息
                        $('#total_pay_bx').text(data.content.total)//还款总额
                        $('#interest_bx').text(data.content.totalInterest)//利息总额
                        $('#average_pay_bx').text(data.content.everyMonth)//月均还款
                    }else if(g==2){
                        //本金
                        $('#total_pay_bj').text(data.content.total)//还款总额
                        $('#interest_bj').text(data.content.totalInterest)//利息总额
                        $('#average_pay_bj').text(data.content.everyMonth)//月均还款

                        //还款明细
                        var qi=data.content.years*12
                        $(".onemain").empty()
                        for(i=0;i<qi;i++){
                            var j=i+1
                            var oneStr='<div class="omi"><em>第'+j+'期</em><em>'+data.content.detail[i].everyTotalPay+'元</em></div>'
                            $(".onemain").append(oneStr)
                        }
                    }
                }
            }
        })
    }


    //本金还款的月还款额(参数: 年利率 / 贷款总额 / 贷款总月份 / 贷款当前月0～length-1)
    function getMonthMoney2(lilv, total, month, cur_month) {
        var lilv_month = lilv / 12; //月利率
        var benjin_money = total / month;
        return (total - benjin_money * cur_month) * lilv_month + benjin_money;

    }

    //本息还款的月还款额(参数: 年利率/贷款总额/贷款总月份)
    function getMonthMoney1(lilv, total, month) {
        var lilv_month = lilv / 12; //月利率
        return total * lilv_month * Math.pow(1 + lilv_month, month) / (Math.pow(1 + lilv_month, month) - 1);
    }

    $(function() {
        //公积金贷款按贷款额度
        (function() {
            var rate_input = $('#rate_b'),
                business_rate_data = {
                    date_20150301: [4.35, 4.75, 4.75, 4.90],
                };

            //计算利率
            function setRate() {
                var $current = $('#original_rate_b').find('.selected_drop_item');
                var year = parseInt($('#loan_time_select_b').text()),
                    rate_result;
                if (year == 1) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][0];
                } else if (year <= 3) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][1];
                } else if (year <= 5) {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][2];
                } else {
                    rate_result = business_rate_data['date_' + $current.attr('data-date')][3];
                }
                rate_input.val((rate_result * $current.attr('data-discount')).toFixed(4));
            }

            //利率联动
            $('#original_rate_b, #loan_time_b').bind('select', function(event, current) {
                setRate();
            });

            //验证方法
            var applyUtil_b = {
                checkzongE: function(zonge) {
                    zonge = $.trim(zonge);
                    if (getBytesLength(zonge) > 9 || getBytesLength(zonge) < 1 || isNaN(zonge) || parseFloat(zonge) > 99999) {
                        $('#fund_area_b').find('.error_msg').css('visibility', 'hidden');
                        $('#fund_area_b input[name=zonge]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_b input[name=zonge]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkLiLuB: function(lilu_b) {
                    lilu_b = $.trim(lilu_b);
                    if (isNaN(lilu_b) || lilu_b == '' || lilu_b <= 0 || parseInt(lilu_b) >= 100) {
                        $('#fund_area_b').find('.error_msg').css('visibility', 'hidden');
                        $('.error_msg[data-for="rate_b"]').css('visibility', 'visible');
                        //$('#fund_area_b input[name=lilu_b]').parent().children('.error_msg').css('visibility', 'visible');
                        $('#fund_area_b input[name=lilu_b]').addClass('error_txt');
                        return false;
                    }
                    return true;
                },
                checkLpr: function(input_lpr,ji){
                    input_lpr = $.trim(input_lpr);
                    if (isNaN(input_lpr) || input_lpr == '') {
                        var jidain='.error_msg[data-for="'+ji+'"]'
                        $('#fund_area_b').find('.error_msg').css('visibility', 'hidden');
                        $(jidain).css('visibility', 'visible');
                        $('#fund_area_b input[name=input_lpr]').addClass('error_txt');
                        return false;
                    }
                    return true;
                }
            };

            $('.txt[data-type="number"]').focus(function() {
                $(this).removeClass('error_txt');
                $(this).parent('.guding').children('.error_msg').css('visibility', 'hidden');
            }).blur(function() {
                if ($(this).val() == '') {
                    $(this).addClass('error_txt');
                    $(this).parent('.guding').children('.error_msg').css('visibility', 'visible');
                }
            })

            function setSelectedItem(ul, selectedItem) {
                ul.children().each(function() {
                    if ($.trim($(this).text()) == selectedItem) {
                        $(this).children().trigger('click');
                        return false;
                    }
                });
            }

            //清空重填
            $('#reset_rate').click(function() {
                var date=new Date;
                var year2=date.getFullYear();
                $('#fund_area_b input[name=zonge]').val('');
                $(".wum").val($(".setjd").attr('jd'))
                // $(".wum").val('75')
                var a=$(".newp").attr('rt1')
                newLpr(a,75)
                $('#fund_area_b').find('.error_msg').css('visibility', 'hidden');
                setSelectedItem($('#original_rate_b'), year2 + '最新基准利率');
                setSelectedItem($('#loan_time_b'), '30年（360期）');
                $('[data-type="number"]').each(function() {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });
            });

            //点击计算按钮之后
            $('.Start_calculation_rate').mousedown(function() {
                var zonge = $("#fund_area_b").find('input[name=zonge]').val();//贷款总额
                var lilu_b = $("#fund_area_b").find('input[name=lilu_b]').val();//贷款利率
                var input_lpr = $("#fund_area_b").find('input[name=input_lpr2]').val();//基点

                // 校验表单
                if (!applyUtil_b.checkzongE(zonge)) {
                    return false;
                }
                if (!applyUtil_b.checkLiLuB(lilu_b)) {
                    return false;
                }
                //基点
                if (!applyUtil_b.checkLpr(input_lpr,'jidian2')){
                    return false;
                }

                //关闭错误提示
                $('.ping_gu').hide();
                $('[data-type="number"]').each(function() {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });

                business_num();//请求计算

                //显示计算结果
                if ($('.counter_right').css('display') === 'none') {
                    if (!$(this).parents().find(".counter_left").is(":animated")) {
                        $('.counter_left').animate({
                            'margin-left': '0px'
                        }, 500, function() {
                            $('.arrows_ico').show();
                            $('.counter_right').fadeIn();
                            $('.focus_counter').addClass('focus_counter_ed');
                        });
                    };
                };
                //添加统计正确计算结果的代码
                addRecord(1);
            });


            //在GBK编码里，除了ASCII字符，其它都占两个字符宽
            function getBytesLength(str) {
                return str.replace(/[^\x00-\xff]/g, 'xx').length;
            };

            function setBytesLength(str, num) {
                var len = 0;
                for (var i = 0; i < str.length; i++) {
                    if (str.charCodeAt(i) > 128) {
                        len = len + 2;
                    } else {
                        len = len + 1;
                    }
                    if (len == num) {
                        i = i + 1;
                        return i;
                    }
                }
            };
        })()
    });

    function business_num() {
        var month = parseInt($('#loan_time_select_b').text()) * 12;
        var lilv = $('#rate_b').val() / 100; //得到利率
        var jidian2= $(".wum").val()//基点


        //房款总额
        $('#total_price_bx, #total_price_bj, #first_pay_bx, #first_pay_bj').parent().parent().remove();
        //贷款总额
        var daikuan_total = parseFloat($('#loan_money').val()) * 10000;
        $('#total_loan_bx, #total_loan_bj').text(Math.round(daikuan_total * 100) / 100);
        $('#loan_month_bx, #loan_month_bj').text(month);


        //显示计算结果
        daikuan_total=daikuan_total/10000
        loanWithLPR2(daikuan_total,'',month,'',1,'',1,jidian2)//本息
        loanWithLPR2(daikuan_total,'',month,'',1,'',2,jidian2)//本金
    }
    //新版LPR贷款计算器
    function loanWithLPR2(a,b,c,d,e,f,g,h){
        $.ajax({
            type: "POST",
            url: "/calForLoan",
            data: {
                priceA:a,//商贷金额
                priceB:b,//公积金金额
                month1:c,//商贷月份
                month2:d,//公积金月份
                loanType:e,//贷款类型（1.商贷，2.公积金，3.组合贷）
                discount:f,//公积金倍率
                countMethod:g,//计算方式（1.等额本息，2.等额本金）
                basePoint:h,//基点
            },
            headers : {
                'Content-Type' : 'application/x-www-form-urlencoded'
            },
            success: function (data) {
                console.log(data.content)
                if(data.status==1){
                    if(g==1){
                        //本息
                        $('#total_pay_bx').text(data.content.total)//还款总额
                        $('#interest_bx').text(data.content.totalInterest)//利息总额
                        $('#average_pay_bx').text(data.content.everyMonth)//月均还款
                    }else if(g==2){
                        //本金
                        $('#total_pay_bj').text(data.content.total)//还款总额
                        $('#interest_bj').text(data.content.totalInterest)//利息总额
                        $('#average_pay_bj').text(data.content.everyMonth)//月均还款

                        //还款明细
                        var qi=data.content.years*12
                        $(".onemain").empty()
                        for(i=0;i<qi;i++){
                            var j=i+1
                            var oneStr='<div class="omi"><em>第'+j+'期</em><em>'+data.content.detail[i].everyTotalPay+'元</em></div>'
                            $(".onemain").append(oneStr)
                        }
                    }
                }
            }
        })
    }



    //本金还款的月还款额(参数: 年利率 / 贷款总额 / 贷款总月份 / 贷款当前月0～length-1)
    function getMonthMoney2(lilv, total, month, cur_month) {
        var lilv_month = lilv / 12; //月利率
        var benjin_money = total / month;
        return (total - benjin_money * cur_month) * lilv_month + benjin_money;

    }

    //本息还款的月还款额(参数: 年利率/贷款总额/贷款总月份)
    function getMonthMoney1(lilv, total, month) {
        var lilv_month = lilv / 12; //月利率
        return total * lilv_month * Math.pow(1 + lilv_month, month) / (Math.pow(1 + lilv_month, month) - 1);
    }


</script>

<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>

<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>

</html>
