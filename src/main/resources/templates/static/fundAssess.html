<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>公积金贷款额度评估 - 房小二网</title>
    <meta name="keywords" content="沈阳贷款,房贷,沈阳公积金贷款,沈阳商业贷款,提前还款，购房能力评估"/>
    <meta name="description" th:content="'房小二网购房工具为购房者提供'+${yearTime}+'年房贷计算器及各种在线购房计算工具。包括房贷计算器，公积金贷款计算器，商业贷款计算器，组合贷款计算器，房贷提前还贷计算器，税费计算器，购房能力评估，公积金贷款额度评估等服务。'"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link type="text/css" rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/tool/jsq.css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <style>
        .counter_left {
            width: 380px;
            padding: 35px 15px 55px 20px;
        }
        .sperator {
            border: none;
            border-bottom: 1px dotted #ccc;
            margin: 0px 10px 20px 0;
        }
        .counter_left dt {
            width: 182px;
        }
        .counter_left dd .txt {
            width: 140px;
            color: #333;
            font-size: 16px;
            vertical-align: top;
            padding-right: 40px;
        }
        .item_drop span {
            display: block;
            height: 32px;
            width: 160px;
            padding-left: 10px;
            background: #fff url(https://static.fangxiaoer.com/web/images/sy/tool/jsq/icon_bg01.gif) 170px 14px no-repeat;
            border: 1px solid #dfdfdf;
            color: #333;
            overflow: hidden;
            padding-right: 20px;
        }
        .compute_btn {
            width: 194px;
            height: 40px;
            background-color: #ff6600;
            border-radius:5px;
            border: none;
            font-size: 18px;
            font-family: "Microsoft Yahei";
            color: #fff;
            cursor: pointer;
            margin: 0 auto;
            display: block;
            margin-bottom: 40px;
            zoom: 1;
        }
        .loan_limit .txt {
            width: 140px;
            color: #999999;
            font-size: 16px;
            vertical-align: top;
            padding-right: 40px;
            padding-left: 10px;
            border: 1px solid #dfdfdf;
            line-height: 32px;
            height: 32px;
            vertical-align: middle;
        }
        .result_left {
            width: 170px;
            text-align: right;
        }
        .result em {
            line-height: 34px;
        }
        .c_gjj_moudle_bd dt {
            float: left;
            display: inline;
            width: 80px;
            height: 39px;
            background: #fbfbfb;
            border-right: 1px solid #efefef;
            line-height: 39px;
            color: #666;
            font-size: 14px;
            text-align: center;
        }
        .c_gjj_a {
            margin-right: 30px;
        }
        .c_gjj_a dd {
            width: 125px;
        }
        .loan_limit {
            margin-top: 10px;
        }
        .result {
            font-size: 14px;
            color: #333;
            margin-bottom: 0;
        }
        .item_drop .hide_box {
            z-index: 100;
            max-height: 300px;
            width: 190px;
            overflow: auto;
        }
        .c_gjj_moudle_bd .detail_dd {
            padding-right: 5px;
            width: 160px;
        }
        .detail_unit {
            margin-right: 5px;
        }
        .guding .error_txt {
            border: 1px solid red;
        }
        #compute_result {
            display: none;
        }
    </style>
</head>

<body class="w1210">
<form name="Form1" method="post" action="gongjijindaikuanedupinggu.aspx" id="Form1">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav"></div>
    <!--页面位置-->
    <div class="w place">您的位置：<a href="/">沈阳房产网</a> &gt;  公积金贷款额度评估</div>

    <div class="w wrap_nav">
        <a href="business.htm">商业贷款计算器</a>
        <a href="fund.htm" >公积金贷款计算器</a>
        <a href="assemble.htm">组合贷款计算器</a>
        <a href="advance.htm">提前还款计算器</a>
        <a href="tallage.htm">税费计算器</a>
        <a href="ability.htm">购房能力评估</a>
        <a href="fundAssess.htm" class="hover">公积金贷款额度评估</a>
    </div>
    <div class="cl"></div>
    <div class="focus_counter clear">
        <div class="counter_left">
            <s class="arrows_ico"></s>
            <div class="counter_left_tt counter_moudle_tt">
                <h3>填写信息</h3>
            </div>
            <div class="fanshi_bd">
                <!--公积金月缴存额-->
                <dl class="clear margin_top_20">
                    <dt>公积金月缴存额：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="fund_money" maxlength="9" data-type="number" class="txt">
                        <em>元</em>
                        <div class="error_msg" data-for="fund_money">请输入正确的缴存额</div>
                    </dd>

                </dl>
                <!--缴存比例-->
                <dl class="clear no_margin">
                    <dt>缴存比例：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="fund_ratio" maxlength="9" data-type="number" class="txt" value="12">
                        <em>%</em>
                        <div class="error_msg" data-for="fund_ratio">请输入正确的缴存比例</div>
                    </dd>

                </dl>
                <hr class="sperator">
                <!--配偶公积金月缴存额-->
                <dl class="clear no_margin">
                    <dt>配偶公积金月缴存额：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="spouse_money" maxlength="9" data-type="number" class="txt">
                        <em>元</em>
                        <div class="error_msg" data-for="spouse_money">请输入正确的缴存额</div>
                    </dd>

                </dl>
                <!--缴存比例-->
                <dl class="clear no_margin">
                    <dt>缴存比例：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="spouse_ratio" maxlength="9" data-type="number" class="txt" value="12">
                        <em>%</em>
                        <div class="error_msg" data-for="spouse_ratio">请输入正确的缴存比例</div>
                    </dd>

                </dl>
                <hr class="sperator">
                <!--房屋评估价值或实际购房款-->
                <dl class="clear margin_top_20">
                    <dt>房屋评估价值或实际购房款：</dt>
                    <dd>
                    </dd>
                    <dd class="guding">
                        <input type="text" id="house_price" maxlength="9" data-type="number" class="txt">
                        <em>万元</em>
                        <div class="error_msg" data-for="house_price">请输入正确的金额</div>
                    </dd>

                </dl>
                <!--房屋性质-->
                <dl class="clear no_margin">
                    <dt>房屋性质：</dt>
                    <dd>
                        <input name="house_type" id="policy_house" type="radio" value="1">
                        <label for="policy_house">政策性住宅</label>
                        <input name="house_type" id="other_house" type="radio" checked="checked" value="2">
                        <label for="other_house">其它</label>
                    </dd>
                </dl>
                <hr class="sperator margin_top_20">
                <!--贷款申请年限-->
                <dl class="clear no_margin" style="z-index:3;">
                    <dt>贷款申请年限：</dt>
                    <dd>
                        <div class="loan_year item_drop">
                            <span id="loan_time_select">30年(360期)<s></s></span>
                            <div class="hide_box" style="display: none;">
                                <ul id="load_time">
                                    <li><a href="javascript:;">1年(12期)</a>
                                    </li>
                                    <li><a href="javascript:;">2年(24期)</a>
                                    </li>
                                    <li><a href="javascript:;">3年(36期)</a>
                                    </li>
                                    <li><a href="javascript:;">4年(48期)</a>
                                    </li>
                                    <li><a href="javascript:;">5年(60期)</a>
                                    </li>
                                    <li><a href="javascript:;">6年(72期)</a>
                                    </li>
                                    <li><a href="javascript:;">7年(84期)</a>
                                    </li>
                                    <li><a href="javascript:;">8年(96期)</a>
                                    </li>
                                    <li><a href="javascript:;">9年(108期)</a>
                                    </li>
                                    <li><a href="javascript:;">10年(120期)</a>
                                    </li>
                                    <li><a href="javascript:;">11年(132期)</a>
                                    </li>
                                    <li><a href="javascript:;">12年(144期)</a>
                                    </li>
                                    <li><a href="javascript:;">13年(156期)</a>
                                    </li>
                                    <li><a href="javascript:;">14年(168期)</a>
                                    </li>
                                    <li><a href="javascript:;">15年(180期)</a>
                                    </li>
                                    <li><a href="javascript:;">16年(182期)</a>
                                    </li>
                                    <li><a href="javascript:;">17年(204期)</a>
                                    </li>
                                    <li><a href="javascript:;">18年(216期)</a>
                                    </li>
                                    <li><a href="javascript:;">19年(228期)</a>
                                    </li>
                                    <li><a href="javascript:;">20年(240期)</a>
                                    </li>
                                    <li><a href="javascript:;">21年(252期)</a>
                                    </li>
                                    <li><a href="javascript:;">22年(264期)</a>
                                    </li>
                                    <li><a href="javascript:;">23年(276期)</a>
                                    </li>
                                    <li><a href="javascript:;">24年(288期)</a>
                                    </li>
                                    <li><a href="javascript:;">25年(300期)</a>
                                    </li>
                                    <li><a href="javascript:;">26年(312期)</a>
                                    </li>
                                    <li><a href="javascript:;">27年(324期)</a>
                                    </li>
                                    <li><a href="javascript:;">28年(336期)</a>
                                    </li>
                                    <li><a href="javascript:;">29年(348期)</a>
                                    </li>
                                    <li><a href="javascript:;" class="selected_drop_item">30年(360期)</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </dd>
                </dl>
                <!--房屋性质-->
                <dl class="clear margin_top_20">
                    <dt>个人信用等级：</dt>
                    <dd>
                        <input name="credit_level" id="credit_aaa" type="radio" value="1">
                        <label for="credit_aaa">AAA</label>
                        <input name="credit_level" id="credit_aa" type="radio" value="2">
                        <label for="credit_aa">AA</label>
                        <input name="credit_level" id="credit_other" type="radio" checked="checked" value="3">
                        <label for="credit_other">其它</label>
                    </dd>
                </dl>
                <!--计算结果-->
                <dl class="clear">
                    <dt style="width: 160px;"></dt>
                    <dd class="jisuan">
                        <input type="button" class="Start_calculation" value="开始计算">
                        <a href="javascript:;" id="reset">清空重填</a>
                    </dd>
                </dl>
            </div>
        </div>
        <div class="counter_right">
            <div class="counter_right_tt counter_moudle_tt">
                <h3>计算结果</h3>
            </div>
            <div class="counter_gongjijin clear">
                <p class="result"><span class="result_left inline_block">您可申请的最高贷款额度：</span><span id="max_loan_value" class="highlight_result"></span>万元</p>
                <p class="result"><span class="result_left inline_block">您需要的贷款额度：</span>
                    <span class="guding inline_block loan_limit">
                        <input type="text" id="loan_money" maxlength="9" class="txt">
                        <em>万元</em>
                        </span>
                </p>
                <div class="error_msg" data-for="loan_money">请输入正确的贷款额度</div>

                <p></p>
                <button type="button" class="compute_btn">计算每月还款额</button>
            </div>
            <div id="compute_result">
                <hr class="sperator">
                <!--结果-->
                <div class="counter_gongjijin clear">
                    <div class="c_gjj_a c_gjj_moudle">
                        <h3>等额本息还款
                            <span class="tips_mark">
                                <i></i>
                                <div class="tips_div" style="display: none;">
                                    <div class="tips_div_cen">
                                        <s></s>
                                        <div class="tips_div_tt">等额本息还款法：</div>
                                        <p>指借款人每月按相等的金额偿还贷款本息，其中每月贷款利息按月初剩余贷款本金计算并逐月结清。<a href="#" target="_blank"> 详细信息 &gt;&gt;</a></p>
                                    </div>
                                </div>
                            </span>
                        </h3>
                        <div class="c_gjj_moudle_bd">
                            <dl class="clear">
                                <dt>还款总额</dt>
                                <dd class="result"><span id="total_money_debx"></span><span class="unit">元</span>
                                </dd>
                            </dl>
                            <dl class="clear">
                                <dt>支付利息</dt>
                                <dd class="result"><span id="interest_debx"></span><span class="unit">元</span>
                                </dd>
                            </dl>
                            <dl class="clear">
                                <dt>月均还款</dt>
                                <dd class="result"><span id="average_debx"></span><span class="unit">元</span>
                                </dd>
                            </dl>
                        </div>
                    </div>
                    <div class="c_gjj_b c_gjj_moudle">
                        <h3>等额本金还款
                            <span class="tips_mark">
                                <i></i>
                                <div class="tips_div" style="display: none;">
                                    <div class="tips_div_cen">
                                        <s></s>
                                        <div class="tips_div_tt">等额本金还款法：</div>
                                        <p>指本金保持相同，利息逐月递减，月还款数递减；由于每月的还款本金额固定，而利息越来越少，贷款人起初还款压力较大，但是随时间的推移每月还款数也越来越少。<a href="#" target="_blank"> 详细信息 &gt;&gt;</a></p>
                                    </div>
                                </div>
                            </span>
                        </h3>
                        <div class="c_gjj_moudle_bd">
                            <dl class="clear">
                                <dt>还款总额</dt>
                                <dd class="result"><span id="total_money_debj"></span><span class="unit">元</span>
                                </dd>
                            </dl>
                            <dl class="clear">
                                <dt>支付利息</dt>
                                <dd class="result"><span id="interest_debj"></span><span class="unit">元</span>
                                </dd>
                            </dl>
                            <dl class="clear">
                                <dt>首月还款</dt>
                                <dd class="result meiyue_huankuan"><span id="average_debj"></span><span class="unit">元</span><a href="javascript:;" class="huankuan_mingxi">明细</a>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="beizhu">
                    <h3><span>备注：</span>（以上结果仅供参考）</h3>
                    <p>北京市管公积金个人信用等级：AAA级，AA级，其他；国管公积金个人信用等级：A级，B级，其他；市管公积金AAA级和AA级分别对应国管公积金的A级和B级。</p>
                    <p>购买政策性住房或中小户型首套自住房，个人信用等级为AAA级（国管A级），贷款最高额度上浮30%，即不超过104万元；个人信用等级为AA级（国管B级），贷款最高额度上浮15%，即不超过92万元；对于购买套型建筑面积在90m²以上非政策性住房的，最高贷款额度为80万元。</p>
                </div>
            </div>
        </div>
    </div>
    </div>
    <div class="popup" id="popup_one">
        <div class="popBox">
            <div class="popup_hd clear">
                <h5>等额本金每月还款明细</h5>
                <a class="close_ico" href="javascript:;"></a>
            </div>
            <div class="popup_bd ">
                <table cellpadding="0" cellspacing="0" border="0" class="popup_table">
                    <tbody>
                    <tr>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                    </tr>
                    <tr class="td_bg">
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                    </tr>
                    <tr>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                    </tr>
                    <tr class="td_bg">
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                    </tr>
                    <tr>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                    </tr>
                    <tr class="td_bg">
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                    </tr>
                    <tr>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                        <td>第1期，10000元</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="mask"></div>
    </div>
</form>









<script src="https://static.fangxiaoer.com/js/jsq/common.js"></script>
<script>
    $(function () {
        var inputs = $('.txt[data-type="number"]');

        function setSelectedItem(ul, selectedItem) {
            ul.children().each(function () {
                if ($.trim($(this).text()) == selectedItem) {
                    $(this).children().trigger('click');
                    return false;
                }
            });
        }

        function isTextValid() {
            var valid = true;
            inputs.each(function () {
                var $this = $(this);
                if ($this.attr('id') != 'spouse_money' && $this.attr('id') != 'spouse_ratio' && $this.attr('id') != 'loan_money' &&
                        $this.is(':enabled') && $this.is(':visible') && $.trim($(this).val()) === '') {
                    $this.addClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'visible');
                    valid = false;
                }
            });
            if ($('#fund_ratio').val() !== '' && $('#fund_ratio').val() >= 100) {
                $('#fund_ratio').addClass('error_txt');
                $('.error_msg[data-for="fund_ratio"]').css('visibility', 'visible');
                valid = false;
            }
            if ($('#spouse_money').val() !== '' && isNaN($('#spouse_money').val())) {
                $('#spouse_money').addClass('error_txt');
                $('.error_msg[data-for="spouse_money"]').css('visibility', 'visible');
                valid = false;
            }
            if ($('#spouse_money').val() !== '' && ($.trim($('#spouse_ratio').val()) === '' || $.trim($('#spouse_ratio').val()) >= 100 || isNaN($.trim($('#spouse_ratio').val())))) {
                $('#spouse_ratio').addClass('error_txt');
                $('.error_msg[data-for="spouse_ratio"]').css('visibility', 'visible');
                valid = false;
            }
            return valid;
        }

        //配偶公积金不是必填项
        $('#spouse_money').unbind('focus').unbind('blur');
        $('#spouse_ratio').unbind('focus').unbind('blur')
                .blur(function () {
                    if ($('#spouse_money').val() !== '') {
                        var $this = $(this),
                                value = $this.val();
                        if ($.trim(value) === '' || parseFloat(value) >= 100) {
                            $this.addClass('error_txt');
                            $('.error_msg[data-for="spouse_ratio"]').css('visibility', 'visible');
                        }
                    }
                })
                .focus(function () {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
                });

        $('#fund_ratio').blur(function () {
            var $this = $(this),
                    value = $this.val();
            if ($.trim(value) === '' || parseFloat(value) >= 100) {
                $this.addClass('error_txt');
                $('.error_msg[data-for="fund_ratio"]').css('visibility', 'visible');
            }
        })
                .focus(function () {
                    var $this = $(this);
                    $this.removeClass('error_txt');
                    $('.error_msg[data-for="fund_ratio"]').css('visibility', 'hidden');
                });

        //清空重填
        $('#reset').click(function () {
            $('#other_house, #credit_other').attr('checked', 'checked');
            inputs.val('');
            setSelectedItem($('#load_time'), '30年(360期)');
            $('#fund_ratio, #spouse_ratio').val('12');
            $('[data-type="number"]').each(function () {
                var $this = $(this);
                $this.removeClass('error_txt');
                $('.error_msg[data-for="' + $this.attr('id') + '"]').css('visibility', 'hidden');
            });
        });

        //开始计算
        $('.Start_calculation').click(function () {
            var valid = true;
            if (!isTextValid()) {
                valid = false;
            }

            if (valid) {
                $('#spouse_money').removeClass('error_txt');
                $('.error_msg[data-for="spouse_money"]').css('visibility', 'hidden');
                $('#spouse_ratio').removeClass('error_txt');
                $('.error_msg[data-for="spouse_ratio"]').css('visibility', 'hidden');

                if (!gjjloan1()) return false;

                if ($('.counter_right').css('display') === 'none') {
                    if (!$(this).parents().find(".counter_left").is(":animated")) {
                        $('.counter_left').animate({
                            'margin-left': '0px'
                        }, 500, function () {
                            $('.arrows_ico').show();
                            $('.counter_right').fadeIn();
                            $('.focus_counter').addClass('focus_counter_ed');
                        });
                    };
                };
                //添加统计正确计算结果的代码
                // deleted by sunlong 
                // addRecord(7);
            }
        });

        //计算还款明细
        $('.compute_btn').click(function () {
            var zgdk = $('#max_loan_value').text(); //最高贷款额度
            var dked = Math.round($('#loan_money').val() * 10000) / 10000; //所需贷款额度

            if ($('#loan_money').val() == '' || $('#loan_money').val() == 0 || isNaN($('#loan_money').val()) || dked > zgdk) {
                $('#loan_money').addClass('error_txt')
                $('.error_msg[data-for="loan_money"]').css('visibility', 'visible');
            } else {
                $('#loan_money').removeClass('error_txt')
                $('.error_msg[data-for="loan_money"]').css('visibility', 'hidden');

                gjjloan2();
                $('#compute_result').fadeIn();
            }
        })

    });

    var lilv6_30 = 0.0450;
    var lilv1_5 = 0.0400;

    function gjjloan1() {
        var gryjce; //住房公积金个人月缴存额
        var poyjce; //配偶住房公积金个人月缴存额
        var grjcbl; //缴存比例
        var pojcbl; //配偶缴存比例
        var xy; //个人信用
        var fwzj; //房屋总价
        var fwxz; //房屋性质
        var dknx; //贷款申请年限

        var jtysr; //家庭月收入
        var r; //月还款
        var gjjdka; //住房公积金贷款额度a
        var gjjdkb; //住房公积金贷款额度b
        var gjjdkc; //住房公积金贷款额度c

        gryjce = $('#fund_money').val();
        poyjce = $('#spouse_money').val();
        grjcbl = $('#fund_ratio').val() / 100;
        pojcbl = $('#spouse_ratio').val() / 100;

        if ($('#policy_house').attr('checked') == 'checked') fwxz = 0.9;
        if ($('#other_house').attr('checked') == 'checked') fwxz = 0.8;

        if ($('#credit_aaa').attr('checked') == 'checked') xy = 1.3;
        if ($('#credit_aa').attr('checked') == 'checked') xy = 1.15;
        if ($('#credit_other').attr('checked') == 'checked') xy = 1;

        fwzj = $('#house_price').val() * 10000;
        dknx = parseInt($('#loan_time_select').text());

        var bcv = 0;
        if (dknx > 5) {
            bcv = Math.round(1000000 * lilv6_30 / 12) / 1000000;
        } else {
            bcv = Math.round(1000000 * lilv1_5 / 12) / 1000000;
        }
        r = Math.round((10000 * bcv * Math.pow(1 + bcv, 12 * dknx)) / (Math.pow(1 + bcv, 12 * dknx) - 1), 2);

        if (poyjce.length > 0) {
            jtysr = Math.ceil((gryjce / grjcbl + poyjce / pojcbl) * 10) / 10;
        } else {
            jtysr = Math.ceil((gryjce / grjcbl) * 10) / 10;
        }

        if (jtysr <= 400) {
            alert('家庭月收入低于400，不符合贷款条件');
            return false;
        }

        gjjdka = Math.min(Math.round((jtysr - 400) / r * 10000 * 10) / 10, 800000);
        gjjdkb = Math.round(gjjdka * xy * 10) / 10;
        gjjdkc = Math.round(fwzj * fwxz * 10) / 10;
        $('#max_loan_value').text(Math.floor(Math.min(gjjdkb, gjjdkc) / 10000 * 10) / 10); //最高贷款额度

        return true;

        //do_record(obj);
    }


    function gjjloan2(obj) {
        var dknx; //贷款申请年限
        var syhk; //首月还款额
        var dked; //需要贷款额度
        var bxhj; //本息合计
        var rb;

        //计算2
        dknx = parseInt($('#loan_time_select').text());
        dked = Math.round($('#loan_money').val() * 10000) / 10000; //所需贷款额度
        $('#loan_money').val(dked); //取整后再赋值

        //等额本息
        var ylv_new; //月利率
        if (dknx >= 1 && dknx <= 5)
            ylv_new = lilv1_5 / 12;
        else
            ylv_new = lilv6_30 / 12;

        var ncm = parseFloat(ylv_new) + 1; //n次幂
        var dknx_new = dknx * 12; //贷款月数
        total_ncm = Math.pow(ncm, dknx_new)

        $('#average_debx').text(Math.round(((dked * 10000 * ylv_new * total_ncm) / (total_ncm - 1)) * 100) / 100); //月均还款额
        var pp = Math.round(((dked * 10000 * ylv_new * total_ncm) / (total_ncm - 1)) * 100) / 100;
        bxhj = Math.round(pp * dknx * 12 * 100) / 100;

        $('#total_money_debx').text(Math.round(bxhj * 100) / 100); //还款总额
        $('#interest_debx').text(Math.round(bxhj - dked * 10000));

        //等额本金
        if (dknx > 5) {
            rb = lilv6_30 * 100;
        } else {
            rb = lilv1_5 * 100;
        }

        syhk = Math.round((dked * 10000 / (dknx * 12) + dked * 10000 * rb / (100 * 12)) * 100) / 100; //首月还款=（所需贷款数*10000/（贷款年限*12）+所需贷款数*利率/（100*12）*100）/100   即：平均每月还款本金数+第一个月的利息
        $('#average_debj').text(syhk);
        var yhke; //月还款额
        var bxhj; //本息合计
        var dkys; //贷款月数
        var sydkze; //当前剩余贷款总额
        var yhkbj; //月还款本金
        dkys = dknx * 12; //贷款月数
        yhkbj = dked * 10000 / dkys; //月还款本金

        yhke = syhk;
        sydkze = dked * 10000 - yhkbj;
        bxhj = syhk;
        for (var count = 2; count <= dkys; ++count) {
            yhke = dked * 10000 / dkys + sydkze * rb / 1200; //月还款额= 月还款本金+剩余贷款月利息
            sydkze -= yhkbj;
            bxhj += yhke;
        }

        var all_total2 = 0;
        var month_money2 = "<tr>";
        var mingxi = '<table cellpadding="0" cellspacing="0" border="0" class="popup_table" ><tbody>';
        for (j = 0; j < dkys; j++) {
            //调用函数计算: 本金月还款额
            huankuan = getMonthMoney2(rb / 100, dked * 10000, dkys, j);
            all_total2 += huankuan;
            huankuan = Math.round(huankuan * 100) / 100;
            month_money2 += '<td>第' + (j + 1) + '期<br>' + huankuan + '元</td>';
            if ((j + 1) % 4 == 0) {
                if (j == dkys - 1) month_money2 += '</tr>';
                else month_money2 += '</tr><tr>';
            }
        }
        mingxi += month_money2 + '</tbody></table>';

        $('#popup_one').children().children('.popup_bd').html(mingxi);
        $('#total_money_debj').text(Math.round(bxhj * 100) / 100);
        $('#interest_debj').text(Math.round(bxhj - dked * 10000));
    }

    function getMonthMoney2(lilv, total, month, cur_month) {
        var lilv_month = lilv / 12; //月利率
        var benjin_money = total / month;
        return (total - benjin_money * cur_month) * lilv_month + benjin_money;

    }
</script>
<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>

</html>
