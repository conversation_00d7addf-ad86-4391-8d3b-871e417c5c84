<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳房产网,沈阳房产,沈阳房地产信息服务平台 - 房小二网</title>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/default/404.css" rel="stylesheet" type="text/css" />
</head>
<body class="w1210">
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=1,subNavIndex=6"></div>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/event_data.js"></script>
    <div class="nav1">
        <div class="left1">
            <img src="https://static.fangxiaoer.com/web/images/sy/404/renwu.jpg"  />

        </div>
        <div class="right1">
            <p class="top1">
                您本次访问需要认证<br>
                请填写验证码继续浏览<br>
            </p>
            <div class="control-group">
                <label class="control-label" >
                    验证码：</label>
                <div class="controls">
                    <input name="imgCode" maxlength="4" id="imgCode" type="text" class="inputStyle widthMd fxe_CodeImg" onfocus="clearjiayan();"/>
                    <img id="CodeImg" width="90" height="48" style="vertical-align: middle; cursor: pointer" onclick="setSrc()" src="" />

                    <span id="imgCode_info" class="verify_tip fxe_jiaoyan_error"><i></i></span>
                </div>
                <div class="color_999 retrP" style="margin-left: 127px">
                    请填写图片中的字符，不区分大小写
                </div>
            </div>
        </div>

        <div class="cl"></div>
    </div>

    <div class="cl"></div>
    <div class="footer" th:include="fragment/fragment::footer_detail"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::commonFloat"></div>
<script type="application/javascript">
    jQuery(document).ready(function () {
        setSrc();
    })
    $(function(){

        $('#imgCode').bind('input propertychange', function() {
            if($(this).val().length >= 4) {
                if(TPyzmFS()) {
                    window.location.reload();
                };
            }
        });

    })
    var verify_t;
    function setSrc() {
        $.ajax({
            url:'/getSysTime',
            type:'post',
            success:function(data) {
                if (data.status == 1) {
                    verify_t = data.content;
                    $("#CodeImg").attr("src","https://ltapi.fangxiaoer.com/apiv1/other/verifyImage?t="+verify_t);
                }
            }
        });
        //$("#CodeImg").attr("src","https://ltapi.fangxiaoer.com/apiv1/other/verifyImage?t="+verify_t);
    }

    //查图片验证码是否正确
    function TPyzmFS() {
        var code = $(".fxe_CodeImg").val();
        var x = false;
        if(code!=undefined && code.trim()!=''){
            $.ajax({
                type: "POST",
                data: { verifyCode: code, verifySize : 4, t : verify_t },
                url: "/verifyImageCode",
                async: false,
                success: function (data) {
                    if (data.status == 1) {
                        $(".fxe_jiaoyan_error").html("");
                        x = true;
                    } else {
                        $(".fxe_jiaoyan_error").html(data.msg);//hint[6]
                        setSrc();
                        x = false;
                    }
                }
            });
        }else{
            $(".fxe_jiaoyan_error").html(hint[6]);
        }

        return x;
    }
    function clearjiayan() {
        $(".fxe_jiaoyan_error").html("&nbsp;");
    }
</script>
</body>
</html>
