<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳房产网,沈阳房产,沈阳房地产信息服务平台 - 房小二网</title>
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/default/404.css?v=20191115" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
</head>

<body class="w1210">
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=0,subNavIndex=6"></div>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/event_data.js"></script>
    <div class="nav1">
        <div class="left1">
            <img src="https://static.fangxiaoer.com/web/images/sy/404/renwu.jpg"  />

        </div>
        <div class="right1">
            <p class="top1" th:text="${msg}">
            </p>
            <p class="bottom1">
                <i>3</i>秒后为您跳转到<span></span>
                <!--<a href="javascript:history.back()" class="pre">返回上一页面 </a>-->
                <!--<a  href="/" class="first">返回网站首页 </a>-->
            <div class="cl"></div>
            </p>
        </div>
        <div class="cl"></div>
    </div>

    <div class="cl"></div>
    <div class="footer" th:include="fragment/fragment::footer_detail"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::commonFloat"></div>
    <script>
        $(function () {
            var lianjie = window.location.href;
            var yy = 3;
            console.log(lianjie)
            var  pos = lianjie.search("house") != -1;//新房列表页
            var  pos1 = lianjie.search("salehouse") != -1;//二手房列表页
            var  pos2 = lianjie.search("rent") != -1;//租房列表页
            var  pos3 = lianjie.search("shop") != -1;//商铺列表页
            var  pos4 = lianjie.search("scriptorium") != -1;//写字楼列表页
            function after() {
                $(".bottom1 i").empty().append(yy);
                yy = yy - 1;
                setTimeout(function() {
                    after();
                }, 1000);
            }
            if (pos1 == true){
                $(".bottom1 span").html("二手房列表页")
                after();
                $(".bottom1 span").click(function() {
                    window.location.href = "https://sy.fangxiaoer.com/saleHouses/";
                });
                setTimeout(function() {
                    window.location.href = "https://sy.fangxiaoer.com/saleHouses/";
                }, 3000);
            }else if (pos == true){
                $(".bottom1 span").html("新房列表页")
                after();
                $(".bottom1 span").click(function() {
                    window.location.href = "https://sy.fangxiaoer.com/houses/";
                });
                setTimeout(function() {
                    window.location.href = "https://sy.fangxiaoer.com/houses/";
                }, 3000);
            }else if (pos2 == true){
                $(".bottom1 span").html("租房列表页")
                after();
                $(".bottom1 span").click(function() {
                    window.location.href = "https://sy.fangxiaoer.com/rents/";
                });
                setTimeout(function() {
                    window.location.href = "https://sy.fangxiaoer.com/rents/";
                }, 3000);
            }else if (pos3 == true){
                $(".bottom1 span").html("商铺列表页")
                after();
                $(".bottom1 span").click(function() {
                    window.location.href = "https://sy.fangxiaoer.com/shops/";
                });
                setTimeout(function() {
                    window.location.href = "https://sy.fangxiaoer.com/shops/";
                }, 3000);
            }else if (pos4 == true){
                $(".bottom1 span").html("写字楼列表页")
                after();
                $(".bottom1 span").click(function() {
                    window.location.href = "https://sy.fangxiaoer.com/scriptoriums/";
                });
                setTimeout(function() {
                    window.location.href = "https://sy.fangxiaoer.com/scriptoriums/";
                }, 3000);
            }else {
                $(".bottom1 span").html("网站首页")
                after();
                $(".bottom1 span").click(function() {
                    window.location.href = "https://sy.fangxiaoer.com/";
                });
                setTimeout(function() {
                    window.location.href = "https://sy.fangxiaoer.com/";
                }, 3000);
            }
        })
    </script>

</body>
</html>
