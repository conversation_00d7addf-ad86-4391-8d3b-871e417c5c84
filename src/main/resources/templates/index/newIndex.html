<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"
      xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="utf-8" />
    <title>沈阳房产网,沈阳房产,沈阳房地产信息服务平台 - 房小二网</title>
    <meta name="keywords" content="沈阳房产网,沈阳二手房,沈阳房价,沈阳房产,沈阳租房" />
    <link rel="icon" href="/favicon.ico" type="image/x-icon"/>
    <meta property="wb:webmaster" content="baed630db1459dfa" />
    <meta name="description"
          content="房小二网是沈阳房产专业门户网站,为您提供沈阳房价,沈阳租房,沈阳二手房等及时信息,定期举办沈阳房交会,同时发布沈阳房产资讯和最新学区、贷款等房产政策解读。" />
    <meta name="msvalidate.01" content="D2084F64E7258D71B598234CA0A678C4" />
    <meta name="360-site-verification" content="6183dc015b1763ea6dbaeac1360364a9" />
    <meta name="sogou_site_verification" content="ABjAlhG4A2"/>
    <meta name="baidu-site-verification" content="613vHEKYFA" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <!--导航样式-->
    <link rel="stylesheet" type="text/css" href="/css/newIndex2020.css?v=20240819" />
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <link href="https://static.fangxiaoer.com/web/styles/swiper.min.css" rel="stylesheet" type="text/css">
    <script src="https://static.fangxiaoer.com/js/swiper/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/bootstrap.min.js" type="text/javascript"> </script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.SuperSlide.2.1.1.js"></script>
    <!--搜索-->
    <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/scroll.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/searchIndex2.js?v=20201030" type="text/javascript" charset="utf-8"></script>
    <!--    这个文件 用于楼盘对比搜索-->
    <script src="/js/house/contrastHouse.js?v=20240510" type="text/javascript"></script>
    <script>
        var t1 ;
        function setTabHouseDelayout(){
            clearTimeout(t1);
        }
        function setTabHouseDelay (a,b,c){
            t1 = setTimeout("setTabHouse("+ a +","+ c +","+ b +")",120);//延迟速度
        }
    </script>
    <!--[if IE 8]>
    <script src="https://static.fangxiaoer.com/js/ie8_bnzf_yz.js" type="text/javascript" charset="utf-8"></script>
    <![endif]-->
    <script th:inline="javascript">
        var sessionId = [[${session.muser}]];
        var ad = [[${ad}]];
        console.log('ad数据', ad)
    </script>
    <style>
        .d-video{
            width: 100%;
            min-width: 1170px;

            height: auto;
        }
</style>
    <link href="https://static.fangxiaoer.com/sy/ai/aiStyle.css" rel="stylesheet" type="text/css">
</head>
<body style="position: relative;">
<!--首页导航-->
<div class="headTOP" th:include="fragment/fragment::firstNav"  th:with="firstNavIndex=1,indexTab=1"></div>
<div>
    <style th:if="${theme ne null and theme eq '1'}">
        html{
            filter: grayscale(100%);
            -webkit-filter: grayscale(100%);
        }
    </style>
    <div th:style="${#lists.isEmpty(backGround)?'display:none':''}" class="bg-bannerK">
        <a th:if="${!#lists.isEmpty(backGround)}" onload="checkCookie()" class="bg-banner"  th:href="${backGround.get(0).url}" target="_blank"
           th:style="'background:url('+${#lists.isEmpty(backGround)?'':backGround.get(0).image}+')  no-repeat center top #fff'">
        </a>
  </div>
    <div class="w1170" th:style="${#lists.isEmpty(backGround)?'margin-top:55px;':'margin-top: 95px;'}">
        <span  class="close_fastival" ne-click="closeFestivel();" th:if="${!#lists.isEmpty(backGround)}"></span>

<!--图片轮播-->
        <div class="carousel-inner" th:if="${!#lists.isEmpty(ad) and #lists.toList(ad).size() ne 1}"  id="newWheel">
            <div class="bannerLoop">
                <div class="swiper-container bannerLoop1">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide" th:if="${!#lists.isEmpty(ad)}" th:each="getad:${ad}">
                            <a th:href="${getad.url}" target="_blank" class="">
                                <!--视频-->
                                <th:block th:if="${getad.huaState eq '0'}">
                                    <video class="d-video" muted  autoplay loop name="media" type="video/mp4"
                                           th:poster="${getad.image}" th:src="${'https://ossvideos1.fangxiaoer.com/' + getad.videoUrl}">
                                    </video>
                                    <span class="uuipezy" th:text="${getad.introduce}"></span>
                                </th:block>
                                <!--图片-->
                                <th:block th:if="${getad.huaState eq '1'}">
                                    <img th:src="${getad.image}" alt="">
                                    <span th:text="${getad.introduce}"></span>
                                </th:block>

                            </a>
                        </div>
                    </div>
                </div>
                <div class="swiper-pagination swiper-pagination-white"></div>
                <div class="banner_bottom"></div>
            </div>
            <div class="new_banner_gif">
                <div class="prev"><a  href="javascript:void(0)" ></a></div>
                <div class="next"><a href="javascript:void(0)" ></a></div>
            </div>
        </div>

        <!-- 搜索框和四个快速入口 -->
        <div class="quick-links-container">
            <div class="search-overlay">
                <div class="search-box">
                    <input type="text" placeholder="请输入楼盘名称、开发商名称开始找房" class="search-input" />
                    <button class="search-button">搜索</button>
                </div>
            </div>
            <div class="quick-links">
                <a href="/houses" class="quick-link">我要买新房</a>
                <a href="/saleHouses" class="quick-link">我要买二手房</a>
                <a href="/saleHouses/sell" class="quick-link">我要卖二手房</a>
                <a href="/houseKeeper" class="quick-link">小二管家代办服务</a>
            </div>
        </div>
        <th:block th:if="${!#lists.isEmpty(ad) and #lists.toList(ad).size() ne 1}">
            <script>
                $(document).ready(function(){
                    var getAdSwiperTime = ''
                    $.ajax({
                        type: "POST",
                        url: "https://ltapi.fangxiaoer.com/apiv1/other/getAdSwiperInterval",
                        dataType: "json",
                        async: false,
                        success: function (data) {
                            if(data.status == 1 && data.content > 0){
                                getAdSwiperTime = (data.content)*1000
                            }else{
                                getAdSwiperTime= '5000'
                            }
                        },
                        error:function (){
                            getAdSwiperTime= '5000'
                        }
                    })
                    var swiper = new Swiper('.bannerLoop1', {
                        pagination: '.swiper-pagination',
                        paginationClickable: '.swiper-pagination',
                        nextButton: '.next',
                        prevButton: '.prev',
                        spaceBetween: 30,
                        effect: 'fade',
                        centeredSlides: true,
                        autoplay: getAdSwiperTime,
                        autoplayDisableOnInteraction: false,
                        loop: true
                    });
                    $(document).ready(function() {
                        $("#spxg1").addClass("hover")
                        /*鼠标移入停止轮播，鼠标离开 继续轮播*/
                        var comtainer = document.getElementById('newWheel');
                        comtainer.onmouseenter = function () {
                            // console.log('鼠标悬浮')
                            swiper.stopAutoplay();
                        };
                        comtainer.onmouseleave = function () {
                            // console.log('鼠标离开')
                            swiper.startAutoplay();
                        }

                        //此方法为模拟的，hover到分页器的小圆点后自动触发其本身的click事件
                        $(".bannerLoop .swiper-pagination-bullet").each(function () {
                            $(this).hover(function() {
                                $(this).click(); //鼠标划上去之后，自动触发点击事件来模仿鼠标划上去的事件
                            });
                        })
                    });

                })


            </script>
        </th:block>
        <!--黄金眼只有一个广告的时候(视频或图片)-->
        <div class="carousel-inner bannerLoop" th:unless="${!#lists.isEmpty(ad) and #lists.toList(ad).size() ne 1}">
            <a th:each="getad:${ad}" th:href="${getad.url}" target="_blank">
                <!--只有一个视频的时候-->
                <th:block th:if="${getad.huaState eq '0'}">
                    <video class="d-video" muted  autoplay loop name="media" type="video/mp4"
                           th:poster="${getad.image}" th:src="${'https://ossvideos1.fangxiaoer.com/' + getad.videoUrl}">
                    </video>
                    <span class="uuipezy" th:text="${getad.introduce}"></span>
                </th:block>
                <!--只有一张图片时候-->
                <th:block th:if="${getad.huaState eq '1'}">
                    <img th:src="${getad.image}" alt="">
                    <span class="uuipezy" th:text="${getad.introduce}"></span>
                </th:block>
            </a>
        </div>

        <div id="app">
            <!--搜索-->
            <div class="search" id="qdelay2">
                <ul>
                    <li id="search1" onmouseover="setTab('search',1,6)" class="hover"><a th:href="@{/houses/}" target="_blank">买新房</a></li>
                    <li id="search2" onmouseover="setTab('search',2,6)" class=""><a th:href="@{/saleHouses}" target="_blank">买二手房</a></li>
                    <li id="search3" onmouseover="setTab('search',3,6)" class=""><a th:href="@{/rents/}" target="_blank">找租房</a></li>
                    <li id="search5" onmouseover="setTab('search',5,6)" class=""><a th:href="@{/shops}" target="_blank">商铺</a></li>
                    <li id="search6" onmouseover="setTab('search',6,6)" class=""><a th:href="@{/scriptoriums}" target="_blank">写字楼</a></li>
                    <li id="search4" onmouseover="setTab('search',4,6)" class=""><a th:href="@{/news}" target="_blank">资讯</a></li>
                </ul>
                <div class="cl"></div>
                <div style="margin: 0 auto" id="inputList">
                    <div th:id="${'con_search_'+item}" th:class="${item eq '1'?'search_div':'search_div hid'}"  th:each="item:${#strings.setSplit('1,2,3,5,6,4',',') }" >
                        <i></i>
                        <input th:if="${item eq '1'}" type="text" th:class="${'index_search_input search_btn'+item}" th:dir="${item}" placeholder="请输入楼盘名称、开发商名称开始找房">
                        <input th:if="${item eq '2'or item eq '3' }" type="text" th:class="${'index_search_input search_btn'+item}" th:dir="${item}" placeholder="请输入区域、商圈或小区名开始找房">
                        <input th:if="${item eq '5'}" type="text" th:class="${'index_search_input search_btn'+item}" th:dir="${item}" placeholder="请输入区域、板块开始找房">
                        <input th:if="${item eq '6'}" type="text" th:class="${'index_search_input search_btn'+item}" th:dir="${item}" placeholder="请输入区域、板块或写字楼名称找房">
                        <input th:if="${item eq '4'}" type="text" th:class="${'index_search_input search_btn'+item}" th:dir="${item}" placeholder="请输入您感兴趣的内容进行搜索，比如楼盘名字">
                        <input type="button" class="index_search_btn" value="">
                        <div @click="toggleChat" class="houseSelection">AI选房</div>
                        <div class="search_map">
                            <p></p>
                            <a th:if="${item eq '1' or item eq '4'}" th:href="@{/static/houseMap.htm}" target="_blank">地图找新房</a>
                            <a th:if="${item eq '2' }" th:href="@{/salemap}" target="_blank">地图找新房</a>
                            <a th:if="${item eq '3'}" th:href="@{/static/rentmap.htm}" target="_blank">地图找新房</a>
                        </div>
                    </div>
                </div>
                <input type="hidden" id="searchType">
            </div>

            <!-- Floating Chat Box -->
            <div class="chatShow">
                <div v-show="isChatOpen" :class="{ 'chat-container': true, 'closing': isClosing }">
                    <div class="chat-header">
                        <span class="chat-title">房小二AI选房</span>
                        <button @click="toggleChat" class="close-btn">X</button>
                    </div>
                    <div class="chat-body" ref="chatBody">
                        <div class="chat-nologin" v-if="!loginStatus">
                            <div class="nologin-top">
                                <img src="/images/guo/ai.png">
                                <p>Ai选房已全面接入</p>
                                <p>DeepSeek R1满血版，快来试试吧~!</p>
                            </div>
                            <div class="nologin-form">
                                <input type="number" placeholder="请输入和手机号" v-model="usePhone" oninput="limitInputLength(this, 11)"/>
                                <span class="getcode c1"  @click="getcodeFn" >{{codeT}}</span>
                                <span class="getcode c2"  id="mcode" style="cursor: unset;">{{codeT}}</span>
                                <input type="number" placeholder="请输入验证码" v-model="useCode" oninput="limitInputLength(this, 6)"/>
                                <div class="nologin-btn" @click="subMit">登录</div>
                                <div class="nologin-info">还没有账户？<a href="https://my.fangxiaoer.com/register" target="_blank">创建账户</a></div>
                                <div class="nologin-foot">
                                    <img src="/images/guo/shield.png">
                                    承诺数据不外泄，无垃圾信息
                                    继续即表示您同意<a href="https://info.fangxiaoer.com/About/policy">隐私政策</a>和<a href="https://info.fangxiaoer.com/About/protocol">使用条件</a>
                                </div>
                            </div>
                        </div>
                        <div v-if="loginStatus">
                            <div class="presetProblem">
                                <div class="chat-per">
                                    <p>Hi，我是房小二AI选房～</p>
                                    <p>很高兴遇见你！你可以随时问我关于沈阳市新房、</p>
                                    <p>二手房的相关信息。请问您想咨询什么问题？</p>
                                </div>
                                <div class="default_Problem">
                                    <p @click="sendMessage('沈阳新房买哪里')">沈阳新房买哪里-点击咨询</p>
                                    <p @click="sendMessage('沈阳二手房买哪里')">沈阳二手房买哪里-点击咨询</p>
                                    <p @click="sendMessage('沈阳各区房价排名')">沈阳各区房价排名-点击咨询</p>
                                </div>
                            </div>
                            <div v-for="(message, index) in messages" :key="index" class="chat-message">
                                <div class="user-message" v-if="message.isUser" v-text="message.content"></div>
                                <div class="ai-message" v-else v-html="convertMarkdown(message.content)"></div>
                            </div>
                            <div v-if="isLoading" class="loading">Loading...</div>
                        </div>
                    </div>
                    <div class="chat-footer" v-if="loginStatus">
                        <textarea v-model="userInput" @keydown.enter="sendMessage()" placeholder="请输入你想问的问题" rows="4"></textarea>
                        <button @click="sendMessage()" class="send-btn">发送</button>
                    </div>
                    <div class="popp" v-if="poppStatus"><p>{{poppText}}</p></div>
                </div>
            </div>

        </div>

        <!--广告切换 -->
        <script>
            jQuery(".carousel-inner").slide({ titCell: ".fxe_banner ul", mainCell: ".bannerLoop ul", autoPage: true, effect: "fade", autoPlay: true, scroll: 1, vis: 1,interTime:5000 });
        </script>
        <!--分类-->
        <div class="navigation">
            <div class="main">
                <div style="width:1200px">
                    <div class="box" style="width: 196px;">
                        <h2 style="margin-left: 0;text-align:left;width:60px;"><a th:href="@{/houses/}" target="_blank">新房</a></h2>
                        <ul style="padding-left: 45px">
                            <a th:href="@{/videos/}" target="_blank">
                                <li style="margin-right: 13px">房产视频</li>
                            </a>
                            <a th:href="@{/subways/k1}" target="_blank">
                                <li style="margin-right: 13px">地铁找房</li>
                            </a>
                        </ul>
                        <ul>
                            <a th:href="@{/static/houseMap.htm}" target="_blank">
                                <li  style="margin-right: 13px">地图找房</li>
                            </a>
                            <!--<a th:href="@{/liveList/}" target="_blank">
                                <li style="color: #ff5200;margin-right: 0">直播看房<i class="liveHuo"></i></li>
                            </a>-->
                        </ul>
                        <div class="line"></div>
                    </div>
                    <div class="box" style="width: 206px">
                        <h2 style="  padding-right: 9px"><a th:href="@{/saleHouses/}" target="_blank">二手房</a></h2>
                        <ul style="margin-left: 60px">
                            <a th:href="@{/saleHouses/z1}" target="_blank">
                                <li style="margin-right: 14px;">地铁</li>
                            </a>
                            <a th:href="@{/helpSearch?ids=2}" target="_blank">
                                <li style="margin-right: 14px;">我要买房</li>
                            </a>
                        </ul>
                        <ul>
                            <!--<a th:href="@{https://sy.fangxiaoer.com/schools/}" target="_blank">
                                <li>学区找房</li>
                            </a>-->
                            <a th:href="@{https://sy.fangxiaoer.com/saleVillages/}" target="_blank">
                                <li>小区找房</li>
                            </a>

                            <a th:href="@{/static/saleHouse/saleHouse.htm}" target="_blank">
                                <li  style="color: #ff5200">我要卖房</li>
                            </a>
                        </ul>
                        <div class="line"></div>
                    </div>
                    <div class="box" style="width: 184px">
                        <h2 style=" padding-right: 10px"><a th:href="@{/rents/}" target="_blank">租房</a></h2>
                        <ul style="margin-left: 49px">
                            <a th:href="@{/rents/k1}" target="_blank">
                                <li>地铁</li>
                            </a>
                            <a th:href="@{'/helpSearch?ids=3'}" target="_blank">
                                <li  style="color: #ff5200">求租</li>
                            </a>
                        </ul>
                        <ul>
                            <a  th:href="@{/rents/w1}" target="_blank">
                                <li>整租</li>
                            </a>
                            <a th:href="@{/rentwhole/}" target="_blank">
                                <li>我要出租</li>
                            </a>
                        </ul>
                        <div class="line"></div>
                    </div>
                    <div class="box" style=" width: 195px;" >
                        <h2 style=" padding-right: 10px"><a th:href="@{/shops/}" target="_blank">商铺</a></h2>
                        <ul style="margin-left: 52px">
                            <a th:href="@{/shops/b1}"  target="_blank">
                                <li>出售</li>
                            </a>
                            <a th:href="@{/houses/pt3}" target="_blank">
                                <li>新盘</li>
                            </a>
                        </ul>
                        <ul>
                            <a th:href="@{/shops/b2}" target="_blank">
                                <li>出租</li>
                            </a>
                            <a th:href="@{/shopsell}" target="_blank" style="color: #ff5200">
                                <li>我要出售</li>
                            </a>
                        </ul>
                        <div class="line"></div>
                    </div>
                    <div class="box" style=" width: 202px;" >
                        <h2 style=" padding-right: 10px"><a th:href="@{/scriptoriums/}" target="_blank">写字楼</a></h2>
                            <ul style="margin-left: 37px">
                            <a th:href="@{/scriptoriums/}"  target="_blank">
                                <li>房源</li>
                            </a>
                            <a th:href="@{/scriptoriums/b4}" target="_blank" style="color: #ff5200">
                                <li>出租</li>
                            </a>
                        </ul>
                        <ul>
                            <a th:href="@{/scriptoriums/b5}" target="_blank">
                                <li>出售</li>
                            </a>
                            <a th:href="@{/officeSell}" target="_blank">
                                <li>我要出售</li>
                            </a>
                        </ul>
                        <div class="line"></div>
                    </div>
                    <div class="box" style="width: 202px;border: none;">
                        <h2 style="line-height: 26px;text-align:left; padding-right: 10px"><a href="javascript:void(0)">服务</a></h2>
                        <ul style="margin-left: 37px">
                            <a href="/fastSeek" target="_blank" style="color: #ff5200"><li>房产快搜</li></a>

                        </ul>
                        <ul>
<!--                            <a th:href="@{/housingprice/}" target="_blank"><li>房价评估</li></a>-->
                            <a href="https://event.fangxiaoer.com/20240813.htm" target="_blank"><li>加盟申请</li></a>
<!--                            <a th:href="@{/seekOrder/1/}" target="_blank" style="color: #ff5200;cursor: pointer;"><li>求租求购</li></a>-->
                        </ul>
                        <div class="line"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="cl"></div>
<!--楼盘排行榜-->
        <div class="index_block ranking-section">
            <div class="index_title">
                <h1 class="index_h1 zf_h1"><a th:href="@{/projectRank/1}" target="_blank">
                    楼盘排行榜</a></h1>
                <ul class="pull-left">
                    <li id="phb1" th:class="${rankNum eq 0?'hover':''}" onmouseenter="setTabHouseDelay('\'phb\'', 3, 1)" onmouseout="setTabHouseDelayout()">
                        <span>
                            <a  target="_blank">小二精选</a>
                        </span>
                    </li>
                    <!--<li id="phb2" th:class="${rankNum eq 1?'hover':''}" onmouseenter="setTabHouseDelay('\'phb\'', 3, 2)" onmouseout="setTabHouseDelayout()">
                        <span>
                            <a  target="_blank">热搜榜</a>
                        </span>
                    </li>-->
                    <li id="phb3" th:class="${rankNum ne 0 ?'hover':''}" onmouseenter="setTabHouseDelay('\'phb\'', 3, 3)" onmouseout="setTabHouseDelayout()">
                        <span>
                            <a  target="_blank">热门新盘</a>
                        </span>
                    </li>
              </ul>
                <a th:href="@{/projectRank/1}" target="_blank" class="pull-right index_title_right">
                    <span class="index_icon wymf_icon geng_d"></span>
                    更多
                </a>
            </div>
            <div class="listBuilding">
                <ul th:if="${!#lists.isEmpty(rb.ranks.rank1)}" id="con_phb_1" th:style="${rankNum eq 0?'':'display:none;'}">
                    <li th:each="one,i:${rb.ranks.rank1}" th:if="${i.count lt 5}" th:class="${i.count == 1 ? 'liFirst': 'listR'}">
                        <a th:href="${'/house/' + one.projectId + '-' + one.projectType + '.htm'}" target="_blank">
                        <img class="listN" th:if="${i.count lt 4}" th:src="${'https://static.fangxiaoer.com/web/images/img/list'+i.count+'.png'}" alt="">
                        <div class="liFposi">
                            <img class="listB" th:src="${one.ImageUrl}" alt="">
                            <div th:class="${i.count == 1 ? 'liFbg':'liFbg liFbg2'}">
                                <h2>
                                    推荐理由
                                </h2>
                                <img src="https://static.fangxiaoer.com/web/images/img/hengG.png" alt="">
                                <p th:text="${one.rankDesc}"></p>
                            </div>
                            <div th:class="${i.count == 1 ?'liFbt' :'liFbt liFbt2'}">
                                <h2 th:text="${one.projectName}"></h2>
                                <span class="daiding" th:utext="${one.mPrice eq null ? '<i>待定</i>' :'<i>'+one.mPrice.priceMoney+'</i>元/㎡'}"></span>
                            </div>
                            <div th:class="${i.count == 1 ?'vr_ls' :'vr_ls vrb0'}" th:if="${one.pan ne null}"></div>
                        </div>
                        </a>
                    </li>
                </ul>
               <!-- <ul th:if="${!#lists.isEmpty(rb.ranks.rank2)}" id="con_phb_2" th:style="${rankNum eq 1?'':'display:none;'}">
                    <li th:each="one,i:${rb.ranks.rank2}" th:if="${i.count lt 5}" th:class="${i.count == 1 ? 'liFirst': 'listR'}">
                        <a th:href="${'/house/' + one.projectId + '-' + one.projectType + '.htm'}" target="_blank">
                            <img class="listN" th:if="${i.count lt 4}" th:src="${'https://static.fangxiaoer.com/web/images/img/list'+i.count+'.png'}" alt="">
                            <div class="liFposi liFposi2">
                                <img class="listB listB2" th:src="${one.ImageUrl}" alt="">
                            </div>
                            <div th:class="${i.count == 1 ?'liFbt' :'liFbt liFbt2'}">
                                <h2 th:text="${one.projectName}"></h2>
                                <span class="daiding" th:utext="${one.mPrice eq null ? '<i>待定</i>' :'<i>'+one.mPrice.priceMoney+'</i>元/㎡'}"></span>
                            </div>
                            <div th:class="${i.count == 1 ?'vr_ls' :'vr_ls vrb0'}" th:if="${one.pan ne null}"></div>
                        </a>
                    </li>
                </ul>-->
                <ul th:if="${!#lists.isEmpty(rb.ranks.rank4)}" id="con_phb_3" th:style="${rankNum ne 0?'':'display:none;'}">
                    <li th:each="one,i:${rb.ranks.rank4}" th:if="${i.count lt 5}" th:class="${i.count == 1 ? 'liFirst': 'listR'}">
                        <a th:href="${'/house/' + one.projectId + '-' + one.projectType + '.htm'}" target="_blank">
                            <img class="listN" th:if="${i.count lt 4}" th:src="${'https://static.fangxiaoer.com/web/images/img/list'+i.count+'.png'}" alt="">
                            <div class="liFposi">
                                <img class="listB" th:src="${one.ImageUrl}" alt="">
                                <div th:class="${i.count == 1 ? 'liFbg':'liFbg liFbg2'}">
                                    <h2>
                                        推荐理由
                                    </h2>
                                    <img src="https://static.fangxiaoer.com/web/images/img/hengG.png" alt="">
                                    <p th:text="${one.rankDesc}"></p>
                                </div>
                                <div th:class="${i.count == 1 ?'liFbt' :'liFbt liFbt2'}">
                                    <h2 th:text="${one.projectName}"></h2>
                                    <span class="daiding" th:utext="${one.mPrice eq null ? '<i>待定</i>' :'<i>'+one.mPrice.priceMoney+'</i>元/㎡'}"></span>
                                </div>
                                <div th:class="${i.count == 1 ?'vr_ls' :'vr_ls vrb0'}" th:if="${one.pan ne null}"></div>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>

        </div>

        <!--房产视频 2022年3月24日新增-->
        <div class="index_block">
            <ul class="index_title_w50">
                <!--<li>直播看房 <a th:href="@{/liveList/}" target="_blank" class="pull-right index_title_right spxg1">
                    <span class="index_icon wymf_icon geng_d"></span>
                    更多
                </a></li>-->
                <li>
                    房产视频
                    <a th:href="@{/videos/}" target="_blank" class="pull-right index_title_right spxg2">
                        <span class="index_icon wymf_icon geng_d"></span>
                        更多
                    </a>
                </li>
            </ul>
            <div class="video_show">
                <!--<ul class="new_live_ul new_live_ul_50" id="con_spxg_1">
                    <li class="new_live_li" th:each="live, iterStat : ${wzLive}" th:if="${iterStat.index} < 2">
                        <a th:href="${'/liveDetail/' + live.id + '.htm'}" target="_blank">
                            <img th:src="${live.banner}" alt="">
                            <div class="new_live_type"><i class="live_type live_type1"
                                                          th:text="${live.status eq '-1'? '未开始': live.status eq '0'? '回放':'直播中'}"></i><span th:text="${live.pv + '观看'}"></span></div>
                            <h5 class="new_live_txt" th:text="${live.title}"></h5>
                        </a>
                    </li>
                </ul>
-->
                <ul class="new_video_ul new_live_ul2_50" id="con_spxg_2" >
                    <li class="new_video_li" th:each="video, iterStat2 : ${videos}" th:if="${iterStat2.index} < 4">
                        <a th:href="${'/video/' + video.videoId + '.htm'}" target="_blank">
                            <img th:src="${video.videoPic}" alt="">
                            <div class="new_video_time"><i></i><span th:text="${video.newVideoTime}"></span></div>
                            <h5 th:text="${video.videoTitle}"></h5>
                        </a>
                    </li>
                </ul>

            </div>


        <!--买新房-->
        <div class="index_block" style="margin-bottom: 0;">
            <div class="index_title new_wei">
                <h1 class="index_h1 mxf_h1"><a th:href="@{'/houses'}" target="_blank">
                    新房
                </a></h1>
                <a href="https://sy.fangxiaoer.com/house/housemap/" target="_blank" class="index_dzyh pull-left">
                    <span class="index_icon ydtz_icon di"></span>
                    用地图找
                </a>
                <ul class="pull-left">
                    <li th:if="${!#lists.isEmpty(names2)}" th:each="mxf,iterStat:${names2}" th:class="${iterStat.count eq 1 ?'hover':''}" th:id="${'mxfqh'+iterStat.count}" th:onmouseenter="${'setTabHouseDelay('''+'\'+'''mxfqh'+'\'+''''',10,'+iterStat.count+')'}" onmouseout="setTabHouseDelayout()"><span><a th:text="${#strings.isEmpty(mxf)?'':mxf }"></a></span></li>
                </ul>
                <a href="/static/business.htm" target="_blank" class="pull-right index_title_right">
                    <span class="index_icon dkjsq_icon"></span>
                    贷款计算器
                </a>
            </div>
            <div class="bztj_left new_house" style="margin-top: 0px">

                <div class="new_left new_gbor g_oa" th:if="${!#lists.isEmpty(region)}" style="margin-bottom: 14px">
                    <h1>区域找房</h1>
                    <a th:each="r,rIndex:${region}" th:if="${r.id ne ''}" th:href="${'/houses/'+ r.url}" th:class="${rIndex.count % 3 == 0 || rIndex.last?'': 'index_screen_line'}"  target="_blank" th:text="${r.name}"></a>
                    <div class="cl"></div>
                </div>

                <div class="new_left new_gbor" th:if="${!#lists.isEmpty(price)}" style="margin-bottom: 13px">
                    <h1>价格找房</h1>
                    <a th:each="p,rIndex:${price}" th:if="${p.id ne ''}" th:href="${'/houses/' + p.url}" target="_blank" th:class="${rIndex.count % 2 == 0 || rIndex.last?'': 'index_screen_line'}" th:text="${#strings.toString(p.name).replace('元/m²','')}">4000以下</a>
                    <div class="cl"></div>
                </div>

                <div class="new_left new_gbor" style="margin-bottom: 13px">
                    <h1>类型找房</h1>
                    <a th:each="projectType,rIndex:${projectType}" th:if="${projectType.id ne ''}" th:href="${'/houses/' +projectType.url}" th:class="${rIndex.count % 3 == 0  || rIndex.last ?'': 'index_screen_line'}" th:text="${projectType.name}" target="_blank">普宅</a>
                    <div class="cl"></div>
                </div>
                <!--<div class="new_left">-->
                    <!--<h1>特色找房</h1>-->
                    <!--<a th:href="@{/subways/k1}" target="_blank" class="index_screen_line_ts">地铁沿线</a>-->
                    <!--&lt;!&ndash;<a th:href="@{/decorations/}" target="_blank" >精装房</a>&ndash;&gt;-->
                    <!--<a th:href="@{/schools/}" target="_blank" class="index_screen_line_ts">优质学区</a>-->
                    <!--<a th:href="@{/exits/}" target="_blank" class="index_screen_line_ts">现房</a>-->
                    <!--<div class="cl"></div>-->
                <!--</div>-->
                <div class="new_left" style="border-bottom: 0">
                    <h1>品牌找房</h1>
                    <a th:href="@{'/brandCompany/2.htm'}" target="_blank" class="index_screen_line">万科</a>
                    <a th:href="@{'/brandCompany/7.htm'}" target="_blank" >金地</a>
                    <a th:href="@{'/brandCompany/3.htm'}"  target="_blank" class="index_screen_line">保利</a>
                    <a th:href="@{'brandCompany/9.htm'}"   target="_blank" class="index_screen_line" >中海</a>
                    <!--<a th:href="@{'/brandCompany/4.htm'}"  target="_blank" class="order">恒大</a>-->
                    <a th:href="@{'/brandCompany/16.htm'}"  target="_blank" class="index_screen_line">龙湖</a>
                    <a th:href="@{'/brandCompany/11.htm'}" target="_blank" class="index_screen_line">华润</a>
                    <a th:href="@{'/houses/search=碧桂园'}"  target="_blank">碧桂园</a>
                    <div class="cl"></div>
                </div>
            </div>
            <div class="bztj_middle">
                <div  th:if="${!#lists.isEmpty(newHouse)}" th:each="houselist,i:${newHouse}" th:id="${'con_mxfqh_'+i.count}"  th:style="${i.count == 1? '':'display: none;'}">
                    <div class="swiper-container index_css wei" style="display:none;" th:id="${'huaStat' + i.index}">
                        <div class="swiper-wrapper">
                            <a th:each="house,j:${houselist}" th:if="${house.huaState eq '0'}" th:href="${house.url}" th:class="${j.count ==1 ? 'swiper-slide swiper-slide-visible swiper-slide-active':'swiper-slide'}" th:style="'background-image: url('+${house.image}+') ;'" target="_blank">
                                <sub>
                                    <img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></sub>
                                <div class="vrn" th:if="${house.hasVR ne '0'}"></div>
                            </a>
                        </div>
                        <div class="pagination ZLB"></div>
                    </div>
                    <a class="pic_wei" th:if="${house.huaState eq '1'}" th:each="house,j:${houselist}" th:href="${house.url}" target="_blank">
                        <i class="liveTxt"  th:if="${house.hasLive ne '0'}">直播看房</i>
                        <sub>
                            <img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></sub>
                        <img th:src="${house.image}" th:alt="${house.projectName}" />
                        <h4 class="bztj_title" th:text="${house.projectName+'['+house.region+']'}"></h4>
                        <div class="bztj_price"><span th:utext="${house.price}"></span></div>
                        <div class="cff5200" th:text="${house.introduce}"></div>
                        <div class="vrn vrn1" th:if="${house.hasVR ne '0'}"></div>
                    </a>
                    <th:block th:each="house,j:${houselist}" th:if="${house.huaState eq '0'}">
                        <script th:inline="javascript">
                            var indexy = [[${i.index}]];
                            $(function () {
                                $("#huaStat" + indexy).css("display","block");
                                $("#huaStat" + indexy).next("a").css("margin",0);
                            });
                        </script>
                    </th:block>
                </div>
            </div>
            <script>
                $(function () {
                    $(".bztj_middle .pic_wei:nth-child(2)").css("margin-top",0);
                    $(".bztj_middle .pic_wei:nth-child(3)").css("margin-top",0);
                    $(".bztj_middle .pic_wei:nth-child(4)").css("margin-top",0);
                    $(".bztj_middle .index_css").each(function () {
                        if($(this).css("display") == "block"){
                            $(this).parent().find(".pic_wei:eq(1)").css("margin-top","17px");
                            $(this).parent().find(".pic_wei:eq(2)").css("margin-top","17px");
                        }
                    });
                });
            </script>
            <!--新房右侧焦点图-->
            <div class="bztj_right" style="margin-top: 16px;">
                <div class="Headline">
                    <img src="https://static.fangxiaoer.com/web/images/indexReversion/Headline.png"/>
                    <div class="show">
                        <div class="lunbo">
                            <a th:each="normalad,i:${normal.topline}"   target="_blank"  th:href="${'/news/'+normalad.id+'.htm'}" th:text="${normalad.titleShow}">新年第一波开盘潮，快来看看有没有你</a>
                        </div>

                    </div>
                </div>
                <div class="dynamic">
                    <a class="name" th:href="@{/news/151}" target="_blank">楼盘要闻</a>
                    <a class="more" th:href="@{/news/151}" target="_blank">更多</a>
                    <ul class="index_ul mxf_ul" th:if="${!#lists.isEmpty(projectActivity)}">
                        <li  th:each="projectActivity,pindex:${projectActivity}" th:if="${pindex.index lt 2}"><a th:href="${#strings.isEmpty(projectActivity.url)?'':projectActivity.url}" th:title="${#strings.isEmpty(projectActivity.projectName)?'':projectActivity.projectName}" th:text="${#strings.isEmpty(projectActivity.projectName)?'':projectActivity.projectName}" target="_blank" class="bztj_li">为爱攀登 沈阳碧桂园地产将公益进行到底</a></li>
                    </ul>
                </div>
                <!--<div class="vido">
                    <a  href="https://event.fangxiaoer.com/20210622_mobile.htm" target="_blank" class="new_video">
                       <img src="https://imageicloud.fangxiaoer.com/event/2021/07/09/164033537.jpeg" alt="">
                    </a>
                </div>-->
                <!--楼盘动态 2022年3月24日新增-->
                <div class="dynamic" style="margin-top: 26px;">
                    <a class="name" href="/projectnews">楼盘动态速览</a>
                    <a class="more" href="/projectnews">更多</a>
                </div>
                <div class="index_house_trends" style="margin-top: 0px; border-top: 1px dashed #ebebeb;">
                    <div class="trends_show">
                        <div class="trends_lunbo">
                            <th:block th:each="dynamic : ${projectDynamics}">
                                <a th:href="${'/house/' + dynamic.projectId + '-' + dynamic.projectType + '/news.htm'}" target="_blank" th:text="${dynamic.projectDesc}"></a></th:block>
                        </div>
                    </div>
                    <!--<div class="trends_btn">
                        <a href="/projectnews"><img src="https://static.fangxiaoer.com/images/dynamic.jpeg" alt=""></a>
                    </div>-->
                </div>
                <style>
                    .index_house_trends{width: 270px;height: 198px; margin-top: 29px;}
                    .trends_show{overflow: hidden;width: 100%;height: 155px; padding-top: 12px;margin-bottom: 10px;}
                    .trends_show a{font-size: 14px;font-family: Microsoft YaHei;font-weight: 400; color: #333333;line-height: 16px;margin-bottom: 5px !important;display: block;padding: 0 10px;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;line-height: 22px;height: 48px;}
                    .trends_btn{width: 270px;height: 51px;}
                    .trends_btn img{width: 100%;height: 100%}
                    .trends_show a:hover{ color: #ff5200 !important;}
                </style>
                <script>
                    if($(".trends_lunbo a").length>1){
                        trends_lunbo()
                    }
                    function trends_lunbo() {
                        setInterval(
                            function () {
                            $(".trends_lunbo").animate({top:"-127px"},500,function () {
                                $(".trends_lunbo a:last").after($(".trends_lunbo a:first"))
                                $(".trends_lunbo").css("top",0)
                            })
                            },3000
                        )
                    }
                </script>

            </div>
            <div class="cl"></div>
        </div>
        <div class="cl"></div>
        <!--通栏广告1-->
        <div th:class="${#lists.size(normal.normal1) le 1 ? 'tlBanner':( nIndex.last? 'tlBanner middle tlBannerLast': 'tlBanner middle' ) }"
             th:if="${!#lists.isEmpty(normal.normal1)}"  th:each="normalad,nIndex:${normal.normal1}">
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('广告') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png">
            </sup>
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('活动') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ard.png">
            </sup>
            <th:block th:if="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
            <a th:href="${normalad.url}" target="_blank">
                <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
            </a>
            </th:block>
            <th:block th:unless="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
            </th:block>
        </div>
       <!--品牌馆-->
        <div class="pinpaiG">
            <ul>
                <li class="pinpaiF">
                    <img src="https://static.fangxiaoer.com/web/images/sy20240819.png" alt="">
                </li>
                <li class="pinpaiH" th:each="one,i:${rb.brands}" th:if="${i.count lt 8}">
                    <a th:href="${'/brandCompany/'+one.id+'.htm'}" target="_blank">
                        <img th:src="${one.logo}" th:alt="${one.brandName}">
                    </a>
                </li>
            </ul>
            <span>
                <a th:href="${'/brandCompany/'}" target="_blank">更多</a>
            </span>
        </div>
        <!--优惠-->
        <div class="index_block">
            <div class="index_title" style="margin-bottom: 20px">
                <h1 class="index_h1 bztj_h1">
                    <a  href="/brandCompany/" target="_blank">
                    <span class="name">品牌</span>
                </a>
                </h1>
                <a th:href="@{'/houses/'}" target="_blank" class="index_dzyh pull-left">
                    <span class="index_icon dzyh_icon sh"></span>
                    楼盘搜索
                </a>
                <!--本周推荐-->
                <ul class="pull-left" th:if="${!#lists.isEmpty(names1)}">
                    <li th:each="bz,i:${names1}" th:id="${'bztjqh'+ i.count}" th:class="${i.count eq 1 ?'hover':''}" th:onmouseenter="${'setTabHouseDelay('''+'\'+'''bztjqh'+'\'+''''',10,'+i.count+')'}" onmouseout="setTabHouseDelayout()"><span><a th:text="${bz}"></a></span></li>
                </ul>
                <div class="buy">
                    <a th:href="${'/helpSearch?ids=1'}" target="_blank">我要买房</a>
                </div>
            </div>
            <div class="bztj_left" style="margin-top: 0">
                <div th:if="${!#lists.isEmpty(hotRecomment)}" >
                    <h2><a>热门推荐</a></h2>
                    <ul>
                        <li th:each="hotRecomment:${hotRecomment}">
                            <a th:href="${#strings.isEmpty(hotRecomment.url)?'':hotRecomment.url}" th:text="${#strings.isEmpty(hotRecomment.projectName)?'':hotRecomment.projectName}" th:title="${#strings.isEmpty(hotRecomment.projectName)?'':hotRecomment.projectName}" target="_blank">长堤湾</a>
                            <span class="rw" th:text="${#strings.isEmpty(hotRecomment.price)?'':hotRecomment.price}">3080元/平</span>
                        </li>
                        <div class="clearfix"></div>
                    </ul>
                </div>
                <div th:if="${!#lists.isEmpty(lastOpen)}">
                    <h2 class="news_w"><a>最新开盘</a></h2>
                    <div class="scroll_out">
                        <div class="scroll">
                            <div class="txtblock">
                                <div id="myScroll">
                                    <ul>
                                        <li th:each="lastOpen:${lastOpen}" >
                                            <span th:text="${ lastOpen.AddTime}">7月31日</span>
                                            <p><i><a th:href="${#strings.isEmpty(lastOpen.url)?'':lastOpen.url}" target="_blank" class="img" th:text="${#strings.isEmpty(lastOpen.projectName)?'':lastOpen.projectName}" th:title="${#strings.isEmpty(lastOpen.projectName)?'':lastOpen.projectName}">万科春河里</a></i><i class="price" th:text="${#strings.isEmpty(lastOpen.price)?'':lastOpen.price}">19000元/平</i></p>
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bztj_middle">
                <div  th:if="${!#lists.isEmpty(recommend)}" th:each="houselist,i:${recommend}" th:id="${'con_bztjqh_'+i.count}"  th:style="${i.count == 1? '':'display: none;'}">
                    <div class="swiper-container index_css weu" style="display:none;" th:id="${'huaState' + i.index}">
                        <div class="swiper-wrapper">
                            <a th:each="house,j:${houselist}" th:if="${house.huaState eq '0'}"  th:href="${house.url}" th:class="${j.count ==1 ? 'swiper-slide swiper-slide-visible swiper-slide-active':'swiper-slide'}" th:style="'background-image: url('+${house.image}+') ;'" target="_blank">
                                <sub><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></sub>
                                <div class="vrn" th:if="${house.hasVR ne '0'}"></div>
                            </a>
                        </div>
                        <div class="pagination ZLB"></div>
                    </div>
                    <a class="pic_wei" th:if="${house.huaState eq '1'}" th:href="${house.url}"  target="_blank" th:each="house,j:${houselist}">
                        <i class="liveTxt" th:if="${house.hasLive ne '0'}">直播看房</i>
                        <sub>
                            <img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></sub>
                        <img  th:src="${house.image}" th:alt="${house.projectName}" />
                        <h4 class="bztj_title" th:text="${house.projectName}+'['+${house.region}+']'" ></h4>
                        <div class="bztj_price"><span th:utext="${house.price}"></span></div>
                        <div class="cff5200" th:text="${house.introduce}"></div>
                        <div class="vrn vrn1" th:if="${house.hasVR ne '0'}"></div>
                    </a>
                    <th:block  th:each="house,j:${houselist}" th:if="${house.huaState eq '0'}">
                        <script>
                            var indexh = [[${i.index}]];
                            $(function () {
                                $("#huaState" + indexh).css("display","block");
                                $("#huaState" + indexh).next("a").css("margin",0);
                            });
                        </script>
                    </th:block>
                </div>
                <script>
                    $(function () {
                        $(".bztj_middle .pic_wei:nth-child(2)").css("margin-top",0);
                        $(".bztj_middle .pic_wei:nth-child(3)").css("margin-top",0);
                        $(".bztj_middle .pic_wei:nth-child(4)").css("margin-top",0);
                        $(".bztj_middle .index_css").each(function () {
                            if($(this).css("display") == "block"){
                                $(this).parent().find(".pic_wei:eq(1)").css("margin-top","17px");
                                $(this).parent().find(".pic_wei:eq(2)").css("margin-top","17px");
                            }
                        });
                    });
                </script>
            </div>
            <!--本周推荐右侧广告 焦点图2-->
            <div class="bztj_right yh_right" th:if="${!#lists.isEmpty(normal.focus2) and !#maps.isEmpty(normalad)}"  th:each="normalad:${normal.focus2}" >
                <a th:href="${normalad.url}" target="_blank" style="width: 270px;height: 410px;overflow:hidden;display:block;margin-top: 18px;">
                    <img th:src="${normalad.image}"th:alt="${normalad.projectName}" style="width: 100%">
                </a>
            </div>
            <div class="cl"></div>
        <div class="cl"></div>
        <!--通栏广告2-->
        <div th:class="${#lists.size(normal.normal2) le 1 ? 'tlBanner':( nIndex.last? 'tlBanner middle tlBannerLast': 'tlBanner middle' ) }" th:if="${!#lists.isEmpty(normal.normal2)}"  th:each="normalad,nIndex:${normal.normal2}">
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('广告') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png">
            </sup>
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('活动') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ard.png">
            </sup>
            <th:block th:if="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <a th:href="${normalad.url}" target="_blank">
                    <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
                </a>
            </th:block>
            <th:block th:unless="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
            </th:block>
        </div>
        <!--<div class="no_banner" th:unless="${!#lists.isEmpty(normal.normal2)}" ></div>-->
        <!--便民广告-->
        <!--<div th:include="fragment/fragment :: ConvenientService"></div>-->
        <!--买二手房-->
        <div class="index_block">
            <div class="index_title">
                <h1 class="index_h1 mesf_h1"><a th:href="@{/saleHouses}" target="_blank">
                    二手房
                </a></h1>
                <a href="/salemap/" target="_blank" class="index_dzyh pull-left">
                    <span class="index_icon ydtz_icon di"></span>
                    用地图找
                </a>
                <ul class="pull-left" th:if="${!#lists.isEmpty(names3)}">

                    <li  th:each="one,i:${names3}" th:id="${'mesfqh'+i.count}" th:class="${i.count eq 1 ? 'hover': ''}"  th:onmouseenter="${'setTabHouseDelay('''+'\'+'''mesfqh'+'\'+''''',10,'+i.count+')'}" onmouseout="setTabHouseDelayout()">
                <span>
                    <a th:text="${one}"></a>
                </span>
                    </li>
                </ul>
                <a href="/static/saleHouse/saleHouse.htm" target="_blank" class="pull-right index_title_right">
                    <span class="index_icon wymf_icon mai_wei"></span>
                    我要卖房
                </a>
            </div>
            <div class="bztj_left esf_left   ershoufang">
                <div class="new_left" th:if="${!#lists.isEmpty(commonRegion)}">
                    <h1>区域找房</h1>
                    <a th:each="r,rIndex:${commonRegion}" th:if="${r.id ne ''}" th:href="${'/saleHouses/'+r.url}" th:class="${rIndex.count % 3 == 0 || rIndex.last  ?'': 'index_screen_line'}"  target="_blank" th:text="${r.name}"></a>
                    <div class="cl"></div>
                </div>

                <div class="new_left" th:if="${!#lists.isEmpty(saleHousePrice)}">
                    <h1>价格找房</h1>
                    <a th:each="saleHousePrice,rIndex:${saleHousePrice}"  th:if="${saleHousePrice.id ne ''}" th:class="${rIndex.count % 2 == 0 || rIndex.last ?'': 'index_screen_line'}" th:href="${'/saleHouses/'+saleHousePrice.url}" th:text="${saleHousePrice.name}" target="_blank">50万以下</a>
                    <div class="cl"></div>
                </div>

                <div class="new_left"  th:if="${!#lists.isEmpty(room)}" style="border-bottom: 0;padding-bottom: 0">
                    <h1>户型找房</h1>
                    <a th:each="room,rIndex:${room}" th:if="${room.id ne ''}" th:class="${rIndex.count % 3 == 0 || rIndex.last ?'': 'index_screen_line'}" th:href="${'/saleHouses/'+room.url}" th:text="${room.name}" target="_blank">50万以下</a>
                    <div class="cl"></div>
                </div>
                <!--<div class="new_left" >-->
                    <!--<h1>来源</h1>-->
                    <!--<a th:href="@{/saleHouses/g1}" target="_blank" class="index_screen_line">个人</a>-->
                    <!--<a  th:href="@{/saleHouses/g2}" target="_blank">经纪人</a>-->
                    <!--<div class="cl"></div>-->
                <!--</div>-->
            </div>
            <div class="bztj_middle">

                <div th:if="${!#lists.isEmpty(secondhouse)}" th:each="house, i: ${secondhouse}" th:id="${'con_mesfqh_' + i.count}" th:style="${i.count == 1? '':'display: none;'}" >
                    <a th:each="secondhouse_1,secIndex:${house}" th:href="${secondhouse_1.url}" th:class="${secondhouse_1.Name eq '优秀经纪人'?'excellentAgent':''}" th:if="${secIndex.index lt 6}" target="_blank">
                        <sub><img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></sub>
                        <img th:src="${secondhouse_1.image}"  th:alt="${secondhouse_1.projectName}" class="header"/>
                        <img th:if="${secondhouse_1.Name eq '优秀经纪人'}"  src="https://static.fangxiaoer.com/web/images/indexReversion/jjr.png" class="jjr">
                        <!--<h4 class="bztj_title" th:text="${secondhouse_1.projectName}"></h4>-->
                        <div class="esf_layout" th:if="${secondhouse_1.Name ne '优秀经纪人'}" >
                            <th:block th:text="${secondhouse_1.projectName}"></th:block><span></span>
                        </div>
                        <div class="houeseText" th:if="${secondhouse_1.Name ne '优秀经纪人'}" >
                            <span class="index_icon esf_ads_icon"></span><th:block th:text="${secondhouse_1.introduce}"></th:block>
                            <i th:utext="${secondhouse_1.price}"> </i>
                        </div>
                        <div class="ye" th:unless="${secondhouse_1.Name ne '优秀经纪人'}" >
                            <h1>从业年限：<th:block th:text="${secondhouse_1.price}"></th:block></h1>
                        </div>
                        <div class="qu" th:unless="${secondhouse_1.Name ne '优秀经纪人'}" >
                            <h1>主营区域：<th:block th:text="${secondhouse_1.region}"></th:block></h1>
                        </div>
                    </a>
                </div>

            </div>
            <div class="bztj_right esf_right">
                <div class="saleAdsSwiper1" th:if="${!#lists.isEmpty(secFocus)}">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide index-slide" th:if="${!#lists.isEmpty(secFocus) and #strings.toString(focusSaleItem.url).indexOf('http') ne -1}" th:each="focusSaleItem:${secFocus}">
                            <a  th:href="${#strings.isEmpty(focusSaleItem.url)?'':focusSaleItem.url}" target="_blank" class="ad_qh ">
                                <img style="height:410px;" th:src="${#strings.isEmpty(focusSaleItem.image)?'': focusSaleItem.image}" th:alt="${#strings.isEmpty(focusSaleItem.projectName)?'':focusSaleItem.projectName}" />
                            </a>
                        </div>
                        <div class="swiper-slide" th:if="${!#lists.isEmpty(focusSale) and #strings.toString(focusSaleItem.url).indexOf('http') eq -1}" th:each="focusSaleItem:${focusSale}">
                            <img  style="height:410px;" th:src="${#strings.isEmpty(focusSaleItem.image)?'': focusSaleItem.image}" th:alt="${#strings.isEmpty(focusSaleItem.projectName)?'':focusSaleItem.projectName}" />
                        </div>
                    </div>
                    <div class="pagination ESF"></div>
                    <style>
                        .saleAdsSwiper1 {
                            width: 270px;
                            height: 410px;
                        }
                    </style>
                </div>
                <th:block th:if="${!#lists.isEmpty(secFocus) and #lists.size(secFocus) gt 1}">
                    <script>
                        var saleAdsSwiper1 = new Swiper ('.saleAdsSwiper', {
                            direction: 'horizontal',
                            loop: true,
                            autoplay:5000,
                            autoplayDisableOnInteraction : false,
                            pagination: '.pagination',
                            paginationClickable: true
                        })
                    </script>
                </th:block>
                <th:block th:unless="${!#lists.isEmpty(secFocus)}">
                <h2 style="margin-top: 19px">
                    <div class="saleAdsSwiper" >
                        <div class="swiper-wrapper">
                            <div class="swiper-slide index-slide" th:if="${!#lists.isEmpty(focusSale) and #strings.toString(focusSaleItem.url).indexOf('http') ne -1}" th:each="focusSaleItem:${focusSale}">
                                <a  th:href="${#strings.isEmpty(focusSaleItem.url)?'':focusSaleItem.url}" target="_blank" class="ad_qh ">
                                    <img style="height:190px;" th:src="${#strings.isEmpty(focusSaleItem.image)?'': focusSaleItem.image}" th:alt="${#strings.isEmpty(focusSaleItem.projectName)?'':focusSaleItem.projectName}" />
                                </a>
                            </div>
                            <div class="swiper-slide" th:if="${!#lists.isEmpty(focusSale) and #strings.toString(focusSaleItem.url).indexOf('http') eq -1}" th:each="focusSaleItem:${focusSale}">
                                <img  style="height:190px;" th:src="${#strings.isEmpty(focusSaleItem.image)?'': focusSaleItem.image}" th:alt="${#strings.isEmpty(focusSaleItem.projectName)?'':focusSaleItem.projectName}" />
                            </div>
                        </div>
                        <div class="pagination ESF"></div>
                    </div>
                </h2>
                <h2><a th:href="@{/videos/10}" target="_blank" class="pull-right more">更多</a><a th:href="@{/videos/10}" target="_blank">中介零距离</a></h2>
                <a th:if="${!#lists.isEmpty(saleVideo)}" th:each="saleVideo:${saleVideo}" th:href="${saleVideo.url}" style="height:145px;" target="_blank" class="new_video">
                    <img th:src="${saleVideo.image}" th:alt="${saleVideo.projectName}" style="margin-top: 0">
                    <span class="index_icon video_icon"></span>
                    <h4><th:block th:text="${saleVideo.projectName}"></th:block></h4>
                </a>
                    <th:block th:if="${!#lists.isEmpty(focusSale) and #lists.size(focusSale) gt 1}">
                        <script>
                            var saleAdsSwiper = new Swiper ('.saleAdsSwiper', {
                                direction: 'horizontal',
                                loop: true,
                                autoplay:5000,
                                autoplayDisableOnInteraction : false,
                                pagination: '.pagination',
                                paginationClickable: true
                            })
                        </script>
                    </th:block>
                </th:block>
            </div>
            <div class="cl"></div>
        </div>
        <div class="cl"></div>
        <!--通栏广告3-->
        <div th:class="${#lists.size(normal.normal3) le 1 ? 'tlBanner':( nIndex.last? 'tlBanner middle tlBannerLast': 'tlBanner middle' ) }"
             th:if="${!#lists.isEmpty(normal.normal3)}"  th:each="normalad, nIndex:${normal.normal3}">
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('广告') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png">
            </sup>
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('活动') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ard.png">
            </sup>
            <th:block th:if="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <a th:href="${normalad.url}" target="_blank">
                    <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
                </a>
            </th:block>
            <th:block th:unless="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
            </th:block>
        </div>
        <!--租房-->
        <div class="index_block" style="margin-bottom: 0">
            <div class="index_title">
                <h1 class="index_h1 zf_h1"><a th:href="@{/rents}" target="_blank">
                    租房</a></h1>
                <a th:href="@{/static/rentmap.htm}" target="_blank" class="index_dzyh pull-left">
                    <span class="index_icon ydtz_icon di"></span>
                    用地图找
                </a>
                <ul class="pull-left"  th:if="${!#lists.isEmpty(names4)}">

                    <li th:each="zf,i:${names4}" th:id="${'zfqh'+i.count}" th:class="${i.count eq 1 ? 'hover':''}" th:onmouseenter="${'setTabHouseDelay('''+'\'+'''zfqh'+'\'+''''',10,'+i.count+')'}" onmouseout="setTabHouseDelayout()">
                            <span>
                                <a th:text="${zf}"></a>
                            </span>
                    </li>
                </ul>
                <a href="/rentwhole" target="_blank" class="pull-right index_title_right">
                    <span class="index_icon wymf_icon zhu_wei"></span>
                    我要出租
                </a>
            </div>


            <div class="bztj_left esf_left zhufang">

                <div class="new_left"  th:if="${!#lists.isEmpty(commonRegion)}" style="padding-top: 0; margin-top: 0px">
                    <h1>区域找房</h1>
                    <a th:each="r,rIndex:${commonRegion}" th:if="${r.id ne ''}" th:href="${'/rents/'+r.url}"  th:class="${rIndex.count % 3 == 0 || rIndex.last ?'': 'index_screen_line'}"  target="_blank" th:text="${r.name}"></a>
                    <div class="cl"></div>
                </div>

                <div class="new_left"  th:if="${!#lists.isEmpty(rentBuget)}">
                    <h1>租金找房</h1>
                    <a th:each="rentBuget,rIndex:${rentBuget}" th:if="${rentBuget.id ne ''}"  th:class="${rIndex.count % 2 == 0 || rIndex.last ?'': 'index_screen_line'}"  th:href="${'/rents/'+rentBuget.url}"  target="_blank" th:text="${rentBuget.name}"></a>
                    <div class="cl"></div>
                </div>

                <div class="new_left" th:if="${!#lists.isEmpty(room)}">
                   <h1>户型找房</h1>
                    <a th:each="room,rIndex:${room}" th:if="${room.id ne ''}" th:href="${'/rents/'+room.url}" th:class="${rIndex.count % 3 == 0|| rIndex.last  ?'': 'index_screen_line'}" th:text="${#strings.isEmpty(room.name)?'':room.name}" target="_blank">50万以下</a>
                    <div class="cl"></div>
                </div>

                <div class="new_left" style="border-bottom: 0;padding-bottom: 0">
                    <h1>租住方式</h1>
                    <a th:href="@{/rents/w1}" target="_blank" class="index_screen_line">整租</a>
                    <a th:href="@{/rents/w2}" target="_blank">合租</a>
                    <div class="cl"></div>
                </div>
                <!--<div class="new_left" style="border-bottom: 0; padding-bottom: 0">-->
                    <!--<h1>来源</h1>-->
                    <!--<a th:href="@{/rents/g1}" target="_blank" class="index_screen_line">个人</a>-->
                    <!--<a th:href="@{/rents/g2}" target="_blank">经纪人</a>-->
                    <!--<div class="cl"></div>-->
                <!--</div>-->
            </div>
            <div class="bztj_middle">
                <div th:if="${!#lists.isEmpty(renthouse)}" th:each="house, i : ${renthouse}" th:id="${'con_zfqh_' + i.count}" th:style="${i.count == 1? '':'display: none;'}" >
                    <a th:each="renthouse_1:${house}" th:href="${renthouse_1.url}" target="_blank">
                        <sub><img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></sub>
                        <img th:src="${renthouse_1.image}"  th:alt="${renthouse_1.projectName}" />
                        <div class="esf_layout"><th:block th:text="${renthouse_1.projectName}"></th:block><span></span></div>
                        <div class="houeseText"><span class="index_icon esf_ads_icon"></span><th:block th:text="${renthouse_1.introduce}"></th:block><i th:utext="${renthouse_1.price}"></i></div>
                    </a>
                </div>

            </div>
            <div class="bztj_right zf_right">
                 <div class="rentAdsSwiper" >
                    <div class="swiper-wrapper">
                        <div class="swiper-slide" th:if="${!#lists.isEmpty(focusRent) and #strings.toString(focusRentItem.url).indexOf('http') ne -1}" th:each="focusRentItem:${focusRent}">
                            <a  th:href="${#strings.isEmpty(focusRentItem.url)?'':focusRentItem.url}" target="_blank" class="ad_qh ">
                                <img style="height:410px;" th:src="${#strings.isEmpty(focusRentItem.image)?'': focusRentItem.image}" th:alt="${#strings.isEmpty(focusRentItem.projectName)?'':focusRentItem.projectName}" />
                            </a>
                        </div>
                        <div class="swiper-slide" th:if="${!#lists.isEmpty(focusRent) and #strings.toString(focusRentItem.url).indexOf('http') eq -1}" th:each="focusRentItem:${focusRent}">
                            <img  style="height:410px;" th:src="${#strings.isEmpty(focusRentItem.image)?'': focusRentItem.image}" th:alt="${#strings.isEmpty(focusRentItem.projectName)?'':focusRentItem.projectName}" />
                        </div>
                    </div>
                    <div class="pagination ZF"></div>
                </div>
                <th:block th:if="${!#lists.isEmpty(focusRent) and #lists.size(focusRent) gt 1}">
                <script>
                        var myrentSwiper = new Swiper ('.rentAdsSwiper', {
                            direction: 'horizontal',
                            loop: true,
                            autoplay:5000,
                            autoplayDisableOnInteraction : false,
                            pagination: '.pagination',
                            paginationClickable: true
                        })
                </script>
                </th:block>
            </div>
            <div class="cl"></div>
        </div>
        <div class="cl"></div>
        <!--通栏广告4-->
        <div th:class="${#lists.size(normal.normal4) le 1 ? 'tlBanner':( nIndex.last? 'tlBanner middle tlBannerLast': 'tlBanner middle' ) }"
             th:if="${!#lists.isEmpty(normal.normal4)}"  th:each="normalad,nIndex:${normal.normal4}">
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('广告') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png">
            </sup>
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('活动') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ard.png">
            </sup>
            <th:block th:if="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <a th:href="${normalad.url}" target="_blank">
                    <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
                </a>
            </th:block>
            <th:block th:unless="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
            </th:block>
        </div>
           <!--商铺/写字楼-->
            <div class="index_block" style="margin-bottom: 0">
                <div class="index_title">
                    <h1 class="index_h1 zf_h1" style="margin-left: 15px">商铺/写字楼</h1>
                    <a th:href="@{'/helpSearch?ids=4'}" target="_blank" class="index_dzyh pull-left">
                        <span class="index_icon ydtz_icon soi"></span>
                        商铺选址
                    </a>
                    <ul class="pull-left"  th:if="${!#lists.isEmpty(names5)}">

                        <li th:each="zf,i:${names5}" th:id="${'spqh'+i.count}" th:class="${i.count eq 1 ? 'hover':''}" th:onmouseenter="${'setTabHouseDelay('''+'\'+'''spqh'+'\'+''''',10,'+i.count+')'}" onmouseout="setTabHouseDelayout()">
                            <span>
                                <a th:text="${zf}"></a>
                            </span>
                        </li>
                    </ul>
                    <a href="/shopsell" target="_blank" class="pull-right index_title_right">
                        <span class="index_icon wymf_icon soi_wei"></span>
                        我要出售
                    </a>
                </div>
                <div class="bztj_left esf_left shangpu">
                    <div class="new_left"  th:if="${!#lists.isEmpty(shopType)}" style="padding-top: 0; margin-top: 0px">
                        <h1>商铺类型</h1>
                        <a target="_blank" th:each="shopType,i:${shopType}"  th:if="${shopType.id ne '' and i.index != 5}" th:class="${i.count % 2 == 0 || i.last ||i.index==4 ?'': 'index_screen_line'}" th:href="${'/shops/'+shopType.url }" th:text="${#strings.isEmpty(shopType.name)?'': #strings.toString(shopType.name).replace('配套','') }">住宅底商</a>
                        <div class="cl"></div>
                    </div>
                    <div class="new_left"  th:if="${!#lists.isEmpty(shopArea)}">
                        <h1>面积找房</h1>
                        <a th:each="shopArea,sIndex:${shopArea}" th:if="${shopArea.id ne ''}"  th:class="${sIndex.count % 2 != 0 || sIndex.last ?'': 'index_screen_line'}"  th:href="${'/shops/'+shopArea.url}"  target="_blank" th:text="${shopArea.name}"></a>
                        <div class="cl"></div>
                    </div>

                    <!--<div class="new_left" th:if="${!#lists.isEmpty(shopMemberType)}">-->
                        <!--<h1>来源</h1>-->
                        <!--<a th:each="membertype,sIndex:${shopMemberType}" th:if="${membertype.id ne ''}" th:href="${'/shops/'+membertype.url}" th:class="${sIndex.count % 2 != 0|| sIndex.last ? '':'index_screen_line'}" th:text="${#strings.isEmpty(membertype.name)?'':membertype.name}" target="_blank">50万以下</a>-->
                        <!--<div class="cl"></div>-->
                    <!--</div>-->

                    <div class="new_left" th:if="${!#lists.isEmpty(category)}">
                        <h1>写字楼类型</h1>
                        <a th:each="categoryItem,cIndex:${category}" th:if="${categoryItem.id ne ''}" th:href="${'/scriptoriums/'+categoryItem.url}" th:class="${cIndex.count % 2 != 0|| cIndex.last  ?'': 'index_screen_line'}" th:text="${#strings.isEmpty(categoryItem.name)?'':categoryItem.name}" target="_blank">50万以下</a>
                        <div class="cl"></div>
                    </div>
                    <div class="new_left"  th:if="${!#lists.isEmpty(officeArea)}" style="border-bottom: 0;padding-bottom: 0">
                        <h1>面积找房</h1>
                        <a th:each="officeAreaItem,oIndex:${officeArea}" th:if="${officeAreaItem.id ne '' and oIndex.index lt 6}"  th:class="${oIndex.count % 2 == 0 || oIndex.last ?'': 'index_screen_line'}"  th:href="${'/scriptoriums/'+officeAreaItem.url}"  target="_blank" th:text="${officeAreaItem.name}"></a>
                        <div class="cl"></div>
                    </div>
                    <!--<div class="new_left" th:if="${!#lists.isEmpty(shopMemberType)}" style="border-bottom: 0; padding-bottom: 0">-->
                        <!--<h1>来源</h1>-->
                        <!--<a th:each="membertype,sIndex:${shopMemberType}" th:if="${membertype.id ne ''}"  th:href="${'/scriptoriums/'+ #strings.replace(membertype.url,'i','m') }" th:class="${sIndex.count % 2 != 0|| sIndex.last ? '':'index_screen_line'}" th:text="${#strings.isEmpty(membertype.name)?'':membertype.name}" target="_blank">50万以下</a>-->
                        <!--<div class="cl"></div>-->
                    <!--</div>-->
                </div>
                <div class="bztj_middle">
                    <div th:if="${!#lists.isEmpty(shophouse)}" th:each="house, i : ${shophouse}" th:id="${'con_spqh_' + i.count}" th:style="${i.count == 1? '':'display: none;'}" >
                        <a th:each="shophouse_1:${house}" th:href="${shophouse_1.url}" target="_blank">
                            <sub><img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></sub>
                            <img th:src="${shophouse_1.image}"  th:alt="${shophouse_1.projectName}" />
                            <!--<h4 class="bztj_title" th:text="${shophouse_1.projectName}"></h4>-->
                            <div class="esf_layout"><th:block th:text="${shophouse_1.projectName}"></th:block><span><th:block th:text="${shophouse_1.region}"></th:block></span></div>
                            <div class="houeseText gthx">
                                <span class="index_icon esf_ads_icon"></span>
<!--                                <th:block th:text="${shophouse_1.introduce}"></th:block>-->
                                <em th:text="${shophouse_1.introduce}"></em>
                                <i th:utext="${shophouse_1.price}"></i>
                            </div>
                        </a>
                    </div>

                </div>
                <div class="bztj_right xzl_right">
                    <div class="shopAdsSwiper" >
                        <div class="swiper-wrapper">
                            <div class="swiper-slide" th:if="${!#lists.isEmpty(focusHouse) and #strings.toString(focusShopItem.url).indexOf('http') ne -1}" th:each="focusShopItem:${focusHouse}">
                                <a  th:href="${#strings.isEmpty(focusShopItem.url)?'':focusShopItem.url}" target="_blank" class="ad_qh ">
                                    <img style="height:410px;" th:src="${#strings.isEmpty(focusShopItem.image)?'': focusShopItem.image}" th:alt="${#strings.isEmpty(focusShopItem.projectName)?'':focusShopItem.projectName}" />
                                </a>
                            </div>
                            <div class="swiper-slide" th:if="${!#lists.isEmpty(focusHouse) and #strings.toString(focusShopItem.url).indexOf('http') eq -1}" th:each="focusShopItem:${focusHouse}">
                                <img  style="height:410px;" th:src="${#strings.isEmpty(focusShopItem.image)?'': focusShopItem.image}" th:alt="${#strings.isEmpty(focusShopItem.projectName)?'':focusShopItem.projectName}" />
                            </div>
                        </div>
                        <div class="pagination SPXZL"></div>
                    </div>
                    <th:block th:if="${ !#lists.isEmpty(focusHouse) and #lists.size(focusHouse) gt 1}">
                        <script>
                            var myShopSwiper = new Swiper ('.shopAdsSwiper', {
                                direction: 'horizontal',
                                loop: true,
                                autoplay:5000,
                                autoplayDisableOnInteraction : false,
                                pagination: '.pagination',
                                paginationClickable: true
                            })



                        </script>
                    </th:block>
                </div>
                <div class="cl"></div>
            </div>
            <div class="cl"></div>
        <div class="fcphb index_block">
            <div class="index_title">
                <h1 class="index_h1 bnzf_h1">
                    <span>房产排行榜</span>
                    <a th:href="@{'/houses/'}" target="_blank"><i></i><label>楼盘搜索</label></a>
                </h1>
                <a href="/news/" target="_blank" class="pull-right index_title_right">
                    <span class="index_icon lskx_icon lou_wei"></span>
                    房产快讯
                </a>
            </div>
            <div class="rplp">
                <h3 th:text="${hotProjectTitle}"></h3>
                <ul th:if="${!#lists.isEmpty(hotProjects)}">

                    <li th:if="${!#lists.isEmpty(hotProjects)}"  th:each="hotTalkSubdistricts,status:${hotProjects}"><s th:text="${status.count}">1</s><p class="n1"><a th:href="${!#strings.isEmpty(hotTalkSubdistricts.ProjectId) and !#strings.isEmpty(hotTalkSubdistricts.projectType)? '/house/' +hotTalkSubdistricts.ProjectId+'-'+hotTalkSubdistricts.projectType+'.htm#xmpj':''}" target="_blank" th:text="${#strings.isEmpty(hotTalkSubdistricts.projectname)?'':hotTalkSubdistricts.projectname}">东亚·翰林世家</a></p>
                        <p class="n2">
                            <i th:text="${#strings.isEmpty(hotTalkSubdistricts.lowprice) or hotTalkSubdistricts.lowprice eq '0'?'待定':hotTalkSubdistricts.lowprice}">8000</i><th:block th:if="${!#strings.isEmpty(hotTalkSubdistricts.lowprice) and hotTalkSubdistricts.lowprice ne '0'}">元/m²</th:block>

                        </p>
                        <p class="n3" th:text="${#strings.isEmpty(hotTalkSubdistricts.number)?'':hotTalkSubdistricts.number+'条'}"></p>
                    </li>

                </ul>
            </div>
            <div class="rplp">
                <h3>热门楼盘</h3>
                <ul>
                    <li th:each="one,i:${rb.ranks.rank4}" th:if="${i.index lt 8}">
                        <s th:text="${i.count}"></s>
                        <p class="n1"><a th:href="${'/house/' + one.projectId + '-' + one.projectType + '.htm'}" target="_blank" th:text="${one.projectName}"></a></p>
                        <p class="n4">
                            <i th:text="${one.regionName}"></i>
                        </p>
                        <p class="n5" th:utext="${one.mPrice eq null ? '<i>待定</i>' :'<i>'+one.mPrice.priceMoney+'</i>元/㎡'}"></p>
                    </li>
                </ul>
            </div>
            <div class="rplp" style="margin-right: 0;">
                <h3>小二精选</h3>
                <ul  style="border: 0;">
                    <li th:each="one, i: ${rb.ranks.rank1}" th:if="${i.index lt 8}">
                        <s th:text="${i.count}"></s>
                        <p class="n1">
                            <a th:href="${'/house/' + one.projectId + '-' + one.projectType + '.htm'}" target="_blank"
                               th:text="${one.projectName}"></a>
                        </p>
                        <p class="n4">
                            <i th:text="${one.regionName}"></i>
                        </p>
                        <p class="n5" th:utext="${one.mPrice eq null ? '待定' : one.mPrice.priceMoney+'元/㎡<b>'+#strings.toString(one.mPrice.priceType).replace('价', '')+'</b>'}"></p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="cl"></div>
        <!--友情链接-->
        <div  class="link index_block"  th:include="fragment/fragment::link"></div>
        <!--底部广告图片-->
        <div class="cl"></div>
        <!--通栏广告5-->
        <div th:class="${#lists.size(normal.normal5) le 1 ? 'tlBanner':( nIndex.last? 'tlBanner middle tlBannerLast': 'tlBanner middle' ) }" style="margin-top: 6px" th:if="${!#lists.isEmpty(normal.normal5)}"  th:each="normalad,nIndex:${normal.normal5}">
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('广告') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png">
            </sup>
            <sup th:if="${#strings.toString(normalad.projectName).indexOf('活动') ne -1}">
                <img src="https://static.fangxiaoer.com/web/images/ico/sign/ard.png">
            </sup>
            <th:block th:if="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <a th:href="${normalad.url}" target="_blank">
                    <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
                </a>
            </th:block>
            <th:block th:unless="${!#strings.isEmpty(normalad.url) and #strings.toString(normalad.url).indexOf('http') ne -1}">
                <img th:src="${normalad.image}"th:alt="${normalad.projectName}">
            </th:block>
        </div>
        <div class="cl"></div>
        <!--弹屏广告-->
        <div class="popup" th:if="${pad}">
            <s class="popupClose"><img src="https://static.fangxiaoer.com/web/images/ico/sign/kpTc_07.png" alt=""></s>
            <a th:if="${pad}" th:href="${pad.url}" target="_blank">
                <img th:if="${pad}" th:src="${pad.image}" th:alt="${pad.projectName}">
            </a>
        </div>
        <script>
            $(".popupClose").click(function () {
                $(".popup").hide()
            })
            var username=document.cookie.split(";")[0].split("=")[1];
            //JS操作cookies方法!
            //写cookies
            if(getCookie("bullet")==1){
                $(".popup").hide()
            }else{
                setCookie("bullet","1","h2");
                $(".popup").show()
            }
            $(".popup").click(function(){
                $(".popup").hide()
            })
            function setCookie(name,value)
            {
                var Days = 30;
                var exp = new Date();
                exp.setTime(exp.getTime() + Days*24*60*60*1000);
                document.cookie = name + "="+ escape (value) + ";expires=" + exp.toGMTString();
            }
            function getCookie(name)
            {
                var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
                if(arr=document.cookie.match(reg))
                    return unescape(arr[2]);
                else
                    return null;
            }
            function setCookie(name,value,time)
            {
                var strsec = getsec(time);
                var exp = new Date();
                exp.setTime(exp.getTime() + strsec*1);
                document.cookie = name + "="+ escape (value) + ";expires=" + exp.toGMTString();
            }
            function getsec(str)
            {
                var str1=str.substring(1,str.length)*1;
                var str2=str.substring(0,1);
                if (str2=="s")
                {
                    return str1*1000;
                }
                else if (str2=="h")
                {
                    return str1*60*60*1000;
                }
                else if (str2=="d")
                {
                    return str1*24*60*60*1000;
                }
            }
        </script>
        <!-- End Alexa Certify Javascript -->
        <!--主页最底部-->

        <div th:include="fragment/fragment::tongji"></div>
        <!--右侧浮标-->
<!--        <div   class="suspensionIcon" style="display: none"  th:include="fragment/fragment::commonFloat" th:with="commonType=1"></div>-->
        <div   class="suspensionIcon" style="display: none"  th:include="fragment/fragment::newIndexFloat" th:with="commonType=1"></div>
        <!--背景banner大广告-->
        <script>
            if($(".advertising").length == 0 && $(".bg-banner").length != 0){
                $(".w1170").css("margin-top","105px");
                $(".bg-banner").css("background-position","center top");
            }
            if($(".bg-banner").length == 0 && $(".advertising").length != 0){
                $(".w1170").css("margin-top","100px");
                $(".bg-banner").css("background-position","center top");
            }
            if($(".advertising").length == 0 && $(".bg-banner").length == 0){
                $(".w1170").css("margin-top","55px");
                $(".bg-banner").css("background-position","center top");
            }
            if($(".advertising").length != 0 && $(".bg-banner").length != 0){
                $(".w1170").css("margin-top","150px");
                $(".bg-banner").css("background-position","center 45px");
            }

            $(".close_fastival").click(function(){
                $(".bg-banner").css("background","#fff");
                $(this).hide();
                $(".bg-bannerK").hide();
                $(".bg-bannerK a").hide();
                if($(".advertising").length == 0){
                    $(".w1170").css("margin-top","55px");
                }else {
                    $(".w1170").css("margin-top","100px");
                }
            })

            var avatar = $(".bg-banner").css("backgroundImage");
            if(avatar !="" && avatar !=undefined){
                avatar = avatar.split("\"")[1];
            }else {
                $(".bg-bannerK").hide();
                $(".bg-bannerK a").hide();
            }
        </script>
        <!--弹屏广告-->
        <script type="text/javascript">
            var ary = location.href.split("&");
            jQuery(".picScroll-left").slide({ titCell: ".hd ul", mainCell: ".bd ul", autoPage: true, effect: "leftLoop", autoPlay: true, scroll: 1, vis: 5 });
        </script>
        <script>
            $(function () {
                $(".bnzf_right1 li").each(function () {
                    if ($(this).height() > 56) {
                        $(this).css({ height: "56px", overflow: "hidden" })
                        $(this).append("<i></i>")
                    } else if ($(this).height() < 40) {
                        $(this).height(56)
                    }
                })

                $(".bnzf_right1 li p,.bnzf_right1 li span,.bnzf_right3 li p,.bnzf_right3 li span,.bnzf_right1 li img").each(function () {
                    var xx = $(this).html();
                    $(this).replaceWith(xx);
                })


                $(".bnzf_qh ul").append($(".bnzf_qh ul").html()).css("top", -1065)
                var lih = $(".bnzf_qh li").height() + 15;
                var hhh = -1065
                var hhh1 = hhh
                var dd = setInterval(gd, 3000);

                function gd() {
                    if (hhh != 0) {
                        hhh = hhh + lih
                        $(".bnzf_qh ul").animate({ top: hhh })
                    } else {
                        $(".bnzf_qh ul").css("top", -$(".bnzf_qh ul").height() / 2)
                        hhh = hhh1 + lih
                        $(".bnzf_qh ul").animate({ top: hhh })
                    }
                }
                $(".bnzf_qh").mouseleave(function () {
                    dd = setInterval(gd, 3000);
                });
                $(".bnzf_qh").mouseenter(function () {
                    clearInterval(dd);
                });

                //右侧漂浮事件
                $(window).scroll(function() {
                    xianshi()
                });

            })
            $("#con_search_2").hide();
            $(".tc_main").click(function(){
                $(".tc_main,.bg_tc").hide();
            })
            function xianshi() {
                if ($(document).scrollTop() > 300) {
                    $(".suspensionIcon").fadeIn()
                } else {
                    event.stopPropagation();
                    $(".suspensionIcon").fadeOut()
                }
            }
            function setCookie(cname, cvalue, exdays) {
                var d = new Date();
                d.setTime(d.getTime() + (exdays * 1 * 60 * 60 * 1000));
                var expires = "expires=" + d.toGMTString();
                document.cookie = cname + "=" + cvalue + "; " + expires;
            }
            function getCookie(cname) {
                var name = cname + "=";
                var ca = document.cookie.split(';');
                for (var i = 0; i < ca.length; i++) {
                    var c = ca[i].trim();
                    if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
                }
                return "";
            }
            function checkCookie() {
                var user = getCookie("username");
                if (user != "fangxiaoer20160903") {
                    user = "fangxiaoer20160903";
                    //方法
                    $(".tc_main,.bg_tc").show();
                    if (user != "" && user != null) {
                        setCookie("username", user, 1);
                    }
                }
            }
            $(".my_xl_list").hover(function(){
                    $(this).show()},
                function(){
                    $(this).hide()
                })
            $(".my_xl").hover(function(){},function(){
                $(this).find(".my_xl_list").hide()
            })
            $(function(){
                $(".pull-left li").removeAttr("style");
            })
        </script>
        <script>
            $(function () {
                $(".fxe_banner ul").css("margin-left", Math.floor($(".fxe_banner ul").width() * -0.5) + "px");
                var xxx_ooo = $(".wei .swiper-slide").length
                if (xxx_ooo > 1) {
                    var xxx_wei = new Swiper('.wei', {
                        direction: 'horizontal',
                        loop: true,
                        autoplay: 5000,
                        autoplayDisableOnInteraction: false,
                        pagination: '.pagination',
                        paginationClickable: true
                    })
                    $(".wei .swiper-pagination-bullet").each(function () {
                        $(this).hover(function() {
                            $(this).click(); //鼠标划上去之后，自动触发点击事件来模仿鼠标划上去的事件
                        });
                    })
                }

                var xxx_ggg = $(".weu .swiper-slide").length
                if (xxx_ggg > 1) {
                    var xxx_weu = new Swiper('.weu', {
                        direction: 'horizontal',
                        loop: true,
                        autoplay: 5000,
                        autoplayDisableOnInteraction: false,
                        pagination: '.pagination',
                        paginationClickable: true
                    })
                    $(".weu .swiper-pagination-bullet").each(function () {
                        $(this).hover(function() {
                            $(this).click(); //鼠标划上去之后，自动触发点击事件来模仿鼠标划上去的事件
                        });
                    })
                }
            })
        </script>


        <script src="https://static.fangxiaoer.com/sy/ai/vue3.js"></script>
        <script src="https://static.fangxiaoer.com/sy/ai/marked.js"></script>
        <script src="https://static.fangxiaoer.com/sy/ai/crypto-js.min.js"></script>
        <script src="https://static.fangxiaoer.com/sy/ai/ai.js"></script>

    </div>
    </div>
    <div style="position: absolute; z-index: 999;width: 100%;">
<!--        <div th:include="fragment/fragment::footer_list"></div>-->
        <div th:include="fragment/fragment::new_footer"></div>
        <div class="quanwei" th:include="fragment/fragment::footerAuthority" style="margin-top: 15px; background-color: #fff; padding-top: 14px;  padding-bottom: 11px;"></div>
    </div>
    <script>
        if($(".bztj_right .Headline .show .lunbo a").length>1){
            lunbo()
        }
        function lunbo() {
            setInterval(
                function () {
                  $(".bztj_right .Headline .show .lunbo").animate({top:"-70px"},500,function () {
                      $(".bztj_right .Headline .show .lunbo a:last").after($(".bztj_right .Headline .show .lunbo a:first"))
                      $(".bztj_right .Headline .show .lunbo").css("top",0)
                  })
                },5000
            )
        }
    </script>
    <!--热力图处理 -->
    <!--<script type="text/javascript" src="http://clickheat.fangxiaoer.com/js/clickheat.js"></script>
    <noscript><p><a href=" ">ClickHeat</a ></p ></noscript>
    <script type="text/javascript">&lt;!&ndash;
    clickHeatSite = 'sy.fangxiaoer.com';clickHeatGroup = 'index';clickHeatServer = 'http://clickheat.fangxiaoer.com/click.php';initClickHeat(); //&ndash;&gt;
    </script>-->
        <!---->
</div>
</body>
</html>
