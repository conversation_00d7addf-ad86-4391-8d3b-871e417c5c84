<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>沈阳抵押贷款，沈阳贷款服务 - 房小二网</title>
    <meta name="keywords" content="抵押贷款,按揭贷款,信用贷款,购房贷款,购车贷款,房贷车贷,沈阳买房贷款,沈阳信用贷款,沈阳贷款服务,二手房过户代办"/>
    <meta name="description" content="沈阳买房贷款是房小二网推出的一项买房贷款服务，提供个人消费贷款、购房按揭贷款、二手房贷款、无抵押贷款、装修贷款、购车贷款等贷款类别，旨在帮助您解决买房和其他资金问题，助您实现安家的梦想。">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/other/loan/css.css" rel="stylesheet" type="text/css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="/js/loanForm.js" ></script>
</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="subNavIndex=1"></div>
<div class="top_head" th:include ="fragment/fragment::loanHead"  th:with="count=4"></div>
<div class="cl"></div>
<div class="banner"  style="background:url(https://static.fangxiaoer.com/web/images/sy/other/loan/banner02.jpg) center;">
    <div class="loan">
        <div class="loanForm">
            <div id="from1" class="from">
                <div>
                    <div style="margin: 0;background: url(https://static.fangxiaoer.com/web/images/sy/other/loan/loan-num1.jpg) no-repeat;">输入您的电话号</div>
                    <div style="background: url(https://static.fangxiaoer.com/web/images/sy/other/loan/loan-num2.jpg) no-repeat;">填写贷款申请</div>
                </div>
                <ul>
                    <li>
                        <span>手机号码</span>
                        <input type="tel" name="phone" id="phone" value="" class="fxe_mobile" maxlength="11" onblur="confirm.mobile();" onkeyup="this.value=this.value.replace(/\D/g,'')" onfocus="confirm.clearMobile();" placeholder="请输入您的手机号码" />
                        <div class="erro fxe_mobile_info">&nbsp;</div>
                    </li>
                    <li>
                        <span>动态密码</span>
                        <input type="text" id="code" onblur="" onkeyup="this.value=this.value.replace(/\D/g,'')" maxlength="6" onfocus="confirm.clearCode();" name="code"  class="fxe_messageCode" style="width: 99px;" placeholder="请输入验证码" />
                        <b onclick="confirm.yzmZT();">获取验证码</b>
                        <div class="erro fxe_messageCode_info">&nbsp;</div>
                    </li>
                    <li>
                        <div class="submit" onclick="confirmNext();">下一步</div>
                    </li>
                </ul>
            </div>
            <div id="from2" class="from" style="padding-top: 44px;">
                <ul>
                    <li>
                        <span>您的姓名</span>
                        <input type="text" name="name" id="name" value="" placeholder="请输入您的姓名" />
                        <div class="erro fxe_name">&nbsp;</div>
                    </li>
                    <li class="single">
                        <span>您的性别</span>
                        <input type="radio" name="xb" placeholder="请输入验证码" class="check" checked="checked" style="margin-left: 0;" value="0"/>先生
                        <input type="radio" name="xb" placeholder="请输入验证码" class="check" style="margin-left: 46px;" value="1"/>女士</br>
                        <div class="erro">&nbsp;</div>
                    </li>
                    <li>
                        <span>贷款金额</span>
                        <input type="text" id="money" name="money" maxlength="4" onkeyup="this.value=this.value.replace(/\D/g,'')" placeholder="贷款金额5万起" />
                        <p>万元</p>
                        <div class="erro fxe_Money">&nbsp;</div>
                    </li>
                    <li class="single">
                        <div class="erro">&nbsp;</div>
                    </li>
                    <li>
                        <div class="submit"  onclick="submitapply()">提交申请</div>
                    </li>
                </ul>
            </div>
            <div id="from3" class="fromState">
                <img src="https://static.fangxiaoer.com/web/images/sy/other/loan/loan-succeed.jpg"/></br>
                <p>恭喜您申请成功！</p></br>
                <span>24小时内客服专员与您沟通</span>
            </div>
            <div id="from4" class="fromState">
                <img src="https://static.fangxiaoer.com/web/images/sy/other/loan/loan-failed.jpg"/></br>
                <p>您已经申请过抵押贷！</p></br>
                <span>我们的客服会尽快与您沟通，请您耐心等待</br>请您保持通话畅通</span>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">$(".single input").click(function () {
    $(this).parent().find("input").removeAttr("checked")
    $(this).attr("checked", "checked");
})</script>
<div class="title"><img src="https://static.fangxiaoer.com/web/images/sy/other/loan/title.gif" alt="申请流程"></div>
<div class="liucheng"><img src="https://static.fangxiaoer.com/web/images/sy/other/loan/liucheng.jpg" alt="流程"></div>
<div class="manzeshengming"><p>免责声明：</p>本站旨在为广大用户提供更多信息服务，不声明或保证所提供信息的准确性和完整性。页面所载内容及数据仅供用户参考和借鉴，最终以开发商实际公示为准，用户因参照本站信息进行相关交易所造成的任何后果与本站无关。您可以投诉或拨打举报电话：400-893-9709</div>
<div class="cl"></div>
<h1 class="h1">合作机构</h1>
<div class="hzjg">
    <ul>
        <li><img src="https://static.fangxiaoer.com/web/images/sy/other/loan/login_jsyh.jpg" alt="建设银行"></li>
        <li><img src="https://static.fangxiaoer.com/web/images/sy/other/loan/login_gdyh.jpg" alt="光大银行"></li>
        <li><img src="https://static.fangxiaoer.com/web/images/sy/other/loan/login_gsyh.jpg" alt="工商银行"></li>
        <li><img src="https://static.fangxiaoer.com/web/images/sy/other/loan/login_zgyh.jpg" alt="中国银行"></li>
    </ul>
</div>
<div class="cl"></div>
<!--页面底部-->
<div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include ="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
</body>
</html>
<script type="text/javascript">
    function submitapply() {
        if (conf(5, 1000)) {
            var phone = $("#phone").val();
            var username = $("#name").val();
            var sex = $('input[name="xb"]:checked').val();
            var range = "7"; // $('input[name="fw"]:checked').val();
            var price = $("#money").val();
            var type = "3";
            var code = $("#code").val();
            $.ajax({
                type: "POST",
                data: { phone: phone, username: username, sex: sex, price: price, range: range, type: type,code:code },
                url: "/saveLoan",
                dataType: "json",
                success: function(json) {
                    if (json.status == "1") {
                        $("#from2").hide();
                        $("#from3").show();
                    } else {
                        $("#from2").hide();
                        $("#from4").show();
                    }
                }
            });
        }
    }
</script>
