<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳购房顾问_沈阳买房专家_沈阳帮你找房 - 房小二网</title>
    <meta name="keywords" content="沈阳帮您找房,沈阳买房专家,沈阳楼盘分析,沈阳买房指导,沈阳购房顾问"/>
    <meta name="description" content="房小二网帮您找房，只需告诉我您的需求，就能为您定制选房报告。关注楼盘，随时随地了解楼盘动态。一个预约专职司机，全程看房接送。楼盘评测，为您提供专业的楼盘评测服务。"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="mobile-agent" th:if="${helpId == 1}" content="format=html5;url=https://m.fangxiaoer.com/normalnew.htm">
    <meta name="mobile-agent" th:if="${helpId == 2}" content="format=html5;url=https://m.fangxiaoer.com/normalsale.htm">
    <meta name="mobile-agent" th:if="${helpId == 3}" content="format=html5;url=https://m.fangxiaoer.com/normalRent.htm">
    <meta name="mobile-agent" th:if="${helpId == 4}" content="format=html5;url=https://m.fangxiaoer.com/normalShop.htm">
    <meta name="mobile-agent" th:if="${helpId == 5}" content="format=html5;url=https://m.fangxiaoer.com/kfztc.htm">
    <meta name="mobile-agent" th:if="${helpId == 6}" content="format=html5;url=https://m.fangxiaoer.com/ghdb.htm">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <!--    <link href="https://static.fangxiaoer.com/web/styles/sy/formulate/house_newQzqg.css" rel="Stylesheet" type="text/css"/>-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/buyhouse/house_newQzqg.css" rel="Stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/formulate/jquery.datetimepicker.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/buyhouse/help_decoration.css"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript"></script>
    <!--    <script type="text/javascript" src="/js/helpsearch.js" ></script>-->
    <script type="text/javascript" src="https://static.fangxiaoer.com/web/styles/sy/buyhouse/help.js" ></script>
    <script>
        (function(b,a,e,h,f,c,g,s){b[h]=b[h]||function(){(b[h].c=b[h].c||[]).push(arguments)};
            b[h].s=!!c;g=a.getElementsByTagName(e)[0];s=a.createElement(e);
            s.src="//s.union.360.cn/"+f+".js";s.defer=!0;s.async=!0;g.parentNode.insertBefore(s,g)
        })(window,document,"script","_qha",228056,false);
    </script>
    <script th:inline="javascript">
        var s = [[${session.muser}]];
        var sid = [[${session.sessionId}]]
    </script>
</head>

<body>


<!--引入头部导航栏-->
<!--<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=${ids+1}"></div>-->
<div id="head2017" th:include = "fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
<!--搜索-->
<!--<div id="search2017"  th:include = "fragment/fragment::searchNav" th:with="type=1"></div>-->
<input type="hidden" id="ly" th:value="${#strings.isEmpty(ly)?'':'来源：'+ly}"/>

<!--<div class="topbanner"></div>-->
<div class="g_setbanner">
    <div class="g_stit">让买房更简单</div>
    <div class="gtap">
        <div class="gu">
            <a class="gi" th:href="@{'/helpSearch?ids=1'}">买新房</a>
        </div>
        <div class="gu">
            <a class="gi" th:href="@{'/helpSearch?ids=2'}">买二手房</a>
        </div>
        <div class="gu">
            <a class="gi" th:href="@{'/helpSearch?ids=4'}">买商业</a>
        </div>
        <div class="gu">
            <a class="gi giv" th:href="@{'/helpSearch?ids=6'}">装修</a>
        </div>
    </div>
</div>
<div class="ghelp setwidth">
    <div class="gh3">
        <h3>填写装修表单</h3>
<!--        <p>已有 <i>8848989</i> 户申请成功</p>-->
    </div>
    <div class="gmain">
        <div class="gml" style="width: 496px;">

            <div class="gmli">
                <div class="xti xztext">选择区域:</div>
                <div class="gmp2 fx1">
                    <div class="areas areaclick">
                        <h4>请选择</h4><i></i>
                        <div class="areaList areadata">
                            <span>不限</span>
                        </div>
                    </div>
                    <div class="selects">
                        <div class="seli"><i></i>新房</div>
                        <div class="seli"><i></i>二手房</div>
                    </div>
                </div>
            </div>
            <div class="gmli">
                <div class="xti">小区名称:</div>
                <div class="gmp2 fx1">
                    <input type="text" class="arcss aname" placeholder="请输入" style="margin-right: 24px;">
                    <span style="line-height: 35px;">房屋面积:</span>
                    <input type="number" oninput="limitInputLength(this, 5)" class="arcss aread" placeholder="请输入">
                </div>
            </div>
            <div class="gmli">
                <div class="xti">户型结构:</div>
                <div class="gmp2 fx1">
                    <div class="areas layoutClick" style="width: 158px;margin-right: 24px;">
                        <h4>请选择</h4><i></i>
                        <div class="areaList layoutData" style="width: 158px;"></div>
                    </div>
                    <span style="line-height: 35px;">装修预算:</span>
                    <div class="areas ysclick" style="width: 158px;">
                        <h4>请选择</h4><i></i>
                        <div class="areaList ysData">
                            <span>2万以下</span>
                            <span>2万~5万</span>
                            <span>5万~10万</span>
                            <span>10万~20万</span>
                            <span>20万~50万</span>
                            <span>50万以上</span>
                            <span>面议</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="gmli">
                <div class="xti">您的称呼:</div>
                <div class="gmp2 fx1">
                    <input type="text" class="arcss usenames" placeholder="请输入">
                    <div >
                        <span style="line-height: 35px;">手机号码:</span>
                        <input type="number" oninput="limitInputLength(this, 11)" class="arcss arphone" placeholder="请输入" id="mval">
                    </div>
                </div>
            </div>
            <div class="gmli">
                <div class="xti"><span class="w3">验证码</span>:</div>
                <div class="gmp2 fx1">
                    <input type="number" oninput="limitInputLength(this, 6)" class="arcss arcode" placeholder="请输入" id="acode">
                    <div class="getcode" id="getCode">获取验证码</div>
                </div>
            </div>

            <div class="gmli">
                <div class="xti"><span>装修需求</span>:</div>
                <div class="gmp2">
                    <textarea id="text" placeholder="请输入" rows="5" class="textateaBg"></textarea>
<!--                    <div id="viewBox">0/100</div>-->
                </div>
            </div>
            <!--<div class="checkagreeInput" style="margin:13px 0px 14px 0px !important;">
                <i id="checkagree" class="checkimg cheimg5"></i><div>我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《用户服务协议》</a>及
                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《隐私政策》</a></div>
            </div>-->
            <div class="bt1 map55" ty="6">填写完毕，立即申请</div>
        </div>
        <div class="gmr">
            <img alt="" src="https://static.fangxiaoer.com/web/styles/sy/buyhouse/infos.png" class="decorationInfo"/>
        </div>
    </div>
</div>


<!--弹窗-->
<div class="iph">
    <div class="ite">
        <div class="closex"></div>
        <div class="ith">请输入您的手机号方便我们联系您</div>
        <div class="dias"></div>
<!--        <div class="iip"><input placeholder="请输入手机号码" type="text" id="mval" /></div>-->
<!--        <div class="iip2"><input placeholder="请输入验证码" type="text" id="acode" /><em id="mcode">获取验证码</em></div>-->
        <div class="bt2 sh map11">提交</div>
    </div>
</div>
<!--提交成功-->
<div class="success_bg su"></div>
<div class="success_tt su">
    <div class="suma">
        <div class="sutii">
            <span>您的需求已提交，稍后会有工作人员与您联系，请保持手机畅通！</span>
            <!--            <span>工作人员联系您。</span>-->
            <!--<span>您的要求已提交！请等待工作人员联系您。</span>
            <span>您可以在房小二网APP上看到已提交的找房需求</span>-->
        </div>
        <div class="bt2 sh btst map12" style="margin-top: 39px">好的</div>
    </div>
</div>
<!--提示-->
<div class="success_bg dia"></div>
<div class="success_tt dia">
    <div class="suma">
        <div class="sutii dlog">
            <span>您的要求已提交！请等待工作人员联系您。</span>
            <span>您可以在房小二网APP上看到已提交的找房需求</span>
        </div>
        <div class="bt2 sh btst map13" style="margin-top: 39px">好的</div>
    </div>
</div>


<script type="text/javascript" src="https://static.fangxiaoer.com/js/my_xiala.js"></script>
<script src="https://static.fangxiaoer.com/js/jquery.datetimepicker.js"></script>
<script>

    $('#datetimepicker_mask').datetimepicker({
        //    mask: '9999/19/39 29:59'
    });

</script>

<script>
    $(".cheimg5").click(function () {
        if($(this).hasClass("checked")){
            $(this).removeClass("checked")
        }else{
            $(this).addClass("checked")
        }
    })
    $(function () {
        //取值显示
        var url = window.location.href;
        var r = "0";
        if (url.indexOf("ids=") > 0) {
            var ly = url.indexOf("&ly")
            if (ly == "-1") {
                ly = url.length
            } else {
                var lys = url.substring(url.indexOf("ly=") + 3, url.length)
                r="1"
            }
            var url_id = url.substring(url.indexOf("ids=") + 4, ly);
            if (url_id == "7") { url_id="1"}
            $(".form_qh li a").each(function () {
                var href = $(this).attr("href")
                href = href.substring(href.indexOf("ids=") + 4, ly)
                if (href == url_id) { $(this).addClass("hover") }
            })
            $(".ids_" + url_id).show()
        } else {
            $(".form_qh li a").eq(0).addClass("hover")
            $(".ids_1").show()
        }

        if (r=="1") {
            $(".form_qh li").each(function () {
                $(this).find("a").attr("href", $(this).find("a").attr("href") + "&ly=" + lys)
            })
        }

        //商业
        $(".syfs li").click(function () {
            if ($(this).index() == "0") {
                $(".form_l span").html("您的价格预算")
                $(".sydw").html("万元")
            } else {
                $(".form_l span").html("您的租金预算")
                $(".sydw").html("万元/年")
            }
        })


        //划出下拉 关闭下拉
        $(".my_xl").hover(function(){},function(){
            $(this).find(".my_xl_list").hide()
        })
        //电话判断
        $(".title p").html("在线值班时间：周一至周六时间是8:00-21:00，周日9:00-21:00　客服热线：400-893-9709")
    })
</script>




<div class="cl"></div>
</div>
<!--<div class="bnzf-kuangBottom">-->
<!--<div><img src="https://static.fangxiaoer.com/web/images/sy/comment/bnzf-kuangBottom.jpg" alt=""></div>-->
<!--</div>-->
<div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::newHouseFloat"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
</html>