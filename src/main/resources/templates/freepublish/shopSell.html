<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳商铺信息发布_免费信息发布_个人房源 - 房小二网</title>
    <meta name="keywords" content="二手房信息发布,个人房源,沈阳二手房信息,二手房中介,免费信息发布"/>
    <meta name="description" content="房小二网为您提供专业贴心的二手房服务，为您提供免费二手房信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/pubShop1.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/issue/form1.css?t=20220902" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.validate.min1.js"></script>
    <script type="text/javascript" src="/js/freepublish/personal1.js"></script>
    <script type="text/javascript" src="/js/AjaxforJquery.js"></script>
    <script type="text/javascript" src="/js/jquery.autocomplete.js"></script>

    <!--[if IE 6]>
    <style type="text/css">
        input{display:inline !important}
    </style>
    <![endif]-->
    <style >
        .diyCancel { bottom: 1px !important; }
        .successMsgTc { }
        #p-text1 { display: block !important; font-size: 18px !important; margin-top: 70px !important; font-size: 17px !important; line-height: 28px !important; font-weight: normal !important; }
        #p-text2 { display: block !important; font-size: 18px !important; font-size: 17px !important; line-height: 28px !important; font-weight: normal !important; margin-top: -5px !important; }
        #p-text3 { /*display: block !important;*/
            font-size: 18px !important; color: red !important; font-size: 17px !important; line-height: 28px !important; }
        .closeImg { width: 19px !important; height: 19px !important; position: absolute !important; right: 12px !important; top: 12px !important; }
        .successMsgTcBtn { display: block !important; width: 150px !important; height: 40px !important; background-color: #ff5200 !important; font-size: 16px !important; text-align: center !important; margin: 0 auto !important; line-height: 40px !important; margin-top: 36px !important; text-decoration: none !important; cursor: pointer !important; color: #fff !important; font-weight: normal !important; border-radius: 0px !important; }
        #head2017 { position: absolute;width: 100%;left: 0;top: 0;z-index: 9999999999; }
    </style>
    <script>
        $(function () {
            if($("#Price").val()==0){
                $("#Price").val("面议")
            }

            $(".my_xl").each(function () {
                if ($(this).find("input").val() == "0" && $(this).find("li").val() != "0") {
                    $(this).find(".my_xl_txt").html("请选择");
                    $(this).find("input").val("");

                } else
                    for (i = 0; i < $(this).find("li").length; i++) {
                        if ($(this).find("li").eq(i).val() == $(this).find("input").val()) {
                            $(this).find(".my_xl_txt").html($(this).find("li").eq(i).html());
                        }
                    }
            });
            $(".sh_btn").click(function () {
                if ($(".showhide").hasClass("hid")) {
                    $(".showhide").slideDown();
                    $(".showhide").removeClass("hid");
                    $(".icon").addClass("bg_ico");
                } else {
                    $(".showhide").slideUp();
                    $(".showhide").addClass("hid");
                    $(".icon").removeClass("bg_ico");
                }
            })
            $('.huxing input').bind('input propertychange', function () {
                if ($(this).val().length >= 1) {
                    $(this).parent().next().find("input").focus();
                }
            });
            $(".floor").find("input").keyup(function () {
                if ($(this).val().length == 2) {
                    $(this).parent().next().find("input").focus();
                }
            })

            $(".zujin").bind({
                click: function () {
                    if ($(this).val() == "面议") {
                        $(this).val("");
                    }
                },
                blur: function () {
                    if ($(this).val() == "") {
                        $(this).val("面议");
                    }
                }
            })

            //// 房屋配置
            //$(".fwpz a").click(function () {
            //    $("#fwpz_yz").show();
            //    if ($(this).attr("class") == "focus") {
            //        $(this).removeClass("focus");
            //        $(".qx").html("全选")
            //    } else {
            //        $(this).addClass("focus");
            //    }

            //})
            // 房屋配置
            $(".fwpz a").click(function () {
                $("#fwpz_yz").show();
                if ($(this).html() == "无") {
                    if ($(this).attr("class") == "focus") {
                        $(this).removeClass("focus");
                    } else {
                        $(".fwpz a").removeClass("focus");
                        $(this).addClass("focus");
                    }
                } else {
                    if ($(this).attr("class") == "focus") {
                        $(this).removeClass("focus");

                    } else {
                        $(this).addClass("focus");
                        $(".fwpz a:last").removeClass("focus");
                    }

                }
            })

            $(".qx").click(function () {
                if ($(this).html() == "全选") {
                    $(this).html("取消");
                    $(".fwpz a").addClass("focus");
                } else {
                    $(this).html("全选");
                    $(".fwpz a").removeClass("focus");
                }
            })

            //提交执行
            $(".submitBtnFroms").click(function () {
                $("#MIndustry").val("");
                for (i = 0; i < $(".fwpz a").length; i++) {
                    if ($(".fwpz a:eq(" + i + ")").hasClass("focus")) {
                        $("#MIndustry").val($("#MIndustry").val() + "," + $(".fwpz a:eq(" + i + ")").attr("rel"))
                    }
                }
                $("#HouseTrait").val("");
                for (i = 0; i < $(".fyts a").length; i++) {
                    if ($(".fyts a:eq(" + i + ")").hasClass("focus") && $("#HouseTrait").val().split(",").length <= 6) {
                        $("#HouseTrait").val($("#HouseTrait").val() + "," + $(".fyts a:eq(" + i + ")").attr("rel"));
                    }
                }
            })

            //非必选对号
            $(".fbx li").click(function () {
                $(this).parent().parent().parent().find("i").show();
            })
            $(".Regiondiv").find("li").click(function () {
                BindPlate(this);
            })
            //获取板块
            function BindPlate(obj) {
                var RegionID = $(obj).val();
                var ul = $(".Platediv").find("ul");
                var div = $(".Platediv").find(".my_xl_txt");
                ul.html("");
                $.ajax({
                    type: "POST",
                    url: "/getPlates",
                    data: { RegionID: RegionID },
                    success: function (data) {
                        var cs = data;
//                        var resdata = JSON.parse(data);
                        $("#PlateID").val("");
                        div.html("请选择板块");
                        for (var i = 0; i < data.length; i++) {
                            ul.append('<li value="' + data[i].id + '">' + data[i].name + '</li>');
                        }
                    }

                });
            }
        })
    </script>
    <script type="text/javascript" src="/js/freepublish/form.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>

</head>
<body>

<script src="https://static.fangxiaoer.com/js/jquery.autocomplete.js" type="text/javascript"></script>
<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<div class="title">
    为您提供专业贴心的商铺租售服务<p>免费咨询电话：400-893-9709</p>
</div>

<!--二手房发布成功提示页面优化-->
<!--<div class="successMsgTc" >
    <img class="closeImg" src="https://static.fangxiaoer.com/web/images/my/admin/yun_close.gif" alt="">
    <p id="p-text1">房源信息发布成功，稍后会有工作人员跟你联系</p>
    <p id="p-text2">如有疑问请咨询电话：<span id="p-text3">400-893-9709</span></p>

    <div class="successMsgTcBtn">确定</div>
</div>-->
<div class="successMsgTc">
    <div class="success_m">
        <div class="closeSml"></div>
        <div class="ics"></div>
        <div class="sumsg">房源基本信息发布成功</div>
        <div class="smo"><span>房小二网客服正在核实基本消息</span><span>继续认证真房源可加速出售</span></div>
        <div class="chbtn verf">继续认证</div>
        <div class="rnbtn" onclick="window.open('https://my.fangxiaoer.com/house/houses0_3')">管理房产</div>
        <div class="multiple"><i class="left0"></i>多个认证途径 <i class="right0"></i></div>
        <div class="muft">排序优先<i>·</i>增加曝光<i>·</i>独有标识</div>
    </div>
</div>
<div class="successMsgFull"></div>

<div class="kuang">
    <!--表单-->
    <div class="sale_form_r qh_btn"  style="width: 1270px;margin: 0 auto;float: none;" th:include="fragment/freepublish::publishType" th:with="currType=3">
    </div>
    <div class="sale_form">
        <form name="jsUpForm" method="post"
              th:action="@{/saveShopSell}" th:object="${shopEntity}"  id="jsUpForm" class="bbb">
            <script type="text/javascript">
                //<![CDATA[
                var theForm = document.forms['jsUpForm'];
                if (!theForm) {
                    theForm = document.jsUpForm;
                }
                function __doPostBack(eventTarget, eventArgument) {
                    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                        theForm.__EVENTTARGET.value = eventTarget;
                        theForm.__EVENTARGUMENT.value = eventArgument;
                        theForm.submit();
                    }
                }
                //]]>
            </script>


            <div class="cl"></div>
            <div class="sale_form_l">房源类别</div>
            <div class="sale_form_r">
                <ul>
                    <li style="margin-bottom: 20px;">
                        <p><span>*</span>供求方式</p>
                        <div class="rs">

                            <a href="/shopsell" class="hover">商铺出售</a>
                            <a href="/shoprent" class="">出租转租</a>
                            <a href="/shoptransfer" class="">出兑转让</a>
                            <input type="hidden" name="shoptype" value="1"/>
                        </div>
                        <div class="cl"></div>
                    </li>
                </ul>

            </div>

            <input type="hidden"  id="houseid" th:field="*{houseid}"/>
            <div class="sale_form_l">商业基础信息</div>
            <div class="sale_form_r">
                <ul>
                    <li>
                        <p><span>*</span>标题</p>
                        <div class="pr">
                            <input th:field="*{title}" type="text" id="Title" />
                        </div>
                        <i id="Title123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                    <li class="xl_yz">
                        <p><span>*</span>区域板块</p>
                        <div class="my_xl Regiondiv" style="z-index: 98">
                            <input th:field="*{regionid}" type="text" id="RegionID" class="my_xl_input" />
                            <div class="my_xl_txt gckt">请选择区域</div>
                            <div class="my_xl_btn"></div>
                            <ul class="my_xl_list">

                                <li th:each="regions:${region}" th:value="${regions.id}" th:text="${regions.name}"></li>

                            </ul>
                        </div>
                        <div class="my_xl Platediv" style="z-index: 98">
                            <input th:field="*{plateid}" type="text" id="PlateID" class="my_xl_input" />
                            <div class="my_xl_txt gckt">请选择板块</div>
                            <div class="my_xl_btn"></div>
                            <ul class="my_xl_list">
                                <li th:each="plate:${plates}" th:value="${plate.id}" th:text="${plate.name}"></li>
                            </ul>
                        </div>
                        <i id="HouseTrait123" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox" style="visibility: hidden"></div>
                    </li>
                    <li>
                        <p>地址</p>

                        <div class="pr">
                            <input th:field="*{address}" type="text" id="Address"  /><!--name="Address"-->
                        </div>
                        <i id="I1" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                    <li>
                        <p><span>*</span>类型</p>
                        <div class="pr rs lx">

                            <a th:each="type:${type}" th:rel="${type.id}" th:text="${type.name}" class="gckt"></a>


                            <input th:field="*{shopcategories}" type="text" id="ShopCategories" class="my_xl_input" />
                        </div>
                        <i id="ShopCategories123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                    <li class="w130">
                        <p>商铺配套</p>
                        <div class="pr fwpz">

                            <a th:each="industry:${industry}" th:rel="${industry.id}" th:text="${industry.name}" class="">酒店餐饮</a>


                            <input th:field="*{mindustry}" type="text" id="MIndustry" class="tese_input" />
                        </div>
                        <div class="cl"></div>

                        <i id="fwpz_yz" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>
                    <li>
                        <p>经营历史</p>
                        <div class="pr">
                            <input th:field="*{mhistory}" type="text" id="MHistory" />
                        </div>
                        <i id="I2" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130">
                        <p><span>*</span>售价</p>
                        <div class="pr" style="float: left">
                            <input th:field="*{price}" type="text" id="Price"  onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"/><span class="right">万元</span>
                        </div>
                        <i id="RentPrice123" class="ljz"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>
                    <li class="w130">
                        <p><span>*</span>面积</p>
                        <div class="pr">
                            <input th:field="*{truearea}" type="text" maxlength="6" id="TrueArea" class="mianji" /><span class="right">m²</span>
                        </div>
                        <i id="BuildArea123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130">
                        <p>房源特色</p>
                        <div class="pr fyts">

                            <a th:each="trait:${trait}" th:rel="${trait.id}" th:text="${trait.name}" class=""></a>

                            <input th:field="*{housetrait}" type="text" id="HouseTrait" class="tese_input" />
                        </div>
                        <div class="cl"></div>

                        <i id="I3" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>
                </ul>
            </div>
            <div class="sale_form_l fxe_fxgxms">房源个性描述</div>
            <div class="sale_form_r" th:include="fragment/freepublish::houseDescribe"></div>
            <div class="cl"></div>

            <div class="sale_form_l">联系方式</div>
            <div class="sale_form_r" th:include="fragment/freepublish :: contactWay"></div>
            <div class="cl"></div>
            <div th:include="fragment/freepublish::terms"></div>
            <style>
                .padB10{
                    padding-bottom: 20px;
                    margin-left: 113px;
                    margin-top: 0px;
                }
            </style>
            <input type="submit" name="submit" value="立即发布" id="submit" class="cl submitBtnFroms" />
            <input type="hidden" id="alertType" th:value="${alert}"/>
            <script th:inline="javascript">
                $(function () {
                    // 初始化数据
                    // $(':input','#jsUpForm')
                    //     .not(':button, :submit, :reset, :hidden')
                    //     .val('')
                    //     .removeAttr('checked')
                    //     .removeAttr('selected');
                    //
                    // $("#HouseTrait").val("");        // 清空房源特色值
                    // $("#OtherEstablish").val("");        // 清空商铺配套特色值
                    // $(".fyts a").removeClass("focus")  // 移除房源特色选中
                    // $(".fwpz a").removeClass("focus")      // 移除商铺配套选中

                    var alertType = [[${alert}]];
                    console.log('打印返回值')
                    console.log(alertType)
                    if (alertType == 1) {
                        var sessionId = [[${session.muser}]];
                        var saleHouseId = [[${houseId}]];

                        // Alertljz.ShowAlert('房源信息发布成功，稍后会有工作人员跟你联系<br>如有疑问请咨询电话：<span>400-893-9709</span>','https://sy.fangxiaoer.com/new_secondPublish');

                        // 显示成功后的弹窗
                        /*$(".successMsgTc").show()
                        $(".successMsgFull").show()*/


                        location.href="/realShop/"+saleHouseId//跳转继续认证



                        // 关闭弹窗
                        $(".closeSml").click(function(){
                            window.location.href='/shopsell'
                        });
                        console.log(sessionId)
                        console.log(saleHouseId)

                        //继续认证
                        $(".verf").click(function(){
                            location.href="/realShop/"+saleHouseId
                        })
                    }
                    if (alertType == 2) {
                        Alertljz.ShowAlert('登录失败!账号或密码错误');
                    }
                    if (alertType == 6) {
                        Alertljz.ShowAlert('亲，个人账户最多只能发布6条房源噢！','https://sy.fangxiaoer.com/new_secondPublish');
                    }
                    if (alertType == 5) {
                        Alertljz.ShowAlert('亲，您是经纪人需要去经纪人站才能发布，这里是不能发布噢！','https://agent.fangxiaoer.com/');
                    }
                })
            </script>
        </form>
        <input type="hidden" value="" id="textLength"/>
    </div>
</div>

<!--实名验证  开始-->
<!--页面弹窗-->
<style>
    /*#submitForm{display: none !important;}*/
    /*#loginClose{display: none !important;}*/
    #txt_LoginName,#txt_Password{box-shadow: none!important;}
    body{padding-top: 50px}
    .sale_form_r input{display: inline-block !important;}
</style>
<div th:include="house/detail/fragment_login::login"></div>
<script src="https://static.fangxiaoer.com/js/tc/weui_v1.2.8.js"></script>
<link rel="stylesheet" type="text/css" href="/css/saleHouse.css"/>
<div class="tc_realname">
    <h4>账号实名认证</h4>
    <h5>根据相关部门要求，发布房源之前个人账号需要实名认证</h5>
    <div class="realname_main">
        <ul class="realname_ul">
            <li class="err1" data-id="err1">
                <span class="span1"><i>*</i>上传身份证件</span>
                <div class="errorBox"></div>
                <div class="real_Id_img">
                    <div id="uploader1">
                        <i>不对外展示</i>
                        <p>上传身份证正面</p>
                        <div class="img_item" id='addimg1'>
                            <input id="uploaderInput1" class="uploader_input" type="file" accept="image/*"  multiple="" />
                            <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png" class="img_add1">
                        </div>
                    </div>
                    <div id="uploader2">
                        <i>不对外展示</i>
                        <p>上传身份证反面</p>
                        <div class="img_item" id='addimg2'>
                            <input id="uploaderInput2" class="uploader_input" type="file" accept="image/*"  multiple="" />
                            <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png" class="img_add2">
                        </div>
                    </div>
                    <p class="idMsg">水印内容：仅限房小二网房源登记</p>

                </div>
                <div class="cl"></div>
            </li>
            <li class="inputid err2" data-id="err2">
                <span><i>*</i>姓名</span>
                <input type="text" id="idName" placeholder="请输入姓名" maxlength="10">
                <div class="errorBox"></div>
                <div class="cl"></div>
            </li>
            <li class="inputid err3" data-id="err3">
                <span><i>*</i>身份证号</span>
                <input type="text" id="idNum" placeholder="请输入身份证号" onkeyup="certNumOnkeyup(this)" oninput="if(value.length>18)value=value.slice(0,18)" maxlength="18">
                <div class="errorBox"></div>
                <div class="cl"></div>
            </li>
            <li class="inputid err4 jeinpbox" data-id="err4">
                <span><i>*</i>身份证有效期</span>
                <input type="text" id="startime" class="idtime jeinput" placeholder="例：2019.03.19">
                <s>--</s>
                <input type="text" id="endtime" class="idtime jeinput" placeholder="例：2029.03.19">
                <div class="errorBox" style="bottom: -20px;"></div>
                <div class="cl"></div>
            </li>

        </ul>

    </div>
    <p class="errMsg"></p>
    <div class="realname_btn" id="realname_btn">提交审核</div>
</div>
<div class="idErr">
    <p>您的身份证信息和手机号不一致，是否继续发布？</p>
    <div class="idErr-btn">
        <a class="idErr-btn-yes">是</a>
        <a class="idErr-btn-no">否</a>
    </div>
</div>
<div class="tc_trust">
    <i class="trust_colse"></i>
    <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/21.png" alt="" class="trust_img">
    <h4 class="trust_h4">实名认证成功</h4>
</div>
<div class="tc_full"></div>
<link type="text/css" rel="stylesheet" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/jedate.css">
<script type="text/javascript" src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/jedate.js"></script>
<script th:inline="javascript">
    var sessionId1 = [[${session.muser}]]; // 有值-登录了  没值-没登录
    var authenticationStatus = [[${session.authenticationStatus}]];  //0-未认证 1-认证成功  2-认证失败
    $(document).ready(function () {
        certNumOnkeyup = function (obj) {
            obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"以外的字符
            obj.value = obj.value.replace(/^0+$/g,"");//以0开头时清空
        }
        // 根据账户状态来判定我自己卖跳转打开页面
        if(sessionId1 == null){//没登录 打开登录弹窗
            $("#login").show()
            $(".tc_full").show()
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
            $(".tc_realname").show()
            $(".tc_full").show()
            $("body").css("overflow-y","clip")
        }

        //点击验证是否登陆
        $('input').attr('autocomplete','off')
        $('input,textarea,.gckt').click(function(){
            if(sessionId1 == null){//没登录 打开登录弹窗
                $("#login").show()
                $(".tc_full").show()
            }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
                $(".tc_realname").show()
                $(".tc_full").show()
                $("body").css("overflow-y","clip")
            }
        })

        $(document).on('click','#loginClose',function(){
            $("#login").hide()
            $(".tc_full").hide()
        })
        $(document).on('click','.trust_colse',function(){
            window.location.reload()
        })


        // 是否勾选协议
        $(document).on('click','.check_span',function(){
            $('.check_span').toggleClass('check_span2');
        })

        // 上传身份证weui 正
        var uploadCount1 =0;
        // $("#idName,#idNum").attr("readOnly",true);
        $(".realname_ul li input").attr("readOnly",true);
        weui.uploader('#uploader1', {
            url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
            auto: true,
            type: 'file',
            fileVal: 'file',
            compress: false,
            onBeforeQueued: function(files) {
                var uploadCount1 = 0;
                // `this` 是轮询到的文件, `files` 是所有文件
                if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                    alert('请上传图片');
                    return false; // 阻止文件添加
                }
                if (this.size > 5 * 1024 * 1024) {
                    alert('请上传不超过5M的图片');
                    return false;
                }
                if (files.length > 1) { // 防止一下子选择过多文件
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                if (uploadCount1 + 1 > 1) {
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                ++uploadCount1;

                // return true; // 阻止默认行为，不插入预览图的框架
            },
            onSuccess: function(ret) {
                if (ret.msg == 'success') {
                    var data = ret.content;
                    $("#uploader1 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png")
                    $("#addimg1").before(
                        '<div class="img_item">' +
                        '<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del1" alt="">' +
                        '<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info1"></div>');
                    idOcr("https://images1.fangxiaoer.com/"+data,"FRONT")
                    moveErrorMsg("err1")
                    imgdata1(); //数据处理
                } else {
                    var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
                    alert(msg);
                    imgdata1(); //数据处理
                    return false;
                }
                return true; // 阻止默认行为，不使用默认的成功态
            },
            onError: function(err) {
                // alert("上传失败");
                imgdata1(); //数据处理
                // return true; // 阻止默认行为，不使用默认的失败态
            }
        });

        // 身份证正面 删除
        $("#uploader1").on('click', '.img_del1', function() {
            $(this).parent().remove();
            $("#idName").val("")
            $("#idNum").val("")
            $("#idName").attr("readOnly",true);
            $("#idNum").attr("readOnly",true);
            imgdata1(); //数据处理
        });

        $("#uploader1").on('click', '.img_info', function() {
            weui.gallery(this.src);
        })

        function imgdata1() {
            var imgHeight = $('.img_item').width();
            $('.img_item').css('height', imgHeight + 'px')

            $('#uploadCount1').text($(".info1").length) //小区图数量
            uploadCount1 = $(".info1").length
            if($(".info1").length>=1){
                $('#addimg1').hide();
            }else{
                $('#addimg1').show();
            }
            var result = '';
            // |www.baidu.com;255, |www.baidu.com;256,
            $(".info1").each(function() {
                result = result + $(this).attr("datasearchSubName-id") + ',';
            });
            result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
            // $("#pic").val(result); //室内实拍图片赋值
        }

        <!--上传身份证weui 反面-->
        var uploadCount2 =0;
        weui.uploader('#uploader2', {
            url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
            auto: true,
            type: 'file',
            fileVal: 'file',
            compress: false,
            onBeforeQueued: function(files) {
                var uploadCount2 = 0;
                console.log(this)
                // `this` 是轮询到的文件, `files` 是所有文件
                if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                    alert('请上传图片');
                    return false; // 阻止文件添加
                }
                if (this.size > 5 * 1024 * 1024) {
                    alert('请上传不超过5M的图片');
                    return false;
                }
                if (files.length > 1) { // 防止一下子选择过多文件
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                if (uploadCount2 + 1 > 1) {
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                ++uploadCount2;

                // return true; // 阻止默认行为，不插入预览图的框架
            },
            onSuccess: function(ret) {
                if (ret.msg == 'success') {
                    var data = ret.content;
                    $("#uploader2 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png")
                    $("#addimg2").before(
                        '<div class="img_item">' +
                        '<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del2" alt="">' +
                        '<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info2"></div>');
                    idOcr("https://images1.fangxiaoer.com/"+data,"BACK")
                    moveErrorMsg("err1")
                    //身份证有效期
                    jeDate("#startime",{
                        //onClose:false,
                        format: "YYYY-MM-DD"
                    });
                    jeDate("#endtime",{
                        //onClose:false,
                        format: "YYYY-MM-DD"
                    });
                    imgdata2(); //数据处理

                } else {
                    var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
                    alert(msg);
                    imgdata1(); //数据处理
                    return false;
                }
                return true; // 阻止默认行为，不使用默认的成功态
            },
            onError: function(err) {
                // alert("上传失败");
                imgdata1(); //数据处理
                // return true; // 阻止默认行为，不使用默认的失败态
            }
        });

        // 身份证反面 删除
        $("#uploader2").on('click', '.img_del2', function() {
            $(this).parent().remove();
            $("#startime").val("")
            $("#endtime").val("")
            $("#startime").attr("readonly",true)
            $("#endtime").attr("readonly",true)
            imgdata2(); //数据处理
        });
        $("#uploader2").on('click', '.img_info', function() {
            weui.gallery(this.src);
        })
        function imgdata2() {
            var imgHeight = $('.img_item').width();
            $('.img_item').css('height', imgHeight + 'px')

            $('#uploadCount2').text($(".info2").length) //小区图数量
            uploadCount2 = $(".info2").length
            if($(".info2").length>=1){
                $('#addimg2').hide();
            }else{
                $('#addimg2').show();
            }
            var result = '';
            // |www.baidu.com;255, |www.baidu.com;256,
            $(".info2").each(function() {
                result = result + $(this).attr("data-id") + ',';
            });
            result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
            // $("#pic").val(result); //室内实拍图片赋值
        }

        // 身份证ocr认证
        function idOcr(imageUrl,side) {
            var timeMillis =  Date.parse(new Date())
            console.log(imageUrl)
            $.ajax({
                url:"/checkOCRIDCard",
                data: {
                    imageUrl:imageUrl,
                    side:side,
                    timeMillis:timeMillis
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if(side == "FRONT"){//正面传好
                        $("#idName").attr("readonly",false)
                        $("#idNum").attr("readonly",false)
                    }else{
                        /*$("#startime").attr("readonly",false)
                        $("#endtime").attr("readonly",false)*/
                    }
                    if (data.status == 1) {
                        data = data.content
                        // console.log(data)
                        if(side == "FRONT"){
                            $("#idName").val(data.PERSON_NAME)
                            $("#idNum").val(data.PERSON_ID.substring(0,18))
                            $("#idTextType").val("1")
                            $("#idTextType").attr("data-z","1")
                        }else{
                            var idTime = data.TIME_ZONE.split("-")
                            $("#startime").val(idTime[0].split(".").join("-"))
                            $("#endtime").val(idTime[1].split(".").join("-"))
                            $("#idTextType").val("2")
                            $("#idTextType").attr("data-f","1")
                        }
                        $(".realname_btn").addClass("hover")
                    }else{
                        errorMsg("err1",data.msg)
                    }
                }
            })
        }
        // 错误提醒
        function errorMsg(idname,errorMsg){
            $('.'+idname).addClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('<label class="error">'+errorMsg+'</label>')
            $('.'+idname).find(".errorBox").css("visibility","initial")
        }
        // 移除错误提醒
        function moveErrorMsg(idname){
            $('.'+idname).removeClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('')
            $('.'+idname).find(".errorBox").css("visibility","hidden")
        }

        // 点击提交实名审核
        $(document).on('click','#realname_btn.hover',function(){
            // console.log(sessionId1)
            var idCardFontPic = $(".info1").attr("data-id")
            var idCardBackPic = $(".info2").attr("data-id")
            var rName = $("#idName").val()
            var idCard = $("#idNum").val()
            var idCardStartDateStr = $("#startime").val()
            var idCardEndDateStr = $("#endtime").val()
            if(idCardFontPic == "" || idCardFontPic == undefined){
                errorMsg("err1","上传身份证正面")
            }else if(idCardBackPic == "" || idCardBackPic == undefined){
                errorMsg("err1","上传身份证反面")
            }else if(rName == ""){
                errorMsg("err2","请输入姓名")
            }else if(idCard == ""){
                errorMsg("err3","请输入身份证号")
            }else if(idCardStartDateStr == ""){
                errorMsg("err4","请输入身份证有效期--起始日期")
            }else if(idCardEndDateStr == ""){
                errorMsg("err4","身份证有效期--截至日期")
            }else{
                $.ajax({
                    url:"/memberIdCardAuthentication",
                    data: {
                        sessionId:sessionId1,
                        idCardFontPic:idCardFontPic,
                        idCardBackPic:idCardBackPic,
                        rName:rName,
                        idCard:idCard,
                        idCardStartDateStr:idCardStartDateStr,
                        idCardEndDateStr:idCardEndDateStr
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        console.log(data)
                        if (data.status == 1 ) {
                            if(data.msg == "error"){// 认证失败
                                $(".idErr").show()
                                $(".tc_full").show()
                                $(".idErr p").html(data.content+",是否继续发布？")
                                $(".tc_realname").hide()
                                $("body").css("overflow-y","inherit")
                            }else{//认证成功
                                $(".tc_trust").show()
                                $(".tc_full").show()
                                $(".tc_realname").hide()
                                $("body").css("overflow-y","inherit")
                            }

                        }else if(data.status == 0 ){// 认证三次后 失败
                            $(".errMsg").show()
                            $(".errMsg").html(data.msg)
                            // $(".realname_btn").removeClass("hover")
                        }

                    },
                    error:function (data) {//认证失败
                        console.log(data)
                        $(".errMsg").show()
                        $(".errMsg").html(data.msg)
                        $(".realname_btn").removeClass("hover")
                    }
                })
            }
        })

        $("input").focus(function () {
            var idName = $(this).parent().attr("data-id")
            moveErrorMsg(idName)
        })

        // 身份证信息和手机号不一致，点击 是 继续 继续发布
        $(document).on('click','.idErr-btn-yes',function(){
            $.ajax({
                url:"/confirmAuthenticationInfo",
                data: {
                    sessionId:sessionId1
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    if (data.status == 1) {
                        window.location.reload()
                    }
                }
            })
        })
        // 身份证信息和手机号不一致，点击 否 继续弹出认证弹窗
        $(document).on('click','.idErr-btn-no',function(){
            $(".tc_realname").show()
            $("body").css("overflow-y","clip")
            $(".idErr").hide()
            $(".tc_full").show()
            //身份证有效期
            jeDate("#startime",{
                //onClose:false,
                format: "YYYY-MM-DD"
            });
            jeDate("#endtime",{
                //onClose:false,
                format: "YYYY-MM-DD"
            });
        })

    })




</script>
<!--实名验证  结束-->

<script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
<script>
    $("#submit").click(function () {

        if(sessionId1 == null){//没登录 打开登录弹窗
            $("#login").show()
            $(".tc_full").show()
            return false;
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
            $(".tc_realname").show()
            $(".tc_full").show()
            $("body").css("overflow-y","clip")
            return false;
        }

        if(!$("#checkagree").hasClass("checked")){
            // timeout(".checkimg", "请阅读服务条款");
            alert("请仔细阅读并同意服务协议及隐私政策！");
            return false;
        }
        $(".xl_yz")
                .each(function () {
                    $(this).find(".my_xl_input").each(function () {
                        if ($(this).val() == "") {
                            $(this).parent().css("border", "1px solid #fd634f");
                            $(document).scrollTop($(this).parent().offset().top);
                        }
                    });
                });
    });
    $("#OwnerPhone").keyup(function () {
        this.value = this.value.replace(/\D/g, '')
    })

    $(".successMsgTcBtn").click(function () {
        $(".successMsgTc").hide()
        $(".successMsgFull").hide()

        window.location.href="https://sy.fangxiaoer.com/new_secondPublish";
    })
    // 关闭弹窗
    $(".closeImg").click(function(){
        $(".successMsgTc").hide()
        $(".successMsgFull").hide()
        // window.location.href="https://sy.fangxiaoer.com/new_secondPublish";


        // 清空表单数据
        $(':input','#jsUpForm')
            .not(':button, :submit, :reset, :hidden')
            .val('')
            .removeAttr('checked')
            .removeAttr('selected');

        $("#HouseTrait").val("");        // 清空房源特色值
        $("#OtherEstablish").val("");        // 清空商铺配套特色值
        $(".fyts a").removeClass("focus")  // 移除房源特色选中
        $(".fwpz a").removeClass("focus")      // 移除商铺配套选中
    });

    $(".my_xl_list li").live("click", function () {
        $(this).parent().parent().css("border", "1px solid #ccc");
    })
    $(".lx a")
            .click(function () {
                $(this).parent().find("a").removeClass("hover");
                $(this).addClass("hover");
                $(this).parent().find("input").val($(this).parent().find(".hover").attr("rel"));
            });
    $(".fg a")
            .click(function () {
                $(this).parent().find("a").removeClass("hover");
                $(this).addClass("hover");
                $(this).parent().find("input").val($(this).parent().find(".hover").attr("rel"));
            });
    //付款方式wr
    $(".fkfsul li").click(function () {
        var vv = $(this).attr("value");
        //$(this).parent().siblings("input").val($(this).attr("value"));
        $(this).parent().siblings("input").attr("value", vv);
    });
    $(function () {
        var fwpz;
        $(".lx a").each(function () {
            if ($(this).attr("rel") == $("#ShopCategories").val()) {
                $(this).addClass("hover");
            }
        });

        $(".fwpz a").each(function () {
            var fwpz = $("#MIndustry").val().split(",");
            for (var t = 0; t < fwpz.length; t++) {
                if (fwpz[t] != "" && fwpz[t] != null) {
                    if ($(this).attr("rel") == fwpz[t]) {
                        $(this).addClass("focus");
                    }
                }

            }
        });

        if ($("#TranType").val() == "1") {
            $(".zr a").eq(0).addClass("hover")
            $(".zr input").eq(0).val($("#TranFee").val())
        } else {
            $(".zr a").eq(1).addClass("hover")
            $(".zr input").eq(1).val($("#TranFee").val())
        }


    });

    $(function () {
        var checkBox = $("#HouseTrait").val();
        console.log(checkBox);
        var checkBoxArray = checkBox.split(",");
        console.log(checkBoxArray);
        for (var i = 1; i <= checkBoxArray.length; i++) {
            $(".fyts a").eq(checkBoxArray[i] - 1).addClass("focus");

        }
    })
    //特色
    $(".fyts a").click(function () {

        //$("#fwpz_yz").show();
        if ($(this).attr("class") == "focus") {
            $(this).removeClass("focus");
        } else {
            if ($(".fyts a.focus").length >= 3) {
                alert("最多可选3个特色");
            } else {
                $(this).addClass("focus");
            }
        }
    });
    $("#Price").blur(function(){
        if($("#Price").val()==""){
            $("#Price").val("面议")
        }
    })
    $("#Price").focus(function(){
        if($("#Price").val()=="面议"){
            $("#Price").val("")
        }
    })
</script>
<div th:include="fragment/fragment::notLoggedIn"></div>
<!--底部-->
<div class="issuedInfo" th:include="fragment/fragment:: publish_footer1"></div>
<div class="cl"></div>
<div class="footer" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>

</body>
</html>


