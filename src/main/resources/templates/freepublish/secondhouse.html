<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script type="text/javascript" src="/js/AliyunCaptcha.js"></script>
    <script src="/js/signatureUtils.js"></script>
    <title>沈阳二手房信息发布_免费信息发布_个人房源 - 房小二网</title>
    <meta name="keywords" content="二手房信息发布,个人房源,沈阳二手房信息,二手房中介,免费信息发布"/>
    <meta name="description" content="房小二网为您提供专业贴心的二手房服务，为您提供免费二手房信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/pubSale.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/images/sy/check/form1.css?v=20220902" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.validate.min1.js"></script>
    <script type="text/javascript" src="/js/freepublish/personal.js"></script>
    <script type="text/javascript" src="/js/AjaxforJquery.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/agent/autocomplete.js"></script>
    <!--文本编辑器-->
<!--    <link rel="stylesheet" type="text/css" href="/Kindeditor/default.css" />
    <link rel="stylesheet" type="text/css" href="/Kindeditor/prettify.css" />
    <script type="text/javascript" charset="utf-8" src="/Kindeditor/kindeditor.js"></script>
    <script type="text/javascript" charset="utf-8" src="/Kindeditor/zh_CN.js"></script>
    <script type="text/javascript" charset="utf-8" src="/Kindeditor/pr  ettify.js"></script>-->
    <!--[if IE 6]>
    <style type="text/css">
        input{display:inline !important}
    </style>
    <![endif]-->
    <script type="text/javascript" src="/js/freepublish/tese.js"></script>
    <!-- <script type="text/javascript" src="/js/freepublish/form.js"></script> -->
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js"></script>
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <style>
		.noselect{
			pointer-events:none;
		}
        .realRight{
            bottom: 0;
            z-index: 999999999;
        }
        .diyCancel{bottom: 1px !important;}
        .diyProgressText{cursor: pointer}
        .closeImg{
            display: block;
            float: right;


            margin: 12px;
            cursor: pointer;
        }

      .closeImg{
          width: 19px !important;
          height: 19px !important;
          position: absolute !important;
          right: 12px !important;
          top: 12px !important;
      }
      .ui-autocomplete{    max-height: 220px;
          height: auto;
          overflow-y: auto;
          z-index: 99 !important;}
        .ui-autocomplete li span{display: none}
        #head2017 { position: absolute;width: 100%;left: 0;top: 0;z-index: 9999999999; }
	</style>

</head>

<body >
<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<div class="title">为您提供专业贴心的二手房服务<p>免费咨询电话：400-893-9709</p></div>
<input type="hidden" id="check" th:value="${check}"/>

<!--二手房发布页挂角引导优化-->
<!--<div class="esfRightBottom">
    <i class="realRightClose"></i>
    <img src="https://static.fangxiaoer.com/web/images/download/successMsgTcImg.jpg" alt="">
    <p>使用房小二网小程序</p>
    <p>随时随地查看房产售卖信息</p>
</div>-->
<!--二手房发布成功提示页面优化-->
<!--<div class="successMsgTc" >
    <img class="closeImg" src="https://static.fangxiaoer.com/web/images/my/admin/yun_close.gif" alt="">
    <p>房源信息发布成功</p>
    <p>认证真房源  提升十倍曝光  加速房产售卖</p>
    <img class="realImg"   src="" alt="">
    <p>微信扫一扫 登录小程序 开始认证</p>
    <p>稍后将有工作人员与您联系核实信息</p>
    <p>如有疑问欢迎致电：400-893-9709</p>
    <div class="successMsgTcBtn">确定</div>
</div>-->
<div class="successMsgTc">
    <div class="success_m">
        <div class="closeSml"></div>
        <div class="ics"></div>
        <div class="sumsg">房源基本信息发布成功</div>
        <div class="smo"><span>房小二网客服正在核实基本消息</span><span>继续认证真房源可加速出售</span></div>
        <div class="chbtn verf">继续认证</div>
        <div class="rnbtn" onclick="window.open('https://my.fangxiaoer.com/house/houses0_1')">管理房产</div>
        <div class="multiple"><i class="left0"></i>多个认证途径 <i class="right0"></i></div>
        <div class="muft">排序优先<i>·</i>增加曝光<i>·</i>独有标识</div>
    </div>
</div>
<div class="successMsgFull"></div>

<!--真房源引导 开始-->

<div class="realGuideFull">
    <div class="realGuide">
        <h5>推荐使用房小二网手机端</h5>
        <p>认证“真房源”，提升曝光量，加速售卖!</p>
        <div class="realGuideBtn">
            <div class="realGuideBtnClose">取消</div>
            <a href="/secondPublish/" class="realGuideBtnToReal">立即使用</a>
        </div>
    </div>
</div>

<!--右下角真房源引导-->
<!--<div class="realRight">-->
<!--    <i class="realRightClose"></i>-->
<!--    <img class="realRightIco" src="https://static.fangxiaoer.com/web/images/sy/download/realRightIco.png" alt="">-->
<!--    <h5>发布真房源</h5>-->
<!--    <p>10倍曝光 加速售卖</p>-->
<!--    <a href="/secondPublish/">立即发布</a>-->
<!--</div>-->
<!--<script type="text/javascript">-->
<!--    $(document).ready(function () {-->
<!--        $(".realGuideFull").show()-->
<!--        $(".realGuideBtnClose").click(function () {-->
<!--            $(".realGuideFull").hide()-->
<!--        })-->
<!--        $(".realRightClose").click(function () {-->
<!--            $(".realRight").hide()-->
<!--        })-->
<!--    })-->
<!--</script>-->
<!--真房源引导 结束-->

<div class="kuang">
    <!--表单-->
    <div class="sale_form_r qh_btn" style="width: 1270px;margin: 0 auto;float: none;"th:include="fragment/freepublish::publishType" th:with="currType=1">
    </div>

    <div class="sale_form">
<!--        java绑定数据，直接把接口写在th:action里面-->
        <form name="jsUpForm" method="post"
              th:action="@{/saveSecondHouse}" th:object="${saleHouseEntity}" id="jsUpForm" validateRequest="false">
            <div class="cl"></div>
            <div class="sale_form_l">房源基本信息</div>
            <div class="sale_form_r">
                <ul>
                    <li class="new-sub-name">
                        <p><span>*</span>小区名称</p>
<!--                        <div class="pr" style="position: relative">-->
<!--                            这里th:field 相当于vue的v-model 绑数据 *后面是参数-->
                            <input type="hidden"  id="houseid" th:field="*{houseid}"/>
                            <input type="hidden"  id="RegionID" th:field="*{regionid}"/>
                            <input type="hidden"  id="PlateID" th:field="*{plateid}"/>
                            <input type="hidden"  id="SubID" th:field="*{subid}"/>
                            <input type="hidden"  id="SubAddress" th:field="*{subaddress}"/>
                            <input type="hidden"  id="SubwayLine" th:field="*{subwayline}"/>
                            <input type="hidden"  id="SubwaySite" th:field="*{subwaysite}"/>
                            <input type="hidden"  id="newsub" th:field="*{newsub}"/>
                            <div style="position: absolute; z-index: -10">
                                <input type="text" id="subname" th:field="*{subname}" />
                            </div>
                      <!--  <div style="position: absolute; z-index: -10">
                            <input type="text"  id="detailid" th:field="*{detailid}"/>
                        </div>-->
<!--                            <input type="text" id="searchSubName" class="xiaoqu newSubSearch" th:value="*{subname}" placeholder="只填写小区名称，例枫景瑞阁" onkeyup="noSelect()" autocomplete="on"/>-->
<!--                        </div>-->
<!--                        <i id="SubName123" class="ljz"></i>-->
<!--                        <span style="color: #999;">请填写小区名称，并在下拉框中选择小区</span>-->
                        <div class="ui-widget pr" style="position: relative">
                            <input type="text" id="searchSubName" class="xiaoqu" th:value="*{subname}" placeholder="请输入小区名" onkeyup="noSelect()"/>
                            <i id="SubName123" class="ljz"></i>
                            <span style="color: #666;" class="subMsg">请填写小区名称，并在下拉框中选择小区</span>
                            <ul class="newSubList">
                            </ul>
                        </div>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130 new-sub-lph" id="lph" >
                        <p><span>*</span>门牌信息</p>

                        <div style="position: absolute; z-index: -10">
                            <input type="text"  id="detailid" th:field="*{detailid}"/>
                        </div>
                        <div class="lphselect lphselect1">
                            <span class="showLphselect1 gckt" data-val="">请选择门牌信息</span>
                            <ul id="listArchId" style="display: none;"></ul>
                        </div>
                        <div class="lphselect lphselect2">
                            <span  class="showLphselect2 gckt">请选择单元</span>
                            <ul id="listUnitId" style="display: none;"></ul>
                        </div>
                        <div class="lphselect lphselect3">
                            <span  class="showLphselect3 gckt">请选择楼层</span>
                            <ul id="listDetailId" style="display: none;"></ul>
                        </div>
                        <div class="lphselect lphselect4">
                            <span  class="showLphselect4 gckt">请选择具体户室</span>
                            <ul id="listFloor" style="display: none;"></ul>
                        </div>
                        <div class="nolph">
                            <p>没有符合的楼层户室号？</p>
                            <span class="gckt">点此继续发布</span>
                        </div>
                        <div class="cl"></div>
                        <div class="errorBox"></div>

                    </li>

                    <li class="w130 huxing">
                        <p><span>*</span>房屋户型</p>
                        <div class="pr">
                            <input name="room" type="text" maxlength="1" id="room" class="room" th:field="*{room}"/><span class="right">室</span></div>
                        <div class="pr">
                            <input name="hall" type="text" maxlength="1" id="Hall" class="hall" th:field="*{hall}"/><span class="right">厅</span></div>
                        <div class="pr">
                            <input name="toilet" type="text" maxlength="1" id="Toilet" class="toilet" th:field="*{toilet}"/><span class="right">卫</span></div>
                        <div class="pr">
                            <input name="buildarea" type="text" maxlength="6" id="BuildArea" class="mianji" placeholder="建筑面积" th:field="*{buildarea}"/><span class="left">共</span><span class="right">㎡</span></div>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130">
                        <p><span>*</span>总价</p>
                        <div class="pr">
                            <input name="saleprice" type="text" id="SalePrice" class="zujin" th:field="*{saleprice}" placeholder="请输入您期望卖出的价格" style="text-align: left;width: 180px !important;"/><span class="right">万</span></div>
                        <i id="zujin1" class="ljz"></i><div class="errorBox"></div>
                    </li>

                    <li>
                        <p><span>*</span>标题</p>
                        <div class="pr">
                            <input name="title" type="text" id="Title" th:field="*{title}"/></div>
                        <i id="Title123" class="ljz"></i><div class="errorBox"></div>
                    </li>
                </ul>
            </div>
            <div class="cl"></div>

            <div class="sale_form_r">
                <ul class="">

                    <li class="w130">
                        <p>装修/朝向</p>
                        <!--<div class='my_xl xx' style="z-index:98">-->
                            <!--<input name="saletype" type="text" id="SaleType" tabindex="-1" class="my_xl_input" value="4" th:field="*{saletype}"/>-->
                            <!--<div class='my_xl_txt' value="1235">普宅</div>-->
                            <!--<div class='my_xl_btn'></div>-->
                            <!--<ul class='my_xl_list'>-->
                                <!--<li th:each="types,iter:${types}"  th:value="${types.id}" th:text="${types.name}">公寓</li>-->
                            <!--</ul>-->
                        <!--</div>-->
                        <div class='my_xl xx' style="z-index:97">
                            <input name="fitmenttype" type="text" id="FitmentType" tabindex="-1" class="my_xl_input" value="3" th:field="*{fitmenttype}"/>
                            <div class='my_xl_txt gckt'>精装</div>
                            <div class='my_xl_btn'></div>
                            <ul class='my_xl_list'>

<!--                                each就相当于v-for遍历   -->
                                <li th:each="fitment:${fitment}" th:value="${fitment.id}" th:text="${fitment.name}" ></li>
                            </ul>
                        </div>
                        <div class='my_xl xx' style="margin-bottom: 25px; z-index:96">
                            <input name="forwardtype" type="text" id="ForwardType" tabindex="-1" class="my_xl_input" th:field="*{forwardtype}"/>
                            <div class='my_xl_txt gckt'>南北</div>
                            <div class='my_xl_btn'></div>
                            <ul class='my_xl_list'>
                                <li th:each="forward:${forward}" th:value="${forward.id}" th:text="${forward.name}" ></li>
                            </ul>
                        </div>


                        <i id="enter" class="ljz"></i>
                    </li>
                    <div class="cl"></div>
<!--
                    <li class="w130">
                        <p>产权</p>
                        <div class='my_xl' style="z-index:95">
                            <input name="propertyrighttype" type="text" id="PropertyRightType" class="my_xl_input"  th:field="*{propertyrighttype}"/>
                            <div class='my_xl_txt'>产权</div>
                            <div class='my_xl_btn'></div>
                            <ul class='my_xl_list'>
                                <li th:each="propertyRight:${propertyRight}" th:value="${propertyRight.id}" th:text="${propertyRight.name}" ></li>
                            </ul>
                        </div>
                        <div class='my_xl' style="z-index:93">
                            <input name="housingtype" type="text" id="HousingType" class="my_xl_input" value="1" th:field="*{housingtype}"/>
                            <div class='my_xl_txt'>商品房</div>
                            <div class='my_xl_btn'></div>
                            <ul class='my_xl_list'>
                                <li th:each="kinds:${kinds}" th:value="${kinds.id}" th:text="${kinds.name}" ></li>
                            </ul>
                        </div>
                        <div class="pr">
                            <input name="buildingtime" type="text" id="buildingtime" placeholder="建筑年代" th:field="*{buildingtime}"/><span class="right">年</span></div>
                        <i id="buildingtime123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
-->
                    <li class="w130 floor lddysh">
                        <p><span>*</span>楼层</p>
                        <div class="pr">
                            <input name="floor" type="text" id="Floor" th:field="*{floor}"
                                   onkeyup="value=value.replace(/[^\- \d]/g ,'')" onafterpaste="this.value=this.value.replace(/[1-9]/g,'')"
                            /><span class="left">第</span><span class="right">层</span></div>
                        <div class="pr">
                            <input name="totalfloornumber" type="text" id="Totalfloornumber" th:field="*{totalfloornumber}"
                                   onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/[1-9]/g,'')"
                            /><span class="left">共</span><span class="right">层</span></div>
                        <i id="Totalfloornumber123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130 floor lddysh">
                        <p>楼栋</p>
                        <div class="pr">
                            <input name="unit" type="text" maxlength="4" id="unit"   th:field="*{unit}" onkeyup="value=value.replace(/[^\- \d]/g ,'')" onafterpaste="this.value=this.value.replace(/[1-9]/g,'')"/><span class="right">单元</span></div>
                        <div class="pr">
                            <input name="floorNumber" type="text" maxlength="4" id="FloorNumber"   th:field="*{floornumber}" onkeyup="value=value.replace(/[^\- \d]/g ,'')" onafterpaste="this.value=this.value.replace(/[1-9]/g,'')"/><span class="right">室号</span></div>
                        <i id="FloorNumber123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130">
                        <p>房源特色</p>
                        <div class="pr fyts gckt">

                            <a th:each="traits:${traits}" th:rel="${traits.id}" th:text="${traits.name}" th:id="${'tese'+traits.id}" th:value="${traits.id}"></a>



                            <input name="housetrait" type="text" id="HouseTrait" class="tese_input"  th:value="*{housetrait}"/>
                        </div>
                        <div class="cl"></div>

                        <i id="HouseTrait123" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>
                </ul>

                <div class="sh_btn hid">
                    <span class="icon"></span>
                    <div class="toggle_text">
                        <span>更多选填信息</span>
                        <p>丰富内容可使成交加速一倍</p>
                    </div>
                </div>

            </div>
            <div class="cl"></div>

            <div class="sale_form_l fxe_fxgxms">房源个性描述</div>
<!--            这里是插了一个描述和上传图片片段，在frament文件下-->
            <div class="sale_form_r" th:include="fragment/freepublish::SechouseDescribe"></div>

            <div class="cl"></div>


            <div class="sale_form_l">联系方式</div>
            <!--<div class="sale_form_r" th:include="fragment/freepublish::contactWay"></div>-->
            <!--联系方式-->

            <div class="sale_form_r" th:include="fragment/freepublish :: SeccontactWay"></div>
            <!-- 放在 body 的任意位置都可以，比如页面底部 -->
            <div id="captchaContainer" style="display: none;"></div>

            <div class="cl"></div>
            <div th:include="fragment/freepublish::terms"></div>
            <style>
                .padB10{
                    padding-bottom: 20px;
                    margin-left: 113px;
                    margin-top: 0px;
                }
            </style>
<!--            发布逻辑在static/js/freepublish/form.js里面  这里java绑定数据 不用ajax传了 直接在form的action里请求api即可-->
            <input type="submit" name="submit" value="立即发布" id="submit" class="cl submitBtnFroms" />

            <input type="hidden" id="alertType" th:value="${alert}"/>

             <input type="hidden" id="history_back" />
            <script th:inline="javascript">
                var saleHouseId = [[${houseId}]];
                var sessionId = [[${session.muser}]];; // 有值-登录了  没值-没登录
                $(function () {


                        // 初始化数据
                        // $(':input','#jsUpForm')
                        //     .not(':button, :submit, :reset, :hidden')
                        //     .val('')
                        //     .removeAttr('checked')
                        //     .removeAttr('selected');
                        //
                        // $("#alertType").val("");
                        // $("#HouseTrait").val("");      // 移除房源特色值
                        // $(".fyts a").removeClass("focus")  // 移除房源特色选中

                    var pageHref = window.location.href
                    if(pageHref.indexOf("/myPublish/edit/") >= 0 ) {
                        $(".realGuideFull").hide()
                    }


                    var alertType = [[${alert}]];

                    console.log('打印返回值')
                    console.log(alertType)
                    // 1是发布成功  显示扫码跳转小程序认证真房源弹窗

                    if (alertType == 1) {
                    // 这地方应该请求下接口  获取当前房源对应太阳码
                        /*console.log('当前的sessionId- '+sessionId)
                        console.log('房源id- '+saleHouseId)*/
                        // 有sessionId说明登录
                        if(sessionId !== '' && sessionId!== null && sessionId !== undefined){
                            console.log('已经登录')
                            var loginState2 = 1
                            var scene = 'login,' + loginState1 +',sessionId,' + sessionId;
                            console.log(scene)

                            $.ajax({
                                type: "GET",
                                async: false,
                                url:  "/getWxSecCode",
                                data:{"scene": scene},
                                dataType : 'json',
                                headers : {
                                    'Content-Type' : 'application/json;charset=utf-8'
                                },
                                success: function (data) {
                                    console.log('图片数据')
                                    console.log(data)
                                    let img = data.img
                                    $(".realImg").attr('src',"data:text/html;base64,"+img);  // 给太阳码图片赋值
                                    // 显示成功后的弹窗
                                    /*$(".successMsgTc").show()
                                    $(".successMsgFull").show()*/
                                    location.href="/verifyIt/"+saleHouseId//跳转继续认证
                                }
                            });
                        }else{
                            console.log('未登录')
                            var loginState1 = 0
                            var scene = 'login,' + loginState1 +',sessionId,' + sessionId;
                            console.log(scene)
                            $.ajax({
                                type: "GET",
                                async: false,
                                url:  "/getWxSecCode",
                                data:{"scene": scene},
                                dataType : 'json',
                                headers : {
                                    'Content-Type' : 'application/json;charset=utf-8'
                                },
                                success: function (data) {
                                    console.log('图片数据')
                                    console.log(data)
                                    let img = data.img
                                    $(".ics").attr('src',"data:text/html;base64,"+img);  // realImg 给太阳码图片赋值
                                    // 显示成功后的弹窗
                                    /*$(".successMsgTc").show()
                                    $(".successMsgFull").show()*/
                                    location.href="/verifyIt/"+saleHouseId//跳转继续认证
                                }
                            });

                        }


                        // var scene = {
                        //     'login':loginState1,
                        //     'sessionId':sessionId
                        // }

                        //继续认证
                        $(".verf").click(function(){
                            location.href="/verifyIt/"+saleHouseId
                        })



                    }
                    if (alertType == 2) {
                        Alertljz.ShowAlert('登录失败!账号或密码错误');
                        $(".realGuideFull").hide()
                        $("#alertType").val("6");
                    }
                    if (alertType == 6) {
                        Alertljz.ShowAlert('亲，个人账户最多只能发布6条房源噢！','https://sy.fangxiaoer.com/new_secondPublish');
                        $(".realGuideFull").hide()
                        $("#alertType").val("6");
                    }
                    if (alertType == 5) {
                        Alertljz.ShowAlert('亲，您是经纪人需要去经纪人站才能发布，这里是不能发布噢！','https://agent.fangxiaoer.com/');
                        $(".realGuideFull").hide()
                    }



                    // window.location.reload()
                    // // 初始化时候关闭弹窗
                    // $(".successMsgTc").hide()
                    // $(".successMsgFull").hide()
                    // $("#aerltAllDom").hide()



                })





                //修改Bug #15906
                 function noSelect(){
                    	var noselect= $(".ac_results ul li").eq(0).text();
                    	if(noselect=="未找到对应的小区"){
                    		$(".ac_results ul li").removeClass("ac_even");
                    		$(".ac_results ul").addClass("noselect");
                    		$(".ac_results ul li").addClass("noselect");
                    	}else{
                    		$(".ac_results ul").removeClass("noselect");
                    		$(".ac_results ul li").removeClass("noselect");
                    		
                    	}
                    }
            </script>

        </form>

        <input type="hidden" value="" id="textLength"/>

    </div>
    <p style="color: #999999;font-size: 12px;float: left;margin-left: 210px;">免责提醒：本平台展示为您私人电话，谨防有人恶意行骗，如遇骚扰/诈骗可下架房源，如遇到钱财损失请立即报警。</p>

</div>
<!--实名验证  开始-->
<!--页面弹窗-->
<style>
    /*#submitForm{display: none !important;}*/
    /*#loginClose{display: none !important;}*/
    #txt_LoginName,#txt_Password{box-shadow: none!important;}
    body{padding-top: 50px}
    .sale_form_r input{display: inline-block !important;}
</style>
<div th:include="house/detail/fragment_login::login"></div>
<script src="https://static.fangxiaoer.com/js/tc/weui_v1.2.8.js"></script>
<link rel="stylesheet" type="text/css" href="/css/saleHouse.css"/>
<div class="tc_realname">
    <h4>账号实名认证</h4>
    <h5>根据相关部门要求，发布房源之前个人账号需要实名认证</h5>
    <div class="realname_main">
        <ul class="realname_ul">
            <li class="err1" data-id="err1">
                <span class="span1"><i>*</i>上传身份证件</span>
                <div class="errorBox"></div>
                <div class="real_Id_img">
                    <div id="uploader1">
                        <i>不对外展示</i>
                        <p>上传身份证正面</p>
                        <div class="img_item" id='addimg1'>
                            <input id="uploaderInput1" class="uploader_input" type="file" accept="image/*"  multiple="" />
                            <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png" class="img_add1">
                        </div>
                    </div>
                    <div id="uploader2">
                        <i>不对外展示</i>
                        <p>上传身份证反面</p>
                        <div class="img_item" id='addimg2'>
                            <input id="uploaderInput2" class="uploader_input" type="file" accept="image/*"  multiple="" />
                            <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png" class="img_add2">
                        </div>
                    </div>
                    <p class="idMsg">水印内容：仅限房小二网房源登记</p>

                </div>
                <div class="cl"></div>
            </li>
            <li class="inputid err2" data-id="err2">
                <span><i>*</i>姓名</span>
                <input type="text" id="idName" placeholder="请输入姓名" maxlength="10">
                <div class="errorBox"></div>
                <div class="cl"></div>
            </li>
            <li class="inputid err3" data-id="err3">
                <span><i>*</i>身份证号</span>
                <input type="text" id="idNum" placeholder="请输入身份证号" onkeyup="certNumOnkeyup(this)" oninput="if(value.length>18)value=value.slice(0,18)" maxlength="18">
                <div class="errorBox"></div>
                <div class="cl"></div>
            </li>
            <li class="inputid err4 jeinpbox" data-id="err4">
                <span><i>*</i>身份证有效期</span>
                <input type="text" id="startime" class="idtime jeinput" placeholder="例：2019.03.19">
                <s>--</s>
                <input type="text" id="endtime" class="idtime jeinput" placeholder="例：2029.03.19">
                <div class="errorBox" style="bottom: -20px;"></div>
                <div class="cl"></div>
            </li>

        </ul>

    </div>
    <p class="errMsg"></p>
    <div class="realname_btn" id="realname_btn">提交审核</div>
</div>
<div class="idErr">
    <p>您的身份证信息和手机号不一致，是否继续发布？</p>
    <div class="idErr-btn">
        <a class="idErr-btn-yes">是</a>
        <a class="idErr-btn-no">否</a>
    </div>
</div>
<div class="tc_trust">
    <i class="trust_colse"></i>
    <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/21.png" alt="" class="trust_img">
    <h4 class="trust_h4">实名认证成功</h4>
</div>
<div class="tc_full"></div>
<link type="text/css" rel="stylesheet" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/jedate.css">
<script type="text/javascript" src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/jedate.js"></script>
<script th:inline="javascript">
    var sessionId1 = [[${session.muser}]]; // 有值-登录了  没值-没登录
    var authenticationStatus = [[${session.authenticationStatus}]];  //0-未认证 1-认证成功  2-认证失败
    $(document).ready(function () {
        certNumOnkeyup = function (obj) {
            obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"以外的字符
            obj.value = obj.value.replace(/^0+$/g,"");//以0开头时清空
        }
        // 根据账户状态来判定我自己卖跳转打开页面
        if(sessionId1 == null){//没登录 打开登录弹窗
         /*   $("#login").show()
            $(".tc_full").show()*/
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
           /* $(".tc_realname").show()
            $(".tc_full").show()
            $("body").css("overflow-y","clip")*/
        }

        //点击验证是否登陆
        $('input').attr('autocomplete','off')
        $('input,textarea,.gckt').click(function(){
            if(sessionId1 == null){//没登录 打开登录弹窗
               /* $("#login").show()
                $(".tc_full").show()*/
            }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
               /* $(".tc_realname").show()
                $(".tc_full").show()
                $("body").css("overflow-y","clip")*/
            }
        })

        $(document).on('click','#loginClose',function(){
            $("#login").hide()
            $(".tc_full").hide()
        })
        $(document).on('click','.trust_colse',function(){
            window.location.reload()
        })


        // 是否勾选协议
        $(document).on('click','.check_span',function(){
            $('.check_span').toggleClass('check_span2');
        })

        // 上传身份证weui 正
        var uploadCount1 =0;
        // $("#idName,#idNum").attr("readOnly",true);
        $(".realname_ul li input").attr("readOnly",true);
        weui.uploader('#uploader1', {
            url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
            auto: true,
            type: 'file',
            fileVal: 'file',
            compress: false,
            onBeforeQueued: function(files) {
                var uploadCount1 = 0;
                // `this` 是轮询到的文件, `files` 是所有文件
                if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                    alert('请上传图片');
                    return false; // 阻止文件添加
                }
                if (this.size > 5 * 1024 * 1024) {
                    alert('请上传不超过5M的图片');
                    return false;
                }
                if (files.length > 1) { // 防止一下子选择过多文件
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                if (uploadCount1 + 1 > 1) {
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                ++uploadCount1;

                // return true; // 阻止默认行为，不插入预览图的框架
            },
            onSuccess: function(ret) {
                if (ret.msg == 'success') {
                    var data = ret.content;
                    $("#uploader1 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png")
                    $("#addimg1").before(
                        '<div class="img_item">' +
                        '<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del1" alt="">' +
                        '<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info1"></div>');
                    idOcr("https://images1.fangxiaoer.com/"+data,"FRONT")
                    moveErrorMsg("err1")
                    imgdata1(); //数据处理
                } else {
                    var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
                    alert(msg);
                    imgdata1(); //数据处理
                    return false;
                }
                return true; // 阻止默认行为，不使用默认的成功态
            },
            onError: function(err) {
                // alert("上传失败");
                imgdata1(); //数据处理
                // return true; // 阻止默认行为，不使用默认的失败态
            }
        });

        // 身份证正面 删除
        $("#uploader1").on('click', '.img_del1', function() {
            $(this).parent().remove();
            $("#idName").val("")
            $("#idNum").val("")
            $("#idName").attr("readOnly",true);
            $("#idNum").attr("readOnly",true);
            imgdata1(); //数据处理
        });

        $("#uploader1").on('click', '.img_info', function() {
            weui.gallery(this.src);
        })

        function imgdata1() {
            var imgHeight = $('.img_item').width();
            $('.img_item').css('height', imgHeight + 'px')

            $('#uploadCount1').text($(".info1").length) //小区图数量
            uploadCount1 = $(".info1").length
            if($(".info1").length>=1){
                $('#addimg1').hide();
            }else{
                $('#addimg1').show();
            }
            var result = '';
            // |www.baidu.com;255, |www.baidu.com;256,
            $(".info1").each(function() {
                result = result + $(this).attr("datasearchSubName-id") + ',';
            });
            result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
            // $("#pic").val(result); //室内实拍图片赋值
        }

        <!--上传身份证weui 反面-->
        var uploadCount2 =0;
        weui.uploader('#uploader2', {
            url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
            auto: true,
            type: 'file',
            fileVal: 'file',
            compress: false,
            onBeforeQueued: function(files) {
                var uploadCount2 = 0;
                console.log(this)
                // `this` 是轮询到的文件, `files` 是所有文件
                if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                    alert('请上传图片');
                    return false; // 阻止文件添加
                }
                if (this.size > 5 * 1024 * 1024) {
                    alert('请上传不超过5M的图片');
                    return false;
                }
                if (files.length > 1) { // 防止一下子选择过多文件
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                if (uploadCount2 + 1 > 1) {
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                ++uploadCount2;

                // return true; // 阻止默认行为，不插入预览图的框架
            },
            onSuccess: function(ret) {
                if (ret.msg == 'success') {
                    var data = ret.content;
                    $("#uploader2 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png")
                    $("#addimg2").before(
                        '<div class="img_item">' +
                        '<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del2" alt="">' +
                        '<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info2"></div>');
                    idOcr("https://images1.fangxiaoer.com/"+data,"BACK")
                    moveErrorMsg("err1")
                    //身份证有效期
                    jeDate("#startime",{
                        //onClose:false,
                        format: "YYYY-MM-DD"
                    });
                    jeDate("#endtime",{
                        //onClose:false,
                        format: "YYYY-MM-DD"
                    });
                    imgdata2(); //数据处理

                } else {
                    var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
                    alert(msg);
                    imgdata1(); //数据处理
                    return false;
                }
                return true; // 阻止默认行为，不使用默认的成功态
            },
            onError: function(err) {
                // alert("上传失败");
                imgdata1(); //数据处理
                // return true; // 阻止默认行为，不使用默认的失败态
            }
        });

        // 身份证反面 删除
        $("#uploader2").on('click', '.img_del2', function() {
            $(this).parent().remove();
            $("#startime").val("")
            $("#endtime").val("")
            $("#startime").attr("readonly",true)
            $("#endtime").attr("readonly",true)
            imgdata2(); //数据处理
        });
        $("#uploader2").on('click', '.img_info', function() {
            weui.gallery(this.src);
        })
        function imgdata2() {
            var imgHeight = $('.img_item').width();
            $('.img_item').css('height', imgHeight + 'px')

            $('#uploadCount2').text($(".info2").length) //小区图数量
            uploadCount2 = $(".info2").length
            if($(".info2").length>=1){
                $('#addimg2').hide();
            }else{
                $('#addimg2').show();
            }
            var result = '';
            // |www.baidu.com;255, |www.baidu.com;256,
            $(".info2").each(function() {
                result = result + $(this).attr("data-id") + ',';
            });
            result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
            // $("#pic").val(result); //室内实拍图片赋值
        }

        // 身份证ocr认证
        function idOcr(imageUrl,side) {
            var timeMillis =  Date.parse(new Date())
            console.log(imageUrl)
            $.ajax({
                url:"/checkOCRIDCard",
                data: {
                    imageUrl:imageUrl,
                    side:side,
                    timeMillis:timeMillis
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if(side == "FRONT"){//正面传好
                        $("#idName").attr("readonly",false)
                        $("#idNum").attr("readonly",false)
                    }else{
                        /*$("#startime").attr("readonly",false)
                        $("#endtime").attr("readonly",false)*/
                    }
                    if (data.status == 1) {
                        data = data.content
                        // console.log(data)
                        if(side == "FRONT"){
                            $("#idName").val(data.PERSON_NAME)
                            $("#idNum").val(data.PERSON_ID.substring(0,18))
                            $("#idTextType").val("1")
                            $("#idTextType").attr("data-z","1")
                        }else{
                            var idTime = data.TIME_ZONE.split("-")
                            $("#startime").val(idTime[0].split(".").join("-"))
                            $("#endtime").val(idTime[1].split(".").join("-"))
                            $("#idTextType").val("2")
                            $("#idTextType").attr("data-f","1")
                        }
                        $(".realname_btn").addClass("hover")
                    }else{
                        errorMsg("err1",data.msg)
                    }
                }
            })
        }
        // 错误提醒
        function errorMsg(idname,errorMsg){
            $('.'+idname).addClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('<label class="error">'+errorMsg+'</label>')
            $('.'+idname).find(".errorBox").css("visibility","initial")
        }
        // 移除错误提醒
        function moveErrorMsg(idname){
            $('.'+idname).removeClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('')
            $('.'+idname).find(".errorBox").css("visibility","hidden")
        }

        // 点击提交实名审核
        $(document).on('click','#realname_btn.hover',function(){
            // console.log(sessionId1)
            var idCardFontPic = $(".info1").attr("data-id")
            var idCardBackPic = $(".info2").attr("data-id")
            var rName = $("#idName").val()
            var idCard = $("#idNum").val()
            var idCardStartDateStr = $("#startime").val()
            var idCardEndDateStr = $("#endtime").val()
            if(idCardFontPic == "" || idCardFontPic == undefined){
                errorMsg("err1","上传身份证正面")
            }else if(idCardBackPic == "" || idCardBackPic == undefined){
                errorMsg("err1","上传身份证反面")
            }else if(rName == ""){
                errorMsg("err2","请输入姓名")
            }else if(idCard == ""){
                errorMsg("err3","请输入身份证号")
            }else if(idCardStartDateStr == ""){
                errorMsg("err4","请输入身份证有效期--起始日期")
            }else if(idCardEndDateStr == ""){
                errorMsg("err4","身份证有效期--截至日期")
            }else{
                $.ajax({
                    url:"/memberIdCardAuthentication",
                    data: {
                        sessionId:sessionId1,
                        idCardFontPic:idCardFontPic,
                        idCardBackPic:idCardBackPic,
                        rName:rName,
                        idCard:idCard,
                        idCardStartDateStr:idCardStartDateStr,
                        idCardEndDateStr:idCardEndDateStr
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        console.log(data)
                        if (data.status == 1 ) {
                            if(data.msg == "error"){// 认证失败
                                $(".idErr").show()
                                $(".tc_full").show()
                                $(".idErr p").html(data.content+",是否继续发布？")
                                $(".tc_realname").hide()
                                $("body").css("overflow-y","inherit")
                            }else{//认证成功
                                $(".tc_trust").show()
                                $(".tc_full").show()
                                $(".tc_realname").hide()
                                $("body").css("overflow-y","inherit")
                            }

                        }else if(data.status == 0 ){// 认证三次后 失败
                            $(".errMsg").show()
                            $(".errMsg").html(data.msg)
                            // $(".realname_btn").removeClass("hover")
                        }

                    },
                    error:function (data) {//认证失败
                        console.log(data)
                        $(".errMsg").show()
                        $(".errMsg").html(data.msg)
                        $(".realname_btn").removeClass("hover")
                    }
                })
            }
        })

        $("input").focus(function () {
            var idName = $(this).parent().attr("data-id")
            moveErrorMsg(idName)
        })

        // 身份证信息和手机号不一致，点击 是 继续 继续发布
        $(document).on('click','.idErr-btn-yes',function(){
            $.ajax({
                url:"/confirmAuthenticationInfo",
                data: {
                    sessionId:sessionId1
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    if (data.status == 1) {
                        window.location.reload()
                    }
                }
            })
        })
        // 身份证信息和手机号不一致，点击 否 继续弹出认证弹窗
        $(document).on('click','.idErr-btn-no',function(){
            // $(".tc_realname").show()
            // $("body").css("overflow-y",tc_realname"clip")
            $(".idErr").hide()
            $(".tc_full").show()
            //身份证有效期
            jeDate("#startime",{
                //onClose:false,
                format: "YYYY-MM-DD"
            });
            jeDate("#endtime",{
                //onClose:false,
                format: "YYYY-MM-DD"
            });
        })

    })




</script>
<!--实名验证  结束-->



<script>
    $("#OwnerPhone").keyup(function () {
        this.value = this.value.replace(/\D/g, '')
    })
    $(".successMsgTcBtn").click(function () {


        $(".successMsgTc").hide()
        $(".successMsgFull").hide()

        window.location.href="https://sy.fangxiaoer.com/new_secondPublish";
    })


    // 关闭弹窗
    $(".closeSml").click(function(){
        // $(".successMsgTc").hide()
        // $(".successMsgFull").hide()
        window.location.href='/new_secondPublish'

        // 清空表单数据
        /*$(':input','#jsUpForm')
            .not(':button, :submit, :reset, :hidden')
            .val('')
            .removeAttr('checked')
            .removeAttr('selected');

        $("#HouseTrait").val("");      // 移除房源特色值
        $(".fyts a").removeClass("focus")  // 移除房源特色选中*/

    });
    $(".realRightClose").click(function () {
        $(".esfRightBottom").hide()
    })
</script>
<script type="text/javascript" src="/js/freepublish/form_xl.js"></script>

<!--底部-->
<div class="issuedInfo" th:include="fragment/fragment:: publish_footer1"></div>
<div class="cl"></div>
<div class="footer" th:include="fragment/fragment:: publish_footer2"></div>

<input type="hidden" id="newSubSearch">



<!--搜索框自动提示-->
<script th:inline="javascript">
    var host = "https://ltapi.fangxiaoer.com/apiv1/house/"
    // var host = "http://*************:8081/apiv1/house/"
    var dataSubId; //小区id
    var dataNewSub;//新旧小区
    var dataSubName;//小区名字
    var dataArchId; //楼栋ID
    var dataArchTxt;//楼栋名字
    var dataUnitId;//单元ID
    var dataUnitTxt;//单元内容
    var dataFloor ;//楼层
    var dataLph;//具体蓝牌号 1-2-1
    var showSubName ;//小区输入名字
    var showLphselect1 ;//展示的蓝牌号
    var showLphselect2 ;//展示的单元
    var showLphselect3 ;//展示的楼层
    var showLphselect4 ;//展示的户室
    $(document).ready(function() {
        $("#lph .lphselect  span").addClass("Font999")
        /*autoSearch("searchSubName");
        function autoSearch(searchId) {
            $("#" + searchId).autocomplete("/searchs", {
                multiple: false,
                max: 15,
                parse: function (data) {
                    return $.map(eval(data), function (row) {
                        console.log(data)
                        console.log(row)
                        return {
                            data: row,
                            value: row.subName,
                            id: row.subId,
                            result: row.subName
                        };
                    });
                },
                formatItem: function (item) {
                    return item.subName;
                }
            }).result(function (e, item) {
                $("#SubID").attr("value",item.subId);
                $("#SubName").attr("value",item.subName);
            });
        }*/

        // 错误提醒
        function errorMsg(idname,errorMsg,idLiName){
            $('.'+idname).addClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('<label for="'+idLiName+'" class="error">'+errorMsg+'</label>')
            $('.'+idname).find(".errorBox").css("visibility","initial")
        }
        // 移除错误提醒
        function moveErrorMsg(idname){
            $('.'+idname).removeClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('')
            $('.'+idname).find(".errorBox").css("visibility","hidden")
        }

        // 修改房源跳转进页面时回显房源蓝牌号
        var pageUrl = window.location.href;
        var toDetailid  = $("#detailid").val()
        var OwnerPhone = $("#OwnerPhoneH").val()

        if(pageUrl.indexOf("new_secondPublish") >= 0 ) {
            $("#lph").show()
            $(".nolph").show()
            $(".lddysh").hide()
        }else{
            if(toDetailid != "" ){//有蓝牌号
                $("#lph").show()
                $(".nolph").show()
                $(".lddysh").hide()
                $.ajax({
                    url: host + "viewDetailBlueForChange",
                    data: {
                        realId: toDetailid
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        if (data.status == 1) {
                            data = data.content
                            $("#lph span").removeClass("Font999")
                            $(".showLphselect1").text(data.buildNum)
                            $(".showLphselect2").text(data.unitName)
                            $(".showLphselect3").text(data.floorName)
                            $(".showLphselect4").text(data.roomAlias)
                        }
                    }
                })

            }else{//没有蓝牌号
                $("#lph").hide()+
                $(".lddysh").show()
            }
        }
        //输入小区进行
        $('#searchSubName').autocomplete({
            source: function (request, cb) {
                console.log(request.term)
                $.ajax({
                    url: host + "searchSubdistrictByName",
                    data: {
                        subName: request.term,
                        page: 1,
                        pageSize: 100
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        moveErrorMsg("new-sub-name")
                        $(".newSubList").html("")
                        $("#searchSubName").removeClass("error")
                        $(".subMsg").css("color", "#999999")
                        data = data.content
                        console.log(data);
                        if (data.length != 0) {
                            $(".newSubList").show()
                            var names = [];
                            for (var i = 0; i < data.length; i++) {
                                newSubList = '<li  class="newSubListLi" data-subId="' + data[i].subId + '" data-newSub="' + data[i].newSub + '">' + data[i].subName + '</li>'
                                $(".newSubList").prepend(newSubList)
                            }
                        }
                    }
                })
            }
        })

        // 清空蓝牌号
        function clearLph(a){
            if(a == 1){//点击选择小区
                $("#listArchId").html("")
                $("#listUnitId").html("")
                $("#listDetailId").html("")
                $("#listFloor").html("")
                $(".showLphselect1").text("请选择楼栋蓝牌号")
                $(".showLphselect2").text("请选择单元")
                $(".showLphselect3").text("请选择楼层")
                $(".showLphselect4").text("请选择具体户室")
            }else if(a == 2){//点击选择楼栋蓝牌号
                $("#listUnitId").html("")
                $("#listDetailId").html("")
                $("#listFloor").html("")
                $(".showLphselect2").text("请选择单元")
                $(".showLphselect3").text("请选择楼层")
                $(".showLphselect4").text("请选择具体户室")
            }else if(a == 3) {//点击选择单元
                $("#listDetailId").html("")
                $("#listFloor").html("")
                $(".showLphselect3").text("请选择楼层")
                $(".showLphselect4").text("请选择具体户室")
            }else if(a == 4) {//点击选择层
                $("#listFloor").html("")
                $(".showLphselect4").text("请选择具体户室")
            }
            $("#lph").find(".errorBox").html('')
        }

        //点击小区下拉选中
        $(document).on('click','.newSubListLi',function(){
            clearLph(1)
            dataSubName = $(this).text()
            dataSubId = $(this).attr("data-subId")
            dataNewSub = $(this).attr("data-newSub")
            $("#newsub").val(dataNewSub)
            $("#SubID").val(dataSubId)
            $("#subname").val(dataSubName)
            $("#searchSubName").val(dataSubName)
            showSubName = dataSubName
            console.log(dataSubName,dataSubId,dataNewSub)
            $(".newSubList").hide()
            if (dataNewSub == 0) { //没有蓝牌号
                $("#lph").hide()
                $(".lddysh").show()
            } else {
                $("#lph").show()
                $(".nolph").show()
                $(".lddysh").hide()
                $.ajax({
                    url: host + "viewArchBySubId",
                    data: {
                        subId: dataSubId
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        console.log(data)
                        if (data.status == 1) {
                            moveErrorMsg("new-sub-name")
                            $("#listArchId").html("")
                            data = data.content
                            for (var i = 0; i < data.length; i++) {
                                listArchId = '<li class="ArchIdLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                                $("#listArchId").append(listArchId)
                            }
                        }
                    }
                })
            }

        })

        // 点击楼栋号 展示下方下拉数据
        $(document).on('click','.showLphselect1',function(){
            $("#lph ul").hide()
            if( showSubName == "" || showSubName == undefined){
                errorMsg("new-sub-name","请从下拉框中选择小区","subname")
            }else{
                $("#listArchId").show()
            }
        })
        //选择楼栋 后 生成单元选项
        $(document).on('click','.ArchIdLi',function(){
            clearLph(2)
            dataArchId = $(this).attr("data-id")
            dataArchTxt = $(this).text()
            $(".showLphselect1").text(dataArchTxt)
            $("#listArchId").hide()
            showLphselect1  = dataArchTxt
            $.ajax({
                url: host + "viewUnitByArch",
                data: {
                    archId: dataArchId
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if (data.status == 1) {
                        moveErrorMsg("new-sub-lph")
                        $(".showLphselect1").removeClass("Font999")
                        $("#listUnitId").html("")
                        data = data.content
                        for (var i = 0; i < data.length; i++) {
                            listUnitId = '<li class="UnitIdLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                            $("#listUnitId").append(listUnitId)
                        }
                    }
                }
            })
        })

        // 点击单元
        $(document).on('click','.showLphselect2',function(){
            $("#lph ul").hide()
            if( showSubName == "" || showSubName == undefined){
                errorMsg("new-sub-name","请从下拉框中选择小区","subname")
            }else if ( showLphselect1 == "" || showLphselect1 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
            }else{
                $("#listUnitId").show()
            }
        })
        //选择单元 后 生成楼层
        $(document).on('click','.UnitIdLi',function(){
            clearLph(3)
            dataUnitId = $(this).attr("data-id")
            dataUnitTxt = $(this).html()
            $(".lphselect2 span").text(dataUnitTxt)
            $("#listUnitId").hide()
            showLphselect2  = dataUnitTxt
            $.ajax({
                url: host + "viewFloorByUnitAndArch",
                data: {
                    archId: dataArchId,
                    unit:dataUnitId
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if (data.status == 1) {
                        moveErrorMsg("new-sub-lph")
                        $(".showLphselect2").removeClass("Font999")
                        $(".listDetailId").html("")
                        data = data.content
                        for (var i = 0; i < data.length; i++) {
                            listUnitId = '<li class="listDetailLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                            $("#listDetailId").append(listUnitId)
                        }
                      }
                }
            })

        })
        // 点击楼层
        $(document).on('click','.showLphselect3',function(){
            $("#lph ul").hide()
            if( showSubName == "" || showSubName == undefined){
                errorMsg("new-sub-name","请从下拉框中选择小区","subname")
            }else if ( showLphselect1 == "" || showLphselect1 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
            }else if ( showLphselect2 == "" || showLphselect2 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择单元","lph")
            }else{
                $("#listDetailId").show()
            }
        })
        // 点击楼层显示下拉选项
        $(document).on('click','.listDetailLi',function(){
            clearLph(4)
            dataFloor = $(this).attr("data-id")
            dataUnitTxt = $(this).html()
            $(".lphselect3 span").text(dataUnitTxt)
            $("#listDetailId").hide()
            showLphselect3 = dataUnitTxt
            $.ajax({
                url: host + "viewDetailFilter",
                data: {
                    archId: dataArchId,
                    unit:dataUnitId,
                    floor:dataFloor
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if (data.status == 1) {
                        moveErrorMsg("new-sub-lph")
                        $(".showLphselect3").removeClass("Font999")
                        $("#listFloor").html("")
                        data = data.content
                        for (var i = 0; i < data.length; i++) {
                            listUnitId = '<li class="listFloorLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                            $("#listFloor").prepend(listUnitId)
                        }
                    }
                }
            })

        })
        // 点击户室
        $(document).on('click','.showLphselect4',function(){
            $("#lph ul").hide()
            if( showSubName == "" || showSubName == undefined){
                errorMsg("new-sub-name","请从下拉框中选择小区","subname")
            }else if ( showLphselect1 == "" || showLphselect1 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
            }else if ( showLphselect2 == "" || showLphselect2 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择单元","lph")
            }else if ( showLphselect3 == "" || showLphselect3 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择楼层","lph")
            }else{
                $("#listFloor").show()
            }
        })
        // 点击户室显示下拉选项
        $(document).on('click','.listFloorLi',function(){
            dataFloor = $(this).attr("data-id")
            $(".showLphselect4").removeClass("Font999")
            $("#detailid").val(dataFloor)
            dataLph = $(this).html()
            $(".lphselect4 span").text(dataLph)
            $("#listFloor").hide()
            $(".nolph").show()

        })
        //点击没有符合户室，清除掉已选
        $(document).on('click','.nolph span',function(){
            $("#lph").hide()
            $(".nolph").hide()
            $(".lddysh").show()
            $("#newsub").val("0")

        })

        // 点击关闭下拉内容
        $("*").not(".new-sub-name").on('click', function () {
            $(".newSubList").hide()
        })
        $("*").not(".lph").on('click', function () {
            $("#lph ul").hide()
        })

        $('#searchSubName').on('blur', function () {
            var searchVal = $('#searchSubName').val();
            var choiceVal = $('#subname').val();
            if (searchVal != choiceVal) {
                $('#SubID').val('')
                $('#subname').val('')
            }
        })

    })


    /*$(document).on('click','#fileImg label,.submitBtnFroms',function(){
        if(sessionId1 == null){//没登录 打开登录弹窗
           /!* $("#login").show()
            $(".tc_full").show()*!/
            return false;
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
            $(".tc_realname").show()
            $(".tc_full").show()
            $("body").css("overflow-y","clip")
            return false;
        }
    })*/
</script>




<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>

<script type="text/javascript">
    $(document).ready(function () {
    //划出下拉 关闭下拉
    $(".my_xl").bind("mouseleave",function(){
        $(this).find(".my_xl_list").hide()
    })

        //获取验证码
        var rbtn = $("#ReSendValidateCoad");
        //获取验证码按钮事件
        rbtn.click(function () {
            var tmobile = $.trim($("#OwnerPhone").val());
            var reg = new RegExp("^1[0-9]{10}$", "ig");
            if (!reg.test(tmobile) || tmobile == "") {
                alert("请正确填写11位手机号码!");
                return false;
            } else {

             // 定义验证成功的回调函数
            const onSuccessCallback = function() {
                console.log('验证码验证成功！');
                // 可以在这里添加验证成功后的逻辑，比如提交表单等
                time();
                try {
                const url = '/sendSmsCode'
                buildSignedHeaders('POST', url, '')
                    .then(headers => {
                        headers['clickType'] = 'my站';
                        $.ajax({
                            type: "POST",
                            contentType: 'application/x-www-form-urlencoded',
                            headers: headers,
                            data: { action: "Send", mobile: tmobile },
                            url: "/sendSmsCode",
                            async: false,
                            success: function(data) {
                            console.log(data)
                            r = data;
                            },
                            error: function(e) {
                                alert(e);
                            }
                        });
                    })
                    .catch(error => {
                        console.error('构建签名失败:', error);
                    });
                } catch (e) {
                        console.log(e.message);
                }

            };
             console.log('点击点击！');
            // 调用 initAliCaptcha 方法
            initAliCaptcha(onSuccessCallback).then(result => {
                console.log('验证码初始化完成22:', result);
                captchaInstance.show();
            }).catch(error => {
                console.error('验证码初始化失败:', error);
            });
          }
        });
});

//验证码倒计时
var wait = 60;
function time(o) {
    if (wait == 0) {
        $("#validateCode").hide();
        $("#ReSendValidateCoad").show().html("重新获取");
        wait = 60;
    } else {
        $("#validateCode").show().html("在" + wait + "秒后重发");
        $("#ReSendValidateCoad").hide();
        wait--;
        setTimeout(function () {
                time(o);
            },
            1000);
    }
}
</script>
</body>
</html>

