<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳租房信息发布_免费信息发布_个人房源 - 房小二网</title>
    <meta name="keywords" content="租房信息发布,个人房源,沈阳租房信息,租房中介,免费信息发布"/>
    <meta name="description" content="房小二网为您提供专业贴心的租房服务，为您提供免费租房信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/pubSale.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
<!--    <link href="https://static.fangxiaoer.com/web/images/sy/check/form1.css?t=0902" rel="stylesheet" type="text/css" />-->
    <link href="https://static.fangxiaoer.com/web/styles/sy/rental/form1.css?t=12" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.validate.min1.js"></script>
    <script type="text/javascript" src="/js/freepublish/personal.js"></script>
    <script type="text/javascript" src="/js/AjaxforJquery.js"></script>
    <script type="text/javascript" src="/js/jquery.autocomplete.js"></script>
    <style>
        .hide{ display: none !important;}
        .fk { width: 18px; height: 18px; position: absolute; top: 66px; right: -30px; cursor: pointer; user-select: none; background-image: url(https://static.fangxiaoer.com/web/images/sy/check/kot.png); background-size: 100%; background-repeat: no-repeat; background-position: center; }
        .cben { position: absolute; z-index: 99999999; top: -79px; right: -295px; box-shadow: 0px 0px 17px #6a6a6c; border-radius: 5px; background-color: #fff; padding: 5px; box-sizing: border-box; display: none; }
        .cben img { height: 350px; border-radius: 5px; }
        .cil { width: 0; height: 0; border-top: 10px solid transparent; border-right: 13px solid #fff; border-bottom: 10px solid transparent; position: absolute; left: -13px; top: 144px; }
        .cbg { width: 38px; height: 39px; background-image: url(https://static.fangxiaoer.com/web/images/sy/check/icon_close.png); background-size: 100%; background-repeat: no-repeat; background-position: center; position: absolute; left: 0; right: 0; bottom: -108px; margin: auto; cursor: pointer; user-select: none; }
        .fi2 { width: 14px; height: 14px; position: absolute; top: 26px; right: -3px; cursor: pointer; user-select: none; background-image: url(https://static.fangxiaoer.com/web/images/sy/check/icon_notes.png); background-size: 100%; background-repeat: no-repeat; background-position: center; }
        .fi2m { position: absolute; top: -3px; right: -286px; width: 244px; height: 50px; background: #FFFFFF; border-radius: 9px; z-index: 99999; box-shadow: 0px 0px 12px #8b8b8f; line-height: 23px; padding: 13px; font-size: 14px; font-family: Microsoft YaHei; font-weight: 400; color: #101D37; display: none; }
        .clf2 { display: block; width: 0; height: 0; border-top: 10px solid transparent; border-right: 11px solid #fff; border-bottom: 10px solid transparent; position: absolute; left: -11px; top: 25px; }
        .cgm { display: inline-block; padding-left: 43px; box-sizing: border-box; height: 65px; }
        .cgmi { display: inline-block; font-size: 14px; cursor: pointer; user-select: none; font-family: Microsoft YaHei; font-weight: 400; color: #9399A5; position: relative; }
        .cgmi em { width: 14px; height: 14px; display: inline-block; background-image: url(https://static.fangxiaoer.com/web/images/sy/check/kon.png); background-size: 100%; background-repeat: no-repeat; background-position: center; position: absolute; top: 26px; left: -20px; cursor: pointer; user-select: none; }
        .cgmi:nth-child(1) { margin-right: 55px; }
        .cgmi:nth-child(2) { margin-right: 55px; }
        .kon em { background-image: url(https://static.fangxiaoer.com/web/images/sy/check/koy.png) !important; }
        .showarg { display: none; }

        input::-webkit-input-placeholder { color: #9399A5 !important;}
        input:-moz-placeholder { color: #9399A5 !important;}
        input::-moz-placeholder { color: #9399A5 !important;}
        input:-ms-input-placeholder {color: #9399A5 !important;}

        textarea::-webkit-input-placeholder { color: #9399A5 !important;}
        textarea:-moz-placeholder { color: #9399A5 !important;}
        textarea::-moz-placeholder { color: #9399A5 !important;}
        textarea:-ms-input-placeholder {color: #9399A5 !important;}

        .vx{ display: none !important;}
        .vmright{ height: unset !important; padding-bottom: 42px;}

        .g_ftmx{ text-align: center; font-size: 12px;font-family: Microsoft YaHei-Regular, Microsoft YaHei; font-weight: 400; color: #666666;line-height: 16px; margin-left: 0 !important;}
        .budwai{ position: absolute;
            right: 0;
            top: 0;
            width: 88px;
            height: 20px;
            background: #FF7534;
            border-radius: 0px 5px 0px 0px;
            opacity: 1;
            font-size: 10px;
            color: #fff;
            text-align: center;
            line-height: 20px;
            z-index: 9;}
        .vf a{ color: #Ff5200 !important;}
    </style>
</head>
<body>
<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<!--<div class="title">为您提供专业贴心的租房服务<p>免费咨询电话：400-893-9709</p></div>-->


<div class="kuang gvsp">
    <div class="vmain">

        <div class="vmleft">
            <h5 class="newRealH5">房源审核</h5>
            <div class="vtap">
                <div class="vsp act" i="m1">我是产权人</div>
                <div class="vsp" i="m2">我帮他人租房</div>
            </div>
            <!--我是产权人-->
            <div class="vtm1">
                <div class="vsf" style="display: none">
                    <div class="vtit">身份认证</div>
                    <div class="vi"><em>产权人姓名</em><input class="proname" placeholder="请输入产权人姓名"></div>
                    <div class="vi"><em>我的身份证号</em><input class="procard" placeholder="请输入身份证" style="width: 260px;"></div>
                    <div class="vu">
                        <em>产权人身份证照片</em>
                        <div class="vum">
                            <div class="vumi mright50 uploadz">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/img_id_positive.png" class="imgsz"/></div>
                                <div class="vuf vfa">上传身份证正面</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadFront" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadf">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/img_id_back.png" class="imgsf"/></div>
                                <div class="vuf vfb">上传身份证反面</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadBack" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                </div>
                <div class="vsf">
<!--                    <div class="vtit">房本认证</div>-->
                    <div class="vi"><em>小区名称</em><input class="arname" value="铁路小区" disabled></div>
                    <div class="vi gpai"><em>门牌信息</em><input class="proBlue" placeholder="请输入楼栋外墙体的蓝牌号信息 如：中山路397号" style="width: 55%;"></div>
                    <!--<div class="vi">
                        <div class="vie">
                            户室号
                            <b>*</b>
                            <span><i>请与房本信</i><i>息保持一致</i></span>
                        </div>
                        <input placeholder="单元" class="vit prounit">
                        <input placeholder="楼层" class="vit profloor">
                        <input placeholder="门牌号" class="vit prodoor">
                    </div>-->
                    <div class="vi">
                        <div class="vie" style="width: 117px;">
                            <i style="color:#ff5200">*</i>权属证件类型
                            <div class="fi2"></div>
                            <div class="fi2m"><i class="clf2"></i>请上传房产证、商品房购买合同、老房本</div>
                        </div>
                        <div class="cgm acth" dt="k1">
                            <div class="cgmi cgk kon" k="k1"><em></em>房本原件</div>
<!--                            <div class="cgmi cgk" k="k2"><em></em>无房产证号（老房本）</div>-->
                            <div class="cgmi cgk" k="k3"><em></em>商品房买卖合同</div>
                        </div>
                    </div>
                    <!--<div class="vi showvi">
                        <div class="vie">房产证号<div class="fi"></div></div>
                        <input placeholder="请输入房产证号" class="proCertificate" style="width: 260px;">
                    </div>-->
                    <div class="vu shwoper">
                        <em style="color: #666666">
                            <i style="color:#ff5200">*</i>请上传产权人房产照片
                            <span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的房产证信息将不对外展示</span>
                        </em>
                        <div class="vum" style="position: relative; text-align: left; ">
                            <div class="vumi mright50 uploadCard" style="height: 160px;">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png" class="cardz" style="height: 160px; border-radius: 10px;"/></div>
                                <div class="vuf">上传房本原件首页</div>
                                <div class="budwai">不对外展示</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInc" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadCardf .mright50" style="height: 160px;">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/7.png" class="cardf" style="height: 160px; border-radius: 10px;"/></div>
                                <div class="vuf">上传房本原件信息页</div>
                                <div class="budwai">不对外展示</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInf" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu shwoper2" style="display: none;">
                        <em>产权人房产照片</em>
                        <div class="vum" style="position: relative; ">
                            <div class="vumi mright50 uploadCard2">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png" class="cardz2"/></div>
                                <div class="vuf">上传房本原件首页</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInc2" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadCardf2 .mright50">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png" class="cardf2"/></div>
                                <div class="vuf">上传房本原件信息页</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInf2" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu showarg">
                        <em style="color: #666666">
                            <i style="color:#ff5200">*</i>请上传商品房买卖合同照片
                            <span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的商品房买卖合同将不对外展示</span>
                        </em>
                        <div class="vum" style="position: relative; text-align: left; ">
                            <div class="vumi mright50 uploadContract" style="position: relative; height: 160px;">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png" dat="" class="vcont" style="height: 160px; border-radius: 10px;"/></div>
                                <div class="vuf">上传买受人信息页</div>
                                <div class="budwai">不对外展示</div>
                                <div class="vclose"></div>
                                <div class="fk f1"></div>
                                <div class="cben m1">
                                    <div class="cil"></div>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/check/kor.png"/>
                                </div>
                            </div>
                            <input type="file" id="uploadInt" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadContract2" style="position: relative; height: 160px;">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png" dat="" class="vcont2" style="height: 160px; border-radius: 10px;"/></div>
                                <div class="vuf" >上传房屋基本信息页</div>
                                <div class="budwai">不对外展示</div>
                                <div class="vclose"></div>
                                <div class="fk f2"></div>
                                <div class="cben m2">
                                    <div class="cil"></div>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/check/kor.png"/>
                                </div>
                            </div>
                            <input type="file" id="uploadInt2" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu">
                        <em>补充认证描述<i style="color: #9399A5">（选填）</i></em>
                        <textarea class="prosupplement" placeholder="如：提供其他法律有效的证件，来证明此房源真实性和有效性"></textarea>
                    </div>
                    <div class="vu" style="text-align: left;">
                        <em>补充认证</em>
                        <div class="vumi mright50 uploadbcrz1" style="width: 102px;height: 102px">
                            <div class="vmimg" ><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/8.png" class="imgbcrz1" style="height: 102px;"/></div>
                            <div class="vclose"></div>
                        </div>
                        <input type="file" id="uploadBCRZ1" accept="image/*" style="display: none !important;">
                    </div>
                    <div class="vf">
                        <em class="agre"></em><i>同意</i><a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房本认证隐私条款》</a>
                    </div>
                    <div class="subt propbt">提交</div>
                </div>
            </div>





            <!--我帮他人卖房-->
            <div class="vtm2">
                <div class="vsf" style="display: none">
                    <div class="vtit">身份认证</div>
                    <div class="vi"><em>我的姓名</em><input class="helpmyname" placeholder="请输入姓名"></div>
                    <div class="vi"><em>我的身份证号</em><input placeholder="请输入身份证号" class="helpmycard" style="width: 260px;"></div>

                    <div class="vi"><em>产权人姓名</em><input class="helpname" placeholder="请输入产权人姓名"></div>
                    <div class="vi"><em>产权人身份证号</em><input placeholder="请输入产权人身份证号" class="helpcard" style="width: 260px;"></div>
                    <div class="vu">
                        <em>产权人身份证照片</em>
                        <div class="vum">
                            <div class="vumi mright50 helpCardz">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/img_id_positive.png" class="imghcz"/></div>
                                <div class="vuf">上传身份证正面</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="helpupz" accept="image/*" style="display: none !important;">
                            <div class="vumi helpCardf">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/img_id_back.png" class="imghcf"/></div>
                                <div class="vuf">上传身份证反面</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="helpupf" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                </div>
                <div class="vsf">
<!--                    <div class="vtit">房本认证</div>-->
                    <div class="vi">
                        <em><i style="color:#ff5200">*</i>与产权人关系</em>
                        <div class="navht" id="nav" >
                            <p class="set helprelation" id="00">请选择关系</p>
                            <ul class="newkk">
                                <li></li>
                            </ul>
                        </div>
                    </div>
                    <div class="vi"><em>小区名称</em><input class="helparea" value="铁路小区" disabled></div>
                    <div class="vi gpai"><em>门牌信息</em><input placeholder="请输入楼栋外墙体的蓝牌号信息 如：中山路397号" style="width: 55%;" class="helpBlue"></div>
                    <!--<div class="vi">
                        <div class="vie">
                            户室号
                            <b>*</b>
                            <span><i>请与房本信</i><i>息保持一致</i></span>
                        </div>
                        <input placeholder="单元" class="vit helpunit">
                        <input placeholder="楼层" class="vit helpfloor">
                        <input placeholder="门牌号" class="vit helpdoor">
                    </div>-->
                    <div class="vi">
                        <div class="vie" style="width: 117px;">
                            <i style="color:#ff5200">*</i>权属证件类型
                            <div class="fi2"></div>
                            <div class="fi2m"><i class="clf2"></i>请上传房产证、商品房购买合同、老房本</div>
                        </div>
                        <div class="cgm acth2" dt="h1">
                            <div class="cgmi cgh kon" k="h1"><em></em>房本原件</div>
<!--                            <div class="cgmi cgh" k="h2"><em></em>无房产证号（老房本）</div>-->
                            <div class="cgmi cgh" k="h3"><em></em>商品房买卖合同</div>
                        </div>
                    </div>
                    <!--<div class="vi showvi3">
                        <div class="vie">房产证号<div class="fi"></div></div>
                        <input placeholder="请输入房产证号" class="helpCertificate" style="width: 260px;">
                    </div>-->
                    <div class="vu shwoper3">
                        <em style="color: #666666; margin: 7px 0 25px 0;"><i style="color:#ff5200">*</i>请上传产权人房产照片<span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的房产证信息将不对外展示</span></em>
                        <div class="vum">
                            <div class="vumi mright50 helpHouz">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png" class="imghz"/></div>
                                <div class="vuf">上传房本原件首页</div>
                                <div class="budwai">不对外展示</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="helphz" accept="image/*" style="display: none !important;">
                            <div class="vumi helpHouf">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/7.png" class="imghf"/></div>
                                <div class="vuf">上传房本原件信息页</div>
                                <div class="budwai">不对外展示</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="helphf" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu shwoper33" style="display: none;">
                        <em>产权人房产照片</em>
                        <div class="vum" style="position: relative; ">
                            <div class="vumi mright50 uploadCard3">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png" class="cardz3"/></div>
                                <div class="vuf">上传房本原件首页</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInc3" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadCardf3 .mright50">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png" class="cardf3"/></div>
                                <div class="vuf">上传房本原件信息页</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInf3" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu showarg3" style="display: none" >
                        <em style="color: #666666; margin: 7px 0 25px 0;"><i style="color:#ff5200">*</i>请上传商品房买卖合同照片<span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的商品房买卖合同将不对外展示</span></em>
                        <div class="vum" style="position: relative; ">
                            <div class="vumi mright50 uploadContract4" style="position: relative;">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png" dat="" class="vcont4"/></div>
                                <div class="vuf">上传买受人信息页</div>
                                <div class="budwai">不对外展示</div>
                                <div class="vclose"></div>
                                <div class="fk f1"></div>
                                <div class="cben m1">
                                    <div class="cil"></div>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/check/kor.png"/>
                                </div>
                            </div>
                            <input type="file" id="uploadInt4" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadContract5" style="position: relative;">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png" dat="" class="vcont5"/></div>
                                <div class="vuf">上传房屋基本信息页</div>
                                <div class="budwai">不对外展示</div>
                                <div class="vclose"></div>
                                <div class="fk f2"></div>
                                <div class="cben m2">
                                    <div class="cil"></div>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/check/kor.png"/>
                                </div>
                            </div>
                            <input type="file" id="uploadInt5" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu">
                        <em>补充认证描述<i style="color: #9399A5">（选填）</i></em>
                        <textarea class="helpsupplement" placeholder="如：提供其他法律有效的证件，来证明此房源真实性和有效性"></textarea>
                    </div>
                    <div class="vu">
                        <em>补充认证</em>
                        <div class="vumi mright50 uploadbcrz2" style="width: 102px;height: 102px">
                            <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/8.png" class="imgbcrz2" style="height: 102px;"></div>
                            <div class="vclose" style="display: none;"></div>
                        </div>
                        <input type="file" id="uploadBCRZ2" accept="image/*" style="display: none !important;">
                    </div>
                    <div class="vf">
                        <em class="agre2"></em><i>同意</i><a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房本认证隐私条款》</a>
                    </div>
                    <div class="subt helpBtn">提交</div>
                </div>
            </div>
        </div>

        <!--其他认证方式-->
        <div class="vmright" style="width: 346px; box-shadow: unset;">
            <div class="shs_cont1">
                <h4>出租流程</h4>
                <ul class="shs_cont1_ul">
                    <i class="gun"></i>
                    <i class="gun2"></i>
                    <li class="shs_cont1_li"><i class="cont1_icon"></i><div><h4>填写信息</h4><p>填写房源真实信息</p></div></li>
                    <li class="shs_cont1_li"><i class="cont1_icon"></i><div><h4>房源审核<s>进行中</s></h4><p>上传审核资料，核实录入的房源信息</p></div></li>
                    <li class="shs_cont1_li"><i class="cont1_icon cont1_icon2"></i><div><h4>房源展示</h4><p>房源在房小二网站上展示</p></div></li>
                </ul>
            </div>
            <div class="shs_cont2">
                <h4>其它认证方式</h4>
                <div class="shs_cont2_div" style="padding-left: 0;">
                    <!--<div class="shs_cont2_img">
                        <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/1.png" alt="">
                        <p>扫码到小程序认证微信扫一扫直接认证</p>
                    </div>-->
                    <div class="shs_cont2_img" style="float: unset; margin: 0 auto;">
                        <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/2.png" alt="">
                        <!--                        <p>扫码下载打开APP<br>进入我要卖房 — 我的房产进行认证</p>-->
                    </div>
                    <div class="g_ftmx">扫码下载打开APP<br>进入我要卖房 — 我的房产进行认证</div>
                </div>
            </div>
        </div>

        <div class="cl"></div>
    </div>
</div>

<!--认证成功弹窗-->
<div class="successMsgTc w410">
    <div class="success_m">
        <div class="closeSml"></div>
        <div class="ics"></div>
        <div class="sumsg">房源认证成功</div>
        <div class="smo"><span>房小二网客服正在核实基本消息</span><span>认证成功后前排展示，增加10倍曝光量</span></div>
        <div class="chbtn" onclick="location.href='https://my.fangxiaoer.com/house/houses0_1'">管理房产</div>
        <div class="multiple"><i class="left0"></i>真房源认证<i class="right0"></i></div>
        <div class="muft">排序优先<i>·</i>增加曝光<i>·</i>独有标识</div>
    </div>
</div>
<!--房产证号弹窗-->
<div class="seek">
    <div class="skm">
        <div class="stop">房产证号哪里找？</div>
        <div class="smoo">1.请输入房产证首页或详情页的编号</div>
        <img src="https://static.fangxiaoer.com/web/images/sy/check/img_home.png" />
        <div class="smoo">2.如果只有不动产单元号，也可输入</div>
        <img src="https://static.fangxiaoer.com/web/images/sy/check/img_next.png" />
        <div class="smoo">3.如果较早的房产证没有编号，请勾选“无房产证”</div>
        <div class="close_sk"></div>
    </div>
</div>
<div class="successMsgFull"></div>
<input type="hidden" id="houseId" th:value="${houseId}">
<!--底部-->
<div class="issuedInfo" th:include="fragment/fragment:: publish_footer1"></div>
<div class="cl"></div>
<div class="footer" th:include="fragment/fragment:: publish_footer2"></div>


<script type="application/javascript">
    var saleHouseId=$("#houseId").val();
    var idents=0//0产权人 1帮他人卖房



    //备案合同弹窗
    $('.f1').mouseover(function() {
        $(".m1").show()
    });
    $('.f1').mouseout(function() {
        $(".m1").hide()
    });
    $('.f2').mouseover(function() {
        $(".m2").show()
    });
    $('.f2').mouseout(function() {
        $(".m2").hide()
    });
    //权属证件类型 弹窗
    $('.fi2').mouseover(function() {
        $(".fi2m").show()
    });
    $('.fi2').mouseout(function() {
        $(".fi2m").hide()
    });
    //产权人 租房 切换
    $(".cgk").click(function(){
        var th=$(this).attr('k')
        $(this).addClass('kon')
        $(this).siblings().removeClass('kon')
        $(this).parent().attr('dt',th)
        //房本原件
        if(th=='k1'){
            $(".showarg").hide()//合同
            $('.proCertificate').removeAttr('disabled')
            $(".showvi").show()//房产证号
            $(".shwoper").show()//房证
            $(".shwoper2").hide()//老房证
        }
        //无房证号
        if(th=='k2'){
            $('.proCertificate').attr('disabled',"disabled").val('')
            $(".showvi").hide()//房产证号
            $(".showarg").hide()//合同
            $(".shwoper").hide()//房证
            $(".shwoper2").show()//老房证
        }
        //买卖合同
        if(th=='k3'){
            $(".showarg").show()//合同
            $(".shwoper").hide()//房证
            $('.proCertificate').attr('disabled',"disabled").val('')
            $(".showvi").hide()//房产证号
            $(".shwoper2").hide()//老房证
        }
    })

    //帮他人 租房 切换
    $(".cgh").click(function(){
        var th=$(this).attr('k')
        $(this).addClass('kon')
        $(this).siblings().removeClass('kon')
        $(this).parent().attr('dt',th)
        //房本原件
        if(th=='h1'){
            $(".showarg3").hide()//合同
            $('.helpCertificate').removeAttr('disabled')
            $(".showvi3").show()//房产证号
            $(".shwoper3").show()//房证
            $(".shwoper33").hide()//老房证
        }
        //无房证号
        if(th=='h2'){
            $('.helpCertificate').attr('disabled',"disabled").val('')
            $(".showvi3").hide()//房产证号
            $(".showarg3").hide()//合同
            $(".shwoper3").hide()//房证
            $(".shwoper33").show()//老房证
        }
        //买卖合同
        if(th=='h3'){
            $(".showarg3").show()//合同
            $(".shwoper3").hide()//房证
            $('.helpCertificate').attr('disabled',"disabled").val('')
            $(".showvi3").hide()//房产证号
            $(".shwoper33").hide()//老房证
        }
    })


    //房源账号是否认证
    $.ajax({
        type: "POST",
        async: false,
        url: "/checkRealEstate",
        data:{"houseId":saleHouseId,"houseType":2},
        dataType : 'json',
        headers : {
            'Content-Type' : 'application/x-www-form-urlencoded'
        },
        success: function (data) {
            var res=data.content
            console.log(data)
            //是否认证 0未认证 1认证
            if(res.hasIndentity==1){
                //产权人
                $(".proname").attr('disabled',"disabled").val(data.content.hasChecked.split(' ')[0])
                $(".procard").attr('disabled',"disabled").val(data.content.hasChecked.split(' ')[1])
                $(".vumi").removeClass('uploadz')//取消点击上传
                $(".vumi").removeClass('uploadf')//取消点击上传
                $(".vumi").find('.vclose').hide()//关闭按钮
                $(".imgsz").attr({'src': 'https://images1.fangxiaoer.com/' + data.content.cachePic[0].url + '/big_fxr_watermark','dat':data.content.cachePic[0].url})//身份证正面
                $(".imgsf").attr({'src': 'https://images1.fangxiaoer.com/' + data.content.cachePic[1].url + '/big_fxr_watermark','dat':data.content.cachePic[1].url})
                $(".vfa").text('身份证正面')
                $(".vfb").text('身份证反面')
                //帮他人
                $(".helpmyname").attr('disabled',"disabled").val(data.content.hasChecked.split(' ')[0])
                $(".helpmycard").attr('disabled',"disabled").val(data.content.hasChecked.split(' ')[1])


                //设置门牌信息
                if(res.blueLabel!='' && res.blueLabel!=null){
                    $(".proBlue,.helpBlue").attr("disabled","disabled");
                    $(".proBlue").val(res.blueLabel)
                    $(".helpBlue").val(res.blueLabel)
                }else{
                    $(".gpai").hide()
                }

                //设置身份证 url
                $(".imgsz").attr('dat',data.content.cachePic[0].url)
                $(".imgsf").attr('dat',data.content.cachePic[1].url)

                $(".imghcz").attr('dat',data.content.cachePic[0].url)
                $(".imghcf").attr('dat',data.content.cachePic[1].url)

            }
            $(".arname").val(res.subName)//小区名称
            $(".helparea").val(res.subName)//小区名称
        }
    });
    //与产权人关系
    $.ajax({
        type: "POST",
        async: false,
        url: "/relationShipFilter",
        data:{},
        dataType : 'json',
        headers : {
            'Content-Type' : 'application/x-www-form-urlencoded'
        },
        success: function (data) {
            var res=data.content
            console.log(res)
            $(".newkk").empty()
            for(var i=0;i<res.length;i++){
                var str="<li id="+res[i].id+">"+res[i].name+"</li>"
                $(".newkk").append(str)
            }
        }
    });
    //提交修改真房源  产权人
    $(".propbt").click(function(){
        var proname=$(".proname").val()//产权人姓名
        var procard=$(".procard").val()//产权人身份证
        var proarName=$(".arname").val()//小区名称
        var proBlue=$('.proBlue').val()//楼栋蓝牌号
        var prounit=$('.prounit').val()//单元
        var profloor=$('.profloor').val()//楼层
        var prodoor=$('.prodoor').val()//门牌号
        var proCertificate=$('.proCertificate').val()//房产证号
        var prosupplement=$('.prosupplement').val()//补充说明

        var proFront=$(".imgsz").attr('dat')//身份证
        var proBack=$(".imgsf").attr('dat')
        var proCardFront=$(".cardz").attr('dat')//房证
        var proCardBack=$(".cardf").attr('dat')
        var proCardFront2=$(".cardz2").attr('dat')//老房证
        var proCardBack2=$(".cardf2").attr('dat')

        var contractImg=$(".vcont").attr('dat')//商品房买卖合同
        var contractImg2=$(".vcont2").attr('dat')//商品房买卖合同
        var imageType=$(".acth").attr('dt')//权属证件类型
        var conimgt=''//0-房本原件，1-无房产证号（老房本），2-商品房买卖合同

        //补充认证
        var bcsmImg1 = $(".imgbcrz1").attr('dat')
        if(bcsmImg1 == undefined){
            bcsmImg1 = ""
        }

        //表单验证
        /*var regName =/^[\u4e00-\u9fa5]{2,4}$/;
        if(!regName.test(proname)){
            if($(".proname").attr('disabled')!='disabled'){
                alert('请正确输入产权人姓名！');
                return false;
            }
        }*/
        /*var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if(reg.test(procard) === false)
        {
            if($(".procard").attr('disabled')!='disabled'){
                alert("请正确输入身份证号！");
                return
            }
        }*/
        /*if($(".imgsz").attr('dat')==undefined && $(".imgsz").attr('dat')!='def' || $(".imgsz").attr('dat')==''){
            alert('请上传产权人身份证正面！')
            return
        }
        if($(".imgsf").attr('dat')==undefined && $(".imgsf").attr('dat')!='def' || $(".imgsf").attr('dat')==''){
            alert('请上传产权人身份证反面！')
            return
        }*/
        /*if(proBlue==''){
            alert('请正确输入门牌信息！')
            return
        }*/
        /*if(prounit==''){
            alert('请正确输入单元信息！')
            return
        }
        if(profloor==''){
            alert('请正确输入楼层信息！')
            return
        }
        if(prodoor==''){
            alert('请正确输入门牌号！')
            return
        }*/
        if(imageType=='k1'){
            conimgt=0
            if(proCertificate=='' && $(".proCertificate").attr('disabled')!='disabled'){
                alert('请正确输入房产证号！')
                return
            }
            if($(".cardz").attr('dat')==undefined || $(".cardz").attr('dat')==''){
                alert('请上传房本原件首页！')
                return
            }
            if($(".cardf").attr('dat')==undefined || $(".cardf").attr('dat')==''){
                alert('请上传房本原件信息页！')
                return
            }
        }
        /*if(imageType=='k2'){
            conimgt=1
            if($(".cardz2").attr('dat')==undefined || $(".cardz2").attr('dat')==''){
                alert('请上传房本原件首页(老房本)！')
                return
            }
            if($(".cardf2").attr('dat')==undefined || $(".cardf2").attr('dat')==''){
                alert('请上传房本原件信息页(老房本)！')
                return
            }
        }*/
        if(imageType=='k3'){
            conimgt=2
            if($(".vcont").attr('dat')=='' && $(".vcont2").attr('dat')==''){
                alert('请上传商品房购买备案合同！')
                return
            }
        }
        if($(".agre").hasClass('fv')==false){
            alert('请仔细阅读并同意《房本认证隐私条款》')
            return
        }

        var imgAry=[]
        if(imageType=='k1'){
            if(bcsmImg1==''){
                imgAry=[
                    {'picType':0,'picName':'正面','url':proFront},
                    {'picType':5,'picName':'背面','url':proBack},
                    {'picType':10,'picName':'房产证首页','url':proCardFront},
                    {'picType':15,'picName':'房产证信息页','url':proCardBack},
                ]
            }else{
                imgAry=[
                    {'picType':0,'picName':'正面','url':proFront},
                    {'picType':5,'picName':'背面','url':proBack},
                    {'picType':10,'picName':'房产证首页','url':proCardFront},
                    {'picType':15,'picName':'房产证信息页','url':proCardBack},
                    {'picType':20,'picName':'补充说明','url':bcsmImg1},
                ]
            }

        }
        if(imageType=='k2'){
            imgAry=[
                {'picType':0,'picName':'正面','url':proFront},
                {'picType':5,'picName':'背面','url':proBack},
                {'picType':10,'picName':'房产证首页','url':proCardFront2},
                {'picType':15,'picName':'房产证信息页','url':proCardBack2},
            ]
        }
        if(imageType=='k3'){
            if(contractImg==''){
                if(bcsmImg1==''){
                    imgAry=[
                        {'picType':0,'picName':'正面','url':proFront},
                        {'picType':5,'picName':'背面','url':proBack},
                        {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg2},
                    ]
                }else{
                    imgAry=[
                        {'picType':0,'picName':'正面','url':proFront},
                        {'picType':5,'picName':'背面','url':proBack},
                        {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg2},
                        {'picType':20,'picName':'补充说明','url':bcsmImg1},
                    ]
                }
            }
            if(contractImg2==''){
                if(bcsmImg1==''){
                    imgAry=[
                        {'picType':0,'picName':'正面','url':proFront},
                        {'picType':5,'picName':'背面','url':proBack},
                        {'picType':10,'picName':'商品房买卖合同首页','url':contractImg},
                    ]
                }else{
                    imgAry=[
                        {'picType':0,'picName':'正面','url':proFront},
                        {'picType':5,'picName':'背面','url':proBack},
                        {'picType':10,'picName':'商品房买卖合同首页','url':contractImg},
                        {'picType':20,'picName':'补充说明','url':bcsmImg1},
                    ]
                }

            }
            if(contractImg!='' && contractImg2!=''){
                if(bcsmImg1==''){
                    imgAry=[
                        {'picType':0,'picName':'正面','url':proFront},
                        {'picType':5,'picName':'背面','url':proBack},
                        {'picType':10,'picName':'商品房买卖合同首页','url':contractImg},
                        {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg2},
                    ]
                }else{
                    imgAry=[
                        {'picType':0,'picName':'正面','url':proFront},
                        {'picType':5,'picName':'背面','url':proBack},
                        {'picType':10,'picName':'商品房买卖合同首页','url':contractImg},
                        {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg2},
                        {'picType':20,'picName':'补充说明','url':bcsmImg1},
                    ]
                }

            }
        }

        $.ajax({
            type: "POST",
            async: false,
            url: "/publishRealEstate",
            data:{
                "certificateType":conimgt,//权属证件类型 0房本原件，1无房产证号，2商品房买卖合同
                "houseType":2,
                "id": '',//房源id
                "identity": idents,//身份 0产权人 1帮他人
                // "homeOwnerName": proname,//产权人姓名
                "homeOwnerID": procard,//产权人身份证号
                // "relationship": saleHouseId,//与产权人关系
                "supplement": prosupplement,//补充说明
                "saleHouseId": saleHouseId,//房源id
                "houseCertificate": proCertificate,//房产证号
                // "unit": prounit,//单元
                // "floor": profloor,//楼层
                // "door": prodoor,//门牌号
                "blueLabel": proBlue,//楼栋蓝牌号
                // "rName": proname,//当前用户姓名
                // "IDCard": procard,//当前用户身份证
                "subName": proarName,//小区名称
                "pics": JSON.stringify(imgAry),//图片
            },
            dataType : 'json',
            headers : {
                'Content-Type' : 'application/x-www-form-urlencoded'
            },
            success: function (data) {
                console.log(data)
                if(data.status==1){
                    $(".successMsgTc,.successMsgFull").show()
                }else{
                    // alert('您的房源认证已提交，正在审核中！')
                    // location.href='/rentwhole'

                    //认证弹窗
                    $(".successMsgFull,.rztg").show()

                    var timName = document.getElementById('tim');
                    var t = 2;
                    var timer = setInterval(function(){
                        timName.innerHTML = t;
                        t--;
                        if(t<0){
                            clearInterval(timer);
                            $(".successMsgFull,.rztg").hide()
                            location.href='/rentwhole?t=2'
                            // location.href='/static/saleHouse/saleHouse.htm'
                        }
                    }, 1000)

                }
            }
        });
    })



    //提交修改真房源  帮他人租房
    $(".helpBtn").click(function(){
        var helpmyname=$(".helpmyname").val()//我的姓名
        var helpmycard=$(".helpmycard").val()//我的身份证号
        var helprelation=$(".helprelation").attr('id')//与产权人关系
        var helpname=$(".helpname").val()//产权人姓名
        var helpcard=$(".helpcard").val()//产权人身份证
        var helparea=$('.helparea').val()//小区名称
        var helpBlue=$('.helpBlue').val()//楼栋蓝牌号
        var helpunit=$('.helpunit').val()//单元
        var helpfloor=$('.helpfloor').val()//楼层
        var helpdoor=$('.helpdoor').val()//门牌号
        var helpCertificate=$('.helpCertificate').val()//房产证号
        var helpsupplement=$('.helpsupplement').val()//补充说明

        var helpImgcz=$(".imghcz").attr('dat')//身份证
        var helpImgcf=$(".imghcf").attr('dat')
        var helpImghz=$(".imghz").attr('dat')//房证
        var helpImghf=$(".imghf").attr('dat')
        var helpImghz3=$(".cardz3").attr('dat')//老房证
        var helpImghf3=$(".cardf3").attr('dat')

        var contractImg6=$(".vcont4").attr('dat')//商品房买卖合同
        var contractImg7=$(".vcont5").attr('dat')//商品房买卖合同
        var imageType6=$(".acth2").attr('dt')//权属证件类型
        var conimgt6=''//0-房本原件，1-无房产证号（老房本），2-商品房买卖合同

        //补充认证
        var bcsmImg2 = $(".imgbcrz2").attr('dat')
        if(bcsmImg2 == undefined){
            bcsmImg2 = ""
        }

        //表单验证
        /*var regName =/^[\u4e00-\u9fa5]{2,4}$/;
        if(!regName.test(helpmyname)){
            if($(".helpmyname").attr('disabled')!='disabled'){
                alert('请正确输入姓名！');
                return false;
            }
        }
        var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if(reg.test(helpmycard) === false)
        {
            if($(".helpmycard").attr('disabled')!='disabled'){
                alert("请正确输入身份证号！");
                return
            }
        }*/
        if(helprelation=='00'){
            alert("请选择与产权人关系！");
            return
        }
        /*if(!regName.test(helpname)){
            alert('请正确输入产权人姓名！');
            return false;
        }
        if(reg.test(helpcard) === false)
        {
            alert("请正确输入产权人身份证号！");
            return
        }
        if($(".imghcz").attr('dat')==undefined || $(".imghcz").attr('dat')==''){
            alert('请上传产权人身份证正面！')
            return
        }
        if($(".imghcf").attr('dat')==undefined || $(".imghcf").attr('dat')==''){
            alert('请上传产权人身份证反面！')
            return
        }*/
        /*if(helpBlue==''){
            alert('请正确输入门牌信息!')
            return
        }*/
        /*if(helpunit==''){
            alert('请正确输入单元信息!')
            return
        }
        if(helpfloor==''){
            alert('请正确输入楼层信息！')
            return
        }
        if(helpdoor==''){
            alert('请正确输入门牌号！')
            return
        }*/
        if(imageType6=='h1'){
            conimgt6=0
            if(helpCertificate=='' && $(".helpCertificate").attr('disabled')!='disabled'){
                alert('请正确输入房产证号！')
                return
            }
            if($(".imghz").attr('dat')==undefined || $(".imghz").attr('dat')==''){
                alert('请上传房本原件首页！')
                return
            }
            if($(".imghf").attr('dat')==undefined || $(".imghf").attr('dat')==''){
                alert('请上传房本原件信息页！')
                return
            }
        }
        /*if(imageType6=='h2'){
            conimgt6=1
            if($(".cardz3").attr('dat')==undefined || $(".cardz3").attr('dat')==''){
                alert('请上传房本原件首页(老房本)！')
                return
            }
            if($(".cardf3").attr('dat')==undefined || $(".cardf3").attr('dat')==''){
                alert('请上传房本原件信息页(老房本)！')
                return
            }
        }*/
        if(imageType6=='h3'){
            conimgt6=2
            if($(".vcont4").attr('dat')=='' && $(".vcont4").attr('dat')==''){
                alert('请上传商品房购买备案合同！')
                return
            }
        }
        if($(".agre2").hasClass('fv')==false){
            alert('请仔细阅读并同意《房本认证隐私条款》')
            return
        }

        var helpImgAry=[]
        if(imageType6=='h1'){
            if(bcsmImg2==''){
                helpImgAry=[
                    {'picType':0,'picName':'正面','url':helpImgcz},
                    {'picType':5,'picName':'背面','url':helpImgcf},
                    {'picType':10,'picName':'房产证首页','url':helpImghz},
                    {'picType':15,'picName':'房产证信息页','url':helpImghf},
                ]
            }else{
                helpImgAry=[
                    {'picType':0,'picName':'正面','url':helpImgcz},
                    {'picType':5,'picName':'背面','url':helpImgcf},
                    {'picType':10,'picName':'房产证首页','url':helpImghz},
                    {'picType':15,'picName':'房产证信息页','url':helpImghf},
                    {'picType':20,'picName':'补充说明','url':bcsmImg2},
                ]
            }
        }
        if(imageType6=='h2'){
            helpImgAry=[
                {'picType':0,'picName':'正面','url':helpImgcz},
                {'picType':5,'picName':'背面','url':helpImgcf},
                {'picType':10,'picName':'房产证首页','url':helpImghz3},
                {'picType':15,'picName':'房产证信息页','url':helpImghf3},
                {'picType':20,'picName':'补充说明','url':bcsmImg2},
            ]
        }
        if(imageType6=='h3'){
            if(contractImg6==''){
                if(bcsmImg2==''){
                    helpImgAry=[
                        {'picType':0,'picName':'正面','url':helpImgcz},
                        {'picType':5,'picName':'背面','url':helpImgcf},
                        {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg7},
                    ]
                }else{
                    helpImgAry=[
                        {'picType':0,'picName':'正面','url':helpImgcz},
                        {'picType':5,'picName':'背面','url':helpImgcf},
                        {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg7},
                        {'picType':20,'picName':'补充说明','url':bcsmImg2},
                    ]
                }
            }
            if(contractImg7==''){
                if(bcsmImg2==''){
                    helpImgAry=[
                        {'picType':0,'picName':'正面','url':helpImgcz},
                        {'picType':5,'picName':'背面','url':helpImgcf},
                        {'picType':10,'picName':'商品房买卖合同首页','url':contractImg6},
                    ]
                }else{
                    helpImgAry=[
                        {'picType':0,'picName':'正面','url':helpImgcz},
                        {'picType':5,'picName':'背面','url':helpImgcf},
                        {'picType':10,'picName':'商品房买卖合同首页','url':contractImg6},
                        {'picType':20,'picName':'补充说明','url':bcsmImg2},
                    ]
                }
            }
            if(contractImg6!='' && contractImg7!=''){
                if(bcsmImg2==''){
                    helpImgAry=[
                        {'picType':0,'picName':'正面','url':helpImgcz},
                        {'picType':5,'picName':'背面','url':helpImgcf},
                        {'picType':10,'picName':'商品房买卖合同首页','url':contractImg6},
                        {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg7},
                    ]
                }else{
                    helpImgAry=[
                        {'picType':0,'picName':'正面','url':helpImgcz},
                        {'picType':5,'picName':'背面','url':helpImgcf},
                        {'picType':10,'picName':'商品房买卖合同首页','url':contractImg6},
                        {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg7},
                        {'picType':20,'picName':'补充说明','url':bcsmImg2},
                    ]
                }
            }
        }

        $.ajax({
            type: "POST",
            async: false,
            url: "/publishRealEstate",
            data:{
                "certificateType":conimgt6,//权属证件类型 0房本原件，1无房产证号，2商品房买卖合同
                "houseType":2,
                "id": '',//房源id
                "identity": idents,//身份 0产权人 1帮他人
                "homeOwnerName": helpname,//产权人姓名
                "homeOwnerID": helpcard,//产权人身份证号
                "relationship": helprelation,//与产权人关系
                "supplement": helpsupplement,//补充说明
                "saleHouseId": saleHouseId,//房源id
                "houseCertificate": helpCertificate,//房产证号
                // "unit": helpunit,//单元
                // "floor": helpfloor,//楼层
                // "door": helpdoor,//门牌号
                "blueLabel": helpBlue,//楼栋蓝牌号
                // "rName": helpmyname,//当前用户姓名
                // "IDCard": helpmycard,//当前用户身份证
                "subName": helparea,//小区名称
                "pics": JSON.stringify(helpImgAry),//图片
            },
            dataType : 'json',
            headers : {
                'Content-Type' : 'application/x-www-form-urlencoded'
            },
            success: function (data) {
                console.log(data)
                if(data.status==1){
                    $(".successMsgTc,.successMsgFull").show()
                }else{
                    // alert('您的房源认证已提交，正在审核中！')
                    // location.href='/rentwhole'
                    // location.href='/static/saleHouse/saleHouse.htm'

                    //认证弹窗
                    $(".successMsgFull,.rztg").show()

                    var timName = document.getElementById('tim');
                    var t = 2;
                    var timer = setInterval(function(){
                        timName.innerHTML = t;
                        t--;
                        if(t<0){
                            clearInterval(timer);
                            $(".successMsgFull,.rztg").hide()
                            location.href='/rentwhole?t=2'
                            // location.href='/static/saleHouse/saleHouse.htm'
                        }
                    }, 1000)

                }
            }
        });
    })

    var fileItems = {};
    //产权人-上传身份证正面
    clickUpload('.uploadz','#uploadFront','.imgsz','https://static.fangxiaoer.com/web/images/sy/check/img_id_positive.png')
    //产权人-上传身份证背面
    clickUpload('.uploadf','#uploadBack','.imgsf','https://static.fangxiaoer.com/web/images/sy/check/img_id_back.png')
    //产权人-上传房本首页 \上传房本信息页
    clickUpload('.uploadCard','#uploadInc','.cardz','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    clickUpload('.uploadCardf','#uploadInf','.cardf','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    //老房本-产权人-上传房本首页 \上传房本信息页
    clickUpload('.uploadCard2','#uploadInc2','.cardz2','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    clickUpload('.uploadCardf2','#uploadInf2','.cardf2','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    //产权人-上传备案合同
    clickUpload('.uploadContract','#uploadInt','.vcont','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    clickUpload('.uploadContract2','#uploadInt2','.vcont2','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')


    //帮他人卖-上传身份证正面
    clickUpload('.helpCardz','#helpupz','.imghcz','https://static.fangxiaoer.com/web/images/sy/check/img_id_positive.png')
    //帮他人卖-上传身份证背面
    clickUpload('.helpCardf','#helpupf','.imghcf','https://static.fangxiaoer.com/web/images/sy/check/img_id_back.png')
    //帮他人卖-上传房本首页 上传房本信息页
    clickUpload('.helpHouz','#helphz','.imghz','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    clickUpload('.helpHouf','#helphf','.imghf','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    //老房本 -上传房本首页 上传房本信息页
    clickUpload('.uploadCard3','#uploadInc3','.cardz3','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    clickUpload('.uploadCardf3','#uploadInf3','.cardf3','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    //帮他人-上传备案合同
    clickUpload('.uploadContract4','#uploadInt4','.vcont4','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
    clickUpload('.uploadContract5','#uploadInt5','.vcont5','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')

    // 补充认证
    clickUpload('.uploadbcrz1','#uploadBCRZ1','.imgbcrz1','https://static.fangxiaoer.com/web/images/sy/trustHouse/8.png')
    clickUpload('.uploadbcrz2','#uploadBCRZ2','.imgbcrz2','https://static.fangxiaoer.com/web/images/sy/trustHouse/8.png')

    //点击上传
    function clickUpload(clk,pid,dimg,imgu){
        $(clk).on('click', function () {
            $(pid).val('');
            $(pid).click();
        })
        uploadImg(pid,dimg,clk)
        $(clk).find('.vclose').click(function(event){
            event.stopPropagation();
            fileItems = {}
            $(dimg).attr('src',imgu)
            $(this).hide()
            $(dimg).attr('dat','')
            bcsmImg1=''
        })
    }

    //上传图片
    function uploadImg(uploadInp,domImg,click) {
        $(uploadInp).on('change', function (event) {
            var windowUrl = window.URL || window.webkitURL;
            var files = event.target.files;
            for (var i = 0; i < files.length; i++) {
                var item = files[i];
                var picUrl = windowUrl.createObjectURL(item);
                fileItems = {uri: picUrl, file: item};
            }
            $(uploadInp).val('');
            var formData = new FormData();
            formData.append("file", fileItems.file);
            formData.append("photoType", 'idCardPics');
            $.ajax({
                url: "https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic",
                type: "post",
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'text',
                success: function (data) {
                    console.log(data)
                    var params = JSON.parse(data)
                    var imgurl = 'https://images1.fangxiaoer.com/' + params.content + '/big_fxr_watermark'
                    console.log(imgurl)
                    $(domImg).attr('src', imgurl)
                    $(domImg).attr('dat', params.content)
                    $(click).find('.vclose').show()
                },
                error: function (data) {}
            });
        })
    }


    // 产权人切换
    $(".vsp").click(function(){
        $(this).addClass('act')
        $(this).siblings().removeClass('act')
        if($(this).attr('i')=='m1'){
            $(".vtm1").show()
            $(".vtm2").hide()
            idents=0//产权人
        }else{
            $(".vtm1").hide()
            $(".vtm2").show()
            idents=1//帮他人
        }
    })
    //关闭弹窗
    $(".closeSml").click(function(){
        location.href='/new_secondPublish'
    })
    //房产证号弹窗
    $(".fi").click(function(){
        $(".seek,.successMsgFull").show()
    })
    $(".close_sk").click(function(){
        $(".seek,.successMsgFull").hide()
    })
    //无房证号
    $(".noho").toggle(function(){
        $(this).find('em').addClass('fv')
        $(this).siblings('input').attr('disabled',"disabled").val('')
    },function(){
        $(this).find('em').removeClass('fv')
        $(this).siblings('input').removeAttr('disabled').val('')
    })
    $(".helpNoho").toggle(function(){
        $(this).find('em').addClass('fv')
        $(this).siblings('input').attr('disabled',"disabled").val('')
    },function(){
        $(this).find('em').removeClass('fv')
        $(this).siblings('input').removeAttr('disabled').val('')
    })
    //隐私条款按钮
    $(".agre").toggle(function(){
        $(this).addClass('fv')
    },function(){
        $(this).removeClass('fv')
    })
    $(".agre2").toggle(function(){
        $(this).addClass('fv')
    },function(){
        $(this).removeClass('fv')
    })

    //下拉框
    $(function(){
        $(".navht p").click(function(){
            var ul=$(".newkk");
            if(ul.css("display")=="none"){
                ul.slideDown();
            }else{
                ul.slideUp();
            }
        });
        //点击其他元素
        $(document).bind("click", function(e) {
            var target = $(e.target);
            if (target.closest(".navht p").length == 0) {
                $(".newkk").slideUp()
            }
        })
        $(".set").click(function(){
            var _name = $(this).attr("name");
            if( $("[name="+_name+"]").length > 1 ){
                $("[name="+_name+"]").removeClass("select");
                $(this).addClass("select");
            } else {
                if( $(this).hasClass("select") ){
                    $(this).removeClass("select");
                } else {
                    $(this).addClass("select");
                }
            }
        });
        $(".navht li").click(function(){
            var li=$(this).text();
            var sid=$(this).attr('id')
            $(".navht p").html(li);
            $(".navht p").attr('id',sid)
            $(".newkk").hide();
            /*$(".set").css({background:'none'});*/
            $("p").removeClass("select") ;
        });
    })

    //获取参数
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
</script>


<!--认证弹窗-->
<div class="rztg">
    <span></span>
    <div class="rztm"><img src="https://static.fangxiaoer.com/web/styles/sy/rental/a8.png"></div>
    <div class="rztx">实名认证成功</div>
    <div class="rzdj"><i id="tim">2</i>秒后自动消失</div>
</div>

</body>
</html>