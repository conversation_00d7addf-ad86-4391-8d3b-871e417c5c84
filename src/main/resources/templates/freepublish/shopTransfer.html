<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳商铺信息发布_免费信息发布_个人房源 - 房小二网</title>
    <meta name="keywords" content="二手房信息发布,个人房源,沈阳二手房信息,二手房中介,免费信息发布"/>
    <meta name="description" content="房小二网为您提供专业贴心的二手房服务，为您提供免费二手房信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/pubShop3.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/issue/form1.css?t=20220902" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.validate.min1.js"></script>
    <script type="text/javascript" src="/js/freepublish/personal1.js?t=20180426"></script>
    <script type="text/javascript" src="/js/AjaxforJquery.js"></script>
    <script type="text/javascript" src="/js/jquery.autocomplete.js"></script>

    <style >

        .diyCancel{bottom: 1px !important;}
        .successMsgTc{}
        #p-text1{
            display: block !important;

            font-size: 18px !important;
            margin-top: 70px !important;
            font-size: 17px !important;
            line-height: 28px !important;
            font-weight: normal !important;
        }
        #p-text2{
            display: block !important;

            font-size: 18px !important;

            font-size: 17px !important;
            line-height: 28px !important;
            font-weight: normal !important;
            margin-top: -5px !important;
        }
        #p-text3{
            /*display: block !important;*/

            font-size: 18px !important;
            color: red !important;
            font-size: 17px !important;
            line-height: 28px !important;
        }
        .closeImg{
            width: 19px !important;

            height: 19px !important;
            position: absolute !important;
            right: 12px !important;
            top: 12px !important;
        }
        .successMsgTcBtn{
            display: block !important;
            width: 150px !important;
            height: 40px !important;
            background-color: #ff5200 !important;
            font-size: 16px !important;
            text-align: center !important;
            margin: 0 auto !important;
            line-height: 40px !important;
            margin-top: 36px !important;
            text-decoration: none !important;
            cursor: pointer !important;
            color: #fff !important;
            font-weight: normal !important;
            border-radius: 0px !important;
        }
        #head2017{ position: relative; z-index: 99999999999;}
        .uip{ height: 50px !important;}
        .uip input{ box-shadow: inset 0 0px 0px rgba(0,0,0,0); -webkit-box-shadow:inset 0 0px 0px rgba(0,0,0,0); }
    </style>

    <!--[if IE 6]>
    <style type="text/css">
        input{display:inline !important}
    </style>
    <![endif]-->
    <script>
        $(function () {
            if($("#prices").val()==0){
                $("#prices").val("面议")
            }
            $(".my_xl").each(function () {
                if ($(this).find("input").val() == "0" && $(this).find("li").val() != "0") {
                    $(this).find(".my_xl_txt").html("请选择");
                    $(this).find("input").val("");

                } else
                    for (i = 0; i < $(this).find("li").length; i++) {
                        if ($(this).find("li").eq(i).val() == $(this).find("input").val()) {
                            $(this).find(".my_xl_txt").html($(this).find("li").eq(i).html());
                        }
                    }
            });
            $(".sh_btn").click(function () {
                if ($(".showhide").hasClass("hid")) {
                    $(".showhide").slideDown();
                    $(".showhide").removeClass("hid");
                    $(".icon").addClass("bg_ico");
                } else {
                    $(".showhide").slideUp();
                    $(".showhide").addClass("hid");
                    $(".icon").removeClass("bg_ico");
                }
            })
            $('.huxing input').bind('input propertychange', function () {
                if ($(this).val().length >= 1) {
                    $(this).parent().next().find("input").focus();
                }
            });
            $(".floor").find("input").keyup(function () {
                if ($(this).val().length == 2) {
                    $(this).parent().next().find("input").focus();
                }
            })

            $(".zujin").bind({
                click: function () {
                    if ($(this).val() == "面议") {
                        $(this).val("");
                    }
                },
                blur: function () {
                    if ($(this).val() == "") {
                        $(this).val("面议");
                    }
                }
            })

            //// 房屋配置
            //$(".fwpz a").click(function () {
            //    $("#fwpz_yz").show();
            //    if ($(this).attr("class") == "focus") {
            //        $(this).removeClass("focus");
            //        $(".qx").html("全选")
            //    } else {
            //        $(this).addClass("focus");
            //    }

            //})
            // 房屋配置
            $(".fwpz a").click(function () {
                $("#fwpz_yz").show();
                if ($(this).html() == "无") {
                    if ($(this).attr("class") == "focus") {
                        $(this).removeClass("focus");
                    } else {
                        $(".fwpz a").removeClass("focus");
                        $(this).addClass("focus");
                    }
                } else {
                    if ($(this).attr("class") == "focus") {
                        $(this).removeClass("focus");

                    } else {
                        $(this).addClass("focus");
                        $(".fwpz a:last").removeClass("focus");
                    }

                }
            })

            $(".qx").click(function () {
                if ($(this).html() == "全选") {
                    $(this).html("取消");
                    $(".fwpz a").addClass("focus");
                } else {
                    $(this).html("全选");
                    $(".fwpz a").removeClass("focus");
                }
            })

            //提交执行
            $(".submitBtnFroms").click(function () {
                $("#MIndustry").val("");
                for (i = 0; i < $(".fwpz a").length; i++) {
                    if ($(".fwpz a:eq(" + i + ")").hasClass("focus")) {
                        $("#MIndustry").val($("#MIndustry").val() + "," + $(".fwpz a:eq(" + i + ")").attr("rel"))
                    }
                }
                $("#HouseTrait").val("");
                for (i = 0; i < $(".fyts a").length; i++) {
                    if ($(".fyts a:eq(" + i + ")").hasClass("focus") && $("#HouseTrait").val().split(",").length <= 6) {
                        $("#HouseTrait").val($("#HouseTrait").val() + "," + $(".fyts a:eq(" + i + ")").attr("rel"));
                    }
                }
            })

            //非必选对号
            $(".fbx li").click(function () {
                $(this).parent().parent().parent().find("i").show();
            })
            $(".Regiondiv").find("li").click(function () {
                BindPlate(this);
            })
            //获取板块
            function BindPlate(obj) {
                var RegionID = $(obj).val();
                var ul = $(".Platediv").find("ul");
                var div = $(".Platediv").find(".my_xl_txt");
                ul.html("");
                $.ajax({
                    type: "POST",
                    url: "/getPlates",
                    data: { RegionID: RegionID },
                    success: function (data) {
                        var cs = data;
//                        var resdata = JSON.parse(data);
                        $("#PlateID").val("");
                        div.html("请选择板块");
                        for (var i = 0; i < data.length; i++) {
                            ul.append('<li value="' + data[i].id + '">' + data[i].name + '</li>');
                        }
                    }

                });
            }


        })


    </script>
    <script type="text/javascript" src="/js/freepublish/form.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>

</head>
<body>

<script src="https://static.fangxiaoer.com/js/jquery.autocomplete.js" type="text/javascript"></script>
<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<div class="title">
    为您提供专业贴心的商铺租售服务<p>免费咨询电话：400-893-9709</p>
</div>
<div class="kuang">
    <div class="sale_form_r qh_btn"  style="width: 1270px;margin: 0 auto;float: none;"  th:include="fragment/freepublish::publishType" th:with="currType=3">
    </div>
    <!--表单-->
    <div class="sale_form">
        <form name="jsUpForm" method="post"
              th:action="@{/saveShopTransfer}" th:object="${shopEntity}"  id="jsUpForm" class="bbb">
            <script type="text/javascript">
                //<![CDATA[
                var theForm = document.forms['jsUpForm'];
                if (!theForm) {
                    theForm = document.jsUpForm;
                }
                function __doPostBack(eventTarget, eventArgument) {
                    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                        theForm.__EVENTTARGET.value = eventTarget;
                        theForm.__EVENTARGUMENT.value = eventArgument;
                        theForm.submit();
                    }
                }
                //]]>
            </script>


            <div class="cl"></div>
            <div class="sale_form_l">房源类别</div>
            <div class="sale_form_r">
                <ul>
                    <li style="margin-bottom: 20px;">
                        <p><span>*</span>供求方式</p>
                        <div class="rs">
                            <a href="/shopsell" class="">商铺出售</a>
                            <a href="/shoprent" class="">出租转租</a>
                            <a href="/shoptransfer" class="hover">出兑转让</a>
                            <input type="hidden" name="shoptype" value="3"/>
                        </div>
                        <div class="cl"></div>
                    </li>
                </ul>
            </div>
            <input type="hidden"  id="houseid" th:field="*{houseid}"/>
            <div class="sale_form_l">商业基础信息</div>
            <div class="sale_form_r">
                <ul>
                    <li>
                        <p><span>*</span>标题</p>
                        <div class="pr">
                            <input th:field="*{title}" type="text" id="Title" />
                        </div>
                        <i id="Title123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                    <li class="xl_yz">
                        <p><span>*</span>区域板块</p>
                        <div class="my_xl Regiondiv" style="z-index: 98">
                            <input th:field="*{regionid}" type="text" id="RegionID" class="my_xl_input" />
                            <div class="my_xl_txt gckt">请选择区域</div>
                            <div class="my_xl_btn"></div>
                            <ul class="my_xl_list">

                                <li th:each="regions:${region}" th:value="${regions.id}" th:text="${regions.name}"></li>


                            </ul>
                        </div>
                        <div class="my_xl Platediv" style="z-index: 98">
                            <input th:field="*{plateid}" type="text" id="PlateID" class="my_xl_input" />
                            <div class="my_xl_txt gckt">请选择板块</div>
                            <div class="my_xl_btn"></div>
                            <ul class="my_xl_list">
                                <li th:each="plate:${plates}" th:value="${plate.id}" th:text="${plate.name}"></li>
                            </ul>
                        </div>
                        <i id="HouseTrait123" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox" style="visibility: hidden"></div>
                    </li>
                    <li>
                        <p>地址</p>
                        <div class="pr">

                            <input th:field="*{address}" type="text" id="Address" />
                        </div>
                        <i id="I1" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                    <li>
                        <p><span>*</span>类型</p>
                        <div class="pr rs lx">

                            <a th:each="type:${type}" th:rel="${type.id}" th:text="${type.name}" class="gckt"></a>


                            <input th:field="*{shopcategories}" type="text" id="ShopCategories" style="position: absolute;top:-9999px" />
                        </div>
                        <i id="ShopCategories123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                    <li class="w130">
                        <p>商铺配套</p>
                        <div class="pr fwpz">

                            <a th:each="industry:${industry}" th:rel="${industry.id}" th:text="${industry.name}" class="">酒店餐饮</a>


                            <input th:field="*{mindustry}" type="text" id="MIndustry" class="tese_input" />
                        </div>
                        <div class="cl"></div>

                        <i id="fwpz_yz" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>
                    <li>
                        <p>是否可分割</p>
                        <div class="pr rs fg">
                            <a  th:each="cut:${cut}" th:rel="${cut.id}" th:text="${cut.name}" class="gckt"></a>

                            <input th:field="*{iscut}" type="text" id="IsCut" style="position: absolute;top:-9999px" />
                        </div>
                        <i id="I2" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                    <li class="w130">
                        <p><span>*</span>面积</p>
                        <div class="pr">
                            <input th:field="*{truearea}" type="text" id="TrueArea" class="mianji" /><span class="right">m²</span>
                        </div>
                        <i id="BuildArea123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                    <li class="w130 xl_yz zujin">
                        <p><span>*</span>租金</p>
                        <div class="pr" style="float: left;width:187px;">
                            <input type="text" id="Price" style="display:none !important" name="price"/>
                            <input th:field="*{price}" type="text" id="prices" Style="width:167px !important" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"/>
                            <span class="right payTypeShow">元/月</span>
                        </div>

                        <div class="my_xl" style="z-index: 90">
                            <input th:field="*{payment}" type="text" id="Payment" class="my_xl_input" />
                            <div class="my_xl_txt gckt">请选择付款方式</div>
                            <div class="my_xl_btn"></div>
                            <ul class="my_xl_list fkfsul" id="listType">

                                <li th:each="payment:${payment}" th:value="${payment.id}" th:text="${payment.name}" ></li>


                            </ul>
                        </div>
                        <!--<div class="my_xl payType" style="z-index: 90">-->
                            <!--<input id="payType" type="hidden" value="1" />-->
                            <!--<div class="my_xl_txt " id="danwei">元/月</div>-->
                            <!--<div class="my_xl_btn"></div>-->
                            <!--<ul class="my_xl_list fkfsul" id="Ul1">-->
                                <!--<li value="1">元/月</li>-->
                                <!--<li value="2">元/年</li>-->
                                <!--<li value="3">元/m²/日</li>-->
                                <!--<li value="4">元/m²/年</li>-->

                            <!--</ul>-->
                        <!--</div>-->
                        <i id="RentPrice123" class="ljz"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>

                    <li>
                        <p><span></span>转让费</p>
                        <div class="pr rs zr">
                            <a rel="1"  onclick="$('#ttttt').focus();" class="gckt">整转</a>
                            <div class="pr" style="float: left; margin-right: 50px">
                                <input name="tranfee" type="text" id="ttttt" placeholder="面议" style="width: 100px" /><span class="right">万元</span>
                            </div>
                            <a rel="2" onclick="$('#tttt112t').focus();" class="gckt">空转</a>
                            <div class="pr" style="float: left">
                                <input name="tranfee" type="text" id="tttt112t" placeholder="面议" style="width: 100px"/><span class="right">万元</span>
                            </div>
                            <input th:field="*{trantype}" type="text" id="TranType" class="my_xl_input" />
                            <input th:field="*{tranfee}" type="text" id="TranFee" class="my_xl_input" />
                        </div>
                        <i id="I3" class="ljz"></i>
                        <div class="cl"></div>
                        <div class="errorBox1">
                            <label for="ttttt" class="error" style="margin-left: 180px;display: none;">请填写价格</label>
                            <label for="tttt112t" class="error" style="margin-left: 400px;display: none;">请填写价格</label>
                        </div>
                    </li>


                    <li class="w130">
                        <p>房源特色</p>
                        <div class="pr fyts">

                            <a th:each="trait:${trait}" th:rel="${trait.id}" th:text="${trait.name}" class=""></a>


                            <input th:field="*{housetrait}" type="text" id="HouseTrait" class="tese_input" />
                        </div>
                        <div class="cl"></div>

                        <i id="I4" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>




                </ul>

            </div>
            <div class="sale_form_l fxe_fxgxms">房源个性描述</div>
            <div class="sale_form_r" th:include="fragment/freepublish::houseDescribe"></div>
            <div class="cl"></div>

            <div class="sale_form_l">联系方式</div>
            <div class="sale_form_r" th:include="fragment/freepublish :: contactWay"></div>
            <div class="cl"></div>
            <div th:include="fragment/freepublish::terms"></div>
            <style>
                .padB10{
                    padding-bottom: 20px;
                    margin-left: 113px;
                    margin-top: 0px;
                }
            </style>
            <input type="submit" name="submit" value="立即发布" id="submit" class="cl submitBtnFroms" />
            <input type="hidden" id="alertType" th:value="${alert}"/>
            <script th:inline="javascript">
                $(function () {
                    var alertType = [[${alert}]];
                    if (alertType == 1) {
                        var sessionId = [[${session.muser}]];
                        var saleHouseId = [[${houseId}]];

                        // 显示成功后的弹窗
                        /*$(".successMsgTc").show()
                        $(".successMsgFull").show()*/
                        location.href="/realShop/"+saleHouseId//继续认证

                        // 关闭弹窗
                        $(".closeSml").click(function(){
                            window.location.href='/shoptransfer'
                        });

                        //继续认证
                        $(".verf").click(function(){
                            location.href="/realShop/"+saleHouseId
                        })
                    }
                    if (alertType == 2) {
                        Alertljz.ShowAlert('登录失败!账号或密码错误');
                    }
                    if (alertType == 6) {
                        Alertljz.ShowAlert('亲，个人账户最多只能发布6条房源噢！','https://my.fangxiaoer.com/house/houses0_3');
                    }
                    if (alertType == 5) {
                        Alertljz.ShowAlert('亲，您是经纪人需要去经纪人站才能发布，这里是不能发布噢！','https://agent.fangxiaoer.com/');
                    }

                })
            </script>
        </form>
        <input type="hidden" value="" id="textLength"/>
    </div>
</div>

<div class="successMsgTc">
    <div class="success_m">
        <div class="closeSml"></div>
        <div class="ics"></div>
        <div class="sumsg">房源基本信息发布成功</div>
        <div class="smo"><span>房小二网客服正在核实基本消息</span><span>继续认证真房源可加速出售</span></div>
        <div class="chbtn verf">继续认证</div>
        <div class="rnbtn" onclick="window.open('https://my.fangxiaoer.com/house/houses0_1')">管理房产</div>
        <div class="multiple"><i class="left0"></i>多个认证途径 <i class="right0"></i></div>
        <div class="muft">排序优先<i>·</i>增加曝光<i>·</i>独有标识</div>
    </div>
</div>
<div class="successMsgFull"></div>

<script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>

<div class="tc_full"></div>
<div th:include="house/detail/fragment_login::login"></div>
<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/saleHouse03.css?=4"/>
<style>
   .tc_full { z-index: 999999; } /*显示阿里云弹窗*/
   #login { z-index: 9999999; }   
</style>
<script th:inline="javascript">
    var sessionId1 = [[${session.muser}]]; // 有值-登录了  没值-没登录
    var authenticationStatus = [[${session.authenticationStatus}]];  //0-未认证 1-认证成功  2-认证失败
    $(document).ready(function () {
        certNumOnkeyup = function (obj) {
            obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"以外的字符
            obj.value = obj.value.replace(/^0+$/g,"");//以0开头时清空
        }
        // 根据账户状态来判定我自己卖跳转打开页面
        if(sessionId1 == null){//没登录 打开登录弹窗
            $("#login").show()
            $(".tc_full").show()
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
            $(".tc_realname").show()
            $(".tc_full").show()
            $("body").css("overflow-y","clip")
        }

        //点击验证是否登陆
        $('input').attr('autocomplete','off')
        $('input,textarea,.gckt').click(function(){
            if(sessionId1 == null){//没登录 打开登录弹窗
                $("#login").show()
                $(".tc_full").show()
            }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
                $(".tc_realname").show()
                $(".tc_full").show()
                $("body").css("overflow-y","clip")
            }
        })

        $(document).on('click','#loginClose',function(){
            $("#login").hide()
            $(".tc_full").hide()
        })
        $(document).on('click','.trust_colse',function(){
            window.location.reload()
        })
    })
</script>


<script>
    $("#submit").click(function () {

        if(sessionId1 == null){//没登录 打开登录弹窗
            $("#login").show()
            $(".tc_full").show()
            return false;
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
            $(".tc_realname").show()
            $(".tc_full").show()
            $("body").css("overflow-y","clip")
            return false;
        }

        if(!$("#checkagree").hasClass("checked")){
            // timeout(".checkimg", "请阅读服务条款");
            alert("请仔细阅读并同意服务协议及隐私政策！");
            return false;
        }
        $(".xl_yz")
                .each(function () {
                    $(this).find(".my_xl_input").each(function () {
                        if ($(this).val() == "") {
                            $(this).parent().css("border", "1px solid #fd634f");
                            $(document).scrollTop($(this).parent().offset().top);
                        }
                    });
                });
    });
    $(".my_xl_list li").live("click", function () {
        $(this).parent().parent().css("border", "1px solid #ccc");
    })
    $(".lx a")
            .click(function () {
                $(this).parent().find("a").removeClass("hover");
                $(this).addClass("hover");
                $(this).parent().find("input").val($(this).parent().find(".hover").attr("rel"));
            });
    $(".fg a")
            .click(function () {
                $(this).parent().find("a").removeClass("hover");
                $(this).addClass("hover");
                $(this).parent().find("input").val($(this).parent().find(".hover").attr("rel"));
            });
    $(".zr a").click(function () {
        $(this).parent().find("a").removeClass("hover");
        $(this).addClass("hover");
        $("#TranType").val($(this).attr("rel"));
    });
    $("#ttttt").click(function () {
        $(".zr a").eq(0).click();
    });
    $("#tttt112t").click(function () {
        $(".zr a").eq(1).click();
    });

    //付款方式wr
    $(".fkfsul li").click(function () {
        var vv = $(this).attr("value");
        //$(this).parent().siblings("input").val($(this).attr("value"));
        $(this).parent().siblings("input").attr("value", vv);
    });
    $(function () {
        var fwpz;
        $(".lx a").each(function () {
            if ($(this).attr("rel") == $("#ShopCategories").val()) {
                $(this).addClass("hover");
            }
        });
        $(".fg a").each(function () {
            if ($(this).attr("rel") == $("#IsCut").val()) {
                $(this).addClass("hover");
            }
        });
        $(".fwpz a").each(function () {
            var fwpz = $("#MIndustry").val().split(",")
            for (var t = 0; t < fwpz.length; t++) {
                if (fwpz[t] != "" && fwpz[t] != null) {
                    if ($(this).attr("rel") == fwpz[t]) {
                        $(this).addClass("focus");
                    }
                }

            }
        });

        if ($("#TranType").val() == "1") {
            $(".zr a").eq(0).addClass("hover")
            $(".zr input").eq(0).val($("#TranFee").val())
        } else if($("#TranType").val() == "2"){
            $(".zr a").eq(1).addClass("hover")
            $(".zr input").eq(1).val($("#TranFee").val())
        }


    });
    $("#OwnerPhone").keyup(function () {
        this.value = this.value.replace(/\D/g, '')
    })
    $(function () {
        var checkBox = $("#HouseTrait").val();
        console.log(checkBox);
        var checkBoxArray = checkBox.split(",");
        console.log(checkBoxArray);
        for (var i = 1; i <= checkBoxArray.length; i++) {
            $(".fyts a").eq(checkBoxArray[i] - 1).addClass("focus");

        }
    })
    //特色
    $(".fyts a").click(function () {

        //$("#fwpz_yz").show();
        if ($(this).attr("class") == "focus") {
            $(this).removeClass("focus");
        } else {
            if ($(".fyts a.focus").length >= 3) {
                alert("最多可选3个特色");
            } else {
                $(this).addClass("focus");
            }
        }
    });
    $("#prices").blur(function(){
        if($("#prices").val()==""){
            $("#prices").val("面议")
        }
    })
    $("#prices").focus(function(){
        if($("#prices").val()=="面议"){
            $("#prices").val("")
        }
    })
</script>

<script type="text/javascript">
    //选择付款方式时
    $("#listType li").click(function () {
        var liVal = $(this).val();
        listType(liVal);
    })

    //默认价格单位
    $(function () {
        var liVal = $("#Payment").val();
        listType(liVal);
    })
    //价格单位
    function listType(liVal) {
        if (liVal < 25) {
            $("#Price").parent().find("span").text("元/月");
            $("#danwei").text("元/月");
        } else {
            $("#Price").parent().find("span").text("元/年");
            $("#danwei").text("元/年");
        }
    }
    //未填价格自动填写面议
    $("#Price").blur(function () {
        var va = $(this).val();
        if (va == "") {
            $("#Price").parent().find("span").css("display", "none");
            $("#Price").val("面议")
        } else {
            $("#Price").parent().find("span").css("display", "inline");
        }
    })
    $("#Price").focus(function () {
        var va = $(this).val();
        $("#Price").parent().find("span").css("display", "inline");
        if ($("#Price").val() == "面议") {
            $("#Price").val("");
        }
    })

    //$("#ttttt").blur(function(){
    //    var price = new RegExp('^[1-9][0-9]*$');
    //    var	x = $(this).val();
    //    if (!(price.test(x))) {
    //        $(this).addClass("error");
    //        $(".errorBox1").children().eq(0).css("display","block")
    //        $(".errorBox1").children().eq(1).css("display","none")
    //    }
    //})
    //    $("#tttt112t").blur(function(){
    //        var price = new RegExp('^[1-9][0-9]*$');
    //        var	x = $(this).val();
    //        if (!(price.test(x))) {
    //            $(this).addClass("error");

    //            $(".errorBox1").children().eq(1).css("display","block")
    //            $(".errorBox1").children().eq(0).css("display","none")
    //        }
    //    })
    $("#ttttt").focus(function () {
        $("#tttt112t").val("");
        $(".errorBox1").children().css("display", "none")
        $("#tttt112t").removeClass("error");
        $(this).removeClass("error");

    })
    $("#tttt112t").focus(function () {
        $("#ttttt").val("");
        $(".errorBox1").children().css("display", "none")
        $("#ttttt").removeClass("error");
        $(this).removeClass("error");

    })
//    if($(".zr a").eq(0).hasClass("hover")){
//        $(".zr input").eq(1).val("")
//    }else{
//        $(".zr input").eq(0).val("")
//    }
</script>

<div th:include="fragment/fragment::notLoggedIn"></div>
<!--底部-->
<div class="issuedInfo" th:include="fragment/fragment:: publish_footer1"></div>
<div class="cl"></div>
<div class="footer" th:include="fragment/fragment:: publish_footer2"></div>

<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>

</body>
</html>
