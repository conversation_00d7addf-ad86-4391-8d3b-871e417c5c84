<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <title>沈阳写字楼信息发布_免费信息发布_个人房源 - 房小二网</title>
    <meta name="keywords" content="写字楼信息发布,个人房源,沈阳写字楼信息,写字楼中介,免费信息发布"/>
    <meta name="description" content="房小二网为您提供专业贴心的写字楼服务，为您提供免费写字楼信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css"/>
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/issue/scriptorium.css?t=20220902"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/issue/form1.css?t=20220902" rel="stylesheet"
          type="text/css"/>
    <!--<link rel="stylesheet" type="text/css" href="css/scriptorium.css"/>-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/angular.min.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/angular-animate.min.js"></script>
    <script src="/js/house/verify.js"></script>
    <style type="text/css">
        .sale_form_r {
            width: 700px;
        }

        .diyCancel { bottom: 1px !important; }
        .successMsgTc { z-index: 9999999999 !important;}
        .successMsgFull { z-index: 99999999 !important;}

        #p-text1 { display: block !important; font-size: 18px !important; margin-top: 70px !important; font-size: 17px !important; line-height: 28px !important; font-weight: normal !important; }
        #p-text2 { display: block !important; font-size: 18px !important; font-size: 17px !important; line-height: 28px !important; font-weight: normal !important; margin-top: -5px !important; }
        #p-text3 { /*display: block !important;*/
            font-size: 18px !important; color: red !important; font-size: 17px !important; line-height: 28px !important; }
        .closeImg { width: 19px !important; height: 19px !important; position: absolute !important; right: 12px !important; top: 12px !important; }
        .successMsgTcBtn { display: block !important; width: 150px !important; height: 40px !important; background-color: #ff5200 !important; font-size: 16px !important; text-align: center !important; margin: 0 auto !important; line-height: 40px !important; margin-top: 36px !important; text-decoration: none !important; cursor: pointer !important; color: #fff !important; font-weight: normal !important; border-radius: 0px !important; }
        #head2017 { position: absolute;width: 100%;left: 0;top: 0;z-index: 9999999999; }
    </style>
    <style>
        /*[ng:cloak*/

        /*]*/
        /*,*/
        /*[ng-cloak],*/
        /*[data-ng-cloak],*/
        /*[x-ng-cloak],*/
        /*.ng-cloak,*/
        /*.x-ng-cloak {*/
            /*display: none !important;*/
        /*}*/
    </style>
</head>
<body class="ng-cloak" ng-cloak ng-app="myApp">
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<div class="title">为您提供专业贴心的写字楼服务<p>免费咨询电话：************</p></div>
<div class="kuang">
    <div class="sale_form_r qh_btn" style="width: 1270px;margin: 0 auto;float: none;"
         th:include="fragment/freepublish::publishType" th:with="currType=4"></div>
    <div class="issue" ng-controller="scriptorium">
        <!--th:action="@{/saveOfficeSell}"  th:object="${saleHouseEntity}" -->
        <form name="myForm" novalidate ng-submit="processForm()" autocomplete="off">
            <ul>
                <li>
                    <div class="issueTitle"><b>房源类别</b></div>
                    <div class="issueContent"></div>
                    <div class="issueError">

                    </div>
                </li>
                <li>
                    <div class="issueTitle">供求类型</div>
                    <div class="issueContent">
                        <a href="/officeRent" class="issueRadio">写字楼出租</a>
                        <a href="/officeSell" class="issueRadio hover">写字楼出售</a></div>
                    <div class="issueError">

                    </div>
                </li>
                <li>
                    <div class="issueTitle"><b>基础信息</b></div>
                    <div class="issueContent"></div>
                    <div class="issueError">

                    </div>
                </li>
                <!--<li>-->
                <!--<div class="issueTitle"><span>*</span>区域板块</div>-->
                <!--<div class="issueContent">-->
                <!--<dl class="issueSelect region">-->
                <!--<dt class="issueMin" ng-click="hideList();$root.regionShow=true;" ng-init="regionidInfo=false">{{ $root.regionShowText==""?"请选择区域":$root.regionShowText }}</dt>-->
                <!--<dd class="issueMin" ng-repeat="text in region" ng-show="$root.regionShow" ng-click="formData.regionid=text.id;$root.regionShowText=text.name;getPlat();$root.platShowText='';formData.plateid='';hideList();regionidInfo=true;headeline();" rel="{{ text.id}}">{{ text.name }}</dd>-->
                <!--</dl>-->
                <!--<dl class="issueSelect plat">-->
                <!--<dt class="issueMin" ng-click="hideList();$root.platShow=true" ng-init="plateidInfo=false">{{ $root.platShowText==""?"请选择板块":$root.platShowText }}</dt>-->
                <!--<dd class="issueMin" ng-repeat="text in plat" ng-show="$root.platShow" ng-click="formData.plateid=text.id;$root.platShowText=text.name;hideList();plateidInfo=true;headeline();" rel="{{ text.id}}">{{ text.name }}</dd>-->
                <!--</dl>-->
                <!--<i ng-show="fitment==undefined">信息加载中……</i>-->
                <!--<input type="hidden" name="regionid" value="{{ formData.regionid }}" ng-model="formData.regionid" required/>-->
                <!--<input type="hidden" name="plateid" value="{{ formData.plateid }}" ng-model="formData.plateid" required/>-->
                <!--</div>-->
                <!--<div class="issueError">-->
                <!--<span ng-show="$root.info">-->
                <!--<span ng-show="myForm.regionid.$error.required">请选择区域</span>-->
                <!--</span>-->
                <!--<span ng-show="$root.info">-->
                <!--<span ng-show="myForm.plateid.$error.required">请选择板块</span>-->
                <!--</span>-->
                <!--</div>-->
                <!--</li>-->
                <!--<li>-->
                <!--<div class="issueTitle"><span>*</span>地址</div>-->
                <!--<div class="issueContent">-->
                <!--<input name="address" type="text" ng-model="formData.address" class="issueMax" required/>-->
                <!--</div>-->
                <!--<div class="issueError">-->
                <!--<span ng-show="myForm.address.$dirty||$root.info">-->
                <!--<span ng-show="myForm.address.$error.required">请填写地址</span>-->
                <!--</span>-->
                <!--</div>-->
                <!--</li>-->
                <li>
                    <div class="issueTitle"><span>*</span>写字楼名称</div>
                    <div class="issueContent">

                        <input type="text" placeholder="请输入项目名" name="officename"
                               style="float: left;margin-right: 10px;"
                               ng-model="formData.officename" ng-keyup="subz()" required  ng-blur="headeline()"/>
                        <input type="hidden" name="officeid" ng-model="formData.officeid" readonly
                               style="color: gainsboro;"/>
                        <ul ng-hide="myChoise" class="xzlXlUl">
                            <li ng-click="choose()" ng-repeat="sub in subs">{{sub.name}}</li>
                        </ul>
                        <!--<div class="issueCheckbox" ng-class="{true:'hover',false:''}[isregistInfo]"-->
                        <!--ng-click="isregistInfo=!isregistInfo;formData.isregist=isregistInfo==true?1:0">是否可注册公司-->
                        <!--</div>-->
                        <!--<input type="hidden" name="isregist" value="{{ formData.isregist }}" ng-model="formData.isregist"/>-->
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.officename.$dirty||$root.info">
                                    <span ng-show="myForm.officename.$error.required">请填写写字楼名称</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>类型</div>
                    <div class="issueContent gckt">
                        <div class="issueRadio" ng-repeat="text in officeType"
                             ng-click="formData.shopcategories=text.id"
                             ng-class="{true:'hover',false:''}[formData.shopcategories==text.id]">{{ text.name }}
                        </div>
                        <input type="hidden" name="shopcategories" value="{{ formData.shopcategories }}"
                               ng-model="formData.shopcategories" required/>
                    </div>
                    <div class="issueError">
                        <span ng-show="$root.info">
                            <span ng-show="myForm.shopcategories.$error.required">请选择类型</span>
                        </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>楼层</div>
                    <div class="issueContent">
                        <input type="text" name="floor" value="" class="issueMin" ng-maxlength="2"
                               ng-model="formData.floor" ng-pattern="/^(-)?\d+$/" ng-blur="floorVerify()" required/>
                        <span>共</span>
                        <input type="text" name="totalfloornumber" value="" class="issueMin" ng-maxlength="2"
                               ng-model="formData.totalfloornumber" ng-pattern="/^[0-9]+$/" ng-blur="floorVerify()"
                               required/>

                        <span>层(地下室请填负号)</span>
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.floor.$dirty||$root.info">
                                    <span ng-show="myForm.floor.$error.required">请填写楼层</span>
                                <span ng-show="myForm.floor.$error.pattern">请填写数字</span>
                                <span ng-show="myForm.floor.$error.maxlength">超出最大范围</span>
                                </span>
                        <span ng-show="myForm.totalfloornumber.$dirty||$root.info">
                                    <span ng-show="myForm.totalfloornumber.$error.required">请填写总楼层</span>
                                <span ng-show="myForm.totalfloornumber.$error.pattern">请填写正整数</span>
                                <span ng-show="myForm.totalfloornumber.$error.maxlength">超出最大范围</span>
                                </span>
                        <span ng-show="(myForm.floor.$dirty && myForm.totalfloornumber.$dirty)||$root.info">
                                    <span ng-show="floorAndTotalfloornumber">楼层数不能大于总楼层数</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>面积</div>
                    <div class="issueContent">
                        <input type="text" name="truearea" class="issueMin" ng-pattern="/^[0-9]+(.[0-9]{0,2})?$/"
                               ng-maxlength="8" ng-model="formData.truearea" required ng-blur="calulate();headeline()"/>
                        <span>㎡</span>
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.truearea.$dirty||$root.info">
                                    <span ng-show="myForm.truearea.$error.required">请填写面积</span>
                                <span ng-show="myForm.truearea.$error.pattern">请填写数字且最多保留两位小数</span>
                                <span ng-show="myForm.truearea.$error.maxlength">超出最大范围</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>单价</div>
                    <div class="issueContent">
                        <input type="text" name="unitprice" class="issueMin" ng-model="formData.unitprice"
                               ng-maxlength="10" ng-pattern="/^[0-9]+(.[0-9]{0,2})?$/" required ng-blur="calulate()"
                               ng-focus="calulateClear(1)"/>
                        <span>元/㎡<i>面议请填“0”</i></span>
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.unitprice.$dirty||$root.info">
                                    <span ng-show="myForm.unitprice.$error.required">请填写单价</span>
                                <span ng-show="myForm.unitprice.$error.pattern">请填写数字且最多保留两位小数</span>
                                <span ng-show="myForm.unitprice.$error.maxlength">超出最大范围</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>总价</div>
                    <div class="issueContent">
                        <input type="text" name="price" class="issueMin" ng-model="formData.price" ng-maxlength="10"
                               ng-pattern="/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/"
                               required/>
                        <span>万元 <i>面议请填“0”</i></span>
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.price.$dirty||$root.info">
                                    <span ng-show="myForm.price.$error.required">请填写总价</span>
                                <span ng-show="myForm.price.$error.pattern">请填写数字且最多保留两位小数</span>
                                <span ng-show="myForm.price.$error.maxlength">超出最大范围</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>物业费</div>
                    <div class="issueContent">
                        <input type="text" name="propertyfee" class="issueMin" ng-model="formData.propertyfee"
                               ng-maxlength="10" ng-pattern="/^[0-9]+(.[0-9]{0,2})?$/" required/>
                        <span>元/㎡·月</span>
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.propertyfee.$dirty||$root.info">
                                    <span ng-show="myForm.propertyfee.$error.required">请填写物业费</span>
                                <span ng-show="myForm.propertyfee.$error.pattern">请填写数字且最多保留两位小数</span>
                                <span ng-show="myForm.propertyfee.$error.maxlength">超出最大范围</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>装修</div>
                    <div class="issueContent gckt">
                        <dl class="issueSelect">
                            <dt class="issueMin" ng-click="hideList();$root.fitmentShow=true;fitmentInfo=true;"
                                ng-init="fitmentInfo=false;">{{ $root.fitmentShowText }}
                            </dt>
                            <dd class="issueMin" ng-repeat="text in fitment" ng-show="$root.fitmentShow"
                                ng-click="formData.fitmenttype=text.id;$root.fitmentShowText=text.name;hideList()">{{
                                text.name }}
                            </dd>
                        </dl>
                        <i ng-show="fitment==undefined">信息加载中……</i>
                        <input type="hidden" name="fitmenttype" value="{{ formData.fitmenttype }}"
                               ng-model="formData.fitmenttype" required/>
                    </div>
                    <div class="issueError">
                                <span ng-show="$root.info">
                                    <span ng-show="myForm.fitmenttype.$error.required">请选择装修</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle">配套设施</div>
                    <div class="issueContent gckt">
                        <div class="issueCheckbox" ng-repeat="(key,text) in industry"
                             ng-click="info=!info;$root.mindustryArray[key]=checkBox(text.id,info);formData.mindustry=bindString($root.mindustryArray);"
                             ng-class="{true:'hover'}[info]" ng-init="info= text.key">{{ text.name }}
                        </div>
                        <input type="hidden" name="mindustry" value="{{ formData.mindustry }}"
                               ng-model="formData.mindustry"/>
                    </div>
                    <div class="issueError">

                    </div>
                </li>
                <li>
                    <div class="issueTitle">特色</div>
                    <div class="issueContent gckt">
                        <div class="issueCheckbox" ng-repeat="(key,text) in shopTraits"
                             ng-click="info=!info;$root.housetraitArray[key]=checkBox(text.id,info);formData.housetrait=bindString($root.housetraitArray);checkTrait();"
                             ng-class="{true:'hover'}[info]" ng-init="info=text.key">{{ text.name }}
                        </div>
                        <input type="hidden" name="housetrait" value="{{ formData.housetrait }}"
                               ng-model="formData.housetrait"/>
                    </div>
                    <div class="issueError">
                         <span>
                                    <span ng-show="maxHouseTraits">特色不能大于5个</span>
                                </span>

                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>标题</div>
                    <div class="issueContent">
                        <input type="text" class="issueMax" name="title" ng-model="formData.title" required/>
                    </div>
                    <div class="issueError">
                        <span ng-show="myForm.title.$dirty||$root.info">
                            <span ng-show="myForm.title.$error.required">请填写标题</span>
                        </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><b>房源个性描述</b></div>
                    <div class="issueContent"></div>
                    <div class="issueError">

                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>详细介绍</div>
                    <div class="issueContent" style="max-height: 290px;">
                        <textarea name="describe" ng-model="formData.describe" ng-maxlength="1000" ng-minlength="5"
                                  required></textarea>
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.describe.$dirty||$root.info">
                                    <span ng-show="myForm.describe.$error.required">请填写详细介绍</span>
                                <span ng-show="myForm.describe.$error.minlength">房源描述应在5至1000字之间</span>
                                <span ng-show="myForm.describe.$error.maxlength">房源描述应在5至1000字之间</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>上传图片</div>
                    <div class="issueContent" style="max-height: 290px;">
                        <div class="sale_form_r" th:include="fragment/freepublish::upPhoto"></div>
                    </div>
                    <div class="issueError">

                    </div>

                </li>
                <li>
                    <div class="issueTitle"><span>*</span>联系电话</div>
                    <div class="issueContent">
                        <input type="text" name="phone" maxlength="11" ng-model="formData.phone" ng-blur="change=true"
                               ng-focus="change=false" ng-pattern="/^1[0-9]{10}$/" ng-maxlength="11" ng-minlength="11"
                               value="" class="issueMiddle" required/>
                    </div>
                    <div class="issueError">
                                <span ng-show="(myForm.phone.$dirty && change)||$root.info">
                                    <span ng-show="myForm.phone.$error.required">请输入你的手机号码!</span>
                                    <span ng-show="myForm.phone.$error.pattern">手机号码格式不正确，请重新输入!</span>
                                    <span ng-show="myForm.phone.$error.minlength">手机号码格式不正确，请重新输入!</span>
                                    <span ng-show="myForm.phone.$error.maxlength">手机号码格式不正确，请重新输入!</span>
                                </span>
                    </div>
                </li>
                <li>
                    <div class="issueTitle"><span>*</span>联系人</div>
                    <div class="issueContent">
                        <input type="text" name="userName" ng-model="formData.userName" class="issueMiddle" required/>
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.userName.$dirty||$root.info">
                                    <span ng-show="myForm.userName.$error.required">请填写联系人</span>
                                </span>
                    </div>
                </li>
                <li th:if="${#session?.getAttribute('sessionId') == null}">
                    <div class="issueTitle"><span>*</span>请输入</div>
                    <div class="issueContent">
                        <input type="text" name="pass" ng-model="formData.pass" class="issueMiddle" placeholder="验证码/密码"
                               style="float: left;" required/>
                        <b class="fxe_ReSendValidateCoad validateCode" ng-click="ReSendValidateCoad()">获取验证码</b>
                        <b class="fxe_validateCode validateCode"></b>
                    </div>
                    <div class="issueError">
                                <span ng-show="myForm.pass.$dirty||$root.info">
                                    <span ng-show="myForm.pass.$error.required">请填写验证码或密码</span>
                                </span>
                    </div>
                </li>
                <div th:include="fragment/freepublish::terms"></div>
                <style>
                    .padB10{
                        padding-bottom: 40px;
                        margin-left: 113px;
                        margin-top: 0px;
                    }
                </style>
                <li>
                    <div class="issueTitle"></div>
                    <div class="issueContent">
                        <input type="button" ng-click="formSubmit()" name="button" value="立即发布" id="submit"
                               ng-show="myForm.$valid" ng-disabled="myForm.$invalid">
                        <a ng-click="formSubmit()" id="insubmit" ng-show="myForm.$invalid">立即发布</a>
                        <!--<p>{{ formData }}</p>-->
                        <!--最后的验证全部通过为true -->
                        <input type="hidden" name="" id="formSubmit" value="{{ myForm.$valid }}"
                               ng-model="myForm.$valid"/>
                    </div>
                    <div class="issueError">

                    </div>
                </li>
                <input type="hidden" id="shopid" name="shopid" th:value="${shopId}">
                <input type="hidden" id="sessionId" th:value="${sessionId}" ng-model="formData.sessionId">
                <input id="HouseOwnerH" type="hidden" th:value="${#session?.getAttribute('userName')}">
                <input id="OwnerPhoneH" type="hidden" th:value="${#session?.getAttribute('phoneNum')}">
            </ul>
        </form>
        <div id="aerltAllDom" ng-show="alertClose">
            <div id="alertDom" class="tanChuWenZi">
                <img src="https://static.fangxiaoer.com/web/images/my/admin/yun_close.gif" id="alertClose"
                     ng-click="alertClose=false;">
                <p ng-show="alertText==1">房源信息发布成功，稍后会有工作人员跟你联系<br>如有疑问请咨询电话：<span>************</span></p>
                <p ng-show="alertText==6">亲，个人账户最多只能发布6条房源噢！</p>
                <p ng-show="alertText==5">'亲，您是经纪人需要去经纪人站才能发布，这里是不能发布噢！'</p>
                <p ng-show="alertText==2">登录失败!账号或密码错误</p>
                <a ng-show="alertText!=2 && alertText!=5" href="https://my.fangxiaoer.com/house/houses0_4">确定</a>
                <a ng-show="alertText==5" href="https://agent.fangxiaoer.com/">确定</a>
                <a ng-show="alertText==2" ng-click="alertClose=false;">确定</a>
            </div>
            <div id="alertZhezhao" class="tanChuBeiJing"></div>
        </div>
    </div>
</div>
<div class="issuedInfo" th:include="fragment/fragment:: publish_footer1"></div>
<div class="cl"></div>
<div class="footer" th:include="fragment/fragment:: publish_footer2"></div>
<div class="hint" id="hint" style="display: none;">
    <dl>
        <dd><p>您的浏览器版本过低，<br>建议升级浏览器！</p></dd>
        <dd>
            <img src="https://static.fangxiaoer.com/web/images/sy/map/info.png">
        </dd>
        <dd>
            <a href="javascript:history.back(-1)">留下来再看看</a>
            <a href="https://pc.uc.cn/" target="_blank" style="color: #ff5200;" id="Dherf">升级浏览器</a>
        </dd>
    </dl>
</div>


<div class="successMsgTc">
    <div class="success_m">
        <div class="closeSml"></div>
        <div class="ics"></div>
        <div class="sumsg">房源基本信息发布成功</div>
        <div class="smo"><span>房小二网客服正在核实基本消息</span><span>继续认证真房源可加速出售</span></div>
        <div class="chbtn verf">继续认证</div>
        <div class="rnbtn" onclick="window.open('https://my.fangxiaoer.com/house/houses0_4')">管理房产</div>
        <div class="multiple"><i class="left0"></i>多个认证途径 <i class="right0"></i></div>
        <div class="muft">排序优先<i>·</i>增加曝光<i>·</i>独有标识</div>
    </div>
</div>
<div class="successMsgFull"></div>

<!--实名验证  开始-->
<!--页面弹窗-->
<style>
    /*#submitForm{display: none !important;}*/
    /*#loginClose{display: none !important;}*/
    #txt_LoginName,#txt_Password{box-shadow: none!important;}
    body{padding-top: 50px}
    .sale_form_r input{display: inline-block !important;}
</style>
<div th:include="house/detail/fragment_login::login"></div>
<script src="https://static.fangxiaoer.com/js/tc/weui_v1.2.8.js"></script>
<link rel="stylesheet" type="text/css" href="/css/saleHouse.css"/>
<div class="tc_realname">
    <h4>账号实名认证</h4>
    <h5>根据相关部门要求，发布房源之前个人账号需要实名认证</h5>
    <div class="realname_main">
        <ul class="realname_ul">
            <li class="err1" data-id="err1">
                <span class="span1"><i>*</i>上传身份证件</span>
                <div class="errorBox"></div>
                <div class="real_Id_img">
                    <div id="uploader1">
                        <i>不对外展示</i>
                        <p>上传身份证正面</p>
                        <div class="img_item" id='addimg1'>
                            <input id="uploaderInput1" class="uploader_input" type="file" accept="image/*"  multiple="" />
                            <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png" class="img_add1">
                        </div>
                    </div>
                    <div id="uploader2">
                        <i>不对外展示</i>
                        <p>上传身份证反面</p>
                        <div class="img_item" id='addimg2'>
                            <input id="uploaderInput2" class="uploader_input" type="file" accept="image/*"  multiple="" />
                            <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png" class="img_add2">
                        </div>
                    </div>
                    <p class="idMsg">水印内容：仅限房小二网房源登记</p>

                </div>
                <div class="cl"></div>
            </li>
            <li class="inputid err2" data-id="err2">
                <span><i>*</i>姓名</span>
                <input type="text" id="idName" placeholder="请输入姓名" maxlength="10">
                <div class="errorBox"></div>
                <div class="cl"></div>
            </li>
            <li class="inputid err3" data-id="err3">
                <span><i>*</i>身份证号</span>
                <input type="text" id="idNum" placeholder="请输入身份证号" onkeyup="certNumOnkeyup(this)" oninput="if(value.length>18)value=value.slice(0,18)" maxlength="18">
                <div class="errorBox"></div>
                <div class="cl"></div>
            </li>
            <li class="inputid err4 jeinpbox" data-id="err4">
                <span><i>*</i>身份证有效期</span>
                <input type="text" id="startime" class="idtime jeinput" placeholder="例：2019.03.19">
                <s>--</s>
                <input type="text" id="endtime" class="idtime jeinput" placeholder="例：2029.03.19">
                <div class="errorBox" style="bottom: -20px;"></div>
                <div class="cl"></div>
            </li>

        </ul>

    </div>
    <p class="errMsg"></p>
    <div class="realname_btn" id="realname_btn">提交审核</div>
</div>
<div class="idErr">
    <p>您的身份证信息和手机号不一致，是否继续发布？</p>
    <div class="idErr-btn">
        <a class="idErr-btn-yes">是</a>
        <a class="idErr-btn-no">否</a>
    </div>
</div>
<div class="tc_trust">
    <i class="trust_colse"></i>
    <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/21.png" alt="" class="trust_img">
    <h4 class="trust_h4">实名认证成功</h4>
</div>
<div class="tc_full"></div>
<link type="text/css" rel="stylesheet" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/jedate.css">
<script type="text/javascript" src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/jedate.js"></script>
<script th:inline="javascript">
    var sessionId1 = [[${session.muser}]]; // 有值-登录了  没值-没登录
    var authenticationStatus = [[${session.authenticationStatus}]];  //0-未认证 1-认证成功  2-认证失败
    $(document).ready(function () {
        certNumOnkeyup = function (obj) {
            obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"以外的字符
            obj.value = obj.value.replace(/^0+$/g,"");//以0开头时清空
        }
        // 根据账户状态来判定我自己卖跳转打开页面
        if(sessionId1 == null){//没登录 打开登录弹窗
            $("#login").show()
            $(".tc_full").show()
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
            $(".tc_realname").show()
            $(".tc_full").show()
            $("body").css("overflow-y","clip")
        }

        //点击验证是否登陆
        $('input').attr('autocomplete','off')
        $('input,textarea,.gckt').click(function(){
            if(sessionId1 == null){//没登录 打开登录弹窗
                $("#login").show()
                $(".tc_full").show()
            }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
                $(".tc_realname").show()
                $(".tc_full").show()
                $("body").css("overflow-y","clip")
            }
        })

        $(document).on('click','#loginClose',function(){
            $("#login").hide()
            $(".tc_full").hide()
        })
        $(document).on('click','.trust_colse',function(){
            window.location.reload()
        })


        // 是否勾选协议
        $(document).on('click','.check_span',function(){
            $('.check_span').toggleClass('check_span2');
        })

        // 上传身份证weui 正
        var uploadCount1 =0;
        // $("#idName,#idNum").attr("readOnly",true);
        $(".realname_ul li input").attr("readOnly",true);
        weui.uploader('#uploader1', {
            url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
            auto: true,
            type: 'file',
            fileVal: 'file',
            compress: false,
            onBeforeQueued: function(files) {
                var uploadCount1 = 0;
                // `this` 是轮询到的文件, `files` 是所有文件
                if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                    alert('请上传图片');
                    return false; // 阻止文件添加
                }
                if (this.size > 5 * 1024 * 1024) {
                    alert('请上传不超过5M的图片');
                    return false;
                }
                if (files.length > 1) { // 防止一下子选择过多文件
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                if (uploadCount1 + 1 > 1) {
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                ++uploadCount1;

                // return true; // 阻止默认行为，不插入预览图的框架
            },
            onSuccess: function(ret) {
                if (ret.msg == 'success') {
                    var data = ret.content;
                    $("#uploader1 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png")
                    $("#addimg1").before(
                        '<div class="img_item">' +
                        '<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del1" alt="">' +
                        '<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info1"></div>');
                    idOcr("https://images1.fangxiaoer.com/"+data,"FRONT")
                    moveErrorMsg("err1")
                    imgdata1(); //数据处理
                } else {
                    var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
                    alert(msg);
                    imgdata1(); //数据处理
                    return false;
                }
                return true; // 阻止默认行为，不使用默认的成功态
            },
            onError: function(err) {
                // alert("上传失败");
                imgdata1(); //数据处理
                // return true; // 阻止默认行为，不使用默认的失败态
            }
        });

        // 身份证正面 删除
        $("#uploader1").on('click', '.img_del1', function() {
            $(this).parent().remove();
            $("#idName").val("")
            $("#idNum").val("")
            $("#idName").attr("readOnly",true);
            $("#idNum").attr("readOnly",true);
            imgdata1(); //数据处理
        });

        $("#uploader1").on('click', '.img_info', function() {
            weui.gallery(this.src);
        })

        function imgdata1() {
            var imgHeight = $('.img_item').width();
            $('.img_item').css('height', imgHeight + 'px')

            $('#uploadCount1').text($(".info1").length) //小区图数量
            uploadCount1 = $(".info1").length
            if($(".info1").length>=1){
                $('#addimg1').hide();
            }else{
                $('#addimg1').show();
            }
            var result = '';
            // |www.baidu.com;255, |www.baidu.com;256,
            $(".info1").each(function() {
                result = result + $(this).attr("datasearchSubName-id") + ',';
            });
            result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
            // $("#pic").val(result); //室内实拍图片赋值
        }

        <!--上传身份证weui 反面-->
        var uploadCount2 =0;
        weui.uploader('#uploader2', {
            url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
            auto: true,
            type: 'file',
            fileVal: 'file',
            compress: false,
            onBeforeQueued: function(files) {
                var uploadCount2 = 0;
                console.log(this)
                // `this` 是轮询到的文件, `files` 是所有文件
                if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                    alert('请上传图片');
                    return false; // 阻止文件添加
                }
                if (this.size > 5 * 1024 * 1024) {
                    alert('请上传不超过5M的图片');
                    return false;
                }
                if (files.length > 1) { // 防止一下子选择过多文件
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                if (uploadCount2 + 1 > 1) {
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                ++uploadCount2;

                // return true; // 阻止默认行为，不插入预览图的框架
            },
            onSuccess: function(ret) {
                if (ret.msg == 'success') {
                    var data = ret.content;
                    $("#uploader2 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png")
                    $("#addimg2").before(
                        '<div class="img_item">' +
                        '<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del2" alt="">' +
                        '<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info2"></div>');
                    idOcr("https://images1.fangxiaoer.com/"+data,"BACK")
                    moveErrorMsg("err1")
                    //身份证有效期
                    jeDate("#startime",{
                        //onClose:false,
                        format: "YYYY-MM-DD"
                    });
                    jeDate("#endtime",{
                        //onClose:false,
                        format: "YYYY-MM-DD"
                    });
                    imgdata2(); //数据处理

                } else {
                    var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
                    alert(msg);
                    imgdata1(); //数据处理
                    return false;
                }
                return true; // 阻止默认行为，不使用默认的成功态
            },
            onError: function(err) {
                // alert("上传失败");
                imgdata1(); //数据处理
                // return true; // 阻止默认行为，不使用默认的失败态
            }
        });

        // 身份证反面 删除
        $("#uploader2").on('click', '.img_del2', function() {
            $(this).parent().remove();
            $("#startime").val("")
            $("#endtime").val("")
            $("#startime").attr("readonly",true)
            $("#endtime").attr("readonly",true)
            imgdata2(); //数据处理
        });
        $("#uploader2").on('click', '.img_info', function() {
            weui.gallery(this.src);
        })
        function imgdata2() {
            var imgHeight = $('.img_item').width();
            $('.img_item').css('height', imgHeight + 'px')

            $('#uploadCount2').text($(".info2").length) //小区图数量
            uploadCount2 = $(".info2").length
            if($(".info2").length>=1){
                $('#addimg2').hide();
            }else{
                $('#addimg2').show();
            }
            var result = '';
            // |www.baidu.com;255, |www.baidu.com;256,
            $(".info2").each(function() {
                result = result + $(this).attr("data-id") + ',';
            });
            result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
            // $("#pic").val(result); //室内实拍图片赋值
        }

        // 身份证ocr认证
        function idOcr(imageUrl,side) {
            var timeMillis =  Date.parse(new Date())
            console.log(imageUrl)
            $.ajax({
                url:"/checkOCRIDCard",
                data: {
                    imageUrl:imageUrl,
                    side:side,
                    timeMillis:timeMillis
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if(side == "FRONT"){//正面传好
                        $("#idName").attr("readonly",false)
                        $("#idNum").attr("readonly",false)
                    }else{
                        /*$("#startime").attr("readonly",false)
                        $("#endtime").attr("readonly",false)*/
                    }
                    if (data.status == 1) {
                        data = data.content
                        // console.log(data)
                        if(side == "FRONT"){
                            $("#idName").val(data.PERSON_NAME)
                            $("#idNum").val(data.PERSON_ID.substring(0,18))
                            $("#idTextType").val("1")
                            $("#idTextType").attr("data-z","1")
                        }else{
                            var idTime = data.TIME_ZONE.split("-")
                            $("#startime").val(idTime[0].split(".").join("-"))
                            $("#endtime").val(idTime[1].split(".").join("-"))
                            $("#idTextType").val("2")
                            $("#idTextType").attr("data-f","1")
                        }
                        $(".realname_btn").addClass("hover")
                    }else{
                        errorMsg("err1",data.msg)
                    }
                }
            })
        }
        // 错误提醒
        function errorMsg(idname,errorMsg){
            $('.'+idname).addClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('<label class="error">'+errorMsg+'</label>')
            $('.'+idname).find(".errorBox").css("visibility","initial")
        }
        // 移除错误提醒
        function moveErrorMsg(idname){
            $('.'+idname).removeClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('')
            $('.'+idname).find(".errorBox").css("visibility","hidden")
        }

        // 点击提交实名审核
        $(document).on('click','#realname_btn.hover',function(){
            // console.log(sessionId1)
            var idCardFontPic = $(".info1").attr("data-id")
            var idCardBackPic = $(".info2").attr("data-id")
            var rName = $("#idName").val()
            var idCard = $("#idNum").val()
            var idCardStartDateStr = $("#startime").val()
            var idCardEndDateStr = $("#endtime").val()
            if(idCardFontPic == "" || idCardFontPic == undefined){
                errorMsg("err1","上传身份证正面")
            }else if(idCardBackPic == "" || idCardBackPic == undefined){
                errorMsg("err1","上传身份证反面")
            }else if(rName == ""){
                errorMsg("err2","请输入姓名")
            }else if(idCard == ""){
                errorMsg("err3","请输入身份证号")
            }else if(idCardStartDateStr == ""){
                errorMsg("err4","请输入身份证有效期--起始日期")
            }else if(idCardEndDateStr == ""){
                errorMsg("err4","身份证有效期--截至日期")
            }else{
                $.ajax({
                    url:"/memberIdCardAuthentication",
                    data: {
                        sessionId:sessionId1,
                        idCardFontPic:idCardFontPic,
                        idCardBackPic:idCardBackPic,
                        rName:rName,
                        idCard:idCard,
                        idCardStartDateStr:idCardStartDateStr,
                        idCardEndDateStr:idCardEndDateStr
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        console.log(data)
                        if (data.status == 1 ) {
                            if(data.msg == "error"){// 认证失败
                                $(".idErr").show()
                                $(".tc_full").show()
                                $(".idErr p").html(data.content+",是否继续发布？")
                                $(".tc_realname").hide()
                                $("body").css("overflow-y","inherit")
                            }else{//认证成功
                                $(".tc_trust").show()
                                $(".tc_full").show()
                                $(".tc_realname").hide()
                                $("body").css("overflow-y","inherit")
                            }

                        }else if(data.status == 0 ){// 认证三次后 失败
                            $(".errMsg").show()
                            $(".errMsg").html(data.msg)
                            // $(".realname_btn").removeClass("hover")
                        }

                    },
                    error:function (data) {//认证失败
                        console.log(data)
                        $(".errMsg").show()
                        $(".errMsg").html(data.msg)
                        $(".realname_btn").removeClass("hover")
                    }
                })
            }
        })

        $("input").focus(function () {
            var idName = $(this).parent().attr("data-id")
            moveErrorMsg(idName)
        })

        // 身份证信息和手机号不一致，点击 是 继续 继续发布
        $(document).on('click','.idErr-btn-yes',function(){
            $.ajax({
                url:"/confirmAuthenticationInfo",
                data: {
                    sessionId:sessionId1
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    if (data.status == 1) {
                        window.location.reload()
                    }
                }
            })
        })
        // 身份证信息和手机号不一致，点击 否 继续弹出认证弹窗
        $(document).on('click','.idErr-btn-no',function(){
            $(".tc_realname").show()
            $("body").css("overflow-y","clip")
            $(".idErr").hide()
            $(".tc_full").show()
            //身份证有效期
            jeDate("#startime",{
                //onClose:false,
                format: "YYYY-MM-DD"
            });
            jeDate("#endtime",{
                //onClose:false,
                format: "YYYY-MM-DD"
            });
        })

    })




</script>
<!--实名验证  结束-->

<script type="text/javascript">
    var host='https://ltapi.fangxiaoer.com'
    // var host='http://5021b29h03.zicp.vip:33905'
    // var host = "http://*************:8081"

    sy_confirm.init(1, false);
    var app = angular.module('myApp', ['ngAnimate']);
    app.controller('scriptorium', function ($scope, $rootScope, $http, $timeout) {
        $scope.formData = {};
        $scope.formData.shopid = document.getElementById("shopid").value;
        $scope.formData.phone = document.getElementById("OwnerPhoneH").value;
        $scope.formData.userName = document.getElementById("HouseOwnerH").value;
        $scope.formData.shoptype = 5;
        $scope.alertClose = false;
        $scope.alertText = 1;
        $rootScope.info = false;
        $rootScope.regionShow = false; //区域列表
        $rootScope.regionShowText = "";
        $rootScope.platShow = false; //板块列表
        $rootScope.platShowText = "";
        $rootScope.fitmentShow = false; //装修列表
        $rootScope.fitmentShowText = "请选择装修";
        $rootScope.housetraitArray = [];
        $rootScope.mindustryArray = [];
        $scope.formData.truearea = "";
        //比较楼层数
        $scope.floorVerify = function () {
            if (parseInt($scope.formData.floor) > parseInt($scope.formData.totalfloornumber)) {
                $scope.floorAndTotalfloornumber = true;
            } else {
                $scope.floorAndTotalfloornumber = false;
            }
        }
        $scope.checkTrait = function () {
            var strings = $scope.formData.housetrait;

            if (strings == undefined || strings == '') {
                return true;
            } else if (strings.length > 10) {
                $scope.maxHouseTraits = true;
            } else {
                $scope.maxHouseTraits = false;
            }
        }
        var timer = $timeout(function () {
        }, 500);
        $scope.myChoise = true;
        $scope.subz = function () {
            $scope.formData.officeid = '';
            $timeout.cancel(timer);
            timer = $timeout(function () {
                $http({
                    method: 'POST',
                    url: '/getOfficeDictionary',
                    data: 'officeName=' + $scope.formData.officename + '&pageSize=10',
                    headers: {'content-type': "application/x-www-form-urlencoded;charset=UTF-8"}
                }).then(function successCallback(response) {
                    var data = response.data.content;
                    if (data == undefined || data == null) {
                        $scope.myChoise = true;
                    } else {
                        $scope.subs = data;
                        $scope.myChoise = false;
                    }
                    // 请求成功执行代码
                }, function errorCallback(response) {
                    // 请求失败执行代码
                    console.log('fail');
                    $scope.myChoise = true;
                });
            }, 500);

        }
        $scope.choose = function () {
            $scope.formData.officename = this.sub.name;
            $scope.formData.officeid = this.sub.officeId;
            $scope.myChoise = true;
            setTimeout(hanshu(),200);
            function hanshu(){
                $scope.headeline();
            }
        }
        //清除单价总价
        $scope.calulateClear = function (int) {
            if ($scope.formData.truearea != '') {
                if (int == 1) {
                    $scope.formData.price = ''
//                    }else if(int==2){
//                        $scope.formData.unitprice=''
                }
            }
        }
        //获取验证码
        $scope.ReSendValidateCoad = function () {
            console.log($scope.phone)
            if (sy_confirm.phone($scope.formData.phone) == true) {
                console.log('新验证码验证成功！');
                sy_confirm.Code($scope.formData.phone).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                        sy_confirm.time()
                    }
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            } else {
                alert(sy_confirm.phone($scope.formData.phone))
            }
        }
        if (document.getElementById("shopid").value != "") {
            $http({
                method: 'POST',
                url: host+'/apiv1/house/viewHouseEntity',
                data: "houseId=" + document.getElementById("shopid").value + "&&sessionId=" + document.getElementById("sessionId").value + "&&houseType=4",
                headers: {
                    'Content-Type': "application/x-www-form-urlencoded"
                }
            }).then(function successCallback(response) {
                console.log(response.data.content)
                var data = response.data.content;
                $scope.formData.regionid = data.regionid;//区域
                $scope.formData.plateid = data.plateid;//板块
                $scope.formData.shopcategories = data.shopcategories; //写字间
                $scope.formData.officeid = data.officeid; //写字间所属字典
                $scope.formData.address = data.address;//地址
                $scope.formData.officename = data.officename;//名称
                $scope.formData.floor = data.floor;//楼层
                $scope.formData.totalfloornumber = data.totalfloornumber;//共几层
                $scope.formData.truearea = parseFloat(data.truearea);//面积
                $scope.formData.unitprice = data.unitprice;//单价
                $scope.formData.price = data.price;//总价
                $scope.formData.propertyfee = data.propertyfee;//物业费
                $scope.formData.fitmenttype = data.fitmenttype;//装修
                $scope.formData.mindustry = data.mindustry; //配套
                $scope.formData.housetrait = data.housetrait; //特色
                $scope.formData.title = data.title; //标题
                $scope.formData.describe = data.describe; //描述
                $scope.formData.phone = data.ownerphone; //电话
                $scope.formData.userName = data.houseowner; //联系人
                //获取写字楼配置
                $http({
                    method: 'POST',
                    url: host+'/apiv1/house/officeIndustryFilter'
                }).then(function successCallback(response) {
                    $scope.industry = response.data.content;
                    var mindustry = $scope.formData.mindustry.split(",")
                    for (var i = 0; i < response.data.content.length; i++) {
                        response.data.content[i].key = false;
                        for (var j = 0; j < mindustry.length - 1; j++) {
                            if (mindustry[j] == response.data.content[i].id) {
                                response.data.content[i].key = true;
                                $rootScope.mindustryArray[i] = response.data.content[i].id;
                            }
                        }
                    }
                }, function errorCallback(response) {
                });
                //获取特色
                $http({
                    method: 'POST',
                    url: host+'/apiv1/house/officeTraitFilter'
                }).then(function successCallback(response) {
                    $scope.shopTraits = response.data.content;
                    var shopTraits = $scope.formData.housetrait.split(",")
                    for (var i = 0; i < response.data.content.length; i++) {
                        response.data.content[i].key = false;
                        for (var j = 0; j < shopTraits.length - 1; j++) {
                            if (shopTraits[j] == response.data.content[i].id) {
                                response.data.content[i].key = true;
                                $rootScope.housetraitArray[i] = response.data.content[i].id;
                            }
                        }
                    }
                }, function errorCallback(response) {
                });
                //获取区域
                $http({
                    method: 'POST',
                    url: host+'/apiv1/house/viewRegions'
                }).then(function successCallback(response) {
                    $scope.region = response.data.content;
                    for (var i = 0; i < response.data.content.length; i++) {
                        if (response.data.content[i].id == $scope.formData.regionid) {
                            $rootScope.regionShowText = response.data.content[i].name;
                            $http({
                                method: 'POST',
                                url: host+'/apiv1/house/viewPlates',
                                data: "regionId=" + $scope.formData.regionid,
                                headers: {
                                    'Content-Type': "application/x-www-form-urlencoded"
                                }
                            }).then(function successCallback(response) {
                                $scope.plat = response.data.content;
                                for (var j = 0; j < response.data.content.length; j++) {
                                    if (response.data.content[j].id == $scope.formData.plateid) {
                                        $rootScope.platShowText = response.data.content[j].name;
                                    }
                                }
                            }, function errorCallback(response) {
                            });
                            break;
                        }
                    }
                }, function errorCallback(response) {
                });
                //获取装修
                $http({
                    method: 'POST',
                    url: host+'/apiv1/house/officeFitmentFilter'
                }).then(function successCallback(response) {
                    $scope.fitment = response.data.content;
                    for (var j = 0; j < response.data.content.length; j++) {
                        if (response.data.content[j].id == $scope.formData.fitmenttype) {
                            $rootScope.fitmentShowText = response.data.content[j].name;
                        }
                    }
                }, function errorCallback(response) {
                });
            }, function errorCallback(response) {
            });
        }

        //拼接字符串
        $scope.bindString = function (str) {
            var bindStr = ""
            for (var i = 0; i < str.length; i++) {
                if (str[i] != "" && str[i] != undefined) {
                    bindStr += str[i] + ",";
                }
            }
            return bindStr
        }
        //复选框
        $scope.checkBox = function (checkId, info) {
            var checked = ""
            if (info) {
                checked = checkId
            }
            return checked;
        }
        //拼接标题
        $scope.headeline = function () {
            $scope.formData.officename = $scope.formData.officename == undefined ? "" : $scope.formData.officename;
            var truearea = $scope.formData.truearea == undefined ? "" : $scope.formData.truearea + "㎡";
            var OfficeType = $scope.formData.shopcategories;
            var what  = "";
            if(OfficeType == "1"){
                what = "写字楼";
            }else if(OfficeType == "2"){
                what = "商务公寓";
            }else if(OfficeType == "3"){
                what = "商业综合体楼";
            }else if(OfficeType == "4"){
                what = "酒店写字楼";
            }
            $scope.formData.title = "(出售) " + $rootScope.regionShowText + " " + $scope.formData.officename + " " + truearea + " " + what;
        }
        //单价 面积 总价计算
        $scope.calulate = function () { //int等于1算总价     等于2算单价
            var unitprice = $scope.formData.unitprice == undefined ? false : $scope.formData.unitprice == "" ? false : true,
//                    price=$scope.formData.price==undefined?false:$scope.formData.price==""?false:true,
                truearea = $scope.formData.truearea == undefined ? false : $scope.formData.truearea == "" ? false : true
            if (truearea && unitprice) {
                $scope.formData.price = (($scope.formData.unitprice * $scope.formData.truearea) / 10000).toFixed(2);
            }
//                else if(truearea&&price){
//                    $scope.formData.unitprice=($scope.formData.price*10000/$scope.formData.truearea).toFixed(2)
//                }
        }
        //提交表单
        $scope.formSubmit = function () {

            if(sessionId1 == null){//没登录 打开登录弹窗
                $("#login").show()
                $(".tc_full").show()
                return false;
            }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
                $(".tc_realname").show()
                $(".tc_full").show()
                $("body").css("overflow-y","clip")
                return false;
            }

            if(!$("#checkagree").hasClass("checked")){
                // timeout(".checkimg", "请阅读服务条款");
                alert("请仔细阅读并同意服务协议及隐私政策！");
                return false;
            }
            $rootScope.info = true;
            if ($scope.maxHouseTraits) {
                alert("特色最多5个");
                return;
            } else if ($scope.floorAndTotalfloornumber) {
                alert("楼层数不能大于总层数");
                return;
            }
            else if (document.getElementById("formSubmit").value == "false") {
                $(window).scrollTop(0)
            } else {
                $scope.formData.showtuindex = document.getElementById("ImgUpLoad1_showtuIndex").value;
                $scope.formData.imglist = document.getElementById("ImgUpLoad1_imgList").value;
                $scope.formData.imgvalue = document.getElementById("ImgUpLoad1_imgValue").value;

                $scope.processForm()

            }
        }
        $scope.processForm = function () {
            $http({
                method: 'POST',
                url: '/saveOffice',
                data: $.param($scope.formData),  // pass in data as strings
                headers: {'Content-Type': 'application/x-www-form-urlencoded'}  // set the headers so angular passing info as form data (not request payload)
            }).then(function successCallback(response) {
                if (response.data.status == 1) {
                    // $scope.alertClose = true;
                    $scope.alertText = 1;
                    var hid=response.data.houseId
                    var sid=response.data.sessionId

                    // 显示成功后的弹窗
                    /*$(".successMsgTc").show()
                    $(".successMsgFull").show()*/
                    location.href="/realOffice/"+hid//继续认证

                    // 关闭弹窗
                    $(".closeSml").click(function(){
                        window.location.href='/officeSell'
                    });



                    //继续认证
                    $(".verf").click(function(){
                        location.href="/realOffice/"+hid
                    })

                } else if (response.data.status == 2) {
                    $scope.alertClose = true;
                    $scope.alertText = 2;
                } else if (response.data.status == 6) {
                    $scope.alertClose = true;
                    $scope.alertText = 6;
                } else if (response.data.status == 5) {
                    $scope.alertClose = true;
                    $scope.alertText = 5;
                }else {
                    alert("网络延迟，请稍后重试！")
                }
            }, function errorCallback(response) {
            });
        };
        //获取区域
        $http({
            method: 'POST',
            url: host+'/apiv1/house/viewRegions'
        }).then(function successCallback(response) {
            $scope.region = response.data.content;
        }, function errorCallback(response) {
        });
        //获取装修
        $http({
            method: 'POST',
            url: host+'/apiv1/house/officeFitmentFilter'
        }).then(function successCallback(response) {
            $scope.fitment = response.data.content;
        }, function errorCallback(response) {
        });
        //获取写字楼类型
        $http({
            method: 'POST',
            url: host+'/apiv1/house/officeTypeFilter'
        }).then(function successCallback(response) {
            $scope.officeType = response.data.content
        }, function errorCallback(response) {
        });
        //获取写字楼配置
        $http({
            method: 'POST',
            url: host+'/apiv1/house/officeIndustryFilter'
        }).then(function successCallback(response) {
            $scope.industry = response.data.content;
        }, function errorCallback(response) {
        });
        //获取特色
        $http({
            method: 'POST',
            url: host+'/apiv1/house/officeTraitFilter'
        }).then(function successCallback(response) {
            $scope.shopTraits = response.data.content;
        }, function errorCallback(response) {
        });
        //获取板块
        $scope.getPlat = function () {
            $http({
                method: 'POST',
                url: host+'/apiv1/house/viewPlates',
                data: "regionId=" + $scope.formData.regionid,
                headers: {
                    'Content-Type': "application/x-www-form-urlencoded"
                }
            }).then(function successCallback(response) {
                $scope.plat = response.data.content
            }, function errorCallback(response) {
            });
        }
        //隐藏列表
        $scope.hideList = function () {
            $rootScope.regionShow = false;
            $rootScope.platShow = false;
            $rootScope.fitmentShow = false;
        }

        function formController($scope, $http) {



            // create a blank object to hold our form information

            // $scope will allow this to pass between controller and view


        }
    })
</script>
<div th:include="fragment/fragment::notLoggedIn"></div>

<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
</html>