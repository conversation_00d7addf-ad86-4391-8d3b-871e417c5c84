<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳租房信息发布_免费信息发布_个人房源 - 房小二网</title>
    <meta name="keywords" content="二手房信息发布,个人房源,沈阳二手房信息,二手房中介,免费信息发布"/>
    <meta name="description" content="房小二网为您提供专业贴心的二手房服务，为您提供免费二手房信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/pubRent1.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <!--    <link href="https://static.fangxiaoer.com/web/styles/new_sy/issue/form1.css?t=20220902" rel="stylesheet" type="text/css" />-->
    <link href="https://static.fangxiaoer.com/web/images/sy/check/form1.css?t=20220902" rel="stylesheet" type="text/css" />
<!--        <link href="http://*************/form1.css" rel="stylesheet" type="text/css" />-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.validate.min1.js"></script>
    <script type="text/javascript" src="/js/freepublish/personal.js?t=20180426"></script>
    <script type="text/javascript" src="/js/AjaxforJquery.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/agent/autocomplete.js"></script>
    <!--    <script type="text/javascript" src="/js/jquery.autocomplete.js"></script>-->
    <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js"></script>
    <!--上传图片-->
    <!--[if IE 6]>
    <style type="text/css">
        input{display:inline !important}

    </style>
    <![endif]-->

    <style >
        .diyCancel{bottom: 1px !important;}
        .successMsgTc{}
        #p-text1{
            display: block !important;

            font-size: 18px !important;
            margin-top: 70px !important;
            font-size: 17px !important;
            line-height: 28px !important;
            font-weight: normal !important;
        }
        #p-text2{
            display: block !important;

            font-size: 18px !important;

            font-size: 17px !important;
            line-height: 28px !important;
            font-weight: normal !important;
            margin-top: -5px !important;
        }
        #p-text3{
            /*display: block !important;*/

            font-size: 18px !important;
            color: red !important;
            font-size: 17px !important;
            line-height: 28px !important;
        }
        .closeImg{
            width: 19px !important;

            height: 19px !important;
            position: absolute !important;
            right: 12px !important;
            top: 12px !important;
        }
        .successMsgTcBtn{
            display: block !important;
            width: 150px !important;
            height: 40px !important;
            background-color: #ff5200 !important;
            font-size: 16px !important;
            text-align: center !important;
            margin: 0 auto !important;
            line-height: 40px !important;
            margin-top: 36px !important;
            text-decoration: none !important;
            cursor: pointer !important;
            color: #fff !important;
            font-weight: normal !important;
            border-radius: 0px !important;
        }
        #head2017 { position: absolute;width: 100%;left: 0;top: 0;z-index: 9999999999; }
        .newSubList{ left: 119px !important;}
    </style>
    <script>
        $(function () {
            $(".my_xl").each(function () {
                if ($(this).find("input").val() == "0" && $(this).find("li").val() != "0") {
                    $(this).find(".my_xl_txt").html("请选择");
                    $(this).find("input").val("");

                } else
                    for (i = 0; i < $(this).find("li").length; i++) {
                        if ($(this).find("li").eq(i).val() == $(this).find("input").val()) {
                            $(this).find(".my_xl_txt").html($(this).find("li").eq(i).html())
                        }
                    }
            });
            $(".sh_btn").click(function () {
                if ($(".showhide").hasClass("hid")) {
                    $(".showhide").slideDown();
                    $(".showhide").removeClass("hid");
                    $(".icon").addClass("bg_ico")
                } else {
                    $(".showhide").slideUp();
                    $(".showhide").addClass("hid");
                    $(".icon").removeClass("bg_ico")
                }
            })
            $('.huxing input').bind('input propertychange', function () {
                if ($(this).val().length >= 1) {
                    $(this).parent().next().find("input").focus();
                }
            });
            $(".floor").find("input").keyup(function () {
                if ($(this).val().length == 2) {
                    $(this).parent().next().find("input").focus();
                }
            })

            $(".zujin").bind({
                click: function () {
                    if ($(this).val() == "面议") {
                        $(this).val("");
                    }
                },
                blur: function () {
                    if ($(this).val() == "") {
                        $(this).val("面议");
                    }
                }
            })

            // 房屋配置
            $(".fwpz a").click(function () {
                $("#fwpz_yz").show();
                if ($(this).attr("class") == "focus") {
                    $(this).removeClass("focus")
                    $(".qx").html("全选")
                } else {
                    $(this).addClass("focus")
                }

            })

            $(".qx").click(function () {
                if ($(this).html() == "全选") {
                    $(this).html("取消");
                    $(".fwpz a").addClass("focus");
                } else {
                    $(this).html("全选")
                    $(".fwpz a").removeClass("focus");
                }
            })

            //提交执行
            $(".submitBtnFroms").click(function() {
                if(sessionId1 == null){//没登录 打开登录弹窗
                    $("#login").show()
                    $(".tc_full").show()
                    return false;
                }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
                    $(".tc_realname").show()
                    $(".tc_full").show()
                    // $("body").css("overflow-y","clip")
                    return false;
                }

                if(!$("#checkagree").hasClass("checked")){
                    // timeout(".checkimg", "请阅读服务条款");
                    return false;
                }
                $("#OtherEstablish").val("");
                for (i = 0; i < $(".fwpz a").length; i++) {
                    if ($(".fwpz a:eq(" + i + ")").hasClass("focus")) {
                        $("#OtherEstablish").val($("#OtherEstablish").val() + "," + $(".fwpz a:eq(" + i + ")").attr("rel"))
                    }
                }
                $("#HouseTrait").val("");
                for (i = 0; i < $(".fyts a").length; i++) {
                    if ($(".fyts a:eq(" + i + ")").hasClass("focus") && $("#HouseTrait").val().split(",").length <= 6) {
                        $("#HouseTrait").val($("#HouseTrait").val() + "," + $(".fyts a:eq(" + i + ")").attr("rel"));
                    }
                }
            });

            $(function () {
                var checkBox = $("#OtherEstablish").val();
                console.log(checkBox);
                var checkBoxArray = checkBox.split(",");
                console.log(checkBoxArray);
                for (var i = 1; i <= checkBoxArray.length; i++) {
                    $(".fwpz a").eq(checkBoxArray[i]-1).addClass("focus");

                }
            })
            //非必选对号
            $(".fbx li").click(function () {
                $(this).parent().parent().parent().find("i").show();
            })
        })
    </script>
    <script type="text/javascript" src="/js/freepublish/form.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/Alertljz.js"></script>
    <style>
        .noselect{
            pointer-events:none;
        }
    </style>
</head>
<body>

<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>

<div class="title">为您提供专业贴心的租房服务<p>免费咨询电话：400-893-9709</p></div>

<!--二手房发布成功提示页面优化-->
<!--<div class="successMsgTc" >
    <img class="closeImg" src="https://static.fangxiaoer.com/web/images/my/admin/yun_close.gif" alt="">
    <p id="p-text1">房源信息发布成功，稍后会有工作人员跟你联系</p>
    <p id="p-text2">如有疑问请咨询电话：<span id="p-text3">400-893-9709</span></p>

    <div class="successMsgTcBtn">确定</div>
</div>-->
<div class="successMsgTc">
    <div class="success_m">
        <div class="closeSml"></div>
        <div class="ics"></div>
        <div class="sumsg">房源基本信息发布成功</div>
        <div class="smo"><span>房小二网客服正在核实基本消息</span><span>继续认证真房源可加速出售</span></div>
        <div class="chbtn verf">继续认证</div>
        <div class="rnbtn" onclick="window.open('https://my.fangxiaoer.com/house/houses0_2')">管理房产</div>
        <div class="multiple"><i class="left0"></i>多个认证途径 <i class="right0"></i></div>
        <div class="muft">排序优先<i>·</i>增加曝光<i>·</i>独有标识</div>
    </div>
</div>
<div class="successMsgFull"></div>






<div class="kuang">

    <div class="sale_form_r qh_btn" style="width: 1270px;margin: 0 auto;float: none;"
         th:include="fragment/freepublish::publishType" th:with="currType=2">
    </div>
    <!--表单-->
    <div class="sale_form">
        <form name="jsUpForm" method="post"
              th:action="@{/saveRentWhole}" th:object="${rentHouseEntity}"  id="jsUpForm" class="bbb">
            <script type="text/javascript">
                //<![CDATA[
                var theForm = document.forms['jsUpForm'];
                if (!theForm) {
                    theForm = document.jsUpForm;
                }
                function __doPostBack(eventTarget, eventArgument) {
                    if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                        theForm.__EVENTTARGET.value = eventTarget;
                        theForm.__EVENTARGUMENT.value = eventArgument;
                        theForm.submit();
                    }
                }
                //]]>
            </script>


            <div class="cl"></div>
            <div class="sale_form_l">房源类别</div>
            <div class="sale_form_r">
                <ul>
                    <li style="margin-bottom: 20px;">
                        <p><span>*</span>出租方式</p>
                        <div class="rs">

                            <a href="/rentwhole" class="hover">整租</a>
                            <a href="/rentsingls" class="">合租</a>
                            <input type="hidden" name="renttype" value="1"/>

                        </div>
                        <div class="cl"></div>
                    </li>
                </ul>

            </div>


            <div class="sale_form_l">基础信息</div>
            <div class="sale_form_r">
                <ul>
                    <li>
                        <p><span>*</span>小区名称</p>
                        <div class="pr" style="position: relative">
                            <input type="hidden"  id="houseid" th:field="*{houseid}"/>
                            <input type="hidden" th:field="*{regionid}" id="RegionID" />
                            <input type="hidden" th:field="*{plateid}" id="PlateID" />
                            <input type="hidden" th:field="*{subid}" id="SubID" />
                            <input type="hidden" th:field="*{subaddress}" id="SubAddress" />
                            <input type="hidden" th:field="*{subwayline}" id="SubwayLine" />
                            <input type="hidden" th:field="*{subwaysite}" id="SubwaySite" />
                            <input type="hidden" id="newsub" th:field="*{newsub}"/>
                            <div style="position: absolute;z-index: -10;">
                                <input th:field="*{subname}" type="text" id="SubName"/>
                            </div>
                            <input type="text" id="searchSubName" class="xiaoqu" th:value="*{subname}" placeholder="请输入小区名" onkeyup="noSelect()" />
                        </div>
                        <i id="SubName123" class="ljz"></i>
                        <span style="color: #666;">请填写小区名称，并在下拉框中选择小区</span>
                        <ul class="newSubList">
                        </ul>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130 new-sub-lph" id="lph">
                        <p><span>*</span>门牌信息</p>

                        <div style="position: absolute; z-index: -10">
                            <input type="text"  id="detailid" th:field="*{detailid}" />
                        </div>
                        <div class="lphselect lphselect1">
                            <span class="showLphselect1 gckt" data-val="">请选择门牌信息</span>
                            <ul id="listArchId" style="display: none;"></ul>
                        </div>
                        <div class="lphselect lphselect2">
                            <span  class="showLphselect2 gckt">请选择单元</span>
                            <ul id="listUnitId" style="display: none;"></ul>
                        </div>
                        <div class="lphselect lphselect3">
                            <span  class="showLphselect3 gckt">请选择楼层</span>
                            <ul id="listDetailId" style="display: none;"></ul>
                        </div>
                        <div class="lphselect lphselect4">
                            <span  class="showLphselect4 gckt">请选择具体户室</span>
                            <ul id="listFloor" style="display: none;"></ul>
                        </div>
                        <div class="nolph">
                            <p>没有符合的楼层户室号？</p>
                            <span class="gckt">点此继续发布</span>
                        </div>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130 huxing">
                        <p><span>*</span>房屋户型</p>
                        <div class="pr">
                            <input th:field="*{room}" type="text" maxlength="1" id="room" class="room" /><span class="right">室</span>
                        </div>
                        <div class="pr">
                            <input th:field="*{hall}" type="text" maxlength="1" id="Hall" class="hall" /><span class="right">厅</span>
                        </div>
                        <div class="pr">
                            <input th:field="*{toilet}" type="text" maxlength="1" id="Toilet" class="toilet" /><span class="right">卫</span>
                        </div>
                        <div class="pr">
                            <input th:field="*{buildarea}" type="text" maxlength="6" id="BuildArea" class="mianji" placeholder="建筑面积" /><span class="left">共</span><span class="right">㎡</span>
                        </div>
                        <i id="BuildArea123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130 floor" id="gfo">
                        <p><span>*</span>楼层</p>
                        <div class="pr">
                            <input th:field="*{floor}" type="text" id="Floor"
                                   onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/[1-9]/g,'')"
                            /><span class="left">第</span><span class="right">层</span>
                        </div>
                        <div class="pr">
                            <input th:field="*{totalfloornumber}" type="text" id="Totalfloornumber"
                                   onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/[1-9]/g,'')"
                            /><span class="left">共</span><span class="right">层</span>
                        </div>
                        <i id="Totalfloornumber123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>


                    <li class="w130 fbx">
                        <p>装修/朝向</p>
                        <div class="my_xl" style="z-index:99">
                            <input th:field="*{forwardtype}" type="text" id="ForwardType" class="my_xl_input" />

                            <div class="my_xl_txt gckt">请选择朝向</div>
                            <div class="my_xl_btn"></div>
                            <ul class="my_xl_list">

                                <li th:each="forward:${forward}" th:value="${forward.id}" th:text="${forward.name}" ></li>


                            </ul>
                        </div>
                        <div class="my_xl" style="z-index:98">
                            <input th:field="*{fitmenttype}" type="text" id="FitmentType" class="my_xl_input" />
                            <div class="my_xl_txt gckt">请选择装修情况</div>
                            <div class="my_xl_btn"></div>
                            <ul class="my_xl_list">
                                <li th:each="fitment:${fitment}" th:value="${fitment.id}" th:text="${fitment.name}" ></li>
                            </ul>
                        </div>
                        <!--<div class="my_xl" style="z-index:97">-->
                        <!--<input name="ResType" type="text" id="ResType" tabindex="-1" class="my_xl_input" value="4" th:field="*{restype}"/>-->
                        <!--<div class="my_xl_txt">普宅</div>-->
                        <!--<div class="my_xl_btn"></div>-->
                        <!--<ul class="my_xl_list">-->

                        <!--<li th:each="types,iter:${types}"  th:value="${types.id}" th:text="${types.name}">公寓</li>-->


                        <!--</ul>-->
                        <!--</div>-->
                        <i class="ljz" style="display: inline; background-position-y: 23px; padding: 11px 12px;"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>
                    <li class="w130">
                        <p>房屋配置</p>
                        <div class="pr fwpz">

                            <a th:each="establish:${establish}" th:rel="${establish.id}" th:text="${establish.name}" class=""></a>


                            <span class="qx">全选</span>
                            <input th:field="*{otherestablish}" type="text" id="OtherEstablish" class="tese_input" />
                        </div>
                        <div class="cl"></div>

                        <i id="fwpz_yz" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>

                    <li class="w130">
                        <p><span>*</span>租金</p>
                        <div class="pr" style="float: left">
                            <input th:field="*{rentprice}" type="text" id="RentPrice" class="zujin" /><span class="right">元/月</span>
                        </div>


                        <div class="my_xl fkfs" style="z-index:90">
                            <input th:field="*{payment}" type="text" id="payment" class="my_xl_input" />
                            <div class="my_xl_txt ">请选择付款方式</div>
                            <div class="my_xl_btn"></div>
                            <ul class="my_xl_list fkfsul"  id="listType">

                                <li th:each="payment:${payment}" th:value="${payment.id}" th:text="${payment.name}"></li>


                            </ul>
                        </div>
                        <i id="RentPrice123" class="ljz" style="padding: 8px 14px; background-position: 0px 20px;"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>
                    <li class="w130">
                        <p>房源特色</p>
                        <div class="pr fyts">


                            <a th:each="traits:${traits}" th:rel="${traits.id}" th:text="${traits.name}" class=""></a>

                            <input th:field="*{housetrait}" type="text" id="HouseTrait" class="tese_input" />
                        </div>
                        <div class="cl"></div>

                        <i id="HouseTrait123" class="ljz" style="float: right; margin-top: -41px"></i>
                        <div class="cl"></div>
                        <div class="errorBox"></div>
                    </li>


                </ul>

            </div>
            <div class="sale_form_l fxe_fxgxms">详细信息</div>
            <div class="sale_form_r">
                <ul>
                    <li>
                        <p><span>*</span>标题</p>
                        <div class="pr">
                            <input th:field="*{title}" type="text" id="Title" />
                        </div><i id="Title123" class="ljz"></i>
                        <div class="errorBox"></div>
                    </li>
                </ul>
            </div>
            <div class="sale_form_r" th:include="fragment/freepublish::houseDescribe"></div>
            <div class="cl"></div>

            <div class="sale_form_l">联系方式</div>
            <div class="sale_form_r" th:include="fragment/freepublish :: contactWay"></div>
            <div class="cl"></div>
            <div th:include="fragment/freepublish::terms"></div>
            <style>
                .padB10{
                    padding-bottom: 20px;
                    margin-left: 113px;
                    margin-top: 0px;
                }
            </style>
            <input type="submit" name="submit" value="立即发布" id="submit" class="cl submitBtnFroms" />
            <input type="hidden" id="alertType" th:value="${alert}"/>
            <script th:inline="javascript">

                $(function () {

                    // 初始化数据
                    // $(':input','#jsUpForm')
                    //     .not(':button, :submit, :reset, :hidden')
                    //     .val('')
                    //     .removeAttr('checked')
                    //     .removeAttr('selected');
                    // $("#HouseTrait").val("");        // 清空房源特色值
                    // $("#OtherEstablish").val("");        // 清空房屋配置特色值
                    // $(".fyts a").removeClass("focus")  // 移除房源特色选中
                    // $(".fwpz a").removeClass("focus")      // 移除房屋配置选中

                    var alertType = [[${alert}]];



                    if (alertType == 1) {
                        var sessionId = [[${session.muser}]];
                        var saleHouseId = [[${houseId}]];
                        // Alertljz.ShowAlert('房源信息发布成功，稍后会有工作人员跟你联系<br>如有疑问请咨询电话：<span>400-893-9709</span>','https://sy.fangxiaoer.com/new_secondPublish');
                        // 显示成功后的弹窗
                        /*$(".successMsgTc").show()
                        $(".successMsgFull").show()*/
                        location.href="/realRent/"+saleHouseId//跳转继续认证


                        // 关闭弹窗
                        $(".closeSml").click(function(){
                            window.location.href='/rentwhole'
                        });
                        //继续认证
                        $(".verf").click(function(){
                            location.href="/realRent/"+saleHouseId
                        })
                    }
                    if (alertType == 2) {
                        Alertljz.ShowAlert('登录失败!账号或密码错误');
                    }
                    if (alertType == 6) {
                        Alertljz.ShowAlert('亲，个人账户最多只能发布6条房源噢！','https://sy.fangxiaoer.com/new_secondPublish');
                    }
                    if (alertType == 5) {
                        $(".fblb,.tc_full").hide()
                        Alertljz.ShowAlert('亲，您是经纪人需要去经纪人站才能发布，这里是不能发布噢！','https://agent.fangxiaoer.com/');
                    }

                })
                //修改Bug #15906
                function noSelect(){
                    var noselect= $(".ac_results ul li").eq(0).text();
                    if(noselect=="未找到对应的小区"){
                        $(".ac_results ul li").removeClass("ac_even");
                        $(".ac_results ul").addClass("noselect");
                        $(".ac_results ul li").addClass("noselect");
                    }else{
                        $(".ac_results ul").removeClass("noselect");
                        $(".ac_results ul li").removeClass("noselect");

                    }
                }
            </script>
        </form>
        <input type="hidden" value="" id="textLength"/>
    </div>
</div>

<!--实名验证  开始-->
<!--页面弹窗-->
<style>
    /*#submitForm{display: none !important;}*/
    /*#loginClose{display: none !important;}*/
    #txt_LoginName,#txt_Password{box-shadow: none!important;}
    body{padding-top: 50px}
    .sale_form_r input{display: inline-block !important;}
</style>
<div th:include="house/detail/fragment_login::login"></div>
<script src="https://static.fangxiaoer.com/js/tc/weui_v1.2.8.js"></script>
<link rel="stylesheet" type="text/css" href="/css/saleHouse.css"/>
<div class="tc_realname">
    <h4>账号实名认证</h4>
    <h5>根据相关部门要求，发布房源之前个人账号需要实名认证</h5>
    <div class="realname_main">
        <ul class="realname_ul">
            <li class="err1" data-id="err1">
                <span class="span1"><i>*</i>上传身份证件</span>
                <div class="errorBox"></div>
                <div class="real_Id_img">
                    <div id="uploader1">
                        <i>不对外展示</i>
                        <p>上传身份证正面</p>
                        <div class="img_item" id='addimg1'>
                            <input id="uploaderInput1" class="uploader_input" type="file" accept="image/*"  multiple="" />
                            <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png" class="img_add1">
                        </div>
                    </div>
                    <div id="uploader2">
                        <i>不对外展示</i>
                        <p>上传身份证反面</p>
                        <div class="img_item" id='addimg2'>
                            <input id="uploaderInput2" class="uploader_input" type="file" accept="image/*"  multiple="" />
                            <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png" class="img_add2">
                        </div>
                    </div>
                    <p class="idMsg">水印内容：仅限房小二网房源登记</p>

                </div>
                <div class="cl"></div>
            </li>
            <li class="inputid err2" data-id="err2">
                <span><i>*</i>姓名</span>
                <input type="text" id="idName" placeholder="请输入姓名" maxlength="10">
                <div class="errorBox"></div>
                <div class="cl"></div>
            </li>
            <li class="inputid err3" data-id="err3">
                <span><i>*</i>身份证号</span>
                <input type="text" id="idNum" placeholder="请输入身份证号" onkeyup="certNumOnkeyup(this)" oninput="if(value.length>18)value=value.slice(0,18)" maxlength="18">
                <div class="errorBox"></div>
                <div class="cl"></div>
            </li>
            <li class="inputid err4 jeinpbox" data-id="err4">
                <span><i>*</i>身份证有效期</span>
                <input type="text" id="startime" class="idtime jeinput" placeholder="例：2019.03.19">
                <s>--</s>
                <input type="text" id="endtime" class="idtime jeinput" placeholder="例：2029.03.19">
                <div class="errorBox" style="bottom: -20px;"></div>
                <div class="cl"></div>
            </li>

        </ul>

    </div>
    <p class="errMsg"></p>
    <div class="realname_btn" id="realname_btn">提交审核</div>
</div>
<div class="idErr">
    <p>您的身份证信息和手机号不一致，是否继续发布？</p>
    <div class="idErr-btn">
        <a class="idErr-btn-yes">是</a>
        <a class="idErr-btn-no">否</a>
    </div>
</div>
<div class="tc_trust">
    <i class="trust_colse"></i>
    <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/21.png" alt="" class="trust_img">
    <h4 class="trust_h4">实名认证成功</h4>
</div>
<div class="tc_full"></div>
<link type="text/css" rel="stylesheet" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/jedate.css">
<script type="text/javascript" src="https://static.fangxiaoer.com/web/images/sy/IWSellouse/jedate.js"></script>
<script th:inline="javascript">
    var sessionId1 = [[${session.muser}]]; // 有值-登录了  没值-没登录
    var authenticationStatus = [[${session.authenticationStatus}]];  //0-未认证 1-认证成功  2-认证失败
    $(document).ready(function () {
        certNumOnkeyup = function (obj) {
            obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"以外的字符
            obj.value = obj.value.replace(/^0+$/g,"");//以0开头时清空
        }

        // 根据账户状态来判定我自己卖跳转打开页面
        /*if(sessionId1 == null){//没登录 打开登录弹窗
            $("#login").show()
            $(".tc_full").show()
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
            $(".tc_realname").show()
            $(".tc_full").show()
            $("body").css("overflow-y","clip")
        }*/
        //点击验证是否登陆
        $('input').attr('autocomplete','off')
        $('input,textarea,.gckt').click(function(){
            if(sessionId1 == null){//没登录 打开登录弹窗
                $("#login").show()
                $(".tc_full").show()
            }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
                $(".tc_realname").show()
                $(".tc_full").show()
                // $("body").css("overflow-y","clip")
            }
        })

        $(document).on('click','#loginClose',function(){
            $("#login").hide()
            $(".tc_full").hide()
        })
        $(document).on('click','.trust_colse',function(){
            window.location.reload()
        })


        // 是否勾选协议
        $(document).on('click','.check_span',function(){
            $('.check_span').toggleClass('check_span2');
        })

        // 上传身份证weui 正
        var uploadCount1 =0;
        // $("#idName,#idNum").attr("readOnly",true);
        $(".realname_ul li input").attr("readOnly",true);
        weui.uploader('#uploader1', {
            url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
            auto: true,
            type: 'file',
            fileVal: 'file',
            compress: false,
            onBeforeQueued: function(files) {
                var uploadCount1 = 0;
                // `this` 是轮询到的文件, `files` 是所有文件
                if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                    alert('请上传图片');
                    return false; // 阻止文件添加
                }
                if (this.size > 5 * 1024 * 1024) {
                    alert('请上传不超过5M的图片');
                    return false;
                }
                if (files.length > 1) { // 防止一下子选择过多文件
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                if (uploadCount1 + 1 > 1) {
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                ++uploadCount1;

                // return true; // 阻止默认行为，不插入预览图的框架
            },
            onSuccess: function(ret) {
                if (ret.msg == 'success') {
                    var data = ret.content;
                    $("#uploader1 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/18.png")
                    $("#addimg1").before(
                        '<div class="img_item">' +
                        '<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del1" alt="">' +
                        '<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info1"></div>');
                    idOcr("https://images1.fangxiaoer.com/"+data,"FRONT")
                    moveErrorMsg("err1")
                    imgdata1(); //数据处理
                } else {
                    var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
                    alert(msg);
                    imgdata1(); //数据处理
                    return false;
                }
                return true; // 阻止默认行为，不使用默认的成功态
            },
            onError: function(err) {
                // alert("上传失败");
                imgdata1(); //数据处理
                // return true; // 阻止默认行为，不使用默认的失败态
            }
        });

        // 身份证正面 删除
        $("#uploader1").on('click', '.img_del1', function() {
            $(this).parent().remove();
            $("#idName").val("")
            $("#idNum").val("")
            $("#idName").attr("readOnly",true);
            $("#idNum").attr("readOnly",true);
            imgdata1(); //数据处理
        });

        $("#uploader1").on('click', '.img_info', function() {
            weui.gallery(this.src);
        })

        function imgdata1() {
            var imgHeight = $('.img_item').width();
            $('.img_item').css('height', imgHeight + 'px')

            $('#uploadCount1').text($(".info1").length) //小区图数量
            uploadCount1 = $(".info1").length
            if($(".info1").length>=1){
                $('#addimg1').hide();
            }else{
                $('#addimg1').show();
            }
            var result = '';
            // |www.baidu.com;255, |www.baidu.com;256,
            $(".info1").each(function() {
                result = result + $(this).attr("datasearchSubName-id") + ',';
            });
            result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
            // $("#pic").val(result); //室内实拍图片赋值
        }

        <!--上传身份证weui 反面-->
        var uploadCount2 =0;
        weui.uploader('#uploader2', {
            url: 'https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic',
            auto: true,
            type: 'file',
            fileVal: 'file',
            compress: false,
            onBeforeQueued: function(files) {
                var uploadCount2 = 0;
                console.log(this)
                // `this` 是轮询到的文件, `files` 是所有文件
                if (["image/jpg", "image/jpeg", "image/png", "image/gif"].indexOf(this.type) < 0) {
                    alert('请上传图片');
                    return false; // 阻止文件添加
                }
                if (this.size > 5 * 1024 * 1024) {
                    alert('请上传不超过5M的图片');
                    return false;
                }
                if (files.length > 1) { // 防止一下子选择过多文件
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                if (uploadCount2 + 1 > 1) {
                    alert('最多只能上传1张图片，请删除后再上传');
                    return false;
                }
                ++uploadCount2;

                // return true; // 阻止默认行为，不插入预览图的框架
            },
            onSuccess: function(ret) {
                if (ret.msg == 'success') {
                    var data = ret.content;
                    $("#uploader2 img").attr("src","https://static.fangxiaoer.com/web/images/sy/trustHouse/19.png")
                    $("#addimg2").before(
                        '<div class="img_item">' +
                        '<img src="https://static.fangxiaoer.com/m/static/images/pubsale_out_new/close.png"  class="img_del img_del2" alt="">' +
                        '<img src=' + this.url + ' id=' + this.id + ' data-id=' + data + ' class="img_info info2"></div>');
                    idOcr("https://images1.fangxiaoer.com/"+data,"BACK")
                    moveErrorMsg("err1")
                    //身份证有效期
                    jeDate("#startime",{
                        //onClose:false,
                        format: "YYYY-MM-DD"
                    });
                    jeDate("#endtime",{
                        //onClose:false,
                        format: "YYYY-MM-DD"
                    });
                    imgdata2(); //数据处理

                } else {
                    var msg = (ret != null && ret.msg != null && ret.msg != "") ? ret.msg : "上传失败";
                    alert(msg);
                    imgdata1(); //数据处理
                    return false;
                }
                return true; // 阻止默认行为，不使用默认的成功态
            },
            onError: function(err) {
                // alert("上传失败");
                imgdata1(); //数据处理
                // return true; // 阻止默认行为，不使用默认的失败态
            }
        });

        // 身份证反面 删除
        $("#uploader2").on('click', '.img_del2', function() {
            $(this).parent().remove();
            $("#startime").val("")
            $("#endtime").val("")
            $("#startime").attr("readonly",true)
            $("#endtime").attr("readonly",true)
            imgdata2(); //数据处理
        });
        $("#uploader2").on('click', '.img_info', function() {
            weui.gallery(this.src);
        })
        function imgdata2() {
            var imgHeight = $('.img_item').width();
            $('.img_item').css('height', imgHeight + 'px')

            $('#uploadCount2').text($(".info2").length) //小区图数量
            uploadCount2 = $(".info2").length
            if($(".info2").length>=1){
                $('#addimg2').hide();
            }else{
                $('#addimg2').show();
            }
            var result = '';
            // |www.baidu.com;255, |www.baidu.com;256,
            $(".info2").each(function() {
                result = result + $(this).attr("data-id") + ',';
            });
            result = result.substring(0, result.lastIndexOf(',')); //去除最后一个逗号
            // $("#pic").val(result); //室内实拍图片赋值
        }

        // 身份证ocr认证
        function idOcr(imageUrl,side) {
            var timeMillis =  Date.parse(new Date())
            console.log(imageUrl)
            $.ajax({
                url:"/checkOCRIDCard",
                data: {
                    imageUrl:imageUrl,
                    side:side,
                    timeMillis:timeMillis
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if(side == "FRONT"){//正面传好
                        $("#idName").attr("readonly",false)
                        $("#idNum").attr("readonly",false)
                    }else{
                        /*$("#startime").attr("readonly",false)
                        $("#endtime").attr("readonly",false)*/
                    }
                    if (data.status == 1) {
                        data = data.content
                        // console.log(data)
                        if(side == "FRONT"){
                            $("#idName").val(data.PERSON_NAME)
                            $("#idNum").val(data.PERSON_ID.substring(0,18))
                            $("#idTextType").val("1")
                            $("#idTextType").attr("data-z","1")
                        }else{
                            var idTime = data.TIME_ZONE.split("-")
                            $("#startime").val(idTime[0].split(".").join("-"))
                            $("#endtime").val(idTime[1].split(".").join("-"))
                            $("#idTextType").val("2")
                            $("#idTextType").attr("data-f","1")
                        }
                        $(".realname_btn").addClass("hover")
                    }else{
                        errorMsg("err1",data.msg)
                    }
                }
            })
        }
        // 错误提醒
        function errorMsg(idname,errorMsg){
            $('.'+idname).addClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('<label class="error">'+errorMsg+'</label>')
            $('.'+idname).find(".errorBox").css("visibility","initial")
        }
        // 移除错误提醒
        function moveErrorMsg(idname){
            $('.'+idname).removeClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('')
            $('.'+idname).find(".errorBox").css("visibility","hidden")
        }

        // 点击提交实名审核
        $(document).on('click','#realname_btn.hover',function(){
            // console.log(sessionId1)
            var idCardFontPic = $(".info1").attr("data-id")
            var idCardBackPic = $(".info2").attr("data-id")
            var rName = $("#idName").val()
            var idCard = $("#idNum").val()
            var idCardStartDateStr = $("#startime").val()
            var idCardEndDateStr = $("#endtime").val()
            if(idCardFontPic == "" || idCardFontPic == undefined){
                errorMsg("err1","上传身份证正面")
            }else if(idCardBackPic == "" || idCardBackPic == undefined){
                errorMsg("err1","上传身份证反面")
            }else if(rName == ""){
                errorMsg("err2","请输入姓名")
            }else if(idCard == ""){
                errorMsg("err3","请输入身份证号")
            }else if(idCardStartDateStr == ""){
                errorMsg("err4","请输入身份证有效期--起始日期")
            }else if(idCardEndDateStr == ""){
                errorMsg("err4","身份证有效期--截至日期")
            }else{
                $.ajax({
                    url:"/memberIdCardAuthentication",
                    data: {
                        sessionId:sessionId1,
                        idCardFontPic:idCardFontPic,
                        idCardBackPic:idCardBackPic,
                        rName:rName,
                        idCard:idCard,
                        idCardStartDateStr:idCardStartDateStr,
                        idCardEndDateStr:idCardEndDateStr
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        console.log(data)
                        if (data.status == 1 ) {
                            if(data.msg == "error"){// 认证失败
                                $(".idErr").show()
                                $(".tc_full").show()
                                $(".idErr p").html(data.content+",是否继续发布？")
                                $(".tc_realname").hide()
                                $("body").css("overflow-y","inherit")
                            }else{//认证成功
                                $(".tc_trust").show()
                                $(".tc_full").show()
                                $(".tc_realname").hide()
                                $("body").css("overflow-y","inherit")
                            }

                        }else if(data.status == 0 ){// 认证三次后 失败
                            $(".errMsg").show()
                            $(".errMsg").html(data.msg)
                            // $(".realname_btn").removeClass("hover")
                        }

                    },
                    error:function (data) {//认证失败
                        console.log(data)
                        $(".errMsg").show()
                        $(".errMsg").html(data.msg)
                        $(".realname_btn").removeClass("hover")
                    }
                })
            }
        })

        $("input").focus(function () {
            var idName = $(this).parent().attr("data-id")
            moveErrorMsg(idName)
        })

        // 身份证信息和手机号不一致，点击 是 继续 继续发布
        $(document).on('click','.idErr-btn-yes',function(){
            $.ajax({
                url:"/confirmAuthenticationInfo",
                data: {
                    sessionId:sessionId1
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    if (data.status == 1) {
                        window.location.reload()
                    }
                }
            })
        })
        // 身份证信息和手机号不一致，点击 否 继续弹出认证弹窗
        $(document).on('click','.idErr-btn-no',function(){
            $(".tc_realname").show()
            // $("body").css("overflow-y","clip")
            $(".idErr").hide()
            $(".tc_full").show()
            //身份证有效期
            jeDate("#startime",{
                //onClose:false,
                format: "YYYY-MM-DD"
            });
            jeDate("#endtime",{
                //onClose:false,
                format: "YYYY-MM-DD"
            });
        })

    })




</script>
<!--实名验证  结束-->

<script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
<script type="text/javascript">
    ////选择付款方式时
    //$("#listType li").click(function () {
    //    var liVal = $(this).val();
    //    listType(liVal);
    //})
    $("#OwnerPhone").keyup(function () {
        this.value = this.value.replace(/\D/g, '')
    })


    $(".successMsgTcBtn").click(function () {
        $(".successMsgTc").hide()
        $(".successMsgFull").hide()

        window.location.href="https://sy.fangxiaoer.com/new_secondPublish";
    })
    // 关闭弹窗
    $(".closeImg").click(function(){
        $(".successMsgTc").hide()
        $(".successMsgFull").hide()
        // window.location.href="https://sy.fangxiaoer.com/new_secondPublish";


        // 清空表单数据
        $(':input','#jsUpForm')
            .not(':button, :submit, :reset, :hidden')
            .val('')
            .removeAttr('checked')
            .removeAttr('selected');
        $("#HouseTrait").val("");        // 清空房源特色值
        $("#OtherEstablish").val("");        // 清空房屋配置特色值
        $(".fyts a").removeClass("focus")  // 移除房源特色选中
        $(".fwpz a").removeClass("focus")      // 移除房屋配置选中

    });

    //未填价格自动填写面议
    $("#RentPrice").blur(function () {
        var va = $(this).val();
        if (va == "") {
            $("#RentPrice").parent().find("span").css("display", "none");
            $("#RentPrice").val("面议");
        } else {
            $("#RentPrice").parent().find("span").css("display", "inline");
            if(va.replace(/^\d+(\.\d+)?$/,"")!=""){
                $("#RentPrice").val("")
            }
        }
    })
    $("#RentPrice").focus(function () {
        var va = $(this).val();
        $("#RentPrice").parent().find("span").css("display", "inline");
        if ($("#RentPrice").val() == "面议") {
            $("#RentPrice").val("");
        }
    })
    $(function () {
        var checkBox = $("#HouseTrait").val();
        console.log(checkBox);
        var checkBoxArray = checkBox.split(",");
        console.log(checkBoxArray);
        for (var i = 1; i <= checkBoxArray.length; i++) {
            $(".fyts a").eq(checkBoxArray[i] - 1).addClass("focus");

        }
    })
    //特色
    $(".fyts a").click(function () {


        if ($(this).attr("class") == "focus") {
            $(this).removeClass("focus");
        } else {
            if ($(".fyts a.focus").length >= 4) {
                alert("最多可选4个特色");
            } else {
                $(this).addClass("focus");
            }
        }
    });

</script>
<!--底部-->
<div class="issuedInfo" th:include="fragment/fragment:: publish_footer1"></div>
<div class="cl"></div>
<div class="footer" th:include="fragment/fragment:: publish_footer2"></div>
<!--搜索框自动提示-->
<!--<script th:inline="javascript">
    $(function () {
        autoSearch("searchSubName");
        function autoSearch(searchId) {
            $("#" + searchId).autocomplete("/searchs", {
                multiple: false,
                max: 15,
                parse: function (data) {
                    return $.map(eval(data), function (row) {
                        return {
                            data: row,
                            value: row.subName,
                            id: row.subId,
                            result: row.subName
                        };
                    });
                },
                formatItem: function (item) {
                    return item.subName;
                }
            }).result(function (e, item) {
                $("#SubID").attr("value", item.subId);
                $("#SubName").attr("value", item.subName);
            });
        }

        $('#searchSubName').on('blur', function () {
            var searchVal = $('#searchSubName').val();
            var choiceVal = $('#SubName').val();
            if (searchVal != choiceVal) {
                $('#SubID').val('')
                $('#SubName').val('')
            }
        })

    });
</script>-->


<!--搜索框自动提示-->
<script th:inline="javascript">
    var host = "https://ltapi.fangxiaoer.com/apiv1/house/"
    // var host = "http://*************:8081/apiv1/house/"
    var dataSubId; //小区id
    var dataNewSub;//新旧小区
    var dataSubName;//小区名字
    var dataArchId; //楼栋ID
    var dataArchTxt;//楼栋名字
    var dataUnitId;//单元ID
    var dataUnitTxt;//单元内容
    var dataFloor ;//楼层
    var dataLph;//具体蓝牌号 1-2-1
    var showSubName ;//小区输入名字
    var showLphselect1 ;//展示的蓝牌号
    var showLphselect2 ;//展示的单元
    var showLphselect3 ;//展示的楼层
    var showLphselect4 ;//展示的户室
    $(document).ready(function() {
        $("#lph .lphselect  span").addClass("Font999")
        /*autoSearch("searchSubName");
        function autoSearch(searchId) {
            $("#" + searchId).autocomplete("/searchs", {
                multiple: false,
                max: 15,
                parse: function (data) {
                    return $.map(eval(data), function (row) {
                        console.log(data)
                        console.log(row)
                        return {
                            data: row,
                            value: row.subName,
                            id: row.subId,
                            result: row.subName
                        };
                    });
                },
                formatItem: function (item) {
                    return item.subName;
                }
            }).result(function (e, item) {
                $("#SubID").attr("value",item.subId);
                $("#SubName").attr("value",item.subName);
            });
        }*/

        // 错误提醒
        function errorMsg(idname,errorMsg,idLiName){
            $('.'+idname).addClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('<label for="'+idLiName+'" class="error">'+errorMsg+'</label>')
            $('.'+idname).find(".errorBox").css("visibility","initial")
        }
        // 移除错误提醒
        function moveErrorMsg(idname){
            $('.'+idname).removeClass("errorMsg")//外边的div 添加
            $('.'+idname).find(".errorBox").html('')
            $('.'+idname).find(".errorBox").css("visibility","hidden")
        }

        // 修改房源跳转进页面时回显房源蓝牌号
        var pageUrl = window.location.href;
        var toDetailid  = $("#detailid").val()
        var OwnerPhone = $("#OwnerPhoneH").val()

        /*if(pageUrl.indexOf("rentwhole") >= 0 ) {
            $("#lph").show()
            $("#gfo").hide()
            $(".lddysh").hide()
        }else{
            if(toDetailid != "" ){//有蓝牌号
                $("#lph").show()
                $("#gfo").hide()
                $(".lddysh").hide()
                $.ajax({
                    url: host + "viewDetailBlueForChange",
                    data: {
                        realId: toDetailid
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        if (data.status == 1) {
                            data = data.content
                            $("#lph span").removeClass("Font999")
                            $(".showLphselect1").text(data.buildNum)
                            $(".showLphselect2").text(data.unitName)
                            $(".showLphselect3").text(data.floorName)
                            $(".showLphselect4").text(data.roomAlias)
                        }
                    }
                })

            }else{//没有蓝牌号
                $("#lph").hide()+
                $("#gfo").show()
                $(".lddysh").show()
            }
        }*/

        var h = $("#houseid").val()//房源id
        var a = $("#detailid").val()//门牌号
        var k =[[${alert}]]


        if(a=='' && h==''){
            if(k!=6){
                $("#lph").show()
                $(".nolph").show()
            }else{
                $("#lph").hide()
            }
            $("#gfo").hide()
            $(".lddysh").hide()
        }else{
            if(toDetailid != "" ){//有蓝牌号
                if(k!=6){
                    $("#lph").show()
                    $(".nolph").show()
                }else{
                    $("#lph").hide()
                }
                $("#gfo").hide()
                $(".lddysh").hide()
                $.ajax({
                    url: host + "viewDetailBlueForChange",
                    data: {
                        realId: toDetailid
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        if (data.status == 1) {
                            data = data.content
                            $("#lph span").removeClass("Font999")
                            $(".showLphselect1").text(data.buildNum)
                            $(".showLphselect2").text(data.unitName)
                            $(".showLphselect3").text(data.floorName)
                            $(".showLphselect4").text(data.roomAlias)
                        }
                    }
                })

            }else{//没有蓝牌号
                $("#lph").hide()
                $("#gfo").show()
                $(".lddysh").show()
            }
        }

        //输入小区进行
        $('#searchSubName').autocomplete({
            source: function (request, cb) {
                console.log(request.term)
                $.ajax({
                    url: host + "searchSubdistrictByName",
                    data: {
                        subName: request.term,
                        page: 1,
                        pageSize: 100
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        moveErrorMsg("new-sub-name")
                        $(".newSubList").html("")
                        $("#searchSubName").removeClass("error")
                        $(".subMsg").css("color", "#999999")
                        data = data.content
                        console.log(data);
                        if (data.length != 0) {
                            $(".newSubList").show()
                            var names = [];
                            for (var i = 0; i < data.length; i++) {
                                newSubList = '<li  class="newSubListLi" data-subId="' + data[i].subId + '" data-newSub="' + data[i].newSub + '">' + data[i].subName + '</li>'
                                $(".newSubList").prepend(newSubList)
                            }
                        }
                    }
                })
            }
        })

        // 清空蓝牌号
        function clearLph(a){
            if(a == 1){//点击选择小区
                $("#listArchId").html("")
                $("#listUnitId").html("")
                $("#listDetailId").html("")
                $("#listFloor").html("")
                $(".showLphselect1").text("请选择门牌信息")
                $(".showLphselect2").text("请选择单元")
                $(".showLphselect3").text("请选择楼层")
                $(".showLphselect4").text("请选择具体户室")
            }else if(a == 2){//点击选择楼栋蓝牌号
                $("#listUnitId").html("")
                $("#listDetailId").html("")
                $("#listFloor").html("")
                $(".showLphselect2").text("请选择单元")
                $(".showLphselect3").text("请选择楼层")
                $(".showLphselect4").text("请选择具体户室")
            }else if(a == 3) {//点击选择单元
                $("#listDetailId").html("")
                $("#listFloor").html("")
                $(".showLphselect3").text("请选择楼层")
                $(".showLphselect4").text("请选择具体户室")
            }else if(a == 4) {//点击选择层
                $("#listFloor").html("")
                $(".showLphselect4").text("请选择具体户室")
            }
            $("#lph").find(".errorBox").html('')
        }

        //点击小区下拉选中
        $(document).on('click','.newSubListLi',function(){
            clearLph(1)
            dataSubName = $(this).text()
            dataSubId = $(this).attr("data-subId")
            dataNewSub = $(this).attr("data-newSub")
            $("#newsub").val(dataNewSub)
            $("#SubID").val(dataSubId)
            $("#subname").val(dataSubName)
            $("#searchSubName").val(dataSubName)
            $("#SubName").val(dataSubName)
            showSubName = dataSubName
            console.log(dataSubName,dataSubId,dataNewSub)
            $(".newSubList").hide()
            if (dataNewSub == 0) { //没有蓝牌号
                $("#lph").hide()
                $("#gfo").show()
                $(".lddysh").show()
            } else {
                if(k!=6){
                    $("#lph").show()
                    $(".nolph").show()
                }else{
                    $("#lph").hide()
                }
                $("#gfo").hide()
                $(".lddysh").hide()
                $.ajax({
                    url: host + "viewArchBySubId",
                    data: {
                        subId: dataSubId
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        console.log(data)
                        if (data.status == 1) {
                            moveErrorMsg("new-sub-name")
                            $("#listArchId").html("")
                            data = data.content
                            for (var i = 0; i < data.length; i++) {
                                listArchId = '<li class="ArchIdLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                                $("#listArchId").append(listArchId)
                            }
                        }
                    }
                })
            }

        })

        // 点击楼栋号 展示下方下拉数据
        $(document).on('click','.showLphselect1',function(){
            $("#lph ul").hide()
            if( showSubName == "" || showSubName == undefined){
                errorMsg("new-sub-name","请从下拉框中选择小区","subname")
            }else{
                $("#listArchId").show()
            }
        })
        //选择楼栋 后 生成单元选项
        $(document).on('click','.ArchIdLi',function(){
            clearLph(2)
            dataArchId = $(this).attr("data-id")
            dataArchTxt = $(this).text()
            $(".showLphselect1").text(dataArchTxt)
            $("#listArchId").hide()
            showLphselect1  = dataArchTxt
            $.ajax({
                url: host + "viewUnitByArch",
                data: {
                    archId: dataArchId
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if (data.status == 1) {
                        moveErrorMsg("new-sub-lph")
                        $(".showLphselect1").removeClass("Font999")
                        $("#listUnitId").html("")
                        data = data.content
                        for (var i = 0; i < data.length; i++) {
                            listUnitId = '<li class="UnitIdLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                            $("#listUnitId").append(listUnitId)
                        }
                    }
                }
            })
        })

        // 点击单元
        $(document).on('click','.showLphselect2',function(){
            $("#lph ul").hide()
            if( showSubName == "" || showSubName == undefined){
                errorMsg("new-sub-name","请从下拉框中选择小区","subname")
            }else if ( showLphselect1 == "" || showLphselect1 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
            }else{
                $("#listUnitId").show()
            }
        })
        //选择单元 后 生成楼层
        $(document).on('click','.UnitIdLi',function(){
            clearLph(3)
            dataUnitId = $(this).attr("data-id")
            dataUnitTxt = $(this).html()
            $(".lphselect2 span").text(dataUnitTxt)
            $("#listUnitId").hide()
            showLphselect2  = dataUnitTxt
            $.ajax({
                url: host + "viewFloorByUnitAndArch",
                data: {
                    archId: dataArchId,
                    unit:dataUnitId
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if (data.status == 1) {
                        moveErrorMsg("new-sub-lph")
                        $(".showLphselect2").removeClass("Font999")
                        $(".listDetailId").html("")
                        data = data.content
                        for (var i = 0; i < data.length; i++) {
                            listUnitId = '<li class="listDetailLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                            $("#listDetailId").append(listUnitId)
                        }
                    }
                }
            })

        })
        // 点击楼层
        $(document).on('click','.showLphselect3',function(){
            $("#lph ul").hide()
            if( showSubName == "" || showSubName == undefined){
                errorMsg("new-sub-name","请从下拉框中选择小区","subname")
            }else if ( showLphselect1 == "" || showLphselect1 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
            }else if ( showLphselect2 == "" || showLphselect2 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择单元","lph")
            }else{
                $("#listDetailId").show()
            }
        })
        // 点击楼层显示下拉选项
        $(document).on('click','.listDetailLi',function(){
            clearLph(4)
            dataFloor = $(this).attr("data-id")
            dataUnitTxt = $(this).html()
            $(".lphselect3 span").text(dataUnitTxt)
            $("#listDetailId").hide()
            showLphselect3 = dataUnitTxt
            $.ajax({
                url: host + "viewDetailFilter",
                data: {
                    archId: dataArchId,
                    unit:dataUnitId,
                    floor:dataFloor
                },
                dataType: 'json',
                type: 'POST',
                success: function (data) {
                    console.log(data)
                    if (data.status == 1) {
                        moveErrorMsg("new-sub-lph")
                        $(".showLphselect3").removeClass("Font999")
                        $("#listFloor").html("")
                        data = data.content
                        for (var i = 0; i < data.length; i++) {
                            listUnitId = '<li class="listFloorLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                            $("#listFloor").prepend(listUnitId)
                        }
                    }
                }
            })

        })
        // 点击户室
        $(document).on('click','.showLphselect4',function(){
            $("#lph ul").hide()
            if( showSubName == "" || showSubName == undefined){
                errorMsg("new-sub-name","请从下拉框中选择小区","subname")
            }else if ( showLphselect1 == "" || showLphselect1 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
            }else if ( showLphselect2 == "" || showLphselect2 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择单元","lph")
            }else if ( showLphselect3 == "" || showLphselect3 == undefined){
                errorMsg("new-sub-lph","请从下拉框中选择楼层","lph")
            }else{
                $("#listFloor").show()
            }
        })
        // 点击户室显示下拉选项
        $(document).on('click','.listFloorLi',function(){
            dataFloor = $(this).attr("data-id")
            $(".showLphselect4").removeClass("Font999")
            $("#detailid").val(dataFloor)
            dataLph = $(this).html()
            $(".lphselect4 span").text(dataLph)
            $("#listFloor").hide()
            $(".nolph").show()

        })
        //点击没有符合户室，清除掉已选
        $(document).on('click','.nolph span',function(){
            $("#lph").hide()
            $("#gfo").show()
            $(".nolph").hide()
            $(".lddysh").show()
            $("#newsub").val("0")

        })

        // 点击关闭下拉内容
        $("*").not(".new-sub-name").on('click', function () {
            $(".newSubList").hide()
        })
        $("*").not(".lph").on('click', function () {
            $("#lph ul").hide()
        })

        $('#searchSubName').on('blur', function () {
            var searchVal = $('#searchSubName').val();
            var choiceVal = $('#subname').val();
            if (searchVal != choiceVal) {
                $('#SubID').val('')
                $('#subname').val('')
            }
        })

    })
</script>



<!--租房发布改造-->
<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/rental/rental.css" />
<div class="fblb">
    <div class="gzclose"></div>
    <div class="gckpy">请选择发布类别</div>
    <div class="gcbtn">
        <div class="gtho"><i></i>自己出租</div>
        <div class="gtwt"><i></i>委托出租</div>
        <div class="glint"></div>
    </div>
</div>
<div class="gxf">
    <div class="gxfm">
        <span>自己填写信息太麻烦？</span>
        <span>去尝试<i>委托租房</i>吧</span>
    </div>
    <div class="gxmg"></div>
    <div class="gxfclose"></div>
</div>

<script th:inline="javascript">
    var t = getQueryString('t')
    var aas=[[${alert}]]


    if(t==1 && aas!=5){
        $(".tc_full,.fblb").show()
    }else if(t==2){
        loginOrNot()
        $(".fblb").hide()
    }else{
        $(".tc_full,.fblb").hide()
        loginOrNot()
    }


    $(".gzclose").click(function(){
        $(".tc_full,.fblb").hide()
    })
    //自己租
    $(".gtho").click(function(){
        $(".tc_full,.fblb").hide()
        location.href='/rentwhole?t=2'
    })
    //委托出租
    $(".gtwt").click(function(){
        location.href='/entrustedLease'
    })

    //是否登录
    function loginOrNot(){
        if(sessionId1 == null){//没登录 打开登录弹窗
            $("#login").show()
            $(".tc_full").show()
        }else if(sessionId1 != null && authenticationStatus != 1){//已登录 没有实名认证 直接到新页面
            $(".tc_realname").show()
            $(".tc_full").show()
            // $("body").css("overflow-y","clip")
        }
    }
    //右侧悬浮
    $('.sale_form_r input,textarea').keyup(function(){
        //显示右侧委托买房悬浮
        $(".gxf").animate({}, 500, function () {
            $(".gxf").css({ "right": "0px"});
        })
    })
    $(".gxfclose").click(function(){
        $(".gxf").animate({}, 500, function () {
            $(".gxf").css({ "right": "-250px"});
        })
    })
    $(".gxfm i").click(function(){
        location.href='/entrustedLease'
    })

    //获取参数
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
</script>
<!--end-->


<div th:include="fragment/fragment::notLoggedIn"></div>

<div th:include="fragment/fragment::esfCommon_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
</html>

