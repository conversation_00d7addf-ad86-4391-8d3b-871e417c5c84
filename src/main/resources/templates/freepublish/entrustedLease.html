<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
  <head>
    <meta charset="utf-8" />
    <title>委托出租</title>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/images/sy/IWSellouse/saleHouse03.css?t=1"/>
    <script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/agent/autocomplete.js"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js"></script>
    <script type="text/javascript" src="/js/house/verify.js"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/rental/rental.css" />
  </head>
<body>
<style>
  #head2017 {
    position: inherit;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 9999999;
  }
</style>
<div id="head2017" th:include = "fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<div class="w1170 trustHouse">
  <div class="gwth"><h3>委托出租</h3><span>咨询电话：400-893-9709</span></div>
  <ul class="trustHouse_ul">
    <li class="trustHouse_li">
      <div class="trustHouse_li_l">
        <i>*</i>
        <span>小区名称</span>
      </div>
      <div class="trustHouse_li_r li_r_1">
        <div class="ui-widget pr" style="position: relative">
          <input type="text" id="searchSubName" class="xiaoqu" placeholder="请输入小区名" />
          <i id="SubName123" class="ljz"></i>
          <span style="color: #666;" class="subMsg">
<!--            请填写小区名称，并在下拉框中选择小区-->
          </span>
          <ul class="newSubList">
          </ul>
        </div>
        <div class="errorBox"></div>
      </div>
    </li>
    <li class="trustHouse_li" id="lph">
      <div class="trustHouse_li_l">
        <i>*</i>
        <span>门牌信息</span>
      </div>
      <div class="trustHouse_li_r li_r_2">
        <div class="lphselect lphselect1">
          <span class="showLphselect1" data-val="">请选择门牌信息</span>
          <ul id="listArchId" style="display: none;"></ul>
        </div>
        <div class="lphselect lphselect2">
          <span  class="showLphselect2">请选择单元</span>
          <ul id="listUnitId" style="display: none;"></ul>
        </div>
        <div class="lphselect lphselect3">
          <span  class="showLphselect3">请选择楼层</span>
          <ul id="listDetailId" style="display: none;"></ul>
        </div>
        <div class="lphselect lphselect4">
          <span  class="showLphselect4">请选择具体户室</span>
          <ul id="listFloor" style="display: none;"></ul>
        </div>
        <div class="nolph">
          <p>没有符合的楼层户室号？</p>
          <span>点我继续发布</span>
        </div>
        <div class="cl"></div>
        <div class="errorBox"></div>
      </div>
    </li>
    <li class="trustHouse_li">
      <div class="trustHouse_li_l">
        <i>*</i>
        <span>出租方式</span>
      </div>
      <div class="trustHouse_li_r li_r_10">
        <div class="gdx" dt="">
          <div class="gdxm" dt="1"><em></em><i>整租</i></div>
          <div class="gdxm" dt="2"><em></em><i>合租</i></div>
        </div>
        <div class="errorBox" style="width: 150px;"></div>
      </div>
    </li>
    <li class="trustHouse_li">
      <div class="trustHouse_li_l">
        <span>期望租金</span>
      </div>
      <div class="trustHouse_li_r">
        <input name="saleprice" type="text" id="SalePrice" class="zujin" placeholder="请输入您期望租出的价格" style="text-align: left;width: 199px !important;"/><span class="right">元/月</span>
        <div class="errorBox"></div>
      </div>
    </li>
    <li class="trustHouse_li">
      <div class="trustHouse_li_l">
        <i>*</i>
        <span>称呼</span>
      </div>
      <div class="trustHouse_li_r li_r_3">
        <input name="houseowner" type="text" id="HouseOwner"  placeholder="我们应该如何称呼您" />
        <div class="errorBox"></div>
      </div>
    </li>
    <li class="trustHouse_li">
      <div class="trustHouse_li_l">
        <i>*</i>
        <span>联系电话</span>
      </div>
      <div class="trustHouse_li_r li_r_4">
        <input autocomplete="off" name="Txt_LoginName" id="txt_LoginName" value="" class="fxe_mobile_cs" placeholder="您的联系方式，方便我们及时与您联系" maxlength="11" onblur="btnShouFn()" onkeyup="btnShouFn()" />
        <b class="yzm fxe_ReSendValidateCoad yzmfxe_ReSendValidateCoad">获取验证码</b>
        <b class="yzm fxe_validateCode" style="display: none"></b>
        <div class="errorBox"></div>
      </div>
    </li>
    <li class="trustHouse_li">
      <div class="trustHouse_li_l">
        <i>*</i>
        <span>验证码</span>
      </div>
      <div class="trustHouse_li_r li_r_5">
        <input autocomplete="new-password"  name="Txt_Password" id="txt_Password" class="fxe_password_cs" type="password" placeholder="请输入验证码"  onblur="psaawrodBlur()" onkeyup="psaawrodBlur()">
        <div class="errorBox"></div>
      </div>
    </li>
    <p class="trustHouse_p_msg">您点击“确认提交”后，若您的房源通过初步审核，将会由房小二网上的管家和您取得联系，并对您的房源进行再次核实，核实无误后将与您 建立服务关系。房小二网仅提供信息展示和网络技术服务。</p>
  </ul>

  <div class="trustHouse_agree li_r_6">
    <em class="agre hover"></em><i>同意</i><a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房本认证隐私条款》</a>
    <div class="errorBox" style="top: 16px;left: 22px;"></div>
  </div>
  <div class="trustHouse_btn">确认提交</div>

  <!--<div class="trustHouse_service">
    <div class="trustHouse_service_title">
      服务内容 <span>咨询电话：400-893-9709</span>
    </div>

    <ul class="trustHouse_service_main">
      <li>
        <h5><i>1</i>收费标准</h5>
        <div class="mian_p">
          <p>管家服务费：成交价的1%</p>
          <p>1.管家服务费收取方式：委托方与买方签定《房屋买卖合同》时，一次性支付。</p>
          <p>2.本服务不含赎楼服务(解押贷款)，如房源有抵押贷款未还清，需要解押贷款，须另购买”赎楼服务”。</p>
        </div>
      </li>
      <li>
        <h5><i>2</i>服务清单</h5>
        <ul class="mian_span">
          <li><i></i>实勘包装房源</li>
          <li><i></i>平台推广</li>
          <li><i></i>接受购房者咨询</li>
          <li><i></i>保管钥匙带看</li>
          <li><i></i>议价沟通</li>
          <li><i></i>签合同</li>
          <li><i></i>交易过户</li>
          <li><i></i>物业交割</li>
        </ul>
      </li>
      <li>
        <h5><i>3</i>过户所需材料</h5>
        <div class="mian_i">
          <div><i class="mian_i_icon mian_i_icon1"></i><span>身份证</span></div>
          <div><i class="mian_i_icon mian_i_icon2"></i><span>户口本</span></div>
          <div><i class="mian_i_icon mian_i_icon3"></i><span>结婚证</span></div>
          <div><i class="mian_i_icon mian_i_icon4"></i><span>房产证</span></div>
        </div>
      </li>
      <li>
        <h5><i>4</i>服务细则</h5>
        <div class="mian_p">
          <p>1.用户在线预约服务后，平台在一个工作日内，派出房东管家与下单用户对接，约定上门时间及地点，当面签定《房东管家服务协议》，然后开始提供服务。</p>
          <p>2.房源实勘包装工作须在24小时内完成，并在平台上线推广。</p>
          <p>3.房东管家提供的委托卖房服务，一律严格按公示的标准收取服务费，除此之外，严禁管家向委托房东收取任何形式的额外费用。</p>
        </div>
      </li>
    </ul>
  </div>-->
</div>

<!--弹窗页面-->
<div class="tc_trust">
  <i class="trust_colse"></i>
  <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/21.png" alt="" class="trust_img">
  <h4 class="trust_h4">房源委托成功</h4>
  <p class="trust_p">正在为您分配管家，请耐心等待～</p>
</div>
<div class="tc_full"></div>
<script th:inline="javascript">
  var sessionId = [[${session.muser}]];
  var phoneNum = [[${session.phoneNum}]];
  console.log(phoneNum)
  if(phoneNum != "" || phoneNum != undefined) {
    $("#txt_LoginName").val(phoneNum)
  }
  var host = "https://ltapi.fangxiaoer.com/apiv1/house/"
  var ltapi = "https://ltapi.fangxiaoer.com/"

  // var host = "http://*************:8081/apiv1/house/"
  // var ltapi = "http://*************:8081/"

  var dataSubId; //小区id
  var dataNewSub;//新旧小区
  var dataSubName;//小区名字
  var dataArchId; //楼栋ID
  var dataArchTxt;//楼栋名字
  var dataUnitId;//单元ID
  var dataUnitTxt;//单元内容
  var dataFloor ;//楼层
  var dataLph;//具体蓝牌号 1-2-1
  var showSubName ;//小区输入名字
  var showLphselect1 ;//展示的蓝牌号
  var showLphselect2 ;//展示的单元
  var showLphselect3 ;//展示的楼层
  var showLphselect4 ;//展示的户室

  $(document).ready(function() {
    // 错误提醒
    function errorMsg(idname,errorMsg){
      $('.'+idname).addClass("errorMsg")//外边的div 添加
      $('.'+idname).find(".errorBox").html('<label class="error">'+errorMsg+'</label>')
      $('.'+idname).find(".errorBox").css("visibility","initial")
    }
    // 移除错误提醒
    function moveErrorMsg(idname){
      $('.'+idname).removeClass("errorMsg")//外边的div 添加
      $('.'+idname).find(".errorBox").html('')
      $('.'+idname).find(".errorBox").css("visibility","hidden")
    }

    $(".yzmfxe_ReSendValidateCoad").click(function () {
      $(".fxe-alert").hide()
    })
    sy_confirm.init(2,false)
    $(".yzmfxe_ReSendValidateCoad").click(function () {
      if(sy_confirm.phone($("#txt_LoginName").val())==true){
        $(".error_info").text("");
        $("#error_info i").text("")
        console.log('新验证码验证成功！');
        sy_confirm.Code($("#txt_LoginName").val()).then(res => {
          console.log('发送新验证码验证成功！');
          console.log(res);
          if (res == true) {
           $(".fxe-alert").hide()
           sy_confirm.timeWait()
          }
        }).catch(err => {
          console.log('发送新验证码验证失败！');
          console.log(err)
        })
      }else{
        // $(".error_info").text("请输入手机号码");
        errorMsg("li_r_4","请输入手机号码")
        $(".fxe-alert").hide()
        $("#error_info i").text(sy_confirm.phone($("#txt_LoginName").val()))
      }
    })

    btnShouFn = function (){
      let phe=$("#txt_LoginName").val()

      if(s!=null){
        if(phe==''){
          errorMsg("li_r_4","请输入手机号码")
          return
        } else {
          moveErrorMsg("li_r_4")
          return true;
        }
      }else{
        if(phe==''){
          errorMsg("li_r_4","请输入手机号码")
          return
        } else {
          moveErrorMsg("li_r_4")
          return true;
        }
      }
    }

    psaawrodBlur = function (){
      let phe=$("#txt_LoginName").val()
      let md=$("#txt_Password").val()
      if(phe==''){
        errorMsg("li_r_4","请输入手机号码")
        return
      }else{
        moveErrorMsg("li_r_4")
        return true;
      }
    }



    //输入小区进行
    $('#searchSubName').autocomplete({
      source: function (request, cb) {
        console.log(request.term)
        $.ajax({
          url: host + "searchSubdistrictByName",
          data: {
            subName: request.term,
            page: 1,
            pageSize: 100
          },
          dataType: 'json',
          type: 'POST',
          success: function (data) {
            moveErrorMsg("li_r_1")
            $(".newSubList").html("")
            $("#searchSubName").removeClass("error")
            $(".subMsg").css("color", "#999999")
            data = data.content
            console.log(data);
            if (data.length != 0) {
              $(".newSubList").show()
              var names = [];
              for (var i = 0; i < data.length; i++) {
                newSubList = '<li  class="newSubListLi" data-subId="' + data[i].subId + '" data-newSub="' + data[i].newSub + '">' + data[i].subName + '</li>'
                $(".newSubList").prepend(newSubList)
              }
            }
          }
        })
      }
    })

    // 清空蓝牌号
    function clearLph(a){
      if(a == 1){//点击选择小区
        $("#listArchId").html("")
        $("#listUnitId").html("")
        $("#listDetailId").html("")
        $("#listFloor").html("")
        $(".showLphselect1").text("请选择楼栋蓝牌号")
        $(".showLphselect2").text("请选择单元")
        $(".showLphselect3").text("请选择楼层")
        $(".showLphselect4").text("请选择具体户室")
      }else if(a == 2){//点击选择楼栋蓝牌号
        $("#listUnitId").html("")
        $("#listDetailId").html("")
        $("#listFloor").html("")
        $(".showLphselect2").text("请选择单元")
        $(".showLphselect3").text("请选择楼层")
        $(".showLphselect4").text("请选择具体户室")
      }else if(a == 3) {//点击选择单元
        $("#listDetailId").html("")
        $("#listFloor").html("")
        $(".showLphselect3").text("请选择楼层")
        $(".showLphselect4").text("请选择具体户室")
      }else if(a == 4) {//点击选择层
        $("#listFloor").html("")
        $(".showLphselect4").text("请选择具体户室")
      }
      $("#lph").find(".errorBox").html('')
    }

    //点击小区下拉选中
    $(document).on('click','.newSubListLi',function(){
      clearLph(1)
      dataSubName = $(this).text()
      dataSubId = $(this).attr("data-subId")
      dataNewSub = $(this).attr("data-newSub")
      $("#newsub").val(dataNewSub)
      $("#SubID").val(dataSubId)
      $("#subname").val(dataSubName)
      $("#searchSubName").val(dataSubName)
      showSubName = dataSubName
      console.log(dataSubName,dataSubId,dataNewSub)
      $(".newSubList").hide()
      if (dataNewSub == 0) { //没有蓝牌号
        $("#lph").hide()
        $(".lddysh").show()
      } else {
        $("#lph,.nolph").show()
        $(".lddysh").hide()
        $.ajax({
          url: host + "viewArchBySubId",
          data: {
            subId: dataSubId
          },
          dataType: 'json',
          type: 'POST',
          success: function (data) {
            console.log(data)
            if (data.status == 1) {
              moveErrorMsg("li_r_1")
              $("#listArchId").html("")
              data = data.content
              for (var i = 0; i < data.length; i++) {
                listArchId = '<li class="ArchIdLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
                $("#listArchId").append(listArchId)
              }
            }
          }
        })
      }

    })

    // 点击楼栋号 展示下方下拉数据
    $(document).on('click','.showLphselect1',function(){
      $("#lph ul").hide()
      if( showSubName == "" || showSubName == undefined){
        errorMsg("li_r_1","请从下拉框中选择小区","subname")
      }else{
        $("#listArchId").show()
      }
    })
    //选择楼栋 后 生成单元选项
    $(document).on('click','.ArchIdLi',function(){
      clearLph(2)
      dataArchId = $(this).attr("data-id")
      dataArchTxt = $(this).text()
      $(".showLphselect1").text(dataArchTxt)
      $("#listArchId").hide()
      $(".showLphselect1").attr("data-val",dataArchTxt)
      showLphselect1  = dataArchTxt
      $.ajax({
        url: host + "viewUnitByArch",
        data: {
          archId: dataArchId
        },
        dataType: 'json',
        type: 'POST',
        success: function (data) {
          console.log(data)
          if (data.status == 1) {
            moveErrorMsg("li_r_2")
            $("#listUnitId").html("")
            data = data.content
            for (var i = 0; i < data.length; i++) {
              listUnitId = '<li class="UnitIdLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
              $("#listUnitId").append(listUnitId)
            }
          }
        }
      })
    })
    // 点击单元
    $(document).on('click','.showLphselect2',function(){
      $("#lph ul").hide()
      if( showSubName == "" || showSubName == undefined){
        errorMsg("li_r_1","请从下拉框中选择小区","subname")
      }else if ( showLphselect1 == "" || showLphselect1 == undefined){
        errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
      }else{
        $("#listUnitId").show()
      }
    })
    //选择单元 后 生成楼层
    $(document).on('click','.UnitIdLi',function(){
      clearLph(3)
      dataUnitId = $(this).attr("data-id")
      dataUnitTxt = $(this).html()
      $(".lphselect2 span").text(dataUnitTxt)
      $("#listUnitId").hide()
      $(".showLphselect2").attr("data-val",dataUnitTxt)
      showLphselect2  = dataUnitTxt
      $.ajax({
        url: host + "viewFloorByUnitAndArch",
        data: {
          archId: dataArchId,
          unit:dataUnitId
        },
        dataType: 'json',
        type: 'POST',
        success: function (data) {
          console.log(data)
          if (data.status == 1) {
            moveErrorMsg("li_r_2")
            $(".listDetailId").html("")
            data = data.content
            for (var i = 0; i < data.length; i++) {
              listUnitId = '<li class="listDetailLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
              $("#listDetailId").append(listUnitId)
            }
          }
        }
      })

    })
    // 点击楼层
    $(document).on('click','.showLphselect3',function(){
      $("#lph ul").hide()
      if( showSubName == "" || showSubName == undefined){
        errorMsg("li_r_1","请从下拉框中选择小区","subname")
      }else if ( showLphselect1 == "" || showLphselect1 == undefined){
        errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
      }else if ( showLphselect2 == "" || showLphselect2 == undefined){
        errorMsg("new-sub-lph","请从下拉框中选择单元","lph")
      }else{
        $("#listDetailId").show()
      }
    })
    // 点击楼层显示下拉选项
    $(document).on('click','.listDetailLi',function(){
      clearLph(4)
      dataFloor = $(this).attr("data-id")
      dataUnitTxt = $(this).html()
      $(".lphselect3 span").text(dataUnitTxt)
      $("#listDetailId").hide()
      showLphselect3 = dataUnitTxt
      $(".showLphselect3").attr("data-val",dataUnitTxt)
      $.ajax({
        url: host + "viewDetailFilter",
        data: {
          archId: dataArchId,
          unit:dataUnitId,
          floor:dataFloor
        },
        dataType: 'json',
        type: 'POST',
        success: function (data) {
          console.log(data)
          if (data.status == 1) {
            moveErrorMsg("li_r_2")
            $("#listFloor").html("")
            data = data.content
            for (var i = 0; i < data.length; i++) {
              listUnitId = '<li class="listFloorLi" data-id="' + data[i].id + '" >' + data[i].name + '</li>'
              $("#listFloor").prepend(listUnitId)
            }
          }
        }
      })

    })
    // 点击户室
    $(document).on('click','.showLphselect4',function(){
      $("#lph ul").hide()
      if( showSubName == "" || showSubName == undefined){
        errorMsg("li_r_1","请从下拉框中选择小区","subname")
      }else if ( showLphselect1 == "" || showLphselect1 == undefined){
        errorMsg("new-sub-lph","请从下拉框中选择楼栋蓝牌号","lph")
      }else if ( showLphselect2 == "" || showLphselect2 == undefined){
        errorMsg("new-sub-lph","请从下拉框中选择单元","lph")
      }else if ( showLphselect3 == "" || showLphselect3 == undefined){
        errorMsg("new-sub-lph","请从下拉框中选择楼层","lph")
      }else{
        $("#listFloor").show()
      }
    })
    // 点击户室显示下拉选项
    $(document).on('click','.listFloorLi',function(){
      dataFloor = $(this).attr("data-id")
      $("#detailid").val(dataFloor)
      dataLph = $(this).html()
      $(".lphselect4 span").text(dataLph)
      $("#listFloor").hide()
      $(".nolph").show()
      moveErrorMsg("li_r_2")
      $(".showLphselect4").attr("data-val",dataLph)

    })
    //点击没有符合户室，清除掉已选
    $(document).on('click','.nolph span',function(){
      $("#lph").hide()
      $(".nolph").hide()
      $(".lddysh").show()
      dataNewSub = 0

    })

    // 点击关闭下拉内容
    $("*").not(".li_r_1").on('click', function () {
      $(".newSubList").hide()
    })
    $("*").not(".li_r_2").on('click', function () {
      $(".li_r_2 ul").hide()
    })

    $('#searchSubName').on('blur', function () {
      var searchVal = $('#searchSubName').val();
      var choiceVal = $('#subname').val();
      if (searchVal != choiceVal) {
        $('#SubID').val('')
        $('#subname').val('')
      }
    })

    $('#HouseOwner').on('focus', function () {moveErrorMsg("li_r_3")})
    $('#txt_LoginName').on('focus', function () {moveErrorMsg("li_r_4")})
    $('#txt_Password').on('focus', function () {moveErrorMsg("li_r_5")})



    // 点击确认提交
    $(document).on('click','.trustHouse_btn',function(){
      var searchSubName = $("#searchSubName").val() // 小区名字
      var lph1 = $(".showLphselect1").attr("data-val")
      var lph2 = $(".showLphselect2").attr("data-val")
      var lph3 = $(".showLphselect3").attr("data-val")
      var lph4 = $(".showLphselect4").attr("data-val")
      var budget = $("#SalePrice").val()
      var HouseOwner = $("#HouseOwner").val()
      var OwnerPhone = $("#txt_LoginName").val()
      var messageCode = $("#txt_Password").val()
      var agreType = $(".agre").hasClass("hover")
      var gdxdt = $(".gdx").attr('dt')
      if(dataNewSub != 0 ){
        if(lph1 == "" || lph1 == undefined){
          errorMsg("li_r_2","请选择门牌信息")
          return false;
        }else if(lph2 == "" || lph2 == undefined){
          errorMsg("li_r_2","请选择单元")
          return false;
        }else if(lph3 == "" || lph3 == undefined){
          errorMsg("li_r_2","请选择楼层")
          return false;
        }else if(lph4 == "" || lph4 == undefined){
          errorMsg("li_r_2","请选择具体户室")
          return false;
        }
      }

      if(searchSubName == ""){
        errorMsg("li_r_1","请从下拉框中选择小区")
        return false;
      }else if(gdxdt==''){
        errorMsg("li_r_10","请选择出租方式")
        return false;
      }else if(HouseOwner == "" || HouseOwner == undefined){
        errorMsg("li_r_3","请填写您的称呼")
        return false;
      }else if(OwnerPhone == "" || OwnerPhone == undefined){
        errorMsg("li_r_4","请填写您的联系电话")
        return false;
      }else if(messageCode == "" || messageCode == undefined){
        errorMsg("li_r_5","请填写验证码")
        return false;
      }else if(agreType == false){
        errorMsg("li_r_6","请点击同意《房本认证隐私条款》")
        return false;
      }else{
        var area ;
        if(budget == "" || budget == null ||budget == undefined){
          budget = "无"
        }
        if( dataNewSub == 0){
          area= "无"
        }else{
          area =lph1+lph2+lph3+lph4
        }
        if(gdxdt==1){
          gdxdt='整租'
        }else{
          gdxdt='合租'
        }
        $.ajax({
          type:"post",
          url: ltapi + "apiv1/active/saveGuide",
          async:false,
          data:{
            type:"31",
            region:searchSubName,
            budget:budget,
            area:area,
            phone:OwnerPhone,
            code:messageCode,
            sessionId:"",
            housetype:HouseOwner,
            italy:"Sy站",
            method:gdxdt
          },
          success:function(data){
            console.log(data)
            if (data.status==1){//提交成功
              $(".tc_trust").show();
              $(".tc_full").show();
            }else {
              alert(data.msg)

            }
          },error:function(data){

          }
        });
      }
    })

    // 点击关闭成功弹窗
    $(document).on('click','.trust_colse',function(){
      $(window).scrollTop(0);
      window.location.reload();
    })
    // 是否勾选协议
    $(document).on('click','.agre',function(){
      $('.agre').toggleClass('hover');
    })
  })
  //修改Bug #15906
  function noSelect(){
    var noselect= $(".ac_results ul li").eq(0).text();
    if(noselect=="未找到对应的小区"){
      $(".ac_results ul li").removeClass("ac_even");
      $(".ac_results ul").addClass("noselect");
      $(".ac_results ul li").addClass("noselect");
    }else{
      $(".ac_results ul").removeClass("noselect");
      $(".ac_results ul li").removeClass("noselect");

    }
  }


  //出租方式
  $(".gdxm").click(function(){
    $(this).find('em').addClass('ghv')
    $(this).siblings().find('em').removeClass('ghv')
    $('.gdx').attr('dt',$(this).attr('dt'))
    $(".li_r_10 .errorBox").text('')//清空错误提示
  })

  //修改 门牌信息颜色
  $(document).on('click','.ArchIdLi',function(){
    $(".showLphselect1").css("color","#000")
  })
  $(document).on('click','.UnitIdLi',function(){
    $(".showLphselect2").css("color","#000")
  })
  $(document).on('click','.listDetailLi',function(){
    $(".showLphselect3").css("color","#000")
  })
  $(document).on('click','.listFloorLi',function(){
    $(".showLphselect4").css("color","#000")
  })
</script>
<!--页面底部-->
<div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<!--<div th:include="fragment/fragment::commonFloat"></div>-->
</body>
</html>
