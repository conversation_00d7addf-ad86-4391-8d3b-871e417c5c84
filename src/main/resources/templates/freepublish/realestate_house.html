<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳二手房信息发布_免费信息发布_个人房源 - 房小二网</title>
    <meta name="keywords" content="二手房信息发布,个人房源,沈阳二手房信息,二手房中介,免费信息发布"/>
    <meta name="description" content="房小二网为您提供专业贴心的二手房服务，为您提供免费二手房信息发布，以及专属房源页面进行全网展示与免费推广等全面服务，帮你快速找到精准客户。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/pubSale.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/images/sy/check/form1.css?=t0902" rel="stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.validate.min1.js"></script>
    <script type="text/javascript" src="/js/freepublish/personal.js"></script>
    <script type="text/javascript" src="/js/AjaxforJquery.js"></script>
    <script type="text/javascript" src="/js/jquery.autocomplete.js"></script>
<!--    自动切换验证码输入框-->
<!--    <script type="text/javascript" src="/js/freepublish/tese.js"></script>-->

    <style>
        .hide{ display: none !important;}
        .fk { width: 18px; height: 18px; position: absolute; top: 66px; right: -30px; cursor: pointer; user-select: none; background-image: url(https://static.fangxiaoer.com/web/images/sy/check/kot.png); background-size: 100%; background-repeat: no-repeat; background-position: center; }
        .cben { position: absolute; z-index: 99999999; top: -79px; right: -295px; box-shadow: 0px 0px 17px #6a6a6c; border-radius: 5px; background-color: #fff; padding: 5px; box-sizing: border-box; display: none; }
        .cben img { height: 350px; border-radius: 5px; }
        .cil { width: 0; height: 0; border-top: 10px solid transparent; border-right: 13px solid #fff; border-bottom: 10px solid transparent; position: absolute; left: -13px; top: 144px; }
        .cbg { width: 38px; height: 39px; background-image: url(https://static.fangxiaoer.com/web/images/sy/check/icon_close.png); background-size: 100%; background-repeat: no-repeat; background-position: center; position: absolute; left: 0; right: 0; bottom: -108px; margin: auto; cursor: pointer; user-select: none; }
        .fi2 { width: 14px; height: 14px; position: absolute; top: 26px; right: 0; cursor: pointer; user-select: none; background-image: url(https://static.fangxiaoer.com/web/images/sy/check/icon_notes.png); background-size: 100%; background-repeat: no-repeat; background-position: center; }
        .fi2m { position: absolute; top: -3px; right: -286px; width: 244px; height: 50px; background: #FFFFFF; border-radius: 9px; z-index: 99999; box-shadow: 0px 0px 12px #8b8b8f; line-height: 23px; padding: 13px; font-size: 14px; font-family: Microsoft YaHei; font-weight: 400; color: #101D37; display: none; }
        .clf2 { display: block; width: 0; height: 0; border-top: 10px solid transparent; border-right: 11px solid #fff; border-bottom: 10px solid transparent; position: absolute; left: -11px; top: 25px; }
        .cgm { display: inline-block; padding-left: 43px; box-sizing: border-box; height: 65px; }
        .cgmi { display: inline-block; font-size: 14px; cursor: pointer; user-select: none; font-family: Microsoft YaHei; font-weight: 400; color: #9399A5; position: relative; }
        .cgmi em { width: 14px; height: 14px; display: inline-block; background-image: url(https://static.fangxiaoer.com/web/images/sy/check/kon.png); background-size: 100%; background-repeat: no-repeat; background-position: center; position: absolute; top: 26px; left: -20px; cursor: pointer; user-select: none; }
        .cgmi:nth-child(1) { margin-right: 55px; }
        .cgmi:nth-child(2) { margin-right: 55px; }
        .kon em { background-image: url(https://static.fangxiaoer.com/web/images/sy/check/koy.png) !important; }
        .showarg { display: none; }

        input::-webkit-input-placeholder { color: #9399A5 !important;}
        input:-moz-placeholder { color: #9399A5 !important;}
        input::-moz-placeholder { color: #9399A5 !important;}
        input:-ms-input-placeholder {color: #9399A5 !important;}

        textarea::-webkit-input-placeholder { color: #9399A5 !important;}
        textarea:-moz-placeholder { color: #9399A5 !important;}
        textarea::-moz-placeholder { color: #9399A5 !important;}
        textarea:-ms-input-placeholder {color: #9399A5 !important;}

        .g_ftmx{ text-align: center; font-size: 12px;font-family: Microsoft YaHei-Regular, Microsoft YaHei; font-weight: 400; color: #666666;line-height: 16px; margin-left: 0 !important;}



   .check-box{
       margin-top: 60px;
   }
        .ownerBox{
            display: flex;
            align-items: center;
            width: 990px;
        }
   .oText{
       font-size: 14px;
       font-family: Microsoft YaHei-Regular, Microsoft YaHei;
       font-weight: 400;
       color: #111111;
       white-space: nowrap;
   }
  .oText2{
      font-size: 12px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #999999;
  }
        .ownerName{
            width: 288px;
            height: 40px;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            border: 1px solid #CCCCCC;
            margin-left: 20px;
            text-indent: 6px;
        }

        .phoneBox{
            display: flex;
            align-items: center;
            margin-top: 26px;
            width: 990px;
        }
        .pText{
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #111111;
        }
        .pText2{
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #999999;
        }
        .ownerPhone{
            width: 288px;
            height: 40px;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
            border: 1px solid #CCCCCC;
            margin-left: 20px;
            text-indent: 6px;
        }

        .codeBtn{
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #111111;
            margin-left: 23px;
            white-space: nowrap;
        }
        .codeBtn:hover{

            cursor: pointer;
        }
        .code-box{
            display: flex;

            align-items: center;
            margin-top: 25px;
            margin-left: 76px;

        }
        .c-text{
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #111111;
            white-space: nowrap;
        }

        .code-right{
            display: flex;
            align-items: center;
            margin-left: 21px;
            white-space: nowrap;
        }
        .code1{
            width: 40px;
            height: 40px;
            background: #F7F7F7;
            border-radius: 0px 0px 0px 0px;
            opacity: 1;
            margin-right: 16px;
            text-align: center;
        }
        .code2{
            width: 40px;
            height: 40px;
            background: #F7F7F7;
            border-radius: 0px 0px 0px 0px;
            opacity: 1;
            margin-right: 16px;

            text-align: center;
        }
        .code3{
            width: 40px;
            height: 40px;
            background: #F7F7F7;
            border-radius: 0px 0px 0px 0px;
            opacity: 1;
            margin-right: 16px;
            text-align: center;
        }
        .code4{
            width: 40px;
            height: 40px;
            background: #F7F7F7;
            border-radius: 0px 0px 0px 0px;
            opacity: 1;
            text-align: center;
            margin-right: 16px;

        }
        .code5{
            width: 40px;
            height: 40px;
            background: #F7F7F7;
            border-radius: 0px 0px 0px 0px;
            opacity: 1;
            margin-right: 16px;

            text-align: center;
        }

        .code6{
            width: 40px;
            height: 40px;
            background: #F7F7F7;
            border-radius: 0px 0px 0px 0px;
            opacity: 1;
            margin-right: 16px;

            text-align: center;
        }
        .err1{
            margin-left: 23px;
            color: #FF5200;
            font-size: 12px;
            white-space: nowrap;
        }
        .err2{
            margin-left: 23px;
            color: #FF5200;
            font-size: 12px;
            white-space: nowrap;
        }
        .validateCode{
            margin-left: 23px;
            color: #FF5200;
            font-size: 12px;
            white-space: nowrap;
        }

        .err3{
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #EF2F22;
        }

        .real-box{
            display: flex;
            align-items: center;
            margin-top: 25px;
            margin-left: 176px;
        }
      .real-text{
          font-size: 18px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #333333;
      }
        .real-img{
            width: 32px;
            height: 31px;
            margin-left: 16px;
        }



        .navht:hover{
            cursor: pointer;
        }

        .vsp{background: none;text-align:left;margin-right: -32px !important;}
        .vtm1,.vtm2{padding-top:0}
        .proBlue{width: 377px !important;}
        .newReal .navht .set{width: 270px;background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 245px 16px no-repeat;}
        .newReal .navht .newkk,.newReal .navht{width: 280px}
        .saleHouseStep {margin-top: 85px;}
        .successMsgTc1{height: 349px;}
        .success_m{padding-top: 68px}
        .successMsgTc2{width: 654px;padding-top: 68px;height: 295px !important;}

        .success_l{width: 252px;float: left;margin-left: 46px;border-right: 1px #E2E2E2 solid;padding-right: 29px;}
        .ics2{width: 57px;height: 51px;margin: 0 auto;background-image: url(https://static.fangxiaoer.com/web/images/sy/check/icon_success2.png);background-size: 100%;background-repeat: no-repeat;background-position: center;margin-bottom: 25px;}
        .success_l p{width: 252px;font-size: 14px;font-family: Microsoft YaHei-Regular, Microsoft YaHei;font-weight: 400;color: #333333;line-height: 25px;margin-bottom: 38px;}
        .chbtn0{width: 255px;height: 50px;border-radius: 8px 8px 8px 8px;opacity: 1;line-height: 50px;text-align: center;color: #fff;font-size: 16px;font-weight: bold;cursor: pointer;}
        .chbtn1{background: #F4621E;}
        .chbtn2{background: #FF9D00;margin-top: 53px;margin-left: 27px;}
        .successMsgTc2 .success_m{width: 49%;float: left;padding-top: 0;}
        .pp1{color: #999999;font-size: 12px; width: 315px;margin: 0 auto;line-height: 15px;margin-top: 8px;}
        .pp2{color: #999999;font-size: 12px;float: left;margin-top: -45px;width: 461px;margin-left: 103px;text-align: left;line-height: 15px;}
    </style>
</head>
<body style="background: #fff !important;">
<script src="https://static.fangxiaoer.com/js/head2017.js" type="text/javascript"></script>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=8,subNavIndex=1"></div>
<!--<div class="title">为您提供专业贴心的二手房服务<p>免费咨询电话：400-893-9709</p></div>-->

<div class="kuang newReal">

    <div class="vmain">

        <div class="vmleft">
            <h5 class="newRealH5">房源审核</h5>
            <div class="vi"><em>小区名称</em><input class="arname" value="铁路小区" disabled></div>
            <div class="vi lph"><em>楼栋门牌信息</em><input class="proBlue" placeholder="请输入楼栋外墙体的蓝牌号信息 如：中山路397号" style="width: 55%;"></div>
            <div class="vi">
                <div class="vie" style="width: 117px;">
                    <i style="color:#ff5200">*</i>我是产权人
                    <!-- <div class="fi2"></div>
                     <div class="fi2m"><i class="clf2"></i>是</div>-->
                </div>
                <div class="cgm" >
                    <div class="cgmi vsp kon" i="m1"><em></em>是</div>
                    <!--                            <div class="cgmi cgk" k="k2"><em></em>无房产证号（老房本）</div>-->
                    <div class="cgmi vsp" i="m2"><em></em>否</div>
                </div>
            </div>
            <!--<div class="vtap">
                <div class="vsp act" i="m1">我是产权人</div>
                <div class="vsp" i="m2">我帮他人卖房</div>
            </div>-->
            <!--我是产权人-->
            <div class="vtm1">
                <!--<div class="vsf">
                    <div class="vtit">身份认证</div>
                    <div class="vi"><em>产权人姓名</em><input class="proname" placeholder="请输入产权人姓名"></div>
                    <div class="vi"><em>我的身份证号</em><input class="procard" placeholder="请输入身份证" style="width: 260px;"></div>
                    <div class="vu">
                        <em>产权人身份证照片</em>
                        <div class="vum">
                            <div class="vumi mright50 uploadz">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/img_id_positive.png" class="imgsz"/></div>
                                <div class="vuf vfa">上传身份证正面</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadFront" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadf">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/img_id_back.png" class="imgsf"/></div>
                                <div class="vuf vfb">上传身份证反面</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadBack" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                </div>-->
                <div class="vsf">
<!--                    <div class="vtit">房本认证</div>-->
                   <!--<div class="vi">
                        <div class="vie">
                            户室号
                            <b>*</b>
                            <span><i>请与房本信</i><i>息保持一致</i></span>
                        </div>
                        <input placeholder="单元" class="vit prounit">
                        <input placeholder="楼层" class="vit profloor">
                        <input placeholder="门牌号" class="vit prodoor">
                    </div>-->

                    <div class="vi">
                        <div class="vie" style="width: 117px;">
                            <i style="color:#ff5200">*</i>权属证件类型
                            <div class="fi2"></div>
                            <div class="fi2m"><i class="clf2"></i>请上传房产证、商品房购买合同</div>
                        </div>
                        <div class="cgm acth" dt="k1">
                            <div class="cgmi cgk kon" k="k1"><em></em>房本原件</div>
<!--                            <div class="cgmi cgk" k="k2"><em></em>无房产证号（老房本）</div>-->
                            <div class="cgmi cgk" k="k3"><em></em>商品房买卖合同</div>
                        </div>
                    </div>
                   <!-- <div class="vi shwoper">
                        <div class="vie">房产证号<div class="fi"></div></div>
                        <input placeholder="请输入房产证号" class="proCertificate" style="width: 260px;">
&lt;!&ndash;                        <div class="vir noho"><em class="nozh"></em>无房产证号（老房本）</div>&ndash;&gt;
                    </div>-->
                    <div class="vu shwoper">
                        <em style="color: #666666"><i style="color:#ff5200">*</i>请上传产权人房产照片<span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的房产证信息将不对外展示</span></em>
                        <div class="vum">
                            <div class="vumi mright50 uploadCard">
                                <i>不对外展示</i>
                                <p>上传房本原件首页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png" class="cardz"/></div>
<!--                                <div class="vuf">上传房本原件首页</div>-->
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInc" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadCardf">
                                <i>不对外展示</i>
                                <p>上传房本原件信息页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/7.png" class="cardf"/></div>
<!--                                <div class="vuf">上传房本原件信息页</div>-->
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInf" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu shwoper2" style="display: none;">
                        <em style="color: #666666"><i style="color:#ff5200">*</i>请上传产权人房产照片<span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的房产证信息将不对外展示</span></em>
                        <div class="vum" style="position: relative; ">
                            <div class="vumi mright50 uploadCard2">
                                <i>不对外展示</i>
                                <p>上传房本原件首页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png" class="cardz2"/></div>
<!--                                <div class="vuf">上传房本原件首页</div>-->
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInc2" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadCardf2 .mright50">
                                <i>不对外展示</i>
                                <p>上传房本原件信息页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/7.png" class="cardf2"/></div>
<!--                                <div class="vuf">上传房本原件信息页</div>-->
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInf2" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu showarg">
                        <em style="color: #666666"><i style="color:#ff5200">*</i>请上传商品房买卖合同照片<span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的商品房买卖合同将不对外展示</span></em>
                        <div class="vum" style="position: relative; ">
                            <div class="vumi mright50 uploadContract" style="position: relative;">
                                <i>不对外展示</i>
                                <p>上传买受人信息页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png" dat="" class="vcont"/></div>
<!--                                <div class="vuf">上传买受人信息页</div>-->
                                <div class="vclose"></div>
                                <div class="fk f1"></div>
                                <div class="cben m1">
                                    <div class="cil"></div>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/check/kor.png"/>
                                </div>
                            </div>
                            <input type="file" id="uploadInt" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadContract2" style="position: relative;">
                                <i>不对外展示</i>
                                <p>上传房屋基本信息页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png" dat="" class="vcont2"/></div>
<!--                                <div class="vuf">上传房屋基本信息页</div>-->
                                <div class="vclose"></div>
                                <div class="fk f2"></div>
                                <div class="cben m2">
                                    <div class="cil"></div>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/check/kor.png"/>
                                </div>
                            </div>
                            <input type="file" id="uploadInt2" accept="image/*" style="display: none !important;">
                        </div>
                    </div>






                    <div class="vu">
                        <em>补充认证描述（选填）</em>
                        <textarea class="prosupplement" placeholder="如：提供其他法律有效的证件，来证明此房源真实性和有效性"></textarea>
                    </div>
                    <div class="vu">
                        <em>补充认证</em>
                        <div class="vumi mright50 uploadbcrz1" style="width: 102px;height: 102px">
                            <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/8.png" class="imgbcrz1"/></div>
                            <div class="vclose"></div>
                        </div>
                        <input type="file" id="uploadBCRZ1" accept="image/*" style="display: none !important;">
                    </div>
                    <div class="vu" style="float: left">
                        <div class="vf">
                            <em class="agre" style="top: -20px"></em><i>同意</i><a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房本认证隐私条款》</a>
                        </div>
                    </div>
                    <div class="subt propbt">提交</div>
                </div>
            </div>

            <!--我帮他人卖房-->
            <div class="vtm2">
                <div class="vi">
                 <!--   <div class="vtit">身份认证</div>
                    <div class="vi"><em>我的姓名</em><input class="helpmyname" placeholder="请输入姓名"></div>
                    <div class="vi"><em>我的身份证号</em><input placeholder="请输入身份证号" class="helpmycard" style="width: 260px;"></div>-->
                    <!--<div class="vi"><em>小区名称</em><input class="helparea" value="铁路小区" disabled></div>
                    <div class="vi lph"><em>楼栋门牌信息</em><input placeholder="请输入楼栋外墙体的门牌信息 如：中山路397号" style="width: 55%;" class="helpBlue"></div>
-->


                    <!--<div class="vi"><em>产权人姓名</em><input class="helpname" placeholder="请输入产权人姓名"></div>
                    <div class="vi"><em>产权人身份证号</em><input placeholder="请输入产权人身份证号" class="helpcard" style="width: 260px;"></div>
                    <div class="vu">
                        <em>产权人身份证照片</em>
                        <div class="vum">
                            <div class="vumi mright50 helpCardz">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/img_id_positive.png" class="imghcz"/></div>
                                <div class="vuf">上传身份证正面</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="helpupz" accept="image/*" style="display: none !important;">
                            <div class="vumi helpCardf">
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/check/img_id_back.png" class="imghcf"/></div>
                                <div class="vuf">上传身份证反面</div>
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="helpupf" accept="image/*" style="display: none !important;">
                        </div>
                    </div>-->
                </div>
                <!--<div class="vi">
                    <div class="vie" style="width: 117px;">
                        <i style="color:#ff5200">*</i>我是产权人
                        &lt;!&ndash; <div class="fi2"></div>
                         <div class="fi2m"><i class="clf2"></i>是</div>&ndash;&gt;
                    </div>
                    <div class="cgm acth" >
                        <div class="cgmi vsp " i="m12"><em></em>是</div>
                        &lt;!&ndash;                            <div class="cgmi cgk" k="k2"><em></em>无房产证号（老房本）</div>&ndash;&gt;
                        <div class="cgmi vsp kon" i="m22"><em></em>否</div>
                    </div>
                </div>-->
                <div class="vsf">
                    <div class="vi">
                        <em>与产权人关系</em>
                        <div class="navht" id="nav" >
                            <p class="set helprelation" id="00">请选择关系</p>
                            <ul class="newkk">
                                <li></li>
                            </ul>
                        </div>
                    </div>
<!--                    <div class="vtit">房本认证</div>-->
                    <!--<div class="vi">
                        <div class="vie">
                            户室号
                            <b>*</b>
                            <span><i>请与房本信</i><i>息保持一致</i></span>
                        </div>
                        <input placeholder="单元" class="vit helpunit">
                        <input placeholder="楼层" class="vit helpfloor">
                        <input placeholder="门牌号" class="vit helpdoor">
                    </div>-->
                    <div class="vi">
                        <div class="vie" style="width: 117px;">
                            权属证件类型
                            <div class="fi2"></div>
                            <div class="fi2m"><i class="clf2"></i>请上传房产证、商品房购买合同</div>
                        </div>
                        <div class="cgm acth2" dt="h1">
                            <div class="cgmi cgh kon" k="h1"><em></em>房本原件</div>
<!--                            <div class="cgmi cgh" k="h2"><em></em>无房产证号（老房本）</div>-->
                            <div class="cgmi cgh " k="h3"><em></em>商品房买卖合同</div>
                        </div>
                    </div>
                    <!--<div class="vi showvi3">
                        <div class="vie">房产证号<div class="fi"></div></div>
                        <input placeholder="请输入房产证号" class="helpCertificate" style="width: 260px;">
&lt;!&ndash;                        <div class="vir helpNoho"><em></em>无房产证号（老房本）</div>&ndash;&gt;
                    </div>-->
                    <div class="vu shwoper3">
                        <em style="color: #666666"><i style="color:#ff5200">*</i>请上传产权人房产照片<span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的房产证信息将不对外展示</span></em>
                        <div class="vum">
                            <div class="vumi mright50 helpHouz">
                                <i>不对外展示</i>
                                <p>上传房本原件首页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png" class="imghz"/></div>
<!--                                <div class="vuf">上传房本原件首页</div>-->
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="helphz" accept="image/*" style="display: none !important;">
                            <div class="vumi helpHouf">
                                <i>不对外展示</i>
                                <p>上传房本原件信息页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/7.png" class="imghf"/></div>
<!--                                <div class="vuf">上传房本原件信息页</div>-->
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="helphf" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu shwoper33" style="display: none;">
                        <em style="color: #666666"><i style="color:#ff5200">*</i>请上传产权人房产照片<span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的房产证信息将不对外展示</span></em>
                        <div class="vum" style="position: relative; ">
                            <div class="vumi mright50 uploadCard3">
                                <i>不对外展示</i>
                                <p>上传房本原件首页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png" class="cardz3"/></div>
<!--                                <div class="vuf">上传房本原件首页</div>-->
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInc3" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadCardf3 mright50">
                                <i>不对外展示</i>
                                <p>上传房本原件信息页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/7.png" class="cardf3"/></div>
<!--                                <div class="vuf">上传房本原件信息页</div>-->
                                <div class="vclose"></div>
                            </div>
                            <input type="file" id="uploadInf3" accept="image/*" style="display: none !important;">
                        </div>
                    </div>
                    <div class="vu showarg3" style="display: none" >
                        <em style="color: #666666"><i style="color:#ff5200">*</i>请上传商品房买卖合同照片<span style="color:#ff5200;font-size: 12px;margin-left: 7px">您填写的商品房买卖合同将不对外展示</span></em>
                        <div class="vum" style="position: relative; ">
                            <div class="vumi mright50 uploadContract4" style="position: relative;">
                                <i>不对外展示</i>
                                <p>上传买受人信息页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png" dat="" class="vcont4"/></div>
<!--                                <div class="vuf">上传买受人信息页</div>-->
                                <div class="vclose"></div>
                                <div class="fk f1"></div>
                                <div class="cben m1">
                                    <div class="cil"></div>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/check/kor.png"/>
                                </div>
                            </div>
                            <input type="file" id="uploadInt4" accept="image/*" style="display: none !important;">
                            <div class="vumi uploadContract5" style="position: relative;">
                                <i>不对外展示</i>
                                <p>上传房屋基本信息页</p>
                                <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png" dat="" class="vcont5"/></div>
<!--                                <div class="vuf">上传房屋基本信息页</div>-->
                                <div class="vclose"></div>
                                <div class="fk f2"></div>
                                <div class="cben m2">
                                    <div class="cil"></div>
                                    <img src="https://static.fangxiaoer.com/web/images/sy/check/kor.png"/>
                                </div>
                            </div>
                            <input type="file" id="uploadInt5" accept="image/*" style="display: none !important;">
                        </div>
                    </div>

                    <!-- 房主验真-->
                    <!--<div class="check-box">
                        <div class="ownerBox">  <div class="oText">产权人姓名<span class="oText2">（选填）</span> </div>
                            <input class="ownerName"  placeholder="请输入产权人姓名" maxlength="6" />   <span class="err1"></span>
                        </div>

                        <div class="phoneBox">  <div class="pText">产权人电话<span class="pText2">（选填）</span> </div>
                            <input class="ownerPhone" maxlength="11" type="text" oninput="value=value.replace(/[^\d]/g,'')"  placeholder="请输入产权人电话" />
                            <div class="codeBtn">获取验证码</div>
                            <div class="validateCode" style="display: none; top: 16px;"></div>
                            <span class="err2"></span>

                        </div>

&lt;!&ndash;                        未验真&ndash;&gt;
                        <div class="code-box"> <div class="c-text ">验证码</div>
                       <div class="code-right"> <input class="code1" maxlength="1" type="text" oninput="value=value.replace(/[^\d]/g,'')" />
                           <input class="code2" maxlength="1" type="text" oninput="value=value.replace(/[^\d]/g,'')"/>
                           <input class="code3" maxlength="1" type="text" oninput="value=value.replace(/[^\d]/g,'')" />
                           <input class="code4"  maxlength="1" type="text" oninput="value=value.replace(/[^\d]/g,'')" />
                           <input class="code5" maxlength="1" type="text" oninput="value=value.replace(/[^\d]/g,'')" />
                           <input class="code6" maxlength="1" type="text" oninput="value=value.replace(/[^\d]/g,'')" />
                           <span class="err3"></span>
&lt;!&ndash;                           用于是否验真字段判断&ndash;&gt;
                           <span class="isVerify" style="display: none"> </span>

                       </div>
                        </div>
&lt;!&ndash;                        验真已经通过&ndash;&gt;
                        <div class="real-box">
                        <div class="real-text">验真已通过 </div>
                            <img class="real-img" src="/img/newVerify.png" alt=""/>

                        </div>


                    </div>
-->

                    <div class="vu">
                        <em>补充认证描述（选填）</em>
                        <textarea class="helpsupplement" placeholder="如：提供其他法律有效的证件，来证明此房源真实性和有效性"></textarea>
                    </div>
                    <div class="vu">
                        <em>补充认证</em>
                        <div class="vumi mright50 uploadbcrz2" style="width: 102px;height: 102px">
                            <div class="vmimg"><img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/8.png" class="imgbcrz2"/></div>
                            <div class="vclose"></div>
                        </div>
                        <input type="file" id="uploadBCRZ2" accept="image/*" style="display: none !important;">
                    </div>
                    <div class="vu" style="float: left">
                        <div class="vf">
                            <em class="agre2" style="top: -20px"></em><i>同意</i><a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房本认证隐私条款》</a>
                        </div>
                    </div>
                    <div class="subt helpBtn">提交</div>
                </div>
            </div>
        </div>

        <div class="saleHouseStep">
            <div class="shs_cont1">
                <h4>卖房流程</h4>
                <ul class="shs_cont1_ul">
                    <i class="gun"></i>
                    <i class="gun2"></i>
                    <li class="shs_cont1_li"><i class="cont1_icon"></i><div><h4>填写信息</h4><p>填写要出售的房源基本信息</p></div></li>
                    <li class="shs_cont1_li"><i class="cont1_icon"></i><div><h4>房源审核<s>进行中</s></h4><p>上传审核资料，核实录入的房源信息</p></div></li>
                    <li class="shs_cont1_li"><i class="cont1_icon cont1_icon2"></i><div><h4>房源展示</h4><p>房源在房小二网站上展示</p></div></li>
                </ul>
            </div>
            <div class="shs_cont2">
                <h4>其它认证方式</h4>
                <div class="shs_cont2_div" style="padding-left: 0;">
                    <!--<div class="shs_cont2_img">
                        <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/1.png" alt="">
                        <p>扫码到小程序认证微信扫一扫直接认证</p>
                    </div>-->
                    <div class="shs_cont2_img" style="float: unset; margin: 0 auto;">
                        <img src="https://static.fangxiaoer.com/web/images/sy/trustHouse/2.png" alt="">
<!--                        <p>扫码下载打开APP<br>进入我要卖房 — 我的房产进行认证</p>-->
                    </div>
                    <div class="g_ftmx">扫码下载打开APP<br>进入我要卖房 — 我的房产进行认证</div>
                </div>
            </div>
        </div>

        <!--其他认证方式-->
        <!--<div class="vmright">
            <div class="vr_top">其他认证方式</div>
            <div class="v_code">
                <img src="https://static.fangxiaoer.com/web/images/sy/check/img_xcx.png" />
                <span>扫码到小程序认证</span>
                <span>微信扫一扫直接认证</span>
            </div>
            <div class="v_code">
                <img src="https://static.fangxiaoer.com/web/images/sy/check/img_app.png" />
                <span>扫码下载打开APP</span>
                <span>进入我要卖房—我的房产</span>
                <span>进行认证</span>
            </div>
        </div>-->

        <div class="cl"></div>
    </div>
</div>

<!--认证成功弹窗  已实名-->
<div class="successMsgTc successMsgTc1">
    <div class="success_m">
        <div class="closeSml"></div>
        <div class="ics"></div>
        <div class="sumsg">房源认证成功</div>
        <div class="smo"><span>房小二网客服正在核实基础信息</span></div>
        <div class="chbtn" onclick="location.href='https://my.fangxiaoer.com/house/houses0_1'">二手房管理</div>
        <p class="pp1">免责提醒：本平台展示为您私人电话，谨防有人恶意行骗，如遇骚扰/诈骗可下架房源，如遇到钱财损失请立即报警。</p>
       <!-- <div class="multiple"><i class="left0"></i>真房源认证<i class="right0"></i></div>
        <div class="muft">排序优先<i>·</i>增加曝光<i>·</i>独有标识</div>-->
    </div>
</div>
<!--认证成功弹窗  未实名-->
<div class="successMsgTc w410 successMsgTc2">
    <div class="closeSml"></div>
    <div class="success_l">
        <div class="ics2"></div>
        <p>您的账号尚未实名认证，如果账号实名<span style="color: #E32D2D">房源可展示180天，可发布房源视频，</span>如账 号长时间未实名认证，房源将被下架。</p>
        <div class="chbtn0 chbtn1" onclick="location.href='https://my.fangxiaoer.com/userVerify'">账号实名认证</div>

    </div>
    <div class="success_m">

        <div class="ics"></div>
        <div class="sumsg">房源认证成功</div>
        <div class="smo"><span>房小二网客服正在核实基础信息</span></div>
        <div class="chbtn0 chbtn2" onclick="location.href='https://my.fangxiaoer.com/house/houses0_1'">二手房管理</div>
        <!-- <div class="multiple"><i class="left0"></i>真房源认证<i class="right0"></i></div>

         <div class="muft">排序优先<i>·</i>增加曝光<i>·</i>独有标识</div>-->
    </div>
    <p class="pp2">免责提醒：本平台展示为您私人电话，谨防有人恶意行骗，如遇骚扰/诈骗可下架房源，如遇到钱财损失请立即报警。</p>

</div>
<!--房产证号弹窗-->
<div class="seek">
    <div class="skm">
        <div class="stop">房产证号哪里找？</div>
        <div class="smoo">1.请输入房产证首页或详情页的编号</div>
        <img src="https://static.fangxiaoer.com/web/images/sy/check/img_home.png" />
        <div class="smoo">2.如果只有不动产单元号，也可输入</div>
        <img src="https://static.fangxiaoer.com/web/images/sy/check/img_next.png" />
        <div class="smoo">3.如果较早的房产证没有编号，请勾选“无房产证”</div>
        <div class="close_sk"></div>
    </div>
</div>
<div class="successMsgFull"></div>

<!--身份证信息-->
<input type="hidden" id="id_Name">
<input type="hidden" id="id_Num">
<input type="hidden" id="id_img_1">
<input type="hidden" id="id_img_2">

<!--底部-->
<div class="issuedInfo" th:include="fragment/fragment:: publish_footer1"></div>
<div class="cl"></div>
<div class="footer" th:include="fragment/fragment:: publish_footer2"></div>



<script th:inline="javascript">
// console.log('电话',phoneNum)
// 取出用户登录的电话
var phoneNum = [[${session.phoneNum}]];
console.log('电话2',phoneNum)

   // 获取验证码
    $(".codeBtn").click(function(){
        console.log('触发验证码')
        // 验证姓名
        var Text = $(".ownerName").val();
        console.log('用户名',Text)
        if (Text == "") {
           console.log('进入第一个')
            $(".err1").text("请输入产权人姓名");
            return false
        } else {

            $(".err1").text("");
           // return true
            console.log('进入第二个')
        }

        // 验证电话号

        var mobile = $(".ownerPhone").val();
        console.log(mobile)
        var reg = /^1[1,2,3,4,5,7,8,6,9,0]{1}[0-9]{1}[0-9]{8}$/;



        if (mobile == "") {

            $(".err2").text("请输入产权人手机号");

            return false
        } else if (!reg.test(mobile)) {

            $(".err2").text("手机号码格式不正确，请重新输入!");
            return false;
        } else if(mobile == phoneNum){
            $(".err2").text("禁止使用本人手机号");
            // $(".code1").attr("disabled","disabled");

            return false;
        } else {
            $(".codeBtn").show();
            $(".err2").text("");
            // return true;
        }

        console.log('触发验证码2',mobile)
        var wait = 60;



      // 获取验证码

    $.ajax({
        type: 'post',
        url: "/sendSmsCode",
        contentType: 'application/x-www-form-urlencoded',
        data: {
            mobile: mobile,

        },
        success: (res) => {
            console.log('dayin',res)

            if(res.status == 1){
                time();

            }else {
                alert(res.msg)
            }



        }
    })


        function time() {
            if (wait == 0) {
                $(".validateCode").hide(); // 隐藏秒数
                $(".codeBtn").show().html("重新获取");
                wait = 60;
            } else {
                $(".validateCode").show().html("在" + wait + "秒后重发");
                $(".codeBtn").hide();
                wait--;
                setTimeout(function () {
                        time();
                    },
                    1000);
            }
        }

    })



    $(function(){

        // 监听输入框
        $('.code-right input').bind('input propertychange change', function () {
            console.log('触发方法')
            if ($(this).val().length >= 1) {
                console.log('触发方法2')

                $(this).next().focus();
                var code1 = $(".code1").val();
                var code2 = $(".code2").val();
                var code3 = $(".code3").val();
                var code4 = $(".code4").val();
                var code5 = $(".code5").val();
                var code6 = $(".code6").val();

                var oName = $(".ownerName").val();
                var phone = $(".ownerPhone").val();
                console.log(phone)
                var reg = /^1[1,2,3,4,5,7,8,6,9,0]{1}[0-9]{1}[0-9]{8}$/;


                if (phone == "") {


                    $(".err2").text("请输入产权人手机号");

                    return false
                } else if (!reg.test(phone)) {

                    $(".err2").text("手机号码格式不正确，请重新输入!");
                    return false;
                } else {
                    // $(".codeBtn").show();
                    $(".err2").text("");

                }







                if( code1.length>=1 && code2.length>=1 && code3.length>=1 && code4.length>=1 && code5.length>=1 && code6.length>=1){
                    console.log('触发验真')
                    let newCode = code1.toString() + code2.toString() + code3.toString() + code4.toString() + code5.toString() + code6.toString()
                    console.log('验证码合集',newCode)



                   let aa={
                       mobile: phone,
                       code:newCode,
                   }
                   console.log(aa)
                    $.ajax({
                        type: 'post',
                        url: "https://ltapi.fangxiaoer.com/apiv1/base/verifySmsCode",
                        contentType: 'application/x-www-form-urlencoded',
                        data: {
                            mobile: phone,
                            code:newCode,

                        },
                        success: (res) => {


                            console.log('dayin',res)

                            if(res.status == 1){
                                // 房主验真通过
                                $(".isVerify").val('true');
                                $(".err3").hide()
                                $(".real-box").show();
                                $(".code-box").hide();
                                $(".codeBtn").hide();
                                $(".ownerName").attr("disabled","disabled");
                                $(".ownerPhone").attr("disabled","disabled");
                                $(".ownerName").css("background-color","#F5F5F5");
                                $(".ownerPhone").css("background-color","#F5F5F5");

                            }else {
                                // 房主验真不通过
                                $(".isVerify").val('false');
                                $(".err3").text(res.msg);
                            }



                        }
                    })


                }



            }
        });
        // 这里注意 点击空格也算输入字符  也会触发方法  把空格去掉就能继续输入一位数字
        //   $(".code-right").find("input").keyup(function () {
        //       if ($(this).val().length >=1) {
        //
        //           console.log('触发')
        //           $(this).next().focus();
        //       }
        //   });
    })




</script>


<script th:inline="javascript">
    var saleHouseId = [[${houseId}]];



    //备案合同弹窗
    $('.f1').mouseover(function() {
        $(".m1").show()
    });
    $('.f1').mouseout(function() {
        $(".m1").hide()
    });
    $('.f2').mouseover(function() {
        $(".m2").show()
    });
    $('.f2').mouseout(function() {
        $(".m2").hide()
    });

    //权属证件类型 弹窗
    $('.fi2').mouseover(function() {
        $(".fi2m").show()
    });
    $('.fi2').mouseout(function() {
        $(".fi2m").hide()
    });

    //产权人  切换
    $(".cgk").click(function(){
        var th=$(this).attr('k')
        $(this).addClass('kon')
        $(this).siblings().removeClass('kon')
        $(this).parent().attr('dt',th)
        //房本原件
        if(th=='k1'){
            $(".showarg").hide()//合同
            $('.proCertificate').removeAttr('disabled')
            $(".showvi").show()//房产证号
            $(".shwoper").show()//房证
            $(".shwoper2").hide()//老房证
        }
        //无房证号
        if(th=='k2'){
            $('.proCertificate').attr('disabled',"disabled").val('')
            $(".showvi").hide()//房产证号
            $(".showarg").hide()//合同
            $(".shwoper").hide()//房证
            $(".shwoper2").show()//老房证
        }
        //买卖合同
        if(th=='k3'){
            $(".showarg").show()//合同
            $(".shwoper").hide()//房证
            $('.proCertificate').attr('disabled',"disabled").val('')
            $(".showvi").hide()//房产证号
            $(".shwoper2").hide()//老房证
        }
    })

    //帮他人  切换
    $(".cgh").click(function(){
        var th=$(this).attr('k')
        $(this).addClass('kon')
        $(this).siblings().removeClass('kon')
        $(this).parent().attr('dt',th)
        //房本原件
        if(th=='h1'){
            $(".showarg3").hide()//合同
            $('.helpCertificate').removeAttr('disabled')
            $(".showvi3").show()//房产证号
            $(".shwoper3").show()//房证
            $(".shwoper33").hide()//老房证
        }
        //无房证号
        if(th=='h2'){
            $('.helpCertificate').attr('disabled',"disabled").val('')
            $(".showvi3").hide()//房产证号
            $(".showarg3").hide()//合同
            $(".shwoper3").hide()//房证
            $(".shwoper33").show()//老房证
        }
        //买卖合同
        if(th=='h3'){
            $(".showarg3").show()//合同
            $(".shwoper3").hide()//房证
            $('.helpCertificate').attr('disabled',"disabled").val('')
            $(".showvi3").hide()//房产证号
            $(".shwoper33").hide()//老房证
        }
    })


    //房产证号弹窗
    $(".fi").click(function(){
        $(".seek,.successMsgFull").show()
    })
    $(".close_sk").click(function(){
        $(".seek,.successMsgFull").hide()
    })
</script>

<script type="application/javascript">
var idents=0//0产权人 1帮他人卖房
var hasIndentity = ""
//房源账号是否认证
$.ajax({
    type: "POST",
    async: false,
    url: "/getRealEstateInfo",
    data:{"houseId":saleHouseId,"houseType":1},
    dataType : 'json',
    headers : {
        'Content-Type' : 'application/x-www-form-urlencoded'
    },
    success: function (data) {
        var res=data.content
        console.log('账号是否认证',res)
        //是否认证 0未认证 1认证
        if(res.authenticationStatus==0 || res.authenticationStatus==2 ){
            hasIndentity = 0
        }else{
            hasIndentity = 1
        }
        // 房主是否验真  1验真0未验真
       /* if(res.houseOwnerTelState == 1){



            console.log('房主已经验真')
            $(".isVerify").val('true');   // 设置一下状态

            $(".real-box").show();
            $(".code-box").hide();
            $(".ownerName").val(res.houseOwnerName);
            $(".ownerPhone").val(res.houseOwnerTel);
            $(".ownerName").attr("disabled","disabled");
            $(".ownerPhone").attr("disabled","disabled");
            $(".ownerName").css("background-color","#F5F5F5");
            $(".ownerPhone").css("background-color","#F5F5F5");

        }else {
            $(".isVerify").val('false');   // 设置一下状态




            console.log('房主未验真')
            $(".code-box").show();
            $(".real-box").hide();
        }*/

        //是否认证 0未认证 1认证
        /*if(res.hasIndentity==1){
            //产权人
            $(".proname").attr('disabled',"disabled").val(data.content.hasChecked.split(' ')[0])
            $(".procard").attr('disabled',"disabled").val(data.content.hasChecked.split(' ')[1])
            $(".vumi").removeClass('uploadz')//取消点击上传
            $(".vumi").removeClass('uploadf')//取消点击上传
            $(".vumi").find('.vclose').hide()//关闭按钮
            $(".imgsz").attr({'src': 'https://images1.fangxiaoer.com/' + data.content.cachePic[0].url + '/big_fxr_watermark','dat':data.content.cachePic[0].url})//身份证正面
            $(".imgsf").attr({'src': 'https://images1.fangxiaoer.com/' + data.content.cachePic[1].url + '/big_fxr_watermark','dat':data.content.cachePic[1].url})
            $(".vfa").text('身份证正面')
            $(".vfb").text('身份证反面')
            //帮他人
            $(".helpmyname").attr('disabled',"disabled").val(data.content.hasChecked.split(' ')[0])
            $(".helpmycard").attr('disabled',"disabled").val(data.content.hasChecked.split(' ')[1])

            // 身份证信息
            $("#id_Name").val(data.content.hasChecked.split(' ')[0])
            $("#id_Num").val(data.content.hasChecked.split(' ')[1])
            $("#id_img_1").val(data.content.cachePic[0].url)
            $("#id_img_2").val(data.content.cachePic[1].url)
        }*/
        $(".arname").val(res.subName)//小区名称
        $(".helparea").val(res.subName)//小区名称
        if(res.blueLabel != ""){
            $(".proBlue").val(res.blueLabel )
            $(".helpBlue").val(res.blueLabel )

        }else{
            $(".lph").hide()
        }

    }
});

//与产权人关系
$.ajax({
    type: "POST",
    async: false,
    url: "/relationShipFilter",
    data:{"houseId":saleHouseId},
    dataType : 'json',
    headers : {
        'Content-Type' : 'application/x-www-form-urlencoded'
    },
    success: function (data) {
        var res=data.content
        console.log(res)
        $(".newkk").empty()
        for(var i=0;i<res.length;i++){
            var str="<li id="+res[i].id+">"+res[i].name+"</li>"
            $(".newkk").append(str)
        }
    }
});
//提交修改真房源  产权人
$(".propbt").click(function(){
    // var proname=$(".proname").val()//产权人姓名
    // var procard=$(".procard").val()//产权人身份证
    var proarName=$(".arname").val()//小区名称
    var proBlue=$('.proBlue').val()//楼栋蓝牌号
    var prounit=$('.prounit').val()//单元
    var profloor=$('.profloor').val()//楼层
    var prodoor=$('.prodoor').val()//门牌号
    var proCertificate=$('.proCertificate').val()//房产证号
    var prosupplement=$('.prosupplement').val()//补充说明

    // var proFront=$(".imgsz").attr('dat')//身份证
    // var proBack=$(".imgsf").attr('dat')
    var proCardFront=$(".cardz").attr('dat')//房证
    var proCardBack=$(".cardf").attr('dat')
    var proCardFront2=$(".cardz2").attr('dat')//老房证
    var proCardBack2=$(".cardf2").attr('dat')

    var contractImg=$(".vcont").attr('dat')//商品房买卖合同
    var contractImg2=$(".vcont2").attr('dat')//商品房买卖合同
    var imageType=$(".acth").attr('dt')//权属证件类型
    var conimgt=''//0-房本原件，1-无房产证号（老房本），2-商品房买卖合同



    var proname = $("#id_Name").val()
    var procard = $("#id_Num").val()
    var proFront = $("#id_img_1").val()
    var proBack = $("#id_img_2").val()
    var bcsmImg1 = $(".imgbcrz1").attr('dat')
    if(bcsmImg1 == undefined){
        bcsmImg1 = ""
    }


    //表单验证
    var regName =/^[\u4e00-\u9fa5]{2,4}$/;
    /*if(!regName.test(proname)){
        if($(".proname").attr('disabled')!='disabled'){
            alert('请正确输入产权人姓名！');
            return false;
        }
    }*/
    var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    /*if(reg.test(procard) === false)
    {
        if($(".procard").attr('disabled')!='disabled'){
            alert("请正确输入身份证号！");
            return
        }
    }*/
    /*if($(".imgsz").attr('dat')==undefined && $(".imgsz").attr('dat')!='def' || $(".imgsz").attr('dat')==''){
        alert('请上传产权人身份证正面！')
        return
    }
    if($(".imgsf").attr('dat')==undefined && $(".imgsf").attr('dat')!='def' || $(".imgsf").attr('dat')==''){
        alert('请上传产权人身份证反面！')
        return
    }*/
    if($(".lph").css("display") != "none"){
        if(proBlue==''){
            alert('请正确输入楼栋门牌信息！')
            return
        }
    }

    /*if(prounit==''){
        alert('请正确输入单元信息！')
        return
    }
    if(profloor==''){
        alert('请正确输入楼层信息！')
        return
    }
    if(prodoor==''){
        alert('请正确输入门牌号！')
        return
    }*/


    if(imageType=='k1'){
        conimgt=0
        if(proCertificate=='' && $(".proCertificate").attr('disabled')!='disabled'){
            alert('请正确输入房产证号！')
            return
        }
        if($(".cardz").attr('dat')==undefined || $(".cardz").attr('dat')==''){
            alert('请上传房本原件首页！')
            return
        }
        if($(".cardf").attr('dat')==undefined || $(".cardf").attr('dat')==''){
            alert('请上传房本原件信息页！')
            return
        }
    }
    /*if(imageType=='k2'){
        conimgt=1
        if($(".cardz2").attr('dat')==undefined || $(".cardz2").attr('dat')==''){
            alert('请上传房本原件首页(老房本)！')
            return
        }
        if($(".cardf2").attr('dat')==undefined || $(".cardf2").attr('dat')==''){
            alert('请上传房本原件信息页(老房本)！')
            return
        }
    }*/
    if(imageType=='k3'){
        conimgt=2
        if($(".vcont").attr('dat')=='' && $(".vcont2").attr('dat')==''){
            alert('请上传商品房购买备案合同！')
            return
        }
    }
    if($(".agre").hasClass('fv')==false){
        alert('请仔细阅读并同意《房本认证隐私条款》')
        return
    }

    var imgAry=[]
    if(imageType=='k1'){
        imgAry=[
            {'picType':0,'picName':'正面','url':proFront},
            {'picType':5,'picName':'背面','url':proBack},
            {'picType':10,'picName':'房产证首页','url':proCardFront},
            {'picType':15,'picName':'房产证信息页','url':proCardBack},
            {'picType':20,'picName':'补充说明','url':bcsmImg1},

        ]
    }
    if(imageType=='k2'){
        imgAry=[
            {'picType':0,'picName':'正面','url':proFront},
            {'picType':5,'picName':'背面','url':proBack},
            {'picType':10,'picName':'房产证首页','url':proCardFront2},
            {'picType':15,'picName':'房产证信息页','url':proCardBack2},
        ]
    }
    if(imageType=='k3'){
        if(contractImg==''){
            imgAry=[
                {'picType':0,'picName':'正面','url':proFront},
                {'picType':5,'picName':'背面','url':proBack},
                {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg2},
                {'picType':20,'picName':'补充说明','url':bcsmImg1},
            ]
        }
        if(contractImg2==''){
            imgAry=[
                {'picType':0,'picName':'正面','url':proFront},
                {'picType':5,'picName':'背面','url':proBack},
                {'picType':10,'picName':'商品房买卖合同首页','url':contractImg},
                {'picType':20,'picName':'补充说明','url':bcsmImg1},
            ]
        }
        if(contractImg!='' && contractImg2!=''){
            imgAry=[
                {'picType':0,'picName':'正面','url':proFront},
                {'picType':5,'picName':'背面','url':proBack},
                {'picType':10,'picName':'商品房买卖合同首页','url':contractImg},
                {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg2},
                {'picType':20,'picName':'补充说明','url':bcsmImg1},
            ]
        }
    }
    console.log(imgAry)
    console.log(conimgt,idents,prosupplement,saleHouseId,proBlue,proarName)
    $.ajax({
        type: "POST",
        async: false,
        url: "/publishRealEstate",
        data:{
            "certificateType":conimgt,//权属证件类型 0房本原件，1无房产证号，2商品房买卖合同
            "houseType":1,
            "id": '',//房源id
            "identity": idents,//身份 0产权人 1帮他人
            "supplement": prosupplement,//补充说明
            "saleHouseId": saleHouseId,//房源id
            "blueLabel": proBlue,//楼栋蓝牌号
            "subName": proarName,//小区名称
            "pics": JSON.stringify(imgAry),//图片


         /*   "homeOwnerName": proname,//产权人姓名
            "homeOwnerID": procard,//产权人身份证号
            // "relationship": saleHouseId,//与产权人关系
            // "houseCertificate": proCertificate,//房产证号
            // "unit": prounit,//单元
            // "floor": profloor,//楼层
            // "door": prodoor,//门牌号

            // "rName": proname,//当前用户姓名
            // "IDCard": procard,//当前用户身份证*/

        },
        dataType : 'json',
        headers : {
            'Content-Type' : 'application/x-www-form-urlencoded'
        },
        success: function (data) {
            console.log(data)
            if(data.status==1){
                if(hasIndentity == 1){//已实名账号发布成功
                    $(".successMsgTc1,.successMsgFull").show()
                }else{// 未实名账户发布成功
                    $(".successMsgTc2,.successMsgFull").show()
                }


            } else {
                alert(data.msg)
            }
        }
    });
})


//提交修改真房源  帮他人卖房
$(".helpBtn").click(function(){
    /*var helpmyname=$(".helpmyname").val()//我的姓名
    var helpmycard=$(".helpmycard").val()//我的身份证号*/
    var helprelation=$(".helprelation").attr('id')//与产权人关系
    // var helpname=$(".helpname").val()//产权人姓名
    // var helpcard=$(".helpcard").val()//产权人身份证
    var helparea=$('.helparea').val()//小区名称
    var helpBlue=$('.helpBlue').val()//楼栋蓝牌号
    var helpunit=$('.helpunit').val()//单元
    var helpfloor=$('.helpfloor').val()//楼层
    var helpdoor=$('.helpdoor').val()//门牌号
    var helpCertificate=$('.helpCertificate').val()//房产证号
    var helpsupplement=$('.helpsupplement').val()//补充说明

    // var helpImgcz=$(".imghcz").attr('dat')//身份证
    // var helpImgcf=$(".imghcf").attr('dat')
    var helpImghz=$(".imghz").attr('dat')//房证
    var helpImghf=$(".imghf").attr('dat')
    var helpImghz3=$(".cardz3").attr('dat')//老房证
    var helpImghf3=$(".cardf3").attr('dat')

    var contractImg6=$(".vcont4").attr('dat')//商品房买卖合同
    var contractImg7=$(".vcont5").attr('dat')//商品房买卖合同
    var imageType6=$(".acth2").attr('dt')//权属证件类型
    var conimgt6=''//0-房本原件，1-无房产证号（老房本），2-商品房买卖合同

    var helpname = $("#id_Name").val()
    var helpcard = $("#id_Num").val()
    var helpImgcf = $("#id_img_1").val()
    var helpImgcz = $("#id_img_2").val()
    var bcsmImg2 = $(".imgbcrz2").attr('dat')
    if(bcsmImg2 == undefined){
        bcsmImg2 = ""
    }

    // 是否验真通过
    // if($(".ownerPhone").val().length>0 && $(".isVerify").val() =='false'){
    //     alert("请完善产权人验真");
    //     return
    // }
    var pVerify = $(".isVerify").val();    // 验真状态
    var pName = $(".ownerName").val();    // 产权人名
    var pPhone = $(".ownerPhone").val();   // 产权人电话
    var pcode1 = $(".code1").val();
    var pcode2 = $(".code2").val();
    var pcode3 = $(".code3").val();
    var pcode4 = $(".code4").val();
    var pcode5 = $(".code5").val();
    var pcode6 = $(".code6").val();
  // 如果输入了姓名或电话 或验证码  且 验真状态未通过
    /*if((pName.length>0 || pPhone.length>0 || pcode1.length>0 || pcode2.length>0 || pcode3.length>0 || pcode4.length>0 || pcode5.length>0 || pcode6.length>0  ) && pVerify =='false'){
        alert("请完善产权人验真");
        return
    }*/
    // console.log('电话6',phoneNum)
    // if(pPhone == phoneNum){
    //     alert("禁止使用本人手机号");
    //     return
    // }



    //表单验证
    var regName =/^[\u4e00-\u9fa5]{2,4}$/;
    /*if(!regName.test(helpname)){
        if($(".helpmyname").attr('disabled')!='disabled'){
            alert('请正确输入姓名！');
            return false;
        }
    }*/
    var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;

    /*if(reg.test(helpmycard) === false)
    {
        if($(".helpmycard").attr('disabled')!='disabled'){
            alert("请正确输入身份证号！");
            return
        }
    }*/

    if(helprelation=='00'){
        alert("请选择与产权人关系！");
        return
    }

    /* if(!regName.test(helpname)){
        alert('请正确输入产权人姓名！');
        return false;
    }*/
    /* if(reg.test(helpcard) === false)
    {
        alert("请正确输入产权人身份证号！");
        return
    }*/
    /*if($(".imghcz").attr('dat')==undefined || $(".imghcz").attr('dat')==''){
        alert('请上传产权人身份证正面！')
        return
    }
    if($(".imghcf").attr('dat')==undefined || $(".imghcf").attr('dat')==''){
        alert('请上传产权人身份证反面！')
        return
    }*/

    if($(".lph").css("display") != "none"){
        if(helpBlue==''){
            alert('请正确输入楼栋门牌信息!')
            return
        }
    }
    /*if(helpunit==''){
        alert('请正确输入单元信息!')
        return
    }
    if(helpfloor==''){
        alert('请正确输入楼层信息！')
        return
    }
    if(helpdoor==''){
        alert('请正确输入门牌号！')
        return
    }
*/

    if(imageType6=='h1'){
        conimgt6=0
        if(helpCertificate=='' && $(".helpCertificate").attr('disabled')!='disabled'){
            alert('请正确输入房产证号！')
            return
        }
        if($(".imghz").attr('dat')==undefined || $(".imghz").attr('dat')==''){
            alert('请上传房本原件首页！')
            return
        }
        if($(".imghf").attr('dat')==undefined || $(".imghf").attr('dat')==''){
            alert('请上传房本原件信息页！')
            return
        }
    }
    /*if(imageType6=='h2'){
        conimgt6=1
        if($(".cardz3").attr('dat')==undefined || $(".cardz3").attr('dat')==''){
            alert('请上传房本原件首页(老房本)！')
            return
        }
        if($(".cardf3").attr('dat')==undefined || $(".cardf3").attr('dat')==''){
            alert('请上传房本原件信息页(老房本)！')
            return
        }
    }*/
    if(imageType6=='h3'){
        conimgt6=2
        if($(".vcont4").attr('dat')=='' && $(".vcont4").attr('dat')==''){
            alert('请上传商品房购买备案合同！')
            return
        }
    }

    if($(".agre2").hasClass('fv')==false){
        alert('请仔细阅读并同意《房本认证隐私条款》')
        return
    }

    var helpImgAry=[]
    if(imageType6=='h1'){
        helpImgAry=[
            {'picType':0,'picName':'正面','url':helpImgcz},
            {'picType':5,'picName':'背面','url':helpImgcf},
            {'picType':10,'picName':'房产证首页','url':helpImghz},
            {'picType':15,'picName':'房产证信息页','url':helpImghf},
            {'picType':20,'picName':'补充说明','url':bcsmImg2},
        ]
    }
    if(imageType6=='h2'){
        helpImgAry=[
            {'picType':0,'picName':'正面','url':helpImgcz},
            {'picType':5,'picName':'背面','url':helpImgcf},
            {'picType':10,'picName':'房产证首页','url':helpImghz3},
            {'picType':15,'picName':'房产证信息页','url':helpImghf3},
            {'picType':20,'picName':'补充说明','url':bcsmImg2},
        ]
    }
    if(imageType6=='h3'){
        if(contractImg6==''){
            helpImgAry=[
                {'picType':0,'picName':'正面','url':helpImgcz},
                {'picType':5,'picName':'背面','url':helpImgcf},
                {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg7},
                {'picType':20,'picName':'补充说明','url':bcsmImg2},
            ]
        }
        if(contractImg7==''){
            helpImgAry=[
                {'picType':0,'picName':'正面','url':helpImgcz},
                {'picType':5,'picName':'背面','url':helpImgcf},
                {'picType':10,'picName':'商品房买卖合同首页','url':contractImg6},
                {'picType':20,'picName':'补充说明','url':bcsmImg2},
            ]
        }
        if(contractImg6!='' && contractImg7!=''){
            helpImgAry=[
                {'picType':0,'picName':'正面','url':helpImgcz},
                {'picType':5,'picName':'背面','url':helpImgcf},
                {'picType':10,'picName':'商品房买卖合同首页','url':contractImg6},
                {'picType':15,'picName':'商品房买卖合同信息页','url':contractImg7},
                {'picType':20,'picName':'补充说明','url':bcsmImg2},
            ]
        }
    }

    $.ajax({
        type: "POST",
        async: false,
        url: "/publishRealEstate",
        data:{
            "certificateType":conimgt6,//权属证件类型 0房本原件，1无房产证号，2商品房买卖合同
            "houseType":1,
            "id": '',//房源id
            "identity": idents,//身份 0产权人 1帮他人
            "homeOwnerName": helpname,//产权人姓名
            "homeOwnerID": helpcard,//产权人身份证号
            "relationship": helprelation,//与产权人关系
            "houseOwnerName": pName, //房主验真人姓名
            "houseOwnerTel": pPhone, //房主验真电话
            "supplement": helpsupplement,//补充说明
            "saleHouseId": saleHouseId,//房源id
            "houseCertificate": helpCertificate,//房产证号
            // "unit": helpunit,//单元
            // "floor": helpfloor,//楼层
            // "door": helpdoor,//门牌号
            "blueLabel": helpBlue,//楼栋蓝牌号
            // "rName": helpmyname,//当前用户姓名
            // "IDCard": helpmycard,//当前用户身份证
            "subName": helparea,//小区名称
            "pics": JSON.stringify(helpImgAry),//图片


        },
        dataType : 'json',
        headers : {
            'Content-Type' : 'application/x-www-form-urlencoded'
        },
        success: function (data) {
            console.log(data)
            if(data.status==1){
                $(".successMsgTc,.successMsgFull").show()
            } else {
                alert(data.msg)
            }
        }
    });
})

var fileItems = {};
//产权人-上传身份证正面
clickUpload('.uploadz','#uploadFront','.imgsz','https://static.fangxiaoer.com/web/images/sy/check/img_id_positive.png')
//产权人-上传身份证背面
clickUpload('.uploadf','#uploadBack','.imgsf','https://static.fangxiaoer.com/web/images/sy/check/img_id_back.png')
//产权人-上传房本首页 \上传房本信息页
clickUpload('.uploadCard','#uploadInc','.cardz','https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png')
clickUpload('.uploadCardf','#uploadInf','.cardf','https://static.fangxiaoer.com/web/images/sy/trustHouse/7.png')
//老房本-产权人-上传房本首页 \上传房本信息页
clickUpload('.uploadCard2','#uploadInc2','.cardz2','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
clickUpload('.uploadCardf2','#uploadInf2','.cardf2','https://static.fangxiaoer.com/web/images/sy/check/icon_photo.png')
//产权人-上传备案合同
clickUpload('.uploadContract','#uploadInt','.vcont','https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png')
clickUpload('.uploadContract2','#uploadInt2','.vcont2','https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png')


//帮他人卖-上传身份证正面
clickUpload('.helpCardz','#helpupz','.imghcz','https://static.fangxiaoer.com/web/images/sy/check/img_id_positive.png')
//帮他人卖-上传身份证背面
clickUpload('.helpCardf','#helpupf','.imghcf','https://static.fangxiaoer.com/web/images/sy/check/img_id_back.png')
//帮他人卖-上传房本首页 上传房本信息页
clickUpload('.helpHouz','#helphz','.imghz','https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png')
clickUpload('.helpHouf','#helphf','.imghf','https://static.fangxiaoer.com/web/images/sy/trustHouse/7.png')
//老房本 -上传房本首页 上传房本信息页
clickUpload('.uploadCard3','#uploadInc3','.cardz3','https://static.fangxiaoer.com/web/images/sy/trustHouse/6.png')
clickUpload('.uploadCardf3','#uploadInf3','.cardf3','https://static.fangxiaoer.com/web/images/sy/trustHouse/7png')
//帮他人-上传备案合同
clickUpload('.uploadContract4','#uploadInt4','.vcont4','https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png')
clickUpload('.uploadContract5','#uploadInt5','.vcont5','https://static.fangxiaoer.com/web/images/sy/trustHouse/24.png')

// 补充认证
clickUpload('.uploadbcrz1','#uploadBCRZ1','.imgbcrz1','https://static.fangxiaoer.com/web/images/sy/trustHouse/8.png')
clickUpload('.uploadbcrz2','#uploadBCRZ2','.imgbcrz2','https://static.fangxiaoer.com/web/images/sy/trustHouse/8.png')




//点击上传
function clickUpload(clk,pid,dimg,imgu){
    $(clk).on('click', function () {
        $(pid).val('');
        $(pid).click();
    })
    uploadImg(pid,dimg,clk)
    $(clk).find('.vclose').click(function(event){
        event.stopPropagation();
        fileItems = {}
        $(dimg).attr('src',imgu)
        $(this).hide()
        $(dimg).attr('dat','')
    })
}

//上传图片
function uploadImg(uploadInp,domImg,click) {
    $(uploadInp).on('change', function (event) {
        var windowUrl = window.URL || window.webkitURL;
        var files = event.target.files;
        for (var i = 0; i < files.length; i++) {
            var item = files[i];
            var picUrl = windowUrl.createObjectURL(item);
            fileItems = {uri: picUrl, file: item};
        }
        $(uploadInp).val('');
        var formData = new FormData();
        formData.append("file", fileItems.file);
        formData.append("photoType", 'idCardPics');
        $.ajax({
            url: "https://ltapi.fangxiaoer.com/apiv1/base/newuploadpic",
            type: "post",
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'text',
            success: function (data) {
                if (data.status == 0) {
                    alert("图片上传失败")
                    return;
                }
                var params = JSON.parse(data)
                var imgurl = 'https://images1.fangxiaoer.com/' + params.content + '/big_fxr_watermark'
                console.log(imgurl)
                $(domImg).attr('src', imgurl)
                $(domImg).attr('dat', params.content)
                $(click).find('.vclose').show()
            },
            error: function (data) {}
        });
    })
}


// 产权人切换
$(".vsp").click(function(){
    $(this).siblings().removeClass('kon')
    $(this).addClass('kon')
    if($(this).attr('i')=='m1'){
        $(".vtm1").show()
        $(".vtm2").hide()
        idents=0//产权人
    }else{
        $(".vtm1").hide()
        $(".vtm2").show()
        idents=1//帮他人
    }
})
//关闭弹窗
$(".closeSml").click(function(){
    location.href='/new_secondPublish'
})
//房产证号弹窗
$(".fi").click(function(){
    $(".seek,.successMsgFull").show()
})
$(".close_sk").click(function(){
    $(".seek,.successMsgFull").hide()
})
//无房证号
$(".noho").toggle(function(){
    $(this).find('em').addClass('fv')
    $(this).siblings('input').attr('disabled',"disabled").val('')
},function(){
    $(this).find('em').removeClass('fv')
    $(this).siblings('input').removeAttr('disabled').val('')
})
$(".helpNoho").toggle(function(){
    $(this).find('em').addClass('fv')
    $(this).siblings('input').attr('disabled',"disabled").val('')
},function(){
    $(this).find('em').removeClass('fv')
    $(this).siblings('input').removeAttr('disabled').val('')
})
//隐私条款按钮
$(".agre").toggle(function(){
    $(this).addClass('fv')
},function(){
    $(this).removeClass('fv')
})
$(".agre2").toggle(function(){
    $(this).addClass('fv')
},function(){
    $(this).removeClass('fv')
})

//下拉框
$(function(){
    $(".navht p").click(function(){
        var ul=$(".newkk");
        if(ul.css("display")=="none"){
            ul.slideDown();
        }else{
            ul.slideUp();
        }
    });
    //点击其他元素
    $(document).bind("click", function(e) {
        var target = $(e.target);
        if (target.closest(".navht p").length == 0) {
            $(".newkk").slideUp()
        }
    })
    $(".set").click(function(){
        var _name = $(this).attr("name");
        if( $("[name="+_name+"]").length > 1 ){
            $("[name="+_name+"]").removeClass("select");
            $(this).addClass("select");
        } else {
            if( $(this).hasClass("select") ){
                $(this).removeClass("select");
            } else {
                $(this).addClass("select");
            }
        }
    });
    $(".navht li").click(function(){
        var li=$(this).text();
        var sid=$(this).attr('id')
        $(".navht p").html(li);
        $(".navht p").attr('id',sid)
        $(".newkk").hide();
        /*$(".set").css({background:'none'});*/
        $("p").removeClass("select") ;
    });
})

//获取参数
function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}
</script>

</body>
</html>