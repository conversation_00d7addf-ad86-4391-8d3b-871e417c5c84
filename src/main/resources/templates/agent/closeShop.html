<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>

    <title >房小二网</title>
    <meta name="keywords" content="沈阳二手房,沈阳二手房网,沈阳二手房出售,沈阳二手房买卖,沈阳房产经纪人,沈阳二手房经纪人"/>
    <meta name="description" content="房小二网沈阳二手房为您提供海量真实的沈阳二手房房源信息，沈阳二手房经纪人信息，及时的二手房出租出售信息，帮您定位，搜索各类出售房源，带来更好的二手房买卖体验。"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css?v=20200509" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>



    <style>


        .shop-box{
            width: 100%;
            height: 60vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .no-img{
            width: 122px;
        }
        .shop-text{
            font-size: 16px;
            font-family: PingFang SC-Bold, PingFang SC;

            color: #666666;
            margin-top: 20px;
        }
    </style>


</head>
<body>
<!--页头-->

<div id="head2017" th:include = "fragment/fragment::firstNav"  th:with="firstNavIndex=3"></div>
<div th:include = "fragment/fragment::closeShopCard"></div>

<div class="shop-box">
    <img class="no-img" src="https://static.fangxiaoer.com/web/images/sy/house/close-shop.png">
    <div class="shop-text">该店铺已关闭</div>
</div>
<div th:include="fragment/fragment:: footer_detail" ></div>
</body>
</html>