<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="'沈阳'+ ${seoTitle} +'优秀经纪人_沈阳'+${seoTitle}+'房产经纪人_优秀经纪人风云榜—房小二网'"></title>
    <meta name="keywords" th:content="'房小二,房小二网,沈阳'+${seoTitle}+'优秀经纪人,沈阳'+${seoTitle}+'房产经纪人,沈阳优秀房产经纪人,沈阳经纪人,房产经纪人风云榜,优秀房产经纪人风云榜'" />
    <meta name="description" th:content="'房小二网沈阳'+${seoTitle}+'优秀经纪人风云榜,根据沈阳房产经纪人的综合服务,房产经纪人带看能力,用户评价等多个维度评选出沈阳市优秀房产经纪人.房小二网,让买房更简单!'" />
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20190806" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="/css/memberTopList.css">
</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<div >
    <div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/saleHouses/">沈阳二手房</a> &gt; <a
            href="/fengyun">经纪人风云榜</a></div>

    <div class="main1">
        <div class="row1">
            <img src="https://static.fangxiaoer.com/web/images/sy/prominent/yun.png">
        </div>
    </div>

    <div class="main2">
        <div class="row2">
            <!-- tab选项卡 -->
            <!--<div class="tab" >-->
                <!--<div th:each="r, stat : ${region}" th:class="${r.selected}? 'current':''" th:data-id="${r.id}"  th:data-url="${r.url}" th:text="${stat.index==0?'不限':r.name}" th:id="'r'+${r.id}" >-->
                <!--</div>-->
            <!--</div>-->
            <input type="hidden" id="regionId" name="regionId" th:value="${regionId}">
            <!--   内容区域 -->
            <div class="p-content">
                <div class="p-box" th:each="member,state : ${memberTopList}" th:if="${ state.count < 10}">
                    <a th:href="${#strings.isEmpty(member.memberId) ? '' :'/agent/second/'+member.memberId}">
                        <div class="box-card">
                            <div class="card-box" >
                                <!-- 左边图片盒子 -->
                                <div class="image-box">
                                    <img  class="card-image" th:src="${#strings.isEmpty(member.avatarUrl) ? 'https://images1.fangxiaoer.com/sy/qt/big/2016/11/03/men.png' : member.avatarUrl}" />
                                </div>
                                <!-- 右边详情盒子 -->
                                <div class="box-right">
                                    <!-- 第一行 -->
                                    <div>
                                        <span class="p-title" th:text="${member.RealName}">谭艳艳</span>
                                        <span class="f-title">
                                        <span class="num" th:text="${member.allCount}">32</span>
                                        套房源在售
                                    </span>
                                    </div>
                                    <!-- 第二行 -->
                                    <div class="dian"> <span class="d-title" th:text="${#strings.isEmpty(member.companyName) ? member.intermediaryName : member.companyName}">鑫乐居华人店</span></div>
                                    <!-- 第三行 -->
                                    <div class="p-area">
                                        <div>
                                            <p>主营区域： </p>
                                        </div>
                                        <div class="area-right">
                                            <p th:each="regionPlate : ${member.regionPlateTitles}" th:text="${regionPlate}">于洪区-丁香湖</p>
                                            <!--<p class="p2">于洪区-丁香湖</p>-->
                                            <!--<p class="p3"> 于洪区-丁香湖</p>-->
                                        </div>
                                    </div>
                                    <!-- 最后一行 -->
                                    <div class="p-contact">
                                        <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal">
                                            <div class="chat">
                                                <img src="https://static.fangxiaoer.com/web/images/sy/prominent/icon-chat.png" class="chat-img" />
                                                <p class="chat-text">在线聊呗</p>
                                            </div>
                                        </a>
                                        <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/imRecruit/chatUp-'+member.Mobile}" target="_blank">
                                            <div class="chat">
                                                <img src="https://static.fangxiaoer.com/web/images/sy/prominent/icon-chat.png" class="chat-img" />
                                                <p class="chat-text">在线聊呗</p>
                                            </div>
                                        </a>
                                        <div class="phone">
                                            <input type="hidden" id="memberId" th:value="${member.memberId}">
                                            <input type="hidden" id="mobile" th:value="${member.Mobile}">
                                            <div>
                                                <!--<p class="tel" th:text="${agentInfo.sortTel}">15754631427</p>-->
                                                <!--微信扫码拨号-->
                                                <div class="agentshop-cardTels">
                                                    <s></s>
                                                    <div class="telBtn" th:data-id="${member.memberId}">电话咨询
                                                        <div class="show-topCardTels">
                                                            <img id="forcodex" src="" alt="">
                                                            <p>微信扫码拨号</p>
                                                            <p><i>快速获取经纪人联系方式</i></p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--<img src="http://static.fangxiaoer.com/web/images/sy/prominent/icon-Tel.png" class="phone-img" />-->
                                                <!--<p class="phone-text">电话咨询</p>-->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>

                <th:block th:if="${memberTopList}">
                    <div class="all" id="showAll" th:if="${#lists.size(memberTopList) > 9}">
                        <p >查看更多经纪人</p>
                    </div>
                    <div  class="noAll" id="noAll" >
                        <p >暂无更多经纪人</p>
                    </div>
                </th:block>
            </div>
        </div>
    </div>



</div>
<div th:include="house/detail/fragment_login::login"></div>
<div th:include="fragment/fragment::tongji"></div>
<!--底部1-->
<div th:include="fragment/fragment:: footer_list"></div>
<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
<script th:inline="javascript">
    var flag = true;
    var page = 2;
    var sessionId = [[${session.muser}]];
    $("#showAll").click(function(){
        // $("#showAll").hide();
        // $(".allMember").show();
        var regionID = $("#regionId").val();
        console.log(regionID)
        if(flag){
            flag = false;
            $.ajax({
                type: "POST",
                async: false,
                url:  "/memberTopListOnload",
                data:{
                    "regionID" : regionID,
                    "page" : page,
                    "pageSize":9
                },
                dataType : 'json',
                success: function (data) {

                    var memberTopArr = data.content;

                    console.log(memberTopArr)   // 获取到数据
                    if(memberTopArr != null && memberTopArr.length > 0){
                        for(var i = 0;i<memberTopArr.length;i++){
                            var member = memberTopArr[i];
                            var avatarUrl = (member.avatarUrl == null || member.avatarUrl == "") ? "https://images1.fangxiaoer.com/sy/qt/big/2016/11/03/men.png" : member.avatarUrl;
                            var companyName = (member.companyName == null || member.companyName == "") ? member.intermediaryName : member.companyName
                            var htmlText = `
                   <div class="p-box">
                    <a  href="${'/agent/second/'+member.memberId}">
                        <div class="box-card">
                        <div class="card-box" >
                            <div class="image-box">
                                <img  class="card-image"    src="${avatarUrl}" />
                            </div>
                            <div class="box-right">
                                <div>
                                    <span class="p-title" >${member.RealName}</span>
                                    <span class="f-title">
                                        <span class="num" >${member.allCount}</span>
                                        套房源在售
                                    </span>
                                </div>
                                <!-- 第二行 -->
                                <div class="dian"> <span class="d-title" >${companyName}</span></div>
                                <!-- 第三行  这里得遍历-->
                                <div class="p-area">
                                    <div>
                                        <p>主营区域： </p>
                                    </div>
                                    <div class="area-right">
                                        <p >${member.regionPlateTitles[0] == null || member.regionPlateTitles[0] == "" ? "" : member.regionPlateTitles[0]}</p>
 <p >${member.regionPlateTitles[1] == null || member.regionPlateTitles[1] == "" ? "" : member.regionPlateTitles[1]}</p>
  <p >${member.regionPlateTitles[2] == null || member.regionPlateTitles[2] == "" ? "" : member.regionPlateTitles[2]}</p>
                                    </div>
                                </div>
                                <!-- 最后一行 -->
                                <div class="p-contact">`
if(sessionId == null){
htmlText += `
                                        <a href="#login" target="_blank" data-toggle="modal">
                                            <div class="chat">
                                                <img src="https://static.fangxiaoer.com/web/images/sy/prominent/icon-chat.png" class="chat-img" />
                                                <p class="chat-text">在线聊呗</p>
                                            </div>
                                        </a>
`
}else{
                                htmlText += `<a href="${'/imRecruit/chatUp-'+member.Mobile}" target="_blank">
                                    <div class="chat">
                                    <img src="https://static.fangxiaoer.com/web/images/sy/prominent/icon-chat.png" class="chat-img" />
                                    <p class="chat-text">在线聊呗</p>
                                    </div>
                                    </a>`
}
htmlText += `
                                    <div class="phone">
                                        <input type="hidden" id="memberId" >
                                        <input type="hidden" id="mobile" >
                                        <div>
                                            <!--微信扫码拨号-->
                                            <div class="agentshop-cardTels">
                                                <s></s>
                                                <div class="telBtn" data-id="${member.memberId}">电话咨询
                                                    <div class="show-topCardTels">
                                                        <img id="forcodex" src="" alt="">
                                                        <p>微信扫码拨号</p>
                                                        <p><i>快速获取经纪人联系方式</i></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </a>
                </div> `
                            // 小于9条说明没有下一页
                            if(memberTopArr.length < 9){
                                $("#showAll").hide();
                                $("#noAll").show();
                            }
                            $("div[class=p-box]:last").after(htmlText);

                        }
                        page += 1;
                        setTimeout(function(){
                            flag = true;
                        }, 1000);
                    }else {

                        $("#showAll").hide();
                        $("#noAll").show();

                    }
                }
            })

        }

    })


    $(function(){
        // var page = 1;

    })
    $(".telBtn").live("mouseover",(function(){
        // 一旦生成了二维码，就不再次请求，弊端：如果二维码生成失败，就会一直请求，届时需要连同样式一起换成点击事件
        if($(this).find("#forcodex").attr("src")){
            return;
        };
        var agengcyId = $(this).data('id')+"";
        var img = "";
        var sss;
        $.ajax({
            type: "GET",
            async: false,
            url:  "/getWxACodeByAgentId",
            data:{"agentId": agengcyId},
            dataType : 'json',
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            success: function (data) {
                console.log(data)
                img = data.img;
                sss = data;
            }
        });
        $(this).find("#forcodex").attr("src","data:text/html;base64,"+img);
    }))
    // 利用div跳转
    $(".tab div").click(function () {
        var url = $(this).data('url');
        console.log(url);
        location.href = url;
    })
</script>
</body>
</html>