<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺_'+
     agentInfo.intermediaryName +'_'+ '房小二网经纪人'
     + agentInfo.username
     +
     ',沈阳经纪人个人信息 - 房小二网品质保证，买房卖房，就上房小二网。'}">田经理的店铺，沈阳写字楼经纪人房源信息 - 房小二网</title>
    <meta name="keywords" th:content="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺_'+
     agentInfo.intermediaryName +'_'+ '房小二网经纪人'
     + agentInfo.username
     +
     ',沈阳经纪人个人信息 - 房小二网品质保证，买房卖房，就上房小二网。'}"/>
    <meta name="description" th:content="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺_'+
     agentInfo.intermediaryName +'_'+ '房小二网经纪人'
     + agentInfo.username
     +
     ',沈阳经纪人个人信息 - 房小二网品质保证，买房卖房，就上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/jjrsf/'+agentId+'-3.htm'}">
    <!--<link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
</head>
<style>
    .license{position: absolute;width: 260px;height: 370px;border: 1px solid #ccc;display: none;
        border-radius: 5px;z-index: 99;background-color: #FFF;bottom: 40px;left: 30px;padding: 5px;}
    .license_img{
        width: 100% !important;
        height: 100% !important;
        margin: 0px !important;
        border-radius: 0px !important;
    }
    .warnings {
        margin: 20px 0;
        padding: 20px 0 20px 0;
        background: #fffbf6;
        border: 1px solid #f5dcbc;
    }
    .warnings p {
        background: url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left;
        font-size: 14px;
        line-height: 24px;
        font-weight: bold;
        color: #333;
        width: 420px;
        margin: auto;

    }
</style>
<body>
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"></div>
<!--搜索-->
<!--<div id="search2017"  th:include = "fragment/fragment::searchNav" th:with="type=4"></div>-->
<input type="hidden" id="agencyId" th:value="${agencyId+'-4'}" >
<div class="main">
    <div th:include = "fragment/fragment::agentShopCard"></div>
    <div th:if="${!#strings.isEmpty(agentInfo.ShopStatus)  and  agentInfo.ShopStatus eq '0'}" class="warning warnings">
        <p>
            很抱歉，该经纪人店铺已经关闭。<br>
            <a th:href="@{/findAgent}"> 查找更多经纪人>></a>
        </p>
    </div>
    <div class="content" th:if="${#strings.isEmpty(agentInfo.ShopStatus)  or  agentInfo.ShopStatus eq '1'}">
        <div th:include = "fragment/fragment::agentShopsHead" th:with="agentHouseTpye=6"></div>
        <div class="agentInfoMain" style="float: left;position: relative;overflow: inherit;">
            <ul>
                <li>
                    <h4>信用等级<i></i></h4>
                </li>
                <li>
                    <span>信用等级：</span>
                    <p>
                        <s th:class="${'icon-xy level-icon'+agentInfo.level}">
                        <th:block th:text="${'LV'+agentInfo.stars}"></th:block>
                        </s>
                        <th:block th:if="${#strings.toString('agentInfo.ranking') eq '0%'}">
                            打败了同城 <b><th:block th:text="${agentInfo.ranking}"></th:block></b> 的经纪人
                        </th:block>
                    </p>
                </li>
                <li>
                    <h4>个人资料<i></i></h4>
                </li>
                <li th:if="${!#strings.isEmpty(agentInfo.declaration)}">
                    <span>服务宣言：</span>
                    <p th:text="${#strings.toString(agentInfo.declaration)}"></p>
                </li>
                <li th:if="${#strings.isEmpty(agentInfo.declaration)}">
                    <span>服务宣言：</span>
                    <p th:text="${'以诚感人者 人亦诚而应 愿以我全力以赴 换来您乐享生活'}"></p>
                </li>
                <li th:if="${!#strings.isEmpty(agentInfo.personalIntroduction)}">
                    <span>个人简介：</span>
                    <p th:text="${agentInfo.personalIntroduction}"></p>
                </li>
                <li th:if="${!#strings.isEmpty(agentInfo.sortTel)}">
                    <span>咨询电话：</span>
                    <p th:text="${#strings.toString(agentInfo.sortTel)}"></p>
                </li>
                <li th:if="${!#strings.isEmpty(agentInfo.QRCode)}">
                    <span>微信二维码：</span>
                    <img th:src="${agentInfo.QRCode}">
                </li>
                <li th:if="${!#strings.isEmpty(agentInfo.agentCard) or #strings.toString(agentInfo.EmailState) eq '1'}">
                    <span>认证记录：</span>
                    <div style="margin-top: 10px;margin-left: 20px" th:if="${!#strings.isEmpty(agentInfo.agentCard)}" class="agentInfoRz"><img src="https://static.fangxiaoer.com/web/images/ico/agentInfo_03.jpg" alt=""></div>
                       <div style="margin-top: 10px" th:if="${#strings.toString(agentInfo.EmailState) eq '1'}" class="agentInfoRz"><img src="https://static.fangxiaoer.com/web/images/ico/agentInfo_05.jpg" alt=""></div>
                </li>
                <li>
                    <h4>服务信息<i></i></h4>
                </li>
                <li th:if="${!#strings.isEmpty(agentInfo.intermediaryName) && #strings.isEmpty(intermeStore?.companyId)}">
                    <span>所属机构：</span>
                    <p th:text="${#strings.toString(agentInfo.intermediaryName)}">喜鹊不动产</p>
                </li>
                <li th:if="${!#strings.isEmpty(intermeStore?.companyId)}">
                    <span>所属机构：</span>
                    <a  th:href="${#strings.isEmpty(intermeStore?.companyId) ? '' :'/agent/intermediary/home/'+intermeStore.companyId}" target="_blank"><p style="color:#ff5200;" th:text="${#strings.toString(agentInfo.intermediaryName)+' >'}"></p></a>
                </li>
                <li th:if="${!#strings.isEmpty(agentInfo.memAge)}">
                    <span>从业年限：</span>
                    <p th:text="${#strings.toString(agentInfo.memAge)}"></p>
                </li>
                <li th:if="${!#lists.isEmpty(mainArea)}">
                    <span>主营区域：</span>
                    <p >
                        <span th:if="${!#strings.isEmpty(plateName1)}" th:text="${plateName1}"></span>
                        <span th:if="${!#strings.isEmpty(plateName2)}" th:text="${'、'+plateName2}"></span>
                        <span th:if="${!#strings.isEmpty(plateName3)}" th:text="${'、'+plateName3}"></span>
                    </p>
                </li>
                <li th:if="${!#lists.isEmpty(mainArea)}">
                    <span>主营小区：</span>
                    <ul>
                        <li th:if="${!#strings.isEmpty(subName1)}">
                            <a th:if="${subId1 ne '0'}" th:href="${'/saleVillages/'+subId1+'/index.htm'}" target="_blank">
                                <th:block th:text="${subName1}"></th:block>
                            </a>
                            <th:block  th:if="${subId1 eq '0'}"
                                       th:text="${subName1}"></th:block>
                        </li>
                        <li th:if="${!#strings.isEmpty(subName2)}">
                            <a th:if="${subId2 ne '0'}" th:href="${'/saleVillages/'+subId2+'/index.htm'}" target="_blank">
                                <th:block th:text="${subName2}"></th:block>
                            </a>
                            <th:block th:if="${subId2 eq '0'}" th:text="${subName2}"></th:block>
                        </li>
                        <li th:if="${!#strings.isEmpty(subName3)}">
                            <a th:if="${subId3 ne '0'}" th:href="${'/saleVillages/'+subId3+'/index.htm'}" target="_blank">
                                <th:block th:text="${subName3}"></th:block>
                            </a>
                            <th:block th:if="${subId3 eq '0'}" th:text="${subName3}"></th:block>
                        </li>
                    </ul>
                </li>
                <li th:unless="${agentInfo.agentBusinessCardForPc eq null or agentInfo.agentBusinessCardForPc eq ''}" style="cursor: pointer;padding-top: 20px;" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                    <span>公司执照编码：</span>
                    <span style="color:#999;">
                        <th:block th:text="${agentInfo.agentBusinessNum}"></th:block>
                    </span>
                </li>
                <div class="license" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                    <img th:src="${agentInfo.agentBusinessCardForPc}" class="license_img">
                </div>
                <div style="clear: both;"></div>
            </ul>

        </div>
        <div id="right" style="width: 250px;float: left;" hidden>
        <div class="zsfw">
            <h1 style="text-align: left"><span></span>求购</h1>
            <ul>
                <li>
                    <span>意向区域</span>
                    <div>
                        <select id="region" >
                            <option  th:each="region,stat:${region}" th:selected="${stat.index eq 0}" th:if="${region.name ne '全部'}" th:text="${region.name}" th:value="${region.name}">沈河区</option>
                        </select>
                    </div>
                </li>
                <li class="hx">
                    <span>意向户型</span>
                    <div>
                        <select id="new_huxing" >
                            <option>一居</option>
                            <option>二居</option>
                            <option>三居</option>
                            <option>四居</option>
                            <option>五居及以上</option>
                        </select>
                    </div>
                </li>
                <li class="yx">
                    <span>预算价格</span>
                    <div>
                        <select id="new_yusuan">
                            <option>35万以下</option>
                            <option>35-50万</option>
                            <option>50-80万</option>
                            <option>80-100万</option>
                            <option>100-120万</option>
                            <option>120-150万</option>
                            <option>150万以上</option>
                        </select>
                    </div>
                </li>
                <li>
                    <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
                </li>
                <li>
                    <span>手机号码</span>
                    <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
                    <input type="hidden" id="type" value="2">
                </li>
                <li>
                    <span>验证码</span>
                    <input type="tel" id="code" class="fxe_messageCode"  maxlength="6" style="width: 120px;" placeholder="请输入验证码"/>
                    <p id="hqyzm" class="fxe_ReSendValidateCoad" >获取验证码</p>
                    <p class="fxe_validateCode"></p>
                </li>
                <b class="btn" id="new_submit">提交</b>
                <li style="color: #999;width:237px;text-align:left;font-size: 12px;">若正确提交即同意接受以电话、短信等方式推荐我司业务相关的信息</li>

            </ul>
        </div>
        </div>
    </div>
</div>
<div class="cl"></div>
<script>
    function showLicense(e) {
        $(".license").show();
    }
    function hideLicense(e) {
        $(".license").hide();
    }
</script>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>
