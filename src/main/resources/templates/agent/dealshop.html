<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺,沈阳商铺经纪人房源信息 - 房小二网'}">田经理的店铺，沈阳租房经纪人房源信息 - 房小二网</title>
    <meta name="keywords" content="沈阳经纪人商铺房源,沈阳商铺,沈阳商铺网,沈阳商铺房源"/>
    <meta name="description" content="房小二网沈阳商铺，帮你快速找到精准客户，每天海量真实用户浏览，助您快速成交。提供全面的沈阳商铺信息，让您轻松找到自己想要的房子，为您带来更好的商铺体验。"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/jjrsf/'+agentId+'-3.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        function qingchu() {
            //$("#zongjia").html("区域");
            //$("#mianji").html("类型");
            //$("#huxing").html("供求");

            //$("#PriceBase").val("0");
            //$("#BuildArea").val("0");
            //$("#roomType").val("0");
            //$("#xiaoquid").val("");
            //$("#hidden0").val("-1");
            //$("#Hidden1").val("-1");
            //$("#Hidden2").val("-1");
//            delCookie("searchKey");
            setTimeout(function () {
                location.href = "/agent/dealShops/"+[[${agencyId}]];
            },500);
        }



        $(function () {
            $("#bottonss").click(function () {
                var dd = $("#hidden0").val();
                var dd1 = $("#Hidden2").val();
                var dd2 = $("#Hidden1").val();
                //模糊搜索字段
                var dds = $("#xiaoquid").val();
                // var sou = "72269";
                if (dd != null && dd != "") {
                    dd = "-r"+dd;
                }
                if (dd1 != null && dd1 != "") {
                    dd1 = "-n"+dd1;
                }
                if (dd2 != null && dd2 != "") {
                    dd2 = "-t"+dd2;
                }
                var search ;
                if (dds == null || dds == undefined || dds == '') {
                    search = ""
                }else {
                    search = "-search="+dds;
                }
                var url = "/agent/dealShops/"+[[${agencyId}]]+"/" + dd + dd1 +  dd2 +search;
//                setCookie("searchKey",dds,10);
                location.href = url;

            });
        });

        $(function () {  //3个下拉列表取url参数赋值
            var hr = window.location.href;
            var str = hr.split("_");
            var zj = str[1];
            getData(zj, "#hidden0", "#zongjia");
            var mj = str[2];
            getData(mj, "#Hidden2", "#mianji");
            var hx = str[3];
            getData(hx, "#Hidden1", "#huxing");
            var xq = str[4];

            if (xq != "" && xq != null) {
                $("#xiaoquid").val(decodeURIComponent(xq)); //decodeURIComponent方法用于 window.location.href中 中文乱码解码
            }

        })

        function getData(name, num, adds) {//下拉列表取url参数赋值用到的方法
            if (name != -1 && name != "" && name != null) {
                $(num).val(name);
                var bb = $(num).parent(".my_xl").find(".my_xl_list li[value=" + name + "]").html();
                $(adds).html(bb);
                var container = $("body"),
                        scrollTo = $('.content');
                container.scrollTop(0);
                container.scrollTop(
                        scrollTo.offset().top - container.offset().top + container.scrollTop()
                );
            }
        }
    </script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/contractList.css"/>
    <style type="text/css">
        .infRight p b {
        font-weight: normal;
        margin-left: 10px;
    }
        .bargainHouseList>li .houseInfo .houseInfoLeft{
            width: 440px;
        }
        .bargainHouseList {
            margin: 0;
        }
        .agent-grjj{width: 220px}
    </style>
</head>
<body onload="init()">
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"  th:with="firstNavIndex=5"></div>
<!--搜索-->
<!--<div id="search2017"  th:include = "fragment/fragment::searchNav" th:with="type=4"></div>-->
<input type="hidden" id="agencyId" th:value="${agencyId+'-3'}" >
<div class="main">
    <div th:include = "fragment/fragment::agentShopCard"></div>
    <div class="content">
        <div th:include = "fragment/fragment::agentShopsHead" th:with="agentHouseTpye=5"></div>
        <div th:include = "fragment/fragment::agentShopsDetailHead" th:with="agentHouseDetailTpye=3"></div>
        <div class="contentMain">
            <div class="contentCtn">
                <div class="my_xl" th:if="${!#lists.isEmpty(region)}">
                    <input name="hidden0" type="hidden" id="hidden0"  class="my_xl_input" th:each="region:${region}" th:if="${region.selected}" th:value="${region.id}"/>
                    <div class="my_xl_txt" id="zongjia"  th:each="region:${region}" th:if="${region.selected}"  th:text="${#strings.isEmpty(region.id) ? '区域' : region.name}">区域</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="region:${region}" th:href="${region.url}" ><li data-key="PriceBase" th:text="${region.name}" th:value="${region.id}">全部</li></a>
                    </ul>
                </div>
                <div class="my_xl" th:if="${!#lists.isEmpty(needs)}">
                    <input name="Hidden2" type="hidden" id="Hidden2" class="my_xl_input" th:each="n:${needs}" th:if="${n.selected}"  th:value="${n.id}" />
                    <div class="my_xl_txt" id="mianji" th:each="n:${needs}" th:if="${n.selected}" th:text="${#strings.isEmpty(n.id)? '供求':n.name}" >供求</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="n:${needs}" th:href="${n.url}"><li data-key="BuildArea"  th:text="${n.name}" th:value="${n.id}">全部</li></a>
                    </ul>
                </div>
                <div class="my_xl"  th:if="${!#lists.isEmpty(type)}">
                    <input name="Hidden1" type="hidden" id="Hidden1" class="my_xl_input"th:each="t:${type}" th:if="${t.selected}" th:value="${t.id}" />
                    <div class="my_xl_txt" id="huxing" th:each="t:${type}" th:if="${t.selected}" th:text="${#strings.isEmpty(t.id)?'类型':t.name}" >类型</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="t:${type}" th:href="${t.url}" > <li data-key="roomType"  th:text="${t.name}" th:value="${t.id}">全部</li></a>
                    </ul>
                </div>
                <div class="houseSearch">
                    <input type="text" id="xiaoquid" class="searchInput" th:value="${searchName}" autocomplete="off" placeholder="请输入关键字" >
                    <input name="button" id="bottonss" type="button" class="searchBtn" value="" >
                </div>
                <p onclick="qingchu()"><i></i>清空筛选条件</p>
                <!--<span>小二为你找到<i th:text="${msg}">34</i>个符合条件的房源</span>-->
            </div>
            <div class="cl"></div>
            <div th:if="${#lists.isEmpty(shop)}" class="warning">
                <p>
                    很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                    <a th:href="@{/shops}" target="_blank"> 查找相似房源>></a>
                </p>
            </div>
            <div>
                <ul class="bargainHouseList inf" th:each="sale:${shop}">
                    <li>
                        <div class="houseImg">
                            <a th:if="${sale.spanTime &lt; 31}" target="_blank"  style="cursor: default;">
                                <img th:src="${#strings.isEmpty(sale.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sale.pic}" th:alt="${sale.titile}">
                            </a>
                            <a th:unless="${sale.spanTime &lt; 31}" th:href="${'/dealShop/'+sale.houseId+'.htm'}" target="_blank">
                                <img th:src="${#strings.isEmpty(sale.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sale.pic}" th:alt="${sale.titile}">
                            </a>
                        </div>
                        <div class="houseInfo">
                            <div class="houseTitle">
                                <a th:if="${sale.spanTime &lt; 31}" target="_blank" style="cursor: default;">
                                    <th:block th:text="${sale.titile}"></th:block>
                                </a>
                                <a th:unless="${sale.spanTime &lt; 31}" th:href="${'/dealShop/'+sale.houseId+'.htm'}" target="_blank">
                                    <th:block th:text="${sale.titile}"></th:block>
                                </a>
                            </div>
                            <div class="houseInfoLeft">
                                <p><th:block th:text="${#strings.isEmpty(sale.regionName)?'':sale.regionName}"></th:block><th:block th:if="${!#strings.isEmpty(sale.regionName)}">-</th:block><th:block th:text="${#strings.isEmpty(sale.plateName)?'':sale.plateName}"></th:block>
                                    <i>
                                        <img src="https://static.fangxiaoer.com/web/images/ico/sign/listIcon_house.jpg" alt="">
                                    </i>
                                </p>
                                <p>类型：<th:block th:text="${#strings.isEmpty(sale.shopCategoriesName)?'暂无资料&nbsp;&nbsp;&nbsp;&nbsp;':sale.shopCategoriesName+'&nbsp;&nbsp;&nbsp;&nbsp;'}"></th:block><span th:if="${!#strings.isEmpty(sale.shopCategoriesName) and !#strings.isEmpty(sale.paymentName)}">|</span ><th:block th:text="${#strings.toString(sale.shopType) eq '1'?'':(#strings.isEmpty(sale.paymentName)?'付款方式：暂无资料':'付款方式：'+sale.paymentName)}"></th:block>
                                    <i>
                                        <img src="https://static.fangxiaoer.com/web/images/ico/sign/listIcon_money.jpg" alt="">
                                    </i>
                                </p>
                                <p><th:block th:text="${#strings.isEmpty(sale.price)?'':(#strings.toString(sale.shopType) eq '1'?'挂牌'+sale.price+'万':'租赁标价'+sale.price+((sale.payment eq '100' or sale.payment eq '50'or sale.payment eq '110')? '元/年':'元/月'))}"></th:block>
                                    <span th:if="${!#strings.isEmpty(sale.price) and !#strings.isEmpty(sale.cycle)}">|</span>
                                    <th:block th:text="${#strings.isEmpty(sale.cycle)?'':'成交周期'+sale.cycle+'天'}"></th:block>
                                    <i>
                                        <img src="https://static.fangxiaoer.com/web/images/ico/sign/listIcon_more.jpg" alt="">
                                    </i>
                                </p>
                                <p th:if="${sale.spanTime &lt; 31}">近30天内成交</p>
                                <p th:unless="${sale.spanTime &lt; 31}"><th:block th:text="${#strings.isEmpty(sale.dealTime)?'暂无资料':sale.dealTime+'成交'}"></th:block></p>
                            </div>
                            <div class="houseInfoRight" th:if="${sale.spanTime &lt; 31}">
                                <div><span><th:block th:text="${sale.dealPrice}"></th:block></span>
                                    <th:block th:text="${#strings.toString(sale.shopType) eq '1'?'万':((sale.payment eq '100' or sale.payment eq '50'or sale.payment eq '110')? '元/年':'元/月')}"></th:block></div>
                                <div>
                                    <a href="https://download.fangxiaoer.com/" target="_blank">下载APP查看成交></a>
                                    <div class="recode">
                                        <img src="https://static.fangxiaoer.com/web/images/sy/house/bargainHouseRecode.png"/>
                                    </div>
                                </div>
                            </div>
                            <div class="houseInfoRight" th:unless="${sale.spanTime &lt; 31}">
                                <div><!--总价-->
                                    <span>
                                            <!--成交价格--><th:block th:text="${#strings.indexOf(sale.dealPrice,'.') eq -1 ? sale.dealPrice:#strings.toString(sale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$','')}"></th:block>
                                        </span>
                                    <!--单位--><th:block th:text="${#strings.toString(sale.shopType) eq '1'?'万':((sale.payment eq '100' or sale.payment eq '50'or sale.payment eq '110')? '元/年':'元/月')}"></th:block>
                                </div>
                                <div class="price">
                                    <!--出售单价--><th:block th:if="${#strings.toString(sale.shopType) eq '1'}" th:text="${#strings.isEmpty(sale.dealUnitPrice)?'':(#strings.indexOf(sale.dealUnitPrice,'.') eq -1 ? sale.dealUnitPrice+'元/㎡':#strings.toString(sale.dealUnitPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/㎡')}"></th:block>
                                    <!--出租单价--><th:block th:if="${#strings.toString(sale.shopType) eq '2'}" th:text="${#strings.isEmpty(sale.dealUnitPrice)?'':(#strings.indexOf(sale.dealUnitPrice,'.') eq -1 ? sale.dealUnitPrice+'元/㎡·月':#strings.toString(sale.dealUnitPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/㎡·月')}"></th:block>
                                    <!--出兑单价--><th:block th:if="${#strings.toString(sale.shopType) eq '3'}" th:text="${#strings.isEmpty(sale.tranFee)?'':(#strings.indexOf(sale.tranFee,'.') eq -1 ? '转让费'+sale.tranFee+'万元':'转让费'+#strings.toString(sale.tranFee).replaceAll('0+?$','').replaceAll('[.]$','')+'万元')}"></th:block>
                                </div>
                            </div>
                        </div>
                        <div class="cl"></div>
                    </li>
                </ul>
            </div>
            <!--分页-->
            <div class="page">
                <div id="Pager1">
                    <div th:include="fragment/page :: page "></div>
                </div>
            </div>
            <div ></div>
        </div>
        <div style="float: right; width: 242px;">
        <div class="contentRight">
            <h2>服务信息</h2>
            <!--所属机构-->
            <div th:include = "agent/fragmentinter::affiliate"></div>

            <div class="box">
                <p class="jjr_3">主营区域</p>
                <span th:if="${!#strings.isEmpty(plateName1)}" th:text="${plateName1}"></span>
                <span th:if="${!#strings.isEmpty(plateName2)}" th:text="${plateName2}"></span>
                <span th:if="${!#strings.isEmpty(plateName3)}" th:text="${plateName3}"></span>
            </div>
        </div>
        <div class="agent-grjj" th:if="${!#strings.isEmpty(agentInfo.personalIntroduction)}">
            <h2>个人简介</h2>
            <p class="agent-grjjP" id="content" th:text="${agentInfo.personalIntroduction}"></p>
        </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
<!--个人介绍点击展开/收起-->
<script>
    function init(){
        var len = 51;
        var ctn = document.getElementById("content");
        var content = ctn.innerHTML;
        //alert(content);
        var span = document.createElement("span");
        var a = document.createElement("a");
        span.innerHTML = content.substring(0,len);
        a.innerHTML = content.length>len?"... [详细]":"";
        a.href = "javascript:void(0)";
        a.onclick = function(){
            if(a.innerHTML.indexOf("[详细]")>0){
                a.innerHTML = "<<&nbsp;[收起]";
                span.innerHTML = content;
            }else{
                a.innerHTML = "... [详细]";
                span.innerHTML = content.substring(0,len);
            }
        }
        ctn.innerHTML = "";
        ctn.appendChild(span);
        ctn.appendChild(a);
    }
</script>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>
