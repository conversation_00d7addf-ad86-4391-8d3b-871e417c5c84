<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<!--中介管理上方"您的位置"面包屑-->
<div th:fragment="intermediaryCard">
    <div class="crumbs">您的位置：<a th:href="@{/}" target="_blank">沈阳房产网</a> &gt; <a href="" th:text="${#strings.isEmpty(companyList?.company_name)?'':companyList?.company_name}"></a></div>
</div>
<!--中介管理门店名称-->
<div th:fragment="intermediaryInfo">
    <input type="hidden" id="forqrCode" th:value="${'https://m.fangxiaoer.com/intermediary/'+companyId + '.htm'}">
    <div class="homeBanner w1170Mar" th:if="${!#lists.isEmpty(companyList)}">
        <img  th:src="${#strings.isEmpty(companyList.companyLogo)? 'https://static.fangxiaoer.com/web/images/agent/noShopImg2.png': companyList.companyLogo}">
        <div class="shoreMain">
            <h4 th:text="${companyList.company_name}"></h4>
            <p><i class="mapIcon0"></i><th:block th:text="${companyList.company_address}"></th:block></p>
        </div>
        <!--扫码查看店铺-->
        <div class="seeshop">
            <!--<img src="" alt="" id="link_erwei">-->
            <div id="link_erwei"></div>
            <div>
                <p>微信扫码查看<b>TA</b>的店铺</p>
                <p>手机浏览更方便</p>
            </div>
        </div>
    </div>
    <script src="https://static.fangxiaoer.com/js/m/qrcode.min.js" type="text/javascript" charset="utf-8"></script>
    <script>
        var qrcodeUrl = $("#forqrCode").val()
        new QRCode(document.getElementById("link_erwei"), qrcodeUrl);
    </script>
</div>
<!--中介管理首页入口-->
<div th:fragment="agentIntermeHead">
    <div class="w1170Mar">
        <div class="homeNav">
            <a  th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/home/'+companyId}" th:class=" (${intermediaryType} == 9)? 'hover' : '' ">首页<span></span></a>
            <a th:if="${!#lists.isEmpty(companySecList) }" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/second/'+companyId}" th:class=" (${intermediaryType} == 1)? 'hover' : '' ">二手房<span></span></a>
            <a th:if="${!#lists.isEmpty(companyRentList)}" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/rent/'+companyId}" th:class=" (${intermediaryType} == 2)? 'hover' : '' ">租房<span></span></a>
            <a th:if="${!#lists.isEmpty(shop)}"   th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/shop/'+companyId}" th:class=" (${intermediaryType} == 3)? 'hover' : '' ">商铺<span></span></a>
            <a th:if="${!#lists.isEmpty(officeList)}"   th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/office/'+companyId}" th:class=" (${intermediaryType} == 4)? 'hover' : '' ">写字楼<span></span></a>
            <!--            <a th:if="${!#lists.isEmpty(companyList)}" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/store/'+companyId}" th:class=" (${intermediaryType} == 5)? 'hover' : '' ">门店</a>-->
            <a th:if="${!#lists.isEmpty(eliteList)}" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/elite/'+companyId}" th:class=" (${intermediaryType} == 66)? 'hover' : '' ">精英顾问<span></span></a>
            <a th:if="${!#lists.isEmpty(jobList)}" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/recruit/'+companyId}" th:class=" (${intermediaryType} == 77)? 'hover' : '' ">招贤纳士<span></span></a>
        </div>
    </div>
</div>
<!--中介管理二手房、租房、商铺、写字楼、门店入口-->
<div th:fragment="agentIntermeHead2">
    <div class="w1170Mar">
        <div class="homeNav">
            <a  th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/home/'+companyId}" th:class=" (${intermediaryType} == 9)? 'hover' : '' ">首页<span></span></a>
            <a th:if="${!#lists.isEmpty(second1) }" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/second/'+companyId}" th:class=" (${intermediaryType} == 1)? 'hover' : '' ">二手房<span></span></a>
            <a th:if="${!#lists.isEmpty(rent1)}" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/rent/'+companyId}" th:class=" (${intermediaryType} == 2)? 'hover' : '' ">租房<span></span></a>
            <a th:if="${!#lists.isEmpty(shop1)}"   th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/shop/'+companyId}" th:class=" (${intermediaryType} == 3)? 'hover' : '' ">商铺<span></span></a>
            <a th:if="${!#lists.isEmpty(office1)}"   th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/office/'+companyId}" th:class=" (${intermediaryType} == 4)? 'hover' : '' ">写字楼<span></span></a>
            <!--            <a th:if="${!#lists.isEmpty(companyList)}" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/store/'+companyId}" th:class=" (${intermediaryType} == 5)? 'hover' : '' ">门店</a>-->
            <a th:if="${!#lists.isEmpty(eliteList)}" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/elite/'+companyId}" th:class=" (${intermediaryType} == 66)? 'hover' : '' ">精英顾问<span></span></a>
            <a th:if="${!#lists.isEmpty(jobList)}" th:href="${#strings.isEmpty(companyId)? '': '/agent/intermediary/recruit/'+companyId}" th:class=" (${intermediaryType} == 77)? 'hover' : '' ">招贤纳士<span></span></a>
        </div>
    </div>
</div>
<!--中介 公司推荐经纪人-->
<div th:fragment="companyAgent">
    <div class="groomAgrnt RightK" th:if="${!#lists.isEmpty(eliteList)}">
        <h4 class="shopManageRightH4"><i></i>公司推荐经纪人</h4>
        <ul>
            <li th:each="eliteList,i:${eliteList}" th:if="${i.index lt 4}">
                <!--<a th:href="${'/agent/second/'+eliteList.memberId}" target="_blank">-->
                <a th:href="${eliteList.hasHouseFlag eq '1' ? '/agent/second/'+eliteList.memberId : (eliteList.hasHouseFlag eq '2' ? '/agent/rents/'+eliteList.memberId : (eliteList.hasHouseFlag eq '3' ? '/agent/shops/'+eliteList.memberId : ('/agent/office/'+eliteList.memberId )))}"  target="_blank">
                    <img th:src="${#strings.isEmpty(eliteList.pic)? 'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':eliteList.pic}" alt="">
                    <h5><span><th:block th:text="${eliteList.realName}"></th:block></span><i th:class="${'gra'+eliteList.level}"><th:block th:text="${'LV'+eliteList.stars}"></th:block></i></h5>
                    <p th:text="${eliteList.Mobile}">15802456600</p>
                </a>
            </li>
        </ul>
    </div>
</div>
<!--岗位急聘-->
<div th:fragment="companyUrgent">
    <div class="jiShop RightK" th:if="${ !#strings.isEmpty(agentUrgent)}">
        <h4 class="shopManageRightH4"><i></i>岗位急聘</h4>
        <ul class="urgentWant">
            <li th:each="urgent,i:${agentUrgent}" th:if="${i.index lt 5}">
                <a th:href="${'/positionDetail'+'/'+urgent.jobAdId+'.htm'}" target="_blank">
                    <h5 th:text="${urgent.jobTitle}"></h5>
                    <p class="agentRecruitList">
                        <span th:if="${!#strings.isEmpty(urgent.typeName)}" th:text="${urgent.typeName}">中介经纪人</span>
                        <i>丨</i>
                        <span>招聘人数：<i th:text="${urgent.jobNeedPersonNumber+'人'}">5</i></span>
                    </p>
                    <p th:if="${#strings.toString(urgent.jobSalaryRangeMin) ne '0' && #strings.toString(urgent.jobSalaryRangeMax) ne '0'}" th:text="${urgent.jobSalaryRangeMin+'-'+urgent.jobSalaryRangeMax+'元'}">10000-12000元 </p>
                    <p th:if="${(#strings.isEmpty(urgent.jobSalaryRangeMin)  || #strings.toString(urgent.jobSalaryRangeMin) eq '0') && !#strings.isEmpty(urgent.jobSalaryRangeMax || #strings.toString(urgent.jobSalaryRangeMax) eq '0')}" th:text="${'面议'}"></p>
                </a>
            </li>
            <a class="seeMoreList" th:if="${#lists.size(agentUrgent) gt 4}"  th:href="${'/agent/intermediary/recruit/'+companyId}">查看更多></a>
        </ul>
    </div>
</div>
<!--门店地址-->
<div th:fragment="storeOtherAddress">
    <div class="storeAddress RightK" th:if="${ !#strings.isEmpty(otherCompanyList) and #lists.size(otherCompanyList) ne 0}">
        <h4 class="shopManageRightH4"><i></i>门店地址</h4>
        <ul>
            <li th:each="other,i:${otherCompanyList}" th:if="${i.index lt 3}">
                <a th:href="${#strings.isEmpty(other.company_id)? '': '/agent/intermediary/home/'+other.company_id}"  target="_blank">
                    <span th:text="${other.company_name}"></span>
                </a>
            </li>
        </ul>
    </div>
</div>

<!--经纪人店铺 右侧服务信息 所属机构-->
<div th:fragment="affiliate">
    <div class="box">
        <span th:if="${!#strings.isEmpty(agentInfo.intermediaryName)  && #strings.isEmpty(intermeStore?.companyId) }" class="nothing_intermediary" style="padding-left: 0px;">
            <p class="jjr_1" th:if="${!#strings.isEmpty(agentInfo.intermediaryName)}">所属机构</p>
            <span th:text="${#strings.isEmpty(agentInfo.intermediaryName)? '': agentInfo.intermediaryName}">小二管家</span>
        </span>
        <span th:if="${!#strings.isEmpty(intermeStore?.companyId)}" class="have_intermediary" style="padding-left: 0px;">
            <p class="jjr_1">所属机构</p>
            <a  th:href="${#strings.isEmpty(intermeStore.companyId) ? '' :'/agent/intermediary/home/'+intermeStore.companyId}" target="_blank" style="text-decoration: none">
                <span style="color:#ff5200;" th:text="${#strings.toString(agentInfo.intermediaryName)+' >'}"></span>
                <!--<span style="color:#ff5200;" th:text="${#strings.isEmpty(intermeStore.companyName) ? agentInfo.intermediaryName : #strings.toString(intermeStore.companyName)+' >'}"></span>-->
            </a>
        </span>
    </div>
</div>
<!--新版精英顾问2019.09.19-->
<div th:fragment="new_companyAgent">
    <style>
        .villageAgent{padding: 16.5px 60px !important;width: 1049px !important;}
        .villageAgent  .sy-dynatown{padding: 0;border: none}
        .villageAgentCont{margin-top: 0 !important;}
        .sy-dynatown-ararow div{}
        .newHouseViewChunk #up{    position: absolute;
            left: -2px;
            top: 50%;
            margin-top: -14px;}
        .newHouseViewChunk #down{    position: absolute;
            right: -2px;
            top: 50%;
            margin-top: -14px;}
    </style>
    <!--<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/second/swiper.min.css">-->
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/dynatown.css?v=20200417" rel="stylesheet" type="text/css">
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <!--<script src="https://static.fangxiaoer.com/js/agent/swiper.min.js"></script>-->
    <h4 class="homeTitle w1170Mar" th:if="${!#lists.isEmpty(eliteList)}">
    	精英顾问
    	<i class="titleIco"></i>
    </h4>
    <div class="elite-adviser" th:if="${!#lists.isEmpty(eliteList)}">

        <div class="elite-k">
            <div class="elite-btn">
                <div><span id="up" class="elite-btn-left" style="transform: rotate(180deg);"></span></div>
                <div><span id="down" class="elite-btn-right"></span></div>
            </div>

            <div class="elite-main">
                <div th:each="eliteList,i:${eliteList}" class="elite-Swiper elite-Swiper-show">
                    <div class="elite-slide">
                        <a class="elite-slide-a" th:href="${eliteList.hasHouseFlag eq '1' ? '/agent/second/'+eliteList.memberId : (eliteList.hasHouseFlag eq '2' ? '/agent/rents/'+eliteList.memberId : (eliteList.hasHouseFlag eq '3' ? '/agent/shops/'+eliteList.memberId : ('/agent/office/'+eliteList.memberId )))}"  target="_blank">
                            <div class="elite-slide-img">
                                <img th:src="${#strings.isEmpty(eliteList.pic)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':eliteList.pic}" alt="">
                            </div>
                        </a>
                        <div class="elite-slide-cont">
                            <a class="elite-slide-to" th:href="${eliteList.hasHouseFlag eq '1' ? '/agent/second/'+eliteList.memberId : (eliteList.hasHouseFlag eq '2' ? '/agent/rents/'+eliteList.memberId : (eliteList.hasHouseFlag eq '3' ? '/agent/shops/'+eliteList.memberId : ('/agent/office/'+eliteList.memberId )))}"  target="_blank">
                                <h4 class="elite-slide-h4">
                                    <span class="elite-slide-name"><th:block th:text="${eliteList.realName}"></th:block></span>
                                    <i th:class="${'gra'+eliteList.level}"><th:block th:text="${'LV'+eliteList.stars}"></th:block></i>
                                </h4>
                            </a>
                            <div>
                                <!--微信扫码拨号-->
                                <div class="agent-cardTels">
                                    <span>电话咨询</span>
                                    <div class="show-topCardTels">
                                        <img th:id="${'forcode'+ i.count}" src="" alt="">
                                        <p>微信扫码拨号</p>
                                        <p><i>快速获取经纪人联系方式</i></p>
                                    </div>
                                </div>
                                <script>
                                    /*<![CDATA[*/
                                    $(function () {
                                        var i = [[${i.count}]]
                                        var tels = [[${eliteList.Mobile}]];
                                        var scene = 'tel,' +[[${eliteList.memberId}]]+'-1,6,' + tels;
                                        var img = "";
                                        var sss;
                                        $.ajax({
                                            type: "GET",
                                            async: false,
                                            url:  "/getWxACode",
                                            data:{"scene": scene},
                                            dataType : 'json',
                                            headers : {
                                                'Content-Type' : 'application/json;charset=utf-8'
                                            },
                                            success: function (data) {
                                                console.log(data)
                                                img = data.img;
                                                sss = data;
                                            }
                                        });
                                        $("#forcode" + i).attr("src","data:text/html;base64,"+img);
                                    });

                                </script>
                                <!--<span th:if="${!#strings.isEmpty(eliteList.Mobile)}" th:text="${eliteList.Mobile}">15802456600</span>-->
                                <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal" class="elite-slide-liaobei">
                                    <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" >
                                        <i></i>
                                        <text>聊呗</text>
                                    </div>
                                </a>
                                <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/imPolt/'+eliteList.Mobile}" target="_blank" class="elite-slide-liaobei">
                                    <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)">
                                        <i></i>
                                        <text>聊呗</text>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="suggest-show-txt"  th:if="${eliteList.other3 eq '3'}"><span th:text="${eliteList.other2}"></span></div>
                    </div>
               	<div class="jiangbei">
               		
               	</div>
                </div>

            </div>
        </div>
        <script src="https://static.fangxiaoer.com/js/agent/elite-adviser.js" type="text/javascript"></script>




    </div>
    <script>
        //	根据经纪人的个数判断是否轮播
        $(document).ready(function () {
            var agentLiNum = $('.sy-dynatown>div').length;
            console.log(agentLiNum)
            if(agentLiNum <= 4 ){
                $(".btnIconico").hide()
            }
        });

        //	经纪人轮播
        /*var swiper = new Swiper('.swiper-containervillageAgent', {
            slidesPerView: 4,
            spaceBetween: 20,
            slidesPerGroup: 1,
            loop: false,
            loopFillGroupWithBlank: true,
            pagination: {
                el: '.swiper-paginationVil',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
        });*/
        function changeColor(e) {
            $(e).css("background-color","#188CDE")
        }
        function changeColor2(e) {
            $(e).css("background-color","#32a3f2")
        }
    </script>
</div>
</body>
</html>