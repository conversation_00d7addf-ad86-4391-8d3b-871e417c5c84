<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
	<title th:text="'成交排行榜_沈阳二手房信息服务_沈阳二手房经纪人排行榜 - 房小二网'"></title>
	<meta name="keywords" th:content="'沈阳二手房,二手房排行,二手房经纪人排行,沈阳二手房经纪人'" />
	<meta name="description" th:content="'房小二网沈阳二手房经纪人排行榜为您带来沈阳二手房经纪人的销量排行与联系方式，供广大购房者选择值得信任的房产经纪人。买房，卖房，就上房小二网。'" />
	<meta http-equiv="Content-Type" content="text/html;charset=utf-8">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/dealRankingList.css"/>
	</head>
	<body>
	<!--引入头部导航栏-->
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
	<div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleHouses/" target="_blank">沈阳二手房</a> &gt; <a href="/viewDealRankingList/">成交排行</a></div>
		<div class="dealRankingList">
			<div class="banner"><img src="https://static.fangxiaoer.com/web/images/sy/sale/list/agentdeal_banner.jpg"/></div>
			<div class="listMain">
				<ul class="listMainKuang">
					<li>
						<div class="ulTitle"><i class="icon icon1"></i>月排行榜</div>
						<ul class="agentMain">
							<li th:each="m,mindex:${monthList}">
								<i th:text="${mindex.index+1}">1</i>
								<img th:src="${m.pic}" th:if="${mindex.index &lt; 3}"/>
								<div>
									<h5 th:text="${m.name}">赵国强</h5>
									<p th:text="${m.intermediaryName}" th:if="${mindex.index &lt; 3}">小二管家</p>
									<p th:if="${mindex.index &lt; 3}">成交<span th:text="${m.count}">10</span>套</p>
									<!--<p th:if="${mindex.index ge 3}"th:text="${m.intermediaryName}"></p>-->
									<div th:text="${m.mobile}" th:if="${mindex.index &lt; 3}"><s></s></div>
								</div>
								<a th:href="${#strings.isEmpty(m.AgencyID) ? '' :'/agent/second/'+m.AgencyID}" target="_blank">TA的店铺</a>
							</li>
						</ul>
					</li>
					
					
					<li>
						<div class="ulTitle"><i class="icon icon2"></i>季度排行榜</div>
						<ul class="agentMain">
							<li th:each="q,qindex:${quarterList}">
								<i th:text="${qindex.index+1}">1</i>
								<img th:src="${q.pic}" th:if="${qindex.index &lt; 3}"/>
								<div>
									<h5 th:text="${q.name}">赵国强</h5>
									<p th:text="${q.intermediaryName}" th:if="${qindex.index &lt; 3}">小二管家</p>
									<p th:if="${qindex.index &lt; 3}">成交<span th:text="${q.count}">10</span>套</p>
									<div th:text="${q.mobile}" th:if="${qindex.index &lt; 3}"><s></s></div>
								</div>
								<a th:href="${#strings.isEmpty(q.AgencyID) ? '' :'/agent/second/'+q.AgencyID}" target="_blank">TA的店铺</a>
							</li>
						</ul>
					</li>
					<!--<li>-->
						<!--<div class="ulTitle"><i class="icon icon3"></i>上半年排行榜</div>-->
						<!--<ul class="agentMain">-->
							<!--<li th:each="y,yindex:${yearList}">-->
								<!--<i th:text="${yindex.index+1}">1</i>-->
								<!--<img th:src="${y.pic}" th:if="${yindex.index &lt; 3}"/>-->
								<!--<div>-->
									<!--<h5 th:text="${y.name}">赵国强</h5>-->
									<!--<p th:text="${y.intermediaryName}" th:if="${yindex.index &lt; 3}">小二管家</p>-->
									<!--<p>成交<span th:text="${y.count}">10</span>套</p>-->
									<!--<div th:text="${y.mobile}" th:if="${yindex.index &lt; 3}"><s></s>************</div>-->
								<!--</div>-->
								<!--<a th:href="${#strings.isEmpty(y.AgencyID) ? '' :'/agent/second/'+y.AgencyID}" target="_blank">TA的店铺</a>-->
							<!--</li>-->
						<!--</ul>-->
					<!--</li>-->
					
				</ul>
			</div>
		</div>
	<div class="cl"></div>
	<!--底部1-->
	<div th:include="fragment/fragment:: footer_list"></div>
	<div th:include="fragment/fragment::tongji"></div>
	<div th:include="fragment/fragment::esfCommonFloat"></div>
	</body>
</html>
