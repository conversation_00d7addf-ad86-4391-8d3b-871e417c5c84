<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="utf-8"/>
    <title>房产经纪人_沈阳房产经纪人_房产中介 - 房小二网</title>
    <meta name="keywords" content="房产经纪人,沈阳房产经纪人,房产中介,房产专家"/>
    <meta name="description" content="房小二网为您提供沈阳房产经纪人列表，来自各大平台与中介的房产经纪人联系方式、所属门店、主营范围、联系方式一目了然，买好房，就上房小二网。"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/sale/goldAgent.css"/>

    <style>
        .j-title{
            display: flex;
            align-items: center;

        }
        .jiangbei{
            width: 19px !important;
            height: 19px !important;
            margin-left: 7px;

        }
    </style>
</head>

<body>
<!--页头-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3"></div>
<div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/saleHouses/">沈阳二手房</a> &gt; <a
        href="/findGoldAgent">找经纪人</a></div>
<div class="goldAgent">
    <div class="gAbanner">
        <h4>金牌经纪人展示</h4>
        <a href="/findAgent">查看全部经纪人</a>
    </div>
    <div class="goldAgentMain">
        <div class="gAtitle" hidden>
            <i></i>
            <h4>金牌经纪人展示</h4>
            <i></i>
        </div>
        <ul class="gAlist">
            <li th:each="a:${agentList}">
                <a th:href="${a.hasHouseFlag eq '1' ? '/agent/second/'+a.memberId : (a.hasHouseFlag eq '2' ? '/agent/rents/'+a.memberId : (a.hasHouseFlag eq '3' ? '/agent/shops/'+a.memberId : ('/agent/office/'+a.memberId )))}"
                   target="_blank">
                    <div class="imgL">
                        <img th:src="${a.pic}" alt=""/>
                    </div>
                    <div class="gAlistR">
                        <div class="j-title">
                            <h5 th:text="${a.realName}">张雨生</h5>
                            <a th:if="${!#strings.isEmpty(a.memberTop) and a.memberTop ne '0'}" href="/memberTopList"><img class="jiangbei" src="https://static.fangxiaoer.com/web/images/sy/prominent/jiangbei.png"></a>
                        </div>
                        <p th:if="${!#strings.isEmpty(a.intermediaryName)}">所属门店：<span th:text="${a.intermediaryName}">喜鹊不动产</span></p>
                        <p th:if="${!#lists.isEmpty(a.subName)}">主营小区：<span th:each="s,index:${a.subName}"
                                                                            th:text="${((index.index+1) == #lists.size(a.subName)) ? s.name : s.name+' | '}">宏发·英里蓝湾 | 北美家园啊啊</span>
                        </p>
                        <!--<p th:if="${a.regionName}">主营区域：<span th:text="${a.regionName}">浑南区</span></p>-->
                        <p th:if="${a.sortTel}">电话：<span th:text="${a.sortTel}"></span></p>

                    </div>
                </a>
            </li>


        </ul>
        <a href="/findAgent" class="seeMoreAgent">查看全部经纪人</a>

    </div>

</div>
<script>
    var $li = $('.gAlist').find('li');
    var $more = $('.goldAgentMain').find('#seeMoreAgent');
    for (var i = 0; i < $li.length; i++) {
        i < 25 ? $li.eq(i).show() : $li.eq(i).hide();
    }
    $li.length > 12 ? $more.show() : $more.hide();

</script>

<div class="cl"></div>
<!--底部1-->
<div th:include="fragment/fragment:: footer_list"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
</body>

</html>