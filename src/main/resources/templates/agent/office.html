<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺,沈阳写字楼经纪人房源信息 - 房小二网'}">田经理的店铺，沈阳写字楼经纪人房源信息 - 房小二网</title>
    <meta name="keywords" content="沈阳经纪人写字楼房源,沈阳写字楼,沈阳写字楼网,沈阳写字楼房源"/>
    <meta name="description" content="房小二网沈阳写字楼，帮你快速找到精准客户，每天海量真实用户浏览，助您快速成交。提供全面的沈阳写字楼信息，让您轻松找到自己想要的房子，为您带来更好的写字楼体验。"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/jjrsf/'+agentId+'-3.htm'}">
    <!--<link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css?v=20190425" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        function qingchu() {
            setTimeout(function () {
                location.href = "/agent/office/"+[[${agencyId}]];
            },500);
        }
        $(function () {
            $("#bottonss").click(function () {
                var dd = $("#hidden0").val();
                var dd1 = $("#Hidden2").val();
                var dd2 = $("#Hidden1").val();
                //模糊搜索字段
                var dds = $("#xiaoquid").val();
                // var sou = "72269";
                if (dd != null && dd != "") {
                    dd = "-r"+dd;
                }
                if (dd1 != null && dd1 != "") {
                    dd1 = "-t"+dd1;
                }
                if (dd2 != null && dd2 != "") {
                    dd2 = "-c"+dd2;
                }
                var search ;
                if (dds == null || dds == undefined || dds == '') {
                    search = ""
                }else {
                    search = "-search="+dds;
                }
                var url = "/agent/office/"+[[${agencyId}]]+"/" + dd + dd1 +  dd2 +search;
//                setCookie("searchKey",dds,10);
                location.href = url;

            });
        });

        $(function () {  //3个下拉列表取url参数赋值
            var hr = window.location.href;
            var str = hr.split("_");
            var zj = str[1];
            getData(zj, "#hidden0", "#zongjia");
            var mj = str[2];
            getData(mj, "#Hidden2", "#mianji");
            var hx = str[3];
            getData(hx, "#Hidden1", "#huxing");
            var xq = str[4];

            if (xq != "" && xq != null) {
                $("#xiaoquid").val(decodeURIComponent(xq)); //decodeURIComponent方法用于 window.location.href中 中文乱码解码
            }

        })

        function getData(name, num, adds) {//下拉列表取url参数赋值用到的方法
            if (name != -1 && name != "" && name != null) {
                $(num).val(name);
                var bb = $(num).parent(".my_xl").find(".my_xl_list li[value=" + name + "]").html();
                $(adds).html(bb);
                var container = $("body"),
                        scrollTo = $('.content');
                container.scrollTop(0);
                container.scrollTop(
                        scrollTo.offset().top - container.offset().top + container.scrollTop()
                );
            }
        }
    </script>
    <style type="text/css">
        .infRight p b {
            font-weight: normal;
            margin-left: 10px;
        }
        .agent-grjj{width: 220px}
        .warnings {
            margin: 20px 0;
            padding: 20px 0 20px 0;
            background: #fffbf6;
            border: 1px solid #f5dcbc;
        }
        .warnings p {
            background: url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left;
            font-size: 14px;
            line-height: 24px;
            font-weight: bold;
            color: #333;
            width: 420px;
            margin: auto;

        }


        .contentRight .box p.jjr_6 {
            background-position: 0px -76px;
        }
        .com-text{
            color: #0058FF !important;
        }
        .new-box{
            text-align: left;
            border-bottom: 1px #f2f2f2 dashed;


            line-height: 30px;
            margin-top: 6px;

            display: flex;
        }
        .in-img{
            width: 15px;
            height: 15px;

            margin-left: 10px;
            margin-top: 8px;
        }
        .in-right{
            margin-left: 12px;
        }
        .in-text{
            color: #999;
        }
        a{
            text-decoration: none !important;
        }
        .no-house{
            width: 370px;
            height: 100%;
        }
        .no-text{


            font-family: PingFang SC-Medium, PingFang SC;

            color: #696969;
        }
    </style>
</head>
<body onload="init()">
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"  th:with="firstNavIndex=9"></div>
<!--搜索-->
<!--<div id="search2017"  th:include = "fragment/fragment::searchNav" th:with="type=4"></div>-->
<input type="hidden" id="agencyId" th:value="${agencyId+'-4'}" >
<div class="main">
    <div th:include = "fragment/fragment::agentShopCard"></div>
    <div th:if="${!#strings.isEmpty(agentInfo.ShopStatus)  and  agentInfo.ShopStatus eq '0'}" class="warning warnings">
        <p>
            很抱歉，该经纪人店铺已经关闭。<br>
            <a th:href="@{/findAgent}"> 查找更多经纪人>></a>
        </p>
    </div>
    <div class="content" th:if="${#strings.isEmpty(agentInfo.ShopStatus)  or  agentInfo.ShopStatus eq '1'}">
        <div th:include = "fragment/fragment::agentShopsHead" th:with="agentHouseTpye=4"></div>
        <div class="contentMain">
            <div class="contentCtn">
                <div class="my_xl" th:if="${!#lists.isEmpty(region)}">
                    <input name="hidden0" type="hidden" id="hidden0"  class="my_xl_input" th:each="region:${region}" th:if="${region.selected}" th:value="${region.id}"/>
                    <div class="my_xl_txt" id="zongjia"  th:each="region:${region}" th:if="${region.selected}"  th:text="${#strings.isEmpty(region.id) ? '区域' : region.name}">区域</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="region:${region}" th:href="${region.url}" ><li data-key="PriceBase" th:text="${region.name}" th:value="${region.id}">全部</li></a>
                    </ul>
                </div>
                <div class="my_xl" th:if="${!#lists.isEmpty(type)}">
                    <input name="Hidden2" type="hidden" id="Hidden2" class="my_xl_input" th:each="n:${type}" th:if="${n.selected}"  th:value="${n.id}" />
                    <div class="my_xl_txt" id="mianji" th:each="n:${type}" th:if="${n.selected}" th:text="${#strings.isEmpty(n.id)? '供求':n.name}" >供求</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="n:${type}" th:href="${n.url}"><li data-key="BuildArea"  th:text="${n.name}" th:value="${n.id}">全部</li></a>
                    </ul>
                </div>
                <div class="my_xl"  th:if="${!#lists.isEmpty(category)}">
                    <input name="Hidden1" type="hidden" id="Hidden1" class="my_xl_input"th:each="t:${category}" th:if="${t.selected}" th:value="${t.id}" />
                    <div class="my_xl_txt" id="huxing" th:each="t:${category}" th:if="${t.selected}" th:text="${#strings.isEmpty(t.id)?'类型':t.name}" >类型</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="t:${category}" th:href="${t.url}" > <li data-key="roomType"  th:text="${t.name}" th:value="${t.id}">全部</li></a>
                    </ul>
                </div>
                <div class="houseSearch">
                    <input type="text" id="xiaoquid" class="searchInput" th:value="${searchName}" autocomplete="off" placeholder="请输入关键字" >
                    <input name="button" id="bottonss" type="button" class="searchBtn" value="" >
                </div>
                <p onclick="qingchu()"><i></i>清空筛选条件</p>
                <!--<span>小二为你找到<i th:text="${msg}">34</i>个符合条件的房源</span>-->
            </div>
            <div class="cl"></div>
<!--            <div th:if="${#lists.isEmpty(shop)}" class="warning">-->
<!--                <p>-->
<!--                    很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>-->
<!--                    <a th:href="@{/scriptoriums/}" target="_blank"> 查找相似房源>></a>-->
<!--                </p>-->
<!--            </div>-->

            <div th:if="${#lists.isEmpty(shop)}" class="no-box">
                <img class="no-house" src="https://static.fangxiaoer.com/web/images/sy/house/no-house.png">
                <div class="no-text">暂无房源</div>
            </div>


            <div class="inf" th:if="${!#lists.isEmpty(shop)}" th:each = "shop : ${shop}">
                <a th:href="${#strings.isEmpty(shop.shopId)? '': '/scriptorium/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
                    <!--<i class='imgNum' th:if="${shop.picNum ne null and #strings.toString(shop.picNum).length() gt 1 or shop.picNum gt '4'}" th:text="${#strings.isEmpty(shop.picNum)? '':shop.picNum}">6</i>-->
                    <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : shop.pic}" th:alt="${#strings.isEmpty(shop.title) ? '': shop.title}">
<!--                    <div class="" th:if="${#strings.toString(shop.isXiQue) eq '1'}">佣金95折</div>-->
                    <!--VR and 视频都存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                        <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                        <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                    </s>
                    <!--VR存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                        <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                    </s>
                    <!--视频存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                        <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                    </s>
                </a>
                &nbsp;&nbsp;&nbsp;&nbsp;<div class="infCtn">
                <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)? '': '/scriptorium/'+shop.shopId+'.htm'}" th:text="${#strings.isEmpty(shop.title) ? '': shop.title}">（出售）金地悦峰.金悦街 首府新区陵东板块S8#3门</a>
                <div  class="fourSpan">
                    <span th:text="${#strings.isEmpty(shop.area)? '': #numbers.formatInteger(shop.area,1)+'m²' }"></span>
                    <span th:text = "${#strings.isEmpty(shop.fitmentType) ? '':shop.fitmentType}"></span>
                </div>
                <p  class="houseAddress"  th:if="${ !#strings.isEmpty(shop.officeName)  and !#strings.isEmpty(shop.regionName)  and !#strings.isEmpty(shop.plateName) and !#strings.isEmpty(shop.address)}">
                    <s th:if="${ !#strings.isEmpty(shop.officeName)}" class="houseAddressSpance">
                        <i  th:text="${#strings.isEmpty(shop.officeName)?'':shop.officeName}"></i>
                    </s>
                    <s th:if="${!#strings.isEmpty(shop.regionName) and !#strings.isEmpty(shop.plateName) and !#strings.isEmpty(shop.address)}">
                        <i  th:text="${#strings.isEmpty(shop.regionName)?'':shop.regionName}"></i>-
                        <i  th:text="${#strings.isEmpty(shop.plateName)?'':shop.plateName}"></i>-
                        <i  th:text="${#strings.isEmpty(shop.address)?'':shop.address}"></i>
                    </s>
                </p>
                <!--<p class="person" th:text = "${#strings.isEmpty(shop.spantime)? '' : shop.spantime+'更新'}">-->
                    <!--22天前更新-->
                <!--</p>-->
                <div  class="houseItemIcon">
                    <span th:class="tese_+${i.index+1}" th:if="${!#lists.isEmpty(shop.houseTraits) and i.index &lt; 3}" th:each="houseTraits,i:${#strings.toString(shop.houseTraits).split(',')}"
                       th:text="${houseTraits}">
                    </span>
                </div>
            </div>
                <div class="infRight" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '5'}">
                    <p class="infRightPrise">
                        <s th:text = "${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0' ?'面议': ((#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')))}"></s>
                        <i th:text = "${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0' ?'':'万'}"></i>
                        <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">售</span>-->
                    </p>
                    <p><i><b th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'':#strings.toString(shop.unitPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/m²'}">13007元/m²</b></p>
                </div>

                <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '4'}">
                    <p class="infRightPrise">
                        <s th:block th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'面议': ((#strings.indexOf(shop.unitPrice,'.') eq -1 ? shop.unitPrice:#strings.toString(shop.unitPrice).replaceAll('0+?$','').replaceAll('[.]$','')))}"></s>
                        <i th:block th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'':'元/m²·天'}"></i>
                        <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">租</span>-->
                    </p>
                    <p><b th:text="${#strings.isEmpty(shop.price) or  #strings.toString(shop.price) eq '0.0' ?'':#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')+'元/月'}">1.77 元/m²·天</b></p>
                </div>

            </div>
            <!--分页-->
            <div class="page">
                <div id="Pager1">
                    <div th:include="fragment/page :: page "></div>
                </div>
            </div>
            <div ></div>
        </div>
        <div style="float: right; width: 242px;">
        <div class="contentRight">
            <h2>服务信息</h2>
            <!--所属机构-->
<!--            <div th:include = "agent/fragmentinter::affiliate"></div>-->

            <div class="box" th:if="${agentData.intermediaryState eq '1'}">
                <p class="jjr_6">服务公司</p>
                <a target="_blank" th:href="${'/agentIntermediary/second/'+agentData.id}"><span  class="com-text" th:if="${!#strings.isEmpty(agentData.intermediaryName)}" th:text="${agentData.intermediaryName}"></span>
                </a>
            </div>
            <div class="new-box">
                <img class="in-img"  src="https://static.fangxiaoer.com/web/images/sy/sale/institution.png" />
                <div class="in-right">
                    <p class="jjr_7">中介机构</p>
                    <span class="in-text"  th:if="${!#strings.isEmpty(agentData.agentIntermediaryAlias)}" th:text="${agentData.agentIntermediaryAlias}"></span>
                </div>

            </div>


            <div class="box">
                <p class="jjr_3">主营板块</p>
                <span th:if="${!#strings.isEmpty(plateName1)}" th:text="${plateName1}"></span>
                <span th:if="${!#strings.isEmpty(plateName2)}" th:text="${plateName2}"></span>
                <span th:if="${!#strings.isEmpty(plateName3)}" th:text="${plateName3}"></span>
            </div>

        </div>
<!--        <div class="agent-grjj" th:if="${!#strings.isEmpty(agentInfo.personalIntroduction)}">-->
<!--            <h2>个人简介</h2>-->
<!--            <p class="agent-grjjP"  id="content" th:text="${agentInfo.personalIntroduction}"></p>-->
<!--        </div>-->
        </div>
    </div>

</div>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
<!--个人介绍点击展开/收起-->
<script>
    function init(){
        var len = 51;
        var ctn = document.getElementById("content");
        var content = ctn.innerHTML;
        //alert(content);
        var span = document.createElement("span");
        var a = document.createElement("a");
        span.innerHTML = content.substring(0,len);
        a.innerHTML = content.length>len?"... [详细]":"";
        a.href = "javascript:void(0)";
        a.onclick = function(){
            if(a.innerHTML.indexOf("[详细]")>0){
                a.innerHTML = "<<&nbsp;[收起]";
                span.innerHTML = content;
            }else{
                a.innerHTML = "... [详细]";
                span.innerHTML = content.substring(0,len);
            }
        }
        ctn.innerHTML = "";
        ctn.appendChild(span);
        ctn.appendChild(a);
    }
</script>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
</body>
</html>
