<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺,沈阳二手房经纪人房源信息 - 房小二网'}">欧春鑫的店铺，沈阳二手房经纪人房源信息 - 房小二网</title>
    <meta name="keywords" content="沈阳二手房,沈阳二手房网,沈阳二手房出售,沈阳二手房买卖,沈阳房产经纪人,沈阳二手房经纪人"/>
    <meta name="description" content="房小二网沈阳二手房为您提供海量真实的沈阳二手房房源信息，沈阳二手房经纪人信息，及时的二手房出租出售信息，帮您定位，搜索各类出售房源，带来最佳二手房买卖体验。"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/jjrsf/'+agentId+'-1.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/agent/newsMain.css" />
    <script th:inline="javascript" >
        function qingchu() {
            setTimeout(function () {
                location.href = "/agent/news/"+[[${agencyId}]];
            },500);
        }
        $(function() {
            $("#bottonss").click(function() {
                var dd = $("#hidden0").val();
                var dd1 = $("#Hidden1").val();
                var dd2 = $("#Hidden2").val();
                //模糊搜索字段
                var dds = $("#xiaoquid").val();
                // var sou = "68680";
                if (dd != null && dd !="") {
                    dd = "-p"+dd;
                }
                if (dd1 != null && dd1 != "") {
                    dd1 = "-a"+dd1;
                }
                if (dd2 != null && dd2 != "") {
                    dd2 = "-l"+dd2;
                }
                var search ;
                if (dds == null || dds == undefined || dds == '') {
                    search = ""
                }else {
                    search = "-search="+dds;
                }
                var url = "/agent/news/"+[[${agencyId}]]+'/' + dd + dd1 + dd2 +search;
//                setCookie("searchKey",dds,10);
                location.href = url;

            });
        });
        $(function() {
            var hr = window.location.href;
            var str=hr.split("_");
            var zj = str[1];
            getData(zj, "#hidden0", "#zongjia");
            var mj = str[2];
            getData(mj, "#Hidden1", "#mianji");
            var hx = str[3];
            getData(hx, "#Hidden2", "#huxing");
            var xq = str[4];
            if (xq != "" && xq != null) {
                $("#xiaoquid").val(decodeURIComponent(xq));
            }

            //var name0 = $("#hidden0").pasaleHouse(".my_xl").find(".my_xl_list li[value=" + mj + "]").html();
            //$("#zongjia").html(name0);
        })
        function getData(name, num, adds) {
            if (name != -1 && name != "" && name != null) {
                $(num).val(name);
                var bb = $(num).pasaleHouse(".my_xl").find(".my_xl_list li[value=" + name + "]").html();
                $(adds).html(bb);
                var container = $("body"),
                        scrollTo = $('.content');
                container.scrollTop(0);
                container.scrollTop(
                        scrollTo.offset().top - container.offset().top + container.scrollTop()
                );
            }
        }
    </script>
</head>
<body >
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"  th:with="firstNavIndex=3"></div>
<!--搜索-->
<!--<div id="search2017"  th:include = "fragment/fragment::searchNav" th:with="type=8"></div>-->
<input type="hidden" id="agencyId" th:value="${agencyId+'-1'}" >
<div class="main">
    <div th:include = "fragment/fragment::agentShopCard"></div>
    <div class="content">
        <div th:include = "fragment/fragment::agentShopsHead" th:with="agentHouseTpye=8"></div>
        <div class="newK" >
            <div class="newsMainL" th:if="${!#lists.isEmpty(news)}">
                <ul>
                    <li th:each="n:${news}">
                        <a th:href="${'/agentnews/'+n.newsID+'.htm'}" target="_blank">
                            <img th:src="${n.pic ne '' and n.pic ne null?n.pic:'https://static.fangxiaoer.com/web/images/agent/newsListImgNo.jpg'}" alt="">
                        </a>
                        <div>
                            <a th:href="${'/agentnews/'+n.newsID+'.htm'}" target="_blank"><h4 th:text="${n.title}"></h4></a>
                            <a th:href="${'/agentnews/'+n.newsID+'.htm'}" target="_blank"><p th:text="${n.description}"></p></a>
                            <div>
                                 <p>发布时间：<span th:text="${n.auditTime}">2018-6-5</span></p>
                                 <p>分类： <span>市场观点</span> </p>
                            </div>
                            <a th:href="${'/agentnews/'+n.newsID+'.htm'}" target="_blank" class="seeMoreZi">查看全文</a>
                        </div>

                    </li>
                </ul>
            </div>
            <div class="newsMainR">
                <h5><i></i>房产快讯</h5>
                <!--<ul>-->
                    <!--<li class="newsMainRBli">-->
                        <!--<p>关注 | 房企融资受限资金面持续收紧</p>-->
                    <!--</li>-->
                    <!--<li>-->
                        <!--<p>关注 | 房企融资受限资金面持续收紧</p>-->
                    <!--</li>-->
                <!--</ul>-->
                <div class="hot" th:include="fragment/fragment::news_flash"></div>
            </div>

        </div>
    </div>
</div>
<div class="cl"></div>
<div class="page">
    <div id="Pager1">
        <div th:include="fragment/page :: page"></div>
    </div>
</div>
<div class="cl"></div>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>
