<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺，沈阳租房经纪人房源信息 - 房小二网<'}" >陈晓宇的店铺，沈阳租房经纪人房源信息 - 房小二网</title>
    <meta name="keywords" content="沈阳经纪人租房房源,沈阳租房,沈阳租房网,沈阳租房房源"/>
    <meta name="description" content="房小二网沈阳租房，帮你快速找到精准客户，每天海量真实用户浏览，助您快速成交。提供全面的沈阳租房信息，让您轻松找到自己想要的房子，为您带来更好的租房体验。"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/jjrsf/'+agentId+'-2.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleVillageList.css"/>
</head>
<body onload="init()">
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"></div>
<!--搜索-->
<!--<div id="search2017"  th:include = "fragment/fragment::searchNav" th:with="type=2"></div>-->
<input type="hidden" id="agencyId" th:value="${agencyId+'-2'}" >
<div class="main">
    <div th:include = "fragment/fragment::agentShopCard"></div>
    <div class="content">
        <div th:include = "fragment/fragment::agentShopsHead" th:with="agentHouseTpye=7"></div>
        <div class="w main villageAsk">
            <div class="left">
                <h4 style="text-align: left;padding-left:45px"> <span th:text="${agentInfo.username}"></span>问答</h4>
                <ul>
                    <li th:each="p:${plotAskInfo}">
                        <div class="Q-A">
                            <h4 th:text="${'【'+p.subName+'】 '+p.askContent}">【城建花园】这里的住户基本是什么职业呢？白领集中么？ </h4>
                            <p><span>答&nbsp;:&nbsp;</span><th:block th:text="${p.replyContent}"></th:block></p>
                            <span th:if="${p.replyTime}" th:text="${#strings.toString(p.replyTime).replace('.','-')}">2018年4月11日15:56:57</span>
                            <span th:if="${#strings.isEmpty(p.replyTime)}" th:text="${#strings.toString(p.askTime).replace('.','-')}">2018年4月11日15:56:57</span>
                        </div>
                        <a th:href="${'/saleVillages/'+p.id+'/getAskDetail.htm'}" class="iconQ-A" th:text="${#strings.isEmpty(p.count) ? '0' : p.count}">12</a>
                    </li>
                </ul>
            </div>
            <div class="cl"></div>
            <div class="page">
                <div th:include="fragment/page :: page"></div>
            </div>

    </div>

</div>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>