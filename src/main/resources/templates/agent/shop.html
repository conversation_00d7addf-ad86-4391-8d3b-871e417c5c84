<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${#strings.isEmpty(agentInfo.username)?'':agentInfo.username+'的店铺,沈阳商铺经纪人房源信息 - 房小二网'}">田经理的店铺，沈阳商铺经纪人房源信息 - 房小二网</title>
    <meta name="keywords" content="沈阳经纪人商铺房源,沈阳商铺,沈阳商铺网,沈阳商铺房源"/>
    <meta name="description" content="房小二网沈阳商铺，帮你快速找到精准客户，每天海量真实用户浏览，助您快速成交。提供全面的沈阳商铺信息，让您轻松找到自己想要的房子，为您带来更好的租房体验。"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/jjrsf/'+agentId+'-3.htm'}">
    <!--<link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css?v=20200509" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        function qingchu() {
            setTimeout(function () {
            location.href = "/agent/shops/"+[[${agencyId}]];
            },500);
        }



        $(function () {
            $("#bottonss").click(function () {
                var dd = $("#hidden0").val();
                var dd1 = $("#Hidden2").val();
                var dd2 = $("#Hidden1").val();
                //模糊搜索字段
                var dds = $("#xiaoquid").val();
                // var sou = "72269";
                if (dd != null && dd != "") {
                    dd = "-r"+dd;
                }
                if (dd1 != null && dd1 != "") {
                    dd1 = "-n"+dd1;
                }
                if (dd2 != null && dd2 != "") {
                    dd2 = "-t"+dd2;
                }
                var search ;
                if (dds == null || dds == undefined || dds == '') {
                    search = ""
                }else {
                    search = "-search="+dds;
                }
                var url = "/agent/shops/"+[[${agencyId}]]+"/" + dd + dd1 +  dd2 +search;
//                setCookie("searchKey",dds,10);
                location.href = url;

            });
        });

        $(function () {  //3个下拉列表取url参数赋值
            var hr = window.location.href;
            var str = hr.split("_");
            var zj = str[1];
            getData(zj, "#hidden0", "#zongjia");
            var mj = str[2];
            getData(mj, "#Hidden2", "#mianji");
            var hx = str[3];
            getData(hx, "#Hidden1", "#huxing");
            var xq = str[4];

            if (xq != "" && xq != null) {
                $("#xiaoquid").val(decodeURIComponent(xq)); //decodeURIComponent方法用于 window.location.href中 中文乱码解码
            }

        })

        function getData(name, num, adds) {//下拉列表取url参数赋值用到的方法
            if (name != -1 && name != "" && name != null) {
                $(num).val(name);
                var bb = $(num).parent(".my_xl").find(".my_xl_list li[value=" + name + "]").html();
                $(adds).html(bb);
                var container = $("body"),
                    scrollTo = $('.content');
                container.scrollTop(0);
                container.scrollTop(
                    scrollTo.offset().top - container.offset().top + container.scrollTop()
                );
            }
        }
    </script>
    <style type="text/css">
        .infRight p b {
            font-weight: normal;
            margin-left: 10px;
        }
        .topInf .fourSpan span{border-left: none;color: #333;font-size: 14px}
        .agent-grjj{width: 220px}
        .warnings {
            margin: 20px 0;
            padding: 20px 0 20px 0;
            background: #fffbf6;
            border: 1px solid #f5dcbc;
        }
        .warnings p {
            background: url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left;
            font-size: 14px;
            line-height: 24px;
            font-weight: bold;
            color: #333;
            width: 420px;
            margin: auto;

        }

        .contentRight .box p.jjr_6 {
            background-position: 0px -76px;
        }
        .com-text{
            color: #0058FF !important;
        }
        .new-box{
            text-align: left;
            border-bottom: 1px #f2f2f2 dashed;


            line-height: 30px;
            margin-top: 6px;

            display: flex;
        }
        .in-img{
            width: 15px;
            height: 15px;

            margin-left: 10px;
            margin-top: 8px;
        }
        .in-right{
            margin-left: 12px;
        }
        .in-text{
            color: #999;
        }
        a{
            text-decoration: none !important;
        }
        .no-house{
            width: 370px;
            height: 100%;
        }

        .no-text{
            font-family: PingFang SC-Medium, PingFang SC;

            color: #696969;
        }

    </style>
</head>
<body onload="init()">
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"  th:with="firstNavIndex=5"></div>
<!--搜索-->
<!--<div id="search2017"  th:include = "fragment/fragment::searchNav" th:with="type=4"></div>-->
<input type="hidden" id="agencyId" th:value="${agencyId+'-3'}" >
<div class="main">
    <div th:include = "fragment/fragment::agentShopCard"></div>
    <div th:if="${!#strings.isEmpty(agentInfo.ShopStatus)  and  agentInfo.ShopStatus eq '0'}" class="warning warnings">
        <p>
            很抱歉，该经纪人店铺已经关闭。<br>
            <a th:href="@{/findAgent}"> 查找更多经纪人>></a>
        </p>
    </div>
    <div class="content" th:if="${#strings.isEmpty(agentInfo.ShopStatus)  or  agentInfo.ShopStatus eq '1'}">
        <div th:include = "fragment/fragment::agentShopsHead" th:with="agentHouseTpye=3"></div>
        <div class="topInf"  th:if="${shopPush}">
            <a th:href="${#strings.isEmpty(shopPush.shopId)? '': '/shop/'+shopPush.shopId+'.htm'}" target="_blank" class="infLeft">
                <img th:src="${#strings.isEmpty(shopPush.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':shopPush.pic }" th:alt="${#strings.isEmpty(shopPush.title)? '':shopPush.title}">
                <i></i>
            </a>
            <div class="infCtn">
                <a class="infCtnTitle" target="_blank" th:href="${#strings.isEmpty(shopPush.shopId)? '': '/shop/'+shopPush.shopId+'.htm'}"
                   th:text = "${#strings.isEmpty(shopPush.title)? '':shopPush.title}">
                    国瑞城 1室 1厅 1卫 49㎡</a>
                <div class="" th:if="${!#strings.isEmpty(shopPush.shopType) and  shopPush.shopType eq '1'}">
                    <p class="money" th:if="${!#strings.isEmpty(shopPush.shopType) and  shopPush.shopType eq '1'}"  >
                        <i class="money"><th:block th:text="${#strings.isEmpty(shopPush.price) or shopPush.price eq '0.00' ?'面议': (#strings.indexOf(shopPush.price,'.') eq -1 ? shopPush.price:#strings.toString(shopPush.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block></i>
                        <i th:text="${#strings.isEmpty(shopPush.price) or shopPush.price eq '0.00'?'':'万'}" style="margin-right: 15px;"></i>
                        <th:block th:text="${#strings.isEmpty(shopPush.price) or shopPush.price eq '0.00' ?'':shopPush.unitPrice}"></th:block>
                    </p>

<!--                    <p><i><b >13007元/m²</b></i></p>-->
                </div>

                <div class=""  th:if="${!#strings.isEmpty(shopPush.shopType) and  shopPush.shopType eq '2'}">
                    <p class="money">
                        <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月')}"></th:block>-->
                        <i class="money" style="font-weight: bold" th:if="${#strings.isEmpty(shopPush.price) or shopPush.price eq '0.00'}" th:text="${'面议'}"></i>
                        <s th:if="${!#strings.isEmpty(shopPush.price) && shopPush.price ne '0.00'}">
                            <i th:text="${#strings.toString(shopPush.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></i>
                            <i th:text="${shopPush.payment eq '100' or shopPush.payment eq '50' or shopPush.payment eq '110' ? '元/年':'元/月'}" style="margin-right: 15px;"></i>
                        </s>
                        <th:block th:text="${#strings.isEmpty(shopPush.price)or  shopPush.price eq '0.00' or #strings.toString(shopPush.unitPrice) eq '0元/m²·天' or #strings.isEmpty(shopPush.unitPrice)?'':shopPush.unitPrice}"> </th:block>
                        <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">租</span>-->
                    </p>
<!--                    <p><b >1.77 元/m²·天</b></p>-->
                </div>
                <div class=""  th:if="${!#strings.isEmpty(shopPush.shopType) and  shopPush.shopType eq '3'}">
                    <p class="money">
                        <i class="money" th:if="${#strings.isEmpty(shopPush.price) or shopPush.price eq '0.00'}" th:text="${'面议'}"></i>
                        <s th:if="${!#strings.isEmpty(shopPush.price) && shopPush.price ne '0.00'}">
                            <i  th:text="${#strings.toString(shopPush.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></i>
                            <i th:text="${shopPush.payment eq '100' or shopPush.payment eq '50' or shopPush.payment eq '110' ? '元/年':'元/月'}"></i>
                        </s>
                        <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'? '元/年':'元/月')}"></th:block>-->
                        <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">兑</span>-->
                    </p>
                    <p><b th:text="${#strings.isEmpty(shopPush.price)or  shopPush.price eq '0.00' or #strings.toString(shopPush.unitPrice) eq '0元/m²·天' ?'':(#strings.indexOf(shopPush.unitPrice,'面议') eq -1 ?shopPush.unitPrice+'元':shopPush.unitPrice)}">1.77 元/m²·天</b></p>
                </div>
                <div  class="fourSpan">
                    <span th:text="${#strings.isEmpty(shopPush.area)? '': #numbers.formatInteger(shopPush.area,1)+'m²' }"></span>
                    <span th:text = "${#strings.isEmpty(shopPush.shopCategoriesName) ? '':shopPush.shopCategoriesName}"></span>
                </div>
                <p  class="houseAddress"  th:if="${!#strings.isEmpty(shopPush.regionName)  and !#strings.isEmpty(shopPush.plateName) and !#strings.isEmpty(shopPush.address)}">
                    <s>
                        <i th:if="${!#strings.isEmpty(shopPush.regionName)}" th:text="${shopPush.regionName}"></i>
                        <i th:if="${!#strings.isEmpty(shopPush.plateName)}" th:text="${'-'+shopPush.plateName}"></i>
                        <i th:if="${!#strings.isEmpty(shopPush.address)}" th:text="${'-'+shopPush.address}"></i>
                    </s>
                </p>

                <p class="houseTese" th:text="${#strings.isEmpty(shopPush.describe)? '':'房源特色：'+shopPush.describe}">房源特色： 交通便利
            </div>
        </div>
        <div class="contentMain">
            <div class="contentCtn">
                <div class="my_xl" th:if="${!#lists.isEmpty(region)}">
                    <input name="hidden0" type="hidden" id="hidden0"  class="my_xl_input" th:each="region:${region}" th:if="${region.selected}" th:value="${region.id}"/>
                    <div class="my_xl_txt" id="zongjia"  th:each="region:${region}" th:if="${region.selected}"  th:text="${#strings.isEmpty(region.id) ? '区域' : region.name}">区域</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="region:${region}" th:href="${region.url}" ><li data-key="PriceBase" th:text="${region.name}" th:value="${region.id}">全部</li></a>
                    </ul>
                </div>
                <div class="my_xl" th:if="${!#lists.isEmpty(needs)}">
                    <input name="Hidden2" type="hidden" id="Hidden2" class="my_xl_input" th:each="n:${needs}" th:if="${n.selected}"  th:value="${n.id}" />
                    <div class="my_xl_txt" id="mianji" th:each="n:${needs}" th:if="${n.selected}" th:text="${#strings.isEmpty(n.id)? '供求':n.name}" >供求</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="n:${needs}" th:href="${n.url}"><li data-key="BuildArea"  th:text="${n.name}" th:value="${n.id}">全部</li></a>
                    </ul>
                </div>
                <div class="my_xl"  th:if="${!#lists.isEmpty(type)}">
                    <input name="Hidden1" type="hidden" id="Hidden1" class="my_xl_input"th:each="t:${type}" th:if="${t.selected}" th:value="${t.id}" />
                    <div class="my_xl_txt" id="huxing" th:each="t:${type}" th:if="${t.selected}" th:text="${#strings.isEmpty(t.id)?'类型':t.name}" >类型</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="t:${type}" th:href="${t.url}" > <li data-key="roomType"  th:text="${t.name}" th:value="${t.id}">全部</li></a>
                    </ul>
                </div>
                <div class="houseSearch">
                    <input type="text" id="xiaoquid" class="searchInput" th:value="${searchName}" autocomplete="off" placeholder="请输入关键字" >
                    <input name="button" id="bottonss" type="button" class="searchBtn" value="" >
                </div>
                <p onclick="qingchu()"><i></i>清空筛选条件</p>
                <!--<span>小二为你找到<i th:text="${msg}">34</i>个符合条件的房源</span>-->
            </div>
            <div class="cl"></div>
<!--                <div th:if="${#lists.isEmpty(shop)}" class="warning">-->
<!--                    <p>-->
<!--                        很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>-->
<!--                        <a th:href="@{/shops}" target="_blank"> 查找相似房源>></a>-->
<!--                    </p>-->
<!--                </div>-->
            <div th:if="${#lists.isEmpty(shop)}" class="no-box">
                <img class="no-house" src="https://static.fangxiaoer.com/web/images/sy/house/no-house.png">
                <div class="no-text">暂无房源</div>
            </div>

            <div class="inf" th:if="${!#lists.isEmpty(shop)}" th:each = "shop : ${shop}">
                <a th:href="${#strings.isEmpty(shop.shopId)? '': '/shop/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
<!--                    <i class='imgNum' th:if="${shop.picNum ne null and #strings.toString(shop.picNum).length() gt 1 or shop.picNum gt '4'}" th:text="${#strings.isEmpty(shop.picNum)? '':shop.picNum}">6</i>-->
                    <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : shop.pic}" th:alt="${#strings.isEmpty(shop.title) ? '': shop.title}">
                    <!--VR and 视频都存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                        <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                        <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                    </s>
                    <!--VR存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                        <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                    </s>
                    <!--视频存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                        <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                    </s>
                </a>
                &nbsp;&nbsp;&nbsp;&nbsp;<div class="infCtn">
                <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)? '': '/shop/'+shop.shopId+'.htm'}" th:text="${#strings.isEmpty(shop.title) ? '': shop.title}">（出售）金地悦峰.金悦街 首府新区陵东板块S8#3门</a>
                <div  class="fourSpan">
                    <span th:text="${#strings.isEmpty(shop.area)? '': #numbers.formatInteger(shop.area,1)+'m²' }"></span>
                    <span th:text = "${#strings.isEmpty(shop.shopCategoriesName) ? '':shop.shopCategoriesName}"></span>
                </div>
                <p  class="houseAddress"  th:if="${!#strings.isEmpty(shop.regionName)  and !#strings.isEmpty(shop.plateName)}">
                    <s>
                        <i th:if="${!#strings.isEmpty(shop.regionName)}" th:text="${shop.regionName}"></i>
                        <i th:if="${!#strings.isEmpty(shop.plateName)}" th:text="${'-'+shop.plateName}"></i>
                        <i th:if="${!#strings.isEmpty(shop.address)}" th:text="${'-'+shop.address}"></i>
                    </s>
                </p>
                <div  class="houseItemIcon">
                    <span th:if="${!#strings.isEmpty(shop.houseTrait) and oc.count lt 4}" th:each="establish,oc:${#strings.setSplit(shop.houseTrait,',') }" th:text="${#strings.isEmpty(establish)? '':establish}" th:class="${'tese_'+oc.count}"></span>
                </div>

                <p class="person" th:text = "${#strings.isEmpty(shop.spantime)? '' : shop.spantime+'更新'}">
                    22天前更新
                </p>
            </div>
                <div class="infRight" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}">
                    <p class="infRightPrise" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}"  >
                        <b style="font-weight: bold"><th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议': (#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block></b>
                        <i th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00'?'':'万'}"></i>
                    </p>

                    <p><i><b th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'':shop.unitPrice}">13007元/m²</b></i></p>
                </div>

                <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '2'}">
                    <p class="infRightPrise">
                        <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月')}"></th:block>-->
                        <b  style="font-weight: bold" th:if="${#strings.isEmpty(shop.price) or shop.price eq '0.00'}" th:text="${'面议'}"></b>
                        <s th:if="${!#strings.isEmpty(shop.price) && shop.price ne '0.00'}">
<!--                            <b  style="font-weight: bold"  th:text="${#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></b>-->
                            <b style="font-weight: bold;"><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}" ></th:block></b>
                            <i th:text="${shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月'}"></i>
                        </s>
                        <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">租</span>-->
                    </p>
                    <p><b th:text="${#strings.isEmpty(shop.price)or  shop.price eq '0.00' or #strings.toString(shop.unitPrice) eq '0元/m²·天' or #strings.isEmpty(shop.unitPrice)?'':shop.unitPrice}">1.77 元/m²·天</b></p>
                </div>
                <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '3'}">
                    <p class="infRightPrise">
                        <b style="font-weight: bold" th:if="${#strings.isEmpty(shop.price) or shop.price eq '0.00'}" th:text="${'面议'}"></b>
                        <s th:if="${!#strings.isEmpty(shop.price) && shop.price ne '0.00'}">
<!--                            <b  style="font-weight: bold"  th:text="${#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></b>-->
                            <b style="font-weight: bold;"><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}" ></th:block></b>
                            <i th:text="${shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月'}"></i>
                        </s>
                        <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'? '元/年':'元/月')}"></th:block>-->
                        <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">兑</span>-->
                    </p>
                    <p><b th:text="${#strings.isEmpty(shop.price)or  shop.price eq '0.00' or #strings.toString(shop.unitPrice) eq '0元/m²·天' ?'':(#strings.indexOf(shop.unitPrice,'面议') eq -1 ?shop.unitPrice+'元':shop.unitPrice)}">1.77 元/m²·天</b></p>
                </div>
            </div>
            <!--分页-->
            <div class="page">
                <div id="Pager1">
                    <div th:include="fragment/page :: page "></div>
                </div>
            </div>
            <div ></div>
        </div>
        <div style="float: right; width: 242px;">
        <div class="contentRight">
            <h2>服务信息</h2>
            <!--所属机构-->
<!--            <div th:include = "agent/fragmentinter::affiliate"></div>-->



            <div class="box" th:if="${agentData.intermediaryState eq '1'}">
                <p class="jjr_6">服务公司</p>
                <a target="_blank" th:href="${'/agentIntermediary/second/'+agentData.id}"><span  class="com-text" th:if="${!#strings.isEmpty(agentData.intermediaryName)}" th:text="${agentData.intermediaryName}"></span>
                </a>


            </div>
            <div class="new-box">
                <img class="in-img"  src="https://static.fangxiaoer.com/web/images/sy/sale/institution.png" />
                <div class="in-right">
                    <p class="jjr_7">中介机构</p>
                    <span class="in-text"  th:if="${!#strings.isEmpty(agentData.agentIntermediaryAlias)}" th:text="${agentData.agentIntermediaryAlias}"></span>
                </div>

            </div>
            <div class="box">
                <p class="jjr_3">主营板块</p>
                <span th:if="${!#strings.isEmpty(plateName1)}" th:text="${plateName1}"></span>
                <span th:if="${!#strings.isEmpty(plateName2)}" th:text="${plateName2}"></span>
                <span th:if="${!#strings.isEmpty(plateName3)}" th:text="${plateName3}"></span>
            </div>

        </div>
<!--        <div class="agent-grjj" th:if="${!#strings.isEmpty(agentInfo.personalIntroduction)}">-->
<!--            <h2>个人简介</h2>-->
<!--            <p class="agent-grjjP"  id="content" th:text="${agentInfo.personalIntroduction}"></p>-->
<!--        </div>-->
        </div>
    </div>

</div>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
<!--个人介绍点击展开/收起-->
<script>
    function init(){
        var len = 51;
        var ctn = document.getElementById("content");
        var content = ctn.innerHTML;
        //alert(content);
        var span = document.createElement("span");
        var a = document.createElement("a");
        span.innerHTML = content.substring(0,len);
        a.innerHTML = content.length>len?"... [详细]":"";
        a.href = "javascript:void(0)";
        a.onclick = function(){
            if(a.innerHTML.indexOf("[详细]")>0){
                a.innerHTML = "<<&nbsp;[收起]";
                span.innerHTML = content;
            }else{
                a.innerHTML = "... [详细]";
                span.innerHTML = content.substring(0,len);
            }
        }
        ctn.innerHTML = "";
        ctn.appendChild(span);
        ctn.appendChild(a);
    }
</script>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>


</body>
</html>
