<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

	<head>
		<title>房产经纪人_沈阳房产经纪人 _找经纪人  -房小二网 </title>
		<meta charset="UTF-8">
		<meta name="keywords" th:content="${'房产经纪人,沈阳房产经纪人,不动产经纪人,不动产'}"/>
		<meta name="description"
			  th:content="'房小二网为您提供沈阳房产经纪人列表，来自各大平台与中介的房产经纪人联系方式、所属门店、主营范围、联系方式一目了然，买好房，就上房小二网'"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleHoseView2018.css?t=20180423"/>
		<meta http-equiv="Content-Type" content="text/html;charset=utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
		<meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/jjrsf/'+agentId+'-3.htm'}">
		<link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
		<link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
		<script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
		<script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
		<script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>

	</head>

	<body>
	<style>
		/*搜索框*/
		.searchEsfMap{
			width:  1170px;
			margin: 0px auto 0 auto;
			overflow: hidden;
			position:  relative;
		}
		.searchEsfMap .searchMapInput {
			width: 574px;
			padding: 0;
			float: left;
			border: 1px solid #eaeaea;
			border-radius: 2px 0 0 2px;
			padding-left: 10px;
			outline: none;
			box-shadow: none;
			font-size: 14px;
			height: 44px;
			margin-bottom: 0;
		}

		.searchEsfMap .searchMapInput:focus {
			outline: none !important;
			border-color: #ededed !important;
			box-shadow: inset 0 1px 1px rgba(0,0,0,0.075), 0 0 8px rgba(82,168,236,0) !important;
			border: 1px solid #ededed;
		}
		.searchEsfMap .searchMapBtn {
			width: 99px;
			height: 42px;
			line-height: 42px;
			float: left;
			background: #ff7200;
			border: none;
			color: #fff;
			margin-left: -101px;
			font-size: 16px;
			cursor: pointer;
			margin-top: 2px;
		}

		#deleteButton{
			position: absolute;
			top: 14px;
			left: 450px;
			cursor: pointer;
			display: none;
		}
		.crumbs {
			padding-top: 0;
		}
		/*风云榜成员样式*/
		.jiangbei{
			width: 19px !important;
			height: 19px !important;
			margin-top: 3px;

			margin-left:-6px;
		}

	</style>
	<!--页头-->
	<div id="head2017" th:include = "fragment/fragment::firstNav"  th:with="firstNavIndex=3"></div>
	<!--搜索-->
	<!--<div id="search2017"  th:include = "fragment/fragment::searchNav" th:with="type=2"></div>-->

	<!--搜索框-->
	<div   style="background:#f7f8fc;padding: 10px;">
		<div class="searchEsfMap">
			<input type="text" id="searchNames" placeholder="请输入姓名、手机号、中介名称查找经纪人" class="searchMapInput" th:value="${search}"/>
			<input  type="button" class="searchMapBtn" value="搜 索" >
			<img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png" id="deleteButton" alt="x" style="display: none !important;">
		</div>
	</div>
	<div class="crumbs">您的位置：<a href="/">沈阳房产网</a> &gt; <a href="/saleHouses/">沈阳二手房</a> &gt; <a href="/findAgent/">找经纪人</a></div>


	<div id="option">
		<ul>
			<li ><p>区域：</p>
				<div><a th:each="r:${region}" th:href="${r.url}"  onclick="showIndex(this)" th:text="${#strings.toString(r.name).replaceAll('全部','不限')}" th:id="'r'+${r.id} " th:class="${r.selected}?'hover':''"></a></div>
			</li>
			<!--<li ><p>公司：</p>
				<div><a th:each="b:${brand}" th:href="${b.url}"  onclick="showIndex(this)" th:text="${#strings.toString(b.name).replaceAll('全部','不限')}" th:id="'b'+${b.id} " th:class="${b.selected}?'hover':''"></a></div>
			</li>-->
		</ul>
	</div>
	<div class="w">
		<div class="findAgent">
			<ul th:if="${!#lists.isEmpty(agentList)}">
				<li th:each="a:${agentList}">
					<a th:href="${a.hasHouseFlag eq '1' ? '/agent/second/'+a.memberId : (a.hasHouseFlag eq '2' ? '/agent/rents/'+a.memberId : (a.hasHouseFlag eq '3' ? '/agent/shops/'+a.memberId : ('/agent/office/'+a.memberId )))}" class="agentImge" target="_blank"><img th:src="${a.pic}" alt="" /></a>
					<div>
						<div>
							<a th:href="${a.hasHouseFlag eq '1' ? '/agent/second/'+a.memberId : (a.hasHouseFlag eq '2' ? '/agent/rents/'+a.memberId : (a.hasHouseFlag eq '3' ? '/agent/shops/'+a.memberId : ('/agent/office/'+a.memberId )))}" target="_blank" style="border-right:none;" ><h4 th:text="${a.realName}">张雨生</h4></a>
							<s th:class="${'icon-xy level-icon'+a.level}" style="float: left;">
								<th:block th:text="${'LV'+a.stars}"></th:block>
							</s>
							<a th:if="${!#strings.isEmpty(a.memberTop) and a.memberTop ne '0'}" href="/memberTopList"><img class="jiangbei" src="https://static.fangxiaoer.com/web/images/sy/prominent/jiangbei.png"></a>
						</div>
						<p th:if="${!#strings.isEmpty(a.brandIntermediary) || !#strings.isEmpty(a.intermediaryName)}">所属门店：<span th:text="${#strings.isEmpty(a.brandIntermediary) ? '' : a.brandIntermediary}">喜鹊不动产 / 喜鹊不动产国瑞城店</span> </p>
						<p th:if="${!#lists.isEmpty(a.subName)}">主营小区：<a th:each="s:${a.subName}" th:href="${'/saleVillages/'+s.subId+'/index.htm'}" target="_blank" th:text="${s.name}">巴塞罗那</a> </p>
						<p th:if="${a.regionName}">主营区域：<span th:text="${a.regionName}"> 和平区</span> </p>
					</div>
					<span><i class="organePhone"></i><th:block th:text="${#strings.isEmpty(a.sortTel) ? '' : a.sortTel}"></th:block></span>
				</li>

			</ul>
			<img th:if="${#lists.isEmpty(agentList)}" src="https://static.fangxiaoer.com/web/images/sy/sale/agentListNoren.jpg"/>
		</div>
	</div>
		<div class="cl"></div>
	<div class="page">

		<!-- AspNetPager V7.2 for VS2005 & VS2008  Copyright:2003-2008 Webdiyer (www.webdiyer.com) -->
		<div th:include="fragment/page :: page"></div>
		<!-- AspNetPager V7.2 for VS2005 & VS2008 End -->

	</div>
	<div class="cl"></div>
	<!--底部1-->
	<div th:include="fragment/fragment:: footer_list"></div>
	<div th:include="fragment/fragment::tongji"></div>
	<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
	</body>
	<script>
		$(function () {
			$(".searchMapBtn").click(function(){
				var searchNames = $("#searchNames").val();
				searchNames=stripscript(searchNames);
				var search ="" ;
				if (searchNames == null || searchNames == undefined || searchNames == '') {
					search = "";
				}else {
					search = "search="+searchNames;
				}

				var newProjectUlr = window.location.pathname;
				if(newProjectUlr.indexOf('findAgent/') == -1){
					newProjectUlr +='/';
				}
				var seacIndex =  newProjectUlr.indexOf("search");
				if(seacIndex!=-1){
					newProjectUlr = newProjectUlr.substr(0,seacIndex);
				}else {
					newProjectUlr = newProjectUlr+'-';
				}
				url = newProjectUlr+search;
				//先筛区域，再输入关键字，以搜索框条件筛选
				sousuoUrl = "/findAgent/"+search;
				window.location.href = sousuoUrl;
			});

			function stripscript(s)
			{
				var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
				var rs = "";
				for (var i = 0; i < s.length; i++) {
					rs = rs+s.substr(i, 1).replace(pattern, '');
				}
				return rs;
			}

			//此方法用于搜索框无下拉时enter事件
			$("#searchNames").keydown(function (event) {
				if (event.keyCode == 13) {
					var selecetFlag = true;
					/*  $(".ui-autocomplete").each(function () {
					 if($(this).css("display") == "block"){
					 var item = $(this);
					 $(item).find("a").each (function () {
					 if($(this).hasClass("ui-state-focus")){
					 selecetFlag = false;
					 return false;
					 }
					 });
					 }
					 });*/
					if(selecetFlag){
						$(".searchMapBtn").click();
					}
				}
			});

			// 点击职位，清空搜索框
		/*	$(".jopTypeLi a").click(function () {
				$("#searchNames").val("");
			});*/


			$(".searchMapInput").val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
			$(".searchMapInput").keyup(function(){
				$(this).val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
			})

			$("#deleteButton").click(function () {
				var redirectUrl =location.pathname;
				if( redirectUrl != null || redirectUrl != undefined || redirectUrl != ''){
					if( redirectUrl.indexOf("search") != -1 ){
						redirectUrl =  redirectUrl.replace(/[-]*(search).*$/,'');
						window.location.href = redirectUrl;
					}else {
						$(".searchMapInput").val("");
						$("#deleteButton").hide();
					}
				}
			});
		});
	</script>
</html>