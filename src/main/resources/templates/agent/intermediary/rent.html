<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${companyList?.company_name+'_租房 – 房小二网'}"></title>
    <meta name="keywords" th:content="${companyList?.company_name +','+companyList?.company_name+'房源,沈阳租房,房小二网租房'}"/>
    <meta name="description" th:content="${'房小二网为你提供'+companyList?.company_name+'租房列表，详细信息，联系方式与海量真实房源。买房卖房，就上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/agentcompany/'+companyId+'-2'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20200623">
<!--    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/agentAgency.css?v=20190712">-->
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/agent/agentStore.css?v=20210409">
    <!--<link href="/css/agentStore.css" type="text/css" rel="stylesheet" />-->
    	
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

</head>
<style>
    /*无房源*/
    .warning{margin: 20px 0;padding: 20px 0 20px 195px;background: #fffbf6;border: 1px solid #f5dcbc;}
    .warning p{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left; font-size:14px; line-height:24px; font-weight:bold;color:#333;width: 610px;}
    .warning a{color:#0085ff}
    .warning a:hover{color:#900}
</style>
<body>
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"></div>
<!--面包屑-->
<div th:include = "agent/fragmentinter::intermediaryCard"></div>
<!--中介公司信息-->
<div class="head_info" th:include = "agent/fragmentinter::intermediaryInfo"></div>
<!--中介管理首页-->
<div class="head_shadow" th:include = "agent/fragmentinter::agentIntermeHead2" th:with="intermediaryType=2"></div>
<div id="option">
    <ul>
        <li class="fenlei">
            <p>区域：</p>
            <!--区域-->
            <div id="Search_zf" class="leibie">
                <a th:each="r:${region}" th:href="${r.url}" th:text="${#strings.toString(r.name).replaceAll('全部','不限')}" th:id="'r'+${r.id}" th:class="${r.selected}? 'hover':''"></a>
            </div>
        </li>
        <li th:if="${plate}" class="leibie">
            <a th:each="j:${plate}" th:text="${#strings.toString(j.name).replaceAll('全部','不限')}"
               onclick="showIndex(this)" th:href="${j.url}" th:id="'j'+${j.id}"
               th:class="${j.selected}? 'hover':''"></a>
        </li>
        <li>
            <p>租金：</p>
            <a th:each="p:${price}" th:href="${p.url}" th:text="${#strings.toString(p.name).replaceAll('全部','不限')}" th:id="'p'+${p.id}" th:class="${p.selected}? 'hover':''">></a>
        </li>
        <script>
            $(function () {
                //户型和厅室的处理
                var url_b = location.pathname;
                var num_b = url_b.indexOf("w2");
                if (num_b == -1) {
                    $("#Search_room").show();
                    $("#Search_Bedroom").hide();
                }else {
                    $("#Search_room").hide();
                    $("#Search_Bedroom").show();
                }

            })
        </script>
        <li id="Search_RentType">
            <p>方式：</p>
            <a th:each="w:${way}" th:href="${w.url}"  th:id="'w'+${w.id}" th:class="${w.selected}? 'hover':''">
                <th:block th:text="${#strings.toString(w.name).replaceAll('全部','不限')}"></th:block>
            </a>
        </li>
        <li id="Search_room">
            <p>户型：</p>
            <a th:each="l:${layout}" th:href="${l.url}" th:text="${#strings.toString(l.name).replaceAll('全部','不限')}" th:id="'l'+${l.id}" th:class="${l.selected}? 'hover':''">></a>
        </li>
       <!-- <li id="Search_Bedroom">
            <p>厅室：</p>
            <a th:each="b:${bedroom}" th:href="${b.url}" th:text="${#strings.toString(b.name).replaceAll('全部','不限')}" th:id="'b'+${b.id}" th:class="${b.selected}? 'hover':''">></a>
        </li>-->
    </ul>
</div>
<div class="shopManageCont w1170Mar">
    <div class="shopManageList">
        <div class="cl"></div>
        <div th:if="${#lists.isEmpty(companyRentList)}" class="warning">
            <p>
                很抱歉，暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                <!--<a th:href="@{/rents}" target="_blank"> 查找相似房源>></a>-->
            </p>
        </div>
        <!--租房-->
        <div class="rentList" th:if="${!#lists.isEmpty(companyRentList)}">
            <ul>
                <li class="inf" th:if="${!#lists.isEmpty(companyRentList)}" th:each="rent:${companyRentList}">
                    <a th:href="${#strings.isEmpty(rent.houseId)?'':'/rent/'+rent.houseId+'.htm'}" target="_blank" class="infLeft">
                        <img th:src="${#strings.isEmpty(rent.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': rent.pic}" th:alt="${#strings.isEmpty(rent.title)? '':rent.title}">
                        <!--VR and 视频都存在 -->
                        <s class="listIconK" th:if="${#strings.toString(rent.PanID) ne null and #strings.toString(rent.mediaID) ne null }">
                            <s class="vrListIcon" th:if="${#strings.toString(rent.PanID) ne null}"></s>
                            <s class="videoListIcon" th:if="${rent.mediaID ne null}"></s>
                        </s>
                        <!--VR存在 -->
                        <s class="listIconK" th:if="${#strings.toString(rent.PanID) ne null and #strings.toString(rent.mediaID) eq null }">
                            <s class="vrListIcon" th:if="${#strings.toString(rent.PanID) ne null}"></s>
                        </s>
                        <!--视频存在 -->
                        <s class="listIconK" th:if="${#strings.toString(rent.PanID) eq null and #strings.toString(rent.mediaID) ne null }">
                            <s class="videoListIcon" th:if="${rent.mediaID ne null}"></s>
                        </s>
                    </a>
                    &nbsp;&nbsp;&nbsp;<div class="infCtn">
                    <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(rent.houseId) ? '':'/rent/'+rent.houseId +'.htm' }">
                        <div  th:text="${#strings.isEmpty(rent.title)? '':rent.title}"></div>
                        <i class="listIconBidPrice" th:if="${#strings.toString(rent.auction) eq '1'}"></i>
                        <i class="listIconIstop" th:if="${#strings.toString(rent.auction) ne '1' && #strings.toString(rent.stickOrder) eq '-1'}"></i>
                    </a>

                    <div  class="fourSpan">
                        <span th:text="${#strings.isEmpty(rent.rentTypeName)? '':rent.rentTypeName}"></span>
                        <span th:text="${(#strings.isEmpty(rent.room)?'':rent.room+'室')+(#strings.isEmpty(rent.hall)?'':rent.hall+'厅')+(#strings.isEmpty(rent.toilet)?'':rent.toilet+'卫')}"></span>
                        <span th:text="${#strings.isEmpty(rent.area)? '':(#strings.indexOf(rent.area,'.') eq -1 ? rent.area:#strings.toString(rent.area).replaceAll('0+?$','').replaceAll('[.]$',''))+'m²'}"></span>
                        <span th:text="${#strings.isEmpty(rent.floor) and #strings.isEmpty(rent.totalFloorNumber) ? '':rent.floorDesc +'/'+rent.totalFloorNumber+'层' }"></span>
                        <span th:text="${#strings.isEmpty(rent.forwardTypeName) ? '':rent.forwardTypeName }"></span>
                    </div>
                    <p  class="houseAddress"  th:if="${!#strings.isEmpty(rent.regionName) and !#strings.isEmpty(rent.subName) and !#strings.isEmpty(rent.plateName) and !#strings.isEmpty(rent.address)}">
                        <s th:if="${ !#strings.isEmpty(rent.subName)}" class="houseAddressSpance">
                            <a th:href="${'/saleVillages/'+rent.subId+'/index.htm'}" target='_blank' th:text="${rent.subName}"></a>
                        </s>
                        <s th:if="${!#strings.isEmpty(rent.regionName) and !#strings.isEmpty(rent.plateName) and !#strings.isEmpty(rent.address)}">
                            <i th:if="${!#strings.isEmpty(rent.regionName)}"  th:text="${rent.regionName}"></i>-
                            <i th:if="${!#strings.isEmpty(rent.plateName)}" th:text="${rent.plateName}"></i>-
                            <i th:if="${!#strings.isEmpty(rent.address)}" th:text="${rent.address}"></i>
                        </s>
                    </p>
                    <div  class="houseItemIcon">
                        <s th:if="${rent.houseTrait ne null and rent.houseTrait ne ''}">
                            <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(rent.houseTrait).split(',')}" th:if="${i.count le 3}" th:text="${item}" ></span>
                        </s>
                    </div>
                    <span style="display: none; background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale/iconAuthentichouse.gif) no-repeat 9px 3px; padding-left: 76px; font-size: 16px; color: #E04545; position: absolute; left: 206px; bottom: 2px;"></span>
                    <!--<a class="checkHouse" target="_blank"  th:href="${#strings.isEmpty(rent.subId)? '': '/rents/-v'+rent.subId}">查看同小区房源 &gt;</a>-->
                </div>
                    <div class="infRight">
                        <p class="infRightPriseM" th:if="${#strings.isEmpty(rent.price) or (#strings.toString(rent.price) eq '0')}"  th:text="${'面议'}" style="font-weight: bold"></p>
                        <p class="infRightPrise" th:if= "${!#strings.isEmpty(rent.price) and #strings.toString(rent.price) ne '0'}" >
                            <b style="font-weight: bold" th:text="${#strings.toString(rent.price).contains('.')? #strings.toString(rent.price).replaceAll('0+?$','').replaceAll('[.]$', '') : rent.price}"></b>
                            <i th:text="${#strings.isEmpty(rent.price) or (#strings.toString(rent.price) eq '0')?'':'元/月'}"></i>
                        </p>
                    </div>
                </li>
            </ul>
            <div class="cl"></div>
            <div class="page">
            <div th:include="fragment/page :: page"></div>
        </div>

    </div>
</div>
    <!--右侧内容-->
    <div class="shopManageRight">
        <!--门店地址-->
        <div th:include = "agent/fragmentinter::storeOtherAddress"></div>
        <!--推荐经纪人-->
        <!--<div th:include = "agent/fragmentinter::companyAgent"></div>-->
        <!--岗位急聘-->
        <div th:include = "agent/fragmentinter::companyUrgent"></div>
    </div>
</div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>