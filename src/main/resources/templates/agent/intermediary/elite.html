<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'精英顾问_'+companyList?.company_name+'–房小二网'}"></title>
    <meta name="keywords" th:content="${'房产经纪人，沈阳房产中介，'+companyList?.company_name}"/>
    <meta name="description" th:content="${'房小二网为您提供沈阳'+companyList?.company_name+'房产经纪人列表，经纪人主营范围、联系方式一目了然，买好房，就上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/intermediary/'+companyId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20200623">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/agent/agentStore.css?v=20210409">
    	<!--<link href="/css/agentStore.css" type="text/css" rel="stylesheet" />-->
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
</head>
<style>
    .elitePage {
        overflow: initial;
    }
</style>
<body>
<!--页头-->
<!--bug 19917-->
<div id="head2017" th:include = "fragment/fragment::firstNav" th:with="firstNavIndex=3"></div>
<!--面包屑-->
<div th:include = "agent/fragmentinter::intermediaryCard"></div>
<!--中介公司信息-->
<div class="head_info" th:include = "agent/fragmentinter::intermediaryInfo"></div>
<!--切换导航-->
<div class="head_shadow" th:include = "agent/fragmentinter::agentIntermeHead" th:with="intermediaryType=66"></div>
<ul class="elitePage w1170Mar">
    <li th:each="eliteList,i:${eliteList}">
        <a class="eliteImg" th:href="${eliteList.hasHouseFlag eq '1' ? '/agent/second/'+eliteList.memberId : (eliteList.hasHouseFlag eq '2' ? '/agent/rents/'+eliteList.memberId : (eliteList.hasHouseFlag eq '3' ? '/agent/shops/'+eliteList.memberId : ('/agent/office/'+eliteList.memberId )))}"  target="_blank">
            <img th:src="${#strings.isEmpty(eliteList.pic)? 'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':eliteList.pic}" alt="">
        </a>
        <div class="ren">
            <a class="name" th:href="${eliteList.hasHouseFlag eq '1' ? '/agent/second/'+eliteList.memberId : (eliteList.hasHouseFlag eq '2' ? '/agent/rents/'+eliteList.memberId : (eliteList.hasHouseFlag eq '3' ? '/agent/shops/'+eliteList.memberId : ('/agent/office/'+eliteList.memberId )))}"  target="_blank">
                <h4 ><span><th:block th:text="${eliteList.realName}"></th:block></span><i th:class="${'gra'+eliteList.level}"><th:block th:text="${'LV'+eliteList.stars}"></th:block></i></h4>
            </a>
            <!--微信扫码拨号-->
            <div class="chat_div">
            	<div class="agent-cardTels">
                	<span>电话咨询</span>
                	<div class="show-topCardTels">
	                    <img th:id="${'forcode'+ i.count}" src="" alt="">
	                    <p>微信扫码拨号</p>
	                    <p><i>快速获取经纪人联系方式</i></p>
                	</div>
            	</div>
            	  <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal" class="new-liaobeiBtn">
                    <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" >
                        <text>聊呗</text>
                    </div>
                </a>
                <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/imPolt/'+eliteList.Mobile}" target="_blank" class="new-liaobeiBtn">
                    <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)">
                        <text>聊呗</text>
                    </div>
                </a>
            </div>
            <div class="plateS" th:if="${!#strings.isEmpty(eliteList.plate1) || !#strings.isEmpty(eliteList.plate2) || !#strings.isEmpty(eliteList.plate3)}" >
            <span th:text="${'主营区域：'}"></span>
            <div>
                <s th:if="${!#strings.isEmpty(eliteList.plate1)}" th:text="${eliteList.plate1}"></s>
                <s th:if="${!#strings.isEmpty(eliteList.plate2)}" th:text="${eliteList.plate2}"></s>
                <s th:if="${!#strings.isEmpty(eliteList.plate3)}" th:text="${eliteList.plate3}"></s>
            </div>
        </div>
            
            <script>
                /*<![CDATA[*/
                $(function () {
                		$(".elitePage li").each(function(){
							this.onmouseenter=function(){
								$(this).find(".plateS").hide();
								$(this).find(".chat_div").show();
								
							}
							this.onmouseleave=function(){
								$(this).find(".plateS").show();
								$(this).find(".chat_div").hide();
							}
						})
                	
                    var i = [[${i.count}]]
                    var tels = [[${eliteList.Mobile}]];
                    var scene = 'tel,' +[[${eliteList.memberId}]]+'-1,6,' + tels;
                    var img = "";
                    var sss;
                    $.ajax({
                        type: "GET",
                        async: false,
                        url:  "/getWxACode",
                        data:{"scene": scene},
                        dataType : 'json',
                        headers : {
                            'Content-Type' : 'application/json;charset=utf-8'
                        },
                        success: function (data) {
                            img = data.img;
                            sss = data;
                        }
                    });
                    $("#forcode" + i).attr("src","data:text/html;base64,"+img);
                });

            </script>
              
        </div>
        
        <div class="jiangbei">
               		
        </div>
    </li>
    <div class="clearUl"></div>

</ul>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
<div th:include="house/detail/fragment_login::login"></div>
</body>
<script>
    function changeColor(e) {
        $(e).css("background-color","#188CDE")
    }
    function changeColor2(e) {
        $(e).css("background-color","#32a3f2")
    }

    $(document).ready(function () {
        /*var agentNum = $(".elitePage li").length;
        console.log(agentNum)*/
        var bodyHeight = document.body.clientHeight;
        var pageHeight = window.screen.availHeight;
        console.log(bodyHeight)
        console.log(pageHeight)
        if(bodyHeight>pageHeight){
            $(".elitePage").css("marginBottom","60px")
        }
    })
</script>

</html>