<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${companyList?.company_name+'_二手房 – 房小二网'}"></title>
    <meta name="keywords" th:content="${companyList?.company_name +','+companyList?.company_name+'房源,沈阳二手房,房小二网二手房'}"/>
    <meta name="description" th:content="${'房小二网为你提供'+companyList?.company_name+'二手房列表，详细信息，联系方式与海量真实房源。买房卖房，就上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/intermediary/'+companyId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20200623" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css?v=20190425" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/agentAgency.css?v=20190712">

</head>
<body>
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"></div>
<!--面包屑-->
<div th:include = "agent/fragmentinter::intermediaryCard"></div>
<!--中介公司信息-->
<div th:include = "agent/fragmentinter::intermediaryInfo"></div>
<!--中介管理首页-->
<div th:include = "agent/fragmentinter::agentIntermeHead2" th:with="intermediaryType=5"></div>
<div class="shopManageCont w1170Mar">
    <div class="agentStore">
        <div class="storeContent" th:if="${!#lists.isEmpty(companyList)}">
            <div class="storeImg">
                <img th:src="${#strings.toString(companyList.companyLogo) ne null and #strings.toString(companyList.companyLogo) ne ''?companyList.companyLogo:'https://static.fangxiaoer.com/web/images/m/shopLogoImgNo.jpg'}" alt="">
            </div>
            <div class="storecontR">
                <h4  th:if="${!#strings.isEmpty(companyList.company_name)}" th:text="${companyList.company_name}">恒鑫地产—建筑大学</h4>
                <p th:if="${!#strings.isEmpty(companyList.plateName1) || !#strings.isEmpty(companyList.plateName2) || !#strings.isEmpty(companyList.plateName3)}">
                    <i>主营板块：</i>
                    <span th:if="${!#strings.isEmpty(companyList.plateName1)}" th:text="${companyList.plateName1}">奥体中心</span>
                    <span th:if="${!#strings.isEmpty(companyList.plateName2)}" th:text="${companyList.plateName2}">奥体中心</span>
                    <span th:if="${!#strings.isEmpty(companyList.plateName3)}" th:text="${companyList.plateName3}">奥体中心</span>
                </p>
                <p th:if="${!#strings.isEmpty(companyList.other3) || !#strings.isEmpty(companyList.other4) || !#strings.isEmpty(companyList.other5)}">
                    <i>主营小区：</i>
                    <span th:if="${!#strings.isEmpty(companyList.other3)}" th:text="${companyList.other3}">碧桂园公园里</span>
                    <span th:if="${!#strings.isEmpty(companyList.other4)}" th:text="${companyList.other4}">碧桂园公园里</span>
                    <span th:if="${!#strings.isEmpty(companyList.other5)}" th:text="${companyList.other5}">碧桂园公园里</span>
                </p>
            </div>
        </div>
        <div class="store_introduce" th:if="${!#strings.isEmpty(companyList.company_desc)}">
            <h4>门店介绍</h4>
            <div th:utext="${companyList.company_desc}"></div>
        </div>
        <ul class="storeAgentList" th:if="${!#lists.isEmpty(eliteList)}">
            <li th:each="eliteList,i:${eliteList}">
                <!--<a th:href="${'/agent/second/'+eliteList.memberId}" target="_blank">-->
                    <a th:href="${eliteList.hasHouseFlag eq '1' ? '/agent/second/'+eliteList.memberId : (eliteList.hasHouseFlag eq '2' ? '/agent/rents/'+eliteList.memberId : (eliteList.hasHouseFlag eq '3' ? '/agent/shops/'+eliteList.memberId : ('/agent/office/'+eliteList.memberId )))}"  target="_blank">
                    <img th:src="${#strings.isEmpty(eliteList.pic)? 'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':eliteList.pic}" alt="">
                    <h5><span><th:block th:text="${eliteList.realName}"></th:block></span><i th:class="${'gra'+eliteList.level}"><th:block th:text="${'LV'+eliteList.stars}"></th:block></i></h5>
                    <p th:text="${eliteList.Mobile}">15802456600</p>
                    <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal" class="liaobeiBtn">
                        <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" >
                            <text>聊呗</text>
                        </div>
                    </a>
                    <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/imPolt/'+eliteList.Mobile}" target="_blank" class="liaobeiBtn">
                        <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)">
                            <text>聊呗</text>
                        </div>
                    </a>
                </a>
            </li>
        </ul>
        <div class="otherStore" th:if="${ !#strings.isEmpty(otherCompanyList) and #lists.size(otherCompanyList) ne 0}">
            <h4 th:text="${companyList.company_name+'其他店'}">恒鑫地产其他店</h4>
            <ul>
                <li th:each="other,i:${otherCompanyList}">
                    <i th:text="${'0'+i.count}"></i>
                    <div>
                        <a th:href="${#strings.isEmpty(other.company_id)? '': '/agent/intermediary/home/'+other.company_id}" >
                            <h5 th:if="${!#strings.isEmpty(other.company_name)}" th:text="${other.company_name}">恒鑫地产—建筑大学店</h5>
                            <p th:if="${!#strings.isEmpty(other.company_address)}" th:text="${other.company_address}">沈阳市浑南区长青南街17号-173门</p>
                        </a>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <!--右侧内容-->
    <div class="shopManageRight">
        <!--岗位急聘-->
        <div th:include = "agent/fragmentinter::companyUrgent"></div>
    </div>
</div>
<script>
    function changeColor(e) {
        $(e).css("background-color","#188CDE")
    }
    function changeColor2(e) {
        $(e).css("background-color","#32a3f2")
    }
</script>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
<div th:include="house/detail/fragment_login::login"></div>
</body>
</html>