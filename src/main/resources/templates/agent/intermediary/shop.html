<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${companyList?.company_name+'_商铺 – 房小二网'}"></title>
    <meta name="keywords" th:content="${companyList?.company_name +','+companyList?.company_name+'房源,沈阳商铺,房小二网商铺'}"/>
    <meta name="description" th:content="${'房小二网为你提供'+companyList?.company_name+'商铺列表，详细信息，联系方式与海量真实房源。买房卖房，就上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/agentcompany/'+companyId+'-3'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20200623">
<!--    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/agentAgency.css?v=20190712">-->
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/agent/agentStore.css?v=20210409">
    <!--<link href="/css/agentStore.css" type="text/css" rel="stylesheet" />-->
    	
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

</head>
<style>
    /*无房源*/
    .warning{margin: 20px 0;padding: 20px 0 20px 195px;background: #fffbf6;border: 1px solid #f5dcbc;}
    .warning p{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left; font-size:14px; line-height:24px; font-weight:bold;color:#333;width: 610px;}
    .warning a{color:#0085ff}
    .warning a:hover{color:#900}
    .shopList .infRight p b {font-weight: bold;  }
</style>
<body>
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"></div>
<!--面包屑-->
<div th:include = "agent/fragmentinter::intermediaryCard"></div>
<!--中介公司信息-->
<div class="head_info" th:include = "agent/fragmentinter::intermediaryInfo"></div>
<!--中介管理首页-->
<div class="head_shadow" th:include = "agent/fragmentinter::agentIntermeHead2" th:with="intermediaryType=3"></div>
<div class="shopManageCont w1170Mar">
    <div id="option">
        <ul>
            <!--供求-->
            <li><p>供求：</p>
                <a th:each="n,stat:${needs}" onclick="showIndex(this);" th:href="${n.url}" th:text="${stat.index == 0 ?'不限': n.name.replace('商业','商铺')}" th:id="${'b'+n.id}" th:class="${n.selected}? 'hover':''"></a>
            </li>
            <!--区域-->
            <li class=""><p>区域：</p>
                <a th:each="r,ri:${region}" th:href="${r.url}" th:id="'r'+${r.id}"  th:class="${r.selected}? 'hover':''">
                    <th:block  th:text="${ri.index == 0?'不限':r.name}"></th:block>
                    <i th:if="${!#strings.isEmpty(r.id)}"></i>
                </a><br>
            </li>
            <!--板块-->
            <li th:if="${plate}" id="Search_zf" class="leibie" style="display: block;"><p style="display: none">板块：</p>
                <a th:each="j,stat:${plate}" th:text="${stat.index ==0?'不限': j.name}" th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
            </li>
            <li  th:if="${#strings.isEmpty(typeId) or typeId eq '1'}" ><p>售价：</p>
                <a th:each="p,stat:${price}" th:href="${p.url}" onclick="showIndex(this);" th:text="${stat.index ==0?'不限': p.name}" th:id="'p'+${p.id} " th:class="${p.selected}?'hover':''">></a>
                <!--<div id="Search_PriceDomOk">
                    <label><input name="minPrice" id="minPrice" maxlength="4" type="text" th:value="${minPrice}" > - <input name="maxPrice" id="maxPrice" maxlength="4" type="text" th:value="${maxPrice}" > 万 <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button"></label>
                </div>-->
            </li>
            <li  th:if="${typeId ne '1'}"><p>租金：</p>
                <a th:each="a,stat:${rental}" th:href="${a.url}" onclick="showIndex(this);" th:text="${stat.index==0?'不限':a.name}" th:id="'p'+${a.id}" th:class="${a.selected}?'hover':''">></a>
            </li>
            <li ><p>面积：</p>
                <a th:each="a,stat:${area}" th:href="${a.url}" onclick="showIndex(this);" th:text="${stat.index==0?'不限':a.name}" th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
                <!--<div id="Search_BuildAreaDomOk">
                    <label><input name="minArea" id="minArea" maxlength="5" type="text" th:value="${minArea}"> - <input name="maxArea" id="maxArea" maxlength="5" type="text" th:value="${maxArea}"> ㎡ <input onclick="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button"></label>
                </div>-->
            </li>
            <li><p>类型：</p>
                <a th:each="n,stat:${type}" th:if="${stat.index != 5}" onclick="showIndex(this);" th:href="${n.url}" th:text="${stat.index == 0 ?'不限': n.name}" th:id="${'b'+n.id}" th:class="${n.selected}? 'hover':''"></a>
            </li>
        </ul>
    </div>
    <div class="shopManageList">
        <div class="cl"></div>
        <div th:if="${#lists.isEmpty(shop)}" class="warning">
            <p>
                很抱歉，暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                <!--<a th:href="@{/shops}" target="_blank"> 查找相似房源>></a>-->
            </p>
        </div>
        <!--商铺-->
        <div class="shopList" th:if="${!#lists.isEmpty(shop)}">
            <ul>
                <li class="inf" th:if="${!#lists.isEmpty(shop)}" th:each = "shop : ${shop}">
                    <a th:href="${#strings.isEmpty(shop.shopId)? '': '/shop/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
<!--                        <i class='imgNum' th:if="${shop.picNum ne null and #strings.toString(shop.picNum).length() gt 1 or shop.picNum gt '4'}" th:text="${#strings.isEmpty(shop.picNum)? '':shop.picNum}">6</i>-->
                        <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : shop.pic}" th:alt="${#strings.isEmpty(shop.title) ? '': shop.title}">
<!--                        <div class="" th:if="${#strings.toString(shop.isXiQue) eq '1'}">佣金95折</div>-->
                        <!--VR and 视频都存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                            <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                            <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                        </s>
                        <!--VR存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                            <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                        </s>
                        <!--视频存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                            <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                        </s>
                    </a>
                    &nbsp;&nbsp;&nbsp;&nbsp;<div class="infCtn">
                    <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)? '': '/shop/'+shop.shopId+'.htm'}" >
                        <div th:text="${#strings.isEmpty(shop.title) ? '': shop.title}"></div>
                        <i class="listIconBidPrice" th:if="${#strings.toString(shop.auction) eq '1'}"></i>
                        <i class="listIconIstop" th:if="${#strings.toString(shop.auction) ne '1' && shop.stickOrder eq '-1'}"></i>
                     </a>
                    <div  class="fourSpan">
                        <span th:text="${#strings.isEmpty(shop.area)? '': #numbers.formatInteger(shop.area,1)+'m²' }"></span>
                        <span th:text = "${#strings.isEmpty(shop.shopCategoriesName) ? '':shop.shopCategoriesName}"></span>
                    </div>
                    <p  class="houseAddress"  th:if="${!#strings.isEmpty(shop.regionName)  and !#strings.isEmpty(shop.plateName)}">
                        <s>
                            <i th:if="${!#strings.isEmpty(shop.regionName)}" th:text="${shop.regionName}"></i>
                            <i th:if="${!#strings.isEmpty(shop.plateName)}" th:text="${'-'+shop.plateName}"></i>
                            <i th:if="${!#strings.isEmpty(shop.address)}" th:text="${'-'+shop.address}"></i>
                        </s>
                    </p>
                    <div  class="houseItemIcon">
                        <span th:if="${!#strings.isEmpty(shop.houseTrait) and oc.count lt 4}" th:each="establish,oc:${#strings.setSplit(shop.houseTrait,',') }" th:text="${#strings.isEmpty(establish)? '':establish}" th:class="${'tese_'+oc.count}"></span>
                    </div>

                    <!--<p class="person" th:text = "${#strings.isEmpty(shop.spantime)? '' : shop.spantime+'更新'}">22天前更新</p>-->
                </div>
                    <div class="infRight" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}">
                        <!--<p class="infRightPrise">
                            <b th:text = "${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议': ((#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')))}"></b>
                            <i style="margin-left: 0">万</i>
                            &lt;!&ndash;<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">售</span>&ndash;&gt;
                        </p>-->
                        <p class="infRightPrise" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}"  >
                            <b><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议': (#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$',''))}"></th:block></b>
                            <i th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00'?'':'万'}"></i>
                        </p>
                        <p><i th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'':shop.unitPrice}"></i></p>
                    </div>

                    <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '2'}">
                        <p class="infRightPrise">
                            <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月')}"></th:block>-->
                            <b th:if="${#strings.isEmpty(shop.price) or shop.price eq '0.00'}" th:text="${'面议'}"></b>
                            <s th:if="${!#strings.isEmpty(shop.price) && shop.price ne '0.00'}">
<!--                                <b  th:text="${#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></b>-->
                                <b><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price :#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}"></th:block></b>
                                <i th:text="${shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月'}"></i>
                            </s>
                            <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">租</span>-->
                        </p>
                        <p><s th:text="${#strings.isEmpty(shop.price)or  shop.price eq '0.00' or #strings.toString(shop.unitPrice) eq '0元/m²·天' or #strings.isEmpty(shop.unitPrice)?'':shop.unitPrice}">1.77 元/m²·天</s></p>
                    </div>
                    <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '3'}">
                        <p class="infRightPrise">
                            <b th:if="${#strings.isEmpty(shop.price) or shop.price eq '0.00'}" th:text="${'面议'}"></b>
                            <s th:if="${!#strings.isEmpty(shop.price) && shop.price ne '0.00'}">
<!--                                <b  th:text="${#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','万')}"></b>-->
                                <b><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}" ></th:block></b>
                                <i th:text="${shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月'}"></i>
                            </s>
                            <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'? '元/年':'元/月')}"></th:block>-->
                            <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">兑</span>-->
                        </p>
                        <p><s th:text="${#strings.isEmpty(shop.price)or  shop.price eq '0.00' or #strings.toString(shop.unitPrice) eq '0元/m²·天' ?'':(#strings.indexOf(shop.unitPrice,'面议') eq -1 ?shop.unitPrice+'元':shop.unitPrice)}">1.77 元/m²·天</s></p>
                    </div>
                </li>
            </ul>
            <div class="cl"></div>
            <div class="page">
            <div th:include="fragment/page :: page"></div>
        </div>
    </div>

</div>
    <!--右侧内容-->
    <div class="shopManageRight">
        <!--门店地址-->
        <div th:include = "agent/fragmentinter::storeOtherAddress"></div>
        <!--推荐经纪人-->
        <!--<div th:include = "agent/fragmentinter::companyAgent"></div>-->
        <!--岗位急聘-->
        <div th:include = "agent/fragmentinter::companyUrgent"></div>
    </div>
</div>
<script type="text/javascript">
    //筛选的确定按钮显示隐藏
    function price(priceIdName) {
        if($("#"+priceIdName).length >0){
            $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
            $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
            var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
            var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
            if (num1 == "" && num2 != "") {
                $("#" + priceIdName + " input").eq(0).val("0");
                $("#" + priceIdName + " input").eq(2).show();
            } else if (num2 == "" && num1 != "") {
                $("#" + priceIdName + " input").eq(2).show();
            } else if (num1 != "" || num2 != "") {
                $("#" + priceIdName + " input").eq(2).show();
            } else {
                $("#" + priceIdName + " input").eq(2).hide();
            }
        }
    }

    price("Search_BuildAreaDomOk");
    price("Search_PriceDomOk");
    $("#Search_PriceDomOk input").keyup(function () {
        price("Search_PriceDomOk");
    })
    $("#Search_BuildAreaDomOk input").keyup(function () {
        price("Search_BuildAreaDomOk");
    })
    $("#Search_PriceDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            // $("#Search_Btn_Search1").click()
        }
    });
    $("#Search_BuildAreaDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            $("#Search_Btn_Searchs").click()
        }
    });
    //此段js用于手填面积和价格连接拼接处理
    function __doPostBack(pager1, page) {
        var url = window.location.pathname;
        if(url.lastIndexOf("/")) {
            url +='/';
        }
        if (pager1 == "Search$Btn_Search1") {
            var priceBegin = $("#minPrice").val();
            var priceEnd = $("#maxPrice").val();
            var ref1 = url.replace(/-lp[0-9]\d*/,''); //k最小值
            var ref2 = ref1.replace(/-hp[0-9]\d*/,'');  //x最大值
            if (parseInt(priceEnd) < parseInt(priceBegin)){
                priceEnd = [priceBegin,priceBegin=priceEnd][0];
            }
            if(priceBegin != "" && priceBegin != 0)
                ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-lp" + priceBegin;
            if(priceEnd != "")
                ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') +"-hp"+ priceEnd;
            location.href = ref2;
        }
        if (pager1 == "Search$Btn_Searchs") {
            var areaBegin = $("#minArea").val();
            var areaEnd = $("#maxArea").val();
            var ref1 = url.replace(/-la[0-9]\d*/,''); //y最小值
            var ref2 = ref1.replace(/-ha[0-9]\d*/,'');  //e最大值
            if (parseInt(areaEnd) < parseInt(areaBegin)){
                areaEnd = [areaBegin,areaBegin=areaEnd][0];
            }
            if(areaBegin != "" && areaBegin != 0)
                ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-la" + areaBegin;
            if(areaEnd != "")
                ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') +"-ha" + areaEnd;
            location.href = ref2;
        }
    }
</script>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>