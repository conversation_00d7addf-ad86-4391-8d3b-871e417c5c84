<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'招贤纳士_'+companyList?.company_name+'–房小二网'}"></title>
    <meta name="keywords" th:content="${companyList?.company_name +'，'+companyList?.company_name+'招聘，'+'房产招聘'}"/>
    <meta name="description" th:content="${companyList?.company_name+'招贤纳士，为您提供沈阳稀缺房产人才与招聘信息，找人才，就上房小二网！'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/intermediary/'+companyId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20200623">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/agent/agentStore.css?v=20210409">
    <!--<link href="/css/agentStore.css" type="text/css" rel="stylesheet" />-->
    	
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
</head>
<body>
<!--页头-->
<!--bug 19917-->
<div id="head2017" th:include = "fragment/fragment::firstNav" th:with="firstNavIndex=3"></div>
<!--面包屑-->
<div th:include = "agent/fragmentinter::intermediaryCard"></div>
<!--中介公司信息-->
<div class="head_info" th:include = "agent/fragmentinter::intermediaryInfo"></div>
<!--切换导航-->
<div class="head_shadow" th:include = "agent/fragmentinter::agentIntermeHead" th:with="intermediaryType=77"></div>
<!--招聘职位-->
<div class="agentRecruitList">
    <div class="arlLeft" th:if="${!#strings.isEmpty(jobList) && #lists.toList(jobList).size() ne 0}">
        <div class="arlLeftTitle">
            <p>招聘职位</p>
        </div>
        <ul>
            <li  th:each="recruit:${jobList}">
                <input type="hidden" th:value="${recruit.jobAdId}">
                <a th:href="${'/positionDetail'+'/'+recruit.jobAdId+'.htm'}" target="_blank">
                    <h4 class="otherJobH4">
                        <span class="otherJobH4Span otherJobH4Span1" th:text="${recruit.plateName}"></span> <i class="otherJobH4I">|</i> <span class="otherJobH4Span2" th:text="${recruit.jobTitle}"></span>
                        <!--聊呗-->
                        <div class="otherJobH4div" th:include="fragment/fragmentrecruit::liaobei"></div>
                    </h4>
                    <div class="otherJobLiL">
                        <div class="otherJobLiL-txt">
                            <div class="otherJobLiL-Prise" th:if="${#strings.toString(recruit.jobSalaryRangeMin) ne '0' && #strings.toString(recruit.jobSalaryRangeMax) ne '0'}" th:text="${recruit.jobSalaryRangeMin+'-'+recruit.jobSalaryRangeMax+'元'}">10000-12000元 </div>
                            <div class="otherJobLiL-Prise" th:if="${(#strings.isEmpty(recruit.jobSalaryRangeMin)  || #strings.toString(recruit.jobSalaryRangeMin) eq '0') && !#strings.isEmpty(recruit.jobSalaryRangeMax || #strings.toString(recruit.jobSalaryRangeMax) eq '0')}" th:text="${'面议'}"></div>
                            <p class="agentRecruitListp">
                                <!--2020-11-20 Bowen 改版为工作区域、工作经验要求、学历要求、入职类型-->
                                <span>沈阳</span>
                                <span th:if="${!#strings.isEmpty(recruit.regionName)}" th:text="${' - ' + recruit.regionName}"></span>
                                <span th:if="${!#strings.isEmpty(recruit.plateName)}" th:text="${' - ' + recruit.plateName}"></span>
                                <th:block th:each="item,state : ${workingLife}" th:if=" ${item.id eq recruit.jobWorkingLifeType}">
                                    <i>丨</i>
                                    <span th:text="${item.name}">工作经验要求</span>
                                </th:block>
                                <th:block th:each="item,state : ${education}" th:if=" ${item.id eq recruit.education}">
                                    <i>丨</i>
                                    <span th:text="${item.name}">学历要求</span>
                                </th:block>
                                <th:block th:each="item,state : ${induction}" th:if="${item.id eq recruit.induction}">
                                    <i>丨</i>
                                    <span th:text="${item.name}">入职类型</span>
                                </th:block>
                                <!--2020-11-20 End-->
                            </p>
                        </div>
                        <div class="agentSpan" th:if="${recruit._transJobWelfare ne null and recruit._transJobWelfare ne ''}">
                            <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(recruit._transJobWelfare).split(',')}"  th:text="${item}" ></span>
                        </div>
                    </div>
                    <div class="arlLeftLiR">
                        <!--                        <a th:href="${'/companyDetail'+'/'+recruit.companyId+'.htm'}"><h3 th:text="${recruit.companyName}">沈阳瑞家坚果中介</h3></a>-->
                        <p class="agentRecruitListp arlLeftLiRTitle">
                            <span th:if="${!#strings.isEmpty(recruit.typeName)}" th:text="${recruit.typeName}">中介经纪人</span>
                            <i>丨</i>
                            <span>招聘人数：<i th:text="${recruit.jobNeedPersonNumber+'人'}">5</i></span>
                        </p>
                        <a th:if="${#session?.getAttribute('sessionId') == null}" class="topR-login" href="#login" target="_blank" data-toggle="modal">申请职位</a>
                        <a th:if="${#session?.getAttribute('sessionId') != null}" class="arlLeftBTn">申请职位</a>
                    </div>
                </a>
            </li>
        </ul>
    </div>
    <div class="arlRight" th:if="${!#strings.isEmpty(companyDetail?.companyAddress)}">
        <div class="arlRightMap">
            <div>
                <h4 >公司地址</h4>
                <p th:text="${#strings.isEmpty(companyDetail?.companyAddress)?'':companyDetail?.companyAddress}" id="companyAddress"></p>
            </div>
            <!--地图显示公司地址-->
            <div th:include="fragment/fragmentrecruit::company_address_map"></div>
        </div>
    </div>

</div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
<div th:include="house/detail/fragment_login::login"></div>
<!--申请职位-->
<div th:include="fragment/fragmentrecruit::apply_position"></div>
</body>
</html>