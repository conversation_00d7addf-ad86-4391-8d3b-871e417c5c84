<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${companyList?.company_name+'_二手房 – 房小二网'}"></title>
    <meta name="keywords" th:content="${companyList?.company_name +','+companyList?.company_name+'房源,沈阳二手房,房小二网二手房'}"/>
    <meta name="description" th:content="${'房小二网为你提供'+companyList?.company_name+'二手房列表，详细信息，联系方式与海量真实房源。买房卖房，就上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/agentcompany/'+companyId+'-1'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20200623">
    <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2018.css?v=20190307" />-->
<!--    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/agentAgency.css?v=20190712">-->
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/agent/agentStore.css?v=20210409">
    <!--<link href="/css/agentStore.css" type="text/css" rel="stylesheet" />-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/agent/interme_second.js" type="text/javascript"></script>

</head>
<style>
    /*无房源*/
    .warning{margin: 20px 0;padding: 20px 0 20px 195px;background: #fffbf6;border: 1px solid #f5dcbc;}
    .warning p{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left; font-size:14px; line-height:24px; font-weight:bold;color:#333;width: 610px;}
    .warning a{color:#0085ff}
    .warning a:hover{color:#900}
    .secList .infRight p:first-child{font-weight: normal}
</style>
<body>
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"></div>
<!--面包屑-->
<div th:include = "agent/fragmentinter::intermediaryCard"></div>
<!--中介公司信息-->
<div class="head_info" th:include = "agent/fragmentinter::intermediaryInfo"></div>
<!--中介管理首页-->
<div class="head_shadow" th:include = "agent/fragmentinter::agentIntermeHead2" th:with="intermediaryType=1"></div>
<div id="option">
    <ul>
        <li class="fenlei">
            <p>区域：</p>
            <!--区域-->
            <div id="Search_zf" class="leibie">
                <a th:if="${r.id ne '16'}" th:each="r:${region}" th:href="${r.url}" th:text="${#strings.toString(r.name).replaceAll('全部','不限')}" th:id="'r'+${r.id}" th:class="${r.selected}? 'hover':''"></a>
            </div>
        </li>
        <li th:if="${plate}" class="leibie">
            <a th:each="j:${plate}" th:text="${#strings.toString(j.name).replaceAll('全部','不限')}"
               onclick="showIndex(this)" th:href="${j.url}" th:id="'j'+${j.id}"
               th:class="${j.selected}? 'hover':''"><i></i></a>
        </li>
        <script type="text/javascript">
            $(function () {
                var r_or_s = location.pathname;
                if (r_or_s.indexOf('z1') != -1) {
                    $("#btnSubway").attr("class","hover");
                    $("#Search_ditie").css('display','block');
                }
                else {
                    $("#btnRegion").attr("class","hover");
                    $("#Search_zf").css('display','block')
                }
            })
        </script>
        <li ><p>总价：</p>
            <a th:each="p:${price}" th:href="${p.url}"  onclick="showIndex(this)" th:text="${#strings.toString(p.name).replaceAll('全部','不限')}" th:id="'p'+${p.id} " th:class="${p.selected}?'hover':''">></a>
            <!--<div id="Search_PriceDomOk">
                <label><input name="minPrice" id="minPrice" maxlength="4" type="text" th:value="${minPrice}" > - <input name="maxPrice" id="maxPrice" maxlength="4" type="text" th:value="${maxPrice}" > 万 <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button"></label>
            </div>-->
        </li>
        <!--</div>-->
        <!--<div id="Search_BuildAreaDom">-->
        <li ><p>面积：</p>
            <a th:each="a:${area}" th:href="${a.url}" onclick="showIndex(this)" th:text="${#strings.toString(a.name).replaceAll('全部','不限')}" th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
            <!--<div id="Search_BuildAreaDomOk">
                <label><input name="minArea" id="minArea" maxlength="5" type="text" th:value="${minArea}"> - <input name="maxArea" id="maxArea" maxlength="5" type="text" th:value="${maxArea}"> ㎡ <input onclick="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button"></label>
            </div>-->
        </li>
        <!--</div>-->
        <!--<div id="Search_RoomDom">-->
        <li ><p>户型：</p>
            <a th:each="l:${room}" th:href="${l.url}"  onclick="showIndex(this)" th:text="${#strings.toString(l.name).replaceAll('全部','不限')}" th:id="'l'+${l.id}" th:class="${l.selected}?'hover':''">></a>
        </li>
        <!--</div>-->
    </ul>
</div>
<div class="shopManageCont w1170Mar">
    <div class="shopManageList">
        <div class="cl"></div>
        <div th:if="${#lists.isEmpty(companySecList)}" class="warning">
            <p>
                很抱歉，暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                <!--<a th:href="@{/saleHouses}" target="_blank"> 查找相似房源>></a>-->
            </p>
        </div>
        <!--二手房-->
        <div class="secList" th:if="${!#lists.isEmpty(companySecList)}">
            <ul>
                <li class="inf" th:if="${!#lists.isEmpty(companySecList)}" th:each="saleHouse:${companySecList}">
                    <a th:href="${'/salehouse/'+saleHouse.houseId+'.htm'}" target="_blank">

                    </a>
                    <a th:href="${#strings.isEmpty(saleHouse.houseId)?'':'/salehouse/'+saleHouse.houseId+'.htm'}" target="_blank" class="infLeft">
                        <img  th:src="${#strings.isEmpty(saleHouse.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': saleHouse.pic}" th:alt="${#strings.isEmpty(saleHouse.title)? '':saleHouse.title}" />
                        <!--VR and 视频都存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) ne null and #strings.toString(saleHouse.mediaID) ne null }">
                            <s class="vrListIcon" th:if="${#strings.toString(saleHouse.PanID) ne null}"></s>
                            <s class="videoListIcon" th:if="${saleHouse.mediaID ne null}"></s>
                        </s>
                        <!--VR存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) ne null and #strings.toString(saleHouse.mediaID) eq null }">
                            <s class="vrListIcon" th:if="${#strings.toString(saleHouse.PanID) ne null}"></s>
                        </s>
                        <!--视频存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) eq null and #strings.toString(saleHouse.mediaID) ne null }">
                            <s class="videoListIcon" th:if="${saleHouse.mediaID ne null}"></s>
                        </s>
                    </a>
                    <div class="infCtn">
                        <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(saleHouse.houseId) ? '':'/salehouse/'+saleHouse.houseId+'.htm' }">
                            <div th:text="${#strings.isEmpty(saleHouse.title)? '':saleHouse.title}"></div>
                            <i class="listIconBidPrice" th:if="${#strings.toString(saleHouse.auction) eq '1'}"></i>
                            <i class="listIconIstop" th:if="${#strings.toString(saleHouse.auction) ne '1' && #strings.toString(saleHouse.stickOrder) eq '-1'}"></i>
                        </a>
                        <div  class="fourSpan">
                            <span><th:block th:text="${#strings.isEmpty(saleHouse.room)?'':saleHouse.room+'室'}"></th:block><th:block th:text="${#strings.isEmpty(saleHouse.hall)?'':saleHouse.hall+'厅'}"></th:block><th:block th:text="${#strings.isEmpty(saleHouse.toilet)?'':saleHouse.toilet+'卫'}"></th:block></span>
                            <span th:text="${#strings.isEmpty(saleHouse.area)? '':(#strings.indexOf(saleHouse.area,'.') eq -1 ? saleHouse.area:#strings.toString(saleHouse.area).replaceAll('0+?$','').replaceAll('[.]$',''))+'m²'}"></span>
                            <span th:text="${#strings.isEmpty(saleHouse.floor) and #strings.isEmpty(saleHouse.totalFloorNumber) ? '':saleHouse.floorDesc +'/'+saleHouse.totalFloorNumber+'层' }"></span>
                            <span th:text="${#strings.isEmpty(saleHouse.forward) ? '':saleHouse.forward }"></span>
                            <span th:if="${saleHouse.buildDate ne null and saleHouse.buildDate ne ''}" th:text="${saleHouse.buildDate+'年建筑'}"></span>

                        </div>
                        <p  class="houseAddress"  th:if="${!#strings.isEmpty(saleHouse.regionName) and !#strings.isEmpty(saleHouse.subName) and !#strings.isEmpty(saleHouse.plantName) and !#strings.isEmpty(saleHouse.address)}">
                            <s th:if="${ !#strings.isEmpty(saleHouse.subName)}" class="houseAddressSpance">
                                <a th:href="${'/saleVillages/'+saleHouse.subID+'/index.htm'}" target='_blank' th:text="${saleHouse.subName}"></a>
                            </s>
                            <s th:if="${!#strings.isEmpty(saleHouse.regionName) and !#strings.isEmpty(saleHouse.plantName) and !#strings.isEmpty(saleHouse.address)}">
                                <i th:if="${!#strings.isEmpty(saleHouse.regionName)}" th:text="${saleHouse.regionName}"></i>-
                                <i th:if="${!#strings.isEmpty(saleHouse.plantName)}" th:text="${saleHouse.plantName}"></i>-
                                <i th:if="${!#strings.isEmpty(saleHouse.address)}" th:text="${saleHouse.address}"></i>
                            </s>
                        </p>
                        <div  class="houseItemIcon">
                            <div class="isGoodHouse" th:if="${saleHouse.isGoodHouse eq '-1'}"></div>
                            <span th:if="${!#strings.isEmpty(saleHouse.houseTrait) and oc.count le 3}" th:each="establish,oc:${#strings.setSplit(saleHouse.houseTrait,',') }" th:text="${#strings.isEmpty(establish)? '':establish}" th:class="${'tese_'+oc.count}"></span>
                        </div>
                        <!--<p class="person" th:text="${#strings.isEmpty(saleHouse.spanTime)? '':saleHouse.spanTime+'更新'}">8分钟前更新</p>-->
                        <!--<span class='tese_2'>繁华地段</span> <span class='tese_3'>交通便利</span>-->
                        <span style="display: none; background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale/iconAuthentichouse.gif) no-repeat 9px 3px; padding-left: 76px; font-size: 16px; color: #E04545; position: absolute; left: 206px; bottom: 2px;"></span>
                        <!--<a class="checkHouse" target="_blank"  th:href="${#strings.isEmpty(saleHouse.subID) ? '': '/saleHouses/-v'+saleHouse.subID}">查看同小区房源 ></a>-->
                    </div>
                    <div class="infRight">
                        <p class="infRightPriseM" th:if="${#strings.isEmpty(saleHouse.price) or saleHouse.price eq 0 }"  ><b th:text="${'面议'}"></b></p>
                        <p class="infRightPrise" th:if= "${!#strings.isEmpty(saleHouse.price) and saleHouse.price ne 0}" >
                            <b><th:block th:text="${(#strings.indexOf(saleHouse.price,'.') eq -1 ? rent.price:#strings.toString(saleHouse.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block></b>
                            <i th:text="${#strings.isEmpty(saleHouse.price) or (#strings.toString(saleHouse.price) eq '0.0')?'':'万'}"></i>
                        </p>
                        <p>
                            <th:block th:text="${#strings.isEmpty(saleHouse.price) or saleHouse.price eq 0 ? '': (#strings.indexOf(saleHouse.unitPrice,'.') eq -1 ? saleHouse.area:#strings.toString(saleHouse.unitPrice).replaceAll('0+?$','').replaceAll('[.]$',''))+'元/m²'}"></th:block>
                        </p>

                    </div>
                </li>
            </ul>
            <div class="cl"></div>
            <div class="page">
            <div th:include="fragment/page :: page"></div>
        </div>
    </div>
    </div>
    <!--右侧内容-->
    <div class="shopManageRight">
        <!--门店地址-->
        <div th:include = "agent/fragmentinter::storeOtherAddress"></div>
        <!--推荐经纪人-->
        <!--<div th:include = "agent/fragmentinter::companyAgent"></div>-->
        <!--岗位急聘-->
        <div th:include = "agent/fragmentinter::companyUrgent"></div>
    </div>
</div>
<script>
    function price(priceIdName) {
        $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
        $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
        var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
        var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
        if (num1 == "" && num2 != "") {
            $("#" + priceIdName + " input").eq(0).val("0");
            $("#" + priceIdName + " input").eq(2).show();
        } else if (num2 == "" && num1 != "") {
            $("#" + priceIdName + " input").eq(2).show();
        } else if (num1 != "" || num2 != "") {
            $("#" + priceIdName + " input").eq(2).show();
        } else {
            $("#" + priceIdName + " input").eq(2).hide();
        }
    }
    price("Search_BuildAreaDomOk");
    price("Search_PriceDomOk");
    $("#Search_PriceDomOk input").keyup(function () {
        price("Search_PriceDomOk");
    })
    $("#Search_BuildAreaDomOk input").keyup(function () {
        price("Search_BuildAreaDomOk");
    })
    $("#Search_PriceDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            // $("#Search_Btn_Search1").click()
        }
    });
    $("#Search_BuildAreaDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            $("#Search_Btn_Searchs").click()
        }
    });
</script>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>