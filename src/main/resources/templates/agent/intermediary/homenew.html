<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${companyList?.company_name+'_二手房 – 房小二网'}"></title>
    <meta name="keywords" th:content="${companyList?.company_name +'，'+companyList?.company_name+'房源，沈阳二手房，房小二网二手房'}"/>
    <meta name="description" th:content="${'房小二网为你提供'+companyList?.company_name+'二手房列表，详细信息，联系方式与海量真实房源。买房卖房，就上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/intermediary/'+companyId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20200623">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/agent/agentStore.css?v=20210409">
    <!--<link href="/css/agentStore.css" type="text/css" rel="stylesheet" />-->
    	
</head>
<body>
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"></div>
<!--面包屑-->
<div th:include = "agent/fragmentinter::intermediaryCard"></div>
<!--中介公司信息-->
<div class="head_info" th:include = "agent/fragmentinter::intermediaryInfo"></div>
<!--中介首页切换导航-->
<div class="head_shadow" th:include = "agent/fragmentinter::agentIntermeHead" th:with="intermediaryType=9"></div>
<!--门店简介-->
<div class="storeText w1170Mar" th:if="${!#strings.isEmpty(companyList.company_desc)}">
    <h4 class="homeTitle">
    	门店简介
    	<i class="titleIco"></i>
    </h4>
    <div class="txtMain">
        <p class="" th:utext="${companyList.company_desc}"></p>
        <img th:if="${!#lists.isEmpty(companyList.pics)}" th:each="pics:${companyList.pics}" th:src="${pics}" alt="" >
        <a class="seemore seeBtn" style="display: none;">查看更多></a>
        <a class="seeShou seeBtn">收起></a>

    </div>
</div>
<!--精英顾问-->
<div th:include = "agent/fragmentinter::new_companyAgent"></div>
<div class="shopManageCont w1170Mar" th:if="${!#lists.isEmpty(companySecList) || !#lists.isEmpty(companyRentList) || !#lists.isEmpty(shop) || !#lists.isEmpty(officeList)}">
    <h4 class="homeTitle">
    	店长推荐
    	<i class="titleIco"></i>
    </h4>
    <div class="shopManageList">
        <div class="cl"></div>
        <div th:if="${#lists.isEmpty(companySecList) && #lists.isEmpty(companyRentList) && #lists.isEmpty(shop) && #lists.isEmpty(officeList)}" class="warning">
            <p>
                很抱歉，暂时没有房源信息，店铺正在建设中。。。<br>
            </p>
        </div>
        <!--二手房-->
        <div class="secList" th:if="${!#lists.isEmpty(companySecList)}">
            <h4 class="shopManageListTitle">
            	推荐二手房
            	
            </h4>
            <ul>
                <li class="inf" th:if="${!#lists.isEmpty(companySecList) and i.index lt 10}" th:each="saleHouse,i:${companySecList}" >

                    <a th:href="${#strings.isEmpty(saleHouse.houseId)?'':'/salehouse/'+saleHouse.houseId+'.htm'}" target="_blank" class="infLeft">
                        <img  th:src="${#strings.isEmpty(saleHouse.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': saleHouse.pic}" th:alt="${#strings.isEmpty(saleHouse.title)? '':saleHouse.title}" />
                        <!--VR and 视频都存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) ne null and #strings.toString(saleHouse.mediaID) ne null }">
                            <s class="vrListIcon" th:if="${#strings.toString(saleHouse.PanID) ne null}"></s>
                            <s class="videoListIcon" th:if="${saleHouse.mediaID ne null}"></s>
                        </s>
                        <!--VR存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) ne null and #strings.toString(saleHouse.mediaID) eq null }">
                            <s class="vrListIcon" th:if="${#strings.toString(saleHouse.PanID) ne null}"></s>
                        </s>
                        <!--视频存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) eq null and #strings.toString(saleHouse.mediaID) ne null }">
                            <s class="videoListIcon" th:if="${saleHouse.mediaID ne null}"></s>
                        </s>
                    </a>
                    <div class="infCtn">
                        <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(saleHouse.houseId) ? '':'/salehouse/'+saleHouse.houseId+'.htm' }">
                            <div th:text="${#strings.isEmpty(saleHouse.title)? '':saleHouse.title}"></div>
                            <i class="listIconBidPrice" th:if="${#strings.toString(saleHouse.auction) eq '1'}"></i>
                            <i class="listIconIstop" th:if="${#strings.toString(saleHouse.auction) ne '1' && #strings.toString(saleHouse.stickOrder) eq '-1'}"></i>
                        </a>

                        <div  class="fourSpan">
                            <span><th:block th:text="${#strings.isEmpty(saleHouse.room)?'':saleHouse.room+'室'}"></th:block><th:block th:text="${#strings.isEmpty(saleHouse.hall)?'':saleHouse.hall+'厅'}"></th:block><th:block th:text="${#strings.isEmpty(saleHouse.toilet)?'':saleHouse.toilet+'卫'}"></th:block></span>
                            <span th:text="${#strings.isEmpty(saleHouse.area)? '':(#strings.indexOf(saleHouse.area,'.') eq -1 ? saleHouse.area:#strings.toString(saleHouse.area).replaceAll('0+?$','').replaceAll('[.]$',''))+'m²'}"></span>
                            <span th:text="${#strings.isEmpty(saleHouse.floor) and #strings.isEmpty(saleHouse.totalFloorNumber) ? '':saleHouse.floorDesc +'/'+saleHouse.totalFloorNumber+'层' }"></span>
                            <span th:text="${#strings.isEmpty(saleHouse.forward) ? '':saleHouse.forward }"></span>
                            <span th:if="${saleHouse.buildDate ne null and saleHouse.buildDate ne ''}" >
                                <th:block th:text="${saleHouse.buildDate+'年建筑'}"></th:block>
                            </span>
                        </div>
                        <p  class="houseAddress"  th:if="${!#strings.isEmpty(saleHouse.regionName) and !#strings.isEmpty(saleHouse.subName) and !#strings.isEmpty(saleHouse.plantName) and !#strings.isEmpty(saleHouse.address)}">
                            <s th:if="${ !#strings.isEmpty(saleHouse.subName)}" class="houseAddressSpance">
                                <a th:href="${'/saleVillages/'+saleHouse.subID+'/index.htm'}" target='_blank' th:text="${saleHouse.subName}"></a>
                            </s>
                            <s th:if="${!#strings.isEmpty(saleHouse.regionName) and !#strings.isEmpty(saleHouse.plantName) and !#strings.isEmpty(saleHouse.address)}">
                                <i th:if="${!#strings.isEmpty(saleHouse.regionName)}" th:text="${saleHouse.regionName}"></i>-
                                <i th:if="${!#strings.isEmpty(saleHouse.plantName)}" th:text="${saleHouse.plantName}"></i>-
                                <i th:if="${!#strings.isEmpty(saleHouse.address)}" th:text="${saleHouse.address}"></i>
                            </s>
                        </p>
                        <div  class="houseItemIcon">
                            <div class="isGoodHouse" th:if="${saleHouse.isGoodHouse eq '-1'}"></div>
                            <span th:if="${!#strings.isEmpty(saleHouse.houseTrait) and oc.count lt 4}" th:each="establish,oc:${#strings.setSplit(saleHouse.houseTrait,',') }" th:text="${#strings.isEmpty(establish)? '':establish}" th:class="${'tese_'+oc.count}"></span>
                        </div>
                    </div>
                    <div class="infRight">
                        <p class="infRightPriseM" th:if="${#strings.isEmpty(saleHouse.price) or saleHouse.price eq 0 }"  ><b th:text="${'面议'}"></b></p>
                        <p class="infRightPrise" th:if= "${!#strings.isEmpty(saleHouse.price) and saleHouse.price ne 0}" >
                            <b><th:block th:text="${(#strings.indexOf(saleHouse.price,'.') eq -1 ? rent.price:#strings.toString(saleHouse.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block></b>
                            <i th:text="${#strings.isEmpty(saleHouse.price) or (#strings.toString(saleHouse.price) eq '0.0')?'':'万'}"></i>
                        </p>
                        <p>
                            <th:block th:text="${#strings.isEmpty(saleHouse.price) or saleHouse.price eq 0 ? '': (#strings.indexOf(saleHouse.unitPrice,'.') eq -1 ? saleHouse.area:#strings.toString(saleHouse.unitPrice).replaceAll('0+?$','').replaceAll('[.]$',''))+'元/m²'}"></th:block>
                        </p>
                    </div>

                </li>
            </ul>
        </div>
        <!--租房-->
        <div class="rentList" th:if="${!#lists.isEmpty(companyRentList)}">
            <h4 class="shopManageListTitle">推荐租房</h4>
            <ul>
                <li class="inf" th:if="${!#lists.isEmpty(companyRentList) and i.index lt 10}" th:each="rent,i:${companyRentList}">
                    <a th:href="${#strings.isEmpty(rent.houseId)?'':'/rent/'+rent.houseId+'.htm'}" target="_blank" class="infLeft">
                        <img th:src="${#strings.isEmpty(rent.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': rent.pic}" th:alt="${#strings.isEmpty(rent.title)? '':rent.title}">
                        <div class="" th:if="${#strings.toString(rent.isXiQue) eq '1'}">佣金95折</div>
                        <!--VR and 视频都存在 -->
                        <s class="listIconK" th:if="${#strings.toString(rent.PanID) ne null and #strings.toString(rent.mediaID) ne null }">
                            <s class="vrListIcon" th:if="${#strings.toString(rent.PanID) ne null}"></s>
                            <s class="videoListIcon" th:if="${rent.mediaID ne null}"></s>
                        </s>
                        <!--VR存在 -->
                        <s class="listIconK" th:if="${#strings.toString(rent.PanID) ne null and #strings.toString(rent.mediaID) eq null }">
                            <s class="vrListIcon" th:if="${#strings.toString(rent.PanID) ne null}"></s>
                        </s>
                        <!--视频存在 -->
                        <s class="listIconK" th:if="${#strings.toString(rent.PanID) eq null and #strings.toString(rent.mediaID) ne null }">
                            <s class="videoListIcon" th:if="${rent.mediaID ne null}"></s>
                        </s>
                    </a>
                    &nbsp;&nbsp;&nbsp;<div class="infCtn">
                    <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(rent.houseId) ? '':'/rent/'+rent.houseId +'.htm' }">
                        <div th:text="${#strings.isEmpty(rent.title)? '':rent.title}"></div>
                        <i class="listIconBidPrice" th:if="${#strings.toString(rent.auction) eq '1'}"></i>
                        <i class="listIconIstop" th:if="${#strings.toString(rent.auction) ne '1' && #strings.toString(rent.stickOrder) eq '-1'}"></i>
                    </a>

                    <div  class="fourSpan">
                        <span th:text="${#strings.isEmpty(rent.rentTypeName)? '':rent.rentTypeName}"></span>
                        <span th:text="${(#strings.isEmpty(rent.room)?'':rent.room+'室')+(#strings.isEmpty(rent.hall)?'':rent.hall+'厅')+(#strings.isEmpty(rent.toilet)?'':rent.toilet+'卫')}"></span>
                        <span th:text="${#strings.isEmpty(rent.area)? '':(#strings.indexOf(rent.area,'.') eq -1 ? rent.area:#strings.toString(rent.area).replaceAll('0+?$','').replaceAll('[.]$',''))+'m²'}"></span>
                        <span th:text="${#strings.isEmpty(rent.fitment)? '': rent.fitment}"></span>
                        <span th:text="${#strings.isEmpty(rent.floor) and #strings.isEmpty(rent.totalFloorNumber) ? '':rent.floorDesc +'/'+rent.totalFloorNumber+'层' }"></span>
                        <span th:text="${#strings.isEmpty(rent.forwardTypeName) ? '':rent.forwardTypeName }"></span>
                    </div>
                    <p  class="houseAddress"  th:if="${!#strings.isEmpty(rent.regionName) and !#strings.isEmpty(rent.subName) and !#strings.isEmpty(rent.plateName) and !#strings.isEmpty(rent.address)}">
                        <s th:if="${ !#strings.isEmpty(rent.subName)}" class="houseAddressSpance">
                            <a th:href="${'/saleVillages/'+rent.subId+'/index.htm'}" target='_blank' th:text="${rent.subName}"></a>
                        </s>
                        <s th:if="${!#strings.isEmpty(rent.regionName) and !#strings.isEmpty(rent.plateName) and !#strings.isEmpty(rent.address)}">
                            <i th:if="${!#strings.isEmpty(rent.regionName)}"  th:text="${rent.regionName}"></i>-
                            <i th:if="${!#strings.isEmpty(rent.plateName)}" th:text="${rent.plateName}"></i>-
                            <i th:if="${!#strings.isEmpty(rent.address)}" th:text="${rent.address}"></i>
                        </s>
                    </p>
                    <div  class="houseItemIcon">
                        <span th:if="${!#strings.isEmpty(rent.houseTrait) and oc.count lt 4}" th:each="establish,oc:${#strings.setSplit(rent.houseTrait,',') }" th:text="${#strings.isEmpty(establish)? '':establish}" th:class="${'tese_'+oc.count}"></span>
                    </div>
                </div>
                    <div class="infRight">
                        <p class="infRightPrise">
                            <b th:text="${#strings.isEmpty(rent.price) or rent.price eq 0? '面议':(#strings.indexOf(rent.price,'.') eq -1 ? rent.price:#strings.toString(rent.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></b>
                            <i th:text="${#strings.isEmpty(rent.price) or rent.price eq 0 ? '':(rent.payment eq 100 or rent.payment eq 50 ?'元/年':'元/月')}"></i>
                        </p>

                    </div>
                </li>
            </ul>
        </div>
        <!--商铺-->
        <div class="shopList" th:if="${!#lists.isEmpty(shop)}">
            <h4 class="shopManageListTitle">推荐商铺</h4>
            <ul>
                <li class="inf" th:if="${!#lists.isEmpty(shop) and i.index lt 10}" th:each = "shop,i : ${shop}">
                    <a th:href="${#strings.isEmpty(shop.shopId)? '': '/shop/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
                        <!--                        <i class='imgNum' th:if="${shop.picNum ne null and #strings.toString(shop.picNum).length() gt 1 or shop.picNum gt '4'}" th:text="${#strings.isEmpty(shop.picNum)? '':shop.picNum}">6</i>-->
                        <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : shop.pic}" th:alt="${#strings.isEmpty(shop.title) ? '': shop.title}">
                        <!--VR and 视频都存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                            <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                            <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                        </s>
                        <!--VR存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                            <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                        </s>
                        <!--视频存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                            <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                        </s>
                    </a>
                    &nbsp;&nbsp;&nbsp;&nbsp;<div class="infCtn">
                    <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)? '': '/shop/'+shop.shopId+'.htm'}">
                        <div th:text="${#strings.isEmpty(shop.title) ? '': shop.title}"></div>
                        <i class="listIconBidPrice" th:if="${#strings.toString(shop.auction) eq '1'}"></i>
                        <i class="listIconIstop" th:if="${#strings.toString(shop.auction) ne '1' && shop.stickOrder eq '-1'}"></i>
                    </a>
                    <div  class="fourSpan">
                        <span th:text="${#strings.isEmpty(shop.area)? '': #numbers.formatInteger(shop.area,1)+'m²' }"></span>
                        <span th:text = "${#strings.isEmpty(shop.shopCategoriesName) ? '':shop.shopCategoriesName}"></span>
                    </div>
                    <p  class="houseAddress" th:if="${!#strings.isEmpty(shop.regionName)  and !#strings.isEmpty(shop.plateName)}">
                        <s>
                            <i th:if="${!#strings.isEmpty(shop.regionName)}" th:text="${shop.regionName}"></i>
                            <i th:if="${!#strings.isEmpty(shop.plateName)}" th:text="${'-'+shop.plateName}"></i>
                            <i th:if="${!#strings.isEmpty(shop.address)}" th:text="${'-'+shop.address}"></i>
                        </s>
                    </p>
                    <div  class="houseItemIcon">
                        <span th:if="${!#strings.isEmpty(shop.houseTrait) and oc.count lt 4}" th:each="establish,oc:${#strings.setSplit(shop.houseTrait,',') }" th:text="${#strings.isEmpty(establish)? '':establish}" th:class="${'tese_'+oc.count}"></span>
                    </div>
                </div>
                    <div class="infRight" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}">
                        <p class="infRightPrise" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}"  >
                            <b style="font-weight: bold"><th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议': (#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block></b>
                            <i th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00'?'':'万'}"></i>
                        </p>

                        <p><i><b th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'':shop.unitPrice}">13007元/m²</b></i></p>
                    </div>

                    <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '2'}">
                        <p class="infRightPrise">
                            <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月')}"></th:block>-->
                            <span style="font-weight: bold" th:if="${#strings.isEmpty(shop.price) or shop.price eq '0.00'}" th:text="${'面议'}"></span>
                            <s th:if="${!#strings.isEmpty(shop.price) && shop.price ne '0.00'}">
                                <!--                                <span  style="font-weight: bold;"  th:text="${#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>-->
                                <b style="font-weight: bold;"><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price :#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}"></th:block></b>
                                <i th:text="${shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月'}"></i>
                            </s>
                            <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">租</span>-->
                        </p>
                        <p><b th:text="${#strings.isEmpty(shop.price)or  shop.price eq '0.00' or #strings.toString(shop.unitPrice) eq '0元/m²·天' or #strings.isEmpty(shop.unitPrice)?'':shop.unitPrice}">1.77 元/m²·天</b></p>
                    </div>
                    <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '3'}">
                        <p class="infRightPrise">
                            <span style="font-weight: bold" th:if="${#strings.isEmpty(shop.price) or shop.price eq '0.00'}" th:text="${'面议'}"></span>
                            <s th:if="${!#strings.isEmpty(shop.price) && shop.price ne '0.00'}">
                                <!--                                <span style="font-weight: bold;" th:text="${#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>-->
                                <b style="font-weight: bold;"><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}" ></th:block></b>
                                <i th:text="${shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月'}"></i>
                            </s>
                            <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'? '元/年':'元/月')}"></th:block>-->
                            <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">兑</span>-->
                        </p>
                        <p><b th:text="${#strings.isEmpty(shop.price)or  shop.price eq '0.00' or #strings.toString(shop.unitPrice) eq '0元/m²·天' ?'':(#strings.indexOf(shop.unitPrice,'面议') eq -1 ?shop.unitPrice+'元':shop.unitPrice)}">1.77 元/m²·天</b></p>
                    </div>
                </li>
            </ul>
        </div>
        <!--写字楼-->
        <div class="officeList" th:if="${!#lists.isEmpty(officeList)}">
            <h4 class="shopManageListTitle">推荐写字楼</h4>
            <ul>
                <li class="inf" th:if="${!#lists.isEmpty(officeList) and i.index lt 10}" th:each = "shop,i : ${officeList}">
                    <a th:href="${#strings.isEmpty(shop.shopId)? '': '/scriptorium/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
                        <!--                        <i class='imgNum' th:if="${shop.picNum ne null and #strings.toString(shop.picNum).length() gt 1 or shop.picNum gt '4'}" th:text="${#strings.isEmpty(shop.picNum)? '':shop.picNum}"></i>-->
                        <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : shop.pic}" th:alt="${#strings.isEmpty(shop.title) ? '': shop.title}">
                        <!--VR and 视频都存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                            <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                            <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                        </s>
                        <!--VR存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                            <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                        </s>
                        <!--视频存在 -->
                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                            <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                        </s>
                    </a>
                    &nbsp;&nbsp;&nbsp;&nbsp;<div class="infCtn">
                    <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)? '': '/scriptorium/'+shop.shopId+'.htm'}" >
                        <div th:text="${#strings.isEmpty(shop.title) ? '': shop.title}"></div>
                        <i class="listIconBidPrice" th:if="${#strings.toString(shop.auction) eq '1'}"></i>
                        <i class="listIconIstop" th:if="${#strings.toString(shop.auction) ne '1' && shop.stickOrder eq '-1'}"></i>
                    </a>
                    <div  class="fourSpan">
                        <span th:text="${#strings.isEmpty(shop.area)? '': #numbers.formatInteger(shop.area,1)+'m²' }"></span>
                        <span th:text = "${#strings.isEmpty(shop.fitmentType) ? '':shop.fitmentType}"></span>
                    </div>
                    <p  class="houseAddress" >
                        <a class="office_jump" th:if="${!#strings.isEmpty(shop?.officeId)}" th:href="${'/officeProject/'+shop.officeId+'.htm'}" target="_blank" th:text="${#strings.isEmpty(shop.officeName)?'':shop.officeName}"></a>
                        <s class="office_jump" th:if="${#strings.isEmpty(shop?.officeId)}" th:text="${#strings.isEmpty(shop.officeName)?'':shop.officeName}"></s>
                        <s th:if="${!#strings.isEmpty(shop.regionName) and !#strings.isEmpty(shop.plateName) and !#strings.isEmpty(shop.address)}">
                            <i  th:text="${#strings.isEmpty(shop.regionName)?'':shop.regionName}"></i>-
                            <i  th:text="${#strings.isEmpty(shop.plateName)?'':shop.plateName}"></i>-
                            <i  th:text="${#strings.isEmpty(shop.address)?'':shop.address}"></i>
                        </s>
                    </p>
                    <div  class="houseItemIcon">
                    <span th:class="tese_+${i.index+1}" th:if="${!#lists.isEmpty(shop.houseTraits) and i.index &lt; 3}" th:each="houseTraits,i:${#strings.toString(shop.houseTraits).split(',')}"
                          th:text="${houseTraits}"></span>
                    </div>
                    </span>
                </div>
                    <div class="infRight" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '5'}">
                        <p class="infRightPrise">
                            <b style="font-weight: bold" th:text = "${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0' ?'面议': ((#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')))}"></b>
                            <i th:text = "${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0' ?'':'万'}"></i>
                            <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">售</span>-->
                        </p>

                        <p><i><b th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'':#strings.toString(shop.unitPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/m²'}">13007元/m²</b></p>
                    </div>

                    <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '4'}">
                        <p class="infRightPrise">
                            <b style="font-weight: bold" th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'面议': ((#strings.indexOf(shop.unitPrice,'.') eq -1 ? shop.unitPrice:#strings.toString(shop.unitPrice).replaceAll('0+?$','').replaceAll('[.]$','')))}"></b>
                            <i th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'':'元/m²·天'}"></i>
                            <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">租</span>-->
                        </p>
                        <p><b th:text="${#strings.isEmpty(shop.price) or  #strings.toString(shop.price) eq '0.0' ?'':#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')+'元/月'}">1.77 元/m²·天</b></p>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <!--首页右侧内容-->
    <div class="shopManageRight">
        <!--门店地址-->
        <div th:include = "agent/fragmentinter::storeOtherAddress"></div>
        <!--岗位急聘-->
        <div th:include = "agent/fragmentinter::companyUrgent"></div>
    </div>
</div>


<script>
    $(document).ready(function(){
        //根据行高判断查看更多显隐
        var pHeight = $(".txtMain").height();
        pHeight = pHeight/36;
        console.log(pHeight)
        if( pHeight > 5 ){
            $(".txtMain p").addClass("four");
            $(".txtMain img").css("display","none");
            $(".seemore").show();
        }else{
            $(".txtMain p").removeClass("four");
            $(".seemore").hide();
        }
        $(".seemore").click(function(){
            $(".txtMain p").removeClass("four");
            $(".txtMain img").css("display","block");
            $(this).hide();
            $(".seeShou").css("display","block")
        })
        $(".seeShou").click(function(){
            $(".txtMain img").css("display","none");
            $(".txtMain p").addClass("four");
            $(this).hide();
            $(".seemore").show();
        })
    })
</script>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
<div th:include="house/detail/fragment_login::login"></div>
</body>
</html>













