<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${companyList?.company_name+'_写字楼 – 房小二网'}"></title>
    <meta name="keywords" th:content="${companyList?.company_name +','+companyList?.company_name+'房源,沈阳写字楼,房小二网写字楼'}"/>
    <meta name="description" th:content="${'房小二网为你提供'+companyList?.company_name+'写字楼列表，详细信息，联系方式与海量真实房源。买房卖房，就上房小二网。'}"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/agentcompany/'+companyId+'-4'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20200623">
<!--    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/agentAgency.css?v=20190712">-->
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/agent/agentStore.css?v=20210409">
    <!--<link href="/css/agentStore.css" type="text/css" rel="stylesheet" />-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

</head>
<style>
    /*无房源*/
    .warning{margin: 20px 0;padding: 20px 0 20px 195px;background: #fffbf6;border: 1px solid #f5dcbc;}
    .warning p{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left; font-size:14px; line-height:24px; font-weight:bold;color:#333;width: 610px;}
    .warning a{color:#0085ff}
    .warning a:hover{color:#900}
    .infRight p s {font-weight: bold;  }
</style>
<body>
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"></div>
<!--面包屑-->
<div th:include = "agent/fragmentinter::intermediaryCard"></div>
<!--中介公司信息-->
<div class="head_info" th:include = "agent/fragmentinter::intermediaryInfo"></div>
<!--中介管理首页-->
<div class="head_shadow" th:include = "agent/fragmentinter::agentIntermeHead2" th:with="intermediaryType=4"></div>
<div class="shopManageCont w1170Mar">
    <div id="option">
        <ul>
            <!--供求-->
            <li><p>供求：</p>
                <a th:each="t,i:${type}" th:if="${i.index == 0}"  th:href="${t.url}" th:text="${'不限'}"  th:class="${t.selected}? 'hover':''"></a>
                <a th:each="t,i:${type}" th:if="${#strings.toString(t.id) eq '5'}" th:id="${'t'+t.id}"  th:href="${t.url}" th:text="${t.name}"  th:class="${t.selected}? 'hover':''"></a>
                <a th:each="t,i:${type}" th:if="${#strings.toString(t.id) eq '4'}" th:id="${'t'+t.id}"  th:href="${t.url}" th:text="${t.name}"  th:class="${t.selected}? 'hover':''"></a>
            </li>
            <!--区域-->
            <li class=""><p>区域：</p>
                <a th:each="r,ri:${region}" th:href="${r.url}" th:id="'r'+${r.id}"  th:class="${r.selected}? 'hover':''">
                    <th:block  th:text="${ri.index == 0?'不限':r.name}"></th:block>
                    <i th:if="${!#strings.isEmpty(r.id)}"></i>
                </a><br>
            </li>
            <!--板块-->
            <li th:if="${plate}" id="Search_zf" class="leibie" style="display: block;"><p style="display: none">板块：</p>
                <a th:each="j,stat:${plate}" th:text="${stat.index ==0?'不限': j.name}" th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
            </li>
            <!--售价-->
            <li th:if="${typeId ne '4'}"><p>售价：</p>
                <a th:each="sp,stat:${sprice}" th:href="${sp.url}" th:text="${stat.index ==0?'不限': sp.name}" th:id="'sp'+${sp.id} " th:class="${sp.selected}?'hover':''">></a>
               <!-- <div id="Search_sPriceDomOk">
                    <label>
                        <input name="sminPrice" id="sminPrice" maxlength="4" type="text" th:value="${sminPrice}" > - <input name="smaxPrice" id="smaxPrice" maxlength="4" type="text" th:value="${smaxPrice}" > 万
                        <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button">
                    </label>
                </div>-->
            </li>
            <th:block  th:if="${typeId ne '5'}">
                <li class="rPriceDomOk"><p>租金：</p><!--总价-->
                    <a th:each="rp,stat:${rprice}" th:href="${rp.url}" th:text="${stat.index==0?'不限':rp.name}" th:id="'rp'+${rp.id}" th:class="${rp.selected}?'hover':''">></a>
                   <!-- <div id="Search_rPriceDomOk">
                        <label>
                            <input name="rminPrice" id="rminPrice" maxlength="6" type="text" th:value="${rminPrice}" > - <input name="rmaxPrice" id="rmaxPrice" maxlength="6" type="text" th:value="${rmaxPrice}" > 元/月
                            <input onclick="__doPostBack('Search$Btn_Search2','')" name="Search$Btn_Search2" id="Search_Btn_Search2" value="确定" class="btn_search" style="display: none;" type="button">
                        </label>
                    </div>
                    <a th:each="cp,i:${choosePrice}" th:if="${i.index ne 0}" th:text="${cp.name}" th:id="'cp'+${cp.id}"  th:href="${cp.url}" th:class="${cp.selected?'Search_uPriceDom hover':'Search_uPriceDom'}"></a>
                   -->
                </li>
                <li class="uPriceDomOk" ><p>租金：</p><!--单价-->
                    <a th:each="up,stat:${uprice}" th:href="${up.url}" th:text="${stat.index==0?'不限':up.name}" th:id="'up'+${up.id}" th:class="${up.selected}?'hover':''">></a>
                   <!-- <div id="Search_uPriceDomOk">
                        <label>
                            <input name="uminPrice" id="uminPrice" maxlength="4" type="text" th:value="${uminPrice}" > - <input name="umaxPrice" id="umaxPrice" maxlength="4" type="text" th:value="${umaxPrice}" > 元/㎡·天
                            <input onclick="__doPostBack('Search$Btn_Search3','')" name="Search$Btn_Search3" id="Search_Btn_Search3" value="确定" class="btn_search" style="display: none;" type="button">
                        </label>
                    </div>
                    <a th:each="cp,i:${choosePrice}" th:if="${i.index ne 0}" th:text="${cp.name}" th:id="'cp'+${cp.id}"  th:href="${cp.url}" th:class="${cp.selected?'Search_uPriceDom hover':'Search_uPriceDom'}"></a>
                   -->
                </li>

                <script>
                    var officeUrl = location.pathname;
                    if (officeUrl.indexOf("cp2") != -1) {
                        $(".rPriceDomOk").hide()
                    }else{
                        $(".uPriceDomOk").hide()
                    }
                </script>
            </th:block>
            <li ><p>面积：</p>
                <a th:each="a,stat:${area}" th:href="${a.url}" th:text="${stat.index==0?'不限':a.name}" th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
               <!-- <div id="Search_BuildAreaDomOk">
                    <label>
                        <input name="ominArea" id="ominArea" maxlength="5" type="text" th:value="${ominArea}"> - <input name="omaxArea" id="omaxArea" maxlength="5" type="text" th:value="${omaxArea}"> ㎡
                        <input onclick="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button">
                    </label>
                </div>-->
            </li>
            <li><p>类型：</p>
                <a th:each="c,stat:${category}" th:href="${c.url}" th:text="${stat.index == 0 ?'不限': c.name}" th:id="${'c'+c.id}" th:class="${c.selected}? 'hover':''"></a>
            </li>
        </ul>
    </div>
    <div class="shopManageList">
        <div class="cl"></div>
        <div th:if="${#lists.isEmpty(officeList)}" class="warning">
            <p>
                很抱歉，暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                <!--<a th:href="@{/scriptoriums/}" target="_blank"> 查找相似房源>></a>-->
            </p>
        </div>
        <!--写字楼-->
        <div class="officeList" th:if="${!#lists.isEmpty(officeList)}">
            <ul>
                <li class="inf" th:if="${!#lists.isEmpty(officeList)}" th:each = "shop : ${officeList}">
                   <a th:href="${#strings.isEmpty(shop.shopId)? '': '/scriptorium/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
<!--                       <i class='imgNum' th:if="${shop.picNum ne null and #strings.toString(shop.picNum).length() gt 1 or shop.picNum gt '4'}" th:text="${#strings.isEmpty(shop.picNum)? '':shop.picNum}">6</i>-->
                        <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : shop.pic}" th:alt="${#strings.isEmpty(shop.title) ? '': shop.title}">
                       <!--VR and 视频都存在 -->
                       <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                           <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                           <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                       </s>
                       <!--VR存在 -->
                       <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                           <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                       </s>
                       <!--视频存在 -->
                       <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                           <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                       </s>
                    </a>
                    &nbsp;&nbsp;&nbsp;&nbsp;<div class="infCtn">
                    <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)? '': '/scriptorium/'+shop.shopId+'.htm'}" >
                        <div th:text="${#strings.isEmpty(shop.title) ? '': shop.title}"></div>
                        <i class="listIconBidPrice" th:if="${#strings.toString(shop.auction) eq '1'}"></i>
                        <i class="listIconIstop" th:if="${#strings.toString(shop.auction) ne '1' && shop.stickOrder eq '-1'}"></i>
                    </a>
                    <div  class="fourSpan">
                        <span th:text="${#strings.isEmpty(shop.area)? '': #numbers.formatInteger(shop.area,1)+'m²' }"></span>
                        <span th:text = "${#strings.isEmpty(shop.fitmentType) ? '':shop.fitmentType}"></span>
                    </div>
                    <p  class="houseAddress"  th:if="${ !#strings.isEmpty(shop.officeName)  and !#strings.isEmpty(shop.regionName)  and !#strings.isEmpty(shop.plateName) and !#strings.isEmpty(shop.address)}">
                        <s class="houseAddressSpance">
                            <a th:if="${!#strings.isEmpty(shop?.officeId)}" th:href="${'/officeProject/'+shop.officeId+'.htm'}" target="_blank" th:text="${#strings.isEmpty(shop.officeName)?'':shop.officeName}"></a>
                            <s th:if="${#strings.isEmpty(shop?.officeId)}" th:text="${#strings.isEmpty(shop.officeName)?'':shop.officeName}"></s>
                        </s>
                        <s th:if="${!#strings.isEmpty(shop.regionName) and !#strings.isEmpty(shop.plateName) and !#strings.isEmpty(shop.address)}">
                            <i  th:text="${#strings.isEmpty(shop.regionName)?'':shop.regionName}"></i>-
                            <i  th:text="${#strings.isEmpty(shop.plateName)?'':shop.plateName}"></i>-
                            <i  th:text="${#strings.isEmpty(shop.address)?'':shop.address}"></i>
                        </s>
                    </p>
                    <div  class="houseItemIcon">
                        <span th:class="tese_+${i.index+1}" th:if="${!#lists.isEmpty(shop.houseTraits) and i.index &lt; 3}" th:each="houseTraits,i:${#strings.toString(shop.houseTraits).split(',')}"
                              th:text="${houseTraits}">
                        </span>
                    </div>
                    <!--<p class="person" th:text = "${#strings.isEmpty(shop.spantime)? '' : shop.spantime+'更新'}">22天前更新</p>-->
                </div>
                    <div class="infRight" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '5'}">
                        <p class="infRightPrise">
                            <s th:text = "${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0' ?'面议': ((#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')))}"></s>
                            <i th:text = "${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0' ?'':'万'}"></i>
                            <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">售</span>-->
                        </p>
                        <p><i><b th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'':#strings.toString(shop.unitPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/m²'}">13007元/m²</b></i></p>
                    </div>

                    <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '4'}">
                        <p class="infRightPrise">
                            <s th:block th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'面议': ((#strings.indexOf(shop.unitPrice,'.') eq -1 ? shop.unitPrice:#strings.toString(shop.unitPrice).replaceAll('0+?$','').replaceAll('[.]$','')))}"></s>
                            <i th:block th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0' ?'':'元/m²·天'}"></i>
                            <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">租</span>-->
                        </p>
                        <p><b th:text="${#strings.isEmpty(shop.price) or  #strings.toString(shop.price) eq '0.0' ?'':#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')+'元/月'}">1.77 元/m²·天</b></p>
                    </div>
                </li>
            </ul>
            <div class="cl"></div>
            <div class="page">
            <div th:include="fragment/page :: page"></div>
        </div>
    </div>

    </div>
    <!--右侧内容-->
    <div class="shopManageRight">
        <!--门店地址-->
        <div th:include = "agent/fragmentinter::storeOtherAddress"></div>
        <!--推荐经纪人-->
        <!--<div th:include = "agent/fragmentinter::companyAgent"></div>-->
        <!--岗位急聘-->
        <div th:include = "agent/fragmentinter::companyUrgent"></div>
    </div>
</div>
</div>

<script type="text/javascript">
    //筛选的确定按钮显示隐藏
    function price(priceIdName) {
        if($("#"+priceIdName).length >0){
            $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
            $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
            var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
            var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
            if (num1 == "" && num2 != "") {
                $("#" + priceIdName + " input").eq(0).val("0");
                $("#" + priceIdName + " input").eq(2).show();
            } else if (num2 == "" && num1 != "") {
                $("#" + priceIdName + " input").eq(2).show();
            } else if (num1 != "" || num2 != "") {
                $("#" + priceIdName + " input").eq(2).show();
            } else {
                $("#" + priceIdName + " input").eq(2).hide();
            }
        }
    }

    price("Search_BuildAreaDomOk");
    price("Search_sPriceDomOk");
    $("#Search_sPriceDomOk input").keyup(function () {
        price("Search_sPriceDomOk");
    })
    $("#Search_rPriceDomOk input").keyup(function () {
        price("Search_rPriceDomOk");
    })
    $("#Search_uPriceDomOk input").keyup(function () {
        price("Search_uPriceDomOk");
    })
    $("#Search_BuildAreaDomOk input").keyup(function () {
        price("Search_BuildAreaDomOk");
    })
    $("#Search_sPriceDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            $("#Search_Btn_Search1").click()
        }
    });
    $("#Search_rPriceDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            $("#Search_Btn_Search2").click()
        }
    });
    $("#Search_uPriceDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            $("#Search_Btn_Search3").click()
        }
    });
    $("#Search_BuildAreaDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            $("#Search_Btn_Searchs").click()
        }
    });
    //此段js用于手填面积和价格连接拼接处理
    function __doPostBack(pager1, page) {
        var url = window.location.pathname;
        if(url.indexOf('cp') != -1) {//链接中包含‘cp’
            url == url;
        }else if( url.lastIndexOf("/")){
            url +='/';
        }
        if (pager1 == "Search$Btn_Search1") {
            var priceBegin = $("#sminPrice").val();
            var priceEnd = $("#smaxPrice").val();
            var ref1 = url.replace(/-sn[0-9]\d*/,''); //k最小值
            var ref2 = ref1.replace(/-sm[0-9]\d*/,'');  //x最大值
            if (parseInt(priceEnd) < parseInt(priceBegin)){
                priceEnd = [priceBegin,priceBegin=priceEnd][0];
            }
            if(priceBegin != "" && priceBegin != 0) {
                ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-rn[0-9]\d*/, '').replace(/\/rn[0-9]\d*/, '\/').replace(/-rm[0-9]\d*/, '').replace(/\/rm[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-un[0-9]\d*/, '').replace(/\/un[0-9]\d*/, '\/').replace(/-um[0-9]\d*/, '').replace(/\/um[0-9]\d*/, '\/') + "-sn" + priceBegin;
            }
            if(priceEnd != "") {
                ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-rn[0-9]\d*/, '').replace(/\/rn[0-9]\d*/, '\/').replace(/-rm[0-9]\d*/, '').replace(/\/rm[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-un[0-9]\d*/, '').replace(/\/un[0-9]\d*/, '\/').replace(/-um[0-9]\d*/, '').replace(/\/um[0-9]\d*/, '\/') + "-sm" + priceEnd;
            }
            location.href = ref2;
        }
        if (pager1 == "Search$Btn_Search2") {
            var priceBegin = $("#rminPrice").val();
            var priceEnd = $("#rmaxPrice").val();
            var ref1 = url.replace(/-rn[0-9]\d*/,''); //k最小值
            var ref2 = ref1.replace(/-rm[0-9]\d*/,'');  //x最大值
            if (parseInt(priceEnd) < parseInt(priceBegin)){
                priceEnd = [priceBegin,priceBegin=priceEnd][0];
            }
            if(priceBegin != "" && priceBegin != 0) {
                ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-un[0-9]\d*/, '').replace(/\/un[0-9]\d*/, '\/').replace(/-um[0-9]\d*/, '').replace(/\/um[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-sn[0-9]\d*/, '').replace(/\/sn[0-9]\d*/, '\/').replace(/-sm[0-9]\d*/, '').replace(/\/sm[0-9]\d*/, '\/') + "-rn" + priceBegin;
            }
            if(priceEnd != ""){
                ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-un[0-9]\d*/,'').replace(/\/un[0-9]\d*/,'\/').replace(/-um[0-9]\d*/,'').replace(/\/um[0-9]\d*/,'\/');
                ref2 = ref2.replace(/-sn[0-9]\d*/,'').replace(/\/sn[0-9]\d*/,'\/').replace(/-sm[0-9]\d*/,'').replace(/\/sm[0-9]\d*/,'\/') + "-rm" + priceEnd;
            }
            location.href = ref2;
        }
        if (pager1 == "Search$Btn_Search3") {
            var priceBegin = $("#uminPrice").val();
            var priceEnd = $("#umaxPrice").val();
            var ref1 = url.replace(/-un[0-9]\d*/,''); //k最小值
            var ref2 = ref1.replace(/-um[0-9]\d*/,'');  //x最大值
            if (parseInt(priceEnd) < parseInt(priceBegin)){
                priceEnd = [priceBegin,priceBegin=priceEnd][0];
            }
            if(priceBegin != "" && priceBegin != 0){
                ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-sn[0-9]\d*/,'').replace(/\/sn[0-9]\d*/,'\/').replace(/-sm[0-9]\d*/,'').replace(/\/sm[0-9]\d*/,'\/');
                ref2 = ref2.replace(/-rn[0-9]\d*/,'').replace(/\/rn[0-9]\d*/,'\/').replace(/-rm[0-9]\d*/,'').replace(/\/rm[0-9]\d*/,'\/') + "-un" + priceBegin;
            }
            if(priceEnd != ""){
                ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                ref2 = ref2.replace(/-sn[0-9]\d*/,'').replace(/\/sn[0-9]\d*/,'\/').replace(/-sm[0-9]\d*/,'').replace(/\/sm[0-9]\d*/,'\/');
                ref2 = ref2.replace(/-rn[0-9]\d*/,'').replace(/\/rn[0-9]\d*/,'\/').replace(/-rm[0-9]\d*/,'').replace(/\/rm[0-9]\d*/,'\/') + "-um"+ priceEnd;
            }
            location.href = ref2;
        }
        if (pager1 == "Search$Btn_Searchs") {
            var areaBegin = $("#ominArea").val();
            var areaEnd = $("#omaxArea").val();
            var ref1 = url.replace(/-la[0-9]\d*/,''); //y最小值
            var ref2 = ref1.replace(/-ha[0-9]\d*/,'');  //e最大值
            if (parseInt(areaEnd) < parseInt(areaBegin)){
                areaEnd = [areaBegin,areaBegin=areaEnd][0];
            }
            if(areaBegin != "" && areaBegin != 0)
                ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-la" + areaBegin;
            if(areaEnd != "")
                ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') +"-ha" + areaEnd;
            location.href = ref2;
        }
    }
</script>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>