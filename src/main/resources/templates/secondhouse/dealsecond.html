
<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="'沈阳' + ${seoTitle + seoSubName} + '二手房_沈阳' + ${seoTitle + seoSubName} + '二手房出售_沈阳二手房买卖信息 - 房小二网'"></title>
    <meta name="keywords" th:content="'沈阳' + ${seoTitle + seoSubName} + '二手房,沈阳' + ${seoTitle + seoSubName} + '二手房价格,沈阳二手房信息,二手房中介,沈阳二手房网'" />
    <meta name="description" th:content="'房小二网沈阳二手房为您提供海量真实的沈阳' + ${seoTitle + seoSubName} + '二手房信息，沈阳二手房经纪人信息，二手房源信息，及时的二手房出售信息以及二手房中介信息，带来更好的二手房买卖体验。'" />
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/fang2">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2017.css" />

    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/contractList.css?t=20200220"/>
<!--    <link rel="stylesheet" type="text/css" href="/css/contractList.css?t=20190809"/>-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/second/dealsecond.css?t=20200115"/>
<!--    <link rel="stylesheet" type="text/css" href="/css/dealsecond.css?t=20190809"/>-->

<body>
<!-- 二手房成交房源列表 -->
<style>
    /* 右侧推荐列表 */
    /* .recommend dd:hover .hideA{display:block}
    .recommend dd:hover .hideD{display:none} */
    .recommend dd{height: auto;}
    .hideAhover{display:block !important}
    .hideAnone{display:none !important}
    .recommend dd>a.hideA{display:none;cursor:pointer; float: left; width: 100%;}
    .recommend dd>a.hideA img{
        width: 100px;
        height:  70px;
        margin: 0 5px 0 0;
        display:  block;
    }
    .recommend dd>a.hideA .hideATitle{
        display:  block;
        width: 48%;
        text-overflow: ellipsis;
        white-space:  pre;
        overflow:  hidden;
        color:  #333;
        font-weight:  normal;
        font-size:  14px;
    }
    .recommend dd>a.hideA span{
        display: block;
        line-height: 22px;
        font-size:  14px;
        color: #ff5200;
        font-weight:  bold;
    }
    .recommend dd>a.hideA p{
        display: block;
        line-height: 24px;
        font-weight:  normal;
    }
    .recommend dd>a.hideA p span{
        display: inline-block;
        font-size: 12px;
        color: #333;
        margin-right: 3px;
        font-weight: normal;
        width: 60px;
        overflow:  hidden;
        text-overflow:  ellipsis;
        white-space:  pre;
    }
    .recommend dd>a.hideA p span+span{margin-right:0;width: auto;}
    .recommend dd>a.hideA p s{
        display: block;}

    .recommend dd>a.hideA span s{
        color:  #999;
        margin-right: 5px;
    }
    /* 二手房列表页新增广告位 */
    .listBannerImg{
        overflow: hidden;
        width: 893px;
        margin-bottom: 20px;
    }
    .listBannerImg a{
        display: block;
        float: left;
        width: 437px;
        height: 80px;
    }
    .listBannerImg a+a{
        margin-left: 18px;
    }
    .listBannerImg a img{
        width: 100%;
        height: 100%;
    }
    /*改*/
    .shortRight .rightQzQgBtn a {
        width: 168px;
        line-height: 30px;
        height: 30px;
        padding: 0;
        margin-bottom: 14px;
    }
    .shortRight .rightQzQgBtn a i {
        display: none;
    }
	/* 增加安心好房样式修改 */
	.sort_new {
		border-bottom: 2px solid #ff5200;
	}
	
	.new_label {
		padding: 0 30px;
		text-align: center;
		line-height: 45px;
		font-size: 16px;
		display: inline-block;
	}
	
	#sortParamNewest {
		margin: 0;
		
	}
	
	#sortParamNewest>a:hover {
		/* background: #333; */
		color: #333;
		text-decoration: none;
	}
	
	.screen_new {
		width: calc(100% - 40px);
		height:53px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #ededed;
		margin-left: 20px;
	}
	
	.new_hover {
		background: #ff5200;
		color: #fff;
	}
	.new_hover:hover{
		color: #fff !important;
	}
	.anxin_icon{
		display: flex;
		line-height: 21px;
		font-size: 13px;
		border: 1px solid #ff5200;
		margin-left: 10px;
	}
	.anxin_icon_l{
		width:33px;
		height:21px;
		color: #fff;
		text-align: center;
		background:linear-gradient(90deg,rgba(255,144,0,1) 0%,rgba(255,82,0,1) 100%);
	}
	.anxin_icon_r{
		width:33px;
		height:21px;
		color: #ff5200;
		text-align: center;
	}
	/* 增加安心好房样式修改 */
</style>
<form name="form1" method="post" action="" id="form1">
    <!--隐藏域-->
    <input type="hidden" id="names" th:value="${chooseName}">
    <input type="hidden" id="chooseType" th:value="${chooseType}">
    <input type="hidden" id="chooseId" th:value="${chooseId}">
    <input type="hidden" id="choose_region_id" th:value="${choose_region_id}">
    <!--隐藏域-->
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=10"></div>

    <div class="main">
        <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleHouses/" target="_blank">沈阳二手房</a> &gt;
            <a th:if="${isSubway == 0}" href="/dealSales/">成交房源</a>
            <a th:if="${isSubway == 1}" href="/dealSales/z1">成交房源</a>
        </div>
        <div id="option">
            <ul>
                <li class="fenlei">
                    <p>位置：</p>
                    <a id="btnRegion" th:each="r,i:${region}"  onclick="showIndex(this)" th:if="${i.index eq 0 }" th:href="${r.url}"  class="">
                        <b class="listIcon listIcon_dw"></b>区域<i></i></a>
                    <a id="btnSubway" th:each="z,i:${location}"  onclick="showIndex(this)" th:if="${i.index eq 1 }" th:href="${z.url}"  class="">
                        <b class="listIcon listIcon_ditie"></b>地铁<i></i></a>
                    <a href="/saleVillages/"><b class="listIcon listIcon_esf"></b>小区<i></i></a>
                    <a href="/salemap"><b class="listIcon listIcon_map"></b>地图<i></i></a>
                </li>
                <!--区域-->
                <li id="Search_zf" class="leibie" style="display: none">
                    <a th:each="r:${region}" th:href="${r.url}"  onclick="showIndex(this)" th:text="${#strings.toString(r.name).replaceAll('全部','不限')}" th:id="'r'+${r.id}" th:class="${r.selected}? 'hover':''"></a><br>
                    <span th:if="${plate}">
                        <a th:each="j:${plate}" th:text="${#strings.toString(j.name).replaceAll('全部','不限')}"  onclick="showIndex(this)" th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
                    </span>
                </li>
                <!--地铁-->
                <li id="Search_ditie" class="leibie" style="display: none">
                    <a th:each="b:${subWayId}" th:href="${b.url}"  onclick="showIndex(this)" th:text="${#strings.toString(b.name).replaceAll('全部','不限')}" th:id="'r'+${b.id}" th:class="${b.selected}? 'hover':''"></a><br>
                    <span th:if="${subwayStation}">
                        <a th:each="q:${subwayStation}"  onclick="showIndex(this)" th:text="${#strings.toString(q.name).replaceAll('全部','不限')}" th:href="${q.url}" th:id="'j'+${q.id}" th:class="${q.selected}? 'hover':''"></a>
                    </span>
                </li>
                <script type="text/javascript">
                    $(function () {
                        var r_or_s = location.pathname;
                        if (r_or_s.indexOf('z1') != -1) {
                            $("#btnSubway").attr("class","hover");
                            $("#Search_ditie").css('display','block');
                        }
                        else {
                            $("#btnRegion").attr("class","hover");
                            $("#Search_zf").css('display','block')
                        }
                    })
                </script>
                <li ><p>总价：</p>
                    <a th:each="p:${price}" th:href="${p.url}"  onclick="showIndex(this)" th:text="${#strings.toString(p.name).replaceAll('全部','不限')}" th:id="'p'+${p.id} " th:class="${p.selected}?'hover':''">></a>
                    <div id="Search_PriceDomOk">
                        <label><input name="minPrice" id="minPrice" maxlength="4" type="text" th:value="${minPrice}" > - <input name="maxPrice" id="maxPrice" maxlength="4" type="text" th:value="${maxPrice}" > 万 <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button"></label>
                    </div>
                </li>
                <!--</div>-->
                <!--<div id="Search_BuildAreaDom">-->
                <li ><p>面积：</p>
                    <a th:each="a:${area}" th:href="${a.url}" onclick="showIndex(this)" th:text="${#strings.toString(a.name).replaceAll('全部','不限')}" th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
                    <div id="Search_BuildAreaDomOk">
                        <label><input name="minArea" id="minArea" maxlength="5" type="text" th:value="${minArea}"> - <input name="maxArea" id="maxArea" maxlength="5" type="text" th:value="${maxArea}"> ㎡ <input onclick="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button"></label>
                    </div>
                </li>
                <!--</div>-->
                <!--<div id="Search_RoomDom">-->
                <li ><p>户型：</p>
                    <a th:each="l:${room}" th:href="${l.url}"  onclick="showIndex(this)" th:text="${#strings.toString(l.name).replaceAll('全部','不限')}" th:id="'l'+${l.id}" th:class="${l.selected}?'hover':''">></a>
                </li>
                <!--</div>-->
            </ul>
        </div>
        <div id="option_other">
            <ul>
                <li><p>更多：</p>
                    <div class="select_box">
                        <div class="select_info">朝向不限</div>
                        <ul>
                            <li th:each="f:${forward}"> <a th:href="${f.url}" onclick="showIndex(this)" th:text="${f.id eq '' ? '朝向不限':f.name}" th:class="${f.selected}? 'hover':''"></a></li>
                        </ul>
                    </div>
                    <div class="select_box">
                        <div class="select_info">装修不限</div>
                        <ul>
                            <li th:each="d:${decoration}">  <a th:href="${d.url}" onclick="showIndex(this)" th:text="${d.id eq '' ? '装修不限':d.name}" th:class="${d.selected}? 'hover':''">></a></li>
                        </ul>
                    </div>
                    <div class="select_box">
                        <div class="select_info">楼层不限</div>
                        <ul>
                            <li th:each="m:${floor}"> <a th:href="${m.url}" onclick="showIndex(this)" th:text="${m.id eq '' ? '楼层不限':m.name}" th:class="${m.selected}?'hover'"></a></li>
                        </ul>
                    </div>
                    <!--<div class="select_box">
                        <div class="select_info">来源不限</div>
                        <ul>
                            <li th:each="g:${memberType}">
                                <a th:href="${g.url}" th:if="${g.id eq ''}"  onclick="showIndex(this)" th:text="来源不限" th:class="${g.selected}? 'hover':''">
                                </a>
                                <a th:href="${g.url}" th:if="${g.name eq '个人'}"  onclick="showIndex(this)" th:text="个人" th:class="${g.selected}? 'hover':''">
                                </a>
                                <a th:href="${g.url}" th:if="${g.name eq '经纪人'}"  onclick="showIndex(this)" th:text="经纪人" th:class="${g.selected}? 'hover':''">
                                </a>
                            </li>
                        </ul>
                    </div>-->
                </li>
            </ul>
        </div>
        <div id="option_info" style="display: none">
            <b >已选：</b>
            <!--小区名-->
            <div th:if="${!#strings.isEmpty(subId) and !#strings.isEmpty(sub?.title)}">
                <span class="condition"><th:block th:text="${sub.title}"></th:block></span>
                <i class="cleanUrl" id="clearSubName" th:value="${'-v'+subId}"></i>
            </div>
            <script>
                $("#clearSubName").click(function () {
                    var nowUrl = location.pathname;
                    var clrerUrl = '[[${subId}]]';
                    var newUrl = nowUrl.replace("-v"+clrerUrl,"");
                    window.location.href = newUrl;
                })
            </script>
            <!--区域-->
            <div th:each="r:${region}" th:if="${r.selected and !#strings.isEmpty(r.id)}" th:onclick="${'removeSelect('+1+')'}">
                <span class="condition"><th:block th:text="${r.name}"></th:block></span>
                <i class="cleanUrl" ></i>
            </div>
            <!--板块-->
            <div th:each="p:${plate}" th:if="${p.selected and  !#strings.isEmpty(p.id)}" th:onclick="${'removeSpan('+1+')'}">
                <span class="condition"><th:block th:text="${p.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
            <!--地铁线-->
            <div th:each="s:${subway}" th:if="${s.selected and  !#strings.isEmpty(s.id)}" th:onclick="${'removeSelect('+1+')'}" >
                <span class="condition"><th:block th:text="${s.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
            <!--地铁站-->
            <div th:each="s:${subwayStation}" th:if="${s.selected and  !#strings.isEmpty(s.id)}" th:onclick="${'removeSpan('+1+')'}">
                <span class="condition"><th:block th:text="${s.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
            <!--价格-->
            <div th:each="p:${price}" th:if="${p.selected and !#strings.isEmpty(p.id)}" th:onclick="${'removeSelect('+3+')'}">
                <span class="condition"><th:block th:text="${p.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
            <!--面积-->
            <div th:each="p:${area}" th:if="${p.selected and !#strings.isEmpty(p.id)}" th:onclick="${'removeSelect('+4+')'}">
                <span class="condition"><th:block th:text="${p.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
            <!--户型-->
            <div th:each="l:${room}" th:if="${l.selected and !#strings.isEmpty(l.id)}" th:onclick="${'removeSelect('+5+')'}">
                <span class="condition"><th:block th:text="${l.name}"></th:block></span>
                <i class="cleanUrl" ></i>
            </div>
            <!--朝向-->
            <div th:each="d:${forward}" th:if="${d.selected and !#strings.isEmpty(d.id)}" th:onclick="${'removeSelectClick('+0+')'}">
                <span class="condition"><th:block th:text="${d.name}"></th:block></span>
                <i class="cleanUrl" ></i>
            </div>
            <!--装修-->
            <div th:each="d:${decoration}" th:if="${d.selected and !#strings.isEmpty(d.id)}" th:onclick="${'removeSelectClick('+1+')'}">
                <span class="condition"><th:block th:text="${d.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
            <!--楼层-->
            <div th:each="d:${floor}" th:if="${d.selected and !#strings.isEmpty(d.id)}" th:onclick="${'removeSelectClick('+2+')'}">
                <span class="condition"><th:block th:text="${d.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
            <!--来源-->
            <div th:each="g:${memberType}" th:if="${g.selected and !#strings.isEmpty(g.id)}" th:onclick="${'removeSelectClick('+3+')'}">
                <span class="condition"><th:block th:text="${g.name}"></th:block></span>
                <i class="cleanUrl"></i>
            </div>
        </div>
        <script>
            $(function () {
                //条件区域加载渲染
                if($(".condition").text() != ""){
                    $("#option_info").css("display","block");
                }
                loadCondition();
            });
            function loadCondition() {
                var isAdd = false;
                //非手填项渲染
                selectPrice(isAdd);
                //清空全部按钮
                if($(".condition").text() != ""){
                    $("#option_info").append("<a>清空筛选条件</a> ");
                    $("#option_info a:last").attr("href", "/dealSales/").attr("class", "clean");
                }
            }
            function removeSelect(index){
                $("#option ul li:eq("+index+") a:eq(0)").click();
            }
            function removeSelectClick(index){
                $('.select_box:eq('+index+') a:eq(0)').click();
            }
            function removeSpan(index){
                $("#option ul li:eq("+index+") span a:eq(0)").click();
            }
            function showIndex(obj) {
                obj.click();
            }
            function selectPrice(isAdd) {
                //总价、面积筛选条件渲染
                var bp = $("#option input[id='minPrice']").val();
                var ep = $("#option input[id='maxPrice']").val();
                var ba = $("#option input[id='minArea']").val();
                var ea = $("#option input[id='maxArea']").val();
                if(bp != "" || ep != ""){
                    if (!isAdd) {
                        $("#option_info").show();
                        isAdd = true;
                    }
                    var str = "";
                    bp = bp == "" ? 0 : bp;
                    if(ep == ""){
                        str = "大于等于" + bp;
                    }else{
                        if(bp == ""){
                            $("#option input[id='bp']").val("0");
                        }
                        str =  bp + "-" + ep;
                    }
                    $("#option_info").append("<div><span class='condition'>" + str + "万元</span><i></i> </div>");
                    $("#option_info div:last").attr("onclick", "removeSelect(" + 3 + ");");
                }
                if(ba != "" || ea != ""){
                    if (!isAdd) {
                        $("#option_info").show();
                        isAdd = true;
                    }
                    var str = "";
                    ba = ba == "" ? 0 : ba;
                    if(ea == ""){
                        str = "大于等于" + ba;
                    }else{
                        if(ba == ""){
                            $("#option input[id='ba']").val("0");
                        }
                        str = ba + "-" + ea;
                    }
                    $("#option_info").append("<div><span class='condition'>" + str + "㎡</span><i></i> </div>");
                    $("#option_info div:last").attr("onclick", "removeSelect(" + 4 + ");");
                }
            }
        </script>
        <div class="cl"></div>
        <div id="Search_PanSaleDom">

            <div class="search_more hid">
                <ul>
                    <li><p>更多找房条件：</p>
                        <div class="select_box">
                            <div class="select_info">朝向不限</div>

                            <ul>

                            </ul>
                        </div>
                        <div class="select_box">
                            <div class="select_info">装修不限</div>
                            <ul>

                            </ul>
                        </div>
                        <div class="select_box">
                            <div class="select_info">楼层不限</div>
                            <ul>

                            </ul>
                        </div>
                    </li>

                </ul>
            </div>
            <script>
                var i = 0;
                for (i = 0; i < $("#Search_PanSaleDom .select_info").length; i++) {
                    if ($("#Search_PanSaleDom .select_box").eq(i).find(".hover").length > 0) {
                        $("#Search_PanSaleDom .select_info").eq(i).text($("#Search_PanSaleDom .select_box").eq(i).find(".hover").text())

                    }
                }

            </script>
            <script type="text/javascript">
                function price(priceIdName) {
                    $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
                    $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
                    var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
                    var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
                    if (num1 == "" && num2 != "") {
                        $("#" + priceIdName + " input").eq(0).val("0");
                        $("#" + priceIdName + " input").eq(2).show();
                    } else if (num2 == "" && num1 != "") {
                        $("#" + priceIdName + " input").eq(2).show();
                    } else if (num1 != "" || num2 != "") {
                        $("#" + priceIdName + " input").eq(2).show();
                    } else {
                        $("#" + priceIdName + " input").eq(2).hide();
                    }
                }

                price("Search_BuildAreaDomOk");
                price("Search_PriceDomOk");
                $("#Search_PriceDomOk input").keyup(function () {
                    price("Search_PriceDomOk");
                })
                $("#Search_BuildAreaDomOk input").keyup(function () {
                    price("Search_BuildAreaDomOk");
                })
                $("#Search_PriceDomOk").keydown(function (event) {
                    if (event.keyCode == 13) {
                        // $("#Search_Btn_Search1").click()
                    }
                });
                $("#Search_BuildAreaDomOk").keydown(function (event) {
                    if (event.keyCode == 13) {
                        $("#Search_Btn_Searchs").click()
                    }
                });
            </script>

        </div>
        <div id="main">
            <div id="left" style="border:none">
                <div></div>
                <!--列表页新增广告-->
                <div class="listBannerImg" th:if="${!#lists.isEmpty(advert.recommand) && #lists.size(advert.recommand) eq 2}">
                    <a th:href="${advert.recommand[0].url}" target="_blank" class="bannerLeft"><img th:src="${advert.recommand[0].image}" alt=""></a>
                    <a th:href="${advert.recommand[1].url}" target="_blank" class="bannerRight"><img th:src="${advert.recommand[1].image}" alt=""></a>
                </div>
                <div class="secNewLeft" style="border: 1px solid #ededed">
                <div class="sort sort_new" >
					<!-- // 增加安心好房/修改样式后 -->
					<p id="sortParamNewest">
						<a id="2"  href="/saleHouses/" class="new_label sortParamNewA">全部</a>
						<a id="1"  href="/saleHouses/-W1-u200" class="new_label sortParamNewA">真房源</a>
						<!--<a th:if="${#strings.equals(forAnxuan, '1')}"  href="/saleHouses/-S8" class="new_label sortParamNewA">安心好房</a>-->
						<a id="8"  href="/saleHouses/-W8" class="new_label sortParamNewA">新房房源</a>
						<a id="6" href="/saleHouses/-W6"  class="new_label sortParamNewA">最新房源</a>
						<a id="4" href="/saleHouses/-W4"  class="new_label sortParamNewA">视频看房</a>
						<!--<a href="" class="new_label dealCodition new_hover">成交房源</a>-->
                    </p>
                  
                   <!-- <p id="sort_qh">
                        <a id="line"  href=''  class=''></a>
                    </p>-->
                   
                </div>
				<div class="screen_new">
					<div style="margin-left: 10px;font-size: 14px;">
						为您找到以下<span style="color: #FF5200;">沈阳</span>二手房
					</div>
					<p id="screening_Items">
						<a id="0" href="">综合排序</a>
						<a id="3" href="">总价</a>
						<a id="7" href="">单价</a>
						<a id="5" href="">面积</a>
						<a id="9" href="" >时间</a>
						<script>
							$(document).ready(function(){
								var ref = location.pathname;
								if(ref.indexOf('dealSales/') == -1) ref +='/';
								var y = ref.split('/dealSales/');
								var x = y.length > 1 ? y[1] : "";
								if(x.indexOf('o') != -1){
									var num = x.split('o')[1].substring(0,1);
									if(num == 0){
										$("#screening_Items a[id ='"+ num+"']").addClass("hover");
									}else{
										if(num%2 == 1){
											$("#screening_Items a[id ='"+ num+"']").addClass("sort_jg");
											$("#screening_Items a[id ='"+ num+"']").addClass("down");
											$("#screening_Items a[id ='"+ num+"']").attr("id",num-1);
										}else{
											num = parseInt(num) +1;
											$("#screening_Items a[id ='"+ num+"']").addClass("sort_jg");
											$("#screening_Items a[id ='"+ num+"']").addClass("up");
										}
									}
								}else{
									$("#screening_Items a:eq(0)").addClass("hover");
								}
								var ref2 = ref.replace(/-o[0-9]/,'');
								$("#screening_Items a").each(function () {
									var ids = $(this).attr("id");
									var ref3 = ref2 + '-o' + ids;
									$(this).attr("href",ref3);
								});
							});
						</script>
					</p>
					<script type="text/javascript">
					    $(function () {
					        var p_or_l = location.pathname;
					        if (p_or_l.indexOf("h1") == -1){
					            $("#replie").show();
					            $("#reprow").hide();
					            $("#piece").attr("class","");
					            $("#line").attr("class","hover");
					        }else {
					            $("#replie").hide();
					            $("#reprow").show();
					            $("#piece").attr("class","hover");
					            $("#line").attr("class","");
					        }
					    })
					    $("#line").click(function () {
					        var url = location.pathname;
					        if(url.indexOf("-h1") != -1){
					            $("#line").attr("href",url.replace("-h1",""));
					        }
					        else {
					            $("#line").attr("href",url.replace("h1",""));
					        }
					    })
					</script>
					</div>
			 <!--   <div class="tese">
                    <p>特色:</p>
                    <div>
                        <a th:id ="${'tese'+u.id}" th:each="u,i:${feature}" th:if="${u.id ne ''}" th:href="${u.url}" th:text="${u.name}" th:class="${u.selected}? 'hover':''">></a>
                        <script >
                            $(".tese a").click(function () {
                                var teseid = $(this).attr("id").replace("tese", "");
                                var ref = location.pathname;
                                if(ref.indexOf('dealSales/') == -1) ref +='/';
                                ref = ref.replace(/[-]*[n][0-9]?$/, "");
                                if ($(this).attr("class") == null) {
                                    $(this).attr("href", ref + "-u" + teseid);
                                }
                                else {
                                    var outref = ref.replace("-u"+teseid,"");
                                    $(this).attr("href", outref);
                                }
                            })
                        </script>
                    </div>
                </div>-->
                <div class="cl"></div>
                <!--<div class="content">小二为你找到<i th:text="${msg}"></i>个符合条件的房源</div>-->

                <div class="contentMain" id="saleCol">
                    <div class="warning" th:if="${#lists.isEmpty(secondhouse)}">
                        <p>
                            很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。
                            <br> 请重试<!--<a th:href="@{/helpSearch}" target="_blank">点击免费体验购房服务方案>></a>-->
                        </p>
                    </div>
                    <!--以下为list形式-->
                    <div id=replie class="house_left">
                        <ul class="bargainHouseList" th:each="sale:${secondhouse}">
                            <li>
                                <div class="houseImg">
                                    <a th:if="${sale.spanTime &lt; 31 and #session?.getAttribute('sessionId') == null}" target="_blank"  data-toggle="modal" href="#login">
                                        <img th:src="${#strings.isEmpty(sale.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sale.pic}" th:alt="${sale.titile}">
                                    </a>
                                    <a th:if="${sale.spanTime &gt; 30 or #session?.getAttribute('sessionId') != null}" th:href="${'/dealSale/'+sale.houseId+'.htm'}" target="_blank">
                                        <img th:src="${#strings.isEmpty(sale.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sale.pic}" th:alt="${sale.titile}">
                                    </a>
                                </div>
                                <div class="houseInfo">
                                    <div class="houseTitle">
                                        <a th:if="${sale.spanTime &lt; 31 and #session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login">
                                            <th:block th:text="${sale.titile}"></th:block>
                                        </a>
                                        <a th:if="${sale.spanTime &gt; 30 or #session?.getAttribute('sessionId') != null}" th:href="${'/dealSale/'+sale.houseId+'.htm'}" target="_blank">
                                            <th:block th:text="${sale.titile}"></th:block>
                                        </a>
                                    </div>
                                    <a th:if="${sale.spanTime &lt; 31 and #session?.getAttribute('sessionId') == null}" target="_blank"  data-toggle="modal" href="#login">
                                        <div class="houseInfoLeft">
                                            <p><th:block th:text="${#strings.isEmpty(sale.forward)?'':sale.forward}"></th:block><i class="gang" th:if="${!#strings.isEmpty(sale.forward)  and !#strings.isEmpty(sale.fitmentTypeName)}"></i><th:block th:text="${#strings.isEmpty(sale.fitmentTypeName)?'':sale.fitmentTypeName}"></th:block><i class="gang"></i><th:block th:if="${!#strings.isEmpty(sale.floorDesc) and !#strings.isEmpty(sale.totalFloorNumber)}" th:text="${sale.floorDesc+'/'+sale.totalFloorNumber}"></th:block><i class="gang" th:if="${!#strings.isEmpty(sale.buildingtime)}"></i><th:block th:text="${#strings.isEmpty(sale.buildingtime)?' ':sale.buildingtime+'年建筑'}"></th:block></p>
                                            <!--                                        <p><th:block th:if="${!#strings.isEmpty(sale.floorDesc) and !#strings.isEmpty(sale.totalFloorNumber)}" th:text="${sale.floorDesc+'/'+sale.totalFloorNumber}"></th:block><span th:if="${!#strings.isEmpty(sale.floorDesc) and !#strings.isEmpty(sale.totalFloorNumber)}">|</span>建筑年代：<th:block th:text="${#strings.isEmpty(sale.buildingtime)?'暂无资料':sale.buildingtime}"></th:block></p>-->
                                            <p><th:block th:text="${#strings.isEmpty(sale.salePrice)?'':'挂牌'+sale.salePrice+'万'}"></th:block><i class="gang" th:if="${!#strings.isEmpty(sale.salePrice)}"></i><th:block th:text="${#strings.isEmpty(sale.cycle)?'':'成交周期'+sale.cycle+'天'}"></th:block></p>
                                            <div th:if="${sale.MemberType eq '2'}" class="gaiagent">
                                                <div class="agentShowImg" th:if="${!#strings.isEmpty(sale.Avatar)}"><img  th:src="${sale.Avatar}" alt=""></div>
                                                <div class="agentShowImg" th:if="${#strings.isEmpty(sale.Avatar)}"><img  src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt=""></div>
                                                <i>
                                                    <th:block  th:text="${sale.agency}"></th:block>
                                                    <!--                                                <th:block th:if="${!#strings.isEmpty(sale.spanTime)}" th:text="${sale.spanTime+'更新'}"></th:block>-->
                                                </i>
                                                <i><th:block th:if="${!#strings.isEmpty(sale.intermediaryName)}" th:text="${sale.intermediaryName}"></th:block></i>
                                            </div>
                                            <div th:if="${sale.MemberType eq '1'}">
                                                <img  src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
                                                <i><th:block  th:text="${'个人'}"></th:block></i>
                                            </div>
                                            <!--                                        <p class="agentName">经纪人： <th:block th:text="${#strings.isEmpty(sale.agency)?'暂无资料':sale.agency}"></th:block></p>-->
                                        </div>
                                        <div class="houseInfoMiddle">
                                            <div th:if="${sale.spanTime &lt; 31}"><p>近<span>30</span>天内成交</p></div>
                                            <div th:unless="${sale.spanTime &lt; 31}"><p><span><th:block th:text="${#strings.isEmpty(sale.dealTime)?'暂无资料':sale.dealTime}"></th:block></span></p></div>
                                            <div class="gaiMID"><th:block th:text="${#strings.isEmpty(sale.intermediaryName)?'暂无资料':sale.intermediaryName+'成交'}"></th:block></div>
                                        </div>
                                    </a>
                                    <a th:if="${sale.spanTime &gt; 30 or #session?.getAttribute('sessionId') != null}" th:href="${'/dealSale/'+sale.houseId+'.htm'}" target="_blank">
                                        <div class="houseInfoLeft">
                                            <p><th:block th:text="${#strings.isEmpty(sale.forward)?'':sale.forward}"></th:block><i class="gang" th:if="${!#strings.isEmpty(sale.forward)  and !#strings.isEmpty(sale.fitmentTypeName)}"></i><th:block th:text="${#strings.isEmpty(sale.fitmentTypeName)?'':sale.fitmentTypeName}"></th:block><i class="gang"></i><th:block th:if="${!#strings.isEmpty(sale.floorDesc) and !#strings.isEmpty(sale.totalFloorNumber)}" th:text="${sale.floorDesc+'/'+sale.totalFloorNumber}"></th:block><i class="gang" th:if="${!#strings.isEmpty(sale.buildingtime)}"></i><th:block th:text="${#strings.isEmpty(sale.buildingtime)?' ':sale.buildingtime+'年建筑'}"></th:block></p>
                                            <!--                                        <p><th:block th:if="${!#strings.isEmpty(sale.floorDesc) and !#strings.isEmpty(sale.totalFloorNumber)}" th:text="${sale.floorDesc+'/'+sale.totalFloorNumber}"></th:block><span th:if="${!#strings.isEmpty(sale.floorDesc) and !#strings.isEmpty(sale.totalFloorNumber)}">|</span>建筑年代：<th:block th:text="${#strings.isEmpty(sale.buildingtime)?'暂无资料':sale.buildingtime}"></th:block></p>-->
                                            <p><th:block th:text="${#strings.isEmpty(sale.salePrice)?'':'挂牌'+sale.salePrice+'万'}"></th:block><i class="gang" th:if="${!#strings.isEmpty(sale.salePrice)}"></i><th:block th:text="${#strings.isEmpty(sale.cycle)?'':'成交周期'+sale.cycle+'天'}"></th:block></p>
                                            <div th:if="${sale.MemberType eq '2'}" class="gaiagent">
                                                <div class="agentShowImg" th:if="${!#strings.isEmpty(sale.Avatar)}"><img  th:src="${sale.Avatar}" alt=""></div>
                                                <div class="agentShowImg" th:if="${#strings.isEmpty(sale.Avatar)}"><img  src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt=""></div>
                                                <i>
                                                    <th:block  th:text="${sale.agency}"></th:block>
                                                    <!--                                                <th:block th:if="${!#strings.isEmpty(sale.spanTime)}" th:text="${sale.spanTime+'更新'}"></th:block>-->
                                                </i>
                                                <i><th:block th:if="${!#strings.isEmpty(sale.intermediaryName)}" th:text="${sale.intermediaryName}"></th:block></i>
                                            </div>
                                            <div th:if="${sale.MemberType eq '1'}">
                                                <img  src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
                                                <i><th:block  th:text="${'个人'}"></th:block></i>
                                            </div>
                                            <!--                                        <p class="agentName">经纪人： <th:block th:text="${#strings.isEmpty(sale.agency)?'暂无资料':sale.agency}"></th:block></p>-->
                                        </div>
                                        <div class="houseInfoMiddle">
                                            <div th:if="${sale.spanTime &lt; 31}"><p>近<span>30</span>天内成交</p></div>
                                            <div th:unless="${sale.spanTime &lt; 31}"><p><span><th:block th:text="${#strings.isEmpty(sale.dealTime)?'暂无资料':sale.dealTime}"></th:block></span></p></div>
                                            <div class="gaiMID"><th:block th:text="${#strings.isEmpty(sale.intermediaryName)?'暂无资料':sale.intermediaryName+'成交'}"></th:block></div>
                                        </div>
                                    </a>


                                    <div class="houseInfoRight" th:if="${sale.spanTime &lt; 31}">
                                        <div><span><th:block th:text="${sale.dealPrice}"></th:block></span>万</div>
                                        <div>
                                            <a class="gaixz" href="https://download.fangxiaoer.com/" target="_blank">下载APP查看成交></a>
                                            <div class="recode">
                                                <img src="https://static.fangxiaoer.com/web/images/sy/house/bargainHouseRecode.png"/>
                                            </div>
                                        </div>
                                        <a class="checkHouse" th:if="${#session?.getAttribute('sessionId') == null}"  target="_blank" style="cursor: pointer;margin-top: 0" data-toggle="modal" id="CommentListAdd" href="#login">查看成交价格&gt;</a>
                                        <a class="checkHouse" style="margin-top: 0" th:if="${#session?.getAttribute('sessionId') != null}"  th:href="${'/dealSale/'+sale.houseId+'.htm'}" target="_blank">查看成交价格&gt;</a>
                                        <a class="checkHouse" style="margin-top: 0" target="_blank" th:href="'/saleHouses/-v'+${sale.SubID}">查看同小区房源&gt;</a>
                                    </div>
                                    <div class="houseInfoRight" th:unless="${sale.spanTime &lt; 31}">
                                        <div><span><th:block th:text="${#strings.indexOf(sale.dealPrice,'.') eq -1 ? sale.dealPrice:#strings.toString(sale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$','')}"></th:block></span>万</div>
                                        <div class="price"><th:block th:text="${#strings.isEmpty(sale.dealUnitPrice)?'':#strings.indexOf(sale.dealUnitPrice,'.') eq -1 ? sale.dealUnitPrice+'元/㎡':#strings.toString(sale.dealUnitPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/㎡'}"></th:block></div>
                                        <a class="checkHouse" target="_blank" th:href="'/saleHouses/-v'+${sale.SubID}">查看同小区房源&gt;</a>
                                    </div>

                                </div>
                                <div class="cl"></div>
                            </li>
                        </ul>
                    </div>

                    <div class="cl"></div>
                </div>
                <div class="cl"></div>
                <div class="page">

                    <!-- AspNetPager V7.2 for VS2005 & VS2008  Copyright:2003-2008 Webdiyer (www.webdiyer.com) -->
                    <div th:include="fragment/page :: page"></div>
                    <!-- AspNetPager V7.2 for VS2005 & VS2008 End -->

                </div>
                </div>
            </div>
            <div id="right" class="shortRight">
                <!--列表页右侧房价评估-->
<!--                <div th:include="secondhouse/fragment::areaHighCharts"></div>-->
                <!--我要买房,求购-->
                <div th:include="secondhouse/fragment::wantBuy"></div>
                <!--画中画广告-->
                <div th:include="secondhouse/fragment::pictureadvert"></div>
                <!--列表页右侧中介广告-->
                <div th:include="secondhouse/fragment::companyadvert"></div>
            </div>
        </div>
        <div class="cl"></div>
        <div class="gxq hid">
            <h3>您可能感兴趣的房源</h3>
            <ul>
                <li>
                    <a href="" target="_blank"><img src="https://images.fangxiaoer.com/sy/esf/fy/middle/2017/07/03/59599f4d2858e.jpg" alt=""></a>
                    <a href="" target="_blank">投资首选万科明天广场 1室 1厅 1卫 46㎡精装修拎包即住</a>
                    <p><span>820<i>万</i></span>4室3厅</p>
                </li>
            </ul>
        </div>
        <div class="gxqxq" th:if="${!#lists.isEmpty(interest)}">
            <h3><span></span>您可能感兴趣的小区</h3>
            <ul>
                <li th:each="is:${interest}">
                    <a th:href="'/saleVillages/'+${is.SubID}+'/index.htm'" target="_blank">
                        <img th:src="${#strings.isEmpty(is.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':is.pic}" alt=""></a>
                    <p th:text="${is.regionName}"> </p>
                    <p th:text="${is.Name}"></p>
                    <div class="cl"></div>
                    <p th:if="${is.salecount ne 0 and is.salecount ne null}">二手房<i th:text="${is.salecount}"></i>套</p>
                    <p th:if="${is.rentcount ne 0 and is.rentcount ne null}">租房<i th:text="${is.rentcount}"></i>套</p>
                </li>
            </ul>
        </div>

        <script>
            var i = 0;
            for (i = 0; i < $("#option_other .select_info").length; i++) {
                if ($("#option_other .select_box").eq(i).find(".hover").length > 0) {
                    $("#option_other .select_info").eq(i).text($("#option_other .select_box").eq(i).find(".hover").text())
                }
            }
        </script>
        <script type="text/javascript">
            function __doPostBack(pager1, page) {
                var url = window.location.pathname;
                if (pager1 == "Search$Btn_Search1") {
                    var priceBegin = $("#minPrice").val();
                    var priceEnd = $("#maxPrice").val();
                    var ref1 = url.replace(/-k[0-9]\d*/,''); //k最小值
                    var ref2 = ref1.replace(/-x[0-9]\d*/,'');  //x最大值
                    if (parseInt(priceEnd) < parseInt(priceBegin)){
                        priceEnd = [priceBegin,priceBegin=priceEnd][0];
                    }
                    if(priceBegin != "" && priceBegin != 0)
                        ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-k" + priceBegin;
                    if(priceEnd != "")
                        ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-x" + priceEnd;
                    location.href = ref2;
                }
                if (pager1 == "Search$Btn_Searchs") {
                    var areaBegin = $("#minArea").val();
                    var areaEnd = $("#maxArea").val();
                    var ref1 = url.replace(/-y[0-9]\d*/,''); //y最小值
                    var ref2 = ref1.replace(/-e[0-9]\d*/,'');  //e最大值
                    if (parseInt(areaEnd) < parseInt(areaBegin)){
                        areaEnd = [areaBegin,areaBegin=areaEnd][0];
                    }
                    if(areaBegin != "" && areaBegin != 0)
                        ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-y" + areaBegin;
                    if(areaEnd != "")
                        ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-e" + areaEnd;
                    location.href = ref2;
                }
            }
        </script>

        <div class="cl"></div>
        <!--底部1-->
        <div th:include="fragment/fragment:: footer_list"></div>
        <div th:include="fragment/fragment::tongji"></div>
    </div>
</form>
<div th:include="house/detail/fragment_login::login"></div>
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
<div  th:include="secondhouse/fragment::highChartsJs"></div>
</body>
</html>
