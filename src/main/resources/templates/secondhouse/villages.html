<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title>沈阳小区找二手房_沈阳小区二手房_沈阳二手房信息服务 - 房小二网</title>
    <meta name="keywords" content="沈阳小区找二手房,沈阳小区二手房,沈阳二手房信息服务,沈阳按小区找房,沈阳找房，沈阳租房"/>
    <meta name="description"
          content="房小二网沈阳小区为你提供海量真实的沈阳小区信息,包括沈阳房价走势、沈阳最新开盘楼盘、沈阳高档小区，帮您定位，选择，收藏，搜索各类小区房源，带来更好二手房买卖体验。"/>
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/sub">
<!--    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css"/>-->
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
<!--    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/default.css?t=20180710" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/second/default.css?t=20210507" />
	<!--<link rel="stylesheet" href="/css/second/default.css">-->
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/saleListRightBanner.css"/>
    <script src="https://static.fangxiaoer.com/js/listHousePrise/house_price.js?v=20191228" type="text/javascript" charset="utf-8"></script>

    <style>
        .w1210 .mt120 {
            margin-top: 20px;
        }

        .warning p {
            padding-left: 47px;
            text-align: left;
        }
        #MEIQIA-BTN-HOLDER {
            display: block !important;
            z-index: 2147483 !important;
        }
        /* 列表页右侧房价走势图 */
        .shortRight  #areaHighCharts>div{
            width: 170px !important;
        }
        .shortRight  #areaHighCharts>div svg{
            width: 170px !important;
            height: 163.2px;
            margin-left: -8px;
        }
        .shortRight  .highcharts-credits{display:none}
        .shortRight .rightQzQgBtn{
            margin-bottom: 6px;
        }
        .shortRight .rightQzQgBtn a {
            width: 168px;
            line-height: 30px;
            height: 30px;
            padding: 0;
            margin-bottom: 14px;
        }
        .shortRight .rightQzQgBtn a i{display: none;}

        .shortRight  #areaHighCharts{margin-bottom: 6px !important;width: 170px !important;height: 163.2px !important;}

        .grg a { width: 170px; line-height: 49px; padding: 0; margin-bottom: 24px; height: 49px; background: #FFF7EE; border: 1px solid #EAEAEA; font-size: 18px; font-family: Microsoft YaHei; font-weight: 400; color: #FF5200; display: block; text-align: center; }
        .grg a:hover { background-color: #FFF7EE; text-decoration: none; color: #FF5200; }

        /*右侧-房产快搜*/
        .gksou { width: 168px; border: 1px solid #EAEAEA; margin-bottom: 24px; font-size: 16px; font-family: Microsoft YaHei; font-weight: 400; color: #333333; }
        .gktp { width: 100%; border-bottom: 1px solid #EAEAEA; line-height: 46px; height: 46px; position: relative; }
        .gktp span { display: inline-block; width: 4px; height: 16px; margin-bottom: -1px; border-radius: 50px; background: #ff5200; margin-right: 8px; margin-left: 16px; }
        .gktp a{ font-size: 12px; color: #5097FF; cursor: pointer; user-select: none; position: absolute; bottom: -2px; right: 24px;}
        .gktp a:hover{ text-decoration: none; color: #5097FF;}
        .gkmn{ width: 100%; padding: 10px 5px 10px 16px; box-sizing: border-box;}
        .gkmn a{ display: block; width: 100%; position: relative; margin-bottom: 7px;}
        .gkmn a i{ display: inline-block; width: 15px; height: 15px; line-height: 15px; font-size: 12px; border-radius: 50%; background: #FF5200; color: #fff; text-align: center; margin-right: 8px;}
        .gkmn a em{ position: absolute; left: 24px; bottom: -1px;}

        .boomMap { background: url(https://static.fangxiaoer.com/web/images/sy/sale/boomMapBg.jpg) top center; background-size: 100% 100%; width: 170px; height: 140px; cursor: pointer; margin-bottom: 20px; }
        .boomMap img { width: 104px; height: 88px; display: block; padding-top: 26px; margin: 0 auto; }
        .boomMap:hover img { width: 110px; height: 94px; display: block; padding-top: 20px; margin: 0 auto; }

    </style>
</head>
<body class="w1210">
<!--隐藏域-->
<input type="hidden" id="names" th:value="${chooseName}">
<input type="hidden" id="chooseType" th:value="${chooseType}">
<input type="hidden" id="chooseId" th:value="${chooseId}">
<input type="hidden" id="choose_region_id" th:value="${choose_region_id}">
<!--隐藏域-->
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=7"></div>

<div class="main">
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleHouses/" target="_blank">沈阳二手房</a>
        &gt; <a href="/saleVillages/">小区找房</a></div>
    <div class="title w mt120">
        <p class=""><a href="/saleHouses/" id="Top_a_Region"><i class="qy"></i>按区域</a></p>
        <p class=""><a href="/saleHouses/z1" id="Top_a_Subway"><i class="dt"></i>按地铁</a></p>
        <p class=""><a href="/saleVillages" id="Top_a_villageList"><i class="xq"></i>按小区</a></p>
    </div>
    <div class="topSearch" id="option">
        <div class="search_title hid">
            <ul>
                <li class="st1 "><a href="/saleHouses/">按区域查询</a></li>
                <li class="st2 "><a href="/saleHouses/z1">按地铁查询</a></li>
                <li class="st3"><a href="/saleVillages">切换到地图搜索</a></li>
            </ul>
        </div>

        <div class="w search">
            <ul>
                <div id="SearchForSub_RegionDom">

                    <li class="fenlei">
                        <p>区域：</p>
                        <a th:each="r:${region}" th:href="${r.url}" th:id="'r'+${r.id}"
                           th:class="${r.selected}? 'hover':''">
                            <th:block th:text="${#strings.toString(r.name).replaceAll('全部','不限')}"></th:block>
                            <i th:if="${#strings.toString(r.name) ne '全部'}"></i></a>

                    </li>
                    <li th:if="${plate}" class="leibie">
                        <a th:each="j:${plate}" th:text="${#strings.toString(j.name).replaceAll('全部','不限')}"
                           onclick="showIndex(this)" th:href="${j.url}" th:id="'j'+${j.id}"
                           th:class="${j.selected}? 'hover':''"></a>
                    </li>
                </div>
                <div id="SearchForSub_buildYearDom">

                    <li><p>房龄：</p>
                        <a th:each="b:${buildYear}" th:href="${b.url}"
                           th:text="${#strings.toString(b.name).replaceAll('全部','不限')}" th:id="'b'+${b.id}"
                           th:class="${b.selected}? 'hover':''"></a>
                    </li>
                </div>
                <li><p>均价：</p>
                    <a th:each="p:${price}" th:href="${p.url}" onclick="showIndex(this)"
                       th:text="${#strings.toString(p.name).replaceAll('全部','不限')}" th:id="'p'+${p.id} "
                       th:class="${p.selected}?'hover':''">></a>
                    <div id="Search_PriceDomOk">
                        <label>
                            <input name="minPrice" id="minPrice" maxlength="5" type="text" th:value="${minPrice}"
                                   class="inputMoney"> -
                            <input name="maxPrice" id="maxPrice" maxlength="6" type="text" th:value="${maxPrice}"
                                   class="inputMoney">
                            <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1"
                                   id="Search_Btn_Search1" value="确定" class=" inputMoneyBTN" type="button"></label>
                    </div>
                </li>
                <div id="SearchForSub_bran_property">
                    <li><p>品牌物业：</p>
                        <a th:each="p:${property}" th:href="${p.url}"
                           th:text="${#strings.toString(p.name).replaceAll('全部','不限')}" th:id="'p'+${p.id}"
                           th:class="${p.selected}? 'hover':''"></a>
                    </li>
                </div>
            </ul>
        </div>
        <div class="cl"></div>

    </div>
    <div class="classification"></div>
    <div class="content">
        <div class="contentCtn">
            <span>小二为你找到<i th:text="${msg}"></i>个符合条件的小区</span>
            <div id="titleRight" class="titleRight">
                <a id="0" href=""><span>综合排序<th:block th:text="${'&nbsp;&nbsp;'}"></th:block></span></a>
                <a id="5" href=""><span>均价</span><i></i></a>
<!--                <a id="3" href=""><span>涨跌幅</span><i></i></a>-->
                <script>
                    $(document).ready(function () {
                        var minPrice = $("#minPrice").val();
                        var maxPrice = $("#maxPrice").val();
                        if ((maxPrice != null && maxPrice != '' && maxPrice != undefined) || (minPrice != null && minPrice != '' && minPrice != undefined)) {
                            $("#p").removeClass("hover");
                        }
                        var ref = location.pathname;
                        if(ref.indexOf('saleVillages/') == -1) ref +='/';
                        var y = ref.split('/saleVillages/');
                        var x = y.length > 1 ? y[1] : "";
                        if (x.indexOf('o') != -1) {
                            var num = x.split('o')[1].substring(0, 1);
                            if (num == 0 || num == 1) {
                                $("#titleRight a[id ='" + num + "']").addClass("active");
                            } else {
                                if (num % 2 == 1) {
                                    $("#titleRight a[id ='" + num + "']").addClass("active");
                                    $("#titleRight a[id ='" + num + "']").attr("id", parseInt(num) + 1);
                                } else {
                                    num = parseInt(num) - 1;
                                    $("#titleRight a[id ='" + num + "']").addClass("up");
                                }
                            }
                        } else {
                            $("#titleRight a:eq(0)").addClass("active");
                        }
                        var ref2 = ref.replace(/-o[0-9]/, '');
                        $("#titleRight a").each(function () {
                            var ids = $(this).attr("id");
                            var ref3 = ref2 + '-o' + ids;
                            $(this).attr("href", ref3);
                        });
                    });
                </script>
            </div>
        </div>
        <div class="contentMain">

            <div class="inf" th:each="v:${village}" >
                <a th:href="${'/saleVillages/'+v.subId+'/index.htm'}" target="_blank" class="infLeft">
                    <img th:src="${v.picUrl eq '' ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':v.picUrl}"
                         th:alt="${v.subName}"/>
                </a>
                <div class="infCtn">
                    <a class="infCtnTitle" target="_blank" th:href="${'/saleVillages/'+v.subId+'/index.htm'}"
                       th:text="${v.subName}"></a>
                    <p>
                        <th:block
                                th:text="${v.regionName+ (#strings.isEmpty(v.heating) ? '' : '-'+ v.heating)}"></th:block><i class="gang" th:if="${!#strings.isEmpty(v.address)}"></i><th:block th:text="${v.address}"></th:block>
                    </p>
                    <p>
                        <th:block th:if="${v.buildTypeValue ne null and v.buildTypeValue ne ''}">
                            <th:block th:each="one, i: ${#strings.toString(v.buildTypeValue).split('/')}">
                                <th:block th:text="${one}"></th:block>
                                <i class="gang"></i>
                            </th:block>
                        </th:block>
                        <th:block th:text="${'建筑年代：'+(!#strings.isEmpty(v.buildDate)?v.buildDate + '年' :'暂无资料')}"></th:block>
                    </p>
                    <p class="person">
                        <th:block th:text="${v.company}"></th:block><i class="gang" th:if="${!#strings.isEmpty(v.heating) && !#strings.isEmpty(v.company)}"></i>
                        <th:block th:text="${v.heating}"></th:block>
                    </p>
                    <div class="smallInfRight">
                        <ul th:if="${#strings.toString(v.saleCount) ne '0' and !#strings.isEmpty(v.saleCount)}">
                            <li>二手房</li>
                            <!--<li class="hid" th:text="${v.saleCount}+'套'"></li>-->
                            <li class=""><a th:href="${'/saleHouses/-v'+v.subId+'-r'+v.regionId}" target="_blank"
                                            class="more">
                                <th:block th:text="${v.saleCount}"></th:block>
                                <b>套</b></a></li>
                        </ul>
                        <!-- <ul th:if="${#strings.toString(v.rentCount) ne '0' and !#strings.isEmpty(v.rentCount)}">
                             <li>租房</li>
                             &lt;!&ndash;<li class="hid" th:text="${v.rentCount}+'套'"></li>&ndash;&gt;
                             <li class=""><a th:href="${'/rents/-v'+v.subId+'-r'+v.RegionID}" target="_blank"
                                             class="more">
                                 <th:block th:text="${v.rentCount}"></th:block>
                                 <b>套</b></a></li>
                         </ul>-->
                    </div>
                </div>
                <div class="infoRightPriseM">
                    <!--<p th:if="${#strings.toString(v.monthRate) ne '0.0' && !#strings.isEmpty(v.monthRate)}"
                       th:text="${#strings.toString(v.monthRate) eq '0.0' ? '' : (#strings.toString(v.monthRate).replace('-','')) + '%'}"
                       th:class="${#strings.toString(v.monthRate) eq '0.0' ? 'increaseRate ping' : (#strings.toString(v.monthRate).substring(0,1) eq '-' ? 'increaseRate down' : 'increaseRate up')}"></p>-->
                    <p class="infoRightPrise"
                       th:if="${!#strings.isEmpty(v.monthPrice) && #strings.toString(v.monthPrice) ne '0.0'}">
                        <th:block th:text="${#strings.toString(v.monthPrice).replace('.0','')}"></th:block>
                        <s>元/㎡</s></p>

                </div>


            </div>
            <div class="warning" th:if="${#lists.isEmpty(village)}">
                <p>
                    很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。
                    <br> 请重试<!--<a th:href="@{/helpSearch}" target="_blank">点击免费体验购房服务方案>></a>-->
                </p>
            </div>
        </div>
    </div>
    <div id="right" class="shortRight">
        <!--列表页右侧房价评估-->
<!--        <a id="house_price_address" target="_blank"></a>-->
<!--        <div class="areaChartimg"></div>-->
<!--        <a id="hightCharts_jump" target="_blank"><div id="areaHighCharts" style="max-width:180px;height:110px;margin:0 auto"></div></a>-->



        <!--我要买房,求购-->
<!--        <div th:include="secondhouse/fragment::wantBuy"></div>-->
        <div class="grg">
            <a th:href="@{'/helpSearch?ids=1'}" rel="2" target="_blank">我要买房</a>
            <a href="/static/saleHouse/saleHouse.htm" target="_blank" th:class=" (${firstNavIndex} == 8)? 'hover' : '' ">我要卖房</a>
        </div>


        <!--画中画广告-->
        <div th:include="secondhouse/fragment::pictureadvert"></div>
        <!--列表页右侧中介广告-->
        <div th:include="secondhouse/fragment::companyadvert"></div>
    </div>
    <div class="cl"></div>
    <div class="page">
        <div id="Pager1">
            <div th:include="fragment/page :: page"></div>
        </div>
    </div>

</div>
<div class="cl"></div>
<!--底部1-->
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/my_xiala.js"></script>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
<div  th:include="secondhouse/fragment::highChartsJs"></div>

<script type="text/javascript">
    function __doPostBack(pager1, page) {
        var priceBegin = $("#minPrice").val();
        var priceEnd = $("#maxPrice").val();
        var url = window.location.pathname;
        if (pager1 == "Search$Btn_Search1") {
            var ref1 = url.replace(/-k[0-9]\d*/, ''); //k最小值
            var ref2 = ref1.replace(/-x[0-9]\d*/, '');  //x最大值
            if (parseInt(priceEnd) < parseInt(priceBegin)) {
                priceEnd = [priceBegin, priceBegin = priceEnd][0];
            }
            if (priceBegin != "" && priceBegin != 0)
                ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/') + "-k" + priceBegin;
            if (priceEnd != "")
                ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/') + "-x" + priceEnd;
            location.href = ref2;
        }
    }
</script>
</body>
<script src="https://static.fangxiaoer.com/js/listHousePrise/house_price.js?v=20191228" type="text/javascript" charset="utf-8"></script>

</html>
