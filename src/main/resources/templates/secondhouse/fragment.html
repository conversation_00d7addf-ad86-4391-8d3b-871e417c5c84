<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div  th:fragment="specialist">
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/specialist.css" rel="stylesheet" type="text/css">
    <div class="specialist house2">
        <div class="house2Title">专家解读 <a href="">更多解读></a></div>
        <ul>
            <li>
                <div class="headPortrait">
                    <div><img src="https://images.fangxiaoer.com/sy/qt/big/2017/09/21/59c317f02a874.jpg" alt=""></div>
                    <p>杨彬</p>
                </div>
                <div class="specialistContent">
                    <dl>
                        <dt>
                            <span>特色</span>
                            <div>
                                <span>【小区户型】</span><p>目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                <div class="cl"></div>
                                <span>【其他描述】</span><p>物业为沈阳建筑服务有限公司，物业费为2元/㎡/月，物业服务好。</p>
                            </div>
                        <dd>
                            <span>不足</span>
                            <div>小区商业还未完善，配套不成熟。</div>
                        </dd>
                        </dt></dl>
                </div>
            </li>
            <li>
                <div class="headPortrait">
                    <div><img src="https://images.fangxiaoer.com/sy/qt/big/2017/09/21/59c317f02a874.jpg" alt=""></div>
                    <p>杨彬</p>
                </div>
                <div class="specialistContent">
                    <dl>
                        <dt>
                            <span>特色</span>
                            <div>
                                <span>【小区户型】</span><p>目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                <div class="cl"></div>
                                <span>【其他描述】</span><p>物业为沈阳建筑服务有限公司，物业费为2元/㎡/月，物业服务好。</p>
                            </div>
                        <dd>
                            <span>不足</span>
                            <div>小区商业还未完善，配套不成熟。</div>
                        </dd>
                        </dt></dl>
                </div>
            </li>
            <li>
                <div class="headPortrait">
                    <div><img src="https://images.fangxiaoer.com/sy/qt/big/2017/09/21/59c317f02a874.jpg" alt=""></div>
                    <p>杨彬</p>
                </div>
                <div class="specialistContent">
                    <dl>
                        <dt>
                            <span>特色</span>
                            <div>
                                <span>【小区户型】</span><p>目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                <div class="cl"></div>
                                <span>【其他描述】</span><p>物业为沈阳建筑服务有限公司，物业费为2元/㎡/月，物业服务好。</p>
                            </div>
                        <dd>
                            <span>不足</span>
                            <div>小区商业还未完善，配套不成熟。</div>
                        </dd>
                        </dt></dl>
                </div>
            </li>
        </ul>
    </div>
</div>
<div  th:fragment="MapZhouBianPeiTao1_mapDom">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
    <style>
        .map_lpcon {
            padding:0!important;
            margin:0!important;
        }
        .map_lp{
            display:block!important;
            padding: 0!important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
            z-index: 3;
            padding: 16px 0px;
        }
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            background-color: #fff;
            cursor: default;
            border-radius: 10px;
            padding: 0 10px;
        }
        .BMapLib_SearchInfoWindow img {
            border: 0;
            margin-top: 2px!important;
            top: auto !important;
            /*bottom: -35px !important;*/
        }
        .BMapLib_bubble_content p{white-space: normal !important;}
        .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
            border-bottom: 1px solid #ccc;
            height: 40px!important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
            line-height: 40px!important;
            background-color: #fff!important;
            overflow: hidden;
            height: 40px!important;
            adding: 0 5px;
            font-size: 14px!important;
        }
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            border: 0!important;
            background-color: #fff;
            cursor: default;
            box-shadow: 0px 0px 10px #999;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
            float: right;
            height: 30px;
            width: 22px;
            cursor: pointer;
            background-color: #fff!important;
            padding-top: 8px!important;
        }
        /*全屏*/
        .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
        .full .nmaptitleleft{margin-top:0}
        .full .symap{height:100%}
        .full .dt1{height:100%;}
        .full #memap{height: 100% !important;}
        .full .mapsele{height:660px !important}
        .nmaptitleleft a{float:right;background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg) no-repeat;padding-left:23px}
        .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:20px;}
        .nmaptitleleft a.map_house{background-position:0px 12px}

        .canteen div p{display: block;width: 94%;float: left;margin-bottom: 6px;line-height: 27px;}
        .duohangHide{display: -webkit-box !important;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
        .canteen div+div+div{display:none}
        .peitaoSeeMore{font-size: 14px;color: #666;cursor: pointer;padding-right: 30px;margin-left: 520px;}
        .peitaoSeeMoreClose{background: url(https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png) no-repeat 62px 9px;}
        .peitaoSeeMoreOpen{background: url(https://static.fangxiaoer.com/web/images/sy/house/userEvaluate2.png) no-repeat 37px 7px;}
        .divShow{display:block !important}

    </style>
    <div id="MapZhouBianPeiTao1_mapDom">
        <div class="nmaptitle">
            <div class="nmaptitleleft">
                <a href="https://sy.fangxiaoer.com/salemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                <p>周边配套</p>
            </div>
        </div>
        <div class="symap">
            <div class="dt1">
                <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                </div>
                <div class="mapsele" id="mapsele" >
                    <!--<div class="msclose" id="msclose"></div>-->
                    <div class="mapTab" id="mapTab">
                        <ul>
                            <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                            <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                            <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                            <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                            <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                            <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                        </ul>
                    </div>
                    <div class="map_lp">
                        <div id="hs_wrap">
                            <!--<div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>-->
                            <div class="map_lpcon"  id="r-result">
                                <div class="map_dl">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="symap">
                <div style="border:0" class="canteen" id="peitao">
                    <div th:if="${!#strings.isEmpty(v.traffic)}"><span>交通：</span>
                        <p th:text="${v.traffic}"></p></div><!-- traffic-->
                    <div th:if="${!#strings.isEmpty(v.education)}"><span>学校：</span>
                        <p th:text="${v.education}"></p></div><!-- education -->
                    <div th:if="${!#strings.isEmpty(v.market)}"><span>商场：</span>
                        <p th:text="${v.market}"></p></div><!-- market -->
                    <div th:if="${!#strings.isEmpty(v.hospital)}"><span>医院：</span>
                        <p th:text="${v.hospital}"></p></div><!-- hospital -->
                    <div th:if="${!#strings.isEmpty(v.bank)}"><span>银行：</span>
                        <p th:text="${v.bank}"></p></div><!-- bank -->
                    <div th:if="${!#strings.isEmpty(v.othersettings)}"><span>其他：</span>
                        <p th:text="${v.othersettings}"></p></div><!-- othersettings -->
                    <span class="peitaoSeeMore peitaoSeeMoreClose">查看全部<i></i></span>
                    <span class="peitaoSeeMore peitaoSeeMoreOpen">收起<i></i></span>
                </div>
                <!--<i class="mapBtn mapBtnF"></i>-->
            </div>
            <script>
                $(document).ready(function(){
                    $("#peitao div p").addClass("duohangHide")
                    $(".peitaoSeeMoreOpen").hide();
                    var peitaoDivNum = $('.canteen>div').length;
                    if(peitaoDivNum > 2 ){
                        $(".peitaoSeeMoreClose").show()
                    }else {
                        $(".peitaoSeeMoreClose").hide()
                    }
                });

                $(".peitaoSeeMoreClose").click(function () {
                    $(this).hide();
                    $(".peitaoSeeMoreOpen").show();
                    $(".canteen div p").removeClass("duohangHide")
                    $(".canteen div+div+div").css("display","block")

                });

                $(".peitaoSeeMoreOpen").click(function () {
                    $(this).hide();
                    $(".peitaoSeeMoreClose").show();
                    $(".canteen div p").addClass("duohangHide")
                    $(".canteen div+div+div").css("display","none")
                });


                $(function () {
                    $(".mapTab ul li ").click(function () {
                        $(".mapTab ul li ").removeClass("hover")
                        $(this).addClass("hover")
                    });
                    $(".map_dl a").mouseover(function () {
                        $(this).css("background-color", "#e8f4ff");
                    });
                    $(".map_dl a").mouseout(function () {
                        $(this).css("background-color", "#fff");
                    });
                });
                function Full() {
                    if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                        $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                        $(".map_lpcon").height("288px")
                        $(".map_full").html("全屏")
                    } else {
                        $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                        $(".map_lpcon").height("100%")
                        $(".map_full").html("取消全屏")
                    }
                }
            </script>
        </div>
        <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>
        <!--<script type="text/javascript">-->
        <!--$(".mapBtn").hide()-->
        <!--$(".canteen").css("height", "88px")-->
        <!--$(".canteen").css("overflow", "hidden")-->

        <!--window.onload = function () {-->

        <!--var houseNum = $(".canteen>div").length-->
        <!--clickInfo = !clickInfo-->
        <!--if (houseNum > 2) {-->
        <!--$(".mapBtn").show()-->
        <!--}-->
        <!--var clickInfo = true-->
        <!--$(".mapBtn").click(function () {-->
        <!--if (clickInfo) {-->
        <!--$(".canteen").css("height", "auto")-->
        <!--$(this).removeClass("mapBtnF")-->
        <!--$(this).addClass("mapBtnS")-->
        <!--clickInfo = !clickInfo-->
        <!--} else {-->
        <!--$(".canteen").css("height", "88px")-->
        <!--$(".canteen").css("overflow", "hidden")-->
        <!--$(this).removeClass("mapBtnS")-->
        <!--$(this).addClass("mapBtnF")-->
        <!--clickInfo = !clickInfo-->
        <!--}-->
        <!--})-->

        <!--}-->
        <!--</script>-->
    </div>
</div>

<div  th:fragment="MapZhouBianPeiTao1_mapDom_QQ">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
    <style>
        .map_lpcon {
            padding:0!important;
            margin:0!important;
        }
        .map_lp{
            display:block!important;
            padding: 0!important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
            z-index: 3;
            padding: 16px 0px;
        }
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            background-color: #fff;
            cursor: default;
            border-radius: 10px;
            padding: 0 10px;
        }
        .BMapLib_SearchInfoWindow img {
            border: 0;
            margin-top: 2px!important;
        }
        /*.BMapLib_bubble_content p{white-space: normal !important;}*/
        .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
            border-bottom: 1px solid #ccc;
            height: 40px!important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
            line-height: 40px!important;
            background-color: #fff!important;
            overflow: hidden;
            height: 40px!important;
            adding: 0 5px;
            font-size: 14px!important;
        }
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            border: 0!important;
            background-color: #fff;
            cursor: default;
            box-shadow: 0px 0px 10px #999;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
            float: right;
            height: 30px;
            width: 22px;
            cursor: pointer;
            background-color: #fff!important;
            padding-top: 8px!important;
        }
        /*全屏*/
        .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
        .full .nmaptitleleft{margin-top:0}
        .full .symap{height:100%}
        .full .dt1{height:100%;}
        .full #memap{height: 100% !important;}
        .full .mapsele{height:660px !important}
        .nmaptitleleft a{float:right;background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg) no-repeat;padding-left:23px}
        .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:20px;}
        .nmaptitleleft a.map_house{background-position:0px 12px}

        .canteen div p{display: block;width: 94%;float: left;margin-bottom: 6px;line-height: 27px;}
        .duohangHide{display: -webkit-box !important;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
        .canteen div+div+div{display:none}
        .peitaoSeeMore{font-size: 14px;color: #666;cursor: pointer;padding-right: 30px;margin-left: 520px;}
        .peitaoSeeMoreClose{background: url(https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png) no-repeat 62px 9px;}
        .peitaoSeeMoreOpen{background: url(https://static.fangxiaoer.com/web/images/sy/house/userEvaluate2.png) no-repeat 37px 7px;}
        .divShow{display:block !important}

    </style>
    <div id="MapZhouBianPeiTao1_mapDom">
        <div class="nmaptitle">
            <div class="nmaptitleleft">
                <a href="https://sy.fangxiaoer.com/salemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                <p>周边配套</p>
            </div>
        </div>
        <div class="symap">
            <div class="dt1">
                <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                </div>
                <div class="mapsele" id="mapsele" >
                    <!--<div class="msclose" id="msclose"></div>-->
                    <div class="mapTab" id="mapTab">
                        <ul>
                            <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                            <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                            <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                            <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                            <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                            <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                        </ul>
                    </div>
                    <div class="map_lp">
                        <div id="hs_wrap">
                            <!--<div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>-->
                            <div class="map_lpcon"  id="r-result">
                                <div class="map_dl">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="symap">
                <div style="border:0" class="canteen" id="peitao">
                    <div th:if="${!#strings.isEmpty(v.traffic)}"><span>交通：</span>
                        <p th:text="${v.traffic}"></p></div><!-- traffic-->
                    <div th:if="${!#strings.isEmpty(v.education)}"><span>学校：</span>
                        <p th:text="${v.education}"></p></div><!-- education -->
                    <div th:if="${!#strings.isEmpty(v.market)}"><span>商场：</span>
                        <p th:text="${v.market}"></p></div><!-- market -->
                    <div th:if="${!#strings.isEmpty(v.hospital)}"><span>医院：</span>
                        <p th:text="${v.hospital}"></p></div><!-- hospital -->
                    <div th:if="${!#strings.isEmpty(v.bank)}"><span>银行：</span>
                        <p th:text="${v.bank}"></p></div><!-- bank -->
                    <div th:if="${!#strings.isEmpty(v.othersettings)}"><span>其他：</span>
                        <p th:text="${v.othersettings}"></p></div><!-- othersettings -->
                    <span class="peitaoSeeMore peitaoSeeMoreClose">查看全部<i></i></span>
                    <span class="peitaoSeeMore peitaoSeeMoreOpen">收起<i></i></span>
                </div>
                <!--<i class="mapBtn mapBtnF"></i>-->
            </div>
            <script>
                $(document).ready(function(){
                    $("#peitao div p").addClass("duohangHide")
                    $(".peitaoSeeMoreOpen").hide();
                    var peitaoDivNum = $('.canteen>div').length;
                    if(peitaoDivNum > 2 ){
                        $(".peitaoSeeMoreClose").show()
                    }else {
                        $(".peitaoSeeMoreClose").hide()
                    }
                });

                $(".peitaoSeeMoreClose").click(function () {
                    $(this).hide();
                    $(".peitaoSeeMoreOpen").show();
                    $(".canteen div p").removeClass("duohangHide")
                    $(".canteen div+div+div").css("display","block")

                });

                $(".peitaoSeeMoreOpen").click(function () {
                    $(this).hide();
                    $(".peitaoSeeMoreClose").show();
                    $(".canteen div p").addClass("duohangHide")
                    $(".canteen div+div+div").css("display","none")
                });


                $(function () {
                    $(".mapTab ul li ").click(function () {
                        $(".mapTab ul li ").removeClass("hover")
                        $(this).addClass("hover")
                    });
                    $(".map_dl a").mouseover(function () {
                        $(this).css("background-color", "#e8f4ff");
                    });
                    $(".map_dl a").mouseout(function () {
                        $(this).css("background-color", "#fff");
                    });
                });
                function Full() {
                    if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                        $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                        $(".map_lpcon").height("288px")
                        $(".map_full").html("全屏")
                    } else {
                        $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                        $(".map_lpcon").height("100%")
                        $(".map_full").html("取消全屏")
                    }
                }
            </script>
        </div>
        <!--        <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>-->
        <script src="/js/QQMap/newBaiduMap_SelectZhoubian.js?t=1"></script>
        <!--<script type="text/javascript">-->
        <!--$(".mapBtn").hide()-->
        <!--$(".canteen").css("height", "88px")-->
        <!--$(".canteen").css("overflow", "hidden")-->

        <!--window.onload = function () {-->

        <!--var houseNum = $(".canteen>div").length-->
        <!--clickInfo = !clickInfo-->
        <!--if (houseNum > 2) {-->
        <!--$(".mapBtn").show()-->
        <!--}-->
        <!--var clickInfo = true-->
        <!--$(".mapBtn").click(function () {-->
        <!--if (clickInfo) {-->
        <!--$(".canteen").css("height", "auto")-->
        <!--$(this).removeClass("mapBtnF")-->
        <!--$(this).addClass("mapBtnS")-->
        <!--clickInfo = !clickInfo-->
        <!--} else {-->
        <!--$(".canteen").css("height", "88px")-->
        <!--$(".canteen").css("overflow", "hidden")-->
        <!--$(this).removeClass("mapBtnS")-->
        <!--$(this).addClass("mapBtnF")-->
        <!--clickInfo = !clickInfo-->
        <!--}-->
        <!--})-->

        <!--}-->
        <!--</script>-->
    </div>
</div>


<!--小区详细页地图周边配套-->
<div  th:fragment="MapZhouBianPeiTao1_mapDom_village">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
    <style>
        .map_lpcon {
            padding:0!important;
            margin:0!important;
        }
        .map_lp{
            display:block!important;
            padding: 0!important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
            z-index: 3;
            padding: 16px 0px;
        }
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            background-color: #fff;
            cursor: default;
            border-radius: 10px;
            padding: 0 10px;
        }
        .BMapLib_SearchInfoWindow img {
            border: 0;
            margin-top: 2px!important;
            top: auto !important;
            bottom: -35px !important;
        }
        .BMapLib_bubble_content p{white-space: normal !important;}
        .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
            border-bottom: 1px solid #ccc;
            height: 40px!important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
            line-height: 40px!important;
            background-color: #fff!important;
            overflow: hidden;
            height: 40px!important;
            adding: 0 5px;
            font-size: 14px!important;
        }
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            border: 0!important;
            background-color: #fff;
            cursor: default;
            box-shadow: 0px 0px 10px #999;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
            float: right;
            height: 30px;
            width: 22px;
            cursor: pointer;
            background-color: #fff!important;
            padding-top: 8px!important;
        }
        /*全屏*/
        .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
        .full .nmaptitleleft{margin-top:0}
        .full .symap{height:100%}
        .full .dt1{height:100%;}
        .full #memap{height: 100% !important;}
        .full .mapsele{height:660px !important}
        .nmaptitleleft a{float:right;background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg) no-repeat;padding-left:23px}
        .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:20px;}
        .nmaptitleleft a.map_house{background-position:0px 12px}

        .canteen div p{display: block;width: 94%;float: left;margin-bottom: 6px;line-height: 27px;}
        .duohangHide{display: -webkit-box !important;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
        .canteen div+div+div{display:none}
        .peitaoSeeMore{font-size: 14px;color: #666;cursor: pointer;padding-right: 30px;margin-left: 520px;}
        .peitaoSeeMoreClose{background: url(https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png) no-repeat 62px 9px;}
        .peitaoSeeMoreOpen{background: url(https://static.fangxiaoer.com/web/images/sy/house/userEvaluate2.png) no-repeat 37px 7px;}
        .divShow{display:block !important}

    </style>
    <div id="MapZhouBianPeiTao1_mapDom">
        <div class="nmaptitle">
            <div class="nmaptitleleft">
                <a href="https://sy.fangxiaoer.com/salemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                <p><span th:text="${v.title }"></span>周边配套</p>
            </div>
        </div>
        <div class="symap">
            <div class="dt1">
                <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                </div>
                <div class="mapsele" id="mapsele" >
                    <!--<div class="msclose" id="msclose"></div>-->
                    <div class="mapTab" id="mapTab">
                        <ul>
                            <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                            <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                            <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                            <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                            <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                            <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                        </ul>
                    </div>
                    <div class="map_lp">
                        <div id="hs_wrap">
                            <!--<div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>-->
                            <div class="map_lpcon"  id="r-result">
                                <div class="map_dl">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!--<div class="symap">-->
                <!--<div style="border:0" class="canteen" id="peitao">-->
                    <!--<div th:if="${!#strings.isEmpty(v.traffic)}"><span>交通：</span>-->
                        <!--<p th:text="${v.traffic}"></p></div>&lt;!&ndash; traffic&ndash;&gt;-->
                    <!--<div th:if="${!#strings.isEmpty(v.education)}"><span>学校：</span>-->
                        <!--<p th:text="${v.education}"></p></div>&lt;!&ndash; education &ndash;&gt;-->
                    <!--<div th:if="${!#strings.isEmpty(v.market)}"><span>商场：</span>-->
                        <!--<p th:text="${v.market}"></p></div>&lt;!&ndash; market &ndash;&gt;-->
                    <!--<div th:if="${!#strings.isEmpty(v.hospital)}"><span>医院：</span>-->
                        <!--<p th:text="${v.hospital}"></p></div>&lt;!&ndash; hospital &ndash;&gt;-->
                    <!--<div th:if="${!#strings.isEmpty(v.bank)}"><span>银行：</span>-->
                        <!--<p th:text="${v.bank}"></p></div>&lt;!&ndash; bank &ndash;&gt;-->
                    <!--<div th:if="${!#strings.isEmpty(v.othersettings)}"><span>其他：</span>-->
                        <!--<p th:text="${v.othersettings}"></p></div>&lt;!&ndash; othersettings &ndash;&gt;-->
                    <!--<span class="peitaoSeeMore peitaoSeeMoreClose">查看全部<i></i></span>-->
                    <!--<span class="peitaoSeeMore peitaoSeeMoreOpen">收起<i></i></span>-->
                <!--</div>-->
                <!--&lt;!&ndash;<i class="mapBtn mapBtnF"></i>&ndash;&gt;-->
            <!--</div>-->
            <script>
                $(document).ready(function(){
                    $("#peitao div p").addClass("duohangHide")
                    $(".peitaoSeeMoreOpen").hide();
                    var peitaoDivNum = $('.canteen>div').length;
                    if(peitaoDivNum > 2 ){
                        $(".peitaoSeeMoreClose").show()
                    }else {
                        $(".peitaoSeeMoreClose").hide()
                    }
                });

                $(".peitaoSeeMoreClose").click(function () {
                    $(this).hide();
                    $(".peitaoSeeMoreOpen").show();
                    $(".canteen div p").removeClass("duohangHide")
                    $(".canteen div+div+div").css("display","block")

                });

                $(".peitaoSeeMoreOpen").click(function () {
                    $(this).hide();
                    $(".peitaoSeeMoreClose").show();
                    $(".canteen div p").addClass("duohangHide")
                    $(".canteen div+div+div").css("display","none")
                });


                $(function () {
                    $(".mapTab ul li ").click(function () {
                        $(".mapTab ul li ").removeClass("hover")
                        $(this).addClass("hover")
                    });
                    $(".map_dl a").mouseover(function () {
                        $(this).css("background-color", "#e8f4ff");
                    });
                    $(".map_dl a").mouseout(function () {
                        $(this).css("background-color", "#fff");
                    });
                });
                function Full() {
                    if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                        $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                        $(".map_lpcon").height("288px")
                        $(".map_full").html("全屏")
                    } else {
                        $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                        $(".map_lpcon").height("100%")
                        $(".map_full").html("取消全屏")
                    }
                }
            </script>
        </div>
        <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>
        <!--<script type="text/javascript">-->
        <!--$(".mapBtn").hide()-->
        <!--$(".canteen").css("height", "88px")-->
        <!--$(".canteen").css("overflow", "hidden")-->

        <!--window.onload = function () {-->

        <!--var houseNum = $(".canteen>div").length-->
        <!--clickInfo = !clickInfo-->
        <!--if (houseNum > 2) {-->
        <!--$(".mapBtn").show()-->
        <!--}-->
        <!--var clickInfo = true-->
        <!--$(".mapBtn").click(function () {-->
        <!--if (clickInfo) {-->
        <!--$(".canteen").css("height", "auto")-->
        <!--$(this).removeClass("mapBtnF")-->
        <!--$(this).addClass("mapBtnS")-->
        <!--clickInfo = !clickInfo-->
        <!--} else {-->
        <!--$(".canteen").css("height", "88px")-->
        <!--$(".canteen").css("overflow", "hidden")-->
        <!--$(this).removeClass("mapBtnS")-->
        <!--$(this).addClass("mapBtnF")-->
        <!--clickInfo = !clickInfo-->
        <!--}-->
        <!--})-->

        <!--}-->
        <!--</script>-->
    </div>
</div>
<!--小区详情页周边配套 QQMap-->
<div  th:fragment="QQMapZhouBianPeiTao1_mapDom_village">
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
    <style>
        .map_lpcon {
            padding:0!important;
            margin:0!important;
        }
        .map_lp{
            display:block!important;
            padding: 0!important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
            z-index: 3;
            padding: 16px 0px;
        }
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            background-color: #fff;
            cursor: default;
            border-radius: 10px;
            padding: 0 10px;
        }
        .BMapLib_SearchInfoWindow img {
            border: 0;
            margin-top: 2px!important;
            top: auto !important;
            bottom: -35px !important;
        }
        /*.BMapLib_bubble_content p{white-space: normal !important;}*/
        .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
            border-bottom: 1px solid #ccc;
            height: 40px!important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
            line-height: 40px!important;
            background-color: #fff!important;
            overflow: hidden;
            height: 40px!important;
            adding: 0 5px;
            font-size: 14px!important;
        }
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            border: 0!important;
            background-color: #fff;
            cursor: default;
            box-shadow: 0px 0px 10px #999;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
            float: right;
            height: 30px;
            width: 22px;
            cursor: pointer;
            background-color: #fff!important;
            padding-top: 8px!important;
        }
        /*全屏*/
        .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
        .full .nmaptitleleft{margin-top:0}
        .full .symap{height:100%}
        .full .dt1{height:100%;}
        .full #memap{height: 100% !important;}
        .full .mapsele{height:660px !important}
        .nmaptitleleft a{float:right;background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg) no-repeat;padding-left:23px}
        .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:20px;}
        .nmaptitleleft a.map_house{background-position:0px 12px}

        .canteen div p{display: block;width: 94%;float: left;margin-bottom: 6px;line-height: 27px;}
        .duohangHide{display: -webkit-box !important;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
        .canteen div+div+div{display:none}
        .peitaoSeeMore{font-size: 14px;color: #666;cursor: pointer;padding-right: 30px;margin-left: 520px;}
        .peitaoSeeMoreClose{background: url(https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png) no-repeat 62px 9px;}
        .peitaoSeeMoreOpen{background: url(https://static.fangxiaoer.com/web/images/sy/house/userEvaluate2.png) no-repeat 37px 7px;}
        .divShow{display:block !important}

    </style>
    <div id="MapZhouBianPeiTao1_mapDom">
        <div class="nmaptitle">
            <div class="nmaptitleleft">
                <a href="https://sy.fangxiaoer.com/salemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                <p><span th:text="${v.title }"></span>周边配套</p>
            </div>
        </div>
        <div class="symap">
            <div class="dt1">
                <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                </div>
                <div class="mapsele" id="mapsele" >
                    <!--<div class="msclose" id="msclose"></div>-->
                    <div class="mapTab" id="mapTab">
                        <ul>
                            <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                            <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                            <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                            <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                            <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                            <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                        </ul>
                    </div>
                    <div class="map_lp">
                        <div id="hs_wrap">
                            <!--<div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>-->
                            <div class="map_lpcon"  id="r-result">
                                <div class="map_dl">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                $(document).ready(function(){
                    $("#peitao div p").addClass("duohangHide")
                    $(".peitaoSeeMoreOpen").hide();
                    var peitaoDivNum = $('.canteen>div').length;
                    if(peitaoDivNum > 2 ){
                        $(".peitaoSeeMoreClose").show()
                    }else {
                        $(".peitaoSeeMoreClose").hide()
                    }
                });

                $(".peitaoSeeMoreClose").click(function () {
                    $(this).hide();
                    $(".peitaoSeeMoreOpen").show();
                    $(".canteen div p").removeClass("duohangHide")
                    $(".canteen div+div+div").css("display","block")

                });

                $(".peitaoSeeMoreOpen").click(function () {
                    $(this).hide();
                    $(".peitaoSeeMoreClose").show();
                    $(".canteen div p").addClass("duohangHide")
                    $(".canteen div+div+div").css("display","none")
                });


                $(function () {
                    $(".mapTab ul li ").click(function () {
                        $(".mapTab ul li ").removeClass("hover")
                        $(this).addClass("hover")
                    });
                    $(".map_dl a").mouseover(function () {
                        $(this).css("background-color", "#e8f4ff");
                    });
                    $(".map_dl a").mouseout(function () {
                        $(this).css("background-color", "#fff");
                    });
                });
                function Full() {
                    if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                        $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                        $(".map_lpcon").height("288px")
                        $(".map_full").html("全屏")
                    } else {
                        $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                        $(".map_lpcon").height("100%")
                        $(".map_full").html("取消全屏")
                    }
                }
            </script>
        </div>
<!--        <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>-->
        <script src="/js/QQMap/newBaiduMap_SelectZhoubian.js?t=1"></script>
    </div>
</div>

<!--列表页右侧我要卖房、房价评估-->
<div  th:fragment="rightselling">
    <div class="rightQzQgBtn">
        <a href="/static/saleHouse/saleHouse.htm" target="_blank">我要卖房</a>
<!--        <a href="/housingprice/" target="_blank">房价评估</a>-->
        <a th:href="@{'/helpSearch?ids=2'}" target="_blank" style="margin-top: 20px">我要买房</a>
    </div>
    <style>
        .rightQzQgBtn a{
            padding-left: 0 !important;
        }
    </style>
</div>
<!--右侧求购-->
<div  th:fragment="rightwantbuy">
    <div class="zsfw">
        <h1><span></span>求购</h1>
        <ul>
            <li>
                <span>意向区域</span>
                <div>
                    <select id="region" >
                        <option  th:each="region,stat:${region}" th:selected="${stat.index eq 1}" th:if="${region.name ne '全部'}" th:text="${region.name}" th:value="${region.name}">沈河区</option>
                    </select>
                </div>
            </li>
            <li class="hx">
                <span>意向户型</span>
                <div>
                    <select id="new_huxing" >
                        <option>一居</option>
                        <option>二居</option>
                        <option>三居</option>
                        <option>四居</option>
                        <option>五居及以上</option>
                    </select>
                </div>
            </li>
            <li class="yx">
                <span>预算价格</span>
                <div>
                    <select id="new_yusuan">
                        <option>35万以下</option>
                        <option>35-50万</option>
                        <option>50-80万</option>
                        <option>80-100万</option>
                        <option>100-120万</option>
                        <option>120-150万</option>
                        <option>150万以上</option>
                    </select>
                </div>
            </li>
            <li>
                <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
            </li>
            <li>
                <span>手机号码</span>
                <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') "  placeholder="请输入手机号" maxlength="11">
                <input type="hidden" id="type" value="2">
            </li>
            <li>
                <span>验证码</span>
                <input type="tel" id="code" class="fxe_messageCode"  maxlength="6" style="width: 120px;" placeholder="请输入验证码"/>
                <p id="hqyzm" class="fxe_ReSendValidateCoad" >获取验证码</p>
                <p class="fxe_validateCode"></p>
            </li>
            <b class="btn" id="new_submit">提交</b>
            <li style="color: #999;width:237px;text-align:left;font-size: 12px;">若正确提交即同意接受以电话、短信等方式推荐我司业务相关的信息</li>

        </ul>
    </div>
</div>
<!--右侧二手房推荐-->
<div th:fragment="secondrecommend">
    <!--  <dl class="recommend" th:if="${!#lists.isEmpty(advert.command)}" >
          <dt><span></span>二手房推荐</dt>
          <dd th:each="command,i:${advert.command}">
              &lt;!&ndash;判断第一个&ndash;&gt;
              <a th:if="${i.count == 1}">
                  <div class="hideD" style="display: none;">
                      <a th:href="${command.url}" target="_blank">
                          <p th:class="hover"><th:block th:text="${i.index+1}"></th:block></p>
                          <span><th:block th:text="${command.region + command.projectName}"></th:block></span>
                          <b style="width: 75px;"><th:block th:utext="${command.price}"></th:block></b>
                      </a>
                  </div>
                  <a class="hideA" style="display: block" th:href="${command.url}" target="_blank">
                      <img th:src="${command.housePic}" alt="">
                      <span class="hideATitle"><th:block th:text="${command.subName}"></th:block></span>
                      <span><i><th:block th:utext="${command.price}"></th:block></i></span>
                      <p>
                          <span><th:block th:text="${command.room+'室'+command.hall+'厅'+command.toilet+'卫'}"></th:block></span>
                          <span><th:block th:text="${#strings.toString(command.area).contains('.')? #strings.toString(command.area).replaceAll('0+?$','').replaceAll('[.]$', '') : command.area}+'m²'"></th:block></span>
                      </p>

                  </a>
              </a>
          </dd>

          &lt;!&ndash;判断除第一个以外的其他&ndash;&gt;
          <dd th:each="command,i:${advert.command}">
              <a th:if="${i.count &lt; 11}" th:href="${command.url}" target="_blank">
                  <div class="hideD">
                      <a th:href="${command.url}" target="_blank">
                          <p th:class="${i.index &lt; 3?'hover':''}"><th:block th:text="${i.index+1}"></th:block></p>
                          <span><th:block th:text="${command.region + command.projectName}"></th:block></span>
                          <b style="width: 75px;"><th:block th:utext="${command.price}"></th:block></b>
                      </a>
                  </div>
                  <a class="hideA" th:href="${command.url}" target="_blank">
                      <img th:src="${command.housePic}" alt="">
                      <span class="hideATitle"><th:block th:text="${command.subName}"></th:block></span>
                      <span><i><th:block th:utext="${command.price}"></i></span>
                      <p><span><th:block  th:text="${command.room+'室'+command.hall+'厅'+command.toilet+'卫'}"></th:block></span>
                          <span><th:block th:text="${#strings.toString(command.area).contains('.')? #strings.toString(command.area).replaceAll('0+?$','').replaceAll('[.]$', '') : command.area}+'m²'"></th:block></span>
                      </p>

                  </a>
              </a>
          </dd>
      </dl>
      <script>
          $(".recommend dd").mousemove(function () {
              $(".hideA").hide()
              $(this).find(".hideD").hide()
              $(this).find(".hideA").show()
          })
          $(".recommend dd").mouseleave(function () {
              $(".hideD").show()
              $(this).find(".hideA").show()
          })
      </script>-->
    <div class="groomList" th:if="${!#lists.isEmpty(advert.command)}">
        <h4><i></i>二手房推荐</h4>
        <ul>
            <li th:each="command,i:${advert.command}">
                <a th:href="${command.url}" target="_blank">
                    <img th:src="${command.housePic}" alt="">
                    <div class="imgBottm">
                        <span th:if="${#strings.toString(command.realName) ne null && #strings.toString(command.realName) ne ''}" th:text="${command.realName}">小明</span>
                        <span th:if="${#strings.toString(command.mobile) ne null && #strings.toString(command.mobile) ne ''}" th:text="${command.mobile}">12698456544</span>
                    </div>
                    <div class="oneH">
                        <span th:if="${#strings.toString(command.regionName) ne null && #strings.toString(command.regionName) ne ''}" th:text="${command.regionName}">沈北新区</span>
                        <p th:if="${#strings.toString(command.subName) ne null && #strings.toString(command.subName) ne ''}" th:text="${command.subName}">华强城</p>
                    </div>
                    <div class="twoH">
                        <p><span th:text="${command.room+'室'+command.hall+'厅'+command.toilet+'卫'}">2室2厅1卫</span><span th:text="${#strings.toString(command.area).contains('.')? #strings.toString(command.area).replaceAll('0+?$','').replaceAll('[.]$', '') : command.area}+'m²'"> 86.6㎡</span></p>
                        <div><span class="tuijian_price"><th:block th:utext="${command.price}"></th:block></span><i></i></div>
                    </div>
                </a>
            </li>
        </ul>
    </div>
</div>
<!--右侧画中画广告-->
<div th:fragment="pictureadvert">
    <style>
        .picture_advert img{
            margin-bottom: 20px;
            width:  250px;
            height:  192px;
        }
    </style>
    <div class="picture_advert" th:if="${!#lists.isEmpty(advert.focus)}">
        <a th:each="focus,i:${advert.focus}" th:href="${focus.url}" target="_blank">
            <img width="100%" th:src="${focus.image}" th:alt="${focus.projectName}">
        </a>
    </div>
</div>
<!--列表页右侧中介广告-->
<div th:fragment="companyadvert">
    <div th:if="${!#lists.isEmpty(advert.company)}">
        <th:block th:each="company,i:${advert.company}">
            <a th:if="${#strings.toString(company.url).indexOf('http') ne -1}" th:href="${company.url}" target="_blank" class="sed_ad_pic_url">
                <img th:src="${company.image}" th:alt="${company.projectName}" width="100%"  class="sed_ad_img" />
            </a>
            <img th:if="${#strings.toString(company.url).indexOf('http') eq -1}"  th:src="${company.image}" th:alt="${company.projectName}" class="sed_ad_img" width="100%" style="margin-top:20px" />
        </th:block>
    </div>
</div>
<!--小区专家-->
<div th:fragment="plotexpert">
    <div  class="plotexpertKuang"  th:if="${!#lists.isEmpty(plotExpertList)}">
        <h4 th:if="${#strings.toString(agentStoreType) eq '2' || #strings.toString(agentStoreType) eq '3'}"><i></i>小区专家<s>为您提供专业服务</s></h4>
        <h4 th:if="${#strings.toString(agentStoreType) eq '4' || #strings.toString(agentStoreType) eq '5'}"><i></i>推荐专家<s>为您提供专业服务</s></h4>
        <ul>
            <li th:each="plotExpertList,i:${plotExpertList}" th:if="${i.index lt 4}">
                <a th:if="${#strings.toString(agentStoreType) eq '2' || #strings.toString(agentStoreType) eq '3'}" th:href="${'/agent/second/'+plotExpertList._memberId}" target="_blank">
                    <div class="imgBorder"><img th:src="${#strings.isEmpty(plotExpertList.avatar)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':plotExpertList.avatar}"/></div>
                    <div class="contentRight">
                        <h5 th:text="${plotExpertList.realName}">小明</h5>
                        <p th:text="${plotExpertList.intermediaryName}">点赞专家</p>
                        <div th:text="${plotExpertList.mobile}">15790990265</div>
                    </div>
                </a>
                <a th:if="${#strings.toString(agentStoreType) eq '4'}" th:href="${'/agent/shops/'+plotExpertList._memberId}" target="_blank">
                    <div class="imgBorder"><img th:src="${#strings.isEmpty(plotExpertList.avatar)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':plotExpertList.avatar}"/></div>
                    <div class="contentRight">
                        <h5 th:text="${plotExpertList.realName}">小明</h5>
                        <p th:text="${plotExpertList.intermediaryName}">点赞专家</p>
                        <div th:text="${plotExpertList.mobile}">15790990265</div>
                    </div>
                </a>
                <a th:if="${#strings.toString(agentStoreType) eq '5'}" th:href="${'/agent/office/'+plotExpertList._memberId}" target="_blank">
                    <div class="imgBorder"><img th:src="${#strings.isEmpty(plotExpertList.avatar)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':plotExpertList.avatar}"/></div>
                    <div class="contentRight">
                        <h5 th:text="${plotExpertList.realName}">小明</h5>
                        <p th:text="${plotExpertList.intermediaryName}">点赞专家</p>
                        <div th:text="${plotExpertList.mobile}">15790990265</div>
                    </div>
                </a>
            </li>
        </ul>
        <a th:if="${#strings.toString(plotId) ne null && #strings.toString(plotId) ne ''}" th:href="${'/saleVillages/'+plotId+'/plotExpertInterpretation.htm'}" target="_blank" class="plotexpertKuangSeeMore">查看全部解读</a>
    </div>
</div>
<!--二手房列表筛选小区展示小区相关信息-->
<div th:fragment="viewplotinformation">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/villa/villageDetailsNav.css">

    <div class="villageDetailsNav" th:if="${!#lists.isEmpty(plotInformation)}">
        <div class="villageDetailsNavLeft" >
            <div>
                <h4 th:text="${plotInformation.title}">帅府社区</h4>
                <!--<p> <i th:if="${plotInformation.unitPrice} ne 0.0">本周<s class="this_price"  th:text="${#strings.toString(plotInformation.unitPrice).contains('.') ?  #strings.toString(plotInformation.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : plotInformation.unitPrice}+'元/㎡'">5940元/㎡</s></i>
                    &lt;!&ndash;<s class="this_price" th:if="${plotInformation.unitPrice} eq 0.0" th:text="${'暂无资料'}"></s>&ndash;&gt;

                    &lt;!&ndash;<li class="min" th:if="${  #strings.toString(plotInformation.increaseRate) eq '0.0'}"><span th:text="&#45;&#45;&#45;&#45;"></span></li>&ndash;&gt;
                <li class="min" th:if="${!#strings.isEmpty(plotInformation.increaseRate) &&  #strings.toString(plotInformation.increaseRate).substring(0,1) ne '-' and #strings.toString(plotInformation.increaseRate) ne '0.0'}">比上月<i th:text="${'高'+plotInformation.increaseRate+'%'}" class="red">2.3%</i></li>
                <li class="min" th:if="${!#strings.isEmpty(plotInformation.increaseRate) &&  #strings.toString(plotInformation.increaseRate).substring(0,1) eq '-'}">比上月<i  th:text="${'低'+#strings.toString(plotInformation.increaseRate).substring(1)+'%'}" class="green">2.3%</i></li>
                </p>-->
            </div>
            <div>
                <span th:text="${plotInformation.regionName+' '+plotInformation.plateName+' '+plotInformation.address}"></span>
                <span th:if="${#strings.toString(plotInformation.subAge) ne null && #strings.toString(plotInformation.subAge) ne ''}" th:text="${'建筑年代：'+plotInformation.subAge}"></span>
                <!--<span>距离地铁1号线1200米</span>-->
            </div>
        </div>
        <div class="villageDetailsNavRight">
            <a th:href="${'/saleVillages/'+plotId+'/index.htm'}" target="_blank"><i class="icon1"></i>小区详情</a>
            <a th:href="${'/saleVillages/'+plotId+'/plotSupport.htm'}" target="_blank"><i class="icon2"></i>查看周边</a>
<!--            <a th:href="${'/saleVillages/'+plotId+'/index.htm#zou'}" target="_blank"><i class="icon3"></i>价格走势</a>-->
        </div>
    </div>
</div>

<!--租房列表筛选小区展示小区相关信息-->
<div th:fragment="viewRentplotinformation">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/villa/villageDetailsNav.css">

    <div class="villageDetailsNav" th:if="${!#lists.isEmpty(plotInformation)}">
        <div class="villageDetailsNavLeft" >
            <div>
                <h4 th:text="${plotInformation.title}">帅府社区</h4>
            </div>
            <div>
                <span th:text="${plotInformation.regionName+' '+plotInformation.plateName+' '+plotInformation.address}"></span>
                <span th:if="${#strings.toString(plotInformation.subAge) ne null && #strings.toString(plotInformation.subAge) ne ''}" th:text="${'建筑年代：'+plotInformation.subAge}"></span>
                <!--<span>距离地铁1号线1200米</span>-->
            </div>
        </div>
        <div class="villageDetailsNavRight">
            <a th:href="${'/saleVillages/'+plotId+'/index.htm'}" target="_blank"><i class="icon1"></i>小区详情</a>
            <a th:href="${'/saleVillages/'+plotId+'/plotSupport.htm'}" target="_blank"><i class="icon2"></i>查看周边</a>
        </div>
    </div>
</div>
<!--二手房、租房、商铺、写字楼列表上方广告位-->
<div th:fragment="advert_recommand">
    <div class="listBannerImg" th:if="${!#lists.isEmpty(advert.recommand) && #lists.size(advert.recommand) eq 2}">
        <a th:href="${advert.recommand[0].url}" target="_blank" class="bannerLeft"><img th:src="${advert.recommand[0].image}" alt=""></a>
        <a th:href="${advert.recommand[1].url}" target="_blank" class="bannerRight"><img th:src="${advert.recommand[1].image}" alt=""></a>
    </div>
    <div class="listBannerImg1" th:if="${!#lists.isEmpty(advert.recommand) && #lists.size(advert.recommand) eq 1}">
        <a th:href="${advert.recommand[0].url}" target="_blank" class="bannerLeft"><img th:src="${advert.recommand[0].image}" alt=""></a>
    </div>
    <style>
        /* 二手房列表页新增广告位 */
        .listBannerImg1{
            overflow: hidden;
            width: 893px;
            margin-bottom: 20px;
        }
        .listBannerImg1 a{
            display: block;
            float: left;
            width: 890px;
            height: 80px;
        }
        .listBannerImg1 a img{
            width: 100%;
            height: 100%;
        }
    </style>
</div>
<!--房价评估-->
<!--<div th:fragment="areaHighCharts">
    <a id="house_price_address" target="_blank"></a>
    <div class="areaChartimg"></div>
    <a id="hightCharts_jump" target="_blank"><div id="areaHighCharts" style="max-width:180px;height:110px;margin:0 auto"></div></a>
</div>-->
<!--折线图js-->
<div th:fragment="highChartsJs">
    <script src="https://static.fangxiaoer.com/js/highcharts6.0.7.js"></script>
    <script src="https://static.fangxiaoer.com/js/listHousePrise/house_price.js?v=20191228" type="text/javascript" charset="utf-8"></script>
</div>
<!--我要买房,求购-->
<div th:fragment="wantBuy">
    <div class="rightQzQgBtn">
        <a href="/static/saleHouse/saleHouse.htm" target="_blank"><i class="rightQzQgBtn-icon1"></i>我要卖房</a>
        <a th:href="@{'/helpSearch?ids=2'}" rel="2" dir="1" target="_blank"><i class="rightQzQgBtn-icon2"></i>求购</a>
    </div>
</div>
</body>
</html>