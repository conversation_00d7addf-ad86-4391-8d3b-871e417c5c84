<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:if="${!#strings.isEmpty(sehouse.title) && !#strings.isEmpty(sehouse.subName)}" th:text="${sehouse.title+'_沈阳'+sehouse.subName +'二手房_'+sehouse.room+ '室' + sehouse.hall +'厅' +  (#strings.toString(sehouse.area) ne '0' || !#strings.isEmpty(sehouse.area)?sehouse.area + '平米':'')+(
    sehouse.price eq '0' || sehouse.price eq '0.0'? '':(#strings.toString(sehouse.price).contains('.') ?  #strings.toString(sehouse.price).replaceAll('0+?$','').replaceAll('[.]$', '')+'万元' : sehouse.price+'万元'))+' - 房小二网'}"></title>
    <meta name="keywords" th:content="${sehouse.subName + '二手房,沈阳' + sehouse.subName +'二手房,' + sehouse.regionName +'二手房,沈阳二手房,' + sehouse.subName +'称现房'}"/>
    <meta name="description" th:content="${'房小二网二手房频道为您提供'+sehouse.title+'二手房信息，以及'+sehouse.regionName+sehouse.subName+'二手房价格、最新'+ sehouse.subName+'二手房房源'
    +sehouse.subName + '过户代办，'+sehouse.subName+'求租信息、地图位置等'+sehouse.subName+'房源信息。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang2/'+houseId+'.htm'}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="x-ua-compatible" content="IE=edge" >
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?t=20220126" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/saleHoseView2018.css?t=20190604" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/global/js/tab.js"></script>
    <script src="https://static.fangxiaoer.com/js/highcharts6.0.7.js"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/trendChart.css"/>
    <script src="/js/md5.js"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <link rel="stylesheet" href="https://g.alicdn.com/de/prismplayer/2.6.0/skins/default/aliplayer-min.css" />
    <script type="text/javascript" src="https://g.alicdn.com/de/prismplayer/2.6.0/aliplayer-min.js"></script>
    <link href="https://static.fangxiaoer.com/web/styles/main.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/reportPopup.css"/>
    <script src="/js/house/detail/details_collection.js" type="text/javascript"></script>
    <!--    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/deleteListings.css">-->

    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/houseAlbum.css">
    <script src="https://static.fangxiaoer.com/js/houseAlbum.js"></script>
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspendedCeiling.css" rel="stylesheet" type="text/css" />
</head>
<style>
    img {
        -webkit-filter: saturate(1.3) brightness(102%); /* Chrome, Safari, Opera */
        filter: saturate(1.3) brightness(102%);
    }
    .n2,.n4{display: none !important;}
    .title{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/title_line.gif) repeat-x bottom;height:50px;margin: 17px auto 12px;}
    .title p{font-size:20px;  color:#ff6600; border-bottom:5px solid #ff6600; float:left;padding:0 16px; line-height:46px;margin-right: 32px;}
    .canteen div p {
        display: block;
        width: 89%;
        float: left;
        margin-bottom: 6px;
        line-height: 27px;
    }
    .title a{/* float:right; */font-size: 12px; line-height:50px;color: #ff5200;}
    .left {
        width: 874px !important;
        float: left;
        border: 1px solid #eaeaea;
        margin-right: 0px !important;
        overflow: auto !important;
    }
    .villageImage>ul {
        height: 205px;
    }
    .left{border: none !important;}
    .house2,.villageImage {
        width: 868px;
    }
    .villageImage>ul {
        height: 205px;
    }
    .photo>ul{
        height: auto !important;
        width: 832px !important;
    }
    .house2 {
        width: 870px !important;
        border: 1px solid #ebebeb;
        margin-bottom: 30px;
    }
    .trendCharMap{
        /*border: 1px solid #ebebeb;*/
        width:850px;
    }


    .imgMax .prism-player{
        min-width: 620px !important;
        min-height: 425px !important;
        z-index: 9;
    }
    .prism-animation,.current-speed-selector{display: none}
    .right .recommend ul>a {
        text-align: center;
        display: block;
        color: #ff5200;
        line-height: 30px;
        border-top: 1px solid #eaeaea;
        margin-top: 5px;
    }
    .right .recommend ul>a:hover{background: #ededed}
    .main .header_sale ul li {
        float: left;
        margin-right: 30px;
    }
    .soucang{left: 0px; position: relative}
    .license{position: absolute;width: 260px;height: 370px;border: 1px solid #ccc;display: none;
        border-radius: 5px;z-index: 99;background-color: #FFF;bottom: 40px;right: 30px;padding: 5px;}
    .license_img{
        width: 100% !important;
        height: 100% !important;
        margin: 0px !important;
        border-radius: 0px !important;
    }
    .joinShop{    margin-top: 65px;}
    /* 安心好房样式 */
    .anin_info{
        font-size: 14px;
        color: #333;

    }
    .anin_interval{
        color: #999;
        margin-left: 4px;
        margin-right: 8px;
    }
    .anxin_logo{
        width: 77px;
        height: 18px;
        vertical-align: middle;
    }
    .anin_renzheng{
        vertical-align: middle;
        margin-left: 14px;
    }
    .anxin_color{
        color: #FF5200;
        margin-right: 22px;
        cursor: pointer;

    }
    .anxin_color:hover .xsimg{
        display:block;
    }
    .bubble{
        padding-bottom: 10px;
        position: absolute;
        bottom: 40px;
        left: 170px;
        display: none;
    }
    .xszimg {
        cursor: pointer;
        padding: 0 13px;
        /* width:387px; */
        height:31px;
        line-height: 15px;
        background:rgba(255,255,255,1);
        border:1px solid rgba(230,230,230,1);
        box-shadow:0px 3px 7px 0px rgba(38,39,39,0.15);
    }

    .xszimg::before {
        content: "";
        position: relative;
        bottom: -31px;
        left: 22px;
        width: 0;
        height: 0;
        display: block;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-top: 8px solid #fff;
    }
    .smore{ position: absolute; width: 122px; top: 33px; right: 18px;
        font-size: 14px; font-family: PingFang SC; font-weight: 500; color: #FF6600; cursor: pointer;}
    .smore:hover{ color: #0F5AFF !important;}
    .smore i{ width: 1rem; height: 1rem; display: inline-block; position: absolute; top: 0px; right: -8px;}

    /*风险提醒*/
    .risk{ display: inline-block; margin-left: 35px; font-size: 14px; color: #169BD5; cursor: pointer; user-select: none;}
    .riskm{ position: fixed; width: 55vw; min-width: 771px; height: 590px; top: 0; bottom: 0; left: 0; right: 0; margin: auto; background-color: #fff; border-radius: 5px; z-index: 9999999999; display: none; padding: 45px;}
    .rkm{ width: 100%; font-size: 14px;}
    .rkm h2{ font-size: 18px !important; margin-bottom: 10px;}
    .rkm h3{ margin-top: 5px !important; font-size: 14px;}
    .rclose{ position: absolute; top: 7px; right: 7px; width: 27px; height: 27px; border-radius: 50%; border: 2px solid #585858;
        background-image: url('https://static.fangxiaoer.com/m/static/images/search/close.png'); background-size: 60%; background-repeat: no-repeat; background-position: center; cursor: pointer; user-select: none;}
    .isCare{ background-image: url('https://static.fangxiaoer.com/m/static/images/Collection/selected.png') !important; background-size: 15px !important; background-repeat: no-repeat !important; background-position: center !important;}

    .p-note{

        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #666666;
        line-height: 19px;

        margin-top: 3px;
    }
    .newCardTels{
        margin-top: -12px;
        float: right !important;
    }
    .peo-text{
        margin-right: 0px !important;
    }
    .peo-b{
        font-size: 14px !important;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400 !important;
        color: #111111 !important;
        margin-top: 2px;
    }
    .people-img{
        margin-top: 13px;
    }
    .people-name{
        margin-bottom: 20px !important;
    }
    .ico-attestation{
        margin: 10px 0 0 0px !important;
    }
    .agent-text{
        margin-top: -12px !important;
    }
    .sddti{
        height: 37px !important;
        line-height: 37px !important;
    }
    .ico-name-attestation{
        margin-left: 0px !important;
    }
    .agent-text2{
        margin-bottom: 2px !important;
    }
    .agent-vip{
        width: 20px !important;
        height: 20px !important;
        margin-top: 5px !important;
    }
    #houseDetailOwner{
        margin-right: 0px !important;
    }









    .people-img img{
        width: 100px !important;
        height: 100px !important;
        border-radius: 50% !important;
    }

    .noHover:hover{
        cursor: auto !important;
    }

    .sddtj{
        width: 169px !important;
        white-space: nowrap !important;
        text-overflow: ellipsis !important;
        overflow: hidden;
    }



</style>

<body>
<input type="hidden" id="houseType" value="1"/>
<input type="hidden" id="Idhouse" th:value="${houseId}"/>
<input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}"/>
<input type="hidden" id="type" th:value="${'3'}">
<input type="hidden" id="projectId" th:value="${houseId}">
<input type="hidden" id="agentId" th:value="${sehouse.agencyId}">
<input type="hidden" id="mediaId" th:value="${houseVideo?.mediaID}">
<input type="hidden" id="videoPath" th:value="${videoPath}">
<input type="hidden" id="videoImgPath" th:value="${videoImgPath}">
<input type="hidden" id="housePan" th:value="${sehouse.housePan}">
<input type="panUrl" id="panUrl" th:value="${panUrl_VR}" style="display:none">
<input type="panUrl" id="panUrl_VRImg" th:value="${panUrl_VRImg}" style="display:none">
<input type="hidden" id="goods" value="1" />
<input type="hidden" value="1" id="collectType"/>
<!-- 二手房详情页 -->
<script>
    //图片自适应大小
    function imgSize() {
        //滚动大图
        var imgWidthHeght2 = $(".imgMax1 ul li").eq(0).width() / $(".imgMax1 ul li").eq(0).height()
        $(".imgMin ul li img").each(function () {
            if (parseInt($(this).width()) <= parseInt($(this).height())) {
                $(this).css({"height": "100%", "width": "auto"})
                $(this).css({
                    "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                    "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                })
            } else {
                $(this).css({"width": "100%", "height": "auto"})
                $(this).css({
                    "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                    "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                })
            }
        })
    }

</script>



<form name="form1" method="post" action="105868" id="form1">
    <div class="hid">
        <!--本小区房源-->
        <div class="title hid">
            <a th:href="'/saleHouses/-v'+${sehouse.subId}" class="more" target="_blank">查看本小区全部出售房源 ></a>
            <span>本小区房源</span>
        </div>
        <div class="row hid">
        </div>
    </div>

    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="cue=2,map=2,phone=2,type=2"></div>

    <!--面包屑-->
    <div class="w crumbs">您的位置：
        <a href="/" target="_blank">沈阳房产网</a> &gt;
        <a href="/saleHouses/" target="_blank">沈阳二手房</a> &gt;
        <a th:href="${'/saleHouses/r'+sehouse.regionId}" target="_blank" th:text="${sehouse.regionName + '二手房'}"></a> &gt;
        <a th:href="${'/saleHouses/r'+sehouse.regionId + '-j' + sehouse.platId}" target="_blank" th:text="${sehouse.platName + '二手房'}"></a> &gt;
        <a th:href="${'/saleVillages/'+sehouse.subId+'/index.htm'}" target='_blank' th:text="${sehouse.subName}"></a>
    </div>
    <!--基本信息-->
    <!--<th:block th:if="${#strings.toString(sehouse.isDel) ne '1'}">-->
    <div class="w main">
        <div class="header_sale" style="position: relative;">
            <h1 class="title1" th:text="${sehouse.title}"></h1>
            <!-- 安心好房气泡 -->
            <div class="bubble">
                <div class="xszimg">
                    本房源享受安心房源保障，最高保额65万元，<a target="_blank" href="/static/secondhouse_guarantee_info.htm" style="color: #2B61CC;">了解保障规则></a>
                </div>
            </div>
            <ul class="axinfo">
                <!--                <li th:if="${!#strings.isEmpty(updateTime)}"  th:text="${'更新时间：'+updateTime}"></li>-->
                <li class="anin_info" style="margin-right: 35px" th:if="${#strings.equals(sehouse.anXuan,'-1')}">
					<span class="bubble_none">
						<img src="https://static.fangxiaoer.com/web/images/sy/anxin/anxin_logo.png" class="anxin_logo">
						<span class="anin_interval">|</span>真实在售
						<img src="https://static.fangxiaoer.com/web/images/sy/anxin/renzheng.png" class="anin_renzheng">
					</span>
                    <span class="anxin_color jiaoyi">安心交易</span>
                </li>
                <li th:if="${!#strings.isEmpty(sehouse.schoolAreaMIDStr)}"  th:text="${'审核时间：'+sehouse.schoolAreaMIDStr}" style="margin-right: 20px;"></li>
                <li th:if="${!#strings.equals(sehouse.anXuan,'-1')}" th:text="${sehouse.visitedNum ne '0'}?${sehouse.visitedNum+'人已浏览'}:''"></li>
                <a class="soucang" data-toggle="modal" href="#login" th:if="${sehouse.cooperationState eq null}"><i style="margin: 6px 0px 0 0;"></i>收藏</a>
                <!--虚假举报-->
                <div th:include="fragment/falsityreport:: falsityreport"></div>
            </ul>
            <!--分享标签-->
            <!--            <div th:include="fragment/fragment:: share_code"></div>-->


        </div>
        <!--房源轮播图-->
        <div class="houseAlbum" id="houseAlbum">
            <!--扫码拨号落地小程序-->
            <div class="img-Applets" th:if="${sehouse.cooperationState eq null}">
                <i class="img-Applets-Close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" alt=""></i>
                <img class="img-Applets-Show" alt="" src="">
                <p>扫码到微信</p>
            </div>
            <div class="big_photo" id="big_photo">
                <ul>
                    <li th:if="${!#lists.isEmpty(sehouse.housePan)}" class="noAClick">
                        <a class='panUrl' target='_blank' th:href="${sehouse.housePan.panUrl}">
                            <img onload='imgSize()' style='width: 100%; height: auto;margin-left: -35px;' class='panUrl_VRImg' th:src="${#strings.isEmpty(sehouse.housePan.panImageUrl)?'':#strings.toString(sehouse.housePan.panImageUrl).replaceAll('middle','big')}" >
                        </a>
                    </li>
                    <li class="prism-player2 noAClick " id="J_prismPlayer" th:if="${houseVideo?.mediaID}" style="height: 417px !important;"></li>
                    <li  th:if="${!#lists.isEmpty( sehouse.pic) }"  th:each="pic,p:${sehouse.pic}" class="playerPause">
                        <img th:src="${#strings.isEmpty(pic.url)?'':#strings.toString(pic.url).replaceAll('middle','big')}" />
                    </li>
                    <li th:if="${#lists.isEmpty( sehouse.pic)}">
                        <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" />
                    </li>
                </ul>
                <div  th:if="${!#lists.isEmpty( sehouse.pic)}" th:each="pic,p:${sehouse.pic}" style="display: none;">
                    <input id="indexPic" th:if="${p.index == 0}" th:value="${#lists.toList(sehouse.pic).get(0).url}" style="display: none;"/>
                </div>
            </div>
            <div class="min_photo">
                <div class="min_photoMain clearfix" id="min_photoMain">
                    <ul>
                        <li th:if="${!#lists.isEmpty(sehouse.housePan)}">
                            <img onload='imgSize()' class='panUrl_VRImg' th:src="${#strings.isEmpty(sehouse.housePan.panImageUrl)?'':#strings.toString(sehouse.housePan.panImageUrl).replaceAll('middle','big')}" >
                        </li>
                        <li th:if="${houseVideo?.mediaID}">
                            <i class='videoIcon'></i>
                            <img th:src="${videoImgPath}">
                        </li>
                        <li  th:if="${!#lists.isEmpty( sehouse.pic)}" th:each="pic,p:${sehouse.pic}" class="playerPause">
                            <img th:src="${#strings.isEmpty(pic.url)?'':#strings.toString(pic.url)}"  id="imgssss"/>
                        </li>
                        <li th:if="${#lists.isEmpty( sehouse.pic)}">
                            <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" />
                        </li>
                    </ul>
                </div>
                <div class="min_prev_btn1 playerPause" id="min_prev_btn1"><s></s></div>
                <div class="min_next_btn1 playerPause" id="min_next_btn1"><s></s></div>
            </div>
        </div>
        <!--弹窗轮播大图-->
        <div class="photo_Popup"></div>
        <div class="large_photo" id="large_photo">
            <div class="photo_Popup_xx"></div>
            <div class="large_photoMain" id="large_photoMain">
                <ul>
                    <li th:if="${!#lists.isEmpty(sehouse.housePan)}">
                        <a class='' target='_blank' th:href="${sehouse.housePan.panUrl}">
                            <img onload='imgSize()' class='panUrl_VRImg' th:src="${#strings.isEmpty(sehouse.housePan.panImageUrl)?'':#strings.toString(sehouse.housePan.panImageUrl).replaceAll('middle','big')}" >
                        </a>
                    </li>
                    <li class="prism-player2" id="J_prismPlayer2" th:if="${houseVideo?.mediaID}" style="width: 1170px;height: 100%;"></li>
                    <li th:if="${#lists.isEmpty( sehouse.pic)}" class="playerPause">
                        <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" />
                    </li>
                    <li th:if="${!#lists.isEmpty( sehouse.pic)}" th:each="pic,p:${sehouse.pic}">
                        <img  th:src="${#strings.toString(pic.url).replace('middle','big')}" alt="" />
                    </li>
                </ul>
            </div>
            <div class="large_prev_btn1 playerPause" id="large_prev_btn1"></div>
            <div class="large_next_btn1 playerPause" id="large_next_btn1"></div>
        </div>

        <div th:class="${#strings.toString(sehouse.isDel) ne '1' ?'mainContent':(#strings.toString(sehouse.saleState) eq '4' and #strings.toString(sehouse.state ) eq '3' ?'mainContent sellState1':'mainContent sellState3')}">
            <div class="price"><span id="pt"><b th:text="${sehouse.price eq '0' ? '面议':(#strings.toString(sehouse.price).contains('.') ?  #strings.toString(sehouse.price).replaceAll('0+?$','').replaceAll('[.]$', '') : sehouse.price)}"></b><th:block th:if="${sehouse.price ne '0'}">万</th:block></span>
                <p th:if="${sehouse.price ne '0'}" class="priceKuang"><s><th:block th:text="${#strings.toString(sehouse.unitPrice).contains('.') ?  #strings.toString(sehouse.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : sehouse.unitPrice}"></th:block>元/m² <a class="toMortgage" href="#mortgageBtn">贷款计算器</a></s></br><span  style="color:#999;" >首付<i class="shoufu"></i>万　月供<i class="debx"></i>元(按揭30年)</span></p>
            </div>
            <div class="basic">
                <ul>
                    <li>
                        <span>小区名称</span>
                        <p>
                            <a th:href="${'/saleHouses/-r'+sehouse.regionId}" target='_blank' th:text="${sehouse.regionName}" > </a>
                            -
                            <a th:href="${'/saleHouses/-r'+sehouse.regionId + '-j' + sehouse.platId}" target='_blank' th:text="${sehouse.platName}" > </a>
                            -
                            <a th:href="${'/saleVillages/'+sehouse.subId+'/index.htm'}"  target='_blank' th:text="${sehouse.subName}"> </a>
                        </p>
                    </li>
                    <!--<li>-->
                    <!--<span>产权性质</span>-->
                    <!--<p th:text="${sehouse.propertyRightTypeName ne ''}?${sehouse.propertyRightTypeName}:'暂无资料'"></p>-->
                    <!--</li>-->
                    <li>
                        <span>装修情况</span>
                        <p th:text="${sehouse.fitmentTypeName ne ''}?${sehouse.fitmentTypeName}:'暂无资料'" style=" display: inline-block; width: unset;"></p>
                        <div class="risk">《风险提醒》</div>
                    </li>
                    <li>
                        <span>所在地址</span>
                        <p ><th:block th:text="${sehouse.address}"></th:block><a id="MapPeiTao"><s></s></a></p>
                    </li>
                </ul>
            </div>
            <div class="type">
                <ul>
                    <li>
                        <b th:text="${sehouse.layout}"></b>
                        <p th:text="${sehouse.totleFloorNumber ne null and sehouse.totleFloorNumber ne '' ? sehouse.floorDesc+'/'+sehouse.totleFloorNumber+'层': ''}"></p>
                    </li>
                    <li>
                        <b th:text="${sehouse.area+'m²'}"></b>
                        <p>建筑面积</p>
                    </li>
                    <li>
                        <b th:text="${sehouse.forward}"></b>
                        <p th:text="${#strings.isEmpty(sehouse.buildDate)}?'':${sehouse.buildDate + '年建筑'}"></p>
                    </li>
                </ul>
            </div>
            <div class="card jjr" th:if="${#strings.toString(sehouse.isDel) ne '1' and sehouse.cooperationState eq null}" style="position: relative;overflow: inherit;">
                <div class="people-img">
                    <a class="people-img-join" th:if="${sehouse.keeperLevel2 ne '1' and sehouse.agentState eq '1'}"
                       th:href="${#strings.isEmpty(sehouse.agencyId) ? '' :'/agent/second/'+sehouse.agencyId}"  target="_blank">
                        <img th:src="${sehouse.keeperPic2 ne null and sehouse.keeperPic2 ne''}?${sehouse.keeperPic2}:@{https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png}"  />
                    </a>
                    <img th:if="${sehouse.keeperLevel2 ne '1' and sehouse.agentState ne '1'}"
                         th:src="${sehouse.keeperPic2 ne null and sehouse.keeperPic2 ne''}?${sehouse.keeperPic2}:@{https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png}"/>
                    <img th:if="${sehouse.keeperLevel2 eq '1'}" th:src="${sehouse.keeperPic2 ne null and sehouse.keeperPic2 ne''}?${sehouse.keeperPic2}:@{https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png}"  />
                    <!--                    <a th:style="${sehouse.keeperLevel2 ne '1'}?'display:block':'display:none'"  th:href="${#strings.isEmpty(sehouse.agencyId) ? '' :'/agent/second/'+sehouse.agencyId}"  target="_blank" class="join-Shop">进入Ta的店铺</a>-->
                </div>

                <dl  th:class="${sehouse.keeperLevel2} eq '1' ? 'normolTel':'agentTel'">
                    <dt class="people-name">
                        <a th:if="${sehouse.keeperLevel2 ne '1' and sehouse.agentState eq '1'}" th:href="${#strings.isEmpty(sehouse.agencyId) ? '' :'/agent/second/'+sehouse.agencyId}"  target="_blank" style="width: 100%; margin-bottom: 17px;">
                            <!--                            <b  th:text="${sehouse.keeperName2}"></b>-->
                            <b  id="houseDetailOwner"></b>
                            <img th:if="${sehouse.intermediaryState eq '1'}" class="agent-vip" src="https://static.fangxiaoer.com/web/images/sy/house/agent-vip.png" >
                        </a>
                        <a th:if="${sehouse.keeperLevel2 ne '1' and sehouse.agentState ne '1'}">
                            <!--                            <b  th:text="${sehouse.keeperName2}"></b>-->
                            <b  id="houseDetailOwner" class="noHover"></b>
                        </a>
                        <b class="peo-text" th:if="${sehouse.keeperLevel2 eq '1'}" th:text="${sehouse.keeperName2}"></b>  <b class="peo-b" th:if="${sehouse.keeperLevel2 eq '1'}" >（个人）</b>
                        <!--                        <span th:text="${sehouse.intermediaryName ne null and sehouse.intermediaryName ne ''}?${sehouse.intermediaryName}:''"></span>-->
                    </dt>
                    <th:block th:if="${sehouse.keeperLevel2 ne '1'}">
                        <!--新增经纪人相关认证-->
                        <div >

                            <!--                            <div style="float: left"  class="agengt-ico-new" th:if="${sehouse.agentBusinessCardForPc eq null or sehouse.agentBusinessCardForPc eq ''}"></div>-->

                            <div class="ico-attestation agent-text" style="margin: 24px 0 0 10px;">
                                <div class="ico-name-attestation agent-text2 " th:if="${sehouse.hasIdCard eq '1'}"><i></i><span>实名认证</span></div>
                                <th:block th:if="${sehouse.agentState eq '1'}">
                                    <div class="sddtj"><lable th:text="${sehouse.agentIntermediaryAlias}"></lable></div>
                                </th:block>
                                <th:block th:if="${sehouse.agentState ne '1'}">
                                    <div class="sddtj">经纪人</div>
                                </th:block>
                            </div>

                        </div>
                        <!--<div  class="encoded" th:unless="${sehouse.agentBusinessCardForPc eq null or sehouse.agentBusinessCardForPc eq ''}"
                              onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)" style="margin: 0 0 11px 0; height: unset; float: unset; ">
                            <i>公司执照编码：</i>

                            <span>
                                <th:block th:text="${sehouse.agentBusinessNum}"></th:block>
                            </span>
                        </div>-->
                        <!--                        <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal">-->
                        <!--                            <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" class="newChat">-->
                        <!--                                <img src="https://static.fangxiaoer.com/web/images/sy/sale/s_5.png" >-->
                        <!--                                <text>在线聊呗</text>-->
                        <!--                            </div>-->
                        <!--                        </a>-->
                        <!--                        <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/im/'+houseId+'-1'}" target="_blank">-->
                        <!--                            <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" class="newChat">-->
                        <!--                                <img src="https://static.fangxiaoer.com/web/images/sy/sale/s_5.png" >-->
                        <!--                                <text >在线聊呗</text>-->
                        <!--                            </div>-->
                        <!--                        </a>-->
                    </th:block>

                    <!--认证icon-->
                    <div class="ico-attestation" th:if="${sehouse.keeperLevel2 eq '1' and sehouse.realEstateStatus eq '100'}"
                         style="margin: 12px 0 0 0px; height: 35px; width: unset;">
                        <div class="ico-name-attestation" th:if="${sehouse.hasIdCard eq '1'}" style="margin-right: 13px; margin-left: 0px;"><i></i><span>实名认证</span></div>
                        <div class="ico-name-attestation" th:if="${sehouse.realEstateStatus eq '100'}" style="width: 104px; margin-right: 20px; margin-left: 7px;"><i class="isCare"></i><span>已上传房证</span></div>
                    </div>

                    <dd class="newCardTels" th:if="${sehouse.keeperLevel2 ne '1'}">
                        <div class="sddti">
                            电话咨询
                        </div>
                        <div class="show-CardTels">
                            <img id="second1"  src="" alt="">
                            <p>微信扫码拨号</p>
                            <p th:if="${sehouse.keeperLevel2 ne '1'}"><i>快速获取经纪人联系方式</i></p>
                            <p th:unless="${sehouse.keeperLevel2 ne '1'}"><i>快速获取房主联系方式</i></p>
                        </div>
                    </dd>

                    <dd class="newCardTels" style="width: 154px;" th:if="${sehouse.keeperLevel2 eq '1'}">
                        <div class="sddti">
                            电话咨询
                        </div>
                        <div class="show-CardTels">
                            <img id="second1"  src="" alt="">
                            <p>微信扫码拨号</p>
                            <p th:if="${sehouse.keeperLevel2 ne '1'}"><i>快速获取经纪人联系方式</i></p>
                            <p th:unless="${sehouse.keeperLevel2 ne '1'}"><i>快速获取房主联系方式</i></p>
                        </div>
                    </dd>



                    <div style="clear: both;"></div>
                    <div class="p-note"> 如在沟通或交易过程中遇到任何问题，均可致电房小二网平台服务电话：************</div>
                    <!--<th:block th:if="${sehouse.keeperLevel2 ne '1'}">
                        <div class="license" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                            <img  th:src="${sehouse.agentBusinessCardForPc}" class="license_img">
                        </div>
                        <div style="clear: both;"></div>
                        &lt;!&ndash;                        <div th:if="${sehouse.agentBusinessCardForPc eq null or sehouse.agentBusinessCardForPc eq ''}" class="sddtk">房源录入人，熟悉本房源</div>&ndash;&gt;
                        &lt;!&ndash;                        <div th:unless="${sehouse.agentBusinessCardForPc eq null or sehouse.agentBusinessCardForPc eq ''}" class="sddtk" style="margin-top: 10px;">房源录入人，熟悉本房源</div>&ndash;&gt;
                    </th:block>-->
                </dl>
                <div style="clear: both;"></div>
                <script src="/js/appointmentLook.js" type="text/javascript"></script>
                <script>
                    $("#CommentListAdd").click(function () {
                        $("#login").show();
                        $(".reportPopupHb").show();
                    })
                    function changeColor(e) {
                        $(e).css("background-color","#188CDE")
                    }
                    function changeColor2(e) {
                        $(e).css("background-color","#32a3f2")
                    }
                </script>
            </div>
            <div class="sole-people" th:if="${#strings.toString(sehouse.isDel) ne '1' and sehouse.cooperationState ne null}">
                <input type="hidden" id="showMobile" th:value="${sehouse.showMobile}">
                <span class="sole-span">联系人</span>
                <div class="sole-people-name">
                    <b  th:text="${sehouse.showContact}"></b>
                    <div class="sole-tel" th:data-tel="${sehouse.showMobile}"><i><img src="https://static.fangxiaoer.com/web/images/ico/icon-newCardTels.png" alt=""></i><span>查看联系电话</span></div>
                </div>
                <!--独家房源联系人弹窗-->
                <div class="sole-tc-full"></div>
                <div class="sole-tc">
                    <p th:text="${sehouse.showContact}"></p>
                    <p th:text="${sehouse.showMobile}"></p>
                    <div>确定</div>
                </div>
                <script>
                    $(".sole-tel").click(function () {
                        $(".sole-tc-full").show()
                        $(".sole-tc").show()
                    })
                    $(".sole-tc div").click(function () {
                        $(".sole-tc-full").hide()
                        $(".sole-tc").hide()
                    })
                </script>
            </div>

        </div>
    </div>
    <div class="w">
        <div class="left">
            <div class="details">
                <div class="head">房源描述</div>
                <ul>
                    <!--<li class="" th:style="${sehouse.houseTrait ne ''}?'display:block':'display:none'">-->
                    <!--<span>房源标签</span>-->
                    <!--<div>-->
                    <!--<th:block th:each="l:${list}" >-->
                    <!--<b th:text="${l}"></b>&nbsp;-->
                    <!--</th:block>-->

                    <!--</div>-->
                    <!--</li>-->
                    <li>
                        <span>小区详情</span>
                        <div>
                            <ul>
                                <li>
                                    <span>小区名称：</span>
                                    <p th:text="${sehouse.subName}"></p>
                                </li>
                                <li>
                                    <span>绿&ensp;化&ensp;率：</span>
                                    <p th:text="${sehouse.landScaping ne '' and sehouse.landScaping ne null}?${sehouse.landScaping}:'暂无资料'"></p>
                                </li>
                                <li>
                                    <span>物业公司：</span>
                                    <p th:text="${sehouse.propertyCom ne '' and sehouse.propertyCom ne null}?${sehouse.propertyCom}:'暂无资料'"></p>
                                </li>
                                <li>
                                    <span>物&ensp;业&ensp;费：</span>
                                    <p th:text="${sehouse.propertyFee ne '' and sehouse.propertyFee ne null and sehouse.propertyFee ne '0.0'}?${sehouse.propertyFee+'元/m²·月'}:'暂无资料'"></p>
                                </li>
                                <!--<li>-->
                                <!--<span>租&emsp;&emsp;房：</span>-->
                                <!--<a th:if="${sehouse.rentCount ne '' and sehouse.rentCount ne null}" th:href="${'/rents/-v'+sehouse.subId+'-r'+sehouse.regionId}" target="_blank">-->
                                <!--<p th:text="${sehouse.rentCount+'套'}" style="color:#2f51b3;"> </p>-->
                                <!--</a>-->
                                <!--<p th:if="${sehouse.rentCount eq '' or sehouse.rentCount eq null}" th:text="${'暂无资料'}"></p>-->
                                <!--</li>-->

                                <!--<li>-->
                                <!--<span>二&ensp;手&ensp;房：</span>-->
                                <!--<a th:if="${sehouse.saleCount ne '' and sehouse.saleCount ne null}" th:href="${'/saleHouses/-v'+sehouse.subId+'-r'+sehouse.regionId}" target="_blank">-->
                                <!--<p th:text="${sehouse.saleCount+'套'}" style="color:#2f51b3;"> </p>-->
                                <!--</a>-->
                                <!--<p th:if="${sehouse.saleCount eq '' or sehouse.saleCount eq null}" th:text="${'暂无资料'}"></p>-->
                                <!--</li>-->
                                <!--<li>-->
                                <!--<span>成交房源：</span>-->
                                <!--<a th:if="${sehouse.dealCount ne '' and sehouse.dealCount ne null and sehouse.dealCount ne '0'}" th:href="${'/dealSales/-v'+sehouse.subId+'-r'+sehouse.regionId}" target="_blank">-->
                                <!--<p th:text="${sehouse.dealCount+'套'}" style="color:#2f51b3;"> </p>-->
                                <!--</a>-->
                                <!--<p th:if="${sehouse.dealCount eq '' or sehouse.dealCount eq null or sehouse.dealCount eq '0'}" th:text="${'暂无资料'}"></p>-->
                                <!--</li>-->
                            </ul>
                        </div>
                    </li>
                    <th:block th:if="${sehouse.cooperationState eq null}">
                        <li th:if="${!#strings.isEmpty(sehouse.describe)}">
                            <span>核心卖点</span>
                            <div class="fyms" th:utext="${sehouse?.describe}"></div>
                        </li>
                        <!--<li class="" th:if="${!#strings.isEmpty(sehouse.mentality)}">
                            <span>业主心态</span>
                            <div class="fyms" th:utext="${sehouse.mentality}"></div>
                        </li>-->
                        <li class=""  th:if="${!#strings.isEmpty(sehouse.serviceIntro) || !#strings.isEmpty(agentLabel)}">
                            <section th:if="${!#strings.isEmpty(sehouse.serviceIntro)}">
                                <span>服务介绍</span>
                                <div class="fyms" th:utext="${sehouse.serviceIntro}"></div>
                            </section>
                            <!--经纪人服务标签-->
                            <section th:if="${!#strings.isEmpty(agentLabel)}">
                                <span>服务特色</span>
                                <div class="grfw">
                                    <span  th:each="agentLabel:${agentLabel}"  th:text="${agentLabel.name}"></span>
                                </div>
                            </section>
                        </li>
                    </th:block>

                    <li class="" th:if="${!#strings.isEmpty(sehouse.traffic) || !#strings.isEmpty(sehouse.education) || !#strings.isEmpty(sehouse.market)
         || !#strings.isEmpty(sehouse.hospital) || !#strings.isEmpty(sehouse.bank) || !#strings.isEmpty(sehouse.othersettings)}">
                        <span>小区配套</span>
                        <!--<div th:text="${sehouse.configuration}"></div>-->
                        <div style="border:0" class="canteen">
                            <div th:if="${!#strings.isEmpty(sehouse.traffic)}"><span>交通：</span><p th:text="${sehouse.traffic}"></p></div><!-- traffic-->
                            <div th:if="${!#strings.isEmpty(sehouse.education)}"><span>学校：</span><p th:text="${sehouse.education}"></p></div><!-- education -->
                            <div th:if="${!#strings.isEmpty(sehouse.market)}"><span>商场：</span><p th:text="${sehouse.market}"></p></div><!-- market -->
                            <div th:if="${!#strings.isEmpty(sehouse.hospital)}"><span>医院：</span><p th:text="${sehouse.hospital}"></p></div><!-- hospital -->
                            <div th:if="${!#strings.isEmpty(sehouse.bank)}"><span>银行：</span><p th:text="${sehouse.bank}"></p></div><!-- bank -->
                            <div th:if="${!#strings.isEmpty(sehouse.othersettings)}"><span>其他：</span><p th:text="${sehouse.othersettings}"></p></div><!-- othersettings -->
                        </div>
                    </li>
                </ul>
            </div>
            <div class="details details2" th:if="${!#strings.isEmpty(sehouse.weAre) or !#strings.isEmpty(sehouse.serviceProcess) or !#strings.isEmpty(sehouse.servicePromise) or !#strings.isEmpty(sehouse.suggestion)}">
                <div class="head">服务保障</div>
                <ul>
                    <li th:if="${!#strings.isEmpty(sehouse.weAre)}">
                        <i class="serve serve-1"></i>
                        <span>我们是谁？</span>
                        <div class="fyms" th:utext="${sehouse.weAre}"></div>
                    </li>
                    <li th:if="${!#strings.isEmpty(sehouse.servicePromise)}">
                        <i class="serve serve-2"></i>
                        <span>我的承诺</span>
                        <div class="fyms" th:utext="${sehouse.servicePromise}"></div>
                    </li>
                    <li th:if="${!#strings.isEmpty(sehouse.serviceProcess)}">
                        <i class="serve serve-3"></i>
                        <span>服务流程</span>
                        <div class="fyms" th:utext="${sehouse.serviceProcess}"></div>
                    </li>
                    <li th:if="${!#strings.isEmpty(sehouse.suggestion)}">
                        <i class="serve serve-4"></i>
                        <span>服务建议</span>
                        <div class="fyms" th:utext="${sehouse.suggestion}"></div>
                    </li>
                </ul>
            </div>
            <!--安心好房模块-->
            <div class="w photo" style="margin-bottom: 30px;height: 70px; display: flex;align-items: center;font-size: 14px;color: #333;font-weight: 700;" th:if="${#strings.equals(sehouse.anXuan,'-1')}">
                <img src="https://static.fangxiaoer.com/web/images/sy/anxin/anxin_logo.png" style="margin-right: 14px;">
                该经纪人已加入“安心交易”保障服务，最高保额65万，<a href="/static/secondhouse_guarantee_info.htm" target="_blank" style="color: #2B61CC;font-weight: 500;">了解保障规则></a>
            </div>
            <!--房源相册模块-->
            <div class="w photo" th:if="${!#lists.isEmpty( sehouse.picOrange)}" id="big_photo2">
                <div class="head">房源相册</div>
                <ul>
                    <li class="span4" th:each="pic:${sehouse.picOrange}">
                        <img th:src="${#strings.isEmpty(pic.url)?'':pic.url}" th:alt="${pic.photoName =='室内图'?'房源图' :pic.photoName}"/>
                        <p th:text="${pic.photoName =='室内图'?'房源图' :pic.photoName}"></p>
                    </li>
                </ul>
            </div>
            <!--房源相册弹窗轮播大图-->
            <div class="large_photo" id="large_photo2">
                <div class="photo_Popup_xx"></div>
                <div class="large_photoMain" id="large_photoMain2">
                    <ul>
                        <li th:each="pic:${sehouse.picOrange}">
                            <img th:src="${#strings.toString(pic.url).replace('middle','big')}" th:alt="${pic.photoName =='室内图'?'房源图' :pic.photoName}"/>
                        </li>
                    </ul>
                </div>
                <div class="large_prev_btn1 playerPause" id="large_prev_btn12"></div>
                <div class="large_next_btn1 playerPause" id="large_next_btn12"></div>
            </div>


            <!--您可能感兴趣的房源-->
            <div class="interest_house"  style="width: 870px" th:if="${!#lists.isEmpty(interestHouse)  and #lists.size(interestHouse) ge 5}">
                <div class="interest_house_title">您可能感兴趣的房源</div>
                <ul>
                    <li th:each="second,i:${interestHouse}" th:if="${ #strings.toString(second.houseId) != #strings.toString(houseId)}">
                        <div class="interest_house_images">
                            <a th:href="'/salehouse/'+${second.houseId}+'.htm'" target="_blank">
                                <img th:src="${#strings.isEmpty(second.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : second.pic}"
                                     th:alt="${second.subName}">
                                <span th:text="${second.subName}"></span>
                            </a>
                        </div>
                        <div class="interest_house_info">
                            <a th:href="'/salehouse/'+${second.houseId}+'.htm'" target="_blank">
                                <span class="inter_house_title" th:text="${second.title}"></span>
                            </a>
                            <div class="interest_house_details">
                                <span th:if="${#strings.isEmpty(second.price) or (#strings.toString(second.price) eq '0.0')}"  th:text='面议'></span>
                                <span th:if= "${!#strings.isEmpty(second.price) and #strings.toString(second.price) ne '0.0'}" >
                                    <th:block th:text="${#strings.toString(second.price).contains('.')? #strings.toString(second.price).replaceAll('0+?$','').replaceAll('[.]$', '') : second.price}"></th:block>
                                    <i th:text="${#strings.isEmpty(second.price) or (#strings.toString(second.price) eq '0.0')?'':'万'}"></i>
                                </span>
                                <s  th:text="${#strings.toString(second.area).contains('.')? #strings.toString(second.area).replaceAll('0+?$','').replaceAll('[.]$', '') : second.area}+'m²'"></s>
                                <b th:text="${second.room+'室'+second.hall+'厅'+second.toilet+'卫'}"></b>
                            </div>

                        </div>
                    </li>
                </ul>
            </div>


            <div th:if="${!#strings.isEmpty(sehouse.subId) and sehouse.subId ne 0}">
                <!--<div class="expert" th:if="${sehouse.expertComment ne null and #lists.toList(sehouse.expertComment).size() ne 0}">
                    <div class="head">专家解读 <a th:href="${'/saleVillages/'+sehouse.subId+'/plotExpertInterpretation.htm'}" target="_blank">更多解读></a> </div>


                    <ul th:each="e:${sehouse.expertComment}">

                        <div class="expertImg">
                            <a th:if="${#strings.toString(e.isEnd) ne '0'}" th:href="${'/agent/second/'+(#strings.toString(e.memberId).contains('.') ?  #strings.toString(e.memberId).replaceAll('0+?$','').replaceAll('[.]$', '') : e.memberId)}">
                                <img th:if="${#strings.toString(e.avatar) ne null && #strings.toString(e.avatar) ne ''}"  th:src="${e.avatar}" alt="">
                                <img th:if="${#strings.toString(e.avatar) eq null || #strings.toString(e.avatar) eq ''}"  src="https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png" alt="">
                                <i th:text="${e.realName}"></i>
                            </a>
                            <a th:if="${#strings.toString(e.isEnd) eq '0'}">
                                <img th:if="${#strings.toString(e.avatar) ne null && #strings.toString(e.avatar) ne ''}"  th:src="${e.avatar}" alt="">
                                <img th:if="${#strings.toString(e.avatar) eq null || #strings.toString(e.avatar) eq ''}"  src="https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png" alt="">
                                <i th:text="${e.realName}"></i>
                            </a>
                        </div>
                        <div class="expertRight">
                            <li>
                                <i class="tese1">特色</i>
                                <div>
                                    <p th:if="${!#strings.isEmpty(e.layout)}" th:text="${'【小区户型】'+e.layout}">【小区户型】目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                    <p th:if="${!#strings.isEmpty(e.matching)}" th:text="${'【生活配套】'+e.matching}">【小区配置】物业为沈阳建筑服务有限公司，物业费为2元/㎡/月，物业服务好。</p>
                                    <p th:if="${!#strings.isEmpty(e.facilities)}" th:text="${'【小区设施】'+e.facilities}">【小区设施】物业为沈阳建筑服务有限公司，物业费为2元/㎡/月，物业服务好。</p>
                                    <p th:if="${!#strings.isEmpty(e.traffic)}" th:text="${'【交通情况】'+e.traffic}">目前在售高层产品，户型为44、83、104㎡，南北通透。</p>
                                    <p th:if="${!#strings.isEmpty(e.school)}" th:text="${'【附近学校】'+e.school}">【学校】物业为沈阳建筑服务有限公司，物业费为2元/㎡/月，物业服务好。</p>
                                    <p th:if="${!#strings.isEmpty(e.other)}" th:text="${'【其他描述】'+e.other}">【其他描述】物业为沈阳建筑服务有限公司，物业费为2元/㎡/月，物业服务好。</p>
                                </div>

                            </li>
                            <li th:if="${!#strings.isEmpty(e.insufficient)}">
                                <i class="tese2">不足</i>
                                <div>
                                    <p th:text="${e.insufficient}"></p>
                                </div>

                            </li>
                            <li th:if="${!#lists.isEmpty(e.pic)}">
                                <img  th:each="pic:${e.pic}" th:src="${pic.picUrl}" th:alt="${pic.picName}">
                            </li>
                        </div>
                    </ul>

                </div>
                <div class="village" th:if="${sehouse.subdistrictsAsk ne null and #lists.toList(sehouse.subdistrictsAsk).size() ne 0}">
                    <div class="head">小区问答 <a th:href="${'/saleVillages/'+sehouse.subId+'/plotAsk.htm'}" target="_blank">更多回答></a> </div>
                    <ul>
                        <li th:each="a:${sehouse.subdistrictsAsk}">
                            <i class="iconQuestion"></i>
                            <div>
                                <a th:href="${'/saleVillages/'+a.id+'/getAskDetail.htm'}" target="_blank"><h5 th:text="${'【'+a.subName+'】'+a.askContent}">【城建花园】这里的住户基本是什么职业呢？白领集中么？  <span th:if="${a.replyTime}" th:text="${#strings.toString(a.replyTime).replace('.','-')}">2018年4月11日15:56:57</span>
                                    <span th:if="${#strings.isEmpty(a.replyTime)}" th:text="${#strings.toString(a.askTime).replace('.','-')}">2018年4月11日15:56:57</span>
                                </h5></a>
                                <p>
                                    <th:block th:if="${a.isEnd eq '1'}">
                                        <a th:href="'/agent/second/' + ${a.replyId}"><span th:text="${a.replyName}">杨彬：</span></a>
                                    </th:block>
                                    <th:block th:if="${a.isEnd eq'0'}">
                                        <span th:text="${a.replyName}">杨彬：</span>
                                    </th:block>
                                    <th:block th:text="${a.replyContent}"></th:block></p>
                            </div>
                            <a th:href="${'/saleVillages/'+a.id+'/getAskDetail.htm'}" target="_blank">我补充></a>
                        </li>

                    </ul>
                    <a th:href="${'/saleVillages/'+sehouse.subId+'/forAddPlotAsk.htm'}" target="_blank" class="villagebtn">我要提问</a>
                    <a th:href="${'/saleVillages/'+sehouse.subId+'/getAskNoReply.htm'}" target="_blank" class="villagebtn">我要回答</a>
                </div>-->
                <th:block th:if="${isRound == 1}">
                    <!--<div class="trendChar house2" style="margin-top:30px" >
                        <div class="house2Title">房价走势</div>
                        <div class="trendCharContent">
                            <dl>
                                <dt>
                                    <div class="trendChartTitle" style="overflow: hidden;"><i style="background: #75d88f;float: left;margin-top: 7px;"></i><div id = "community_Name" style="float: left">城建花园</div></div>
                                    <div class="trendChartPrice" th:if="${#strings.toString(villageDetail.unitPrice) ne '0.0'}"><b  th:text="${'￥ '} +${#strings.toString(villageDetail.unitPrice).contains('.') ?  #strings.toString(villageDetail.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', '')+ '元/㎡' : villageDetail.unitPrice+ '元/㎡'}">￥8652元/㎡ </b>
                                        <span  th:if="${#strings.toString(villageDetail.increaseRate) eq '0.0'}" th:text="${'&#45;&#45;&#45;&#45;'}" style="font-weight: normal"></span>
                                        <span  th:if="${#strings.toString(villageDetail.increaseRate).substring(0,1) ne '-' and #strings.toString(villageDetail.increaseRate) ne '0.0'}" th:text="${'比本小区环比高'+villageDetail.increaseRate+'%'}" style="font-weight: normal">比本小区环比高0.38%</span>
                                        <span class="dis"  th:if="${#strings.toString(villageDetail.increaseRate).substring(0,1) eq '-'}" th:text="${'比本小区环比低' + #strings.toString(villageDetail.increaseRate).substring(1)+'%'}">比本小区环比高0.38%</span>
                                    </div>
                                    <div class="trendChartPrice" th:if="${#strings.toString(villageDetail.unitPrice) eq '0.0'}"><b th:text="${'&#45;&#45;&#45;&#45;'}"></b></div>
                                </dt>
                                <dd>
                                    <div class="trendChartTitle" style="overflow: hidden;"><i style="background: #8cc9d5;float: left;margin-top: 7px;"></i><div id ="region_name" style="float: left">铁西</div> </div>
                                    <div class="trendChartPrice"><b>￥8652元/㎡ </b><span>比本小区环比低0.38%</span></div>
                                </dd>
                                <dd>
                                    <div class="trendChartTitle" style="overflow: hidden;"><i style="float: left;margin-top: 7px;"></i><div id = "city_name" style="float: left">沈阳</div> </div>
                                    <div class="trendChartPrice"><b>￥8652元/㎡ </b><span>比本小区环比高0.38%</span></div>
                                </dd>
                            </dl>
                            <div class="vs">vs</div>
                        </div>
                        <ul class="trendCharTime">
                            &lt;!&ndash;<li class="hover">6个月</li>&ndash;&gt;
                            &lt;!&ndash;<li >1年</li>&ndash;&gt;
                            &lt;!&ndash;<li>3年</li>&ndash;&gt;
                        </ul>
                        <div class="cl"></div>
                        <ul class="trendCharMap">
                            <li>
                                <div id="container1"></div>
                            </li>
                            &lt;!&ndash;<li>&ndash;&gt;
                            &lt;!&ndash;<div id="container2"></div>&ndash;&gt;
                            &lt;!&ndash;</li>&ndash;&gt;
                            &lt;!&ndash;<li style="display: none;">&ndash;&gt;
                            &lt;!&ndash;<div id="container3"></div>&ndash;&gt;
                            &lt;!&ndash;</li>&ndash;&gt;
                        </ul>
                        <div class="infuse">注：本站“均价”数据统计来自经纪人发布信息</div>
                    </div>-->
                    <script th:inline="javascript">
                        var highChartsData = [[${data}]];
                        var msg1 = JSON.parse(highChartsData[0].highChartsData);
                        var msg2 = JSON.parse(highChartsData[1].highChartsData);
                        var msg3 = JSON.parse(highChartsData[2].highChartsData);//小区数据
                        $("#community_Name").text(highChartsData[2].keyName);
                        /*  if(msg3.status == 0 || msg3.length == 0){
                              $(".trendCharContent dl dt div b").text("暂无资料");
                          }else{
                              $(".trendCharContent dl dt div b").text("￥ "+msg3[0].unitPrice +"元/m²");
                          }*/
                        $("#region_name").text(highChartsData[1].keyName);
                        $(".trendCharContent dl dd:eq(0) div b").text("￥ "+msg2[0].unitPrice +"元/m²");
                        $("#city_name").text(highChartsData[0].keyName);
                        $(".trendCharContent dl dd:eq(1) div b").text("￥ "+msg1[0].unitPrice +"元/m²");
                        /*  if(msg3.length == 0 || msg3.status == 0 ){
                              $(".trendCharContent dl dt div span").text("----");
                          } else if(msg3.length > 0  && msg3[0].unitPrice - msg3[1].unitPrice == 0) {
                              $(".trendCharContent dl dt div span").text("----");
                          }else if (msg3[0].unitPrice - msg3[1].unitPrice > 0) {
                                  $(".trendCharContent dl dt div span").text("比本小区环比高" + ((msg3[0].unitPrice - msg3[1].unitPrice) / msg3[1].unitPrice * 100).toFixed(2) + "%");
                              } else {
                                  $(".trendCharContent dl dt div span").text("比本小区环比低" + (-(msg3[0].unitPrice - msg3[1].unitPrice) / msg3[1].unitPrice * 100).toFixed(2) + "%");
                                  $(".trendCharContent dl dt div span").addClass("dis");
                          }*/

                        if (msg2[0].unitPrice - msg2[1].unitPrice > 0) {
                            $(".trendCharContent dl dd:eq(0) div span").text("环比上周高" + ((msg2[0].unitPrice - msg2[1].unitPrice) / msg2[1].unitPrice * 100).toFixed(2) + "%");
                        } else {
                            $(".trendCharContent dl dd:eq(0) div span").text("环比上周低" + (-(msg2[0].unitPrice - msg2[1].unitPrice) / msg2[1].unitPrice * 100).toFixed(2) + "%");
                            $(".trendCharContent dl dd:eq(0) div span").addClass("dis");
                        }
                        if (msg1[0].unitPrice - msg1[1].unitPrice > 0) {
                            $(".trendCharContent dl dd:eq(1) div span").text("环比上周高" + ((msg1[0].unitPrice - msg1[1].unitPrice) / msg1[1].unitPrice * 100).toFixed(2) + "%");
                        } else {
                            $(".trendCharContent dl dd:eq(1) div span").text("环比上周低" + (-(msg1[0].unitPrice - msg1[1].unitPrice) / msg1[1].unitPrice * 100).toFixed(2) + "%");
                            $(".trendCharContent dl dd:eq(1) div span").addClass("dis");
                        }
                        /*  var length = msg1.length <= msg3.length ? msg1.length : msg3.length;
                         var categories = new Array();
                         var data1 = new Array();
                         var data2 = new Array();
                         var data3 = new Array();
                         var temp = '';
                         for (i = 0; i < length; i++) {
                         if (i % 4 == 0) {
                         var inArray = Highcharts.dateFormat('%y年%m月', msg1[i].trendingTime);
                         if(temp != inArray) {
                         temp = inArray;
                         categories.push(inArray);
                         data1.push(msg1[i].unitPrice)
                         data2.push(msg2[i].unitPrice)
                         data3.push(msg3[i].unitPrice)
                         }
                         }
                         }*/
                        var length = 0;
                        if(msg3.content != null ){
                            length = msg1.length <= msg3.length ? msg1.length : msg3.length;
                        }else if(msg3.length < msg1.length){
                            length = msg3.length;
                        }else{
                            length = msg1.length;
                        }
                        var length2 = msg2.length ;
                        var length1 = msg1.length ;
                        var categories = new Array();
                        var data1 = new Array();
                        var data2 = new Array();
                        var data3 = new Array();

                        var temp = '';
                        if(msg1.length != 0 && msg2.length != 0 && msg3.length != 0){
                            for (i = 0; i < length; i++) {
//                                if (i % 4 == 0) {
                                var inArray = Highcharts.dateFormat('%y年%m月', msg1[i].trendingTime);
                                if(temp != inArray) {
                                    temp = inArray;
                                    categories.push(inArray);
                                    if(msg3.length > 0  && i < msg3.length){
                                        data3.push(msg3[i].unitPrice)
                                    }
                                    data1.push(msg1[i].unitPrice)
                                    data2.push(msg2[i].unitPrice)
                                }
//                                }
                            }
                        }else if(msg1.length == 0 || msg2.length == 0 || msg3.length == 0){
                            for (i = 0; i <length1; i++) {
                                var inArray = Highcharts.dateFormat('%y年%m月', msg1[i].trendingTime);
                                if(temp != inArray){
                                    temp = inArray;
                                    categories.push(inArray);
                                    data1.push(msg1[i].unitPrice);
                                }
                            }
                            for (i = 0; i <length2; i++) {
                                var inArray = Highcharts.dateFormat('%y年%m月', msg2[i].trendingTime);
                                if(temp != inArray){
                                    temp = inArray;
                                    categories.push(inArray);
                                    data2.push(msg2[i].unitPrice);
                                }
                            }
                            for (i = 0; i <length; i++) {
                                var inArray = Highcharts.dateFormat('%y年%m月', msg3[i].trendingTime);
                                if(temp != inArray){
                                    temp = inArray;
                                    categories.push(inArray);
                                    if(msg3.length > 0){
                                        data3.push(msg3[i].unitPrice);
                                    }
                                }
                            }
                        }
                        var series = [{
                            name: highChartsData[0].keyName,
                            data: data1.reverse(),
                        }, {
                            name: highChartsData[1].keyName,
                            data: data2.reverse(),
                        }, {
                            name: highChartsData[2].keyName,
                            data: data3.reverse(),
                        }]
                        //            var categories=['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
                        categories = categories.reverse();
                        highchart('container1', series, categories)
                        //            highchart('container2',series,categories)
                        //            highchart('container3',series,categories)
                        //                    debugger;

                        function highchart(id, series, categories) {
                            Highcharts.chart(id, {
                                title: {
                                    text: false
                                },
                                yAxis: {
                                    title: {
                                        text: false
                                    },
                                    labels: {
                                        formatter: function () {
                                            return this.value + '元/m²';
                                        }
                                    }
                                },
                                xAxis: {
                                    categories: categories
                                },
                                legend: {
                                    enabled: false
                                },
                                tooltip: {
                                    crosshairs: true,
                                    shared: true,
                                    pointFormat: '<tspan style="fill:{series.color}" x="8" dy="15">●</tspan> <b>{series.name}</b> {point.y} 元/m²<br>'
                                },
                                plotOptions: {
                                    spline: {
                                        marker: {
                                            radius: 4,
                                            lineColor: '#666666',
                                            lineWidth: 1
                                        }
                                    }
                                },
                                series: series,
                                responsive: {
                                    rules: [{
                                        condition: {
                                            maxWidth: 500
                                        },
                                        chartOptions: {
                                            legend: {
                                                layout: 'horizontal',
                                                align: 'center',
                                                verticalAlign: 'bottom'
                                            }
                                        }
                                    }]
                                },
                                colors: ['#fe7175', '#8cc9d5', '#75d88f']
                            });
                        }
                    </script>
                </th:block>
            </div>
        </div>
        <!--<div class="right" style="overflow: unset;">-->
            <!--<div class="head sddt">-->
                <!--<h1><i class="shugang"></i>为您推荐其他经纪人</h1>-->
                <!--<ul></ul>-->
                <!--<script th:inline="javascript">-->
                    <!--$.ajax({-->
                        <!--type: "POST",-->
                        <!--data:{ houseId:$("#Idhouse").val()},-->
                        <!--url:  "/viewRecommandYiAgent",-->
                        <!--dataType : 'json',-->
                        <!--success: function (data) {-->
                            <!--if(data.status==1){-->
                                <!--res=data.content-->
                                <!--if(res.length>0){-->
                                    <!--$(".sddt").show()-->
                                    <!--for(var i=0;i<res.length;i++){-->
                                        <!--var st=`<li style="overflow: unset;">-->
                                                <!--<div class="sddt_i">-->
                                                    <!--<img src="`+(res[i].memberPic!=''?res[i].memberPic:'https://static.fangxiaoer.com/web/images/sy/sale/s_18.png')+`"/>-->
                                                <!--</div>-->
                                                <!--<div class="sddt_r">-->
                                                    <!--<div class="sdr1">-->
                                                        <!--<span>`+res[i].memberName+`</span>-->
                                                        <!--<span>经纪人</span>-->
                                                    <!--</div>-->
                                                    <!--<div class="sdr2">-->
                                                        <!--<span class="sdrsp" d="`+res[i].memberId+`">在线聊呗</span>-->
                                                        <!--<span style="position: relative;" ph="`+res[i].tel+`">-->
                                                            <!--电话咨询-->
                                                            <!--<div class="show-CardTels" style="left: 34px; bottom: 20px;">-->
                                                                <!--<img class="second1 jk`+i+`"/>-->
                                                                <!--<p>微信扫码拨号</p>-->
                                                                <!--<p><i>快速获取经纪人联系方式</i></p>-->
                                                            <!--</div>-->
                                                        <!--</span>-->
                                                    <!--</div>-->
                                                    <!--<div class="sdr3">熟悉本房源</div>-->
                                                <!--</div>-->
                                            <!--</li>`-->
                                        <!--$(".sddt ul").append(st)-->


                                        <!--$.ajax({-->
                                            <!--type: "GET",-->
                                            <!--async: false,-->
                                            <!--url:  "/getWxSecCode",-->
                                            <!--data:{"scene": 'yitel,' + res[i].tel},-->
                                            <!--dataType : 'json',-->
                                            <!--headers : {-->
                                                <!--'Content-Type' : 'application/json;charset=utf-8'-->
                                            <!--},-->
                                            <!--success: function (t) {-->
                                                <!--$('.jk'+i).attr("src","data:text/html;base64,"+t.img);-->
                                            <!--}-->
                                        <!--});-->
                                    <!--}-->
                                <!--}-->
                            <!--}else{-->
                                <!--console.log(data.msg)-->
                            <!--}-->
                        <!--}-->
                    <!--});-->
                    <!--//点击在线聊呗-->
                    <!--$(document).on('click','.sdr2 .sdrsp',function(){-->
                        <!--if($("#sessionId").val()=='' || $("#sessionId").val()==null){-->
                            <!--$("#login").show()-->
                            <!--$('.reportPopupHb').show()-->
                        <!--}else{-->
                            <!--window.open("/im/chat/"+$(this).attr('d'))-->
                        <!--}-->
                    <!--})-->
                    <!--//电话咨询-->
                    <!--$(document).on('mouseover','.sdr2 span',function(){-->
                        <!--$(this).find('.show-CardTels').show()-->
                    <!--})-->
                    <!--$(document).on('mouseout','.sdr2 span',function(){-->
                        <!--$(this).find('.show-CardTels').hide()-->
                    <!--})-->
                    <!--//电话二维码-->
                    <!--function hcode(houseId,tels,dom){-->
                        <!--var scene1 = 'tel,' +houseId+',2,' + tels;-->
                        <!--var img1 = "";-->
                        <!--$.ajax({-->
                            <!--type: "GET",-->
                            <!--async: false,-->
                            <!--url:  "/getWxSecCode",-->
                            <!--data:{"scene": scene1},-->
                            <!--dataType : 'json',-->
                            <!--headers : {-->
                                <!--'Content-Type' : 'application/json;charset=utf-8'-->
                            <!--},-->
                            <!--success: function (data) {-->
                                <!--img1 = data.img;-->
                            <!--}-->
                        <!--});-->
                        <!--dom.attr("src","data:text/html;base64,"+img1);-->
                    <!--}-->

                <!--</script>-->
            <!--</div>-->
            <!--<div id="xiangsihouse" class="head recommend" th:if="${similar ne null and #lists.toList(similar).size() ne 0}">-->
                <!--<h1><i class="shugang"></i>同小区您可能感兴趣的房源</h1>-->
                <!--<ul >-->
                    <!--<li th:each="s:${similar}">-->
                        <!--<a  th:href="${s.houseId + '.htm'}" target="_blank">-->
                            <!--<div>-->
                                <!--<img th:src="${#strings.isEmpty(s.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.pic}" th:alt="${s.subName}" />-->
                                <!--<b th:text="${s.subName}"></b>-->
                                <!--<p th:text="${s.room + '室' + s.hall + '厅'}"></p>-->
                                <!--<s th:if= "${!#strings.isEmpty(s.price) and #strings.toString(s.price) ne '0.0'}"><span  th:text="${#strings.toString(s.price).contains('.')? #strings.toString(s.price).replaceAll('0+?$','').replaceAll('[.]$', '') : s.price}"></span><i>万</i></s>-->
                                <!--<span th:if= "${#strings.isEmpty(s.price) || #strings.toString(s.price) eq '0.0'}" th:text="${'面议'}"></span>-->
                            <!--</div>-->
                        <!--</a>-->
                    <!--</li>-->
                    <!--&lt;!&ndash;<a th:href="${'/saleHouses/search='+sehouse.subName+'-r'+sehouse.regionId}" target="_blank">查看更多房源 ></a>&ndash;&gt;-->
                    <!--<a th:href="${'/saleHouses/-v'+sehouse.subId+'-r'+sehouse.regionId}" target="_blank">查看更多房源 ></a>-->
                <!--</ul>-->
            <!--</div>-->

            <!--<div id="xftuijian" class="head recommend" th:if="${!#lists.isEmpty(lottery)}">-->
                <!--<h1><i class="shugang"></i>新房推荐</h1>-->
                <!--<ul >-->
                    <!--<li th:each="l:${lottery}">-->
                        <!--<a  th:href="${'/house/'+l.projectId+'-'+l.type+ '.htm'}" target="_blank">-->
                            <!--<div>-->
                                <!--<img th:src="${#strings.isEmpty(l.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':l.pic}" th:alt="${l.projectName}" />-->
                                <!--<b th:text="${l.projectName}"></b>-->
                                <!--<p th:text="${l.regionName}"></p>-->
                                <!--<span th:if= "${!#strings.isEmpty(l.mPrice) and !#strings.isEmpty(l.mPrice.priceMoney) and #strings.toString(l.mPrice.priceMoney) ne '0.0'}" th:text="${#strings.toString(l.mPrice.priceMoney).contains('.')? #strings.toString(l.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$', '') : l.mPrice.priceMoney}"></span>-->
                                <!--<i th:text="${#strings.isEmpty(l?.mPrice?.priceMoney) ? '待定' : '' }"></i>-->
                                <!--<i th:text="${!#strings.isEmpty(l?.mPrice?.priceMoney)  and #strings.toString(l.mPrice.priceMoney) ne '0.0' ? '元/㎡' : ''}"></i>-->
                            <!--</div>-->

                        <!--</a>-->
                    <!--</li>-->
                <!--</ul>-->
            <!--</div>-->

        <!--</div>-->

        <div class="cl"></div>
        <div class="">
<!--            <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
            <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
            <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
            <div class="cl"></div>
            <div th:include="secondhouse/fragment::MapZhouBianPeiTao1_mapDom"></div>
            <div class="cl"></div>
            <script th:inline="javascript">
                $(document).ready(function () {
                    var id = [[${sehouse.subId}]];
                    var lng = [[${sehouse.longitude}]];
                    var lat = [[${sehouse.latitude}]];
                    var title = [[${sehouse.subName}]];
                    var address = [[${sehouse.address}]];
                    var city = '沈阳';
                    var content = "";
                    var houseOwner = [[${sehouse.houseOwner}]];
                    var sortTel = [[${sehouse.sortTel}]];
                    var keeperTel2 = [[${sehouse.keeperTel2}]];
                    var keeperName2 = [[${sehouse.keeperName2}]];
                    houseOwner += "  "
                    if (sortTel != ""){
                        houseOwner += sortTel;
                    }else {
                        houseOwner += keeperTel2
                    }
                    $("#houseOwner").text(houseOwner)
                    $("#houseDetailOwner").text(keeperName2)
                    $("#houseOwner").css("white-space","pre")
                    $("#mapsubTitle").val(title);
                    bdMap.init("memap", {
                        id: id,
                        houselng: lng,
                        houselat: lat,
                        radius: 2000,
                        suofa: 14,
                        bdtitle: title,
                        bdcontent: content,
                        address: address,
                        city: city
                    });
                });
            </script>

            <!-- 房贷计算器-->
            <div class="title ttt" th:if="${sehouse.price ne '0'}"><p>房贷计算器</p><i id="mortgageBtn"></i></div>
            <div th:fragment="calculator">
                <link href="https://static.fangxiaoer.com/js/jsq/house/index.css" rel="stylesheet"  type="text/css" />
                <link href="https://static.fangxiaoer.com/js/jsq/house/framework.css" rel="stylesheet" />
                <script type="text/javascript" src="/js/fangdai2.js"></script>
                <!--<div class="title ttt"><p>参考月供</p></div>-->
                <div id="js201" class="yinchang">
                    <div class="hj-col-lg-22" th:if="${sehouse.price ne '0'}">
                        <div class="hj-row">
                            <div class="jsq_l" style="position: relative;">
                                <a href="/static/business.htm" target="_blank"><div class="smore">查看更多贷款方式<i>></i></div></a>
                                <div class="month-pay" js="month-pay">
                                    <h4 style="padding-bottom: 40px;">计算条件</h4>
                                    <div class="mp-form-group">
                                        <div class="mp-label">
                                            估算总价
                                        </div>
                                        <div class="mp-form-control">
                                            <input maxlength="5" class="mp-input mp-input-md" style="width: 110px;height: 47px;padding: 0 10px;border-radius: 0;" id="totalHousePrice" type="text" total-price="101"  placeholder="请输入预算" />
                                            <span class="mp-unit">万元</span>
                                        </div>
                                    </div>
                                    <div class="mp-form-group">
                                        <div class="mp-label">
                                            按揭成数
                                        </div>
                                        <div class="mp-form-control">
                                            <div class="mp-select-mask">
                                                7成
                                            </div>
                                            <ul tabindex="5001" class="optgroup" id="percent-dropdown" style="overflow-y: auto;">
                                                <li data-code="0.1">1成</li>
                                                <li data-code="0.2">2成</li>
                                                <li data-code="0.3">3成</li>
                                                <li data-code="0.4">4成</li>
                                                <li data-code="0.5">5成</li>
                                                <li data-code="0.6">6成</li>
                                                <li data-code="0.7">7成</li>
                                                <li data-code="0.8">8成</li>
                                                <li data-code="0.9">9成</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="mp-form-group">
                                        <div class="mp-label">
                                            贷款类别
                                        </div>
                                        <div class="mp-form-control">
                                            <div class="mp-select-mask">
                                                商业贷款
                                            </div>
                                            <ul class="optgroup" id="daikuan_type_list">
                                                <li data-code="1">商业贷款</li>
                                                <li data-code="2">公积金贷款</li>
                                                <li data-code="3">组合贷款</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="mp-form-group" id="group-dk-area" style="display: none;">
                                        <div class="mp-label">
                                            贷款金额
                                        </div>
                                        <div class="mp-form-control">
                                            <span class="mp-loan-total">(贷款总额<i id="group-dk-total"></i>万)</span>
                                            <div class="sec-input">
                                                <span class="type-name">公积金</span>
                                                <input class="mp-input mp-input-md" id="total-price-gjj" type="text" />
                                                <span class="mp-unit">万元</span>
                                            </div>
                                            <div class="sec-input">
                                                <span class="type-name">商贷</span>
                                                <input class="mp-input mp-input-md" id="total-price-sy" type="text" />
                                                <span class="mp-unit">万元</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mp-form-group">
                                        <div class="mp-label">
                                            贷款时间
                                        </div>
                                        <div class="mp-form-control">
                                            <div class="mp-select-mask" id="shijian">
                                                30年(360个月)
                                            </div>
                                            <ul tabindex="5002" class="optgroup" id="time-dropdown" style="overflow-y: auto;">
                                                <li data-code="1">1年(12个月)</li>
                                                <li data-code="2">2年(24个月)</li>
                                                <li data-code="3">3年(36个月)</li>
                                                <li data-code="4">4年(48个月)</li>
                                                <li data-code="5">5年(60个月)</li>
                                                <li data-code="6">6年(72个月)</li>
                                                <li data-code="7">7年(84个月)</li>
                                                <li data-code="8">8年(96个月)</li>
                                                <li data-code="9">9年(108个月)</li>
                                                <li data-code="10">10年(120个月)</li>
                                                <li data-code="11">11年(132个月)</li>
                                                <li data-code="12">12年(144个月)</li>
                                                <li data-code="13">13年(156个月)</li>
                                                <li data-code="14">14年(168个月)</li>
                                                <li data-code="15">15年(180个月)</li>
                                                <li data-code="16">16年(192个月)</li>
                                                <li data-code="17">17年(204个月)</li>
                                                <li data-code="18">18年(216个月)</li>
                                                <li data-code="19">19年(228个月)</li>
                                                <li data-code="20">20年(240个月)</li>
                                                <li data-code="21">21年(252个月)</li>
                                                <li data-code="22">22年(264个月)</li>
                                                <li data-code="23">23年(276个月)</li>
                                                <li data-code="24">24年(288个月)</li>
                                                <li data-code="25">25年(300个月)</li>
                                                <li data-code="26">26年(312个月)</li>
                                                <li data-code="27">27年(324个月)</li>
                                                <li data-code="28">28年(336个月)</li>
                                                <li data-code="29">29年(348个月)</li>
                                                <li data-code="30">30年(360个月)</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="mp-form-group" style="height: 8px;">
                                        <div class="mp-label" style="height: 1px;">
                                        </div>
                                        <div class="mp-form-control">
                                            <input id="currentChooseArea" type="hidden" value="1" />
                                            <input id="currentBuildingPirce" type="hidden" value='6300' />
                                            <input id="daikuan_type" type="hidden" value="1" />
                                            <input id="daikuan_total_price" type="hidden" value="1" />
                                            <input id="years" type="hidden" value="20" />
                                            <input id="lilv" type="hidden" value="1.9" />
                                            <input id="anjie" type="hidden" value="0.7" />
                                            <div class="jsq_bg">
                                                <img src="https://static.fangxiaoer.com/js/jsq/house//jsq_bg.gif" alt="计算器">
                                            </div>
                                            <button class="hj-btn hj-btn-md hj-btn-red" id="mortgageCalculation" type="button">
                                                开始计算</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="hj-col-lg-9 referesult">
                                <!--                                <h4 class="referesult-title"><a href="/static/business.htm" target="_blank">查看更多贷款方式 ></a>参考结果</h4>-->

                                <div id="pieChart">
                                </div>
                                <div id="priceLineChart" style="height: 16px;">
                                </div>
                                <span class="total-price" style="left: 206.5px; top: 270.5px;">总价 <strong><i id="ht-total-price"></i><span>万</span> </strong></span>
                                <ul>
                                    <li style="margin-bottom: 10px;"><span class="fieldt">月均还款</span><i id="average_month_pay"></i></li>
                                    <li><span class="square orange-square-std"></span><span class="field">参考首付</span><span
                                            id="first_pay"></span></li>
                                    <li><span class="square aqua-square"></span><span class="field">贷款金额</span><span
                                            id="daikuan_total_price2"></span></li>
                                    <li><span class="square purple-square"></span><span class="field">支付利息</span><span
                                            id="pay_lixi"></span></li>
                                    <li id="whichrate">(利率 商贷:4.9%)</li>
                                </ul>
                                <div class="result-tips">
                                    备注：以等额本息计算结果，数据仅供参考
                                </div>
                            </div>
                        </div>
                    </div>
                    <script src="https://static.fangxiaoer.com/js/jsq/house/index.js"></script>
                    <script src="https://static.fangxiaoer.com/js/jsq/house/echarts-all.js"></script>
                    <script src="/js/fangdai2.js"></script>


                    <script>
                        //计算器 默认 类型
                        $("#htype-dropdown").attr("data-glzs", $("#htype-dropdown li").eq(0).attr("data-glzs"));
                        $("#htype-dropdown li").each(function () {
                            $(this).append($(this).attr("data-glzs"));
                            var flzs = $(this).attr("data-glzs");
                            var thisLi = $(this)
                            $(".price li").each(function () {
                                //动态获取正则表达式
                                var reg1 = thisLi.attr("data-glzs");
                                var reg = $(this).html().split("<i>")[0] == "底商" ? "商铺" : $(this).html().split("<i>")[0];//存在 给出'底商'价格房源类型却是'商铺' 的问题   手动改'底商'为'商铺'
                                if (reg1 == reg) {
                                    var price = $(this).find("i").text();
                                    thisLi.attr("data-glzs", price)//修改为对应的价格
                                }
                            })
                        })
                        //如果没有价格默认为第一个的价格
                        $("#htype-dropdown li").each(function(){
                            console.log(isNaN(parseInt($(this).attr("data-glzs"))))
                            if(isNaN(parseInt($(this).attr("data-glzs")))){
                                $(this).attr("data-glzs", $(".price li i").eq(0).text());
                            }
                        })

                    </script>
                    <!--<![endif]-->
                </div>
                <!--计算器结束-->
            </div>
        </div>
    </div>
    <!--免责声明-->
    <div class="cl"></div>
    <div class="disclaimer">
        <strong>免责声明：</strong>房源信息由网站用户提供，其真实性、合法性由信息提供者负责，最终以政府部门登记备案为准。本网站不声明或保证内容之正确性和可靠性，购买该房屋时，请谨慎核查。如该房源信息有误，您可以投诉此房源信息或<strong>拔打举报电话：************</strong>。
    </div>
    <!--</th:block>-->
    <!--<th:block th:if="${#strings.toString(sehouse.isDel) eq '1'}">
        <div class="delList">
            <div class="delListMsg">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/deleteListingsIcon.png" alt="">
                <div>
                    <h4>抱歉，您查看的信息已过期!</h4>
                    <p>建议您继续访问其他页面<a href="/saleHouses">查看更多二手房房源 ></a></p>
                </div>
            </div>
            <div class="delListContList" th:if="${!#lists.isEmpty(similar) and similar.size() ge 4}">
                <h4>同小区您可能感兴趣的房源</h4>
                <ul>
                    <li th:each="s,i:${similar}" th:if="${i.index lt 5}">
                        <a  th:href="${s.houseId + '.htm'}" target="_blank">
                            <img th:src="${#strings.isEmpty(s.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.pic}" th:alt="${s.subName}" />
                            <p th:text="${s.subName}">恒大御峰</p>
                            <div>
                                <p th:if= "${!#strings.isEmpty(s.price) and #strings.toString(s.price) ne '0.0'}">
                                    <span th:text="${#strings.toString(s.price).contains('.')? #strings.toString(s.price).replaceAll('0+?$','').replaceAll('[.]$', '') : s.price}">666</span>万
                                </p>
                                <p th:if= "${#strings.isEmpty(s.price) or #strings.toString(s.price) eq '0.0'}"><span>面议</span></p>
                                <span th:text="${#strings.toString(s.area).contains('.')? #strings.toString(s.area).replaceAll('0+?$','').replaceAll('[.]$', '')+'㎡' : s.area+'㎡'}">92m²</span>
                                <span th:text="${s.room + '室' + s.hall + '厅'+s.toilet+'卫'}">2室2厅1卫</span>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="delListContList" th:if="${!#lists.isEmpty(interestHouse) and interestHouse.size() ge 4}">
                <h4>您可能感兴趣的房源</h4>
                <ul>
                    <li th:each="second,i:${interestHouse}" th:if="${ #strings.toString(second.houseId) != #strings.toString(houseId) and i.index lt 5}">
                        <a th:href="${second.houseId + '.htm'}" target="_blank">
                            <img th:src="${#strings.isEmpty(second.pic) ? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : second.pic}" th:alt="${second.subName}">
                            <p th:text="${second.subName}">恒大御峰</p>
                            <div>
                                <p th:if="${!#strings.isEmpty(second.price) and #strings.toString(second.price) ne '0.0'}">
                                    <span th:text="${#strings.toString(second.price).contains('.')? #strings.toString(second.price).replaceAll('0+?$','').replaceAll('[.]$', '') : second.price}"></span>万
                                </p>
                                <p th:if="${#strings.isEmpty(second.price) or (#strings.toString(second.price) eq '0.0')}"><span>面议</span></p>
                                <span th:text="${#strings.toString(second.area).contains('.')? #strings.toString(second.area).replaceAll('0+?$','').replaceAll('[.]$', '')+'㎡' : second.area+'㎡'}">92m²</span>
                                <span th:text="${second.room+'室'+second.hall+'厅'+second.toilet+'卫'}">2室2厅1卫</span>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </th:block >-->
    <div class="cl"></div>
    <div th:include="fragment/fragment:: footer_detail" ></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
    <noscript><img src="https://d5nxst8fruw4z.cloudfront.net/atrk.gif?account=YWBQn1QolK10fn" style="display:none" height="1" width="1" alt="" /></noscript>
    <!-- End Alexa Certify Javascript -->
    <!--佣金-->
    <div class="saleHouseIndent" style="display: none">
        <div class="page1">
            <s class="
            x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
            <h1>在线支付享优惠</h1>
            <ul >
                <li><span>购房人</span><input id="name" placeholder="请填写姓名" type="text" maxlength="20" /></li>
                <li><span>手机号码</span><input type="text" id="phone" placeholder="请填写手机号码" maxlength="11" value="" /></li>
                <li class="yzm" style="border-bottom: 1px solid #ccc;"><span>请输入</span><input autocomplete="new-password" type="password" id="code" placeholder="验证码/密码" maxlength="20" style="width: 129px;" /><b class="fxe_ReSendValidateCoad">获取验证码</b><b class="fxe_validateCode" style="display: none;"></b></li>
                <!--
                                <li class="mm" style="display: none; border-bottom: 1px solid #ccc;"><span>密码</span><input type="password" id="pwd" placeholder="请填写密码" maxlength="20" /></li>
                -->
                <input type="hidden" id="userType" value="">
            </ul>
            <a>佣金95折</a>
            <dl>
                <dt></dt>
                <dd>我已经看过并同意<a href="https://info.fangxiaoer.com/Help/remission" target="_blank">《佣金减免说明书》</a></dd>
            </dl>
            <!--<span class="x"></span>-->
        </div>
        <div class="page2">
            <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
            <h1>在线支付</h1>
            <p th:text="${sehouse.title}">
            </p>
            <ul>
                <li><span>在线支付20元，约折佣340元</span></li>
                <li th:text="${'地址：'+sehouse.address}"></li>
                <li th:text="${'经纪人：'+ (sehouse.keeperName2 ne null and sehouse.keeperName2 ne ''? sehouse.keeperName2:sehouse.houseOwner) + ' '+ (sehouse.sortTel == null or #strings.toString(sehouse.sortTel) eq ''? sehouse.keeperTel2:sehouse.sortTel)}"></li>
            </ul>
            <dl>
                <dt>支付方式： </dt>
                <dd class="weixin hover">
                    <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/weixin.png" alt="微信" /><i></i></dd>
                <dd class="alipay">
                    <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/alipay.png" alt="支付宝" /><i></i></dd>
            </dl>
            <span>支付金额：20元</span>
            <a>去支付</a>
            <!--<span class="x"></span>-->
        </div>
        <div class="page3">
            <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
            <h1>请使用<span>支付宝</span>扫描下面二维码</h1>
            <img src="" alt="二维码获取失败" />
            <!--<span class="x1"></span>-->
        </div>
        <div class="page4">
            <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
            <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/success.png" />
            <b>支付成功</b>
            <a href="https://my.fangxiaoer.com/payOrder">查看订单</a>
            <!--<span class="x1"></span>-->
        </div>
    </div>

    <th:block th:if="${#strings.toString(sehouse.isDel) ne '1'}">
        <!--页面滚动时出现的头部定位-->
        <div class="bkNavFloat fixbkb">
            <div class="container">
                <div class="unstyled" style="width: 1170px;margin: 0 auto;height: 26px;">
                    <h4 th:text="${sehouse.title}">城建花园最好户型，价格便宜 4室2厅2卫167㎡</h4>
                    <p th:text="${sehouse.layout+'，'+sehouse.area+'m²，'+(sehouse.price ne '0' ? sehouse.price+'万' : '面议')}">2室2厅1卫，167㎡，90万</p>
                    <!--微信扫码拨号-->
                    <div class="fix-cardTels" th:if="${sehouse.cooperationState eq null}">
                        <s></s>
                        <span>电话咨询</span>
                        <div class="show-topCardTels">
                            <img id="second2" src="" alt="">
                            <p>微信扫码拨号</p>
                            <p th:if="${sehouse.keeperLevel2 ne '1'}"><i>快速获取经纪人联系方式</i></p>
                            <p th:unless="${sehouse.keeperLevel2 ne '1'}"><i>快速获取房主联系方式</i></p>
                        </div>
                    </div>
                    <!--<div>
                        <i></i>
    &lt;!&ndash;                    <p th:text="${sehouse.houseOwner+'  '+((sehouse.sortTel ne '' and sehouse.sortTel ne null) ? sehouse.sortTel : sehouse.keeperTel2)}"></p>&ndash;&gt;
                        <p id="houseOwner"></p>
                    </div>-->
                </div>
            </div>
            <!--页面向下滚动时 头部出现定位条-->
        </div>
    </th:block>
    <!--<script src="static/2015/0321/js/verify.js" type="text/javascript"></script>-->

    <!--头部轮播图视频播放-->
    </div>
    <div th:include="house/detail/fragment_login::login"></div>
</form>
<!--<div th:include="fragment/fragment::esfCommonFloat"></div>-->


<!--风险提醒-->
<div class="riskm">
    <div class="rclose"></div>
    <div class="rkm">
        <h2>风险提醒：</h2>

        <p>本站旨在为广大用户提供更丰富的信息，部分信息由第三方提供，我们持续通过管理手段提升信息的准确度，但<strong>我们无法确保信息的准确性和完整性。房产交易滋事体大，本站信息不应作为您买卖决策的依据，您决策前应与房源业主核实相关信息、并亲自到房屋中核验信息，或以产权证明、政府类网站发布的官方信息为准。本站不对您交易过程中对本网站信息产生的依赖承担任何明示或默示的担保责任或任何责任。</strong></p>

        <h3>请您详细阅读如下声明：</h3>

        <h3>1、关于参考户型图</h3>
        <p>本网呈现的户型图为平台根据第三方提供的内容/数据而非标准的参考户型图，其中户型结构及房屋面积并非按国家标准进行的测绘专业活动取得，户型图与真实现状一定存在差异，我们无法保障户型图准确性和差异率，<strong>户型图仅供参考，不应作为您交易的决策依据，房屋面积的准确信息请您与房源业主核实，并请以产权证明或您委托的专业机构测量结果为准。</strong></p>

        <h3>2、关于房屋装修情况</h3>
        <p>本网房源图片、VR效果图、视频等呈现出的房屋装修情况可能因为拍摄时间、拍摄角度等原因和实际场景存在出入,仅供参考，不应作为您交易的决策依据，请以您在看房时房源的实际装修情况为准。</p>

        <h3>3、关于房屋情况</h3>
        <p>本网展示的房源信息（包括但不限于房屋面积、所在楼层、房屋朝向、房屋用途、建成年代、建筑结构、供暖方式、抵押信息、交易权属）由经纪人/个人提供，<strong>仅供参考不应作为您交易的决策依据，房源的准确信息请您与房源业主核实，并以房本信息、房屋实际情况、您签订房屋买卖合同中披露的信息为准。</strong></p>

        <h3>4、关于房屋周边配套等</h3>
        <p>房源介绍中的周边配套、在建设施、规划设施、地铁信息、绿化率、得房率等内容均系第三方提供，<strong>仅供参考不应作为您交易的决策依据，房屋周边配套请您与房源业主及主管部门核实，并以房本信息、房屋实际情况、您签订房屋买卖合同中披露的信息为准。</strong></p>

        <h3>5、关于距离</h3>
        <p>房源介绍中与距离相关的数据均来源于百度地图。</p>
    </div>
</div>


<script>
    var monthLpr = parseInt($('#shijian').text()) * 12;

    getLPRLoanRate()//获取利率
    $("#loan_time_a").find('a').click(function(){
        setTimeout(function (){
            monthLpr = parseInt($('#shijian').text()) * 12;
            getLPRLoanRate()
        },500)
    })
    function getLPRLoanRate(){
        $.ajax({
            type: "POST",
            url: "/viewLoanFilter",
            data: {'month': monthLpr,'discount':1},
            success: function (data) {
                console.log(data)
                $("#whichrate").text('（利率: 公积金'+data.content.rate2+'%、商贷'+data.content.rate1+'%）')
                $("#whichrate").attr({'rate-sy':data.content.rate1,'rate-gjj':data.content.rate2})
            }
        })
    }
</script>

<script th:inline="javascript">
    var result = [[${sehouse}]];
    //console.log('打印结果',result)
    var agentState = [[${sehouse.agentState}]];
    //console.log("@@@@@@@@@@@",agentState);
</script>



<script th:inline="javascript">
    function showLicense(e) {
        $(".license").show();
    }
    function hideLicense(e) {
        $(".license").hide();
    }
    /*<![CDATA[*/
    var sortTel = [[${sehouse.sortTel}]];
    var keeperTel2 = [[${sehouse.keeperTel2}]];
    var isDel = [[${sehouse.isDel}]];
    var saleState = [[${sehouse.saleState}]];
    var state = [[${sehouse.state}]];
    $(function () {
        var tels="";
        if(sortTel != null && sortTel != ''){
            tels =sortTel;
        }else{
            if (isDel == 1 || (saleState == 4 && state == 3)) {
                tels = keeperTel2.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
            }else {
                tels = keeperTel2;
            }
        }


        var sessionId = $("#sessionId").val()
        var projectId = $("#projectId").val()
        var linksessionId = '';
        if (sessionId!= ''){
            var pagekey = 'https://sy.fangxiaoer.com/salehouse/'+projectId+'.htm?sessionId='+sessionId
            $.ajax({
                type: "POST",
                async: false,
                url:  "https://tools.fangxiaoer.com/url/encode",
                data:{'url': pagekey},
                dataType : 'json',
                success: function (data) {
                    console.log(data)
                    linksessionId = (data.content).replace("/",'');
                    console.log(linksessionId)

                }
            });
        }

        var scene = 'tel,' +[[${houseId}]]+',2,' + linksessionId;
        var scene3 = 'fang,' +[[${houseId}]]+',2';
        var img = "";
        var img3 = "";
        var sss;
        $.ajax({
            type: "GET",
            async: false,
            url:  "/getWxACode",
            data:{"scene": scene},
            dataType : 'json',
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            success: function (data) {
                img = data.img;
                sss = data;
            }
        });

        $.ajax({
            type: "GET",
            async: false,
            url:  "/getWxACode",
            data:{"scene": scene3},
            dataType : 'json',
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            success: function (data) {
                img3 = data.img;
                sss = data;
            }
        });
        console.log(scene3)
        $("#second1").attr("src","data:text/html;base64,"+img);
        $("#second2").attr("src","data:text/html;base64,"+img);
        $(".img-Applets-Show").attr("src","data:text/html;base64,"+img3);
    });
    /*]]>*/
</script>
<script>
    $(document).ready(function() {
        $("#peitao").hide();
        $(".bkNavFloat").removeClass("fixbkb");
        $(window).scroll(function() {
            var s = $(this).scrollTop();
            if (s >= 800) {
                $(".bkNavFloat").addClass("fixbkb");
                player.pause();
                player2.pause();
            } else {
                $(".bkNavFloat").removeClass("fixbkb");
            }

        })
        $(".img-Applets-Close").click(function () {
            $(".img-Applets").hide()
        })
    });

    function classon(index) {
        var offsetTop = $("#h" + index).offset().top;
        var scrollTop = $(document).scrollTop();
        var w_height = $(window).height();
        var load_height = $("#h" + index).height();

        //if (scrollTop > offsetTop - w_height && scrollTop < offsetTop + load_height) {
        if (scrollTop > offsetTop - 50) {
            $(".bkNavFloat").find("li").removeClass("on");
            $(".bkNavFloat").find("li:eq(" + index + ")").addClass("on");
        }
    }
    $(".bkNavFloat").find("li").click(function(e) {
        $(".bkNavFloat").find("li").removeClass("on");
        $(this).addClass("on");
    });
    $(document).ready(function () {
        var videoVal = $("#mediaId").val();
        var housePan = $("#housePan").val();
        var panUrl = $("#panUrl").val();
        var videoImgPath =$("#videoImgPath").val()
        var panUrl_VRImg = $("#panUrl_VRImg").val();
        var videoMinImg = $("#indexPic").val()
        if( videoVal !=""){
            //改为调接口获取视频地址（2018.10.20）
            player = new Aliplayer({
                id: 'J_prismPlayer',
                autoplay: false,
                //支持播放地址播放,此播放优先级最高
                source: $("#videoPath").val(),
                cover: $("#videoImgPath").val()
            }, function (player) {
                player.on("ended", endedHandle);
                console.log('播放器创建好了。')
            });
            function endedHandle() {
                player.dispose(); //销毁
                $('#J_prismPlayer').empty();
                //重新创建
                player = new Aliplayer({
                    id: 'J_prismPlayer',
                    autoplay: false,
                    //支持播放地址播放,此播放优先级最高
                    source: $("#videoPath").val(),
                    cover: $("#videoImgPath").val()
                });
            };
            player2 = new Aliplayer({
                id: 'J_prismPlayer2',
                autoplay: false,
                //支持播放地址播放,此播放优先级最高
                source: $("#videoPath").val(),
                cover: $("#videoImgPath").val()
            }, function (player2) {
                player2.on("ended", endedHandle);
                console.log('播放器创建好了。')
            });
            function endedHandle() {
                player2.dispose(); //销毁
                $('#J_prismPlayer2').empty();
                //重新创建
                player2 = new Aliplayer({
                    id: 'J_prismPlayer2',
                    autoplay: false,
                    //支持播放地址播放,此播放优先级最高
                    source: $("#videoPath").val(),
                    cover:$("#videoImgPath").val()
                });
            }
            $(".playerPause").click(function () {
                player.pause();
                player2.pause();
            })

        }

//                头部详情房源相册轮播
        $('#demo1').banqh({
            box:"#houseAlbum",//总框架
            pic:"#big_photo",//大图框架
            pnum:"#min_photoMain",//小图框架
            prev_btn:"#min_prev_btn1",//小图左箭头
            next_btn:"#min_next_btn1",//小图右箭头
            pop_prev:"#large_prev_btn1",//弹出框左箭头
            pop_next:"#large_next_btn1",//弹出框右箭头
            pop_div:"#large_photo",//弹出框框架
            pop_pic:"#large_photoMain",//弹出框图片框架
            pop_xx:".photo_Popup_xx",//关闭弹出框按钮
            mhc:".photo_Popup",//朦灰层
            autoplay:false,//是否自动播放
            interTime:5000,//图片自动切换间隔
            delayTime:400,//切换一张图片时间
            pop_delayTime:400,//弹出框切换一张图片时间
            order:0,//当前显示的图片（从0开始）
            picdire:true,//大图滚动方向（true为水平方向滚动）
            mindire:true,//小图滚动方向（true为水平方向滚动）
            min_picnum:4,//小图显示数量
            pop_up:true//大图是否有弹出框
        })
//                房源详情模块相册点击弹出
        $('#demo2').banqh({
            box:"#houseAlbum2",//总框架
            pic:"#big_photo2",//大图框架
            pnum:"#min_photoMain2",//小图框架
            prev_btn:"#min_prev_btn12",//小图左箭头
            next_btn:"#min_next_btn12",//小图右箭头
            pop_prev:"#large_prev_btn12",//弹出框左箭头
            pop_next:"#large_next_btn12",//弹出框右箭头
            pop_div:"#large_photo2",//弹出框框架
            pop_pic:"#large_photoMain2",//弹出框图片框架
            pop_xx:".photo_Popup_xx",//关闭弹出框按钮
            mhc:".photo_Popup",//朦灰层
            autoplay:false,//是否自动播放
            interTime:5000,//图片自动切换间隔
            delayTime:400,//切换一张图片时间
            pop_delayTime:400,//弹出框切换一张图片时间
            order:0,//当前显示的图片（从0开始）
            picdire:true,//大图滚动方向（true为水平方向滚动）
            mindire:true,//小图滚动方向（true为水平方向滚动）
            min_picnum:4,//小图显示数量
            pop_up:true//大图是否有弹出框
        })
        $(".noAClick").click(function () {
            $(".photo_Popup").hide();
            $(".large_photo").hide();
        })

    })

    //登录关闭X
    $("#loginClose").click(function () {
        $("#login").hide();
        $(".reportPopupHb").hide();
    });
    $("#MapPeiTao").click(function() {
        $("html, body").animate({
            scrollTop: $("#MapZhouBianPeiTao1_mapDom").offset().top }, {duration: 500,easing: "swing"});
        return false;
    });

    var islogin;
    $(function () {
        islogin = $("#sessionId").val();
//                pay.init();
//                photo.init(0)
    });

    $(function () {
        var zj = $(".price b").html()
        var sf = zj * 0.3
        dk = zj - sf
        dk1 = parseInt(dk)
        sf = dk - dk1 + sf
        sf = Math.round(sf)
        $(".shoufu").html(sf)
        dk = zj - sf
        yll = 4.90 * 0.01 / 12
        n = Math.pow(1 + yll, 30 * 12)
        debx = dk * yll * n / (n - 1)
        debx = Math.round(debx * 10000)
        $(".debx").html(debx)
    })

    //图片自适应大小
    function imgSize5() {
        //滚动大图
        var imgWidthHeght2 = $(".imgMax1 ul li").eq(0).width() / $(".imgMax1 ul li").eq(0).height()
        $(".imgMin ul li img").each(function () {
            if (parseInt($(this).width()) <= parseInt($(this).height())) {
                $(this).css({"height": "100%", "width": "auto"})
                $(this).css({
                    "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                    "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                })
            } else {
                $(this).css({"width": "100%", "height": "auto"})
                $(this).css({
                    "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                    "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                })
            }
        })
    }

    $(function () {
        //更多相册
        var len = $(".photo li").length;
        if (len > 8) {
            $(".photo li:gt(7)").hide();
            $(".photo ul").after("<span>查看全部照片("+len+"张)</span>")
        }
        $(".photo span").live("click", function () {
            $(this).hide();
            $(".photo li").show();
        })

        //地图 向下滑动
        var maptop = $("#MapZhouBianPeiTao1_mapDom").offset().top
        $(".basic s").click(function () {
            $('body,html').animate({ scrollTop: maptop }, 500);
        })
    })


    //风险提醒
    $(".risk").click(function(){
        $('.reportPopupHb,.riskm').show()

    })
    $(".rclose").click(function(){
        $('.reportPopupHb,.riskm').hide()
    })
</script>

<!--2020-07-24 安心好房事件 -->
<script>
    $('.anin_info ,.bubble').mouseenter(function() {
        $('.bubble').show()
    })
    $('.bubble_none').mouseenter(function() {
        $('.bubble').hide()
    })
    $('.anin_info,.bubble').mouseleave(function() {
        $('.bubble').hide()
    })
</script>
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
</body>
</html>