<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${sehouse.subName + '二手房_沈阳' + sehouse.subName +'_' +  sehouse.subName + '二手房信息服务 - 房小二网'}"></title>
    <meta name="keywords" th:content="${sehouse.subName + '二手房,沈阳' + sehouse.subName +'二手房,' + sehouse.regionName +'二手房,沈阳二手房,' + sehouse.subName +'称现房'}"/>
    <meta name="description" th:content="${'房小二网二手房频道为您提供'+sehouse.title+'二手房信息，以及'+sehouse.regionName+sehouse.subName+'二手房价格、最新'+ sehouse.subName+'二手房房源'
    +sehouse.subName + '过户代办，'+sehouse.subName+'求租信息、地图位置等'+sehouse.subName+'房源信息。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang2/'+houseId+'.htm'}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/view2017.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/global/js/tab.js"></script>
    <script src="https://static.fangxiaoer.com/global/js/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/md5.js"></script>
    <script src="/js/indent.js"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/saleHouseIndent.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
    <style>
        .mapsele{top: 0;width: 350px;}

        /*.map_dl dd a:hover{ background:url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian2.png) no-repeat 7px 18px;}*/
        .mapTab li span.s8 {
            background-position: -543px -4px;
        }
        .mapTab li a:hover .s8{background-position:-543px -64px;}
        .mapTab li {
            padding: 13px 11px;
        }
        .noyongjin dt {
            margin-top: 8px !important;
        }
        .license{position: absolute;width: 260px;height: 370px;border: 1px solid #ccc;display: none;
            border-radius: 5px;z-index: 99;background-color: #FFF;bottom: 40px;right: 30px;padding: 5px;}
        .license_img{
            width: 100% !important;
            height: 100% !important;
            margin: 0px !important;
            border-radius: 0px !important;
        }
        /*.map_dl dd a i{display: none}*/
    </style>
</head>

<body>
<input type="hidden" id="houseType" value="1"/>
<input type="hidden" id="Idhouse" th:value="${houseId}"/>
<input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}"/>
<!--<input type="hidden" id="sessionId" value="cf9058acf5eb1a847550acd8c81427f3"/>-->
<form name="form1" method="post" action="105868" id="form1">
    <div class="hid">

        <!--看房记录-->



        <!--本小区房源-->
        <div class="title hid">
            <a th:href="'/saleHouses/-v'+${sehouse.subId}" class="more" target="_blank">查看本小区全部出售房源 ></a>
            <span>本小区房源</span>
        </div>
        <div class="row hid">

        </div>

    </div>

    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="cue=2,map=2,phone=2,type=2"></div>

    <!--面包屑-->
    <div class="w crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleHouses/" target="_blank">沈阳二手房</a> &gt; <a href="/dealSales/" target="_blank">成交房源</a> &gt; <a th:href="${'/dealSales/r'+sehouse.regionId}" target="_blank" th:text="${sehouse.regionName}"></a> &gt; <a th:href="${'/saleVillages/'+sehouse.subId+'/index.htm'}" target='_blank' th:text="${sehouse.subName}"></a></div>
    <!--基本信息-->
    <div class="w main">
        <div class="header_sale">
            <h1 class="title1" th:text="${sehouse.title}"></h1>
            <ul>
                <li th:if="${!#strings.isEmpty(dealSale.dealTime)}"  th:text="${'成交时间：'+#strings.toString(dealSale.dealTime).replace('.','-')}"></li>
            </ul>
            <!--分享标签-->
            <div th:include="fragment/fragment:: shareIcon"></div>
        </div>
        <div class="photoAlbum">
            <div class="imgMax">
                <ul>

                    <li  th:if="${!#lists.isEmpty( sehouse.pic)}" th:each="pic:${sehouse.pic}">
                        <img th:src="${#strings.isEmpty(pic.url)?'':#strings.toString(pic.url).replaceAll('middle','big')}" onload="imgSize()" />
                    </li>
                    <li th:if="${#lists.isEmpty( sehouse.pic)}">
                        <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" onload="imgSize()"  />
                    </li>
                </ul>
            </div>
            <div class="imgMin">
                <span class="hover"><s></s></span>
                <div>
                    <ul>

                        <li  th:if="${!#lists.isEmpty( sehouse.pic)}" th:each="pic:${sehouse.pic}">
                            <img th:src="${#strings.isEmpty(pic.url)?'':#strings.toString(pic.url)}" onload="imgSize()" />
                        </li>
                        <li th:if="${#lists.isEmpty( sehouse.pic)}">
                            <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" onload="imgSize()"  />
                        </li>
                    </ul>
                </div>

                <b><s></s></b>
            </div>
        </div>
        <div class="mainContent">
            <div class="price"><span><b th:text="${dealSale.dealPrice eq '0' ? '面议':(#strings.toString(dealSale.dealPrice).contains('.') ?  #strings.toString(dealSale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : dealSale.dealPrice)}"></b><th:block th:if="${dealSale.dealPrice ne '0'}">万</th:block></span>
                <p th:if="${dealSale.dealPrice ne '0'}"><th:block th:text="${#strings.toString(dealSale.dealUnitPrice).contains('.') ?  #strings.toString(dealSale.dealUnitPrice).replaceAll('0+?$','').replaceAll('[.]$', '') : dealSale.dealUnitPrice}"></th:block>元/m²</p>
            </div>
            <div class="type">
                <ul style="margin-left: 0">
                    <li style="width: 110px">
                        <b th:text="${#strings.toString(dealSale.SalePrice).contains('.') ?  #strings.toString(dealSale.SalePrice).replaceAll('0+?$','').replaceAll('[.]$', ''):dealSale.SalePrice}"></b>
                        <p>挂牌价格（万）</p>
                    </li>
                    <li style="width: 110px">
                        <b th:text="${dealSale.cycle}"></b>
                        <p>成交周期（天）</p>                    </li>
                    <li style="width: 110px">
                        <b th:text="${dealSale.totalVisitedNum}"></b>
                        <p>浏览（次）</p>                    </li>
                    <li style="width: 110px">
                        <b th:text="${dealSale.favorite}"></b>
                        <p>关注（次）</p>
                    </li>
                </ul>
            </div>
            <div class="type">
                <ul>
                    <li>
                        <b th:text="${sehouse.layout}"></b>
                        <p th:text="${sehouse.floorDesc+'/'+sehouse.totleFloorNumber+'层'}"></p>
                    </li>
                    <li>
                        <b th:text="${sehouse.area+'m²'}"></b>
                        <p>建筑面积</p>
                    </li>
                    <li>
                        <b th:text="${sehouse.forward}"></b>
                        <p th:text="${sehouse.buildDate eq ''}?${sehouse.buildingTime }:${ sehouse.buildDate}"></p>
                    </li>
                </ul>
            </div>
            <div class="card jjr" style="position: relative;overflow: inherit;">
                <p>
                    <a  th:if="${sehouse.keeperLevel2 ne '1'}" th:href="${#strings.isEmpty(sehouse.agencyId) ? '' :'/agent/dealSecond/'+sehouse.agencyId}"  target="_blank">
                        <img th:src="${sehouse.keeperPic2 ne null and sehouse.keeperPic2 ne''}?${sehouse.keeperPic2}:@{https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png}"  /><br>
                    </a>
                    <img th:if="${sehouse.keeperLevel2 eq '1'}" th:src="${sehouse.keeperPic2 ne null and sehouse.keeperPic2 ne''}?${sehouse.keeperPic2}:@{https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png}"  /><br>
                    <a th:style="${sehouse.keeperLevel2 ne '1'}?'display:block':'display:none'"  th:href="${#strings.isEmpty(sehouse.agencyId) ? '' :'/agent/dealSecond/'+sehouse.agencyId}"  target="_blank">进入Ta的店铺</a>
                </p>

                <dl  th:class="${sehouse.isXiQue} eq '1' ? '':'noyongjin'">
                    <dt>
                        <a th:if="${sehouse.keeperLevel2 ne '1'}" th:href="${#strings.isEmpty(sehouse.agencyId) ? '' :'/agent/dealSecond/'+sehouse.agencyId}"  target="_blank">
                            <b  th:text="${sehouse.keeperName2}"></b>
                        </a>
                        <b th:if="${sehouse.keeperLevel2 eq '1'}" th:text="${sehouse.keeperName2}"></b>
                        <span th:text="${sehouse.intermediaryName ne null and sehouse.intermediaryName ne ''}?${sehouse.intermediaryName}:''"></span>
                    </dt>
                    <dd th:text="${sehouse.sortTel == null or #strings.toString(sehouse.sortTel) eq ''? sehouse.keeperTel2:sehouse.sortTel}"></dd>
                    <ddd><th:block th:text="${#strings.isEmpty(dealSale.dealCount)?'':'成交'+dealSale.dealCount+'套&nbsp;&nbsp;'}"></th:block><th:block th:text="${#strings.isEmpty(dealSale.dealCycle)?'':'平均成交周期'+dealSale.dealCycle+'天'}"></th:block></ddd>
                    <div style="clear: both;"></div>
                    <span style="cursor: pointer;padding-top: 20px;" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                        公司执照编码：
                        <span style="color:#999;">
                            <th:block th:text="${sehouse.agentBusinessNum}"></th:block>
                        </span>
                    </span>
                    <div class="license" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                        <img th:src="${sehouse.agentBusinessCardForPc}" class="license_img">
                    </div>
                    <div style="clear: both;"></div>
                </dl>
                <div style="clear: both;"></div>
                <!--<p class="card_btn" th:if="${sehouse.isXiQue eq '1'}"><a class="djyh" style="display:">佣金95折</a></p>-->
            </div>
        </div>
    </div>
    <script>
        function showLicense(e) {
            $(".license").show();
        }
        function hideLicense(e) {
            $(".license").hide();
        }
    </script>
    <div class="w">
        <div class="left">
            <div class="details ">
                <div class="head">房源描述</div>
                <ul>
                    <li>
                        <span>交易属性</span>
                        <div>
                            <ul>
                                <li>
                                    <span>挂牌时间：</span>
                                    <p th:text="${#strings.toString(dealSale.AddTime).substring(0,#strings.toString(dealSale.AddTime).length()-8).replace('.','-')}"></p>
                                </li>
                                <li>
                                    <span>房屋年限：</span>
                                    <p th:text="${#strings.isEmpty(dealSale.FiveYearName)?'暂无资料':dealSale.FiveYearName}"></p>
                                </li>
                                <li>
                                    <span>房屋用途：</span>
                                    <p th:text="${#strings.isEmpty(sehouse.saleTypeName)?'暂无资料':sehouse.saleTypeName}"></p>
                                </li>
                                <li>
                                    <span>交易属性：</span>
                                    <p th:text="${#strings.isEmpty(dealSale.HousingType)?'暂无资料':dealSale.HousingType}"></p>
                                </li>
                                <li>
                                    <span>土地年限：</span>
                                    <p th:text="${#strings.isEmpty(dealSale.deadline)?'详见业主土地证明材料或相关政府部门登记文件':dealSale.deadline+'年'}"></p>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li class="" th:style="${sehouse.houseTrait ne ''}?'display:block':'display:none'">
                        <span>房源标签</span>
                        <div>
                            <th:block th:each="l:${list}" >
                                <b th:text="${l}"></b>&nbsp;
                            </th:block>

                        </div>
                    </li>
                    <li>
                        <span>小区详情</span>
                        <div>
                            <ul>
                                <li>
                                    <span>小区名称：</span>

                                    <a th:href="${'/saleVillages/'+sehouse.subId+'/index.htm'}"  target='_blank'> <p th:text="${sehouse.subName}"  style="color:#2f51b3;"></p></a>
                                </li>
                                <li>
                                    <span>物&ensp;业&ensp;费：</span>
                                    <p th:text="${sehouse.propertyFee ne '' and sehouse.propertyFee ne null and sehouse.propertyFee ne '0.0'}?${sehouse.propertyFee+'元/m²·月'}:'暂无资料'"></p>
                                </li>
                                <li>
                                    <span>物业公司：</span>
                                    <p th:text="${sehouse.propertyCom ne '' and sehouse.propertyCom ne null}?${sehouse.propertyCom}:'暂无资料'"></p>
                                </li>
                                <li>
                                    <span>租&emsp;&emsp;房：</span>
                                    <p th:text="${sehouse.rentCount ne '' and sehouse.rentCount ne null}?${sehouse.rentCount+'套'}:'暂无资料'"> </p>
                                </li>
                                <li>
                                    <span>绿&ensp;化&ensp;率：</span>
                                    <p th:text="${sehouse.landScaping ne '' and sehouse.landScaping ne null}?${sehouse.landScaping}:'暂无资料'"></p>
                                </li>
                                <li>
                                    <span>二&ensp;手&ensp;房：</span>
                                    <p th:text="${sehouse.saleCount ne '' and sehouse.saleCount ne null}?${sehouse.saleCount+'套'}:'暂无资料'"> </p>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li th:if="${!#strings.isEmpty(sehouse.describe)}">
                        <span>房源描述</span>
                        <div class="fyms" th:utext="${sehouse?.describe}">
                        </div>
                    </li>
                    <li class="" th:if="${!#strings.isEmpty(sehouse.mentality)}">
                        <span>业主心态</span>
                        <div class="fyms" th:utext="${sehouse.mentality}"></div>
                    </li>
                    <li class="" th:if="${!#strings.isEmpty(sehouse.traffic) || !#strings.isEmpty(sehouse.education) || !#strings.isEmpty(sehouse.market)
         || !#strings.isEmpty(sehouse.hospital) || !#strings.isEmpty(sehouse.bank) || !#strings.isEmpty(sehouse.othersettings)}">
                        <span>小区配套</span>
                        <div  style="border:0" class="canteen">
                            <div th:if="${!#strings.isEmpty(sehouse.traffic)}"><span>交通：</span><p th:text="${sehouse.traffic}"></p></div><!-- traffic-->
                            <div th:if="${!#strings.isEmpty(sehouse.education)}"><span>学校：</span><p th:text="${sehouse.education}"></p></div><!-- education -->
                            <div th:if="${!#strings.isEmpty(sehouse.market)}"><span>商场：</span><p th:text="${sehouse.market}"></p></div><!-- market -->
                            <div th:if="${!#strings.isEmpty(sehouse.hospital)}"><span>医院：</span><p th:text="${sehouse.hospital}"></p></div><!-- hospital -->
                            <div th:if="${!#strings.isEmpty(sehouse.bank)}"><span>银行：</span><p th:text="${sehouse.bank}"></p></div><!-- bank -->
                            <div th:if="${!#strings.isEmpty(sehouse.othersettings)}"><span>其他：</span><p th:text="${sehouse.othersettings}"></p></div><!-- othersettings -->
                        </div>
                    </li>
                    <li class="" th:if="${!#strings.isEmpty(sehouse.serviceIntro)}">
                        <span>服务介绍</span>
                        <div class="fyms" th:utext="${sehouse.serviceIntro}"></div>
                        <div style="border:0"></div>
                    </li>
                </ul>
            </div>
            <div class="w photo" th:if="${!#lists.isEmpty( sehouse.pic)}">
                <div class="head " style="display:">房源相册</div>
                <ul>

                    <li class="span4" th:each="pic:${sehouse.pic}">
                        <img th:src="${#strings.isEmpty(pic.url)?'':pic.url}" th:alt="${pic.photoName}"/>
                        <p th:text="${sehouse.subName+' '+sehouse.area+'m²'}"></p>
                    </li>

                </ul>
            </div>
        </div>
        <div class="right" th:if="${similar ne null and #lists.toList(similar).size() ne 0}">
            <div id="xiangsihouse" class="head recommend">
                <h1>同小区你可能感兴趣的房源</h1>
                <ul >
                    <li th:each="s:${similar}">
                        <a  th:href="${'/salehouse/'+s.houseId + '.htm'}" target="_blank">
                            <div>
                                <img th:src="${#strings.isEmpty(s.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.pic}" th:alt="${s.subName}" />
                                <span th:if= "${!#strings.isEmpty(s.price) and #strings.toString(s.price) ne '0.0'}" th:text="${#strings.toString(s.price).contains('.')? #strings.toString(s.price).replaceAll('0+?$','').replaceAll('[.]$', '') : s.price}"></span><i>万</i>
                                <p th:text="${s.room + '室' + s.hall + '厅'}"></p>
                                <b th:text="${s.subName}"></b>
                            </div>

                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="cl"></div>
        <div class="">
<!--            <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
            <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
            <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
            <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>
            <script th:inline="javascript">
                $(document).ready(function () {

                    var id = [[${sehouse.subId}]];
                    var lng = [[${sehouse.longitude}]];
                    var lat = [[${sehouse.latitude}]];
                    var title = [[${sehouse.subName}]];
                    var address = [[${sehouse.address}]];
                    var city = '沈阳';
                    var content = "";
                    $("#mapsubTitle").val(title);
                    bdMap.init("memap", { id: id, houselng: lng, houselat: lat, radius: 2000, suofa: 14, bdtitle: title, bdcontent: content, address: address, city: city });
                });
            </script>
        </div>
        <style>
            .map_lpcon {
                padding:0!important;
                margin:0!important;
            }
            .map_lp{
                display:block!important;
                padding: 0!important;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_center {
                z-index: 3;
                padding: 16px 0px;
            }
            .BMapLib_SearchInfoWindow {
                font: 12px arial,宋体,sans-serif;
                position: absolute;
                background-color: #fff;
                cursor: default;
                border-radius: 10px;
                padding: 0 10px;
            }
            .BMapLib_SearchInfoWindow img {
                border: 0;
                margin-top: 2px!important;
                top: auto !important;
            }
            .BMapLib_bubble_content p{white-space: normal !important;}
            .BMapLib_SearchInfoWindow .BMapLib_bubble_top {
                border-bottom: 1px solid #ccc;
                height: 40px!important;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_title {
                line-height: 40px!important;
                background-color: #fff!important;
                overflow: hidden;
                height: 40px!important;
                adding: 0 5px;
                font-size: 14px!important;
            }
            .BMapLib_SearchInfoWindow {
                font: 12px arial,宋体,sans-serif;
                position: absolute;
                border: 0!important;
                background-color: #fff;
                cursor: default;
                box-shadow: 0px 0px 10px #999;
            }
            .BMapLib_SearchInfoWindow .BMapLib_bubble_tools div {
                float: right;
                height: 30px;
                width: 22px;
                cursor: pointer;
                background-color: #fff!important;
                padding-top: 8px!important;
            }
            /*全屏*/
            .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
            .full .nmaptitleleft{margin-top:0}
            .full .symap{height:100%}
            .full .dt1{height:100%;}
            .full #memap{height: 100% !important;}
            .full .mapsele{height:660px !important}
            .full{z-index: 100003 !important;}
            .nmaptitleleft a{float:right;background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg) no-repeat;padding-left:23px}
            .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:20px;}
            .nmaptitleleft a.map_house{background-position:0px 12px}
        </style>
        <div id="MapZhouBianPeiTao1_mapDom">
            <div class="nmaptitle">
                <div class="nmaptitleleft">
                    <a href="https://sy.fangxiaoer.com/salemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                    <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                    <p>周边配套</p>
                </div>
            </div>
            <div class="symap">
                <div class="dt1">
                    <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                    </div>
                    <div class="mapsele" id="mapsele" >
                        <!--<div class="msclose" id="msclose"></div>-->
                        <div class="mapTab" id="mapTab">
                            <ul>
                                <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                                <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                                <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                                <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                                <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                                <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                                <!--<li onclick="bdMap.searechMap('银行')"><a href="javascript:void(0)">银行</a></li>-->
                            </ul>
                        </div>
                        <div class="map_lp">
                            <div id="hs_wrap">
                                <!--<div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>-->
                                <div class="map_lpcon"  id="r-result">
                                    <div class="map_dl">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <script>
                    $(function () {
                        $(".mapTab ul li ").click(function () {
                            $(".mapTab ul li ").removeClass("hover")
                            $(this).addClass("hover")
                        });
                        $(".map_dl a").mouseover(function () {
                            $(this).css("background-color", "#e8f4ff");
                        });
                        $(".map_dl a").mouseout(function () {
                            $(this).css("background-color", "#fff");
                        });
                    });
                    function Full() {
                        if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                            $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                            $(".map_lpcon").height("288px")
                            $(".map_full").html("全屏")
                        } else {
                            $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                            $(".map_lpcon").height("100%")
                            $(".map_full").html("取消全屏")
                        }
                    }
                </script>
            </div>
        </div>
    </div>

    <div class="cl"></div>
    <div class="disclaimer">
        <strong>免责声明：</strong>房源信息由网站用户提供，其真实性、合法性由信息提供者负责，最终以政府部门登记备案为准。本网站不声明或保证内容之正确性和可靠性，购买该房屋时，请谨慎核查。如该房源信息有误，您可以投诉此房源信息或<strong>拔打举报电话：400-893-9709</strong>。
    </div>
    <div class="cl"></div>

    <div class="cl"></div>
        <div th:include="fragment/fragment:: footer_detail" ></div>
        <div th:include="fragment/fragment::tongji"></div>
        <div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
        <noscript><img src="https://d5nxst8fruw4z.cloudfront.net/atrk.gif?account=YWBQn1QolK10fn" style="display:none" height="1" width="1" alt="" /></noscript>
        <!-- End Alexa Certify Javascript -->


        <!--佣金-->
        <div class="saleHouseIndent">
            <div class="page1">
                <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <h1>在线支付享优惠</h1>
                <ul >
                    <li><span>购房人</span><input id="name" placeholder="请填写姓名" type="text" maxlength="20" /></li>
                    <li><span>手机号码</span><input type="text" id="phone" placeholder="请填写手机号码" maxlength="11" value="" /></li>
                    <li class="yzm" style="border-bottom: 1px solid #ccc;"><span>请输入</span><input type="password" autocomplete="new-password" id="code" placeholder="验证码/密码" maxlength="20" style="width: 129px;" /><b class="fxe_ReSendValidateCoad">获取验证码</b><b class="fxe_validateCode" style="display: none;"></b></li>
                    <!--
                                    <li class="mm" style="display: none; border-bottom: 1px solid #ccc;"><span>密码</span><input type="password" id="pwd" placeholder="请填写密码" maxlength="20" /></li>
                    -->
                    <input type="hidden" id="userType" value="">
                </ul>
                <a>佣金95折</a>
                <dl>
                    <dt></dt>
                    <dd>我已经看过并同意<a href="https://info.fangxiaoer.com/Help/remission" target="_blank">《佣金减免说明书》</a></dd>
                </dl>
                <!--<span class="x"></span>-->
            </div>
            <div class="page2">
                <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <h1>在线支付</h1>
                <p th:text="${sehouse.title}">
                </p>
                <ul>
                    <li><span>在线支付20元，约折佣340元</span></li>
                    <li th:text="${'地址：'+sehouse.address}"></li>
                    <li th:text="${'经纪人：'+ (sehouse.keeperName2 ne null and sehouse.keeperName2 ne ''? sehouse.keeperName2:sehouse.houseOwner) + ' '+ (sehouse.sortTel == null or #strings.toString(sehouse.sortTel) eq ''? sehouse.keeperTel2:sehouse.sortTel)}"></li>
                </ul>
                <dl>
                    <dt>支付方式： </dt>
                    <dd class="weixin hover">
                        <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/weixin.png" alt="微信" /><i></i></dd>
                    <dd class="alipay">
                        <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/alipay.png" alt="支付宝" /><i></i></dd>
                </dl>
                <span>支付金额：20元</span>
                <a>去支付</a>
                <!--<span class="x"></span>-->
            </div>
            <div class="page3">
                <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <h1>请使用<span>支付宝</span>扫描下面二维码</h1>
                <img src="" alt="二维码获取失败" />
                <!--<span class="x1"></span>-->
            </div>
            <div class="page4">
                <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/success.png" />
                <b>支付成功</b>
                <a href="https://my.fangxiaoer.com/payOrder">查看订单</a>
                <!--<span class="x1"></span>-->
            </div>
        </div>

        <div class="bigImgShow">
            <div class="showImg">
                <ul>
                    <li th:if="${#lists.isEmpty( sehouse.pic)}">
                        <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" onload="imgSize()"  />
                    </li>
                    <li th:if="${!#lists.isEmpty( sehouse.pic)}" th:each="pic:${sehouse.pic}">
                        <img th:src="${#strings.toString(pic.url).replace('middle','big')}" alt=""  onload="imgSize()"/>
                    </li>
                </ul>
            </div>
            <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"/></div>
            <div class="prev"></div>
            <div class="next"></div>
        </div>
        <script type="text/javascript">
            var islogin;
            $(function () {
                islogin = $("#sessionId").val();
                pay.init();
                photo.init()
            });

            $(function () {
                var zj = $(".price b").html()
                var sf = zj * 0.3
                dk = zj - sf
                dk1 = parseInt(dk)
                sf = dk - dk1 + sf
                sf = Math.round(sf)
                $(".shoufu").html(sf)
                dk = zj - sf
                yll = 4.90 * 0.01 / 12
                n = Math.pow(1 + yll, 30 * 12)
                debx = dk * yll * n / (n - 1)
                debx = Math.round(debx * 10000)
                $(".debx").html(debx)
            })

            //图片自适应大小
            function imgSize5() {
                //滚动大图
                var imgWidthHeght2 = $(".imgMax1 ul li").eq(0).width() / $(".imgMax1 ul li").eq(0).height()
                $(".imgMin ul li img").each(function () {
                    if (parseInt($(this).width()) <= parseInt($(this).height())) {
                        $(this).css({"height": "100%", "width": "auto"})
                        $(this).css({
                            "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                            "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                        })
                    } else {
                        $(this).css({"width": "100%", "height": "auto"})
                        $(this).css({
                            "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                            "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                        })
                    }
                })
            }

            $(function () {
                //更多相册
                var len = $(".photo li").length;
                if (len > 8) {
                    $(".photo li:gt(7)").hide();
                    $(".photo ul").after("<span>查看全部照片("+len+"张)</span>")
                }
                $(".photo span").live("click", function () {
                    $(this).hide();
                    $(".photo li").show();
                })

                //地图 向下滑动
                var maptop = $("#MapZhouBianPeiTao1_mapDom").offset().top
                $(".basic s").click(function () {
                    $('body,html').animate({ scrollTop: maptop }, 500);
                })
            })

        </script>

</form>
<!--<div th:include="fragment/fragment::esfCommonFloat"></div>-->
<script type="text/javascript">
    $("#MapPeiTao").click(function() {
        $("html, body").animate({
            scrollTop: $("#MapZhouBianPeiTao1_mapDom").offset().top }, {duration: 500,easing: "swing"});
        return false;
    });
</script>
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
<div th:include="house/detail/fragment_login::login"></div>
</body>
</html>

