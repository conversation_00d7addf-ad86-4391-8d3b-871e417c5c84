<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"  xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>沈阳地图找二手房，沈阳二手房产地图，沈阳二手房地图 - 房小二网</title>
    <meta name="keywords" content="沈阳二手房,沈阳二手房地图,沈阳地图找房,沈阳房产地图,沈阳楼市地图" />
    <meta name="description" content="房小二网地图找房为您提供更新的沈阳二手房楼盘地图信息，全面的沈阳二手房位置及沈阳二手房出售相关信息。通过简单方便的地图找房功能，使您更加方便地找到目标区域的二手房信息，为您创造更好的二手房买房体验，查找沈阳二手房，就来房小二网二手房地图找房。" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/housemap1.htm">
    <link rel="stylesheet" type="text/css" href="//static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
<!--    <script type="text/javascript" src="//api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <link rel="stylesheet" href="//api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
    <link href="/css/jquery-ui-1.9.2.custom.min.css"  rel="stylesheet" type="text/css" >
    <script src="//static.fangxiaoer.com/js/baiduMap/ajax.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="//static.fangxiaoer.com/web/styles/sy/baiduMap/baiduStationMap.css?v=20190528" />
    <link rel="stylesheet" type="text/css" href="//static.fangxiaoer.com/web/styles/map/map_z.css?v=20180605" />
    <script src="//static.fangxiaoer.com/js/baiduMap/vue.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-ui-1.9.2.custom.min.js" type="text/javascript" charset="utf-8"></script>
<!--    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>-->
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
<!--    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>-->
    <!--搜索框与租房共用一个js-->
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/map/js/rentSearch.js?v=20191129"></script>
<!--    <script type="text/javascript" src="/js/rentbaidumap/rentSearch.js"></script>-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/js/map/style/secMapDual.css?v=20191122"/>
<!--    <link href="/css/secondbaidumap/secMapDual.css"  rel="stylesheet" type="text/css" >-->


</head>
<style>
    .stationModel {
        width: auto;
        height: 170px;
        padding:11px 34px ;
    }
    .showRegion span {
    width: 60px;
    margin: auto;
}
    .stationModel li{line-height: 29px;    font-weight: bold;}
</style>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<img id="loading" src="https://static.fangxiaoer.com/web/images/sy/map/loading.gif" alt="加载中……" style="display: none;">
<div id="map" v-bind:style="{height: height+'px',width:windowWidth+'px'}">

    <div id="saleMetroNav">
        <!--搜索框-->
        <div id="search2017" class="saleMetroBtn">
            <input id="txtkeys"  type="text"   placeholder="请输入小区或地铁站开始找房" class="ac_input">
            <!-- <img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png" alt="x" style="display: none">-->
            <input type="button" value="" class="btn_search search_btn">
            <div class="cl"></div>
        </div>
        <div class="saleMetroSelect" id="saleMetroSelect">
            <!--        <div class="mapSelect">-->
            <div class="price metroSelectBtn" v-if="navIsActive==0||navIsActive==1||navIsActive==2||navIsActive==4">
                <p class="price_show_text">{{ priceList.ActiveText }}</p>
                <ul style="overflow: auto;width: 138px;height: 400px">
                    <li @click="navNormal(priceList,'价格');fontWeight22()">不限</li>
                    <li v-for="vprice in priceList.content" @click="navSelect(priceList,vprice.name,vprice.id);showHouseForSearch(2);fontWeight()">{{ vprice.name }}</li>
                    <div class="writeInput">
                        <input type="text" maxlength="4" id="minPrice" oninput = "value=value.replace(/[^\d]/g,'')">
                        -<input type="text" maxlength="4" id="maxPrice" oninput = "value=value.replace(/[^\d]/g,'')">万
                        <div class="moreSelectBtn">
                            <span>取消</span>
                            <span class="hover" @click="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" type="button" value="">确认</span>
                        </div>
                    </div>
                </ul>
            </div>

            <div class="area metroSelectBtn" v-if="navIsActive==0||navIsActive==1||navIsActive==2||navIsActive==4" >
                <p class="area_show_text">{{ areaList.ActiveText }}</p>
                <ul style="overflow: auto;width: 138px;height: 430px;" >
                    <li @click="navNormal(areaList,'面积');fontWeight11()">不限</li>
                    <li v-for="vprice in areaList.content" @click="navSelect(areaList,vprice.name,vprice.id);showHouseForSearch(2);fontWeight()">{{ vprice.name }}</li>
                    <div class="writeInput">
                        <input type="text" maxlength="4" id="minArea" oninput = "value=value.replace(/[^\d]/g,'')">
                        -<input type="text" maxlength="4" id="maxArea" oninput = "value=value.replace(/[^\d]/g,'')">m²
                        <div class="moreSelectBtn">
                            <span>取消</span>
                            <span class="hover" @click="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="">确定</span>
                        </div>
                    </div>
                </ul>
            </div>
            <div class="layout metroSelectBtn" v-if="navIsActive==0||navIsActive==1||navIsActive==2||navIsActive==4"  >
                <p class="layout_show_text">{{ layoutList.ActiveText }}</p>
                <ul  style="overflow: auto;width: 113px">
                    <li @click="navNormal(layoutList,'户型');fontWeight33();">不限</li>
                    <li v-for="vprice in layoutList.content" @click="navSelect(layoutList,vprice.name,vprice.id);showHouseForSearch(2);fontWeight();">{{ vprice.name }}</li>
                </ul>
            </div>
            <!--更多-->
            <div class="more_screen" v-if="navIsActive==0||navIsActive==1||navIsActive==2||navIsActive==4"  >
                <p>更多</p>
                <ul class="moreSelect" style="overflow: auto">
                    <div  class="brUl">
                        <p class="brP" >朝向</p>
                        <ul>
                            <li @click="navNormal(forwardList,'朝向');brUlClick2()" ><i class="radioBtn"></i>不限</li>
                            <li v-for="vprice in forwardList.content" @click="navSelect(forwardList,vprice.name,vprice.id);showHouseForSearch(2);brUlClick(vprice.id);"><i class="radioBtn"></i>{{ vprice.name }}</li>
                        </ul>
                    </div>
                    <div>
                        <p class="brP" >楼层</p>
                        <ul class="louCeng">
                            <li @click="navNormal(floorList,'楼层');louCengClick2()" ><i class="radioBtn"></i>不限</li>
                            <li v-for="vprice in floorList.content" @click="navSelect(floorList,vprice.name,vprice.id);showHouseForSearch(2);louCengClick(vprice.id);"><i class="radioBtn"></i>{{ vprice.name }}</li>
                        </ul>
                    </div>
                    <div class="moreSelectBtn" style="    margin-left: 177px;">
                        <!--                        <span>取消</span>-->
                        <span class="hover">确认</span>
                    </div>
                </ul>

            </div>
            <span class="clearSeach" v-if="priceList.ActiveId != ''||areaList.ActiveId !=''||layoutList.ActiveId != ''||forwardList.ActiveId != ''||floorList.ActiveId != '' || priceList.ActiveText != '价格' || areaList.ActiveText != '面积'" @click="deleteAllValue();">清空条件</span>
        </div>
        <div class="chooseBtn hoverMap" data-mapNum="1" id="mapNumQY"></div>
        <div class="chooseBtn" data-mapNum="2" id="mapNumDT" ></div>
        <div class="listBtn hover"><i></i></div>
        <div  class="mapHouseList  HouseList" id="metroMapList" >
            <!--左侧小区信息-->
            <div class="metroVillage">
                <h4 class="villageName"><a id="map_plot_title" href="" target="_blank"></a><div><p id="map_jun"></p><span id="map_plot_price"></span><s id="map_jun_unit"></s></div></h4>
                <div class="mVl">
                    <div><p id="map_rate"></p><s id="map_plot_increaseRate"></s></div>
                    <p id="map_plot_address"></p>
                </div>
                <div class="mVR" id="map_agent_information" style="display: none">
                    <a href="" id="map_agent_jump" target="_blank"><img id="map_agent_photo" src="" alt=""></a>

                    <div class="map_agentR">
                        <a id="map_agent" target="_blank"><s id="map_agent_name"></s><span id="map_agent_title"></span></a>
                        <div>
                            <p id="map_agent_mobile"></p>
                             <a  target="_blank" class="liaobeiBtn" id="map_liaobei_jmp">
                                 <div>
                                     <p id="map_agent_liaobei">聊呗</p>
                                 </div>
                             </a>
                        </div>
                    </div>
                </div>
            </div>
            <!--左侧列表内容-->
            <div>

            </div>
            <div class="metroMapListUl" >
                <h3 >小区在售房源<span id="map_house_total"></span>套</h3>
                <!--左侧列表筛选内容-->
                <ul class="chooiceLi" >
                    <li id="0" class="hover">默认<i></i></li>
                    <li id="3" >总价<i></i></li>
                    <li id="5" >面积<i></i></li>
                    <li id="1" >最新<i></i></li>
                </ul>
                <ul class="mMapListUl">
                    <li v-for="vnewHousList in newHousList" >
                        <a v-bind:href="'https://sy.fangxiaoer.com/salehouse/'+vnewHousList.houseId+'.htm'" target="_blank">
                            <img v-if="vnewHousList.pic!='' && vnewHousList.pic!= null" v-bind:src="vnewHousList.pic"/>
                            <img v-if="vnewHousList.pic==null" src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg"/>
                            <div class="metroListR">
                                <h5>{{vnewHousList.title}}<i v-on:click="SetMap(vnewHousList.latitude,vnewHousList.longitude,maxPlat);" onclick="return false;" class="move"></i></h5>
                                <p>
                                    <span>{{vnewHousList.area}}㎡</span>
                                    <span>{{vnewHousList.floorDesc}}/{{vnewHousList.totalFloorNumber}}层</span>
                                    <span>{{vnewHousList.forward}}</span>
                                </p>
                                <ul class="metroTese">
                                    <li v-if="(vnewHousList.houseTrait!='')&&(index<4)" v-for="(trait,index) in vnewHousList.houseTrait.split(',')" v-bind:class="['tese_'+(index+1)]">{{ trait }}</li>
                                </ul>
                                <div class="metroPrise"  v-if="vnewHousList.price != 0">
                                    <span>{{vnewHousList.price}}</span>
                                    <i>万</i>
                                </div>
                                <div class="metroPrise"  v-if="vnewHousList.price == 0">
                                    <span>面议</span>
                                    <!--                            <i>万</i>-->
                                </div>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>

        </div>
        <div  class="noHouseMap">
            <div>
                <h4>暂时没有搜到符合您要求的房源</h4>
                <p>建议您：<a href="/helpSearch?ids=2" target="_blank">填需求表单，为您专属定制找房</a></p>
            </div>
        </div>

    </div>
    <div id="baiduMap"></div>

</div>


<div class="stationModel" style="position: absolute">
    <ul>
        <li onclick="choose_one_line();" data="1"><s></s><i class="stationModelI stationModelI1" style="background:#DB413F" ></i><span>一号线</span></li>
        <li onclick="choose_two_line();"  data="2"><s></s><i class="stationModelI stationModelI2" style="background:#FA6D15"></i><span>二号线</span></li>
        <li onclick="choose_four_line()"  data="6"><s></s><i class="stationModelI stationModelI2" style="background:#9464D6"></i><span>四号线</span></li>
        <li onclick="choose_nine_line();"  data="4"><s></s><i class="stationModelI stationModelI9" style="background:#487FE2"></i><span>九号线</span></li>
        <li onclick="choose_ten_line();" data="7"><s></s><i class="stationModelI stationModelI10"  style="background:#44AD4B"></i><span>十号线</span></li>
        <li onclick="choose_six_line()" data="9"><s></s><i class="stationModelI stationModelI10"  style="background:#F2B40B"></i><span style="color:#888888">六号线（在建中）</span></li>
    </ul>

</div>
<!--登录聊呗 禁止使用经纪人账号-->
<input type="hidden" id="goods" value="1" />
<script>
    var type=2;
    //如果是ie显示不支持
    if(navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > -1 && !(navigator.userAgent.indexOf("Opera") > -1) ){
        document.getElementById("hint").style.display="block";
    }
</script>
<script>
    $(document).ready(function () {
        $("#mapNumQY").click(function () {//点击区域找房 展示地铁模式
            $(this).hide();
            $("#mapNumDT").show();
            baiduMap.mapType = 1;
            $(".stationModel").hide(); //区域模式 隐藏地铁线图标
            // $("#metroMapList").hide();// 点击切换模式 先隐藏左侧列表
            $(this).addClass("hoverMap");
            $("#mapNumDT").removeClass("hoverMap");
            baiduMap.regionStratumJudge();
            $(".stationModel li").css("opacity","1");
        });
        $("#mapNumDT").click(function (){//点击地铁找房 展示区域模式
            $(this).hide();
            $("#mapNumQY").show();
            baiduMap.mapType = 2;
            $(".stationModel").show();
            // $("#metroMapList").hide();// 点击切换模式 先隐藏左侧列表
            $(this).addClass("hoverMap");
            $("#mapNumQY").removeClass("hoverMap");
            //地铁模式 线路层级隐藏筛选=====
            if(baiduMap.map.getZoom() < baiduMap.maxRegion){
                $("#saleMetroSelect").hide();
            }
            baiduMap.subwayStratumJudge();
        });

        var url_ = location.href;
        if(url_.indexOf('subway') != -1){
            $("#mapNumDT").addClass("hoverMap").css("display","none");
            $("#mapNumQY").removeClass("hoverMap").css("display","block");
            $("#saleMetroSelect").css("display","none");
        }
        $(".stationModel li").click(function () {//点击右侧线路选择，增加选中效果
            $(".stationModel li").removeClass("hover");
            $(".stationModel li").css("opacity","0.4");
            $(this).addClass("hover")
        })



        $('#txtkeys').bind('input propertychange', function() {//搜索框下拉出的内容框高度
            var seachH = window.innerHeight-115;
            $(".ui-menu").css("max-height",seachH)
        })


        //点击除筛选项之外的地方，均隐藏筛选项下拉列表
        $(document).bind('click', function(e) {
            var e = e || window.event; //浏览器兼容性
            var elem = e.target || e.srcElement;
            while (elem) { //循环判断至跟节点，防止点击的是div子元素
                if (elem.id && elem.id == 'saleMetroSelect') {
                    return;
                }
                elem = elem.parentNode;
            }
            $('.metroSelectBtn>ul').css('display', 'none'); //点击的不是div或其子元素
        })

        //筛选项点击后列表收起事件
        $(".metroSelectBtn p").click(function () {
            $("#saleMetroNav .saleMetroSelect p").not(this).parent().find("ul").hide()
            $(this).parent().find("ul").toggle()
        })
        /*$(".metroSelectBtn li").click(function () {
            $(this).parent().parent().find("ul").hide()
        })*/
        /**
         * 更多里的点击
         */
        $(".more_screen p").click(function () {
            $("#saleMetroNav .saleMetroSelect p").not(this).parent().find("ul").hide();
            $(this).parent().find("ul").toggle();
        })
        $(".moreSelectBtn span").click(function () {//点击取消收起下拉
            $(this).parent().parent().parent().parent().find("ul").hide()
        })
        $(".moreSelectBtn .hover").click(function () {
            if (baiduMap.forwardList.ActiveId !="" || baiduMap.floorList.ActiveId != ""){
                $(".more_screen>p").addClass("hover")
            }else {
                $(".more_screen>p").removeClass("hover")
            }
        })

        //筛选项增加下拉三角
        $(".saleMetroSelect>div").append("<i class=\"metroIconDown\"></i>")
        //点击按钮控制左侧列表显隐
        $(".listBtn").click(function () {
            if( $(this).hasClass("hover")){
                // $(".noHouseMap").hide();
                $(this).animate({'left':'0px'},500);
                $(this).find("i").css("background","url(https://static.fangxiaoer.com/web/images/map/metroIconRight.jpg) #fff top center");
                $(this).removeClass("hover")
                $("#metroMapList").hide().animate({'left':'-408px'},500);
            }else{
                // $(".noHouseMap").show();
                $(this).animate({'left':'408px'},500);
                $(this).addClass("hover");
                $(this).find("i").css("background","url(https://static.fangxiaoer.com/web/images/map/metroIconRight2.png) #fff top center");
                $("#metroMapList").show().animate({'left':'0px'},500);
            }

        })




        //左侧列表 默认、总价、面积、最新 筛选
        var defaultKeyId =  $(".chooiceLi").find(".hover").attr("id");
        baiduMap.orderKeyId = defaultKeyId;
        $(".chooiceLi li").click(function(){

            var num = $(this).attr("id");
            if(num == 0 || num == 1){
                baiduMap.orderKeyId = num;
                baiduMap.newHouseListRegionAjax();
                $(".chooiceLi li").removeClass("hover");
                $(".chooiceLi li").removeClass("sort_jg");
                $(".chooiceLi li").removeClass("up");
                $(".chooiceLi li").removeClass("down");
                $(".chooiceLi li[id ='"+ num+"']").addClass("hover");
            }else{
                if(num%2 == 1){
                    baiduMap.orderKeyId = num;
                    baiduMap.newHouseListRegionAjax();
                    $(".chooiceLi li").removeClass("hover");
                    $(".chooiceLi li").removeClass("sort_jg");
                    $(".chooiceLi li").removeClass("up");
                    $(".chooiceLi li").removeClass("down");
                    $(".chooiceLi li[id ='"+ num+"']").addClass("sort_jg");
                    $(".chooiceLi li[id ='"+ num+"']").addClass("down");
                    $(".chooiceLi li[id ='"+ num+"']").removeClass("up");
                    $(".chooiceLi li[id ='"+ num+"']").attr("id",num-1);
                }else{
                    baiduMap.orderKeyId = num;
                    baiduMap.newHouseListRegionAjax();
                    num = parseInt(num) +1;
                    $(this).attr("id",num);
                    $(".chooiceLi li").removeClass("hover");
                    $(".chooiceLi li").removeClass("sort_jg");
                    $(".chooiceLi li").removeClass("up");
                    $(".chooiceLi li").removeClass("down");
                    $(".chooiceLi li[id ='"+ num+"']").addClass("sort_jg");
                    $(".chooiceLi li[id ='"+ num+"']").addClass("up");
                    $(".chooiceLi li[id ='"+ num+"']").removeClass("down");
                    $(".chooiceLi li").removeClass("hover");

                }
            }

        });
    })


    //html获取链接上面的参数
    function getQueryString(name){
        var currentType = "";
        var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r!=null) currentType= r[2];
        if(currentType != ""){
            baiduMap.navLeft(parseInt(currentType));
        }
    }
    window.onload =  getQueryString("type")



</script>
<script th:inline="javascript">
    /*<![CDATA[*/
    var sessionId = [[${session.muser}]];
    $(function(){
        $("#map_agent_liaobei").click(function(){ //点击弹出聊呗
            if(sessionId != null && sessionId != "" && sessionId != undefined){
                $(".liaobeiBtn").attr("href","/imPolt/"+ baiduMap.agentMobile);
            }else{
                $("#map_liaobei_jmp").attr("data-toggle","modal");
                $("#login").show();
                $(".modal-backdrop").show();
            }
        });

        $("#loginClose").click(function () {
            $("#login").hide();
            $(".modal-backdrop").hide();
        });
        //点击黑幕关闭登录弹窗
        $("#loginzhezhao").click(function(){
            $("#login").hide();
            $(".modal-backdrop").hide();
        });
    })
    /*]]>*/
</script>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/map/js/secMapDual.js?v=20230116"></script>
<!--<script type="text/javascript" src="/js/secMapDual.js"></script>-->


<div th:include="fragment/fragment::tongji"></div>
<div th:include="house/detail/fragment_login::login"></div>
<div class="modal-backdrop  in" id="loginzhezhao" style="display: none;"></div>
</body>
<script>
    var unSelected = "#999";
    var selected = "#333";

    $("select").css("color", unSelected);
    $("option").css("color", selected);
    $("select").change(function () {
        var selItem = $(this).val();
        if (selItem == $(this).find('option:first').val()) {
            $(this).css("color", unSelected);
        } else {
            $(this).css("color", selected);
        }
    });

</script>
</html>