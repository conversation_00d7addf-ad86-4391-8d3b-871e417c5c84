<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<title th:text="'沈阳' + ${seoTitle + seoSubName} + '二手房_沈阳' + ${seoTitle + seoSubName} + '二手房出售_沈阳二手房买卖信息 - 房小二网'"></title>
		<meta name="keywords" th:content="'沈阳' + ${seoTitle + seoSubName} + '二手房,沈阳' + ${seoTitle + seoSubName} + '二手房价格,沈阳二手房信息,二手房中介,沈阳二手房网'" />
		<meta name="description" th:content="'房小二网沈阳二手房为您提供海量真实的沈阳' + ${seoTitle + seoSubName} + '二手房信息，沈阳二手房经纪人信息，二手房源信息，及时的二手房出售信息以及二手房中介信息，带来更好二手房买卖体验。'" />
		<meta http-equiv="Content-Type" content="text/html;charset=utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
		<meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang2/'+mobileAgent}">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20190806" />
<!--		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/second/default2018.css?v=20200212" />-->
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/maifang_right/default2018.css?v=20200212" />
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20190308">
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
		<script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
		<script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css" />
		<link href="https://static.fangxiaoer.com/web/styles/new_sy/house/suspendedCeiling.css" rel="stylesheet" type="text/css" />
		<script src="https://static.fangxiaoer.com/js/highcharts6.0.7.js"></script>

		<style>
			.sub{ position: absolute; top: 0px; right: 0px; z-index: 10;}/*增加广告图标*/
		</style>

	</head>
	<body>
		<style>
			img {
        -webkit-filter: saturate(1.3) brightness(102%); /* Chrome, Safari, Opera */
        filter: saturate(1.3) brightness(102%);
    }
    #scdHouseLeftList {
        border: 1px solid #eaeaea;
        background: #fff;
        float: left;
        width: 958px;
        padding-right: 0;
    }
    #left{margin-bottom: 30px}
    #right{float: right;margin-bottom: 30px}
    .scdHouseLeftListTitle{line-height: 30px;
        font-size: 16px;
        padding-left: 20px;
        font-weight: bold;
        padding-top: 10px;
        display: block;}
    .tese{padding-right: 0}
    .tese div{width: 830px}
    .tese div a{padding-left: 16px;margin-right: 19px;}
    .listIcon_subway{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_subway.png) no-repeat}
	/* 增加安心好房样式修改 */
	.sort_new {
		border-bottom: 2px solid #ff5200;
	}

	.new_label {
		padding: 0 30px;
		text-align: center;
		line-height: 45px;
		font-size: 16px;
		display: inline-block;
	}

	#sortParamNewest {
		margin: 0;

	}

	#sortParamNewest>a:hover {
		/* background: #333; */
		color: #333;
		text-decoration: none;
	}

	.screen_new {
		width: calc(100% - 40px);
		height:53px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #ededed;
		margin-left: 20px;
	}

	.new_hover {
		background: #ff5200;
		color: #fff;
	}
	.new_hover:hover{
		color: #fff !important;
	}
	.anxin_icon{
		display: flex;
		line-height: 21px;
		font-size: 13px;
		margin-left: 10px;
	}
	.anxin_icon_l{
		width:33px;
		height:21px;
		color: #fff;
		text-align: center;
		background:linear-gradient(90deg,rgba(255,144,0,1) 0%,rgba(255,82,0,1) 100%);
	}
	.anxin_icon_r{
		width: 33px;
		height: 19px;
		line-height: 19px;
		color: #ff5200;
		text-align: center;
		border: 1px solid #ff5200;
	}
	.youXuan {
		width: 24px;
		height: 24px;
		display: block;
		float: left;
		margin-left: 10px;
		background-color: #23c593;
		color: #fff;
		text-align: center;
		line-height: 22px;
		font-size: 15px;
		font-weight: 400;
		box-sizing: border-box;
	}
	/* 增加安心好房样式修改 */
</style>
		<!--<form name="form1" method="post" action="" id="form1">-->
		<div th:include="house/detail/fragment_login::login"></div>
		<div class="modal-backdrop  in" id="loginzhezhao" style="display: none; z-index: 1000000;"></div>
		<!--引入头部导航栏-->
		<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
		<!--搜索栏-->
		<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=2,listType=0"></div>

		<div class="main">
			<div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/saleHouses/" target="_blank">沈阳二手房</a>
				<th:block th:if="${isSubway == 1}">
					&gt;<a href="/saleHouses/z1">地铁沿线</a>
				</th:block>
				<th:block th:if="${isSubway == 0}" >
					<th:block th:each="r:${region}" th:if="${r.selected and r.name ne '全部'}">
							  &gt;
						<a th:href="${'/saleHouses/r' + r.id}"  th:text="${r.name + '二手房'}"></a>
						<th:block th:if="${plate}">
							<th:block th:each="j:${plate}"  th:if="${j.selected and j.name ne '全部'}">
								&gt;
								<a th:href="${'/saleHouses/r' + r.id + '-j' + j.id}"  th:text="${j.name + '二手房'}"></a>
							</th:block>
						</th:block>
					</th:block>
				</th:block>
			</div>
			<!--小区信息展示-->
			<div th:include="secondhouse/fragment::viewplotinformation"></div>
			<div id="option">
				<ul>
					<li class="fenlei">
						<p>位置：</p>
						<a id="btnRegion" th:each="r,i:${region}" onclick="showIndex(this)" th:if="${i.index eq 0 }" th:href="${r.url}"
						 class="">
							<b class="listIcon listIcon_dw"></b>区域<i></i></a>
						<a id="btnSubway" th:each="z,i:${location}" onclick="showIndex(this)" th:if="${i.index eq 1 }" th:href="${z.url}"
						 class="">
							<b class="listIcon listIcon_ditie"></b>地铁<i></i></a>
						<a href="/saleVillages/"><b class="listIcon listIcon_esf"></b>小区<i></i></a>
						<a href="/salemap"><b class="listIcon listIcon_map"></b>地图<i></i></a>
						<a th:href="${'/salemap'+'?subway'}"><b class="listIcon listIcon_subway"></b>地铁沿线<i></i></a>
					</li>
					<!--区域-->
					<li id="Search_zf" class="leibie" style="display: none">
						<a th:each="r:${region}" th:href="${r.url}" onclick="showIndex(this)" th:text="${#strings.toString(r.name).replaceAll('全部','不限')}"
						 th:id="'r'+${r.id}" th:class="${r.selected}? 'hover':''"></a><br>
						<span th:if="${plate}">
							<a th:each="j:${plate}" th:text="${#strings.toString(j.name).replaceAll('全部','不限')}" onclick="showIndex(this)"
							 th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
						</span>
					</li>
					<!--地铁-->
					<li id="Search_ditie" class="leibie" style="display: none">
						<a th:each="b:${subWayId}" th:href="${b.url}" onclick="showIndex(this)" th:text="${#strings.toString(b.name).replaceAll('全部','不限')}"
						 th:id="'r'+${b.id}" th:class="${b.selected}? 'hover':''"></a><br>
						<span th:if="${subwayStation}">
							<a th:each="q:${subwayStation}" onclick="showIndex(this)" th:text="${#strings.toString(q.name).replaceAll('全部','不限')}"
							 th:href="${q.url}" th:id="'j'+${q.id}" th:class="${q.selected}? 'hover':''"></a>
						</span>
					</li>
					<script type="text/javascript">
						$(function() {
							var r_or_s = location.pathname;
							if (r_or_s.indexOf('z1') != -1) {
								$("#btnSubway").attr("class", "hover");
								$("#Search_ditie").css('display', 'block');
							} else {
								$("#btnRegion").attr("class", "hover");
								$("#Search_zf").css('display', 'block')
							}
						})
					</script>
					<li>
						<p>总价：</p>
						<a th:each="p:${price}" th:href="${p.url}" onclick="showIndex(this)" th:text="${#strings.toString(p.name).replaceAll('全部','不限')}"
						 th:id="'p'+${p.id} " th:class="${p.selected}?'hover':''">></a>
						<div id="Search_PriceDomOk">
							<label><input name="minPrice" id="minPrice" maxlength="4" type="text" th:value="${minPrice}"> - <input name="maxPrice"
								 id="maxPrice" maxlength="4" type="text" th:value="${maxPrice}"> 万 <input onclick="__doPostBack('Search$Btn_Search1','')"
								 name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button"></label>
						</div>
					</li>
					<!--</div>-->
					<!--<div id="Search_BuildAreaDom">-->
					<li>
						<p>面积：</p>
						<a th:each="a:${area}" th:href="${a.url}" onclick="showIndex(this)" th:text="${#strings.toString(a.name).replaceAll('全部','不限')}"
						 th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
						<div id="Search_BuildAreaDomOk">
							<label><input name="minArea" id="minArea" maxlength="5" type="text" th:value="${minArea}"> - <input name="maxArea"
								 id="maxArea" maxlength="5" type="text" th:value="${maxArea}"> ㎡ <input onclick="__doPostBack('Search$Btn_Searchs','')"
								 name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button"></label>
						</div>
					</li>
					<!--</div>-->
					<!--<div id="Search_RoomDom">-->
					<li>
						<p>户型：</p>
						<a th:each="l:${room}" th:href="${l.url}" onclick="showIndex(this)" th:text="${#strings.toString(l.name).replaceAll('全部','不限')}"
						 th:id="'l'+${l.id}" th:class="${l.selected}?'hover':''">></a>
					</li>
					<!--</div>-->
					<div class="tese" style="padding: 0 20px;display: block;border: none">
						<p>特色：</p>
						<div>
							<a style="text-decoration: none;color: #425571;" th:id="${'tese'+u.id}" th:each="u,i:${feature}" th:if="${u.id ne ''}"
							 th:href="${u.url}" th:text="${u.name}" th:class="${u.selected}? 'hover':''">></a>
							<script>
								$(".tese a").click(function () {
                                var teseid = $(this).attr("id").replace("tese", "");
                                var ref = location.pathname;
                                if(ref.indexOf('saleHouses/') == -1) ref +='/';
                                ref = ref.replace(/[-]*[n][0-9]?$/, "");
                                if ($(this).attr("class") == null) {
                                    $(this).attr("href", ref + "-u" + teseid);
                                }
                                else {
                                    var outref = ref.replace("-u"+teseid,"");
                                    $(this).attr("href", outref);
                                }
                            })
                        </script>
						</div>
					</div>

				</ul>
			</div>
			<div id="option_other">
				<ul>
					<li>
						<p>更多：</p>
						<div class="select_box">
							<div class="select_info">朝向不限</div>
							<ul>
								<li th:each="f:${forward}"> <a th:href="${f.url}" onclick="showIndex(this)" th:text="${f.id eq '' ? '朝向不限':f.name}"
									 th:class="${f.selected}? 'hover':''"></a></li>
							</ul>
						</div>
						<div class="select_box">
							<div class="select_info">装修不限</div>
							<ul>
								<li th:each="d:${decoration}"> <a th:href="${d.url}" onclick="showIndex(this)" th:text="${d.id eq '' ? '装修不限':d.name}"
									 th:class="${d.selected}? 'hover':''">></a></li>
							</ul>
						</div>
						<div class="select_box">
							<div class="select_info">楼层不限</div>
							<ul>
								<li th:each="m:${floor}"> <a th:href="${m.url}" onclick="showIndex(this)" th:text="${m.id eq '' ? '楼层不限':m.name}"
									 th:class="${m.selected}?'hover'"></a></li>
							</ul>
						</div>
						<!--<div class="select_box">-->
							<!--<div class="select_info">来源不限</div>-->
							<!--<ul>-->
								<!--<li th:each="g:${memberType}">-->
									<!--<a th:href="${g.url}" th:if="${g.id eq ''}" onclick="showIndex(this)" th:text="来源不限" th:class="${g.selected}? 'hover':''">-->
									<!--</a>-->
									<!--<a th:href="${g.url}" th:if="${g.name eq '个人'}" onclick="showIndex(this)" th:text="个人" th:class="${g.selected}? 'hover':''">-->
									<!--</a>-->
									<!--<a th:href="${g.url}" th:if="${g.name eq '经纪人'}" onclick="showIndex(this)" th:text="经纪人" th:class="${g.selected}? 'hover':''">-->
									<!--</a>-->
								<!--</li>-->
							<!--</ul>-->
						<!--</div>-->
						<!--<div class="select_box ppbx">-->
						<!--<div class="select_info">品牌不限</div>-->
						<!--<ul>-->
						<!--&lt;!&ndash;力创商业地产(id=10)暂无二手房相关业务因此隐藏(2018.08.30)&ndash;&gt;-->
						<!--<li th:each="i:${intermediary}" th:if="${i.id ne '10'}">-->
						<!--<a th:href="${i.url}" onclick="showIndex(this)"  th:text="${i.id eq '' ? '品牌不限' : i.name}" th:class="${i.selected}? 'hover':''">-->
						<!--</a>-->

						<!--</li>-->
						<!--</ul>-->
						<!--</div>-->
					</li>
				</ul>
			</div>

			<div id="option_info" style="display: none">
				<input type="hidden" id="houseName" th:value="${sub?.title}" />
				<b>已选：</b>
				<!--小区名-->
				<div th:if="${!#strings.isEmpty(subId) and !#strings.isEmpty(sub?.title)}">
					<span class="condition">
						<th:block th:text="${sub.title}"></th:block>
					</span>
					<i class="cleanUrl" id="clearSubName" th:value="${'-v'+subId}"></i>
				</div>
				<!--头部搜索-->
				<div th:if="${!#strings.isEmpty(searchKey) and !#strings.isEmpty(searchKey)}">
					<span class="condition">
						<th:block th:text="${searchKey}"></th:block>
					</span>
					<i class="cleanUrl" id="clearSearchKey" th:value="${searchKey}"></i>
				</div>
				<script>
					$("#clearSubName").click(function() {
						var nowUrl = location.pathname;
						var clrerUrl = '[[${subId}]]';
						var newUrl = nowUrl.replace("-v" + clrerUrl, "");
						window.location.href = newUrl;
					});
					//清除头部搜索key
					$("#clearSearchKey").click(function() {
						var nowUrl = location.pathname;
						var searchTitle = nowUrl.indexOf('search'); //只有手输内容搜索
						var newUrl = nowUrl.substring(0, searchTitle);
						window.location.href = newUrl;
					});

					var houseName = $("#houseName").val();
					if (houseName != "" || houseName != undefined || houseName != null) {
						if (!$("#txtkeys").val()) {
							$("#txtkeys").val(houseName);
						}
					}
				</script>

				<!--区域-->
				<div th:each="r:${region}" th:if="${r.selected and !#strings.isEmpty(r.id)}" th:onclick="${'removeSelect('+1+')'}">
					<span class="condition">
						<th:block th:text="${r.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--板块-->
				<div th:each="p:${plate}" th:if="${p.selected and  !#strings.isEmpty(p.id)}" th:onclick="${'removeSpan('+1+')'}">
					<span class="condition">
						<th:block th:text="${p.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--地铁线-->
				<div th:each="s:${subway}" th:if="${s.selected and  !#strings.isEmpty(s.id)}" th:onclick="${'removeSelect('+1+')'}">
					<span class="condition">
						<th:block th:text="${s.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--地铁站-->
				<div th:each="s:${subwayStation}" th:if="${s.selected and  !#strings.isEmpty(s.id)}" th:onclick="${'removeSpan('+1+')'}">
					<span class="condition">
						<th:block th:text="${s.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--价格-->
				<div th:each="p:${price}" th:if="${p.selected and !#strings.isEmpty(p.id)}" th:onclick="${'removeSelect('+3+')'}">
					<span class="condition">
						<th:block th:text="${p.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--面积-->
				<div th:each="p:${area}" th:if="${p.selected and !#strings.isEmpty(p.id)}" th:onclick="${'removeSelect('+4+')'}">
					<span class="condition">
						<th:block th:text="${p.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--户型-->
				<div th:each="l:${room}" th:if="${l.selected and !#strings.isEmpty(l.id)}" th:onclick="${'removeSelect('+5+')'}">
					<span class="condition">
						<th:block th:text="${l.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--朝向-->
				<div th:each="d:${forward}" th:if="${d.selected and !#strings.isEmpty(d.id)}" th:onclick="${'removeSelectClick('+0+')'}">
					<span class="condition">
						<th:block th:text="${d.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--装修-->
				<div th:each="d:${decoration}" th:if="${d.selected and !#strings.isEmpty(d.id)}" th:onclick="${'removeSelectClick('+1+')'}">
					<span class="condition">
						<th:block th:text="${d.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--楼层-->
				<div th:each="d:${floor}" th:if="${d.selected and !#strings.isEmpty(d.id)}" th:onclick="${'removeSelectClick('+2+')'}">
					<span class="condition">
						<th:block th:text="${d.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--来源-->
				<div th:each="g:${memberType}" th:if="${g.selected and !#strings.isEmpty(g.id)}" th:onclick="${'removeSelectClick('+3+')'}">
					<span class="condition">
						<th:block th:text="${g.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--品牌-->
				<div th:each="i:${intermediary}" th:if="${i.selected and !#strings.isEmpty(i.id)}" th:onclick="${'removeSelectClick('+4+')'}">
					<span class="condition">
						<th:block th:text="${i.name}"></th:block>
					</span>
					<i class="cleanUrl"></i>
				</div>
				<!--<li th:each="i:${intermediary}">-->
				<!--<a th:href="${i.url}" onclick="showIndex(this)" th:text="${i.id eq '' ? '品牌不限' : i.name}" th:class="${i.selected}? 'hover':''">-->
				<!--</a>-->

			</div>

			<script th:inline="javascript">
				$(".smxqi a").mouseover(function () {
					$(this).css({"color":"#6BA2F3","text-decoration":"none"})
				})
				$(".smxqi a").mouseout(function () {
					$(this).css({"color":"#6BA2F3","text-decoration":"none"})
				})
				$(".smbtc").mouseover(function () {
					$(this).css({"color":"#fff","text-decoration":"none"})
				})
				$(".smbtc").mouseout(function () {
					$(this).css({"color":"#fff","text-decoration":"none"})
				})

				//电话咨询
				$(".sdhx").mouseover(function () {
					$(".show-CardTels").show()
				})
				$(".sdhx").mouseout(function () {
					$(".show-CardTels").hide()
				})



				$(function() {
					//条件区域加载渲染
					if ($(".condition").text() != "") {
						$("#option_info").css("display", "block");
						// $(".sdd_m").show()
					}else{
						// $(".sdd_m").hide()
					}
					loadCondition();
				});

				function loadCondition() {
					var isAdd = false;
					//非手填项渲染
					selectPrice(isAdd);
					//清空全部按钮
					if ($(".condition").text() != "") {
						$("#option_info").append("<a>清空筛选条件</a> ");
						$("#option_info a:last").attr("href", "/saleHouses/").attr("class", "clean");
					}
				}

				function removeSelect(index) {
					$("#option ul li:eq(" + index + ") a:eq(0)").click();
				}

				function removeSelectClick(index) {
					$('.select_box:eq(' + index + ') a:eq(0)').click();
				}

				function removeSpan(index) {
					$("#option ul li:eq(" + index + ") span a:eq(0)").click();
				}

				function showIndex(obj) {
					obj.click();
				}

				function selectPrice(isAdd) {
					//总价、面积筛选条件渲染
					var bp = $("#option input[id='minPrice']").val();
					var ep = $("#option input[id='maxPrice']").val();
					var ba = $("#option input[id='minArea']").val();
					var ea = $("#option input[id='maxArea']").val();
					if (bp != "" || ep != "") {
						if (!isAdd) {
							$("#option_info").show();
							isAdd = true;
						}
						var str = "";
						bp = bp == "" ? 0 : bp;
						if (ep == "") {
							str = "大于等于" + bp;
						} else {
							if (bp == "") {
								$("#option input[id='bp']").val("0");
							}
							str = bp + "-" + ep;
						}
						$("#option_info").append("<div><span class='condition'>" + str + "万元</span><i></i> </div>");
						$("#option_info div:last").attr("onclick", "removeSelect(" + 3 + ");");
					}
					if (ba != "" || ea != "") {
						if (!isAdd) {
							$("#option_info").show();
							isAdd = true;
						}
						var str = "";
						ba = ba == "" ? 0 : ba;
						if (ea == "") {
							str = "大于等于" + ba;
						} else {
							if (ba == "") {
								$("#option input[id='ba']").val("0");
							}
							str = ba + "-" + ea;
						}
						$("#option_info").append("<div><span class='condition'>" + str + "㎡</span><i></i> </div>");
						$("#option_info div:last").attr("onclick", "removeSelect(" + 4 + ");");
					}
				}
			</script>
			<div class="cl"></div>
			<div id="Search_PanSaleDom">

				<div class="search_more hid">
					<ul>
						<li>
							<p>更多找房条件：</p>
							<div class="select_box">
								<div class="select_info">朝向不限</div>

								<ul>

								</ul>
							</div>
							<div class="select_box">
								<div class="select_info">装修不限</div>
								<ul>

								</ul>
							</div>
							<div class="select_box">
								<div class="select_info">楼层不限</div>
								<ul>

								</ul>
							</div>
						</li>

					</ul>
				</div>
				<script>
					var i = 0;
					for (i = 0; i < $("#Search_PanSaleDom .select_info").length; i++) {
						if ($("#Search_PanSaleDom .select_box").eq(i).find(".hover").length > 0) {
							$("#Search_PanSaleDom .select_info").eq(i).text($("#Search_PanSaleDom .select_box").eq(i).find(".hover").text())

						}
					}
				</script>
				<script type="text/javascript">
					function price(priceIdName) {
						$("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
						$("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
						var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
						var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
						if (num1 == "" && num2 != "") {
							$("#" + priceIdName + " input").eq(0).val("0");
							$("#" + priceIdName + " input").eq(2).show();
						} else if (num2 == "" && num1 != "") {
							$("#" + priceIdName + " input").eq(2).show();
						} else if (num1 != "" || num2 != "") {
							$("#" + priceIdName + " input").eq(2).show();
						} else {
							$("#" + priceIdName + " input").eq(2).hide();
						}
					}

					price("Search_BuildAreaDomOk");
					price("Search_PriceDomOk");
					$("#Search_PriceDomOk input").keyup(function() {
						price("Search_PriceDomOk");
					})
					$("#Search_BuildAreaDomOk input").keyup(function() {
						price("Search_BuildAreaDomOk");
					})
					$("#Search_PriceDomOk").keydown(function(event) {
						if (event.keyCode == 13) {
							// $("#Search_Btn_Search1").click()
						}
					});
					$("#Search_BuildAreaDomOk").keydown(function(event) {
						if (event.keyCode == 13) {
							$("#Search_Btn_Searchs").click()
						}
					});
				</script>

			</div>

			<div id="main">

				<div id="left" class="longLeft" style="border:none">
					<!--列表页新增广告-->
					<div th:include="secondhouse/fragment::advert_recommand"></div>
					<div class="secNewLeft" style="border: 1px solid #ededed">
						<div class="sort sort_new">
							<!-- 建立一个新的查询条件区域 -->
							<!-- // 增加安心好房/修改样式后 -->
							<p id="sortParamNewest">
								<a id="2" href="" class="new_label sortParamNewA">全部</a>
								<!--<a id="1" href="" class="new_label sortParamNewA">真房源</a>-->
								<!--<a id="-1" href="" class="new_label sortParamNewA">独家房源</a>-->
								<!--<a th:if="${#strings.equals(forAnxuan, '1')}" id="9" href="" class="new_label sortParamNewA">安心好房</a>-->
								<!--<a id="8" href="" class="new_label sortParamNewA">新房房源</a>-->
								<a id="6" href="" class="new_label sortParamNewA">最新房源</a>
								<!--<a id="4" href="" class="new_label sortParamNewA">视频看房</a>-->
								<!--<a href="/dealSales/" class="new_label dealCodition">成交房源</a>-->
								<script>
									// <!-- 判断字符串末尾是否是给定内容的方法 -->
									String.prototype.endWith = function(endStr) {
										var d = this.length - endStr.length;
										return (d >= 0 && this.lastIndexOf(endStr) == d);
									}
									// 增加安心好房/修改样式后
									$(document).ready(function() {
										var ref = location.pathname;
										if (ref.endWith("/saleHouses")) {
											ref = ref + '/';
										}
										var y = ref.split('/saleHouses/');
										var x = y.length > 1 ? y[1] : "";
										if(x.indexOf("co") != -1){
                                            $("#sortParamNewest a[id ='-1']").addClass("new_hover");
										}else if (x.indexOf('W') != -1 || x.indexOf('S') != -1) {
										    if(x.indexOf('S') != -1){
                                                $("#sortParamNewest a[id ='9']").addClass("new_hover");
											}else{
                                                var num = x.split('W')[1].substring(0, 1);
                                                if (num == 2 || num == 4 || num == 6 || num == 8 || num == 3 || num == 1) {
                                                    $("#sortParamNewest a[id ='" + num + "']").addClass("new_hover");
                                                } else {
                                                    if (num % 2 == 1) {
                                                        $("#sortParamNewest a[id ='" + num + "']").addClass("new_hover");
                                                        $("#sortParamNewest a[id ='" + num + "']").attr("id", num - 1);
                                                    } else {
                                                        num = parseInt(num) + 1;
                                                        $("#sortParamNewest a[id ='" + num + "']").addClass("new_hover");
                                                    }
                                                }

                                            }
										} else {
											$("#sortParamNewest a:eq(0)").addClass("new_hover");
										}
										var ref2 = ref.replace(/-W[0-9]/, '').replace(/-n[0-9]/, '').replace(/-N[0-9]/, '').replace(/-S[0-9]/, '').replace(/-u200/, '').replace(/-co[0-9]/, '');
										var ref3 = '';
										$("#sortParamNewest a.sortParamNewA").each(function() {
											var ids = $(this).attr("id");
											if(ids == 9){
                                                ref3 = ref2 + '-S8';
											} else if (ids == 1){
											    ref3 = ref2 + '-W' + ids + '-u200';
											} else if (ids == 2){
											    ref3 = '/saleHouses/'
											} else if (ids == -1){
                                                ref3 = ref2 + '-co1';
											} else{
                                                ref3 = ref2 + '-W' + ids;
											}
											$(this).attr("href", ref3);
										});
									});
								</script>
							</p>
						</div>
						<div class="screen_new">
							<div style="margin-left: 10px;font-size: 14px;">
								为您找到以下<span style="color: #FF5200;">沈阳</span>二手房
							</div>
							<p id="screening_Items">
								<a id="0" href="" class="hover">综合排序</a>
								<a id="3" href="">总价</a>
								<a id="7" href="">单价</a>
								<a id="5" href="">面积</a>
								&nbsp;
								<script>
									$(document).ready(function() {
										var ref = location.pathname;
										if (ref.indexOf('saleHouses/') == -1) ref += '/';
										var y = ref.split('/saleHouses/');
										var x = y.length > 1 ? y[1] : "";
										if (x.indexOf('o') != -1) {
											var num = x.split('o')[1].substring(0, 1);
											if (num == 0 || num == 1) {
												$("#screening_Items a").removeClass("hover");
												$("#screening_Items a[id ='" + num + "']").addClass("hover");
											} else {
												if (num % 2 == 1) {
													$("#screening_Items a").removeClass("hover");
													$("#screening_Items a[id ='" + num + "']").addClass("sort_jg");
													$("#screening_Items a[id ='" + num + "']").addClass("down");
													$("#screening_Items a[id ='" + num + "']").attr("id", num - 1);
												} else {
													num = parseInt(num) + 1;
													$("#screening_Items a[id ='" + num + "']").addClass("sort_jg");
													$("#screening_Items a[id ='" + num + "']").addClass("up");
													$("#screening_Items a").removeClass("hover");
												}
											}
										} else {
											$("#screening_Items a:eq(0)").addClass("hover");
										}
										var ref2 = ref.replace(/-o[0-9]/, '');
										$("#screening_Items a").each(function() {
											var ids = $(this).attr("id");
											var ref3 = ref2 + '-o' + ids;
											$(this).attr("href", ref3);
										});
									});
								</script>
							</p>
						</div>
						<!--                <p class="wantbuy"><a href="/needSeconds/"><i class="wantbuyIcon"></i>求购信息</a></p>-->
						<!-- <a href="https://event.fangxiaoer.com/20190824.htm" target="_blank" class="listBannerSchool">
			                        <img src="https://static.fangxiaoer.com/web/images/sy/house/listBannerSchool2.gif" alt="">
			                    </a>-->
						<script type="text/javascript">
							$(function() {
								var p_or_l = location.pathname;
								if (p_or_l.indexOf("h1") == -1) {
									$("#replie").show();
									$("#reprow").hide();
									$("#piece").attr("class", "");
									$("#line").attr("class", "hover");
								} else {
									$("#replie").hide();
									$("#reprow").show();
									$("#piece").attr("class", "hover");
									$("#line").attr("class", "");
									$("#esfListEwmBanner").hide()
								}
							})
							//                    $("#line").click(function () {
							function lineShow() {
								var url = location.pathname;
								if (url.indexOf("-h1") != -1) {
									$("#line").attr("href", url.replace("-h1", ""));
								} else {
									$("#line").attr("href", url.replace("h1", ""));
								}
								//                    })
							}
						</script>
						<div class="cl"></div>
						<!--                <div class="content">-->
						<!--                    <span>小二为你找到<i th:text="${msg}"></i>个符合条件的房源</span>-->
						<!--                    <p id="sort_qh">-->
						<!--                        <a id="line"  href=''  class='' onclick="lineShow();"></a>-->
						<!--                        <a id="piece" th:each="h,i:${piece}" th:if="${i.index eq 1 }" th:href="${h.url}"    class=''></a>-->
						<!--                    </p>-->
						<!--                   </div>-->

						<div class="contentMain" id="saleCol">
							<div class="warning" th:if="${#lists.isEmpty(secondhouse)}">
								<p>
									很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
									懒得搜索？！<a th:href="@{'/helpSearch?ids=2'}" rel="2" dir="1" target="_blank">点击免费发布购房服务方案>></a>
								</p>
							</div>

							<div class="cl"></div>
							<!--以下为list形式-->
							<div id=replie class="house_left">
								<th:block th:each="sh,sequence:${secondhouse}">
									<div class="inf" th:onclick="${'window.open(''/salehouse/'+sh.houseId+'.htm'')'}">
										<a th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank"></a>
										<a th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank" class="infLeft">
											<!--<i  th:if="${sh.picNum ne null and #strings.toString(sh.picNum).length() gt 1 or sh.picNum gt '4'}" th:text="${sh.picNum}"></i>-->
											<!--加广告图标-->
											<span class="sub" th:if="${#strings.toString(sh.auction) eq '1'}"><img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></span>
											<span class="sub" th:if="${#strings.toString(sh.auction) ne '1' && #strings.toString(sh.stickOrder) eq '-1'}"><img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" /></span>

											<img th:src="${#strings.isEmpty(sh.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sh.pic}"
											 th:alt="${sh.title}" />
											<!--                                <div class="" th:if="${sh.isXiQue eq '1'}">佣金95折</div>-->
											<!--VR and 视频都存在 -->
											<s class="listIconK" th:if="${#strings.toString(sh.PanID) ne null and #strings.toString(sh.mediaID) ne null }">
												<s class="vrListIcon" th:if="${#strings.toString(sh.PanID) ne null}"></s>
												<s class="videoListIcon" th:if="${sh.mediaID ne null}"></s>
											</s>
											<!--VR存在 -->
											<s class="listIconK" th:if="${#strings.toString(sh.PanID) ne null and #strings.toString(sh.mediaID) eq null }">
												<s class="vrListIcon" th:if="${#strings.toString(sh.PanID) ne null}"></s>
											</s>
											<!--视频存在 -->
											<s class="listIconK" th:if="${#strings.toString(sh.PanID) eq null and #strings.toString(sh.mediaID) ne null }">
												<s class="videoListIcon" th:if="${sh.mediaID ne null}"></s>
											</s>
										</a>
										<div class="infCtn">
											<a class="newHouseListTitle" th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank">
												<!--真实房源-->
												<div style="margin-top: 2px; margin-right: 8px" th:if="${sh.cooperationState eq null and #maps.containsKey(sh,'realEstateStatus') and #strings.equals(sh?.realEstateStatus,'100')}">
													<img src="https://static.fangxiaoer.com/web/images/sy/download/zxzs_icon.jpg" alt="">
												</div>
												<div style="margin-top: 2px; margin-right: 8px" th:if="${sh.cooperationState ne null}">
													<img src="https://static.fangxiaoer.com/web/images/sy/download/zxzs_icon.jpg" alt="">
												</div>
												<div th:text="${sh.title}" th:title="${sh.title}"></div>
												<!--<div th:if="${#strings.isEmpty(searchKey)}" th:text="${sh.title}"></div>
                                    <div th:if="${!#strings.isEmpty(searchKey)}"  th:utext="${#strings.replace(sh.title,#strings.toString(searchKey),'<s>jjjjjjj</s>')}"></div>-->
												<!-- 增加安心好房icon -->
												<div class="anxin_icon" th:if="${#strings.equals(sh.anXuan,'-1')}">
													<div class="anxin_icon_l">安心</div>
													<div class="anxin_icon_r">好房</div>
												</div>

												<i class="listIconBidPrice" th:if="${#strings.toString(sh.auction) eq '1'}"></i>
												<i class="listIconIstop" th:if="${#strings.toString(sh.auction) ne '1' && #strings.toString(sh.stickOrder) eq '-1'}"></i>
												<i class="youXuan" th:if="${#strings.equals(sh.youXuan, '-1')}">优</i>
											</a>
											<div class="fourSpan">
												<span>
													<th:block th:text="${sh.room+'室'+sh.hall+'厅'+sh.toilet+'卫'}"></th:block>
												</span>
												<span>
													<th:block th:text="${#strings.toString(sh.area).contains('.')? #strings.toString(sh.area).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.area}+'m²'"></th:block>
												</span>
												<span th:if="${sh.totalFloorNumber ne null and sh.totalFloorNumber ne '' }" th:text="${sh.floorDesc+'/'+sh.totalFloorNumber+'层'}">
												</span>
												<span>
													<th:block th:text="${sh.forward}"></th:block>
												</span>
												<span th:if="${sh.buildDate ne null and sh.buildDate ne ''}">
													<th:block th:text="${sh.buildDate+'年建筑'}"></th:block>
												</span>

											</div>
											<p class="houseAddress" th:if="${!#strings.isEmpty(sh.regionName) and !#strings.isEmpty(sh.subName) and !#strings.isEmpty(sh.plantName) and !#strings.isEmpty(sh.address)}">
												<s th:if="${ !#strings.isEmpty(sh.subName)}" class="houseAddressSpance">
													<a th:href="${'/saleVillages/'+sh.subID+'/index.htm'}" target='_blank' th:text="${sh.subName}"></a>
												</s>
												<i class="gang"></i>
												<s th:if="${!#strings.isEmpty(sh.regionName) and !#strings.isEmpty(sh.plantName) and !#strings.isEmpty(sh.address)}">
													<a th:href="${'/saleHouses/r'+sh.regionId}" th:text="${sh.regionName}"></a>-
													<a th:href="${'/saleHouses/j'+sh.PlatId+'-r'+sh.regionId}" th:text="${sh.plantName}"></a>-
													<th:block th:text="${sh.address}"></th:block>
												</s>
											</p>
											<div class="bottomtext">
												<div class="houseItemIcon">
													<div class="isGoodHouseIcon" th:if="${sh.isGoodHouse eq '-1'}"></div>
													<th:block th:if="${sh.houseTrait ne null and sh.houseTrait ne ''}">
														<span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(sh.houseTrait).split(',')}" th:if="${i.count le 3}"
														 th:text="${item}"></span>
													</th:block>
												</div>
												<span class="personShow" th:if="${sh.cooperationState eq null}">
													<div th:if="${sh.memberType eq '经纪人'}">
														<!--                                            <i class="personIcon"></i>-->
														<div class="agentShowImg"><img th:if="${!#strings.isEmpty(sh.Avatar)}" th:src="${sh.Avatar}" alt=""></div>
														<div class="agentShowImg"><img th:if="${#strings.isEmpty(sh.Avatar)}" src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png"
															 alt=""></div>
														<i>
															<th:block th:text="${sh.agency}"></th:block>
															<th:block th:if="${!#strings.isEmpty(sh.spanTime)}" th:text="${sh.spanTime+'更新'}"></th:block>
														</i>
														<i>
															<th:block th:if="${!#strings.isEmpty(sh.IntermediaryName)}" th:text="${sh.IntermediaryName}"></th:block>
														</i>
														<!-- 安心经纪人标识 -->
														<i th:if="${#strings.equals(sh.anXuan,'-1')}">
															<img src="https://static.fangxiaoer.com/web/images/sy/anxin/renzheng_iocn.png" style="width: 14.6px;height: 14.6px;">
														</i>
													</div>
													<div th:if="${sh.memberType eq '个人'}">
														<img src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
														<i>
															<th:block th:text="${sh.memberType}"></th:block>
														</i>
													</div>
													<!-- <div th:style="${sh.memberType eq '个人'}?'display:block':'display:none'">
                                         <span style="background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale/iconAuthentichouse.gif) no-repeat 9px 3px; padding-left: 76px; font-size: 16px; color: #E04545; position: absolute; left: 260px; bottom: 2px;"></span>
                                     </div>-->
												</span>
												<span  class="personShow" th:unless="${sh.cooperationState eq null}">
													<div>
														<img src="/img/icon_sole.png" alt="" class="sole-icon">
														<i>独家房源</i>
													</div>
												</span>
											</div>
										</div>
										<div class="infRight">
											<p class="infRightPriseM" th:if="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')}"
											 th:text='面议'></p>
											<p class="infRightPrise" th:if="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}">
												<th:block th:text="${#strings.toString(sh.price).contains('.')? #strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.price}"></th:block>
												<i th:text="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')?'':'万'}"></i>
											</p>
											<p th:if="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}" th:text="${#strings.toString(sh.unitPrice).contains('.')?#strings.toString(sh.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):sh.unitPrice}+'元/m²'"
											 th:style="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}?'display:block':'display:none'">
											</p>
											<!-- <em th:if="${!#strings.isEmpty(sh.subID) and #strings.toString(sh.subID) ne '0'}">
                                <a  th:if="${!#strings.isEmpty(sh.hasCJ) and #strings.toString(sh.hasCJ) eq '1'}" class="checkHouse" target="_blank" th:href="'/dealSales/-v'+${sh.subID}">查看同小区成交房源&gt;</a>
                                <a class="checkHouse" target="_blank" th:href="'/saleHouses/-v'+${sh.subID}">查看同小区房源&gt;</a>
                                </em>-->
										</div>
									</div>


									<!-- 需要判断显示内容 -->
									<div class="inf" th:if="${sequence.index eq 6}" id="esfListEwmBanner">
										<div id="brandpic" class="esfListEwmBanner" style="background-color: #fff">
											<img src="https://static.fangxiaoer.com/web/images/sy/sale/list/esfListEwmBanner.png" alt="">
											<h4>扫描下载房小二app</h4>
											<p>好房为你而选，让买房更简单！</p>
											<i><img src="https://static.fangxiaoer.com/web/images/sy/sale/list/esfListClose.png" alt="" class="esfListClose"></i>
										</div>
									</div>
								</th:block>
								<script>
									$("#esfListEwmBanner").hide();
									$(".esfListClose").click(function() {
										$("#esfListEwmBanner").hide()
									})
								</script>

							</div>
							<!--  横版展示
                    <div id=reprow class="list">
                        <div class="house" th:each="sh:${secondhouse}">
                            <a class="house" th:href="${'/salehouse/'+sh.houseId+'.htm'}"  target="_blank">

                                <div  class="ico">
                                    <span class="ico_yongjin " th:if="${sh.isXiQue eq '1'}">佣金95折</span>
                                    <span class="ico_zdbg" th:if="${sh.stickOrder eq '-1'}"></span>
                                    <span class="jing" th:if="${#strings.toString(sh.auction) eq '1'}"></span>
                                    <span class="is_good_house" th:if="${#strings.toString(sh.isGoodHouse) eq '-1'}">优质好房</span>
                                    <s class="videoIcon videoIcon2" th:if="${sh.mediaID ne null}"></s>
                                </div>
                                <img th:src="${#strings.isEmpty(sh.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sh.pic}" th:alt="${sh.title}" />
                                <p>
                                    <span th:if="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')}"  th:text='面议'></span>
                                    <span th:if= "${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}" >
                                        <th:block th:text="${#strings.toString(sh.price).contains('.')? #strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.price}"></th:block>
                                        <i th:text="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')?'':'万'}"></i>
                                    </span>
                                    <th:block th:text="${sh.room+'室'+sh.hall+'厅'}"></th:block>
                                </p>
                                <p><th:block th:text="${#strings.abbreviate(sh.title,28)}"></th:block></p>
                                <p><span th:text="${sh.regionName}"></span> <th:block th:text="${sh.subName}"></th:block></p>
                            </a>
                        </div>
                    </div>
                -->
							<div class="cl"></div>
							<!-- <script type="text/javascript">
                       var ary = location.href.split("&");
                       jQuery(".picScroll-left").slide({ titCell: ".hd ul", mainCell: ".bd ul", autoPage: true, effect: "leftLoop", autoPlay: true, scroll: 1, vis: 5 });
                       $(function () {
                           $(".bd li").mouseover(function () {
                               $(this).find("p").stop()
                               $(this).find("p").animate({ bottom: '0px' }, 300)
                           })
                           $(".bd li").mouseout(function () {
                               $(this).find("p").stop()
                               $(this).find("p").animate({ bottom: '-200px' }, 300)
                           })
                       })
                   </script>-->
						</div>
						<div class="cl"></div>
						<div class="page">

							<!-- AspNetPager V7.2 for VS2005 & VS2008  Copyright:2003-2008 Webdiyer (www.webdiyer.com) -->
							<div th:include="fragment/page :: page"></div>
							<!-- AspNetPager V7.2 for VS2005 & VS2008 End -->

						</div>
					</div>
				</div>
				<div id="right" class="shortRight">
					<div th:if="${!#lists.isEmpty(advert.topRight)}">
						<th:block th:if="${i.count eq 1}" th:each="company,i:${advert.topRight}">
							<a th:if="${#strings.toString(company.url).indexOf('http') ne -1}" th:href="${company.url}" target="_blank">
								<img th:src="${company.image}" th:alt="${company.projectName}" style="display: block;width: 170px;height: 110px;margin-bottom: 20px;" />
							</a>
							<img th:if="${#strings.toString(company.url).indexOf('http') eq -1}"  th:src="${company.image}" th:alt="${company.projectName}"style="display: block;width: 170px;height: 110px;margin-bottom: 20px;" />
						</th:block>
					</div>
					<!--小区专家-->
					<div th:include="secondhouse/fragment::plotexpert"></div>



<!--					<a id="house_price_address" target="_blank"></a>-->
<!--					<div class="areaChartimg"></div>-->
					<!--<a id="hightCharts_jump" target="_blank">
						<div id="areaHighCharts" style="max-width:180px;height:110px;margin:0 auto"></div>
					</a>-->
					<!--列表页右侧我要卖房、房价评估-->
					<!--                <div th:include="secondhouse/fragment::rightselling"></div>-->
                    <!--房产快搜-->
                    <!--<div class="gksou">-->
						<!--<div class="gktp"><span></span>房产快搜 <a href="/fastSeek" target="_blank">详情>></a></div>-->
						<!--<div class="gkmn">-->
							<!--<a href="https://sy.fangxiaoer.com/news/94587.htm" target="_blank"><i>1</i><em>房产过户</em></a>-->
							<!--<a href="https://sy.fangxiaoer.com/news/94586.htm" target="_blank"><i>2</i><em>购房流程</em></a>-->
							<!--<a href="https://sy.fangxiaoer.com/news/94572.htm" target="_blank"><i>3</i><em>征信查询</em></a>-->
							<!--&lt;!&ndash;<a href="/fastSeek#edu" target="_blank"><i>4</i><em>沈阳推荐小学</em></a>-->
							<!--<a href="/fastSeek#edu" target="_blank"><i>5</i><em>沈阳推荐中学</em></a>&ndash;&gt;-->
						<!--</div>-->
					<!--</div>-->
					<div>
						<div class="rightQzQgBtn">
							<a th:href="@{'/helpSearch?ids=2'}" rel="2" dir="1" target="_blank"><i class="rightQzQgBtn-icon2"></i>我要买房</a>
							<a href="/static/saleHouse/saleHouse.htm" target="_blank"><i class="rightQzQgBtn-icon1"></i>我要卖房</a>
						</div>
					</div>
					<!--右侧求购-->
					<!--                <div th:include="secondhouse/fragment::rightwantbuy"></div>-->
					<!--二手房推荐-->
					<!--                <div th:include="secondhouse/fragment::secondrecommend"></div>-->
					<!--画中画广告-->
					<div th:include="secondhouse/fragment::pictureadvert"></div>

					<!--地图指示-->
					<a th:href="${'/salemap'+'?subway'}">
						<div class="boomMap top"><img src="https://static.fangxiaoer.com/web/images/sy/sale/boomMapIcon.png" alt=""></div>
					</a>
					<!--列表页右侧中介广告-->
					<div th:include="secondhouse/fragment::companyadvert"></div>
				</div>
				<!--列表为空时推荐房源-->
				<div th:if="${(!#lists.isEmpty(sechouseList) and #lists.isEmpty(secondhouse)) || (!#lists.isEmpty(sechouseList) and #lists.size(secondhouse) lt 60)}"
				 id="scdHouseLeftList" class="longLeft">
					<span class="scdHouseLeftListTitle">房源推荐</span>
					<div id="scdHouseLeft" class="house_left">
						<div th:each="sh,sequence:${sechouseList}">
							<div class="inf" th:onclick="${'window.open(''/salehouse/'+sh.houseId+'.htm'')'}">
								<a th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank">
									<!--<img  th:if="${sh.stickOrder eq '-1'}"  style="position: absolute;top: 0px;right: 16px" src='https://static.fangxiaoer.com/web/images/ico/sign/list_v.png'/>-->
								</a>
								<a th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank" class="infLeft">
									<!--<i  th:if="${sh.picNum ne null and #strings.toString(sh.picNum).length() gt 1 or sh.picNum gt '4'}" th:text="${sh.picNum}"></i>-->
									<img th:src="${#strings.isEmpty(sh.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sh.pic}"
									 th:alt="${sh.title}" />
									<!--                                <s class="videoIcon" th:if="${sh.mediaID ne null}"></s>-->
									<!--VR and 视频都存在 -->
									<s class="listIconK" th:if="${#strings.toString(sh.PanID) ne null and #strings.toString(sh.mediaID) ne null }">
										<s class="vrListIcon" th:if="${#strings.toString(sh.PanID) ne null}"></s>
										<s class="videoListIcon" th:if="${sh.mediaID ne null}"></s>
									</s>
									<!--VR存在 -->
									<s class="listIconK" th:if="${#strings.toString(sh.PanID) ne null and #strings.toString(sh.mediaID) eq null }">
										<s class="vrListIcon" th:if="${#strings.toString(sh.PanID) ne null}"></s>
									</s>
									<!--视频存在 -->
									<s class="listIconK" th:if="${#strings.toString(sh.PanID) eq null and #strings.toString(sh.mediaID) ne null }">
										<s class="videoListIcon" th:if="${sh.mediaID ne null}"></s>
									</s>
								</a>
								<div class="infCtn">
									<a class="newHouseListTitle" th:href="${'/salehouse/'+sh.houseId+'.htm'}" target="_blank">
										<em th:text="${sh.title}" th:title="${sh.title}"></em>
										<!-- 增加安心好房icon -->
										<div class="anxin_icon" style="float: right;" th:if="${#strings.equals(sh.anXuan,'-1')}">
											<div class="anxin_icon_l">安心</div>
											<div class="anxin_icon_r">好房</div>
										</div>
									</a>
									<i class="youXuan" th:if="${#strings.equals(sh.youXuan, '-1')}">精</i>
									<!--<span class="jing" th:if="${#strings.toString(sh.auction) eq '1'}">精</span>-->
									<!--<div class="isGoodHouse" th:if="${sh.isGoodHouse eq '-1'}" style="margin: -2px 0 0 10px">优质好房</div>-->
									<div class="fourSpan">
										<span th:text="${sh.room+'室'+sh.hall+'厅'+sh.toilet+'卫'}"></span>
										<span th:text="${#strings.toString(sh.area).contains('.')? #strings.toString(sh.area).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.area}+'m²'"></span>
										<span th:if="${sh.totalFloorNumber ne null and sh.totalFloorNumber ne '' }" th:text="${sh.floorDesc+'/'+sh.totalFloorNumber+'层'}"></span>
										<span th:text="${sh.forward}"></span>
										<span th:if="${sh.buildDate ne null and sh.buildDate ne ''}">
											<th:block th:text="${sh.buildDate+'年建筑'}"></th:block>
										</span>

									</div>
									<p class="houseAddress" th:if="${!#strings.isEmpty(sh.regionName) and !#strings.isEmpty(sh.subName) and !#strings.isEmpty(sh.plantName) and !#strings.isEmpty(sh.address)}">
										<s th:if="${ !#strings.isEmpty(sh.subName)}" class="houseAddressSpance">
											<a th:href="${'/saleVillages/'+sh.subID+'/index.htm'}" target='_blank' th:text="${sh.subName}"></a>
										</s>
										<s th:if="${!#strings.isEmpty(sh.regionName) and !#strings.isEmpty(sh.plantName) and !#strings.isEmpty(sh.address)}">
											<a th:href="${'/saleHouses/r'+sh.regionId}" th:text="${sh.regionName}"></a>-
											<a th:href="${'/saleHouses/j'+sh.PlatId+'-r'+sh.regionId}" th:text="${sh.plantName}"></a>-
											<th:block th:text="${sh.address}"></th:block>
										</s>
									</p>
									<div class="bottomtext">
										<div class="houseItemIcon">
											<th:block th:if="${sh.houseTrait ne null and sh.houseTrait ne ''}">
												<span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(sh.houseTrait).split(',')}" th:if="${i.count le 3}"
												 th:text="${item}"></span>
											</th:block>
										</div>
										<span class="personShow">
											<div th:if="${sh.memberType eq '经纪人'}">
												<!--                                            <i class="personIcon"></i>-->
												<div class="agentShowImg"><img th:if="${!#strings.isEmpty(sh.Avatar)}" th:src="${sh.Avatar}" alt=""></div>
												<div class="agentShowImg"><img th:if="${#strings.isEmpty(sh.Avatar)}" src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png"
													 alt=""></div>
												<span>
													<th:block th:text="${sh.agency}"></th:block>
													<th:block th:if="${!#strings.isEmpty(sh.spanTime)}" th:text="${sh.spanTime+'更新'}"></th:block>
												</span>
												<span>
													<th:block th:if="${!#strings.isEmpty(sh.IntermediaryName)}" th:text="${sh.IntermediaryName}"></th:block>
												</span>
												<!-- 安心经纪人标识 -->
												<img th:if="${#strings.equals(sh.anXuan,'-1')}" src="https://static.fangxiaoer.com/web/images/sy/anxin/renzheng_iocn.png" style="width: 14.6px;height: 14.6px;margin-top: 4px;margin-left: -8px;">
											</div>

											<div th:if="${sh.memberType eq '个人'}">
												<img src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
												<s>
													<th:block th:text="${sh.memberType}"></th:block>
												</s>
											</div>
											<!-- <div th:style="${sh.memberType eq '个人'}?'display:block':'display:none'">
                                             <span style="background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale/iconAuthentichouse.gif) no-repeat 9px 3px; padding-left: 76px; font-size: 16px; color: #E04545; position: absolute; left: 260px; bottom: 2px;"></span>
                                         </div>-->
										</span>

									</div>

									<!--<div th:style="${sh.memberType eq '个人'}?'display:block':'display:none'">
                                    <span style="background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale/iconAuthentichouse.gif) no-repeat 9px 3px; padding-left: 76px; font-size: 16px; color: #E04545; position: absolute; left: 260px; bottom: 2px;"></span>
                                </div>-->
								</div>
								<div class="infRight">
									<p class="infRightPriseM" th:if="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')}"
									 th:text='面议'></p>
									<p class="infRightPrise" th:if="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}">
										<th:block th:text="${#strings.toString(sh.price).contains('.')? #strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$', '') : sh.price}"></th:block>
										<i th:text="${#strings.isEmpty(sh.price) or (#strings.toString(sh.price) eq '0.0')?'':'万'}"></i>
									</p>
									<p th:if="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}" th:text="${#strings.toString(sh.unitPrice).contains('.')?#strings.toString(sh.unitPrice).replaceAll('0+?$','').replaceAll('[.]$', ''):sh.unitPrice}+'元/m²'"
									 th:style="${!#strings.isEmpty(sh.price) and #strings.toString(sh.price) ne '0.0'}?'display:block':'display:none'">
									</p>
									<!-- <a class="checkHouse" target="_blank" th:href="'/dealSales/-v'+${sh.subID}">查看同小区成交房源&gt;</a>
                                <a class="checkHouse" target="_blank" th:href="'/saleHouses/-v'+${sh.subID}">查看同小区房源&gt;</a>-->
								</div>
							</div>
							<!-- 需要判断显示内容 -->
							<!--<div class="inf" th:if="${sequence.index eq 6}" id="esfListEwmBanner">-->
							<!--<div id = "brandpic"  class="esfListEwmBanner" style="background-color: #fff">-->
							<!--<img src="https://static.fangxiaoer.com/web/images/sy/sale/list/esfListEwmBanner.png" alt="">-->
							<!--<h4>扫描下载房小二app</h4>-->
							<!--<p>好房为你而选，让买房更简单！</p>-->
							<!--<i><img src="https://static.fangxiaoer.com/web/images/sy/sale/list/esfListClose.png" alt="" class="esfListClose" ></i>-->
							<!--</div>-->
							<!--</div>-->
						</div>
						<!--<script>-->
						<!--$(".esfListClose").click(function () {-->
						<!--$("#esfListEwmBanner").hide()-->
						<!--})-->
						<!--</script>-->

					</div>
				</div>

			</div>
			<div class="cl"></div>
			<div class="gxq hid">
				<h3>您可能感兴趣的房源</h3>
				<ul>
					<li>
						<a href="" target="_blank"><img src="https://images.fangxiaoer.com/sy/esf/fy/middle/2017/07/03/59599f4d2858e.jpg"
							 alt=""></a>
						<a href="" target="_blank">投资首选万科明天广场 1室 1厅 1卫 46㎡精装修拎包即住</a>
						<p><span>820<i>万</i></span>4室3厅</p>
					</li>
				</ul>
			</div>
			<div class="gxqxq" th:if="${!#lists.isEmpty(interest)}">
				<h3><span></span>您可能感兴趣的小区</h3>
				<ul>
					<li th:each="is:${interest}">
						<a th:href="'/saleVillages/'+${is.SubID}+'/index.htm'" target="_blank">
							<img th:src="${#strings.isEmpty(is.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':is.pic}"
							 alt=""></a>
						<p th:text="${is.regionName}"> </p>
						<p th:text="${is.Name}"></p>
						<div class="cl"></div>
						<p th:if="${!#strings.isEmpty(is.salecount) and is.salecount ne '0'}">二手房<i th:text="${is.salecount}"></i>套</p>
						<p th:if="${!#strings.isEmpty(is.rentcount) and is.rentcount ne '0'}">租房<i th:text="${is.rentcount}"></i>套</p>
					</li>
				</ul>
			</div>

			<script>
				var i = 0;
				for (i = 0; i < $("#option_other .select_info").length; i++) {
					if ($("#option_other .select_box").eq(i).find(".hover").length > 0) {
						$("#option_other .select_info").eq(i).text($("#option_other .select_box").eq(i).find(".hover").text())
					}
				}
			</script>
			<script type="text/javascript">
				function __doPostBack(pager1, page) {
					var url = window.location.pathname;
					if (pager1 == "Search$Btn_Search1") {
						var priceBegin = $("#minPrice").val();
						var priceEnd = $("#maxPrice").val();
						var ref1 = url.replace(/-k[0-9]\d*/, ''); //k最小值
						var ref2 = ref1.replace(/-x[0-9]\d*/, ''); //x最大值
						if (parseInt(priceEnd) < parseInt(priceBegin)) {
							priceEnd = [priceBegin, priceBegin = priceEnd][0];
						}
						if (priceBegin != "" && priceBegin != 0)
							ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/') + "-k" + priceBegin;
						if (priceEnd != "")
							ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/') + "-x" + priceEnd;
						location.href = ref2;
					}
					if (pager1 == "Search$Btn_Searchs") {
						var areaBegin = $("#minArea").val();
						var areaEnd = $("#maxArea").val();
						var ref1 = url.replace(/-y[0-9]\d*/, ''); //y最小值
						var ref2 = ref1.replace(/-e[0-9]\d*/, ''); //e最大值
						if (parseInt(areaEnd) < parseInt(areaBegin)) {
							areaEnd = [areaBegin, areaBegin = areaEnd][0];
						}
						if (areaBegin != "" && areaBegin != 0)
							ref2 = ref2.replace(/-a[0-9]\d*/, '').replace(/\/a[0-9]\d*/, '\/') + "-y" + areaBegin;
						if (areaEnd != "")
							ref2 = ref2.replace(/-a[0-9]\d*/, '').replace(/\/a[0-9]\d*/, '\/') + "-e" + areaEnd;
						location.href = ref2;
					}
				}
				$("a").click(function() {
					event.stopPropagation();
				});
			</script>

			<div class="cl"></div>

			<div th:include="fragment/fragment::tongji"></div>
		</div>
		<input type="hidden" id="names" th:value="${chooseName}">
		<input type="hidden" id="chooseType" th:value="${chooseType}">
		<input type="hidden" id="chooseId" th:value="${chooseId}">
		<input type="hidden" id="choose_region_id" th:value="${choose_region_id}">

		<!--</form>-->
		<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>

		<!--底部1-->
		<div th:include="fragment/fragment:: footer_list"></div>
		<!--<script src="/js/house/second/house_price.js" type="text/javascript" charset="utf-8"></script>-->
		<script src="https://static.fangxiaoer.com/js/listHousePrise/house_price.js?v=20191228" type="text/javascript"
		 charset="utf-8"></script>

	</body>
</html>
