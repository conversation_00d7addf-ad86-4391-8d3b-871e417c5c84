<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml"  xmlns:th="https://www.thymeleaf.org">
<head>
    <title>沈阳地图找二手房，沈阳二手房产地图，沈阳二手房地图 - 房小二网</title>
    <meta name="keywords" content="沈阳二手房,沈阳二手房地图,沈阳地图找房,沈阳房产地图,沈阳楼市地图" />
    <meta name="description" content="房小二网地图找房为您提供更新的沈阳二手房楼盘地图信息，全面的沈阳二手房位置及沈阳二手房出售相关信息。通过简单方便的地图找房功能，使您更加方便地找到目标区域的二手房信息，为您创造更好的二手房买房体验，查找沈阳二手房，就来房小二网二手房地图找房。" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/housemap2.htm">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
<!--    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
    <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
    <script src="https://static.fangxiaoer.com/js/baiduMap/ajax.js" type="text/javascript" charset="utf-8"></script>
    <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/baiduMap/baiduMap1.css" />-->
    <script src="https://static.fangxiaoer.com/js/baiduMap/vue.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="/css/secondbaidumap/secondmap.css?t=20190104" />
    <script src="https://static.fangxiaoer.com/js/baiduMap/subWayLine.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="/js/jquery.autocomplete.js"></script>
    <style type="text/css">
        .mapList .mapSelect>div>p{
            width:112px
        }
        .ac_results{
            width: 435px !important;
            border-top: none !important;
            top: 96px !important;
            z-index:  99;
        }
        .chooseBtnIcon{
            display: block;
            width: 5px;
            height: 10px;
            font-size: inherit;
            background: url(https://static.fangxiaoer.com/web/images/map/metroIconRight.jpg) top center;
            background-size: 100% 100%;
            float: right;
            margin-top: 19px;
            margin-left: 5px;
        }
        .chooseBtn:hover .chooseBtnIcon{ background: url(https://static.fangxiaoer.com/web/images/map/metroIconRightH.png) top center;background-size: 100% 100%;}

    </style>
</head>
<body>


<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=3,subNavIndex=1"></div>
<!--<form name="form1" method="post" action="" id="form1">-->
    <div id="map" v-bind:style="{height: height+'px',width:windowWidth+'px'}">
        <!--搜索框-->
        <div class="searchEsfMap" style="width: 100%">
            <a class="chooseBtn" style="text-decoration: none;position: absolute;right: 50px;top: -6px;height: 45px;line-height: 45px;cursor: pointer;background: #fff;z-index: 9999;padding: 0 10px;" href="/subwaymap.htm">切换至地铁找房<i class="chooseBtnIcon"></i></a>
            <input type="text" id="searchNames" placeholder="请输入区域、小区名称开始找房" class="searchMapInput" />
            <input  type="button" class="searchMapBtn" value="搜索" >
            <img src="https://static.fangxiaoer.com/web/images/sy/index/delete.png" id="deleteButton" alt="x" style="display: none !important;">

        </div>
        <div class="mapList" v-bind:style="{width:listwidth+'px'}">

            <div class="mapSelect">
                <div class="region" @mouseover="showSelectList=0;"  @mouseout="showSelectList=-1;">
                    <p>{{ regionList.ActiveText }}</p>
                    <ul  v-bind:style="{display: (showSelectList==0 ? 'block':'none')}">
                        <li @click="navNormal(regionList,'区域不限')">不限</li>
                        <li v-for="vregion in regionList.content" @click="navSelect1(regionList,vregion.name,vregion.id);SetMap1(vregion.latitude,vregion.longitude,maxRegion)">{{ vregion.name }}</li>
                    </ul>
                </div>
                <div class="price" v-if="navIsActive==0||navIsActive==1||navIsActive==2" @mouseover="showSelectList=2;"  @mouseout="showSelectList=-1;">
                    <p>{{ priceList.ActiveText }}</p>
                    <ul  v-bind:style="{display: (showSelectList==2 ? 'block':'none')}">
                        <li @click="navNormal(priceList,'价格不限')">不限</li>
                        <li v-for="vprice in priceList.content" @click="navSelect(priceList,vprice.name,vprice.id)">{{ vprice.name }}</li>
                    </ul>
                </div>
                <div class="price" v-if="navIsActive==0||navIsActive==1||navIsActive==5"  @mouseover="showSelectList=5;"  @mouseout="showSelectList=-1;">
                    <p>{{ areaList.ActiveText }}</p>
                    <ul  v-bind:style="{display: (showSelectList==5 ? 'block':'none')}">
                    <li @click="navNormal(areaList,'面积不限')">不限</li>
                    <li v-for="vareaList in areaList.content" @click="navSelect(areaList,vareaList.name,vareaList.id)">{{ vareaList.name }}</li>
                    <i class="cleanUrl" ></i>
                    </ul>
                </div>
              <!--  <div class="price" v-if="navIsActive==0||navIsActive==1||navIsActive==5">
                    <p @click="showSelectList=5">{{ areaList.ActiveText }}</p>
                    <ul v-if="showSelectList==5">
                        <li @click="navNormal(areaList,'面积不限')">不限</li>
                        <li v-for="vareaList in areaList.content[5].selection" @click="navSelect(areaList,vareaList.name,vareaList.id)">{{ vareaList.name }}</li>
                    </ul>
                </div>-->


                <div class="type" @mouseover="showSelectList=1;"  @mouseout="showSelectList=-1;">
                    <p>{{ typeList.ActiveText }}</p>
                    <ul  v-bind:style="{display: (showSelectList==1 ? 'block':'none')}">
                        <li @click="navNormal(typeList,'户型不限')">不限</li>
                        <li v-for="vtype in typeList.content" @click="navSelect(typeList,vtype.name,vtype.id)">{{ vtype.name }}</li>
                    </ul>
                </div>
            </div>


            <div class="selected_condition" v-if="regionList.ActiveId != ''||priceList.ActiveId !=''||areaList.ActiveId != ''||typeList.ActiveId != ''||searchTitle != ''">
                <h1>已选：</h1>
                <div class="box">
                    <p @click="deleteSearchValue();" v-if="searchTitle != ''"><label v-bind:title="  searchTitle ">{{searchTitle}}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                    <p @click="navNormal(regionList,'区域')" v-if="regionList.ActiveId != ''"><label v-bind:title=" regionList.ActiveText  ">{{  regionList.ActiveText  }}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                    <p @click="navNormal(priceList,'价格')" v-if=" priceList.ActiveId != ''"><label v-bind:title=" priceList.ActiveText ">{{ priceList.ActiveText}}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                    <p @click="navNormal(areaList,'面积')" v-if=" areaList.ActiveId != ''"><label v-bind:title=" areaList.ActiveText ">{{ areaList.ActiveText}}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                    <p @click="navNormal(typeList,'户型')" v-if=" typeList.ActiveId != ''"><label v-bind:title=" typeList.ActiveText ">{{ typeList.ActiveText}}</label><img src="https://static.fangxiaoer.com/web/images/ico/map/close.png"></p>
                    <div class="cancelAll" @click="deleteAllValue();">清空</div>
                </div>
                <div class="cl"></div>
            </div>
            <div class="village_details" style="display: none;">
                <div class="village_left">
                    <span class="su_title" id="su_title"></span>
                    <span class="su_build" id="su_build"></span>
                    <span class="jun_priceSpan"><s class="jun_price"></s><span class="su_unitprice" id="su_unitprice"></span><s class="jun_unit"></s></span>

                </div>
                <div class="village_right">
                    <p><i><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_map.gif" alt=""></i><span class="su_address" id="su_address"></span></p>
                    <span class="su_chainSpan"><s class="su_chain"></s><span class="su_rate" id="su_rate"></span></span>
                </div>
            </div>

            <div v-bind:style="{height: height-120+'px'}" class="mapSaleHouseList HouseList">
                <ul>
                    <li v-for="vnewHousList in newHousList">
                        <a v-bind:href="'https://sy.fangxiaoer.com/salehouse/'+vnewHousList.houseId+'.htm'" target="_blank">
                            <img v-if="vnewHousList.pic!=''" v-bind:src="vnewHousList.pic"/>
                            <img v-if="vnewHousList.pic==null" src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg"/>
                            <div>
                                <h1><p>{{vnewHousList.title}}</p><i v-on:click="SetMap(vnewHousList.latitude,vnewHousList.longitude,maxPlat);" onclick="return false;" class="move"></i></h1>
                                <div  v-if="vnewHousList.price != 0"><p>{{vnewHousList.regionName}}-{{vnewHousList.subName}}二手房</p><h4><span>{{vnewHousList.price}}万</span></h4></div>
                                <div  v-if="vnewHousList.price == 0"><p>{{vnewHousList.regionName}}-{{vnewHousList.subName}}二手房</p><h4><span>面议</span></h4></div>
                                <!--<div><p>{{vnewHousList.layout}}　{{vnewHousList.floor}}/{{vnewHousList.totalFloorNumber}}层　{{vnewHousList.forward}}</p><span>{{Math.floor((vnewHousList.price*10000)/vnewHousList.area)}}元/㎡</span></div>-->
                                <div v-if="vnewHousList.price != 0"><p>{{vnewHousList.layout}}   {{vnewHousList.floor}}/{{vnewHousList.totalFloorNumber}}层　{{vnewHousList.forward}}</p ><span>{{Math.floor((vnewHousList.price*10000)/vnewHousList.area)}}元/㎡</span></div>
                                <div>
                                    <div v-if="(vnewHousList.houseTrait!='')&&(index<4)" v-for="(trait,index) in vnewHousList.houseTrait.split(',')" v-bind:class="['tese_'+(index+1)]">{{ trait }}</div>
                                    <span>{{vnewHousList.area}}㎡</span>
                                </div>
                            </div>
                        </a>
                    </li>
                </ul>

                <div  class="noHouseMap">
                    <div  v-bind:style="{display: recommentSwitch}" style="display: none;border-bottom: 1px solid #ddd;padding-bottom: 30px">
                        <h4>地图范围内没有找到房源</h4>
                        <p>建议您：拖动地图更改位置或 <a href="/helpSearch?ids=2" target="_blank">填写表单，专属定制找房</a></p>
                    </div>
                </div>
            </div>
            <img id="loading" src="https://static.fangxiaoer.com/web/images/sy/map/loading.gif" alt="加载中……" />
        </div>
        <div id="baiduMap" v-bind:style="{width:mapWidth+'px'}">
        </div>
    </div>
    <div class="hint" id="hint" style="display: none;">
        <dl>
            <dd><p>您的浏览器版本过低，<br>建议升级浏览器！</p></dd>
            <dd>
                <img src="https://static.fangxiaoer.com/web/images/sy/map/info.png"/>
            </dd>
            <dd>
                <a href="/static/oldSaleMap.htm">查看低版本地图</a>
                <a href="https://pc.uc.cn/" target="_blank" style="color: #ff5200;" id="Dherf">升级浏览器</a>
            </dd>
        </dl>
    </div>
    <script>
        var type=2
        var navType=0;
        //如果是safari浏览器显示不支持
//        if(navigator.userAgent.indexOf("Safari") > -1 && navigator.userAgent.indexOf("Chrome") < 1){
//            document.getElementById("hint").style.display="block";
//        }
        //如果是ie显示不支持
        if(navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > -1 && !(navigator.userAgent.indexOf("Opera") > -1) ){
            document.getElementById("hint").style.display="block";
        }


    </script>

    <!--<script src="https://static.fangxiaoer.com/js/baiduMap/baiduMap1.js" type="text/javascript" charset="utf-8"></script>-->
    <script src="/js/baiDuMapUtils/baiduSecondMap.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="/js/secondbaidumap/secondmap.js"></script>

<!--</form>-->
<div th:include="fragment/fragment::tongji"></div>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/my_xiala.js"></script>
<!--<div th:include="fragment/fragment::esfCommon_meiqia"></div>-->
</body>
</html>
