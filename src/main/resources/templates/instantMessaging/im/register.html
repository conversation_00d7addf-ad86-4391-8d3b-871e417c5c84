<!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>云信 Web Demo</title>
	<link rel="stylesheet" href="css/base.css"/>
	<link rel="stylesheet" href="css/animate.css"/>
	<link rel="stylesheet" href="css/index.css?v=1"/>
	<link rel="icon" href="images/icon.ico" type="image/x-icon"/>
</head>
<body>
	<div class="wrapper radius10px">
		<form action="" method="post" class="m-register">
			<div class="row tc">
				<span class="type">账号：</span>
				<input type="text" class="radius5px box-sizing" id="username" autofocus="autofocus" autocomplete="on" placeholder="限20位字母或者数字"  maxlength="20"/>
			</div>
			<div class="row tc">
				<span class="type">昵称：</span>
				<input type="text" class="radius5px box-sizing" id="nickname" autofocus="autofocus" autocomplete="on" placeholder="限10位汉字、字母或者数字" maxlength="10"/>
			</div>
			<div class="row tc">
				<span class="type">密码：</span>
				<input type="password" class="radius5px box-sizing" id="password" placeholder="6~20位字母或者数字"  maxlength="20" autocomplete="new-password"/>
			</div>
			<div class="row">
				<div class="error-msg hide" id="errorMsg">此账号已存在</div>
			</div>
			<div class="row tc">
				<button type="button" class="btn btn-login" id="submit">注册</button>
			</div>
		</form>
		<div class="u-redirect"><a href="./index.html">已有账号？直接登录</a></div>
	</div>
	<div class="footer tc">
		<p class="hide">为了更好地体验本Demo，建议您使用IE10、Chrome、FireFox、Safari、360等主流浏览器。</p>
		<p>&copy;1997 - 2018 网易公司版权所有</p>
	</div>
	<script src="../3rd/jquery-1.11.3.min.js"></script>
	<script src="js/config.js"></script>
	<script src="js/md5.js"></script>
	<script src="js/register.js"></script>
</body>
</html>