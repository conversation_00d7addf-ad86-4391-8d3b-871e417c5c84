<ul class="member-list J-member-list" id="memberList"></ul>
<div class="option-box">
	<div class="operation meeting">
		<div class="control">
			<div class="switch control-item hide">
				<span class="fullScreenIcon" title="切换全屏">&nbsp;</span>
			</div>
			<div class="microphone control-item">
				<span class="icons icon-micro"></span>
				<div class="slider hide">
					<div class="txt">10</div>
					<input class="microSliderInput" type="range" min="0" max="10" step="1" value="10" data-orientation="vertical">
				</div>
			</div>
			<div class="volume control-item">
				<span class="icons icon-volume"></span>
				<div class="slider hide">
					<div class="txt">10</div>
					<input class="volumeSliderInput" type="range" min="0" max="10" step="1" value="10" data-orientation="vertical">
				</div>
			</div>
			<div class="camera control-item">
				<span class="icon-camera"></span>
			</div>
			<div class="speakBan control-item">
				<span class="icon-ban icon-disabled"></span>
			</div>
			<div class="desktop control-item J-desktop-share hide">
				<span class="icon-desktop">
					<div class="desktop-share">
						<p class="share-item" data-type="screen">共享全屏幕</p>
						<p class="share-item" data-type="window">共享程序窗口</p>
						<p class="share-item" data-type="close">停止共享</p>
					</div>
				</span>
			</div>
		</div>
		<div class="op">
			<button class="hangupButton netcall-button red">挂断</button>
		</div>
		<div class="tip">等待接听...</div>
	</div>
</div>