<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>sy站聊呗</title>
</head>

<body>
	<div th:fragment="im_mini_window">
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/css/im_main.css">
		<link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/css/CEmojiEngine.css">
		<style>
			.im_talk_you>.im_talk_msg {
				word-wrap: break-word;
				word-break: normal;
				text-align: left;
			}
			.im_input textarea {
				color: #333;
			}
		</style>

		<div class="im_outer" style="display: none">
			<!-- 头部 -->
			<div id="im_head" class="no_user-select">
				<!-- 左侧客服信息区 -->
				<div id="im_service" style="visibility: hidden">
					<div class="im_online_point" style="background: #999;"></div>
					<div class="im_head_service_namer"></div>
					<div class="im_head_service_state">[<span id="im_online" style="color: #999">离线</span>]</div>
				</div>
				<!-- 右侧按钮区 -->
				<div id="im_head_right">
					<div id="im_close">
						<img src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/images/close.png" alt="">
					</div>
					<div id="im_history">
						<img src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/images/time.png" alt="">
					</div>
					<!-- 电话图标区 -->
					<div id="im_phone">
						<img class="phone_1" src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/images/call.png" alt="">
						<img class="phone_2" style="display: none;" src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/images/call_selected.png" alt="">
						<img class="im_400_phone" style="opacity: 0;" src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/images/400.png" alt="">
					</div>
				</div>
			</div>
			<!-- 聊天内容区 -->
			<div class="im_content_outer">
				<div class="im_content_inner">
					<div class="im_content" id="im_content">
						<!-- <div class="im_talk_time">昨天 晚上 10：30</div>
							<div class="im_talk_item im_talk_me">
								<div class="im_talk_avatar"><img src="/images/im/man.png" alt=""></div>
								<div class="im_talk_msg">Hi~有个问题咨询</div>
							</div>

						<div class="im_talk_item im_talk_you">
							<div class="im_talk_avatar"><img src="/images/im/service.png" alt=""></div>
							<div class="im_talk_msg">Hi~等你好久了。</div>
						</div> -->
					</div>
				</div>

			</div>
			<!-- 底部 -->
			<div>
				<div id="im_quickTalkList" class="no_user-select">
					<div id="im_change_quickTalkList">
						<!-- <div class="quickMsg">
							<p>这套房子还在吗?</p>
						</div>
						<div class="quickMsg">
							<p>房子位置怎么样?</p>
						</div>
						<div class="quickMsg">
							<p>小区情况怎么样?</p>
						</div>
						<div class="quickMsg">
							<p>房子基本信息如何?</p>
						</div> -->
					</div>
					<div class="im_change_quickTalkList_Btn">
						<span id="im_change_btn" class="im_change_btn"><img src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/images/rotate.png" alt="">换一批</span>
					</div>
				</div>
				<div class="im_input_content">
					<div class="im_input">
						<textarea id="im_msg_input" rows="2" maxlength="500"
								  placeholder="请输入您想要咨询的问题，例如：这套房子升值空间如何？"></textarea>
					</div>
					<div class="im_input_btn no_user-select">
						<img id="im_emoji" src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/images/expression.png" alt="">
						<img id="im_file" src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/images/icon-file.png" alt="">
						<label id="im_send" style="display: none;">发送</label>
					</div>
					<div id="emojiTag" class="m-emojiTag"></div>
				</div>
			</div>
		</div>

		<form action="#" id="uploadForm">
			<input style="display: none;" id="im_uploadFile" multiple="multiple" type="file" name="file" accept="image/*">
		</form>

		<!--<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/jquery.min.js"></script>-->
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/NIM_Web_SDK_v7.1.1.js"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/ui.js"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/config.js"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/util.js?v=4"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/base.js"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/cache.js"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/message.js"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/emoji.js"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/link.js"></script>
		<script src="https://static.fangxiaoer.com/web/styles/sy/sy-im-mini/js/main.js"></script>
	</div>
</body>
</html>