<!doctype html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">

<head>
	<meta charset="UTF-8" />
	<title>聊呗</title>
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/base.css" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/animate.css" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/jquery-ui.css" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/contextMenu/jquery.contextMenu.css" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/minAlert.css" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/main_new.css?v=1" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/uiKit_new.css?v=1" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/CEmojiEngine.css" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/rangeslider.css" />
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/pure-min.css">
	<link rel="stylesheet" href="https://static.fangxiaoer.com/im_ziyuan/im/css/whiteboard.css" />
	<!--<link rel="icon" href="https://static.fangxiaoer.com/im_ziyuan/im/images/icon.ico" type="image/x-icon" />-->
</head>
<style>
	.hd_warn_bg{
		background: rgba(0,0,0,0.7);
		width: 100%;
		height: 100%;
		position: fixed;
		z-index: 99999;
		top: 0px;
		left: 0px;
		display: none;
	}
	.hd_warn_content{
		width: 260px;
		height: 140px;
		margin: 20% auto;
		background-color: #FFF;
		font-size: 14px;
		padding: 10px 10px;
		border-radius: 3px;
	}
	.clear{
		clear: both;
	}
	.hd_warn_cancel{
		width: 70px;
		height: 28px;
		line-height: 28px;
		text-align: center;
		color: #ff5200;
		border: 1px solid #dd5200;
		border-radius: 3px;
		background-color: #FFF;
		margin-top: 20px;
		float: left;
		margin-left: 13px;
		cursor: pointer;
	}
	.hd_warn_resumed{
		float: right;
		width: 102px;
		height: 28px;
		line-height: 28px;
		color: #FFF;
		background-color: #dd5200;
		border-radius: 3px;
		margin-top: 20px;
		text-align: center;
		border: 1px solid #dd5200;
		margin-right: 13px;
		cursor: pointer;
	}
	.hd_chatTitle_warn{
		width: 100%;
		height: 24px;
		line-height: 24px;
		position: absolute;
		top: 0px;
		left: 0px;
		background-color: #fd5d55;
		z-index: 9999;
		color: #fff;
		cursor: pointer;
		display: none;
	}
	.reply-font {
		color: #FFBC15;
		line-height: 18px;
		height: 33px;
		font-size: 18px;
		font-family: Microsoft YaHei;
		font-weight: bold;
		border-bottom: 2px solid #FFBC15;
		float: left;
		margin-left: 21px;
		margin-top: 5px;
	}
</style>
<body>
<div class="hd_warn_bg">
	<div class="hd_warn_content">
		<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/hd_out.png" style="float: right;cursor: pointer;" onclick="closeContent(this)">
		<div class="clear"></div>
		<div style="width: 225px;margin-top: 25px;line-height: 25px;margin: 15px auto;">
			<div>您已在其它页面发起咨询，当前咨询已失效，<a style="color: #ff5200;" href="" onclick="refresh(this)">请点击这里重新咨询</a></div>
			<div class="hd_warn_cancel" onclick="closeContent(this)">取消</div>
			<div class="hd_warn_resumed" onclick="refresh(this)">重新发起咨询</div>
		</div>

	</div>
</div>
<div class="bad-network hide tc radius5px" id="errorNetwork">已与服务器断开，请检查网络连接</div>
<div class="wrapper box-sizing">
	<!--<div style="width: 1003px;margin-top: 150px;">
		<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/bg_head.png" style="width: 100%;">
	</div>-->
	<div class="content" style="margin-top: -3px;">
		<div class="left-panel radius5px" id="left-panel">
			<div class="hide loading" id="loading"></div>
			<div class="user-title">
				<img src="" alt="" class="radius-circle avatar" id="userPic" />
				<span id="userName">&nbsp;</span>
				<!--<span>
						<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/pen.png" class="u-modify" id="showMyInfo">
				</span>-->
				<!--<span class="exit" title="退出" id="logout">退出</span>-->
			</div>
			<!--<div class="m-devices hide">正在使用云信web版</div>
			<div class="panel" id="switchPanel">
				<a class="box-sizing tc m-unread panel_tab cur" data-type="sessions">
					<span class="icon icon-session"></span>
					<b class="u-unread hide"> </b>
				</a>
				<a class="box-sizing tc panel_tab" data-type="friends">
					<span class="icon icon-friend"></span>
				</a>
				<a class="box-sizing tc panel_tab" data-type="teams">
					<span class="icon icon-team"></span>
				</a>
			</div>-->
			<div class="item" id="sessionsWrap" data-type="sessions">
				<!-- 最近联系人 -->
				<div class="list">
					<!--<div class="m-panel">
						<div class="panel_item m-sysMsg" id="showNotice">
							<div class="panel_avatar">
								<img class="panel_image" src="https://static.fangxiaoer.com/im_ziyuan/im/images/notice.png" alt="消息中心" />
							</div>
							<div class="panel_text">
								<p class="panel_single-row">消息中心</p>
							</div>
							<b class="count j-sysMsgCount hide"></b>
						</div>
					</div>-->
					<div id="sessions">
						<p>正在获取最近联系人...</p>
					</div>
				</div>
			</div>
			<div class="item hide" id="friendsWrap" data-type="friends">
				<!-- 通讯录 -->
				<div class="list">
					<div class="m-panel">
						<div class="panel_item" id="addFriend">
							<div class="panel_avatar">
								<img class="panel_image" src="https://static.fangxiaoer.com/im_ziyuan/im/images/addFriend.png" alt="添加好友" />
							</div>
							<div class="panel_text">
								<p class="panel_single-row">添加好友</p>
							</div>
						</div>
						<div class="panel_item" id="showBlacklist">
							<div class="panel_avatar">
								<img class="panel_image" src="https://static.fangxiaoer.com/im_ziyuan/im/images/black.png" alt="黑名单" />
							</div>
							<div class="panel_text">
								<p class="panel_single-row">黑名单</p>
							</div>
						</div>
						<div class="panel_item" id="myPhone">
							<div class="panel_avatar">
								<img class="panel_image" src="https://static.fangxiaoer.com/im_ziyuan/im/images/myPhone.png" alt="我的手机" />
							</div>
							<div class="panel_text">
								<p class="panel_single-row">我的手机</p>
							</div>
						</div>
					</div>
					<div id="friends">
						<p>正在获取通讯录...</p>
					</div>
				</div>
			</div>
			<div class="item hide" id="teamsWrap" data-type="teams">
				<!-- 群 -->
				<div class="list">
					<div class="m-panel">
						<div class="panel_item" id="createTeam">
							<div class="panel_avatar">
								<img class="panel_image" src="https://static.fangxiaoer.com/im_ziyuan/im/images/addTeam.png" alt="创建讨论组" />
							</div>
							<div class="panel_text">
								<p class="panel_single-row">创建讨论组</p>
							</div>
						</div>
					</div>
					<div class="m-panel">
						<div class="panel_item" id="createAdvanceTeam">
							<div class="panel_avatar">
								<img class="panel_image" src="https://static.fangxiaoer.com/im_ziyuan/im/images/addTeam.png" alt="创建高级群" />
							</div>
							<div class="panel_text">
								<p class="panel_single-row">创建高级群</p>
							</div>
						</div>
					</div>
					<div class="m-panel">
						<div class="panel_item" id="searchAdvanceTeam">
							<div class="panel_avatar">
								<img class="panel_image" src="https://static.fangxiaoer.com/im_ziyuan/im/images/searchTeam.png" alt="搜索高级群" />
							</div>
							<div class="panel_text">
								<p class="panel_single-row">搜索高级群</p>
							</div>
						</div>
					</div>
					<div class="teams m-panel" id="teams">
					</div>
				</div>
			</div>
		</div>
		<div class="chatVernier" id="chatVernier">
			<span class="radius-circle hide"></span>
		</div>
		<div class="right-panel hide" id="rightPanel">
			<!-- 聊天面板 -->
			<div class="chat-box show-netcall-box" id="chatBox">
				<div class="hd_chatTitle_warn">
					<span style="margin-left: 25px;">啊哦，消息链接断开了！</span>
					<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/hd_out3.png" onmouseout="changeClose2(this)" onmouseover="changeClose1(this)" onclick="closewarn(this)" style="float: right;margin-right: 10px;margin-top: 5px;">
					<div class="clear"></div>
				</div>
				<div class="title" id="chatTitle">
					<div class="im_new_info_title">
						<div class="im_stateLight" id="im_stateLight"></div>
						<span id="nickName"></span>
					</div>
					<!--<img src="" width="56" height="56" class="radius-circle img" id="headImg" />
					<span id="nickName"></span>-->
					<!--<div class="cloudMsg tc radius4px" data-record-id="" id="cloudMsg">
						<i class="icon icon-team-info"></i>
						<p>云记录</p>
					</div>-->
					<div class="team-info hide tc radius4px" data-team-id="" id="teamInfo">
						<i class="icon icon-team-info"></i>
						<p>资料</p>
					</div>
				</div>
				<div class="netcall-box" id="netcallBox">
					<div class="netcall-mask hide">
						<div class="netcallTip"></div>
					</div>
					<div class="top hide">
						<span class="transferAudioAndVideo switchToAudio" id="switchToAudio">切换音频</span>
						<span class="transferAudioAndVideo switchToVideo" id="switchToVideo">切换视频</span>
						<span class="fullScreenIcon toggleFullScreenButton" id="toggleFullScreenButton" title="切换全屏">&nbsp;</span>
					</div>
					<!-- p2p呼叫界面 -->
					<div class="netcall-calling-box hide">
						<img alt="用户头像" class="avatar">
						<div class="nick"></div>
						<div class="tip">等待对方接听...</div>
						<div class="op">
							<button id="callingHangupButton" class="netcall-button red">挂断</button>
						</div>
					</div>
					<!-- p2p视频界面 -->
					<div class="netcall-show-video hide">
						<div class="netcall-video-left">
							<div class="netcall-video-remote bigView">
								<div class="message"></div>
								<span class="switchViewPositionButton"></span>
							</div>
						</div>
						<div class="netcall-video-right">
							<div class="netcall-video-local smallView">
								<div class="message"></div>
								<span class="switchViewPositionButton"></span>
							</div>
							<div class="operation">
								<div class="control">
									<div class="microphone control-item">
										<div class="slider hide">
											<div class="txt">10</div>
											<input class="microSliderInput" id="microSliderInput1" type="range" min="0" max="10" step="1" value="10" data-orientation="vertical">
										</div>
										<span class="icon-micro"></span>
									</div>
									<div class="volume control-item">
										<div class="slider hide">
											<div class="txt">10</div>
											<input class="volumeSliderInput" id="volumeSliderInput1" type="range" min="0" max="10" step="1" value="10" data-orientation="vertical">
										</div>
										<span class="icon-volume"></span>
									</div>
									<div class="camera control-item">
										<span class="icon-camera"></span>
									</div>
								</div>
								<div class="op">
									<button class="hangupButton netcall-button red">挂断</button>
								</div>
								<div class="tip">00 : 00</div>
							</div>
						</div>
					</div>
					<!-- p2p音频界面 -->
					<div class="netcall-show-audio hide">
						<img alt="用户头像" class="avatar">
						<div class="nick">test</div>
						<div class="tip">00 : 00</div>
						<div class="control">
							<div class="microphone control-item ">
								<div class="slider hide">
									<div class="txt">10</div>
									<input class="microSliderInput" id="microSliderInput" type="range" min="0" max="10" step="1" value="10" data-orientation="vertical">
								</div>
								<span class="icon-micro"></span>
							</div>
							<div class="volume control-item">
								<div class="slider hide">
									<div class="txt">10</div>
									<input class="volumeSliderInput" id="volumeSliderInput" type="range" min="0" max="10" step="1" value="10" data-orientation="vertical">
								</div>
								<span class="icon-volume"></span>
							</div>
						</div>
						<div class="op">
							<button class="hangupButton netcall-button red">挂断</button>
						</div>

					</div>
					<!-- 多人音视频互动界面 -->
					<div class="netcall-meeting-box hide" id="netcallMeetingBox"></div>
					<!-- 被叫界面 -->
					<div class="netcall-becalling-box hide">
						<img alt="用户头像" class="avatar">
						<div class="nick"></div>
						<p id="becallingText" class="tip"></p>
						<div class="op">
							<div class="normal">
								<div class="checking-tip">检查插件中...
									<span class="netcall-icon-checking"></span>
								</div>
								<button class="netcall-button blue beCallingAcceptButton" id="beCallingAcceptButton">
									<span class="txt">接听</span>
									<span class="netcall-icon-checking"></span>
								</button>
								<button class="netcall-button red beCallingRejectButton" id="beCallingRejectButton">
									拒绝
								</button>
							</div>
							<div class="exception">
								<button class="netcall-button blue" id="downloadAgentButton">下载音视频插件</button>
								<br/>
								<button class="netcall-button red beCallingRejectButton">拒绝</button>
								<div class="exception-tip">拒绝调用插件申请会导致无法唤起插件,需重启浏览器</div>
							</div>
						</div>
					</div>
					<div class="dialogs hide">
					</div>
				</div>
				<div id="whiteboard"></div>
				<div class="chat-content box-sizing" id="chatContent">
					<!-- 聊天记录 -->
				</div>
				<div class="u-chat-notice">您已退出</div>
				<div class="chat-mask"></div>
				<div class="chat-editor box-sizing" id="chatEditor" data-disabled="1">
					<div id="emojiTag" class="m-emojiTag"></div>
					<div class="chat-editor-iconBtn box-sizing">
						<div class="reply-font">回复</div>
						<span class="chat-btn msg-type" id="chooseFileBtn">
							<a class="icon icon-file" data-type="file"></a>
						</span>
						<a class="chat-btn u-emoji" id="showEmoji"></a>
					</div>
					<!--<a class="chat-btn u-netcall-audio-link" id="showNetcallAudioLink">&nbsp;</a>
					<a class="chat-btn u-netcall-video-link" id="showNetcallVideoLink">&nbsp;</a>
					&lt;!&ndash; 在netcall_ui.js的fn.whenOpenChatBox中隐藏 &ndash;&gt;
					<a class="chat-btn u-whiteboard" id="showWhiteboard">&nbsp;</a>-->
					<textarea id="messageText" class="msg-input box-sizing radius5px p2p" rows="1" autofocus="autofocus" maxlength="500" placeholder="请输入您想要咨询的问题，例如：这套房子升值空间如何？"></textarea>
					<span class="chat-context" id="showTeamMsgReceipt">
							<label for="needTeamMsgReceipt">
							<input id="needTeamMsgReceipt" type="checkbox" name="needTeamMsgReceipt" />回执</label>
						</span>
					<a class="btn-send radius4px" id="sendBtn">发送</a>
					<form action="#" id="uploadForm">
						<input multiple="multiple" type="file" name="file" id="uploadFile" class="hide" accept="image/*"/>
					</form>
				</div>
			</div>
		</div>
		<div class="right-panel hide radius5px devices-container" id="devices">
			<!-- 设备管理面板 -->
			<div class="info-box">
				<div class="title tc">
					<button class="btn back-btn radius5px" id="backBtn2">返回</button>多端登录管理
				</div>
				<div class="content">
					<div class="main">
						<div class="pic"></div>
						<div class="mobile hide">
							<p>云信手机版</p>
							<a class="u-kick j-kickMoblie">下线</a>
						</div>
						<div class="pc hide">
							<p>云信PC版</p>
							<a class="u-kick j-kickPc">下线</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="asideBox">
		<div class="m-goWhiteboard hide">
			<span class="whiteboard-icon-state netcall-icon-state-audio"></span>
			<div class="nick"></div>
			<div class="tip"></div>
		</div>
		<div class="m-goNetcall hide">
			<span class="netcall-icon-state netcall-icon-state-audio"></span>
			<div class="nick">&nbsp;</div>
			<div class="tip">00 : 00</div>
		</div>
		<!--<a href="./chatroom/list.html" target="_blank">
			<div class="u-goChartRoom"></div>
		</a>-->
	</div>
	<!-- 群资料 -->
	<div class="team-info-container hide" id="teamInfoContainer"></div>
	<!-- 云记录 -->
	<div class="cloud-msg-container hide" id="cloudMsgContainer"></div>
	<!-- 群弹框 -->
	<div class="dialog-team-container radius5px hide" id="dialogTeamContainer"></div>
	<!-- 技术方案弹框 -->
	<div class="dialog-team-container radius5px hide" id="dialogCallMethod"></div>
	<!-- 黑名单 -->
	<div class="m-blacklist radius5px hide" id="blacklist">
		<div class="title box-sizing">
			黑名单
			<i class="icon icon-close j-close"></i>
		</div>
		<div class="notice">你不会接收到列表中联系人的任何消息</div>
		<ul class="f-cb list">
			<li class="items f-cb">
				<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/default-icon.png" class="head">
				<span class="nick">未知</span>
				<button class="btn radius4px btn-ok j-rm">解除</button>
			</li>
		</ul>
	</div>
	<!-- 消息中心 -->
	<div class="m-notice radius5px hide" id="notice">
		<div class="title box-sizing">
			消息中心
			<i class="icon icon-close j-close"></i>
			<b class="j-rmAllSysNotice rmAll"></b>
		</div>
		<ul class="tab f-cb">
			<li class="crt" data-value="sys">系统通知</li>
			<li data-value="other">自定义通知</li>
		</ul>
		<div class="content j-sysMsg">
		</div>
		<div class="content j-customSysMsg hide">
		</div>
	</div>
	<!-- 搜索高级群 -->
	<div class="m-dialog hide" id="searchTeamBox">
		<div class="title box-sizing">
			搜索高级群
			<i class="icon icon-close j-close"></i>
		</div>
		<div class="content tc">
			<input type="text" class="ipt radius5px box-sizing j-account" placeholder="请输入群id" />
			<div class="info f-cb">
				<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/advanced.png">
				<div class="desc">
					<p class="j-name"></p>
					<p>
						<span class="j-teamId"></span>
					</p>
				</div>
			</div>
		</div>
		<div class="btns tc">
			<button class="btn btn-cancel radius4px cancel j-close">取消</button>
			<button class="btn btn-ok radius4px search j-search">确定</button>
			<button class="btn btn-cancel radius4px back j-back">继续搜索</button>
			<button class="btn btn-ok radius4px add j-add">申请加入</button>
			<button class="btn btn-ok radius4px chat j-chat">聊天</button>
		</div>
	</div>
</div>
<!--<div class="footer tc">
	<span>&copy;1997 - 2018 网易公司版权所有</span>
</div>-->

<!-- 退出确认框 -->
<div class="dialog radius5px hide" id="logoutDialog">
	<span class="icon icon-close j-close"></span>
	<div class="content tc">确定要退出云信网页版吗？</div>
	<div class="buttons tc">
		<button class="btn radius4px btn-cancel j-close">取消</button>
		<button class="btn radius4px btn-ok j-ok">确定</button>
	</div>
</div>

<!-- 添加好友 -->
<!--<div class="m-dialog hide" id="addFriendBox">
	<div class="title box-sizing">
		添加好友
		<i class="icon icon-close j-close"></i>
	</div>
	<div class="content tc">
		<input type="text" class="ipt radius5px box-sizing j-account" placeholder="请输入帐号" />
		<div class="info f-cb">
			<img src="">
			<div class="desc">
				<p class="j-nickname"></p>
				<p>
					<span class="j-username"></span>
				</p>
			</div>
		</div>
		<div class="tip">
		</div>
	</div>
	<div class="btns tc">
		<button class="btn btn-cancel radius4px cancel j-close">取消</button>
		<button class="btn btn-ok radius4px search j-search">确定</button>
		<button class="btn btn-cancel radius4px back j-back">继续搜索</button>
		<button class="btn btn-ok radius4px add j-add">加为好友</button>
		<button class="btn btn-ok radius4px done j-close">完成</button>
		<button class="btn btn-ok radius4px chat j-chat">聊天</button>
		<button class="btn btn-del radius4px blacklist j-blacklist">移出黑名单</button>
	</div>
</div>-->
<!-- 人物信息 -->
<!--<div class="m-card hide" id="personCard">
	<i class="icon icon-close j-close"></i>
	<div class="uInfo f-cb">
		<img class="u-icon" src="">
		<div class="desc">
			<p class="j-nick nick">超级大饼</p>
			<img src="" class="j-gender gender">
			<p>
				<span class="j-username">帐号：caojidabin</span>
			</p>
			<p>
				<span class="j-nickname">昵称：caojidabin</span>
			</p>
		</div>
	</div>
	<div class="infos">
		<div class="items alias">
			<div class="item">备注名</div>
			<input type="text" class="e-alias ipt" maxlength="16">
			<a class="j-saveAlias save">保存</a>
		</div>
		<div class="items">
			<div class="item">生日</div>
			<div class="j-birth">1990-08-18</div>
		</div>
		<div class="items">
			<div class="item">手机</div>
			<div class="j-tel">18072912974</div>
		</div>
		<div class="items">
			<div class="item">邮箱</div>
			<div class="j-email"><EMAIL> </div>
		</div>
		<div class="items">
			<div class="item">签名</div>
			<div class="j-sign sign">相信真善美,相信真善美相信真善美相信真善美,相信真善美,相信真善美,相信真善美,相信真善美,相信真善美</div>
		</div>
	</div>
	<ul>
		<li class="mutelist">消息提醒
			<div class="u-switch">
				<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/btn_switch.png">
				<span class="off">off</span>
				<span class="on">on</span>
			</div>
		</li>
		<li class="blacklist">黑名单
			<div class="u-switch">
				<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/btn_switch.png">
				<span class="off">off</span>
				<span class="on">on</span>
			</div>
		</li>
		<li class="mute hide" id="setTeamMute">设置禁言
			<div class="u-switch">
				<img src="https://static.fangxiaoer.com/im_ziyuan/im/images/btn_switch.png">
				<span class="off">off</span>
				<span class="on">on</span>
			</div>
		</li>
	</ul>
	<div class="btns tc">
		<button class="btn btn-del radius4px j-del del">删除好友</button>
		<button class="btn btn-cancel radius4px j-add add">加为好友</button>
		<button class="btn btn-ok radius4px j-chat chat">聊天</button>
	</div>
</div>-->
<!-- 个人信息 -->
<div class="m-card m-card-1 hide" id="myInfo">
	<i class="icon icon-close j-close"></i>
	<div class="uInfo f-cb">
		<img class="u-icon modifyAvatar j-modifyAvatar" src="" alt="更换头像">
		<div class="desc">
			<p class="j-nickname nick">超级大饼</p>
			<img src="" class="j-gender gender">
			<p>
				<span class="j-username">帐号：caojidabin</span>
			</p>
		</div>
	</div>
	<div class="infos">
		<div class="operate">
			<span class="edit j-edit">编辑</span>
			<span class="save j-save">保存</span>
			<span class="cancel j-cancel"> 取消</span>
		</div>
		<div class="tt">基本信息</div>
		<div class="showUI">
			<div class="items">
				<div class="item">生日</div>
				<div class="j-birth">1990-08-18</div>
			</div>
			<div class="items">
				<div class="item">手机</div>
				<div class="j-tel">18072912974</div>
			</div>
			<div class="items">
				<div class="item">邮箱</div>
				<div class="j-email"><EMAIL> </div>
			</div>
			<div class="items">
				<div class="item">签名</div>
				<div class="j-sign sign">相信真善美,相信真善美相信真善美相信真善美,相信真善美,相信真善美,相信真善美,相信真善美,相信真善美</div>
			</div>
		</div>
		<div class="editUI">
			<div class="items">
				<div class="item">昵称</div>
				<input type="text" class="e-nick ipt" maxlength="10">
			</div>
			<div class="items">
				<div class="item">性别</div>
				<select class="e-gender slt">
					<option value="unknown">不显示</option>
					<option value="male">男</option>
					<option value="female">女</option>
				</select>
			</div>
			<div class="items">
				<div class="item">生日</div>
				<input type="text" class="e-birth ipt" maxlength="20" id="datepicker">
			</div>
			<div class="items">
				<div class="item">手机</div>
				<input type="text" class="e-tel ipt ipt-1" maxlength="13">
			</div>
			<div class="items">
				<div class="item">邮箱</div>
				<input type="text" class="e-email ipt ipt-1" maxlength="30">
			</div>
			<div class="items">
				<div class="item">签名</div>
				<textarea class="e-sign" maxlength="30"></textarea>
			</div>
		</div>
	</div>
</div>
<!-- 修改头像 -->
<div class="m-modifyAvatar hide" id="modifyAvatar">
	<i class="icon icon-close j-close"></i>
	<div class="choseFileCtn">
		<form action="#" class="j-uploadForm">
			<input type="file" class="j-upload" style="display: none;">
		</form>
		<div class="choseFile j-choseFile"> 选择图片</div>
	</div>
	<div class="tt">头像更换</div>
	<div class="chose" id="cropImg">
		<img src="" class="hide">
	</div>
	<div class="big">
		<div class="img">
			<img src="" class="hide">
		</div>
		<p>160*160</p>
	</div>
	<div class="small">
		<div class="img">
			<img src="" class="hide">
		</div>
		<p>40*40</p>
	</div>
	<div class="btns f-cb">
		<button class="f-fr btn btn-ok radius4px j-save">保存</button>
		<button class="f-fr btn btn-cancel radius4p j-reupload">重新选择</button>
		<button class="f-fr btn btn-cancel radius4p j-close">取消</button>
	</div>
</div>
<!--<div id="mask" class="mask hide"></div>-->
<script>
	// IE 11 polify
	if (!Object.assign) {
		Object.assign = function () {
			var list = arguments
			for (var i = 1; i < list.length; i++) {
				list[0] = extend(list[0], list[i])
			}
			return list[0]
		}

		function extend(obj1, obj2) {
			for (var i in obj2) {
				obj1[i] = obj2[i]
			}
			return obj1
		}
	}
	function closewarn(e) {
		$(".hd_chatTitle_warn").hide();
	}
	function closeContent(e) {
		$(".hd_warn_bg").hide();
	}
	function refresh(e) {
		$(".hd_warn_bg").hide();
		window.location.reload(true);
	}
	function changeClose1(e) {
		$(e).attr("src","https://static.fangxiaoer.com/im_ziyuan/im/images/hd_out2.png")
	}
	function changeClose2(e) {
		$(e).attr("src","https://static.fangxiaoer.com/im_ziyuan/im/images/hd_out3.png")
	}
</script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/nim_server_conf.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/NIM_Web_SDK_v5.4.0.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/NIM_Web_Netcall_v5.4.0.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/NIM_Web_WebRTC_v5.4.0.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/NIM_Web_WhiteBoard_v5.4.0.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/DrawPlugin.js"></script>

<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/3rd/whiteboard.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/jquery-1.11.3.min.js"></script>
<!--<script src="../3rd/zepto.min.js"></script>-->
<script>
	// FastClick.attach(document.body);
	window.onerror = function (errorMessage, source, lineno, colno, error) {
		var info = "错误信息：" + errorMessage + "\n" +
				"出错文件：" + source + "\n " +
				"出错行号：" + lineno + "\n" +
				"出错列号：" + colno + "\n" +
				"错误详情：" + error + "\n";

		// alert(JSON.stringify(ERROR));
		// ajax.postp(monitorUrl, ERROR, null, function(e){
		//     alert(e.stack);
		// });
		console.error(JSON.stringify(info));
		// ajax.post('/data/updateLog', ERROR);
		// alert(JSON.stringify(info))
		// minAlert.alert({
		// 	msg: JSON.stringify(info),
		// 	confirmBtnMsg: '好哒'
		// })
	}
</script>

<!-- <script src="../3rd/platform.js"></script> -->
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/rtcSupport.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/3rd/jquery-ui.min.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/rangeslider.min.js"></script>
<!-- 右键菜单-->
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/3rd/contextMenu/jquery.ui.position.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/3rd/contextMenu/jquery.contextMenu.js"></script>

<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/config.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/emoji.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/util.js?v=2"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/cache.js?v=2"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/link.js"></script>
<script src="https://static.fangxiaoer.com/js/esfIm_ziyuan/utilRecruit_new.js?v=2" type="text/javascript"></script>
<script src="https://static.fangxiaoer.com/js/esfIm_ziyuan/linkRecruit_new.js?v=4"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/ui.js?v=2"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/widget/uiKit.js?v=2"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/widget/minAlert.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/base_new.js?v=2"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/message.js?v=2"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/sysMsg.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/personCard.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/session.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/friend.js"></script>

<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/team.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/dialog_team.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/cloudMsg.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/notification.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/netcall.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/netcall_meeting.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/netcall_ui.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/dialog_call_method.js"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/main.js?v=2"></script>
<script src="https://static.fangxiaoer.com/im_ziyuan/3rd/vue.min.js"></script>
<script id="whiteboard-template" type="text/x-template">
	<div v-show="display" class="function-box whiteboard">
		<div v-if="banner.length" class="whiteboard-banner">
			<p>{{ banner }}</p>
		</div>
		<div v-if="tip.length" class="whiteboard-mask">
			<p>{{ tip }}</p>
		</div>
		<div v-if="bottomTip.length" class="whiteboard-bottom-tip">
			<p>{{ bottomTip }}</p>
		</div>
		<!-- P2P呼叫界面 -->
		<div v-if="!connected && isCalling">
			<img :src="avatar || 'https://static.fangxiaoer.com/im_ziyuan/im/images/default-icon.png'" alt="用户头像" class="avatar">
			<div class="nick">{{ nick }}</div>
			<div class="tip">{{ this.statusTip || '正在邀请对方，请稍候...' }} </div>
			<div class="op">
				<button @click="hangup" class="pure-button hangup">取消</button>
			</div>
		</div>
		<!-- P2P被叫界面 -->
		<div v-if="!connected && isCalled">
			<img :src="avatar || 'https://static.fangxiaoer.com/im_ziyuan/im/images/default-icon.png'" alt="用户头像" class="avatar">
			<div class="nick">{{ nick }}</div>
			<div class="tip">{{ this.statusTip || '邀请你进行白板互动' }}</div>
			<div v-if="needAgent" class="op download">
				<div>
					<button @click="downloadAgent" class="pure-button accept">下载插件</button>
				</div>
				<div>
					<button @click="reject" class="pure-button hangup">拒绝</button>
				</div>
				<div>
					<p>拒绝调用插件申请会导致无法唤起插件</p>
					<p>需重启浏览器</p>
				</div>
			</div>
			<div v-else class="op">
				<button @click="accept" class="pure-button accept">
					<span v-if="!waiting">接受</span>
					<span v-else class="netcall-icon-checking"></span>
				</button>
				<button @click="reject" class="pure-button hangup">拒绝</button>
			</div>
		</div>
		<!-- P2P白板界面 -->
		<div v-show="connected" class="whiteboard-ui pure-g">
			<div ref="container" class="pure-u-1-2 whiteboard-container"></div>
			<div class="pure-u-1-2 control-panel">
				<div class="pure-g btns">
					<div class="pure-u-1-3 control-btn">
						<div class="btn-tip">撤销</div>
						<span @click="undo" class="icon-undo"></span>
					</div>
					<div class="pure-u-1-3 control-btn">
						<div class="btn-tip">清除全部笔迹</div>
						<span @click="clear" class="icon-clear"></span>
					</div>
					<div class="pure-u-1-3 control-btn">
						<div v-if="!canWeUseMicro" class="alert-icon">!</div>
						<div v-if="!canWeUseMicro" class="btn-tip">麦克风不可用</div>
						<div v-else-if="isMicroOpen" class="btn-tip">关闭己方语音</div>
						<div v-else class="btn-tip">开启己方语音</div>
						<span @click="switchAudio" class="icon-mic" :class="{
								close: !isMicroOpen,
								disable: !canWeUseMicro
							}"></span>
					</div>
				</div>
				<button @click="hangup" class="pure-button hangup">结束</button>
			</div>
		</div>
		<div class="dialogs"></div>
	</div>

</script>
<script src="https://static.fangxiaoer.com/im_ziyuan/im/js/module/whiteboard.js"></script>
<th:block th:if="${#lists.size(info_im) != 0}" th:each="info_im:${info_im}">
	<input type="hidden" id="keeperTel2_im" th:value="${info_im.keeperTel2}">
	<input type="hidden" id="type_im" th:value="${info_im.type}">
	<input type="hidden" id="accid_im" th:value="${info_im.accid}">
	<input type="hidden" id="linkUrl_im" th:value="${info_im.linkUrl}">
	<input type="hidden" id="housePic_im" th:value="${info_im.housePic}">
	<input type="hidden" id="subName_im" th:value="${info_im.subName}">
	<input type="hidden" id="hallRoom_im" th:value="${info_im.hallRoom}">
	<input type="hidden" id="area_im" th:value="${info_im.area}">
	<input type="hidden" id="unit_im" th:value="${info_im.unit}">
	<input type="hidden" id="price_im" th:value="${info_im.price}">
	<input type="hidden" id="houseId_im" th:value="${info_im.houseId}">
	<input type="hidden" id="regionName_im" th:value="${info_im.regionName}">
	<input type="hidden" id="plateName_im" th:value="${info_im.plateName}">
	<input type="hidden" id="plateId_im" th:value="${info_im.plateId}">
</th:block>
<th:block th:if="${#lists.size(info_im_recruit) !=0}" th:each="info_im_recruit:${info_im_recruit}">
	<input type="hidden" id="keeperTel2_im_recruit" th:value="${info_im_recruit.keeperTel2}">
	<input type="hidden" id="accid_im_recruit" th:value="${info_im_recruit.accid}">
	<input type="hidden" id="linkUrl_im_recruit" th:value="${info_im_recruit.linkUrl}">
	<input type="hidden" id="jobAdId_im" th:value="${info_im_recruit.jobAdId}">
	<input type="hidden" id="typeName" th:value="${info_im_recruit.typeName}">
	<input type="hidden" id="jobTitle" th:value="${info_im_recruit.jobTitle}">
	<input type="hidden" id="type_im_recruit" th:value="${info_im_recruit.type}">
	<input type="hidden" id="plateName_im_recruit" th:value="${info_im_recruit.plateName}">
</th:block>
<th:block th:if="${#strings.toString(service_im_accid)}">
	<input type="hidden" id="service_im_accid" th:value="${service_im_accid}">
</th:block>
<script>

</script>
</body>
</html>