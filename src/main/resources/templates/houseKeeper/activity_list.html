<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>团队故事_优惠活动_购房优惠 - 房小二网</title>
    <meta name="keywords" content="团队故事,优惠活动,购房优惠,特价活动"/>
    <meta name="description" content="房小二网专题活动为您提供各类节日优惠活动，独家专属特惠，精品专题以供购房者了解与参考。"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/activity_list.css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/news/default.css?t=20170601" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/Housekeeper/Housekeeper.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/details.css" />

<!--    <script type="text/javascript">-->
<!--        $(function () {-->
<!--            var url = window.location.href-->
<!--            if (url.indexOf("keys=") != "-1") {-->
<!--                url = url.substring(url.indexOf("keys=") + 5, url.lenght);-->
<!--                $("#txtkeys").val(decodeURI(url));-->
<!--                $(".warning i").html(decodeURI(url))-->
<!--                var word = decodeURI(url)-->
<!--                $(".news p").each(function () {-->
<!--                    $(this).find("a").html($(this).find("a").html().replace(word, "<s>" + word+"</s>"))-->
<!--                })-->
<!--            }-->
<!--        })-->
<!--    </script>-->

    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>

</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1,searchButton='houseKepper'"></div>
<div class="current_location">
    <div class="content_box">
        <h1>您的位置：<a th:href="@{'/'}">沈阳房产网</a>&nbsp>&nbsp<a th:href="${'/houseKeeper1.htm'}">小二管家</a>&nbsp>&nbsp<a th:href="${'/festivalNews'}">团队故事</a></h1>
    </div>
</div>
<div class="big_box">
    <div class="content_box">
        <div class="left_box">
            <div class="list" th:if="${!#lists.isEmpty(festivalNews)}">
                <ul>
                    <li th:each="news:${festivalNews}">
                        <div class="name_box">
                            <div class="name">
                                <a target="_blank" th:href="${'/afestivalNews/'+news.xnid+'.htm'}">
                                    <h1 th:text="${#strings.isEmpty(news.title)?'':news.title}">【端午节】小二管家送温暖，带您看未来的家</h1>
                                </a>
                            </div>
                            <div class="time">
                                <h1 th:text="${#strings.isEmpty(news.addtime)?'':#strings.toString(news.addtime).replaceAll('\.','-')}">2018-6-16</h1>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="text">
                            <a target="_blank" th:href="${'/afestivalNews/'+news.xnid+'.htm'}">
                                <p th:text="${#strings.isEmpty(news.summary)?'':news.summary}">2018年6月16日，小二管家带领购房者，西部看房，本次看房团楼盘未来升值潜力都是非常巨大的，尤其丁香湖板块近期上涨趋势明显能够拉动附近周边区域房价，所以现在在于洪区域买房，是非常划算的...</p>
                            </a>
                        </div>
                        <div class="photo" th:if="${!#lists.isEmpty(news.photos)}">
                            <a target="_blank" th:href="${'/afestivalNews/'+news.xnid+'.htm'}">
                                <div class="list_sun" th:each="photo,pi:${news.photos}" th:if="${pi.index lt 4}">
                                    <img th:src="${#strings.isEmpty(photo.photoUrl)?'':photo.photoUrl}">
                                </div>
                                <div class="clearfix"></div>
                            </a>
                            <!--<div class="list_sun">-->
                            <!--<img src="https://static.fangxiaoer.com/web/images/Housekeeper/activity02.png">-->
                            <!--</div>-->
                            <!--<div class="list_sun">-->
                            <!--<img src="https://static.fangxiaoer.com/web/images/Housekeeper/activity03.png">-->
                            <!--</div>-->
                            <!--<div class="list_sun">-->
                            <!--<img src="https://static.fangxiaoer.com/web/images/Housekeeper/activity04.png">-->
                            <!--</div>-->
                            <!--<div class="clearfix"></div>-->
                        </div>
                    </li>

                </ul>
            </div>
        </div>
        <div class="right_box">
            <div class="phone">
                <h1>看房热线：</h1>
                <h2>************</h2>
                <button onclick="showPhonePop(2)">免费通话</button>
            </div>

            <div class="zsfw">
                <h1><span></span>帮您找房</h1>
                <ul>
                    <li>
                        <span>意向区域</span>
                        <div>
                            <select id="region">
                                <option>沈河区</option>
                                <option>大东区</option>
                                <option>皇姑区</option>
                                <option>和平区</option>
                                <option>铁西区</option>
                                <option>于洪区</option>
                                <option>浑南区</option>
                                <option>沈北新区</option>
                                <option>苏家屯</option>
                            </select>
                        </div>
                    </li>
                    <li class="new_huxing">
                        <span>意向户型</span>
                        <div>
                            <select id="new_huxing">
                                <option>一居</option>
                                <option>二居</option>
                                <option>三居</option>
                                <option>四居</option>
                                <option>五居及以上</option>
                            </select>
                        </div>
                    </li>
                    <li class="new_yusuan">
                        <span>预算价格</span>
                        <div>
                            <select id="new_yusuan">
                                <option>35万以下</option>
                                <option>35-50万</option>
                                <option>50-80万</option>
                                <option>80-100万</option>
                                <option>100-120万</option>
                                <option>120-150万</option>
                                <option>150万以上</option>
                            </select>
                        </div>
                    </li>
                    <li>
                        <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
                    </li>
                    <li>
                        <span>手机号码</span>
                        <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
                        <input type="hidden" id="type" value="1">
                    </li>
                    <li>
                        <span>验证码</span>
                        <input type="tel" id="code" class="fxe_messageCode" maxlength="6" style="width: 120px;" placeholder="请输入验证码">
                        <p id="hqyzm" class="fxe_ReSendValidateCoad">获取验证码</p>
                        <p class="fxe_validateCode"></p>
                    </li>
                    <div class="checkagreeInput" style="margin: 0 auto 10px auto;">
                        <div style="font-size:12px;">提交即代表同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                            <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                    </div>
                    <b class="btn" id="new_submit">提交</b>
                </ul>
                <div class="form_phone">客服咨询电话：************</div>
            </div>


            <div class="Rankings" th:if="${ranking != null and #lists.size(ranking) gt 0}">
                <div class="title">
                    <div class="line"></div>
                    <h1>热卖排行</h1>
                    <div class="clearfix"></div>
                </div>
                <div class="content">
                    <ul>
                        <li th:each="rank,i:${ranking}">
                            <a th:href="${rank.TargetUrl}" target="_blank">
                                <div class="number">
                                    <h1><span th:text="${i.count}">1</span>/</h1>
                                </div>
                                <div class="name">
                                    <h1  th:text="${#strings.isEmpty(rank.AdTitle)?'':rank.AdTitle}">中冶上和湾</h1>
                                    <div class="money">
                                        <h1 th:if="${rank.ADPropriceyh ne '0'}">￥<span th:text="${#strings.isEmpty(rank.ADPropriceyh)?'':rank.ADPropriceyh}">10000</span>元/㎡</h1>
                                        <h1 th:if="${rank.ADPropriceyh eq '0'}"><span>待定</span></h1>
                                    </div>
                                    <div class="clearfix"></div>
                                    <h2 th:text="${#strings.isEmpty(rank.ADproAdderss)?'':rank.ADproAdderss}">和平</h2>
                                </div>

                            </a>
                            <div class="clearfix"></div>
                        </li>

                    </ul>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
        <div class="page">
            <div th:include="fragment/page :: page"></div>
        </div>
        <div class="clearfix"></div>
    </div>
</div>
<script>
    $(".cheimg5").click(function () {
        if($(this).hasClass("checked")){
            $(this).removeClass("checked")
        }else{
            $(this).addClass("checked")
        }
    })
</script>
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<!--弹窗-->
<div th:include="house/detail/fragment_order::houseKeeper_freeCall"></div>
</body>
</html>