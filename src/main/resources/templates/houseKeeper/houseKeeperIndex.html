<!DOCTYPE html>
<html lang="en"
      xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>小二管家_购房指导_置业顾问 - 房小二网</title>
    <meta name="keywords" content="沈阳买房,沈阳置业顾问,沈阳房源,房源推荐" />
    <meta name="description" content="房小二网小二管家为您购房提供全程一对一贴心服务，包括房源推荐、购房指导、顾问式陪看房、交易磋商、合同签约服务，为你精准制定专属购房计划，比你更懂你自己。" />
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/Housekeeper.css?V=20181225" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/Housekeeper/Housekeeper.js" type="text/javascript" charset="utf-8"></script>
</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<div class="banner">

</div>
<div class="current_location">
    <div class="content_box">
        <h1>当前位置：<a href="/">沈阳房产网</a>&nbsp>&nbsp<a href="/freeServiceIndex/">服务</a>&nbsp>&nbsp<a href="/houseKeeper1.htm/">小二管家</a></h1>
    </div>
</div>
<div class="process">
    <div class="content_box">
        <div class="title_box">
            <h1>先领券再买房，点一下就“ 购 ”了！</h1>
            <div class="line"></div>
        </div>
        <div class="content">
            <ul>
                <li>
                    <div class="photo">
                        <img src="https://static.fangxiaoer.com/web/images/Housekeeper/process01.png">
                    </div>
                    <div class="name">
                        <h1>领取优惠券</h1>
                    </div>
                    <div class="text">
                        <p>点击立即领取，输入真实姓名和手机电话号码，<br>
                            注册成功后，即可领取优惠券。<br>
                            （优惠券以电子码的形式存放于账户内）</p>
                    </div>
                </li>
                <li>
                    <div class="photo">
                        <img src="https://static.fangxiaoer.com/web/images/Housekeeper/process02.png">
                    </div>
                    <div class="name">
                        <h1>尊享小二管家服务</h1>
                    </div>
                    <div class="text">
                        <p>成功领取优惠券后的三十分钟内，房小二网的专业小<br>二管家电话联系您，为您讲解优惠卷使用规则并确定<br>您何时到现场看房、购房。</p>
                    </div>
                </li>
                <li>
                    <div class="photo">
                        <img src="https://static.fangxiaoer.com/web/images/Housekeeper/process03.png">
                    </div>
                    <div class="name">
                        <h1>享受购房优惠</h1>
                    </div>
                    <div class="text">
                        <p>前往售楼处，通过房小二网现场工作人员指引、认证优惠券信息无误后，购房享受相应优惠。</p>
                    </div>
                </li>
                <div class="clearfix"></div>
            </ul>
        </div>
        <div class="list" th:if="${!#lists.isEmpty(houseKeeperInfo.activities)}">
            <uL>
                <li th:each="activity:${houseKeeperInfo.activities}" th:class="${#strings.toString(activity.state) eq '1'? 'color':''}">
                    <div class="photo">
                        <img th:src="${activity.AppPic}">
                    </div>
                    <div class="information">
                        <div class="name">
                            <h1 th:text="${activity.ProjectName}"></h1>
                        </div>
                        <div class="Price">
                            <th:block th:if="${activity.price ne null }">
                                <h1><b th:text="${activity.price.priceType}"></b>：<span th:text="${#strings.substring(activity.price.priceMoney,0,#strings.indexOf(activity.price.priceMoney,'.'))}+'元/㎡'"></span></h1>
                            </th:block>
                            <th:block th:if="${activity.price eq null }">
                                <h1><span>待定</span></h1>
                            </th:block>
                        </div>
                        <div class="huxing">
                            <h1>主力户型：<span th:title="${#strings.length(activity.layoutStr) gt 20 ? #strings.toString(activity.layoutStr).replace('-',' '):''}"
                                           th:text="${activity.layoutStr eq ''? '暂无资料':(#strings.length(activity.layoutStr) gt 20 ? #strings.toString(activity.layoutStr).replace('-',' ').substring(0,14)+'...': #strings.toString(activity.layoutStr).replace('-',' '))}"></span></h1>
                        </div>
                        <div class="address">
                            <h1>地址：<span th:text="${activity.address}"></span></h1>
                        </div>
                    </div>
                    <div class="activity">
                        <h1></h1>
                        <h2><span th:text="${activity.FaceValue}"></span>元</h2>
                        <h3>活动时间：</h3>
                        <h4><span th:text="${activity.StarTime}"></span>-<span th:text="${activity.EndTime}"></span></h4>
                        <button th:text="${#strings.toString(activity.state) eq '1'?'已领取':''}" th:class="${#strings.toString(activity.state) eq '1'?'':'canClick'}"></button>
                        <input type="hidden" id="ActivityID" th:value="${activity.ActivityID}">
                        <input type="hidden" id="ActAviliable" th:value="${activity.avaliable}">
                    </div>
                    <div class="clearfix"></div>
                </li>
                <div class="clearfix"></div>
            </uL>
        </div>
        <div class="btn" th:if="${!#lists.isEmpty(houseKeeperInfo.activities)}">
            <div class="content_sun">
                <div class="text">
                    <ul>
                        <li></li>
                        <li></li>
                        <li></li>
                        <div class="clearfix"></div>
                    </ul>
                    <a th:href="@{'/viewManagerDiscount'}" target="_blank">点击查看更多优惠</a>
                    <ul>
                        <li></li>
                        <li></li>
                        <li></li>
                        <div class="clearfix"></div>
                    </ul>
                    <div class="clearfix"></div>
                </div>

            </div>
        </div>
    </div>
</div>
<div class="Housekeeper">
    <div class="content_box">
        <div class="title_box">
            <h1>专业的技能，贴心的服务，小二管家为您精挑合适房源！</h1>
            <div class="line"></div>
        </div>
        <div class="switch">
            <ul>
                <li>
                    <h1>小二管家专业团队</h1>
                    <div class="line"></div>
                </li>
                <li>
                    <h1>安心服务保障</h1>
                    <div class="line"></div>
                </li>
                <div class="clearfix"></div>
            </ul>
        </div>
        <div class="list_box">
            <ul>
                <li>
                    <div class="title_sun">
                        <h1>【区域专家】</h1>
                    </div>
                    <div class="list_sun" th:if="${!#lists.isEmpty(houseKeeperInfo.xiaoers.feature1)}">
                        <div class="list" th:each="xiaoer:${houseKeeperInfo.xiaoers.feature1}">
                            <a th:href="${'/agent/second/'+xiaoer.memberId}" target="_blank">
                                <div class="photo">
                                    <img th:src="${#strings.isEmpty(xiaoer.headPic)?'':xiaoer.headPic}" th:alt="${#strings.isEmpty(xiaoer.memberName)?'':xiaoer.memberName}">
                                </div>
                                <div class="text">
                                    <h1 th:text="${#strings.isEmpty(xiaoer.memberName)?'':xiaoer.memberName}">张王李</h1><span th:text="${#strings.isEmpty(xiaoer.feature)?'':'【'+xiaoer.feature+'】'}"></span>
                                    <div class="clearfix"></div>
                                    <h2 th:text="${#strings.isEmpty(xiaoer.mobile)?'':xiaoer.mobile}"></h2>
                                </div>
                            </a>
                        </div>
                        <div class="clearfix"></div>
                    </div>

                </li>
                <li>
                    <div class="title_sun">
                        <h1>【投资专家】</h1>
                    </div>
                    <div class="list_sun">
                        <div class="list_sun" th:if="${!#lists.isEmpty(houseKeeperInfo.xiaoers.feature2)}">
                            <div class="list" th:each="xiaoer:${houseKeeperInfo.xiaoers.feature2}">
                                <a th:href="${'/agent/second/'+xiaoer.memberId}" target="_blank">
                                    <div class="photo">
                                        <img th:src="${#strings.isEmpty(xiaoer.headPic)?'':xiaoer.headPic}" th:alt="${#strings.isEmpty(xiaoer.memberName)?'':xiaoer.memberName}">
                                    </div>
                                    <div class="text">
                                        <h1 th:text="${#strings.isEmpty(xiaoer.memberName)?'':xiaoer.memberName}"></h1>
                                        <h2 th:text="${#strings.isEmpty(xiaoer.mobile)?'':xiaoer.mobile}"></h2>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>

                </li>
                <li>
                    <div class="title_sun">
                        <h1>【房产政策专家】</h1>
                    </div>
                    <div class="list_sun" th:if="${!#lists.isEmpty(houseKeeperInfo.xiaoers.feature3)}">
                        <div class="list" th:each="xiaoer:${houseKeeperInfo.xiaoers.feature3}">
                            <a th:href="${'/agent/second/'+xiaoer.memberId}" target="_blank">
                                <div class="photo">
                                    <img th:src="${#strings.isEmpty(xiaoer.headPic)?'':xiaoer.headPic}" th:alt="${#strings.isEmpty(xiaoer.memberName)?'':xiaoer.memberName}">
                                </div>
                                <div class="text">
                                    <h1 th:text="${#strings.isEmpty(xiaoer.memberName)?'':xiaoer.memberName}"></h1>
                                    <h2 th:text="${#strings.isEmpty(xiaoer.mobile)?'':xiaoer.mobile}"></h2>
                                </div>
                            </a>
                        </div>
                    </div>

                </li>
                <li>
                    <div class="title_sun">
                        <h1>【高端服务专家】</h1>
                    </div>
                    <div class="list_sun" th:if="${!#lists.isEmpty(houseKeeperInfo.xiaoers.feature4)}">
                        <div class="list" th:each="xiaoer:${houseKeeperInfo.xiaoers.feature4}">
                            <a th:href="${'/agent/second/'+xiaoer.memberId}" target="_blank">
                                <div class="photo">
                                    <img th:src="${#strings.isEmpty(xiaoer.headPic)?'':xiaoer.headPic}" th:alt="${#strings.isEmpty(xiaoer.memberName)?'':xiaoer.memberName}">
                                </div>
                                <div class="text">
                                    <h1 th:text="${#strings.isEmpty(xiaoer.memberName)?'':xiaoer.memberName}"></h1>
                                    <h2 th:text="${#strings.isEmpty(xiaoer.mobile)?'':xiaoer.mobile}"></h2>
                                </div>
                            </a>
                        </div>
                    </div>

                </li>
                <div class="clearfix"></div>
            </ul>
            <div class="btn">
                <div class="content_sun">
                    <div class="text">
                        <ul>
                            <li></li>
                            <li></li>
                            <li></li>
                            <div class="clearfix"></div>
                        </ul>
                        <a th:href="@{'/keeperMember'}" target="_blank">点击查看更多</a>
                        <ul>
                            <li></li>
                            <li></li>
                            <li></li>
                            <div class="clearfix"></div>
                        </ul>
                        <div class="clearfix"></div>
                    </div>

                </div>
            </div>
        </div>
        <div class="list_box">
            <div class="content_sun">
                <div class="name">
                    <h1>小二管家服务内容</h1>
                </div>
                <div class="left_box">
                    <img src="https://static.fangxiaoer.com/web/images/Housekeeper/guarantee.png">
                </div>
                <div class="right_box">

                    <div class="list">
                        <ul>
                            <li>
                                <p>（1）根据您的购房需求，为您量身定制购房方案。</p>
                            </li>
                            <li>
                                <p>（2）开发商背景以及房源优劣客观分析。</p>
                            </li>
                            <li>
                                <p>（3）买房过程中对购房风险进行阳光提示。</p>
                            </li>
                            <li>
                                <p>（4）房小二网免费看房车为您车接车送。</p>
                            </li>
                            <li>
                                <p>（5）确保售楼处底价成交，买贵10倍补差价。</p>
                            </li>
                            <li>
                                <p>（6）额外获得房小二网小二管家提供的购房优惠券。</p>
                            </li>
                            <li>
                                <p>（7）投诉电话：************。</p>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="clearfix"></div>
            </div>
            <div class="process">
                <div class="name">
                    <h1>小二管家服务流程</h1>
                </div>
                <div class="text_sun">
                    <ul>
                        <li>
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/process_sun01.png">
                            <h1>联系小二管家<br>
                                定制购房方案</h1>
                        </li>
                        <li></li>
                        <li style="width: 126px">
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/process_sun02.png">
                            <h1>看房车带您实地看房<br>
                                优劣分析</h1>
                        </li>
                        <li style="margin-left: 43px;margin-right: 43px"></li>
                        <li>
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/process_sun03.png">
                            <h1>成功订房</h1>
                        </li>
                        <li></li>
                        <li>
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/process_sun04.png">
                            <h1>获得房小二网<br>
                                额外优惠</h1>
                        </li>
                        <li></li>
                        <li>
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/process_sun05.png">
                            <h1>享受终身<br>
                                房产咨询服务</h1>
                        </li>
                        <div class="clearfix"></div>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="Deal" th:if="${!#lists.isEmpty(houseKeeperInfo.dealstories)}">
    <div class="content_box">
        <div class="title_box">
            <h1>海量成交故事，倾听购房者内心深处的秘密</h1>
        </div>
        <div class="content">
            <div class="btn_left">
                <img src="https://static.fangxiaoer.com/web/images/Housekeeper/left.png">
            </div>
            <div class="show">
                <div class="lunbo">
                    <ul id="storiesList">
                        <li>
                            <div class="list" th:each="dealstoriy:${houseKeeperInfo.dealstories}">
                                <img th:src="${#strings.isEmpty(dealstoriy.photo)?'':dealstoriy.photo}" >
                                <div class="text">
                                    <img src="https://static.fangxiaoer.com/web/images/Housekeeper/Select.png" class="Choose">
                                    <p th:text="${#strings.isEmpty(dealstoriy.summary)?'':dealstoriy.summary}" >记得是5月12日在网上浏览房源信息看到了十里春风的楼盘，留了联系方式，13日xxx就联系到我，及时有效地跟我个普通了房源信息，确认了我的购房资质，次日14号约了专车来接我到了宏发英里，介绍了详细的情况，当天到深夜，还准备了饮料和毯子，服务非常周到，贴心。成功选到心仪的楼层之后，又帮我跟开发商积极沟通了公积金贷款示意，解决了不少困难，非常感谢！无论是xxx本人的专业性和服务态度，还是房小二网，都是非常靠谱！</p>
                                    <h1><soan th:text="${#strings.isEmpty(dealstoriy.showtime)?'':'时间：'+#dates.format(dealstoriy.showDate,'yyyy年MM月dd日')}">时间：2018年6月26日    </soan><a target="_blank" th:href="${'/agent/second/'+dealstoriy.memberId}"><span  th:text="${#strings.isEmpty(dealstoriy.xeName)?'':' 服务人：小二管家 '+dealstoriy.xeName}"> 服务人：小二管家 xxx</span></a> </h1>
                                </div>
                                <div class="name">
                                    <h1  th:text="${#strings.isEmpty(dealstoriy.dealname)?'':'购房者：'+dealstoriy.dealname}" >购房者：张王李</h1>
                                    <h2 th:text="${#strings.isEmpty(dealstoriy.projectname)?'':'购买楼盘：'+dealstoriy.projectname}" >购买楼盘：宏发英里</h2>
                                </div>
                            </div>
                            <div class="clearfix"></div>
                        </li>

                    </ul>
                </div>
            </div>
            <div class="btn_right">
                <img src="https://static.fangxiaoer.com/web/images/Housekeeper/right.png">
            </div>
            <div class="clearfix"></div>
        </div>
    </div>
</div>
<div class="team_box">
    <div class="content_box">
        <div class="Free ">
            <div class="title_box">
                <h1>免费看房团</h1>
                <button onclick="showPhonePop(1)">免费专车</button>
                <h2>看房专线：************</h2>
                <div class="clearfix"></div>
                <div class="line"></div>
            </div>
            <div class="content">
                <div class="photo">
                    <img src="" class="bdg">

                    <div class="btn_sun">
                        <div class="left">
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/left02.png" >
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/left02_sun.png" class="hover">
                        </div>
                        <div class="show">
                            <div class="lunbo">
                                <ul id="guideTour">

                                    <div class="clearfix"></div>
                                </ul>
                            </div>
                        </div>
                        <div class="right">
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/right02.png" >
                            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/right02_sun.png"class="hover" >
                        </div>
                        <div class="clearfix"></div>
                    </div>
                </div>
                <div class="text" th:if="${!#lists.isEmpty(houseKeeperInfo.xiaoerguides)}">
                    <h1 th:text="${#strings.isEmpty(houseKeeperInfo.xiaoerguides.get(0).title)?'':houseKeeperInfo.xiaoerguides.get(0).title}">3月31日西线看房团圆满成功</h1>
                    <p  th:text="${#strings.isEmpty(houseKeeperInfo.xiaoerguides.get(0).summary)?'':houseKeeperInfo.xiaoerguides.get(0).summary}">       桃花满城绽放，初春悄然而至，外出踏青者渐多，看房购房者亦日渐活络。本周六的路线安排在西线，为帮助购房者尽量全面的了解沈阳房产市场，看到更多的项目情况，我们的路线及楼盘选择更加多样，但保证质量兼备，以本次路线为例区域内品牌地产
                        项目优先选择，因此本次看房团中多个项目具有多组意向客户核算房价，咨询细节。桃花满城绽放，初春悄然而至渐活络。因此本次看房团中多个项目具有多组意向客户核算房价，咨询细节。</p>
                </div>
                <div class="clearfix"></div>
                <div class="btn" th:if="${!#lists.isEmpty(houseKeeperInfo.xiaoerguides)}">
                    <div class="content_sun">
                        <div class="text_sun">
                            <ul>
                                <li></li>
                                <li></li>
                                <li></li>
                                <div class="clearfix"></div>
                            </ul>
                            <a th:href="@{'/viewLookHouse'}" target="_blank">点击查看更多</a>
                            <ul>
                                <li></li>
                                <li></li>
                                <li></li>
                                <div class="clearfix"></div>
                            </ul>
                            <div class="clearfix"></div>
                        </div>

                    </div>
                </div>
                <div class="process">

                </div>
            </div>
            <div class="Technological">
                <div class="top_box">
                    <ul>
                        <li><h1>1 报名</h1></li>
                        <li><h1>2 确认</h1></li>
                        <li><h1>3 接送</h1></li>
                        <li><h1>4 看房</h1></li>
                        <li><h1>5 回程</h1></li>
                        <div class="clearfix"></div>
                    </ul>
                </div>
                <div class="bottom_box">
                    <ul>
                        <li>
                            <div class="name">
                                <img src="https://static.fangxiaoer.com/web/images/Housekeeper/look_01.png">
                                <h1>全程免费</h1>
                                <div class="clearfix"></div>
                            </div>
                            <div class="text">
                                <p>预约看房，车接车送，全程不收取任何费用。看房成本由房小二网独家承担。</p>
                            </div>
                        </li>
                        <li>
                            <div class="name">
                                <img src="https://static.fangxiaoer.com/web/images/Housekeeper/look_02.png">
                                <h1>专业讲解</h1>
                                <div class="clearfix"></div>
                            </div>
                            <div class="text">
                                <p>车上提供看房路线详情和沈城楼市报，另有小二管家提供专业房产类解读。</p>
                            </div>
                        </li>
                        <li>
                            <div class="name">
                                <img src="https://static.fangxiaoer.com/web/images/Housekeeper/look_03.png">
                                <h1>大巴接送</h1>
                                <div class="clearfix"></div>
                            </div>
                            <div class="text">
                                <p>固定地点签到集合，大巴车接送参观项目，准时安心。</p>
                            </div>
                        </li>
                        <li>
                            <div class="name">
                                <img src="https://static.fangxiaoer.com/web/images/Housekeeper/look_04.png">
                                <h1>独家补贴</h1>
                                <div class="clearfix"></div>
                            </div>
                            <div class="text">
                                <p>此服务由房小二网小二管家精心打造，确保售楼处底价成交，额外有小二管家独家优惠劵。</p>
                            </div>
                        </li>

                        <div class="clearfix"></div>
                    </ul>
                </div>
            </div>
        </div>
        <div class="welfare">
            <div class="title_sun">
                <h1>专题活动</h1>
                <ul>
                    <li></li>
                    <li></li>
                    <li></li>
                    <div class="clearfix"></div>
                </ul>
                <div class="clearfix"></div>
                <div class="line"></div>
            </div>
            <div class="content_sun">
                <div class="show">
                    <div class="lunbo">
                        <ul>
                            <li th:each="xiaoerfestival:${houseKeeperInfo.xiaoerfestivals}">
                                <div class="photo">
                                    <a th:href="${'/afestivalNews/'+xiaoerfestival.xnid+'.htm'}" target="_blank">
                                    <img th:src="${#strings.isEmpty(xiaoerfestival.photo)?'':xiaoerfestival.photo}" th:alt="${#strings.isEmpty(xiaoerfestival.projectname)?'':xiaoerfestival.projectname}">
                                    </a>
                                </div>
                                <div class="text">
                                    <a th:href="${'/afestivalNews/'+xiaoerfestival.xnid+'.htm'}" target="_blank">
                                        <h1 th:text="${#strings.isEmpty(xiaoerfestival.title)?'':xiaoerfestival.title}">感恩母亲节，小二管家送温暖</h1>
                                    </a>
                                    <p th:text="${#strings.isEmpty(xiaoerfestival.summary)?'':xiaoerfestival.summary}">      桃花满城绽放，初春悄然而至，外出踏青者渐多，看房购房者亦日渐活络。本周六的路线安排在西线，为帮助购房者尽量全面的了解沈阳房有多组意向客户核算房价，咨询细节。</p>
                                </div>

                            </li>
                            <div class="clearfix"></div>
                        </ul>
                    </div>
                </div>
                <div class="btn_sun" th:if="${!#lists.isEmpty(houseKeeperInfo.xiaoerfestivals)}">
                    <div class="content_sun">
                        <div class="text_sun">
                            <ul>
                                <li></li>
                                <li></li>
                                <li></li>
                                <div class="clearfix"></div>
                            </ul>
                            <a th:href="@{'/festivalNews'}" target="_blank">点击查看全部</a>
                            <ul>
                                <li></li>
                                <li></li>
                                <li></li>
                                <div class="clearfix"></div>
                            </ul>
                            <div class="clearfix"></div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>
</div>
<script th:inline="javascript">
    var seeBusImages = [[${houseKeeperInfo.xiaoerguides.get(0).photos}]];
    var j = 0;
    var h=0;
    var q=0;
    for (j ; j <  seeBusImages.length; j++){

        if(j%5 == 0){
            $(".team_box .content_box .Free .content .photo .btn_sun .show ul .clearfix").before("<li></li>");

        }
        if(h==0){
            $(".team_box .content_box .Free .content .photo .btn_sun .show ul li").eq(q).append("<div class='list_sun'><img src='"+seeBusImages[j].photoUrl+"'></div>");
        }else{
            if(0==h%5){
                q++
            }
            $(".team_box .content_box .Free .content .photo .btn_sun .show ul li").eq(q).append("<div class='list_sun'><img src='"+seeBusImages[j].photoUrl+"'></div>");
        }
        h++
    }
</script>
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<!--弹窗-->
<div th:include="house/detail/fragment_order::houseKeeper_freeCall"></div>
<!--抢优惠弹窗-->
<div th:include="house/detail/fragment_activity::orderPrivActivity"></div>
</body>
<script>
    var dealStoCurrentPage = 2;
    var dealStoMaxLength = 0;
    var dealStoMaxPage = 2;
    //加载成交故事
    function addStoris(nextPage) {
        var params = {page:nextPage,pageSize:5};
        if(dealStoMaxPage >= dealStoCurrentPage) {
            $.ajax({
                type: "post",
                url: "/getDaelstories",
//						async:false,
                data: JSON.stringify(params),
                headers: {
                    'Content-Type': 'application/json;charset=utf-8'
                },
                success: function (data) {
                    if (data.status == 1) {
                        var content = data.content;
                        if(content.length>0){
                            var htmlElements = "<li>";
                            for (var i = 0; i < content.length; i++) {
                                htmlElements = htmlElements + "<div class='list'><img src='" + content[i].photo + "'>"
                                    + "<div class='text'>" +
                                    "<img src='https://static.fangxiaoer.com/web/images/Housekeeper/Select.png' class='Choose'>" +
                                    " <p>" + content[i].summary + "</p>"
                                    + "<h1><soan>时间：" + formatDateTime(content[i].showtime) + "</soan><a target='_blank' href='/agent/second/"+content[i].memberId+"'><span> 服务人：小二管家 " + content[i].xeName + "</span></a> </h1></div>"
                                    + "<div class='name'><h1>购房者：" + content[i].dealname + "</h1><h2>购买楼盘：" + content[i].projectname + "</h2></div></div>"
                            }
                            htmlElements = htmlElements + "</li>"
                            $("#storiesList").append(htmlElements);

                        }
                        $(".Deal .content_box .show .lunbo").width(liw*($(".Deal .content_box .show ul li").length+1))
                        dealStoMaxLength = data.msg;
                        calculatePage(dealStoMaxLength);
                        for (var i=0; i<$(".Deal .content_box .show ul li").length;i++){
                            $(".Deal .content_box .show ul li").eq(i).find(" .list:eq(0) .text").show()
                            $(".Deal .content_box .show ul li").eq(i).find(" .list:eq(0)").addClass("color")

                        }
                    }
                },
                error: function (data) {
                    alert("服务器繁忙请稍后重试")
                    console.log(data)
                }
            });
        }
    }
    //时间处理
    function formatDateTime(date) {
        if(date!="" && date!= null && date != undefined){

            var value = date.split('.')
            return value[0] + '年' + value[1] + '月' + value[2] + '日';  //可自行改变时间格式
        }
    };
    function calculatePage(dealStoMaxLength) {
        if(dealStoMaxLength != 0){
            len = dealStoMaxLength%5;
            if(len == 0){
                dealStoMaxPage = dealStoMaxLength/5;
            }else {
                dealStoMaxPage = parseInt(dealStoMaxLength/5)+1;
            }
        }
    }
    $(document).ready(function () {
        addStoris(dealStoCurrentPage);

    });
</script>
</html>