<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title >房小二网看房团,沈阳看房团,免费看房 - 房小二网</title>
    <meta name="keywords"
          content="房小二网看房团,沈阳看房团,免费看房,看房"/>
    <meta name="description"
          content="房小二网看房团全城招募中，免费空调大巴、全程置业指导、专属独家优惠，全沈城优质楼盘，房小二网将为您专属定制，让您的每一次看房之旅都不虚此行。"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/review.css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/layer.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/Housekeeper/Housekeeper.js" type="text/javascript" charset="utf-8"></script>
</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1,searchButton='houseKepper'"></div>
<div class="current_location">
    <div class="content_box">
        <h1>当前位置：<a href="/">沈阳房产网</a>&nbsp>&nbsp<a href="/houseKeeper1.htm">小二管家</a>&nbsp>&nbsp<a href="/viewLookHouse">看房团</a></h1>
    </div>
</div>
<div class="big_box">
    <div class="content_box">
        <div class="left_box">
            <div class="title_sun">
                <h1>往期看房团回顾</h1>
                <h2>共有<span th:text="${totalNum}"></span>个沈阳往期看房团</h2>
                <div class="clearfix"></div>
            </div>
            <div class="content_sun">
                <ul>
                    <li th:each="one:${content}">
                        <div class="photo">
                                <img th:src="${one.photo eq null or one.photo eq '' ? 'https://static.fangxiaoer.com/web/images/Housekeeper/none.jpg':one.photo}">
                        </div>
                        <div class="name">
                            <h1 th:text="${one.title}"></h1>
                        </div>
                        <input type="hidden" th:value="${one.xnid}" class="thisId">
                    </li>

                    <div class="clearfix"></div>
                </ul>
            </div>

        </div>
        <div class="right_box">
            <div class="phone">
                <h1>看房热线：</h1>
                <h2>************</h2>
                <button onclick="showPhonePop(2)">免费通话</button>
            </div>
            <div class="Rankings" th:if="${ranking != null and #lists.size(ranking) gt 0}">
                <div class="title">
                    <div class="line"></div>
                    <h1>热卖排行</h1>
                    <div class="clearfix"></div>
                </div>
                <div class="content" >
                    <ul>
                        <li th:each="rank,i:${ranking}">
                            <a th:href="${rank.TargetUrl}" target="_blank">
                                <div class="number">
                                    <h1><span th:text="${i.count}">1</span>/</h1>
                                </div>
                                <div class="name">
                                    <h1  th:text="${#strings.isEmpty(rank.AdTitle)?'':rank.AdTitle}">中冶上和湾</h1>
                                    <div class="money">
                                        <h1 th:if="${rank.ADPropriceyh ne '0'}">￥<span th:text="${#strings.isEmpty(rank.ADPropriceyh)?'':rank.ADPropriceyh}">10000</span>元/㎡</h1>
                                        <h1 th:if="${rank.ADPropriceyh eq '0'}"><span>待定</span></h1>
                                    </div>
                                    <div class="clearfix"></div>
                                    <h2 th:text="${#strings.isEmpty(rank.ADproAdderss)?'':rank.ADproAdderss}">和平</h2>
                                </div>

                            </a>
                            <div class="clearfix"></div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>
</div>
<div class="review_layer">
<table>
    <tr>
        <td>
            <div class="layer">
                <div class="close">
                    <img src="https://static.fangxiaoer.com/web/images/Housekeeper/close.png">
                </div>
                <iframe id="onlyIframe" src=""></iframe>
            </div>
        </td>
    </tr>
</table>
</div>
<!--页码-->
<div class="page">
    <div th:include="fragment/page :: page"></div>
</div>
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<!--弹窗-->
<div th:include="house/detail/fragment_order::houseKeeper_freeCall"></div>
<script>
    $(".big_box .content_box .left_box .content_sun ul li").click(
        function(){
            var xnid = $(this).find(".thisId").val();
            $("#onlyIframe").attr("src","/lookHouseDetail/"+xnid)
            $(".review_layer").show()
        }
    )
    $(".review_layer table .layer .close").click(
        function(){
            $(".review_layer").hide()
            $("#onlyIframe").attr("src","about:blank");
        }
    )
    $(".review_layer").click(
        function () {
            $(".review_layer").hide()
            $("#onlyIframe").attr("src","about:blank");
        }
    )
</script>
</body>
</html>