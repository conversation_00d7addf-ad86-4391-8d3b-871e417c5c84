<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>小二管家_成交故事 - 房小二网</title>
    <meta name="keywords" content="沈阳买房,沈阳置业顾问,沈阳房源,房源推荐"/>
    <meta name="description" content="房小二网小二管家成交故事，海量成交故事，倾听消费者内心深处的秘密">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/WaiterHousekeeper.css?v=20190507" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/saleTrends.css?v=20180706" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
    <link type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/Villa_index.css?v=20181023" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/details.css" />

</head>
<body style="background-color: #FFFFFF !important;">
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>

<div class="housekeeper-condotour-banner">
    <p>此刻，累计帮助<span class="timer count-title" data-to="" data-speed="3500">1,532,900</span>个家庭实现安居梦想</p>
</div>
<script th:inline="javascript">
    //设置计数
     var familyCount = [[${familyCount}]]
    $.fn.countTo = function(options) {
        options = options || {};
        return $(this).each(function() {
            //当前元素的选项
            var settings = $.extend({}, $.fn.countTo.defaults, {
                from: $(this).data('from'),
                to: familyCount,
                speed: $(this).data('speed'),
                refreshInterval: $(this).data('refresh-interval'),
                decimals: $(this).data('decimals')
            }, options);
            //更新值
            var loops = Math.ceil(settings.speed / settings.refreshInterval),
                increment = (settings.to - settings.from) / loops;
            //更改应用和变量
            var self = this,
                $self = $(this),
                loopCount = 0,
                value = settings.from,
                data = $self.data('countTo') || {};
            $self.data('countTo', data);
            //如果有间断，找到并清除
            if (data.interval) {
                clearInterval(data.interval);
            };
            data.interval = setInterval(updateTimer, settings.refreshInterval);
            //初始化起始值
            render(value);
            function updateTimer() {
                value += increment;
                loopCount++;
                render(value);
                if (typeof(settings.onUpdate) == 'function') {
                    settings.onUpdate.call(self, value);
                }
                if (loopCount >= loops) {
                    //移出间隔
                    $self.removeData('countTo');
                    clearInterval(data.interval);
                    value = settings.to;
                    if (typeof(settings.onComplete) == 'function') {
                        settings.onComplete.call(self, value);
                    }
                }
            }
            function render(value) {
                var formattedValue = settings.formatter.call(self, value, settings);
                $self.html(formattedValue);
            }
        });
    };
    $.fn.countTo.defaults = {
        from: 0, //数字开始的值
        to: 0, //数字结束的值
        speed: 90000, //设置步长的时间
        refreshInterval: 100, //隔间值
        decimals: 0, //显示小位数
        formatter: formatter, //渲染之前格式化
        onUpdate: null, //每次更新前的回调方法
        onComplete: null //完成更新的回调方法
    };

    function formatter(value, settings) {
        return value.toFixed(settings.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
    }
    //自定义格式
    $('#count-number').data('countToOptions', {
        formmatter: function(value, options) {
            return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
        }
    });
    //定时器
    $('.timer').each(count);
    function count(options) {
        var $this = $(this);
        options = $.extend({}, options || {}, $this.data('countToOptions') || {});
        $this.countTo(options);
    }
    var timeout1 = null;
</script>
<!--小二管家-看房团-banner-->
<!--小二管家-看房团-5分栏-->
<div class="housekeeper-condotour-tab">
    <div class="tab">
        <a th:href="@{'/houseKeeper1.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda1.png" />
                </h1>
                <p>我们的团队</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper2.htm'}">
            <div class="tab-mask  ">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda2.png" />
                </h1>
                <p>我们的看房团</p>
                <P>免费看房团<br> 带你一站式看房</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper3.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda3.png" />
                </h1>
                <p>购房百问</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper4.htm'}">
            <div class="tab-mask activetab">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda4.png" />
                </h1>
                <p>成交故事</p>
                <P>海量成交故事<br>倾听购房者内心<br>深处的秘密</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper5.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda5.png" />
                </h1>
                <p>购房福利</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
</div>
<!--小二管家-看房团-5分栏-->


<!--小二管家-看房团-当前位置-->
<div class="crumb">
    <div class="content_box">
        <h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> > <a> 小二管家</a></h1>
    </div>
</div>



<div style="    width: 1170px;
    margin: auto;
    border: 1px solid #ededed;
    margin-bottom: 60px;">
    <div class="saleTrends tab">
        <ul class="saleTrendsIcon">
            <i class="dotGun"></i>
            <a th:class="${number &gt; 1 ? 'dotLeft hover' : 'dotLeft'}" th:href="${number &gt; 1 ? pageUrl + '-n' + (number-1)+'.htm' : '#'}"><</a>
            <a th:class="${totalPages &gt; number ? 'dotRight hover' : 'dotRight'}" th:href="${totalPages &gt; number ? pageUrl + '-n' + (number+1)+'.htm' : '#'}">></a>
            <li th:each="s,i:${storyData}">
                <i class="dot" th:rel="${i.index}" th:id="${'a'+ i.index}" ></i>
                <div class="timeShort">
                    <span th:text="${#strings.toString(s.buyTime).substring(5,10)}">4.23</span>
                    <p th:text="${#strings.toString(s.buyTime).substring(0,4)}">2018</p>
                </div>
                <span class="timeLong" th:text="${#strings.toString(s.buyTime)}">2018.4.23</span>
            </li>
        </ul>
    </div>
    <div >
        <div  th:rel="${j.index}" th:id="${'con_a_'+ j.index}"   th:each="s,j :${storyData}">
            <!--            购房时间及信息-->
            <div class="dealinformation">
                <div class="dealUser">
                    <div class="dealUT">
                        <b th:if="${!#strings.isEmpty(s.customerName)}" th:text="${s.customerName}"></b>
                        <span>购买时间：<i th:text="${#strings.isEmpty(s.buyTime) ? '' : #strings.toString(s.buyTime).replace('.','-')}"></i></span>
                    </div>
                    <div class="dealProject">
                        <a th:href="${'/house/'+s.projectId+'-'+s.projectType+'.htm'}" target="_blank"><span>购买项目</span><i th:text="${#strings.isEmpty(s.projectName) ? '' : s.projectName }">中海城</i></a>
                    </div>
                </div>
                <p th:text="${#strings.isEmpty(s.commandInfo) ? '' : '购房需求：'+s.commandInfo }"></p>
            </div>

            <!--            购房时间及信息-->

            <div>
                <a class="dealServerMan" th:href="${#strings.equals(s.whetherShow, '1') ?  '/agent/second/'+s.memberId: 'javascript:void(0);' }" th:target="${#strings.equals(s.whetherShow, '1') ? '_blank': '_self'}">
                    <div class="dealSample">
                        <img th:src="${#strings.isEmpty(s.headPic) ? '' : s.headPic }">
                    </div>
                    <p th:text="${#strings.isEmpty(s.xiaoerName) ? '' : '服务人'+s.xiaoerName }"></p>
                </a>
                <div class="dealReal">
                    <ul class="dealRealestate">

                        <li style="margin-top: 52px">
                            <div class="dealRealestatediv">
                                <span>01</span><p>对比楼盘推荐</p><p class="dealDivP"><th:block th:text="${#strings.isEmpty(s.contrastReason) ? '' : s.contrastReason }"></th:block></p>
                            </div>
                        </li>
                        <li style="margin-top: 130px">
                            <div  class="dealRealestatediv">
                                <img th:src="${#strings.isEmpty(s.layoutPic) ? '' : s.layoutPic }">

                                <div class="dealRealestatedivR">
                                    <span>03</span><p>项目户型解析</p><p class="dealDivP"><th:block th:text="${#strings.isEmpty(s.analyzeLayout) ? '' : s.analyzeLayout }"></th:block></p>


                                </div>

                            </div>
                        </li>

                    </ul>
                    <ul class="dealRealestate3"><li>
                        <img src="https://static.fangxiaoer.com/web/images/imagese/xian.png" alt="">
                    </li></ul>
                    <ul class="dealRealestate2">
                        <li style="margin-top: 156px">
                            <div class="dealRealestatediv">
                                <div class="dealRealestatedivL">
                                    <span>02</span><p>项目分析</p><p class="dealDivP"><th:block th:text="${#strings.isEmpty(s.analyzeProject) ? '' : s.analyzeProject }"></th:block></p>
                                </div>
                                <img th:src="${#strings.isEmpty(s.projectPic) ? '' : s.projectPic }">

                            </div>
                        </li>
                        <li style="margin-top: 130px" >
                            <div  class="dealRealestatediv">
                                <div class="dealRealestatedivL">
                                    <span>04</span><p>优惠福利</p><p class="dealDivP"><th:block th:text="${#strings.isEmpty(s.activityInfo) ? '' : s.activityInfo }"></th:block></p>

                                </div>
                                <img th:src="${#strings.isEmpty(s.activityPic) ? '' : s.activityPic }">

                            </div>

                        </li>
                    </ul>
                </div>

                <div class="dealTips">
                    <span>小二心语</span>
                    <p th:text="${#strings.isEmpty(s.xiaoerReview) ? '' : s.xiaoerReview }"></p>
                </div>
            </div>
        </div>
    </div>
</div>



<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<script type="text/javascript">
    $(document).ready(function(){
        var urlPath = window.location.href;
        if(urlPath.indexOf("#") != -1){
            var currentIndex =  urlPath.split("#")[1];
            $("#a"+currentIndex).addClass("hover");
            $(".timeShort").hide()
            $(".timeLong").show()
            $("#a"+currentIndex).parent().find(".timeShort").show();
            $("#a"+currentIndex).parent().find(".timeLong").hide();
            for (var i = 0; i <10; i++) {
                if(i!=currentIndex){
                    $("#" + "con_a_"+i).hide();
                }
            }
        }else {
            $("#a0").addClass("hover");
            for (var i = 1; i <10; i++) {
                $("#" + "con_a_"+i).hide();
            }
        }
    });
    $(".dot").mouseover(function() {

        var dotRel = $(this).attr("rel");
        var dotparent = $(this).parent()
        $(".timeShort").hide()
        $(".timeLong").show()
        $(".dot").removeClass("hover")
        $(this).addClass("hover")
        dotparent.find(".timeShort").show()
        dotparent.find(".timeLong").hide()

        $("#" + "con_a_"+ dotRel).show();
//                console.log(dotRel)
        for (var i = 0; i <10; i++) {
            if (dotRel != i)
                $("#" + "con_a_"+i).hide();
        }

    })
    $(".timeLong").mouseover(function() {

        var dotRel = $(this).parents("li").find(".dot").attr("rel");
        var dotparent = $(this).parents("li").find(".dot")
        $(".timeShort").hide()
        $(".timeLong").show()
        $(".dot").removeClass("hover")
        $(this).parents("li").find(".dot").addClass("hover")
        $(this).parents("li").find(".timeShort").show()
        $(this).parents("li").find(".timeLong").hide()

        $("#" + "con_a_"+ dotRel).show();
//                console.log(dotRel)
        for (var i = 0; i <10; i++) {
            if (dotRel != i)
                $("#" + "con_a_"+i).hide();
        }

    })
</script>
</body>
</html>