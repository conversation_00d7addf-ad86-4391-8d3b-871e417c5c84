<!DOCTYPE html>
<html lang="en">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/photo.css?v=20180918" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/layer.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/Housekeeper/Housekeeper.js" type="text/javascript" charset="utf-8"></script>
</head>
<body>
<div class="left_box">
    <div class="top_box">
        <div class="photo">
            <img th:each="pic:${lookDetail.photos}" th:src="${pic.photoUrl}">
        </div>
        <div class="left">
            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/left03.png">
        </div>
        <div class="right">
            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/right03.png">
        </div>
    </div>
    <div class="bottom_box">
        <div class="left">
            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/left04.png">
        </div>
        <div class="show">
            <div class="lunbo">
                <ul>
                    <th:block th:each="pic,picIter:${lookDetail.photos}">
                    <li th:if="${picIter.index %5 == 0}">
                        <div class="list"  th:each="one,i:${lookDetail.photos}" th:if="${i.index ge picIter.index and i.index lt picIter.index +5}">
                            <img  th:src="${one.photoUrl}">
                        </div>
                        <div class="clearfix"></div>
                    </li>
                    </th:block>
                    <div class="clearfix"></div>
                </ul>
            </div>
        </div>
        <div class="right">
            <img src="https://static.fangxiaoer.com/web/images/Housekeeper/right04.png">
        </div>
        <div class="clearfix"></div>
    </div>
</div>
<div class="right_box">
    <h1 th:text="${lookDetail.title}"></h1>
    <p th:text="${lookDetail.summary}">         桃花满城绽放，初春悄然而至，外出踏青者渐多，看房购房者亦日渐活络。本周六的路线安排在西线，为帮助购房者尽量全面的了解沈阳房产市场，看到更多的项目情况，我们的路线及楼盘选择更加多样，但保证质量兼备，以本次路线为例区域内品牌地产项目优先选择，因此本次看房团中多个项目具有多组意向客户核算房价，咨询细节。桃花满城绽放，初春悄然而至，外出踏青者渐多，看房购房者亦日渐活络。本周六的路线安排在西线，为帮助购房者尽量全面的了解沈阳房产市场，看到更多的项目情况，因此本次看房团中多个项目具有多组意向客户核算房价，咨询细节。了解沈阳房产市场，看到更多的项目情况，因此本次看房团中多个项目具有多组意向客户核算房价，咨询细节。</p>
</div>
<div class="left"></div>
<script>


    var index=0
    var index_sun=0
    $(".left_box  .bottom_box  .show ul  .list:eq(0)").addClass("color")
    $(".left_box .top_box .right").click(
        function(){
            if(index<$('.left_box .top_box .photo img').length-1){
                index++
                $('.left_box .top_box .photo img').eq(index).fadeIn(600).siblings().fadeOut(600);
                $(".left_box  .bottom_box  .show ul  .list").removeClass("color")
                $(".left_box  .bottom_box  .show ul  .list").eq(index).addClass("color")
                if(0==index%5){
                    if(index_sun<$(".left_box  .bottom_box  .show ul li").length-1){
                        index_sun++
                        $(".left_box  .bottom_box  .show .lunbo").animate({left:-index_sun*567},500,function(){
                            if(index_sun==$(".left_box  .bottom_box  .show ul li").length-1){
                                $(".left_box  .bottom_box  .show .lunbo").css("left",0)
                                index_sun=0
                            }
                        })
                    }
                }
            }else{
                index=0
                index_sun++
                $('.left_box .top_box .photo img').eq(index).fadeIn(600).siblings().fadeOut(600);
                $(".left_box  .bottom_box  .show ul  .list").removeClass("color")
                $(".left_box  .bottom_box  .show ul  .list").eq(index).addClass("color")
                $(".left_box  .bottom_box  .show .lunbo").animate({left:-index_sun*567},500,function(){
                    if(index_sun==$(".left_box  .bottom_box  .show ul li").length-1){
                        $(".left_box  .bottom_box  .show .lunbo").css("left",0)
                        index_sun=0
                    }
                })

            }

        }
    )







    $(".left_box .top_box .left").click(
        function(){
            if(index>0){
                if (0==index%5){
                    index_sun--
                    $(".left_box  .bottom_box  .show .lunbo").animate({left:-index_sun*567},500)
                }
                index--
                $('.left_box .top_box .photo img').eq(index).fadeIn(600).siblings().fadeOut(600);
                $(".left_box  .bottom_box  .show ul  .list").removeClass("color")
                $(".left_box  .bottom_box  .show ul  .list").eq(index).addClass("color")


            }else{
                index=$('.left_box .top_box .photo img').length-1
                $('.left_box .top_box .photo img').eq(index).fadeIn(600).siblings().fadeOut(600);
                $(".left_box  .bottom_box  .show ul  .list").removeClass("color")
                $(".left_box  .bottom_box  .show ul  .list").eq(index).addClass("color")
                $(".left_box  .bottom_box  .show .lunbo").css("left",-($(".left_box  .bottom_box  .show ul li").length-1)*liw)
                index_sun=$(".left_box  .bottom_box  .show ul li").length-1
                index_sun--
                $(".left_box  .bottom_box  .show .lunbo").animate({left:-index_sun*567},500)
            }
        }
    )

    // $(".left_box  .bottom_box  .show ul li .list").click(
    //     function () {
    //         index= $(".left_box  .bottom_box  .show ul li .list").index(this)
    //         $('.left_box .top_box .photo img').eq(index).fadeIn(600).siblings().fadeOut(600);
    //         $(".left_box  .bottom_box  .show ul  .list").removeClass("color")
    //         $(".left_box  .bottom_box  .show ul  .list").eq(index).addClass("color")
    //     }
    // )

    $(document).on("click",".left_box  .bottom_box  .show ul li .list",function(){
        index= $(".left_box  .bottom_box  .show ul li .list").index(this)
        $('.left_box .top_box .photo img').eq(index).fadeIn(600).siblings().fadeOut(600);
        $(".left_box  .bottom_box  .show ul  .list").removeClass("color")
        $(".left_box  .bottom_box  .show ul  .list").eq(index).addClass("color")
    })

    var liw=$(".left_box  .bottom_box  .show ul li").width()
    $(".left_box  .bottom_box  .show .lunbo").width(liw*($(".left_box  .bottom_box  .show ul li").length+1))
    $(".left_box  .bottom_box  .show ul li:last").after( $(".left_box  .bottom_box  .show ul li:first").clone())

    $(".left_box  .bottom_box .right").click(
        function(){
            if(index_sun<$(".left_box  .bottom_box  .show ul li").length-1){
                index_sun++
                $(".left_box  .bottom_box  .show .lunbo").animate({left:-index_sun*567},500,function(){
                    if(index_sun==$(".left_box  .bottom_box  .show ul li").length-1){
                        $(".left_box  .bottom_box  .show .lunbo").css("left",0)
                        index_sun=0
                    }
                })
            }
        }
    )

    $(".left_box  .bottom_box .left").click(
        function(){
            if(index_sun==0){
                $(".left_box  .bottom_box  .show .lunbo").css("left",-($(".left_box  .bottom_box  .show ul li").length-1)*liw)
                index_sun=$(".left_box  .bottom_box  .show ul li").length-1
                index_sun--
                $(".left_box  .bottom_box  .show .lunbo").animate({left:-index_sun*567},500)
            }else{
                index_sun--
                $(".left_box  .bottom_box  .show .lunbo").animate({left:-index_sun*567},500)
            }
        }
    )

    $(".left_box  .bottom_box  .show .lunbo").width(($(".left_box  .bottom_box  .show ul li").length+1)*567)

</script>
</body>
</html>