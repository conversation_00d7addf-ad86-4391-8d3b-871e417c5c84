<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <!--teamHousekeeper-->
    <meta charset="UTF-8">
    <title>小二管家_购房指导_置业顾问 - 房小二网</title>
    <meta name="keywords" content="沈阳买房,沈阳置业顾问,沈阳房源,房源推荐" />
    <meta name="description" content="房小二网小二管家为您购房提供全程一对一贴心服务，包括房源推荐、购房指导、顾问式陪看房、交易磋商、合同签约服务，为你精准制定专属购房计划，比你更懂你自己。" />
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/brand/brandIndex.css?v=20181225" />-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/swiper/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/swiper.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/WaiterHousekeeper.css?v=20190507" />
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
</head>

<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<!--小二管家-看房团-banner-->
<div class="housekeeper-condotour-banner">
    <p>此刻，累计帮助<span class="timer count-title" data-to="" data-speed="3500">1,532,900</span>个家庭实现安居梦想</p>
</div>
<!--小二管家-看房团-banner-->

<!--小二管家-看房团-5分栏-->
<div class="housekeeper-condotour-tab">
    <div class="tab">
        <a th:href="@{'/houseKeeper1.htm'}">
            <div class="tab-mask activetab">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda1.png" />
                </h1>
                <p>我们的团队</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper2.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda2.png" />
                </h1>
                <p>我们的看房团</p>
                <P>免费看房团<br> 带你一站式看房</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper3.htm'}">
           <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda3.png" />
                </h1>
                <p>购房百问</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper4.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda4.png" />
                </h1>
                <p>成交故事</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper5.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda5.png" />
                </h1>
                <p>购房福利</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
</div>
<!--小二管家-看房团-5分栏-->

<!--小二管家-看房团-当前位置-->
<div class="crumb">
    <div class="content_box">
        <h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> ><a> 小二管家</a></h1>
    </div>
</div>
<!--小二管家-看房团-当前位置-->

<div class="hk-team-list">
    <div class="team-list-left">
        <!--第一部分-->
        <div class="team-list-lump">
            <div class="team-list-video">
                <!-- //此为小二管家首图、固定图-->
                <img style="position: absolute;" src="//static.fangxiaoer.com\web\images\sercoudo\xegj-video-banner.jpg">

                <div class="team-list-video-mask" th:if="${!#lists.isEmpty(xiaoerData.teamVideo)}">
                    <img id="team-play" src="https://static.fangxiaoer.com/web/images/sercoudo/weteam-play.png">
                    <!-- //此为小二管家后台传入图片-->
                    <img th:src="${#strings.isEmpty(xiaoerData.teamVideo[0].image)?'':xiaoerData.teamVideo[0].image}">
                </div>

                <video id="condotour"  th:if="${!#lists.isEmpty(xiaoerData.teamVideo)}" preload="preload" controls="true" th:poster="${#strings.isEmpty(xiaoerData.teamVideo[0].image)?'':xiaoerData.teamVideo[0].image}" style="width: 868px; height: 498px;">
                    <source th:src="${#strings.isEmpty(xiaoerData.teamVideo[0].videoUrl)?'':xiaoerData.teamVideo[0].videoUrl}" type="video/mp4">
                </video>
            </div>

            <script>
                $(".team-list-video img").mouseover(function(){
                    $(".team-list-video-mask").show();
                });

                $(".team-list-video-mask").mouseout(function(){
                    $(".team-list-video-mask").hide();
                });

                $(".team-list-video-mask").click(function (){
                    $(".team-list-video-mask").hide();
                    $(".team-list-video img").hide();
                    $("#condotour").get(0).load();
                    $("#condotour").get(0).play();
                    $("#condotour").show();
                })

            </script>

            <div class="team-list-service">
                <h1>服务内容</h1>
                <div class="service-info">
                    <div class="service-info-detailed">
                        <img src="https://static.fangxiaoer.com/web/images/sercoudo/1.png" alt=""/>
                        <p>买贵十倍补差价</p>
                    </div>
                    <div class="service-info-detailed">
                        <img src="https://static.fangxiaoer.com/web/images/sercoudo/2.png" alt=""/>
                        <p>购房需求定制服务</p>
                    </div>
                    <div class="service-info-detailed">
                        <img src="https://static.fangxiaoer.com/web/images/sercoudo/3.png" alt=""/>
                        <p>免费预约时时看房车</p>
                    </div>
                    <div class="service-info-detailed">
                        <img src="https://static.fangxiaoer.com/web/images/sercoudo/4.png" alt=""/>
                        <p>小二管家1对1带看</p>
                    </div>
                    <div class="service-info-detailed">
                        <img src="https://static.fangxiaoer.com/web/images/sercoudo/5.png" alt=""/>
                        <p>房源解析,客观点评</p>
                    </div>
                    <div class="service-info-detailed">
                        <img src="https://static.fangxiaoer.com/web/images/sercoudo/6.png" alt=""/>
                        <p>磋商议价底价成交</p>
                    </div>
                    <div class="service-info-detailed">
                        <img src="https://static.fangxiaoer.com/web/images/sercoudo/7.png" alt=""/>
                        <p>获得房小二网独家优惠</p>
                    </div>
                    <div class="service-info-detailed">
                        <img src="https://static.fangxiaoer.com/web/images/sercoudo/8.png" alt=""/>
                        <p>享受终身房产咨询服务</p>
                    </div>
                </div>
            </div>
        </div>
        <!--第一部分-->

        <!--第二部分-->
        <div class="team-list-lump" style="margin-top:30px;height:auto;">
            <div class="team-list-expert" th:if="${!#lists.isEmpty(xiaoerData.xiaoers)}">
                <!--单个专家-->
                <div class="expert-single" th:each="xiaoer,xi:${xiaoerData.xiaoers}">
                    <a th:href="${#strings.equals(xiaoer.whetherShow, '1') ? '/agent/second/'+xiaoer.xiaoerId : 'javascript:void(0);'}" th:target="${#strings.equals(xiaoer.whetherShow, '1') ? '_blank': '_self'}">
                    <h1 th:text="${#strings.isEmpty(xiaoer.title) ? '': xiaoer.title}">区域专家</h1>
                    <h2 th:text="${#strings.isEmpty(xiaoer.summary) ? '': xiaoer.summary}">偏好的专区，给你指引方向</h2>
                    <div class="single-info">
                        <div class="single-info-img">
                            <img th:src="${#strings.isEmpty(xiaoer.bigPhoto) ? '': xiaoer.bigPhoto}" th:alt="${#strings.isEmpty(xiaoer.xiaoerName) ? '': xiaoer.xiaoerName}"/>
                        </div>
                        <div class="single-info-data">
                            <p><th:block th:text="${#strings.isEmpty(xiaoer.xiaoerName) ? '': xiaoer.xiaoerName}"></th:block><span></span></p>
                            <p th:text="${#strings.isEmpty(xiaoer.description) ? '': xiaoer.description}">从事房地产行业多年，具有丰富的从业经验，熟悉各个区域房源，为您合理科学的设计购房计划，本人服务热情；周到；专业；真诚，竭诚为您觅得理想家园。</p>
                            <p th:if="${!#strings.isEmpty(xiaoer.mobile)}">
                                <th:block th:each="xmobile ,xbi:${#strings.toString(xiaoer.mobile).toCharArray()}" th:text="${xbi.index == 3 or xbi.index == 6 ? '-'+xmobile : xmobile}"></th:block>
                            </p>
                        </div>
                        <div class="qrcode">
                            <span></span>
                            <div>
                                <img width="100px" height="100px"th:src="${#strings.isEmpty(xiaoer.qrCode) ? '': xiaoer.qrCode}"alt="二维码"/>
                            </div>
                        </div>
                    </div>
                    </a>
                    <div class="center-border"></div>
                </div>
                <!--单个专家-->
                <div class="lump-expert-viewall">
                    <a th:href="@{'/keeperMember'}" target="_blank">
                        <P>点击查看全部</p>
                    </a>
                </div>
            </div>
            <!--查看全部按钮-->

        </div>
        <!--第二部分-->
    </div>

    <div class="team-list-right">
        <div class="team-list-lump">
            <div class="lump-swiper">
                <div class="swiper-container " th:if="${!#lists.isEmpty(xiaoerData.xiaoerfestivals)}">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide" th:each="festival,fi:${xiaoerData.xiaoerfestivals}">
                            <a target="_blank" th:href="${'/afestivalNews/'+festival.xnid+'.htm'}">
                                <div class="team-title-img">
                                    <img th:src="${#strings.isEmpty(festival.photo) ? '': festival.photo}" th:alt="${#strings.isEmpty(festival.title) ? '': festival.title}"/>
                                </div>
                                <div class="team-title-mark">
                                    <h1>团队故事</h1>
                                </div>
                                <div class="team-title-msg">
                                    <h6><img src="https://static.fangxiaoer.com/web/images/sercoudo/index-page-shengluehaoshang.png"/></h6>
                                    <h1 th:text="${#strings.isEmpty(festival.title) ? '': festival.title}"></h1>
                                    <p th:text="${#strings.isEmpty(festival.summary) ? '': festival.summary}"></p>
                                    <h6 style="text-align:right;"><img style="transform: rotate(180deg)" src="https://static.fangxiaoer.com/web/images/sercoudo/index-page-shengluehaoshang.png"/></h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <!-- 如果需要分页器 -->
                    <div class="swiper-pagination lump-swiper-pagination"></div>
                </div>
                <div class="lump-viewall" th:if="${!#lists.isEmpty(xiaoerData.xiaoerfestivals)}">
                    <a th:href="@{'/festivalNews'}" target="_blank">
                        <P>点击查看全部</p>
                    </a>
                </div>
            </div>
        </div>
        <div class="team-list-lump">
            <div id="right">
                <div th:include="fragment/fragment :: right_order"  th:with="xiaoerIndex=1"></div>
            </div>
        </div>
    </div>
</div>



<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
</body>
<script>
    //    加他微信二维码显示隐藏
    $(".single-info-data span").mouseover(function () {
        $(this).parent().parent()[0].nextElementSibling.style.display="block"
    });

    $(".single-info-data span").mouseout(function () {
        $(".qrcode").css("display", "")
    });
    //    加他微信二维码显示隐藏

    //顶部导航栏计数器
     var familyCount = [[${familyCount}]]
    $.fn.countTo = function(options) {
        options = options || {};
        return $(this).each(function() {
            //当前元素的选项
            var settings = $.extend({}, $.fn.countTo.defaults, {
                from: $(this).data('from'),
                to: familyCount,
                speed: $(this).data('speed'),
                refreshInterval: $(this).data('refresh-interval'),
                decimals: $(this).data('decimals')
            }, options);
            //更新值
            var loops = Math.ceil(settings.speed / settings.refreshInterval),
                increment = (settings.to - settings.from) / loops;
            //更改应用和变量
            var self = this,
                $self = $(this),
                loopCount = 0,
                value = settings.from,
                data = $self.data('countTo') || {};
            $self.data('countTo', data);
            //如果有间断，找到并清除
            if (data.interval) {
                clearInterval(data.interval);
            };
            data.interval = setInterval(updateTimer, settings.refreshInterval);
            //初始化起始值
            render(value);
            function updateTimer() {
                value += increment;
                loopCount++;
                render(value);
                if (typeof(settings.onUpdate) == 'function') {
                    settings.onUpdate.call(self, value);
                }
                if (loopCount >= loops) {
                    //移出间隔
                    $self.removeData('countTo');
                    clearInterval(data.interval);
                    value = settings.to;
                    if (typeof(settings.onComplete) == 'function') {
                        settings.onComplete.call(self, value);
                    }
                }
            }
            function render(value) {
                var formattedValue = settings.formatter.call(self, value, settings);
                $self.html(formattedValue);
            }
        });
    };
    $.fn.countTo.defaults = {
        from: 0, //数字开始的值
        to: 0, //数字结束的值
        speed: 90000, //设置步长的时间
        refreshInterval: 100, //隔间值
        decimals: 0, //显示小位数
        formatter: formatter, //渲染之前格式化
        onUpdate: null, //每次更新前的回调方法
        onComplete: null //完成更新的回调方法
    };

    function formatter(value, settings) {
        return value.toFixed(settings.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
    }
    //自定义格式
    $('#count-number').data('countToOptions', {
        formmatter: function(value, options) {
            return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
        }
    });
    //定时器
    $('.timer').each(count);
    function count(options) {
        var $this = $(this);
        options = $.extend({}, options || {}, $this.data('countToOptions') || {});
        $this.countTo(options);
    }
    var timeout1 = null;
    //顶部导航栏计数器

    //    团队故事swiper
    var mySwiper = new Swiper('.swiper-container', {
        autoplay: 5000,//可选选项，自动滑动
        pagination : '.swiper-pagination',
        paginationClickable :true,
    })
</script>

</html>