<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>小二管家_购房指导_购房服务 - 房小二网</title>
    <meta name="keywords"
          content="沈阳买房,购房指导,房源推荐,沈阳房源"/>
    <meta name="description"
          content="房小二网小二管家为您购房提供全程一对一贴心服务，包括房源推荐、购房指导、顾问式陪看房、交易磋商、合同签约服务，为你精准制定专属购房计划，比你更懂你自己。热线电话：400-893-9709"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/housekeeper_details.css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/details.css" />

    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/Housekeeper/Housekeeper.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="css/WaiterHousekeeper.css" />

</head>


<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<div class="banner">

</div>
<div class="current_location">
    <div class="content_box">
        <h1>您的位置：<a href="/">沈阳房产网</a>&nbsp>&nbsp<a href="/houseKeeper1.htm">小二管家</a>&nbsp>&nbsp<a href="/keeperMember">人员列表</a></h1>
    </div>
</div>
<div class="title_box">
    <div class="content_box">
        <h1>小二管家</h1>
        <h2>400-893-9709</h2>
        <div class="clearfix"></div>
    </div>
</div>
<div class="list_box">
    <div class="content_box">
        <ul>
            <li th:each="member:${content}">
                <a th:href="${#strings.equals(member.whetherShow, '1') ? '/agent/second/'+member.memberId : 'javascript:void(0);'}" th:target="${#strings.equals(member.whetherShow, '1')? '_blank':'_self'}">
                <div class="photo">
                    <img th:src="${member.headPic}">
                    <div class="detailsBg"></div>
                </div>
                <div class="text_box">
<!--                        <div class="name">-->
<!--                            <h1 th:text="${member.memberName}"></h1>-->
<!--                        </div>-->
                        <div id="share_code" >
                            <div class="name">
                                <h1 th:text="${member.memberName}"></h1>
                            </div>
                            <span th:if="${!#strings.isEmpty(member.qrCode)}"></span>
                        </div>
                    <div class="qrcode" >
                        <span></span>
                        <div>
                            <img width="65px" height="65px"th:src="${#strings.isEmpty(member.qrCode) ? '': member.qrCode}"alt="二维码"/>
                        </div>
                    </div>
                    <div class="text">
                        <p th:text="${member.description}"></p>
                    </div>
                    <div class="phone">
                        <h1 th:text="${member.mobile}"></h1>
                    </div>
                </div>
                <div class="clearfix"></div>
                </a>
            </li>
            <div class="clearfix"></div>
        </ul>
    </div>
</div>
<script>
    //    加他微信二维码显示隐藏
    $("#share_code span").mouseover(function () {
        $(this).parent().nextAll(".qrcode").css("display", "block");
    });

    $("#share_code span").mouseout(function () {
        $(".qrcode").css("display", "none")
    });
</script>
<!--页码-->
<div class="page">
    <div th:include="fragment/page :: page"></div>
</div>
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
</body>
</html>