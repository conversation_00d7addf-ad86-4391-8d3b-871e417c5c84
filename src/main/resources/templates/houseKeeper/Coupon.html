<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>小二管家_特价房_沈阳楼盘折扣 - 房小二网</title>
    <meta name="keywords"
          content="沈阳楼盘优惠,沈阳楼盘折扣,小二管家优惠,沈阳特价房"/>
    <meta name="description" content="小二管家抢优惠专区，为您提供沈阳全境众多优质特价好房源的专属折扣活动与优惠活动，免费咨询热线：400-893-9709">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/Cpupon.css?v=201800904" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/Housekeeper/Housekeeper.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/WaiterHousekeeper.css?v=20190507" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/details.css" />

</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<div class="housekeeper-condotour-banner">
    <p>此刻，累计帮助<span class="timer count-title" data-to="" data-speed="3500">1,532,900</span>个家庭实现安居梦想</p>
</div>
<script th:inline="javascript">
    //设置计数
     var familyCount = [[${familyCount}]]
    $.fn.countTo = function(options) {
        options = options || {};
        return $(this).each(function() {
            //当前元素的选项
            var settings = $.extend({}, $.fn.countTo.defaults, {
                from: $(this).data('from'),
                to: familyCount,
                speed: $(this).data('speed'),
                refreshInterval: $(this).data('refresh-interval'),
                decimals: $(this).data('decimals')
            }, options);
            //更新值
            var loops = Math.ceil(settings.speed / settings.refreshInterval),
                increment = (settings.to - settings.from) / loops;
            //更改应用和变量
            var self = this,
                $self = $(this),
                loopCount = 0,
                value = settings.from,
                data = $self.data('countTo') || {};
            $self.data('countTo', data);
            //如果有间断，找到并清除
            if (data.interval) {
                clearInterval(data.interval);
            };
            data.interval = setInterval(updateTimer, settings.refreshInterval);
            //初始化起始值
            render(value);
            function updateTimer() {
                value += increment;
                loopCount++;
                render(value);
                if (typeof(settings.onUpdate) == 'function') {
                    settings.onUpdate.call(self, value);
                }
                if (loopCount >= loops) {
                    //移出间隔
                    $self.removeData('countTo');
                    clearInterval(data.interval);
                    value = settings.to;
                    if (typeof(settings.onComplete) == 'function') {
                        settings.onComplete.call(self, value);
                    }
                }
            }
            function render(value) {
                var formattedValue = settings.formatter.call(self, value, settings);
                $self.html(formattedValue);
            }
        });
    };
    $.fn.countTo.defaults = {
        from: 0, //数字开始的值
        to: 0, //数字结束的值
        speed: 90000, //设置步长的时间
        refreshInterval: 100, //隔间值
        decimals: 0, //显示小位数
        formatter: formatter, //渲染之前格式化
        onUpdate: null, //每次更新前的回调方法
        onComplete: null //完成更新的回调方法
    };

    function formatter(value, settings) {
        return value.toFixed(settings.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
    }
    //自定义格式
    $('#count-number').data('countToOptions', {
        formmatter: function(value, options) {
            return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
        }
    });
    //定时器
    $('.timer').each(count);
    function count(options) {
        var $this = $(this);
        options = $.extend({}, options || {}, $this.data('countToOptions') || {});
        $this.countTo(options);
    }
    var timeout1 = null;
</script>
<!--小二管家-看房团-banner-->
<!--小二管家-看房团-5分栏-->
<div class="housekeeper-condotour-tab">
    <div class="tab">
        <a th:href="@{'/houseKeeper1.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda1.png" />
                </h1>
                <p>我们的团队</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper2.htm'}">
            <div class="tab-mask  ">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda2.png" />
                </h1>
                <p>我们的看房团</p>
                <P>免费看房团<br> 带你一站式看房</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper3.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda3.png" />
                </h1>
                <p>购房百问</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper4.htm'}">
            <div class="tab-mask ">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda4.png" />
                </h1>
                <p>成交故事</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper5.htm'}">
            <div class="tab-mask activetab">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda5.png" />
                </h1>
                <p>购房福利</p>
                <P>先领券再买房<br>点一下就“购”了</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
</div>
<!--小二管家-看房团-5分栏-->



<div class="crumb">
    <div class="content_box">
        <h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> > 小二管家 </h1>
    </div>
</div>
<div class="process">
    <div class="content_box">
        <div class="left_box">
            <ul>
                <li>
<!--                    <img src="https://static.fangxiaoer.com/web/images/Housekeeper/Receive01.png">-->
                    <img src="https://static.fangxiaoer.com/web/images/imagese/cpy1.png" alt="">
                    <h1>领取优惠券</h1>
                    <p>点击立即领取，输入真实姓名和电话号码，注册成功后，即可领取优惠券（优惠券以电子码的形式存放于个人中心内）</p>
                </li>
                <li>
<!--                    <img src="https://static.fangxiaoer.com/web/images/Housekeeper/Receive02.png">-->
                    <img src="https://static.fangxiaoer.com/web/images/imagese/cpy2.png" alt="">
                    <h1>尊享小二管家服务</h1>
                    <p>成功领取优惠券后的三十分钟内，会有房小二网的专业小二管家电话联系您，为您讲解优惠券使用规则</p>
                </li>
                <li>
<!--                    <img src="https://static.fangxiaoer.com/web/images/Housekeeper/Receive03.png">-->
                    <img src="https://static.fangxiaoer.com/web/images/imagese/cpy3.png" alt="">
                    <h1>享受购房优惠</h1>
                    <p>使用优惠券前，通过房小二网小二管家认证优惠券信息无误后，由小二管家带领至售楼处购房并享受相应优惠</p>
                </li>
                <div class="clearfix"></div>
            </ul>
        </div>
        <div class="clearfix"></div>
    </div>
</div>
<div class="list_box">
    <div class="content_box">
        <div class="list" th:if="${#lists.toList(couponData).size() ne 0 }">
            <uL>
                <li th:each="activity:${couponData}" th:class="${#strings.toString(activity.state) eq '1'? 'color':''}">
                    <div class="couponBg2">
                        <div class="photo">
                            <img th:src="${activity.AppPic}">
                        </div>
                        <div class="information">
                            <div class="name">
                                <h1 th:text="${activity.ProjectName}"></h1>
                            </div>
                            <div class="Price">
                                <th:block th:if="${activity.price ne null }">
                                    <h1><b th:text="${activity.price.priceType}"></b>：<span th:text="${#strings.substring(activity.price.priceMoney,0,#strings.indexOf(activity.price.priceMoney,'.'))}+'元/㎡'"></span></h1>
                                </th:block>
                                <th:block th:if="${activity.price eq null }">
                                    <h1><span>待定</span></h1>
                                </th:block>
                            </div>
                            <div class="huxing">
                                <h1>主力户型：<span th:title="${#strings.length(activity.layoutStr) gt 20 ? #strings.toString(activity.layoutStr).replace('-',' '):''}"
                                               th:text="${activity.layoutStr eq ''? '暂无资料':(#strings.length(activity.layoutStr) gt 20 ? #strings.toString(activity.layoutStr).replace('-',' ').substring(0,14)+'...': #strings.toString(activity.layoutStr).replace('-',' '))}"></span></h1>
                            </div>
                            <div class="address">
                                <h1>地址：<span th:text="${activity.address}"></span></h1>
                            </div>
                        </div>
                        <div class="activity">
                            <h1></h1>
                            <h2><span th:text="${activity.FaceValue}"></span>元</h2>
                            <h3>活动时间：</h3>
                            <h4><span th:text="${activity.StarTime}"></span>-<span th:text="${activity.EndTime}"></span></h4>
                            <button th:text="${#strings.toString(activity.state) eq '1'?'已领取':''}" th:class="${#strings.toString(activity.state) eq '1'?'':'canClick'}"></button>
                            <input type="hidden" id="ActivityID" th:value="${activity.ActivityID}">
                            <input type="hidden" id="ActAviliable" th:value="${activity.avaliable}">
                        </div>
                        <div class="clearfix"></div>
                    </div>

                </li>
                <div class="clearfix"></div>
            </uL>
        </div>
    </div>
</div>
<!--页码-->
<div class="page">
    <div th:include="fragment/page :: pageContainParams"></div>
</div>
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<!--抢优惠弹窗-->
<div th:include="house/detail/fragment_activity::orderPrivActivity"></div>
<!--免费通话-->
<div th:include="house/detail/fragment_order::houseKeeper_freeCall"></div>
</body>
</html>