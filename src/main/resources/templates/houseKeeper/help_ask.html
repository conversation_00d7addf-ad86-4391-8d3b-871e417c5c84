<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>小二管家_购房百问 - 房小二网</title>
    <meta name="keywords" content="沈阳买房,沈阳置业顾问,沈阳房源,房源推荐"/>
    <meta name="description" content="房小二网小二管家购房百问，最权威的专家为您解读您最想知道的购房问题">

    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/WaiterHousekeeper.css" />


    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/saleTrends.css?v=20180706" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20190507" />
    <link type="text/css" href="https://static.fangxiaoer.com/web/styles/Villa/Villa_index.css?v=20181023" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/shuffling/css/iconfont.css?v=20190507" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/shuffling/css/index.css?v=20190507" />

    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/details.css?v=20190507" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/benfen.css?v=20190507" />




</head>

<body style="background-color: #FFFFFF !important;">

<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>

<div class="housekeeper-condotour-banner">
    <p>此刻，累计帮助<span class="timer count-title" data-to="" data-speed="3500">1,532,900</span>个家庭实现安居梦想</p>
</div>
<script>




    //设置计数
    var familyCount = [[${familyCount}]]
    $.fn.countTo = function(options) {
        options = options || {};
        return $(this).each(function() {
            //当前元素的选项
            var settings = $.extend({}, $.fn.countTo.defaults, {
                from: $(this).data('from'),
                to: familyCount,
                speed: $(this).data('speed'),
                refreshInterval: $(this).data('refresh-interval'),
                decimals: $(this).data('decimals')
            }, options);
            //更新值
            var loops = Math.ceil(settings.speed / settings.refreshInterval),
                increment = (settings.to - settings.from) / loops;
            //更改应用和变量
            var self = this,
                $self = $(this),
                loopCount = 0,
                value = settings.from,
                data = $self.data('countTo') || {};
            $self.data('countTo', data);
            //如果有间断，找到并清除
            if (data.interval) {
                clearInterval(data.interval);
            };
            data.interval = setInterval(updateTimer, settings.refreshInterval);
            //初始化起始值
            render(value);
            function updateTimer() {
                value += increment;
                loopCount++;
                render(value);
                if (typeof(settings.onUpdate) == 'function') {
                    settings.onUpdate.call(self, value);
                }
                if (loopCount >= loops) {
                    //移出间隔
                    $self.removeData('countTo');
                    clearInterval(data.interval);
                    value = settings.to;
                    if (typeof(settings.onComplete) == 'function') {
                        settings.onComplete.call(self, value);
                    }
                }
            }
            function render(value) {
                var formattedValue = settings.formatter.call(self, value, settings);
                $self.html(formattedValue);
            }
        });
    };
    $.fn.countTo.defaults = {
        from: 0, //数字开始的值
        to: 0, //数字结束的值
        speed: 90000, //设置步长的时间
        refreshInterval: 100, //隔间值
        decimals: 0, //显示小位数
        formatter: formatter, //渲染之前格式化
        onUpdate: null, //每次更新前的回调方法
        onComplete: null //完成更新的回调方法
    };

    function formatter(value, settings) {
        return value.toFixed(settings.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
    }
    //自定义格式
    $('#count-number').data('countToOptions', {
        formmatter: function(value, options) {
            return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
        }
    });
    //定时器
    $('.timer').each(count);
    function count(options) {
        var $this = $(this);
        options = $.extend({}, options || {}, $this.data('countToOptions') || {});
        $this.countTo(options);
    }
    var timeout1 = null;
</script>
<!--小二管家-看房团-banner-->
<!--小二管家-看房团-5分栏-->
<div class="housekeeper-condotour-tab">
    <div class="tab">
        <a th:href="@{'/houseKeeper1.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda1.png" />
                </h1>
                <p>我们的团队</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper2.htm'}">
            <div class="tab-mask  ">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda2.png" />
                </h1>
                <p>我们的看房团</p>
                <P>免费看房团<br> 带你一站式看房</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper3.htm'}">
            <div class="tab-mask activetab">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda3.png" />
                </h1>
                <p>购房百问</p>
                <P>最权威的专家为您解<br>读，您最想知道的购<br>房问题</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper4.htm'}">
            <div class="tab-mask ">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda4.png" />
                </h1>
                <p>成交故事</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper5.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda5.png" />
                </h1>
                <p>购房福利</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
</div>
<!--小二管家-看房团-5分栏-->


<!--小二管家-看房团-当前位置-->
<div class="crumb">
    <div class="content_box">
        <!--        <h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> > <a th:href="${'/houses/'}">沈阳新房</a> > 小二管家看房团</h1>-->
        <h1 style="height: 24px;line-height: 24px;">您的位置 : <a th:href="${'/'}">沈阳房产网</a> > 小二管家 </h1>

    </div>
</div>
<div class="container" style="width: 1170px; height: 800px">
    <div class=" page-layout page-main-content">
        <div class="layout layout-grid-0">
            <div class="grid-0">
                <div class="col col-main">
                    <div class="main-wrap J_Region">
                        <!--主体开始 -->
                        <div class="wb-zc-lxmod-yunqi-review-2018 J_Module">
                            <div class="module-wrap J_tb_lazyload dbl_tms_module_wrap">
                                <!-- 避免用window的全局变量获取数据 不要删 -->
                                <textarea class="schemaData" style="display:none;">{}</textarea>
                                <div class="dn-container">

                                    <div class="years">
                                        <a th:if="${number &gt; 1}" th:href="${pageUrl + '-n' + (number-1)+'.htm#currView'}">
                                            <img src="https://static.fangxiaoer.com/web/images/imagese/help_go.png" id="up" alt=""/>
                                        </a>
                                        <div id="yearContener">
                                            <div th:if="${!#lists.isEmpty(askData)}" th:class="${#strings.toString(hpi.index) eq yIndex ?'item cur':'item'}" th:each="hpItem,hpi:${askData}">
                                                <span class="vertical"></span>
                                                <a th:href="${'/houseKeeper3/'+'-n'+number+'-i'+hpi.index+'.htm#currView'}" target="_self">
													<span>
														<b th:text="${hpItem.month +'月份'}"></b>
														<i th:text="${hpItem.year+'年'}"></i>
													</span>
                                                </a>
                                            </div>
                                        </div>
                                        <a th:if="${totalPages &gt; number}"  th:href="${pageUrl + '-n' + (number+1)+'.htm#currView'}">
                                            <img src="https://static.fangxiaoer.com/web/images/imagese/help_down.png" id="down" alt="">
                                        </a>
                                    </div>
                                    <div class="kv-wrap">
                                        <div class="con">
                                            <img src="https://static.fangxiaoer.com/web/images/imagese/loading.png" class="kv">
<!--                                            <p class="title">ABOUT 关于 2018 云栖大会</p>-->
<!--                                            <p class="text">云栖大会由阿里巴巴集团主办，已经成为全球云计算TOP级峰会，汇聚DT时代最强大脑，描绘云计算发展趋势和蓝图，展现云计算、大数据、人工智能蓬勃发展的技术生态全景。</p>-->
                                        </div>
                                    </div>
                                    <div class="review-wrap">
                                        <div id="titleContener">
                                            <div class="city-list" th:each="hpItem,hpind:${askData}" th:if="${#strings.toString(hpind.index) eq yIndex}">
                                                <div class="item"  th:if="${!#lists.isEmpty(hpItem.videoList)}"  th:each="hpi,hpindex:${hpItem.videoList}"><em></em><span th:text="${#lists.isEmpty(hpi.title)?'':hpi.title}">学区房最新政策解读</span></div>
                                            </div>
                                        </div>
                                        <div id="videoListContener">
                                            <div class="slick-wrap swiper-container">
                                                <div class="swiper-wrapper"  th:each="hpItem,hpind:${askData}" th:if="${#strings.toString(hpind.index) eq yIndex}">
                                                    <div class="item swiper-slide" th:if="${!#lists.isEmpty(hpItem.videoList)}" th:each="hpi,hpindex:${hpItem.videoList}">
                                                        <div class="con">
                                                            <a href="javascript:;" >
                                                                <div class="color">
                                                                    <div class="mask"></div>
                                                                </div>
                                                                <span class="span">
                                                                <img src="https://static.fangxiaoer.com/web/images/imagese/span.png" alt="">

                                                                </span>

                                                                <video preload class="video"   width="500" height="456" style="" th:poster="${#strings.isEmpty(hpi.image)?'':hpi.image}">
                                                                    <source th:src="${#strings.isEmpty(hpi.videoUrl)?'':hpi.videoUrl}" type="video/mp4">
                                                                </video>
                                                                <span class="player-b"><img src="https://static.fangxiaoer.com/web/images/sercoudo/play.png"></span>

                                                                <div class="info" th:onclick="${#strings.equals(hpi.whetherShow, '1') ? 'window.open(''/agent/second/'+hpi.xiaoerId+''')' : 'javascript:void(0);'}">

                                                                    <div class="date" th:text="${#strings.isEmpty(hpi.title)?'':hpi.title}">学区房正确最新政策解读</div>
                                                                    <div class="txt">
                                                                        <!--                                                                    <img th:src="${#strings.isEmpty(hpi.image)?'':hpi.image}" alt="">-->
                                                                        <div class="help_Tn">
                                                                            <!--                                                                        <a th:href="${'/agent/second/'+hpi.xiaoerId}" target="_blank">-->

                                                                            <div class="dealSample">
                                                                                <!--                                                                            <img src="https://imageicloud.fangxiaoer.com/xe/2018/12/07/151503611.jpg">-->
                                                                                <img th:src="${#strings.isEmpty(hpi.avatar)?'':hpi.avatar}" alt="">

                                                                            </div>
                                                                            <span class="helpNz" th:text="${#strings.isEmpty(hpi.xiaoerName)?'':hpi.xiaoerName}">名字</span>
                                                                            <span class="help_simg" style="display: inline-block">

<!--                                                                                <img src="https://static.fangxiaoer.com/web/images/imagese/help_nm.png" alt="">-->
<!--                                                                                <span class="helpVx">加TA微信</span>-->
                                                                                <!--                                                                                <img th:src="${#strings.isEmpty(hpi.qrCode)?'':hpi.qrCode}" alt="">-->
<!--                                                                                <div class="hqrcode" style="width: 72px;height: 72px;">-->
<!--                                                                        <span></span>-->

                                                                            </span>

                                                                        </div>
                                                                        <div class="haskEWM">
                                                                            <img style="width: 90px;height: 90px; background-color: #fff;" th:src="${#strings.isEmpty(hpi.qrCode)?'':hpi.qrCode}" alt="">
                                                                            <span class="addWx">加 <b>TA</b> 微信</span>
                                                                        </div>

<!--                                                                        <p class="help_In" th:text="${#strings.isEmpty(hpi.description)?'':hpi.description}">-->
<!--                                                                            学区房购房专家，从事房产行业10年，学区房购房专家，从事房产行业10年-->
<!--                                                                        </p>-->
                                                                    </div>
                                                                    <div class="mask"></div>
                                                                </div>
                                                            </a>
                                                        </div>
                                                        <div class="unclick"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="nav">
                                        <div class="fix fixed" style="background: #010017;">
                                            <div class="con">

                                                <div class="scroll">

                                                    <div class="item cur">
                                                        <a href="javascript:;" target="_self">2018</a>
                                                    </div>

                                                    <div class="item">
                                                        <a href="javascript:;" target="_self">2017</a>
                                                    </div>

                                                    <div class="item">
                                                        <a href="javascript:;" target="_self">2016</a>
                                                    </div>

                                                    <div class="item" style="margin-right: 0;">
                                                        <a href="javascript:;" target="_self">2015</a>
                                                    </div>

                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                  <div class="video-mask">
                                    <div class="video-wrap">
                                        <i class="iconfont icon-wrong-thin close"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--主体结束-->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="currView" style="position: absolute;top: 500px;left: 466px;z-index: 1;"></div>
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<script type="text/javascript">

    //    $("#videoListContener").each(function(){
    //        $(this).find(".item").first().addClass("cur");
    //        $(this).find(".swiper-wrapper").first().show().siblings().hide();
    //    });
    $('.years .item').click(function(){
        $(this).addClass("cur").siblings().removeClass("cur")
        var index = $(this).index();
        $(".city-list").eq(index).show().siblings().hide();
        $(".swiper-wrapper").eq(index).show().siblings().hide();
    })

    //    $(".years").each(function(){
    //      $(this).find(".item").first().addClass("cur");
    // $(this).find(".item").first().siblings().show();
    //    });
    $('.item').click(function(){
        $(this).addClass("cur").siblings().removeClass("cur")
    })

    // $(".help_simg").mouseenter(function () {
    //     $(this).parents().siblings().find(".hqrcode").hide();
    //     $(".hqrcode").css("display","none");
    //     // alert(1)
    // });
    // $(".help_simg").mouseover(function () {
    //     $(this).parents().siblings().find(".hqrcode").show();
    //     $(".hqrcode").css("display","block");
    //
    // });
    $(".player-b").click(function () {
        // alert(11)
        $(this).hide();
        $(this).prev()[0].play()
        $(this).prev()[0].controls=true;

    })
    // $(".video").click(function () {
    //     // alert(11)
    //     // $(this).hide();
    //     console.log($(this).prev()[0])
    //     $(this).next()[0].style.display="block";
    //
    //     // $(this).siblings().find(".player-b")[].show();
    //     $(this).get(0).pause();
    //
    // })
    $(".video").bind("ended", function() {
        $(this).next()[0].style.display="block";
        // $(this).get(0).pause();
        $(this).get(0).controls=false;

    });


    var videos = document.getElementsByTagName('video');
    for (var i = videos.length - 1; i >= 0; i--) {
        (function(){
            var p = i;
            videos[p].addEventListener('play',function(){
                pauseAll(p);
            })
        })()
    }
    function pauseAll(index){

        for (var j = videos.length - 1; j >= 0; j--) {
            if (j!=index) videos[j].pause();
        }
    }

    document.body.parentNode.style.overflowY = "auto";
</script>
<script src="https://static.fangxiaoer.com/web/styles/shuffling/js/canvas.js?v=20190602" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/web/styles/shuffling/js/index.js?v=20190602" type="text/javascript" charset="utf-8"></script>
<!--<script src="/shuffling/js/index.js" type="text/javascript" charset="utf-8"></script>-->

</body>
</html>