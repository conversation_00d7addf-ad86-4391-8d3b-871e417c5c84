<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="${#strings.isEmpty(newsInfo.title)?'':newsInfo.title+'_团队故事_购房服务 - 房小二网'}">文章标题_专题活动_购房服务 - 房小二网</title>
    <meta name="keywords" th:content="${#strings.isEmpty(newsInfo.projectname)?'':newsInfo.projectname}"/>
    <meta name="description" th:content="${#strings.isEmpty(newsInfo.summary)?'':newsInfo.summary}"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/activity_details.css?v=20181012" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/layer.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/Housekeeper/Housekeeper.js" type="text/javascript" charset="utf-8"></script>
</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1,searchButton='houseKepper'"></div>
<div class="current_location">
    <div class="content_box">
        <h1>当前位置：<a th:href="@{'/'}">沈阳房产网</a>&nbsp>&nbsp<a th:href="${'/houseKeeper1.htm'}">小二管家</a>&nbsp>&nbsp<a th:href="@{'/festivalNews'}">团队故事</a></h1>
    </div>
</div>
<div class="big_box">
    <div class="content_box">
        <div class="left_box" th:if="${!#lists.isEmpty(newsInfo)}">
            <div class="title_sun">
                <h1 th:text="${#strings.isEmpty(newsInfo.title)?'':newsInfo.title}">【端午节】小二管家送温暖，带您看未来的家</h1>
                <h2 th:text="${#strings.isEmpty(newsInfo.addtime)?'':#strings.toString(newsInfo.addtime).replaceAll('\.','-')}">2018-6-16</h2>
                <div class="clearfix"></div>
            </div>
            <div class="content_sun" th:utext="${#strings.isEmpty(newsInfo.description)?'':#strings.toString(newsInfo.description)}"></div>
        </div>
        <div class="right_box">
            <div class="phone">
                <h1>看房热线：</h1>
                <h2>************</h2>
                <button  onclick="showPhonePop(2)">免费通话</button>
            </div>
            <div class="Rankings"  th:if="${ranking != null and #lists.size(ranking) gt 0}">
                <div class="title">
                    <div class="line"></div>
                    <h1>热卖排行</h1>
                    <div class="clearfix"></div>
                </div>
                <div class="content">
                    <ul>
                        <li th:each="rank,i:${ranking}">
                            <a th:href="${rank.TargetUrl}" target="_blank">
                                <div class="number">
                                    <h1><span th:text="${i.count}">1</span>/</h1>
                                </div>
                                <div class="name">
                                    <h1  th:text="${#strings.isEmpty(rank.AdTitle)?'':rank.AdTitle}">中冶上和湾</h1>
                                    <div class="money">
                                        <h1 th:if="${rank.ADPropriceyh ne '0'}">￥<span th:text="${#strings.isEmpty(rank.ADPropriceyh)?'':rank.ADPropriceyh}">10000</span>元/㎡</h1>
                                        <h1 th:if="${rank.ADPropriceyh eq '0'}"><span>待定</span></h1>
                                    </div>
                                    <div class="clearfix"></div>
                                    <h2 th:text="${#strings.isEmpty(rank.ADproAdderss)?'':rank.ADproAdderss}">和平</h2>
                                </div>

                            </a>
                            <div class="clearfix"></div>
                        </li>
                        <!--<li>-->
                        <!--<div class="number">-->
                        <!--<h1><span>3</span>/</h1>-->
                        <!--</div>-->
                        <!--<div class="name">-->
                        <!--<h1>汇邦克莱枫丹</h1>-->
                        <!--<h2>和平</h2>-->
                        <!--</div>-->
                        <!--<div class="money">-->
                        <!--<h1>￥<span>10000</span>元/㎡</h1>-->
                        <!--</div>-->
                        <!--<div class="clearfix"></div>-->
                        <!--</li>-->

                    </ul>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>
</div>
<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<!--弹窗-->
<div th:include="house/detail/fragment_order::houseKeeper_freeCall"></div>
</body>
</html>