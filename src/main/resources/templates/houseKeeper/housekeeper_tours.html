<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <title>房小二网看房团,沈阳看房团,免费看房 - 房小二网</title>
    <meta name="keywords" content="房小二网看房团,沈阳看房团,免费看房,看房" />
    <meta name="description" content="房小二网看房团全城招募中，免费空调大巴、全程置业指导、专属独家优惠，全沈城优质楼盘，房小二网将为您专属定制，让您的每一次看房之旅都不虚此行。" />
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <!--<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/brand/brandIndex.css?v=20181225" />-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/WaiterHousekeeper.css?v=20190507" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/Housekeeper/condoToursPrivate.css" />
</head>

<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=6"></div>
<!--小二管家-看房团-banner-->
<div class="housekeeper-condotour-banner">
    <p>此刻，累计帮助<span class="timer count-title" data-to="" data-speed="3500">1,532,900</span>个家庭实现安居梦想</p>
</div>
<script>
    //设置计数
    var familyCount = [[${familyCount}]]
    $.fn.countTo = function(options) {
        options = options || {};
        return $(this).each(function() {
            //当前元素的选项
            var settings = $.extend({}, $.fn.countTo.defaults, {
                from: $(this).data('from'),
                to: familyCount,
                speed: $(this).data('speed'),
                refreshInterval: $(this).data('refresh-interval'),
                decimals: $(this).data('decimals')
            }, options);
            //更新值
            var loops = Math.ceil(settings.speed / settings.refreshInterval),
                increment = (settings.to - settings.from) / loops;
            //更改应用和变量
            var self = this,
                $self = $(this),
                loopCount = 0,
                value = settings.from,
                data = $self.data('countTo') || {};
            $self.data('countTo', data);
            //如果有间断，找到并清除
            if (data.interval) {
                clearInterval(data.interval);
            };
            data.interval = setInterval(updateTimer, settings.refreshInterval);
            //初始化起始值
            render(value);
            function updateTimer() {
                value += increment;
                loopCount++;
                render(value);
                if (typeof(settings.onUpdate) == 'function') {
                    settings.onUpdate.call(self, value);
                }
                if (loopCount >= loops) {
                    //移出间隔
                    $self.removeData('countTo');
                    clearInterval(data.interval);
                    value = settings.to;
                    if (typeof(settings.onComplete) == 'function') {
                        settings.onComplete.call(self, value);
                    }
                }
            }
            function render(value) {
                var formattedValue = settings.formatter.call(self, value, settings);
                $self.html(formattedValue);
            }
        });
    };
    $.fn.countTo.defaults = {
        from: 0, //数字开始的值
        to: 0, //数字结束的值
        speed: 90000, //设置步长的时间
        refreshInterval: 100, //隔间值
        decimals: 0, //显示小位数
        formatter: formatter, //渲染之前格式化
        onUpdate: null, //每次更新前的回调方法
        onComplete: null //完成更新的回调方法
    };

    function formatter(value, settings) {
        return value.toFixed(settings.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
    }
    //自定义格式
    $('#count-number').data('countToOptions', {
        formmatter: function(value, options) {
            return value.toFixed(options.decimals).replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
        }
    });
    //定时器
    $('.timer').each(count);
    function count(options) {
        var $this = $(this);
        options = $.extend({}, options || {}, $this.data('countToOptions') || {});
        $this.countTo(options);
    }
    var timeout1 = null;
</script>
<!--小二管家-看房团-banner-->

<!--小二管家-看房团-5分栏-->
<div class="housekeeper-condotour-tab">
    <div class="tab">
        <a th:href="@{'/houseKeeper1.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda1.png" />
                </h1>
                <p>我们的团队</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper2.htm'}">
            <div class="tab-mask  activetab">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda2.png" />
                </h1>
                <p>我们的看房团</p>
                <P>免费看房团<br> 带你一站式看房</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper3.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda3.png" />
                </h1>
                <p>购房百问</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper4.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda4.png" />
                </h1>
                <p>成交故事</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
    <div class="tab">
        <a th:href="@{'/houseKeeper5.htm'}">
            <div class="tab-mask">
                <h1>
                    <img src="https://static.fangxiaoer.com/web/images/sercoudo/conda5.png" />
                </h1>
                <p>购房福利</p>
                <P>专业的技能，贴心的<br>服务购房专家为您精<br>挑合适房源</P>
                <img src="https://static.fangxiaoer.com/web/images/sercoudo/arrow.png" />
            </div>
        </a>
    </div>
</div>
<!--小二管家-看房团-5分栏-->

<!--小二管家-看房团-当前位置-->
<div class="crumb">
    <div class="content_box">
        <h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> > <a> 小二管家</a></h1>
    </div>
</div>
<!--小二管家-看房团-当前位置-->

<!--小二管家-看房团-四项服务--->
<div class="housekeeper-condotour-server">
    <div class="server">
        <h1>
            <img src="https://static.fangxiaoer.com/web/images/sercoudo/qcmf.png" alt="123">
        </h1>
        <h2>全程免费</h2>
        <p>参与看房团，车接车送，全程不收<br>取任何费用。看房成本由房小二网<br>独家承担。</p>
    </div>

    <div class="server">
        <h1>
            <img src="https://static.fangxiaoer.com/web/images/sercoudo/zyjj.png" alt="123">
        </h1>
        <h2>专业讲解</h2>
        <p>看房团途中，有小二管家介绍看房<br>路线详情，解答专业房产问题。</p>
    </div>

    <div class="server">
        <h1>
            <img src="https://static.fangxiaoer.com/web/images/sercoudo/dbjs.png" alt="" 123>
        </h1>
        <h2>大巴接送</h2>
        <p>每月固定地点签到集合，大巴车准<br>时接送，安心放心。</p>
    </div>

    <div class="server">
        <h1>
            <img src="https://static.fangxiaoer.com/web/images/sercoudo/djyh.png" alt="123">
        </h1>
        <h2>独家补贴</h2>
        <p>此服务由房小二网小二管家精心打<br>造，确保售楼处底价成交，额外有<br>小二管家独家优惠劵。</p>
    </div>
</div>
<!--小二管家-看房团-四项服务--->

<!--小二管家-看房团-看房团信息-->
<div class="housekeeper-condotour-info" th:if="${!#maps.isEmpty(toursData)}">
    <div class="flow">
        <h1>
            <th:block th:text="${#strings.isEmpty(toursData.condoTour)?'':toursData.condoTour}"></th:block>
        </h1>
        <p>
            <span>报名</span>
            <span>确认</span>
            <span>接送</span>
            <span>看房</span>
            <span>回程</span>
        </p>
    </div>
    <div class="route">
        <div class="route-left">
            <p>集合</p>
        </div>
        <div class="route-way" th:if="${!#lists.isEmpty(toursData.projects)}">
            <div class="toute-way-sysingle" th:each="tours,ti:${toursData.projects}">
                <div class="toute-way-sywindow">
                    <a th:href="${'/house/'+tours.projectId+'-'+tours.type+'.htm'}" target="_blank">
                        <div class="toute-way-sywindow-win">
                            <div class="toute-way-sywindow-left">
                                <img th:src="${tours.pic}" th:alt="${tours.projectName}" />/>
                            </div>
                            <div class="toute-way-sywindow-right">
                                <P th:text="${#strings.isEmpty(tours.projectName)?'':tours.projectName}">请问请问群翁群无</P>
                                <p style="font-size: 16px;color: #ff5200;" th:if="${tours.mPrice eq null}">待定</p>
                                <p th:unless="${tours.mPrice eq null}">价格：<i>
                                    <th:block th:utext="${#strings.toString(tours.mPrice.priceMoney).indexOf('.') eq -1 ? tours.mPrice.priceMoney+'元/㎡' : #strings.toString(tours.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','') +'元/㎡'}"></th:block>
                                </i>
                                    <em th:class="${tours.mPrice.priceType eq '均价'?'junjia':'qijia'}"></em>
                                </p>

                                <p th:if="${!#lists.isEmpty(tours.layout) and !#maps.isEmpty(tours.area)}">
                                    <i class="housetype" th:if="${!#lists.isEmpty(tours.layout)}">
                                        <th:block th:each="layout,i:${tours.layout}" th:if="${i.index lt 3}" th:text="${i.index eq 0 ? layout.RoomType+'居':'/'+layout.RoomType+'居'}"></th:block>
                                    </i>
                                    <i th:if="${!#lists.isEmpty(tours.area)}">-</i>
                                    <th:block th:if="${!#lists.isEmpty(tours.area)}" th:each="area:${tours.area}" th:text="${#strings.toString(area.minArea).substring(0,#strings.toString(area.minArea).indexOf('.'))+'~'+#strings.toString(area.maxArea).substring(0,#strings.toString(area.maxArea).indexOf('.'))+'㎡'}"></th:block>
                                    <th:block th:if="${#lists.isEmpty(tours.area) and #lists.isEmpty(tours.layout)}" th:text="${'暂无资料'}"></th:block>
                                </p>
                            </div>
                            <div style="clear: both;"></div>
                            <P class="address">地址：<th:block th:text="${#strings.isEmpty(tours.projectAddress)? '': #strings.abbreviate('['+tours.regionName+']'+tours.projectAddress,26)}"></th:block></P>
                            <p class="address-mark"><span th:text="${#strings.isEmpty(tours.projectName)?'':tours.projectName}"></span></p>
                        </div>
                    </a>
                </div>
                <h1>
                    <a th:href="${'/house/'+tours.projectId+'-'+tours.type+'.htm'}" target="_blank">
                        <span th:text="${#strings.isEmpty(tours.projectName)?'':tours.projectName}"></span>
                    </a>
                </h1>
            </div>


        </div>
        <div class="route-right">
            <img src="https://static.fangxiaoer.com/web/images/sercoudo/car.png">
        </div>

    </div>
    <script>
        //鼠标移入移出看房团路线效果
        $(".route-way-single").mouseover(function() {
            $("div.route-way-single-windows").css("display", "")
            var thisWin = $(this).children()[0];
            thisWin.style.display = "block";
        })
        $(".route-way-single").mouseout(function() {
            $(".route-way-single-windows").css("display", "")
        })
        //鼠标移入移出看房团路线效果
    </script>

    <div class="signup">
        <div class="left">
            <p>
                <b>发车时间：</b>
                <span th:text="${#strings.isEmpty(toursData.sendTime)?'':toursData.sendTime}">3月11日9：30发车</span>
                <b>集合地点：</b>
                <span th:text="${#strings.isEmpty(toursData.colPlace)?'':toursData.colPlace}">地铁二号线市府广场站C口</span>
            </p>
            <h2>承诺服务：
                <span><th:block th:text="${#strings.isEmpty(toursData.serviceInfo)?'':toursData.serviceInfo}"></th:block></span>
            </h2>
        </div>

        <div class="right">
            <div class="go">
                <p id="gratis-apply">免费报名</p>
            </div>
        </div>
</div>
</div>
<!--小二管家-看房团-看房团信息-->
<!--小二管家-看房团-往期回顾-->
<div class="housekeeper-condotour-review">
    <div class="left" th:if="${!#lists.isEmpty(toursVideo)}">
        <span id="player-b"><img src="https://static.fangxiaoer.com/web/images/sercoudo/play.png"></span>
        <video id="condotour"  th:poster="${#strings.isEmpty(toursVideo[0].image)?'':toursVideo[0].image}" webkit-playsinline="" playsinline="" x-webkit-airplay="" x5-playsinline="" preload="preload"  style="width: 665px; height: 430px;">
            <source th:src="${#strings.isEmpty(toursVideo[0].videoUrl)?'':toursVideo[0].videoUrl}" type="video/mp4">
        </video>
    </div>

    <div class="right">
        <div class="title">
            <span id="up" class="arrow-tour-ico" style="transform:rotate(180deg)"></span>
            <b>往期回顾</b>
            <span id="down" class="arrow-tour-ico"></span>
        </div>

        <div class="right-route">
           <!--路线-->
        </div>
        <div class="right-route-center"></div>
        <div class="right-info">
            <h1>年终岁尾，欢享圣诞</h1>
            <p>本次12月23日品牌看房团，总共到访7组11人，看房团路线：碧桂园太阳城—亚泰城—郡源悦城—宝能水岸康城—五彩阳光城四个项目，本次看房团以地铁房、学区房为主，沿路赏蒲河风光，大城景色。北部风光，无限美好。有置业顾问的详细讲解，有小二管家的周到服务，只为您选到满意的家。感谢您选择房小二网，选择小二管家。</p>
        </div>
    </div>

    <script>
        $(function(){//页面加载完毕时调用一次：初始化往期回顾
            toursInfo(1);
        });

        var pageNum = 1;//参数pageNum：请求的回顾期数
        $("#down").click(function(){
            pageNum++;
            toursInfo(pageNum);
        });

        $("#up").click(function(){
            pageNum--;
            if(pageNum<=1){
                pageNum=1
            };
            toursInfo(pageNum);
        });

        function toursInfo(itemNum){
            if(itemNum!=1){
                $("#up").removeClass("arrow-tour-ico-hover");
            }
            $.ajax({
                url: '/houseKeeperTour',
                data: {
                    page: itemNum,
                    pageSize: 1
                },
                type: 'post',
                success: function(data) {
                    if (data.status == 1) {
                        let msg = data.msg
                        let current=msg.split("/")[0];//当前的回顾编号
                        let maxNum= msg.split("/")[1];//最大数量
                        if(current==1){
                            $("#up").addClass("arrow-tour-ico-hover");
                        }

                        if(current>= parseInt(maxNum)){
                            $("#down").addClass("arrow-tour-ico-hover");
                            pageNum=maxNum-1;
                        }else{
                            $("#down").removeClass("arrow-tour-ico-hover");
                        }

                        $(".right-route").html("")//清空往期回顾轴线路线

                        if(data.content[0].projects == null ||data.content[0].projects == ""){
                            $(".right-route-center").hide();
                        }else{
                            $(".right-route-center").show();
                            $.each(data.content[0].projects,function(i,val){
                                $(".right-route").append('<div class="right-route-info"><a href=\'/house/'
                                    +val.projectId
                                    +"-"
                                    +val.projectType
                                    +'.htm\' target="_blank"><P><span>'
                                    +val.projectName
                                    +'</span></P></a><div class="center-border"></div> </div>')
                            });//增加往期回顾轴线路线
                        }

                        $(".right-info").html('<h1>'+data.content[0].condoTour+'</h1><p>'+data.content[0].review+'</p>')//往期回顾短讯息内容
                    }
                }
            });
        }
    </script>
</div>

<div th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<!--报名弹窗-->
<div id="condo-apply">
    <div class="condo-apply-win">
    <h6>免费报名<span id="close"><img src="https://static.fangxiaoer.com/web/images/sercoudo/tour-close.png"></span></h6>
    <!--已登录用户报名-->
    <th:if th:if="${#session?.getAttribute('sessionId') ne null and #session?.getAttribute('sessionId') ne ''}">
        <div class="condo-apply-put">
            <input id="phone" type="tel" name="phone" placeholder="请输入手机号" readonly="readonly" th:value="${session.phoneNum}" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">
        </div>
        <div class="condo-apply-put">
            <input id="address" type="text" name="address" placeholder="请输入您的姓名">
            <span class="errormsg name" style="display: none">请输入您的姓名</span>
        </div>
        <div class="condo-apply-put" style="border: none;">
            <div class="checkagreeInput" style="margin: 3px auto 10px auto;">
                <i id="checkagree1" class="checkimg checked cheimg9"></i><div style="font-size:12px;">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>及
                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
            </div>
        </div>
        <div class="condo-apply-put condo-apply-subme">
            <p id="submit">立即提交</p>
        </div>
    </th:if>

    <!--已登录用户报名-->
    <!--未登录用户报名-->
    <th:if th:unless="${#session?.getAttribute('sessionId') ne null and #session?.getAttribute('sessionId') ne ''}">
        <div class="condo-apply-put">
            <input id="phone" type="tel" name="phone" placeholder="请输入手机号" maxlength="11" onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')">

            <span class="errormsg cellphone" style="display: none">请输入正确的手机号码</span>
        </div>
        <div class="condo-apply-put" id="code-input">
            <input id="code" type="tel" name="code" style="width: 65%;border-right: 1px solid #dedede;" placeholder="请输入验证码" maxlength="6">
            <span id="code-pull">获取验证码</span>
            <span class="errormsg verifycode" style="display: none">请输入正确的验证码</span>
        </div>

        <div class="condo-apply-put">
            <input id="address" type="text" name="address" placeholder="请输入您的姓名">
            <span class="errormsg name" style="display: none">请输入您的姓名</span>
        </div>
        <div class="condo-apply-put" style="border: none;">
            <div class="checkagreeInput" style="margin: 3px auto 10px auto;">
                <i id="checkagree1" class="checkimg checked cheimg9"></i><div style="font-size:12px;">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>及
                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
            </div>
        </div>
        <div class="condo-apply-put condo-apply-subme">
            <p id="submit">立即提交</p>
        </div>
    </th:if>
    </div>
    <!--未登录用户报名-->
    <div id="success" style="display: none;">
        <span id="closeb"><img src="https://static.fangxiaoer.com/web/images/sercoudo/tour-close.png"></span>
        <p><img src="https://static.fangxiaoer.com/web/images/sercoudo/success.png"></p>
        <P>预约成功</P>
        <p>工作人员将尽快与您联系，免费为您提供专业服务</p>
    </div>

</div>


    <script th:inline="javascript">
        $(".cheimg9").click(function () {
            if($(this).hasClass("checked")){
                $(this).removeClass("checked")
            }else{
                $(this).addClass("checked")
            }
        })
        $("#gratis-apply").click(function(){
            $("#condo-apply").show()
            $(".condo-apply-win").show();
            $("body").css("overflow","hidden")
        });

        $("#close").click(function() {
            $("#condo-apply").hide();
            $("#success").hide()
            $("body").css("overflow","auto")
        })

        $("#closeb").click(function() {
            clearTimeout(closeTime);
            $("#condo-apply").hide();
            $("#success").hide()
            $("body").css("overflow","auto")
        })



        var wait = 60;
        $("#code-pull").click(function() {
            var tel = $("#phone").val();//获取手机号
            if (tel.length == 11 && tel.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/) && wait == 60) {
                $.ajax({
                    type: "POST",
                    data: {
                        mobile: tel
                    },
                    url: "/sendSmsCode",
                    success: function (result) {
                        if (result.status == 0) {
                            alert("系统繁忙，请稍后重试!");
                        } else {
                            $("#code-pull").css({
                                "border": "none",
                                "color": "#ccc"
                            });
                            time(wait);
                        }
                    }
                });
            } else {
                $(".cellphone").html("请输入正确的手机号码").show();
                return false;
            }
        })

        function time(o) {
            if (wait == 0) {
                $("#code-pull").html("重新获取");
                wait = 60;
                $("#code-pull").css({
                    "border": "none",
                    "color": "#ff5200"
                });
            } else {
                $("#code-pull").html(wait + "秒后重发");
                wait--;
                setTimeout(function() {time(o); },
                    1000);
            }
        }


        var closeTime ;
        var sessionId = [[${#session?.getAttribute('sessionId')}]];
        $("#submit").click(function () {
            var phone = "",
                code = "",
                carWhereId = "";
            phone = $("#phone").val(); //手机号
            code = $("#code").val(); //验证码
            carWhereId = $("#address").val(); //用户名
            if (sessionId == null || sessionId == "") {
                if (phone == "" || phone.length != 11 || !phone.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)) {
                    $(".cellphone").show();
                    setTimeout(function () {
                        $(".cellphone").hide();
                    }, 2000);
                    // alert("请正确输入您的手机号码");
                    return;
                } else {
                    $(".cellphone").hide();
                }

                if (code == "" || code.length != 6) {
                    $(".verifycode").show();
                    setTimeout(function () {
                        $(".verifycode").hide();
                    }, 2000);
                    // alert("请正确输入您的验证码");
                    return;
                } else {
                    $(".verifycode").hide();
                }
            }


            if(carWhereId == null || carWhereId == ""){
                $("span.name").html("请输入姓名");
                $("span.name").show()
                setTimeout(function () {
                    $("span.name").hide();
                }, 2000);
                return
            }
            if ($(".cheimg9").hasClass("checked") == false){
                alert("请仔细阅读并同意服务协议及隐私政策。");
                return
            }
            var params = {
                phone: phone,
                sessionId: sessionId,
                code: code,
                region: carWhereId,
                area: "小二管家看房团",
                type: 4
            };
            $.ajax({
                type: "POST",
                url: "/saveHouseOrder",
                data: JSON.stringify(params),
                headers: {
                    'Content-Type': 'application/json;charset=utf-8'
                },
                success: function (data) {
                    console.log(data)
                    if (data.status == 1) {
                        $(".condo-apply-win").hide();
                        $("#success").show();
                        $("#success").children()[0].style.display=" ";
                        $("#success").children()[2].style.display=" ";
                        if(sessionId == '' || sessionId == null || sessionId == undefined){
                            $("#phone").val("");
                        }
                        $("#code").val(""); //验证码
                        $("#address").val(""); //用户名
                        closeTime =setTimeout(function () {
                            $("#condo-apply").hide();
                            $("#success").hide()
                            $("body").css("overflow","auto")
                        },3000)
                    } else {
//                       $(".condo-apply-win").hide();
//                        $("#success").children()[0].style.display="none";
//                        $("#success").children()[2].style.display="none";
//                        $("#success").children()[1].html(data.msg);
                        alert(data.msg+",请检查后重新提交")
                    }
                }
            })
        })
        //获取验证码倒计时

        $("#player-b").click(function(){
            $("#condotour").get(0).play();
            $("#condotour").get(0).controls=true;
            $("#player-b").hide();
        });

        $("#condotour").bind("ended", function() {
            $("#player-b").show();
            $("#condotour").get(0).controls=false;
        });
        // $("#condotour").get(0).onpause=function () {
        //     $("#player-b").show();
        //     $("#condotour").get(0).pause();
        //     $("#condotour").get(0).controls=false;
        // }

    </script>
<!--报名弹窗-->
</body>
</html>