<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title>沈阳楼盘优惠_沈阳楼盘折扣_沈阳新房活动 - 房小二网</title>
    <meta name="keywords" content="沈阳楼盘优惠,沈阳楼盘折扣,沈阳新房活动,沈阳找房,沈阳低价楼盘" />
    <meta name="description" content="房小二网新房抢优惠专区，为您提供沈阳全境众多优质特价好房源，以及沈阳最全面的购房折扣活动与优惠活动，让您在购房过程中享受到实实在在的优惠。" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/activity">
    <!-- Bootstrap -->
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet"
          type="text/css">
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet"
          type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/default/default.css?v=20181225" rel="Stylesheet"
          type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/marketing/gift.css" rel="Stylesheet" type="text/css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/bootstrap.min.js" type="text/javascript"> </script>
    <script src="https://static.fangxiaoer.com/js/default.js"></script>
    <script src="https://static.fangxiaoer.com/js/time.js"></script>
    <script src="https://static.fangxiaoer.com/js/swiper/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/swiper.min.css" rel="stylesheet" type="text/css">
    <style type="text/css">
        .swiper-button-prev{
            background: url(https://static.fangxiaoer.com/web/images/sy/house/3.png) no-repeat;
            margin-left: 40px;
            height: 60px;
            width: 30px;
        }
        .swiper-button-next{
            background: url(https://static.fangxiaoer.com/web/images/sy/house/4.png) no-repeat;
            margin-right: 40px;
            height: 60px;
            width: 30px;
        }
        .swiper-button-next.swiper-button-disabled{
            background: url(https://static.fangxiaoer.com/web/images/sy/house/2.png) no-repeat;
        }
        .swiper-button-prev.swiper-button-disabled{
            background: url(https://static.fangxiaoer.com/web/images/sy/house/1.png) no-repeat;
        }
        .swiper-slide div{
            width: 1170px;
            margin: 0 auto;
            padding-top: 250px;
        }
        .swiper-slide div span{
            background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
            padding: 4px 14px;
            border-radius: 4px;
            color: #fff;
            float: right;
        }
    </style>

</head>
<body class="w1210">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2"></div>

    <!--搜索-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

    <div id="myCarousel" class="carousel slide thzq_gg">

        <div class="swiper-container">
            <div class="swiper-wrapper" th:if="${!#lists.isEmpty(up)}">
                <div class="swiper-slide" th:each="up,upc: ${up}">
                    <a th:href="${#strings.isEmpty(up.TargetUrl)?  '':up.TargetUrl }"
                       th:style="'background: url('+${#strings.isEmpty(up.AdFilePath)?'https://images.fangxiaoer.com/sy/esf/fy/big/noimage375275.jpg': up.AdFilePath}+') no-repeat center;'" target="_blank">
                        <div><span><th:block: th:text="${up.AdTitle}"></th:block:> </span></div>
                    </a>
                </div>
            </div>
            <div class="swiper-pagination1"></div>
            <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>
        </div>
        <script>
            var mySwiper = new Swiper('.swiper-container', {
                autoplay: 5000,//可选选项，自动滑动
                pagination : '.swiper-pagination1',
                prevButton:'.swiper-button-prev',
                nextButton:'.swiper-button-next'
            })
        </script>
    </div>
    <div class="container thzq_zf_dl">
        <dl class="hid">
            <dt >新房类型：</dt>

            <dd class='avtive'>
                <a href="/act/default/-1_-1_1">普宅</a></dd>

            <dd >
                <a href="/act/default/-1_-1_2">别墅</a></dd>

            <dd >
                <a href="/act/default/-1_-1_3">公寓</a></dd>


        </dl>
    </div>


    <div class="container thzq">
        <!--限时秒杀-->
        <div class="xsms_div">

        </div>

        <div th:if="${!#lists.isEmpty(activitys)}" th:each="activitys,i:${activitys}"
             th:class="${'Favorable Favorable'+((i.index+4)%4+1)}">
            <div class="left">
                <img th:src="${activitys.ActPic}" th:alt="${#strings.isEmpty(activitys.ProjectName)? '': activitys.ProjectName}"/>
                <div class="prices">
                    <ul>
                        <li><p></p></li>
                        <li><p><span><th:block th:text="${activitys.FaceValue}"></th:block></span>元</p></li>
                        <li><p>主力户型：</p></li>
                        <li><p class="room">
                            <th:block th:each="layout,i:${activitys.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block><th:block th:each="layout,i:${activitys.layout}" th:if="${i.index &gt; 0}" th:text="${'/'+layout.RoomType+'居'}"></th:block>
                            <th:block th:if="${!#lists.isEmpty(activitys.layout)}">-</th:block>
                            <th:block th:if="${#lists.isEmpty(activitys.area) and #lists.isEmpty(activitys.layout)}" th:text="${'暂无资料'}"></th:block>
                            <th:block th:each="area:${activitys.area}" th:text="${#numbers.formatInteger(area.minArea,1)+'~'+#numbers.formatInteger(area.maxArea,1)+'㎡'}"></th:block>
                        </p></li>
                    </ul>
                </div>
                <div class="region"><i></i><th:block th:text="${activitys.regionName}"></th:block></div>
            </div>
            <div class="right">
                <h1>
                    <a target="_blank" th:href="${#strings.isEmpty(activitys.ProjectID)?'':'/house/'+activitys.ProjectID+ '-' +activitys.ProjectType +'.htm'}">
                       <th:block th:text="${#strings.isEmpty(activitys.ProjectName)? '': activitys.ProjectName}"></th:block>
                    </a>
                </h1>
                <p><th:block th:text="${#strings.isEmpty(activitys.Slogan) ?'':activitys.Slogan}"></th:block></p>
                <a class="activitysAname" target="_blank" th:href="${#strings.isEmpty(activitys.ProjectID)?'':'/house/'+activitys.ProjectID+ '-' +activitys.ProjectType +'.htm'}">立即开抢</a>
                <i></i>
            </div>
        </div>
    </div>
    <!--分页-->

    <div class="cl"></div>
    <div class="page">
        <div id="Pager1">
            <div th:include="fragment/page :: page "></div>
        </div>
    </div>
    <!--页面底部-->
    <div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: footer_detail" ></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::commonFloat"></div>

</body>
</html>
