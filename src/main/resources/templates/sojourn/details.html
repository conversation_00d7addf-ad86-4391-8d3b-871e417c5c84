<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="UTF-8">
		<title th:text="${project.projectName+'_'+project.projectName+'信息_旅居地产 - 房小二网'}"></title>
		<meta name="keywords"
			  th:content="${project.projectName+','+project.projectName+','+project.projectName+'售楼处地址,'+project.projectName+'交房时间'}"/>
		<meta name="Description"
			  th:content="${'房小二网'+project.projectName+'详情为您提供'+project.projectName+'的售楼处地址，交房时间，联系方式，楼盘详情等各种必要信息，旅居一方，自在优家，尽在房小二网'}"/>
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/lvju/details.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/details.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
		<script type="text/javascript" src="/js/house/verify.js"></script>
		<script src="/js/house/AskListVilla.js"></script>
		<script src="/js/house/photoAlbum.js" type="text/javascript" charset="utf-8"></script>

		<script th:inline="javascript">
            /*<![CDATA[*/
            var userName = [[${session.userName}]];
            var phoneNum = [[${session.phoneNum}]];
            var sessionId = [[${session.muser}]];
            var projectId = [[${project.projectId}]];
            var projectName = [[${project.projectName}]];
            /*]]>*/
        </script>
	</head>
	<style>
		.clearfix {
			clear: both;
			content: "";
			display: block;
			overflow: inherit;
		}
		.clearfix2 {
			clear: both;
			content: "";
			display: block;
			overflow: hidden;
		}
	</style>

	<body>
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
	<div th:include="house/detail/fragment_order::useTouristCode"></div>
	<div th:include="house/detail/fragment_order::guideMessage"></div>
	<div th:include="house/detail/fragment_login::login"></div>

	<div class="aircraft">
		<div class="ai">
			<h1>预约看房</h1>
			<p>已有<span id="guideCount">822</span>人预约</p>
		</div>
	</div>

    <div class="Where_play" th:if="${#lists.size(project.en2) ne 0}">
		<div class="Where_play_bg">
			<img class="bigClo" src="https://static.fangxiaoer.com/web/images/lvjuImg/bigClo.png"/>
			<div class="swiper-container swiper-container-initialized swiper-container-horizontal expandBg">
				<div class="swiHuxing">
					<div class="swiper-pagination swiper-pagination-fraction swiIl"><span class="swiper-pagination-current">1</span> / <span class="swiper-pagination-total" th:text="${#lists.size(project.en2)}"></span></div>
				</div>
				<div class="swiper-wrapper">
					<div class="swiper-slide" style="width: 1739px;" th:each="one:${project.en2}">
						<img th:src="${one.imageUrl}" />
						<h1 class="WhereH1" th:text="${one.title}"></h1>
						<p class="WhereP" th:text="${one.enjoyDesc}"></p>
					</div>
				</div>
				<!-- Add Pagination -->
				<!-- Add Arrows -->
				<div class="swiper-button-next sn" tabindex="0" role="button" aria-label="Next slide" aria-disabled="false"></div>
				<div class="swiper-button-prev swiper-button-disabled sp" tabindex="0" role="button" aria-label="Previous slide" aria-disabled="true"></div>
				<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
			</div>
		</div>

	</div>
	<div class="Where_play_02" th:if="${#lists.size(project.en1) ne 0}">
		<div class="Where_play_bg">
			<img class="bigClo" src="https://static.fangxiaoer.com/web/images/lvjuImg/bigClo.png"/>
			<div class="swiper-container swiper-container-initialized swiper-container-horizontal expandBg_02">
				<div class="swiHuxing">
					<div class="swiper-pagination swiper-pagination-fraction swiIl"><span class="swiper-pagination-current">1</span> / <span class="swiper-pagination-total" th:text="${#lists.size(project.en1)}"></span></div>
				</div>
				<div class="swiper-wrapper">
					<div class="swiper-slide" style="width: 1739px;" th:each="one:${project.en1}">
						<img th:src="${one.imageUrl}" />
						<h1 class="WhereH1" th:text="${one.title}"></h1>
						<p class="WhereP" th:text="${one.enjoyDesc}"></p>
					</div>
				</div>
				<!-- Add Pagination -->
				<!-- Add Arrows -->
				<div class="swiper-button-next sn" tabindex="0" role="button" aria-label="Next slide" aria-disabled="false"></div>
				<div class="swiper-button-prev swiper-button-disabled sp" tabindex="0" role="button" aria-label="Previous slide" aria-disabled="true"></div>
				<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
			</div>
		</div>
	</div>
	<div class="headFixed" th:include="sojourn/fragment::floatmenu" th:with="firstNavIndex=1"></div>
	<div class="content">
		<p class="comLocation decomLocation">您的位置：
			<a th:href="@{'/'}">沈阳房产网</a>>
			<a th:href="@{'/houses/'}">沈阳新房</a>>
			<a th:href="@{'/tourist.htm'}">旅居地产</a>>
			<a th:href="${'/tourist/'+project.projectId+'.htm'}" th:text="${project.projectName}"></a>
		</p>
		<div class="deinformation">
			<div id="qrcode">
				<div class="layer_wei">
					<h1>手机看房</h1>
					<h2>更方便</h2>
				</div>
			</div>
			<div class="deSelection">
				<h1 class="H1" th:text="${project.projectName}"></h1>
				<span th:if="${project.minTemp ne null and #strings.toString(project.minTemp) ne ''}" class="detemperature" th:text="${project.minTemp + '℃/'+project.maxTemp+'℃'}"></span>

			</div>
		</div>
		<div class="deInsale">
			<i th:class="${'zai'+project.projectStatus}" th:text="${project.projectStatusValue}">在售</i>
			<th:block th:if="${project.features ne null}" >
			<span class="zaiSp" th:each="feature ,i:${#strings.listSplit(project.features,'/')}" th:if="${i.index lt 3}" th:text="${feature}"></span></th:block>
			<div class="dephone">
				<p>售楼处咨询电话<span th:utext="${#strings.toString(project.sortTel).replace('转','<b>转</b>')}"></span><input name="" type="button" onclick="showUsercode(2)" value="免费通话" class="button btn_1"></p>
			</div>
		</div>
		<div th:if="${#lists.size(project.prices) eq 0}">待定</div>
		<div th:if="${#lists.size(project.prices) ne 0}" class="deCategory">
			<ul class="deCategoryUl1">
				<li th:each="price ,i:${project.prices}" th:if="${i.index lt 3}">
						<span th:if="${#strings.toString(price.price) ne '0'}"><th:block th:text="${price.buildTypeName}"></th:block>
						<b th:text="${price.price}"></b>元/㎡
						<i th:text="${#strings.toString(price.priceType) eq '起价' ?'起':'均'}"></i>
						</span>
						<span th:if="${#strings.toString(price.price) eq '0'}">
							<th:block th:text="${price.buildTypeName}"></th:block><b class="deCategoryUl1Dd">待定</b>
						</span>
				</li>
				<li class="delimorePic" th:if="${#lists.size(project.prices) gt 3}">
					<span class="deCategorySpan">更多价格</span>
					<div class="deCategoryDiv">
						<span class="deCategoryDivSpan2">
							<img src="https://static.fangxiaoer.com/web/images/lvjuImg/jianjian.png" alt="">
						</span>
						<ul class="deCategoryUl2" >
							<li th:each="price ,i:${project.prices}" th:if="${i.count gt 3}">
								<th:block th:text="${price.buildTypeName}"></th:block>
									<span th:if="${#strings.toString(price.price) ne '0'}">
									<b th:text="${price.price}"></b>元/㎡
									<i th:text="${#strings.toString(price.priceType) eq '起价' ?'起':'均'}"></i></span>
								<span th:if="${#strings.toString(price.price) eq '0'}"><th:block th:text="${price.buildTypeName}"></th:block><b class="deCategoryUl1Dd">待定</b></span>
							</li>
						</ul>
					</div>
				</li>

			</ul>
			<div class="deAddress">
				<img src="https://static.fangxiaoer.com/web/images/lvjuImg/coordinates2.png" />
				<span th:text="${project.projectAddress}">辽阳市双泉镇</span>
				<!--			<a th:href="${'/tourist/'+project.projectId+'/info.htm'}" class="deAddGD" target="_blank">更多详细信息></a>-->
			</div>
		</div>

		<div class="">
			<th:block th:include="sojourn/fragment::touristmenu" th:with="firstNavIndex=1"></th:block>
			<div class="deImage" th:if="${#lists.size(project.photos) eq 3}">
				<div class="deImageL">
					<div class="videoImg" th:if="${#strings.toString(project.mobileVideo) ne ''}">

					</div>
<!--					<img class="videoImg" src="/lvjuImg/videoIco.png" alt="">-->
					<video th:if="${#strings.toString(project.mobileVideo) ne ''}" id="deVideo" width="782" height="424" th:src="${project.mobileVideo}" type="video/mp4" th:poster="${#strings.replace(project.photos[0].imageUrl, 'middle', 'big')}"></video>
					<span th:unless="${#strings.toString(project.mobileVideo) ne ''}" class="dexiaoguo" th:text="${project.photos[0].photoTypeValue}"></span>
					<div th:unless="${#strings.toString(project.mobileVideo) ne ''}"><a th:href="${'/tourist/'+project.projectId+'/album.htm'}"><img th:src="${#strings.replace(project.photos[0].imageUrl, 'middle', 'big')}" /></a></div>
				</div>
				<div class="deImageR">
					<div>
						<a th:href="${'/tourist/'+project.projectId+'/album.htm'}">
							<img th:src="${#strings.replace(project.photos[1].imageUrl, 'middle', 'big')}">
							<span class="dexiaoguo" th:text="${project.photos[1].photoTypeValue}">效果图</span>
						</a>
					</div>
					<div>
						<a th:href="${'/tourist/'+project.projectId+'/album.htm'}">
							<img th:src="${#strings.replace(project.photos[2].imageUrl, 'middle', 'big')}">
							<span class="dexiaoguo" th:text="${project.photos[2].photoTypeValue}">效果图</span>
						</a>
					</div>
				</div>
			</div>
		</div>
		<!--楼盘户型-->
		<div class="deDoormodel" th:if="${#lists.size(project.layouts) ne 0}">
			<div class="deBuilding">
				<h3 class="H3">楼盘户型</h3>
				<div class="deBedroom">
					<a th:if="${layFilter1 ne null and #lists.size(layFilter1) ne 0}" th:each="lf:${layFilter1}" th:href="${'/tourist/layout-'+project.projectId+'/r'+lf.id+'-pid'+project.projectId}">
						<span th:text="${#strings.toString(lf.name).replace('(','&nbsp;[').replace(')',']')}"></span></a>
					<a th:if="${layFilter2 ne null and #lists.size(layFilter2) ne 0}" th:each="lf:${layFilter2}" th:href="${'/tourist/layout-'+project.projectId+'/lt'+lf.id+'-pid'+project.projectId}">
						<span th:text="${#strings.toString(lf.name).replace('(','&nbsp;[').replace(')',']')}"></span></a>
					<a th:href="${'/tourist/layout-'+project.projectId+'/pid'+project.projectId}"><span class="deBedroommore">查看更多</span></a>
				</div>
			</div>
			<div class="deDoordetails">
				<a target="_blank" th:each="one,i:${project.layouts}" th:if="${i.count lt 3}" th:href="${#strings.toString(one.layoutType) eq '1' ? '/tourist/layout-'+project.projectId+'/pid'+project.projectId+'-r'+one.room+'-l'+one.layId
                    :'/tourist/layout-'+project.projectId+'/pid'+project.projectId+'-lt'+one.layoutType+'-l'+one.layId}">
					<div class="deDoordetailsL">
						<div class="deDoordetailsIMG">
							<div class="deDoordetailsIMGBg" th:style="${'background-image: url('+one.imageUrl+')'}">

							</div>
<!--							<img th:src="${one.imageUrl}"/>-->
						</div>
						<div class="deDoordetailsdiv">
							<h1>
								<th:block th:text="${one.title}"></th:block>
								<span th:class="${'deDdesspan'+one.state}" th:text="${#strings.equals(one.state, '1') ? '在售' : (#strings.equals(one.state, '2') ? '待售':'售罄')}"></span>
							</h1>

								<div class="deDoordetailsdiv1">
								<span>建筑面积:&nbsp;<i th:text="${#strings.arraySplit(one.buildArea,'.')[0]+' ㎡'}"></i></span>
									<span>类型:&nbsp;<i th:text="${one.buildTypeName}"></i></span>
								</div>
							<div class="deDoordetailsdiv2" th:unless="${one.totalPrice} eq 0 ">
								参考总价:&nbsp;<span th:text="${one.totalPrice}"></span>万元/套
							</div>
							<div class="deDoordetailsdiv2" th:if="${one.totalPrice} eq 0 "><span>待定</span></div>

						</div>
					</div>
				</a>
			</div>
		</div>

		<!--旅居锦囊-->
		<div class="kitscont clearfix2" th:if="${#lists.size(project.newsInfo) ne 0}">
			<div class="kitsdiv clearfix2">
				<div class="kitshead clearfix2">
					<h3 class="H3">旅居锦囊</h3>
				</div>
				<div class="kitsdis">
					<ul>
						<li th:each="one:${project.newsInfo}">
							<a th:href="${'/tNews/'+one.newsId+'.htm'}" target="_blank">
								<div>
									<h1>
									<span th:class="${'jinnangdisB labels'+one.newsType}" th:text="${one.newsTypeValue}">
									</span>
										<th:block th:text="${one.title}"></th:block>
									</h1>
								</div>
								<p th:text="${one.newsSummary}"></p>
							</a>
						</li>
					</ul>
				</div>
			</div>
			<div class="kitsDT" th:if="${#lists.size(project.infos) ne 0}">
				<div class="kitsDThead clearfix2">
					<h3 class="H3 H3DT">最新动态</h3>
					<a th:href="${'/tourist/'+project.projectId+'/dy.htm'}" target="_blank"><span class="deBedroommore spanDT" >查看更多</span></a>
				</div>
				<div class="kitsDTdivOF">
					<div class="kitsDTdiv">
						<ul>
							<li th:each="info:${project.infos}">
								<a th:href="${'/tourist/'+project.projectId+'/dy-'+info.infoType +'.htm'}" target="_blank">
									<img class="tuoyuan" src="https://static.fangxiaoer.com/web/images/lvjuImg/tuoyuan.png" />
									<div class="timeDT">
										<i th:text="${info.showTime}"></i>
										<span th:class="${'infoType'+info.infoType}" th:text="${info.infoTypeValue}"></span>
									</div>
									<div class="DTdiv" th:if="${#strings.toString(info.infoType) ne '4'}">
										<p class="DTnews" th:each="one:${#strings.toString(info.summary).split('【')}" th:if="${one ne null and one ne ''}">
											<span th:text="${#strings.toString(one).indexOf('】') ne -1 ? '【' + one : one}"></span>
										</p>
									</div>
									<div class="DTdiv" th:if="${#strings.toString(info.infoType) eq '4'}">
										<p  class="DTnews" th:text="${'【'+info.license+'】'+info.summary}"></p>
									</div>
								</a>
							</li>
						</ul>
					</div>
				</div>



			</div>
		</div>

		<!--城市地图-->
		<div class="map">
			<div class="demapH3">
				<h3 class="H3">城市地图</h3>
			</div>
			<div class="content_box1">

				<div class="content">
					<div class="btn">
						<ul>
							<li>
								<div class="pic">
									<img src="https://static.fangxiaoer.com/web/images/lvjuImg/Scenic.png" class="not">
									<img src="https://static.fangxiaoer.com/web/images/lvjuImg/Scenic02.png" class="already">
								</div>
								<div class="text">
									<h1>景点</h1>
								</div>
							</li>
							<li class="">
								<div class="pic">
									<img src="https://static.fangxiaoer.com/web/images/lvjuImg/food.png" class="not">
									<img src="https://static.fangxiaoer.com/web/images/lvjuImg/food02.png" class="already">
								</div>
								<div class="text">
									<h1>美食</h1>
								</div>
							</li>
							<li class="">
								<div class="pic">
									<img src="https://static.fangxiaoer.com/web/images/lvjuImg/Shopping.png" class="not">
									<img src="https://static.fangxiaoer.com/web/images/lvjuImg/Shopping02.png" class="already">
								</div>
								<div class="text">
									<h1>购物</h1>
								</div>
							</li>
							<li class="">
								<div class="pic">
									<img src="https://static.fangxiaoer.com/web/images/lvjuImg/Other.png" class="not">
									<img src="https://static.fangxiaoer.com/web/images/lvjuImg/Other02.png" class="already">
								</div>
								<div class="text">
									<h1>其它</h1>
								</div>
							</li>
							<div class="clearfix2"></div>
						</ul>
					</div>
					<div class="map_sun">
						<div class="content_sun">
							<div class="left">
								<div class="show">
									<div class="lunbo">
										<ul>
											<li th:if="${#lists.size(project.l0) ne 0}" th:each="one:${project.l0}">
												<h1 th:text="${one.locationName}"></h1>
												<h2 th:text="${'距离'+one.distance+'km'}"></h2>
											</li>
											<li th:if="${#lists.size(project.l0) eq 0}">
												<h1>小编还没有找到周边配套~</h1>
											</li>
										</ul>
										<ul>
											<li th:if="${#lists.size(project.l1) ne 0}" th:each="one:${project.l1}">
												<h1 th:text="${one.locationName}"></h1>
												<h2 th:text="${'距离'+one.distance+'km'}"></h2>
											</li>
											<li th:if="${#lists.size(project.l1) eq 0}">
												<h1>小编还没有找到周边配套~</h1>
											</li>
										</ul>
										<ul>
											<li th:if="${#lists.size(project.l2) ne 0}" th:each="one:${project.l2}">
												<h1 th:text="${one.locationName}"></h1>
												<h2 th:text="${'距离'+one.distance+'km'}"></h2>
											</li>
											<li th:if="${#lists.size(project.l2) eq 0}">
												<h1>小编还没有找到周边配套~</h1>
											</li>
										</ul>
										<ul>
											<li th:if="${#lists.size(project.l3) ne 0}" th:each="one:${project.l3}">
												<h1 th:text="${one.locationName}"></h1>
												<h2 th:text="${'距离'+one.distance+'km'}"></h2>
											</li>
											<li th:if="${#lists.size(project.l3) eq 0}">
												<h1>小编还没有找到周边配套~</h1>
											</li>
										</ul>
										<ul>
											<li th:if="${#lists.size(project.l4) ne 0}" th:each="one:${project.l4}">
												<h1 th:text="${one.locationName}"></h1>
												<h2 th:text="${'距离'+one.distance+'km'}"></h2>
											</li>
											<li th:if="${#lists.size(project.l4) eq 0}">
												<h1>小编还没有找到周边配套~</h1>
											</li>
										</ul>
									</div>
								</div>
							</div>
							<div class="right">
								<img src="https://static.fangxiaoer.com/web/images/lvjuImg/periphery.png">
								<h1>小二为您搜周边</h1>
							</div>
							<div class="clearfix2"></div>
						</div>
					</div>

				</div>
			</div>
		</div>

		<!--玩哪里-->
		<div class="depaly" th:if="${#lists.size(project.en2) ne 0}" th:imgleng="${#lists.size(project.en2)}">
			<div class="demapH3 dewherePlay">
				<h3 class="H3">玩哪里</h3>
			</div>
			<div class="swiper-container swiper-container-initialized swiper-container-horizontal deWPswiper">
				<div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(-1950px, 0px, 0px);">
					<div th:each="one,i:${project.en2}" class="swiper-slide expandPar"  th:data-swiper-slide-index="${i.index}" th:data-id="${i.index}">
						<img th:src="${one.imageUrl}" />
						<div class="deslidBg">
							<p class="deslidBgP" th:text="${one.title}"></p>
							<span class="deslidBgSpan" th:text="${one.enjoyDesc}"></span>
						</div>

					</div>
				</div>
				<!-- Add Pagination -->
				<!--<div class="swiper-pagination swiper-pagination-clickable swiper-pagination-bullets"><span class="swiper-pagination-bullet swiper-pagination-bullet-active" tabindex="0" role="button" aria-label="Go to slide 1"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 2"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 3"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 4"></span></div>-->
				<!-- Add Arrows -->
				<div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide"></div>
				<div class="swiper-button-prev" tabindex="0" role="button" aria-label="Previous slide"></div>
				<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>
		</div>
		<!--吃什么-->
		<div class="deeat" th:if="${#lists.size(project.en1) ne 0}" th:imgleng="${#lists.size(project.en1)}">
			<div class="demapH3 dewherePlay">
				<h3 class="H3">吃什么</h3>
			</div>
			<div class="swiper-container swiper-container-initialized swiper-container-horizontal deWPswiper">
				<div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(-1950px, 0px, 0px);">
					<div class="swiper-slide exeatPar"  th:each="one,i:${project.en1}" th:data-swiper-slide-index="${i.index}" th:data-id="${i.index}">
						<img th:src="${one.imageUrl}" />
						<div class="deslidBg">
							<p class="deslidBgP" th:text="${one.title}"></p>
							<span class="deslidBgSpan" th:text="${one.enjoyDesc}"></span>
						</div>
					</div>
				</div>
				<!-- Add Pagination -->
				<!--<div class="swiper-pagination swiper-pagination-clickable swiper-pagination-bullets"><span class="swiper-pagination-bullet swiper-pagination-bullet-active" tabindex="0" role="button" aria-label="Go to slide 1"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 2"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 3"></span><span class="swiper-pagination-bullet" tabindex="0" role="button" aria-label="Go to slide 4"></span></div>-->
				<!-- Add Arrows -->
				<div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide"></div>
				<div class="swiper-button-prev" tabindex="0" role="button" aria-label="Previous slide"></div>
				<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>
		</div>

		<!--用户点评-->
		<div class="decomments">
			<div class="decommentshead clearfix2">
				<h3 class="H3">用户点评</h3>
				<a th:if="${#lists.size(project.comments) ne 0 }" th:href="${'/tourist/'+project.projectId+'/comment.htm'}"
				   id="comment_all"><span class="deBedroommore">查看更多</span>
				</a>
			</div>

			<div class="decommentPJ clearfix2" th:if="${#lists.size(project.comments) ne 0}">
				<div class="decommentPJdiv">
					<span>分享您对楼盘的真实评价，给更多购房者参考吧  </span>
					<a   th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#login" id="CommentListAdd" >我要评价</a>
					<a  th:unless="${#session?.getAttribute('sessionId') == null}"  id="CommentListAdd" th:href="${'/tourist/'+project.projectId+'/pushcomment'}">我要评价</a>
				</div>

			</div>
			<div class="none" th:if="${#lists.size(project.comments) eq 0}">
				<h1>暂无用户点评，快点点评一下吧~~~</h1>
				<a   th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" class="askbutton" href="#login" id="CommentListAdd" >我要评价</a>
				<a  th:unless="${#session?.getAttribute('sessionId') == null}" class="askbutton"  id="CommentListAdd" th:href="${'/tourist/'+project.projectId+'/pushcomment'}">我要评价</a>
			</div>
			<div class="decommentdis">
				<ul class="userSynthesize clearfix2" th:if="${#lists.size(project.comments) ne 0}">
					<li class="userSynthesizeli clearfix2" th:each="one:${project.comments}">
<!--						<a th:href="${'/tCommentList/'+project.projectId+'.htm'}">-->
							<div class="decommentuse">
								<div class="decommentusechi">
									<img class="decommentuseImg" th:src="${#strings.toString(one.isNiming) eq '1' ? 'https://static.fangxiaoer.com/m/static/images/house/noagent.png':(one.headPic eq null ? 'https://static.fangxiaoer.com/m/static/images/house/noagent.png':one.headPic)}">
									<p th:if="${#strings.toString(one.isNiming) eq '1'} ">匿名</p>
									<p th:unless="${#strings.toString(one.isNiming) eq '1'} " th:text="${one.RealName}"></p>
								</div>
							</div>

							<div class="decommentMore">
								<p class="decommentMoreP" th:text="${one.commentDesc}"></p>
								<div class="decomMoreImg" th:if="${#lists.size(one.photos) ne 0}">
									<img th:each="photo:${one.photos}" th:src="${photo.imageUrl}" />
								</div>

								<div class="bigImgShow" style="display: none;">
									<div class="showImg">
										<ul style="margin-left: 0px;" th:if="${#lists.size(one.photos) ne 0}">
											<li th:each="photo:${one.photos}">
												<img th:src="${photo.imageUrl}" onload="imgSize()">
											</li>
										</ul>
									</div>
									<div class="close"><img
											src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"></div>
									<div class="prev"></div>
									<div class="next"></div>
								</div>

								<div class="decomDetails">

									<i th:text="${one.addTime}"></i>
									<span class="decomDetails1 open">查看更多</span>
									<!--<span class="bdsharebuttonbox" style="float: right;"><a href="javascript:void(0);" class="bds_more contentShare " data-cmd="more" th:onclick="'javascript:sharedetailclick(\'https://m.fangxiaoer.com/tCommentList/'+${project.projectId} +'.htm\')'">分享</a></span>-->
								</div>
							</div>
<!--						</a>-->
					</li>
				</ul>
			</div>
		</div>
		<!--<script src="/js/share.js"></script>-->
		<!--<script src="/js/personal_share.js"></script>-->
		<!--项目回答-->
		<div class="deQueAnswer">
			<div class="decommentshead">
				<h3 class="H3">楼盘问答</h3>
				<a th:if="${#lists.size(project.asks) ne 0}" th:href="${'/tourist/'+project.projectId+'/ask.htm'}"><span class="deBedroommore">查看更多</span></a>
			</div>

			<div class="decommentPJ clearfix2" th:if="${#lists.size(project.asks) ne 0}">
				<div class="decommentPJdiv">
					<span>说出您对楼盘的疑问，我们帮您答疑解惑</span>
					<a th:if="${#session?.getAttribute('sessionId') == null}" class="askbutton"  target="_blank" data-toggle="modal" href="#login">我要提问</a>
					<button  th:if="${#session?.getAttribute('sessionId') != null}" onclick="javascript:addProjectAsk();">我要提问</button>
				</div>
			</div>
			<div class="none" th:if="${#lists.size(project.asks) eq 0}">
				<h1>说出您对楼盘的疑问，我们帮您答疑解惑！</h1>
				<a th:if="${#session?.getAttribute('sessionId') == null}" class="askbutton"  target="_blank" data-toggle="modal" href="#login">我要提问</a>
				<button  class="askbutton"   th:if="${#session?.getAttribute('sessionId') != null}" onclick="javascript:addProjectAsk();">我要提问</button>
			</div>
			<div class="deAnswer">
				<ul th:if="${#lists.size(project.asks) ne 0}">
					<li class="clearfix2" th:each="asks,i:${project.asks}" th:if="${i.count lt 4}">
						<div class="deAnswerImg">
							<img src="https://static.fangxiaoer.com/web/images/lvjuImg/xiaoxi.png" />
						</div>
						<div class="deAnswerCon">
							<h1 th:text="${asks.askInfo}"></h1>
							<div class="deAnswerConPho">
								<span th:text="${#strings.substring(asks.phone,0,3)+'****'+#strings.substring(asks.phone,7)}"></span>
								<i th:text="${asks.addTime}"></i>
							</div>
							<div class="deAnswerConQuestion">
								<span>房小二网客服回复:</span>
								<p th:text="${asks.replyInfo}"></p>
							</div>
						</div>
					</li>
				</ul>
			</div>
		</div>
		<!--咨询说明-->
		<div class="deConsulting">
			<p>咨询说明：</p>
			<p>1、项目咨询是房小二网为您提供的疑难问题解答板块，在这里你可以与工作人员直接交流。</p>
			<p>2、因提问的用户较多，可能无法及时进行回复，我们将尽力在最快的时间内为您解答。</p>
			<p>3、当涉及广告、不实评论、恶意评论、爆粗口、危害国家安全等不当言论时，房小二网有权实施封号等管理行为。</p>
		</div>
	</div>
	<!--    <div th:include="house/detail/fragment_contactAgent::contact"></div>-->
<!--	<div th:include="house/detail/fragment_contactAgent::loginAgent"></div>-->
	<div th:include="fragment/fragment::commonFloat"></div>
	<div th:include="fragment/fragment:: footer_seo"></div>
	<div class="disclaimer">
		<strong>免责声明：楼盘信息由开发商提供，最终以政府部门登记备案为准，请谨慎核查。如该楼盘信息有误，您可以投诉或拔打举报电话：400-893-9709。</strong>
	</div>
	<!--   <div th:include="fragment/fragment:: footer_list"></div>-->
	<!--   <div th:include="fragment/fragment::tongji"></div>-->
	<div class="cl"></div>
	<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
	<div th:include="fragment/fragment::tongji"></div>

<!--	预约弹窗-->
	<div class="formDivbg" style="display: none">
		<div class="formDiv" >
			<img src="https://static.fangxiaoer.com/web/images/lvjuImg/formTrue.png" alt="">
			<h2>提交成功</h2>
			<p>您已预约成功，稍后会有工作人员与您联系</p>
		</div>
	</div>
	<div class="tcbg">

		<div class="text_box">
			<div class="content_box">
				<img class="yuyueC" src="https://static.fangxiaoer.com/web/images/lvjuImg/delClo.png" alt="">
				<p class="yuyueP">预约看房</p>
				<div class="properties">
					<h1 th:if="${project eq null}">请选择楼盘</h1>
					<h1 th:if="${project ne null}" th:text="${project.projectName}" style="color: rgb(0, 0, 0);"></h1>
					<img class="delxl" src="https://static.fangxiaoer.com/web/images/lvjuImg/delXl.png" alt="">
				</div>
				<div class="name">
					<input type="text" placeholder="请输入姓名">
				</div>
				<input type="hidden" class="selectCity" value="">
				<input type="hidden" class="selectProjectId" value="">
				<div class="phone">
					<input type="tel" placeholder="请输入手机号" onkeyup="if(value.length>11)value=value.slice(0,11)" onafterpaste="if(value.length>6)value=value.slice(0,6)">
				</div>
				<div class="Verification">
					<input type="tel" placeholder="请输入验证码" onkeyup="if(value.length>6)value=value.slice(0,6)" onafterpaste="if(value.length>6)value=value.slice(0,6)">
					<button>获取验证码</button>
					<div class="clearfix2"></div>
				</div>
				<div style="width: 90%;margin: 0 auto;margin-top: 15px;">
					<div class="checkagreeInput" style="margin: 3px auto 10px auto;">
						<i id="checkagree1" class="checkimg checked cheimg10"></i><div style="font-size:12px;">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>及
						<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
					</div>
				</div>
				<div class="btn">
					<button style="margin-top: 20px;">提交</button>
				</div>
			</div>
		</div>
		<div class="cityBra">
			<div class="m_top m_nav">
				<a class="return" onclick="closeCity()"></a>
				<a class="catalog"></a>
				<div class="news_icon"></div>
			</div>
			<div class="list_box">
				<ul class="scoUL">
			</div>
		</div>
		<div class="properties_box" >
			<div class="list">
<!--				<ul><li data-id="3" onclick="projectClick(this)"><div class="photo"><div class="layer_wei"></div></div><div class="text"><div class="name"><h1>佳兆业•汤泉驿</h1></div><div class="price"><h1>均价<span>4199</span>元/㎡</h1></div></div><div class="Choice_sun"></div></li><div class="clearfix"></div></ul>-->
				<ul class="scoUL">

				</ul>
			</div>

		</div>


	</div>


	</body>
	<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>

	<script type="text/javascript">
		$(".cheimg10").click(function () {
			if($(this).hasClass("checked")){
				$(this).removeClass("checked")
			}else{
				$(this).addClass("checked")
			}
		})
		photo.init();
		$(".deDoordetailsdiv").eq(1).css("border-right","0")
		function addProjectAsk() {
			$("#iNeedAsk").show();
		}
		$(function () {
			$("body").css("overflow-x","hidden")
			$('#qrcode').qrcode(
					{
						width: 70,
						height: 70,
						text: "https://m.fangxiaoer.com/tourist/"+[[${project.projectId}]]+ ".htm"
					});
			$('#qrcode2').qrcode(
					{
						width: 60,
						height: 60,
						text: "https://m.fangxiaoer.com/tourist/"+[[${project.projectId}]]+ ".htm"
					});
		});
		$(".bigClo").click(function () {
			$(".Where_play").hide()
			$(".Where_play_02").hide()

		})
		$(".depaly .swiper-slide").live("click",function() {
			$(".Where_play").show();
			var hhh = $(this).attr("data-id");
			console.log(hhh)
			var swiper2 = new Swiper('.expandBg', {
				spaceBetween: 0,
				initialSlide :hhh,
				pagination: {
					el: '.swiper-pagination',
					type: 'fraction',
				},
				navigation: {
					nextEl: '.swiper-button-next',
					prevEl: '.swiper-button-prev',
				},
			});
		})
		$(".deeat .swiper-slide").live("click",function() {
			$(".Where_play_02").show();
			var jjj = $(this).attr("data-id");
			var swiper3 = new Swiper('.expandBg_02', {
				spaceBetween: 0,
				initialSlide :jjj,
				pagination: {
					el: '.swiper-pagination',
					type: 'fraction',
				},
				navigation: {
					nextEl: '.swiper-button-next',
					prevEl: '.swiper-button-prev',
				},
			});
		})

		var swiper = new Swiper('.deWPswiper', {
			slidesPerView: 3,
			spaceBetween: 15,
			slidesPerGroup: 1,
			loop: true,
			loopFillGroupWithBlank: true,
			pagination: {
				el: '.swiper-pagination',
				clickable: true,
			},
			navigation: {
				nextEl: '.swiper-button-next',
				prevEl: '.swiper-button-prev',
			},
		});
		var exLeng = $(".depaly").attr("imgleng");

		var exLeng2 = $(".deeat").attr("imgleng");

		console.log(exLeng)
	if(exLeng<4){
		$(".depaly .swiper-wrapper").addClass("swiper-no-swiping")
		$(".depaly .swiper-button-next,.depaly .swiper-button-prev").hide()

	}
		if(exLeng2<4){
			$(".deeat .swiper-wrapper").addClass("swiper-no-swiping")
			$(".deeat .swiper-button-next,.deeat .swiper-button-prev").hide()

		}


		$(function() {
			var f = false;
			$(".open").click(function() {
				if(f = !f) {
					$(this).html("收起全文");
					$(this).removeClass('open');
					$(this).parent().prev().css("height", "auto");
					$(this).parent().parent().find(".decommentMoreP").removeClass('ov');
				} else {
					$(this).html("查看更多");
					$(this).addClass("open");
					//       $(".contentInfoMmore").css("height","90px");
					$(this).parent().parent().find(".decommentMoreP").addClass('ov');
				}
			})
			if($(".decommentMoreP").height() > 50) {
//				$(".decommentMoreP").css("height", "64px");
			} else {
				$(".decommentMoreP").css("height", "auto");
			}
			var page = $(".decommentMoreP").length;
			console.log(page)
			// alert(page)
			for(var i = 0; i < page; i++) {
				var text = $(".decommentMoreP").eq(i).height();
				if(text > 63) {
					$('.decommentMoreP').eq(i).addClass('ov');
					$(".decomDetails1").eq(i).show()
				} else {
					$('.decommentMoreP').eq(i).removeClass('ov');
					$(".decomDetails1").eq(i).hide()
				}
			}
		})



		//    预约
		function f() {
			var sco_Box = $(".list_box ul").height();
			console.log(sco_Box)
			if(sco_Box>184){
				// $(".city").css("overflow-y","scroll")
				// $(".city").css("heigth","184")
				$(".cityBra").addClass("cityScoll")


			}
			var scoBox = $(".list ul").height();
			if(scoBox>184){
				$(".properties_box").addClass("cityScoll")
				// $(".properties_box").css("heigth","184px")

			}
		}
		$(".text_box .content_box .properties").click( function () {
					if ($(".cityBra").hasClass("block")) {
						$(".cityBra").removeClass("block")
					}else{
						$(".cityBra").addClass("block")
					}
					$(".properties_box").removeClass("block")
					$(".cityBra .list_box ul").html("");
					$.ajax({
						url: '/getTouristFilter',
						data: {},
						type: 'post',
						success: function (data) {
							console.log(data)
							if (data.status == 1) {
								data = data.content
								for (i = 0; i < data.length; i++) {
									var li = $("<li data-id='" + data[i].id + "' onclick='cityClick(" + "this" + ")'><h1 >" + data[i].name + "</h1></li>")
									$(".cityBra .list_box ul").append(li);
								}
							}
							f();
						}

					})



				}
		)

		function cityClick(e) {
			// $(".city .list_box ul li").removeClass("color")
			// $(e).addClass("color")
			var nowCityId = $(e).attr("data-id")
			var cityName = $(e).find("h1").html()
			$(".properties h1").html(cityName)
			var cityId = $(".selectCity").val()
			//加载城市
			if (nowCityId == cityId) {
			} else {
				$(".selectCity").val(nowCityId)
				$(".properties_box .list ul").empty()
				$.ajax({
					url: '/orderProject',
					data: {cityName: cityName},
					type: 'post',
					success: function (data) {
						if (data.status == 1) {
							data = data.content
							for (i = 0; i < data.length; i++) {
								var price
								if (data[i].price == null) {
									price = "<h1>待定</h1>"
								} else {
									price = "<h1>" + data[i].price.priceType + "<span>" + data[i].price.price + "</span>元/㎡</h1>"
								}
								var img = "/images/sojourn/pic.png"
								if (data[i].imageUrl != null) {
									img = data[i].imageUrl
								}
								var li = $("<li data-id='" + data[i].projectId + "' onclick='projectClick(" + "this" + ")'>" +
										"<h1>" + data[i].projectName + "</h1></li>")
								$(".properties_box .list ul").append(li);
							}
							$(".properties_box .list ul").append($("<div class='clearfix'></div>"))
						}
					}
				})

			}
			$(".cityBra").removeClass("block")
			$(".properties_box").addClass("block")
			$(".properties_box .btn").addClass("block")
		}
		function projectClick(e) {
			var proName = $(e).find("h1").html()
			$(".properties h1").html(proName)
			var selectProjectId = $(e).attr("data-id")
			$(".selectProjectId").val(selectProjectId)
			$(".properties_box").removeClass("block");
			// if ($(e).hasClass("color")) {
			// 	$(e).removeClass("color")
			// 	$(e).find(".layer_wei").hide()
			// } else {
			// 	$(".properties_box .list ul li").removeClass("color")
			// 	$(".properties_box .list ul li").find(".layer_wei").hide()
			// 	$(e).addClass("color")
			// 	$(e).find(".layer_wei").show()
			// 	pname = $(e).find(" .text .name h1").text()
			// 	var selectProjectId = $(e).attr("data-id")
			// 	$(".selectProjectId").val(selectProjectId)
			// }
		}

		var wtime;

		$(".text_box .content_box .Verification button").click(
				function () {
					var phone = $(".text_box .content_box .phone input").val()
					if(phone==""){
						alert("请输入您的电话")
						return;
					}else{
						if (!reg.test(phone)) {
							alert("手机号格式不正确")
							return;
						}else{
							$.ajax({
								type: "POST",
								data: {mobile: phone},
								url: "/sendSmsCode",
								success: function (result) {
									if (result.status == 0) {
										confirm.message("操作过于频繁,请稍候再获取验证码");
									}
									Time(60)
								}
							})
						}
					}
				}
		)
		function Time(o) {
			clearInterval(wtime)
			wtime = setInterval(
					function () {
						if (o > 0) {
							o--
							$(".text_box .content_box .Verification  button").css("color", "#333")
							$(".text_box .content_box .Verification  button").text(o + "秒")
							$(".text_box .content_box .Verification  button").attr('disabled', true)

							if (o == 0) {
								$(".text_box .content_box .Verification  button").attr('disabled', false)
								clearInterval(wtime)
								$(".text_box .content_box .Verification  button").css("color", "#ff5200")
								$(".text_box .content_box .Verification  button").text("重新获取")

							}
						}
					}, 1000
			)

		}




		var h1conent = $(".properties h1").html();
		$(".formDivbg").click(function(event){
			$(this).hide()
		});
		// 关闭
		$(".yuyueC").click(function () {
			$(".tcbg").hide();
			$(".properties h1").html(h1conent)
			$(".cityBra,.properties_box ").removeClass("block")
			$(".content_box input").val("")
		})
		$(".aircraft").click(function () {
			$(".tcbg,.content_box").show();
			$(".formDivbg").hide()

		})

		var reg = /^1[0-9]{10}$/;
		var value
		$(".text_box .content_box .btn button").click(
				function () {
					var name = $(".text_box .content_box .name input").val()
					var phone = $(".text_box .content_box .phone input").val()
					var Verification = $(".text_box .content_box .Verification input").val()
					var properties = $(".text_box .content_box .properties h1").text()
					if (properties == "请选择楼盘") {
						alert("请选择楼盘")
						return;
					}
					if(name==""){
						alert("请输入您的姓名")
						return;
					}

					if(phone==""){
						alert("请输入您的电话")
						return;
					}else{
						if (!reg.test(phone)) {
							alert("手机号格式不正确")
							return;
						}
					}
					if (Verification == "") {
						alert("请输入验证码")
						return;
					}
					if ($(".cheimg10").hasClass("checked") == false){
						alert("请仔细阅读并同意服务协议及隐私政策。");
						return
					}
					$.ajax({
						url: '/addTouristOrder',
						type: "post",
						data: {
							// sessionId: sessionId,
							phone: phone,
							code: Verification,
							projectId: $(".selectProjectId").val(),
							projectName: properties,
							memberName: name
						},
						success: function (value) {
							if (value.status == 1) {
								$(".formDivbg").show();
								$(".tcbg").hide();
								$(".properties h1").html(h1conent)
								$(".cityBra,.properties_box ").removeClass("block")
								$(".content_box input").val("")

								setTimeout(function () {
									$(".formDivbg").hide()
								},3000)

							} else {
								alert(value.msg)
								setInterval(function () {
									window.history.go(-1)
								}, 3000);
							}
						}
					});
				}
		)

	//浮窗
		window.onscroll = function() {
			var s = $(this).scrollTop();
			if (s >= 346) {
				$(".headFixed").addClass("block");
			} else {
				$(".headFixed").removeClass("block");
			}
		}
	</script>

	<div class="cl"></div>
	<div>
		<input type="hidden" id="forSessionId" th:value="${#session?.getAttribute('sessionId')}">
		<div class="kfzc_heibu" style="display: none;" id="iNeedAsk">
			<div class="kfzc" style="display: block;margin-top: -160px;">
				<h1 th:text="${'提问：'+project.projectName}"></h1>
				<textarea name="" id="instantlyAsk" rows="" cols="" onkeyup="words_deal();"></textarea>
				<div style="float: inherit;">
					<span id="textCount">0</span><th:block>/300</th:block>
				</div>
				<a href="javascript:addProjectInfoAsk();">立即提问</a>
				<img id="closeBut" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" />
			</div>
		</div>
		<div class="yydk_heibu" style="display: none;" id="askSuccess">
			<div class="yyDk">
				<div class="yyDk_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
				<div>
					<div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"></div>
					<p>提交成功</p>
					<p>我们会尽快审核您的问题，及时为您解答</p>
				</div>

			</div>
		</div>
		<script type="text/javascript">
			$("#closeBut").click(function () {
				$("#iNeedAsk").hide();
			});
			function words_deal() {
				var curLength = $("#instantlyAsk").val().length;
				if (curLength > 300) {
					var num = $("#instantlyAsk").val().substr(0, 300);
					$("#instantlyAsk").val(num);
					alert("超过字数限制，多出的字将被截断！");
				}
				else {
					$("#textCount").text($("#instantlyAsk").val().length);
				}
			}
			$(".yyDk_close").click(function () {
                $("#askSuccess").hide();
            });
			function addProjectInfoAsk() {
				var Projectid = [[${project.projectId}]];
				var sessionId = $("#forSessionId").val();
				var content = $("#instantlyAsk").val();
				if (sessionId == null || sessionId == '') {
					alert("请先登录");
				}else if(content == null || content == ''){
					alert("请填写提问内容");
				}else {
					$.ajax({
						type: "POST",
						data: {
                            sessionId: sessionId,
                            projectId: Projectid,
                            askInfo: content
						},
						url: "/addTouristAsk",
						dataType: "json",
						success: function (data) {
							if (data.status == 1) {
							    $("#instantlyAsk").val('')
								$("#iNeedAsk").hide();
								$("#askSuccess").show();
                                words_deal()
							} else {
								alert(data.msg);
							}
						}
					});
				}
			}
			$(".videoImg").click(function(){
				$("#deVideo").get(0).play();
				$("#deVideo").get(0).controls=true;
				$(".videoImg").hide();
			});

			$("#deVideo").bind("ended", function() {
				$(".videoImg").show();
				$("#deVideo").get(0).controls=false;
			});
            $.ajax({
                type: "POST",
                data: {},
                url: "/touristGuideCount",
                success: function (data) {
                    if (data.status == 1) {
                        $("#guideCount").text(data.content)
                    }
                }
            })


		//	评论图片放大
			$(".decomMoreImg  img").click(function () {
				// alert(111)
				photo.maxIndex = $(this).parent().parent().find(".bigImgShow li").length
				photo.ind = $(this).index()
				photo.showInd = photo.ind
				$(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
				console.log(photo.maxIndex)
				console.log(photo.ind)
				$(this).parent().parent().find(".bigImgShow").show()
			})

		</script>
	</div>


</html>