<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="UTF-8">
		<title th:text="${brief.projectName+'_'+brief.projectName+'基本信息_旅居地产 - 房小二网'}"></title>
		<meta name="keywords"
			  th:content="${brief.projectName+','+brief.projectName+'动态,'+brief.projectName+'开盘,'+brief.projectName+'交房'}"/>
		<meta name="description" th:content="${'房小二网旅居地产栏目为您提供'+brief.projectName+'最新动态，开盘交房信息，旅居一方，自在优家，尽在房小二网'}"/>
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/module.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/detailsDyn.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
		<script type="text/javascript" src="/js/house/verify.js"></script>
		<script th:inline="javascript">
			/*<![CDATA[*/
			var userName = [[${session.userName}]];
			var phoneNum = [[${session.phoneNum}]];
			var sessionId = [[${session.muser}]];
			var projectId = [[${brief.projectId}]];
			var projectName = [[${brief.projectName}]];
			/*]]>*/
		</script>
	</head>

	<body>
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
	<div th:include="house/detail/fragment_order::useTouristCode"></div>
	<div th:include="house/detail/fragment_order::guideMessage"></div>

	<div class="content">
		<div class="headFixed" th:include="sojourn/fragment::floatmenu" th:with="firstNavIndex=7"></div>
		<p class="comLocation decomLocation">您的位置：
			<a th:href="@{'/'}">沈阳房产网</a>>
			<a th:href="@{'/houses/'}">沈阳新房</a>>
			<a th:href="@{'/tourist.htm'}">旅居地产</a>>
			<a th:href="${'/tourist/'+brief.projectId+'.htm'}" th:text="${brief.projectName}"></a>
		</p>
			<div class="pro_name">
				<div id="qrcode">
					<div class="layer_wei">
						<h1>手机看房</h1>
						<h2>更方便</h2>
					</div>
					<img src="https://static.fangxiaoer.com/web/images/ico/head/ewm_1.jpg" alt="" />-->
				</div>
				<div class="box_sun">
					<p>
						<span th:text="${brief.projectName}"></span>
						<span th:if="${brief.minTemp ne null and brief.maxTemp ne null}" class="centigrade" th:text="${brief.minTemp + '℃~' + brief.maxTemp + '℃'}"></span>
					</p>
					<div class="type_sun wait_1" th:text="${brief.projectStatusValue}"></div>
					<th:block th:if="${brief.features ne null}" >
						<span  class="box_span" th:each="feature ,i:${#strings.listSplit(brief.features,'/')}" th:if="${i.index lt 3}" th:text="${feature}"></span></th:block>
				</div>
				<span class="s_time" th:text="${brief.upTime + '更新'}"></span>
			</div>
			<div class="">
				<th:block th:include="sojourn/fragment::touristmenu" th:with="firstNavIndex=7"></th:block>
				<!--免费通话-->
				<div class="housesRight">
					<div class="salesOffice houseRight">
						<p>售楼处电话：</p>
						<span th:utext="${#strings.toString(brief.sortTel).replace('转','<b>转</b>')}"></span>
						<a onclick="showUsercode(2)" class="houseRightBtn" style="cursor:pointer ; display: block;">免费通话</a>
					</div>
					<th:block th:if="${#lists.size(brief.newsInfo) ne 0}">
						<div class="houseRight subscription">
							<div class="houseRinghtTitle">旅居锦囊</div>
							<ul>
								<li th:each="one:${brief.newsInfo}">
									<a th:href="${'/tNews/'+one.newsId+'.htm'}">
										<div th:class="${'lj_tit labels'+one.newsType}" th:text="${one.newsTypeValue}"></div>
										<b th:text="${one.title}"></b>
										<p th:text="${one.newsSummary}"></p>
									</a>
								</li>
							</ul>
						</div>
					</th:block>
				</div>
				<!--项目回答-->
				<div class="deQueAnswer">

					<div class="decommentPJ clearfix">
						<ul>
							<li th:each="one:${dyFilter}">
								<a th:href="${'/tourist/'+projectId+'/dy-'+one.id+'.htm'}"><span th:class="${#strings.equals(dyType,one.id) ?'dynSelec':''}" th:text="${one.name}"></span><img src="https://static.fangxiaoer.com/web/images/lvjuImg/ljGeng.png"/></a>
							</li>
						</ul>
					</div>
					<div class="deAnswer">
						<ul>
							<li class="clearfix" th:each="one:${detail}">
								<div class="dynLiDiv">
									<div class="dynLiDivL">
										<p class="dynDL1" th:text="${#strings.listSplit(one.showTime,'.').get(1) + '-' + #strings.listSplit(one.showTime, '.').get(2)}"></p>
										<p class="dynDL2">-   <i th:text="${#strings.listSplit(one.showTime, '.').get(0)}"></i>  -</p>
									</div>
									<div class="dynLiDivLR" th:if="${#strings.toString(one.infoType) eq '1'}">
										<p class="dynDR1" th:each="info:${#strings.listSplit(one.summary,'【')}" th:text="${'【'+info}"></p>
									</div>
									<div class="dynLiDivLR" th:if="${#strings.toString(one.infoType) ne '1' and #strings.toString(one.infoType) ne '4'}">
										<p class="dynDR1" th:text="${one.summary}"></p>
									</div>
									<div class="dynLiDivLR" th:if="${#strings.toString(one.infoType) eq '4' }">
										<p class="dynDR1" th:text="${'【'+one.license+'】'+one.summary}"></p>
									</div>
								</div>
							</li>
						</ul>
						<div th:include="fragment/page :: page"></div>
					</div>
				</div>
			</div>
	</div>
	<div class="cl"></div>
	<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
	<div th:include="fragment/fragment::tongji"></div>
	</body>
	<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
	<script>
		$(function () {
			$('#qrcode').qrcode(
					{
						width: 60,
						height: 60,
						text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"
					});
			$('#qrcode2').qrcode(
					{
						width: 60,
						height: 60,
						text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"

					});
		});
		var  lilength =  $(".decommentPJ  li").length;
		if (lilength<2){
			$(".decommentPJ  img").hide()
		}

		//浮窗
		window.onscroll = function() {
			var s = $(this).scrollTop();
			if (s >= 256) {
				$(".headFixed").addClass("block");
			} else {
				$(".headFixed").removeClass("block");
			}
		}
	</script>
</html>