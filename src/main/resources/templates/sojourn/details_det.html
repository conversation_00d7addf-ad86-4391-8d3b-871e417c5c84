<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="UTF-8">
		<title th:text="${detail.projectName+'_'+detail.projectName+'基本信息_旅居地产 - 房小二网'}"></title>
		<meta name="keywords"
			  th:content="${detail.projectName+','+detail.projectName+'信息,'+detail.developer+','+detail.WYGS}">
		<meta name="description"
			  th:content="${'房小二网旅居地产栏目为您提供'+detail.projectName+'详细信息，旅居一方，自在优家，尽在房小二网'}">
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<!--<script src="js/details.js" type="text/javascript" charset="utf-8"></script>-->
		<script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/module.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/detailsDet.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
		<script type="text/javascript" src="/js/house/verify.js"></script>
		<script th:inline="javascript">
			/*<![CDATA[*/
			var userName = [[${session.userName}]];
			var phoneNum = [[${session.phoneNum}]];
			var sessionId = [[${session.muser}]];
			var projectId = [[${detail.projectId}]];
			var projectName = [[${detail.projectName}]];
			/*]]>*/
		</script>
	</head>
	<style>

	</style>

	<body>
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
	<div th:include="house/detail/fragment_order::useTouristCode"></div>
	<div th:include="house/detail/fragment_order::guideMessage"></div>

	<div class="content">
		<div class="headFixed" th:include="sojourn/fragment::floatmenu" th:with="firstNavIndex=2"></div>
			<p class="comLocation decomLocation">您的位置：
				<a th:href="@{'/'}">沈阳房产网</a>>
				<a th:href="@{'/houses/'}">沈阳新房</a>>
				<a th:href="@{'/tourist.htm'}">旅居地产</a>>
				<a th:href="${'/tourist/'+brief.projectId+'.htm'}" th:text="${brief.projectName}"></a>
			</p>
			<div class="pro_name">
				<div id="qrcode">
					<div class="layer_wei">
						<h1>手机看房</h1>
						<h2>更方便</h2>
					</div>
				</div>
				<div class="box_sun">
					<p>
						<span th:text="${brief.projectName}"></span>
						<span th:if="${brief.minTemp ne null and brief.maxTemp ne null}" class="centigrade" th:text="${brief.minTemp + '℃~' + brief.maxTemp + '℃'}"></span>
					</p>
					<div class="type_sun wait_1" th:text="${brief.projectStatusValue}"></div>
					<th:block th:if="${brief.features ne null}" >
						<span  class="box_span" th:each="feature ,i:${#strings.listSplit(brief.features,'/')}" th:if="${i.index lt 3}" th:text="${feature}"></span></th:block>
				</div>
				<span class="s_time" th:text="${brief.upTime + '更新'}"></span>
			</div>
			<div class="">
				<th:block th:include="sojourn/fragment::touristmenu" th:with="firstNavIndex=2"></th:block>
				<!--免费通话-->
				<div class="housesRight">
					<div class="salesOffice houseRight">
						<p>售楼处电话：</p>
						<span th:utext="${#strings.toString(brief.sortTel).replace('转','<b>转</b>')}"></span>
						<a onclick="showUsercode(2)" class="houseRightBtn" style="cursor:pointer ; display: block;">免费通话</a>
					</div>
					<th:block th:if="${#lists.size(brief.newsInfo) ne 0}">
					<div class="houseRight subscription">
					<div class="houseRinghtTitle">旅居锦囊</div>
						<ul>
							<li th:each="one:${brief.newsInfo}">
								<a th:href="${'/tNews/'+one.newsId+'.htm'}">
									<div th:class="${'lj_tit labels'+one.newsType}" th:text="${one.newsTypeValue}"></div>
									<b th:text="${one.title}"></b>
									<p th:text="${one.newsSummary}"></p>
								</a>
							</li>
						</ul>
					</div>
					</th:block>
				</div>

				<div class="deQueAnswer">
					<div class="deAnswer">
						<h1>基本信息</h1>
						<ul>
							<li>项目业态：<th:block th:text="${#strings.toString(detail.projectTypes).replaceAll('/',' ')}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.projectTypes) gt 25}" ><th:block th:text="${#strings.toString(detail.projectTypes).replaceAll('/',' ')}"></th:block>
								</p>
							</li>
							<li>特色标签：<th:block th:text="${#strings.toString(detail.featureValue).replaceAll('/',',').substring(0,#strings.length(detail.featureValue)-1)}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.featureValue) gt 25}" ><th:block th:text="${#strings.toString(detail.featureValue).replaceAll('/',' ')}"></th:block>
								</p>
							</li>
							<li>区域板块：<th:block th:text="${detail.region + detail.plate}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.region + detail.plate) gt 25}" ><th:block th:text="${#strings.toString(detail.region + detail.plate).replaceAll('/',' ')}"></th:block>
								</p>
							</li>
							<li>开发商：<th:block th:text="${detail.developer}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.developer) gt 25}" ><th:block th:text="${#strings.toString(detail.developer).replaceAll('/',' ')}"></th:block>
								</p>
							</li>
							<li>交通信息：<th:block th:text="${detail.transportation}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.transportation) gt 25}" ><th:block th:text="${#strings.toString(detail.transportation).replaceAll('/',' ')}"></th:block>
								</p>
							</li>
						</ul>
                        <ul style="    border-top: 1px dashed #ededed;padding-top: 17px;}">
							<div>
								<li>总占地面积：<th:block th:text="${detail.totalArea+'㎡'}"></th:block>
<!--									<p class="content_pop" th:if="${#strings.length(detail.totalArea) gt 25}" ><th:block th:text="${#strings.toString(detail.totalArea+'㎡').replaceAll('/',' ')}"></th:block>-->
<!--									</p>-->
								</li>
								<li>总建筑面积：<th:block th:text="${detail.totalBuildingArea+'㎡'}"></th:block>
<!--									<p class="content_pop" th:if="${#strings.length(detail.totalBuildingArea) gt 25}" ><th:block th:text="${#strings.toString(detail.totalBuildingArea+'㎡').replaceAll('/',' ')}"></th:block>-->
<!--									</p>-->
								</li>
								<li>项目状态：<th:block th:text="${detail.condition}"></th:block>
									<p class="content_pop" th:if="${#strings.length(detail.condition) gt 25}" ><th:block th:text="${#strings.toString(detail.condition).replaceAll('/',' ')}"></th:block>
									</p></li>
								<li>容积率：<th:block th:text="${detail.plotRatio}"></th:block>
									<p class="content_pop" th:if="${#strings.length(detail.plotRatio) gt 25}" ><th:block th:text="${#strings.toString(detail.plotRatio).replaceAll('/',' ')}"></th:block>
									</p></li>
								<li>绿化率：<th:block th:text="${detail.greeningRatio+'%'}"></th:block>
									</li>
								<li>物业公司：<th:block th:text="${detail.WYGS}"></th:block>
									<p class="content_pop" th:if="${#strings.length(detail.WYGS) gt 25}" ><th:block th:text="${#strings.toString(detail.WYGS).replaceAll('/',' ')}"></th:block>
									</p></li>
							</div>

                        </ul>
					</div>
					<div class="deAnswer" th:if="${detail.details.pz ne null}">
						<h1>普宅信息</h1>
						<ul>
							<li>建筑类型：<th:block th:text="${#strings.toString(detail.details.pz.buildTypeName).replaceAll('/',' ')}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.pz.buildTypeName) gt 25}" ><th:block th:text="${#strings.toString(detail.details.pz.buildTypeName).replaceAll('/',' ')}"></th:block>
								</p></li>
                            <li>面积段：<th:block th:text="${#strings.toString(detail.details.pz.areaSection).replaceAll('.00','')}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.pz.areaSection) gt 25}" ><th:block th:text="${#strings.toString(detail.details.pz.areaSection).replaceAll('.00','')}"></th:block>
								</p></li>
                            <li>交付标准：<th:block th:text="${detail.details.pz.fitment}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.pz.fitment) gt 25}" ><th:block th:text="${#strings.toString(detail.details.pz.fitment).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>产权年限：<th:block th:text="${detail.details.pz.propertyRight}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.pz.propertyRight) gt 25}" ><th:block th:text="${#strings.toString(detail.details.pz.propertyRight ).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>公摊：<th:block th:text="${detail.details.pz.poolArea}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.pz.poolArea) gt 25}" ><th:block th:text="${#strings.toString(detail.details.pz.poolArea).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>建筑特色：<th:block th:text="${detail.details.pz.buildFeature}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.pz.buildFeature) gt 25}" ><th:block th:text="${#strings.toString(detail.details.pz.buildFeature).replaceAll('/',' ')}"></th:block>
								</p></li>
						</ul>
					</div>
					<div class="deAnswer" th:if="${detail.details.yf ne null}">
						<h1>洋房信息</h1>
						<ul>
							<li>面积段：<th:block th:text="${#strings.toString(detail.details.yf.areaSection).replaceAll('.00','')}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.yf.areaSection) gt 25}" ><th:block th:text="${#strings.toString(detail.details.yf.areaSection).replaceAll('/',' ')}"></th:block>
								</p></li>
                            <li>交付标准：<th:block th:text="${detail.details.yf.fitment}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.yf.fitment) gt 25}" ><th:block th:text="${#strings.toString(detail.details.yf.fitment).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>产权年限：<th:block th:text="${detail.details.yf.propertyRight}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.yf.propertyRight) gt 25}" ><th:block th:text="${#strings.toString(detail.details.yf.propertyRight).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>物业费：<th:block th:text="${detail.details.yf.propertyFee}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.yf.propertyFee) gt 25}" ><th:block th:text="${#strings.toString(detail.details.yf.propertyFee).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>公摊：<th:block th:text="${detail.details.yf.poolArea}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.yf.poolArea) gt 25}" ><th:block th:text="${#strings.toString(detail.details.yf.poolArea).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li th:if="${#strings.toString(detail.details.yf.buildFeature) ne ''}">建筑特色：<th:block th:text="${detail.details.yf.buildFeature}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.yf.buildFeature) gt 25}" ><th:block th:text="${#strings.toString(detail.details.yf.buildFeature).replaceAll('/',' ')}"></th:block>
								</p></li>
						</ul>
					</div>
					<div class="deAnswer" th:if="${detail.details.sp ne null}">
						<h1>商铺信息</h1>
						<ul>
							<li>出售类型：<th:block th:text="${detail.details.sp.saleType}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.sp.saleType) gt 25}" ><th:block th:text="${#strings.toString(detail.details.sp.saleType).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>面积段：<th:block th:text="${#strings.toString(detail.details.sp.areaSection).replaceAll('.00','')}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.sp.areaSection) gt 25}" ><th:block th:text="${#strings.toString(detail.details.sp.areaSection).replaceAll('.00','')}"></th:block>
								</p></li>
                            <li>产权年限：<th:block th:text="${detail.details.sp.propertyRight}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.sp.propertyRight) gt 25}" ><th:block th:text="${#strings.toString(detail.details.sp.propertyRight).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>物业费：<th:block th:text="${detail.details.sp.propertyFee}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.sp.propertyFee) gt 25}" ><th:block th:text="${#strings.toString(detail.details.sp.propertyFee).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>公摊：<th:block th:text="${detail.details.sp.poolArea}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.sp.poolArea) gt 25}" ><th:block th:text="${#strings.toString(detail.details.sp.poolArea).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li th:if="${detail.details.sp.basement ne null and #strings.toString(detail.details.sp.basement) ne ''}">地下室信息：<th:block th:text="${detail.details.sp.basement}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.sp.basement) gt 25}" ><th:block th:text="${#strings.toString(detail.details.sp.basement).replaceAll('/',' ')}"></th:block>
								</p></li>
						</ul>
					</div>
					<div class="deAnswer" th:if="${detail.details.bs ne null}">
						<h1>别墅信息</h1>
						<ul>
							<li>建筑类型：<th:block th:text="${#strings.toString(detail.details.bs.buildTypeName).replaceAll('/',' ')}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.bs.buildTypeName) gt 25}" ><th:block th:text="${#strings.toString(detail.details.bs.buildTypeName).replaceAll('/',' ')}"></th:block>
								</p></li>
                            <li>面积段：<th:block th:text="${#strings.toString(detail.details.bs.areaSection).replaceAll('.00','')}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.bs.areaSection) gt 25}" ><th:block th:text="${#strings.toString(detail.details.bs.areaSection).replaceAll('.00','')}"></th:block>
								</p></li>
                            <li>产权年限：<th:block th:text="${detail.details.bs.propertyRight}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.bs.propertyRight) gt 25}" ><th:block th:text="${#strings.toString(detail.details.bs.propertyRight).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>物业费：<th:block th:text="${detail.details.bs.propertyFee}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.bs.propertyFee) gt 25}" ><th:block th:text="${#strings.toString(detail.details.bs.propertyFee).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>交付标准：<th:block th:text="${detail.details.bs.fitment}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.bs.fitment) gt 25}" ><th:block th:text="${#strings.toString(detail.details.bs.fitment).replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>总层数：<th:block th:text="${detail.details.bs.totalfloor+'层'}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.bs.totalfloor) gt 25}" ><th:block th:text="${#strings.toString(detail.details.bs.totalfloor+'层').replaceAll('/',' ')}"></th:block>
								</p></li>
							<li>建筑结构：<th:block th:text="${detail.details.bs.buildStructure}"></th:block>
								<p class="content_pop" th:if="${#strings.length(detail.details.bs.buildStructure) gt 25}" ><th:block th:text="${#strings.toString(detail.details.bs.buildStructure).replaceAll('/',' ')}"></th:block>
								</p></li>
						</ul>
					</div>
					<div class="deAnswer" th:if="${detail.details.xzj ne null}">
						<h1>写字间信息</h1>
						<ul>
							<li>产权年限：<th:block th:text="${detail.details.xzj.propertyRight}"></th:block></li>
							<li>总层高：<th:block th:text="${detail.details.xzj.totalfloor}"></th:block></li>
							<li>公摊：<th:block th:text="${detail.details.xzj.poolArea}"></th:block></li>
							<li>交付标准：<th:block th:text="${detail.details.xzj.fitment}"></th:block></li>
							<li>出售类型：<th:block th:text="${detail.details.xzj.saleType}"></th:block></li>
                            <li>面积段：<th:block th:text="${#strings.toString(detail.details.xzj.areaSection).replaceAll('.00','')}"></th:block></li>
							<li>物业费：<th:block th:text="${detail.details.xzj.propertyFee}"></th:block></li>
						</ul>
					</div>
					<div class="deAnswer" th:if="${detail.details.gy ne null}">
						<h1>公寓信息</h1>
						<ul>
							<li>产权年限：<th:block th:text="${detail.details.gy.propertyRight}"></th:block></li>
							<li>总层高：<th:block th:text="${detail.details.gy.totalfloor}"></th:block></li>
							<li>公摊：<th:block th:text="${detail.details.gy.poolArea}"></th:block></li>
							<li>交付标准：<th:block th:text="${detail.details.gy.fitment}"></th:block></li>
							<li>面积段：<th:block th:text="${#strings.toString(detail.details.gy.areaSection).replaceAll('.00','')}"></th:block></li>
                            <li>物业费：<th:block th:text="${detail.details.gy.propertyFee}"></th:block></li>
						</ul>
					</div>
					<div class="deAnswer">
						<h1>销售信息</h1>
						<p>售楼处地址：<th:block th:text="${detail.saleAddress}"></th:block></p>
						<p th:if="${#lists.size(detail.exist) ne 0}">交房时间：<th:block th:text="${detail.exist[0].showTime+'  '+detail.exist[0].summary}"></th:block></p>
						<p th:if="${#lists.size(detail.license) eq 0}">预售许可证： 暂无资料</p>
						<th:block th:if="${#lists.size(detail.license) ne 0}">
                        <p>预售许可证：</p>
						<table class="detTable" border="" cellspacing="" cellpadding="">
							<tr>
								<th width="210"></th>
								<th width="210">发证时间</th>
								<th>绑定楼栋</th>
							</tr>
							<tr th:each="one:${detail.license}">
								<td th:text="${one.license}"></td>
								<td th:text="${one.showTime}"></td>
								<td th:text="${one.summary}"></td>
							</tr>
                        </table>
						<div class="dettage">
							<span class="dettageS">
								展开
							</span>
						</div>
                        </th:block>
					</div>
					<div class="deAnswer">
						<h1>项目简介</h1>
						<p th:text="${detail.projectDescription}"></p>
					</div>
					<div class="deAnswer">
						<h1>开发商简介</h1>
						<p th:text="${detail.developerDescription}"></p>
					</div>
				</div>

			</div>
		</div>
				<div class="cl"></div>
				<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
				<div th:include="fragment/fragment::tongji"></div>


	</body>
	<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
	<script type="text/javascript">
		$(function () {
			$('#qrcode').qrcode(
					{
						width: 60,
						height: 60,
						text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"
					});
			$('#qrcode2').qrcode(
					{
						width: 60,
						height: 60,
						text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"

					});
		});
		$(".depaly .swiper-slide").click(function() {
			$(".Where_play").show();
			var hhh = $(".depaly .swiper-slide").index(this)
			var swiper2 = new Swiper('.expandBg', {
				spaceBetween: 0,

				pagination: {
					el: '.swiper-pagination',
					type: 'fraction',
				},
				navigation: {
					nextEl: '.swiper-button-next',
					prevEl: '.swiper-button-prev',
				},
			});
		})

		var swiper = new Swiper('.deWPswiper', {
			slidesPerView: 3,
			spaceBetween: 15,
			slidesPerGroup: 3,
			loop: true,
			loopFillGroupWithBlank: true,
			pagination: {
				el: '.swiper-pagination',
				clickable: true,
			},
			navigation: {
				nextEl: '.swiper-button-next',
				prevEl: '.swiper-button-prev',
			},
		});
		var f = false;
		$(".detTable tr").filter(":lt(2)").show().end().filter(":gt(1)").hide()
	var trLeng=	$(".detTable tr").length;
if (trLeng<3){
	$(".dettageS").hide()
}
		$(".dettageS").click(function() {
			if(f = !f) {
				$(this).html("收起");
				$(this).addClass('deSpan');
				$(".detTable tr").show()
			} else {
				$(this).html("展开");
				$(this).removeClass("deSpan");
				$(".detTable tr").filter(":lt(2)").show().end().filter(":gt(1)").hide()

			}
		})
		//浮窗
		window.onscroll = function() {
			var s = $(this).scrollTop();
			if (s >= 256) {
				$(".headFixed").addClass("block");
			} else {
				$(".headFixed").removeClass("block");
			}
		}
	</script>

</html>