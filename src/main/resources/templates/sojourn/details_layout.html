<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="UTF-8">
		<title th:text="${'户型图_'+brief.projectName+'户型图_旅居地产 - 房小二网'}"></title>
		<meta name="keywords" th:content="${brief.projectName+','+brief.projectName+'户型图,'+brief.projectName+'户型结构'}">
		<meta name="description"
			  th:content="${'房小二网'+brief.projectName+'户型图为您提供'+brief.projectName+'的户型图，户型结构等各种必要信息，旅居一方，自在优家，尽在房小二网'}">
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<!--<script src="js/details.js" type="text/javascript" charset="utf-8"></script>-->
		<script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/module.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/detailsDmo.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
		<script type="text/javascript" src="/js/house/verify.js"></script>
		<script type="application/javascript" src="https://static.fangxiaoer.com/js/layout.js?t=20170904" charset="utf-8"></script>
		<script th:inline="javascript">
			/*<![CDATA[*/
			var userName = [[${session.userName}]];
			var phoneNum = [[${session.phoneNum}]];
			var sessionId = [[${session.muser}]];
			var projectId = [[${brief.projectId}]];
			var projectName = [[${brief.projectName}]];
			/*]]>*/
		</script>

	</head>

	<body>
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
	<div th:include="house/detail/fragment_order::useTouristCode"></div>
	<div th:include="house/detail/fragment_order::guideMessage"></div>

	<div class="content">
		<div class="headFixed" th:include="sojourn/fragment::floatmenu" th:with="firstNavIndex=3"></div>
			<p class="comLocation decomLocation">您的位置：
				<a th:href="@{'/'}">沈阳房产网</a>>
				<a th:href="@{'/houses/'}">沈阳新房</a>>
				<a th:href="@{'/tourist.htm'}">旅居地产</a>>
				<a th:href="${'/tourist/'+brief.projectId+'.htm'}" th:text="${brief.projectName}"></a>
			</p>
			<div class="pro_name">
				<div id="qrcode">
					<div class="layer_wei">
						<h1>手机看房</h1>
						<h2>更方便</h2>
					</div>
<!--			</div>
				<div class="box_sun">
					<p>
						<span th:text="${brief.projectName}"></span>
						<span th:if="${brief.minTemp ne null and brief.maxTemp ne null}" class="centigrade" th:text="${brief.minTemp + '℃~' + brief.maxTemp + '℃'}"></span>
					</p>
					<div class="type_sun wait_1" th:text="${brief.projectStatusValue}"></div>
					<th:block th:if="${brief.features ne null}" >
						<span   class="box_span" th:each="feature ,i:${#strings.listSplit(brief.features,'/')}" th:if="${i.index lt 3}" th:text="${feature}"></span></th:block>
				</div>
				<span class="s_time" th:text="${brief.upTime + '更新'}"></span>
			</div>
			<div class="">
				<th:block th:include="sojourn/fragment::touristmenu" th:with="firstNavIndex=3"></th:block>
				<!--免费通话-->
				<div class="deoList">
					<p>类&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;型&nbsp;:</p>
					<a th:class="${roomId[0].selected and layoutType[0].selected ? 'deoSel':''}" th:href="${'/tourist/'+ projectId +'/layout/pid'+ projectId}" th:text="${'全部('+detail.totalFilter+')'}"></a>
					<a th:each="roomId:${roomId}" th:class="${roomId.selected ? 'deoSel':''}" th:if="${roomId.name ne '全部' }" th:text="${roomId.name}" th:href="${roomId.url}"></a>
					<a th:each="layoutType:${layoutType}" th:class="${layoutType.selected ? 'deoSel':''}" th:if="${layoutType.name ne '全部' }" th:text="${layoutType.name}" th:href="${layoutType.url}"></a>

				</div>
				<div class="search">
					<a class="btn_left zbefore"></a>
					<a class="btn_right next"></a>
					<div class="search_gd">
						<ul style="margin-left: 0px;">
							<li th:each="room,index:${layId}" th:if="${index.count ne 1}" th:id="${'lay_' + room.id}" th:class="${(layId[0].selected and index.count eq 2) or room.selected ? 'hover':''}">
								<a th:href="${room.url}"><th:block th:text="${room.name}"></th:block><i th:if="${layFilter[index.index-1].state eq '1'}" class="saleTypeIcon saleTypeIcon1">在售</i>
                                    <i th:if="${layFilter[index.index-1].state eq '2'}" class="saleTypeIcon saleTypeIcon2">待售</i>
                                    <i th:if="${layFilter[index.index-1].state eq '3'}" class="saleTypeIcon saleTypeIcon3">售罄</i><br>建筑面积：<th:block th:text="${#strings.toString(layFilter[index.index-1].area).replace('.0','')+'㎡'}"></th:block>

								</a>
							</li>
						</ul>
					</div>
				</div>
				<div class="w huxingMain">
					<!--基本信息-->
					<div class="pic">
						<p class="lunbohx">户型图（<span class="index">1</span>/<span class="all" th:text="${#lists.toList(detail.pics).size()}"></span>）</p>
						<div class="show">

							<div class="lunbo" >
								<a th:each="pic:${detail.pics}"  class="various1" target="_blank"><img   th:src="${pic.imageUrl}" th:alt="${pic.title}"></a>
							</div>
						</div>
						<div class="left_btn">
							<img src="https://static.fangxiaoer.com/web/images/Villa/left_btn.png" alt="">
						</div>
						<div class="right_btn">
							<img src="https://static.fangxiaoer.com/web/images/Villa/right_btn.png" alt="">
						</div>
						<div class="look">
							<h1>查看大图</h1></div>
					</div>
					<div class="right">
						<div class="style">
							<th:block th:text="${detail.title}"></th:block>
							<s th:if="${detail.state eq '1'}" class="saleTypeIcon saleTypeIcon1 titleTypeR">在售</s>
							<s th:if="${detail.state eq '2'}" class="saleTypeIcon saleTypeIcon2 titleTypeR">待售</s>
							<s th:if="${detail.state eq '3'}" class="saleTypeIcon saleTypeIcon3 titleTypeR">售罄</s>
							<i style="color:#48add6;background: #eff3fd;border: none" th:text="${detail.buildTypeName}"></i>
						</div>
						<!--普宅：户型 类型 建筑面积 朝向-->
						<!--公寓：户型  建筑面积 朝向-->
						<!--写字间：建筑面积-->
						<!--别墅：户型 类型 建筑面积 -->
						<div class="info">
							<ul class="type_wei_wun">
								<li class="huxing_wei" th:if="${#strings.toString(detail.layoutType) ne '3'}"><label>户　　型</label>
									<p th:text="${detail.layOut}"></p>

								</li>

								<li class="type_wei" th:if="${#strings.toString(detail.layoutType) eq '1' or #strings.toString(detail.layoutType) eq '2'}"><label>类　　型</label>
									<p th:text="${detail.buildTypeName}"></p>
								</li>
								<div class="cl"></div>
								<li class="jian_wei"><label>建筑面积</label>
									<p th:text="${#strings.toString(detail.buildArea).replace('.0','') + '㎡'}"></p>
								</li>
								<li class="jian_wei" th:if="${#strings.toString(detail.layoutType) eq '1' or #strings.toString(detail.layoutType) eq '4'}"><label>朝　　向</label>
									<p th:text="${detail.forwardTypeName}"></p>
								</li>
								<div class="cl"></div>
							</ul>
							<div class="price dmoPri">
								<label class="dmoLab">参考价格</label>
								<ul>
									<li class="dmoLabLi" th:utext="${#strings.toString(detail.price) eq'0'?'<b>待定</b>':'<i>'+ detail.price+'</i>元/㎡'}"></li>
								</ul>
							</div>
							<div class="price" style="border-bottom: 1px solid #EDEDED;">
								<label class="dmoLab">参考总价</label>
								<ul>
									<li class="dmoLabLi"><th:block th:utext="${#strings.toString(detail.totalPrice).replace('.0','') eq'0'?'<b>待定</b>':'<i>'+ #strings.toString(detail.totalPrice).replace('.0','')+'</i>万元/套'}">
									</th:block><th:block th:unless="${#strings.toString(detail.totalPrice).replace('.0','') eq'0'}">[参考总价=参考均价*建筑面积]</th:block></li>
								</ul>
							</div>
							<ul>
								<div class="cl"></div>
								<li class="dmoDmo" style="overflow: hidden; ">
									<label>户型解析</label>
									<p class="huxing_p" th:text="${detail.layDesc}"></p>
								</li>
							</ul>
						</div>
						<div class="tel1" style="line-height: 33px;margin-top: 24px;"><i>售楼处咨询电话</i><b th:utext="${#strings.toString(brief.sortTel).replace('转','<b>转</b>')}"></b>
						</div>
						<div class="tel2 " style="display: block;">
							<a onclick="showUsercode(2) " style="cursor:pointer ; display: block;margin-top: 5px;">免费通话</a>
						</div>
						<div></div>
					</div>
					<div class="cl"></div>
				</div>
			</div>

			<!--户型弹窗-->
			<div class="layer_box">
				<table>
					<tr>
						<td>
							<div class="layer">
								<p>户型图（<span class="index">1</span>/<span class="all" th:text="${#lists.toList(detail.pics).size()}"></span>）</p>
								<div class="photo">
									<div class="lunbo">
										<ul>
											<li th:each="pic:${detail.pics}">
												<img th:src="${pic.imageUrl}"/>
											</li>
											<div class="clearfix"></div>
										</ul>
									</div>
								</div>
								<div class="close">
									<img src="https://static.fangxiaoer.com/web/images/Villa/close.png" />
								</div>
								<div class="left_btn">
									<img src="https://static.fangxiaoer.com/web/images/Villa/left_wei.png" class="left_wei" />
									<img src="https://static.fangxiaoer.com/web/images/Villa/left_wei02.png" class="hover" />
								</div>
								<div class="right_btn">
									<img src="https://static.fangxiaoer.com/web/images/Villa/right_wei.png" class="right_wei" />
									<img src="https://static.fangxiaoer.com/web/images/Villa/right_wei02.png" class="hover" />
								</div>
							</div>
						</td>
					</tr>
				</table>
			</div>

		</div>
		<div class="cl"></div>
		<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
		<div th:include="fragment/fragment::tongji"></div>
	</body>
	<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
	<script type="text/javascript">
		$(function () {
			$('#qrcode').qrcode(
					{
						width: 60,
						height: 60,
						text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"

					});
			$('#qrcode2').qrcode(
					{
						width: 60,
						height: 60,
						text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"

					});
		});
		$(function() {
			id = $(".pic img").attr("lang")
			$("#" + id).addClass("hover")

			var n = $(".search_gd li").length
			if($(".search_gd li.hover").index() < 1) {
				$(".btn_left").removeClass("before")
				$(".btn_left").addClass("zbefore")
			}
			if($(".search_gd li.hover").index() > n - 2) {
				$(".btn_right").removeClass("next")
				$(".btn_right").addClass("znext")
			}

			tt = ($(".search_gd li.hover").index() - 7) * 142
			//if (tt > 0) { $(".search_gd ul").css("left", -tt) }
			//向右滑动
			$(".btn_right").click(function() {
				if(num1 < num0) {
					num1++;
					$(".search_gd ul").animate({
						marginLeft: num1 * -1105 + "px"
					}, 1000);
					$(".btn_left").removeClass("zbefore");

				} else if(num1 == num0) {
					$(".search_gd ul").animate({
						marginLeft: num1 * -1105 + "px"
					}, 1000);
				}
				if(num1 == num0) {
					$(".btn_right").addClass("znext")
				}
				console.log(num1);

			})
			//向左滑动
			$(".btn_left").click(function() {
				if(num1 > 0) {
					num1--;
					$(".search_gd ul").animate({
						marginLeft: num1 * -1105 + "px"
					}, 500);
					$(".btn_right").removeClass("znext");
				} else if(num1 == 0) {
					$(".search_gd ul").animate({
						marginLeft: num1 * -1105 + "px"
					}, 500);
				}
				if(num1 == 0) {
					$(".btn_left").addClass("zbefore");
				}
			})
			var ind = $(".search_gd ul").children().length; //
			//var num = Math.floor(ind + 1 / 8);
			var num0 = Math.floor(ind / 7);
			var num1 = Math.floor($(".search_gd .hover").index() / 7); //
			//如果不足8个不加箭头
			if(ind <= 7) {
				$(".searh li").css("margin-right", "26px");
				$(".search_gd").width("1170px");
				$(".btn_right").css("display", "none");
				$(".btn_left").css("display", "none");
                $(".search_gd").css("margin","0");
			}
			//如果没有选中默认选中第一个
			if(num1 == -1) {
				num1 = 0;
				$(".search_gd ul li").eq(0).addClass("hover");
			}
			//设置初始位置
			$(".search_gd ul").css({
				"margin-left": num1 * -1105 + "px"
			});
			if(num1 == 0) {
				$(".btn_left").addClass("zbefore");
			}
			if(num1 == num0) {
				$(".btn_right").addClass("znext")
			}
		})
		$(".various1 img,.look").click(function(){
			$(".layer_box").show();
		})
		$(".close").click(function(){
			$(".layer_box").hide();

		})
		//浮窗
		window.onscroll = function() {
			var s = $(this).scrollTop();
			if (s >= 256) {
				$(".headFixed").addClass("block");
			} else {
				$(".headFixed").removeClass("block");
			}
		}


	//基础页轮播
        var img_x=$(".various1").length;
		console.log(img_x)
        $(".pic").mouseenter(
            function () {
                if (img_x>1){
                    $(".pic .left_btn").show()
                    $(".pic .right_btn").show()
                }
            }
        )
        $(".pic").mouseleave(
            function () {
                $(".pic .left_btn").hide()
                $(".pic .right_btn").hide()
            }
        )

        $(".pic .lunbo").width(($(".various1").length+1)*400)
        $(".pic .lunbo a:last").after($(".pic .lunbo a:first").clone())
        var index_x=0
        var layer_x=0
        // $(".big_wei .content_box .information .photo .lunbo .cl").before($(".big_wei .content_box .information .photo .lunbo img:first").clone())
        $(".pic .left_btn").click(
            function (e) {
                e.stopPropagation();
                if (index_x==0){
                    index_x=$(".various1").length-1
                    layer_x=index_x
                    $(".pic .index").text(index_x)
                    $(".layer_box table .layer .index").text(index_x)
                    $(".pic .lunbo").css("left",-index_x*400)
                    index_x--
                    layer_x=index_x
                    $(".pic .lunbo").animate({left:-index_x*400},300)
                    $(".layer_box .layer .photo .lunbo").animate({left:-layer_x*600},300,function(){
                        if(layer_x==$(".layer_box .layer .photo ul li").length-1){
                            $(".layer_box .layer .photo .lunbo").css("left",0)
                            layer_x=0
                        }
                    })
                }else{
                    if(index_x>0){
                        index_x--
                        layer_x=index_x
                        $(".pic .index").text(index_x+1)
                        $(".layer_box table .layer .index").text(index_x+1)
                        $(".pic .lunbo").animate({left:-index_x*400},300)
                        $(".layer_box .layer .photo .lunbo").animate({left:-layer_x*600},300,function(){
                            if(layer_x==$(".layer_box .layer .photo ul li").length-1){
                                $(".layer_box .layer .photo .lunbo").css("left",0)
                                layer_x=0
                            }
                        })

                    }
                }
            }
        )
        $(".pic .right_btn").click(
            function (e) {
                e.stopPropagation();
                if(index_x<$(".various1").length-1){
                    index_x++
                    layer_x=index_x
                    if(index_x==$(".various1").length-1){

                        $(".pic .index").text(1)
                        $(".layer_box table .layer .index").text(1)
                    }else{
                        $(".pic .index").text(index_x+1)
                        $(".layer_box table .layer .index").text(index_x+1)
                    }
                    $(".pic .lunbo").animate({left:-index_x*400},300,function(){
                        if(index_x==$(".various1").length-1){
                            $(".pic .lunbo").css("left",0)
                            index_x=0
                            layer_x=index_x
                        }
                    })
                    $(".layer_box .layer .photo .lunbo").animate({left:-layer_x*600},300,function(){
                        if(layer_x==$(".layer_box .layer .photo ul li").length-1){
                            $(".layer_box .layer .photo .lunbo").css("left",0)
                            layer_x=0
                        }
                    })
                }
            }
        )




    </script>

</html>