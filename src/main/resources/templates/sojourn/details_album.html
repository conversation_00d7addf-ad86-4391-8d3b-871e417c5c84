<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
	<meta charset="utf-8" />
    <meta charset="UTF-8">
    <title th:text="${brief.projectName+'_'+brief.projectName+'图片 - 房小二网'}"></title>
    <meta name="keywords" th:content="${brief.projectName+','+brief.projectName+'图片,'+brief.projectName+'照片'}">
    <meta name="description"
          th:content="${'房小二网旅居地产栏目为您提供'+brief.projectName+'的大量图片与照片，旅居一方，自在优家，尽在房小二网'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css" />
	<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script><script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/lvAlbum.css" />
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/imgShow.js"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/module.css" />

    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
    <script type="text/javascript" src="/js/house/verify.js"></script>
    <script  type="text/javascript" >
        $(document).ready(function(){
            $(".showImg").show()
            $(".showList").hide()
            $(".imgList").show()
            $(".highDefinition").hide()
        });
    </script>
    <style>

    </style>
</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
<div th:include="house/detail/fragment_order::useCode"></div>
<div th:include="house/detail/fragment_order::guideMessage"></div>
<form name="form1" method="post" action="12_0" id="form1">
<div class="content">
    <div class="headFixed" th:include="sojourn/fragment::floatmenu" th:with="firstNavIndex=4"></div>
    <p class="comLocation decomLocation">您的位置：
        <a th:href="@{'/'}">沈阳房产网</a>>
        <a th:href="@{'/houses/'}">沈阳新房</a>>
        <a th:href="@{'/tourist.htm'}">旅居地产</a>>
        <a th:href="${'/tourist/'+brief.projectId+'.htm'}" th:text="${brief.projectName}"></a>
    </p>
    <div class="pro_name">
        <div id="qrcode">
            <div class="layer_wei">
                <h1>手机看房</h1>
                <h2>更方便</h2>
            </div>
        </div>
        <div class="box_sun">
            <p>
                <span th:text="${brief.projectName}"></span>
                <span th:if="${brief.minTemp ne null and brief.maxTemp ne null}" class="centigrade" th:text="${brief.minTemp + '℃~' + brief.maxTemp + '℃'}"></span>
            </p>
            <div class="type_sun wait_1" th:text="${brief.projectStatusValue}"></div>
            <th:block th:if="${brief.features ne null}" >
                <span   class="box_span" th:each="feature ,i:${#strings.listSplit(brief.features,'/')}" th:if="${i.index lt 3}" th:text="${feature}"></span></th:block>
        </div>
        <span class="s_time" th:text="${brief.upTime + '更新'}"></span>
    </div>
    <th:block th:include="sojourn/fragment::touristmenu" th:with="firstNavIndex=4"></th:block>
    <div class="w">
        <div class="left leftImg">
            <div class="imgList">
                <div class="position_pbl">
                    <ul class="bigImg">
                        <li th:each="photo:${projectAlbum}">
                            <div>
                                <img th:src="${#strings.replace(photo.imageUrl, 'middle', 'big')}" th:alt="${photo.photoName}"  >
                                <th:block th:if="${photo.photoType eq 'hxt'}">
                                    <span th:class="${'type_sun wait_'+photo.state}"><th:block th:text="${photo.state eq '3'?'售罄':(photo.state eq '2'?'待售':'在售')}"></th:block></span>
                                </th:block>
                            </div>
                            <p th:text="${photo.photoName}"></p>
                        </li>
                    </ul>
                </div>
                <div class="imgPage">
                    <div class="homePage">首页</div>
                    <div class="prev">上一页</div>
                    <ul>
                        <li class="hover">1</li>
                        <li>2</li>
                        <li>3</li>
                        <li>4</li>
                        <li>5</li>
                    </ul>
                    <div class="next">下一页</div>
                    <div class="trailerPage">尾页</div>
                </div>
            </div>
        <div class="highDefinition">
            <div class="highImg">
                <ul style="margin-left: -900px;">
                    <li th:each="photo:${projectAlbum}">
                        <div><img th:src="${#strings.replace(photo.imageUrl, 'middle', 'big')}" th:alt="${photo.photoName}" onload="imgShow.imgSize(this)" ></div>
                        <p th:text="${photo.photoName ne '' ? photo.photoName :''}"  style="text-align: left;padding-left: 10px"></p>
                    </li>
                </ul>
                <div class="highImgPrev"></div>
                <div class="highImgNext"></div>
                <div class="firstImg"><span>已经是是第一张了</span></div>

            </div>
            <div class="highList">
                <div class="highListShow">
                    <ul>
                        <li th:each="photo:${projectAlbum}">
                            <div>
                                <img th:src="${#strings.replace(photo.imageUrl, 'middle', 'big')}"  th:alt="${photo.photoName}">
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="highListPrev"></div>
                <div class="highListNext"></div>
            </div>
        </div>

    </div>
        <div class="photoSearch">
            <p class="showList"><img src="https://static.fangxiaoer.com/web/images/ico/sign/photo_list_sun.gif" alt="" />列表查看</p>
            <a th:href="'/tourist/'+${projectId} + '/album.htm'"  th:class = "${#strings.isEmpty(photoType)}? 'hover':''">全部<span ></span><img src='https://static.fangxiaoer.com/web/images/ico/sign/rt_jt.png' ></a>
            <a th:each="colum:${albumColumn}" th:class="${colum.photoType == photoType}?'hover':''" th:href="'/tourist/'+${projectId}+ '/album/'+ ${colum.photoType} + '.htm'"> <th:block th:text="${colum.name}"></th:block>
                <span th:text="'('+${colum.photoNum}+')'"></span><img src='https://static.fangxiaoer.com/web/images/ico/sign/rt_jt.png'></a>
        </div>
    </div>
</div>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <script>
        $(function () {
            $('#qrcode').qrcode(
                {
                    width: 60,
                    height: 60,
                    text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"

                });
            $('#qrcode2').qrcode(
            		{
            			width: 60,
            			height: 60,
                        text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"

                    });
        });
    imgPage.init();
    imgShow.init();
    switchover.init()
    //图片自适应
    imgShow.imgSize();


        //浮窗
        window.onscroll = function() {
            var s = $(this).scrollTop();
            if (s >= 256) {
                $(".headFixed").addClass("block");
            } else {
                $(".headFixed").removeClass("block");
            }
        }
</script>
<div class="cl"></div>
<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::tongji"></div>
</form>
</body>
</html>