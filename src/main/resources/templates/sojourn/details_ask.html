<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="UTF-8">
		<title th:text="${brief.projectName+'_'+brief.projectName+'咨询_'+brief.projectName+'客服 - 房小二网'}"></title>
		<meta name="keywords"
			  th:content="${brief.projectName+','+brief.projectName+'楼盘咨询,'+brief.projectName+'客服,'+brief.projectName+'电话号码'+brief.sortTel}">
		<meta name="description"
			  th:content="${'房小二网为您提供'+brief.projectName+'最新咨询与客服解答，官方咨询热线：'+brief.sortTel+'以及快捷的'+brief.projectName+'项目详情咨询服务，专人为您第一时间解答'}">
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<!--<script src="js/details.js" type="text/javascript" charset="utf-8"></script>-->
		<script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/module.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/detailsAsk.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522" />
		<script type="text/javascript" src="/js/house/verify.js"></script>
		<script th:inline="javascript">
			/*<![CDATA[*/
			var userName = [[${session.userName}]];
			var phoneNum = [[${session.phoneNum}]];
			var sessionId = [[${session.muser}]];
			var projectId = [[${brief.projectId}]];
			var projectName = [[${brief.projectName}]];
			/*]]>*/
		</script>
	</head>
	<style>
		.clearfix {
			clear: both;
			content: "";
			display: block;
			overflow: inherit;
		}
	</style>
	<body>
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>
	<div th:include="house/detail/fragment_order::useTouristCode"></div>
	<div th:include="house/detail/fragment_order::guideMessage"></div>
	<div th:include="house/detail/fragment_login::login"></div>
	<div class="content">
		<div class="headFixed" th:include="sojourn/fragment::floatmenu" th:with="firstNavIndex=6"></div>
		<p class="comLocation decomLocation">您的位置：
			<a th:href="@{'/'}">沈阳房产网</a>>
			<a th:href="@{'/houses/'}">沈阳新房</a>>
			<a th:href="@{'/tourist.htm'}">旅居地产</a>>
			<a th:href="${'/tourist/'+brief.projectId+'.htm'}" th:text="${brief.projectName}"></a>
		</p>
		<div class="pro_name">
			<div id="qrcode">
				<div class="layer_wei">
					<h1>手机看房</h1>
					<h2>更方便</h2>
				</div>
			</div>
			<div class="box_sun">
				<p>
					<span th:text="${brief.projectName}"></span>
					<span th:if="${brief.minTemp ne null and brief.maxTemp ne null}" class="centigrade" th:text="${brief.minTemp + '℃~' + brief.maxTemp + '℃'}"></span>
				</p>
				<div class="type_sun wait_1" th:text="${brief.projectStatusValue}"></div>
				<th:block th:if="${brief.features ne null}" >
					<span class="box_span" th:each="feature ,i:${#strings.listSplit(brief.features,'/')}" th:if="${i.index lt 3}" th:text="${feature}"></span></th:block>
			</div>
			<span class="s_time" th:text="${brief.upTime + '更新'}"></span>
		</div>
		<div class="">
			<th:block th:include="sojourn/fragment::touristmenu" th:with="firstNavIndex=6"></th:block>
			<!--免费通话-->
			<div class="housesRight">
				<div class="salesOffice houseRight">
					<p>售楼处电话：</p>
					<span th:utext="${#strings.toString(brief.sortTel).replace('转','<b>转</b>')}"></span>
					<a onclick="showUsercode(2)" class="houseRightBtn" style="cursor:pointer ; display: block;">免费通话</a>
				</div>
				<th:block th:if="${#lists.size(brief.newsInfo) ne 0}">
					<div class="houseRight subscription">
						<div class="houseRinghtTitle">旅居锦囊</div>
						<ul>
							<li th:each="one:${brief.newsInfo}">
								<a th:href="${'/tNews/'+one.newsId+'.htm'}">
									<div th:class="${'lj_tit labels'+one.newsType}" th:text="${one.newsTypeValue}"></div>
									<b th:text="${one.title}"></b>
									<p th:text="${one.newsSummary}"></p>
								</a>
							</li>
						</ul>
					</div>
				</th:block>
			</div>
<!--			项目回答-->
			<div class="deQueAnswer" th:if="${asks ne null and #lists.size(asks) ne 0}">
				<div class="decommentPJ clearfix">
					<span>关于<i th:text="${brief.projectName}"></i>的<i th:text="${askCount}"></i>个问题</span>
					<a th:if="${#session?.getAttribute('sessionId') == null}" class="askbutton"  target="_blank" data-toggle="modal" href="#login">我要提问</a>

					<button th:if="${#session?.getAttribute('sessionId') != null}" onclick="javascrpit:addProjectAsk();">我要提问</button>
				</div>

				<div class="deAnswer">
					<ul>
						<li class="clearfix" th:each="one:${asks}">
							<div class="deAnswerImg askAnswerImg">
								<img src="https://static.fangxiaoer.com/web/images/lvjuImg/xiaoxi.png" />
							</div>
							<div class="deAnswerCon askAnswerCon">
								<h1 th:text="${one.askInfo}"></h1>
								<div class="deAnswerConPho">
									<span th:text="${#strings.substring(one.phone,0,3)+'****'+#strings.substring(one.phone,7)}"></span>
									<i th:text="${one.addTime}"></i>
								</div>
								<div class="deAnswerConQuestion">
									<span>房小二网客服回复:</span>
									<p th:text="${one.replyInfo}"></p>
								</div>
							</div>
						</li>
					</ul>
					<div th:include="fragment/page :: page"></div>
				</div>
			</div>
			<div class="deQueAnswer deqA" th:unless="${asks ne null and #lists.size(asks) ne 0}">
<!--				<div class="deQueAnswer" th:unless="${asks ne null and #lists.size(asks) ne 0}">-->
				<div>说出您对楼盘的疑问，我们帮您答疑解惑！</div>
				<a th:if="${#session?.getAttribute('sessionId') == null}" class="loginQASK"  target="_blank" data-toggle="modal" href="#login">我要提问</a>

				<button th:if="${#session?.getAttribute('sessionId') != null}"  onclick="javascrpit:addProjectAsk();">我要提问</button>
			</div>
		</div>
		<!--咨询说明-->
		<div class="deConsulting" th:if="${asks ne null and #lists.size(asks) ne 0}">
			<div class="deCondiv">
				<p>咨询说明：</p>
				<p>1、项目咨询是房小二网为您提供的疑难问题解答板块，在这里你可以与工作人员直接交流。</p>
				<p>2、因提问的用户较多，可能无法及时进行回复，我们将尽力在最快的时间内为您解答。</p>
				<p>3、当涉及广告、不实评论、恶意评论、爆粗口、危害国家安全等不当言论时，房小二网有权实施封号等管理行为。</p>
			</div>

		</div>
	</div>
	<div class="cl"></div>
	<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
	<div th:include="fragment/fragment::tongji"></div>
	</body>
	<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>

	<script type="text/javascript">
		function addProjectAsk() {
			$("#iNeedAsk").show();
		}

		//  var swiper = new Swiper('.swiper-container', {
		//    slidesPerView: 3,
		//    spaceBetween: 30,
		//    slidesPerGroup: 1,
		//    loop: true,
		//    loopFillGroupWithBlank: true,
		//    pagination: {
		//      el: '.swiper-pagination',
		//      clickable: true,
		//    },
		//    navigation: {
		//      nextEl: '.swiper-button-next',
		//      prevEl: '.swiper-button-prev',
		//    },
		//  });
		$(".depaly .swiper-slide").click(function() {
			$(".Where_play").show();
			var hhh = $(".depaly .swiper-slide").index(this)
			var swiper2 = new Swiper('.expandBg', {
				spaceBetween: 0,

				pagination: {
					el: '.swiper-pagination',
					type: 'fraction',
				},
				navigation: {
					nextEl: '.swiper-button-next',
					prevEl: '.swiper-button-prev',
				},
			});
		})

		var swiper = new Swiper('.deWPswiper', {
			slidesPerView: 3,
			spaceBetween: 15,
			slidesPerGroup: 3,
			loop: true,
			loopFillGroupWithBlank: true,
			pagination: {
				el: '.swiper-pagination',
				clickable: true,
			},
			navigation: {
				nextEl: '.swiper-button-next',
				prevEl: '.swiper-button-prev',
			},
		});
	</script>
	<div class="cl"></div>
	<form action="post">


	<div>
		<input type="hidden" id="forSessionId" th:value="${#session?.getAttribute('sessionId')}">
		<div class="kfzc_heibu" style="display: none;" id="iNeedAsk">
			<div class="kfzc" style="display: block;margin-top: -160px;">
				<h1 th:text="${'提问：'+brief.projectName}"></h1>
				<textarea name="" id="instantlyAsk" rows="" cols="" onkeyup="words_deal();"></textarea>
				<div style="float: inherit;">
					<span id="textCount">0</span><th:block>/300</th:block>
				</div>
				<a href="javascript:addProjectInfoAsk();">立即提问</a>
				<img id="closeBut" src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" />
			</div>
		</div>
		<div class="yydk_heibu" style="display: none;" id="askSuccess">
			<div class="yyDk">
				<div class="yyDk_close"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png"></div>
				<div>
					<div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"></div>
					<p>提交成功</p>
					<p>我们会尽快审核您的问题，及时为您解答</p>
				</div>

			</div>
		</div>
		<script type="text/javascript">
			$(function () {
				$('#qrcode').qrcode(
						{
							width: 60,
							height: 60,
							text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"

						});
				$('#qrcode2').qrcode(
						{
							width: 60,
							height: 60,
							text: "https://m.fangxiaoer.com/tourist/"+[[${brief.projectId}]]+ ".htm"

						});
			});

            $("#closeBut").click(function () {
                $("#iNeedAsk").hide();
            });
            function words_deal() {
                var curLength = $("#instantlyAsk").val().length;
                if (curLength > 300) {
                    var num = $("#instantlyAsk").val().substr(0, 300);
                    $("#instantlyAsk").val(num);
                    alert("超过字数限制，多出的字将被截断！");
                }
                else {
                    $("#textCount").text($("#instantlyAsk").val().length);
                }
            }
            function addProjectInfoAsk() {
                var Projectid = [[${brief.projectId}]];
                var sessionId = $("#forSessionId").val();
                var content = $("#instantlyAsk").val();
                if (!sessionId) {
                    alert("请先登录");
                }else if(!content){
                    alert("请填写提问内容");
                }else {
                    $.ajax({
						data: {
							sessionId: sessionId,
							projectId: Projectid,
							askInfo: content
						},
                        type: "POST",
                        url: "/addTouristAsk",
                        success: function (data) {
                            if (data.status == 1) {
                                $("#iNeedAsk").hide();
                                $("#askSuccess").show();
                            } else {
                            	alert(22)
                                alert(data.msg);
                            }
                        }
                    });
                }
            }
            $(".yyDk_close").click(function () {
				$(".yydk_heibu").hide()
			})
			//浮窗
			window.onscroll = function() {
				var s = $(this).scrollTop();
				if (s >= 256) {
					$(".headFixed").addClass("block");
				} else {
					$(".headFixed").removeClass("block");
				}
			}
		</script>
	</div>
	</form>
</html>