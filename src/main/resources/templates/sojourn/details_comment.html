<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <title th:text="${brief.projectName+'_'+brief.projectName+'点评_'+brief.projectName+'用户点评 - 房小二网'}"></title>
    <meta name="keywords"
          th:content="${brief.projectName+','+brief.projectName+'用户点评,'+brief.projectName+'点评,'+brief.projectName+'口碑'}">
    <meta name="description"
          th:content="${'房小二网为您提供'+brief.projectName+'最新业主点评，真实的'+brief.projectName+'口碑，并且可以畅所欲言，表达您对'+brief.projectName+'的真实看法。'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css"/>
    <script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/comments.css"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191112">
    <!--<link rel="stylesheet" type="text/css" href="/css/main2017.css">-->
    <link rel="stylesheet" type="text/css"
          href="https://static.fangxiaoer.com/web/styles/new_sy/index/indexBB.css?v=20180522"/>
    <script type="text/javascript" src="/js/house/verify.js"></script>
    <script src="/js/house/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script th:inline="javascript">
        /*<![CDATA[*/
        var userName = [[${session.userName}]];
        var phoneNum = [[${session.phoneNum}]];
        var sessionId = [[${session.muser}]];
        var projectId = [[${brief.projectId}]];
        var projectName = [[${brief.projectName}]];
        /*]]>*/
    </script>
</head>
<style>
    .clearfix {
        clear: both;
        content: "";
        display: block;
        overflow: inherit;
    }
</style>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
<div th:include="house/detail/fragment_order::useTouristCode"></div>
<div th:include="house/detail/fragment_order::guideMessage"></div>
<div th:include="house/detail/fragment_login::login"></div>

<div class="content">
    <div class="headFixed" th:include="sojourn/fragment::floatmenu" th:with="firstNavIndex=5"></div>
    <p class="comLocation">您的位置：
        <a th:href="@{'/'}">沈阳房产网</a>>
        <a th:href="@{'/houses/'}">沈阳新房</a>>
        <a th:href="@{'/tourist.htm'}">旅居地产</a>>
        <a th:href="${'/tourist/'+brief.projectId+'.htm'}" th:text="${brief.projectName}"></a>
    </p>
    <div class="pro_name">
        <div id="qrcode">
            <div class="layer_wei">
                <h1>手机看房</h1>
                <h2>更方便</h2>
            </div>
        </div>
        <div class="box_sun">
            <p>
                <span th:text="${brief.projectName}"></span>
                <span th:if="${brief.minTemp ne null and brief.maxTemp ne null}" class="centigrade"
                      th:text="${brief.minTemp + '℃~' + brief.maxTemp + '℃'}"></span>
            </p>
            <div class="type_sun wait_1" th:text="${brief.projectStatusValue}"></div>
            <div th:block th:if="${brief.features ne null}" class="wait_2">
                <span th:each="feature ,i:${#strings.listSplit(brief.features,'/')}" th:if="${i.index lt 3}"
                      th:text="${feature}"></span>
            </div>
        </div>
        <span class="s_time" th:text="${brief.upTime + '更新'}"></span>
    </div>
    <th:block th:include="sojourn/fragment::touristmenu" th:with="firstNavIndex=5"></th:block>
    <div class="wuuiptyi">
        <div class="uuipj">
            <div class="housesLeft" th:unless="${comments ne null and #lists.size(comments) ne 0}">
                <div class="housesLeftNo">暂无用户点评，快点点评一下吧~~~</div>
                <a class="loginQASK " th:if="${#session?.getAttribute('sessionId') == null}" target="_blank"
                   data-toggle="modal" href="#login" id="CommentListAdd">我要评价</a>
                <a class="loginQASK " th:if="${#session?.getAttribute('sessionId') != null}" id="CommentListAdd"
                   th:href="${'/tourist/'+brief.projectId+'/pushcomment'}">我要点评</a>
            </div>
            <th:block th:if="${comments ne null and #lists.size(comments) ne 0}">
                <div class="synthesize">
                    <div class="synthesizeRight">
                        <span>分享您对楼盘的真实评价，给更多购房者参考吧</span>
                        <a th:if="${#session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal"
                           href="#login" id="CommentListAdd">我要评价</a>
                        <a th:if="${#session?.getAttribute('sessionId') != null}" id="CommentListAdd"
                           th:href="${'/tourist/'+brief.projectId+'/pushcomment'}">我要评价</a>
                    </div>
                </div>
                <div class="userSynthesize">
                    <ul>
                        <li class="li" th:each="one:${comments}">
                            <table cellspacing="0" cellpadding="0">
                                <tr>
                                    <td>
                                        <div class="contentRight">
                                            <div class="headerImg hover">
                                                <div class="headerImges"><img
                                                        th:src="${#strings.toString(one.isNiming) eq '1' ? 'https://static.fangxiaoer.com/m/static/images/house/noagent.png':(one.headPic eq null ? 'https://static.fangxiaoer.com/m/static/images/house/noagent.png':one.headPic)}">
                                                </div>
                                                <div class="ueserName"
                                                     th:if="${#strings.toString(one.isNiming) eq '1'}">匿名
                                                </div>
                                                <div class="ueserName"
                                                     th:unless="${#strings.toString(one.isNiming) eq '1'}"
                                                     th:text="${one.RealName}"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="contentLeft">
                                            <ul class="contentInfoMmore">
                                                <li>
                                                    <p th:text="${one.commentDesc}"></p>
                                                </li>
                                            </ul>
                                            <div class="contentInfoImg" th:if="${#lists.size(one.photos) ne 0}">
                                                <div th:each="photo:${one.photos}"><img th:src="${photo.imageUrl}"
                                                                                        alt=""/></div>
                                            </div>
                                            <div class="contentBottom">
                                                <div class="contentTime" th:text="${one.addTime}"></div>
                                                <div class="contentShow openAndClose open">查看全文</div>
                                                <!--<span class="bdsharebuttonbox" style="float: right;"><a href="javascript:void(0);" class="bds_more contentShare " th:onclick="'javascript:sharedetailclick(\'https://m.fangxiaoer.com/tourist/'+${brief.projectId} +'.htm\')'"          data-cmd="more">分享</a></span>-->
                                            </div>
                                        </div>

                                        <div class="bigImgShow" style="display: none;">
                                            <div class="showImg">
                                                <ul style="margin-left: 0px;" th:if="${#lists.size(one.photos) ne 0}">
                                                    <li th:each="photo:${one.photos}">
                                                        <img th:src="${photo.imageUrl}" onload="imgSize()">
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="close"><img
                                                    src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"></div>
                                            <div class="prev"></div>
                                            <div class="next"></div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <!--点击图片放大 -->

                        </li>
                    </ul>

                    <!--<script src="/js/share.js"></script>-->
                    <!--<script src="/js/personal_share.js"></script>-->
                    <div th:include="fragment/page :: page"></div>
                </div>


            </th:block>
        </div>
        <div class="right">
            <div class="salesOffice houseRight">
                <p>售楼处电话：</p>
                <span><b th:utext="${#strings.toString(brief.sortTel).replace('转','<b>转</b>')}"></b></span>
                <a class="houseRightBtn" onclick="showUsercode(2)" style="cursor:pointer;">免费通话</a>
            </div>
            <th:block th:if="${#lists.size(brief.newsInfo) ne 0}">
                <div class="houseRight subscription">
                    <div class="houseRinghtTitle">旅居锦囊</div>
                    <ul>
                        <li th:each="one:${brief.newsInfo}">
                            <a th:href="${'/tNews/'+one.newsId+'.htm'}">
                                <div th:class="${'lj_tit labels'+one.newsType}" th:text="${one.newsTypeValue}"></div>
                                <b th:text="${one.title}"></b>
                                <p th:text="${one.newsSummary}"></p>
                            </a>
                        </li>
                    </ul>
                </div>
            </th:block>
        </div>
    </div>
</div>
</div>
<script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>

<script type="text/javascript">
    photo.init();
    $(function () {
        $('#qrcode').qrcode(
            {
                width: 60,
                height: 60,
                text: "https://m.fangxiaoer.com/tourist/" + [[${brief.projectId}]] + ".htm"

            });
        $('#qrcode2').qrcode(
            {
                width: 60,
                height: 60,
                text: "https://m.fangxiaoer.com/tourist/" + [[${brief.projectId}]] + ".htm"

            });
    });
    $(function () {
        var f = false;
        $(".open").click(function () {
            if (f = !f) {
                $(this).html("收起全文");
                $(this).removeClass('open');
                $(this).parent().prevAll().css("height", "auto");
                $(this).parent().prevAll().find("li").removeClass('ov');
            } else {
                $(this).html("查看全文");
                $(this).addClass("open");
                $(this).parent().prevAll().find("li").addClass('ov');
            }
        })
        if ($(".contentInfoMmore li").height() > 90) {
            //		$(".contentInfoMmore").css("height","90px");
        } else {
            $(".contentInfoMmore").css("height", "auto");
        }
        var page = $(".userSynthesize .li").length;
        for (var i = 0; i < page; i++) {
            var text = $(".contentInfoMmore li").eq(i).height();
            if (text > 90) {
                $('.contentInfoMmore li').eq(i).addClass('ov');
                $(".openAndClose").eq(i).show()
            } else {
                $('.contentInfoMmore li').eq(i).removeClass('ov');
                $(".openAndClose").eq(i).hide()
            }
        }
    })

    //浮窗
    window.onscroll = function () {
        var s = $(this).scrollTop();
        if (s >= 256) {
            $(".headFixed").addClass("block");
        } else {
            $(".headFixed").removeClass("block");
        }
    }

    $(".contentInfoImg  img").click(function () {
        // alert(111)
        photo.maxIndex = $(this).parent().parent().parent().parent().find(".bigImgShow li").length
        photo.ind = $(this).parent().index()
        photo.showInd = photo.ind
        $(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
        console.log(photo.maxIndex)
        console.log(photo.ind)
        $(this).parent().parent().parent().parent().find(".bigImgShow").show()
    })


</script>
<div class="cl" style="height: 25px;"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
</html>