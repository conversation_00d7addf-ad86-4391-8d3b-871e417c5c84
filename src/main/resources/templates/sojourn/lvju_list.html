<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="utf-8" />
		<title>旅居置业 - 房小二网</title>
		<meta name="keywords" th:content="${'旅居地产,异地买房,旅居投资,沈阳地产投资'}"/>
		<meta name="Description" th:content="${'房小二网旅居地产为您提供全国各地潜力地产，旅居度假，异地投资，候鸟一族，生活·旅行·家——旅居全球，美好共享'}"/>
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css" />
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/indexDes.css" />

	</head>

	<body>
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
		<div class="banner">
			<!--<img src="img/index_banner.png" />-->
		</div>
		<div class="content">
			<p class="comLocation">您的位置：
				<a th:href="@{'/'}">沈阳房产网></a>
				<a th:href="@{'/houses/'}">沈阳新房></a>
				<a th:href="@{'/tourist.htm'}">旅居地产></a>
				<a href="#">旅居置业</a>
			</p>
			<div class="right">
				<div class="aircraft">
					<div class="ai">
						<h1>预约看房</h1>
						<p>已有<span id="guideCount"></span>人预约</p>
					</div>
				</div>
				<div class="main_window">

					<div class="inEWM">
						<div class="loading loading-orange">
							<ul>
								<li><img src="https://static.fangxiaoer.com/web/images/lvjuImg/dm3.png"></li>
								<li><img src="https://static.fangxiaoer.com/web/images/lvjuImg/dm3.png"></li>
							</ul>
						</div>
<!--						<div class="cloud" data-speed="1" style="top: 30px;font-size: 16px;color: #272727;">这里云集</div>-->
<!--						<div class="cloud" style="top: 60px;" data-speed="2" ><img src="/lvjuImg/inEWM1.png"/></div>-->
<!--						<div class="cloud" data-speed="3" style="top: 90px;font-size: 18px;color: #272727;">吃喝玩乐短视频</div>-->
<!--						<div class="cloud"  style="top: 150px;" data-speed="4"><span style="float: left; margin-right: 10px; margin-top: 9px;">APP端</span><img src="/lvjuImg/inEWM2.png"/></div>-->
<!--						<div class="cloud" data-speed="5" style="top: 190px;font-size: 14px;color: #272727;">栏目火热开启让你一秒钟身临其境</div>-->
						<div class="cloud" style="top: 240px; text-align: center;width: 200px">
							<img src="https://static.fangxiaoer.com/web/images/lvjuImg/EVM.png"/>
						</div>
					</div>
				</div>
			</div>



			<!--</div>-->

			<div class="estate">
				<div class="estateDiv" th:each="projectItem:${projectList}">
                    <div class="estateOver">
                        <img class="estateimg" th:src="${#strings.isEmpty(projectItem.imageUrl) ? '':projectItem.imageUrl}" />
                    </div>
					<a th:href="${'/tourist/'+projectItem.projectId+'.htm'}" target="_blank">


					<div class="estateR">
						<div class="estateCon">
							<div class="inlocation">
								<img class="coordinatesimg" src="https://static.fangxiaoer.com/web/images/lvjuImg/coordinates.png" alt="" />
								<span th:text="${#strings.isEmpty(projectItem.cityName) ? '':projectItem.cityName}"></span>
							</div>
							<div class="info">
								<h1 th:text="${#strings.isEmpty(projectItem.projectName) ? '':projectItem.projectName}">碧桂园.波尔多庄园</h1>
								<div class="inestate">
									<span th:each="feature ,i:${#strings.listSplit(projectItem.featureValue,'/')}" th:if="${i.index lt 3}" th:text="${feature}">景点地产</span>
								</div>
							</div>
							<div class="inhouseholder clearfix">
								<div class="inhouse">
									<span th:if="${projectItem.price ne null}">
										<b th:text="${projectItem.price.price}"></b>元/㎡
										<i th:text="${#strings.toString(projectItem.price.priceType).substring(0,1)}"></i>
									</span>
									<span th:if="${projectItem.price eq null}"><b class="undetermined">待定</b></span>
								</div>
								<div class="inholder">
									<span
											th:if="${projectItem.area ne null and !#strings.equals(projectItem.area.minArea,projectItem.area.maxArea)}"
											th:text="${#strings.toString(projectItem.area.minArea).substring(0,#strings.indexOf(projectItem.area.minArea,'.'))+'~'+#strings.toString(projectItem.area.maxArea).substring(0,#strings.indexOf(projectItem.area.maxArea,'.'))+'㎡'}"></span>
									<span
											th:if="${projectItem.area ne null and #strings.equals(projectItem.area.minArea,projectItem.area.maxArea)}"
											th:text="${#strings.toString(projectItem.area.minArea).substring(0,#strings.indexOf(projectItem.area.minArea,'.'))+'㎡'}"></span>
									<span>
                                                <th:block th:each="room,i:${projectItem.room}" th:if="${room.room ne null}"
														  th:text="${ i.index lt #lists.size(projectItem.room)-1? room.room+'居/':room.room+'居' }"></th:block>
									</span>
								</div>
							</div>
							<p class="indetails" th:text="${#strings.isEmpty(projectItem.pushReason) ? '' : projectItem.pushReason}">法式风格红酒主题度假庄园，邂逅异域风情的浪漫</p>
							<div class="innecessary" th:if="${projectItem.news ne null}">
								<a target="_blank" th:href="${'/tNews/'+projectItem.news.newsId+'.htm'}">
									<span th:class=" ${'jinnangdisB labels' +projectItem.news.newsType}" th:text="${projectItem.news.newsTypeName}"></span> <span th:text="${#strings.isEmpty(projectItem.news.title) ? '' : projectItem.news.title}">碧桂园。波尔多庄园</span>
								</a>
							</div>
						</div>

					</div>
					</a>
				</div>
				<div th:include="fragment/page :: page"></div>
			</div>
		</div>
	<div class="cl"></div>
	<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
	<div th:include="fragment/fragment::tongji"></div>

	<!--	预约弹窗-->
	<div class="formDivbg" style="display: none">
		<div class="formDiv" >
			<img src="https://static.fangxiaoer.com/web/images/lvjuImg/formTrue.png" alt="">
			<h2>提交成功</h2>
			<p>您已预约成功，稍后会有工作人员与您联系</p>
		</div>
	</div>
	<div class="tcbg">
		<div class="text_box">
			<div class="content_box">
				<img class="yuyueC" src="https://static.fangxiaoer.com/web/images/lvjuImg/delClo.png" alt="">
				<p class="yuyueP">预约看房</p>
				<div class="properties">
					<h1 th:if="${project eq null}">请选择楼盘</h1>
					<h1 th:if="${project ne null}" th:text="${project.projectName}" style="color: rgb(0, 0, 0);"></h1>
					<img class="delxl" src="https://static.fangxiaoer.com/web/images/lvjuImg/delXl.png" alt="">
				</div>
				<div class="name">
					<input type="text" placeholder="请输入姓名">
				</div>
				<input type="hidden" class="selectCity" value="">
				<input type="hidden" class="selectProjectId" value="">
				<div class="phone">
					<input type="tel" placeholder="请输入手机号" onkeyup="if(value.length>11)value=value.slice(0,11)" onafterpaste="if(value.length>6)value=value.slice(0,6)">
				</div>
				<div class="Verification">
					<input type="tel" placeholder="请输入验证码" onkeyup="if(value.length>6)value=value.slice(0,6)" onafterpaste="if(value.length>6)value=value.slice(0,6)">
					<button>获取验证码</button>
					<div class="clearfix2"></div>
				</div>
				<div class="checkagreeInput" style="margin:10px auto 0 auto;width: 396px;">
					<i id="checkagree1" class="checkimg checked cheimg977"></i>
					<div style="font-size:12px;">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol"
														   target="_blank">《房小二网用户服务协议》</a>及
						<a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
				</div>
				<div class="btn">
					<button>提交</button>
				</div>
			</div>
		</div>
		<div class="cityBra">
			<div class="m_top m_nav">
				<a class="return" onclick="closeCity()"></a>
				<a class="catalog"></a>
				<div class="news_icon"></div>
			</div>
			<div class="list_box">
				<ul class="scoUL">
					<li data-id="1" onclick="cityClick(this)"><h1>辽阳市</h1></li>
					<li data-id="1" onclick="cityClick(this)"><h1>辽阳市</h1></li><li data-id="3" onclick="cityClick(this)"><h1>马来西亚</h1></li><li data-id="4" onclick="cityClick(this)"><h1>墨尔本</h1></li><li data-id="5" onclick="cityClick(this)"><h1>本溪市</h1></li>
				</ul>
			</div>
		</div>
		<div class="properties_box" >
			<div class="list">
				<!--				<ul><li data-id="3" onclick="projectClick(this)"><div class="photo"><div class="layer_wei"></div></div><div class="text"><div class="name"><h1>佳兆业•汤泉驿</h1></div><div class="price"><h1>均价<span>4199</span>元/㎡</h1></div></div><div class="Choice_sun"></div></li><div class="clearfix"></div></ul>-->
				<ul class="scoUL">

				</ul>
			</div>

		</div>


	</div>
	</body>
	<script>
		$(".cheimg977").click(function () {
			if($(this).hasClass("checked")){
				$(this).removeClass("checked")
			}else{
				$(this).addClass("checked")
			}
		})
		//    预约
		function f() {
			var sco_Box = $(".list_box ul").height();
			console.log(sco_Box)
			if(sco_Box>184){
				// $(".city").css("overflow-y","scroll")
				// $(".city").css("heigth","184")
				$(".cityBra").addClass("cityScoll")


			}
			var scoBox = $(".list ul").height();
			if(scoBox>184){
				$(".properties_box").addClass("cityScoll")
				// $(".properties_box").css("heigth","184px")

			}
		}
		$(".text_box .content_box .properties").click( function () {
					if ($(".cityBra").hasClass("block")) {
						$(".cityBra").removeClass("block")
					}else{
						$(".cityBra").addClass("block")
					}
					$(".properties_box").removeClass("block")
					$(".cityBra .list_box ul").html("");
					$.ajax({
						url: '/getTouristFilter',
						data: {},
						type: 'post',
						success: function (data) {
							console.log(data)
							if (data.status == 1) {
								data = data.content
								for (i = 0; i < data.length; i++) {
									var li = $("<li data-id='" + data[i].id + "' onclick='cityClick(" + "this" + ")'><h1 >" + data[i].name + "</h1></li>")
									$(".cityBra .list_box ul").append(li);
								}
							}
							f();
						}

					})



				}
		)

		function cityClick(e) {
			// $(".city .list_box ul li").removeClass("color")
			// $(e).addClass("color")
			var nowCityId = $(e).attr("data-id")
			var cityName = $(e).find("h1").html()
			$(".properties h1").html(cityName)
			var cityId = $(".selectCity").val()
			//加载城市
			if (nowCityId == cityId) {
			} else {
				$(".selectCity").val(nowCityId)
				$(".properties_box .list ul").empty()
				$.ajax({
					url: '/orderProject',
					data: {cityName: cityName},
					type: 'post',
					success: function (data) {
						if (data.status == 1) {
							data = data.content
							for (i = 0; i < data.length; i++) {
								var price
								if (data[i].price == null) {
									price = "<h1>待定</h1>"
								} else {
									price = "<h1>" + data[i].price.priceType + "<span>" + data[i].price.price + "</span>元/㎡</h1>"
								}
								var img = "/images/sojourn/pic.png"
								if (data[i].imageUrl != null) {
									img = data[i].imageUrl
								}
								var li = $("<li data-id='" + data[i].projectId + "' onclick='projectClick(" + "this" + ")'>" +
										"<h1>" + data[i].projectName + "</h1></li>")
								$(".properties_box .list ul").append(li);
							}
							$(".properties_box .list ul").append($("<div class='clearfix'></div>"))
						}
					}
				})

			}
			$(".cityBra").removeClass("block")
			$(".properties_box").addClass("block")
			$(".properties_box .btn").addClass("block")
		}
		function projectClick(e) {
			var proName = $(e).find("h1").html()
			$(".properties h1").html(proName)
			var selectProjectId = $(e).attr("data-id")
			$(".selectProjectId").val(selectProjectId)
			$(".properties_box").removeClass("block");
			// if ($(e).hasClass("color")) {
			// 	$(e).removeClass("color")
			// 	$(e).find(".layer_wei").hide()
			// } else {
			// 	$(".properties_box .list ul li").removeClass("color")
			// 	$(".properties_box .list ul li").find(".layer_wei").hide()
			// 	$(e).addClass("color")
			// 	$(e).find(".layer_wei").show()
			// 	pname = $(e).find(" .text .name h1").text()
			// 	var selectProjectId = $(e).attr("data-id")
			// 	$(".selectProjectId").val(selectProjectId)
			// }
		}

		var wtime;

		$(".text_box .content_box .Verification button").click(
				function () {
					var phone = $(".text_box .content_box .phone input").val()
					if(phone==""){
						alert("请输入您的电话")
						return;
					}else{
						if (!reg.test(phone)) {
							alert("手机号格式不正确")
							return;
						}else{
							$.ajax({
								type: "POST",
								data: {mobile: phone},
								url: "/sendSmsCode",
								success: function (result) {
									if (result.status == 0) {
										confirm.message("操作过于频繁,请稍候再获取验证码");
									}
									Time(60)
								}
							})
						}
					}
				}
		)
		function Time(o) {
			clearInterval(wtime)
			wtime = setInterval(
					function () {
						if (o > 0) {
							o--
							$(".text_box .content_box .Verification  button").css("color", "#333")
							$(".text_box .content_box .Verification  button").text(o + "秒")
							$(".text_box .content_box .Verification  button").attr('disabled', true)

							if (o == 0) {
								$(".text_box .content_box .Verification  button").attr('disabled', false)
								clearInterval(wtime)
								$(".text_box .content_box .Verification  button").css("color", "#ff5200")
								$(".text_box .content_box .Verification  button").text("重新获取")

							}
						}
					}, 1000
			)

		}
		var h1conent = $(".properties h1").html();
		$(".formDivbg").click(function(event){
			$(this).hide()
		});
		// 关闭
		$(".yuyueC").click(function () {
			$(".tcbg").hide();
			$(".properties h1").html(h1conent)
			$(".cityBra,.properties_box ").removeClass("block")
			$(".content_box input").val("")
		})
		$(".aircraft").click(function () {
			$(".tcbg,.content_box").show();
			$(".formDivbg").hide()

		})

		var reg = /^1[0-9]{10}$/;
		var value
		$(".text_box .content_box .btn button").click(
				function () {
					var name = $(".text_box .content_box .name input").val()
					var phone = $(".text_box .content_box .phone input").val()
					var Verification = $(".text_box .content_box .Verification input").val()
					var properties = $(".text_box .content_box .properties h1").text()
					if (properties == "请选择楼盘") {
						alert("请选择楼盘")
						return;
					}
					if(name==""){
						alert("请输入您的姓名")
						return;
					}

					if(phone==""){
						alert("请输入您的电话")
						return;
					}else{
						if (!reg.test(phone)) {
							alert("手机号格式不正确")
							return;
						}
					}
					if (Verification == "") {
						alert("请输入验证码")
						return;
					}
					if ($(".cheimg977").hasClass("checked") == false){
						alert("请仔细阅读并同意服务协议及隐私政策。")
						return;
					}
					$.ajax({
						url: '/addTouristOrder',
						type: "post",
						data: {
							// sessionId: sessionId,
							phone: phone,
							code: Verification,
							projectId: $(".selectProjectId").val(),
							projectName: properties,
							memberName: name
						},
						success: function (value) {
							if (value.status == 1) {
								$(".formDivbg").show();
								$(".tcbg").hide();
								$(".properties h1").html(h1conent)
								$(".cityBra,.properties_box ").removeClass("block")
								$(".content_box input").val("")
								setTimeout(function () {
									$(".formDivbg").hide()
								},3000)

							} else {
								alert(value.msg)
								setInterval(function () {
									window.history.go(-1)
								}, 3000);
							}
						}
					});
				}
		)

		$.ajax({
            type: "POST",
            data: {},
            url: "/touristGuideCount",
            success: function (data) {
                if (data.status == 1) {
                    $("#guideCount").text(data.content)
                }
            }
        })

        window.onscroll = function() {
            var s = $(this).scrollTop();
            if (s >= 508) {
                $(".main_window").addClass("lvListPos");
            } else {
                $(".main_window").removeClass("lvListPos");
            }
        }




	</script>
</html>