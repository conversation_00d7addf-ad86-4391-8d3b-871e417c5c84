<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<th:block th:fragment="floatmenu">
    <div class="detHeader">
            <div id="qrcode2">
                <div class="layer_wei">
                    <h1>手机看房</h1>
                    <h2>更方便</h2>
                </div>
            </div>
            <div class="qrdRight">
                <div class="qrdRightD">
                    <h1 class="H1"  th:text="${brief.projectName}"></h1>
                    <i th:class="${'zai'+brief.projectStatus}" th:text="${brief.projectStatusValue}">在售</i>
                </div>
                <p class="headP">售楼处咨询电话：<span th:utext="${#strings.toString(brief.sortTel).replace('转','<b>转</b>')}"></span><input name="" type="button" onclick="showUsercode(2)" value="免费通话" class=" btn_1"></p>
                <div th:if="${#lists.size(brief.prices) ne 0}" class="deCategory">
                    <span class="headC">参考价格：</span>
                    <ul class="deCategoryUl1">
                        <li th:each="price ,i:${brief.prices}" th:if="${i.index lt 3}">
						<span th:if="${#strings.toString(price.price) ne '0'}"><th:block th:text="${price.buildTypeName}"></th:block>
						<b th:text="${price.price}"></b>元/㎡
						<i th:text="${#strings.toString(price.priceType) eq '起价' ?'起':'均'}"></i>
						</span>
                            <span th:if="${#strings.toString(price.price) eq '0'}">
							<th:block th:text="${price.buildTypeName}"></th:block><b >待定</b>
						</span>
                        </li>
                        <li class="delimorePic" th:if="${#lists.size(brief.prices) gt 3}">
                            <span class="deCategorySpan">更多价格</span>
                            <div class="deCategoryDiv">
                                <span class="deCategoryDivSpan">
                                    							<img src="https://static.fangxiaoer.com/web/images/lvjuImg/jianjian.png" alt="">

                                </span>

                                <ul class="deCategoryUl2" >
                                    <li th:each="price ,i:${brief.prices}" th:if="${i.count gt 3}">
                                        <th:block th:text="${price.buildTypeName}"></th:block>
                                        <span th:if="${#strings.toString(price.price) ne '0'}">
									<b th:text="${price.price}"></b>元/㎡
									<i th:text="${#strings.toString(price.priceType) eq '起价' ?'起':'均'}"></i></span>
                                        <span th:if="${#strings.toString(price.price) eq '0'}"><th:block th:text="${price.buildTypeName}"></th:block><b>待定</b></span>
                                    </li>
                                </ul>
                            </div>
                        </li>

                    </ul>
                </div>
            </div>
        </div>
    <div class="comtittleDiv">
        <div class="comtittle comtittle2">
            <ul>
                <li><a th:href="${'/tourist/'+brief.projectId+'.htm'}"><span th:class=" (${firstNavIndex} == 1)? 'deselect' : '' ">楼盘首页</span></a></li>
                <li><a th:href="${'/tourist/'+brief.projectId+'/info.htm'}"><span th:class=" (${firstNavIndex} == 2)? 'deselect' : '' ">楼盘详情</span></a></li>
                <li th:if="${brief.layoutCount ne '0'}"><a th:href="${'/tourist/'+brief.projectId + '/layout.htm'}"><span th:class=" (${firstNavIndex} == 3)? 'deselect' : '' ">户型</span></a></li>
                <li><a th:href="${'/tourist/'+brief.projectId+'/album.htm'}"><span th:class=" (${firstNavIndex} == 4)? 'deselect' : '' ">相册</span></a></li>
                <li><a th:href="${'/tourist/'+brief.projectId+'/comment.htm'}"><span th:class=" (${firstNavIndex} == 5)? 'deselect' : '' ">用户点评</span></a></li>
                <li><a th:href="${'/tourist/'+brief.projectId+'/ask.htm'}"><span th:class=" (${firstNavIndex} == 6)? 'deselect' : '' ">楼盘问答</span></a></li>
                <li><a th:href="${'/tourist/'+brief.projectId+'/dy.htm'}"><span th:class=" (${firstNavIndex} == 7)? 'deselect' : '' ">楼盘动态</span></a></li>
            </ul>
        </div>
    </div>
</th:block>
<th:block th:fragment="touristmenu">
    <div class="comtittle">
        <ul>
            <li><a th:href="${'/tourist/'+brief.projectId+'.htm'}"><span th:class=" (${firstNavIndex} == 1)? 'deselect' : '' ">楼盘首页</span></a></li>
            <li><a th:href="${'/tourist/'+brief.projectId+'/info.htm'}"><span th:class=" (${firstNavIndex} == 2)? 'deselect' : '' ">楼盘详情</span></a></li>
            <li th:if="${brief.layoutCount ne '0'}"><a th:href="${'/tourist/'+brief.projectId + '/layout.htm'}"><span th:class=" (${firstNavIndex} == 3)? 'deselect' : '' ">户型</span></a></li>
            <li><a th:href="${'/tourist/'+brief.projectId+'/album.htm'}"><span th:class=" (${firstNavIndex} == 4)? 'deselect' : '' ">相册</span></a></li>
            <li><a th:href="${'/tourist/'+brief.projectId+'/comment.htm'}"><span th:class=" (${firstNavIndex} == 5)? 'deselect' : '' ">用户点评</span></a></li>
            <li><a th:href="${'/tourist/'+brief.projectId+'/ask.htm'}"><span th:class=" (${firstNavIndex} == 6)? 'deselect' : '' ">楼盘问答</span></a></li>
            <li><a th:href="${'/tourist/'+brief.projectId+'/dy.htm'}"><span th:class=" (${firstNavIndex} == 7)? 'deselect' : '' ">楼盘动态</span></a></li>
        </ul>
    </div>
</th:block>
</body>
</html>