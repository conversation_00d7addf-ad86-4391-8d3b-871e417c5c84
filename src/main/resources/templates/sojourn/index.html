<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
	<head>
		<meta charset="utf-8" />
		<title>旅居地产 - 房小二网</title>
		<meta name="keywords" th:content="${'旅居地产,异地买房,旅居投资,沈阳地产投资'}"/>
		<meta name="Description" th:content="${'房小二网旅居地产为您提供全国各地潜力地产，旅居度假，异地投资，候鸟一族，生活·旅行·家——旅居全球，美好共享'}"/>
		<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
		<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css?v=20190920" />
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css?v=20190920" />
		<script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<!--<link rel="stylesheet" type="text/css" href="css/style.css"/>-->
		<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/index.css?v=20190921" />
<!--		<link rel="stylesheet" type="text/css" href="/css/lvju/index.css?v=20190920" />-->
	</head>
	<style>

	</style>
	<body>

	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=6"></div>

		<div class="banner">
			<img src="https://static.fangxiaoer.com/web/images/lvjuImg/index_banner.png" />
		</div>
		<div class="content">
			<img class="aircraft2" src="https://static.fangxiaoer.com/web/images/lvjuImg/air.png" alt="">

			<p class="comLocation">您的位置：
				<a th:href="@{'/'}">沈阳房产网</a>>
				<a th:href="@{'/houses/'}">沈阳新房</a>>
				<a th:href="@{'/tourist.htm'}">旅居地产</a>
			</p>

			<div class="inSelection" th:if="${!#lists.isEmpty(ad11)}">
				<h1 class="H1">旅居甄选</h1>
				<div>
					<div class="">
						<img class="geng" src="https://static.fangxiaoer.com/web/images/lvjuImg/geng.png" />
						<div class="inSelSwi">
							<div class="swiper-container swiper-container-initialized swiper-container-horizontal">
								<div class="swiper-wrapper" style="transition-duration: 0ms; transform: translate3d(-9750px, 0px, 0px);">
									<div class="swiper-slide" th:each="aditem:${ad11}">
										<a th:href="${aditem.adviseUrl}" target="_blank">
										<div class="activity-photos-bg">
											<img th:src="${aditem.imageUrl}">
											<div class="node">
												<span class="node_1" th:text="${aditem.adviseTitle}"></span>
												<span class="node_2" th:text="${aditem.adviseSubTitle}"></span>
											</div>
											<div class="bgclo"></div>
										</div>
										</a>
									</div>
								</div>
								<div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide"></div>
								<div class="swiper-button-prev" tabindex="0" role="button" aria-label="Previous slide"></div>
								<span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>
						</div>
					</div>

					<div class="main_window">
						<div class="loading loading-orange-right">
							<ul>
								<li><img src="https://static.fangxiaoer.com/web/images/lvjuImg/danm.png" alt=""></li>
								<li><img src="https://static.fangxiaoer.com/web/images/lvjuImg/danm.png" alt=""></li>
							</ul>
						</div>
<!--						<img src="/lvjuImg/fudong.png" alt="">-->
						<div class="inEWM">
<!--							<div class="cloud" data-speed="1" style="top: 30px;font-size: 16px;color: #272727;">这里云集</div>-->
<!--							<div class="cloud" style="top: 60px;" data-speed="2" ><img src="/lvjuImg/inEWM1.png"/><img src="/lvjuImg/inEWM1.png"/></div>-->
<!--							<div class="cloud" data-speed="3" style="top: 90px;font-size: 18px;color: #272727;">吃喝玩乐短视频</div>-->
<!--							<div class="cloud"  style="top: 150px;" data-speed="4"><span style="float: left; margin-right: 10px; margin-top: 9px;">APP端</span><img src="/lvjuImg/inEWM2.png"/></div>-->
<!--							<div class="cloud" data-speed="5" style="top: 190px;font-size: 14px;color: #272727;">栏目火热开启让你一秒钟身临其境</div>-->
							<div class="cloud" style="top: 268px; text-align: center;">
								<img src="https://static.fangxiaoer.com/web/images/lvjuImg/EVM.png"/>
							</div>
						</div>
					</div>
				</div>

			</div>

			<div class="estate">
				<div>
					<h1  class="H1">旅居置业</h1>
					<a target="_blank" th:href="@{'/touristList'}"><span class="h1span">查看更多》</span></ a>
				</div>
				<img class="gengg" src="https://static.fangxiaoer.com/web/images/lvjuImg/gengg.png" />
				<div class="estateDiv" th:each="projectItem:${projectList}">
					<a target="_blank" th:href="${'/tourist/'+projectItem.projectId+'.htm'}">
						<div class="estateDivOver">
							<img class="estateimg" th:src="${#strings.isEmpty(projectItem.imageUrl) ? '':projectItem.imageUrl}" />
						</div>
					<div class="estateR">
						<div class="estateCon">
							<div class="inlocation">
								<img class="coordinatesimg" src="https://static.fangxiaoer.com/web/images/lvjuImg/coordinates.png" alt="" />
								<span th:text="${#strings.isEmpty(projectItem.cityName) ? '':projectItem.cityName}">大连</span>
							</div>
							<div class="info">
								<p th:text="${#strings.isEmpty(projectItem.projectName) ? '':projectItem.projectName}">碧桂园.波尔多庄园</p>
								<div class="inestate">
									<span th:each="feature ,i:${#strings.listSplit(projectItem.featureValue,'/')}" th:if="${i.index lt 3}" th:text="${feature}">景点地产</span>
								</div>
							</div>
							<div class="inhouseholder clearfix">
								<div class="inhouse">
									<span th:if="${projectItem.price ne null}">
										<b th:text="${projectItem.price.price}"></b>元/㎡
										<i th:text="${#strings.toString(projectItem.price.priceType).substring(0,1)}"></i>
									</span>
									<span th:if="${projectItem.price eq null}"><b class="undetermined">待定</b></span>
								</div>
								<div class="inholder">
									<span
											th:if="${projectItem.area ne null and !#strings.equals(projectItem.area.minArea,projectItem.area.maxArea)}"
											th:text="${#strings.toString(projectItem.area.minArea).substring(0,#strings.indexOf(projectItem.area.minArea,'.'))+'~'+#strings.toString(projectItem.area.maxArea).substring(0,#strings.indexOf(projectItem.area.maxArea,'.'))+'㎡'}"></span>
									<span
											th:if="${projectItem.area ne null and #strings.equals(projectItem.area.minArea,projectItem.area.maxArea)}"
											th:text="${#strings.toString(projectItem.area.minArea).substring(0,#strings.indexOf(projectItem.area.minArea,'.'))+'㎡'}"></span>
									<span>
                                                <th:block th:each="room,i:${projectItem.room}" th:if="${room.room ne null}"
														  th:text="${ i.index lt #lists.size(projectItem.room)-1? room.room+'居/':room.room+'居' }"></th:block>
									</span>
								</div>
							</div>
							<p class="indetails" th:text="${#strings.isEmpty(projectItem.pushReason) ? '' : projectItem.pushReason}">法式风格红酒主题度假庄园，邂逅异域风情的浪漫</p>
							<div class="innecessary" th:if="${projectItem.news ne null}">
								<a target="_blank" th:href="${'/tNews/'+projectItem.news.newsId+'.htm'}">
									<span th:class="${'jinnangdisB labels' +projectItem.news.newsType}" th:text="${projectItem.news.newsTypeName}"></span> <span th:text="${#strings.isEmpty(projectItem.news.title) ? '' : projectItem.news.title}">碧桂园。波尔多庄园</span>
								</a>
							</div>
						</div>

					</div>
					</a>
				</div>
			</div>
			<div class="inkits">
				<div id="">
					<h1  class="H1">旅居锦囊</h1>
					<a target="_blank" th:href="@{'/tNews.htm'}"><span class="h1span">查看更多》</span></a>
				</div>
				<img class="gengg" src="https://static.fangxiaoer.com/web/images/lvjuImg/gengg.png" />
				<ul>
					<li th:each="newsitem:${newsList}">
						<a th:href="${#strings.isEmpty(newsitem.newsType) ? '#':'/tNews'+newsitem.newsType+'.htm'}" target="_blank">
						<img th:src="${#strings.isEmpty(newsitem.newsType) ? '':'https://static.fangxiaoer.com/web/images/lvjuImg/jinnang'+newsitem.newsType+'.png'}" alt="" />
						<div class="inkitsdiv">
								<h2 th:text="${#strings.isEmpty(newsitem.title) ? '':newsitem.title}">富力博士山，以温度为生活带来灵魂！</h2>
								<p th:text="${#strings.isEmpty(newsitem.newsSummary) ? '':newsitem.newsSummary}">说到澳洲，很多人首先会想到悉尼.</p>
						</div>
						</a>
					</li>
				</ul>
			</div>
		</div>
		<div th:include="fragment/fragment:: footer_seo"></div>
		<div class="disclaimer">
			<strong>免责声明：楼盘信息由开发商提供，最终以政府部门登记备案为准，请谨慎核查。如该楼盘信息有误，您可以投诉或拔打举报电话：400-893-9709。</strong>
		</div>
		<div class="cl"></div>
		<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
		<div th:include="fragment/fragment::tongji"></div>
	</body>
	<script type="text/javascript">
		$(function () {
			$("body").css("overflow-x","hidden")

		})
		var swiper = new Swiper('.swiper-container', {
			effect: 'coverflow',
			slidesPerView: 3,
			// spaceBetween: 1,
			// slidesPerGroup: 1,
			loop: true,
			coverflowEffect: {
				rotate: 0,
				stretch: 0,
				depth: 360,
				modifier: 1,
				slideShadows: true,
			},
			pagination: {
				el: '.swiper-pagination',
				clickable: true,
			},
			navigation: {
				nextEl: '.swiper-button-next',
				prevEl: '.swiper-button-prev',
			},
		});
	</script>

</html>