<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <title>旅居锦囊_旅居地产 - 房小二网</title>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
    <meta name="keywords" content="旅居投资指南,异地买房推荐,异地购房,异地看房"/>
    <meta name="Description" content="房小二网旅居锦囊为您提供全国各地潜力地产资讯，投资信息与价格波动提示，生活·旅行·家——旅居全球，美好共享"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/living_list.css"/>

</head>
<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
<div class="banner2" th:if="${!#lists.isEmpty(ad13) and #lists.toList(ad13).size() eq 1}"
     th:style="${'background-image: url('+ad13[0].imageUrl+')'}">
    <div class="guang">
        <p th:text="${#strings.isEmpty(ad13[0].adviseTitle) ? '':ad13[0].adviseTitle}"></p>

    </div>


</div>
<div class="banner" th:if="${!#lists.isEmpty(ad13)  and #lists.toList(ad13).size() ne 1}">
    <!-- Swiper -->
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <div class="swiper-slide" th:each="aditem:${ad13}">
                <a th:href="${aditem.adviseUrl}">
                    <img th:src="${#strings.isEmpty(aditem.imageUrl) ? '':aditem.imageUrl}"/>
                    <div class="guang guang2">
                        <p th:text="${#strings.isEmpty(ad13[0].adviseTitle) ? '':ad13[0].adviseTitle}"></p>
                    </div>
                </a>
            </div>

        </div>

        <!-- Add Pagination -->
        <div class="swiper-pagination"></div>
        <!-- Add Arrows -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>
</div>
<script>
    var swiper = new Swiper('.swiper-container', {
        loop: true,
        pagination: {
            el: '.swiper-pagination',
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        autoplay: {
            delay: 2500,
            disableOnInteraction: false,
        },
    });
</script>
<div class="content">
    <p class="comLocation">您的位置：
        <a th:href="@{'/'}">沈阳房产网</a>>
        <a th:href="@{'/houses/'}">沈阳新房</a>>
        <a th:href="@{'/tourist.htm'}">旅居地产></a>
        <a href="#">旅居锦囊</a>
    </p>
    <div class="w">
        <div class="left">
            <div class="left_tit">
                <li th:each="one:${newsFilter}"><a th:href="${'/tNews'+one.id+'.htm'}"
                                                   th:class="${#strings.toString(one.id) eq #strings.toString(categoryId) ? 'aa':''}"
                                                   th:text="${one.name}"></a></li>
            </div>
            <div class="left_list">
                <ul>
                    <li th:each="one:${news}">
                        <a th:href="${'/tNews/'+one.newsId+'.htm'}" target="_blank">
                            <h1><span th:text="${one.title}"></span></h1>
                            <div class="info">
                                <p>来源：<b th:text="${one.newsSource}"></b>&nbsp;|&nbsp;编辑：&nbsp;<b
                                        th:text="${one.authorName}"></b>&nbsp;|&nbsp;<b
                                        th:text="${one.wholeAddTime}"></b>

                                </p>
                                <span th:text="${one.visitTime}"></span>
                            </div>
                            <div class="inlocation">
                                <img th:src="${one.imageUrl ne null ?one.imageUrl:'https://static.fangxiaoer.com/m/images/sload.jpg' }"/>
                                <div class="indetails" th:text="${one.newsSummary}"></div>
                                <div class="inestate"><a th:href="${'/tNews/'+one.newsId+'.htm'}">全文>></a></div>
                            </div>
                        </a>
                    </li>
                </ul>
                <div th:include="fragment/page :: page"></div>
            </div>
        </div>

        <div class="right">
            <!--飞机-->
            <div class="aircraft">
                <div class="ai">
                    <h1>预约看房</h1>
                    <p>已有<span id="guideCount"></span>人预约</p>
                </div>
            </div>
            <div class="main_window">
                <div class="inEWM">
                    <div class="loading loading-orange">
                        <ul>
                            <li><img src="https://static.fangxiaoer.com/web/images/lvjuImg/dm3.png"></li>
                            <li><img src="https://static.fangxiaoer.com/web/images/lvjuImg/dm3.png"></li>
                        </ul>
                    </div>

                    <div class="cloud" style="top: 240px; text-align: center;">
                        <img src="https://static.fangxiaoer.com/web/images/lvjuImg/EVM.png"/>
                    </div>
                </div>
            </div>
            <div class="recommend" th:if="${!#lists.isEmpty(recommendAdProjects)}">
                <div class="recommend-title">
                    <div class="chengse"></div>
                    <div class="tuijian">好房推荐</div>
                </div>
                <div class="right_list">
                    <ul>
                        <li th:each="projectAd:${recommendAdProjects}">
                            <a th:href="${projectAd.TargetUrl}" target="_blank">
                                <div class="recommend-img">
                                    <img th:src="${projectAd.AdFilePath}"/>
                                    <p th:text="${#strings.isEmpty(projectAd.AdTitle)?'':projectAd.AdTitle}"></p>
                                </div>
                                <div class="unstyled">
                                    <th:block th:if="${#strings.toString(projectAd.Adproprice) eq '0'}">
                                        <b>待定</b>
                                    </th:block>
                                    <th:block th:unless="${#strings.toString(projectAd.Adproprice) eq '0'}">
                                        <th:block
                                                th:text="${#strings.toString(projectAd.Number) eq '2'?'均价：':'起价：'}"></th:block>
                                        <b th:text="${#strings.isEmpty(projectAd.Adproprice)?'':projectAd.Adproprice}"></b>元/㎡
                                    </th:block>
                                    <span th:text="${#strings.isEmpty(projectAd.Propertys)?'':projectAd.Propertys}"></span>
                                </div>
                                <div class="ts_desc"
                                     th:text="${#strings.isEmpty(projectAd.AdPhrase)?'':projectAd.AdPhrase}"></div>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!--	预约弹窗-->
    <div class="formDivbg" style="display: none">
        <div class="formDiv" >
            <img src="https://static.fangxiaoer.com/web/images/lvjuImg/formTrue.png" alt="">
            <h2>提交成功</h2>
            <p>您已预约成功，稍后会有工作人员与您联系</p>
        </div>
    </div>
    <div class="tcbg">
        <div class="text_box">
            <div class="content_box">
                <img class="yuyueC" src="https://static.fangxiaoer.com/web/images/lvjuImg/delClo.png" alt="">
                <p class="yuyueP">预约看房</p>
                <div class="properties">
                    <h1 th:if="${project eq null}">请选择楼盘</h1>
                    <h1 th:if="${project ne null}" th:text="${project.projectName}" style="color: rgb(0, 0, 0);"></h1>
                    <img class="delxl" src="https://static.fangxiaoer.com/web/images/lvjuImg/delXl.png" alt="">
                </div>
                <div class="name">
                    <input type="text" placeholder="请输入姓名">
                </div>
                <input type="hidden" class="selectCity" value="">
                <input type="hidden" class="selectProjectId" value="">
                <div class="phone">
                    <input type="tel" placeholder="请输入手机号" onkeyup="if(value.length>11)value=value.slice(0,11)"
                           onafterpaste="if(value.length>6)value=value.slice(0,6)">
                </div>
                <div class="Verification">
                    <input type="tel" placeholder="请输入验证码" onkeyup="if(value.length>6)value=value.slice(0,6)"
                           onafterpaste="if(value.length>6)value=value.slice(0,6)">
                    <button>获取验证码</button>
                    <div class="clearfix2"></div>
                </div>
                <div class="checkagreeInput" style="margin:10px auto 0 auto;width: 396px;">
                    <i id="checkagree1" class="checkimg checked cheimg978"></i>
                    <div style="font-size:12px;">我已阅读并同意<a href="https://info.fangxiaoer.com/About/protocol"
                                                           target="_blank">《房小二网用户服务协议》</a>及
                        <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                </div>
                <div class="btn">
                    <button>提交</button>
                </div>
            </div>
        </div>
        <div class="cityBra">
            <div class="m_top m_nav">
                <a class="return" onclick="closeCity()"></a>
                <a class="catalog"></a>
                <div class="news_icon"></div>
            </div>
            <div class="list_box">
                <ul class="scoUL">
                    <li data-id="1" onclick="cityClick(this)"><h1>辽阳市</h1></li>
                    <li data-id="1" onclick="cityClick(this)"><h1>辽阳市</h1></li>
                    <li data-id="3" onclick="cityClick(this)"><h1>马来西亚</h1></li>
                    <li data-id="4" onclick="cityClick(this)"><h1>墨尔本</h1></li>
                    <li data-id="5" onclick="cityClick(this)"><h1>本溪市</h1></li>
                </ul>
            </div>
        </div>
        <div class="properties_box">
            <div class="list">
                <!--				<ul><li data-id="3" onclick="projectClick(this)"><div class="photo"><div class="layer_wei"></div></div><div class="text"><div class="name"><h1>佳兆业•汤泉驿</h1></div><div class="price"><h1>均价<span>4199</span>元/㎡</h1></div></div><div class="Choice_sun"></div></li><div class="clearfix"></div></ul>-->
                <ul class="scoUL">

                </ul>
            </div>

        </div>
    </div>


</div>
<script>
    $(".cheimg978").click(function () {
        if($(this).hasClass("checked")){
            $(this).removeClass("checked")
        }else{
            $(this).addClass("checked")
        }
    })
    $.ajax({
        type: "POST",
        data: {},
        url: "/touristGuideCount",
        success: function (data) {
            if (data.status == 1) {
                $("#guideCount").text(data.content)
            }
        }
    })


    //    预约
    function f() {
        var sco_Box = $(".list_box ul").height();
        console.log(sco_Box)
        if(sco_Box>184){
            // $(".city").css("overflow-y","scroll")
            // $(".city").css("heigth","184")
            $(".cityBra").addClass("cityScoll")


        }
        var scoBox = $(".list ul").height();
        if(scoBox>184){
            $(".properties_box").addClass("cityScoll")
            // $(".properties_box").css("heigth","184px")

        }
    }
    $(".text_box .content_box .properties").click( function () {
            if ($(".cityBra").hasClass("block")) {
                $(".cityBra").removeClass("block")
            }else{
                $(".cityBra").addClass("block")
            }
            $(".properties_box").removeClass("block")
            $(".cityBra .list_box ul").html("");
            $.ajax({
                url: '/getTouristFilter',
                data: {},
                type: 'post',
                success: function (data) {
                    console.log(data)
                    if (data.status == 1) {
                        data = data.content
                        for (i = 0; i < data.length; i++) {
                            var li = $("<li data-id='" + data[i].id + "' onclick='cityClick(" + "this" + ")'><h1 >" + data[i].name + "</h1></li>")
                            $(".cityBra .list_box ul").append(li);
                        }
                    }
                    f();
                }

            })



        }
    )

    function cityClick(e) {
        // $(".city .list_box ul li").removeClass("color")
        // $(e).addClass("color")
        var nowCityId = $(e).attr("data-id")
        var cityName = $(e).find("h1").html()
        $(".properties h1").html(cityName)
        var cityId = $(".selectCity").val()
        //加载城市
        if (nowCityId == cityId) {
        } else {
            $(".selectCity").val(nowCityId)
            $(".properties_box .list ul").empty()
            $.ajax({
                url: '/orderProject',
                data: {cityName: cityName},
                type: 'post',
                success: function (data) {
                    if (data.status == 1) {
                        data = data.content
                        for (i = 0; i < data.length; i++) {
                            var price
                            if (data[i].price == null) {
                                price = "<h1>待定</h1>"
                            } else {
                                price = "<h1>" + data[i].price.priceType + "<span>" + data[i].price.price + "</span>元/㎡</h1>"
                            }
                            var img = "/images/sojourn/pic.png"
                            if (data[i].imageUrl != null) {
                                img = data[i].imageUrl
                            }
                            var li = $("<li data-id='" + data[i].projectId + "' onclick='projectClick(" + "this" + ")'>" +
                                "<h1>" + data[i].projectName + "</h1></li>")
                            $(".properties_box .list ul").append(li);
                        }
                        $(".properties_box .list ul").append($("<div class='clearfix'></div>"))
                    }
                }
            })

        }
        $(".cityBra").removeClass("block")
        $(".properties_box").addClass("block")
        $(".properties_box .btn").addClass("block")
    }
    function projectClick(e) {
        var proName = $(e).find("h1").html()
        $(".properties h1").html(proName)
        var selectProjectId = $(e).attr("data-id")
        $(".selectProjectId").val(selectProjectId)
        $(".properties_box").removeClass("block");
        // if ($(e).hasClass("color")) {
        // 	$(e).removeClass("color")
        // 	$(e).find(".layer_wei").hide()
        // } else {
        // 	$(".properties_box .list ul li").removeClass("color")
        // 	$(".properties_box .list ul li").find(".layer_wei").hide()
        // 	$(e).addClass("color")
        // 	$(e).find(".layer_wei").show()
        // 	pname = $(e).find(" .text .name h1").text()
        // 	var selectProjectId = $(e).attr("data-id")
        // 	$(".selectProjectId").val(selectProjectId)
        // }
    }

    var wtime;

    $(".text_box .content_box .Verification button").click(
        function () {
            var phone = $(".text_box .content_box .phone input").val()
            if(phone==""){
                alert("请输入您的电话")
                return;
            }else{
                if (!reg.test(phone)) {
                    alert("手机号格式不正确")
                    return;
                }else{
                    $.ajax({
                        type: "POST",
                        data: {mobile: phone},
                        url: "/sendSmsCode",
                        success: function (result) {
                            if (result.status == 0) {
                                confirm.message("操作过于频繁,请稍候再获取验证码");
                            }
                            Time(60)
                        }
                    })
                }
            }
        }
    )
    function Time(o) {
        clearInterval(wtime)
        wtime = setInterval(
            function () {
                if (o > 0) {
                    o--
                    $(".text_box .content_box .Verification  button").css("color", "#333")
                    $(".text_box .content_box .Verification  button").text(o + "秒")
                    $(".text_box .content_box .Verification  button").attr('disabled', true)

                    if (o == 0) {
                        $(".text_box .content_box .Verification  button").attr('disabled', false)
                        clearInterval(wtime)
                        $(".text_box .content_box .Verification  button").css("color", "#ff5200")
                        $(".text_box .content_box .Verification  button").text("重新获取")

                    }
                }
            }, 1000
        )

    }
    var h1conent = $(".properties h1").html();
    $(".formDivbg").click(function(event){
        $(this).hide()
    });
    // 关闭
    $(".yuyueC").click(function () {
        $(".tcbg").hide();
        $(".properties h1").html(h1conent)
        $(".cityBra,.properties_box ").removeClass("block")
        $(".content_box input").val("")
    })
    $(".aircraft").click(function () {
        $(".tcbg,.content_box").show();
        $(".formDivbg").hide()

    })

    var reg = /^1[0-9]{10}$/;
    var value
    $(".text_box .content_box .btn button").click(
        function () {
            var name = $(".text_box .content_box .name input").val()
            var phone = $(".text_box .content_box .phone input").val()
            var Verification = $(".text_box .content_box .Verification input").val()
            var properties = $(".text_box .content_box .properties h1").text()
            if (properties == "请选择楼盘") {
                alert("请选择楼盘")
                return;
            }
            if(name==""){
                alert("请输入您的姓名")
                return;
            }

            if(phone==""){
                alert("请输入您的电话")
                return;
            }else{
                if (!reg.test(phone)) {
                    alert("手机号格式不正确")
                    return;
                }
            }
            if (Verification == "") {
                alert("请输入验证码")
                return;
            }
            if ($(".cheimg978").hasClass("checked") == false){
                alert("请仔细阅读并同意服务协议及隐私政策。")
                return;
            }
            $.ajax({
                url: '/addTouristOrder',
                type: "post",
                data: {
                    // sessionId: sessionId,
                    phone: phone,
                    code: Verification,
                    projectId: $(".selectProjectId").val(),
                    projectName: properties,
                    memberName: name
                },
                success: function (value) {
                    if (value.status == 1) {
                        $(".formDivbg").show();
                        $(".tcbg").hide();
                        $(".properties h1").html(h1conent)
                        $(".cityBra,.properties_box ").removeClass("block")
                        $(".content_box input").val("")

                        setTimeout(function () {
                            $(".formDivbg").hide()
                        },3000)

                    } else {
                        alert(value.msg)
                        setInterval(function () {
                            window.history.go(-1)
                        }, 3000);
                    }
                }
            });
        }
    )

    //浮窗
    window.onscroll = function () {
        var s = $(this).scrollTop();
        if (s >= 346) {
            $(".headFixed").addClass("block");
        } else {
            $(".headFixed").removeClass("block");
        }
    }
</script>
<div th:include="fragment/fragment::commonFloat"></div>
<div th:include="fragment/fragment:: footer_seo"></div>
<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<!--    <div th:include="fragment/fragment::tongji"></div>-->
</body>
</html>