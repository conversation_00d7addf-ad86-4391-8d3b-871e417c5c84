<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
	<meta charset="utf-8" />
	<title>发布点评 - 房小二网</title>
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/common.css" />
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/swiper.min.css" />
	<script src="https://static.fangxiaoer.com/js/lvju/swiper.min.js" type="text/javascript" charset="utf-8"></script>
	<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/lvju/mycomment.css" />
	<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
</head>
<body>
<form name="form1" method="post" action="" id="form1">
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
	<div class="content">
		<p class="comLocation">您的位置：
			<a th:href="@{'/'}">沈阳房产网</a>>
			<a th:href="@{'/houses/'}">沈阳新房</a>>
			<a th:href="@{'/tourist.htm'}">旅居地产</a>> 我要点评
		</p>
		<div class="pro_name">
			<div class="containerHeader">我要点评</div>
			<input th:value="${#session?.getAttribute('sessionId')}" name = "sessionId" type="hidden">
			<input type="hidden" id="projectId" name="projectId" th:value="${projectId}">
			<input type="hidden" id="commentId" name="commentId" th:value="${commentId}">
			<div class="xiangxi">
				<div class="left">
					<i>*</i>
					您对楼盘的印象：
				</div>
				<div class="bottom">
					<p>
						<i><s id="zi">0</s></i>/500字(至少10字)
					</p>
				</div>
				<div>
					<textarea name="commentDesc" type="text" id="virtues" th:text="${commentInfo.commentDesc}" maxlength="500" placeholder="请认真分析项目情况，您专业的分析我们会推荐您的点评。"></textarea>
				</div>
			</div>
			<div class="photoTitle"><p>上传图片</p></div>
			<div class="pr" style="position: relative; margin: 0 auto;min-height: 216px;">
				<div style="width: 700px; display: inline-block;margin-left: 50px;">
					<link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/diyUpload.css?t=20220902"/>
					<link rel="stylesheet" type="text/css" href="/ImgUpLoad/Script/webuploader.css"/>

					<script type="text/javascript" charset="utf-8" src="https://static.fangxiaoer.com/js/webuploader.js"></script>
					<script type="text/javascript" charset="utf-8" src="/ImgUpLoad/Script/diyCommonUpload.js"></script>
					<script type="text/javascript">
						$(function () {
							$('#fileImg').diyUpload({
								url: '/uploadNewCommentPic',
								success: function (file, data) {
									var files = $("#ImgUpLoad1_imgValue").val();
									console.log(files)
									if (file._info.width > file._info.height && file._info.width >= 450 && file._info.height >= 450) {
										if (data) {
											if (files == "") {
												$(".fileBoxUl li").eq(0).find(".shoutu").show();
												$("#ImgUpLoad1_imgValue").val(file.id + "|" + data._raw);
											} else {
												$("#ImgUpLoad1_imgValue").val(files + "," + file.id + "|" + data._raw);
											}
										}
									}
									//else if (file._info.width <= file._info.height) {
									//        t = "请上传横版图片"
									//        $("#fileBox_" + file.id).find(".diyCancel").click()

									//}
									else if (file._info.width < 450 || file._info.height < 450) {
										t = "请上传大于450*450px的图片"
										$("#fileBox_" + file.id).find(".diyCancel").click()

									}
									$("#zhaopian").html(t)
								},
								error: function (err) {
									console.log(err);
								}
							});
							//SetImg();
						});

					</script>
					<div>
						<input type="hidden" name="showtuindex" id="ImgUpLoad1_showtuIndex" th:value="${showtuIndex}"/>
						<input type="hidden" name="imglist" id="ImgUpLoad1_imgList" th:value="${imgurl}"/>
						<input type="hidden" name="pic" id="ImgUpLoad1_imgValue" th:value="${imgurl}"/>
					</div>
					<div id="fileImg">

					</div>
				</div>
				<div class="xiaotieshi" style="left: 383px;    top: -7px;">
					<h3>小贴士</h3>
					1.带图片的房源排位更靠前，被看到的概率更大；<br>
					2.您上传的首张图片将作为封面图，建议选择室内图片；<br>
					3.为了更好展示您的房源，请选择大于450x450像素的图片。<br>
					4.添加几张照片，让您的信息更受欢迎（最多上传<i style="color:#ff5200">15</i>张，每张最大<i style="color:#ff5200">5M</i>，图片为<i style="color:#ff5200">jpg</i>格式）
				</div>
			</div>
			<div class="tijiao">
				<input type="button" name="submit" value="提交点评" id="submit" class="tijiao_btn" style="    float: left; margin-left: 55px;"/>
				<div class="bt"></div>
				<input id="cb_IsNiming" type="checkbox" name="cb_IsNiming" style="float: left;"/><label for="cb_IsNiming">匿名点评</label>
				<a href="https://event.fangxiaoer.com/20180911_ms.htm" target="_blank" >【房小二网用户点评内容管理规范】</a>
			</div>
		</div>
	</div>
<script th:inline="javascript">
    /*<![CDATA[*/
    var isNiming = [[${commentInfo.isNiming}]];
    /*]]>*/
</script>
	<script>
    $(function () {
        var f = isNiming == null || isNiming == 0 ? false : true;
        $(".bt").click(function () {
            if(f = !f){
                $(".bt").addClass('color');
                $("#cb_IsNiming").prop("checked",true);
            }else{
                $(".bt").removeClass('color');
                $("#cb_IsNiming").removeAttr("checked");
            }
        });
        $("#label").click(function () {
            if(f = !f){
                $(".bt").addClass('color');
                $("#cb_IsNiming").prop("checked",true);
            }else{
                $(".bt").removeClass('color');
                $("#cb_IsNiming").removeAttr("checked");
            }
        });
		//封装一个限制字数方法
		var checkStrLengths = function (str, maxLength) {
		    var maxLength = maxLength;
		    var result = 0;
		    if (str && str.length > maxLength) {
		        result = maxLength;
		    } else {0
		        result = str.length;
		    }
		    return result;
		}
		//监听输入
		$("#virtues").on('input propertychange', function () {
			//获取输入内容
		    var userDesc = $(this).val();
		    var len;
		    //判断字数
		    if (userDesc) {
		        len = checkStrLengths(userDesc, 500);
		    } else {
		        len = 0
		    }
		    //显示字数
		    $("#zi").html(len);
		});
	});
	//form 表单转json
	$.fn.serializeObject = function () {
		var o = {};
		var a = this.serializeArray();
		$.each(a, function () {
			if (o[this.name]) {
				if (!o[this.name].push) {
					o[this.name] = [o[this.name]];
				}
				o[this.name].push(this.value || '');
			} else {
				o[this.name] = this.value || '';
			}
		});
		return o;
	};
    $(".tijiao_btn").click(function () {
        var formData = $("#form1").serializeObject();
        var isNiMing = 0;
        if ($('#cb_IsNiming').is(':checked')) {
            isNiMing =1;
        }
        formData.isNiming = isNiMing
        var picurls = "";
        var pics =  $("#ImgUpLoad1_imgValue").val()
        if(pics != null && pics != undefined && pics != ''){
            pics = pics.split(",");
            for(var i =0;i<pics.length;i++){
                var pic =  pics[i].split("|");
                if(pic.length == 1) {
                    picurls = picurls != "" ? picurls + "," + pic[0].replace("https://imageicloud.fangxiaoer.com/middle/","")
                        : pic[0].replace("https://imageicloud.fangxiaoer.com/middle/","");
                }else if(pic.length > 1){
                    picurls = picurls != "" ? picurls + "," + pic[1].replace("https://imageicloud.fangxiaoer.com/middle/","")
                        : pic[1].replace("https://imageicloud.fangxiaoer.com/middle/","");
				}

            }
        }
        if ($("#virtues").val()==""){
        	alert("请填写点评内容")
			return
		}
        formData.commentPics = picurls;
        $.ajax({
            type: "POST",
            data: JSON.stringify(formData),
            url: "/tourist/saveComment",
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            dataType: "json",
            success: function(data) {
                if(data.status ==1 ){
                    alert("点评提交成功");
                    window.location.href= "https://my.fangxiaoer.com/comment";
                }else {
                    alert(data.msg);
                }
            }
        });

	});
</script>
</form>
<div class="cl" style="height: 25px;"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
</html>