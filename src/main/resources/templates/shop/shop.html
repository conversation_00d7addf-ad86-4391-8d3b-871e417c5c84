<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title th:text="${shop.title+'_'+shop.regionName+'商铺出售_沈阳商铺出售 - 房小二网'}"></title>
    <meta name="keywords" th:content="${shop.title+',沈阳商铺出售,'+shop.regionName+'商铺转让,出兑'}"/>
    <meta name="description"
          th:content="${'房小二网沈阳商铺频道为你提供'+shop.title+'，以及'+shop.regionName+'商铺与沈阳其他商铺，门市的出兑，出租，出售与转让信息，购买和发布沈阳商铺出租出售信息首选房小二网。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang4/'+houseId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?t=20220126"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/view2017.css?t=20180925"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/global/js/tab.js"></script>
    <script src="https://static.fangxiaoer.com/global/js/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/saleHouseIndent.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <script src="https://static.fangxiaoer.com/global/js/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/global/js/circulation.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <script src="/js/md5.js"></script>
    <script src="/js/house/detail/details_collection.js" type="text/javascript"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/house/deleteListings.css">
    <link rel="stylesheet" href="//g.alicdn.com/de/prismplayer/2.6.0/skins/default/aliplayer-min.css" />
    <script type="text/javascript" src="//g.alicdn.com/de/prismplayer/2.6.0/aliplayer-min.js"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
    <!--<script src="/js/indent.js"></script>-->
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/new_sy/comment/houseAlbum.css">
    <script src="https://static.fangxiaoer.com/js/houseAlbum.js"></script>
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
    <script>
        //图片自适应大小
        function imgSize5() {
            //滚动大图
            var imgWidthHeght2 = $(".imgMax1 ul li").eq(0).width() / $(".imgMax1 ul li").eq(0).height()
            $(".imgMax1 ul li img").each(function () {
                if (parseInt($(this).width()) <= parseInt($(this).height())) {
                    $(this).css({"height": "100%", "width": "auto"})
                    $(this).css({
                        "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                        "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                    })
                } else {
                    $(this).css({"width": "100%", "height": "auto"})
                    $(this).css({
                        "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                        "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                    })
                }
            })
        }

        $(function () {
            //更多相册
            var len = $(".photo li").length;
            if (len > 8) {
                $(".photo li:gt(7)").hide();
                $(".photo ul").after("<span>查看全部照片(" + len + "张)</span>")
            }
            $(".photo span").live("click", function () {
                $(this).hide();
                $(".photo li").show();
            })
        })
        //商铺专用enter事件
        $("input").keypress(function(e) {
            // 回车键事件
            if(e.which == 13) {
                $(".search_btn").click();
            }
        });
        function showLicense(e) {
            $(".license").show();
        }
        function hideLicense(e) {
            $(".license").hide();
        }
    </script>
    <style>
        .main .mainContent .basic ul li p{width: auto}
        .right .recommend ul li div p{line-height: 15px}
        .license{position: absolute;width: 260px;height: 370px;border: 1px solid #ccc;display: none;
            border-radius: 5px;z-index: 99;background-color: #FFF;bottom: 40px;right: 30px;padding: 5px;}
        .license_img{
            width: 100% !important;
            height: 100% !important;
            margin: 0px !important;
            border-radius: 0px !important;
        }
        .prism-player {
            min-width: 620px !important;
            min-height: 425px !important;
            z-index: 9;
        }
        .prism-player .prism-big-play-btn {
            top: 50% !important;
            left: 50% !important;
            width: 100px !important;
            height: 100px !important;
            margin-left: -50px;
            margin-top: -50px !important;
        }
        .main .mainContent{float: right}
        .photoAlbum .imgMin ul .hover{
            border: 3px solid #ff5200 !important;
            width: 111px;
            height: 74px;
        }
        .current-speed-selector{display: none}
        .photoAlbum .imgMin ul .hover img {
            margin-left: -55px !important;
            margin-top: -37px !important;
        }
        .map_lp{padding-top: 0px;margin-top: -10px;display: block}
        .map_lpcon{height: 270px}
        /*全屏*/
        .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
        .full .nmaptitleleft{margin-top:0}
        .full .symap{height:100%}
        .full .dt1{height:100%;}
        .full #memap{height: 100% !important;}
        .full .mapsele{height:660px !important}
        .nmaptitleleft a{float:right;background:url(https://static.fangxiaoer.com/js/map/img/ico_full.jpg) no-repeat;padding-left:23px}
        .nmaptitleleft a.map_full{background-position:0px -28px;margin-right:20px;}
        .nmaptitleleft a.map_house{background-position:0px 12px}
        .left .head {
            padding-top: 8px;
            border-bottom: 1px solid #ededed;
            color: #333;
            font-size: 20px;
            line-height: 56px;
        }
        .photo .head{
        }
        .details {
            border: 1px solid #ededed;
            width: 832px;
            padding:  15px 18px;
            margin-bottom: 30px;
            padding-top:  0;
            font-size:  14px;
        }
        .details>ul>li {
            overflow: hidden;
            border-bottom: 1px solid #ededed;
            padding-bottom:  10px;
            margin-bottom: 10px;
        }
        .photo{
            width: 832px !important;
            border:  1px solid #ededed;
            padding: 0 18px;
        }
        .details>ul>li:last-child{border-bottom:none;margin-bottom:  0;padding-bottom:  0;}
        .details>ul>li>div{padding-bottom:0;margin-bottom:0}

        .peo-text{
            margin-right: 0px !important;
        }
        .peo-b{
            font-size: 14px !important;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400 !important;
            color: #111111 !important;
            margin-top: 2px;
        }
        .people-img{

            margin-top: 13px;
        }
        .people-name{
            margin-bottom: 15px !important;
        }

        .ico-attestation{
            margin: 13px 0 0 0px !important;
        }




        .newCardTels{
            width: 104px !important;
            height: 37px !important;
            background: #FF6102 !important;
            border-radius: 6px !important;
            padding: unset !important;
            box-sizing: border-box;
            margin-top: -9px;
            display: flex;
            align-items: center;
        }
        .newCardTels>i{
            display: inline-block;
            width: 18px !important;
            height: 18px !important;
            margin: 0 !important;
            border-radius: initial !important;

            float: left;
            padding-left: 10px;
        }

        .p-note{

            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 19px;
            float: right;
            width: 340px;
            margin-top: 19px;
        }
        .main .mainContent img{
            margin-top: 23px;
        }





        .people-name{
            margin-bottom: 20px !important;
        }
        .ico-attestation{
            margin: 10px 0 0 0px !important;
        }
        .agent-text{
            margin-top: -12px !important;
        }
        .sddti{
            height: 37px !important;
            line-height: 37px !important;
        }
        .ico-name-attestation{
            margin-left: 0px !important;
        }
        .agent-text2{
            margin-bottom: 2px !important;
        }
        .agent-vip{
            width: 20px !important;
            height: 20px !important;
            margin-top: 5px !important;
            float: inherit !important;

            margin-left: 0px !important;
        }
        #houseDetailOwner{
            margin-right: 0px !important;
        }
        .newCardTels{

            float: right !important;
        }


        .p-note2{
            font-size: 13.9px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 19px;
        }

        .people-img img{
            width: 100px !important;
            height: 100px !important;
            border-radius: 50% !important;
        }

        #people-dl{
            width: 351px !important;
        }
        #agent-text1{
            margin-top: -6px !important;
        }

        .sddtj{
            color: #999999 !important;
            width: 169px !important;

            white-space: nowrap !important;
            text-overflow: ellipsis !important;
            overflow: hidden;
        }


        .noHover:hover{
            cursor: auto !important;
        }



    </style>
</head>
<body>

<!--<form name="form1" method="post" action="7727" id="form1">-->
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=5"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=4"></div>
    <input type="hidden" id="Idhouse" th:value="${houseId}"/>
    <input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}"/>
    <input type="hidden" id="houseType" value="3" />
    <input type="hidden" id="type" th:value="${'5'}">
    <input type="hidden" id="projectId" th:value="${houseId}">
    <input type="hidden" id="goods" value="1" />
    <input type="hidden" value="3" id="collectType"/>
    <input type="hidden" id="mediaId" th:value="${houseVideo?.mediaID}">
    <input type="hidden" id="videoPath" th:value="${videoPath}">
    <input type="hidden" id="videoImgPath" th:value="${videoImgPath}">
    <input type="hidden" id="housePan" th:value="${shop.housePan}">
    <input type="panUrl" id="panUrl" th:value="${panUrl_VR}" style="display:none">
    <input type="panUrl" id="panUrl_VRImg" th:value="${panUrl_VRImg}" style="display:none">
    <!--面包屑-->
    <div class="w crumbs">您的位置：<a th:href="@{'/'}" target="_blank">沈阳房产网</a> &gt; <a th:href="@{'/shops'}"
                                                                                     target="_blank">沈阳商铺</a> &gt;
        <a th:href="${#strings.isEmpty(shop.regionId)?'':'/shops/-r'+shop.regionId}"
           th:text="${#strings.isEmpty(shop.regionName)?'':shop.regionName}">铁西区</a> &gt;
        <a th:href="${#strings.isEmpty(shop.plateId)?'':'/shops/-r'+shop.regionId + '-j' +shop.plateId}"
           th:text="${#strings.isEmpty(shop.plateName)?'':shop.plateName}"></a> &gt;
        <th:block th:text="${#strings.isEmpty(shop.title)?'':shop.title}"></th:block>
    </div>
    <!--基本信息-->
<th:block th:if="${#strings.toString(shop.isDel) ne '1'}">
    <div class="w main">
        <div class="header_sale">
            <div class="cl"></div>
            <h1 class="title1" th:text="${#strings.isEmpty(shop.title)?'':shop.title}">（出租）铁西云峰街北四路 巴塞罗那 60平米 一楼门市</h1>
            <ul>
                <li th:text="${#strings.isEmpty(shop.checkTimeStr)?'':'审核时间：'+ shop.checkTimeStr}"></li>
                <li th:text="${#strings.isEmpty(shop.visitNum)?'':shop.visitNum+'人已浏览'}">1人已浏览</li>
                <a class="soucang" data-toggle="modal" href="#login"><i></i>收藏</a>
            </ul>
            <!--分享标签-->
            <div th:include="fragment/fragment:: share_code"></div>
        </div>
        <div class="houseAlbum" id="houseAlbum">
            <div class="big_photo" id="big_photo">
                <ul>
                    <li th:if="${!#lists.isEmpty(shop.housePan)}" class="noAClick">
                        <a class='panUrl' target='_blank' th:href="${panUrl_VR}">
                            <img onload='imgSize()' style='width: 100%; height: auto;margin-left: -35px;' class='panUrl_VRImg' th:src="${#strings.isEmpty(panUrl_VRImg)?'':#strings.toString(panUrl_VRImg).replaceAll('middle','big')}" >
                        </a>
                    </li>
                    <li class="prism-player2 noAClick " id="J_prismPlayer" th:if="${houseVideo?.mediaID}" style="height: 417px !important;"></li>
                    <li th:if="${#lists.isEmpty(shop.pic)}">
                        <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" onload="imgSize()"  />
                    </li>
                    <!--图片轮播-->
                    <li  th:if="${!#lists.isEmpty(shop.pic)}"  th:each="pics,p:${shop.pic}" class="playerPause">
                        <img th:src="${#strings.isEmpty(pics.pic)?'':#strings.toString(pics.pic).replaceAll('middle','big')}" onload="imgSize()" />
                    </li>
                </ul>
                <!--视频首图-->
                <div  th:if="${!#lists.isEmpty( shop.pic)}" th:each="pic,p:${shop.pic}" style="display: none;">
                    <input id="indexPic" th:if="${p.index == 0}"  th:value="${#strings.toString(bigImage).replace('middle','big')}" style="display: none;"/>
                </div>
            </div>
            <div class="min_photo">
                <div class="min_photoMain clearfix" id="min_photoMain">
                    <ul style="left: 0">
                        <li th:if="${!#lists.isEmpty(shop.housePan)}" class="noAClick">
                             <img onload='imgSize()' style='width: 100%; height: auto;' class='panUrl_VRImg' th:src="${#strings.isEmpty(panUrl_VRImg)?'':#strings.toString(panUrl_VRImg).replaceAll('middle','big')}" >
                        </li>
                        <li th:if="${houseVideo?.mediaID}">
                            <i class='videoIcon'></i>
                            <img th:src="${videoImgPath}">
                        </li>
                        <li th:if="${#lists.isEmpty( shop.pic)}">
                            <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" onload="imgSize()"  />
                        </li>
                        <li  th:if="${!#lists.isEmpty(shop.pic) }"  th:each="pics,p:${shop.pic}" class="playerPause">
                            <img th:src="${#strings.isEmpty(pics.pic)?'':#strings.toString(pics.pic).replaceAll('middle','big')}" onload="imgSize()" />
                        </li>
                    </ul>
                </div>
                <div class="min_prev_btn1 playerPause" id="min_prev_btn1"><s></s></div>
                <div class="min_next_btn1 playerPause" id="min_next_btn1"><s></s></div>
            </div>
        </div>
        <!--弹窗轮播大图-->
        <div class="photo_Popup"></div>
        <div class="large_photo" id="large_photo">
            <div class="photo_Popup_xx"></div>
            <div class="large_photoMain" id="large_photoMain">
                <ul>
                    <li th:if="${!#lists.isEmpty(shop.housePan)}" class="noAClick">
                        <a target='_blank' th:href="${panUrl_VR}">
                            <img class='panUrl_VRImg' th:src="${#strings.isEmpty(panUrl_VRImg)?'':#strings.toString(panUrl_VRImg).replaceAll('middle','big')}" >
                        </a>
                    </li>
                    <li class="prism-player2" id="J_prismPlayer2" th:if="${houseVideo?.mediaID}" style="height:100% !important;width: 1170px"></li>
                    <li th:if="${#lists.isEmpty( shop.pic)}">
                        <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" />
                    </li>
                    <li th:if="${!#lists.isEmpty(shop.pic)}"  th:each="pics,p:${shop.pic}" class="playerPause">
                        <img th:src="${#strings.isEmpty(pics.pic)?'':#strings.toString(pics.pic).replaceAll('middle','big')}" />
                    </li>
                </ul>
            </div>
            <div class="large_prev_btn1 playerPause" id="large_prev_btn1"></div>
            <div class="large_next_btn1 playerPause" id="large_next_btn1"></div>
        </div>
        <script>
            $(document).ready(function () {
                var videoVal = $("#mediaId").val();
//                        var housePan = $("#housePan").val();
//                        var panUrl = $("#panUrl").val();
//                        var panUrl_VRImg = $("#panUrl_VRImg").val();
                var videoMinImg = $("#indexPic").val()
                if( videoVal !=""){
                    //改为调接口获取视频地址（2018.10.20）
                    player = new Aliplayer({
                        id: 'J_prismPlayer',
                        autoplay: false,
                        //支持播放地址播放,此播放优先级最高
                        source: $("#videoPath").val(),
                        cover: $("#videoImgPath").val()
                    }, function (player) {
                        player.on("ended", endedHandle);
                        console.log('播放器创建好了。')
                    });
                    function endedHandle() {
                        player.dispose(); //销毁
                        $('#J_prismPlayer').empty();
                        //重新创建
                        player = new Aliplayer({
                            id: 'J_prismPlayer',
                            autoplay: false,
                            //支持播放地址播放,此播放优先级最高
                            source: $("#videoPath").val(),
                            cover: $("#videoImgPath").val()
                        });
                    };
                    player2 = new Aliplayer({
                        id: 'J_prismPlayer2',
                        autoplay: false,
                        //支持播放地址播放,此播放优先级最高
                        source: $("#videoPath").val(),
                        cover: $("#videoImgPath").val()
                    }, function (player2) {
                        player2.on("ended", endedHandle);
                        console.log('播放器创建好了。')
                    });
                    function endedHandle() {
                        player2.dispose(); //销毁
                        $('#J_prismPlayer2').empty();
                        //重新创建
                        player2 = new Aliplayer({
                            id: 'J_prismPlayer2',
                            autoplay: false,
                            //支持播放地址播放,此播放优先级最高
                            source: $("#videoPath").val(),
                            cover: $("#videoImgPath").val()
                        });
                    }
                    $(".playerPause").click(function () {
                        player.pause();
                        player2.pause();
                    })

                }

//                头部详情房源相册轮播
                $('#demo1').banqh({
                    box:"#houseAlbum",//总框架
                    pic:"#big_photo",//大图框架
                    pnum:"#min_photoMain",//小图框架
                    prev_btn:"#min_prev_btn1",//小图左箭头
                    next_btn:"#min_next_btn1",//小图右箭头
                    pop_prev:"#large_prev_btn1",//弹出框左箭头
                    pop_next:"#large_next_btn1",//弹出框右箭头
                    pop_div:"#large_photo",//弹出框框架
                    pop_pic:"#large_photoMain",//弹出框图片框架
                    pop_xx:".photo_Popup_xx",//关闭弹出框按钮
                    mhc:".photo_Popup",//朦灰层
                    autoplay:false,//是否自动播放
                    interTime:5000,//图片自动切换间隔
                    delayTime:400,//切换一张图片时间
                    pop_delayTime:400,//弹出框切换一张图片时间
                    order:0,//当前显示的图片（从0开始）
                    picdire:true,//大图滚动方向（true为水平方向滚动）
                    mindire:true,//小图滚动方向（true为水平方向滚动）
                    min_picnum:4,//小图显示数量
                    pop_up:true//大图是否有弹出框
                })
//                房源详情模块相册点击弹出
                $('#demo2').banqh({
                    box:"#houseAlbum2",//总框架
                    pic:"#big_photo2",//大图框架
                    pnum:"#min_photoMain2",//小图框架
                    prev_btn:"#min_prev_btn12",//小图左箭头
                    next_btn:"#min_next_btn12",//小图右箭头
                    pop_prev:"#large_prev_btn12",//弹出框左箭头
                    pop_next:"#large_next_btn12",//弹出框右箭头
                    pop_div:"#large_photo2",//弹出框框架
                    pop_pic:"#large_photoMain2",//弹出框图片框架
                    pop_xx:".photo_Popup_xx",//关闭弹出框按钮
                    mhc:".photo_Popup",//朦灰层
                    autoplay:false,//是否自动播放
                    interTime:5000,//图片自动切换间隔
                    delayTime:400,//切换一张图片时间
                    pop_delayTime:400,//弹出框切换一张图片时间
                    order:0,//当前显示的图片（从0开始）
                    picdire:true,//大图滚动方向（true为水平方向滚动）
                    mindire:true,//小图滚动方向（true为水平方向滚动）
                    min_picnum:4,//小图显示数量
                    pop_up:true//大图是否有弹出框
                })
                $(".noAClick").click(function () {
                    $(".photo_Popup").hide();
                    $(".large_photo").hide();
                })

            })

        </script>

        <div class="newShopMainRight">
            <div th:class="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '1'? (#strings.toString(shop.isDel) ne '1' ?'mainContent chushou':(#strings.toString(shop.shopState) eq '4' and #strings.toString(shop.state ) eq '3' ?'mainContent chushou sellState1':'mainContent chushou sellState3'))
            :(shop.shopType eq '2'?(#strings.toString(shop.isDel) ne '1' ?'mainContent chuzu':(#strings.toString(shop.shopState) eq '4' and #strings.toString(shop.state ) eq '3' ?'mainContent chuzu sellState2':'mainContent chuzu sellState3'))
            :(#strings.toString(shop.isDel) ne '1' ?'mainContent chudui':(#strings.toString(shop.shopState) eq '4' and #strings.toString(shop.state ) eq '3' ?'mainContent chudui sellState2':'mainContent chudui sellState3')))}  ">
                <div class="price cs" th:if="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '1' }"><span>
                    <b th:text="${#strings.isEmpty(shop.price)or shop.price eq '0.00' ?'面议': (#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))}">42000</b>
                    <th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'':'万'}"></th:block> </span>
                    <p><span>出售</span></p>
                </div>
                <div class="price cz" th:if="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '2' }"><span><b
                        th:text="${#strings.isEmpty(shop.price)or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))}">42000</b>
                    <th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ? '':(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'?'元/年':'元/月')}"></th:block></span>
                    <p><span>出租</span></p>
                    <p><span th:text="${#strings.isEmpty(shop.paymentName) or shop.price eq '0.00' ?'':shop.paymentName}">年付</span></p>
                </div>
                <div class="price cd" th:if="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '3'}"><span><b
                        th:text="${#strings.isEmpty(shop.price)or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))}">42000</b>
                    <th:block th:text="${#strings.isEmpty(shop.price)or shop.price eq '0.00' ? '':(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'?'元/年':'元/月')}"></th:block></span>
                    <p><span>出兑</span></p>
                    <p><span th:text="${#strings.isEmpty(shop.paymentName)or shop.price eq '0.00' ?'':shop.paymentName}">年付</span></p>
                </div>
                <div class="basic">
                    <ul>
                        <li th:if="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '3'}" class="cd">
                            <span>转 让 费</span>
                            <p>
                                <th:block th:text="${#strings.isEmpty(shop.tranTypeName)?'':shop.tranTypeName}"></th:block>
                                <th:block th:text="${#strings.isEmpty(shop.tranFee) or shop.tranFee eq 0? '面议':(#strings.indexOf(shop.tranFee,'.') eq -1 ? shop.tranFee:#strings.toString(shop.tranFee).replaceAll('0+?$','').replaceAll('[.]$',''))+'万元'}"></th:block>
                            </p>
                        </li>
                        <li>
                            <span>所在地段</span>
                            <p th:title="${#strings.isEmpty(shop.address)?'':shop.address}" ><a class="quyu" th:href="${'/shops/r'+shop.regionId}"
                                  th:text="${#strings.isEmpty(shop.regionName)?'':shop.regionName}" target="_blank">铁西区</a>
                                -
                                <th:block th:text="${#strings.isEmpty(shop.plateName)?'其它':shop.plateName}"></th:block>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <th:block th:text="${#strings.isEmpty(shop.address)?'':#strings.abbreviate(shop.address,16)}"></th:block>
                            </p>
                        </li>
                    </ul>
                </div>
                <div class="type">
                    <ul>
                        <li>
                            <b th:text="${#strings.isEmpty(shop.shopCategoriesName)?'':shop.shopCategoriesName}">住宅底商</b>
                            <p>商铺类型</p>
                        </li>
                        <li>
                            <b th:text="${#strings.isEmpty(shop.area)?'': (#strings.indexOf(shop.area,'.') eq -1 ? shop.area:#strings.toString(shop.area).replaceAll('0+?$','').replaceAll('[.]$',''))+'m²'}">60m²</b>
                            <p>面积</p>
                        </li>
                        <li>
                            <b th:text="${#strings.isEmpty(shop.isCutName)?'不可分割':shop.isCutName}">无</b>
                            <p>是否分割</p>
                        </li>
                    </ul>
                </div>
                <div th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '2'}" class="card jjr" style="position: relative;overflow: inherit;">
                    <div class="people-img">
                        <a class="people-img-join" th:if="${shop.agentState eq '1'}"
                           th:href="${#strings.isEmpty(shop.agencyId)?'':'/agent/shops/'+shop.agencyId}" target="_blank">
                            <img th:src="${#strings.isEmpty(shop.headPic)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':shop.headPic}"/>
                        </a>

                        <a class="people-img-join" th:if="${shop.agentState ne '1'}">
                            <img th:src="${#strings.isEmpty(shop.headPic)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':shop.headPic}"/>
                        </a>


                    </div>




                     <dl  th:class="${shop.memberType} eq '1' ? 'normolTel':'agentTel'" id="people-dl">
                        <dt class="people-name">
                            <a th:if="${shop.agentState eq '1'}"   th:href="${#strings.isEmpty(shop.agencyId)?'':'/agent/shops/'+shop.agencyId}" target="_blank">
                                <b th:text="${#strings.isEmpty(shop.houseOwner)?'':shop.houseOwner}">任薏明</b>
                                <img  th:if="${shop.intermediaryState eq '1'}"   class="agent-vip" src="https://static.fangxiaoer.com/web/images/sy/house/agent-vip.png" >
                            </a>

                            <a th:if="${shop.agentState ne '1'}"  >
                                <b th:text="${#strings.isEmpty(shop.houseOwner)?'':shop.houseOwner}" class="noHover">任薏明</b>

                            </a>




<!--                            <span th:text="${#strings.isEmpty(shop.intermediaryName)?'':shop.intermediaryName}">喜鹊不动产晶座店</span>-->
                        </dt>
                        <!--<dd class="cardTels" ></dd>-->
                        <th:block th:if="${shop.memberType ne '1'}">
                            <!--新增经纪人相关认证-->
                            <div >
<!--                                <div style="float: left"   class="agengt-ico-new" th:if="${shop.agentBusinessCardForPc eq null or shop.agentBusinessCardForPc eq ''}"></div>-->
                                <div class="ico-attestation" id="agent-text1">
                                    <!--<div class="ico-house-attestation"><i></i><span>房本认证</span></div>-->
                                    <div class="ico-name-attestation" th:if="${shop.hasIdCard eq '1'}"><i></i><span>实名认证</span></div>

                                    <th:block th:if="${shop.agentState eq '1'}">
                                        <div class="sddtj"><lable th:text="${shop.agentIntermediaryAlias}"></lable></div>


                                    </th:block>
                                    <th:block th:if="${shop.agentState ne '1'}">
                                        <div class="sddtj">经纪人</div>
                                    </th:block>

                                </div>
                            </div>
                             <!--<span  class="encoded" th:unless="${shop.agentBusinessCardForPc eq null or shop.agentBusinessCardForPc eq ''}"
                               onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                                 <i>公司执照编码：</i>
                                <span>
                                    <th:block th:text="${shop.agentBusinessNum}"></th:block>
                                </span>
                            </span>-->
<!--                            <a th:if="${#session?.getAttribute('sessionId') == null}" href="#login" target="_blank" data-toggle="modal">-->
<!--                                <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" class="newChat">-->
<!--                                    <img src="https://static.fangxiaoer.com/im_ziyuan/im/images/liaobei.png" >-->
<!--                                    <text >在线聊呗</text>-->
<!--                                </div>-->
<!--                            </a>-->
<!--                            <a th:if="${#session?.getAttribute('sessionId') != null}" th:href="${'/im/'+houseId+'-3'}" target="_blank">-->
<!--                                <div onmouseover="changeColor(this)" onmouseout="changeColor2(this)" class="newChat">-->
<!--                                    <img src="https://static.fangxiaoer.com/im_ziyuan/im/images/liaobei.png">-->
<!--                                    <text >在线聊呗</text>-->
<!--                                </div>-->
<!--                            </a>-->
                            <!--微信扫码拨号-->
                            <dd class="newCardTels">
                                <i><img src="https://static.fangxiaoer.com/web/images/sy/sale/s_6.png" alt=""></i>
                                <span>电话咨询</span>
                                <div class="show-CardTels">
                                    <img id="shop1" src="" alt="">
                                    <p>微信扫码拨号</p>
                                    <p><i>快速获取经纪人联系方式</i></p>
                                </div>
                            </dd>
                            <div style="clear: both;"></div>
                            <div class="p-note2"> 如在沟通或交易过程中遇到任何问题，均可致电房小二网平台服务电话：************</div>
                            <div style="clear: both;"></div>

                            <!--<div class="license" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                                <img th:src="${shop.agentBusinessCardForPc}" class="license_img">
                            </div>
                            <div style="clear: both;"></div>-->
                        </th:block>
                    </dl>
                    <div style="clear: both;"></div>
                    <script src="/js/appointmentLook.js" type="text/javascript"></script>
                    <script>
                        function changeColor(e) {
                            $(e).css("background-color","#188CDE")
                        }
                        function changeColor2(e) {
                            $(e).css("background-color","#32a3f2")
                        }
                    </script>
                </div>
                <script>
                    $("#CommentListAdd").click(function () {
                        $("#login").show();
                        $(".reportPopupHb").show();

                    })
                </script>
                <div th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '1'}" class="card gr">
                    <p><img th:src="${#strings.isEmpty(shop.headPic)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':shop.headPic}"/>
                    </p>
                    <dl style="width: 340px;">
                        <dt style="margin:10px 0;"><b class="peo-text" th:text="${#strings.isEmpty(shop.houseOwner)?'':shop.houseOwner}">陈先生</b>
                            <b th:if="${!#strings.isEmpty(shop.houseOwner)}" class="peo-b" >（个人）</b>
                        </dt>
                        <!--认证icon-->
                        <div class="ico-attestation" th:if="${shop.agentBusinessCardForPc eq null or shop.agentBusinessCardForPc eq ''}" style="margin: 12px 0 0 10px;">
                            <!--<div class="ico-house-attestation"><i></i><span>房本认证</span></div>-->
                            <div class="ico-name-attestation" th:if="${shop.hasIdCard eq '1'}"><i></i><span>实名认证</span></div>
                        </div>
                        <dd class="newCardTels">
                            <i><img src="https://static.fangxiaoer.com/web/images/sy/sale/s_6.png" alt=""></i>
                            <span>电话咨询</span>
                            <div class="show-CardTels">
                                <img id="shop1" src="" alt="">
                                <p>微信扫码拨号</p>
                            </div>
                        </dd>


                    </dl>
                    <div class="p-note"> 如在沟通或交易过程中遇到任何问题，均可致电房小二网平台服务电话：************</div>
                </div>
                <script th:inline="javascript">
                    /*<![CDATA[*/
                    var ownerPhone = [[${shop.ownerPhone}]];
                    var isDel = [[${shop.isDel}]];
                    var shopState = [[${shop.shopState}]];
                    var state = [[${shop.state}]];
                    $(function () {
                        var tels="";
                        if(ownerPhone.substring(0,3) == 400){
                            tels = ownerPhone;
                        }else{
                            if (isDel == 1 || (shopState == 4 && state == 3)) {
                                tels = ownerPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                            }else {
                                tels = ownerPhone;
                            }
                        }

                        var sessionId = $("#sessionId").val()
                        var projectId = $("#projectId").val()
                        var linksessionId = '';
                        if (sessionId!= ''){
                            var pagekey = 'https://sy.fangxiaoer.com/shop/'+projectId+'.htm?sessionId='+sessionId
                            $.ajax({
                                type: "POST",
                                async: false,
                                url:  "https://tools.fangxiaoer.com/url/encode",
                                data:{'url': pagekey},
                                dataType : 'json',
                                success: function (data) {
                                    console.log(data)
                                    linksessionId = (data.content).replace("/",'');
                                    console.log(linksessionId)

                                }
                            });
                        }

                        var scene = 'tel,' +[[${houseId}]]+',4,' +  linksessionId;
                        var img = "";
                        var sss;
                        $.ajax({
                            type: "GET",
                            async: false,
                            url:  "/getWxACode",
                            data:{"scene": scene},
                            dataType : 'json',
                            headers : {
                                'Content-Type' : 'application/json;charset=utf-8'
                            },
                            success: function (data) {
                                img = data.img;
                                sss = data;
                            }
                        });
                        $("#shop1").attr("src","data:text/html;base64,"+img);
                    });
                    /*]]>*/
                </script>
            </div>
        </div>

        <div class="w" style="overflow: hidden;">
            <div class="left">
                <div class="details">
                    <div class="head">房源描述</div>
                    <ul>
                        <li th:if="${!#strings.isEmpty(shop.mIndustryName)}">
                            <span>商铺配套</span>
                            <div>
                                <th:block th:text="${#strings.isEmpty(mIndustryName)?'':mIndustryName}"></th:block>
                            </div>
                        </li>
                        <li>
                            <span>房源描述</span>
                            <div class="fyms"><span
                                    style="color:#333333;font-family:微软雅黑;font-size:14px;line-height:24px;">
                                <th:block
                                        th:utext="${#strings.isEmpty(shop.description)?'':shop.description}"></th:block>
                            </span></div>
                        </li>
                       <!-- <li class="" th:if="${!#strings.isEmpty(shop.serviceIntro)}">
                            <span>服务介绍</span>
                            <div class="fyms" th:utext="${shop.serviceIntro}" ></div>
                            &lt;!&ndash;<div style="border:0"></div>&ndash;&gt;
                        </li>-->
                        <li class=""  th:if="${!#strings.isEmpty(shop.serviceIntro) || !#strings.isEmpty(agentLabel)}">
                            <section th:if="${!#strings.isEmpty(shop.serviceIntro)}">
                                <span>服务介绍</span>
                                <div class="fyms" th:utext="${shop.serviceIntro}" style="border:0;margin-bottom: 0;"></div>
                            </section>
                            <!--经纪人服务标签-->
                            <section th:if="${!#strings.isEmpty(agentLabel)}">
                                <span>服务特色</span>
                                <div class="grfw">
                                    <span  th:each="agentLabel:${agentLabel}"  th:text="${agentLabel.name}"></span>
                                </div>
                            </section>
                        </li>
                    </ul>
                </div>
                <div class="details details2" th:if="${!#strings.isEmpty(shop.weAre) or !#strings.isEmpty(shop.serviceProcess) or !#strings.isEmpty(shop.servicePromise) or !#strings.isEmpty(shop.suggestion)}">
                    <div class="head">服务保障</div>
                    <ul>
                        <li th:if="${!#strings.isEmpty(shop.weAre)}">
                            <i class="serve serve-1"></i>
                            <span>我们是谁？</span>
                            <div class="fyms" th:utext="${shop.weAre}"></div>
                        </li>
                        <li th:if="${!#strings.isEmpty(shop.servicePromise)}">
                            <i class="serve serve-2"></i>
                            <span>我的承诺</span>
                            <div class="fyms" th:utext="${shop.servicePromise}"></div>
                        </li>
                        <li th:if="${!#strings.isEmpty(shop.serviceProcess)}">
                            <i class="serve serve-3"></i>
                            <span>服务流程</span>
                            <div class="fyms" th:utext="${shop.serviceProcess}"></div>
                        </li>
                        <li th:if="${!#strings.isEmpty(shop.suggestion)}">
                            <i class="serve serve-4"></i>
                            <span>服务建议</span>
                            <div class="fyms" th:utext="${shop.suggestion}"></div>
                        </li>
                    </ul>
                </div>
                <div class="w photo" th:if="${!#lists.isEmpty(shop.pic)}" id="big_photo2">
                    <div class="head " >房源相册</div>
                    <ul >
                        <li th:each="pics:${shop.pic}">
                            <img th:src="${#strings.isEmpty(pics.pic)? '': pics.pic}">
                        </li>
                    </ul>
                </div>
            </div>
            <!--房源相册弹窗轮播大图-->
            <div class="large_photo" id="large_photo2">
                <div class="photo_Popup_xx"></div>
                <div class="large_photoMain" id="large_photoMain2">
                    <ul>
                        <li th:if="${!#lists.isEmpty(shop.pic)}"  th:each="pics,p:${shop.pic}" class="playerPause">
                            <img th:src="${#strings.isEmpty(pics.pic)?'':#strings.toString(pics.pic).replaceAll('middle','big')}" />
                        </li>
                    </ul>
                </div>
                <div class="large_prev_btn1 playerPause" id="large_prev_btn12"></div>
                <div class="large_next_btn1 playerPause" id="large_next_btn12"></div>
            </div>
            <div class="right">
                <div id="xiangsihouse" class="head recommend">
                    <h1>同区域您可能感兴趣的房源</h1>
                    <ul>
                        <li  th:each="recommend,status:${recommend}" th:if="${!#lists.isEmpty(recommend)}">
                            <a th:href="${#strings.isEmpty(recommend.shopId)? '':'/shop/'+recommend.shopId+'.htm' }">
                                <div>
                                    <img th:src="${#strings.isEmpty(recommend.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': recommend.pic}"
                                         th:alt="${#strings.isEmpty(recommend.title)? '':recommend.title}">
                                    <div>
                                        <p th:if="${recommend.shopType eq '1'}">
                                            <span th:text="${#strings.isEmpty(recommend.price)or recommend.price eq '0.00'? '面议':(#strings.indexOf(recommend.price,'.') eq -1 ? recommend.price:#strings.toString(recommend.price).replaceAll('0+?$','').replaceAll('[.]$','')) }">45000元/年</span>
                                            <i th:if="${!#strings.isEmpty(recommend.price) and recommend.price ne '0.00'}" th:text="${'万'}"></i>
                                        </p>
                                        <p th:if="${recommend.shopType eq '2' or recommend.shopType eq '3'}">
                                            <span th:text="${#strings.isEmpty(recommend.price)or recommend.price eq '0.00'? '面议':(#strings.indexOf(recommend.price,'.') eq -1 ? recommend.price:#strings.toString(recommend.price).replaceAll('0+?$','').replaceAll('[.]$','')) }">45000元/年</span>
                                            <i th:if="${!#strings.isEmpty(recommend.price) and recommend.price ne '0.00'}" th:text="${recommend.payment  eq '50' or recommend.payment eq '100'or recommend.payment eq '110' ?  '元/年':'元/月'}"></i>
                                        </p>
                                        <b th:if="${recommend.shopType eq '1'}" th:text="${#strings.isEmpty(recommend.regionName)? '':'(出售)'+recommend.regionName }">(出租)铁西区</b>
                                        <b th:if="${recommend.shopType eq '2'}" th:text="${#strings.isEmpty(recommend.regionName)? '':'(出租)'+recommend.regionName }">(出租)铁西区</b>
                                        <b th:if="${recommend.shopType eq '3'}" th:text="${#strings.isEmpty(recommend.regionName)? '':'(出兑)'+recommend.regionName }">(出租)铁西区</b>
                                        <i style="color: #666" th:text="${#strings.isEmpty(recommend.area)? '':#numbers.formatInteger(recommend.area,1)+'m²'}">81m²</i>
                                        <p th:text="${#strings.isEmpty(recommend.shopCategoriesName)?'':recommend.shopCategoriesName}">住宅底商</p>
                                    </div>
                                </div>
                            </a>
                        </li>

                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="cl"></div>

    <!--周边配套-->
    <div class="w">
<!--        <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
        <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
        <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
        <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>
        <script th:inline="javascript">
            $(document).ready(function () {

                var id = [[${houseId}]];
                var lng = [[${shop._longitude}]];
                var lat = [[${shop._latitude}]];
                if(lat != null ){
                    var title = "商铺本案";
                    var address = [[${shop.address}]];
                    var city = '沈阳';
                    var content = "";
                    $("#mapsubTitle").val(title);
                    bdMap.init("memap", { id: id, houselng: lng, houselat: lat, radius: 2000, suofa: 14, bdtitle: title, bdcontent: content, address: address, city: city });
                }
            });
        </script>

        <div id="MapZhouBianPeiTao1_mapDom" th:if="${!#strings.isEmpty(shop._longitude) && !#strings.isEmpty(shop._latitude)}">
            <div class="nmaptitle">
                <div class="nmaptitleleft">
                    <a href="https://sy.fangxiaoer.com/salemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                    <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                    <p>周边配套</p>
                </div>
            </div>
            <div class="symap">
                <div class="dt1">
                    <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                    </div>
                    <div class="mapsele" id="mapsele" >
                        <div class="mapTab" id="mapTab">
                            <ul>
                                <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                                <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                                <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                                <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                                <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                                <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                            </ul>
                        </div>
                        <div class="map_lp">
                            <div id="hs_wrap">
                                <div class="map_lpcon"  id="r-result">
                                    <div class="map_dl">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <script>
                    $(function () {
                        $(".mapTab ul li ").click(function () {
                            $(".mapTab ul li ").removeClass("hover")
                            $(this).addClass("hover")
                            $(".map_lp").show();
                        });
                        $(".map_dl a").mouseover(function () {
                            $(this).css("background-color", "#e8f4ff");
                        });
                        $(".map_dl a").mouseout(function () {
                            $(this).css("background-color", "#fff");
                        });
                    });
                    function Full() {
                        if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                            $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                            $(".map_lpcon").height("288px")
                            $(".map_full").html("全屏")
                        } else {
                            $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                            $(".map_lpcon").height("100%")
                            $(".map_full").html("取消全屏")
                        }
                    }
                </script>
            </div>
        </div>
    </div>
    <div class="disclaimer"><strong>免责声明：</strong>
        房源信息由网站用户提供，其真实性、合法性由信息提供者负责，最终以政府部门登记备案为准。本网站不声明或保证内容之正确性和可靠性，租赁该房屋时，请谨慎核查。如该房源信息有误，您可以投诉或<strong>拔打举报电话：************</strong>。
    </div>
</th:block>
<th:block th:if="${#strings.toString(shop.isDel) eq '1'}">
    <div class="delList">
        <div class="delListMsg">
            <img src="https://static.fangxiaoer.com/web/images/sy/house/deleteListingsIcon.png" alt="">
            <div>
                <h4>抱歉，您查看的信息已过期!</h4>
                <p>建议您继续访问其他页面<a href="/shops/">查看更多商铺房源 ></a></p>
            </div>
        </div>
        <!--	商铺删除房源-->
        <div class="delListContListShop" th:if="${!#lists.isEmpty(recommend) and recommend.size() ge 4}">
            <h4>同区域您可能感兴趣的房源</h4>
            <ul>
                <li th:each="recommend,status:${recommend}" th:if="${status.index lt 5}">
                    <a th:href="${#strings.isEmpty(recommend.shopId)? '':'/shop/'+recommend.shopId+'.htm'}" target="_blank">
                        <img th:src="${#strings.isEmpty(recommend.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': recommend.pic}"
                             th:alt="${#strings.isEmpty(recommend.title)? '':recommend.title}">
                        <p>
                            <b th:text="${#strings.isEmpty(recommend.regionName)?'':recommend.regionName}">和平区</b>
                            <span th:text="${#strings.isEmpty(recommend.shopCategoriesName)?'':recommend.shopCategoriesName}">商铺街商铺</span>
                        </p>
                        <div>
                            <span th:text="${#strings.isEmpty(recommend.area)? '':#numbers.formatInteger(recommend.area,1)+'m²'}">92m²</span>
                            <p th:if="${recommend.shopType eq '1'}">
                                <span th:text="${#strings.isEmpty(recommend.price)or recommend.price eq '0.00'? '面议':(#strings.indexOf(recommend.price,'.') eq -1 ? recommend.price:#strings.toString(recommend.price).replaceAll('0+?$','').replaceAll('[.]$','')) }">45000元/年</span>
                                <th:bolck th:if="${!#strings.isEmpty(recommend.price) and recommend.price ne '0.00'}" th:text="${'万'}"></th:bolck>
                            </p>
                            <p th:if="${recommend.shopType eq '2' or recommend.shopType eq '3'}">
                                <span th:text="${#strings.isEmpty(recommend.price)or recommend.price eq '0.00'? '面议':(#strings.indexOf(recommend.price,'.') eq -1 ? recommend.price:#strings.toString(recommend.price).replaceAll('0+?$','').replaceAll('[.]$','')) }">45000元/年</span>
                                <th:bolck th:if="${!#strings.isEmpty(recommend.price) and recommend.price ne '0.00'}" th:text="${recommend.payment  eq '50' or recommend.payment eq '100'or recommend.payment eq '110' ?  '元/年':'元/月'}"></th:bolck>
                            </p>
                        </div>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</th:block>

<script th:inline="javascript">
    var result = [[${shop}]];
    console.log('打印结果',result)

</script>


<!--    <div class="bigImgShow">
        <div class="showImg" th:if="${!#lists.isEmpty(shop.pic)}">
            <ul>
                <li th:each="pics:${shop.pic}">
                    <img th:src="${#strings.isEmpty(pics.pic)? '': #strings.toString(pics.pic).replace('middle','big')}" th:alt="${#strings.isEmpty(shop.title)?'':shop.title}" onload="imgSize()">
                </li>
            </ul>
        </div>
        <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"/></div>
        <div class="prev"></div>
        <div class="next"></div>
    </div>-->
    <script type="text/javascript">
        photo.init();
//        calculate();
    </script>

    <div class="cl"></div>
    <!--页面底部-->
    <div th:include="fragment/fragment:: footer_detail" ></div>
    <div th:include="fragment/fragment::tongji"></div>
<!--</form>-->
<!--<div th:include="house/detail/fragment_login::login"></div>-->
<div  class="suspensionIcon" th:include="fragment/fragment::esfCommonFloatNoRight"></div>
<!--引入仿美洽按钮-->
<div th:if="${commonType eq null}" th:include="fragment/fragment::talkBtn('/im/new/service/1')"></div>
<script>
    var islogin
    $(document).ready(function () {
        islogin = $("#sessionId").val();
    });
</script>
<div class="saleHouseIndent">
    <div class="page1">
        <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
        <h1>在线支付享优惠</h1>
        <ul>
            <li><span>购房人</span><input id="name" placeholder="请填写姓名" type="text" maxlength="20" /></li>
            <li><span>手机号码</span><input type="text" id="phone" placeholder="请填写手机号码" maxlength="11" value="" /></li>
            <li class="yzm" style="border-bottom: 1px solid #ccc;"><span>请输入</span><input autocomplete="new-password" type="password" id="code" placeholder="验证码/密码" maxlength="20" style="width: 129px;" /><b class="fxe_ReSendValidateCoad">获取验证码</b><b class="fxe_validateCode" style="display: none;"></b></li>
            <!--
                            <li class="mm" style="display: none; border-bottom: 1px solid #ccc;"><span>密码</span><input type="password" id="pwd" placeholder="请填写密码" maxlength="20" /></li>
            -->
            <input type="hidden" id="userType" value="">
        </ul>
        <a>佣金95折</a>
        <dl>
            <dt></dt>
            <dd>我已经看过并同意<a href="https://info.fangxiaoer.com/Help/remission" target="_blank">《佣金减免说明书》</a></dd>
        </dl>
        <!--<span class="x"></span>-->
    </div>
    <div class="page2">
        <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
        <h1 >在线支付</h1>
        <p  th:text="${shop.title}" >国瑞城 1室 0厅 1卫 </p>
        <ul>
            <li><span>在线支付20元，约折佣340元</span></li>
            <li  th:text="${'地址：'+shop.address}">地址： 大东区东北大马路398号</li>
            <li th:text="${'经纪人：'+ (shop.houseOwner ne null and shop.houseOwner ne ''? shop.houseOwner:'') + ' '+ (shop.ownerPhone == null or #strings.toString(shop.ownerPhone) eq '' ? '':shop.ownerPhone)}">经纪人： 常先生 13840383296</li>
        </ul>
        <dl>
            <dt>支付方式： </dt>
            <dd class="weixin hover">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/weixin.png" alt="微信" /><i></i></dd>
            <dd class="alipay">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/alipay.png" alt="支付宝" /><i></i></dd>
        </dl>
        <span>支付金额：20元</span>
        <a>去支付</a>
        <!--<span class="x"></span>-->
    </div>
    <div class="page3">
        <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
        <h1>请使用<span>支付宝</span>扫描下面二维码</h1>
        <img src="" alt="二维码获取失败" />
        <!--<span class="x1"></span>-->
    </div>
    <div class="page4">
        <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
        <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/success.png" />
        <b>支付成功</b>
        <a href="https://my.fangxiaoer.com/payOrder">查看订单</a>
        <!--<span class="x1"></span>-->
    </div>

</div>
<!--<script>
    pay.init();
</script>-->
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
</body>
</html>
