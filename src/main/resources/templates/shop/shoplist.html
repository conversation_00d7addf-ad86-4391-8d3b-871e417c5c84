
<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'沈阳'+seoTitle+'商铺_'+seoTitle+'商铺出售_沈阳商铺 - 房小二网'}"></title>
    <meta name="keywords" th:content="${'沈阳'+seoTitle+'商铺,沈阳'+seoTitle+'商铺出租,沈阳商铺转让'}"/>
    <meta name="description" th:content="${'房小二网为您提供丰富全面的沈阳'+seoTitle+'商铺信息及最新沈阳'+seoTitle+'门市楼盘的出售转让信息,每天数千条真实有效信息帮您快速找到理想的商铺，购买和发布沈阳商铺出租出售信息首选房小二网。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang4/'+mobileAgent}">
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20190718" />
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2018.css?v=20200103" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/maifang_right/default2018.css?v=20200103" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20190521">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css" />
<body>
<style>
    #sortParamNew a{color:  #666;font-size: 16px;padding:  0 25px;line-height: 45px;display:  inline-block; border-right: 1px solid #eaeaea;}
    #sortParamNew a.allCodition{ padding: 0 25px;}
    #sortParamNew a.videoCodition{padding-left: 35px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/videoCodition.png) #fff 2px -1px;}
    #sortParamNew a.videoCodition.hover,.sort #sortParamNew .videoCodition:hover{background: url(https://static.fangxiaoer.com/web/images/ico/sign/videoCoditionH.png) #ff5200 2px -1px;}
    #sortParamNew a.goodCodition{padding-left: 35px; background: url(https://static.fangxiaoer.com/web/images/ico/sign/goodCodition.png) #fff 2px -1px;}
    #sortParamNew a.goodCodition.hover, .sort #sortParamNew a.goodCodition:hover{background: url(https://static.fangxiaoer.com/web/images/ico/sign/goodCoditionH.png) #ff5200 2px -1px;}
    #sortParamNew a.hover,.sort #sortParamNew a:hover{color: #ff5200;}
    .recommend dd>a.hideA p span{width: 53px}
    .rentListTese .tese div a {
        padding: 0 5px 0 20px !important;
    }
    .sub{ position: absolute; top: 0px; right: 0px; z-index: 10;}/*增加广告图标*/
</style>
<!--<form name="form1" method="post" action="" id="form1">-->

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=5"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=4,listType=0"></div>

    <div class="main">
        <div class="crumbs">您的位置：<a th:href="@{/}" target="_blank">沈阳房产网</a> &gt; <a th:href="@{'/shops/'}" target="_blank">沈阳商铺</a>
            <th:block th:each="n,stat:${needs}" th:if="${n.selected and stat.index > 0}">
                &gt;<a th:href="${'/shops/b' + n.id}" th:text="${'沈阳' + n.name}"></a>
                <th:block th:each="r, x: ${region}" th:if="${r.selected and x.index > 0}">
                    &gt;<a th:href="${'/shops/b' + n.id + '-r' + r.id}" th:text="${r.name}"></a>
                    <th:block th:if="${plate}">
                        <th:block th:each="j,m:${plate}" th:if="${j.selected and m.index > 0}">
                            &gt;<a th:href="${'/shops/b' + n.id+'-r' + r.id + '-j' + j.id}" th:text="${j.name}"></a>
                        </th:block>
                    </th:block>
                </th:block>
            </th:block>
            <th:block th:each="n,stat:${needs}" th:if="${n.selected and stat.index == 0}">
                <th:block th:each="r,x: ${region}" th:if="${r.selected and x.index > 0}">
                    &gt;<a th:href="${'/shops/r' + r.id}" th:text="${r.name}"></a>
                    <th:block th:if="${plate}">
                        <th:block th:each="j,m:${plate}" th:if="${j.selected and m.index > 0}">
                            &gt;<a th:href="${'/shops/r' + r.id + '-j' + j.id}" th:text="${j.name}"></a>
                        </th:block>
                    </th:block>
                </th:block>
            </th:block>
        </div>

        <div id="option">
            <ul>
                <!--供求-->
                <li><p>供求：</p>
                    <a th:each="n,stat:${needs}" onclick="showIndex(this);" th:href="${n.url}" th:text="${stat.index == 0 ?'不限': n.name.replace('商业','商铺')}" th:id="${'b'+n.id}" th:class="${n.selected}? 'hover':''"></a>
                </li>
                <!--区域-->
                <li class=""><p>区域：</p>
                    <a th:each="r,ri:${region}" onclick="showIndex(this);" th:href="${r.url}" th:id="'r'+${r.id}" th:if="${r.id ne '10'and r.id ne '11'}" th:class="${r.selected}? 'hover':''">
                        <th:block  th:text="${ri.index == 0?'不限':r.name}"></th:block>
                        <i th:if="${!#strings.isEmpty(r.id)}"></i> </a><br>

                </li>
                <!--板块-->
                <li th:if="${plate}" id="Search_zf" class="leibie" style="display: block;"><p style="display: none">板块：</p>
                        <a th:each="j,stat:${plate}" onclick="showIndex(this);" th:text="${stat.index ==0?'不限': j.name}" th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
                </li>
                <li  th:if="${#strings.isEmpty(typeId) or typeId eq '1'}" ><p>售价：</p>
                    <a th:each="p,stat:${price}" th:href="${p.url}" onclick="showIndex(this);" th:text="${stat.index ==0?'不限': p.name}" th:id="'p'+${p.id} " th:class="${p.selected}?'hover':''">></a>
                    <div id="Search_PriceDomOk">
                        <label><input name="minPrice" id="minPrice" maxlength="4" type="text" th:value="${minPrice}" > - <input name="maxPrice" id="maxPrice" maxlength="4" type="text" th:value="${maxPrice}" > 万 <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button"></label>
                    </div>
                </li>
                <li  th:if="${typeId ne '1'}"><p>租金：</p>
                    <a th:each="a,stat:${rental}" th:href="${a.url}" onclick="showIndex(this);" th:text="${stat.index==0?'不限':a.name}" th:id="'p'+${a.id}" th:class="${a.selected}?'hover':''">></a>
                </li>
                <li ><p>面积：</p>
                    <a th:each="a,stat:${area}" th:href="${a.url}" onclick="showIndex(this);" th:text="${stat.index==0?'不限':a.name}" th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
                    <div id="Search_BuildAreaDomOk">
                        <label><input name="minArea" id="minArea" maxlength="5" type="text" th:value="${minArea}"> - <input name="maxArea" id="maxArea" maxlength="5" type="text" th:value="${maxArea}"> ㎡ <input onclick="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button"></label>
                    </div>
                </li>
                <li><p>类型：</p>
                    <a th:each="n,stat:${type}" th:if="${stat.index != 5}" onclick="showIndex(this);" th:href="${n.url}" th:text="${stat.index == 0 ?'不限': n.name}" th:id="${'b'+n.id}" th:class="${n.selected}? 'hover':''"></a>
                </li>
                <li class="rentListTese">
                    <div class="tese" style="border: none">
                        <p>特色：</p>
                        <div>
                            <a th:id ="${u.id}" th:each="u,i:${houseTrait}" th:if="${i.index != 0}" th:href="${u.url}" th:text="${u.name}" th:class="${u.selected}? 'hover':''">></a>
                            <!--点击房源特色js-->
                            <script >
                                $(".tese a").click(function () {
//                                var teseid = $(this).attr("id").replace("tese", "");
                                    var ref = location.pathname;
                                    if(ref.indexOf('shops/') == -1) ref +='/';
                                    ref = ref.replace(/[-]*[u][0-9,]*[0-9]?$/, "");
                                    ref = ref.replace(/[-]*[n][0-9]?$/, "");
                                    if($(this).attr("id")){
                                        if ($(this).attr("class") == null) {
                                            $(this).attr("class", "hover");
                                        } else {
                                            $(this).attr("class", "");
                                        }
                                        var uIds="";
                                        $(".tese a").each(function () {
                                            if ($(this).attr("class") == "hover") {
                                                if($(this).attr("id")!=null && $(this).attr("id")!=''&& $(this).attr("id")!=undefined){
                                                    uIds += $(this).attr("id")+",";
                                                }
                                            }
                                        });
                                        uIds = uIds.replace(/[,]$/,"");
                                        if(uIds != null && uIds != undefined && uIds != ''){
                                            $(this).attr("href",ref+"-u"+uIds);
                                        }else {
                                            $(this).attr("href",ref);
                                        }
                                    }else {
                                        $(this).attr("href",ref);
                                    }
                                })
                            </script>
                        </div>
                    </div>
                </li>

            </ul>
        </div>
        <div id="option_other">
            <ul>
                <li><p>更多：</p>
                    <div class="select_box">
                        <div class="select_info" th:each="memberType,i:${memberType}" th:if="${memberType.selected}" th:text="${i.index == 0 ? '来源不限':memberType.name}">来源</div>
                        <ul>
                            <li th:each="memberType,i:${memberType}">
                                <a th:href="${memberType.url}"  onclick="showIndex(this);" th:text="${i.index ==0 ? '来源不限':memberType.name}" th:class="${memberType.selected? 'hover':''}"></a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
        <div id="option_info"  style="display: none">
            <b >已选：</b>
            <!--头部搜索-->
            <div th:if="${!#strings.isEmpty(searchKey) and !#strings.isEmpty(searchKey)}">
                <span class="condition"><th:block th:text="${searchKey}"></th:block></span>
                <i class="cleanUrl" id="clearSearchKey" th:value="${searchKey}"></i>
            </div>
            <!--供求-->
            <div th:each="needs:${needs}" th:if="${needs.selected and needs.name ne '全部'}">
                <span class="condition"><th:block th:text="${needs.name.replace('商业','商铺')}"></th:block></span>
                <i class="cleanUrl" th:value="${'b'+needs.id}"></i>
            </div>
            <!--区域-->
            <div th:each="region:${region}" th:if="${region.selected and region.name ne '全部'}">
                <span class="condition"><th:block th:text="${region.name}"></th:block></span>
                <i class="cleanUrl" th:each="plate:${plate}" th:if="${plate.selected and plate.name ne '全部'}"
                   th:value="${'j'+plate.id+'-r'+region.id}"></i>
                <i class="cleanUrl" th:each="plate:${plate}" th:if="${plate.selected and plate.name eq '全部'}"
                   th:value="${'r'+region.id}"></i>
            </div>
            <!--板块-->
            <div th:each="plate:${plate}" th:if="${plate.selected and plate.name ne '全部'}">
                <span class="condition"><th:block th:text="${plate.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'j'+plate.id}"></i>
            </div>
            <!--售价-->
            <div th:each="price:${price}" th:if="${price.selected and price.name ne '全部'}">
                <span class="condition"><th:block th:text="${price.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'p'+price.id}"></i>
            </div>
            <!--自定义售价-->
            <div th:if="${!#strings.isEmpty(minPrice) or !#strings.isEmpty(maxPrice)}">
                <span class="condition">
                    <th:block th:if="${#strings.isEmpty(minPrice)}" th:text="${'小于等于'+maxPrice+'万元'}"></th:block>
                    <th:block th:if="${#strings.isEmpty(maxPrice)}" th:text="${'大于等于'+minPrice+'万元'}"></th:block>
                    <th:block th:if="${!#strings.isEmpty(maxPrice) and !#strings.isEmpty(minPrice)}" th:text="${minPrice+'-'+maxPrice+'万元'}"></th:block>
                </span>
                <i class="cleanUrl" th:if="${#strings.isEmpty(minPrice)}" th:value="${'hp'+maxPrice}"></i>
                <i class="cleanUrl" th:if="${#strings.isEmpty(maxPrice)}" th:value="${'lp'+minPrice}"></i>
                <i class="cleanUrl" th:if="${!#strings.isEmpty(maxPrice) and !#strings.isEmpty(minPrice)}" th:value="${'lp'+minPrice+'-'+'hp'+maxPrice}"></i>
            </div>
            <!--租金-->
            <div th:each="rental:${rental}" th:if="${rental.selected and rental.name ne '全部'}">
                <span class="condition"><th:block th:text="${rental.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'p'+rental.id}"></i>
            </div>
            <!--面积-->
            <div th:each="area:${area}" th:if="${area.selected and area.name ne '全部'}">
                <span class="condition"><th:block th:text="${area.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'a'+area.id}"></i>
            </div>
            <!--自定义售价-->
            <div th:if="${!#strings.isEmpty(minArea) or !#strings.isEmpty(maxArea)}">
                <span class="condition">
                    <th:block th:if="${#strings.isEmpty(minArea)}" th:text="${'小于等于'+maxArea+'㎡'}"></th:block>
                    <th:block th:if="${#strings.isEmpty(maxArea)}" th:text="${'大于等于'+minArea+'㎡'}"></th:block>
                    <th:block th:if="${!#strings.isEmpty(maxArea) and !#strings.isEmpty(minArea)}" th:text="${minArea+'-'+maxArea+'㎡'}"></th:block>
                </span>
                <i class="cleanUrl" th:if="${#strings.isEmpty(minArea)}" th:value="${'ha'+maxArea}"></i>
                <i class="cleanUrl" th:if="${#strings.isEmpty(maxArea)}" th:value="${'la'+minArea}"></i>
                <i class="cleanUrl" th:if="${!#strings.isEmpty(maxArea) and !#strings.isEmpty(minArea)}" th:value="${'la'+minArea+'-'+'ha'+maxArea}"></i>
            </div>
            <!--类型-->
            <div th:each="type:${type}" th:if="${type.selected and type.name ne '全部'}">
                <span class="condition"><th:block th:text="${type.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'t'+type.id}"></i>
            </div>
            <!--来源-->
            <div th:each="memberType:${memberType}" th:if="${memberType.selected and memberType.name ne '全部'}">
                <span class="condition"><th:block th:text="${memberType.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'i'+memberType.id}"></i>
            </div>
            <a href="/shops/" class="clean">清空筛选条件</a>
            <script>
                /*判断是否显示条件栏目*/
                $(function () {
                    if($(".condition").text() != ""){
                        $("#option_info").css("display","block");
                    }
                })
                /*去掉条件标签*/
                $(".cleanUrl").click(function () {
                    var oldUrl = location.pathname;
                    if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
                        var moreUrl ="-" + $(this).attr("value");
                    }else{ var moreUrl = $(this).attr("value");}

                    var newUrl = oldUrl.replace(moreUrl,"");
                    window.location.href = newUrl;
                    //跳到newUrl指定链接
                });
                //清除头部搜索key
                $("#clearSearchKey").click(function () {
                    var nowUrl = location.pathname;
                    var searchTitle = nowUrl.indexOf('search');//只有手输内容搜索
                    var newUrl = nowUrl.substring(0,searchTitle);
                    window.location.href = newUrl;
                });
            </script>
        </div>
        <script type="text/javascript">
            //筛选的确定按钮显示隐藏
            function price(priceIdName) {
                if($("#"+priceIdName).length >0){
                    $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
                    $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
                    var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
                    var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
                    if (num1 == "" && num2 != "") {
                        $("#" + priceIdName + " input").eq(0).val("0");
                        $("#" + priceIdName + " input").eq(2).show();
                    } else if (num2 == "" && num1 != "") {
                        $("#" + priceIdName + " input").eq(2).show();
                    } else if (num1 != "" || num2 != "") {
                        $("#" + priceIdName + " input").eq(2).show();
                    } else {
                        $("#" + priceIdName + " input").eq(2).hide();
                    }
                }
            }

            price("Search_BuildAreaDomOk");
            price("Search_PriceDomOk");
            $("#Search_PriceDomOk input").keyup(function () {
                price("Search_PriceDomOk");
            })
            $("#Search_BuildAreaDomOk input").keyup(function () {
                price("Search_BuildAreaDomOk");
            })
            $("#Search_PriceDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                    // $("#Search_Btn_Search1").click()
                }
            });
            $("#Search_BuildAreaDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                    $("#Search_Btn_Searchs").click()
                }
            });
            //此段js用于手填面积和价格连接拼接处理
            function __doPostBack(pager1, page) {
                var url = window.location.pathname;
                if (pager1 == "Search$Btn_Search1") {
                    var priceBegin = $("#minPrice").val();
                    var priceEnd = $("#maxPrice").val();
                    var ref1 = url.replace(/-lp[0-9]\d*/,''); //k最小值
                    var ref2 = ref1.replace(/-hp[0-9]\d*/,'');  //x最大值
                    if (parseInt(priceEnd) < parseInt(priceBegin)){
                        priceEnd = [priceBegin,priceBegin=priceEnd][0];
                    }
                    if(priceBegin != "" && priceBegin != 0)
                        ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-lp" + priceBegin;
                    if(priceEnd != "")
                        ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') +"-hp"+ priceEnd;
                    location.href = ref2;
                }
                if (pager1 == "Search$Btn_Searchs") {
                    var areaBegin = $("#minArea").val();
                    var areaEnd = $("#maxArea").val();
                    var ref1 = url.replace(/-la[0-9]\d*/,''); //y最小值
                    var ref2 = ref1.replace(/-ha[0-9]\d*/,'');  //e最大值
                    if (parseInt(areaEnd) < parseInt(areaBegin)){
                        areaEnd = [areaBegin,areaBegin=areaEnd][0];
                    }
                    if(areaBegin != "" && areaBegin != 0)
                        ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-la" + areaBegin;
                    if(areaEnd != "")
                        ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') +"-ha" + areaEnd;
                    location.href = ref2;
                }
            }
        </script>
        <div class="cl"></div>
        <div id="main">
            <div class="bannerLeft">

                <div id="left" class="longLeft" style="border: none">
                    <!--列表页新增广告-->
<!--                    <div th:include="secondhouse/fragment::advert_recommand"></div>-->
                    <div style="border: 1px solid #eaeaea">
                    <div style="border-bottom: 1px solid #eee;">
                        <p id="screening_Items" style="margin-top: 12px;margin-right: 15px">
                            <a id="originalOrder" href="" th:class="${orderKey eq '0'?'hover':''}">综合排序</a>
                            <a  id="2" onclick="changeUrl(this)" th:class="${orderKey eq '1'? 'sort_jg up':(orderKey eq '2'?'sort_jg down':'')}" href="">价格</a>
                            <a  id="4" onclick="changeUrl(this)" th:class="${orderKey eq '3' or orderKey eq '4'?(orderKey eq '3'? 'sort_jg up':'sort_jg down') :''}" href="">面积</a>
                        </p>
                        <p id="sortParamNew">
<!--                            <a id="" href="/shops/-W2" class="hover">全部</a>-->
                            <a id="2" href="" class="allCodition sortParamNewA" style="padding: 0 25px;">全部</a>
<!--                            <a id="4" href="" class="videoCodition sortParamNewA">视频看房</a>-->
                            <!--<a id="6" href="" class="goodCodition">优质好房</a>-->
                            <script>
                                $(document).ready(function(){
                                    var ref = location.pathname;
                                    var y = ref.split('/shops/');
                                    var x = y.length > 1 ? y[1] : "";
                                    if(x.indexOf('W') != -1){
                                        var num = x.split('W')[1].substring(0,1);
                                        if(num == 2 || num == 4 || num == 6){
                                            $("#sortParamNew a[id ='"+ num+"']").addClass("hover");
                                        }else{
                                            if(num%2 == 1){
                                                $("#sortParamNew a[id ='"+ num+"']").addClass("sort_jg");
                                                $("#sortParamNew a[id ='"+ num+"']").attr("id",num-1);
                                            }else{
                                                num = parseInt(num) +1;
                                                $("#sortParamNew a[id ='"+ num+"']").addClass("sort_jg");
                                            }
                                        }
                                    }else{
                                        $("#sortParamNew a:eq(0)").addClass("hover");
                                    }
                                    var ref2 = ref.replace(/-W[0-6]/,'').replace(/-n[0-9]/,'');
                                    $("#sortParamNew a.sortParamNewA").each(function () {
                                        var ids = $(this).attr("id");
                                        var ref3 = ref2 + '-W' + ids;
                                        $(this).attr("href",ref3);
                                    });
                                });
                            </script>
                        </p>
                    </div>
                    <script th:inline="javascript">
                        var pageUrl = [[${pageUrl}]];
                        var orderKey = [[${orderKey}]];
                        pageUrl = pageUrl.replace(/-?o[0-9]/,'');
                        $("#originalOrder").attr("href", pageUrl+"-o0");
                        function changeUrl(obj) {
                            if(orderKey != $(obj).attr("id")) {
                                orderKey =  $(obj).attr("id");
                            }else {
                                orderKey =  parseInt($(obj).attr("id"))-1;
                            }
                            if(typeof(orderKey)=="undefined"){
                                $(obj).attr("href", pageUrl);
                            }
                            $(obj).attr("href", pageUrl+'-o'+orderKey);
                        }
                    </script>

                    <div class="cl"></div>
                    <div class="contentMain" id="saleCol">
                        <!--以下为list形式-->
                        <div class="warning" th:if="${#lists.isEmpty(shop) and sw ne '2'}" >
                            <p>
                                很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                                懒得搜索？！<a th:href="@{'/helpSearch?ids=4'}" rel="8" dir="3" target="_blank">点击免费发布商铺租售方案>></a>
                            </p>
                        </div>
                        <div id=replie class="house_left" th:if="${sw ne '2'}">
<!--                            <div class="inf" th:if="${!#lists.isEmpty(shop)}" th:each="shop:${shop}" th:onclick="${'window.open(''/shop/'+shop.shopId+'.htm'')'}">-->
                            <div class="inf" th:if="${!#lists.isEmpty(shop)}" th:each="shop:${shop}">
                                 <a th:href="${'/shop/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
    <!--                                <i th:if="${'0,1,2,3,4'.indexOf(shop.picNum) eq -1}" th:text="${shop.picNum}"></i>-->
                                     <span class="sub" th:if="${#strings.toString(shop.auction) eq '1'}">
                                        <img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" />
                                     </span>
                                     <span class="sub" th:if="${#strings.toString(shop.auction) ne '1' && shop.stickOrder eq '-1'}">
                                        <img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" />
                                     </span>

                                    <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': shop.pic}" th:alt="${#strings.isEmpty(shop.title)? '':shop.title}">
    <!--                                 <s class="videoIcon" th:if="${shop.mediaID ne null}"></s>-->
                                     <!--VR and 视频都存在 -->
                                     <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                                         <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                                         <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                                     </s>
                                     <!--VR存在 -->
                                     <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                                         <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                                     </s>
                                     <!--视频存在 -->
                                     <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                                         <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                                     </s>
                                 </a>
                                &nbsp;&nbsp;&nbsp;&nbsp;<div class="infCtn">
                                <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)?'':'/shop/'+shop.shopId+'.htm'}" >
                                    <div th:text="${#strings.isEmpty(shop.title)? '':shop.title}" th:title="${#strings.isEmpty(shop.title)? '':shop.title}"></div>
                                    <i class="listIconBidPrice" th:if="${#strings.toString(shop.auction) eq '1'}"></i>
                                    <i class="listIconIstop" th:if="${#strings.toString(shop.auction) ne '1' && shop.stickOrder eq '-1'}"></i>
                                </a>

                                <div  class="fourSpan">
                                    <span th:text="${#strings.isEmpty(shop.area)? '':#numbers.formatInteger(shop.area,1)+'m²'}"></span>
                                    <span th:text="${#strings.isEmpty(shop.shopCategoriesName)?'':shop.shopCategoriesName}"></span>
                                </div>
                                <p  class="houseAddress"  th:if="${!#strings.isEmpty(shop.regionName)  and !#strings.isEmpty(shop.plateName)}">
                                    <s>
                                        <a th:if="${!#strings.isEmpty(shop.regionName)}" th:href="${'/shops/-r'+shop.regionId}" th:text="${shop.regionName}"></a>
                                        <a th:if="${!#strings.isEmpty(shop.plateName)}" th:href="${'/shops/j'+shop.plateId+'-r'+shop.regionId}" th:text="${'-'+shop.plateName}"></a>
                                        <s th:if="${!#strings.isEmpty(shop.address)}" th:text="${'-'+shop.address}" class="shop_address_"></s>
                                    </s>
                                </p>
                                <div class="bottomtext">
                                    <div  class="houseItemIcon">
                                        <th:block th:if="${shop.houseTrait ne null and shop.houseTrait ne ''}">
                                            <span th:class="${'tese_'+i.count}" th:each="item,i:${#strings.toString(shop.houseTrait).split(',')}" th:if="${i.count le 3}" th:text="${item}" ></span>
                                        </th:block>
                                    </div>
                                    <span class="personShow">
                                     <div th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '个人'}">
                                          <img  src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
                                          <i th:text="${shop.memberType}"></i>
                                     </div>
                                     <div th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '经纪人'}">
                                          <div class="agentShowImg"><img th:if="${!#strings.isEmpty(shop.Avatar)}" th:src="${shop.Avatar}" alt=""></div>
                                            <div class="agentShowImg"><img th:if="${#strings.isEmpty(shop.Avatar)}" src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt=""></div>
                                         <i th:text="${#strings.isEmpty(shop.houseOwner)?'':shop.houseOwner}"></i>
                                         <i th:if="${!#strings.isEmpty(shop.IntermediaryName)}" th:text="${shop.IntermediaryName}"></i>
                                     </div>
                                </span>
                                </div>
                                    <!--<th:block th:text="${#strings.isEmpty(shop.tel)?(#strings.isEmpty(shop.ownerPhone)? '': shop.ownerPhone):shop.tel}"></th:block>-->
                            </div>
                                <div class="infRight">
                                    <p class="infRightPrise" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}"  >
                                        <th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议': (#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>
                                        <i th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00'?'':'万'}"></i>
                                        <!--<span th:text="售">售</span>-->
                                    </p>
                                    <p class="infRightPrise" th:if="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '2'}">
                                        <th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price :#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}"></th:block>
                                        <i th:text="${!#strings.isEmpty(shop.price) and  shop.price eq '0.00'?'':((shop.payment eq '100' or shop.payment eq '50'or shop.payment eq '110')? '元/年':'元/月')}"></i>
                                        <!--<b th:text="${!#strings.isEmpty(shop.price) and (shop.payment eq '100' or shop.payment eq '50')? '元/年':'元/月'}"></b>-->
                                        <!--<span th:text="租">租</span>-->
                                    </p>
                                    <p class="infRightPrise" th:if="${!#strings.isEmpty(shop.shopType) and   shop.shopType eq '3'}">
                                        <th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}" ></th:block>
                                        <i th:text="${!#strings.isEmpty(shop.price)  and shop.price eq '0.00'?'': ((shop.payment eq '100' or shop.payment eq '50'or shop.payment eq '110')? '元/年':'元/月')}"></i>
                                        <!--<span th:text="兑">兑</span>-->
                                    </p>
                                    <p><b th:text="${#strings.isEmpty(shop.unitPrice)?'':shop.unitPrice}">8400.00元/平</b></p>
                                    <!--<a class="checkHouse" target="_blank" th:href="'/dealShops/-r'+${shop.regionId}">查看同区域成交房源&gt;</a>-->
<!--                                    <a class="checkHouse" target="_blank" th:href="'/shops/-r'+${shop.regionId}">查看同区域房源 &gt;</a>-->
                                </div>
                            </div>
                         </div>
                        <div id=reprow class="list" th:if="${sw eq '2'}">
                            <div class="warning" th:if="${#lists.isEmpty(shop)}">
                                <p>
                                    很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>
                                    懒得搜索？！<a th:href="@{'/helpSearch?ids=4'}" rel="8" dir="3" target="_blank">点击免费发布商铺租售方案>></a>
                                </p>
                            </div>
                            <div class="house shopHouse" th:each="sh:${shop}">
                                <a class="house" th:href="${'/shop/'+sh.shopId+'.htm'}"  target="_blank">
                                    <div  class="ico">
                                        <span class="ico_yongjin " th:if="${sh.isXiQue eq '1'}">佣金95折</span>
                                        <span class="ico_zdbg" th:if="${sh.stickOrder eq '-1'}"></span>
                                        <span class="jing" th:if="${#strings.toString(sh.auction) eq '1'}">精</span>
                                    </div>
                                    <img th:src="${#strings.isEmpty(sh.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sh.pic}" th:alt="${sh.title}" />
                                    <p>
                                        <text  th:text="${#strings.isEmpty(sh.area)? '':#numbers.formatInteger(sh.area,1)+'m²'}" ></text>
                                        <text style="float: right" th:text="${#strings.isEmpty(sh.shopCategoriesName)?'':sh.shopCategoriesName}" ></text>
                                    </p>
                                    <p class="sy_price"th:if="${!#strings.isEmpty(sh.shopType) and  sh.shopType eq '1'}"  >
                                        <span>
                                            <th:block th:text="${#strings.isEmpty(sh.price) or sh.price eq '0.00' ?'面议': (#strings.indexOf(sh.price,'.') eq -1 ? sh.price:#strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>
                                            <i th:text="${#strings.isEmpty(sh.price) or sh.price eq '0.00'?'':'万'}"></i>
                                        </span>
                                        <!--<b th:text="售">售</b>-->
                                    </p>
                                    <p class="sy_price" th:if="${!#strings.isEmpty(sh.shopType) and sh.shopType eq '2'}">
                                        <span>
                                            <th:block th:text="${#strings.isEmpty(sh.price) or sh.price eq '0.00' ?'面议':(#strings.indexOf(sh.price,'.') eq -1 ? sh.price:#strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>
                                            <i th:text="${!#strings.isEmpty(sh.price) and  sh.price eq '0.00'?'':((sh.payment eq '100' or sh.payment eq '50'or sh.payment eq '110')? '元/年':'元/月')}"></i>
                                        </span>
                                        <!--<b th:text="租">租</b>-->
                                    </p>
                                    <p class="sy_price"  th:if="${!#strings.isEmpty(sh.shopType) and   sh.shopType eq '3'}">
                                        <span >
                                            <th:block  th:text="${#strings.isEmpty(sh.price) or sh.price eq '0.00' ?'面议':(#strings.indexOf(sh.price,'.') eq -1 ? sh.price:#strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$',''))}" ></th:block>
                                            <i th:text="${!#strings.isEmpty(sh.price) and !#strings.isEmpty(sh.paymentName) and sh.price eq '0.00'?'': (sh.payment eq '100' or sh.payment eq '50'or sh.payment eq '110' ? '元/年':'元/月')}"></i>
                                        </span>
                                        <!--<b th:text="兑">兑</b>-->
                                    </p>
                                    <p>
                                        <span style="float: left;" >
                                            <th:block  th:text="${#strings.abbreviate(sh.title,28)}"></th:block>
                                        </span>
                                    </p>
                                    <p><span th:text="${sh.regionName}"></span> <th:block th:text="${sh.plateName}"></th:block> </p>
                                </a>
                            </div>
                        </div>
                        <div class="cl"></div>
                        <!--<script type="text/javascript">-->
                            <!--var ary = location.href.split("&");-->
                            <!--jQuery(".picScroll-left").slide({ titCell: ".hd ul", mainCell: ".bd ul", autoPage: true, effect: "leftLoop", autoPlay: true, scroll: 1, vis: 5 });-->
                            <!--$(function () {-->
                                <!--$(".bd li").mouseover(function () {-->
                                    <!--$(this).find("p").stop()-->
                                    <!--$(this).find("p").animate({ bottom: '0px' }, 300)-->
                                <!--})-->
                                <!--$(".bd li").mouseout(function () {-->
                                    <!--$(this).find("p").stop()-->
                                    <!--$(this).find("p").animate({ bottom: '-200px' }, 300)-->
                                <!--})-->
                            <!--})-->
                        <!--</script>-->
                    </div>
                    <div class="cl"></div>
                    <div class="page">
                        <div th:include="fragment/page :: page"></div>
                    </div>
                    </div>
                </div>
            </div>
            <div id="right"  class="shortRight" >
                <!--<div class="saleHouse">-->
                    <!--<a href="/shopsell" target="_blank">我要卖房</a>-->
                <!--</div>-->
                <!--小区专家-->
                <div th:include="secondhouse/fragment::plotexpert"></div>
                <!--房产快搜-->
                <!--<div class="gksou">-->
                    <!--<div class="gktp"><span></span>房产快搜 <a href="/fastSeek" target="_blank">详情>></a></div>-->
                    <!--<div class="gkmn">-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94587.htm" target="_blank"><i>1</i><em>房产过户</em></a>-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94586.htm" target="_blank"><i>2</i><em>购房流程</em></a>-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94572.htm" target="_blank"><i>3</i><em>征信查询</em></a>-->
                        <!--&lt;!&ndash;<a href="/fastSeek#edu" target="_blank"><i>4</i><em>沈阳推荐小学</em></a>-->
                        <!--<a href="/fastSeek#edu" target="_blank"><i>5</i><em>沈阳推荐中学</em></a>&ndash;&gt;-->
                    <!--</div>-->
                <!--</div>-->
                <!--列表页右侧求租求购-->
                <div class="rightQzQgBtn">
                    <a href="/shopsell" target="_blank"><i class="rightQzQgBtn-icon3"></i>我要出售</a>
                    <a th:href="@{'/helpSearch?ids=4'}" rel="8" dir="3" target="_blank"><i class="rightQzQgBtn-icon4"></i>买商业</a>
                    <!--<a href="/shoprent/" target="_blank"><i class="rightQzQgBtn-icon3"></i>我要出租</a>
                    <a href="/shopsell/" target="_blank"><i class="rightQzQgBtn-icon4"></i>我要出售</a>-->
                </div>
                <div th:if="${!#lists.isEmpty(advert.shopAd1)}">
                    <th:block th:each="shopAd1,i:${advert.shopAd1}">
                        <div  class="shop_ad_pic_url">
                        <a   th:if="${#strings.toString(shopAd1.url).indexOf('http') ne -1}" th:href="${shopAd1.url}" target="_blank">
                            <img width="100%"  class="shopAd1_link_" th:src="${shopAd1.image}" th:alt="${shopAd1.projectName}">
                        </a>
                        <img class="shopAd1_link_" th:if="${#strings.toString(shopAd1.url).indexOf('http') eq -1}" width="100%" th:src="${shopAd1.image}" th:alt="${shopAd1.projectName}">
                        </div>
                    </th:block>
                </div>
                <div th:if="${!#lists.isEmpty(advert.shopAd2)}" class="shop_ad_pic_url">
                    <a th:each="shopAd2,i:${advert.shopAd2}" th:href="${shopAd2.url}" target="_blank">
                        <img class="shopAd1_link_" width="100%" th:src="${shopAd2.image}" th:alt="${shopAd2.projectName}">
                    </a>
                </div>
            </div>
        </div>
        <div class="cl"></div>
        <div class="cl"></div>
        <!--底部1-->
        <div th:include="fragment/fragment:: footer_list"></div>
        <div th:include="fragment/fragment::tongji"></div>
    </div>
<!--</form>-->
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>


</body>
</html>


