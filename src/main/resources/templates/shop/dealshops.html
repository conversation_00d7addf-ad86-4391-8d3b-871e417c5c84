<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'沈阳'+seoTitle+'商铺_'+seoTitle+'商铺出售_沈阳商铺 - 房小二网'}"></title>
    <meta name="keywords" th:content="${'沈阳'+seoTitle+'商铺,沈阳'+seoTitle+'商铺出租,沈阳商铺转让'}"/>
    <meta name="description" th:content="${'房小二网为您提供丰富全面的沈阳'+seoTitle+'商铺信息及最新沈阳'+seoTitle+'门市楼盘的出售转让信息,每天数千条真实有效信息帮您快速找到理想的商铺，购买和发布沈阳商铺出租出售信息首选房小二网。'}"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/fang4">
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2017.css" />

    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/contractList.css"/>
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
<body>

<form name="form1" method="post" action="" id="form1">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=5"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=12"></div>

    <div class="main">
        <div class="crumbs">您的位置：<a th:href="@{/}" target="_blank">沈阳房产网</a> &gt; <a th:href="@{'/shops/'}" target="_blank">沈阳商铺</a> &gt;<a href="/dealShops/">成交房源</a></div>
        <div id="option">
            <ul>
                <!--供求-->
                <li><p>供求：</p>
                    <a th:each="n,stat:${needs}" onclick="showIndex(this);" th:href="${n.url}" th:text="${stat.index == 0 ?'不限': n.name.replace('商业','商铺')}" th:id="${'b'+n.id}" th:class="${n.selected}? 'hover':''"></a>
                </li>
                <!--区域-->
                <li class=""><p>区域：</p>
                    <a th:each="r,ri:${region}" onclick="showIndex(this);" th:href="${r.url}" th:id="'r'+${r.id}" th:if="${r.id ne '10'and r.id ne '11'}" th:class="${r.selected}? 'hover':''">
                        <th:block  th:text="${ri.index == 0?'不限':r.name}"></th:block>
                        <i th:if="${!#strings.isEmpty(r.id)}"></i> </a><br>

                </li>
                <!--板块-->
                <li th:if="${plate}" id="Search_zf" class="leibie" style="display: block;"><p style="display: none">板块：</p>
                    <a th:each="j,stat:${plate}" onclick="showIndex(this);" th:text="${stat.index ==0?'不限': j.name}" th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
                </li>
                <li  th:if="${#strings.isEmpty(typeId) or typeId eq '1'}" ><p>售价：</p>
                    <a th:each="p,stat:${price}" th:href="${p.url}" onclick="showIndex(this);" th:text="${stat.index ==0?'不限': p.name}" th:id="'p'+${p.id} " th:class="${p.selected}?'hover':''">></a>
                    <div id="Search_PriceDomOk">
                        <label><input name="minPrice" id="minPrice" maxlength="4" type="text" th:value="${minPrice}" > - <input name="maxPrice" id="maxPrice" maxlength="4" type="text" th:value="${maxPrice}" > 万 <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button"></label>
                    </div>
                </li>
                <li  th:if="${typeId ne '1'}"><p>租金：</p>
                    <a th:each="a,stat:${rental}" th:href="${a.url}" onclick="showIndex(this);" th:text="${stat.index==0?'不限':a.name}" th:id="'p'+${a.id}" th:class="${a.selected}?'hover':''">></a>
                </li>
                <li ><p>面积：</p>
                    <a th:each="a,stat:${area}" th:href="${a.url}" onclick="showIndex(this);" th:text="${stat.index==0?'不限':a.name}" th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
                    <div id="Search_BuildAreaDomOk">
                        <label><input name="minArea" id="minArea" maxlength="5" type="text" th:value="${minArea}"> - <input name="maxArea" id="maxArea" maxlength="5" type="text" th:value="${maxArea}"> ㎡ <input onclick="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button"></label>
                    </div>
                </li>
                <li><p>类型：</p>
                    <a th:each="n,stat:${type}" th:if="${stat.index != 5}" onclick="showIndex(this);" th:href="${n.url}" th:text="${stat.index == 0 ?'不限': n.name}" th:id="${'b'+n.id}" th:class="${n.selected}? 'hover':''"></a>
                </li>
            </ul>
        </div>
        <!--<div id="option_other">
            <ul>
                <li><p>更多：</p>
                    <div class="select_box">
                        <div class="select_info" th:each="memberType,i:${memberType}" th:if="${memberType.selected}" th:text="${i.index == 0 ? '来源不限':memberType.name}">来源</div>
                        <ul>
                            <li th:each="memberType,i:${memberType}">
                                <a th:href="${memberType.url}"  onclick="showIndex(this);" th:text="${i.index ==0 ? '来源不限':memberType.name}" th:class="${memberType.selected? 'hover':''}"></a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>-->
        <div id="option_info"  style="display: none">
            <b >已选：</b>
            <!--供求-->
            <div th:each="needs:${needs}" th:if="${needs.selected and needs.name ne '全部'}">
                <span class="condition"><th:block th:text="${needs.name.replace('商业','商铺')}"></th:block></span>
                <i class="cleanUrl" th:value="${'b'+needs.id}"></i>
            </div>
            <!--区域-->
            <div th:each="region:${region}" th:if="${region.selected and region.name ne '全部'}">
                <span class="condition"><th:block th:text="${region.name}"></th:block></span>
                <i class="cleanUrl" th:each="plate:${plate}" th:if="${plate.selected and plate.name ne '全部'}"
                   th:value="${'j'+plate.id+'-r'+region.id}"></i>
                <i class="cleanUrl" th:each="plate:${plate}" th:if="${plate.selected and plate.name eq '全部'}"
                   th:value="${'r'+region.id}"></i>
            </div>
            <!--板块-->
            <div th:each="plate:${plate}" th:if="${plate.selected and plate.name ne '全部'}">
                <span class="condition"><th:block th:text="${plate.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'j'+plate.id}"></i>
            </div>
            <!--售价-->
            <div th:each="price:${price}" th:if="${price.selected and price.name ne '全部'}">
                <span class="condition"><th:block th:text="${price.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'p'+price.id}"></i>
            </div>
            <!--自定义售价-->
            <div th:if="${!#strings.isEmpty(minPrice) or !#strings.isEmpty(maxPrice)}">
                <span class="condition">
                    <th:block th:if="${#strings.isEmpty(minPrice)}" th:text="${'小于等于'+maxPrice+'万元'}"></th:block>
                    <th:block th:if="${#strings.isEmpty(maxPrice)}" th:text="${'大于等于'+minPrice+'万元'}"></th:block>
                    <th:block th:if="${!#strings.isEmpty(maxPrice) and !#strings.isEmpty(minPrice)}" th:text="${minPrice+'-'+maxPrice+'万元'}"></th:block>
                </span>
                <i class="cleanUrl" th:if="${#strings.isEmpty(minPrice)}" th:value="${'hp'+maxPrice}"></i>
                <i class="cleanUrl" th:if="${#strings.isEmpty(maxPrice)}" th:value="${'lp'+minPrice}"></i>
                <i class="cleanUrl" th:if="${!#strings.isEmpty(maxPrice) and !#strings.isEmpty(minPrice)}" th:value="${'lp'+minPrice+'-'+'hp'+maxPrice}"></i>
            </div>
            <!--租金-->
            <div th:each="rental:${rental}" th:if="${rental.selected and rental.name ne '全部'}">
                <span class="condition"><th:block th:text="${rental.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'p'+rental.id}"></i>
            </div>
            <!--面积-->
            <div th:each="area:${area}" th:if="${area.selected and area.name ne '全部'}">
                <span class="condition"><th:block th:text="${area.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'a'+area.id}"></i>
            </div>
            <!--自定义售价-->
            <div th:if="${!#strings.isEmpty(minArea) or !#strings.isEmpty(maxArea)}">
                <span class="condition">
                    <th:block th:if="${#strings.isEmpty(minArea)}" th:text="${'小于等于'+maxArea+'㎡'}"></th:block>
                    <th:block th:if="${#strings.isEmpty(maxArea)}" th:text="${'大于等于'+minArea+'㎡'}"></th:block>
                    <th:block th:if="${!#strings.isEmpty(maxArea) and !#strings.isEmpty(minArea)}" th:text="${minArea+'-'+maxArea+'㎡'}"></th:block>
                </span>
                <i class="cleanUrl" th:if="${#strings.isEmpty(minArea)}" th:value="${'ha'+maxArea}"></i>
                <i class="cleanUrl" th:if="${#strings.isEmpty(maxArea)}" th:value="${'la'+minArea}"></i>
                <i class="cleanUrl" th:if="${!#strings.isEmpty(maxArea) and !#strings.isEmpty(minArea)}" th:value="${'la'+minArea+'-'+'ha'+maxArea}"></i>
            </div>
            <!--类型-->
            <div th:each="type:${type}" th:if="${type.selected and type.name ne '全部'}">
                <span class="condition"><th:block th:text="${type.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'t'+type.id}"></i>
            </div>
            <!--来源-->
            <div th:each="memberType:${memberType}" th:if="${memberType.selected and memberType.name ne '全部'}">
                <span class="condition"><th:block th:text="${memberType.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'i'+memberType.id}"></i>
            </div>
            <a href="/dealShops/" class="clean">清空筛选条件</a>
            <script>
                /*判断是否显示条件栏目*/
                $(function () {
                    if($(".condition").text() != ""){
                        $("#option_info").css("display","block");
                    }
                })
                /*去掉条件标签*/
                $(".cleanUrl").click(function () {
                    var oldUrl = location.pathname;
                    if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
                        var moreUrl ="-" + $(this).attr("value");
                    }else{ var moreUrl = $(this).attr("value");}

                    var newUrl = oldUrl.replace(moreUrl,"");
                    window.location.href = newUrl;
                    //跳到newUrl指定链接
                });
            </script>
        </div>
        <script type="text/javascript">
            //筛选的确定按钮显示隐藏
            function price(priceIdName) {
                if($("#"+priceIdName).length >0){
                    $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
                    $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
                    var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
                    var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
                    if (num1 == "" && num2 != "") {
                        $("#" + priceIdName + " input").eq(0).val("0");
                        $("#" + priceIdName + " input").eq(2).show();
                    } else if (num2 == "" && num1 != "") {
                        $("#" + priceIdName + " input").eq(2).show();
                    } else if (num1 != "" || num2 != "") {
                        $("#" + priceIdName + " input").eq(2).show();
                    } else {
                        $("#" + priceIdName + " input").eq(2).hide();
                    }
                }
            }

            price("Search_BuildAreaDomOk");
            price("Search_PriceDomOk");
            $("#Search_PriceDomOk input").keyup(function () {
                price("Search_PriceDomOk");
            })
            $("#Search_BuildAreaDomOk input").keyup(function () {
                price("Search_BuildAreaDomOk");
            })
            $("#Search_PriceDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                    // $("#Search_Btn_Search1").click()
                }
            });
            $("#Search_BuildAreaDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                    $("#Search_Btn_Searchs").click()
                }
            });
            //此段js用于手填面积和价格连接拼接处理
            function __doPostBack(pager1, page) {
                var url = window.location.pathname;
                if (pager1 == "Search$Btn_Search1") {
                    var priceBegin = $("#minPrice").val();
                    var priceEnd = $("#maxPrice").val();
                    var ref1 = url.replace(/-lp[0-9]\d*/,''); //k最小值
                    var ref2 = ref1.replace(/-hp[0-9]\d*/,'');  //x最大值
                    if (parseInt(priceEnd) < parseInt(priceBegin)){
                        priceEnd = [priceBegin,priceBegin=priceEnd][0];
                    }
                    if(priceBegin != "" && priceBegin != 0)
                        ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-lp" + priceBegin;
                    if(priceEnd != "")
                        ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') +"-hp"+ priceEnd;
                    location.href = ref2;
                }
                if (pager1 == "Search$Btn_Searchs") {
                    var areaBegin = $("#minArea").val();
                    var areaEnd = $("#maxArea").val();
                    var ref1 = url.replace(/-la[0-9]\d*/,''); //y最小值
                    var ref2 = ref1.replace(/-ha[0-9]\d*/,'');  //e最大值
                    if (parseInt(areaEnd) < parseInt(areaBegin)){
                        areaEnd = [areaBegin,areaBegin=areaEnd][0];
                    }
                    if(areaBegin != "" && areaBegin != 0)
                        ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-la" + areaBegin;
                    if(areaEnd != "")
                        ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') +"-ha" + areaEnd;
                    location.href = ref2;
                }
            }
        </script>
        <div class="cl"></div>
        <div id="main">
            <div id="left">
                <div class="sort">
                    <p id="sortParam">
                        <a id="originalOrder" href="" th:class="${orderKey eq '0'?'hover':''}">综合排序</a>
                        <a  id="2" onclick="changeUrl(this)" th:class="${orderKey eq '1'? 'sort_jg up':(orderKey eq '2'?'sort_jg down':'')}" href="">价格</a>
                        <a  id="4" onclick="changeUrl(this)" th:class="${orderKey eq '3' or orderKey eq '4'?(orderKey eq '3'? 'sort_jg up':'sort_jg down') :''}" href="">面积</a>
                    </p>
                    <p id="sort_qh">
                        <a href="/dealShops/sw1" class="hover"></a>
                    </p>
                    <script th:inline="javascript">
                        var pageUrl = [[${pageUrl}]];
                        var orderKey = [[${orderKey}]];
                        pageUrl = pageUrl.replace(/-?o[0-9]/,'');
                        $("#originalOrder").attr("href", pageUrl+"-o0");
                        function changeUrl(obj) {
                            if(orderKey != $(obj).attr("id")) {
                                orderKey =  $(obj).attr("id");
                            }else {
                                orderKey =  parseInt($(obj).attr("id"))-1;
                            }
                            if(typeof(orderKey)=="undefined"){
                                $(obj).attr("href", pageUrl);
                            }
                            $(obj).attr("href", pageUrl+'-o'+orderKey);
                        }
                    </script>
                </div>
                <div class="tese">
                    <p>特色:</p>
                    <div>
                        <a th:id ="${u.id}" th:each="u,i:${houseTrait}" th:if="${i.index != 0}" th:href="${u.url}" th:text="${u.name}" th:class="${u.selected}? 'hover':''">></a>
                        <!--点击房源特色js-->
                        <script >
                            $(".tese a").click(function () {
//                                var teseid = $(this).attr("id").replace("tese", "");
                                var ref = location.pathname;
                                if(ref.indexOf('dealShops/') == -1) ref +='/';
                                ref = ref.replace(/[-]*[u][0-9,]*[0-9]?$/, "");
                                ref = ref.replace(/[-]*[n][0-9]?$/, "");
                                if($(this).attr("id")){
                                    if ($(this).attr("class") == null) {
                                        $(this).attr("class", "hover");
                                    } else {
                                        $(this).attr("class", "");
                                    }
                                    var uIds="";
                                    $(".tese a").each(function () {
                                        if ($(this).attr("class") == "hover") {
                                            if($(this).attr("id")!=null && $(this).attr("id")!=''&& $(this).attr("id")!=undefined){
                                                uIds += $(this).attr("id")+",";
                                            }
                                        }
                                    });
                                    uIds = uIds.replace(/[,]$/,"");
                                    if(uIds != null && uIds != undefined && uIds != ''){
                                        $(this).attr("href",ref+"-u"+uIds);
                                    }else {
                                        $(this).attr("href",ref);
                                    }
                                }else {
                                    $(this).attr("href",ref);
                                }
                            })
                        </script>
                    </div>
                </div>
                <div class="cl"></div>
                <!--<div class="content">小二为你找到<i th:text="${#strings.isEmpty(msg)?'': msg}"></i>个符合条件的房源</div>-->

                <div class="contentMain" id="saleCol">
                    <!--以下为list形式-->
                    <div class="warning" th:if="${#lists.isEmpty(shop) and sw ne '2'}" >
                        <p>
                            很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。
                            <br> 请重试<!--<a th:href="@{'/helpSearch?ids=4'}" target="_blank">点击免费发布商铺租售方案>></a>-->
                        </p>
                    </div>
                    <div id=replie class="house_left" th:if="${sw ne '2'}">
                        <ul class="bargainHouseList" th:each="sale:${shop}">
                            <li>
                                <div class="houseImg">
                                    <a th:if="${sale.spanTime &lt; 31 and #session?.getAttribute('sessionId') == null}" target="_blank"  data-toggle="modal" href="#loginAgent">
                                        <img th:src="${#strings.isEmpty(sale.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sale.pic}" th:alt="${sale.titile}">
                                    </a>
                                    <a th:if="${sale.spanTime &gt; 31 or #session?.getAttribute('sessionId') != null}" th:href="${'/dealShop/'+sale.houseId+'.htm'}" target="_blank">
                                        <img th:src="${#strings.isEmpty(sale.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sale.pic}" th:alt="${sale.titile}">
                                    </a>
                                </div>
                                <div class="houseInfo">
                                    <div class="houseTitle">
                                        <a th:if="${sale.spanTime &lt; 31 and #session?.getAttribute('sessionId') == null}" target="_blank" data-toggle="modal" href="#loginAgent">
                                            <th:block th:text="${sale.titile}"></th:block>
                                        </a>
                                        <a th:if="${sale.spanTime &gt; 31 or #session?.getAttribute('sessionId') != null}" th:href="${'/dealShop/'+sale.houseId+'.htm'}" target="_blank">
                                            <th:block th:text="${sale.titile}"></th:block>
                                        </a>
                                    </div>
                                    <div class="houseInfoLeft">
                                        <p><th:block th:text="${#strings.isEmpty(sale.regionName)?'':sale.regionName}"></th:block><th:block th:if="${!#strings.isEmpty(sale.regionName)}">-</th:block><th:block th:text="${#strings.isEmpty(sale.plateName)?'':sale.plateName}"></th:block></p>
                                        <p>类型：<th:block th:text="${#strings.isEmpty(sale.shopCategoriesName)?'暂无资料&nbsp;&nbsp;':sale.shopCategoriesName+'&nbsp;&nbsp;'}"></th:block><span th:if="${!#strings.isEmpty(sale.shopCategoriesName) and !#strings.isEmpty(sale.paymentName)}">|</span>
                                            <th:block th:text="${#strings.toString(sale.shopType) eq '1'?'':(#strings.isEmpty(sale.paymentName)?'付款方式：暂无资料':'付款方式：'+sale.paymentName)}"></th:block></p>
                                        <p>
                                            <!--价格--><th:block th:text="${#strings.isEmpty(sale.price) and #strings.isEmpty(sale.price) eq '0.00'?'面议':(#strings.toString(sale.shopType) eq '1'?'挂牌'+sale.price:'租赁标价'+sale.price)}"></th:block>
                                            <!--单位--><th:block th:text="${#strings.isEmpty(sale.price) and #strings.isEmpty(sale.price) eq '0.00'?'':(#strings.toString(sale.shopType) eq '1'?'万':((sale.payment eq '100' or sale.payment eq '50'or sale.payment eq '110')? '元/年':'元/月'))}"></th:block>
                                            <span th:if="${!#strings.isEmpty(sale.price) and !#strings.isEmpty(sale.cycle)}">|</span>
                                            <th:block th:text="${#strings.isEmpty(sale.cycle)?'':'成交周期'+sale.cycle+'天'}"></th:block></p>
                                        <p class="agentName">经纪人： <th:block th:text="${#strings.isEmpty(sale.agency)?'暂无资料':sale.agency}"></th:block></p>
                                    </div>
                                    <div class="houseInfoMiddle">
                                        <div th:if="${sale.spanTime &lt; 31}"><p>近<span>30</span>天内成交</p></div>
                                        <div th:unless="${sale.spanTime &lt; 31}"><p><span><th:block th:text="${#strings.isEmpty(sale.dealTime)?'暂无资料':sale.dealTime}"></th:block></span></p></div>
                                        <div><th:block th:text="${#strings.isEmpty(sale.intermediaryName)?'暂无资料':sale.intermediaryName+'成交'}"></th:block></div>
                                    </div>
                                    <div class="houseInfoRight" th:if="${sale.spanTime &lt; 31}">
                                        <div><span><th:block th:text="${sale.dealPrice}"></th:block></span>
                                            <th:block th:text="${#strings.toString(sale.shopType) eq '1'?'万':((sale.payment eq '100' or sale.payment eq '50'or sale.payment eq '110')? '元/年':'元/月')}"></th:block>
                                        </div>
                                        <div>
                                            <a href="https://download.fangxiaoer.com/" target="_blank">下载APP查看成交></a>
                                            <div class="recode">
                                                <img src="https://static.fangxiaoer.com/web/images/sy/house/bargainHouseRecode.png"/>
                                            </div>
                                        </div>
                                        <a class="checkHouse"  th:if="${#session?.getAttribute('sessionId') == null}"  target="_blank" style="cursor: pointer;margin-top: 0" data-toggle="modal" id="CommentListAdd" href="#loginAgent">查看成交价格&gt;</a>
                                        <a class="checkHouse" style="margin-top: 0"  th:if="${#session?.getAttribute('sessionId') != null}"  th:href="${'/dealShop/'+sale.houseId+'.htm'}" target="_blank">查看成交价格&gt;</a>
                                        <a class="checkHouse" style="margin-top: 0"  target="_blank" th:href="'/shops/-r'+${sale.regionID}">查看同区域房源 &gt;</a>
                                    </div>

                                    <div class="houseInfoRight" th:unless="${sale.spanTime &lt; 31}">
                                        <div><!--总价-->
                                            <span>
                                                <!--成交价格--><th:block th:text="${#strings.indexOf(sale.dealPrice,'.') eq -1 ? sale.dealPrice:#strings.toString(sale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$','')}"></th:block>
                                            </span>
                                            <!--单位--><th:block th:text="${#strings.toString(sale.shopType) eq '1'?'万':((sale.payment eq '100' or sale.payment eq '50'or sale.payment eq '110')? '元/年':'元/月')}"></th:block>
                                        </div>
                                        <div class="price">
                                            <!--出售单价--><th:block th:if="${#strings.toString(sale.shopType) eq '1'}" th:text="${#strings.isEmpty(sale.dealUnitPrice)?'':(#strings.indexOf(sale.dealUnitPrice,'.') eq -1 ? sale.dealUnitPrice+'元/㎡':#strings.toString(sale.dealUnitPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/㎡')}"></th:block>
                                            <!--出租单价--><th:block th:if="${#strings.toString(sale.shopType) eq '2'}" th:text="${#strings.isEmpty(sale.dealUnitPrice)?'':(#strings.indexOf(sale.dealUnitPrice,'.') eq -1 ? sale.dealUnitPrice+'元/㎡·月':#strings.toString(sale.dealUnitPrice).replaceAll('0+?$','').replaceAll('[.]$','')+'元/㎡·月')}"></th:block>
                                            <!--出兑单价--><th:block th:if="${#strings.toString(sale.shopType) eq '3'}" th:text="${#strings.isEmpty(sale.tranFee)?'':(#strings.indexOf(sale.tranFee,'.') eq -1 ? '转让费'+sale.tranFee+'万元':'转让费'+#strings.toString(sale.tranFee).replaceAll('0+?$','').replaceAll('[.]$','')+'万元')}"></th:block>
                                        </div>
                                        <a class="checkHouse" target="_blank" th:href="'/shops/-r'+${sale.regionID}">查看同区域房源 &gt;</a>
                                    </div>
                                </div>
                                <div class="cl"></div>
                            </li>
                        </ul>
                    </div>
                    <div class="cl"></div>
                </div>
                <div class="cl"></div>
                <div class="page">
                    <div th:include="fragment/page :: page"></div>
                </div>
            </div>
            <div id="right">
                <!--<div class="saleHouse">-->
                    <!--<a href="/shopsell" target="_blank">我要卖房</a>-->
                <!--</div>-->
                <!--列表页右侧求租求购-->
                <div class="rightQzQgBtn">
                    <a href="/shoprent/" target="_blank"><i class="rightQzQgBtn-icon3"></i>我要出租</a>
                    <a href="/shopsell/" target="_blank"><i class="rightQzQgBtn-icon4"></i>我要出售</a>
                </div>

                <div class="zsfw">
                    <h1><span></span>帮您找房</h1>
                    <ul>
                        <li>
                            <span>意向区域</span>
                            <div>
                                <select id="region" >
                                    <option  th:each="region,stat:${region}" th:selected="${stat.index eq 1}" th:if="${region.id ne '' and stat.index lt 10}" th:text="${region.name}" th:value="${region.name}">沈河区</option>
                                </select>
                            </div>
                        </li>
                        <li class="hx">
                            <span>供求</span>
                            <div>
                                <select id="needs" >
                                    <!--<option  th:selected="${stat.index eq 1}" th:each="n,stat:${needs}"   th:if="${n.name ne '全部'}" th:text="${n.name}" th:value="${n.name}" >商业出售</option>-->
                                    <option selected="true" th:text="求购" th:value="求购" ></option>
                                    <option  th:text="求租" th:value="求租" ></option>
                                    <option  th:text="求兑" th:value="求兑" ></option>
                                </select>
                            </div>
                        </li>
                        <li class="yx">
                            <span>类型</span>
                            <div>
                                <select id="shoptype" >
                                    <option th:selected="${stat.index eq 1}" th:each="t,stat:${type}"  th:if="${stat.index != 0}" th:text="${t.name}" th:value="${t.name}">住宅底商</option>
                                </select>
                            </div>
                        </li>
                        <li>
                            <textarea id="describe" placeholder="请输入您对需求的描述..." name="describe"></textarea>
                        </li>
                        <li>
                            <span>手机号码</span>
                            <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
                            <input type="hidden" id="type" value="8">
                        </li>
                        <li>
                            <span>验证码</span>
                            <input type="tel" id="code" class="fxe_messageCode"  maxlength="6" style="width: 120px;" placeholder="请输入验证码"/>
                            <p id="hqyzm" class="fxe_ReSendValidateCoad" >获取验证码</p>
                            <p class="fxe_validateCode"></p>
                        </li>
                        <b class="btn" id="new_submit">提交</b>
                        <li style="color: #999;width:237px;text-align:left;font-size: 12px;">若正确提交即同意接受以电话、短信等方式推荐我司业务相关的信息</li>

                    </ul>
                    <div class="form_phone">客服咨询电话：************</div>
                </div>
                <div th:if="${!#lists.isEmpty(advert.shopAd1)}">
                    <th:block th:each="shopAd1,i:${advert.shopAd1}">
                        <a  th:if="${#strings.toString(shopAd1.url).indexOf('http') ne -1}" th:href="${shopAd1.url}" target="_blank">
                            <img width="100%" th:src="${shopAd1.image}" th:alt="${shopAd1.projectName}">
                        </a>
                        <img th:if="${#strings.toString(shopAd1.url).indexOf('http') eq -1}" width="100%" th:src="${shopAd1.image}" th:alt="${shopAd1.projectName}">
                    </th:block>
                </div>
                <dl class="recommend" th:if="${!#lists.isEmpty(advert.command)}">
                    <dt><span></span>商铺推荐</dt>
                    <dd th:each="command,i:${advert.command}"><div>
                        <a th:href="${command.url}" target="_blank">
                            <p th:class="${i.index &lt; 3?'hover':''}"><th:block th:text="${'0'+(i.index+1)}"></th:block></p>
                            <span><th:block th:text="${command.projectName}"></th:block></span>
                            <b style="width: 75px;"><th:block th:utext="${command.price}"></th:block></b>
                        </a>
                    </div></dd>
                </dl>
                <div th:if="${!#lists.isEmpty(advert.shopAd2)}">
                    <a th:each="shopAd2,i:${advert.shopAd2}" th:href="${shopAd2.url}" target="_blank">
                        <img width="100%" th:src="${shopAd2.image}" th:alt="${shopAd2.projectName}">
                    </a>
                </div>
            </div>
        </div>
        <div class="cl"></div>
        <div class="cl"></div>
        <!--底部1-->
        <div th:include="fragment/fragment:: footer_list"></div>
        <div th:include="fragment/fragment::tongji"></div>
    </div>
</form>
<div th:include="house/detail/fragment_contactAgent::loginAgent"></div>
<div th:include="fragment/fragment::commonFloat"></div>
</body>
</html>
