<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title th:text="${shop.title+'_'+shop.regionName+'商铺出售_沈阳商铺出售 - 房小二网'}"></title>
    <meta name="keywords" th:content="${shop.title+',沈阳商铺出售,'+shop.regionName+'商铺转让,出兑'}"/>
    <meta name="description"
          th:content="${'房小二网沈阳商铺频道为你提供'+shop.title+'，以及'+shop.regionName+'商铺与沈阳其他商铺，门市的出兑，出租，出售与转让信息，购买和发布沈阳商铺出租出售信息首选房小二网。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang4/'+houseId+'.htm'}">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/view2017.css"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/global/js/tab.js"></script>
    <script src="https://static.fangxiaoer.com/global/js/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/saleHouseIndent.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <script src="https://static.fangxiaoer.com/global/js/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/global/js/circulation.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <script src="/js/md5.js"></script>
    <script src="/js/indent.js"></script>
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
    <script>
        //图片自适应大小
        function imgSize5() {
            //滚动大图
            var imgWidthHeght2 = $(".imgMax1 ul li").eq(0).width() / $(".imgMax1 ul li").eq(0).height()
            $(".imgMax1 ul li img").each(function () {
                if (parseInt($(this).width()) <= parseInt($(this).height())) {
                    $(this).css({"height": "100%", "width": "auto"})
                    $(this).css({
                        "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                        "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                    })
                } else {
                    $(this).css({"width": "100%", "height": "auto"})
                    $(this).css({
                        "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                        "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                    })
                }
            })
        }

        $(function () {
            //更多相册
            var len = $(".photo li").length;
            if (len > 6) {
                $(".photo li:gt(5)").hide();
                $(".photo ul").after("<span>查看全部照片(" + len + "张)</span>")
            }
            $(".photo span").live("click", function () {
                $(this).hide();
                $(".photo li").show();
            })
        })
        //商铺专用enter事件
        $(document).keypress(function(e) {
            // 回车键事件
            if(e.which == 13) {
                $(".search_btn").click();
            }
        });
        function showLicense(e) {
            $(".license").show();
        }
        function hideLicense(e) {
            $(".license").hide();
        }
    </script>
    <style>
        .details>ul>li ul li {
            width: 100%;
        }
        .noyongjin dt {
            margin-top: 8px !important;
        }
        .license{position: absolute;width: 260px;height: 370px;border: 1px solid #ccc;display: none;
            border-radius: 5px;z-index: 99;background-color: #FFF;bottom: 40px;right: 30px;padding: 5px;}
        .license_img{
            width: 100% !important;
            height: 100% !important;
            margin: 0px !important;
            border-radius: 0px !important;
        }
    </style>
</head>
<body>
<!--<form name="form1" method="post" action="7727" id="form1">-->
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=5"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=4"></div>
<input type="hidden" id="Idhouse" th:value="${houseId}"/>
<input type="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}"/>
<input type="hidden" id="houseType" value="3" />
<!--面包屑-->
<div class="w crumbs">您的位置：
    <a href="/" target="_blank">沈阳房产网</a> &gt;
    <a href="/shops/" target="_blank">沈阳商铺</a> &gt;
    <a href="/dealShops/" target="_blank">成交房源</a> &gt;
    <a th:href="${'/dealShops/r'+shop.regionId}" target="_blank" th:text="${shop.regionName}"></a> &gt;
    <th:block th:text="${#strings.isEmpty(shop.title)?'':shop.title}"></th:block>
</div>
<!--基本信息-->
<div class="w main">
    <div class="header_sale">
        <div class="cl"></div>
        <h1 class="title1" th:text="${#strings.isEmpty(shop.title)?'':shop.title}">（出租）铁西云峰街北四路 巴塞罗那 60平米 一楼门市</h1>
        <ul>
            <li th:if="${!#strings.isEmpty(dealSale.dealTime)}"  th:text="${'成交时间：'+#strings.toString(dealSale.dealTime).replace('.','-')}"></li>
        </ul>
        <!--分享标签-->
        <div th:include="fragment/fragment:: shareIcon"></div>
    </div>
    <div class="photoAlbum">
        <div class="imgMax1">
            <ul>
                <li>
                    <img th:src="${bigImage eq 'https://images.fangxiaoer.com/sy/esf/fy/big/noimage375275.jpg'?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':#strings.toString(bigImage).replace('middle','big')}"
                         th:alt="${#strings.isEmpty(shop.title)?'':shop.title}" onload="imgSize5()"/>
                </li>
            </ul>
        </div>
    </div>
    <div th:class="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '1'? 'mainContent chushou':(shop.shopType eq '2'?'mainContent chuzu':'mainContent chudui')}">
        <div class="price cs" th:if="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '1' }"><span>
                <b th:text="${#strings.isEmpty(dealSale.dealPrice)or dealSale.dealPrice eq '0.00' ?'面议': (#strings.indexOf(dealSale.dealPrice,'.') eq -1 ? dealSale.dealPrice:#strings.toString(dealSale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$',''))}">42000</b>
                <th:block th:text="${#strings.isEmpty(dealSale.dealPrice) or dealSale.dealPrice eq '0.00' ?'':'万'}"></th:block> </span>
            <p><span>出售</span></p>
        </div>
        <div class="price cz" th:if="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '2' }"><span><b
                th:text="${#strings.isEmpty(dealSale.dealPrice)or dealSale.dealPrice eq '0.00' ?'面议':(#strings.indexOf(dealSale.dealPrice,'.') eq -1 ? dealSale.dealPrice:#strings.toString(dealSale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$',''))}">42000</b>
                <th:block th:text="${#strings.isEmpty(dealSale.dealPrice) or dealSale.dealPrice eq '0.00' ? '':(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'?'元/年':'元/月')}"></th:block></span>
            <p><span>出租</span></p>
            <p><span th:text="${#strings.isEmpty(shop.paymentName) or dealSale.dealPrice eq '0.00' ?'':shop.paymentName}">年付</span></p>
        </div>
        <div class="price cd" th:if="${!#strings.isEmpty(shop.shopType) and shop.shopType eq '3'}"><span><b
                th:text="${#strings.isEmpty(dealSale.dealPrice)or dealSale.dealPrice eq '0.00' ?'面议':(#strings.indexOf(dealSale.dealPrice,'.') eq -1 ? dealSale.dealPrice:#strings.toString(dealSale.dealPrice).replaceAll('0+?$','').replaceAll('[.]$',''))}">42000</b>
                <th:block th:text="${#strings.isEmpty(dealSale.dealPrice)or dealSale.dealPrice eq '0.00' ? '':(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'?'元/年':'元/月')}"></th:block></span>
            <p><span>出兑</span></p>
            <p><span th:text="${#strings.isEmpty(shop.paymentName)or dealSale.dealPrice eq '0.00' ?'':shop.paymentName}">年付</span></p>
        </div>
        <div class="type">
            <ul style="margin-left: 0">
                <li style="width: 120px" th:if="${shop.shopType eq '1'}">
                    <b th:text="${#strings.toString(dealSale.price).contains('.') ?  #strings.toString(dealSale.price).replaceAll('0+?$','').replaceAll('[.]$', ''):dealSale.price}"></b>
                    <p>挂牌价格（万）</p>
                </li>
                <li style="width: 120px" th:if="${shop.shopType ne '1'}">
                    <b th:text="${#strings.toString(dealSale.price).contains('.') ?  #strings.toString(dealSale.price).replaceAll('0+?$','').replaceAll('[.]$', ''):dealSale.price}"></b>
                    <p>租赁标价（<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ? '':(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'?'元/年':'元/月')}"></th:block>）</p>
                </li>
                <li style="width: 110px">
                    <b th:text="${dealSale.cycle}"></b>
                    <p>成交周期（天）</p>                    </li>
                <li style="width: 110px">
                    <b th:text="${dealSale.totalVisitedNum}"></b>
                    <p>浏览（次）</p>                    </li>
                <li style="width: 110px">
                    <b th:text="${dealSale.favorite}"></b>
                    <p>关注（次）</p>
                </li>
            </ul>
        </div>
        <div class="type">
            <ul th:style="${shop.shopType eq '3' and #strings.toString(dealSale.tranFee).replaceAll('0+?$','').replaceAll('[.]$', '') ne '0' and !#strings.isEmpty(dealSale.tranFee)?'margin-left: 0':''}">
                <li th:style="${shop.shopType eq '3' and #strings.toString(dealSale.tranFee).replaceAll('0+?$','').replaceAll('[.]$', '') ne '0' and !#strings.isEmpty(dealSale.tranFee)?'width: 112px;':'width: 164px;'}">
                    <b th:text="${#strings.isEmpty(shop.shopCategoriesName)?'':shop.shopCategoriesName}">住宅底商</b>
                    <p>商铺类型</p>
                </li>
                <li th:style="${shop.shopType eq '3' and #strings.toString(dealSale.tranFee).replaceAll('0+?$','').replaceAll('[.]$', '') ne '0' and !#strings.isEmpty(dealSale.tranFee)?'width: 112px;':'width: 164px;'}">
                    <b th:text="${#strings.isEmpty(shop.area)?'': (#strings.indexOf(shop.area,'.') eq -1 ? shop.area:#strings.toString(shop.area).replaceAll('0+?$','').replaceAll('[.]$',''))+'m²'}">60m²</b>
                    <p>面积</p>
                </li>
                <li th:style="${shop.shopType eq '3' and #strings.toString(dealSale.tranFee).replaceAll('0+?$','').replaceAll('[.]$', '') ne '0' and !#strings.isEmpty(dealSale.tranFee)?'width: 112px;':'width: 164px;'}">
                    <b th:text="${#strings.isEmpty(shop.isCutName)?'不可分割':shop.isCutName}">无</b>
                    <p>是否分割</p>
                </li>
                <li th:if="${shop.shopType eq '3' and #strings.toString(dealSale.tranFee).replaceAll('0+?$','').replaceAll('[.]$', '') ne '0' and !#strings.isEmpty(dealSale.tranFee)}"
                    th:style="${shop.shopType eq '3' and #strings.toString(dealSale.tranFee).replaceAll('0+?$','').replaceAll('[.]$', '') ne '0' and !#strings.isEmpty(dealSale.tranFee)?'width: 112px;':'width: 164px;'}"
                >
                    <b th:text="${#strings.toString(dealSale.tranFee).contains('.') ?  #strings.toString(dealSale.tranFee).replaceAll('0+?$','').replaceAll('[.]$', '')+'万':dealSale.tranFee+'万'}">无</b>
                    <p th:text="${shop.tranTypeName}">是否分割</p>
                </li>
            </ul>
        </div>
        <div th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '2'}" class="card jjr" style="position: relative;overflow: inherit;">
            <p>
                <a th:href="${#strings.isEmpty(shop.agencyId)?'':'/agent/dealShops/'+shop.agencyId+'/'}" target="_blank">
                    <img th:src="${#strings.isEmpty(shop.headPic)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':shop.headPic}"/><br>
                </a>
                <a th:href="${#strings.isEmpty(shop.agencyId)?'':'/agent/dealShops/'+shop.agencyId+'/'}" target="_blank">进入Ta的店铺</a>
            </p>
            <dl class="noyongjin">
                <dt>
                    <a th:href="${#strings.isEmpty(shop.agencyId)?'':'/agent/dealShops/'+shop.agencyId+'/'}" target="_blank">
                        <b th:text="${#strings.isEmpty(shop.houseOwner)?'':shop.houseOwner}">任薏明</b>
                    </a>
                    <span th:text="${#strings.isEmpty(shop.intermediaryName)?'':shop.intermediaryName}">喜鹊不动产晶座店</span>
                </dt>
                <dd th:text="${#strings.isEmpty(shop.ownerPhone)?'':shop.ownerPhone}">15640076431</dd>
                <ddd><th:block th:text="${#strings.isEmpty(dealSale.dealCount)?'':'成交'+dealSale.dealCount+'套&nbsp;&nbsp;'}"></th:block><th:block th:text="${#strings.isEmpty(dealSale.dealCycle)?'':'平均成交周期'+dealSale.dealCycle+'天'}"></th:block></ddd>
                <div style="clear: both;"></div>
                <span style="cursor: pointer;padding-top: 20px;" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                    公司执照编码：
                    <span style="color:#999;">
                        <th:block th:text="${shop.agentBusinessNum}"></th:block>
                    </span>
                </span>
                <div class="license" onmouseenter="showLicense(this)" onmouseleave="hideLicense(this)">
                    <img th:src="${shop.agentBusinessCardForPc}" class="license_img">
                </div>
                <div style="clear: both;"></div>
            </dl>
            <div style="clear: both;"></div>
        </div>
        <div th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '1'}" class="card gr">
            <p><img th:src="${#strings.isEmpty(shop.headPic)?'https://static.fangxiaoer.com/web/images/ico/sign/agent_men.png':shop.headPic}"/>
            </p>
            <dl>
                <dt style="margin-top:10px;"><b th:text="${#strings.isEmpty(shop.houseOwner)?'':shop.houseOwner}">陈先生</b>
                </dt>
                <dd th:text="${#strings.isEmpty(shop.ownerPhone)?'':shop.ownerPhone}">***********</dd>
            </dl>
        </div>
    </div>

    <div class="w" style="overflow: hidden;">
        <div class="left">
            <div class="details">
                <div class="head">房源描述</div>
                <ul>
                    <li th:if="${#strings.toString(dealSale.shopType) eq '1'}">
                        <span>交易属性</span>
                        <div>
                            <ul>
                                <li>
                                    <span>挂牌时间：</span>
                                    <p th:text="${#strings.toString(dealSale.AddTime).replace('.','-')}"></p>
                                </li>
                                <li>
                                    <span>房屋年限：</span>
                                    <p th:text="${#strings.isEmpty(dealSale.FiveYearName)?'暂无资料':dealSale.FiveYearName}"></p>
                                </li>
                                <li>
                                    <span>房屋用途：</span>
                                    <p th:text="${#strings.isEmpty(dealSale.shopCategoriesName)?'暂无资料':dealSale.shopCategoriesName}"></p>
                                </li>
                                <!--<li>
                                    <span>交易属性：</span>
                                    <p th:text="${#strings.isEmpty(dealSale.HousingType)?'暂无资料':dealSale.HousingType}"></p>
                                </li>-->
                                <li>
                                    <span>土地年限：</span>
                                    <p th:text="${#strings.isEmpty(dealSale.deadline)?'详见业主土地证明材料或相关政府部门登记文件':dealSale.deadline+'年'}"></p>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li th:if="${!#strings.isEmpty(shop.mIndustryName)}">
                        <span>商铺配套</span>
                        <div>
                            <th:block th:text="${#strings.isEmpty(mIndustryName)?'':mIndustryName}"></th:block>
                        </div>
                    </li>
                    <li>
                        <span>房源描述</span>
                        <div style="border: 0;" class="fyms"><span
                                style="color:#333333;font-family:微软雅黑;font-size:14px;line-height:24px;">
                                <th:block
                                        th:text="${#strings.isEmpty(shop.description)?'':shop.description}"></th:block>
                            </span></div>
                    </li>
                </ul>
            </div>
            <div class="w photo" th:if="${!#lists.isEmpty(shop.pic)}">
                <div class="head " >房源相册</div>
                <ul >
                    <li th:each="pics:${shop.pic}">
                        <img th:src="${#strings.isEmpty(pics.pic)? '': pics.pic}">
                    </li>
                </ul>
            </div>
        </div>
        <div class="right">
            <div id="xiangsihouse" class="head recommend">
                <h1>同区域您可能感兴趣的房源</h1>
                <ul>
                    <li  th:each="recommend,status:${recommend}" th:if="${!#lists.isEmpty(recommend)}">
                        <a th:href="${#strings.isEmpty(recommend.shopId)? '':'/shop/'+recommend.shopId+'.htm' }">
                            <div>
                                <img th:src="${#strings.isEmpty(recommend.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': recommend.pic}"
                                     th:alt="${#strings.isEmpty(recommend.title)? '':recommend.title}">
                                <div>
                                    <p th:if="${recommend.shopType eq '1'}">
                                        <span th:text="${#strings.isEmpty(recommend.price)or recommend.price eq '0.00'? '面议':(#strings.indexOf(recommend.price,'.') eq -1 ? recommend.price:#strings.toString(recommend.price).replaceAll('0+?$','').replaceAll('[.]$','')) }">45000元/年</span>
                                        <i th:if="${!#strings.isEmpty(recommend.price) and recommend.price ne '0.00'}" th:text="${'万'}"></i>
                                    </p>
                                    <p th:if="${recommend.shopType eq '2' or recommend.shopType eq '3'}">
                                        <span th:text="${#strings.isEmpty(recommend.price)or recommend.price eq '0.00'? '面议':(#strings.indexOf(recommend.price,'.') eq -1 ? recommend.price:#strings.toString(recommend.price).replaceAll('0+?$','').replaceAll('[.]$','')) }">45000元/年</span>
                                        <i th:if="${!#strings.isEmpty(recommend.price) and recommend.price ne '0.00'}" th:text="${recommend.payment  eq '50' or recommend.payment eq '100'or recommend.payment eq '110' ?  '元/年':'元/月'}"></i>
                                    </p>
                                    <b th:if="${recommend.shopType eq '1'}" th:text="${#strings.isEmpty(recommend.regionName)? '':'(出售)'+recommend.regionName }">(出租)铁西区</b>
                                    <b th:if="${recommend.shopType eq '2'}" th:text="${#strings.isEmpty(recommend.regionName)? '':'(出租)'+recommend.regionName }">(出租)铁西区</b>
                                    <b th:if="${recommend.shopType eq '3'}" th:text="${#strings.isEmpty(recommend.regionName)? '':'(出兑)'+recommend.regionName }">(出租)铁西区</b>
                                    <i style="color: #666" th:text="${#strings.isEmpty(recommend.area)? '':#numbers.formatInteger(recommend.area,1)+'m²'}">81m²</i>
                                    <p th:text="${#strings.isEmpty(recommend.shopCategoriesName)?'':recommend.shopCategoriesName}">住宅底商</p>
                                </div>
                            </div>
                        </a>
                    </li>

                </ul>
            </div>
        </div>
    </div>
</div>
<div class="cl"></div>

<div class="disclaimer"><strong>免责声明：</strong>
    房源信息由网站用户提供，其真实性、合法性由信息提供者负责，最终以政府部门登记备案为准。本网站不声明或保证内容之正确性和可靠性，租赁该房屋时，请谨慎核查。如该房源信息有误，您可以投诉或<strong>拔打举报电话：400-893-9709</strong>。
</div>
<div class="bigImgShow">
    <div class="showImg" th:if="${!#lists.isEmpty(shop.pic)}">
        <ul>
            <li th:each="pics:${shop.pic}">
                <img th:src="${#strings.isEmpty(pics.pic)? '': #strings.toString(pics.pic).replace('middle','big')}" th:alt="${#strings.isEmpty(shop.title)?'':shop.title}" onload="imgSize()">
            </li>
        </ul>
    </div>
    <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"/></div>
    <div class="prev"></div>
    <div class="next"></div>
</div>
<script type="text/javascript">
    photo.init(5);
    //        calculate();
</script>

<div class="cl"></div>
<!--页面底部-->
<div class="footer" style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<!--</form>-->
<div  class="suspensionIcon" th:include="fragment/fragment::commonFloat"></div>
<script>
    var islogin
    $(document).ready(function () {
        islogin = $("#sessionId").val();
    });
</script>
<div class="saleHouseIndent">
    <div class="page1">
        <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
        <h1>在线支付享优惠</h1>
        <ul>
            <li><span>购房人</span><input id="name" placeholder="请填写姓名" type="text" maxlength="20" /></li>
            <li><span>手机号码</span><input type="text" id="phone" placeholder="请填写手机号码" maxlength="11" value="" /></li>
            <li class="yzm" style="border-bottom: 1px solid #ccc;"><span>请输入</span><input type="password" autocomplete="new-password" id="code" placeholder="验证码/密码" maxlength="20" style="width: 129px;" /><b class="fxe_ReSendValidateCoad">获取验证码</b><b class="fxe_validateCode" style="display: none;"></b></li>
            <!--
                            <li class="mm" style="display: none; border-bottom: 1px solid #ccc;"><span>密码</span><input type="password" id="pwd" placeholder="请填写密码" maxlength="20" /></li>
            -->
            <input type="hidden" id="userType" value="">
        </ul>
        <a>佣金95折</a>
        <dl>
            <dt></dt>
            <dd>我已经看过并同意<a href="https://info.fangxiaoer.com/Help/remission" target="_blank">《佣金减免说明书》</a></dd>
        </dl>
        <!--<span class="x"></span>-->
    </div>
    <div class="page2">
        <s class="x"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
        <h1 >在线支付</h1>
        <p  th:text="${shop.title}" >国瑞城 1室 0厅 1卫 </p>
        <ul>
            <li><span>在线支付20元，约折佣340元</span></li>
            <li  th:text="${'地址：'+shop.address}">地址： 大东区东北大马路398号</li>
            <li th:text="${'经纪人：'+ (shop.houseOwner ne null and shop.houseOwner ne ''? shop.houseOwner:'') + ' '+ (shop.ownerPhone == null or #strings.toString(shop.ownerPhone) eq '' ? '':shop.ownerPhone)}">经纪人： 常先生 13840383296</li>
        </ul>
        <dl>
            <dt>支付方式： </dt>
            <dd class="weixin hover">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/weixin.png" alt="微信" /><i></i></dd>
            <dd class="alipay">
                <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/alipay.png" alt="支付宝" /><i></i></dd>
        </dl>
        <span>支付金额：20元</span>
        <a>去支付</a>
        <!--<span class="x"></span>-->
    </div>
    <div class="page3">
        <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
        <h1>请使用<span>支付宝</span>扫描下面二维码</h1>
        <img src="" alt="二维码获取失败" />
        <!--<span class="x1"></span>-->
    </div>
    <div class="page4">
        <s class="x1"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
        <img src="https://static.fangxiaoer.com/web/images/sy/house/giftIndent/success.png" />
        <b>支付成功</b>
        <a href="https://my.fangxiaoer.com/payOrder">查看订单</a>
        <!--<span class="x1"></span>-->
    </div>

</div>
<script>
    pay.init();
</script>
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
<div th:include="house/detail/fragment_login::login"></div>
</body>
</html>
