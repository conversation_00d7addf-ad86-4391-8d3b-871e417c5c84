<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>积分商城，会员积分换好礼 - 房小二网</title>
    <meta name="keywords" content="积分兑换,积分商城,积分计划,房仔积分,积分换好礼"/>
    <meta name="description" content="为答谢广大房仔对房小二网的支持与厚爱，房小二网特推出积分商城供房仔兑换丰富多样的礼品，让大家享受到房小二网给予的实实在在的实惠。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/fzsc.htm">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/need/integral_new.css" >
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/need/vip/base.css?v=20181225" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
</head>
<body class="w1210">
<!--表单-->
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
    <!--搜索栏-->
    <!--<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=6"></div>-->
    <!--“感恩有你”图片-->
    <div id="myCarousel"  class="carousel slide need_top" style="background:url(https://static.fangxiaoer.com/web/images/sy/need/ban_11.jpg) no-repeat center"></div>
    <!--页面位置-->
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a>&nbsp;>&nbsp;<a href="/freeServiceIndex/">服务</a>&nbsp;>&nbsp;积分商城</div>

    <!--礼品分类与获奖用户-->
    <div class="w sort">
        <div class="txtScroll-left">
            <div class="hd">
            </div>
            <div class="bd">
                <ul class="infoList" >
                    <li th:each="midUser:${midUser}">
                        <th:block th:text="${'用户'+midUser.CommberPhone+'兑换'}"></th:block>
                        <i th:text="${midUser.goodsName}"></i>
                        <th:block th:text="${midUser.count+'个'}"></th:block>
                    </li>
                </ul>
            </div>
        </div>
        <span>礼品分类：</span>
        <a href="/pointsMall"  th:class="${#strings.toString(sortId) eq '0' ? 'hover':''}">全部</a>
        <!--<div th:each="title:${title}">-->
        <span th:each="title:${title}">
        <a
            th:href="${'/pointsMall/'+title.id}"
            th:class="${#strings.toString(sortId) eq #strings.toString(title.id) ? 'hover':''}">
            <th:bolck th:utext="${title.name}"></th:bolck>
        </a>&nbsp;丨
        </span>
        <!--</div>-->
    </div>
    <script type="text/javascript">
        var ary = location.href.split("&");
        jQuery(".txtScroll-left").slide({ titCell: ".hd ul", mainCell: ".bd ul", autoPage: true, effect: "leftLoop", autoPlay: true, vis: 2, delayTime: 700, interTime: 4000 });
    </script>


    <!--商品展示-->
    <div class="w list">
        <ul>
            <li th:each="m:${mall}">
                <a th:href="${'/goods/'+m.id+'.htm'}" target="_blank">
                    <img  th:src="${m.pic}" th:alt="${m.title}"  /> </a><p>
                <span>
                        <em class="n1" th:if="${m.sort eq '1'}" th:text="文化用品"></em>
                        <em class="n1" th:if="${m.sort eq '2'}" th:text="家居装饰"></em>
                        <em class="n1" th:if="${m.sort eq '3'}" th:text="数码家电"></em>
                        <em class="n1" th:if="${m.sort eq '4'}" th:text="家庭保健"></em>
                    <a th:href="${'/goods/'+m.id+'.htm'}" target="_blank" th:text="${m.title}"> </a></span>
                兑换积分：<i>
                    <th:block th:text="${#strings.isEmpty(m.integration)?'暂无资料':m.integration+'积分'}"></th:block>
                </i><br><th:block th:text="${#strings.isEmpty(m.balance)?'暂无':'礼品剩余数量：'+m.balance+'个'}"></th:block></p></li>

        </ul>
    </div>
    <div class="w list"></div>
    <div class="cl"></div>


    <!--如何获得积分。温馨提示-->
    <div class="w">
        <div class="w585 jfsm">
            <div class="jfsm_title">如何获得积分</div>
            <div>
                1、 首次注册房小二网会员成为房仔10积分；<br />
                2、每天登录签到1次获得5积分；<br />
                3、推荐好友成功注册会员，推荐人得10积分；<br />
                4、兑换积分礼品会员将自己的“积分兑换故事”以文字+图片的形式发给我们，一经录用即可获得200积分的奖励；<br />
                5、“拍”出生活精彩：<br />
                将您生活中的精彩瞬间、开心故事以照片的形式发给我们，一经采用，会获得100积分的奖励；<br />
                6、推荐他人于房小二网购房成功者，推荐者获得10000积分/人。<br />
                推荐热线400-893-9709（获得积分需提供两人身份证复印件及合影一张）。
            </div>
        </div>
        <div class="w585 jfsm">
            <div class="jfsm_title">温馨提示</div>
            <div>
                1、本人注册、购房、参与活动或推荐他人注册、购房后，可获得相应积分；<br />
                2、每周推荐注册会员人数超过5人的，房仔会员服务中心统一核实被推荐人资格；被推荐者对注册房小二网情况一无所知的，推荐者的会员积分将被清零；<br />
                3、有资格参与线上特惠活动，享受独家优惠，并可直接进行在线支付；<br />
                4、支付后可7天无理由退订，全额返还交易钱款。退订后可重新抢购特惠产品或获得优惠资格；<br />
                5、免费享受大型看房团、时时看房车、一对一带看等看房服务；<br />
                6、资深房产专家即时在线解答各类房产问题；<br />
                7、专业客服量身推荐合适房源，免费制定专属购房方案；<br />
                8、项目助理即时反馈房仔所购或所关注楼盘最新动态、工程进度及点滴细节。
            </div>
        </div>
    </div>

    <!--积分兑换须知-->
    <div class="cl"></div>
    <div class="w">
        <div class="jfsm1" style="border-top: 1px solid #f1f1f1;padding: 20px 0 0 0;margin-top: 20px;width: 1157px !important;">
            <div class="jfsm_title">积分有效期</div>
            <div style="line-height: 27px;">
                积分商城积分有效期截止2019年12月31日，逾期失效。有效期间，积分可随时兑换，兑换后工作人员会通过电话通知商品兑换相关事宜。如：有效期截止后办理积分兑换或者积分不足者，积分自动清零，不做任何补偿，恕不另行通知。
            </div>
        </div>
        <div class="jfsm" style="color: #282828;margin-bottom:30px;">
            <div class="jfsm_title">温馨提示</div>
            <div>
                （一）声明<br />
                积分兑换方式及条件由房小二网通过网站发布，由于您信息接收延误、号码错误，或者更换号码后未通知房小二网等产生的无法兑换商品等情况，房小二网概不负责。实施兑换后，房小二网将从会员积分中扣除兑换分值，请及时查阅您的积分，如有问题，请立即联系房小二网工作人员。<br />
                （二）提示<br />
                由于积分系统全新升级，原积分商城会员积分可于有效期内兑换礼品，但无法再通过原有渠道获取积分，2019年12月31日后，原积分商城积分全部清零，对您带来不便敬请谅解！
            </div>
        </div>
    </div>
    <div class="cl"></div>


    <div class="cl"></div>

    <!--底部1-->
    <div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>

    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.SuperSlide.2.1.1.js"></script>
    <style>
        .txtScroll-left{ position:relative; float:right;padding-left:26px;background:url(https://static.fangxiaoer.com/web/images/sy/need/ico_integral.jpg) left no-repeat}
        .txtScroll-left .hd{display:none}
        .txtScroll-left .hd ul{ float:right; overflow:hidden; zoom:1; }
        .txtScroll-left .hd ul li{ float:left; overflow:hidden; margin-right:5px; text-indent:-999px; }
        .txtScroll-left .hd ul li.on{ background-position:0 0; }
        .txtScroll-left .bd{ width:500px; overflow:hidden;    }
        .txtScroll-left .bd ul{ overflow:hidden; zoom:1; }
        .txtScroll-left .bd ul li{ float:left;text-align:left; _display:inline;width:250px;height: 16px;height: 16px;}
        .txtScroll-left .bd ul li span{ color:#999;  }
        .infoList i{color:#ff5200}
    </style>
    <script type="text/javascript">
        var ary = location.href.split("&");
        jQuery(".txtScroll-left").slide({ titCell: ".hd ul", mainCell: ".bd ul", autoPage: true, effect: "leftLoop", autoPlay: true, vis: 2, delayTime: 700, interTime: 4000 });
    </script>

    <div th:include="fragment/fragment::esfCommon_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>