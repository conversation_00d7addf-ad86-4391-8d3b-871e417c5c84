<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>购房攻略_沈阳楼市_购房政策 - 小二管家</title>
    <meta name="keywords" content="沈阳房产新闻,沈阳楼市动态,沈阳楼市新闻,沈阳房地产资讯" />
    <meta name="description" content="沈阳房小二网是沈阳最权威的房地产媒体.沈阳房产新闻第一时间报道,楼市动态权威解读,房产业界资讯专业点评,民生资讯,沈阳房地产信息一网打尽!" />
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/fzgl.htm">
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css"/>
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap.min2017.css" rel="Stylesheet" type="text/css"/>
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/need/strategy.css" rel="stylesheet" type="text/css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
</head>

<body class="w1210">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=6"></div>

    <div id="myCarousel"  class="carousel slide need_top" style="cursor:pointer; background:url(https://static.fangxiaoer.com/web/images/sy/need/ban_10.jpg) no-repeat center">
    </div>
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt;<!-- <a href="/activities" target="_blank">房仔中心</a> &gt;--> 购房攻略</div>
    <div class="container" style="margin-top:0px !important">
        <div class="row">
            <div class="left-w span9">
                <!--攻略分类-->
                <ul class="fxe_bq">
                    <li th:each="strategy:${strategy}"  >
                        <a  th:href="${'/strategy/'+strategy.CategoryId}" th:class ="${#strings.toString(strategy.CategoryId) eq typeId ?'fxe_hover':''}"  th:text="${strategy.CategoryName}"></a>
                    </li>
                </ul>
                <!--活动信息-->
                <div class=" news" th:each="news:${news}">
                    <p>
                        <a th:href="${'/activity/'+news.id+'.htm'}" target="_blank" th:text="${news.titleShow}"></a><br/>
                        <span>
                        <i th:text="${news.visitedTimes}"></i>
                        来源： 房小二网  |  <th:block th:text="${'编辑： '+news.author+' '}"></th:block>|  <th:block th:text="${news.updateTime}"></th:block>
                        </span>
                    </p>

                    <div class="left">
                        <a th:href="${'/activity/'+news.id+'.htm'}" target="_blank"><img th:src="${news.pic}" width="252" height="184" th:alt="${news.title}" /></a></div>
                    <div class="right">
                        <a th:href="${'/activity/'+news.id+'.htm'}" target="_blank">
                            全文>>
                        </a>
                        <span th:text="${news.description}"></span>
                    </div>
                    <div class="cl"></div>
                </div>
            </div>
            <div class="right-w span3 " style="padding-top: 39px">
                <div id="newsAD1_guanggao" class="flash"  th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}"><!--广告-->
                    <ul class="rotaion_list">
                        <li th:each="advert1:${advert1}">
                            <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                            <a th:href="${advert1.TargetUrl}" target="_blank">
                                <img th:src="${advert1.AdFilePath}" alt="">
                            </a>
                        </li>
                    </ul>
                </div>

                <!--房产快讯标题-->
                <div class="title">房产快讯</div>
                <!--房产快讯信息-->
                <div class="hot" th:include="fragment/fragment::news_flash"></div>


                <div id="newsAD2_guanggao" class="flash"  th:if="${advert1 ne null and #lists.toList(advert2).size() ne 0}"><!--广告-->
                    <ul class="rotaion_list">
                        <li th:each="advert2:${advert2}">
                            <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                            <a th:href="${advert2.TargetUrl}" target="_blank">
                                <img th:src="${advert2.AdFilePath}" alt="">
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    </div>
        </div>
    </div>
    <!--分页-->
    <div class="page">
        <!--<input name="page"/>-->
        <div class="page">
            <div id="Pager1">
                <div th:include="fragment/page :: page "></div>
            </div>
        </div>
    </div>

    <div class="cl"></div>
    <!--底部1-->
    <div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::commonFloat"></div>
    <input name="isShow" type="hidden" id="isShow" value="1" />
    <script type="text/javascript">
        //<![CDATA[
        var theForm = document.forms['ctl00'];
        if (!theForm) {
            theForm = document.ctl00;
        }
        function __doPostBack(eventTarget, eventArgument) {
            if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
                theForm.__EVENTTARGET.value = eventTarget;
                theForm.__EVENTARGUMENT.value = eventArgument;
                theForm.submit();
            }
        }
        //]]>
    </script>

</body>
</html>

