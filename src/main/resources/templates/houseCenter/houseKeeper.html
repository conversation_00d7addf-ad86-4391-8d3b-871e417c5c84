<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>小二管家_购房指导_购房服务 - 您身边的购房管家</title>
    <meta name="keywords" content="购房管家,购房指导,房源推荐,专家购房,专家买房" />
    <meta name="description" content="房小二网小二管家为您购房提供全程一对一贴心服务，包括房源推荐、购房指导、顾问式陪看房、交易磋商、合同签约服务，为你精准制定专属购房计划，比你更懂你自己。" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main.css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles//new_sy/need/keeper0313.css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/house/Freefone.css"/>

    <script src="/js/house/verify.js" type="text/javascript" charset="utf-8"></script>
    <style>
        .Freefone{
            display: none;
        }
    </style>
</head>
<body>
<form name="form1" method="post" action="index.aspx" id="form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
    <div class="banner"></div>
    <div class="content1 contenter">
        <div class="head"><img src="https://static.fangxiaoer.com/web/images/sy/need/keeper/1.png"/>我们是谁？</div>
        <ul>
            <li>我们是房小二网旗下小二管家，帮你们破解买房营销套路，规避购房风险，力争买房利益最大化。</li>
            <li>我们是第一个站在买房人的立场来帮助买房的管家队伍。当你有购房需求，不知道房价是涨还是跌，到底该不该买？</li>
            <li>买哪的，是买新房还是成熟区的二手房？是买品牌的还是不知名企业的？这时你们应该找我们！</li>
        </ul>
       <dl>
           <dd>
               <h1>【地铁/学区房专家】</h1>
               <div th:each="feature,i:${data.feature}" th:if="${i.index lt 2}">
                   <a th:href="${'https://sy.fangxiaoer.com/agent/second/' + feature.memberId}" target="_blank">
                       <img th:src="${feature.headPic}" th:alt="${feature.memberName}">
                       <p><span th:text="${feature.memberName}"></span><th:block th:text="${feature.mobile}"></th:block></p>
                   </a>
               </div>
           </dd>
           <dd>
               <h1>【投资房产专家】</h1>
               <div th:each="feature,i:${data.feature2}" th:if="${i.index lt 2}">
                   <a th:href="${'https://sy.fangxiaoer.com/agent/second/' + feature.memberId}" target="_blank">
                       <img th:src="${feature.headPic}" th:alt="${feature.memberName}">
                       <p><span th:text="${feature.memberName}"></span><th:block th:text="${feature.mobile}"></th:block></p>
                   </a>
               </div>
           </dd>
           <dd>
               <h1>【品质改善专家】</h1>
               <div th:each="feature,i:${data.feature3}" th:if="${i.index lt 2}">
                   <a th:href="${'https://sy.fangxiaoer.com/agent/second/' + feature.memberId}" target="_blank">
                       <img th:src="${feature.headPic}" th:alt="${feature.memberName}">
                       <p><span th:text="${feature.memberName}"></span><th:block th:text="${feature.mobile}"></th:block></p>
                   </a>
               </div>
           </dd>
           <dd>
               <h1>【房贷/房税专家】</h1>
               <div th:each="feature,i:${data.feature4}" th:if="${i.index lt 2}">
                   <a th:href="${'https://sy.fangxiaoer.com/agent/second/' + feature.memberId}" target="_blank">
                       <img th:src="${feature.headPic}" th:alt="${feature.memberName}">
                       <p><span th:text="${feature.memberName}"></span><th:block th:text="${feature.mobile}"></th:block></p>
                   </a>
               </div>
           </dd>
       </dl>
        <p><a href="../keeperMore/">点击查看更多小二管家</a></p>
    </div>
    <div class="content2 contenter content3">
        <div class="head"><img src="https://static.fangxiaoer.com/web/images/sy/need/keeper/2.png"/>服务流程</div>
        <ul>
            <li>
                <div>最便宜流程</div>
                <p>看房前第一时间联系小二管家，我们为您专业分析、帮您砍价，并提供额外<span style="color: #ff8400">钻石优惠券</span>。</p>
                <a href="https://sy.fangxiaoer.com/discount/" target="_blank" class="yhqIcon_1 yhqIcon"></a>
            </li>
            <li>
                <div> 相对便宜流程</div>
                <p>去过售楼处看过房子，但未交定金，之后联系小二管家，我们为您赠送<span style="color: #ff8400">黄金优惠券</span>。</p>
                <i class="yhqIcon_2 yhqIcon"></i>
            </li>
            <li>
                <div>一点便宜流程</div>
                <p>在售楼处已经签订完合同交完定金，之后联系小二管家，我们为您提供<span style="color: #ff8400">助力优惠券</span>。</p>
                <i  class="yhqIcon_3 yhqIcon"></i>
            </li>
        </ul>
    </div>
    <!--小二管家优惠券领取弹出框-->
    <div class="heibuKeeper2 heibuKeeper">
        <!--黄金优惠券-->
        <div class="yhjtc2 yhjtc" >
            <div class="yhjKuang">
                <!--<div class="yhjClose"></div>-->
                <s class="x yhjClose"><img src="https://static.fangxiaoer.com/web/images/sy/house/alertX.png" /></s>
                <h3>领取黄金优惠券</h3>
                <input type="text" maxlength="11" placeholder="请输入手机号" class="yhjPhone" id="phone1">
                <div>
                    <input type="text" maxlength="6" placeholder="请输入验证码" class="yhjCode" id="code1">
                    <label class="fxe_ReSendValidateCoad yhjHq1">获取验证码</label>
                    <label class="fxe_validateCode"></label>
                </div>
                <div>
                    <input type="checkbox" class="yhjD" checked="checked">
                    <p>我已阅读并接受<a style="color: #ff5200" target="_blank" href="https://info.fangxiaoer.com/About/copyright">《房小二网用户服务协议》</a></p>
                </div>
                <div class="yhjBtnDl yhjBtnD">领取</div>
            </div>

        </div>
    </div>


    <div class="heibuKeeper4 heibuKeeper">
        <!--领取成功-->
        <div class="yhjtc4 yhjtc" >
            <div class="yhjtc4_icon">
                <div class="yyDkImg"><img src="https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkTcicon.jpg"></div>
                <p>恭喜您领取成功</p>
            </div>
            <p>稍后小二管家会联系您。</p>
            <div class="yhjBtnD3  yhjBtnD">确认</div>
        </div>
    </div>



    <div class="content1 contenter">
        <div class="head"><img src="https://static.fangxiaoer.com/web/images/sy/need/keeper/3.png"/>服务承诺</div>
        <ul>
            <p style="color: #ff8400;font-size: 16px;text-align: center;">小二承诺：买贵补10倍差价；优质服务：专业购房管家一对一带看；专属礼包：购房优惠券2千-10万不等；投诉热线：024-88905000转8028。</p>
        </ul>
    </div>
    <div class="content2 contenter">
        <div class="head"><img src="https://static.fangxiaoer.com/web/images/sy/need/keeper/4.png"/>给买房人的建议？</div>
        <ul>
            <li>买房是一个关系重大的交易，一个家庭几十年的财富积累和命运，不可轻信他人，找个真心帮你的人很关键，那个人最少需要满足两点。 </li>
            <li>1） 首先要真的懂，房小二网推出的小二管家不同，他们隶属于佳恒集团，佳恒集团根植于房地产传媒已经20年了，他们都是经过佳恒集团层层考核通过的房产领域的专家，才可以有资格对买房人提供建议。</li>
            <li>2） 其次是态度，真有服务大众的公益心。我们小二管家就是这样的一群人！</li>
        </ul>
    </div>
    <div class="content1 contenter">
        <div class="info">
            <p><span>特别提醒:</span>买房子，越早联系我们越好。获得额外优惠是一项系统工程，需要在买房前就要开始准备。更多问题请联系我们管家。</p>
        </div>
    </div>
    <div class="freePhone">
        <p>小二管家帮你砍价！砍不下来，不要钱！（2千-10万不等）<span>免费通话</span></p>
    </div>
    <script type="text/javascript">
        var projectId = "18";
        //弹出框
        $(".freePhone span").click(function(){
            $("#FreefonePhone").val("");
            $("#FreefoneCode").val("");
            $(".FreefonePopup dl dd .error").text("");
            $(".phoneForm").fadeIn();
        })
    </script>
    <!--小二管家免费电话-->
    <div class="w ">
        <div th:include="house/detail/fragment_freefone::Freefone"></div>
        <div class="cl"></div>
    </div>
    <div class="cl"></div>
    <!--底部1-->
    <div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>

    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>

    <div class="cl">
    </div>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>

    <!--小二管家优惠券-->
    <script type="text/javascript">

        $(".yhjBtnD3").click(function () {
            $(".heibuKeeper").hide()
        })
        var houseKeeper;
        $(".yhqIcon_2").click(function () {
            $(".yhjKuang>h3").text("领取黄金优惠券");
//            console.log( $(".yhjKuang>p").text("领取黄金优惠券"))
            houseKeeper = "黄金优惠券"
            $(".heibuKeeper2").show();
            $(".yhjtc2").show();

        })
        $(".yhqIcon_3").click(function () {
            $(".yhjKuang>h3").text("领取助力优惠券");
            houseKeeper = "助力优惠券"
            $(".heibuKeeper2").show();
            $(".yhjtc2").show();

        })


        $(".yhjClose").click(function () {
            $(".heibuKeeper").hide()
        })
        sy_confirm.init(1,false);
//        获取验证码
        $(".yhjHq1").click(function () {
            if(sy_confirm.phone($("#phone1").val())==true){
                console.log('新验证码验证成功！');
                sy_confirm.Code($("#phone1").val()).then(res => {
                    console.log('发送新验证码验证成功！');
                    console.log(res);
                    if (res == true) {
                      sy_confirm.timeWait();
                    }                    
                }).catch(err => {
                    console.log('发送新验证码验证失败！');
                    console.log(err)
                })
            }
        })

//        确认领取
        $(".yhjBtnDl").click(function () {
            if(sy_confirm.phone($("#phone1").val())==true){
                if(sy_confirm.code($("#code1").val())==true){
                        $.ajax({
                            type:"post",
                            url:"https://ltapi.fangxiaoer.com/apiv1/active/saveGuide",
                            async:false,
                            data:{
                                phone:$("#phone1").val(),
                                code:$("#code1").val(),
                                region:"无",
                                budget:"无",
                                housetype:"无",
                                area:"无",
                                italy:"来源sy站小二管家领取优惠券弹窗  "+houseKeeper,
                                type:"1"
                            },
                            success:function(data){
                                console.log(data)
                                if (data.status==1){
                                    $(".heibuKeeper4").show();
                                    $(".yhjtc2").hide()
                                }else {
                                    alert(data.msg)

                                }
                            },error:function(data){

                            }
                        });
                }
            }
        })

    </script>
</form>
</body>
</html>
