<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <link href="https://static.fangxiaoer.com/web/styles/main.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/vipHouse/vipStyle.css?t=20181225"/>

    <title></title>
</head>

<body>

<div class="heibuVip" th:fragment="heibuVIP">
    <!--支付成功弹窗-->
    <div class="paySuccess">
        <div class="vip_close" onclick="javaScript:closeHeibu(0);"><img src="https://static.fangxiaoer.com/web/images/ico/BtnClose.png"/></div>
        <div class="heibuVipMain">
            <h3>恭喜您支付成功！</h3>
            <p>您已成功购买<span>房小二网VIP会员服务</span></p>
            <div class="btnSuccess" onclick="javaScript:returnVip();" style="cursor:pointer">完成</div>
        </div>

    </div>

    <!--小二币支付不足-->
    <div class="XebNo">
        <div class="vip_close" onclick="javaScript:closeHeibu(3);"><img src="https://static.fangxiaoer.com/web/images/ico/BtnClose.png"/></div>
        <div class="heibuVipMain">
            <h3>对不起，您的小二币余额不足</h3>
            <p>请您先进行充值</p>
            <div class="btnXebNo"><a href="https://my.fangxiaoer.com/wallets" target="_blank">去充值</a></div>
            <p>点击去充值跳转到<span>我的钱包页面</span></p>
        </div>

    </div>

    <!--使用小二币付款-->
    <div class="XebYes">
        <div class="vip_close" onclick="javaScript:closeHeibu(2);"><img src="https://static.fangxiaoer.com/web/images/ico/BtnClose.png"/></div>
        <div class="heibuVipMain">
            <h3>使用小二币付款</h3>
            <p>您将支付<span>98</span>个小二币</p>
            <div class="btnXebYes" onclick="javaScript:toPayVip(4);" style="cursor:pointer">确认支付</div>
        </div>

    </div>

    <!--经纪人拒绝使用-->
    <div class="agentNo" style="height: 160px;margin-top: -80px;" hidden="hidden">
        <div class="heibuAgentNo">
            <h3>经纪人不能开通VIP会员！</h3>
            <div class="btnXebYes" onclick="javaScript:closeHeibu()" style="cursor:pointer">确认</div>
        </div>
    </div>

    <div class="agentNo loguot" style="height: 160px;margin-top: -80px;" hidden="hidden">
        <div class="heibuAgentNo">
            <h3>账号失效，请重新登录！</h3>
            <div class="btnXebYes" onclick="javaScript:closeHeibu(4)" style="cursor:pointer">确认</div>
        </div>
    </div>

    <!--支付方式选择-->
    <div class="PayWay" id="payWay">
        <div class="vip_close" onclick="javaScript:closeHeibu(1);" ><img src="https://static.fangxiaoer.com/web/images/ico/BtnClose.png"/></div>
        <div class="heibuVipMain">
            <h3>选择付款方式</h3>
            <p>您将支付<span>￥</span><b>98</b></p>
            <div class="PayWayGou">
                <input type="checkbox" class="payGou">
                <p>我已阅读<span class="vipTxt-btn" onclick="javaScript:ccc();">《房小二网VIP会员协议》</span></p>
            </div>
            <ul>
                <li>
                    <div id="wxcode"></div>
                    <p>
                        <i><img src="https://static.fangxiaoer.com/web/images/sy/vip/vipTc_13.jpg" alt=""/></i>
                        微信扫码付款
                    </p>
                </li>
                <li>
                    <div><img src="https://static.fangxiaoer.com/web/images/sy/vip/vipTc_03.jpg" alt=""/></div>
                    <p onclick="javaScript:confirmPay();" style="cursor:pointer">使用小二币付款</p>
                </li>
                <li>
                    <div id="zfbcode"></div>
                    <p>
                        <i><img src="https://static.fangxiaoer.com/web/images/sy/vip/vipTc_16.jpg" alt=""/></i>
                        支付宝扫码付款
                    </p>
                </li>
            </ul>
            <p style="font-size: 14px;margin-top: 20px">此窗口将于<b style="color: #ff5200;font-size: 20px;padding: 0 10px"
                                                                id="countDown">120</b>秒关闭，请您尽快完成支付</p>
        </div>


        <script type="text/javascript">
            $(".payGou").change(function () {
                $(".PayWay ul").toggle()
            })
        </script>
    </div>


    <!--vip协议-->
    <div class="vipText" hidden="hidden">
        <div class="vip_close" onclick="javaScript:closeHeibu(5);" ><img src="https://static.fangxiaoer.com/web/images/ico/BtnClose.png"/></div>
        <h3>欢迎您使用房小二网vip服务！</h3>
        <div class="vipTextMain">　　
            <p>为使用房小二网vip服务（简称为：本服务），您应当阅读并遵守《房小二网vip服务协议》（简称为：本协议）。请您务必审慎阅读、充分理解各条款内容，特别是免除或限制责任的相应条款，以及开通并选择接受或不接受。</p>
            <p>除非您已阅读并接受本协议所有条款，否则您无权使用本服务。您对本服务的购买等获取行为及登录、查看等任何使用行为即视为您已阅读并同意本协议的约束。</p>
            <p>如果您未满18周岁，请在法定监护人的陪同下阅读本协议，并特别注意未成年人使用条款。</p>
            <p>一、协议的范围</p>　
            <p>协议适用主体范围:本协议是您与房小二网关于您使用本服务所订立的协议。</p>
            <p>二、权利义务</p>　
            <p>2.1【关于收费】</p>　
            <p>本服务是房小二网提供的收费服务，您须在按照本服务的收费标准支付相应费用后，方可使用本服务。</p>　　
            <p>房小二网可能会根据本服务的整体规划，对本服务的收费标准、方式等进行修改和变更，前述修改、变更，房小二网将在相应服务页面进行展示。您若需要获取、使用本服务，请先提前了解清楚当时关于本服务的收费标准、方式等信息。</p>　　
            <p>2.2【服务开通】</p>　　
            <p>您应该通过房小二网指定的包括但不限于微信、支付宝支付方式、今后房小二网指定方式在依约支付一定费用后开通本服务。本服务开通之后，不可进行转让。</p>　　
            <p>您理解并同意：为了保障手机用户帐户安全，用户开通房小二网vip服务业务请使用微信或支付宝方式进行支付，如在支付过程中使用微信/支付宝支付、因其他因素造成的未支付或您以扣费，房小二网未收到对应支付金额，请您改用对于另一种进行支付，房小二网采取的相关措施导致您可能无法开通本服务或可能给您造成损失的，您同意自行承担相关责任和损失。</p>　　
            <p>2.3【服务期限】</p>　　
            <p>本服务的服务期限以选择并支付相应服务费用的期限为准，您也可以登陆房小二网充值中心查询。</p>　　
            <p>2.4【五不准】</p>　
            <p>您在使用本服务时不得利用本服务从事以下行为，包括但不限于：</p>　　　
            <p>（1）发布、传送、传播、储存违反国家法律、危害国家安全统一、社会稳定、公序良俗、社会公德以及侮辱、诽谤、淫秽、暴力的内容；</p>　　
            <p>（2）发布、传送、传播、储存侵害他人名誉权、肖像权、知识产权、商业秘密等合法权利的内容；</p>　　
            <p>（3）虚构事实、隐瞒真相以误导、欺骗他人；</p>　　
            <p>（4）发表、传送、传播广告信息及垃圾信息；</p>　　
            <p>（5）从事其他违反法律法规、政策及公序良俗、社会公德等的行为。</p>　　
            <p>2.5【用户禁止行为】</p>　　
            <p>本服务仅供您个人使用，除非经房小二网书面许可，您不得进行以下行为：</p>　　
            <p>（1）通过本服务发布包含广告、宣传、促销等内容的信息；</p>　　
            <p>（2）将本服务再次许可他人使用；</p>　　
            <p>（3）其他未经房小二网书面许可的行为。</p>　　
            <p>2.6【对自己行为负责】</p>　　
            <p>您充分了解并同意，您必须为自己注册账号下的一切行为负责，包括但不限于您所发表的任何内容以及由此产生的任何后果。您应对本服务中的内容自行加以判断，并承担因使用内容而引起的所有风险，包括因对内容的正确性、完整性或实用性的依赖而产生的风险。</p>　　
            <p>2.7【服务的变更、中止或终止】　　
            <p>您充分了解并同意，由于互联网服务的特殊性，房小二网可能会按照相关法规、双方约定或在其他必要时，中止或终止向您提供本服务，届时，房小二网会依法保护您的合法权益。</p>
            <p>三、【违约责任】</p>
            <p> 3.1【对中止服务的处理】</p>
            <p>如果房小二网发现或收到他人举报您有违反本协议任何行为的，房小二网有权依法进行独立判断并采取技术手段予以删除、屏蔽或断开相关的信息。同时，房小二网有权视您的行为性质，对您采取包括但不限于暂停或终止本服务、追究您法律责任等措施，房小二网也无需向您退还任何费用，而由此给您带来的损失，由您自行承担，造成房小二网损失的，您也应予以赔偿。</p>　　
            <p>3.2【对第三方损害的处理】</p>　　
            <p> 您有违反本协议任何行为，导致任何第三方损害的，您应当独立承担责任；房小二网因此遭受损失的，您也应当一并赔偿。</p>
            <p>四、其他</p>　
            <p>　4.1【协议的生效】</p>　
            <p>　您使用本服务即视为您已阅读并同意受本协议的约束。</p>　
            <p>　4.2【适用法律】</p>　
            <p>　本协议的成立、生效、履行、解释及纠纷解决，适用中华人民共和国大陆地区法律（不包括冲突法）。</p>　　
            <p>4.3【争议解决】</p>　　
            <p>若您和房小二网之间发生任何纠纷或争议，首先应友好协商解决；协商不成的，您同意将纠纷或争议提交本协议签订地有管辖权的人民法院管辖。</p>　　
            <p>4.4【条款标题】</p>　　
            <p>本协议所有条款的标题仅为阅读方便，本身并无实际涵义，不能作为本协议涵义解释的依据。</p>　　
            <p>4.5【条款解释】</p>　　
            <p>本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。</p>
            <p>五、责任免除</p>
            <p>5.1 因战争、自然灾害等导致乙方服务器不能正常运行。</p>
            <p>5.2 因政府行政行为导致乙方不能正常使用。</p>
            <p>5.3 互联网通讯提供商原因导致乙方服务器不能正常接入。</p>
            <p>5.4 因操作平台及应用软件原因导致乙方服务器临时性不能正常运行。</p>
            <p>5.5 因乙方网站遭遇不法攻击导致服务器临时性不能正常运行。</p>
            <p>基于以上原因，导致房小二网站不能正常运行，房小二网不承担任何法律上和其它方式的责任。</p>

        </div>
    </div>


</div>


</body>

</html>
