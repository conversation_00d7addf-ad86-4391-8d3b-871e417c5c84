<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title th:text="${goods.title+'，房小二网积分兑换'}"></title>
    <meta name="keywords" th:content="${goods.title}">
    <meta name="description" content="为答谢广大房仔对房小二网的支持与厚爱，房小二网特推出积分商城供房仔兑换丰富多样的礼品，让大家享受到房小二网给予的实实在在的实惠。">
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fzsc/'+id+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link href="https://static.fangxiaoer.com/web/styles/sy/need/new_goods.css" rel="stylesheet" type="text/css">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>



</head>

<body class="w1210">
<form name="form1" method="post" action="/takeOrder" id="form1">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7"></div>
    <!--搜索栏-->
    <!--<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=6"></div>-->
    <!--“感恩有你”图片-->
    <div id="myCarousel"  class="carousel slide need_top" style=" margin-top:0px !important;background:url(https://static.fangxiaoer.com/web/images/sy/need/ban_3.jpg) no-repeat center"></div>
    <!--页面位置-->
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt;<!--
        <a href="/activities/" target="_blank">房仔中心</a> &gt;-->
        <a href="/pointsMall" target="_blank">积分商城</a> &gt; <th:block th:text="${goods.title}"></th:block></div>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.SuperSlide.2.1.1.js"></script>
    <style>
        .txtScroll-left{ position:relative; float:right;padding-left:26px;background:url(https://static.fangxiaoer.com/web/images/sy/need/ico_integral.jpg) left no-repeat}
        .txtScroll-left .hd{display:none}
        .txtScroll-left .hd ul{ float:right; overflow:hidden; zoom:1; }
        .txtScroll-left .hd ul li{ float:left; overflow:hidden; margin-right:5px; text-indent:-999px; }
        .txtScroll-left .hd ul li.on{ background-position:0 0; }
        .txtScroll-left .bd{ width:500px; overflow:hidden;    }
        .txtScroll-left .bd ul{ overflow:hidden; zoom:1; }
        .txtScroll-left .bd ul li{ float:left;text-align:left; _display:inline;width:250px;height: 42px}
        .txtScroll-left .bd ul li span{ color:#999;  }
        .infoList i{color:#ff5200}
    </style>
    <div class="w sort">
        <div class="txtScroll-left">
            <div class="hd">
            </div>
            <div class="bd">
                <ul class="infoList">
                    <li th:each="midUser:${midUser}">
                        <th:block th:text="${'用户'+midUser.CommberPhone+'兑换'}"></th:block>
                        <i th:text="${midUser.goodsName}"></i>
                        <th:block th:text="${midUser.count+'个'}"></th:block>
                    </li>
                </ul>
            </div>
        </div>
        <span>礼品分类：</span>
        <a href="/pointsMall"  class='hover'>全部</a>
        <a href="/pointsMall/1" >文化用品</a> 丨
        <a href="/pointsMall/2" >家居装饰</a> 丨
        <a href="/pointsMall/3" >数码家电</a> 丨
        <a href="/pointsMall/4" >家庭保健</a> 丨

    </div>
    <script type="text/javascript">
        var ary = location.href.split("&");
        jQuery(".txtScroll-left").slide({ titCell: ".hd ul", mainCell: ".bd ul", autoPage: true, effect: "leftLoop", autoPlay: true, vis: 2, delayTime: 700, interTime: 4000 });
    </script>
    <script type="application/javascript">
        var msg = '[[${msg}]]';
        if(msg) alert(msg);
    </script>
    <div class="w">
        <div class="left">
            <div class="view">
                <!--商品编号：--><input type="hidden" name="goodsId" id="goodsId" th:value="${goods.id}">
                <!--sessionId：--><input type="hidden" name="sessionId" id="sessionId" th:value="${#session?.getAttribute('sessionId')}">
                <img class="img" th:src="${goods.pic}" th:alt="${goods.title}"/>
                <div class="txt">
                    <h1 th:text="${goods.title}"></h1>
                    <p>兑换积分：<strong><span class="jf" id="jifen"><th:block th:text="${goods.integration}"></th:block></span>
                        积分 </strong><br><th:block th:text="${'礼品数量：'+goods.goodsNumber+'个'}"></th:block><br>
                        剩余数量：<span class="zs" id="shengyu"th:text="${goods.balance}"></span>个</p>
                    <div class="jifen"><p>数　　量：</p><span id="plus"><img src="https://static.fangxiaoer.com/web/images/my/admin/jian.gif" alt="减" /></span><input name="count" type="text" id="count" value="1" onkeyup="this.value=this.value.replace(/[^\d]/g, '')" onafterpaste="this.value=this.value.replace(/\D/g,'')" /><span id="add"><img src="https://static.fangxiaoer.com/web/images/my/admin/jia.gif" alt="加" /></span><span class="xl_span"> 此商品每人限量<a class="xianliang"><th:block th:text="${goods.number}"></th:block></a>个</span></div>
                    <div class="cl"></div>
                    <div class="btn">
                        <a data-toggle="modal" href="#login" id="Btn_dh1" th:if="${#session?.getAttribute('sessionId') == null}">立即兑换</a>
                        <input type="submit" name="Btn_dh" value="立即兑换" id="Btn_dh" th:if="${#session?.getAttribute('sessionId') != null}"/>
                    </div>
                </div>
            </div>
            <div class="cl"></div>
            <div style="width:970px;float:left">
                <div class="title"><span>礼品描述</span></div>


                    <div class="lpcs">
                        <th:block th:utext="${goods.description}"></th:block>
                    </div>
            </div>
        </div>
        <div class="right"></div>

    </div>



    <div class="cl"></div>
</div>
    <div class="cl"></div>
    <div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
    <div th:include="fragment/fragment::esfCommon_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>
</form>
<input type="hidden" id="goods" value="1">
<div th:include="house/detail/fragment_login::login"></div>
<script language="javascript" type="text/javascript">
    var jifen = parseInt($("#jifen").html());
    var renci = parseInt($("#renci").html());
    $("#count").bind("keyup", function () {
        ISlwNum();
    })
    $("#plus").bind("click", function () {
        var num = parseInt($("#count").val()) - 1;
        $("#count").val(num);
        ISlwNum();
    })
    $("#add").bind("click", function () {
        debugger;
        var num = parseInt($("#count").val()) + 1;
        $("#count").val(num);
        ISlwNum();
    })
    function ISlwNum() {
        debugger;

        var num = parseInt($("#count").val());
        var shengyu = parseInt($("#shengyu").html());
        var xianliang = parseInt($(".xianliang").html());
        if (num > shengyu) {
            $("#count").val(shengyu);
            $("#jifen").html(jifen * shengyu);
            $("#renci").html(renci * shengyu);
        } else if (num < 1) {
            $("#count").val("1");
            $("#jifen").html(jifen * 1);
            $("#renci").html(renci * 1);
        } else if (num > xianliang) {
            $("#count").val(xianliang);
            $("#jifen").html(jifen * xianliang);
            $("#renci").html(renci * xianliang);
            num = parseInt($("#count").val());
            alert(" 此商品 每人最多可兑换" + $(".xianliang").html() + "次")
        }
        if(num>=1 && num<=shengyu ) {
            $("#jifen").html(jifen * num);
            $("#renci").html(renci * num);
        }

    }
</script>
</body>
</html>
