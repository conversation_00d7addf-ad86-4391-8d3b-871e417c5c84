<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>小二管家服务团队，购房咨询师信息 - 房小二网</title>
    <meta name="keywords" content="购房管家,小二管家,购房指导,房源推荐,购房服务,买房优惠,沈阳买房" />
    <meta name="description" content="房小二网小二管家为您购房提供全程一对一贴心服务，包括房源推荐、购房指导、顾问式陪看房、交易磋商、合同签约服务，为你精准制定专属购房计划，比你更懂你自己。" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/need/list.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        $(function () {
            $(".paixu").find("a").click(function () {
                $("#orderby").val($(this).attr("class"));
            });
            if ($("#orderby").attr("value") == "AdvisoryNumber") {
                $("#zxrs").addClass("hover")
            } else {
                $("#cjrs").addClass("hover")
            }
        });
        $(function () {
            $(".info_txt span,.info_txt p,.info_txt div").each(function () {
                var xx = $(this).html();
                $(this).replaceWith(xx);
            })
            $(".info_txt").each(function () {
                if ($(this).height() > 90) {
                    $(this).css({ "height": "90px", "overflow": "hidden" })
                    $(this).find(".chakan2").show()
                }
            })
        })
    </script>
</head>
<body>
<form name="ctl00" method="post" action="" id="ctl00">
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
    <div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt; <a href="/houseKeeper1.htm">小二管家</a></div>
    <div class="main">
        <div class="left">
            <div class="title"><span>400-893-9709</span>小二管家</div>
            <div class="cl"></div>
            <div class="list">
                <ul>
                    <li th:each="xiaoer:${data}">
                        <a th:href="${'https://sy.fangxiaoer.com/agent/second/'+xiaoer.memberId}" target="_blank">
                            <img th:src="${xiaoer.headPic}"><p>
                            <span th:text="${xiaoer.memberName}"></span><br>
                            <th:block th:text="${xiaoer.description}"></th:block></p></a>
                    </li>
                </ul>
            </div>
            <div class="page">
            </div>
        </div>
        <div class="cl"></div>
    </div>
    <div class="cl"></div>
    <!--底部1-->
    <div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>

    <div class="cl"></div>
</form>
</body>
</html>
