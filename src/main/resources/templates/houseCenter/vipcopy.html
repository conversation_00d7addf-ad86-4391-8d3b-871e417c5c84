<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="utf-8"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/vipHouse/vipStyle.css?t=20181225"/>-->
        <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/vipHouse/vipStylecopy.css?t=20181225"/>

<!--    <link rel="stylesheet" type="text/css" href="/css/vipStyle.css"/>-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <title>VIP会员 - 房小二网</title>
    <meta name="keywords" content="VIP会员,房小二网VIP,房小二网服务,房小二网会员"/>
    <meta name="description" content="房小二网VIP为您提供九大权益与专属团队为您保驾护航，98/年 尊享服务即可开通！买房卖房，就上房小二网"/>
</head>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
<!--&lt;!&ndash;搜索栏&ndash;&gt;-->
<!--<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=6"></div>-->
<div class="vIp">
    <div class="vipBanner">
        <p class="p1">只为给您最尊贵的体验和服务</p>
        <p class="p2"><i>享受资深管家服务 </i>私人定制购房方案</p>
        <p class="p3"><i>底价购买心仪房源</i>你我的信任从VIP开始</p>
        <div class="viplogin">
            <img class="hportrait" th:src="${avatar ne null  and avatar ne ''? avatar: 'https://static.fangxiaoer.com/web/images/vipImg/viplogin2.png'}"
                 alt=""  style="cursor:pointer"/>
<!--            alt="" onclick="javaScript:judgeLoginOrPay();" style="cursor:pointer"/>-->

            <p class="vipUsername" th:if="${#strings.isEmpty(userName)}" onclick="javaScript:judgeLoginOrPay();">请登录</p>
            <p class="vipUsername" th:if="${userName}" th:text="${userName}" style="cursor:pointer"></p>

        </div>
        <div class="vipOpen" onclick="javaScript:judgeLoginOrPay();">立即开通/98元尊享服务</div>

        <!--        <p class="vipOpen" th:if="${#strings.isEmpty(userName)}" onclick="javaScript:judgeLoginOrPay();">请登录</p>-->
        <p class="vipOpen"  th:if="${isVip eq 1}" th:text="${'立即续费/您的会员将于 '+vipEndTime}+'到期'" onclick="javaScript:judgeLoginOrPay();"></p>
    </div>
    <div class="vipEquitydiv">
        <div class="vipEquity">
            <h1><i>9</i>大权益</h1>
            <p>
                <span>房小二网</span>
                <span>贴心服务</span>
                <span>省时省力</span>
                <span>又省心</span>
            </p>
        </div>
    </div>
    <div class="bquityDel">
        <ul>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy1.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            01
                        </i>
                        专属方案定制
                    </h1>
                    <p>依据您的需求，量身定制购房方案，挑选符合您需求的楼盘与房源，进行全方位的解读，供您全面了解，择优入手。</p>
                </div>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy2.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            02
                        </i>
                        资深管家服务
                    </h1>
                    <p>为您匹配佳恒集团旗下从事房地产行业2年以上的小二管家，带您破解购房营销套路，规避购房风险，保障购房利益。</p>
                </div>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy3.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            03
                        </i>
                        看房专车接送
                    </h1>
                    <p>根据您的个人安排，灵活预约看房时间，并且全程提供免费的看房车接送服务。</p>
                </div>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy4.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            04
                        </i>
                        远程实景看房
                    </h1>
                    <p>工作忙，加班，不休息，都没关系，我们为您提供实景拍摄的照片与视频，给您带来身临其境的看房体验。</p>
                </div>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy5.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            05
                        </i>
                        底价购房保障
                    </h1>
                    <p>各楼盘最新优惠政策，近期购房成交价格也可查询，承诺底价购房，不多花一分钱。</p>
                </div>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy6.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            06
                        </i>
                        贷款法律援助
                    </h1>
                    <p>商业贷款和公积金贷款最新政策解读，同时配备房小二网法律专家为您答疑解惑。</p>
                </div>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy7.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            07
                        </i>
                        装修分期贷建行独家合作
                    </h1>
                    <p>房小二网联合中国建设银行提供特惠装修分期贷款，缓解您装修房屋的压力。</p>
                </div>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy8.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            08
                        </i>
                        独享VIP优惠券
                    </h1>
                    <p>除享受售楼处底价优惠外，还有房小二网线上近百家楼盘优惠券，由您独享。</p>
                </div>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/qy9.png" alt="">
                <div class="bquityDeldiv">
                    <h1>
                        <i>
                            09
                        </i>
                        会员其他福利
                    </h1>
                    <p>a. 全程陪同签约，规避交易风险；</p>
                    <p> b. 实时跟踪所购房源工程进度与配套动态；</p>
                    <p> c. 亲友回馈：亲友购房免费续享VIP特权。</p>
                </div>
            </li>
        </ul>
    </div>


<!--    专属团队-->
    <div class="vipExclusive">
        <div class="vipBquity vipEquity">
            <h1>专属团队&nbsp; 为您保驾护航</h1>
            <p>
                <span>您买房 </span>
                <span>我支招</span>
                <span>让您底价买好房</span>
            </p>
        </div>
        <div class="equityOpen">
            <ul class="equityOpenul1">
                <li>
                    <span>重复奔波</span>
                    <span>遭遇陷阱</span>
                    <span>屡见不鲜</span>
                </li>
                <li>
                    <img class="vippng" src="https://static.fangxiaoer.com/web/images/vipImg/VIP.png" alt="">
                </li>
                <li>
                    <span>省时</span>
                    <span>省力</span>
                    <span>省心</span>
                </li>
            </ul>
            <ul class="equityOpenul2">
                <li>
                    <p><i>300</i>小时<i>5</i>天事假 </p>
                </li>
                <li class="lijiLi">
                    <span class="lijiSpan">
                        <em onclick="javaScript:judgeLoginOrPay();">立即开通</em>
                    </span>
                    <span th:if="${isVip eq 1}" class="lijiSpan">
                        <em   onclick="javaScript:judgeLoginOrPay();">立即续费</em>
                    </span>

                </li>
                <li class="liji3">
                    <p><i>30</i>分钟内享受资深</p>
                    <p>小二管家服务</p>
                </li>
                <p class="lijiP" th:text="${isVip eq 1 ? '将于'+vipEndTime+'到期' : '98元尊享服务'}">98元尊享服务</p>
            </ul>
        </div>
    </div>

    <div class="seniorDiv">
        <p style="margin-top: 42px">根据需求，量身定制专属购房方案；匹配房小二网资深小二管家，购房无套路；</p>
        <p>对符合需求房源全方位解读，择优入手，让买房更简单。</p>
        <a th:href="@{/houseKeeper1.htm}" target="_blank">
            <span >资深管家&nbsp;等你来选</span>
        </a>
    </div>
        </div>
        <input type="hidden" id="vipType" th:value="${vipPayMoney}">
        <input type="hidden" id="payType" value="1">
        <input type="hidden" id="orderId" value="">
        <input id="userName" th:value="${userName}" type="hidden">
        <input hidden="hidden" id="memberType" th:value="${#session?.getAttribute('memberType')}">
        <input hidden="hidden" id="sessionId" th:value="${#session?.getAttribute('sessionId')}">
    <div class="heibuVip" th:include="houseCenter/vipTc::heibuVIP" style="display: none"></div>

</div>
<div class="wriDiv">
        <ul>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/xj1.png" alt="">
                <div>
                    <span>98元</span>
                </div>
                <p>成为尊贵VIP用户</p>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/xj2.png" alt="">
                <div>
                    <span>30分钟内</span>
                </div>
                <p>享受资深小二管家服务</p>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/xj3.png" alt="">
                <div>
                    <span>1天内</span>
                </div>
                <p>私人订制专属购房方案</p>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/xj4.png" alt="">
                <div>
                    <span style="width: 210px;">提供多个项目  信息供您参考</span>
                </div>
                <p>房产问题全程解答</p>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/xj5.png" alt="">
                <div>
                    <span style="width: 145px;">预约时时看房车</span>
                </div>
                <p>实地考察全程陪同</p>
            </li>
            <li>
                <img src="https://static.fangxiaoer.com/web/images/vipImg/xj6.png" alt="">
                <div>
                    <span>永久免费</span>
                </div>
                <p>房产相关服务</p>
            </li>
        </ul>

</div>
<div class="cl"></div>

<div th:include="house/detail/fragment_login::login"></div>
<div class="cl"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<script type="text/javascript">
    function judgeLoginOrPay() {
        var identifying = $("#userName").val();
        if (identifying) {
            var memberType = $("#memberType").val();
            if (memberType == '2' || memberType == 2 ){
                $(".PayWay").hide();
                $(".heibuVip").show();
                $(".agentNo").show();
                return ;
            }
            toPayVip(99);
        } else {
            $("#login").show();
        }
    };
    $("#loginClose").click(function () {
        $("#login").hide();
        $("#loginzhezhao").hide();
    });

    function confirmPay() { //确认支付
        $(".heibuVip").show();
        $(".PayWay").hide();
        $(".XebYes").show();
    }

    var setTimeoutName;
    var countDownNum;

    function toPayVip(useXeb) {
        var sessionId = $("#sessionId").val()
        if(sessionId == null || sessionId == ''){
            $("#login").show();
            return
        }
        if (useXeb == 4){
            $("#payType").val(4);
        }else {
            $("#payType").val(1);
        }
        $(".heibuVip").show();
        $(".PayWay").show();
        $.ajax({
            type: "post",
            url: "/payVip",
            data: {
                sessionId: sessionId,
                vipType: $("#vipType").val(),
                payType: $("#payType").val(),
            },
            success: function (data) {
                console.log(data)
                if(useXeb == 4){
                    if (data.status == 1.0){
                        $(".heibuVip").show();
                        $(".XebYes").hide();
                        $("#payWay").hide();
                        $(".paySuccess").show();
                    }else if(data.status == 0.0){
                        $(".heibuVip").show();
                        $("#payWay").hide();
                        $(".XebYes").hide();
                        $(".XebNo").show();
                    }else {
                        $(".PayWay").hide();
                        $(".loguot").show();
                    }
                }else {
                    countDownStart();
                    if (data.status == 1.0) {
                        var wcountent = data.content;
                        console.log(wcountent);
                        var wxcode = wcountent.wxPayCode;
                        var acountent = data.content;
                        var zfbcode = acountent.aliPayCode;
                        $("#orderId").val(data.content.orderId);
                        $('#wxcode').qrcode(
                            {
                                width: 100,
                                height: 100,
                                text: wxcode
                            });
                        $('#zfbcode').qrcode(
                            {
                                width: 100,
                                height: 100,
                                text: zfbcode
                            });
                        startTime();
                    } else if (data.status == (-1.0)) {
                        $(".PayWay").hide();
                        $(".loguot").show();

                    }
                }


            },
            error: function () {
                // alert("网络延迟请稍后重试!");
            }
        });

        var index = 0;
        var time = 120;
        function startTime() {
            //定时器
            setTimeoutName = setTimeout(function () {
                startTime();
            }, 2000);
            index++;
            if (index >= 60) {
                clearTimeout(setTimeoutName);
                window.location.href = "/skipToVip";
            }
            getStickOrderStatus();
        }
        function countDownStart() {
            countDownNum = setTimeout(function () {
                countDownStart();
            },1000);
            time--;
            if(time>0){
                $("#countDown").text(time);
            }else {
                clearTimeout(countDownNum);
                window.location.href = "/skipToVip";
            }
        }



        function getStickOrderStatus() {
            var orderId = $("#orderId").val();
            $.ajax({
                type: "post",
                url: "/status",
                data: {
                    orderId: orderId
                },
                async: false,
                dataType: "json",
                success: function (data) {
                    if (data.status == 1) {
                        $("#payWay").hide();
                        $(".heibuVip").show();
                        clearTimeout(setTimeoutName);
                        clearTimeout(countDownNum);
                        $(".paySuccess").show();
                    } else if (data.status == 3) {
                        alert("支付失败！");
                    } else if (data.status == 99) {
                        alert("异常订单！");
                    }
                },
                error: function () {
                    alert("网络延迟请稍后重试!");
                }
            });
        }
    }
    function returnVip() {
        window.location.href="https://my.fangxiaoer.com/";
    }
    function closeHeibu(sign) {
        if (sign == 1){
            $('#wxcode').text('');
            $('#zfbcode').text('');
            clearTimeout(setTimeoutName);
            clearTimeout(countDownNum);
            $(".heibuVip").hide();

        }else if (sign == 2){
            $(".XebYes").hide();
            $(".PayWay").show();

        }else if (sign == 5){
            $(".vipText").hide();
            $(".PayWay").show();

        }
        else if (sign == 3){
            $(".XebNo").hide();
            $(".PayWay").show();
        }else if (sign == 4){
            window.location.href = "/skipToVip";
        }
        else {
            $(".heibuVip").hide();
        }


    }
</script>
<script>
//    点击协议说明，弹出协议具体内容
    function ccc() {
        $(".heibuVip").show();
        $(".vipText").show();
    }
</script>
<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
</html>