<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title>小二活动，会员专属活动 - 房小二网</title>
    <meta name="keywords" content="会员活动,会员生活,会员福利,沈阳买房"/>
    <meta name="description" content="房仔之家是中国房地产传媒行业的第一个粉丝俱乐部组织，是与全社会互动的购房交流平台，是房小二网服务理念的倡导者和传播者，定期举办多样的房仔活动，为房仔粉丝和购房者提供了丰富多样的娱乐生活。"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/fzhd.htm">
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" >
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" >
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="Stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/need/homeAct1.css" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
</head>
<body class="w1210">
<!--表单-->
<!--<form name="form1" method="post" action="homeAct.aspx" id="form1">-->
    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=1"></div>

    <!--看房团图片链接-->
    <a href="https://event.fangxiaoer.com/20170623.htm" target="_blank">
        <div id="myCarousel"  class="carousel slide need_top" style="cursor:pointer; background:url(https://static.fangxiaoer.com/web/images/sy/need/ban_9.jpg) no-repeat center">
        </div>
    </a>
    <!--页面位置-->
    <div class="crumbs">
        您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt;
        <a href="/activities/" target="_blank">房仔中心</a>&nbsp;&gt; 房仔活动
    </div>

    <!--中间显示部分-->
    <div class="container">
        <div class="row">
            <!--中间左侧显示-->
            <div class="left-w span9">
                <!--活动信息-->
                <div class=" news" th:each="news:${news}">
                    <p>
                        <a th:href="${'/activity/'+news.id+'.htm'}" target="_blank" th:text="${news.titleShow}"></a><br/>
                        <span>
                        <i th:text="${news.visitedTimes}"></i>
                        来源： 房小二网  |  <th:block th:text="${'编辑： '+news.author+' '}"></th:block>|  <th:block th:text="${news.updateTime}"></th:block>
                        </span>
                    </p>

                    <div class="left">
                        <a th:href="${'/activity/'+news.id+'.htm'}" target="_blank"><img th:src="${news.pic}" width="252" height="184" th:alt="${news.title}" /></a></div>
                    <div class="right">
                        <a th:href="${'/activity/'+news.id+'.htm'}" target="_blank">
                            全文>>
                        </a>
                        <span th:text="${news.description}"></span>
                    </div>
                    <div class="cl"></div>
                </div>
            </div>
            <!--中间右侧显示-->
            <div class="right-w span3 ">
                <!--广告-->
                <div id="newsAD1_guanggao" class="flash"  th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}"><!--广告-->
                    <ul class="rotaion_list">
                        <li th:each="advert1:${advert1}">
                            <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                            <a th:href="${advert1.TargetUrl}" target="_blank">
                                <img th:src="${advert1.AdFilePath}" alt="">
                            </a>
                        </li>
                    </ul>
                </div>
                <!--房产快讯标题-->
                <div class="title">房产快讯</div>
                <!--房产快讯-->
                <div class="hot" th:include="fragment/fragment::news_flash"></div>
                <!--广告-->


                <!--楼盘视频标题-->
                <div class="title">最新视频</div>
                <!--楼盘视频信息-->
                <div class="video" th:each="v:${video}">
                    <div class="shipin">
                        <a th:href="${'/video/'+v.videoId+'.htm'}" target="_blank">
                            <img th:src="${v.videoPic}" th:alt="v.videoTitle" /></a>
                        <p><a th:href="${'/video/'+v.videoId+'.htm'}" target="_blank" th:text="${v.videoTitle}"></a></p>
                    </div>
                </div>

                <div id="newsAD2_guanggao" class="flash"  th:if="${advert2 ne null and #lists.toList(advert2).size() ne 0}"><!--广告-->
                    <ul class="rotaion_list">
                        <li th:each="advert2:${advert2}">
                            <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                            <a th:href="${advert2.TargetUrl}" target="_blank">
                                <img th:src="${advert2.AdFilePath}" alt="">
                            </a>
                        </li>
                    </ul>
                </div>

            <div class="cl"></div>
                <div class="cl"></div>
            </div>
        </div>
    </div>
    <div class="cl"></div>

    <!--分页-->
    <div class="page">
        <!--<input name="page"/>-->
        <div class="page">
            <div id="Pager1">
                <div th:include="fragment/page :: page "></div>
            </div>
        </div>

    </div>

    <!--底部1-->
    <div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>

    <div th:include="fragment/fragment::common_meiqia"></div>
    <div th:include="fragment/fragment::tongji"></div>


</body>
</html>