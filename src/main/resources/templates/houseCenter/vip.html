<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="utf-8"/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/vipHouse/vipStyle.css?t=20181225"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css" />
    <title>VIP会员</title>
    <meta name="keywords" content="“专属管家”、“积分特权”、“VIP赠予”、“贵宾通道”"/>
    <meta name="description" content="加入房小二网会员，尽享“专属管家”、“积分特权”、“VIP赠予”、“贵宾通道”服务。仅需66元/年。此外还享受线上购房补贴，最高10万补贴权益。"/>
</head>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
<!--&lt;!&ndash;搜索栏&ndash;&gt;-->
<!--<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=6"></div>-->
<div class="vIp">
    <div class="vipKuang">
        <div class="vipLogin">
            <i class="vipIcon" style="position: absolute;cursor:pointer;left: 185px;top: 161px;"><a target="_blank" href="https://sy.fangxiaoer.com/skipToVip"><img th:if="${isVip eq 1}" style="cursor:pointer" src="https://static.fangxiaoer.com/web/images/my/admin/vipIcon.png" alt=""></a></i>

            <div class="loginImg">
                <img th:src="${avatar ne null ? avatar: 'https://static.fangxiaoer.com/web/images/sy/vip/viplogin.png'}"
                     alt="" onclick="javaScript:judgeLoginOrPay();" style="cursor:pointer"/>

            </div>
            <p th:if="${#strings.isEmpty(userName)}" onclick="javaScript:judgeLoginOrPay();" style="cursor:pointer">请登录</p>
            <p th:if="${userName}" th:text="${userName}" style="cursor:pointer"></p>
            <p th:if="${isVip eq 1}" th:text="${'您的会员将于 '+vipEndTime}+'到期'"></p>
            <div class="vipLoginBott">
                <p>加入房小二网会员</p>
                <p>¥ 66元/年</p>
            </div>
        </div>
        <input type="hidden" id="vipType" th:value="${vipPayMoney}">
        <input type="hidden" id="payType" value="1">
        <input type="hidden" id="orderId" value="">
        <input id="userName" th:value="${userName}" type="hidden">
        <!--二维码-->
        <!--<div id="tanchuang" style="width: 550px;height: 550px;color: #3498DB" hidden="hidden">-->
        <!--<div id="wxcode"></div>-->
        <!--<div id="zfbcode"></div>-->
        <!--</div>-->
        <input hidden="hidden" id="memberType" th:value="${#session?.getAttribute('memberType')}">

        <div class="vipMain">
            <div class="title title1">
                <img th:if="${isVip ne 1}"
                     src="https://static.fangxiaoer.com/web/images/sy/vip/title1.png" alt="开通会员" class=""/>
                <img th:if="${isVip eq 1}"
                     src="https://static.fangxiaoer.com/web/images/sy/vip/title2.png" alt="续费会员"/>
            </div>
            <div class="vipMain-con2">
                <!--<a class="normolIcon" id="MEIQIA-BTN" ><i><img src="https://static.fangxiaoer.com/web/images/sy/vip/vipIcon1.png"
                                   alt=""/></i>常见问题</a>-->
                <div style="margin-left: 115px;"><i><img src="https://static.fangxiaoer.com/web/images/sy/vip/vipIcon2.png" alt=""/></i>400-893-9709
                </div>
            </div>
            <div class="vipMain-con3">
                <p>加入房小二网会员，尽享“专属管家”、“积分特权”</p>
                <p>“VIP赠予”、“贵宾通道”服务。此外还享受</p>
                <p>优质房源，仅需要<span><b>66</b>元/年</span>。</p>
            </div>
            <div class="vipBtn" onclick="javaScript:judgeLoginOrPay();">立即订购</div>
        </div>
        <ul class="vipBottom">
            <li>
                <i class="vipIcon vipIcon1"></i>
                <h4>专属管家</h4>
                <p>1对1购房服务、全程陪同</p>
            </li>
            <li>
                <i class="vipIcon vipIcon2"></i>
                <h4>积分特权</h4>
                <p>会员优先享1.5倍积分</p>
            </li>
            <li>
                <i class="vipIcon vipIcon3"></i>
                <h4>Vip赠予</h4>
                <p>钻石优惠券，赠送楼市报12期</p>
            </li>
            <li>
                <i class="vipIcon vipIcon4"></i>
                <h4>贵宾通道</h4>
                <p>尊享免排队，优先参与等活动特权</p>
            </li>
            <li>
                <i class="vipIcon vipIcon5"></i>
                <h4></h4>
                <p style="padding-top: 40px;">更多服务，敬请期待</p>
            </li>
            <li class="cl"></li>
        </ul>
        <div class="cl"></div>
        <a data-toggle="modal" href="#login" id="Btn_dh1"></a>
    </div>
    <div class="heibuVip" th:include="houseCenter/vipTc::heibuVIP" style="display: none"></div>

    <!--&lt;!&ndash;底部1&ndash;&gt;-->
    <!--<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>-->
    <!--<div th:include="fragment/fragment::common_meiqia"></div>-->
    <!--<div th:include="fragment/fragment::tongji"></div>-->
    <!--<div th:include="fragment/fragment::commonFloat"></div>-->

</div>
<div class="cl"></div>

<div th:include="house/detail/fragment_login::login"></div>
<div class="cl"></div>
<!--<div th:include="fragment/fragment::common_meiqia"></div>-->
<script type="text/javascript">
    function judgeLoginOrPay() {
        var identifying = $("#userName").val();
        if (identifying) {
            var memberType = $("#memberType").val();
            if (memberType == '2' || memberType == 2 ){
                $(".PayWay").hide();
                $(".heibuVip").show();
                $(".agentNo").show();
                return ;
            }
            toPayVip();
        } else {
            $("#login").show();
        }
    };
    $("#loginClose").click(function () {
        $("#login").hide();
        $("#loginzhezhao").hide();
    });

    function confirmPay() { //确认支付
        $(".heibuVip").show();
        $(".PayWay").hide();
        $(".XebYes").show();
    }

    var setTimeoutName;
    var countDownNum;

    function toPayVip(useXeb) {
        if (useXeb == 4){
            $("#payType").val(4);
        }else {
            $("#payType").val(1);
        }
        $(".heibuVip").show();
        $(".PayWay").show();
        $.ajax({
            type: "post",
            url: "/payVip",
            data: {
                vipType: $("#vipType").val(),
                payType: $("#payType").val(),
            },
            success: function (data) {
                if(useXeb == 4){
                    if (data.status == 1.0){
                        $(".heibuVip").show();
                        $(".XebYes").hide();
                        $("#payWay").hide();
                        $(".paySuccess").show();
                    }else if(data.status == 0.0){
                        $(".heibuVip").show();
                        $("#payWay").hide();
                        $(".XebYes").hide();
                        $(".XebNo").show();
                    }else {
                        $(".PayWay").hide();
                        $(".loguot").show();
                    }
                }else {
                    countDownStart();
                    if (data.status == 1.0) {
                        var wcountent = data.content;
                        var wxcode = wcountent.wxPayCode;
                        var acountent = data.content;
                        var zfbcode = acountent.aliPayCode;
                        $("#orderId").val(data.content.orderId);
                        $('#wxcode').qrcode(
                            {
                                width: 100,
                                height: 100,
                                text: wxcode
                            });
                        $('#zfbcode').qrcode(
                            {
                                width: 100,
                                height: 100,
                                text: zfbcode
                            });
                        startTime();
                    } else if (data.status == (-1.0)) {
                        $(".PayWay").hide();
                        $(".loguot").show();

                    }
                }


            },
            error: function () {
                alert("网络延迟请稍后重试!");
            }
        });

        var index = 0;
        var time = 120;
        function startTime() {
            //定时器
            setTimeoutName = setTimeout(function () {
                startTime();
            }, 2000);
            index++;
            if (index >= 60) {
                clearTimeout(setTimeoutName);
                window.location.href = "/skipToVip";
            }
            getStickOrderStatus();
        }
        function countDownStart() {
            countDownNum = setTimeout(function () {
                countDownStart();
            },1000);
            time--;
            if(time>0){
                $("#countDown").text(time);
            }else {
                clearTimeout(countDownNum);
                window.location.href = "/skipToVip";
            }
        }



        function getStickOrderStatus() {
            var orderId = $("#orderId").val();
            $.ajax({
                type: "post",
                url: "/status",
                data: {
                    orderId: orderId
                },
                async: false,
                dataType: "json",
                success: function (data) {
                    if (data.status == 1) {
                        $("#payWay").hide();
                        $(".heibuVip").show();
                        clearTimeout(setTimeoutName);
                        clearTimeout(countDownNum);
                        $(".paySuccess").show();
                    } else if (data.status == 3) {
                        alert("支付失败！");
                    } else if (data.status == 99) {
                        alert("异常订单！");
                    }
                },
                error: function () {
                    alert("网络延迟请稍后重试!");
                }
            });
        }
    }
    function returnVip() {
        window.location.href="https://my.fangxiaoer.com/";
    }
    function closeHeibu(sign) {
        if (sign == 1){
            $('#wxcode').text('');
            $('#zfbcode').text('');
            clearTimeout(setTimeoutName);
            clearTimeout(countDownNum);
            $(".heibuVip").hide();

        }else if (sign == 2){
            $(".XebYes").hide();
            $(".PayWay").show();

        }else if (sign == 5){
            $(".vipText").hide();
            $(".PayWay").show();

        }
        else if (sign == 3){
            $(".XebNo").hide();
            $(".PayWay").show();
        }else if (sign == 4){
            window.location.href = "/skipToVip";
        }
        else {
            $(".heibuVip").hide();
        }


    }
</script>
<script>
//    点击协议说明，弹出协议具体内容
    function ccc() {
//        alert(1);
        $(".heibuVip").show();
        $(".vipText").show();

    }
</script>
<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
</body>

</html>