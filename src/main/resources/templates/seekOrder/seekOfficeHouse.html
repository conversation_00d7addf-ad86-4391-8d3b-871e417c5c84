<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title></title>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20190112" rel="stylesheet" type="text/css"/>
    <link href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20191115" rel="stylesheet" type="text/css"/>
    <!--    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20190112">-->
    <!--    <link href="https://static.fangxiaoer.com/web/styles/sy/maifang_right/newHouse.css?t=20211123" rel="stylesheet" type="text/css">-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/modal.js"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/tc/tc.js"></script>
    <script src="https://static.fangxiaoer.com/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <!--    <link href="https://static.fangxiaoer.com/js/tc/tc.css?v=20190112" rel="stylesheet" type="text/css" />-->
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/house/contrastHouse.js?v=20240510" type="text/javascript"></script>
    <script src="https://static.fangxiaoer.com/js/verify.js" type="text/javascript"></script>
    <!--求租求购-->
    <link href="https://static.fangxiaoer.com/m/resources/seekOrder/seekOrder.css" rel="stylesheet" type="text/css">
</head>
<body>

<!--引入头部导航栏-->
<!--<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>-->
<div id="head2017" th:include="fragment/fragment::firstNav"></div>


<div class="main">
    <!--tap切换-->
    <div th:include="fragment/fragment::seekOrderTap" th:with="seek=5"></div>

    <!--条件搜索-->
    <div id="option" style="margin-top: 50px;">
        <ul>
            <li>
                <p>供求：</p>
                <a th:each="demandType:${demandType5}" th:text="${#strings.toString(demandType.name).replaceAll('全部','不限')}"
                   th:href="${demandType.url}" th:class="${demandType.selected}? 'hover':''"></a>
            </li>

            <li>
                <p>位置：</p>
                <a th:each="region:${region}" th:text="${#strings.toString(region.name).replaceAll('全部','不限')}"
                   th:href="${region.url}" th:class="${region.selected}? 'hover':''"></a>
            </li>

            <li>
                <p>面积：</p>
                <a th:each="area:${area5}" th:text="${#strings.toString(area.name).replaceAll('全部','不限')}"
                   th:href="${area.url}" th:class="${area.selected}? 'hover':''"></a>
                <div id="Search_PriceDomOk">
                    <label>
                        <input name="minArea" id="minArea" maxlength="5" type="text" th:value="${minArea}">
                        -
                        <input name="maxArea" id="maxArea" maxlength="5" type="text" th:value="${maxArea}"> m²
                        <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1"
                               id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;"
                               type="button">
                    </label>
                    <script type="text/javascript">
                        function __doPostBack(pager1, page) {
                            var url = window.location.pathname;
                            if (pager1 == "Search$Btn_Search1") {
                                var areaBegin = $("#minArea").val();
                                var areaEnd = $("#maxArea").val();
                                var ref1 = url.replace(/-lm[0-9]\d*/, ''); //k最小值
                                var ref2 = ref1.replace(/-hm[0-9]\d*/, '');  //x最大值
                                if (parseInt(areaEnd) < parseInt(areaBegin)) {
                                    areaEnd = [areaBegin, areaBegin = areaEnd][0];
                                }
                                if (areaBegin != "" && areaBegin != 0 && url.indexOf("oa") == -1)
                                    ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/') + "-lm" + areaBegin;
                                if (areaEnd != "" && url.indexOf("oa") == -1)
                                    ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/') + "-hm" + areaEnd;
                                if (areaBegin != "" && areaBegin != 0 && url.indexOf("oa") != -1)
                                    ref2 = ref2.replace(/-oa[0-9]\d*/, '').replace(/\/oa[0-9]\d*/, '\/') + "-lm" + areaBegin;
                                if (areaEnd != "" && url.indexOf("oa") != -1)
                                    ref2 = ref2.replace(/-oa[0-9]\d*/, '').replace(/\/oa[0-9]\d*/, '\/') + "-hm" + areaEnd;
                                location.href = ref2;
                            }
                        }
                    </script>
                    <script type="text/javascript">
                        function price(priceIdName) {
                            $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
                            $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
                            var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
                            var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
                            if (num1 == "" && num2 != "") {
                                $("#" + priceIdName + " input").eq(0).val("0");
                                $("#" + priceIdName + " input").eq(2).show();
                            } else if (num2 == "" && num1 != "") {
                                $("#" + priceIdName + " input").eq(2).show();
                            } else if (num1 != "" || num2 != "") {
                                $("#" + priceIdName + " input").eq(2).show();
                            } else {
                                $("#" + priceIdName + " input").eq(2).hide();
                            }
                        }

                        price("Search_PriceDomOk");
                        $("#Search_PriceDomOk input").keyup(function () {
                            price("Search_PriceDomOk");
                        })
                        $("#Search_PriceDomOk").keydown(function (event) {
                            if (event.keyCode == 13) {
                                // $("#Search_Btn_Search1").click()
                            }
                        });
                    </script>
                </div>
            </li>

        </ul>
    </div>

    <div class="seekContent">
        <div class="seekTop">小二为你找到 <i th:text="${msg}">14</i> 个符合条件的购房意向</div>
        <div class="seekMain">

            <div class="seekli" th:if="${dataList ne null and #lists.size(dataList) > 0}" th:each="data : ${dataList}">
                <div class="ski1" th:text="${data.title}">求购浑南区200m²写字楼</div>
                <div class="ski2">
                    <div class="ski2m" th:text="${data.description}">
                        我希望在铁西区求购一套一居室，价位在150万以内，高层洋房都可以我希望在铁西区求购一套一居室，价位在150万以内，高层洋房都可以我希望在铁西区求购一套一居室，价位在高层洋房都可以我希望在铁西区求购一套一居室
                    </div>
                    <div class="skmore">展开<i></i></div>
                </div>
                <div class="ski6">
                    <div class="ski2m" th:text="${data.description}">
                        我希望在铁西区求购一套一居室，价位在150万以内，高层洋房都可以我希望在铁西区求购一套一居室，价位在150万以内，高层洋房都可以我希望在铁西区求购一套一居室，价位在高层洋房都可以我希望在铁西区求购一套一居室
                    </div>
                    <div class="skhide">收起<i></i></div>
                </div>
                <div class="ski3">
                    <th:block th:if="${data.label ne null}" th:each="label : ${#strings.arraySplit(data.label, ',')}">
                        <i th:text="${label}">地铁房</i>
                    </th:block>
                </div>
                <div class="ski4">面积 <span><i th:text="${data.minArea + '㎡-' + data.maxArea + '㎡'}">100㎡-300㎡</i></span></div>
                <div class="ski5">
                    <span th:text="${data.userName}">李先生</span>
                    <span class="skip">获取电话</span>
                    <div class="ski5_i">18624066224</div>
                </div>
            </div>

            <!--缺省页-->
            <div class="seekNull" th:unless="${dataList ne null and #lists.size(dataList) > 0}">
                <img src="https://static.fangxiaoer.com/m/resources/seekOrder/a3.png">
                <span>对不起，小二什么都没搜到，请搜索其他内容试一试吧！</span>
            </div>

            <!--实名认证弹窗-->
            <div class="seekbg"></div>
            <div class="seekCheck">
                <span></span>
                <img src="https://static.fangxiaoer.com/m/resources/seekOrder/a4.png">
                <div class="seekc1">您还未实名认证，需要认证后才能操作</div>
                <div class="seekc2">账号实名认证</div>
            </div>

        </div>
    </div>

    <div class="cl"></div>
    <div class="page">
        <div th:include="fragment/page :: page"></div>
    </div>
    <div class="cl"></div>

</div>

<script type="text/javascript" th:inline="javascript">
    var sessionId = [[${session.muser}]];
    var authenticationStatus = [[${session.authenticationStatus}]];  //0-未认证 1-认证成功  2-认证失败
    console.log('sessionId: ' + sessionId)
</script>

<script type="text/javascript" src="/js/seekOrder.js"></script>
<div th:include="house/detail/fragment_login::login"></div>
<div th:include="fragment/fragment:: footer_list"></div>
<div th:include="fragment/fragment::tongji"></div>

</body>
</html>
