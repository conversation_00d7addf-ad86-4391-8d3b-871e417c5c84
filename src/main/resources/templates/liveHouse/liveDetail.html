<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>沈阳房产网,沈阳房产,沈阳房地产信息服务平台 - 房小二网</title>
    <meta name="keywords" content="沈阳房产网,沈阳二手房,沈阳房价,沈阳房产,沈阳租房" />
    <link rel="icon" href="/favicon.ico" type="image/x-icon"/>
    <meta property="wb:webmaster" content="baed630db1459dfa" />
    <meta name="description" content="房小二网是沈阳房产专业门户网站,为您提供沈阳房价,沈阳租房,沈阳二手房等及时信息,定期举办沈阳房交会,同时发布沈阳房产资讯和最新学区、贷款等房产政策解读。" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20181019"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="/css/liveHouse.css" />
    <style>
        #showP{min-width: 1170px;display: block;margin: 0 auto;width: 100%;min-height: 800px}
    </style>
<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" ></div>
<iframe th:src="${'https://live.fangxiaoer.com/live/tvchat-' + weizan.get(0).id}" frameborder="0" scrolling="no" id="showP"></iframe>
<!--底部1-->
<div th:include="fragment/fragment:: footer_list"></div>
<!--<div th:include="fragment/fragment::common_meiqia"></div>-->
<div th:include="fragment/fragment::tongji"></div>
<div class="cl"></div>
<script type="text/javascript">
    //<![CDATA[
    var theForm = document.forms['Form1'];
    if (!theForm) {
        theForm = document.Form1;
    }
    function __doPostBack(eventTarget, eventArgument) {
        if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
            theForm.__EVENTTARGET.value = eventTarget;
            theForm.__EVENTARGUMENT.value = eventArgument;
            theForm.submit();
        }
    }
    //]]>
</script>

</body>
</html>