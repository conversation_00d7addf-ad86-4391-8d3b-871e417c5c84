<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>沈阳房产网,沈阳房产,沈阳房地产信息服务平台 - 房小二网</title>
    <meta name="keywords" content="沈阳房产网,沈阳二手房,沈阳房价,沈阳房产,沈阳租房" />
    <link rel="icon" href="/favicon.ico" type="image/x-icon"/>
    <meta property="wb:webmaster" content="baed630db1459dfa" />
    <meta name="description"
          content="房小二网是沈阳房产专业门户网站,为您提供沈阳房价,沈阳租房,沈阳二手房等及时信息,定期举办沈阳房交会,同时发布沈阳房产资讯和最新学区、贷款等房产政策解读。" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20181019"/>
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="/css/liveHouse.css" />
    <style>
        .noLive{text-align: center;user-select: none;color: #999;    width: 100%;display: block;float: left;}
    </style>
<body>
<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=1"></div>
<div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt;<a href="/houses/" target="_blank">沈阳新房</a>&gt;直播看房</div>
<!--直播列表    -->
<ul class="new_live_ul width1170" >
   <!-- <li class="new_live_li" >
        <a>
            <img alt="" src="https://imageicloud.fangxiaoer.com/middle/project/normal/2023/06/07/082439242.jpg">
            <div class="new_live_type"><i class="live_type live_type1">直播中</i><span>108观看</span></div>
            <h5 class="new_live_txt" >【房小二网直播】带你去了解盛世园田居…</h5>
        </a>
    </li>-->
</ul>

<script type="application/javascript">
    $(document).ready(function() {
        var start = 2; //用于存储数据开始索引
        var count = 12; //每次刷新的数据数量
        var boType = 0
        loadList(1,24)
         function loadList(a,b){
            if(boType == 0){
                $.ajax({
                    url:"/viewWeizanList",
                    data: {
                        page:a,
                        page_size:b
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (data) {
                        data= data.content
                        console.log(data)
                        if(data.length != 0){
                            $(".noLive").hide()
                            for (var i = 0; i<data.length ; i++) {
                                var lstatus = data[i].status
                                console.log(lstatus)
                                var lTxt ;
                                var lIcon
                                if(lstatus == 1 || lstatus == 2){
                                    lTxt = '直播中'
                                    lIcon = 'lIcon2'
                                }else if(lstatus == 0){
                                    lTxt = '回放'
                                    lIcon = 'lIcon3'
                                }else{
                                    lTxt = '未开始'
                                    lIcon = 'lIcon1'
                                }
                                listLi = '<li class="new_live_li" >'+
                                    '  <a href="/liveDetail/'+data[i].id+'.htm">'+
                                    '      <img src="'+data[i].banner+'">'+
                                    '                <div class="new_live_type"><i class="live_type '+lIcon+'">'+lTxt+'</i><span>'+data[i].pv+'观看</span></div>'+
                                    '        <h5 class="new_live_txt" >'+data[i].title+'</h5>'+
                                    '     </a>'+
                                    ' </li>'
                                $(".new_live_ul").append(listLi)
                            }

                        }else{
                            $(".new_live_ul").append("<p class=\"noLive\">已到底，无更多直播</p>")
                            boType = 1
                            return

                        }
                    }})
            }


         }
        $(window).scroll(function() {
            var scrollTop = $(this).scrollTop(); //获取当前页面滚动距离
            var scrollHeight = $(document).height(); //获取页面总高度
            var windowHeight = $(this).height(); //获取当前窗口高度
            if ((scrollTop + windowHeight) == scrollHeight) { //判断是否到达页面底部
                start += start; //计算新数据的开始索引
                //通过Ajax请求获取新数据
                loadList(start,12)
            }
        });
    });
</script>
<!--底部1-->
<div class="liveBottom"><div th:include="fragment/fragment:: footer_list"></div></div>
<!--<div th:include="fragment/fragment::common_meiqia"></div>-->
<div th:include="fragment/fragment::tongji"></div>
<div class="cl"></div>
<script type="text/javascript">
    //<![CDATA[
    var theForm = document.forms['Form1'];
    if (!theForm) {
        theForm = document.Form1;
    }

    function __doPostBack(eventTarget, eventArgument) {
        if (!theForm.onsubmit || (theForm.onsubmit() != false)) {
            theForm.__EVENTTARGET.value = eventTarget;
            theForm.__EVENTARGUMENT.value = eventArgument;
            theForm.submit();
        }
    }


    //]]>
</script>

</body>
</html>