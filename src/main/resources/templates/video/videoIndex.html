<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title th:each="videoType:${videoFeature}" th:if="${videoType.selected}">
        <th:block th:text="${(#strings.isEmpty(videoType.id)?'房产视频':videoType.name)+',沈阳房产活动视频 - 房小二网'}"></th:block>
    </title>
    <meta name="keywords" content="房产活动,沈阳楼盘活动视频,沈阳房产活动视频,房小二视频，沈阳视频看房"/>
    <meta name="description" content="沈阳房小二网楼盘工程进度，追踪沈阳热点房产工程进度情况，报道热门项目开盘进度，为您提供最及时楼盘工程信息。房小二网-房地产专业媒体！"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/video.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/video/videoLeft.css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/video/videoIndex.css?v=2021115">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <!--<link rel="stylesheet" type="text/css" href="/css/videoIndex.css">-->
     <link href="https://static.fangxiaoer.com/m/static/css/video/animate.css" type="text/css" rel="stylesheet">
    
    <style>
        .video_box p{height: 49px;}
        .video_box p a{width: 95%;overflow: hidden;text-overflow:ellipsis; white-space: nowrap;margin-bottom: 0 !important;}
        .recommend a img {
            width:250px;
            height: 140px;
        }
        .grg a{ width: 250px;
            line-height: 49px;
            padding: 0;
            margin-bottom: 24px;
            height: 49px;
            background: #FFF7EE;
            border: 1px solid #EAEAEA;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #FF5200;display: block; text-align: center;}
        .grg a:hover{ background-color: #FFF7EE; text-decoration: none; color: #FF5200;}
        /*小二精选*/
        .childLtit { font-size: 16px; color: #272727; height: 60px; line-height: 60px; padding: 0px 0 0px 22px; border-bottom: 1px solid #f2f2f2; }
        .daiding { margin-bottom: 25px; }
        .childLtab { padding: 10px 0 10px 11px; font-size: 14px; color: #272727; font-weight: bold; border-bottom: 1px dashed #ededed; margin-bottom:8px;}
        .chiListRank { width: 17px; height: 17px; background-color: #dbdbdb; text-align: center; line-height: 17px; border-radius: 90px; float: left; font-size: 12px; color: #ffffff;  margin: 4px 7px 0 7px; }
        .crankList { float: left; width: 214px; color: #272727; font-size: 12px !important; margin-top: 5px; margin-left: 0px; }
        .crankList li { float: left;/* width:33.33% */
        }
        .crankList li:first-child { margin-right: 0px; width: 61px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
        .crankList li:nth-child(2) { margin-right: 0px; width: 80px; text-align: right; }
        .crankList li:nth-child(3) { width: 56px; text-align: right; }
        .crankList span { color: #ff5200; }
        .chitabN { margin-right: 12px; }
        .chitabL { margin-right: 27px; }
        .chitabJ { margin-right: 31px; }
        .childlist { margin-bottom: 20px; background-color: #ffffff; padding-bottom: 20px; }
        .childlist:last-child { margin-bottom: 0px; }
        .crankDetailsdiv img { width: 80px !important; height: 60px !important; float: left; margin-right: 8px; margin-top: 1px; }
        .crankDetails { float: left; width: 205px; background-color: #f3f5f7; margin-top: 5px;/* margin-bottom: 16px; */
        }
        .crankDetailsdiv { padding: 6px 0 6px 6px;   height:66px;  font-size: 12px;}
        .crankDetailsP span { color: #ff5200; display: inline-block; }
        .crankDetailsreason { margin: 0px 3px 12px 8px; padding-top: 1px solid #f2f2f2; font-weight: bold; border-top: 1px dashed #d9d9d9; padding-left: -4px; padding-left: 1px; padding-right: 4px; padding-top: 7px; white-space:normal !important; text-align: justify;}
        .crankDetailsdivR h2 { font-weight: 500; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}
        .crankDetailsreason span { font-weight: 500; line-height: 20px;}
        .chilistLi { overflow: hidden; cursor: pointer; }
        .crankDetails { display: none; }
        .spcolor { background-color: #ff5200; color: #ffffff; font-size: 12px; line-height: 17px; text-align: center; }
        .DDlist { color: #ff5200; text-align: center !important; }
        .Ljun { color: #333333 !important; width: 22px; height: 18px; line-height: 18px; padding-left: 2px; /* margin-top: 7px; */
            /* padding-top: 3px; */
            text-align: center; background: url(https://static.fangxiaoer.com/web/images/ranking/border3.svg) no-repeat; background-size: 100% 100%; }
        .lGeng { border-left: 1px solid #dddddd; display: inline-block; height: 11px; padding-top: 0px; line-height: 10px; padding-left: 4px; margin-left: 0px; }
        .Early { display: inline-block; color: #ec1b03; font-weight: bold; width: 155px; font-size: 14px; border: 1px solid #ec1b03; height: 18px; line-height: 17px; text-align: center; margin-top: 18px; margin-bottom: 0px; }
        .Early span { font-family: dinot-bold; }
        .crankDetailsP { color: #ff5200; }


        .pl0{ padding-left:0 !important; margin-bottom:5px !important;}
        .p00{ padding-left:9px !important; margin-bottom:0px !important;}
        .crankDetailsdivR{ line-height: 20px;}
        .recommend .pl0 a{ color: unset !important;}
        .unstyled{ height: 151px; overflow: hidden;}
        .reaa{
            font-size: 12px;
            color: #5097FF;
            cursor: pointer;
            user-select: none;
            position: absolute;
            bottom: -2px;
            right: 24px;}
    </style>

</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::videoSearch" th:with="type=5"></div>
<div class="position">
    <div class="content_box">
        <h1>您的位置 : <a th:href="@{'/'}">沈阳房产网</a> > <a th:href="@{'/news'}">资讯</a> > <a th:href="@{'/videos'}" >房产视频</a> </h1>
    </div>
</div>
<div class="nav_box">
    <div class="content_box">
        <ul class="w25-li">
            <li th:each="videoType,vT:${videoFeature}" th:class="${vT.first?'color':''}">
            	<a th:href="${videoType.url}"   th:class="${videoType.selected?'hover':''}" th:text="${#strings.isEmpty(videoType.id)?'视频首页':videoType.name}">
            		
            	</a>
            	<span></span>
            </li>
            <div class="clearfix"></div>
        </ul>
    </div>
</div>
<div class="big_box">
    <div class="content_box">
        <div class="left_box">
            <div class="activity" th:if="${videosList ne null}" th:each="one:${videosList}">
                <div class="title">
                    <h1 th:text="${one.videoFeatureName}">楼盘活动</h1>
                    <a th:href="${'/videos/' + one.videoFeature}">查看更多</a>
                    <div class="clearfix"></div>
                </div>
                <div class="content">
                    <ul>
                        <li th:each="video,vIndex:${one.videoList}">
                            <a th:href="${'/video/'+video.videoId+'.htm'}" target="_blank">
                                <div class="photo">
                                    <div class="pic">
                                            <img th:src="${#strings.isEmpty(video.videoPic)?'':video.videoPic}">
                                    </div>
                                    <div class="time">
                                        <h1 th:text="${#strings.isEmpty(video.newVideoTime)?'':video.newVideoTime}">02:27</h1>
                                    </div>
                                </div>
                                <div class="text">
                                    <h1 th:text="${#strings.isEmpty(video.videoTitle)?'':video.videoTitle}">坤博幸福城不负万众期待 耀坤博幸福城不负万众期待 耀</h1>
                                    <h2 th:if="${vIndex.first}" th:text="${#strings.isEmpty(video.description)?'':video.description}">坤博幸福城不负万众期待 耀坤博幸福城不负万众期待 耀</h2>
                                </div>
                            </a>
                        </li>
                        <div class="clearfix"></div>
                    </ul>
                </div>
            </div>
        </div>
        <div class="right_box">
            <div id="right">
                <!--<div class="zsfw">
                    <h1><span></span>帮您找房</h1>
                    <ul>
                        <li>
                            <span>意向区域</span>
                            <div>
                                <select id="region">
                                    <option>沈河区</option>
                                    <option>大东区</option>
                                    <option>皇姑区</option>
                                    <option>和平区</option>
                                    <option>铁西区</option>
                                    <option>于洪区</option>
                                    <option>浑南区</option>
                                    <option>沈北新区</option>
                                    <option>苏家屯</option>
                                </select>
                            </div>
                        </li>
                        <li class="hx">
                            <span>意向户型</span>
                            <div>
                                <select id="new_huxing">
                                    <option>一居</option>
                                    <option>二居</option>
                                    <option>三居</option>
                                    <option>四居</option>
                                    <option>五居及以上</option>
                                </select>
                            </div>
                        </li>
                        <li class="yx">
                            <span>预算价格</span>
                            <div>
                                <select id="new_yusuan">
                                    <option>35万以下</option>
                                    <option>35-50万</option>
                                    <option>50-80万</option>
                                    <option>80-100万</option>
                                    <option>100-120万</option>
                                    <option>120-150万</option>
                                    <option>150万以上</option>
                                </select>
                            </div>
                        </li>
                        <li>
                            <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
                        </li>
                        <li>
                            <span>手机号码</span>
                            <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
                            <input type="hidden" id="type" value="1">
                        </li>
                        <li>
                            <span>验证码</span>
                            <input type="tel" id="code" class="fxe_messageCode" maxlength="6" style="width: 120px;"
                                   placeholder="请输入验证码"/>
                            <p id="hqyzm" class="fxe_ReSendValidateCoad">获取验证码</p>
                            <p class="fxe_validateCode"></p>
                        </li>
                        <b class="btn" id="new_submit">提交</b>
                        <div class="checkagreeInput" style="margin: 0 auto 10px auto;">
                            <div style="font-size:12px;">提交即代表同意<a href="https://info.fangxiaoer.com/About/protocol" target="_blank">《房小二网用户服务协议》</a>
                                <a href="https://info.fangxiaoer.com/About/policy" target="_blank">《房小二网隐私政策》</a></div>
                        </div>
                    </ul>
                </div>-->
                <!--帮您找房-->
                <!--<div th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}">
                    <div id="newsAD1_guanggao" class="flash">&lt;!&ndash;广告&ndash;&gt;
                        <ul class="rotaion_list">
                            <li th:each="advert1:${advert1}">
                                <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                                <a th:href="${advert1.TargetUrl}" target="_blank">
                                    <img th:src="${advert1.AdFilePath}" alt=""></a>
                            </li>
                        </ul>
                    </div>
                </div>-->

                <dl class="recommend">
                    <dt style="position: relative"><span></span>房产快讯 <i onclick="window.open('/news')" class="reaa">更多>></i></dt>
                    <!--房产快讯-->
                    <div class="hot" th:include="fragment/fragment::news_flash"></div>
                </dl>
                <div class="cl"></div>

                <div class="grg">
                    <a th:href="@{'/helpSearch?ids=1'}" rel="2" target="_blank">我要买房</a>
                    <a href="/static/saleHouse/saleHouse.htm" th:class=" (${firstNavIndex} == 8)? 'hover' : '' ">我要卖房</a>
                </div>

                <!-- 小二精选楼盘 -->
                <dl class="recommend" style=" margin-top: 24px;">
                    <dt><span></span>小二精选</dt>
                    <div class="">
                        <div style=""><!--padding: 18px 15px; box-sizing: border-box;-->

                            <div class="childlist">
                                <div class="childLtab">
                                    <span class="chitabN">排名</span>
                                    <span class="chitabL">楼盘名称</span>
                                    <span class="chitabJ">价格</span>
                                    <span class="chitabQ">区域</span>
                                </div>
                                <ul>
                                    <li class="chilistLi pl0" th:each="project, i : ${rankList}" th:if="${i.index lt 10}">
                                        <div>
                                            <div th:class="${i.count le 3 ? 'chiListRank spcolor' : 'chiListRank'}" th:text="${i.count}"></div><!--前三个 加spcolor-->
                                            <div class="crankList" th:style="${i.count eq 1 ? 'display:none':'display:block'}">
                                                <ul>
                                                    <li class="pl0" th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></li>
                                                    <li class="pl0 DDlist" th:if="${project.mPrice eq null}">待定</li>
                                                    <li class="pl0" th:if="${project.mPrice ne null}"><span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡</li>
                                                    <li class="p00" th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}">沈北新区</li>
                                                </ul>
                                            </div>
                                            <a th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}" target="_blank">
                                                <div class="crankDetails" th:style="${i.count eq 1 ? 'display:block':'display:none'}">
                                                    <div class="crankDetailsdiv" style="position: relative">
                                                        <img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
                                                        <div class="crankDetailsdivR">
                                                            <h2 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></h2>
                                                            <p class="crankDetailsP" th:if="${project.mPrice eq null}" >待定</p>
                                                            <p class="crankDetailsP" th:if="${project.mPrice ne null}">
                                                                <span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡
                                                                <span class="Ljun" th:text="${project.mPrice.priceType eq '起价'? '起' :'均'}"></span></p>
                                                            <p class="crankDetailsD">
                                                                <span th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}"></span>
                                                                <span class="lGeng" th:if="${!#maps.isEmpty(project.area)}"
                                                                      th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))
                                                          + '-' + #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}"></span>
                                                            </p>
                                                        </div>
                                                        <div class="vru" th:if="${project.pan ne null}"></div>
                                                    </div>
                                                    <div class="crankDetailsreason">
                                                        推荐理由：<span th:text="${project.rankDesc}"></span>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    </li>
                                </ul>
                            </div>

                        </div>
                    </div>
                </dl>

                <!--<div class="video_box">
                    <div style="font-size:18px;" class="title"><span></span>最新视频</div>
                    <dl class="recommend" style="border:none" th:each="v:${video.content}">
                        <a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank"><i></i>
                            <img th:src="${v.videoPic}" th:alt="${v.videoTitle}"/></a>
                        <p><a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank" th:text="${v.videoTitle}"></a></p>
                    </dl>
                </div>-->

                <div class="cl"></div>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>
</div>
<script>
    $(".cheimg5").click(function () {
        if($(this).hasClass("checked")){
            $(this).removeClass("checked")
        }else{
            $(this).addClass("checked")
        }
    })
    $(document).ready(function() {
    	$(".head_nav ul li").eq(7).find("p").find("a").eq(6).removeClass("hot_red");
    	$(".head_nav ul li").eq(7).find("p").find("a").eq(5).addClass("hot_red");
    	var btn_x = 0;
    	var storage = window.sessionStorage;
		var videoSearch = storage.getItem("select");
		
			for(var i = 0; i < $(".content_box li").length; i++) {
			if($(".content_box li").eq(i).hasClass("color")) {
				var classname=$(this).className;
			if(classname==undefined || classname==''){
				$(".content_box li").eq(i).find("span").addClass("oncolor");
			}
				if(videoSearch != null && videoSearch != '') {
					if(i - videoSearch > 0) {
						$(".content_box  li span").eq(i).addClass("fadeInLeft");
						
					} else if(i - videoSearch < 0){
						$(".content_box  li span").eq(i).addClass("fadeInRight");
						
					}
					
					btn_x = i;
					setInfo1(btn_x);

				} else {
					btn_x = i;
					setInfo1(btn_x);
				}

			}
		}
		
    
    })
    
    //sessionStorage 储存搜索
	function setInfo1(select) {
		var storage = window.sessionStorage;
		storage.setItem('select', select);
	}

    /*小二精选*/
    $(".crankList").hover(function() {
        // $(".crankList").show();
        $(this).parent().parent().parent().find(".crankList").show();
        $(this).hide();
        $(this).parent().parent().parent().find(".crankDetails").hide();
        $(this).parent().find(".crankDetails").show();
    })
    
</script>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
<style>

</style>
</body>
</html>