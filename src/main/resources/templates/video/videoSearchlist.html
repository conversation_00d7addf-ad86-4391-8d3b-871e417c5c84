<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title th:each="videoType:${videoFeature}" th:if="${videoType.selected}">
        <th:block th:text="${(#strings.isEmpty(videoType.id)?'房产视频':videoType.name)+',沈阳房产活动视频 - 房小二网'}"></th:block>
    </title>
    <meta name="keywords" content="房产活动,沈阳楼盘活动视频,沈阳房产活动视频,房小二视频，沈阳视频看房"/>
    <meta name="description" content="沈阳房小二网楼盘工程进度，追踪沈阳热点房产工程进度情况，报道热门项目开盘进度，为您提供最及时楼盘工程信息。房小二网-房地产专业媒体！"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/video.htm">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/video/videoLeft.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/video/videoSearch.css?v=2021115"/>
    <!--<link rel="stylesheet" type="text/css" href="/css/videoSearch.css">-->
	    
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
        $(function () {
            var url = window.location.href
            if (url.indexOf("keys=") != "-1") {
                url = url.substring(url.indexOf("keys=") + 5, url.lenght);
                $("#txtkeys").val(decodeURI(url));
                $(".warning i").html(decodeURI(url))
                var word = decodeURI(url)
                $(".news p").each(function () {
                    $(this).find("a").html($(this).find("a").html().replace(word, "<s>" + word + "</s>"))
                })
            }
        })
    </script>
</head>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::videoSearch" ></div>


<div class="crumbs">您的位置：<a href="/" target="_blank">沈阳房产网</a> &gt;
    <a href="/news/" target="_blank">资讯</a>&gt;
<!--    <a th:each="videoType:${videoFeature}" th:if="${videoType.selected}" th:href="@{'/videos/'}" style="cursor: pointer"-->
<!--       th:text="${#strings.isEmpty(videoType.id)?'房产视频':videoType.name}"></a>-->
</div>
<style>
    .grg a{ width: 250px;
        line-height: 49px;
        padding: 0;
        margin-bottom: 24px;
        height: 49px;
        background: #FFF7EE;
        border: 1px solid #EAEAEA;
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #FF5200;display: block; text-align: center;}
    .grg a:hover{ background-color: #FFF7EE; text-decoration: none; color: #FF5200;}
    /*小二精选*/
    .childLtit { font-size: 16px; color: #272727; height: 60px; line-height: 60px; padding: 0px 0 0px 22px; border-bottom: 1px solid #f2f2f2; }
    .daiding { margin-bottom: 25px; }
    .childLtab { padding: 10px 0 10px 11px; font-size: 14px; color: #272727; font-weight: bold; border-bottom: 1px dashed #ededed; margin-bottom:8px;}
    .chiListRank { width: 17px; height: 17px; background-color: #dbdbdb; text-align: center; line-height: 17px; border-radius: 90px; float: left; font-size: 12px; color: #ffffff;  margin: 4px 7px 0 7px; }
    .crankList { float: left; width: 214px; color: #272727; font-size: 12px !important; margin-top: 5px; margin-left: 0px; }
    .crankList li { float: left;/* width:33.33% */
    }
    .crankList li:first-child { margin-right: 0px; width: 61px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
    .crankList li:nth-child(2) { margin-right: 0px; width: 80px; text-align: right; }
    .crankList li:nth-child(3) { width: 56px; text-align: right; }
    .crankList span { color: #ff5200; }
    .chitabN { margin-right: 12px; }
    .chitabL { margin-right: 27px; }
    .chitabJ { margin-right: 31px; }
    .childlist { margin-bottom: 20px; background-color: #ffffff; padding-bottom: 20px; }
    .childlist:last-child { margin-bottom: 0px; }
    .crankDetailsdiv img { width: 80px !important; height: 60px !important; float: left; margin-right: 8px; margin-top: 1px; }
    .crankDetails { float: left; width: 205px; background-color: #f3f5f7; margin-top: 5px;/* margin-bottom: 16px; */
    }
    .crankDetailsdiv { padding: 6px 0 6px 6px;   height:66px;  font-size: 12px;}
    .crankDetailsP span { color: #ff5200; display: inline-block; }
    .crankDetailsreason { margin: 0px 3px 12px 8px; padding-top: 1px solid #f2f2f2; font-weight: bold; border-top: 1px dashed #d9d9d9; padding-left: -4px; padding-left: 1px; padding-right: 4px; padding-top: 7px; white-space:normal !important; text-align: justify;}
    .crankDetailsdivR h2 { font-weight: 500; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}
    .crankDetailsreason span { font-weight: 500; line-height: 20px;}
    .chilistLi { overflow: hidden; cursor: pointer; }
    .crankDetails { display: none; }
    .spcolor { background-color: #ff5200; color: #ffffff; font-size: 12px; line-height: 17px; text-align: center; }
    .DDlist { color: #ff5200; text-align: center !important; }
    .Ljun { color: #333333 !important; width: 22px; height: 18px; line-height: 18px; padding-left: 2px; /* margin-top: 7px; */
        /* padding-top: 3px; */
        text-align: center; background: url(https://static.fangxiaoer.com/web/images/ranking/border3.svg) no-repeat; background-size: 100% 100%; }
    .lGeng { border-left: 1px solid #dddddd; display: inline-block; height: 11px; padding-top: 0px; line-height: 10px; padding-left: 4px; margin-left: 0px; }
    .Early { display: inline-block; color: #ec1b03; font-weight: bold; width: 155px; font-size: 14px; border: 1px solid #ec1b03; height: 18px; line-height: 17px; text-align: center; margin-top: 18px; margin-bottom: 0px; }
    .Early span { font-family: dinot-bold; }
    .crankDetailsP { color: #ff5200; }


    .pl0{ padding-left:0 !important; margin-bottom:5px !important;}
    .p00{ padding-left:9px !important; margin-bottom:0px !important;}
    .crankDetailsdivR{ line-height: 20px;}
    .recommend .pl0 a{ color: unset !important;}
    .unstyled{ height: 151px; overflow: hidden;}
    .reaa{
        font-size: 12px;
        color: #5097FF;
        cursor: pointer;
        user-select: none;
        position: absolute;
        bottom: -2px;
        right: 24px;}
</style>
<div style="width: 1170px;margin: 0 auto;">
    <div class="videoLeft">
        <div class="none_box" th:if="${!#lists.isEmpty(recommendVideos)}">
            <h1>抱歉，没有找到与“<span th:text="${searchKey}"></span>”相关的视频</h1>
        </div>
        <div class="tuijian" th:if="${!#lists.isEmpty(recommendVideos)}">
            <h1>为您推荐</h1>
        </div>
        <div class="list" >
            <ul>
                <li th:each="video:${videoList}">
                    <a target="_blank" th:href="${'/video/'+video.videoId+'.htm'}">
                        <div class="pic">
                                <img th:src="${#strings.isEmpty(video.videoPic)?'': video.videoPic}" class="pic_sun">
                                <!--<div class="label">
                                    <h3 th:text="${#strings.isEmpty(video.videoTypeName)?'': video.videoTypeName}">企业视频</h3>
                                </div>-->
                            <div class="time">
                                <!--<img src="https://static.fangxiaoer.com/web/images/video/time.png">-->
                                <h2 th:text="${#strings.isEmpty(video.newVideoTime)?'': video.newVideoTime}">03:52</h2>
                                <div class="cl"></div>
                            </div>
                        </div>
                    </a>
                    <div class="text" >
                        <a target="_blank" th:href="${'/video/'+video.videoId+'.htm'}">
                            <h2 th:if="${!#strings.isEmpty(video.videoTitle)}" th:text="${video.videoTitle}">万科·东部“V盟荟”全面启动</h2>
                            <div class="info">
                        		<div class="touxiang">
                        		<img th:if="${!#strings.isEmpty(video.videoTypeLogo)}" th:src="${video.videoTypeLogo}" />
									
                        			
                        		</div>
                        		<span class="infoname" th:text="${video.videoTypeName}"></span>
                        		<div class="video_r">
                        			<div class="video_ll"><img src="https://static.fangxiaoer.com/web/images/video/spbf_img_ll.png"></div>
                        			<span class="span_div mr14" th:text="${video.visitNum}">3.1K</span>
                        			<div class="video_dz"><img src="https://static.fangxiaoer.com/web/images/video/spbf_img_dz.png"></div>
                        			<span class="span_div" th:text="${video.videoLikeNum}">9</span>
                        		</div>
                        	</div>
                        </a>
                    </div>
                </li>
                <li th:if="${!#lists.isEmpty(recommendVideos)}" th:each="video:${recommendVideos}" >
                    <a target="_blank" th:href="${'/video/'+video.videoId+'.htm'}">
                        <div class="pic">
                                <img th:src="${#strings.isEmpty(video.videoPic)?'': video.videoPic}" class="pic_sun">
                                <!--<div class="label">
                                    <h3 th:text="${#strings.isEmpty(video.videoTypeName)?'': video.videoTypeName}">企业视频</h3>
                                </div>-->
                            <div class="time">
                                <!--<img src="https://static.fangxiaoer.com/web/images/video/time.png">-->
                                <h2 th:text="${#strings.isEmpty(video.newVideoTime)?'': video.newVideoTime}">03:52</h2>
                                <div class="cl"></div>
                            </div>
                        </div>
                    </a>
                    <div class="text" >
                        <a target="_blank" th:href="${'/video/'+video.videoId+'.htm'}">
                            <h2 th:if="${!#strings.isEmpty(video.videoTitle)}" th:text="${video.videoTitle}">万科·东部“V盟荟”全面启动</h2>
                            <div class="info">
                        		<div class="touxiang">
                        		<img th:if="${!#strings.isEmpty(video.videoTypeLogo)}" th:src="${video.videoTypeLogo}" />
                        		</div>
                        		<span class="infoname" th:text="${video.videoTypeName}">楼盘活动</span>
                        		<div class="video_r">
                        			<div class="video_ll"><img src="https://static.fangxiaoer.com/web/images/video/spbf_img_ll.png"></div>
                        			<span class="span_div mr14" th:text="${video.visitNum}">3.1K</span>
                        			<div class="video_dz"><img src="https://static.fangxiaoer.com/web/images/video/spbf_img_dz.png"></div>
                        			<span class="span_div" th:text="${video.videoLikeNum}">9</span>
                        		</div>
                        	</div>
                        </a>
                    </div>
                </li>
                <div class="cl"></div>
                <div class="page">
                    <div id="Pager1">
                        <div th:include="fragment/page :: page"></div>
                    </div>
                </div>
            </ul>
        </div>
    </div>
    <div id="right">
        <!--<div class="zsfw">
            <h1><span></span>帮您找房</h1>
            <ul>
                <li>
                    <span>意向区域</span>
                    <div>
                        <select id="region">
                            <option>沈河区</option>
                            <option>大东区</option>
                            <option>皇姑区</option>
                            <option>和平区</option>
                            <option>铁西区</option>
                            <option>于洪区</option>
                            <option>浑南区</option>
                            <option>沈北新区</option>
                            <option>苏家屯</option>
                        </select>
                    </div>
                </li>
                <li class="hx">
                    <span>意向户型</span>
                    <div>
                        <select id="new_huxing">
                            <option>一居</option>
                            <option>二居</option>
                            <option>三居</option>
                            <option>四居</option>
                            <option>五居及以上</option>
                        </select>
                    </div>
                </li>
                <li class="yx">
                    <span>预算价格</span>
                    <div>
                        <select id="new_yusuan">
                            <option>35万以下</option>
                            <option>35-50万</option>
                            <option>50-80万</option>
                            <option>80-100万</option>
                            <option>100-120万</option>
                            <option>120-150万</option>
                            <option>150万以上</option>
                        </select>
                    </div>
                </li>
                <li>
                    <textarea id="describe" placeholder="请输入您对需求的描述..."></textarea>
                </li>
                <li>
                    <span>手机号码</span>
                    <input type="tel" id="phone" class="fxe_mobile" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " placeholder="请输入手机号" maxlength="11">
                    <input type="hidden" id="type" value="1">
                </li>
                <li>
                    <span>验证码</span>
                    <input type="tel" id="code" class="fxe_messageCode" maxlength="6" style="width: 120px;"
                           placeholder="请输入验证码"/>
                    <p id="hqyzm" class="fxe_ReSendValidateCoad">获取验证码</p>
                    <p class="fxe_validateCode"></p>
                </li>
                <b class="btn" id="new_submit">提交</b>
                <li style="color: #999;width:237px;text-align:left;font-size: 12px;">若正确提交即同意接受以电话、短信等方式推荐我司业务相关的信息</li>
            </ul>
        </div>
        &lt;!&ndash;帮您找房&ndash;&gt;
        <div th:if="${advert1 ne null and #lists.toList(advert1).size() ne 0}">
            <div id="newsAD1_guanggao" class="flash">&lt;!&ndash;广告&ndash;&gt;
                <ul class="rotaion_list">
                    <li th:each="advert1:${advert1}">
                        <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                        <a th:href="${advert1.TargetUrl}" target="_blank">
                            <img th:src="${advert1.AdFilePath}" alt=""></a>
                    </li>
                </ul>
            </div>
        </div>-->

        <dl class="recommend">
<!--            <dt><span></span>房产快讯</dt>-->
            <dt style="position: relative"><span></span>房产快讯 <i onclick="window.open('/news')" class="reaa">更多>></i></dt>
            <!--房产快讯-->
            <div class="hot" th:include="fragment/fragment::news_flash"></div>
        </dl>

        <div class="cl"></div>

        <div class="grg">
            <a th:href="@{'/helpSearch?ids=1'}" rel="2" target="_blank">我要买房</a>
            <a href="/static/saleHouse/saleHouse.htm" th:class=" (${firstNavIndex} == 8)? 'hover' : '' ">我要卖房</a>
        </div>

        <!-- 小二精选楼盘 -->
        <dl class="recommend" style=" margin-top: 24px;">
            <dt><span></span>小二精选</dt>
            <div class="">
                <div style=""><!--padding: 18px 15px; box-sizing: border-box;-->

                    <div class="childlist">
                        <div class="childLtab">
                            <span class="chitabN">排名</span>
                            <span class="chitabL">楼盘名称</span>
                            <span class="chitabJ">价格</span>
                            <span class="chitabQ">区域</span>
                        </div>
                        <ul>
                            <li class="chilistLi pl0" th:each="project, i : ${rankList}" th:if="${i.index lt 10}">
                                <div>
                                    <div th:class="${i.count le 3 ? 'chiListRank spcolor' : 'chiListRank'}" th:text="${i.count}"></div><!--前三个 加spcolor-->
                                    <div class="crankList" th:style="${i.count eq 1 ? 'display:none':'display:block'}">
                                        <ul>
                                            <li class="pl0" th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></li>
                                            <li class="pl0 DDlist" th:if="${project.mPrice eq null}">待定</li>
                                            <li class="pl0" th:if="${project.mPrice ne null}"><span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡</li>
                                            <li class="p00" th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}">沈北新区</li>
                                        </ul>
                                    </div>
                                    <a th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}" target="_blank">
                                        <div class="crankDetails" th:style="${i.count eq 1 ? 'display:block':'display:none'}">
                                            <div class="crankDetailsdiv" style="position: relative">
                                                <img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
                                                <div class="crankDetailsdivR">
                                                    <h2 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></h2>
                                                    <p class="crankDetailsP" th:if="${project.mPrice eq null}" >待定</p>
                                                    <p class="crankDetailsP" th:if="${project.mPrice ne null}">
                                                        <span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡
                                                        <span class="Ljun" th:text="${project.mPrice.priceType eq '起价'? '起' :'均'}"></span></p>
                                                    <p class="crankDetailsD">
                                                        <span th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}"></span>
                                                        <span class="lGeng" th:if="${!#maps.isEmpty(project.area)}"
                                                              th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))
                                                          + '-' + #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}"></span>
                                                    </p>
                                                </div>
                                                <div class="vru" th:if="${project.pan ne null}"></div>
                                            </div>
                                            <div class="crankDetailsreason">
                                                推荐理由：<span th:text="${project.rankDesc}"></span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>

                </div>
            </div>
        </dl>

        <!--                &lt;!&ndash;楼盘视频标题&ndash;&gt;
                        <div class="title">楼盘视频</div>-->
        <!--楼盘视频信息-->
        <!--<div class="video_box">
            <div style="font-size:18px;" class="title"><span></span>最新视频</div>
            <dl class="recommend" style="border:none" th:each="v:${video}">

                <a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank"><i></i>
                    <img th:src="${v.videoPic}" th:alt="${v.videoTitle}"/></a>
                <p><a th:href="${'../video/'+v.videoId+'.htm'}" target="_blank" th:text="${v.videoTitle}"></a></p>

            </dl>
        </div>
        <div th:if="${advert2 ne null and #lists.toList(advert2).size() ne 0}">
            <div id="newsAD2_guanggao" class="flash">&lt;!&ndash;广告&ndash;&gt;
                <ul class="rotaion_list">
                    <li th:each="advert2:${advert2}">
                        <sup><img src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png"></sup>
                        <a th:href="${advert2.TargetUrl}" target="_blank">
                            <img th:src="${advert2.AdFilePath}" alt=""></a>
                    </li>
                </ul>
            </div>
        </div>-->
        <div class="cl"></div>
        <div class="cl"></div>
    </div>

</div>
<div class="cl"></div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
<script type="text/javascript" src="//static.fangxiaoer.com/js/modal.js"></script>
<script>
    function submitForm() {
        function $Arg(id) {
            return document.getElementById(id);
        }
        var r = 0;
        if (fxeTime.mobile()) {
            if (fxeTime.password()) {
                if (confirm.mobile()) {
                    $.ajax({
                        type: "POST",
                        data: {number: $Arg("Txt_LoginName").value, passowrd: $Arg("Txt_Password").value},
                        url: "/action/userlogin.ashx",
                        async: false,
                        success: function (data) {
                            r = data;
                        }
                    });
                } else {
                    r = 0
                    $(".error_info").text(hint[0]);
                }
            }
        }
        if (r == 1) {
            if ($("#LoginUrl").val() != "" && IsCommentList == 1) {
                window.location.href = $("#LoginUrl").val();
            } else {
                window.location.reload();
            }
        } else {
            r = 0
            $(".error_info").text(hint[0]);
        }
    }

</script>
<script type="text/javascript">
    $(document).ready(function () {
        $("a.forgot").click(function () {
            $("#login-modal").modal("hide");
            $("#forgetform").modal({show: !0});
        });
        $("#login").modal("hide");
    });
    $(document).keypress(function (e) {
        // 回车键事件
        if (e.which == 13) {
            submitForm();
            return false;
        }
    });

    /*小二精选*/
    $(".crankList").hover(function() {
        // $(".crankList").show();
        $(this).parent().parent().parent().find(".crankList").show();
        $(this).hide();
        $(this).parent().parent().parent().find(".crankDetails").hide();
        $(this).parent().find(".crankDetails").show();
    })
</script>
</html>