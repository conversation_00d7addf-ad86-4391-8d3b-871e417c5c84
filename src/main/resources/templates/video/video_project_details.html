<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${video.videoTitle}+'看房视频，'+${video.videoTitle}+'视频 - 房小二网'"></title>
    <meta name="keywords" th:content="${video.videoTitle}" />
    <meta name="description" th:content="${video.videoTitle+'视频看房,视频为您介绍'+video.videoTitle+'楼盘、样板间、配套等信息，了解'+video.videoTitle+'楼盘户型、开盘信息，赶快体验房小二网视频看房！'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/video/'+videoId+'.htm'}">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/video/video_details.css?v=2021115">
    <!--<link rel="stylesheet" type="text/css" href="/css/video_details.css">-->
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="//g.alicdn.com/de/prismplayer/2.7.4/skins/default/aliplayer-min.css" />
    <script type="text/javascript" charset="utf-8" src="//g.alicdn.com/de/prismplayer/2.7.4/aliplayer-min.js"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
</head>
<body>
<style>
    .video_box .content_box .content .right_box .list {
        padding-top: 12px;
        height: 444px;
        overflow-x: scroll;
    }
    .video_box .content_box .content .right_box .list .text { float: right; width: 127px !important;}
</style>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
<input type="hidden" id="sessionId" th:value="${session.sessionId}">
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::videoSearch" th:with="type=5"></div>
<div class="position">
    <div class="content_box">
        <h1>您的位置 : <a th:href="@{'/'}">沈阳房产网</a> > <a th:href="@{'/videos'}">房产视频</a><th:block th:unless="${#strings.isEmpty(video.videoTypeName)}" > > [<span th:text="${video.videoTypeName}">楼盘活动</span>]</th:block><th:block th:text="${#strings.isEmpty(video.videoTitle)?'':video.videoTitle}"></th:block> </h1>
    </div>
</div>
<div class="video_box">
    <div class="content_box">
       <div class="title">
           <div class="left_box">
               <h1><th:block th:text="${#strings.isEmpty(video.videoTitle)?'':video.videoTitle}"></th:block></h1>
           </div>
           <div class="right_box">
               <div class="volume">
                   <a href="#" th:unless="${#strings.isEmpty(video.videoCount)}" th:text="${video.videoCount}">1020</a>
               </div>
               <div class="share">
                   <a  class="share_tan" href="#">分享</a>
                       <div class="share_tan_main" style="display: none">
                           <img src="https://static.fangxiaoer.com/web/images/video/Arrow.png">
                           <a class="ewm">
                               <div id="qrcode"></div>
                               <h1>发送到手机 手机扫一扫</h1>
                               <h2>把好房源分享给朋友</h2>
                           </a>
                       </div>
                   <script>
                       $(function () {
                           $(".share_tan").mouseover(function () {
                               $(".share_tan_main").toggle()
                           });
                           $(".share_tan").mouseout(function () {
                               $(".share_tan_main").toggle()
                           });
                           var mhead = "video/";
                           $('#qrcode').qrcode(
                               {
                                   width: 90,
                                   height: 90,
                                   text: "https://m.fangxiaoer.com/"+mhead+[[${houseId}]]+".htm"
                               })
                       });
                   </script>
               </div>
               <div class="edit">
                   <h1 th:unless="${#strings.isEmpty(video.editName)}" th:text="${'编辑：'+video.editName}">编辑：MZ</h1>
               </div>
               <div class="time">
                   <h1 th:text="${video.addTime != null and #strings.length(video.addTime) gt 10 ? #strings.toString(video.addTime).substring(0,10) :video.addTime}">2017-10-10</h1>
               </div>
               <div class="clearfix"></div>
           </div>
           <div class="clearfix"></div>
       </div>
        <div class="content">
            <div class="left_box">
                <div class="prism-player" id="player-con"></div>
            </div>
            <div class="right_box">
                <div class="name">
                    <h1>楼盘视频</h1>
                </div>
                <div class="list">
                    <ul>
                        <li th:each="with,i:${videoWith}">
                            <a th:href="${'/video/'+ with.videoId + '-' +projectId+'.htm'}">
                                <div class="pic" th:style="${i.index == 0 ? 'border: 2px solid #ff5200':''}">

                                        <img th:src="${with.videoPic}" th:alt="${with.videoTitle}">

                                </div>
                                <div class="text">
                                    <div class="name_sun">

                                            <h1 th:style="${i.index == 0 ? 'color: #ff5200':''}"  th:text="${with.videoTitle}">九颂大院暴电音节暨创意九颂大院暴电音节暨创意</h1>

                                    </div>
                                    <div class="time">
                                        <h1 th:text="${with.newVideoTime}">03:20</h1>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                            </a>

                        </li>
                    </ul>
                </div>
            </div>
            <div class="clearfix"></div>
        </div>
    </div>
</div>
<!--<div class="video_information">
    <div class="content_box">
        <div class="name">
            <h1>视频信息</h1>
        </div>
        <div class="type">
           <ul>
               <li th:unless="${#strings.isEmpty(video.videoTypeName)}">
                   <h1>类型：<th:block th:text="${video.videoTypeName}"></th:block></h1>
               </li>
               <li th:unless="${#strings.isEmpty(video.addTime)}">
                   <h1>时间：<th:block th:text="${video.addTime}"></th:block></h1>
               </li>
               <li th:unless="${#strings.isEmpty(video.videoTime)}">
                   <h1>时长：<th:block th:text="${video.videoTime}"></th:block></h1>
               </li>
               <div class="clearfix"></div>
           </ul>
        </div>
        <div class="information">
            <p th:unless="${#strings.isEmpty(video.description)}">简介：<th:block th:text="${video.description}"></th:block></p>
        </div>
    </div>
</div>-->
<div class="video_information">
	<div class="content_box">
		<!--<div class="main_info">-->
			<!--<div class="touxiang">-->
                <!--<img th:if="${!#strings.isEmpty(video.videoTypeLogo)}" th:src="${video.videoTypeLogo}" />-->
				<!---->
			<!--</div>-->
			<!--<span class="infoname"><th:block th:text="${video.videoTypeName}"></th:block></span>-->
		<!--</div>-->
		<div class="information">
            <p th:unless="${#strings.isEmpty(video.description)}">简介：<th:block th:text="${video.description}"></th:block></p>
			
		</div>
		<div class="hot_info" th:if="${!#strings.isEmpty(video.linkUrl)}">
			<a th:href="${video.linkUrl}">
				<span th:text="${video.linkName}">九宋大院洋房三室二厅二卫116m</span>
			</a>
		</div>
		
		<div class="dz">
			<span>如果这篇内容对您有用，请点赞支持我们</span>
			<div class="dzimg" th:if="${session.sessionId eq null}">
				<a target="_blank" data-toggle="modal" href="#login">
				<div style="display: none;" class="videoid" th:text="${video.videoId}"></div>
				<div style="display: none;" id="likeNum" th:text="${video.videoLikeNum}"></div>
				<div style="display: none;" id="hasLike" th:text="${video.hasLike}"></div>
				<img id="likeUp" src="https://static.fangxiaoer.com/web/images/video/img_dz.png" th:if="${video.hasLike == '0'}" />
				<span th:if="${video.hasLike == '0'}" th:text="${video.videoLikeNum}">点赞</span>
				<img src="https://static.fangxiaoer.com/web/images/video/img_ydz.png" th:if="${video.hasLike == '1'}"/>
				<span th:if="${video.hasLike == '1'}"  th:text="${video.videoLikeNum}">已点赞</span>
				</a>
			</div>
				<div class="dzimg" th:if="${!#strings.isEmpty(session.sessionId)}">
				<div style="display: none;" class="videoid" th:text="${video.videoId}"></div>
				<div style="display: none;" id="likeNum" th:text="${video.videoLikeNum}"></div>
				<div style="display: none;" id="hasLike" th:text="${video.hasLike}"></div>
				<img id="likeUp" src="https://static.fangxiaoer.com/web/images/video/img_dz.png" th:if="${video.hasLike == '0'}" />
				<span th:if="${video.hasLike == '0'}" th:text="${video.videoLikeNum}">点赞</span>
				<img src="https://static.fangxiaoer.com/web/images/video/img_ydz.png" th:if="${video.hasLike == '1'}"/>
				<span th:if="${video.hasLike == '1'}"  th:text="${video.videoLikeNum}">已点赞</span>
				
			</div>
		</div>
		
	</div>
	
</div>
<div class="activity" >
    <div class="content_box" th:each="one:${videoMaps}">
        <div class="title">
            <h1 th:text="${one.videoFeatureName}">楼盘活动</h1>
            <a th:href="${'/videos/' + one.videoFeature}" target="_blank">查看更多</a>
            <div class="clearfix"></div>
        </div>
        <div class="content">
            <ul th:unless="${#lists.isEmpty(one.videoList)}">
                <li th:each="video1:${one.videoList}">
                    <a th:href="'/video/'+${video1.videoId}+'.htm'">
                        <div class="photo">
                            <div class="pic">
                                    <img th:src="${video1.videoPic}" th:alt="${video1.videoTitle}">
                            </div>
                            <div class="time">
                                <h1 th:unless="${#strings.isEmpty(video1.newVideoTime)}" th:text="${video1.newVideoTime}" >03:09</h1>
                            </div>
                        </div>
                        <div class="name">
                                <h1 th:unless="${#strings.isEmpty(video1.videoTitle)}" th:text="${video1.videoTitle}">坤博幸福城不负万众期待 耀世开盘</h1>
                        </div>
                        <div class="info">
                        	<div class="touxiang">
                        		<img th:if="${!#strings.isEmpty(video1.videoTypeLogo)}" th:src="${video1.videoTypeLogo}" />
                        	</div>
                        	<span th:text="${video1.videoTypeName}">楼盘活动</span>
                        	<div class="video_r">
                        	
                        		<div class="video_ll">
                        			<img src="https://static.fangxiaoer.com/web/images/video/spbf_img_ll.png"></div>
                        		<span class="span_div mr14" th:text="${video1.visitNum}">3.1K</span>
                        		<div class="video_dz"><img src="https://static.fangxiaoer.com/web/images/video/spbf_img_dz.png"></div>
                        		<span class="span_div" th:text="${video1.videoLikeNum}">9</span>
                        	</div>
                        </div>
                    </a>
                </li>
                <div class="clearfix"></div>
            </ul>
        </div>
    </div>
</div>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
<!--<div th:include="house/detail/fragment_login::login"></div>-->
</body>
<style>

</style>
<script>
//    var player = new Aliplayer({
//            "id": "player-con",
//            "source": "[[${video?.videoUrl}]]",
//            "width": "880px",
//            "height": "495px",
//            "autoplay": false,
//            "isLive": false,
//            "cover": "[[${video?.videoPic}]]",
//            "rePlay": false,
//            "playsinline": true,
//            "preload": false,
//            "controlBarVisibility": "hover",
//        }, function (player) {
//            console.log("播放器创建了。");
//        }
//    );
    var player = new Aliplayer({
            "id": "player-con",
            "source": "[[${video.videoUrl}]]",
            "width": "880px",
            "height": "500px",
            "autoplay": true,
            "isLive": false,
            "rePlay": false,
            "playsinline": true,
            "preload": true,
            "controlBarVisibility": "hover",
            "useH5Prism": true,
            "skinLayout": [
                {
                    "name": "bigPlayButton",
                    "align": "blabs",
                    "x": 30,
                    "y": 80
                },
                {
                    "name": "H5Loading",
                    "align": "cc"
                },
                {
                    "name": "errorDisplay",
                    "align": "tlabs",
                    "x": 0,
                    "y": 0
                },
                {
                    "name": "infoDisplay"
                },
                {
                    "name": "tooltip",
                    "align": "blabs",
                    "x": 0,
                    "y": 56
                },
                {
                    "name": "thumbnail"
                },
                {
                    "name": "controlBar",
                    "align": "blabs",
                    "x": 0,
                    "y": 0,
                    "children": [
                        {
                            "name": "progress",
                            "align": "blabs",
                            "x": 0,
                            "y": 44
                        },
                        {
                            "name": "playButton",
                            "align": "tl",
                            "x": 15,
                            "y": 12
                        },
                        {
                            "name": "timeDisplay",
                            "align": "tl",
                            "x": 10,
                            "y": 7
                        },
                        {
                            "name": "fullScreenButton",
                            "align": "tr",
                            "x": 10,
                            "y": 12
                        },
                        {
                            "name": "volume",
                            "align": "tr",
                            "x": 5,
                            "y": 10
                        }
                    ]
                }
            ]
        }, function (player) {
            console.log("播放器创建了。");
        }
    );
var _video = document.querySelector('video');
player.on('play', function(e) {
    _video.removeEventListener('click', play);
    _video.addEventListener('click', pause);
    $.ajax({
        type: "POST",
        data:{audioId: [[${video.videoId}]]},
        url: "/addAudioNum",
        dataType : 'json',
        success: function (data) {
            if(data.status == 1){
                console.log("success plus 1");
            }
        }
    });



});
player.on('pause', function(e) {
    _video.removeEventListener('click', pause);
    _video.addEventListener('click', play)
});

function play() {
    if (player) player.play();
}

function pause() {
    if (player) player.pause();
}
var sessionId = $("#sessionId").val();
$(".dzimg").click(function(){
	if(sessionId!=''){

	var videoid=$(".videoid").text();
	setVideoLike(videoid);
}
})



$(function(){
	//点赞为0显示点赞
	nopl();
	//鼠标划过点赞变色
	
		
	
		$(".dzimg").hover(function(){
			var likeimg=$("#hasLike").text();
			if(likeimg!=1){
		 	$("#likeUp").attr("src","https://static.fangxiaoer.com/web/images/video/img_ydz.png");
		 		}
   		},function(){
   			var likeimg=$("#hasLike").text();
   			if(likeimg!=1){
    		$("#likeUp").attr("src","https://static.fangxiaoer.com/web/images/video/img_dz.png");
   				
   			}

		});


		})
	function nopl(){
		var pl=$(".dzimg span").text();
			if(pl==0){
				$(".dzimg span").html("点赞");
				}
			}

	// 视频点赞功能
	function setVideoLike(videoId) {
		$("#login").hide();
		$.ajax({
			type: "POST",
			url: "/setVideoLike",
			data: {
				videoId: videoId,
				sessionId:sessionId
			},
			success: function(data) {
				if(data.status == 1) {
//					var likeNum = $("#likeNum").text();
					var likeNum = $("#likeNum").text();
					$(".dzimg span").text(++likeNum);
					// 这里有没有layui啥的，前端大佬可以美化一下，如果没有现成的就Alert也行，hh
					// 问一下产品和设计，如果这里点过赞了用不用实心显示
					alert("感谢您的点赞");
					$("#hasLike").text("1");
					$("#likeUp").attr("src","https://static.fangxiaoer.com/web/images/video/img_ydz.png");
				} else {
					alert(data.msg);
				}
			}
		})
	}
</script>
</html>