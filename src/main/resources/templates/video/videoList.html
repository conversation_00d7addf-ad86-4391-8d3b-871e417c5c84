<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title th:each="videoType:${videoFeature}" th:if="${videoType.selected}">
        <th:block th:text="${(#strings.isEmpty(videoType.id)?'房产视频':videoType.name)+',沈阳房产活动视频 - 房小二网'}"></th:block>
    </title>
    <meta name="keywords" content="房产活动,沈阳楼盘活动视频,沈阳房产活动视频,房小二视频，沈阳视频看房"/>
    <meta name="description" content="沈阳房小二网楼盘工程进度，追踪沈阳热点房产工程进度情况，报道热门项目开盘进度，为您提供最及时楼盘工程信息。房小二网-房地产专业媒体！"/>
    <meta name="mobile-agent" content="format=html5;url=https://m.fangxiaoer.com/video.htm">
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/video/videolist.css?v=2021115">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <!--<link rel="stylesheet" type="text/css" href="/css/videolist.css">-->
     <link href="https://static.fangxiaoer.com/m/static/css/video/animate.css" type="text/css" rel="stylesheet">

</head>
<body>
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=6,subNavIndex=1"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::videoSearch" th:with="type=5"></div>
<div class="position">
    <div class="content_box">
        <h1>您的位置 : <a th:href="@{'/'}">沈阳房产网</a> > <a th:href="@{'/news'}">资讯</a> >
            <a th:each="videoType:${videoFeature}" th:if="${videoType.selected}" th:href="${videoType.url}"><th:block   style="cursor: pointer" th:text="${#strings.isEmpty(videoType.id)?'不限':videoType.name}"></th:block></a></h1>
    </div>
</div>
<div class="nav_box">
    <div class="content_box">
        <ul>
            <li th:each="videoType:${videoFeature}"  th:class="${videoType.selected?'color':''}">
            	<a th:href="${videoType.url}"   th:text="${#strings.isEmpty(videoType.id)?'视频首页':videoType.name}"></a>
            	<span></span>
            </li>
            <div class="clearfix"></div>
        </ul>
    </div>
</div>
<div class="list_box">
    <div class="content_box">
        <div class="content">
            <ul>
                <li  th:each="v:${video}">
                    <a th:href="${'/video/'+v.videoId+'.htm'}" target="_blank">
                        <div class="photo">
                            <div class="pic">
                                    <img th:src="${v.videoPic}" th:alt="${v.videoTitle}"/>
                            </div>
                            <div class="time">
                                <h1 th:text="${#strings.isEmpty(v.newVideoTime)?'':v.newVideoTime}">02:27</h1>
                            </div>
                        </div>
                        <div class="name">
                                <h1 th:text="${#strings.isEmpty(v.videoTitle)?'':v.videoTitle}">坤博幸福城不负万众期待 耀世开盘坤博幸福城不负万众期待 耀世开盘坤博幸福城不负万众期待 耀世开盘</h1>
                        </div>
                        <div class="info">
                        	<div class="touxiang">
                        		<img th:if="${!#strings.isEmpty(v.videoTypeLogo)}" th:src="${v.videoTypeLogo}" />
                        	</div>
                        	<span class="infoname" th:text="${v.videoTypeName}"></span>
                        	<div class="video_r">
                        		<div class="video_ll"><img src="https://static.fangxiaoer.com/web/images/video/spbf_img_ll.png"></div>
                        		<span class="span_div mr14" th:text="${v.visitNum}">3.1K</span>
                        		<div class="video_dz"><img src="https://static.fangxiaoer.com/web/images/video/spbf_img_dz.png"></div>
                        		<span class="span_div" th:text="${v.videoLikeNum}"></span>
                        	</div>
                        </div>
                    </a>
                </li>
                <div class="clearfix"></div>
                <!--分页-->
                <div class="minPage">
                    <div class="page">
                        <div id="Pager1">
                            <div th:include="fragment/page :: page"></div>
                        </div>
                    </div>
                </div>
            </ul>
        </div>
    </div>

</div>
<script>
		var btn_x = 0;
    	var storage = window.sessionStorage;
		var videoSearch = storage.getItem("select");

			for(var i = 0; i < $(".content_box li").length; i++) {
			if($(".content_box li").eq(i).hasClass("color")) {
			var classname=$(this).className;
			if(classname==undefined || classname==''){
				$(".content_box li").eq(i).find("span").addClass("oncolor");
			}
				if(videoSearch != null && videoSearch != '') {
					if(i - videoSearch > 0) {
						$(".content_box  li span").eq(i).addClass("fadeInLeft");

					} else if(i - videoSearch < 0){
						$(".content_box  li span").eq(i).addClass("fadeInRight");

					}

					btn_x = i;
					setInfo1(btn_x);

				} else {
					btn_x = i;
					setInfo1(btn_x);
				}

			}
		}

			 //sessionStorage 储存搜索
	function setInfo1(select) {
		var storage = window.sessionStorage;
		storage.setItem('select', select);
	}
</script>
<div style="background:#f5f5f5;border-top:1px solid #eeeeee" th:include="fragment/fragment:: publish_footer2"></div>
<div th:include="fragment/fragment::common_meiqia"></div>
<div th:include="fragment/fragment::tongji"></div>
</body>
<style>


</style>
</html>