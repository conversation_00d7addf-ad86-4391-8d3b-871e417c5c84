<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'沈阳'+seoTitle+'写字楼_'+seoTitle+'写字间出售_沈阳写字楼 - 房小二网'}">沈阳写字楼_沈阳写字楼出租_沈阳写字间出售 - 房小二网</title>
    <meta name="keywords" th:content="${'沈阳'+seoTitle+'写字楼,沈阳'+seoTitle+'写字间出售,写字间转让'}"/>
    <meta name="description" th:content="${'房小二网为您提供丰富全面的沈阳'+seoTitle+'写字楼信息及最新沈阳'+seoTitle+'门市楼盘的出售转让信息,每天数千条真实有效信息帮您快速找到理想的写字间'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang5/'+mobileAgent}">
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20190718" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2018.css?v=20200103" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css" />
<body>
<style>
    #option .rPriceDomOk label input{width: 55px;height: 20px;border: 1px solid #eaeaea;outline: none;text-align: center;background: none;font-size: 12px;padding: 0;}
    #sortParamNew a{color:  #666;font-size: 16px;padding:  0 25px;line-height: 45px;display:  inline-block; border-right: 1px solid #eaeaea;}
    #sortParamNew a.allCodition{ padding: 0 25px;}
    #sortParamNew a.videoCodition{padding-left: 35px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/videoCodition.png) #fff 2px -1px;}
    #sortParamNew a.videoCodition.hover,.sort #sortParamNew .videoCodition:hover{background: url(https://static.fangxiaoer.com/web/images/ico/sign/videoCoditionH.png) #ff5200 2px -1px;}
    #sortParamNew a.goodCodition{padding-left: 35px; background: url(https://static.fangxiaoer.com/web/images/ico/sign/goodCodition.png) #fff 2px -1px;}
    #sortParamNew a.goodCodition.hover, .sort #sortParamNew a.goodCodition:hover{background: url(https://static.fangxiaoer.com/web/images/ico/sign/goodCoditionH.png) #ff5200 2px -1px;}
    #sortParamNew a.hover,.sort #sortParamNew a:hover{color: #ff5200;}
    .recommend dd>a.hideA p span{width: 53px}
    .rentListTese .tese div a {
        padding: 0 5px 0 20px !important;
    }
    .sub{ position: absolute; top: 0px; right: 0px; z-index: 10;}/*增加广告图标*/

    .shortRight .rightQzQgBtn a {
        width: 168px;
        line-height: 49px;
        padding: 0;
        margin-bottom: 14px;
        height: 49px;
        background: #FFF7EE;
        border: 1px solid #EAEAEA;
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #FF5200;
    }
    .shortRight .rightQzQgBtn a i{display: none;}
    .shortRight .rightQzQgBtn a:hover{ background-color: #FFF7EE; text-decoration: none; color: #FF5200;}

    /*右侧-房产快搜*/
    .gksou { width: 168px; border: 1px solid #EAEAEA; margin-bottom: 14px; font-size: 16px; font-family: Microsoft YaHei; font-weight: 400; color: #333333; }
    .gktp { width: 100%; border-bottom: 1px solid #EAEAEA; line-height: 46px; height: 46px; position: relative; }
    .gktp span { display: inline-block; width: 4px; height: 16px; margin-bottom: -1px; border-radius: 50px; background: #ff5200; margin-right: 8px; margin-left: 16px; }
    .gktp a{ font-size: 12px; color: #5097FF; cursor: pointer; user-select: none; position: absolute; bottom: -2px; right: 24px;}
    .gktp a:hover{ text-decoration: none; color: #5097FF;}
    .gkmn{ width: 100%; padding: 10px 5px 10px 16px; box-sizing: border-box;}
    .gkmn a{ display: block; width: 100%; position: relative; margin-bottom: 7px;}
    .gkmn a i{ display: inline-block; width: 15px; height: 15px; line-height: 15px; font-size: 12px; border-radius: 50%; background: #FF5200; color: #fff; text-align: center; margin-right: 8px;}
    .gkmn a em{ position: absolute; left: 24px; bottom: -1px;}
</style>
<form name="form1" method="post" action="" id="form1">

    <!--引入头部导航栏-->
    <div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=9"></div>
    <!--搜索栏-->
    <div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=9,listType=0"></div>

    <div class="main">
        <div class="crumbs">您的位置：
            <a th:href="@{/}" target="_blank">沈阳房产网</a> &gt;
            <a href="/scriptoriums/">写字楼房源</a>
            <th:block th:each="n,stat:${type}" th:if="${n.selected and stat.index > 0}">
                &gt;<a th:href="${'/scriptoriums/b' + n.id}" th:text="${'沈阳' + n.name}"></a>
                <th:block th:each="r, x: ${region}" th:if="${r.selected and x.index > 0}">
                    &gt;<a th:href="${'/scriptoriums/b' + n.id + '-r' + r.id}" th:text="${r.name}"></a>
                    <th:block th:if="${plate}">
                        <th:block th:each="j,m:${plate}" th:if="${j.selected and m.index > 0}">
                            &gt;<a th:href="${'/scriptoriums/b' + n.id+'-r' + r.id + '-j' + j.id}" th:text="${j.name}"></a>
                        </th:block>
                    </th:block>
                </th:block>
            </th:block>
            <th:block th:each="n,stat:${type}" th:if="${n.selected and stat.index == 0}">
                <th:block th:each="r,x: ${region}" th:if="${r.selected and x.index > 0}">
                    &gt;<a th:href="${'/scriptoriums/r' + r.id}" th:text="${r.name}"></a>
                    <th:block th:if="${plate}">
                        <th:block th:each="j,m:${plate}" th:if="${j.selected and m.index > 0}">
                            &gt;<a th:href="${'/scriptoriums/r' + r.id + '-j' + j.id}" th:text="${j.name}"></a>
                        </th:block>
                    </th:block>
                </th:block>
            </th:block>
        </div>
        <div id="option">
            <ul>
                <!--供求-->
                <li><p>供求：</p>
                    <a th:each="t,i:${type}" th:if="${i.index == 0}"  th:href="${t.url}" th:text="${'不限'}"  th:class="${t.selected}? 'hover':''"></a>
                    <a th:each="t,i:${type}" th:if="${#strings.toString(t.id) eq '5'}" th:id="${'t'+t.id}"  th:href="${t.url}" th:text="${t.name}"  th:class="${t.selected}? 'hover':''"></a>
                    <a th:each="t,i:${type}" th:if="${#strings.toString(t.id) eq '4'}" th:id="${'t'+t.id}"  th:href="${t.url}" th:text="${t.name}"  th:class="${t.selected}? 'hover':''"></a>
                </li>
                <!--区域-->
                <li class=""><p>区域：</p>
                    <a th:each="r,ri:${region}" th:href="${r.url}" th:id="'r'+${r.id}" th:if="${r.id ne '10'and r.id ne '11'}" th:class="${r.selected}? 'hover':''">
                        <th:block  th:text="${ri.index == 0?'不限':r.name}"></th:block>
                        <i th:if="${!#strings.isEmpty(r.id)}"></i>
                    </a><br>
                </li>
                <!--板块-->
                <li th:if="${plate}" id="Search_zf" class="leibie" style="display: block;"><p style="display: none">板块：</p>
                        <a th:each="j,stat:${plate}" th:text="${stat.index ==0?'不限': j.name}" th:href="${j.url}" th:id="'j'+${j.id}" th:class="${j.selected}? 'hover':''"></a>
                </li>
                <!--售价-->
                <li th:if="${typeId ne '4'}"><p>售价：</p>
                    <a th:each="sp,stat:${sprice}" th:href="${sp.url}" th:text="${stat.index ==0?'不限': sp.name}" th:id="'sp'+${sp.id} " th:class="${sp.selected}?'hover':''">></a>
                    <div id="Search_sPriceDomOk">
                        <label>
                            <input name="sminPrice" id="sminPrice" maxlength="4" type="text" th:value="${sminPrice}" > - <input name="smaxPrice" id="smaxPrice" maxlength="4" type="text" th:value="${smaxPrice}" > 万
                            <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button">
                        </label>
                    </div>
                </li>
                <th:block  th:if="${typeId ne '5'}">
                    <li class="rPriceDomOk"><p>租金：</p><!--总价-->
                        <a th:each="rp,stat:${rprice}" th:href="${rp.url}" th:text="${stat.index==0?'不限':rp.name}" th:id="'rp'+${rp.id}" th:class="${rp.selected}?'hover':''">></a>
                        <div id="Search_rPriceDomOk">
                            <label>
                                <input name="rminPrice" id="rminPrice" maxlength="6" type="text" th:value="${rminPrice}" > - <input name="rmaxPrice" id="rmaxPrice" maxlength="6" type="text" th:value="${rmaxPrice}" > 元/月
                                <input onclick="__doPostBack('Search$Btn_Search2','')" name="Search$Btn_Search2" id="Search_Btn_Search2" value="确定" class="btn_search" style="display: none;" type="button">
                            </label>
                        </div>
                        <a th:each="cp,i:${choosePrice}" th:if="${i.index ne 0}" th:text="${cp.name}" th:id="'cp'+${cp.id}"  th:href="${cp.url}" th:class="${cp.selected?'Search_uPriceDom hover':'Search_uPriceDom'}"></a>
                    </li>
                    <li class="uPriceDomOk" ><p>租金：</p><!--单价-->
                        <a th:each="up,stat:${uprice}" th:href="${up.url}" th:text="${stat.index==0?'不限':up.name}" th:id="'up'+${up.id}" th:class="${up.selected}?'hover':''">></a>
                        <div id="Search_uPriceDomOk">
                            <label>
                                <input name="uminPrice" id="uminPrice" maxlength="4" type="text" th:value="${uminPrice}" > - <input name="umaxPrice" id="umaxPrice" maxlength="4" type="text" th:value="${umaxPrice}" > 元/㎡·天
                                <input onclick="__doPostBack('Search$Btn_Search3','')" name="Search$Btn_Search3" id="Search_Btn_Search3" value="确定" class="btn_search" style="display: none;" type="button">
                            </label>
                        </div>
                        <a th:each="cp,i:${choosePrice}" th:if="${i.index ne 0}" th:text="${cp.name}" th:id="'cp'+${cp.id}"  th:href="${cp.url}" th:class="${cp.selected?'Search_uPriceDom hover':'Search_uPriceDom'}"></a>
                    </li>

                    <script>
                        var officeUrl = location.pathname;
                        if (officeUrl.indexOf("cp2") != -1) {
                            $(".rPriceDomOk").hide()
                        }else{
                            $(".uPriceDomOk").hide()
                        }
                    </script>
                </th:block>
                <li ><p>面积：</p>
                    <a th:each="a,stat:${area}" th:href="${a.url}" th:text="${stat.index==0?'不限':a.name}" th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
                    <div id="Search_BuildAreaDomOk">
                        <label>
                            <input name="ominArea" id="ominArea" maxlength="5" type="text" th:value="${ominArea}"> - <input name="omaxArea" id="omaxArea" maxlength="5" type="text" th:value="${omaxArea}"> ㎡
                            <input onclick="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button">
                        </label>
                    </div>
                </li>
                <li><p>类型：</p>
                    <a th:each="c,stat:${category}" th:href="${c.url}" th:text="${stat.index == 0 ?'不限': c.name}" th:id="${'c'+c.id}" th:class="${c.selected}? 'hover':''"></a>
                </li>
                <li class="rentListTese">
                    <div class="tese" style="border: none">
                        <p>特色：</p>
                        <div>
                            <a th:id ="${x.id}" th:each="x,i:${houseTrait}" th:if="${i.index != 0}" th:href="${x.url}" th:text="${x.name}" th:class="${x.selected}? 'hover':''">></a>
                            <!--点击房源特色js-->
                            <script >
                                $(".tese a").click(function () {
//                                var teseid = $(this).attr("id").replace("tese", "");
                                    var ref = location.pathname;
                                    if(ref.indexOf('scriptoriums/') == -1) ref +='/';
                                    ref = ref.replace(/[-]*[x][0-9,]*[0-9]?$/, "");
                                    ref = ref.replace(/[-]*[n][0-9]?$/, "");
                                    if($(this).attr("id")){
                                        if ($(this).attr("class") == null) {
                                            $(this).attr("class", "hover");
                                        } else {
                                            $(this).attr("class", "");
                                        }
                                        var uIds="";
                                        $(".tese a").each(function () {
                                            if ($(this).attr("class") == "hover") {
                                                if($(this).attr("id")!=null && $(this).attr("id")!=''&& $(this).attr("id")!=undefined){
                                                    uIds += $(this).attr("id")+",";
                                                }
                                            }
                                        });
                                        uIds = uIds.replace(/[,]$/,"");
                                        if(uIds != null && uIds != undefined && uIds != ''){
                                            $(this).attr("href",ref+"-x"+uIds);
                                        }else {
                                            $(this).attr("href",ref);
                                        }
                                    }else {
                                        $(this).attr("href",ref);
                                    }
                                })
                            </script>
                        </div>
                    </div>
                </li>

            </ul>
        </div>
        <div id="option_other">
            <ul>
                <li><p>更多：</p>
                    <div class="select_box">
                        <div class="select_info" th:each="f,i:${fitment}" th:if="${f.selected}" th:text="${i.index == 0 ? '装修':f.name}">类型</div>
                        <ul>
                            <li th:each="f,i:${fitment}">  <a th:href="${f.url}" th:text="${i.index == 0 ? '装修':f.name}" th:class="${f.selected}? 'hover':''"></a></li>
                        </ul>
                    </div>
                    <div class="select_box">
                        <div class="select_info" th:each="memberType,i:${memberType}" th:if="${memberType.selected}" th:text="${i.index == 0 ? '来源':memberType.name}">来源</div>
                        <ul>
                            <li th:each="memberType,i:${memberType}">
                                <a th:href="${memberType.url}" th:text="${i.index ==0 ? '来源':memberType.name}" th:class="${memberType.selected? 'hover':''}"></a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
        <div id="option_info"  style="display: none">
            <b >已选：</b>
            <!--头部搜索-->
            <div th:if="${!#strings.isEmpty(searchKey) and !#strings.isEmpty(searchKey)}">
                <span class="condition"><th:block th:text="${searchKey}"></th:block></span>
                <i class="cleanUrl" id="clearSearchKey" th:value="${searchKey}"></i>
            </div>
            <!--供求-->
            <div th:each="t:${type}" th:if="${t.selected and t.name ne '全部'}">
                <span class="condition"><th:block th:text="${t.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'b'+t.id}"></i>
            </div>
            <!--区域-->
            <div th:each="region:${region}" th:if="${region.selected and region.name ne '全部'}">
                <span class="condition"><th:block th:text="${region.name}"></th:block></span>
                <i class="cleanUrl" th:each="plate:${plate}" th:if="${plate.selected and plate.name ne '全部'}"
                   th:value="${'j'+plate.id+'-r'+region.id}"></i>
                <i class="cleanUrl" th:each="plate:${plate}" th:if="${plate.selected and plate.name eq '全部'}"
                   th:value="${'r'+region.id}"></i>
            </div>
            <!--板块-->
            <div th:each="plate:${plate}" th:if="${plate.selected and plate.name ne '全部'}">
                <span class="condition"><th:block th:text="${plate.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'j'+plate.id}"></i>
            </div>
            <!--售价-->
            <div th:each="sprice:${sprice}" th:if="${sprice.selected and sprice.name ne '全部'}">
                <span class="condition"><th:block th:text="${sprice.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'p'+sprice.id}"></i>
            </div>
            <!--自定义售价-->
            <div th:if="${!#strings.isEmpty(sminPrice) or !#strings.isEmpty(smaxPrice)}">
                <span class="condition">
                    <th:block th:if="${#strings.isEmpty(sminPrice)}" th:text="${'小于等于'+smaxPrice+'万元'}"></th:block>
                    <th:block th:if="${#strings.isEmpty(smaxPrice)}" th:text="${'大于等于'+sminPrice+'万元'}"></th:block>
                    <th:block th:if="${!#strings.isEmpty(smaxPrice) and !#strings.isEmpty(sminPrice)}" th:text="${sminPrice+'-'+smaxPrice+'万元'}"></th:block>
                </span>
                <i class="cleanUrl" th:if="${#strings.isEmpty(sminPrice)}" th:value="${'sm'+smaxPrice}"></i>
                <i class="cleanUrl" th:if="${#strings.isEmpty(smaxPrice)}" th:value="${'sn'+sminPrice}"></i>
                <i class="cleanUrl" th:if="${!#strings.isEmpty(smaxPrice) and !#strings.isEmpty(sminPrice)}" th:value="${'sn'+sminPrice+'-'+'sm'+smaxPrice}"></i>
            </div>
            <!--租金 总价-->
            <div th:each="rprice:${rprice}" th:if="${rprice.selected and rprice.name ne '全部'}">
                <span class="condition"><th:block th:text="${rprice.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'q'+rprice.id}"></i>
            </div>
            <!--自定义租金 总价-->
            <div th:if="${!#strings.isEmpty(rminPrice) or !#strings.isEmpty(rmaxPrice)}">
                <span class="condition">
                    <th:block th:if="${#strings.isEmpty(rminPrice)}" th:text="${'小于等于'+rmaxPrice+'元/月'}"></th:block>
                    <th:block th:if="${#strings.isEmpty(rmaxPrice)}" th:text="${'大于等于'+rminPrice+'元/月'}"></th:block>
                    <th:block th:if="${!#strings.isEmpty(rmaxPrice) and !#strings.isEmpty(rminPrice)}" th:text="${rminPrice+'-'+rmaxPrice+'元/月'}"></th:block>
                </span>
                <i class="cleanUrl" th:if="${#strings.isEmpty(rminPrice)}" th:value="${'rm'+rmaxPrice}"></i>
                <i class="cleanUrl" th:if="${#strings.isEmpty(rmaxPrice)}" th:value="${'rn'+rminPrice}"></i>
                <i class="cleanUrl" th:if="${!#strings.isEmpty(rmaxPrice) and !#strings.isEmpty(rminPrice)}" th:value="${'rn'+rminPrice+'-'+'rm'+rmaxPrice}"></i>
            </div>
            <!--租金 单价-->
            <div th:each="uprice:${uprice}" th:if="${uprice.selected and uprice.name ne '全部'}">
                <span class="condition"><th:block th:text="${uprice.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'up'+uprice.id}"></i>
            </div>
            <!--自定义租金 单价-->
            <div th:if="${!#strings.isEmpty(uminPrice) or !#strings.isEmpty(umaxPrice)}">
                <span class="condition">
                    <th:block th:if="${#strings.isEmpty(uminPrice)}" th:text="${'小于等于'+umaxPrice+'元/㎡·天'}"></th:block>
                    <th:block th:if="${#strings.isEmpty(umaxPrice)}" th:text="${'大于等于'+uminPrice+'元/㎡·天'}"></th:block>
                    <th:block th:if="${!#strings.isEmpty(umaxPrice) and !#strings.isEmpty(uminPrice)}" th:text="${uminPrice+'-'+umaxPrice+'元/㎡·天'}"></th:block>
                </span>
                <i class="cleanUrl" th:if="${#strings.isEmpty(uminPrice)}" th:value="${'um'+umaxPrice}"></i>
                <i class="cleanUrl" th:if="${#strings.isEmpty(umaxPrice)}" th:value="${'un'+uminPrice}"></i>
                <i class="cleanUrl" th:if="${!#strings.isEmpty(umaxPrice) and !#strings.isEmpty(uminPrice)}" th:value="${'un'+uminPrice+'-'+'um'+umaxPrice}"></i>
            </div>
            <!--面积-->
            <div th:each="area:${area}" th:if="${area.selected and area.name ne '全部'}">
                <span class="condition"><th:block th:text="${area.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'a'+area.id}"></i>
            </div>
            <!--自定义面积-->
            <div th:if="${!#strings.isEmpty(ominArea) or !#strings.isEmpty(omaxArea)}">
                <span class="condition">
                    <th:block th:if="${#strings.isEmpty(ominArea)}" th:text="${'小于等于'+omaxArea+'㎡'}"></th:block>
                    <th:block th:if="${#strings.isEmpty(omaxArea)}" th:text="${'大于等于'+ominArea+'㎡'}"></th:block>
                    <th:block th:if="${!#strings.isEmpty(omaxArea) and !#strings.isEmpty(ominArea)}" th:text="${ominArea+'-'+omaxArea+'㎡'}"></th:block>
                </span>
                <i class="cleanUrl" th:if="${#strings.isEmpty(ominArea)}" th:value="${'ha'+omaxArea}"></i>
                <i class="cleanUrl" th:if="${#strings.isEmpty(omaxArea)}" th:value="${'la'+ominArea}"></i>
                <i class="cleanUrl" th:if="${!#strings.isEmpty(omaxArea) and !#strings.isEmpty(ominArea)}" th:value="${'la'+ominArea+'-'+'ha'+omaxArea}"></i>
            </div>
            <!--类型-->
            <div th:each="category:${category}" th:if="${category.selected and category.name ne '全部'}">
                <span class="condition"><th:block th:text="${category.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'t'+category.id}"></i>
            </div>
            <!--装修-->
            <div th:each="fitment:${fitment}" th:if="${fitment.selected and fitment.name ne '全部'}">
                <span class="condition"><th:block th:text="${fitment.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'f'+fitment.id}"></i>
            </div>
            <!--来源-->
            <div th:each="memberType:${memberType}" th:if="${memberType.selected and memberType.name ne '全部'}">
                <span class="condition"><th:block th:text="${memberType.name}"></th:block></span>
                <i class="cleanUrl" th:value="${'m'+memberType.id}"></i>
            </div>
            <a href="/scriptoriums/" class="clean">清空筛选条件</a>
            <script>
                /*判断是否显示条件栏目*/
                $(function () {
                    if($(".condition").text() != ""){
                        $("#option_info").css("display","block");
                    }
                })
                /*去掉条件标签*/
                $(".cleanUrl").click(function () {
                    var oldUrl = location.pathname;
                    if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
                        var moreUrl ="-" + $(this).attr("value");
                    }else{ var moreUrl = $(this).attr("value");}

                    var newUrl = oldUrl.replace(moreUrl,"");
                    window.location.href = newUrl;
                    //跳到newUrl指定链接
                });
                //清除头部搜索key
                $("#clearSearchKey").click(function () {
                    var nowUrl = location.pathname;
                    var searchTitle = nowUrl.indexOf('search');//只有手输内容搜索
                    var newUrl = nowUrl.substring(0,searchTitle);
                    window.location.href = newUrl;
                });
            </script>
        </div>

        <script type="text/javascript">
            //筛选的确定按钮显示隐藏
            function price(priceIdName) {
                if($("#"+priceIdName).length >0){
                    $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
                    $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
                    var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
                    var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
                    if (num1 == "" && num2 != "") {
                        $("#" + priceIdName + " input").eq(0).val("0");
                        $("#" + priceIdName + " input").eq(2).show();
                    } else if (num2 == "" && num1 != "") {
                        $("#" + priceIdName + " input").eq(2).show();
                    } else if (num1 != "" || num2 != "") {
                        $("#" + priceIdName + " input").eq(2).show();
                    } else {
                        $("#" + priceIdName + " input").eq(2).hide();
                    }
                }
            }

            price("Search_BuildAreaDomOk");
            price("Search_sPriceDomOk");
            $("#Search_sPriceDomOk input").keyup(function () {
                price("Search_sPriceDomOk");
            })
            $("#Search_rPriceDomOk input").keyup(function () {
                price("Search_rPriceDomOk");
            })
            $("#Search_uPriceDomOk input").keyup(function () {
                price("Search_uPriceDomOk");
            })
            $("#Search_BuildAreaDomOk input").keyup(function () {
                price("Search_BuildAreaDomOk");
            })
            $("#Search_sPriceDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                     $("#Search_Btn_Search1").click()
                }
            });
            $("#Search_rPriceDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                    $("#Search_Btn_Search2").click()
                }
            });
            $("#Search_uPriceDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                    $("#Search_Btn_Search3").click()
                }
            });
            $("#Search_BuildAreaDomOk").keydown(function (event) {
                if (event.keyCode == 13) {
                    $("#Search_Btn_Searchs").click()
                }
            });
            //此段js用于手填面积和价格连接拼接处理
            function __doPostBack(pager1, page) {
                var url = window.location.pathname;
                if (pager1 == "Search$Btn_Search1") {
                    var priceBegin = $("#sminPrice").val();
                    var priceEnd = $("#smaxPrice").val();
                    var ref1 = url.replace(/-sn[0-9]\d*/,''); //k最小值
                    var ref2 = ref1.replace(/-sm[0-9]\d*/,'');  //x最大值
                    if (parseInt(priceEnd) < parseInt(priceBegin)){
                        priceEnd = [priceBegin,priceBegin=priceEnd][0];
                    }
                    if(priceBegin != "" && priceBegin != 0) {
                        ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-rn[0-9]\d*/, '').replace(/\/rn[0-9]\d*/, '\/').replace(/-rm[0-9]\d*/, '').replace(/\/rm[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-un[0-9]\d*/, '').replace(/\/un[0-9]\d*/, '\/').replace(/-um[0-9]\d*/, '').replace(/\/um[0-9]\d*/, '\/') + "-sn" + priceBegin;
                    }
                    if(priceEnd != "") {
                        ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-rn[0-9]\d*/, '').replace(/\/rn[0-9]\d*/, '\/').replace(/-rm[0-9]\d*/, '').replace(/\/rm[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-un[0-9]\d*/, '').replace(/\/un[0-9]\d*/, '\/').replace(/-um[0-9]\d*/, '').replace(/\/um[0-9]\d*/, '\/') + "-sm" + priceEnd;
                    }
                    location.href = ref2;
                }
                if (pager1 == "Search$Btn_Search2") {
                    var priceBegin = $("#rminPrice").val();
                    var priceEnd = $("#rmaxPrice").val();
                    var ref1 = url.replace(/-rn[0-9]\d*/,''); //k最小值
                    var ref2 = ref1.replace(/-rm[0-9]\d*/,'');  //x最大值
                    if (parseInt(priceEnd) < parseInt(priceBegin)){
                        priceEnd = [priceBegin,priceBegin=priceEnd][0];
                    }
                    if(priceBegin != "" && priceBegin != 0) {
                        ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-un[0-9]\d*/, '').replace(/\/un[0-9]\d*/, '\/').replace(/-um[0-9]\d*/, '').replace(/\/um[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-sn[0-9]\d*/, '').replace(/\/sn[0-9]\d*/, '\/').replace(/-sm[0-9]\d*/, '').replace(/\/sm[0-9]\d*/, '\/') + "-rn" + priceBegin;
                    }
                    if(priceEnd != ""){
                        ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-un[0-9]\d*/,'').replace(/\/un[0-9]\d*/,'\/').replace(/-um[0-9]\d*/,'').replace(/\/um[0-9]\d*/,'\/');
                        ref2 = ref2.replace(/-sn[0-9]\d*/,'').replace(/\/sn[0-9]\d*/,'\/').replace(/-sm[0-9]\d*/,'').replace(/\/sm[0-9]\d*/,'\/') + "-rm" + priceEnd;
                    }
                    location.href = ref2;
                }
                if (pager1 == "Search$Btn_Search3") {
                    var priceBegin = $("#uminPrice").val();
                    var priceEnd = $("#umaxPrice").val();
                    var ref1 = url.replace(/-un[0-9]\d*/,''); //k最小值
                    var ref2 = ref1.replace(/-um[0-9]\d*/,'');  //x最大值
                    if (parseInt(priceEnd) < parseInt(priceBegin)){
                        priceEnd = [priceBegin,priceBegin=priceEnd][0];
                    }
                    if(priceBegin != "" && priceBegin != 0){
                        ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-sn[0-9]\d*/,'').replace(/\/sn[0-9]\d*/,'\/').replace(/-sm[0-9]\d*/,'').replace(/\/sm[0-9]\d*/,'\/');
                        ref2 = ref2.replace(/-rn[0-9]\d*/,'').replace(/\/rn[0-9]\d*/,'\/').replace(/-rm[0-9]\d*/,'').replace(/\/rm[0-9]\d*/,'\/') + "-un" + priceBegin;
                    }
                    if(priceEnd != ""){
                        ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                        ref2 = ref2.replace(/-sn[0-9]\d*/,'').replace(/\/sn[0-9]\d*/,'\/').replace(/-sm[0-9]\d*/,'').replace(/\/sm[0-9]\d*/,'\/');
                        ref2 = ref2.replace(/-rn[0-9]\d*/,'').replace(/\/rn[0-9]\d*/,'\/').replace(/-rm[0-9]\d*/,'').replace(/\/rm[0-9]\d*/,'\/') + "-um"+ priceEnd;
                    }
                    location.href = ref2;
                }
                if (pager1 == "Search$Btn_Searchs") {
                    var areaBegin = $("#ominArea").val();
                    var areaEnd = $("#omaxArea").val();
                    var ref1 = url.replace(/-la[0-9]\d*/,''); //y最小值
                    var ref2 = ref1.replace(/-ha[0-9]\d*/,'');  //e最大值
                    if (parseInt(areaEnd) < parseInt(areaBegin)){
                        areaEnd = [areaBegin,areaBegin=areaEnd][0];
                    }
                    if(areaBegin != "" && areaBegin != 0)
                        ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-la" + areaBegin;
                    if(areaEnd != "")
                        ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') +"-ha" + areaEnd;
                    location.href = ref2;
                }
            }
        </script>
        <div class="cl"></div>
        <div id="main">
            <div class="bannerLeft">

                <div id="left" class="longLeft"  style="border: none">
                    <!--列表页新增广告-->
<!--                    <div th:include="secondhouse/fragment::advert_recommand"></div>-->
                    <div style="border: 1px solid #eaeaea">
                        <div style="border-bottom: 1px solid #eee;">
                            <p id="screening_Items" style="margin-top: 12px;margin-right: 15px">
                                <a id="originalOrder" href="" th:class="${orderKey eq '0'?'hover':''}">综合排序</a>
                                <a  id="2" onclick="changeUrl(this)" th:class="${orderKey eq '1'? 'sort_jg up':(orderKey eq '2'?'sort_jg down':'')}" href="">价格</a>
                                <a  id="4" onclick="changeUrl(this)" th:class="${orderKey eq '3' or orderKey eq '4'?(orderKey eq '3'? 'sort_jg up':'sort_jg down') :''}" href="">面积</a>
                            </p>
                            <p id="sortParamNew">
<!--                                <a id="" href="/scriptoriums/-W2" class="hover">全部</a>-->
                                <a id="2" href="" class="allCodition sortParamNewA" style="padding: 0 25px;">全部</a>
                                <!--                            <a id="4" href="" class="videoCodition sortParamNewA">视频看房</a>-->
                                <!--<a id="6" href="" class="goodCodition">优质好房</a>-->
                                <script>
                                    $(document).ready(function(){
                                        var ref = location.pathname;
                                        var y = ref.split('/shops/');
                                        var x = y.length > 1 ? y[1] : "";
                                        if(x.indexOf('W') != -1){
                                            var num = x.split('W')[1].substring(0,1);
                                            if(num == 2 || num == 4 || num == 6){
                                                $("#sortParamNew a[id ='"+ num+"']").addClass("hover");
                                            }else{
                                                if(num%2 == 1){
                                                    $("#sortParamNew a[id ='"+ num+"']").addClass("sort_jg");
                                                    $("#sortParamNew a[id ='"+ num+"']").attr("id",num-1);
                                                }else{
                                                    num = parseInt(num) +1;
                                                    $("#sortParamNew a[id ='"+ num+"']").addClass("sort_jg");
                                                }
                                            }
                                        }else{
                                            $("#sortParamNew a:eq(0)").addClass("hover");
                                        }
                                        var ref2 = ref.replace(/-W[0-6]/,'').replace(/-n[0-9]/,'');
                                        $("#sortParamNew a.sortParamNewA").each(function () {
                                            var ids = $(this).attr("id");
                                            var ref3 = ref2 + '-W' + ids;
                                            $(this).attr("href",ref3);
                                        });
                                    });
                                </script>
                            </p>

                        </div>


                           <!-- <p id="sort_qh">
                                <a th:each="h,i:${showWay}" th:if="${i.index != 0}" th:href="${h.url}"    th:class="${h.selected or (#strings.isEmpty(sw) and i.index ==1)?'hover':''}"></a>
                            </p>-->
                            <script th:inline="javascript">
                                var pageUrl = [[${pageUrl}]];
                                var orderKey = [[${orderKey}]];
                                pageUrl = pageUrl.replace(/-?o[0-9]/,'');
                                $("#originalOrder").attr("href", pageUrl+"-o0");
                                function changeUrl(obj) {
                                    if(orderKey != $(obj).attr("id")) {
                                        orderKey =  $(obj).attr("id");
                                    }else {
                                        orderKey =  parseInt($(obj).attr("id"))-1;
                                    }
                                    if(typeof(orderKey)=="undefined"){
                                        $(obj).attr("href", pageUrl);
                                    }
                                    $(obj).attr("href", pageUrl+'-o'+orderKey);
                                }
                            </script>

                        <div class="cl"></div>
                        <!--<div class="content">小二为你找到<i th:text="${#strings.isEmpty(msg)?'': msg}"></i>个符合条件的房源</div>-->
                        <div class="contentMain" id="saleCol">
                            <!--以下为list形式-->
                            <div class="warning" th:if="${#lists.isEmpty(offices) and sw ne '2'}" >
                                <p>
                                    很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。
                                    <br> 请重试<!--<a th:href="@{/helpSearch}" target="_blank">点击免费体验购房服务方案>></a>-->
                                </p>
                            </div>
                            <div id=replie class="house_left" th:if="${sw ne '2'}">
                                <div class="inf" th:if="${!#lists.isEmpty(offices)}" th:each="shop:${offices}">
                                    <a th:href="${'/scriptorium/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
        <!--                                <i th:if="${'0,1,2,3,4'.indexOf(shop.picNum) eq -1}" th:text="${shop.picNum}"></i>-->
                                        <span class="sub" th:if="${#strings.toString(shop.auction) eq '1'}">
                                            <img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" />
                                        </span>
                                        <span class="sub" th:if="${#strings.toString(shop.auction) ne '1' && shop.stickOrder eq '-1'}">
                                            <img  src="https://static.fangxiaoer.com/web/images/ico/sign/ico_ad.png" />
                                        </span>

                                        <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': shop.pic}" th:alt="${#strings.isEmpty(shop.title)? '':shop.title}">
                                        <!--VR and 视频都存在 -->
                                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                                            <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                                            <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                                        </s>
                                        <!--VR存在 -->
                                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                                            <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                                        </s>
                                        <!--视频存在 -->
                                        <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                                            <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                                        </s>
                                    </a>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                    <div class="infCtn">
                                        <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)?'':'/scriptorium/'+shop.shopId+'.htm'}" >
                                            <div th:text="${#strings.isEmpty(shop.title)? '':shop.title}"  th:title="${#strings.isEmpty(shop.title)? '':shop.title}"></div>
                                            <i class="listIconBidPrice" th:if="${#strings.toString(shop.auction) eq '1'}"></i>
                                            <i class="listIconIstop" th:if="${#strings.toString(shop.auction) ne '1' && shop.stickOrder eq '-1'}"></i>
                                        </a>
                                        <div  class="fourSpan">
                                            <span th:text="${#strings.isEmpty(shop.area)? '':#numbers.formatInteger(shop.area,1)+'m²'}"></span>
                                            <span th:text="${#strings.isEmpty(shop.fitmentType)?'':shop.fitmentType}"></span>
                                        </div>
                                        <p  class="houseAddress"  th:if="${ !#strings.isEmpty(shop.officeName)  and !#strings.isEmpty(shop.regionName)  and !#strings.isEmpty(shop.plateName) and !#strings.isEmpty(shop.address)}">
                                            <s th:if="${ !#strings.isEmpty(shop.officeName)}" class="houseAddressSpance">
                                                <i th:text="${#strings.isEmpty(shop.officeName)?'':shop.officeName}"></i>
                                            </s>
                                            <s th:if="${!#strings.isEmpty(shop.regionName) and !#strings.isEmpty(shop.plateName) and !#strings.isEmpty(shop.address)}">
                                                <a th:href="${'/scriptoriums/-r'+shop.regionId}" th:text="${shop.regionName}"></a>-
                                                <a th:href="${'/scriptoriums/j'+shop.plateId+'-r'+shop.regionId}" th:text="${shop.plateName}"></a>-
                                                <i th:text="${#strings.isEmpty(shop.address)?'':shop.address}"></i>
                                            </s>
                                        </p>
                                        <div class="bottomtext">
                                            <div  class="houseItemIcon">
                                                <span th:class="${'tese_'+(i.index+1)}" th:if="${!#strings.isEmpty(shop.houseTraits) and i.index &lt; 3}" th:each="houseTrait,i:${#strings.toString(shop.houseTraits).split(',')}" th:text="${houseTrait}"></span>
                                            </div>
                                            <span class="personShow">
                                            <div th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '经纪人'}">
                                                <div class="agentShowImg"><img th:if="${!#strings.isEmpty(shop.Avatar)}" th:src="${shop.Avatar}" alt=""></div>
                                            <div class="agentShowImg"><img th:if="${#strings.isEmpty(shop.Avatar)}" src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt=""></div>

                                                   <i th:text="${#strings.isEmpty(shop.houseOwner)?'':shop.houseOwner}"></i>
                                                    <th:block  th:if="${!#strings.isEmpty(shop.spantime)}"th:text="${shop.spantime}+'更新'"></th:block>

                                                <i th:if="${!#strings.isEmpty(shop.IntermediaryName)}" th:text="${shop.IntermediaryName}"></i>
                                            </div>
                                            <div th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '个人'}">
                                                 <img  src="https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png" alt="" class="useImg">
                                                <i th:text="${shop.memberType}"></i>
                                            </div>
                                        </span>
                                        </div>
                                        <!--<p>-->
                                            <!--<span style="background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale/iconAuthentichouse.gif) no-repeat 9px 3px;padding-left: 78px;height: 26px;line-height: 24px;"  th:if="${!#strings.isEmpty(shop.memberType) and shop.memberType eq '个人'}" ></span>-->
                                        <!--</p>-->
                                    </div>
                                    <div class="infRight">
                                        <!--出售价格-->
                                        <th:block th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '5'}"  >
                                            <p class="infRightPrise">
                                                <th:block th:text="${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0'?'面议': (#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>
                                                <i th:text="${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0'?'':'万'}"></i>
                                            </p>
                                            <p><b th:text="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0'?'':(#strings.indexOf(shop.unitPrice,'.') eq -1 ? shop.unitPrice:#strings.toString(shop.unitPrice).replaceAll('0+?$','').replaceAll('[.]$',''))+'元/㎡'}"></b></p>
                                        </th:block>
                                        <!--出租价格-->
                                        <th:block th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '4'}"  >
                                            <p class="infRightPrise">
                                                <th:block th:utext="${#strings.isEmpty(shop.unitPrice) or #strings.toString(shop.unitPrice) eq '0.0'?'面议':(#strings.indexOf(shop.unitPrice,'.') eq -1 ? shop.unitPrice :#strings.toString(shop.unitPrice).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>
                                                <i th:text="${!#strings.isEmpty(shop.unitPrice) and  #strings.toString(shop.unitPrice) eq '0.0'?'':'元/㎡·天'}"></i>
                                            </p>
                                            <p><b th:text="${#strings.isEmpty(shop.price) or #strings.toString(shop.price) eq '0.0'?'':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+'元/月'}"></b></p>
                                        </th:block>
                                    </div>
                                </div>
                            </div>
                            <div id=reprow class="list" th:if="${sw eq '2'}">
                                <div class="warning" th:if="${#lists.isEmpty(offices)}">
                                    <p>
                                        很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。
                                        <br> 请重试<!--<a th:href="@{/helpSearch}" target="_blank">点击免费体验购房服务方案>></a>-->
                                    </p>
                                </div>
                                <div class="house shopHouse" th:each="sh:${offices}">
                                    <a class="house" th:href="${'/scriptorium/'+sh.shopId+'.htm'}"  target="_blank">
                                        <div  class="ico">
                                            <span class="ico_zd" th:if="${sh.stickOrder eq '-1'}"></span>
                                            <span class="jing" th:if="${#strings.toString(sh.auction) eq '1'}"></span>
                                        </div>
                                        <img th:src="${#strings.isEmpty(sh.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':sh.pic}" th:alt="${sh.title}" />
                                        <p>
                                            <text  th:text="${#strings.isEmpty(sh.area)? '':#numbers.formatInteger(sh.area,1)+'m²'}" ></text>
                                            <text style="float: right" th:text="${#strings.isEmpty(sh.shopCategoriesName)?'':sh.shopCategoriesName}" ></text>
                                        </p>
                                        <p class="sy_price"th:if="${!#strings.isEmpty(sh.shopType) and  sh.shopType eq '5'}"  >
                                            <span>
                                                <th:block th:text="${#strings.isEmpty(sh.price) or sh.price eq 0.00 ?'面议': (#strings.indexOf(sh.price,'.') eq -1 ? sh.price:#strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>
                                                <i th:text="${#strings.isEmpty(sh.price) or sh.price eq 0.00 ?'':'万'}"></i>
                                            </span>
                                        </p>
                                        <p class="sy_price" th:if="${!#strings.isEmpty(sh.shopType) and sh.shopType eq '4'}">
                                            <span>
                                                <th:block th:text="${#strings.isEmpty(sh.price) or sh.price eq 0.00 ?'面议':(#strings.indexOf(sh.price,'.') eq -1 ? sh.price:#strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>
                                                <i th:text="${!#strings.isEmpty(sh.price) and  sh.price eq 0.00?'':'元/月'}"></i>
                                            </span>
                                        </p>
                                        <p class="sy_price"  th:if="${!#strings.isEmpty(sh.shopType) and   sh.shopType eq '3'}">
                                            <span >
                                                <th:block  th:text="${#strings.isEmpty(sh.price) or sh.price eq '0.00' ?'面议':(#strings.indexOf(sh.price,'.') eq -1 ? sh.price:#strings.toString(sh.price).replaceAll('0+?$','').replaceAll('[.]$',''))}" ></th:block>
                                                <i th:text="${!#strings.isEmpty(sh.price) and !#strings.isEmpty(sh.paymentName) and sh.price eq '0.00'?'': (sh.payment eq '100' or sh.payment eq '50'or sh.payment eq '110' ? '元/年':'元/月')}"></i>
                                            </span>
                                            <b th:text="兑">兑</b>
                                        </p>
                                        <p>
                                            <span style="float: left;" >
                                                <th:block  th:text="${#strings.abbreviate(sh.title,28)}"></th:block>
                                            </span>
                                        </p>
                                        <p><span th:text="${sh.regionName}"></span> <th:block th:text="${sh.plateName}"></th:block> </p>
                                    </a>
                                </div>
                            </div>
                            <div class="cl"></div>
                            <!--<script type="text/javascript">-->
                                <!--var ary = location.href.split("&");-->
                                <!--jQuery(".picScroll-left").slide({ titCell: ".hd ul", mainCell: ".bd ul", autoPage: true, effect: "leftLoop", autoPlay: true, scroll: 1, vis: 5 });-->
                                <!--$(function () {-->
                                    <!--$(".bd li").mouseover(function () {-->
                                        <!--$(this).find("p").stop()-->
                                        <!--$(this).find("p").animate({ bottom: '0px' }, 300)-->
                                    <!--})-->
                                    <!--$(".bd li").mouseout(function () {-->
                                        <!--$(this).find("p").stop()-->
                                        <!--$(this).find("p").animate({ bottom: '-200px' }, 300)-->
                                    <!--})-->
                                <!--})-->
                            <!--</script>-->
                        </div>
                        <div class="cl"></div>
                        <div class="page">
                            <div th:include="fragment/page :: page"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="right" class="shortRight">
                <!--<div class="saleHouse">-->
                    <!--<a href="/officeSell" target="_blank">我要卖房</a>-->
                <!--</div> -->
                <!--小区专家-->
                <div th:include="secondhouse/fragment::plotexpert"></div>


                <!--房产快搜-->
                <!--<div class="gksou">-->
                    <!--<div class="gktp"><span></span>房产快搜 <a href="/fastSeek" target="_blank">详情>></a></div>-->
                    <!--<div class="gkmn">-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94587.htm" target="_blank"><i>1</i><em>房产过户</em></a>-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94586.htm" target="_blank"><i>2</i><em>购房流程</em></a>-->
                        <!--<a href="https://sy.fangxiaoer.com/news/94572.htm" target="_blank"><i>3</i><em>征信查询</em></a>-->
                        <!--&lt;!&ndash;<a href="/fastSeek#edu" target="_blank"><i>4</i><em>沈阳推荐小学</em></a>-->
                        <!--<a href="/fastSeek#edu" target="_blank"><i>5</i><em>沈阳推荐中学</em></a>&ndash;&gt;-->
                    <!--</div>-->
                <!--</div>-->

                <div class="rightQzQgBtn">
                    <a href="/officeSell" target="_blank"><i class="rightQzQgBtn-icon3"></i>我要出售</a>
                    <a th:href="@{'/helpSearch?ids=9'}" rel="8" dir="3" target="_blank"><i class="rightQzQgBtn-icon4"></i>买商业</a>
                    <!--<a href="/shoprent/" target="_blank"><i class="rightQzQgBtn-icon3"></i>我要出租</a>
                    <a href="/shopsell/" target="_blank"><i class="rightQzQgBtn-icon4"></i>我要出售</a>-->
                </div>
                <div>
                <div th:if="${!#lists.isEmpty(advert.officeAd1)}" class="shop_ad_pic_url">
                    <th:block th:each="officeAd1,i:${advert.officeAd1}">
                        <a  th:if="${#strings.toString(officeAd1.url).indexOf('http') ne -1}" th:href="${officeAd1.url}" target="_blank">
                            <img width="100%" th:src="${officeAd1.image}" th:alt="${officeAd1.projectName}">
                        </a>
                        <img th:if="${#strings.toString(officeAd1.url).indexOf('http') eq -1}" width="100%" th:src="${officeAd1.image}" th:alt="${officeAd1.projectName}">
                    </th:block>                </div>
                <div th:if="${!#lists.isEmpty(advert.officeAd2)}" class="shop_ad_pic_url">
                    <a th:each="officeAd2,i:${advert.officeAd2}" th:href="${officeAd2.url}" target="_blank">
                        <img width="100%" th:src="${officeAd2.image}" th:alt="${officeAd2.projectName}">
                    </a>
                </div>
                    <!--写字楼推荐-->
                   <!-- <div class="groomList" th:if="${!#lists.isEmpty(advert.command)}">
                        <h4><i></i>写字楼推荐</h4>
                        <ul>
                            <li th:each="command,i:${advert.command}">
                                <a th:href="${command.url}" target="_blank">
                                    <img th:src="${command.housePic}" alt="">
                                    <div class="imgBottm">
                                        <span th:if="${#strings.toString(command.realName) ne null && #strings.toString(command.realName) ne ''}" th:text="${command.realName}">小明</span>
                                        <span th:if="${#strings.toString(command.mobile) ne null && #strings.toString(command.mobile) ne ''}" th:text="${command.mobile}">12698456544</span>
                                    </div>
                                    <div class="oneH">
                                        <span th:if="${#strings.toString(command.regionName) ne null && #strings.toString(command.regionName) ne ''}" th:text="${command.regionName}">沈北新区</span>
                                        <p th:if="${#strings.toString(command.officeName) ne null && #strings.toString(command.officeName) ne ''}" th:text="${command.officeName}">华强城</p>
                                    </div>
                                    <div class="twoH">
                                        <p><span th:text="${command.fitmentTypeName}">2室2厅1卫</span><span th:text="${#strings.toString(command.area).contains('.')? #strings.toString(command.area).replaceAll('0+?$','').replaceAll('[.]$', '') : command.area}+'m²'"> 86.6㎡</span></p>
                                        <div><span th:utext="${command.price}">555</span><i></i></div>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </div>-->
            </div>
        </div>
        </div>
        <div class="cl"></div>
        <div class="cl"></div>
        <!--底部1-->
        <div th:include="fragment/fragment:: footer_list"></div>
        <div th:include="fragment/fragment::tongji"></div>
    </div>
</form>
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
</body>
</html>
