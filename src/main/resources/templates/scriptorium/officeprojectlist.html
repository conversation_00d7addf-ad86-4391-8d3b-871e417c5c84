<!DOCTYPE html>
<html xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${'沈阳'+seoTitle+'写字楼_'+seoTitle+'写字间出售_沈阳写字楼 - 房小二网'}">沈阳写字楼_沈阳写字楼出租_沈阳写字间出售 - 房小二网</title>
    <meta name="keywords" th:content="${'沈阳'+seoTitle+'写字楼,沈阳'+seoTitle+'写字间出售,写字间转让'}"/>
    <meta name="description" th:content="${'房小二网为您提供丰富全面的沈阳'+seoTitle+'写字楼信息及最新沈阳'+seoTitle+'门市楼盘的出售转让信息,每天数千条真实有效信息帮您快速找到理想的写字间'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang5/'+mobileAgent}">
    <meta http-equiv="Content-Type" content="text/html;charset=utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20181019" />
<!--    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/default2018.css?v=20190712" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/second/default2018.css?v=20200212" />

    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/exclusiveServe.css?t=20170706">
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://static.fangxiaoer.com/js/fxe_confirm.js" type="text/javascript"></script>
    <script src="/js/AjaxforJquery.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/esf_fxe_bnzf2017.js" type="text/javascript" charset="utf-8"></script>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/warning.css" rel="stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/second/officeprojectlist.css" rel="stylesheet" type="text/css" />
<body>
<style>
    #replie>div:first-child {
        padding-top: 20px;
    }
    .shortRight .rightQzQgBtn a {
        width: 168px;
        line-height: 49px;
        padding: 0;
        margin-bottom: 14px;
        height: 49px;
        background: #FFF7EE;
        border: 1px solid #EAEAEA;
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #FF5200;
    }
    .shortRight .rightQzQgBtn a i{display: none;}
    .shortRight .rightQzQgBtn a:hover{ background-color: #FFF7EE; text-decoration: none; color: #FF5200;}

    /*右侧-房产快搜*/
    .gksou { width: 168px; border: 1px solid #EAEAEA; margin-bottom: 14px; font-size: 16px; font-family: Microsoft YaHei; font-weight: 400; color: #333333; }
    .gktp { width: 100%; border-bottom: 1px solid #EAEAEA; line-height: 46px; height: 46px; position: relative; }
    .gktp span { display: inline-block; width: 4px; height: 16px; margin-bottom: -1px; border-radius: 50px; background: #ff5200; margin-right: 8px; margin-left: 16px; }
    .gktp a{ font-size: 12px; color: #5097FF; cursor: pointer; user-select: none; position: absolute; bottom: -2px; right: 24px;}
    .gktp a:hover{ text-decoration: none; color: #5097FF;}
    .gkmn{ width: 100%; padding: 10px 5px 10px 16px; box-sizing: border-box;}
    .gkmn a{ display: block; width: 100%; position: relative; margin-bottom: 7px;}
    .gkmn a i{ display: inline-block; width: 15px; height: 15px; line-height: 15px; font-size: 12px; border-radius: 50%; background: #FF5200; color: #fff; text-align: center; margin-right: 8px;}
    .gkmn a em{ position: absolute; left: 24px; bottom: -1px;}
</style>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=9"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=13,listType=0"></div>

<div class="main">
    <div class="crumbs">您的位置：
        <a th:href="@{/}" target="_blank">沈阳房产网</a> &gt;
        <a href="/officeProjects/">写字楼项目</a></div>
    <!--筛选条件-->
    <div id="option">
        <ul>
            <!--区域-->
            <li class=""><p>区域：</p>
                <a th:each="r,ri:${region}" th:href="${r.url}" th:id="'r'+${r.id}" th:if="${r.id ne '10'and r.id ne '11'}" th:class="${r.selected}? 'hover':''">
                    <th:block  th:text="${ri.index == 0?'不限':r.name}"></th:block>
                    <i th:if="${!#strings.isEmpty(r.id)}"></i>
                </a><br>
            </li>
            <!--售价-->
            <li th:if="${typeId ne '4'}"><p>售价：</p>
                <a th:each="sp,stat:${sprice}" th:href="${sp.url}" th:text="${stat.index ==0?'不限': sp.name}" th:id="'sp'+${sp.id} " th:class="${sp.selected}?'hover':''">></a>
                <div id="Search_sPriceDomOk">
                    <label>
                        <input name="sminPrice" id="sminPrice" maxlength="5" type="text" th:value="${sminPrice}" > - <input name="smaxPrice" id="smaxPrice" maxlength="5" type="text" th:value="${smaxPrice}" > 元/㎡
                        <input onclick="__doPostBack('Search$Btn_Search1','')" name="Search$Btn_Search1" id="Search_Btn_Search1" value="确定" class="btn_search" style="display: none;" type="button">
                    </label>
                </div>
            </li>
            <li class="uPriceDomOk" ><p>租金：</p><!--单价-->
                <a th:each="up,stat:${uprice}" th:href="${up.url}" th:text="${stat.index==0?'不限':up.name}" th:id="'up'+${up.id}" th:class="${up.selected}?'hover':''">></a>
                <div id="Search_uPriceDomOk">
                    <label>
                        <input name="uminPrice" id="uminPrice" maxlength="4" type="text" th:value="${uminPrice}" > - <input name="umaxPrice" id="umaxPrice" maxlength="4" type="text" th:value="${umaxPrice}" > 元/㎡·天
                        <input onclick="__doPostBack('Search$Btn_Search3','')" name="Search$Btn_Search3" id="Search_Btn_Search3" value="确定" class="btn_search" style="display: none;" type="button">
                    </label>
                </div>
            </li>
            <li ><p>面积：</p>
                <a th:each="a,stat:${area}" th:href="${a.url}" th:text="${stat.index==0?'不限':a.name}" th:id="'a'+${a.id}" th:class="${a.selected}?'hover':''">></a>
                <div id="Search_BuildAreaDomOk">
                    <label>
                        <input name="ominArea" id="ominArea" maxlength="5" type="text" th:value="${ominArea}"> - <input name="omaxArea" id="omaxArea" maxlength="5" type="text" th:value="${omaxArea}"> ㎡
                        <input onclick="__doPostBack('Search$Btn_Searchs','')" name="Search$Btn_Searchs" id="Search_Btn_Searchs" value="确定" class="btn_search" style="display: none;" type="button">
                    </label>
                </div>
            </li>
            <li><p>类型：</p>
                <a th:each="c,stat:${category}" th:href="${c.url}" th:text="${stat.index == 0 ?'不限': c.name}" th:id="${'c'+c.id}" th:class="${c.selected}? 'hover':''"></a>
            </li>
        </ul>
    </div>
    <!--已选条件-->
    <div id="option_info"  style="display: none">
        <b >已选：</b>
        <!--头部搜索-->
        <div th:if="${!#strings.isEmpty(searchKey) and !#strings.isEmpty(searchKey)}">
            <span class="condition"><th:block th:text="${searchKey}"></th:block></span>
            <i class="cleanUrl" id="clearSearchKey" th:value="${searchKey}"></i>
        </div>
        <!--区域-->
        <div th:each="region:${region}" th:if="${region.selected and region.name ne '全部'}">
            <span class="condition"><th:block th:text="${region.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'r'+region.id}"></i>
        </div>
        <!--售价-->
        <div th:each="sprice:${sprice}" th:if="${sprice.selected and sprice.name ne '全部'}">
            <span class="condition"><th:block th:text="${sprice.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'p'+sprice.id}"></i>
        </div>
        <!--自定义售价-->
        <div th:if="${!#strings.isEmpty(sminPrice) or !#strings.isEmpty(smaxPrice)}">
            <span class="condition">
                <th:block th:if="${#strings.isEmpty(sminPrice)}" th:text="${'小于等于'+smaxPrice+'元/㎡'}"></th:block>
                <th:block th:if="${#strings.isEmpty(smaxPrice)}" th:text="${'大于等于'+sminPrice+'元/㎡'}"></th:block>
                <th:block th:if="${!#strings.isEmpty(smaxPrice) and !#strings.isEmpty(sminPrice)}" th:text="${sminPrice+'-'+smaxPrice+'元/㎡'}"></th:block>
            </span>
            <i class="cleanUrl" th:if="${#strings.isEmpty(sminPrice)}" th:value="${'sm'+smaxPrice}"></i>
            <i class="cleanUrl" th:if="${#strings.isEmpty(smaxPrice)}" th:value="${'sn'+sminPrice}"></i>
            <i class="cleanUrl" th:if="${!#strings.isEmpty(smaxPrice) and !#strings.isEmpty(sminPrice)}" th:value="${'sn'+sminPrice+'-'+'sm'+smaxPrice}"></i>
        </div>
        <!--租金 单价-->
        <div th:each="uprice:${uprice}" th:if="${uprice.selected and uprice.name ne '全部'}">
            <span class="condition"><th:block th:text="${uprice.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'up'+uprice.id}"></i>
        </div>
        <!--自定义租金 单价-->
        <div th:if="${!#strings.isEmpty(uminPrice) or !#strings.isEmpty(umaxPrice)}">
            <span class="condition">
                <th:block th:if="${#strings.isEmpty(uminPrice)}" th:text="${'小于等于'+umaxPrice+'元/㎡·天'}"></th:block>
                <th:block th:if="${#strings.isEmpty(umaxPrice)}" th:text="${'大于等于'+uminPrice+'元/㎡·天'}"></th:block>
                <th:block th:if="${!#strings.isEmpty(umaxPrice) and !#strings.isEmpty(uminPrice)}" th:text="${uminPrice+'-'+umaxPrice+'元/㎡·天'}"></th:block>
            </span>
            <i class="cleanUrl" th:if="${#strings.isEmpty(uminPrice)}" th:value="${'um'+umaxPrice}"></i>
            <i class="cleanUrl" th:if="${#strings.isEmpty(umaxPrice)}" th:value="${'un'+uminPrice}"></i>
            <i class="cleanUrl" th:if="${!#strings.isEmpty(umaxPrice) and !#strings.isEmpty(uminPrice)}" th:value="${'un'+uminPrice+'-'+'um'+umaxPrice}"></i>
        </div>
        <!--面积-->
        <div th:each="area:${area}" th:if="${area.selected and area.name ne '全部'}">
            <span class="condition"><th:block th:text="${area.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'a'+area.id}"></i>
        </div>
        <!--自定义面积-->
        <div th:if="${!#strings.isEmpty(ominArea) or !#strings.isEmpty(omaxArea)}">
            <span class="condition">
                <th:block th:if="${#strings.isEmpty(ominArea)}" th:text="${'小于等于'+omaxArea+'㎡'}"></th:block>
                <th:block th:if="${#strings.isEmpty(omaxArea)}" th:text="${'大于等于'+ominArea+'㎡'}"></th:block>
                <th:block th:if="${!#strings.isEmpty(omaxArea) and !#strings.isEmpty(ominArea)}" th:text="${ominArea+'-'+omaxArea+'㎡'}"></th:block>
            </span>
            <i class="cleanUrl" th:if="${#strings.isEmpty(ominArea)}" th:value="${'ha'+omaxArea}"></i>
            <i class="cleanUrl" th:if="${#strings.isEmpty(omaxArea)}" th:value="${'la'+ominArea}"></i>
            <i class="cleanUrl" th:if="${!#strings.isEmpty(omaxArea) and !#strings.isEmpty(ominArea)}" th:value="${'la'+ominArea+'-'+'ha'+omaxArea}"></i>
        </div>
        <!--类型-->
        <div th:each="category:${category}" th:if="${category.selected and category.name ne '全部'}">
            <span class="condition"><th:block th:text="${category.name}"></th:block></span>
            <i class="cleanUrl" th:value="${'t'+category.id}"></i>
        </div>
        <a href="/officeProjects/" class="clean">清空筛选条件</a>
        <script>
            /*判断是否显示条件栏目*/
            $(function () {
                if($(".condition").text() != ""){
                    $("#option_info").css("display","block");
                }
            })
            /*去掉条件标签*/
            $(".cleanUrl").click(function () {
                var oldUrl = location.pathname;
                if (oldUrl.substring(oldUrl.indexOf($(this).attr("value"))-1,oldUrl.indexOf($(this).attr("value"))) == "-"){
                    var moreUrl ="-" + $(this).attr("value");
                }else{ var moreUrl = $(this).attr("value");}

                var newUrl = oldUrl.replace(moreUrl,"");
                window.location.href = newUrl;
                //跳到newUrl指定链接
            });
            //清除头部搜索key
            $("#clearSearchKey").click(function () {
                var nowUrl = location.pathname;
                var searchTitle = nowUrl.indexOf('search');//只有手输内容搜索
                var newUrl = nowUrl.substring(0,searchTitle);
                window.location.href = newUrl;
            });
        </script>
    </div>
    <script type="text/javascript">
        //筛选的确定按钮显示隐藏
        function price(priceIdName) {
            if($("#"+priceIdName).length >0){
                $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
                $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
                var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
                var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
                if (num1 == "" && num2 != "") {
                    $("#" + priceIdName + " input").eq(0).val("0");
                    $("#" + priceIdName + " input").eq(2).show();
                } else if (num2 == "" && num1 != "") {
                    $("#" + priceIdName + " input").eq(2).show();
                } else if (num1 != "" || num2 != "") {
                    $("#" + priceIdName + " input").eq(2).show();
                } else {
                    $("#" + priceIdName + " input").eq(2).hide();
                }
            }
        }

        price("Search_BuildAreaDomOk");
        price("Search_sPriceDomOk");
        $("#Search_sPriceDomOk input").keyup(function () {
            price("Search_sPriceDomOk");
        })
        $("#Search_rPriceDomOk input").keyup(function () {
            price("Search_rPriceDomOk");
        })
        $("#Search_uPriceDomOk input").keyup(function () {
            price("Search_uPriceDomOk");
        })
        $("#Search_BuildAreaDomOk input").keyup(function () {
            price("Search_BuildAreaDomOk");
        })
        $("#Search_sPriceDomOk").keydown(function (event) {
            if (event.keyCode == 13) {
                $("#Search_Btn_Search1").click()
            }
        });
        $("#Search_rPriceDomOk").keydown(function (event) {
            if (event.keyCode == 13) {
                $("#Search_Btn_Search2").click()
            }
        });
        $("#Search_uPriceDomOk").keydown(function (event) {
            if (event.keyCode == 13) {
                $("#Search_Btn_Search3").click()
            }
        });
        $("#Search_BuildAreaDomOk").keydown(function (event) {
            if (event.keyCode == 13) {
                $("#Search_Btn_Searchs").click()
            }
        });
        //此段js用于手填面积和价格连接拼接处理
        function __doPostBack(pager1, page) {
            var url = window.location.pathname;
            if (pager1 == "Search$Btn_Search1") {
                var priceBegin = $("#sminPrice").val();
                var priceEnd = $("#smaxPrice").val();
                var ref1 = url.replace(/-sn[0-9]\d*/,''); //k最小值
                var ref2 = ref1.replace(/-sm[0-9]\d*/,'');  //x最大值
                if (parseInt(priceEnd) < parseInt(priceBegin)){
                    priceEnd = [priceBegin,priceBegin=priceEnd][0];
                }
                if(priceBegin != "" && priceBegin != 0) {
                    ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-rn[0-9]\d*/, '').replace(/\/rn[0-9]\d*/, '\/').replace(/-rm[0-9]\d*/, '').replace(/\/rm[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-un[0-9]\d*/, '').replace(/\/un[0-9]\d*/, '\/').replace(/-um[0-9]\d*/, '').replace(/\/um[0-9]\d*/, '\/') + "-sn" + priceBegin;
                }
                if(priceEnd != "") {
                    ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-rn[0-9]\d*/, '').replace(/\/rn[0-9]\d*/, '\/').replace(/-rm[0-9]\d*/, '').replace(/\/rm[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-un[0-9]\d*/, '').replace(/\/un[0-9]\d*/, '\/').replace(/-um[0-9]\d*/, '').replace(/\/um[0-9]\d*/, '\/') + "-sm" + priceEnd;
                }
                location.href = ref2;
            }
            if (pager1 == "Search$Btn_Search3") {
                var priceBegin = $("#uminPrice").val();
                var priceEnd = $("#umaxPrice").val();
                var ref1 = url.replace(/-un[0-9]\d*/,''); //k最小值
                var ref2 = ref1.replace(/-um[0-9]\d*/,'');  //x最大值
                if (parseInt(priceEnd) < parseInt(priceBegin)){
                    priceEnd = [priceBegin,priceBegin=priceEnd][0];
                }
                if(priceBegin != "" && priceBegin != 0){
                    ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-sn[0-9]\d*/,'').replace(/\/sn[0-9]\d*/,'\/').replace(/-sm[0-9]\d*/,'').replace(/\/sm[0-9]\d*/,'\/');
                    ref2 = ref2.replace(/-rn[0-9]\d*/,'').replace(/\/rn[0-9]\d*/,'\/').replace(/-rm[0-9]\d*/,'').replace(/\/rm[0-9]\d*/,'\/') + "-un" + priceBegin;
                }
                if(priceEnd != ""){
                    ref2 = ref2.replace(/-p[0-9]\d*/, '').replace(/\/p[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-up[0-9]\d*/, '').replace(/\/up[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-q[0-9]\d*/, '').replace(/\/q[0-9]\d*/, '\/');
                    ref2 = ref2.replace(/-sn[0-9]\d*/,'').replace(/\/sn[0-9]\d*/,'\/').replace(/-sm[0-9]\d*/,'').replace(/\/sm[0-9]\d*/,'\/');
                    ref2 = ref2.replace(/-rn[0-9]\d*/,'').replace(/\/rn[0-9]\d*/,'\/').replace(/-rm[0-9]\d*/,'').replace(/\/rm[0-9]\d*/,'\/') + "-um"+ priceEnd;
                }
                location.href = ref2;
            }
            if (pager1 == "Search$Btn_Searchs") {
                var areaBegin = $("#ominArea").val();
                var areaEnd = $("#omaxArea").val();
                var ref1 = url.replace(/-la[0-9]\d*/,''); //y最小值
                var ref2 = ref1.replace(/-ha[0-9]\d*/,'');  //e最大值
                if (parseInt(areaEnd) < parseInt(areaBegin)){
                    areaEnd = [areaBegin,areaBegin=areaEnd][0];
                }
                if(areaBegin != "" && areaBegin != 0)
                    ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-la" + areaBegin;
                if(areaEnd != "")
                    ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') +"-ha" + areaEnd;
                location.href = ref2;
            }
        }
    </script>
    <div class="cl"></div>
    <div id="main">
        <div id="left" class="longLeft">
            <!--特色-->
            <div class="tese">
                <p>特色:</p>
                <div>
                    <a th:id ="${x.id}" th:each="x,i:${houseTrait}" th:if="${i.index != 0}" th:href="${x.url}" th:text="${x.name}" th:class="${x.selected}? 'hover':''">></a>
                    <!--点击房源特色js-->
                    <script >
                        $(".tese a").click(function () {
//                                var teseid = $(this).attr("id").replace("tese", "");
                            var ref = location.pathname;
                            if(ref.indexOf('officeProjects/') == -1) ref +='/';
                            ref = ref.replace(/[-]*[x][0-9,]*[0-9]?$/, "");
                            ref = ref.replace(/[-]*[n][0-9]?$/, "");
                            if($(this).attr("id")){
                                if ($(this).attr("class") == null) {
                                    $(this).attr("class", "hover");
                                } else {
                                    $(this).attr("class", "");
                                }
                                var uIds="";
                                $(".tese a").each(function () {
                                    if ($(this).attr("class") == "hover") {
                                        if($(this).attr("id")!=null && $(this).attr("id")!=''&& $(this).attr("id")!=undefined){
                                            uIds += $(this).attr("id")+",";
                                        }
                                    }
                                });
                                uIds = uIds.replace(/[,]$/,"");
                                if(uIds != null && uIds != undefined && uIds != ''){
                                    $(this).attr("href",ref+"-x"+uIds);
                                }else {
                                    $(this).attr("href",ref);
                                }
                            }else {
                                $(this).attr("href",ref);
                            }
                        })
                    </script>
                </div>
            </div>
            <div class="cl"></div>
            <!--<div class="content">小二为你找到<i th:text="${#strings.isEmpty(msg)?'': msg}"></i>个符合条件的写字楼</div>-->
            <div class="contentMain" id="saleCol">
                <!--无房源显示-->
                <div class="warning" th:if="${#lists.isEmpty(offices)}">
                    <p>
                        很抱歉，沈阳暂时没有符合您要求的写字楼，您可以更改条件重新搜索。
                        <br> 请重试<!--<a th:href="@{/helpSearch}" target="_blank">点击免费体验购房服务方案>></a>-->
                    </p>
                </div>
                <!--房源显示-->
                <div id=replie class="house_left" th:if="${!#lists.isEmpty(offices)}">
                    <div class="inf" th:if="${!#lists.isEmpty(offices)}" th:each="offices:${offices}" th:onclick="${'window.open(''/officeProject/'+offices.buildingID+'.htm'')'}">
                        <!--<input th:if="${!#strings.isEmpty(shop.stickOrder) and shop.stickOrder eq '-1'}" type='image' style="position: absolute;top: 0; right: 20px;" src='https://static.fangxiaoer.com/web/images/ico/sign/list_v.png'/>-->
                        <a href="javascript:void(0)" class="infLeft">
                            <!--<i th:if="${'0,1,2,3,4'.indexOf(shop.picNum) eq -1}" th:text="${shop.picNum}"></i>-->
                            <img th:src="${#strings.isEmpty(offices.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': offices.pic}" th:alt="${#strings.isEmpty(offices.name)? '':offices.name}">
                            <!--<div class="" th:if="${shop.isXiQue eq '1'}">佣金95折</div>-->
                        </a>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <div class="infCtn">
                            <a class="infCtnTitle" href="javascript:void(0)" th:text="${#strings.isEmpty(offices.name)? '':offices.name}"></a>
                            <p class="gcol6">
                                <i th:text="${#strings.isEmpty(offices.minArea)?'':(#strings.toString(offices.minArea).contains('.')? #strings.toString(offices.minArea).replaceAll('0+?$','').replaceAll('[.]$', ''):offices.minArea)}"></i>
                                <i th:if="${!#strings.isEmpty(offices.minArea) and !#strings.isEmpty(offices.maxArea)}">-</i>
                                <i th:text="${#strings.isEmpty(offices.maxArea)?'':(#strings.toString(offices.maxArea).contains('.')? #strings.toString(offices.maxArea).replaceAll('0+?$','').replaceAll('[.]$', ''):offices.maxArea)}"></i>
                                <i th:if="${!#strings.isEmpty(offices.minArea) or !#strings.isEmpty(offices.maxArea)}">㎡</i>
                            </p>
                            <p  class="gcol6">
                                <i th:text="${#strings.isEmpty(offices.allCount) or #strings.toString(offices.allCount) eq '0'? '暂无写字楼可租售':'共有'+(#strings.toString(offices.allCount).contains('.')? #strings.toString(offices.allCount).replaceAll('0+?$','').replaceAll('[.]$', ''):offices.allCount)+'套可租售'}"></i>
                            </p>
                            <p  class="gcol9">
                                <i class="xzlKuo" th:text="${#strings.isEmpty(offices.regionName)?'':offices.regionName+'-'}"></i>
                                <i class="xzlNkuo" th:text="${#strings.isEmpty(offices.address)?'':offices.address}"></i>
                            </p>
                            <div class="houseItemIcon">
                                <span th:each="houseTrait,i:${#strings.toString(offices.HouseTrait).split(',')}" th:class="${'tese_'+(i.index+1)}" th:if="${!#strings.isEmpty(offices.HouseTrait) and i.index &lt; 3}"  th:text="${houseTrait}"></span>

                            </div>
                        </div>
                        <div class="xzjListRight">
                            <!--出售价格-->
                            <th:block th:if="${!#strings.isEmpty(offices.unitPriceSale)}">
                                <p class="sy_price">
                                    <s class="xzjSale">售</s>
                                    <span class="gaifont">
                                                                           <th:block th:text="${ #strings.toString(offices.salePrice) eq '0.0'?'面议': (#strings.indexOf(offices.unitPriceSale,'.') eq -1 ? offices.salePrice:#strings.toString(offices.salePrice).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>

                                    </span>
                                    <i th:text="${#strings.toString(offices.salePrice) eq '0.0'?'':offices.saleUnit}"></i>
                                </p>
                            </th:block>
                            <!--出租价格-->
                            <th:block th:if="${!#strings.isEmpty(offices.unitPriceRent)}">
                                <p class="sy_price">
                                    <s class="xzjRent">租</s>
                                    <span class="gaifont">
                                                                            <th:block th:utext="${#strings.toString(offices.unitPriceRent) eq '0.0'?'面议':(#strings.indexOf(offices.unitPriceRent,'.') eq -1 ? shop.unitPrice :#strings.toString(offices.unitPriceRent).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>

                                    </span>
                                    <i th:text="${#strings.toString(offices.unitPriceRent) eq '0.0'?'':'元/㎡·天'}"></i>
                                </p>
                            </th:block>
                        </div>
                    </div>
                </div>
                <div class="cl"></div>
            </div>
            <div class="cl"></div>
            <div class="page">
                <div th:include="fragment/page :: page"></div>
            </div>
        </div>
        <div id="right" class="shortRight">
            <!--<div class="saleHouse">-->
                <!--<a href="/officeSell" target="_blank">我要卖房</a>-->
            <!--</div>-->

            <!--房产快搜-->
            <!--<div class="gksou">-->
                <!--<div class="gktp"><span></span>房产快搜 <a href="/fastSeek" target="_blank">详情>></a></div>-->
                <!--<div class="gkmn">-->
                    <!--<a href="https://sy.fangxiaoer.com/news/94587.htm" target="_blank"><i>1</i><em>房产过户</em></a>-->
                    <!--<a href="https://sy.fangxiaoer.com/news/94586.htm" target="_blank"><i>2</i><em>购房流程</em></a>-->
                    <!--<a href="https://sy.fangxiaoer.com/news/94572.htm" target="_blank"><i>3</i><em>征信查询</em></a>-->
                    <!--&lt;!&ndash;<a href="/fastSeek#edu" target="_blank"><i>4</i><em>沈阳推荐小学</em></a>-->
                    <!--<a href="/fastSeek#edu" target="_blank"><i>5</i><em>沈阳推荐中学</em></a>&ndash;&gt;-->
                <!--</div>-->
            <!--</div>-->

            <div class="rightQzQgBtn">
                <a href="/officeRent/" target="_blank"><i class="rightQzQgBtn-icon3"></i>我要卖房</a>
                <a th:href="@{'/helpSearch?ids=9'}" rel="8" dir="3" target="_blank"><i class="rightQzQgBtn-icon4"></i>买商业</a>
                <!--<a href="/shoprent/" target="_blank"><i class="rightQzQgBtn-icon3"></i>我要出租</a>
                <a href="/shopsell/" target="_blank"><i class="rightQzQgBtn-icon4"></i>我要出售</a>-->
            </div>

            <!--列表页右侧求租求购-->
            <!--<div th:include="scriptorium/fragment::officeWant"></div>
            <div>
                <div th:if="${!#lists.isEmpty(advert.officeAd1)}" style="padding-bottom: 20px">
                    <th:block th:each="officeAd1,i:${advert.officeAd1}">
                        <a  th:if="${#strings.toString(officeAd1.url).indexOf('http') ne -1}" th:href="${officeAd1.url}" target="_blank">
                            <img width="100%" th:src="${officeAd1.image}" th:alt="${officeAd1.projectName}">
                        </a>
                        <img th:if="${#strings.toString(officeAd1.url).indexOf('http') eq -1}" width="100%" th:src="${officeAd1.image}" th:alt="${officeAd1.projectName}">
                    </th:block>                </div>
                <div th:if="${!#lists.isEmpty(advert.officeAd2)}">
                    <a th:each="officeAd2,i:${advert.officeAd2}" th:href="${officeAd2.url}" target="_blank">
                        <img width="100%" th:src="${officeAd2.image}" th:alt="${officeAd2.projectName}">
                    </a>
                </div>
            </div>-->
        </div>
        <div class="cl"></div>
    </div>
</div>
<!--底部1-->
<div th:include="fragment/fragment:: footer_list"></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
</body>
</html>
