
<!DOCTYPE html>
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title th:text="${office.name+'_'+office.regionName+'写字间出售_沈阳写字楼出售 - 房小二网'}"></title>
    <meta name="keywords" th:content="${office.name+',沈阳写字楼出售,'+office.regionName+'写字楼转让,写字间出兑'}"/>
    <meta name="description" th:content="${'房小二网沈阳写字楼频道为你提供'+office.name+'，以及'+office.regionName+'写字楼与沈阳其他写字间，门市的出兑，出租，出售与转让信息，购买和发布沈阳写字楼出租出售信息首选房小二网。'}"/>
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/fang4/'+houseId+'.htm'}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/new_sy/sale/view2017.css?t=20180925" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/global/js/tab.js"></script>
    <script src="https://static.fangxiaoer.com/global/js/photoAlbum.js" type="text/javascript" charset="utf-8"></script>
    <script src="/js/md5.js"></script>
    <script src="/js/indent.js"></script>
    <script src="/js/house/jquery.qrcode.min.js" type="text/javascript"></script>
    <link href="https://static.fangxiaoer.com/web/styles/sy/sale/saleHouseIndent.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/global/css/sale/imgShow.css"/>
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/sy/house/suspensionIcon.css"/>
    <link href="https://static.fangxiaoer.com/web/styles/new_sy/house/newBaiduMap_SelectZhoubian.css?v=20190401" rel="stylesheet" type="text/css">
    <script src="https://static.fangxiaoer.com/js/forbiddenCopy.js"></script>
    <style>
        .main .mainContent .price span{
            color: #999;
        }
        .main .mainContent .price b{
            color: #ff5200;
        }
        .main .mainContent .price{
            line-height: 86px;
            padding-bottom: 0;
        }
        .main .mainContent .card p{
            display: block;
            float: left;
            margin: 0;
            text-align: left;
            width: 88px;
            line-height: 30px;
        }
        .main .mainContent .card span{
            color: #ff5200;
            font-weight: bolder;
            font-size: 26px;
            float: left;
            display: block;
            line-height: 30px;
        }
        .main .mainContent .card a{
            color: #fff;
            background: #ff5200;
            width: 80px;
            display: block;
            line-height: 30px;
            float: left;
            margin-left: 20px;
        }
        .right .recommend ul li div span{
            font-size: 16px;
        }
        .Iconshutiao{
            width: 4px;
            height: 22px;
            background: #ff5200;
            display: inline-block;
            float: left;
            margin-right: 4px;
            margin-top: 2px;
        }
        /*.dt1{height: auto !important;}*/
        .mapsele{top: 0;width: 350px;}
        .mapTab li {
            padding: 13px 11px;
        }
        .map_lpcon{height: 260px;margin-top: 0}
        .map_lp{margin: 0;    padding: 0;display: block}
        /*.map_dl dd a{padding-left: 50px;width: 277px;    background: url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian1.png) no-repeat 7px 18px;}*/
        /*.mapTab li:hover{background:#f5f5f5;color:#ff5200;}*/
        /*.mapTab li.hover{background:#f5f5f5;color:#ff5200;}*/
        /*.mapTab li:hover a{ text-decoration:none; color:#ff5200;}*/
        /*.mapTab li.hover a{ text-decoration:none; color:#ff5200;}*/
        /*.map_dl dd a:hover{ background:url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian2.png) no-repeat 7px 18px;}*/
        .mapTab li span.s8 {
            background-position: -543px -4px;
        }
        .mapTab li a:hover .s8{background-position:-543px -64px;}
        .full{position:fixed;width:100%;top:0;left:0;background:#fff;z-index:100000;height: 100%;}
        .full .nmaptitleleft{margin-top:0}
        .full .symap{height:100%}
        .full .dt1{height:100%;}
        .full #memap{height: 100% !important;}
        .full .mapsele{height:660px !important}
        .full{z-index: 100003 !important;}
        .BMapLib_SearchInfoWindow img {
            border: 0;
            margin-top: 2px!important;
            top: auto !important;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_content  p{white-space: normal !important;padding: 16px 0px;}
        .BMapLib_SearchInfoWindow {
            font: 12px arial,宋体,sans-serif;
            position: absolute;
            border: 0!important;
            background-color: #fff;
            cursor: default;
            box-shadow: 0px 0px 10px #999;
            border-radius: 10px;
            padding: 0 10px;
        }
        .BMapLib_SearchInfoWindow .BMapLib_bubble_title{background-color: #fff !important;}
        .bigImgShow{background: rgba(0, 0, 0, 0.5);}
    </style>
</head>

<body>
<!--引入头部导航栏-->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=9"></div>
<!--搜索栏-->
<div id="search2017" th:include="fragment/fragment::searchNav" th:with="type=13"></div>
<!--面包屑-->
<div class="w crumbs">您的位置：
    <a href="/" target="_blank">沈阳房产网</a> &gt;
    <a href="/officeProjects/" target="_blank">写字楼项目</a> &gt;
    <a th:href="${'/officeProjects/-r'+office.regionID}" target="_blank"><th:block th:text="${office.regionName}"></th:block></a> &gt;
    <a href=""><th:block th:text="${office.name}"></th:block></a>
</div>
<!--基本信息-->
<div class="w main">
    <!--<div class="header_sale">
        <h1 class="title1" th:text="${sehouse.title}"></h1>
        <ul>
            <li th:if="${!#strings.isEmpty(updateTime)}"  th:text="${'更新时间：'+updateTime}"></li>
            <li th:if="${#strings.isEmpty(updateTime)}"  th:text="${'发布时间：'+addTime}"></li>
            <li th:text="${sehouse.visitedNum ne '0'}?${sehouse.visitedNum+'人已浏览'}:''"></li>
        </ul>
        &lt;!&ndash;分享标签&ndash;&gt;
        <div th:include="fragment/fragment:: shareIcon"></div>
    </div>-->
    <!--左侧显示图片-->
    <div class="photoAlbum">
        <div class="imgMax">
            <ul>
                <li  th:if="${!#lists.isEmpty(office.pic)}" th:each="pics:${office.pic}">
                    <img th:src="${#strings.isEmpty(pics.pic)?'':#strings.toString(pics.pic).replaceAll('middle','big')}" onload="imgSize()" />
                </li>
                <li th:if="${#lists.isEmpty(office.pic)}">
                    <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" onload="imgSize()"  />
                </li>
            </ul>
        </div>
        <div class="imgMin">
            <span class="hover"><s></s></span>
            <div>
                <ul>
                    <li  th:if="${!#lists.isEmpty(office.pic)}" th:each="pics:${office.pic}">
                        <img th:src="${#strings.isEmpty(pics.pic)?'':#strings.toString(pics.pic)}" onload="imgSize()" />
                    </li>
                    <li th:if="${#lists.isEmpty(office.pic)}">
                        <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" onload="imgSize()"  />
                    </li>
                </ul>
            </div>
            <b><s></s></b>
        </div>
    </div>
    <!--右侧基础信息-->
    <div class="mainContent">
        <h2 th:text="${office.name}"></h2>
        <div class="price"th:if="${!#strings.isEmpty(office.unitPriceRent) or !#strings.isEmpty(office.unitPriceSale)}">
            <span th:if="${!#strings.isEmpty(office.unitPriceRent)}">
                <th:block>出租</th:block>
                <b th:text="${office.unitPriceRent eq '0'? '面议':(#strings.toString(office.unitPriceRent).contains('.') ?  #strings.toString(office.unitPriceRent).replaceAll('0+?$','').replaceAll('[.]$', '') : office.unitPriceRent)}"></b>
                <th:block th:if="${office.unitPriceRent ne '0'}">元/㎡·天</th:block>
            </span>
            <span th:if="${!#strings.isEmpty(office.unitPriceSale)}">
                <th:block>出售</th:block>
                <b th:text="${office.salePrice eq '0' ? '面议':(#strings.toString(office.salePrice).contains('.') ?  #strings.toString(office.salePrice).replaceAll('0+?$','').replaceAll('[.]$', '') : office.salePrice)}"></b>
                <th:block th:text="${office.salePrice eq '0'?'':office.saleUnit}"></th:block>
            </span>
        </div>
        <div class="basic">
            <ul>
                <li>
                    <span>租售房源</span>
                    <p th:if="${allCount eq 0}" th:text="${allCount eq 0?'暂无写字楼可租售':allCount+'套'}"></p></a>
                    <a th:href="${'/scriptoriums/-v'+office.officeId}"><p th:if="${allCount ne 0}" th:text="${allCount eq 0?'暂无写字楼可租售':allCount+'套'}" style="color: #ff5200;"></p></a>
                </li>
                <li>
                    <span>租售面积</span>
                    <p>
                        <th:block th:text="${#strings.isEmpty(office.minArea)?'':(#strings.toString(office.minArea).contains('.')? #strings.toString(office.minArea).replaceAll('0+?$','').replaceAll('[.]$', ''):office.minArea)}"></th:block>
                        <th:block th:if="${!#strings.isEmpty(office.minArea) and !#strings.isEmpty(office.maxArea)}">-</th:block>
                        <th:block th:text="${#strings.isEmpty(office.maxArea)?'':(#strings.toString(office.maxArea).contains('.')? #strings.toString(office.maxArea).replaceAll('0+?$','').replaceAll('[.]$', ''):office.maxArea)}"></th:block>
                        <th:block th:if="${!#strings.isEmpty(office.minArea) or !#strings.isEmpty(office.maxArea)}">㎡</th:block>
                        <th:block th:if="${#strings.isEmpty(office.minArea) and #strings.isEmpty(office.maxArea)}">暂无资料</th:block>
                    </p>
                </li>
                <li>
                    <span>建筑年代</span>
                    <p th:text="${#strings.isEmpty(office.buildYear)?'暂无资料':office.buildYear}"></p>
                </li>
                <li>
                    <span>物&nbsp;业&nbsp;费&nbsp;</span>
                    <p th:text="${#strings.isEmpty(office.propertyFee)?'暂无资料':(#strings.toString(office.propertyFee).contains('.') ? #strings.toString(office.propertyFee).replaceAll('0+?$','').replaceAll('[.]$', '') : office.propertyFee)+'元/㎡/月'}"></p>
                </li>
                <li>
                    <span>楼盘地址</span>
                    <p th:text="${#strings.isEmpty(office.address)?'暂无资料':office.address}"></p>
                </li>
                <li>
                    <span>附近地铁</span>
                    <p th:text="${#strings.isEmpty(office.subway)?'暂无资料':office.subway}"></p>
                </li>
            </ul>
        </div>
        <div class="card jjr">
           <p>联系电话</p>
            <span><th:block th:text="${office.tel}"></th:block></span>
            <a href="/helpSearch?ids=4">帮你选址</a>
        </div>
    </div>
</div>
<div class="w">
    <div class="left">
        <div class="details ">
            <div class="head">楼盘信息</div>
            <ul>
                <li th:if="${!#strings.isEmpty(office.HouseTrait)}">
                    <span>特色</span>
                    <div><th:block th:text="${#strings.toString(office.HouseTrait).replaceAll('[,]$','').replaceAll(',',' | ')}"></th:block></div>
                </li>
                <li>
                    <span>建筑信息</span>
                    <div>
                        <ul>
                            <li>
                                <span>总建筑面积：</span>
                                <p th:text="${#strings.isEmpty(office.buildArea)?'暂无资料':(#strings.toString(office.buildArea).contains('.')?#strings.toString(office.buildArea).replaceAll('0+?$','').replaceAll('[.]$', '')+' ㎡':office.buildArea+' ㎡')}"></p>
                            </li>
                            <li>
                                <span>层高：</span>
                                <p th:text="${#strings.isEmpty(office.floorHeight)?'暂无资料':(#strings.toString(office.floorHeight).contains('.')?#strings.toString(office.floorHeight).replaceAll('0+?$','').replaceAll('[.]$', '')+' ㎡':office.floorHeight+' ㎡')}"></p>
                            </li>
                            <li>
                                <span>标准层面积：</span>
                                <p th:text="${#strings.isEmpty(office.flatArea)?'暂无资料':(#strings.toString(office.flatArea).contains('.')?#strings.toString(office.flatArea).replaceAll('0+?$','').replaceAll('[.]$', '')+' ㎡':office.flatArea+' ㎡')}"></p>
                            </li>
                            <li>
                                <span>栋座数量：</span>
                                <p th:text="${#strings.isEmpty(office.buildingNum)?'暂无资料':office.buildingNum+'座'}"></p>
                            </li>
                            <li>
                                <span>货梯数量：</span>
                                <p th:text="${#strings.isEmpty(office.goodLadder) || office.goodLadder eq '0' ?'暂无资料':office.goodLadder+'部'}"></p>
                            </li>
                            <li>
                                <span>客梯数量：</span>
                                <p th:text="${#strings.isEmpty(office.elevatorNum) || office.elevatorNum eq '0' ?'暂无资料':office.elevatorNum+'部'}"></p>
                            </li>
                            <li>
                                <span>结构：</span>
                                <p th:text="${#strings.isEmpty(office.structure)?'暂无资料':#strings.toString(office.structure).replaceAll('[,]$', '')}"></p>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <span>物业配套</span>
                    <div>
                        <ul>
                            <li>
                                <span>物业公司：</span>
                                <p th:text="${#strings.isEmpty(office.propertyCompany)?'暂无资料':office.propertyCompany}"></p>
                            </li>
                            <li>
                                <span>绿化率：</span>
                                <p th:text="${#strings.isEmpty(office.greenery)?'暂无资料':office.greenery+'%'}"></p>
                            </li>
                            <li>
                                <span>采暖方式：</span>
                                <p th:text="${#strings.isEmpty(office.heatingMode)?'暂无资料':#strings.toString(office.heatingMode).replaceAll('[,]$', '')}"></p>
                            </li>
                            <!--<li th:if="${!#strings.isEmpty(office.airCondition)}">
                                <span>空调类型：</span>
                                <p th:text="${#strings.isEmpty(office.airCondition)?'暂无资料':office.airCondition}"></p>
                            </li>-->
                            <li>
                                <span>电压：</span>
                                <p th:text="${#strings.isEmpty(office.voltage)?'暂无资料':office.voltage+'v'}"></p>
                            </li>
                            <li>
                                <span>车位数量：</span>
                                <p th:text="${#strings.isEmpty(office.parkingSpace)?'暂无资料':office.parkingSpace}"></p>
                            </li>
                        </ul>
                    </div>
                </li>
                <li>
                    <span>入驻企业</span>
                    <div class="fyms" th:text="${#strings.isEmpty(office.settledEnterprise)?'暂无资料':office.settledEnterprise}">
                    </div>
                </li>
                <li>
                    <span>楼盘介绍</span>
                    <div class="fyms" th:utext="${#strings.isEmpty(office.introduction)?'暂无资料':office.introduction}"></div>
                </li>
            </ul>
        </div>
    </div>
    <div class="right" th:if="${!#lists.isEmpty(similar)}">
        <div id="xiangsihouse" class="head recommend">
            <h1>相关房源</h1>
            <ul >
                <li th:each="s,i:${similar}" th:if="${i.index lt 5}">
                    <a  th:href="${'/scriptorium/'+s.shopId + '.htm'}" target="_blank">
                        <div>
                            <img th:src="${#strings.isEmpty(s.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':s.pic}" th:alt="${s.title}" />
                            <b   th:text="${#strings.isEmpty(office.name)?'暂无资料':office.name}"></b>
                            <p>
                               <th:block th:text="${#strings.isEmpty(s.area)? '':#numbers.formatInteger(s.area,1)+'m²'}"></th:block>
                            </p>
                            <!--出租价格-->
                            <span th:if="${!#strings.isEmpty(s.shopType) and  s.shopType eq '4'}">
                                <th:block th:utext="${#strings.isEmpty(s.unitPrice) or #strings.toString(s.unitPrice) eq '0.0'?'面议':(#strings.indexOf(s.unitPrice,'.') eq -1 ? s.unitPrice :#strings.toString(s.unitPrice).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block>
                                <th:block th:text="${!#strings.isEmpty(s.unitPrice) and  #strings.toString(s.unitPrice) eq '0.0'?'':'元/㎡·天'}">元/㎡·天</th:block>
                            </span>
                            <!--出售价格-->
                            <span th:if="${!#strings.isEmpty(s.shopType) and s.shopType eq '5'}">
                                <th:block th:text="${#strings.isEmpty(s.unitPrice) or #strings.toString(s.unitPrice) eq '0.0'?'面议':(#strings.indexOf(s.unitPrice,'.') eq -1 ? s.unitPrice:#strings.toString(s.unitPrice).replaceAll('0+?$','').replaceAll('[.]$',''))+'元/㎡'}"></th:block>
                            </span>
                        </div>

                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div class="cl"></div>
    <!--周边配套-->
    <div class="">
<!--        <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=QcjndwPs1WR8gSTsr6TCz2NAfofGry6i"></script>-->
        <script type="text/javascript" src="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.js"></script>
        <link rel="stylesheet" href="https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/SearchInfoWindow_min.css" />
        <script src="/js/house/newBaiduMap_SelectZhoubian.js?t=20190117"></script>
        <script th:inline="javascript">
            $(document).ready(function () {

                var id = [[${houseId}]];
                var lng = [[${office.longitude}]];
                var lat = [[${office.latitude}]];
                var title = [[${office.name}]];
                var address = [[${office.address}]];
                var city = '沈阳';
                var content = "";
                $("#mapsubTitle").val(title);
                // bdMap.init("memap", { id: id, houselng: lng, houselat: lat, radius: 2000, suofa: 14, bdtitle: title, bdcontent: content, address: address, city: city });
            });
        </script>
       <!-- <div id="MapZhouBianPeiTao1_mapDom">
            <div class="nmaptitle">
                <div class="nmaptitleleft">
                    <p>周边配套</p>
                </div>
            </div>
            <div class="symap">
                <div class="dt1">
                    <div id="memap" style="margin: 0; border: 0; height: 335px; width: 100%; float: left;">
                    </div>
                    <div class="mapsele" id="mapsele" style="height: 335px; margin-right: 0px; position: absolute; z-index: 10000; right: 0">
                        <div class="msclose" id="msclose"></div>
                        <div class="mapTab" id="mapTab">
                            <ul>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('公交')"><span class="s2"></span>公交</a></li>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('地铁')"><span class="s1"></span>地铁</a></li>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('学校')"><span class="s9"></span>学校</a></li>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('超市')"><span class="s4"></span>超市</a></li>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('医院')"><span class="s7"></span>医院</a></li>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('餐饮')"><span class="s5"></span>餐饮</a></li>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('银行')"><span class="s6"></span>银行</a></li>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('娱乐')"><span class="s3"></span>娱乐</a></li>
                                <li><a href="javascript:void(0)" onclick="bdMap.searechMap('公园')"><span class="s8"></span>公园</a></li>

                            </ul>
                        </div>
                        <div class="map_lp">
                            <div id="hs_wrap">
                                <div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>
                                <div class="map_lpcon" style="height: 240px;" id="r-result">
                                    <div class="map_dl">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <script>
                $(function () {
                    $(".mapTab ul li a").click(function () {
                        $(".map_lp").show();
                        $(".mapTab").hide();
                    });
                    $(".map_dl a").mouseover(function () {
                        $(this).css("background-color", "#e8f4ff");
                    });
                    $(".map_dl a").mouseout(function () {
                        $(this).css("background-color", "#fff");
                    });
                    $(".map_tit a").click(function () {
                        $(".map_lp").hide();
                        $(".mapTab").show();
                    });
                    $("#msclose").click(function () {
                        var mr = $("#mapsele").css("margin-right");
                        if (mr == "0px") {
                            $("#mapsele").css("margin-right", "-350px");
                            $(this).css("background-position", "89% 1%");
                        } else {
                            $("#mapsele").css("margin-right", "0px");
                            $(this).css("background-position", "9% 1%");
                        }

                    });
                });
            </script>
        </div>-->
        <div id="MapZhouBianPeiTao1_mapDom">
            <div class="nmaptitle">
                <div class="nmaptitleleft">
                    <a href="https://sy.fangxiaoer.com/salemap/" target="_blank" class="map_house">沈阳楼盘地图</a>
                    <a href="javascript:void(0)" onclick="Full()" class="map_full">全屏</a>
                    <p>周边配套</p>
                </div>
            </div>
            <div class="symap">
                <div class="dt1">
                    <div id="memap" style="margin: 0; border: 0; height: 400px; width: 100%; float: left;">
                    </div>
                    <div class="mapsele" id="mapsele" >
                        <!--<div class="msclose" id="msclose"></div>-->
                        <div class="mapTab" id="mapTab">
                            <ul>
                                <li class="hover" style="    padding-right: 19px;padding-left: 25px;"><a href="javascript:void(0)" onclick="bdMap.searechHouse()">周边小区</a></li>
                                <li onclick="bdMap.searechMap('公交')"><a href="javascript:void(0)">公交</a></li>
                                <li onclick="bdMap.searechMap('地铁')"><a href="javascript:void(0)">地铁</a></li>
                                <li onclick="bdMap.searechMap('学校')"><a href="javascript:void(0)">学校</a></li>
                                <li onclick="bdMap.searechMap('超市')"><a href="javascript:void(0)">超市</a></li>
                                <li onclick="bdMap.searechMap('医院')"><a href="javascript:void(0)">医院</a></li>
                                <!--<li onclick="bdMap.searechMap('银行')"><a href="javascript:void(0)">银行</a></li>-->
                            </ul>
                        </div>
                        <div class="map_lp">
                            <div id="hs_wrap">
                                <!--<div class="map_tit"><a href="javascript:void(0)">返回</a><strong id="mapsubTitle"><i class="searechMapTitle"></i></strong></div>-->
                                <div class="map_lpcon"  id="r-result">
                                    <div class="map_dl">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
           <!--     <div class="symap">
                    <div style="border:0" class="canteen" id="peitao">
                        <div th:if="${!#strings.isEmpty(office.traffic)}"><span>交通：</span>
                            <p th:text="${office.traffic}"></p></div>&lt;!&ndash; traffic&ndash;&gt;
                        <div th:if="${!#strings.isEmpty(office.education)}"><span>学校：</span>
                            <p th:text="${office.education}"></p></div>&lt;!&ndash; education &ndash;&gt;
                        <div th:if="${!#strings.isEmpty(office.market)}"><span>商场：</span>
                            <p th:text="${office.market}"></p></div>&lt;!&ndash; market &ndash;&gt;
                        <div th:if="${!#strings.isEmpty(office.hospital)}"><span>医院：</span>
                            <p th:text="${office.hospital}"></p></div>&lt;!&ndash; hospital &ndash;&gt;
                        <div th:if="${!#strings.isEmpty(office.bank)}"><span>银行：</span>
                            <p th:text="${office.bank}"></p></div>&lt;!&ndash; bank &ndash;&gt;
                        <div th:if="${!#strings.isEmpty(office.othersettings)}"><span>其他：</span>
                            <p th:text="${office.othersettings}"></p></div>&lt;!&ndash; othersettings &ndash;&gt;

                    </div>
                    &lt;!&ndash;<i class="mapBtn mapBtnF"></i>&ndash;&gt;
                </div>-->
                <script>
                    $(function () {
                        $(".mapTab ul li ").click(function () {
                            $(".mapTab ul li ").removeClass("hover")
                            $(this).addClass("hover")
                            $(".map_lp").show();
                        });
                       /* $(".mapTab ul li a").click(function () {
                            $(".map_lp").show();
                            $(".mapTab").hide();
                        });*/
                        $(".map_dl a").mouseover(function () {
                            $(this).css("background-color", "#e8f4ff");
                        });
                        $(".map_dl a").mouseout(function () {
                            $(this).css("background-color", "#fff");
                        });
                    });
                    function Full() {
                        if ($("#MapZhouBianPeiTao1_mapDom").hasClass("full")) {
                            $("#MapZhouBianPeiTao1_mapDom").removeClass("full")
                            $(".map_lpcon").height("288px")
                            $(".map_full").html("全屏")
                        } else {
                            $("#MapZhouBianPeiTao1_mapDom").addClass("full")
                            $(".map_lpcon").height("100%")
                            $(".map_full").html("取消全屏")
                        }
                    }
                </script>
            </div>
        </div>
    </div>
    <div class="cl"></div>
    <!--附近写字楼-->
    <div class="gxqxq" th:if="${!#lists.isEmpty(nearOffices)}">
        <h3><span></span>附近写字楼</h3>
        <ul>
            <li th:each="near:${nearOffices}">
                <a th:href="${'/officeProject/'+near.officeId+'.htm'}" target="_blank">
                    <img th:src="${#strings.isEmpty(near.pic)?'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg':near.pic}" th:alt="${near.name}">
                </a>
                <p th:text="${near.name}"> </p>
                <div class="cl"></div>
                <p th:text="${#strings.toString(near.distance).contains('.')? '距离'+#strings.toString(near.distance).replaceAll('0+?$','').replaceAll('[.]$', '')+'米' : '距离'+near.distance+'米'}"></p>
                <p><i th:text="${near.unitPriceRent eq '0' or #strings.isEmpty(near.unitPriceRent)? '面议':(#strings.toString(near.unitPriceRent).contains('.') ?  #strings.toString(near.unitPriceRent).replaceAll('0+?$','').replaceAll('[.]$', '') : near.unitPriceRent)+'元/㎡·天'}"></i></p>
            </li>
        </ul>
    </div>
</div>
<div class="cl"></div>
<div class="disclaimer">
    <strong>免责声明：</strong>房源信息由网站用户提供，其真实性、合法性由信息提供者负责，最终以政府部门登记备案为准。本网站不声明或保证内容之正确性和可靠性，购买该房屋时，请谨慎核查。如该房源信息有误，您可以投诉此房源信息或<strong>拔打举报电话：400-893-9709</strong>。
</div>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloat"></div>
<noscript><img src="https://d5nxst8fruw4z.cloudfront.net/atrk.gif?account=YWBQn1QolK10fn" style="display:none" height="1" width="1" alt="" /></noscript>
<!-- End Alexa Certify Javascript -->
<!--点击图片放大-->
<div class="bigImgShow">
    <div class="showImg">
        <ul>
            <li th:if="${#lists.isEmpty( office.pic)}">
                <img src="https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg" alt="" onload="imgSize()"  />
            </li>
            <li th:if="${!#lists.isEmpty( office.pic)}" th:each="pic:${office.pic}">
                <img th:src="${#strings.toString(pic.pic).replace('middle','big')}" alt=""  onload="imgSize()"/>
            </li>
        </ul>
    </div>
    <div class="close"><img src="https://static.fangxiaoer.com/global/imgs/ico/cha.jpg"/></div>
    <div class="prev"></div>
    <div class="next"></div>
</div>
    <script type="text/javascript">
    var islogin;
    $(function () {
        islogin = $("#sessionId").val();
        pay.init();
        photo.init()
    });

    $(function () {
        var zj = $(".price b").html()
        var sf = zj * 0.3
        dk = zj - sf
        dk1 = parseInt(dk)
        sf = dk - dk1 + sf
        sf = Math.round(sf)
        $(".shoufu").html(sf)
        dk = zj - sf
        yll = 4.90 * 0.01 / 12
        n = Math.pow(1 + yll, 30 * 12)
        debx = dk * yll * n / (n - 1)
        debx = Math.round(debx * 10000)
        $(".debx").html(debx)
    })

    //图片自适应大小
    function imgSize5() {
        //滚动大图
        var imgWidthHeght2 = $(".imgMax1 ul li").eq(0).width() / $(".imgMax1 ul li").eq(0).height()
        $(".imgMin ul li img").each(function () {
            if (parseInt($(this).width()) <= parseInt($(this).height())) {
                $(this).css({"height": "100%", "width": "auto"})
                $(this).css({
                    "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                    "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                })
            } else {
                $(this).css({"width": "100%", "height": "auto"})
                $(this).css({
                    "margin-top": Math.floor($(this).height() / 2) * -1 + "px",
                    "margin-left": Math.floor($(this).width() / 2) * -1 + "px"
                })
            }
        })
    }

    $(function () {
        //更多相册
        var len = $(".photo li").length;
        if (len > 8) {
            $(".photo li:gt(7)").hide();
            $(".photo ul").after("<span>查看全部照片("+len+"张)</span>")
        }
        $(".photo span").live("click", function () {
            $(this).hide();
            $(".photo li").show();
        })

        //地图 向下滑动
        var maptop = $("#MapZhouBianPeiTao1_mapDom").offset().top
        $(".basic s").click(function () {
            $('body,html').animate({ scrollTop: maptop }, 500);
        })
    })
</script>
</div>
<script type="text/javascript">
    $("#MapPeiTao").click(function() {
        $("html, body").animate({
            scrollTop: $("#MapZhouBianPeiTao1_mapDom").offset().top }, {duration: 500,easing: "swing"});
        return false;
    });
</script>
<!--<div th:include="fragment/fragment::guideLoginPopup"></div>-->
<div th:include="house/detail/fragment_login::login"></div>
</body>
</html>

