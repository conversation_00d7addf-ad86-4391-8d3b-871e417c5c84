<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${#strings.isEmpty(intermediaryName)?'':intermediaryName+'的店铺,沈阳二手房经纪人房源信息 - 房小二网'}">欧春鑫的店铺，沈阳二手房经纪人房源信息 - 房小二网</title>
    <meta name="keywords" content="沈阳二手房,沈阳二手房网,沈阳二手房出售,沈阳二手房买卖,沈阳房产经纪人,沈阳二手房经纪人"/>
    <meta name="description" content="房小二网沈阳二手房为您提供海量真实的沈阳二手房房源信息，沈阳二手房经纪人信息，及时的二手房出租出售信息，帮您定位，搜索各类出售房源，带来更好的二手房买卖体验。"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/intermediary/'+agentIntermediaryId+'.htm'}">
    <!--<link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css?v=20200509" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <style>
        .isGoodHouse{
            background:url(https://static.fangxiaoer.com/web/images/sy/house/isGoodHouseIcon.png);width:60px;height:22px;float: left;margin-right: 5px;margin-top: 1px;}
        .agent-grjj{width: 220px}
        .warnings {
            margin: 20px 0;

            padding: 20px 0 20px 0;
            background: #fffbf6;
            border: 1px solid #f5dcbc;
        }
        .warnings p {
            background: url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left;
            font-size: 14px;
            line-height: 24px;
            font-weight: bold;
            color: #333;
            width: 420px;
            margin: auto;

        }
        .contentRight .box p.jjr_6 {
            background-position: 0px -76px;
        }
        .com-text{
            color: #0058FF !important;
        }
        .new-box{
            text-align: left;
            border-bottom: 1px #f2f2f2 dashed;


            line-height: 30px;
            margin-top: 6px;

            display: flex;
        }
        .in-img{
            width: 15px;
            height: 15px;

            margin-left: 10px;
            margin-top: 8px;
        }
        .in-right{
            margin-left: 12px;
        }
        .in-text{
            color: #999;
        }

        .contentRight h2{
            line-height: 26px !important;
        }

        .a-con{
            display: flex;
            margin-top: 16px;
        }
        .a-img{
            width: 46px;
            height: 46px;
            border-radius: 50%;
            margin-right: 9px;
        }
        .a-name{
            text-align: left;
            font-size: 13.9px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #303030;
            margin-bottom: 5px;
        }
        .a-area{
            font-size: 12px;
            font-family: PingFang SC-Regular, PingFang SC;
            color: #303030;
            text-align: left;
        }

        .no-house{
            width: 370px;
            height: 100%;
        }
        .no-text{
            font-family: PingFang SC-Medium, PingFang SC;

            color: #696969;
        }

        .a-click{
            display: flex;

            text-decoration: none !important;
        }

    </style>
    <script th:inline="javascript" >
        function qingchu() {
            setTimeout(function () {
                location.href = "/agentIntermediary/shops/"+[[${agentIntermediaryId}]];
            },500);
        }
        $(function() {
            $("#bottonss").click(function() {
                var dd = $("#hidden0").val();
                var dd1 = $("#Hidden1").val();
                var dd2 = $("#Hidden2").val();
                //模糊搜索字段
                var dds = $("#xiaoquid").val();
                // var sou = "68680";
                if (dd != null && dd !="") {
                    dd = "-p"+dd;
                }
                if (dd1 != null && dd1 != "") {
                    dd1 = "-a"+dd1;
                }
                if (dd2 != null && dd2 != "") {
                    dd2 = "-l"+dd2;
                }
                var search ;
                if (dds == null || dds == undefined || dds == '') {
                    search = ""
                }else {
                    search = "-search="+dds;
                }
                var url = "/agentIntermediary/second/"+[[${agentIntermediaryId}]]+'/' + dd + dd1 + dd2 +search;
//                setCookie("searchKey",dds,10);
                location.href = url;

            });
        });
        $(function() {
            var hr = window.location.href;
            var str=hr.split("_");
            var zj = str[1];
            getData(zj, "#hidden0", "#zongjia");
            var mj = str[2];
            getData(mj, "#Hidden1", "#mianji");
            var hx = str[3];
            getData(hx, "#Hidden2", "#huxing");
            var xq = str[4];
            if (xq != "" && xq != null) {
                $("#xiaoquid").val(decodeURIComponent(xq));
            }

            //var name0 = $("#hidden0").pasaleHouse(".my_xl").find(".my_xl_list li[value=" + mj + "]").html();
            //$("#zongjia").html(name0);
        })
        function getData(name, num, adds) {
            if (name != -1 && name != "" && name != null) {
                $(num).val(name);
                var bb = $(num).pasaleHouse(".my_xl").find(".my_xl_list li[value=" + name + "]").html();
                $(adds).html(bb);
                var container = $("body"),
                    scrollTo = $('.content');
                container.scrollTop(0);
                container.scrollTop(
                    scrollTo.offset().top - container.offset().top + container.scrollTop()
                );
            }
        }
    </script>
</head>
<body onload="init()">
<!--页头-->
<div id="head2017" th:include = "fragment/fragment::firstNav"  th:with="firstNavIndex=3"></div>
<input type="hidden" id="agentIntermediaryId" th:value="${agentIntermediaryId}" >
<div class="main">
    <div th:include = "fragment/fragment::intermediaryShopCard"></div>
    <div th:if="${!#strings.isEmpty(shopStatus)  and  shopStatus ne '1'}" class="warning warnings">
        <p>
            很抱歉，该经纪人店铺已经关闭。<br>
            <a th:href="@{/findAgent}"> 查找更多经纪人>></a>
        </p>
    </div>
    <div class="content" th:if="${#strings.isEmpty(shopStatus)  or  shopStatus eq '1'}">
        <div th:include = "fragment/fragment::agentIntermediaryHead" th:with="agentHouseTpye=3"></div>
        <div class="contentMain">
            <div class="contentCtn">
                <div class="my_xl" th:if="${!#lists.isEmpty(region)}">
                    <input name="hidden0" type="hidden" id="hidden0"  class="my_xl_input" th:each="region:${region}" th:if="${region.selected}" th:value="${region.id}"/>
                    <div class="my_xl_txt" id="zongjia"  th:each="region:${region}" th:if="${region.selected}"  th:text="${#strings.isEmpty(region.id) ? '区域' : region.name}">区域</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="region:${region}" th:href="${region.url}" ><li data-key="PriceBase" th:text="${region.name}" th:value="${region.id}">全部</li></a>
                    </ul>
                </div>
                <div class="my_xl" th:if="${!#lists.isEmpty(needs)}">
                    <input name="Hidden2" type="hidden" id="Hidden2" class="my_xl_input" th:each="n:${needs}" th:if="${n.selected}"  th:value="${n.id}" />
                    <div class="my_xl_txt" id="mianji" th:each="n:${needs}" th:if="${n.selected}" th:text="${#strings.isEmpty(n.id)? '供求':n.name}" >供求</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="n:${needs}" th:href="${n.url}"><li data-key="BuildArea"  th:text="${n.name}" th:value="${n.id}">全部</li></a>
                    </ul>
                </div>
                <div class="my_xl"  th:if="${!#lists.isEmpty(type)}">
                    <input name="Hidden1" type="hidden" id="Hidden1" class="my_xl_input"th:each="t:${type}" th:if="${t.selected}" th:value="${t.id}" />
                    <div class="my_xl_txt" id="huxing" th:each="t:${type}" th:if="${t.selected}" th:text="${#strings.isEmpty(t.id)?'类型':t.name}" >类型</div>
                    <div class="my_xl_btn"></div>
                    <ul class="my_xl_list">
                        <a th:each="t:${type}" th:href="${t.url}" > <li data-key="roomType"  th:text="${t.name}" th:value="${t.id}">全部</li></a>
                    </ul>
                </div>
                <div class="houseSearch">
                    <input type="text" id="xiaoquid" class="searchInput" th:value="${searchName}" autocomplete="off" placeholder="请输入关键字" >
                    <input name="button" id="bottonss" type="button" class="searchBtn" value="" >
                </div>
<!--                <p onclick="qingchu()"><i></i>清空筛选条件</p>-->
                <!--<span>小二为你找到<i th:text="${msg}">34</i>个符合条件的房源</span>-->
            </div>
            <div class="cl"></div>
<!--            <div th:if="${#lists.isEmpty(shop)}" class="warning">-->
<!--                <p>-->
<!--                    很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>-->
<!--                    <a th:href="@{/shops}" target="_blank"> 查找相似房源>></a>-->
<!--                </p>-->
<!--            </div>-->

            <div th:if="${#lists.isEmpty(shop)}" class="no-box">
                <img class="no-house" src="https://static.fangxiaoer.com/web/images/sy/house/no-house.png">
                <div class="no-text">暂无房源</div>
            </div>
            <div class="inf" th:if="${!#lists.isEmpty(shop)}" th:each = "shop : ${shop}">
                <a th:href="${#strings.isEmpty(shop.shopId)? '': '/shop/'+shop.shopId+'.htm'}" target="_blank" class="infLeft">
                    <!--                    <i class='imgNum' th:if="${shop.picNum ne null and #strings.toString(shop.picNum).length() gt 1 or shop.picNum gt '4'}" th:text="${#strings.isEmpty(shop.picNum)? '':shop.picNum}">6</i>-->
                    <img th:src="${#strings.isEmpty(shop.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg' : shop.pic}" th:alt="${#strings.isEmpty(shop.title) ? '': shop.title}">
                    <!--VR and 视频都存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) ne null }">
                        <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                        <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                    </s>
                    <!--VR存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) ne null and #strings.toString(shop.mediaID) eq null }">
                        <s class="vrListIcon" th:if="${#strings.toString(shop.PanID) ne null}"></s>
                    </s>
                    <!--视频存在 -->
                    <s class="listIconK" th:if="${#strings.toString(shop.PanID) eq null and #strings.toString(shop.mediaID) ne null }">
                        <s class="videoListIcon" th:if="${shop.mediaID ne null}"></s>
                    </s>
                </a>
                &nbsp;&nbsp;&nbsp;&nbsp;<div class="infCtn">
                <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(shop.shopId)? '': '/shop/'+shop.shopId+'.htm'}" th:text="${#strings.isEmpty(shop.title) ? '': shop.title}">（出售）金地悦峰.金悦街 首府新区陵东板块S8#3门</a>
                <div  class="fourSpan">
                    <span th:text="${#strings.isEmpty(shop.area)? '': #numbers.formatInteger(shop.area,1)+'m²' }"></span>
                    <span th:text = "${#strings.isEmpty(shop.shopCategoriesName) ? '':shop.shopCategoriesName}"></span>
                </div>
                <p  class="houseAddress"  th:if="${!#strings.isEmpty(shop.regionName)  and !#strings.isEmpty(shop.plateName)}">
                    <s>
                        <i th:if="${!#strings.isEmpty(shop.regionName)}" th:text="${shop.regionName}"></i>
                        <i th:if="${!#strings.isEmpty(shop.plateName)}" th:text="${'-'+shop.plateName}"></i>
                        <i th:if="${!#strings.isEmpty(shop.address)}" th:text="${'-'+shop.address}"></i>
                    </s>
                </p>
                <div  class="houseItemIcon">
                    <span th:if="${!#strings.isEmpty(shop.houseTrait) and oc.count lt 4}" th:each="establish,oc:${#strings.setSplit(shop.houseTrait,',') }" th:text="${#strings.isEmpty(establish)? '':establish}" th:class="${'tese_'+oc.count}"></span>
                </div>

                <p class="person" th:text = "${#strings.isEmpty(shop.spantime)? '' : shop.spantime+'更新'}">
                    22天前更新
                </p>
            </div>
                <div class="infRight" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}">
                    <p class="infRightPrise" th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '1'}"  >
                        <b style="font-weight: bold"><th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议': (#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))}"></th:block></b>
                        <i th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00'?'':'万'}"></i>
                    </p>

                    <p><i><b th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'':shop.unitPrice}">13007元/m²</b></i></p>
                </div>

                <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '2'}">
                    <p class="infRightPrise">
                        <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月')}"></th:block>-->
                        <b  style="font-weight: bold" th:if="${#strings.isEmpty(shop.price) or shop.price eq '0.00'}" th:text="${'面议'}"></b>
                        <s th:if="${!#strings.isEmpty(shop.price) && shop.price ne '0.00'}">
                            <!--                            <b  style="font-weight: bold"  th:text="${#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></b>-->
                            <b style="font-weight: bold;"><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}" ></th:block></b>
                            <i th:text="${shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月'}"></i>
                        </s>
                        <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">租</span>-->
                    </p>
                    <p><b th:text="${#strings.isEmpty(shop.price)or  shop.price eq '0.00' or #strings.toString(shop.unitPrice) eq '0元/m²·天' or #strings.isEmpty(shop.unitPrice)?'':shop.unitPrice}">1.77 元/m²·天</b></p>
                </div>
                <div class="infRight"  th:if="${!#strings.isEmpty(shop.shopType) and  shop.shopType eq '3'}">
                    <p class="infRightPrise">
                        <b style="font-weight: bold" th:if="${#strings.isEmpty(shop.price) or shop.price eq '0.00'}" th:text="${'面议'}"></b>
                        <s th:if="${!#strings.isEmpty(shop.price) && shop.price ne '0.00'}">
                            <!--                            <b  style="font-weight: bold"  th:text="${#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','')}"></b>-->
                            <b style="font-weight: bold;"><th:block th:utext="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$','').replaceAll('0{4}$','<i> 万</i>'))}" ></th:block></b>
                            <i th:text="${shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110' ? '元/年':'元/月'}"></i>
                        </s>
                        <!--<th:block th:text="${#strings.isEmpty(shop.price) or shop.price eq '0.00' ?'面议':(#strings.indexOf(shop.price,'.') eq -1 ? shop.price:#strings.toString(shop.price).replaceAll('0+?$','').replaceAll('[.]$',''))+(shop.payment eq '100' or shop.payment eq '50' or shop.payment eq '110'? '元/年':'元/月')}"></th:block>-->
                        <!--<span th:if="${!#strings.isEmpty(shop.price) and shop.price ne '0.00'}">兑</span>-->
                    </p>
                    <p><b th:text="${#strings.isEmpty(shop.price)or  shop.price eq '0.00' or #strings.toString(shop.unitPrice) eq '0元/m²·天' ?'':(#strings.indexOf(shop.unitPrice,'面议') eq -1 ?shop.unitPrice+'元':shop.unitPrice)}">1.77 元/m²·天</b></p>
                </div>
            </div>
            <!--分页-->
            <div class="page">
                <div id="Pager1">
                    <div th:include="fragment/page :: page "></div>
                </div>
            </div>
            <div ></div>
        </div>
        <div style="float: right; width: 242px;">
            <div class="contentRight">
                <h2>推荐经纪人</h2>

                <div class="a-box">

                    <div class="a-con" th:each="p:${companyAgentList}">
                        <a  class="a-click" th:href="${'/agent/second/'+p.memberId}">
                        <img class="a-img"  th:src="${#strings.isEmpty(p.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': p.pic}" />
                        <div class="a-right">
                            <div class="a-name" th:text="${p.realName}"></div>
                            <div class="a-area">主营小区： <span th:if="${!#strings.isEmpty(p.subName1)}" th:text="${p.subName1}"></span>
                                <span th:if="${!#strings.isEmpty(p.subName2)}" th:text="${p.subName2}"></span>
                                <span th:if="${!#strings.isEmpty(p.subName3)}" th:text="${p.subName3}"></span></div>
                        </div>
                        </a>
                    </div>

                </div>
                <!--所属机构-->
                <!--<div th:include = "agent/fragmentinter::affiliate"></div>-->



            </div>
        </div>
    </div>
    <!--个人介绍点击展开/收起-->
    <script>
        function init(){
            // var len = 51;
            // var ctn = document.getElementById("content");
            // var content = ctn.innerHTML;
            // //alert(content);
            // var span = document.createElement("span");
            // var a = document.createElement("a");
            // span.innerHTML = content.substring(0,len);
            // a.innerHTML = content.length>len?"... [详细]":"";
            // a.href = "javascript:void(0)";
            // a.onclick = function(){
            //     if(a.innerHTML.indexOf("[详细]")>0){
            //         a.innerHTML = "<<&nbsp;[收起]";
            //         span.innerHTML = content;
            //     }else{
            //         a.innerHTML = "... [详细]";
            //         span.innerHTML = content.substring(0,len);
            //     }
            // }
            // ctn.innerHTML = "";
            // ctn.appendChild(span);
            // ctn.appendChild(a);
        }
    </script>
</div>
<script th:inline="javascript">
    var result = [[${price}]];
    console.log('打印结果',result)
</script>
<script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
<div class="cl"></div>
<div th:include="fragment/fragment:: footer_detail" ></div>
<div th:include="fragment/fragment::tongji"></div>
<div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
</body>
</html>
