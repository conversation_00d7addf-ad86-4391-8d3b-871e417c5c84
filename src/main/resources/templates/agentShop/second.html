<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "https://www.w3.org/TR/html4/loose.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xmlns:th="https://www.thymeleaf.org">
<head>
    <title th:text="${#strings.isEmpty(intermediaryName)?'':intermediaryName+'的店铺,沈阳二手房经纪人房源信息 - 房小二网'}">欧春鑫的店铺，沈阳二手房经纪人房源信息 - 房小二网</title>
    <meta name="keywords" content="沈阳二手房,沈阳二手房网,沈阳二手房出售,沈阳二手房买卖,沈阳房产经纪人,沈阳二手房经纪人"/>
    <meta name="description" content="房小二网沈阳二手房为您提供海量真实的沈阳二手房房源信息，沈阳二手房经纪人信息，及时的二手房出租出售信息，帮您定位，搜索各类出售房源，带来更好的二手房买卖体验。"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta name="mobile-agent" th:content="${'format=html5;url=https://m.fangxiaoer.com/intermediary/'+agentIntermediaryId+'.htm'}">
    <!--<link href="https://static.fangxiaoer.com/web/styles/bootstrap.min.css" rel="Stylesheet" type="text/css" />
    <link href="https://static.fangxiaoer.com/web/styles/bootstrap-responsive.css" rel="stylesheet" type="text/css" />-->
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/sy/sale/shop.css?v=20200509" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <style>
        .isGoodHouse{
            background:url(https://static.fangxiaoer.com/web/images/sy/house/isGoodHouseIcon.png);width:60px;height:22px;float: left;margin-right: 5px;margin-top: 1px;}
        .agent-grjj{width: 220px}
        .warnings {
            margin: 20px 0;

            padding: 20px 0 20px 0;
            background: #fffbf6;
            border: 1px solid #f5dcbc;
        }
        .warnings p {
            background: url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left;
            font-size: 14px;
            line-height: 24px;
            font-weight: bold;
            color: #333;
            width: 420px;
            margin: auto;

        }
        .contentRight .box p.jjr_6 {
            background-position: 0px -76px;
        }
        .com-text{
            color: #0058FF !important;
        }
        .new-box{
            text-align: left;
            border-bottom: 1px #f2f2f2 dashed;


            line-height: 30px;
            margin-top: 6px;

            display: flex;
        }
        .in-img{
            width: 15px;
            height: 15px;

            margin-left: 10px;
            margin-top: 8px;
        }
        .in-right{
            margin-left: 12px;
        }
        .in-text{
            color: #999;
        }
        .contentRight h2{
            line-height: 26px !important;
        }

        .a-con{
           display: flex;
            margin-top: 16px;
        }
        .a-img{
            width: 46px;
            height: 46px;
            border-radius: 50%;
            margin-right: 9px;
        }
        .a-name{
            text-align: left;
            font-size: 13.9px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #303030;
            margin-bottom: 5px;
        }
        .a-area{
            font-size: 12px;
            font-family: PingFang SC-Regular, PingFang SC;
            color: #303030;
            text-align: left;
        }
        .no-house{
            width: 370px;
            height: 100%;
        }
        .no-text{
            font-family: PingFang SC-Medium, PingFang SC;

            color: #696969;
        }
        .a-click{
            display: flex;

            text-decoration: none !important;
        }

    </style>
    <script th:inline="javascript" >
        function qingchu() {
            setTimeout(function () {
                location.href = "/agentIntermediary/second/"+[[${agentIntermediaryId}]];
            },500);
        }
        $(function() {
            $("#bottonss").click(function() {
                var dd = $("#hidden0").val();
                var dd1 = $("#Hidden1").val();
                var dd2 = $("#Hidden2").val();
                //模糊搜索字段
                var dds = $("#xiaoquid").val();
                // var sou = "68680";
                if (dd != null && dd !="") {
                    dd = "-p"+dd;
                }
                if (dd1 != null && dd1 != "") {
                    dd1 = "-a"+dd1;
                }
                if (dd2 != null && dd2 != "") {
                    dd2 = "-l"+dd2;
                }
                var search ;
                if (dds == null || dds == undefined || dds == '') {
                    search = ""
                }else {
                    search = "-search="+dds;
                }
                var url = "/agentIntermediary/second/"+[[${agentIntermediaryId}]]+'/' + dd + dd1 + dd2 +search;
//                setCookie("searchKey",dds,10);
                location.href = url;

            });
        });
        $(function() {
            var hr = window.location.href;
            var str=hr.split("_");
            var zj = str[1];
            getData(zj, "#hidden0", "#zongjia");
            var mj = str[2];
            getData(mj, "#Hidden1", "#mianji");
            var hx = str[3];
            getData(hx, "#Hidden2", "#huxing");
            var xq = str[4];
            if (xq != "" && xq != null) {
                $("#xiaoquid").val(decodeURIComponent(xq));
            }

            //var name0 = $("#hidden0").pasaleHouse(".my_xl").find(".my_xl_list li[value=" + mj + "]").html();
            //$("#zongjia").html(name0);
        })
        function getData(name, num, adds) {
            if (name != -1 && name != "" && name != null) {
                $(num).val(name);
                var bb = $(num).pasaleHouse(".my_xl").find(".my_xl_list li[value=" + name + "]").html();
                $(adds).html(bb);
                var container = $("body"),
                    scrollTo = $('.content');
                container.scrollTop(0);
                container.scrollTop(
                    scrollTo.offset().top - container.offset().top + container.scrollTop()
                );
            }
        }
    </script>
</head>
<body >
    <!--页头-->
    <div id="head2017" th:include = "fragment/fragment::firstNav"  th:with="firstNavIndex=3"></div>
    <input type="hidden" id="agentIntermediaryId" th:value="${agentIntermediaryId}" >
    <div class="main">
        <div th:include = "fragment/fragment::intermediaryShopCard"></div>
        <div th:if="${!#strings.isEmpty(shopStatus)  and  shopStatus ne '1'}" class="warning warnings">
            <p>
                很抱歉，该经纪人店铺已经关闭。<br>
                <a th:href="@{/findAgent}"> 查找更多经纪人>></a>
            </p>
        </div>
        <div class="content" th:if="${#strings.isEmpty(shopStatus)  or  shopStatus eq '1'}">
            <div th:include = "fragment/fragment::agentIntermediaryHead" th:with="agentHouseTpye=1"></div>
            <div class="contentMain">
                <div class="contentCtn">
                    <div class="my_xl" th:if="${!#lists.isEmpty(price)}">
                        <input name="hidden0" type="hidden" id="hidden0"  class="my_xl_input" th:each="p,stat:${price}" th:if="${p.selected}" th:value="${p.id}" />
                        <div class="my_xl_txt" th:each="p,stat:${price}" th:if="${p.selected}" th:text="${stat.index eq 0 ? '总价': p.name}" id="zongjia">总价</div>
                        <div class="my_xl_btn"></div>
                        <ul class="my_xl_list">
                            <a  th:each="p:${price}" th:href="${#strings.isEmpty(p.url)? '':p.url}" ><li data-key="PriceBase" th:text="${#strings.isEmpty(p.name)? '':p.name }" th:value="${#strings.isEmpty(p.id)? '': p.id}">全部</li></a>
                        </ul>
                    </div>
                    <div class="my_xl" th:if="${!#lists.isEmpty(area)}">
                        <input name="Hidden1" type="hidden" id="Hidden1" class="my_xl_input" th:each="a,sata:${area}" th:if="${a.selected}" th:value="${a.id}"  />
                        <div class="my_xl_txt" id="mianji" th:each="a,sata:${area}" th:if="${a.selected}" th:text="${sata.index eq 0 ?'面积': a.name}"  >面积</div>
                        <div class="my_xl_btn"></div>
                        <ul class="my_xl_list">
                            <a  th:each="a:${area}" th:href="${#strings.isEmpty(a.url)? '':a.url }" ><li data-key="BuildArea" th:value="${#strings.isEmpty(a.id)? '':a.id}" th:text="${#strings.isEmpty(a.name)?'':a.name}">全部</li></a>
                        </ul>
                    </div>
                    <div class="my_xl"th:if="${!#lists.isEmpty(room)}">
                        <input name="Hidden2" type="hidden" id="Hidden2" class="my_xl_input"  th:each="l,stat:${room}" th:if="${l.selected}" th:value="${l.id}" />
                        <div class="my_xl_txt" id="huxing" th:each="l,stat:${room}" th:if="${l.selected}" th:text="${stat.index eq 0? '户型':l.name}">户型</div>
                        <div class="my_xl_btn"></div>
                        <ul class="my_xl_list">
                            <a th:each="l:${room}" th:href="${#strings.isEmpty(l.url)? '':l.url}"><li data-key="roomType" th:text="${#strings.isEmpty(l.name)? '':l.name}" th:value="${#strings.isEmpty(l.id)? '':l.id}">全部</li></a>
                        </ul>
                    </div>
                    <div class="houseSearch">
                        <input type="text" id="xiaoquid" class="searchInput" th:value="${searchName}" autocomplete="off" placeholder="请输入查询的小区" >
                        <input name="button" id="bottonss" type="button" class="searchBtn" value="" >
                    </div>
<!--                    <p onclick="qingchu()"><i></i>清空筛选条件</p>-->
                    <!--<span>小二为你找到<i th:text="${#strings.isEmpty(msg)? '': msg}">55</i>个符合条件的房源</span>-->
                </div>
                <div class="cl"></div>
<!--                <div th:if="${#lists.isEmpty(saleHouses)}" class="warning">-->
<!--                    <p>-->
<!--                        很抱歉，沈阳暂时没有符合您要求的房源，您可以更改条件重新搜索。<br>-->
<!--                        <a th:href="@{/saleHouses}" target="_blank"> 查找相似房源>></a>-->
<!--                    </p>-->
<!--                </div>-->

                <div th:if="${#lists.isEmpty(saleHouses)}" class="no-box">
                    <img class="no-house" src="https://static.fangxiaoer.com/web/images/sy/house/no-house.png">
                    <div class="no-text">暂无房源</div>
                </div>

                <div class="inf" th:if="${!#lists.isEmpty(saleHouses)}" th:each="saleHouse:${saleHouses}">
                    <a th:href="${#strings.isEmpty(saleHouse.houseId)?'':'/salehouse/'+saleHouse.houseId+'.htm'}" target="_blank" class="infLeft">
                        <img  th:src="${#strings.isEmpty(saleHouse.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': saleHouse.pic}" th:alt="${#strings.isEmpty(saleHouse.title)? '':saleHouse.title}" />
                        <!--VR and 视频都存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) ne null and #strings.toString(saleHouse.mediaID) ne null }">
                            <s class="vrListIcon" th:if="${#strings.toString(saleHouse.PanID) ne null}"></s>
                            <s class="videoListIcon" th:if="${saleHouse.mediaID ne null}"></s>
                        </s>
                        <!--VR存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) ne null and #strings.toString(saleHouse.mediaID) eq null }">
                            <s class="vrListIcon" th:if="${#strings.toString(saleHouse.PanID) ne null}"></s>
                        </s>
                        <!--视频存在 -->
                        <s class="listIconK" th:if="${#strings.toString(saleHouse.PanID) eq null and #strings.toString(saleHouse.mediaID) ne null }">
                            <s class="videoListIcon" th:if="${saleHouse.mediaID ne null}"></s>
                        </s>
                    </a>
                    <div class="infCtn">
                        <a class="newHouseListTitle" target="_blank" th:href="${#strings.isEmpty(saleHouse.houseId) ? '':'/salehouse/'+saleHouse.houseId+'.htm' }" th:text="${#strings.isEmpty(saleHouse.title)? '':saleHouse.title}">
                            国瑞城 1室 1厅 1卫 49㎡</a>
                        <div  class="fourSpan">
                            <span><th:block th:text="${#strings.isEmpty(saleHouse.room)?'':saleHouse.room+'室'}"></th:block><th:block th:text="${#strings.isEmpty(saleHouse.hall)?'':saleHouse.hall+'厅'}"></th:block><th:block th:text="${#strings.isEmpty(saleHouse.toilet)?'':saleHouse.toilet+'卫'}"></th:block></span>
                            <span><th:block th:text="${#strings.isEmpty(saleHouse.area)? '':(#strings.indexOf(saleHouse.area,'.') eq -1 ? saleHouse.area:#strings.toString(saleHouse.area).replaceAll('0+?$','').replaceAll('[.]$',''))+'m²'}"></th:block></span>
                            <span><th:block th:text="${#strings.isEmpty(saleHouse.floor) and #strings.isEmpty(saleHouse.totalFloorNumber) ? '':saleHouse.floorDesc +'/'+saleHouse.totalFloorNumber+'层' }"></th:block></span>
                            <span th:text="${#strings.isEmpty(saleHouse.forward) ? '':saleHouse.forward }"></span>
                            <span th:if="${saleHouse.buildDate ne null and saleHouse.buildDate ne ''}" th:text="${saleHouse.buildDate+'年建筑'}"></span>
                        </div>
                        <p  class="houseAddress"  th:if="${!#strings.isEmpty(saleHouse.regionName) and !#strings.isEmpty(saleHouse.subName) and !#strings.isEmpty(saleHouse.plantName) and !#strings.isEmpty(saleHouse.address)}">
                            <s th:if="${ !#strings.isEmpty(saleHouse.subName)}" class="houseAddressSpance">
                                <i th:text="${saleHouse.subName}"></i>
                            </s>
                            <s th:if="${!#strings.isEmpty(saleHouse.regionName) and !#strings.isEmpty(saleHouse.plantName) and !#strings.isEmpty(saleHouse.address)}">
                                <i th:if="${!#strings.isEmpty(saleHouse.regionName)}" th:text="${saleHouse.regionName}"></i>-
                                <i th:if="${!#strings.isEmpty(saleHouse.plantName)}" th:text="${saleHouse.plantName}"></i>-
                                <i th:if="${!#strings.isEmpty(saleHouse.address)}" th:text="${saleHouse.address}"></i>
                            </s>
                        </p>
                        <div  class="houseItemIcon">
                            <div class="isGoodHouse" th:if="${saleHouse.isGoodHouse eq '-1'}"></div>
                            <span th:if="${!#strings.isEmpty(saleHouse.houseTrait) and oc.count lt 4}" th:each="establish,oc:${#strings.setSplit(saleHouse.houseTrait,',') }" th:text="${#strings.isEmpty(establish)? '':establish}" th:class="${'tese_'+oc.count}"></span>
                        </div>
                        <!--<p class="person" th:text="${#strings.isEmpty(saleHouse.spanTime)? '':saleHouse.spanTime+'更新'}">-->
                        <!--8分钟前更新-->
                        <!--</p>-->
                        <!--<span class='tese_2'>繁华地段</span> <span class='tese_3'>交通便利</span>-->
                        <!--<span style="display: none; background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale/iconAuthentichouse.gif) no-repeat 9px 3px; padding-left: 76px; font-size: 16px; color: #E04545; position: absolute; left: 206px; bottom: 2px;"></span>-->
                        <!--<a class="checkHouse" target="_blank"  th:href="${#strings.isEmpty(saleHouse.subID) ? '': '/saleHouses/-v'+saleHouse.subID}">查看同小区房源 ></a>-->
                    </div>
                    <div class="infRight">
                        <p class="infRightPriseM" th:if="${#strings.isEmpty(saleHouse.price) or (#strings.toString(saleHouse.price) eq '0.0')}"  th:text='面议' style="font-weight:bold" ></p>
                        <p class="infRightPrise" th:if= "${!#strings.isEmpty(saleHouse.price) and #strings.toString(saleHouse.price) ne '0.0'}" >
                            <th:block th:text="${#strings.toString(saleHouse.price).contains('.')? #strings.toString(saleHouse.price).replaceAll('0+?$','').replaceAll('[.]$', '') : saleHouse.price}"></th:block>
                            <i th:text="${#strings.isEmpty(saleHouse.price) or (#strings.toString(saleHouse.price) eq '0.0')?'':'万'}"></i>
                        </p>
                        <p>
                            <th:block th:text="${#strings.isEmpty(saleHouse.price) or saleHouse.price eq 0 ? '': (#strings.indexOf(saleHouse.unitPrice,'.') eq -1 ? saleHouse.area:#strings.toString(saleHouse.unitPrice).replaceAll('0+?$','').replaceAll('[.]$',''))+'元/m²'}"></th:block>
                        </p>
                    </div>
                </div>
                <div class="page" th:include="fragment/page :: page "></div>
            </div>
            <div style="float: right; width: 242px;">
                <div class="contentRight">
                    <h2>推荐经纪人</h2>

                    <div class="a-box">

                        <div class="a-con" th:each="p:${companyAgentList}">
                            <a  class="a-click" th:href="${'/agent/second/'+p.memberId}">
                            <img class="a-img"  th:src="${#strings.isEmpty(p.pic)? 'https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg': p.pic}" />
                            <div class="a-right">
                                <div class="a-name" th:text="${p.realName}"></div>
                                <div class="a-area">主营小区： <span th:if="${!#strings.isEmpty(p.subName1)}" th:text="${p.subName1}"></span>
                                    <span th:if="${!#strings.isEmpty(p.subName2)}" th:text="${p.subName2}"></span>
                                    <span th:if="${!#strings.isEmpty(p.subName3)}" th:text="${p.subName3}"></span></div>
                            </div>
                            </a>
                        </div>

                    </div>


                    <!--所属机构-->
                    <!--<div th:include = "agent/fragmentinter::affiliate"></div>-->



                </div>

            </div>
        </div>
        <!--个人介绍点击展开/收起-->
        <script>

        </script>
    </div>
    <script th:inline="javascript">
        var intermediaryName = [[${intermediaryName}]];
        console.log('打印结果',intermediaryName)


        var companyAgentList = [[${companyAgentList}]];
        console.log('打印结果',companyAgentList)

    </script>
    <script type="text/javascript" src="https://static.fangxiaoer.com/js/form_xl.js"></script>
    <div class="cl"></div>
    <div th:include="fragment/fragment:: footer_detail" ></div>
    <div th:include="fragment/fragment::tongji"></div>
    <div th:include="fragment/fragment::esfCommonFloatNoRight"></div>
</body>
</html>
