<!doctype html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<title>沈阳楼盘风云榜 - 房小二网</title>
	<meta name="keywords" content="沈阳楼盘,沈阳楼盘榜,沈阳新房">
	<meta name="description" content="沈阳楼盘风云榜为您提供沈阳最新人气楼盘，热搜楼盘，热评楼盘以供广大购房者参考。买房卖房，就上房小二网！">
	<!-- <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script> -->
	<link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
	<link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/brand/brandIndex.css?v=20181225" />
	<script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
    <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/syranking/syranking.css?v=20190322">
</head>
<body>
	<!-- 头部导航 -->
	<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=7,subNavIndex=1"></div>
	<!-- 头部导航 -->

	<!-- 风云榜banner -->
	<div class="ranking-banner"></div>
	<!-- 风云榜banner -->

	<!-- 网站位置提醒 -->
	<div class="crumb">
		<div class="content_box">
			<h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> > <a th:href="${'/houses/'}">沈阳新房</a> > 房产排行榜</h1>
		</div>
	</div>
	<!-- 网站位置提醒 -->

	<!-- 排行榜切换按钮 -->
	<div class="ranking-tab">
		<a th:href="${'/projectRank/'+rankt.id}" th:each="rankt,i:${rankType}">
			<div th:class="'hotSearch layout'+${#strings.toString(type) eq rankt.id?' click':''}" th:id="${'btn'+i.count}" th:text="${rankt.name}" class="popularity layout" >人气榜</div>
		</a>
	</div>
	<!-- 排行榜切换按钮 -->

	<!-- 排行榜品牌窗口:前三名 -->
	<div class="ranking-developer" th:if="${!#lists.isEmpty(topList)}">
		<!-- 第二名 -->
		<a th:if="${i.index &lt; 3}"  th:each="project,i:${topList}" th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}" target="_blank">
			<div  th:class="${i.index == 1?'ranking-number-c':'ranking-number-m'}" >
				<h3 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}">中旅万科城</h3>
				<div class="developer-img">
					<img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" th:alt="${#strings.isEmpty(project.projectName)?'':project.projectName}" />
				</div>

				<div class="flag">
					<img th:src="'https://static.fangxiaoer.com/web/images/ranking/Crown-'+${project.rankOrder}+'.png'" class="crown" alt="皇冠">
					<img th:src="'https://static.fangxiaoer.com/web/images/ranking/first-'+${project.rankOrder}+'.png'" />
					<h1 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}">中旅万科城</h1>
				</div>
				<div class="developer-info">
					<h2 style="font-size: 24px" th:if="${project.mPrice eq null}">待定</h2>
					<h2 th:unless="${project.mPrice eq null}">
						<th:block th:utext="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney+'<span>元/㎡</span>' :#strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','') +'<span>元/㎡</span>'}"></th:block>
						<span class="mark" th:text="${project.mPrice.priceType eq '均价'?'均':'起'}">均</span>
					</h2>
					<p>
						<span th:each="features,j:${#strings.setSplit(project.features,' ')}" th:if="${j.index &lt; 3 and !#strings.isEmpty(project.features)}" th:text="${features}">品牌地产</span>
					</p>
				</div>
			</div>
		</a>
		<!-- 第二名 -->
	</div>
	<!-- 排行榜品牌窗口 -->

	<!-- 排行榜top ten -->
	<div class="ranking-topten">
		<!-- 单数排名 -->
		<a th:if="${!#lists.isEmpty(rankList) and i.index lt 10}" th:each="project,i:${rankList}" th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}"target="_blank">
			<div class="ranking-star" >
			<th:block th:if="${i.index%2 == 0}">
				<!-- 项目图片 -->
				<div class="star-img">
					<img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" th:alt="${#strings.isEmpty(project.projectName)?'':project.projectName}" />
				</div>
				<!-- 项目图片 -->
				<!-- =================================== -->
				<!-- 中心轴线 -->
				<div class="star-center">
					<span class="sign"></span>
					<span class="arrow arrow-left"></span>
				</div>
				<!-- 中心轴线 -->
				<!-- =================================== -->
				<!-- 项目信息 -->
				<div class="star-info">

					<div class="serial serial-right">
						<span th:class="${'list-'+i.count}"></span>
						<img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
					</div>

					<div class="info-detailed detailed-right">
						<h1><th:block th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></th:block> <span th:text="${#strings.isEmpty(project.regionName)?'':'['+project.regionName+']'}">[浑南区]</span></h1>
						<div class="msg">
							<p>
								<span style="margin-right: 15px" th:if="${!#lists.isEmpty(project.layout) and !#maps.isEmpty(project.area)}">
									<th:block th:if="${!#maps.isEmpty(project.area)}" th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))+'~'+
                 #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}"></th:block>
									<th:block th:if="${#lists.isEmpty(project.area) and #lists.isEmpty(project.layout)}" th:text="${'暂无资料'}"></th:block>

                                    <span class="housetype" th:if="${!#lists.isEmpty(project.layout)}"><th:block th:each="layout,i:${project.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block><th:block th:each="layout,i:${project.layout}" th:if="${i.index &gt; 0}" th:text="${'/'+layout.RoomType+'居'}"></th:block></span>
								</span>

								<b style="font-size: 22px;" th:if="${project.mPrice eq null}">待定</b>
								<b th:unless="${project.mPrice eq null}"><th:block th:utext="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney+'<span>元/㎡</span>' : project.mPrice.priceType + ':' +#strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','') +'<span>元/㎡</span>'}"></th:block>
									<span class="mark" th:text="${project.mPrice.priceType eq '均价'?'均':'起'}">均</span></b>
							</p>

						</div>

						<div class="tag tag-left">
							<span class="y" th:if="${project.projectType ne '1'}" >
								<th:block th:if="${project.projectType eq '2'}">别墅</th:block>
								<th:block  th:if="${project.projectType eq '3'}">写字楼</th:block>
							</span>
                            <span class="b" th:each="features,i:${#strings.setSplit(project.features,' ')}" th:if="${i.index &lt; 3 and project.projectType ne '3'}" th:text="${features}">品牌地产</span>
                            <span class="b" th:each="features,i:${#strings.setSplit(project.features,' ')}" th:if="${i.index &lt; 2 and project.projectType eq '3'}" th:text="${features}">品牌地产</span>
						</div>
					</div>
				</div>
				<!-- 项目信息 -->
			</th:block>
			<th:block th:if="${i.index%2!= 0}">
				<div class="star-info">
					<div class="serial serial-left">
						<span th:class="${'list-'+i.count}"></span>
						<img  th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
					</div>
					<div class="info-detailed detailed-left">
						<h1><th:block th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></th:block> <span th:text="${#strings.isEmpty(project.regionName)?'':'['+project.regionName+']'}">[浑南区]</span></h1>

						<div class="msg">
							<p>
								<b style="font-size: 22px;" th:if="${project.mPrice eq null}">待定</b>
								<b th:unless="${project.mPrice eq null}"><th:block th:utext="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney+'<span>元/㎡</span>' : project.mPrice.priceType + ':' +#strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','') +'<span>元/㎡</span>'}"></th:block>
									<span class="mark" th:text="${project.mPrice.priceType eq '均价'?'均':'起'}">均</span></b>

                                <span style="margin-left: 15px" th:if="${!#lists.isEmpty(project.layout) and !#maps.isEmpty(project.area)}">
									<th:block th:if="${!#maps.isEmpty(project.area)}" th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))+'~'+
                 #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}"></th:block>
									<th:block th:if="${#lists.isEmpty(project.area) and #lists.isEmpty(project.layout)}" th:text="${'暂无资料'}"></th:block>
                                    	<span class="housetype" th:if="${!#lists.isEmpty(project.layout)}">
                                            <th:block th:each="layout,i:${project.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block><th:block th:each="layout,i:${project.layout}" th:if="${i.index &gt; 0}" th:text="${'/'+layout.RoomType+'居'}"></th:block>
                                        </span>
								</span>


							</p>

						</div>

						<div class="tag tag-right">
							<span class="b" th:each="features,i:${#strings.setSplit(project.features,' ')}" th:if="${i.index &lt; 3 and project.projectType ne '3'}" th:text="${features}">品牌地产</span>
							<span class="b" th:each="features,i:${#strings.setSplit(project.features,' ')}" th:if="${i.index &lt; 2 and project.projectType eq '3'}" th:text="${features}">品牌地产</span>
							<span class="y" th:if="${project.projectType ne '1'}" >
								<th:block th:if="${project.projectType eq '2'}">别墅</th:block>
								<th:block  th:if="${project.projectType eq '3'}">写字楼</th:block>
							</span>
						</div>
					</div>
				</div>
				<!-- 项目信息 -->
				<!-- =================================== -->
				<!-- 中心轴线 -->
				<div class="star-center">
					<span class="sign"></span>
					<span class="arrow arrow-right"></span>
				</div>
				<!-- 中心轴线 -->
				<!-- =================================== -->
				<!-- 项目图片 -->
				<div class="star-img">
					<img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" th:alt="${#strings.isEmpty(project.projectName)?'':project.projectName}" />
				</div>
				<!-- 项目图片 -->
			</th:block>
		</div>
		</a>
	</div>


	<!-- 免责声明 -->
	<div class="ranking-declare">
		<p>免责声明：榜单推荐根据房小二网新房的浏览量、搜索量、关注量、评论量等统计数据计算得出，不能完全代表市场真实情况，仅供参考。</p>
	</div>

</body>

<script>
//    首部切换效果
	$(".ranking-developer").children().hover(function () {
		$(".ranking-number-c").removeClass("ranking-number-c");
		$("div.ranking-developer").children().children().addClass("ranking-number-m");
		$(this).children().removeClass('ranking-number-m');
		$(this).children().addClass('ranking-number-c');
	})

//    图片缩放效果
    $("div.star-img").mouseover(function (){
        $(".star-img img").removeClass("img-hover");
        $(this).find("img").addClass("img-hover");
	}).mouseout(function () {
        $(".star-img img").removeClass("img-hover");
    });


</script>
<!--底部-->
<div class="footer" th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--统计-->
<!--右侧浮标-->
<div th:include="fragment/fragment::commonFloat"></div>
<!--右侧浮标-->

</html>