<!doctype html>
<html xmlns:th="https://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <title>沈阳楼盘风云榜 - 房小二网</title>
    <meta name="keywords" content="沈阳楼盘,沈阳楼盘榜,沈阳新房">
    <meta name="description" content="沈阳楼盘风云榜为您提供沈阳最新人气楼盘，热搜楼盘，热评楼盘以供广大购房者参考。买房卖房，就上房小二网！">
    <!-- <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script> -->
    <link href="https://static.fangxiaoer.com/web/styles/main.css?v=20180602" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/main2017.css?v=20180602" />
    <link rel="stylesheet" type="text/css" href="https://static.fangxiaoer.com/web/styles/brand/brandIndex.css?v=20181225" />
    <script src="https://static.fangxiaoer.com/js/jquery-1.9.0.min.js" type="text/javascript" charset="utf-8"></script>
<script src="https://static.fangxiaoer.com/js/jquery-migrate-1.4.1.js" type="text/javascript" charset="utf-8"></script>
        <link rel="stylesheet" href="https://static.fangxiaoer.com/web/styles/syranking/syranking.css?v=20241230">
<!--    <link rel="stylesheet" href="/css/syranking.css">-->
</head>
<style>
    .none{
        display: none;
    }
    .fixed{
        display: inline-flex;
        top: 0;
        /*width: 1171px;*/
        position: fixed;
        z-index: 99;
        margin: 0;
    }
    /*.vrs{ width: 34px; height: 34px; position: absolute; left: 5px; bottom: 5px;*/
    /*    background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}*/

    .vrs{ width: 34px; height: 34px; position: absolute; left: 5px; bottom: 5px; z-index: 0; }
    .vrs i{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 3; display: block; background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;}
    .vrs b{ width: 100%; height: 100%; position: absolute; left: 0px; bottom: 0px; z-index: 1; display: block; background-color: rgba(0,0,0,0.5); border-radius: 50%;}

    .vru{ width: 22px; height: 22px; position: absolute; left: 13px; top: 55px;
        background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_list.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 0;}
</style>
<body>
<!-- 头部导航 -->
<div id="head2017" th:include="fragment/fragment::firstNav" th:with="firstNavIndex=2,subNavIndex=4"></div>
<!-- 头部导航 -->

<!-- 风云榜banner -->
<div class="ranking-banner"></div>
<!-- 风云榜banner -->
<div class="bodyBG">
    <!-- 网站位置提醒 -->
    <div class="crumb">
        <div class="content_box">
            <h1>您的位置 : <a th:href="${'/'}">沈阳房产网</a> > <a th:href="${'/houses/'}">沈阳新房</a> > 房产排行榜</h1>
        </div>
    </div>
    <!-- 网站位置提醒 -->

    <!-- 排行榜切换按钮 -->
    <div class="content">
        <div class="contentLeft">
            <div class="ranking-tab ranking-tab2 none">
                <a th:href="${'/projectRank/'+rankt.id}" th:each="rankt,i:${rankType}">
                    <div th:class="'hotSearch layout'+${#strings.toString(value) eq rankt.id?' click':''}" th:id="${'btn'+i.count}" th:text="${rankt.name}" class="popularity layout" >人气榜</div>
                </a>
            </div>

            <!-- 排行榜切换按钮 -->
            <div class="rank_area rank_area2 none" th:if="${#strings.toString(type) eq '2' or #strings.toString(type) eq '5'}">
                <ul>.0.
                    <li th:class="${rId eq null? 'areaBg' : ''}"><a th:href="${'/projectRank/' + type}">全城</a></li>
                    <li th:each="region:${regionFilter}" th:if="${region.id ne ''}"  th:class="${region ne null and #strings.toString(rId) eq region.id ? 'areaBg' : ''}">
                        <a th:href="${'/projectRank/5_' + region.id}" th:text="${region.name}"></a>
                    </li>
                </ul>
            </div>
            <div class="ranking-tab">
                <a th:href="${'/projectRank/'+rankt.id}" th:each="rankt,i:${rankType}">
                    <div th:class="'hotSearch layout'+${#strings.toString(value) eq rankt.id?' click':''}" th:id="${'btn'+i.count}" th:text="${rankt.name}" class="popularity layout" >人气榜</div>
                </a>
            </div>

            <!-- 排行榜切换按钮 -->
            <div class="rank_area" th:if="${#strings.toString(type) eq '2' or #strings.toString(type) eq '5'}">
                <ul>
                    <li th:class="${rId eq null? 'areaBg' : ''}"><a th:href="${'/projectRank/' + type}">全城</a></li>
                    <li th:each="region:${regionFilter}" th:if="${region.id ne ''}"  th:class="${region ne null and #strings.toString(rId) eq region.id ? 'areaBg' : ''}">
                        <a th:href="${'/projectRank/5_' + region.id}" th:text="${region.name}"></a>
                    </li>
                </ul>
            </div>

            <!-- 排行榜top ten -->
            <div class="ranking-topten">
                <!-- 单数排名 -->
                <a th:if="${!#lists.isEmpty(rankList) and i.index lt 10}" th:each="project,i:${rankList}" th:href="${'/house/'+project.projectId+'-'+project.projectType+'.htm'}" target="_blank">
                    <div  class="divlist">
                        <div class="ranking-star" >
                            <!-- 项目图片 -->
                            <div class="star_bg">
                                <span th:text="${i.count}"></span>
                            </div>
                            <div class="star-img" style="position:relative;">
                                <img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" th:alt="${#strings.isEmpty(project.projectName)?'':project.projectName}" />
                                <div class="vrs" th:if="${project.pan ne null}"><i></i><b></b></div>
                            </div>
                            <!-- 项目图片 -->
                            <!-- =================================== -->
                            <!-- 中心轴线 -->
                            <div class="star-center none">
                                <span class="sign"></span>
                                <span class="arrow arrow-left"></span>
                            </div>
                            <!-- 中心轴线 -->
                            <!-- =================================== -->
                            <!-- 项目信息 -->
                            <div class="star-info">

                                <div class="serial serial-right none">
                                    <span th:class="${'list-'+i.count}"></span>
                                    <img th:src="${#strings.isEmpty(project.ImageUrl)?'':project.ImageUrl}" alt="">
                                </div>

                                <div th:class="${#strings.toString(project.rankType) eq '4' ? 'info-detailed detailed-right newproject' : 'info-detailed detailed-right'}">
                                    <h1><th:block th:text="${#strings.isEmpty(project.projectName) ? '' : #strings.toString(project.projectName).length() gt 15 ? #strings.toString(project.projectName).substring(0, 10) + '...'  : project.projectName}"></th:block>
                                        <span th:text="${#strings.isEmpty(project.regionName)?'':'['+project.regionName+']'}">[浑南区]</span></h1>
                                    <div class="msg">
                                        <p>
								<span style="margin-right: 15px" th:if="${!#lists.isEmpty(project.layout) and !#maps.isEmpty(project.area)}">
									                                    <span class="housetype" th:if="${!#lists.isEmpty(project.layout)}"><th:block th:each="layout,i:${project.layout}" th:if="${i.index eq 0}" th:text="${layout.RoomType+'居'}"></th:block><th:block th:each="layout,i:${project.layout}" th:if="${i.index &gt; 0}" th:text="${'&nbsp;&nbsp;'+layout.RoomType+'居'}"></th:block></span>

									<img class="geng" src="/imagese/Geng.png" alt=""><th:block th:if="${!#maps.isEmpty(project.area)}" th:text="${#strings.toString(project.area.minArea).substring(0,#strings.toString(project.area.minArea).indexOf('.'))+'-'+
                 #strings.toString(project.area.maxArea).substring(0,#strings.toString(project.area.maxArea).indexOf('.'))+'㎡'}"></th:block>
									<th:block th:if="${#lists.isEmpty(project.area) and #lists.isEmpty(project.layout)}" th:text="${'暂无资料'}"></th:block>

								</span>

                                            <b style="font-size: 22px;" th:if="${project.mPrice eq null}" class="daiding">待定</b>
                                            <b th:unless="${project.mPrice eq null}"><th:block th:utext="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney+'<span>元/㎡</span>' : project.mPrice.priceType + ':' +#strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','') +'<span>元/㎡</span>'}"></th:block>
                                                <span class="mark" th:text="${project.mPrice.priceType eq '均价'?'均':'起'}">均</span></b>
                                        </p>

                                    </div>
                                    <div class="Early" th:if="${#strings.toString(type) eq '4'}">开盘时间 ：<span th:text="${project.addTime}"></span></div>
                                    <div class="tag tag-left">
							<span class="y" th:if="${project.projectType ne '1'}" >
								<th:block th:if="${project.projectType eq '2'}">别墅</th:block>
								<th:block  th:if="${project.projectType eq '3'}">写字楼</th:block>
							</span>
                                        <span class="b" th:each="features,i:${#strings.setSplit(project.features,' ')}" th:if="${i.index &lt; 4 and project.projectType ne '3' and project.projectType ne '2'}" th:text="${features}">品牌地产</span>
                                        <span class="b" th:each="features,i:${#strings.setSplit(project.features,' ')}" th:if="${i.index &lt; 3 and project.projectType ne '1'}" th:text="${features}">品牌地产</span>
                                    </div>
                                </div>
                                <div>

                                </div>
                            </div>
                            <!-- 项目信息 -->
                        </div>
                        <div class="reasons" th:if="${project.rankType ne '2' and project.rankType ne '5'}">
                            推荐理由：<span th:text="${project.rankDesc}"></span>
                        </div>
                    </div>


                </a>
            </div>

        </div>
        <div class="contentRight">
            <div class="childlist">
            <h1 class="childLtit">最新楼盘信息</h1>
            <div class="childLtab">
                <span class="chitabN">序号</span>
                <span class="chitabL">楼盘名称</span>
                <span class="chitabJ">价格</span>
                <span class="chitabQ">区域</span>
            </div>
            <ul>
                <li class="chilistLi" th:each="project,i:${projects}">
                    <div>
                        <div th:class="${i.count le 3 ? 'chiListRank spcolor' : 'chiListRank'}" th:text="${i.count}"></div>
                        <div class="crankList" th:style="${i.count eq 1 ? 'display:none':'display:block'}">
                            <ul>
                                <li th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></li>
                                <li th:if="${project.mPrice eq null}" class="DDlist">待定</li>
                                <li th:if="${project.mPrice ne null}"><span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡</li>
                                <li th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}"></li>
                            </ul>
                        </div>
                        <a  th:href="${'/house/'+project.projectId+'-'+project.type+'.htm'}" target="_blank">
                            <div class="crankDetails" th:style="${i.count eq 1 ? 'display:block':'display:none'}">
                                <div class="crankDetailsdiv" style="position: relative">
                                    <img th:src="${#strings.isEmpty(project.pic)?'':project.pic}" alt="">
                                    <div class="vru" th:if="${project.pan ne null}"></div>
                                    <div class="crankDetailsdivR">
                                        <h2 th:text="${#strings.isEmpty(project.projectName)?'':project.projectName}"></h2>
                                        <p class="crankDetailsP" th:if="${project.mPrice eq null}" >待定</p>
                                        <p class="crankDetailsP" th:if="${project.mPrice ne null}"><span th:text="${#strings.toString(project.mPrice.priceMoney).indexOf('.') eq -1 ? project.mPrice.priceMoney : #strings.toString(project.mPrice.priceMoney).replaceAll('0+?$','').replaceAll('[.]$','')}"></span>元/㎡
                                            <span class="Ljun" th:text="${project.mPrice.priceType eq '起价'? '起' :'均'}"></span></p>
                                        <p  class="crankDetailsD">
                                            <span th:text="${#strings.isEmpty(project.regionName)?'':project.regionName}"></span>
                                            <span class="lGeng" th:if="${!#maps.isEmpty(project.area)}"
                                                  th:text="${#strings.toString(project.area[0].minArea).substring(0,#strings.toString(project.area[0].minArea).indexOf('.'))+'-'+
                 #strings.toString(project.area[0].maxArea).substring(0,#strings.toString(project.area[0].maxArea).indexOf('.'))+'㎡'}"></span>
                                        </p>
                                    </div>
                                </div>
                                <!--<div class="crankDetailsreason" th:if="${#strings.toString(project.rankType) eq '6' or #strings.toString(project.rankType) eq '7'}">-->
                                    <!--推荐理由：<span th:text="${project.rankDesc}"></span>-->
                                <!--</div>-->
                            </div>

                        </a>
                    </div>
                </li>
            </ul>
        </div>
        </div>
    </div>
</div>




<!-- 免责声明 -->
<div class="ranking-declare">
    <p>免责声明：榜单推荐根据房小二网新房的浏览量、搜索量、关注量等统计数据计算得出，不能完全代表市场真实情况，仅供参考。</p>
</div>

</body>

<script>
    //    首部切换效果
    $(".ranking-developer").children().hover(function () {
        $(".ranking-number-c").removeClass("ranking-number-c");
        $("div.ranking-developer").children().children().addClass("ranking-number-m");
        $(this).children().removeClass('ranking-number-m');
        $(this).children().addClass('ranking-number-c');
    })

    //    图片缩放效果
    $("div.star-img").mouseover(function (){
        $(".star-img img").removeClass("img-hover");
        $(this).find("img").addClass("img-hover");
    }).mouseout(function () {
        $(".star-img img").removeClass("img-hover");
    });
    $(".star_bg").eq(0).css({
        "background-color":"#fe3003",
        "color":"#ffffff"
    });
    $(".star_bg").eq(1).css({
        "background-color":"#fe7713",
        "color":"#ffffff"
    });
    $(".star_bg").eq(2).css({
        "background-color":"#feb313",
        "color":"#ffffff"
    });

    $(".crankList").eq(0).hide();
    $(".crankDetails").eq(0).show();

    $(".crankList").hover(function () {
        // $(".crankList").show();
        $(this).parent().parent().parent().find(".crankList").show();
        $(this).hide();
        $(this).parent().parent().parent().find(".crankDetails").hide();
        $(this).parent().find(".crankDetails").show();
    })




    window.onscroll = function(){
        var s = $(this).scrollTop();
        if (s >= 400) {
            $(".ranking-tab2,.rank_area2").addClass("fixed");
//								$(".FloatNav").show();
//								$(".schoolNav li a").removeClass("schoolNavHover");
								$(".rank_area2").css("margin-top","50px");

        }else {
            $(".ranking-tab2,.rank_area2").removeClass("fixed");
//								$(".FloatNav").hide();
//             $(".ranking-topten").css("margin-top","20px");

//								scrollTop: targetOffset - 283

        }
//
//


    }
</script>
<!--底部-->
<div class="footer" th:include="fragment/fragment::footer_detail"></div>
<!--统计-->
<div th:include="fragment/fragment::tongji"></div>
<!--统计-->
<!--右侧浮标-->
<div th:include="fragment/fragment::newHouseFloat"></div>
<!--右侧浮标-->

</html>