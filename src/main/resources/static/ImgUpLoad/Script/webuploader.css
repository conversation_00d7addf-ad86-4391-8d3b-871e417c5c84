.webuploader-container {
	position: relative;
}
.webuploader-element-invisible {
	position: absolute !important;
	clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
    clip: rect(1px,1px,1px,1px);
}
.webuploader-pick {
	position: relative;
	display: inline-block;
	cursor: pointer;
	background: url(https://static.fangxiaoer.com/web/images/sy/sale/form/icons_pic.png);
	/* padding: 2px 5px 3px 5px; */
	color: #fff;
	text-align: center;
	border-radius: 3px;
	overflow: hidden;
}
.webuploader-pick-hover {
	background: url(https://static.fangxiaoer.com/web/images/sy/sale/form/icons_pic.png) -127px 0;
}

.webuploader-pick-disable {
	opacity: 0.6;
	pointer-events:none;
}

