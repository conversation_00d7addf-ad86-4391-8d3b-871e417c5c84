﻿@charset "utf-8";
/* CSS Document*/
.parentFileBox {
	width:auto;
	height:auto;
	overflow:hidden;
	position:relative;
}
.parentFileBox>.fileBoxUl {
	position:relative;
	width:100%;
	height:auto;
	overflow:hidden;
	padding-bottom:5px;
}
.parentFileBox>.fileBoxUl>li {
	float:left;
	border: 1px solid #ddd;
	border-radius:5px;
	width:90px;
	height:90px;
	margin-top:5px;
	margin-left:5px;
	overflow:hidden;
	position:relative;
	background-color:#fff;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.075);
	padding:5px;
	clear: none;
}
.parentFileBox>.fileBoxUl>li>.viewThumb {
	position:absolute;
	top:5px;
	left:5px;
	width:90px;
	line-height:90px;
	height:90px;
	overflow:hidden;
	background-repeat:no-repeat;
	background-position:center;
	background-size:100%;
}
.parentFileBox>.fileBoxUl>li>.viewThumb>img {
	max-width:100%;
	max-height:100%;
}
.parentFileBox>.fileBoxUl>li>.diyCancel,.parentFileBox>.fileBoxUl>li>.diySuccess {
	position:absolute;
	width: 33px;
	height:20px;
	bottom: -2px;
	left: 5px;
	cursor:pointer;
	display:none;
	z-index:200;
	color: #FFF;
}
.parentFileBox>.fileBoxUl>li>.diySuccess {
	/*background:url(/images/check_alt.png) no-repeat;*/
	cursor:default;
}
.parentFileBox>.fileBoxUl>li>.diyFileName {
	position:absolute;
	bottom:0px;
	left:0px;
	width:100%;
	height:20px;
	line-height:20px;
	text-align:center;
	color:#fff;
	font-size:12px;
	display:none;
	/*/images/bgblack.png*/
}
.parentFileBox>.fileBoxUl>li>.diyBar {
	top:0;
	left:0;
	position: absolute;
	width: 100%;
	height: 100%;
	line-height:100px;
	/*background:url(/images/bgblack.png);*/
	display:none;
}
.parentFileBox>.fileBoxUl>li>.diyBar>.diyProgressText {
	font-size:13px;
	text-align:right;
	color:#FFF;
	position: absolute;
	z-index:99;
	width: 100%;
	bottom: -40px;
	right: 3px;
}
.parentFileBox>.fileBoxUl>li>.diyBar>.diyProgress {
	position:absolute;
	left:0;
	bottom: 0;
	height:18px;
	line-height:18px;
	width:100%;
	background-color:#000;
	filter:alpha(opacity=70);
	-moz-opacity:0.7;
	opacity:0.7;
	z-index:97;
}
.parentFileBox>.diyButton {
	width:100%;
	margin-top:5px;
	margin-bottom:5px;
	height:20px;
	line-height:20px;
	text-align:center;
}
.parentFileBox>.diyButton>a {
	padding:5px 10px 5px 10px;
	background-color:#09C;
	color:#FFF;
	font-size:12px;
	text-decoration:none;
	border-radius:3px;
}
.parentFileBox>.diyButton>a:hover {
	background-color:#0CC;
}


.parentFileBox>.fileBoxUl>li:hover .diyFileName {
	display:block;
}
.avi_diy_bg,.txt_diy_bg,.doc_diy_bg,.zip_diy_bg,.csv_diy_bg,.xls_diy_bg,.mp3_diy_bg,.pdf_diy_bg,.rar_diy_bg {
	background-position:center;
	background-repeat:no-repeat;
}

/* 2015-08-11 王鹏加入 */
.parentFileBox>.fileBoxUl>.diyUploadHover:hover .setCover {
	display:block;
}

.parentFileBox .diyUploadHover .setCover .diyProgressText{
	font-size:13px;
	line-height:20px;
	color:#FFF;
	position:relative;
	z-index:99;
}
.parentFileBox .diyUploadHover .setCover {
	bottom:0px;
	left:0;
	position: absolute;
	width: 100%;
	height: 22px;
	line-height:22px;
	/*background:url(../images/bgblack.png);*/
	display:none;
}

.parentFileBox>.fileBoxUl>li>.setCover>.diyProgress {
	position:absolute;
	left:0;
	bottom:0px;
	height:22px;
	line-height:22px;
	width:100%;
	background-color:#000;
	filter:alpha(opacity=70);
	-moz-opacity:0.7;
	opacity:0.7;
	z-index:97;
}
.parentFileBox .diyUploadHover .icoCover {
	top:0;
	left:0;
	position: absolute;
	width: 49px;
	height: 43px;
	background:url(/images/icoCover.png);
}
.shoutu{ position: absolute; background: url(https://static.fangxiaoer.com/web/images/ico/sign/shoutu.png);width: 27px;height: 27px;}

#fileImg{height: 158px;border: 1px solid #ececec;}
.webuploader-pick{width: 96px;height: 84px;line-height: 40px;font-size: 16px;font-family: "微软雅黑";margin: 34px 0 0 50px;}
.diyUploadHover .loading{background: url(https://static.fangxiaoer.com/web/images/ico/sign/loading.gif) center no-repeat #fff ;z-index: 100;position: absolute;width: 100%;height: 100%;left: 0;top: 0;}

.xiaotieshi{width: 460px;position:absolute;top: 24px;left: 182px;font-size: 14px;line-height: 28px;}
.xiaotieshi h3{font-size: 14px;color: #111;}

/* 2022年9月2日10 提示语样式修改 */
.Font999{color:#999 !important;font-size: 12px;}