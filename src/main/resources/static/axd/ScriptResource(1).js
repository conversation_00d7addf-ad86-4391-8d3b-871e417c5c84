﻿//----------------------------------------------------------
// Copyright (C) Microsoft Corporation. All rights reserved.
//----------------------------------------------------------
// MicrosoftAjaxWebForms.js
Type.registerNamespace("Sys.WebForms");Sys.WebForms.BeginRequestEventArgs=function(b,a){Sys.WebForms.BeginRequestEventArgs.initializeBase(this);this._request=b;this._postBackElement=a};Sys.WebForms.BeginRequestEventArgs.prototype={get_postBackElement:function(){return this._postBackElement},get_request:function(){return this._request}};Sys.WebForms.BeginRequestEventArgs.registerClass("Sys.WebForms.BeginRequestEventArgs",Sys.EventArgs);Sys.WebForms.EndRequestEventArgs=function(c,a,b){Sys.WebForms.EndRequestEventArgs.initializeBase(this);this._errorHandled=false;this._error=c;this._dataItems=a||{};this._response=b};Sys.WebForms.EndRequestEventArgs.prototype={get_dataItems:function(){return this._dataItems},get_error:function(){return this._error},get_errorHandled:function(){return this._errorHandled},set_errorHandled:function(a){this._errorHandled=a},get_response:function(){return this._response}};Sys.WebForms.EndRequestEventArgs.registerClass("Sys.WebForms.EndRequestEventArgs",Sys.EventArgs);Sys.WebForms.InitializeRequestEventArgs=function(b,a){Sys.WebForms.InitializeRequestEventArgs.initializeBase(this);this._request=b;this._postBackElement=a};Sys.WebForms.InitializeRequestEventArgs.prototype={get_postBackElement:function(){return this._postBackElement},get_request:function(){return this._request}};Sys.WebForms.InitializeRequestEventArgs.registerClass("Sys.WebForms.InitializeRequestEventArgs",Sys.CancelEventArgs);Sys.WebForms.PageLoadedEventArgs=function(b,a,c){Sys.WebForms.PageLoadedEventArgs.initializeBase(this);this._panelsUpdated=b;this._panelsCreated=a;this._dataItems=c||{}};Sys.WebForms.PageLoadedEventArgs.prototype={get_dataItems:function(){return this._dataItems},get_panelsCreated:function(){return this._panelsCreated},get_panelsUpdated:function(){return this._panelsUpdated}};Sys.WebForms.PageLoadedEventArgs.registerClass("Sys.WebForms.PageLoadedEventArgs",Sys.EventArgs);Sys.WebForms.PageLoadingEventArgs=function(b,a,c){Sys.WebForms.PageLoadingEventArgs.initializeBase(this);this._panelsUpdating=b;this._panelsDeleting=a;this._dataItems=c||{}};Sys.WebForms.PageLoadingEventArgs.prototype={get_dataItems:function(){return this._dataItems},get_panelsDeleting:function(){return this._panelsDeleting},get_panelsUpdating:function(){return this._panelsUpdating}};Sys.WebForms.PageLoadingEventArgs.registerClass("Sys.WebForms.PageLoadingEventArgs",Sys.EventArgs);Sys.WebForms.PageRequestManager=function(){this._form=null;this._activeDefaultButton=null;this._activeDefaultButtonClicked=false;this._updatePanelIDs=null;this._updatePanelClientIDs=null;this._updatePanelHasChildrenAsTriggers=null;this._asyncPostBackControlIDs=null;this._asyncPostBackControlClientIDs=null;this._postBackControlIDs=null;this._postBackControlClientIDs=null;this._scriptManagerID=null;this._pageLoadedHandler=null;this._additionalInput=null;this._onsubmit=null;this._onSubmitStatements=[];this._originalDoPostBack=null;this._originalDoPostBackWithOptions=null;this._originalFireDefaultButton=null;this._originalDoCallback=null;this._isCrossPost=false;this._postBackSettings=null;this._request=null;this._onFormSubmitHandler=null;this._onFormElementClickHandler=null;this._onWindowUnloadHandler=null;this._asyncPostBackTimeout=null;this._controlIDToFocus=null;this._scrollPosition=null;this._processingRequest=false;this._scriptDisposes={};this._transientFields=["__VIEWSTATEENCRYPTED","__VIEWSTATEFIELDCOUNT"]};Sys.WebForms.PageRequestManager.prototype={_get_eventHandlerList:function(){if(!this._events)this._events=new Sys.EventHandlerList;return this._events},get_isInAsyncPostBack:function(){return this._request!==null},add_beginRequest:function(a){this._get_eventHandlerList().addHandler("beginRequest",a)},remove_beginRequest:function(a){this._get_eventHandlerList().removeHandler("beginRequest",a)},add_endRequest:function(a){this._get_eventHandlerList().addHandler("endRequest",a)},remove_endRequest:function(a){this._get_eventHandlerList().removeHandler("endRequest",a)},add_initializeRequest:function(a){this._get_eventHandlerList().addHandler("initializeRequest",a)},remove_initializeRequest:function(a){this._get_eventHandlerList().removeHandler("initializeRequest",a)},add_pageLoaded:function(a){this._get_eventHandlerList().addHandler("pageLoaded",a)},remove_pageLoaded:function(a){this._get_eventHandlerList().removeHandler("pageLoaded",a)},add_pageLoading:function(a){this._get_eventHandlerList().addHandler("pageLoading",a)},remove_pageLoading:function(a){this._get_eventHandlerList().removeHandler("pageLoading",a)},abortPostBack:function(){if(!this._processingRequest&&this._request){this._request.get_executor().abort();this._request=null}},_cancelPendingCallbacks:function(){for(var a=0,e=window.__pendingCallbacks.length;a<e;a++){var c=window.__pendingCallbacks[a];if(c){if(!c.async)window.__synchronousCallBackIndex=-1;window.__pendingCallbacks[a]=null;var d="__CALLBACKFRAME"+a,b=document.getElementById(d);if(b)b.parentNode.removeChild(b)}}},_commitControls:function(a,b){if(a){this._updatePanelIDs=a.updatePanelIDs;this._updatePanelClientIDs=a.updatePanelClientIDs;this._updatePanelHasChildrenAsTriggers=a.updatePanelHasChildrenAsTriggers;this._asyncPostBackControlIDs=a.asyncPostBackControlIDs;this._asyncPostBackControlClientIDs=a.asyncPostBackControlClientIDs;this._postBackControlIDs=a.postBackControlIDs;this._postBackControlClientIDs=a.postBackControlClientIDs}if(typeof b!=="undefined"&&b!==null)this._asyncPostBackTimeout=b*1000},_createHiddenField:function(c,d){var b,a=document.getElementById(c);if(a)if(!a._isContained)a.parentNode.removeChild(a);else b=a.parentNode;if(!b){b=document.createElement("span");b.style.cssText="display:none !important";this._form.appendChild(b)}b.innerHTML="<input type='hidden' />";a=b.childNodes[0];a._isContained=true;a.id=a.name=c;a.value=d},_createPageRequestManagerTimeoutError:function(){var b="Sys.WebForms.PageRequestManagerTimeoutException: "+Sys.WebForms.Res.PRM_TimeoutError,a=Error.create(b,{name:"Sys.WebForms.PageRequestManagerTimeoutException"});a.popStackFrame();return a},_createPageRequestManagerServerError:function(a,d){var c="Sys.WebForms.PageRequestManagerServerErrorException: "+(d||String.format(Sys.WebForms.Res.PRM_ServerError,a)),b=Error.create(c,{name:"Sys.WebForms.PageRequestManagerServerErrorException",httpStatusCode:a});b.popStackFrame();return b},_createPageRequestManagerParserError:function(b){var c="Sys.WebForms.PageRequestManagerParserErrorException: "+String.format(Sys.WebForms.Res.PRM_ParserError,b),a=Error.create(c,{name:"Sys.WebForms.PageRequestManagerParserErrorException"});a.popStackFrame();return a},_createPostBackSettings:function(c,b,a){return {async:c,panelID:b,sourceElement:a}},_convertToClientIDs:function(a,d,c){if(a)for(var b=0,e=a.length;b<e;b++){Array.add(d,a[b]);Array.add(c,this._uniqueIDToClientID(a[b]))}},_destroyTree:function(f){if(f.nodeType===1){var d=f.childNodes;for(var b=d.length-1;b>=0;b--){var a=d[b];if(a.nodeType===1){if(a.dispose&&typeof a.dispose==="function")a.dispose();else if(a.control&&typeof a.control.dispose==="function")a.control.dispose();var e=Sys.UI.Behavior.getBehaviors(a);for(var c=e.length-1;c>=0;c--)e[c].dispose();this._destroyTree(a)}}}},dispose:function(){if(this._form){Sys.UI.DomEvent.removeHandler(this._form,"submit",this._onFormSubmitHandler);Sys.UI.DomEvent.removeHandler(this._form,"click",this._onFormElementClickHandler);Sys.UI.DomEvent.removeHandler(window,"unload",this._onWindowUnloadHandler);Sys.UI.DomEvent.removeHandler(window,"load",this._pageLoadedHandler)}if(this._originalDoPostBack){window.__doPostBack=this._originalDoPostBack;this._originalDoPostBack=null}if(this._originalDoPostBackWithOptions){window.WebForm_DoPostBackWithOptions=this._originalDoPostBackWithOptions;this._originalDoPostBackWithOptions=null}if(this._originalFireDefaultButton){window.WebForm_FireDefaultButton=this._originalFireDefaultButton;this._originalFireDefaultButton=null}if(this._originalDoCallback){window.WebForm_DoCallback=this._originalDoCallback;this._originalDoCallback=null}this._form=null;this._updatePanelIDs=null;this._updatePanelClientIDs=null;this._asyncPostBackControlIDs=null;this._asyncPostBackControlClientIDs=null;this._postBackControlIDs=null;this._postBackControlClientIDs=null;this._asyncPostBackTimeout=null;this._scrollPosition=null},_doCallback:function(d,b,c,f,a,e){if(!this.get_isInAsyncPostBack())this._originalDoCallback(d,b,c,f,a,e)},_doPostBack:function(a,e){this._additionalInput=null;var b=this._form;if(a===null||typeof a==="undefined"||this._isCrossPost){this._postBackSettings=this._createPostBackSettings(false,null,null);this._isCrossPost=false}else{var f=this._uniqueIDToClientID(a),d=document.getElementById(f);if(!d)if(Array.contains(this._asyncPostBackControlIDs,a))this._postBackSettings=this._createPostBackSettings(true,this._scriptManagerID+"|"+a,null);else if(Array.contains(this._postBackControlIDs,a))this._postBackSettings=this._createPostBackSettings(false,null,null);else{var c=this._findNearestElement(a);if(c)this._postBackSettings=this._getPostBackSettings(c,a);else this._postBackSettings=this._createPostBackSettings(false,null,null)}else this._postBackSettings=this._getPostBackSettings(d,a)}if(!this._postBackSettings.async){b.onsubmit=this._onsubmit;this._originalDoPostBack(a,e);b.onsubmit=null;return}b.__EVENTTARGET.value=a;b.__EVENTARGUMENT.value=e;this._onFormSubmit()},_doPostBackWithOptions:function(a){this._isCrossPost=a&&a.actionUrl;this._originalDoPostBackWithOptions(a)},_elementContains:function(b,a){while(a){if(a===b)return true;a=a.parentNode}return false},_endPostBack:function(a,d,f){if(this._request===d.get_webRequest()){this._processingRequest=false;this._additionalInput=null;this._request=null}var e=this._get_eventHandlerList().getHandler("endRequest"),b=false;if(e){var c=new Sys.WebForms.EndRequestEventArgs(a,f?f.dataItems:{},d);e(this,c);b=c.get_errorHandled()}if(a&&!b)throw a},_findNearestElement:function(a){while(a.length>0){var d=this._uniqueIDToClientID(a),c=document.getElementById(d);if(c)return c;var b=a.lastIndexOf("$");if(b===-1)return null;a=a.substring(0,b)}return null},_findText:function(b,a){var c=Math.max(0,a-20),d=Math.min(b.length,a+20);return b.substring(c,d)},_fireDefaultButton:function(a,d){if(a.keyCode===13){var c=a.srcElement||a.target;if(!c||c.tagName.toLowerCase()!=="textarea"){var b=document.getElementById(d);if(b&&typeof b.click!=="undefined"){this._activeDefaultButton=b;this._activeDefaultButtonClicked=false;try{b.click()}finally{this._activeDefaultButton=null}a.cancelBubble=true;if(typeof a.stopPropagation==="function")a.stopPropagation();return false}}}return true},_getPageLoadedEventArgs:function(j,e){var i=[],h=[],d=e?e.updatePanelData:null,c,g,b;if(!d){c=this._updatePanelIDs;g=null;b=null}else{c=d.updatePanelIDs;g=d.childUpdatePanelIDs;b=d.panelsToRefreshIDs}var a,f;if(b)for(a=0,f=b.length;a<f;a++)Array.add(i,document.getElementById(this._uniqueIDToClientID(b[a])));for(a=0,f=c.length;a<f;a++)if(j||Array.indexOf(g,c[a])!==-1)Array.add(h,document.getElementById(this._uniqueIDToClientID(c[a])));return new Sys.WebForms.PageLoadedEventArgs(i,h,e?e.dataItems:{})},_getPageLoadingEventArgs:function(h){var g=[],f=[],c=h.updatePanelData,b=c.oldUpdatePanelIDs,j=c.updatePanelIDs,i=c.childUpdatePanelIDs,e=c.panelsToRefreshIDs,a,d;for(a=0,d=e.length;a<d;a++)Array.add(g,document.getElementById(this._uniqueIDToClientID(e[a])));for(a=0,d=b.length;a<d;a++)if(Array.indexOf(e,b[a])===-1&&(Array.indexOf(j,b[a])===-1||Array.indexOf(i,b[a])>-1))Array.add(f,document.getElementById(this._uniqueIDToClientID(b[a])));return new Sys.WebForms.PageLoadingEventArgs(g,f,h.dataItems)},_getPostBackSettings:function(a,c){var d=a,b=null;while(a){if(a.id){if(!b&&Array.contains(this._asyncPostBackControlClientIDs,a.id))b=this._createPostBackSettings(true,this._scriptManagerID+"|"+c,d);else if(!b&&Array.contains(this._postBackControlClientIDs,a.id))return this._createPostBackSettings(false,null,null);else{var e=Array.indexOf(this._updatePanelClientIDs,a.id);if(e!==-1)if(this._updatePanelHasChildrenAsTriggers[e])return this._createPostBackSettings(true,this._updatePanelIDs[e]+"|"+c,d);else return this._createPostBackSettings(true,this._scriptManagerID+"|"+c,d)}if(!b&&this._matchesParentIDInList(a.id,this._asyncPostBackControlClientIDs))b=this._createPostBackSettings(true,this._scriptManagerID+"|"+c,d);else if(!b&&this._matchesParentIDInList(a.id,this._postBackControlClientIDs))return this._createPostBackSettings(false,null,null)}a=a.parentNode}if(!b)return this._createPostBackSettings(false,null,null);else return b},_getScrollPosition:function(){var a=document.documentElement;if(a&&(this._validPosition(a.scrollLeft)||this._validPosition(a.scrollTop)))return {x:a.scrollLeft,y:a.scrollTop};else{a=document.body;if(a&&(this._validPosition(a.scrollLeft)||this._validPosition(a.scrollTop)))return {x:a.scrollLeft,y:a.scrollTop};else if(this._validPosition(window.pageXOffset)||this._validPosition(window.pageYOffset))return {x:window.pageXOffset,y:window.pageYOffset};else return {x:0,y:0}}},_initializeInternal:function(a,b){if(this._prmInitialized)throw Error.invalidOperation(Sys.WebForms.Res.PRM_CannotRegisterTwice);this._prmInitialized=true;this._scriptManagerID=a;this._form=b;this._onsubmit=this._form.onsubmit;this._form.onsubmit=null;this._onFormSubmitHandler=Function.createDelegate(this,this._onFormSubmit);this._onFormElementClickHandler=Function.createDelegate(this,this._onFormElementClick);this._onWindowUnloadHandler=Function.createDelegate(this,this._onWindowUnload);Sys.UI.DomEvent.addHandler(this._form,"submit",this._onFormSubmitHandler);Sys.UI.DomEvent.addHandler(this._form,"click",this._onFormElementClickHandler);Sys.UI.DomEvent.addHandler(window,"unload",this._onWindowUnloadHandler);this._originalDoPostBack=window.__doPostBack;if(this._originalDoPostBack)window.__doPostBack=Function.createDelegate(this,this._doPostBack);this._originalDoPostBackWithOptions=window.WebForm_DoPostBackWithOptions;if(this._originalDoPostBackWithOptions)window.WebForm_DoPostBackWithOptions=Function.createDelegate(this,this._doPostBackWithOptions);this._originalFireDefaultButton=window.WebForm_FireDefaultButton;if(this._originalFireDefaultButton)window.WebForm_FireDefaultButton=Function.createDelegate(this,this._fireDefaultButton);this._originalDoCallback=window.WebForm_DoCallback;if(this._originalDoCallback)window.WebForm_DoCallback=Function.createDelegate(this,this._doCallback);this._pageLoadedHandler=Function.createDelegate(this,this._pageLoadedInitialLoad);Sys.UI.DomEvent.addHandler(window,"load",this._pageLoadedHandler)},_matchesParentIDInList:function(c,b){for(var a=0,d=b.length;a<d;a++)if(c.startsWith(b[a]+"_"))return true;return false},_onFormElementActive:function(a,d,e){if(a.disabled)return;this._postBackSettings=this._getPostBackSettings(a,a.name);if(a.name){var b=a.tagName.toUpperCase();if(b==="INPUT"){var c=a.type;if(c==="submit")this._additionalInput=encodeURIComponent(a.name)+"="+encodeURIComponent(a.value);else if(c==="image")this._additionalInput=encodeURIComponent(a.name)+".x="+d+"&"+encodeURIComponent(a.name)+".y="+e}else if(b==="BUTTON"&&a.name.length!==0&&a.type==="submit")this._additionalInput=encodeURIComponent(a.name)+"="+encodeURIComponent(a.value)}},_onFormElementClick:function(a){this._activeDefaultButtonClicked=a.target===this._activeDefaultButton;this._onFormElementActive(a.target,a.offsetX,a.offsetY)},_onFormSubmit:function(h){var g=true,t=this._isCrossPost;this._isCrossPost=false;var e,s;if(this._onsubmit)g=this._onsubmit();if(g)for(e=0,s=this._onSubmitStatements.length;e<s;e++)if(!this._onSubmitStatements[e]()){g=false;break}if(!g){if(h)h.preventDefault();return}var n=this._form;if(t)return;if(this._activeDefaultButton&&!this._activeDefaultButtonClicked)this._onFormElementActive(this._activeDefaultButton,0,0);if(!this._postBackSettings.async)return;var a=new Sys.StringBuilder;a.append(encodeURIComponent(this._scriptManagerID)+"="+encodeURIComponent(this._postBackSettings.panelID)+"&");var v=n.elements.length;for(e=0;e<v;e++){var d=n.elements[e],f=d.name;if(typeof f==="undefined"||f===null||f.length===0||f===this._scriptManagerID)continue;var m=d.tagName.toUpperCase();if(m==="INPUT"){var k=d.type;if(k==="text"||k==="password"||k==="hidden"||(k==="checkbox"||k==="radio")&&d.checked){a.append(encodeURIComponent(f));a.append("=");a.append(encodeURIComponent(d.value));a.append("&")}}else if(m==="SELECT"){var u=d.options.length;for(var o=0;o<u;o++){var q=d.options[o];if(q.selected){a.append(encodeURIComponent(f));a.append("=");a.append(encodeURIComponent(q.value));a.append("&")}}}else if(m==="TEXTAREA"){a.append(encodeURIComponent(f));a.append("=");a.append(encodeURIComponent(d.value));a.append("&")}}a.append("__ASYNCPOST=true&");if(this._additionalInput){a.append(this._additionalInput);this._additionalInput=null}var c=new Sys.Net.WebRequest,b=n.action;if(Sys.Browser.agent===Sys.Browser.InternetExplorer){var p=b.indexOf("#");if(p!==-1)b=b.substr(0,p);var l=b.indexOf("?");if(l!==-1){var r=b.substr(0,l);if(r.indexOf("%")===-1)b=encodeURI(r)+b.substr(l)}else if(b.indexOf("%")===-1)b=encodeURI(b)}c.set_url(b);c.get_headers()["X-MicrosoftAjax"]="Delta=true";c.get_headers()["Cache-Control"]="no-cache";c.set_timeout(this._asyncPostBackTimeout);c.add_completed(Function.createDelegate(this,this._onFormSubmitCompleted));c.set_body(a.toString());var i,j=this._get_eventHandlerList().getHandler("initializeRequest");if(j){i=new Sys.WebForms.InitializeRequestEventArgs(c,this._postBackSettings.sourceElement);j(this,i);g=!i.get_cancel()}if(!g){if(h)h.preventDefault();return}this._scrollPosition=this._getScrollPosition();this.abortPostBack();j=this._get_eventHandlerList().getHandler("beginRequest");if(j){i=new Sys.WebForms.BeginRequestEventArgs(c,this._postBackSettings.sourceElement);j(this,i)}if(this._originalDoCallback)this._cancelPendingCallbacks();this._request=c;this._processingRequest=false;c.invoke();if(h)h.preventDefault()},_onFormSubmitCompleted:function(c){this._processingRequest=true;if(c.get_timedOut()){this._endPostBack(this._createPageRequestManagerTimeoutError(),c,null);return}if(c.get_aborted()){this._endPostBack(null,c,null);return}if(!this._request||c.get_webRequest()!==this._request)return;if(c.get_statusCode()!==200){this._endPostBack(this._createPageRequestManagerServerError(c.get_statusCode()),c,null);return}var a=this._parseDelta(c);if(!a)return;var b,e;if(a.asyncPostBackControlIDsNode&&a.postBackControlIDsNode&&a.updatePanelIDsNode&&a.panelsToRefreshNode&&a.childUpdatePanelIDsNode){var p=this._updatePanelIDs,h=a.childUpdatePanelIDsNode.content,n=h.length?h.split(","):[],l=this._splitNodeIntoArray(a.asyncPostBackControlIDsNode),m=this._splitNodeIntoArray(a.postBackControlIDsNode),o=this._splitNodeIntoArray(a.updatePanelIDsNode),g=this._splitNodeIntoArray(a.panelsToRefreshNode);for(b=0,e=g.length;b<e;b++){var i=this._uniqueIDToClientID(g[b]);if(!document.getElementById(i)){this._endPostBack(Error.invalidOperation(String.format(Sys.WebForms.Res.PRM_MissingPanel,i)),c,a);return}}var f=this._processUpdatePanelArrays(o,l,m);f.oldUpdatePanelIDs=p;f.childUpdatePanelIDs=n;f.panelsToRefreshIDs=g;a.updatePanelData=f}a.dataItems={};var d;for(b=0,e=a.dataItemNodes.length;b<e;b++){d=a.dataItemNodes[b];a.dataItems[d.id]=d.content}for(b=0,e=a.dataItemJsonNodes.length;b<e;b++){d=a.dataItemJsonNodes[b];a.dataItems[d.id]=Sys.Serialization.JavaScriptSerializer.deserialize(d.content)}var k=this._get_eventHandlerList().getHandler("pageLoading");if(k)k(this,this._getPageLoadingEventArgs(a));Sys._ScriptLoader.readLoadedScripts();Sys.Application.beginCreateComponents();var j=Sys._ScriptLoader.getInstance();this._queueScripts(j,a.scriptBlockNodes,true,false);this._processingRequest=true;j.loadScripts(0,Function.createDelegate(this,Function.createCallback(this._scriptIncludesLoadComplete,a)),Function.createDelegate(this,Function.createCallback(this._scriptIncludesLoadFailed,a)),null)},_onWindowUnload:function(){this.dispose()},_pageLoaded:function(a,c){var b=this._get_eventHandlerList().getHandler("pageLoaded");if(b)b(this,this._getPageLoadedEventArgs(a,c));if(!a)Sys.Application.raiseLoad()},_pageLoadedInitialLoad:function(){this._pageLoaded(true,null)},_parseDelta:function(g){var c=g.get_responseData(),d,h,D,E,C,b=0,e=null,i=[];while(b<c.length){d=c.indexOf("|",b);if(d===-1){e=this._findText(c,b);break}h=parseInt(c.substring(b,d),10);if(h%1!==0){e=this._findText(c,b);break}b=d+1;d=c.indexOf("|",b);if(d===-1){e=this._findText(c,b);break}D=c.substring(b,d);b=d+1;d=c.indexOf("|",b);if(d===-1){e=this._findText(c,b);break}E=c.substring(b,d);b=d+1;if(b+h>=c.length){e=this._findText(c,c.length);break}C=c.substr(b,h);b+=h;if(c.charAt(b)!=="|"){e=this._findText(c,b);break}b++;Array.add(i,{type:D,id:E,content:C})}if(e){this._endPostBack(this._createPageRequestManagerParserError(String.format(Sys.WebForms.Res.PRM_ParserErrorDetails,e)),g,null);return null}var w=[],u=[],o=[],v=[],r=[],B=[],z=[],y=[],t=[],q=[],k,n,s,l,m,p,x;for(var j=0,F=i.length;j<F;j++){var a=i[j];switch(a.type){case "updatePanel":Array.add(w,a);break;case "hiddenField":Array.add(u,a);break;case "arrayDeclaration":Array.add(o,a);break;case "scriptBlock":Array.add(v,a);break;case "scriptStartupBlock":Array.add(r,a);break;case "expando":Array.add(B,a);break;case "onSubmit":Array.add(z,a);break;case "asyncPostBackControlIDs":k=a;break;case "postBackControlIDs":n=a;break;case "updatePanelIDs":s=a;break;case "asyncPostBackTimeout":l=a;break;case "childUpdatePanelIDs":m=a;break;case "panelsToRefreshIDs":p=a;break;case "formAction":x=a;break;case "dataItem":Array.add(y,a);break;case "dataItemJson":Array.add(t,a);break;case "scriptDispose":Array.add(q,a);break;case "pageRedirect":if(Sys.Browser.agent===Sys.Browser.InternetExplorer){var f=document.createElement("a");f.style.display="none";f.attachEvent("onclick",A);f.href=a.content;document.body.appendChild(f);f.click();f.detachEvent("onclick",A);document.body.removeChild(f);function A(a){a.cancelBubble=true}}else window.location.href=a.content;return;case "error":this._endPostBack(this._createPageRequestManagerServerError(Number.parseInvariant(a.id),a.content),g,null);return null;case "pageTitle":document.title=a.content;break;case "focus":this._controlIDToFocus=a.content;break;default:this._endPostBack(this._createPageRequestManagerParserError(String.format(Sys.WebForms.Res.PRM_UnknownToken,a.type)),g,null);return null}}return {executor:g,updatePanelNodes:w,hiddenFieldNodes:u,arrayDeclarationNodes:o,scriptBlockNodes:v,scriptStartupNodes:r,expandoNodes:B,onSubmitNodes:z,dataItemNodes:y,dataItemJsonNodes:t,scriptDisposeNodes:q,asyncPostBackControlIDsNode:k,postBackControlIDsNode:n,updatePanelIDsNode:s,asyncPostBackTimeoutNode:l,childUpdatePanelIDsNode:m,panelsToRefreshNode:p,formActionNode:x}},_processUpdatePanelArrays:function(e,l,n){var d,c,b;if(e){var f=e.length;d=new Array(f);c=new Array(f);b=new Array(f);for(var a=0;a<f;a++){var k=e[a].substr(1),m=e[a].charAt(0)==="t";b[a]=m;d[a]=k;c[a]=this._uniqueIDToClientID(k)}}else{d=[];c=[];b=[]}var i=[],g=[];this._convertToClientIDs(l,i,g);var j=[],h=[];this._convertToClientIDs(n,j,h);return {updatePanelIDs:d,updatePanelClientIDs:c,updatePanelHasChildrenAsTriggers:b,asyncPostBackControlIDs:i,asyncPostBackControlClientIDs:g,postBackControlIDs:j,postBackControlClientIDs:h}},_queueScripts:function(scriptLoader,scriptBlockNodes,queueIncludes,queueBlocks){for(var i=0,l=scriptBlockNodes.length;i<l;i++){var scriptBlockType=scriptBlockNodes[i].id;switch(scriptBlockType){case "ScriptContentNoTags":if(!queueBlocks)continue;scriptLoader.queueScriptBlock(scriptBlockNodes[i].content);break;case "ScriptContentWithTags":var scriptTagAttributes;eval("scriptTagAttributes = "+scriptBlockNodes[i].content);if(scriptTagAttributes.src){if(!queueIncludes||Sys._ScriptLoader.isScriptLoaded(scriptTagAttributes.src))continue}else if(!queueBlocks)continue;scriptLoader.queueCustomScriptTag(scriptTagAttributes);break;case "ScriptPath":if(!queueIncludes||Sys._ScriptLoader.isScriptLoaded(scriptBlockNodes[i].content))continue;scriptLoader.queueScriptReference(scriptBlockNodes[i].content)}}},_registerDisposeScript:function(a,b){if(!this._scriptDisposes[a])this._scriptDisposes[a]=[b];else Array.add(this._scriptDisposes[a],b)},_scriptIncludesLoadComplete:function(e,b){if(b.executor.get_webRequest()!==this._request)return;this._commitControls(b.updatePanelData,b.asyncPostBackTimeoutNode?b.asyncPostBackTimeoutNode.content:null);if(b.formActionNode)this._form.action=b.formActionNode.content;var a,d,c;for(a=0,d=b.updatePanelNodes.length;a<d;a++){c=b.updatePanelNodes[a];var j=document.getElementById(c.id);if(!j){this._endPostBack(Error.invalidOperation(String.format(Sys.WebForms.Res.PRM_MissingPanel,c.id)),b.executor,b);return}this._updatePanel(j,c.content)}for(a=0,d=b.scriptDisposeNodes.length;a<d;a++){c=b.scriptDisposeNodes[a];this._registerDisposeScript(c.id,c.content)}for(a=0,d=this._transientFields.length;a<d;a++){var g=document.getElementById(this._transientFields[a]);if(g){var k=g._isContained?g.parentNode:g;k.parentNode.removeChild(k)}}for(a=0,d=b.hiddenFieldNodes.length;a<d;a++){c=b.hiddenFieldNodes[a];this._createHiddenField(c.id,c.content)}if(b.scriptsFailed)throw Sys._ScriptLoader._errorScriptLoadFailed(b.scriptsFailed.src,b.scriptsFailed.multipleCallbacks);this._queueScripts(e,b.scriptBlockNodes,false,true);var i="";for(a=0,d=b.arrayDeclarationNodes.length;a<d;a++){c=b.arrayDeclarationNodes[a];i+="Sys.WebForms.PageRequestManager._addArrayElement('"+c.id+"', "+c.content+");\r\n"}var h="";for(a=0,d=b.expandoNodes.length;a<d;a++){c=b.expandoNodes[a];h+=c.id+" = "+c.content+"\r\n"}if(i.length)e.queueScriptBlock(i);if(h.length)e.queueScriptBlock(h);this._queueScripts(e,b.scriptStartupNodes,true,true);var f="";for(a=0,d=b.onSubmitNodes.length;a<d;a++){if(a===0)f="Array.add(Sys.WebForms.PageRequestManager.getInstance()._onSubmitStatements, function() {\r\n";f+=b.onSubmitNodes[a].content+"\r\n"}if(f.length){f+="\r\nreturn true;\r\n});\r\n";e.queueScriptBlock(f)}e.loadScripts(0,Function.createDelegate(this,Function.createCallback(this._scriptsLoadComplete,b)),null,null)},_scriptIncludesLoadFailed:function(d,c,b,a){a.scriptsFailed={src:c.src,multipleCallbacks:b};this._scriptIncludesLoadComplete(d,a)},_scriptsLoadComplete:function(f,c){var e=c.executor;if(window.__theFormPostData)window.__theFormPostData="";if(window.__theFormPostCollection)window.__theFormPostCollection=[];if(window.WebForm_InitCallback)window.WebForm_InitCallback();if(this._scrollPosition){if(window.scrollTo)window.scrollTo(this._scrollPosition.x,this._scrollPosition.y);this._scrollPosition=null}Sys.Application.endCreateComponents();this._pageLoaded(false,c);this._endPostBack(null,e,c);if(this._controlIDToFocus){var a,d;if(Sys.Browser.agent===Sys.Browser.InternetExplorer){var b=$get(this._controlIDToFocus);a=b;if(b&&!WebForm_CanFocus(b))a=WebForm_FindFirstFocusableChild(b);if(a&&typeof a.contentEditable!=="undefined"){d=a.contentEditable;a.contentEditable=false}else a=null}WebForm_AutoFocus(this._controlIDToFocus);if(a)a.contentEditable=d;this._controlIDToFocus=null}},_splitNodeIntoArray:function(b){var a=b.content,c=a.length?a.split(","):[];return c},_uniqueIDToClientID:function(a){return a.replace(/\$/g,"_")},_updateControls:function(d,a,c,b){this._commitControls(this._processUpdatePanelArrays(d,a,c),b)},_updatePanel:function(updatePanelElement,rendering){for(var updatePanelID in this._scriptDisposes)if(this._elementContains(updatePanelElement,document.getElementById(updatePanelID))){var disposeScripts=this._scriptDisposes[updatePanelID];for(var i=0,l=disposeScripts.length;i<l;i++)eval(disposeScripts[i]);delete this._scriptDisposes[updatePanelID]}this._destroyTree(updatePanelElement);updatePanelElement.innerHTML=rendering},_validPosition:function(a){return typeof a!=="undefined"&&a!==null&&a!==0}};Sys.WebForms.PageRequestManager.getInstance=function(){var a=Sys.WebForms.PageRequestManager._instance;if(!a)a=Sys.WebForms.PageRequestManager._instance=new Sys.WebForms.PageRequestManager;return a};Sys.WebForms.PageRequestManager._addArrayElement=function(a){if(!window[a])window[a]=[];for(var b=1,c=arguments.length;b<c;b++)Array.add(window[a],arguments[b])};Sys.WebForms.PageRequestManager._initialize=function(a,b){Sys.WebForms.PageRequestManager.getInstance()._initializeInternal(a,b)};Sys.WebForms.PageRequestManager.registerClass("Sys.WebForms.PageRequestManager");Sys.UI._UpdateProgress=function(a){Sys.UI._UpdateProgress.initializeBase(this,[a]);this._displayAfter=500;this._dynamicLayout=true;this._associatedUpdatePanelId=null;this._beginRequestHandlerDelegate=null;this._startDelegate=null;this._endRequestHandlerDelegate=null;this._pageRequestManager=null;this._timerCookie=null};Sys.UI._UpdateProgress.prototype={get_displayAfter:function(){return this._displayAfter},set_displayAfter:function(a){this._displayAfter=a},get_dynamicLayout:function(){return this._dynamicLayout},set_dynamicLayout:function(a){this._dynamicLayout=a},get_associatedUpdatePanelId:function(){return this._associatedUpdatePanelId},set_associatedUpdatePanelId:function(a){this._associatedUpdatePanelId=a},_clearTimeout:function(){if(this._timerCookie){window.clearTimeout(this._timerCookie);this._timerCookie=null}},_handleBeginRequest:function(d,c){var a=c.get_postBackElement(),b=!this._associatedUpdatePanelId;while(!b&&a){if(a.id&&this._associatedUpdatePanelId===a.id)b=true;a=a.parentNode}if(b)this._timerCookie=window.setTimeout(this._startDelegate,this._displayAfter)},_startRequest:function(){if(this._pageRequestManager.get_isInAsyncPostBack())if(this._dynamicLayout)this.get_element().style.display="block";else this.get_element().style.visibility="visible";this._timerCookie=null},_handleEndRequest:function(){if(this._dynamicLayout)this.get_element().style.display="none";else this.get_element().style.visibility="hidden";this._clearTimeout()},dispose:function(){if(this._beginRequestHandlerDelegate!==null){this._pageRequestManager.remove_beginRequest(this._beginRequestHandlerDelegate);this._pageRequestManager.remove_endRequest(this._endRequestHandlerDelegate);this._beginRequestHandlerDelegate=null;this._endRequestHandlerDelegate=null}this._clearTimeout();Sys.UI._UpdateProgress.callBaseMethod(this,"dispose")},initialize:function(){Sys.UI._UpdateProgress.callBaseMethod(this,"initialize");this._beginRequestHandlerDelegate=Function.createDelegate(this,this._handleBeginRequest);this._endRequestHandlerDelegate=Function.createDelegate(this,this._handleEndRequest);this._startDelegate=Function.createDelegate(this,this._startRequest);if(Sys.WebForms&&Sys.WebForms.PageRequestManager)this._pageRequestManager=Sys.WebForms.PageRequestManager.getInstance();if(this._pageRequestManager!==null){this._pageRequestManager.add_beginRequest(this._beginRequestHandlerDelegate);this._pageRequestManager.add_endRequest(this._endRequestHandlerDelegate)}}};Sys.UI._UpdateProgress.registerClass("Sys.UI._UpdateProgress",Sys.UI.Control);
Type.registerNamespace('Sys.WebForms');Sys.WebForms.Res={"PRM_UnknownToken":"未知标记:“{0}”。","PRM_MissingPanel":"找不到 ID 为“{0}”的 UpdatePanel。如果要动态更新该  UpdatePanel，则它必须位于另一个 UpdatePanel 内部。","PRM_ServerError":"在服务器上处理请求时出现未知错误。服务器返回的状态码为: {0}","PRM_ParserError":"无法分析从服务器收到的消息。之所以出现此错误，常见的原因是: 在通过调用 Response.Write() 修改响应时，将启用响应筛选器、HttpModule 或服务器跟踪。\r\n详细信息: {0}","PRM_TimeoutError":"服务器请求超时。","PRM_ParserErrorDetails":"分析附近的“{0}”时出错。","PRM_CannotRegisterTwice":"不能初始化 PageRequestManager 多次。"};
if(typeof(Sys)!=='undefined')Sys.Application.notifyScriptLoaded();