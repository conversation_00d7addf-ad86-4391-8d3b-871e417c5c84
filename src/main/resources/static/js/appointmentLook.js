/**
 * Created by Administrator on 2018/2/7.
 */
$.ajax({
    type: "POST",
    data: { sessionId: $("#sessionId").val(),projectId:$("#projectId").val(),type:$("#type").val() },
    url:"/checkGuideState",
    dataType: "json",
    success: function (data) {
        if(data.content==1){
            $("#appointmentLook").hide();
            $("#orderGuide").show();
        }
    }
})
$("#appointmentLook").click(function(){
    $.ajax({
        type: "POST",
        url: "/orderGuide",
        data: { sessionId: $("#sessionId").val(),projectId:$("#projectId").val(),type:$("#type").val() },
        dataType: "json",
        success: function (data) {
            if(data.status == 1){
                $("#appointmentLook").hide();
                $("#orderGuide").show();
                alert("预约成功");
            }else{
                alert(data.msg);
                window.location.reload();
            }
        }
    });
})
$("#orderGuide").click(function(){
    $.ajax({
        type: "POST",
        url: "/cancelGuide",
        data: { sessionId: $("#sessionId").val(),projectId:$("#projectId").val(),type:$("#type").val() },
        dataType: "json",
        success: function (data) {
            if(data.status == 1){
                $("#appointmentLook").show();
                $("#orderGuide").hide();
                alert("取消成功");
            }else{
                alert("取消失败");
                window.location.reload();
            }
        }
    });
})
