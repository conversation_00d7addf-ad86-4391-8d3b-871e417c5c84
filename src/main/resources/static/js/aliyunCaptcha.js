// 阿里云验证码2.0配置
window.AliyunCaptchaConfig = {
    // 必填，验证码示例所属地区，支持中国内地（cn）、新加坡（sgp）
    region: "cn",
    // 必填，身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
    prefix: "16kesb", // 请替换为您的实际身份标
};

// 动态创建并加载阿里云验证码2.0主JS
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// 加载阿里云验证码主JS
loadScript('https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js')
    .then(() => {
        console.log('阿里云验证码2.0主JS加载成功');
    })
    .catch((error) => {
        console.error('阿里云验证码2.0主JS加载失败:', error);
    });






// 全局验证码实例
let captchaInstance = null;

// 阿里云验证码2.0配置
const CAPTCHA_CONFIG = {
    sceneId: '1d08twtn', // 场景ID，使用时需替换为实际场景ID
    mode: 'embed', // 验证码模式，动态设置
    element: '#captchaContainer', // 渲染验证码的元素
    button: '#captchaBtn', // 弹出式模式的触发按钮
};

const headUrl = 'https://gateway.fangxiaoer.cn'

// 后端API配置
const API_CONFIG = {
    // baseUrl: 'http://********:8000/services/sms-service', // 短信微服务地址
    baseUrl: 'https://gateway.fangxiaoer.cn/services/sms-service', // 短信微服务地址
    endpoints: {
        sendSms: '/api/v1/public/sms/code',
        verifySms: '/api/v1/public/sms/code/verification',
        verifyCaptcha: '/api/v1/public/captcha/verify'
    },

    // API签名配置
    signature: {
        enabled: true, // 是否启用签名验证
        // secret: '123456' // 签名密钥，需与后端保持一致
        secret: 'c56A26Qtr4' // 签名密钥，需与后端保持一致
    }
};

// // API签名工具类
// class SignatureUtils {
//     // 生成随机Nonce
//     static generateNonce() {
//         return Math.random().toString(36).substring(2, 15) +
//             Math.random().toString(36).substring(2, 15) +
//             Date.now().toString(36);
//     }

//     // 生成时间戳（秒级）
//     static generateTimestamp() {
//         return Math.floor(Date.now()).toString();
//     }

//     // HMAC-SHA256签名（使用Web Crypto API，返回 base64）
//     static async generateSignature(data, secret) {
//         try {
//             const encoder = new TextEncoder();
//             const keyData = encoder.encode(secret);  // 密钥
//             const messageData = encoder.encode(data); // 原文数据

//             // 导入为 HMAC 密钥
//             const cryptoKey = await crypto.subtle.importKey(
//                 'raw',
//                 keyData,
//                 { name: 'HMAC', hash: 'SHA-256' },
//                 false,
//                 ['sign']
//             );

//             // 执行 HMAC-SHA256 签名
//             const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);

//             // 将 ArrayBuffer 转成 Uint8Array，再转成 base64
//             const base64Signature = btoa(
//                 String.fromCharCode(...new Uint8Array(signature))
//             );

//             return base64Signature;
//         } catch (error) {
//             console.error('签名生成失败:', error);
//             throw new Error('签名生成失败: ' + error.message);
//         }
//     }

//     // 构建签名数据字符串
//     static buildSignatureData(method, path, query, body, timestamp, nonce) {
//         return method + "\n" +
//             path + "\n" +
//             (query || "") + "\n" +
//             (body || "") + "\n" +
//             timestamp + "\n" +
//             nonce;
//     }
// }

// // 构建带签名的请求头
// async function buildSignedHeaders(method, url, body = null) {
//     if (!API_CONFIG.signature.enabled) {
//         console.log('签名验证已禁用，使用普通请求');
//         return {
//             'Content-Type': 'application/json'
//         };
//     }

//     console.log('开始构建API签名...');

//     try {
//         const urlObj = new URL(url);
//         const path = urlObj.pathname;
//         const query = urlObj.search ? urlObj.search.substring(1) : '';
//         const timestamp = SignatureUtils.generateTimestamp();
//         const nonce = SignatureUtils.generateNonce();

//         // 处理请求体
//         let bodyStr = '';
//         if (body) {
//             if (typeof body === 'string') {
//                 try {
//                     // 确保JSON格式标准化
//                     const jsonObj = JSON.parse(body);
//                     bodyStr = JSON.stringify(jsonObj);
//                 } catch {
//                     bodyStr = body;
//                 }
//             } else {
//                 bodyStr = JSON.stringify(body);
//             }
//         }

//         // 构建签名数据
//         const dataToSign = SignatureUtils.buildSignatureData(
//             method.toUpperCase(),
//             path.replace('/services/sms-service', ''),
//             query,
//             bodyStr,
//             timestamp,
//             nonce
//         );

//         console.log('签名数据字符串:', dataToSign);

//         // 生成签名
//         const signature = await SignatureUtils.generateSignature(dataToSign, API_CONFIG.signature.secret);

//         console.log('生成的签名:', signature);
//         console.log('签名头信息:', {
//             'X-Signature': signature,
//             'X-Timestamp': timestamp,
//             'X-Nonce': nonce
//         });

//         return {
//             'Content-Type': 'application/json',
//             'X-Signature': signature,
//             'X-Timestamp': timestamp,
//             'X-Nonce': nonce
//         };
//     } catch (error) {
//         console.error('构建签名失败:', error);
//         throw new Error('签名构建失败: ' + error.message);
//     }
// }

// 初始化阿里云验证码2.0
function initAliCaptcha(onSuccessCallback) {
    return new Promise((resolve, reject) => {
        const currentMode = 'popup'; // 设置为弹窗模式
        console.log(`开始初始化阿里云验证码2.0（${currentMode}模式）...`);

        // 确保阿里云验证码2.0 SDK已加载
        if (typeof window.initAliyunCaptcha === 'undefined') {
            reject(new Error('阿里云验证码2.0 SDK未加载'));
            return;
        }

        // 清空容器内容
        const container = document.getElementById('captchaContainer');
        container.innerHTML = '';

        try {
            // 基础配置
            const initConfig = {
                // 场景ID，替换成实际的场景ID
                SceneId: CAPTCHA_CONFIG.sceneId,
                // 弹窗模式
                mode: currentMode,
                // 页面上预留的渲染验证码的元素
                element: CAPTCHA_CONFIG.element,
                captchaVerifyCallback: captchaVerifyCallback, // 验证成功回调
                onBizResultCallback: function (bizResult) {
                    return onBizResultCallback(bizResult, onSuccessCallback);
                }, // 业务结果回调
                getInstance: function (instance) {
                    captchaInstance = instance;
                    console.log(`验证码实例初始化成功（${currentMode}模式）`);
                    resolve({
                        success: true,
                        message: `验证码初始化成功（${currentMode}模式）`
                    });
                },
                slideStyle: {
                    width: 320,
                    height: 40,
                },
                language: 'cn', // 设置语言为中文
                onError: function (error) {
                    console.error('验证码初始化错误:', error);
                    reject({
                        success: false,
                        error: '验证码初始化失败: ' + (error.message || 'Unknown error'),
                        detail: error
                    });
                }
            };

            // 弹出式模式需要指定触发按钮
            if (currentMode === 'popup') {
                // 为弹出式模式创建专用的触发按钮
                const popupTriggerBtn = document.createElement('button');
                popupTriggerBtn.type = 'button';
                popupTriggerBtn.id = 'popupTriggerBtn';
                popupTriggerBtn.className = 'captcha-trigger';
                popupTriggerBtn.textContent = '触发验证码弹窗';
                container.appendChild(popupTriggerBtn);

                initConfig.button = '#popupTriggerBtn';
                console.log('弹出式模式，触发按钮:', initConfig.button);
            }

            // 按照官方V2架构初始化验证码
            window.initAliyunCaptcha(initConfig);
        } catch (error) {
            console.error('验证码初始化异常:', error);
            reject({
                success: false,
                error: '验证码初始化异常: ' + error.message,
                detail: error
            });
        }
    });
}

// 业务请求(带验证码校验)回调函数 - 官方V2架构核心函数
async function captchaVerifyCallback(captchaVerifyParam) {
    console.log('=== 开始V2架构验证流程 ===');
    console.log('收到验证码参数:', captchaVerifyParam);

    try {
        // 调用后端验证接口
        const result = await verifyCaptchaWithBackend(captchaVerifyParam);
        console.log('后端验证结果:', result);

        // 构造标准返回参数 (按官方文档要求)
        const verifyResult = {
            // 验证码验证是否通过，boolean类型，必选
            captchaResult: result.success,
            // 业务验证是否通过，boolean类型，可选
            bizResult: result.success
        };

        console.log('返回验证结果:', verifyResult);
        return verifyResult;

    } catch (error) {
        console.error('验证过程出错:', error);

        // 返回失败结果
        return {
            captchaResult: false,
            bizResult: false
        };
    }
}

// 业务请求验证结果回调函数
function onBizResultCallback(bizResult, onSuccessCallback) {
    console.log('=== V2架构业务结果回调 ===');
    console.log('业务验证结果:', bizResult);

    if (bizResult === true) {
        console.log('验证成功！');

        // 如果提供了成功回调函数，则调用它
        if (typeof onSuccessCallback === 'function') {
            onSuccessCallback();
        }
    } else {
        console.log('验证失败！');
        // 这里可以添加验证失败的处理逻辑
    }
}

// 调用后端验证码验证接口
async function verifyCaptchaWithBackend(captchaVerifyParam) {
    console.log('调用后端验证码验证API...');
    console.log('API地址:', `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.verifyCaptcha}`);
    console.log('验证参数:', captchaVerifyParam);

    try {
            const url = `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.verifyCaptcha}?captchaVerifyParam=${encodeURIComponent(captchaVerifyParam)}`;
            const header = `${headUrl}${API_CONFIG.endpoints.verifyCaptcha}?captchaVerifyParam=${encodeURIComponent(captchaVerifyParam)}`;

            const urlObj = new URL(header);
            const path = urlObj.pathname;
            const query = urlObj.search ? urlObj.search.substring(1) : '';

            const headers = await buildSignedHeaders('GET', path, query);

        const response = await fetch(url, {
            method: 'GET',
            headers: headers
        });

        console.log('后端验证API响应状态:', response.status);

        if (response.ok) {
            console.log('后端验证通过');
            return { success: true, message: '后端验证通过' };
        } else {
            try {
                const data = await response.json();
                console.error('后端验证API返回错误:', data);
                return { success: false, message: data.detail || '验证失败' };
            } catch {
                const text = await response.text();
                console.error('后端验证API返回错误文本:', text);
                return { success: false, message: text || '验证失败' };
            }
        }
    } catch (error) {
        console.error('后端验证API调用失败:', error);
        throw error;
    }
} 