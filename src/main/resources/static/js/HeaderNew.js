/**
 * Created by zz on 2017/1/4.
 */
$(function () {
    $(".nav_one").children().each(function (i, _i) {
        if ($(_i).attr("class") == "active") {
            $(".nav_hover").show();
        }
    })
    $(".search_input").bind({
        focus: function () {
            $(this).next().hide();
        },
        blur: function () {
            if ($(this).val() == "") {
                $(this).next().show();
            }
        }
    })
    $(".header_tel").bind({
        mouseover: function () {
            if (!$(this).is(":animated")) {
                $(this).animate({ height: "88px" }, 300);
            }
        },
        mouseleave: function () {
            $(this).animate({ height: "30px" }, 100);
        }
    })
    $(".nav_one").mouseenter(function () {
        var t1;
        $(".nav_one a").mouseover(function () {
            var nav_one_active = 0;
            var t2 = $(this);
            t1 = setTimeout(function () {
                $(".nav_one li").each(function () {
                    if ($(t2).attr("class") == "active") {
                        nav_one_active = 1
                    }
                })
                //////////这里这里这里！！！
                if (nav_one_active == "0") {
                    if ($(t2).attr("id") == "shouyeli") {
                        var ind = $(".active").index();
                        $(".nav_one").find("li").attr("class", "");
                        $(".nav_float").children().each(function () {
                            $(this).find("ul").css("display", "none");
                        })
                        $(".nav_float").find("ul").eq(ind - 1).css("display", "block");
                        $(".nav_one").find("li").eq(ind).attr("class", "active");
                        return;
                    }
                }
                $(t2).parent().index()
                $(".nav_hover ul").hide();
                if ($(t2).parent().index() == 0) {
                    var shownav = $(".nav_one .active").find("a").attr("class");
                    $(".nav_one li").removeClass("a_hover");
                    if (shownav != undefined) {
                        shownav = shownav.substr(5);
                        $(".nav_hover").show();
                        $("#" + shownav).show();
                    }
                } else {
                    var shownav = $(t2).attr("class");
                    $(".nav_one li").removeClass("a_hover");
                    if ($(t2).parent().hasClass("active") == false) {
                        $(t2).parent().addClass("a_hover");
                        if (shownav != "") {
                            shownav = shownav.substr(5);
                            $(".nav_hover").show();
                            $("#" + shownav).show();
                        }
                    } else {
                        if (shownav != "") {
                            shownav = shownav.substr(5);
                            $(".nav_hover").show();
                            $("#" + shownav).show();
                        }
                    }
                }
            }, 200)
        })

        $(".nav_one a").mouseleave(function () {
            clearTimeout(t1);

        })
        $("#house1").mouseleave(function () {
            clearTimeout(t1);
            $("#house").css("display", "block");
        })
    })



    $(".nav_show").mouseleave(function () {
        var kaiguan = 0;
        $(".nav_one").children().each(function (i, _i) {
            if ($(_i).attr("class") == "active") {
                kaiguan = 1
            }
        })
        if (kaiguan != 1) {
            $(".nav_hover").hide();
        }
        $(".nav_hover ul").hide();
        $(".nav_one").find("li").removeClass("a_hover");
        var active = $(".nav_one .active a").attr("class");
        if (active != "") {
            if (active != undefined) {
                active = active.substr(5);
            }
            $("#" + active).show()
        } else {
            $(".nav_hover").hide()
        }
    })
    $(".nav_one li").mouseleave(function () {
        var classname = $(this).find("a").attr("class")
        var active = $(".nav_one .active a").attr("class");
        if (classname == "") {
            $(this).removeClass("a_hover");
            if (active != undefined) {
                $(".nav_hover").show();
                active = active.substr(5);
                $("#" + active).show()
            }
        }
    })
    function changeauto(method) {
        $(".search_input").autocomplete('/Action/PageHandler.ashx?method=' + method + '&city=1', {
            multiple: false,
            scrollHeight: 300,    // 下拉框的高度， Default: 180
            max: 20,
            parse: function (data) {
                //alert(data);
                return $.map(eval(data), function (row) {
                    return {
                        data: row,
                        value: row.CompanyName,
                        result: row.CompanyName
                    };
                });
            },
            formatItem: function (item) {
                var txt = $("#chid").val();
                switch (txt) {
                    case "5":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到关键字请重新输入条件！</i>";
                        } else {
                            return "<i style='float:right'></i>" + item.CompanyName;
                        }
                        break;
                    case "9":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            return item.CompanyName;
                        }
                        break;
                    default:
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            return "<i style='float:right'>" + item.Address + " [ 直达 ]</i>" + item.CompanyName;
                        }
                        break;
                }
            }
        }).result(function (e, item) {
            if (item.Telephone == 1) {
                $("#chid").val("1");
                if (item.isvip) {
                    window.location.href = "/dealer/house/view/" + item.ID;
                } else {
                    window.location.href = "/house/view/" + item.ID;
                }
            } else if (item.Telephone == 2) {
                $("#chid").val("2");
                window.location.href = "/villa/view/" + item.ID;
            } else if (item.Telephone == 3) {
                $("#chid").val("3");
                window.location.href = "/office/view/" + item.ID;
            } else if (item.Telephone == 5) {
                $("#chid").val("5");
                window.location.href = "/news/view/" + item.ID;
            }
            else if (item.Telephone == 6) {//二手房小区
                window.location.href = "/UHouse/Subdistrict/view/" + item.ID;
            }
            else if (item.Telephone == 9) {//全网找房
                window.location.href = "/UHouse/sale/dashuju/1_全部__-1_-1_-1_0_0_0_0_0_0_" + item.CompanyName + "_1";
            }
            else if (item.Telephone == 10) {
                window.location.href = "/uhouse/shangpu/1_0_0_0_1_0_0_0_0_0_0_" + input + "_1";
            }

            else {
                $(".ac_input").val(item.CompanyName);
                $("#chid").val("4");
            }

        });
    };

    $(".ac_input").bind("click", function () {
        var txt = $("#chid").val();
        switch (txt) {
            case "5":
                changeauto("getxinwen");
                break;
            case "7":
                changeauto("getsub");
                break;
            case "8":
                changeauto("getsub");
                break;
            case "9":
                changeauto("getsalefullsub");
                break;
            case "10":
                break;
            default:
                changeauto("get1");
                break;
        }
    });

    $(".search_btn").click(function () {
        var txt = $("#chid").val();
        input = $(this).prev().find("input").val(); //取输入框值
        input=stripscript(input)
        if (input != "") {
            switch (txt) {
                case "1":
                    url = "/house/1_0_-1_-1_-1_-1_0_0_0_0_0_" + input + "_1";
                    break;
                case "3":
                    url = "/office/1_0_0_0_0_0_" + input + "_1";
                    break;
                case "4":
                    url = "/house/search.aspx?keys=" + input;
                    break;
                case "5":
                    url = "/news/default.aspx?keys=" + input;
                    break;
                case "7":
                    url = "/UHouse/Sale/Region/1_-1_-1_-1_0_-1_-1_0_0_0_-1_" + input + "_1_-1_-1_-1_-1_-1_-1_-1"
                    break;
                case "8":
                    url = "/UHouse/rent/RentRegion/1_-1_-1_-1_0_-1_-1_0_0_0_-1_" + input + "_1"
                    break;
                case "9":
                    url = "/UHouse/sale/dashuju/1_全部__-1_-1_-1_0_0_0_0_0_0_" + input + "_1";
                    break;
                case "10":
                    url = "/uhouse/shangpu/1_0_0_0_1_0_0_0_0_0_0_"+input+"_1";
                    break;
            }
            window.location.href = url;
        }; //判断结束
    });

    function stripscript(s)
    {
        var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
        var rs = "";
        for (var i = 0; i < s.length; i++) {
            rs = rs+s.substr(i, 1).replace(pattern, '');
        }
        return rs;
    }

    var keys = '';
    if (keys != "" && keys != "null" && keys != null && keys != "0") {
        if (keys != -1) {
            $("#txtkeys").val(keys);
        }
    }
    $(".ac_input").each(function () {
        var thisVal = $(".ac_input").val();    //判断文本框的值是否为空，有值的情况就隐藏提示语，没有值就显示
        if (thisVal != "") { $(".ac_input").siblings("span").hide(); }
        else { $(".ac_input").siblings("span").show(); }
        $(".ac_input").keyup(function () {
            $(".ac_input").siblings("span").hide();
        }).blur(function () {
            var val = $(this).val();
            if (val != "") {
                $(this).siblings("span").hide();
            } else {
                $(this).siblings("span").show();
            }
        });
    });
});

$(document).keydown(function (event) {
    if (event.keyCode == 13) {
        $(".search_btn").click();
        var type = $("#chid").val();

        switch (type) {
            case "1":
                url = "/house/search.aspx?keys=" + keys;
                break;
            case "3":
                url = "/office/1_0_0_0_0_0_" + keys + "_1";
                break;
            case "5":
                url = "/news/default.aspx?keys=" + input;
                break;
            case "9":
                url = "/UHouse/sale/dashuju/1_全部__-1_-1_-1_0_0_0_0_0_0_" + input + "_1";
                break;
            case "10":
                url = "/uhouse/shangpu/1_0_0_0_1_0_0_0_0_0_0_" + keys + "_1";
                break;

        } window.location.href = url;
    }
    $(".nav_one").children().bind("click", function () {
        $(this).addclass("")
    })
});



//弹出
$(function(){
    top();
    $(window).scroll(function () {
        top();
    });
    function top() {
        if ($(".nav_one .active a").html() == "资讯") {
            $(".top_move_act").stop()
            if ($(document).scrollTop() > 157) {
                $(".top_move_act").addClass("topMove_head")
                $(".top_move_act").animate({ "top": "0px" }, 100)
            } else {
                $(".top_move_act").animate({ "top": "-42px" }, 100)
                $(".top_move_act").removeClass("topMove_head")
            }
        }
    }
});
