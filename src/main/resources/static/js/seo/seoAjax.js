$(".index_search_btn_test").click(function () {
    var txt = $(this).prev().attr("dir");
    var input = $(this).prev().val(); //取输入框值
    input=stripscript(input);
    if(txt=="1" && input==""){
        window.open("https://sy.fangxiaoer.com/dealer/house/view/268")
    }
    if(!input=="") {
        var r;
        $.ajax({
            type: "POST",
            data: {key: txt, search: input},
            url: "/seoNewhouse",
            async: false,
            success: function (data) {
                window.location.href = "/seohouse";
                loadlistdata(data, "0")
            }
        });
    }
});

function loadlistdata(data, proty) {
    //var data = JSON.parse(data);

    alert(data.content.length);
    $(".title span").html(data.content.length);
    console.warn(data.count);
    data = data.list;
    if (data.length > 0) {
        for (var i = 0; i < data.length; i++) {
            var cl = $('<div class="cl"></div>');
            var zhuzhai = $('<a href="/fang2/' + data[i].houseId + '.htm" class="zhuzhai"></a>');
            var left_tu = "";
            if (data[i].pic != "") {
                left_tu = $('<img src="' + data[i].pic + '" class="left_tu"/>')
            } else {
                left_tu = $('<img src="https://static.fangxiaoer.com/m/images/sload.jpg" class="left_tu"/>')
            }


            var right_info = $('<div class="right_info"></div>');

            switch (proty) {
                case "subway": var title = $('<div class="title">' + data[i].projectName + '</div>'); break;
                default: var title = $('<div class="title">' + data[i].title + '</div>');
            }
            //var dist_price = $('<div class="dist_price"></div>');
            var dist = "";
            if (data[i].distance != "") {
                var dist = $('<div class="dist"><img src="https://static.fangxiaoer.com/m/images/distance.png"/>' + data[i].distance + 'km</div>');
            }
            //var price = $('<div class="price"><span>' + data[i].price + '</span>万</div>')

            //dist_price.append(price,dist);

            usprice=parseFloat(data[i].price);

            var favourable = $('<div class="address"><div class="address_left">' + data[i].regionName + '&nbsp;' + data[i].subName + '&nbsp;' + data[i].room + '室' + data[i].hall + '厅</div><div class="price"><span>' + usprice + '</span>万</div></div>');

            var spanTime=data[i].spanTime;
            //alert(isNaN(spanTime));
            if(parseInt(spanTime)>90){
                spanTime="";
            }else{
                spanTime;
            }
            var charact = $('<ul class="charact"><i>' + spanTime + '</i></ul>');

            var charact_dist=$('<div class="charact_dist"></div>')
            var houseTrait = data[i].houseTrait;
            houseTrait = houseTrait.split(",");
            for (var j = 0; j < houseTrait.length; j++) {
                if (houseTrait[j] != ""&&houseTrait[j] !=null) {
                    charact.append($('<li class="em' +[j+1]+ ' ">' + houseTrait[j] + '</li>'));
                } else {
                    continue;
                }
            }
            //charact_dist.append(charact, dist)
            charact_dist.append(charact)
            right_info.append(title, favourable, charact_dist, cl);

            zhuzhai.append(left_tu, right_info, cl);
            $(".list").append(zhuzhai);
            status = "true";
            $(".loading").hide();
        }
    } else {
        if ($(".zhuzhai").length == 0) {
            $(".list").append('<div class="no_info"><img src="https://static.fangxiaoer.com/m/images/no_info.png"><br>没有找到您想要的数据</div></div>');

        }
        status = "false";
        $(".loading").hide();
        return;
    }
}