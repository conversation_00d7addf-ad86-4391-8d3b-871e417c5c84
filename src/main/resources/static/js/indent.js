// var host = "http://16408f55n6.imwork.net:13765";
// var host = "http://roysms.f3322.net:8080";
var host = "https://ltapi.fangxiaoer.com";
//支付状态的载体
var payment;
var payMoney = 10;
var sessionId = "";
var loginType;
var pay = {
    //初始化
    init: function () {
        //金额
        $.ajax({
            type: "POST",
            url: host + "/apiv1/other/getSecondHandHousePayMoney",
            async: false,
            success: function (data) {
                data = data.content
                for (var i = 0; i < data.length; i++) {
                    if (data[i].id == $("#houseType").val()) {
                        $(".page2>span").eq(0).text("支付金额：" + data[i].name + "元");
                        payMoney = data[i].name;
                    }
                }
            }
        });
        $.ajax({
            type: "POST",
            url: host + "/apiv1/other/getHousePayMoney",
            data: { "houseType": $("#houseType").val() },
            async: false,
            success: function (data) {
                if (data.status == 1) {
                    $(".page2 li span").text(data.content.describe)
                }
            }
        });
        //使用微信支付
        $(".weixin").click(function () {
            $(".alipay").removeClass("hover")
            $(this).addClass("hover")
        })
        //使用支付宝支付
        $(".alipay").click(function () {
            $(".weixin").removeClass("hover")
            $(this).addClass("hover")
        })
        //获取验证码
        $(".fxe_ReSendValidateCoad").click(function () {
            if (pay.phone($("#phone").val()) != "1") {
                fxe_alert(pay.phone($("#phone").val()))
            } else {
                pay.code($("#phone").val());
            };
        })
        //注册用户使用密码
        /*
        $("#phone").blur(function () {
            if ($(this).val().length == 11) {
                pay.user($("#phone").val())
            }
        });*/
        //填写信息
        $(".page1>a").click(function () {
            if ($("#name").val() == "") {
                fxe_alert("请填写姓名");
            } else if (pay.phone($("#phone").val()) != "1") {
                fxe_alert(pay.phone($("#phone").val()))
            } else if ($(".yzm").css("display") == "none") {
                if ($("#pwd").val() == "") {
                    fxe_alert("请输入密码");
                } else {
                    pay.next();
                }
            } else {
                if ($("#code").val() == "") {
                    fxe_alert("请输入验证码/密码")
                } else {
                    pay.next();
                }
            }
        })
        //去支付
        $(".page2>a").click(function () {
            if ($(".weixin").hasClass("hover")) {
                pay.weixinPay()
            } else if ($(".alipay").hasClass("hover")) {
                pay.aliPay()
            } else {
                fxe_alert("请选择支付方式")
            }
        })
        //关闭
        $(".x,.x1").click(function () {
            $(".saleHouseIndent>div").hide()
            $(".yuyue>div").hide()
            $(".page1").show()
            $(".saleHouseIndent").hide()
            $(".yuyue").hide()
            pay.clear()
        })
        $(".djyh").click(function () {
            $(".saleHouseIndent>div").hide()
            if (islogin != "") {
                $(".page2").show();
            } else {
                $(".page1").show();
            }

            $(".saleHouseIndent").show()

        })
    },
    //重置
    clear: function () {
        $("#phone").val("")
        $("#code").val("")
        $("#pwd").val("")
        $("#name").val("")
    },
    //下一步
    next: function () {
        $(".saleHouseIndent>div").hide()
        $(".page2").show()
    },
    //手机号格式是否正确  正确返回1  错误返回对应信息
    phone: function (phone) {
        var reg = /^1[0-9]{10}$/;
        if (phone == "") {
            return "请输入电话号码";
        } else if (!reg.test(phone)) {
            return "电话号码格式不正确";
        } else {
            return "1";
        }
    },
    //验证码倒计时
    wait: 60,//倒计时
    timeWait: function () {//倒计时函数
        pay.wait = 60;
        time();
        function time() {
            if (pay.wait == 0) {
                $(".fxe_validateCode").hide();
                $(".fxe_ReSendValidateCoad").show().html("重新获取");
                pay.wait = 60;
            } else {
                $(".fxe_ReSendValidateCoad").hide();
                $(".fxe_validateCode").show().html("在" + pay.wait + "秒后重发");
                pay.wait--;
                setTimeout(function () {
                    time();
                },
                    1000);
            }
        }
    },
    //获取验证码
    code: function (phone) {//获取验证码  获取成功返回1
        try {
            var r = 0;
            $.ajax({
                type: "POST",
                data: { mobile: phone },
                url: "/sendSmsCode",
                async: false,
                success: function (data) {
                    if (data.status == 0) {
                        fxe_alert(data.msg)
                    } else if (data.status == 1) {
                        pay.timeWait();
                        r = data.status;
                    }
                }
            });
        } catch (e) {
            console.log(e.message);
        }
        return r;
    },
    weixinPay: function () {//微信支付
        var pwd = $("#userType").val() == 1 ?  hex_md5($("#pwd").val()) : hex_md5($("#code").val());
        var params =  {
                mobile: $("#phone").val(), //电话
                pwd: pwd, //验证码
                name: $("#name").val(), //姓名
                body: $(".title1").text(),
                payType: "2",
                //	            activityid: pay.activityid,
                houseId: $("#Idhouse").val(),
                password: $("#pwd").val(),
                houseType: $("#houseType").val(),
                sessionId:islogin,
                payMoney: payMoney,
                tradeType:'NATIVE',
                deviceType:'1',
                loginWay:'1'
            };
        $.ajax({
            type: "POST",
            // url: "/Action/ActivityHelp.ashx",/apiv1/other/wxPay
            // url: host+ "/apiv1/other/getSecondHandHousePay",
            url:"/getPayOrder",
            data:JSON.stringify(params),
            async: false,
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            success: function (data) {
                // data = jQuery.parseJSON(data);
                if (data.status == "1") {
                    data = data.content;
                    $(".page3 img").attr("src", host + "/apiv1/other/getDimension?codeUrl=" + data.code_url + "&width=200&height=200");
                    $(".page3 span").eq(0).text("微信")
                    $(".saleHouseIndent>div").hide();
                    $(".page3").show();
                    payment = setInterval("pay.payment('" + data.orderId + "')", 2000);
                    // $.ajax({
                    //     type: "POST",
                    //     url: "/Action/ActivityHelp.ashx",
                    //     //url: host + "/apiv1/other/getPublicPay",
                    //     data: {
                    //         action: "denglu",
                    //         mobile: $("#phone").val(), //电话
                    //     },
                    //     async: false,
                    //     success: function (data) {
                    //         data = jQuery.parseJSON(data);
                    //     }
                    // });
                } else {
                    fxe_alert(data.msg);
                }
            },
            error: function () {
                fxe_alert("获取二维码失败");
            }
        })
    },
    aliPay: function () {//支付宝支付
        var pwd = $("#userType").val() == 1 ?  hex_md5($("#pwd").val()) : hex_md5($("#code").val());
        var data = {
            action: "esfzf",
            mobile: $("#phone").val(), //电话
            pwd: pwd, //验证码
            name: $("#name").val(), //姓名
            body: $(".title1").text(),
            payType: "1",
            //	            activityid: pay.activityid,
            houseId: $("#Idhouse").val(),
            houseType: $("#houseType").val(),
            payMoney: payMoney,
            sessionId:islogin,
            tradeType:'NATIVE',
            deviceType:'1',
            loginWay:'1'

        };
        $.ajax({
            type: "POST",
            // url: "/Action/ActivityHelp.ashx",
            // url: host+"/apiv1/other/getSecondHandHousePay",
            url: "/getPayOrder",
            data:JSON.stringify(data) ,
            async: false,
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            success: function (data) {
                if (data.status == "1") {
                    data = data.content;
                    $(".page3 img").attr("src", host + "/apiv1/other/getDimension?codeUrl=" + data.alipay_trade_precreate_response.qr_code + "&width=200&height=200");
                    $(".page3 span").eq(0).text("支付宝")
                    $(".saleHouseIndent>div").hide()
                    $(".page3").show();
                    payment = setInterval("pay.payment('" + data.orderId + "')", 2000);
                    // $.ajax({
                    //     type: "POST",
                    //     url: "/Action/ActivityHelp.ashx",
                    //     //url: host + "/apiv1/other/getPublicPay",
                    //     data: {
                    //         action: "denglu",
                    //         mobile: $("#phone").val(), //电话
                    //     },
                    //     async: false,
                    //     success: function (data) {
                    //         data = jQuery.parseJSON(data);
                    //     }
                    // });
                } else {
                    fxe_alert(data.msg);
                }
            },
            error: function () {
                fxe_alert("获取二维码失败");
            }
        });
    },
/*    //用户是否注册
    user: function (phone) {
        $.ajax({
            type: "POST",
            url: host + "/apiv1/base/checkIsMember",
            data: { mobile: phone },
            dataType: "json",
            success: function (data) {
                if (data.status == "1") {
                    $(".mm").show();
                    $(".yzm").hide();
                    $("#pwd").focus();
                    $("#code").val("");
                    $(".yzm b").hide();
                    $("#userType").val(1)
                    //return "1";
                } else {
                    $(".yzm").show();
                    $(".mm").hide();
                    $("#code").focus();
                    $("#pwd").val("");
                    $(".fxe_ReSendValidateCoad").show();
                    $("#userType").val(0)
                    //return "0";
                }
            }
        });
        // return t;
    },*/
    //付款状态
    payment: function (PayMent) {//支付状态
        $.ajax({
            type: "POST",
            url: host + "/apiv1/house/viewStickStatus",
            data: {
                orderId: PayMent
            },
            async: false,
            success: function (data) {
                if (data.status == "1") {
                    data = data.content;
                    if (data.status == 1) {
                        //支付成功
                        clearInterval(payment);
                        $(".saleHouseIndent>div").hide();
                        if(islogin == ""){
                            pay.quickLogion();
                        }
                        $(".page4").show();
                    } else if (data.status == "2") {
                        //支付中
                    } else if (data.status == "3") {
                        //已失效
                        clearInterval(payment);
                        $(".page4 img").attr("src", "https://static.fangxiaoer.com/web/images/sy/house/giftIndent/failed.png")
                        $(".saleHouseIndent>div").hide();
                        $(".page4").show()
                    } else if (data.status == "99") {
                        //订单异常
                    }
                }
            }
        });
    },
    quickLogion:function(){
        var pwd = $("#userType").val() == 1 ?  $("#pwd").val() : $("#code").val();
        $.ajax({
            type: "POST",
            url: "/login",
            data: {
                telNumber: $("#phone").val(), //电话
                password: pwd, //电话
                registFromUrl: window.location.href
            },
            async: false,
            success: function (data) {
               if(data.status == 1){
                   loginType = data.status;
               }
            }
        });
    }

}
//弹出
function fxe_alert(text) {
    $("body").append("<div class='fxe-alert'>" + text + "</div>");
    setTimeout("del()", 3000);
}
function del() {
    var time = 300;
    $(".fxe-alert").eq(0).hide(time);
    setTimeout("$('.fxe-alert').eq(0).remove()", time)
}

//支付状态的载体
var pyy = {

    //手机号格式是否正确  正确返回1  错误返回对应信息
    phone: function (phone) {
        var reg = /^1[0-9]{10}$/;
        if (phone == "") {
            return "请输入电话号码";
        } else if (!reg.test(phone)) {
            return "电话号码格式不正确";
        } else {
            return "1";
        }
    },
    //验证码倒计时
    wait: 60,//倒计时
    timeWait: function () {//倒计时函数
        pyy.wait = 60;
        yy_time();
        function yy_time() {
            if (pyy.wait == 0) {
                $(".yy_fxe_validateCode").hide();
                $(".yy_fxe_ReSendValidateCoad").show().html("重新获取");
                pyy.wait = 60;
            } else {
                $(".yy_fxe_validateCode").show().html("在" + pyy.wait + "秒后重发");
                $(".yy_fxe_ReSendValidateCoad").hide();
                pyy.wait--;
                setTimeout(function () {
                    yy_time();
                },
                    1000);
            }
        }
    },
    //获取验证码
    code: function (phone) {//获取验证码  获取成功返回1
        try {
            var r = 0;
            $.ajax({
                type: "POST",
                data: { action: "Send", mobile: phone },
                url: "/sendSmsCode",
                async: false,
                success: function (data) {
                    data = jQuery.parseJSON(data)
                    if (data == -4) {
                        fxe_alert("十分抱歉！您今日的请求次数已达上限，请明日再进行重试。")
                    } else if (data == 1) {
                        pyy.timeWait();
                        r = data;
                    }
                }
            });
        } catch (e) {
            console.log(e.message);
        }
        return r;
    },
    next: function () {
        alert("1")
    },

}
//弹出
function fxe_alert(text) {
    $("body").append("<div class='fxe-alert'>" + text + "</div>");
    setTimeout("del()", 3000);
}
function del() {
    var time = 300;
    $(".fxe-alert").eq(0).hide(time);
    setTimeout("$('.fxe-alert').eq(0).remove()", time)
}

$(function () {
    //获取验证码
    $(".yy_fxe_ReSendValidateCoad").click(function () {
        if (pyy.phone($("#yy_phone").val()) != "1") {
            fxe_alert(pyy.phone($("#yy_phone").val()))
        } else {
            pyy.code($("#yy_phone").val());
        }
    })
    $(".yydk").click(function () {
        $(".yy_page").show()
        $(".yuyue").show()
    })

    //填写信息
    $(".yy_page>a").click(function () {
        if ($("#yy_name").val() == "") {
            fxe_alert("请填写姓名")
        } else if (pyy.phone($("#yy_phone").val()) != "1") {
            fxe_alert(pyy.phone($("#yy_phone").val()))
        } else if ($(".yy_yzm").css("display") == "none") {
            if ($("#yy_pwd").val() == "") {
                fxe_alert("请输入密码")
            } else {
                pyy.next()
            }
        } else {
            if ($("#yy_code").val() == "") {
                fxe_alert("请输入验证码")
            } else {
                pyy.next()
            }
        }
    })
})