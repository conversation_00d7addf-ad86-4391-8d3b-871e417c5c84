var state = false; //全局正确开关
var errcount = 0; //全局错误数
$(function () {
    var OwnerPhone = $("#OwnerPhone").val();

})

//获取验证码
$(function () {
    var rbtn = $("#ReSendValidateCoad");
    //获取验证码按钮事件
    rbtn.click(function () {
        var tmobile = $.trim($("#OwnerPhone").val());
        var reg = new RegExp("^1[0-9]{10}$", "ig");
        if (!reg.test(tmobile) || tmobile == "") {
            alert("请正确填写11位手机号码!");
            return false;
        } else {
            time();
            var r = SendFunc.ajaxSendCode(tmobile); //发送短信验证码
        }
    });
});
//发送验证码
var SendFunc = {
    ajaxSendCode: function (mobile) {
        try {
            var r = 0;
            $.ajax({
                type: "POST",
                data: { action: "Send", mobile: mobile },
                url: "/sendSmsCode",
                async: false,
                success: function (data) {
                    r = data;
                }
            });
        } catch (e) {
            console.log(e.message);
        }
        return r;
    }
};
//验证码倒计时
var wait = 60;
function time(o) {
    if (wait == 0) {
        $("#validateCode").hide();
        $("#ReSendValidateCoad").show().html("重新获取");
        wait = 60;
    } else {
        $("#validateCode").show().html("在" + wait + "秒后重发");
        $("#ReSendValidateCoad").hide();
        wait--;
        setTimeout(function () {
                time(o);
            },
            1000);
    }
}

$(document).ready(function () {
    $(".sjmm").hide();
    IsRegister();
    //划出下拉 关闭下拉
    $(".my_xl").bind("mouseleave",function(){
        $(this).find(".my_xl_list").hide()
    })
});
function IsRegister() {
   /* $("#OwnerPhone").keyup(function () {
        var mobile = $("#OwnerPhone").val();
        if (mobile && mobile.length == 11) {
            $.ajax({
                type: "POST",
                url: "/searchPhone",
                data: { action: "IsRegister", phone: $("#OwnerPhone").val() },
                dataType: "json",
                success: function (data) {
                    if (data.temp == "1") {
                        $(".sjmm").show();
                        $(".sjyzm").hide();
                        $("#ReSendValidateCoad").hide();
                    } else {
                        $(".sjmm").hide();
                        $(".sjyzm").show();
                        $("#ReSendValidateCoad").show();
                    }
                }
            });
        }
    });*/
}