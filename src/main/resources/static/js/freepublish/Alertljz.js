/**
 * Created by Administrator on 2017/9/21.
 */
var Alertljz = {
    BindEvent: function (href) {
        if (href && href != "") {
            document.getElementById("alertHref").href = href;
        }
        else {
            document.getElementById("alertHref").onclick = function () {
                Alertljz.HideAlert();
            };
        }
        document.getElementById("alertClose").onclick = function () {
            Alertljz.HideAlert();
        };
    },
    ShowAlert: function (text, href) {
        if (document.getElementById("aerltAllDom")) {
            document.getElementById("aerltAllDom").style.visibility = "visible";
        }
        else {
            Alertljz.CreateAlert(text, href);
        }
    },
    ShowAlertgl: function (text, href) {
        if (document.getElementById("aerltAllDom")) {
            document.getElementById("aerltAllDom").style.visibility = "visible";
        }
        else {
            Alertljz.CreateAlertgl(text, href);
        }


    },
    HideAlert: function () {
        if (document.getElementById("aerltAllDom")) {
            document.getElementById("aerltAllDom").style.visibility = "hidden";
        }
    },
    CreateAlert: function (text, href) {
        var div = document.createElement("div");
        div.id = "aerltAllDom";
        var alertDom = document.createElement("div");
        alertDom.id = "alertDom";
        alertDom.className = "tanChuWenZi";
        var img = document.createElement("img");
        img.src = "https://static.fangxiaoer.com/web/images/my/admin/yun_close.gif";
        img.id = "alertClose";
        var p = document.createElement("p");
        p.id = "alertText";
        p.innerHTML = text;
        var a = document.createElement("a");
        a.id = "alertHref";
        a.textContent = "确定";
        alertDom.appendChild(img);
        alertDom.appendChild(p);
        alertDom.appendChild(a);
        var alertZhezhao = document.createElement("div");
        alertZhezhao.id = "alertZhezhao";
        alertZhezhao.className = "tanChuBeiJing";
        div.appendChild(alertDom);
        div.appendChild(alertZhezhao);
        document.body.appendChild(div);
        Alertljz.BindEvent(href);
    },
    CreateAlertgl: function (text, href) {
        var div = document.createElement("div");
        div.id = "aerltAllDom";
        var alertDom = document.createElement("div");
        alertDom.id = "alertDom";
        alertDom.className = "tanChuWenZi";
        var img = document.createElement("img");
        img.src = "https://static.fangxiaoer.com/web/images/my/admin/yun_close.gif";
        img.id = "alertClose";
        var p = document.createElement("p");
        p.id = "alertText";
        p.innerHTML = text;
        var a = document.createElement("a");
        a.id = "alertHref";
        a.textContent = "查看";
        alertDom.appendChild(img);
        alertDom.appendChild(p);
        alertDom.appendChild(a);
        var alertZhezhao = document.createElement("div");
        alertZhezhao.id = "alertZhezhao";
        alertZhezhao.className = "tanChuBeiJing";
        div.appendChild(alertDom);
        div.appendChild(alertZhezhao);
        document.body.appendChild(div);
        Alertljz.BindEvent(href);
    }


}