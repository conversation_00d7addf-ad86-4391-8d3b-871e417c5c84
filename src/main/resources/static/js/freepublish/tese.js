/**
 * Created by Administrator on 2017/9/21.
 */
$(function () {
    $(function() {
        var checkBox = $("#HouseTrait").val();
        var checkBoxArray = checkBox.split(",");
        // if(null != checkBoxArray && checkBoxArray.length > 0 ){
        //     $(".fyts a").each(function(){
        //         if(checkBoxArray.length >1){
        //             for ( var i = 0; i < checkBoxArray.length; i++){
        //                 if($(this).val() == checkBoxArray[i] ){
        //                     $(this).addClass("focus");
        //                 }
        //                 continue;
        //             }
        //         }
        //     })
        // }
        for ( var j = 0; j < checkBoxArray.length; j++){
            for (i = 0; i < $(".fyts a").length; i++) {
                var value = $(".fyts a:eq(" + i + ")").attr("rel");
                if(value == checkBoxArray[j]){
                    $(".fyts a:eq(" + i + ")").addClass("focus");
                    break;
                }
            }
        }
    });
    $(".my_xl").each(function () {
        if ($(this).find("input").val() == "0" && $(this).find("li").val() != "0") {
            $(this).find(".my_xl_txt").html("请选择");
            $(this).find("input").val("");

        } else
            for (i = 0; i < $(this).find("li").length; i++) {
                if ($(this).find("li").eq(i).val() == $(this).find("input").val()) {
                    $(this).find(".my_xl_txt").html($(this).find("li").eq(i).html())
                }
            }
    });
    $(".zujin").bind({
        click: function () {
            if ($(this).val() == "面议") {
                $(this).val("");
            }
        },
        blur: function () {
            if ($(this).val() == "") {
                $(this).val("面议");
            }
        }
    });
    $(".sh_btn").click(function () {
        if ($(".showhide").hasClass("hid")) {
            $(".showhide").slideDown();
            $(".showhide").removeClass("hid");
            $(".icon").addClass("bg_ico");
        } else {
            $(".showhide").slideUp();
            $(".showhide").addClass("hid");
            $(".icon").removeClass("bg_ico");
        }
    });
    $('.huxing input').bind('input propertychange', function () {
        if ($(this).val().length >= 1) {
            $(this).parent().next().find("input").focus();
        }
    });
    $(".floor").find("input").keyup(function () {
        if ($(this).val().length == 2) {
            $(this).parent().next().find("input").focus();
        }
    });
    $(".xx li").click(function () {
        $("#enter").show();
    });

    //特色
    $(".fyts a").click(function () {

        $("#fyts_yz").show();
        if ($(this).attr("class") == "focus") {
            $(this).removeClass("focus");
        } else {
            if ($(".fyts a.focus").length >= 5) {
                alert("最多可选5个特色");
            } else {
                $(this).addClass("focus");
            }
        }
    });
    $(".submitBtnFroms").click(function () {
        $("#HouseTrait").val("");
        for (i = 0; i < $(".fyts a").length; i++) {
            if ($(".fyts a:eq(" + i + ")").hasClass("focus") && $("#HouseTrait").val().split(",").length<=6) {
                $("#HouseTrait").val($("#HouseTrait").val() + "," + $(".fyts a:eq(" + i + ")").attr("rel"));
            }
        }
    });
})
