$(function () {
    //验证正确返回 正确状态
    function getSuccess() {
        $(".ljz").hide();
        $("em.success").each(function (index, element) {
            $("#" + $(this).parent().attr("for") + "123").show();
            if ($(this).parent().next("label.error").length > 0)
                $(this).parent().hide()
        });
    }
    getSuccess();
    $("#SalePrice").blur(function(){
        if($("#SalePrice").val()=="面议"){

        }else if($("#SalePrice").val()!=""){
            console.log($("#SalePrice").val().replace(/^\d+(\.\d+)?$/,""))
            if($("#SalePrice").val().replace(/^\d+(\.\d+)?$/,"")!=""){
                $("#SalePrice").val("")
            }else{
                $("#SalePrice").val(parseFloat($("#SalePrice").val()).toFixed(2))
            }
        }
    })
    $("#jsUpForm input").bind("click", function () {
        if($("#RentPrice").val()==""){
            $("#RentPrice").val("面议");
        }
        getSuccess();
    });
    $(".submitBtnFroms").bind("click", function () {
        if(!$("#checkagree").hasClass("checked")){
            // timeout(".checkimg", "请阅读服务条款");
            alert("请仔细阅读并同意服务协议及隐私政策！");
            return false;
        }
        if($("#RentPrice").val()==""){
            $("#RentPrice").val("面议");
        }
        if($("#SalePrice").val()==""){
            $("#SalePrice").val("面议");
        }
        getSuccess();
    });
    function getYear() {//获取当前年份
        var date = new Date;
        var year = date.getFullYear();
        return year;
    }

    $.validator.addMethod("mTmlNum", function (value, element, params) {
        var reg1 = "^1[0-9]{10}$";//手机号码
        if (value != 0 && value.match(reg1))
            return true;
        else
            return false;
    }, "手机号码不正确");

    $.validator.addMethod("noNum0", function (value, element, params) {
        if (value != 0)
            return true;
        else
            return false;
    }, "不能为0或非数字");
    $.validator.addMethod("Describe", function (value, element, params) {
        if (value.length < 5 || value.length > 1000)
            return false;
        else
            return true;
    }, "房源描述至少5字且不能超过1000字");
    $.validator.addMethod("noNum01", function (value, element, params) {
        if (value != 0 || value == "面议" || value=="" || typeof(value)== number)
            return true;
        else
            return false;
    }, "不能为0或非数字");

    $.validator.addMethod("decimal1", function (value, element, params) {
        var decimal1 = /^\d+(\.\d{1,2})?$/;
        if (value == "面议" || value.match(decimal1))
            return true;
        else
            return false;
    }, "不等于0的整数或其他非整数");

    $.validator.addMethod("textInteger", function (value, element, params) {
        var reg1 = "^-?[1-9]\\d*$";//整数
        if (value != 0 && value.match(reg1))
            return true;
        else
            return false;
    }, "不等于0的整数或其他非整数");


    //手机短信验证码
    //$.validator.addMethod("SecurityCode", function (value, element, params) {
    //    var phone = $("#OwnerPhone").val();
    //    var r;
    //    $.ajax({
    //        type: "POST",
    //        data: { action: "CheckSend", mobile: phone, code: value },
    //        url: "/Action/SendsmsHelp.ashx",
    //        async: false,
    //        success: function (data) {
    //            if (data == "1") {
    //                r = true;
    //            }
    //            else {
    //                r = false;
    //            }
    //        }
    //    });

    //}, "验证码不正确！");








    // 验证值小数位数不能超过1位
    $.validator.addMethod("decimal", function (value, element, params) {
        var decimal = /^\d+(\.\d{1,2})?$/;
        return this.optional(element) || (decimal.test(value));
    }, $.validator.format("小数位数不能超过一位!且不能为0"));


    //$(".submitBtnFroms").click(function(){

    $("#jsUpForm").validate({

        success: function (label) {
            label.html("<em class='success'></em>")
        },
        errorPlacement: function (error, element) {
            error.appendTo(element.parents(".sale_form_r li").find(".errorBox"));
            $("body").click();
        },
        /*submitHandler: function (form) {
            $(".fxe_fyms label").hide();
            if ($("#Describe").val().length > 30 && $("#Describe").val().length < 1000) {//限制房源详情字数
                if ($("#messageCode") && $("#messageCode").is(":visible")) {
                    var phone = $("#OwnerPhone").val();
                    $.ajax({
                        type: "POST",
                        data: { action: "CheckSend", mobile: phone, code: $("#messageCode").val() },
                        url: "/checkPasscode",
                        async: false,
                        success: function (data) {
                            if (data == "1") {
                                form.submit();
                                return false;
                            } else {
                                $("#messageCodeValue").text("验证码不正确");
                                $("#messageCodeValue").show();
                                return false;
                            }
                        }
                    });
                } else {
                    form.submit();
                }
            } else {
                $(".fxe_fyms label").show();
                $("html,body").animate({ scrollTop: $(".fxe_fxgxms").offset().top }, 500)
            }
        },*/

        //required  必填   decimal 小数  maxlength最大长度

        rules: {
            describe:{
                required: true,
                maxlength: 1000,
                minlength: 5
             },
            //小区名称
            subname: {
                required: true
            },
            detailid: {
                required: true
            },
            //室厅卫
            room: {
                required: true,
                digits: true,
                min: 1
            },
            truearea: {
                required: true,
                noNum0: true
            },
            hall: {
                required: true,
                digits: true
            },
            toilet: {
                required: true,
                digits: true
            },
            //面积
            BuildArea: {
                required: true,
                decimal: true,
                noNum0: true
            },
            payment: {
                required: true
            },
            RentPrice: {
                decimal1: true,
                noNum01: true
            },
            SalePrice: {
                decimal1: true,
                noNum01: true
            },
            ownerphone: {
                required: true,
                mTmlNum: true
            },

            Password_text: {
                required: true,
                maxlength: 25,
                minlength: 4
            },
            danyuan: {
                maxlength: 2
            },
            shihao: {
                maxlength: 4
            },

            xingbie: {
                required: true
            },


            title: {
                maxlength: 30,
                minlength: 4,
                required: true
            },

            //行驶里程
            Mileage: {
                maxlength: 8,
                required: true,
                noNum0: true,
                decimal: false
            },
            //长度
            Txt_BodyLength: {
                required: true,
                decimal: true
            },
            blockshowname: {
                required: true
            },

            district2: {
                required: true
            },
            buildarea2: {
                required: true,
                digits: true,
                noNum0: true,
                min: function () {
                    if ($("#jsBuildareaB").length > 0 && $("#jsBuildareaB").val() != "")
                        return parseInt($("#jsBuildareaB").val());
                }
            },
            buildarea1: {
                required: true,
                digits: true
            },
            buildarea: {
                required: true,
                noNum0: true,
                decimal: true
            },
            price2: {
                required: true,
                decimal: true,
                noNum0: true,
                min: function () {
                    if ($("#jsZongJiaB").length > 0 && $("#jsZongJiaB").val() != "")
                        return parseInt($("#jsZongJiaB").val());
                }
            },
            price1: {
                required: true,
                decimal: true
            },
            price: {
                required: true,
                noNum0: true,
                decimal: true
            },
            pricetermname: {
                required: true
            },
            floor:
            {//单层 1
                required: true,
                // digits: true,
                noNum0: true,
               // max:99,
                min:-1

            },
            totalfloornumber:
            {//单层 2
                required: true,
                noNum0: true,
                min: function () {
                    if ($("#Floor").val()) {
                        return parseInt($("#Floor").val())
                    } else {
                        return parseInt(0)
                    }
                }
            },
            zfloor1:
            {//单层 1
                required: true,
                digits: true
            },
            ztotalfloor:
            {//单层 2
                required: true,
                min: function () {
                    if ($("#floor").val()) {
                        return parseInt($("#floor").val())
                    } else {
                        return parseInt(0)
                    }
                }
            },
            //            subfloor:
            //                    {//跃层1
            //                        required: function () {
            //                            if ($("#jsInputRadio2").attr("checked"))
            //                                return true;
            //                            else
            //                                return false;
            //                        },
            //                        textInteger: true
            //                    },
            //            floor2:
            //                    {//跃层1
            //                        required: "#jsInputRadio2:checked",
            //                        digits: true,
            //                        noNum0: true,
            //                        min: function () {
            //                            if ($("#subfloor").val()) {
            //                                return parseInt($("#subfloor").val()) + 1
            //                            } else {
            //                                return parseInt(1)
            //                            }
            //                        }
            //                    },
            //            floor3:
            //                    {//跃层1
            //                        required: "#jsInputRadio2:checked",
            //                        digits: true,
            //                        noNum0: true,
            //                        min: 0
            //                    },
            //            totalfloor2:
            //                    {//跃层1
            //                        required: "#jsInputRadio2:checked",
            //                        digits: true,
            //                        noNum0: true,
            //                        min: function () {
            //                            if ($("#floor2").val()) {
            //                                return parseInt($("#floor2").val())
            //                            } else {
            //                                return parseInt(0)
            //                            }
            //
            //                        }
            //                    },
            mrightname: {
                required: true
            },
            fitmentname: {
                required: true
            },
            forwardname: {
                required: true
            },
            buildingtime:
            {//建筑年代
                digits: true,
                maxlength: 4,
                minlength: 4,
                max: function () {
                    return parseInt(getYear())
                }
            },
            buildyear1: {
                required: true,
                digits: true,
                maxlength: 4,
                minlength: 4,
                max: function () {
                    return parseInt(getYear())
                }
            },
            buildyear: {
                required: true,
                digits: true,
                maxlength: 4,
                minlength: 4,
                max: function () {
                    return parseInt(getYear())
                }
            },
            Txt_Title: {
                required: true
            },
            //服务宗旨
            Txt_ServiceMission: {
                required: true
            },
            remark2: {
                required: true
            },

            houseowner: {
                required: true,
                maxlength: 10,
                minlength: 2
            },
            LookCarAd: {
                required: true
            },
            shop_stausname: {
                required: true
            },

            Phone: {
                mTmlNum: true
            },

            checkConsent: {
                required: true
            },
            fee: {//物业费
                required: true,
                noNum0: true,
                min: 0,
                number: true
            },
            floorVilla: {//别墅楼层
                required: true,
                noNum0: true,
                digits: true
            },
            renttypename: {
                required: true
            },
            paymentname: {
                required: true
            },
            rentType2: {
                required: function () {
                    if ($(".rentType2Box").is(":visible")) {
                        return true;
                    }
                }
            },
            rentType3: {
                required: function () {
                    if ($(".rentType3Box").is(":visible")) {
                        return true;
                    }
                }
            },
            houseAssort: {
                required: true,
                minlength: 1
            },
            houseType1: {
                required: true
            },
            'findroom[]': {
                required: true,
                minlength: 1
            },
            housekindname: {
                required: true
            },
            divisionname: {
                required: true
            },
            'equipment[]': {
                required: true
            },
            shopcategories: {
                required: true
            },

        },
        messages: {
            describe:{
                required: "请填写详细介绍",
                maxlength: "房源描述应在5至1000字之间",
                minlength: "房源描述应在5至1000字之间"
            },
            shopcategories: {
                required: "请填写商铺类型"
            },
            truearea: {
                required: "请填写面积",
                noNum0: "面积不能为0或其他非整数"
            },
            payment: {
                required: "请填选择付款方式",
                // noNum09: "面积不能为0或其他非整数"
            },
            subname: {
                required: "请从下拉框中选择小区"
            },
            detailid: {
                required: "请从下拉框中选择门牌信息"
            },
            room: {
                required: "请填写数字，如 2 室 1 厅 1 卫",
                digits: "请填写数字，如 2 室 1 厅 1 卫",
                min: "请填写数字，如 2 室 1 厅 1 卫"
            },
            hall: {
                required: "请填写数字，如 2 室 1 厅 1 卫",
                digits: "请填写数字，如 2 室 1 厅 1 卫"
            },
            toilet: {
                required: "请填写数字，如 2 室 1 厅 1 卫",
                digits: "请填写数字，如 2 室 1 厅 1 卫"
            },
            BuildArea: {
                required: "请填写面积",
                decimal: "面积不能为0或其他非整数",
                noNum0: "面积不能为0或其他非整数"
            },
            RentPrice: {
                decimal1: "价格只能为数字，允许两位小数",
                noNum01: "价格不能为0或者负数"
            },
            danyuan: {
                maxlength: 2
            },
            shihao: {
                maxlength: 4
            },
            SalePrice: {
                decimal1: "价格只能为数字，允许两位小数"
            },
            Password_text: {
                required: "请填写密码",
                maxlength: "4~25个字母或数字，不能填写特殊符号",
                minlength: "4~25个字母或数字，不能填写特殊符号"
            },
            ownerphone: {
                required: "请填写联系方式"
            },
            xingbie: {
                required: "性别要求不能为空"
            },

            title: {
                maxlength: "房源标题最多30个字",
                minlength: "标题太过简单",
                required: "请填写房源标题"
            },



            cartypelist: {
                required: "请选择车型",
                min: "车型必须要选择一个"
            },

            firmlist: {
                required: "请选择区域",
                min: "区域必须要选择一个"
            },
            bandlist: {
                required: "请选择板块",
                min: "板块必须要选择一个"
            },
            serieslist: {
                required: "请选择板块",
                min: "板块必须要选择一个"
            },
            modellist: {
                required: "请选择板块",
                min: "板块必须要选择一个"
            },

            linelist: {
                required: "请选看车时间",
                min: "看车时间必须要选择一个"
            },
            blockshowname: {
                required: "请填写小区名称"
            },

            district2: {
                required: "请选择板块"
            },
            buildarea: {
                required: "请正确填写面积",
                noNum0: "面积不能为0或其他非整数",
                decimal: "面积必须为正整数或两位小数"
            },
            buildarea1: {
                required: "请正确填写面积",
                digits: "面积必须为正整数"
            },
            buildarea2: {
                required: "请填写面积",
                digits: "面积不能为0或其他非整数",
                noNum0: "面积不能为0或其他非整数",
                min: "不能小于起始面积"
            },

            price2: {
                required: "请填写价格",
                decimal: "价格必须为正整数或两位小数",
                noNum0: "价格不能为0",
                min: "不能小于起始价格"
            },
            Txt_BodyLength: {
                required: "请填写车身长度",
                decimal: "车身长度必须为正整数或两位小数"
            },

            Mileage: {
                required: "请填写行驶里程",
                decimal: "行驶里程必须为正整数或两位小数",
                maxlength: "最大不能超过6位数"
            },
            price1: {
                required: "请填写价格",
                decimal: "价格必须为正整数或两位小数"
            },
            price: {
                required: "请填写价格",
                noNum0: "价格不能为0",
                decimal: "价格必须为正整数或两位小数"
            },
            pricetermname: {
                required: "请选择付税方式"
            },
            floor:
            {//单层 1
                required: "请填写楼层",
                digits:"",
                noNum0: "楼层数不能为0",
                min:"楼层数最小为-1"
                //max:"输入值不能大于99"
            },
            totalfloornumber:
            {//单层 1
                required: "请填写楼层",
                digits: "总楼层只能为正整数",
                min: "总楼层不能小于当前楼层",
                noNum0: "楼层不能为0或其他非整数"
            },
            zfloor:
            {//单层 1
                digits: "请填写楼层"
            },
            ztotalfloor:
            {//单层 1
                required: "请填写楼层",
                digits: "总楼层只能为正整数",
                min: "总楼层不能小于当前楼层",
                noNum0: "楼层不能为0或其他非整数"
            },
            subfloor: {
                required: "请填写楼层",
                textInteger: "楼层不能为0或其他非整数"
            },
            floor2: {
                required: "请填写楼层",
                digits: "请正确填写楼层",
                noNum0: "楼层不能为0或其他非整数",
                min: "请正确填写楼层"
            },
            floor3: {
                required: "请填写楼层",
                digits: "请正确填写楼层",
                noNum0: "楼层不能为0或其他非整数",
                min: "请正确填写楼层"
            },
            totalfloor2: {
                required: "请填写楼层",
                digits: "请正确填写楼层",
                noNum0: "楼层不能为0或其他非整数",
                min: "总楼层不能小于当前楼层"
            },
            mrightname: {
                required: "请选择产权属性"
            },
            fitmentname: {
                required: "请选择装修状况"
            },
            forwardname: {
                required: "请选择房屋朝向"
            },

            buildingtime:
            {//建筑年代
                required: "请填写建筑年代",
                digits: "请正确填写 - 格式:2010",
                maxlength: "请正确填写 - 格式:2010",
                minlength: "请正确填写 - 格式:2010",
                max: "不能大于当前年份",
                min: "不能小于起始年份"
            },
            buildyear1: {
                required: "请填写建筑年代",
                digits: "请正确填写 - 格式:2010",
                maxlength: "请正确填写 - 格式:2010",
                minlength: "请正确填写 - 格式:2010",
                max: "不能大于当前年份"
            },
            buildyear: {
                required: "请填写建筑年代",
                digits: "请正确填写 - 格式:2010",
                maxlength: "请正确填写 - 格式:2010",
                minlength: "请正确填写 - 格式:2010",
                max: "不能大于当前年份"
            },
            Txt_Title: {
                required: "请填写车源标题"
            },
            remark2: {
                required: "请正确填写详细介绍"
            },
            Txt_ServiceMission: {
                required: "服务宗旨有利您的业务哦！"
            },
            shop_stausname: {
                required: "请选择现状"
            },
            houseowner: {
                required: "请填写联系人",
                maxlength: "2~10个字，不能填写电话和特殊符号",
                minlength: "2~10个字，不能填写电话和特殊符号"
            },
            LookCarAd: {
                required: "请填看车地址"
            },

            Phone: {
                mTmlNum: "请正确填写11位手机号码"
            },

            checkConsent: {
                required: "请阅读并同意发布房源规则"
            },
            fee: {//物业费
                required: "请填写物业费",
                noNum0: "物业费不能为0",
                min: "请正确填写物业费",
                number: "请正确填写物业费"
            },
            floorVilla: {//别墅楼层
                required: "请填写总楼层",
                noNum0: "总楼层不能为0",
                digits: "请正确填写总楼层"
            },
            renttypename: {
                required: "请选择租赁方式"
            },
            paymentname: {
                required: "请选择付款方式"
            },
            rentType2: {
                required: "请选择卧室类型"
            },
            rentType3: {
                required: "请选择限制条件"
            },
            houseAssort: {
                required: "请选择房屋配套",
                minlength: "请选择房屋配套"
            },
            houseType1: {//houseStyle
                required: "请选择物业类型"
            },
            'findroom[]': {
                required: "请选择找房特点",
                minlength: "请选择找房特点"
            },
            housekindname: {
                required: "请选择属性"
            },
            divisionname: {
                required: "请选择是否可分割"
            },
            'equipment[]': {
                required: "请选择房屋配置"
            }
        }
    });






    function remarkyz() {


    }

    /***资料完善程度***/
    function getNum() {//计算百分比
        return parseInt($("#jsNumJdInput1").val()) + parseInt($("#jsNumJdInput2").val())
            + parseInt($("#jsNumJdInput3").val()) + "%"
    }
    function addGetNum() {
        var jbfenshu = 0;
        var fenshu = getNum();
        var tempfenshu = parseInt(fenshu);
        if (tempfenshu <= 20) {
            jbfenshu = tempfenshu * 0.6;
        } else if (tempfenshu > 20 && tempfenshu <= 40) {
            jbfenshu = tempfenshu * 0.7;
        } else if (tempfenshu > 40 && tempfenshu <= 60) {
            jbfenshu = tempfenshu * 0.8;
        } else if (tempfenshu > 60 && tempfenshu <= 90) {
            jbfenshu = tempfenshu * 0.9;
        } else if (tempfenshu > 90 && tempfenshu <= 100) {
            jbfenshu = tempfenshu;

        }
        jbfenshu = Math.floor(jbfenshu);
        $("#jsjbDataNum").text(jbfenshu + '%');
        $("#jsNumJd,#jsDataNum,#jsDataPercentage .dataPercentageInner").text(getNum());
        $("#jsProgressInner").css("width", getNum());
        $("#jsDataPercentage").css("left", getNum());
    }
    if (($.browser.msie) && ($.browser.version == "6.0")) {
        $("#fixDate").hide();
        $("#zlwsd").hide();
    }
    else {
        $(".jsDateInput").bind("change blur", function () {
            var oInputLen = $(".jsDateInput:visible[type=text]").length;
            var oInputLenVal = $(".jsDateInput:visible[type=text][value!='']").length;
            var oNum = (oInputLenVal / oInputLen) * 30;
            $("#jsNumJdInput1").val(parseInt(oNum));
            addGetNum();
        });
    }
    function tagNum() {
        var oItemlen = $("#jsHouseTabBox .itemSelect").length;
        if (oItemlen == 1)
            $("#jsNumJdInput2").val(7);
        else if (oItemlen == 2)
            $("#jsNumJdInput2").val(14);
        else if (oItemlen == 3)
            $("#jsNumJdInput2").val(20);
        else
            $("#jsNumJdInput2").val(0);
        addGetNum();
    }
    $("#jsAddTagBoxS .addTagInner .del,#jsHouseTabBox .item").live("click", function () {
        tagNum()
    })
    //picLenGetNum();
    function picLenGetNum() {// 计算资料完成度
        var picNum = $(".deltextFg3Img").length
        if ($("#jsHouseTabBox").length > 0) {//当房源特色存在的时候  $("#jsNumJdInput3").val();
            $("#jsNumJdInput3").val(picNum * 5);
        }
        else {//当房源特色不存在的时候  $("#jsNumJdInput3").val();
            $("#jsNumJdInput3").val(picNum * 7);
        }
        addGetNum();
    };

    //picLenGetNum(); //传图片后会调用


    $("#pricetype,#registered").change(function () {
        $("#Title").val(getInputVal())
    })
    $(".xiaoqu,.room,.hall,.toilet,.mianji").blur(function () {
        $("#Title").val(getInputVal())
    })
    var fkfs="";//付款方式
    var zw="";//主卧
    $(".fkfsul").find("li").mousedown(function(){
        if($(this).html()!="请选择付款方式")
            fkfs=$(this).html();
        $("#Title").val(getInputVal())
    })
    $(".zwul").find("li").mousedown(function(){
        if($(".zw").html()!="请选择主卧")
            zw=$(this).html();
        $("#Title").val(getInputVal())
    })
    //房源标题60个字符 1个汉字算2个字符
    $("#Title").live("blur keyup", function () {
        var obj_value_arr = $("#Title").val();
        var num = 0;
        var toNum = 0;
        for (var i = 0; i < obj_value_arr.length; i++) {
            num += returnStrLen(obj_value_arr[i]);
            if (returnStrLen(obj_value_arr[i]) == 2)
                toNum++
        }
        if (num > 60) {
            var oVal = $("#Title").val().substr(0, 60 - toNum);
            $("#Title").val(oVal);
            alert("最多60个字符(一个汉字等于2个字符)")
        }
    })

    function getInputVal() {
        var val1 = "";
        if ($(".xiaoqu").length > 0 && $(".xiaoqu").val() != "")
            val1 = $(".xiaoqu").val() + " ";

        var val2 = "";
        if ($(".room").length > 0 && $(".room").val() != "")
            val2 = $(".room").val() + "室 ";

        var val3 = "";
        if ($(".hall").length > 0 && $(".hall").val() != "")
            val3 = $(".hall").val() + "厅 ";

        var val4 = "";
        if ($(".toilet").length > 0 && $(".toilet").val() != "")
            val4 = $(".toilet").val() + "卫 ";

        var val5 = "";
        if ($(".mianji").length > 0 && $(".mianji").val() != "")
             val5 = $(".mianji").val() + "㎡ ";
        var getInputVal = val1 + val2 + val3 + val4 +val5;

        var rentrs=$(".rs .hover").html();
        switch(rentrs){
            case "整套出租":
                return getInputVal+fkfs;break;
            case "单间出租":
                return getInputVal+zw;break;
            case "床位出租":return val1+" 床位";break;
            default:return getInputVal
        }
    }



    function returnStrLen(sStr) {
        var aMatch = sStr.match(/[\u4E00-\u9FA5|，]+/g);
        return (aMatch ? 2 : 1);
    }
    //全选
    //   $("#houseAssort1All").change(function () {
    //        if (this.checked) {
    //            //$("#jsAllOrNo").text("取消");
    //            $(this).parent().siblings(".labelFg").find(".input_c").attr("checked", true);
    //            $("#houseAssortAllNO").attr("checked", false);
    //        }
    //        else {
    //            $("#jsAllOrNo").text("全选");
    //            $(this).parent().siblings(".labelFg").find(".input_c").attr("checked", false);
    //        }
    //    })
    //
    //    //全无处理
    //    $("#houseAssortAllNO").change(function () {
    //        if (this.checked) {
    //            $("#jsAllOrNo").text("全选");
    //            $(this).parent().siblings(".labelFg").find(".input_c").attr("checked", false);
    //            $(this).parent().siblings(".labelFg").find(".input_c").attr("disabled", true);
    //        }
    //        else {
    //            $(this).parent().siblings(".labelFg").find(".input_c").attr("disabled", false);
    //        }
    //    })

    //出租 合租选择出现卧室和限制条件选项
    $("#jsHZ").click(function () {
        $("#rentType2Box,#rentType3Box").show();
    }).parent().siblings().find(".link").click(function () {
        $("#rentType2Box,#rentType3Box").hide();
        $("#rentroom").val("");
        $("#rentremark").val("");
        $("#rentType2").val("");
        $("#rentType3").val("");
        $("#rentType4").html("选择卧室类型");
        $("#rentType5").html("选择限制条件");
        $("#rentType4").show();
        $("#rentType5").show();

    })
    //短租日租 更改单位
    $("#jsRZ").click(function () {
        $("#priceunit1").hide();
        $("#priceunit2").show();
    }).parent().siblings().find(".link").click(function () {
        $("#priceunit1").show();
        $("#priceunit2").hide();
    })
})




function insert(item) {
    if ($("#yysj").val().indexOf(item) < 0) {
        /*$("#yysj").val() = "";*/
        document.form1.yysj.value += item;
    }
}
