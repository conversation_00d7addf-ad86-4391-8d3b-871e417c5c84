/**
 * Created by Administrator on 2018/11/6 0006.
 */
$(function () {
    var l1 = 41.720732;//纬度
    var l2 = 123.45852;//经度
    var level = 12 ;//层级
    autoSearch("searchNames");
    function autoSearch(searchNames) {
        $("#searchNames").autocomplete("/searchRentByTitle", {
            multiple: false,
            max: 10,
            delay: 800, //延迟800毫秒
            async:false,
            matchSubset:false,//不用缓存
            parse: function (data) {
                return $.map(eval(data), function (row) {
                    return {
                        data: row,
                        //value: row.subName,
                        id: row.subId,
                        //result: row.subName
                    };
                });
            },
            formatItem: function (item) {
                var titles = '';
                if(item.Level == "1"){
                    titles = item.Title +"&nbsp;&nbsp;"+"<span style='color:#d0d6d6;'>"+item.Title+"</span>";
                    return titles; //下拉框显示区域内容
                }else if(item.Level == "2"){
                    titles = item.Title2  +"&nbsp;&nbsp;"+ "<span style='color:#d0d6d6;'>"+item.Title+"</span>";
                    return titles; //下拉框显示板块内容
                }else if(item.Level == "3"){
                    titles = item.Title3  +"&nbsp;&nbsp;"+ "<span style='color:#d0d6d6;'>"+item.Title+"</span>";
                    return titles; //下拉框显示小区内容
                }else{
                    return "" ;
                }
            }
        }).result(function (e, item) {
            if(item != null){
                if(item.Level == "1"){
                    $("#searchNames").attr("value", item.Title);
                    var searchName =$("#searchNames").val();
                    baiduMap.searchTitle = searchName;
                    baiduMap.regionList.ActiveId = item._Id;
                    baiduMap.regionList.ActiveText = searchName;
                    baiduMap.searchTitle = "";
                    $("#searchNames").val("");
                    $("#deleteButton").hide();
                    baiduMap.SetMap1(item.Latitude,item.Longitude,14);
                }else if(item.Level == "2"){
                    $("#searchNames").attr("value", item.Title2);
                    var searchName =$("#searchNames").val();
                    baiduMap.searchTitle = searchName;
                    baiduMap.regionList.ActiveId = "";
                    baiduMap.regionList.ActiveText="区域";
                    // baiduMap.searchPlateId = item._Id;
                    baiduMap.searchHierarchy = item.Level;
                    baiduMap.SetMap(item.Latitude,item.Longitude,15);
                }else if(item.Level == "3"){
                    $("#searchNames").attr("value", item.Title3);
                    var searchName =$("#searchNames").val();
                    baiduMap.housingEstate = item._Id;
                    baiduMap.searchTitle = searchName;
                    baiduMap.regionList.ActiveId = "";
                    baiduMap.regionList.ActiveText="区域";
                    baiduMap.SetMap(item.Latitude,item.Longitude,16);
                }else{
                    return "" ;
                }
            }
        });
    }

    //搜索按钮点击事件
    $(".searchMapBtn").click(function () {
        // var searchNames =  encodeURI(encodeURI($(".searchMapInput").val()));
        var searchNames = $(".searchMapInput").val();
        if(searchNames.length < 2 ){
            return;
        }else{
            baiduMap.regionList.ActiveId = "";
            baiduMap.regionList.ActiveText="区域";
            $.ajax({
                type: "POST",
                url: "/searchRentOneInfo",
                dataType: "json",
                data: {
                    search: searchNames
                },
                async:false,
                success: function (data) {
                    console.log(data);
                    searchName = stripscript(searchNames);
                    baiduMap.searchTitle = searchName;
                    console.log(data.length)
                    if(data.length >0){
                        $(".noHouseMap").hide();
                        // baiduMap.searchHierarchy = data.level; //搜索标题的层级
                        for(var i=0;i<data.length;i++){
                            // baiduMap.housingEstate = data[i]._id;
                            baiduMap.searchHierarchy = data[i].level; //搜索标题的层级
                            // baiduMap.housingEstate = data[i]._id;
                            if(data.length >1 ){
                                if(data[i].level == "1"){
                                    baiduMap.regionList.ActiveId = data[i]._id;
                                    baiduMap.regionList.ActiveText="区域";
                                    if(data[i]._id != null && searchName != null){
                                        baiduMap.regionList.ActiveId = "";
                                    }
                                    baiduMap.SetMap1(data[i].latitude,data[i].longitude,14);
                                    return;
                                }else if(data[i].level == "3"){
                                    //模糊搜小区，data.length多条数据时只给地图打一个点
                                    if(i == 0){
                                        baiduMap.SetMap(41.720732,123.45852,12);
                                        return ;
                                    }
                                    // baiduMap.SetMap(data[i].latitude,data[i].longitude,12);
                                }else if(data[i].level == "2"){
                                    baiduMap.regionList.ActiveId = "";
                                    baiduMap.regionList.ActiveText="区域";
                                    baiduMap.SetMap1(data[i].latitude,data[i].longitude,15);
                                    return;
                                }
                            }else if(data[i].level == "3" && data.length == 1){
                                baiduMap.SetMap1(data[i].latitude,data[i].longitude,16);
                            }else if(data[i].level == "2" && data.length == 1){
                                baiduMap.regionList.ActiveId = "";
                                baiduMap.regionList.ActiveText="区域";
                                baiduMap.searchPlateId = data[i]._id;
                                baiduMap.SetMap(data[i].latitude,data[i].longitude,15);
                            }else{
                                baiduMap.SetMap(data[i].latitude,data[i].longitude,16);

                            }
                        }
                    }else if(data.length == 0 &&  (baiduMap.newHousList != null || baiduMap.newHousList != "")){
                        queryLocation(searchNames);
                        baiduMap.SetMap(l1,l2,level);
                    }else if(data.length == 0){
                        $(".noHouseMap").show();
                        queryLocation(searchNames);
                        baiduMap.SetMap(l1,l2,level);
                        // baiduMap.SetMap(41.720732,123.45852,12); //沈阳
                    }
                }
            });
        }
    });

    function queryLocation(address){
        $.ajax({
            type: "POST",
            url: "/queryLocationByAddress",
            dataType: "json",
            data: {
                address: address
            },
            async:false,
            success: function (data) {
                // alert(data.content[0]);
                // alert(data.content[1]);
                if(data.content[0] >0.0){
                    l2 = data.content[0];
                    l1 = data.content[1];
                    level = 16;
                }
            }
        });
    }
    function stripscript(s) {
        var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
        var rs = "";
        for (var i = 0; i < s.length; i++) {
            rs = rs + s.substr(i, 1).replace(pattern, '');
        }
        return rs;
    }

    //回车事件
    $(".searchMapInput").keydown(function (event) {
        if (event.keyCode == 13) {
            $(".ac_results li").each(function () {
                if($(this).hasClass("ac_over")){
                    var x = $(this).html()
                    var y = x.split('<span');
                    var re = new RegExp("&nbsp;","g");
                    var z = y[0].replace(re,"").replace("<strong>","").replace("</strong>","")
                    $("#searchNames").val(z)
                }
            })

            var selecetFlag = true;
            $(".ui-autocomplete").each(function () {
                if($(this).css("display") == "block"){
                    var item = $(this);
                    $(item).find("a").each (function () {
                        if($(this).hasClass("ac_over")){
                            selecetFlag = false;
                            return false;
                        }
                    });
                }
            });
            if(selecetFlag){
                $(".searchMapBtn").click();
                //回车事件搜索提示框收起
                $(".ac_results").hide();
            }
        }
    });
});
$(".searchMapInput").val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
$(".searchMapInput").keyup(function(){
    $(this).val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
})
$("#deleteButton").click(function () {
    $(".searchMapInput").val("");
    $("#deleteButton").hide();
    baiduMap.searchTitle ="";
    var redirectUrl =location.pathname;
    window.location.href = redirectUrl;
});







