var bdMap = {
    id:0,
    houselng: 0,
    houselat: 0,
    radius: 1000, //搜索半径
    suofa: 15,//缩放比例
    openInfoList: [],//数据集合
    bdmap: undefined,
    bdpoit: {},
    bdtitle: "",
    bdcontent: "",
    address: "",
    city: "沈阳",
    switchKanfang: true,
    init: function (divid, entity) {
        bdMap.houselng = entity.houselng;
        bdMap.houselat = entity.houselat;
        bdMap.radius = entity.radius;
        bdMap.suofa = entity.suofa;
        bdMap.bdtitle = entity.bdtitle;
        bdMap.bdcontent = entity.bdcontent;
        bdMap.address = entity.address;
        bdMap.city = entity.city;
        bdMap.id = entity.id;

        if (bdMap.houselng * 1 == 0 || bdMap.houselat * 1 == 0) {
            bdMap.AddressToPoint(bdMap.address, bdMap.city, divid);//没有坐标的情况下从地址你想解析坐标
        }
        else {
            bdMap.showMap(divid);
        }

    },
    AddressToPoint: function (address, city, divid) {
        var myGeo = new BMap.Geocoder();
        // 将地址解析结果显示在地图上,并调整地图视野
        myGeo.getPoint(address, function (point) {
            if (point) {
                bdMap.houselng = point.lng;
                bdMap.houselat = point.lat;
                bdMap.showMap(divid);
            }
        }, city);
    },
    showMap: function (divid) {
        $("#" + divid).html("");
        openInfoWinFuns = []; bdMarkers = [];
        if (bdMap.bdmap == undefined) {
            bdMap.bdmap = new BMap.Map(divid, { enableMapClick: false });
        }
        bdMap.bdmap.clearOverlays();
        // bdMap.bdmap.enableScrollWheelZoom(true);
        bdMap.bdpoit = new BMap.Point(bdMap.houselng, bdMap.houselat);
        bdMap.bdmap.centerAndZoom(bdMap.bdpoit, bdMap.suofa);
        bdMap.bdmap.addControl(new BMap.NavigationControl()); //缩放尺
        bdMap.bdmap.addControl(new BMap.ScaleControl()); //比例尺
        bdMap.bdmap.enableInertialDragging(true); //惯性拖拽
        //bdMap.bdmap.addControl(new BMap.MapTypeControl({ anchor: BMAP_ANCHOR_TOP_RIGHT }));    //左上角，默认地图控件
        //bdMap.bdmap.setCurrentCity(bdMap.city);   //由于有3D图，需要设置城市哦
        if (bdMap.houselng * 1 == 0 || bdMap.houselat * 1 == 0) {
            bdMap.AddressToPoint(bdMap.address, bdMap.city);//没有坐标的情况下从地址你想解析坐标
        }
        bdMap.addnewmarker({ lng: bdMap.houselng, lat: bdMap.houselat, title: bdMap.bdtitle, content: bdMap.bdcontent });
        bdMap.SetMap( bdMap.houselng,bdMap.houselat);
        /*************绑定事件******************/
        bdMap.bdmap.addEventListener("dblclick", function () { getcenteris = true; });
        //bdmap.addEventListener("zoomend", function () { if (getcenteris) { bdmap.setCenter(bdpoit); getcenteris = false; } });
        bdMap.bdmap.addEventListener("rightclick", bdMap.showInfo);
        bdMap.searechHouse()
    },
    addnewmarker: function (data) {
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            div.style.position = "absolute";
            div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
            div.style.backgroundColor ="rgba(255, 82,0, 0.9)";
            div.style.color = "white";
            div.style.height = "18px";
            div.style.fontSize="14px";
            div.style.borderRadius="4px";
            div.style.padding = "8px 10px";
            div.style.lineHeight = "18px";
            div.style.whiteSpace = "nowrap";
            div.style.MozUserSelect = "none";
            div.style.fontSize = "12px";
            var span = this._span = document.createElement("span");
            div.appendChild(span);
            span.appendChild(document.createTextNode(this._text));
            var that = this;

            var arrow = this._arrow = document.createElement("div");
            arrow.style.background = "url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png) no-repeat";
            arrow.style.position = "absolute";
            arrow.style.width = "16px";
            arrow.style.height = "10px";
            arrow.style.top = "34px";
            arrow.style.left = "22px";
            arrow.style.overflow = "hidden";
            div.appendChild(arrow);
            bdMap.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(this._point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(data.lng, data.lat), data.title, data.content);
        bdMap.bdmap.addOverlay(myCompOverlay);
        if (bdMap.id && bdMap.id > 0) {
        }

    },
    addnewmarkerHouse: function (data,show,i) {
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            //div.attr("class", "bubble-2 bubble");
            div.style.position = "absolute";
            div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
            div.style.backgroundColor = "rgba(255, 82,0, 0.9)";
            div.className="showHousePrice"
            // div.style.border = "1px solid #BC3B3A";
            div.style.color = "white";
            div.style.height = "18px";
            div.style.fontSize="14px";
            div.style.borderRadius="4px";
            div.style.padding = "8px 10px";
            div.style.lineHeight = "18px";
            div.style.whiteSpace = "nowrap";
            div.style.MozUserSelect = "none";
            div.style.fontSize = "12px";
            var span = this._span = document.createElement("span");
            div.appendChild(span);
            span.appendChild(document.createTextNode(this._text));

            var price = show.mPrice;
            if(price == null && show.prType!=1){
                var pNames = document.createElement("label");
                pNames.appendChild(document.createTextNode("待售"));
                pNames.className="forSale"
                div.style.backgroundColor = "rgba(51,51,51, 0.9)";
                span.appendChild(pNames);
                div.className="showHousePrice showHousePrice_sun"
            }
            if(show.projectStatus == 3){
                div.style.backgroundColor = "rgba(128,153,175, 0.9)";
                div.style.zIndex = "-99999999999";
            }
            var that = this;

            var arrow = this._arrow = document.createElement("div");
            arrow.style.background = "url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png) no-repeat";
            arrow.style.position = "absolute";
            var showStatus = show.projectStatus;
            if(show.prType!=1){
                showStatus = 4;
            }
            arrow.className="showHouseJiao"+showStatus;
            arrow.style.width = "16px";
            arrow.style.height = "10px";
            arrow.style.top = "34px";
            arrow.style.left = "50%";
            arrow.style.marginLeft="-8px"
            arrow.style.overflow = "hidden";
            var kanfang =  document.createElement("div");
            kanfang.className="kanfang";
            kanfang.id="kanfang"+i
            kanfang.style.display="none"
            var x= document.createElement("img")
            x.src="https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian3.png"
            x.className="jiao"
            var fang = document.createElement("dl");
            var fang1 = document.createElement("dt");
            fang1.style.fontSize="16px";
            var projectUrl = document.createElement("a");
            projectUrl.appendChild(document.createTextNode(show.projectName))
            projectUrl.setAttribute("href", "https://sy.fangxiaoer.com/house/"+show.projectId+"-"+show.projectType+".htm");
            projectUrl.setAttribute("target","_blank");
            var projectState =document.createElement("span");
            var projectValue ="在售";
            if(show.projectStatus == 2) {
                projectValue = "待售";
            }else if(show.projectStatus == 3){
                projectValue = "售罄";
            }
            projectState.appendChild(document.createTextNode(projectValue));
            projectState.className = "zt" + show.projectStatus;
            var kanfangX=document.createElement("div");
            kanfangX.className="kanfangX";
            var fang2 = document.createElement("dd");
            var imgOutSide = document.createElement("a");
            imgOutSide.setAttribute("href", "https://sy.fangxiaoer.com/house/"+show.projectId+"-"+show.projectType+".htm");
            imgOutSide.setAttribute("target","_blank");
            var img = document.createElement("img");
            img.src=show.ImageUrl
            imgOutSide.appendChild(img);
            var infoOutSie = document.createElement("a");
            infoOutSie.setAttribute("href", "https://sy.fangxiaoer.com/house/"+show.projectId+"-"+show.projectType+".htm");
            infoOutSie.setAttribute("target","_blank");
            infoOutSie.className="infosupera";
            // var img1=document.createElement("img")
            // img1.src="https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian6.jpg"
            var ul = document.createElement("ul");
            var li1 = document.createElement("li");
            if(show.mPrice== null){
                li1.innerHTML="<p><span>"+"待定"+"</span></p>"
            }else{
                li1.innerHTML="<p><span>"+parseInt(show.mPrice.priceMoney)+"</span>元/㎡</p>"
            }
            var li2 = document.createElement("li");
            if(show.minArea!=null){
                li2.innerHTML=show.layout+" - "+show.minArea+"~"+show.maxArea+"㎡"
            }else{
                li2.style.height="26px"
            }

            var li3 = document.createElement("li");
            li3.innerHTML="咨询：<b>"+show.sortelTel+"</b>";
            infoOutSie.appendChild(li1)
            infoOutSie.appendChild(li2)
            infoOutSie.appendChild(li3)
            ul.appendChild(infoOutSie)
            // if(show.projectStatus != 3){
            //     ul.appendChild(img1)
            // }
            fang2.appendChild(imgOutSide)
            fang2.appendChild(ul)
            fang1.appendChild(projectUrl)
            fang1.appendChild(projectState)
            fang1.appendChild(kanfangX);
            fang.appendChild(fang1);
            fang.appendChild(fang2);
            kanfang.appendChild(fang);
            div.appendChild(arrow);
            kanfang.appendChild(x);
            div.appendChild(kanfang);
            div.onmouseover = function() {
                if(bdMap.switchKanfang){
                    div.style.zIndex="10000000";
                }
            }
            div.onmouseout = function() {
                if(bdMap.switchKanfang) {
                    if (show.projectStatus == 3) {
                        div.style.zIndex = "-9999999999";
                    } else {
                        div.style.zIndex = "9999999";
                    }
                }
            }
            bdMap.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(this._point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(data.lng, data.lat), data.title, data.content);
        bdMap.bdmap.addOverlay(myCompOverlay);
        if (bdMap.id && bdMap.id > 0) {
            if(show.prType == 1){
                myCompOverlay.addEventListener("click", function () {
                    bdMap.switchKanfang = false;
                    $(".kanfang").hide()
                    $(".showHousePrice ").removeClass("hover")
                    $(".showHousePrice").css("z-index","9999999")
                    document.getElementById("kanfang"+i).style.display="block"
                    document.getElementById("kanfang"+i).parentNode.style.zIndex="999999999";
                    bdMap.SetMap(data.lng, data.lat)
                    $(".map_dl dd").removeClass("hover")
                    $(".map_dl dd").each(function(){
                        if( $(this).find("i").text()==i){
                            $(this).addClass("hover")
                        }
                    })

                });
            }
        }
        // if(i==0){
            // $(".kanfang").hide()
            // $(".showHousePrice").css("z-index","9999999")
            // document.getElementById("kanfang0").parentNode.style.zIndex="999999999"
            // document.getElementById("kanfang0").style.display="block"
            // bdMap.SetMap(data.lng,data.lat)
        // }
    },
    //添加标注
    addmarker: function (poit, title, content) {
        var marker = new BMap.Marker(poit);
        bdMap.showFrom(marker, title, bdMap.bdmap, content);
        bdMap.bdmap.addOverlay(marker);//添加标注
    },
    //标注弹出面板
    showFrom: function (marker, title, map, content) {
        var searchInfoWindow = new BMapLib.SearchInfoWindow(map, "<p style='font-weight: 400;color: #333;'>" + content + "</p>", {
            title: "<p style='width: 355px;font: bold 14px/16px arial,sans-serif;margin: 0;color: #333;white-space: nowrap;overflow: hidden;font-size: 16px;line-height: 40px;' title='" + title + "'>" + title + "</p>", //标题
            width: 290, //宽度
            panel: "panel", //检索结果面板
            enableAutoPan: true, //自动平移
            searchTypes: [
            ]
        });
        marker.addEventListener("click", function(e) {
            searchInfoWindow.open(marker);
        });
    },

    //查询方法
    searechMap: function (type) {
        local = new BMap.LocalSearch(bdMap.bdmap, { renderOptions: { map: bdMap.bdmap, autoViewport: false }, onSearchComplete: bdMap.select });
        local.searchNearby(type, bdMap.bdpoit, bdMap.radius);
        $(".searechMapTitle").text(type);
        $(".map_dl").html("");
        local.setMarkersSetCallback(function (markers) {
            if(markers.length == 0 ||markers == ''){
                $(".map_dl").html("<div class='mao_null'> <p>暂时没有相关信息，看看其他内容吧</p></div>");
            }
            for (var i = 0; i < markers.length; i++) {
                bdMap.showFrom(markers[i].marker, markers[i].title, bdMap.bdmap, markers[i].address);
                bdMap.bdmap.addOverlay(markers[i].marker); //添加标注
            }
        });

    },
    //周边楼盘
    searechHouse:function(){
        bdMap.bdmap.clearOverlays(); //清除覆盖物
        var showPoint = bdMap.bdmap.getBounds();
        var bssw = showPoint.getSouthWest() //可视区域左下角
        var bsne = showPoint.getNorthEast() //可视区域右上角
        $.ajax({
            type:'post',
            url:"/viewBrandMap",
            data:{
                brandId:bdMap.id,
            },
            success:function (data) {
                var data = data.content
                var html = "";
                var price = "";
                for(var i =data.length-1 ;i>=0;i--){
                    price = data[i].mPrice;
                    if(price == null){
                        price = data[i].prType==1?'价格：待定':'';
                    }else {
                        price = data[i].mPrice.priceMoney+'元/㎡'
                    }
                    bdMap.addnewmarkerHouse({ lng: data[i].longitude, lat: data[i].latitude, title:data[i].projectName+' '+price, content:'' },data[i],i);
                    html += "<dd onclick='showHouseId("+i+","+data[i].longitude+","+data[i].latitude+")'><a title='" + data[i].projectName + "'><i>"+i+"</i><span>"+data[i].distance+"米</span>" + data[i].projectName + "</a></dd>";
                }
                $(".map_dl").html(html);
            }
        })
    },

    //添加显示面板
    openInfoWindow: function (searchInfo) {
        var searchInfoWindow = new BMapLib.SearchInfoWindow(bdMap.bdmap, "<p style='width:355px;font:bold 14px/16px arial,sans-serif;margin:0;color:#333;font-weight: 400;white-space:nowrap;overflow:hidden'>"+searchInfo.address+"</p>", {
            title: "<p style='width: 355px;font: bold 14px/16px arial,sans-serif;margin: 0;color: #333;white-space: nowrap;overflow: hidden;font-size: 16px;line-height: 40px;' title='" + searchInfo.title + "'>" + searchInfo.title + "</p>", //标题
            width: 350, //宽度
            panel: "panel", //检索结果面板
            enableAutoPan: false, //自动平移
            searchTypes: [
            ]
        }); bdMap.SetMap(searchInfo.lng, searchInfo.lat)
        searchInfoWindow.open(new BMap.Point(searchInfo.lng, searchInfo.lat));

    },
    //设置地图中心点
    SetMap: function ( lat,lng) {
        var  bdpoit = new BMap.Point( parseFloat(lat)+0.0025,lng);
        bdMap.bdmap.panTo(bdpoit); //将地图移动到点
    },
    //查询结果绑定
    select: function (results) {
        bdMap.openInfoList = [];
        bdMap.bdmap.clearOverlays();
        bdMap.bdmap.centerAndZoom(bdMap.bdpoit, bdMap.suofa);
        bdMap.addnewmarker({ lng: bdMap.houselng, lat: bdMap.houselat, title: bdMap.bdtitle, content: bdMap.bdcontent });
        local.disableFirstResultSelection();
        // 判断状态是否正确
        if (local.getStatus() == BMAP_STATUS_SUCCESS) {
            var s = [];
            for (var i = 0; i < results.getCurrentNumPois() ; i++) {

                var entity = {};
                entity.title = results.getPoi(i).title;
                entity.address = results.getPoi(i).address;
                entity.phoneNumber = results.getPoi(i).phoneNumber;
                entity.point = results.getPoi(i).point;
                entity.detailUrl = results.getPoi(i).detailUrl;
                entity.url = results.getPoi(i).url;
                var pointA = new BMap.Point(bdMap.houselng, bdMap.houselat);  // 创建点坐标A
                var pointB = new BMap.Point(results.getPoi(i).point.lng, results.getPoi(i).point.lat);  // 创建点坐标B
                entity.distance = (bdMap.bdmap.getDistance(pointA, pointB)).toFixed(2);  //获取两点距离,保留小数点后两位
                bdMap.openInfoList.push(entity);

            }
            //排序对象集合
            var list = bdMap.openInfoList.sort(function (a, b) {
                return a.distance - b.distance;
            });
            var html = "";
            for (var i = 0; i < list.length; i++) {
                html += "<dd onclick='bdMap.openInfoWindow({title:\"" + list[i].title + "\",address:\"" + list[i].address + "\",lng:" + list[i].point.lng + ",lat:" + list[i].point.lat + "})'><a title='" + list[i].title + "'><i>"+(i+1)+"</i><span>" + list[i].distance + "米</span>" + list[i].title + "</a></dd>";
            }
            $(".map_dl").html(html);
        }
    }

};

$(document).on("click",".kanfangX",function(){
    bdMap.switchKanfang = true;
    $(".kanfang").hide();
    $(".map_dl dd").removeClass("hover")
    // $("#kanfang0").parent().addClass("hover")
})

$(document).on("click",".kanfang dd ul>img",function(){
    // $(".title2 p").text($(this).parent().parent().parent().find("dt").text())
    // $(".tcbg2").fadeIn(300)
    // $(".main2").fadeIn(300)
    //修改地图点击看房车的项目名
    $("#projectName_for_freeCar").val($(this).parent().parent().prev().find("a").text())
    showUsercode(3);
})
function showHouseId(i,lng,lat){
    $(".kanfang").hide()
    $(".map_dl dd").removeClass("hover")
    $(this).addClass("hover")
    $(".showHousePrice").css("z-index","9999999")
    document.getElementById("kanfang"+i).parentNode.style.zIndex="999999999"
    document.getElementById("kanfang"+i).style.display="block"
    bdMap.SetMap(lng,lat)
}
$(".map_dl dd").live("click",function(){
    $(".map_dl dd").removeClass("hover")
    $(this).addClass("hover")
    $(".showHousePrice ").removeClass("hover")
})

function ComplexCustomOverlay(point, text, mouseoverText) {
    this._point = point;
    this._text = text;
    this._overText = mouseoverText;
}
// bdMap.searechMap('公交')
