/**
 * Created by t<PERSON><PERSON> on 2017/9/6.
 */

$(function () {
    $(".houseMore").click(function () {
        var id = $(this).attr("data-id");
        $(".tanIframe").html("<iframe class='moreCen' allowtransparency='true' src='/decolayout/" + id + ".htm'></iframe>");
        $(".openClose,.moreCen").fadeIn();
        $(".blackBg").show();
    });
    $(".openClose").click(function () {
        $(".openClose,.moreCen").fadeOut(function () {
            $(".tanIframe").html("");
        });
        $(".blackBg").hide();
    });

    $(".houseMore_box").click(function () {
        var id = $(this).prev().attr("data-id");
        $(".tanIframe").html("<iframe class='moreCen' allowtransparency='true' src='/decolayout/" + id + ".htm'></iframe>");
        $(".openClose,.moreCen").fadeIn();
        $(".blackBg").show();
    });
});
var width = 0, len = 0;
$(".picture li").click(function () {
    which = $(this).index();
    len = $(".picture li").length;
    $(".bigPic").html("");
    cPic(which);
    $(".tanPic").fadeIn();
    $(".picPre,.picNext,.blackBg").show();
    panDuan();
});
$(".picNext").click(function () {
    $(".picPre").show();
    ++which;
    panDuan();
    cPic(which);
});
$(".picPre").click(function () {
    $(".picNext").show();
    --which;
    panDuan();
    cPic(which);
});
$(".bigPicClose").click(function () {
    $(".tanPic").fadeOut();
    $(".blackBg").hide();
    $(".bigPic").html("");
});
function cPic(which) {
    var address = $(".picture li").eq(which).find("img").attr("src").replace("middle", "big");
    $(".bigPic").html("").html("<img/>");
    $(".bigPic img").attr("src", address).fadeIn();
}
function panDuan() {
    if (which <= 0) {
        $(".picPre").hide();
    } else if (which >= (len - 1)) {
        $(".picNext").hide();
    }
}
