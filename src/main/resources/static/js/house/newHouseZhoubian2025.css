*{
    margin: 0;
    padding: 0;
}
#allmap{
    width: 100% !important;
    height: 400px;
}
.labelSecHouse{padding: 4px 8px;    cursor: pointer;position: relative;z-index: 2}
.Status_1,.Status_2{background-color: rgba(255, 82, 0, 0.8);z-index: 9;}
.Status_3{background-color:rgba(127, 152, 174, 0.9);}
.showThis{background: #2a95fe!important}
.labelSecHouse.hover{background: #2a95fe!important;z-index: 10}
.map-a,.map-a:hover{color: #fff}
.map_dl{
    display: none;
}
.map_dl dd .map_icon {
    height: 60px;
    display: inline-block;
    margin-right: 10px;
    text-align: center;
    float: left;
    color: #fff;
    width: 28px !important;
}
.mapicon{
    width: 30px;
    height: 36px;
    display: block;
    position: relative;
}
.map_icon_0{
    background-image: url(https://static.fangxiaoer.com/web/images/map/map_6_sun.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 28px 36px;

}
.map_icon_1{
    background-image: url(https://static.fangxiaoer.com/web/images/map/map_1_sun.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 28px 36px;

}
.map_icon_2{
    background-image: url(https://static.fangxiaoer.com/web/images/map/map_2_sun.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 28px 36px;

}
.map_icon_3{
    background-image: url(https://static.fangxiaoer.com/web/images/map/map_3_sun.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 28px 36px;

}
.map_icon_4{
    background-image: url(https://static.fangxiaoer.com/web/images/map/map_4_sun.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 28px 36px;

}
.map_icon_5{
    background-image: url(https://static.fangxiaoer.com/web/images/map/map_5_sun.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 28px 36px;

}

.nearOther{
    display: none;
    background: #fff;
    position: absolute;
    padding: 10px  20px;
    border-radius: 10px;
    z-index: 99;
    bottom: 59px;
    width: 300px;
    text-align: left;
    left: -150px;
    box-shadow: 0px 0px 10px #999;
}
.nearOther h4{
    font-size: 16px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    margin-bottom: 5px;
}
.nearOther p{}
.otherInfoIcon{
    width: 58px;
    height: 31px;
    display: block;
    background: url(http://api.map.baidu.com/library/SearchInfoWindow/1.4/src/iw_tail.png) top center;
    background-size: 100% 100%;
    z-index: 5;
    position: absolute;
    bottom: -31px;
    left: 169px;
}

.nearOther p{
    width: 100%;
    display: block;
    white-space: break-spaces;
}

.closeInfo{
    background: url(https://static.fangxiaoer.com/m/static/IM/img/sy_btn_close.png) no-repeat center;
    float: right;
    height: 15px;
    width: 15px;
    cursor: pointer;
    background-size: 100% 100%;
    z-index: 9999;
    position: absolute;
    right: 10px;
    top: 10px;
}
.projectInfo{
    background: #fff;
    padding: 10px;
    border-radius: 5px;
    width: 335px;
    padding-bottom: 6px;
    position: absolute;
    top: -140px;
    left: -101px;
    z-index: 999;
    text-align: left;
    display: none;
}
.projectInfo h4{
    text-align: left;
    padding-bottom: 4px;
}
.projectInfo h4 a{
    font-weight: bold;
    padding-bottom: 10px;
    color: #333;
    font-size: 16px;
}
.projectInfo h4 a:hover{color:#ff5200}
.projectInfo .spanType{
    font-size: 14px;
    background-color: #fe6110;
    font-weight: normal;
    color: #fff;
    line-height: 22px;
    padding-right: 12px;
    padding-left: 12px;
    border-radius: 3px;
    margin-left: 10px;
}
.projectInfo .spanType_1{
    background-color: #fe6110;
}
.projectInfo .spanType_2{
    background-color:#fe9c02;
}
.projectInfo .spanType_3{
    background-color: #8099af;
}
.projectInfo .InfoMain{
    border-top: 1px solid #eee;
    padding-top: 10px;
}
.projectInfo .InfoMain a{}
.projectInfo .InfoImg{
    width: 110px;
    height: 75px;
    float: left;
    display: block;
    margin-right: 10px;
}
.projectInfo .InfoMainR{}
.projectInfo .InfoP{
    font-size: 14px;
    color: #333;
}
.projectInfo .InfoP1{}
.projectInfo .InfoP1 span{
    font-size: 20px;
    font-weight: bold;
    color: #ff5200;
    font-family: Georgia;
}
.projectInfo .InfoP3 span{
    font-size: 14px;
    color: #ff5200;
    font-weight: bold;
}
.projectInfo .InfoP2{
    color: #666;
    font-size: 12px;
    line-height: 26px;
}
.projectInfo .sj{
    display: block;
    position: absolute;
    bottom: -8px;
    left: 160px;
    width: 13px;
    height: 8px;
    background: url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian3.png) top center;
    background-size: 100% 100%;
}