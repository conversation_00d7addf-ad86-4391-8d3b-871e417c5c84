/**
 * Created by Administrator on 2017/10/11.
 */
function setCookie(cname, cvalue) {
    var d = new Date();
    d.setTime(d.getTime() + (1 * 60 * 60 * 1000));
    var expires = "expires=" + d.toGMTString();
    document.cookie = cname + "=" + cvalue + "; " + expires + ";path=/";
}
function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i].trim();
        if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
    }
    return "";
}
function checkCookie() {
    var user = getCookie("MAP");
    var str = window.location.pathname;
    var ind,
        i,
        j = str.length,
        k = 0,
        lastk,
        start = 0,
        num = new Array,
        num1 = new Array;
    n = [1, 2, 10, -2, -1];
    for (i = 0 ; i <= j ; i++) {
        lastk = k;
        start = str.indexOf("/", k + 1);
        if (start == -1) {
            break;
        }
        k = start;
    }
    for (i = 0 ; i <= j ; i++) {
        lastk = k;
        k = str.indexOf("_", k + 1);
        num[i] = window.location.pathname.slice(lastk + 1, k);
        if (k == -1) {
            num[i + 1] = parseInt(window.location.pathname.slice(k, j));
            break;
        }
    }
    var setType;
    for (i = 0 ; i <= 4; i++) {
        j = $(".contentTop a").eq(i).attr("class");

        if (j != "") {
            setType = i;
        }
    }
    if (str.indexOf("_", 0) == -1) {
        num[7] = 0;
        num[1] = 0;
    }
    var MAP = "1a" + num[1] + "a" + "-1" + "a" + num[7];//"1a"找楼盘
    setCookie("MAP", MAP);
    window.location.href = "/static/houseMap.htm";
}