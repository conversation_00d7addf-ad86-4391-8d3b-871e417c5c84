﻿var LjzPage = {
    MaxCount: 0,
    MethodName:"",
    PageSize: 0,
    MaxPage: 0,
    ThisPage:1,
    minPage:1,
    toPage:10,
    Init: function (maxCount,methodName, pagesize) {
        LjzPage.MethodName = methodName;
        LjzPage.MaxCount = maxCount;
        LjzPage.PageSize = pagesize;
        LjzPage.PageCreate(1);
    },   
    PageCreate: function (pageindex) {
        if (LjzPage.MaxCount % LjzPage.PageSize == 0) {
            LjzPage.MaxPage =  parseInt(LjzPage.MaxCount / LjzPage.PageSize);
        } else {
            LjzPage.MaxPage = parseInt(LjzPage.MaxCount / LjzPage.PageSize + 1);
        }
        LjzPage.ThisPage = pageindex;
        var ye = "";
        var tou = "";
        var wei = "";
        var n =  Math.floor((LjzPage.ThisPage-1)/10)
        if(n>=1){
            LjzPage.minPage = n * 10+1;
            LjzPage.toPage = 10+ LjzPage.minPage-1;
        }else {
            LjzPage.minPage = 1;
            LjzPage.toPage = 10;
        }
        if(LjzPage.toPage >LjzPage.MaxPage){
            LjzPage.toPage = LjzPage.MaxPage;
        }
        switch (pageindex) {
            case 1:
                tou+="<a disabled='disabled' style='margin-right:5px;'>首页</a>" +
                "<a disabled='disabled' style='margin-right:5px;'>上一页</a>";
                if(LjzPage.minPage>10){
                    tou += "<a style='margin-right:5px;cursor:pointer;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.minPage - 1) + ")'>...</a>";
                }
                if (LjzPage.MaxPage == 1) {
                    wei += "<a disabled='disabled' style='margin-right:5px;'>下一页</a>" +
                "<a disabled='disabled' style='margin-right:5px;'>尾页</a>";
                } else {
                    if(LjzPage.toPage < LjzPage.MaxPage ){
                        wei += "<a style='margin-right:5px;cursor:pointer;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.toPage + 1) + ")'>...</a>";
                    }
                    wei += "<a href='javascript:void(0)' style='margin-right:5px;' onclick='" + LjzPage.MethodName + "(" + (LjzPage.ThisPage + 1) + ")'>下一页</a>";
                    wei += "<a href='javascript:void(0)' style='margin-right:5px;' onclick='" + LjzPage.MethodName + " (" + LjzPage.MaxPage + ")'>尾页</a>";
                }
                
         
            break ;
            case LjzPage.MaxPage:
                tou += "<a href='javascript:void(0)' style='margin-right:5px;'  onclick='" + LjzPage.MethodName + "(1)'>首页</a>" +
                "<a href='javascript:void(0)' style='margin-right:5px;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.ThisPage - 1) + ")'>上一页</a>";
                if(LjzPage.minPage>10){
                    tou += "<a style='margin-right:5px;cursor:pointer;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.minPage - 1) + ")'>...</a>";
                }
                wei += "<a disabled='disabled' style='margin-right:5px;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.ThisPage + 1) + ")'>下一页</a>";
                wei += "<a disabled='disabled' style='margin-right:5px;' onclick='"+LjzPage.MethodName+"(" + LjzPage.MaxPage + ")'>尾页</a>";
                break;
            default:
                tou += "<a href='javascript:void(0)' style='margin-right:5px;' onclick='"+LjzPage.MethodName+"(1)'>首页</a>" +
                                 "<a href='javascript:void(0)' style='margin-right:5px;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.ThisPage - 1) + ")'>上一页</a>";
                if(LjzPage.minPage>10){
                    tou += "<a style='margin-right:5px;cursor:pointer;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.minPage - 1) + ")'>...</a>";
                }
                if(LjzPage.toPage < LjzPage.MaxPage ){
                    wei += "<a style='margin-right:5px;cursor:pointer;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.toPage + 1) + ")'>...</a>";
                }
                wei += "<a style='margin-right:5px;cursor:pointer;' onclick='"+LjzPage.MethodName+"(" + (LjzPage.ThisPage + 1) + ")'>下一页</a>";
                wei += "<a style='margin-right:5px;cursor:pointer;' onclick='"+LjzPage.MethodName+"(" + LjzPage.MaxPage + ")'>尾页</a>";
                break;
        }
        
        for (var i = LjzPage.minPage;i <= LjzPage.MaxPage && i<=LjzPage.toPage; i++) {
            if (i  == pageindex) {
                ye += "<span style='margin-right:5px;font-weight:Bold;color:red;' class='thispage'>" + (i) + "</span>";
            }
            else {
                ye += "<a href='javascript:void(0)' style='margin-right:5px;' onclick='"+LjzPage.MethodName+"(" + (i) + ")'>" + (i) + "</a>";
            }
        }
        var html = tou + ye + wei;
        $("#LjzPage").html(html);
    }
};