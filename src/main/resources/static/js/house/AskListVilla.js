/**
 * Created by Administrator on 2018/6/18.
 */
var AskList = {
    PageIndex: 1,
    PageSize: 5,
    ProjectId: 0,
    MemberID: 0,
    Phone: "",
    UserName: "",
    MaxCount: 0,
    GetList: function (projectId, memberId, phone, userName) {
        AskList.ProjectId = projectId;
        AskList.MemberID = memberId;
        AskList.Phone = phone;
        AskList.UserName = userName;
        $.ajax({
            type: "post",
            url: "/getAskInfo",
            data: {
                askType: $("#askType").val(),
                page: AskList.PageIndex,
                pageSize: AskList.PageSize,
                projectId: AskList.ProjectId
            },
            scriptCharset: 'utf-8',
            dataType: "json",
            success: function (data) {
                deleteDisableElement();
                var data = eval(data);
                if (data != null) {
                    AskList.MaxCount = data.msg;
                    // $("#dianpingDom").show();s
                    var list = data.content;
                    if(list != null && list != undefined && list != ''){
                        AskList.BindData(list);
                        AskLjzPage.Init(AskList.MaxCount, "AskList.AppList", AskList.PageSize);
                        $("#current_asklist_isEmpty").parent().remove();
                    }else {
                        $("#view_more_ask").remove();
                        $("#current_asklist_isNotEmpty").remove();
                        $("#AskList_Pager1").parent().remove();
                    }
                    // else {
                    //     $("#listDom").html("<div class='zhanwuziliao'>暂无评价，欢迎发表您的观点</div>");
                    // }

                }


                if (window._bd_share_main) {
                    window._bd_share_main.init();
                }
            }
        });
        //getCommentCount
    },
    SetCount: function (projectId) {
        $.ajax({
            type: "post",
            url: "/getAskCount",
            data: {
                //action: "GetCommentCount",
                projectId: projectId
            },
            dataType: "json",
            success: function (data){
                //var entity = eval('('+data+')');
                // alert(data);
                if (data != null) {
                    // var entity = jQuery.parseJSON(data.Json);

                    $("#zx1").text("全部问答（" + data.totalAsk + "）");
                    /*$("#zx2").text("房源提问（" + data.houseAsk + "）");
                     $("#zx3").text("促销活动（" + data.activityAsk + "）");
                     $("#zx4").text("支付及退款（" + data.payAndRefund + "）");
                     $("#zx5").text("房仔服务（" + data.serviceAsk + "）");*/
                }
            }
        });
    },
    BindData: function (data) {
        var html = "";
        for (var i = 0; i < data.length; i++) {
            var item = $("<li></li>");
            var ask = $("<div class='houseAnswerTitle'></div>");
            var askContent = data[i].content;
            ask.html(askContent);
            var askdiv = $('<div class="houseAnswerPhone"></div>');
            var askInfo = "<b>"+data[i].tel +"</b><span>"+data[i].askTime+"</span>";
            askdiv.html(askInfo);

            var answer = $("<div class='houseAnswerContent'></div>");
            var answer_info = "<span>房小二网客服回复：</span>"+data[i].replyContent;
            answer.html(answer_info);

            item.append(ask);
            item.append(askdiv);
            item.append(answer);
            html += "<li>"+item.html()+"</li>";
        }
        $(".houseAnswer").html(html);
        //CommentList.BindSubmitReply();
    },
    BindPic: function (data) {
        var picDom = $("<div class='ck_pic'></div>");
        var num = $("<span>共<i>" + data.length + "</i>张图片</span>");
        var ul = $("<ul></ul>");
        for (var i = 0; i < data.length; i++) {
            ul.append("<li><img src='"+ data[i].smallImageUrl + "' alt=''></li>");
        }
        picDom.append(ul);
        picDom.append(num);
        return picDom;
    },
    BindFenXiang: function () {
        window._bd_share_config = { "common": { "bdSnsKey": {}, "bdText": "", "bdMini": "2", "bdMiniList": false, "bdPic": "", "bdStyle": "0", "bdSize": "16" }, "share": {} }; with (document) 0[(getElementsByTagName('head')[0] || body).appendChild(createElement('script')).src = 'http://bdimg.share.baidu.com/static/api/js/share.js?v=89860593.js?cdnversion=' + ~(-new Date() / 36e5)];
    },
    BindReply: function (data, commentId) {
        var count = 0;
        if (data && data.length > 0) {
            count = data.length;
        }
        var html = "<div class='remark_discuss'>" +
            "<span class='replay_btn' style='float:left'>回复（" + count + "）</span>" +
            // "<div class='bdsharebuttonbox' style='display:inline'><a href='#' class='bds_more' data-cmd='more' style='background: url(https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon2.jpg) no-repeat 0 4px;color: #999;padding-top: 2px;'>分享</a></div>" +
            //"<span><img src='https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon2.jpg'/>分享</span>" +
            "<div class='remark_reply'>" +
            "<textarea rows='1'></textarea>" +
            "<label>回复 ：</label>" +
            "<input type='button' value='提交回复' class='SubmitReply' data-id='" + commentId + "'/>" +
            "<div class='cl'></div>" +
            "<img src='https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg'/>" +
            "</div>" +
            "</div>";
        if (data && data.length > 0) {
            for (var i = 0; i < data.length; i++) {
                html += "<div class='remark_reply_as'>"+
                    "<span>" + data[i].phone + "：</span>　" + data[i].replyContent + "　<span>" + data[i].addTime + "</span>" +
                    "<p class='replay_btn'>回复</p>"+
                    "<div class='remark_reply'>"+
                    "<textarea rows='1'></textarea>"+
                    "<label>回复 ：</label>"+
                    "<input type='button' value='提交回复' class='SubmitReply' data-id='" + commentId + "'/>" +
                    "<div class='cl'></div>"+
                    "<img src='https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg'/>" +
                    "</div>"+
                    "</div>";
            }
        }
        return html;
    },
    BindSubmitReply: function () {
        $(".SubmitReply").click(function () {
            if (CommentList.MemberID > 0) {
                var text = $(this).prev().prev().val();
                $.ajax({
                    type: "GET",
                    url: "/Action/CommentHandler.ashx",
                    data: {
                        action: "AddReply",
                        MemberID: CommentList.MemberID,
                        ProjectId: CommentList.ProjectId,
                        Phone: CommentList.Phone,
                        UserName: CommentList.UserName,
                        ReplyContent: text,
                        CommentId: $(this).attr("data-id")
                    },
                    dataType: "json",
                    success: function (data) {
                        if (data && data.State == 1) {
                            Alertljz.ShowAlert("回复成功，请耐心等待审核！", "https://my.fangxiaoer.com//Admin/Comment/MyPublishReply.aspx");
                        } else {
                            Alertljz.ShowAlert(data.Message);
                        }

                    }
                });
            } else {
                $(".modal-backdrop").show();
                $("#login").show();
                //Alertljz.ShowAlert("登录以后才可以回复哦!");
            }

        });
    },

    AppList: function (pageIndex) {
        if (pageIndex <= AskLjzPage.MaxPage) {
            $.ajax({
                type: "post",
                url: "/getAskInfo",
                data: {
                    askType: $("#askType").val(),
                    page: pageIndex,
                    pageSize: AskList.PageSize,
                    projectId: AskList.ProjectId
                },
                dataType: "json",
                success: function (data) {
                    if (data != null ) {
                        var list = data.content;
                        AskList.BindData(list);
                        AskLjzPage.PageCreate(pageIndex);
                    }
                    // else {
                    //     $("#listDom").html("<div class='zhanwuziliao'>暂无评价，欢迎发表您的观点</div>");
                    // }
                    if (window._bd_share_main) {
                        window._bd_share_main.init();
                    }
                    // if ($("#LjzPage")) {
                    //     var offset = $("#LjzPage").offset();
                    //     offset.top = offset.top - 360;
                    //     scrollOffset(offset);
                    // }

                }
            });
        }
    }
};
function scrollOffset(scroll_offset) {
    $("body,html").animate({
        scrollTop: scroll_offset.top - 70
    }, 0);
}
function PicTanchu() {
    var width = 0, len;
    var which1 = 0;
    $('.ck_pic li').unbind("click");
    $(".ck_pic li").click(function () {
        $(this).parent("ul").addClass("isMe");
        len = $(".ck_pic .isMe li").length;
        which1 = $(this).index();
        $(".bigPic").html("");
        cPic(which1);
        $(".tanPic").fadeIn();
        $(".picPre,.picNext,.blackBg").show();
        panDuan(which1, len);

    });
    $('.picNext').unbind("click");
    $(".picNext").click(function () {
        $(".picPre").show();
        ++which1;
        panDuan(which1, len);
        cPic(which1);
    });
    $('.picPre').unbind("click");
    $(".picPre").click(function () {
        $(".picNext").show();
        --which1;
        panDuan(which1, len);
        cPic(which1);
    });
    $('.bigPicClose').unbind("click");
    $(".bigPicClose").click(function () {
        $(".tanPic").fadeOut();
        $(".blackBg").hide();
        $(".bigPic").html("");
        /*$(".ck_pic ul").removeClass("isMe");*/
        $(".ck_pic ul").attr("class", "noMe");
    });

}

function cPic(which) {
    var address = $(".ck_pic .isMe li").eq(which).find("img").attr("src").replace("middle", "big");
    //var address = $(".ck_pic .isMe li").eq(which).find("img").attr("src")
    // var address = $(".fy li").eq(which).find("img").attr("src").replace("middle", "big");
    $(".bigPic").html("").html("<img/>");
    $(".bigPic img").attr("src", address).fadeIn();
}
function panDuan(w, l) {
    if (l== 1) {
        $(".picPre").hide();
        $(".picNext").hide();
    } else {
        if (w <= 0) {
            $(".picPre").hide();
        } else if (w >= (l - 1)) {
            $(".picNext").hide();
        }
    }


}
