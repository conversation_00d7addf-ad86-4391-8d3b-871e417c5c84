/**
 * Created by Administrator on 2017/11/22.
 */

//创建时间：2017-04-26
//更新时间:
//用于复杂页面的常用验证
//作者:张凯情
//fxe_alert("提示语")非阻塞式提示
//正确返回true  错误返回错误信息

var sy_hint = ["账户名与密码不匹配，请重新输入!",//登录  0
    "请输入账户名!",//1
    "请输入密码或验证码!",//2
    "请输入账户名和密码!",//3
    "请输入你的手机号码!",//4
    "手机号码格式不正确，请重新输入!",//
    "请输入验证码!",//6
    "验证码或密码错误!",//7
    "手机号格式不正确!",//  8
    "该账户不存在，请请核对后重新输入。",//  9
    "密码确认不正确!",//10
    "密码格式不正确!",//11
    "推荐人与用户名不能重复!",//12
    "该账户不存在，请核对后重新输入。",//13
    "推荐人手机号格式不正确!",//14
    "该手机号已注册!",//15
    "原始密码错误",//16
    "请输入手机号码",//17
    "请输入原始密码!",//18
    "密码长度6-20位，必须由数字和字母组成，可以包含下划线“_”"//19
]
var getCheckTimeFn=null
//弹出
function fxe_alert(text) {
    $("body").append("<div class='fxe-alert'>" + text + "</div>");
    setTimeout("del()", 3000);
}
function fxe_alert_index(text) {
    $("body").append("<div class='window_sun'>" + text + "</div>");
    setTimeout("del()", 3000);
};
function del() {
    var time = 400;
    $(".fxe-alert").eq(0).hide(time);
    setTimeout("$('.fxe-alert').eq(0).remove()", time)
    setTimeout("$('.window_sun').eq(0).remove()", time)
}
var sy_confirm = {
    //初始化  int =1 弹出提示并返回   int=2 只做返回  alert 失去焦点的时候是否做提示
    init:function(int,alert){
        sy_confirm.type = int
        if(alert){
            $("#phone").blur(function(){
                sy_confirm.phone($("#phone").val())
            })
            $("#code").blur(function(){
                sy_confirm.code($("#code").val())
            })
            $("#pwd").blur(function(){
                sy_confirm.password($("#pwd").val())
            })
            $("#repwd").blur(function(){
                if($("#pwd").val()!=$("#repwd").val()){
                    fxe_alert("两次输入的密码不一致")
                }
            })

        }
    },
    type:1,
    //手机号格式
    phone: function(phone) {
        var msg = false;
        var reg = /^1[0-9]{10}$/;
        if(phone == "") {
            msg = sy_hint[17];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[17]) : ""
        } else if(!reg.test(phone)) {
            msg = sy_hint[8];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[8]) : ""
        } else {
            msg =  true;
        }
        return msg;
    },
    //密码格式验证
    password:function(pas){
        var msg = false;
        var regp = /(?:\d.*_)|(?:_.*\d)|(?:[A-Za-z].*_)|(?:_.*[A-Za-z])|(?:[A-Za-z].*\d)|(?:\d.*[A-Za-z])/
        if (pas== "") {
            msg = sy_hint[2];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[2]) : ""
        } else if (!regp.test(pas)) {
            msg = sy_hint[19];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[19]) : ""
        } else if (pas.length < 4 || pas.length > 20) {//4-20个字符
            msg = sy_hint[11];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[11]) : ""
        } else {
            msg = true;
        }
        return msg;
    },
    //密码 只验证位数 不验证规则
    Password:function(pas){
        var msg = false;
        if (pas== "") {
            msg = sy_hint[18];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[18]) : ""
        } else if (pas.length < 4 || pas.length > 20) {//4-20个字符
            msg = sy_hint[0];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[0]) : ""
        } else {
            msg = true;
        }
        return msg;
    },
    //旧验证码验证（只验证位数 不验证规则）
    oldPassword:function(pas){
        var msg = false;
        if (pas== "") {
            msg = sy_hint[18];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[18]) : ""
        } else if (pas.length < 4 || pas.length > 20) {//4-20个字符
            msg = sy_hint[16];
            sy_confirm.type == 1 ? fxe_alert(sy_hint[16]) : ""
        } else {
            msg = true;
        }
        return msg;
    },
    code:function(code){
        var msg = false;
        if(code.length==0){
            msg = sy_hint[6]
            sy_confirm.type == 1 ? fxe_alert(sy_hint[6]) : ""
        }else if(code.length!=6){
            msg = sy_hint[7]
            sy_confirm.type == 1 ? fxe_alert(sy_hint[7]) : ""
        }else{
            msg=true;
        }
        return msg;
    },
    Code: async function(phone) {
    console.log("发送验证码");
    
        try {
            // 构建签名请求头
            const headers = await buildSignedHeaders('POST', '/sendSmsCode', '');
            
            // 使用 Promise 包装 AJAX
            const result = await new Promise((resolve, reject) => {
                $.ajax({
                    type: "POST",
                    contentType: 'application/x-www-form-urlencoded',
                    headers: headers,
                    data: { mobile: phone },
                    url: "/sendSmsCode",
                    success: function (data) {
                        if(data.status == 0) {
                            sy_confirm.type == 1 ? fxe_alert(data.msg) : "";
                            resolve(data.msg);
                        } else if(data.status == 1){
                            resolve(true);
                        } else {
                            resolve(data.status);
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(new Error(`请求失败: ${error}`));
                    }
                });
            });
            
            return result;
            
        } catch(error) {
            console.error('发送验证码失败:', error);
            sy_confirm.type == 1 ? fxe_alert("发送验证码失败，请重试") : "";
            return 0;
        }
},
    // Code: function(phone) {//获取验证码
        // try {
        //     var r = 0;
        //     $.ajax({
        //         type: "POST",
        //         data: {
        //             mobile: phone
        //         },
        //         url: "/sendSmsCode/",
        //         async: false,
        //         success: function(data) {
        //             if(data.status == 0) {
        //                 r=data.msg
        //                 sy_confirm.type == 1 ? fxe_alert(data.msg) : ""
        //             } else if(data.status==1){
        //                 r=true;
        //             }else{
        //                 r = data.status;
        //             }
        //         },
        //         error: function(e) {
        //             alert(e);
        //         }
        //     });
        // } catch(e) {
        //     console.log(e.message);
        // }
        // return r;
    // },
    confirmCode: function (phone,code) {
        var msg = false;
        $.ajax({
            type: "POST",
            async: false,
            data: { action: "CheckSend", mobile: phone, code: code },
            url: "https://ltapi.fangxiaoer.com/apiv1/base/verifySmsCode",
            success: function (data) {
                if (data.status == "1") {
                    msg = true;
                }
                else {
                    msg = data.msg
                    sy_confirm.type == 1 ? fxe_alert(sy_hint[7]) : ""
                }
            },
            error: function (error) {
                console.log(error);
                msg = "服务器繁忙请稍后重试"
                sy_confirm.type == 1 ? fxe_alert("服务器繁忙请稍后重试") : ""
            }
        });
        return msg;
    },
//是否是会员
    isVIP:function(phone){
        var msg=false
        $.ajax({
            type:"post",
            url:"https://ltapi.fangxiaoer.com/apiv1/base/checkIsMember",
            async:false,
            data:{mobile:phone},
            success:function(data){
                if(data.status==1){
                    msg=true
                    msg = sy_hint[15];
                    sy_confirm.type == 1 ? fxe_alert(sy_hint[15]) : ""
                }
            }
        });

        return msg;
    },
    wait: 60,//倒计时
    timeWait: function() {//倒计时函数
        sy_confirm.wait = 60;
        sy_confirm.time();

    },
    time:function () {
        if (sy_confirm.wait == 0) {
            $(".fxe_validateCode").hide();
            $(".fxe_ReSendValidateCoad").show().html("重新获取");
            $(".fxe_ReSendValidateCoadAgent").show().html("重新获取");
            $(".ReSendCode").show().html("重新获取");
            $(".bnzfTCSendCode").show().html("重新获取")
            sy_confirm.wait = 60;
        } else {
            $(".fxe_validateCode").show().html("在" + sy_confirm.wait + "秒后重发");
            $(".fxe_ReSendValidateCoad").hide();
            $(".fxe_ReSendValidateCoadAgent").hide();
            sy_confirm.wait--;
            getCheckTimeFn=setTimeout("sy_confirm.time()", 1000);
        }
    }
//	isWeiXin: function() {//判断是否是微信  如果不是提示  如果是 可以使用微信支付
//		var ua = window.navigator.userAgent.toLowerCase();
//		if(ua.match(/MicroMessenger/i) == 'micromessenger') {} else {
//			$(".payment").hide();
//			$(".alipay").width("93%");
//			fxe_alert("在微信客户端打开即可使用微信支付");
//		}
//	}

}
