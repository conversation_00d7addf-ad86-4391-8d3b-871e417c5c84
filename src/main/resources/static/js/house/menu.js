/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/12.
 */

    //弹出
    $(function () {
        //查看收藏
        var sc = $("#glzs").val()
        var houseId = projectId;
        if (sc != "") {
            $(".soucang").removeAttr("href");
            $.ajax({
                type: "POST",
                url: "/manageMyCollection",
                data: { sessionId: sc,
                    houseId: houseId,
                    type: 4,
                    methodName: 'checkFavorite'
                },
                dataType: "json",
                success: function (data) {
                    if (data.content == 1) {
                        $(".soucang i").addClass("hover")
                    } else {
                        $(".soucang i").removeClass("hover")
                    }
                }
            });

            $(".soucang").click(function () {
                if ($(".soucang i").attr("class") == "hover") {
                    quxiao(sc, houseId)
                }
                else {
                    tianjia(sc, houseId)
                }
            })

            function quxiao(sc, houseId) {
                $.ajax({
                    type: "POST",
                    url: "/manageMyCollection",
                    data: { sessionId: sc,
                        houseId: houseId,
                        type: 4,
                        methodName: 'cancelFavorite'
                    },
                    dataType: "json",
                    success: function (data) {
                        if (data.status == 1) {
                            $(".soucang i").removeClass("hover")
                        } else {
                            alert("取消失败");
                            window.location.reload();
                        }
                    }
                });
            }

            function tianjia(sc, houseId) {
                $.ajax({
                    type: "POST",
                    url: "/manageMyCollection",
                    data: { sessionId: sc,
                        houseId: houseId,
                        type: 4,
                        methodName: 'newFavorite'
                    },
                    dataType: "json",
                    success: function (data) {
                        if (data.status == 1) {
                            $(".soucang i").addClass("hover")
                        } else {
                            alert("收藏失败");
                            window.location.reload();
                        }
                    }
                });
            }

        }
        //收藏

        //下滑浮窗新需求与原有冲突 2018.04.27
        // house_top();
        // $(window).scroll(function () {
        //     house_top();
        // });
        // function house_top() {
        //     $(".house_move_act").stop()
        //     if ($(document).scrollTop() > 380) {
        //         $(".house_move_act").addClass("houseMove_head")
        //         $(".house_move_act").animate({ "top": "0px" }, 100)
        //     } else {
        //         $(".house_move_act").animate({ "top": "-42px" }, 100)
        //         $(".house_move_act").removeClass("houseMove_head")
        //     }
        // }
    });
