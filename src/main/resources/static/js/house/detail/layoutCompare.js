/**
 * Created by Administrator on 2020/4/23.
 */





function getCookie(name) {
    var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
    if(arr != null) return unescape(arr[2]);
    return null;
}
function setCookie(name, value) {
    var exp = new Date();
    exp.setTime(exp.getTime() +  60 * 60 * 1000); //有效期10分钟
    document.cookie = name + "=" + escape(value) + ";path=/;expires=" + exp.toGMTString();
}

function newSetCookie(value,valueName) {
    var arrayLayId =   getCookie("arrayLayId");
    var arrayLayName =   getCookie("arrayLayName");
    arrayLayId == null || arrayLayId == "" || arrayLayId == undefined ? value : arrayLayId + ',' + value;
    arrayLayName == null || arrayLayName == "" || arrayLayName == undefined ? valueName : arrayLayName + ',' + valueName;
    setCookie("arrayLayId",arrayLayId);
    setCookie("arrayLayName",arrayLayName);
}

function newGetCookie() {
    var arrayLayId = getCookie("arrayLayId").split(",");
    var arrayLayName = getCookie("arrayLayName").split(",");
    $("#showLayout").html("");
    $(".no-compare").hide()
    for (var i = 0 ; i < arrayLayId.length ; i++){
      var layId = arrayLayId[i];
      var layName = arrayLayName[i];
        if (layId !="" && layId != null && layId != undefined){
            readyList = ' ' +
                '<li class="li'+i+'">' +
                '<div class="comparisonOpen2 hover" onclick="chooselayoud('+layId+')" id="open'+layId+'"></div>' +
                '<div class="comparisonSubName" id="'+layId+'">'+layName+'</div>' +
                '<div class="comparisonClose hover" onclick="deletelayoud('+i+')">' +
                '<input type="hidden" value='+layId+' class="inputVal">' +
                '</div>' +
                '<p>'+layName+'</p>' +
                '</li>';
            $(".tyleUl").append(readyList)
        }else {
            typeClear()
        }

    }
}

function readyCookie() {
    var arrayLayId = getCookie("arrayLayId") != "" &&getCookie("arrayLayId") != null &&getCookie("arrayLayId") != undefined ? getCookie("arrayLayId").split(","):[];
   console.log('初始化方法')
    if (arrayLayId.length > 0){
        console.log('初始化11111111')
        $(".n20").show();   // 如果当前已经点击了户型对比就显示右侧悬浮户型对比选项

        newGetCookie();
        /*for (var i = 0 ; i< arrayLayId.length ; i++){
            $(".type-compare-btn1").each(function () {
                var choType1 = $(this).attr("data-layid")
                if( choType1 == arrayLayId[i] ){
                    $(this).hide()
                }
            })
            $(".type-compare-btn2").each(function () {
                var choType2 = $(this).attr("data-layid")
                if( choType2 == arrayLayId[i] ){
                    $(this).show()
                }
            })
        }*/
        var trueType = 0;

        for (var i = 0 ; i< arrayLayId.length ; i++){
            if ( trueType == 0){
                $(".type-compare-btn1").each(function () {
                    var choTypeNum = $(this).attr("data-layid")
                    if( choTypeNum == arrayLayId[i] ){
                        $(this).hide();
                        $(this).parent().find(".type-compare-btn2").show();
                        trueType = 1;
                        return
                    }else {
                        $(this).show();
                        $(this).parent().find(".type-compare-btn2").hide();
                    }
                })
            }else {
                return
            }

         }

    }else {
        console.log('初始化2222222222')
        $(".n20").hide();   // 如果当前没有户型对比就隐藏右侧悬浮户型对比选项
        $(".no-compare").show()
    }
}

$(".type-compare-btn1").click(function(){
    console.log('触发点击方法')
    $(".n20").show();   // 点击加入户型对比时候 就显示右侧悬浮户型对比选项
    var arrayLayId = getCookie("arrayLayId");
    var arrayLayName = getCookie("arrayLayName");
    var newLayId = $(this).attr("data-layid");
    var newLayName = $(this).attr("data-layname");
    if (arrayLayId != null && arrayLayId != '' && arrayLayId != undefined){
        console.log('进入1111111')

        for (var i = 0 ; i < arrayLayId.split(",").length ; i++){
            if (arrayLayId.split(",")[i] == newLayId){
                alert("您已添加此户型");
                return
            }else if (arrayLayId.split(",").length >= 4){
                alert("最多只能对比四个户型");
                return;
            }else{
                var layIdResult = arrayLayId + ','+newLayId;
                var layNameResult = arrayLayName + ','+newLayName;
                setCookie("arrayLayId",layIdResult);
                setCookie("arrayLayName",layNameResult);
                if($(".suspensionIcon .n20 .type-compare-main").css("display")=='none'){
                                    $(".suspensionIcon .n20 .type-compare-main").show();
                }
                $(this).hide()
                $(this).parent().find(".type-compare-btn2").show()
            }
        }
    }else {
        console.log('进入2222222222')

        setCookie("arrayLayId",newLayId);
        setCookie("arrayLayName",newLayName);
        if($(".suspensionIcon .n20 .type-compare-main").css("display")=='none'){
            $(".suspensionIcon .n20 .type-compare-main").show();
        }
        $(this).hide()
        $(this).parent().find(".type-compare-btn2").show()
    }
    newGetCookie();
})

function deletelayoud(liLength) {
    var arrayLayId = getCookie("arrayLayId").split(",");
    var arrayLayName = getCookie("arrayLayName").split(",");
    var newLayId = '';
    var newLayName = '';
    for (var i = 0 ; i < arrayLayId.length ; i++){
        if (i != liLength){
            newLayId = newLayId == null || newLayId == "" || newLayId == undefined ? arrayLayId[i] : newLayId + ',' + arrayLayId[i];
            newLayName = newLayName == null || newLayName == "" || newLayName == undefined ? arrayLayName[i] : newLayName + ',' + arrayLayName[i];
        }
        setCookie("arrayLayId",newLayId);
        setCookie("arrayLayName",newLayName);
        newGetCookie();
        readyCookie()
    }

}

function typeClear() {
    setCookie("arrayLayId","");
    setCookie("arrayLayName","");
    $(".tyleUl").find("li").remove()
    $(".no-compare").show()
    $(".type-compare-btn1").show()
    $(".type-compare-btn2").hide()

}


function clearCookie(name) {
    setCookie(name, "", -1);
}

// function readyCookie() {
//     var rightNum = getCookie("rightLiLength")
//     if( rightNum > 0 ){
//         $(".no-compare").hide()
//         for( var i =1 ; i <= rightNum ; i++){
//             var readyCookie =   getCookie("layId"+i)
//             if(readyCookie != ""){
//                 layId = readyCookie.match(/layId(\S*)layName/)[1];
//                 layName  = readyCookie.split("layName")[1];
//                 $(".type-compare-btn1").each(function () {
//                     var choType1 = $(this).attr("data-layid")
//                     if( choType1 == layId ){
//                         $(this).hide()
//                     }
//                 })
//                 $(".type-compare-btn2").each(function () {
//                     var choType2 = $(this).attr("data-layid")
//                     if( choType2 == layId ){
//                        $(this).show()
//                     }
//                 })
//                 readyList = ' <li class="li'+i+'"><div class="comparisonOpen hover"   onclick="chooseContrastHouse('+layId+')" id="open'+layId+'"></div><div class="comparisonSubName" id="'+layId+'">'+layName+'</div><div class="comparisonClose hover" onclick="deleteContrastHouse('+i+')"><input type="hidden" value='+layId+' class="inputVal"></div><p>'+layName+'</p></li>';
//                 $(".tyleUl").append(readyList)
//             }
//
//         }
//
//     }else {
//         $(".no-compare").show()
//     }
// }
    $(document).ready(function () {
        readyCookie()
        // $(".typeClear").click(function(){//全部清空
        //     var closeNum = getCookie("rightLiLength")
        //     for( var e =1 ; e <= closeNum ; e++){
        //         setCookie('layId'+e, "",-1);
        //     }
        //     setCookie("rightLiLength","")
        //     $(".tyleUl").find("li").remove()
        //     $(".no-compare").show()
        //     $(".type-compare-btn1").show()
        //     $(".type-compare-btn2").hide()
        // })

        //开始对比
        $(".typeBegin").click(function(){
            if($(".comparisonOpen2.hover").length == 0) {
                alert("请选择需要对比的户型");
            }
            if($(".comparisonOpen2.hover").length > 0 && $(".comparisonOpen2.hover").length <= 4) {
                var txt=""
                $(".comparisonOpen2.hover").each(function(){
                    txt+=$(this).parent().find(".comparisonSubName").attr("id")+','
                })
                txt = txt.substring(0,txt.length-1)
                setCookie("arrayLayId", txt);
                window.open("/contrastLayout")

            }
            if($(".comparisonOpen2.hover").length>4) {
                alert("最多勾选4项");
            }
        })
    })

    // $(".type-compare-btn1").click(function(){
    //     $(this).hide()
    //     $(this).parent().find(".type-compare-btn2").show()
    //     var canAdd = "0";
    //     $(".no-compare").hide()
    //     var liLength = $(".tyleUl li").length+1;
    //     if(liLength <=4){
    //         var layId = $(this).attr("data-layId");
    //         var layName = $(this).attr("data-layName")
    //         var a = 0;
    //         if( liLength > 1){
    //
    //             $(".inputVal").each(function(){
    //
    //                 if (a == 0){
    //                     inputVal = $(this).val()
    //                     if(layId == inputVal ){
    //                         canAdd = "1"
    //                         a = 1;
    //                         alert("您已添加此户型")
    //                         return
    //                     }else{
    //                         canAdd = "0"
    //                     }
    //                 }
    //             })
    //         }
    //         if( canAdd == 0 ){
    //             setCookie('layId'+liLength, "layId"+layId+"layName"+layName, { expires: 7 ,path:'/'});
    //             setCookie("rightLiLength",liLength)
    //             if($(".suspensionIcon .n20 .type-compare-main").css("display")=='none'){
    //                 $(".suspensionIcon .n20 .type-compare-main").show();
    //             }
    //             list='<li class="li'+liLength+'"><div class="comparisonOpen hover"   onclick="chooseContrastHouse('+layId+')" id="open'+layId+'"></div><div class="comparisonSubName" id="'+layId+'">'+layName+'</div><div class="comparisonClose hover" onclick="deleteContrastHouse('+liLength+')"><input type="hidden" value='+layId+'  class="inputVal"></div><p>'+layName+'</p></li>'
    //             $(".tyleUl").append(list)
    //         }
    //
    //
    //     }else{
    //         alert("最多只能对比四个户型")
    //     }
    //
    // });

//添加需要对比的戶型
function chooselayoud(houseId) {
    if($("#open"+houseId).hasClass("hover")){
        $("#open"+houseId).removeClass("hover");
    }else if($(".comparisonOpen2.hover").length>=4){
        alert("最多勾选4项")
    }else{
        $("#open"+houseId).addClass("hover");
    }
}

//取消对比
function deletelayoud2(datalayid) {
    $(".tyleUl li").each(function () {
        var rightNum = $(this).find(".comparisonSubName").attr("id")
        if( rightNum == datalayid ){
            var indexNum = $(this).index()
            deletelayoud(indexNum)
        }
    })
}

//删除添加的对比戶型
// function deleteContrastHouse(liLength) {
//     var quxiaoNum = $('.tyleUl .li'+liLength+' .comparisonSubName').attr("id")
//     $(".type-compare-btn1").each(function () {
//         var choType1 = $(this).attr("data-layid")
//         if( choType1 == quxiaoNum ){
//             $(this).show()
//         }
//     })
//     $(".type-compare-btn2").each(function () {
//         var choType2 = $(this).attr("data-layid")
//         if( choType2 == quxiaoNum ){
//             $(this).hide()
//         }
//     })
//
//     setCookie('layId'+liLength, "",-1);
//     var rightNum = getCookie("rightLiLength")
//     setCookie("rightLiLength",rightNum-1)
//     $('.tyleUl .li'+liLength+'').remove()
//     var lishow = $(".tyleUl li").length
//     if(lishow == 0){
//         $(".no-compare").show()
//     }
//     // readyCookie()
// }


