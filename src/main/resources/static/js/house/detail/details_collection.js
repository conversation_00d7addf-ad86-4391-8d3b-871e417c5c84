/**
 * Created by Administrator on 2019/1/10 0010.
 */
$(function () {
    //查看收藏
    var sc = $("#sessionId").val()
    var houseId = $("#projectId").val();
    if (sc != "") {
        $(".soucang").removeAttr("href");
        $.ajax({
            type: "POST",
            url: "/manageMyCollection",
            data: { sessionId: sc, houseId: houseId, type: $("#collectType").val(),methodName:'checkFavorite' },
            dataType: "json",
            success: function (data) {
                if (data.content == 1) {
                    $(".soucang i").addClass("hover")
                } else {
                    $(".soucang i").removeClass("hover")
                }
            }
        });

        $(".soucang").click(function () {
            if ($(".soucang i").attr("class") == "hover") {
                quxiao(sc, houseId)
            }
            else {
                tianjia(sc, houseId)
            }
        })
       //取消收藏
        function quxiao(sc, houseId) {
            $.ajax({
                type: "POST",
                url: "/manageMyCollection",
                data: { sessionId: sc, houseId: houseId, type: $("#collectType").val(),methodName:'cancelFavorite' },
                dataType: "json",
                success: function (data) {
                    if (data.status == 1) {
                        $(".soucang i").removeClass("hover")
                    } else {
                        alert("取消失败");
                        window.location.reload();
                    }
                }
            });
        }
        //添加收藏
        function tianjia(sc, houseId) {
            $.ajax({
                type: "POST",
                url: "/manageMyCollection",
                data: { sessionId: sc, houseId: houseId, type: $("#collectType").val(),methodName:'newFavorite' },
                dataType: "json",
                success: function (data) {
                    if (data.status == 1) {
                        $(".soucang i").addClass("hover")
                    } else {
                        alert("收藏失败");
                        window.location.reload();
                    }
                }
            });
        }

    }
    //收藏

    house_top();
    $(window).scroll(function () {
        house_top();
    });
    function house_top() {
        $(".house_move_act").stop()
        if ($(document).scrollTop() > 380) {
            $(".house_move_act").addClass("houseMove_head")
            $(".house_move_act").animate({ "top": "0px" }, 100)
        } else {
            $(".house_move_act").animate({ "top": "-42px" }, 100)
            $(".house_move_act").removeClass("houseMove_head")
        }
    }
});