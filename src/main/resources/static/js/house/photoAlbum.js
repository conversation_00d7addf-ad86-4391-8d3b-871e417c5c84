var photo = {
	page: 0, //下方缩略图页数 0开始
	ind: 0, //当前图片数 0开始
	maxIndex: 1, //图片数
	time: 4, //自动轮播间隔时间 秒
	Time: 0, //当前轮播间时间 
	imgMinSize: 0, //小图片的宽度（宽度+左边距+共2像素的边框！边框是写死的）
	imgMaxSize: 0, //大图片的宽度
	imgMinProportion: 1,
	imgMaxProportion: 1,
	showInd: 0,//大图的索引值
	init: function(time) {
		//初始化参数
		photo.time = time;
		photo.imgMaxSize = parseInt($(".imgMax ul li").eq(0).width()) + parseInt($(".imgMax ul li").eq(0).css("margin-left"));
		photo.imgMinSize = parseInt($(".imgMin ul li").eq(0).width()) + parseInt($(".imgMin ul li").eq(0).css("margin-left")) + 2;
		photo.imgMaxProportion = parseInt($(".imgMax ul li").eq(0).width()) / parseInt($(".imgMax ul li").eq(0).height());
		photo.imgMinProportion = parseInt($(".imgMin ul li").eq(0).width()) / parseInt($(".imgMin ul li").eq(0).height());
		photo.maxIndex = $(".imgMax img").length;
		//点击放大图片
		$(".photoAlbum .imgMax ul li").click(function(){
			photo.showInd=$(this).index()
			$(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
			$(".bigImgShow ").show()
			photo.showImgSize()
		})
		//房源相册点击放大图片
		$(".photo ul li").click(function(){
			photo.showInd=$(this).index()
			$(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
			$(".bigImgShow ").show()
			photo.showImgSize()
		})
		//大图上一张
		$(".bigImgShow .prev").click(function() {
			if(photo.showInd > 0) {
				photo.showInd--
				$(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
			}
		})
		//大图下一张
		$(".bigImgShow .next").click(function() {
			var ind = parseInt($(".showImg ul").css("margin-left")) / parseInt($(".showImg li").css("width"))
			ind = Math.floor(ind)
			if(photo.showInd <photo.maxIndex-1) {
				photo.showInd++
				$(".showImg ul").css("margin-left", photo.showInd * parseInt($(".showImg li").eq(0).css("width")) * -1 + "px")
			}
		})
		//关闭大图
		$(".bigImgShow .close").click(function(){
			$(".bigImgShow ").hide()
		})
		//轮播开始
		setInterval("photo.timeOut()", 1000);
		//点击小图
		$(".imgMin img").click(function() {
			photo.ind = $(this).parent().index();
			photo.imgMaxMove($(this).parent().index());
			photo.imgMinMove($(this).parent().index());
			photo.Time = 0;
		})
		//上一张
		$(".imgMin span").click(function() {
			photo.imgPrev()
			photo.Time = 0;
		})
		//下一张
		$(".imgMin b").click(function() {
			photo.imgNext()
			photo.Time = 0;
		})
	},
	//下一张
	imgNext: function() {
		if(photo.ind >= photo.maxIndex - 1) {
			photo.imgMaxMove(photo.maxIndex);
			photo.imgMinMove(photo.maxIndex);
		} else {
			photo.ind++
				photo.imgMaxMove(photo.ind);
			photo.imgMinMove(photo.ind);
		}
	},
	//上一张
	imgPrev: function() {
		if(photo.ind <= 0) {
			photo.imgMaxMove(0);
			photo.imgMinMove(0);
		} else {
			photo.ind--;
			photo.imgMaxMove(photo.ind);
			photo.imgMinMove(photo.ind);
		}
	},
	//每次设置索引后需回调 设置外框和上一张下一张的样式
	imgRim: function() {
		if(photo.ind == 0) {
			$(".imgMin span").addClass("hover");
		} else if(photo.ind == photo.maxIndex - 1) {
			$(".imgMin b").addClass("hover");
		} else {
			$(".imgMin b").removeClass("hover");
			$(".imgMin span").removeClass("hover");
		}
		$(".imgMin div ul li").removeClass("hover");
		$(".imgMin div ul li").eq(photo.ind).addClass("hover");
	},
	//小图片移动
	imgMinMove: function(ind) {
		if(ind <= 0) {
			$(".imgMin div ul").stop().animate({ "margin-left": "0px" }, 400);
		} else if(photo.ind == (photo.maxIndex - 2)) {
			$(".imgMin div ul").stop().animate({ "margin-left": (photo.ind - 2) * -photo.imgMinSize + "px" }, 400);
		} else if(photo.ind < (photo.maxIndex - 1)) {
			$(".imgMin div ul").stop().animate({ "margin-left": (photo.ind - 1) * -photo.imgMinSize + "px" }, 400);
		}
		photo.imgRim();
	},
	//大图片移动
	imgMaxMove: function(ind) {
		if(ind <= 0) {
			$(".imgMax ul").stop().animate({ "margin-left": "0px" }, 400);
		} else if(photo.ind < photo.maxIndex) {
			$(".imgMax ul").stop().animate({ "margin-left": photo.ind * -photo.imgMaxSize + "px" }, 400);
		}
	},
	//自动切换
	timeOut: function() {
		photo.Time++;
		if(photo.Time == photo.time) {
			photo.ind++
				if(photo.ind >= photo.maxIndex) {
					photo.ind = 0;
				}
			photo.imgMaxMove(photo.ind);
			photo.imgMinMove(photo.ind);
			photo.Time = 0
		}
	},
	showImgSize:function(){
		var size = parseInt($(".showImg li").eq(0).width()) / parseInt($(".showImg li").eq(0).height())
	$(".showImg li img").each(function(){
		var imgSize = parseInt($(this).width()) / parseInt($(this).height())
	if(size > imgSize) {
		$(this).css({ "height": $(".showImg li").eq(0).height(), "width": "auto" })
		$(this).css({ "margin-left": Math.floor(parseInt($(this).width()) * -0.5) + "px", "margin-top": Math.floor(parseInt($(this).height()) * -0.5) + "px" })
	} else {
		$(this).css({ "height": "auto", "width": "100%" })
		$(this).css({ "margin-left": Math.floor(parseInt($(this).width()) * -0.5) + "px", "margin-top": Math.floor(parseInt($(this).height()) * -0.5) + "px" })
	}
		
	})
	}


}
//图片自适应大小
function imgSize() {
	//下方缩略图
	var imgWidthHeght = $(".imgMin ul li").eq(0).width() / $(".imgMin ul li").eq(0).height()
	$(".imgMin ul li img").each(function() {
		if(Math.floor($(this).height()) < Math.floor($(".imgMin ul li").height())) {
			$(this).css({ "height": "100%", "width": "auto" })
			$(this).css({ "margin-top": Math.floor($(this).height() / 2) * -1 + "px", "margin-left": Math.floor($(this).width() / 2) * -1 + "px" })
		} else {
			$(this).css({ "width": "100%", "height": "auto" })
			$(this).css({ "margin-top": Math.floor($(this).height() / 2) * -1 + "px", "margin-left": Math.floor($(this).width() / 2) * -1 + "px" })
		}
	})
	//滚动大图
	var imgWidthHeght2 = $(".imgMax ul li").eq(0).width() / $(".imgMax ul li").eq(0).height()
	$(".imgMax ul li img").each(function() {
	    if (parseInt($(this).width()) <= parseInt($(this).height())) {
			$(this).css({ "height": "100%", "width": "auto" })
			$(this).css({ "margin-top": Math.floor($(this).height() / 2) * -1 + "px", "margin-left": Math.floor($(this).width() / 2) * -1 + "px" })
		} else {
			$(this).css({ "width": "100%", "height": "auto" })
			$(this).css({ "margin-top": Math.floor($(this).height() / 2) * -1 + "px", "margin-left": Math.floor($(this).width() / 2) * -1 + "px" })
		}
	})
}
