﻿var CommentList = {
    PageIndex: 1,
    PageSize: $("#pigeSize").val(),
    ProjectId: 0,
    MemberID: 0,
    Phone: "",
    UserName: "",
    MaxCount: 0,
    /*ProjectType: $("#ProjectType").val(),*/
    GetList: function (projectId, memberId, phone, userName) {
        CommentList.ProjectId = projectId;
        CommentList.MemberID = memberId;
        CommentList.Phone = phone;
        CommentList.UserName = userName;
        var flasw = $("#checkbox_a3").is(':checked') ? 1: 0;
        var virtues = $("#checkbox_a1").is(':checked') ? 1: 0;
        var huxing = $("#checkbox_a4").is(':checked') ? 1: 0;
        var property = $("#checkbox_a5").is(':checked') ? 1: 0;
        var space = $("#checkbox_a6").is(':checked') ? 1: 0;
        var supporting = $("#checkbox_a7").is(':checked') ? 1: 0;
        var ratio = $("#checkbox_a8").is(':checked') ? 1: 0;
        $.ajax({
            type: "post",
            url: "/getCommentInfo",
            data: {
                Flaws:flasw,
                Virtues:virtues,
                Huxing:huxing,
                Property:property,
                Space:space,
                Supporting:supporting,
                Ratio:ratio,
                isYezhu: $("#IsYezhu").val(),
                isPic: $("#IsPic").val(),
                pageIndex: CommentList.PageIndex,
                pageSize: CommentList.PageSize,
                projectId: CommentList.ProjectId
            },
            scriptCharset: 'utf-8',
            dataType: "json",
            success: function (data) {
                var data = eval(data);
                if (data != null) {
                    CommentList.MaxCount = data.msg;
                    $("#dianpingDom").show();
                    var list = data.content;
                    if(list != null && list != undefined && list != ''){
                        CommentList.BindData(list);
                    }else {
                        $("#listDom").html("<div class='zhanwuziliao'>暂无评价，欢迎发表您的观点</div>");
                    }
                    LjzPage.Init(CommentList.MaxCount, "CommentList.AppList", CommentList.PageSize);
                    PicTanchu();
                }
                else {
                    $("#listDom").html("<div class='zhanwuziliao'>暂无评价，欢迎发表您的观点</div>");
                }

                if (window._bd_share_main) {
                    window._bd_share_main.init();
                }
            }
        });
    //getCommentCount
    },
    SetCount: function (projectId) {
        var flasw = $("#checkbox_a3").is(':checked') ? 1: 0;
        var virtues = $("#checkbox_a1").is(':checked') ? 1: 0;
        var huxing = $("#checkbox_a4").is(':checked') ? 1: 0;
        var property = $("#checkbox_a5").is(':checked') ? 1: 0;
        var space = $("#checkbox_a6").is(':checked') ? 1: 0;
        var supporting = $("#checkbox_a7").is(':checked') ? 1: 0;
        var ratio = $("#checkbox_a8").is(':checked') ? 1: 0;
        $.ajax({
            type: "post",
            url: "/getCommentCount",
            data: {
                Flaws:flasw,
                Virtues:virtues,
                Huxing:huxing,
                Property:property,
                Space:space,
                Supporting:supporting,
                Ratio:ratio,
                //action: "GetCommentCount",
                projectId: projectId
            },
            dataType: "json",
            success: function (data){
                //var entity = eval('('+data+')');
                // alert(data);
                if (data != null) {
                   // var entity = jQuery.parseJSON(data.Json);

                    $("#dianping1").text("用户点评（" + data.AllCount + "）");
                   /* $("#dianping5").text("经纪人评价（" + data.jingjirenCount + "）");
                    $("#dianping2").text("业主评价（" + data.YezhuCount + "）");
                    $("#dianping3").text("非业主评价（" + data.NoCount + "）");*/
                    $("#dianping4").text("带图片点评（" + data.PicCount + "）");
                }
            }
        });
    },
    BindData: function (data) {
        var html = "";
        for (var i = 0; i < data.length; i++) {
            var dom = $("<div ></div>");
            var remark_item = $("<div class='remark_item'></div>");
            var remark_ico = $("<div class='remark_ico'></div>");
            if (data[i].isJinghua == 1) {
                remark_ico.append("<img src='https://static.fangxiaoer.com/web/images/sy/comment/jinghua.png'/>");
            }
            if (data[i].isZhiding == 1) {
                remark_ico.append("<img src='https://static.fangxiaoer.com/web/images/sy/comment/zhiding.png'/>");
            }
            remark_item.append(remark_ico);
            var top=data[i].isYezhu == 1?"":"remark_lTop";
            var remark_l = $("<div class='remark_l  "+top+"'></div>");
            var remark_r = $("<div class='remark_r'></div>");
            var imgsrc = data[i].isYezhu == 3 ? data[i].agentPic:(data[i].sex == 0 ? "https://static.fangxiaoer.com/web/images/ico/sign/men.gif" : "https://static.fangxiaoer.com/web/images/ico/sign/women.gif");
            var tel = data[i].isYezhu == 3 ? "经纪人 "+data[i].agentName:(data[i].isNiming == 1? "匿名":data[i].memberTel);
            var letgHtmlImg="";
            if( data[i].IsOptimum == 1){
                letgHtmlImg="<img class='Best' src='https://static.fangxiaoer.com/web/images/sy/house/consultBest.png'/>"
            }
            var remark_phone="";
            var leftHtml = "<img class='userImg' s src='" + imgsrc + "'/>" +letgHtmlImg+
                "<div class='remark_l_info'>" +
                "<p class='remark_phone'>" + tel + "</p>" +
                "</div>";
            if (data[i].isYezhu == 1) {
                leftHtml += "<p>购买项目：" + data[i].projectName + "</p>" +
                "<p>购买价格：" + data[i].price + "万</p>" +
                "<p>购买面积：" + data[i].area + "m²</p>" +
                "<p>购买时间：" + data[i].cyear + "-" + data[i].cmonth + "</p>" +
                "<p>物业类型：" + data[i].propertyType + "</p>";
            }
            remark_l.html(leftHtml);
            var rightHtml = "";
            if (data[i].score != null && data[i].memberType == 2) {
                var scores = Math.ceil(data[i].score/5);

                rightHtml +="<div class='consultStar consultStar"+scores+"'>满意：<span>"+data[i].scoreSatisfaction+"</span>  地段：<span>"+data[i].scoreSection+"</span>  交通：<span>"+data[i].scoreTraffic+"</span>  配套：<span>"+data[i].scoreSupporting+"</span>  环境：<span>"+data[i].scoreEnvironment+"</span></div>"
            }
            if (data[i].virtues && data[i].virtues!="") {
                rightHtml += "<p><b class='colorf05050'>综合：</b>" + data[i].virtues + "</p>";
            }
            if (data[i].flaws && data[i].flaws != "") {
                rightHtml += "<p><b class='color64a014'>缺点：</b>" + data[i].flaws + "</p>";
            }
            if (data[i].huxing && data[i].huxing != "") {
                rightHtml += "<p><b>户型：</b>" + data[i].huxing + "</p>";
            }
            if (data[i].property && data[i].property != "") {
                rightHtml += "<p><b>物业：</b>" + data[i].property + "</p>";
            }
            if (data[i].Space && data[i].Space != "") {
                rightHtml += "<p><b>车位：</b>" + data[i].Space + "</p>";
            }
            if (data[i].supporting && data[i].supporting != "") {
                rightHtml += "<p><b>配套：</b>" + data[i].supporting + "</p>";
            }
            if (data[i].ratio && data[i].ratio != "") {
                rightHtml += "<p><b>性价比：</b>" + data[i].ratio + "</p>";
            }if (data[i].memberType == 2 && data[i].isYezhu == 3) {
                rightHtml += "<a class='consultZixun agentUrl'  target='_blank' data-toggle='modal'  href='#login'><img src='https://static.fangxiaoer.com/web/images/sy/house/consultZixun.png' alt='我要咨询'><input type='hidden' class='agentId' value=" + data[i].memberID + "><input type='hidden' class='projectId' value=" + data[i].projectId + "></a>"
            }
            var cl = $("<div class='cl'></div>");
            remark_r.html(rightHtml);
            var imgList = data[i].pic;
            if (imgList && imgList.length > 0) {
                remark_r.append(CommentList.BindPic(imgList));
            }
            var replyList = data[i].reply;
            // remark_r.append("<div style='clear: both'></div>")
            remark_r.append(CommentList.BindReply(replyList, data[i].commentId,data[i].addTime));
            remark_item.append(remark_l);
            remark_item.append(remark_r, cl);
            dom.append(remark_item);
            html += dom.html();

        }
        if($("#pigeSize").val()==3){
            html+="<div class='remark_more'><a href='/house/appraise/"+$("#ProjectId").val()+"-"+$("#ProjectType").val()+".htm'>查看全部评价></a></div>"
        }
        $("#listDom").html(html);

        CommentList.BindSubmitReply();
        if(!($("#sessionId").val()=="" || $("#sessionId").val()== null || $("#sessionId").val()== undefined)){
            $(".agentUrl").removeAttr("href")
        }
        $(".remark_item").each(function(){
            var remark_itemHright=$(this).find(".remark_l").height()>$(this).find(".remark_r").height()?$(this).find(".remark_l").height():$(this).find(".remark_r").height()
            $(this).css("min-height",remark_itemHright+"px")
            $(this).find(".remark_l").css("min-height",remark_itemHright+"px");
            $(this).find(".remark_r").css("min-height",remark_itemHright+"px")
        })
    },
    BindPic: function (data) {
        var picDom = $("<div class='ck_pic'></div>");
        var num = $("<span>共<i>" + data.length + "</i>张图片</span>");
        var ul = $("<ul></ul>");
        for (var i = 0; i < data.length; i++) {
            ul.append("<li><img src='"+ data[i].smallImageUrl + "' alt=''></li>");
        }
        picDom.append(ul);
        picDom.append(num);
        return picDom;
    },
    BindFenXiang: function () {
        window._bd_share_config = { "common": { "bdSnsKey": {}, "bdText": "", "bdMini": "2", "bdMiniList": false, "bdPic": "", "bdStyle": "0", "bdSize": "16" }, "share": {} }; with (document) 0[(getElementsByTagName('head')[0] || body).appendChild(createElement('script')).src = 'http://bdimg.share.baidu.com/static/api/js/share.js?v=89860593.js?cdnversion=' + ~(-new Date() / 36e5)];
    },
    BindReply: function (data, commentId,addTime) {
        var count = 0;
        if (data && data.length > 0) {
            count = data.length;
        }
        var html =
            "<p class='remark_addTime'>" + addTime + " 发表</p><div class='cl'></div>"+
            "<div class='remark_discuss'>" +
                        "<span class='replay_btn' style='float:right'>回复（" + count + "）</span>" +
            // "<div class='bdsharebuttonbox' style='display:inline;float: right;margin-right: 20px;'><a href='#' class='bds_more' data-cmd='more' style='background: url(https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon2.jpg) no-repeat 0 1px;    padding-left: 22px;margin-right: 0;padding-top: 2px;'>分享</a></div>" +
                        //"<span><img src='https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon2.jpg'/>分享</span>" +
                        "<div class='cl'></div><div class='remark_reply'>" +
                        "<textarea rows='1'></textarea>" +
                        "<label>回复 ：</label>" +
                        "<input type='button' value='提交回复' class='SubmitReply' data-id='" + commentId + "'/>" +
                        "<div class='cl'></div>" +
                        "<img src='https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg'/>" +
                        "</div>" +
	                "</div>";
        if (data && data.length > 0) {
            html +="<div style='clear: both'></div>"
            for (var i = 0; i < data.length; i++) {
                html += "<div class='remark_reply_as'>"+
                            "<span>" + data[i].phone + "：</span>　" + data[i].replyContent + "　<span>" + data[i].addTime + "</span>" +
                            "<p class='replay_btn'>回复</p>"+
                            "<div class='remark_reply'>"+
                                "<textarea rows='1'></textarea>"+
                                "<label>回复 ：</label>"+
                               "<input type='button' value='提交回复' class='SubmitReply' data-id='" + commentId + "'/>" +
                                "<div class='cl'></div>"+
                                "<img src='https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon1.jpg'/>" +
                             "</div>"+
                        "</div>";
            }
        }
        return html;
    },
    BindSubmitReply: function () {
        $(".SubmitReply").click(function () {
            var sessionId = $("#glzs").val();
            if(sessionId != null && sessionId != undefined && sessionId != ''){
                var text = $(this).prev().prev().val();
                var params  = {
                    action: "AddReply",
                    MemberID: CommentList.MemberID,
                    ProjectId: CommentList.ProjectId,
                    Phone: CommentList.Phone,
                    UserName: CommentList.UserName,
                    content: text,
                    commentId: $(this).attr("data-id"),
                    sessionId:sessionId
                };
                $.ajax({
                    type: "POST",
                    url: "/saveReply",
                    data: JSON.stringify(params),
                    headers: {
                        'Content-Type': 'application/json;charset=utf-8'
                    },
                    dataType: "json",
                    success: function (data) {
                        if (data && data.status == 1) {
                            Alertljz.ShowAlert("回复成功，请耐心等待审核！", "https://my.fangxiaoer.com/reply");
                        } else {
                            Alertljz.ShowAlert(data.msg);
                        }

                    }
                });
            } else {
                $(".modal-backdrop").show();
                $("#login").show();
                //Alertljz.ShowAlert("登录以后才可以回复哦!");
            }

        });
    },

    AppList: function (pageIndex) {
        if (pageIndex <= LjzPage.MaxPage) {
            var flasw = $("#checkbox_a3").is(':checked') ? 1: 0;
            var virtues = $("#checkbox_a1").is(':checked') ? 1: 0;
            var huxing = $("#checkbox_a4").is(':checked') ? 1: 0;
            var property = $("#checkbox_a5").is(':checked') ? 1: 0;
            var space = $("#checkbox_a6").is(':checked') ? 1: 0;
            var supporting = $("#checkbox_a7").is(':checked') ? 1: 0;
            var ratio = $("#checkbox_a8").is(':checked') ? 1: 0;
            $.ajax({
                type: "post",
                url: "/getCommentInfo",
                data: {
                    Flaws:flasw,//缺点
                    Virtues:virtues,//优点
                    Huxing:huxing,
                    Property:property,//物业
                    Space:space,//车位
                    Supporting:supporting,//配套
                    Ratio:ratio,//性价比
                    isYezhu: $("#IsYezhu").val(),
                    isPic: $("#IsPic").val(),
                    pageIndex: pageIndex,
                    pageSize: CommentList.PageSize,
                    projectId: CommentList.ProjectId,
                    memberType:$("#memberType").val(),
                },
                dataType: "json",
                success: function (data) {
                    if (data != null ) {
                        var list = data.content;
                        CommentList.BindData(list);
                        LjzPage.PageCreate(pageIndex);
                        PicTanchu();
                    } else {
                        $("#listDom").html("<div class='zhanwuziliao'>暂无评价，欢迎发表您的观点</div>");
                    }
                    if (window._bd_share_main) {
                        window._bd_share_main.init();
                    }
                    if ($("#LjzPage")) {
                        var offset = $("#LjzPage").offset();
                        offset.top = offset.top - 360;
                        scrollOffset(offset);
                    }

                }
            });
        }
    }
};
function scrollOffset(scroll_offset) {
    $("body,html").animate({
        scrollTop: scroll_offset.top - 70
    }, 0);
}
function PicTanchu() {
    var width = 0, len;
    var which1 = 0;
    $('.ck_pic li').unbind("click");
    $(".ck_pic li").click(function () {
        $(this).parent("ul").addClass("isMe");
        len = $(".ck_pic .isMe li").length;
        which1 = $(this).index();
        $(".bigPic").html("");
        cPic(which1);
        $(".tanPic").fadeIn();
        $(".picPre,.picNext,.blackBg").show();
        panDuan(which1, len);

    });
    $('.picNext').unbind("click");
    $(".picNext").click(function () {
        $(".picPre").show();
        ++which1;
        panDuan(which1, len);
        cPic(which1);
    });
    $('.picPre').unbind("click");
    $(".picPre").click(function () {
        $(".picNext").show();
        --which1;
        panDuan(which1, len);
        cPic(which1);
    });
    $('.bigPicClose').unbind("click");
    $(".bigPicClose").click(function () {
        $(".tanPic").fadeOut();
        $(".blackBg").hide();
        $(".bigPic").html("");
        /*$(".ck_pic ul").removeClass("isMe");*/
        $(".ck_pic ul").attr("class", "noMe");
    });

}

function cPic(which) {
    var address = $(".ck_pic .isMe li").eq(which).find("img").attr("src").replace("middle", "big");
    //var address = $(".ck_pic .isMe li").eq(which).find("img").attr("src")
    // var address = $(".fy li").eq(which).find("img").attr("src").replace("middle", "big");
    $(".bigPic").html("").html("<img/>");
    $(".bigPic img").attr("src", address).fadeIn();
}

function panDuan(w, l) {
    if (l== 1) {
        $(".picPre").hide();
        $(".picNext").hide();
    } else {
        if (w <= 0) {
            $(".picPre").hide();
        } else if (w >= (l - 1)) {
            $(".picNext").hide();
        }
    }


}

$(".agentUrl").live("click",function(){
    var agentId = $(this).find(".agentId").val();
    if (agentId == undefined) {

    }
    if($("#sessionId").val()=="" || $("#sessionId").val()== null || $("#sessionId").val()== undefined){
//                window.location.href=window.location.href+"#login"
    }else{
        $.ajax({
            type: "post",
            url: "/getAgentInfo",
            data: {
                agentId:agentId,
            },
            scriptCharset: 'utf-8',
            dataType: "json",
            success: function (data) {
                /*var data = eval(data);*/
                var content = data.content;
                if (data.status == 1) {
                    //弹出框复制
                    $("#AgentId").text(content.MemberID);
                    $("#AgentPic").attr("src",content.agentPic);
                    $("#AgentName").text(content.agentName);
                    $("#AgentAskCount").text(content.askCount);
                    $("#AgentShop").text(content.IntermediaryName);
                    //显示弹出框
                    $(".consultAlert").show();
                }
                else {
                }
            }
        });
    }
})

