﻿var AskLjzPage = {
    MaxCount: 0,
    MethodName:"",
    PageSize: 0,
    MaxPage: 0,
    ThisPage:1,
    minPage:1,
    toPage:10,
    Init: function (maxCount,methodName, pagesize) {
        AskLjzPage.MethodName = methodName;
        AskLjzPage.MaxCount = maxCount;
        AskLjzPage.PageSize = pagesize;
        AskLjzPage.PageCreate(1);
    },   
    PageCreate: function (pageindex) {
        if (AskLjzPage.MaxCount % AskLjzPage.PageSize == 0) {
            AskLjzPage.MaxPage =  parseInt(AskLjzPage.MaxCount / AskLjzPage.PageSize);
        } else {
            AskLjzPage.MaxPage = parseInt(AskLjzPage.MaxCount / AskLjzPage.PageSize + 1);
        }
        AskLjzPage.ThisPage = pageindex;
        var n =  Math.floor((AskLjzPage.ThisPage-1)/10)
        if(n>=1){
            AskLjzPage.minPage = n * 10+1;
            AskLjzPage.toPage = 10+ AskLjzPage.minPage-1;
        }else {
            AskLjzPage.minPage = 1;
            AskLjzPage.toPage = 10;
        }
        if(AskLjzPage.toPage >AskLjzPage.MaxPage){
            AskLjzPage.toPage = AskLjzPage.MaxPage;
        }
        var ye = "";
        var tou = "";
        var wei = "";
        switch (pageindex) {
            case 1:
                tou+="";
                if (AskLjzPage.MaxPage == 1) {
                    wei += "";
                } else {
                    if(AskLjzPage.toPage < AskLjzPage.MaxPage ){
                        wei += "<a style='margin-right:5px;cursor:pointer;' onclick='"+AskLjzPage.MethodName+"(" + (AskLjzPage.toPage + 1) + ")'>...</a>";
                    }
                    wei += "<a href='javascript:void(0)' style='margin-right:5px;' onclick='" + AskLjzPage.MethodName + "(" + (AskLjzPage.ThisPage + 1) + ")'>下一页</a>";
                    // wei += "<a href='javascript:void(0)' style='margin-right:5px;' onclick='" + AskLjzPage.MethodName + " (" + AskLjzPage.MaxPage + ")'>尾页</a>";
                }
                
         
            break ;
            case AskLjzPage.MaxPage:
                tou += "<a href='javascript:void(0)' style='margin-right:5px;'  onclick='" + AskLjzPage.MethodName + "(1)'>首页</a>" +
                "<a href='javascript:void(0)' style='margin-right:5px;' onclick='"+AskLjzPage.MethodName+"(" + (AskLjzPage.ThisPage - 1) + ")'>上一页</a>";
                if(AskLjzPage.minPage>10){
                    tou += "<a style='margin-right:5px;cursor:pointer;' onclick='"+AskLjzPage.MethodName+"(" + (AskLjzPage.minPage - 1) + ")'>...</a>";
                }
                // wei += "<a disabled='disabled' style='margin-right:5px;' onclick='"+AskLjzPage.MethodName+"(" + (AskLjzPage.ThisPage + 1) + ")'>下一页</a>";
                // wei += "<a disabled='disabled' style='margin-right:5px;' onclick='"+AskLjzPage.MethodName+"(" + AskLjzPage.MaxPage + ")'>尾页</a>";
                wei = "";
                break;
            default:
                tou += "<a href='javascript:void(0)' style='margin-right:5px;' onclick='"+AskLjzPage.MethodName+"(1)'>首页</a>" +
                                 "<a href='javascript:void(0)' style='margin-right:5px;' onclick='"+AskLjzPage.MethodName+"(" + (AskLjzPage.ThisPage - 1) + ")'>上一页</a>";
                if(AskLjzPage.minPage>10){
                    tou += "<a style='margin-right:5px;cursor:pointer;' onclick='"+AskLjzPage.MethodName+"(" + (AskLjzPage.minPage - 1) + ")'>...</a>";
                }
                if(AskLjzPage.toPage < AskLjzPage.MaxPage ){
                    wei += "<a style='margin-right:5px;cursor:pointer;' onclick='"+AskLjzPage.MethodName+"(" + (AskLjzPage.toPage + 1) + ")'>...</a>";
                }
                wei += "<a style='margin-right:5px;cursor:pointer;' onclick='"+AskLjzPage.MethodName+"(" + (AskLjzPage.ThisPage + 1) + ")'>下一页</a>";
                // wei += "<a style='margin-right:5px;cursor:pointer;' onclick='"+AskLjzPage.MethodName+"(" + AskLjzPage.MaxPage + ")'>尾页</a>";
                break;
        }
        for (var i = AskLjzPage.minPage; i <= AskLjzPage.MaxPage && i<=AskLjzPage.toPage; i++) {
            if(AskLjzPage.MaxPage == 1){
                ye == "";
            } else if (i  == pageindex) {
                ye += "<span style='margin-right:5px;font-weight:Bold;color:red;' class='thispage'>" + (i) + "</span>";
            }
            else {
                ye += "<a href='javascript:void(0)' style='margin-right:5px;' onclick='"+AskLjzPage.MethodName+"(" + (i ) + ")'>" + (i) + "</a>";
            }
        }
        var html = tou + ye + wei;
        $("#AskList_Pager1").html(html);
    }
};