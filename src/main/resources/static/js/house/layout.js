$(function () {
    /* 样式部分加载 */
    var l_src=$(".banner_wei .left_wei img").attr("src")
    $(".banner_wei .left_wei").css("background-image","url("+l_src+")")
    var r_src=$(".banner_wei .right_wei img").attr("src")
    $(".banner_wei .right_wei").css("background-image","url("+r_src+")")
    var m_src=$(".banner_wei .Middle_wei img").attr("src")
    $(".banner_wei .Middle_wei").css("background-image","url("+m_src+")")
    $(".big_wei .content_box .border  .btn .lunbo").width($(".big_wei .content_box .border .btn ul li").length*160)
    var ind = $(".btn ul").children().length;
    var num0 = $(".btn .S").index();
    /* 判断页数是否有分页决定不同样式 */
    if (ind <= 7) {
        $("#right_btn").css("display", "none");
        $("#left_btn").css("display", "none");
        $(".Select").css("left", num0*160);
        $(".btn_box").css("border","1px");
        $(".show").css("float","left").css("border","1px solid #ededed");
    }else{
        var num1 = Math.floor(num0/7);
        var num2 = ind - (num1 + 1)*7;
        var num3 = num0 % 7;
        var left_info = num1 * 7 * 160;
        if(ind % 7 != 0){
            $(".btn li:last").css("border-right","1px solid #ededed");
        }
        $(".lunbo").animate({left: -left_info});
        if(num1 == 0){
            $("#left_btn").removeClass("left_click");
        }else{
            $("#left_btn").addClass("left_click");
        }
        $(".Select").css("left", num3*159 + 29);
        if(num2 <= 0){
            $("#right_btn").removeClass("right_click");
        }else{
            $("#right_btn").addClass("right_click");
        }
    }
    /* 当前页标志 */
    var i = 0;
    /* 前一页 */
    $(".left_click").live("click",function () {
        i--;
        $(".lunbo").animate({left: -(num1+ i)* 7 * 160});
        $("#right_btn").addClass("right_click");
        if(num1 + i <= 0){
            $("#left_btn").removeClass("left_click");
        }
        if(i == 0){
            $(".Select").show()
        }else{
            $(".Select").hide();
        }
    });
    /* 后一页 */
    $(".right_click").live("click",function () {
        i++;
        $(".lunbo").animate({left: - (num1 + i) * 7 * 160});
        $("#left_btn").addClass("left_click");
        var ind_value = Math.floor(ind/7) + (ind%7 == 0 ? 0 : 1);
        if(num1 + 1 + i >= ind_value){
            $("#right_btn").removeClass("right_click");
        }
        if(i == 0){
            $(".Select").show()
        }else{
            $(".Select").hide();
        }
    })



    var img_w=$(".big_wei .content_box .information .photo .lunbo img").width()
    $(".big_wei .content_box .information .photo .lunbo").width(img_w*($(".big_wei .content_box .information .photo .lunbo img").length+1))
    var img_x=$(".big_wei .content_box .information .photo .lunbo img").length
    $(".big_wei .content_box .information .photo h1 .all").text($(".big_wei .content_box .information .photo .lunbo img").length)
    $(".big_wei .content_box .information .photo").mouseenter(
        function () {
            if (img_x>1){
                $(".big_wei .content_box .information .photo .left_btn").show()
                $(".big_wei .content_box .information .photo .right_btn").show()
            }
        }
    )
    $(".big_wei .content_box .information .photo").mouseleave(
        function () {
            $(".big_wei .content_box .information .photo .left_btn").hide()
            $(".big_wei .content_box .information .photo .right_btn").hide()
        }
    )


    var index_x=0
    var layer_x=0
    $(".layer_box .layer h1 .all").text($(".big_wei .content_box .information .photo .lunbo img").length)
    $(".big_wei .content_box .information .photo .lunbo .cl").before($(".big_wei .content_box .information .photo .lunbo img:first").clone())
    $(".big_wei .content_box .information .photo .left_btn").click(
        function (e) {
            e.stopPropagation();
            if (index_x==0){
                index_x=$(".big_wei .content_box .information .photo .lunbo img").length-1
                layer_x=index_x
                $(".big_wei .content_box .information .photo h1 .index").text(index_x)
                $(".layer_box table .layer .index").text(index_x)
                $(".big_wei .content_box .information .photo .lunbo").css("left",-index_x*400)
                index_x--
                layer_x=index_x
                $(".big_wei .content_box .information .photo .lunbo").animate({left:-index_x*400},300)
                $(".layer_box .layer .photo .lunbo").animate({left:-layer_x*600},300,function(){
                    if(layer_x==$(".layer_box .layer .photo ul li").length-1){
                        $(".layer_box .layer .photo .lunbo").css("left",0)
                        layer_x=0
                    }
                })
            }else{
                if(index_x>0){
                    index_x--
                    layer_x=index_x
                    $(".big_wei .content_box .information .photo h1 .index").text(index_x+1)
                    $(".layer_box table .layer .index").text(index_x+1)
                    $(".big_wei .content_box .information .photo .lunbo").animate({left:-index_x*400},300)
                    $(".layer_box .layer .photo .lunbo").animate({left:-layer_x*600},300,function(){
                        if(layer_x==$(".layer_box .layer .photo ul li").length-1){
                            $(".layer_box .layer .photo .lunbo").css("left",0)
                            layer_x=0
                        }
                    })

                }
            }
        }
    )
    $(".big_wei .content_box .information .photo .right_btn").click(
        function (e) {
            e.stopPropagation();
            if(index_x<$(".big_wei .content_box .information .photo .lunbo img").length-1){
                index_x++
                layer_x=index_x
                if(index_x==$(".big_wei .content_box .information .photo .lunbo img").length-1){

                    $(".big_wei .content_box .information .photo h1 .index").text(1)
                    $(".layer_box table .layer .index").text(1)
                }else{
                    $(".big_wei .content_box .information .photo h1 .index").text(index_x+1)
                    $(".layer_box table .layer .index").text(index_x+1)
                }
                $(".big_wei .content_box .information .photo .lunbo").animate({left:-index_x*400},300,function(){
                    if(index_x==$(".big_wei .content_box .information .photo .lunbo img").length-1){
                        $(".big_wei .content_box .information .photo .lunbo").css("left",0)
                        index_x=0
                        layer_x=index_x
                    }
                })
                $(".layer_box .layer .photo .lunbo").animate({left:-layer_x*600},300,function(){
                    if(layer_x==$(".layer_box .layer .photo ul li").length-1){
                        $(".layer_box .layer .photo .lunbo").css("left",0)
                        layer_x=0
                    }
                })
            }
        }
    )

    /* 图片放大及轮播功能 */
    $(".layer_box .layer .photo .lunbo").width(($(".layer_box .layer .photo ul li").length+1)*600)
    $(".layer_box .layer .photo .lunbo ul li:last").after($(".layer_box .layer .photo .lunbo ul li:first").clone())

    $(".layer_box .layer .right_btn img").click(
        function(){

            if(layer_x<$(".layer_box .layer .photo ul li").length-1){
                layer_x++
                if(layer_x==$(".layer_box .layer .photo ul li").length-1){
                    $(".layer_box table .layer .index").text(1)
                }else{
                    $(".layer_box table .layer .index").text(layer_x+1)
                }

                $(".layer_box .layer .photo .lunbo").animate({left:-layer_x*600},300,function(){
                    if(layer_x==$(".layer_box .layer .photo ul li").length-1){
                        $(".layer_box .layer .photo .lunbo").css("left",0)
                        layer_x=0
                        $(".layer_box table .layer .index").text(layer_x+1)
                    }
                })

            }
        }
    )
    /* 点击查看大图 */
    $(".layer_box .layer .left_btn img").click(
        function(){

            if(layer_x==0){
                $(".layer_box .layer .photo .lunbo").css("left",-($(".layer_box .layer .photo ul li").length-1)*600)
                layer_x=$(".layer_box .layer .photo ul li").length-1
                layer_x--
                $(".layer_box .layer .photo .lunbo").animate({left:-(layer_x)*600},300)
                $(".layer_box table .layer .index").text(layer_x+1)
            }else{
                layer_x--
                $(".layer_box .layer .photo .lunbo").animate({left:-(layer_x)*600},300)
              if(layer_x==0){
                  $(".layer_box table .layer .index").text(1)
              }else{
                  $(".layer_box table .layer .index").text(layer_x+1)
              }
            }
        }
    )



    $(".big_wei .content_box .information .photo").click(
        function(){
            $(".layer_box").show()
        }
    )
    $(".layer_box table .layer .close").click(
        function(){
            layer_x=index_x
            $(".layer_box .layer .photo .lunbo").css("left",-(layer_x)*600)
            $(".layer_box").hide()
            if(layer_x==0){
                $(".layer_box table .layer .index").text(1)
            }else{
                $(".layer_box table .layer .index").text(layer_x+1)
            }

        }
    )
    $(".layer_box").click(
        function(){
            $(".layer_box").hide()
        }
    )
    $(".layer_box table .layer").click(
        function(e){
            e.stopPropagation();
        }
    )
})