$(function(){




    //右侧列表
    list(rel=101);
    function list(rel){
        var h=1, tex="",li,t1,list="'list'",t2,li_house=""
        $(".xiala").html("")
        for(var i=1;i<6;i++){
            li=""
            s=0;
            for(var t=0;t<onsell.length;t++){
                for(var b=0;b<onsell[t].BuildInfo.length;b++){
                    if(onsell[t].BuildInfo[b].RoomType==i){
                        if(rel==onsell[t].Staging || rel=="100"){
                            li=li+"<li><a href='/house/listd/"+onsell[t].BuildInfo[b].LayoutUrl+"' target='_blank'><p class='w110'>"+onsell[t].BuildInfo[b].RoomName+"</p><p>"+onsell[t].BuildInfo[b].RoomType+"室"+onsell[t].BuildInfo[b].HallType+"厅"+onsell[t].BuildInfo[b].GuardType+"卫</p><p>"+onsell[t].BuildInfo[b].Area+"平</p></a></li>";
                            s++
                        }
                    }
                }
                if(rel==101){
                    for(var b=0;b<onsell[t].SpecialRoom.length;b++){
                        if(onsell[t].SpecialRoom[b].RoomType==i){
                            li=li+"<li><a href='/house/room/"+onsell[t].SpecialRoom[b].RoomUrl+"' target='_blank'><p class='w110'>"+onsell[t].SpecialRoom[b].RoomLocation+"</p><p>"+onsell[t].SpecialRoom[b].Area+"平</p><p>"+onsell[t].SpecialRoom[b].PrePrice.toFixed(2)+"万</p></a></li>";
                            s++
                        }
                    }
                }
            }
            if(li!=""){

                if(rel==101){
                    t2="<li><p>房源位置</p><p>面积</p><p>价格</p></li>";
                    tt="<div id=\"list"+h+"\" onclick=\"setTab("+list+","+h+",8)\"  class=\"qh\">"+i+"室("+s+"户)</div>"
                }else{
                    t2="<li><p>户型楼栋</p><p>厅室</p><p>面积</p></li>";
                    tt="<div id=\"list"+h+"\" onclick=\"setTab("+list+","+h+",8)\"  class=\"qh\">"+i+"室("+s+"户)</div>"
                }
                t1="<div id='con_list_"+h+"' class='xiala_gd hid'><ul>aaaaa</ul></div>";
                $(".xiala").append(tt);
                $("#list"+h).after(t1);
                t2=t2+li
                $("#con_list_"+h).find("ul").html(t2);
                h++
            }
        }
        $("#list1").addClass("hover");
        $("#con_list_1").show();
        gaodu();
    }
    function gaodu(){
        var v = $(".xiala_gd").length;
        switch (v) {
            case 1: $(".xiala_gd").height(322); break;
            case 2: $(".xiala_gd").height(289); break;
            case 3: $(".xiala_gd").height(256); break;
            case 4: $(".xiala_gd").height(223); break;
            case 5: $(".xiala_gd").height(190); break;
        }
    }

//定位图片位置
if($(".pro_name p").html()=="龙湖西府原著"){
	$("#worldMap").css("top","-154px")
}


})
