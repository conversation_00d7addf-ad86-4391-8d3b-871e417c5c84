var bdMap = {
    id:0,
    houselng: 0,
    houselat: 0,
    radius: 1000, //搜索半径
    suofa: 15,//缩放比例
    openInfoList: [],//数据集合
    bdmap: undefined,
    bdpoit: {},
    bdtitle: "",
    bdcontent: "",
    address: "",
    city: "沈阳",
    init: function (divid, entity) {
        bdMap.houselng = entity.houselng;
        bdMap.houselat = entity.houselat;
        bdMap.radius = entity.radius;
        bdMap.suofa = entity.suofa;
        bdMap.bdtitle = entity.bdtitle;
        bdMap.bdcontent = entity.bdcontent;
        bdMap.address = entity.address;
        bdMap.city = entity.city;
        bdMap.id = entity.id;
        if (bdMap.houselng * 1 == 0 || bdMap.houselat * 1 == 0) {
            bdMap.AddressToPoint(bdMap.address, bdMap.city, divid);//没有坐标的情况下从地址你想解析坐标
        }
        else {
            bdMap.showMap(divid);
        }
        // bdMap.searechMapList('公交',0)
        // bdMap.searechMapList('地铁',0)
        // bdMap.searechMapList('学校',1)
        // bdMap.searechMapList('商场',2)
        // bdMap.searechMapList('医院',3)
        // bdMap.searechMapList('银行',4)
        // bdMap.searechMapList('其他',5)
    },
    AddressToPoint: function (address, city, divid) {
        var myGeo = new BMap.Geocoder();
        // 将地址解析结果显示在地图上,并调整地图视野
        myGeo.getPoint(address, function (point) {
            if (point) {
                bdMap.houselng = point.lng;
                bdMap.houselat = point.lat;
                bdMap.showMap(divid);
            }
        }, city);
    },
    showMap: function (divid) {
        $("#" + divid).html("");
        openInfoWinFuns = []; bdMarkers = [];
        if (bdMap.bdmap == undefined) {
            bdMap.bdmap = new BMap.Map(divid, { enableMapClick: false });
        }
        bdMap.bdmap.clearOverlays();

        bdMap.bdpoit = new BMap.Point(bdMap.houselng, bdMap.houselat);
        bdMap.bdmap.centerAndZoom(bdMap.bdpoit, bdMap.suofa);
        //bdMap.bdmap.enableScrollWheelZoom(true); //滚轮缩放
        bdMap.bdmap.addControl(new BMap.NavigationControl()); //缩放尺
        bdMap.bdmap.addControl(new BMap.ScaleControl()); //比例尺
        bdMap.bdmap.enableInertialDragging(true); //惯性拖拽
        //bdMap.bdmap.addControl(new BMap.MapTypeControl({ anchor: BMAP_ANCHOR_TOP_RIGHT }));    //左上角，默认地图控件
        //bdMap.bdmap.setCurrentCity(bdMap.city);   //由于有3D图，需要设置城市哦
        if (bdMap.houselng * 1 == 0 || bdMap.houselat * 1 == 0) {
            bdMap.AddressToPoint(bdMap.address, bdMap.city);//没有坐标的情况下从地址你想解析坐标
        }

        //bdMap.addmarker(bdMap.bdpoit, bdMap.bdtitle, bdMap.bdcontent);
        bdMap.addnewmarker({ lng: bdMap.houselng, lat: bdMap.houselat, title: bdMap.bdtitle, content: bdMap.bdcontent });
        /*************绑定事件******************/
        bdMap.bdmap.addEventListener("dblclick", function () { getcenteris = true; });
        //bdmap.addEventListener("zoomend", function () { if (getcenteris) { bdmap.setCenter(bdpoit); getcenteris = false; } });
        bdMap.bdmap.addEventListener("rightclick", bdMap.showInfo);
        bdMap.bdmap.addEventListener("mousemove",
                                 function set(e) {
                                     $("#lng").val(e.point.lng);
                                     $("#lat").val(e.point.lat);
                                 }
                             );
    },
    addnewmarker: function (data) {
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            //div.attr("class", "bubble-2 bubble");
            div.style.position = "absolute";
            div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
            div.style.backgroundColor = "#EE5D5B";
            div.style.border = "1px solid #BC3B3A";
            div.style.color = "white";
            div.style.height = "18px";
            div.style.padding = "2px";
            div.style.lineHeight = "18px";
            div.style.whiteSpace = "nowrap";
            div.style.MozUserSelect = "none";
            div.style.fontSize = "12px";
            var span = this._span = document.createElement("span");
            div.appendChild(span);
            span.appendChild(document.createTextNode(this._text));
            var that = this;

            var arrow = this._arrow = document.createElement("div");
            arrow.style.background = "url(https://map.baidu.com/fwmap/upload/r/map/fwmap/static/house/images/label.png) no-repeat";
            arrow.style.position = "absolute";
            arrow.style.width = "11px";
            arrow.style.height = "10px";
            arrow.style.top = "22px";
            arrow.style.left = "10px";
            arrow.style.overflow = "hidden";
            div.appendChild(arrow);
            bdMap.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(this._point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(data.lng, data.lat), data.title, data.content);
        bdMap.bdmap.addOverlay(myCompOverlay);
        if (bdMap.id && bdMap.id > 0) {
            myCompOverlay.addEventListener("click", function () {
                if (bdmap.id == 1) {
                    return;
                }
                window.open("https://sy.fangxiaoer.com/UHouse/Subdistrict/view/" + bdMap.id);
            });
        }
       
    },
    
    //添加标注
    addmarker: function (poit, title, content) {
        var marker = new BMap.Marker(poit);
        bdMap.showFrom(marker, title, bdMap.bdmap, content);
        bdMap.bdmap.addOverlay(marker);//添加标注
    },
    //标注弹出面板
    showFrom: function (marker, title, map, content) {
        var searchInfoWindow = new BMapLib.SearchInfoWindow(map, "<p style='line-height:16px'>" + content + "</p>", {
            title: "<p style='width:210px;font:bold 14px/16px arial,sans-serif;margin:0;color:#cc5522;white-space:nowrap;overflow:hidden' title='" + title + "'>" + title + "</p>", //标题
                    width: 290, //宽度
                    panel: "panel", //检索结果面板
                    enableAutoPan: true, //自动平移
                    searchTypes: [
                    ]
                });
        marker.addEventListener("click", function(e) {
            searchInfoWindow.open(marker);
        });
    },

        //查询方法
    searechMap: function (type) {
        local = new BMap.LocalSearch(bdMap.bdmap, { renderOptions: { map: bdMap.bdmap, autoViewport: false }, onSearchComplete: bdMap.select });
        local.searchNearby(type, bdMap.bdpoit, bdMap.radius);
        $(".searechMapTitle").text(type);
        $(".map_dl").html("");
        local.setMarkersSetCallback(function (markers) {
            for (var i = 0; i < markers.length; i++) {
                bdMap.showFrom(markers[i].marker, markers[i].title, bdMap.bdmap, markers[i].address);
                bdMap.bdmap.addOverlay(markers[i].marker); //添加标注
            }
        });

    },
    //配套查询方法
    // searechMapList: function (type,ind) {
    //     var text ="";
    //     local = new BMap.LocalSearch(bdMap.bdmap, { renderOptions: { map: bdMap.bdmap, autoViewport: false }, onSearchComplete: bdMap.select });
    //     local.searchNearby(type, bdMap.bdpoit, 500);
    //     local.setMarkersSetCallback(function (markers) {
    //         for (var i = 0; i < markers.length; i++) {
    //             if(i==markers.length-1){
    //                 if(type=="公交"){
    //                     text+= markers[i].title+"站。"
    //                 }else if(type=="地铁"){
    //                     text+= markers[i].title+"地铁站。"
    //                 }else{
    //                     text+= markers[i].title+"。"
    //                 }
    //             }else{
    //                 if(type=="公交"){
    //                     text+= markers[i].title+"站、"
    //                 }else if(type=="地铁"){
    //                     text+= markers[i].title+"地铁站、"
    //                 }else{
    //                     text+= markers[i].title+"、"
    //                 }
    //             }
    //         }
    //         if(markers.length!=0){
    //             $(".canteen div").eq(ind).find("p").text($(".canteen div").eq(ind).find("p").text()+text)
    //             $(".canteenTitle").show()
    //         }else{
    //             $(".canteen div").eq(ind).hide()
    //         }
    //     });
    //
    // },
    //添加显示面板
    openInfoWindow: function (searchInfo) {
        var searchInfoWindow = new BMapLib.SearchInfoWindow(bdMap.bdmap, "<p style='line-height:16px'>"+searchInfo.address+"</p>", {
            title: "<p style='width:210px;font:bold 14px/16px arial,sans-serif;margin:0;color:#cc5522;white-space:nowrap;overflow:hidden' title='" + searchInfo.title + "'>" + searchInfo.title + "</p>", //标题
            width: 290, //宽度
            panel: "panel", //检索结果面板
            enableAutoPan: true, //自动平移
            searchTypes: [
            ]
        });
        searchInfoWindow.open(new BMap.Point(searchInfo.lng, searchInfo.lat));
    },

    //查询结果绑定
    select: function (results) {
        bdMap.openInfoList = [];
        bdMap.bdmap.clearOverlays();
        bdMap.bdmap.centerAndZoom(bdMap.bdpoit, bdMap.suofa);
        bdMap.addnewmarker({ lng: bdMap.houselng, lat: bdMap.houselat, title: bdMap.bdtitle, content: bdMap.bdcontent });
        local.disableFirstResultSelection();
        // 判断状态是否正确
        if (local.getStatus() == BMAP_STATUS_SUCCESS) {
            var s = [];
            for (var i = 0; i < results.getCurrentNumPois() ; i++) {
            
                var entity = {};
                entity.title = results.getPoi(i).title;
                entity.address = results.getPoi(i).address;
                entity.phoneNumber = results.getPoi(i).phoneNumber;
                entity.point = results.getPoi(i).point;
                entity.detailUrl = results.getPoi(i).detailUrl;
                entity.url = results.getPoi(i).url;
                var pointA = new BMap.Point(bdMap.houselng, bdMap.houselat);  // 创建点坐标A
                var pointB = new BMap.Point(results.getPoi(i).point.lng, results.getPoi(i).point.lat);  // 创建点坐标B
                entity.distance = (bdMap.bdmap.getDistance(pointA, pointB)).toFixed(2);  //获取两点距离,保留小数点后两位
                bdMap.openInfoList.push(entity);
     
            }
            //排序对象集合
            var list = bdMap.openInfoList.sort(function (a, b) {
                return a.distance - b.distance;
            });
            var html = "";
            for (var i = 0; i < list.length; i++) {
               
                html += "<dd onclick='bdMap.openInfoWindow({title:\"" + list[i].title + "\",address:\"" + list[i].address + "\",lng:" + list[i].point.lng + ",lat:" + list[i].point.lat + "})'><a title='" + list[i].title + "'><span>" + list[i].distance + "米</span>" + list[i].title + "</a></dd>";
            }
            $(".map_dl").html(html);
        }
    }

};

function ComplexCustomOverlay(point, text, mouseoverText) {
    this._point = point;
    this._text = text;
    this._overText = mouseoverText;
}

