/**
 * Created by Administrator on 2018/2/28.
 */
$(function () {
    $.ajax({
        type: "post",
        url: "/getContrastHouse",
        async: false,
        success: function (list) {
            if (list.length != 0) {
                if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                    $(".suspensionIcon .n9 .comparison").show();
                }
            }
            $(".comparison ul li").remove();
            for(i=0;i<list.length;i++){
                var data='<li><div class="comparisonOpen hover" onclick="chooseContrastHouse('+list[i].houseId+')" id="open'+list[i].houseId+'"></div><div class="comparisonSubName" id="'+list[i].houseId+'">'+list[i].houseName+'</div><div class="comparisonClose hover" onclick="deleteContrastHouse('+list[i].houseId+')"><input type="hidden" value='+list[i].houseId+'></div></li>';
                $(".comparison ul").append(data);
                $("#house"+list[i].houseId+" .contrast").hide()
                $("#house"+list[i].houseId+" .contrastNo").show()
            }
        }
    });
    //取消已加入楼盘
    $(".contrastNo").click(function(){
        var houseId = $(this).parent().find(".cHouseId").val();
        deleteContrastHouse(houseId)
        // alert("该楼盘已加入对比列表");
    });
    //加入楼盘对比列表
    $(".contrast").click(function(){
        if($(".comparisonOpen.hover").length >=4){
            alert("最多只能对比四个楼盘");
            return;
        }
        $(this).css('display','none');
        $(this).parent().find(".contrastNo").css('display','block');
        var houseId = $(this).parent().find(".cHouseId").val();
        var houseName = $(this).parent().find(".cHouseName").val();
        var houseType = $(this).parent().find(".cHouseType").val();
        $.ajax({
            type: "post",
            data: { houseId : houseId,houseName:houseName, houseType : houseType},
            url: "/addContrastHouse",
            //dataType: 'json',
            async: false,
            success: function (list) {
                if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                    $(".suspensionIcon .n9 .comparison").show();
                }
                $(".comparison ul li").remove();
                for(i=0;i<list.length;i++){
                    var data='<li><div class="comparisonOpen hover "  onclick="chooseContrastHouse('+list[i].houseId+')" id="open'+list[i].houseId+'"></div><div class="comparisonSubName" id="'+list[i].houseId+'">'+list[i].houseName+'</div><div class="comparisonClose hover" onclick="deleteContrastHouse('+list[i].houseId+')"><input type="hidden" value='+list[i].houseId+'></div></li>';
                    $(".comparison ul").append(data);
                }
            },
        });
    });

    //全部清空
    $(".comparisonClear").click(function(){
        var houseSize = $(".comparison dl dd ul li").length;
        if (houseSize > 0) {
            $.ajax({
                type: "post",
                url: "/deleteAllContrastHouse",
                async: false,
                success: function (list) {
                    if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                        $(".suspensionIcon .n9 .comparison").show();
                    }
                    var houseId = $(".comparison").find("ul").find("li").find(".comparisonSubName");
                    for (var i = 0 ; i < houseId.length ; i++ ) {

                        $("#house"+houseId[i].getAttribute("id")+" .contrastNo").hide()
                        $("#house"+houseId[i].getAttribute("id")+" .contrast").show()
                    }
                    $(".comparison ul li").remove();
                    for(i=0;i<list.length;i++){
                        var data='<li><div class="comparisonOpen hover"   onclick="chooseContrastHouse('+list[i].houseId+')" id="open'+list[i].houseId+'"></div><div class="comparisonSubName" id="'+list[i].houseId+'">'+list[i].houseName+'</div><div class="comparisonClose hover" onclick="deleteContrastHouse('+list[i].houseId+')"><input type="hidden" value='+list[i].houseId+'></div></li>';
                        $(".comparison ul").append(data);
                        $("#house"+list[i].houseId+" .contrastNo").hide()
                        $("#house"+list[i].houseId+" .contrast").show()
                    }
                },
            });
        }else {
            alert("您没有需要清除的房源。")
        }

    })

    //开始对比
    $(".comparisonBegin").click(function(){
        if($(".comparisonOpen.hover").length == 0) {
            alert("请选择需要对比的楼盘");
        }
        if($(".comparisonOpen.hover").length > 0 && $(".comparisonOpen.hover").length <= 4) {
            var txt=""
            $(".comparisonOpen.hover").each(function(){
                txt+=$(this).parent().find(".comparisonSubName").attr("id")+','
            })
            txt = txt.substring(0,txt.length-1)
            window.open("https://sy.fangxiaoer.com/gotoContrastHouse")
            // window.open("http://192.168.6.36:8888/gotoContrastHouse")
        }
        if($(".comparisonOpen.hover").length>4) {
            alert("最多勾选4项");
        }
    })

    //点击出发搜索事件
    $(".serachHouseInfo").click(function () {
        setTimeout(function() {serachHouseInfo(1);},1000);
    });
    function serachHouseInfo(method) {
        $(".serachHouseInfo").autocomplete({
            source: function( request, response ) {
                var searchInfo = request.term;
                searchInfo = encodeURI(searchInfo);
                $.ajax({
                    url: "/searchNewHouse",
                    dataType: "json",
                    data: {key: 1,serachInfo: searchInfo},
                    success: function (data) {
                        if (data.length != 0) {
                            response($.map(data, function (item) {
                                var highLightTitle = item.title;
                                highLightTitle = highLightTitle.replace(
                                    new RegExp(
                                        "(?![^&;]+;)(?!<[^<>]*)(" +
                                        $.ui.autocomplete.escapeRegex(request.term) +
                                        ")(?![^<>]*>)(?![^&;]+;)", "gi"
                                    ), "<strong>$1</strong>");
                                return {
                                    label: "<i style='float:right'></i>" + highLightTitle,
                                    tableId: item.tableId,
                                    projectType: item.projectType,
                                    projectName: item.title,
                                    value: item.title,
                                };
                            }));
                        }
                    }
                })
            },
            select: function( event, ui ) {
                var houseId = ui.item.tableId;
                var houseName = ui.item.projectName;
                var houseType = 1;
                $.ajax({
                    type: "post",
                    data: { houseId : houseId,houseName:houseName, houseType : houseType},
                    url: "/addContrastHouse",
                    async: false,
                    success: function (list) {
                        if($(".suspensionIcon .n9 .comparison").css("display")=='none'){
                            $(".suspensionIcon .n9 .comparison").show();
                        }
                        $(".comparison ul li").remove();
                        for(i=0;i<list.length;i++){
                            var data='<li><div class="comparisonOpen hover"  onclick="chooseContrastHouse('+list[i].houseId+')" id="open'+list[i].houseId+'"></div><div class="comparisonSubName" id="'+list[i].houseId+'">'+list[i].houseName+'</div><div class="comparisonClose hover" onclick="deleteContrastHouse('+list[i].houseId+')"><input type="hidden" value='+list[i].houseId+'></div></li>';
                            $(".comparison ul").append(data);
                        }
                    },
                });
            }
        });
    }
});

//添加需要对比的房源
function chooseContrastHouse(houseId) {
    if($("#open"+houseId).hasClass("hover")){
        $("#open"+houseId).removeClass("hover");
    }else if($(".comparisonOpen.hover").length>=4){
        alert("最多勾选4项")
    }else{
        $("#open"+houseId).addClass("hover");
    }
}

//删除添加的对比房源
function deleteContrastHouse(houseId) {
    $.ajax({
        type:"post",
        url:"/deleteContrastHouse",
        data: { houseId : houseId},
        async: false,
        success: function (list) {
            $(".comparison ul li").remove();
            for(i=0;i<list.length;i++){
                var data='<li><div class="comparisonOpen hover"   onclick="chooseContrastHouse('+list[i].houseId+')" id="open'+list[i].houseId+'"></div><div class="comparisonSubName" id="'+list[i].houseId+'">'+list[i].houseName+'</div><div class="comparisonClose hover" onclick="deleteContrastHouse('+list[i].houseId+')"><input type="hidden" value='+list[i].houseId+'></div></li>';
                $(".comparison ul").append(data);
            }
            $("#house"+houseId+" .contrastNo").hide()
            $("#house"+houseId+" .contrast").show()
        }
    })
}


