/**
 * Created by Administrator on 2017/5/20.
 */


$(function () {
    $(".right_ico .n7").click(function(){
        var sc=$(window).scrollTop();
        $('body,html').animate({scrollTop:0},500);
    })
    $(".right_ico a").mouseover(function(){
        $(this).stop()
        $(this).animate({"width":$(this).attr("rel")},100)
    })

    $(".right_ico a").mouseout(function(){
        $(this).stop()
        $(this).animate({"width":"31px"},100)
    });

    $(window).scroll(function() {
        if($(document).scrollTop()>300){
            $(".n7").fadeIn()
        }else{
            event.stopPropagation();
            $(".n7").fadeOut()
        }
    });



    //返回
    $(".bnzf h1").click(function () {
        $(".n9").hide();
        $(".right_ico a").css("background", "");
    })
    //$(".n9").mouseout(function () {
    //    $(".fxe_bnzf_xl").hide();
    //})
    function xuqiudingzhi() {
        $(".n9").hide();
    }
    //发送验证码
    $(".fxe_ReSendValidateCoad").click(function () {
        if ($("#fxe_bnzf_phone_fb").val() == "") {
            alert("请填写电话");
        } else if (!confirm.mobile()) {
            alert(hint[5]);
        } else if (SendFunc.ajaxSendCode($("#fxe_bnzf_phone_fb").val()) == 1) {
            fxeTime.timeWait();
            $(".fxe_validateCode").css("display", "inline-block");
        }
    })
    //发送验证码
    var SendFunc = {
        ajaxSendCode: function (mobile) {
            try {
                var ryzm = 0;
                $.ajax({
                    type: "POST",
                    data: { action: "Send", mobile: mobile },
                    url: "/Action/ActivityHelp.ashx",
                    async: false,
                    success: function (data) {
                        ryzm = data;
                    }
                });
            } catch (e) {
                console.log(e.message);
            }
            return ryzm;
        }
    };
    //核对验证码
    function confirmYzm() {
        var phone = $("#fxe_bnzf_phone_fb").val();
        var code = $("#fxe_bnzf_yzm").val();
        var t = false;
        $.ajax({
            type: "POST",
            async: false,
            data: { action: "CheckSend", mobile: phone, code: code },
            url: "/Action/SendsmsHelp.ashx",
            success: function (data) {
                if (data == "1") {
                    t = true;

                }
                else {
                    t = false;
                }
            },
            error: function (error) {
                console.log(error);
                t = false;
            }
        });
        return t;
    }
    //帮你找房提交
    $("#fxe_bnzf_submit").click(function () {
        if ($("#fxe_bnzf_qy").next().html() == "请选择") {
            alert("请选择区域");
        } else if ($("#fxe_bnzf_ys").next().html() == "请选择") {
            alert("请选择预算");
        } else if ($("#fxe_bnzf_hx").next().html() == "请选择") {
            alert("请选择户型");
        } else if ($("#fxe_bnzf_phone_fb").val() == "") {
            alert("请填写电话");
        } else if (!confirm.mobile()) {
            alert(hint[5]);
        } else if (!confirmYzm()) {
            alert(hint[7]);
        } else {
            var tphone = $.trim($("#fxe_bnzf_phone_fb").val());//电话
            var yusuang = $("#fxe_bnzf_ys").next().html();//预算
            var quyu = $("#fxe_bnzf_qy").next().html();//区域
            var jusi = $("#fxe_bnzf_hx").next().html();//户型
            var miaos = $.trim($("#fxe_ms").val());//描述
            fangxiaoer.ajax("upguide", "{tel:'" + tphone + "',Budget:'" + yusuang + "',region:'" + quyu + "',area:'" + jusi + "',italy:'" + miaos + "',stype:'" + 1 + "',type:'one'}", function (data) {
                if (data == "1") {
                    //重置
                    $("#fxe_bnzf_phone_fb").val("");
                    $("#fxe_bnzf_ys").next().html("请选择");
                    $("#fxe_bnzf_qy").next().html("请选择");
                    $("#fxe_bnzf_hx").next().html("请选择");
                    $("#fxe_ms").val("");
                    $("#fxe_bnzf_yzm").val("");
                    $(".fxe_validateCode").hide();
                    $(".fxe_ReSendValidateCoad").show().html("重新获取");
                    alert("亲，您的需求已提交成功，工作人员会及时跟您联系，请耐心等待！");
                }
            });
        }
        return false;
    })
    //点击选择
    $(".fxe_bnzf_xl li").click(function () {
        $(".fxe_bnzf_xl").hide();
        $(this).parent().prev().text($(this).text()).css("color", "#000");
        $(this).parent().parent().find("input").val($(this).text());
    })
    //点击显示下拉列表
    $(".fxe_bnzf_xs").click(function () {
        $(".fxe_bnzf_xl").hide();
        $(this).next().show();
    })
    //滑动消失下拉列表
    $(".fxe_bnzf_xl").mouseover(function () {
        $(".fxe_bnzf_xl").hover(function () {
            $(this).show()
        }, function () {
            $(this).hide()
        })
    })
    $(".right_ico .n8").click(function () {
        if ($(".right_ico .n9").css("display") != "none") {
            $(".right_ico .n9").hide();
            $(this).css("background", "");
        } else {
            $(".right_ico .n9").show();
            $(this).css("background", "#ff5200");
        }
    })
})