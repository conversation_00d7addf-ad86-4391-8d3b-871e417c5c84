$(function () {
   // $("#currentBuildingPirce").val($("#htype-dropdown li").eq(0).attr("data-glzs"))
   if($("#htype-dropdown li").length!=0){
	    $("#htype-dropdown li").eq(0).each(function () {
	        var flzs = $(this).attr("data-glzs");
	        var thisLi = $(this)
	        $(".price li").eq(0).each(function () {
	            //动态获取正则表达式
	            var reg1 = thisLi.attr("data-glzs");
	            var reg = $(this).html().split("<i>")[0] == "底商" ? "商铺" : $(this).html().split("<i>")[0];//存在 给出'底商'价格房源类型却是'商铺' 的问题   手动改'底商'为'商铺'
	            if (reg1 == reg) {
	                var price = $(this).find("i").text();
	                $("#currentBuildingPirce").val(price)//修改为对应的价格
	            }
	        })
	    })
   }else{
   	$("#currentBuildingPirce").val("0")
   }

    /* 饼状图表 */
    $("#htype-dropdown li").click(function () {
        var currentChooseArea = $(this).attr('data-code');    // 当前所选户型面积
        $("#currentChooseArea").val(currentChooseArea);
        $("#currentBuildingPirce").val($(this).attr("data-glzs"))
        var currentBuildingPirce = $("#currentBuildingPirce").val();    // 当前楼盘均价
        var totalHousePrice = Math.round(currentBuildingPirce * currentChooseArea / 10000);    // 房屋总金额
        $("#totalHousePrice").val(totalHousePrice);
        // $("#totalPrice").html(totalHousePrice);
        $('#ht-total-price').html(totalHousePrice);

        var anjie = $('#anjie').val();   // 按揭成数
        var daikuan_total_price = Math.round(currentBuildingPirce * currentChooseArea / 10000 * anjie);    // 贷款总额
        $("#daikuan_total_price").val(daikuan_total_price);
        $('#group-dk-total').text($("#daikuan_total_price").val());
    })

    /* 输入总价 */
    $('#totalHousePrice').keyup(function () {
        var totalPrice = $.trim($(this).val());

        if (/^[0-9]+(.[0-9]{1,2})?$/.test(totalPrice)) {
            $("#daikuan_total_price").val(Math.round(totalPrice * $('#anjie').val()));
            $('#group-dk-total').text($("#daikuan_total_price").val());
            $('#ht-total-price').text(totalPrice);
        }
    });

    /* 贷款类型 */
    $("#daikuan_type_list li").click(function () {
        $("#daikuan_type").val($(this).attr('data-code'));
        var years = $("#years").val();
        if ($(this).attr('data-code') == 3) {
            $('#group-dk-total').text(Math.round($('#daikuan_total_price').val()));
            $('#group-dk-area').css('display', 'block');
            /*if (years <= 1 ){
                $('#whichrate').text("(利率 公积金:2.75% 商贷:4.35%)");
            }else if (years > 1 && years <= 5){
                $('#whichrate').text("(利率 公积金:2.75% 商贷:4.75%)");
            }else{
                $('#whichrate').text("(利率 公积金:3.25% 商贷:4.9%)");
            }*/
        }
        else {
            /*if($(this).attr('data-code') == 2){
                if (years <= 5){
                    $('#whichrate').text("(利率 公积金:2.75%)");
                }else{
                    $('#whichrate').text("(利率 公积金:3.25%)");
                }
            }else{
                if (years <= 1){
                    $('#whichrate').text("(利率 商贷:4.35%)");
                }else if (years > 1 && years <= 5 ){
                    $('#whichrate').text("(利率 商贷:4.75%)");
                }else {
                    $('#whichrate').text("(利率 商贷:4.9%)");
                }

            }*/
            $('#group-dk-area').css('display', 'none');
        }

        if ($(this).parents('.hj-col-md-9:first').length > 0) {
            $('.referesult').css('height', $(this).parents('.hj-col-md-9:first').height());
        }
        else {
            $('.referesult').css('height', $(this).parents('.month-pay:first').height() + 40);
        }
    })

    /* 贷款年限 */
    $("#time-dropdown li").click(function () {
        $("#years").val($(this).attr('data-code'));
        var years = $("#years").val();
        var daikuan_type = $("#daikuan_type").val();
        /*if (daikuan_type == 1){
            if (years <= 1 ){
                $('#whichrate').text("(利率 商贷:4.35%)");
            }else if (years > 1 && years <= 5){
                $('#whichrate').text("(利率 商贷:4.75%)");
            }else{
                $('#whichrate').text("(利率 商贷:4.9%)");
            }
        }else if (daikuan_type == 2){
            if (years <= 5){
                $('#whichrate').text("(利率 公积金:2.75%)");
            }else{
                $('#whichrate').text("(利率 公积金:3.25%)");
            }
        }else {
            if (years <= 1 ){
                $('#whichrate').text("(利率 公积金:2.75% 商贷:4.35%)");
            }else if (years > 1 && years <= 5){
                $('#whichrate').text("(利率 公积金:2.75% 商贷:4.75%)");
            }else{
                $('#whichrate').text("(利率 公积金:3.25% 商贷:4.9%)");
            }
        }*/
    })

    /* 按揭比例 */
    $("#percent-dropdown li").click(function () {
        var anjie = $(this).attr('data-code');
        var totalPrice = Math.round($('#totalHousePrice').val());
        $("#anjie").val(anjie);

        var daikuan_total_price = Math.round(totalPrice * anjie);    // 贷款总额
        $("#daikuan_total_price").val(daikuan_total_price);
        $('#group-dk-total').text($("#daikuan_total_price").val());
    })

    /* 点击计算按钮计算房贷 */
    $('#mortgageCalculation').unbind("click").click(function () {
        /* 饼状图表 */
        showResult($("#totalHousePrice").val());
    });

    /* 初始化总价 */


 	var HousePrice=parseFloat($(".main .mainContent .price b").text())
     console.log(HousePrice)
	 if(HousePrice!=0){
		 $('#totalHousePrice').val(HousePrice);
		 }else{
         $('#totalHousePrice').val(0);
     }



    $('#anjie').val('0.7');
    $("#daikuan_total_price").val( $('#totalHousePrice').val()* $('#anjie').val());

    $('#daikuan_type').val('1');
    $('#years').val('30');

    showResult($("#totalHousePrice").val());
})

lilv_array = new Array;
//12年6月8日基准利率
lilv_array[1] = new Array;
lilv_array[1][1] = new Array;
lilv_array[1][2] = new Array;
lilv_array[1][1][1] = 0.0435;//商贷1年 6.31%
lilv_array[1][1][3] = 0.0475;//商贷1～3年 6.4%
lilv_array[1][1][5] = 0.0475;//商贷 3～5年 6.65%
lilv_array[1][1][10] = 0.049;//商贷 5-30年 6.8%
lilv_array[1][2][5] = 0.0275;//公积金 1～5年 4.2%
lilv_array[1][2][10] = 0.0325;//公积金 5-30年 4.7%
//12年6月8日利率下限（7折）
lilv_array[2] = new Array;
lilv_array[2][1] = new Array;
lilv_array[2][2] = new Array;
lilv_array[2][1][1] = 0.0357;//商贷1年 4.417%
lilv_array[2][1][3] = 0.0385;//商贷1～3年 4.48%
lilv_array[2][1][5] = 0.0385; //商贷 3～5年 4.655%
lilv_array[2][1][10] = 0.0396;//商贷 5-30年 4.76%
lilv_array[2][2][5] = 0.0325; //公积金 1～5年 4.2%
lilv_array[2][2][10] = 0.0375; //公积金 5-30年 4.7%
//12年6月8日利率下限（85折）
lilv_array[3] = new Array;
lilv_array[3][1] = new Array;
lilv_array[3][2] = new Array;
lilv_array[3][1][1] = 0.0433;//商贷1年 5.3635%
lilv_array[3][1][3] = 0.0467;//商贷1～3年 5.44%
lilv_array[3][1][5] = 0.0467;//商贷 3～5年 5.6525%
lilv_array[3][1][10] = 0.048;//商贷 5-30年 5.78%
lilv_array[3][2][5] = 0.0325; //公积金 1～5年 4.2%
lilv_array[3][2][10] = 0.0375; //公积金 5-30年 4.7%
//12年6月8日利率上限（1.1倍）
lilv_array[4] = new Array;
lilv_array[4][1] = new Array;
lilv_array[4][2] = new Array;
lilv_array[4][1][1] = 0.0561;//商贷1年 6.941%
lilv_array[4][1][3] = 0.0605;//商贷1～3年 7.04%
lilv_array[4][1][5] = 0.0605;//商贷 3～5年 7.315%
lilv_array[4][1][10] = 0.0622;//商贷 5-30年 7.48%
lilv_array[4][2][5] = 0.0325; //公积金 1～5年 4.2%
lilv_array[4][2][10] = 0.0375; //公积金 5-30年 4.7%



var _$ = function (id) {
    return document.getElementById(id);
}

function myround(v, e) {
    var t = 1;
    e = Math.round(e);
    for (; e > 0; t *= 10, e--);
    for (; e < 0; t /= 10, e++);
    return Math.round(v * t) / t;
}

/* 选择的贷款类型 */
function exc_zuhe(fmobj, v) {
    if (v == 3) {
        document.all.calc1_zuhe.style.display = 'block';
    } else {
        document.all.calc1_zuhe.style.display = 'none';
    }
}

//验证是否为数字
function reg_Num(str) {
    if (str.length == 0) { return false; }
    var Letters = "1234567890.";

    for (i = 0; i < str.length; i++) {
        var CheckChar = str.charAt(i);
        if (Letters.indexOf(CheckChar) == -1) { return false; }
    }

    return true;
}

//得到利率
function getlilv(lilv_class, type, years) {
    var lilv_class = parseInt(lilv_class);
    if (type == 1){
        if (years <= 1) {
            return lilv_array[lilv_class][type][1];
        }else if (years >1 && years <= 5){
            return lilv_array[lilv_class][type][5];
        } else {
            return lilv_array[lilv_class][type][10];
        }
    }else{
        if (years <= 5){
            return lilv_array[lilv_class][type][5];
        }else{
            return lilv_array[lilv_class][type][10];
        }
    }

}
//本金还款的月还款额(参数: 年利率 / 贷款总额 / 贷款总月份 / 贷款当前月0～length-1)
function getMonthMoney2(lilv, total, month, cur_month) {
    var lilv_month = lilv / 12;//月利率
    //return total * lilv_month * Math.pow(1 + lilv_month, month) / ( Math.pow(1 + lilv_month, month) -1 );
    var benjin_money = total / month;
    return (total - benjin_money * cur_month) * lilv_month + benjin_money;
}

//本息还款的月还款额(参数: 年利率/贷款总额/贷款总月份)
function getMonthMoney1(lilv, total, month) {
    var lilv_month = lilv / 12;//月利率
    return total * lilv_month * Math.pow(1 + lilv_month, month) / (Math.pow(1 + lilv_month, month) - 1);
}

/* 显示结果 */
function showResult(totalPrice) {
    if (totalPrice > 0 && /^[0-9]+(.[0-9]{1,2})?$/.test($.trim(totalPrice))) {
        chartPie(ext_total());
    }
    else {
    	 $("#average_month_pay").text("0元")
 		$("#first_pay").text("0万元")
 		$("#daikuan_total_price2").text("0万元")
 		$("#pay_lixi").text("0万元")
 		$("#totalHousePrice").val("");
    	//   alert('估算总价填写错误');
    }
}

/* 统计计算 */
function ext_total() {

    var years = $('#years').val();
    var month = years * 12;
    var totalHousePrice = $('#totalHousePrice').val();    // 总房款

    /* 组合型贷款(组合型贷款的计算，只和商业贷款额、和公积金贷款额有关，和按贷款总额计算无关) */
    if ($("#daikuan_type").val() == 3) {
        var total_price_sy = Math.round($('#total-price-sy').val());
        var total_price_gjj = Math.round($('#total-price-gjj').val());
        var lilv_sy = getlilv($("#lilv").val(), 1, $("#years").val());
        var lilv_gjj = getlilv($("#lilv").val(), 2, $("#years").val());
        var daikuan_total_price = Math.round($('  #daikuan_total_price').val());    // 贷款总额
        console.log("商业贷款:"+lilv_sy)
        console.log("公积金:"+lilv_gjj)
        /* 判断总金额是否正确 */
        if ((total_price_gjj + total_price_sy) != daikuan_total_price) {
            if ($('#total-price-gjj').val() == "") {
                alert("请填写公积金货款金额");
            } else if ($('#total-price-sy').val() == "") {
                alert("请填写商业货款金额");
            } else {
                alert('公积金和商业贷款总和不等于贷款总额');
            }
            return false;
        }

        // 首期付款
        var first_pay = totalHousePrice - daikuan_total_price;
        first_pay = Math.round(first_pay * 100) / 100
        $("#first_pay").html(first_pay + '万元');
        $("#first_pay").attr('c',first_pay);



        $("#daikuan_total_price2").html((daikuan_total_price).toFixed(2) + '万元');
        $("#daikuan_total_price2").attr('b',(daikuan_total_price).toFixed(2));


        // 贷款总月数
        $("#daikuan_total_month").val(month);

        /* 本息还款 */
        //商贷月均还款
        var month_money_sy = getMonthMoney1(lilv_sy, total_price_sy * 10000, month);    // 调用函数计算
        var average_month_pay_sy = Math.round(month_money_sy * 100);

        //商贷还款总额
        var all_total_sy = Math.round(month_money_sy * month * 100) / 100;
        //商贷支付利息款
        var pay_lixi_sy = (Math.round((all_total_sy - total_price_sy * 10000) * 100) / 100) / 10000;

        //公积金月均还款
        var month_money_gjj = getMonthMoney1(lilv_gjj, total_price_gjj * 10000, month);    // 调用函数计算
        var average_month_pay_gjj = Math.round(month_money_gjj * 100);

        //公积金还款总额
        var all_total_gjj = Math.round(month_money_gjj * month * 100) / 100;
        //公积金支付利息款
        var pay_lixi_gjj = (Math.round((all_total_gjj - total_price_gjj * 10000) * 100) / 100) / 10000;

        var month_money = (average_month_pay_sy + average_month_pay_gjj) / 100;
        var pay_lixi = parseFloat(pay_lixi_sy.toFixed(1)) + parseFloat(pay_lixi_gjj.toFixed(1));

        $("#pay_total_price").val(all_total_sy + all_total_gjj);
        $("#pay_lixi").html((pay_lixi).toFixed(1) + '万元');
        $("#average_month_pay").html(month_money + '元');
    }
        /* 公积金贷款或者商贷 */
    else {
        /* 商业贷款、公积金贷款 */
        var lilv = getlilv($("#lilv").val(), $("#daikuan_type").val(), $("#years").val());    // 得到利率

        /* 根据贷款总额计算 */
        if ($("#daikuan_total_price").val() == '') {
            return;
        }

        // 首期付款
        var first_pay = totalHousePrice - $('#daikuan_total_price').val();
        first_pay = Math.round(first_pay * 100) / 100
        $("#first_pay").html(first_pay + '万元');
        $("#first_pay").attr('c',first_pay)

        //贷款总额
        var daikuan_total_price = Math.round($('#daikuan_total_price').val());
        $("#daikuan_total_price2").html(daikuan_total_price.toFixed(2) + '万元');
        $("#daikuan_total_price2").attr('b',(daikuan_total_price).toFixed(2));

        // 贷款总月数
        $("#daikuan_total_month").val(month);

        /* 2.本息还款 */
        //月均还款
        var month_money1 = getMonthMoney1(lilv, daikuan_total_price * 10000, month);    // 调用函数计算

        var average_month_pay = Math.round(month_money1 * 100) / 100;
        $("#average_month_pay").html(average_month_pay + '元');

        //还款总额
        var all_total1 = Math.round(month_money1 * month * 100) / 100;
        $("#pay_total_price").val(all_total1);

        //支付利息款
        var pay_lixi = (Math.round((all_total1 - daikuan_total_price * 10000) * 100) / 100) / 10000;
        $("#pay_lixi").html(pay_lixi.toFixed(1) + '万元');
    }

    $('.toolsform-defal').hide();
    $('.toolsform-res').stop().fadeIn(350);


    var dtype=$("#daikuan_type").val()//贷款类型
    var shouf=parseInt($("#swf").text())*10//首付比例
    var dktotal = Math.round($('#daikuan_total_price').val()) // 贷款总额
    var ratej=$(".lprm").attr('rate-gjj')//公积金利率 /3.25
    if(dtype==1){
        //商贷
        loanWithLPR(dktotal,'',month,'',1,'',1,75)//本息
    }else if(dtype==2){
        //公积金贷
        loanWithLPR('',dktotal,'',month,2,1,1,75)//本息
    }else if(dtype==3){
        //组合贷
        var totalGjj=$("#total-price-gjj").val()
        var totalSy=$("#total-price-sy").val()
        loanWithLPR(totalSy,totalGjj,month,month,3,1,1,75)//本息
    }


    return [first_pay, daikuan_total_price, pay_lixi.toFixed(1)];
}


//新版LPR贷款计算器
function loanWithLPR(a,b,c,d,e,f,g,h){
    $.ajax({
        type: "POST",
        url: "/calForLoan",
        data: {
            priceA:a,//商贷金额
            priceB:b,//公积金金额
            month1:c,//商贷月份
            month2:d,//公积金月份
            loanType:e,//贷款类型（1.商贷，2.公积金，3.组合贷）
            discount:f,//公积金倍率
            countMethod:g,//计算方式（1.等额本息，2.等额本金）
            basePoint:h,//基点
        },
        headers : {
            'Content-Type' : 'application/x-www-form-urlencoded'
        },
        success: function (data) {
            if(data.status==1){
                if(g==1){
                    //本息
                    var a=data.content.totalInterest/10000
                    a=Math.floor(a*100)/100
                    // a=a.toFixed(2)//四舍五入
                    $("#average_month_pay").text(data.content.everyMonth+'元')//月均还款
                    $("#pay_lixi").text(a+'万元')//totalInterest 利息总额
                    var b=$("#daikuan_total_price2").attr('b')
                    var c=$("#first_pay").attr('c')
                    c=Math.floor(c*100)/100
                    var thp=$("#totalHousePrice").val()

                    //修改饼状图
                    if (thp > 0 && /^[0-9]+(.[0-9]{1,2})?$/.test($.trim(thp))) {
                        chartPie([c,b,a])//['参考首付', '贷款金额', '支付利息']
                    }
                    else {
                        $("#average_month_pay").text("0元")
                        $("#first_pay").text("0万元")
                        $("#daikuan_total_price2").text("0万元")
                        $("#pay_lixi").text("0万元")
                        $("#totalHousePrice").val("");
                    }

                }else if(g==2){
                    //本金
                }
            }
        }
    })
}




function isLeapYear(year) {

    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
        return true;
    }

    return false;
}

function isEmpty(str) {
    if ((str == null) || (str.length == 0)) return true;
    else return (false);
}

function adv_format(value, num)   //四舍五入
{
    var a_str = formatnumber(value, num);

    var a_int = parseFloat(a_str);

    if (value.toString().length > a_str.length) {

        var b_str = value.toString().substring(a_str.length, a_str.length + 1)

        var b_int = parseFloat(b_str);

        if (b_int < 5) {

            return a_str

        }

        else {

            var bonus_str, bonus_int;

            if (num == 0) {

                bonus_int = 1;

            }

            else {

                bonus_str = "0."

                for (var i = 1; i < num; i++)

                    bonus_str += "0";

                bonus_str += "1";

                bonus_int = parseFloat(bonus_str);

            }

            a_str = formatnumber(a_int + bonus_int, num)

        }

    }

    return a_str
}

/* 房贷计算饼图 */
function chartPie(analyzeResult) {
    var pieChart = echarts.init(document.getElementById('pieChart'));
    if ($("#totalHousePrice")=="0"){
    $("#totalHousePrice").val("")
    }
    var name = ['参考首付', '贷款金额', '支付利息'];
    var colorArr = ['#2f69bf', '#a2bf2f', '#bf5a2f'];
    var data = [];

    for (var i = 0; i < analyzeResult.length; i++) {
        data.push({
            'value': analyzeResult[i],
            'name': name[i],
            'itemStyle':
            {
                normal: {
                    color: colorArr[i]
                }
            }
        });
    }

    /*pieChart.showLoading({
     text: '正在努力的读取数据中...'
     });*/

    var option = {
        tooltip: {
            trigger: 'item',
            formatter: "{a} <br/>{b} : {c} ({d}%)"
        },
        calculable: true,
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['40%', '60%'],
                itemStyle: {
                    normal: {
                        label: {
                            show: false
                        },
                        labelLine: {
                            show: false
                        }
                    },
                    emphasis: {
                        label: {
                            show: true,
                            position: 'center',
                            textStyle: {
                                fontSize: '25',
                                fontWeight: 'bold'
                            }
                        }
                    }
                },
                data: data
            }
        ]
    };

    pieChart.setOption(option);
}


