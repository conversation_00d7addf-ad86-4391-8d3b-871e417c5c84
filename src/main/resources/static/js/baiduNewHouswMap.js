/**
 * Created by Administrator on 2018/11/9.
 */
Vue.config.productionTip = false;
var baiduMap = new Vue({
	el: '#map',
	data: {
		houseType: type, //地图类型 1  新房            2  二手房            3 租房
		map: {},
		mapZoom: 12, //初始化级别
		maxRegion: 14, //地图区域和房源分界线  二手房租房 区域和板块的分界线   新接口大于等于14(传type=1展示项目,不传展示区域),小于14展示区域;
		maxPlat: 16, //地图板块和房源分界线
		gapZoom: -1, //与级别的差
		lngRight: "", //四个坐标点
		lngLeft: "",
		latTop: "",
		latBottom: "",
		isSchool: "", //是不是学区房
		isSubWay: "", //是不是地铁房
		isPayment: "", //是不是低首付
		isExistinfo: "", //是不是现房
		rentType: 1, //租赁类型
		scroll: true, //加载开关
		housingEstate: "", //小区id
		schoolHome: 0, //0为加载学校1为加载学校房源
		schoolProjectType:0,//学校类型 0为全部 1 普宅 2洋房 3商铺 4别墅
		page: 1, //列表页
		zoomType: 1, //地图显示级别  1区域  2 板块  3房源
		navIsActive: navType, //左侧菜单选中项
		windowWidth: window.innerWidth, //
		height: window.innerHeight - 50, // //50是主导航栏高度
		listwidth: 420, //左侧列表宽度
		showSelectList: -1, //筛选项当前显示的是哪一个  -1代表没有列表显示
		regionList: { //区域
			ActiveText: '区域',
			ActiveId: "",
			content: [{
				id: "",
				name: ''
			}]
		},
		typeList: { //新房类型 二手房户型
			ActiveText: '类型',
			ActiveId: "",
			content: [{
				id: "",
				name: ''
			}]
		},
		priceList: { //新房价格区间 二手房总价
			ActiveText: '价格',
			ActiveId: "",
			content: [{
				id: "",
				name: ''
			}]
		},
		schoolList: { //学校类型
			ActiveText: '类别',
			ActiveId: "",
			content: [{
				id: "",
				name: ''
			}]
		},
		subWayList: { //地铁线
			ActiveText: '地铁',
			ActiveId: "",
			content: [{
				id: "",
				name: ''
			}]
		},
		areaList: { //面积
			ActiveText: '面积',
			ActiveId: "",
			content: [{
				id: "",
				name: ''
			}]
		},
		selectSchool: '',//选中学校
		newHousList: [], //房源列表
		decidedType: "1", //对应接口中tab 其中不填或者填0则由接口提供返回zoomlevel，填其他则由客户端js处理；
		searchValue: '',
		projectId: "",
		searchZoomLevel: 18,
		sreachResultType: true,
		houseListheigth: window.innerHeight - 50,
		sreachRegionSwitch: false,
	},
	computed: {
		mapWidth: function() {
			var mapWidth;
			if(this.houseType == 2) {
				mapWidth = this.windowWidth - this.listwidth
			} else {
				mapWidth = this.windowWidth - 60 - this.listwidth
			}
			return mapWidth
		},
		recommentSwitch: function() {
			if(this.newHousList == '' || this.newHousList == undefined || this.newHousList.length == 0) {
				return "block";
			} else {
				return "none";
			}
		}
	},
	methods: {
		//初始化
		init: function() {
			//区分新房二手房租房的细微差别
			baiduMap.typeList.ActiveText = baiduMap.houseType == 1 ? "类型" : "户型"
			baiduMap.listwidth = baiduMap.houseType == 1 ? 420 : 530;
			baiduMap.map = new BMap.Map("baiduMap", {
				enableMapClick: false,
				minZoom: 10
			}); //创建地图并且禁用弹出信息框
			var point = new BMap.Point(123.439941, 41.798142); //中心点
			baiduMap.map.centerAndZoom(point, baiduMap.mapZoom); // 编写自定义函数，创建标注
			baiduMap.map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
			//初始化第一次获取地图数据
			var urltype=getUrlParam('type');//获取地址栏type参数
			if(baiduMap.navIsActive == 3 ||urltype==3) { //3为学区房
				baiduMap.SchoolHouseList();
			} else {
				baiduMap.showHouse();
			}

			baiduMap.map.addControl(new BMap.ScaleControl());
			//移动时重新获取数据
			// baiduMap.map.addEventListener("moveend", function() {
			//     if(!baiduMap.myManageType){
			//         return false;
			//     }
			//     alert('moveend');
			//     baiduMap.decidedType='1';
			//     baiduMap.page=1;
			//     baiduMap.housingEstate="";
			//     if(baiduMap.map.getZoom() >= baiduMap.maxRegion){
			//         baiduMap.regionList.ActiveId='';
			//         baiduMap.regionList.ActiveText="区域";
			//     }
			//     baiduMap.showHouse();
			//     baiduMap.showSelectList=-1;
			// });
			baiduMap.map.addEventListener("dragend", function() {
				// alert("dragend");
				baiduMap.page = 1;
				baiduMap.housingEstate = "";
				if(baiduMap.map.getZoom() >= baiduMap.maxRegion) {
					baiduMap.regionList.ActiveId = '';
					baiduMap.regionList.ActiveText = "区域";
					baiduMap.selectSchool='';
				}
				if(baiduMap.navIsActive == 3) { //3为学区房
				baiduMap.schoolHome=0;
					baiduMap.showSchool();

				} else {
					baiduMap.showHouse();
				}
				baiduMap.showSelectList = -1;
				baiduMap.sreachResultType = false;
				baiduMap.setHouseHigth();
			})
			//缩放
			baiduMap.map.addEventListener("zoomend", function() {
				baiduMap.decidedType = '1';
				if(baiduMap.map.getZoom() < baiduMap.maxRegion) {
					baiduMap.regionList.ActiveText = "区域";
					baiduMap.regionList.ActiveId = '';
					baiduMap.schoolHome = 0;
					baiduMap.selectSchool='';
				}
				
				baiduMap.housingEstate = "";
				baiduMap.page = 1;
				if(baiduMap.navIsActive == 3) { //3为学区房
					baiduMap.showSchool();

				} else {
					baiduMap.showHouse();
				}

				baiduMap.showSelectList = -1;
				baiduMap.sreachResultType = false;
				baiduMap.setHouseHigth();
			})

			if(baiduMap.houseType == 1) { //新房
				//初始化 下拉选择区域
				baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getRegionFilter", baiduMap.regionList)
				//初始化  下拉选择价格
				baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getNewPriceFilter", baiduMap.priceList)
				//初始化  下拉选择房源类型
				baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/projectTypeFilter", baiduMap.typeList)
				//初始化  下拉选择学区类型
				baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getSchoolTypeFilter", baiduMap.schoolList)
				//初始化  下拉选择地铁
				baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getSubWayFilter", baiduMap.subWayList)
			}

			//窗口大小变化重新计算高度
			window.onresize = function() {
				baiduMap.height = window.innerHeight - 50;
				baiduMap.setHouseHigth();
			}

			//滚动加载
			window.document.getElementsByClassName("HouseList")[0].addEventListener("scroll", winScroll);

			function winScroll() {
				var scrTop = window.document.getElementsByClassName("HouseList")[0].scrollTop;
				var windoweight;
				if($(".Choice").length > 0) {
					windoweight = (window.document.getElementsByClassName("HouseList")[0].getElementsByTagName("ul")[0].offsetHeight - (baiduMap.height - 153));
				} else {
					windoweight = (window.document.getElementsByClassName("HouseList")[0].getElementsByTagName("ul")[0].offsetHeight - (baiduMap.height - 106));
				}
				if(scrTop == windoweight) {
					baiduMap.scroll = false;
					baiduMap.page += 1;
					if(!(baiduMap.map.getZoom() > baiduMap.maxRegion)) {
						document.getElementById("loading").style.display = "block"
					}
					if(baiduMap.navIsActive == 3) {
						baiduMap.regionHouse(1);

					} else {
						baiduMap.regionHouse(2);

					}

				}
			}
		},

		//设置地图中心点
		SetMap: function(lat, lng, size) {
			baiduMap.map.setZoom(size);
			baiduMap.map.bdpoit = new BMap.Point(lng, lat);
			baiduMap.map.panTo(baiduMap.map.bdpoit); //将地图移动到点
			if(baiduMap.navIsActive == 3) { //3为学区房
				baiduMap.showSchool();
			} else {
				baiduMap.showHouse();
			}
		},
		//设置地图中心点
		SetMap1: function(lat, lng, size) {
			baiduMap.map.bdpoit = new BMap.Point(lng, lat);
			baiduMap.map.centerAndZoom(baiduMap.map.bdpoit, size);
		},
		//构造 覆盖物
		overlay: function(point, text, mouseoverText) {
			this._point = point;
			this._text = text;
			this._overText = mouseoverText;
		},
		//下拉选择不限
		navNormal: function(data, text) {
			if(data.ActiveId!=undefined && data.ActiveId!=null && data.ActiveId!=''){
				data.ActiveId = '';
				data.ActiveText = text;
				
			}
			baiduMap.selectSchool='';
			baiduMap.showSelectList = -1;
			
			if(text=="类型"|| text=="类型不限"){
				baiduMap.schoolProjectType=0;
			}
			if(baiduMap.navIsActive == 3) { //3为学区房
				//解决Bug #17028
				baiduMap.schoolHome=0;
				baiduMap.showSchool();

			} else {
				baiduMap.showHouse();
			}
			baiduMap.setHouseHigth();
		},
		//重置筛选项
		clearSelect: function(data, text) {
			if(data.ActiveId!=undefined && data.ActiveId!=null && data.ActiveId!=''){
				data.ActiveId = '';
				data.ActiveText = text;
				
			}else{
			baiduMap.selectSchool='';
				
			}
			
			if(text=="类型"&& text=="类型不限"){
				baiduMap.schoolProjectType=0;
			}
			baiduMap.showSelectList = -1;
			
			baiduMap.setHouseHigth();
		},
		//选择左侧菜单
		navLeft: function(int) {

			baiduMap.clearSelect(baiduMap.regionList, '区域');
			baiduMap.clearSelect(baiduMap.typeList, '类型');
			baiduMap.clearSelect(baiduMap.priceList, '价格');
			baiduMap.clearSelect(baiduMap.schoolList, '类别');
			baiduMap.clearSelect(baiduMap.subWayList, '地铁');

			baiduMap.page = 1;
			this.isSchool = ""
			this.isSubWay = ""
			this.isPayment = ""
			this.isExistinfo = ""
			switch(int) {
				case 0:
					this.navIsActive = 0
					break;
				case 1:
					this.navIsActive = 1
					this.isPayment = 1
					break;
				case 2:
					this.navIsActive = 2
					this.isExistinfo = 1
					break;
				case 3:
					this.navIsActive = 3
					this.isSchool = 1
					baiduMap.schoolHome = 0
					baiduMap.schoolProjectType=0
					break;
				case 4:
					window.location.href = "https://sy.fangxiaoer.com/static/station_map.htm";
					break;
				case 5:
					this.navIsActive = 5
					this.rentType = 1
					break;
				case 6:
					this.navIsActive = 6
					this.rentType = 2
					break;
				default:
					break;
			}
			baiduMap.SetMap1(41.798142, 123.439941, baiduMap.maxRegion - 2);
			if(this.navIsActive == 3) {
				baiduMap.showSchool()
			} else {
				baiduMap.showHouse()

			}

		},
		//筛选条件 下拉菜单初始化
		navInit: function(url, nav) {
			ajax({
				type: "POST",
				url: url,
				dataType: "json",
				data: {},
				success: function(data) {
					nav.content = data.content
				},
				error: function(data) {
					console.log(data)
				}
			})
		},
		//筛选条件 下拉菜单选择
		navSelect: function(showData, dataName, dataId) {
			baiduMap.page = 1;
			showData.ActiveText = dataName;
			this.showSelectList = -1;
			if(dataName=='普宅'||dataName=='洋房'||dataName=='商铺'||dataName=='别墅'){
				baiduMap.schoolProjectType=dataId;
			}
			if(dataName.length>2){
				baiduMap.selectSchool='';
			}
			if(dataName=="小学" ||dataName=="中学"){
				baiduMap.selectSchool='';
				
			}
			
			showData.ActiveId = dataId;
			baiduMap.searchZoomLevel = baiduMap.maxRegion;
			if(baiduMap.navIsActive == 3) { //3为学区房
				baiduMap.schoolHome=0;
				baiduMap.showSchool();
			} else {
				baiduMap.showHouse();
			}
		},
		deleteSearchValue: function() {
			this.searchValue = '';
			$("#txtkeys").val('');
			if(baiduMap.navIsActive == 3) { //3为学区房
				baiduMap.schoolHome=0;
				baiduMap.showSchool();
			} else {
				baiduMap.showHouse();
			}
			baiduMap.setHouseHigth();
			$("#search2017 img").hide();
		},
		deleteAllValue: function() {
			this.searchValue = '';
			$("#txtkeys").val('');
			baiduMap.showSelectList = -1;
			baiduMap.schoolProjectType=0;
			this.subWayList.ActiveText = "地铁";
			this.subWayList.ActiveId = '';
			this.schoolList.ActiveText = "类别";
			this.schoolList.ActiveId = '';
			this.priceList.ActiveText = "价格";
			this.priceList.ActiveId = '';
			this.typeList.ActiveText = "类型";
			this.typeList.ActiveId = '';
			this.regionList.ActiveText = "区域";
			this.regionList.ActiveId = "";
			this.selectSchool = "";
			if(baiduMap.navIsActive == 3) { //3为学区房
				baiduMap.schoolHome=0;
				baiduMap.showSchool();
			} else {
				baiduMap.showHouse();
			}
			baiduMap.setHouseHigth();
			$("#search2017 img").hide();
		},
		setHouseHigth: function() {
			if(this.subWayList.ActiveId != '' || this.schoolList.ActiveId != '' || this.priceList.ActiveId != '' || this.typeList.ActiveId != '' || this.regionList.ActiveId != '' || this.searchValue != '') {
				baiduMap.houseListheigth = window.innerHeight - 50 - 47;
			} else {
				baiduMap.houseListheigth = window.innerHeight - 50;
			}
		},
		//获取地图房源
		newHouseList: function() {
			//新房获取地图点
			ajax({
				type: "POST",
				url: "/searchNewProjectMap",
				dataType: "json",
				async: false,
				data: {
					zoomLevel: baiduMap.map.getZoom(),
					moneyId: baiduMap.priceList.ActiveId, //价格id-----------------------
					regionId: baiduMap.regionList.ActiveId, //区域id-------------------------
					subWayStationId: "", //地铁站id
					subWayId: baiduMap.subWayList.ActiveId, //地铁线id------------------------------
					latitude: "", //
					longitude: "", //
					distanceId: "", //
					schoolId: "", //学校id
					schoolTypeId: baiduMap.schoolList.ActiveId, //学校类型id-------------------------
					projectName: baiduMap.searchValue, //项目名称
					roomType: "", //户型id
					isSubWay: baiduMap.isSubWay, //是不是地铁 非空为是
					isSchoolArea: baiduMap.isSchool, //是不是学区房  非空为是
					projectType: baiduMap.typeList.ActiveId, //项目类型id
					payment: baiduMap.isPayment, //baiduMap.loopList.ActiveId,//是不是低首付 1为是!!!!!!!!!!!!
					existinfo: baiduMap.isExistinfo, //是不是现房  1为是
					leftLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngLeft : '', //四个坐标点  接口名有错误（错着写是对的！！）
					leftLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latTop : '',
					rightLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngRight : '',
					rightLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latBottom : '',
					tab: baiduMap.decidedType,
					projectId: baiduMap.projectId,
					pageSize: 10000, //分页
				},
				beforeSend: function() {},
				success: function(data) {
					var playList = new Array();
					if(data.status == 1) {
						playList = data.content.projectList;
					}
					if(data.content.zoomLevel >= baiduMap.maxRegion) {
						baiduMap.newHousList = playList;
						window.document.getElementsByClassName("mapHouseList")[0].scrollTop = 0
						//加地铁站名
						baiduMap.isSubWay != "" ? baiduMap.subWayStationList() : ""
						for(var i = 0; i < playList.length; i++) {
							//项目链接
							var url1 = "https://sy.fangxiaoer.com/house/" + playList[i].projectId + "-" + playList[i].projectType + ".htm";
							//项目价格无价格一房一价   有则显示最低价
							var Price
							if(playList[i].projectStatus.toString() != '3') { //是否是已经售罄
								if(playList[i].projectPrice == null || playList[i].projectPrice.length == 0) {
									Price = "待定";
								} else {
									Price = playList[i].projectPrice[0].showPrice
									for(var pri = 1; pri < playList[i].projectPrice.length; pri++) {
										if(parseInt(playList[i].projectPrice[pri].showPrice) < parseInt(Price)) {
											Price = playList[i].projectPrice[pri].showPrice
										}
									}
								}
							} else {
								if(playList[i].projectType.toString() != '2') { //是否是别墅类型
									if(playList[i].subPrice != null && playList[i].subPrice.price != 0) {
										Price = playList[i].subPrice.price + '元/㎡';
									} else {
										Price = "新房已售完";
									}
								} else {
									Price = "新房已售完";
								}
							}
							baiduMap.showRegionOverlay(new BMap.Point(playList[i].longitude, playList[i].latitude), playList[i].projectName, Price, url1, baiduMap.regionList.ActiveId, "", playList[i].projectId, playList[i].projectStatus);

						}
					} else {
						for(var i = 0; i < playList.length; i++) {
							baiduMap.showRegionOverlay(new BMap.Point(playList[i].longitude, playList[i].latitude), playList[i].regionName, playList[i].projectCount + "个", "", playList[i].regionId, "", "", playList[i].projectStatus);
						}
					}
				},
				error: function(data) {
					console.log(data)
				}
			});
			baiduMap.regionHouse(2); //区域时房源列表（区域时 排序按照新房列表页）

		},
		//房源列表
		regionHouse: function(int) {
			if(int == 1) {
				baiduMap.SchoolHouseList();

			} else {
				baiduMap.newHouseListRegionAjax();

			}

		},
		//新房列表区域下的数据获取
		newHouseListRegionAjax: function() {
			ajax({
				type: "POST",
				url: "/searchNewProjectMap",
				async: false,
				dataType: "json",
				data: {
					moneyId: baiduMap.priceList.ActiveId, //价格id
					regionId: baiduMap.regionList.ActiveId, //区域id
					subWayStationId: "", //地铁站id
					subWayId: baiduMap.subWayList.ActiveId, //地铁线id
					distanceId: "", //
					schoolId: "", //学校id
					schoolTypeId: baiduMap.schoolList.ActiveId, //学校类型id
					projectName: baiduMap.searchValue, //项目名称
					roomType: "", //几户型
					isSubWay: baiduMap.isSubWay, //是不是地铁房
					isSchoolArea: baiduMap.isSchool, //是不是学区房
					projectType: baiduMap.typeList.ActiveId, //项目类型
					page: baiduMap.page, //页数
					pageSize: 10, //分页
					leftLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngLeft : '', //四个坐标点  接口名有错误（错着写是对的！！）
					leftLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latTop : '',
					rightLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngRight : '',
					rightLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latBottom : '',
					zoomLevel: baiduMap.map.getZoom() > 13 ? baiduMap.map.getZoom() : 14,
					tab: "1",
					projectId: baiduMap.projectId,
				},
				beforeSend: function() {},
				success: function(data) {
					var playList = new Array();
					if(data.status == 1) {
						playList = data.content.projectList;
						if(playList.length == 0 && baiduMap.page == 1) {
							if(baiduMap.sreachResultType) {
								$("#recommentT1").text("未找到符合条件的楼盘");
								$("#recommentT2").text("可填写需求单，专属定制找房。");
							} else {
								$("#recommentT1").text("地图范围内没找到楼盘");
								$("#recommentT2").text("建议您：拖动地图更改位置或填写需求单，专属定制找房。");
							}
							$("#recommentOrder").show();
						} else {
							$("#recommentOrder").hide();
						}
						document.getElementById("loading").style.display = "none";
						if(baiduMap.page == 1) {
							baiduMap.newHousList = playList;
						} else {
							for(var i = 0; i < playList.length; i++) { //10  是分页大小
								baiduMap.newHousList.push(playList[i]);
							}
							baiduMap.scroll = true;
						}
						baiduMap.projectId = "";
					}
				},
				error: function(data) {
					document.getElementById("loading").style.display = "none";
					console.log(data)
				}
			});
		},
		
		
	
		//地图找房学校数据获取
		SchoolListRegionAjax: function() {
			ajax({
				type: "POST",
				url: "/searchSchoolListForMap",
				async: false,
				dataType: "json",
				data: {
					searchKey: baiduMap.searchValue,
					schoolTypeId: baiduMap.schoolList.ActiveId, //学校类型id
					regionId: baiduMap.regionList.ActiveId, //区域id
					page: 1, //页数
					pageSize: 10000, //分页
					isSchoolArea: baiduMap.isSchool, //是不是学区房  非空为是
					leftLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngLeft : '', //四个坐标点  接口名有错误（错着写是对的！！）
					leftLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latTop : '',
					rightLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngRight : '',
					rightLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latBottom : '',

				},
				beforeSend: function() {},
				success: function(data) {
					baiduMap.map.clearOverlays(); //清除覆盖物
					baiduMap.schoolHome = 0;

					var playList = new Array();
					if(data.status == 1) {
						baiduMap.SchoolHouseList();
						playList = data.content;
						var Price='';
						for(var i = 0; i < playList.length; i++) {
							if(playList[i].price == null || playList[i].price.length == 0) {
									Price = "待定";
								} else {
									Price = playList[i].price[0].showPrice
								}
							
							
							baiduMap.showSchoolOverlay(new BMap.Point(playList[i].longitude, playList[i].latitude), playList[i].schoolName, playList[i].regionId, playList[i].schoolType, playList[i].id, "", 0,Price,playList[i].regionName,'');
							//baiduMap.showRegionOverlay(new BMap.Point(playList[i].longitude,playList[i].latitude),playList[i].regionName,playList[i].projectCount+"个","",playList[i].regionId,"","",playList[i].projectStatus);
						}

					}
				},
				error: function(data) {
					document.getElementById("loading").style.display = "none";
					console.log(data)
				}
			});
		},

		//学校关联房型列表数据获取
		SchoolHouseListAjax: function() {
			ajax({
				type: "POST",
				url: "/viewNewSchoolHouseList",
				async: false,
				dataType: "json",
				data: {
					searchKey: baiduMap.searchValue,
					schoolTypeId: baiduMap.schoolList.ActiveId, //学校类型id
					regionId: baiduMap.regionList.ActiveId, //区域id
					schoolAreaId:'',
					projectType:baiduMap.schoolProjectType,//房源类型
					isSchoolArea: baiduMap.isSchool, //是不是学区房  非空为是
					page: baiduMap.page, //页数
					pageSize: 10000, //分页
					leftLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngLeft : '', //四个坐标点  接口名有错误（错着写是对的！！）
					leftLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latTop : '',
					rightLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngRight : '',
					rightLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latBottom : '',

				},
				beforeSend: function() {},
				success: function(data) {
					baiduMap.map.clearOverlays(); //清除覆盖物
					baiduMap.schoolHome = 1;
					var playList = new Array();
					if(data.status == 1) {

						playList = data.content;
						if(playList.length == 0 && baiduMap.page == 1 ) {
							if(baiduMap.page == 1){
								if(baiduMap.sreachResultType) {
								$("#recommentT1").text("未找到符合条件的楼盘");
								$("#recommentT2").text("可填写需求单，专属定制找房。");
							} else {
								$("#recommentT1").text("地图范围内没找到楼盘");
								$("#recommentT2").text("建议您：拖动地图更改位置或填写需求单，专属定制找房。");
							}
							$("#recommentOrder").show();
							}
							
						} else {
							$("#recommentOrder").hide();
						}
						document.getElementById("loading").style.display = "none";
						if(baiduMap.page == 1) {
							baiduMap.newHousList = playList;
						} else {
							for(var i = 0; i < playList.length; i++) { //10  是分页大小
								baiduMap.newHousList.push(playList[i]);
							}
							baiduMap.scroll = true;
						}
						baiduMap.projectId = "";

							var Price;
							//回传地图显示选中学校
							if(baiduMap.selectSchool.id != '') {
								
								baiduMap.showSchoolOverlay(new BMap.Point(baiduMap.selectSchool.longitude, baiduMap.selectSchool.latitude),baiduMap.selectSchool.schoolName, baiduMap.selectSchool.regionId, baiduMap.selectSchool.schoolType, baiduMap.selectSchool.id,"", 0,Price,baiduMap.selectSchool.aeraName,'');
							}

						for(var i = 0; i < playList.length; i++) {
							var url1 = "https://sy.fangxiaoer.com/house/" + playList[i].projectId + "-" + playList[i].projectType + ".htm";
							//项目价格无价格一房一价   有则显示最低价
							
							if(playList[i].price == '' || playList[i].price == null || playList[i].price.length == 0) {
								Price = "待定";
							} else {
								Price = playList[i].price[0].showPrice;
							}

							baiduMap.showSchoolOverlay(new BMap.Point(playList[i].longitude, playList[i].latitude), playList[i].projectName, playList[i].regionID, playList[i].schoolType, playList[i].schoolId, url1, 1,Price,playList[i].regionName,playList[i].projectStatus);

						}

					}
				},
				error: function(data) {
					document.getElementById("loading").style.display = "none";
					console.log(data)
				}
			});
		},

		//左侧获取学校房型列表数据
		SchoolHouseList: function() {
			ajax({
				url: "/viewNewSchoolHouseList",
				type: "POST",
				async: false,
				dataType: "json",
				data: {
					searchKey: baiduMap.searchValue,
					schoolTypeId: baiduMap.schoolList.ActiveId, //学校类型id
					regionId: baiduMap.regionList.ActiveId, //区域id
					page: baiduMap.page, //页数
					projectType:baiduMap.schoolProjectType,
					schoolAreaId:'',
					pageSize: 10, //分页
					//解决Bug #17029
//					leftLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngLeft : '', //四个坐标点  接口名有错误（错着写是对的！！）
//					leftLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latTop : '',
//					rightLng: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.lngRight : '',
//					rightLat: baiduMap.map.getZoom() >= baiduMap.maxRegion ? baiduMap.latBottom : '',

				},
				beforeSend: function() {},
				success: function(data) {
					var playList = new Array();
					if(data.status == 1) {
						playList = data.content;
						if(playList.length == 0 && baiduMap.page == 1) {
							if(baiduMap.sreachResultType) {
								$("#recommentT1").text("未找到符合条件的楼盘");
								$("#recommentT2").text("可填写需求单，专属定制找房。");
							} else {
								$("#recommentT1").text("地图范围内没找到楼盘");
								$("#recommentT2").text("建议您：拖动地图更改位置或填写需求单，专属定制找房。");
							}
							$("#recommentOrder").show();
						} else {
							$("#recommentOrder").hide();
						}
						document.getElementById("loading").style.display = "none";
						if(playList.length!=0){
							if(baiduMap.page == 1) {
							baiduMap.newHousList = playList;
						} else {
							for(var i = 0; i < playList.length; i++) { //10  是分页大小
								baiduMap.newHousList.push(playList[i]);
							}
							baiduMap.scroll = true;
						}
						}else{
							if(baiduMap.page == 1){
								baiduMap.newHousList='';
							}
							
						}
					
						
						baiduMap.projectId = "";
					}

				},
				error: function(data) {
					document.getElementById("loading").style.display = "none";
					console.log(data)
				}
			});
		},
		//获取地铁站
		subWayStationList: function() {
			ajax({
				type: "POST",
				url: "https://ltapi.fangxiaoer.com/apiv1/house/getSubStationFilter",
				dataType: "json",
				data: {
					subWayId: baiduMap.subWayList.ActiveId
				},
				beforeSend: function() {},
				success: function(data) {
					if(data.status == 1) {
						for(var i = 0; i < data.content.length; i++) {
							baiduMap.showRegionOverlay(new BMap.Point(data.content[i].longitude, data.content[i].latitude), data.content[i].stationName, "", "", "", data.content[i].stationId, "", data.content[i].projectStatus);
						}
					}
				},
				error: function(data) {
					console.log(data)
				}
			});
		},
		//定义学校区域的 覆盖物
		showSchoolOverlay: function(point, schoolName, regionId, schoolType, schoolId, mapUrl, typeid,price,areaName,projectStatus) {
			ComplexCustomOverlay.prototype = new BMap.Overlay();
			ComplexCustomOverlay.prototype.initialize = function(map) {
				this._map = map;

				var div = this._div = document.createElement("div");
				div.style.position = "absolute";
//				div.style.backgroundColor = "#ff5200";
				div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
				var zindex=BMap.Overlay.getZIndex(this._point.lat);

				var span = this._span = document.createElement("span");
				div.appendChild(span);
				this._text != undefined && typeid == 1 ? span.className = "shadow" : "";
				this._text.length >8 && this._text.length <=13? span.className = "widthName" : "";
				this._text.length >13 ? span.className = "widthName2" : "";
				span.appendChild(document.createTextNode(this._text));
				var that = this;
				var arrow = this._arrow = document.createElement("div");
				baiduMap.houseType == 1 //1为新房
				var spitem = "";
				if(projectStatus == 3) {
						spitem = "soldoutProject"
					} else {
						spitem = "showHouse";
					}
				if(baiduMap.houseType == 1) {
					//   baiduMap.map.getZoom()<baiduMap.maxRegion?div.className="showShool":div.className=spitem//小于14就是region 大于等于14就是house
					typeid != 1 ? div.className = "showShool" : div.className = spitem
					
				}
				
				if(projectStatus == 3) {
						div.style.backgroundColor = "#8099af";
						
					}
				if(typeid==1){
				var span1 = this._span = document.createElement("b");
					div.appendChild(span1);
					span1.appendChild(document.createTextNode(price));
				div.style.zIndex = 0;	
				}
				

				//              if(baiduMap.map.getZoom()>=baiduMap.maxRegion){
				//                  div.setAttribute("projectId",housingId);
				//              }
				arrow.style.position = "absolute";
				arrow.style.width = "11px";
				arrow.style.height = "10px";
				arrow.style.top = "22px";
				arrow.style.left = "20px";
				div.appendChild(arrow);

				div.onmouseover = function() {

//					this.style.backgroundColor = "#41a8f3";
					this.className="curshowSchool";
					
					if(typeid==1){
					if(baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxPlat) {
							div.className = "showHouseHover"
						} else if(baiduMap.map.getZoom() >= baiduMap.maxRegion) {
							baiduMap.houseType == 1 ? div.className = "showHouseHover" : div.className = "showPlat"
						}	
					}
					

					this.style.zIndex = "99999";
				}
				div.onmouseout = function() {
					if(projectStatus == 3) {
						var spitem = "soldoutProject";
						this.className="showShool";
					}else{
						var spitem = "showHouse";
						this.className="showShool";
					}
					
					
//					this.style.backgroundColor = "#ff5200";
					if(zindex<0){
						this.style.zIndex = zindex;
					}else{
						this.style.zIndex = 1;
					}

					
					if(typeid==1){
						if(baiduMap.map.getZoom() > baiduMap.maxPlat) {
							div.className = spitem;
						} else if(baiduMap.map.getZoom() >= baiduMap.maxRegion) {
							baiduMap.houseType == 1 ? div.className = spitem : div.className = "showPlat"
						}
					}
					

				}
				var clickPoint = this._point
				var urlText = mapUrl
				var clickText = schoolName
				div.onclick = function() {
					if(typeid == 0 ) {

						// baiduMap.SetMap1(clickPoint.lat,clickPoint.lng,baiduMap.maxRegion+1)
						if(baiduMap.selectSchool.schoolName!=schoolName){
								var selectSchool = new Object();
						selectSchool.regionId = regionId;
						selectSchool.latitude = point.lat;
						selectSchool.longitude = point.lng;
						selectSchool.schoolType = schoolType;
						selectSchool.id = schoolId;
						selectSchool.schoolName = schoolName;
						selectSchool.areaName = areaName;

						baiduMap.selectSchool = selectSchool;
							baiduMap.searchZoomLevel = baiduMap.maxRegion;
						baiduMap.regionList.ActiveId = regionId;
						baiduMap.regionList.ActiveText = areaName;
						baiduMap.page=1;
						baiduMap.decidedType = "1";
						baiduMap.schoolHome = 1;
						baiduMap.showHouseForSearch(3);
						}
					

					
					} else {
						window.open(urlText);
					}

				}
				baiduMap.map.getPanes().labelPane.appendChild(div);
				return div;
			}
			ComplexCustomOverlay.prototype.draw = function() {
				var map = this._map;
				var pixel = map.pointToOverlayPixel(this._point);
				this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
				this._div.style.top = pixel.y - 30 + "px";
			}
			var myCompOverlay = new ComplexCustomOverlay(point, schoolName)
			baiduMap.map.addOverlay(myCompOverlay);
		},

		//定义区域或板块的 覆盖物
		showRegionOverlay: function(point, text, num, mapUrl, regionId, subId, housingId, projectStatus) {
			ComplexCustomOverlay.prototype = new BMap.Overlay();
			ComplexCustomOverlay.prototype.initialize = function(map) {
				this._map = map;
				var div = this._div = document.createElement("div");
				div.style.position = "absolute";
				div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
				var span = this._span = document.createElement("span");
				div.appendChild(span);
				this._text != undefined && this._text.length > 5 ? span.className = "shadow" : ""
				span.appendChild(document.createTextNode(this._text));
				var that = this;
				var arrow = this._arrow = document.createElement("div");
				baiduMap.houseType == 1
				if(subId != "") {
					baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxRegion ? div.className = "showsubWay" : ""
					div.style.backgroundColor = "#fffff";
				} else {
					var spitem = "";
					if(projectStatus == 3) {
						spitem = "soldoutProject"
					} else {
						spitem = "showHouse";
					}
					if(baiduMap.houseType == 1) {
						baiduMap.map.getZoom() < baiduMap.maxRegion ? div.className = "showRegion" : div.className = spitem //小于14就是region 大于等于14就是house
					}

					if(projectStatus == 3) {
						div.style.backgroundColor = "#8099af";
						div.style.zIndex = -99999;
					} else {
						div.style.backgroundColor = "#ff5200";
						div.style.zIndex = 0;
					}

					var span1 = this._span = document.createElement("b");
					div.appendChild(span1);
					span1.appendChild(document.createTextNode(this._overText));
				}
				if(baiduMap.map.getZoom() >= baiduMap.maxRegion) {
					div.setAttribute("projectId", housingId);
				}
				arrow.style.position = "absolute";
				arrow.style.width = "11px";
				arrow.style.height = "10px";
				arrow.style.top = "22px";
				arrow.style.left = "20px";
				div.appendChild(arrow);

				div.onmouseover = function() {
					if(subId != "") {
						this.style.backgroundColor = "#e66a6a";
						baiduMap.map.getZoom() > baiduMap.maxRegion ? this.className = "showsubWayHover" : ""
					} else {
						this.style.backgroundColor = "#41a8f3";
						if(baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxPlat) {
							div.className = "showHouseHover"
						} else if(baiduMap.map.getZoom() >= baiduMap.maxRegion) {
							baiduMap.houseType == 1 ? div.className = "showHouseHover" : div.className = "showPlat"
						}
					}
					this.style.zIndex = "99999";
				}
				div.onmouseout = function() {
					var spitem = "";
					if(projectStatus == 3) {
						spitem = "soldoutProject"
					} else {
						spitem = "showHouse";
					}
					if(subId != "") {
						this.style.backgroundColor = "#fff";
						baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxRegion ? this.className = "showsubWay" : ""
						this.style.zIndex = "99900"
					} else {
						if(projectStatus == 3) {
							this.style.zIndex = -99999;
							this.style.backgroundColor = "#8099af";
						} else {
							this.style.backgroundColor = "#ff5200";
							this.style.zIndex = 0;
						}
						if(baiduMap.map.getZoom() > baiduMap.maxPlat) {
							div.className = spitem;
						} else if(baiduMap.map.getZoom() >= baiduMap.maxRegion) {
							baiduMap.houseType == 1 ? div.className = spitem : div.className = "showPlat"
						}
					}
				}
				var clickPoint = this._point
				var urlText = mapUrl
				var clickText = text
				div.onclick = function() {
					if(baiduMap.map.getZoom() < baiduMap.maxRegion) {
						// baiduMap.SetMap1(clickPoint.lat,clickPoint.lng,baiduMap.maxRegion+1)
						baiduMap.searchZoomLevel = baiduMap.maxRegion;
						baiduMap.regionList.ActiveId = regionId;
						baiduMap.regionList.ActiveText = clickText;
						baiduMap.decidedType = "1";
						baiduMap.showHouseForSearch(3);
					} else {
						if(subId == "") {
							window.open(urlText);
						}
					}
				}
				baiduMap.map.getPanes().labelPane.appendChild(div);
				return div;
			}
			ComplexCustomOverlay.prototype.draw = function() {
				var map = this._map;
				var pixel = map.pointToOverlayPixel(this._point);
				this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
				this._div.style.top = pixel.y - 30 + "px";
			}
			var myCompOverlay = new ComplexCustomOverlay(point, text, num)
			baiduMap.map.addOverlay(myCompOverlay);
		},
		//每次操作后 重新获取所有数据
		showHouse: function() {
			baiduMap.map.clearOverlays(); //清除覆盖物
			var showPoint = this.map.getBounds();
			var bssw = showPoint.getSouthWest() //可视区域左下角
			var bsne = showPoint.getNorthEast() //可视区域右上角
			this.lngRight = baiduMap.map.getZoom() >= baiduMap.maxRegion ? bsne.lng : '';
			this.lngLeft = baiduMap.map.getZoom() >= baiduMap.maxRegion ? bssw.lng : '';
			this.latTop = baiduMap.map.getZoom() >= baiduMap.maxRegion ? bsne.lat : '';
			this.latBottom = baiduMap.map.getZoom() >= baiduMap.maxRegion ? bssw.lat : '';
			this.newHouseList()
			if(baiduMap.isSubWay != "") {
				baiduMap.map.addOverlay(polyline[baiduMap.subWayList.ActiveId]);
			}
		},
		//每次操作后 重新获取所有数据
		showSchool: function() {

			baiduMap.map.clearOverlays(); //清除覆盖物
			var showPoint = this.map.getBounds();
			var bssw = showPoint.getSouthWest() //可视区域左下角
			var bsne = showPoint.getNorthEast() //可视区域右上角
			this.lngRight = baiduMap.map.getZoom() >= baiduMap.maxRegion ? bsne.lng : '';
			this.lngLeft = baiduMap.map.getZoom() >= baiduMap.maxRegion ? bssw.lng : '';
			this.latTop = baiduMap.map.getZoom() >= baiduMap.maxRegion ? bsne.lat : '';
			this.latBottom = baiduMap.map.getZoom() >= baiduMap.maxRegion ? bssw.lat : '';

			if(baiduMap.schoolHome == 0) {
				baiduMap.SchoolListRegionAjax()
			} else {
				baiduMap.SchoolHouseListAjax()
			}
			//          	this.newHouseList()

			if(baiduMap.isSubWay != "") {
				baiduMap.map.addOverlay(polyline[baiduMap.subWayList.ActiveId]);
			}
		},

		showHouseForSearch: function(type) { //type1搜索点击清除筛选项//type3区域点击//type2当前层级筛选//type4只保留区域其他清空
			baiduMap.map.clearOverlays(); //清除覆盖物
			this.lngRight = "";
			this.lngLeft = "";
			this.latTop = "";
			this.latBottom = "";
			if(baiduMap.isSubWay != "") {
				baiduMap.map.addOverlay(polyline[baiduMap.subWayList.ActiveId]);
			}
			if(type == 1) {
				baiduMap.regionList.ActiveId = '';
				baiduMap.regionList.ActiveText = '区域';
				baiduMap.schoolList.ActiveId = '';
				baiduMap.schoolList.ActiveText = '类别';
				baiduMap.priceList.ActiveId = '';
				baiduMap.priceList.ActiveText = '价格';
				baiduMap.subWayList.ActiveId = '';
				baiduMap.subWayList.ActiveText = '地铁';
				baiduMap.typeList.ActiveId = '';
				baiduMap.typeList.ActiveText = '类型';
			} else if(type == 4) {
				baiduMap.schoolList.ActiveId = '';
				baiduMap.schoolList.ActiveText = '类别';
				baiduMap.priceList.ActiveId = '';
				baiduMap.priceList.ActiveText = '价格';
				baiduMap.subWayList.ActiveId = '';
				baiduMap.subWayList.ActiveText = '地铁';
				baiduMap.typeList.ActiveId = '';
				baiduMap.typeList.ActiveText = '类型';
			}
			//新房获取地图点
			ajax({
				type: "POST",
				url: "/searchNewProjectMap",
				dataType: "json",
				async: false,
				data: {
					zoomLevel: baiduMap.searchZoomLevel,
					moneyId: baiduMap.priceList.ActiveId, //价格id-----------------------
					regionId: baiduMap.regionList.ActiveId, //区域id-------------------------
					subWayStationId: "", //地铁站id
					subWayId: baiduMap.subWayList.ActiveId, //地铁线id------------------------------
					latitude: "", //
					longitude: "", //
					distanceId: "", //
					schoolId: "", //学校id
					schoolTypeId: baiduMap.schoolList.ActiveId, //学校类型id-------------------------
					projectName: baiduMap.searchValue, //项目名称
					roomType: "", //户型id
					isSubWay: baiduMap.isSubWay, //是不是地铁 非空为是
					isSchoolArea: baiduMap.isSchool, //是不是学区房  非空为是
					projectType: baiduMap.typeList.ActiveId, //项目类型id
					payment: baiduMap.isPayment, //baiduMap.loopList.ActiveId,//是不是低首付 1为是!!!!!!!!!!!!
					existinfo: baiduMap.isExistinfo, //是不是现房  1为是
					tab: baiduMap.decidedType,
					projectId: baiduMap.projectId,
					pageSize: 1000000, //分页
				},
				beforeSend: function() {},
				success: function(data) {
					if(type == 1 || type == 4) {
						if(data.content.projectList != '' && data.content.projectList != null && data.content.projectList.length == 1 && data.content.projectList[0].projectId != undefined) {
							baiduMap.searchZoomLevel = baiduMap.maxRegion;
						}
						if(baiduMap.searchZoomLevel != baiduMap.map.getZoom()) {
							baiduMap.SetMap1(data.content.la, data.content.lo, baiduMap.searchZoomLevel);
						} else {
							baiduMap.SetMap1(data.content.la, data.content.lo, baiduMap.searchZoomLevel);
							if(baiduMap.navIsActive == 3) { //3为学区房
								baiduMap.showSchool();
							} else {
								baiduMap.showHouse();
							}
						}
						if(baiduMap.sreachRegionSwitch) {
							$("#txtkeys").val('');
							$("#search2017 img").hide();
							baiduMap.sreachRegionSwitch = false;
						}
					} else if(type == 2) {
						if(data.content.projectList != '' && data.content.projectList != null && data.content.projectList.length == 1 && data.content.projectList[0].projectId != undefined) {
							baiduMap.SetMap1(data.content.la, data.content.lo, baiduMap.maxRegion);
						} else if(data.content.projectList != null && data.content.projectList.length == 0) {} else {
							baiduMap.SetMap1(data.content.la, data.content.lo, baiduMap.map.getZoom());
						}
						if(baiduMap.navIsActive == 3) { //3为学区房
							baiduMap.showSchool();
						} else {
							baiduMap.showHouse();
						}
					} else if(type == 3) {
						
						
						//baiduMap.SetMap1(data.content.la, data.content.lo, baiduMap.searchZoomLevel);
						
						if(baiduMap.navIsActive == 3) { //3为学区房
							//解决Bug #17014
						if(baiduMap.selectSchool!=''|| baiduMap.selectSchool!=null || baiduMap.selectSchool!=undefined){
						baiduMap.SetMap1(baiduMap.selectSchool.latitude, baiduMap.selectSchool.longitude, baiduMap.searchZoomLevel);	
							
						}else{
						baiduMap.SetMap1(data.content.la, data.content.lo, baiduMap.searchZoomLevel);	
						}
							baiduMap.showSchool();
						} else {
							baiduMap.SetMap1(data.content.la, data.content.lo, baiduMap.searchZoomLevel);	
							
							baiduMap.showHouse();
						}

					} else {
						baiduMap.SetMap(data.content.la, data.content.lo, baiduMap.searchZoomLevel);
					}
					baiduMap.sreachResultType = true;
					baiduMap.setHouseHigth();
				},
				error: function(data) {
					console.log(data)
				}
			});
		},

		relasionShow: function(projectId, type) {
			if(type == 1) {
				$("[projectId='" + projectId + "']").mouseover();
			} else {
				$("[projectId='" + projectId + "']").mouseout();
			}
		},
	}
})
//构造自定义覆盖物
function ComplexCustomOverlay(point, text, num) {
	this._point = point;
	this._text = text;
	this._overText = num;
}
//获取地址栏参数
function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
            var r = window.location.search.substr(1).match(reg);  //匹配目标参数
            if (r != null) return unescape(r[2]); return null; //返回参数值
        }
baiduMap.init()