/**
 * Created by Administrator on 2017/8/29.
 */
// var msg =[{"trendingTime":1500912000000,"unitPrice":7361},{"trendingTime":1500739200000,"unitPrice":7356},{"trendingTime":1500134400000,"unitPrice":7755},{"trendingTime":1499529600000,"unitPrice":7740},{"trendingTime":1498924800000,"unitPrice":7723},{"trendingTime":1498320000000,"unitPrice":7643},{"trendingTime":1497715200000,"unitPrice":7428},{"trendingTime":1497196800000,"unitPrice":7327},{"trendingTime":1496592000000,"unitPrice":7117},{"trendingTime":1495987200000,"unitPrice":7163},{"trendingTime":1495382400000,"unitPrice":7448},{"trendingTime":1494777600000,"unitPrice":7508},{"trendingTime":1494172800000,"unitPrice":7415},{"trendingTime":1493568000000,"unitPrice":7307},{"trendingTime":1492963200000,"unitPrice":7217},{"trendingTime":1492358400000,"unitPrice":7070},{"trendingTime":1491667200000,"unitPrice":6889},{"trendingTime":1491062400000,"unitPrice":6945},{"trendingTime":1490457600000,"unitPrice":6991},{"trendingTime":1489852800000,"unitPrice":7024},{"trendingTime":1489248000000,"unitPrice":7068},{"trendingTime":1488643200000,"unitPrice":7645},{"trendingTime":1488038400000,"unitPrice":7270},{"trendingTime":1487433600000,"unitPrice":7215},{"trendingTime":1486828800000,"unitPrice":7285}];
var msg = JSON.parse(highChartsData);
var length = msg.length;
var myday = msg[0].trendingTime;
var myprice = msg[0].unitPrice;
var shijian=new Date(msg[0].trendingTime)
$(".dashuju_nav span").html("　本周均价 "+msg[0].unitPrice+"元/m²　环比上周 <i>"+((msg[0].unitPrice-msg[1].unitPrice)/msg[1].unitPrice*100).toFixed(2)+"%</i>")
if(((msg[0].unitPrice-msg[1].unitPrice)/msg[1].unitPrice*100).toFixed(2)>="0"){
    $(".dashuju_nav i").addClass("dsj_up")
}else{
    $(".dashuju_nav i").addClass("dsj_down")
}
myArrayDate = [[myday, myprice]];
maxPrice=myprice;
minPrice=myprice;
for (i = 1; i < length; i++) {
    var children = [msg[i].trendingTime, msg[i].unitPrice];
    if(maxPrice < msg[i].unitPrice) {
        maxPrice = msg[i].unitPrice;
    }
    if(minPrice > msg[i].unitPrice) {
        minPrice = msg[i].unitPrice;
    }
    myArrayDate.push(children);
}

//大数据
jQuery(function () {
    jQuery(document).ready(function () {
        Highcharts.setOptions({
            global: {
                useUTC: false
            }
        });
        createChartDefault('1617206587', '', '', '', '', '357', '770', '', '');
    });
});
function createChartDefault(newcode, city, district, commerce, dis, height, width, titleshow, year) {
    var minvalue, settickInterval, setdataLabels, test = true, setfontSize = '8pt', num = 0;
    settickInterval = 1000 * 3600 * 24 * 30;//间隔3月
    chart = new Highcharts.Chart({
        chart: {
            renderTo: 'container',
            type: 'spline',
            width: 660,
            height:340
        },

        credits: {
            enabled: false
        },
        xAxis: {
            type: 'datetime',
            lineWidth: 1, //自定义y轴宽度
            gridLineWidth: 2, //默认是0，即在图上没有纵轴间隔线
            startOnTick: true,
            showFirstLabel: false,
            gridLineDashStyle: 'Dot',
            lineColor: '#808080',
            tickInterval: settickInterval,
            labels: {
                formatter: function () {
                    num++;
                    var tempvalue;
                    tempvalue = Highcharts.dateFormat('%y年%m月', this.value);
                    return tempvalue;
                }
            }
        },
        tooltip: {
            formatter: function () {
                var tempvalue;
                yue = Highcharts.dateFormat('%m', this.x);
                tempvalue = Highcharts.dateFormat('%Y年', this.x) + yue + '月' + Highcharts.dateFormat('%e日', this.x) + '<br/>' + this.y + ' 元/m²';
                return tempvalue;
            },
            crosshairs: {                 //交叉点是否显示的一条纵线
                width: 0,
                color: "#CCC",
                dashStyle: "longdash"
            },
            crosshairs: false
        },

        legend: {//方框所在的位置(不知道怎么表达)
            enabled: false,  //是否显示图例说明
            layout: 'vertical',  //【图例】显示的样式：水平（horizontal）/垂直（vertical）
            align: 'center',
            verticalAlign: 'top',
            x: 500,
            y: 30,
            borderWidth: 0,
            lineHeight: 30
        },
        series: [{
            data: myArrayDate.reverse()
        }],
        title: {
            text: "　",
            style: { "fontSize": "14px","text-align":"left","color":"#666","font-family":"微软雅黑" },
            useHTML: true,
            x: -230, //center设置标题的位置
            y: 1 //center设置标题的位置
        },
        yAxis: {
            title: {
                text: '',
                offset: 28  //轴标题和轴线的距离可以配合title的text调整边距。
            },
            max:maxPrice * 1.1,
            min:minPrice * 0.9,
            tickPixelInterval: 50,
            tickWidth: 1, //刻度的宽度
            lineWidth: 1, //自定义y轴宽度
            gridLineWidth: 1, //默认是0，即在图上没有纵轴间隔线
            maxPadding: 0.1,
            gridLineDashStyle: 'Dot',
            lineColor: '#808080',
            labels: {
                align: 'right',
                x: 0,
                y: 0,
                formatter: function () {
                    return this.value + "元";
                }
            },
            plotLines: [{
                value: 10,
                width: 10,
                color: '#808080'
            }]
        }
    });
}
