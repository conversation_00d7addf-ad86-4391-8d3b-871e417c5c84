
var state = false; //全局正确开关
var errcount = 0; //全局错误数

//获取验证码
$(function () {
    $(".su_close").click(function () {
        $(".success_tc").hide();
    });
    $(".my_xl_list").click(function () {
        $(this).parents("li").find(".errorBox").html('');
    });
    $("#xf_hqyzm").click(function () {
        send_yzm("#xf_hqyzm", "#phone0");
    });
    $("#esf_hqyzm").click(function () {
        send_yzm("#esf_hqyzm", "#phone1");
    });
    $("#kf_hqyzm").click(function () {
        send_yzm("#kf_hqyzm", "#phone2");
    });
    $("#lppc_hqyzm").click(function () {
        send_yzm("#lppc_hqyzm", "#phone3");
    });
    $("#gh_hqyzm").click(function () {
        send_yzm("#gh_hqyzm", "#phone4");
    });
    //获取验证码按钮事件
    function send_yzm(yzm_btn, tel_input) {
        var tmobile = $.trim($(tel_input).val());
        var reg = new RegExp("^1[0-9]{10}$", "ig");
        if (!reg.test(tmobile) || tmobile == "") {
            $(tel_input).next().next().html('<label for="newtel" class="error">请填写联系方式</label>')
            $(tel_input).addClass("error")
            return false;
        } else {
            $(tel_input).next().next().html('<label for="newtel" class="error"><em class="success"></em></label>');
            $(tel_input).removeClass("error");
            $(tel_input).next().show();
            /*            $.ajax({
            type: "POST",
            data: { telNumber: obj.value },
            url: "/action/handler.ashx?method=haveuser",
            async: false,
            success: function (data) {
            if (data == 1) {     //已注册
            $("#yzmyc,.sjyzm").hide();
            $(".sjmm").show();
	
            } else if (data == 0) {   //未注册
            $("#yzmyc,.sjyzm").show();
            $(".sjmm").hide();
            }
            }
            });*/
            time(yzm_btn, "60");

            	var r = SendFunc.ajaxSendCode(tmobile); //发送短信验证码
        }
    };
})
//发送验证码
var SendFunc = {
    ajaxSendCode: function (mobile) {
    try {
    var r = 0;
    $.ajax({
    type: "POST",
    data: { mobile: mobile },
    url: "/sendSmsCode",
    async: false,
    success: function (data) {
    r = data;
    }
    });
    } catch (e) {
    console.log(e.message);
    }
    return r;
    }
    };

//验证码倒计时
/*var wait = 60;*/
function time(yzm_btn,wait) {
    if (wait == 0) {
		$(yzm_btn).prev().hide();
		$(yzm_btn).show().html("重新获取");
		wait = 60;
    } else {
		$(yzm_btn).hide();
		$(yzm_btn).prev().show().html("在" + wait + "秒后重发");
		wait--;
		setTimeout(function () {
			time(yzm_btn,wait);
		},1000);
    }
}


//检查验证码
function checkCode() {
    var tmobile = $("#mobile").val();
    var code = $.trim($("#messageCode").val());
    if (code == '') {
    confirm_messagecode($Arg("messageCode"), 1); if (errcount == 1) isCheckAllOk = false;
    } else {
    var r = SeachFunc.ajaxSendSeach();
    if (r != 1) {
    objerrstate("messageCode", errorClass, '请输入正确的验证码!');
    errcount = 1;
    } else {
    errcount = 0;
    objerrstate("messageCode", rightClass, '', '');
    }
    }
    }

//查看证码是否正确
var SeachFunc = {
    ajaxSendSeach: function () {
    try {
    var r = 0;
    $.ajax({
    type: "POST",
    data: { mobile: $Arg("mobile").value, code: $Arg("messageCode").value },
    url: "/checkPasscode",
    async: false,
    success: function (data) {
    r = data;
    if (data == 1) {
    errcount = 0;
    }
    else {
    errcount = 1;
    }
    }
    });
    } catch (e) {
    }
    return r;
    }
};
/*
$(function(){
	//新房提交
	//去掉姓名和面积，需要增加户型，验证码，描述
	$("#new_submit").click(function(){
		var tphone = $.trim($("#newtel").val());
		var yusuang = $("#new_yusuan").html();
		var quyu = $("#new_qy").html();
		fangxiaoer.ajax("upguide", "{tel:'" + tphone + "',Budget:'" + yusuang + "',region:'" + quyu + "',stype:'" + 1 + "',type:'one'}", function (data) {
            if (data == "1") {
                $(".success_tc").show();
            }
            else {
                
            }
        });
	})	
	//楼盘测评提交
	//增加验证码
	$("#lppc_submit").click(function(){
		var inputPhonepc = $.trim($("#lppctel").val());
		var inputNamepc = $("#lppcname").val();
		fangxiaoer.ajax("upguide", "{tel:'" + inputPhonepc + "',name:'" + inputNamepc + "',stype:'" + 4 + "'}", function (data) {
            if (data == "1") {
                $(".success_tc").show();
            }
            else {
                
            }
        });
	})
	//看房直通车提交
	$("#lppc_submit").click(function(){
		var phoneztc = $.trim($("#kftel").val());
		var nameztc = $("#kfname").val();
		var carztc = $("#kpaddress").html();
		var timeztc=$("#datetimepicker_mask").val();
		fangxiaoer.ajax("upguide", "{tel:'" + phoneztc + "',name:'" + nameztc + "',address:'" + carztc + "',time:'" + timeztc + "',stype:'" + 5 + "'}", function (data) {
            if (data == "1") {
                $(".success_tc").show();
            }
            else {
                
            }
        });

	})
	
	
})*/
