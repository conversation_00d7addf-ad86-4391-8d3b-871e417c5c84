$(function () {
    $(document).on("click", ".closer", function () {
        $(this).parent().hide();
        $(this).parents(".mask_bg").hide();
        $("#buildType").val("");
        $("#layId").val("");
        $("#buildId").val("");
        $("#unitId").val("");
        $("#roomId").val("");
    })
    $(document).on("click", ".rgingcloser", function () {
        $(".rging").hide();
    })
    $(document).on("click", ".closer1", function () {
        $(this).parents(".infotion").hide();
        $("#buildType").val("");
        $("#layId").val("");
        $("#buildId").val("");
        $("#unitId").val("");
        $("#roomId").val("");
        $(".infotion .infotionck,.infotion .dingd,.infotion .ontdown").remove();
        $(".tx,.yzm,.dingd,.xffs,.qs,.cg").hide();
        $(".infotionck").css("margin-top", "10%");
    })
    $(document).on("click", ".layouty_list ul li", function () {
        var layId = $(this).attr("data_id");
        $("#layId").val(layId);
        $(this).addClass("hovtt").siblings().removeClass("hovtt");
    })
    $(document).on("click", ".drop_top .drop_sk", function () {
        if ($(this).hasClass("hidety")) {
            $(this).removeClass("hidety");
            $(this).siblings("ul").show();
        } else {
            $(this).addClass("hidety");
            $(this).siblings("ul").hide();
        }
    })
    $(document).on("click", ".drop_left .drop_sk", function () {
        $(".drop_rig ul").hide().prev().addClass("hidety");
    })
    $(document).on("click", ".drop_rig .drop_sk", function () {
        $(".drop_left ul").hide().prev().addClass("hidety");
    })
    $(document).on("click", ".shops_left .shops_sk", function () {
        $(".shops_rig ul").hide().prev().addClass("hidety");
    })
    $(document).on("click", ".shops_rig .shops_sk", function () {
        $(".shops_left ul").hide().prev().addClass("hidety");
    })
    $(document).on("click", ".Housing .drop_left ul li", function () {
        var text = $(this).html();
        if (text == $(".Housing .drop_left .drop_sk span").html()) {
            que()
        } else {
            $("#unitId").val("");
            que()
        }
        $(".Housing .drop_left .drop_sk").html("<span>" + text + "</span>")
        $(this).parent().hide();
        $(".Housing .drop_sk").addClass("hidety");
        var buildId = $(this).attr("data_id");
        $("#buildId").val(buildId);
        chooseUnit()
    })
    $(document).on("click", ".Housing .drop_rig ul li", function () {
        var text = $(this).html();
        $(".Housing .drop_rig .drop_sk").html("<span>" + text + "</span>")
        $(this).parent().hide();
        $(".Housing .drop_sk").addClass("hidety");
        var unit = $(this).attr("data_id");
        $("#unitId").val(unit);
        que()
        choRoom()
    })
    $(document).on("click", ".shops_top .shops_sk", function () {
        if ($(this).hasClass("hidety")) {
            $(this).removeClass("hidety");
            $(this).siblings("ul").show();
        } else {
            $(this).addClass("hidety");
            $(this).siblings("ul").hide();
        }
    })
    $(document).on("click", ".shops_left ul li", function () {
        var buildId = $(this).attr("data_id");
        $("#buildId").val(buildId);
        var text = $(this).html();
        if (text == $(".shops .shops_left .shops_sk span").html()) {
            shops_default()
        } else {
            $("#layId").val("");
            shops_default()
        }
        $(".shops_left .shops_sk").html("<span>" + text + "</span>")
        $(this).parent().hide();
        $(".shops_sk").addClass("hidety");
    })
    $(document).on("click", ".shops_rig ul li", function () {
        var text = $(this).html();
        $(".shops_rig .shops_sk").html("<span>" + text + "</span>")
        $(this).parent().hide();
        $(".shops_sk").addClass("hidety");
        $(".ovhie .shshxdils").eq($(this).index()).show().siblings().hide();
    })
    $(document).on("click", ".lot_top .lotrig", function () {
        if ($(this).hasClass("hidety")) {
            $(this).removeClass("hidety");
            $(this).siblings("ul").show();
        } else {
            $(this).addClass("hidety");
            $(this).siblings("ul").hide();
        }
    })
    $(document).on("click", ".room_list ul .col1", function () {
        var roomId = $(this).attr("roomid");
        $("#roomId").val(roomId);
        $(this).addClass("col3").siblings().removeClass("col3");
    })
    $(document).on("click", ".lot_list ul .lot1", function () {
        var roomId = $(this).attr("roomid");
        $("#roomId").val(roomId);
        $(this).addClass("lot3").siblings().removeClass("lot3");
    })
    $(document).on("click", ".shops_list ul .shops1", function () {
        var roomId = $(this).attr("roomid");
        $("#roomId").val(roomId);
        $(this).addClass("shops3").siblings().removeClass("shops3");
    })
})
// 判断
$(document).on("click", ".drop_rig", function () {
    var left = $(".drop_left .drop_sk").html();
    if (left == '请选择楼号') {
        $(".drop_rig ul").hide();
        layer.msg("请先选择楼号!");
    }
})
$(document).on("click", ".shops_rig", function () {
    var left = $(".shops_left .shops_sk").html();
    if (left == '请选择楼号') {
        $(".shops_rig ul").hide();
        layer.msg("请选择楼号!");
    }
})
// console.log($("#unitId").val())
$(document).on("click", ".subscrip", function () {
    var sessionId = $("#sessionIdd").val();
    if (sessionId == null || sessionId == '' || sessionId == undefined) {
        $("#login,.modal-backdrop").show();
    } else {
        $(".rging,.rging_right").show();
    }
})
$(document).on("click", ".rging .rging_right ul li", function () {
    $(this).addClass("rgingi").siblings().removeClass("rgingi");
    $(".rging .quediuy").attr("data_id",$(this).attr("data_id"))
    $(".rging .quediuy").attr("projectId",$(this).attr("projectId"))
})
$(document).on("click", ".rging .quediuy", function () {
    if ($(".rging .rging_right ul li").hasClass("rgingi")) {
        var sessionId = $("#sessionIdd").val();
        $(".cdmobile").html($("#mobile").val());
        var projectId = $(this).attr("projectId");
        var buildType = $(this).attr("data_id");
        $("#projectId").val(projectId);
        $("#buildType").val(buildType);
        if (buildType == 6) {
            $(".shops_left .shops_sk ").html("请选择楼号");
            shops_default()
            chooseRoom()
        } else if (buildType == 10) {
            $(".parking_lot h1").html("请选择车库")
            $(".queey p").html("选择您想购买的车库")
            queey()
            chooseUnit()
        } else if (buildType == 11) {
            $(".parking_lot h1").html("请选择车位")
            $(".queey p").html("选择您想购买的车位")
            queey()
            chooseUnit()
        } else {
            que()
            getLayout(projectId, buildType)
        }
    } else {
        layer.msg("请选择类型");
    }
})
$(document).on("click", ".shops_rig ul li", function () {
    var layId = $(this).attr("layId");
    $("#layId").val(layId);
    shops_default()
    choRoom()
})
$(document).on("click", ".lot_top ul li", function () {
    var text = $(this).html();
    $(".lotrig").html("<span>" + text + "</span>")
    $(this).parent().hide();
    $(".lotrig").addClass("hidety");
    var unit = $(this).attr("data_id");
    $("#unitId").val(unit);
    queey()
    choRoom()
})

function que() {
    var unitid = $("#unitId").val();
    if (unitid == '') {
        $(".drop_rig .drop_sk").html("请选择单元")
        $(".que").show();
        $(".roomber").hide();
    } else {
        $(".roomber").show();
        $(".que").hide();
    }
}

function queey() {
    var unitid = $("#unitId").val();
    if (unitid == '') {
        $(".lot_top .lotrig").html("请选择区域")
        $(".queey").show();
        $(".huiio").hide();
    } else {
        $(".huiio").show();
        $(".queey").hide();
    }
}

function shops_default() {
    var layId = $("#layId").val();
    if (layId == '') {
        $(".shops_rig .shops_sk ").html("请选择户型");
        $(".shops_default").show();
        $(".shops_bottom").hide();
    } else {
        $(".shops_bottom").show();
        $(".shops_default").hide();
    }
}

// 选择户型
function getLayout(projectId, buildType) {
    $.ajax({
        type: 'POST',
        url: '/getLayOut',
        data: {
            projectId: projectId,
            buildType: buildType
        },
        success: function (obj) {
            // console.log(obj)
            $("#buildType").val(buildType);
            if (obj.status == 1) {
                var data = obj.content;
                if (data.length > 0) {
                    var li = '';
                    var li1 = '';
                    for (i = 0; i < data.length; i++) {
                        if (buildType == 6) {
                            li = li + '<li layId="' + data[i].layId + '">' + data[i].title + '</li>'
                            li1 = li1 + '<div class="shshxdils"><img src="' + data[i].imageUrl + '" alt=""><div class="shopsrig"><h1>' + data[i].buildArea + '㎡' + data[i].roomType + '室' + data[i].hallType + '厅' + data[i].guardType + '卫</h1><p class="p1">建筑面积：<span>' + data[i].buildArea + '</span>㎡</p><p class="p2">类型：<span class="span1">' + data[i].buildTypeName + '</span></p></div></div>'
                        } else {
                            li = li + '<li data_id="' + data[i].layId + '"><img class="layout_img" src="' + data[i].imageUrl + '" alt="">' +
                                '<div class="lay_rig"><h1>' + data[i].buildArea + '㎡' + data[i].roomType + '室' + data[i].hallType + '厅' + data[i].guardType + '卫</h1><p class="p1">建筑面积：<span>' + data[i].buildArea + '</span>㎡</p>' +
                                '<p class="p2">朝向：<span class="span1" style="margin-right:16px;">' + data[i].forwardTypeName + '</span>类型：<span class="span2">' + data[i].buildTypeName + '</span></p>' +
                                '</div>' +
                                '<div class="yest"></div>' +
                                '</li>'
                        }
                    }
                    if (buildType == 6) {
                        $(".shops_rig ul").html(li);
                        $(".ovhie").html(li1);
                    } else {
                        $(".layouty_list ul").html(li);
                        if ($(".layouty_list ul li").length > 6) {
                            $(".layouty_list").css("overflow-y", "scroll");
                        } else {
                            $(".layouty_list").css("overflow", "hidden");
                        }
                        $(".mask_bg").show();
                        $(".rging").hide();
                        $(".layouty").show();
                    }
                } else {
                    layer.msg("没有对应信息");
                }
            } else {
                layer.msg(obj.msg);
            }
        }
    })
}

// 选择楼号
function chooseRoom() {
    var projectId = $("#projectId").val();
    var buildType = $("#buildType").val();
    var layId = $(".layouty_list ul .hovtt").attr('data_id');
    $.ajax({
        type: 'POST',
        url: '/getBuildName',
        data: {
            projectId: projectId,
            buildType: buildType,
            layId: layId,
        },
        success: function (obj) {
            // console.log(obj)
            $("#layId").val(layId);
            if (obj.status == 1) {
                var data = obj.content;
                if (data.length > 0) {
                    var li = '';
                    for (i = 0; i < data.length; i++) {
                        li = li + '<li data_id="' + data[i].id + '">' + data[i].name + '</li>'
                    }
                    if (buildType == 6) {
                        $(".shops_left ul").html(li);
                        $(".mask_bg").show();
                        $(".shops").show();
                        $(".rging").hide();
                        getLayout(projectId, buildType)
                    } else if (buildType == 10) {
                        $(".lot_top ul").html(li);
                        $(".rging").hide();
                    } else if (buildType == 11) {
                        $(".lot_top ul").html(li);
                        $(".rging").hide();
                    } else {
                        $(".rging").hide();
                        $(".layouty").hide();
                        $(".Housing").html($("#Hous").html());
                        $(".Housing").show();
                        $(".Housing .drop_left ul").html(li);
                        $(".Housing .hxdils img").attr("src", $(".hovtt img").attr("src"));
                        $(".Housing .hxdils h1").html($(".hovtt h1").html());
                        $(".Housing .hxdils .p1 span").html($(".hovtt .p1 span").html());
                        $(".Housing .hxdils .p2").html($(".hovtt .p2").html());
                    }
                } else {
                    layer.msg("没有对应信息");
                }
            } else {
                layer.msg(obj.msg);
            }
        }
    })
}

// 选择单元
function chooseUnit() {
    var projectId = $("#projectId").val();
    var buildType = $("#buildType").val();
    var layId = $("#layId").val();
    var buildId = $("#buildId").val();
    $.ajax({
        type: 'POST',
        url: '/getUnit',
        data: {
            projectId: projectId,
            buildType: buildType,
            layId: layId,
            buildId: buildId
        },
        success: function (obj) {
            // console.log(obj)
            if (obj.status == 1) {
                var data = obj.content;
                if (data.length > 0) {
                    var li = '';
                    for (i = 0; i < data.length; i++) {
                        li = li + '<li data_id="' + data[i].id + '">' + data[i].name + '单元</li>';
                    }
                    if (buildType == 10) {
                        $(".lot_top ul").html(li);
                        $(".mask_bg").show();
                        $(".parking_lot").show();
                        $(".rging").hide();
                    } else if (buildType == 11) {
                        $(".lot_top ul").html(li);
                        $(".mask_bg").show();
                        $(".parking_lot").show();
                        $(".rging").hide();
                    } else {
                        $(".Housing .drop_rig ul").html(li);
                    }
                } else {
                    layer.msg("没有对应信息");
                }
            } else {
                layer.msg(obj.msg);
            }
        }
    })
}

// 选择房号
function choRoom() {
    var projectId = $("#projectId").val();
    var buildType = $("#buildType").val();
    var layId = $("#layId").val();
    var buildId = $("#buildId").val();
    var unit = $("#unitId").val();
    $.ajax({
        type: 'POST',
        url: '/getRoom',
        data: {
            projectId: projectId,
            buildType: buildType,
            layId: layId,
            buildId: buildId,
            unit: unit
        },
        success: function (obj) {
            // console.log(obj)
            $("#buildId").val(buildId);
            var data = obj.content;
            var li = '';
            for (i = 0; i < data.length; i++) {
                if (buildType == 6) {
                    li = li + '<li class="shops' + data[i].status + '" roomId="' + data[i].roomId + '"><i></i><p>' + data[i].door + '</p></li>';
                } else if (buildType == 10) {
                    li = li + '<li class="lot' + data[i].status + '" roomId="' + data[i].roomId + '"><i></i><p>' + data[i].unit + '区' + data[i].floor + '</p></li>';
                } else if (buildType == 11) {
                    li = li + '<li class="lot' + data[i].status + '" roomId="' + data[i].roomId + '"><i></i><p>' + data[i].unit + '区' + data[i].floor + '</p></li>';
                } else {
                    li = li + '<li class="col' + data[i].status + '" roomId="' + data[i].roomId + '"><i></i><p>' + data[i].unit + '-' + data[i].floor + '-' + data[i].door + '</p></li>';
                }
            }
            if (buildType == 6) {
                $(".shops_bottom").show();
                $(".shops_list ul").html(li);
            } else if (buildType == 10) {
                $(".lot_list ul").html(li);
            } else if (buildType == 11) {
                $(".lot_list ul").html(li);
            } else {
                $(".Housing .room_list ul").html(li);
            }
        }
    })
}

$(".laybuttom").click(function () {
    var layId = $("#layId").val();
    if (layId == '') {
        layer.msg("请先选择户型!");
    } else {
        chooseRoom()
    }
})
//确定
$(document).on("click", ".issi", function () {
    var buildType = $("#buildType").val();
    if (buildType == 6) {
        if ($("#buildId").val() == '') {
            layer.msg("请先选择楼号!");
        } else if ($("#roomId").val() == '') {
            layer.msg("请先选择房号!");
        } else {
            $(".infotion").html($("#inghu").html())
            $(".infotion .paybutt").after('<div class="ontdown">【在<span>15：00</span>内完成支付有效】</div>')
            $(".infotion").show();
            $(".tx").show();
            $(".mask_bg,.Housing,.layouty,.parking_lot,.shops").hide();
        }
    } else if (buildType == 10 || buildType == 11) {
        if ($("#unitId").val() == '') {
            layer.msg("请选择区域!");
        } else if ($("#roomId").val() == '') {
            layer.msg("请先选择房号!");
        } else {
            $(".infotion").html($("#inghu").html())
            $(".infotion .paybutt").after('<div class="ontdown">【在<span>15：00</span>内完成支付有效】</div>')
            $(".infotion").show();
            $(".tx").show();
            $(".mask_bg,.Housing,.layouty,.parking_lot,.shops").hide();
        }
    } else {
        if ($("#buildId").val() == '') {
            layer.msg("请先选择楼号!");
        } else if ($("#unitId").val() == '') {
            layer.msg("请选择单元!");
        } else if ($("#roomId").val() == '') {
            layer.msg("请先选择房号!");
        } else {
            $(".infotion").html($("#inghu").html())
            $(".infotion .paybutt").after('<div class="ontdown">【在<span>15：00</span>内完成支付有效】</div>')
            $(".infotion").show();
            $(".tx").show();
            $(".mask_bg,.Housing,.layouty,.parking_lot,.shops").hide();
        }
    }
})
// 提交订单
$(document).on("click", ".Vertion", function () {
    var memberName = $(".infotion .Customer_name").val();//客户姓名
    var mobile = $(".infotion .cdmobile").html();//客户电话
    var IDcard = $(".infotion .Customer_id").val();//客户身份证号码
    var address = $(".infotion .address").val();//通信地址
    var email = $(".infotion .e_mail").val();//客户邮箱
    if (memberName == '' || memberName == null || memberName == undefined) {
        layer.msg("请先填写姓名");
        return false;
    }
    if (!(/^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/).test(memberName)) {
        layer.msg("请填写正确姓名");
        return false;
    }
    if (IDcard == '' || IDcard == null || IDcard == undefined) {
        layer.msg("请先填写身份证号码");
        return false;
    }
    if (IDcard == '' || IDcard == null || IDcard == undefined) {
        layer.msg("请先填写身份证号码");
        return false;
    }
    if (IDcard.length != 15 && IDcard.length != 18 && !(/^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/).test(IDcard)) {
        layer.msg("请填写正确身份证号码");
        return false;
    }
    if (email != '' && email != null && email != undefined && !(/^(\w)+(\.\w+)*@(\w)+((\.\w+)+)$/.test(email))) {
        layer.msg("请填写正确邮箱地址");
        return false;
    }
    sms(mobile)
})

$(document).on("click", ".thq1", function () {
    var mobile = $(".infotion .cdmobile").html();
    sms(mobile)
})

function sms(mobile) {
    $.ajax({
        type: "POST",
        data: {action: "Send", mobile: mobile},
        url: "/sendSmsCode",
        async: false,
        success: function (data) {
            // console.log(data)
            if (data.status == 1) {
                $(".yzm h1 span").html(mobile.substring(0, 3) + '****' + mobile.substring(mobile.length - 4));
                $(".infotionck").css("margin-top", "15%");
                bindTime()
                $(".tx").hide();
                $(".yzm").show();
            } else {
                layer.msg(data.msg);
            }
        }
    });
}

function bindTime() {
    wait = 60;
    time();

    function time() {
        if (wait == 0) {
            $(".thq2").hide();
            $(".thq1").show().html("重新获取");
            wait = 60;
        } else {
            $(".thq2").show().html("在" + wait + "秒后重发");
            $(".thq1").hide();
            wait--;
            setTimeout(function () {
                    time();
                },
                1000);
        }
    }
}

$(document).on("click", ".yzmsion", function () {
    var smsCode = $(".yzm .tbor input").val();//验证码
    if (smsCode != '') {
        var projectId = $("#projectId").val();//项目id
        var buildType = $("#buildType").val();//建筑类型
        var roomId = $("#roomId").val();//房号
        var memberName = $(".infotion .Customer_name").val();//客户姓名
        var mobile = $(".infotion .cdmobile").html();//客户电话
        var IDCard = $(".infotion .Customer_id").val();//客户身份证号码
        var address = $(".infotion .address").val();//通信地址
        var email = $(".infotion .e_mail").val();//客户邮箱
        var sessionId = $("#sessionIdd").val();
        $.ajax({
            type: "post",
            data: {
                projectId: projectId,
                buildType: buildType,
                whetherAgent: 0,
                memberName: memberName,
                IDcard: IDCard,
                mobile: mobile,
                email: email,
                address: address,
                roomId: roomId,
                smsCode: smsCode,
                sessionId: sessionId
            },
            url: "/createOrder",
            success: function (data) {
                // console.log(data)
                var orderid = data.content;
                var orderid = orderid;
                if (data.status == 1) {
                    $(".infotionck").css("margin-top", "10%");
                    order(orderid)
                    $(".infotion .pr2").addClass("pro1")
                    $(".dingd").show();
                    $(".yzm").hide();
                } else {
                    layer.msg(data.msg);
                }
            }
        });
    } else {
        layer.msg("请填写短信验证码");
    }
})

function order(orderid) {
    $.ajax({
        type: "POST",
        data: {orderId: orderid},
        url: "/getOrderDetail",
        async: false,
        success: function (data) {
            // console.log(data)
            var li = ''
            var li1 = ''
            if (data.status == 1) {
                var data = data.content;
                if (data.projectName.length > 10) {
                    var projectName = '<span style="width:66%;display:block;float:left;line-height:24px;margin-top:-5px;">' + data.projectName + '</span>'
                } else {
                    var projectName = '<span style="width:66%;display:block;float:left;">' + data.projectName + '</span>'
                }

                if (data.buildType == 10 || data.buildType == 11) {
                    var area = ''
                    var area1 = '<div style="width:100%;float:left;">备注：<span>' + data.projectName + '，' + data.showHouse + '</span></div>'
                } else {
                    var area = '<div style="width:50%;float:right;">建筑面积：<span>' + data.area + '㎡</span></div>'
                    var area1 = '<div style="width:100%;float:left;">备注：<span>' + data.projectName + '，' + data.showHouse + '，' + data.area + '㎡</span></div>'
                }

                li += '<div>\n' +
                    '<div class="detailed">\n' +
                    '<div style="width:50%;float:left;">订单编号：<span>' + data.orderId + '</span></div>\n' +
                    '<div style="width:50%;float:right;">意向房源：<span>' + data.showHouse + '</span></div>\n' +
                    '<div style="width:50%;float:left;"><span style="color:#999999;width:33%;display:block;float:left;">楼盘名称：</span>' + projectName + '</div>\n' +
                    '' + area + '\n' +
                    '<div style="width:50%;float:left;">款项名称：<span>诚意金</span></div>\n' +
                    '</div>\n' +
                    '<div class="detailed">\n' +
                    '<div style="width:43%;float:left;">购房者姓名：<span>' + data.memberName + '</span></div>\n' +
                    '<div style="width:57%;float:right;">电子邮箱：<span>' + data.email + '</span></div>\n' +
                    '<div style="width:43%;float:left;">电话号：<span>' + data.mobile + '</span></div>\n' +
                    '<div style="width:57%;float:right;">证件号码：<span>' + data.IDcard + '</span></div>\n' +
                    '<div style="width:100%;float:left;">地址：<span>' + data.address + '</span></div>\n' +
                    '</div>\n' +
                    '<div class="detailed">\n' +
                    '<div style="width:43%;">款项金额：<b>' + data.payMoney + '</b><span>元</span></div>\n' +
                    '<div style="width:50%;">订单状态：<s class="shixiao">' + data.orderStatusValue + '</s></div>\n' +
                    '</div>\n' +
                    '<div class="detailed" style="margin-bottom:22px;">\n' +
                    '<div style="width:100%;">专属置业顾问：<span><i style="margin-right:17px;">' + data.agentName + '</i><i>' + data.agentMobile + '</i></span>\n' +
                    '</div>\n' +
                    '<div style="width:100%;">订单产生时间：<span><i\n' +
                    '>' + data.createTime.substring(0, 16).replace(/-/g, ".") + '</i></span></div>\n' +
                    '</div>\n' +
                    '</div>'
                $(".winjo").html(li);
                li1 += '<h1>收银台<span>（我的账户：' + data.memberName + '）</span></h1>\n' +
                    '<div style="float:left;margin-bottom:14px;">订单编号：<span>' + data.orderId + '</span></div>\n' +
                    '<div style="float:right;margin-bottom:14px;">购买时间：<span>' + data.createTime.substring(0, 16).replace(/-/g, ".") + '</span></div>\n' +
                    '' + area1 + ''
                $(".infotion .xffs .Cashier").html(li1);
                $("#orderId").val(data.orderId);
                $(".infotion .xffs .shiert div b").html("￥" + data.payMoney);
                var createTime = data.createTime;
                var status = data.orderStatus;
                fktc(createTime, status)
                var terval = setInterval(function () {
                    fktc(createTime, status)
                }, 1000)
                $(document).on("click", ".closer1", function () {
                    window.clearInterval(terval);
                })
                var payMoney = data.payMoney;
                var id = data.id;
                var sessionId = $("#sessionIdd").val();
                var body = data.projectName + '-' + data.showHouse;
                // console.log(body)
                paybutt(payMoney, id, sessionId, body)
            }
        }
    });
}

function fktc(createTime, status) {
    var new_date = new Date(); //新建一个日期对象，默认现在的时间
    var starttime = new Date(createTime.replace(/-/g, "/"))
    var difftime = (new_date - starttime) / 1000;
    var s = 900 - difftime;
    var seconds = parseInt(s % 60);
    var minutes = parseInt(s % 3600 / 60);
    if ((status == 1 || status == 2) && difftime < 900) {
        if (minutes > 10 && seconds < 10) {
            $(".infotion .ontdown span").html(minutes + ':0' + seconds);
        } else if (minutes < 10 && seconds > 10) {
            $(".infotion .ontdown span").html('0' + minutes + ':' + seconds);
        } else if (minutes < 10 && seconds < 10) {
            $(".infotion .ontdown span").html('0' + minutes + ':0' + seconds);
        } else if (minutes == 10 && seconds < 10) {
            $(".infotion .ontdown span").html(minutes + ':0' + seconds);
        } else if (minutes < 10 && seconds == 10) {
            $(".infotion .ontdown span").html('0' + minutes + ':' + seconds);
        } else {
            $(".infotion .ontdown span").html(minutes + ':' + seconds);
        }
        if (s <= 0) {
            $(".infotion .dingd .paybutt").hide();
            $(".infotion .dingd .ontdown").hide();
            $(".infotion .dingd .shixiao").html("已失效");
        }
        if ($(".infotion .ontdown span").html() == "00:00" || $(".infotion .ontdown span").html() == "00:01") {
            $(".infotion .dingd .paybutt").hide();
            $(".infotion .dingd .ontdown").hide();
            $(".infotion .dingd .shixiao").html("已失效");
        }
    }
}

// 支付
function paybutt(payMoney, id, sessionId, body) {
    $(document).on("click", ".paybutt", function () {
        $.ajax({
            type: "POST",
            data: {
                payMoney: payMoney,
                id: id,
                sessionId: sessionId,
                body: body
            },
            url: "/orderPay",
            async: false,
            success: function (data) {
                // console.log(data)
                if (data.status == 1) {
                    var date = new Date();
                    var pay_time = 120;
                    date.setTime(date.getTime() + 120 * 1000);//120秒
                    QR_data = data;
                    wxPayCode = data.content.wxPayCode;//微信
                    aliPayCode = data.content.aliPayCode;//支付宝
                    orderId = data.content.orderId;//订单id
                    $.cookie(sessionId + "_wxPayCode", wxPayCode, {expires: date, path: '/'});
                    $.cookie(sessionId + "_aliPayCode", aliPayCode, {expires: date, path: '/'});
                    $.cookie(sessionId + "_orderId", orderId, {expires: date, path: '/'});
                    parseQR(wxPayCode, aliPayCode);
                    pay_online_timer(orderId, pay_time);
                    if (data.msg != null && "error" == data.msg) {
                        $.removeCookie(sessionId + "_wxPayCode");
                        $.removeCookie(sessionId + "_aliPayCode");
                        $.removeCookie(sessionId + "_orderId");
                        goParticipate(e);
                    }
                    $(".xffs").show();
                    $(".dingd").hide();
                } else {
                    layer.msg(data.msg);
                }
            }
        });
    })
}

var parseQR = function (wxPayCode, aliPayCode) {
    $('#wxPayCode').empty();
    $('#aliPayCode').empty();
    $('#wxPayCode').qrcode(
        {
            width: 120,
            height: 120,
            text: wxPayCode
        });
    $('#aliPayCode').qrcode(
        {
            width: 120,
            height: 120,
            text: aliPayCode
        });
}
//二维码窗口时间
var pay_online_timer = function (orderId, pay_time) {
    pay_success(orderId);
    if (pay_time == 0) {
        $(".infotion").hide();//倒计时结束，所有窗口均关闭
        $(".xffs").hide();
        return;
    }
    pay_time--;
    $(".infotion .xffs .shiert p b").html(pay_time);
    var sdky = setTimeout(function () {
        if ($("#djs").val() == 1) {
            clearTimeout(sdky);
        } else {
            if ($(".infotion").is(':hidden')) {
                return;
            } else {
                pay_online_timer(orderId, pay_time);
            }
        }
    }, 1000);
}
//是否支付成功心跳检测
var pay_success = function (orderId) {
    $.ajax({
        type: "POST",
        data: {orderId: orderId},
        url: "/payForSuccess",
        async: false,
        success: function (data) {
            // console.log(data);
            if (data.content.status == 1) {//支付成功
                $("#djs").val("1");
                makeQRcode($("#orderId").val(), $("#sessionIdd").val());
                $(".infotion .pr3").addClass("pro1");
                $(".infotion .xffs").hide();
                $(".infotion .qs").show();
                Testing($("#orderId").val());
            }
        }
    });
}

var makeQRcode = function (orderId, sessionId) {
    var elText = "https://m.fangxiaoer.com/signContract/" + orderId + "?sessionId=" + sessionId;
    $('#jump').empty();
    $('#jump').qrcode(
        {
            width: 160,
            height: 160,
            text: elText
        });
}

function Testing(orderId) {
    var Tting = setInterval(function () {
        $.ajax({
            type: "POST",
            data: {orderId: orderId},
            url: "/checkIsSign",
            async: false,
            success: function (data) {
                // console.log(data);
                if (data.status == 1) {
                    if (data.content.orderStatus == 4) {
                        $(".infotion .pr4").addClass("proo1");
                        $(".infotion .cg .a2").attr("href", "https://sy.fangxiaoer.com/contactInfo/" + orderId)
                        $(".infotion .qs").hide();
                        $(".infotion .cg").show();
                        window.clearInterval(Tting);
                    }
                } else {
                    layer.msg(data.msg);
                }
            }
        });
    }, 1000)
}