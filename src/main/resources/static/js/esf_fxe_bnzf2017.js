/**
 * Created by Administrator on 2017/9/6.
 */
$(function () {
    //发送验证码
    $(".fxe_ReSendValidateCoad").click(function () {
        if ($("#phone0").val() == "") {
            alert("请填写电话");
        } else if (!confirm.mobile()) {
            alert(hint[5]);
        } else if (SendFunc.ajaxSendCode($(".fxe_mobile").val()).status == 1) {
            fxeTime.timeWait();
            $(".yzm_btn1").css("display", "inline-block");
        }
    });
    //发送验证码
    var SendFunc = {
        ajaxSendCode: function (mobile) {
            try {
                var r = 0;
                $.ajax({
                    type: "POST",
                    data: { action: "Send", mobile: mobile },
                    url: "/sendSmsCode",
                    async: false,
                    success: function (data) {
                        r = data;
                    }
                });
            } catch (e) {
                console.log(e.message);
            }
            return r;
        }
    };
    //点击选择
    $(".fxe_my_xl_list li").click(function () {
        $(".fxe_my_xl_list").hide();
        $(this).parent().prev().text($(this).text()).css("color", "#000");
    })
    //点击显示下拉列表
    $(".fxe_my_xl_txt").click(function () {
        $(".fxe_my_xl_txt").next().hide();
        $(this).next().show();
    })
    //滑动消失下拉列表
    $(".fxe_my_xl_list").mouseover(function () {
        $(".fxe_my_xl_list").hover(function () {
            $(this).show()
        }, function () {
            $(this).hide()
        })
    })
    $(".bnzf_ul").mouseleave(function () {
        $(".fxe_my_xl_list").hide();
    })
    //核对验证码
    function confirmYzm() {
        var phone = $(".fxe_mobile").val();
        var code = $(".fxe_messageCode").val();
        var t = false;
        $.ajax({
            type: "POST",
            async: false,
            data: { action: "CheckSend", mobile: phone, code: code },
            url: "/checkPasscode",
            success: function (data) {
                if (data == "1") {
                    t= true;
                }
                else {
                    t= false;
                }
            },
            error: function (error) {
                console.log(error);
                t= false;
            }
        });
        return t;
    }
    //帮你找房提交
    $("#new_submit").click(function () {
        if ($("#new_qy").html() == "请选择") {
            alert("请选择区域");
        } else if ($("#new_yusuan").html() == "请选择") {
            alert("请选择预算");
        } else if ($("#new_huxing").html() == "请选择") {
            alert("请选择户型");
        } else if ($("#phone0").val() == "") {
            alert("请填写电话");
        } else if (!confirm.mobile()) {
            alert(hint[5]);
        } else if (!confirmYzm()) {
            alert(hint[7]);
        } else {
            var tphone = $.trim($("#phone").val());//电话
            var gongqiu = $("#needs").val();//商铺供求类型
            var quyu = $("#region").val();//区域
            var houseType = $("#shoptype").val();//商铺类型
            var miaos = $.trim($("#describe").val());//描述
            var type = $("#type").val();//类型
            var code = $("#code").val();//验证码
            var yusuan = $("#new_yusuan").val();//二手房预算
            var huxing = $("#new_huxing").val();//二手房户型
            if (type =="1"){
                var params = {phone:tphone,code:code,budget:yusuan,region:quyu,housetype:huxing, area:huxing,italy:miaos,type:type}
                fangxiaoer.ajax("upguide", params, function (data) {
                    if (data.status == "1") {
                        //        //重置
                        $(".success_tc").show();
                        $("#phone").val("");
                        $("#new_yusuan").val("35万以下");
                        $("#new_qy").val("沈河区");
                        $("#new_huxing").val("一居");
                        $("#describe").val("");
                        $("#yzm0").val("");
                        $("#code").val("");
                        $("#yzm0").parents("ul").find(".input_desc").show();
                        $("span.yzm_btn").hide();
                        $(".fxe_validateCode").hide();
                        $(".fxe_ReSendValidateCoad").show().html("重新获取");
                        wait = 0;
                        $("#con_bnzfqh_1").find(".errorBox").html("");
                        $("#con_bnzfqh_1").find(".my_xl_input").removeClass("error");
                        $("#con_bnzfqh_1").find(".ljz").hide();
                        $(".my_xl_input").val("");
                        $("#yzm").val("");
                        alert("亲，您的需求已提交成功，工作人员会及时跟您联系，请耐心等待！")
                        return false;
                    }
                });
            }else if(type ==2){
                var params = {phone:tphone,code:code,budget:yusuan,region:quyu,housetype:huxing, area:huxing,italy:miaos,type:type}
                fangxiaoer.ajax("upguide", params, function (data) {
                    if (data.status == "1") {
                        //        //重置
                        $(".success_tc").show();
                        $("#phone").val("");
                        $("#new_yusuan").val("35万以下");
                        $("#new_qy").val("沈河区");
                        $("#new_huxing").val("一居");
                        $("#describe").val("");
                        $("#code").val("");
                        $("#yzm0").parents("ul").find(".input_desc").show();
                        $("span.yzm_btn").hide();
                        $(".fxe_validateCode").hide();
                        $(".fxe_ReSendValidateCoad").show().html("重新获取");
                        wait = 0;
                        $("#con_bnzfqh_1").find(".errorBox").html("");
                        $("#con_bnzfqh_1").find(".my_xl_input").removeClass("error");
                        $("#con_bnzfqh_1").find(".ljz").hide();
                        $(".my_xl_input").val("");
                        $("#yzm").val("");
                        alert("亲，您的需求已提交成功，客服人员会及时跟您联系，请耐心等待！")
                        return false;
                    }
                });
            }else if(type ==3){
                var params = {phone:tphone,code:code,budget:yusuan,region:quyu,housetype:huxing, area:"无",italy:miaos,type:3}
                fangxiaoer.ajax("upguide", params, function (data) {
                    if (data.status == "1") {
                        //        //重置
                        $(".success_tc").show();
                        $("#phone").val("");
                        $("#new_yusuan").val("500元以下");
                        $("#region").val("沈河区");
                        $("#new_huxing").val("整套求租");
                        $("#describe").val("");
                        $("#code").val("");
                        $("#yzm0").parents("ul").find(".input_desc").show();
                        $("span.yzm_btn").hide();
                        $(".fxe_validateCode").hide();
                        $(".fxe_ReSendValidateCoad").show().html("重新获取");
                        wait = 0;
                        $("#con_bnzfqh_1").find(".errorBox").html("");
                        $("#con_bnzfqh_1").find(".my_xl_input").removeClass("error");
                        $("#con_bnzfqh_1").find(".ljz").hide();
                        $(".my_xl_input").val("");
                        $("#yzm").val("");
                        alert("亲，您的需求已提交成功，客服人员会及时跟您联系，请耐心等待！")
                        return false;
                    }
                });
            }

            else{
                var params = {phone:tphone,code:code,budget:"无",region:quyu,housetype:gongqiu, area:houseType,italy:miaos,type:8}
                fangxiaoer.ajax("upguide", params, function (data) {
                    if (data.status == "1") {
                        //        //重置
                        $(".success_tc").show();
                        $("#phone").val("");
                        // $("#new_yusuan").val("35万以下");
                        $("#region").val("沈河区");
                        // $("#new_huxing").val("一居");
                        $("#describe").val("");
                        $("#shoptype").val("住宅底商");
                        $("#needs").val("求购");
                        $("#code").val("");
                        $("#code").parents("ul").find(".input_desc").show();
                        $("span.yzm_btn").hide();
                        $(".fxe_validateCode").hide();
                        $(".fxe_ReSendValidateCoad").show().html("重新获取");
                        wait = 0;
                        $("#con_bnzfqh_1").find(".errorBox").html("");
                        $("#con_bnzfqh_1").find(".my_xl_input").removeClass("error");
                        $("#con_bnzfqh_1").find(".ljz").hide();
                        $(".my_xl_input").val("");
                        $("#yzm").val("");
                        alert("亲，您的需求已提交成功，客服人员会及时跟您联系，请耐心等待！")
                        return false;
                    }
                });
            }
        }
        return false;
    })
    //帮你找房提交
    $("#map_submit").click(function () {
        if ($("#region").val() == "请选择区域") {
            alert("请选择区域");
        } else if ($("#new_yusuan").val() == "请选择价格") {
            alert("请选择预算");
        } else if ($("#new_huxing").val() == "请选择户型") {
            alert("请选择户型");
        } else if ($("#phone0").val() == "") {
            alert("请填写电话");
        } else if (!confirm.mobile()) {
            alert(hint[5]);
        } else if (!confirmYzm()) {
            alert(hint[7]);
        } else {
            var tphone = $.trim($("#phone").val());//电话
            var gongqiu = $("#needs").val();//商铺供求类型
            var quyu = $("#region").val();//区域
            var houseType = $("#shoptype").val();//商铺类型
            var miaos = $.trim($("#describe").val());//描述
            var type = $("#type").val();//类型
            var code = $("#code").val();//验证码
            var yusuan = $("#new_yusuan").val();//二手房预算
            var huxing = $("#new_huxing").val();//二手房户型
            var unSelected = "#999";
            if (type =="1"){
                var params = {phone:tphone,code:code,budget:yusuan,region:quyu,housetype:huxing, area:huxing,italy:miaos,type:type}
                fangxiaoer.ajax("upguide", params, function (data) {
                    if (data.status == "1") {
                        //        //重置
                        $(".success_tc").show();
                        $("#phone").val("");
                        $("#new_yusuan").val("请选择价格");
                        $("#region").val("请选择区域");
                        $("#new_huxing").val("请选择户型");
                        $("#describe").val("");
                        $("#yzm0").val("");
                        $("#code").val("");
                        $("#yzm0").parents("ul").find(".input_desc").show();
                        $("span.yzm_btn").hide();
                        $(".fxe_validateCode").hide();
                        $(".fxe_ReSendValidateCoad").show().html("重新获取");
                        wait = 0;
                        $(".zsfw ul li div select").css("color", unSelected);
                        $("#con_bnzfqh_1").find(".errorBox").html("");
                        $("#con_bnzfqh_1").find(".my_xl_input").removeClass("error");
                        $("#con_bnzfqh_1").find(".ljz").hide();
                        $(".my_xl_input").val("");
                        $("#yzm").val("");
                        alert("亲，您的需求已提交成功，工作人员会及时跟您联系，请耐心等待！")
                        return false;
                    }
                });
            } else{
                var params = {phone:tphone,code:code,budget:"无",region:quyu,housetype:gongqiu, area:houseType,italy:miaos,type:8}
                fangxiaoer.ajax("upguide", params, function (data) {
                    if (data.status == "1") {
                        //        //重置
                        $(".success_tc").show();
                        $("#phone").val("");
                        // $("#new_yusuan").val("35万以下");
                        $("#region").val("沈河区");
                        // $("#new_huxing").val("一居");
                        $("#describe").val("");
                        $("#shoptype").val("住宅底商");
                        $("#needs").val("求购");
                        $("#code").val("");
                        $("#code").parents("ul").find(".input_desc").show();
                        $("span.yzm_btn").hide();
                        $(".fxe_validateCode").hide();
                        $(".fxe_ReSendValidateCoad").show().html("重新获取");
                        wait = 0;
                        $("#con_bnzfqh_1").find(".errorBox").html("");
                        $("#con_bnzfqh_1").find(".my_xl_input").removeClass("error");
                        $("#con_bnzfqh_1").find(".ljz").hide();
                        $(".my_xl_input").val("");
                        $("#yzm").val("");
                        alert("亲，您的需求已提交成功，客服人员会及时跟您联系，请耐心等待！")
                        return false;
                    }
                });
            }
        }
        return false;
    })
})
