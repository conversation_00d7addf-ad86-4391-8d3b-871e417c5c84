/**
 * Created by Administrator on 2019.08.17
 */
var timer=null;
var polyline = new Array();

//1号线
var path1 = [
    [123.511613, 41.814227],
    [123.500783, 41.815326],
    [123.498556, 41.815346],
    [123.49485, 41.81503],
    [123.492506, 41.814822],
    [123.488315, 41.813629],
    [123.485647, 41.812594],
    [123.481811, 41.81053],
    [123.480109, 41.809845],
    [123.477639, 41.809334],
    [123.47065, 41.807657],
    [123.468112, 41.807627],
    [123.46663, 41.807603],
    [123.466034, 41.807457],
    [123.463618, 41.806621],
    [123.461327, 41.805828],
    [123.45812, 41.804577],
    [123.45569, 41.803636],
    [123.454828, 41.803351],
    [123.452708, 41.802541],
    [123.451459, 41.802043],
    [123.450058, 41.801489],
    [123.44863, 41.800927],
    [123.443545, 41.798911],
    [123.44081, 41.798262],
    [123.430785, 41.795946],
    [123.428934, 41.795516],
    [123.421564, 41.793822],
    [123.419008, 41.793439],
    [123.417993, 41.793314],
    [123.416426, 41.793923],
    [123.41228, 41.795798],
    [123.404164, 41.799344],
    [123.401958, 41.800272],
    [123.395567, 41.80291],
    [123.391843, 41.804463],
    [123.39086, 41.804772],
    [123.390442, 41.804806],
    [123.38993, 41.804803],
    [123.389283, 41.804638],
    [123.388686, 41.804453],
    [123.388151, 41.80411],
    [123.386566, 41.80201],
    [123.38476, 41.799728],
    [123.383857, 41.798635],
    [123.383179, 41.798296],
    [123.382739, 41.798245],
    [123.382245, 41.798276],
    [123.381576, 41.798447],
    [123.377794, 41.800161],
    [123.376244, 41.800837],
    [123.363192, 41.806352],
    [123.361463, 41.807068],
    [123.360479, 41.807394],
    [123.35605, 41.808513],
    [123.350238, 41.809843],
    [123.348797, 41.81018],
    [123.345576, 41.810973],
    [123.345123, 41.811013],
    [123.343443, 41.811033],
    [123.339081, 41.810989],
    [123.337707, 41.810976],
    [123.334271, 41.810966],
    [123.332982, 41.810929],
    [123.332618, 41.810882],
    [123.332007, 41.810737],
    [123.331513, 41.810532],
    [123.329281, 41.809481],
    [123.327969, 41.808876],
    [123.32721, 41.808519],
    [123.326114, 41.808032],
    [123.325292, 41.807817],
    [123.318667, 41.807421],
    [123.316871, 41.807306],
    [123.31533, 41.807165],
    [123.314477, 41.80697],
    [123.314001, 41.806799],
    [123.313354, 41.806483],
    [123.31295, 41.806251],
    [123.312514, 41.805902],
    [123.31233, 41.805431],
    [123.312254, 41.804937],
    [123.312366, 41.80288],
    [123.312424, 41.800376],
    [123.312424, 41.799331],
    [123.312375, 41.796447],
    [123.312285, 41.790494],
    [123.312267, 41.789166],
    [123.312276, 41.788333],
    [123.31206, 41.7872],
    [123.31175, 41.785892],
    [123.311405, 41.785257],
    [123.310452, 41.784473],
    [123.306123, 41.781549],
    [123.302547, 41.778893],
    [123.301402, 41.778035],
    [123.299861, 41.776909],
    [123.296632, 41.774602],
    [123.295832, 41.774068],
    [123.295235, 41.773846],
    [123.284586, 41.771973],
    [123.282973, 41.771684],
    [123.280669, 41.771257],
    [123.279874, 41.771186],
    [123.278042, 41.771237],
    [123.266709, 41.771499],
    [123.26494, 41.771536],
    [123.24966, 41.771785],
    [123.247908, 41.771798],
    [123.237991, 41.771748],
    [123.235969, 41.771734]
]
//2号线
var path2 =
    [
        [123.412009,41.963001],
        [123.415139,41.952467],
        [123.416082,41.949536],
        [123.415647,41.947829],
        [123.41226,41.945278],
        [123.404467,41.942531],
        [123.401853,41.941555],
        [123.401085,41.941099],
        [123.399881,41.939007],
        [123.400739,41.935348],
        [123.401732,41.932685],
        [123.403556,41.929975],
        [123.409044,41.924675],
        [123.409956, 41.923313],
        [123.410162, 41.922969],
        [123.415525, 41.913767],
        [123.416378, 41.912213],
        [123.419284, 41.906543],
        [123.419338, 41.906426],
        [123.420026, 41.904516],
        [123.42087, 41.901764],
        [123.421504, 41.899435],
        [123.42176, 41.898515],
        [123.421863, 41.897223],
        [123.422371, 41.890937],
        [123.423184, 41.881552],
        [123.423776, 41.874855],
        [123.423929, 41.873076],
        [123.423992, 41.872341],
        [123.42428, 41.8675084],
        [123.424765, 41.861715],
        [123.42493, 41.860382],
        [123.425205, 41.858578],
        [123.425575, 41.853928],
        [123.425708, 41.852302],
        [123.426054, 41.848195],
        [123.426271, 41.845679],
        [123.426340, 41.845226],
        [123.426394, 41.845149],
        [123.426409, 41.845122],
        [123.426495, 41.845038],
        [123.426727, 41.844927],
        [123.42698, 41.844869],
        [123.427127, 41.844858],
        [123.429939, 41.844961],
        [123.431964, 41.845041],
        [123.432373, 41.845065],
        [123.433352, 41.845051],
        [123.433887, 41.844981],
        [123.434466, 41.844736],
        [123.434691, 41.844635],
        [123.435005, 41.84443],
        [123.435288, 41.844212],
        [123.4354, 41.84394],
        [123.43546, 41.842233],
        [123.435495, 41.840299],
        [123.435515, 41.840211],
        [123.435993, 41.838582],
        [123.435998, 41.838579],
        [123.436100, 41.838179],
        [123.43616, 41.837323],
        [123.436220, 41.836899],
        [123.43632, 41.835945],
        [123.43632, 41.835945],
        [123.436505, 41.834662],
        [123.436600, 41.833775],
        [123.436715, 41.832308],
        [123.436755, 41.832072],
        [123.436775, 41.831817],
        [123.436798, 41.831602],
        [123.436784, 41.831525],
        [123.436784, 41.831417],
        [123.436743, 41.831286],
        [123.436573, 41.830812],
        [123.436210, 41.829916],
        [123.435972, 41.829281],
        [123.435978, 41.827356],
        [123.435958, 41.826868],
        [123.43603, 41.825894],
        [123.436208, 41.825488],
        [123.436708, 41.825061],
        [123.43748, 41.824759],
        [123.437803, 41.824644],
        [123.438967, 41.824237],
        [123.439645, 41.824033],
        [123.441527, 41.823351],
        [123.443786, 41.822605],
        [123.444213, 41.822467],
        [123.444599, 41.822264],
        [123.44605, 41.820156],
        [123.446135, 41.819645],
        [123.446105, 41.819215],
        [123.445749, 41.818615],
        [123.444181, 41.816678],
        [123.443553, 41.815885],
        [123.44255, 41.814491],
        [123.442331, 41.814171],
        [123.442158, 41.813876],
        [123.44204, 41.81362],
        [123.441955, 41.813318],
        [123.441868, 41.812474],
        [123.441831, 41.812357],
        [123.441730, 41.812199],
        [123.441092, 41.811197],
        [123.439800, 41.809117],
        [123.43970, 41.808959],
        [123.439614, 41.808741],
        [123.439277, 41.80784],
        [123.439239, 41.807726],
        [123.438648, 41.806358],
        [123.438432, 41.805894],
        [123.438295, 41.805632],
        [123.438239, 41.805421],
        [123.438213, 41.805242],
        [123.438222, 41.804802],
        [123.438554, 41.802517],
        [123.438900, 41.799979],
        [123.439254, 41.7988],
        [123.439856, 41.797139],
        [123.440472, 41.795533],
        [123.441373, 41.793065],
        [123.442031, 41.791865],
        [123.442381, 41.791166],
        [123.442951, 41.789912],
        [123.442196, 41.782987],
        [123.441945, 41.780644],
        [123.441875, 41.779736],
        [123.441704, 41.77786],
        [123.441613, 41.776983],
        [123.441825, 41.772258],
        [123.441977, 41.769289],
        [123.442016, 41.768546],
        [123.442066, 41.768065],
        [123.442433, 41.766787],
        [123.442899, 41.765069],
        [123.442926, 41.765016],
        [123.443289, 41.763818],
        [123.443509, 41.763058],
        [123.443689, 41.762621],
        [123.443923, 41.762224],
        [123.444974, 41.760862],
        [123.4454, 41.760348],
        [123.445751, 41.759971],
        [123.447251, 41.758925],
        [123.450831, 41.756319],
        [123.451945, 41.755508],
        [123.453189, 41.754529],
        [123.454343, 41.753621],
        [123.456059, 41.752276],
        [123.457554, 41.751092],
        [123.45932, 41.749706],
        [123.460856, 41.748411],
        [123.461004, 41.748266],
        [123.4613, 41.747869],
        [123.461714, 41.747029],
        [123.461714, 41.747029],
        [123.461709, 41.747035],
        [123.46197, 41.746336],
        [123.462288, 41.745411],
        [123.462504, 41.744489],
        [123.462576, 41.743702],
        [123.462666, 41.742777],
        [123.462688, 41.742477],
        [123.462774, 41.741391],
        [123.462841, 41.740055],
        [123.462895, 41.738369],
        [123.462971, 41.736546],
        [123.463106, 41.735816],
        [123.463254, 41.735348],
        [123.463654, 41.734554],
        [123.464907, 41.732451],
        [123.46523, 41.731782],
        [123.465675, 41.73086],
        [123.466246, 41.729588],
        [123.467234, 41.72762],
        [123.468819, 41.725291],
        [123.469578, 41.724188],
        [123.47027, 41.723192],
        [123.470656, 41.722734],
        [123.472102, 41.720997],
        [123.473005, 41.719988],
        [123.473899, 41.718992],
        [123.47451, 41.718329],
        [123.475839, 41.71706],
        [123.476742, 41.716249],
        [123.478898, 41.715142],
        [123.48069, 41.714553],
        [123.484319, 41.713722],
        [123.484876, 41.713654],
        [123.486174, 41.713883],
        [123.486916, 41.713974],
        [123.487495, 41.713893],
        [123.488371, 41.713728],
        [123.489682, 41.713271],
        [123.495362,41.710949],
        [123.501489,41.706856],
        [123.501525,41.706654],
        [123.501327,41.706022],
        [123.50095,41.705658],
        [123.491068,41.701323],
        [123.487008,41.700313],
        [123.475761,41.694792],
        [123.471701,41.694469],
        [123.465503,41.694334],
        [123.465017,41.694078],
        [123.464766,41.693694],
        [123.464631,41.693169],
        [123.464649,41.692967],
        [123.464676,41.685546],
        [123.464676,41.685546],
        [123.464353,41.645884],
        [123.464568,41.643822],
        [123.46694,41.639994],
        [123.469419,41.638283],
        [123.480576,41.635978],
        [123.489829,41.634239],
        [123.491913,41.634306],
        [123.500811,41.639839],
    ]
//4号线
var path6 =
    [
        [123.486981,41.893538],//望花街
        [123.486348,41.887625],
        [123.485818,41.882358],
        [123.48633,41.879143],
        [123.486523,41.878646],
        [123.486963,41.878307],
        [123.487547,41.878099],
        [123.493503,41.87734],//文贸路
        [123.496257,41.876944],
        [123.496719,41.876813],
        [123.496966,41.876588],
        [123.497074,41.875967],
        [123.49711, 41.874665], //望花屯
        [123.494271, 41.864146],
        [123.489151, 41.854466], //观全路
        [123.483977, 41.84431], //南卡门路
        [123.482198, 41.840733],
        [123.481946, 41.839994],
        [123.481263, 41.837549],
        [123.481031, 41.835685], //北大营街
        [123.480778, 41.833867],
        [123.477149, 41.829755], //吉祥
        [123.468597, 41.827524],
        [123.459003, 41.826584], //小北关街
        [123.450739, 41.82567],
        [123.44287, 41.822848], //北站
        [123.430168, 41.818816],
        [123.428641, 41.81805],
        [123.42724, 41.816736], //皇寺路
        [123.426485, 41.815026],
        [123.425155, 41.812821],
        [123.423108, 41.810204], //市府大路
        [123.411933, 41.795846], //太原街
        [123.405896, 41.78794], //南五马路
        [123.39729, 41.776587],
        [123.39729, 41.776022],
        [123.397991, 41.773901], //砂阳路
        [123.3995, 41.770104],
        [123.403955, 41.762248],
        [123.407334, 41.754342], //长白北
        [123.409525, 41.748405], //长白岛
        [123.415976, 41.732957], //长白南
        [123.417861, 41.727237],
        [123.418059, 41.726066],
        [123.417987, 41.725056],
        [123.410011, 41.710586], //王士屯
        [123.406166, 41.704029], //上河村
        [123.404261, 41.700191],
        [123.403883, 41.699141],
        [123.404117, 41.698508],
        [123.404494, 41.698117],
        [123.409562, 41.69595], //红椿路
        [123.421991,41.69103],
        [123.422745,41.69002],
        [123.422565,41.687798],//城建学院
        [123.419951,41.681586],
        [123.41855,41.676009],//新沈阳南站
        [123.418478,41.673733],
        [123.420059,41.666741],
        [123.420059,41.666741],
        [123.420019,41.66374] //航天南路
    ]
//6号线
var path9 =
    [
        [123.480653,41.912893],
        [123.474061,41.899973],
        [123.473836,41.886576],
        [123.468878,41.875891],
        [123.463888,41.864923],
        [123.463367,41.857271],
        [123.463317,41.847337],
        [123.463214,41.839239],
        [123.465761,41.82464],
        [123.466165,41.807476],
        [123.466385,41.799185],
        [123.466336,41.794546],
        [123.464413,41.786677],
        [123.460326,41.777529],
        [123.456126,41.770492],
        [123.442450,41.766766],
        [123.429954,41.763309],
        [123.419296,41.750485],
        [123.408584,41.74816],
        [123.394076,41.743512],
        [123.382214,41.740679],
        [123.376855,41.735061],
        [123.371250,41.728547],
        [123.366228,41.716244],
        [123.361167,41.706756],
        [123.354312,41.69386],
        [123.345810,41.684968],
        [123.346574,41.671377],
        [123.352592,41.659606]
    ]
//9号线
var path4 =
    [
        [123.527303, 41.75484], //建筑大学
        [123.52477, 41.753754],
        [123.519991, 41.752516],
        [123.508169, 41.750498], //浑南大道
        [123.493023, 41.748301], //朗日街
        [123.487203, 41.747645],
        [123.480151, 41.749899],
        [123.473368, 41.75005], //奥体东
        [123.46827, 41.749949],
        [123.463059,41.749857],
        [123.461838,41.74973],
        [123.460185,41.74903], //奥体中心
        [123.459686,41.748774],
        [123.458002,41.747691],
        [123.45758,41.747207],
        [123.457378,41.746591],//法治文化
        [123.457786,41.744832],
        [123.45775,41.743998],
        [123.456488,41.742268],
        [123.456475,41.742265],
        [123.448072, 41.74012], //浑河堡
        [123.437283, 41.737523], //金阳大街
        [123.430276, 41.736042], //榆树台
        [123.415993, 41.73296], //长白 南
        [123.401001, 41.729784], //胜利南街
        [123.39632, 41.729771],
        [123.389475, 41.730551],
        [123.383394, 41.732261], //苏家屯西路
        [123.378875, 41.733364],
        [123.366506, 41.739205], //曹仲
        [123.364086,41.74065],
        [123.360708,41.745468],
        [123.360852,41.745225],
        [123.355282,41.755276],
        [123.352632,41.758269],
        [123.348985,41.762086],
        [123.34704,41.764124],
        [123.346825,41.764589],
        [123.346906,41.765043],
        [123.3474,41.765443],
        [123.350881,41.766979],
        [123.352803,41.767635],
        [123.354451,41.768049],
        [123.355592,41.768311],
        [123.356912, 41.768991], //吉利湖街
        [123.35816, 41.770242],
        [123.360298, 41.774237],
        [123.364853, 41.778406], //滑翔
        [123.367215, 41.78049],
        [123.367449, 41.781109],
        [123.367485, 41.781728],
        [123.367323, 41.782239],
        [123.366658, 41.783099],
        [123.366371, 41.785977],
        [123.367225, 41.787281], //沈辽路
        [123.372111, 41.793655], //兴华公园
        [123.377106, 41.800458], //铁西广场
        [123.385847, 41.811577], //北二路
        [123.389817, 41.816938],
        [123.390221, 41.819088], //北一路
        [123.390571, 41.8214],
        [123.393204, 41.827421], //皇姑屯
        [123.397228, 41.836316], //淮河街
        [123.396859, 41.843692],
        [123.395539, 41.846984] //怒江公园
    ]
//10号线
var path7 =
    [
        [123.354568, 41.870606], //丁香公园
        [123.355033, 41.867101],
        [123.355914, 41.865154],
        [123.362868, 41.857418], //白山路
        [123.366316, 41.853643],
        [123.37167, 41.845986],
        [123.371634, 41.845448],
        [123.370378, 41.842695], //向工街
        [123.368849, 41.839255],
        [123.368831, 41.838879],
        [123.369173, 41.838516],
        [123.36964, 41.838247],
        [123.381228, 41.835184], //塔湾街
        [123.382324, 41.834888],
        [123.382917, 41.834888],
        [123.383384, 41.834928],
        [123.385109, 41.835963],
        [123.386151, 41.836191],
        [123.397182, 41.836635], //淮河街
        [123.407602, 41.836796], //百鸟公园
        [123.412291, 41.837078],
        [123.418886, 41.837589], //长江街
        [123.436888, 41.838422], //中医药大学
        [123.450111, 41.839013], //松花江街
        [123.464842, 41.839645],
        [123.465884, 41.839524],
        [123.470106, 41.838368], //柳条湖
        [123.479215, 41.836191],
        [123.481085, 41.835681], //北大营街
        [123.482503, 41.834848],
        [123.486078, 41.830897],
        [123.488235, 41.827619], //东北大马路
        [123.492025, 41.821289],
        [123.494847, 41.815066], //滂江街
        [123.4956, 41.813305],
        [123.495708, 41.812741],
        [123.495654, 41.81227],
        [123.495367, 41.811343],
        [123.493157, 41.808332],
        [123.49208, 41.804582], //长安路
        [123.49021, 41.796891],
        [123.489781, 41.796246], //万泉公园
        [123.488288, 41.794135],
        [123.482772, 41.789873],
        [123.481461, 41.788017],
        [123.481479, 41.787358],
        [123.481874, 41.786484],
        [123.484534, 41.784252], //泉园一路
        [123.493877, 41.776426], //文化路东
        [123.500595, 41.770561],
        [123.501906, 41.76884],
        [123.502644, 41.767199], //长青桥
        [123.50825, 41.750894], //浑南大道
        [123.511591, 41.739861], //理工大学
        [123.513405, 41.734868],
        [123.519065, 41.727479] //张沙布
    ]





// 选中线路后，地铁线变宽
function setLineWeigth(currentNum) {
    if(currentNum==""){
        for(var i=1;i<polyline.length;i++){
            if(polyline[i]!= "" && polyline[i]!= null && polyline[i]!= undefined){
                polyline[i].setStrokeWeight(0);
            }
        }
    }else {
        for(var i=1;i<polyline.length;i++){
            if(polyline[i]!= "" && polyline[i]!= null && polyline[i]!= undefined){
                if(i !=currentNum){
                    polyline[i].setStrokeWeight(0);
                }else {
                    polyline[i].setStrokeWeight(8);
                }
            }
        }
    }

}


Vue.config.productionTip = false;
var baiduMap = new Vue({
    el: '#map',
    data: {
        houseType:type,//地图类型 1  新房            2  二手房            3 租房
        map: {},
        mapZoom:12,//初始化级别
        maxRegion:13,//地铁模式 站点层级
        maxPlat:16,//地铁、区域模式 小区层级
        gapZoom:-1,//与级别的差
        lngRight: "", //四个坐标点
        lngLeft: "",
        latTop: "",
        latBottom: "",
        isSchool: "", //是不是学区房
        isSubWay: "1", //是不是地铁房
        isPayment: "", //是不是低首付
        isExistinfo: "", //是不是现房
        rentType:1,//租赁类型
        scroll:true,//加载开关
        housingEstate:"",//小区id
        page:1,//列表页
        zoomType:1,//地图显示级别  1区域  2 板块  3房源
        navIsActive: 4, //左侧菜单选中项
        windowWidth: window.innerWidth, //
        height: window.innerHeight-100, // //50是主导航栏高度
        listwidth: 420, //左侧列表宽度
        showSelectList: -1, //筛选项当前显示的是哪一个  -1代表没有列表显示
        priceList: { // 价格》》》》
            ActiveText: '价格',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        layoutList: { //户型》》》》》
            ActiveText: '户型',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        rentTypeList: { //出租方式-------
            ActiveText: '出租方式',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        subWayList: { //地铁线 》》》》》
            ActiveText: '地铁',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        subWayStationText: { //地铁站
            ActiveText: '',
            ActiveId: "",
        },
        newHousList: [], //房源列表
        decidedType:"1",//对应接口中tab 其中不填或者填0则由接口提供返回zoomlevel，填其他则由客户端js处理；
        searchValue:"",
        projectId:"",
        searchZoomLevel:18,
        sreachResultType:true,//用来控制左侧表单的文字；
        houseListheigth:window.innerHeight-307,
        searchModel:false,//用来控制是否是搜索模式，用于控制站点是否为常立
        houseSwitch:false,//房源展示开关 ture房源展示 false房源不展示
        subwaySwitchFlag:false,//下拉切换线路开关
        lineOrSubOrStation:3,  //  地铁线:1  小区:2 区域:3 板块:4----------
        totalSecondCount:"",   //小区在售数量》》》》》
        inputMaxPrice:"", //输入框最大价格》》》》》
        inputMinPrice:"",//输入框最小价格》》》》》
        orderKeyId:"",//排序》》》》》
        agentMobile:"",//经纪人电话》》》》》
        mapType:1, //地图类型 1，区域 2，地铁-------
        region_id:"",//区域的id---------
        search_type:"",//搜索框内容类型  1，地铁站点 2，具体小区
        allLabels:[],
        allSubway:[],
    },
    computed: {
        mapWidth: function() {
            var mapWidth;
            if(this.houseType==2){
                mapWidth=this.windowWidth - this.listwidth
            }else{
                mapWidth=this.windowWidth - 60 - this.listwidth
            }
            return mapWidth
        },
        recommentSwitch:function () {
            // if(this.newHousList == ''||this.newHousList == undefined||this.newHousList.length == 0){
            //     return "block";
            // }else {
            //     return "none";
            // }
        }
    },
    methods: {
        //初始化
        init: function() {
            //区分新房二手房租房的细微差别
            // baiduMap.typeList.ActiveText=baiduMap.houseType==2?"类型":"户型"
            baiduMap.listwidth=baiduMap.houseType==1?420:530;

            baiduMap.map = new qq.maps.Map(document.getElementById("baiduMap"), {
                center: new qq.maps.LatLng(41.798142, 123.439941),      // 地图的中心地理坐标。
                zoom:11,                                                // 地图的中心地理坐标。
                panControl: false,         //平移控件的初始启用/停用状态。
                zoomControl: false,       //缩放控件的初始启用/停用状态。
                scaleControl: true,       //滚动控件的初始启用/停用状态。
                mapTypeControl: false,
            })

            // 监听地图的 idle 事件，即加载完成
            qq.maps.event.addDomListener(baiduMap.map, 'idle', function() {
                baiduMap.canSeeArea();//可视区域
                var url = location.href;
                if(url.indexOf('subway') != -1){
                    baiduMap.mapType = 2;

                }else if(baiduMap.mapType == 1){
                    baiduMap.getRegionDataList();//初始化第一次获取地图数据(默认展示区域的)=====
                }
                // level=1 返回站点 level=2 返回站点和小区  level=3 返回区域 level=4 返回板块
                // if(baiduMap.lineOrSubOrStation!=1 && url.indexOf('subway') == -1){
                if(url.indexOf('subway') == -1 && baiduMap.mapType == 1){

                    polyline[1].setStrokeColor(new qq.maps.Color(219,65,63,0));
                    polyline[2].setStrokeColor(new qq.maps.Color(250,109,21,0));
                    polyline[4].setStrokeColor(new qq.maps.Color(148,100,214,0));
                    polyline[6].setStrokeColor(new qq.maps.Color(242,180,11,0));
                    polyline[9].setStrokeColor(new qq.maps.Color(72,127,226,0));
                    polyline[7].setStrokeColor(new qq.maps.Color(68,173,75,0));
                }
            });



            /*baiduMap.map = new BMap.Map("baiduMap",{enableMapClick:false,minZoom:11});//创建地图并且禁用弹出信息框
            var point = new BMap.Point(123.439941, 41.798142); //中心点
            baiduMap.map.centerAndZoom(point,baiduMap.mapZoom); // 编写自定义函数，创建标注
            baiduMap.map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
            // baiduMap.newHouseList(); //初始化第一次获取地图数据
            baiduMap.canSeeArea();
            // baiduMap.getRegionDataList();
            var url = location.href;
            if(url.indexOf('subway') != -1){
                baiduMap.mapType = 2;
                baiduMap.newHouseList();
            }else if(baiduMap.mapType == 1){
                baiduMap.getRegionDataList();//初始化第一次获取地图数据(默认展示区域的)=====
            }
            baiduMap.map.addControl(new BMap.ScaleControl({anchor: BMAP_ANCHOR_BOTTOM_RIGHT}));//比例尺显示在右下角
            //移动时获取数据-------
            baiduMap.map.addEventListener("dragend", function() {
                //搜站点 移动后 baiduMap.mapType 被赋值为 1 导致地铁线覆盖物没有了
                // baiduMap.mapType = $(".hoverMap").attr("data-mapNum");
                baiduMap.page=1;
                // baiduMap.housingEstate="";
                baiduMap.canSeeArea();
                if(baiduMap.mapType == 2){
                    baiduMap.subwayStratumJudge();
                }else  if(baiduMap.mapType == 1){
                    baiduMap.regionStratumJudge();
                }
            })
            //缩放时重新获取数据
            baiduMap.map.addEventListener("zoomend", function() {
                baiduMap.clearAllLabels(); //清除覆盖物
                if(baiduMap.mapType == 2) {
                    if (baiduMap.map.getZoom() == 11 || baiduMap.map.getZoom() == 12) {

                        $("#saleMetroSelect").hide();
                    } else {
                        $("#saleMetroSelect").show();
                    }
                    baiduMap.canSeeArea();
                    if(baiduMap.search_type == ""){
                        baiduMap.subwayStratumJudge();
                    }
                }else if(baiduMap.mapType == 1){
                    if(baiduMap.search_type == ""){
                        baiduMap.showHouse(); //解决小区层级缩放至区域层级 区域展示不全问题
                        baiduMap.regionStratumJudge();
                    }
                }
            });
            if(baiduMap.houseType==2){
                //下拉选择 出租方式-----------
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/viewRentMapFilter", baiduMap.rentTypeList);
                //下拉选择 价格-----------
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getRentPriceFilter", baiduMap.priceList);
                //下拉选择 户型-----------
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getRoomTypeFilter", baiduMap.layoutList);
            }

            //窗口大小变化重新计算高度
            window.onresize = function(){
                baiduMap.height=window.innerHeight-113;
                baiduMap.setHouseHigth();
            }
            //滚动加载
            window.document.getElementsByClassName("HouseList")[0].addEventListener("scroll",winScroll);
            function winScroll(){
                var scrTop = window.document.getElementsByClassName("HouseList")[0].scrollTop;
                var windoweight;
                if($(".Choice").length>0){
                    windoweight = (window.document.getElementsByClassName("HouseList")[0].getElementsByTagName("ul")[0].offsetHeight-(baiduMap.height-153));
                }else{
                    windoweight = (window.document.getElementsByClassName("HouseList")[0].getElementsByTagName("ul")[0].offsetHeight-(baiduMap.height-106));
                }
                if(scrTop== windoweight){
                    baiduMap.scroll=false;
                    baiduMap.page += 1;
                    if(!(baiduMap.map.getZoom()>baiduMap.maxRegion)){
                        document.getElementById("loading").style.display="block"
                    }
                }

            }*/
            //窗口大小变化重新计算高度
            window.onresize = function(){
                baiduMap.height=window.innerHeight-100;
                baiduMap.setHouseHigth();
            }

        },
        //设置地图中心点
        SetMap: function ( lat,lng, size) {
            baiduMap.map.zoomTo(size);
            baiduMap.map.bdpoit = new BMap.Point(lng, lat);
            baiduMap.map.panTo(baiduMap.map.bdpoit); //将地图移动到点
            baiduMap.showHouse()
        },
        //设置地图中心点
        SetMap1: function ( lat,lng, size) {
            baiduMap.map.zoomTo(size)
            baiduMap.map.setCenter(new qq.maps.LatLng(lat,lng))

            // baiduMap.map.bdpoit = new BMap.Point(lng, lat);
            // baiduMap.map.centerAndZoom(baiduMap.map.bdpoit, size);

        },
        //构造 覆盖物
        overlay: function(point, text, mouseoverText,total) {
            this._point = point;
            this._text = text;
            this._overText = mouseoverText;
            this._total = total;
        },
        //下拉选择不限
        navNormal:function(data,text){
            data.ActiveText=text;
            baiduMap.showSelectList=-1;
            data.ActiveId='';
            if(text == "地铁" || text == "地铁不限"){
                baiduMap.subWayStationText.ActiveId = "";
                baiduMap.subWayStationText.ActiveText = "";
                baiduMap.subwaySwitchFlag = false;
            }
            baiduMap.showHouse();
            //小区层级 请求左侧列表数据-------
            if(baiduMap.map.getZoom() >= baiduMap.maxPlat){
                baiduMap.newHouseListRegionAjax();
            }
            /* if(text == "出租方式" || text == "价格" || text == "户型"){
                 $(".metroSelectBtn p").css("fontWeight","normal");
                 $(".metroSelectBtn>ul").hide();
             }*/
        },

        //筛选条件 下拉菜单初始化
        navInit: function(url, nav) {
            $.ajax({
                type: "POST",
                url: url,
                dataType: "json",
                data: {},
                success: function(data) {
                    nav.content = data.content
                },
                error: function(data) {
                    console.log(data)
                }
            })
        },
        //筛选条件 下拉菜单选择
        navSelect: function(showData, dataName,dataId) {
            baiduMap.page=1;
            showData.ActiveText = dataName;
            this.showSelectList = -1;
            showData.ActiveId = dataId;
            if(baiduMap.map.getZoom() >= baiduMap.maxPlat){
                baiduMap.newHouseListRegionAjax();
            }
            baiduMap.showHouse();

            if(showData == baiduMap.subWayList){
                baiduMap.subwaySwitchFlag = true;
                baiduMap.subWayStationText.ActiveId = "";
                baiduMap.subWayStationText.ActiveText = "";
            }
        },

        deleteAllValue: function() {
            $("#saleMetroNav .saleMetroSelect div p").css("fontWeight","normal")
            $(".more_screen li").removeClass("hover")
            $(".moreSelect").hide();
            this.priceList.ActiveText="价格";//价格--------
            this.priceList.ActiveId='';
            this.layoutList.ActiveText="户型";//户型--------
            this.layoutList.ActiveId='';
            this.rentTypeList.ActiveText="出租方式";//出租方式-------
            this.rentTypeList.ActiveId='';
            baiduMap.subWayStationText.ActiveId = "";
            baiduMap.subWayStationText.ActiveText = "";
            if(baiduMap.map.getZoom() > baiduMap.mapZoom){
                baiduMap.canSeeArea();
                baiduMap.newHouseList();
            }
            if(baiduMap.mapType == 1){
                baiduMap.regionStratumJudge();
            }else {
                baiduMap.subwayStratumJudge();
            }
            // baiduMap.getRegionDataList();
            if(baiduMap.map.getZoom() >= baiduMap.maxPlat){
                baiduMap.newHouseListRegionAjax();
            }

        },
        setHouseHigth:function () {
            var selectCount = 0;
            if(this.subWayList.ActiveId != ''){
                selectCount = selectCount+1;
            }
            if(this.priceList.ActiveId != ''){
                selectCount = selectCount+1;
            }
            if(this.subWayStationText.ActiveId != ''){
                selectCount = selectCount+1;
            }
            if(selectCount >3){
                baiduMap.houseListheigth =  window.innerHeight-50 -83;
            }else if(selectCount <=3 && selectCount >0){
                baiduMap.houseListheigth =  window.innerHeight-50 -47;
            }else {
                baiduMap.houseListheigth =  window.innerHeight-50;
            }

        },
        //  地铁模式 获取地图站点 及 视域具体小区----------
        newHouseList: function() {

            $.ajax({
                type: "POST",
                url: "/rentMapDualInfo",
                dataType: "json",
                async:false,
                data: {
                    subwayId: baiduMap.subWayList.ActiveId,//地铁线线线id》》》》》》》》》
                    priceId: baiduMap.priceList.ActiveId,//价格id》》》》》
                    room: baiduMap.layoutList.ActiveId,//户型id》》》》》
                    rentalTypeId:baiduMap.rentTypeList.ActiveId,//出租方式---------
                    leftLng:baiduMap.lngLeft,
                    leftLat:baiduMap.latBottom,
                    rightLng:baiduMap.lngRight,
                    rightLat:baiduMap.latTop,
                    // pageSize:10000,//分页
                    level:baiduMap.lineOrSubOrStation, // level=1 返回站点 level=2 返回站点和小区》》》》》》》》
                },
                beforeSend: function() {
                },
                success: function(data) {
                    var subList = new Array();
                    if(data.status == 1) {
                        var info_type ="";
                        subList = data.content.content;
                        console.log(subList);
                        //加地铁站名
                        if(subList != null  && subList != "")
                            baiduMap.addRegionLabel(subList)//划点
                            for (var i = 0; i < subList.length; i++) {
                                info_type = subList[i].infoType;
                                if(subList[i].infoType == 1){ //infoType=1 是小区
                                    var Price = "";
                                    if (subList[i].unitPrice == null || subList[i].unitPrice == 0) {
                                        // Price = "暂无资料";
                                    } else {
                                        // var unit_pr =  parseFloat(subList[i].unitPrice);
                                        Price = subList[i].unitPrice;
                                    }
                                    if(subList[i].id == parseInt(baiduMap.housingEstate)){
                                        baiduMap.totalSecondCount = subList[i].houseCount;
                                    }
                                    //小区图片》》》》》
                                    var sub_pic ="";
                                    if(subList[i].pic == null || subList[i].pic == ""){
                                        sub_pic = "https://static.fangxiaoer.com/web/images/map/noVallageMain.jpg";
                                    }else{
                                        sub_pic = subList[i].pic;
                                    }
                                    if(subList[i].houseCount >0 || (subList[i].id == parseInt(baiduMap.housingEstate))){
                                        /*baiduMap.showRegionOverlay(new BMap.Point(subList[i].longitude, subList[i].latitude), subList[i].infoName, Price, subList[i].houseCount+"套", sub_pic, "", subList[i].id,subList[i].propertyCom,info_type);*/
                                    }
                                }else if(subList[i].infoType == 2){ //infoType=2 是站点
                                    if(baiduMap.map.getZoom() < baiduMap.maxPlat){
                                        $(".noHouseMap").hide();
                                    }
                                    // baiduMap.showRegionOverlay(new BMap.Point(subList[i].longitude, subList[i].latitude), subList[i].infoName, "", "", "", "", subList[i].id,"在租"+subList[i].houseCount+"套",info_type);
                                }
                            }
                    }
                },
                error: function(data) {
                    console.log(data)
                }
            });
        },
        //地图左侧房源列表》》》》》
        newHouseListRegionAjax:function(){
            $.ajax({
                type: "POST",
                url: "/getRentMapLeftList",
                async:false,
                dataType: "json",
                data: {
                    priceId: baiduMap.priceList.ActiveId,//价格id》》》》》
                    room: baiduMap.layoutList.ActiveId,//户型id》》》》》
                    orderKey:baiduMap.orderKeyId,//排序id （默认、总价高低、面积大小、最新）》》》》》
                    page:baiduMap.page,//页数
                    pageSize:10000,//分页
                    subId:baiduMap.housingEstate, //小区Id》》》》》
                    rentalTypeId:baiduMap.rentTypeList.ActiveId,//出租方式---------
                },
                beforeSend: function() {
                },
                success: function(data) {
                    var subList = new Array();
                    if(data.status == 1) {
                        subList = data.content;
                        $("#map_house_total").text(subList.length);
                        if(subList.length == 0){
                            // if (baiduMap.totalSecondCount == 0) {
                            //在售房源=0 房源列表隐藏 表单链接展示
                            $(".noHouseMap").show();
                            $("#metroMapList").hide();
                            $(".listBtn").hide();
                            // var noHouseMapH = window.innerHeight-307;
                            $(".noHouseMap").css("height",window.innerHeight-113)
                        }else if( baiduMap.housingEstate != ""){
                            baiduMap.getPlotDetails();
                            $(".noHouseMap").hide();
                            $(".mapHouseList").show();
                            $(".listBtn").show();
                            var metroMapListUlHeight = window.innerHeight-295;
                            $(".mMapListUl").css("height",metroMapListUlHeight)
                        }

                        document.getElementById("loading").style.display = "none";
                        if (baiduMap.page == 1) {
                            baiduMap.newHousList = subList;
                        } else {
                            for (var i = 0; i < subList.length; i++) {//10  是分页大小
                                baiduMap.newHousList.push(subList[i]);
                            }
                            baiduMap.scroll = true;
                        }
                    }
                },
                error: function(data) {
                    document.getElementById("loading").style.display="none";
                    // console.log(data)
                }
            });
        },
        //区域模式 地图数据
        getRegionDataList:function(){
            $.ajax({
                type: "POST",
                url: "/rentMapDualInfo",
                dataType: "json",
                async:false,
                data: {
                    priceId: baiduMap.priceList.ActiveId,//价格id》》》》》
                    room: baiduMap.layoutList.ActiveId,//户型id》》》》》
                    rentalTypeId:baiduMap.rentTypeList.ActiveId,//出租方式---------
                    leftLng:baiduMap.lngLeft,
                    leftLat:baiduMap.latBottom,
                    rightLng:baiduMap.lngRight,
                    rightLat:baiduMap.latTop,
                    level:baiduMap.lineOrSubOrStation, // level=1 返回站点 level=2 返回站点和小区  level=3 返回区域 level=4 返回板块
                    regionId:baiduMap.region_id, //区域id-----------
                },
                beforeSend: function() {
                },
                success: function(data) {
                    /*  if(baiduMap.map.getZoom() == 11 || baiduMap.map.getZoom() == 12){
                          $("#saleMetroSelect").hide();
                      }else{
                          $("#saleMetroSelect").show();
                      }*/
                    var subList = new Array();
                    if(data.status == 1) {
                        var info_type ="";
                        subList = data.content.content;
                        //加地铁站名
                        if(subList != null  && subList != ""){
                            baiduMap.addRegionLabel(subList)//划点
                            if(baiduMap.map.getZoom() == 11 || baiduMap.map.getZoom() == 12 || baiduMap.map.getZoom() == 13){
                                for (var i = 0; i < subList.length; i++) {//区域层级
                                    /*baiduMap.showOverlay(new BMap.Point(subList[i].longitude, subList[i].latitude), subList[i].infoName, subList[i].houseCount+"套", subList[i].houseCount+"套", "", "", subList[i].id,subList[i].propertyCom,info_type);*/
                                }
                            }else if(baiduMap.map.getZoom() == 14 || baiduMap.map.getZoom() == 15){
                                for (var i = 0; i < subList.length; i++) {//板块层级
                                    /*baiduMap.showOverlay(new BMap.Point(subList[i].longitude, subList[i].latitude), subList[i].infoName, subList[i].houseCount+"套", subList[i].houseCount+"套", "", "", subList[i].id,subList[i].propertyCom,info_type);*/
                                }
                            }

                        }
                    }
                },
                error: function(data) {
                    console.log(data)
                }
            });
        },
        //定义地铁模式 覆盖物
        showRegionOverlay: function(point, text, num , mapUrl,subPic,subId,housingId,wuye,infoType) {
            ComplexCustomOverlay.prototype = new BMap.Overlay();
            ComplexCustomOverlay.prototype.initialize = function(map) {
                this._map = map;
                var border_div = this._div = document.createElement("div");
                border_div.style.position = "absolute";
                var div = document.createElement("div");
                div.style.position = "absolute";
                div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
                var divKuang = document.createElement("div");
                var that = this;
                var arrow = this._arrow = document.createElement("div");
                baiduMap.houseType==2
                arrow.style.position = "absolute";
                arrow.style.width = "8px";
                arrow.style.height = "8px";

                arrow.style.left = "0px";
                div.appendChild(arrow);

                if(baiduMap.mapType == 2 || baiduMap.search_type == 1){
                    for(var i=0;i<polyline.length;i++){
                        if(polyline[i]!=""  && polyline[i]!=null){
                            baiduMap.map.addOverlay(polyline[i]);
                        }
                    }
                }else if(baiduMap.mapType == 1){
                    /*baiduMap.map.removeOverlay(polyline[1]);
                    baiduMap.map.removeOverlay(polyline[2]);
                    baiduMap.map.removeOverlay(polyline[4]);
                    baiduMap.map.removeOverlay(polyline[7]);
                    baiduMap.map.removeOverlay(polyline[6]);
                    baiduMap.map.removeOverlay(polyline[9]);*/
                    $(".vallgeSite").hide(); // 站点名称+图标
                    $(".tohotelshade").remove();//区域模式删除蒙版
                    $(".stationModel li").removeClass("hover");//去掉地铁线图标选中效果
                    $(".stationModel").hide();//去掉地铁线图标
                }

                if(baiduMap.map.getZoom() ==15 || baiduMap.map.getZoom() ==14 || baiduMap.map.getZoom() ==13 ){//地铁站点层级时覆盖物
                    div.className ="lineSite";
                    arrow.style.top = "51px";
                    var span = this._span = document.createElement("span");
                    div.appendChild(span);
                    var p = this._span = document.createElement("p");
                    div.appendChild(p);
                    // infoType!= undefined && infoType.length>5?span.className="":"";
                    span.appendChild(document.createTextNode(text));
                    p.appendChild(document.createTextNode(wuye)); //站点层级 wuye=在售房源数
                }
                if(baiduMap.map.getZoom() >= baiduMap.maxPlat){//小区层级时覆盖物
                    if(infoType == 1){
                        console.log("小区地铁覆盖物的请求！！！");
                        //小区层级赋id》》》》》
                        div.setAttribute("projectId",housingId);
                        if(housingId == baiduMap.housingEstate){
                            $(".showVallge").attr("id","");
                            div.setAttribute("id", "clickHover");
                        }
                        arrow.style.width = "12px";
                        arrow.style.height = "8px";
                        arrow.style.left = "65px";
                        arrow.style.top = "22px";
                        //价格处理
                        var vallagePrice = "";
                        var unit_pr =  parseFloat(num);
                        if(num == null || num == 0){
                            vallagePrice = "";
                        }else{
                            vallagePrice = (unit_pr / 10000).toFixed(1)+"万";
                        }

                        div.className ="showVallge";
                        var span = this._span = document.createElement("span");
                        div.appendChild(span);
                        var p = this._span = document.createElement("p");
                        var p2 = this._span = document.createElement("p");
                        div.appendChild(p);
                        div.appendChild(p2);
                        span.appendChild(document.createTextNode(text));
                        // p.appendChild(document.createTextNode(vallagePrice));
                        p2.appendChild(document.createTextNode(mapUrl));



                        // 悬浮时出现的小区详情框
                        divKuang.className ="vallgeMain";
                        var dk_Img  = document.createElement("img");
                        var dk_Prise = document.createElement("h5");
                        var dk_Name  = document.createElement("p");
                        var dk_Name1  = document.createElement("span");
                        var dk_Name2  = document.createElement("span");
                        var dk_icon  = document.createElement("s");
                        var dk_adress  = document.createElement("i");
                        div.prepend(divKuang);
                        divKuang.appendChild(dk_Img);
                        divKuang.appendChild(dk_Prise);
                        divKuang.appendChild(dk_Name);
                        dk_Name.appendChild(dk_Name1);
                        dk_Name.appendChild(dk_Name2);
                        divKuang.appendChild(dk_icon);
                        divKuang.appendChild(dk_adress);
                        // var junPrice = "";
                        /*  if(num == null || num == 0){
                              junPrice = "小区均价：暂无资料";
                          }else{
                              junPrice = "小区均价："+ num+"元/m²";
                          }*/
                        var wuye_ = "";
                        if(wuye == "" || wuye == null || wuye == undefined ){
                            wuye_ = "";
                        }else{
                            wuye_ = wuye;
                        }
                        dk_Img.setAttribute("src",subPic)
                        // dk_Prise.appendChild(document.createTextNode(junPrice));
                        // dk_Name.appendChild(document.createTextNode(text));
                        dk_Name1.appendChild(document.createTextNode(text));
                        dk_Name2.appendChild(document.createTextNode(mapUrl));
                        dk_icon.appendChild(document.createTextNode("普通住宅"));
                        dk_adress.appendChild(document.createTextNode(wuye_));
                    }else if(infoType == 2) {
                        // console.log("站点地铁覆盖物的请求！！！");
                        div.className = "vallgeSite";
                        var span2 = this._span = document.createElement("span");
                        div.appendChild(span2);
                        span2.appendChild(document.createTextNode(text));
                        $(".tohotelshade").remove(); //地铁模式小区层级去掉蒙版=====11.2

                    }
                    baiduMap.search_type = "";
                }
                if(subId!="" ){
                    div.style.backgroundColor = "#fffff";
                    div.setAttribute("subwayid",subId);
                    div.setAttribute("stationid",housingId);//TODO 单机单个楼盘判断不了哪个站点被选中
                    div.style.zIndex = 99990;
                    var stationFocus =  document.createElement("div");
                    stationFocus.className = "stationFocus";
                    stationFocus.style.display = "none";
                    var stationNormal = document.createElement("div");
                    if(baiduMap.map.getZoom() >= baiduMap.maxPlat){//小区层级时覆盖物
                    }
                    switch (subId){
                        case "1"://1号线
                            stationNormal.style.borderColor = "#DB413F";
                            break;
                        case "2"://2号线
                            stationNormal.style.borderColor = "#FA6D15";
                            break;
                        case "6"://四号线
                            stationNormal.style.borderColor = "#9464D6";
                            break;
                        case "9"://六号线
                            stationNormal.style.borderColor = "#F2B40B";
                            break;
                        case "4"://九号线
                            stationNormal.style.borderColor = "#487FE2";
                            break;
                        case "7"://十号线
                            stationNormal.style.borderColor = "#44AD4B";
                            break;
                        default:
                            stationNormal.style.borderColor = "#e60012";
                            break;
                    }
                    var stationFocusFlag = false;//用于控制搜索时，站牌立起不受mouseover影响

                    //单价不为0》》》》》
                    if(num != 0){
                        if(baiduMap.projectId != "" && baiduMap.subWayStationText.ActiveId == housingId){//搜索点击某个项目当前站点亮起来
                            stationFocus.style.display = "block";
                            div.style.backgroundColor = "#d42b2b";
                            // div.className="showsubWayHover";
                            div.style.display = "block";
                            // stationFocusFlag = true;
                        }else {//搜索模糊字段或者筛选价格等
                            if(baiduMap.subWayList.ActiveId == ""){
                                div.style.display = "block";
                            }else if(subId == baiduMap.subWayList.ActiveId){
                                div.style.display = "block";
                            }else {
                                div.style.display = "none";
                            }
                        }
                    }else {
                        //todo 搜索时被隐藏的div需要滑过展示划走隐藏并且 **不允许div点击
                        div.style.display = "none";
                    }
                    //非搜索模式
                    if(baiduMap.subWayList.ActiveId == ""){//未选线路
                        div.style.display = "none";
                    }else{
                        if(baiduMap.subWayList.ActiveId == subId){
                            div.style.display = "block";
                            //如果有站被点击
                        }else {
                            div.style.display = "none";
                        }
                    }
                    if(baiduMap.subWayStationText.ActiveId != "" && baiduMap.subWayStationText.ActiveId == housingId){//此时是站点被选择后立起
                        stationFocus.style.display = "block";
                        div.className="showSite";
                    }
                    stationNormal.onclick = function () {
                        baiduMap.searchZoomLevel = baiduMap.maxRegion+2;
                        baiduMap.subWayList.ActiveId = subId; //线路id》》》》》
                        baiduMap.subWayStationText.ActiveId = housingId; //小区id》》》》》
                        baiduMap.page = 1;
                        baiduMap.setHouseHigth();
                    }
                }


                div.onmouseover = function() {
                    if(baiduMap.map.getZoom() >= baiduMap.maxPlat){//小区层级时覆盖物
                        // console.log(infoType)
                        if(infoType == 1){
                            div.className ="showVallge";
                            arrow.style.width = "12px";
                            arrow.style.height = "8px";
                            arrow.style.left = "65px";
                            arrow.style.top = "22px";
                            div.firstElementChild.style.display = "block"
                        }else if(infoType == 2){
                            div.className ="vallgeSite";
                            arrow.style.width = "8px";
                            arrow.style.height = "8px";
                            arrow.style.left = "0px";
                            arrow.style.top = "41px";
                        }
                    }

                    if(subId!=""){
                        if(baiduMap.map.getZoom() ==15 || baiduMap.map.getZoom() ==14 || baiduMap.map.getZoom() ==13){//站点层级时覆盖物>>>>>
                            this.className="lineSiteHover";
                            this.style.zIndex="999999";
                        }else if(baiduMap.map.getZoom() >= baiduMap.maxPlat){//小区层级时覆盖物
                            if(infoType == 1){
                                div.className ="showVallge";
                            }else if(infoType == 2){
                                div.className ="vallgeSite";
                            }
                        }
                    }else{
                        this.style.background = "rgba(65, 168, 243, 0.85)";
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                        }else if( baiduMap.map.getZoom()>=baiduMap.maxRegion){
                            baiduMap.houseType==2?this.className="vallgeSiteHover":this.className="showPlat";

                        }
                        this.style.zIndex="99999";
                    }
                }
                div.onmouseout = function() {
                    var spitem = "";
                    if(subId!=""){
                        if(baiduMap.map.getZoom() ==15 || baiduMap.map.getZoom() ==14 || baiduMap.map.getZoom() ==13){//站点层级时覆盖物>>>>>
                            this.style.backgroundColor = "#fff";
                            this.className="lineSite";
                            this.style.zIndex="99900";
                        }
                    }
                    else if(baiduMap.map.getZoom() >= baiduMap.maxPlat){//小区层级时覆盖物
                        if(infoType == 1){
                            div.className ="showVallge";
                            this.style.background = "rgba(255, 82, 0, 0.85)";
                            div.firstElementChild.style.display = "none"
                        }
                    }else{
                        if( baiduMap.map.getZoom()>baiduMap.maxPlat){
                            div.className=spitem;
                        }else if( baiduMap.map.getZoom()>=baiduMap.maxRegion){
                            div.className="lineSite";
                        }
                    }
                }
                var clickPoint=this._point;
                var urlText=mapUrl;
                var clickText=text;
                //点击具体小区 展示左侧该小区列表
                div.onclick = function(){
                    if(baiduMap.map.getZoom() == 13 || baiduMap.map.getZoom() == 14 || baiduMap.map.getZoom() == 15){
                        //以该站点为中心点
                        baiduMap.SetMap1(point.lat,point.lng,baiduMap.maxPlat);
                        //传视域
                        baiduMap.canSeeArea();
                        //返回站点及小区
                        baiduMap.lineOrSubOrStation =2;
                        baiduMap.clearAllLabels(); //清除覆盖物
                        //展示小区房源
                        baiduMap.newHouseList(); //地图站点 及 视域具体小区选我
                    }else if(baiduMap.map.getZoom() >= baiduMap.maxPlat){
                        //传小区id
                        baiduMap.housingEstate = $(this).attr("projectid");
                        $(".showVallge").attr("id","");
                        $(this).attr("id","clickHover");
                        var sub_total = mapUrl.replace("套","");
                        if($(this).attr("projectid") == housingId ){
                            baiduMap.totalSecondCount = sub_total;
                        }
                        if(sub_total > 0){
                            //展示左侧房源列表
                            baiduMap.newHouseListRegionAjax();
                            $(".mapHouseList").show();
                            $("#metroMapList").show();
                            $("#metroMapList").css("left","0");
                            $(".listBtn").addClass("hover");
                            $(".listBtn ").css("left","408px");
                            $(".listBtn i").css("background","url('https://static.fangxiaoer.com/web/images/map/metroIconRight2.png') center top rgb(255, 255, 255)");
                            $(".listBtn").show();
                        }else{
                            $(".noHouseMap").show();
                            $("#metroMapList").hide();
                            $(".listBtn").hide();
                            $(".noHouseMap").css("height",window.innerHeight-113)
                        }
                        //每次点击小区 列表滚动条回到顶端
                        $(".mMapListUl").animate({scrollTop:0},100);
                    }
                }
                border_div.appendChild(div);
                //地铁线id 不为空，站点显示
                if(subId != ""){
                    border_div.appendChild(stationFocus); //站点
                    border_div.appendChild(stationNormal);//站点下的小圆圈
                }
                baiduMap.map.getPanes().labelPane.appendChild(border_div);
                return border_div;

            }
            ComplexCustomOverlay.prototype.draw = function() {
                var map = this._map;
                var pixel = map.pointToOverlayPixel(this._point);
                this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
                this._div.style.top = pixel.y - 44 + "px";
            }


            var myCompOverlay = new ComplexCustomOverlay(point, text, num);
            baiduMap.map.addOverlay(myCompOverlay);
        },

        //区域或板块的 覆盖物
        showOverlay: function(point, text, num , mapUrl,subPic,subId,housingId,wuye,infoType) {
            ComplexCustomOverlay.prototype = new BMap.Overlay();
            ComplexCustomOverlay.prototype.initialize = function(map) {
                $(".tohotelshade").hide();
                this._map = map;
                var div = this._div = document.createElement("div");
                div.style.position = "absolute";
                div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
                var span = this._span = document.createElement("span");
                div.appendChild(span);
                this._text!= undefined && this._text.length>5?span.className="shadow":""
                span.appendChild(document.createTextNode(this._text));
                var that = this;
                var arrow = this._arrow = document.createElement("div");

                if(subId!=""){
                    baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion?div.className="showsubWay":""
                    div.style.backgroundColor = "#fffff";
                }else{
                    if((baiduMap.houseType==2)||(baiduMap.houseType==3) ){
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                            div.className="showHouse";
                            //+1 解决区域放大至2公里时 点击区域 返回所有板块问题
                        }else if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion+1){
                            div.className="showPlat";
                            var att= document.createAttribute("data-id");
                            att.value=housingId;
                            div.style.background = "rgba(255, 82, 0, 0.85)";
                            div.setAttributeNode(att);
                        }else{
                            div.className="showRegion";
                            //为class=showRegion 添加一个（data-id）属性
                            var att= document.createAttribute("data-id");
                            att.value=housingId;
                            div.style.background = "rgba(255, 82, 0, 0.85)";
                            div.setAttributeNode(att);
                        }
                    }
                    // baiduMap.search_type = "";
                    var span1 = this._span = document.createElement("b");
                    div.appendChild(span1);
                    span1.appendChild(document.createTextNode(this._overText));
                }



                arrow.style.position = "absolute";
                arrow.style.width = "11px";
                arrow.style.height = "10px";
                arrow.style.top = "22px";
                arrow.style.left = "20px";
                div.appendChild(arrow);
                div.onmouseenter = function() {
                    //任务12859
                    timer=setTimeout(function () {
                        getBoundary('沈阳市' + text);
                    },100);
                    if(subId!=""){
                        this.style.background = "#e66a6a";
                        baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion?this.className="showsubWayHover":""
                    }else{
                        this.style.background = "rgba(65, 168, 243, 0.85)";//鼠标悬浮背景色是蓝色
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                            div.className="showHouseHover"
                        }else if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion){
                            // baiduMap.houseType==1?div.className="showHouseHover":div.className="showPlat"
                        }
                    }
                    this.style.zIndex="99999";
                }
                div.onmouseleave = function() {
                    //任务12859
                    clearTimeout(timer);
                    var allOverlay = baiduMap.map.getOverlays();
                    for(var i = 0; i < allOverlay.length; i++) {
                        if(allOverlay[i]._text == undefined) {
                            baiduMap.map.removeOverlay(allOverlay[i]);

                            // 这里一定要继续删除下一个！不然是不能把图标给删除的！至于什么原因，打印一下allOverlay即可明了
                        }

                    }

                    if(subId!=""){
                        this.style.backgroundColor = "#fff";
                        baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion?this.className="showsubWay":""
                        this.style.zIndex="99900"
                    }else{
                        this.style.background = "rgba(255, 82, 0, 0.85)";//鼠标移开背景色是主题色
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                            div.className="showHouse"
                        }else if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion){
                            // baiduMap.houseType==1?div.className="showHouse":div.className="showPlat"
                        }
                        this.style.zIndex=""
                    }
                }
                var clickPoint=this._point;
                var urlText=mapUrl;
                var clickText=text;
                div.onclick = function(){
                    //解决 点击大东区 返回其板块 还带有其他区域覆盖物问题
                    baiduMap.clearAllLabels();
                    //区域模式 区域层级 点击传regionID、level=4（返回该区域板块）
                    if($(this).hasClass('showRegion')){
                        baiduMap.region_id = $(this).attr("data-id");
                        baiduMap.lineOrSubOrStation = 4;
                    }else if($(this).hasClass('showPlat')){
                        baiduMap.searchPlateId = $(this).attr("data-id")
                    }
                    // if(baiduMap.mapType == 1){
                    if(baiduMap.map.getZoom()== 12 || baiduMap.map.getZoom() == 13 || baiduMap.map.getZoom() == 11 ){
                        //点击区域展示 该区域所有版块（用SetMap方法会有问题，因此要用SetMap1）
                        baiduMap.SetMap1(clickPoint.lat,clickPoint.lng,baiduMap.maxRegion+1);
                        baiduMap.page=1;
                        baiduMap.region_id=housingId;  //给区域id赋值-------
                        baiduMap.lineOrSubOrStation = 4; // 返回该区域下的所有板块--------
                        // baiduMap.getRegionDataList(); //区域模式数据--------

                        //如果是区域，传regionId
                        if($(this).hasClass('showRegion')){
                            housingId = $(this).attr("data-id");
                        }
                    }else if(baiduMap.map.getZoom() == 14 || baiduMap.map.getZoom() == 15) {
                        //板块层级 点击小区 展示该小区及该小区周边小区（用SetMap方法会有问题，因此要用SetMap1）
                        baiduMap.SetMap1(clickPoint.lat, clickPoint.lng, baiduMap.maxPlat);
                        if ($(this).hasClass('showPlat')) {
                            baiduMap.searchPlateId = $(this).attr("data-id");
                        }
                    }else if(baiduMap.map.getZoom() >= baiduMap.maxPlat){
                        baiduMap.page=1;
                        baiduMap.housingEstate=housingId;
                        baiduMap.getRegionDataList();
                    }
                    /*if(( baiduMap.map.getZoom()-baiduMap.gapZoom<=baiduMap.maxPlat)&&(baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion)){
                        if($(this).hasClass('showPlat')) {
                            baiduMap.searchPlateId = $(this).attr("data-id");
                        }
                        //板块层级 点击小区 展示该小区及该小区周边小区（用SetMap方法会有问题，因此要用SetMap1）
                        baiduMap.SetMap1(clickPoint.lat,clickPoint.lng,baiduMap.maxPlat);
                    }else if((baiduMap.houseType==2)){
                        baiduMap.page=1
                        baiduMap.housingEstate=housingId;
                        baiduMap.getRegionDataList();
                    }*/
                    // }
                }

                baiduMap.map.getPanes().labelPane.appendChild(div);
                return div;
            }
            ComplexCustomOverlay.prototype.draw = function() {
                var map = this._map;
                var pixel = map.pointToOverlayPixel(this._point);
                this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
                this._div.style.top = pixel.y - 30 + "px";
            }
            var myCompOverlay = new ComplexCustomOverlay(point, text, num);
            //数据为0 不显示覆盖物
            if(num != '0套'){
                baiduMap.map.addOverlay(myCompOverlay);
            }
        },
        //每次操作后 重新获取所有数据
        showHouse: function() {
            baiduMap.clearAllLabels(); //清除覆盖物
            baiduMap.canSeeArea();
            if((baiduMap.mapType == 1 && baiduMap.map.getZoom() >= baiduMap.maxPlat) || baiduMap.mapType == 2){
                baiduMap.newHouseList();
            }else if(baiduMap.mapType == 1){
                baiduMap.getRegionDataList();
            }
        },
        showHouseForSearch: function(type) {//type2当前层级筛选
            baiduMap.clearAllLabels(); //清除覆盖物
            baiduMap.canSeeArea();
            if(type ==2){
                //地铁模式
                if(baiduMap.mapType == 2){
                    //筛选在默认层级不可用
                    if(baiduMap.map.getZoom() >= baiduMap.maxRegion) {
                        baiduMap.newHouseList();
                    }

                    //区域模式下小区层级
                }else if(baiduMap.mapType == 1 && baiduMap.map.getZoom() >= baiduMap.maxPlat){
                    baiduMap.newHouseList();
                }else if(baiduMap.mapType == 1){
                    baiduMap.getRegionDataList();
                }
                baiduMap.setHouseHigth();
            }
        },
        showClickLine:function (subwayId) {//此方法用于已选线路，当前线路加粗，非搜索条件下站牌立起
            if(subwayId != ""){
                setLineWeigth(subwayId)
                var lineList = ["1","2","4","6","7","9"];
                if(!baiduMap.searchModel) {
                    for(var i=0;i<lineList.length;i++){
                        if(lineList[i] != subwayId){
                            $("[subwayid="+lineList[i]+"]").hide();
                        }else {
                            $("[subwayid="+lineList[i]+"]").show();
                        }
                    }
                }
            }
        },

        //左侧小区详情、小区专家
        getPlotDetails:function(){
            $.ajax({
                type: "POST",
                url: "/secPlotDetailList",
                dataType: "json",
                data: {subId:baiduMap.housingEstate},
                success: function(data) {
                    plotExpertList = data.plotExpertList;
                    plot_details = data.plot_details.content;
                    if(plot_details != null){
                        $("#map_plot_title").text(plot_details.title);
                        $("#map_plot_title").attr("href","/saleVillages/"+ baiduMap.housingEstate+"/index.htm");
                        /*   if(plot_details.unitPrice != 0 && (plot_details.unitPrice != null || plot_details.unitPrice !='' || plot_details.unitPrice != undefined)){
                               $("#map_jun").text("均价");
                               $("#map_plot_price").text(plot_details.unitPrice);
                               $("#map_plot_price").show();
                               $("#map_jun_unit").text('元/m²');
                               $("#map_jun_unit").show();
                           }else if(plot_details.unitPrice == 0){
                               $("#map_jun").text("暂无资料");
                               $("#map_jun_unit").hide();
                               $("#map_plot_price").hide();
                           }*/
                        /* if(plot_details.increaseRate != 0 && plot_details.increaseRate > 0){
                             $("#map_plot_increaseRate").text(Math.abs(plot_details.increaseRate)+"%");
                             $("#map_rate").text("环比上周高");
                             $("#map_plot_increaseRate").css("color","#ff5200")
                         }else if(plot_details.increaseRate != 0 && plot_details.increaseRate < 0){
                             $("#map_rate").text("环比上周低");
                             $("#map_plot_increaseRate").text(Math.abs(plot_details.increaseRate)+"%");
                             $("#map_plot_increaseRate").css("color","#55a700")
                         }else{
                             $("#map_rate").text("环比上周");
                             $("#map_plot_increaseRate").text('----');
                             $("#map_plot_increaseRate").css("color","#999")
                         }*/
                        $("#map_plot_address").text(plot_details.address.replace("辽宁省",''));
                    }
                    if(plotExpertList != null ){
                        if(plotExpertList.length <= 0){
                            $("#map_agent_information").hide();
                        }else  if(plotExpertList.length > 0){
                            for(var i=0;i<plotExpertList.length;i++){
                                $("#map_agent_information").show();
                                $("#map_agent_jump").attr("href","/agent/second/"+ plotExpertList[0]._memberId);
                                $("#map_agent_photo").attr("src",plotExpertList[0].avatar);
                                $("#map_agent").text(plotExpertList[0].realName +' '+plotExpertList[0].intermediaryName).attr("href","/agent/second/"+ plotExpertList[0]._memberId);
                                // $("#map_agent_name").text(plotExpertList[0].realName);
                                // $("#map_agent_title").text(plotExpertList[0].intermediaryName);
                                $("#map_agent_mobile").text(plotExpertList[0].mobile);
                                baiduMap.agentMobile = plotExpertList[0].mobile;
                                break;
                            }
                        }
                    }
                },
                error: function(data) {
                    console.log(data)
                }
            })
        },
        //视域计算
        canSeeArea:function(){
            var mapBounds = baiduMap.map.getBounds();
            var leftlng = mapBounds.getSouthWest().getLng().toFixed(6)
            var leftlat = mapBounds.getSouthWest().getLat().toFixed(6)
            var rightlng = mapBounds.getNorthEast().getLng().toFixed(6)
            var rightlat = mapBounds.getNorthEast().getLat().toFixed(6)
            baiduMap.lngRight = rightlng;
            baiduMap.lngLeft =  leftlng;
            baiduMap.latTop =  rightlat;
            baiduMap.latBottom =  leftlat;
        },
        //区域模式 各层级
        regionStratumJudge:function(){
            baiduMap.subWayList.ActiveId = "";
            /*baiduMap.map.removeOverlay(polyline[1]);
            baiduMap.map.removeOverlay(polyline[2]);
            baiduMap.map.removeOverlay(polyline[4]);
            baiduMap.map.removeOverlay(polyline[7]);
            baiduMap.map.removeOverlay(polyline[6]);
            baiduMap.map.removeOverlay(polyline[9]);*/
            $(".vallgeSite").hide(); // 站点名称+图标
            $("#saleMetroSelect").show();
            baiduMap.clearAllLabels(); //清除覆盖物
            if(baiduMap.map.getZoom() == 13 || baiduMap.map.getZoom() == 12 || baiduMap.map.getZoom() == 11){
                $(".tohotelshade").hide();
                baiduMap.lineOrSubOrStation = 3;
                baiduMap.region_id =""; //区域层级 显示所有区域
                baiduMap.getRegionDataList();//quyu右侧小区数据
            }else if(baiduMap.map.getZoom() == 14 || baiduMap.map.getZoom() == 15){
                $(".tohotelshade").hide();
                baiduMap.subWayList.ActiveId = "";
                baiduMap.lineOrSubOrStation = 4;
                baiduMap.getRegionDataList();//quyu右侧小区数据
            }else if(baiduMap.map.getZoom() >= baiduMap.maxPlat){
                baiduMap.lineOrSubOrStation = 2;
                baiduMap.newHouseList(); //右侧地铁小区层级数据
                $(".vallgeSite").hide(); // 站点名称+图标
            }
        },
        //地铁模式 各层级
        subwayStratumJudge:function(){
            baiduMap.clearAllLabels(); //清除覆盖物
            if(baiduMap.subWayList.ActiveId == ""){
                $(".stationModel li").removeClass("hover");
            }
            if(baiduMap.map.getZoom() == 13 || baiduMap.map.getZoom() == 14 || baiduMap.map.getZoom() == 15){
                $(".tohotelshade").remove();
                $(".tohotelshade").show(); //线路指示图
                // $("#baiduMap>div>div").prepend("<div class='tohotelshade'></div>");
                baiduMap.lineOrSubOrStation = 1;
            }else{
                $(".tohotelshade").remove();
            }
            //小区层级 返回小区
            if(baiduMap.map.getZoom() >= baiduMap.maxPlat){
                baiduMap.lineOrSubOrStation =2;
                $(".vallgeSite").show(); // 站点名称+图标
            }
            baiduMap.newHouseList(); //ditie右侧小区数据
        },

        fontWeight:function(){
            if(baiduMap.rentTypeList.ActiveId != ""){
                $(".rentType_show_text").css("fontWeight","bold");
                $(".metroSelectBtn>ul").hide();
            }
            if(baiduMap.priceList.ActiveId != ""){
                $(".price_show_text").css("fontWeight","bold");
                $(".metroSelectBtn>ul").hide();
            }
            if(baiduMap.layoutList.ActiveId != ""){
                $(".huxing_show_text").css("fontWeight","bold");
                $(".metroSelectBtn>ul").hide();
            }

        },
        fontWeight11:function(){
            $(".rentType_show_text").css("fontWeight","normal");
            $(".metroSelectBtn>ul").hide();
        },
        fontWeight22:function(){
            $(".price_show_text").css("fontWeight","normal");
            $(".metroSelectBtn>ul").hide();
        },
        fontWeight33:function(){
            $(".huxing_show_text").css("fontWeight","normal");
            $(".metroSelectBtn>ul").hide();
        },


        /*-----------*/
        // 创建区域层级标记
        addRegionLabel(data){
            let areaStyle={
                color: '#FFF',
                fontSize: '14px',
                borderRadius: '33px',
                backgroundColor: "rgba(255, 82, 0, 0.85)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                width:'66px',
                height:'54px',
                paddingTop:"12px",
            }
            let areaHoverStyle={
                color: '#FFF',
                fontSize: '14px',
                borderRadius: '33px',
                backgroundColor: "rgba(65, 168, 243, 0.85)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                width:'66px',
                height:'54px',
                paddingTop:"12px",
            }
            let saleStyle={
                color: '#FFF',
                fontSize: '14px',
                backgroundColor: "rgba(255, 82, 0, 0.85)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                height:'24px',
                padding:"0 15px"
            }
            let saleHoverStyle={
                color: '#FFF',
                fontSize: '14px',
                backgroundColor: "rgba(65, 168, 243, 0.85)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                height:'24px',
                padding:"0 15px"
            }
            let subwayStyle={
                color: '#000',
                fontSize: '14px',
                backgroundColor: "#fff",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                padding:"5px 10px",
                boxShadow: "1px 1px 8px #bdb9b9",
            }
            let subwayHoverStyle={
                color: '#fff',
                fontSize: '14px',
                backgroundColor: "rgba(79,109,255,1)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                padding:"5px 10px",
                boxShadow: "1px 1px 8px #bdb9b9",
            }
            let content,activeStyle
            //样式
            // baiduMap.lineOrSubOrStation, // level=1 返回站点 level=2 返回站点和小区  level=3 返回区域 level=4 返回板块
            console.log(baiduMap.lineOrSubOrStation+'--------')//  地铁线:1  小区:2 区域:3 板块:4----------
            switch (baiduMap.lineOrSubOrStation){
                case 1:
                    activeStyle = subwayStyle
                    break;
                case 2:
                    activeStyle = saleStyle
                    break;
                case 3:
                    activeStyle = areaStyle
                    break;
                case 4:
                    activeStyle = areaStyle
                    break;
                default:
                    activeStyle = areaStyle
            }



            data.forEach(function(item) {

                // item.houseCount//0条
                // baiduMap.mapType == 2//地铁
                // baiduMap.lineOrSubOrStation==1// 地铁线:1  小区:2 区域:3 板块:4

                if(item.houseCount!=0 || baiduMap.lineOrSubOrStation == 1){
                    if(baiduMap.lineOrSubOrStation==2){
                        content = `<p>${item.infoName}  ${item.houseCount}套</p><div class="sanjiao sanjiao1 t${item.id}"></div>
                                <div class="vallgeMain f${item.id}" ><img src="${(item.pic==null || item.pic=='')?'https://static.fangxiaoer.com/web/images/map/noVallageMain.jpg':item.pic}"><h5>小区均价：${item.unitPrice}元/m²</h5><p><span>${item.infoName}</span><span>${item.houseCount}套</span></p><s>普通住宅</s><i>${item.propertyCom}</i></div>`
                    }else{
                        content = `<p>${item.infoName}</p><p>${item.houseCount}套</p>`
                    }
                    var label = new qq.maps.Label({
                        position: new qq.maps.LatLng(item.latitude,item.longitude),
                        map: baiduMap.map,
                        content: content,
                        style: activeStyle,
                        item: item
                    });
                    qq.maps.event.addDomListener(label, 'click', function() {
                        baiduMap.clickFun(item,data)
                    });
                    //鼠标移入
                    qq.maps.event.addDomListener(label, 'mouseover', function() {
                        console.log(baiduMap.lineOrSubOrStation)
                        if(baiduMap.lineOrSubOrStation==2){
                            $(".f"+label.item.id).show()
                            label.setStyle(saleHoverStyle)
                            $(".t"+label.item.id).css("background",'url("https://static.fangxiaoer.com/web/images/ico/map/blue.png") no-repeat')
                        }else if(baiduMap.lineOrSubOrStation==1){
                            label.setStyle(subwayHoverStyle)
                        }else{
                            label.setStyle(areaHoverStyle)
                        }
                    });
                    //鼠标移出
                    qq.maps.event.addDomListener(label, 'mouseout', function() {
                        if(baiduMap.lineOrSubOrStation==2){
                            $(".f"+label.item.id).hide()
                            label.setStyle(saleStyle)
                            $(".t"+label.item.id).css("background",'url("https://static.fangxiaoer.com/web/images/ico/map/orange.png") no-repeat')
                        }else if(baiduMap.lineOrSubOrStation==1){
                            label.setStyle(subwayStyle)
                        }else{
                            label.setStyle(areaStyle)
                        }
                    });
                    baiduMap.allLabels.push(label);
                }


            });
        },
        //划地铁站
        addSubwayLabel(data) {
            let locationStyle={
                color: '#FFF',
                fontSize: '14px',
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                backgroundColor: "rgba(0, 0, 0, 0)",
            }
            console.log(data)
            let content=
                `<div class="vallgeSite">
                    <div ></div>
                 </div>`
            data.forEach(function(item) {
                var label2 = new qq.maps.Label({
                    position: new qq.maps.LatLng(item.latitude,item.longitude),
                    map: baiduMap.map,
                    content: content,
                    style: locationStyle,
                    item: item
                });
                baiduMap.allSubway.push(label2)
            })
        },
        //清除所有地铁站
        clearAaaallSubway(){
            baiduMap.allSubway.forEach(function(label2) {
                label2.setMap(null);
            });
            baiduMap.allSubway=[]
        },
        //清除所有覆盖物
        clearAllLabels (){
            baiduMap.allLabels.forEach(function(label) {
                label.setMap(null);
            });
            baiduMap.allLabels = [];
        },
        //点击标记点
        clickFun(t,data){
            baiduMap.map.setCenter(new qq.maps.LatLng(t.latitude,t.longitude))//设置中心点
            let zoom=baiduMap.map.getZoom()


            if(zoom == 13 || zoom == 14 || zoom == 15){
                //以该站点为中心点
                // baiduMap.SetMap1(point.lat,point.lng,baiduMap.maxPlat);
                //传视域
                baiduMap.canSeeArea();
                //返回站点及小区
                baiduMap.lineOrSubOrStation =2;
                baiduMap.clearAllLabels(); //清除覆盖物
                //展示小区房源
                baiduMap.newHouseList(); //地图站点 及 视域具体小区选我
                baiduMap.map.zoomTo(16)//设置缩放层级

                if(baiduMap.mapType==2){
                    console.log('划地铁站')
                    baiduMap.addSubwayLabel(data)
                }
            }else if(zoom >= baiduMap.maxPlat){
                //传小区id
                baiduMap.housingEstate = t.id
                $(".showVallge").attr("id","");
                $(this).attr("id","clickHover");
                if(t.houseCount > 0){
                    //展示左侧房源列表
                    baiduMap.newHouseListRegionAjax();
                    $(".mapHouseList").show();
                    $("#metroMapList").show();
                    $("#metroMapList").css("left","0");
                    $(".listBtn").addClass("hover");
                    $(".listBtn ").css("left","408px");
                    $(".listBtn i").css("background","url('https://static.fangxiaoer.com/web/images/map/metroIconRight2.png') center top rgb(255, 255, 255)");
                    $(".listBtn").show();
                }else{
                    $(".noHouseMap").show();
                    $("#metroMapList").hide();
                    $(".listBtn").hide();
                    $(".noHouseMap").css("height",window.innerHeight-113)
                }
                //每次点击小区 列表滚动条回到顶端
                $(".mMapListUl").animate({scrollTop:0},100);

            }else{
                baiduMap.lineOrSubOrStation =4
                baiduMap.clearAllLabels()//清除覆盖物
                baiduMap.page=1;
                baiduMap.region_id=t.regionId;  //给区域id赋值-------
                baiduMap.map.zoomTo(14)//设置缩放层级
            }




        }
    }
})
//构造自定义覆盖物
function ComplexCustomOverlay(point, text, num,siteText) {
    this._point = point;
    this._text = text;
    this._overText = num;
    this._siteText =  siteText;
}
//任务12859
function getBoundary(name) {
    var bdary = new BMap.Boundary();

    // 判断是否是选择的基础单元，然后先将整个基础单元的信息给移除，再新增加周边基础单元信息
    var allOverlay = baiduMap.map.getOverlays();
    for(var i = 0; i < allOverlay.length; i++) {
        if(allOverlay[i]._text == undefined) {
            baiduMap.map.removeOverlay(allOverlay[i]);

            // 这里一定要继续删除下一个！不然是不能把图标给删除的！至于什么原因，打印一下allOverlay即可明了
        }

    }

    bdary.get(name, function(rs) { //获取行政区域
        var count = rs.boundaries.length; //行政区域的点有多少个
        if(count != 0) {
            //			alert('未能获取当前输入行政区域');
            var pointArray = [];
            for(var i = 0; i < count; i++) {
                var ply = new BMap.Polygon(rs.boundaries[i], {
                    strokeWeight: 2,
                    strokeColor: "#30a2f1",
                    strokeOpacity: 1,
                    fillColor: "#30a2f1",
                    fillOpacity: 0.15
                }); //建立多边形覆盖物
                baiduMap.map.addOverlay(ply); //添加覆盖物
                pointArray = pointArray.concat(ply.getPath());
            }
            var allOverlay = baiduMap.map.getOverlays();
            console.log(allOverlay)
        }


    });
}


baiduMap.init();

//划 地铁线
//1号线
polyline[1] = new qq.maps.Polyline({
    path: a(path1),
    strokeColor: new qq.maps.Color(219,65,63,1),
    strokeWeight: 5,
    editable:false,
    map: baiduMap.map
});
//2号线
polyline[2] = new qq.maps.Polyline({
    path: a(path2),
    strokeColor: new qq.maps.Color(250,109,21,1),
    strokeWeight: 5,
    editable:false,
    map: baiduMap.map
});
//4号线
polyline[6] = new qq.maps.Polyline({
    path: a(path6),
    strokeColor: new qq.maps.Color(148,100,214,1),
    strokeWeight: 5,
    editable:false,
    map: baiduMap.map
});
//6号线
polyline[9] = new qq.maps.Polyline({
    path: a(path9),
    strokeColor: new qq.maps.Color(242,180,11,1),
    strokeWeight: 5,
    editable:false,
    map: baiduMap.map
});
//9号线
polyline[4] = new qq.maps.Polyline({
    path: a(path4),
    strokeColor: new qq.maps.Color(72,127,226,1),
    strokeWeight: 5,
    editable:false,
    map: baiduMap.map
});
//10号线
polyline[7] = new qq.maps.Polyline({
    path: a(path7),
    strokeColor: new qq.maps.Color(68,173,75,1),
    strokeWeight: 5,
    editable:false,
    map: baiduMap.map
});


//点击右上角1号线
function choose_one_line() {
    baiduMap.map.zoomTo(11)//设置缩放层级


    setTimeout(() => {
        baiduMap.SetMap1(41.800484,123.377081,baiduMap.maxRegion); //1号线 铁西广场站中心点坐标
        baiduMap.canSeeArea();
        //设置地铁线 透明度
        polyline[1].setStrokeColor(new qq.maps.Color(219,65,63,1));
        polyline[2].setStrokeColor(new qq.maps.Color(250,109,21,0.1));
        polyline[4].setStrokeColor(new qq.maps.Color(148,100,214,0.1));
        polyline[6].setStrokeColor(new qq.maps.Color(242,180,11,0.1));
        polyline[9].setStrokeColor(new qq.maps.Color(72,127,226,0.1));
        polyline[7].setStrokeColor(new qq.maps.Color(68,173,75,0.1));
        baiduMap.lineOrSubOrStation =1;
        baiduMap.subWayList.ActiveId =1;
        baiduMap.clearAllLabels(); //清除覆盖物
        baiduMap.newHouseList();

        baiduMap.clearAaaallSubway()
    },100)
}
//点击右上角2号线
function choose_two_line(){
    baiduMap.map.zoomTo(11)//设置缩放层级

    setTimeout(() => {
        //设置地铁线 透明度
        polyline[1].setStrokeColor(new qq.maps.Color(219,65,63,0.1));
        polyline[2].setStrokeColor(new qq.maps.Color(250,109,21,1));
        polyline[4].setStrokeColor(new qq.maps.Color(148,100,214,0.1));
        polyline[6].setStrokeColor(new qq.maps.Color(242,180,11,0.1));
        polyline[9].setStrokeColor(new qq.maps.Color(72,127,226,0.1));
        polyline[7].setStrokeColor(new qq.maps.Color(68,173,75,0.1));

        baiduMap.SetMap1(41.823001,123.442579,baiduMap.maxRegion); //二号线 沈阳北站中心点坐标
        baiduMap.canSeeArea();
        baiduMap.lineOrSubOrStation =1;
        baiduMap.subWayList.ActiveId =2;
        baiduMap.clearAllLabels(); //清除覆盖物
        baiduMap.newHouseList();

        baiduMap.clearAaaallSubway()
    },100)
}
//点击右上角4号线
function choose_four_line(){
    baiduMap.map.zoomTo(11)//设置缩放层级
    setTimeout(() => {
        //设置地铁线 透明度
        polyline[1].setStrokeColor(new qq.maps.Color(219,65,63,0.1));
        polyline[2].setStrokeColor(new qq.maps.Color(250,109,21,0.1));
        polyline[4].setStrokeColor(new qq.maps.Color(148,100,214,0.1));
        polyline[6].setStrokeColor(new qq.maps.Color(242,180,11,1));
        polyline[9].setStrokeColor(new qq.maps.Color(72,127,226,0.1));
        polyline[7].setStrokeColor(new qq.maps.Color(68,173,75,0.1));

        baiduMap.SetMap1(41.795847,123.411979,baiduMap.maxRegion); //4号线  太原街站为中心
        baiduMap.canSeeArea();
        baiduMap.lineOrSubOrStation =1;
        baiduMap.subWayList.ActiveId =6;
        baiduMap.clearAllLabels(); //清除覆盖物
        baiduMap.newHouseList(); //地图右侧站点、在售套数

        baiduMap.clearAaaallSubway()
    },100)
}
//点击右上角6号线
function choose_six_line(){
    baiduMap.map.zoomTo(11)//设置缩放层级
    setTimeout(() => {
        //设置地铁线 透明度
        polyline[1].setStrokeColor(new qq.maps.Color(219,65,63,0.1));
        polyline[2].setStrokeColor(new qq.maps.Color(250,109,21,0.1));
        polyline[4].setStrokeColor(new qq.maps.Color(148,100,214,0.1));
        polyline[6].setStrokeColor(new qq.maps.Color(242,180,11,0.1));
        polyline[9].setStrokeColor(new qq.maps.Color(72,127,226,1));
        polyline[7].setStrokeColor(new qq.maps.Color(68,173,75,0.1));

        baiduMap.SetMap1(41.777529,123.460326,baiduMap.maxRegion); //6号线	南塔站为中心
        baiduMap.canSeeArea();
        baiduMap.lineOrSubOrStation =1;7
        baiduMap.subWayList.ActiveId =9;
        baiduMap.clearAllLabels(); //清除覆盖物
        baiduMap.newHouseList(); //地图右侧站点、在售套数

        baiduMap.clearAaaallSubway()
    },100)

}
//点击右上角9号线
function choose_nine_line(){
    baiduMap.map.zoomTo(11)//设置缩放层级
    setTimeout(() => {
        //设置地铁线 透明度
        polyline[1].setStrokeColor(new qq.maps.Color(219,65,63,0.1));
        polyline[2].setStrokeColor(new qq.maps.Color(250,109,21,0.1));
        polyline[4].setStrokeColor(new qq.maps.Color(148,100,214,1));
        polyline[6].setStrokeColor(new qq.maps.Color(242,180,11,0.1));
        polyline[9].setStrokeColor(new qq.maps.Color(72,127,226,0.1));
        polyline[7].setStrokeColor(new qq.maps.Color(68,173,75,0.1));


        baiduMap.SetMap1(41.797871,123.439677,baiduMap.maxRegion); //9号线   青年大街地铁站为中心
        baiduMap.canSeeArea();
        baiduMap.lineOrSubOrStation =1;
        baiduMap.subWayList.ActiveId =4;
        baiduMap.clearAllLabels(); //清除覆盖物
        baiduMap.newHouseList(); //地图右侧站点、在售套数

        baiduMap.clearAaaallSubway()
    },100)

}
//点击右上角10号线
function choose_ten_line(){
    baiduMap.map.zoomTo(11)//设置缩放层级
    setTimeout(() => {
        //设置地铁线 透明度
        polyline[1].setStrokeColor(new qq.maps.Color(219,65,63,0.1));
        polyline[2].setStrokeColor(new qq.maps.Color(250,109,21,0.1));
        polyline[4].setStrokeColor(new qq.maps.Color(148,100,214,0.1));
        polyline[6].setStrokeColor(new qq.maps.Color(242,180,11,0.1));
        polyline[9].setStrokeColor(new qq.maps.Color(72,127,226,0.1));
        polyline[7].setStrokeColor(new qq.maps.Color(68,173,75,1));

        baiduMap.SetMap1(41.815028,123.494855,baiduMap.maxRegion); //10号线   滂江站为中心
        baiduMap.canSeeArea();
        baiduMap.lineOrSubOrStation =1;
        baiduMap.subWayList.ActiveId =7;
        baiduMap.clearAllLabels(); //清除覆盖物
        baiduMap.newHouseList(); //地图右侧站点、在售套数

        baiduMap.clearAaaallSubway()
    },100)

}


//----------
//百度经纬度 转 腾讯
function bdMapToTencentMap(lng, lat) {
    var x_pi = 3.14159265358979324 * 3000.0 / 180.0;
    var x = lng - 0.0065;
    var y = lat - 0.006;
    var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
    var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
    var qqLng = z * Math.cos(theta);
    var qqLat = z * Math.sin(theta);
    return {
        lng: qqLng,
        lat: qqLat
    };
}
function a(t){
    let d = [];
    t.forEach(function(latLng) {
        let tencentCoord = bdMapToTencentMap(latLng[0],latLng[1])
        let swappedLatLng = new qq.maps.LatLng(tencentCoord.lat,tencentCoord.lng)
        d.push(swappedLatLng);
    });
    return d
}