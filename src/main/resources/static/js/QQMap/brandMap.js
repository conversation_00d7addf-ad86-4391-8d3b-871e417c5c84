var bdMap = {
    id:0,
    houselng: 0,
    houselat: 0,
    radius: 1000, //搜索半径
    suofa: 15,//缩放比例
    openInfoList: [],//数据集合
    bdmap: undefined,
    bdpoit: {},
    bdtitle: "",
    bdcontent: "",
    address: "",
    city: "沈阳",
    switchKanfang: true,
    allLabels:[],
    init: function (divid, entity) {
        bdMap.houselng = entity.houselng;
        bdMap.houselat = entity.houselat;
        bdMap.radius = entity.radius;
        bdMap.suofa = entity.suofa;
        bdMap.bdtitle = entity.bdtitle;
        bdMap.bdcontent = entity.bdcontent;
        bdMap.address = entity.address;
        bdMap.city = entity.city;
        bdMap.id = entity.id;

        if (bdMap.houselng * 1 == 0 || bdMap.houselat * 1 == 0) {
            bdMap.AddressToPoint(bdMap.address, bdMap.city, divid);//没有坐标的情况下从地址你想解析坐标
        }
        else {
            bdMap.showMap(divid);
        }

    },
    AddressToPoint: function (address, city, divid) {
        var myGeo = new BMap.Geocoder();
        // 将地址解析结果显示在地图上,并调整地图视野
        myGeo.getPoint(address, function (point) {
            if (point) {
                bdMap.houselng = point.lng;
                bdMap.houselat = point.lat;
                bdMap.showMap(divid);
            }
        }, city);
    },
    showMap: function (divid) {
        bdMap.bdmap = new qq.maps.Map(document.getElementById(divid), {
            center: new qq.maps.LatLng(bdMap.houselat, bdMap.houselng),      // 地图的中心地理坐标。
            zoom:bdMap.suofa-1,                                                // 地图的中心地理坐标。
            panControl: true,         //平移控件的初始启用/停用状态。
            zoomControl: true,       //缩放控件的初始启用/停用状态。
            scaleControl: true,       //滚动控件的初始启用/停用状态。
            mapTypeControl: false,
        })
        bdMap.searechHouse()
    },
    addnewmarker: function (data) {
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            div.style.position = "absolute";
            div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
            div.style.backgroundColor ="rgba(255, 82,0, 0.9)";
            div.style.color = "white";
            div.style.height = "18px";
            div.style.fontSize="14px";
            div.style.borderRadius="4px";
            div.style.padding = "8px 10px";
            div.style.lineHeight = "18px";
            div.style.whiteSpace = "nowrap";
            div.style.MozUserSelect = "none";
            div.style.fontSize = "12px";
            var span = this._span = document.createElement("span");
            div.appendChild(span);
            span.appendChild(document.createTextNode(this._text));
            var that = this;

            var arrow = this._arrow = document.createElement("div");
            arrow.style.background = "url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png) no-repeat";
            arrow.style.position = "absolute";
            arrow.style.width = "16px";
            arrow.style.height = "10px";
            arrow.style.top = "34px";
            arrow.style.left = "22px";
            arrow.style.overflow = "hidden";
            div.appendChild(arrow);
            bdMap.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(this._point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(data.lng, data.lat), data.title, data.content);
        bdMap.bdmap.addOverlay(myCompOverlay);
        if (bdMap.id && bdMap.id > 0) {
        }

    },
    addnewmarkerHouse: function (data,show,i) {
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            //div.attr("class", "bubble-2 bubble");
            div.style.position = "absolute";
            div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
            div.style.backgroundColor = "rgba(255, 82,0, 0.9)";
            div.className="showHousePrice"
            // div.style.border = "1px solid #BC3B3A";
            div.style.color = "white";
            div.style.height = "18px";
            div.style.fontSize="14px";
            div.style.borderRadius="4px";
            div.style.padding = "8px 10px";
            div.style.lineHeight = "18px";
            div.style.whiteSpace = "nowrap";
            div.style.MozUserSelect = "none";
            div.style.fontSize = "12px";
            var span = this._span = document.createElement("span");
            div.appendChild(span);
            span.appendChild(document.createTextNode(this._text));

            var price = show.mPrice;
            if(price == null && show.prType!=1){
                var pNames = document.createElement("label");
                pNames.appendChild(document.createTextNode("待售"));
                pNames.className="forSale"
                div.style.backgroundColor = "rgba(51,51,51, 0.9)";
                span.appendChild(pNames);
                div.className="showHousePrice showHousePrice_sun"
            }
            if(show.projectStatus == 3){
                div.style.backgroundColor = "rgba(128,153,175, 0.9)";
                div.style.zIndex = "-99999999999";
            }
            var that = this;

            var arrow = this._arrow = document.createElement("div");
            arrow.style.background = "url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png) no-repeat";
            arrow.style.position = "absolute";
            var showStatus = show.projectStatus;
            if(show.prType!=1){
                showStatus = 4;
            }
            arrow.className="showHouseJiao"+showStatus;
            arrow.style.width = "16px";
            arrow.style.height = "10px";
            arrow.style.top = "34px";
            arrow.style.left = "50%";
            arrow.style.marginLeft="-8px"
            arrow.style.overflow = "hidden";
            var kanfang =  document.createElement("div");
            kanfang.className="kanfang";
            kanfang.id="kanfang"+i
            kanfang.style.display="none"
            var x= document.createElement("img")
            x.src="https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian3.png"
            x.className="jiao"
            var fang = document.createElement("dl");
            var fang1 = document.createElement("dt");
            fang1.style.fontSize="16px";
            var projectUrl = document.createElement("a");
            projectUrl.appendChild(document.createTextNode(show.projectName))
            projectUrl.setAttribute("href", "https://sy.fangxiaoer.com/house/"+show.projectId+"-"+show.projectType+".htm");
            projectUrl.setAttribute("target","_blank");
            var projectState =document.createElement("span");
            var projectValue ="在售";
            if(show.projectStatus == 2) {
                projectValue = "待售";
            }else if(show.projectStatus == 3){
                projectValue = "售罄";
            }
            projectState.appendChild(document.createTextNode(projectValue));
            projectState.className = "zt" + show.projectStatus;
            var kanfangX=document.createElement("div");
            kanfangX.className="kanfangX";
            var fang2 = document.createElement("dd");
            var imgOutSide = document.createElement("a");
            imgOutSide.setAttribute("href", "https://sy.fangxiaoer.com/house/"+show.projectId+"-"+show.projectType+".htm");
            imgOutSide.setAttribute("target","_blank");
            var img = document.createElement("img");
            img.src=show.ImageUrl
            imgOutSide.appendChild(img);
            var infoOutSie = document.createElement("a");
            infoOutSie.setAttribute("href", "https://sy.fangxiaoer.com/house/"+show.projectId+"-"+show.projectType+".htm");
            infoOutSie.setAttribute("target","_blank");
            infoOutSie.className="infosupera";
            // var img1=document.createElement("img")
            // img1.src="https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian6.jpg"
            var ul = document.createElement("ul");
            var li1 = document.createElement("li");
            if(show.mPrice== null){
                li1.innerHTML="<p><span>"+"待定"+"</span></p>"
            }else{
                li1.innerHTML="<p><span>"+parseInt(show.mPrice.priceMoney)+"</span>元/㎡</p>"
            }
            var li2 = document.createElement("li");
            if(show.minArea!=null){
                li2.innerHTML=show.layout+" - "+show.minArea+"~"+show.maxArea+"㎡"
            }else{
                li2.style.height="26px"
            }

            var li3 = document.createElement("li");
            li3.innerHTML="咨询：<b>"+show.sortelTel+"</b>";
            infoOutSie.appendChild(li1)
            infoOutSie.appendChild(li2)
            infoOutSie.appendChild(li3)
            ul.appendChild(infoOutSie)
            // if(show.projectStatus != 3){
            //     ul.appendChild(img1)
            // }
            fang2.appendChild(imgOutSide)
            fang2.appendChild(ul)
            fang1.appendChild(projectUrl)
            fang1.appendChild(projectState)
            fang1.appendChild(kanfangX);
            fang.appendChild(fang1);
            fang.appendChild(fang2);
            kanfang.appendChild(fang);
            div.appendChild(arrow);
            kanfang.appendChild(x);
            div.appendChild(kanfang);
            div.onmouseover = function() {
                if(bdMap.switchKanfang){
                    div.style.zIndex="10000000";
                }
            }
            div.onmouseout = function() {
                if(bdMap.switchKanfang) {
                    if (show.projectStatus == 3) {
                        div.style.zIndex = "-9999999999";
                    } else {
                        div.style.zIndex = "9999999";
                    }
                }
            }
            bdMap.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(this._point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(data.lng, data.lat), data.title, data.content);
        bdMap.bdmap.addOverlay(myCompOverlay);
        if (bdMap.id && bdMap.id > 0) {
            if(show.prType == 1){
                myCompOverlay.addEventListener("click", function () {
                    bdMap.switchKanfang = false;
                    $(".kanfang").hide()
                    $(".showHousePrice ").removeClass("hover")
                    $(".showHousePrice").css("z-index","9999999")
                    document.getElementById("kanfang"+i).style.display="block"
                    document.getElementById("kanfang"+i).parentNode.style.zIndex="999999999";
                    bdMap.SetMap(data.lng, data.lat)
                    $(".map_dl dd").removeClass("hover")
                    $(".map_dl dd").each(function(){
                        if( $(this).find("i").text()==i){
                            $(this).addClass("hover")
                        }
                    })
                });
            }
        }
    },
    //周边楼盘
    searechHouse:function(){
        $.ajax({
            type:'post',
            url:"/viewBrandMap",
            data:{
                brandId:bdMap.id,
            },
            success:function (data) {
                var data = data.content
                var html = "";
                var price = "";
                for(var i =data.length-1 ;i>=0;i--){
                    price = data[i].mPrice;
                    if(price == null){
                        price = data[i].prType==1?'价格：待定':'';
                    }else {
                        price = data[i].mPrice.priceMoney+'元/㎡'
                    }
                }

                bdMap.addRegionLabel(data)
                $(".map_dl").html(html);
            }
        })
    },
    //添加显示面板
    openInfoWindow: function (searchInfo) {
        var searchInfoWindow = new BMapLib.SearchInfoWindow(bdMap.bdmap, "<p style='width:355px;font:bold 14px/16px arial,sans-serif;margin:0;color:#333;font-weight: 400;white-space:nowrap;overflow:hidden'>"+searchInfo.address+"</p>", {
            title: "<p style='width: 355px;font: bold 14px/16px arial,sans-serif;margin: 0;color: #333;white-space: nowrap;overflow: hidden;font-size: 16px;line-height: 40px;' title='" + searchInfo.title + "'>" + searchInfo.title + "</p>", //标题
            width: 350, //宽度
            panel: "panel", //检索结果面板
            enableAutoPan: false, //自动平移
            searchTypes: [
            ]
        }); bdMap.SetMap(searchInfo.lng, searchInfo.lat)
        searchInfoWindow.open(new BMap.Point(searchInfo.lng, searchInfo.lat));

    },
    //设置地图中心点
    SetMap: function ( lat,lng) {
        var  bdpoit = new BMap.Point( parseFloat(lat)+0.0025,lng);
        bdMap.bdmap.panTo(bdpoit); //将地图移动到点
    },
    //查询结果绑定
    select: function (results) {
        bdMap.openInfoList = [];
        bdMap.bdmap.clearOverlays();
        bdMap.bdmap.centerAndZoom(bdMap.bdpoit, bdMap.suofa);
        bdMap.addnewmarker({ lng: bdMap.houselng, lat: bdMap.houselat, title: bdMap.bdtitle, content: bdMap.bdcontent });
        local.disableFirstResultSelection();
        // 判断状态是否正确
        if (local.getStatus() == BMAP_STATUS_SUCCESS) {
            var s = [];
            for (var i = 0; i < results.getCurrentNumPois() ; i++) {

                var entity = {};
                entity.title = results.getPoi(i).title;
                entity.address = results.getPoi(i).address;
                entity.phoneNumber = results.getPoi(i).phoneNumber;
                entity.point = results.getPoi(i).point;
                entity.detailUrl = results.getPoi(i).detailUrl;
                entity.url = results.getPoi(i).url;
                var pointA = new BMap.Point(bdMap.houselng, bdMap.houselat);  // 创建点坐标A
                var pointB = new BMap.Point(results.getPoi(i).point.lng, results.getPoi(i).point.lat);  // 创建点坐标B
                entity.distance = (bdMap.bdmap.getDistance(pointA, pointB)).toFixed(2);  //获取两点距离,保留小数点后两位
                bdMap.openInfoList.push(entity);

            }
            //排序对象集合
            var list = bdMap.openInfoList.sort(function (a, b) {
                return a.distance - b.distance;
            });
            var html = "";
            for (var i = 0; i < list.length; i++) {
                html += "<dd onclick='bdMap.openInfoWindow({title:\"" + list[i].title + "\",address:\"" + list[i].address + "\",lng:" + list[i].point.lng + ",lat:" + list[i].point.lat + "})'><a title='" + list[i].title + "'><i>"+(i+1)+"</i><span>" + list[i].distance + "米</span>" + list[i].title + "</a></dd>";
            }
            $(".map_dl").html(html);
        }
    },
    //---------------
    // 创建标记
    addRegionLabel(data){
        let areaStyle={
            color: '#FFF',
            fontSize: '14px',
            borderRadius: '4px',
            backgroundColor: "rgba(255, 82, 0, 0.9)",
            border: 'none',
            textAlign: 'center',
            alignment: 'center',
            height:'18px',
            padding:"8px 10px",
            lineHeight: "18px",
        }
        let sellOutStyle = {
            color: '#FFF',
            fontSize: '14px',
            borderRadius: '4px',
            backgroundColor: "rgba(128, 153, 175, 0.9)",
            border: 'none',
            textAlign: 'center',
            alignment: 'center',
            height:'18px',
            padding:"8px 10px",
            lineHeight: "18px",
        }
        let mouseoverSetStyle = {
            color: '#FFF',
            fontSize: '14px',
            borderRadius: '4px',
            backgroundColor: "rgba(42, 149, 254, 0.9)",
            border: 'none',
            textAlign: 'center',
            alignment: 'center',
            height:'18px',
            padding:"8px 10px",
            lineHeight: "18px",
        }
        let content

        data.forEach(function(item) {
            let statusText,activeStyle

            if(item.projectStatus=='1'){
                statusText="在售"
                activeStyle = areaStyle
            }else if(item.projectStatus=='2'){
                statusText="待售"
                activeStyle = areaStyle
            }else if(item.projectStatus=='3'){
                statusText="售罄"
                activeStyle = sellOutStyle
            }

            content = `<p>${item.projectName}  ${item.mPrice== null?'价格: 待定':item.mPrice.priceMoney+'元/㎡'}</p><div class="t${item.projectId} sanjiao sanjiao${item.projectStatus}"></div>
                <div class="kanfang" id="k${item.projectId}">
                    <dl>
                        <dt style="font-size: 16px;" class="text_left">
                            <a href="https://sy.fangxiaoer.com/house/${item.projectId}-${item.prType}.htm" target="_blank" >${item.projectName}</a>
                            <span class="zt${item.projectStatus}">${statusText}</span>
                            <div class="kanfangX"></div>
                        </dt>
                        <dd class="text_left">
                            <a href="https://sy.fangxiaoer.com/house/${item.projectId}-${item.prType}.htm" target="_blank">
                                <img src="${item.ImageUrl}">
                            </a>
                            <ul>
                                <a href="https://sy.fangxiaoer.com/house/${item.projectId}-${item.prType}.htm" target="_blank" class="infosupera text_center">
                                    <li>
                                        <p><span>${item.mPrice!=null?item.mPrice.priceMoney+'元/㎡':'待定'}</span></p>
                                    </li>
                                    <li>${item.layout} - ${item.minArea}~${item.maxArea}㎡</li>
                                    <li v-if="${item.sortelTel}">咨询：<b>${item.sortelTel}</b></li>
                                </a>
                            </ul>
                        </dd>
                    </dl>
                    <img src="https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian3.png" class="jiao">
                </div>`

            var label = new qq.maps.Label({
                position: new qq.maps.LatLng(item.latitude,item.longitude),
                map: bdMap.bdmap,
                content: content,
                style: activeStyle,
                item: item
            });
            qq.maps.event.addDomListener(label, 'click', function(e) {
                data.forEach(function(item) {
                    $("#k"+item.projectId).hide()
                });
                $("#k"+label.item.projectId).show()
                bdMap.bdmap.setCenter(new qq.maps.LatLng(label.item.latitude,label.item.longitude))//设置中心点
            });
            //鼠标移入`
            qq.maps.event.addDomListener(label, 'mouseover', function() {
                label.setStyle(mouseoverSetStyle);
                $(".t"+label.item.projectId).css("background",'url("https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian7.png") no-repeat')
                $(this).css("zIndex","9")
            });
            //鼠标移出
            qq.maps.event.addDomListener(label, 'mouseout', function() {
                if(label.item.projectStatus=='3'){
                    label.setStyle(sellOutStyle)
                    $(".t"+label.item.projectId).css("background",'url("https://static.fangxiaoer.com/web/images/brand/Gray.png") no-repeat')
                }else{
                    label.setStyle(areaStyle)
                    $(".t"+label.item.projectId).css("background",'url("https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png") no-repeat')
                }
                $(this).css("zIndex","")
            });
            bdMap.allLabels.push(label);
        });
    }
};

$(document).on("click",".kanfangX",function(){
    bdMap.switchKanfang = true;
    $(".kanfang").hide();
    $(".map_dl dd").removeClass("hover")
})
$(document).on("click",".kanfang dd ul>img",function(){
    //修改地图点击看房车的项目名
    $("#projectName_for_freeCar").val($(this).parent().parent().prev().find("a").text())
    showUsercode(3);
})
$(".map_dl dd").live("click",function(){
    $(".map_dl dd").removeClass("hover")
    $(this).addClass("hover")
    $(".showHousePrice ").removeClass("hover")
})
function ComplexCustomOverlay(point, text, mouseoverText) {
    this._point = point;
    this._text = text;
    this._overText = mouseoverText;
}

