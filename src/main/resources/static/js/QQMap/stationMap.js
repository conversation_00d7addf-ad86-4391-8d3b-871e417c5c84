/**
 * Created by Administrator on 2018/11/9.
 */
Vue.config.productionTip = false;
var baiduMap = new Vue({
    el: '#map',
    data: {
        houseType:type,//地图类型 1  新房            2  二手房            3 租房
        map: {},
        mapZoom:12,//初始化级别
        maxRegion:14,//地图区域和房源分界线  二手房租房 区域和板块的分界线   新接口大于等于14(传type=1展示项目,不传展示区域),小于14展示区域;
        maxPlat:15,//地图板块和房源分界线
        gapZoom:-1,//与级别的差
        lngRight: "", //四个坐标点
        lngLeft: "",
        latTop: "",
        latBottom: "",
        isSchool: "", //是不是学区房
        isSubWay: "1", //是不是地铁房
        isPayment: "", //是不是低首付
        isExistinfo: "", //是不是现房
        rentType:1,//租赁类型
        scroll:true,//加载开关
        housingEstate:"",//小区id
        page:1,//列表页
        zoomType:1,//地图显示级别  1区域  2 板块  3房源
        navIsActive: 4, //左侧菜单选中项
        windowWidth: window.innerWidth, //
        height: window.innerHeight-50, // //50是主导航栏高度
        listwidth: 488, //左侧列表宽度
        showSelectList: -1, //筛选项当前显示的是哪一个  -1代表没有列表显示
        regionList: { //区域
            ActiveText: '区域',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        typeList: { //新房类型 二手房户型
            ActiveText: '类型',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        priceList: { //新房价格区间 二手房总价
            ActiveText: '价格',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        schoolList: { //学校类型
            ActiveText: '类别',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        subWayList: { //地铁线
            ActiveText: '地铁',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        areaList: { //面积
            ActiveText: '面积',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        subWayStationText: { //地铁站
            ActiveText: '',
            ActiveId: "",
        },
        salesStatus:{
            ActiveText: '状态',
            ActiveId: "",
            content: [{
                id: "1",
                name: '在售'
            },{
                id: "2",
                name: '待售'
            },{
                id: "3",
                name: '售罄'
            }]
        },
        salesType:"",//销售状态
        newHousList: [], //房源列表
        decidedType:"1",//对应接口中tab 其中不填或者填0则由接口提供返回zoomlevel，填其他则由客户端js处理；
        searchValue:"",
        projectId:"",
        searchZoomLevel:18,
        sreachResultType:true,//用来控制左侧表单的文字；
        houseListheigth:window.innerHeight-50,
        searchModel:false,//用来控制是否是搜索模式，用于控制站点是否为常立
        houseSwitch:false,//房源展示开关 ture房源展示 false房源不展示
        subwaySwitchFlag:false,//下拉切换线路开关
        allLabels:[],
        allSubway:[],
        readyNum:0,
        playList:[],
        addStationsList:[],
        addStationsProjectList:[],
        clickStationName:'',
        clickStationla:'',
        clickStationlo:'',
        searchbtnType:false,
    },
    computed: {
        mapWidth: function() {
            var mapWidth;
            if(this.houseType==2){
                mapWidth=this.windowWidth - this.listwidth
            }else{
                mapWidth=this.windowWidth - 60 - this.listwidth
            }
            return mapWidth
        },
        recommentSwitch:function () {
            if(this.newHousList == ''||this.newHousList == undefined||this.newHousList.length == 0){
                return "block";
            }else {
                return "none";
            }
        }
    },
    methods: {
        //初始化
        init: function() {
            //区分新房二手房租房的细微差别
            baiduMap.typeList.ActiveText=baiduMap.houseType==1?"类型":"户型"
            baiduMap.listwidth=baiduMap.houseType==1?488:530;

            baiduMap.map = new qq.maps.Map(document.getElementById("baiduMap"), {
                center: new qq.maps.LatLng(41.798142, 123.439941),      // 地图的中心地理坐标。
                zoom:this.mapZoom,                                                // 地图的中心地理坐标。
                panControl: false,         //平移控件的初始启用/停用状态。
                zoomControl: false,       //缩放控件的初始启用/停用状态。
                scaleControl: true,       //滚动控件的初始启用/停用状态。
                mapTypeControl: false,
            })

            // 监听地图的 idle 事件，即加载完成
            qq.maps.event.addDomListener(baiduMap.map, 'idle', function() {
                // baiduMap.canSeeArea();//可视区域
                baiduMap.showHouse() //初始化第一次获取地图数据

            });



            if(baiduMap.houseType==1){//新房
                //初始化 下拉选择区域
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getRegionFilter", baiduMap.regionList)
                //初始化  下拉选择价格
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getNewPriceFilter", baiduMap.priceList)
                //初始化  下拉选择房源类型
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/projectTypeFilter", baiduMap.typeList)
                //初始化  下拉选择学区类型
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getSchoolTypeFilter", baiduMap.schoolList)
                //初始化  下拉选择地铁
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getSubWayFilter", baiduMap.subWayList)
            }

            //窗口大小变化重新计算高度
            window.onresize = function(){
                baiduMap.height=window.innerHeight-50;
                baiduMap.setHouseHigth();
            }

            /*//区分新房二手房租房的细微差别
            baiduMap.typeList.ActiveText=baiduMap.houseType==1?"类型":"户型"
            baiduMap.listwidth=baiduMap.houseType==1?488:530;
            baiduMap.map = new BMap.Map("baiduMap",{enableMapClick:false,minZoom:10});//创建地图并且禁用弹出信息框
            var point = new BMap.Point(123.439941, 41.798142); //中心点
            baiduMap.map.centerAndZoom(point,baiduMap.mapZoom); // 编写自定义函数，创建标注
            baiduMap.map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
            baiduMap.showHouse() //初始化第一次获取地图数据
            baiduMap.map.addControl(new BMap.ScaleControl());
            baiduMap.map.addEventListener("dragend", function() {
                baiduMap.page=1;
                baiduMap.housingEstate="";
                if(baiduMap.map.getZoom() >= baiduMap.maxRegion){
                    baiduMap.regionList.ActiveId='';
                    baiduMap.regionList.ActiveText="区域";
                }
                if(baiduMap.projectId == ""){
                    baiduMap.subWayStationText.ActiveId = "";
                    baiduMap.subWayStationText.ActiveText = "";
                }
                baiduMap.showHouse();
                baiduMap.showSelectList=-1;
                baiduMap.sreachResultType = false;
                baiduMap.setHouseHigth();
            })
            //缩放时重新获取数据
            baiduMap.map.addEventListener("zoomend", function() {
                if(baiduMap.map.getZoom() <= baiduMap.maxRegion){
                    baiduMap.decidedType='1';
                }else if(baiduMap.subWayList.ActiveId != ""||baiduMap.subWayStationText.ActiveId != ""){
                    baiduMap.decidedType='0';
                }
                baiduMap.housingEstate="";
                baiduMap.page=1;
                baiduMap.showHouse();
                baiduMap.showSelectList=-1;
                baiduMap.sreachResultType = false;
                baiduMap.setHouseHigth();
            })
            if(baiduMap.houseType==1){//新房
                //初始化 下拉选择区域
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getRegionFilter", baiduMap.regionList)
                //初始化  下拉选择价格
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getNewPriceFilter", baiduMap.priceList)
                //初始化  下拉选择房源类型
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/projectTypeFilter", baiduMap.typeList)
                //初始化  下拉选择学区类型
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getSchoolTypeFilter", baiduMap.schoolList)
                //初始化  下拉选择地铁
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getSubWayFilter", baiduMap.subWayList)
            }

            //窗口大小变化重新计算高度
            window.onresize = function(){
                baiduMap.height=window.innerHeight-50;
                baiduMap.setHouseHigth();
            }
            //滚动加载
            window.document.getElementsByClassName("HouseList")[0].addEventListener("scroll",winScroll);
            function winScroll(){
                var scrTop = window.document.getElementsByClassName("HouseList")[0].scrollTop;
                var windoweight;
                if($(".Choice").length>0){
                    windoweight = (window.document.getElementsByClassName("HouseList")[0].getElementsByTagName("ul")[0].offsetHeight-(baiduMap.height-153));
                }else{
                    windoweight = (window.document.getElementsByClassName("HouseList")[0].getElementsByTagName("ul")[0].offsetHeight-(baiduMap.height-106));
                }
                if(scrTop== windoweight){
                    baiduMap.scroll=false;
                    baiduMap.page += 1;
                    if(!(baiduMap.map.getZoom()>baiduMap.maxRegion)){
                        document.getElementById("loading").style.display="block"
                    }
                    baiduMap.newHouseListRegionAjax();
                }
            }
            if(baiduMap.isSubWay!=""){
                for(var i=0;i<polyline.length;i++){
                    if(polyline[i]!=""  && polyline[i]!=null){
                        baiduMap.map.addOverlay(polyline[i]);
                    }
                }
            }*/
            baiduMap.readyNum = 1
        },
        //设置地图中心点
        SetMap: function ( lat,lng, size) {
            // baiduMap.map.setZoom(size);
            // baiduMap.map.bdpoit = new BMap.Point(lng, lat);
            // baiduMap.map.panTo(baiduMap.map.bdpoit); //将地图移动到点
            baiduMap.map.zoomTo(size)
            baiduMap.map.setCenter(new qq.maps.LatLng(lat,lng))
            baiduMap.showHouse()
        },
        //设置地图中心点
        SetMap1: function ( lat,lng, size) {
            baiduMap.map.zoomTo(size)
            baiduMap.map.setCenter(new qq.maps.LatLng(lat,lng))

            // baiduMap.map.bdpoit = new BMap.Point(lng, lat);
            // baiduMap.map.centerAndZoom(baiduMap.map.bdpoit, size);
        },
        //构造 覆盖物
        overlay: function(point, text, mouseoverText) {
            this._point = point;
            this._text = text;
            this._overText = mouseoverText;
        },
        //下拉选择不限
        navNormal:function(data,text,salesId){
            if(salesId!= undefined && salesId!= null && salesId!= ''){
                if(salesId==0){
                    baiduMap.salesType=''
                }else{
                    baiduMap.salesType=salesId
                }
            }

            data.ActiveText=text;
            baiduMap.showSelectList=-1;
            data.ActiveId='';
            if(text == "地铁" || text == "地铁不限"){
                baiduMap.subWayStationText.ActiveId = "";
                baiduMap.subWayStationText.ActiveText = "";
                baiduMap.subwaySwitchFlag = false;
            }
            baiduMap.switchSearch();
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
            setLineWeigth("");
        },
        //选择左侧菜单
        navLeft: function(int) {
            switch (int) {
                case 0:
                    window.location.href="/static/houseMap.htm?type=0";
                    break;
                case 1:
                    window.location.href="/static/houseMap.htm?type=1";
                    break;
                case 2:
                    window.location.href="/static/houseMap.htm?type=2";
                    break;
                case 3:
                    window.location.href="/static/houseMap.htm?type=3";
                    break;
                case 4:
                    window.location.href="/static/station_map.htm";
                    break;
                default:
                    break;
            }
        },
        //筛选条件 下拉菜单初始化
        navInit: function(url, nav) {
            ajax({
                type: "POST",
                url: url,
                dataType: "json",
                data: {},
                success: function(data) {
                    nav.content = data.content
                },
                error: function(data) {

                }
            })
        },
        //筛选条件 下拉菜单选择
        navSelect: function(showData, dataName,dataId, s) {
            if(s==0){
                baiduMap.salesType=dataId
            }
            baiduMap.page=1;
            showData.ActiveText = dataName;
            this.showSelectList = -1;
            showData.ActiveId = dataId;
            baiduMap.searchZoomLevel  = baiduMap.maxRegion;
            baiduMap.switchSearch()
            if(showData == baiduMap.subWayList){
                baiduMap.subwaySwitchFlag = true;
                baiduMap.subWayStationText.ActiveId = "";
                baiduMap.subWayStationText.ActiveText = "";
            }
            // baiduMap.showHouse()
        },
        deleteSearchValue: function() {
            this.searchValue = "";
            $("#txtkeys").val("");
            baiduMap.projectId = "";
            baiduMap.switchSearch()
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
            $("#search2017 img").hide();
        },
        deleteAllValue: function() {
            baiduMap.clearAllLabels()
            baiduMap.salesType=''//清空销售状态
            this.searchValue = "";
            $("#txtkeys").val("");
            baiduMap.projectId = "";
            baiduMap.showSelectList=-1;
            this.salesStatus.ActiveText = "状态";
            this.salesStatus.ActiveId = '';
            this.subWayList.ActiveText="地铁";
            this.subWayList.ActiveId='';
            this.schoolList.ActiveText="类别";
            this.schoolList.ActiveId='';
            this.priceList.ActiveText="价格";
            this.priceList.ActiveId='';
            this.typeList.ActiveText="类型";
            this.typeList.ActiveId='';
            this.regionList.ActiveText="区域";
            this.regionList.ActiveId='';
            baiduMap.subWayStationText.ActiveId = "";
            baiduMap.subWayStationText.ActiveText = "";
            baiduMap.searchModel = false;
            baiduMap.houseSwitch = false;
            baiduMap.subwaySwitchFlag = false;
            if(baiduMap.mapZoom != baiduMap.map.getZoom()){
                baiduMap.SetMap1(41.798142, 123.439941,baiduMap.mapZoom);
            }else {
                baiduMap.SetMap1(41.798142, 123.439941,baiduMap.mapZoom);
                baiduMap.showHouse();
            }
            baiduMap.setHouseHigth();
            $("#search2017 img").hide();
            setLineWeigth("");
        },
        setHouseHigth:function () {
            var selectCount = 0;
            if(this.subWayList.ActiveId != ''){
                selectCount = selectCount+1;
            }
            if(this.priceList.ActiveId != ''){
                selectCount = selectCount+1;
            }
            if(this.typeList.ActiveId != ''){
                selectCount = selectCount+1;
            }
            if(this.searchValue != ''){
                selectCount = selectCount+1;
            }
            if(this.subWayStationText.ActiveId != ''){
                selectCount = selectCount+1;
            }
            if(selectCount >3){
                baiduMap.houseListheigth =  window.innerHeight-50 -83;
            }else if(selectCount <=3 && selectCount >0){
                baiduMap.houseListheigth =  window.innerHeight-50 -47;
            }else {
                baiduMap.houseListheigth =  window.innerHeight-50;
            }
        },
        //获取地图房源
        newHouseList: function() {
            //新房获取地图点
            ajax({
                type: "POST",
                url: "/searchStationMap",
                dataType: "json",
                async:false,
                data: {
                    zoomLevel:baiduMap.map.getZoom(),
                    moneyId: baiduMap.priceList.ActiveId,//价格id-----------------------
                    regionId: baiduMap.regionList.ActiveId,//区域id-------------------------
                    subWayStationId: "",//地铁站id
                    subWayId: baiduMap.subWayList.ActiveId,//地铁线id------------------------------
                    latitude: "",//
                    longitude: "",//
                    distanceId: "",//
                    schoolId: "",//学校id
                    schoolTypeId: baiduMap.schoolList.ActiveId,//学校类型id-------------------------
                    projectName: baiduMap.searchValue,//项目名称
                    roomType: "",//户型id
                    isSubWay: baiduMap.isSubWay,//是不是地铁 非空为是
                    isSchoolArea: baiduMap.isSchool,//是不是学区房  非空为是
                    projectType:baiduMap.typeList.ActiveId,//项目类型id
                    payment: baiduMap.isPayment,//baiduMap.loopList.ActiveId,//是不是低首付 1为是!!!!!!!!!!!!
                    existinfo:baiduMap.isExistinfo,//是不是现房  1为是
                    // leftLng:baiduMap.map.getZoom()>=baiduMap.maxRegion?baiduMap.lngLeft:'',//四个坐标点  接口名有错误（错着写是对的！！）
                    // leftLat:baiduMap.map.getZoom()>=baiduMap.maxRegion?baiduMap.latTop:'',
                    // rightLng:baiduMap.map.getZoom()>=baiduMap.maxRegion?baiduMap.lngRight:'',
                    // rightLat:baiduMap.map.getZoom()>=baiduMap.maxRegion?baiduMap.latBottom:'',
                    leftLng:baiduMap.lngLeft,//四个坐标点  接口名有错误（错着写是对的！！）
                    leftLat:baiduMap.latTop,
                    rightLng:baiduMap.lngRight,
                    rightLat:baiduMap.latBottom,
                    tab:"0",
                    projectId:baiduMap.projectId,
                    pageSize:10000,//分页
                    loopline:baiduMap.subWayList.ActiveId,
                    stationId:baiduMap.subWayStationText.ActiveId,
                    projectStates: baiduMap.salesType
                },
                beforeSend: function() {
                },
                success: function(data) {
                    if(data.status == 1) {
                        data = data.content
                        baiduMap.clickStationName = data.stationName
                        baiduMap.clickStationla = data.la
                        baiduMap.clickStationlo = data.lo
                    }
                },
                error: function(data) {
                }
            });
        },
        //地图左侧楼盘列表
        newHouseListRegionAjax:function(){
            ajax({
                type: "POST",
                url: "/searchStationMap",
                async:false,
                dataType: "json",
                data: {
                    moneyId: baiduMap.priceList.ActiveId,//价格id
                    regionId: baiduMap.regionList.ActiveId,//区域id
                    subWayStationId: "",//地铁站id
                    subWayId: baiduMap.subWayList.ActiveId,//地铁线id
                    distanceId: "",//
                    schoolId: "",//学校id
                    schoolTypeId: baiduMap.schoolList.ActiveId,//学校类型id
                    projectName: baiduMap.searchValue,//项目名称
                    roomType: "",//几户型
                    isSubWay: baiduMap.isSubWay,//是不是地铁房
                    isSchoolArea: baiduMap.isSchool,//是不是学区房
                    projectType:baiduMap.typeList.ActiveId,//项目类型
                    page:baiduMap.page,//页数
                    pageSize:10,//分页
                    leftLng: baiduMap.subWayStationText.ActiveId == "" ? baiduMap.lngLeft:'',//四个坐标点  接口名有错误（错着写是对的！！）
                    leftLat:baiduMap.subWayStationText.ActiveId == "" ? baiduMap.latTop:'',
                    rightLng:baiduMap.subWayStationText.ActiveId == "" ? baiduMap.lngRight:'',
                    rightLat:baiduMap.subWayStationText.ActiveId == "" ? baiduMap.latBottom:'',
                    zoomLevel:baiduMap.map.getZoom()>13 ? baiduMap.map.getZoom():14,
                    tab:"0",
                    projectId:baiduMap.projectId,
                    loopline:baiduMap.subWayList.ActiveId,
                    stationId:baiduMap.subWayStationText.ActiveId,
                    projectStates: baiduMap.salesType
                },
                beforeSend: function() {
                },
                success: function(data) {

                    if(data.status == 1) {
                        baiduMap.playList = data.content.projects;
                        baiduMap.addStationsProjectList = data.content.projects;
                        if (baiduMap.playList.length == 0 && baiduMap.page == 1) {
                            if(baiduMap.sreachResultType){
                                $("#recommentT1").text("未找到符合条件的楼盘");
                                $("#recommentT2").text("可填写需求单，专属定制找房。");
                            }else {
                                $("#recommentT1").text("地图范围内没找到楼盘");
                                $("#recommentT2").text("建议您：拖动地图更改位置或填写需求单，专属定制找房。");
                            }
                            $("#recommentOrder").show();
                        } else {
                            $("#recommentOrder").hide();
                        }
                        document.getElementById("loading").style.display = "none";
                        if (baiduMap.page == 1) {
                            baiduMap.newHousList = baiduMap.playList;
                        } else {
                            for (var i = 0; i < baiduMap.playList.length; i++) {//10  是分页大小
                                baiduMap.newHousList.push(baiduMap.playList[i]);
                            }
                            baiduMap.scroll = true;
                        }
                    }
                },
                error: function(data) {
                    document.getElementById("loading").style.display="none";
                }
            });
        },
        //获取地铁站
        subWayStationList: function() {
            ajax({
                type: "POST",
                url: "/searchStationMap",
                dataType: "json",
                data: {
                    subWayStationId: "",//地铁站id
                    subWayId: baiduMap.subWayList.ActiveId,//地铁线id
                    projectType:baiduMap.typeList.ActiveId,//项目类型
                    projectName:baiduMap.searchValue,//项目名称
                    moneyId: baiduMap.priceList.ActiveId,//价格id
                    leftLng:baiduMap.map.getZoom()>= baiduMap.maxRegion ? baiduMap.lngLeft:'',//四个坐标点  接口名有错误（错着写是对的！！）
                    leftLat:baiduMap.map.getZoom()>= baiduMap.maxRegion ?baiduMap.latTop:'',
                    rightLng:baiduMap.map.getZoom()>= baiduMap.maxRegion ?baiduMap.lngRight:'',
                    rightLat:baiduMap.map.getZoom()>= baiduMap.maxRegion ?baiduMap.latBottom:'',
                    zoomLevel:baiduMap.map.getZoom()>13?baiduMap.map.getZoom():14,
                    tab:"1",
                    projectId:baiduMap.projectId,
                    loopline:baiduMap.subWayList.ActiveId,
                    stationId:baiduMap.subWayStationText.ActiveId,
                    projectStates: baiduMap.salesType
                },
                beforeSend: function() {
                },
                success: function(data) {
                    if(data.status==1){
                        var stationList = data.content.stations;
                        baiduMap.addStationsList = data.content.stations;
                        if (baiduMap.map.getZoom() < 14){
                            baiduMap.addLineLabel(stationList)
                        }else{
                            baiduMap.addStationsLabel()
                        }
                    }
                },
                error: function(data) {
                }
            });
        },
        // 创建线路层级--站点层级标记
        addLineLabel(data){
            let stationsStyle={
                color: '#000',
                fontSize: '14px',
                borderRadius: '6px',
                backgroundColor: "#FFF",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                padding:"3px 6px",
                zIndex:"9",
                cursor: "pointer"
            }
            let stationsHoverStyle={
                color: '#FFF',
                fontSize: '14px',
                borderRadius: '6px',
                backgroundColor: "rgba(212, 43, 43)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                padding:"3px 6px",
                zIndex:"999",
                cursor: "pointer"
            }
            let content,activeStyle
            data.forEach(function(item) {

                  if(baiduMap.subWayList.ActiveId == item.subWayId){
                      content = `<p class="stationsStyle" >${item.stationName}[${item.stationCount}个]</p>`
                      var label = new qq.maps.Label({
                          position: new qq.maps.LatLng(item.latitude,item.longitude),
                          map: baiduMap.map,
                          content: content,
                          style: stationsStyle,
                          item: item
                      });
                      qq.maps.event.addDomListener(label, 'click', function() {
                          baiduMap.clickFun(item,data)
                      });
                      //鼠标移入
                      qq.maps.event.addDomListener(label, 'mouseover', function() {
                          label.setStyle(stationsHoverStyle)
                      });
                      //鼠标移出
                      qq.maps.event.addDomListener(label, 'mouseout', function() {
                          label.setStyle(stationsStyle)
                      });
                      baiduMap.allLabels.push(label);
                  }else if(baiduMap.searchbtnType == true){
                      if(item.stationCount != 0){
                          content = `<p class="stationsStyle" >${item.stationName}[${item.stationCount}个]</p>`
                          var label = new qq.maps.Label({
                              position: new qq.maps.LatLng(item.latitude,item.longitude),
                              map: baiduMap.map,
                              content: content,
                              style: stationsStyle,
                              item: item
                          });
                          baiduMap.searchbtnType = false
                          qq.maps.event.addDomListener(label, 'click', function() {
                              baiduMap.clickFun(item,data)
                          });
                          //鼠标移入
                          qq.maps.event.addDomListener(label, 'mouseover', function() {
                              label.setStyle(stationsHoverStyle)
                          });
                          //鼠标移出
                          qq.maps.event.addDomListener(label, 'mouseout', function() {
                              label.setStyle(stationsStyle)
                          });
                          baiduMap.allLabels.push(label);
                      }

                  }

            });
        },

        // 创建站点层级的站点+项目
        addStationsLabel(){
            let stationsStyle={
                color: '#000',
                fontSize: '14px',
                borderRadius: '6px',
                backgroundColor: "#FF5200",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                padding:"3px 6px",
                zIndex:"9",
                cursor: "pointer"
            }
            let stationsHoverStyle={
                color: '#FFF',
                fontSize: '14px',
                borderRadius: '6px',
                backgroundColor: "rgba(212, 43, 43)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                padding:"3px 6px",
                zIndex:"999",
                cursor: "pointer"
            }
            let saleStyle={
                color: '#FFF',
                fontSize: '14px',
                background: "rgba(255, 82, 0, 0.85)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                height:'24px',
                padding:"3px 6px",
                zIndex:"9",
            }
            let saleHoverStyle={
                color: '#FFF',
                fontSize: '14px',
                background: "linear-gradient(309deg, #00BE5F 0%, #80C788 100%)",
                border: 'none',
                textAlign: 'center',
                alignment: 'center',
                height:'24px',
                padding:"3px 6px",
                zIndex:"999",
            }
            data= baiduMap.addStationsProjectList //项目
            dataStations = baiduMap.addStationsList
            dataStations.forEach(function(item) {
                if(baiduMap.subWayList.ActiveId == item.subWayId){
                    content = `<div class="showsubWayStation" subwayid="${baiduMap.subWayList.ActiveId}" stationid="${baiduMap.subWayStationText.ActiveId }" ><span class="shadow">${item.stationName }[${item.stationCount}个]</span><div style="position: absolute; width: 11px; height: 10px; top: 22px; left: 20px;"></div></div><div class="stationFocus" style="display: block;"></div><div class="stationNormal" style="border-color: rgb(230, 0, 18);"></div>`
                    var label = new qq.maps.Label({
                        position: new qq.maps.LatLng(item.latitude,item.longitude),
                        map: baiduMap.map,
                        content: content,
                        style: stationsStyle,
                        item: item
                    });
                    baiduMap.allLabels.push(label);
                    qq.maps.event.addDomListener(label, 'click', function() {
                        baiduMap.clickFun(item,data)
                    });
                }

            });
            data.forEach(function(item) {
                    var Price ;
                    if(item.projectPrice == null || item.projectPrice.length == 0) {
                        Price = "待定";
                    } else {
                        Price = item.projectPrice[0].showPrice
                        for(var pri = 1; pri < item.projectPrice.length; pri++) {
                            if(parseInt(item.projectPrice[pri].showPrice) < parseInt(Price)) {
                                Price = item.projectPrice[pri].showPrice
                            }
                        }
                    }
                    content2 = `<a class="newProjectA" href="https://sy.fangxiaoer.com/house/${item.projectId}-${item.projectType}.htm" target="_blank"><p class="newProject">${item.projectName}   <b class="projectPrice">${Price}</b></p><div class="sanjiao sanjiao1 t${item.projectId}"></div></a>`

                    var label2 = new qq.maps.Label({
                        position: new qq.maps.LatLng(item.latitude,item.longitude),
                        map: baiduMap.map,
                        content: content2,
                        style: saleStyle,
                        item: item
                    });
                    baiduMap.allLabels.push(label2);

                //鼠标移入
                qq.maps.event.addDomListener(label2, 'mouseover', function() {
                    label2.setStyle(saleHoverStyle)
                });
                //鼠标移出
                qq.maps.event.addDomListener(label2, 'mouseout', function() {
                    label2.setStyle(saleStyle)
                });
            });

        },
        //每次操作后 重新获取所有数据
        showHouse: function() {
            if (baiduMap.readyNum != 0){
                baiduMap.clearAllLabels(); //清除覆盖物
                if(baiduMap.houseSwitch){
                    baiduMap.newHouseList()
                }
                this.subWayStationList();
                this.newHouseListRegionAjax();//区域时房源列表（区域时 排序按照新房列表页）
                if (baiduMap.map.getZoom()>13){
                    this.addStationsLabel()
                }
                // baiduMap.addStationsLabel()
            }

        },
        //点击站点
        clickFun(t,data){
            if(baiduMap.map.getZoom()>14){
                baiduMap.clearAllLabels();
            }
            baiduMap.map.zoomTo(15)
            baiduMap.subWayStationText.ActiveId = t.subStationId
            baiduMap.subWayStationText.ActiveText = t.stationName
            baiduMap.subWayList.ActiveId = t.subWayId
            baiduMap.subWayList.ActiveText = t.subWayName
            baiduMap.map.setCenter(new qq.maps.LatLng(t.latitude,t.longitude))
            baiduMap.addStationsLabel()
        },


        showHouseForSearch: function(type) {//type1搜索点击清除筛选项//type3区域点击//type2当前层级筛选//type4只保留区域其他清空
            baiduMap.clearAllLabels(); //清除覆盖物
            this.lngRight = "";
            this.lngLeft =  "";
            this.latTop =  "";
            this.latBottom = "";
            baiduMap.searchModel = true;
            if(type ==1){
                baiduMap.regionList.ActiveId = '';
                baiduMap.regionList.ActiveText = '区域';
                baiduMap.schoolList.ActiveId = '';
                baiduMap.schoolList.ActiveText = '类别';
                baiduMap.priceList.ActiveId = '';
                baiduMap.priceList.ActiveText = '价格';
                baiduMap.subWayList.ActiveId = '';
                baiduMap.subWayList.ActiveText = '地铁';
                baiduMap.typeList.ActiveId = '';
                baiduMap.typeList.ActiveText = '类型';
                //新房获取地图点
                ajax({
                    type: "POST",
                    url: "/searchNewProjectMap",
                    dataType: "json",
                    async:false,
                    data: {
                        zoomLevel:baiduMap.searchZoomLevel,
                        moneyId: baiduMap.priceList.ActiveId,//价格id-----------------------
                        regionId: baiduMap.regionList.ActiveId,//区域id-------------------------
                        subWayStationId: "",//地铁站id
                        subWayId: baiduMap.subWayList.ActiveId,//地铁线id------------------------------
                        latitude: "",//
                        longitude: "",//
                        distanceId: "",//
                        schoolId: "",//学校id
                        schoolTypeId: baiduMap.schoolList.ActiveId,//学校类型id-------------------------
                        projectName: baiduMap.searchValue,//项目名称
                        roomType: "",//户型id
                        isSubWay: baiduMap.isSubWay,//是不是地铁 非空为是
                        isSchoolArea: baiduMap.isSchool,//是不是学区房  非空为是
                        projectType:baiduMap.typeList.ActiveId,//项目类型id
                        payment: baiduMap.isPayment,//baiduMap.loopList.ActiveId,//是不是低首付 1为是!!!!!!!!!!!!
                        existinfo:baiduMap.isExistinfo,//是不是现房  1为是
                        tab:1,
                        projectId:baiduMap.projectId,
                        pageSize:1000000,//分页
                        projectStates: baiduMap.salesType
                    },
                    beforeSend: function() {
                    },
                    success: function(data) {
                        if(data.content.projectList != ''&& data.content.projectList != null && data.content.projectList.length == 1 && data.content.projectList[0].projectId != undefined){
                            baiduMap.searchZoomLevel = baiduMap.maxRegion;
                        }
                        if(baiduMap.searchZoomLevel != baiduMap.map.getZoom()){
                            baiduMap.SetMap1(data.content.la,data.content.lo,baiduMap.searchZoomLevel);
                        }else {
                            baiduMap.SetMap1(data.content.la,data.content.lo,baiduMap.searchZoomLevel);
                            baiduMap.showHouse();
                        }
                        baiduMap.sreachResultType = true;
                        baiduMap.setHouseHigth();
                    },
                    error: function(data) {
                    }
                });
            }else if(type ==2 || type ==3){
                if(type == 3){
                    baiduMap.searchModel = false;
                }
                if(baiduMap.map.getZoom() != baiduMap.mapZoom){
                    baiduMap.SetMap1( 41.798142,123.439941,baiduMap.mapZoom);
                }else {
                    baiduMap.SetMap1(41.798142,123.439941, baiduMap.mapZoom);
                    baiduMap.showHouse();
                }
                baiduMap.setHouseHigth();
            }
        },
        relasionShow: function(projectId,type) {
            if(type == 1){
                $("[projectId='"+ projectId+"']").mouseover();
            }else {
                $("[projectId='"+ projectId+"']").mouseout();
            }
        },
        clearStationInfo:function () {
            baiduMap.subWayStationText.ActiveId = "";
            baiduMap.subWayStationText.ActiveText = "";
            baiduMap.showSelectList=-1;
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
        },
        switchSearch:function () {
            if(baiduMap.typeList.ActiveId == "" && baiduMap.subWayStationText.ActiveId == "" && baiduMap.priceList.ActiveId == ""&& baiduMap.searchValue == ""){
                baiduMap.searchModel = false;
                baiduMap.houseSwitch = false;
            }
        },
        showClickLine:function (subwayId,stationList) {//此方法用于已选线路，当前线路加粗，非搜索条件下站牌立起
            if(subwayId != ""){
                setLineWeigth(subwayId)
                var lineList = ["1","2","4","6","7"];
                if(!baiduMap.searchModel) {
                    for(var i=0;i<lineList.length;i++){
                        if(lineList[i] != subwayId){
                            $("[subwayid="+lineList[i]+"]").hide();
                        }else {
                            $("[subwayid="+lineList[i]+"]").show();
                        }
                    }
                }


            }
        },
        //--------------

        //清除所有覆盖物
        clearAllLabels: function() {
            if (baiduMap.allLabels[0] != undefined) {
                baiduMap.allLabels.forEach(function(label) {
                    label.setMap(null);
                });
                baiduMap.allLabels = [];
            }

        },
        //视域计算
        canSeeArea:function(){
            var mapBounds = baiduMap.map.getBounds();
            var leftlng = mapBounds.getSouthWest().getLng().toFixed(6)
            var leftlat = mapBounds.getSouthWest().getLat().toFixed(6)
            var rightlng = mapBounds.getNorthEast().getLng().toFixed(6)
            var rightlat = mapBounds.getNorthEast().getLat().toFixed(6)
            baiduMap.lngRight = rightlng;
            baiduMap.lngLeft =  leftlng;
            baiduMap.latTop =  rightlat;
            baiduMap.latBottom =  leftlat;
        },
    }
})
//构造自定义覆盖物
function ComplexCustomOverlay(point, text, num) {
    this._point = point;
    this._text = text;
    this._overText = num;
}
baiduMap.init()
