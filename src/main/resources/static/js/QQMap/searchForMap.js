/**
 * Created by Administrator on 2018/11/2.
 */
$(function () {
    function changeauto(method) {
        $(".ac_input").autocomplete({
            delay: 20,
            source: function( request, response ) {
                var searchInfo = request.term;
                // searchInfo = encodeURI(searchInfo);//如果到线上不好用，则就转换字符试试
                $.ajax({
                    url: "/searchNewProject?key="+method,
                    dataType: "json",
                    type:'post',
                    data: {
                        q: searchInfo
                    },
                    success: function( data ) {
                        if(data != null && data != undefined){
                            if (data.length != 0) {
                                var count = 0;
                                response($.map(data, function (item) {
                                    var highLightTitle = item.projectName;
                                    highLightTitle = highLightTitle.replace(
                                        new RegExp(
                                            "(?![^&;]+;)(?!<[^<>]*)(" +
                                            $.ui.autocomplete.escapeRegex(request.term) +
                                            ")(?![^<>]*>)(?![^&;]+;)", "gi"
                                        ), "<strong>$1</strong>");
                                    var ldec =   "";//右侧描述
                                    var regionInfo = "";//中间描述
                                    if(item.projectId == 0){
                                        ldec = "<i style='float:right'>" +(item.projectType == 0?'':item.projectType+"个楼盘")+ " </i>"
                                        // ldec = <i style='float:right' class='ligthI'>" + 'item.projectType == 0?'':item.projectType+"个楼盘";'+ " </i>
                                        regionInfo = "";
                                    }else {
                                        switch (item.projectType){
                                            case '1':
                                                ldec = '';
                                                break;
                                            case '2':
                                                ldec = "<i style='float:right' class='ligthI'>" + '别墅'+ " </i>";
                                                break;
                                            case '3':
                                                ldec = "<i style='float:right' class='ligthI'>" + '写字楼'+ " </i>"
                                                break;
                                            default:
                                                ldec = '';
                                                break;
                                        }
                                        regionInfo = "&nbsp;&nbsp;<span class='greyFont'>"+item.regionName+"</span>";
                                    }
                                    return {
                                        label:  ldec+highLightTitle+ regionInfo,
                                        projectId: item.projectId,
                                        projectType: item.projectType,
                                        value: item.projectName,
                                        regionId: item.regionId,
                                    };
                                }));
                            } else {
                                var txt = $(".ac_input").attr("name");
                                data = [{"txt":txt}];
                                response($.map(data, function (item) {
                                    return {
                                        label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                        tableId: -1,
                                        value:request.term,
                                        projectId:"-1"
                                    }
                                }))
                            }

                        }

                    }
                });
            },
            select: function( event, ui ) {
                //重置销售状态
                baiduMap.salesType=''
                baiduMap.salesStatus.ActiveId=''
                baiduMap.salesStatus.ActiveText='状态'

                switch (ui.item.projectId){
                    case "0":
                        if(ui.item.regionId!=undefined && ui.item.regionId!=null){
                            baiduMap.projectId ="";
                            baiduMap.regionList.ActiveId = ui.item.regionId;
                            baiduMap.regionList.ActiveText=ui.item.value;
                            baiduMap.decidedType =1;
                            baiduMap.searchZoomLevel =14;
                            baiduMap.searchValue = "";
                            baiduMap.showHouseForSearch(4);
                            baiduMap.sreachRegionSwitch = true;
                        }else {
                            baiduMap.searchValue = ui.item.value;
                            baiduMap.projectId ="";
                            baiduMap.decidedType ="";
                            baiduMap.decidedType =1;
                            baiduMap.searchZoomLevel =12;
                            baiduMap.showHouseForSearch(1);
                        }
                        break;
                    case "-1":
                        window.open("/helpSearch?ids=1");
                        break;
                    default:
                        baiduMap.decidedType =1;
                        baiduMap.projectId =ui.item.projectId;
                        baiduMap.searchZoomLevel =18;
                        baiduMap.searchValue = ui.item.value;
                        baiduMap.showHouseForSearch(1);
                        break;
                }
            },
            open: function() {
                $( this ).removeClass( "ui- corner-all" ).addClass( "ui-corner-top" );
                $( this ).css("z-index", 100);
            },
            close: function() {
                $( this ).removeClass( "ui-corner-top" ).addClass( "ui-corner-all" );
            }
        });
    };
    $("#txtkeys").bind("input propertychange",function(event){
        var valx=$(this).val()
        if(valx==""){
            $("#search2017").find("img").hide()
        }else{
            $("#search2017").find("img").show()
        }
    });
    $(".ac_input").click(function () {
        var txt = $(".ac_input").attr("name");
        changeauto(txt);//需要根据地图找房的类型传key 1-学区，2-地铁，3-现房 新房就是空
    });

    $(".search_btn").click(function () {
        //重置销售状态
        baiduMap.salesType=''
        baiduMap.salesStatus.ActiveId=''
        baiduMap.salesStatus.ActiveText='状态'

        var txt = $(".ac_input").attr("name")
        // input = $(this).prev().val() //取输入框值
        var input = $(".ac_input").val();
        input=stripscript(input);
        baiduMap.searchValue = input;
        baiduMap.projectId ="";
        baiduMap.decidedType ="";
        baiduMap.searchZoomLevel =12;
        baiduMap.showHouseForSearch(1);
        $(".ui-autocomplete").hide();
        // }; //判断结束
    });

    function stripscript(s)
    {
        var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
        var rs = "";
        for (var i = 0; i < s.length; i++) {
            rs = rs+s.substr(i, 1).replace(pattern, '');
        }
        return rs;
    }

    var keys = '';
    if (keys != "" && keys != "null" && keys != null && keys != "0") {
        if (keys != -1) {
            $(".ac_input").val(keys);
        }
    }
    $(".ac_input").each(function () {
        var thisVal = $(".ac_input").val();    //判断文本框的值是否为空，有值的情况就隐藏提示语，没有值就显示
        if (thisVal != "") { $(".ac_input").siblings("span").hide(); }
        else { $(".ac_input").siblings("span").show(); }
        $(".ac_input").keyup(function () {
            $(".ac_input").siblings("span").hide();
        }).blur(function () {
            var val = $(this).val();
            if (val != "") {
                $(this).siblings("span").hide();
            } else {
                $(this).siblings("span").show();
            }
        });
    });
    //此方法用于搜索框无下拉时enter事件
    $(".ac_input").keydown(function (event) {
        if (event.keyCode == 13) {
            //重置销售状态
            baiduMap.salesType=''
            baiduMap.salesStatus.ActiveId=''
            baiduMap.salesStatus.ActiveText='状态'

            var selecetFlag = true;
            $(".ui-autocomplete").each(function () {
                if($(this).css("display") == "block"){
                    var item = $(this);
                    $(item).find("a").each (function () {
                        if($(this).hasClass("ui-state-focus")){
                            selecetFlag = false;
                            return false;
                        }
                    });
                }
            });
            if(selecetFlag){
                $(".search_btn").click();
            }
        }
    });
    $("#deleteButton").click(function () {
        // $("#search2017 .search .ac_input").val("");
        // $("#deleteButton").hide();
        var redirectUrl =location.pathname;
        if( redirectUrl != null || redirectUrl != undefined || redirectUrl != ''){
            if( redirectUrl.indexOf("search") != -1 ){
                redirectUrl =   redirectUrl.substr(0,redirectUrl.indexOf("search"));
                window.location.href = redirectUrl;
            }else {
                // window.location.reload();
                $("#search2017 .search .ac_input").val("");
                $("#deleteButton").hide();
            }
        }
    });
    $("#search2017 .search .ac_input").val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
    $("#search2017 .search .ac_input").keyup(function(){
        $(this).val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
    })
});
//设置cookie time按分钟计时
function setCookie(name,value,time) {
    var exp = new Date();
    exp.setTime(exp.getTime() + time*60*1000);
    value =  encodeURI(value)
    document.cookie = name + "="+ value + ";expires=" + exp.toGMTString();
}
function delCookie(name)
{
    var exp = new Date();
    exp.setTime(exp.getTime() - 20000);
    var cval=getCookie(name);
    if(cval!=null)
        document.cookie= name + "="+cval+";expires="+exp.toGMTString();
}
function getCookie(name) {
    var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
    if(arr=document.cookie.match(reg)){
        return unescape(arr[2]);}
    else {
        return null;
    }
}
function setContentText( itemSummary,contentType) {
    var countNumber = "";
    var placeName = "";
    if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
        var Arr   = itemSummary.split(",");
        if(Arr.length>0){
            if(contentType == 0){
                placeName = "租房0套";
            }else {
                placeName = "二手房0套";
            }
            if(Arr[contentType]!=placeName){
                countNumber=    Arr[contentType].match(/\d+/g);
                countNumber = countNumber[0];
            }else {
                itemSummary =   "";
            }
        }
        if(countNumber !=0 && countNumber!=""){
            itemSummary = "约"+countNumber+"条房源";
        }
        return itemSummary;
    }
}