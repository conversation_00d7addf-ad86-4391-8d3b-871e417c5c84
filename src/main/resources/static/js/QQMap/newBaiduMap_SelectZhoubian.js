let searchService
var bdMap = {
    id:0,
    houselng: 0,
    houselat: 0,
    radius: 1000, //搜索半径
    suofa: 15,//缩放比例
    openInfoList: [],//数据集合
    bdmap: undefined,
    bdpoit: {},
    bdtitle: "",
    bdcontent: "",
    address: "",
    city: "沈阳",
    allLabels:[],
    subId:"",
    data:[],
    init: function (divid, entity) {
        bdMap.houselng = entity.houselng;
        bdMap.houselat = entity.houselat;
        bdMap.radius = entity.radius;
        bdMap.suofa = entity.suofa+1;
        bdMap.bdtitle = entity.bdtitle;
        bdMap.bdcontent = entity.bdcontent;
        bdMap.address = entity.address;
        bdMap.city = entity.city;
        bdMap.id = entity.id;
        bdMap.subId = entity.subId;

        if (bdMap.houselng * 1 == 0 || bdMap.houselat * 1 == 0) {
            bdMap.AddressToPoint(bdMap.address, bdMap.city, divid);//没有坐标的情况下从地址你想解析坐标
        }
        else {
            bdMap.showMap(divid);
        }
    },
    AddressToPoint: function (address, city, divid) {
        var myGeo = new BMap.Geocoder();
        // 将地址解析结果显示在地图上,并调整地图视野
        myGeo.getPoint(address, function (point) {
            if (point) {
                bdMap.houselng = point.lng;
                bdMap.houselat = point.lat;
                bdMap.showMap(divid);
            }
        }, city);
    },
    showMap: function (divid) {

        bdMap.bdmap = new qq.maps.Map(document.getElementById(divid), {
            center: new qq.maps.LatLng(bdMap.houselat, bdMap.houselng),      // 地图的中心地理坐标。
            zoom:bdMap.suofa,                                                // 地图的中心地理坐标。
            panControl: false,         //平移控件的初始启用/停用状态。
            zoomControl: false,       //缩放控件的初始启用/停用状态。
            scaleControl: true,       //滚动控件的初始启用/停用状态。
            mapTypeControl: false,
        })

        bdMap.searechHouse()
    },
    addnewmarker: function (data) {
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            //div.attr("class", "bubble-2 bubble");
            div.style.position = "absolute";
            div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);

            if(bdMap.bdtitle == data.title){
                div.className="bdtitleHover"

            }else{
                div.style.backgroundColor = "#ff5200";
                div.className="bdtitleHoverElse"
            }
            // div.style.border = "1px solid #BC3B3A";
            div.style.color = "white";
            div.style.height = "18px";
            div.style.fontSize="14px";
            div.style.borderRadius="4px";
            div.style.padding = "8px 10px";
            div.style.lineHeight = "18px";
            div.style.whiteSpace = "nowrap";
            div.style.MozUserSelect = "none";
            div.style.fontSize = "12px";
            var span = this._span = document.createElement("span");
            div.appendChild(span);
            span.appendChild(document.createTextNode(this._text));
            var that = this;

            var arrow = this._arrow = document.createElement("div");
            arrow.style.background = "url(https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png) no-repeat";
            arrow.style.position = "absolute";
            arrow.style.width = "16px";
            arrow.style.height = "10px";
            arrow.style.top = "34px";
            arrow.style.left = "22px";
            arrow.style.overflow = "hidden";
            div.appendChild(arrow);
            bdMap.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(this._point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(data.lng, data.lat), data.title, data.content);
        bdMap.bdmap.addOverlay(myCompOverlay);
        // console.log(data)
        if (bdMap.id && bdMap.id > 0) {
            myCompOverlay.addEventListener("click", function () {
                if(data.subId == undefined) return;
                window.open("https://sy.fangxiaoer.com/saleVillages/" + data.subId+"/index.htm");
            });
        }

    },

    //添加标注
    addmarker: function (poit, title, content) {
        var marker = new BMap.Marker(poit);
        bdMap.showFrom(marker, title, bdMap.bdmap, content);
        bdMap.bdmap.addOverlay(marker);//添加标注
    },
    //标注弹出面板
    showFrom: function (marker, title, map, content,icona,iconb) {
        var searchInfoWindow = new BMapLib.SearchInfoWindow(map, "<p style='font-weight: 400;color: #333;'>" + content + "</p>", {
            title: "<p style='width: 355px;font: bold 14px/16px arial,sans-serif;margin: 0;color: #333;white-space: nowrap;overflow: hidden;font-size: 16px;line-height: 40px;' title='" + title + "'>" + title + "</p>", //标题
            width: 290, //宽度
            panel: "panel", //检索结果面板
            enableAutoPan: true, //自动平移
            searchTypes: [
            ]
        });
        marker.addEventListener("mouseover", function(e) {
            marker.setIcon(iconb);
            searchInfoWindow.open(marker);
        });
        marker.addEventListener("mouseout", function(e) {
            marker.setIcon(icona);
            searchInfoWindow.close(marker);
        });
    },



    //查询方法
    searechMap: function (type) {
        bdMap.clearAllLabels()//清除覆盖物

        $.ajax({
            type: 'get',
            url: `https://apis.map.qq.com/ws/place/v1/search?key=I3ZBZ-5K5WL-77WPV-M7G3Q-3Z2ZK-DVFNX&location=${bdMap.houselat},${bdMap.houselng}&keyword=${type}&boundary=nearby(${bdMap.houselat},${bdMap.houselng},1900,0)&output=jsonp`,
            async: false,
            dataType: 'jsonp',
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET,POST',
            },
            success: function (data) {
                let res=data.data
                let className
                bdMap.data=res
                console.log(res)
                $(".map_dl").html("");
                switch (type){
                    case "公交":
                        className = 'buswei'
                        break;
                    case "地铁":
                        className = 'metrowei'
                        break;
                    case "学校":
                        className = 'schoolowei'
                        break;
                    case "超市":
                        className = 'shopwei'
                        break;
                    case "医院":
                        className = 'hospitalwei'
                        break;
                }

                if(res.length == 0 || res == ''){
                    $(".map_dl").html("<div class='mao_null'> <p>暂时没有相关信息，看看其他内容吧</p></div>");
                }else{
                    let s=``
                    res.forEach((item)=>{
                        s+=`<dd onclick='bdMap.openInfoWindow({title: "${item.title}",address:"${item.address}",lng:"${item.location.lng}",lat:"${item.location.lat}",subId:"${item.subId?item.subId:item.id}"})'><a title="${item.title}"><i class="${className}"></i><span>${item._distance}米</span>${item.title}</a></dd>`

                    })
                    $(".map_dl").html(s);
                }
                bdMap.addRegionLabel(res,2,type)

            }
        })

    },
    //周边楼盘
    searechHouse:function(){
        bdMap.clearAllLabels(); //清除覆盖物
        $.ajax({
            type:'post',
            url:"https://ltapi.fangxiaoer.com/apiv1/house/viewDistrictList",
            data:{
                except:bdMap.id,
                distanceId:3,
                longitude:bdMap.houselng,
                latitude:bdMap.houselat,
                orderType:bdMap.orderType,
                pageSize:10
            },
            success:function (data) {
                var data = data.content
                bdMap.data = data
                console.log(data)
                var html = "";
                var distance ="";
                data.unshift({ longitude: bdMap.houselng, latitude: bdMap.houselat, title: bdMap.bdtitle, content: bdMap.bdcontent,subId:bdMap.subId })
                bdMap.addRegionLabel(data,1) //创建标记点
                bdMap.SetMap(bdMap.houselng,bdMap.houselat);
                for(var i =0 ;i<data.length;i++){
                    distance = parseInt(data[i].distance);


                    if(i == 0){
                        if(data.length<=1){
                            html = '<div class="mao_null"> <p>暂时没有相关信息，看看其他内容吧</p></div>'
                        }
                    }
                    if(bdMap.subId!=data[i].subId){
                        html += "<dd onclick='bdMap.openInfoWindow({title:\"" + data[i].title + "\",address:\"" + data[i].address + "\",lng:" + data[i].longitude + ",lat:" + data[i].latitude +",subId:"+data[i].subId+ "})'><a title='" + data[i].title + "'><i class='housewei'></i><span>"+distance+"米</span>" + data[i].title + "</a></dd>";
                    }

                    if(i>=10){
                        break;
                    }
                }
                $(".map_dl").html(html);
            }
        })
    },


    //添加显示面板
    openInfoWindow: function (searchInfo) {
        console.log(searchInfo)
        console.log(bdMap.data)
        bdMap.SetMap(searchInfo.lng, searchInfo.lat)//设置中心点
        bdMap.data.forEach(function(item){
            $(".c"+item.subId).hide()
            $(".c"+item.id).hide()
        })
        $(".c"+searchInfo.subId).show()

    },
    //设置地图中心点
    SetMap: function (lat,lng) {
        bdMap.bdmap.setCenter(new qq.maps.LatLng(lng,lat))//设置中心点
        // bdMap.bdmap.setZoom(16)//设置缩放层级
    },
    //查询结果绑定
    select: function (results) {
        bdMap.openInfoList = [];
        bdMap.bdmap.clearOverlays();
        bdMap.bdmap.centerAndZoom(bdMap.bdpoit, bdMap.suofa);
        bdMap.addnewmarker({ lng: bdMap.houselng, lat: bdMap.houselat, title: bdMap.bdtitle, content: bdMap.bdcontent,subId:bdMap.id});
        local.disableFirstResultSelection();
        // 判断状态是否正确
        if (local.getStatus() == BMAP_STATUS_SUCCESS) {
            var s = [];
            for (var i = 0; i < results.getCurrentNumPois() ; i++) {

                var entity = {};
                entity.title = results.getPoi(i).title;
                entity.address = results.getPoi(i).address;
                entity.phoneNumber = results.getPoi(i).phoneNumber;
                entity.point = results.getPoi(i).point;
                entity.detailUrl = results.getPoi(i).detailUrl;
                entity.url = results.getPoi(i).url;
                var pointA = new BMap.Point(bdMap.houselng, bdMap.houselat);  // 创建点坐标A
                var pointB = new BMap.Point(results.getPoi(i).point.lng, results.getPoi(i).point.lat);  // 创建点坐标B
                entity.distance = (bdMap.bdmap.getDistance(pointA, pointB)).toFixed(2);  //获取两点距离,保留小数点后两位
                bdMap.openInfoList.push(entity);

            }
            //排序对象集合
            var iclass=""
            switch (results.keyword){
                case "公交":
                    iclass="buswei"
                    break;
                case "地铁":
                    iclass="metrowei"
                    break;
                case "学校":
                    iclass="schoolowei"
                    break;
                case "超市":
                    iclass="shopwei"
                    break;
                case "医院":
                    iclass="hospitalwei"
                    break;
            }
            var list = bdMap.openInfoList.sort(function (a, b) {
                return a.distance - b.distance;
            });
            var html = "";
            for (var i = 0; i < list.length; i++) {

                html += "<dd onclick='bdMap.openInfoWindow({title:\"" + list[i].title + "\",address:\"" + list[i].address + "\",lng:" + list[i].point.lng + ",lat:" + list[i].point.lat + "})'><a title='" + list[i].title + "'><i class='"+iclass+"'></i><span>" + list[i].distance + "米</span>" + list[i].title + "</a></dd>";
            }
            $(".map_dl").html(html);
        }
    },

    //-----------------
    //创建标记点
    addRegionLabel(data,type,keyWord){
        let areaStyle={
            color: '#FFF',
            fontSize: '14px',
            borderRadius: '4px',
            backgroundColor: "rgba(255, 82, 0, 0.9)",
            border: 'none',
            textAlign: 'center',
            alignment: 'center',
            height:'18px',
            padding:"8px 10px",
            lineHeight: "18px",
        }
        let sellOutStyle = {
            color: '#FFF',
            fontSize: '14px',
            borderRadius: '4px',
            backgroundColor: "rgba(128, 153, 175, 0.9)",
            border: 'none',
            textAlign: 'center',
            alignment: 'center',
            height:'18px',
            padding:"8px 10px",
            lineHeight: "18px",
        }
        let mouseoverSetStyle = {
            color: '#FFF',
            fontSize: '14px',
            borderRadius: '4px',
            backgroundColor: "rgba(42, 149, 254, 0.9)",
            border: 'none',
            textAlign: 'center',
            alignment: 'center',
            height:'18px',
            padding:"8px 10px",
            lineHeight: "18px",
        }
        let locationStyle={
            color: '#FFF',
            fontSize: '14px',
            border: 'none',
            textAlign: 'center',
            alignment: 'center',
            backgroundColor: "rgba(0, 0, 0, 0)",
        }

        data.forEach(function(item) {
            console.log(item)
            let activeStyle,url
            if(type==1){
                bdMap.bdtitle == item.title?activeStyle= mouseoverSetStyle : activeStyle= areaStyle
            }else{
                activeStyle=locationStyle
            }


            if(bdMap.subId!=item.subId){
                url = `<a href="/saleVillages/${item.subId}/index.htm"  target="_blank">${item.title}</a>`
            }else{
                url = item.title
            }

            let u,m,h
            if(type==2){
                switch (keyWord){
                    case "公交":
                        m='https://static.fangxiaoer.com/web/images/map/map_1_sun.png'
                        break;
                    case "地铁":
                        m='https://static.fangxiaoer.com/web/images/map/map_2_sun.png'
                        break;
                    case "学校":
                        m='https://static.fangxiaoer.com/web/images/map/map_3_sun.png'
                        break;
                    case "超市":
                        m='https://static.fangxiaoer.com/web/images/map/map_4_sun.png'
                        break;
                    case "医院":
                        m='https://static.fangxiaoer.com/web/images/map/map_5_sun.png'
                        break;
                }
            }
            type==1?u=`<span>${url}</span><div class=\"sanjiao sanjiao${bdMap.bdtitle == item.title?2:1} t${item.subId}\"></div>`:u=`<img src="${m}" style="width: 30px; height: 36px;"/>`
            type==1?h=`left: -140px; top: -102px;`:h=`left: -147px; top: -99px;`


            let content = `${u}
                        <div class="c${type==1?item.subId:item.id}"  style="position: absolute; height: 0px; width: auto; z-index: 800;display: none; ${h}">
                            <div class="BMapLib_SearchInfoWindow" id="z${type==1?item.subId:item.id}" style="/*width: 360px;*/ user-select: none; ">
                                <div class="BMapLib_bubble_top">
                                    <div class="BMapLib_bubble_title" ><p
                                        style="width: 355px;font: bold 14px/16px arial,sans-serif;margin: 0;color: #333;white-space: nowrap;overflow: hidden;font-size: 16px;line-height: 40px; text-align: left; padding: 0 5px;">${item.title}</p></div>
                                    <div class="BMapLib_bubble_tools">
                                        <div class="BMapLib_bubble_close" title="关闭" style="background: url('https://api.map.baidu.com/library/SearchInfoWindow/1.5/src/iw_close.gif') no-repeat center center; position: absolute; top: 0; right: 6px;"></div>
                                    </div>
                                </div>
                                <div class="BMapLib_bubble_center">
                                    <div class="BMapLib_bubble_content" ><p
                                        style="width:355px;font:bold 14px/16px arial,sans-serif;margin:0;color:#333;font-weight: 400;white-space:nowrap;overflow:hidden;text-align: left;padding: 0 5px;">
                                        ${item.address}</p></div>
                                </div>
                                <div class="BMapLib_bubble_bottom"></div>
                                <img src="http://api.map.baidu.com/library/SearchInfoWindow/1.4/src/iw_tail.png" width="58" height="31" alt=""
                                         class="BMapLib_trans" id="BMapLib_trans37" style="left: 159px; top: 87px; position: absolute;">
                            </div>
                        </div>`

            var label = new qq.maps.Label({
                position: type==1?new qq.maps.LatLng(item.latitude,item.longitude):new qq.maps.LatLng(item.location.lat,item.location.lng),
                map: bdMap.bdmap,
                content: content,
                style: activeStyle,
                item: item
            });

            qq.maps.event.addDomListener(label, 'click', function() {
                console.log(label)
                $(".c"+label.item.subId).hide()
                $(".c"+label.item.id).hide()
            });
            //鼠标移入`
            qq.maps.event.addDomListener(label, 'mouseover', function() {
                if(type==1){
                    label.setStyle(mouseoverSetStyle);
                    $(".t"+label.item.subId).css("background",'url("https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian7.png") no-repeat')
                    $(this).css("zIndex","9")
                }else{
                    $(".c"+label.item.id).show()
                }
            });
            //鼠标移出
            qq.maps.event.addDomListener(label, 'mouseout', function() {
                if(type==1){
                    if(label.item.projectStatus=='3'){
                        label.setStyle(sellOutStyle)
                        $(".t"+label.item.subId).css("background",'url("https://static.fangxiaoer.com/web/images/brand/Gray.png") no-repeat')
                    }else{
                        if(bdMap.bdtitle == item.title){
                            label.setStyle(mouseoverSetStyle)
                            $(".t"+label.item.subId).css("background",'url("https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian7.png") no-repeat')
                        } else{
                            label.setStyle(areaStyle)
                            $(".t"+label.item.subId).css("background",'url("https://static.fangxiaoer.com/web/images/sy/house/newBaiduMap_SelectZhoubian5.png") no-repeat')
                        }
                    }
                    $(".t"+bdMap.subId).parent().css("zIndex","")
                    $(this).css("zIndex","")
                }else{
                    $(".c"+label.item.id).hide()
                }
            });
            bdMap.allLabels.push(label);
        });
    },

    //清除所有覆盖物
    clearAllLabels (){
        bdMap.allLabels.forEach(function(label) {
            if(bdMap.bdtitle!=label.item.title){
                label.setMap(null);
            }
        });
        // bdMap.allLabels = [];
    },


}

function ComplexCustomOverlay(point, text, mouseoverText) {
    this._point = point;
    this._text = text;
    this._overText = mouseoverText;
}

