var polyline = new Array()
//1号线
var path1 = [
	[123.511613, 41.814227],
	[123.500783, 41.815326],
	[123.498556, 41.815346],
	[123.49485, 41.81503],
	[123.492506, 41.814822],
	[123.488315, 41.813629],
	[123.485647, 41.812594],
	[123.481811, 41.81053],
	[123.480109, 41.809845],
	[123.477639, 41.809334],
	[123.47065, 41.807657],
	[123.468112, 41.807627],
	[123.46663, 41.807603],
	[123.466034, 41.807457],
	[123.463618, 41.806621],
	[123.461327, 41.805828],
	[123.45812, 41.804577],
	[123.45569, 41.803636],
	[123.454828, 41.803351],
	[123.452708, 41.802541],
	[123.451459, 41.802043],
	[123.450058, 41.801489],
	[123.44863, 41.800927],
	[123.443545, 41.798911],
	[123.44081, 41.798262],
	[123.430785, 41.795946],
	[123.428934, 41.795516],
	[123.421564, 41.793822],
	[123.419008, 41.793439],
	[123.417993, 41.793314],
	[123.416426, 41.793923],
	[123.41228, 41.795798],
	[123.404164, 41.799344],
	[123.401958, 41.800272],
	[123.395567, 41.80291],
	[123.391843, 41.804463],
	[123.39086, 41.804772],
	[123.390442, 41.804806],
	[123.38993, 41.804803],
	[123.389283, 41.804638],
	[123.388686, 41.804453],
	[123.388151, 41.80411],
	[123.386566, 41.80201],
	[123.38476, 41.799728],
	[123.383857, 41.798635],
	[123.383179, 41.798296],
	[123.382739, 41.798245],
	[123.382245, 41.798276],
	[123.381576, 41.798447],
	[123.377794, 41.800161],
	[123.376244, 41.800837],
	[123.363192, 41.806352],
	[123.361463, 41.807068],
	[123.360479, 41.807394],
	[123.35605, 41.808513],
	[123.350238, 41.809843],
	[123.348797, 41.81018],
	[123.345576, 41.810973],
	[123.345123, 41.811013],
	[123.343443, 41.811033],
	[123.339081, 41.810989],
	[123.337707, 41.810976],
	[123.334271, 41.810966],
	[123.332982, 41.810929],
	[123.332618, 41.810882],
	[123.332007, 41.810737],
	[123.331513, 41.810532],
	[123.329281, 41.809481],
	[123.327969, 41.808876],
	[123.32721, 41.808519],
	[123.326114, 41.808032],
	[123.325292, 41.807817],
	[123.318667, 41.807421],
	[123.316871, 41.807306],
	[123.31533, 41.807165],
	[123.314477, 41.80697],
	[123.314001, 41.806799],
	[123.313354, 41.806483],
	[123.31295, 41.806251],
	[123.312514, 41.805902],
	[123.31233, 41.805431],
	[123.312254, 41.804937],
	[123.312366, 41.80288],
	[123.312424, 41.800376],
	[123.312424, 41.799331],
	[123.312375, 41.796447],
	[123.312285, 41.790494],
	[123.312267, 41.789166],
	[123.312276, 41.788333],
	[123.31206, 41.7872],
	[123.31175, 41.785892],
	[123.311405, 41.785257],
	[123.310452, 41.784473],
	[123.306123, 41.781549],
	[123.302547, 41.778893],
	[123.301402, 41.778035],
	[123.299861, 41.776909],
	[123.296632, 41.774602],
	[123.295832, 41.774068],
	[123.295235, 41.773846],
	[123.284586, 41.771973],
	[123.282973, 41.771684],
	[123.280669, 41.771257],
	[123.279874, 41.771186],
	[123.278042, 41.771237],
	[123.266709, 41.771499],
	[123.26494, 41.771536],
	[123.24966, 41.771785],
	[123.247908, 41.771798],
	[123.237991, 41.771748],
	[123.235969, 41.771734]
]
//2号线
var path2 =
	[
		[123.412009,41.963001],
		[123.415139,41.952467],
		[123.416082,41.949536],
		[123.415647,41.947829],
		[123.41226,41.945278],
		[123.404467,41.942531],
		[123.401853,41.941555],
		[123.401085,41.941099],
		[123.399881,41.939007],
		[123.400739,41.935348],
		[123.401732,41.932685],
		[123.403556,41.929975],
		[123.409044,41.924675],
		[123.409956, 41.923313],
		[123.410162, 41.922969],
		[123.415525, 41.913767],
		[123.416378, 41.912213],
		[123.419284, 41.906543],
		[123.419338, 41.906426],
		[123.420026, 41.904516],
		[123.42087, 41.901764],
		[123.421504, 41.899435],
		[123.42176, 41.898515],
		[123.421863, 41.897223],
		[123.422371, 41.890937],
		[123.423184, 41.881552],
		[123.423776, 41.874855],
		[123.423929, 41.873076],
		[123.423992, 41.872341],
		[123.42428, 41.8675084],
		[123.424765, 41.861715],
		[123.42493, 41.860382],
		[123.425205, 41.858578],
		[123.425575, 41.853928],
		[123.425708, 41.852302],
		[123.426054, 41.848195],
		[123.426271, 41.845679],
		[123.426340, 41.845226],
		[123.426394, 41.845149],
		[123.426409, 41.845122],
		[123.426495, 41.845038],
		[123.426727, 41.844927],
		[123.42698, 41.844869],
		[123.427127, 41.844858],
		[123.429939, 41.844961],
		[123.431964, 41.845041],
		[123.432373, 41.845065],
		[123.433352, 41.845051],
		[123.433887, 41.844981],
		[123.434466, 41.844736],
		[123.434691, 41.844635],
		[123.435005, 41.84443],
		[123.435288, 41.844212],
		[123.4354, 41.84394],
		[123.43546, 41.842233],
		[123.435495, 41.840299],
		[123.435515, 41.840211],
		[123.435993, 41.838582],
		[123.435998, 41.838579],
		[123.436100, 41.838179],
		[123.43616, 41.837323],
		[123.436220, 41.836899],
		[123.43632, 41.835945],
		[123.43632, 41.835945],
		[123.436505, 41.834662],
		[123.436600, 41.833775],
		[123.436715, 41.832308],
		[123.436755, 41.832072],
		[123.436775, 41.831817],
		[123.436798, 41.831602],
		[123.436784, 41.831525],
		[123.436784, 41.831417],
		[123.436743, 41.831286],
		[123.436573, 41.830812],
		[123.436210, 41.829916],
		[123.435972, 41.829281],
		[123.435978, 41.827356],
		[123.435958, 41.826868],
		[123.43603, 41.825894],
		[123.436208, 41.825488],
		[123.436708, 41.825061],
		[123.43748, 41.824759],
		[123.437803, 41.824644],
		[123.438967, 41.824237],
		[123.439645, 41.824033],
		[123.441527, 41.823351],
		[123.443786, 41.822605],
		[123.444213, 41.822467],
		[123.444599, 41.822264],
		[123.44605, 41.820156],
		[123.446135, 41.819645],
		[123.446105, 41.819215],
		[123.445749, 41.818615],
		[123.444181, 41.816678],
		[123.443553, 41.815885],
		[123.44255, 41.814491],
		[123.442331, 41.814171],
		[123.442158, 41.813876],
		[123.44204, 41.81362],
		[123.441955, 41.813318],
		[123.441868, 41.812474],
		[123.441831, 41.812357],
		[123.441730, 41.812199],
		[123.441092, 41.811197],
		[123.439800, 41.809117],
		[123.43970, 41.808959],
		[123.439614, 41.808741],
		[123.439277, 41.80784],
		[123.439239, 41.807726],
		[123.438648, 41.806358],
		[123.438432, 41.805894],
		[123.438295, 41.805632],
		[123.438239, 41.805421],
		[123.438213, 41.805242],
		[123.438222, 41.804802],
		[123.438554, 41.802517],
		[123.438900, 41.799979],
		[123.439254, 41.7988],
		[123.439856, 41.797139],
		[123.440472, 41.795533],
		[123.441373, 41.793065],
		[123.442031, 41.791865],
		[123.442381, 41.791166],
		[123.442951, 41.789912],
		[123.442196, 41.782987],
		[123.441945, 41.780644],
		[123.441875, 41.779736],
		[123.441704, 41.77786],
		[123.441613, 41.776983],
		[123.441825, 41.772258],
		[123.441977, 41.769289],
		[123.442016, 41.768546],
		[123.442066, 41.768065],
		[123.442433, 41.766787],
		[123.442899, 41.765069],
		[123.442926, 41.765016],
		[123.443289, 41.763818],
		[123.443509, 41.763058],
		[123.443689, 41.762621],
		[123.443923, 41.762224],
		[123.444974, 41.760862],
		[123.4454, 41.760348],
		[123.445751, 41.759971],
		[123.447251, 41.758925],
		[123.450831, 41.756319],
		[123.451945, 41.755508],
		[123.453189, 41.754529],
		[123.454343, 41.753621],
		[123.456059, 41.752276],
		[123.457554, 41.751092],
		[123.45932, 41.749706],
		[123.460856, 41.748411],
		[123.461004, 41.748266],
		[123.4613, 41.747869],
		[123.461714, 41.747029],
		[123.461714, 41.747029],
		[123.461709, 41.747035],
		[123.46197, 41.746336],
		[123.462288, 41.745411],
		[123.462504, 41.744489],
		[123.462576, 41.743702],
		[123.462666, 41.742777],
		[123.462688, 41.742477],
		[123.462774, 41.741391],
		[123.462841, 41.740055],
		[123.462895, 41.738369],
		[123.462971, 41.736546],
		[123.463106, 41.735816],
		[123.463254, 41.735348],
		[123.463654, 41.734554],
		[123.464907, 41.732451],
		[123.46523, 41.731782],
		[123.465675, 41.73086],
		[123.466246, 41.729588],
		[123.467234, 41.72762],
		[123.468819, 41.725291],
		[123.469578, 41.724188],
		[123.47027, 41.723192],
		[123.470656, 41.722734],
		[123.472102, 41.720997],
		[123.473005, 41.719988],
		[123.473899, 41.718992],
		[123.47451, 41.718329],
		[123.475839, 41.71706],
		[123.476742, 41.716249],
		[123.478898, 41.715142],
		[123.48069, 41.714553],
		[123.484319, 41.713722],
		[123.484876, 41.713654],
		[123.486174, 41.713883],
		[123.486916, 41.713974],
		[123.487495, 41.713893],
		[123.488371, 41.713728],
		[123.489682, 41.713271],
		[123.495362,41.710949],
		[123.501489,41.706856],
		[123.501525,41.706654],
		[123.501327,41.706022],
		[123.50095,41.705658],
		[123.491068,41.701323],
		[123.487008,41.700313],
		[123.475761,41.694792],
		[123.471701,41.694469],
		[123.465503,41.694334],
		[123.465017,41.694078],
		[123.464766,41.693694],
		[123.464631,41.693169],
		[123.464649,41.692967],
		[123.464676,41.685546],
		[123.464676,41.685546],
		[123.464353,41.645884],
		[123.464568,41.643822],
		[123.46694,41.639994],
		[123.469419,41.638283],
		[123.480576,41.635978],
		[123.489829,41.634239],
		[123.491913,41.634306],
		[123.500811,41.639839],

	]
//4号线
var path6 =
	[
		[123.486981,41.893538],//望花街
		[123.486348,41.887625],
		[123.485818,41.882358],
		[123.48633,41.879143],
		[123.486523,41.878646],
		[123.486963,41.878307],
		[123.487547,41.878099],
		[123.493503,41.87734],//文贸路
		[123.496257,41.876944],
		[123.496719,41.876813],
		[123.496966,41.876588],
		[123.497074,41.875967],
		[123.49711, 41.874665], //望花屯
		[123.494271, 41.864146],
		[123.489151, 41.854466], //观全路
		[123.483977, 41.84431], //南卡门路
		[123.482198, 41.840733],
		[123.481946, 41.839994],
		[123.481263, 41.837549],
		[123.481031, 41.835685], //北大营街
		[123.480778, 41.833867],
		[123.477149, 41.829755], //吉祥
		[123.468597, 41.827524],
		[123.459003, 41.826584], //小北关街
		[123.450739, 41.82567],
		[123.44287, 41.822848], //北站
		[123.430168, 41.818816],
		[123.428641, 41.81805],
		[123.42724, 41.816736], //皇寺路
		[123.426485, 41.815026],
		[123.425155, 41.812821],
		[123.423108, 41.810204], //市府大路
		[123.411933, 41.795846], //太原街
		[123.405896, 41.78794], //南五马路
		[123.39729, 41.776587],
		[123.39729, 41.776022],
		[123.397991, 41.773901], //砂阳路
		[123.3995, 41.770104],
		[123.403955, 41.762248],
		[123.407334, 41.754342], //长白北
		[123.409525, 41.748405], //长白岛
		[123.415976, 41.732957], //长白南
		[123.417861, 41.727237],
		[123.418059, 41.726066],
		[123.417987, 41.725056],
		[123.410011, 41.710586], //王士屯
		[123.406166, 41.704029], //上河村
		[123.404261, 41.700191],
		[123.403883, 41.699141],
		[123.404117, 41.698508],
		[123.404494, 41.698117],
		[123.409562, 41.69595], //红椿路
		[123.421991,41.69103],
		[123.422745,41.69002],
		[123.422565,41.687798],//城建学院
		[123.419951,41.681586],
		[123.41855,41.676009],//新沈阳南站
		[123.418478,41.673733],
		[123.420059,41.666741],
		[123.420059,41.666741],
		[123.420019,41.66374] //航天南路
	]
//6号线
var path9 =
	[
		[123.480653,41.912893],
		[123.474061,41.899973],
		[123.473836,41.886576],
		[123.468878,41.875891],
		[123.463888,41.864923],
		[123.463367,41.857271],
		[123.463317,41.847337],
		[123.463214,41.839239],
		[123.465761,41.82464],
		[123.466165,41.807476],
		[123.466385,41.799185],
		[123.466336,41.794546],
		[123.464413,41.786677],
		[123.460326,41.777529],
		[123.456126,41.770492],
		[123.442450,41.766766],
		[123.429954,41.763309],
		[123.419296,41.750485],
		[123.408584,41.74816],
		[123.394076,41.743512],
		[123.382214,41.740679],
		[123.376855,41.735061],
		[123.371250,41.728547],
		[123.366228,41.716244],
		[123.361167,41.706756],
		[123.354312,41.69386],
		[123.345810,41.684968],
		[123.346574,41.671377],
		[123.352592,41.659606]
	]
//9号线
var path4 =
	[
		[123.527303, 41.75484], //建筑大学
		[123.52477, 41.753754],
		[123.519991, 41.752516],
		[123.508169, 41.750498], //浑南大道
		[123.493023, 41.748301], //朗日街
		[123.487203, 41.747645],
		[123.480151, 41.749899],
		[123.473368, 41.75005], //奥体东
		[123.46827, 41.749949],
		[123.463059,41.749857],
		[123.461838,41.74973],
		[123.460185,41.74903], //奥体中心
		[123.459686,41.748774],
		[123.458002,41.747691],
		[123.45758,41.747207],
		[123.457378,41.746591],//法治文化
		[123.457786,41.744832],
		[123.45775,41.743998],
		[123.456488,41.742268],
		[123.456475,41.742265],
		[123.448072, 41.74012], //浑河堡
		[123.437283, 41.737523], //金阳大街
		[123.430276, 41.736042], //榆树台
		[123.415993, 41.73296], //长白 南
		[123.401001, 41.729784], //胜利南街
		[123.39632, 41.729771],
		[123.389475, 41.730551],
		[123.383394, 41.732261], //苏家屯西路
		[123.378875, 41.733364],
		[123.366506, 41.739205], //曹仲
		[123.364086,41.74065],
		[123.360708,41.745468],
		[123.360852,41.745225],
		[123.355282,41.755276],
		[123.352632,41.758269],
		[123.348985,41.762086],
		[123.34704,41.764124],
		[123.346825,41.764589],
		[123.346906,41.765043],
		[123.3474,41.765443],
		[123.350881,41.766979],
		[123.352803,41.767635],
		[123.354451,41.768049],
		[123.355592,41.768311],
		[123.356912, 41.768991], //吉利湖街
		[123.35816, 41.770242],
		[123.360298, 41.774237],
		[123.364853, 41.778406], //滑翔
		[123.367215, 41.78049],
		[123.367449, 41.781109],
		[123.367485, 41.781728],
		[123.367323, 41.782239],
		[123.366658, 41.783099],
		[123.366371, 41.785977],
		[123.367225, 41.787281], //沈辽路
		[123.372111, 41.793655], //兴华公园
		[123.377106, 41.800458], //铁西广场
		[123.385847, 41.811577], //北二路
		[123.389817, 41.816938],
		[123.390221, 41.819088], //北一路
		[123.390571, 41.8214],
		[123.393204, 41.827421], //皇姑屯
		[123.397228, 41.836316], //淮河街
		[123.396859, 41.843692],
		[123.395539, 41.846984] //怒江公园
	]
//10号线
var path7 =
	[
		[123.354568, 41.870606], //丁香公园
		[123.355033, 41.867101],
		[123.355914, 41.865154],
		[123.362868, 41.857418], //白山路
		[123.366316, 41.853643],
		[123.37167, 41.845986],
		[123.371634, 41.845448],
		[123.370378, 41.842695], //向工街
		[123.368849, 41.839255],
		[123.368831, 41.838879],
		[123.369173, 41.838516],
		[123.36964, 41.838247],
		[123.381228, 41.835184], //塔湾街
		[123.382324, 41.834888],
		[123.382917, 41.834888],
		[123.383384, 41.834928],
		[123.385109, 41.835963],
		[123.386151, 41.836191],
		[123.397182, 41.836635], //淮河街
		[123.407602, 41.836796], //百鸟公园
		[123.412291, 41.837078],
		[123.418886, 41.837589], //长江街
		[123.436888, 41.838422], //中医药大学
		[123.450111, 41.839013], //松花江街
		[123.464842, 41.839645],
		[123.465884, 41.839524],
		[123.470106, 41.838368], //柳条湖
		[123.479215, 41.836191],
		[123.481085, 41.835681], //北大营街
		[123.482503, 41.834848],
		[123.486078, 41.830897],
		[123.488235, 41.827619], //东北大马路
		[123.492025, 41.821289],
		[123.494847, 41.815066], //滂江街
		[123.4956, 41.813305],
		[123.495708, 41.812741],
		[123.495654, 41.81227],
		[123.495367, 41.811343],
		[123.493157, 41.808332],
		[123.49208, 41.804582], //长安路
		[123.49021, 41.796891],
		[123.489781, 41.796246], //万泉公园
		[123.488288, 41.794135],
		[123.482772, 41.789873],
		[123.481461, 41.788017],
		[123.481479, 41.787358],
		[123.481874, 41.786484],
		[123.484534, 41.784252], //泉园一路
		[123.493877, 41.776426], //文化路东
		[123.500595, 41.770561],
		[123.501906, 41.76884],
		[123.502644, 41.767199], //长青桥
		[123.50825, 41.750894], //浑南大道
		[123.511591, 41.739861], //理工大学
		[123.513405, 41.734868],
		[123.519065, 41.727479] //张沙布
	]

// 3号线
var path10 =
[
	[123.148138,41.674248],
	[123.158702,41.687825],
	[123.166679,41.697036],
	[123.169769,41.69876],
	[123.178824,41.701291],
	[123.196862,41.705708],
	[123.201748,41.70735],
	[123.209582,41.711658],
	[123.213606,41.71322],
	[123.213606,41.71322],
	[123.229919,41.719493],
	[123.244005,41.726816],
	[123.253311,41.733976],
	[123.255602,41.734776],
	[123.257506,41.734992],
	[123.264369,41.73518],
	[123.267244,41.736014],
	[123.268861,41.736142],
	[123.272885,41.737071],
	[123.273981,41.738188],
	[123.273981,41.741862],
	[123.274161,41.742225],
	[123.274691,41.742716],
	[123.275625,41.743133],
	[123.279578,41.744048],
	[123.280754,41.744277],
	[123.289845,41.745643],
	[123.292585,41.746288],
	[123.296385,41.746712],
	[123.301685,41.747627],
	[123.317055,41.753164],
	[123.317055,41.753164],
	[123.318128,41.753214],
	[123.318506,41.752915],
	[123.322261,41.748152],
	[123.323141,41.74758],
	[123.324219,41.747325],
	[123.325791,41.747621],
	[123.330022,41.749309],
	[123.344269,41.756487],
	[123.345985,41.756843],
	[123.345985,41.756843],
	[123.349893,41.757213],
	[123.353719,41.758518],
	[123.368613,41.764209],
	[123.371456,41.765406],
	[123.373486,41.76737],
	[123.375822,41.773301],
	[123.379308,41.776112],
	[123.380026,41.776206],
	[123.393159,41.773207],
	[123.404244,41.77544],
	[123.411521,41.78484],
	[123.412239,41.785042],
	[123.430008,41.777484],
	[123.431104,41.777336],
	[123.464611,41.776811],
	[123.46691,41.775574],
	[123.491524,41.774982],
	[123.500112,41.782836],
	[123.502483,41.784477],
]



//划 地铁线
//1号线
polyline[1] = new qq.maps.Polyline({
	path: a(path1),
	strokeColor: new qq.maps.Color(218,46,46,0.7),
	strokeWeight: 5,
	editable:false,
	map: baiduMap.map
});
//2号线
polyline[2] = new qq.maps.Polyline({
	path: a(path2),
	strokeColor: new qq.maps.Color(255,82,0,0.7),
	strokeWeight: 5,
	editable:false,
	map: baiduMap.map
});
//4号线
polyline[6] = new qq.maps.Polyline({
	path: a(path6),
	strokeColor: new qq.maps.Color(147,102,63,0.7),
	strokeWeight: 5,
	editable:false,
	map: baiduMap.map
});
//6号线
polyline[9] = new qq.maps.Polyline({
	path: a(path9),
	strokeColor: new qq.maps.Color(93,28,179,0.7),
	strokeWeight: 5,
	editable:false,
	map: baiduMap.map
});
//9号线
polyline[4] = new qq.maps.Polyline({
	path: a(path4),
	strokeColor: new qq.maps.Color(57,116,228,0.7),
	strokeWeight: 5,
	editable:false,
	map: baiduMap.map
});
//10号线
polyline[7] = new qq.maps.Polyline({
	path: a(path7),
	strokeColor: new qq.maps.Color(51,167,62,0.7),
	strokeWeight: 5,
	editable:false,
	map: baiduMap.map
});

//3号线
polyline[10] = new qq.maps.Polyline({
	path: a(path10),
	strokeColor: new qq.maps.Color(20,39,138,0.7),
	strokeWeight: 5,
	editable:false,
	map: baiduMap.map
});





/*polyline[2].addEventListener("mouseover",function () {//添加折线交互事件
    if(baiduMap.map.getZoom() <baiduMap.maxRegion && !baiduMap.subwaySwitchFlag) {
        setLineWeigth(2)
        if (baiduMap.subWayList.ActiveId != 2) {
            baiduMap.subWayList.ActiveId = "2";
            baiduMap.subWayList.ActiveText = "二号线";
            baiduMap.subWayStationText.ActiveId = "";
            baiduMap.subWayStationText.ActiveText = "";
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
        }
    }
})
polyline[2].addEventListener("click",function () {
    setLineWeigth(2)
    if(baiduMap.subWayList.ActiveId != 2){
        baiduMap.subWayList.ActiveId = "2";
        baiduMap.subWayList.ActiveText = "二号线";
        baiduMap.subWayStationText.ActiveId = "";
        baiduMap.subWayStationText.ActiveText = "";
        baiduMap.showHouse();
        baiduMap.setHouseHigth();
    }
})
polyline[1].addEventListener("mouseover",function () {
    if(baiduMap.map.getZoom() <baiduMap.maxRegion && !baiduMap.subwaySwitchFlag) {
        setLineWeigth(1)
        if (baiduMap.subWayList.ActiveId != 1) {
            baiduMap.subWayList.ActiveId = "1";
            baiduMap.subWayList.ActiveText = "一号线";
            baiduMap.subWayStationText.ActiveId = "";
            baiduMap.subWayStationText.ActiveText = "";
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
        }
    }
})
polyline[1].addEventListener("click",function () {
    setLineWeigth(1)
    if(baiduMap.subWayList.ActiveId != 1){
        baiduMap.subWayList.ActiveId = "1";
        baiduMap.subWayList.ActiveText = "一号线";
        baiduMap.subWayStationText.ActiveId = "";
        baiduMap.subWayStationText.ActiveText = "";
        baiduMap.showHouse();
        baiduMap.setHouseHigth();
    }
})
polyline[4].addEventListener("mouseover",function () {
    if(baiduMap.map.getZoom() <baiduMap.maxRegion && !baiduMap.subwaySwitchFlag) {
        setLineWeigth(4)
        if (baiduMap.subWayList.ActiveId != 4) {
            baiduMap.subWayList.ActiveId = "4";
            baiduMap.subWayList.ActiveText = "九号线";
            baiduMap.subWayStationText.ActiveId = "";
            baiduMap.subWayStationText.ActiveText = "";
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
        }
    }
})
polyline[4].addEventListener("click",function () {
    setLineWeigth(4)
    if(baiduMap.subWayList.ActiveId != 4){
        baiduMap.subWayList.ActiveId = "4";
        baiduMap.subWayList.ActiveText = "九号线";
        baiduMap.subWayStationText.ActiveId = "";
        baiduMap.subWayStationText.ActiveText = "";
        baiduMap.showHouse();
        baiduMap.setHouseHigth();
    }
})
polyline[6].addEventListener("mouseover",function () {
    if(baiduMap.map.getZoom() <baiduMap.maxRegion && !baiduMap.subwaySwitchFlag){
		setLineWeigth(6)
		if(baiduMap.subWayList.ActiveId != 6){
			baiduMap.subWayList.ActiveId = "6";
			baiduMap.subWayList.ActiveText = "四号线";
            baiduMap.subWayStationText.ActiveId = "";
            baiduMap.subWayStationText.ActiveText = "";
			baiduMap.showHouse();
			baiduMap.setHouseHigth();
		}
    }
})
polyline[6].addEventListener("click",function () {
    setLineWeigth(6)
    if(baiduMap.subWayList.ActiveId != 6){
        baiduMap.subWayList.ActiveId = "6";
        baiduMap.subWayList.ActiveText = "四号线";
        baiduMap.subWayStationText.ActiveId = "";
        baiduMap.subWayStationText.ActiveText = "";
        baiduMap.showHouse();
        baiduMap.setHouseHigth();
    }
})
polyline[7].addEventListener("mouseover",function () {
	if(baiduMap.map.getZoom()<baiduMap.maxRegion && !baiduMap.subwaySwitchFlag){
        setLineWeigth(7)
        if(baiduMap.subWayList.ActiveId != 7){
            baiduMap.subWayList.ActiveId = "7";
            baiduMap.subWayList.ActiveText = "十号线";
            baiduMap.subWayStationText.ActiveId = "";
            baiduMap.subWayStationText.ActiveText = "";
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
        }
	}
});
polyline[7].addEventListener("click",function () {
    setLineWeigth(7)
    if(baiduMap.subWayList.ActiveId != 7){
        baiduMap.subWayList.ActiveId = "7";
        baiduMap.subWayList.ActiveText = "十号线";
        baiduMap.subWayStationText.ActiveId = "";
        baiduMap.subWayStationText.ActiveText = "";
        baiduMap.showHouse();
        baiduMap.setHouseHigth();
    }
});
*/


//1号线
qq.maps.event.addListener(polyline[1],"mouseover",function(){
	if (baiduMap.subWayList.ActiveId != 1) {
		baiduMap.subWayList.ActiveId = "1";
		baiduMap.subWayList.ActiveText = "一号线";
		baiduMap.subWayStationText.ActiveId = "";
		baiduMap.subWayStationText.ActiveText = "";
		baiduMap.showHouse();
		baiduMap.setHouseHigth();
	}
	setLineWeigth(1)
});
qq.maps.event.addListener(polyline[1],"click",function(){
	if(baiduMap.subWayList.ActiveId != 1){
		baiduMap.subWayList.ActiveId = "1";
		baiduMap.subWayList.ActiveText = "一号线";
		baiduMap.subWayStationText.ActiveId = "";
		baiduMap.subWayStationText.ActiveText = "";
		baiduMap.showHouse();
		baiduMap.setHouseHigth();

	}
	baiduMap.newHouseList();
});
//2号线
qq.maps.event.addListener(polyline[2],"mouseover",function(){
	if (baiduMap.subWayList.ActiveId != 2) {
		baiduMap.subWayList.ActiveId = "2";
		baiduMap.subWayList.ActiveText = "二号线";
		baiduMap.subWayStationText.ActiveId = "";
		baiduMap.subWayStationText.ActiveText = "";
		baiduMap.showHouse();
		baiduMap.setHouseHigth();
	}
	setLineWeigth(2)
});
//4号线
qq.maps.event.addListener(polyline[6],"mouseover",function(){
	if (baiduMap.subWayList.ActiveId != 6) {
		baiduMap.subWayList.ActiveId = "6";
		baiduMap.subWayList.ActiveText = "四号线";
		baiduMap.subWayStationText.ActiveId = "";
		baiduMap.subWayStationText.ActiveText = "";
		baiduMap.showHouse();
		baiduMap.setHouseHigth();
	}
	setLineWeigth(6)
});
//6号线
qq.maps.event.addListener(polyline[9],"mouseover",function(){
	if (baiduMap.subWayList.ActiveId != 9) {
		baiduMap.subWayList.ActiveId = "9";
		baiduMap.subWayList.ActiveText = "六号线";
		baiduMap.subWayStationText.ActiveId = "";
		baiduMap.subWayStationText.ActiveText = "";
		baiduMap.showHouse();
		baiduMap.setHouseHigth();
	}
	setLineWeigth(9)
});

//9号线
qq.maps.event.addListener(polyline[4],"mouseover",function(){
	if (baiduMap.subWayList.ActiveId != 4) {
		baiduMap.subWayList.ActiveId = "4";
		baiduMap.subWayList.ActiveText = "九号线";
		baiduMap.subWayStationText.ActiveId = "";
		baiduMap.subWayStationText.ActiveText = "";
		baiduMap.showHouse();
		baiduMap.setHouseHigth();
	}
	setLineWeigth(4)
});
//10号线
qq.maps.event.addListener(polyline[7],"mouseover",function(){
	if (baiduMap.subWayList.ActiveId != 7) {
		baiduMap.subWayList.ActiveId = "7";
		baiduMap.subWayList.ActiveText = "十号线";
		baiduMap.subWayStationText.ActiveId = "";
		baiduMap.subWayStationText.ActiveText = "";
		baiduMap.showHouse();
		baiduMap.setHouseHigth();
	}
	setLineWeigth(7)
});


//3号线
qq.maps.event.addListener(polyline[10],"mouseover",function(){
	if (baiduMap.subWayList.ActiveId != 10) {
		baiduMap.subWayList.ActiveId = "10";
		baiduMap.subWayList.ActiveText = "三号线";
		baiduMap.subWayStationText.ActiveId = "";
		baiduMap.subWayStationText.ActiveText = "";
		baiduMap.showHouse();
		baiduMap.setHouseHigth();
	}
	setLineWeigth(10)
});





function setLineWeigth(currentNum) {
	if(currentNum==""){
        for(var i=1;i<polyline.length;i++){
            if(polyline[i]!= "" && polyline[i]!= null && polyline[i]!= undefined){
				polyline[i].setStrokeWeight(6);
            }
        }
	}else {
        for(var i=1;i<polyline.length;i++){
            if(polyline[i]!= "" && polyline[i]!= null && polyline[i]!= undefined){
                if(i !=currentNum){
                    polyline[i].setStrokeWeight(6);
                }else {
                    polyline[i].setStrokeWeight(12);
                }
            }
        }
	}

}




//----------
//百度经纬度 转 腾讯
function bdMapToTencentMap(lng, lat) {
	var x_pi = 3.14159265358979324 * 3000.0 / 180.0;
	var x = lng - 0.0065;
	var y = lat - 0.006;
	var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
	var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
	var qqLng = z * Math.cos(theta);
	var qqLat = z * Math.sin(theta);
	return {
		lng: qqLng,
		lat: qqLat
	};
}
function a(t){
	let d = [];
	t.forEach(function(latLng) {
		let tencentCoord = bdMapToTencentMap(latLng[0],latLng[1])
		let swappedLatLng = new qq.maps.LatLng(tencentCoord.lat,tencentCoord.lng)
		d.push(swappedLatLng);
	});
	return d
}
