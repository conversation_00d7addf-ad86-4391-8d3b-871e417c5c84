/**
 * Created by Administrator on 2019.09.11
 */
$(function () {
    function changeauto(method) {
        $(".ac_input").autocomplete({
            delay: 20,
            source: function( request, response) {
                var searchInfo = request.term;
                // searchInfo = encodeURI(searchInfo);//如果到线上不好用，则就转换字符试试
                $.ajax({
                    url: "/searchForSubway?key="+method,
                    dataType: "json",
                    type:'post',
                    data: {
                        q: searchInfo
                    },
                    success: function( data ) {
                        // console.log(data);
                        if (data.length > 0) {
                            response($.map(data, function (item) {
                                var highLightTitle = item.name;
                                highLightTitle = highLightTitle.replace(
                                    new RegExp(
                                        "(?![^&;]+;)(?!<[^<>]*)(" +
                                        $.ui.autocomplete.escapeRegex(request.term) +
                                        ")(?![^<>]*>)(?![^&;]+;)", "gi"
                                    ), "<strong>$1</strong>");
                                return {
                                    label:  highLightTitle,
                                    subId: item.ID,
                                    type: item.type,  //type=1 地铁  type=2 小区
                                    value: item.name,
                                    latitude: item.Latitude,
                                    longitude:item.Longitude,
                                    subwayId:item.SubWayID,
                                };
                                var showHeight = window.screen.height;
                                var seachUlHeight = showHeight-100
                            }));
                        } else {
                            var txt = $(".ac_input").attr("name");
                            data = [{"txt":txt}];
                            response($.map(data, function (item) {
                                /*  return {
                                      label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                      tableId: -1,
                                      value:request.term,
                                      projectId:"-1"
                                  }*/
                                var first_Val = $("#ui-id-1 li").eq(0).val();
                                console.log(first_Val)
                                if(txt == undefined && data.length == 1){
                                }
                            }));
                        }
                    }
                });
            },
            select: function( event, ui ) {
                var liData ="";
                console.log(ui.item.type);
                if(ui.item.type == 1){ //type=1是地铁站点
                    baiduMap.search_type = 1;
                    baiduMap.mapType = 2;
                    $("#mapNumQY").show();
                    $("#mapNumDT").hide();
                    $(".noHouseMap").hide();//隐藏表单提醒链接
                    baiduMap.subWayStationText.ActiveId = ui.item.subId;
                    baiduMap.subWayList.ActiveId = ui.item.subwayId; //地铁线线线
                    baiduMap.lineOrSubOrStation =2;
                    baiduMap.SetMap1(ui.item.latitude,ui.item.longitude,baiduMap.maxPlat);
                    baiduMap.clearAllLabels()//清除覆盖物
                    baiduMap.canSeeArea();
                    baiduMap.newHouseList(); //地图站点 及 视域具体小区
                    //解决 选择一条地铁线后，在搜索框输入另一条线路的站点，地图上搜索站点所在线路描线没有显示出来=====
                    if(baiduMap.subWayList.ActiveId == 1){
                        polyline[1].setStrokeWeight(8);
                        polyline[4].setStrokeWeight(0.1);
                        polyline[2].setStrokeWeight(0.1);
                        polyline[7].setStrokeWeight(0.1);
                    }else if(baiduMap.subWayList.ActiveId == 2){
                        polyline[1].setStrokeWeight(0.1);
                        polyline[4].setStrokeWeight(0.1);
                        polyline[2].setStrokeWeight(8);
                        polyline[7].setStrokeWeight(0.1);
                    }else  if(baiduMap.subWayList.ActiveId == 4){
                        polyline[1].setStrokeWeight(0.1);
                        polyline[4].setStrokeWeight(8);
                        polyline[2].setStrokeWeight(0.1);
                        polyline[7].setStrokeWeight(0.1);
                    }else if(baiduMap.subWayList.ActiveId == 7){
                        polyline[1].setStrokeWeight(0.1);
                        polyline[4].setStrokeWeight(0.1);
                        polyline[2].setStrokeWeight(0.1);
                        polyline[7].setStrokeWeight(8);
                    }
                    $(".stationModel").show(); //地铁图标
                    //搜索地铁站 点亮右上角对应地铁线
                    $(".stationModel li").removeClass("hover");
                    $(".stationModel li").css("opacity","0.4");
                    var lineSiteNum =  ui.item.subwayId;
                    $.each($(".stationModel li"),function(){
                        liData = $(this).attr("data");
                        if( liData == lineSiteNum){
                            $(this).addClass("hover")
                        }
                    })

                    $("#metroMapList").hide();//隐藏列表guo
                    $(".showVallge").attr('id','')//清空选中
                    $(".listBtn").hide()//隐藏箭头按钮
                }
                else if(ui.item.type == 2){//type=2是小区
                    baiduMap.search_type = 2;
                    baiduMap.housingEstate = ui.item.subId;
                    baiduMap.lineOrSubOrStation =2;
                    baiduMap.mapType = 1;
                    $("#mapNumQY").hide();
                    $("#mapNumDT").show();
                    baiduMap.SetMap1(ui.item.latitude,ui.item.longitude,baiduMap.maxPlat);
                    baiduMap.clearAllLabels();//清除覆盖物
                    baiduMap.canSeeArea();
                    baiduMap.subWayList.ActiveId = "";
                    // baiduMap.map.removeOverlay(polyline[1]);
                    // baiduMap.map.removeOverlay(polyline[2]);
                    // baiduMap.map.removeOverlay(polyline[4]);
                    // baiduMap.map.removeOverlay(polyline[7]);
                    baiduMap.newHouseList();
                    baiduMap.newHouseListRegionAjax();
                }
            },
            open: function() {
                $( this ).removeClass( "ui- corner-all" ).addClass( "ui-corner-top" );
                $( this ).css("z-index", 100);
            },
            close: function() {
                $( this ).removeClass( "ui-corner-top" ).addClass( "ui-corner-all" );
            }
        });
    };
    //根据搜索框是否有值 判断删除按钮的显隐
    $("#txtkeys").bind("input propertychange",function(event){
        var valx=$(this).val()
        if(valx==""){
            $("#search2017").find("img").hide()
        }else{
            $("#search2017").find("img").show()
        }
    });
    //输入值匹配 下拉结果框
    $(".ac_input").click(function () {
        var txt = $(".ac_input").attr("name");
        changeauto(txt);//需要根据地图找房的类型传key 1-学区，2-地铁，3-现房 新房就是空
    });


    //点击搜索按钮事件》》》》》
    $(".search_btn").click(function () {
        var input = $(".ac_input").val();
        input=stripscript(input);
        //获取下拉列表中第一个值
        var first_Val = $("#ui-id-1 li").eq(0).text();
        if(!first_Val){
            // alert("亲 没有匹配到内容哦")
        }else{
            $.ajax({
                type: "POST",
                url: "/searchForSubway",
                dataType: "json",
                data: {
                    q: input
                },
                async:false,
                success: function (data) {
                    if(data.length > 0){
                        for(var i=0;i<data.length;i++){
                            if(data[i].type == 1){
                                baiduMap.search_type = 1;
                                baiduMap.mapType = 2;
                                $("#mapNumQY").show();
                                $("#mapNumDT").hide();
                                $(".noHouseMap").hide();//隐藏表单提醒链接
                                baiduMap.subWayStationText.ActiveId = data[i].ID;
                                baiduMap.subWayList.ActiveId = data[i].SubWayID; //地铁线线线
                                baiduMap.lineOrSubOrStation =2;
                                baiduMap.SetMap1(data[i].Latitude,data[i].Longitude,baiduMap.maxPlat);
                                baiduMap.clearAllLabels()//清除覆盖物
                                baiduMap.canSeeArea();
                                baiduMap.newHouseList(); //地图右侧小区数据
                                //解决 选择一条地铁线后，在搜索框输入另一条线路的站点，地图上搜索站点所在线路描线没有显示出来=====
                                if(baiduMap.subWayList.ActiveId == 1){
                                    polyline[1].setStrokeWeight(8);
                                    polyline[4].setStrokeWeight(0.1);
                                    polyline[2].setStrokeWeight(0.1);
                                    polyline[7].setStrokeWeight(0.1);
                                }else if(baiduMap.subWayList.ActiveId == 2){
                                    polyline[1].setStrokeWeight(0.1);
                                    polyline[4].setStrokeWeight(0.1);
                                    polyline[2].setStrokeWeight(8);
                                    polyline[7].setStrokeWeight(0.1);
                                }else  if(baiduMap.subWayList.ActiveId == 4){
                                    polyline[1].setStrokeWeight(0.1);
                                    polyline[4].setStrokeWeight(8);
                                    polyline[2].setStrokeWeight(0.1);
                                    polyline[7].setStrokeWeight(0.1);
                                }else if(baiduMap.subWayList.ActiveId == 7){
                                    polyline[1].setStrokeWeight(0.1);
                                    polyline[4].setStrokeWeight(0.1);
                                    polyline[2].setStrokeWeight(0.1);
                                    polyline[7].setStrokeWeight(8);
                                }
                                $(".stationModel").show(); //地铁图标
                                //搜索地铁站 点亮右上角对应地铁线
                                $(".stationModel li").removeClass("hover");
                                $(".stationModel li").css("opacity","0.4");
                                var lineSiteNum =  data[i].SubWayID;
                                $.each($(".stationModel li"),function(){
                                    var liData = $(this).attr("data");
                                    if( liData == lineSiteNum){
                                        $(this).addClass("hover")
                                    }
                                })

                                $("#metroMapList").hide();//隐藏列表guo
                                $(".showVallge").attr('id','')//清空选中
                                $(".listBtn").hide()//隐藏箭头按钮
                            }else if(data[i].type == 2){
                                baiduMap.search_type = 2;
                                $("#mapNumQY").hide();
                                $("#mapNumDT").show();
                                baiduMap.housingEstate = data[i].ID;
                                baiduMap.lineOrSubOrStation =2;
                                baiduMap.mapType = 1;
                                baiduMap.SetMap1(data[i].Latitude,data[i].Longitude,baiduMap.maxPlat);
                                baiduMap.clearAllLabels()//清除覆盖物
                                baiduMap.canSeeArea();
                                baiduMap.subWayList.ActiveId = "";
                                // baiduMap.map.removeOverlay(polyline[1]);
                                // baiduMap.map.removeOverlay(polyline[2]);
                                // baiduMap.map.removeOverlay(polyline[4]);
                                // baiduMap.map.removeOverlay(polyline[7]);
                                baiduMap.newHouseList();
                                baiduMap.newHouseListRegionAjax();
                                break;
                            }
                        }
                    }
                }
            });
        }

        // }

        $(".ui-autocomplete").hide();
        // }; //判断结束
    });

    function stripscript(s)
    {
        var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
        var rs = "";
        for (var i = 0; i < s.length; i++) {
            rs = rs+s.substr(i, 1).replace(pattern, '');
        }
        return rs;
    }

    var keys = '';
    if (keys != "" && keys != "null" && keys != null && keys != "0") {
        if (keys != -1) {
            $(".ac_input").val(keys);
        }
    }
    $(".ac_input").each(function () {
        var thisVal = $(".ac_input").val();    //判断文本框的值是否为空，有值的情况就隐藏提示语，没有值就显示
        if (thisVal != "") { $(".ac_input").siblings("span").hide(); }
        else { $(".ac_input").siblings("span").show(); }
        $(".ac_input").keyup(function () {
            $(".ac_input").siblings("span").hide();
        }).blur(function () {
            var val = $(this).val();
            if (val != "") {
                $(this).siblings("span").hide();
            } else {
                $(this).siblings("span").show();
            }
        });
    });
    //此方法用于搜索框无下拉时enter事件
    $(".ac_input").keydown(function (event) {
        if (event.keyCode == 13) {
            var selecetFlag = true;
            $(".ui-autocomplete").each(function () {
                if($(this).css("display") == "block"){
                    var item = $(this);
                    $(item).find("a").each (function () {
                        if($(this).hasClass("ui-state-focus")){
                            selecetFlag = false;
                            return false;
                        }
                    });
                }
            });
            if(selecetFlag){
                $(".search_btn").click();
            }
        }
    });
    /*  $("#deleteButton").click(function () {
          // $("#search2017 .search .ac_input").val("");
          // $("#deleteButton").hide();
          var redirectUrl =location.pathname;
          if( redirectUrl != null || redirectUrl != undefined || redirectUrl != ''){
              if( redirectUrl.indexOf("search") != -1 ){
                  redirectUrl =   redirectUrl.substr(0,redirectUrl.indexOf("search"));
                  window.location.href = redirectUrl;
              }else {
                  // window.location.reload();
                  $("#search2017 .search .ac_input").val("");
                  $("#deleteButton").hide();
              }
          }
      });
      $("#search2017 .search .ac_input").val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
      $("#search2017 .search .ac_input").keyup(function(){
          $(this).val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
      })*/
});
//设置cookie time按分钟计时
function setCookie(name,value,time) {
    var exp = new Date();
    exp.setTime(exp.getTime() + time*60*1000);
    value =  encodeURI(value)
    document.cookie = name + "="+ value + ";expires=" + exp.toGMTString();
}
function delCookie(name)
{
    var exp = new Date();
    exp.setTime(exp.getTime() - 20000);
    var cval=getCookie(name);
    if(cval!=null)
        document.cookie= name + "="+cval+";expires="+exp.toGMTString();
}
function getCookie(name) {
    var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
    if(arr=document.cookie.match(reg)){
        return unescape(arr[2]);}
    else {
        return null;
    }
}

