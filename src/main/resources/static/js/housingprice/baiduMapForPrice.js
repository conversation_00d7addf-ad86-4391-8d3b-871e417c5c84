$(document).ready(function () {
    var a = $("#longtitude").val();
    var b = $("#latitude").val();
    var chooseType = $("#chooseType").val()
    var c = chooseType == 1 ? 13 : (chooseType == 2 ? 15 : 17);   //初始化级别：13区域  15 板块  17 小区
    // var c = $("#zoomLevel").val();
    Vue.config.productionTip = false;
    var baiduMap = new Vue({
        el: '#map',
        data: {
            houseType: 2,//地图类型 1  新房            2  二手房            3 租房
            map: {},
            mapZoom: c,//
            maxRegion: 14,//地图区域和房源分界线  二手房租房 区域和板块的分界线
            maxPlat: 16,//地图板块和房源分界线
            gapZoom: -1,//与级别的差
            lngRight: "", //四个坐标点
            lngLeft: "",
            latTop: "",
            latBottom: "",
            scroll: true,//加载开关
            housingEstate: "",//小区id
            zoomType: 1,//地图显示级别  1区域  2 板块  3房源
            windowWidth: window.innerWidth, //
            height: window.innerHeight - 400, // //50是主导航栏高度
            listwidth: 420, //左侧列表宽度
            showSelectList: -1, //筛选项当前显示的是哪一个  -1代表没有列表显示
            regionList: { //区域
                ActiveText: '区域',
                ActiveId: "",
                content: [{
                    id: "",
                    name: ''
                }]
            },
            platList: { //板块
                ActiveText: '板块',
                ActiveId: "",
                content: [{
                    id: "",
                    name: ''
                }]
            },
            priceList: { //新房价格区间 二手房总价
                ActiveText: '价格',
                ActiveId: "",
                content: [{
                    id: "",
                    name: ''
                }]
            },

            newHousList: [], //房源列表

        },
        computed: {
            mapWidth: function () {
                var mapWidth;
                if (this.houseType == 2) {
                    mapWidth = this.windowWidth - this.listwidth
                } else {
                    mapWidth = this.windowWidth - 60 - this.listwidth
                }
                return mapWidth
            }
        },
        methods: {
            //初始化
            init: function () {
                baiduMap.listwidth = baiduMap.houseType == 1 ? 420 : 530;
                baiduMap.map = new BMap.Map("baiduMap", {enableMapClick: false,minZoom:11,maxZoom:17});//创建地图并且禁用弹出信息框（最大100米，最小10公里）
                var point = new BMap.Point(a, b); //中心点
                baiduMap.map.centerAndZoom(point, baiduMap.mapZoom); // 编写自定义函数，创建标注
                baiduMap.map.addControl(new BMap.NavigationControl()); //缩放尺
                baiduMap.map.addControl(new BMap.ScaleControl()); //比例尺
                // var myDis = new BMapLib.DistanceTool(baiduMap);//测距
                baiduMap.showHouse() //初始化第一次获取地图数据
                //移动时重新获取数据
                baiduMap.map.addEventListener("moveend", function () {
                    baiduMap.housingEstate = "";
                    baiduMap.showHouse();
                    baiduMap.showSelectList = -1;
                })
                //窗口大小变化重新计算高度
                window.onresize = function () {
                    baiduMap.height = window.innerHeight;
                }
            },
            //设置地图中心点
            SetMap: function (lat, lng, size) {
                baiduMap.showHouse()
                baiduMap.map.setZoom(size);
                baiduMap.map.bdpoit = new BMap.Point(lng, lat);
                baiduMap.map.panTo(baiduMap.map.bdpoit); //将地图移动到点
            },
            //构造 覆盖物
            overlay: function (point, text, mouseoverText) {
                this._point = point;
                this._text = text;
                this._overText = mouseoverText;
            },


            //获取地图房源
            newHouseList: function () {
                if (baiduMap.houseType == 2) {//二手房获取地图点
                    ajax({
                        type: "POST",
                        url: "https://ltapi.fangxiaoer.com/apiv1/house/viewSubPriceMap",
                        dataType: "json",
                        data: {
                            zoomLevel: baiduMap.map.getZoom() - baiduMap.gapZoom,
                            regionId: baiduMap.regionList.ActiveId,//区域id
                            platId: baiduMap.platList.ActiveId,//板块id
                            leftLng: (baiduMap.map.getZoom() - baiduMap.gapZoom) > baiduMap.maxRegion ? baiduMap.lngLeft : "",//四个坐标点  接口名有错误（错着写是对的！！）
                            leftLat: (baiduMap.map.getZoom() - baiduMap.gapZoom) > baiduMap.maxRegion ? baiduMap.latTop : "",
                            rightLng: (baiduMap.map.getZoom() - baiduMap.gapZoom) > baiduMap.maxRegion ? baiduMap.lngRight : "",
                            rightLat: (baiduMap.map.getZoom() - baiduMap.gapZoom) > baiduMap.maxRegion ? baiduMap.latBottom : ""
                        },
                        beforeSend: function () {
                        },
                        success: function (data) {

                            if (baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxPlat) {
                                for (var i = 0; i < data.content.length; i++) {
                                    var priceStr  = "";
                                    if(data.content[i].price != 0){
                                        priceStr =  data.content[i].price+'元/m²';
                                    }else{
                                        priceStr = "暂无资料";
                                    }

                                    baiduMap.showRegionOverlay(new BMap.Point(data.content[i].longitude, data.content[i].latitude), data.content[i].name, priceStr, "", baiduMap.regionList.id, data.content[i].id, data.content[i].id,data.content[i].regionId);
                                }
                            } else {
                                for (var i = 0; i < data.content.length; i++) {
                                    var priceStr  = "";
                                    if(data.content[i].price != 0){
                                        priceStr =  data.content[i].price+'元/m²';
                                    }else{
                                        priceStr = "暂无资料";
                                    }
                                    baiduMap.showRegionOverlay(new BMap.Point(data.content[i].longtitude, data.content[i].latitude), data.content[i].name, priceStr, "", data.content[i].id, data.content[i].id, "",data.content[i].regionId);
                                }
                            }
                        },
                        error: function (data) {
                            console.log(data)
                        }
                    });
                }

            },

            //定义区域或板块的 覆盖物
            showRegionOverlay: function (point, text, num, mapUrl, regionId, subId, housingId,realRegionId) {
                ComplexCustomOverlay.prototype = new BMap.Overlay();
                ComplexCustomOverlay.prototype.initialize = function (map) {
                    this._map = map;
                    var div = this._div = document.createElement("div");
                    div.style.position = "absolute";
                    div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
                    var span = this._span = document.createElement("span");
                    div.appendChild(span);
                    this._text != undefined && this._text.length > 5 ? span.className = "shadow" : ""
                    span.appendChild(document.createTextNode(this._text));
                    var that = this;
                    var arrow = this._arrow = document.createElement("div");
                    baiduMap.houseType == 1
                    if (baiduMap.houseType == 1) {
                        baiduMap.map.getZoom() - baiduMap.gapZoom <= baiduMap.maxRegion ? div.className = "showRegion" : div.className = "showHouse"
                    } else if ((baiduMap.houseType == 2) || (baiduMap.houseType == 3)) {
                        if (baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxPlat) {
                            div.className = "showHouse"
                        } else if (baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxRegion) {
                            div.className = "showPlat"
                        } else {
                            div.className = "showRegion"
                        }
                    }
                    div.style.backgroundColor = "#ff5200";
                    var span1 = this._span = document.createElement("b");
                    div.appendChild(span1);
                    span1.appendChild(document.createTextNode(this._overText));
                    arrow.style.position = "absolute";
                    arrow.style.width = "11px";
                    arrow.style.height = "10px";
                    arrow.style.top = "22px";
                    arrow.style.left = "20px";
                    div.appendChild(arrow);
                    div.onmouseover = function () {
                        if (subId != "") {
                            this.style.backgroundColor = "#2a95ff";
                            // baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxRegion ? this.className = "showPlat" : ""
                        } else {
                            this.style.backgroundColor = "#41a8f3";
                            if (baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxPlat) {
                                div.className = "showHouseHover"
                            } else if (baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxRegion) {
                                // baiduMap.houseType == 1 ? div.className = "showHouseHover" : div.className = "showPlat"
                            }
                        }
                        this.style.zIndex = "99999";
                    }
                    div.onmouseout = function () {
                        if (subId != "") {
                            this.style.backgroundColor = "#ff5200";
                            // baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxRegion ? this.className = "showPlat" : ""
                            this.style.zIndex = "99900"
                        } else {
                            this.style.backgroundColor = "#ff5200";
                            if (baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxPlat) {
                                div.className = "showHouse"
                            } else if (baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxRegion) {
                                // baiduMap.houseType == 1 ? div.className = "showHouse" : div.className = "showPlat"
                            }
                            this.style.zIndex = ""
                        }
                    }
                    var clickPoint = this._point
                    var urlText = mapUrl
                    var clickText = text
                    div.onclick = function () {
                        if (baiduMap.map.getZoom() - baiduMap.gapZoom <= baiduMap.maxRegion) {
                            window.location.href = "/housingprice/r"+regionId+'#areaMap';//区域regionId    点击跳转对应连接加载页面
                            baiduMap.SetMap(clickPoint.lat, clickPoint.lng, baiduMap.maxRegion)
                            baiduMap.regionList.ActiveId = regionId
                            baiduMap.regionList.ActiveText = clickText

                        } else {
                            if (( baiduMap.map.getZoom() - baiduMap.gapZoom <= baiduMap.maxPlat) && (baiduMap.map.getZoom() - baiduMap.gapZoom > baiduMap.maxRegion) && ((baiduMap.houseType == 2) || (baiduMap.houseType == 3))) {
                                window.location.href = "/housingprice/j"+subId+"-r"+ realRegionId+'#areaMap';// 板块platId
                                baiduMap.SetMap(clickPoint.lat, clickPoint.lng, baiduMap.maxPlat)

                            } else if ((baiduMap.houseType == 2) || (baiduMap.houseType == 3)) {
                                baiduMap.housingEstate = housingId;
                                baiduMap.showHouse();
                                var newUrl  = "/saleVillages/"+housingId+"/index.htm";//小区housingId
                                window.open(newUrl);
                            }
                        }
                    }
                    baiduMap.map.getPanes().labelPane.appendChild(div);
                    return div;
                }
                ComplexCustomOverlay.prototype.draw = function () {
                    var map = this._map;
                    var pixel = map.pointToOverlayPixel(this._point);
                    this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
                    this._div.style.top = pixel.y - 30 + "px";

                }
                var myCompOverlay = new ComplexCustomOverlay(point, text, num)
                baiduMap.map.addOverlay(myCompOverlay);
            },
            //每次操作后 重新获取所有数据
            showHouse: function () {
                baiduMap.map.clearOverlays(); //清除覆盖物
                var showPoint = this.map.getBounds();
                var bssw = showPoint.getSouthWest() //可视区域左下角
                var bsne = showPoint.getNorthEast() //可视区域右上角
                this.lngRight = bsne.lng
                this.lngLeft = bssw.lng
                this.latTop = bsne.lat
                this.latBottom = bssw.lat
                this.newHouseList()
                if (baiduMap.isSubWay != "") {
                    baiduMap.map.addOverlay(polyline[baiduMap.ActiveId]);

                }
            },
        }
    })

//构造自定义覆盖物
    function ComplexCustomOverlay(point, text, num) {
        this._point = point;
        this._text = text;
        this._overText = num;
    }

    baiduMap.init()
});
