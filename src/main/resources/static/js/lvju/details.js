$(document).ready(function() {

				var yy = 0
				var uu = 0;
				lunbo_wei(yy)

				var time

				function lunbo_wei(number) {
					var num = $(".map .map_sun .content_sun .left ul ").eq(number).find("li").length;
					$(".map .map_sun .content_sun .left .lunbo").css("top", "0px")
					if(num == 1) {
						return
					}
					var h = $(".map .map_sun .content_sun .left ul ").eq(number).find("li").height();
					$(".map .map_sun .content_sun .left .show").height(h);
					$(".map .map_sun .content_sun .left ul ").eq(number).find("li:last").after($(".map .map_sun .content_sun .left ul ").eq(number).find("li:first").clone());
					$(".map .map_sun .content_sun .left .show .lunbo").height(h * ($(".map .map_sun .content_sun .left .show ul ").eq(number).find("li").height().length + 1));

					time = setInterval(
						function() {
							if(uu < $(".map .map_sun .content_sun .left .show ul ").eq(number).find("li").length - 1) {
								uu++
								$(".map .map_sun .content_sun .left .show .lunbo").animate({
									top: uu * (-h)
								}, 500)
							} else {
								uu = 0
								$(".map .map_sun .content_sun .left .show .lunbo").css("top", "0")
								uu++
								$(".map .map_sun .content_sun .left .show .lunbo").animate({
									top: uu * (-h)
								}, 500)
							}

						}, 3000
					)
				}

				$(".map .content .btn ul li").click(
					function() {

						uu = 0;
						if($(this).hasClass("color")) {
							clearInterval(time)
							$(".map .content .btn ul li").removeClass("color")
							$(".map .map_sun .content_sun .left ul").hide()
							$(".map .map_sun .content_sun .left ul").eq("0").show()
							lunbo_wei(0)
						} else {
							clearInterval(time)
							$(".map .content .btn ul li").removeClass("color")
							$(this).addClass("color")
							yy = $(".map .content .btn ul li").index(this) + 1
							$(".map .map_sun .content_sun .left ul").hide()
							$(".map .map_sun .content_sun .left ul").eq(yy).show()

							lunbo_wei(yy)
						}
					})
				
				
				
   
  
		})
