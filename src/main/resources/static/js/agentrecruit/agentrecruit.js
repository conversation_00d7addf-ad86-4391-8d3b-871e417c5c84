//老版样式js
$(function() {

	$(".searchMapBtn").click(function() {
		var searchNames = $("#searchNames").val();
		searchNames = stripscript(searchNames);
		var search = "";
		if(searchNames == null || searchNames == undefined || searchNames == '') {
			search = "";
		} else {
			search = "search=" + searchNames;
		}
		sessionStorage.setItem("search", searchNames);

		var types = '';
		var type1 = sessionStorage.getItem("j");
		var type2 = sessionStorage.getItem("r");
		var type3 = sessionStorage.getItem("s");
		var type4 = sessionStorage.getItem("e");
		var type5 = sessionStorage.getItem("w");
		var type6 = sessionStorage.getItem("search");

		if(type1 != null && type1 != '') {
			types += 'j' + type1 + '-';
		}
		if(type2 != null && type2 != '') {
			types += 'r' + type2 + '-';
		}
		if(type3 != null && type3 != '') {
			types += 's' + type3 + '-';
		}
		if(type4 != null && type4 != '') {
			types += 'e' + type4 + '-';
		}
		if(type5 != null && type5 != '') {
			types += 'w' + type5 + '-';
		}
		if(type6 != null && type6 != '') {
			types += 'search=' + type6;
		}

		window.location.href = "/agentRecruit/" + types;
	});

	function stripscript(s) {
		var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
		var rs = "";
		for(var i = 0; i < s.length; i++) {
			rs = rs + s.substr(i, 1).replace(pattern, '');
		}
		return rs;
	}

	//此方法用于搜索框无下拉时enter事件
	$("#searchNames").keydown(function(event) {
		if(event.keyCode == 13) {
			var selecetFlag = true;
			/*  $(".ui-autocomplete").each(function () {
			      if($(this).css("display") == "block"){
			          var item = $(this);
			          $(item).find("a").each (function () {
			              if($(this).hasClass("ui-state-focus")){
			                  selecetFlag = false;
			                  return false;
			              }
			          });
			      }
			  });*/
			if(selecetFlag) {
				$(".searchMapBtn").click();
			}
		}
	});

	// 点击职位，清空搜索框
	$(".jopTypeLi a").click(function() {
		$("#searchNames").val("");
	});

	$(".searchMapInput").val() != "" ? $("#deleteButton").show() : $("#deleteButton").hide()
	$(".searchMapInput").keyup(function() {
		$(this).val() != "" ? $("#deleteButton").show() : $("#deleteButton").hide()
	})
	$("#deleteButton").click(function() {
		var redirectUrl = location.pathname;
		sessionStorage.removeItem("search");
		if(redirectUrl != null || redirectUrl != undefined || redirectUrl != '') {
			if(redirectUrl.indexOf("search") != -1) {
				redirectUrl = redirectUrl.replace(/[-]*(search).*$/, '');
				window.location.href = redirectUrl;
			} else {
				$(".searchMapInput").val("");
				$("#deleteButton").hide();
			}
		}
	});
});

//2021-3-26改版样式

$(document).ready(function() {
	 var newProjectUlr = window.location.pathname;
	 if(newProjectUlr=="/agentRecruit"|| newProjectUlr=="/agentRecruit/"){
	 	sessionStorage.clear();
	 }
	
	$(".clear_option").click(function() {
		sessionStorage.clear();
		window.location.href = "/agentRecruit/";
	})

	$(".arrEach ul li").each(function() {
		this.onmouseenter = function() {
			$(this).find(".arlLeftLiR").hide();
			$(this).find(".arrSq").show();
			$(this).find(".arrSq").addClass("fadeInRight");

		}
		this.onmouseleave = function() {
			$(this).find(".arlLeftLiR").show();
			$(this).find(".arrSq").hide();
			$(this).find(".arrSq").removeClass("fadeInRight");
		}
	})
	var num = 0;
	var senum = 0;

	$(".cs-options").each(function() {

		this.onmouseenter = function() {
			num = 1;
			senum = 0;

		}
		this.onmouseleave = function() {
			if(num == 1 && senum == 0) {
				num = 0;
				var opid = this.id.substr(this.id.length - 1, 1) - 1;
				$(".option").find(".cs-active").removeClass("cs-active");

			}
		}
	})
	$(".cs-select").each(function() {
		this.onmouseenter = function() {
			senum = 1;
			num = 0;

		}
		this.onmouseleave = function() {
			if(senum == 1 && num == 0) {
				senum = 0;
				$(".option").find(".cs-active").removeClass("cs-active");

			}
		}
	})

	var jtype = sessionStorage.getItem("j");
	var rtype = sessionStorage.getItem("r");
	var stype = sessionStorage.getItem("s");
	var etype = sessionStorage.getItem("e");
	var wtype = sessionStorage.getItem("w");

	var jname = sessionStorage.getItem("jname");
	var rname = sessionStorage.getItem("rname");
	var sname = sessionStorage.getItem("sname");
	var ename = sessionStorage.getItem("ename");
	var wname = sessionStorage.getItem("wname");

	$("#option_1 ul").find("li").removeClass("cs-selected");
	$("#option_2 ul").find("li").removeClass("cs-selected");
	$("#option_3 ul").find("li").removeClass("cs-selected");
	$("#option_4 ul").find("li").removeClass("cs-selected");
	$("#option_5 ul").find("li").removeClass("cs-selected");

	if(jtype != null && jtype != '') {
		$("#type_1").html(jname);
		$("#option_1 ul").find("li").each(function() {
			if($(this).attr("data-id") == jtype) {
				$(this).addClass("cs-selected");
			}
		})
	} else {
		$("#option_1 ul").find("li:first-child").addClass("cs-selected");
		$("#type_1").html("职位");

	}
	if(rtype != null && rtype != '') {

		$("#type_2").html(rname);
		$("#option_2 ul").find("li").each(function() {
			if($(this).attr("data-id") == rtype) {
				$(this).addClass("cs-selected");
			}

		})
	} else {
		$("#option_2 ul").find("li:first-child").addClass("cs-selected");
		$("#type_2").html("地点");

	}

	if(stype != null && stype != '') {

		$("#type_3").html(sname);
		$("#option_3 ul").find("li").each(function() {
			if($(this).attr("data-id") == stype) {
				$(this).addClass("cs-selected");
			}

		})
	} else {
		$("#option_3 ul").find("li:first-child").addClass("cs-selected");
		$("#type_3").html("薪资");

	}

	if(etype != null && etype != '') {

		$("#type_4").html(ename);
		$("#option_4 ul").find("li").each(function() {
			if($(this).attr("data-id") == etype) {
				$(this).addClass("cs-selected");
			}

		})
	} else {
		$("#option_4 ul").find("li:first-child").addClass("cs-selected");
		$("#type_4").html("工作经验");

	}

	if(wtype != null && wtype != '') {

		$("#type_5").html(wname);
		$("#option_5 ul").find("li").each(function() {
			if($(this).attr("data-id") == wtype) {
				$(this).addClass("cs-selected");
			}

		})
	} else {
		$("#option_5 ul").find("li:first-child").addClass("cs-selected");
		$("#type_5").html("福利");

	}

	$(".sel_wrap").on("change", function() {
		var o;
		var opt = $(this).find('option');
		opt.each(function(i) {
			if(opt[i].selected == true) {
				o = opt[i].innerHTML;
			}
		})
		$(this).find('label').html(o);
	}).trigger('change');

})

//储存数据  
function setInfo(mainId) {
	var storage = window.sessionStorage;
	storage.setItem('name', mainId);
}
//显示数据  
function show() {
	var storage = window.sessionStorage;
	var str = "your name is " + storage.getItem("name");
	document.getElementById("text").value = str;
}

function gradeChange(eid, id, name) {
	//id 为-1时，选择不限
	if(id == -1) {
		id = '';
	}

	switch(eid) {
		case 1:
			sessionStorage.setItem("j", id);
			sessionStorage.setItem("jname", name);

			break;
		case 2:
			sessionStorage.setItem("r", id);
			sessionStorage.setItem("rname", name);
			break;
		case 3:
			sessionStorage.setItem("s", id);
			sessionStorage.setItem("sname", name);
			break;
		case 4:
			sessionStorage.setItem("e", id);
			sessionStorage.setItem("ename", name);
			break;
		case 5:
			sessionStorage.setItem("w", id);
			sessionStorage.setItem("wname", name);
			break;
		default:
			break;

	}
	var types = '';
	var type1 = sessionStorage.getItem("j");
	var type2 = sessionStorage.getItem("r");
	var type3 = sessionStorage.getItem("s");
	var type4 = sessionStorage.getItem("e");
	var type5 = sessionStorage.getItem("w");
	var type6 = sessionStorage.getItem("search");

	if(type1 != null && type1 != '') {
		types += 'j' + type1 + '-';
	}
	if(type2 != null && type2 != '') {
		types += 'r' + type2 + '-';
	}
	if(type3 != null && type3 != '') {
		types += 's' + type3 + '-';
	}
	if(type4 != null && type4 != '') {
		types += 'e' + type4 + '-';
	}
	if(type5 != null && type5 != '') {
		types += 'w' + type5 + '-';
	}
	if(type6 != null && type6 != '') {
		types += 'search=' + type6;
	}
	window.location.href = "/agentRecruit/" + types;

}