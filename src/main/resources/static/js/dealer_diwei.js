$(function(){
    var onsell;
    $.ajax({
        type: "post",
        url: "/getOnlineJson",
        data: {
            projectId: projectId
        },
        async: false,
        scriptCharset: 'utf-8',
        dataType: "json",
        success: function (data) {
            onsell = data;
            if (onsell != null) {
                $("#online").show();
            } else {

            }
        }
    });

    var animals=new Array();
    for(var i=0;i<onsell.length;i++){
        animals[i]=""+onsell[i].Staging+""

    };
    $.unique(animals);
    $.unique(animals);
    animals = animals.sort(function(a,b){
        return a-b;
    });
    var qishu="";
    // for(var i=0;i<animals.length;i++){      //循环期数
    //     qishu=qishu+"<li  rel='"+animals[i]+"'>"+animals[i]+"期产品</li>"
    // }
    // for(var i=0;i<onsell.length;i++){
    //     if(onsell[i].SpecialRoom.length>0){
    //         qishu=qishu+"<li  rel='101'>特价房</li>"
    //         break;
    //     }
    // }
    if(animals.length=="1"){
        qishu=qishu
    }else if(animals.length!="0"){
        qishu="<li  rel='100'>全部</li>"+qishu
    }

    $(".zsxx_list ul").html(qishu)
    $(".zsxx_list ul li").eq(0).addClass("hover")

    var fxe_status = $("#fxe_status").val();
    if (fxe_status == "vip") {
        $(".mappy li").click(function(){
            $(".mappy_info").show();
            var showid=$(this).attr("showid");
            var AA = new Array("零", "一", "二", "三", "四", "五", "六", "七", "八", "九");
            for (var i=0;i<onsell.length;i++){
                if(onsell[i].showID==showid){
                    var huxing=""
                    $(".mappy_t").html(onsell[i].BuildID)
                    $(".mappy_xx .n1").html("<p>开盘时间</p>"+(onsell[i].Opening == null? "暂无资料":onsell[i].Opening));
                    $(".mappy_xx .n2").html("<p>入住时间</p>"+(onsell[i].Stay == null? "暂无资料":onsell[i].Stay));
                    $(".mappy_xx .n3").html("<p>单　　元</p>"+(onsell[i].units == 0? "暂无资料":onsell[i].units+"个"));
                    $(".mappy_xx .n4").html("<p>层　　数</p>"+(onsell[i].layers == 0 ?"暂无资料":onsell[i].layers+"层"));
                    $(".mappy_xx .n5").html("<p>户　　数</p>"+(onsell[i].holds == 0 ?"暂无资料": onsell[i].holds+"户"));
                    $(".mappy_xx .n6").html("<p>梯户配比</p>"+(onsell[i].elevator == null?"暂无资料": onsell[i].elevator));

                    if($(".zsxx_list li.hover").attr("rel")!="101"){
                        for(var t=0;t<onsell[i].BuildInfo.length;t++){
                            huxing=huxing+"<li><a href='/house/layout/pid" + projectId +"-pt" + projectType +"-l" + onsell[i].BuildInfo[t].layId+"' target='_blank'><p>"+AA[onsell[i].BuildInfo[t].RoomType]+"居</p><p>"+onsell[i].BuildInfo[t].RoomName+"</p><p>"+onsell[i].BuildInfo[t].Area+"平</p></a></li>"
                        }
                        $("#myScroll ul").html(huxing);
                        $(".mappy_hx").html("户　　型");
                    }else{
                        for(var t=0;t<onsell[i].SpecialRoom.length;t++){
                            huxing=huxing+"<li><a href='/house/online/"+ projectId + "-" + projectType + "-" + onsell[i].SpecialRoom[t].roomId + ".htm" + "' target='_blank'><p>"+onsell[i].SpecialRoom[t].RoomLocation+"</p><p>"+onsell[i].SpecialRoom[t].Area+"平</p><p>"+onsell[i].SpecialRoom[t].PrePrice.toFixed(2)+"万</p></a></li>"
                        }
                        $("#myScroll ul").html(huxing);
                        $(".mappy_hx").html("特价房源");
                    }
                }
            };
        })
    }


    $(".mappy_close").click(function(){
        $(".mappy_info").hide();
    })

    $(function(){
        $(".zsxx_list li").click(function(){
            $(".zsxx_list li").removeClass("hover");
            $(".mappy_info").hide();
            $(this).addClass("hover")
            $(".picbox li").hide();
            var rel=$(this).attr("rel");
            list(rel);
            for (var i=0;i<onsell.length;i++){
                if(rel==onsell[i].Staging){
                    $(".picbox li").each(function(){
                        if($(this).attr("showid")==onsell[i].showID){
                            $(this).show()
                        }
                    })
                }else if(rel=="100"){
                    $("#worldMap li").show()
                }else if(rel=="101"){

                    if(onsell[i].SpecialRoom.length>0){
                        $(".picbox li[showid="+onsell[i].showID+"]").show()
                    }
                }
            }

        })
    });



    //右侧列表
    list(rel=100);
    function list(rel){
        var h=1, tex="",li,t1,list="'list'",t2,li_house=""
        $(".xiala").html("")
        for(var i=1;i<6;i++){
            li=""
            s=0;
            for(var t=0;t<onsell.length;t++){
                for(var b=0;b<onsell[t].BuildInfo.length;b++){
                    if(onsell[t].BuildInfo[b].RoomType==i){
                        if(rel==onsell[t].Staging || rel=="100"){
                            li=li+"<li><a href='/house/layout/pid" + projectId +"-pt" + projectType +"-l" + onsell[t].BuildInfo[b].layId+"' target='_blank'><p class='w110'>"+onsell[t].BuildInfo[b].RoomName+"</p><p>"+onsell[t].BuildInfo[b].RoomType+"室"+onsell[t].BuildInfo[b].HallType+"厅"+onsell[t].BuildInfo[b].GuardType+"卫</p><p>"+onsell[t].BuildInfo[b].Area+"平</p></a></li>";
                            s++
                        }
                    }
                }
                if(rel==101){
                    for(var b=0;b<onsell[t].SpecialRoom.length;b++){
                        if(onsell[t].SpecialRoom[b].RoomType==i){
                            li=li+"<li><a href='/house/online/"+ projectId + "-" + projectType + "-" + onsell[t].SpecialRoom[b].roomId + ".htm" + "' target='_blank'><p class='w110'>"+onsell[t].SpecialRoom[b].RoomLocation+"</p><p>"+onsell[t].SpecialRoom[b].Area+"平</p><p>"+onsell[t].SpecialRoom[b].PrePrice.toFixed(2)+"万</p></a></li>";
                            s++
                        }
                    }
                }
            }
            if(li!=""){

                if(rel==101){
                    t2="<li><p>房源位置</p><p>面积</p><p>价格</p></li>";
                    tt="<div id=\"list"+h+"\" onclick=\"setTab("+list+","+h+",8)\"  class=\"qh\">"+i+"室("+s+"户)</div>"
                }else{
                    t2="<li><p>户型楼栋</p><p>厅室</p><p>面积</p></li>";
                    tt="<div id=\"list"+h+"\" onclick=\"setTab("+list+","+h+",8)\"  class=\"qh\">"+i+"室("+s+"户)</div>"
                }
                t1="<div id='con_list_"+h+"' class='xiala_gd hid'><ul>aaaaa</ul></div>";
                $(".xiala").append(tt);
                $("#list"+h).after(t1);
                t2=t2+li
                $("#con_list_"+h).find("ul").html(t2);
                h++
            }
        }
        $("#list1").addClass("hover");
        $("#con_list_1").show();
        gaodu();
    }
    function gaodu(){
        var v = $(".xiala_gd").length;
        switch (v) {
            case 1: $(".xiala_gd").height(322); break;
            case 2: $(".xiala_gd").height(289); break;
            case 3: $(".xiala_gd").height(256); break;
            case 4: $(".xiala_gd").height(223); break;
            case 5: $(".xiala_gd").height(190); break;
        }
    }

//定位图片位置
    if($(".pro_name p").html()=="龙湖西府原著"){
        $("#worldMap").css("top","-154px")
    }
})
function setTab(name, cursel, n) {
    for (i = 1; i <= n; i++) {
        var menu = document.getElementById(name + i);
        var con = document.getElementById("con_" + name + "_" + i);
        menu.className = i == cursel ? "hover" : "";
        con.style.display = i == cursel ? "block" : "none";
    }
}
