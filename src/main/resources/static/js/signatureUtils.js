// 后端API配置
// const API_CONFIG = {
//     // baseUrl: 'http://10.7.0.4:8000/services/sms-service', // 短信微服务地址
//     // // baseUrl: 'https://gateway.fangxiaoer.cn/services/sms-service', // 短信微服务地址
//     // endpoints: {
//     //     sendSms: '/api/v1/public/sms/code',
//     //     verifySms: '/api/v1/public/sms/code/verification',
//     //     verifyCaptcha: '/api/v1/public/captcha/verify'
//     // },
//
//     // API签名配置
//     signature: {
//         enabled: true, // 是否启用签名验证，设为true启用
//         // secret: 'c56A26Qtr4' // 签名密钥，需与后端保持一致
//         secret: '123456'
//     }
// };



// API签名工具类
class SignatureUtils {
    // 生成随机Nonce
    static generateNonce() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15) +
            Date.now().toString(36);
    }

    // 生成时间戳（秒级）
    static generateTimestamp() {
        return Math.floor(Date.now()).toString();
    }

    // HMAC-SHA256签名（使用Web Crypto API，返回 base64）
    static async generateSignature(data, secret) {
        try {
            const encoder = new TextEncoder();
            const keyData = encoder.encode(secret);  // 密钥
            const messageData = encoder.encode(data); // 原文数据

            // 导入为 HMAC 密钥
            const cryptoKey = await crypto.subtle.importKey(
                'raw',
                keyData,
                { name: 'HMAC', hash: 'SHA-256' },
                false,
                ['sign']
            );

            // 执行 HMAC-SHA256 签名
            const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData);

            // 将 ArrayBuffer 转成 Uint8Array，再转成 base64
            return btoa(String.fromCharCode(...new Uint8Array(signature)));
        } catch (error) {
            console.error('签名生成失败:', error);
            throw new Error('签名生成失败: ' + error.message);
        }
    }

    // 构建签名数据字符串
    static buildSignatureData(method, path, query, body, timestamp, nonce) {
        return method + "\n" +
            path + "\n" +
            (query || "") + "\n" +
            (body || "") + "\n" +
            timestamp + "\n" +
            nonce;
    }
}

// 构建带签名的请求头
async function buildSignedHeaders(method, path, query, body = null) {
    // if (!API_CONFIG.signature.enabled) {
    //     console.log('签名验证已禁用，使用普通请求');
    //     return {
    //         'Content-Type': 'application/json'
    //     };
    // }

    console.log('开始构建API签名...');

    try {
        const timestamp = SignatureUtils.generateTimestamp();
        const nonce = SignatureUtils.generateNonce();

        // 处理请求体
        let bodyStr = '';
        if (body) {
            if (typeof body === 'string') {
                try {
                    // 确保JSON格式标准化
                    const jsonObj = JSON.parse(body);
                    bodyStr = JSON.stringify(jsonObj);
                } catch {
                    bodyStr = body;
                }
            } else {
                bodyStr = JSON.stringify(body);
            }
        }

        // 构建签名数据
        const dataToSign = SignatureUtils.buildSignatureData(
            method.toUpperCase(),
            path,
            query,
            bodyStr,
            timestamp,
            nonce
        );

        console.log('签名数据字符串:', dataToSign);

        // 生成签名
        const signature = await SignatureUtils.generateSignature(dataToSign, "c56A26Qtr4");//123456

        console.log('生成的签名:', signature);
        console.log('签名头信息:', {
            'X-Signature': signature,
            'X-Timestamp': timestamp,
            'X-Nonce': nonce
        });

        return {
            'X-Signature': signature,
            'X-Timestamp': timestamp,
            'X-Nonce': nonce
        };
    } catch (error) {
        console.error('构建签名失败:', error);
        throw new Error('签名构建失败: ' + error.message);
    }
}

// export { SignatureUtils, buildSignedHeaders };