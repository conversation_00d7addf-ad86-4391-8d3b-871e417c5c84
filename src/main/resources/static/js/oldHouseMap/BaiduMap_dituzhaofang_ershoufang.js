jQuery.support.cors = true;
//新接口枚举 价格和面积均减1    从0开始
var dituzhaofang = {
    PostUrl: "",
    IsPost: true,
    IsSalePost: true,
    IsMoveSubid: 0,
    pageIndex: 1,
    mapSizeQuyu: 13, //区域最大地图级别
    mapSizeBankuai: 15, //板块最大地图级别
    mapSizeFangyuan: 19, //房源最大地图级别
    pageSize: 10,
    sizeInfo: 0,//取消板块限制的zoom标记
    moveInfo: 0,//取消板块限制的移动标记
    //地图对象
    bdmap: undefined,
    defaultMouseStyle: "",
    //地图初始化
    init: function (divid) {
        // 百度地图API功能
        dituzhaofang.bdmap = new BMap.Map(divid, {
            enableMapClick: false
        }); // 创建Map实例 { enableMapClick: false }关闭自带的信息窗口
        dituzhaofang.bdmap.centerAndZoom("沈阳", dituzhaofang.mapSizeQuyu); // 初始化地图,设置中心点坐标和地图级别
        //dituzhaofang.bdmap.addControl(new BMap.MapTypeControl()); //添加地图类型控件
        dituzhaofang.bdmap.setCurrentCity("沈阳"); // 设置地图显示的城市 此项是必须设置的
        dituzhaofang.bdmap.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
        dituzhaofang.bdmap.addEventListener("zoomend", function () {
            if (this.getZoom() <= dituzhaofang.mapSizeQuyu) {
                dituzhaofang.ClearFilter();
            }
            //取消板块限制的zoom标记
            if (this.getZoom() > dituzhaofang.mapSizeBankuai) {
                dituzhaofang.sizeInfo += 1

            } else {
                dituzhaofang.sizeInfo = 0
                dituzhaofang.moveInfo = 0
            }
            dituzhaofang.GetShowData();
            $("#subid").val(0);
        });
        dituzhaofang.bdmap.addEventListener("moveend", function () {
            if (this.getZoom() <= dituzhaofang.mapSizeQuyu) {
                dituzhaofang.ClearFilter();
            }
            //取消板块限制的移动标记
            if (this.getZoom() > dituzhaofang.mapSizeBankuai) {
                dituzhaofang.moveInfo += 1

            } else {
                dituzhaofang.sizeInfo = 0
                dituzhaofang.moveInfo = 0
            }
            dituzhaofang.GetShowData();
            $("#subid").val(0);
        });
        dituzhaofang.bdmap.addEventListener("dragend", function () {
            dituzhaofang.GetShowData();
            $("#subid").val(0);

        });
    },
    //清除条件
    ClearFilter: function () {
        var mapSize = dituzhaofang.bdmap.getZoom();
        if (mapSize <= dituzhaofang.mapSizeQuyu) {
            $("#quyu").html("区域");
            $("#zongjia").html("总价");
            $("#mianji").html("面积");
            $("#huxing").html("户型");
            $("#regionId").val("0");
            $("#PriceBase").val("0");
            $("#BuildArea").val("0");
            $("#roomType").val("0");
            $(".my_xl_list_quyu").attr("identify", "0");//identify使用说明 0 未选择区域默认值      -1返回未选择区域   大于0跳回至所选区域 如4代表大东
        }
        //else if ($(".my_xl_list_quyu").attr("identify") == "-1") {
        //    $("#quyu").html("区域");
        //    $("#zongjia").html("总价");
        //    $("#mianji").html("面积");
        //    $("#huxing").html("户型");
        //    $("#regionId").val("0");
        //    $("#PriceBase").val("0");
        //    $("#BuildArea").val("0");
        //    $("#roomType").val("0");
        //    $(".my_xl_list_quyu").attr("identify", "0");
        //} else if ($(".my_xl_list_quyu").attr("identify") != 0) {
        //    $("#quyu").html($(".my_xl_list_quyu").find("li").eq($(".my_xl_list_quyu").attr("identify")).text()); //板块对应的区域
        //    $("#zongjia").html("总价");
        //    $("#mianji").html("面积");
        //    $("#huxing").html("户型");
        //    $("#regionId").val($(".my_xl_list_quyu").attr("identify")); //板块对应的区域
        //    $("#PriceBase").val("0");
        //    $("#BuildArea").val("0");
        //    $("#roomType").val("0");
        //}
    },
    //清除数据
    ClearData: function () {
        dituzhaofang.bdmap.clearOverlays();
        $("#ulList").html("");
    },
    //移动地图到指定点
    SetMap: function (lng, lat, size) {
        dituzhaofang.ClearData();
        dituzhaofang.bdmap.setZoom(size);
        dituzhaofang.bdmap.bdpoit = new BMap.Point(lat,lng);
        dituzhaofang.bdmap.panTo(dituzhaofang.bdmap.bdpoit); //将地图移动到点
    },
    //获取区域
    GetArea: function (callBack) {
        $.ajax({
            type: "post",
            url: "https://ltapi.fangxiaoer.com/apiv1/house/countSaleHouse",
            async:false,
            data: {
                zoomLevel:"10",
                moneyId: $("#PriceBase").val() == "0" ? "" : Math.floor($("#PriceBase").val()) - 1,
                layoutId: $("#roomType").val(),
                areaId: $("#BuildArea").val() == "0" ? "" : Math.floor($("#BuildArea").val())-1
            },
            success: function (data) {
                callBack.call(this, data);
            }
        });
    },
    //根据后台项目数据绘制房源点
    AddMapOverla: function (data) {
        //内容形式
        var point = new BMap.Point(data.Longitude, data.Latitude);
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
            div.style.MozUserSelect = "none";
            div.className = "name_small";
            var span = this._span = document.createElement("span");
            div.appendChild(span);
            span.appendChild(document.createTextNode(this._text));

            var arrow = this._arrow = document.createElement("div");
            arrow.id = "smallarrow" + data.SubId;
            arrow.style.background = "url(https://static.fangxiaoer.com/web/images/ico/map/mapicon.png) 0px -319px no-repeat";
            arrow.style.position = "absolute";
            arrow.style.width = "11px";
            arrow.style.height = "10px";
            arrow.style.top = "24px";
            arrow.style.left = "45%";
            arrow.style.overflow = "hidden";
            div.appendChild(arrow);
            var tan = this._tan = document.createElement("div");
            tan.className = "fang_tan";
            tan.id = "smalltan" + data.SubId;
            tan.innerHTML = "  二手房 " + data.Number + " 套  ";
            div.appendChild(tan);
            div.id = "smalldom" + data.ProjectId;
            div.onmouseover = function () {
                this.className = "name_small_hover";
                /*this.getElementsByTagName("span")[0].innerHTML = that._overText;*/
                arrow.style.backgroundPosition = "0px -332px";
                tan.style.display = "block";
                map.setDefaultCursor("pointer");
            };
            div.onmouseout = function () {
                this.className = "name_small";
                /*this.getElementsByTagName("span")[0].innerHTML = that._text;*/
                arrow.style.backgroundPosition = "0px -319px";
                tan.style.display = "none";
                map.setDefaultCursor(dituzhaofang.defaultMouseStyle);
            };
            dituzhaofang.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var txt = data.ProjectName, mouseoverTxt = txt + " " + data.Number;
        var projectCompOverlay = new ComplexCustomOverlay(new BMap.Point(data.Longitude, data.Latitude), data.Name, data.Number + "套");
        dituzhaofang.bdmap.addOverlay(projectCompOverlay);//
        projectCompOverlay.addEventListener("click", function () {//
            $(".xq_box").hide();
            $(".arrowDom").hide();
            $(".bubble-2").css("backgroundPosition", "0px -445px");
            $(this).find(".xq_box").show();
            $(this).find(".arrowDom").show();
            $(this).css("backgroundPosition", "0px -400px");
            $(this).css("zIndex", "300");
            $("#subid").val(data.SubId);
            $("#SaleHousesCount").val(data.Number);
            dituzhaofang.pageIndex = 1;
            console.log(data)
            window.open("https://sy.fangxiaoer.com/village/"+data.SubId+".htm")
            // dituzhaofang.GetSaleHouseInfo(false, data.SubId);
        });
        //点形式




        //ComplexCustomOverlay.prototype = new BMap.Overlay();
        //ComplexCustomOverlay.prototype.initialize = function (map) {
        //    this._map = map;
        //    var div = this._div = document.createElement("div");
        //    div.className = "bubble-2 bubble pointer";
        //    div.style.position = "absolute";
        //    var index = BMap.Overlay.getZIndex(this._point.lat);
        //    div.style.zIndex = index;
        //    var span = document.createElement("div");
        //    span.className = "xq_box";
        //    var spanLi1 = document.createElement("span");
        //    var textnode1 = document.createTextNode(this._text);
        //    spanLi1.appendChild(textnode1);
        //    span.appendChild(spanLi1);
        //    dituzhaofang.defaultMouseStyle = map.getDefaultCursor();
        //    div.onmouseover = function () {
        //        $(".xq_box").hide();
        //        $(".arrowDom").hide();
        //        $(".bubble-2").css("backgroundPosition", "0px -445px");
        //        span.style.display = "block";
        //        arrow.style.display = "block";
        //        div.style.backgroundPosition = "0px -400px";
        //        this.style.zIndex = "300";
        //        map.setDefaultCursor("pointer");
        //    };
        //    div.onmouseout = function () {
        //        span.style.display = "none";
        //        arrow.style.display = "none";
        //        div.style.backgroundPosition = "0px -445px";
        //        this.style.zIndex = index;
        //        map.setDefaultCursor("url(https://api.map.baidu.com/images/openhand.cur) 8 8,default");
        //    };
        //    div.appendChild(span);
        //    var arrow = this._arrow = document.createElement("div");
        //    arrow.className = "arrowDom";
        //    arrow.style.position = "absolute";
        //    arrow.style.width = "25px";
        //    arrow.style.height = "25px";
        //    arrow.style.top = "-4px";
        //    arrow.style.left = "5px";
        //    arrow.style.display = "none";
        //    arrow.style.overflow = "hidden";
        //    arrow.style.backgroundImage = "url(https://static.fangxiaoer.com/web/images/ico/map/mapicon.png)";
        //    arrow.style.backgroundPosition = " -24px -331px";
        //    div.appendChild(arrow);
        //    if (dituzhaofang.IsMoveSubid == data.SubId) {
        //        span.style.display = "block";
        //        arrow.style.display = "block";
        //        div.style.backgroundPosition = "0px -400px";
        //        div.style.zIndex = "300";
        //    }
        //    dituzhaofang.bdmap.getPanes().labelPane.appendChild(div);
        //    return div;
        //};
        //ComplexCustomOverlay.prototype.draw = function () {
        //    var map = this._map;
        //    var pixel = map.pointToOverlayPixel(this._point);
        //    this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
        //    this._div.style.top = pixel.y - 30 + "px";
        //};
        //ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
        //    this._div['on' + event] = fun;
        //};
        //var mouseoverTxt = data.Name + "　二手房" + data.Number + "套";
        //var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(data.Longitude, data.Latitude), mouseoverTxt, "");
        //dituzhaofang.bdmap.addOverlay(myCompOverlay);
        //myCompOverlay.addEventListener("click", function () {
        //    $(".xq_box").hide();
        //    $(".arrowDom").hide();
        //    $(".bubble-2").css("backgroundPosition", "0px -445px");
        //    $(this).find(".xq_box").show();
        //    $(this).find(".arrowDom").show();
        //    $(this).css("backgroundPosition", "0px -400px");
        //    $(this).css("zIndex", "300");
        //    $("#subid").val(data.SubId);
        //    $("#SaleHousesCount").val(data.Number);
        //    dituzhaofang.pageIndex = 1;
        //    dituzhaofang.GetSaleHouseInfo(false, data.SubId);
        //});
        //var bs = dituzhaofang.bdmap.getBounds(); //获取可视区域
        //var bssw = bs.getSouthWest(); //可视区域左下角
        //var bsne = bs.getNorthEast(); //可视区域右上角
    } ,
    //获取房源点
    GetSub: function (isBounds) {
        var bs = dituzhaofang.bdmap.getBounds(); //获取可视区域
        var bssw = bs.getSouthWest(); //可视区域左下角
        var bsne = bs.getNorthEast(); //可视区域右上角
        var palte =  $("#regionId2").val() == "0" ? "" : $("#regionId2").val()
        if ((dituzhaofang.moveInfo > 1) || (dituzhaofang.sizeInfo > 1)) {//移动后或放大后取消板块的限制
            palte = "";
        }
        $.ajax({
            type: "post",
            url: "https://ltapi.fangxiaoer.com/apiv1/house/countSaleHouse",
            async: false,
            data: {
                zoomLevel: "18",
                moneyId: $("#PriceBase").val() == "0" ? "" : Math.floor($("#PriceBase").val())-1,
                areaId: $("#BuildArea").val() == "0" ? "" : Math.floor($("#BuildArea").val())-1,
                layoutId: $("#roomType").val() == "0" ? "" : $("#roomType").val(),
                platId: palte,
                regionId: $("#regionId").val() == "0" ? "" : $("#regionId").val(),
                leftLng: bssw.lng,
                leftLat: bsne.lat,
                rightLng: bsne.lng,
                rightLat: bssw.lat
            },
            success: function (data) {
                var mapSize = dituzhaofang.bdmap.getZoom();
                if (data && mapSize > dituzhaofang.mapSizeBankuai) {
                    for (var i = 0; i < data.content.length; i++) {
                        dituzhaofang.AddMapOverla(data.content[i]);
                    }
                }
            }
        });
    },
    //添加区域
    AddArea: function (areaData) {
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            div.className = "bubble-2 bubble";
            div.style.position = "absolute";
            var index = BMap.Overlay.getZIndex(this.latitude);
            div.style.zIndex = index;
            div.onmouseover = function () {
                this.style.zIndex = "300";
                map.setDefaultCursor("pointer");
            };
            div.onmouseout = function () {
                this.style.zIndex = index;
                map.setDefaultCursor(dituzhaofang.defaultMouseStyle);
            };
            var span = this._span = document.createElement("ul");
            span.className = "name";
            var spanLi1 = document.createElement("LI");
            var spanLi2 = document.createElement("LI");
            var textnode1 = document.createTextNode(this._text);
            var textnode2 = document.createTextNode(this._overText);
            spanLi1.appendChild(textnode1);
            spanLi2.appendChild(textnode2);
            span.appendChild(spanLi1);
            span.appendChild(spanLi2);
            dituzhaofang.defaultMouseStyle = map.getDefaultCursor();
            span.onmouseover = function () {
                this.className = "name_hover";
                map.setDefaultCursor("pointer");
            };
            span.onmouseout = function () {
                this.className = "name";
                map.setDefaultCursor("url(https://api.map.baidu.com/images/openhand.cur) 8 8,default");
            };
            div.appendChild(span);
            var arrow = this._arrow = document.createElement("div");
            arrow.style.position = "absolute";
            arrow.style.width = "11px";
            arrow.style.height = "10px";
            arrow.style.top = "22px";
            arrow.style.left = "10px";
            arrow.style.overflow = "hidden";
            div.appendChild(arrow);
            dituzhaofang.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(this._point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var mouseoverTxt = areaData.Number + "套";
        var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(areaData.Longitude, areaData.Latitude), areaData.Name, mouseoverTxt);
        dituzhaofang.bdmap.addOverlay(myCompOverlay);
        myCompOverlay.addEventListener("click", function () { //点击区域
            $("#regionId").val(areaData.RegionId);
            $("#quyu").text(areaData.Name);
            dituzhaofang.SetMap(areaData.Latitude, areaData.Longitude, dituzhaofang.mapSizeQuyu + 1);
            dituzhaofang.Getquyu()
            $(".my_xl_list_quyu").attr("identify", areaData.RegionId)
        });
    },
    //添加板块
    AddArea2: function (areaData) {
        ComplexCustomOverlay.prototype = new BMap.Overlay();
        ComplexCustomOverlay.prototype.initialize = function (map) {
            this._map = map;
            var div = this._div = document.createElement("div");
            div.className = "bubble-2 bubble";
            div.style.position = "absolute";
            var index = BMap.Overlay.getZIndex(this.latitude);
            div.style.zIndex = index;
            div.onmouseover = function () {
                this.style.zIndex = "300";
                map.setDefaultCursor("pointer");
            };
            div.setAttribute("lng", areaData.Latitude) //记录位置
            div.setAttribute("lat", areaData.Longitude)
            div.setAttribute("regionId", areaData.PlatId) //记住板块id
            //div.setAttribute("quyu", areaData.regionId) //记住板块id
            div.onmouseout = function () {
                this.style.zIndex = index;
                map.setDefaultCursor(dituzhaofang.defaultMouseStyle);
            };
            var span = this._span = document.createElement("ul");
            span.className = "name";
            var spanLi1 = document.createElement("LI");
            var spanLi2 = document.createElement("LI");
            var textnode1 = document.createTextNode(this._text);
            var textnode2 = document.createTextNode(this._overText);
            spanLi1.appendChild(textnode1);
            spanLi2.appendChild(textnode2);
            span.appendChild(spanLi1);
            span.appendChild(spanLi2);
            dituzhaofang.defaultMouseStyle = map.getDefaultCursor();
            span.onmouseover = function () {
                this.className = "name_hover";
                map.setDefaultCursor("pointer");
            };
            span.onmouseout = function () {
                this.className = "name";
                map.setDefaultCursor("url(https://api.map.baidu.com/images/openhand.cur) 8 8,default");
            };
            div.appendChild(span);
            var arrow = this._arrow = document.createElement("div");
            arrow.style.position = "absolute";
            arrow.style.width = "11px";
            arrow.style.height = "10px";
            arrow.style.top = "22px";
            arrow.style.left = "10px";
            arrow.style.overflow = "hidden";
            div.appendChild(arrow);
            dituzhaofang.bdmap.getPanes().labelPane.appendChild(div);
            return div;
        };
        ComplexCustomOverlay.prototype.draw = function () {
            var map = this._map;
            var pixel = map.pointToOverlayPixel(this._point);
            this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
            this._div.style.top = pixel.y - 30 + "px";
        };
        ComplexCustomOverlay.prototype.addEventListener = function (event, fun) {
            this._div['on' + event] = fun;
        };
        var mouseoverTxt = areaData.count + "套";
        var myCompOverlay = new ComplexCustomOverlay(new BMap.Point(areaData.lng, areaData.lat), areaData.areaName, mouseoverTxt);
        dituzhaofang.bdmap.addOverlay(myCompOverlay);

    },
    //获取板块列表
    quyu: function () {
        var list = new Array(); //定义一维数组 
        var x = 0
        for (x = 1; x <= 9; x++) {
            list[x] = new Array(); //定义二维数组
            list[x][0]=0
        }
        function getquyu(x) {
            $.ajax({
                type: "post",
                url: "https://ltapi.fangxiaoer.com/apiv1/house/countSaleHouse",
                async: false,
                data: {
                    zoomLevel: "16",
                    regionId: $(".my_xl_list_quyu li").eq(x).attr("data-id"),
                },
                success: function (data) {
                    var resdata = data.content;
                    var i;
                    for (i = 1; i <= resdata.length; i++) { //初始化区域列表
                        list[x][i] = '<li value="' + resdata[i - 1].Name + '"quyu="' + resdata[i - 1].Latitude + '"lng="' + resdata[i - 1].Latitude + '"lat="' + resdata[i - 1].Longitude + '"regionId="' + resdata[i - 1].PlatId + '">' + resdata[i - 1].Name + '</li>'; //板块 0用来记住是否加载 0未加载  1已加载
                        $(".fxe_xl_list").append(list[x][i]);
                    }
                }
            });
        }

        $(".my_xl_list_quyu li").click(function () {
            $(".my_xl_list_quyu").val($(this).attr("data-id"));
            if ($(this).attr("data-id") == 0) {
                $("#regionId2").val("0")
                dituzhaofang.GetShowData()
            }
            dituzhaofang.Getquyu();
        })
        $(".my_xl_list_quyu>li").click(function () {
            $(".my_xl_list_quyu").attr("identify", "0") //记住点击的是区域还是板块
        })
        //$(".fxe_xl_list_quyu2 li").live("click", function () {

        //    $("#regionId").val("")
        //    $("#regionId2").val($(this).attr("regionid"));
        //})
        $(".my_xl_list_quyu li").hover(function () {
            $(".fxe_xl_list li").remove()
            $(".my_xl_list_quyu").attr("identify", $(this).attr("data-id")) //记住点击的是区域还是板块
            var ind = $(this).index();
            if (ind != 0) {
                if (list[ind][0] == "0") {//第一次划入时加载
                    getquyu(ind)
                    list[ind][0] = 1;
                    $(".fxe_xl_list").css("top", (($(this).index() -ind+2) * 30) + 2 + "px");
                    $(".fxe_xl_list").show();
                    $(".fxe_xl_list").scrollTop(0)
                } else {
                    // $(".fxe_xl_list").css("top", (($(this).index() - ind + 2) * 30) + 2 + "px");
                    $(".fxe_xl_list").css("top", (($(this).index() - ind + 2) * 30) + 2 + "px");
                    var i;
                    for (i = 1; i <= list[ind].length; i++) {
                        $(".fxe_xl_list").append(list[ind][i]);
                    }
                    $(".fxe_xl_list").show();
                    $(".fxe_xl_list").scrollTop(0)
                }

            }


        }, function () {
            $(".fxe_xl_list").hide();
        })
        $(".fxe_xl_list").hover(function () {
            $(".fxe_xl_list").show()

            $(".fxe_xl_list_quyu2 li").click(function () {
                dituzhaofang.sizeInfo =0
                dituzhaofang.moveInfo = 0
                $("#regionId2").val($(this).attr("regionid"))
                // dituzhaofang.GetSaleHouseInfo(true, $("#subid").val());
                $("#quyu").text($(this).text())
                dituzhaofang.SetMap($(this).attr("lng"), $(this).attr("lat"), dituzhaofang.mapSizeBankuai + 1); //跳转到相应位置
                dituzhaofang.GetShowData(); //显示房源点
                $("#quyu").val("") //置空区域
                $(".fxe_xl_list").hide()
            })
        }, function () {
            $(".fxe_xl_list").hide()
        })
    },
    //显示区域板块还是房源
    GetShowData: function () {
        dituzhaofang.ClearData();
        var mapSize = dituzhaofang.bdmap.getZoom();
        if (mapSize <= dituzhaofang.mapSizeQuyu) {
            dituzhaofang.GetArea(function (data) {
                if (data.status == "1") {
                    data = data.content
                    for (var i = 0; i < data.length; i++) {
                        dituzhaofang.AddArea(data[i]);
                    }
                }
            });
        } else if (mapSize <= dituzhaofang.mapSizeBankuai) {
            $("#regionId2").val("0")
            dituzhaofang.Getquyu()
        } else {

            dituzhaofang.GetSub(1);
        }
        dituzhaofang.pageIndex = 1;
        dituzhaofang.pageSize = 10;
        // dituzhaofang.GetSaleHouseInfo(false, 0);
        dituzhaofang.isShowData = true;
    },
    //获取板块
    Getquyu: function () {
        var zoom = dituzhaofang.bdmap.getZoom();
        if (zoom > dituzhaofang.mapSizeQuyu && zoom <= dituzhaofang.mapSizeBankuai) {
            var bs = dituzhaofang.bdmap.getBounds(); //获取可视区域
            var bssw = bs.getSouthWest(); //可视区域左下角
            var bsne = bs.getNorthEast(); //可视区域右上角
            var quyu = $("#regionId").val() == "0" ? "" : $("#regionId").val();
            if (quyu == "") {//如果没选择区域则一次获取区域内的板块 并记住是哪个区域的板块
                //var j = 0;
                //for (j = 1; j < $(".my_xl_list_quyu li").length; j++) {
                $.ajax({
                    type: "post",
                    url: "https://ltapi.fangxiaoer.com/apiv1/house/countSaleHouse",
                    async: false,
                    data: {
                        zoomLevel:"16",
                        moneyId: $("#PriceBase").val() == "0" ? "" : Math.floor($("#PriceBase").val())-1,
                        areaId: $("#BuildArea").val() == "0" ? "" : Math.floor($("#BuildArea").val())-1,
                        layoutId: $("#roomType").val() == "0" ? "" : $("#roomType").val(),
                        leftLng:bssw.lng,
                        leftLat:bsne.lat,
                        rightLng:bsne.lng,
                        rightLat:bssw.lat
                    },
                    success: function (data) {

                        // var status = $.parseJSON(data).status
                        var status = data.status
                        if (status == 1) {
                            dituzhaofang.ClearFilter()
                            var list = data.content;
                            var quyulng = "";
                            var quyulat = "";
                            var myCompOverlay;
                            for (var i = 0; i < list.length; i++) {
                                dituzhaofang.AddArea2(list[i]);

                                if (list[i].Number != 0) { //只显示有房源的板块
                                    var mouseoverTxt = list[i].Number + "套";
                                    myCompOverlay = new ComplexCustomOverlay(new BMap.Point(list[i].Longitude, list[i].Latitude), list[i].Name, mouseoverTxt); //坐标名字
                                    dituzhaofang.bdmap.addOverlay(myCompOverlay);
                                    quyulng = list[i].Longitude;
                                    quyulat = list[i].Latitude;
                                    var quyu = list[i].regionId
                                    myCompOverlay.addEventListener("click", function () {
                                        dituzhaofang.SetMap($(this).attr("lng"), $(this).attr("lat"), dituzhaofang.mapSizeBankuai + 1); //点击板块跳转到对应位置
                                        $("#quyu").text($(this).find("li").eq(0).text()) //列表显示选择的区域
                                        if ($(".my_xl_list_quyu").attr("identify") <= 0) {
                                            $(".my_xl_list_quyu").attr("identify", "-1") //列表选择区域板块标识 更改为选择区域
                                        }
                                        $("#regionId2").val($(this).attr("regionid"))
                                        $("#regionId").val(quyu)
                                    });
                                }
                            }
                            $(".bubble-2 .name").css({
                                "width": "65px",
                                "height": "54px",
                                "padding-top": "11px"
                            })
                            $(".bubble-2 .name li").css({
                                "width": "200%",
                                "margin-left": "-50%"
                            })
                            $(".bubble-2 .name li").each(function () {
                                if ($(this).text().length > 5) { //超过5个字加阴影避免看不清
                                    $(this).css("text-shadow", "0px 0px 3px #333");
                                }
                            })
                        }

                    }
                });
                //}
            } else {//获取选择区域的板块
                $.ajax({
                    type: "post",
                    url: "https://ltapi.fangxiaoer.com/apiv1/house/countSaleHouse",
                    async: false,
                    data: {
                        zoomLevel:"16",
                        moneyId: $("#PriceBase").val() == "0" ? "" : Math.floor($("#PriceBase").val())-1,
                        areaId: $("#BuildArea").val() == "0" ? "" : Math.floor($("#BuildArea").val())-1,
                        regionId: $("#regionId").val(),
                        layoutId: $("#roomType").val() == "0" ? "" : $("#roomType").val(),
                    },
                    success: function (data) {
                        dituzhaofang.ClearFilter()
                        var list = data.content;
                        var quyulng = "";
                        var quyulat = "";
                        var myCompOverlay;
                        for (var i = 0; i < list.length; i++) {
                            dituzhaofang.AddArea2(list[i]);
                            if (list[i].Numbe != 0) { //只显示有房源的板块
                                var mouseoverTxt = list[i].Number + "套";
                                myCompOverlay = new ComplexCustomOverlay(new BMap.Point(list[i].Longitude, list[i].Latitude), list[i].Name, mouseoverTxt); //坐标名字
                                dituzhaofang.bdmap.addOverlay(myCompOverlay);
                                quyulng = list[i].Longitude;
                                quyulat = list[i].Latitude;
                                var quyu = list[i].regionId
                                myCompOverlay.addEventListener("click", function () {
                                    dituzhaofang.SetMap($(this).attr("lng"), $(this).attr("lat"), dituzhaofang.mapSizeBankuai + 1); //点击板块跳转到对应位置
                                    $("#quyu").text($(this).find("li").eq(0).text()) //列表显示选择的区域
                                    if ($(".my_xl_list_quyu").attr("identify") <= 0) {
                                        $(".my_xl_list_quyu").attr("identify", "-1") //列表选择区域板块标识 更改为选择区域
                                    }
                                    $("#regionId2").val($(this).attr("regionid"))
                                    $("#regionId").val(quyu)
                                });
                            }
                        }
                        $(".bubble-2 .name").css({
                            "width": "65px",
                            "height": "54px",
                            "padding-top": "11px"
                        })
                        $(".bubble-2 .name li").css({
                            "width": "200%",
                            "margin-left": "-50%"
                        })
                        $(".bubble-2 .name li").each(function () {
                            if ($(this).text().length > 5) { //超过5个字加阴影避免看不清
                                $(this).css("text-shadow", "0px 0px 3px #333");
                            }
                        })
                    }
                });

            }

        }
    },
    //获取左侧房源列表
    GetSaleHouseInfo: function (isAppend, subid) {
        if (dituzhaofang.IsSalePost) {
            dituzhaofang.IsSalePost = false;
            var bs = dituzhaofang.bdmap.getBounds(); //获取可视区域
            var bssw = bs.getSouthWest(); //可视区域左下角
            var bsne = bs.getNorthEast(); //可视区域右上角
            $.ajax({
                type: "post",
                url: "https://ltapi.fangxiaoer.com/apiv1/house/viewHouses",
                data: {
                    page: dituzhaofang.pageIndex,
                    pageSize: dituzhaofang.pageSize,
                    subId: subid,
                    plateId: $("#regionId").val(),
                    priceId: $("#PriceBase").val(),
                    areaId: $("#BuildArea").val(),
                    room: $("#roomType").val(),
                    // LngRight: bssw.lng,
                    // LngLeft: bsne.lng,
                    // LatBottom: bssw.lat,
                    // LatTop: bsne.lng
                },
                success: function (data) {
                    dituzhaofang.IsSalePost = true;
                    data=data.content
                    console.log(data)
                    if (data != null && data.length > 0) {
                        if (isAppend) {
                            dituzhaofang.AppendList(data);
                        } else {
                            dituzhaofang.BindList(data);
                        }
                        if (subid > 0) {
                            $(".xiaoQu").html("<i>" + data[0].SubName + "</i> <span>" + data[0].RegionName + "-" + data[0].SubName + "</span><span>" + $("#SaleHousesCount").val() + "套</span>");
                            $(".xiaoQu").show();
                        } else {
                            $(".xiaoQu").hide();
                        }
                    }

                }
            });
        }
    },
    BindList: function (data) {
        var html = dituzhaofang.ResultData(data);
        $("#ulList").html(html);

    },
    AppendList: function (data) {
        var html = dituzhaofang.ResultData(data);
        $("#ulList").append(html);
    },
    Move: function (lng, lat, subid) {
        dituzhaofang.IsMoveSubid = subid;
        var point = new BMap.Point(lat,lng);
        var zoom = dituzhaofang.bdmap.getZoom();
        dituzhaofang.bdmap.setZoom(dituzhaofang.mapSizeBankuai + 1);
        dituzhaofang.bdmap.panTo(point); //将地图移动到点
        if (zoom == dituzhaofang.mapSizeBankuai + 1) {
            dituzhaofang.ClearData();
            dituzhaofang.GetShowData();
        }
    },
    //左侧房源列表内容
    ResultData: function (data) {
        var html = "";
        for (var i = 0; i < data.length; i++) {
            html += "<li>" +
                "<div class='listLeft'>" +
                "<div class='listImg'>" +
                "<a target='_blank' href='https://sy.fangxiaoer.com/UHouse/sale/view/" + data[i].HouseID + "'><img src='" + data[i].DefautHousePicUrl + "' alt=''/></a>" +
                "</div>" +
                "<div class='content'>" +
                "<h2><a target='_blank' href='https://sy.fangxiaoer.com/UHouse/sale/view/" + data[i].HouseID + "'>" + data[i].Title + "</a></h2>" +
                "<p>" + data[i].RegionName + "-" + data[i].SubName + "二手房</p>" +
                "<p>" + data[i].room + "室" + data[i].Hall + "厅" + data[i].Toilet + "卫　" + data[i].Floor + "/" + data[i].Totalfloornumber + "层　" + data[i].ForwardTypeName + "</p>" +
                "<p class='tese'>" + data[i].HouseTraitNames + "</p>" +
                "</div>" +
                "</div>" +
                "<div class='listRight'>" +
                "<div onclick='dituzhaofang.Move(" + data[i].Latitude + "," + data[i].Longitude + "," + data[i].SubID + ")'></div>" +
                "<p><i>" + data[i].SalePrice + "万</i></p>" +
                "<p>" + data[i].UnitPrice + "元/平</p>" +
                "<p>" + data[i].BuildArea + "平</p>" +
                "</div>" +
                "</li>";
        }
        return html;
    },
};
//百度地图自定义遮盖物调用方法
function ComplexCustomOverlay(point, text, mouseoverText) {
    this._point = point;
    this._text = text;
    this._overText = mouseoverText;
}
window.onload = function () {
    dituzhaofang.init("bdmap");
    $(".my_xl_list").find("li").click(function () {
        $("#" + $(this).attr("data-key")).val($(this).attr("data-id"));
        if ($(this).attr("data-key") == "regionId") {
            if ($(this).attr("data-id") > 0) {
                var zoom = dituzhaofang.bdmap.getZoom();
                dituzhaofang.SetMap( $(this).attr("Lat"),$(this).attr("Lng"), dituzhaofang.mapSizeQuyu + 1);
                $("#quyu").val()
                if (zoom > dituzhaofang.mapSizeQuyu) {
                    dituzhaofang.GetShowData();
                }
            } else {
                $("#quyu").html("区域");
                $("#regionId").val("0");
                dituzhaofang.GetShowData();
            }
        } else {
            dituzhaofang.GetShowData();
        }
    });
    $("#divexample1").scroll(function () {
        if ($("#divexample1").scrollTop() >= $("#ulList").height() - $("#divexample1").height()) {
            if (dituzhaofang.IsSalePost) {
                dituzhaofang.pageIndex++;
                // dituzhaofang.GetSaleHouseInfo(true, $("#subid").val());
            }
        }
    });
    dituzhaofang.quyu() //加载下拉列表板块信息

}
