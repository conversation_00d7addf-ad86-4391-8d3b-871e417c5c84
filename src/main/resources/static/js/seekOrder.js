$(document).ready(function(){

    //限制字数   113个
    $(".ski2 .ski2m").each(function(i,val){
        console.log('length: ' + val.textContent.length)
        if(val.textContent.length>98){
            val.textContent=val.textContent.substring(0,92)+'...'
            $(val).siblings('.skmore').show()//显示展开按钮
        }
    })
    //展开
    $(".skmore").click(function(){
        $(this).parent('.ski2').hide()
        $(this).parent().siblings('.ski6').show()
    })
    //收起
    $(".skhide").click(function(){
        $(this).parent().siblings('.ski2').show()
        $(this).parent('.ski6').hide()
    })

    //实名认证
    $(".seekc2").click(function(){
        window.location.href='https://my.fangxiaoer.com/userVerify'
    })
    //关闭按钮
    $(".seekCheck span").click(function(){
        $(".seekbg,.seekCheck").hide()
    })


    //获取电话号
    $(".skip").click(function(){
        if(sessionId == null){//没登录 打开登录弹窗
            $("#login").show()
            $(".seekbg").show()
            return false;
        }else if(sessionId != null){
            $(".login").hide()

            if(authenticationStatus != 1){
                $(".seekbg,.seekCheck").show()//显示实名认证弹窗
                return false;
            }

            $(".seekbg").hide()
            //显示电话
            $(this).hide()
            $(this).siblings('.ski5_i').css("display","inline-block")
            return false;
        }
    })
    $(document).on('click','#loginClose',function(){
        $("#login").hide()
        $(".seekbg").hide()
    })

})