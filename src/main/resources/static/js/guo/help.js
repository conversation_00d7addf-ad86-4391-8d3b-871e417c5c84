$(document).ready(function (){
    var map= new Map()
    map['area']='不限'
    var pays='按全款'
    var that='b1'

    /*修改-未登录弹窗样式*/
    $(".iph").css("z-index",'99999999999')
    $(".iip2 em").css("cursor","pointer")
    $(".map11").css("cursor","pointer")


    //首套二套
    $(document).on('click','.map1 span',function (){
        $(this).addClass("iv")
        $(this).siblings().removeClass("iv")
        map['houses'] = $(this).text()//首套二套
    })
    //首付全款
    $(".map2 span").click(function (){
        $(this).addClass("iv")
        $(this).siblings().removeClass("iv")
        var ta=$(this).text()
        if(ta=='首付'){
            $(".a1").hide()
            $(".a2").show()
            $('.bgtop').css("width","0%")
            map['budget']='按首付'+$(".tm2").text()
            that='b2'
        }else if(ta=='全款'){
            $(".a1").show()
            $(".a2").hide()
            $('.bgtop').css("width","0%")
            that='b1'
            map['budget']='按全款'+$(".tm1").text()
        }
    })
    //目标区域
    $(document).on('click','.map3 span',function (){
        $(this).addClass("iv")
        $(this).siblings().removeClass("iv")
        map['region']=$(this).text()
    })
    //居室
    $(".juli").click(function(){
        map['area']=$(".ju").text()
    })
    //选址类型
    $(".kli").click(function(){
        map['area']=$(".k").text()
    })
    //面积
    $(".jmli").click(function(){
        map['others']=$(".jm").text()
    })
    //怎么租
    $(document).on('click','.map33 span',function (){
        $(this).addClass("iv")
        $(this).siblings().removeClass("iv")
        var tat=$(this).text()
        console.log(tat)
        if(tat=='求购'){
            $(".izu").text('请输入您的价格预算：')
            $(".iwan").text("万元")
        }else if(tat=='求租'){
            $(".izu").text('请输入您的租金预算：')
            $(".iwan").text("万元/年")
        }else if(tat=='求兑'){
            $(".izu").text('请输入您的租金预算：')
            $(".iwan").text("万元/年")
        }
        map['housetype']=$(this).text()
    })
    //其他要求
    var st=[]
    $(document).on('click','.map4 span',function(){
        $(this).toggle(
            function (){
                $(this).addClass("iv")
                var that=$(this).text()
                st.push(that)
                console.log(st.toString())
                map['method']=st.toString()
            },
            function (){
                $(this).removeClass("iv")
                var that=$(this).text()
                st.splice($.inArray(that,st),1);
                console.log(st.toString())
                map['method']=st.toString()
            }
        )
        $(this).trigger('click');
    })
    /*$(".map4 span").toggle(
        function (){
            $(this).addClass("iv")
            var that=$(this).text()
            st.push(that)
            console.log(st.toString())
            map['method']=st.toString()
        },
        function (){
            $(this).removeClass("iv")
            var that=$(this).text()
            st.splice($.inArray(that,st),1);
            console.log(st.toString())
            map['method']=st.toString()
        }
    )*/
    //商铺其他要求
    $(document).on('click','.map444 span',function(){
        $(this).addClass("iv")
        $(this).siblings().removeClass("iv")
        map['method']=$(this).text()
    })
    //提交
    $(".map55").click(function(){
        console.log(s)

        //1-新房  2-二手房  3-租房  8-商业  4-看房  5-过户
        if($(this).attr('ty')==1 || $(this).attr('ty')==2){
            map['type']=$(this).attr('ty')
            map['italy']=$("#text").val()
            if(that=='b1'){
                map['budget']='按全款'+$(".tm1").text()//全款
            }else{
                map['budget']='按首付'+$(".tm2").text()//首付
            }
            if(map['houses'] == null || map['houses'] == ""){
                $(".dia").show()
                $(".dlog").html("<span>请选择首套房还是二套房！</span>")
                return;
            }
/*            if(map['budget']=="10万" || map['budget']=="50万"){
                $(".dia").show()
                $(".dlog").html("<span>请选择您的价格预算！</span>")
                return;
            }*/
            if(map['budget'].indexOf('按') == -1 ){
                $(".dia").show()
                $(".dlog").html("<span>请选择按首付还是按全款！</span>")
                return;
            }
            if(map['region'] == null || map['region'] == ""){
                $(".dia").show()
                $(".dlog").html("<span>请选择您要买房的区域！</span>")
                return;
            }
            if(!$("#checkagree").hasClass("checked")){
                $(".dia").show()
                $(".dlog").html("<span>请仔细阅读并同意服务协议及隐私政策！</span>")
                return false;
            }
            if(s==null || s==''){
                console.log('未登录')
                $(".iph").show()
            }else{
                console.log('已登陆')
                map['sessionId']=sid
                //提交
                $.ajax({
                    type: "POST",
                    url: "/saveGuide",
                    data: JSON.stringify(map),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success: function (data) {
                        console.log(data.status)
                        if (data.status == 1) {
                            $(".iph").hide()
                            $(".su").show()
                        } else {
                            $(".dias").text(data.msg);
                        }
                    }
                })
            }
            console.log(map)
        }else if($(this).attr('ty')==3){
            map['type']=$(this).attr('ty')
            map['budget']=$(".tm").text()//房款预算
            map['italy']=$("#text").val()
            if(map['region'] == null || map['region'] == ""){
                $(".dia").show()
                $(".dlog").html("<span>请选择您要租房的区域！</span>")
                return;
            }
            if(map['budget']=="0元"){
                $(".dia").show()
                $(".dlog").html("<span>请选择您的价格预算！</span>")
                return;
            }
            if(map['housetype']=="" || map['housetype']==undefined){
                $(".dia").show()
                $(".dlog").html("<span>请选择您想怎么租！</span>")
                return;
            }
            if(!$("#checkagree").hasClass("checked")){
                $(".dia").show()
                $(".dlog").html("<span>请仔细阅读并同意服务协议及隐私政策！</span>")
                return false;
            }
            if(s==null || s==''){
                console.log('未登录')
                $(".iph").show()
            }else{
                console.log('已登陆')
                map['sessionId']=sid
                //提交
                $.ajax({
                    type: "POST",
                    url: "/saveGuide",
                    data: JSON.stringify(map),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success: function (data) {
                        console.log(data.status)
                        if (data.status == 1) {
                            $(".iph").hide()
                            $(".su").show()
                        } else {
                            $(".dias").text(data.msg);
                        }
                    }
                })
            }
            console.log(map)
        }else if($(this).attr('ty')==4){
            var ysuan=$("#yu").val()+$(".iwan").text()
            map['type']=$(this).attr('ty')
            map['budget']=ysuan//房款预算
            map['italy']=$("#text").val()
            if(map['region'] == null || map['region'] == ""){
                $(".dia").show()
                $(".dlog").html("<span>请选择您要选址的区域！</span>")
                return;
            }
            if(map['housetype']=="" || map['housetype']==undefined){
                $(".dia").show()
                $(".dlog").html("<span>请选择您想怎么租！</span>")
                return;
            }
            if(map['budget']=="" || map['budget']=="万元" || map['budget']=="万元/年"){
                $(".dia").show()
                $(".dlog").html("<span>请输入您的价格预算！</span>")
                return;
            }
            if(!$("#checkagree").hasClass("checked")){
                $(".dia").show()
                $(".dlog").html("<span>请仔细阅读并同意服务协议及隐私政策！</span>")
                return false;
            }
            if(s==null || s==''){
                console.log('未登录')
                $(".iph").show()
            }else{
                console.log('已登陆')
                map['sessionId']=sid
                //提交
                $.ajax({
                    type: "POST",
                    url: "/saveGuide",
                    data: JSON.stringify(map),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success: function (data) {
                        console.log(data.status)
                        if (data.status == 1) {
                            $(".iph").hide()
                            $(".su").show()
                        } else {
                            $(".dias").text(data.msg);
                        }
                    }
                })
            }
            console.log(map)
        }else if($(this).attr('ty')==8){
            map['type']=$(this).attr('ty')
            map['italy']=$("#text").val()
            map['area']=$(".k").text()
            map['budget']=' '//房款预算
            if(map['region'] == null || map['region'] == ""){
                $(".dia").show()
                $(".dlog").html("<span>请选择您要选址的区域！</span>")
                return;
            }
            if(map['housetype']=="" || map['housetype']==undefined){
                $(".dia").show()
                $(".dlog").html("<span>请选择您想怎么租！</span>")
                return;
            }
            if(!$("#checkagree").hasClass("checked")){
                $(".dia").show()
                $(".dlog").html("<span>请仔细阅读并同意服务协议及隐私政策！</span>")
                return false;
            }
            if(s==null || s==''){
                console.log('未登录')
                $(".iph").show()
            }else{
                console.log('已登陆')
                map['sessionId']=sid
                //提交
                $.ajax({
                    type: "POST",
                    url: "/saveGuide",
                    data: JSON.stringify(map),
                    headers : {
                        'Content-Type' : 'application/json;charset=utf-8'
                    },
                    success: function (data) {
                        console.log(data.status)
                        if (data.status == 1) {
                            $(".iph").hide()
                            $(".su").show()
                        } else {
                            $(".dias").text(data.msg);
                        }
                    }
                })
            }
            console.log(map)
        }
    })
    $(".map11").click(function(){
        var reg = new RegExp("^1[0-9]{10}$")
        map['phone']=$("#mval").val()
        map['code']=$("#acode").val()
        if (map['phone'] == "" || map['phone'].length != 11 || !map['phone'].match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)) {
            $(".dias").text("请正确输入您的手机号码");
            return;
        } else if (map['code'] == "" || map['code'].length != 6) {
            $(".dias").text("请正确输入您的验证码");
            return;
        }
        $(".dias").text("")
        console.log(map)
        $.ajax({
            type: "POST",
            url: "/saveGuide",
            data: JSON.stringify(map),
            headers : {
                'Content-Type' : 'application/json;charset=utf-8'
            },
            success: function (data) {
                console.log(data.status)
                if (data.status == 1) {
                    $(".iph").hide()
                    $(".su").show()
                } else {
                    $(".dias").text(data.msg);
                }
            }
        })
    })
    //关闭弹窗
    $(".closex").click(function(){
        $(".iph").hide()
    })
    //提示关闭
    $(".map12").click(function () {
        $(".su").hide()
        window.location.reload()
    })
    $(".map13").click(function () {
        $(".dia").hide()
    })





//进度条
var rangeSlider = function(){
    var slider = $('.range-slider'),
        range = $('.range-slider input[type="range"]'),
        value = $('.range-value');
    slider.each(function(){
        value.each(function(){
            var value = $(this).prev().attr('value');
            console.log(value)
            var max=$(this).prev().attr('max')
            var ut=$(this).prev().attr('ut')
            var bw=value/max*100+'%'
            $('.tm1').html(value+ut)
            $(".bgtop").css("width",bw)
        });
        range.on('input', function(){
            var max=$(this).attr('max')
            var min=$(this).attr('min')
            var c=$(this).attr('c')
            var ut=$(this).attr('ut')
            var bw=this.value/max*100+'%'
            var ff=parseInt(this.value)+parseInt(min)
            if(ff<parseInt(c)){
                ff=ff+parseInt(c)
            }
              // $('.tm1').html(this.value+ut)
            $(this).parent().parent().prev().children('span').html(ff+ut)
            $(".bgtop").css("width",bw)
        });
    });
};
rangeSlider();


    //textare显示剩余可输入字数
    LimitedNnumber("#text", "#viewBox", 100)

    //获取验证码
    var wait = 60;
    $("#mcode").bind("click", function () {
        var tel = $("#mval").val();
        if (tel.length == 11 && tel.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/) && wait == 60) {
            $.ajax({
                type: "POST",
                data: { mobile: tel },
                url: "/sendSmsCode",
                success: function (result) {
                    if(result.status == 0){
                        $(".dias").text("操作过于频繁,请稍候再获取验证码");
                    }
                    console.log(result)
                    $('#mcode').css({ "border": "1px solid #bfbdbd"});
                    time(wait);
                }
            });
        } else if(wait == 60) {
            $(".dias").text("请输入正确的手机号码！");
        }
        return false;
    })
    //获取验证码倒计时
    function time(o) {
        if (wait == 0) {
            $("#mcode").html("重新获取");
            wait = 60;
            $("#mcode").css({ "border": "1px solid #ff5200", "color": "#ff5200" });
        } else {
            $("#mcode").html("在" + wait + "秒后重发");
            $("#mcode").css("color","#777573")
            wait--;
            setTimeout(function () {
                    time(o);
                },
                1000);
        }
    }


})

//加载区域
loadRegion('.at1')
loadRegion('.at6')
loadRegion('.at11')
loadRegion('.at15')
loadRegion('.at18')
//首套 二套
loadsaleinfo('.at2')
loadsaleinfo('.at5')
//买房 其他要求
loadsaleFeature('.at3')
loadsaleFeature('.at7')
//面积
loadsaleOthers('.at4')
loadsaleOthers('.at8')
//居室
loadlayout('.at9')
loadlayout('.at10')
loadlayout('.at12')
//租房怎么租
loadrentInfo('.at13')
//租房 其他要求
loadrentFeature(".at14")
//商铺其他要求
loadsaleOthers2('.at16')
//商铺供求方式
loadshopInfo('.at17')



//商铺 供求方式
function loadshopInfo(dom){
    $.ajax({
        type: "POST",
        url: "/shopInfoFilter ",
        success: function (data) {
            if (data.status == "1") {
                $(dom).empty()
                for (var i=0; i<data.content.length; i++){
                    htm='<span>'+data.content[i].name+'</span>'
                    $(dom).append(htm)
                }
                $(dom).append('<span class="c"></span>')
            }
        }
    });
}
//租房 其他要求
function loadrentFeature(dom){
    $.ajax({
        type: "POST",
        url: "/rentFeatureFilter ",
        success: function (data) {
            if (data.status == "1") {
                $(dom).empty()
                for (var i=0; i<data.content.length; i++){
                    htm='<span>'+data.content[i].name+'</span>'
                    $(dom).append(htm)
                }
                $(dom).append('<span class="c"></span>')
            }
        }
    });
}
//怎么租
function loadrentInfo(dom){
    $.ajax({
        type: "POST",
        url: "/rentInfoFilter ",
        success: function (data) {
            if (data.status == "1") {
                $(dom).empty()
                for (var i=0; i<data.content.length; i++){
                    htm='<span>'+data.content[i].name+'</span>'
                    $(dom).append(htm)
                }
                $(dom).append('<span class="c"></span>')
            }
        }
    });
}
//居室
function loadlayout(dom){
    $.ajax({
        type: "POST",
        url: "/layoutFilter",
        success: function (data) {
            if (data.status == "1") {
                var filter = data.content;
                $(dom).empty()
                for (var i = 0; i < filter.length; i++) {
                    if(filter[i].name=='五居以上'){
                        filter[i].name='≥5居'
                    }
                    html='<li da="'+filter[i].name+'" id="'+filter[i].id+'">'+filter[i].name+'</li>'
                    $(dom).append(html)
                }
            }
        }
    });
}

//面积
function loadsaleOthers(dom){
    $.ajax({
        type: "POST",
        url: "/saleOthersFilter ",
        success: function (data) {
            if (data.status == "1") {
                $(dom).empty()
                for (var i=0; i<data.content.length; i++){
                    htm='<li class="">'+data.content[i].name+'</li>'
                    $(dom).append(htm)
                }
            }
        }
    });
}
//商铺其他要求
function loadsaleOthers2(dom){
    $.ajax({
        type: "POST",
        url: "/saleOthersFilter ",
        success: function (data) {
            if (data.status == "1") {
                $(dom).empty()
                for (var i=0; i<data.content.length; i++){
                    htm='<span class="">'+data.content[i].name+'</span>'
                    $(dom).append(htm)
                }
            }
        }
    });
}

//买房 其他要求
function loadsaleFeature(dom){
    $.ajax({
        type: "POST",
        url: "/saleFeatureFilter",
        success: function (data) {
            if (data.status == "1") {
                $(dom).empty()
                for (var i=0; i<data.content.length; i++){
                    htm='<span>'+data.content[i].name+'</span>'
                    $(dom).append(htm)
                }
            }
        }
    });
}

//首套 二套
function loadsaleinfo(dom){
    $.ajax({
        type: "POST",
        url: "/saleInfoFilter",
        success: function (data) {
            if (data.status == "1") {
                $(dom).empty()
                for (var i=0; i<data.content.length; i++){
                    htm='<span>'+data.content[i].name+'</span>'
                    $(dom).append(htm)
                }
            }
        }
    });
}

//加载区域
function loadRegion(dom){
    $.ajax({
        type: "POST",
        url: "/usedRegionFilter",///regionFilter
        success: function (data){
            if (data.status == "1") {
                $(dom).empty()
                for (var i = 0; i < data.content.length; i++) {
                    html ='<span>'+data.content[i].name+'</span>'
                    $(dom).append(html);
                }
            }
        }
    });
}








//textare显示剩余可输入字数
function LimitedNnumber(eventBox, viewBox, textLength) {
    $(document).on('input propertychange paste keyup', eventBox, function(event) {
        //超出长度的部分替换为空
        this.value = this.value.replace(this.value.slice(textLength), "")
        $(viewBox).html(this.value.length + "/" + textLength)
    })
}


//jquery1.8.1以上版本支持toggle的方法
$.fn.toggle = function( fn, fn2 ) {
    var args = arguments,guid = fn.guid || $.guid++,i=0,
        toggle = function( event ) {
            var lastToggle = ( $._data( this, "lastToggle" + fn.guid ) || 0 ) % i;
            $._data( this, "lastToggle" + fn.guid, lastToggle + 1 );
            event.preventDefault();
            return args[ lastToggle ].apply( this, arguments ) || false;
        };
    toggle.guid = guid;
    while ( i < args.length ) {
        args[ i++ ].guid = guid;
    }
    return this.click( toggle );
}



