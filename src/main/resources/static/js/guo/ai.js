setTimeout(function(){
    $(".chatShow").css('display','block')
},500)
var wait = 60;

// 创建Vue应用实例
const { createApp, ref } = Vue;

const app = createApp({
    data() {
        return {
            isChatOpen: false,  // Determines whether the chat box is open
            isClosing: false, // 新增控制动画状态
            messages: [],       // Stores the messages in the chat
            userInput: '',      // Stores the user's input
            isLoading: false,   // Flag to show loading text
            aiMessageBuffer: '', // Buffer to accumulate AI's response
            user_id: '', // 可以动态指定用户id user123
            encryptionKey: 'your-secret-key', // 替换为你的密钥
            loginStatus: false,
            usePhone:'',
            useCode:'',
            poppStatus:false,
            poppText:'',
            codeT:'获取验证码',
            codes: false,
            host: 'https://sy.fangxiaoer.com/aichat',//http://localhost:5173  https://sy.fangxiaoer.com/aichat
        }
    },
    mounted() {
        if (this.isChatOpen) {
            this.loadMessages();
            this.scrollToBottom();
        }
    },
    methods: {
        // 从sessionStorage加载并解密消息
        loadMessages() {
            const encryptedMessages = sessionStorage.getItem(`chat_${this.user_id}`);
            if (encryptedMessages) {
                try {
                    const bytes = CryptoJS.AES.decrypt(encryptedMessages, this.encryptionKey);
                    const decryptedMessages = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
                    this.messages = decryptedMessages || [];
                    this.scrollToBottom();
                } catch (error) {
                    console.error('Failed to decrypt/load messages:', error);
                    this.messages = [];
                }
            }
        },
        // 保存消息到sessionStorage并加密
        saveMessages() {
            const recentMessages = this.messages.slice(-20); // 保留最近20条
            const encryptedMessages = CryptoJS.AES.encrypt(
                JSON.stringify(recentMessages),
                this.encryptionKey
            ).toString();
            sessionStorage.setItem(`chat_${this.user_id}`, encryptedMessages);
        },
        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                const chatBody = this.$refs.chatBody;
                if (chatBody) {
                    chatBody.scrollTop = chatBody.scrollHeight;
                }
            });
        },
        // Toggle the chat box visibility
        toggleChat() {
            if(getCookie('chat-mobile')){//sessionId ||
                this.loginStatus=true
            }

            // 读取cookie
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'chat-mobile') {
                    this.user_id = value; // 如果存在sessionId，则更新user_id
                    break;
                }
            }


            if (this.isChatOpen) {
                this.isClosing = true;
                this.saveMessages();
                setTimeout(() => {
                    this.isChatOpen = false;
                    this.isClosing = false;
                    this.messages = [];
                }, 400); // 与CSS动画持续时间一致
            } else {
                this.isChatOpen = true;
                this.loadMessages();  // 打开时加载聊天记录
            }
        },

        // Convert Markdown to HTML
        convertMarkdown(content) {
            return marked.parse(content); // 将Markdown内容转换为HTML
        },

        // Send message to the backend
        async sendMessage(t) {
            if (this.userInput.trim() === '' && !t) return; // Don't send empty messages
            const userMessage = { content: t?t:this.userInput, isUser: true };
            this.messages.push(userMessage);  // Add the user message to the chat
            this.scrollToBottom(); // 滚动到底部
            this.userInput = '';  // Clear input field
            this.isLoading = true;  // Show loading text

            try {
                // Send message to the server
                const response = await fetch(this.host+'/api/chat-stream/yq', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [{ role: 'user', content: userMessage.content }],
                        user_id: this.user_id,
                    }),
                });

                // Process the server response stream
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let done = false;
                let buffer = '';
                let aiMessage = ''; // 用于保存AI逐步返回的消息内容

                // 发送用户消息
                this.messages.push({ content: this.userInput, isUser: true });  // 保证用户消息在消息列表中
                this.scrollToBottom(); // 滚动到底部

                while (!done) {
                    const { value, done: streamDone } = await reader.read();
                    buffer += decoder.decode(value, { stream: true });

                    const lines = buffer.split('\n');
                    for (let line of lines) {
                        line = line.trim(); // 去除空格
                        // 如果遇到 [DONE]，则跳过该行，不做解析
                        if (line === "[DONE]") {
                            console.log("Stream finished.");
                            done = true;
                            break;
                        }

                        // 处理有效的 data: 行
                        if (line.startsWith('data:')) {
                            try {
                                const jsonData = line.replace('data: ', '').trim(); // 去除 data: 前缀并清除多余空格
                                if (jsonData === '[DONE]') {
                                    // 如果是 [DONE]，直接跳过，不进行 JSON 解析
                                    continue;
                                }

                                if (jsonData.includes('results')) {
                                    // 如果返回的消息包含 "results"，则跳过该消息
                                    continue;
                                }
                                const data = JSON.parse(jsonData);
                                const newAiMessage = data.choices[0]?.delta?.content;

                                if (newAiMessage) {
                                    aiMessage += newAiMessage;  // 将每个返回的内容拼接起来
                                    this.messages[this.messages.length - 1] = { content: aiMessage, isUser: false }; // 更新最后一条AI消息
                                    this.scrollToBottom(); // 确保滚动到底部
                                }
                            } catch (error) {
                                console.error('Error parsing message:', error, 'Raw data:', line);
                            }
                        }
                    }

                    buffer = lines[lines.length - 1]; // 处理剩余的部分
                    done = streamDone;
                }

                // 流结束后，更新最后的AI消息
                if (done && aiMessage) {
                    this.messages[this.messages.length - 1] = { content: aiMessage, isUser: false };
                    this.isLoading = false; // 隐藏加载状态
                    this.scrollToBottom(); // 确保最终滚动到底部
                    this.saveMessages(); // 每次消息发送完毕都保存记录
                }
            } catch (error) {
                console.error('Error sending message:', error);
            } finally {
                this.isLoading = false;
                this.userInput = ''; // Clear the input field
            }
        },
        //登录
        subMit() {
            let that = this
            if (that.usePhone == "" || that.usePhone.toString().length != 11 || !that.usePhone.toString().match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)) {
                that.poppStatus = true;
                that.poppText = "请正确输入您的手机号码";
                that.d()
                return;
            } else if (that.useCode == "" || that.useCode.toString().length != 6) {
                that.poppStatus = true;
                that.poppText = "请正确输入您的验证码";
                that.d()
                return;
            }
            $.ajax({
                type: "POST",
                url: "/login",
                data: {
                    telNumber: that.usePhone,
                    code: that.useCode,
                    registFromUrl: window.location.href
                },
                success: function (data) {
                    if (data.status == 1) {
                        that.loginStatus = true;
                        that.scrollToBottom(); // 滚动到底部

                        // 设置cookie，有效期为7天
                        var date = new Date();
                        date.setTime(date.getTime() + (1 * 24 * 60 * 60 * 1000));
                        document.cookie = "chat-mobile=" + data.content.mobile + "; expires=" + date.toUTCString() + "; path=/";
                        this.user_id = data.content.mobile

                    } else {
                        that.poppStatus = true;
                        that.poppText = data.msg;
                        that.d()
                    }
                }
            })
        },
        d(){
            setTimeout(() => {
                this.poppStatus = false;
            }, 3000)
        },
        getcodeFn(){
            let that=this
            if (that.usePhone.toString().length == 11 && that.usePhone.toString().match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/) && wait == 60) {
                $.ajax({
                    type: "POST",
                    data: { mobile: that.usePhone.toString() },
                    url: "/sendSmsCode",
                    success: function (result) {
                        if(result.status == 0){
                            that.poppStatus = true;
                            that.poppText = "操作过于频繁,请稍候再获取验证码";
                            that.d()
                        }
                        console.log(result)
                        time(wait);
                    }
                });
            } else if(wait == 60) {
                that.poppStatus = true;
                that.poppText = "请输入正确的手机号码!";
                that.d()
            }
        },

    },
});

// 挂载应用
app.mount('#app');

function time(o) {
    if (wait == 0) {
        $("#mcode").html("重新获取");
        wait = 60;
        $("#mcode").css({ "color": "#4E8CFF" });
        $(".c1").show()
        $(".c2").hide()
    } else {
        $(".c1").hide()
        $(".c2").show()
        $("#mcode").html("在" + wait + "秒后重发");
        $("#mcode").css("color","#777573")
        wait--;
        setTimeout(function () {
                time(o);
            },
            1000);
    }
}

function limitInputLength(input, maxLength) {
    if (input.value.length > maxLength) {
        input.value = input.value.slice(0, maxLength);
    }
}