/**
 * Created by Administrator on 2017/9/22.
 */
yzm = 1;
var hint = ["账户名与密码不匹配，请重新输入!", //登录  0
    "请输入手机号!", //1
    "请输入密码!", //2
    "请输入账户名和密码!", //3
    "请输入你的手机号码!", //注册 修改密码  4
    "手机号码格式不正确，请重新输入!", //注册修改密码5
    "请输入验证码!", //6
    "验证码错误!", //7
    "手机号格式不正确!", //注册修改密码  8
    "该账户不存在，请请核对后重新输入。", //修改密码  9
    "密码确认不正确!", //10
    "密码格式不正确!", //11
    "推荐人与用户名不能重复!", //12
    "该账户不存在，请核对后重新输入。", //13
    "推荐人手机号格式不正确!", //14
    "该手机号已注册!", //15
    "原始密码错误"
] //16

//fxe_mobile手机号/账号名
//fxe_mobile_info手机号/账号名验证错误提示
//fxe_tmobile推荐人手机号
//fxe_tmobile_info推荐人手机号错误提示
//fxe_password密码
//fxe_password_info密码错误提示
//fxe_confirmpassword确认密码
//fxe_confirmpassword_info确认密码错误提示
//fxe_prePassword原始密码
//fxe_prePassword_info原始密码错误提示
//fxe_Code图片验证码的图片
//fxe_jiaoyan_bg图片验证码的背景
//fxe_messageCode手机验证码
//fxe_messageCode_info手机验证码提示
//fxe_jiaoyan_div图片验证码的内容
//fxe_mobile_info用户名的提示信息
//fxe_CodeImg图片验证码的用户输入值
//fxe_jiaoyan_error图片验证码的提示
//fxe_ReSendValidateCoad重新获取验证码按钮
//fxe_validateCode认证时间
//fxe_ReSendValidateCoad重新获取
//fxe_validateCode多少秒后重新发送
//error_info错误信息提示
//验证手机号
var confirm = {
    mobile: function () {
        var mobile = $(".fxe_mobile").val();
        var reg = /^1[0-9]{10}$/;
        if (mobile == "") {
            $(".fxe_mobile_info").text(hint[1]);
            return false;
        } else if (!reg.test(mobile)) {
            $(".fxe_mobile_info").text(hint[5]);
            return false;
        } else {
            return true;
        }
    },
    //验证手机验证码是否正确服务器!!!!只能用一次!!!!(sy注册页不能用......)
    SJyzmYZ: function () {
        var r = false;
        if ($(".fxe_messageCode").val() == "") {
            $(".fxe_messageCode_info").text(hint[6]);
        } else {
            $.ajax({
                type: "POST",
                data: {
                    mobile: $(".fxe_mobile").val(),
                    code: $(".fxe_messageCode").val()
                },
                url: "/checkPasscode",
                async: false,
                success: function (data) {
                    if (data == 1) {
                        $(".fxe_messageCode_info").text("");
                        r = true;
                    } else {
                        $(".fxe_messageCode_info").text(hint[7]);
                        r = false;
                    }
                }
            });
        }
        return r;
    },
    //验证手机验证码是否正确
    SJyzmYZ1: function () {
        var reg = /^\d{6}$/;
        var mobile = $(".fxe_mobile").val();
        var yzm = $(".fxe_messageCode").val();
        if (reg.test(yzm)) {
            $(".fxe_messageCode_info").text("");
            $.ajax({
                type: "POST",
                data: { action: "CheckSend", mobile: mobile, code:yzm},
                url: "/checkPasscode",
                async: false,
                success: function (data) {
                    if (data == "1") {
                        $("#from1").hide();
                        $("#from2").show();
                        return true;
                    }
                    else {
                        $(".fxe_messageCode_info").text(hint[7]);
                        return false;
                    }
                }
            });

        } else {

            $(".fxe_messageCode_info").text(hint[6]);
            return false;
        }
    },
    //查看手机验证码状态
    yzmZT: function () {
        var x = false;
        if (confirm.mobile()) {
            fxe_confirm.SJyzmFS();
            // if (fxeTime.r == 1) {
            //     fxeTime.timeWait();
            //     x = true;
            // } else if (fxeTime.r == -4) {
            //     $(".fxe_messageCode_info").html("十分抱歉！您今日的请求次数已达上限，请明日再进行重试。");
            //     x = false;
            // } else {
            //     $(".fxe_messageCode_info").html("系统繁忙，请稍后重试!");
            //     x = false;
            // }
            return x;
        }

    },
    // 清除手机号错误提示
    clearMobile: function () {
        $(".fxe_mobile_info").html("&nbsp;");
    },
    // 清除验证码错误提示
    clearjiayan: function () {
        $(".fxe_jiaoyan_error").html("&nbsp;");
    },
    clearCode: function () {
        $(".fxe_messageCode_info").html("&nbsp;");
    }

}
var fxeTime = {
    //时间
    timeWait: function () {
        if (yzm == 1) {
            wait = 60;
            time();
            $("#from1 b").css({ "color": "#999", "border": "1px solid #999" })
        }

        function time() {
            if (wait == 0) {
                $("#from1 b").css({ "color": "#ff5200", "border": "1px solid #ff5200" })
                $("#from1 b").show().html("重新获取");
                $("#from1 b").removeAttr("disabled");
                yzm = 1;
                wait = 60;
            } else {
                yzm = 0;
                $("#from1 b").show().html("在" + wait + "秒后重发");
                $("#from1 b").attr("disabled", "disabled");
                wait--;
                setTimeout(function () {
                        time();
                    },
                    1000);
            }
        }
    },
    r: 0, //手机验证码状态标识
    //验证用户名是否为空
    mobile: function () {
        var Text = $(".fxe_mobile").val();
        if (Text == "") {
            $(".error_info").text(hint[1]);
            return false;
        } else {
            $(".error_info").text("");
            return true;
        }
    },
    //验证密码是否为空
    password: function () {
        var Text = $(".fxe_password").val();
        if (Text == "") {
            $(".error_info").text(hint[2]);
            return false;
        } else {
            return true;
        }
    }
}
var fxe_confirm = {
    //向用户发送手机验证码
    SJyzmFS: function () {
        //发送短信验证码
        var mobile = $(".fxe_mobile").val();
        var x = false;
        if ($("#from1 b").attr("disabled")!="disabled"){
            $.ajax({
                type: "POST",
                data: { action: "Send", mobile: mobile },
                url: "/sendSmsCode",
                async: false,
                success: function (data) {
                    fxeTime.r = data.status;
                    if (fxeTime.r == 1) {
                        fxeTime.timeWait();
                        x = true;
                    }else{
                        $(".fxe_messageCode_info").html(data.msg);
                    }
                }
            })
        }

    }

}
$(function () {


    $("#name").blur(function () {
        if ($(this).val() == "") {
            $(".fxe_name").text("请输入姓名！");
        }
    });
    $("#money").blur(function () {
        if ($(this).val() == "") {
            $(".fxe_Money").text("请输入金额！");
        }
    });
    $("#name").focus(function () {
        $(".fxe_name").html("&nbsp;");
    });
    $("#money").focus(function () {
        $(".fxe_Money").html("&nbsp;");
    });
})


function confirmNext() {
    if (!confirm.mobile()) {
    } else if (!confirm.SJyzmYZ1()) {
    } else {
        $("#from1").hide();
        $("#from2").show();
    }
}
function conf(minNum, maxNum) {
    if ($("#name").val() == "") {
        $(".fxe_name").text("请输入姓名！");
        return false;
    }
    if ($("#money").val() == "") {
        $(".fxe_Money").text("请输入金额！");
        return false;
    } else if ($("#money").val() < 0) {
        $(".fxe_Money").text("金额不能为负！");
        return false;
    } else if ($("#money").val() < minNum) {
        $(".fxe_Money").text("金额不能低于" + minNum + "万！");
        return false;
    } else if ($("#money").val() > maxNum) {
        $(".fxe_Money").text("金额不能高于" + maxNum + "万！");
        return false;
    } else {
        return true;
    }
}