/**
 * Created by Administrator on 2018/11/22.
 */
/**
 * Created by Administrator on 2017/12/18.
 */
$(function () {
    function changeauto() {
        $(".ac_input").autocomplete({
            source: function( request, response ) {
                var searchInfo = request.term;
                $.ajax({
                    url: "/videoSearch",
                    dataType: "json",
                    data: {
                        projectName: searchInfo
                    },
                    type:"post",
                    success: function( data ) {
                        if (data != null && data.content.length != 0 && data.status == 1) {
                            response($.map(data.content, function (item) {
                                var highLightTitle = item.videoTitle;
                                highLightTitle = highLightTitle.replace(
                                new RegExp(
                                    "(?![^&;]+;)(?!<[^<>]*)(" +
                                    $.ui.autocomplete.escapeRegex(request.term) +
                                    ")(?![^<>]*>)(?![^&;]+;)", "gi"
                                ), "<strong>$1</strong>");
                                return {
                                    label: "<i style='float:right'></i>" + highLightTitle,
                                    tableId: item.videoId,
                                    value: item.videoTitle,
                                };
                            }));
                        }
                    }
                });
            },
            select: function( event, ui ) {
                window.open("/video/"+ui.item.tableId+".htm");
            },
            open: function() {
                $( this ).removeClass( "ui-corner-all" ).addClass( "ui-corner-top" );
                $( this ).css("z-index", 100);
            },
            close: function() {
                $( this ).removeClass( "ui-corner-top" ).addClass( "ui-corner-all" );
            }
        });
    };

    $(".ac_input").click(function () {
        changeauto();
    });

    $(".search_btn").click(function () {
        var input = $(".ac_input").val();
        input=stripscript(input);
        var search ="" ;
        if (input == null || input == undefined || input == '') {
            window.location.href = '/videos/';
        }else {
            search = "search="+input;
            var url = "/videoSearchlist/" + search;
            window.location.href = url;
        }
    });

    function stripscript(s)
    {
        var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
        var rs = "";
        for (var i = 0; i < s.length; i++) {
            rs = rs+s.substr(i, 1).replace(pattern, '');
        }
        return rs;
    }


    $(".ac_input").each(function () {
        var thisVal = $(".ac_input").val();    //判断文本框的值是否为空，有值的情况就隐藏提示语，没有值就显示
        if (thisVal != "") { $(".ac_input").siblings("span").hide(); }
        else { $(".ac_input").siblings("span").show(); }
        $(".ac_input").keyup(function () {
            $(".ac_input").siblings("span").hide();
        }).blur(function () {
            var val = $(this).val();
            if (val != "") {
                $(this).siblings("span").hide();
            } else {
                $(this).siblings("span").show();
            }
        });
    });
    //此方法用于搜索框无下拉时enter事件
    $(".ac_input").keydown(function (event) {
        if (event.keyCode == 13) {
            var selecetFlag = true;
            $(".ui-autocomplete").each(function () {
                if($(this).css("display") == "block"){
                    var item = $(this);
                    $(item).find("a").each (function () {
                        if($(this).hasClass("ui-state-focus")){
                            selecetFlag = false;
                            return false;
                        }
                    });
                }
            });
            if(selecetFlag){
                $(".search_btn").click();
            }
        }
    });
    $("#deleteButton").click(function () {
        var redirectUrl =location.pathname;
        if( redirectUrl != null || redirectUrl != undefined || redirectUrl != ''){
            if( redirectUrl.indexOf("search") != -1 ){
                // redirectUrl =   redirectUrl.substr(0,redirectUrl.indexOf("search"));
                window.location.href = '/videos/';
            }else {
                // window.location.reload();
                $("#search2017 .search .ac_input").val("");
                $("#deleteButton").hide();
            }
        }
    });
    $("#search2017 .search .ac_input").val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
    $("#search2017 .search .ac_input").keyup(function(){
        $(this).val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
    })
});