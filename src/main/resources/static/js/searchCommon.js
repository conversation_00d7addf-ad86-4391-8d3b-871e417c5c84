/**
 * Created by Administrator on 2017/12/18.
 */
$(function () {
    function changeauto(method) {
        $(".ac_input").autocomplete({
            source: function( request, response ) {
                var searchInfo = request.term;
                searchInfo = encodeURI(searchInfo);
                $.ajax({
                    url: "/commonSearch?key="+method,
                    dataType: "json",
                    data: {
                        q: searchInfo
                    },
                    success: function( data ) {
                        if (data.length != 0) {
                            var count = 0;
                            data.push(data[0]);
                            response($.map(data, function (item) {
                                var highLightTitle = item.title;
                                highLightTitle = highLightTitle.replace(
                                    new RegExp(
                                        "(?![^&;]+;)(?!<[^<>]*)(" +
                                        $.ui.autocomplete.escapeRegex(request.term) +
                                        ")(?![^<>]*>)(?![^&;]+;)", "gi"
                                    ), "<strong>$1</strong>");
                                var txt = $(".ac_input").attr("name");
                                switch (txt) {
                                    case "1":
                                        count++;
                                        var tableName = item.tableName;
                                        var projectTypes = item.projectZx;
                                        if(tableName == 'brand' || tableName == 'region' || tableName == 'subwaystation' || tableName == 'subway'){
                                            projectTypes = item.projectZx + "个楼盘";
                                        }else{
                                            if (projectTypes == null || projectTypes == undefined || projectTypes == '') {
                                                // projectTypes = "新房";
                                                projectTypes = "<s class='high_light_span'>新房</s>"
                                            }
                                            if (item.projectType == 1) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span02'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span02'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span02'>写字间</s>");
                                            } else if (item.projectType == 2) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span02'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span02'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span02'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span02'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span02'>写字间</s>");
                                            } else if (item.projectType == 3) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span02'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span02'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span02'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span02'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span'>写字间</s>");
                                            }
                                        }
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            label: "<i style='float:right'>" + projectTypes + " </i>" + highLightTitle+"<input type='hidden' class='newhouse'>",
                                            tableId: item.tableId,
                                            projectType: item.projectType,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                    case "2":
                                        count++;
                                        var itemSummary = item.summary;
                                        itemSummary = setContentText(itemSummary, 1);
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            // label: "<i style='float:right'>" + itemSummary + " </i>" + highLightTitle,
                                            tableId: item.tableId,
                                            summary: item.summary,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                    case "7":
                                        count++;
                                        if (count < data.length) {
                                            return {
                                                label: "<i style='float:right'>" + item.category + " </i>" + highLightTitle,
                                                tableId: item.tableId,
                                                summary: item.summary,
                                                value: item.title,
                                                tableName: item.tableName,
                                            };
                                        }
                                        break;
                                    case "8":
                                        count++;
                                        if (count < data.length) {
                                            return {
                                                label: "<i style='float:right'>" + item.category + " </i>" + highLightTitle,
                                                tableId: item.tableId,
                                                summary: item.summary,
                                                value: item.title,
                                                tableName: item.tableName,
                                            };
                                        }
                                        break;
                                    case "3":
                                        count++;
                                        var itemSummary = item.summary;
                                        itemSummary = setContentText(itemSummary, 0);
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            // label: "<i style='float:right'>" + itemSummary + " </i>" + highLightTitle,
                                            tableId: item.tableId,
                                            summary: item.summary,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                    case "5":
                                        count++;
                                        if (count == data.length) {
                                            break;
                                        }
                                        return {
                                            label: "<i style='float:right'>" + " </i>" + highLightTitle,
                                            tableId: item.tableId,
                                            projectType: item.projectType,
                                            value: item.title,
                                            tableName: item.tableName,
                                        }
                                        break;
                                    default:
                                        count++;
                                        var tableName = item.tableName;
                                        var projectTypes = item.projectZx;
                                        if(tableName == 'brand' || tableName == 'region' || tableName == 'subwaystation' || tableName == 'subway'){
                                            projectTypes = item.projectZx + "个楼盘";
                                        }else{
                                            if (projectTypes == null || projectTypes == undefined || projectTypes == '') {
                                                // projectTypes = "新房";
                                                projectTypes = "<s class='high_light_span'>新房</s>"
                                            }
                                            if (item.projectType == 1) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span02'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span02'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span02'>写字间</s>");
                                            } else if (item.projectType == 2) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span02'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span02'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span02'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span02'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span02'>写字间</s>");
                                            } else if (item.projectType == 3) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span02'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span02'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span02'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span02'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span'>写字间</s>");
                                            }
                                        }
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            label: "<i style='float:right'>" + projectTypes + " </i>" + highLightTitle+"<input type='hidden' class='newhouse'>",
                                            tableId: item.tableId,
                                            projectType: item.projectType,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                }
                            }));
                            }
                            else {
                                var txt = $(".ac_input").attr("name");
                                data = [{"txt":txt}];
                                response($.map(data, function (item) {
                                    switch (txt) {
                                        case "1":
                                            return {
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                            break;
                                        case "2":
                                            return {
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                            break;
                                        case "3":
                                            return {
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                            break;
                                        case "6":
                                            return {
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                            break;
                                        default:
                                        break;
                                }
                            }))
                        }
                    }
                });
            },
            select: function( event, ui ) {
                // 1029 搜索统计服务
                var sherchInfo = $(this).val();
                var searchSource = "1";
                var searchIndex = $(".head_nav .hover").text();
                var searchType = 0;
                var keyTab = "other";
                var keyTabIndex = ui.item.tableName;
                var keyId = ui.item.tableId;
                switch (searchIndex) {
                    case "新房":
                        searchType = 1;
                        break;
                    case "二手房":
                        searchType = 2;
                        break;
                    case "租房":
                        searchType = 3;
                        break;
                    case "资讯":
                        searchType =4;
                        break;
                }
                switch(keyTabIndex){
                    case 'brand':
                        keyTab = "brand";
                        break;
                    case "region":
                        keyTab = "region";
                        break;
                    case "subway":
                        keyTab = "line";
                        break;
                    case "subwaystation":
                        keyTab = "station";
                        break;
                    case "video":
                        keyTab = "video";
                        break;
                    case "news":
                        keyTab = "news";
                        break;
                    case "ProjectBase":
                        keyTab = "project";
                        break;
                    case "xinfang1":
                    case "xinfang3":
                        keyTab = "station";
                        break;
                }
                $.ajax({
                    type: "POST",
                    url: "/searchClickAnalyze",
                    data: {
                        "sessionId": sessionId,
                        "searchInfo": sherchInfo,
                        "searchSource": searchSource,
                        "searchType": searchType,
                        "keyId": keyId,
                        "keyTab": keyTab
                    },
                    success: function () {
                        console.log("AnalyzeSuccess...");
                    }
                })

                var resultType  =    $("#txtkeys").attr("name")
                var itemSummary =   ui.item.summary;
                var tableName = ui.item.tableName;
                switch (resultType){
                    case "1":
                        if (ui.item.tableId == -1) {
                            window.open("/helpSearch?ids=1");
                        }else if(tableName == 'brand'){
                            window.open("/brandCompany/"+ui.item.tableId+".htm");
                        }else if(tableName == 'region'){
                            window.open("/houses/r"+ui.item.tableId);
                        }else if(tableName == 'subway'){
                            window.open("/houses/l"+ui.item.tableId + "-k1");
                        }else if(tableName == 'subwaystation'){
                            window.open("/houses/s"+ui.item.tableId + "-l" + ui.item.projectType + "-k1");
                        }else{
                            window.open("/house/"+ui.item.tableId+"-"+ui.item.projectType+".htm");
                        }
                        break;
                    case "2":
                        if (ui.item.tableId == -1) {
                            window.open("/helpSearch?ids=2");
                        }else {
                            if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
                                var Arr   = itemSummary.split(",");
                                if(Arr.length>0){
                                    if(Arr[1]=="二手房0套"){
                                        window.open("/saleHouses/search="+ui.item.value);
                                    }else {
                                        window.open("/saleHouses/-v" + ui.item.tableId);
                                    }
                                }
                            }
                        }
                        break;
                    case "3":
                        if (ui.item.tableId == -1) {
                            window.open("/helpSearch?ids=3");
                        }else {
                            if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
                                var Arr   = itemSummary.split(",");
                                if(Arr.length>0){
                                    if(Arr[0]=="租房0套"){
                                        window.open("/rents/search=" + ui.item.value);
                                    }else {
                                        window.open("/rents/-v" + ui.item.tableId);
                                    }
                                }
                            }
                        }
                        break;
                    case "5":
                        window.open("/news/" + ui.item.tableId + ".htm");
                        break;
                    case "7":
                    case "8":
                        window.open("/village/" + ui.item.tableId+".htm");
                        break;
                    default:
                        if (ui.item.tableId == -1) {
                            window.open("/helpSearch?ids=1");
                        }else if(tableName == 'brand'){
                            window.open("/brandCompany/"+ui.item.tableId+".htm");
                        }else if(tableName == 'region'){
                            window.open("/houses/r"+ui.item.tableId);
                        }else if(tableName == 'subway'){
                            window.open("/houses/l"+ui.item.tableId + "-k1");
                        }else if(tableName == 'subwaystation'){
                            window.open("/houses/s"+ui.item.tableId + "-l" + ui.item.projectType + "-k1");
                        }else{
                            window.open("/house/"+ui.item.tableId+"-"+ui.item.projectType+".htm");
                        }
                        break;
                }
            },
            open: function() {
                $( this ).removeClass( "ui-corner-all" ).addClass( "ui-corner-top" );
                $( this ).css("z-index", 100);
            },
            close: function() {
                $( this ).removeClass( "ui-corner-top" ).addClass( "ui-corner-all" );
            }
        });
    };

    $(".ac_input").click(function () {
        var txt = $(".ac_input").attr("name");
        setTimeout(function() {
            switch (txt) {
                case "1":
                    changeauto(1);
                    break;
                case "5":
                    changeauto(4);
                    break;
                case "4":
                    break;
                case "9":
                    break;
                case "10":
                    break;
                case "11":
                    break;
                case "12":
                    break;
                case "13":
                    break;
                case "2":
                case "7":
                case "8":
                case "3":
                    changeauto(5);
                    break;
                default:
                    changeauto(1);
                    break;
            }
        },1000);
    });

    $(".search_btn").click(function () {
        var txt = $(".ac_input").attr("name")
        // input = $(this).prev().val() //取输入框值
        var input = $(".ac_input").val();
        input=stripscript(input);
        var search ="" ;
        if(txt ==1){
            if (input == null || input == undefined || input == '') {
                var AdProjectName =   $("#txtkeys").attr("placeholder");
                // AdProjectName = AdProjectName.replace(/[：]{1}[\u4e00-\u9fa5]+$/,"");
                regProject = AdProjectName.match(/[：]{1}.*$/);
                AdProjectName = regProject[0];
                AdProjectName = AdProjectName.replace(/^[：]{1}/,"");
                search = "search="+AdProjectName;
            }else {
                search = "search="+input;
            }
        }else {
            if (input == null || input == undefined || input == '') {
                search = "";
            }else {
                search = "search="+input;
            }
        }
        var newProjectUlr = window.location.pathname;
        var subwayType = false;
        if(newProjectUlr.startsWith('/houses/')||newProjectUlr.startsWith('/subways/')){
            subwayType = true;
            newProjectUlr = newProjectUlr.replace(/-?n{1}[0-9]+/,'');
        }
        var seacIndex =  newProjectUlr.indexOf("search");
        if(seacIndex!=-1){
            newProjectUlr = newProjectUlr.replace(/[-]*(search).*$/,'');
        }else {
            newProjectUlr = newProjectUlr;
        }
        switch (txt) {
            case "1":
                if(subwayType){
                    url = newProjectUlr+"-"+search;
                }else {
                    url = "/houses/-"+search;
                }
                break;
            case "5":
                url = "/news/"+search;
                break;

            case "4":  //商铺
                url = "/shops/"+search;
                break;
            case "3":  //租房
                url = "/rents/"+search;
                break;
            case "2"://二手房
                url = "/saleHouses/"+search;
                break;
            case "7"://二手房小区
                url = "/saleVillages/"+search;
                break;
            case "8"://租房小区
                url = "/villages/"+search;
                break;
            case "9"://写字楼房源
                url = "/scriptoriums/"+search;
                break;
            case "10"://成交二手房
                url = "/dealSales/"+search;
                break;
            case "11"://成交租房
                url = "/dealRents/"+search;
                break;
            case "12"://成交商铺
                url = "/dealShops/"+search;
                break;
            case "13"://写字楼项目
                url = "/officeProjects/"+search;
                break;
            case "14"://精装房列表
                url = "/decorations/"+search;
                break;
            case "15"://经纪人资讯
                url = "/agentnews/"+search;
                break;
            default :
                url = "/houses/" + search;
                break;
        }
        window.location.href = url;
        // }; //判断结束
    });

    function stripscript(s)
    {
        var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
        var rs = "";
        for (var i = 0; i < s.length; i++) {
            rs = rs+s.substr(i, 1).replace(pattern, '');
        }
        return rs;
    }

    var keys = '';
    if (keys != "" && keys != "null" && keys != null && keys != "0") {
        if (keys != -1) {
            $(".ac_input").val(keys);
        }
    }
    $(".ac_input").each(function () {
        var thisVal = $(".ac_input").val();    //判断文本框的值是否为空，有值的情况就隐藏提示语，没有值就显示
        if (thisVal != "") { $(".ac_input").siblings("span").hide(); }
        else { $(".ac_input").siblings("span").show(); }
        $(".ac_input").keyup(function () {
            $(".ac_input").siblings("span").hide();
        }).blur(function () {
            var val = $(this).val();
            if (val != "") {
                $(this).siblings("span").hide();
            } else {
                $(this).siblings("span").show();
            }
        });
    });
    //此方法用于搜索框无下拉时enter事件
    $(".ac_input").keydown(function (event) {
        if (event.keyCode == 13) {
            var selecetFlag = true;
            $(".ui-autocomplete").each(function () {
                if($(this).css("display") == "block"){
                    var item = $(this);
                    $(item).find("a").each (function () {
                        if($(this).hasClass("ui-state-focus")){
                            selecetFlag = false;
                            return false;
                        }
                    });
                }
            });
            if(selecetFlag){
                $(".search_btn").click();
            }
        }
    });
    $("#deleteButton").click(function () {
        // $("#search2017 .search .ac_input").val("");
        // $("#deleteButton").hide();
        var redirectUrl =location.pathname;
        if( redirectUrl != null || redirectUrl != undefined || redirectUrl != ''){
            if( redirectUrl.indexOf("search") != -1 ){
                redirectUrl =  redirectUrl.replace(/[-]*(search).*$/,'');
                window.location.href = redirectUrl;
            }else {
                // window.location.reload();
                $("#search2017 .search .ac_input").val("");
                $("#deleteButton").hide();
            }
        }
    });
    $("#search2017 .search .ac_input").val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
    $("#search2017 .search .ac_input").keyup(function(){
        $(this).val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
    })
});
//设置cookie time按分钟计时
function setCookie(name,value,time) {
    var exp = new Date();
    exp.setTime(exp.getTime() + time*60*1000);
    value =  encodeURI(value)
    document.cookie = name + "="+ value + ";expires=" + exp.toGMTString();
}
function delCookie(name)
{
    var exp = new Date();
    exp.setTime(exp.getTime() - 20000);
    var cval=getCookie(name);
    if(cval!=null)
        document.cookie= name + "="+cval+";expires="+exp.toGMTString();
}
function getCookie(name) {
    var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
    if(arr=document.cookie.match(reg)){
        return unescape(arr[2]);}
    else {
        return null;
    }
}
function setContentText( itemSummary,contentType) {
    var countNumber = "";
    var placeName = "";
    if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
        var Arr   = itemSummary.split(",");
        if(Arr.length>0){
            if(contentType == 0){
                placeName = "租房0套";
            }else {
                placeName = "二手房0套";
            }
            if(Arr[contentType]!=placeName){
                countNumber=    Arr[contentType].match(/\d+/g);
                countNumber = countNumber[0];
            }else {
                itemSummary =   "";
            }
        }
        if(countNumber !=0 && countNumber!=""){
            itemSummary = "约"+countNumber+"条房源";
        }
        return itemSummary;
    }
}