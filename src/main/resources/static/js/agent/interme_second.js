/**
 * Created by Administrator on 2019/3/29 0029.
 */
$(function(){
    //条件区域加载渲染
    if($(".condition").text() != ""){
        $("#option_info").css("display","block");
    }
    loadCondition();
    var r_or_s = location.pathname;
    if (r_or_s.indexOf('z1') != -1) {
        $("#btnSubway").attr("class","hover");
        $("#Search_ditie").css('display','block');
    }else {
        $("#btnRegion").attr("class","hover");
        $("#Search_zf").css('display','block')
    }
});
    function selectPrice(isAdd) {
        //总价、面积筛选条件渲染
        var bp = $("#option input[id='minPrice']").val();
        var ep = $("#option input[id='maxPrice']").val();
        var ba = $("#option input[id='minArea']").val();
        var ea = $("#option input[id='maxArea']").val();
        if(bp != "" || ep != ""){
            if (!isAdd) {
                $("#option_info").show();
                isAdd = true;
            }
            var str = "";
            bp = bp == "" ? 0 : bp;
            if(ep == ""){
                str = "大于等于" + bp;
            }else{
                if(bp == ""){
                    $("#option input[id='bp']").val("0");
                }
                str =  bp + "-" + ep;
            }
            $("#option_info").append("<div><span class='condition'>" + str + "万元</span><i></i> </div>");
            $("#option_info div:last").attr("onclick", "removeSelect(" + 3 + ");");
        }
        if(ba != "" || ea != ""){
            if (!isAdd) {
                $("#option_info").show();
                isAdd = true;
            }
            var str = "";
            ba = ba == "" ? 0 : ba;
            if(ea == ""){
                str = "大于等于" + ba;
            }else{
                if(ba == ""){
                    $("#option input[id='ba']").val("0");
                }
                str = ba + "-" + ea;
            }
            $("#option_info").append("<div><span class='condition'>" + str + "㎡</span><i></i> </div>");
            $("#option_info div:last").attr("onclick", "removeSelect(" + 4 + ");");
        }
    }

    function loadCondition() {
        var isAdd = false;
        //非手填项渲染
        selectPrice(isAdd);
        //清空全部按钮
        if($(".condition").text() != ""){
            $("#option_info").append("<a>清空筛选条件</a> ");
            $("#option_info a:last").attr("href", "/saleHouses/").attr("class", "clean");
        }
    }
    function price(priceIdName) {
        if($("#" + priceIdName + " input").eq(0).val() != null && $("#" + priceIdName + " input").eq(1).val() != null){
            $("#" + priceIdName + " input").eq(0).val($("#" + priceIdName + " input").eq(0).val().replace(/\D/g, ''));
            $("#" + priceIdName + " input").eq(1).val($("#" + priceIdName + " input").eq(1).val().replace(/\D/g, ''));
        }
        var num1 = Math.floor($("#" + priceIdName + " input").eq(0).val());
        var num2 = Math.floor($("#" + priceIdName + " input").eq(1).val());
        if (num1 == "" && num2 != "") {
            $("#" + priceIdName + " input").eq(0).val("0");
            $("#" + priceIdName + " input").eq(2).show();
        } else if (num2 == "" && num1 != "") {
            $("#" + priceIdName + " input").eq(2).show();
        } else if (num1 != "" || num2 != "") {
            $("#" + priceIdName + " input").eq(2).show();
        } else {
            $("#" + priceIdName + " input").eq(2).hide();
        }
    }
    price("Search_BuildAreaDomOk");
    price("Search_PriceDomOk");
    $("#Search_PriceDomOk input").keyup(function () {
        price("Search_PriceDomOk");
    })
    $("#Search_BuildAreaDomOk input").keyup(function () {
        price("Search_BuildAreaDomOk");
    })
    $("#Search_PriceDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            // $("#Search_Btn_Search1").click()
        }
    });
    $("#Search_BuildAreaDomOk").keydown(function (event) {
        if (event.keyCode == 13) {
            $("#Search_Btn_Searchs").click()
        }
    });

function showIndex(obj) {
    obj.click();
}
function __doPostBack(pager1, page) {
    var url = window.location.pathname;
    if(url.lastIndexOf("/")) {
        url +='/';
    }
    if (pager1 == "Search$Btn_Search1") {
        var priceBegin = $("#minPrice").val();
        var priceEnd = $("#maxPrice").val();
        var ref1 = url.replace(/-k[0-9]\d*/,''); //k最小值
        var ref2 = ref1.replace(/-x[0-9]\d*/,'');  //x最大值
        if (parseInt(priceEnd) < parseInt(priceBegin)){
            priceEnd = [priceBegin,priceBegin=priceEnd][0];
        }
        if(priceBegin != "" && priceBegin != 0)
            ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-k" + priceBegin;
        if(priceEnd != "")
            ref2 = ref2.replace(/-p[0-9]\d*/,'').replace(/\/p[0-9]\d*/,'\/') + "-x" + priceEnd;
        location.href = ref2;
    }
    if (pager1 == "Search$Btn_Searchs") {
        var areaBegin = $("#minArea").val();
        var areaEnd = $("#maxArea").val();
        var ref1 = url.replace(/-y[0-9]\d*/,''); //y最小值
        var ref2 = ref1.replace(/-e[0-9]\d*/,'');  //e最大值
        if (parseInt(areaEnd) < parseInt(areaBegin)){
            areaEnd = [areaBegin,areaBegin=areaEnd][0];
        }
        if(areaBegin != "" && areaBegin != 0)
            ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-y" + areaBegin;
        if(areaEnd != "")
            ref2 = ref2.replace(/-a[0-9]\d*/,'').replace(/\/a[0-9]\d*/,'\/') + "-e" + areaEnd;
        location.href = ref2;
    }
}
