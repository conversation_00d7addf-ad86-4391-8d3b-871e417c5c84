/**
 * Created by Administrator on 2017/9/4.
 */
$(function () {
    var textpd = [];
    //买新房
    $("#new_submit").click(function () {
        textpd = [];
        //区域必填
        textpd.push(xiala_require("con_bnzfqh_1", 0, "区域"));
        //户型必填
        textpd.push(xiala_require("con_bnzfqh_1", 1, "户型"));
        //预算必填
        textpd.push(xiala_require("con_bnzfqh_1", 2, "预算"));
        //手机必填
        textpd.push(tel_require("con_bnzfqh_1", 4));
        //验证码必填
        textpd.push(yzm_require("con_bnzfqh_1", 5));
        if ($.inArray("0", textpd) != -1) {
            return false;
        } else {
            var tphone = $.trim($("#phone0").val());//电话
            var yusuang = $("#new_yusuan").html();//预算
            var quyu = $("#new_qy").html();//区域
            var jusi = $("#new_huxing").html();//户型
            var miaos = $.trim($("#newms").val());//描述
            var yzm = $("#yzm0").val();//描述
            var params = {phone: tphone ,code:yzm,budget:yusuang,region:quyu,area:jusi,italy:miaos,type:1};
            // fangxiaoer.ajax("upguide", "{phone:'" + tphone + "',code:'" + yzm +"',budget:'" + yusuang + "',region:'" + quyu + "',area:'" + jusi + "',italy:'" + miaos + "',type:'" + 1 + "'}", function (data) {
            fangxiaoer.ajax("upguide", params, function (data) {
                if (data.status == "1") {
                    $(".success_tc").show();
                    $("#phone0").val("");
                    $("#new_yusuan").html("请选择");
                    $("#new_qy").html("请选择");
                    $("#new_huxing").html("请选择");
                    $("#newms").val("");
                    $("#yzm0").val("");
                    $("#yzm0").parents("ul").find(".input_desc").show();
                    $("span.yzm_btn").hide();
                    $("a.yzm_btn").show();
                    $("#con_bnzfqh_1").find(".errorBox").html("");
                    $("#con_bnzfqh_1").find(".my_xl_input").removeClass("error");
                    $("#con_bnzfqh_1").find(".ljz").hide();
                }
                textpd = [];
            });
        }
    })
    //买二手房
    $("#two_submit").click(function () {
        textpd = [];
        //区域必填
        textpd.push(xiala_require("con_bnzfqh_2", 0, "区域"));
        //户型必填
        textpd.push(xiala_require("con_bnzfqh_2", 1, "户型"));
        //预算必填
        textpd.push(xiala_require("con_bnzfqh_2", 2, "预算"));
        //手机必填
        textpd.push(tel_require("con_bnzfqh_2", 4));
        //验证码必填
        textpd.push(yzm_require("con_bnzfqh_2", 5));
        if ($.inArray("0", textpd) != -1) {
            return false;
        } else {
            var phone1 = $.trim($("#phone1").val());//电话
            var esfyusuan = $("#esfyusuan").html();//预算
            var esfqy = $("#esfqy").html();//区域
            var esfhuxing = $("#esfhuxing").html();//户型
            var esfms = $.trim($("#esfms").val());//描述
            var yzm =$("#yzm1").val();//验证码
            var params = {phone: phone1 ,code:yzm,budget:esfyusuan,region:esfqy,area:esfhuxing,italy:esfms,type:2};
            fangxiaoer.ajax("upguide",  params , function (data) {
                if (data.status == "1") {
                    $(".success_tc").show();
                    $("#phone1").val("");
                    $("#esfyusuan").html("请选择");
                    $("#esfqy").html("请选择");
                    $("#esfhuxing").html("请选择");
                    $("#esfms").val("");
                    $("#yzm1").val("");
                    $("#yzm1").parents("ul").find(".input_desc").show();
                    $("span.yzm_btn").hide();
                    $("a.yzm_btn").show();
                    $("#con_bnzfqh_2").find(".errorBox").html("");
                    $("#con_bnzfqh_2").find(".my_xl_input").removeClass("error");
                    $("#con_bnzfqh_2").find(".ljz").hide();
                }
            })
            textpd = [];
        }
    })
    //预约看房
    $("#kf_submit").click(function () {
        textpd = [];
        //楼盘名称
        textpd.push(is_null_require("con_bnzfqh_3", 0, "楼盘名称"));
        //上车地点
        textpd.push(is_null_require("con_bnzfqh_3", 1, "上车时间"));
        //上车时间
        textpd.push(is_null_require("con_bnzfqh_3", 2, "上车人数"));
        //手机
        textpd.push(tel_require("con_bnzfqh_3", 3));
        //验证码
        textpd.push(yzm_require("con_bnzfqh_3", 4));
        if ($.inArray("0", textpd) != -1) {
            return false;
        } else {

            var phoneztc = $.trim($("#phone2").val());
            var nameztc = $("#kfname").val();
            var carztc = $("#kpaddress").html();
            var timeztc = $("#datetimepicker_mask").val();
            var kfms = $.trim("看房人数："+$("#bnzf_kfrs").val());
            var yzm = $("#yzm2").val();//验证码
            var params = {phone: phoneztc ,code:yzm,area:nameztc,italy:kfms,buyTime:timeztc,region:"无", type:4};
            fangxiaoer.ajax("upguide",params, function (data) {
                if (data.status == "1") {
                    $(".success_tc").show();
                    $("#phone2").val("");
                    $("#kfname").val("");
                    $("#kpaddress").html("请选择");
                    $("#datetimepicker_mask").val("");
                    $("#kfms").val("");
                    $("#yzm2").val("");
                    $("#yzm2").parents("ul").find(".input_desc").show();
                    $("span.yzm_btn").hide();
                    $("a.yzm_btn").show();
                    $("#con_bnzfqh_3").find(".errorBox").html("");
                    $("#con_bnzfqh_3").find(".my_xl_input").removeClass("error");
                    $("#con_bnzfqh_3").find(".ljz").hide();
                    $("#bnzf_kfrs").val("");
                }
            });
            textpd = [];
        }
    })
    //求租
    $("#four_submit").click(function () {
        textpd = [];
        //区域必填
        textpd.push(xiala_require("con_bnzfqh_4", 0, "区域"));
        //租房方式
        textpd.push(xiala_require("con_bnzfqh_4", 1, "租房方式"));
        //户型必填
        textpd.push(xiala_require("con_bnzfqh_4", 2, "户型"));
        //预算必填
        textpd.push(xiala_require("con_bnzfqh_4", 3, "预算"));
        //手机必填
        textpd.push(tel_require("con_bnzfqh_4", 4));
        //验证码必填
        textpd.push(yzm_require("con_bnzfqh_4", 5));
        if ($.inArray("0", textpd) != -1) {
            return false;
        } else {
            var phone3 = $.trim($("#phone3").val());//电话
            var zfyusuan = $("#zfyusuan").html();//预算
            var zfqy = $("#zfqy").html();//区域
            var zffangshi=$("#zffangshi").html()//租房方式
            var zfhuxing = $("#zfhuxing").html();//户型
            var zfms = $.trim($("#esfms").val());//描述
            var yzm = $("#yzm5").val();//验证码
            var params = {phone: phone3 ,code:yzm,budget:zfyusuan,region:zfqy,housetype:zffangshi,area:zfhuxing,italy:zfms,region:zfqy,type:3};
            fangxiaoer.ajax("upguide",params , function (data) {
                if (data.status == "1") {
                    $(".success_tc").show();
                    $("#phone4").val("");
                    $("#zfyusuan").html("请选择");
                    $("#zfqy").html("请选择");
                    $("#zfhuxing").html("请选择");
                    $("#zfms").val("");
                    $("#yzm5").val("");
                    $("#yzm5").parents("ul").find(".input_desc").show();
                    $("span.yzm_btn").hide();
                    $("a.yzm_btn").show();
                    $("#con_bnzfqh_2").find(".errorBox").html("");
                    $("#con_bnzfqh_2").find(".my_xl_input").removeClass("error");
                    $("#con_bnzfqh_2").find(".ljz").hide();
                }
            })
            textpd = [];
        }
    })
    //过户代办
    $("#gh_submit").click(function () {
        textpd = [];
        //区域
        textpd.push(xiala_require("con_bnzfqh_5", 0, "区域"));
        //楼盘名称
        textpd.push(is_null_require("con_bnzfqh_5", 1, "楼盘名称"));
        //预算
        textpd.push(is_null_require("con_bnzfqh_5", 2, "贷款金额"));
        //手机
        textpd.push(tel_require("con_bnzfqh_5", 3));
        //验证码
        textpd.push(yzm_require("con_bnzfqh_5", 4));
        if ($.inArray("0", textpd) != -1) {
            return false;
        } else {
            var phone4 = $.trim($("#phone4").val());
            var uname = $("#uname").val();
            var qy = $("#gh_qyv").html();
            var yzm = $("#yzm4").val();
            var budget = $("#Div1").html();
            var params = {phone: phone4 ,code:yzm,region:qy,area:uname,budget:budget,type:6};
            fangxiaoer.ajax("upguide",params , function (data) {
                if (data.status == "1") {
                    $(".success_tc").show();
                    $("#phone4").val("");
                    $("#uname").val("");
                    $("#gh_qyv").html("请选择");
                    $("#Div1").html("请选择");
                    $("#con_bnzfqh_5").find(".errorBox").html("");
                    $("#yzm4").val("");
                    $("#yzm4").parents("ul").find(".input_desc").show();
                    $("span.yzm_btn").hide();
                    $("a.yzm_btn").show();
                    $("#con_bnzfqh_5").find(".my_xl_input").removeClass("error");
                    $("#con_bnzfqh_5").find(".ljz").hide();
                }
            });
            textpd = [];
        }
    })
})
//下拉必选
function xiala_require(tab_id, index, prompt) {
    $("#" + tab_id).find(".errorBox:eq(" + index + ")").html('');
    if (tab_id == "con_bnzfqh_3") {
        var ts_html = $("#" + tab_id).find(".my_xl_txt:eq(0)").html();
        if (ts_html.substr(0, 3) == "请选择") {
            $("#" + tab_id).find(".errorBox:eq("+index+")").html('<label for="one_qy" class="error">请选择' + prompt + '</label>');
            $("#" + tab_id).find(".ljz:eq(" + index + ")").hide();
            return "0";
        } else {
            $("#" + tab_id).find(".errorBox:eq(" + index + ")").parents("li").find(".ljz").show();
        }
    } else {
        var ts_html=$("#" + tab_id).find(".my_xl_txt:eq(" + index + ")").html();
        if (ts_html.substr(0, 3) == "请选择") {
            $("#" + tab_id).find(".errorBox:eq(" + index + ")").html('<label for="one_qy" class="error">请选择' + prompt + '</label>');
            $("#" + tab_id).find(".ljz:eq(" + index + ")").hide();
            return "0";
        } else {
            $("#" + tab_id).find(".ljz:eq(" + index + ")").show();
        }
    }
    return "1";
}
//手机号码
function tel_require(tab_id, index) {
    $("#" + tab_id).find(".phone_error").html('');
    var val = $("#" + tab_id).find(".phone_input").val();
    if (val == "") {
        $("#" + tab_id).find(".phone_error").html('<label for="one_qy" class="error">请填写联系方式</label>');
        return "0";
    } else {
        var reg = new RegExp("^1[0-9]{10}$", "ig");
        if (!reg.test(val) && val != '13500000000') {
            $("#" + tab_id).find(".phone_error").html('<label for="one_qy" class="error">请填写正确的联系方式</label>');
            $("#" + tab_id).find(".phone_error").parents("li").find(".ljz").hide();
            return "0";
        } else {
            $("#" + tab_id).find(".phone_error").parents("li").find(".ljz").show();
        }
    }
    return "1";
}
//验证码
function yzm_require(tab_id, index) {
    $("#" + tab_id).find(".yzm_error").html('');
    var val = $("#" + tab_id).find(".yzm_input").val();
    if (val == "" || val.length < 6 || isNaN(val)) {
        $("#" + tab_id).find(".yzm_error").html('<label for="one_qy" class="error">请填写验证码</label>');
        return "0";
    } else {
        var phone = $("#" + tab_id).find(".phone_input").val();
        var code = $("#" + tab_id).find(".yzm_input").val();
        var r = 1;
        $.ajax({
            type: "POST",
            async: false,
            data: { action: "CheckSend", mobile: phone, code: code },
            url: "/checkPasscode",
            success: function (data) {
                if (data == "1") {
                    r = "1";
                    $("#" + tab_id).find(".yzm_error").parents("li").find(".ljz").show();
                }
                else {
                    $("#" + tab_id).find(".yzm_error").html('<label for="one_qy" class="error">验证码错误</label>');
                    $("#" + tab_id).find(".yzm_error").parents("li").find(".ljz").hide();
                    r = "0";
                }
            },
            error: function (error) {
                console.log(error);
            }
        });
        if (r == 1) {
            return "1";
        } else {
            return "0";
        }
    }
    return "1";
}
//楼盘名称、姓名、时间非空验证
function is_null_require(tab_id, index, prompt) {
    $("#" + tab_id).find(".errorBox:eq(" + index + ")").html('');
    var val = $("#" + tab_id).find("input:eq(" + index + ")").val();
    if (val == "") {
        $("#" + tab_id).find(".errorBox:eq(" + index + ")").html('<label for="one_qy" class="error">请填写' + prompt + '</label>');
        $("#" + tab_id).find(".ljz:eq(" + index + ")").hide();
        return "0";
    } else {
        $("#" + tab_id).find(".ljz:eq(" + index + ")").show();
    }
    return "1";
}
