Vue.config.productionTip = false;
var baiduMap = new Vue({
    el: '#map',
    data: {
        houseType:type,//地图类型 1  新房            2  二手房            3 租房
        map: {},
        mapZoom:12,//初始化级别
        maxRegion:14,//地图区域和房源分界线  二手房租房 区域和板块的分界线
        maxPlat:16,//地图板块和房源分界线
        gapZoom:-1,//与级别的差
        lngRight: "", //四个坐标点
        lngLeft: "",
        latTop: "",
        latBottom: "",
        isSchool: "", //是不是学区房
        isSubWay: "", //是不是地铁房
        isPayment: "", //是不是低首付
        isExistinfo: "", //是不是现房
        rentType:1,//租赁类型
        scroll:true,//加载开关
        housingEstate:"",//小区id
        page:1,//列表页
        zoomType:1,//地图显示级别  1区域  2 板块  3房源
        navIsActive: navType, //左侧菜单选中项
        windowWidth: window.innerWidth, //
        height: window.innerHeight-50, // //50是主导航栏高度
        listwidth: 420, //左侧列表宽度
        showSelectList: -1, //筛选项当前显示的是哪一个  -1代表没有列表显示
        regionList: { //区域
            ActiveText: '区域',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        typeList: { //新房类型 二手房户型
            ActiveText: '类型',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        priceList: { //新房价格区间 二手房总价
            ActiveText: '价格',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        schoolList: { //学校类型
            ActiveText: '类别',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        subWayList: { //地铁线
            ActiveText: '地铁',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        areaList: { //面积
            ActiveText: '面积',
            ActiveId: "",
            content: [{
                id: "",
                name: ''
            }]
        },
        newHousList: [], //房源列表
        searchPlateId:"", //搜索框关键字为板块时id
        searchHierarchy:"",//点击搜索关键字所在层级（1：区域 2：板块 3：小区）
        searchTitle:"",//搜索关键字
    },
    computed: {
        mapWidth: function() {
            var mapWidth;
            if(this.houseType==2){
                mapWidth=this.windowWidth - this.listwidth
            }else{
                mapWidth=this.windowWidth - 60 - this.listwidth
            }
            return mapWidth
        },
        //列表无数据时展示
        recommentSwitch:function () {
            if(this.newHousList == ''||this.newHousList == undefined||this.newHousList.length == 0){
                return "block";
            }else {
                return "none";
            }
        }
    },
    methods: {
        //初始化
        init: function() {
            //区分新房二手房租房的细微差别
            baiduMap.typeList.ActiveText=baiduMap.houseType==1?"类型":"户型"
            baiduMap.listwidth=baiduMap.houseType==1?420:530;
            baiduMap.map = new BMap.Map("baiduMap",{enableMapClick:false,minZoom:12,maxZoom:17});//创建地图并且禁用弹出信息框（最大50米，最小5公里）
            var point = new BMap.Point(123.439941, 41.798142); //中心点
            baiduMap.map.centerAndZoom(point,baiduMap.mapZoom); // 编写自定义函数，创建标注
            // 添加比例尺控件
            baiduMap.map.addControl(new BMap.ScaleControl({anchor: BMAP_ANCHOR_BOTTOM_RIGHT}));
            // this.plyList = [];
            baiduMap.map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
            baiduMap.showHouse() //初始化第一次获取地图数据
            //移动时重新获取数据
            baiduMap.map.addEventListener("dragend", function() {
                baiduMap.page=1;
                baiduMap.housingEstate="";
                // baiduMap.regionList.ActiveId='';
                // baiduMap.regionList.ActiveText="区域";
                if(baiduMap.map.getZoom() >= baiduMap.maxRegion && (baiduMap.regionList.ActiveId == null || baiduMap.regionList.ActiveId == "")){
                    baiduMap.regionList.ActiveId='';
                    baiduMap.regionList.ActiveText="区域";
                }
                baiduMap.showHouse();
                baiduMap.showSelectList=-1;
                baiduMap.setHouseHigth();
                $(".village_details").hide();
            })
            //缩放时重新获取数据
            baiduMap.map.addEventListener("zoomend", function() {
                if(baiduMap.maxRegion>=(baiduMap.map.getZoom()-baiduMap.gapZoom)){
                    baiduMap.regionList.ActiveText="区域";
                    baiduMap.regionList.ActiveId='';
                }
                baiduMap.housingEstate="";
                baiduMap.page=1;
                baiduMap.showHouse();
                baiduMap.showSelectList=-1;
                $(".village_details").hide();
            });
            // if(baiduMap.searchTitle != null && baiduMap.searchTitle != "" && baiduMap.searchTitle != undefined){
            //     baiduMap.housingEstate=data._id
            // }
          if(baiduMap.houseType==2){//二手房
                //初始化 下拉选择区域
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/viewRegions", baiduMap.regionList)
                //初始化  下拉选择价格
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/viewPriceFilter", baiduMap.priceList)
                //初始化  下拉选择房源户型
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getRoomTypeFilter", baiduMap.typeList)
                //初始化 下拉选择面积
                baiduMap.navInit("https://ltapi.fangxiaoer.com/apiv1/house/getDecAreaFilter", baiduMap.areaList)
            }

            //窗口大小变化重新计算高度
            window.onresize = function(){
                baiduMap.height=window.innerHeight-120;
            }
            //滚动加载
            window.document.getElementsByClassName("HouseList")[0].addEventListener("scroll",winScroll);
            function winScroll(){
                var scrTop = window.document.getElementsByClassName("HouseList")[0].scrollTop;
                var windoweight = (window.document.getElementsByClassName("HouseList")[0].getElementsByTagName("ul")[0].offsetHeight-(baiduMap.height-120));
                if(scrTop== windoweight){
                    baiduMap.scroll=false;
                    baiduMap.page += 1;
                    if(!(baiduMap.houseType==1&&baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion)){
                        document.getElementById("loading").style.display="block"
                    }
                    console.log("滚动")
                    baiduMap.regionHouse();
                }
            }
        },
        //设置地图中心点
        SetMap: function ( lat,lng, size) {
            baiduMap.map.setZoom(size);
            baiduMap.map.bdpoit = new BMap.Point(lng, lat);
            baiduMap.map.panTo(baiduMap.map.bdpoit); //将地图移动到点
            baiduMap.showHouse()
        },
        //模糊搜索+筛选条件 地图打点及时刷新数据使用
        SetMap1: function ( lat,lng, size) {
            // baiduMap.map.setZoom(size);
            baiduMap.map.bdpoit = new BMap.Point(lng, lat);
            // baiduMap.map.panTo(baiduMap.map.bdpoit); //将地图移动到点
            baiduMap.map.centerAndZoom(baiduMap.map.bdpoit,size);
            baiduMap.showHouse()
        },
        //构造 覆盖物
        overlay: function(point, text, mouseoverText,price) {
            this._point = point;
            this._text = text;
            this._overText = mouseoverText;
            this._price = price;
        },
        //下拉选择不限
        navNormal:function(data,text){
            data.ActiveText=text;
            baiduMap.showSelectList=-1;
            data.ActiveId='';
            baiduMap.showHouse();
        },
        //选择左侧菜单
        navLeft: function(int) {
            baiduMap.SetMap(41.798142,123.439941,baiduMap.maxRegion-2)
            baiduMap.navNormal(baiduMap.regionList,'区域');
            baiduMap.navNormal(baiduMap.typeList,'类型');
            baiduMap.navNormal(baiduMap.priceList,'价格');
            baiduMap.navNormal(baiduMap.schoolList,'类别');
            baiduMap.navNormal(baiduMap.subWayList,'地铁');
            baiduMap.page=1;
            this.isSchool = ""
            this.isSubWay = ""
            this.isPayment = ""
            this.isExistinfo = ""
            switch (int) {
                case 0:
                    this.navIsActive = 0
                    break;
                case 1:
                    this.navIsActive = 1
                    this.isPayment = 1
                    break;
                case 2:
                    this.navIsActive = 2
                    this.isExistinfo = 1
                    break;
                case 3:
                    this.navIsActive = 3
                    this.isSchool = 1
                    break;
                case 4:
                    this.navIsActive = 4
                    this.isSubWay = 1
                    break;
                case 5:
                    this.navIsActive = 5
                    this.rentType = 1
                    break;
                case 6:
                    this.navIsActive = 6
                    this.rentType = 2
                    break;
                default:
                    break;
            }
            baiduMap.showHouse()
        },
        //筛选条件 下拉菜单初始化
        navInit: function(url, nav) {
            ajax({
                type: "POST",
                url: url,
                dataType: "json",
                data: {},
                success: function(data) {
                    nav.content = data.content
                },
                error: function(data) {
                    console.log(data)
                }
            })
        },
        //筛选条件 下拉菜单选择
        navSelect: function(showData, dataName,dataId) {
            baiduMap.page=1;
            showData.ActiveText = dataName;
            this.showSelectList = -1;
            showData.ActiveId = dataId;
            baiduMap.showHouse()
            $(".village_details").hide();
        },
        deleteSearchValue: function() {
            this.searchTitle = "";
            $("#searchNames").val("");
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
        },
        deleteAllValue: function() {
            this.searchNames = "";
            baiduMap.searchTitle = "";
            $("#searchNames").val("");
            baiduMap.showSelectList=-1;
            this.regionList.ActiveText="区域";
            this.regionList.ActiveId='';
            this.priceList.ActiveText="价格";
            this.priceList.ActiveId='';
            this.areaList.ActiveText="面积";
            this.areaList.ActiveId='';
            this.typeList.ActiveText="户型";
            this.typeList.ActiveId='';
            baiduMap.showHouse();
            baiduMap.setHouseHigth();
            $("#deleteButton").hide();
            var redirectUrl =location.pathname;
            window.location.href = redirectUrl;
        },
        setHouseHigth:function () {
            if(this.regionList.ActiveId != ''||this.priceList.ActiveId != ''||this.areaList.ActiveId != ''||this.typeList.ActiveId !=''||this.searchTitle != ''){
                baiduMap.houseListheigth =  window.innerHeight-50 -47;
            }else {
                baiduMap.houseListheigth =  window.innerHeight-50;
            }
        },
        //区域筛选条件 下拉菜单选择
        navSelect1: function(showData, dataName,dataId) {
            baiduMap.page=1;
            showData.ActiveText = dataName;
            this.showSelectList = -1;
            showData.ActiveId = dataId;
            $(".village_details").hide();
        },
        //获取地图房源
        newHouseList: function() {
           if(baiduMap.houseType==2){//二手房获取地图点
                ajax({
                    type: "POST",
                    url: "/searchSecMap",
                    dataType: "json",
                    // async:false,
                    data: {
                        zoomLevel:baiduMap.map.getZoom()-baiduMap.gapZoom,
                        moneyId: baiduMap.priceList.ActiveId,//价格id
                        areaId:baiduMap.areaList.ActiveId,//面积id
                        regionId: baiduMap.regionList.ActiveId,//区域id
                        platId:"",
                        layoutId: baiduMap.typeList.ActiveId,
                        searchTitle:baiduMap.searchTitle,
                        SubId:"",//小区id
                        leftLng:(baiduMap.map.getZoom()-baiduMap.gapZoom)>baiduMap.maxRegion?baiduMap.lngLeft:"",//四个坐标点  接口名有错误（错着写是对的！！）
                        leftLat:(baiduMap.map.getZoom()-baiduMap.gapZoom)>baiduMap.maxRegion?baiduMap.latTop:"",
                        rightLng:(baiduMap.map.getZoom()-baiduMap.gapZoom)>baiduMap.maxRegion?baiduMap.lngRight:"",
                        rightLat:(baiduMap.map.getZoom()-baiduMap.gapZoom)>baiduMap.maxRegion?baiduMap.latBottom:""
                    },
                    beforeSend: function() {
                    },
                    success: function(data) {
                        if(data.content.length > 0){
                            $(".noHouseMap").hide();
                        }
                        // console.log(data);
                        console.log(baiduMap.map.getZoom());
                        var price='';
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                            for (var i =0 ;i < data.content.length;i++){//小区层级
                                if(data.content[i].UnitPrice == null || data.content[i].UnitPrice == 0){
                                    price="暂无资料";
                                }else{
                                    price = data.content[i].UnitPrice+'元/m²';
                                }
                                baiduMap.showRegionOverlay(new BMap.Point(data.content[i].Longitude,data.content[i].Latitude),data.content[i].Name,"二手房"+data.content[i].Number+"套","",baiduMap.regionList.SubId,"",data.content[i].SubId,price);
                            }
                        }else if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion){
                            for (var i =0 ;i < data.content.length;i++){//板块层级
                                if(data.content[i].UnitPrice == null || data.content[i].UnitPrice == 0){
                                    price="暂无资料";
                                }else{
                                    price = data.content[i].UnitPrice+'元/m²';
                                }
                                baiduMap.showRegionOverlay(new BMap.Point(data.content[i].Longitude,data.content[i].Latitude),data.content[i].Name,data.content[i].Number+"套","",data.content[i].PlatId,"","",price);
                                // console.log("层级"+baiduMap.searchHierarchy);
                                if(baiduMap.searchHierarchy == 2){
                                    if(data.content[i].UnitPrice == null || data.content[i].UnitPrice == 0){
                                        price="暂无资料";
                                    }else{
                                        price = data.content[i].UnitPrice+'元/m²';
                                    }
                                    baiduMap.showRegionOverlay(new BMap.Point(data.content[i].Longitude,data.content[i].Latitude),data.content[i].Name,data.content[i].Number+"套","",baiduMap.regionList.SubId,"",data.content[i].SubId,price);
                                }
                            }
                        }else{
                            for (var i =0 ;i < data.content.length;i++){//区域层级
                                if((data.content[i].UnitPrice == null || data.content[i].UnitPrice == 0 ) && !baiduMap.searchHierarchy){
                                    price="暂无资料";
                                }else{
                                    price = data.content[i].UnitPrice+'元/m²';
                                }
                                baiduMap.showRegionOverlay(new BMap.Point(data.content[i].Longitude,data.content[i].Latitude),data.content[i].Name,data.content[i].Number+"套","",data.content[i].RegionId,"","",price);
                                // console.log("subId"+data.content[i].SubId);
                                if(baiduMap.searchHierarchy == 3){
                                    if(data.content[i].UnitPrice == null || data.content[i].UnitPrice == 0){
                                        price="暂无资料";
                                    }else{
                                        price = data.content[i].UnitPrice+'元/m²';
                                    }
                                    baiduMap.showRegionOverlay(new BMap.Point(data.content[i].Longitude,data.content[i].Latitude),data.content[i].Name,data.content[i].Number+"套","",baiduMap.regionList.SubId,"",data.content[i].SubId,price);
                                }
                            }
                        }
                    },
                    error: function(data) {
                        console.log(data)
                    }
                });
            }
            baiduMap.regionHouse();//区域时房源列表（区域时 排序按照新房列表页）
        },
        //房源列表
        regionHouse:function(){
            baiduMap.saleHouseListRegionAjax();
        },

        //二手房列表的数据获取
        saleHouseListRegionAjax:function(){
            ajax({
                type:"POST",
                url:"/searchSecMapLeftList",
                dataType: "json",
                async:false,
                data: {
                    regionId:baiduMap.regionList.ActiveId,//区域id
                    plateId:"",//板块id
                    room:baiduMap.typeList.ActiveId,//居室id
                    latitude:"",//经纬度
                    longitude:"",//经纬度
                    priceId: baiduMap.priceList.ActiveId,//价格id
                    areaId:baiduMap.areaList.ActiveId,//面积id
                    page:baiduMap.page,//页数
                    pageSize:10,//分页
                    subId:baiduMap.housingEstate,//小区id
                    searchTitle:baiduMap.searchTitle,
                    leftLng:baiduMap.lngLeft,
                    leftLat:baiduMap.latTop,
                    rightLng:baiduMap.lngRight,
                    rightLat:baiduMap.latBottom
                },
                beforeSend: function() {
                },
                success: function(data) {
                    document.getElementById("loading").style.display="none"
                    if(data.status == 1){
                        if(data.content.length == 0 && baiduMap.page==1){
                            $(".noHouseMap").show();
                        }else{
                            $(".noHouseMap").hide();
                        }
                        // console.log(data.content)
                        if(data.content.length>0){
                            if(baiduMap.page==1){
                                baiduMap.newHousList =data.content
                            }else{
                                for (i=0;i<data.content.length;i++){//10  是分页大小
                                    baiduMap.newHousList.push(data.content[i])
                                    baiduMap.scroll=true;
                                }
                            }
                        }else {
                            if(baiduMap.page==1){
                                // $(".noHouseMap").show();
                                baiduMap.newHousList = "";
                            }
                        }
                    }
                },
                error: function(data) {
                    document.getElementById("loading").style.display="none"
                    console.log(data)
                }
            });
        },
        //定义区域或板块的 覆盖物
        showRegionOverlay: function(point, text, num , mapUrl,regionId,subId,housingId,unitPrice) {
            ComplexCustomOverlay.prototype = new BMap.Overlay();
            ComplexCustomOverlay.prototype.initialize = function(map) {
                this._map = map;
                var div = this._div = document.createElement("div");
                div.style.position = "absolute";
                div.style.zIndex = BMap.Overlay.getZIndex(this._point.lat);
                var span = this._span = document.createElement("span");
                div.appendChild(span);
                this._text!= undefined && this._text.length>5?span.className="shadow":""
                span.appendChild(document.createTextNode(this._text));
                var that = this;
                var arrow = this._arrow = document.createElement("div");
                baiduMap.houseType==1

                if(subId!=""){
                    baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion?div.className="showsubWay":""
                    div.style.backgroundColor = "#fffff";
                }else{
                    if(baiduMap.houseType==1){
                        baiduMap.map.getZoom()-baiduMap.gapZoom<=baiduMap.maxRegion?div.className="showRegion":div.className="showHouse"
                    }else if((baiduMap.houseType==2)||(baiduMap.houseType==3) ){
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                            div.className="showHouse"
                        }else if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion){
                            div.className="showPlat"
                            var att= document.createAttribute("data-id")
                            att.value=regionId
                            div.setAttributeNode(att)
                        }else{
                            div.className="showRegion"
                            //为class=showRegion 添加一个（data-id）属性
                            var att= document.createAttribute("data-id")
                            att.value=regionId
                            div.setAttributeNode(att)
                        }
                    }

                    //搜索框查询具体小区 展示为小区样式及层级
                    if((baiduMap.searchTitle != null && baiduMap.searchTitle != "" && baiduMap.searchTitle != undefined) && baiduMap.searchHierarchy == "3"){
                        div.className="newShowHouse";
                        div.style.backgroundColor = "#ff5200";
                        var span1 = this._span = document.createElement("b");
                        var b1 = this._span = document.createElement("s");
                        div.appendChild(span1);
                        span1.appendChild(document.createTextNode(this._overText));
                        span1.appendChild(b1);
                        b1.after(document.createTextNode(this._price));
                    }else if( baiduMap.searchHierarchy == "2" && (baiduMap.searchTitle != null && baiduMap.searchTitle != "" && baiduMap.searchTitle != undefined)){ //搜板块显示小区层级
                        div.className="newShowHouse";
                        //搜索板块显示小区层级样式
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion){
                            div.style.backgroundColor = "#ff5200";
                            var span1 = this._span = document.createElement("b");
                            var b1 = this._span = document.createElement("s");
                            div.appendChild(span1);
                            span1.appendChild(document.createTextNode(this._overText));
                            span1.appendChild(b1);
                            b1.after(document.createTextNode(this._price));
                        }
                    }else if(baiduMap.searchHierarchy == "1" && (baiduMap.searchTitle != null && baiduMap.searchTitle != "" && baiduMap.searchTitle != undefined)){
                        div.className="showRegion"
                    }
                    //点击进入小区层级样式
                    if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                        div.style.backgroundColor = "#ff5200";
                        var span1 = this._span = document.createElement("b");
                        var b1 = this._span = document.createElement("s");
                        div.appendChild(span1);
                        span1.appendChild(document.createTextNode(this._overText));
                        span1.appendChild(b1);
                        b1.after(document.createTextNode(this._price));
                    }else if(!baiduMap.searchTitle){
                        div.style.backgroundColor = "#ff5200";
                        var span1 = this._span = document.createElement("b");
                        div.appendChild(span1);
                        span1.appendChild(document.createTextNode(this._price));
                        var b1 = this._span1 = document.createElement("s");
                        div.appendChild(b1);
                        b1.appendChild(document.createTextNode(this._overText));
                    }else if(baiduMap.searchHierarchy == 1 && baiduMap.regionList.ActiveId != null){
                        div.style.backgroundColor = "#ff5200";
                        var span1 = this._span = document.createElement("b");
                        div.appendChild(span1);
                        span1.appendChild(document.createTextNode(this._price));
                        var b1 = this._span1 = document.createElement("s");
                        div.appendChild(b1);
                        b1.appendChild(document.createTextNode(this._overText));
                    }
                }
                arrow.style.position = "absolute";
                arrow.style.width = "11px";
                arrow.style.height = "10px";
                arrow.style.top = "22px";
                arrow.style.left = "20px";
                div.appendChild(arrow);

                div.onmouseover = function() {
                    if(subId!=""){
                        this.style.backgroundColor = "#e66a6a";
                        baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion?this.className="showsubWayHover":""
                    }else{
                        this.style.backgroundColor = "#41a8f3";//鼠标悬浮背景色是蓝色
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                            div.className="showHouseHover"
                        }else if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion){
                            // baiduMap.houseType==1?div.className="showHouseHover":div.className="showPlat"
                        }
                    }
                    this.style.zIndex="99999";
                }
                div.onmouseout = function() {
                    if(subId!=""){
                        this.style.backgroundColor = "#fff";
                        baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion?this.className="showsubWay":""
                        this.style.zIndex="99900"
                    }else{
                        this.style.backgroundColor = "#ff5200";//鼠标移开背景色是主题色
                        if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxPlat){
                            div.className="showHouse"
                        }else if( baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion){
                            // baiduMap.houseType==1?div.className="showHouse":div.className="showPlat"
                        }
                        this.style.zIndex=""
                    }
                }
                var clickPoint=this._point
                var urlText=mapUrl
                var clickText=text
                div.onclick = function(){
                 /*   if($(this).hasClass('showRegion')){
                        baiduMap.regionList.ActiveId = $(this).attr("data-id")
                    }else if($(this).hasClass('showPlat')){
                        baiduMap.searchPlateId = $(this).attr("data-id")
                    }*/
                    baiduMap.searchTitle = "";
                    $(".searchMapInput").val("");
                    $("#deleteButton").hide();

                    if(baiduMap.map.getZoom()-baiduMap.gapZoom<=baiduMap.maxRegion){
                        //区域层级点击直接进入小区层级
                        if(baiduMap.searchHierarchy != null && baiduMap.searchHierarchy != "" && baiduMap.searchHierarchy != undefined){
                            baiduMap.SetMap(clickPoint.lat,clickPoint.lng,baiduMap.maxRegion+2);
                            baiduMap.page=1;
                            baiduMap.housingEstate=housingId;
                            baiduMap.showHouse();
                            return;
                        }
                        //如果是区域，传regionId
                        if($(this).hasClass('showRegion')){
                            baiduMap.regionList.ActiveId = $(this).attr("data-id");
                        }

                        //点击区域展示 改区域所有版块（用SetMap方法会有问题，因此要用SetMap1）
                        baiduMap.SetMap1(clickPoint.lat,clickPoint.lng,baiduMap.maxRegion);
                        baiduMap.regionList.ActiveId=regionId==undefined?"":regionId;
                        //如果是关键字搜索的结果，点击不给区域的框赋值
                        if(!baiduMap.searchTitle){
                            baiduMap.regionList.ActiveText=clickText
                        }
                    }else{
                        if(( baiduMap.map.getZoom()-baiduMap.gapZoom<=baiduMap.maxPlat)&&(baiduMap.map.getZoom()-baiduMap.gapZoom>baiduMap.maxRegion)&&((baiduMap.houseType==2)||(baiduMap.houseType==3))){
                            if($(this).hasClass('showPlat')) {
                                baiduMap.searchPlateId = $(this).attr("data-id")
                            }
                            //板块层级 点击小区 展示该小区及该小区周边小区（用SetMap方法会有问题，因此要用SetMap1）
                            baiduMap.SetMap1(clickPoint.lat,clickPoint.lng,baiduMap.maxPlat)
                        }else if(baiduMap.houseType==1){
                            window.open(urlText);
                        }else if((baiduMap.houseType==2)||(baiduMap.houseType==3)){
                            baiduMap.page=1
                            baiduMap.housingEstate=housingId;
                            baiduMap.showHouse();
                            ajax({
                                type: "POST",
                                url: "/querySubDetail",
                                dataType: "json",
                                data: {subId:housingId},
                                success: function(data) {
                                   console.log(data.content)
                                    subDetail = data.content;
                                    $("#su_title").text(subDetail.title);
                                    if(subDetail.buildDate != null && subDetail.buildDate != ''){
                                        $("#su_build").text('建筑年代'+subDetail.buildDate);
                                    }

                                    $("#su_address").text(subDetail.address.replace("辽宁省",''));
                                    if(subDetail.unitPrice != 0 && (subDetail.unitPrice != null || subDetail.unitPrice !='' || subDetail.unitPrice != undefined)){
                                        $(".jun_price").text("均价");
                                        $("#su_unitprice").text(subDetail.unitPrice);
                                        $("#su_unitprice").show();
                                        $(".jun_unit").text('元/m²');
                                        $(".jun_unit").show();
                                    }else if(subDetail.unitPrice == 0){
                                        $(".jun_price").text("暂无资料");
                                        // $(".jun_price").css("color",'#999');
                                        $(".jun_unit").hide();
                                        $("#su_unitprice").hide();
                                    }
                                    if(subDetail.increaseRate != 0 && subDetail.increaseRate > 0){
                                        $("#su_rate").text(Math.abs(subDetail.increaseRate)+"%");
                                        $(".su_chain").text("环比上周高");
                                        $("#su_rate").css("color","#ff5200")
                                    }else if(subDetail.increaseRate != 0 && subDetail.increaseRate < 0){
                                        $(".su_chain").text("环比上周低");
                                        $("#su_rate").text(Math.abs(subDetail.increaseRate)+"%");
                                        $("#su_rate").css("color","#55a700")
                                    }else{
                                        $(".su_chain").text("环比上周");
                                        $("#su_rate").text('----');
                                        $("#su_rate").css("color","#999")
                                    }
                                    $('.village_details').show();
                                },
                                error: function(data) {
                                    console.log(data)
                                }
                            })
                        }
                    }
                }
                baiduMap.map.getPanes().labelPane.appendChild(div);
                return div;
            }
            ComplexCustomOverlay.prototype.draw = function() {
                var map = this._map;
                var pixel = map.pointToOverlayPixel(this._point);
                this._div.style.left = pixel.x - parseInt(this._arrow.style.left) + "px";
                this._div.style.top = pixel.y - 30 + "px";
            }
            var myCompOverlay = new ComplexCustomOverlay(point, text, num,unitPrice)
            baiduMap.map.addOverlay(myCompOverlay);
        },
        //每次操作后 重新获取所有数据
        showHouse: function() {
            baiduMap.map.clearOverlays(); //清除覆盖物
            var showPoint = this.map.getBounds();
            var bssw = showPoint.getSouthWest() //可视区域左下角
            var bsne = showPoint.getNorthEast() //可视区域右上角
            // alert(bssw.lng + "|" + bsne.lng + ";" + bsne.lat + "|" + bssw.lat);
            /*this.lngRight = bsne.lng
            this.lngLeft = bssw.lng
            this.latTop = bsne.lat
            this.latBottom = bssw.lat*/
            this.lngRight =baiduMap.map.getZoom()>=baiduMap.maxRegion?bsne.lng: '';
            this.lngLeft = baiduMap.map.getZoom()>=baiduMap.maxRegion ? bssw.lng:'';
            this.latTop = baiduMap.map.getZoom()>=baiduMap.maxRegion? bsne.lat:'';
            this.latBottom = baiduMap.map.getZoom()>=baiduMap.maxRegion? bssw.lat:'';
            // console.log("newhouseList 前")
            this.newHouseList()
            if(baiduMap.isSubWay!=""){
                baiduMap.map.addOverlay(polyline[baiduMap.subWayList.ActiveId]);

            }
        },
    }
})
//构造自定义覆盖物
function ComplexCustomOverlay(point, text, num,price) {
    this._point = point;
    this._text = text;
    this._overText = num;
    this._price = price;
}

baiduMap.init()
