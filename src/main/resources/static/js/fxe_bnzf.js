$(function () {
    //发送验证码
    $(".fxe_ReSendValidateCoad").click(function () {
        if ($("#phone0").val() == "") {
            alert("请填写电话");
        } else if (!confirm.mobile()) {
            alert(hint[5]);
        } else if (SendFunc.ajaxSendCode($(".fxe_mobile").val()) == 1) {
            fxeTime.timeWait();
            $(".yzm_btn1").css("display", "inline-block");
        }
    })
    //发送验证码
    var SendFunc = {
        ajaxSendCode: function (mobile) {
            try {
                var r = 0;
                $.ajax({
                    type: "POST",
                    data: { action: "Send", mobile: mobile },
                    url: "/sendSmsCode",
                    async: false,
                    success: function (data) {
                        r = data.status;
                    }
                });
            } catch (e) {
                console.log(e.message);
            }
            return r;
        }
    };
    //点击选择
    $(".my_xl_list li").click(function () {
        $(".my_xl_list").hide();
        $(this).parent().prev().text($(this).text()).css("color", "#000");
    })
    //点击显示下拉列表
    $(".my_xl_txt").click(function () {
        $(".my_xl_txt").next().hide();
        $(this).next().show();
    })
    //滑动消失下拉列表
    $(".my_xl_list").mouseover(function () {
        $(".my_xl_list").hover(function () {
            $(this).show()
        }, function () {
            $(this).hide()
        })
    })
    $(".bnzf_ul").mouseleave(function () {
        $(".my_xl_list").hide();
    })
    //核对验证码
    function confirmYzm() {
        var phone = $(".fxe_mobile").val();
        var code = $(".fxe_messageCode").val();
        var t = false;
        $.ajax({
            type: "POST",
            async: false,
            data: { action: "CheckSend", mobile: phone, code: code },
            url: "/Action/SendsmsHelp.ashx",
            success: function (data) {
                if (data == "1") {
                    t= true;
                }
                else {
                    t= false;
                }
            },
            error: function (error) {
                console.log(error);
                t=false;
            }
        });
        return  t;
    }
    //帮你找房提交
    $("#new_submit").click(function () {
        if ($("#new_qy").html() == "请选择") {
            alert("请选择区域");
        } else if ($("#new_yusuan").html() == "请选择") {
            alert("请选择预算");
        } else if ($("#new_huxing").html() == "请选择") {
            alert("请选择户型");
        } else if ($("#phone0").val() == "") {
            alert("请填写电话");
        } else if (!confirm.mobile()) {
            alert(hint[5]);
        } else {
            var tphone = $.trim($("#phone0").val());//电话
            var yusuang = $("#new_yusuan").html();//预算
            var quyu = $("#new_qy").html();//区域
            var jusi = $("#new_huxing").html();//户型
            var miaos = $.trim($("#newms").val());//描述
            var code = $(".fxe_messageCode").val();//验证码
            $.ajax({
                type: "POST",
                url:"/helpSearch",
                data: {
                    type: 1,
                    phone: tphone,
                    budget: yusuang,
                    //sessionId: sessionId,
                    code: code,
                    region: quyu,
                    area: jusi,
                    //memberId: memberId,
                    italy:miaos,
                },
                success: function (data){
                    if (data == "1") {
                        //重置
                        $(".success_tc").show();
                        $("#phone0").val("");
                        $("#new_yusuan").html("请选择");
                        $("#new_qy").html("请选择");
                        $("#new_huxing").html("请选择");
                        $("#newms").val("");
                        $("#yzm0").val("");
                        $("#yzm0").parents("ul").find(".input_desc").show();
                        $("span.yzm_btn").hide();
                        $(".fxe_validateCode").hide();
                        $(".fxe_ReSendValidateCoad").show().html("重新获取");
                        wait = 0;
                        $("#con_bnzfqh_1").find(".errorBox").html("");
                        $("#con_bnzfqh_1").find(".my_xl_input").removeClass("error");
                        $("#con_bnzfqh_1").find(".ljz").hide();
                        $(".my_xl_input").val("");
                        $("#yzm").val("");
                        alert("亲，您的需求已提交成功，工作人员会及时跟您联系，请耐心等待！");
                        return;
                    }else if(data == -1){
                        alert("登录超时，提交失败");
                        return;
                    }else{
                        alert("系统繁忙，请稍后重试");
                        return;
                    }
                }
            });
        }
        //return false;
    })
})