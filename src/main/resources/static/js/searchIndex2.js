/**
 * Created by Administrator on 2017/12/16.
 */
$(function() {
    function changeauto(className,searchType){
        $(className).autocomplete({
            source: function( request, response ) {
                var searchInfo = request.term;
                searchInfo = encodeURI(searchInfo);
                $.ajax({
                    url: "/fuzzySearchSubd?key="+searchType,
                    dataType: "json",
                    data: {
                        q: searchInfo
                    },
                    success: function( data ) {
                        if (data.length != 0) {
                            var count = 0;
                            data.push(data[0]);
                            response($.map(data, function (item) {
                                var highLightTitle = item.title;
                                highLightTitle = highLightTitle.replace(
                                    new RegExp(
                                        "(?![^&;]+;)(?!<[^<>]*)(" +
                                        $.ui.autocomplete.escapeRegex(request.term) +
                                        ")(?![^<>]*>)(?![^&;]+;)", "gi"
                                    ), "<strong>$1</strong>");
                                var txt = $("#searchType").val();
                                switch (txt) {
                                    case "1":
                                        count++;
                                        // return "<i style='float:right'>" + item.Address + " [ 直达 ]</i>" + item.CompanyName;
                                        var tableName = item.tableName;
                                        var projectTypes = item.projectZx;
                                        if(tableName == 'brand' || tableName == 'region' || tableName == 'subwaystation' || tableName == 'subway'){
                                            projectTypes = item.projectZx + "个楼盘";
                                        }else{
                                            if (projectTypes == null || projectTypes == undefined || projectTypes == '') {
                                                projectTypes = "<s class='high_light_span'>新房</s>"
                                                // projectTypes = projectTypes.replace("新房", " <s class='high_light_span'>新房</s>");

                                            }
                                            if (item.projectType == 1) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span02'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span02'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span02'>写字间</s>");
                                            } else if (item.projectType == 2) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span02'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span02'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span02'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span02'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span02'>写字间</s>");
                                            } else if (item.projectType == 3) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span02'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span02'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span02'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span02'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span'>写字间</s>");
                                            }
                                        }
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            label: "<i style='float:right'>" + projectTypes + " </i>" + highLightTitle+"<input type='hidden' class='newhouse'>",
                                            tableId: item.tableId,
                                            projectType: item.projectType,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                    case "2":
                                        count++;
                                        var itemSummary = item.summary;
                                        itemSummary = setContentText(itemSummary, 1);
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            // label: "<i style='float:right'>" + itemSummary + " </i>" + highLightTitle+"<input type='hidden' class='secondhouse'>",
                                            tableId: item.tableId,
                                            summary: item.summary,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                    case "3":
                                        count++;
                                        var itemSummary = item.summary;
                                        itemSummary = setContentText(itemSummary, 0);
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            // label: "<i style='float:right'>" + itemSummary + " </i>" + highLightTitle+"<input type='hidden' class='renthouse'>",
                                            tableId: item.tableId,
                                            summary: item.summary,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;

                                    case "4":
                                        count++;
                                        if (count == data.length) {
                                            break;
                                        }
                                        return {
                                            label: "<i style='float:right'>" + " </i>" + highLightTitle,
                                            tableId: item.tableId,
                                            projectType: item.projectType,
                                            value: item.title,
                                            tableName: item.tableName,
                                        }
                                        break;
                                    case "5":
                                        count++;
                                        var itemSummary = item.summary;
                                        // itemSummary = setContentText(itemSummary, 0);
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            label: highLightTitle,
                                            tableId: item.tableId,
                                            summary: item.summary,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                    case "6":
                                        count++;
                                        var itemSummary = item.summary;
                                        // itemSummary = setContentText(itemSummary, 0);
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            label: highLightTitle,
                                            tableId: item.tableId,
                                            summary: item.summary,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                    default:
                                        count++;
                                        var tableName = item.tableName;
                                        var projectTypes = item.projectZx;
                                        if(tableName == 'brand' || tableName == 'region' || tableName == 'subwaystation' || tableName == 'subway'){
                                            projectTypes = item.projectZx + "个楼盘";
                                        }else{
                                            if (projectTypes == null || projectTypes == undefined || projectTypes == '') {
                                                projectTypes = "<s class='high_light_span'>新房</s>"
                                                // projectTypes = projectTypes.replace("新房", " <s class='high_light_span'>新房</s>");
                                            }
                                            if (item.projectType == 1) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span02'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span02'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span02'>写字间</s>");
                                            } else if (item.projectType == 2) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span02'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span02'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span02'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span02'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span02'>写字间</s>");
                                            } else if (item.projectType == 3) {
                                                projectTypes = projectTypes.replace("普宅", "<s class='high_light_span02'>普宅</s>");
                                                projectTypes = projectTypes.replace("洋房", "<s class='high_light_span02'>洋房</s>");
                                                projectTypes = projectTypes.replace("商铺", "<s class='high_light_span02'>商铺</s>");
                                                projectTypes = projectTypes.replace("别墅", "<s class='high_light_span02'>别墅</s>");
                                                projectTypes = projectTypes.replace("公寓", "<s class='high_light_span'>公寓</s>");
                                                projectTypes = projectTypes.replace("写字间", "<s class='high_light_span'>写字间</s>");
                                            }
                                        }
                                        if (count == data.length && data.length <= 6 ) {
                                            return{
                                                label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >>",
                                                tableId: -1,
                                                value:request.term,
                                            }
                                        }
                                        if (count == data.length && data.length > 6) {
                                            break;
                                        }
                                        return {
                                            label: "<i style='float:right'>" + projectTypes + " </i>" + highLightTitle+"<input type='hidden' class='newhouse'>",
                                            tableId: item.tableId,
                                            projectType: item.projectType,
                                            value: item.title,
                                            tableName: item.tableName,
                                        };
                                        break;
                                }
                            }));
                        }else {
                            var txt = $("#searchType").val();
                            data = [{"txt":txt}];
                            response($.map(data, function (item) {
                                switch (txt) {
                                    case "1":
                                        return {
                                            label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >><input type='hidden' class='newhouse'>",
                                            tableId: -1,
                                            value:request.term,
                                        }
                                        break;
                                    case "2":
                                        return {
                                            label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >><input type='hidden' class='secondhouse'>",
                                            tableId: -1,
                                            value:request.term,
                                        }
                                        break;
                                    case "3":
                                        return {
                                            label: "<i style='float:right' value='-1'></i>没有找到？试试免费的专家服务吧 >><input type='hidden' class='renthouse'>",
                                            tableId: -1,
                                            value:request.term,
                                        }
                                        break;
                                    default:
                                        break;
                                }
                            }))
                        }
                    }
                });
            },
            select: function( event, ui ) {
                // 1029 搜索统计服务
                var sherchInfo = $(this).val();
                var searchSource = "1";
                var searchIndex = $(".search .hover").text();
                var searchType = 0;
                var keyTab = "other";
                var keyTabIndex = ui.item.tableName;
                var keyId = ui.item.tableId;
                switch (searchIndex) {
                    case "买新房":
                        searchType = 1;
                        break;
                    case "买二手房":
                        searchType = 2;
                        break;
                    case "找租房":
                        searchType = 3;
                        break;
                    case "资讯":
                        searchType =4;
                        break;
                }
                switch(keyTabIndex){
                    case'sub':
                        keyTab= "sub";
                        break;
                    case 'brand':
                        keyTab = "brand";
                        break;
                    case "region":
                        keyTab = "region";
                        break;
                    case "subway":
                        keyTab = "line";
                        break;
                    case "subwaystation":
                        keyTab = "station";
                        break;
                    case "video":
                        keyTab = "video";
                        break;
                    case "news":
                        keyTab = "news";
                        break;
                    case "ProjectBase":
                        keyTab = "project";
                        break;
                    case "xinfang1":
                    case "xinfang3":
                        keyTab = "station";
                        break;
                }
                $.ajax({
                    type: "POST",
                    url: "/searchClickAnalyze",
                    data: {
                        "sessionId": sessionId,
                        "searchInfo": sherchInfo,
                        "searchSource": searchSource,
                        "searchType": searchType,
                        "keyId": keyId,
                        "keyTab": keyTab
                    },
                    success: function () {
                        console.log("AnalyzeSuccess...");
                    }
                })

                var openType = $("#searchType").val();
                var itemSummary =   ui.item.summary;
                var tableName = ui.item.tableName;
                switch (openType){
                    case "1":
                        if (ui.item.tableId == -1) {
                            window.open("/helpSearch?ids=1");
                        }else if(tableName == 'brand'){
                            window.open("/brandCompany/"+ui.item.tableId+".htm");
                        }else if(tableName == 'region'){
                            window.open("/houses/r"+ui.item.tableId);
                        }else if(tableName == 'subway'){
                            window.open("/houses/l"+ui.item.tableId + "-k1");
                        }else if(tableName == 'subwaystation'){
                            window.open("/houses/s"+ui.item.tableId + "-l" + ui.item.projectType + "-k1");
                        }else{
                            window.open("/house/"+ui.item.tableId+"-"+ui.item.projectType+".htm");
                        }
                        break;
                    case "2":
                        if (ui.item.tableId == -1) {
                            window.open("/helpSearch?ids=2");
                        }else {
                            if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
                                var Arr   = itemSummary.split(",");
                                if(Arr.length>0){
                                    if(Arr[1]=="二手房0套"){
                                        window.open("/saleHouses/search="+ui.item.value);
                                    }else {
                                        window.open("/saleHouses/-v" + ui.item.tableId);
                                    }
                                }
                            }
                        }
                        break;
                    case "3":
                        if (ui.item.tableId == -1) {
                            window.open("/helpSearch?ids=3");
                        }else {
                            if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
                                var Arr   = itemSummary.split(",");
                                if(Arr.length>0){
                                    if(Arr[0]=="租房0套"){
                                        window.open("/rents/search=" + ui.item.value);
                                    }else {
                                        window.open("/rents/-v" + ui.item.tableId);
                                    }
                                }
                            }
                        }
                        break;
                    case "5":
                        window.open("/shop/"+ui.item.tableId+".htm");
                        break;
                    case "6":
                        window.open("/scriptorium/"+ui.item.tableId+".htm");
                        break;
                    case "4":
                        if(tableName == 'video'){
                            window.open("/video/" + ui.item.tableId + ".htm");
                        }else{
                            window.open("/news/" + ui.item.tableId + ".htm");
                        }
                        break;
                    default:
                        if (ui.item.tableId == -1) {
                            window.open("/helpSearch?ids=1");
                        }else if(tableName == 'brand'){
                            window.open("/brandCompany/"+ui.item.tableId+".htm");
                        }else if(tableName == 'region'){
                            window.open("/houses/r"+ui.item.tableId);
                        }else if(tableName == 'subway'){
                            window.open("/houses/l"+ui.item.tableId + "-k1");
                        }else if(tableName == 'subwaystation'){
                            window.open("/houses/s"+ui.item.tableId + "-l" + ui.item.projectType + "-k1");
                        }else{
                            window.open("/house/"+ui.item.tableId+"-"+ui.item.projectType+".htm");
                        }
                        break;
                }
            },
            open: function() {
                $( this ).removeClass( "ui-corner-all" ).addClass( "ui-corner-top" );
            },
            close: function() {
                $( this ).removeClass( "ui-corner-top" ).addClass( "ui-corner-all" );
            },
        });
    }

    $(".index_search_input").click(function () {
        var ele = $(this)
        setTimeout(function(){
            switch (ele.attr("dir")) {
                case "4":
                    changeauto(".search_btn4", 4);
                    break;
                case "3":
                    changeauto(".search_btn3", 5);
                    break;
                case "2":
                    changeauto(".search_btn2", 5);
                    break;
                case "5":
                    changeauto(".search_btn5", 7);
                    break;
                case "6":
                    changeauto(".search_btn6", 8);
                    break;
                default:
                    changeauto(".search_btn1", 1);
                    break;
            }
        },1000);
    });
    //此方法用于搜索框无下拉时enter事件
    $(".index_search_input").keydown(function (event) {
        if (event.keyCode == 13) {
            var selecetFlag = true;
            $(".ui-autocomplete").each(function () {
                if($(this).css("display") == "block"){
                    var item = $(this);
                    $(item).find("a").each (function () {
                        if($(this).hasClass("ui-state-focus")){
                            selecetFlag = false;
                            return false;
                        }
                    });
                }
            });
            if(selecetFlag){
                var lenn=$("#qdelay2 li.hover").index();
                // if($(".index_search_input").eq(lenn).is(':focus')){
                    $(".index_search_btn").eq(lenn).click();
                // }
            }
        }
    });

    $(".index_search_btn").click(function () {
        var txt = $("#qdelay2>ul .hover").attr("id");
        var txtUrl = $(this).prev().prev().attr("dir");
        var input = $(this).prev().prev().val(); //取输入框值
        if( input == "" || input == undefined ){
            if(txt=="search1"){
                window.open("/houses/")
             }else if (txt=="search2"){
                window.open("/saleHouses/")
            }else if (txt=="search3"){
                window.open("/rents/")
            }else if (txt=="search4"){
                window.open("/news/")
            }else if (txt=="search5"){
                window.open("/shops/")
            }else if (txt=="search6" ){
                window.open("/scriptoriums/")
            }
        }
        input=stripscript(input);
        //下面js用于点击搜索按钮跳转的模糊搜索
        if (input != "") {
            switch (txtUrl) {
                case "1":
                    url = "/houses/search="+input;
                    break;
                case "4":
                    url = "/news/search="+input ;
                    break;
                case "3":  //租房
                    url = "/rents/search="+input ;
                    break;
                case "2"://二手房
                    url = "/saleHouses/search="+input;
                    break;
                case "5"://商铺
                    url = "/shops/search="+input;
                    break;
                case "6"://写字楼
                    url = "/scriptoriums/search="+input;
                    break;
            }
            window.open(url);
        }; //判断结束
    });
});
<!--搜索栏鼠标切换-->
function setTab(name, cursel, n) {
    for (i = 1; i <= n; i++) {
        var menu = document.getElementById(name + i);
        var con = document.getElementById("con_" + name + "_" + i);
        menu.className = i == cursel ? "hover" : "";
        con.style.display = i == cursel ? "block" : "none";
    }
    $("#searchType").val(cursel);
}
<!--根据房源分类二手房显示 约xxx套房源-->
function setContentText( itemSummary,contentType) {
    var countNumber = "";
    var placeName = "";
    if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
        var Arr  = itemSummary.split(",");
        if(Arr.length>0){
            if(contentType == 0){
                placeName = "租房0套";
            }else {
                placeName = "二手房0套";
            }
            if(Arr[contentType] != placeName){
                countNumber=    Arr[contentType].match(/\d+/g);
                countNumber = countNumber[0];
            }else {
                itemSummary =   "";
            }
        }
        if(countNumber !=0 && countNumber!=""){
            itemSummary = "约"+countNumber+"条房源";
        }
        return itemSummary;
    }
}
<!--处理特殊字符-->
function stripscript(s)
{
    var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
    var rs = "";
    for (var i = 0; i < s.length; i++) {
        rs = rs+s.substr(i, 1).replace(pattern, '');
    }
    return rs;
}
<!--帮你找房切换标签-->
function bnzfsetTab(name, cursel, n) {
    for (var i = 1; i <= n; i++) {
        if (i != cursel) {
            $("#con_" + name + "_" + i).hide();
            $("#" + name + i).removeClass("hover");
        } else {
            $("#con_" + name + "_" + i).show();
            $("#" + name + i).addClass("hover");

        }
    }
}
<!--栏目广告分栏切换-->
function setTabHouse(name, cursel, n) {
    for (var i = 1; i <= n; i++) {
        $("#con_" + name + "_" + i).hide();
    }
    $("#con_" + name + "_" + cursel).show();
    $("#" + name + "1").parent().find("li").removeClass("hover");
    $("#" + name + "1").parent().find("a").removeAttr("style");
    $("#" + name + cursel).addClass("hover");

    $("#" + name + (cursel - 1)).find("a").css("border-right", "none");
    if (cursel == n) {
        $("#" + name + cursel).attr("style", "");
    } else {
        $("#" + name + n).attr("style", "background:none");
    }
    if(name=='spxg'){
        if(cursel == '1'){
            $(".spxg1").show()
            $(".spxg2").hide()
        }else{
            $(".spxg1").hide()
            $(".spxg2").show()
        }
    }
}
<!--滑动广告稍大图-->
$(function () {
    // var mySwiper = $('.swiper-container').swiper({
    //     loop: true,
    //     pagination: '.pagination',
    //     paginationClickable: true
    // });
    $(".fcphb ul").each(function () {
        $(this).find("s :gt(2)").css("background-color", "#ccc")
    })
    if ($("#con_mxfqh_1 .swiper-wrapper").find("a").length == 3) {
        $("#con_mxfqh_1 .pagination").hide();
    }
})

// $('#datetimepicker_mask').datetimepicker({
    //    mask: '9999/19/39 29:59'
// });
$(".index_bnzf_samll").click(function () {
    var w = $(window).width();
    $(this).animate({ width: "0px" }, 300)
    setTimeout(function () {
        $(".index_bnzf_samll").hide();
        $(".index_bnzf_big").show().animate({ width: w + "px" })
    }, 300)
})
$(".index_bnzf_close,.index_success_close").click(function () {
    $(".index_bnzf_big,.index_bnzf_success").animate({ width: "0" }, 600)
    setTimeout(function () {
        $(".index_bnzf_big,.index_bnzf_success").hide();
    }, 590)
    setTimeout(function () {
        $(".index_bnzf_samll").show().animate({ width: "150px" }, 300)
    }, 600)
})
$(".input_desc").click(function () {
    $(this).parent().find(".hf_input").focus();
    $(this).hide()
})
$(".hf_input").focus(function () {
    $(this).parent().find(".input_desc").hide();
})
$(".hf_input").blur(function () {
    if ($(this).val() == "") {
        $(this).parent().find(".input_desc").show();
    } else {
        $(this).parent().find(".input_desc").hide();
    }
})

$(document).ready(function () {
    $("#success_tc,.houseqh li").click(function () {
        $(".success_tc").hide();
    })
    $("#myScroll").niceScroll();
    $('.carousel').carousel({
        interval: 10000
    })
    $(".xegj_cj a :even ").addClass("xegj_even")
});
$(".index_t_last").parents("li").css("background", "none")

$(function () {
    if (!isSupportPlaceholder()) {
        $('input').not("input[type='password']").each(
            function () {
                var self = $(this);
                var val = self.attr("placeholder");
                input(self, val);
            }
        );
        $('input[type="password"]').each(
            function () {
                var pwdField = $(this);
                var pwdVal = pwdField.attr('placeholder');
                var pwdId = pwdField.attr('id');
                // 重命名该input的id为原id后跟1
                pwdField.after('<input id="' + pwdId + '1" type="text" value=' + pwdVal + ' autocomplete="new-password"" />');
                var pwdPlaceholder = $('#' + pwdId + '1');
                pwdPlaceholder.show();
                pwdField.hide();
                pwdPlaceholder.focus(function () {
                    pwdPlaceholder.hide();
                    pwdField.show();
                    pwdField.focus();
                });
                pwdField.blur(function () {
                    if (pwdField.val() == '') {
                        pwdPlaceholder.show();
                        pwdField.hide();
                    }
                });
            }
        );
    }
});
// 判断浏览器是否支持placeholder属性
function isSupportPlaceholder() {
    var input = document.createElement('input');
    return 'placeholder' in input;
}
// jQuery替换placeholder的处理
function input(obj, val) {
    var $input = obj;
    var val = val;
    $input.attr({ value: val });
    $input.focus(function () {
        if ($input.val() == val) {
            $(this).attr({ value: "" });
        }
    }).blur(function () {
        if ($input.val() == "") {
            $(this).attr({ value: val });
        }
    });
}
function autoScroll(obj) {
    $(obj).find("ul").animate({
        marginTop: "-60px"
    }, 500, function () {
        $(this).css({ marginTop: "0px" }).find("li:first").appendTo(this);
    })
}
$(function () {
    setInterval('autoScroll(".maquee")', 3000);
})
//设置cookie time按分钟计时
function setCookie1(name,value,time) {
    var exp = new Date();
    exp.setTime(exp.getTime() + time*60*1000);
    value =  encodeURI(value)
    document.cookie = name + "="+ value + ";expires=" + exp.toGMTString();
}