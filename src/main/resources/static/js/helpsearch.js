/**
 * Created by Administrator on 2017/9/11.
 */
$(function () {
    //提交表单
    var reg = new RegExp("^1[0-9]{10}$")
    $(".form_btn a").click(function () {
        if(!$("#checkagree").hasClass("checked")){
            // timeout(".checkimg", "请阅读服务条款");
            alert("请仔细阅读并同意服务协议及隐私政策！");
            return false;
        }
        var url = window.location.href;
        var lys=$("#ly").val();
        // if (url.indexOf("ly=") != "-1") {
        //     lys = "来源：sy站"+url.substring(url.indexOf("ly=") + 3, url.length)+"　"
        // }



        $(".error").css("visibility", "hidden")
        var r = 1
        var type = $(".form_qh a.hover").attr("rel")   //1-新房  2-二手房  3-租房  8-商业  4-看房  5-过户
        var num = $(".form_qh a.hover").attr("dir")
        switch (num) {
            case "0":
            case "1":
            case "2":
            case "3":
                var phone = $(".tel").eq(num).val();
                var code = $(".yzm").eq(num).val();
                var region = $(".qy").eq(num).val();
                var budget = $(".jg").eq(num).val();
                var housetype = $(".lx").eq(num).val();//类型
                var area = $(".hx").eq(num).val()
                var italy = lys+$(".ms").eq(num).val()


                if (phone == "" || !reg.test(phone)) { $(".error_tel").css("visibility", "visible"); r = 0 }
                if (code == "") { $(".error_yzm").css("visibility", "visible"); r = 0 }
                if (region == "") { $(".error_qy").css("visibility", "visible"); r = 0 }
                if (budget == "") { $(".error_jg").css("visibility", "visible"); r = 0 }
                if (area == "") { $(".error_hx").css("visibility", "visible"); r = 0 }
                if (housetype == "" && (num == "2" || num == "3")) { $(".error_lx").css("visibility", "visible"); r = 0 }
                //做新房及二手房的判断
                if(housetype != "" && housetype == '1'){
                    if ($(".cheimg5").eq(num).hasClass("checked") == false){ $(".error_yszc").css("visibility", "visible"); r = 0 }
                }
                if (typeof (housetype) == "undefined") { housetype = "" }
                var params = {type:type, housetype: housetype,
                    phone: phone,
                    budget: budget,
                    code: code,
                    region: region,
                    area: area,
                    italy: italy}
                if (r == 1) {
                    $.ajax({
                        type: "POST",
                        url: "/saveGuide",
                        data:JSON.stringify(params),
                        headers : {
                            'Content-Type' : 'application/json;charset=utf-8'
                        },

                        success: function (data) {
                            if (data.status == 1) {
                                $(".success_bg,.success_tc").show()
                            } else {
                                $(".error_yzm").css("visibility", "visible");
                            }
                        }
                    })
                }
                break;

            case "4":
            case "5":
                var phone = $(".tel").eq(num).val();
                var code = $(".yzm").eq(num).val();
                var region = $(".qy").eq(num).val();
                var buyTime = $(".sj").val()
                var italy = lys + $(".ms").eq(num).val()
                var area = $(".dd").eq(num-4).val();
                var budget = $(".jr").val();
                var mName = $(".mName").val();

                if (phone == "" || !reg.test(phone)) { $(".error_tel").css("visibility", "visible"); r = 0 }
                if (code == "") { $(".error_yzm").css("visibility", "visible"); r = 0 }
                if (region == "") { $(".error_qy").css("visibility", "visible"); r = 0 }
                if (buyTime == "" && num=="4") { $(".error_sj").css("visibility", "visible"); r = 0 }
                if (area == "") { $(".error_dd").css("visibility", "visible"); r = 0 }
                if (budget == "" && num=="5") { $(".error_jr").css("visibility", "visible"); r = 0 }
                if (mName == "" && num=="5") { $(".error_mName").css("visibility", "visible"); r = 0 }
                if ($(".cheimg5").eq(num).hasClass("checked") == false){ $(".error_yszc").css("visibility", "visible"); r = 0 }
                if (typeof (region) == "undefined") { region = "" }
                if (typeof (buyTime) == "undefined") { buyTime = "" }
                if (typeof (italy) == "undefined") { italy = "" }
                if (typeof (area) == "undefined") { area = "" }
                if (typeof (budget) == "undefined") { budget = "" }
                italy   =   italy   +   mName;
                var params = {type: type,
                    phone: phone,
                    code: code,
                    region: region,
                    buyTime: buyTime,
                    area:area,
                    budget:budget,
                    italy: italy}
                if (r == 1) {
                    $.ajax({
                        type: "POST",
                        url: "/saveGuide",
                        data: JSON.stringify(params),
                        headers : {
                            'Content-Type' : 'application/json;charset=utf-8'
                        },
                        success: function (data) {
                            if (data.status == 1) {
                                $(".success_bg,.success_tc").show()
                            } else {
                                $(".error_yzm").css("visibility", "visible");
                            }
                        }
                    })
                }



                break;

        }

    })

    //获取验证
    $(".yzm_btn").click(function () {
        var num = $(".form_qh a.hover").attr("dir")
        var phone = $(".tel").eq(num).val();
        if (phone != "" && reg.test(phone)) {
            $.ajax({
                type: "POST",
                data: { action: "Send", mobile: phone },
                url: "/sendSmsCode",
                async: false,
                success: function (data) {
                    time()
                }
            });
        }
    })
    var wait = 60;
    function time() {
        if (wait == 0) {
            $(".yzm_btn_time").hide();
            $(".yzm_btn").show().html("重新获取");
            wait = 60;
        } else {
            $(".yzm_btn").hide();
            $(".yzm_btn_time").show().html("在" + wait + "秒后重发");
            wait--;
            setTimeout(function () {
                time();
            }, 1000);
        }
    }
})