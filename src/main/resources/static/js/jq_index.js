/**
 * Created by Administrator on 2017/9/6.
 */
$(function () {
    function changeauto(classname, method) {
        $(classname).autocomplete('/fuzzySearchSubd?key='+method, {
            multiple: false,
            scrollHeight: 300,    // 下拉框的高度， Default: 180
            max: 10,
            width:760,
            parse: function (data) {
                //alert(data);
                if(data!=null && data != "") {
                    return $.map(data, function (row) {
                        return {
                            data: row,
                            value: row.title,
                            result: row.title
                        };
                    });

                }else {
                    return data;
                }
            },
            formatItem: function (item) {
                var txt = $("#searchType").val();
                switch (txt) {

                    case "1":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            // return "<i style='float:right'>" + item.Address + " [ 直达 ]</i>" + item.CompanyName;
                            var projectTypes = item.projectZx;
                            if( projectTypes == null || projectTypes == undefined || projectTypes == ''){
                                projectTypes = "新房";
                            }
                            if( item.projectType ==1){
                                projectTypes = projectTypes.replace("普宅","<s class='high_light_span'>普宅</s>");
                                projectTypes = projectTypes.replace("洋房","<s class='high_light_span'>洋房</s>");
                                projectTypes = projectTypes.replace("商铺","<s class='high_light_span'>商铺</s>");
                            }else  if(item.projectType ==2){
                                projectTypes = projectTypes.replace("别墅","<s class='high_light_span'>别墅</s>");;
                            }else if(item.projectType ==3){
                                projectTypes =  projectTypes.replace("公寓","<s class='high_light_span'>公寓</s>");
                                projectTypes =  projectTypes.replace("写字间","<s class='high_light_span'>写字间</s>");
                            }
                            return "<i style='float:right'>" + projectTypes + " </i>" + item.title;
                        }
                        break;
                    case "2":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            var itemSummary =   item.summary;
                            itemSummary =    setContentText(itemSummary,1);
                            return "<i style='float:right'>" + itemSummary +" </i>" + item.title;
                        }
                        break;
                    case "3":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else{
                            var itemSummary =   item.summary;
                            itemSummary =   setContentText(itemSummary,0);
                            return "<i style='float:right'>" + itemSummary +" </i>" + item.title;
                        }
                        break;

                    case "4":
                        if (item.ID == 0) {
                               return "<i style='float:left '>未找到关键字请重新输入条件！</i>";
                        } else {
                            return "<i style='float:right'>" + " </i>"+item.title  ;
                        }
                        break;

                    default:
                        if (item.ID == 0) {
                                return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            var projectTypes = item.projectZx;
                            if( projectTypes == null || projectTypes == undefined || projectTypes == ''){
                                projectTypes = "新房";
                            }
                            if( item.projectType ==1){
                                projectTypes = projectTypes.replace("普宅","<s class='high_light_span'>普宅</s>");
                                projectTypes = projectTypes.replace("洋房","<s class='high_light_span'>洋房</s>");
                                projectTypes = projectTypes.replace("商铺","<s class='high_light_span'>商铺</s>");
                            }else  if(item.projectType ==2){
                                projectTypes = projectTypes.replace("别墅","<s class='high_light_span'>别墅</s>");;
                            }else if(item.projectType ==3){
                                projectTypes =  projectTypes.replace("公寓","<s class='high_light_span'>公寓</s>");
                                projectTypes =  projectTypes.replace("写字间","<s class='high_light_span'>写字间</s>");
                            }
                            return "<i style='float:right'>" + projectTypes + " </i>" + item.title;
                        }
                        break;
                }
            }
        }).result(function (e, item) {
            if(item.category == "新房"){
                $("#chid").val("1");
                // window.location.href = "/house/" + item.tableId+"-"+item.projectType+".htm";
                window.open("/house/" + item.tableId+"-"+item.projectType+".htm");
                window.location.reload() ;
            }else if(item.category == "小区"){
                    $("#chid").val("5");
                    var searchIndex=$("#qdelay2 li.hover").index();
                    if(searchIndex == 1){
                        var itemSummary =   item.summary;
                        if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
                            var Arr   = itemSummary.split(",");
                            if(Arr.length>0){
                                if(Arr[1]=="二手房0套"){
                                    // window.location.href = "/saleHouses/search="+item.title;
                                    window.open("/saleHouses/search="+item.title);
                                    window.location.reload() ;
                                }else {
                                    window.open("/saleHouses/-v" + item.tableId);
                                    window.location.reload() ;
                                    // window.location.href = "/saleHouses/-v" + item.tableId;
                                }
                            }
                        }
                    }else if(searchIndex == 2){
                        var itemSummary =   item.summary;
                        if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
                            var Arr   = itemSummary.split(",");
                            if(Arr.length>0){
                                if(Arr[0]=="租房0套"){
                                    window.open("/rents/search=" + item.title);
                                    window.location.reload() ;
                                        // window.location.href = "/rents/search="+item.title;
                                }else {
                                        // window.location.href = "/rents/-v" + item.tableId;
                                        window.open("/rents/-v" + item.tableId);
                                    window.location.reload() ;
                                }
                            }
                        }
                    }else {
                            window.open("/saleHouses/-v" + item.tableId);
                            // window.location.href = "/saleHouses/-v" + item.tableId;
                        window.location.reload() ;
                    }
            }else if(item.category == "资讯"){
                $("#chid").val("4");
                // window.location.href = "/news/" + item.tableId+".htm";
                window.open("/news/" + item.tableId + ".htm");
                window.location.reload() ;
            } else {
                $(".index_search_input").val(item.title);
                $("#chid").val("1");
            }
        });
    };

    // $(".index_search_input").bind("click", function () {
        $(".index_search_input").click(function () {
            var ele = $(this)
            setTimeout(function(){
                switch (ele.attr("dir")) {
                    case "4":
                        changeauto(".search_btn4", 4);
                        break;
                    case "3":
                        changeauto(".search_btn3", 5);
                        break;
                    case "2":
                        changeauto(".search_btn2", 5);
                        break;
                    default:
                        changeauto(".search_btn1", 1);
                        break;
                }
            },1000);
        });

    // });

    $(".index_search_btn").click(function () {
        var txt = $(this).prev().attr("dir");
        input = $(this).prev().val(); //取输入框值
        if(txt=="1" && input==""){
            window.open("/houses/")
        }else if (txt=="2" && input==""){
            window.open("/saleHouses/")
        }else if (txt=="3" && input==""){
            window.open("/rents/")
        }else if (txt=="4" && input==""){
            window.open("/news/")
        }
        input=stripscript(input);
        //下面js用于点击搜索按钮跳转的模糊搜索
        if (input != "") {
            switch (txt) {
                case "1":
                    url = "/houses/search="+input;
                    break;
                case "4":
                    url = "/news/search="+input ;
                    break;

                case "3":  //租房
                    url = "/rents/search="+input ;
                    break;
                case "2"://二手房
                    url = "/saleHouses/search="+input;
                    break;
            }

            // if(txt=="1"){
            //     for (var i=0;i<keywords.length;i++){
            //         for(var t=1;t<keywords[i].length;t++){
            //             if(input==keywords[i][t]){
            //                 url=keywords[i][0]
            //             }
            //         }
            //     }
            // }else if(txt=="2") {
            //     for (var i = 0; i <sale.length; i++) {
            //         for (var t = 1; t <sale[i].length; t++) {
            //             if (input == sale[i][t]) {
            //                 url = sale[i][0]
            //             }
            //         }
            //     }
            // }else if(txt=="3") {
            //     for (var i = 0; i <rent.length; i++) {
            //         for (var t = 1; t <rent[i].length; t++) {
            //             if (input == rent[i][t]) {
            //                 url = rent[i][0]
            //             }
            //         }
            //     }
            // }

            window.open(url);
        }; //判断结束
        window.location.reload() ;
    });
    //此方法用于搜索框无下拉时enter事件
    $(".index_search_input").keydown(function (event) {
        if (event.keyCode == 13) {
            var selecetFlag = true;
            $(".ac_results").each(function () {
                if($(this).css("display") == "block"){
                    var item = $(this);
                    $(item).find("li").each (function () {
                        if($(this).hasClass("ac_over")){
                            selecetFlag = false;
                            return false;
                        }
                    });
                }
            });
            if(selecetFlag){
                var lenn=$("#qdelay2 li.hover").index();
                if($(".index_search_input").eq(lenn).is(':focus')){
                   $(".index_search_btn").eq(lenn).click();
                }
            }
        }
    });
})

function stripscript(s)
{
    var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
    var rs = "";
    for (var i = 0; i < s.length; i++) {
        rs = rs+s.substr(i, 1).replace(pattern, '');
    }
    return rs;
}


function bnzfsetTab(name, cursel, n) {
    for (var i = 1; i <= n; i++) {
        if (i != cursel) {
            $("#con_" + name + "_" + i).hide();
            $("#" + name + i).removeClass("hover");
        } else {
            $("#con_" + name + "_" + i).show();
            $("#" + name + i).addClass("hover");

        }
    }
}

$('#datetimepicker_mask').datetimepicker({
    //    mask: '9999/19/39 29:59'
});
$(".index_bnzf_samll").click(function () {
    var w = $(window).width();
    $(this).animate({ width: "0px" }, 300)
    setTimeout(function () {
        $(".index_bnzf_samll").hide();
        $(".index_bnzf_big").show().animate({ width: w + "px" })
    }, 300)
})
$(".index_bnzf_close,.index_success_close").click(function () {
    $(".index_bnzf_big,.index_bnzf_success").animate({ width: "0" }, 600)
    setTimeout(function () {
        $(".index_bnzf_big,.index_bnzf_success").hide();
    }, 590)
    setTimeout(function () {
        $(".index_bnzf_samll").show().animate({ width: "150px" }, 300)
    }, 600)
})
$(".input_desc").click(function () {
    $(this).parent().find(".hf_input").focus();
    $(this).hide()
})
$(".hf_input").focus(function () {
    $(this).parent().find(".input_desc").hide();
})
$(".hf_input").blur(function () {
    if ($(this).val() == "") {
        $(this).parent().find(".input_desc").show();
    } else {
        $(this).parent().find(".input_desc").hide();
    }
})

$(document).ready(function () {
    $("#success_tc,.houseqh li").click(function () {
        $(".success_tc").hide();
    })
    $("#myScroll").niceScroll();
    $('.carousel').carousel({
        interval: 10000
    })

    $(".xegj_cj a :even ").addClass("xegj_even")
});
$(".index_t_last").parents("li").css("background", "none")
function setTabHouse(name, cursel, n) {
    for (var i = 1; i <= n; i++) {
        $("#con_" + name + "_" + i).hide();
    }
    $("#con_" + name + "_" + cursel).show();
    $("#" + name + "1").parent().find("li").removeClass("hover");
    $("#" + name + "1").parent().find("a").removeAttr("style");
    $("#" + name + cursel).addClass("hover");

    $("#" + name + (cursel - 1)).find("a").css("border-right", "none");
    if (cursel == n) {
        $("#" + name + cursel).attr("style", "");
    } else {
        $("#" + name + n).attr("style", "background:none");
    }
}
$(function () {
    var mySwiper = $('.swiper-container').swiper({
        loop: true,
        pagination: '.pagination',
        paginationClickable: true
    });
    $(".fcphb ul").each(function () {
        $(this).find("s :gt(2)").css("background-color", "#ccc")
    })
    if ($("#con_mxfqh_1 .swiper-wrapper").find("a").length == 3) {
        $("#con_mxfqh_1 .pagination").hide();
    }
})
$(function () {
    if (!isSupportPlaceholder()) {
        $('input').not("input[type='password']").each(
            function () {
                var self = $(this);
                var val = self.attr("placeholder");
                input(self, val);
            }
        );
        $('input[type="password"]').each(
            function () {
                var pwdField = $(this);
                var pwdVal = pwdField.attr('placeholder');
                var pwdId = pwdField.attr('id');
                // 重命名该input的id为原id后跟1
                pwdField.after('<input id="' + pwdId + '1" type="text" value=' + pwdVal + ' autocomplete="new-password"" />');
                var pwdPlaceholder = $('#' + pwdId + '1');
                pwdPlaceholder.show();
                pwdField.hide();
                pwdPlaceholder.focus(function () {
                    pwdPlaceholder.hide();
                    pwdField.show();
                    pwdField.focus();
                });
                pwdField.blur(function () {
                    if (pwdField.val() == '') {
                        pwdPlaceholder.show();
                        pwdField.hide();
                    }
                });
            }
        );
    }
});
// 判断浏览器是否支持placeholder属性
function isSupportPlaceholder() {
    var input = document.createElement('input');
    return 'placeholder' in input;
}
// jQuery替换placeholder的处理
function input(obj, val) {
    var $input = obj;
    var val = val;
    $input.attr({ value: val });
    $input.focus(function () {
        if ($input.val() == val) {
            $(this).attr({ value: "" });
        }
    }).blur(function () {
        if ($input.val() == "") {
            $(this).attr({ value: val });
        }
    });
}

function autoScroll(obj) {
    $(obj).find("ul").animate({
        marginTop: "-60px"
    }, 500, function () {
        $(this).css({ marginTop: "0px" }).find("li:first").appendTo(this);
    })
}
$(function () {
    setInterval('autoScroll(".maquee")', 3000);
})
//设置cookie time按分钟计时
function setCookie1(name,value,time) {
    var exp = new Date();
    exp.setTime(exp.getTime() + time*60*1000);
    value =  encodeURI(value)
    document.cookie = name + "="+ value + ";expires=" + exp.toGMTString();
}
function setTab(name, cursel, n) {
    for (i = 1; i <= n; i++) {
        var menu = document.getElementById(name + i);
        var con = document.getElementById("con_" + name + "_" + i);
        menu.className = i == cursel ? "hover" : "";
        con.style.display = i == cursel ? "block" : "none";
    }
    $("#searchType").val(cursel);
}
function setContentText( itemSummary,contentType) {
    var countNumber = "";
    var placeName = "";
    if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
        var Arr  = itemSummary.split(",");
        if(Arr.length>0){
            if(contentType == 0){
                placeName = "租房0套";
            }else {
                placeName = "二手房0套";
            }
            if(Arr[contentType] != placeName){
                countNumber=    Arr[contentType].match(/\d+/g);
                countNumber = countNumber[0];
            }else {
                itemSummary =   "";
            }
        }
        if(countNumber !=0 && countNumber!=""){
            itemSummary = "约"+countNumber+"条房源";
        }
        return itemSummary;
    }
}
