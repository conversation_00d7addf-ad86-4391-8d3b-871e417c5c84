/**
 * Created by Administrator on 2017/9/4.
 */
function fangxiaoer() { };
fangxiaoer.ajax = function (method, para, func, baseurl) {
    if (baseurl  ==null) {
        baseurl = "";
    }
    para = JSON.stringify(para);
    $.ajax({

        type: "POST",

        cache: false,

        async: false,

        url: baseurl + "/saveGuide",

        data: para,
        dataType : 'json',
        headers : {
            'Content-Type' : 'application/json;charset=utf-8'
        },

        success: function (data) {

            func(data);
        }
    });
};
