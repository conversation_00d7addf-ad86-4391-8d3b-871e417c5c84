/**
 * Created by Administrator on 2017/9/12.
 */
/**
 * Created by zz on 2017/7/4.
 * 头文件搜索功能
 */
$(function () {
    function changeauto(method) {
        $(".ac_input").autocomplete('/fuzzySearchSubd?key=' + method , {
            multiple: false,
            scrollHeight: 300,    // 下拉框的高度， Default: 180
            max: 20,
            width:483,
            parse: function (data) {
                //alert(data);
                if(data!=null && data != "") {
                    return $.map(data, function (row) {
                        return {
                            data: row,
                            value: row.title,
                            result: row.title
                        };
                    });
                }
            },
            formatItem: function (item) {
                var txt = $(".ac_input").attr("name");
                switch (txt) {
                    case "1":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            // return "<i style='float:right'>" + item.Address + " [ 直达 ]</i>" + item.CompanyName;
                            var projectTypes = item.projectZx;
                            if( projectTypes == null || projectTypes == undefined || projectTypes == ''){
                                projectTypes = "新房";
                            }
                            if( item.projectType ==1){
                                projectTypes = projectTypes.replace("普宅","<s class='high_light_span'>普宅</s>");
                                projectTypes = projectTypes.replace("洋房","<s class='high_light_span'>洋房</s>");
                                projectTypes = projectTypes.replace("商铺","<s class='high_light_span'>商铺</s>");
                            }else  if(item.projectType ==2){
                                projectTypes = projectTypes.replace("别墅","<s class='high_light_span'>别墅</s>");;
                            }else if(item.projectType ==3){
                                projectTypes =  projectTypes.replace("公寓","<s class='high_light_span'>公寓</s>");
                                projectTypes =  projectTypes.replace("写字间","<s class='high_light_span'>写字间</s>");
                            }
                            return "<i style='float:right'>" + projectTypes + " </i>" + item.title;
                        }
                        break;
                    case "2":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            var itemSummary =   item.summary;
                            itemSummary =    setContentText(itemSummary,1);
                            return "<i style='float:right'>" + itemSummary +" </i>" + item.title;
                        }
                        break;
                    case "7":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            return "<i style='float:right'>" + item.category + " </i>" + item.title;
                        }
                        break;
                    case "8":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            return "<i style='float:right'>" + item.category + " </i>" + item.title;
                        }
                        break;
                    case "3":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            var itemSummary =   item.summary;
                            itemSummary =       setContentText(itemSummary,0);
                            return "<i style='float:right'>" + itemSummary +" </i>" + item.title;
                        }
                        break;
                    case "5":
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到关键字请重新输入条件！</i>";
                        } else {
                            return "<i style='float:right'></i>" + item.title;
                        }
                        break;
                    default:
                        if (item.ID == 0) {
                            return "<i style='float:left '>未找到项目请重新输入条件！</i>";
                        } else {
                            // return "<i style='float:right'>" + item.Address + " [ 直达 ]</i>" + item.CompanyName;
                            var projectTypes = item.projectZx;
                            if( projectTypes == null || projectTypes == undefined || projectTypes == ''){
                                projectTypes = "新房";
                            }
                            if( item.projectType ==1){
                                projectTypes = projectTypes.replace("普宅","<s class='high_light_span'>普宅</s>");
                                projectTypes = projectTypes.replace("洋房","<s class='high_light_span'>洋房</s>");
                                projectTypes = projectTypes.replace("商铺","<s class='high_light_span'>商铺</s>");
                            }else  if(item.projectType ==2){
                                projectTypes = projectTypes.replace("别墅","<s class='high_light_span'>别墅</s>");;
                            }else if(item.projectType ==3){
                                projectTypes =  projectTypes.replace("公寓","<s class='high_light_span'>公寓</s>");
                                projectTypes =  projectTypes.replace("写字间","<s class='high_light_span'>写字间</s>");
                            }
                            return "<i style='float:right'>" + projectTypes + " </i>" + item.title;
                        }
                        break;
                }
            }
        }).result(function (e, item) {
            if(item.category == "新房"){
                $("#chid").val("1");
                window.open("/house/" + item.tableId+"-"+item.projectType+".htm");
                window.location.reload() ;
            }else if(item.category == "小区"){
                $("#chid").val("5");
                if($("#txtkeys").attr("name") == 2){
                    var itemSummary =   item.summary;
                    if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
                        var Arr   = itemSummary.split(",");
                        if(Arr.length>0){
                            if(Arr[1]=="二手房0套"){
                                window.open("/saleHouses/search="+item.title);
                                window.location.reload() ;
                                // window.location.href = "/saleHouses/search="+item.title;
                            }else {
                                window.open("/saleHouses/-v" + item.tableId);
                                window.location.reload() ;
                                // window.location.href = "/saleHouses/-v" + item.tableId;
                            }
                        }
                    }
                }else if($("#txtkeys").attr("name") == 3){
                    var itemSummary =   item.summary;
                    if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
                        var Arr   = itemSummary.split(",");
                        if(Arr.length>0){
                            if(Arr[0]=="租房0套"){
                                window.open("/rents/search="+item.title);
                                window.location.reload() ;
                                // window.location.href = "/rents/search="+item.title;
                            }else {
                                window.open("/rents/-v" + item.tableId);
                                window.location.reload() ;
                                // window.location.href = "/rents/-v" + item.tableId;
                            }
                        }
                    }
                }else {
                    window.open("/village/" + item.tableId+".htm");
                    window.location.reload() ;
                    // window.location.href = "/village/" + item.tableId+".htm";
                }
            }else if(item.category == "资讯"){
                $("#chid").val("4");
                window.open("/news/" + item.tableId+".htm");
                window.location.reload() ;
                // window.location.href = "/news/" + item.tableId+".htm";
            } else {
                $(".index_search_input").val(item.title);
                $("#chid").val("1");
            }

        });
    };

    $(".ac_input").click(function () {
        var txt = $(".ac_input").attr("name");
        setTimeout(function() {
            switch (txt) {
                case "1":
                    changeauto(1);
                    break;
                case "5":
                    changeauto(4);
                    break;
                case "4":
                    break;
                case "2":
                    changeauto(5);
                    break;
                case "7":
                case "8":
                case "3":
                    changeauto(5);
                    break;
                default:
                    changeauto(1);
                    break;
            }
        },1000);
    });

    $(".search_btn").click(function () {
        var txt = $(".ac_input").attr("name")
        input = $(this).prev().val() //取输入框值
        input=stripscript(input);
        var search ="" ;
        if(txt ==1){
            if (input == null || input == undefined || input == '') {
                var AdProjectName =   $("#txtkeys").attr("placeholder");
                // AdProjectName = AdProjectName.replace(/[：]{1}[\u4e00-\u9fa5]+$/,"");
                regProject = AdProjectName.match(/[：]{1}[\u4e00-\u9fa5]+$/);
                AdProjectName = regProject[0];
                AdProjectName = AdProjectName.replace(/^[：]{1}/,"");
                search = "search="+AdProjectName;
            }else {
                search = "search="+input;
            }
        }else {
            if (input == null || input == undefined || input == '') {
                search = "";
            }else {
                search = "search="+input;
            }
        }

            switch (txt) {
                case "1":
                    url = "/houses/"+search;
                    break;
                case "5":
                    url = "/news/"+search;
                    break;

                case "4":  //商铺
                    url = "/shops/"+search;
                    break;
                case "3":  //租房
                    url = "/rents/"+search;
                    break;
                case "2"://二手房
                    url = "/saleHouses/"+search;
                    break;
                case "7"://二手房小区
                    url = "/saleVillages/"+search;
                    break;
                case "8"://租房小区
                    url = "/villages/"+search;
                    break;
                default :
                    url = "/houses/" + search;
                    break;
            }
            window.location.href = url;
        // }; //判断结束
    });

    function stripscript(s)
    {
        var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")
        var rs = "";
        for (var i = 0; i < s.length; i++) {
            rs = rs+s.substr(i, 1).replace(pattern, '');
        }
        return rs;
    }

    var keys = '';
    if (keys != "" && keys != "null" && keys != null && keys != "0") {
        if (keys != -1) {
            $(".ac_input").val(keys);
        }
    }
    $(".ac_input").each(function () {
        var thisVal = $(".ac_input").val();    //判断文本框的值是否为空，有值的情况就隐藏提示语，没有值就显示
        if (thisVal != "") { $(".ac_input").siblings("span").hide(); }
        else { $(".ac_input").siblings("span").show(); }
        $(".ac_input").keyup(function () {
            $(".ac_input").siblings("span").hide();
        }).blur(function () {
            var val = $(this).val();
            if (val != "") {
                $(this).siblings("span").hide();
            } else {
                $(this).siblings("span").show();
            }
        });
    });
    //此方法用于搜索框无下拉时enter事件
    $(".ac_input").keydown(function (event) {
        if (event.keyCode == 13) {
            var selecetFlag = true;
            $(".ac_results").each(function () {
                if($(this).css("display") == "block"){
                    var item = $(this);
                    $(item).find("li").each (function () {
                        if($(this).hasClass("ac_over")){
                            selecetFlag = false;
                            return false;
                        }
                    });
                }
            });
            if(selecetFlag){
                $(".search_btn").click();
            }
        }
    });
    $("#deleteButton").click(function () {
        // $("#search2017 .search .ac_input").val("");
        // $("#deleteButton").hide();
        var redirectUrl =location.pathname;
        if( redirectUrl != null || redirectUrl != undefined || redirectUrl != ''){
            if( redirectUrl.indexOf("search") != -1 ){
                redirectUrl =   redirectUrl.substr(0,redirectUrl.indexOf("search"));
                window.location.href = redirectUrl;
            }else {
                // window.location.reload();
                $("#search2017 .search .ac_input").val("");
                $("#deleteButton").hide();
            }
        }
    });
    $("#search2017 .search .ac_input").val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
    $("#search2017 .search .ac_input").keyup(function(){
        $(this).val()!=""?$("#deleteButton").show():$("#deleteButton").hide()
    })
});
//设置cookie time按分钟计时
function setCookie(name,value,time) {
    var exp = new Date();
    exp.setTime(exp.getTime() + time*60*1000);
    value =  encodeURI(value)
    document.cookie = name + "="+ value + ";expires=" + exp.toGMTString();
}
function delCookie(name)
{
    var exp = new Date();
    exp.setTime(exp.getTime() - 20000);
    var cval=getCookie(name);
    if(cval!=null)
        document.cookie= name + "="+cval+";expires="+exp.toGMTString();
}
function getCookie(name) {
    var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
    if(arr=document.cookie.match(reg)){
        return unescape(arr[2]);}
    else {
        return null;
    }
}
function setContentText( itemSummary,contentType) {
    var countNumber = "";
    var placeName = "";
    if(itemSummary != null && itemSummary != undefined && itemSummary != ''){
        var Arr   = itemSummary.split(",");
        if(Arr.length>0){
            if(contentType == 0){
                placeName = "租房0套";
            }else {
                placeName = "二手房0套";
            }
            if(Arr[contentType]!=placeName){
                countNumber=    Arr[contentType].match(/\d+/g);
                countNumber = countNumber[0];
            }else {
                itemSummary =   "";
            }
        }
        if(countNumber !=0 && countNumber!=""){
            itemSummary = "约"+countNumber+"条房源";
        }
        return itemSummary;
    }
}