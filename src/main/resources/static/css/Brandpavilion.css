.pinpai{
    /* background-color:#fd4032; */
    line-height:38px;
    display:inline-block;
    width: 360px;
    color: #333333 !important;
    /* box-shadow: 1.5px 2.598px 5px 0px rgb( 255, 30, 28 ); */
    border-radius:0 19px 19px 0;
    /* background-image: url(https://static.fangxiaoer.com/web/images/Housekeeper/more.png); */
    background-repeat: no-repeat;
    background-position: 246px center;
    margin-top: -55px;
    margin-bottom: 22px;
}
.pinpai label {
    padding-left: 15px;
    display: inline-block;
    padding-right: 13px;
    border-right: 1px solid #fff;
    line-height: 12px;
    margin-right: 14px;

}
.pinpai a{
    color: #333333;
    text-decoration: none;
}

.barnd_icon {
    display: inline-block;
    width: 26px;
    height: 24px;
    margin-bottom: -6px;
    margin-left: 6px;
    margin-right: 10px;
    background-image: url(https://static.fangxiaoer.com/web/images/brand/brand.svg);
}
.rank_icon {
    display: inline-block;
    width: 26px;
    height: 24px;
    margin-right: 10px;
    margin-bottom: -6px;
    background-image: url(https://static.fangxiaoer.com/web/images/brand/rank.svg);
}
.pinpai1{
    margin-right: 24px;
}