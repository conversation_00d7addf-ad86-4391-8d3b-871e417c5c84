@charset "utf-8";
.main1 {
	display: flex;
	justify-content: center;

	align-items: center;
	background-color: #FFFFFF;
}

/*上面a标签*/
.crumbs {
	color: #999;
	font-size: 12px;
	margin: 5px auto 10px !important;
	width: 1170px;
}

.main2 {
	display: flex;
	justify-content: center;


	background-color: #FFFFFF;
}

.row1 {
	width: 1170px;

}

.row2 {
	width: 1170px;

}



.yun {
	height: 270px;
}



/* 新选项卡 */
.tab {
	display: flex;
	justify-content: start;
	margin-top: 26px;
	border-bottom: 1px solid #ff6100;
}

.tab div {
	width: 100px;
	height: 48px;

	color: #333333;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 18px;

}
/* .tab .first {

	color: #FFFFFF;
	background-color: #FF6100;
}
 */
.current{
	color: #FFFFFF !important;
	background-color: #FF6100;
}
.tab div:hover {
	color: #FF6100;


	cursor: pointer;
}

/* .tab .first:hover {

	color: #FFFFFF;
	background-color: #FF6100;
}
 */
/* 经纪人模块盒子 */
.p-content {
	display: flex;
	flex-wrap: wrap;

	justify-content: flex-start;
	margin-top: 2px;

}
a{
	text-decoration: none !important;
}
.p-box {
	margin-top: 30px;
	width: 376px;

	height: 200px;
	margin-right: 10px;
	-webkit-box-shadow: 0 6px 6px 0 rgba(39, 32, 27, .15);
	box-shadow: 0 2px 6px 0 rgba(39, 32, 27, .15);
}

.noAll{
	width: 100%;
	text-align: center !important;
	margin-top: 50px;
	display: none;
}
.noAll p {

	font-size: 16px;
	color: #666666;

	cursor: pointer;
}

/* 经纪人卡片 */
.card-box {
	display: flex;
	padding: 9px;

}
.card-box .image-box{
	width: 128px !important;
	height: 180px !important;
	overflow: hidden !important;
}
.card-box .card-image{
	width: 128px !important;
	height: 180px !important;
	border-radius: 2px;
	object-fit: cover;
}
.el-card__body {
	padding: 6px;

}

.box-right {
	display: flex;
	flex-direction: column;
	margin-left: 10px;


}

.people {
	width: 128px;
	height: 180px;
}

.p-area {
	font-size: 14px;
	display: flex;
	color: #808080;
	margin-top: 3px;
}
.p-area p{
	white-space: nowrap;
	overflow: hidden;

}
.area-right .p2 {
	margin-top: -6px;
}

.area-right .p3 {
	margin-top: -6px;
}

.p-title {
	font-size: 21px;
	font-weight: bold;
	color: #333333;
}


.f-title {

	font-size: 14px;
	color: #4c4c4c;
}

.num {
	color: #ff6100;
}

.dian {
	margin-top: 9px;
}

.d-title {
	color: #808080;
	font-size: 14px;

}

.f-title {
	color: #666666;
}



.p-contact {
	display: flex;
	margin-top: auto;
}

.chat {
	display: flex;
	justify-content: space-around;
	align-items: center;
	background-color: #32a3f2;
	width: 102px;

	height: 32px;
	border-radius: 3px;
	cursor: pointer;
}

.chat-img {

	width: 16px;
	height: 16px;
}

.chat-text {
	font-size: 14px;
	color: #FFFFFF;
	margin-left: -20px;
}

.phone {
	display: flex;
	justify-content: space-around;
	align-items: center;
	background-color: #ff6100;
	width: 102px;
	margin-left: 16px;
	height: 32px;
	border-radius: 3px;
	cursor: pointer;
}

.phone-img {
	width: 13px;
	height: 16px;
}

.phone-text {
	font-size: 14px;
	color: #FFFFFF;
	margin-left: -20px;
}

.all {
	width: 100%;
	display: flex;
	justify-content: center;
	text-align: center;
	margin-top: 50px;
}

.all p {
	width: 142px;
	height: 40px;
	font-size: 14px;

	color: #FF6100;
	border-radius: 5px;
	border: 1px solid #FF6100;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}
