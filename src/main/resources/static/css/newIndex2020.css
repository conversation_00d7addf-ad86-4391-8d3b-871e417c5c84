/*index_2018*/
@charset "utf-8";

body {
    color: #333;
    font-size: 14px;
}

body>div:nth-child(2) {
    height: 5550px !important;
}

big {
    font-size: inherit;
    font-weight: inherit;
}

.pull-left {
    float: left;
}

.pull-right {
    float: right;
}

a:hover {
    text-decoration: none;
}

.index_block {
    width: 1170px;
    margin: 0px auto 20px;
    margin-top: 0 !important;
}

.flash {
    width: 1170px;
    margin: 30px auto 10px;
}

sub {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 10;
}

sub img {
    width: auto !important;
    height: auto !important
}

sup {
    position: absolute;
    bottom: 0px;
    left: 0px;
    z-index: 10;
}

sup img {
    width: auto !important;
    height: auto !important
}

.ac_results {
    border: none;
    width: 760px !important;
}

.ac_results li {
    padding: 0px 5px;
    line-height: 30px;
}

.ac_odd {
    background: #fff;
}

.ac_over {
    background: #ededed;
    color: #333;
}

.sy_index {
    height: 60px;
}

.sy_index .index_block {
    margin: 0 auto
}

.sy_index li {
    float: left;
    font-size: 14px;
    line-height: 60px;
    padding-right: 30px;
}

.sy_index .logo {
    margin-right: 28px;
    margin-top: 16px;
}

.sy_index li a {
    color: #333;
}

.sy_index li a.hover {
    color: #ff5200
}

.sy_index p {
    float: right;
    font-size: 12px;
    line-height: 60px;
    color: #333;
}

.sy_index p a {
    margin: 0 7px;
    color: #333;
}

.sy_index p a:hover {
    color: #ff5200;
}

.sy_index span {
    position: relative;
    margin-right: 45px;
    height: 14px;
    line-height: 14px;
    padding-right: 25px;
    margin: 11px 3px 0 0;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/head_bottom.gif) no-repeat 55px 5px;
}

.sy_index s {
    display: none;
    position: absolute;
    padding: 10px;
    background: #fff;
    border: 1px solid #dfdfdf;
    top: 33px;
    z-index: 9999999;
}

.sy_index span:hover s {
    display: block
}

.sy_index span:hover {
    color: #ff5200;
    cursor: pointer;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/head_top.gif) no-repeat 55px 5px;
}

.sy_index i {
    position: absolute;
    top: -61px;
    right: 53px;
    width: 88px;
    padding-top: 50px;
    text-align: center
}

.head_app {
    float: left;
    margin-right: 10px;
}

.search {
    width: 820px;
    height: 103px;
    position: absolute;
    z-index: 10;
    left: 50%;
    top: 265px;
    padding-top: 15px;
    margin-left: -410px;
}

.search ul {
    width: 821px;
    margin: 0 auto;
    display: block;
}

.search li {
    float: left;
    font-size: 16px;
    margin: 0 30px 0 8px;
    line-height: 45px;
    height: 45px;
    color: #fff;
}

.search li.hover {
    background: url(https://static.fangxiaoer.com/global/imgs/index/ico_search.png) bottom no-repeat
}

.search li a {
    color: #ccc;
    font-weight: bold;
    text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.49);
}

.search li.hover a {
    color: #fff;
}

.index_search_input {
    width: 615px;
    height: 60px;
    line-height: 60px;
    border: none;
    float: left;
    border-radius: 8px 0 0 8px;
    padding-left: 10px;
    font-size: 16px;
}

.search_div {
    position: relative;
    *margin-top: -12px;
    width: 692px;
    /* margin: 0px auto; */
    float: left;
}

.index_search_btn {
    border: none;
    width: 66px;
    height: 60px;
    font-size: 18px;
    color: #fff;
    background: #ff5200 url(https://static.fangxiaoer.com/global/imgs/ico/ico_fdz.png) no-repeat center;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
}

.search_div i {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_icon.png) -89px -100px;
    width: 18px;
    height: 9px;
    position: absolute;
    display: none;
}

#con_search_1 i {
    top: -9px;
    left: 36px;
}

#con_search_2 i {
    top: -9px;
    left: 136px;
}

#con_search_3 i {
    top: -9px;
    left: 237px;
}

#con_search_4 i {
    top: -9px;
    left: 336px;
}

.search_map {
    width: 140px;
    height: 36px;
    float: left;
    border-radius: 5px;
    position: absolute;
    top: 0;
    right: 0;
    background: #ff5200;
    text-align: center;
    line-height: 36px;
    color: #fff;
    display: none;
}

.search_map p {
    width: 180px;
    height: 53px;
    background: #fff;
    opacity: 0.1;
    position: absolute;
    *background: none;
    filter: alpha(opacity=10);
    display: none;
}

.search_map a {
    color: #fff;
    font-size: 16px;
}

.carousel {
    position: relative;
    line-height: 1
}

.carousel-inner {
    position: relative;
    width: 100%;
    overflow: hidden
}

.carousel-inner>.item {
    position: relative;
    display: none;
    -webkit-transition: .6s ease-in-out left;
    -moz-transition: .6s ease-in-out left;
    -o-transition: .6s ease-in-out left;
    transition: .6s ease-in-out left
}

.carousel-inner>.item>img,
.carousel-inner>.item>a>img {
    display: block;
    line-height: 1
}

.carousel-inner>.active,
.carousel-inner>.next,
.carousel-inner>.prev {
    display: block
}

.carousel-inner>.active {
    left: 0
}

.carousel-inner>.next,
.carousel-inner>.prev {
    position: absolute;
    top: 0;
    width: 100%
}

.carousel-inner>.next {
    left: 100%
}

.carousel-inner>.prev {
    left: -100%
}

.carousel-inner>.next.left,
.carousel-inner>.prev.right {
    left: 0
}

.carousel-inner>.active.left {
    left: -100%
}

.carousel-inner>.active.right {
    left: 100%
}

.carousel-control {
    position: absolute;
    top: 47%;
    left: 15px;
    width: 40px;
    height: 40px;
    margin-top: -20px;
    font-size: 60px;
    font-weight: 100;
    line-height: 30px;
    color: #fff;
    text-align: center;
    background: #222;
    border: 3px solid #fff;
    -webkit-border-radius: 23px;
    -moz-border-radius: 23px;
    border-radius: 23px;
    opacity: 0.4 !important;
    filter: alpha(opacity=50)
}

a.carousel-control:hover {
    opacity: 0.7 !important;
}

.carousel-control.right {
    right: 15px;
    left: auto
}

.carousel-control:hover,
.carousel-control:focus {
    color: #fff;
    text-decoration: none;
    opacity: .9;
    filter: alpha(opacity=90)
}

.carousel-indicators {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 5;
    margin: 0;
    list-style: none
}

.carousel-indicators li {
    display: block;
    float: left;
    width: 10px;
    height: 10px;
    margin-left: 5px;
    text-indent: -999px;
    background-color: #ccc;
    background-color: rgba(255, 255, 255, 0.25);
    border-radius: 5px;
    border: 1px solid #ccc;
}

.carousel-indicators .active {
    background-color: #fff;
    border: 1px solid #999;
}

.carousel-caption {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 15px;
    background: #333;
    background: rgba(0, 0, 0, 0.75)
}

.carousel-caption h4,
.carousel-caption p {
    line-height: 20px;
    color: #fff
}

.carousel-caption h4 {
    margin: 0 0 5px
}

.carousel-caption p {
    margin-bottom: 0
}

.guanggao {}

.guanggao .carousel-inner {
    min-width: 940px;
}

.guanggao .item {
    min-width: 940px;
    height: 460px;
    background: center no-repeat;
}

.guanggao .item_jpg_1 {
    background-image: url("https://static.fangxiaoer.com/web/images/sy/default/item_jpg_1.jpg")
}

.guanggao .item_jpg_2 {
    background-image: url("https://static.fangxiaoer.com/web/images/sy/default/item_jpg_2.jpg")
}

.guanggao .carousel-indicators {
    top: 431px;
    right: 48%;
}

.guanggao .carousel-indicators li:hover {
    cursor: pointer
}

.guanggao span {
    height: 26px;
    line-height: 26px;
    position: absolute;
    right: 50%;
    margin-right: -583px;
    bottom: 22px;
    z-index: 2;
    color: #fff;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#99000000', endColorstr='#99000000');
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 3px;
    display: block;
    font-size: 12px;
    font-weight: normal;
    text-align: left;
    padding: 0 15px;
    cursor: pointer;
}

.guanggao span:hover {
    text-decoration: underline
}

/*nav*/
.navigation {
    width: 100%;
    border-bottom: 1px #e8e8e8 solid;
    padding: 25px 0;
    overflow: hidden;
}

.navigation h2 a {
    color: #ff5200
}

.navigation h2 a:hover {
    color: #be0000
}

.need_box a {
    margin-right: 13px;
}

.navigation .main {
    width: 1170px;
    margin: 0 auto;
    overflow: hidden;
}

.navigation .box {
    width: 238px;
    border-right: 1px #ddd dashed;
    overflow: hidden;
    text-align: center;
    float: left;
}

.navigation h2 {
    float: left;
    font-size: 24px;
    color: #ff5200;
    height: 55px;
    width: 85px;
    line-height: 55px;
    font-weight: normal;
}

.navigation ul {
    float: left;
    line-height: 22px;
    margin-top: 5px;
}

.navigation ul li {
    margin-right: 18px;
    text-align: left;
}

.navigation ul li.hot {
    background: url("https://static.fangxiaoer.com/web/images/sy/index_new/hot.gif") no-repeat top right;
    padding-right: 30px;
}

.navigation ul li.libao {
    background: url("https://static.fangxiaoer.com/web/images/sy/index_new/libao.gif") no-repeat right;
    padding-right: 17px;
}

/*nav end*/

/*subject start*/
.subject {
    width: 100%;
    padding: 30px 0 20px;
    overflow: hidden;
}

.subject .main {
    width: 1170px;
    margin: 0 auto;
    overflow: hidden;
}

.subject .main .title {
    width: 100%;
    overflow: hidden;
    margin-bottom: 15px;
}

.subject .main .title li {
    float: left;
    width: 388px;
    height: 42px;
    text-align: center;
    background: #fbfbfb !important;
    border: 1px #d6d6d6 solid;
    border-top: 2px #e6e6e6 solid;
    border-right: none;
    line-height: 42px;
    cursor: pointer;
    font-size: 18px;
    color: #757575;
}

.subject .main .title li.hover {
    border-top: 2px #ff5200 solid;
    /* border-bottom:none; */
    color: #ff5200;
}

#con_qyh_1,
#con_qyh_2,
#con_qyh_3,
#con_qyh_4 {
    display: none;
    width: 1400px;
}

#con_qyh_1 {
    display: block
}

.subject .main .box {
    float: left;
    margin-right: 22px;
    *width: 275px;
    position: relative;
}

.subject .main .box .img {
    width: 275px;
    height: 185px;
    position: relative;
    display: block;
}

.subject .main .box .img img {
    width: 100%;
    height: 185px;
}

.subject .main .box .img i {
    background: #000;
    filter: alpha(opacity=50);
    opacity: 0.5;
}

.subject .main .box .img p {
    width: 100%;
    height: 33px;
    position: absolute;
    bottom: 0;
    left: 0;
    color: #fff;
    background: url("https://static.fangxiaoer.com/web/images/sy/index_new/black.png");
    line-height: 33px;
    font-size: 14px;
}

.subject .main .box .img p span {
    margin-left: 10px;
    width: 50%;
}

.subject .main .box .text {
    font-size: 14px;
    line-height: 29px;
}

.subject .main .box .text b {
    font-weight: normal;
}

.subject .main .box .text a {
    color: #ff5200;
    font-size: 12px;
    cursor: pointer;
    margin-left: 15px;
}

.subject .main .box .text span a {
    color: #333;
    font-size: 14px;
    font-weight: normal;
    margin-left: 0;
}

.subject .main .box .text span a:hover {
    color: #ff5200;
}

.subject .main .box .text p a {
    font-size: 14px;
    font-weight: normal;
    margin-left: 0;
}

.subject .main .box .text p a:hover {
    color: #ff5200
}

.subject i {
    float: right;
}

.subject .main .box .text big {
    font-size: 16px;
    font-weight: bold;
}

/*subject end*/

.index_title {
    width: 100%;
    height: 40px;
    border-bottom: 3px solid #333;
    position: relative;
}

.index_h1 {
    height: 24px;
    display: block;
    float: left;
    margin: 8px 17px 0 0;
}

.bztj_h1 {
    width: 96px;
}

.mxf_h1 {
    width: 72px;
    background-position: 0 -30px;
}

.mesf_h1 {
    width: 72px;
    background-position: 0 -62px;
}

.zf_h1 {
    width: 47px;
    background-position: 0 -95px;
}

.index_icon {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_icon.png) no-repeat;
    display: block;
    float: left;
    margin-right: 5px;
}

.dzyh_icon {
    width: 15px;
    height: 14px;
    background-position: -29px -195px;
}

.video_icon {
    width: 60px;
    height: 60px;
    background-position: -83px 0;
    position: absolute;
    top: 41px;
    left: 105px;
}

.bnzf_icon {
    width: 20px;
    height: 19px;
    background-position: -53px -1px;
}

.ydtz_icon {
    width: 10px;
    height: 14px;
    background-position: -7px -30px;
}

.dkjsq_icon {
    width: 14px;
    height: 19px;
    background-position: -34px -27px;
}

.wymf_icon {
    width: 20px;
    height: 19px;
    background-position: -53px -27px;
}

.fcdsj_icon {
    width: 20px;
    height: 19px;
    background-position: -91px -193px;
}

.xegj_icon {
    width: 20px;
    height: 19px;
    background-position: 0px -192px;
}

.xqdz_icon {
    width: 20px;
    height: 19px;
    background-position: -124px -192px;
}

.lskx_icon {
    width: 20px;
    height: 19px;
    background-position: -59px -191px;
}

.index_title_right {
    margin-top: 12px
}

.index_dzyh {
    margin-top: 15px;
    line-height: 14px;
    font-size: 12px;
}

.index_title ul {
    position: absolute;
    left: 220px;
    top: 0
}

.index_title li {
    float: left;
    line-height: 43px;
    display: block;
    text-align: center;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor1.jpg) right top no-repeat;
    margin-left: -5px
}

.index_title li a {
    line-height: 43px;
    padding: 0 18px;
    cursor: default;
    display: block;
}

.index_title li.hover {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_ul_hover.png) #fff bottom no-repeat !important;
}

.index_title li.hover a {
    color: #ff5200;
    /* background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor.jpg) right no-repeat; */
}

.index_title li.hover span {
    line-height: 43px;
    display: block;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor.jpg) left no-repeat;
}

.index_t_l {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor.jpg) left no-repeat;
    border-left: none !important;
}

#bztjqh1.hover span {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor2.jpg) left top no-repeat;
}

#xegj1.hover span {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor2.jpg) left top no-repeat;
}

#mxfqh1.hover span {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor2.jpg) left top no-repeat;
}

#mesfqh1.hover span {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor2.jpg) left top no-repeat;
}

#zfqh1.hover span {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor2.jpg) left top no-repeat;
}

.index_title li.hover .index_t_last {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_li_bor2.jpg) right top no-repeat;
}

#xegj2 {
    background: none;
}

.bztj_left {
    float: left;
    width: 200px;
    margin-right: 22px;
    position: relative;
}

.bztj_left h2 a {
    color: #ff5200;
    font-size: 16px;
    margin: 10px 0;
    display: block;
    line-height: 16px;
}

.bztj_left li {
    line-height: 24px;
}

.bztj_left li a {
    width: 105px;
    display: block;
    float: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bztj_left li span {
    color: #999
}

.bztj_middle {
    width: 672px;
    float: left;
    margin-right: 5px;
    position: relative;
}

.bztj_middle #con_mxfqh_1 a b {
    text-decoration: line-through;
}

.bztj_middle #con_mxfqh_1 a big {
    color: red;
}

.bztj_middle a {
    width: 204px;
    height: 201px;
    display: block;
    position: relative;
    float: left;
    margin: 20px 20px 0 0;
}

.bztj_middle img {
    width: 204px;
    height: 136px;
    display: block;
}

.bztj_middle .bztj_title {
    width: 188px;
    height: 28px;
    line-height: 28px;
    color: #fff;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/bg50.png);
    padding: 0 8px;
    position: absolute;
    top: 108px;
    font-size: 14px;
    font-weight: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bztj_middle .bztj_price {
    font-size: 14px;
    color: #333;
    margin: 15px 0 7px;
    line-height: 16px;
    font-weight: normal;
}

.bztj_price {
    font-weight: bold;
}

.bztj_middle li {
    float: left;
    line-height: 18px;
    padding: 0 3px;
    border: 1px solid #ff9900;
    color: #666;
    margin: 0 8px 6px 0;
    font-size: 12px;
}

.bztj_middle a.large_a,
.bztj_middle .large_a img {
    width: 428px;
    height: 201px;
}

.bztj_middle .swiper-wrapper a {
    margin: 0
}

.bztj_middle .bztj_price span big {
    font-size: 16px;
    font-weight: bold;
}

.bztj_right {
    float: right;
    width: 270px;
}

.esf_right a {
    position: relative;
    display: inline-block;
}

.bztj_right h2 {
    border-bottom: 1px solid #ededed;
    line-height: 40px;
    margin-top: 11px;
    position: relative;
}

.bztj_right h2 a {
    font-size: 16px;
    color: #333;
}

.bztj_right h2 a.more {
    font-size: 14px;
    color: #666;
    float: right;
    *position: absolute;
    *top: -10px;
    *right: 0;
}

.bztj_right h3 a {
    font-size: 16px;
    color: #FF5200;
    line-height: 35px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    text-align: center;
}

.bztj_right li {
    line-height: 32px;
    *line-height: 29px;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bztj_right li a {
    float: left;
}

.bztj_right li span {
    width: 4px;
    height: 4px;
    background-color: #ddd;
    margin: 15px 5px 0 0;
    display: block;
    float: left;
}

.bztj_li {
    width: 260px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.index_ul {
    margin-top: -1px;
}

.cff5200 {
    color: #ff5200;
    line-height: 26px;
}

.new_video {
    display: block;
    width: 270px;
    position: relative;
}

.new_video img {
    height: 160px;
    width: 100%;
}

.new_video h4 {
    font-size: 14px;
    font-weight: normal;
    margin-top: 4px;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/bg50.png);
    position: absolute;
    bottom: 0;
    width: 97%;
    color: #fff;
    height: 30px;
    line-height: 30px;
    left: 0;
    padding-left: 3%;
}

.new_title {
    color: #999;
    margin-top: 7px;
}

.new_left a {
    color: #333;
    font-size: 14px;
    margin-right: 12px;
    line-height: 26px;
    display: inline-block;
    line-height: 24px;
}

.swiper-container {
    width: 428px;
    height: 201px;
    float: left;
    margin: 20px 20px 0 0 !important;
}

.swiper-slide {
    width: 428px;
    height: 206px;
}

.pagination {
    position: absolute;
    z-index: 20;
    bottom: 7px;
    right: 10px;
}

.swiper-pagination-switch {
    display: inline-block;
    width: 10px;
    height: 10px;
    background-image: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_icon.png);
    background-position: -45px -55px;
    margin: 0 5px;
    cursor: pointer;
}

.swiper-active-switch {
    background-position: -29px -55px;
}

.mxf_ul .bztj_li {
    width: 260px
}

.esf_left .new_title {
    margin-top: 20px;
}

.esf_layout {
    color: #333;
    font-size: 14px;
    margin: 15px 0 13px;
    line-height: 15px;
    position: relative;
}

.esf_layout span {
    position: absolute;
    right: 0;
    top: 0;
}

.esf_layout i {
    font-size: 14px;
    color: #333;
}

.esf_layout i big {
    color: #ff5200;
    font-size: 16px;
    font-weight: bold;
}

.esf_address {
    font-size: 14px;
    color: #ff5200;
    line-height: 17px;
}

.esf_right img {
    line-height: 0;
    font-size: 0;
    vertical-align: middle;
    margin-top: 18px;
    height: 160px;
    width: 100%;
}

.esf_right .video_icon {
    top: 46px;
}

.esf_right h2 {
    margin-top: 19px;
    border-bottom: none;
}

.zf_right {
    margin-top: 12px;
}

.zf_right a {
    position: relative;
    display: block;
}

.zf_right img {
    line-height: 0;
    font-size: 0;
    vertical-align: middle;
    margin-top: 17px;
}

.scroll_out {
    position: absolute;
}

.scroll {
    position: relative;
}

.scroll li {
    padding-bottom: 9px;
    width: 190px;
    font-size: 14px;
}

.scroll li span {
    display: block;
    width: 63px;
    height: 17px;
    background: #eaeaea;
    color: #999;
    font-size: 12px;
    text-align: center;
    margin-bottom: 5px;
    line-height: 17px;
}

.scroll li p {
    width: 184px;
    line-height: 25px;
}

.scroll li p i {
    width: 55%;
    float: left;
}

.scroll li p i.price {
    text-align: right;
    width: 45%;
    float: none;
}

#myScroll {
    overflow: auto;
    height: 250px;
}

.nicescroll-rails {
    background-color: #eaeaea;
    width: 10px !important;
    left: 190px !important;
    opacity: 1 !important;
}

.rightmap_right_cen {
    position: relative;
    margin-top: 5px;
}

.rightmap_right_cen ul {}

.nicescrolltitle {
    float: left;
    padding-top: 30px;
}

.txtblock {
    background-color: #FFFFFF;
    padding: 0 10px 0px 0px;
    overflow: hidden;
}

.nicescroll-cursors {
    width: 10px !important;
    background-color: #ccc !important;
    border-radius: 0 !important;
    border: none !important;
    cursor: pointer;
}

.more {
    color: #333;
}

.fcphb p {
    float: left;
}

.fcphb h3 {
    font-size: 16px;
    color: #333;
    margin: 15px 0
}

.fcphb a {
    color: #333;
    font-size: 14px;
}

.fcphb a:hover {
    color: #be0000;
}

.fcphb ul {
    border-right: 1px solid #D8D8D8;
    margin-right: 0;
}

.fcphb li {
    line-height: 14px;
    margin-bottom: 20px;
    height: 14px;
    font-size: 14px;
    overflow: hidden;
}

.fcphb s {
    float: left;
    color: #fff;
    font-size: 12px;
    background: #ee5d33;
    width: 14px;
    height: 14px;
    text-align: center;
    line-height: 14px;
    margin-right: 10px;
    font-size: 12px;
}

.fcphb i {
    /* font-weight: bold; */
    /* color:#ff6600; */
}

.rplp,
.zxzx,
.rmxq {
    width: 380px;
    float: left;
    margin-right: 15px
}

.rplp .n1 {
    width: 154px;
}

.rplp .n2 {
    width: 89px;
    text-align: right;
}

.rplp .n3 {
    width: 87px;
    text-align: right;
}
.rplp .n4{
    width: 89px;
    text-align: left;
    padding-left: 15px;
    box-sizing: border-box;
}
.rplp .n5{ width: 96px; text-align: right;}
.rplp .n5 b{ font-weight: unset; margin-left: 3px;}
.pai a i:nth-child(1){ margin-right: 3px;}
.pai a i:nth-child(2){ margin-right: 3px;}

.zxzx .n1 {
    width: 280px;
}

.zxzx .n2 {
    width: 70px;
    text-align: right;
}

.rmxq {
    margin-right: 0
}

.rmxq ul {
    border-right: none
}

.rmxq .n1 {
    width: 150px;
}

.rmxq .n2 {
    width: 89px;
    text-align: right;
}

.rmxq .n3 {
    width: 107px;
    text-align: right;
}

.link {
    line-height: 13px;
    border-top: 1px solid #eaeaea;
    padding-top: 40px;
    margin-top: 10px;
}

.link h3 {
    float: left;
    color: #333;
    width: 70px;
    font-size: 14px;
    margin-top: 2px;
    line-height: 14px;
}

.link p {
    float: left;
    width: 1100px;
    overflow: hidden;
    margin-top: 2px;
    line-height: 15px;
}

.link p a {
    padding: 0 14px;
    margin-left: -1px;
    margin-bottom: 14px;
    height: 14px;
    display: inline-block;
    width: 125px;
}

.quanwei {
    text-align: center;
    background: #f5f5f5;
}

.quanwei .gong {
    width: 1170px;
    margin: 0 auto;
    text-align: left;
}

.t1 img {
    margin-right: 22px;
}

.t2 img {
    margin-right: 22px;
}

.default_img {
    display: none !important
}

@media (max-width: 1200px) {
    .quanwei .gong {
        width: 940px;
    }

    .t1 img {
        margin-right: 12px;
    }

    .t2 img {
        margin-right: 12px;
    }
}

.sy_index li a:hover {
    color: #ff5200
}

.new_left a:hover {
    color: #be0000
}

.ljz {
    display: none !important;
}

/*ÃƒÂ©Ã…â€œÃ¢â€šÂ¬ÃƒÂ¦Ã‚Â±Ã¢â‚¬Å¡ÃƒÂ¥Ã‚Â®Ã…Â¡ÃƒÂ¥Ã‹â€ Ã‚Â¶*/
.fxe_phone {
    line-height: 33px;
    font-size: 23px;
    color: #ff5200;
    padding-left: 313px;
    background: url(https://static.fangxiaoer.com/web/images/sy/index/smallPhone.png) no-repeat 283px 9px;
    width: 197px;
    font-weight: bold;
    font-family: Georgia;
}

.index_bnzf_samll {
    width: 0;
    height: 91px;
    position: fixed;
    left: 0;
    bottom: 88px;
    display: none;
    cursor: pointer;
}

.index_bnzf_big {
    width: 100%;
    height: 180px;
    position: fixed;
    bottom: 0;
    left: 0;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/bg70.png);
    z-index: 99;
    color: #fff;
}

.index_bnzf_success {
    width: 100%;
    height: 180px;
    position: fixed;
    bottom: 0;
    left: 0;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/bg70.png);
    z-index: 99;
    color: #fff;
}

.index_bnzf {
    width: 1170px;
    margin: 0 auto;
    position: relative;
}

.index_bnzf_nav {
    width: 911px;
    height: 16px;
    margin: 21px 0 31px;
    position: relative;
    z-index: 10;
}

.index_bnzf_nav li {
    text-align: center;
    float: left;
    position: relative;
    color: #666;
    font-size: 16px;
    margin-right: 12px;
    line-height: 14px;
    cursor: pointer;
    border: 1px solid #ccc;
    border-bottom: 0;
    width: 132px;
    padding-left: 35px;
    line-height: 46px;
}

.index_bnzf_nav li span {
    width: 13px;
    height: 13px;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_bnzf_icon.png) 0 -17px;
    display: block;
    float: left;
    margin: 1px 11px 0 0;
}

.index_bnzf_nav li.hover span {
    background-position: 0 0;
}

.index_bnzf_form {
    height: 59px;
    width: 100%;
    position: relative;
}

.index_bnzf_form>div {
    border: 1px solid #ff5200;
    width: 844px;
    padding-top: 25px;
    height: 305px;
    padding-left: 50px;
}

.index_bnzf_close {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_bnzf_icon.png) -15px 0;
    cursor: pointer;
    width: 20px;
    height: 19px;
    position: absolute;
    right: 0;
    top: -12px;
    cursor: pointer;
    *top: 20px
}

.index_success_close {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/index_bnzf_icon.png) -15px 0;
    cursor: pointer;
    width: 20px;
    height: 19px;
    position: absolute;
    right: 0;
    cursor: pointer;
    top: 20px
}

.errorBox {
    font-size: 12px;
    line-height: 17px;
    margin-left: 65px;
    height: 30px;
}

.yykf_ul .hf_textarea,
.yykf_ul .hf_input {}

.w168 {
    width: 168px;
}

.w180 {
    /* width: 231px; */
}

.w190 {
    width: 190px;
}

.success_tc {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    background: #fff;
    width: 860px;
    height: 245px;
    z-index: 100;
}

.success_tc p {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/success.png) no-repeat left;
    padding-left: 80px;
    line-height: 40px;
    margin-left: 268px;
    margin-top: 20px;
}

.success_tc .hf_submit {
    position: absolute;
    left: 259px;
    top: 180px;
}

.advertisement {
    width: 365px;
    height: 330px;
    background: url(https://static.fangxiaoer.com/web/images/sy/index/small.jpg) no-repeat right bottom;
    float: right;
    margin-top: -330px;
}

.advertisement>a {
    width: 100%;
    height: 100%;
    display: inline-block;
}

.bnzf_left ul .hover i {
    background-position-y: -32px !important;
}

.bnzf_left ul .hover {
    border: 1px solid #ff5200;
    border-bottom: 1px solid #fff;
}

.my_xl {
    height: 27px;
    line-height: 27px;
    width: 381px;
    padding: 2px 3px 2px 20px;
    position: relative;
    font-size: 14px;
    background: #fff url(https://static.fangxiaoer.com/web/images/sy/index_new/my_xiala2.gif) 381px 12px no-repeat;
    cursor: pointer;
    display: inline-block;
    float: left;
}

.my_xl_txt {
    float: left;
    width: 385px;
    line-height: 28px;
    padding-right: 17px;
    color: #999999;
    height: 27px;
}

.hxmj .my_xl_txt {
    width: 101px;
}

.hf_ul .hxmj,
.hf_ul .yxqy {
    width: 200px;
    float: left;
    margin-right: 0;
}

.hf_ul .hxmj .my_xl {
    width: 101px;
    background: #fff url(https://static.fangxiaoer.com/web/images/sy/index_new/my_xiala2.gif) 103px 12px no-repeat;
}

.hf_ul .hxmj .my_xl {
    width: 101px;
    background: #fff url(https://static.fangxiaoer.com/web/images/sy/index_new/my_xiala2.gif) 103px 12px no-repeat;
}

.hf_ul .yxqy .my_xl {
    width: 101px;
    background: #fff url(https://static.fangxiaoer.com/web/images/sy/index_new/my_xiala2.gif) 103px 12px no-repeat;
}

.my_xl,
.my_xl_list {
    border: 1px solid #ccc;
}

.hf_ul .gfys .my_xl {
    width: 164px;
    background: #fff url(https://static.fangxiaoer.com/web/images/sy/index_new/my_xiala2.gif) 163px 12px no-repeat;
}

.hf_ul .zffs .my_xl {
    width: 164px;
    background: #fff url(https://static.fangxiaoer.com/web/images/sy/index_new/my_xiala2.gif) 163px 12px no-repeat;
}

.gfys .my_xl_txt {
    width: 162px;
}

.hf_ul .gfys,
.hf_ul .zffs {
    width: 263px;
    margin-right: 0;
    padding-left: 17px;
}

.my_xl_txt,
.my_xl_list li {
    text-indent: 0px;
    overflow: hidden;
}

.my_xl_list {
    z-index: 99;
    position: absolute;
    top: 32px;
    left: -1px;
    z-index: 88888;
    border-top: none;
    width: 100%;
    display: none;
    _top: 23px;
    margin-left: 0px !important
}

.my_xl_list li {
    list-style: none;
    height: 30px;
    line-height: 30px;
    cursor: default;
    background: #fff;
    padding-left: 6px;
    color: #666;
}

.my_xl_list li.focus {
    background: #3399FF;
    color: #fff
}

.hf_ul>li {
    width: 480px;
    height: 60px;
    line-height: 34px;
    float: left;
    margin-right: 52px;
    position: relative;
}

.index_bnzf_nav i {
    height: 32px;
    width: 32px;
    position: absolute;
    background: url(https://static.fangxiaoer.com/web/images/sy/index/smallIcon.png) no-repeat;
    top: 6px;
    left: 53px;
}

.hf_ul>li.mr0 {
    margin-right: 0px;
}

.hf_ts {
    float: left;
    margin-right: 10px;
    line-height: 34px;
}

.input_desc {
    position: absolute;
    left: 87px;
    top: 0;
    z-index: 99;
    color: #999999;
}

.yzm_li .input_desc {
    left: 110px;
}

.letter4 {
    letter-spacing: 4px
}

.hf_ts span {
    width: 20px;
    display: block;
    float: left;
    margin-top: 11px;
    color: #f00;
    font-size: 26px;
    line-height: 26px
}

.hf_input {
    width: 381px;
    padding: 2px 6px 2px 17px !important;
    height: 28px !important;
    margin-bottom: 0 !important;
    line-height: 32px;
    color: #666 !important;
    border: 1px solid #ccc;
    /*border-radius:3px;*/
    float: left;
}

::-webkit-input-placeholder {
    /* WebKit browsers */
    color: #666;
    font-family: "Microsoft YaHei"
}

.gfys .hf_input {
    width: 107px;
    padding-left: 11px !important;
}

:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #666;
    font-family: "Microsoft YaHei"
}

::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #666;
    font-family: "Microsoft YaHei"
}

:-ms-input-placeholder {
    /* Internet Explorer 10+ */
    color: #666;
    font-family: "Microsoft YaHei"
}

.yzm_input {
    width: 180px !important;
    margin-left: 66px;
}

.yzm_btn {
    margin-left: 20px;
    text-align: center;
    line-height: 32px;
    font-size: 12px;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 1px;
    background-color: #eee;
    cursor: pointer;
    float: left;
    width: 100px;
    border-radius: 3px;
    color: #999;
}

.hf_ul {
    float: left;
    width: 480px;
}

.hf_textarea {
    width: 291px;
    padding: 6px;
    display: none;
    resize: none;
    height: 79px !important;
}

.hf_submit {
    margin-bottom: 26px;
    width: 204px !important;
    float: left;
    margin-left: 66px;
    height: 35px;
    border: none;
    color: #fff;
    font-size: 16px;
    background-color: #ff5200;
    cursor: pointer;
    outline: none;
    font-family: ÃƒÂ¥Ã‚Â¾Ã‚Â®ÃƒÂ¨Ã‚Â½Ã‚Â¯ÃƒÂ©Ã¢â‚¬ÂºÃ¢â‚¬Â¦ÃƒÂ©Ã‚Â»Ã¢â‚¬Ëœ;
    padding-bottom: 3px;
    border-radius: 3px;
}

#xf_djs {
    display: none;
}

.house_form_p {
    margin: 13px 0 13px 17px;
    font-size: 14px;
    color: #999
}

.errorBox label {
    margin-bottom: 0 !important;
    font-size: 12px;
    line-height: 22px;
    color: #fe2002
}

.yzm_li .errorBox {
    margin-left: 94px;
}

#Span1,
#Span2,
#Span3,
#Span4 {
    display: none;
}

#con_bnzfqh_3 .my_xl_list li {
    padding-left: 3px;
}

#con_bnzfqh_3 #datetimepicker_mask {
    /* padding-left:5px!important; */
}

#con_bnzfqh_3 .area_li {
    width: 165px;
}

#con_bnzfqh_3 .input_desc {
    /* left: 45px; */
    /* text-indent: 5px; */
}

.w190 .input_desc {
    left: 105px !important;
}

#qyh3 {
    border-right: 1px #dedede solid;
}

.bnzf_left {
    float: right;
    width: 898px;
    height: 317px;
}

.bnzf_right {
    float: left;
    width: 270px;
    height: 350px;
}

.bnzf_txt {
    color: #bfbfbf;
    font-size: 14px;
    margin-top: 17px;
}

.bnzf_txt a {
    float: right;
}

.form_left {
    float: left;
    width: 235px;
}

.form_right {
    float: left;
    width: 323px;
    margin-right: 340px;
}

.form_btn {
    position: relative;
    width: 478px;
}

.form_btn .input_desc {
    top: 6px;
}

.bnzf_right li {
    font-size: 14px;
    color: #999;
    line-height: 24px;
    margin-top: 16px;
    padding-left: 45px;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/ico_bnzf.gif) no-repeat
}

.bnzf_right li p {
    color: #333;
}

.bnzf_right li.n1 {
    background-position: 0px 8px;
}

.bnzf_right li.n2 {
    background-position: 0px -54px;
}

.bnzf_right li.n3 {
    background-position: 0px -114px;
}

.bnzf_right li.n4 {
    background-position: 0px -174px;
}

.bnzf_right li.n5 {
    background-position: 0px -238px;
}

/*ÃƒÂ¥Ã‚Â°Ã‚ÂÃƒÂ¤Ã‚ÂºÃ…â€™ÃƒÂ§Ã‚Â®Ã‚Â¡ÃƒÂ¥Ã‚Â®Ã‚Â¶*/
#con_xegj_2 {
    width: 1170px;
    margin: 0 auto;
}

.xegjzszx {
    width: 1000px;
    padding-top: 20px;
    overflow: hidden;
}

.xegjzszx li {
    height: 270px;
    width: 207px;
    float: left;
    text-align: center;
    margin-right: 19px;
    line-height: 24px;
    position: relative;
    margin-bottom: 20px;
}

.xegjzszx li img {
    height: 270px;
    width: 207px;
}

.xegjzszx li .infor {
    position: absolute;
    left: 20px;
    bottom: 10px;
    text-align: left;
    padding-right: 10px;
}

.xegjzszx li .infor span {
    font-size: 18px;
    color: #fff;
    text-align: left;
    line-height: 32px;
    text-shadow: 1px 1px #666;
}

.xegjzszx li .infor p {
    color: #fff;
    line-height: 20px;
    text-shadow: 1px 1px #666;
}

.xegj_cj {
    width: 875px;
    float: left;
    margin-right: 25px;
    height: 300px;
    overflow: hidden;
    padding-top: 20px;
}

.xegj_cj li {
    height: 60px;
    line-height: 60px
}

.xegj_cj span {
    float: left;
}

.xegj_cj a {
    display: block;
    height: 60px;
    background: #f0f0f0;
    color: #333;
}

.xegj_cj span.f1 {
    padding-left: 30px;
    width: 100px
}

.xegj_cj span.f2 {
    width: 64px
}

.xegj_cj span.f3 {
    width: 68px
}

.xegj_cj span.f4 {
    width: 107px
}

.xegj_cj span.f5 {
    width: 66px
}

.xegj_cj span.f6 {
    width: 70px
}

.xegj_cj span.f7 {
    width: 240px
}

.xegj_cj span.f8 {
    width: 88px
}

.xegj_cj span i {
    color: #ff5200
}

.xegj_cj a.xegj_even {
    background: #fff
}

.xegj_cj a:hover {
    background: #e9e9e9
}

.xegj_cj a.xegj_even:hover {
    background: #e9e9e9
}

.bnzf_right1 {
    float: left;
    width: 268px;
    height: 350px;
}

.bnzf_right1 h2 {
    color: #fff;
    background: url(https://static.fangxiaoer.com/web/images/sy/index/1.png) no-repeat center center #ff5200;
    width: 253px;
    margin-top: 22px;
    font-size: 23px;
    text-align: center;
    height: 44px;
}

.bnzf_qh {
    position: relative;
    height: 280px;
    overflow: hidden
}

.bnzf_qh ul {
    position: absolute;
    top: 0;
    width: 270px;
}

.tempWrap {
    margin-left: 31px;
    width: 1110px !important;
    margin-top: 20px;
}

.hd {
    position: relative;
    width: 1170px;
    height: 1px;
}

.hd a {
    position: absolute;
    width: 20px;
    height: 38px;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/xegj_qh_ico.png) 0px -19px;
    top: 134px;
    left: inherit;
    right: 0px;
    cursor: pointer;
}

.hd a.prev {
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/xegj_qh_ico.png);
    left: 0;
}

.bnzf_qh li i {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #fff;
    padding-left: 80px;
    background: url(https://static.fangxiaoer.com/web/images/sy/need/list/hide.png) no-repeat left !important;
    width: 0px;
    text-align: right;
    color: #1e70a8 !important;
    height: 28px;
}

.bnzf_right2 {
    /* padding: 0 16px; */
    border: 1px solid #ff5200;
    float: left;
    margin-top: 22px;
    /* padding-bottom: 4px; */
}

.bnzf_right2 dt {}

.bnzf_right2 dt span {
    color: #fff;
    background: url(https://static.fangxiaoer.com/web/images/sy/index/4.png) no-repeat center center #ff5200;
    width: 253px;
    font-size: 23px;
    text-align: center;
    height: 44px;
    display: block;
}

.bnzf_right2 dt span a {
    width: 253px;
    font-size: 23px;
    height: 44px;
    display: block;
}

.bnzf_right2 dt img {
    /* margin: 16px 0; */
    margin-left: 1px !important;
    /* margin-bottom: -4px!important; */
}

.bnzf_right2 dd {
    background: #f7f7f7;
    line-height: 23px;
    width: 210px;
    padding: 0 10px;
    padding-bottom: 17px;
    margin-bottom: 15px;
    margin-left: 10px;
    display: none;
}

.bnzf_right2 dd img {}

.bnzf_right2 dd p {}

.bnzf_right3 {
    float: left;
    width: 270px;
    height: 350px;
}

.bnzf_right3 li {
    height: 56px;
    overflow: hidden
}

.bnzf_right3 li i {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #fff;
    padding-left: 80px;
    background: url(https://static.fangxiaoer.com/web/images/sy/need/list/hide.png) no-repeat left !important;
    width: 0px;
    text-align: right;
    color: #1e70a8 !important;
    height: 28px;
}

.bnzf_right1 dl {
    width: 251px;
    border: 1px solid #ff5200;
    /* margin-top: 22px; */
    height: 332px;
}

.bnzf_right1 dl dt {
    /* background:url(https://static.fangxiaoer.com/web/images/sy/index/smallHeader.jpg) no-repeat top left; */
    width: 200px;
    height: 26px;
    border: 1px #d2d2d2 dashed;
    border-radius: 100px;
    margin: 0 auto;
    margin-top: 11px;
    text-align: center;
    margin-bottom: 1px;
}

.bnzf_right1 dl dd {
    padding-left: 5px;
    margin-left: 18px;
    /* border-bottom: 1px solid #e9e9e9; */
    width: 213px;
    padding-top: 6px;
}

.bnzf_right1 dd h1 {
    font-weight: 400;
    color: #ff5200;
    font-size: 14px;
    line-height: 20px;
}

.bnzf_right1 dd h1 span {
    display: inline-block;
    width: 7px;
    height: 7px;
    background: #ff5200;
    border-radius: 50%;
    margin-right: 10px;
}

.bnzf_right1 dd p {
    font-size: 12px;
    color: #333;
    line-height: 18px;
    /* padding: 7px 0; */
}

/*ÃƒÂ¥Ã‚Â°Ã‚ÂÃƒÂ¤Ã‚ÂºÃ…â€™ÃƒÂ¥Ã¢â‚¬Â¦Ã‚Â³ÃƒÂ¦Ã‚Â³Ã‚Â¨*/
.xegz {
    width: 840px;
    line-height: 35px;
    float: left;
    height: 36px;
    color: #666;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/ico_hot.jpg) no-repeat left;
    padding-left: 25px;
    overflow: hidden;
    margin-top: 6px;
}

.xegz a {
    float: right;
    color: #ff5200;
}

.xegz a:hover {
    color: #f00;
    text-decoration: underline
}

/*ÃƒÂ¤Ã‚Â¸Ã¢â‚¬Å“ÃƒÂ©Ã‚Â¢Ã‹Å“ÃƒÂ¥Ã¢â‚¬Â¦Ã‚Â¥ÃƒÂ¥Ã‚ÂÃ‚Â£*/
.index_zhuanti {
    width: 1117px;
    overflow: hidden;
    margin: 0 auto 30px;
}

.index_zhuanti ul {
    overflow: hidden;
    zoom: 1;
    width: 1200px;
}

.index_zhuanti li {
    float: left;
    overflow: hidden;
    zoom: 1;
    border-right: 1px #dedede solid;
    padding: 0 13px;
}

.index_zhuanti p {
    width: 48px;
    height: 48px;
    float: left;
    background: url("https://static.fangxiaoer.com/web/images/sy/index_new/index_zhuanti.png") 7px 3px;
    margin-right: 0px;
}

.index_zhuanti p.zt_picture2 {
    background-position: 4px -51px;
}

.index_zhuanti p.zt_picture3 {
    background-position: 7px -102px;
}

.index_zhuanti p.zt_picture4 {
    background-position: 0px -149px;
}

.zhuantiLink {
    float: left;
    text-align: center;
    width: 195px;
    margin-left: 10px;
}

.zhuantiLink span {
    display: block;
    margin: 0 auto;
    color: #333;
    font-size: 16px;
    line-height: 34px;
}

.zhuantiLink b {
    display: block;
    border: 1px #999 solid;
    color: #999;
    width: 70px;
    margin: 0 auto;
    border-radius: 3px;
    font-weight: normal;
}

.zhuantiLink b:hover {
    color: #ff5200;
    border-color: #ff5200;
}

/*ÃƒÂ¥Ã‚Â¼Ã‚Â¹ÃƒÂ¥Ã¢â‚¬Â¡Ã‚Âº*/
.tc_main,
.bg_tc {
    display: none;
}

/*ÃƒÂ¥Ã‚Â¤Ã‚Â§ÃƒÂ¦Ã¢â‚¬Â¢Ã‚Â°ÃƒÂ¦Ã‚ÂÃ‚Â®*/
.dashuju_nav {
    position: absolute;
    top: 17px;
    left: 6px;
    z-index: 2;
}

.dashuju_nav i.dsj_up {
    color: red;
    padding-left: 12px;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/dsj_ico.png) left 3px no-repeat;
    margin-left: 5px;
}

.dashuju_nav i.dsj_down {
    color: #11b900;
    padding-left: 12px;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/dsj_ico.png) left -13px no-repeat;
    margin-left: 5px;
}

.right_ico {
    position: fixed;
    left: 50%;
    top: 50%;
    width: 31px;
    z-index: 10;
    margin-left: 600px;
    display: none;
}

.right_ico a {
    margin-bottom: 2px;
    cursor: pointer;
    border-radius: 3px;
    background: #c2c2c2;
    display: block;
    position: relative;
    width: 35px;
    height: 35px;
    float: right;
    overflow: visible !important;
}

.right_ico a span {
    display: none;
}

.right_ico a i {
    width: 35px;
    height: 35px;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/right_ico.png) no-repeat;
    position: absolute;
    right: 0;
}

.right_ico a.n1 img {
    margin-bottom: 5px;
}

.right_ico a.n1 i {
    background-position: 7px 6px;
}

.right_ico a.n2 i {
    background-position: 7px -25px;
}

.right_ico a.n3 i {
    background-position: 6px -58px;
}

.right_ico a.n4 i {
    background-position: 6px -88px;
}

.right_ico a.n5 i {
    background-position: 7px -186px;
}

.right_ico a.n6 i {
    background-position: 2px -123px;
}

.right_ico a.n7 i {
    background-position: 6px -152px;
}

.right_ico a.n8 i {
    background-position: 7px -220px;
}

.right_ico a:hover {
    background: #ff5520;
}

.right_ico a.n6 {
    display: none
}

.right_ico .n9 {
    position: absolute;
    top: 0;
    right: 33px;
    z-index: 999999;
    display: none;
}

.right_ico .n9 .sj {
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/right_ico.png) no-repeat right center;
    position: absolute;
    background-position: 4px -256px;
    width: 22px;
    height: 32px;
    position: absolute;
    right: 0px;
    top: 66px;
}

.right_ico .n9 .bnzf {
    background: #fff;
    width: 227px;
    padding-top: 10px;
    position: relative;
    margin-top: -76px;
    border: 2px solid #ff5200;
    float: left;
    margin-right: 8px;
}

.right_ico .n9 .bnzf .fxe_bnzf_xl {
    display: none;
    line-height: 23px;
    position: absolute;
    background: #fff;
    width: 142px;
    top: 26px;
    margin-left: -1px;
    border: 1px solid #ccc;
    z-index: 10000;
}

.right_ico .n9 .bnzf .fxe_bnzf_xl>li {
    line-height: 31px;
}

.right_ico .n9 .bnzf .fxe_bnzf_xl>li:hover {
    background: #3399ff;
    color: #fff;
}

.right_ico .n9 .bnzf>ul>li {
    margin-bottom: 4px;
    height: 28px;
    position: relative;
}

.right_ico .n9 .bnzf h1 {
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/right_ico.png) no-repeat right center;
    background-position: 186px -288px;
    font-weight: 400;
    color: #666;
    margin: 0 10px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
    padding-left: 6px;
    line-height: 31px;
    margin-bottom: 14px;
    cursor: pointer;
}

.right_ico .n9 .bnzf .fxe_bnzf_bt {
    color: #666;
    display: inline-block;
    float: left;
    width: 64px;
    line-height: 22px;
    margin-left: 10px;
}

.right_ico .n9 .bnzf #fxe_bnzf_phone {
    line-height: 24px;
    padding-left: 4px;
    width: 136px;
}

.right_ico .n9 .bnzf .fxe_yzm_btn {
    text-align: center;
    line-height: 24px;
    border-radius: 2px;
    display: inline-block;
    width: 86px;
    cursor: pointer;
}

.right_ico .n9 .bnzf textarea {
    width: 142px;
    height: 25px;
    resize: none;
}

.right_ico .n9 .bnzf #fxe_bnzf_yzm {
    width: 46px;
    height: 22px;
    margin-bottom: 20px;
}

.right_ico .n9 .bnzf .fxe_ReSendValidateCoad {
    color: #ff5200;
    border: 1px solid #ff5200;
    font-size: 12px;
}

.right_ico .n9 .bnzf .fxe_validateCode {
    color: #666;
    border: 1px solid #666;
    display: none;
    font-size: 12px;
}

.right_ico .n9 .bnzf #fxe_bnzf_submit {
    background: #ff5200;
    border: 0;
    line-height: 28px;
    margin: 0 11px;
    width: 204px;
    border-radius: 4px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    margin-top: 4px;
}

.right_ico .n9 .bnzf .fxe_bnzf_wk {
    width: 142px;
    /* overflow: hidden; */
    margin: 0;
    float: left;
    height: 25px;
    border: 1px solid #ccc;
    background: #fff url(https://static.fangxiaoer.com/web/images/sy/index_new/my_xiala2.gif) 123px 9px no-repeat;
}

.right_ico .n9 .bnzf .fxe_bnzf_xs {
    float: left;
    line-height: 24px;
    margin-left: 8px;
    color: #666;
    width: 134px;
}

.right_ico a.n1:hover span {
    right: 30px;
    top: 0;
    position: absolute;
    padding: 19px 19px 10px 19px;
    border: 1px solid #dbdbdb;
    background: #fff;
    margin-right: 16px;
    color: #ff5200;
    text-align: center;
    line-height: 21px;
}

.right_ico a.n1:hover span em {
    position: absolute;
    width: 16px;
    height: 35px;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/right_ewm_jd2017.png) no-repeat;
    top: 0;
    right: -16px;
}

.right_ico a.n2:hover {
    width: 100px
}

.right_ico a.n3:hover {
    width: 88px
}

.right_ico a.n4:hover,
.right_ico a.n5:hover,
.right_ico a.n6:hover {
    width: 76px
}

.right_ico a:hover span {
    display: block;
    color: #fff;
    padding-left: 8px;
    line-height: 31px;
}

#MEIQIA-BTN-HOLDER {
    display: none !important
}

.picScroll-left {
    overflow: hidden;
    height: 311px;
}

/*2017-7-13ÃƒÂ¥Ã‚Â¤Ã‚Â´ÃƒÂ¦Ã¢â‚¬â€œÃ¢â‚¬Â¡ÃƒÂ¤Ã‚Â»Ã‚Â¶*/
.head_top {
    position: fixed;
    z-index: 10;
    width: 100%;
    /* background: url(https://static.fangxiaoer.com/global/imgs/ico/nav_bg.png) top; */
    height: 50px;
    background: #fff;
    top: 0;
    z-index: 9999;
    border-bottom: 1px solid #e8dfdf;
}

.carousel-inner {
    position: relative;
    width: 100%;
    overflow: hidden;
    height: 420px;
}

.carousel-inner>.item {
    position: relative;
    display: none;
    -webkit-transition: .6s ease-in-out left;
    -moz-transition: .6s ease-in-out left;
    -o-transition: .6s ease-in-out left;
    transition: .6s ease-in-out left
}

.carousel-inner>.item>img,
.carousel-inner>.item>a>img {
    display: block;
    line-height: 1
}

.carousel-inner>.active,
.carousel-inner>.next,
.carousel-inner>.prev {
    display: block
}

.carousel-inner>.active {
    left: 0
}

.carousel-inner>.next,
.carousel-inner>.prev {
    position: absolute;
    top: 0;
    width: 100%
}

.carousel-inner>.next {
    left: 100%
}

.carousel-inner>.prev {
    left: -100%
}

.carousel-inner>.next.left,
.carousel-inner>.prev.right {
    left: 0
}

.carousel-inner>.active.left {
    left: -100%
}

.carousel-inner>.active.right {
    left: 100%
}

.carousel-control {
    position: absolute;
    top: 47%;
    left: 15px;
    width: 40px;
    height: 40px;
    margin-top: -20px;
    font-size: 60px;
    font-weight: 100;
    line-height: 30px;
    color: #fff;
    text-align: center;
    background: #222;
    border: 3px solid #fff;
    -webkit-border-radius: 23px;
    -moz-border-radius: 23px;
    border-radius: 23px;
    opacity: 0.4 !important;
    filter: alpha(opacity=50)
}

a.carousel-control:hover {
    opacity: 0.7 !important;
}

.carousel-control.right {
    right: 15px;
    left: auto
}

.carousel-control:hover,
.carousel-control:focus {
    color: #fff;
    text-decoration: none;
    opacity: .9;
    filter: alpha(opacity=90)
}

.carousel-indicators {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 5;
    margin: 0;
    list-style: none
}

.carousel-indicators li {
    display: block;
    float: left;
    width: 10px;
    height: 10px;
    margin-left: 5px;
    text-indent: -999px;
    background-color: #ccc;
    background-color: rgba(255, 255, 255, 0.25);
    border-radius: 5px;
    border: 1px solid #ccc;
}

.carousel-indicators .active {
    background-color: #fff;
    border: 1px solid #999;
}

.carousel-caption {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 15px;
    background: #333;
    background: rgba(0, 0, 0, 0.75)
}

.carousel-caption h4,
.carousel-caption p {
    line-height: 20px;
    color: #fff
}

.carousel-caption h4 {
    margin: 0 0 5px
}

.carousel-caption p {
    margin-bottom: 0
}

.bannerLoop {
    width: 100%;
    min-width: 1170px;
    height: 420px;
}

.bannerLoop ul a {
    display: inline-block;
    width: 100%;
    height: 647px;
    background: url(https://static.fangxiaoer.com/global/imgs/index/banner.jpg) no-repeat top;
    height: 420px;
    position: relative;
    background-attachment: fixed;
    cursor: pointer;
}

.bannerLoop span {
    height: 26px;
    line-height: 26px;
    position: absolute;
    right: 50.9%;
    margin-right: -412px;
    top: 285px;
    z-index: 2;
    color: #fff;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#99000000', endColorstr='#99000000');
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 3px;
    display: block;
    font-size: 12px;
    font-weight: normal;
    text-align: left;
    padding: 0 15px;
    cursor: pointer;
}

.fxe_banner {
    position: absolute;
    width: 1170px;
    top: 0;
    display: none;
    left: 50%;
    margin-left: -585px;
}

.fxe_banner a {
    position: absolute;
    top: 175px;
    width: 36px;
    height: 60px;
    background: url(https://static.fangxiaoer.com/global/imgs/ico/banner_qh0.png) no-repeat;
    opacity: 0.8;
}

.fxe_banner a:hover {
    background: url(https://static.fangxiaoer.com/global/imgs/ico/banner_qh1.png) no-repeat;
}

@media (min-width: 1350px) {
    .fxe_banner {
        /* width: 1350px; */
        /* margin-left: -675px; */
    }
}

@media (min-width: 1500px) {
    .fxe_banner {
        /* width: 1170px; */
        /* margin-left: -750px; */
    }
}

.fxe_banner a.next {
    background-position: -36px 0;
}

.carousel-inner:hover .fxe_banner {
    display: block;
}

.logo {
    float: left;
    margin-top: 12px;
    margin-right: 0;
}

.logo img {
    width: 112px;
}

.login {
    color: #fff;
}

.login a {
    color: #fff;
}

.login .recode {
    display: inline-block;
    position: relative;
    background: url(https://static.fangxiaoer.com/global/imgs/index/recodePhoneh.png) no-repeat 2px 6px;
    padding-left: 20px;
    text-decoration: none;
    padding: 3px 0 3px 20px;
}

.login .recode:hover div {
    top: 17px;
    opacity: 1;
    visibility: visible;
    z-index: 10000000;
}

.login .recode div {
    position: absolute;
    background: url(https://static.fangxiaoer.com/global/imgs/index/recode.png) no-repeat top left;
    width: 133px;
    height: 147px;
    top: 35px;
    left: -24px;
    position: absolute;
    left: 50%;
    transition: all .5s;
    -moz-transition: all .5s;
    /* Firefox 4 */
    -webkit-transition: all .5s;
    /* Safari and Chrome */
    -o-transition: all .5s;
    /* Opera */
    opacity: 0;
    visibility: hidden;
    margin-left: -53px;
    z-index: 1000000;
}

.login .recode img {
    width: 106px;
    height: 106px;
    margin: 0 auto;
    display: block;
    margin-top: 25px;
}

.nav_ul {
    width: 610px;
    float: left;
    margin-top: 11px;
    margin-left: 20px;
    font-size: 14px;
}

.nav_ul li {
    float: left;
    margin: 2px 12px;
}

.nav_ul li a {
    color: #333;
    font-size: 16px;
    font-weight: 500;
}

.login {
    color: #333;
    float: right;
    margin-top: 10px;
    position: relative;
}

.login a {
    color: #333;
}

.login a em {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 12px;
    max-width: 76px;
}

.login a b {
    color: #ff5200;
    font-weight: normal;
}

.title {
    width: 1170px;
    margin: 0 auto;
    margin-bottom: 10px;
}

.title p {
    font-size: 16px;
    color: #888;
}

.title h1 {
    font-size: 28px;
    font-weight: 100;
    margin-bottom: 14px;
}

.title a {
    float: right;
}

#head2017 {
    position: fixed;
    top: 0;
    width: 100%;
    left: 0;
    z-index: 10000;
}

.login .login_xl {
    display: inline;
    position: relative;
}

.login .login_xl p {
    width: 108px;
    box-sizing: border-box;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.19);
    border: 1px solid #eaeaea;
    position: absolute;
    top: 45px;
    left: 50%;
    background: #fff;
    transition: all .5s;
    -moz-transition: all .5s;
    /* Firefox 4 */
    -webkit-transition: all .5s;
    /* Safari and Chrome */
    -o-transition: all .5s;
    /* Opera */
    opacity: 0;
    visibility: hidden;
    margin-left: -53px;
    z-index: 1000000;
}

.login .login_xl p a {
    background: none;
    color: #333;
    padding: 0;
    text-align: center;
    line-height: 34px;
    display: block;
}

.login .login_xl p a:hover {
    color: #ff5200;
}

.login .login_xl:hover p a {
    background: none;
}

.login .login_xl:hover p {
    top: 29px;
    opacity: 1;
    visibility: visible;
    z-index: 10000000;
}

.login .login_xl i {
    position: absolute;
    width: 10px;
    height: 8px;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);
    top: -8px;
    left: 50%;
    margin-left: -5px;
}

/*ÃƒÂ¥Ã‹â€ Ã¢â‚¬Â¡ÃƒÂ¦Ã‚ÂÃ‚Â¢ÃƒÂ¥Ã…Â¸Ã…Â½ÃƒÂ¥Ã‚Â¸Ã¢â‚¬Å¡*/
.city {
    float: left;
    margin: 0;
    color: #333;
    background: url(https://static.fangxiaoer.com/web/images/sy/index_new/indexCitySan.png) no-repeat right;
    margin-top: 13px;
    font-size: 15px;
    /* padding-left: 36px; */
    width: 50px;
    background-size: 22px;
    padding-right: 4px;
}

.city_tc {
    width: 352px;
    background: #FFF;
    padding: 9px;
    color: #666;
    border-radius: 5px;
    border: 1px solid #ccc;
    position: absolute;
    left: -110px;
    display: none;
    z-index: 10000000;
}

.city p {
    color: #000;
    border-bottom: 1px solid #e8e8e8;
    padding-left: 10px;
    margin-top: -1px;
    padding-bottom: 3px;
    float: none !important;
    line-height: 31px !important;
    font-size: 14px !important;
}

.city i {
    width: 100px;
    height: 5px;
    display: block;
    background: url("https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png") no-repeat bottom;
    position: absolute;
    left: 96px;
    top: -35px !important;
    padding-top: 30px !important;
}

.city_tc ul {
    padding-left: 10px;
    margin-top: 3px;
    overflow: hidden;
}

.city_tc li {
    float: left;
    margin-right: 30px !important;
    line-height: 32px !important;
    padding-right: 0;
}

.city_tc li a {
    color: #666;
    text-decoration: none
}

.city_tc li a:hover {
    color: #ff5200;
}

.city:hover .city_tc {
    display: block
}

.qieH {
    width: 315px;
    background: #fff;
    padding: 19px 9px 9px 9px;
    color: #666;
    border: 1px solid #ddd;
    border-radius: 5px;
    z-index: 10000000;
    position: absolute;
}

.qieH p {
    color: #000;
    border-bottom: 1px solid #e8e8e8;
    padding-left: 10px;
    margin-top: -8px;
    padding-bottom: 3px;
}

.qieH ul {
    padding-left: 10px;
    margin-top: 3px;
    overflow: hidden;
}

.qieH ul li {
    float: left;
    margin-right: 30px;
}

.qieH ul li a {
    color: #666;
    text-decoration: none
}

.qieH ul li a:hover {
    color: #ff5200;
}

.city .jianjiao {
    top: 31px;
}

.city:hover .jianjiao {
    display: block;
}

.shouye_city:hover .jianjiao {
    display: block;
}

/*ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¤Ã‚ÂºÃ‚Â§ ÃƒÂ¥Ã‚Â¿Ã‚Â«ÃƒÂ¨Ã‚Â®Ã‚Â¯*/
.sy_fczx {
    overflow: hidden;
    padding-left: 15px;
}

.sy_fczx>dl {
    overflow: hidden;
}

.sy_fczx>dl dt {
    font-size: 26px;
    font-weight: bold;
    line-height: 38px;
    padding-top: 32px;
    color: #555;
    overflow: hidden;
}

.sy_fczx a {
    width: auto;
    height: auto;
    display: inline;
    margin: 0;
    float: none;
    color: #333;
}

.sy_fczx dl dt img {
    margin-right: 20px;
    width: 127px;
    height: 40px;
    float: left;
}

.sy_fczx>dl dd {
    width: 50%;
    float: left;
    line-height: 50px;
    text-align: center;
}

.sy_fczx dl dd i {
    display: inline-block;
    width: 11px;
    height: 11px;
    background: #ff5200;
    border-radius: 102px;
    margin-right: 10px;
    margin-top: 6px;
}

.sy_fczx .showfczx {
    margin-bottom: 20px;
    overflow: hidden;
    padding-top: 18px;
}

.sy_fczx .showfczx>img {
    float: left;
    width: 140px;
    height: 103px;
}

.sy_fczx .showfczx>dl {
    float: left;
    width: 435px;
    padding-left: 20px;
}

.sy_fczx .showfczx>dl dt {
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 15px;
}

.sy_fczx .showfczx>dl dd {
    color: #999;
}

.sy_fczx>ul {
    width: 290px;
    float: left;
}

.sy_fczx>ul li {
    line-height: 32px;
    border: 0;
    font-size: 14px;
    color: #333;
}

.close_fastival {
    top: -18px !important
}

/* ÃƒÂ¤Ã‚Â¸Ã¢â€šÂ¬ÃƒÂ©Ã¢â‚¬ÂÃ‚Â®ÃƒÂ¨Ã‚Â®Ã‚Â¢ÃƒÂ©Ã‹Å“Ã¢â‚¬Â¦ÃƒÂ¥Ã‚ÂÃ…Â½ÃƒÂ©Ã‚Â¢Ã¢â‚¬Å¾ÃƒÂ§Ã‚ÂºÃ‚Â¦ÃƒÂ¥Ã‚Â¸Ã‚Â¦ÃƒÂ§Ã…â€œÃ¢â‚¬Â¹ÃƒÂ¦Ã‹â€ Ã‚ÂÃƒÂ¥Ã…Â Ã…Â¸ÃƒÂ¥Ã‚Â¼Ã‚Â¹ÃƒÂ¥Ã¢â‚¬Â¡Ã‚ÂºÃƒÂ¦Ã‚Â¡Ã¢â‚¬Â  */
.yydk_heibu {
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    height: -webkit-fill-available;
    background-size: cover;
    background-position: center;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}

.yyDk {
    width: 500px;
    height: 250px;
    position: fixed;
    left: 50%;
    top: 50%;
    margin-left: -250px;
    margin-top: -125px;
    z-index: 9999;
}

.yyDk>div {
    background: #fff;
    width: 340px;
    height: 205px;
    border-radius: 4px;
    /* padding: 45px 80px 0 80px; */
}

.yyDk>div .yyDkImg {
    text-align: center;
}

.yyDk>div .yyDkImg>img {}

.yyDk>div p {
    text-align: center;
    margin-top: 15px;
    color: #ff5200;
}

.yyDk>div p+p {
    margin-top: 35px;
    color: #333;
}

.yyDk .yyDk_close {
    background: none;
    text-align: center;
    padding: 0;
    width: 20px;
    height: 20px;
    position: absolute;
    right: 10px;
    top: 10px;
}

.yyDk .yyDk_close img {
    cursor: pointer;
    width: 20px;
    height: 20px;
}

.search {
    width: 820px;
    height: 103px;
    position: absolute;
    z-index: 10;
    left: 50%;
    top: 265px;
    padding-top: 15px;
    margin-left: -410px;
}

.search .searchDwzx {
    width: 128px;
    background: #ff5200;
    padding: 10px 0;
    float: left;
    margin-left: -7px;
    line-height: 40px;
    height: 40px;
    border-left: 1px solid #fea980;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
}

.search .searchDwzx i {
    margin-right: 9px;
}

.search .searchDwzx i img {
    margin-top: 8px;
    margin-left: 12px;
}

.search .searchDwzx span {
    color: #fff;
}

.shutiao {
    width: 2px !important;
    height: 40px !important;
    position: absolute !important;
    display: block !important;
    background: #fea980 !important;
    left: 690px !important;
    top: 10px !important;
}

/* ÃƒÂ¥Ã‚ÂºÃ¢â‚¬Â¢ÃƒÂ©Ã†â€™Ã‚Â¨ÃƒÂ©Ã¢â€šÂ¬Ã…Â¡ÃƒÂ¦Ã‚Â Ã‚ÂÃƒÂ¥Ã‚Â¹Ã‚Â¿ÃƒÂ¥Ã¢â‚¬ËœÃ…Â ÃƒÂ¦Ã¢â‚¬â€Ã‚Â ÃƒÂ¥Ã…Â Ã‚Â¨ÃƒÂ§Ã¢â‚¬ÂÃ‚Â»ÃƒÂ§Ã¢â‚¬Â°Ã‹â€  */
.indexBanBottom {
    position: fixed;
    bottom: 0;
    height: 80px;
    overflow: hidden;
    width: 100%;
    z-index: 99;
}

.indexBanBottom a {}

.indexBanBottom a img {
    width: 100%;
}

.indexBanBottom .indexBanBottom_close {
    position: fixed;
    right: 5px;
    bottom: 78px;
    height: 20px;
    width: 52px;
}

.indexBanBottom .indexBanBottom_close img {}

.popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
    z-index: 1000000;
    /* display: none; */
}

.popup a {
    position: fixed;
    top: 50%;
    left: 50%;
    margin-top: -200px;
    margin-left: -400px;
    z-index: 99;
}

.popup a img {}

.popupClose {
    z-index: 99999999999999999999;
    position: fixed;
    right: 10%;
    cursor: pointer;
    top: 10%;
}

/* ÃƒÂ¦Ã‚Â¶Ã‹â€ ÃƒÂ¦Ã‚ÂÃ‚Â¯ÃƒÂ¦Ã‚ÂÃ‚ÂÃƒÂ§Ã‚Â¤Ã‚Âº */
.MessageHints {
    position: absolute;
    background: #fff;
    padding: 10px;
    width: 300px;
    left: -106px;
    top: 40px;
    border: 1px solid #e1e1e1;
    border-radius: 2px;
}

.MessageHints:after {
    content: " ";
    font-weight: bold;
    position: absolute;
    width: 10px;
    height: 8px;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);
    top: -8px;
    left: 80%;
    margin-left: -5px;
}

.MessageHints h4 {
    font-size: 16px;
}

.MessageHints ul {}

.MessageHints ul li {
    line-height: 25px;
    overflow: hidden;
}

.MessageHints ul li a {
    color: #000;
    display: flex;
}

.MessageHints ul li a span i {
    width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    float: left;
}

.MessageHints ul li a s {
    color: #ff5200;
    float: left;
}

/* ÃƒÂ©Ã‚Â¦Ã¢â‚¬â€œÃƒÂ©Ã‚Â¡Ã‚ÂµÃƒÂ©Ã¢â€šÂ¬Ã…Â¡ÃƒÂ¦Ã‚Â Ã‚ÂÃƒÂ¥Ã‚Â¹Ã‚Â¿ÃƒÂ¥Ã¢â‚¬ËœÃ…Â -ÃƒÂ¤Ã‚Â¾Ã‚Â¿ÃƒÂ¦Ã‚Â°Ã¢â‚¬ËœÃƒÂ¦Ã…â€œÃ‚ÂÃƒÂ¥Ã…Â Ã‚Â¡ */
.ConvenientService {
    width: 944px;
    margin: 0 auto;
    padding-left: 226px;
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientServiceBg2.jpg) top center no-repeat;
    height: 80px;
    overflow: hidden;
    margin-bottom: 20px;
}

.ConvenientService ul {
    overflow: hidden;
    margin-left: 25px;
}

.ConvenientService ul li {
    /* display:  none; */
}

.ConvenientService ul li+li {
    display: block;
}

.ConvenientService ul li a {
    float: left;
    display: block;
    margin-right: 63px;
}

.ConvenientService ul li:nth-child(3) {
    display: none !important
}

.ConvenientService ul li a i {
    display: block;
    width: 47px;
    height: 36px;
    margin: 10px auto;
}

.ConvenientService ul li a s {
    color: #fb5539;
}

.ConvenientService ul li a:hover s {
    color: #ff8127
}

.ConvenientService ul li a i.cSIcon1 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientService2.png) 0px 0px no-repeat;
}

.ConvenientService ul li a i.cSIcon2 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientService2.png) -47px 0px no-repeat;
}

.ConvenientService ul li a i.cSIcon3 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientService2.png) -94px 0px no-repeat;
}

.ConvenientService ul li a i.cSIcon4 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientService2.png) -141px 0 no-repeat;
}

.ConvenientService ul li a i.cSIcon5 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientService2.png) -188px 0 no-repeat;
}

.ConvenientService ul li a i.cSIcon6 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientService2.png) -235px 0 no-repeat;
}

.ConvenientService ul li a i.cSIcon7 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientService2.png) -282px 0 no-repeat;
}

.ConvenientService ul li a:hover i.cSIcon1 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientServiceHover2.png) 0px 0px no-repeat;
}

.ConvenientService ul li a:hover i.cSIcon2 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientServiceHover2.png) -47px 0 no-repeat;
}

.ConvenientService ul li a:hover i.cSIcon3 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientServiceHover2.png) -94px 0 no-repeat;
}

.ConvenientService ul li a:hover i.cSIcon4 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientServiceHover2.png) -141px 0 no-repeat;
}

.ConvenientService ul li a:hover i.cSIcon5 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientServiceHover2.png) -188px 0 no-repeat;
}

.ConvenientService ul li a:hover i.cSIcon6 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientServiceHover2.png) -235px 0 no-repeat;
}

.ConvenientService ul li a:hover i.cSIcon7 {
    background: url(https://static.fangxiaoer.com/web/images/ico/ConvenientServiceHover2.png) -282px 0 no-repeat;
}

.index-slide {
    width: 270px;
    height: 190px;
    overflow: hidden;
}

/*index_nav*/
.clearfix {
    clear: both
}

.zhaofang .index_content .Admission ul li img {
    display: block
}

.headTOP h1 {
    float: left;
    padding-top: 10px;
}

.head_nav ul li {
    float: left;
    margin-right: 26px;
}

.head_nav ul li a {
    display: block;
    font-size: 16px !important;
}

.head_nav ul li a:first-child {
    line-height: 60px;
}

.head_nav ul li p a span {
    font-size: 14px !important;
}

.head_right_login .register {
    margin-left: 10px;
    color: #333;
}

.city {
    line-height: 60px;
    margin-top: 0px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/address.png");
    background-position: 68px center !important;
    background-size: 10px;
    font-size: 16px !important;
}

.headTOP {
    position: fixed;
    left: 0;
    top: -0px;
    width: 100%;
    z-index: 1000;
    background-color: #fff
}

.headTOP h1 img {
    width: 108px;
    height: 26px;
    padding-top: 8px
}

.head_nav {
    float: left
}

.headTOP .login a {
    display: block;
    line-height: 34px;
    color: #333;
}

.headTOP .login {
    float: left;
    margin-top: 13px;
    margin-left: 5px;
}

.headTOP .login span {
    padding-right: 5px
}

.headTOP .login p {
    width: 108px;
    background-color: #fff;
    position: absolute;
    left: -39px;
    top: 52px;
    text-align: center;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.19);
    opacity: 0;
    transition: all 0.6s;
    z-index: 0;
    visibility: hidden;
}

.headTOP .login p i {
    position: absolute;
    width: 10px;
    height: 8px;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);
    top: -8px;
    left: 50%;
    margin-left: -5px;
}

.headTOP {
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.26);
}

/*.headTOP .login a:hover {
    color: #f24d00;
}*/

/*.headTOP .login:hover p {
    opacity: 1;
    position: absolute;
    left: -39px;
    top: 42px;
    visibility: visible;
}*/

.headTOP .head_right_login {
    float: right;
    padding-top: 0px;
    line-height: 60px;
    position: relative;
}

.headTOP .head_right_login em {
    display: inline-block;
    max-width: 77px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left;
    margin-right: 8px;
    margin-left: 8px;
}

.headTOP .head_right_login b {
    margin-top: 27px;
    display: initial;
}

.headTOP .head_right_login .recode {
    line-height: 60px;
    float: left;
    background: url(https://static.fangxiaoer.com/global/imgs/index/recodePhone02.png) no-repeat 0px 21px;
    padding-left: 20px;
    position: relative;
    color: #333;
}

.headTOP .head_right_login .recode span {
    color: #b8b8b8 !important;
}

.headTOP .head_right_login .shuxian {
    color: #b8b8b8 !important;
}

.headTOP .head_right_login .recode div {
    position: absolute;
    left: -15px;
    top: 42px;
    width: 133px;
    height: 147px;
    background: url(https://static.fangxiaoer.com/global/imgs/index/recode.png) no-repeat;
    opacity: 0;
    transition: all 0.6s;
    z-index: 0;
    visibility: hidden;
}

.headTOP .head_right_login .recode:hover div {
    opacity: 1;
    position: absolute;
    left: -15px;
    top: 30px;
    visibility: visible;
}

.headTOP .head_right_login .recode div img {
    margin: 0 auto;
    display: block;
    padding-top: 35px
}

.headTOP .login a:first-child {
    margin-right: 9px
}

.w1170 {
    margin-top: 0px
}

.head_nav {
    margin-top: 0px
}

.head_nav ul li {
    position: relative
}

.head_nav ul li:nth-child(10) p {
    position: absolute;
    left: -19px;
    top: 70px;
}

.head_nav ul li:nth-child(10):hover p {
    opacity: 1;
    position: absolute;
    left: -19px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li p {
    width: 103px;
    position: absolute;
    left: -30px;
    top: 70px;
    text-align: center;
    background-color: #fff;
    z-index: 0;
    box-shadow: 1px 1.732px 4px 2px rgba(127, 126, 126, 0.3);
    /*padding-bottom: 10px;*/
    opacity: 0;
    transition: all 0.6s;
    visibility: hidden;
}

.head_nav ul li:nth-child(8) p {
    position: absolute;
    left: -37px;
    top: 70px;
}

.head_nav ul li:nth-child(8):hover p {
    opacity: 1;
    position: absolute;
    left: -37px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li:nth-child(9) p {
    position: absolute;
    left: -38px;
    top: 70px;
}

.head_nav ul li:nth-child(9):hover p {
    opacity: 1;
    position: absolute;
    left: -38px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li:nth-child(7) p {
    position: absolute;
    left: -36px;
    top: 70px;
}

.head_nav ul li:nth-child(7):hover p {
    opacity: 1;
    position: absolute;
    left: -36px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li:nth-child(6) p {
    position: absolute;
    left: -29px;
    top: 70px;
}

.head_nav ul li:nth-child(6):hover p {
    opacity: 1;
    position: absolute;
    left: -29px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li:nth-child(4) p {
    position: absolute;
    left: -35px;
    top: 70px;
}

.head_nav ul li:nth-child(4):hover p {
    opacity: 1;
    position: absolute;
    left: -35px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li:nth-child(5) p {
    position: absolute;
    left: -12px;
    top: 70px;
}

.head_nav ul li:nth-child(5):hover p {
    opacity: 1;
    position: absolute;
    left: -12px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li:nth-child(3) p {
    position: absolute;
    left: -29px;
    top: 70px;
}

.head_nav ul li:nth-child(3):hover p {
    opacity: 1;
    position: absolute;
    left: -29px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li:nth-child(2) p {
    position: absolute;
    left: -36px;
    top: 70px;
}

.head_nav ul li:nth-child(2):hover p {
    opacity: 1;
    position: absolute;
    left: -36px;
    top: 60px;
    z-index: 22;
    visibility: visible;
}

.head_nav ul li p i {
    position: absolute;
    width: 10px;
    height: 8px;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);
    top: -8px;
    left: 50%;
    margin-left: -5px;
}

.head_nav ul li p a {
    font-size: 16px;
    /*margin-bottom: 10px;*/
    line-height: 36px;
}
.head_nav ul li p a:hover{ color: #fff !important; background-color: #f24d00 !important;}

.navigation ul a:nth-child(2n) li {
    margin-right: 0
}

.navigation ul li {
    margin-right: 14px;
}

.navigation .box h2 {
    font-size: 20px;
    width: auto;
}

.navigation {
    background-color: #f5f5f5
}

.hot_red {
    color: #f24d00
}

.zhaofang .index_title .xegz {
    float: right;
    width: auto
}

.zhaofang .index_content {
    margin-top: 20px
}

.zhaofang .index_content .type {
    width: 218px;
    float: left
}

.zhaofang .index_content .type ul li {
    cursor: pointer;
    position: relative;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: 170px center;
}

.zhaofang .index_content .type ul li:hover {
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow02.png");
}

.zhaofang .index_content .type ul li i {
    display: block;
    float: left;
    margin-left: 8px;
    margin-right: 12px
}

.zhaofang .index_content .type ul li .type_title {
    font-size: 14px;
    color: #666666;
    display: block;
    float: left;
    line-height: 44px
}

.zhaofang .index_content .type ul li:nth-child(1) i {
    width: 16px;
    height: 14px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/new.png");
    margin-top: 15px
}

.zhaofang .index_content .type ul li:nth-child(2) i {
    width: 17px;
    height: 15px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/Second.png");
    margin-top: 15px
}

.zhaofang .index_content .type ul li:nth-child(3) i {
    width: 16px;
    height: 14px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/lease.png");
    margin-top: 15px
}

.zhaofang .index_content .type ul li:nth-child(4) i {
    width: 15px;
    height: 14px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/shops.png");
    margin-top: 15px;
}

.zhaofang .index_content .type ul li:nth-child(5) i {
    width: 16px;
    height: 14px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/Building.png");
    margin-top: 15px
}

.zhaofang .index_content .type ul li:hover {
    box-shadow: 0px 0px 7px 2px rgba(142, 142, 142, 0.3);
}

.zhaofang .index_content .type ul li .layer {
    width: 953px;
    height: 220px;
    position: absolute;
    left: 217px;
;
    top: 0;
    box-shadow: 0px 0px 7px 2px rgba(142, 142, 142, 0.3);
    background-color: #fff;
    display: none;
    z-index: 99
}

.zhaofang .index_content .type ul li .patch {
    width: 30px;
    height: 44px;
    position: absolute;
    right: -11px;
    top: 0px;
    z-index: 555;
    background-color: #fff;
    display: none
}

.zhaofang .index_content .type ul li:hover .layer {
    display: block
}

.zhaofang .index_content .type ul li:hover .patch {
    display: block
}

.zhaofang .index_content .type ul li:nth-child(2) .layer {
    position: absolute;
    left: 217px;
;
    top: -44px;
}

.zhaofang .index_content .type ul li:nth-child(3) .layer {
    position: absolute;
    left: 217px;
;
    top: -88px;
}

.zhaofang .index_content .type ul li:nth-child(4) .layer {
    position: absolute;
    left: 217px;
;
    top: -132px;
}

.zhaofang .index_content .type ul li:nth-child(5) .layer {
    position: absolute;
    left: 217px;
;
    top: -176px;
}

.zhaofang .index_content .type ul li:hover .type_title {
    color: #ff6100;
}

.zhaofang .index_content .type ul li:nth-child(1):hover i {
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/new02.png");
}

.zhaofang .index_content .type ul li:nth-child(2):hover i {
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/Second02.png");
}

.zhaofang .index_content .type ul li:nth-child(3):hover i {
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/lease02.png");
}

.zhaofang .index_content .type ul li:nth-child(4):hover i {
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/shops02.png");
}

.zhaofang .index_content .type ul li:nth-child(5):hover i {
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/Building02.png");
}

.zhaofang .index_content .type ul li .layer .region {
    padding-top: 22px
}

.zhaofang .index_content .type ul li .layer .region h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .region a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .region a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .type ul li .layer .Price {
    padding-top: 26px
}

.zhaofang .index_content .type ul li .layer .Price h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .Price a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .Price a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .type ul li .layer .Huxing {
    padding-top: 26px
}

.zhaofang .index_content .type ul li .layer .Huxing h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .Huxing a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .Huxing a:last-child {
    border: 0
}

.zhaofang .index_content .type ul li .layer .Huxing a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .type ul li .layer .type_sun {
    padding-top: 26px
}

.zhaofang .index_content .type ul li .layer .type_sun h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .type_sun a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .type_sun a:last-child {
    border: 0
}

.zhaofang .index_content .type ul li .layer .type_sun a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .type ul li .layer .characteristic {
    padding-top: 26px
}

.zhaofang .index_content .type ul li .layer .characteristic h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .characteristic a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .characteristic a:last-child {
    border: 0
}

.zhaofang .index_content .type ul li .layer .characteristic a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .type ul li .layer .measure {
    padding-top: 26px
}

.zhaofang .index_content .type ul li .layer .measure h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .measure a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .measure a:last-child {
    border: 0
}

.zhaofang .index_content .type ul li .layer .measure a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .type ul li .layer .Mode {
    padding-top: 26px
}

.zhaofang .index_content .type ul li .layer .Mode h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .Mode a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .Mode a:last-child {
    border: 0
}

.zhaofang .index_content .type ul li .layer .Mode a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .type ul li .layer .supply {
    padding-top: 26px
}

.zhaofang .index_content .type ul li .layer .supply h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .supply a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .supply a:last-child {
    border: 0
}

.zhaofang .index_content .type ul li .layer .supply a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .type ul li .layer .type_sun02 {
    padding-top: 26px
}

.zhaofang .index_content .type ul li .layer .type_sun02 h1 {
    display: block;
    float: left;
    margin-left: 18px;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    padding-right: 13px;
    background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png");
    background-repeat: no-repeat;
    background-position: right center;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .type_sun02 a {
    display: inline-block;
    float: left;
    padding-left: 8px;
    padding-right: 8px;
    border-right: #ebebeb solid 1px;
    line-height: 14px
}

.zhaofang .index_content .type ul li .layer .type_sun02 a:last-child {
    border: 0
}

.zhaofang .index_content .type ul li .layer .type_sun02 a:nth-last-of-type(1) {
    border: 0
}

.zhaofang .index_content .Housekeeper {
    float: right;
    width: 270px;
    height: 210px;
}

.zhaofang .index_content .Housekeeper img {
    width: 100%;
    height: 100%;
}

.zhaofang .index_content .Admission {
    width: 659px;
    float: left
}

.zhaofang .index_content .Admission ul li {
    width: 210px;
    height: 160px;
    border: 1px solid #ebebeb;
    border-radius: 5px;
    float: left;
    margin-right: 11px;
    cursor: pointer
}

.zhaofang .index_content .Admission ul li .name {
    margin-top: 19px;
    position: relative;
    transition: All 0.4s ease-in-out;
    -webkit-transition: All 0.4s ease-in-out;
    -moz-transition: All 0.4s ease-in-out;
    -o-transition: All 0.4s ease-in-out;
}

.zhaofang .index_content .Admission ul a:nth-child(3) li {
    margin-right: 0
}

.zhaofang .index_content .Admission ul li .name img {
    width: 47px;
    height: 51px;
    margin: 0 auto
}

.zhaofang .index_content .Admission ul li .name h1 {
    text-align: center;
    font-size: 18px;
    color: #333333;
    font-weight: normal;
    margin-top: 8px
}

.zhaofang .index_content .Admission ul li .name h1 span {
    font-weight: bold
}

.zhaofang .index_content .Admission ul li:hover .name {
    transform: translate(0, -10px);
    -webkit-transform: translate(0, -10px);
    -moz-transform: translate(0, -10px);
    -o-transform: translate(0, -10px);
    -ms-transform: translate(0, -10px);
}

.zhaofang .index_content .Admission ul li .text {
    margin-top: 3px;
}

.zhaofang .index_content .Admission ul li .text h2 {
    font-size: 14px;
    color: #999999;
    font-weight: normal;
}

.zhaofang .index_content .Admission ul li .text h3 {
    font-size: 14px;
    color: #999999;
    font-weight: normal;
    margin-top: -6px;
}

.zhaofang .index_content .Admission .phone #userPhoneNum {
    width: 446px;
    height: 40px;
    background-color: #f0f0f0;
    border: 0;
    display: block;
    float: left;
    border-radius: 3px;
    padding-left: 24px;
    font-size: 14px;
}

.zhaofang .index_content .Admission .phone #userPhoneNum::-webkit-input-placeholder {
    color: #999999;
}

input::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #999999;
}

input:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #999999;
}

input:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #999999;
}

.zhaofang .index_content .Admission .phone #submitPhoneNum {
    width: 170px;
    height: 40px;
    background-color: #ff6100;
    border: 0;
    font-size: 14px;
    color: #f5f5f5;
    display: block;
    float: right;
    border-radius: 3px;
    cursor: pointer;
    font-weight: bold;
}

.zhaofang .index_content .Admission .phone {
    margin-top: 10px
}

.zhaofang .index_content .Admission ul a:nth-child(1) .text {
    width: 98px;
    margin: 0 auto;
    margin-top: 3px;
    padding-left: 13px;
}

.zhaofang .index_content .Admission ul a:nth-child(2) h2 {
    text-align: center
}

.zhaofang .index_content .Admission ul a:nth-child(2) h3 {
    text-align: center
}

.zhaofang .index_content .Admission ul a:nth-child(3) .text {
    width: 84px;
    margin: 0 auto;
    margin-top: 3px;
    padding-left: 13px;
}

.Advertisement {
    margin-top: 20px;
    margin-bottom: 20px;
}

.bnzfTC_heibu {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999
}

.bnzfTC {
    height: 100%;
    position: relative
}

.bnzfTC_box {
    width: 500px;
    height: 300px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -250px;
    margin-top: -150px;
    background-color: #fff;
    position: relative;
    border-radius: 7px
}

.bnzfTC_box .Verification {
    font-size: 14px;
    color: #333333;
    width: 410px;
    margin: 0 auto
}

.bnzfTCSendCode {
    font-size: 12px;
    color: #999999;
    width: 124px;
    height: 30px;
    border: 1px solid #ebebeb;
    display: inline-block;
    line-height: 30px;
    text-align: center;
    float: right;
    border-radius: 5px;
}

.bnzfTC_box .Verification {
    padding-top: 49px;
}

.bnzfTC_box .Verification input {
    width: 78px;
    font-size: 12px;
    border: 0
}

#bnzfTCCode {
    width: 402px;
    display: block;
    margin: 0 auto;
    margin-top: 10px;
    height: 42px;
    padding-left: 8px;
    border: 1px solid #ebebeb;
    border-radius: 5px;
    font-size: 14px;
    color: #999;
}

#bnzfTCCode::-webkit-input-placeholder {
    color: #999999;
}

input::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #999999;
}

input:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #999999;
}

input:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #999999;
}

.bnzfTC_btn {
    width: 412px;
    height: 42px;
    margin: 0 auto;
    background-color: #ff5200;
    border-radius: 5px;
    margin-top: 12px
}

.bnzfTC_btn a {
    line-height: 42px;
    display: block;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
}

.phone_sun {
    width: 412px;
    margin: 0 auto;
    margin-top: 6px;
}

.kbTc_txt1 {
    display: block;
    float: left;
    color: #ff5200
}

.kbTc_txt2 {
    display: block;
    float: right;
    color: #2a95fe;
    cursor: pointer;
}

.bnzfTC_close {
    position: absolute;
    top: 20px;
    right: 16px;
    cursor: pointer;
}

.bnzfTC_form .Prompt {
    width: 412px;
    margin: 0 auto;
    height: 30px
}

.bnzfTC_form .Prompt span {
    font-size: 12px;
    color: #e60012;
    line-height: 30px
}

.bnzfTCss_heibu {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    left: 0;
    top: 0;
    z-index: 999;
    position: fixed;
    left: 0;
    top: 0
}

.bd {
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/select.png) no-repeat;
    background-size: 100% 100%;
}

.bnzfTC_box .Agreement i {
    display: block;
    width: 20px;
    height: 20px;
    border: 1px solid #ededed;
    display: block;
    cursor: pointer;
    float: left;
    margin-left: 43px;
    margin-right: 7px
}

.bnzfTC_box .Agreement input {
    display: none
}

.bnzfTC_box .Agreement h1 {
    font-size: 14px;
    color: #333333;
    font-weight: normal;
}

.bnzfTC_box .Agreement h1 a {
    color: #ff5200
}

.bnzfTC_box .Agreement h1 a:hover {
    text-decoration: underline;
}

.bnzfTCss {
    width: 420px;
    height: 224px;
    background-color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -210px;
    margin-top: -112px;
    border-radius: 7px
}

.bnzfTCss_close img {
    display: none;
}

.bnzfTCss .yyDkImg img {
    margin: 0 auto;
    display: block;
    padding-top: 49px;
}

.bnzfTCss p {
    font-size: 14px;
    color: #666666;
    text-align: center;
    margin-top: 40px
}

.bnzfTCss #guide_result {
    font-size: 16px;
    color: #ff5200;
    margin-top: 14px;
}

.zhaofang .index_content .type ul li:nth-child(2) .layer .Price {
    padding-top: 36px
}

.zhaofang .index_content .type ul li:nth-child(2) .layer .measure {
    padding-top: 36px
}

.zhaofang .index_content .type ul li:nth-child(2) .layer .Huxing {
    padding-top: 36px
}

.zhaofang .index_content .type ul li:nth-child(3) .layer .Price {
    padding-top: 36px
}

.zhaofang .index_content .type ul li:nth-child(3) .layer .Mode {
    padding-top: 36px
}

.zhaofang .index_content .type ul li:nth-child(3) .layer .Huxing {
    padding-top: 36px
}

.window_sun {
    position: fixed;
    top: 48%;
    left: 50%;
    padding-left: 40px;
    padding-right: 40px;
    margin: 0 30%;
    background: url("https://static.fangxiaoer.com/global/imgs/ico/b60.png");
    color: #fff;
    font-size: 11pt;
    text-align: center;
    border-radius: 6px;
    padding: 10px;
    z-index: 1000000;
    transform: translate(-50%, -50%)
}

.fxe_validateCode {
    width: 124px;
    border: 1px solid #ebebeb;
    display: inline-block;
    line-height: 30px;
    text-align: center;
    font-weight: normal;
    font-size: 12px;
    margin-left: 5px;
    border-radius: 3px;
    cursor: pointer;
}

/*NewIndex1*/

.head_nav ul li a {
    display: block;
    font-size: 14px;
    color: #333;
}

.head_nav ul li a:hover {
    color: #ff5200
}

.hot_red {
    color: #ff5200 !important
}

.w1170 {
    margin-bottom: 0 !important;
    padding-bottom: 0px !important;
}

.head_nav ul li p a {
    font-size: 14px
}

.city {
    background-image: url(https://static.fangxiaoer.com/web/images/sy/index_new/indexCitySan.png);
    background-position: 64px center;
    background-size: 8px;
    font-size: 14px;
    width: 76px;
}

.navigation {
    background-color: #fff;
    padding-top: 25px;
    padding-bottom: 22px;
    border-bottom: 0;
}

.navigation h2 {
    float: none;
    text-align: left;
    height: auto;
    line-height: normal;
}

.navigation h2 a {
    color: #333333;
    font-weight: bold
}

.navigation .box {
    border-right: 0;
    position: relative;
    padding-bottom: 5px;
}

.navigation .box:nth-child(1) h2 {
    padding-left: 45px
}

.navigation .box:nth-child(2) h2 {
    padding-left: 60px
}

.navigation .box:nth-child(3) h2 {
    padding-left: 49px
}

.navigation .box:nth-child(4) h2 {
    padding-left: 52px
}

.navigation .box:nth-child(5) h2 {
    padding-left: 37px
}

.navigation .box:nth-child(6) h2 {
    padding-left: 37px
}

.navigation .box ul {
    margin-top: 5px;
}

.navigation .box .line {
    width: 0px;
    height: 57px;
    border-right: 1px dashed #dddddd;
    position: absolute;
    right: 0;
    top: 15px
}

.index_block .index_title {
    border: 0;
    background-color: #f3f3f3;
    height: 50px;
}

.mxf_h1 a {
    font-size: 22px;
    color: #333333;
    font-weight: normal;
    margin-left: 7px;
}

.index_title li.hover {
    background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/index_li_bor1.png) !important;
    background-color: #f3f3f3 !important;
}

.index_title li.hover a {
    /* background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/index_li_bor.png) !important; */
    color: #333333;
    font-weight: bold;
}

.index_title li {
    /* background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/index_li_bor.png) !important; */
    background-position: right center;
}

.index_title li span {
    background: none !important;
}

.index_title li a {
    line-height: 46px;
    background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/index_li_bor.png) !important;
    background-position: left center;
    background-repeat: no-repeat;
    color: #333;
}

.bztj_middle .swiper-wrapper a {
    margin: 0;
    width: 428px !important;
    height: 196px !important;
    background-size: 100% 100%;
}

.new_left h1 {
    float: left;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
    margin-right: 16px;
    /* font-size: 12px; */
    height: 12px;
    line-height: 12px;
}

.new_left a {
    /* border-right: 1px solid #e6e6e6; */
    line-height: 12px;
    margin-right: 0;
    padding-left: 0px;
    padding-right: 0;
    /* margin-bottom: 11px; */
    color: #666666;
    font-size: 12px;
    vertical-align: top;
    margin-right: 12px;
    /* margin-top: 14px; */
}

.new_left a:hover {
    color: #ff5200
}

.index_screen_line {
    margin-right: 14px !important;
    border-right: 1px solid #e6e6e6;
    padding-right: 14px !important;
}

.bztj_middle #con_mxfqh_1 a big {
    color: #ff5200;
    font-size: 14px;
}

.bztj_middle #con_mxfqh_2 a big {
    color: #ff6200
}

.bztj_middle #con_mxfqh_3 a big {
    color: #ff6200
}

.bztj_middle #con_mxfqh_4 a big {
    color: #ff6200
}

.bztj_middle #con_mxfqh_5 a big {
    color: #ff6200
}

.bztj_middle .bztj_price {
    margin-top: 11px;
    margin-bottom: 3px
}

.bztj_right .Headline img {
    display: block
}

.bztj_right .Headline a {
    font-size: 20px;
    color: #333333;
    font-weight: bold;
    height: 24px;
    overflow: hidden;
    display: block;
    margin-top: 27px !important;
    text-align: center;
}

.bztj_left {
    /* margin-left: 12px; */
    margin-right: 22px;
    margin-top: 20px;
}

.index_title_right {
    padding-right: 15px;
    margin-top: 14px;
    color: #333;
}

.dkjsq_icon {
    background-image: url("https://static.fangxiaoer.com/web/images/indexReversion/Calculator.png");
    width: 15px;
    height: 16px;
    background-position: 0 0;
    margin-top: 5px;
}

.dynamic {
    margin-top: 22px;
    margin-bottom: 7px;
}

.dynamic .more {
    float: right;
}

.dynamic .name {
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    border-left: 3px solid #333333;
    padding-left: 8px;
    display: inline-block;
    height: 16px;
    line-height: 16px;
}

.mxf_ul .bztj_li {
    padding-left: 10px;
    color: #333;
}

.mxf_ul .bztj_li:hover {
    color: #ff5200
}

.dynamic .index_ul {
    border-top: 1px dashed #ebebeb;
    margin-top: 9px;
}

.dynamic .index_ul li {
    line-height: 14px;
    margin-top: 14px
}

.vido .name {
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    border-left: 3px solid #333333;
    padding-left: 8px;
    display: inline-block;
    line-height: 16px;
}

.bztj_right h2 {
    border-bottom: 1px dashed #ebebeb;
    margin-bottom: 12px;
}

.vido h2 {
    border-bottom: 1px dashed #ebebeb !important;
    margin-bottom: 12px;
}

.vido .new_video {
    margin-top: 14px
}

.index_h1 .name {
    font-size: 22px;
    display: block;
    float: left;
    font-weight: normal;
}

.bztj_h1 {
    width: 44px;
    margin-left: 14px;
    margin-right: 0px;
}

.index_dzyh {
    margin-top: 19px;
    margin-left: 10px;
    color: #333;
}

.buy {
    float: right;
    margin-right: 12px
}

.buy a {
    line-height: 50px;
    font-size: 14px;
    color: #333333;
    background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/buy.png);
    background-repeat: no-repeat;
    padding-left: 24px;
    background-position: left center;
}

.swiper-container {
    margin-top: 0 !important;
    height: 196px;
}

.bztj_left h2 a {
    margin-top: 0px;
    font-size: 14px;
}

.bztj_middle .pic_wei {
    margin-top: 17px
}

.bztj_middle .pic_wei:nth-child(1) {
    margin-top: 0px
}

.bztj_middle .pic_wei:nth-child(2) {
    margin-top: 0px
}

.bztj_middle .pic_wei:nth-child(3) {
    margin-top: 0px
}

.bztj_right {
    margin-top: 0 !important;
    overflow: hidden;
    /* margin-bottom: 20px; */
}

.bztj_right a {
    margin-top: 0 !important;
    margin-bottom: 0px !important;
    font-size: 14px;
    color: #666;
}

.swiper-container a {
    margin-top: 0 !important;
}

.esf_right img {
    margin-top: 0
}

.excellentAgent {
    background-color: #ebebeb;
    height: 164px !important;
    margin-top: 48px !important;
}

.excellentAgent .header {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    margin: 0 auto;
    display: block;
    margin-top: -36px;
}

.excellentAgent .bztj_title {
    position: static;
    background: none;
    text-align: center;
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    height: 16px;
    line-height: 16px;
    margin-top: 11px;
}

.fcphb s {
    border-radius: 50%;
    width: 15px;
    height: 15px;
    line-height: 15px;
    background-color: #ff5200;
}

.jjr {
    width: 83px !important;
    height: 25px !important;
    margin: 0 auto;
    position: relative;
    margin-top: -9px;
}

.ye h1 {
    /* text-align:center; */
    font-size: 14px;
    margin-top: 13px;
    line-height: 14px;
    color: #333;
    font-weight: normal;
    margin-left: 44px;
}

.qu h1 {
    /* text-align:center; */
    font-size: 14px;
    line-height: 14px;
    margin-top: 6px;
    color: #333;
    font-weight: normal;
    margin-left: 44px;
}

.index_h1 a {
    font-size: 22px !important;
    color: #333 !important;
    margin-left: 15px;
    font-weight: normal;
}

.mesf_h1 {
    width: 81px !important;
}

.zf_h1 {
    width: 59px !important
}

.fcphb .index_h1 span {
    font-size: 22px;
    font-weight: normal;
    margin-left: 15px;
    display: block;
    float: left;
}

.fcphb .index_h1 a {
    font-size: 12px !important;
    font-weight: normal;
    display: block;
    float: left;
    cursor: pointer;
}

.fcphb .index_h1 a i {
    background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/sh.png);
    background-position: 0;
    width: 15px;
    height: 14px;
    display: block;
    margin-top: 5px;
    float: left;
    background-repeat: no-repeat;
}

.fcphb .index_h1 a label {
    display: block;
    float: left;
    margin-left: 8px
}

.new_wei {
    margin-bottom: 20px;
    height: 50px;
}

.bg-bannerK {
    z-index: 0
}

.footer {
    margin-top: 0 !important;
}

.index_screen_line_ts {
    border-right: 1px solid #e9e9e9;
    padding-right: 15px !important;
    margin-right: 9px !important;
}

.new_left {
    border-bottom: 1px dashed #e9e9e9;
    padding-top: 20px;
}

.new_left:nth-child(1) {
    padding-top: 0px
}

.esf_right h2 {
    margin-top: 10px;
}

.fcphb {
    margin-top: 20px !important;
}

.esf_left .new_left {
    padding-bottom: 8px;
}

.mai_wei {
    background: url(https://static.fangxiaoer.com/web/images/indexReversion/sell.png) no-repeat;
    background-position: 0;
    margin-top: 2px;
}

.zhu_wei {
    background: url(https://static.fangxiaoer.com/web/images/indexReversion/lease.png) no-repeat;
    background-position: 0;
    margin-top: 2px;
}

.lou_wei {
    background: url(https://static.fangxiaoer.com/web/images/indexReversion/lou.png) no-repeat;
    background-position: 0;
    margin-top: 2px;
}

.sh {
    background: url(https://static.fangxiaoer.com/web/images/indexReversion/sh.png) no-repeat;
    background-position: 0;
}

.di {
    background: url(https://static.fangxiaoer.com/web/images/indexReversion/di.png) no-repeat;
    background-position: 0;
    width: 13px;
    height: 15px
}

.index_title li:nth-child(1) a {
    background: none;
    background-repeat: no-repeat;
    background-position: -15px;
}

.fcphb .index_h1 a label {
    cursor: pointer
}

.fcphb .index_h1 a:hover label {
    color: #ff6200
}

.zhufang .new_left {
    padding-bottom: 9px;
    margin-top: 0px;
}

.footer_copy a {
    color: #999
}

.index_title li {
    background: none !important
}

.cff5200 {
    color: #ff5200;
    line-height: 14px;
    margin-top: 10px;
}

.esf_layout {
    color: #333;
    /* font-size: 14px; */
    margin: 14px 0 10px;
    line-height: 15px;
    position: relative;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bztj_middle a {
    height: 196px;
    margin: 17px 20px 0 0;
}

.index_h1 {
    margin-top: 14px;
    margin-right: 0;
    width: auto !important;
    font-size: 22px;
    font-weight: normal;
}

.scroll li span {
    color: #333
}

.bztj_left li span {
    color: #666;
}

.bztj_left li .rw {
    display: inline-block;
    text-align: right;
    float: right;
    margin-right: 14px;
}

.bztj_left li a {
    color: #333;
}

.scroll li p i.price {
    color: #666;
    display: inline-block;
}

.fcphb li {
    height: 15px
}

.fcphb ul {
    border-right: 1px dashed #e6e6e6
}

.bztj_left h2:nth-child(1) a {
    margin-bottom: 2px;
}

.link {
    padding-top: 20px
}

.w1170 {
    padding-bottom: 1px !important;
}

.navigation h2 a:hover {
    color: #ff5200
}

.no_banner {
    width: 100%;
    height: 20px
}

.rmxq ul {
    border-right: 0
}

.n1 a:hover {
    color: #ff5200
}

.esf_right h2 {
    /* border-bottom:0 */
}

.news_w {
    margin-top: 5px
}

.navigation .box ul a {
    color: #333
}

.bztj_right h2:nth-child(1) {
    border: 0
}

.bztj_middle .bztj_price span big {
    color: #ff5200;
    font-size: 14px;
}

.esf_layout i big {
    font-size: 14px;
}

.navigation ul li:hover {
    color: #ff5200
}

.bztj_right h2 a.more:hover {
    color: #ff5200 !important
}

.buy a:hover {
    color: #ff5200
}

.fcphb a:hover {
    color: #ff5200 !important
}

.rplp ul li p {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.zxzx ul li p {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.rplp ul li p {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.more:hover {
    color: #ff5200
}

#wei:hover {
    color: #ff5200;
}

.Headline a:hover {
    color: #ff5200
}

.bztj_left ul li a:hover {
    color: #ff5200
}

.shangpu a {
    margin-right: 0
}

.shangpu .new_left {
    border-bottom: 0
}

.shangpu .new_left a {
    /* margin-top: 18px !important; */
}

.shangpu .new_left {
    padding: 0px !important;
    line-height: normal;
}

.shangpu .new_left:nth-child(2) {
    /* margin-top:20px */
}

.shangpu .new_left:nth-child(3) {
    /* margin-top:20px; */
}

.shangpu .new_left:nth-child(4) {
    /* margin-top: 10px; */
    padding-top: 20px !important;
    border-top: 1px dashed #e9e9e9;
}

.shangpu .new_left:nth-child(5) {
    /* margin-top:20px */
}

.shangpu .new_left:nth-child(6) {
    /* margin-top:20px */
}

.shangpu .new_left:nth-child(1) {
    width: 147px;
}

.shangpu .new_left:nth-child(1) a:nth-of-type(1) {
    margin-top: 0 !important;
}

.shangpu .new_left:nth-child(2) a:nth-of-type(1) {
    margin-top: 0 !important
}

.shangpu .new_left:nth-child(2) a:nth-of-type(2) {
    margin-top: 0 !important
}

.shangpu .new_left:nth-child(3) a:nth-of-type(1) {
    margin-top: 0 !important
}

.shangpu .new_left:nth-child(3) a:nth-of-type(2) {
    margin-top: 0 !important
}

.shangpu .new_left:nth-child(4) a:nth-of-type(1) {
    margin-top: 0 !important
}

.shangpu .new_left:nth-child(4) a:nth-of-type(2) {
    margin-top: 0 !important
}

.shangpu .new_left:nth-child(5) a:nth-of-type(1) {
    margin-top: 0 !important
}

.shangpu .new_left:nth-child(6) a:nth-of-type(1) {
    margin-top: 0 !important;
    margin-bottom: 0;
}

.shangpu .new_left:nth-child(6) a:nth-of-type(2) {
    margin-top: 0 !important;
    margin-bottom: 0;
}

.ershouf .new_left:nth-child(6) a:nth-of-type(1) {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.ershouf .new_left:nth-child(6) a:nth-of-type(2) {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.soi {
    background: url(https://static.fangxiaoer.com/web/images/indexReversion/shopSelectIcon.png);
    width: 15px;
    height: 15px
}

.soi_wei {
    background: url(https://static.fangxiaoer.com/web/images/indexReversion/shopPublishIcon.png);
    background-repeat: no-repeat;
    background-position: center;
    margin-top: 3px;
}

.zf_right {
    margin-top: 17px !important;
    margin-bottom: 20px;
}

.zf_right img {
    margin-top: 0
}

.new_house .new_left {
    border: 0;
    padding: 0;
    margin-bottom: 3px;
}

.new_house .new_left a {
    display: block;
    float: left;
    margin-bottom: 14px;
}
.g_oa a{
    display: block;
    float: left;
    margin-bottom: 15px !important;
}
.new_gbor{ border-bottom: 1px dashed #e9e9e9 !important;}

.new_house .new_left .order {
    margin-right: 14px;
    padding-right: 14px
}

.ershoufang .new_left a {
    display: block;
    float: left;
    margin-bottom: 15px
}

.shangpu .new_left a {
    display: block;
    float: left;
    margin-bottom: 19px;
}

.new_house .new_left:nth-child(5) a:nth-child(7) {
    margin-bottom: 0
}

.new_house .new_left:nth-child(5) a:nth-child(8) {
    margin-bottom: 0
}

.new_house .new_left:nth-child(5) a:nth-child(9) {
    margin-bottom: 0
}

.middle {
    margin-top: 0 !important;
    margin-bottom: 0px !important;
    margin-bottom: 3px !important;
}

.tlBannerLast {
    margin-bottom: 20px !important
}

.yh_right {
    margin-bottom: 20px
}

.xzl_right {
    margin-top: 17px !important;
}

#qdelay2 {
    background-color: rgba(0, 0, 0, 0.6);
    width: 633px;
    padding-bottom: 15px;
    margin-left: -316px;
    height: auto;
    border-radius: 5px;
    top: 285px;
    padding-bottom: 8px;
}

.bannerLoop span {
    margin-right: -596px;
    top: 372px;
    border-radius: 0;
    opacity: 1;
}

.index_search_input {
    width: 536px;
    height: 46px;
    border-radius: 3px 0 0 3px;
    font-size: 15px;
}

.index_search_btn {
    width: 56px;
    height: 46px;
    border-radius: 0 3px 3px 0;
}

.search_div {
    width: auto;
    margin-left: 16px
}

.search li {
    margin: 0;
    height: 42px
}

.search li a {
    display: block;
    line-height: 31px;
    padding-left: 13px;
    padding-right: 13px;
    text-shadow: none;
    font-size: 14px;
    border-radius: 3px;
    font-weight: normal;
    color: #fff;
}

.search .hover a {
    background-color: #ff5200;
    font-weight: normal;
    font-size: 14px;
    border-radius: 3px;
}

.search ul {
    margin-left: 16px
}

.search li.hover {
    background: url(https://static.fangxiaoer.com/web/images/ico/Choice.png) bottom no-repeat;
}

#qdelay2 input::-webkit-input-placeholder {

    color: #aab2bd;

}

.ui-autocomplete {
    /* display: block !important; */
}

.high_light_span {
    line-height: 22px;
    display: inline-block;
    border: 1px solid #ededed !important;
    background-color: #fff !important;
    margin-left: 4px;
}

.ui-menu .ui-menu-item {
    padding: 0 !important;
    border: 0 !important;
    line-height: 30px;
    /* padding-left: 12px !important; */
    /* padding-right: 12px !important; */
}

.ui-menu .ui-menu-item a {
    line-height: 30px !important;
    padding: 0;
    padding: 0px .4em !important;
}

.ui-menu {
    padding: 0
}

.ui-state-focus {
    border: 1px solid #ededed !important;
    background: #ededed !important;
    font-weight: normal;
    color: #2b2b2b
}

.ui-autocomplete {
    /* padding-right: 12px !important; */
    /* padding-left: 12px !important; */
    width: 520px;
}

.ui-autocomplete {
    width: 544px !important;
    border: 1px solid #ededed !important;
    box-shadow: 0px 2px 21px rgba(0, 0, 0, 0.1);
}

.high_light_span02 {
    padding: 0 5px;
    border-radius: 4px;
    line-height: 22px;
    display: inline-block;
    border: 1px solid #eee !important;
    background: #fff;
    margin-left: 6px
}

.ui-autocomplete {
    padding: 0 !important;
    border-radius: 0 !important;
    padding-top: 6px !important;
    padding-bottom: 6px !important;
}

.ui-corner-all {
    border-radius: 3px 0 0 3px !important;
}

.ui-corner-top {
    border-radius: 3px 0 0 3px !important
}

.tlBanner {
    margin-top: 0px !important
}

.ershoufang .new_left:nth-child(4) a:nth-of-type(1) {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.ershoufang .new_left:nth-child(4) a:nth-of-type(2) {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

.geng_d {
    background: url(https://static.fangxiaoer.com/web/images/img/listGD.png) no-repeat !important;
    background-position: 0;
    margin-top: 5px;
    margin-right: 0px;
}

.listBuilding {
    overflow: hidden;
    margin-top: 18px;
}

.listBuilding li {
    float: left;
    padding-top: 2px;
}

.listBuilding li:first-child .listB {
    width: 344px;
    height: 222px;
}

.liFirst {
    width: 344px;
    position: relative;
}

.listN {
    position: absolute;
    z-index: 9;
    left: 10px;
    top: 0px;
}

.listB {
    /* position: relative; */
    /* width: 345px; */
    width: 254px;
    height: 188px;
}

.listB2:hover {
    transition: all 0.6s;
    transform: scale(1.1);
}

.liFbg {
    width: 344px;
    height: 222px;
    position: absolute;
    top: 0;
    opacity: 0;
    /* background-color: #00000091; */
    transition: all 600ms ease;
    background-color: #00000069;
    transition: all 600ms ease;
}

.liFbg:hover {
    opacity: 1;
}

.liFbg2 {
    width: 254px;
    height: 188px;
    opacity: 0;
    /* background-color: #00000091; */
    transition: all 600ms ease;
}

.liFbg2:hover {
    background-color: #00000091;
    opacity: 1;
}

.liFbg h2 {
    font-size: 14px;
    color: #ffffff;
    font-weight: bold;
    position: absolute;
    top: -29px;
    left: 35px;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.liFbg:hover h2 {
    top: 46px;
}

.liFbg2 h2 {
    top: -20px;
    left: 22px;
    z-index: 99;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.liFbg2:hover h2 {
    top: 46px;
    z-index: 99;
}

.liFbg img {
    position: absolute;
    left: 35px;
    top: -2px;
    z-index: 99;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.liFbg:hover img {
    top: 73px;
}

.liFbg2 img {
    top: -3px;
    left: 22px;
    z-index: 99;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.liFbg2:hover img {
    top: 73px;
}

.liFbg p {
    font-size: 14px;
    color: #ffffff;
    width: 270px;
    position: absolute;
    top: 225px;
    left: 35px;
    text-align: justify;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -ms-inline-flexbox;
    display: -ms-flexbox;
    box-orient: vertical;
    line-clamp: 4;
    -o---box-orient: vertical;
    -o--line-clamp: 4;
    -moz-box-orient: vertical;
    -moz-line-clamp: 4;
    -ms-box-orient: vertical;
    -ms-line-clamp: 4;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    z-index: 99;
    height: 80px;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
    text-overflow: ellipsis;
}

.liFbg:hover p {
    top: 85px;
}

.liFbg2 p {
    width: 210px;
    top: 185px;
    left: 22px;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}

.liFbg2:hover p {
    top: 85px;
}

.liFbt {
    background-color: #00000082;
    width: 344px;
    overflow: hidden;
    height: 34px;
    line-height: 34px;
    position: absolute;
    top: 190px;
}

.liFbt2 {
    background-color: #ffffff;
    color: #000000;
    width: 254px;
    top: 192px;
}

.liFbt h2 {
    font-size: 14px;
    color: #ffffff;
    font-weight: bold;
    float: left;
    margin-left: 5px;
    width: 137px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.liFbt2 h2 {
    color: #333333;
    width: 137px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.liFbt span {
    font-size: 14px;
    color: #ffffff;
    float: right;
    margin-right: 5px;
}

.liFbt2 span {
    color: #333333;
}

.liFbt i {
    font-size: 16px;
    color: #ffffff;
    font-weight: bold;
}

.liFbt2 i {
    color: #ff5200;
    letter-spacing: 0px;
}

.liFposi {
    position: relative;
}

.liFposi2 {
    overflow: hidden;

}

.listR {
    margin-left: 21px;
    position: relative;
}

.pinpaiG {
    overflow: hidden;
    background: url("https://static.fangxiaoer.com/web/images/img/pinpaiBG.png") no-repeat;
    margin-bottom: 30px;
    vertical-align: middle;
}

.pinpaiG li {
    float: left;
    height: 90px;
    line-height: 90px;
    display: table-cell;
    text-align: center;
}

.pinpaiG li:first-child {
    margin-right: 14px;
}

.pinpaiG li img {
    width: 100px;
    height: 58px;
    padding: 0 8px;
    vertical-align: middle;
    /* transform: scale(1.1); */
    box-shadow: 0px 0px 10px 0px rgba(156, 152, 152, 0.16);
    margin-left: 12px;
    background-color: #fff;
}

.pinpaiG li:first-child img {
    width: 200px;
    height: 90px;
    margin-left: 0;
    padding: 0;
    box-shadow: 0px 0px 10px 0px rgba(156, 152, 152, 0);
    margin-right: 7px;
    background-color: none !important;
    background: none;
}

.pinpaiG span {
    float: right;
    width: 30px;
    display: block;
    height: 47px;
    background-color: #dfdfdf;
    font-size: 14px;
    color: #666666;
    vertical-align: middle;
    line-height: 19px;
    letter-spacing: 2px;
    margin-top: 16px;
    padding-top: 11px;
    cursor: pointer;
}

.pinpaiG span a {
    width: 14px;
    display: inline-block;
    margin-left: 8px;
    width: 14px;
}

.pinpaiG span:hover {
    color: #ff5200;
}

.pinpaiH img:hover {
    transition: all 0.6s;
    transform: scale(1.1);
    box-shadow: 0px 0px 10px 0px rgba(104, 104, 104, 0.17);
}

/*in page*/
#qdelay2 {
    top: 275px !important;
}

.w1170 {
    width: 1170px;
    margin: 0 auto;
    background: #fff;
    padding: 5px;
    margin-top: 95px;
    position: relative;
    padding-bottom: 20px;
    margin-bottom: -46px;
}

.bg-banner {
    display: block;
    margin: 0;
    padding: 0;
    height: 780px;
    position: fixed;
    width: 100%;
    top: 0;
    cursor: pointer;
}

.close_fastival {
    width: 54px;
    height: 18px;
    display: inline-block;
    overflow: hidden;
    background: url(https://static.fangxiaoer.com/web/images/ico/logo/bgBanner-close.png) no-repeat;
    position: absolute;
    right: -2px;
    top: -20px;
    z-index: 2;
    cursor: pointer;
}

.bztj_right>a {
    margin-bottom: 8px;
    display: block;

}

.bztj_right {
    margin-top: 8px;
}

.tlBanner {
    margin: 20px auto;
    width: 1170px;
    position: relative;
    height: 80px;
    overflow: hidden
}

.tlBanner img {
    width: 100%
}

.sy_fczx>ul li {
    line-height: 26px
}

.quanwei {
    margin-top: 0 !important;
}

body>div:nth-child(2) {
    height: auto !important;
}

.swiper-container-horizontal>.swiper-pagination-bullets,
.swiper-pagination-custom,
.swiper-pagination-fraction {
    text-align: right;
}

.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: -4px 3.5px;
}

.swiper-pagination-bullet {
    background: #fff;
    opacity: 0.9;
}

.swiper-pagination-bullet-active {
    background: #ff5200;
}

/*ÃƒÂ©Ã‚Â¦Ã¢â‚¬â€œÃƒÂ©Ã‚Â¡Ã‚ÂµÃƒÂ¨Ã‚Â½Ã‚Â®ÃƒÂ¦Ã¢â‚¬â„¢Ã‚Â­ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ÃƒÂ¥Ã‹â€ Ã¢â‚¬Â ÃƒÂ©Ã‚Â¡Ã‚ÂµÃƒÂ¥Ã¢â€žÂ¢Ã‚Â¨ÃƒÂ¦Ã…â€™Ã¢â‚¬Â°ÃƒÂ©Ã¢â‚¬â„¢Ã‚Â®ÃƒÂ¥Ã‚Â®Ã…Â¡ÃƒÂ¤Ã‚Â½Ã‚Â*/
.ZLB {
    left: -12px !important;
    bottom: 10px !important;
    display: block !important;
}

.ESF {
    left: -12px !important;
    bottom: 0px !important
}

.ZF {
    position: relative;
    left: -11px !important;
    bottom: 32px !important;
}

.SPXZL {
    position: relative;
    top: -32px;
    left: -12px !important;
}

/*ÃƒÂ©Ã‚Â¦Ã¢â‚¬â€œÃƒÂ©Ã‚Â¡Ã‚ÂµÃƒÂ¨Ã‚Â½Ã‚Â®ÃƒÂ¦Ã¢â‚¬â„¢Ã‚Â­ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ÃƒÂ¥Ã‹â€ Ã¢â‚¬Â ÃƒÂ©Ã‚Â¡Ã‚ÂµÃƒÂ¥Ã¢â€žÂ¢Ã‚Â¨ÃƒÂ¦Ã…â€™Ã¢â‚¬Â°ÃƒÂ©Ã¢â‚¬â„¢Ã‚Â®ÃƒÂ¥Ã‚Â®Ã…Â¡ÃƒÂ¤Ã‚Â½Ã‚Â*/

.new_video img {
    height: 188px;
    margin-top: 21px;
    width: 100%;
}

.bannerLoop .swiper-container {
    width: 100%;
    height: 100%;
}

.bannerLoop .swiper-slide {
    background-position: center;
    background-size: cover;
}

.fxe_banner {
    z-index: 9;
}

.swiper-pagination {
    width: max-content;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    bottom: 11px;
}

.swiper-pagination-white .swiper-pagination-bullet {
    width: 10px !important;
    height: 10px;
    background: rgba(255, 255, 255, 0.9) !important;
    margin-right: 10px !important;
    border-radius: 50%;
    padding: 0 !important;
    float: left;
    position: relative;
    right: 0;
    top: 0;
}

.swiper-pagination span:last-child {
    margin-right: 0 !important;
}

.swiper-pagination-white .swiper-pagination-bullet-active {
    background: #ff5200 !important;
}

.bannerLoop .swiper-slide span {
    top: 362px !important;
}

.uuipezy {
    top: 362px !important;
}

.advertising {
    width: 100%;
    height: 45px;
}

.advertising a img {
    width: 100%;
    height: 100%;
}

.bztj_middle a:hover .houeseText {
    color: #333;
}

.houeseText {}

.houeseText i {
    float: right;
}

.houeseText i big {
    font-weight: bold;
    color: #ff5200
}

.dynamic {
    margin-top: 0px
}

.bztj_right .Headline a {
    margin-top: 0 !important;
    line-height: 70px;
    height: auto !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bztj_right .Headline .show {
    height: 70px;
    overflow: hidden
}

.bztj_right .Headline .show .lunbo {
    position: relative
}

.saleAdsSwiper {
    width: 270px;
    height: 190px;
}

.rentAdsSwiper {
    width: 270px;
    height: 410px;
}

.shopAdsSwiper {
    width: 270px;
    height: 410px;
}

/*ÃƒÂ¤Ã‚Â¿Ã‚Â®ÃƒÂ¦Ã¢â‚¬ÂÃ‚Â¹vrÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ÃƒÂ¦Ã‚Â Ã¢â‚¬Â¡*/
.hide {
    display: none !important;
}

.vr_ls {
    width: 22px;
    height: 22px;
    position: absolute;
    left: 3px;
    bottom: 36px;
    background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_detail.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 0;
}

.vrb0 {
    bottom: 3px !important;
}

.vrn {
    width: 22px;
    height: 22px;
    position: absolute;
    left: 3px;
    bottom: 3px;
    background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_detail.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 0;
}

.vrn1 {
    bottom: unset !important;
    top: 84px !important;
}

.vrs {
    width: 22px;
    height: 22px;
    position: absolute;
    left: 3px;
    top: 111px;
    background-image: url('https://static.fangxiaoer.com/web/images/ico/sign/vr_detail.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 0;
}

/*2022Ã¥Â¹Â´4Ã¦Å“Ë†20Ã¦â€”Â¥ Ã©Â¦â€“Ã©Â¡ÂµbannerÃ¥â€ºÂ¾Ã¨Â½Â®Ã¦â€™Â­Ã¤Â¿Â®Ã¦â€Â¹ */
.bannerLoop .swiper-pagination {
    background: rgb(0 0 0 / 40%);
    padding: 3px 10px;
    border-radius: 10px;
}

.new_banner_gif {
    /* display:none */
}

/* .carousel-inner:hover .new_banner_gif{display:block} */
.new_banner_gif div{
    position: absolute;
    display: block;
    overflow: hidden;
    line-height: 999px;
    cursor: pointer;
    top: 175px;
    width: 51px;
    height: 60px;
    z-index: 99;
}
.new_banner_gif div a{
    width: 36px;
    height: 60px;
    display: block;
    float: right;
}
.new_banner_gif .prev{
    left: -80px;
    -webkit-transition: left 0.3s;
    -moz-transition: left 0.3s;
    -ms-transition: left 0.3s;
    -o-transition: left 0.3s;
    transition: left 0.3s;
}
.new_banner_gif .prev a {

    background: url(https://static.fangxiaoer.com/global/imgs/ico/banner_qh0.png) no-repeat;
    background-position: 0 0;
    display: block;
}

.new_banner_gif .next{
    -webkit-transition: right 0.3s;
    -moz-transition: right 0.3s;
    -ms-transition: right 0.3s;
    -o-transition: right 0.3s;
    transition: right 0.3s;
    right: -80px;
}
.new_banner_gif .next a {

    background: url(https://static.fangxiaoer.com/global/imgs/ico/banner_qh0.png) no-repeat;
    background-position: -36px 0;
    float: left;
}

.new_banner_gif .next:hover {
}

.carousel-inner:hover .prev {
    left: 0;
    background-position: 0 0;
}

.carousel-inner:hover .next {
    right: 0;
}

.new_banner_gif .prev:hover a {
    left: 0;
    background: url(https://static.fangxiaoer.com/global/imgs/ico/banner_qh1.png) no-repeat;
    background-position: 0 0;
}

.new_banner_gif .next:hover a {
    background: url(https://static.fangxiaoer.com/global/imgs/ico/banner_qh1.png) no-repeat;
    background-position: -36px 0;
}
/* 2023å¹´4æœˆ17æ—¥ é¦–é¡µæˆ¿äº§è§†é¢‘æ”¹ç‰ˆ */
.new_video_ul{overflow: hidden;margin: 20px 0;}
.new_video_ul .new_video_li{overflow: hidden;float: left;margin-right: 19px;position: relative;}
.new_video_li a{display: block;width: 278px;}
.new_video_li img{    width: 278px;height: 156px;}
.new_video_li h5{width: 100%;line-height: 20px;margin-top: 12px;font-size: 14px;font-family: Microsoft YaHei-Regular, Microsoft YaHei;font-weight: 400;color: #111111;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;}
.new_video_ul li:last-child{margin-right: 0}
.new_video_time{position: absolute;left: 6px;top: 132px;height: 18px;background: rgb(0 0 0 / 50%);border-radius: 6px 6px 6px 6px;padding: 0 6px;line-height: 18px;}
.new_video_time i{background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/new_video_time.png);background-size: 100%;display: block;float: left;width: 14px;height: 14px;margin-top: 2px;margin-right: 3px;}
.new_video_time span{font-size: 14px;font-family: Microsoft YaHei-Regular, Microsoft YaHei;font-weight: 400;color: #FFFFFF;line-height: 18px;}

.gthx em{
    width: 115px;
    overflow: hidden;
    display: inline-block;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.gthx i{
    width: 87px;
    overflow: hidden;
    display: inline-block;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: right;
}

/* 2023å¹´9æœˆ13æ—¥ ç›´æ’­çœ‹æˆ¿ é¦–é¡µç›¸å…³ä¿®æ”¹*/
.liveHuo{
    display: block;
    width: 18px;
    height: 22px;
    background: url(https://static.fangxiaoer.com/images/coin/liveIndex.png) top center;
    background-size: 100% 100%;
    float: right;
    margin-left: 3px;
}
.index_title .indexLiveBtn{overflow: hidden;float: left;margin-left: 15px;position: initial;}
.index_title .indexLiveBtn li{
    /* width: 88px; */
    font-size: 22px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #333333;
    line-height: 47px;
    float: left;
    margin-right: 73px;
    cursor: context-menu;
    text-align: left;
    margin-left: 0;
}
.index_title .indexLiveBtn li a{
    display: block;
    padding: 0;
}
.index_title .indexLiveBtn li.hover a,.index_title .indexLiveBtn li:hover a{
    border-bottom: 3px solid #ff5200;
    color: #FF5200;
    font-weight: bold;
}
.index_title .indexLiveBtn li.hover a{

}
.new_live_ul{
    overflow: hidden;
    margin: 20px 0;
}
.new_live_li{
    width: 278px;
    float: left;
    overflow: hidden;
    float: left;
    margin-right: 19px;
    position: relative;
}
.new_live_ul li:last-child {
    margin-right: 0;
}
.new_live_li a{}
.new_live_li img{
    width: 278px;
    height: 156px;
}
.new_live_type{
    position: absolute;
    left: 11px;
    top: 10px;
    color: #fff;
}
.new_live_type i{
    width: 51px;
    height: 23px;
    background: #FF3B00;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 23px;
    display: inline-block;
    text-align: center;
    float: left;
}
.new_live_type span{
    height: 23px;
    background: rgb(0 0 0 / 60%);
    border-radius: 0px 0px 0px 0px;
    display: inline-block;
    padding: 0 9px;
    float: left;
}
.new_live_txt{
    width: 100%;
    line-height: 20px;
    margin-top: 12px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #111111;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.live_type{}
.live_type1{
    background: #FF3B00;
}
.live_type2{

    background: #FF7F00;
}

.liveTxt{
    position: absolute;
    left: 0;
    top: 2px;
    width: 55px;
    height: 16px;
    background: #FF5200;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    text-align: center;
    color: #FFFFFF;
    line-height: 16px;
}

/* 房产视频 直播看房改版 */
.index_title_w50{
    border: 0;
    height: 50px;
}
.index_title_w50 li{
    border: 0;
    background-color: #f3f3f3;
    height: 50px;
    width: 99%;
    float: left;
    line-height: 50px;
    color: #303030;
    text-align: left;
    font-style: normal;
    text-transform: none;
    font-size: 22px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    padding-left: 14px;
}
.index_title_w50 li+li{
    float: right;
}

.index_title_w50 li a{
    font-size: 14px;
    line-height: 24px;
}
.new_live_ul_50{width: 50%;float: left}
.new_live_ul2_50{width: 100%;float: right;}
.video_show .new_live_ul2_50 li{margin-right: 17px;}
.video_show .new_live_ul2_50 li:last-child{margin-right: 0px;}
.video_show{overflow: hidden}
