.share_icon{position: absolute;left: 686px;top: 218px;z-index: 999;}
.share_tan{
    cursor: pointer;
    /* border: 1px solid #333; */
    width: 60px;
    border-radius: 20px;
    text-align: right;
    padding: 0px 5px;
}
.share_tan:hover{color: #ff5200;}
#qrcode {
    margin-left: 30px;
}
.share_tan_main{
    text-align: center;
    padding: 10px;
    margin: -36px 0 0 32px;
    background: #fff;
    border-radius: 10px;
    border: 1px solid #eee;
    width: 150px;
    font-size: 14px;
    margin-left: 84px;
    overflow: hidden;
}
.share_tan i{
    display: inline-block;
    width: 15px;
    height: 15px;
    margin: 5px 0px 0 9px;
    float: left;
}
.share_tan i img{
    width: 100%;
}
/* è¯¦ç»†é¡µäºŒç»´ç  */
.photoAlbum{position:relative}
.secondDetailEwm {position:absolute;right:  6px;top: 6px;z-index:  99;background:  #fff;padding: 6px 11px;}
.secondDetailEwm .Ewmclose{display:  block;width: 12px;height: 12px;float:  right;margin-top: -6px;margin-right: -11px;background: #eaeaea;cursor:  pointer;}
.secondDetailEwm .Ewmclose img,.secondDetailEwm .EwmImg canvas{
    width:  100%;
}
.secondDetailEwm .EwmImg{
    width: 76px;
    height: 76px;
    margin:  0 auto;
    display:  block;
    margin-top: 5px;
}
.secondDetailEwm p{
    color:  #ff5200;
    text-align:  center;
    line-height: 10px;
    margin-top: 10px;
}