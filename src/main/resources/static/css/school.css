@charset "utf-8";
/* CSS Document */
.crumbs{margin-top:39px;padding-top: 0;}
.w1210 .w{width:1170px}
#left{width: 880px;float:left;margin-right:20px;border: 1px solid #ededed;margin-top: 12px;}
#right{width:250px;float:left;}
.listHeader{
    height: 60px;
    line-height: 60px;
    background: #fafafc;
    padding-left: 20px;
    color: #1d364c;
    font-size: 16px;
    border-bottom: 1px solid #ededed;
}
.mt30{margin-top:30px;}
.title{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/title_line.gif) repeat-x bottom;height:44px;margin:0px auto;margin-top: 4px !important;display: none;}
.title p{margin-right:30px;font-size:18px; font-family:"微软雅黑"; color:#999; float:left;width:112px;text-align:center;line-height:40px;}
.title p.hover{border-bottom:4px solid #ff5200}
.title p a{ text-decoration:none}
.title p.hover a{color:#ff5200;}
/*搜索*/
.search{line-height:12px;font-size: 14px;}
.search li{ clear:both; height: 40px; line-height: 40px; border-bottom:1px dashed #ededed;}
.search li p{/*width:60px;*/float:left; color:#999}
.search li a{float:left;padding:0 14px;border-left:1px solid #dfdfdf; line-height:14px; height:14px;margin-top: 13px;}
.search li :nth-child(2){border-left:none;}
.search li a.hover{color:#ff6600}
.search li span{background:#f4f4f6; clear:both;margin:10px 0 20px;float:left;margin-left:60px;padding:8px 0; width:880px;}
.search li span a{line-height:30px; border:none; height:30px;}
/*房源*/
.lp_count{font-size:14px;/* border-bottom:1px solid #ededed; */padding-top: 15px;padding-bottom: 15px;height: 36px;padding:0;}
.lp_count span{color: #ff5200;padding:0 5px;font-size: 14px;}
.lp_con {float:left;padding-top: 15PX;padding-left: 15px;font-size: 12px;color: #999;}
.count{padding-left: 45px;font-size: 14px;border-bottom: 1px solid #ededed;line-height: 50px; background:url(https://static.fangxiaoer.com/web/images/ico/sign/tou.png) no-repeat left;}
.count span{color:#ff6600}
.count p{display:inline;padding:4px 12px; background:#ccc;color:#fff;margin:0 8px;}
.count a{margin-left:8px;color:#fff; text-decoration:none; cursor:pointer}
#mapTab {float:right;margin-top: 9px;font-size: 16px;padding-right: 18px;}
#mapTab li{width: 72px;height: 30px;float:left;line-height: 40px;cursor:pointer;font-size: 14px;}
#mapTab li.hover {color:#ff5200;cursor: text;}
#mapTab li#tabMap {border-right:none;}
#mapTab li i{background: url("https://static.fangxiaoer.com/web/images/ico/map/img_map.png") no-repeat;display:block;width:20px;height:18px;float:left;margin: 11px 11px 0 10px;}
#mapTab li#tabList.hover i {background-position:0px -50px;}
#mapTab li#tabList i {background-position:0px -73px;}
#mapTab li#tabMap.hover i {background-position:0px -24px;}
.house{border-bottom:1px solid #ededed;padding: 16px 0;font-size:14px;height: 167px;padding-left: 18px;}
.house:hover{background: #f7f8f8;}

.house .img{width: 227px;position:relative;float:left;margin-right: 9px;}
.house .img img{width: 210px;height: 154px;margin-top: 7px;}
.house .map{width:205px;height:35px;position:absolute;top:6px;left:-5px;}
.house .map img{width:205px;height:35px;}
.house .info{float:left;position:relative;overflow:visible;width: 351px;line-height: 30px;font-size: 12px;color: #6d798c;margin-top: 3px;}
.house .info_tit{color: #272727;font-size: 22px;/* font-size: -webkit-xxx-large; */font-family:"微软雅黑";font-weight: 600;margin: 0px 0 10px 0;text-decoration:none;}
.house .info_tit:hover{color:#ff5200}
.house .info li{
    font-size: 14px;
    color: #666666;
    overflow: hidden; /*内容超出宽度时隐藏超出部分的内容 */
    text-overflow:ellipsis;/* 当对象内文本溢出时显示省略标记(...) ；需与overflow:hidden;一起使用。*/
    white-space:nowrap; /*不换行 */
    line-height: 39px;
    /* margin-top: 5px; */
}
.house .info li i{
    color: #ff5200;
}
.quyu{clear:both;font-size:18px;color:#333;}
.house .prices{
    float: right;
    width: 282px;
    font-size: 14px;
    line-height: 30px;
    color: #666666;
    margin-top: 10px;
}
.house .prices a{
    /* text-align: right; */
    float: left;
    /* line-height: 50px; */
    color: #333333;
    cursor:  pointer;
    width: 122px;
    display: inline-block;
    text-align: left;
    font-size: 14px;
    margin-right: 0px;
    margin-left: 18px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;";;
}
.house .prices a:hover{
    text-decoration:none;
    color:#ff5200;
}
.house .prices .rightText{
    text-align: right;
    width: 258px;
    margin-top: 8px;
    color: #57a0e3;
    margin-left: 3px;
    margin-top: 28px;
}
.house .prices .rightText:hover{color:#ff5200;text-decoration:none}
.house .prices span{
    text-align: right;
    /* float: right; */
    width: 101px;
    display: inline-block;
    margin-left: 22px;
    /* margin-right: 0px; */
}
.house .prices span b{color: #ff5200;font-size: 20px;font-family: dinot-bold;margin-right: -3px;display: inline-block;}
.school_num i{font-size:28px; color:#ff6400;}
.loupan { height:auto; overflow:visible}
.loupan a{float:left; width:210px; line-height:35px;color:#999;}
.loupan a:hover{color:#900}
.more{ position:absolute;right:0;bottom:0; line-height:normal;margin:0; color:#1f80d9; font-size:14px; display:none}
.zd{color: #596c91;display:inline;margin-right: 6px;/* padding:3px 12px; */line-height: 56px;background: #f3f5f7;line-height: 24px;padding-left: 5px;padding-right: 8px;display: inline-block;}
.house:hover .zd{background: #F3F5F7!important;}
.price{height:37px;}
.price p{width:50px;float:left;font-size:14px;line-height:42px;}
.price ul{float:left;width:auto}
.price li{float:left;width:197px; line-height:34px; font-size:14px;padding:0 10px;}
.price li img{ vertical-align: bottom;margin-bottom:8px;margin-left:8px;}
.price i{font-size:28px; font-family:Arial; color:#ff6600;}
.price em{font-size:28px;color:#ff6600;}
.school_district{font-size:12px;}
.school_district .schdis_title{float:left;background-color:#50c8c8;color:#fff;width:45px;height:25px;text-align:center;display:inline-block;line-height:25px}
.school_district div{float:left;border:1px solid #d2d2d2;line-height:23px}
.school_district div span{margin:0 15px}
.text{line-height:30px;color:#666;margin-top:5px;}

/*右*/
/*私人定制*/
#right h1{font-size:18px;font-weight:normal;/* width:210px; */margin:0 auto;border-bottom:1px solid #ededed;padding: 0px 0 10px;text-align:left;}
#right h1.bidu{margin-top: 11px;}.srdz{border:1px solid #ededed;}
.rmph{margin-top:18px;border:1px solid #ededed;}
.rmph li{width:210px;margin:-1px auto 0; border-top:1px dashed #ededed;padding:15px 0; clear: both; line-height:24px;}
.rmph s{width:20px;height:20px; line-height:20px; text-align:center; font-size:16px; color:#FFF; float:left; background:#ff6600}
.rmph p{float:left; width:170px; margin-left:13px; margin-bottom:5px;}
.rmph i{ color:#ff6600;}
.zxhd{ text-align:center; padding-bottom:20px;}
.zxhd img{margin-top:20px}

.cnxh{width:210px; margin: 10px auto 20px auto;  font-family:"微软雅黑"}
.cnxh_img{width:210px; margin-left:auto;margin-right:auto; position:relative;}
.cnxh img{width:100%; height:154px;}
.cnxh_pos{ position:absolute; bottom:0;left:0;}
.cnxh_bg{ width:100%; height:28px; background-color:#000;filter:alpha(opacity=50); /* IE */ -moz-opacity:0.5; /* Moz + FF */opacity: 0.5; }
.cnxh_title{ width:205px; line-height:28px; color:#fff; font-size:14px; padding-left:5px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.cnxh_m{ margin-top:6px; margin-bottom:6px;}
.cnxh_qian_icon{color:#ff5200; font-size:16px;}
.cnxh_price{font-size:24px; color:#ff5200;}
.cnxh_quyu{ float:right; padding-top:3px; }
.cnxh_title:hover{color:#fff;}

.rigbox {margin-top:45px;}
.rigbox a{ display:block; width:250px; height:200px; background:#f0f0f0; margin-bottom:20px; text-decoration:none; text-align:center; line-height:30px;}
.rigbox img{margin:25px 0}
.rigbox p{font-size:18px; color:#333;margin-top: -20px;}
.rigbox span{font-size:14px; color:#999}

.right-text{padding-top: 25px;width:250px;padding-left:20px;}
.right-text .title{display:block!important;font-size: 18px;color: #333;height: 30px;border-bottom:1px solid  #ededed !important;background:none;margin-bottom: 4px !important;padding-left:

        5px;}
.hot li{

    width: 220px;

    font-size: 14px;

    line-height: 14px;

    color: #666;

    background:url(https://static.fangxiaoer.com/web/images/ico/sign/news_d.gif) no-repeat 0 17px;

    padding-left:15px;

    overflow: hidden;

    text-overflow:ellipsis;

    white-space: nowrap;

    margin-bottom: 23px;
}
.hot li.news_list_tit{font-weight:bold;font-size: 14px;background:none;}
.hot li:hover a{text-decoration:none}
/*楼盘活动*/
.lou_img{width: 212px;position:relative;float:left;margin-left:18px;margin-bottom: 18px;margin-top: 10px;}
.lou img{width:100%; height:154px;}
.lou_img img{width: 212px;}
.lou_img a{color:#fff;}
.lou_pos{ position:absolute; bottom:0;left:0;}
.lou_bg{width:100%; height:28px; background-color:#000;filter:alpha(opacity=50); /* IE */ -moz-opacity:0.5; /* Moz + FF */opacity: 0.5;}
.lou_title{ width:170px; line-height:28px; color:#fff; font-size:14px; padding-left:5px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}

/*右侧列表样式 2017-7-28*/
#right h1{font-size: 18px;color: #333;font-weight: 400;border-bottom: 1px solid #eaeaea;line-height: 43px;margin-bottom: 12px;padding: 0 16px;}
#right h1 span{display: inline-block;width: 4px;height: 18px;margin-bottom: -1px;border-radius: 50px;background: #ff5200;margin-right: 8px;}
.lou_img a{display:block;}
.lou_img{margin:0;width:100%;float: none;height: initial;}
.lou_img img{width:100%}
#right .lou_img h1{border:none;margin-bottom: 0;padding: 0;}
#right .lou_img a.lou_title{color: #fff;position: absolute;background: url(https://static.fangxiaoer.com/web/images/ico/sign/b60.png);width: 100%;text-align: center;bottom: 0;padding: 0;}

#right .rmph{background: #fff;padding-top: 4px;margin: 20px 0;border: 1px solid #eaeaea;}
#right .rmph li{border:none;}
#right .rmph li s{background:none;font-size: 21px;font-family:Georgia;color:#ff5200;}
#right .rmph li span{float:right;}
#right .rmph p{margin-left:0;width: 190px;margin: 0;}
#right .rmph p a{margin-left:10px}
#right .rmph em{display:block;color:#999;margin: 0 0 0 36px;}

#right .cnxh_txt{height: 86px;line-height: 20px;}
#right .cnxh{background: #fff;padding-top: 4px;border: 1px solid #eaeaea;width: initial;margin-top:20px;}
#right .cnxh img{float:left;width:96px;height:70px;margin: 0 11px 0 20px;}
#right .cnxh .cnxh_title{position:static;color:#000;}
#right .cnxh .cnxh_m{float:left;width: 121px;margin: 0;}
#right .cnxh .cnxh_qian_icon{font-size:12px;color:#808080;}
#right .cnxh .cnxh_qian_icon i{font-size:16px;color:#ff5200;}
#right .cnxh  .cntx{float:left;width: 121px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;font-size: 12px;color: #999;}

.recommend{border: 1px solid #eaeaea;margin-top: 20px;background: #fff;/* padding: 0 16px;*/margin-bottom: 20px;position: relative;}
.recommend .title{display:none}
.recommend dt{line-height: 50px;font-size: 18px;font-weight: 400;border-bottom: 1px solid #eaeaea;padding: 0 16px;}
.recommend dt span{display: inline-block;width: 4px;height: 18px;margin-bottom: -1px;border-radius: 50px;background: #ff5200;margin-right: 8px;}
.recommend .hover{}
.unstyled{margin-top:18px}
.recommend dd{overflow: hidden;font-size: 12px;color: #666;height: 36px;line-height: 36px;padding: 0 16px;}
.recommend dd>div a{text-decoration:none;}
.recommend dd>div span{display: inline-block;width: 103px;color: #444;text-decoration: none;text-overflow: ellipsis;white-space: nowrap;}
.recommend dd>div .hover{color:#ff5200;}
.recommend dd>div p{display: inline-block;width: 30px;color: #cecece;font-size: 14px;font-family: Arial;}
.recommend dd>div b{display: inline-block;text-align: right;width: 76px;color: #ff5200;font-weight: 400;}
.recommend dd>a{overflow: hidden;display: block;text-decoration: none;}
.recommend dd>a img{width: 80px;height: 60px;display: block;float: left;margin: 4px 20px 10px 20px;}
.recommend dd>a div{float: left;}
.recommend dd>a div p{line-height: 20px;}
.recommend li{background: none;}

.left_box{float: left;}
.left_box #left{float: none;margin-top: 16px;width: 898px}
.left_box .none_box{ width: 894px; margin: 0 auto;height: 88px;border: 1px solid #f5dcbc}
#right{width:250px}
.lp_count{ height: auto}
.lp_con{ float: none}
.none_box{margin-top: 26px !important;margin-bottom: 20px !important;background-color: #fffbf6}
.school .content{ border: 1px solid #ededed;width: 900px}
.school .title_sun p{font-size: 18px;color: #333333;font-weight: bold;height: 18px;line-height: 18px;margin-top: 26px;margin-bottom: 15px;padding-left: 12px;border-left: 2px solid #333333;}
.list_box{padding-top: 15px;padding-bottom: 15px;border-top: 1px solid #ededed;/* border-bottom: 1px solid #ededed; */padding: 16px 0;font-size: 14px;height: 167px;padding-left: 18px;}
.list_box .img img{ width: 210px;margin-top: 7px}
.list_box .img{float: left;/* margin-left: 15px */}
.list_box .info_tit{color: #57a0e3;font-size: 18px;color: #272727;font-size: 22px;/* font-size: -webkit-xxx-large; */font-family: "微软雅黑";font-weight: 600;margin: 0px 0 10px 0;text-decoration: none;}
.list_box .info{float: left;margin-left: 17px;width:351px;float: left;position: relative;overflow: visible;width: 351px;line-height: 30px;font-size: 12px;color: #6d798c;margin-top: 3px;}
.list_box .info li{font-size: 14px;color: #333;line-height: 37px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;font-size: 14px;color: #666666;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;line-height: 39px;/* margin-top: 5px; */}
.list_box .info li .housesNum{color: #ff5200}
.list_box .prices{float: right;margin-right: 17px;width: 260px;text-align: right;font-size: 12px;float: right;width: 282px;font-size: 14px;line-height: 30px;color: #666666;margin-top: 10px;}
.list_box .prices span b{font-family: Georgia;font-size: 20px;color: #ff5200;color: #ff5200;font-size: 20px;font-family: dinot-bold;margin-right: -3px;display: inline-block;}
.list_box .prices a{float: left;font-size: 14px;text-align: right; */float: left;/* line-height: 50px; */color: #333333;cursor: pointer;width: 122px;display: inline-block;text-align: left;font-size: 14px;margin-right: 0px;margin-left: 37px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.list_box:nth-child(1){border:0}
.list_box:hover{background-color:#f6f6f6}
.none_box{margin: 20px 0;background: #fffbf6;border: 1px solid #f5dcbc;}
.none_box p{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left; font-size:14px; line-height:24px; font-weight:bold;color:#333;padding-left:60px;
    margin-left: 235px;margin-top: 24px}
.none_box a{color:#0085ff}
.none_box a:hover{color:#900}



.Inventory{border: 1px solid #eaeaea;margin-top: 20px;background: #fff;/* padding: 0 16px;*/margin-bottom: 20px;position: relative;padding-bottom: 14px;}
.Inventory .title{display:none}
.Inventory dt{line-height: 50px;font-size: 18px;font-weight: 400;border-bottom: 1px solid #eaeaea;padding: 0 16px;}
.Inventory dt span{display: inline-block;width: 4px;height: 18px;margin-bottom: -1px;border-radius: 50px;background: #ff5200;margin-right: 8px;}
.Inventory .list ul li .photo{width:224px; margin:0 auto;position:relative}
.Inventory .list ul li .photo img{width:100%;height: 146px;}
.Inventory .list{margin-top:16px}
.Inventory .list ul li .photo .name{ width:100%;position:absolute;left:0;
    bottom:0;}
.Inventory .list ul li .photo .name h2{ float:left}
.Inventory .list ul li .photo .name h3{ float:right}
.Inventory .list ul li .photo .name{background-color:rgba(0,0,0,0.5)}
.Inventory .list ul li .photo .name h2{font-size:14px;line-height:30px;color:#fff;
    font-weight:normal;padding-left:12px}
.Inventory .list ul li .photo .name h3{font-size:14px;line-height:30px;color:#fff;
    font-weight:normal;padding-right:12px}
.Inventory .list ul li .metro{width:224px;margin:0 auto;margin-top:10px;border-bottom: 1px solid #eaeaea;padding-bottom: 10px;}
.Inventory .list ul li .metro p{font-size:12px; color:#333}
.Inventory .list ul li .metro p span{padding:4px;border:1px solid #ebebeb;margin-right:12px;font-weight: bold;}
.Inventory .list ul li .Reason{width:224px;margin:0 auto;}
.Inventory .list ul li .Reason h2{font-size:12px;height:12px; line-height:12px;margin-top:10px}
.Inventory .list ul li .Reason p{font-size:12px;color:#333333;line-height: 17px;margin-top: 6px;}
.Inventory .list ul li{margin-top: 17px;}
.Inventory .list ul li:nth-child(1){margin-top:0px}
.Inventory .list ul li .metro p{float:left}
.Inventory .list ul li .metro .list_sun{float:left;width: 154px;}
.metro .list_sun .list_wei{margin:0 !important;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 151px;border: 0;font-size: 12px;line-height: 20px;}
.Along{/* border: 1px solid #eaeaea; */margin-top: 20px;background: #fff;/* padding: 0 16px;*/margin-bottom: 20px;position: relative;/* padding-bottom: 14px; */}
.Along .title{display:none}
.Along dt{line-height: 50px;font-size: 18px;font-weight: 400;border-bottom: 1px solid #eaeaea;padding: 0 16px;}
.Along dt span{display: inline-block;width: 4px;height: 18px;margin-bottom: -1px;border-radius: 50px;background: #ff5200;margin-right: 8px;}
.imgG{
    /* margin-top: 7px; */
    margin-left: 5px;
    vertical-align: middle;
    margin-right: 5px;
    display: inline-block;
}
.Along .photo img{ width:100%;}
.Along .photo{ position:relative}
.Along .photo .look{ position:absolute;right:0; bottom:0;cursor:pointer}
.Along .photo .look{background-color:rgba(0,0,0,0.6);width:66px}
.Along .photo .look h2{ color:#fff;font-weight:normal;
    line-height:22px;text-align:center;}
.Along .btn{width:224px; margin:0 auto;margin-top:13px}
.Along .btn a{display:block;background-color:#ff6100;text-align:center;line-height:36px;color:#fff;font-size:14px;text-decoration: none;}
.label_wei{/* margin-top: 10px; */margin-top: 4px;}

.Along{border: 1px solid #eaeaea;margin-top: 20px;background: #fff;/* padding: 0 16px;*/margin-bottom: 20px;position: relative;padding-bottom: 13px;}
.Along img{width:224px !important;display:block;margin:0 auto;margin-top: 14px;}

.Advertisement .pic img{width: 900px;}
.Advertisement{margin-bottom:20px;width: 900px;margin-top: 20px;}
.swiper-pagination-bullet{border-radius:0 !important;opacity:1 !important;background-color:#775d1e !important;width: 5px !important;height: 2px !important;border-radius: 1px !important;}
.swiper-pagination-bullet-active{background-color:#fff !important;width: 10px !important;}
.swiper-slide{position:relative}
.swiper-slide .pic_sun{position:absolute;right:0;bottom:0;background-color:rgba(0,0,0,0.2)}
@font-face {
    font-family: 'dinot-bold';   /*字体名称*//*/font/*/
    src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');       /*字体源文件*/
}
.daiding{

    display: inline-block !important;

    margin-right: -14px !important;

    /* text-align: right; */

    font-size: 17px !important;

    font-family: Microsoft YaHe !important;

    margin-right: -17px;
}