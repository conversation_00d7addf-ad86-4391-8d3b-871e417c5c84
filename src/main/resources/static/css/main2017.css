@charset "utf-8";
/*
制作人：庄志
修改时间：2017-3-21
用处：全国站通用文件
*/
*{margin:0;padding:0}
body{font-size: 14px;line-height: 24px;font-family: "微软雅黑";color:#333;}
ul,li{list-style: none}
img{border:none;vertical-align: top;}
i,em,s{font-style:normal; text-decoration:none;}
a{text-decoration:none; color:#333;}
a:hover{color:#ff5200; text-decoration: underline;}
.hid{display:none;}
.cl{clear:both; font-size:1px; line-height:0px; height:0px;}
.w{margin:0 auto;width: 1170px !important;}
input{font-family: "微软雅黑";outline: none}

/*头文件*/
#header{background: #333;height:55px;}
#header .logo{width:112px;float:left;margin-top: 15px;}
#header .logo img{width:112px;}
#header .city{float:left;color:#fff;margin: 16px 0 0 15px;}
#header .city span{color:#999;font-size:12px;margin-left:5px;}
#header .nav_ul{width: 450px;float: right;margin-top: 16px;}
#header .nav_ul li{float:left;margin: 0 16px;}
#header .nav_ul li a{color: #ADADAD;font-size:16px;font-weight:bold;}
#header .nav_ul li a.hover{color:#fff}
#header .nav_ul li a:hover{color:#fff}
#header .login{color:#fff;float: right;margin-top: 16px;}
#header .login a{color:#fff;}

/*顶部搜索*/
#search{height:90px;background: #f7f7f7}
#search .search_input{width: 795px;height: 48px;line-height: 48px;border:none;float:left;border-radius: 3px 0 0 3px;padding-left: 10px;font-size: 16px;border: 1px solid #d2d2d2;border-right: none;margin-top: 20px;}
#search .search_btn{border:none;width: 90px;height: 50px;font-size:18px;color:#fff;background:#ff5200;border-radius: 0 3px 3px 0;margin-top: 20px;cursor: pointer;}

/*面包屑*/
.crumbs{color:#999;font-size: 12px;margin: 5px auto 10px !important;width:1170px;}
.crumbs a{color:#999}
.crumbs a:hover{color:#ff5200}

/*列表页筛选*/
#choose li{height:45px;line-height: 45px;border-top:1px solid #e9e9e9;margin-top:-1px;}
#choose li b{color:#333;margin-right: 25px;}
#choose li a{color:#666;margin-right: 20px;}
#choose li span{margin-left:15px}
#choose li a.hover{color:#ff5200;}
#choose li a:hover{color:#ff5200;}
#choose li.dele b{margin-right:15px}
#choose li.dele a{color:#999;font-size:12px;border:1px solid #ffbc89;text-decoration:none;padding:0 22px 0 6px;background:url(../imgs/ico/ico_dele.gif) no-repeat right;margin-left:10px;}
#choose li.dele a.dele_all{color:#666;border:none;background: url(../imgs/ico/ico_dele_all.gif) 3px 3px no-repeat;padding:0 0 0 20px;}
#choose li label input{width:63px;height:23px;border:1px solid #ddd;text-align: center}
#choose label input.btn_search{width: 54px;height: 23px;background: #fff;border: 1px solid #609ee4 !important;color: #609ee4;margin-left: 5px;cursor:pointer}
#choose .select_box{width:96px;height:25px;float: left;margin: 9px 8px 0 8px;position:relative;text-align: left;}
#choose .select_info{background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale_xl.gif) no-repeat;width: 73px;height:25px;line-height:25px;font-size:12px;padding: 0 20px 0 7px;cursor: pointer;}
#choose .select_box ul{position:absolute;background: #fff;width: 94px;border: 1px solid #ccc;top: 23px;display: none;}
#choose .select_box ul li:hover{background:#ff5200;color:#fff}
#choose .select_box:hover ul{display:block;}
#choose li.search_more b{float:left;margin-right: 20px;}
#choose li.search_more li{border:none;margin:0;height: 30px;line-height: 30px;}
#choose li.search_more li:hover a{color:#fff;text-decoration:none}
#choose input{width: 36px;margin: 0 0 0 0;height: 18px;padding: 4px 9px;border: 1px solid #ccc !important;outline: none;text-align: center;border-radius: 4px;}
#choose input.btn_search{width: 54px;height: 27px;background: #fff;border: 1px solid #609ee4 !important;color: #609ee4;margin-left: 5px;line-height: 18px;display: ;}


/*搜索切换*/
#content a{padding:0 20px;line-height:38px;float:left;font-size:16px;text-decoration: none;}
#content a.hover{background:#ff5200;color:#fff}
#content .contentTop{border-bottom:2px solid #ff5200;height:38px;}
#content .contentCtn{height:42px;line-height:42px;background:#f8f8f8;}
#content .contentCtn span{float:left;font-size: 14px;color: #999999;padding-left: 10px;}
#content .contentCtn span i{color:#FF5200;text-decoration:none;padding:0 3px;font-style: initial;}
#content .select_box{width:96px;height:25px;float: left;margin: 9px 8px 0 8px;position:relative;text-align: left;}
#content .select_info{background: url(https://static.fangxiaoer.com/web/images/sy/sale/sale_xl.gif) no-repeat;width: 73px;height:25px;line-height:25px;font-size:12px;padding: 0 20px 0 7px;cursor: pointer;}
#content .select_box ul{position:absolute;background: #fff;width: 94px;border: 1px solid #ccc;top: 23px;display: none;}
#content .select_box ul li:hover{background:#ff5200;color:#fff}
#content .select_box:hover ul{display:block;}
#content .select_box li{border:none;margin:0;height: 30px;line-height: 30px;}
#content .select_box li:hover a{color:#fff;text-decoration:none}
#content .contentCtnRight{float:right;margin-right: 15px;}
#content .select_box li a{font-size: 12px;line-height: 30px;padding-left: 8px;}
#content .contentCtnRight span{display: block;float:left;width: 48px;height: 22px;line-height: 22px;padding: 0 0 0 4px;text-align: left;font-size: 14px;border: 1px #ccc solid;margin-left:11px;color: #333333;background-image: url("https://static.fangxiaoer.com/web/images/sy/sale/sale/up.png");background-repeat:  no-repeat;background-position: 40px 7px;cursor: pointer;background-color: #fff;margin-top: 9px;}
#content .clickSpan{background-image: url("https://static.fangxiaoer.com/web/images/sy/sale/sale/down.png")!important;}
#content .contentCtnRight span:hover{color: #333333}
#content .contentCtnRight a{font-size:12px;padding: 0;float: none;display: block;line-height: 22px;}


/*底文件*/
#footer{background: #222;height:260px;}
#footer .about{padding: 28px 0 18px;}
#footer .about a{color:#fff;margin-right:15px}
#footer .about p{color:#fff;float:right;}
#footer .foot_ewm{width:140px;height:140px;float:right;margin-top: 16px;}
#footer .foot_ewm img{width:100%;}
#footer .link{height:172px;border-top:1px solid #393939;border-bottom:1px solid #393939;}
#footer .link_qh{height: 45px;margin-bottom:10px}
#footer .link_qh li{float:left;padding:0 10px;background:#121212;color:#999;margin:13px 12px 0 0;cursor: pointer;}
#footer .link_qh li.hover{background:#394043;color:#fff;}
#footer .link_txt li{float:left;margin-right:10px;line-height:27px;}
#footer .link_txt li a{color:#999}
#footer .link_txt li a:hover{color:#fff}
/*弹出*/
.fxe-alert {position: absolute;background: url("https://static.fangxiaoer.com/global/imgs/ico/b60.png");color: #fff;font-size: 11pt;text-align: center;border-radius: 6px;padding: 10px 0;z-index: 1000000;}
.error_list{text-align:center;color:#ff5200;margin-top: -27px;position:absolute;width: 248px;}
/*分页*/
.page{font-size:12px;display: block;text-align:center;margin: 30px auto;}
.page a{border:1px solid #e7e7e7;padding:3px 8px;line-height:22px;text-decoration:none}
.page span{background:#b7b7b7;padding:4px 9px;font-weight:normal !important;color:#fff !important}
.page input#Pager1_input{border:1px solid #e7e7e7;line-height: 22px;text-align:center;width: 37px !important;}
.page input#Pager1_btn{border:none;background:#b7b7b7;color:#fff;width: 30px;height: 24px;cursor:pointer}
.minPage{width: 880px;}

/*切换城市*/
.city{color: #fff;padding: 0px 0 0 22px;float: left;}
.shouye_city{color: #fff;padding: 20px 0 0 22px;float: left;}
.city span{cursor: pointer}
.jianjiao{margin-top: -30px;}
.qieH{width: 352px;background: #fff;margin-left: -100px;padding: 9px;color: #666;display: none;border-radius: 5px;position: absolute;top: 40px;}
.qieH i{width: 8px;height: 8px;display: block; background: url("https://static.fangxiaoer.com/global/imgs/ico/jianjiao.png") no-repeat bottom;position: absolute;left: 123px;top: -17px;position: relative;}
.qieH p{color: #000; border-bottom: 1px solid #e8e8e8;padding-left: 10px; margin-top: -8px;padding-bottom: 3px;}
.qieH ul{padding-left: 10px;margin-top: 3px;overflow: hidden;}
.qieH ul li{float: left;margin-right: 30px;}
.qieH ul li a{color: #666;text-decoration: none}
.qieH ul li a:hover{color: #ff5200;}
.city:hover .qieH{display: block;}
.shouye_city:hover .qieH{display: block;}

/*顶部搜索下拉 不含首页*/
.search_xl{background: #fff;position: absolute;width: 807px;z-index: 100;}
.search_xl a{display:block;line-height:30px;padding-left:10px;}
.search_xl a:hover{background: #999}
.search_xl a.hover{background: #999}


/*沈阳站头文件*/
#sy_head{ background:#FFF;border-bottom: 1px solid #eee;}
.sy_head_top_nav{line-height:33px; background:#f4f4f6;color: #999;z-index:2;}
.sy_head_top_nav .sy_head_left a{padding: 0 6px;color:#999;}
.sy_head_top_nav .sy_head_left a:hover{color:#ff6600;}
.sy_head_top_nav .sy_head_left em{color:#666;}
.sy_head_top_nav .sy_head_left i{color:#ff6600;}
.sy_head_logo{height:103px;z-index:1;}
.sy_head_logo img{float:left;margin:10px 0 0 0;}
.sy_head_input{float:left;margin:35px 0 0 64px; _display:inline; width:587px;}
#sy_head .head_input{width:390px;height:32px;line-height:32px;border:3px solid #FF6600;border-left:none; float:left;padding-left:10px;font-size:14px;outline:medium;}
.sy_head_logo .button,.sy_head_logo .button:hover{width:168px; padding-left:20px;float:right;margin-top:35px; background:url(https://static.fangxiaoer.com/web/images/ico/sign/shopping.gif) no-repeat 35px center;border-radius: 0px;height:38px;line-height:38px;color:#999}
#sy_head .head_nav{height:40px;line-height:40px; background:#404144;}
.sy_leftnav_xl{width:233px; background:#ff5200;color:#FFF;font-weight:bold;padding-left:17px;float:left;margin-right:20px; position:relative; font-size:14px;z-index:99;}
.sy_leftnav_xl p{position:absolute;left:0;top:40px; width:250px;display:none; z-index:99999999}
.sy_leftnav_xl span{ display:block;border-left:1px solid #e1e1e1;border-right:1px solid #e1e1e1;border-bottom:1px solid #e1e1e1;width:208px;padding:0 20px; background:#FFF; padding-bottom:8px;}
.sy_leftnav_xl em{ color:#ff6600;}
#sy_head .head_nav .sy_leftnav_xl span a{padding:0 10px 0 0; color:#6d6d6d; line-height:24px; font-weight:normal;}
#sy_head .head_nav .sy_leftnav_xl span.hover{ background:#f4f4f6;}
#sy_head .head_nav a{/*float:left;*/padding:0 23px; color:#FFF; font-size:14px;}
#sy_head{height:33px; line-height:33px; background: #f5f5f5;border-bottom: 1px solid #eee !important;}
.sy_head_w a{color:#999; text-decoration: none;}
.sy_head_w a:hover{color:#ff5200; text-decoration: none;}
.sy_head_w{width: 1170px;margin:0 auto;}
.sy_head_l{float:left;/* height: 50px; */}
.sy_head_l span a{height:14px;line-height:14px;margin-right: 20px;  border-right: 1px solid #dfdfdf;padding-right: 20px;}
#sy_head .no-border{border:none}

.paddingL0{padding-left:0 !important}
.borderRnone{border-right:none !important}
.header{height: 80px; margin:0 auto;background: #FFF;}
.header_container{ position:relative;height: 70px;width: 1170px;margin: 0 auto;}
.logo{display:block;float:left;margin: 24px 10px 0 0;}
.header_search{width:415px;float:left; position:relative;margin: 19px 88px 0 0;}
.search_input{width: 447px;height: 38px !important;border:2px solid #ff9766 !important;float:left;padding:0 0 0 20px !important; background-color:transparent !important;line-height:30px !important;border-radius:0px !important;outline:none }
.search_btn{width:52px !important;height: 42px;border:none; cursor:pointer; background:url(https://static.fangxiaoer.com/web/images/ico/head/search_btn.png) #ff9766 no-repeat center;outline:none   }
.header_search span{ position:absolute;top: 14px;left:20px;font-size:12px;color:#aaaaaa;z-index: 0;}
.header_tel{width:210px;height:30px;padding-right:15px; text-align:right;display:block;float:left;font-size:12px;color:#aaa;position: absolute;top: 20px;right: 194px;line-height:30px;background-color:#fff; overflow:hidden;z-index:999}
.header_tel span{color:#ff5200}
.header_tel_img{ position:absolute;right:0;top:13px}
.header_gg{width:170px;height: 70px;display:block;float:right; text-align:right;  z-index:999}

.sy_head_r{float:right; color:#999}
.sy_head_r div{float: left;  position: relative;  margin-right: 45px;  border-right: 1px solid #dfdfdf;  height: 14px;  line-height: 14px;  padding-right: 31px;  margin: 11px 15px 0 0; background: url(https://static.fangxiaoer.com/web/images/ico/head/head_bottom.gif) no-repeat 63px 5px}
.sy_head_r p{display: none; position: absolute;padding:10px; background: #fff;border:1px solid #dfdfdf;top:22px; z-index: 9999999}
.sy_head_r div:hover p{display: block}
.sy_head_r div:hover{color:#ff5200; cursor: pointer;background: url(https://static.fangxiaoer.com/web/images/ico/head/head_top.gif) no-repeat 63px 5px}
.sy_head_r i{position: absolute;top:-11px;right:53px;width:88px; text-align: center}
.head_app{float:left;margin-right:10px;}
.sy_head_r p span{font-size:12px}

/*导航*/
.nav{width:100%;margin-bottom:0 !important;height: 40px;background-color:#ff5200;color:#fff}
.nav_container{width: 1170px; margin:0 auto}
.nav_container ul{ list-style:none;margin:0 0 0 0 !important;height: 40px;float:left}
.nav_container li{float:left;}
.nav_container .nav_one a{display:block;padding:0 24px;margin-top:2px;color:#fff; text-decoration:none;line-height: 35px;font-size:16px;height: 35px;}
.nav .active a{color:#fff45c; border-bottom:3px solid #fff45c}
.nav_login{height: 40px;float:right;width:180px;background-color:#ff864d}
.nav_login a{line-height: 39px;color:#fff;font-size:14px; text-decoration:none;}
.a_hover a{color:#ff5200 !important;background-color:#fff;border-bottom:3px solid #fff}
.nav_login img{margin-left:32px;float:left;margin-top: 12px;margin-right:13px}
.nav_login_reg{padding-left:18px;border-left:1px solid #fff;margin-left:23px}
.nav_hover{ position:absolute;top: 154px;left:0;width:100%;background-color:#fff;/* border-bottom:1px solid #cccdd4; */ overflow:hidden;height: 41px;}
.nav_float{height:40px;width: 1170px;margin:0 auto; border: 1px solid #cccdd4;border-top: none;}
.nav_float ul{width:100%;height:40px;display:none;margin:0 !important;padding-left: 14px;}
.nav_float li{float:left;padding: 0 24px;margin-top: 11px;border-right:1px solid #dcdcdc}
.nav_float li a{color:#4c4c4c; text-decoration:none;font-size:14px; position:relative}
.nav_float li a:hover{color:#ff5200;}
.nav_float li a img{position:absolute}
.nav_hover .active{color:#ff5200}

/*城市切换*/
.city{color: #333;padding: 0px 0px 0 28px;float: left;width: 74px;margin-right: 80px;margin-top: 32px;position: relative;font-size: 14px;}
.shouye_city{color: #fff;padding: 20px 0 0 22px;float: left;}
.city span{cursor: pointer;background: url(https://static.fangxiaoer.com/web/images/ico/sign/jt.png) no-repeat right !important;padding-right: 17px !important;}
.city span:hover{color:#333 !important}
.jianjiao{}
.qieH{width: 352px;background: #FFF;padding: 9px;color: #666;display: none;border-radius: 5px;position: absolute;top: 24px;z-index: 8888;border: 1px solid #ccc;left: -116px;}
.qieH i{width: 100px;height: 5px;display: block;background: url("https://static.fangxiaoer.com/global/imgs/ico/jianjiao.png") no-repeat bottom;position: absolute;left: 123px;top: -35px !important;padding-top: 30px !important;}
.qieH p{color: #000;border-bottom: 1px solid #e8e8e8;padding-left: 10px;margin-top: -1px;padding-bottom: 3px;float: none !important;line-height: 31px !important;font-size: 14px !important;}
.qieH ul{padding-left: 10px;margin-top: 3px;overflow: hidden;}
.qieH ul li{float: left;margin-right: 30px !important;line-height: 32px !important;padding-right: 0;}
.qieH ul li a{color: #666;text-decoration: none}
.qieH ul li a:hover{color: #ff5200;}
.city:hover .qieH{display: block;}
.shouye_city:hover .qieH{display: block;}

/*好房低价简单*/
.box_icon {width: 1170px;padding: 18px 0;overflow: hidden;font-size: 14px;color: #999;margin: 0 auto;}
.icon_main{float: left;overflow: hidden;width: 370px;}
.icon_main_1{width: 420px;}
.icon_left{float: left;margin-right: 15px;}
.icon_right {padding: 10px 0px;}
.icon_right>div {margin-right: 8px;margin-bottom: 5px;}
.icon_3 {margin-top: -3px;}

.footer{background:#f5f5f5;border-top: 1px solid #eeeeee;margin-top: 45px;}
.footer_width{width: 1170px;}
.footer_160309{width: 100%;border-top: 1px solid #eeeeee;font-size: 12px;color: #666;background: #f5f5f5;}
.footer_top_160309{margin: 45px auto 0;height: 130px;}
.footer_160309 ul{float: left;margin-right: 68px;width: 142px;}
.footer_160309 li{float: left;margin-right: 20px;line-height: 30px;}
.footer_160309 li a{color: #666666;text-decoration: none;}
.footer_160309 li.footer_t{float: none;margin-bottom: 15px;font-size: 14px;color: #1f1f1f;}
.footer_160309_right{line-height: 28px;padding-left: 17px;border-left: 1px solid #ddd;float: right;}
.footer_160309 ul.footer_ul_1{width: 180px;}
.footer_160309 ul.footer_ul_2{width: 170px;}

.footer_ewm{padding: 45px 0;line-height: 34px;height: 101px;margin: 0 auto;}
.footer_ewm_img{float: left;width: 245px;font-size: 12px;color:#999;margin-right: 40px;line-height: 24px;}
.footer_ewm_img img{float: left;margin-right: 16px;}
.footer_ewm_img p{font-size: 14px;color: #333;margin-top: 10px;margin-bottom:14px;text-indent: 0;}
.footer_biaoshi{float: right;margin:30px 40px 0 0;}

.footer_copy{margin: 0 auto;line-height: 30px;color:#999;font-size: 12px;}
.footer_copy a{color:#999;text-decoration: none;padding-right:5px ;border-right: 1px solid #b3b3b3;margin-right: 5px;line-height: 12px;float: left;margin-bottom: 8px;}
.footer_copy a.bor_n{border-right: none;}
.footer_copy p{text-indent:0}

/*搜索下拉*/
.ac_results {padding: 0px;border: 1px solid #c1c1c1;background-color: white;overflow: hidden;z-index: 99999;width: 482px !important;}
.ac_results ul {width: 100%;list-style-position: outside;list-style: none;padding: 0;margin: 0;}
.ac_results li {margin: 0px;padding: 0px 5px;cursor: default;display: block;text-align:left;font: menu;line-height: 40px;overflow: hidden;background: #fff;font-size: 14px;}
.ac_loading {background: white url('/images/indicator.gif') right center no-repeat;}
.ac_odd {background-color: #eee;}
.ac_over {background-color: #f3f3f3 !important;color: #333;}

/*子导航定位*/
#rent{padding-left: 42px;}
#shangpu{padding-left: 240px;}
#help{padding-left:175px}
#need{padding-left: 259px !important;}
#form{padding-left: 477px;}
#strategy{padding-left: 80px;}

/*新版头文件 2017-7-3*/
#head2017{background: #ff5200;height:50px;line-height: 50px;min-width: 1170px;}
#head2017 h1{float:left;margin-top: 12px;line-height: 26px;height: 26px;font-size: 16px;}
/*城市切换*/
#head2017 .city{float:left;margin: 0;color: #fff;background: url(https://static.fangxiaoer.com/global/imgs/ico/ico_city_qh.png) no-repeat 64px 23px;}
#head2017 .city_tc{width: 352px;background: #FFF;padding: 9px;color: #666;border-radius: 5px;border: 1px solid #ccc;position:absolute;left: -110px;display: none;z-index: 10000000;}
#head2017 .city p{color: #000;border-bottom: 1px solid #e8e8e8;padding-left: 10px;margin-top: -1px;padding-bottom: 3px;float: none !important;line-height: 31px !important;font-size: 14px !important;}
#head2017 .city i{width: 100px;height: 5px;display: block;background: url("https://static.fangxiaoer.com/global/imgs/ico/jianjiao.png") no-repeat bottom;position: absolute;left: 96px;top: -35px !important;padding-top: 30px !important;}
#head2017 .city_tc ul{padding-left: 10px;margin-top: 3px;overflow: hidden;}
#head2017 .city_tc li{float: left;margin-right: 30px !important;line-height: 32px !important;padding-right: 0;}
#head2017 .city_tc li a{color: #666;text-decoration: none}
#head2017 .city_tc li a:hover{color: #ff5200;}
#head2017 .city:hover .city_tc{display:block}
/*新头导航*/
#head2017 .head_nav{float:left;margin: 0;}
#head2017 .head_nav ul{margin:0}
#head2017 .head_nav li{float:left;position: relative;line-height: 50px;z-index: 1000;}
#head2017 .head_nav li a{padding: 0 12px;font-size: 14px;color:#f8f8f8;display:block;text-decoration:none;}
#head2017 .head_nav li:hover a{background:#f24d00}
#head2017 .head_nav li a.hover{background:#f24d00}
#head2017 .head_nav li p{width: 108px;box-sizing: border-box;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.19);border: 1px solid #eaeaea;position:absolute;top:60px;left: 50%;background:#fff;transition: all .5s;-moz-transition:all .5s; /* Firefox 4 */-webkit-transition:all .5s; /* Safari and Chrome */-o-transition:all .5s; /* Opera */opacity: 0;visibility: hidden;margin-left: -53px;}
#head2017 .head_nav li p a{background:none;color:#333;padding: 0;text-align: center;line-height: 34px;}
#head2017 .head_nav li p a:hover{color:#ff5200}
#head2017 .head_nav li p a.hot_red{color:#ff5200}
#head2017 .head_nav li:hover p a{background:none;}
#head2017 .head_nav li:hover p{top:50px; opacity: 1;visibility: visible}
#head2017 .head_nav li i{position:absolute;width:10px;height:8px;background:url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);top: -8px;left: 50%;margin-left: -5px;}
/*登录注册*/
#head2017 .head_right_login{float:right;color:#f8f8f8;position:  relative;}
#head2017 .head_right_login a{color:#fff;text-decoration:none;font-size: 12px;margin-top: 1px;}
#head2017 .head_right_login a:last-child{
    margin-left: 6px;
}
#head2017 .head_right_login a+a+a{
    float:  left;
}
#head2017 .head_right_login a.recode{background: url(https://static.fangxiaoer.com/global/imgs/index/recodePhone.png)no-repeat 0px 4px;padding-left: 20px;text-decoration: none;padding: 3px 0 3px 20px;margin-right: 3px;}
#head2017 .head_right_login .login{display:inline;position: relative;margin-left: 2px;}
#head2017 .head_right_login .login p{width: 108px;box-sizing: border-box;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.19);border: 1px solid #eaeaea;position:absolute;top: 45px;left: 50%;background:#fff;transition: all .5s;-moz-transition:all .5s; /* Firefox 4 */-webkit-transition:all .5s; /* Safari and Chrome */-o-transition:all .5s; /* Opera */opacity: 0;visibility: hidden;margin-left: -53px;}
#head2017 .head_right_login .login p a{background:none;color:#333;padding: 0;text-align: center;line-height: 34px;display: block;}
#head2017 .head_right_login .login p a:hover{color: #ff5200;}
#head2017 .head_right_login .loginhead_right_login .login a:hover{color:#ff5200}
#head2017 .head_right_login .login:hover p a{background:none;}
#head2017 .head_right_login .login:hover p{top: 35px;opacity: 1;visibility: visible;z-index: 10000000;left:16px}
#head2017 .head_right_login .login i{position:absolute;width:10px;height:8px;background:url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);top: -8px;left: 50%;margin-left: -5px;}
#head2017 .head_right_login a em{display: inline-block;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;line-height: 12px;max-width: 66px;}
#head2017 .head_right_login a b{
    color: #fff;
    font-weight:  normal;
}

/*搜索*/
#search2017{height:66px;background: #f7f8fc;min-width: 1170px;}
#search2017 img{margin-top:10px;}
#search2017 .search{float:left;width: 580px !important;height:34px;/* background:#f2f2f2; */position: relative;border-radius:5px;padding:6px;margin-top: 4px !important;margin-left: -8px;}
#search2017 .search img{
    position: absolute;
    top: 8px;
    right: 105px;
    cursor: pointer;
}
#search2017 .search input{height:32px;border:none;padding: 0;}
#search2017 .search .ac_input{width: 574px;float:left;border: 1px solid #eaeaea;border-radius:2px 0 0 2px;padding-left:10px;outline: none;box-shadow: none;font-size: 14px;height: 44px;}
#search2017 .search .btn_search{width: 99px !important;background: #ff7200;height: 42px;font-size: 16px;color: #fff;border-radius: 0 2px 2px 0;cursor: pointer;margin-left: -101px;margin-top: 2px;}
#search2017 .map{float:left;margin: 21px 0 0 22px;}
#search2017 .map a{display:block;background: url("https://static.fangxiaoer.com/web/images/ico/map/img_map.png") no-repeat 0 3px;padding-left: 22px;color: #999;font-size: 14px;}
#search2017 .search_tel{float:left;font-size: 14px;color: #999;line-height: 59px;margin-left: 30px;}
#search2017 .search_tel i{font-family:Georgia;font-size:26px;font-weight:bold;color:#ff5200}

/*列表搜索*/
#option{width:1168px;border:1px solid #eaeaea;margin:0 auto;background: #fff;font-size: 12px;}
#option p{float:left;width:42px;margin-right:10px;color: #91a0b6;font-weight: bold;}
#option li{height:37px;line-height:37px;padding:0 20px;}
/*#option li a{padding: 10px 11px;color:#333;text-decoration:none;;position:relative}*/
#option li a{padding: 10px 11px;color:#425571;text-decoration:none;;position:relative}
#option #cp1,#option #cp2{
    display: inline-block;
    border: 1px solid #eaeaea;
    line-height: 22px;
    padding: 0 10px;
    cursor: pointer;
}
#option #cp1{
    margin-left: 20px;
}
#option .Search_uPriceDom.hover{
    border: 1px solid #ff5200!important;
}
#option li a.hover,#option li a:hover{color:#ff5200;border-color: 1px solid #ff520;}
#option li.fenlei a.hover i{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/ico_option_blue2.png   ) no-repeat bottom;width:16px;height:8px;position:absolute;bottom: -2px;left: 50%;margin-left: -8px;}
#option li.leibie{border: 1px solid #eeeff4;background: #f7f8fc;width:1060px;margin-left:20px;padding: 0 20px 0 10px;height: auto;overflow: hidden;}
#option label{margin-top: 7px;margin-left: 18px;}
#option label input{width: 36px;height: 20px;border: 1px solid #eaeaea;outline: none;text-align: center;background: none;font-size: 12px;padding: 0;}
#option label input.btn_search{width: 54px;height: 27px;background: #fff;border: 1px solid #FF5200 !important;color: #FF5200;margin-left: 5px;line-height: 18px;display: none;}
#option div{display:inline}
#option div#Search_PlateDom{display:block;}
/*搜索 更多*/
#option_other{width:1170px;margin:0 auto; background:#fff;}
#option_other ul{margin:0;position: relative;z-index: 100;top: 0;}
#option_other li{height:40px;line-height:40px;border:1px solid #f1f1f1;margin-top:-1px;}
#option_other li p{float:left;margin-left: 20px;font-size: 12px;color: #91a0b6;font-weight: bold;}
#option_other li.dele a{float:left;border: 1px solid #ffb180;font-size: 12px;padding: 2px 20px 1px 5px;margin: 11px 6px 0 9px;text-decoration: initial;line-height: 13px;color: #444;cursor: pointer;background: url(https://static.fangxiaoer.com/web/images/sy/sale/icoCloseArrow.gif) no-repeat right 0px;}
#option_other li.dele a:hover{background-position: right -17px;border: 1px solid #ff6a0d;}
#option_other li.dele a.qkqb{border: none;padding-left: 15px;padding-right: 0;background: url(https://static.fangxiaoer.com/web/images/sy/sale/qkqb.gif) left no-repeat;}
#option_other li.dele a.qkqb:hover{color:#ff5200}
#option_other .select_box{width:96px;height:25px;float: left;margin: 9px 8px 0 8px;position:relative;text-align: left;}
#option_other .select_info{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/sale_xl.gif) no-repeat;width: 73px;height:25px;line-height:25px;font-size:12px;padding: 0 20px 0 7px;cursor: pointer;}
#option_other .select_box ul{position:absolute;background: #fff;width: 94px;border: 1px solid #ccc !important;top: 23px;display: none;overflow-y: overlay;max-height: 200px;overflow:  auto;}
#option_other .select_box ul li{height: 30px;line-height: 30px;border:none;/* padding-left: 6px; */cursor: pointer;font-size: 12px;}
#option_other .select_box ul li a{width: 93%;height:100%;display:block;padding-left: 6px;}
#option_other .select_box ul li a:hover{background: #f5f5f5;}
#option_other .select_box ul li:hover a{color: #333;text-decoration:none;}
#option_other .select_box:hover ul{display:block}
/*搜索 标签*/
#option_info{border: 1px solid #f1f1f1;margin: 0 auto; width: 1170px;border-top: 0;overflow: hidden;font-size: 12px;height: 42px;line-height: 42px;}
#option_info b{font-weight: 400;display: block;float: left;margin-left: 20px;margin-right: 8px;color: #91a0b6;font-weight: bold;}
#option_info div{overflow: hidden;display: inline-block;line-height: 24px;border: 1px solid #b6c4d9;padding-left:8px;margin-right: 6px;margin-top: 8px;float: left;}
#option_info div span{display: block;float: left;}
#option_info div i{cursor: pointer;display: block;width: 24px;height: 24px;float: left;margin-left: 8px;background: url(https://static.fangxiaoer.com//web/images/sy/sale/xx_blue.png) no-repeat left center;}
#option_info .clean{padding-left: 26px;background: url(https://static.fangxiaoer.com//web/images/sy/sale/delete_blue.png)no-repeat left center;text-decoration: none;cursor: pointer;margin-left: 16px;display: inline-block;float: left;}
#option_info a:hover{color: #333;}

/*排序*/
.sort{height: 57px;line-height:42px;background: #fff;}
.sort p{float: left;margin: 16px 0 0 20px;}
.sort .dealbiao{
    float: right;
    margin-right: 30px;
}
.sort .dealbiao a{
    color: #FFF;
    background: #ff5200;
    width: 93px;
    display: inline-block;
    text-align: center;
    border-radius: 100px;
    line-height: 26px;
    height: 27px;
    float: right;
    text-decoration: none;
    background: -webkit-linear-gradient(90deg,#ffd000, #ff5200); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(90deg,#ffd000, #ff5200); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(90deg,#ffd000, #ff5200); /* Firefox 3.6 - 15 */
    background: linear-gradient(90deg,#ffd000, #ff5200); /* 标准的语法 */
}
.sort #sortParam a{border: 1px solid #ccc;background: #fff;padding: 0 9px;float: left;margin-left: -1px;height: 26px;line-height: 26px;}
.sort #sortParam a:hover{background:#ff5200;color:#fff;border: 1px solid #ff3029;text-decoration:none}
.sort #sortParam a+a{padding: 0 15px 0 16px;}
.sort #sortParam a+a:hover{text-decoration:none;background: url(https://static.fangxiaoer.com/web/images/sy/house/arrow_rank1.png) no-repeat 43px -181px #Ff5200;color:#fff;padding: 0 22px 0 9px;border: 1px solid #ff3029;}
.sort #sortParam a+a+a+a+a:hover{background:#ff5200;color:#fff;border: 1px solid #ff3029;text-decoration:none;padding: 0 15px 0 16px}
.sort #sortParam a.sort_jg{background: url(https://static.fangxiaoer.com/web/images/sy/house/arrow_rank1.png) no-repeat #Ff5200;padding: 0 22px 0 9px}
.sort #sortParam a.hover{color: #fff;border: 1px solid #ff5200;background: #ff5200;}
.sort #sortParam a.up{background-position: 43px -119px;color: #FFFFFF;border: 1px solid #ff3029;}
.sort #sortParam a.down{background-position: 43px -181px;color: #FFFFFF;border: 1px solid #ff3029;}

/*切换*/
.sort #sort_qh{float:right;margin: 22px 10px 0 0px;}
.sort #sort_qh a{float:left;width:16px;height:16px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/list_qh.png) no-repeat 0px 1px;margin-right: 10px;}
.sort #sort_qh a+a{background: url(https://static.fangxiaoer.com/web/images/ico/sign/list_qh.png) no-repeat -29px 0px;}
.sort #sort_qh a.hover{background: url(https://static.fangxiaoer.com/web/images/ico/sign/list_qh.png) no-repeat 0px -22px;}
.sort #sort_qh a+a.hover{background: url(https://static.fangxiaoer.com/web/images/ico/sign/list_qh.png) no-repeat -29px -22px;}

/*美洽透明*/
#MEIQIA-BTN{opacity: 0.5;}
#MEIQIA-BTN:hover{opacity: 1;}

/*sy列表页筛选部分icon图标*/
#option .fenlei a{margin-right:20px}
.listIcon{display: inline-block;width: 18px;height: 14px;position: absolute;left: -10px;top: 10px;}
.listIcon_dw{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_dw.png) no-repeat;left: -4px}
.listIcon_ditie{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_ditie.png) no-repeat}
.listIcon_esf{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_esf.png) no-repeat}
.listIcon_map{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_map.png) no-repeat}
.listIcon_xq{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_xq.png) no-repeat}
.listIcon_bargains{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_bargain.png) no-repeat}
/*sy房源详情 分享二维码*/
.nav_house a.ewm{float:right;position:relative;cursor:pointer;margin-right:25px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_ewm.gif) no-repeat left;padding-left:25px}
.nav_house span{display:none;position:absolute;z-index:1000;border:1px solid #cccdd4;padding: 0;background:#fff;right:-25px;width: 200px;height: 120px;}
.nav_house a.ewm:hover span{display:block}
.nav_house a.ewm i{color:#888;float: left;text-align: left;line-height: 21px;margin-top: -11px;}
.nav_house a.ewm img{width: 120px;margin-bottom: -14px;float: left;}
#qrcode{float: left;margin: 13px;}
/*经纪人左侧菜单小图标*/
#option li a.hover  .listIcon_dw{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_dw_hover.png) no-repeat;left: -4px;}
#option li a.hover  .listIcon_ditie{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_ditie_hover.png) no-repeat}
#option li a.hover  .listIcon_esf{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_esf_hover.png) no-repeat}
#option li a.hover  .listIcon_map{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_map_hover.png) no-repeat}
#option li a.hover  .listIcon_xq{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_xq_hover.png) no-repeat}
#option li a.hover  .listIcon_bargains{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_bargains.png) no-repeat}
/* 右侧个人简介 */
.agent-grjj{wid;width: 188px;float: right;border: 1px solid #ededed;margin-top: 16px;padding: 10px;}
.agent-grjj h2{font-size: 18px;text-align: left;font-weight: normal;margin-bottom: 0;color: #333;margin: 0;line-height: 45px;border-bottom: 1px solid #ededed;margin-bottom: 5px;}
.agent-grjj p{text-align: left;padding-left: 8px;}
.agent-grjj p a{
    color: #1e56d8;
    padding: 0 2px;
    cursor: pointer;
    float: none !important;
    font-size: 14px !important;
    padding: 0 0 !important;
    line-height: 26px !important;
}
.agent-grjj p span{
    font-size: 14px;
    line-height: 26px;
}
.agent-grjj p a:hover{color:#ff5200}
.agent-grjj .btn-sq{}
.agent-grjj .btn-xx{}
.about{
    position: relative !important;
}
.agent-grxy{
    display: inline-block;
    float: left;
    position:  absolute;
    left: 420px;
    top: 70px;
}
/*新二手房头部201804*/
.house2{
    width: 870px;
    border: 1px solid #ebebeb;
    margin-bottom: 30px;
}
.house2 .house2Title{
    font-size:  20px;
    color:  #333;
    line-height: 50px;
    padding: 0px 18px;
    border-bottom:  1px solid #ebebeb;
}
.house2 .house2Title a{
    font-size: 14px;
    color: #aaa;
    float: right;
}


/* 底部一分钟弹出引导登录广告条 */
#popupBottom{
    position:  fixed;
    bottom: 0;
    width:  100%;
    z-index: 99999;
    background: rgba(8, 20, 38, 0.82);
    height: 120px;
    
    /*IE8*/
     /* 一些不支持背景渐变的浏览器 */
    /*     background: -webkit-linear-gradient(left, red , #ff7e24); 
    background: -o-linear-gradient(right, red, #ff7e24);
    background: -moz-linear-gradient(right, red, #ff7e24);
    background: linear-gradient(to right, red , #ff7e24);  */
}
#popupBottom .close{
    position:  inherit;
    float:  right;
    margin-right: 30px;
    margin-top:  20px;
}
#popupBottom .popupBottomFonts{width: 1170px;height: 120px;background: url(https://static.fangxiaoer.com/web/images/sy/house/syNewBottomLogin.png);margin:  0 auto;}
#popupBottom  .popupBottomFonts p{}
#popupBottom  .popupBottomFonts p span{}
#popupBottom .popupLogin{
    background: #ff5200;
    display:  block;
    width: 90px;
    border-radius:  28px;
    text-align:  center;
    color: #fff;
    float:  right;
    font-size: 14px;
    height: 26px;
    margin-top: 66px;
    margin-right: 246px;
}
#popupBottom>img{
    width: 100%;
    display:  none;
}

/* 新房详细页头部二维码部分 */
.newHouseViewpro_name{margin-bottom: 8px;font-size:24px;color:#333;height: 70px;line-height:28px;/* overflow:  hidden; */position:  relative;}
.newHouseViewpro_name p{/* float:left; */margin-right:19px;}
.newHouseViewpro_name #qrcode{float: left;height: 60px;margin: 0;width: 60px;margin-right: 10px;}
.newHouseViewpro_name a{float:left;font-size:14px;color:#FFF;height: 20px;line-height: 20px;text-align:center;border: 1px solid #eff3fd;color: #668ae9;/* margin-left:10px; */border-radius:2px;background:  #eff3fd;margin-top: 10px;margin-right: 10px;padding:  0 5px;}
.newHouseViewpro_name a:hover{text-decoration:none;}
.newHouseViewpro_name img{
    position: absolute;
    top: -12px;
    right: 0px;
}
.newHouseViewpro_name .s_time {
    padding-left: 40px;
    /* background: url(https://static.fangxiaoer.com/web/images/sy/house/view/s_time.gif) 20px no-repeat; */
    position: absolute;
    right: 44px;
    font-size: 12px;
    bottom: -7px;
    color: #999;
    z-index:  9999;
}


/* 消息提示 */
.MessageHints{
    position:  absolute;
    background:  #fff;
    padding:  10px;
    width: 300px;
    left: -106px;
    top: 40px;
    border:  1px solid #e1e1e1;
    border-radius: 2px;
    color: #000;
}
.MessageHints:after
{ 
content:" ";
font-weight:bold;
position: absolute;
    width: 10px;
    height: 8px;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);
    top: -8px;
    left: 80%;
    margin-left: -5px;
}
.MessageHints h4{
    font-size: 16px !important;
    line-height:  20px !important;
}
.MessageHints ul{}
.MessageHints ul li{
    line-height: 25px !important;
    overflow: hidden;
}
.MessageHints ul li a{
    color: #000 !important;
}
.MessageHints ul li a span i{
    width: 250px;
    overflow:  hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display:  inline-block;
    float:  left;
}
.MessageHints ul li a s{
    color:  #ff5200 !important;
    float:  left;
}




#head2017 .login:hover p{opacity: 1;position: absolute; left: -39px;top:42px;visibility:visible;}
#head2017 .head_right_login{float: right;padding-top:0px;line-height: 50px;position:  relative;}
#head2017 .head_right_login em{display:inline-block;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;float:  left;padding-top: 19px;margin-right: 5px;}
#head2017 .head_right_login b{margin-top: 27px;display: initial;}
#head2017 .head_right_login a.recode{line-height: 43px;float: left;background: url(https://static.fangxiaoer.com/global/imgs/index/recodePhone.png)no-repeat 0px 17px !important;padding-left: 20px !important;position:relative;}
#head2017 .head_right_login .recode div{position:absolute;left:-15px;top:42px;width:133px;height:147px;background: url(https://static.fangxiaoer.com/global/imgs/index/recode.png)no-repeat;opacity: 0;transition: all 0.6s;z-index: 22;visibility:hidden;}
#head2017 .head_right_login .recode:hover div{opacity: 1;position: absolute; left: -15px;top:30px;visibility:visible;}
#head2017 .head_right_login .recode div img{ margin:0 auto;display:block; padding-top:35px}
.MessageHints h4{font-size: 14px !important;!importantfont-weight:bold;}
.footer_ewm_img a{color:#999}


/*搜索*/
#search2018{height:66px;background: #f7f8fc;}
#search2018 img{margin-top:10px;}
#search2018 .search{float:left;width: 574px !important;height:34px;/* background:#f2f2f2; */position: relative;border-radius:5px;padding:6px;margin-top: 4px !important;margin-left: -8px;}
#search2018 .search img{
    position: absolute;
    top: 8px;
    right: 105px;
    cursor: pointer;
}
#search2018 .search input{height:32px;border:none;padding: 0;}
#search2018 .search .ac_input{width: 574px;float:left;border: 1px solid #eaeaea;border-radius:2px 0 0 2px;padding-left:10px;outline: none;box-shadow: none;font-size: 14px;height: 44px;}
#search2018 .search .btn_search{width: 99px !important;background: #ff7200;height: 42px;font-size: 16px;color: #fff;border-radius: 0 2px 2px 0;cursor: pointer;margin-left: -101px;margin-top: 2px;}
#search2018 .map{float:left;margin: 21px 0 0 22px;}
#search2018 .map a{display:block;background: url("https://static.fangxiaoer.com/web/images/ico/map/img_map.png") no-repeat 0 3px;padding-left: 22px;color: #999;font-size: 14px;}
#search2018 .search_tel{float:left;font-size: 14px;color: #999;line-height: 59px;margin-left: 30px;}
#search2018 .search_tel i{font-family:Georgia;font-size:26px;font-weight:bold;color:#ff5200}
.register{margin-left:5px}

/* 列表页下拉，搜索框固定顶部 */
#searchFloat{}
 .searchFloat{
        width: 100% !important;
        height: 46px;
        padding-top: 4px;
        margin: 0px 0;
        overflow: hidden;
        color: #fff;
        position: fixed !important;
        top: 0;
        left: 0;
        z-index: 100000;
        background: #ff5200;}
 #searchFloat  .searchFloat {
        width: 100% !important;
        margin: 0 auto;
        float: none;
        margin-top: 0 !important;
        padding: 0 !important;
        border-radius:  0;
        height: 50px;
        background: rgba(255, 82, 0, 0.9);
        }
#searchFloat .searchFloat .searchFloatKuang{
    width:  1170px;
    margin:  0 auto;
    position:  relative;
}
#searchFloat .searchFloat  .searchFloatKuang #txtkeys{
    line-height: 32px;
    height: 30px;
    margin-top: 9px;
    margin-left: 122px;
    width: 653px;
}
#searchFloat .searchFloat  .searchFloatKuang #deleteButton{
    right:  0;
    left: 765px;
    top: 5px;
}
#searchFloat .searchFloat .searchFloatKuang .btn_search{
    height: 30px;
    line-height: 30px;
    margin-top: 10px;
    margin-left: -100px;
    background: rgba(255, 82, 0, 0.9);
    font-size:  14px;
}
.rightA,.rightAS{display:none}
#searchFloat .searchFloat .searchFloatKuang .rightA{float:right;display:  block;line-height: 32px;color: #ff5200;cursor:  pointer;font-size: 14px;text-decoration:  none;padding: 0 22px;}
#searchFloat .searchFloat .searchFloatKuang .rightA:hover{background:rgba(255, 82, 0, 0.12);padding-right:23px}
#searchFloat .searchFloat .searchFloatKuang .rightAS{float:right;display:  block;line-height: 31px;color: #ff5200;cursor:  pointer;font-size: 13px;text-decoration:  none;width:  1px;display:  block;height:  16px;background:  #ff5200;margin-top: 9px;}
.searchFloatImg{display:none}
#searchFloat .searchFloat .searchFloatImg{
   float:  left;
   display:block
}
#searchFloat .searchFloat .searchFloatImg img{
    position:  initial;
    top:  0;
    right:  0;
}
.rightASpan{display:none}
#searchFloat .searchFloat .rightASpan{display: block !important;float:  right;height: 32px;line-height: 32px;margin-top: 9px;background: #fff;}
#searchFloat .searchFloat .rightASpan:hover .rightAS{display:none}

input::-webkit-input-placeholder {color: #757575 !important;}
input:-moz-placeholder {color: #757575 !important;}
input::-moz-placeholder {color: #757575 !important;}
input::-ms-input-placeholder {color: #757575 !important;}
.footer{/* background:#f5f5f5; *//* border-top: 1px solid #eeeeee; */margin-top: 45px;overflow: hidden;}
.footer_width{width: 1170px;}
.footer_160309{width: 100%;border-top: 1px solid #eeeeee;font-size: 12px;color: #666;background: #f5f5f5;overflow: hidden;}
.footer_top_160309{margin: 45px auto 0;height: 130px;}
.footer_160309 ul{float: left;margin-right: 67px;width: 142px;margin-left:0;}
.footer_160309 li{float: left;margin-right: 20px;line-height: 30px;}
.footer_160309 li a{color: #666666;text-decoration: none;}
.footer_160309 li.footer_t{float: none;margin-bottom: 15px;font-size: 14px;color: #1f1f1f;}
.footer_160309_right{line-height: 28px;padding-left: 19px;border-left: 1px solid #ddd;float: right;}
.footer_160309 ul.footer_ul_1{width: 180px;}
.footer_160309 ul.footer_ul_2{width: 170px;}

.footer_ewm{padding: 40px 0 40px 210px;line-height: 34px;height: 100px;margin: 0 auto;background: #fff;width: 960px;border: 1px solid #eaeaea;overflow:  hidden;}
.footer_ewm_img{float: left;width: 245px;font-size: 12px;color:#999;margin-right: 40px;line-height: 24px;}
.footer_ewm_img a{color: #999;}
.footer_ewm_img img{float: left;margin-right: 12px;}
.footer_ewm_img p{font-size: 14px;color: #333;margin-top: 26px;margin-bottom: 0;}
.footer_biaoshi{float: right;margin: 30px 40px 0 20px;}

.footer_copy{margin: 0 auto;line-height: 30px;color:#999;font-size: 12px;padding-bottom: 40px;}
.footer_copy a{color: #333;text-decoration: none;padding-right: 9px;border-right: 1px solid #b3b3b3;margin-right: 10px;line-height: 15px;float: left;margin-bottom: 8px;margin-top: 20px;}
.footer_copy div{
    overflow: hidden;
    width: 766px;
    margin: 0 auto;
}
.footer_copy a.bor_n{border-right: none;}

@media (max-width: 1200px) {
    .footer_width{width: 940px;}
    .footer_160309 ul{margin-right: 11px;}
    .footer_ewm_img{margin-right: 0;}
    .footer_biaoshi{margin:30px 0 0 0;}
    .sy_head_w{width:940px;}
}
.footer_copy p{margin:0;color: #333;text-align: center;}

/* 列表页竞价标签 */
.listIconJing{background: url(https://static.fangxiaoer.com/web/images/ico/sign/list_jing.png) top center;background-size: 100% 100%;width: 44px;height: 32px;display: block;position: absolute;right: 16px;top: 0;}

.newHouseListTitle div{float:left;max-width: 466px;text-overflow: ellipsis;text-decoration: none;overflow: hidden;}
.listIconBidPrice{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIconBidPrice.jpg) top center;background-size: 100% 100%;width: 24px;height: 24px;display: block;float: left;margin-left: 10px;}
.listIconIstop{background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIconIstop.jpg) top center;background-size: 100% 100%;width: 24px;height: 24px;display: block;float: left;margin-left: 10px;}
.houseAddressSpance{margin-right:12px}



/* 视频与VR图标 */
.listIconK{
    position: absolute;
    right: 10px;
    bottom: 10px;
    display: block;
}
.videoListIcon{background: url(https://static.fangxiaoer.com/web/images/agent/videoIcon.png);width: 31px;height: 31px;display:block;background-size: 100%;float: right;margin-left: 10px;}
.vrListIcon{
    background: url(https://static.fangxiaoer.com/web/images/ico/listVR.png);
    width: 29px;
    height: 29px;
    display: block;
    background-size: 100%;
    float: right;
    border: 1px solid #fff;
    border-radius: 50px;
    }
.panUrlIcon{    background: url(https://static.fangxiaoer.com/web/images/ico/listVR.png);
        width: 100px;
        height: 100px;
        background-size: 100%;
        float: right;
        border: 1px solid #fff;
        border-radius: 50px;
        position: absolute;
        left: 50%;
        top: 50%;
        display: block;
        z-index: 9;
        margin-left: -85px;
        margin-top: -49px;
    }

.grfw{
    overflow: hidden;
    width: 670px !important;
}
.grfw span{
    float: left;
    height: 24px;
    line-height: 24px;
    padding: 0 6px;
    background: #6c9ee5;
    color: #fff;
    margin-right: 6px;
}
.details>ul>li section{overflow:hidden}
.details>ul>li section>span{
    display: block;
    float: left;
    width: 80px;
    color: #999;
    line-height: 31px;
}
.details>ul>li section>div{
    overflow: hidden;
    float: left;
    padding: 0;
    width: 600px;
    padding-top: 6px;
}
/*搜索提醒*/
.often{
    display: block;
    height: 66px;
    float: left;
    overflow: hidden;
}
.often ul a li{
    height: 66px;
    margin-left: 14px;
    line-height: 66px;
    font-size: 14px;
    float: left;
    color: #666666 !important;
}
.often ul a li:hover{
    color: #ff5200 !important;
}
/*纠错*/
.error_corrtion{
    position: absolute;
    right: 0;
    font-size: 12px;
    bottom: -7px;
    color: #333333;
    z-index: 9999;
    cursor: pointer;
}
.pro_name{
    overflow: inherit !important;
}
.error_tion{
    width: 323px;
    height: auto;
    position: absolute;
    top: 70px;
    right: -8px;
    background:url(https://static.fangxiaoer.com/web/images/error_corrtion.png) no-repeat;
    background-size: 100% 100%;
    z-index: 999;
    display: none;
}
.Corrected{
    width: 306px;
    height: auto;
    position: relative;
    margin: 0 auto;
    margin-top: 8px;
}
.Corrected .tlt{
    width: 100%;
    height: 50px;
    border-bottom: 1px solid #ebebeb;
}
.Corrected .tlt p{
    display: block;
    font-size: 16px;
    color: #333333;
    line-height: 50px;
    padding-left: 24px;
    float: left;
    margin-right: 0 !important;
}
.shut_down{
    width: 12px;
    height: 12px;
    float: right;
    background:url(https://static.fangxiaoer.com/web/images/shut_down.png) no-repeat;
    background-size: 100% 100%;
    margin: 19px 18px 0 0;
    cursor: pointer;
}
.error_conut{
    width: 262px;
    overflow: hidden;
    margin: 0 auto;
}
.error_conut dl{
    overflow: hidden;
}
.error_conut dl dd{
    width: 100%;
    height: 30px;
    margin-top: 15px;
}
.error_conut dl dd span{
    display: block;
    width: 25.5%;
    line-height: 30px;
    font-size: 13px;
    color: #333333;
    float: left;
}
.error_conut dl dd input{
    display: block;
    width: 73.2%;
    height: 28px;
    border:1px solid #ebebeb;
    color: #333333;
    float: right;
    border-radius: 2px;
    outline: none;
    text-indent: 1em;
    font-size: 13px;
}
.error_di{
    width: 262px;
    height: 30px;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 30px;
}
.error_di input{
    width: 120px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 3px;
    font-size: 13px;
    cursor: pointer;
    outline: none;
    border: none;
}
#error_submission{
    background: #ff5200;
    float: left;
    color: #ffffff;
}
#error_cancel{
    background: #ededed;
    float: right;
    color: #333333;
}
.error_bieshu{
    width: 1200px;
    height: 21px;
    margin: 0 auto;
    margin-top: 23px;
    position: relative;
}
.error_button{
    width: 38px;
    height: 19px;
    border:1px solid #e6e6e6;
    text-align: center;
    line-height: 19px;
    font-size: 12px;
    color: #333333;
    float: right;
    cursor: pointer;
}
.evspan{
    display: block;
    width: 177px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    float: left;
}
.error_corrtion:hover{
    color: #ff5200 !important;
}
.error_button:hover{
    color: #ff5200 !important;
}