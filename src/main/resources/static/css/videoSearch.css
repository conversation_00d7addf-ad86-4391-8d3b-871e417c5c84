@charset "utf-8";
* {
	font-weight: normal
}

.videoLeft .list {
	padding-top: 12px;
	width: 874px
}

.videoLeft .list ul li {
	width: 282px;
	float: left;
	margin-right: 14px;
	margin-bottom: 20px;
}

.videoLeft .list ul li .pic {
	height: 186px
}

.videoLeft .list ul li .pic img {
	max-width: 100%;
	max-height: 100%
}

.videoLeft .list ul li:nth-child(3n) {
	margin-right: 0px
}

.videoLeft .list ul li .text h2 {
	font-size: 14px;
    height: 14px;
    line-height: 14px;
    font-weight: normal;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
        padding-top: 20px;
    padding-bottom: 11px;
}

.videoLeft .list ul li .pic {
	position: relative
}

.videoLeft .list ul li .pic .label {
	position: absolute;
	left: 0;
	top: 0
}

.videoLeft .list ul li .pic .label h3 {
	font-size: 12px;
	line-height: 26px;
	color: #ffffff;
	padding-left: 7px;
	padding-right: 7px;
	background-image: -moz-linear-gradient( 0deg, rgb(255, 108, 0) 0%, rgb(255, 66, 0) 100%);
	background-image: -webkit-linear-gradient( 0deg, rgb(255, 108, 0) 0%, rgb(255, 66, 0) 100%);
	background-image: -ms-linear-gradient( 0deg, rgb(255, 108, 0) 0%, rgb(255, 66, 0) 100%);
}

.videoLeft .list ul li .pic .time {
	position: absolute;
	right: 0px;
	bottom: 0px;
	background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: -o-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	width: 100%;
	padding-left: 0px;
	padding-right: 0px;
	border-radius: 0px
}

.videoLeft .list ul li .pic .time img {
	float: left;
	display: block;
	padding-top: 4px;
	padding-left: 7px;
	margin-right: 4px
}

.videoLeft .list ul li .pic .time h2 {
	float: right;
	font-size: 13px;
	color: #fff;
	line-height: 33px;
	padding-right: 9px;
	text-align: right;
}

.videoLeft .list ul li a {
	text-decoration: none
}

.videoLeft .list ul li:hover a h2 {
	color: #ff5200
}

.videoLeft .none_box h1 {
	padding-left: 52px;
	font-size: 20px;
	line-height: 36px;
	background-image: url("https://static.fangxiaoer.com/web/images/sy/house/house/warning1.png");
	background-repeat: no-repeat;
	background-position: left center
}

.videoLeft .none_box {
	padding-bottom: 28px;
	border-bottom: 1px dashed #e0e0e0
}

.videoLeft .tuijian {
	padding-top: 20px;
	margin-bottom: 8px
}

.videoLeft .tuijian h1 {
	font-size: 18px;
	height: 18px;
	line-height: 18px;
	border-left: 2px solid #333333;
	padding-left: 12px;
	font-weight: bold
}

.video_box .recommend {
	margin: 0
}

.video_box span {
	display: inline-block;
	width: 4px;
	height: 18px;
	margin-bottom: -1px;
	border-radius: 50px;
	background: #ff5200;
	margin-right: 8px;
	margin-left: 16px
}

.video_box {
	border: 1px solid #eaeaea;
}

.video_box .title {
	line-height: 48px;
}

.video_box img {
	display: block
}

.video_box p {
	line-height: 46px;
}

.video_box p a {
	color: #000000;
	margin-left: 12px;
	text-decoration: none
}

.video_box:hover a {
	color: #ff5200
}

.list ul li .pic .pic_sun {
	width: 100%;
	height: 100%
}

.hot a {
	text-decoration: none
}

.zsfw .btn {
	border-radius: 0px;
	background-color: #ff5200;
	color: #fff;
	font-size: 14px;
}

.video_box p {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-right: 12px
}

#hqyzm {
	background-color: #f2f2f2;
	color: #333333
}

.fxe_validateCode {
	background-color: #f2f2f2;
	color: #333333
}

#describe {
	font-family: 微软雅黑
}

strong {
	font-weight: bold
}

.hot li.news_list_tit a {
	font-weight: bold;
}

.zsfw {
	margin: 12px 0 20px 0;
}

.info{
	height: 30px;
	line-height: 30px;
}
.info .touxiang {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    float: left;
    background: url(https://static.fangxiaoer.com/m/static/images/video/noagent.png) no-repeat center;
    background-size: 100% 100%;
    margin-right: 9px;
    
}
.touxiang img{
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.video_r{
	float: right;
	    margin-right: 8px;
	        margin-top: 4px;
	
}
.video_ll {
    width: 16px;
    height: 12px;
    background-size: 100%;
    font-style: normal;
    float: left;
    margin-right: 4px;
        margin-top: 5px;
}
.mr14{
	    margin-right: 14px;
	
}

.video_r span {       
    font-size: 14px;
    color: #535353;
}
.video_dz {
  	width: 17px;
	height: 13px;
    background-size: 100%;
    margin-left: 4px;
    font-style: normal;
    float: left;
    margin-right: 4px;
        margin-top: 4px;
}
.video_r img {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    display: block;
}
.span_div {
    display: block;
    float: left;
    font-size: 14px;
    line-height: 22px;
}
.text a{
	color: #333;
}
