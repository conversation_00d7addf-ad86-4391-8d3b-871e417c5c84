@font-face {
    font-family: 'dinot-bold';   /*Ã¥Â­â€”Ã¤Â½â€œÃ¥ÂÂÃ§Â§Â°*//*/font/*/
    src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');       /*Ã¥Â­â€”Ã¤Â½â€œÃ¦ÂºÂÃ¦â€“â€¡Ã¤Â»Â¶*/
}

.footer{
    margin-top:0;
}

a:hover{
    text-decoration:none;
    color:#333;
}
body {
    background: #ffffff;
}

/* Ã©Â¦â€“Ã©Æ’Â¨banner */
.ranking-banner {
    height: 300px;
    width: 100%;
    min-width: 1170px;
    background-image: url("https://static.fangxiaoer.com/web/images/ranking/rank-banner_03.png");
    background-position: top;
    background-size: cover;
    text-align: center;
}

/* ======================================================================================= */
/* Ã©Â¦â€“Ã©Æ’Â¨tabÃ¥Ë†â€¡Ã¦ÂÂ¢Ã¦Â ÂÃ§â€ºÂ® */
.ranking-tab {
    width: 773px;
    height: 50px;
    margin: 0 0;
    display: flex;
    /*Ã¨Â®Â¾Ã§Â½Â®Ã¤Â¸ÂºflexÃ¥Â¸Æ’Ã¥Â±â‚¬*/
    justify-content: center;
    /*Ã¦Â°Â´Ã¥Â¹Â³Ã¥Â±â€¦Ã¤Â¸Â­*/
    align-items: center;
    /*Ã¥Å¾â€šÃ§â€ºÂ´Ã¥Â±â€¦Ã¤Â¸Â­*/
}

.ranking-tab a{
    width: 33.33%;
}

.ranking-tab a:hover{
    color: #ff5200;
}

.ranking-tab .layout {
    height: 50px;
    font-size: 16px;
    font-weight: bold;
    line-height: 50px;
    background: #ffffff url("https://static.fangxiaoer.com/web/images/ranking/P-11.svg");
    background-size: 28px 25px;
    background-repeat: no-repeat;
    background-position: 80px center;
    padding-left: 115px;
}

.ranking-tab .click {
    background: #ff6100 url("https://static.fangxiaoer.com/web/images/ranking/P-22.svg");
    color: #fff;
    background-size: 28px 25px;
    background-repeat: no-repeat;
    background-position: 80px center;
    /* text-align: center; */
}

/* ======================================================================================= */
/* Ã¦Å½â€™Ã¨Â¡Å’Ã¦Â¦Å“Ã¥â€°ÂÃ¤Â¸â€°Ã¥ÂÂ */
/* Ã¥â€¦Â¬Ã¦Å“â€°Ã¦Â Â·Ã¥Â¼Â */
.ranking-developer {
    height: 530px;
    width: 100%;
    min-width: 1170px;
    background-image: url("https://static.fangxiaoer.com/web/images/ranking/color.jpg");
    background-position: top;
    text-align: center;
    margin-top: 30px;
    display: flex;
    justify-content: center;
    padding-top: 30px;
}

.ranking-developer a{
    width: 414px;
    text-align: center;
    display: inline-block;
}

/* Ã¥â€¦Â¬Ã¦Å“â€°Ã¦Â Â·Ã¥Â¼ÂÃ§Â»â€œÃ¦ÂÅ¸ */

/* Ã©Â»ËœÃ¨Â®Â¤Ã¦Â Â·Ã¥Â¼Â*/
.ranking-developer .ranking-number-m {
    position: relative;
    top: 70px;
    height: 363px;
}

.ranking-developer .ranking-number-m h3 {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin-bottom: 40px;
}


.ranking-developer .ranking-number-m .developer-img {
    width: 216px;
    height: 216px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
}

.ranking-developer .ranking-number-m .developer-img img{
    height: 100%;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
}

.ranking-developer .ranking-number-m .flag {
    width: 216px;
    height: 151px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    position: relative;
    margin: 0 auto;
    margin-top: -216px;
    padding-top: 65px;
}

.ranking-developer .ranking-number-m .flag .crown {
    position: absolute;
    transform: rotate(33deg);
    margin-top: -75px;
    left: 155px;
    width: 37px;
}

.ranking-developer .ranking-number-m .flag h1 {
    display: none;
}

.ranking-developer .ranking-number-m .developer-info h2 {
    color: #ff5200;
    font-size: 28px;
    font-family: dinot-bold;
}

.ranking-developer .ranking-number-m .developer-info h2 span:nth-child(1) {
    font-size: 16px;
    color: #333333;
    font-weight: 400;
}

.ranking-developer .ranking-number-m .developer-info p:nth-child(2) {
    display: flex;
    justify-content: center;
    margin-top: 16px;
}

.ranking-developer .ranking-number-m .developer-info p:nth-child(2) span {
    background: #f3f5f7;
    color: #586c94;
    height: 24px;
    display: block;
    margin: 0 7px;
    padding: 0 10px;
}

.ranking-developer .ranking-number-m .developer-info {
    margin-top: 52px;
}

/* Ã©Â»ËœÃ¨Â®Â¤ */

/* Ã¥Â±â€¢Ã¥Â¼â‚¬ */
.ranking-developer .ranking-number-c {
    width: 340px;
    height: 490px;
    box-shadow: 0 0 15px #ababab;
    margin: 0 auto;
}

.ranking-developer .ranking-number-c h3 {
    display: none;
}

.ranking-developer .ranking-number-c .developer-img {
    width: 340px;
    height: 368px;
    position: absolute;
    overflow: hidden;

}

.ranking-developer .ranking-number-c .developer-img img{
    height: 100%;
    position: relative;
    left: 50%;
    transform: translateX(-50%);

}

.ranking-developer .ranking-number-c .flag {
    width: 216px;
    height: 168px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    position: relative;
    margin: 55px auto 0;
    top: 55px;
    padding-top: 48px;
    margin-top: 0;
}

.ranking-developer .ranking-number-c .flag .crown {
    position: absolute;
    transform: rotate(33deg);
    margin-top: -70px;
    left: 149px;
}

.ranking-developer .ranking-number-c .flag h1 {
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    margin-top: 13px;
    width: 80%;
    margin: 0 auto;
    line-height: 32px;
    position: relative;
    top: 7px;
}

.ranking-developer .ranking-number-c .developer-info {
    position: relative;
    top: 180px;
}

.ranking-developer .ranking-number-c .developer-info h2 {
    color: #ff5200;
    font-size: 28px;
    font-family: dinot-bold;
}

.ranking-developer .ranking-number-c .developer-info h2 span:nth-child(1) {
    font-size: 16px;
    color: #333333;
    font-weight: 400;
}

.ranking-developer .ranking-number-c .developer-info p:nth-child(2) {
    display: flex;
    justify-content: center;
    margin-top: 18px;
}

.ranking-developer .ranking-number-c .developer-info p:nth-child(2) span {
    background: #f3f5f7;
    color: #586c94;
    height: 24px;
    display: block;
    margin: 0 7px;
    padding: 0 10px;
}

/* Ã¨Â§Â¦Ã¥Ââ€˜Ã¥ÂÅ½Ã¦â€¢Ë†Ã¦Å¾Å“ */

/* Ã¦Å½â€™Ã¨Â¡Å’Ã¦Â¦Å“Ã¥â€°ÂÃ¤Â¸â€°Ã¥ÂÂÃ¦Â Â·Ã¥Â¼Â */
/* ======================================================================================= */

/* Ã¥â€°ÂÃ¥ÂÂÃ¥ÂÂÃ¦Â¦Å“Ã¥Ââ€¢Ã¦Â Â·Ã¥Â¼Â */
/* Ã¦Â¦Å“Ã¥Ââ€¢Ã¥â€¦Â¬Ã§â€Â¨Ã¦Â Â·Ã¥Â¼Â */
.ranking-topten {
    width: 773px;
    background: #ffffff;
    /* height: 3186px; */
    padding-top: 0px;
    margin-top: 20px;
}

.ranking-topten .ranking-star {
    width: 773px;
    /* height: 300px; */
    margin: 0 0;
    display: flex;
    align-items: flex-start;
    /* justify-content: center; */
    /* background: goldenrod; */
    padding-top: 26px;
    margin-bottom: 26px;
}

.ranking-topten .ranking-star .star-img {
    width: 230px;
    height: 162px;
    border: 0px solid #fff;
    border-radius: 0;
    overflow: hidden;
}

.ranking-topten .ranking-star .star-img img{
    width: 230px;
    /* width: 230px; */
    height: 162px;
    /* position: relative; */
    top: 0;
    transition: all 1s;
}

.ranking-topten .ranking-star .star-img .img-hover{
    transform: scale(1.1);
}

.ranking-topten .ranking-star .star-center {
    background: #e0e0e0;
    width: 1px;
    height: 316px;
    margin: 0 48px;
}

.ranking-topten .ranking-star .star-center .sign {
    display: block;
    width: 11px;
    height: 11px;
    background: #999999;
    border-radius: 50%;
    position: relative;
    top: 23px;
    left: -12px;
    border: 7px solid #fff;
    box-shadow: 0px 3px 5px #b5b5b5
}

.ranking-topten .ranking-star .star-center .arrow {
    display: block;
    position: relative;
    top: -9px;
    width: 0;
    height: 0;
    border-top: 20px solid transparent;
    border-bottom: 20px solid transparent;
}

.ranking-topten .ranking-star .star-info {
    width: 483px;
    height: 162px;
    /* background: #ff5200; */
}

.ranking-topten .ranking-star .star-info .serial {
    width: 108px;
    height: 108px;
    overflow: hidden;
    border-radius: 50%;
}

.ranking-topten .ranking-star .star-info .serial span {
    width: 108px;
    height: 108px;
    overflow: hidden;
    border-radius: 50%;
    position: absolute;
}

.ranking-topten .ranking-star .star-info .serial img {
    height: 100%
}

.ranking-topten .ranking-star .star-info .info-detailed {
    width: 459px;
    height: 159px;
    background: #fff;
    margin: 0px auto;
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
    border-radius: 0;
    text-align: center;
    margin-left: 24px;
}

.ranking-topten .ranking-star .star-info .info-detailed h1 {
    font-size: 22px;
    font-weight: bold;
}

.ranking-topten .ranking-star .star-info .info-detailed h1:hover{
    color: #ff5200;
}

.ranking-topten .ranking-star .star-info .info-detailed h1 span {
    font-size: 14px;
    color: #333333;
    line-height: 14px;
    font-weight: 500;
    position: relative;
    top: -2px;
}

.ranking-topten .ranking-star .star-info .info-detailed .tag {
    display: flex;
    margin-top: 55px;
}

.ranking-topten .ranking-star .star-info .newproject .tag{
    margin-top: 22px;
}
.ranking-topten .ranking-star .star-info .info-detailed .tag span {
    display: block;
    height: 21px;
    font-size: 14px;
    padding: 0 13px;
    line-height: 21px;
}

.ranking-topten .ranking-star .star-info .info-detailed .tag .y {
    background: #fff4ed;
    color: #ff5200;
    height: 30px;
    /* width: 46px; */
    padding: 0 8px;
    text-align: center;
    line-height: 30px;
}

.ranking-topten .ranking-star .star-info .info-detailed .tag .b {
    background: #f3f5f7;
    color: #596c91;
    font-size: 14px;
    padding: 0px 0;
    display: inline-block;
    /* width: 72px; */
    text-align: center;
    height: 30px;
    padding: 0 8px;
    line-height: 31px;
    /* background: #fff4ed; */
    /* color: #ff5200; */
}

.ranking-topten .ranking-star .star-info .info-detailed .msg {
    color: #333333;
    margin-top: 0;
    overflow: hidden;
}

.ranking-topten .ranking-star .star-info .info-detailed .msg span{
    display: inline-block;
    font-size: 14px;
    margin-top: 12px;
    /* line-height: 16px; */
    /* height: 16px; */
    /* float: left; */
    color: #666666;
}
.ranking-topten .ranking-star .star-info .newproject .msg span{

    margin-top: 8px;
}
.mark {
    background: url(https://static.fangxiaoer.com/web/images/ranking/border.svg);
    display: inline-block;
    width: 27px;
    height: 21px;
    text-align: center;
    line-height: 19px;
    color: #666666;
    font-size: 14px;
    margin: 0 4px;
    font-weight: 400;
    padding-left: 1px;
}

.ranking-topten .ranking-star .star-info .info-detailed .msg .housetype{
    font-size: 14px;
    margin-left: 0;
    color: #666666;
    margin-right: 6px;
}

.ranking-topten .ranking-star .star-info .info-detailed .msg b {
    font-size: 30px;
    color: #ff5200;
    font-family: dinot-bold;
    float: right;
    margin-right: 25px;
}

.ranking-topten .ranking-star .star-info .info-detailed .msg b span {
    margin: 0;
    font-size: 14px;
    color: #666666;
    font-weight: 400;
}


/* Ã¦Å½â€™Ã¨Â¡Å’Ã¦Â¦Å“Ã¥â€ºÂ¾Ã¦Â â€¡1-10 */
.ranking-topten .ranking-star .star-info .list-1 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 1080px;
}

.ranking-topten .ranking-star .star-info .list-2 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 972px;
}

.ranking-topten .ranking-star .star-info .list-3 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 864px;
}

.ranking-topten .ranking-star .star-info .list-4 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 756px;
}

.ranking-topten .ranking-star .star-info .list-5 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 648px;
}

.ranking-topten .ranking-star .star-info .list-6 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 540px;
}

.ranking-topten .ranking-star .star-info .list-7 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 432px;
}

.ranking-topten .ranking-star .star-info .list-8 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 324px;
}

.ranking-topten .ranking-star .star-info .list-9 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 216px;
}

.ranking-topten .ranking-star .star-info .list-10 {
    background: url("https://static.fangxiaoer.com/web/images/ranking/list-num.png");
    background-position: 108px;
}

/* Ã¥â€ºÂ¾Ã¦Â â€¡Ã¦Â Â·Ã¥Â¼ÂÃ§Â»â€œÃ¦ÂÅ¸ */
/* Ã¦Â¦Å“Ã¥Ââ€¢Ã¥â€¦Â¬Ã§â€Â¨Ã¦Â Â·Ã¥Â¼Â */
/* ====================================================================== */
/* Ã¦Å½â€™Ã¥ÂÂÃ¥Ë†â€”Ã¨Â¡Â¨Ã¥Â·Â¦Ã¥ÂÂ³Ã¤Â½ÂÃ§Â½Â®Ã¨Â°Æ’Ã¦ÂÂ¢Ã¨Â¡Â¥Ã¥â€¦â€¦Ã¦Â Â·Ã¥Â¼Â */

/* Ã©Â¡Â¹Ã§â€ºÂ®Ã¥â€ºÂ¾Ã§â€°â€¡Ã¦Å’â€¡Ã¥Ââ€˜Ã¥Â°â€“Ã¨Â§â€™Ã¦Â â€¡Ã¨Â¯â€  */
.ranking-topten .ranking-star .star-center .arrow-left {
    border-left: 20px solid white;
    left: -50px;
}

.ranking-topten .ranking-star .star-center .arrow-right {
    border-right: 20px solid white;
    left: 30px;
}



/* Ã¦Â â€¡Ã§Â­Â¾Ã¥Â·Â¦Ã¥ÂÂ³Ã¥Å’ÂºÃ¥Ë†Â« */
.ranking-topten .ranking-star .star-info .info-detailed .tag-left {
    justify-content: flex-start;
}

.ranking-topten .ranking-star .star-info .info-detailed .tag-left span {
    margin-right: 10px;
}

.ranking-topten .ranking-star .star-info .info-detailed .tag-right {
    justify-content: flex-end;
}

.ranking-topten .ranking-star .star-info .info-detailed .tag-right span {
    margin-left: 8px;
}

/* Ã¦Â â€¡Ã§Â­Â¾Ã¥Â·Â¦Ã¥ÂÂ³Ã¥Å’ÂºÃ¥Ë†Â« */

/* Ã¤Â¿Â¡Ã¦ÂÂ¯Ã¥Ââ€¢Ã¥ÂÅ’Ã¦Å½â€™Ã¥ÂÂÃ¦â€¢Â°Ã©â„¢â€žÃ¥Å Â Ã¦Â Â·Ã¥Â¼Â */
.ranking-topten .ranking-star .star-info .detailed-right {
    float: left;
    text-align: left;
    margin-top: 3px;
}

.ranking-topten .ranking-star .star-info .detailed-left {
    float: right;
    text-align: right;
}


/* Ã¤Â¿Â¡Ã¦ÂÂ¯Ã¥Ââ€¢Ã¥ÂÅ’Ã¦Å½â€™Ã¥ÂÂÃ¦â€¢Â°Ã©â„¢â€žÃ¥Å Â Ã¦Â Â·Ã¥Â¼Â */

/* Ã¦Å½â€™Ã¥ÂÂÃ¥â€ºÂ¾Ã¦Â â€¡Ã¥Ââ€¢Ã¥ÂÅ’Ã¦â€¢Â°Ã¦Â Â·Ã¥Â¼Â */
.ranking-topten .ranking-star .star-info .serial-right {
    margin: 12px 0 0 360px;
    position: absolute;
}

.ranking-topten .ranking-star .star-info .serial-left {
    margin: 12px 0 0px 65px;
    position: absolute;
}

/* Ã¦Å½â€™Ã¥ÂÂÃ¥â€ºÂ¾Ã¦Â â€¡Ã¥Ââ€¢Ã¥ÂÅ’Ã¦â€¢Â°Ã¦Â Â·Ã¥Â¼Â */

/* ================================================================ */
.ranking-declare {
    background: #ffffff;
    height: 96px;
}

.ranking-declare p {
    text-align: center;
    margin: 0 auto;
    background: #ffffff;
    width: 1170px;
    margin-top: 29px;
    height: 65px;
    border: 1px dashed #d9d9d9;
    line-height: 65px;
}
.star_bg{
    width:32px;
    height: 162px;
    color: #272727;
    font-size: 26px;
    background-color: #f2f2f2;
    text-align: center;
    line-height: 162px;
    font-family: dinot-bold;
    margin-left: 20px;
    margin-right: 8px;
}
.reasons{
    padding: 16px 25px;
    background-color: #f3f5f7;
    font-size: 14px;
    color: #272727;
    font-weight: bold;
    margin: -10px 20px 26px 20px;
}
.reasons span{
    font-weight: 400;
}
.divlist{
    border-bottom:1px solid #f2f2f2;
}
.content{
    width: 1170px;
    margin: auto;
}
.geng{
    position: relative;
    bottom: -18px;
    margin-right: 8px;
}
.newproject .geng{
    bottom: -14px;
}
.bodyBG{
    background-color: #f2f2f2;
    padding-bottom: 30px;
}
.rank_area{
    width: 773px;
    height: 132px;
    background-color: #FFFFFF;
    border-top: 1px solid #f2f2f2;
}
.rank_area ul{
    overflow:hidden;
    /* width:712px; */
    /* margin:auto; */
    padding-left: -34px;
    margin-left: -7px;
    margin-top: 26px;
}
.rank_area li{
    float:left;
    width:85px;
    margin-left:38px;
    border: 1px solid #ededed;
    text-align: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: #272727;
    cursor: pointer;
}
.rank_area li:hover{
    border: 1px solid #ff5200;
}
.rank_area li:hover a{
    color: #ff5200;
}
.areaBg{
    background-color: #ff6100;
    border: 1px solid #ff6100!important;
    color: #FFFFFF !important;
}
.areaBg a{
    color: #ffffff !important;
}
.areaBg:hove  a{
    color:#ffffff !important;
}
.content{
    overflow: hidden;
}
.contentLeft{
    float:left;

}
.contentRight{
    float: right;
    width: 380px;
    background-color: #f2f2f2;
}
.childLtit{

    font-size: 16px;

    color: #272727;

    height: 60px;

    line-height: 60px;

    padding: 0px 0 0px 22px;

    border-bottom: 1px solid #f2f2f2;
}
.daiding{

    margin-bottom: 25px;
}
.childLtab{

    padding: 10px 0 10px 22px;

    font-size: 14px;

    color: #272727;

    font-weight: bold;

    border-bottom: 1px dashed #ededed;
}
.chiListRank{

    width: 17px;

    height: 17px;

    background-color: #dbdbdb;

    text-align: center;

    line-height: 17px;

    border-radius: 90px;

    float: left;

    font-size: 12px;

    color: #ffffff;

    /* display: inline-block; */

    margin: 13px 11px 0 26px;
}
.crankList{
    float: left;
    width: 297px;
    color: #272727;
    font-size: 14px;
    margin-top: 10px;
    margin-left: 18px;
}
.crankList li{
    float:left;
    /* width:33.33% */
}
.crankList li:first-child{

    margin-right: 19px;

    width: 99px;

    overflow: hidden;

    white-space: nowrap;

    text-overflow: ellipsis;
}
.crankList li:nth-child(2){

    margin-right: 30px;

    width: 80px;

    text-align: right;
}
.crankList li:nth-child(3){
    width:56px;
    text-align:right;
}
.crankList span{
    color:#ff5200;
}
.chitabN{
    margin-right: 25px;
}
.chitabL{
    margin-right: 77px;
}
.chitabJ{
    margin-right: 74px;
}
.childlist{
    margin-bottom:20px;
    background-color: #ffffff;
    padding-bottom: 20px;
}
.childlist:last-child{
    margin-bottom:0px;
}
.crankDetailsdiv img{
    width:100px;
    height:70px;
    float: left;
    margin-right: 12px;
    margin-top: 1px;
}
.crankDetails{

    float: right;

    width: 298px;

    background-color: #f3f5f7;

    margin-right: 24px;

    margin-top: 14px;

    /* margin-bottom: 16px; */
}
.crankDetailsdiv{

    padding: 10px 0 10px 10px;
}
.crankDetailsP span{

    color: #ff5200;

    display: inline-block;
}
.crankDetailsreason{
    margin: 0px 8px 12px 14px;
    padding-top: 1px solid #f2f2f2;
    font-weight: bold;
    border-top: 1px dashed #d9d9d9;
    padding-left: -4px;
    padding-left: 1px;
    padding-right: 4px;
    padding-top: 7px;
}
.crankDetailsdivR h2{

    font-weight: 500;

    overflow: hidden;

    white-space: nowrap;

    text-overflow: ellipsis;
}
.crankDetailsreason span{
    font-weight:500;
}
.chilistLi{
    overflow:hidden;
    cursor: pointer;
}
.crankDetails{
    display: none;
}
.spcolor{
    background-color:#ff5200;
    color:#ffffff;
    font-size: 12px;
    line-height: 17px;
    text-align: center;
}
.DDlist{
    color: #ff5200;
    text-align: center !important;
}
.Ljun{
    color: #333333 !important;
    width: 22px;
    height: 18px;
    line-height: 18px;
    padding-left: 2px;
    /* margin-top: 7px; */
    /* padding-top: 3px; */
    text-align: center;
    background: url(https://static.fangxiaoer.com/web/images/ranking/border3.svg) no-repeat;
    background-size: 100% 100%;
}
.lGeng{
    border-left: 1px solid #dddddd;
    display: inline-block;
    height: 11px;
    padding-top: 0px;
    line-height: 10px;
    padding-left: 8px;
    margin-left: 4px;
}
.Early{
    display: inline-block;
    color: #ec1b03;
    font-weight: bold;
    width: 155px;
    font-size: 14px;
    border: 1px solid #ec1b03;
    height: 18px;
    line-height: 17px;
    text-align: center;
    margin-top: 18px;
    margin-bottom: 0px;
}
.Early span{
    font-family: dinot-bold;
}
.crankDetailsP{
    color:#ff5200;
}