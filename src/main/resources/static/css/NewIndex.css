
.head_nav ul li a {display: block;font-size: 14px;color: #333;}

.head_nav ul li a:hover{color:#ff5200}
.hot_red{color:#ff5200!important}
.w1170{margin-bottom: 0!important;padding-bottom: 0px !important;}
.head_nav ul li p a{font-size: 14px}
.city{background-image: url(https://static.fangxiaoer.com/web/images/sy/index_new/indexCitySan.png);background-position: 54px center;background-size: 8px;font-size: 14px;width: 76px;}
.navigation{background-color: #fff;padding-top: 25px;padding-bottom: 22px;border-bottom: 0;}
.navigation h2 {float: none;text-align: left; height: auto;line-height: normal; }
.navigation h2 a{color: #333333;font-weight: bold}
.navigation .box{border-right: 0;position: relative;padding-bottom: 5px;}
.navigation .box:nth-child(1) h2{padding-left: 45px}
.navigation .box:nth-child(2) h2{padding-left: 60px}
.navigation .box:nth-child(3) h2{padding-left: 49px}
.navigation .box:nth-child(4) h2{padding-left: 52px}
.navigation .box:nth-child(5) h2{padding-left: 37px}
.navigation .box:nth-child(6) h2{padding-left: 37px}
.navigation .box ul{margin-top: 5px;}
.navigation .box .line{ width: 0px; height: 57px; border-right: 1px dashed #dddddd; position: absolute; right: 0; top: 15px}
.index_block .index_title{border: 0;background-color: #f3f3f3;height: 50px;}
.mxf_h1 a{font-size: 22px;color: #333333;font-weight:  normal;margin-left:  7px;}
.index_title li.hover {background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/index_li_bor1.png) !important;background-color: #f3f3f3 !important;}
.index_title li.hover a{/* background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/index_li_bor.png) !important; */color:  #333333;font-weight:  bold;}
.index_title li{/* background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/index_li_bor.png) !important; */background-position: right center;}
.index_title li span{background: none !important;}
.index_title li a{line-height: 50px;background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/index_li_bor.png) !important;background-position: left center;background-repeat:  no-repeat;color: #333;}
.bztj_middle .swiper-wrapper a {margin: 0;width: 428px !important;height: 196px !important;background-size: 100% 100%;}
.new_left h1{float: left;font-size: 14px;color: #333333;font-weight: bold;margin-right: 16px;/* font-size: 12px; */height: 12px;line-height: 12px;}
.new_left a{/* border-right: 1px solid #e6e6e6; */line-height: 12px;margin-right: 0;padding-left: 0px;padding-right: 0;/* margin-bottom: 11px; */color: #666666;font-size: 12px;vertical-align: top;margin-right: 12px;/* margin-top: 14px; */}

.new_left a:hover{color:#ff5200}
.index_screen_line{margin-right: 14px !important;border-right: 1px solid #e6e6e6;padding-right: 14px !important;}
.bztj_middle #con_mxfqh_1 a big{color: #ff5200;font-size: 14px;}
.bztj_middle #con_mxfqh_2 a big{color: #ff6200}
.bztj_middle #con_mxfqh_3 a big{color: #ff6200}
.bztj_middle #con_mxfqh_4 a big{color: #ff6200}
.bztj_middle #con_mxfqh_5 a big{color: #ff6200}
.bztj_middle .bztj_price{margin-top: 11px;margin-bottom: 3px}
.bztj_right .Headline img{ display: block}
.bztj_right .Headline a{font-size: 20px;color: #333333;font-weight: bold;height: 24px;overflow:  hidden;display:  block;margin-top: 27px !important;text-align: center;}
.bztj_left{/* margin-left: 12px; */margin-right: 22px;margin-top: 20px;}
.index_title_right{padding-right: 15px;margin-top: 14px;color: #333;}

.dkjsq_icon{background-image: url("https://static.fangxiaoer.com/web/images/indexReversion/Calculator.png");width:  15px;height: 16px;background-position: 0 0;margin-top: 5px;}
.dynamic{margin-top: 22px;margin-bottom: 7px;}
.dynamic .name{font-size: 16px;color: #333333;font-weight: bold;border-left: 3px solid #333333;padding-left: 8px;display:  inline-block;height:  16px;line-height: 16px;}
.mxf_ul .bztj_li{padding-left: 10px;color: #333;}

.mxf_ul .bztj_li:hover{color:#ff5200}

.dynamic .index_ul{border-top: 1px dashed #ebebeb;margin-top: 9px;}
.dynamic .index_ul li{ line-height: 14px;margin-top: 14px}
.vido .name{font-size: 16px;color: #333333;font-weight: bold;border-left: 3px solid #333333;padding-left: 8px;display:  inline-block;line-height: 16px;}
.bztj_right h2{border-bottom: 1px dashed #ebebeb;margin-bottom: 12px;}
.vido h2{border-bottom: 1px dashed #ebebeb !important;margin-bottom: 12px;}
.vido .new_video{margin-top: 14px}
.index_h1 .name{font-size: 22px;display: block;float: left;font-weight:  normal;}
.bztj_h1{width: 44px;margin-left: 14px;margin-right: 0px;}
.index_dzyh{margin-top: 19px;margin-left: 10px;color: #333;}
.buy{ float: right;margin-right: 12px}
.buy a{line-height: 50px;font-size: 14px;color: #333333;background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/buy.png);background-repeat: no-repeat;padding-left: 24px;background-position:left center;}
.swiper-container{margin-top: 0!important;height: 196px;}

.bztj_left h2 a{margin-top: 0px;font-size: 14px;}
.bztj_middle .pic_wei{margin-top: 17px}
.bztj_middle .pic_wei:nth-child(1){margin-top: 0px}
.bztj_middle .pic_wei:nth-child(2){margin-top: 0px}
.bztj_middle .pic_wei:nth-child(3){margin-top: 0px}
.bztj_right{margin-top: 0!important;overflow: hidden;/* margin-bottom: 20px; */}
.bztj_right a{margin-top: 0!important;margin-bottom: 0px !important;font-size:  14px;color: #666;}

.swiper-container a{margin-top: 0!important;}
.esf_right img{margin-top: 0}
.excellentAgent{background-color: #ebebeb;height: 164px!important; margin-top: 48px!important;}
.excellentAgent .header{width: 90px;height: 90px;border-radius: 50%;margin: 0 auto;display: block;margin-top: -36px;}
.excellentAgent  .bztj_title{position:static;background: none;text-align: center;font-size: 16px;color: #333333;font-weight: bold;height:  16px;line-height: 16px;margin-top: 11px;}
.fcphb s{border-radius: 50%;width:  15px;height: 15px;line-height:  15px;background-color: #ff5200;}
.jjr{width: 83px!important;height: 25px!important;margin:0 auto;position:relative;margin-top: -9px;}
.ye h1{/* text-align:center; */font-size:14px;margin-top: 13px;line-height: 14px;color: #333;font-weight:  normal;margin-left: 44px;}
.qu h1{/* text-align:center; */font-size:14px;line-height: 14px;margin-top: 6px;color:  #333;font-weight:  normal;margin-left: 44px;}

.index_h1 a{
    font-size:22px !important;
    color:#333!important;
    margin-left:15px;
    font-weight:  normal;
}
.mesf_h1{width: 81px!important;}
.zf_h1{ width:59px!important}
.fcphb .index_h1  span{font-size:22px;font-weight:normal;margin-left:15px;display:  block;float:  left;}

.fcphb .index_h1 a{font-size:12px!important;font-weight:normal;display:  block;float:  left;cursor:  pointer;}

.fcphb .index_h1 a i{
    background-image: url(https://static.fangxiaoer.com/web/images/indexReversion/sh.png);
    background-position: 0;
    width: 15px;
    height: 14px;
    display: block;
    margin-top: 5px;
    float:left;
    background-repeat:  no-repeat;
}

.fcphb .index_h1 a label{display:block;float:left;
    margin-left:8px}
.new_wei{margin-bottom: 20px;height: 50px;}
.bg-bannerK{z-index: 0}
.footer{margin-top: 0!important;}

.index_screen_line_ts{border-right:1px solid #e9e9e9;padding-right:15px!important;margin-right: 9px!important;}


.new_left{border-bottom:1px dashed #e9e9e9;padding-top: 20px;}
.new_left:nth-child(1){padding-top:0px}
.esf_right h2{margin-top: 10px;}

.fcphb{margin-top: 20px !important;}

.esf_left .new_left{padding-bottom: 8px;}



.mai_wei{background: url(https://static.fangxiaoer.com/web/images/indexReversion/sell.png) no-repeat;background-position:0;margin-top: 2px;}

.zhu_wei{background: url(https://static.fangxiaoer.com/web/images/indexReversion/lease.png) no-repeat;background-position:0;margin-top: 2px;}

.lou_wei{background: url(https://static.fangxiaoer.com/web/images/indexReversion/lou.png) no-repeat;background-position:0;margin-top: 2px;}


.sh{background: url(https://static.fangxiaoer.com/web/images/indexReversion/sh.png) no-repeat;background-position:0;}




.di{background: url(https://static.fangxiaoer.com/web/images/indexReversion/di.png) no-repeat;background-position:0;
    width:13px; height:15px}
.index_title li:nth-child(1) a{background: none;background-repeat:  no-repeat;background-position: -15px;}


.fcphb .index_h1 a label{cursor:pointer}
.fcphb .index_h1 a:hover label{color:#ff6200}

.zhufang .new_left{padding-bottom: 9px;margin-top: 0px;}

.footer_copy a{color:#999}
.index_title li{background:none !important}
.cff5200 {
    color: #ff5200;
    line-height: 14px;
    margin-top: 10px;
}

.esf_layout {
    color: #333;
    /* font-size: 14px; */
    margin: 14px 0 10px;
    line-height: 15px;
    position: relative;
}

.bztj_middle a{height:196px;margin: 17px 20px 0 0;}
.index_h1{margin-top: 14px;margin-right: 0;width: auto !important;font-size: 22px;font-weight:  normal;}
.scroll li span{color:#333}

.bztj_left li span {
    color: #666;
}
.bztj_left li .rw{display:inline-block;text-align:  right;float: right;margin-right: 14px;}
.bztj_left li a {
    color: #333;
}
.scroll li p i.price{color:#666;display:  inline-block;}


.fcphb li{height:15px}

.fcphb ul{border-right:1px dashed #e6e6e6}

.bztj_left h2:nth-child(1) a{margin-bottom: 2px;}


.link{padding-top:20px}
.w1170{padding-bottom: 1px!important;}
.navigation h2 a:hover{color:#ff5200}
.no_banner{ width:100%; height:20px}
.rmxq ul {border-right:0}


.n1 a:hover{color:#ff5200}

.esf_right h2{/* border-bottom:0 */}


.news_w{margin-top:5px}
.navigation .box ul a{color:#333}

.bztj_right h2:nth-child(1){border:0}
.bztj_middle .bztj_price span big{color:#ff5200;font-size: 14px;}
.esf_layout i big{font-size: 14px;}

.navigation ul li:hover{color:#ff5200}

.bztj_right h2 a.more:hover{color:#ff5200 !important}
.buy a:hover{color:#ff5200}

.fcphb a:hover{color:#ff5200!important}


.rplp ul li p{overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;}

.zxzx ul li p{overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;}

.rplp ul li p{overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;}


.more:hover{color:#ff5200}

#wei:hover{#ff5200}
.Headline a:hover{color:#ff5200}
.bztj_left ul li a:hover{color:#ff5200}

.shangpu a{margin-right:0}
.shangpu .new_left{border-bottom:0}
.shangpu .new_left a{/* margin-top: 18px !important; */}
.shangpu .new_left{padding: 0px !important;line-height:  normal;}
.shangpu .new_left:nth-child(2){/* margin-top:20px */}
.shangpu .new_left:nth-child(3){/* margin-top:20px; */}
.shangpu .new_left:nth-child(4){/* margin-top: 10px; */padding-top: 20px !important;border-top: 1px dashed #e9e9e9;}
.shangpu .new_left:nth-child(5){/* margin-top:20px */}
.shangpu .new_left:nth-child(6){/* margin-top:20px */}
.shangpu .new_left:nth-child(1){width: 147px;}
.shangpu .new_left:nth-child(1) a:nth-of-type(1){margin-top: 0 !important;}

.shangpu .new_left:nth-child(2) a:nth-of-type(1){margin-top:0!important}
.shangpu .new_left:nth-child(2) a:nth-of-type(2){margin-top:0!important}

.shangpu .new_left:nth-child(3) a:nth-of-type(1){margin-top:0!important}
.shangpu .new_left:nth-child(3) a:nth-of-type(2){margin-top:0!important}

.shangpu .new_left:nth-child(4) a:nth-of-type(1){margin-top:0!important}
.shangpu .new_left:nth-child(4) a:nth-of-type(2){margin-top:0!important}
.shangpu .new_left:nth-child(5) a:nth-of-type(1){margin-top:0!important}

.shangpu .new_left:nth-child(6) a:nth-of-type(1){margin-top:0!important;margin-bottom: 0;}
.shangpu .new_left:nth-child(6) a:nth-of-type(2){margin-top:0!important;margin-bottom: 0;}


.ershouf  .new_left:nth-child(6) a:nth-of-type(1){margin-top:0!important;margin-bottom: 0 !important;}
.ershouf .new_left:nth-child(6) a:nth-of-type(2){margin-top:0!important;margin-bottom: 0 !important;}


.soi{background: url(https://static.fangxiaoer.com/web/images/indexReversion/shopSelectIcon.png);
    width:15px; height:15px }
.soi_wei{background: url(https://static.fangxiaoer.com/web/images/indexReversion/shopPublishIcon.png);background-repeat:no-repeat;background-position:center;margin-top: 3px;}
.zf_right{margin-top: 17px !important;margin-bottom: 20px;}
.zf_right img{margin-top:0}

.new_house .new_left{border:0;padding: 0;margin-bottom: 3px;}
.new_house .new_left a{display:block;float:left;margin-bottom:15px;}
.new_house .new_left .order{margin-right:14px;padding-right:14px}
.ershoufang .new_left a{display:block;float:left;margin-bottom:15px}
.shangpu .new_left a{display:block;float:left;margin-bottom: 19px;}
.new_house .new_left:nth-child(5) a:nth-child(7){margin-bottom:0}
.new_house .new_left:nth-child(5) a:nth-child(8){margin-bottom:0}
.new_house .new_left:nth-child(5) a:nth-child(9){margin-bottom:0}
.middle {margin-top:0 !important;margin-bottom: 0px !important;margin-bottom: 3px !important;}
.tlBannerLast{ margin-bottom:20px !important}
.yh_right {margin-bottom:20px}
.xzl_right{margin-top: 17px !important;}

#qdelay2{background-color: rgba(0,0,0,0.6);width: 633px;padding-bottom: 15px;margin-left: -316px;height: auto;border-radius: 5px;top: 285px;padding-bottom: 8px;}
.bannerLoop span{margin-right: -596px;top: 372px;border-radius: 0;}
.index_search_input{width: 536px;height: 46px;border-radius: 3px 0 0 3px;font-size: 15px;}
.index_search_btn{width: 56px;height: 46px;border-radius: 0 3px 3px 0;}
.search_div{width: auto; margin-left: 16px}
.search li{margin: 0;height: 42px}
.search li a{display: block;line-height: 31px;padding-left: 13px;padding-right: 13px;text-shadow:none;font-size: 14px;border-radius: 3px;font-weight: normal;color: #fff;}
.search .hover a{background-color: #ff5200;font-weight: normal;font-size: 14px;border-radius: 3px;}
.search ul{margin-left: 16px}
.search li.hover {
    background: url(https://static.fangxiaoer.com/web/images/ico/Choice.png) bottom no-repeat;
}


#qdelay2 input::-webkit-input-placeholder {

    color: #aab2bd;


}



.ui-autocomplete{/* display: block !important; */}
.high_light_span {
    line-height: 22px;
    display: inline-block;
    border: 1px solid #ededed !important;
    background-color: #fff !important;
    margin-left: 4px;
}
.ui-menu .ui-menu-item {padding: 0 !important;border: 0 !important;line-height: 30px;/* padding-left: 12px !important; *//* padding-right: 12px !important; */}
.ui-menu .ui-menu-item a{
    line-height: 30px !important;
    padding: 0;
    padding: 0px .4em !important;
}
.ui-menu {padding: 0}
.ui-state-focus{border:1px solid #ededed!important;background:#ededed !important;font-weight:normal;color:#2b2b2b}
.ui-autocomplete{/* padding-right: 12px !important; *//* padding-left: 12px !important; */width: 520px;}
.ui-autocomplete{width: 544px !important;border: 1px solid #ededed !important;box-shadow: 0px 2px 21px rgba( 0, 0, 0,0.1 );}
.high_light_span02{padding: 0 5px;
    border-radius: 4px;
    line-height: 22px;
    display: inline-block;
    border: 1px solid #eee !important;
    background: #fff;margin-left: 6px}
.ui-autocomplete{padding: 0 !important;border-radius: 0 !important;padding-top: 6px !important;padding-bottom: 6px !important;}
.ui-corner-all{border-radius:3px 0 0 3px !important;}


.ui-corner-top{border-radius:3px 0 0 3px !important}

.tlBanner{margin-top:0px !important}
.ershoufang .new_left:nth-child(4) a:nth-of-type(1){margin-top:0!important;margin-bottom: 0 !important;}
.ershoufang .new_left:nth-child(4) a:nth-of-type(2){margin-top:0!important;margin-bottom: 0 !important;}
/*æ¥¼ç›˜æŽ’è¡Œæ¦œå…¥å£*/


.geng_d {
    background: url(/img/listGD.png) no-repeat !important;
    background-position: 0;
    margin-top: 6px;
    margin-right: 0px;
}
.listBuilding{
    overflow: hidden;
    margin-top: 20px;
}
.listBuilding li{
    float: left;
}
.listBuilding li:first-child .listB{
    width:345px;
    height:223px;
}
.liFirst{
    width: 345px;
    position: relative;
}
.listN{
    position: absolute;
    z-index: 9;
    left: 10px;
    top: -2px;
}
.listB{
    /* position: relative; */
    /* width: 345px; */
}
.liFbg{
    width: 345px;
    height: 223px;
    position: absolute;
    top: 0;
    opacity: 0;
    /* background-color: #00000091; */
    transition: all 600ms ease;
    background-color: #00000024;
    transition: all 600ms ease;
}
.liFbg:hover{
    opacity: 1;
}
.liFbg2{
    width:255px;
    height:188px;
    opacity: 0;
    /* background-color: #00000091; */
    transition: all 600ms ease;
}
.liFbg2:hover{
    background-color: #00000091;
    opacity: 1;
}
.liFbg h2{
    font-size: 14px;
    color: #ffffff;
    font-weight: bold;
    position: absolute;
    top: -29px;
    left: 35px;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}
.liFbg:hover h2{
    top:58px;
}
.liFbg2 h2{
    top: -20px;
    left:22px;
    z-index:99;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}
.liFbg2:hover h2{
    top: 46px;
    z-index:99;
}
.liFbg img{
    position: absolute;
    left: 35px;
    top: -2px;
    z-index: 99;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}
.liFbg:hover img{
    top:85px;
}
.liFbg2 img{
    top: -3px;
    left:22px;
    z-index: 99;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}
.liFbg2:hover img{
    top: 73px;
}
.liFbg p{
    font-size: 14px;
    color: #ffffff;
    width: 270px;
    position: absolute;
    top: 225px;
    left: 35px;
    text-align: justify;
    line-height: 20px;
    overflow: hidden;
    text-overflow:ellipsis;
    display:-webkit-box;
    -webkit-box-orient:vertical;
    -webkit-line-clamp: 4;
    z-index: 99;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}
.liFbg:hover p{
    top: 105px;
}
.liFbg2 p{
    width:210px;
    top: 185px;
    left:22px;
    transition: all 500ms ease;
    -webkit-transition: all 500ms ease;
    -ms-transition: all 500ms ease;
    -o-transition: all 500ms ease;
}
.liFbg2:hover p{
    top:89px;
}
.liFbt{
    background-color: #0000004d;
    width: 345px;
    overflow: hidden;
    height: 34px;
    line-height: 34px;
    position: absolute;
    top: 190px;
}
.liFbt2{
    background-color:#ffffff;
    color:#000000;
    width:255px;
    top: 192px;
}
.liFbt h2{
    font-size: 14px;
    color: #ffffff;
    font-weight: bold;
    float: left;
    margin-left: 5px;
}
.liFbt2 h2{
    color:#333333;
}
.liFbt span{
    font-size: 14px;
    color: #ffffff;
    float: right;
    margin-right: 5px;
}
.liFbt2 span{
    color:#333333;
}
.liFbt i{
    font-size: 16px;
    color: #ffffff;
    font-weight: bold;
}
.liFbt2 i{
    color:#ff5200;
}
.liFposi{
    position:relative;
}
.listR{
    /* float: right !important; */
    margin-left: 20px;
    position: relative;
}


/*å“ç‰Œé¦†*/
.pinpaiG{
    overflow: hidden;
    background: url("/img/pinpaiBG.png") no-repeat;
    margin-bottom: 30px;
    /* line-height: 90px; */
    /* display: table-cell; */
    /* height: 90px; */
    vertical-align: middle;
}
.pinpaiG li{
    float: left;
    height: 90px;
    line-height: 90px;
    display: table-cell;
    /* height: 90px; */
    text-align: center;
    /* vertical-align: middle; */
}
.pinpaiG li:first-child{
    margin-right: 14px;
}
.pinpaiG li img{
    width: 116px;
    height: 58px;
    vertical-align: middle;
    /* transform: scale(1.1); */
    box-shadow: 0px 0px 10px 0px rgba(156, 152, 152, 0.16);
    margin-left: 12px;
}
.pinpaiG li:first-child img{
    width: 200px;
    height: 90px;
    margin-left: 0;
    box-shadow: 0px 0px 10px 0px rgba(156, 152, 152, 0);
    margin-right: 9px;
}
.pinpaiG span{
    float: right;
    width: 30px;
    display: block;
    height: 53px;
    background-color: #dfdfdf;
    font-size: 14px;
    color: #666666;
    vertical-align: middle;
    word-wrap: break-word;
    writing-mode: vertical-lr;
    text-align: center;
    line-height: 30px;
    letter-spacing: 5px;
    margin-top: 16px;
    padding-top: 5px;
    cursor: pointer;
}
.pinpaiG span:hover{
    color:#ff5200;
}
.pinpaiH img:hover{
    transition: all 0.6s;
    transform: scale(1.1);
    box-shadow: 0px 0px 10px 0px rgba(104, 104, 104, 0.3);
}