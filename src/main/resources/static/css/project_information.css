@charset "utf-8";
.clearfix{clear: both}
*{font-weight: normal}
.w{width: 1170px !important;}
.housesLeft{ width: 892px !important;}
.Basics_box .title_sun h1{font-size: 18px; color: #333333; height: 18px; line-height: 18px; padding-top: 24px}
.Basics_box .content{ margin-top: 22px}
.Basics_box .content ul li{ width: 50%; float: left;height: 32px; line-height: 32px}
.Basics_box .content .bottom_wei{ border-top: 1px dashed #ededed;margin-top: 11px;padding-top: 11px}
.housesLeft{padding-left: 0 !important;padding-right: 0 !important;border: 0 !important;}
.Basics_box{ padding-left: 30px;padding-right: 30px; border: 1px solid #ededed; padding-bottom: 15px}

.Mansion .title_sun h1{font-size: 18px; color: #333333; height: 18px; line-height: 18px; padding-top: 24px}
.Mansion .content{ margin-top: 22px}
.Mansion .content ul li{ width: 50%; float: left;height: 32px; line-height: 32px}
.Mansion{ padding-left: 30px;padding-right: 30px; border: 1px solid #ededed; padding-bottom: 15px;margin-top: 11px}

.Villa .title_sun h1{font-size: 18px; color: #333333; height: 18px; line-height: 18px; padding-top: 24px}
.Villa .content{ margin-top: 22px}
.Villa .content ul li{ width: 50%; float: left;height: 32px; line-height: 32px}
.Villa{ padding-left: 30px;padding-right: 30px; border: 1px solid #ededed; padding-bottom: 15px;margin-top: 11px}

.shops .title_sun h1{font-size: 18px; color: #333333; height: 18px; line-height: 18px; padding-top: 24px}
.shops .content{ margin-top: 22px}
.shops .content ul li{ width: 50%; float: left;height: 32px; line-height: 32px}
.shops{ padding-left: 30px;padding-right: 30px; border: 1px solid #ededed; padding-bottom: 15px;margin-top: 11px}

.sale .content ul li table{border-collapse: collapse;margin-top: 12px;width: 100%;border-left: 1px solid #ededed;}
.sale .content ul li table thead tr td{ text-align: center}
.sale .content ul li table tbody tr{border-top: 1px solid #ededed}
.sale .content ul li table tbody tr td{border-right: 1px solid #ededed}
.sale .content ul li table tbody tr td:nth-child(3n){border-right:0; padding-left: 20px}

.sale .title_sun h1{font-size: 18px; color: #333333; height: 18px; line-height: 18px; padding-top: 24px}
.sale .content{ margin-top: 22px}
.sale .content ul li{ height: auto; line-height: 32px}
.sale .content ul li a{color: #ff5200;text-decoration:  none;margin-right: 10px;}
.sale{ padding-left: 30px;padding-right: 30px; border: 1px solid #ededed; padding-bottom: 15px;margin-top: 11px}

.project{ padding-left: 30px;padding-right: 30px; border: 1px solid #ededed; padding-bottom: 15px;margin-top: 11px}
.project .title_sun h1{font-size: 18px; color: #333333; height: 18px; line-height: 18px; padding-top: 24px}
.project .content{margin-top: 23px}
.project .content p{ font-size: 14px; color: #333333;line-height: 30px}

.Developers{ padding-left: 30px;padding-right: 30px; border: 1px solid #ededed; padding-bottom: 15px;margin-top: 11px}
.Developers .title_sun h1{font-size: 18px; color: #333333; height: 18px; line-height: 18px; padding-top: 24px}
.Developers .content{margin-top: 23px}
.Developers .content p{ font-size: 14px; color: #333333;line-height: 30px}

.sale .content ul li .btn{ text-align: center}
.sale .content ul li .btn h1{font-size: 12px; color: #333333;display: inline-block; line-height: 22px;border: 1px solid #ededed;border-top: 0;padding-left: 22px;padding-right: 33px;
    background-image: url("https://static.fangxiaoer.com/web/images/Villa/op_sun02.png"); background-repeat: no-repeat; background-position:56px  center;cursor: pointer}
    .nav_house li a{font-weight:bold}
    .housesLeft{padding-bottom:0 !important}
.footer{margin-top: 30px !important;}
          .Basics_box ul li{position:relative;white-space:nowrap;}
            .Basics_box ul li .content_pop{
                width: 485px;
                position: absolute;
                top: 43px;
                left: 70px;
                background-color: #fff;
                box-shadow: 0.5px 0.866px 10px 0px rgba( 0, 0, 0 ,0.3);
                z-index: 99;
                line-height: 30px;
                padding: 17px;
                white-space: normal;
}
.Basics_box ul li:hover .content_pop{display:block !important}


          .Mansion ul li{position:relative;white-space:nowrap;}
            .Mansion ul li .content_pop{
    position:  absolute;
    top: 43px;
   left: 70px;
    width: fit-content;
    background-color: #fff;
    box-shadow: 0.5px 0.866px 10px 0px rgba( 0, 0, 0 ,0.3);
    padding-right: 17px;
    z-index: 99;
    line-height: 50px;
    padding-left: 17px;
    white-space:nowrap;
}
.Mansion ul li:hover .content_pop{display:block !important}


          .Villa ul li{position:relative;white-space:nowrap;}
            .Villa ul li .content_pop{
    position:  absolute;
    top: 43px;
   left: 70px;
    width: fit-content;
    background-color: #fff;
    box-shadow: 0.5px 0.866px 10px 0px rgba( 0, 0, 0 ,0.3);
    padding-right: 17px;
    z-index: 99;
    line-height: 50px;
    padding-left: 17px;
    white-space:nowrap;
}
.Villa ul li:hover .content_pop{display:block !important}


          .shops ul li{position:relative;white-space:nowrap;}
            .shops ul li .content_pop{
    position:  absolute;
    top: 43px;
   left: 70px;
    width: fit-content;
    background-color: #fff;
    box-shadow: 0.5px 0.866px 10px 0px rgba( 0, 0, 0 ,0.3);
    padding-right: 17px;
    z-index: 99;
    line-height: 50px;
    padding-left: 17px;
    white-space:nowrap;
}
.shops ul li:hover .content_pop{display:block !important}


.ms_sanjiao{width:22px;height:11px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/view_ts_icon.jpg) no-repeat;margin-top: -13px;position: absolute;left: 35px;top: 5px;}
