@charset "utf-8";
body {
	line-height: normal
}

.clearfix {
	clear: both
}

.position .content_box {
	width: 1170px;
	margin: 0 auto
}

.position .content_box h1 {
	font-size: 12px;
	color: #999999;
	font-weight: normal;
	line-height: 50px
}

.position .content_box h1 a {
	color: #999999
}

.nav_box .content_box {
	width: 1170px;
	margin: 0 auto;
	background-color: #f3f3f3
}

.nav_box .content_box ul li {
	float: left
}

.nav_box .content_box ul li a {
	display: block;
	font-size: 16px;
	line-height: 50px;
	padding-left: 37px;
	padding-right: 37px;
	background-image: url("https://static.fangxiaoer.com/web/images/video/line.png");
	background-repeat: no-repeat;
	background-position: right center;
	text-decoration: none;
	color: #333;
}

.nav_box .content_box ul li:nth-child(8) a {
	background: none;
}

.nav_box .content_box ul .color a {
	color: #ff5200
}

.nav_box .content_box ul li a:hover {
	color: #ff5200
}

.nav_box .content_box ul .color {
	background-image: url("https://static.fangxiaoer.com/web/images/video/lin02.png");
	background-repeat: no-repeat;
	background-position: center bottom
}

.big_box .content_box {
	width: 1170px;
	margin: 0 auto
}

.big_box .content_box .left_box {
	width: 900px;
	float: left
}

.big_box .content_box .right_box {
	float: right
}
.activity,.look,.show,.character,.enterprise,.intermediary {
	padding-bottom: 14px
}

.big_box .content_box .left_box  .title h1 {
	font-size: 26px;
	color: #222;
	line-height: 30px;
	font-weight: normal;
	float: left;
	font-weight: bold;
}

.big_box .content_box .left_box  .title a {
	float: right;
	line-height: 20px;
	color: #666666;
	text-decoration: none;
	padding-right: 14px;
	background-image: url("https://static.fangxiaoer.com/web/images/video/more.png");
	background-repeat: no-repeat;
	background-position: right center;
	margin-top: 10px;
}

.left_box .title{
	    margin-top: 26px;
    margin-bottom: 18px;
}

.big_box .content_box .left_box  .content ul li {
	float: left;
	width: 194px
}

.big_box .content_box .left_box  .content ul li a {
	text-decoration: none;
}

.big_box .content_box .left_box  .content ul li .photo {
	position: relative
}

.big_box .content_box .left_box  .content ul li:nth-child(1) {
	width: 490px;
	margin-right: 11px
}

.big_box .content_box .left_box  .content ul li:nth-child(2) {
	width: 194px;
	margin-right: 11px
}

.big_box .content_box .left_box  .content ul li:nth-child(4) {
	width: 194px;
	margin-right: 11px
}

.big_box .content_box .left_box  .content ul li:nth-child(1) .photo .pic img {
	width: 490px;
	height: 276px;
}

.big_box .content_box .left_box  .content ul li .photo .pic img {
	width: 194px;
	height: 109px;
}



.big_box .content_box .left_box  .content ul li:nth-child(2) .text h1 {
	font-size: 14px;
	height: 12px;
	line-height: 12px;
	margin-top: 10px;
	margin-bottom:14px
}

.big_box .content_box .left_box  .content ul li:nth-child(3) .text h1 {
	font-size: 14px;
	height: 12px;
	line-height: 12px;
	margin-top: 10px;
	margin-bottom: 14px
}

.big_box .content_box .left_box  .content ul li:nth-child(4) .text h1 {
	font-size: 14px;
	height: 12px;
	line-height: 12px;
	margin-top: 10px;
}

.big_box .content_box .left_box  .content ul li:nth-child(5) .text h1 {
	font-size: 14px;
	height: 12px;
	line-height: 12px;
	margin-top: 10px;
}

.big_box .content_box .left_box  .content ul li .text h1 {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 14px;
	color: #333333;
	font-weight: normal
}

.big_box .content_box .left_box  .content ul li .photo .time {
	position: absolute;
	right: 0px;
	bottom: 0px;
	background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
	background: -o-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
	background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
	background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.5));
	width: 100%;
	padding-left: 0px;
	padding-right: 0px;
	border-radius: 0px
}

.big_box .content_box .left_box  .content ul li .photo .time h1 {
	font-size: 12px;
	color: #fff;
	font-weight: normal;
	line-height: 33px;
	padding-right: 6px;
	/*background-image: url("https://static.fangxiaoer.com/web/images/video/time03.png");*/
	background-position: left center;
	background-repeat: no-repeat;
	text-align: right;
}

.big_box .content_box .left_box  .content ul li:nth-child(1) {
	position: relative
}

.big_box .content_box .left_box  .content ul li:nth-child(1) .text {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
	background: -o-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
	background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
	background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
}

.big_box .content_box .left_box  .content ul li:nth-child(1) .text h1 {
	color: #fff;
	margin-left: 18px;
	font-size: 14px;
	height: 14px;
	line-height: 14px;
	padding-top: 12px;
	font-weight: bold
}

.big_box .content_box .left_box  .content ul li:nth-child(1) .text h2 {
	color: #fff;
	font-size: 12px;
	font-weight: normal;
	width: 377px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-left: 18px;
	color: #ccc;
	height: 14px;
	line-height: 14px;
	padding-top: 6px;
	padding-bottom: 12px
}

.big_box .content_box .left_box  .content ul li:nth-child(1) .time {
	background: none;
	z-index: 999;
	bottom: 10px
}

.big_box .content_box .left_box .content ul li:nth-child(1) .time h1 {
	height: auto;
	line-height: normal;
	    padding-right: 16px;
	    font-size: 14px;
}

.big_box .content_box .left_box  .content ul li:nth-child(1) .text:hover h1 {
	color: #ff5200 !important;
}

.big_box .content_box .left_box  .content ul li:nth-child(1) .text:hover h2 {
	color: #ff5200 !important;
}

.big_box .content_box .left_box  .content ul li .text h1:hover {
	color: #ff5200
}

.big_box .content_box .left_box  .content ul li a {
	text-decoration: none
}

.big_box .content_box .left_box  a {
	text-decoration: none !important;
}

.big_box .content_box .left_box .title a {
	margin-right: 6px
}

.big_box .content_box .left_box .content ul li .photo .pic {
	overflow: hidden;
}

.big_box .content_box .left_box  .content ul li .photo .pic img {
	transition: all 0.6s;
}

.big_box .content_box .left_box  .content ul li .photo .pic:hover img {
	transform: scale(1.1);
}

#right {
	margin-left: 0
}

.video_box .recommend {
	margin: 0
}

.video_box span {
	display: inline-block;
	width: 4px;
	height: 18px;
	margin-bottom: -1px;
	border-radius: 50px;
	background: #ff5200;
	margin-right: 8px;
	margin-left: 16px
}

.video_box {
	border: 1px solid #eaeaea;
}

.video_box .title {
	line-height: 48px;
}

.video_box img {
	display: block
}

.video_box p {
	font-size: 14px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-right: 12px;
	line-height: 49px
}

.video_box p a {
	color: #000000;
	margin-left: 12px;
	text-decoration: none;
	display: inline-block;
	margin-right: 12px
}

.video_box:hover a {
	color: #ff5200
}

.zsfw .btn {
	border-radius: 0px;
	background-color: #ff5200;
	color: #fff;
	font-size: 14px;
	margin-bottom: 15px;
}

#hqyzm {
	background-color: #f2f2f2;
	color: #333333;
}

.hot li a {
	text-decoration: none
}

.position .content_box h1 a:hover {
	color: #ff5200
}

.big_box .content_box .left_box .title a:hover {
	color: #ff5200 !important;
}

.big_box .content_box .left_box .title a:hover {
	background-image: url(https://static.fangxiaoer.com/web/images/video/more02.png) !important;
}