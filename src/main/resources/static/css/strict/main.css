@charset "utf-8";
/* 通用 */
*{
	padding:0px;
	margin:0px;
}
body{
	font-family:"微软雅黑";
	font-size:12px;
	color:#666;
	/* background: url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/head/head_jieri.jpg) no-repeat top  center;*/
}
ul,li{
	list-style:none;
}
img{
	border:none;
}
i,em,s{
	font-style:normal;
	text-decoration:none;
}
a{
	text-decoration:none;
	color:#666;
}
a:hover{
	color:#ff5200;
	text-decoration: underline;
}
.hid{
	display:none;
}
h1{
	font-size:16px;
}
h2{
	font-size:14px;
}
h3{
	font-size:12px;
}
h4{
	font-size:20px;
}
h5{
	font-size:14px;
}
h6{
	font-size:12px;
}
.cl{
	clear:both;
	font-size:1px;
	line-height:0px;
	height:0px;
}
.w{
	margin:0 auto;
	width:990px;
}
.w1210 .w{
	width:1210px;
}
.w1210 .mt120
{
	margin-top:120px;
}
.more{
	float:right;
	margin-right:10px;
	font-size: 12px;
	font-weight: normal;
}
.more a{
	color: #999;
}
.more a:hover{
	color:#F00;
	text-decoration: none;
}
.logInText {
	padding-left: 14px;
	float: left;
}
.logInText a{
	padding: 14px;
	color: #FFF !important;
}
.button{
	cursor: pointer;
	background: #fff;
	border: 1px solid #e5e5e5;
	border-radius: 6px;
	color: #7c7c7c;
	display: block;
	font-family:"微软雅黑";
	font-size: 16px;
	height: 44px;
	line-height: 44px;
	text-align: center;
	width: 320px;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	margin-top:16px;
}
.button:hover{color: #333;}
.orange{background: 0;border: 0 none;background: #ff6600;color: #fff;border: 1px solid #ff7549;}
.orange:hover{background: #ff4a00; color:#FFF;}

/*my头文件*/
#my_head{}
.my_head_top_nav{line-height:33px; background:#f4f4f6;color:#dfdfdf;}
.my_head_top_nav a{padding:0 22px;color:#999;}
.my_head_top_nav a:hover{color:#ff6600;}
.my_head_top_nav em{color:#666;}
.my_head_top_nav i{color:#ff6600;}
.my_head_right{float:right; width:500px;}
.my_head_top_nav a{background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_xia.gif) no-repeat 75px center; position:relative}
.my_head_right a{float:left;height:33px; width:50px;}
.my_head_right em{float:left;color:#dfdfdf;}
.my_head_right a.head_dd{background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/per.gif) no-repeat center left;padding-left:20px;}
.my_head_right span{ color:#999;padding-left:40px; background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_tel.gif) no-repeat 20px center}
.my_head_right b{color:#ff6600; font-family:Arial; font-weight:normal;margin-left:8px;}
.head_nav_xl{float:left;}
.head_xl_txt{ position:absolute; left:-20px;top:33px; background:#FFF;width:200px;border:1px solid #dfdfdf;border-top:none; display:none;}
.my_head_logo{height:103px;}
.my_head_logo img{float:left;margin:20px 0 0 0;}
.my_head_input{float:left;margin:35px 0 0 30px;width:420px; _display:inline;}
.head_input{width:311px;height:32px;line-height:32px;border:3px solid #FF6600; float:left;padding-left:10px;font-size:14px;}
.head_btn{float:left; background:#FF6600;height:38px;line-height:38px;font-size:18px;color:#fff; text-align:center;width:89px; border:none;}
.my_head_logo .button{width:168px; padding-left:20px;float:right;margin-top:35px; background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/shopping.gif) no-repeat 35px center;border-radius: 0px;height:38px;line-height:38px;}
#my_head_nav{width:990px;margin:0 auto; height:40px;line-height:40px;font-size:14px; background:#ff6600;}
#my_head_nav p{width:233px; background:#404144;color:#FFF;font-weight:bold;padding-left:17px;float:left;margin-right:20px;}
#my_head_nav a{float:left;padding:0 20px; color:#FFF;}

/*my通用样式*/
.my_main{ background:#eee;}
#my_left{width:229px;float:left; background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
.my_left_list{width:179px;height:37px;line-height:38px;border-bottom:1px solid #f3f3f3;padding-left:40px; cursor:pointer}
.my_left_list:hover{color:#262626;}
#my_left .m1{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/my_left_1.gif) no-repeat 13px 12px;}
#my_left .m2{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/my_left_2.gif) no-repeat 13px 12px;}
#my_left .m3{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/my_left_3.gif) no-repeat 13px 12px;}
#my_left .m4{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/my_left_4.gif) no-repeat 13px 12px;}
#my_left .m5{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/my_left_5.gif) no-repeat 13px 12px;}
#my_left .m6{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/my_left_6.gif) no-repeat 13px 12px;}
#my_left  i{width:8px;height:8px; float:right; background: url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
#my_left  .mxl i{background: url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
#my_left ul{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
#my_left li{padding-top:26px;padding-left:40px; background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
#my_left p{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
#my_right{float:right; width:739px; background: url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
.my_right_nav{width:725px; position:absolute;right:-16px; top:-65px;line-height:42px;height:42px;border-bottom:1px solid #e5e5e5; background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/my_right_nav.gif) no-repeat  16px center #f3f3f3;padding-left:45px; color:#777;}
.my_right_nav a{color:#ff6600; text-decoration:none;}
.my_right_nav a:hover{color: #900;}
.k-top{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
.k-bottom{ background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>
.my_title{ background: url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/my/home/<USER>


/*沈阳站头文件*/
#sy_head{ background:#FFF;border-bottom: 1px solid #eee;}
.sy_head_top_nav{line-height:33px; background:#f4f4f6;color: #999;z-index:2;}
.sy_head_top_nav .sy_head_left a{padding: 0 6px;color:#999;}
.sy_head_top_nav .sy_head_left a:hover{color:#ff6600;}
.sy_head_top_nav .sy_head_left em{color:#666;}
.sy_head_top_nav .sy_head_left i{color:#ff6600;}
.sy_head_logo{height:103px;z-index:1;}
.sy_head_logo img{float:left;margin:10px 0 0 0;}
.sy_head_input{float:left;margin:35px 0 0 64px; _display:inline; width:587px;}
#sy_head .head_input{width:390px;height:32px;line-height:32px;border:3px solid #FF6600;border-left:none; float:left;padding-left:10px;font-size:14px;outline:medium;}
.sy_head_logo .button,.sy_head_logo .button:hover{width:168px; padding-left:20px;float:right;margin-top:35px; background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/shopping.gif) no-repeat 35px center;border-radius: 0px;height:38px;line-height:38px;color:#999}
#sy_head .head_nav{height:40px;line-height:40px; background:#404144;}
.sy_leftnav_xl{width:233px; background:#ff5200;color:#FFF;font-weight:bold;padding-left:17px;float:left;margin-right:20px; position:relative; font-size:14px;z-index:99;}
.sy_leftnav_xl p{position:absolute;left:0;top:40px; width:250px;display:none; z-index:99999999}
.sy_leftnav_xl span{ display:block;border-left:1px solid #e1e1e1;border-right:1px solid #e1e1e1;border-bottom:1px solid #e1e1e1;width:208px;padding:0 20px; background:#FFF; padding-bottom:8px;}
.sy_leftnav_xl em{ color:#ff6600;}
#sy_head .head_nav .sy_leftnav_xl span a{padding:0 10px 0 0; color:#6d6d6d; line-height:24px; font-weight:normal;}
#sy_head .head_nav .sy_leftnav_xl span.hover{ background:#f4f4f6;}
#sy_head .head_nav a{/*float:left;*/padding:0 23px; color:#FFF; font-size:14px;}

/*搜索*/
.diy_select{width: 73px;position: relative;font-size: 12px;background: #fff;color: #959595;float: left;	cursor: pointer;}
.diy_select_btn,.diy_select_txt{float:left;height:100%;line-height:28px}
.diy_select_txt{	width: 58px;line-height: 32px;border:3px solid #FF6600;border-right: none;height: 32px;padding-right: 15px;text-align: center;background: url(../Images/bg_search.gif) no-repeat 50px -68px  #fff0e7;}
.diy_select_txt,.diy_select_list li{text-indent:10px;overflow:hidden;text-align:center;padding-right:15px;}
.diy_select_list{position: absolute;top: 38px;z-index: 88888;	border-top: none;width: 74px;display: none;_top: 39px;border-bottom: 1px solid #f2d6c5;left: 0px;}
.diy_select_list li{list-style: none;height: 39px;line-height: 39px;background: #FFF;cursor:pointer;border-right:1px solid #f2d6c5;border-left:1px solid #f2d6c5;}
.diy_select_list li.focus{background:#fff0e7;}
.btnsearch{float:right; background:#FF6600;height:38px;line-height:38px;font-size:18px;color:#fff; text-align:center;width: 89px; border:none;outline:medium; cursor:pointer;float: left;right: 0;position: absolute;}
.border_radius{width: 482px;height: 32px;line-height: 32px;border: 3px solid #FF6600;float: left;padding-left: 10px;font-size: 14px;font-family: "微软雅黑";outline: medium;}
.sy_head_input label{display: block;height: 40px;position: relative;}
.sy_head_input label span {position: absolute;float: left;line-height: 40px;left: 10px;color: #BCBCBC;cursor: text; }

/*搜索下拉*/
.ac_results {padding: 0px;border: 1px solid black;background-color: white;overflow: hidden;z-index: 99999;}
.ac_results ul {width: 100%;list-style-position: outside;list-style: none;padding: 0;margin: 0;}
.ac_results li {margin: 0px;padding: 2px 5px;	cursor: default;display: block;text-align:left;font: menu;line-height: 25px;overflow: hidden;}
.ac_loading {background: white url('/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/indicator.gif') right center no-repeat;}
.ac_odd {background-color: #eee;}
.ac_over {background-color: #0A246A;color: white;}

/*右上角导航+下拉*/
.head_nav_right{float:right;position: relative;}
.head_nav_right dd{padding:0px 9px 0px 14px; float:left; cursor:pointer; color:#999;background: url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/head/head_bottom.gif) no-repeat right;padding-right: 16px;}
.head_nav_right dd:hover{color:#ff5200;background: url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/head/head_top.gif) no-repeat right;}
.head_ewm:hover img{display:block;}
.head_ewm img{position:absolute;right: 21px;top: 27px;display:none;z-index: 1000000;}
.head_ewm{margin-right:10px;display:none;}
.head_tel p{display:none; color:#999;z-index: 1000000;}
.head_tel:hover p{display:block;position:absolute;right: -11px;top: 26px;width:222px;height:105px; padding: 15px 0 0 20px;background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/head/head_tel.gif)}
.head_nav_right dd.head_xian{background:none;margin:0;padding:0; display:none;}
.head_nav_right dd.head_xian:hover{color:#999}

.head_nav_right .head_nav_sale{padding-left:20px; background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/per.gif) 0 10px no-repeat;}
.head_nav_right dd.head_right_bottom{position:relative;padding-right:20px;background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_xia.gif) no-repeat 70px 14px;z-index:19999;}
.head_right_bottom p{display:none;}
.head_nav_right dd.head_block{background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_shang.gif) no-repeat 70px 14px;}
.head_block p{line-height:22px;display:block;padding:0 10px; position:absolute; top:33px;_top:37px;left:-80px; background:#FFF;width:240px;height:140px; z-index:1999998;border:1px solid #ddd; text-decoration:none; text-align:left; cursor:auto;color:#666;}
.head_block em{width:75px;border-top:1px solid #fff;position:absolute; top:35px;_top:37px;right:1px;z-index:1999999;}
.head_block a{margin-right:7px;}
.head_block b{margin:5px 0;width:240px;float:left;}
.head_block i{width:240px; border-top:1px solid #ddd; float:left;margin:12px 0 5px; height:1px; line-height:1px; font-size:1px;}
.head_block strong{color:#ff6600;}
.head_nav_right s{float:left; _display:none;}
.head_nav_right a{color:#999}
.head_nav_right a:hover{color:#ff6600}
.head_nav_right dd.sy_head_tel{background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_tel.gif) no-repeat 8px 10px; padding-left:25px;}
.sy_head_tel em{font-size:12px;color:#ff6600; font-family: Arial;margin-left:8px;}

.sy_head_nav{float:right;margin:40px 50px 0 0;}
.sy_head_nav a{float:left;color:#595959; font-size:18px;height:34px;line-height:34px;padding:0 5px; text-decoration:none; margin-left:20px;}
.sy_head_nav a.hover{background:#ff6600; color:#FFF; border-radius:5px;behavior: url(https://static.fangxiaoer.com/js/ie-css3.htc);}

/*首页*/
.xuanchuan{width:170px;height:90px;float:right;}
.xuanchuan img{width:170px;height:90px; margin-top:10px;}
.head_ad{float: left;margin: -20px 0 0 390px; width:600px; color:#999;}
.head_ad a{color:#999;margin:0 5px;}
.head_ad a:hover{ color:#900;}
.head_nav{margin-top:10px;}

/*咨询*/
#head_news .sy_head_top_nav{line-height:45px; background:#404145;}
#head_news .sy_head_top_nav .sy_head_left em,#head_news .sy_head_top_nav .sy_head_left a,#head_news .head_nav_right a,#head_news .head_nav_right dd{color:#dadbdd;}
#head_news .sy_head_top_nav a{font-size:12px;}
#head_news .head_nav_right .head_nav_sale,#head_news .head_nav_right dd.sy_head_tel{ background:none}
#head_news .head_nav_right dd.head_right_bottom{background: url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_news_xia.gif) no-repeat 70px 20px;}
#head_news .head_nav_right dd.head_block{background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_news_shang.gif) no-repeat 70px 20px;}
#head_news .head_nav_right dd.sy_head_tel{padding-left:5px;}
#head_news .head_block p{top:45px;left:-80px;}
#head_news .head_block em{display:none;}
#head_news .head_nav_right p a{color:#666; padding:0px;font-size:12px;}
#head_news .logo{float:left;}
#head_news .logo img{width:114px;height:38px;}
#head_news .column{font-size:20px;float:left;margin-top:10px;margin-left:16px;padding-left:10px; background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/column_dian.gif) no-repeat left; width:90px;}
#head_news .nav{float:left;color:#dfdfdf;margin-top:17px;}
#head_news a{ font-size:14px; padding:0 8px;}
#head_news a.hover{color:#1f80d9;}
#head_news .head_top{padding:17px 0;border-bottom:5px solid #f6f6f6;height:38px;}


/*相册*/
#other_head{ background:#f4f4f6;height:48px;line-height:48px;}
#other_head .logo{float:left;}
#other_head .column{font-size:20px;float:left;margin-left:16px;padding-left:10px; background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/column_dian.gif) no-repeat left; width:90px;}
#other_head .sy_head_left{float:right;color:#dfdfe1}
#other_head .sy_head_left a{padding:0 22px;color:#999;}
#other_head .sy_head_left a:hover{color:#ff6600;}
#other_head .sy_head_left em{color:#666;}
#other_head .sy_head_left i{color:#ff6600;}



/*my 站头文件*/
/*登录*/
#my_head_login .sy_head_top_nav{line-height:45px; background:#404145;}
#my_head_login .sy_head_top_nav .sy_head_left em,#my_head_login .sy_head_top_nav .sy_head_left a,#my_head_login .head_nav_right a,#my_head_login .head_nav_right dd{color:#dadbdd;}
#my_head_login .sy_head_top_nav a{font-size:12px;}
#my_head_login .head_nav_right .head_nav_sale,#my_head_login .head_nav_right dd.sy_head_tel{ background:none}
#my_head_login .head_nav_right dd.head_right_bottom{background: url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_news_xia.gif) no-repeat 70px 20px;}
#my_head_login .head_nav_right dd.head_block{background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/head_news_shang.gif) no-repeat 70px 20px;}
#my_head_login .head_nav_right dd.sy_head_tel{padding-left:5px;}
#my_head_login .head_block p{top:45px;left:-80px;}
#my_head_login .head_block em{display:none;}
#my_head_login .head_nav_right p a{color:#666; padding:0px;font-size:12px;}
#my_head_login .logo{float:left;}
#my_head_login .logo img{width:114px;height:38px;}
#my_head_login .column{font-size:20px;float:left;margin-top:10px;margin-left:16px;padding-left:10px; background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/column_dian.gif) no-repeat left; width:90px;}
#my_head_login .nav{float:left;color:#dfdfdf;margin-top:17px;}
#my_head_login a{ font-size:14px; padding:0 8px;}
#my_head_login a.hover{color:#1f80d9;}
#my_head_login .head_top{padding:17px 0;border-bottom:5px solid #f6f6f6;height:38px;}

/*my后台*/
.my_admin{ background:#ff6600; height:70px;}
.my_admin p{float:right;margin-top:30px; font-size:14px;}
.my_admin p a{color:#FFF;margin-left:40px;}



/*表单 验证*/
/*发布房源*/
input{font-size:12px; font-family:inherit;}
.tbForm{margin-top:10px;}
.tbForm li{ font-size:14px; color:#666; line-height:30px; clear:both;padding-top:20px;}
.tbForm li p{float:left;margin-right:5px;}
.tbForm i{display:inline-block; vertical-align:-3px;font-size:16px; font-style:normal;color:#F00;padding:0 3px; font-family:'\5FAE\8F6F\96C5\9ED1'}
.tbForm .tt{float:left;width:135px; text-align:right;}
.tbForm input.inputStyle{width: 200px;height: 18px;line-height: 18px;padding: 5px;border: 1px solid #cccccc;font-size: 14px;margin:0 8px;}
.tbForm input.widthSm{width:50px;}
.tbForm input.widthMd{width:100px;}
.tbForm input.widthBg{width:300px;}
.tbForm p.radioBox input{margin:0; vertical-align:middle;margin-bottom:4px;}
.tbForm p.radioBox span{padding: 0 8px 0 5px;}

.select_box{float:left; margin-right:10px; position:relative;width:135px; background:url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/rpInput.gif) repeat-x;height:27px;border:1px solid #c9c9c9}
.select_box input{height:29px;border:none; background:none;}
.select_box dt{ background: url(../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/bg_xiala.gif) right no-repeat;line-height:29px;padding:0 28px 0 10px; cursor:pointer;}
.select_box dd{display:none; background:#FFF;border-left:1px solid #c9c9c9;border-right:1px solid #c9c9c9;border-bottom:1px solid #c9c9c9; position:absolute; z-index:1;}
li dl.select_box p{ float: none;width:123px;padding-left:10px;margin:0;cursor:pointer;}
li dl.select_box p:hover{ background:#C6E2FF;}
.tbForm .select_box{float:left; margin-right:10px; position:relative;width:135px;}
.tbForm .select_box label{position:absolute;top:0;left:0;height:28px;line-height:28px;padding-left:10px;}
.tbForm .select_box input{ background:#FFF;color:#FFF; height:1px;outline:medium;}
.tbForm .select_box dt{ background: url(../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/bg_xiala.gif) right no-repeat;line-height:29px;padding:0 28px 0 10px; cursor:pointer;}
.tbForm .select_box dd{display:none; background:#FFF;border-left:1px solid #c9c9c9;border-right:1px solid #c9c9c9;border-bottom:1px solid #c9c9c9; position:absolute; z-index:100; top:28px;left:-1px;}
.tbForm li dl.select_box a{width:125px;padding-left:10px;margin:0;cursor:pointer; display:block;}
.tbForm li dl.select_box a:hover{ background:#eee; text-decoration:none;}

.tbForm li dl.widthSm{width:105px;}
.tbForm li dl.widthSm p{ float: none;width:93px;}
.tbForm li dl.widthMd{width:155px;}
.tbForm li dl.widthMd p{ float: none;width:143px;}
.tbForm li dl.widthBg{width:185px;}
.tbForm li dl.widthBg p{ float: none;width:173px;}


/*底文件*/
/*my*/
#my_foot{color:#676767;text-align:center;margin:20px 0 10px; line-height:24px;}
#my_foot a{color:#676767}
/*sy通用*/
#footer{background:#eee;height:187px;margin-top:30px;}
#footer .footer_nav{ background:#666; line-height:45px; font-size:14px;}
#footer .footer_nav .w{width:1210px; margin:0 auto; background:#666;}
#footer .footer_nav a{color:#fff;margin-right:40px;}
#footer .footer_test .w{width:1210px; margin:0 auto; background:#eee; height:142px;}
#footer .footer_test_l{float:left;margin-top:30px;}
#footer .footer_test_r{float:right;width:640px; font-size:12px;margin-top:15px;}
#footer .footer_test_r span{color:#9c9c9c}
#footer .ft-list img{margin:0 20px 0 0}
#footer .ft-list{ text-align:left;margin-top:10px}

/*需求导购 底*/
#foot_need{background:#eee;height:100px;margin-top:30px;}
#foot_need .footer_nav{ background:#666; line-height:45px; font-size:14px;}
#foot_need .footer_nav .w{width:1210px; margin:0 auto; background:#666;}
#foot_need .footer_nav a{color:#fff;margin-right:40px;}
#foot_need .footer_need_t .w{width:1210px; margin:0 auto; background:#eee; height:55px;}
#foot_need .footer_test_l{float:left;margin-top:8px;}
#foot_need .footer_test_r{float:right;width:800px; font-size:12px;margin-top:20px; text-align:right;}

/*sy 页码*/
.page{margin-top:30px; text-align:center}
.page a{border:1px solid #ccc; border-radius:2px;margin-right:2px;padding:6px 10px; text-decoration:none;}
.page span{padding:0 7px;margin-top:7px;font-weight: bold;color: red;}
#Pager1_input{height: 28px;border: 1px #ccc solid;text-align: center;margin-right: 5px;border-radius: 2px;width: 46px!important;color: #666;line-height: 30px;}
#Pager1_btn{border: none;text-align: center;background:#ccc;height: 30px;width: 30px;border-radius: 2px;color: #fff;cursor: pointer;font-size: 14px;line-height: 30px;}

/*www 头文件*/
#w_head{border-top:5px solid #ff6600;}
#w_head ul{width:1210px;margin:30px auto;}
#w_head li{float:left;margin-right:26px;font-size:14px; line-height:35px;}
#w_head li.hover{border-bottom:3px solid #ff6600;}
#w_head ul p{float:right;}
#w_head ul p img{margin-left:35px;}

/*www 底*/
#w_foot{height:220px; text-align:center; border-top:1px solid #ddd; background:#eee; font-size:14px; line-height:24px;padding-top:35px;margin-top:90px;}
#w_foot p{color:#666;margin:15px 0;}
#w_foot p a{color:#666}
#w_foot span{color:#9c9c9c;}


/*右侧快捷图标*/
.shortcut1{position: fixed;right:20px;width:38px;bottom:50px;_position:absolute;_top:expression(documentElement.scrollTop + documentElement.clientHeight-this.offsetHeight)}
.shortcut2{position: fixed;bottom:50px;left:50%;margin-left:620px;width:38px;_position:absolute;_top:expression(documentElement.scrollTop + documentElement.clientHeight-this.offsetHeight)}
#shortcut_icon{height:280px; z-index:999999;}
#shortcut_icon a{width:38px;height:38px; background-image:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/ico_shortcut.png) ;float:left;padding:6px 0; position:relative; text-decoration:none}
.t1{background-position:-17px -6px;}
.t2{background-position:-17px -56px;cursor:pointer}
.t3{background-position:-17px -106px;}
.t4{background-position:-17px -156px;}
.t5{background-position:-17px -206px;}
.t6{background-position:-17px -256px; display:none}
.t1:hover{background-position:-72px -6px;}
.t2:hover{background-position:-72px -56px;}
.t3:hover{background-position:-72px -106px;}
.t4:hover{background-position:-72px -156px;}
.t5:hover{background-position:-72px -206px;}
.t6:hover{background-position:-72px -256px;}
.shortcut_pop{ position:absolute; background:#ff0000; border:1px solid #FFF;width:220px; right:50px; display:none;padding:10px; line-height:22px; font-size:13px;color:#FFF; text-align:center; font-family:"微软雅黑";}
.shortcut_pop img{width:110px;height:110px;border:5px solid #FFF;}
.shortcut_pop i{position:absolute;right:-15px; top:-3px; float:left;height:40px;width:15px; background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/ico_shortcut.png) no-repeat -21px -564px;}
.shortcut_pop p{line-height: 20px;	padding-bottom: 8px;margin-top: 8px;border-bottom: 1px solid #DCDCDC;text-align: center;}
.shortcut_pop img{margin-bottom:8px;margin-top:15px;}
.t1 .shortcut_pop,.t2 .shortcut_pop{top:-9px; width:120px}
.t1 .shortcut_pop i,.t2 .shortcut_pop i{top:11px;}
.t2 .shortcut_pop{top:-9px; width:150px; cursor:pointer}
.t3 .shortcut_pop{top:-42px; width:250px; cursor: default;}
.t3 .shortcut_pop i{top:47px;}
.t4 .shortcut_pop,.t5 .shortcut_pop{top:-65px; cursor: default;width:150px;}
.t4 .shortcut_pop i,.t5 .shortcut_pop i{top:71px;}
.t6 .shortcut_pop{width:80px}
.t6 .shortcut_pop i{top:1px;}


.footer_top{width:1210px;margin:0 auto;}
.footer_top ul{float:left;width: 208px;border-right:1px solid #434343;margin-right:45px;}
.footer_top li{float:left;width:98px;line-height:30px;}
.footer_top li.foot_t{width:100%;margin-bottom:10px;color: #fff;}
.footer_top .foot_t a{color:#fff;}
.footer_top a{color:#999; text-decoration:none}
.footer_top a:hover{color:#fff;}
.footer_top p{float:left;width: 194px;position: relative;}
.footer_top p img{position:absolute;}
.copying{width:1210px;margin: 51px auto 0;line-height:30px;color:#999;}
.copying a{color:#999; text-decoration:none}
.copying a:hover{color:#fff;}

#cnzz_stat_icon_1255806361{display:none}


.btnfbfy{margin-left:67px;background: #FF6600;height: 38px;line-height: 38px;font-size: 18px;color: #fff;text-align: center;width: 140px;
	border: none;outline: medium;cursor: pointer;padding:0;position: absolute;right: -260px;}
.btnfbfy img{margin: 10px 0px 0 18px;float: left;}
.btnfbfy:hover{color:#fff;text-decoration:none;}

/*2015-11-26  头文件*/
@charset "utf-8";
/* CSS Document */
.paddingL0{padding-left:0 !important}
.borderRnone{border-right:none !important}
.header{height: 80px; margin:0 auto;background: #FFF;}
.header_container{ position:relative;height: 70px;width: 1170px;margin: 0 auto;}
.logo{display:block;float:left;margin: 24px 10px 0 0;}
.header_search{width:415px;float:left; position:relative;margin: 19px 88px 0 0;}
.search_input{width: 447px;height: 38px !important;border:2px solid #ff9766 !important;float:left;padding:0 0 0 20px !important; background-color:transparent !important;line-height:30px !important;border-radius:0px !important;outline:none }
.search_btn{width:52px !important;height: 42px;border:none; cursor:pointer; background:url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/head/search_btn.png) #ff9766 no-repeat center;outline:none   }
.header_search span{ position:absolute;top: 14px;left:20px;font-size:12px;color:#aaaaaa;z-index: 0;}
.header_tel{width:210px;height:30px;padding-right:15px; text-align:right;display:block;float:left;font-size:12px;color:#aaa;position: absolute;top: 20px;right: 194px;line-height:30px;background-color:#fff; overflow:hidden;z-index:999}
.header_tel span{color:#ff5200}
.header_tel_img{ position:absolute;right:0;top:13px}
.header_gg{width:170px;height: 70px;display:block;float:right; text-align:right;  z-index:999}
/*导航*/
.nav{width:100%;margin-bottom:0 !important;height: 40px;background-color:#ff5200;color:#fff}
.nav_container{width: 1170px; margin:0 auto}
.nav_container ul{ list-style:none;margin:0 0 0 0 !important;height: 40px;float:left}
.nav_container li{float:left;}
.nav_container .nav_one a{display:block;padding:0 24px;margin-top:2px;color:#fff; text-decoration:none;line-height: 35px;font-size:16px;height: 35px;}
.nav .active a{color:#fff45c; border-bottom:3px solid #fff45c}
.nav_login{height: 40px;float:right;width:180px;background-color:#ff864d}
.nav_login a{line-height: 39px;color:#fff;font-size:14px; text-decoration:none;}
.a_hover a{color:#ff5200 !important;background-color:#fff;border-bottom:3px solid #fff}
.nav_login img{margin-left:32px;float:left;margin-top: 12px;margin-right:13px}
.nav_login_reg{padding-left:18px;border-left:1px solid #fff;margin-left:23px}
.nav_hover{ position:absolute;top: 154px;left:0;width:100%;background-color:#fff;/* border-bottom:1px solid #cccdd4; */ overflow:hidden;height: 41px;}
.nav_float{height:40px;width: 1170px;margin:0 auto; border: 1px solid #cccdd4;border-top: none;}
.nav_float ul{width:100%;height:40px;display:none;margin:0 !important;padding-left: 14px;}
.nav_float li{float:left;padding: 0 24px;margin-top: 11px;border-right:1px solid #dcdcdc}
.nav_float li a{color:#4c4c4c; text-decoration:none;font-size:14px; position:relative}
.nav_float li a:hover{color:#ff5200;}
.nav_float li a img{position:absolute}
.nav_hover .active{color:#ff5200}
/*面包屑*/
.crumbs{color:#999;width: 1170px;margin:0 auto; text-align:left;padding-top:10px; font-size:12px !important;}
.crumbs a{color:#999}
.crumbs span{color:#666}
.crumbs span a{color:#666}
/*子导航定位*/
#rent{padding-left: 42px;}
#shangpu{padding-left: 240px;}
#help{padding-left:175px}
#need{padding-left: 259px !important;}
#form{padding-left: 477px;}
#strategy{padding-left: 80px;}

/*弹出*/
.tanChuWenZi{z-index:9999999;width:390px;height:240px;border:1px #e2e2e2 solid;border-radius:5px;text-align: center;margin: 0 auto;position: fixed;top: 50%;left: 50%;margin-top:-120px;margin-left:-195px;background-color: #fff;}
.tanChuWenZi p{display: block;font-size: 18px;margin-top: 70px;font-size: 17px;line-height: 28px;}
.tanChuWenZi i{color:#eb6877;padding:0 2px;font-size: 18px;}
.tanChuWenZi a{display: block;width:150px;height:40px;background-color: #ff5200;font-size: 16px;text-align: center; margin: 0 auto;line-height: 40px;margin-top: 36px;text-decoration: none;cursor: pointer;color: #fff;}
.tanChuWenZi img{display: block;float: right;margin: 12px;cursor: pointer;}
.tanChuBeiJing{background: #000;opacity: 0.5;filter:alpha(opacity=50);z-index: 999;width:100%;height:100%;top:0;left:0;position: fixed;display:block;}
.tanChuWenZi p span{color:red;}
/*seo 底*/
.title_seo{overflow:hidden;margin: 25px auto 0;width:1210px;}
.title_seo h3{color:#666;font-size:16px;margin-top: 5px;margin-bottom: 3px;line-height: 40px;}
.title_seo p{margin-left:-15px;float: left;}
.title_seo a{color:#999;font-size:14px; padding-left:15px; border-left:1px solid #999;margin: 0 15px 12px 0; line-height:14px;float:left;}
.title_seo a:hover{color:#be0000}


#sy_head{height:33px; line-height:33px; background: #f5f5f5;border-bottom: 1px solid #eee !important;}
.sy_head_w a{color: #999;text-decoration: none;/* border-right: 1px solid #ccc; *//* line-height: 20px; */font-size: 12px;}
.sy_head_w a:hover{/* color:#008; *//* text-decoration: none; */}
.sy_head_w{width: 1170px;margin:0 auto;}
.sy_head_l{float:left;/* height: 50px; */}
.sy_head_l span a{height:14px;line-height:14px;margin-right: 20px;border-right: 1px solid #dfdfdf;padding-right: 20px;color: #fff;}
#sy_head .no-border{border:none}

.sy_head_r{float:right; color:#999}
.sy_head_r div{float: left;position: relative;margin-right: 45px;border-right: 1px solid #dfdfdf;height: 14px;line-height: 14px;padding-right: 31px;margin: 11px 15px 0 0;background: url(/img/ico_city_qh.png) no-repeat 63px 5px;}
.sy_head_r p{display: none;position: absolute;padding:10px;background: #fff;border:1px solid #dfdfdf;top:22px;z-index: 9999999;}
.sy_head_r div:hover p{display: block}
.sy_head_r div:hover{color:#ff5200;cursor: pointer;background: url(/img/ico_city_qh.png) no-repeat 63px 5px;}
.sy_head_r i{position: absolute;top:-11px;right:53px;width:88px; text-align: center}
.head_app{float:left;margin-right:10px;}
.sy_head_r p span{font-size:12px}


/*专题*/
#sy_head_event{height:50px;line-height:50px;/* border-bottom:1px solid #eee; */background: #ff5200;font-size: 12px;}
.sy_logo_top{float:left;line-height: normal;margin-top: 13px;}
#sy_head_event .sy_head_l{float:right;}
#sy_head_event .sy_head_r div{margin-top:18px;color: #fff;font-size: 12px;}
#sy_head_event .sy_head_r i{top: -29px;padding-top: 21px;}
#sy_head_event .sy_head_r p{top:32px;right: 0px !important;}
.head_daohang span{float:left; line-height: 30px;}
.head_daohang a{float:left;width:84px;}
.head_daohang b{display: block;}
.head_daohang b a{display: block;font-size:14px;color:#1f1f1f; font-weight:100; float:none}

.footer{background:#f5f5f5;border-top: 1px solid #eeeeee;}
.footer_width{width: 1170px;}
.footer_160309{width: 100%;border-top: 1px solid #eeeeee;font-size: 12px;color: #666;background: #f5f5f5;}
.footer_top_160309{margin: 45px auto 0;height: 130px;}
.footer_160309 ul{float: left;margin-right: 68px;width: 142px;}
.footer_160309 li{float: left;margin-right: 20px;line-height: 30px;}
.footer_160309 li a{color: #666666;text-decoration: none;}
.footer_160309 li.footer_t{float: none;margin-bottom: 15px;font-size: 14px;color: #1f1f1f;}
.footer_160309_right{line-height: 28px;padding-left: 17px;border-left: 1px solid #ddd;float: right;}
.footer_160309 ul.footer_ul_1{width: 180px;}
.footer_160309 ul.footer_ul_2{width: 170px;}

.footer_ewm{padding: 45px 0;line-height: 34px;height: 101px;margin: 0 auto;}
.footer_ewm_img{float: left;width: 245px;font-size: 12px;color:#999;margin-right: 40px;line-height: 24px;}
.footer_ewm_img img{float: left;margin-right: 16px;}
.footer_ewm_img p{font-size: 14px;color: #333;margin-top: 10px;margin-bottom:14px;text-indent: 0;}
.footer_biaoshi{float: right;margin:30px 40px 0 0;}

.footer_copy{margin: 0 auto;line-height: 30px;color:#999;font-size: 12px;}
.footer_copy a{color:#999;text-decoration: none;padding-right:5px ;border-right: 1px solid #b3b3b3;margin-right: 5px;line-height: 12px;float: left;margin-bottom: 8px;}
.footer_copy a.bor_n{border-right: none;}
.footer_copy p{text-indent:0}

@media (max-width: 1200px) {
	.footer_width{width: 940px;}
	.footer_160309 ul{margin-right: 11px;}
	.footer_ewm_img{margin-right: 0;}
	.footer_biaoshi{margin:30px 0 0 0;}
	.sy_head_w{width:940px;}
}

/*导航下拉弹出*/
.topMove_head{height: 40px !important;background:#f5f5f5;border-top:1px solid #dadadf;border-bottom:1px solid #dadadf;position:fixed;top:-42px;width: 100% !important;z-index:99999;left: 0;}
.topMove_head p{float:left;line-height:42px;background:#ff5200;margin-top:-1px;width:60px;text-align:center;color:#fff;margin-right:9px}
.topMove_head a{line-height:40px;float:left;padding: 0 6px;margin: 0 9px;}
.topMove_head a.hover{color:#ff5200}
.topMove_head #strategy{width:1170px;margin: 0 auto !important;padding: 0;}
.topMove_head p{display:block !important;float:left;}
.topMove_head li{border:none;padding: 0 9px;margin: 0;}

.houseMove_head{height: 40px !important;background:#f5f5f5;border-top:1px solid #dadadf;border-bottom:1px solid #dadadf;position:fixed;top:-42px;width: 100% !important;z-index:99999;left: 0;}
.houseMove_head p{float:left;line-height:42px;background:#ff5200;margin-top:-1px;width:60px;text-align:center;color:#fff;margin-right:9px;font-size:14px}
.houseMove_head a{line-height:40px;float:left;margin: 0 9px;}
.houseMove_head a.hover{color:#ff5200}
.houseMove_head #strategy{width:1170px;margin: 0 auto !important;padding: 0;}
.houseMove_head p{display:block !important;float:left;}
.houseMove_head ul{width:1170px;margin: 0 auto !important;}
.nav_house .houseMove_head li{border:none;padding: 0 9px;margin: 0;background:none}
.nav_house .houseMove_head li.hover{background:none;}
.nav_house .houseMove_head li.hover a{color:#ff5200}
/*好房低价简单*/
.box_icon {width: 1170px;padding: 18px 0;overflow: hidden;font-size: 14px;color: #999;margin: 0 auto;}
.icon_main{float: left;overflow: hidden;width: 370px;}
.icon_main_1{width: 420px;}
.icon_left{float: left;margin-right: 15px;}
.icon_right {padding: 10px 0px;}
.icon_right>div {margin-right: 8px;margin-bottom: 5px;}
.icon_3 {margin-top: -3px;}

/*城市切换*/
.city{color: #333;padding: 0px 0px 0 22px;float: left;width: 90px;margin-right: 80px;margin-top: 32px;position: relative;font-size: 14px;}
.shouye_city{color: #fff;padding: 20px 0 0 22px;float: left;}
.city span{cursor: pointer;background: url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/jt.png) no-repeat right !important;padding-right: 17px !important;}
.city span:hover{color:#333 !important}
.jianjiao{}
.qieH{width: 352px;background: #FFF;padding: 9px;color: #666;display: none;border-radius: 5px;position: absolute;top: 24px;z-index: 8888;border: 1px solid #ccc;left: -116px;}
.qieH i{width: 100px;height: 5px;display: block;background: url("https://static.fangxiaoer.com/global/../static/2017/0110/imgs/ico/jianjiao.png") no-repeat bottom;position: absolute;left: 123px;top: -35px !important;padding-top: 30px !important;}
.qieH p{color: #000;border-bottom: 1px solid #e8e8e8;padding-left: 10px;margin-top: -1px;padding-bottom: 3px;float: none !important;line-height: 31px !important;font-size: 14px !important;}
.qieH ul{padding-left: 10px;margin-top: 3px;overflow: hidden;}
.qieH ul li{float: left;margin-right: 30px !important;line-height: 32px !important;padding-right: 0;}
.qieH ul li a{color: #666;text-decoration: none}
.qieH ul li a:hover{color: #ff5200;}
.city:hover .qieH{display: block;}
.shouye_city:hover .qieH{display: block;}



/*右侧浮动信息栏*/
.expert{position: fixed; top: 80px; right: 10px; width: 120px; height: auto;}
.expert .ex_bg{position: absolute;top: 0;left: 25px;width: 73px;height: 73px;margin: 0 auto;border-radius: 40px;background: #fff;z-index: 99;}
.expert .ex_pic{position: absolute;top: 5px;left: 30px;width: 57px;height: 57px;margin: 0 auto;border: 3px solid #902602;border-radius: 40px;z-index: 9999;overflow: hidden;}
.expert .ex_close{position: absolute; top: 0; right: 0; width: 22px; cursor: pointer;}
.expert dl{position: absolute; top: 52px; display: block; width: 110px; background: #fff; padding: 5px; z-index: 999;}
.expert dl dd{display: block; background: #902602; padding: 5px 0; margin-bottom: 5px; text-align: center; font-size: 13px; color: #fff; line-height: 20px;}
.expert dl dd a{display: block; color: #fff;}
.expert dl dd span{display: block; width: 60px;margin: 0 auto; margin-top: 2px; border-top: 1px solid #fff;}
.expert dl dd p{display: block; line-height: 20px;}
.expert dl dd:first-child{padding-top: 10px;}
.expert dl dd:last-child{margin-bottom: 0;}

/*M站需求定制*/
.event_xqdz {background: url(https://static.fangxiaoer.com/web/../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/ico/sign/b50.png);position: fixed;top: 0;width: 100%;height: 100%;max-width: 640px;font-size: 11pt;color: #616161;display: none;}
.event_xqdz_footer {display: block;width: 100%;position: fixed;bottom: 0;left: 0;background: #fff;overflow: hidden;margin: 0;}
.event_xqdz_footer dt {float: left;padding: 7px 6px;}
.event_xqdz_footer dt p {font-size: 13px;line-height: 20px;color: #000;margin: 0;}
.event_xqdz_footer dd {float: left;color: #fff;float: right;padding: 0 5px;padding-top: 33px;padding-bottom: 5px;border-left: 1px solid;width: 60px;font-size: 10px;text-align: center;}
.event_xqdz .event_xqdz_select {position: absolute;bottom: 0;left: 0;width: 100%;display: none;background: #fff;}
.event_xqdz .event_xqdz_select li {padding: 10px 5%;overflow: hidden;padding-right: 0;margin-right: 5%;}
.event_xqdz .event_xqdz_select li span {display: block;float: left;text-align: left;padding-right: 2%;border-right: 1px solid #eee;margin-right: 2%;}
.event_xqdz .event_xqdz_select li p {display: block;float: left;height: 18px;color: #666;width: 70%;}
.event_xqdz .event_xqdz_select li input {display: block;float: left;width: 76%;border: 0;font-size: 14px;color: #000;outline: none;}
.event_xqdz .event_xqdz_select li b {display: block;float: right;border: 1px solid #ff5200;color: #ff5200;padding: 2px 6px;}
.event_xqdz .event_xqdz_select li a {color: #fff;display: block;width: 90%;background: #ff5200;border-radius: 2px;text-align: center;line-height: 26pt;margin: 0 auto;}
.event_xqdz .select {background: url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/select.png) no-repeat right center;background-size: 6px;}
.event_xqdz .event_xqdz_area,.event_xqdz .event_xqdz_price {width: 70%;background: #fff;margin: 0 auto;text-align: center;line-height: 34px;position: absolute;top: 50%;left: 15%;margin-top: -152px;border-radius: 2px;overflow: hidden;}
.event_xqdz .event_xqdz_area .hover,.event_xqdz .event_xqdz_price .hover {color: #ff5200;	background: #f5f5f5;}
.event_xqdz .event_xqdz_area {	margin-top: -152px;display: none;}
.event_xqdz .event_xqdz_price {margin-top: -102px;display: none;}
#event_xqdz_validateCode {display: none;}

/*sy站需求定制*/
.event_xqdz_sy{position: fixed;top: 15%;right: 0;background: #fff;box-shadow: 0px 0px 9px #0f0f0f;z-index: 10000;}
.event_xqdz_sy dt{width: 165px;height: 70px;padding-top: 30px;margin-bottom: 16px;}
.event_xqdz_sy dt div div{width: 80px;height: 80px;border: 3px solid #fff;border-radius: 100000px;overflow: hidden;overflow: hidden;margin: 0 auto;margin-bottom: 20px;}
.event_xqdz_sy dt div img{position: absolute;top: 0;right: 0;cursor: pointer;}
.event_xqdz_sy dt div h1{color: #333;font-size: 20px;text-align: center;font-weight: bold;}
.event_xqdz_sy dt div p{font-size: 14px;color: #515151;text-align: center;/* background: url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/event_xqdz_syPhone.png)no-repeat 4px 6px; */width: 166px;/* padding-left: 25px; *//* margin-left: 20px; */line-height: 30px;}
.event_xqdz_sy dd{overflow: hidden;padding-left: 14px;}
.event_xqdz_sy dd select{width: 140px;border: 1px solid #eee;display: block;height: 35px;margin-bottom: 10px;padding-left: 5px;outline: none;}
.event_xqdz_sy dd select option{}
.event_xqdz_sy dd input{width: 132px;border: 1px solid #eee;margin: 0 auto;display: block;padding-left: 5px;height: 35px;margin-bottom: 10px;outline: none;float: left;}
.event_xqdz_sy dd b{display: block;width: 53px;background: #f0f0f0;text-align: center;font-weight: 400;line-height: 37px;float: left;cursor: pointer;}
.event_xqdz_sy dd a{border: 1px solid #ff5200;width: 136px;display: block;text-align: center;height: 35px;line-height: 35px;font-size: 16px;color: #ff5200;margin-bottom: 10px;cursor: pointer;text-decoration: none;}
.event_xqdz_sy dd span{color: #ff5200;text-align: right;line-height: 40px;display: block;font-size: 12px;padding-right: 12px;cursor: pointer;background: url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/event_xqdz_msg.png)no-repeat 12px 15px #fff3ed;background-size: 21px;}
.event_xqdz_x{background: #ff5200;position: fixed;top: 15%;right: 0;padding: 9px 10px;z-index: 999999999;color: #fff;width: 21px;font-size: 20px;text-align: center;cursor: pointer;display: none;}
.event_xqdz_x i{border-bottom: 1px solid #fff;display: block;width: 28px;margin-left: -2px;height: 32px;background: url(../../static/2009/1130../static/2010/1130../static/2010/1108/../static/2010/1108/images/event_xqdz_house.png) no-repeat;}
#event_xqdz_syValidateCode{display: none;}
#MEIQIA-BTN-HOLDER{display: none!important;}

/*公用弹出*/
.fxe-alert {position: fixed;top: 48%;width: 70%;margin: 0 15%;background: url("https://static.fangxiaoer.com/global/../static/2017/0110/imgs/ico/b60.png");color: #fff;font-size: 11pt;text-align: center;border-radius: 6px;padding: 10px;z-index: 1000000;}
