@charset "utf-8";
.banner{background: url(https://static.fangxiaoer.com/web/images/sy/sale/strictchooseBanner.jpg) top center no-repeat;height: 161px;position:  relative;padding-top: 199px;}
.banner h2{
    position: absolute;
    top: 175px;
    /* left: 455px; */
    font-size: 26px;
    color:  #fff;
    width:  100%;
    text-align:  center;
    min-width:  600px;
}
.nameId2,.nameId2:hover{color: #fff !important;}
.banner p{
    position:  absolute;
    bottom: 11px;
    color:  #fff;
    text-align:  center;
    display:  block;
    font-size: 20px;
    width:  100%;
    min-width:  1170px;
}
.contentMain{padding-top: 36px;padding-bottom: 50px;
    background: url(/img/bg.jpg) top center no-repeat;
    background-size: 100% 100%;
    background: -moz-linear-gradient(top, #000000 0%, #ffffff 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#cce0fb), color-stop(100%,#86a6f5));
    background: -webkit-linear-gradient(top, #cce0fb 0%,#538ffd 100%);
    background: -o-linear-gradient(top, #cce0fb 0%,#86a6f5 100%);
    background: -ms-linear-gradient(top, #cce0fb 0%,#86a6f5 100%);
    background: linear-gradient(to bottom, #cce0fb 0%,#86a6f5 100%);}
/*房源类别*/
.Kuang{
    padding: 33px 27px 8px 27px;
    background: #4881fa;
}
.houseCategory{
    width: 1025px;
    margin:  0 auto;
    overflow:  hidden;
    /* background: url(/img/nav.png) top center no-repeat; */
    padding: 53px 16px 0px 56px;
    background-size:  100%;
    margin-top: 10px;
}
.houseCategory li{/* background: url(/img/box.png) top center no-repeat; */float:  left;margin: 0 20px;}
.houseCategory li a{
    display:  block;
    width: 124px;
    height: 88px;
    line-height: 80px;
    text-align:  center;
    font-size: 18px;
    color: #1b50bb;
}
.houseCategory li a:hover,.houseCategory li a.hover{color: #ff9c00;text-decoration: none;font-size: 20px;}

/*区域筛选*/
.regionScreen{
    width: 930px;
    margin:  0 auto;
    overflow: hidden;
    line-height: 36px;
    height: 36px;
    background: #cce0fb;
    padding-left:  240px;
    border:  none;
}
.regionScreen li{
    float:  left;
    margin-right:  40px;
}
.regionScreen li a{
    display:  block;
    font-size: 16px;
    color: #1b50bb;
}
.regionScreen li a:hover,.regionScreen li a.hover{color: #ff9c00;text-decoration: none;}

/*二手房*/
.esfHouseList {
    width: 1170px;
    position: relative;
    margin: 0 auto;
    background: #4881fa;
    /* padding: 0 15px 15px 15px; */
    border-top: 6px solid #1b6bff;
}
.esfHouseList li{overflow: hidden;background: #fff;padding:  17px;position:  relative;margin-bottom: 20px;}
.esfHouseList li:hover {
    background: #eee
}
.inf{overflow:hidden}
.esfHouseList .ico_zd {
    display: block;
    background: #ff5200;
    width: 50px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
    position: absolute;
    top: 5px;
    left: -190px;
    z-index: 100;
}

.esfHouseList .hid {
    display: none!important
}

.esfHouseList .infLeft div {
    background: #ff5200;
    color: #fff;
    display: inline-block;
    padding: 0px 8px;
    border-radius: 3px;
    position: absolute;
    top: 5px;
    right: -9px;
}

.esfHouseList .infLeft {
    float: left;
    display: block;
    width: 210px;
    height: 155px;
    position: relative;
}

.esfHouseList .infLeft i {
    position: absolute;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/ico_duotu.png);
    width: 48px;
    height: 32px;
    line-height: 32px;
    color: #fff;
    text-align: left;
    padding-left: 6px;
    bottom: 1px;
    left: 19px;
}

.esfHouseList .infLeft img {
    width: 210px;
    height: 154px;
    margin-left: 19px;
}

.esfHouseList .infLeft img.yu {
    width: auto;
    height: auto;
    position: absolute;
}

.esfHouseList .infCtn {
    float: left;
    text-align: left;
    margin-left: 30px;
    position: relative;
    width: 530px;
    margin-top: 17px;
}

.esfHouseList .infCtnTitle {
    font-size: 18px;
    color: #57a0e3;
    text-decoration: none;
    line-height: 20px;
    margin-bottom: 22px;
    display: block;
    max-width: 424px;
    float: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.esfHouseList .isGoodHouse{
    color: #ff5200;
    width: 68px;
    display: inline-block;
    text-align: center;
    border-radius: 100px;
    line-height: 22px;
    height: 22px;
    float: left;
    text-decoration: none;
    border: 1px solid #ff5200;
}
.esfHouseList .infCtnTitle:visited {
    color: #1E70A8;
}

.esfHouseList .infCtn .jing {
    background: #32b16c;
    color: #fff;
    width: 20px;
    height: 20px;
    padding: 0;
    border-radius: 20px;
    display: inline-block;
    float: left;
    margin-left: 10px;
    line-height: 20px;
    text-align: center;
}

.esfHouseList .esfHouseList a .jing {
    background: #32b16c;
    color: #fff;
    width: 20px;
    height: 20px;
    padding: 0;
    border-radius: 4px;
    display: inline-block;
    float: left;
    position: absolute;
    bottom: 7px;
    right: -11px;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
}

.esfHouseList .infCtnTitle:hover {
    color: #ff5200;
    text-decoration: none;
}

.esfHouseList .infCtnTitle s {
    background: url(https://static.fangxiaoer.com/web/images/sy/sale/ico_ding.png) 0;
    padding: 10px;
    margin-left: 10px;
}

.esfHouseList .infCtn>span>span {
    height: 25px;
    line-height: 25px;
    display: block;
    float: left;
    text-align: center;
    font-size: 12px;
    margin-right: 5px;
    padding: 0 8px;
    *float: left;
    *margin-top: -15px;
    /* background: #FFF; */
}

.esfHouseList .infCtn span span {
    background: #fff;
}

.esfHouseList .infCtn p {
    font-size: 14px;
    margin-bottom: 11px;
    line-height: 14px;
    width: 471px;
    overflow: hidden;
    height: 18px;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.esfHouseList .xzlKuo{
    margin-left: -5px;
}
.esfHouseList .xzlNkuo{
    margin-left: 5px;
}

.esfHouseList .infRight {
    float: left;
    text-align: right;
    margin-top: 30px;
    margin-right: 15px;
    font-size: 14px;
    line-height: 14px;
    position: absolute;
    right: 70px;
    top: 50px;
    height: 120px;
}


.esfHouseList .infRight p.one {
    color: #FF5200;
    font-size: 32px;
    margin-bottom: 19px;
    display: block;
    line-height: 25px;
    font-family: Georgia;
    font-weight: bold;
    float:  left;
}
.esfHouseList .infRight p.two {
    float:  left;
    margin: 11px 0 0 20px;
    font-size:  16px;
    width: 117px;
}

.esfHouseList .infRight p i {
    font-family: "微软雅黑";
    font-size: 14px;
}

.esfHouseList .infRight p b {
    font-weight: 400;
}

/*按小区右侧价格end*/

.esfHouseList .infCtn span {
    height: 25px;
    line-height: 25px;
    display: block;
    float: left;
    text-align: center;
    font-size: 12px;
    margin-right: 5px;
    padding: 0 8px;
    *float: left;
    *margin-top: -15px;
    /* background: #FFF; */
}

.tese_1 {
    border: 1px #F9792C solid;
    color: #F9792C;
}

.tese_2 {
    border: 1px #5c9436 solid;
    color: #5c9436;
}

.tese_3 {
    border: 1px #2c96cc solid;
    color: #2c96cc;
}

.tese_4 {
    border: 1px #a66eb9 solid;
    color: #a66eb9;
}
/*扫描下载app*/
.esfListEwmBanner{
    float:  left;
    position:  relative;
    margin: 4px 0 -5px 0;
    width: 98%;
}
.esfListEwmBanner>img{
    /* margin:  10px; */
    width:  100%;
    border-radius: 4px;
    display:  block;
}
.esfListEwmBanner h4{
    font-size:  22px;
    margin-bottom:  10px;
    position:  absolute;
    top: 55px;
    left: 190px;
}
.esfListEwmBanner p{
    position: absolute;
    top: 93px;
    left: 190px;
    font-size: 18px;
}
.esfListEwmBanner i{
    display:  block;
    width: 16px;
    height: 16px;
    position:  absolute;
    top: 34px;
    right: 14px;
    cursor:  pointer;
}
.esfListEwmBanner i img{
    width:  100%;
}
#esfListEwmBanner:hover{background:#fff}


/* 商铺房源列表 */
.shopHouseList  {
    width: 1170px;
    /* padding-top: 12px; */
    padding-bottom: 12px;
    position: relative;
    margin:  0 auto;
    border-top: 6px solid #1b6bff;
}
.shopHouseList li{overflow: hidden;background: #fff;padding:  17px;position:  relative;margin-bottom: 20px;}
.shopHouseList .inf{
}

.shopHouseList li:hover {
    background: #eee
}

.shopHouseList .ico_zd {
    display: block;
    background: #ff5200;
    width: 50px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
    position: absolute;
    top: 5px;
    left: -190px;
    z-index: 100;
}

.shopHouseList .hid {
    display: none!important
}

.shopHouseList .infLeft div {
    background: #ff5200;
    color: #fff;
    display: inline-block;
    padding: 0px 8px;
    border-radius: 3px;
    position: absolute;
    top: 5px;
    right: -9px;
}

.shopHouseList .infLeft {
    float: left;
    display: block;
    width: 210px;
    height: 155px;
    position: relative;
}

.shopHouseList .infLeft i {
    position: absolute;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/ico_duotu.png);
    width: 48px;
    height: 32px;
    line-height: 32px;
    color: #fff;
    text-align: left;
    padding-left: 6px;
    bottom: 1px;
    left: 19px;
}
.shopHouseList .infRight .one {
    color: #FF5200;
    font-size: 30px;
    margin-bottom: 19px;
    display: block;
    line-height: 25px;
    font-family: Georgia;
    font-weight: bold;
    float:  left;
    text-align:  right;
}
.shopHouseList .infRight  p i {
    font-family: "微软雅黑";
    font-size: 14px;
}
.shopHouseList .infRight .two{
    float:  left;
    font-size:  16px;
    margin-left:  30px;
    margin-top: 9px;
    width: 117px;
}
.shopHouseList .infRight .two b{font-weight:normal;}
.shopHouseList .inf>a:first-child{float:left; width: 210px;
    height: 154px;overflow:hidden}
.shopHouseList .inf>a:first-child img{
    width:  100%;
    /* height:  100px; */
}
.shopHouseList .infCtnTitle {
    font-size: 18px;
    color: #57a0e3;
    text-decoration: none;
    line-height: 20px;
    margin-bottom: 22px;
    display: block;
    max-width: 470px;
    float: left;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.shopHouseList .isGoodHouse{
    color: #ff5200;
    width: 68px;
    display: inline-block;
    text-align: center;
    border-radius: 100px;
    line-height: 22px;
    height: 22px;
    float: left;
    text-decoration: none;
    border: 1px solid #ff5200;
}
.shopHouseList .infCtnTitle:visited {
    color: #1E70A8;
}

.shopHouseList .infCtn .jing {
    background: #32b16c;
    color: #fff;
    width: 20px;
    height: 20px;
    padding: 0;
    border-radius: 20px;
    display: inline-block;
    float: left;
    margin-left: 10px;
    line-height: 20px;
    text-align: center;
}

.shopHouseList .inf a .jing {
    background: #32b16c;
    color: #fff;
    width: 20px;
    height: 20px;
    padding: 0;
    border-radius: 4px;
    display: inline-block;
    float: left;
    position: absolute;
    bottom: 7px;
    right: -11px;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
}

.shopHouseList .infCtnTitle:hover {
    color: #ff5200;
    text-decoration: none;
}

.shopHouseList .infCtnTitle s {
    background: url(https://static.fangxiaoer.com/web/images/sy/sale/ico_ding.png) 0;
    padding: 10px;
    margin-left: 10px;
}

.shopHouseList .infCtn>span>span {
    height: 25px;
    line-height: 25px;
    display: block;
    float: left;
    text-align: center;
    font-size: 12px;
    margin-right: 5px;
    padding: 0 8px;
    *float: left;
    *margin-top: -15px;
    /* background: #FFF; */
}

.shopHouseList .infCtn span span {
    background: #fff;
}
.shopHouseList .infCtn{
    float: left;
    text-align: left;
    margin-left: 28px;
    position: relative;
    width: 410px;
    margin-top: 10px;
}
.shopHouseList .infCtn p {
    font-size: 14px;
    margin-bottom: 11px;
    line-height: 14px;
    width: 470px;
    overflow: hidden;
    height: 18px;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.shopHouseList .xzlKuo{
    margin-left: -5px;
}
.shopHouseList .xzlNkuo{
    margin-left: 5px;
}
.shopHouseList .infRight {
    float: right;
    margin-top: 57px;
    text-align:  left;
    float: left;
    text-align: right;
    margin-top: 30px;
    margin-right: 15px;
    font-size: 14px;
    line-height: 14px;
    position: absolute;
    right: 70px;
    top: 50px;
    height: 120px;
}
.shopHouseList .infRight ul {
    color: #FF5200;
    font-size: 20px;
    line-height: 20px;
    display: block;
    _width: 152px;
    margin-bottom: 22px;
    clear: both;
}

.shopHouseList .infRight li:first-child {
    float: left;
    font-size: 14px;
    color: #797979;
    text-align: left;
}

.shopHouseList .infRight li {
    width: 76px;
    margin-bottom: 22px;
    float: left;
    text-align: right;
    display: block;
}

.shopHouseList .infCtn span {
    height: 25px;
    line-height: 25px;
    display: block;
    float: left;
    text-align: center;
    font-size: 12px;
    margin-right: 5px;
    padding: 0 8px;
    *float: left;
    *margin-top: -15px;
    /* background: #FFF; */
}

.shopHouseList .tese_1 {
    border: 1px #F9792C solid;
    color: #F9792C;
}

.shopHouseList .tese_2 {
    border: 1px #5c9436 solid;
    color: #5c9436;
}

.shopHouseList .tese_3 {
    border: 1px #2c96cc solid;
    color: #2c96cc;
}

.shopHouseList .tese_4 {
    border: 1px #a66eb9 solid;
    color: #a66eb9;
}
#brandpic{
    background-color: #4881fa !important;
    width: 1117px;
    margin-left: -18px;
    padding-top: 21px;
    margin-bottom: -35px;
    height: 185px;
}