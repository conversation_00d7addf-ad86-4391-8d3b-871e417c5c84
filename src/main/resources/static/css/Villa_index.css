@charset "utf-8";
*{margin: 0; padding: 0; text-decoration: none; list-style: none; font-family:"微软雅黑"; outline: none; font-weight: normal;}
.clearfix{ clear: both;}
@font-face {
    font-family: 'dinot-bold';
    src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');
}
body{background-color: #f5f5f5!important;}
.search_wei{margin-top: 10px;}
.search_wei .content_box{width: 1170px; margin: 0 auto;}
.search_wei .content_box input{ display: block; float: left; width: 459px; height: 46px; border: 1px solid #f0f0f0; border-radius: 4px 0 0 4px;
    font-size: 14px;padding-left: 11px;}
.search_wei .content_box input::-webkit-input-placeholder{
    color:#999999;
}
.search_wei .content_box input::-moz-placeholder{   /* Mozilla Firefox 19+ */
    color:#999999;
}
.search_wei .content_box input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
    color:#999999;
}
.search_wei .content_box input:-ms-input-placeholder{  /* Internet Explorer 10-11 */
    color:#999999;
}
.search_wei .content_box button{ display: block; float: left;width: 102px; height: 47px; border: 1px solid #ff6100; background-color: #FF6100;
    border-radius: 0px 4px 4px 0px; font-size: 16px; color: #ffffff;}
.banner_wei{margin-top: 40px;position: relative;min-width: 1200px;}
.banner_wei .left_wei{ width:33%;height: 430px;position: relative;background-position: top right; background-repeat: no-repeat;float: left;}
.banner_wei .left_wei .hei{position: absolute;; left: 0; top: 0; background-color: rgba(0,0,0,0.5); width: 100%; height: 100%;;}
.banner_wei .left_wei img{display: none;}

.banner_wei .right_wei{ width:35%;height: 430px;position: relative;background-position: top left; background-repeat: no-repeat;float: right;}
.banner_wei .right_wei .hei{position: absolute;; left: 0; top: 0; background-color: rgba(0,0,0,0.5); width: 100%; height: 100%;;}
.banner_wei .right_wei img{display: none}
.banner_wei .Middle_wei{
    position: absolute;
    left: 50%;
    top:-30px;
    margin-left: -450px;
    z-index: 2;
    width: 900px;
    height: 490px;
    box-shadow: 7.5px 12.99px 21px rgba( 89, 88, 88,0.4 );
}
.banner_wei .Middle_wei img{ display: none;}
.banner_wei .Middle_wei .hei{
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 70%;
    background: -webkit-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.5)); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7)); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7)); /* Firefox 3.6 - 15 */
    background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7));
}
.banner_wei .Middle_wei .hei h1{font-size: 40px;color: #ffffff;text-align: center;margin-top: 182px;}
.banner_wei .Middle_wei .hei h1 span{font-size: 42px;margin-left: 24px; margin-right: 24px;}
.banner_wei .Middle_wei .hei h2{font-size: 16px;color: #ffffff;text-align: center;margin-top: 308px;margin-top: 24px;}
.banner_wei .Middle_wei .hei .decorate{display: block;margin: 0 auto;margin-top: 34px;}
.nav_wei{width: 868px;margin: 0 auto;margin-top: 89px;}
.nav_wei ul li{float: left;padding-left:46.5px;padding-right:52.5px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Division.png);background-repeat: no-repeat;background-position: right center;}
.nav_wei ul li:last-of-type{background:none;padding-right: 0;}

.nav_wei ul li a{ font-size:16px; color: #333333;display: block;}
.nav_wei ul li:nth-child(1){padding-left: 0; }
/*.nav_wei ul li:nth-child(6){padding-right: 0;background: none;}*/
.nav_wei ul li .hover{color: #ff6100;}
.nav_wei ul li a:hover{color: #ff6100;text-decoration: none;}
.information_wei{background-color: #fff;/* margin-top: 50px; */padding-top: 28px;/* border: 1px solid #ebebeb; */padding-bottom:  26px;min-width: 1200px;}
.information_wei .content_box{width: 1170px; margin: 0 auto;}
.information_wei .content_box .photo_wei {position: relative;float: left;overflow: hidden;box-shadow: 3.5px 6.062px 9px 0px rgb( 89, 88, 88,0.3 ); }
.information_wei .content_box .photo_wei .photo{width: 750px; height: 440px;overflow: hidden;}
.information_wei .content_box .photo_wei .photo .forImage{display: block;width: 100%;position: absolute;left: 0;top: 0;width: 750px; height: 460px;}
.information_wei .content_box .photo_wei .photo img:nth-child(1){display: block;}
.information_wei .content_box .photo_wei .photo .top_sun{z-index: 11;}
.information_wei .content_box .photo_wei .layer{position: absolute;left: 0; bottom: 0;width: 100%; background-color: rgba(0,0,0,0.5);z-index: 20;}
.information_wei .content_box .photo_wei .layer .name a{font-size: 14px; color: #fff; line-height: 32px;padding-left: 20px;}
.information_wei .content_box .photo_wei .layer .name{ float: left;}
.information_wei .content_box .photo_wei .layer .btn{float: right;margin-right: 8px;padding-top: 6px;width:  auto;background:  none;}
.information_wei .content_box .photo_wei .layer .btn ul li{ float: left; width: 9px; height: 9px; background-color: #fff; border-radius: 50%;margin-right: 12px;cursor: pointer;}
.information_wei .content_box .photo_wei .layer .btn ul .hover{ background-color: #ff5200;}
.information_wei .content_box .information_sun{ width: 389px; float: right;}
.information_wei .content_box .information_sun .name_box .name h1{font-size: 30px;color: #333;float: left;}
.information_wei .content_box .information_sun .name_box .name h1 span{font-size: 30px; padding-left: 16px; padding-right: 16px;}
.information_wei .content_box .information_sun .name_box  .name .type{display: block;}
.information_wei .content_box .information_sun .name_box  .name .type_1{float: left;width: 60px;height: 26px;background-color: #ff6100;border-radius: 3px;margin-top: 0px;margin-left: 13px;}
.information_wei .content_box .information_sun .name_box  .name .type_1 h2{font-size: 14px; color: #ffffff; text-align: center; line-height: 26px; }
.information_wei .content_box .information_sun .name_box  .name .type_2{ float: left;width: 60px; height: 26px; background-color: #fe9c02;border-radius: 3px;margin-top: 10px; margin-left: 13px;}
.information_wei .content_box .information_sun .name_box  .name .type_2 h2{font-size: 14px; color: #ffffff; text-align: center; line-height: 26px; }
.information_wei .content_box .information_sun .name_box  .name .type_3{ float: left;width: 60px; height: 26px; background-color: #8099af;border-radius: 3px;margin-top: 10px; margin-left: 13px;display: block;}
.information_wei .content_box .information_sun .name_box  .name .type_3 h2{font-size: 14px; color: #ffffff; text-align: center; line-height: 26px; }
.information_wei .content_box .information_sun .name_box  .name .type_sun{float: left;width: 60px;height: 26px;background-color: #e8eefc;border-radius: 3px;margin-top: 1px;margin-left: 9px;display: block;}
.information_wei .content_box .information_sun .name_box  .name .type_sun h2{font-size: 14px; color: #668ae9; text-align: center; line-height: 26px; }
.information_wei .content_box .information_sun .Price{margin-top: 30px; border-bottom: 1px solid #f0f0f0; }
.information_wei .content_box .information_sun .Price .name{width: 78px; float: left;}
.information_wei .content_box .information_sun .Price .name h1{font-size: 14px; color: #ff5200; font-weight: bold;font-family:dinot-bold}
.information_wei .content_box .information_sun .Price .text{float: left;}
.information_wei .content_box .information_sun .Price .text ul li .qi{width: 22px; height: 22px; border-radius: 50%; background-color: #ffede5; float: left;}
.information_wei .content_box .information_sun .Price .text ul li .qi h1{font-size: 14px; color: #ff5200;line-height: 22px; text-align: center; }
.information_wei .content_box .information_sun .Price .text ul li .money{ float: left;margin-left: 7px;}
.information_wei .content_box .information_sun .Price .text ul li .money h1{font-family: dinot-bold;font-size: 14px;margin-top: -2px;color: #ff5200;}
.information_wei .content_box .information_sun .Price .text ul li .money h1 span{font-size: 30px;font-weight: bold;vertical-align: -6px;font-family: dinot-bold;}
.information_wei .content_box .information_sun .Price .text ul li{margin-bottom: 16px;}
.information_wei .content_box .information_sun .measure {margin-top: 11px;position: relative;}
.information_wei .content_box .information_sun .measure .name{ float: left; width: 81px;}
.information_wei .content_box .information_sun .measure .name h1{ font-size: 14px; color: #999999;padding-top: 3px;}
.information_wei .content_box .information_sun .measure .text{ float: left;width: 188px;}
.information_wei .content_box .information_sun .measure .text h1{overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap; }
.information_wei .content_box .information_sun .Price i{font-family:dinot-bold;font-size: 30px;color:  #ff5200;}
.information_wei .content_box .information_sun .measure .text h1 span{font-size: 14px; color: #333333;}
.information_wei .content_box .information_sun .measure .photo{ float: right;}
.information_wei .content_box .information_sun .measure .photo a{font-size: 12px;color: #999999;display: block;background: url(https://static.fangxiaoer.com/web/images/sy/house/view/newHouseinfohx.jpg) no-repeat;padding-left: 20px;background-position: 0 6px;margin-top: 3px;}

.information_wei .content_box .information_sun .time{margin-top: 11px;position: relative;}
.information_wei .content_box .information_sun .time .name{ float: left; width: 81px;}
.information_wei .content_box .information_sun .time .name h1{ font-size: 14px; color: #999999;}
.information_wei .content_box .information_sun .time .text{ float: left;width: 188px;}
.information_wei .content_box .information_sun .time .text p{font-size: 14px; color: #333333;overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;}

.information_wei .content_box .information_sun .time_sun{margin-top: 11px;position: relative;}
.information_wei .content_box .information_sun .time_sun .name{ float: left; width: 81px;}
.information_wei .content_box .information_sun .time_sun .name h1{ font-size: 14px; color: #999999;}
.information_wei .content_box .information_sun .time_sun .text{ float: left;width: 188px;}
.information_wei .content_box .information_sun .time_sun .text p{font-size: 14px; color: #333333;overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;}
.information_wei .content_box .information_sun .time_sun .text .win{width:313px}
.information_wei .content_box .information_sun .time_sun table{position: absolute;right: 0;top: 29px;background-color: #fff;box-shadow: 0px 0px 10px rgba( 119, 114, 114,0.5 );z-index: 9;display: none;color: #000;}
.information_wei .content_box .information_sun .time_sun table th{text-align:left;background-color: #f5f5f5;height: 34px;padding-left: 27px;font-size: 16px;}
.information_wei .content_box .information_sun .time_sun table td{ height:30px}
.information_wei .content_box .information_sun .time_sun table tr td:nth-child(1){
    border-right: 1px solid #ebebeb;
}
.information_wei .content_box .information_sun .time_sun table tr:nth-child(2) td{
    border-top:0;
}
.information_wei .content_box .information_sun .time_sun:hover a{color:#000}
.information_wei .content_box .information_sun .time_sun table tr td{border-top: 1px solid #ededed;font-size: 14px;padding-left: 27px;}
.information_wei .content_box .information_sun .address{margin-top: 11px;position: relative;}
.information_wei .content_box .information_sun .address .name{ float: left; width: 81px;}
.information_wei .content_box .information_sun .address .name h1{ font-size: 14px; color: #999999;}
.information_wei .content_box .information_sun .address .text{float: left;width: 300px;}
.information_wei .content_box .information_sun .address .text p{font-size: 14px; color: #333333;overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;}

.information_wei .content_box .information_sun .configure{margin-top: 11px;position: relative;}
.information_wei .content_box .information_sun .configure .name{ float: left; width: 81px;}
.information_wei .content_box .information_sun .configure .name h1{ font-size: 14px; color: #999999;}
.information_wei .content_box .information_sun .configure .text{float: left;width: 300px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.information_wei .content_box .information_sun .configure .text p{font-size: 14px;color: #333333;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;}

.information_wei .content_box .information_sun .type_sun {margin-top: 11px;position: relative;}
.information_wei .content_box .information_sun .type_sun .name{ float: left; width: 81px;}
.information_wei .content_box .information_sun .type_sun .name h1{ font-size: 14px; color: #999999;padding-top: 3px;}
.information_wei .content_box .information_sun .type_sun .text{ float: left;width: 188px;}
.information_wei .content_box .information_sun .type_sun .text h1{overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap; }
.information_wei .content_box .information_sun .type_sun .text h1 span{font-size: 14px; color: #333333;}
.information_wei .content_box .information_sun .type_sun .photo{ float: right;}
.information_wei .content_box .information_sun .type_sun .photo a{ font-size: 14px; color: #ff5200;display: block;
    background-position: 0 2px;margin-top: 3px; font-weight: bold;}
.information_wei .content_box .information_sun .phone{ margin-top: 15px;}
.information_wei .content_box .information_sun .phone button{width: 100%;height: 54px;border: 0;border-radius: 27px;background-color: #ff6100;color: #fff;font-size: 14px;vertical-align: 4px;box-shadow: 2px 3.464px 10px rgb( 255, 97, 0,0.6 );}
.information_wei .content_box .information_sun .phone button p{display:inherit;font-size: 24px;font-family: dinot-bold;margin-left: 5px;Vertical-align: -3px;}
.information_wei .content_box .information_sun .phone button span{font-size: 14px;font-family: dinot-bold;vertical-align: 3px;}
.information_wei .content_box .information_sun .Admission{ margin-top: 17px;}
.information_wei .content_box .information_sun .Admission ul li{ width: 49.8%; float: left;}
.information_wei .content_box .information_sun .Admission ul li a{ font-size: 16px; color: #fe6116;display: block;width: 92px; margin: 0 auto;
    text-align: right;}
.information_wei .content_box .information_sun .Admission ul li:nth-child(1){border-right: 1px solid #e6e6e6;}
.information_wei .content_box .information_sun .Admission ul li:nth-child(1) a{ background-image: url(https://static.fangxiaoer.com/web/images/Villa/car.png); background-repeat: no-repeat;
    background-position: left center; }
.information_wei .content_box .information_sun .Admission ul li:nth-child(2) a{ background-image: url(https://static.fangxiaoer.com/web/images/Villa/phone.png); background-repeat: no-repeat;
    background-position: left center;}
.Discount_wei{ background-color: #fff;padding-top: 20px; padding-bottom: 21px;}
.Discount_wei .content_box{width: 1170px; height: 126px; margin: 0 auto; background-image: url(https://static.fangxiaoer.com/web/images/Villa/Favorable6.png);}
.Discount_wei .content_box .left_wei{float: left;margin-top: 40px; padding-left: 50px;}
.Discount_wei .content_box .left_wei p{ width: 114px; font-size: 18px; color: #fff; float: left;}
.Discount_wei .content_box .left_wei p span{ font-size: 20px; color: #ffe59b; font-weight: bold;}
.Discount_wei .content_box .left_wei h1{ float: left;}
.Discount_wei .content_box .left_wei h1{font-size: 18px; color: #fff;font-weight: bold;line-height: 45px;}
.Discount_wei .content_box .left_wei h1 span{font-size: 58px;font-family: dinot-bold;}
.Discount_wei .content_box .text{float: left;margin-left: 164px;}
.Discount_wei .content_box .text h1{font-size: 18px; color: #fff;margin-top: 39px;}
.Discount_wei .content_box .text h2{font-size: 18px; color: #fff;margin-top: 13px;}
.Discount_wei .content_box .text h2 span{font-family: dinot-bold;font-size: 18px;}
.Discount_wei .content_box .right_wei{ width: 78px; height: 78px; float: right; border-radius: 50%; border: 1px solid #fdd995;
    margin-top: 22px; margin-right: 62px;}
.Discount_wei .content_box .right_wei a h1{font-size: 18px;width: 36px; margin: 0 auto;margin-top: 16px; color: #ffe59b;}
.Discount_wei .content_box .right_wei a h1 span{font-size: 18px; font-weight: bold;}
.Bright_wei .content_box{width: 1200px; margin: 0 auto;}
.Bright_wei .content_box .title_box h1{font-size: 28px; color: #333333; text-align: center; padding-top: 34px;}
.Bright_wei .content_box .content{background-image: url(https://static.fangxiaoer.com/web/images/Villa/bd.jpg);background-position: center;background-size: 100%;height: 380px;margin-top: 35px;}
.Bright_wei .content_box .content table{ width: 100%;height: 100%;}
.Bright_wei .content_box .content .name h1{ text-align: center; font-size: 32px;}
.Bright_wei .content_box .content .text{width: 1023px; margin: 0 auto;margin-top: 30px;}
.Bright_wei .content_box .content .text p{
    font-size: 14px; color: #333333;line-height: 30px;
}

.dynamic_wei .content_box{width: 1200px; margin: 0 auto;}
.dynamic_wei .content_box .title_box h1{font-size: 28px; color: #333333; text-align: center;margin-top: 35px;}
.dynamic_wei .content_box .content{ background-color:#FFFFFF;padding-top: 86px;margin-top: 37px;}
.dynamic_wei .content_box .content .name{width: 265px; margin: 0 auto; border-top: 1px solid #333333;}
.dynamic_wei .content_box .content .name h1{font-size: 26px;width: 165px; margin: 0 auto; background-color: #fff; font-family: dinot-bold;margin-top: -18px;
    text-align: center;}
.dynamic_wei .content_box .content .text p{font-size:18px;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;}
.dynamic_wei .content_box .content .text{width: 1048px;margin: 0 auto;margin-top: 34px;padding-bottom: 75px;line-height: 36px;}
.dynamic_wei .content_box .title_box {position: relative;}
.dynamic_wei .content_box .title_box a{ font-size: 14px; color: #666666;padding-right: 14px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Arrow.png); background-repeat: no-repeat;
    background-position: right center;position: absolute; right: 0; top: 10px;}
.huxing_wei .content_box{ width: 1200px; margin: 0 auto;}
.huxing_wei .content_box .title_box h1{font-size: 28px; color: #333333; text-align: center;margin-top: 35px;}
.huxing_wei .content_box .title_box {position: relative;}
.huxing_wei .content_box .title_box a{ font-size: 14px; color: #666666;padding-right: 14px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Arrow.png); background-repeat: no-repeat;
    background-position: right center;position: absolute; right: 0; top: 10px;}
.huxing_wei .content_box .content {background-color: #fff;margin-top: 35px;padding-bottom: 20px; padding-top: 20px;}
.huxing_wei .content_box .content ul{width: 1106px; margin: 0 auto;}
.huxing_wei .content_box .content ul li{width: 348px;float: left;/* border: 1px solid #ededed; */margin-right: 28px;}
.huxing_wei .content_box .content ul li .photo img{width: 100%;}
.huxing_wei .content_box .content ul li:nth-child(3){margin-right: 0;}
.huxing_wei .content_box .content ul li .text{background-color: #f5f5f5;padding-left: 21px;padding-right: 21px;height: 124px;position:  relative;left:  0;top: -1px;}
.huxing_wei .content_box .content ul li .text h1{ font-size: 20px; color: #333333; font-weight: bold;padding-top: 22px;}
.huxing_wei .content_box .content ul li .text p{font-size: 14px;color: #333333;overflow:hidden;margin-top: 15px;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;height: 45px;}
.huxing_wei .content_box .content ul li .text:hover a{ text-decoration:none}
.huxing_wei .content_box .content ul li .text:hover a h1{color:#ff5200}
.huxing_wei .content_box .content ul li .photo{ border:1px solid #ededed}
.huxing_wei .content_box .content ul li .text p span{ font-size: 14px; color: #333333;}


.Album_wei .content_box{ width: 1200px; margin: 0 auto;}
.Album_wei .content_box .title_box h1{font-size: 28px; color: #333333; text-align: center;margin-top: 35px;}
.Album_wei .content_box .title_box {position: relative;}
.Album_wei .content_box .title_box a{ font-size: 14px; color: #666666;padding-right: 14px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Arrow.png); background-repeat: no-repeat;
    background-position: right center;position: absolute; right: 0; top: 10px;}
.Album_wei .content_box  .content{background-color: #fff;margin-top: 35px;padding-bottom: 20px; padding-top: 20px;}
.Album_wei .content_box  .content .box_sun{width: 1106px; margin: 0 auto;}
.Album_wei .content_box  .content .box_sun .left_wei{width: 900px; height: 600px; float: left;overflow: hidden;}
.Album_wei .content_box  .content .box_sun .left_wei img{ width: 100%;}
.Album_wei .content_box  .content .box_sun .right_wei{width: 190px; height: 600px; float: right;background-color: #ebebeb; position: relative;}
.Album_wei .content_box  .content .box_sun .right_wei .show{margin: 0 auto;margin-top: 35px; height: 530px; width: 160px; overflow:hidden;position: relative;}
.Album_wei .content_box  .content .box_sun .right_wei ul li{width: 160px; height: 100px;margin-bottom: 7px;overflow:hidden;cursor: pointer;}
.Album_wei .content_box  .content .box_sun .right_wei ul li img{ width: 100%;}
.Album_wei .content_box  .content .box_sun .right_wei .show .lunbo{position: relative;}
.Album_wei .content_box  .content .box_sun .right_wei .show .border{width: 154px; height: 94px; border: 3px solid #ff6100;position: absolute;left: 0; top: 0;}
.Album_wei .content_box  .content .box_sun .right_wei .top_wei{position: absolute;top: 10px; left: 86px;cursor: pointer;}
.Album_wei .content_box  .content .box_sun .right_wei .bottom_wei{position: absolute;bottom: -1px;left: 86px;cursor: pointer;}

.housesLeft{float: none !important;border:none!important;background: none !important;}
.userSynthesize {/* border:1px solid #ededed; */width: 100%!important;background-color: #fff;}
.synthesize{/* margin-top:36px; */background-color: #fff;}
.synthesize p{ text-align:center;font-size:28px; color:#333333}
#xmzx p{text-align:center;font-size:28px;color:#333;margin-top:35px;}
.housesLeft .title a{margin-top: -18px;}

#current_asklist_isNotEmpty{background-color:#fff;/* margin-top: 36px; */}
.houseAnswer{background-color:#fff;padding-bottom:  30px !important;}
.page{background-color:#fff; margin:0 !important; padding-bottom:30px;
    padding-top:30px}
.question_r{ background-color:#fff; margin-top:0px !important}
.w{width:1200px !important}
.play_box{width: 351px;height: 58px;background-color: #fff;border-radius: 29px;box-shadow: 0px 0px 12px 0px rgba( 39, 40, 41,0.2 );position:fixed;right: 32px;bottom: 56px;z-index: 998;}







.measure .text{position:relative}
.time .text{position:relative}
.address .text{position:relative}
.configure .text{position:relative}
.type .text{position:relative}
.layer_sun{display:none;padding-left:15px;position:absolute;left: 80px;top: 27px;line-height:50px;background-color:#fff;box-shadow: 0.5px 0.866px 10px 0px rgba( 0, 0, 0 ,0.3);padding-left:17px;padding-right:17px;z-index:99;max-width: 350px;font-size:14px;color:#333;}
.measure:hover .layer_sun{display:block}
.time_sun:hover .layer_sun{display:block}
.time:hover .layer_sun{display:block}
.address:hover .layer_sun{display:block}
.configure:hover .layer_sun{display:block}
.type:hover .layer_sun{display:block}

.main_wei{width:1200px !important;background-color: #fff;padding-top: 20px;margin-top: 20px;padding-bottom: 20px;margin-bottom: 15px;}

.map_wei{width:  1200px;margin:  0 auto;background-color: #fff;}

.map_wei .nmaptitleleft p{text-align:center;float: none;}




.contentLeft{ width:900px !important;}
.userSynthesize>li{margin-left:15px;margin-right:15px}
#huodongDom{ background-color:#fff; padding-bottom:20px}

.nmaptitleleft{width:1200px;margin:0 auto;padding-left:0 !important;margin-top: 27px !important;margin-bottom: 27px !important;}
.nmaptitleleft p{text-align: left !important;float:none !important;}
.nmaptitleleft i{margin-top: 4px;display:inline-block;}
.nmaptitleleft a{margin-top: 5px;display:inline-block;}
.map_wei{ padding-top:15px; padding-bottom:15px}

.housesLeft{ width:1200px!important}
.synthesize .title{ background-color:#f5f5f5;margin-top:0 !important}

.synthesizeRight{margin-top: 27px!important;
    padding-bottom: 23px!important}

#current_asklist_isNotEmpty{/* border-bottom: 1px solid #f0f0f0!important; */padding-bottom: 23px;}

.housesLeft .houseAnswer{
    margin-top: -3px !important;
    padding: 0 68px !important;
    border-top: 1px solid #f0f0f0!important;
    /* margin-right: 30px; */
    /* margin-left: 30px; */
}

.weixiangying p{font-size:28px!important;padding-left: 217px!important;text-align: center !important;}


.next_line{ padding-top:40px}
.next_line .type_1{margin-left:0 !important}
.type{margin-top:0 !important}

.Company{margin-top: 28px;}
.basic_data{margin-top: 20px;}
.Architecture{margin-top: 28px;}
.introduce{margin-top: 28px;}
.Developers{margin-top: 28px;}
.play_box .player{float:left;margin-top: 18px;font-size:15px;color:#bfbfbf;margin-left: 21px;}
.play_box .player h1{font-size:15px}
.play_box .track_box{width:176px;height:1px;background-color:#cccccc;float:left;margin-top: 28px;margin-left:  8px;margin-right: 8px;}
.play_box .time_wei{float:left;margin-top: 18px;font-size:15px;color:#bfbfbf;margin-right:  8px;}
.play_box .suspend{ float:left;margin-top:15px}

.play_box .progress{ height: 1px; background-color: #ff6100; position: relative; width: 0px;}
.play_box .progress .spot{ width: 10px; height: 10px; background-color: #ff6100; border-radius: 50%; position: absolute;right: -4px; top: -4px; cursor: pointer}

.play_box .time_wei h1{font-size:15px}
.line_wei{ width:100%; height:1px; background-color:#ebebeb;margin-top:49px}
.border_wei{background-color:#fff;padding-top:50px; padding-bottom:50px}
.border_wei .houseAskNoList{margin: 0 auto !important;}
.housesLeft .title p{color:#333; font-size:28px}

.newHouseViewChunk .border_wei{/* margin-top:30px */}

.synthesize .synthesizeLeft b{ font-weight:bold}
.synthesize .synthesizeLeft span{font-weight:bold}


.userSynthesize .contentRight .headerImg.hover>img.bestAgent {
    top: 54px !important;
    left: 50%;
    display: block;
    position: absolute;
    margin-left: -29px;
}
.nmaptitleleft p{line-height:inherit !important}
.newHouseViewChunk .title{padding-top:0}
.nmaptitleleft{ height: auto !important}
.kpTc_heibu, .kfzc_heibu{z-index:9999999999 !important}
.weixiangying{ text-align:center}
.element .style {
}
.kanfang dd ul li b{font-weight:bold}
.kanfang a{font-weight:bold}
.titleAskbtn{border:0!important;padding-top: 27px !important;}
a:hover{text-decoration:none}
.newHouseViewChunk{margin-bottom: 15px;}
.ms_sanjiao {
    width: 22px;
    height: 11px;
    background: url(https://static.fangxiaoer.com/web/images/sy/house/view/view_ts_icon.jpg) no-repeat;
    margin-top: -13px;
    position: absolute;
    left: 35px;
    top: 5px;
}
.contentInfoMmoreLong{overflow:hidden}
.question_r{ border:0 !important}
.footer{margin-top: 30px!important;}

.housesRight{margin-right: 18px !important;}
.salesOffice {height: 143px!important;}
.dynamic_wei .content_box .content .text a:hover{text-decoration:none}

.huxing_wei .content_box .title_box a:hover{text-decoration:none;color:#ff5200;background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/hover.png);
    background-size:5px 9px}


.dynamic_wei .content_box .title_box a:hover{text-decoration:none;color:#ff5200;background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/hover.png);
    background-size:5px 9px}

.Album_wei .content_box .title_box a:hover{text-decoration:none;color:#ff5200;background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/hover.png);
    background-size:5px 9px}
.infoIcontime{
    border:  none;
    background: url(https://static.fangxiaoer.com/web/images/sy/house/view/time_wei.png) no-repeat 0 2px;
    padding-left:  19px;
    border: 1px solid #fff;
    margin-top: 7px;
    cursor:pointer;
    font-size:12px;
    color: #999999 !important;
}
.infoIcontime:hover table{ display:block !important}

.loupan_box .content_box{ width: 1200px; margin: 0 auto;}
.loupan_box .content_box .btn_wei{padding-top: 20px;}
.loupan_box .content_box .btn_wei ul li{float: left;margin-right: 82px;cursor: pointer;}
.loupan_box .content_box .btn_wei ul li h1{font-size: 28px;text-align: center;padding-bottom: 35px;}
.loupan_box .content_box .btn_wei ul li:nth-child(2){margin-right: 0}
.loupan_box .content_box .btn_wei ul .color h1{color: #ff5200}
.loupan_box .content_box .btn_wei{width: 334px; margin: 0 auto}
.loupan_box .content_box .content{ background-color: #fff}
.loupan_box .content_box .content ul{ width: 1114px; margin: 0 auto}
.loupan_box .content_box .content ul li{position: relative}
#samePrice{ width: 1110px;margin: 0 auto}
#relantionShipHouse .loupan  .btn_box ul li:nth-child(2){border-right: 0}
#samePrice{padding-top: 26px;margin-top: 0 !important;}
#samePrice ul li{position: relative}
#samePrice a{ float: left;margin-right: 15px;width: 210px}
#samePrice a:nth-child(5){margin-right: 0}
#samePrice a .photo{height: 154px;background-image: url(https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg);background-size: 100%}
#samePrice a img{width: 210px}
#samePrice a .text_wei{ width: 210px !important;margin-top: 13px;padding-bottom: 26px}
#samePrice a .text_wei .name{font-size: 14px;font-weight: bold; color: #333333;height: 20px; line-height: 20px;display: inline-block; float: left}
#samePrice a .text_wei .type{ float: right;font-size: 12px; color: #668ae9; line-height: 20px; padding-left: 8px; padding-right: 8px; background-color: #eff3fc;margin-top: 13px}
#samePrice a .text_wei .Price{font-size: 20px;font-weight: bold; color: #ff5200; font-family: dinot-bold;height: 20px;line-height: 20px;margin-top: 10px;display: inline-block}
#samePrice a .text_wei .Price label{font-size: 14px;}
#aroundHouse{ width: 1110px;margin: 0 auto}
#aroundHouse{padding-top: 26px;margin-top: 0 !important;}
#aroundHouse a{ float: left;margin-right: 15px;width: 210px}
#aroundHouse a:nth-child(5){margin-right: 0}
#aroundHouse a img{width: 210px}
#aroundHouse a .photo{height: 154px;background-image: url(https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg);background-size: 100%}
#aroundHouse a .text_wei{ width: 210px !important;margin-top: 13px; padding-bottom: 26px}
#aroundHouse a .text_wei .name{font-size: 14px;font-weight: bold; color: #333333;height: 20px; line-height: 20px;display: inline-block; float: left}
#aroundHouse a .text_wei .type{float: right; font-size: 12px; color: #668ae9; line-height: 20px; padding-left: 8px; padding-right: 8px; background-color: #eff3fc;margin-top: 13px}
#aroundHouse a .text_wei .Price{font-size: 20px;font-weight: bold; color: #ff5200; font-family: dinot-bold;height: 20px;line-height: 20px;margin-top: 10px;display: inline-block}
#aroundHouse a .text_wei .Price label{font-size: 14px;}
.play{ width: 62px !important; height: 62px; position: absolute; left: 50%; top: 43px;margin-left: -22px}

.ershouf_box .content_box{ width: 1200px; margin: 0 auto;}
.ershouf_box .content_box .btn_wei{padding-top: 35px;}
.ershouf_box .content_box .btn_wei ul li{cursor: pointer}
.ershouf_box .content_box .btn_wei ul li h1{font-size: 28px;text-align: center;padding-bottom: 35px;}
.ershouf_box .content_box .btn_wei ul li:nth-child(2){margin-right: 0}
.ershouf_box .content_box .btn_wei ul .color h1{color: #ff5200}
.ershouf_box .content_box .btn_wei{width: 334px; margin: 0 auto}
.ershouf_box ul{ margin: 0 auto;}
.ershouf_box .content{ background-color: #fff;padding-top: 26px}
.ershouf_box .content ul{ width: 1110px}
.ershouf_box a{ float: left;margin-right: 15px;width: 210px;}
.ershouf_box a:nth-child(5){margin-right: 0}
.ershouf_box ul li{position: relative}
.ershouf_box ul li .photo{height: 154px;background-image: url(https://images.fangxiaoer.com/sy/esf/fy/middle/noimage375275.jpg);background-size: 100%}
.ershouf_box ul li img{ width: 210px;}
.ershouf_box a .text_wei{ width: 210px !important;padding-bottom: 28px}
.ershouf_box a .text_wei .name{font-size: 14px;font-weight: bold; color: #333333;height: 14px; line-height: 14px;display: inline-block;margin-top: 15px; float: left}
.ershouf_box a .text_wei .Price{float: right; font-size: 20px;font-weight: bold; color: #ff5200; font-family: dinot-bold;height: 20px; line-height: 20px;margin-top: 10px}
.ershouf_box a .text_wei .Price label{font-size: 14px}
.ershouf_box a .text_wei .area{ font-size: 14px; color: #666666; height: 14px; line-height: 14px;margin-top: 10px;display: inline-block}
.saleTrends{margin-left: 15px !important;}
.developers{margin-bottom: 15px;}
.vjs-poster{background-size: unset !important;}
#head2017{min-width: 1200px !important;}
#search2017{min-width: 1200px !important;}
.pinpai .name{float:left;text-decoration: none;}
.pinpai .link{float:right;line-height: 22px;color: #fd4032;font-size: 14px;text-decoration: none;}
.pinpai .name label{display:inline-block;line-height:22px;background-color: #fd4032;width: 46px;text-align: center;color: #fff;margin-right: 8px;}
.pinpai{padding-top:20px;padding-bottom:20px;border-bottom: 1px solid #f0f0f0;}


.voiceTabSy{padding-left:10px;color:#333;font-weight:600;position:relative;margin-top:-23px;width:88px;height:23px;display:block;text-align:center;left:250px;background:#f5f5f5 url(https://sy.fangxiaoer.com/img/vrcode_aduio.png);background-repeat:no-repeat;background-position:9px;background-size:9px;cursor:pointer}
.voiceTabSy:hover {
    padding-left: 10px;
    background: #f5f5f5 url(https://sy.fangxiaoer.com/img/vrcode_aduio_hover.png);
    color: #ff5200!important;
    font-weight: 600;
    background-repeat: no-repeat;
    background-position: 9px;
    background-size: 9px;
    cursor: pointer
}
.vrcodeall{width:142px;height:132px;padding-top:12px;position:relative;margin-top:-303px;text-align:center;background:#fff;left:234px;z-index:1;box-shadow:0 0 20px #0000002e}
.vrcodeall p{margin-top:6px;line-height:17px}
.vrcodeall i{font-size:12px}
.vrcodeall img{width:80px}
.Triangle{width:12px;height:12px;display:block;background:#fff;position:relative;top:6px;left:65px;transform:rotate(45deg)}



.barnd_icon {
    display: inline-block;
    width: 26px;
    height: 24px;
    margin-bottom: -6px;
    margin-left: 6px;
    margin-right: 10px;
    background-image: url(https://static.fangxiaoer.com/web/images/brand/brand.svg);
}
.rank_icon {
    display: inline-block;
    width: 26px;
    height: 24px;
    margin-right: 10px;
    margin-bottom: -6px;
    background-image: url(https://static.fangxiaoer.com/web/images/brand/rank.svg);
}
.pinpai1{
    margin-right: 24px;
}

/*看房团*/
.houseKF{
    width: 1200px;
    margin: 0 auto;
    /* margin-top: 24px; */
    /* border: 1px solid #ededed; */
    /* padding: 18px 15px 0; */
    background: #fff;
    padding-top: 0;
    /* padding-bottom: 20px; */
}
.checking{
    height: 100px;
    /* margin: 17px auto 0px; */
    /* border-bottom: 1px solid #ededed; */
    /* padding-left: 31px; */
    background-color: #f5f5f5;
    text-align: center;
}
.checking p {
    font-size: 28px;
    color: #333333;
    /* border-bottom: 5px solid #ff6600; */
    /* float: left; */
    /* padding: 0 16px; */
    line-height: 100px;
    /* margin-right: 32px; */
}
.checking .more{font-size:12px;margin-right: 30px;    line-height: 60px;
    color: #666;}
.checking .more:hover{
    color:#ff5200;
    cursor:pointer;

}
.KFcontent{
    height: 132px;
}
.imgCar{
    width: 120px;
    height: 130px;
    vertical-align: middle;
}
.imgCar img{
    /* vertical-align: middle; */
    margin-left: 35px;
    margin-top: 40px;
}
.KFcontent ul>li{
    float:left;
}
.KFtimeH1{
    margin-top: 38px;
    font-size: 20px;
    color: #333333;
    margin-bottom: 11px;
    margin-left: -8px;
}
.KFtimeH1 span{
}
.KFdetails{
    font-size: 14px;
    color: #333333;
    font-weight: bold;
}
.KFdetails span{
    color: #666666;
    font-weight: 400;
}
.KFdetailsli{
    margin-right: 18px;
}
.KFdetailsli span{
    margin-right: 10px;
}
.KFdetailsli2{
    margin-right: 28px;
}
.KFdetailsli3 span{
    margin-right: 20px;
}
.KFtimeLi2 a{
    display: block;
    width: 150px;
    height: 30px;
    color: #ffffff;
    text-align: center;
    background-color: #ff6100;
    line-height: 30px;
    font-size: 14px;
    margin-top: 36px;
}
.KFtimeLi2{
    float:right !important;
    width:190px

}
.KFtimeLi2 span{
    width: 150px;
    text-align:center;
    display: block;
    color: #666666;
    font-size: 14px;
    margin-top: 10px;
}
.KFtimeLi2 span b{
    color: #ff5200;
    /* margin-top: 10px; */
}