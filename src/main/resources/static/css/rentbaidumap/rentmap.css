/*2018.11.06*/
/*ÃƒÆ’Ã‚Â¤Ãƒâ€šÃ‚ÂºÃƒâ€¦Ã¢â‚¬â„¢ÃƒÆ’Ã‚Â¦ÃƒÂ¢Ã¢â€šÂ¬Ã‚Â°ÃƒÂ¢Ã¢â€šÂ¬Ã‚Â¹ÃƒÆ’Ã‚Â¦Ãƒâ€¹Ã¢â‚¬Â Ãƒâ€šÃ‚Â¿ÃƒÆ’Ã‚Â¥Ãƒâ€¦Ã¢â‚¬Å“Ãƒâ€šÃ‚Â°ÃƒÆ’Ã‚Â¥ÃƒÂ¢Ã¢â€šÂ¬Ã‚ÂºÃƒâ€šÃ‚Â¾ÃƒÆ’Ã‚Â¦ÃƒÂ¢Ã¢â€šÂ¬Ã‚Â°Ãƒâ€šÃ‚Â¾ÃƒÆ’Ã‚Â¦Ãƒâ€¹Ã¢â‚¬Â Ãƒâ€šÃ‚Â¿ÃƒÆ’Ã‚Â¦Ãƒâ€šÃ‚Â Ãƒâ€šÃ‚Â·ÃƒÆ’Ã‚Â¥Ãƒâ€šÃ‚Â¼Ãƒâ€šÃ‚Â*/
/*ÃƒÆ’Ã‚Â¦Ãƒâ€šÃ‚ÂÃƒâ€¦Ã¢â‚¬Å“ÃƒÆ’Ã‚Â§Ãƒâ€šÃ‚Â´Ãƒâ€šÃ‚Â¢ÃƒÆ’Ã‚Â¦Ãƒâ€šÃ‚Â¡ÃƒÂ¢Ã¢â€šÂ¬Ã‚Â ÃƒÆ’Ã‚Â¦Ãƒâ€šÃ‚Â Ãƒâ€šÃ‚Â·ÃƒÆ’Ã‚Â¥Ãƒâ€šÃ‚Â¼Ãƒâ€šÃ‚Â*/
.searchEsfMap{
    margin: 10px 0 10px 10px;
    width: 530px;
    overflow: hidden;
    position:  relative;
}
.searchEsfMap .searchMapInput{
    padding-left:5px;
    width: 430px;
    height: 33px;
    line-height: 33px;
    border: 1px solid #ddd;
    float: left;
}
.searchEsfMap .searchMapBtn{
    width: 80px;
    height: 35px;
    height: 35px;
    line-height: 35px;
    float: left;
    background: #ff5200;
    border: none;
    color: #fff;
    margin-left: -2px;
}
#deleteButton{
    position: absolute;
    top: 8px;
    right: 105px;
    cursor: pointer;
    display: none;
}
#map {
    min-width: 750px;
}

.mapNav {
    background: #313131;
    width: 60px;
    color: #fff;
    text-align: center;
    float: left;
    height: 100%;
}

.mapNav li {
    cursor: pointer;
    height: 85px;
}

.mapNav li:hover {
    background: #535353
}

.mapNav .hover {
    background: #535353
}

.mapNav i {
    display: block;
    margin: 0 auto;
    text-align: center;
    width: 20px;
    height: 18px;
    padding-top: 29px;
}

.mapNav .img1 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -58px;
}

.mapNav .img2 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -95px;
}

.mapNav .img3 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -126px;
}

.mapNav .img4 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -22px;
}

.mapNav .img5 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px 18px;
}

.mapNav .img6 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 20px 22px;
}

.mapNav .img7 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") -30px -19px;
}

.mapList {
    float: left;
    width: 420px;
    height: 100%;
    overflow: hidden;
    position: relative;
}

.mapList .mapSelect {
    background: #eee;
    color: #666;
    /* height: 60px; */
    /* position: absolute; */
    width: 100%;
    overflow:  hidden;
    padding-bottom: 20px;
}

.mapList .mapSelect>div {
    padding-left: 8px;
    float: left;
    margin-top: 13px;
    /* position: relative; */
}

.mapList .mapSelect>div>p {
    width: 120px;
    height: 23px;
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 103px -274px no-repeat #fff;
    display: block;
    padding: 3px 5px;
    cursor: pointer;
    overflow: hidden;
}
.mapList .mapSelect .noHouseMap {
    text-align:  center;
    width:  100%;
    background:  #fff;
    padding: 50px 0;

}
.mapList .mapSelect .noHouseMap h4{}
.mapList .mapSelect .noHouseMap p{
    width:  100%;
}
.mapList .mapSelect .noHouseMap p a{
    color: #ff5200;
    font-weight:  bold;
    font-size: 14px;
}
.mapList .mapSelect>div>ul {
    position: absolute;
    z-index: 9999;
}

.mapList .mapSelect>div>ul>li {
    width: 113px;
    font-size: 13px;
    height: 23px;
    background: #f3f3f3;
    display: block;
    padding: 3px 5px;
    cursor: pointer;
}

.mapList .mapSelect>div>ul>li:hover {
    background: #fff;
    color: #ff5200;
}

.mapList .mapHouseList {
    overflow-y: scroll;
    margin-top: 60px;
}

.mapList .mapHouseList li {
    padding: 8px 10px;
    overflow: hidden;
    border-bottom: 1px solid #eee;
    min-height: 118px;
    cursor: pointer;
    position: relative;
}

.mapList .mapHouseList li a {
    text-decoration: none;
    color: #333;
}

.mapList .mapHouseList li a:hover {
    #333;
}

.mapList .mapHouseList li a img {
    width: 160px;
    float: left;
    top: 50%;
    position: absolute;
    margin-top: -59px;
    left: 10px;
}

.mapList .mapHouseList li a div {
    padding-left: 176px;
    float: left;
    width: 200px;
    line-height: 24px;
    margin-top: -6px;
}

.mapList .mapHouseList li a div>h1 {
    font-size: 16px;
    line-height: 30px;
    float: left;
}

.mapList .mapHouseList li a div>p {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.mapList .mapHouseList li a div>span {
    display: block;
    color: #fff;
    float: left;
    background-color: #8099af;
    padding-left: 10px;
    padding-right: 10px;
    margin-top: 1px;
    border-radius: 3px;
    margin-left: 6px;
}

.mapList .mapHouseList li a div>b {
    display: block;
    font-weight: 400;
}

.mapList .mapHouseList li a div>b span {
    color: #ff5200;
}

.mapSaleHouseList {
    margin-top: 13px;
    overflow-y: scroll;
    /* position: relative; */
    /* float:  left; */
}

#loading {
    position: absolute;
    top: 50%;
    left: 234px;
    display: none;
}

.mapSaleHouseList ul li {
    cursor: pointer;
    overflow: hidden;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin: 0 10px;
}
.mapSaleHouseList ul li .move{
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") -35px -449px;
    display: block;
    width: 15px;
    height: 18px;
    cursor: pointer;
}
.mapSaleHouseList ul li>a {
    overflow: hidden;
}

.mapSaleHouseList ul li>a:hover {
    color: #333;
    text-decoration: none;
}

.mapSaleHouseList ul li>a>img {
    width: 160px;
    display: block;
    float: left;
    margin-right: 10px;
}

.mapSaleHouseList ul li>a>div {
    float: left;
    width: 320px;
}

.mapSaleHouseList ul li>a>div h1 {
    font-size: 16px;
    display: block;
    width: 280px;
    position: relative;
}
.mapSaleHouseList ul li>a>div h1 i{
    position:absolute;
    top: 5px;
    right: -30px;
}
.mapSaleHouseList ul li>a>div h1 p{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.mapSaleHouseList ul li>a>div>div {
    line-height: 30px;
    overflow: hidden;
}

.mapSaleHouseList ul li>a>div>div p {
    display: block;
    float: left;
    max-width: 230px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.mapSaleHouseList ul li>a>div>div span {
    text-align: right;
    float: right;
    font-size: 12px;
}

.mapSaleHouseList ul li>a>div>div h4 span {
    font-size: 18px;
    color: #ff5200;
    font-weight: 400;
}

.mapSaleHouseList ul li>a>div>div div {
    float: left;
    border: 1px solid #eee;
    padding: 0 6px;
    margin-right: 4px;
    line-height: 26px;
    font-size: 12px;
}

.mapSaleHouseList .tese_1 {
    border: 1px #f5b3bb solid;
}

.mapSaleHouseList .tese_2 {
    border: 1px #91d9ff solid;
}

.mapSaleHouseList .tese_3 {
    border: 1px #bde980 solid;
}

.mapSaleHouseList .tese_4 {
    border: 1px #c7adef solid;
}

#baiduMap {
    float: left;
    height: 100%;
}

.showPlat,
.showRegion {
    width: 80px;
    padding-top: 18px;
    height: 62px;
    display: block;
    background: #ff5200;
    color: #fff;
    text-align: center;
    border-radius: 100%;
}
.showPlat{
    width: 70px;
    height: 58px;
    padding-top: 12px;
}
.showPlat span,
.showRegion span {
    display: block;
    white-space: nowrap;
    text-align: center;
    width: 200px;
    margin-left: -65px;
}
.showPlat .shadow,
.showRegion .shadow {
    text-shadow: 1px 1px 1px #000;
}

.showRegion span{
    margin-left: -60px;
}

.showHouse,
.showsubWay,.newShowHouse{
    position: absolute;
    height: 20px;
    padding: 2px 5px;
    line-height: 20px;
    white-space: nowrap;
    font-size: 12px;
    background-color: #ff5200;
    display: block;
    border-radius: 3px;
    behavior: url(http://plug.fangxiaoer.com/PIE/PIE.htc);
    text-align: center;
    color: #fff;
}

.showsubWay {
    background: #fff;
    color: #666;
    border: 1px solid #e66a6a;
    z-index: 10000;
}

.showHouseHover b,
.showsubWay b ,.newShowHouse b{
    background-color: #fff;
    position: absolute;
    height: 18px;
    top: 0px;
    left: 100%;
    overflow: hidden;
    display: blok;
    color: #666;
    line-height:18px;
    padding: 2px 4px;
    font-weight: 400;
    border:1px solid #41a8f3;
}

.showHouseHover,
.showsubWayHover {
    z-index: 20000!important;
    position: absolute;
    height: 20px;
    padding: 2px 5px;
    line-height: 20px;
    white-space: nowrap;
    font-size: 12px;
    background-color: #41a8f3;
    display: block;
    border-radius: 3px;
    behavior: url(http://plug.fangxiaoer.com/PIE/PIE.htc);
    text-align: center;
    color: #fff;
}

.showHouseHover ,.newShowHouse:hover{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.showsubWayHover,.newShowHouse:hover {
    background-color: #e66a6a;
}

.showHouse b,
.showsubWay b,.newShowHouse b{
    display: none;
}
.newShowHouse:hover b{
    display: block;
}
.showHouse div,
.showsubWay divÃƒÆ’Ã‚Â¯Ãƒâ€šÃ‚Â¼Ãƒâ€¦Ã¢â‚¬â„¢ .newShowHouse div{
    background: url(https://static.fangxiaoer.com/web/images/ico/map/mapicon.png) 0px -317px no-repeat;
    position: absolute;
    width: 11px;
    height: 10px;
    top: 24px;
    left: 45%;
    overflow: hidden;
}

.showsubWay div {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -359px no-repeat;
}

.showHouseHover div,
.showsubWayHover div .newShowHouse:hover{
    background: url(https://static.fangxiaoer.com/web/images/ico/map/mapicon.png) 0px -330px no-repeat;
    position: absolute;
    width: 11px;
    height: 10px;
    top: 24px;
    left: 45%;
    overflow: hidden;
}

.showsubWayHover div {
    background: url(https://static.fangxiaoer.com/web/images/ico/map/mapicon.png) 0px -380px no-repeat;
}

.hint {
    position: fixed;
    top: 0;
    left: 0;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b60.png");
    width: 100%;
    height: 100%;
    z-index: 99999999;
}

.hint dl {
    width: 396px;
    margin: 0 auto;
    border-radius: 10px;
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -198px;
    margin-top: -160px;
}

.hint dl dt {
    text-align: right;
}

.hint dl dt img {
    cursor: pointer;
}

.hint dl dd {
    background: #fff;
    overflow: hidden;
}

.hint dl dd p {
    font-size: 20px;
    text-align: center;
    padding: 30px;
    line-height: 30px;
}

.hint dl dd img {}

.hint dl dd a {
    display: inline-block;
    width: 50%;
    float: left;
    text-align: center;
    box-sizing: border-box;
    line-height: 56px;
    font-size: 16px;
    text-decoration: none;
}

.hint dl dd a:hover {
    color: #666
}

.bw{float:none !important;}
.noHouseMap {
    text-align:  center;
    width:  100%;
    background:  #fff;
    padding: 50px 0;

}
.noHouseMap h4{}
.noHouseMap p{
    width:  100%;
}
.noHouseMap p a{
    color: #ff5200;
    font-weight:  bold;
    font-size: 14px;
}
.ac_results ul {
    width: 100%;
    list-style-position: outside;
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 400px !important;
}
.Choice p label {
    display: block;
    width: 62px;
    float: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 8px;
}
.Choice p {
    float: left;
    line-height: 24px;
    border: 1px solid #e6e5e5;
    padding-right: 1px;
    margin-right: 4px;
    width: 92px;
    margin-bottom: 10px;
}
.Choice {
    padding-top: 10px;
    border-bottom: 1px solid #ededed;
}
.Choice h1 {
    font-size: 12px;
    float: left;
    margin-left: 10px;
    line-height: 26px;
}
mapList .mapSelect>div>p {
    width: 104px;
    padding-left: 15px;
    background: url(https://static.fangxiaoer.com/web/images/map/arra.png);
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: 105px center;
}
.cancelAll {
    float: left;
    padding-left: 17px;
    background-image: url(https://static.fangxiaoer.com/web/images/ico/map/Trash.png);
    background-position: left center;
    background-repeat: no-repeat;
    margin-left: 5px;
    cursor: pointer;
}

