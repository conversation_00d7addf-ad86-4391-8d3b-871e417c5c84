#subscrip {
    width: 100%;
    height: auto;
}
.layui-layer-hui{
    z-index: 99999999999999 !important;
}
.subscrip {
    width: 1170px;
    margin: 0 auto;
    border: 1px solid #ededed;
    margin-bottom: 28px;
}

.subscrip .subtitle {
    width: 100%;
    height: 60px;
    border-bottom: 1px solid #ededed;
}

.subtitle p {
    font-size: 20px;
    color: #333333;
    float: left;
    padding: 0 30px;
    line-height: 60px;
    margin-right: 32px;
}

.subscrip .rging {
    padding: 20px 18px 20px 30px;
    overflow: hidden;
}

.rging_left {
    width: 630px;
    height: 270px;
    float: left;
    position: relative;
}

.rging_left p {
    width: 100%;
    height: 30px;
    line-height: 30px;
    color: #FFFFFF;
    font-size: 14px;
    background: rgba(0, 0, 0, 0.4);
    position: absolute;
    left: 0;
    bottom: 0;
    text-indent: 1.5em;
}

.rging_left ul {
    width: auto;
    height: auto;
    position: absolute;
    top: 16px;
    right: 12px;
}

.rging_left ul li {
    width: 57px;
    height: 24px;
    margin-left: 6px;
    float: left;
    border-radius: 3px;
}

.rging_left ul li i {
    display: block;
    width: 14px;
    height: 14px;
    float: left;
    margin: 5px 4px 0 7px;
}

.rging_left ul li .sale1 {
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/Onsale.png) no-repeat;
    background-size: 100% 100%;
}

.rging_left ul li .sale2 {
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/Forsale.png) no-repeat;
    background-size: 100% 100%;
}

.rging_left ul li span {
    display: block;
    float: left;
    line-height: 24px;
    color: #FFFFFF;
    font-size: 12px;
}

.rging_left ul li:nth-child(1) {
    background: #ff5200;
}

.rging_left ul li:nth-child(2) {
    background: #fe9735;
}

.rging_left ul li:nth-child(3) {
    background: #cbcbcb;
}

.rging_right {
    width: 470px;
    height: auto;
    float: right;
}
.rging_right h1{
    font-size: 16px;
    color: #333333;
}
.rging_right ul{
    width: 100%;
    height: auto;
    overflow: hidden;
}
.rging_right ul li{
    width: 100%;
    height: 42px;
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/listbg.png) no-repeat;
    background-size: 100% 100%;
    margin-top: 19px;
    line-height: 42px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
    cursor: pointer;
}
.roomsele {
    width: 58px;
    height: 24px;
    float: right;
    background: #fe5200;
    border-radius: 2px;
    line-height: 24px;
    text-align: center;
    color: #FFFFFF;
    font-size: 14px;
}

.mask_bg {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 99999999;
    display: none;
}
.closer {
    width: 17px;
    height: 17px;
    background: #000000;
    position: absolute;
    top: 20px;
    right: 33px;
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/close.png) no-repeat;
    background-size: 100% 100%;
    cursor:pointer;
}
.closer1 {
    width: 17px;
    height: 17px;
    background: #000000;
    position: absolute;
    top: 20px;
    right: 33px;
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/close.png) no-repeat;
    background-size: 100% 100%;
    cursor:pointer;
}
/*选择户型*/
.layouty {
    width: 800px;
    height: 664px;
    background: #FFFFFF;
    position: relative;
    border-radius: 5px;
    margin: 0 auto;
    margin-top: 100px;
    overflow: hidden;
    display: none;
}
.layouty h1{
    font-size: 20px;
    color: #333333;
    text-align: center;
    margin: 36px 0 24px 0;
}
.layouty_list{
    height: 465px;
    padding: 0 30px 0 40px;
    border-bottom: 1px solid #ededed;
    overflow: hidden;
}
.layouty_list ul{
    overflow: hidden;
}
.layouty_list ul li{
    width: 346px;
    height: 118px;
    border: 1px solid #ededed;
    float: left;
    margin: 0 18px 18px 0;
    position: relative;
    cursor: pointer;
}
.layouty_list ul li:nth-child(2n){
    margin-right: 0;
}
.layouty_list ul li .layout_img{
    width: 118px;
    height: 118px;
    display: block;
    float: left;
}
.layouty_list ul li .lay_rig{
    float: left;
    overflow: hidden;
    padding-top: 20px;
    padding-left: 22px;
}
.layouty_list ul li .lay_rig h1{
    font-size: 20px;
    color: #333333;
    margin: 0;
    text-align: left;
}
.layouty_list ul li .lay_rig p{
    font-size: 14px;
    line-height: 14px;
    color: #666666;
    margin-top: 14px;
}
.layouty_list .hovtt{
    width: 344px;
    height: 116px;
    border: 2px solid #ff5200;
    float: left;
    margin: 0 18px 18px 0;
    position: relative;
}
.layouty_list .hovtt .layout_img{
    width: 116px;
    height: 116px;
}
.layouty_list .hovtt .lay_rig{
    padding-top: 19px;
}
.layouty_list .hovtt .yest{
    display: block;
}
.layouty_list ul li .yest{
    width: 26px;
    height: 26px;
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/yest.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    bottom: 0;
    right:-1px;
    display: none;
}
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}
/*定义滚动条的轨道颜色、内阴影及圆角*/
::-webkit-scrollbar-track{
    background:#eaeaea;
}
/*定义滑块颜色、内阴影及圆角*/
::-webkit-scrollbar-thumb{
    background:#cccccc;
}
.laybuttom{
    width: 410px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    color: #ffffff;
    font-size: 15px;
    font-weight: bold;
    background: #ff5200;
    border-radius: 3px;
    margin: 0 auto;
    margin-top: 35px;
}
/*选择房源*/
.Housing {
    width: 800px;
    height: auto;
    background: #FFFFFF;
    position: relative;
    border-radius: 5px;
    margin: 0 auto;
    margin-top: 100px;
    overflow: hidden;
    display: none;
}
.Housing h1{
    font-size: 20px;
    color: #333333;
    text-align: center;
    margin: 36px 0 24px 0;
}
.drop_top{
    width: 687px;
    height: 45px;
    margin: 0 auto;
    margin-top: 24px;
}
.drop_top .drop_left{
    float: left;
    position: relative;
}
.drop_top .drop_rig{
    float: right;
    position: relative;
}
.drop_top div span{
    display: block;
    float: left;
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    line-height: 45px;
}
.drop_top div .drop_sk{
    width: 252px;
    height: 45px;
    background: #f2f4f6 url(https://static.fangxiaoer.com/web/images/sy/fill/drop.png) 228px 20px no-repeat;
    background-size: 8px 5px;
    border-radius: 3px;
    float: left;
    margin-left: 18px;
    line-height: 45px;
    color: #999999;
    font-size: 14px;
    text-indent: 1em;
    cursor: pointer;
}
.drop_sk span{
    font-weight: 400 !important;
}
.drop_top div ul{
    width: 252px;
    height: 180px;
    background: #ffffff;
    border-radius: 3px;
    position: absolute;
    top: 45px;
    right: 0;
    box-shadow: 0px 3px 9px rgba(0, 0, 0, 0.2);
    overflow-y: scroll;
    display: none;
}
.drop_top div ul li{
    width: 100%;
    height: 45px;
    font-size: 14px;
    color: #333333;
    line-height: 45px;
    text-indent: 1em;
    cursor: pointer;
}
.que{
    width: 100%;
    overflow: hidden;
    margin: 20px 0;
}
.que img{
    display: block;
    width: 200px;
    margin: 0 auto;
}
.que p{
    width: 200px;
    text-indent: 3.5em;
    font-size: 14px;
    color: #333333;
    margin: 0 auto;
    margin-top: 16px;
}
.roomber{
    width: 100%;
    height: 278px;
    margin-top: 32px;
    display: none;
}
.room_list{
    width: 100%;
    height: 198px;
    border-bottom: 1px solid #EDEDED;
    overflow-y: scroll;
}
.room_list ul{
    padding: 0 46px 0 56px;
    overflow: hidden;
}
.room_list ul li{
    width: 50px;
    height: auto;
    float: left;
    margin-right: 29px;
    margin-bottom: 22px;
}
.room_list ul li:nth-child(9n){
    margin-right: 0;
}
.room_list ul li i{
    display: block;
    width: 50px;
    height: 50px;
}
.room_list ul li p{
    font-size: 14px;
    line-height: 14px;
    margin-top: 10px;
    text-align: center;
}
.room_list ul .col1 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/col1.png) no-repeat;
    background-size: 100% 100%;
    color: #999999;
}
.room_list ul .col2 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/col2.png) no-repeat;
    background-size: 100% 100%;
}
.room_list ul .col3 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/col3.png) no-repeat !important;
    background-size: 100% 100% !important;
}
.room_list ul .col4 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/col4.png) no-repeat;
    background-size: 100% 100%;
}
.room_list ul .col1 p,.room_list ul .col4 p{
    color: #999999;
}
.room_list ul .col2 p{
    color: #fcb405;
}
.room_list ul .col3 p{
    color: #ff8b52 !important;
}
.duce{
    width: 100%;
    height: auto;
}
.duce ul{
    width: 405px;
    height: 30px;
    margin: 0 auto;
    padding-top: 23px;
}
.duce ul li{
    float: left;
    height: 30px;
    margin-right: 35px;
}
.duce ul li i{
    display: block;
    width: 30px;
    height: 30px;
    float: left;
    margin-right: 10px;
}
.duce ul li p{
    float: left;
    font-size: 14px;
    line-height: 30px;
    color: #333333;
}
.duce ul li i img{
    width: 100%;
    height: 100%;
}
.duce ul li:nth-child(4){
    margin-right: 0;
}
.duce_bottom{
    width: 100%;
    height: 240px;
    background: #f2f4f6;
    overflow: hidden;
}
.Housing .hxdils{
    width: 346px;
    height: 118px;
    border: 1px solid #e6e6e6;
    background: #ffffff;
    margin: 0 auto;
    margin-top: 26px;
}
.Housing .hxdils img{
    display: block;
    width: 118px;
    height: 118px;
    float: left;
}
.Housing .hxdils .hxdilsrig{
    float: left;
    padding: 20px 0 0 22px;
}
.Housing .hxdils .hxdilsrig h1{
    margin: 0;
    font-size: 20px;
    color: #333333;
}
.Housing .hxdils .hxdilsrig p{
    font-size: 14px;
    line-height: 14px;
    color: #666666;
    margin-top: 14px;
}
.roombuttom{
    width: 410px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    color: #ffffff;
    font-size: 15px;
    font-weight: bold;
    background: #ff5200;
    border-radius: 3px;
    margin: 0 auto;
    margin-top: 22px;
}
.issi{
    cursor: pointer;
}
/*选择车位*/
.parking_lot {
    width: 800px;
    height: 675px;
    background: #FFFFFF;
    position: relative;
    border-radius: 5px;
    margin: 0 auto;
    margin-top: 100px;
    overflow: hidden;
    display: none;
}
.queey{
    width: 100%;
    overflow: hidden;
    margin: 122px 0;
}
.queey img{
    display: block;
    width: 200px;
    margin: 0 auto;
}
.queey p{
    width: 200px;
    text-indent: 3.5em;
    font-size: 14px;
    color: #333333;
    margin: 0 auto;
    margin-top: 16px;
}
.parking_lot h1{
    font-size: 20px;
    color: #333333;
    text-align: center;
    margin: 30px 0 24px 0;
}
.lot_top{
    height: 45px;
    padding: 0 55px;
    position: relative;
}
.lot_top span{
    display: block;
    line-height: 45px;
    font-size: 14px;
    color: #333333;
    float: left;
    font-weight: bold;
}
.lotrig{
    width: 614px;
    height: 45px;
    background: #f2f4f6 url(https://static.fangxiaoer.com/web/images/sy/fill/drop.png) 590px 20px no-repeat;
    background-size: 8px 5px;
    font-size: 14px;
    color: #999999;
    float: right;
    line-height: 45px;
    text-indent: 1em;
}
.lot_top ul{
    width: 614px;
    height: 180px;
    background: #ffffff;
    border-radius: 3px;
    position: absolute;
    top: 45px;
    right: 55px;
    box-shadow: 0px 3px 9px rgba(0, 0, 0, 0.2);
    overflow-y: scroll;
    display: none;
}
.lot_top ul li{
    width: 100%;
    height: 45px;
    font-size: 14px;
    color: #333333;
    line-height: 45px;
    text-indent: 1em;
}
.lotrig span{
    font-weight: 400 !important;
}
.parking_lot .lot_list{
    height: 354px;
    margin-top: 39px;
    overflow-y: scroll;
    border-bottom: 1px solid #ededed;
}
.lot_list ul{
    padding:0 39px 0 49px;
    overflow: hidden;
}
.lot_list ul li{
    width: 60px;
    height: auto;
    float: left;
    margin-right: 46px;
    margin-bottom: 38px;
}
.lot_list ul li:nth-child(7n){
    margin-right: 0;
}
.lot_list ul li i{
    display: block;
    width: 60px;
    height: 60px;
}
.lot_list ul li p{
    font-size: 14px;
    line-height: 14px;
    margin-top: 10px;
    text-align: center;
}
.lot_list ul .lot1 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/lot1.png) no-repeat;
    background-size: 100% 100%;
}
.lot_list ul .lot2 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/lot2.png) no-repeat;
    background-size: 100% 100%;
}
.lot_list ul .lot3 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/lot3.png) no-repeat !important;
    background-size: 100% 100% !important;
}
.lot_list ul .lot4 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/lot4.png) no-repeat;
    background-size: 100% 100%;
}
.lot_list ul .lot1 p,.lot_list ul .lot4 p{
    color: #999999;
}
.lot_list ul .lot2 p{
    color: #fcb405;
}
.lot_list ul .lot3 p{
    color: #ff8b52 !important;
}
.lot_bottom{
    width: 100%;
    height: auto;
}
.lot_bottom ul{
    width: 413px;
    height: 37px;
    margin: 0 auto;
    padding-top: 27px;
}
.lot_bottom ul li{
    float: left;
    height: 37px;
    margin-right: 28px;
}
.lot_bottom ul li i{
    display: block;
    width: 37px;
    height: 37px;
    float: left;
    margin-right: 10px;
}
.lot_bottom ul li p{
    float: left;
    font-size: 14px;
    line-height: 37px;
    color: #333333;
}
.lot_bottom ul li i img{
    width: 100%;
    height: 100%;
}
.lot_bottom ul li:nth-child(4){
    margin-right: 0;
}
.lotbuttom{
    width: 410px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    color: #ffffff;
    font-size: 15px;
    font-weight: bold;
    background: #ff5200;
    border-radius: 3px;
    margin: 0 auto;
    margin-top: 20px;
}
/*选择商铺*/
.shops {
    width: 800px;
    height: 530px;
    background: #FFFFFF;
    position: relative;
    border-radius: 5px;
    margin: 0 auto;
    margin-top: 11%;
    overflow: hidden;
    display: none;
}
.shops h1{
    font-size: 20px;
    color: #333333;
    text-align: center;
    margin: 36px 0 24px 0;
}
.shops_top{
    height: 45px;
    padding: 0 40px;
    margin: 0 auto;
    margin-top: 24px;
}
.shops_top .shops_left{
    float: left;
    position: relative;
}
.shops_top .shops_rig{
    float: right;
    position: relative;
}
.shops_top div span{
    display: block;
    float: left;
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    line-height: 45px;
}
.shops_top div .shops_sk{
    width: 270px;
    height: 45px;
    background: #f2f4f6 url(https://static.fangxiaoer.com/web/images/sy/fill/drop.png) 240px 20px no-repeat;
    background-size: 8px 5px;
    border-radius: 3px;
    float: left;
    margin-left: 20px;
    line-height: 45px;
    color: #999999;
    font-size: 14px;
    text-indent: 1em;
    cursor: pointer;
}
.shops_sk span{
    font-weight: 400 !important;
}
.shops_top div ul{
    width: 270px;
    height: 180px;
    background: #ffffff;
    border-radius: 3px;
    position: absolute;
    top: 45px;
    right: 0;
    box-shadow: 0px 3px 9px rgba(0, 0, 0, 0.2);
    overflow-y: scroll;
    display: none;
}
.shops_top div ul li{
    width: 100%;
    height: 45px;
    font-size: 14px;
    color: #333333;
    line-height: 45px;
    text-indent: 1em;
    cursor: pointer;
}
.shops_bottom{
    width: 100%;
    overflow: hidden;
    margin-top: 32px;
    display: none;
}
.shops_default{
    width: 100%;
    overflow: hidden;
    /*display: none;*/
}
.shops_default img{
    display: block;
    width: 238px;
    height: 205px;
    margin: 0 auto;
    margin-top: 60px;
}
.shops_default p{
    width: 100%;
    text-align: center;
    font-size: 14px;
    color: #333333;
    margin: 0 auto;
    margin-top: 16px;
}
.shops_list{
    height: 197px;
    padding: 0 40px;
    overflow: hidden;
    border-bottom: 1px solid #ededed;
}
.shops_list ul{
    width: 380px;
    height: 170px;
    float: left;
    overflow-y: scroll;
}
.shops_list ul li{
    width: 50px;
    height: auto;
    float: left;
    margin-right: 25px;
    margin-bottom: 22px;
}
.shops_list ul li:nth-child(5n){
    margin-right: 0;
}
.shops_list ul li i{
    display: block;
    width: 50px;
    height: 48px;
}
.shops_list ul li p{
    font-size: 14px;
    line-height: 14px;
    margin-top: 10px;
    text-align: center;
}
.shops_list ul .shops1 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/shops1.png) no-repeat;
    background-size: 100% 100%;
}
.shops_list ul .shops2 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/shops2.png) no-repeat;
    background-size: 100% 100%;
}
.shops_list ul .shops3 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/shops3.png) no-repeat !important;
    background-size: 100% 100% !important;
}
.shops_list ul .shops4 i{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/shops4.png) no-repeat;
    background-size: 100% 100%;
}
.shops_list ul .shops1 p,.shops_list ul .shops4 p{
    color: #999999;
}
.shops_list ul .shops2 p{
    color: #fcb405;
}
.shops_list ul .shops3 p{
    color: #ff8b52 !important;
}
.shops_list .shshxdils{
    width: 322px;
    height: 118px;
    border: 1px solid #e6e6e6;
    background: #ffffff;
}
.shops_list .ovhie{
    width: 324px;
    height: 120px;
    float: right;
    overflow: hidden;
    margin-top: 25px;
}
.shops_list .shshxdils img{
    display: block;
    width: 118px;
    height: 118px;
    float: left;
}
.shops_list .shshxdils .shopsrig{
    float: left;
    padding: 20px 0 0 22px;
}
.shops_list .shshxdils .shopsrig h1{
    margin: 0;
    font-size: 20px;
    color: #333333;
}
.shops_list .shshxdils .shopsrig p{
    font-size: 14px;
    line-height: 14px;
    color: #666666;
    margin-top: 14px;
}
.shops_intro{
    width: 100%;
    height: auto;
}
.shops_intro ul{
    width: 410px;
    height: 30px;
    margin: 0 auto;
    padding-top: 30px;
}
.shops_intro ul li{
    float: left;
    height: 30px;
    margin-right: 32px;
}
.shops_intro ul li i{
    display: block;
    width: 33px;
    height: 30px;
    float: left;
    margin-right: 10px;
}
.shops_intro ul li p{
    float: left;
    font-size: 14px;
    line-height: 30px;
    color: #333333;
}
.shops_intro ul li i img{
    width: 100%;
    height: 100%;
}
.shops_intro ul li:nth-child(4){
    margin-right: 0;
}
.shopsbuttom{
    width: 410px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    color: #ffffff;
    font-size: 15px;
    font-weight: bold;
    background: #ff5200;
    border-radius: 3px;
    margin: 0 auto;
    margin-top: 30px;
}
/*支付流程*/
.infotion{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 99999999;
    display: none;
}
.infclose{
    right: 20px !important;
}
.infotionck{
    width: 480px;
    height: auto;
    background: #FFFFFF;
    position: relative;
    border-radius: 5px;
    padding: 0 40px;
    margin: 0 auto;
    margin-top: 8%;
    overflow: hidden;
}
.infotionck .proces {
    width: 418px;
    height: 80px;
    margin: 40px auto 0 auto;
}
.infotionck .proces li{
    width: 90px;
    height: 80px;
    margin-right: 22px;
    float: left;
    line-height: 80px;
    font-size: 14px;
    text-indent: 0.9em;
}
.pro{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/proces.png) no-repeat;
    background-size: 100% 100%;
    color: #333333;
}
.pro1{
    background: url(https://static.fangxiaoer.com/web/images/sy/fill/proces1.png) no-repeat !important;
    background-size: 100% 100% !important;
    color: #ff6100 !important;
}
.infotionck .proces li:nth-child(4){
    width: 80px;
    height: 80px;
    margin-right: 0;
    border-radius: 50%;
}
.proo{
    border: 1px solid #3d3d3d;
    color: #333333;
}
.proo1{
    border: 1px solid #ff5200 !important;
    color: #ff5200 !important;
}
.inputbor{
    width: 408px;
    height: 42px;
    border: 1px solid #e6e6e6;
    margin: 0 auto;
    margin-bottom: 12px;
    border-radius: 3px;
}
.inputbor p{
    width: 96px;
    line-height: 42px;
    padding-left: 12px;
    float: left;
    font-size: 14px;
    color: #333333;
}
.inputbor input{
    width: 295px;
    outline:none;
    float: left;
    height: 42px;
    border: none;
    font-size: 14px;
}
.inputbor b{
    display: block;
    width: 295px;
    height: 42px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    float: left;
    line-height: 42px;
}
.inputbor p i{
    color: #e60012 !important;
}
.inputbor p span{
    margin-left:33px;
}
.Vertion{
    width: 410px;
    height: 42px;
    border-radius: 3px;
    background: #ff5200;
    line-height: 42px;
    text-align: center;
    font-size: 14px;
    color: #FFFFFF;
    margin: 0 auto;
    margin-top: 22px;
    font-weight: bold;
    margin-bottom: 46px;
    cursor: pointer;
}
.tx{
    margin-top: 22px;
    overflow: hidden;
}
.yzm{
    width: 410px;
    overflow: hidden;
    margin:0 auto;
    margin-top: 42px;
    display: none;
}
.yzm h1{
    font-weight: bold;
    font-size: 12px;
    color: #000000;
}
.tbor{
    width: 408px;
    height: 42px;
    border: 1px solid #e6e6e6;
    margin: 0 auto;
    margin-top: 26px;
    margin-bottom: 22px;
    border-radius: 3px;
}
.tbor input{
    width: 50%;
    height: 42px;
    text-indent: 1em;
    border: none;
    outline: none;
    font-size: 14px;
    float: left;
}
.tbor p{
    width: 106px;
    height: 20px;
    line-height: 20px;
    font-size: 14px;
    color: #ff5200;
    float: right;
    text-align: center;
    border-left: 1px solid #ededed;
    margin: 11px 0;
    cursor: pointer;
}
.yzmsion{
    width: 410px;
    height: 42px;
    border-radius: 3px;
    background: #ff5200;
    line-height: 42px;
    text-align: center;
    font-size: 14px;
    color: #FFFFFF;
    margin: 0 auto;
    font-weight: bold;
    margin-bottom: 46px;
    cursor: pointer;
}
.dingd{
    width: 480px;
    overflow: hidden;
    margin:0 auto;
    margin-top: 24px;
}
.detailed{
    width: 458px;
    height:auto;
    border: 1px solid #e6e6e6;
    padding: 16px 0 0 20px;
    overflow: hidden;
    margin-bottom: 10px;
    border-radius: 3px;
}
.detailed div{
    font-size: 15px;
    line-height: 14px;
    color: #999999;
    float: left;
    margin-bottom: 15px;
    text-align: left;
}
.detailed div span{
    color: #333333;
}
.detailed div b{
    font-weight: bold;
    color: #ff5200;
}
.detailed div s{
    color: #ff5200;
}
.paybutt{
    width: 410px;
    height: 42px;
    background: #ff5200;
    line-height: 42px;
    text-align: center;
    color: #ffffff;
    font-size: 15px;
    font-weight: bold;
    margin: 0 auto;
    border-radius: 2px;
    margin-bottom: 10px;
    cursor: pointer;
}
.ontdown{
    font-size: 12px;
    color: #333333;
    margin-bottom: 36px;
    text-align: center;
    line-height: 16px;
}
.ontdown span{
    color: #ff6100;
}
.xffs{
    overflow: hidden;
    margin-top: 18px;
}
.xffs .Cashier{
    border-bottom: 1px solid #e6e6e6;
    border-top: 1px solid #e6e6e6;
    padding: 20px 0;
    overflow: hidden;
    margin-bottom: 28px;
}
.xffs .Cashier h1{
    width: 100%;
    font-size: 20px;
    color: #333333;
    margin-bottom: 20px;
}
.xffs .Cashier h1 span{
    font-size: 14px;
    font-weight: 400;
    color: #333333;
}
.Cashier div{
    font-size: 14px;
    color: #999999;
    line-height: 14px;
}
.Cashier div span{
    color: #333333;
}
.xffs .shiert{
    margin-bottom: 33px;
}
.xffs .shiert div{
    text-align: center;
    font-size: 14px;
    color: #333333;
    line-height: 17px
}
.xffs .shiert div b{
    font-size: 20px;
    color: #ff5200;
}
.xffs .shiert p{
    text-align: center;
    font-size: 14px;
    color: #333333;
    line-height: 14px;
    margin-top: 16px;
}
.xffs .shiert p b{
    color: #ff5200;
}
.xffs .ewm{
    width: 362px;
    height: auto;
    margin: 0 auto;
    overflow: hidden;
    margin-bottom: 36px;
}
.xffs .ewm .zfewm{
    width: 160px;
    height:auto;
    float: left;
}
.xffs .ewm .wx{
    width: 118px;
    height: 118px;
    border: 1px solid #48b31d;
    padding: 20px;
    margin-bottom: 14px;
}
.xffs .ewm .wx img{
    width: 120px;
    height: 120px;
}
.xffs .ewm .zfb{
    width: 118px;
    height: 118px;
    border: 1px solid #0baaed;
    padding: 20px;
    margin-bottom: 14px;
}
.xffs .ewm .zfb img{
    width: 120px;
    height: 120px;
}
.xffs .ewm div p{
    width:100%;
    height: 19px;
    line-height: 19px;
    font-size: 14px;
    text-align: center;
}
.xffs .ewm div p i{
    width: 20px;
    height: 19px;
    margin: 0 8px;
}
.qs{
    width: 450px;
    overflow: hidden;
    margin:0 auto;
    margin-top: 20px;
}
.qs .sign{
    width: 100%;
    height: auto;
    border-top: 1px solid #e6e6e6;
}
.qs .sign .loget{
    width: 44px;
    height: 44px;
    margin: 0 auto;
    margin-top: 20px;
}
.qs .sign .loget img{
    width: 100%;
}
.qs .sign h1{
    width: 100%;
    font-size: 16px;
    color: #333333;
    text-align: center;
    margin: 12px 0 18px 0;
}
.qs .sign .jump{
    width: 160px;
    height: 160px;
    margin: 0 auto;
    margin-bottom: 6px;
}
.qs .sign .jump img{
    width: 100%;
    height: 100%;
}
.qs .sign p{
    font-size: 14px;
    color: #333333;
    margin-bottom: 26px;
    text-align: center;
}
.myorder{
    display: block;
    text-decoration:none;
    width: 410px;
    height: 42px;
    line-height: 42px;
    background: #ff5200;
    text-align: center;
    color: #FFFFFF;
    margin: 0 auto;
    font-weight: bold;
    font-size: 16px;
    border-radius: 2px;
    margin-bottom: 40px;
}
.myorder:hover{
    color: #ffffff;
}
.cg{
    width: 450px;
    overflow: hidden;
    margin:0 auto;
    margin-top: 20px;
}
.become{
    width: 100%;
    height: auto;
    border-top: 1px solid #e6e6e6;
}
.cg .become .loget{
    width: 44px;
    height: 44px;
    margin: 0 auto;
    margin-top: 20px;
}
.cg .become .loget img{
    width: 100%;
}
.cg .become h1{
    width: 100%;
    font-size: 16px;
    color: #333333;
    text-align: center;
    margin: 12px 0 12px 0;
}
.cg .become p{
    font-size: 14px;
    line-height: 14px;
    color: #333333;
    margin-bottom: 30px;
    text-align: center;
}
.cg .sorting{
    width: 320px;
    height: 42px;
    margin: 0 auto;
    margin-bottom: 40px;
}
.cg .sorting a{
    display: block;
    width: 150px;
    height: 42px;
    background: #ff5200;
    line-height: 42px;
    font-size: 15px;
    color: #FFFFFF;
    text-align: center;
    border-radius: 2px;
}
.thq2{
    display: none;
}