@charset "utf-8";
/*
制作人：张琪
修改时间：2018-6-13
用处：查房价页面专用
*/
#option li{/* height:60px; *//* line-height:60px; */border: none;padding: 11px 20px;}
.checkHR{
    width:  1170px;
    margin:  0 auto;
}
.checkTitle{
    padding: 20px;
    font-size:  22px;
    border-bottom:  1px solid #e6e6e6;
}
.areaFilter,.areaMap,.areaChartimg,.hotVillage,.rankingList{
    border:  1px solid #ebebeb;
    margin-top: 26px;
    overflow:  hidden;
    /* height: 400px; */
    margin-bottom: 20px;
}
#option{background:#f5f5f5;border:  none;padding-bottom: 1px;}
#option li.leibie{background:#fff;line-height: 35px;margin-top: -11px;}
@font-face {
    font-family: 'dinot-bold';   /*字体名称*//*/font/*/
    src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');       /*字体源文件*/
}

/*区域筛选*/
.areaFilter,.filterResult{
    padding:  34px 20px;
    background: URL(https://static.fangxiaoer.com/web/images/sy/sale/list/checkHR.jpg) no-repeat;
    background-size: 100% 100%;
}
.areaFilterL{
    float:  left;
    padding: 45px 0 0 55px;
    width: 585px;
    height: 295px;
    background: rgba(0, 0, 0, 0.72);
    color:  #fff;
}
.areaFilterL h1{
    font-size:  56px;
    font-weight:  bold;
    line-height:  56px;
}
.areaFilterL ul{
    overflow:  hidden;
    margin-top:  50px;
}
.areaFilterL ul li{
    float:  left;
    width: 270px;
}
.areaFilterL ul li h4{
    margin-bottom: 30px;
    font-size:  22px;
    font-weight:  bold;
}
.areaFilterL ul li b>p{
    font-size:  16px;
    display:  inline-block;
    color: #fff;
}
.areaFilterL ul li p+p{
    font-size:  14px;
    margin-top: 35px;
}
.areaFilterL ul li p b{
    font-size:  50px;
    font-weight:  bold;
    font-family: dinot-bold;
    color: #ff6100;
}
.areaFilterL ul li p i{color:#e60012}
.areaFilterL ul li p i p{color:#fff;float:left;margin-right:10px;font-size:  14px;font-weight:  normal;}
.areaFilterL ul li p a{
    color:  #ff6100;
    margin-right: 20px;
    padding:  0 2px;
}
.areaFilterR{
    background:  #fff;
    float:  left;
    padding:  28px 22px;
    width:  430px;
    height: 284px;
}
.areaFilterR>ul{position:relative}
.areaFilterR h4{
    font-size:  16px;
    font-weight:  bold;
    margin-bottom:  15px;
}
.areaFilterR .SubNameUl{
    position: absolute;
    z-index:  99;
    background:  #fff;
    width: 348px;
    top: 40px;
    left: 72px;
    border: 1px solid #e6e6e6;
    max-height:  180px;
    display:none;
}
.areaFilterR .SubNameUl li{margin: 0px;padding: 0px 5px;cursor: default;display: block;text-align:left;font: menu;line-height: 40px;overflow: hidden;background: #fff;font-size: 14px;}
.areaFilterR .SubNameUl li:hover{background:rgba(222, 222, 255, 0.6)}
.areaFilterR ul li{
    margin-bottom:  12px;
    position:  relative;
    overflow:  hidden;
}
.areaFilterR ul li label{
    font-size:  14px;
    color:  #333;
    font-weight:  bold;
    margin-right:  12px;
    width:  56px;
    display:  inline-block;
}
.areaFilterR ul li input{
    width: 342px;
    height:  40px;
    line-height:  40px;
    border:  none;
    background: #f5f5f6;
    padding-left:  8px;
}
.areaFilterR ul li i{
    position: absolute;
    right: 20px;
    top: 8px;
}
.areaFilterR ul li .w50{
    width:  50%;
    float:  left;
    position:  relative;
}
.areaFilterR ul li .w50 input{
    width: 126px;
    padding-left:  8px;
}
.areaFilterR ul li select{
    width: 137px;
    height: 40px;
    border:  none;
    background:  #f5f5f6;
}
.areaFilterR ul li option{}
.checkBtn{
    width:  292px;
    height:  40px;
    font-size:  14px;
    text-align:  center;
    display:  block;
    margin:  0 auto;
    background: #ff6100;
    line-height:  40px;
    color:  #fff;
    margin-top: 8px;
    text-decoration:  none;
}
.checkBtn:hover{color:#fff;text-decoration:  none;}
/*筛选结果小区*/
.filterResult{
    overflow:  hidden;
}
.filterResult .filterResultL{
    width: 690px;
    float:  left;
    background: rgba(0, 0, 0, 0.72);
    color:  #fff;
    /* padding: 45px 40px; */
    height: 261px;
    padding-top: 82px;
    padding-left: 45px;
    padding-bottom: 8px;
    padding-right: 35px;
}
.filterResult .filterResultL h1 span{
    font-size: 48px;
    font-weight: bold;
    line-height: 56px;
    display:  inline-block;
}
.filterResult .filterResultL h1 p{
    /*display:  inline-block;*/
    font-size: 18px;
    font-weight:  normal;
    /* margin-left: 25px; */
    margin-top: 15px;
}
.filterResult .filterResultL ul{
    overflow: hidden;
    margin: 52px 0 28px 0;
}
.filterResult .filterResultL ul li{
    float: left;
    width: 270px;
}
.filterResult .filterResultL ul li p+p{
    font-size: 16px;
    margin-top: 4px;
}
.filterResult .filterResultL ul li p b{
    font-size: 52px;
    font-weight: bold;
    font-family: dinot-bold;
    color: #ff6100;
    line-height:  50px;
}
.filterResult .filterResultL ul li p a{}
.filterResult .newOrder{}
.filterResult .newOrder p{
    font-size:  14px;
}
.filterResult .newOrder p+div+p{
    line-height:  40px;
    margin:  15px 0;
    color: #ff6100;
}
.filterResult .newOrder div{
    overflow:  hidden;
    margin: 15px  0;
    margin-right: 14px;
    float:  left;
}
.filterResult .newOrder div label{
    line-height:  40px;
    width:  70px;
    text-align: left;
    display:  inline-block;
    float:  left;
}
.filterResult .newOrder div input{
    width: 225px;
    padding-left: 10px;
    height: 40px;
    line-height:  40px;
    border: none;
    border-radius:  2px;
    float:  left;
}
.filterResult .newOrder div .newOrderBtn{
    width:  125px;
    display:  inline-block;
    background:  #ff6100;
    color:  #fff;
    text-align: center;
    padding-left: 0;
}
.filterResultR {
    background: rgba(0, 0, 0, 0.72);
    width: 240px;
    padding: 50px 50px 22px;
    float:  left;
    height: 279px;
}
.resultRIcon{
    display:  block;
    width:  240px;
    height:  50px;
    background:  #ff6100;
    color:  #fff;
    text-align:  center;
    line-height: 50px;
    margin-bottom:  50px;
}
.resultRIcon:hover{color: #fff;}
.resultRIcon1{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/resultRIcon.png) #ff6100 no-repeat 65px 16px;}
.resultRIcon2{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/resultRIcon.png) #ff6100 no-repeat;background-position: 65px -80px;}
.resultRIcon3{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/resultRIcon.png) #ff6100 no-repeat;background-position: 65px -178px;}

/*地图*/

/*房价走势图*/

/*热门小区*/
.hotVillage{
    width:  1170px;
    margin: 30px auto;
    height:  auto;
}
.hotVillage div:last-child ul{
    border-bottom: none;
}
.hotVillage div ul{
    padding: 20px;
    overflow:  hidden;
    border-bottom: 1px solid #e6e6e6 !important;
}
.hotVillage ul:last-child{border-bottom:none;}
.hotVillage ul{padding:20px;overflow: hidden;}
.hotVillage ul li{
    width: 23.6%;
    margin-right: 1.68%;
    float:  left;
    overflow:  hidden;
}
.hotVillage ul li>a{
    display:  block;
    height:  180px;
    overflow:  hidden;
}
.hotVillage ul li:last-child{margin-right:0}
.hotVillage ul li img{
    width:  100%;
}
.hotVillage ul li div{float: left;width: 200px;}
.hotVillage ul li div h4{
    margin: 15px 0 4px 0;
    width: 206px;
    text-overflow:  ellipsis;
    white-space:  pre;
    overflow:  hidden;
}
.hotVillage ul li div p{}
.hotVillage ul li>p{
    float:  right;
    font-size:  16px;
    font-weight:  bold;
    color:  #ff6100;
    margin-top:  15px;
}
.hotVillageTitle{
    overflow:  hidden;
    margin-bottom: 10px;
}
.hotVillageTitle p{
    float:  left;
    display:  inline-block;
    margin-right: 30px;
}
.hotVillageTitle p+p{
    margin-right: 15px;
}
.hotVillageTitle p a{
    color:  #ff6100;
    font-size: 20px;
    font-weight:  bold;
}
.ringRatioNumRed{color: #e91b2b;}
.seeMoreBtn{
    float:  right;
    color:  #333;
}

/*排行榜*/
.rankingList{
    color: black;
    padding-bottom: 30px;
    overflow:  hidden;
    width: 1170px;
    margin: 0 auto;
    margin-top: 30px;
    margin-bottom: 40px;
    height:  auto;
}
.rankingList .rankingListK{
    margin: 30px 170px 0 50px;
    float:  left;
    width:  450px;
    position:  relative;
}
.rankingList .rankingListK2{margin:30px 0 0 0
}
.rankingList h5{
    font-size:  18px;
    display:  inline-block;
}
.ranklistBtn{
    float:  right;
}
.rankingBtn{cursor: pointer;}
.rankingListMain{}
.rankingBtnG{display:none}
.rankingListMain li+li+li+li em{
    background:  #dedede;
}
.rankingListMain li{
    border-bottom:  1px dashed #e6e6e6;
    padding: 12px 0 2px 0;
}
.rankingListMain li:last-child{border-bottom:none}
.rankingListMain em{
    display: block;
    float:  left;
    width:  22px;
    height:  22px;
    text-align:  center;
    line-height:  22px;
    color:  #fff;
    background:  #ff6100;
    border-radius:  20px;
}
.rankingListMain li>div{
    overflow:  hidden;
    margin-bottom: 4px;
    margin-left:  35px;
    font-size:  14px;
    font-weight:  bold;
    color:  #333;
}
.rankingListMain .rankingLM1 p{
    display:  inline-block;
    font-weight:  bold;
}
.rankingListMain .rankingLM1 p a{color:#333}
.rankingListMain .rankingLM1 p a:hover{color:#ff5200}
.rankingListMain .rankingLM1 p+p{
    float:  right;
}
.rankingListMain .rankingLM1 p+p b{
    color: #333;
}
.rankingListMain .rankingLM2{
    margin-bottom:  5px;
    font-weight:  normal;
}
.rankingListMain .rankingLM2 p{
    display:  inline-block;
}
.rankingListMain .rankingLM2 p+p{
    float:  right;
}
.rankingListMain .rankingLM2 p+p i{}
.red{
    color:  #e60012 !important;
    font-weight:  bolder;
    font-size:  16px;
    margin-right: 5px;
}
.green{
    color: #22ac38 !important;
    font-weight:  bolder;
    font-size:  16px;
    margin-right: 5px;
}
.rankingListSeeMore{
    background: #f5f5f5;
    height: 50px;
    display: block;
    width:  580px;
    float:  left;
    margin-left:  295px;
    text-align:  center;
    line-height:  50px;
    color:  #666;
    margin-top:  20px;
    cursor:  pointer;
}
.rankingListMainUp,.rankingListMainDown{

    position:  absolute;

    width:  450px;
}
/*点击精准评估后的弹窗*/
.kpTc{
    width: 500px;
    height: 380px;
    position: fixed;
    left: 50%;
    top: 50%;
    margin-left: -250px;
    margin-top: -200px;
    z-index: 99999;
}
.kpTc>div{background: #fff;width: 490px;height: 280px;padding: 5px;border-radius: 8px;}
.fxe-alert {
    position: fixed;
    top: 48%;
    width: 70%;
    margin: 0 15%;
    background: url("https://static.fangxiaoer.com/global/imgs/ico/b60.png");
    color: #fff;
    font-size: 11pt;
    text-align: center;
    border-radius: 6px;
    padding: 10px;
    z-index: 1000000;
}

.kpTc .kpTc_icon{
    width: 100%;
    height: 140px;
    border-bottom: 1px solid #eaeaea99;
    overflow: hidden;
    margin-top: 15px;
}
.kpTc .kpTc_icon img{
    width: 100%;
}
.kpTc .kpTc_form{
    margin-top: 30px;
    padding: 0 36px;
    position:  relative;
}
.kpTc .kpTc_form>div{
    font-size: 12px;
    color: #333;
    position: relative;
}
.kpTc .kpTc_form section{padding-bottom:  5px;}
.kpTc .kpTc_form>div input{
    border:  none;
    width: 70px;
}
.kpTc .kpTc_form>div+div input{
    width: 410px;
    height: 42px;
    border: 1px solid #bfbebe;
    margin: 20px 0 0 0;
    padding-left: 5px;
}
.kpTc .kpTc_form div span{}
.kpTc .kpTc_form div #code{
    border: 1px solid #e5e5e5;
    margin: 30px 0;
    width: 95%;
    line-height: 32px;
    padding-left: 5%;
}
.kpTc .kpTc_form div #phone{
    margin: 0 5px;
    border: none;
    width: 100px;
}
.kpTc .kpTc_form div b{
    position:  absolute;
    top: -25px;
    right: 2px;
    color: #a8a8a8;
    font-weight: 400;
    border: #ededed 1px solid;
    padding: 1px 10px;
    cursor: pointer;
}
.kpTc .kpTc_form div b.fxe_validateCode{
    display:none;
}
.kpTc .kpTc_form div .fxe_ReSendValidateCoad{
    position: absolute;
    top: -24px;
    right: 0;
    border: 1px solid #e8e8e8;
    padding: 0 5px;
    cursor: pointer;
}
.kpTc .kpTc_btn{}
.kpTc .kpTc_btn{

}
.kpTc .kpTc_btn a{
    width: 100%;
    text-align: center;
    background: #ff6100;
    line-height: 40px;
    color: #fff !important;
    display:block;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    text-decoration: none;
    margin-bottom: 10px;
    border: 1px solid #ebebeb;
}
.kpTc .kpTc_close{text-align: center;cursor: pointer;width: 20px;height: 20px;background: none;position: absolute;right: 8px;top: 8px;}
.kpTc .kpTc_close img{}
.kpTc_heibu,.kfzc_heibu{width: 100%; height:100%;background: rgba(0, 0, 0, 0.5);height: -webkit-fill-available;background-size:  cover;background-position:  center;position:  fixed;top: 0;left:  0;z-index: 99999;display:none;}
.kpTc .kbTc_txt1{float: left;color: #ff6100;font-size: 14px;}
.kpTc .kbTc_txt2{float: right;color: #0068b7;font-size: 14px;cursor: pointer;}
.yydk_heibu{width: 100%;background: rgba(0, 0, 0, 0.3);height: -webkit-fill-available;background-size:  cover;background-position:  center;position:  fixed;top: 0;left:  0;z-index: 999999;}

.yyDk{
    width: 420px;
    height: 250px;
    position: fixed;
    left: 50%;
    top: 50%;
    margin-left: -210px;
    margin-top: -125px;
    z-index: 999999;
}
.yyDk>div{
    background: #fff;
    width: 420px;
    height: 224px;
    border-radius: 4px;
    /* padding: 45px 80px 0 80px; */
}
.yyDk>div .yyDkImg{
    text-align: center;
    padding-top: 42px;
}
.yyDk>div .yyDkImg>img{}
.yyDk>div p{
    text-align: center;
    margin-top: 15px;
    color: #ff6100;
    font-size: 14px;
}
.yyDk>div p+p{
    margin-top: 20px;
    color: #333;
    font-size: 13px;
}
.yyDk .yyDk_close{
    background: none;
    text-align: center;
    padding: 0;
    width: 20px;
    height: 20px;
    position: absolute;
    right: 16px;
    top: 16px;
}
.yyDk .yyDk_close img{
    cursor: pointer;
    width: 20px;
    height: 20px;
}
.kfzc_heibu .kfzc{
    width: 320px;
    background: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -210px;
    padding: 40px 50px;
    border-radius: 10px;
    margin-top: -113px;
}
.kfzc_heibu .kfzc h1{
    font-size: 15px;
    font-weight: 400;
    line-height: 26px;
}
.kfzc_heibu .kfzc input{
    display: block;
    border: 1px solid #ededed;
    width: 308px;
    line-height: 42px;
    padding-left: 10px;
    margin-top: 34px;
    margin-bottom: 4px;
    font-size: 14px;
    border-radius: 4px;
}
.errorText{
    height: 24px!important;
    line-height: 24px!important;
    padding-bottom: 3px!important;
    color: #de0000!important;
}
.kfzc_heibu .kfzc a{
    display: block;
    background: #ff6100;
    color: #fff;
    cursor: pointer;
    text-align: center;
    line-height: 42px;
    font-size: 14px;
    border-radius: 4px;
    width: 320px;
    font-weight: bold;
    margin-bottom:  4px;
}
.kfzc_heibu .kfzc img{position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;}
#instantlyAsk{
    margin-top:26px;
    margin-bottom:22px;
    width: 315px;
    height: 120px;
    border-radius: 4px;
    resize: none;
    border: 1px solid #e0e0e0;
}
.housesRight .salesOffice span b b {
    font-size: 14px;
    padding: 0 2px;
}

/*环比样式*/
.trendCharContent{
    position: relative;
}
.trendCharContent .vs{
    color:  #bbb;
    font-size: 28px;
    width: 50px;
    line-height: 46px;
    height: 50px;
    border: 1px solid #ebebeb;
    border-radius: 100px;
    text-align:  center;
    position: absolute;
    background: #fff;
    top: 19px;
    left: 375px;
    font-weight:  normal;
}
.trendCharContent dl{
    overflow: hidden;
    /* border: 1px solid #ebebeb; */
    margin: 24px 16px 0 16px;
    background: #f5f5f5;
}
.trendCharContent dl dt,.trendCharContent dl dd{
    width: 30%;
    float: left;
    padding: 10px 0 10px 10px;
    box-sizing: border-box;
    padding-left: 55px;
}
.trendCharContent dl dd{
    background: #f5f5f5;
}
.trendCharContent dl dt{
    border-right:  1px solid #ebebeb;
    margin-right: 3%;
    background: #f5f5f5;
    width: 34%;
    padding-left: 40px;
}
.trendCharContent dl .trendChartTitle{
    font-size: 14px;
    padding-top: 4px;
}
.trendCharContent dl .trendChartTitle i{
    display: inline-block;
    width: 10px;
    height: 10px;
    background: #fe7175;
    border-radius: 10px;
    margin-right: 10px;
}
.trendCharContent dl .trendChartPrice{
    font-size: 18px;
    line-height: 36px;
}
.trendCharContent dl .trendChartPrice b{
    font-weight:  400;
}
.trendCharContent dl .trendChartPrice span{
    font-size: 12px;
    color: #ff6100;
    padding-left: 4px;
}
.trendCharContent dl .trendChartPrice span.dis{
    color: #5fa807;
}
.trendCharTime{
    border: 1px solid #ededed;
    overflow: hidden;
    float: right;
    margin-right: 18px;
    margin-bottom: 16px;
}
.trendCharMap{
    width: 870px;
}
.infuse{
    font-size: 12px;
    color: #999999;
    padding-left: 20px;
    margin-bottom: 16px;
}
.trendCharTime li{
    color: #666;
    font-size: 12px;
    float: left;
    width: 50px;
    text-align: center;
    cursor: pointer;
}
.Time li.hover{
    background:#a09f9f;
    color:#fff;
}
.bnzfTC_heibu{ width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); position: fixed; left: 0; top: 0; z-index: 999}
.bnzfTC{ height: 100%; position:relative}
.bnzfTC_box{ width: 500px; height: 300px; position: absolute; left: 50%;top: 50%; margin-left: -250px; margin-top: -150px;background-color: #fff; position: relative; border-radius: 7px}
.bnzfTC_box .Verification{font-size: 14px;color: #333333;width: 410px;margin: 0 auto;position:  relative;margin-bottom: 22px;}
.bnzfTCSendCode {font-size: 12px;color: #999999;width: 124px;height: 30px;border: 1px solid #ebebeb;display:inline-block;line-height: 30px;text-align: center;float: right;border-radius: 5px;cursor:  pointer;}
.Verification{ padding-top: 49px;}
.Verification input{ width: 78px; font-size: 12px; border: 0}
#bnzfTCCode{width:402px;display: block;margin: 0 auto;margin-top: 10px;height: 42px;padding-left: 8px;border: 1px solid #ebebeb;border-radius: 5px;font-size: 14px;color: #999;}
#bnzfTCCode::-webkit-input-placeholder{
    color:#999999;
}
input::-moz-placeholder{   /* Mozilla Firefox 19+ */
    color:#999999;
}
input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
    color:#999999;
}
input:-ms-input-placeholder{  /* Internet Explorer 10-11 */
    color:#999999;
}
.bnzfTC_btn{width: 412px; height: 42px; margin: 0 auto;background-color: #ff6100; border-radius: 5px; margin-top: 12px}
.bnzfTC_btn a{ line-height: 42px; display: block; text-align: center; font-size: 14px; color: #ffffff;}
.phone_sun{width: 412px;margin: 0 auto;margin-top: 6px;}
.kbTc_txt1{display: block;float: left; color: #ff6100}
.kbTc_txt2{display: block;float: right;color: #2a95fe;cursor:  pointer;}
.bnzfTC_close{position: absolute;top:20px;right: 16px;cursor:  pointer;}
.Prompt{width:412px; margin:0 auto;height:30px}
.Prompt span{font-size:12px; color:#e60012;line-height:30px}
.help_search_house_error{display: inline-block;color: red;text-align: -webkit-auto;float:  left;}
.bnzfTCss_heibu{ width: 100%; height: 100%; background-color: rgba(0,0,0,0.7);fixed; left: 0; top: 0; z-index: 999;position: fixed; left: 0; top: 0}
.bd{    background: url(https://static.fangxiaoer.com/web/images/ico/sign/select.png)no-repeat;
    background-size: 100% 100%;}
.bnzfTC_box .Agreement i{ display: block;width: 20px;
    height: 20px;
    border: 1px solid #ededed;
    display: block;
    cursor: pointer;
    float: left;
    margin-left: 43px; margin-right: 7px}
.bnzfTC_box .Agreement input{ display: none}
.bnzfTC_box .Agreement h1{ font-size: 14px; color: #333333;font-weight: normal;}
.bnzfTC_box .Agreement h1 a{color: #ff6100}
.bnzfTC_box .Agreement h1 a:hover{text-decoration:underline;}

.bnzfTCss{width: 420px;height: 224px; background-color: #fff;position: absolute; left: 50%; top: 50%; margin-left: -210px; margin-top: -112px; border-radius: 7px}
.bnzfTCss_close img{display:none;}
.bnzfTCss .yyDkImg img{ margin: 0 auto; display: block; padding-top: 49px;}
.bnzfTCss p{ font-size: 14px; color: #666666; text-align: center; margin-top: 40px}
.bnzfTCss #guide_result{font-size: 16px; color: #ff6100; margin-top: 14px;}

.showPlat b, .showRegion b {
    font-size: 14px !important;
    font-weight: bold !important;
}
#map,#map #baiduMap{width: 1170px !important;height: 400px !important;overflow: hidden}
.showHouse div, .showsubWay div {
    background: url(https://static.fangxiaoer.com/web/images/ico/map/showHouseIcon.png) no-repeat !important;
    position: absolute;
    top: 50px !important;
    left: 50% !important;
    margin-left: -7px;
    width: 13px !important;
}
#areaHighCharts{margin-top: 30px !important;}

.areaMap .showPlat,.areaMap .showRegion {
    width: 96px !important;
    padding-top: 7px;
    height: 92px !important;
    display: block;
    background: rgba(255, 97, 0, 0.92) !important;
    color: #fff;
    text-align: center;
    border-radius: 100%;
    box-shadow: 3px 2px 14px rgba(255, 97, 0, 0.35);
    border:  none;
}
.areaMap .showPlat span,.areaMap .showRegion span {
    width: 86px !important;
    margin-left: 0 !important;
    white-space: pre-line;
    padding: 11px 5px 0 5px;
    overflow: hidden;
    white-space: pre;
    text-overflow: ellipsis;
    margin-bottom: 2px;
    text-shadow: none;
}
.fxe_validateCode{
    position: absolute;
    right: 25px;
    top: 53px;
    color:  #999;
    background:  #fff;
}
.Agreement p a{color:#ff5200}
.showHouse {
    display: block;
    padding: 5px 10px !important;
    height: 40px;
    box-shadow: 0px 1px 10px rgb(254, 108, 18) !important;
    background: rgba(255, 97, 0, 0.92) !important;
}
#btnRegion{font-weight:bold}
.listIcon{top:11px !important}
.filPrice{
    margin-top: 56px;
}
.filPrice span{
    font-size: 14px;
    color: #ffffff;
    font-family: dinot-bold;
    margin-right: 195px;
}
.filPrice i{
    font-size: 52px;
    color: #ff6100;
    font-weight: bold;
}