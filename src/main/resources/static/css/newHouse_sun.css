@charset "utf-8";
/* CSS Document */
.crumbs{margin-top:39px;padding-top: 0;}
.w1210 {width:1210px;margin:0 auto}
.w1210 .w{width:1170px}
#left{width: 898px;float:left;margin-right:20px;_display:inline;/* border: 1px solid #eee; */margin-top: 16px;margin-bottom: 20px;border-right:  0;border-left: 0;}
#right{width:250px;float:left;}
.mt30{margin-top:30px;}
.title{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/title_line.gif) repeat-x bottom;height:44px;margin:0px auto;margin-top: 4px !important;display: none;}
.title p{margin-right:30px;font-size:18px; font-family:"微软雅黑"; color:#999; float:left;width:112px;text-align:center;line-height:40px;}
.title p.hover{border-bottom:4px solid #ff5200}
.title p a{ text-decoration:none}
.title p.hover a{color:#ff5200;}
/*搜索*/
.search{line-height:12px;font-size: 14px;}
.search li{ clear:both; height: 40px; line-height: 40px; border-bottom:1px dashed #ededed;}
.search li p{/*width:60px;*/float:left; color:#999}
.search li a{float:left;padding:0 14px;border-left:1px solid #dfdfdf; line-height:14px; height:14px;margin-top: 13px;}
.search li :nth-child(2){border-left:none;}
.search li a.hover{color:#ff6600}
.search li span{background:#f4f4f6; clear:both;margin:10px 0 20px;float:left;margin-left:60px;padding:8px 0; width:880px;}
.search li span a{line-height:30px; border:none; height:30px;}
/*房源*/
.lp_count{font-size:14px;/* border-bottom:1px solid #ededed; *//* line-height: 30px; */padding-top: 10px;padding-bottom: 15px;}
.lp_count span{color:#ff6600;padding:0 5px;font-size: 14px;}
.lp_count {height:45px;padding:0;border-left: 1px solid #ededed;border-right: 1px solid #ededed;}
.lp_con {float:left;line-height:45px;padding-left: 16px;font-size: 12px;color: #999;}
#mapTab {float:right;margin-top: 8px;background:#fff;}
#mapTab li{width: 72px;height: 30px;border: 1px #e8e8e8 solid;float:left;line-height: 30px;cursor:pointer;font-size: 14px;}
#mapTab li.hover {color:#ff5200;cursor: text;}
#mapTab li#tabMap {border-right:none;}
#mapTab li i{background:url("https://static.fangxiaoer.com/web/images/ico/map/mapTab.png") no-repeat;display:block;width:20px;height:18px;float:left;margin: 7px 5px 0 10px;}
#mapTab li#tabList.hover i {background-position:0px -50px;}
#mapTab li#tabList i {background-position:0px -73px;}
#mapTab li#tabMap.hover i {background-position:0px -24px;}
.house{height:278px;border-bottom:1px solid #ededed;padding: 15px 0; font-size:14px; position:relative;}
.house em{color:#ff3333;font-size:18px;}
.house .img{width:375px;height:278px; position:relative; float:left;}
.house .img img{width:375px;height:278px;}
.house .map{width:205px;height:35px;position:absolute;top:6px;left:-5px;}
.house .map img{width:205px;height:35px;}
.house .info{float:left; margin-left:30px;width: 495px; position:relative;height:278px;}
.house .info_tit{float:left; color:#1f80d9; font-size:24px; font-family:"微软雅黑";margin: 5px 0 15px; text-decoration:none}
.house .info_tit.qjd{padding-right: 41px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/vipqjd.gif) no-repeat right -3px;}
.house a.info_tit:visited{color: #1E70A8;}
.house a.info_tit:hover{color:#ff5200;}
.house .market{padding: 2px 10px;margin-left: 10px;margin-top: 8px;float: left;}
.house .tt1{border:1px solid #f03b44;color:#f03b44}
.house .tt2{border:1px solid #f08c00;color:#f08c00}
.house .tt3{border:1px solid #dd8a98;color:#dd8a98}
.house .tt4{border:1px solid #a078c8;color:#a078c8}
.house .tt5{border:1px solid #a6bc37;color:#a6bc37}
.house .tt6{border:1px solid #6C5FFF;color:#6C5FFF}
.house .info_tit:hover{color:#069}
.house .info s{float:left;width:40px;}
.house .dizhi{line-height: 50px;/* height:36px; */}
.price{height:68px; margin:0px 0 15px;}
.price p{width:50px;float:left;font-size:14px;line-height:42px;}
.price ul{float:left;width: 440px;}
.price li{float:left;width:200px; line-height:34px; font-size:14px;padding:0 10px;}
.price li img{ vertical-align: bottom;margin-bottom:8px;margin-left:8px;}
.price i{font-size:28px; font-family:Arial; color:#ff6600;}
.price em{font-size:28px;color:#ff6600;}
.house .btn{float:left;width:145px;}
.button{color:#FFF;width:135px;height:40px;line-height:40px; border-radius:3px; background:#ff6600; font-size:18px; }
.button:hover{color:#FFF;}
.tel{color:#ff6600;	padding-left: 50px;font-family: Arial;font-weight: bold;font-size: 20px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/default_tel.gif) no-repeat 2px 12px;height:69px;line-height: 19px; position:absolute;bottom: -17px;}
.tel i{	font-size:12px;color:#a6a6a6;font-weight:normal;}
.time{height:20px; line-height:20px;margin:10px 0 0;}
.house .time p{float:left;}
.time em{width:18px; color:#666;font-size:16px; font-family:Arial;margin:0 2px 0 4px}
.hui{background:#999}
.youhui{width:144px;height:51px; position:absolute;right:90px;bottom:0;

    /*background: url(https://static.fangxiaoer.com/web/images/sy/house/house/ljzf_icon.png) -35px -73px no-repeat;抢优惠*/
    background:url(https://static.fangxiaoer.com/web/images/sy/house/czlb.jpg) no-repeat;/*抢礼包*/
}
.youhui span{color:#f7f2be;font-size:18px;margin-left: 49px;line-height:50px;display: block;width: 92px;text-align: center;display: none;}
/*右*/
/*私人定制*/
#right h1{font-size:18px;font-weight:normal;/* width:210px; */margin:0 auto;border-bottom:1px solid #ededed;padding: 0px 0 10px;text-align:left;}
#right h1.bidu{margin-top: 20px;}
.srdz{border:1px solid #ededed;}
.rmph{margin-top: 22px;}
.rmph li{width:210px;margin:-1px auto 0; border-top:1px dashed #ededed;padding:15px 0; clear: both; line-height:24px;font-size: 14px;}
.rmph s{width:20px;height:20px; line-height:20px; text-align:center; font-size:16px; color:#FFF; float:left; background:#ff6600}
.rmph p{float:left; width:170px; margin-left:13px; margin-bottom:5px;color: #999;}
.rmph i{ color:#ff6600;}
.zxhd{ text-align:center; padding-bottom:20px;}
.zxhd img{margin-top:20px}

/*顶部广告*/
.yx-rotation-focus,.yx-rotation-t,.yx-rotaion-btn{position:absolute}
a.yx-rotation-t{color:#fff;font-size:12px;z-index:2;bottom:10px;left:0px;line-height:40px; text-align:center; width:210px;}
.yx-rotation-focus span,.yx-rotaion-btn span{background:url(https://static.fangxiaoer.com/web/images/sy/default/top_flash.png) no-repeat;display:block;}
.yx-rotation-focus{width:21px;height:101px;line-height:21px;right:10px;top:0;z-index:2}
.yx-rotation-focus span{width:21px;height:21px;line-height:20px;float:left;margin-left:5px;position:relative;top:5px;cursor:pointer;color:#fff; text-align:center; background-position:-29px 0;margin-bottom:6px;}
.yx-rotation-focus span.hover{color:#fff;background-position:0px 0px;}
.yx-rotaion img{width:210px; height:154px;}
.yx-rotation-title{ background:#000; opacity:0.7; position:absolute; top:132px;left:0;width:210px;height:24px;}

/*新房直达*/
.zhida{height:124px;border-top:1px solid #ebebeb; border-bottom:1px solid #f2f2f2; padding-top:30px; font-size:14px; margin-top:110px}
.zhida p{width:77px;margin-left:35px; font-size:18px; font-family:"微软雅黑"; float:left;height:50px}
.zhida a{margin-left:14px; line-height:25px;}

/*无房源*/
.warning{margin: 20px 0;padding: 20px 0 20px 235px;background: #fffbf6;border: 1px solid #f5dcbc;}
.warning p{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat left; font-size:14px; line-height:24px; font-weight:bold;color:#333;padding-left:60px;}
.warning a{color:#0085ff}
.warning a:hover{color:#900}
.cnxh{width:210px; margin: 10px auto 20px auto !important;  font-family:"微软雅黑"}
.cnxh_img{width:210px; margin-left:auto;margin-right:auto; position:relative;height: 158px;overflow: hidden;}
.cnxh img{width:100%; height: 158px;}
.cnxh_pos{ position:absolute; bottom:0;left:0;}
.cnxh_bg{ width:100%; height:28px; background-color:#000;filter:alpha(opacity=50); /* IE */ -moz-opacity:0.5; /* Moz + FF */opacity: 0.5; }
.cnxh_title{ width:205px; line-height:28px; color:#fff; font-size:14px; padding-left:5px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
.cnxh_m{ margin-top:6px; margin-bottom:6px;}
.cnxh_qian_icon{color:#ff5200; font-size:16px;}
.cnxh_price{font-size:24px; color:#ff5200;}
.cnxh_quyu{ float:right; padding-top:12px; }
.cnxh_title:hover{color:#fff;}

.contentTop{height:43px;line-height: 43px;border-bottom:1px #ff5200 solid;margin-top: 12px; text-align:center}
.contentTop a{float:left;display:block;width:70px;height:44px;line-height:43px;text-decoration:none;font-size: 13px; }
.contentTop a.hover{width:68px;height:42px;line-height:41px;color:#ff5200;border-top:1px #ff5200 solid;border-right:1px #ff5200 solid; border-left:1px #ff5200 solid;border-bottom:1px #fff solid;z-index: 50}

.right-text{padding-top: 5px;width:250px;padding-left:20px;}
.right-text .title{border-bottom:1px solid  #ededed !important;margin-bottom: 4px;display:block!important;font-size: 18px;color: #333;height: 30px;background:none;padding-left: 5px;}
.hot li{font-size: 14px;line-height: 14px;color: #666;background:url(https://static.fangxiaoer.com/web/images/ico/sign/news_d.gif) no-repeat 0 17px;padding-left:15px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.hot li.news_list_tit{font-weight:bold;font-size: 14px;background:none;}



.targer{ display:inline-block;height:35px; /* margin-top: 34px; */ clear:both;border:1px solid #eb6877;}
.targer .tar_left{width:40px;height:30px;padding-top:5px;float:left;background-color:#eb6877;}
.targer .tar_left span{width:25px;height:25px;display:block;margin:0 auto;background: url(https://static.fangxiaoer.com/web/images/sy/house/house/ljzf_icon.png) 0 -159px no-repeat;}
.targer .tar_right{margin:0 15px;float:left;line-height:35px;color:#eb6877;font-size:16px;}
.lowpay .tar_left span{background: url(https://static.fangxiaoer.com/web/images/sy/house/house/ljzf_icon.png) 0 -32px no-repeat;}
.lowpay .tar_left {background-color: #8a77ff;}
.lowpay {border: 1px solid #8a77ff;}
.lowpay .tar_right{color: #8a77ff;}
.exits .tar_left span{background:url(https://static.fangxiaoer.com/web/images/sy/house/house/ljzf_icon.png) 0 -58px no-repeat;}
.exits .tar_left {background-color: #8cb43c;}
.exits {border: 1px solid #8cb43c;}
.exits .tar_right{color: #8cb43c;}
.subway .tar_left span{background:url(https://static.fangxiaoer.com/web/images/sy/house/house/ljzf_icon.png) 0 -109px no-repeat;}
.subway .tar_left {background-color: #eb6877;}
.subway {border: 1px solid #eb6877;}
.subway .tar_right{color: #eb6877;}
.tel1{margin-top:10px;}
.pinpaizt{display:inline;border:1px solid #84b7f1;line-height: 45px;padding: 3px 10px;margin-left: 10px;color:#84b7f1}
.libao a:hover {color: #fff;}
.libao a{color:#fff}
.libao {width: 204px;height: 42px;position: absolute;right: 30px;bottom: 0;text-decoration: none;color: #fff;}
.libao div {background: url(https://static.fangxiaoer.com/web/images/sy/house/libao.png) no-repeat;width: 81px;line-height: 36px; float: left;text-align: center;}
.libao b {font-weight: 400;line-height: 36px;width: 81px;background: url(https://static.fangxiaoer.com/web/images/sy/house/di.png) no-repeat; text-align: center;display: block;float: left;margin-left: 10px;}
/*右侧广告位*/
.rmph2{margin-top:38px;}
.rmph2 .dsf_zt{width:250px;height:175px; display:block;padding-top:25px;margin-bottom:20px;background-color:#f0f0f0;}
.rmph2 .dsf_zt:hover{ text-decoration:none;}
.rmph2 .zt_img{width: 136px;height: 134px;margin: 0 auto 9px;background: url(https://static.fangxiaoer.com/web/images/sy/house/house/ljzf_icon.png) -43px -694px no-repeat;}
.rmph2 .zt_img1{ background-position:-105px 0;}
.rmph2 .zt_title{font-size:18px;color:#333;line-height:18px;width: 100%;text-align: center;}
.rmph2 .zt_con{margin:15px 0 0 25px;font-size:14px;color:#999;}

/*楼盘活动*/
.lou_img{width: 212px;position:relative;float:left;margin-left:18px;margin-bottom: 18px;margin-top: 18px;height: 163px;}
.lou img{width:100%; height:154px;}
.lou_img img{width: 212px;}
.lou_img a{color:#fff;}
.lou_pos{ position:absolute; bottom:0;left:0;}
.lou_bg{width:100%; height:28px; background-color:#000;filter:alpha(opacity=50); /* IE */ -moz-opacity:0.5; /* Moz + FF */opacity: 0.5;}
.lou_title{width:170px;line-height:28px;color:#fff;font-size:14px;/* padding-left:5px; *//* overflow:hidden; */white-space:nowrap;/* text-overflow:ellipsis; */}


.right_ico{position:fixed;right:0;top: 50%;width: 31px;z-index: 10;}
.right_ico a{margin-bottom:2px;cursor:pointer;border-radius:3px;background: #c2c2c2;display: block;position:relative;width:31px;height:31px;float: right;overflow: visible !important;}
.right_ico a span{display:none;}
.right_ico a i{width: 31px;height:31px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/right_ico.png) no-repeat;position:absolute;right: 0;}
.right_ico a.n1 i{background-position: 4px 5px;}
.right_ico a.n2 i{background-position: 5px -27px;}
.right_ico a.n3 i{background-position: 5px -59px;}
.right_ico a.n4 i{background-position: 4px -91px;}
.right_ico a.n5 i{background-position: 4px -187px;}
.right_ico a.n6 i{background-position: 4px -123px;}
.right_ico a.n7 i{background-position: 5px -156px;}
.right_ico a.n8 i{background-position: 4px -223px;}
.right_ico a:hover{background:#ff5520;}
.right_ico a.n6{display:none}
.right_ico a.n7{display:none}
.right_ico .n9{position:absolute;top:0;right:33px;display:none;}
.right_ico .n9 .sj {
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/right_ico.png) no-repeat right center;position:absolute;background-position: 4px -256px;width:22px;height:32px;position:absolute;right: 0px;top: 66px;}
.right_ico .n9 .bnzf{background:#fff;width: 227px;padding-top: 10px;margin-top: -76px;border:2px solid #ff5200;float:left;margin-right:8px;}
.right_ico .n9 .bnzf .fxe_bnzf_xl {display: none;line-height: 23px;position: absolute;background: #fff;width: 142px;top: 26px;margin-left: -1px;border: 1px solid #ccc;z-index: 10000;}
.right_ico .n9 .bnzf .fxe_bnzf_xl>li{line-height: 31px;}
.right_ico .n9 .bnzf .fxe_bnzf_xl>li:hover{background:#3399ff;color:#fff;}
.right_ico .n9 .bnzf>ul>li {margin-bottom: 4px;height: 28px;position: relative;}
.right_ico .n9 .bnzf h1 {background: url(https://static.fangxiaoer.com/web/images/ico/sign/right_ico.png) no-repeat right center;background-position: 186px -288px;font-weight: 400;color: #666;margin: 0 10px;border-bottom: 1px solid #ddd;padding-bottom: 8px;padding-left: 6px;line-height: 31px;margin-bottom: 14px;cursor: pointer;}
.right_ico .n9 .bnzf .fxe_bnzf_bt {color: #666;display: inline-block;float: left;width: 64px;line-height: 22px;margin-left: 10px;}
.right_ico .n9 .bnzf #fxe_bnzf_phone {line-height: 24px;padding-left: 4px;width: 136px;}
.right_ico .n9 .bnzf .fxe_yzm_btn {text-align: center;line-height: 24px;border-radius: 2px;display: inline-block;width: 86px;cursor: pointer;}
.right_ico .n9 .bnzf textarea {width: 142px;height: 65px;resize: none;}
.right_ico .n9 .bnzf #fxe_bnzf_yzm {width: 46px;height: 22px;margin-bottom: 20px;}
.right_ico .n9 .bnzf .fxe_ReSendValidateCoad {color: #ff5200;border: 1px solid #ff5200;}
.right_ico .n9 .bnzf .fxe_validateCode {color: #666;border: 1px solid #666;display:none;font-size:12px;}
.right_ico .n9 .bnzf #fxe_bnzf_submit {background: #ff5200;border: 0;line-height: 28px;margin: 0 11px;width: 204px;border-radius: 4px;font-size: 14px;color: #fff;cursor: pointer;}
.right_ico .n9 .bnzf .fxe_bnzf_wk {width: 142px;/* overflow: hidden; */margin: 0;float: left;height: 25px;border: 1px solid #ccc;background: #fff url(https://static.fangxiaoer.com/web/images/sy/index_new/my_xiala2.gif) 123px 9px no-repeat;}
.right_ico .n9 .bnzf .fxe_bnzf_xs {float: left;line-height: 24px;margin-left: 8px;color: #666;width: 134px;}
.right_ico a.n1:hover span{right: 25px;top:0;position: absolute;padding-right: 10px;}
.right_ico a.n2:hover{width:100px}
.right_ico a.n3:hover{width:88px}
.right_ico a.n4:hover,.right_ico a.n5:hover,.right_ico a.n6:hover{width:76px}
.right_ico a:hover span{display:block;color: #fff;padding-left: 8px;line-height: 31px;}
#MEIQIA-BTN-HOLDER{display:none !important}

/*楼盘活动*/
.lou_img{width: 212px;position:relative;float:left;margin-left:18px;margin-bottom: 18px;margin-top: 10px;}
.lou img{width:100%; height:154px;}
.lou_img img{width: 212px;}
.lou_img a{color:#fff;}
.lou_pos{ position:absolute; bottom:0;left:0;}
.lou_bg{width:100%; height:28px; background-color:#000;filter:alpha(opacity=50); /* IE */ -moz-opacity:0.5; /* Moz + FF */opacity: 0.5;}
.lou_title{ width:170px; line-height:28px; color:#fff; font-size:14px; padding-left:5px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}

/*右侧列表样式 2017-7-28*/
#right h1{font-size: 18px;color: #333;font-weight: 400;border-bottom: 1px solid #eaeaea;line-height: 43px;margin-bottom: 12px;padding: 0 16px;}
#right h1 span{display: inline-block;width: 4px;height: 18px;margin-bottom: -1px;border-radius: 50px;background: #ff5200;margin-right: 8px;}
.lou_img a{display:block;}
.lou_img{margin:0;width:100%;float: none;height: initial;}
.lou_img img{width:100%}
#right .lou_img h1{border:none;margin-bottom: 0;padding: 0;}
#right .lou_img a.lou_title{color: #fff;position: absolute;background: url(https://static.fangxiaoer.com/web/images/ico/sign/b60.png);width: 100%;text-align: center;bottom: 0;padding: 0;}

#right .rmph{background: #fff;padding-top: 4px;margin: 20px 0;border: 1px solid #eaeaea;overflow: hidden;padding-bottom: 10px;}
#right .rmph li{border:none;}
#right .rmph li s{background:none;font-size: 21px;font-family:Georgia;color:#ff5200;}
#right .rmph li span{float:right;}
#right .rmph p{margin-left:0;width: 190px;margin: 0;}
#right .rmph p a{margin-left: 6px;}
#right .rmph em{display:block;color:#999;margin: 0 0 0 36px;}

#right .cnxh_txt{height: 86px;line-height: 20px;}
#right .cnxh{background: #fff;padding-top: 4px;border: 1px solid #eaeaea;width: initial;}
#right .cnxh img{float:left;width:96px;height:70px;margin: 0 11px 0 20px;}
#right .cnxh .cnxh_title{position:static;color:#000;}
#right .cnxh .cnxh_m{float:left;width: 121px;margin: 0;}
#right .cnxh .cnxh_qian_icon{font-size:12px;color:#808080;}
#right .cnxh .cnxh_qian_icon i{font-size:16px;color:#ff5200;}
#right .cnxh  .cntx{float:left;width: 121px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;font-size: 12px;color: #999;}

.recommend{border: 1px solid #eaeaea;margin-top: 20px;background: #fff;/* padding: 0 16px;*/margin-bottom: 20px;position: relative;}
.recommend .title{display:none}
.recommend dt{line-height: 50px;font-size: 18px;font-weight: 400;border-bottom: 1px solid #eaeaea;padding: 0 16px;}
.recommend dt span{display: inline-block;width: 4px;height: 18px;margin-bottom: -1px;border-radius: 50px;background: #ff5200;margin-right: 8px;}
.recommend .hover{}
.recommend dd{overflow: hidden;font-size: 12px;color: #666;height: 36px;line-height: 36px;padding: 0 16px;}
.recommend dd>div a{text-decoration:none;}
.recommend dd>div span{display: inline-block;width: 103px;color: #444;text-decoration: none;text-overflow: ellipsis;white-space: nowrap;}
.recommend dd>div .hover{color:#ff5200;}
.recommend dd>div p{display: inline-block;width: 30px;color: #cecece;font-size: 14px;font-family: Arial;}
.recommend dd>div b{display: inline-block;text-align: right;width: 76px;color: #ff5200;font-weight: 400;}
.recommend dd>a{overflow: hidden;display: block;text-decoration: none;}
.recommend dd>a img{width: 80px;height: 60px;display: block;float: left;margin: 4px 20px 10px 20px;}
.recommend dd>a div{float: left;}
.recommend dd>a div p{line-height: 20px;}
.recommend li{background: none;margin-bottom: 23px;}
.recommend .unstyled{margin-top:18px}
.recommend .unstyled a:hover{text-decoration:none}
.listHeader{
    padding-right: 0px;
    height: 56px;
    line-height: 56px;
    background: #fafafc;
    padding-left: 21px;
    color: #1d364c;
    font-size: 16px;
    border-bottom: 1px solid #ededed;
    border-right: 1px solid #ededed;
    border-left: 1px solid #ededed;
    border-top: 1px solid #ededed;
}
.listHeader .hover img{display:none}
.listHeader .hover .hover_sun{display: inline-block;}
.listHeader a{
    display: inline-block;
    height: 60px;
    float: right;
    font-size: 16px;
    width: 72px;
}
.listHeader .hover{
    color:#ff5200
}
.listHeader a i{
    display: inline-block;
    width: 30px;
    height: 60px;
    float: right;
    background: url("https://static.fangxiaoer.com/web/images/sy/house/down.png") no-repeat 0 22px;
}
.listHeader a .up{
    background: url("https://static.fangxiaoer.com/web/images/sy/house/up.png") no-repeat 0 26px;
}
.listHeader a .down{
    background: url("https://static.fangxiaoer.com/web/images/sy/house/down.png") no-repeat 0 26px;
}
.listHeader a .map{
    background: url("https://static.fangxiaoer.com/web/images/ico/map/img_map.png") no-repeat 0 21px;
    float: left;
}
.listHeader .hover .prices{}
.listHeader .hover .remark{}
.houseInfo{
    border-bottom: 1px solid #eee;
    cursor:pointer;
    position:  relative;
}
.houseInfo:hover{
    background:#f7f8f8;
}
.houseInfo>.img{
    float: left;
    padding: 22px 26px 22px 18px;
    position: relative;
}
.houseInfo>.img img{
    width: 210px;
    height: 154px;
}
.houseInfo>.img span{
    position: absolute;
    bottom: 20px;
    color: #fff;
    background-repeat: no-repeat;
    padding-left: 21px;
    font-size: 12px;
    line-height: 17px;
}
.houseInfo>.img .images{
    background-image: url("https://static.fangxiaoer.com/web/images/sy/house/images.png");
    left: 23px;
}
.houseInfo>.img .video{
    background-image: url("https://static.fangxiaoer.com/web/images/sy/house/video.png");
    left: 80px;
}
.houseInfo>.info{
    font-size: 12px;
    color: #6d798c;
    float: left;
    width: 400px;
    padding-top: 26px;
}
.houseInfo>.info>li{
    line-height: 14px;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    margin-bottom: 13px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    /* line-height: 39px; */
}
.houseInfo>.info>li:nth-child(2){
    color:#666666;
}
.houseInfo>.info>li b{
    font-size: 22px;
    color: #272727 !important;
    font-weight: 600;
    display: inline-block;
    line-height: 16px;
}
.houseInfo>.info>li b a:hover{color: #ff5200 !important;}
.houseInfo>.info>li  a{/* color: #57a0e7 !important; */color: #272727!important;text-decoration: none;text-decoration: none;}
.houseInfo>.info>li  a:hover{color:#ff5200}
.houseInfo>.info>li  a:visited{color: #272727 !important;}
.houseInfo>.info>li .vip{
    background-image: url("https://static.fangxiaoer.com/web/images/sy/house/vip.svg");
    display: inline-block;
    width: 20px;
    height: 17px;
    margin-left: -2px;
    margin-bottom: -2px;
}
.houseInfo>.info>li .rob{
    background-image: url("https://static.fangxiaoer.com/web/images/sy/house/rob.png  ");
    display: inline-block;
    width: 20px;
    height: 15px;
    height: 16px;
    margin-left: 8px;
}
.houseInfo>.info>li .sell{
    color: #e60012 !important;
}
.houseInfo>.info>li .upcoming{
    color: #f19149;
}
.houseInfo>.info>li .label{
    background: #f3f5f7;
    display: inline-block;
    padding: 0px 6px;
    margin-right: 8px;
    line-height: 24px;
    /* margin-top: 14px; */
    color: #596c91;
}
.houseInfo:hover>.info>li .label{
    background: #f3f5f7;
}
.houseInfo>.prices{
    float: right;
    padding-top: 38px;
    margin-right: 20px;
    position: relative;
}
.houseInfo>.prices>li{
    color: #666666;
    font-size: 14px;
    position: relative;
    text-align: right;
    /* padding-right: 5px; */
}
.houseInfo>.prices>li .solded{ font-size:22px; font-weight:normal; color:#ff5200}
.houseInfo>.prices>li a{ font-size:14px; color:#57a0e3}
.houseInfo>.prices .agent-people{}
.houseInfo>.prices>li .contrastNo:hover,.houseInfo>.prices>li .contrast:hover{color: #ff5200 !important;}
.houseInfo>.prices>li .consult{
    cursor:pointer;
    display: block;
    /* overflow:  hidden; */
    margin-left: -180px;
    margin-top: -40px;
}
.agent-people{
    width: 56px;
    height: 60px;
    position:  absolute;
    left: 600px;
    top: 95px;
}
.agent-people  .agentUrl>a{}
.agent-people .agentUrl{width: 113pxpx;}
.agent-people  .agentImg{
    width: 40px;
    height: 40px;
    border-radius:  50%;
    overflow:  hidden;
    margin: 0 auto;
}
.agent-people .agentName{
    width: 60px;
    text-align:  center;
    padding-top: 5px;
}
.agentUrl img{width:100%}
.houseInfo>.prices>li .consult:hover{
    color:#333
}
.houseInfo>.prices>li .agentImg{width: 40px;height: 40px;border-radius: 20px;overflow: hidden;float: left;margin-left: 64px;}
.houseInfo>.prices>li .agentImg img{
    width: 100%;
    /* height: 200px; */
}
.houseInfo>.prices>li .agentName{float: left;padding-top: 21px;padding-left: -18px;/* background: url(https://static.fangxiaoer.com/web/images/sy/house/consult.png) no-repeat 24px 1px; */font-size: 12px;/* width: 90px; */margin-left: -40px;width:  40px;text-align: center;}
.houseInfo>.prices>li .agentName span{
    color: #333;
    background: none;
    padding: 0;
    /* padding-left: 8px; */
}
.houseInfo:hover>.prices>li .agentName span{
    background:none;
}
.houseInfo>.prices>li span{
    color: #666666;
    font-size: 14px;
    background-repeat: no-repeat;
    background-image: url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.svg);
    background-position: 99% 9px;
    padding-right: 18px;
}
.houseInfo:hover>.prices>li span{
    background-image: url( https://static.fangxiaoer.com/web/images/sy/house/my_xiala.svg);
    color: #666666;
    font-size: 14px;
}
.houseInfo>.prices>li span b{
    font-size: 30px;
    font-family: dinot-bold;
    display: inline-block;
    color: #ff5200;
}
.houseInfo>.prices>.phone{
    color: #1d364c;
    font-size: 14px;
    font-weight: bold;
    /* background: url(https://static.fangxiaoer.com/web/images/sy/house/phone.png)no-repeat 0 18px; */
    /* padding-left: 18px; */
    line-height: 50px;
    /* margin-right: 20px; */
    color: #333333;
    font-size: 14px;
    font-weight: 700;
    padding-left: 18px;
    line-height: 50px;
}
.houseInfo>.prices>.phone .icon_iphone{
    display: inline-block;
    background: url(https://static.fangxiaoer.com/web/images/sy/house/phone.svg)no-repeat 0 8px;
    width: 17px;
    height: 24px;
    margin-right: 8px;
    margin-bottom: -3px;
}
.houseInfo>.prices .collect{
    background-image: url(https://static.fangxiaoer.com/web/images/sy/house/collect.png);
    background-repeat: no-repeat;
    background-position: 0 5px;
    padding-left: 22px;
    cursor: pointer;
    display: none;
}
.houseInfo:hover>.prices .collect{
    display:inline-block;
}
.houseInfo>.prices .collectHover{
    background-image: url(https://static.fangxiaoer.com/web/images/sy/house/collect1.png);
}
.houseInfo>.prices>li>ul{
    background: #fff;
    position: absolute;
    z-index: 9999;
    box-shadow: 0px 0px 14px #999;
    padding: 4px 16px;
    padding-right: 0;
    top: -82px;
    display: none;
    /* height: 70px; */
}
.houseInfo>.prices>li .one{
    position: absolute;
    right: -22px;
    top: -50px;
    width: 160px;
}
.houseInfo:hover>.prices>li>ul{
    display:block;
}
.houseInfo>.prices>li>.min{
    width: 160px;
    left: 52px;
}
.houseInfo>.prices>li>.middle{
    width: 311px;
    left: -100px;
}
.houseInfo>.prices>li>.max{
    width: 460px;
    left: -170px;
}
.houseInfo>.prices>li ul li{
    padding-left: 22px;
    background-repeat: no-repeat;
    background-position: 0px 9px;
    line-height: 34px;
    float: left;
    width: 130px;
    text-align: left;
}
.houseInfo>.prices>li ul i{display: block;width: 13px;height: 8px;background: url(https://static.fangxiaoer.com/web/images/sy/house/jiao.png) no-repeat;bottom: -8px;position: absolute;right: 0;}
.houseInfo>.prices>li .one i{
    left: 144px;
}
.houseInfo>.prices>li>.min i{
    left: 142px;
}
.houseInfo>.prices>li>.middle i{
    left: 293px;
}
.houseInfo>.prices>li>.max i{
    left: 313px;
}
.houseInfo>.prices>li ul .rise{
    background-image: url(https://static.fangxiaoer.com/web/images/sy/house/rise.jpg);
}
.houseInfo>.prices>li ul .uniform{
    background-image: url(https://static.fangxiaoer.com/web/images/sy/house/uniform.jpg);
}
.houseInfo:hover .info>li>a:hover{color:#ff5200 !important}
.consultAlert{
    position: fixed;
    width: 100%;
    height: 100%;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/b50.png);
    top: 0;
    left: 0;
    z-index: 100000;
    display: none;
}
.consultAlert .consultAlertContent{
    text-align: center;
    width: 600px;
    background: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -210px;
    margin-left:  -300px;
    border-radius: 10px;
}
.consultAlert .consultAlertContent>img{
    position: absolute;
    right: 36px;
    top: 20px;
    cursor: pointer;
}
.consultAlert .consultAlertContent>ul{}
.consultAlert .consultAlertContent>ul li:nth-child(2){width: 100px;height: 100px;overflow: hidden;border-radius: 100px;margin: 0 auto;margin-top: 40px;}
.consultAlert .consultAlertContent>ul .consulPhone{
    color: #ff5200;
    font-size: 16px;
    font-family: Georgia;
    font-weight: bold;
    line-height: 40px;
    background: url(https://static.fangxiaoer.com/web/images/sy/house/consultPhone.png) no-repeat 1px 14px;
    width: 130px;
    text-align: left;
    padding-left: 20px;
    margin: 0 auto;
}
.consultAlert .consultAlertContent>ul li img{
    width: 100px;
    border-radius: 100000px;
}
.consultAlert .consultAlertContent>ul li b{
    line-height: 50px;
    color: #333;
    font-size: 18px;
}
.consultAlert .consultAlertContent>ul li #AgentShop{
    font-size: 14px;
    font-weight: 400;
}
.consultAlert .consultAlertContent>ul li span{
    color: #ff5200;
}
.consultAlert .consultAlertContent>ul li .consulPhone{}
.consultAlert .consultAlertContent>ul li input{
    width: 400px;
    height: 40px;
    border: 1px solid #ccc;
    margin-top: 10px;
}
.consultAlert .consultAlertContent>ul li a{
    width: 260px;
    height: 40px;
    background: #ff5200;
    display: block;
    line-height: 40px;
    color: #fff;
    font-weight: bold;
    border-radius: 4px;
    margin: 0 auto;
    margin-bottom: 50px;
    margin-top:  24px;
    cursor: pointer;
}
.consultAlert .consultAlertContent>ul li a:hover{
    text-decoration:none;
}

/*新房列表页第七行插入的预约带看*/
.newsList_yydk{
    width: 830px;
    height: 172px;
    background: url(https://static.fangxiaoer.com/web/images/sy/house/house/newsList_yydkBg.jpg);
    padding: 78px 0 0 58px;
    overflow: hidden;
}
.newsList_yydk .yydk_main{}
.newsList_yydk .yydk_main h3{
    font-size: 28px;
    font-weight: normal;
    margin-bottom: 25px;
}
.newsList_yydk .yydk_main>div{
    overflow: hidden;
    margin-bottom: 10px;
}
.newsList_yydk .yydk_main>div input{
    width: 350px;
    height: 30px;
    float: left;
    line-height: 31px;
    border: 1px solid #e5e5e5;
    padding-left: 10px;
    border-radius: 4px;
}
.newsList_yydk .yydk_main>div .ljyyBtn{
    width: 150px;
    height: 32px;
    line-height: 32px;
    float: left;
    text-align: center;
    background: #ff5200;
    color: #fff;
    font-size: 14px;
    margin-left: -2px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}
.newsList_yydk .yydk_main p{
    color: #ff5200;
}
.yydk_heibu{width: 100%;background: rgba(0, 0, 0, 0.3);height: -webkit-fill-available;background-size:  cover;background-position:  center;position:  fixed;top: 0;left:  0;z-index:  299;}

.yyDk{
    width: 500px;
    height: 400px;
    position: fixed;
    left: 50%;
    top: 50%;
    margin-left: -250px;
    margin-top: -200px;
}
.yyDk>div{
    background: #fff;
    width: 340px;
    height: 205px;
    border-radius: 4px;
    padding: 45px 80px 0 80px;
}
.yyDk>div .yyDkImg{
    text-align: center;
}
.yyDk>div .yyDkImg>img{}
.yyDk>div p{
    text-align: center;
    margin-top: 15px;
    color: #ff5200;
}
.yyDk>div p+p{
    margin-top: 35px;
    color: #333;
}
.yyDk .yyDk_close{
    height: 50px;
    background: none;
    margin-top: 125px;
    text-align: center;
    padding: 0;
    width: 100%;
}
.yyDk .yyDk_close img{
    cursor: pointer;
    width: 50px;
    height: 50px;
}


.tuijian{font-size:18px;border-left:2px solid #333333;height:18px;line-height:18px;padding-left:12px;margin-top:27px;font-weight:  bold;}
.list_wei{border-left:1px solid #ededed; border-right:1px solid #ededed;
    border-bottom:1px solid #ededed}
.tuijian_wei{border-left:1px solid #ededed;border-right:1px solid #ededed;border-top:1px solid #ededed;margin-top:15px;/* border-bottom: 1px solid #ededed; *//* padding-bottom: 12px; */}
.tuijian_wei_none{border-top:0; margin-top:0}
.list_wei_none{border-bottom: 0;}
.centerImg{width:62px!important;height:62px !important;position:absolute;left:96.5px;top: 60px;}

.allHouses{float:left !important;width: 87px !important;}
.void_sun{float:left !important;width: 101px !important;border-right: 1px solid #eeeeee;border-left: 1px solid #eeeeee;padding-left:  20px;padding-right: 20px;}
.void_sun img{margin-top: 20px;margin-right: 10px;}
.listHeader a{ height:57px;text-decoration:none}
.lp_count .sort{margin-right: 10px;}

.void_sun:hover{text-decoration:none}
.map_sun{font-size:14px!important; text-decoration:none}
.void_sun .hover_sun{display:none}
.void_sun:hover img{display:none}
.void_sun:hover .hover_sun{display:inline-block}
.map_sun:hover{text-decoration:none}
#option_other .select_box ul li a{height:97%}
#option_other ul li div+div ul {
    height: 145px !important;
}


.houseInfo>.info>li label{background: url(https://static.fangxiaoer.com/web/images/sy/metro/metro.png);line-height:22px;background-size: auto 100%;font-size: 12px;display: inline-block;padding-right: 8px;padding-left: 4px;color: #fff;background-repeat: no-repeat;}
.label_sun{margin-bottom:0 !important;margin-top: 0;}


.Along{border: 1px solid #eaeaea;margin-top: 20px;background: #fff;/* padding: 0 16px;*/margin-bottom: 20px;position: relative;padding-bottom: 14px;}
.Along .title{display:none}
.Along dt{line-height: 50px;font-size: 18px;font-weight: 400;border-bottom: 1px solid #eaeaea;padding: 0 16px;}
.Along dt span{display: inline-block;width: 4px;height: 18px;margin-bottom: -1px;border-radius: 50px;background: #ff5200;margin-right: 8px;}
.Along .photo{width:224px; height:200px; margin:0 auto;margin-top:14px}
.Along .photo img{width:100%;/* height: 200px; */}
.Along .photo{position:relative;cursor: pointer;height: 200px;overflow: hidden;}
.Along .photo .look{ position:absolute;right:0; bottom:0;cursor:pointer}
.Along .photo .look{background-color:rgba(0,0,0,0.6);width:66px}
.Along .photo .look h2{color:#fff;font-weight:normal;line-height:22px;text-align:center;font-size: 12px;}
.Along .btn{width:224px; margin:0 auto;margin-top:13px}
.Along .btn a{display:block;background-color:#ff6100;text-align:center;line-height:36px;color:#fff;font-size:14px;text-decoration: none;}



.layer_box{width:100%;height:100%;position:fixed;left:0;top:0;background-color:rgba(0,0,0,0.5);z-index: 99999999;display:none;}
.layer_box table{ width:100%; height:100%}
.layer_box table .layer{width:800px; margin:0 auto;position:relative}
.layer_box table .layer .close{position:absolute;right:17px;top:17px}
.layer_box table .layer .close:hover{background:none}


.Inventory{border: 1px solid #eaeaea;margin-top: 20px;background: #fff;/* padding: 0 16px;*/margin-bottom: 20px;position: relative;padding-bottom: 14px;}
.Inventory .title{display:none}
.Inventory dt{line-height: 50px;font-size: 18px;font-weight: 400;border-bottom: 1px solid #eaeaea;padding: 0 16px;}
.Inventory dt span{display: inline-block;width: 4px;height: 18px;margin-bottom: -1px;border-radius: 50px;background: #ff5200;margin-right: 8px;}
.Inventory .list ul li .photo{width:224px; margin:0 auto;position:relative}
.Inventory .list ul li .photo img{width:100%;height: 146px;}
.Inventory .list{margin-top:16px}
.Inventory .list ul li .photo .name{ width:100%;position:absolute;left:0;
    bottom:0;}
.Inventory .list ul li .photo .name h2{ float:left}
.Inventory .list ul li .photo .name h3{ float:right}
.Inventory .list ul li .photo .name{background-color:rgba(0,0,0,0.5)}
.Inventory .list ul li .photo .name h2{font-size:14px;line-height:30px;color:#fff;
    font-weight:normal;padding-left:12px}
.Inventory .list ul li .photo .name h3{font-size:14px;line-height:30px;color:#fff;
    font-weight:normal;padding-right:12px}
.Inventory .list ul li .metro{width:224px;margin:0 auto;margin-top:10px;border-bottom: 1px solid #eaeaea;padding-bottom: 10px;}
.Inventory .list ul li .metro p{font-size:12px; color:#333}
.Inventory .list ul li .metro p span{padding:4px;border:1px solid #ebebeb;margin-right:12px;font-weight: bold;}
.Inventory .list ul li .Reason{width:224px;margin:0 auto;}
.Inventory .list ul li .Reason h2{font-size:12px;height:12px; line-height:12px;margin-top:10px}
.Inventory .list ul li .Reason p{font-size:12px;color:#333333;line-height: 17px;margin-top: 6px;}
.Inventory .list ul li{margin-top: 17px;}
.Inventory .list ul li:nth-child(1){margin-top:0px}
.Inventory .list ul li .metro p{float:left}
.Inventory .list ul li .metro .list_sun{ float:left}
.metro .list_sun .list_wei{margin:0 !important;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 151px;border: 0;font-size: 12px;line-height: 20px;}
.houseInfo .info .name_box_wei{overflow: visible;padding-top: 4px;}
.layer_box table .layer img{ width:100%}
.houseInfo>.discontain span{background:none !important}

.contrast{width:auto;/* display: inline-block; */padding-left: 20px;background-image: url(https://static.fangxiaoer.com/web/images/ico/Contrast02.png);background-repeat: no-repeat;background-position: left center;margin-top: 20px;width: auto;display: inline-block;padding-left: 20px;background-image: url(https://static.fangxiaoer.com/web/images/ico/Contrast02.svg);background-repeat: no-repeat;/* left: 6px; */background-position: 0px -100px;background-position: 7px center;margin-top: 23px;border: 1px solid #e8e9ea;padding: 2px 7px 2px 25px;}
.contrast:hover{background-image: url(https://static.fangxiaoer.com/web/images/ico/Contrast.svg);}

.contrastNo{width: 28px;display: inline-block;padding-left: 20px;background-image: url(https://static.fangxiaoer.com/web/images/ico/Contrast.png);background-repeat: no-repeat;background-position: left center;text-align: right;float: right;margin-top: 20px;width: 28px;display: inline-block;padding-left: 20px;background-image: url(https://static.fangxiaoer.com/web/images/ico/Contrast.svg);background-repeat: no-repeat;background-position: left center;text-align: right;float: right;margin-top: 20px;border: 1px solid #e8e9ea;padding: 2px 7px 2px 25px;width: auto;display: inline-block;padding-left: 20px;/* background-image: url(https://static.fangxiaoer.com/web/images/ico/Contrast02_17.png); */background-repeat: no-repeat;/* left: 6px; */background-position: 0px -100px;background-position: 7px center;margin-top: 23px;/* margin-top: 3px; *//* margin-bottom: -4px; */border: 1px solid #e8e9ea;padding: 2px 7px 2px 25px;}
.db-li{display:block !important}

.brand_info{position: relative;margin-top: 6px;}
.brand_info a{display:block}
.brand_info span{
    display: inline-block;
    color: #333;
    padding-left: 36px;
    padding-right: 26px;
    line-height: 23px;
}
.brand_info i{
    display: inline-block;
    width: 26px;
    height: 24px;
    position: absolute;
    left: 0;
    top: 0;
    background-image: url(https://static.fangxiaoer.com/web/images/brand/brand.svg);
}
.brand_info:hover span{color:#ff5200}
.Advertisement .pic img{width:100%}
.Advertisement{margin-bottom:20px}
.swiper-pagination-bullet{border-radius:0 !important;opacity:1 !important;background-color:#775d1e !important;width: 5px !important;height: 2px !important;border-radius: 1px !important;}
.swiper-pagination-bullet-active{background-color:#fff !important;width: 10px !important;}
.swiper-slide{position:relative}
.swiper-slide .pic_sun{position:absolute;right:0;bottom:0;background-color:rgba(0,0,0,0.2)}

.ranks_enter span:first-child {
    margin-right: -4px;
}
.imgG{
    /* margin-top: 7px; */
    margin-left: 5px;
    vertical-align: middle;
    margin-right: 5px;
    display: inline-block;
}
/* 20190325新增排行榜入口 */
.rank_brand_enter{
    background: #f3f4f6;
    display: flex;
    width: 846px;
    height: 36px;
    margin: 0 auto;
    padding: 0 7px;
    position: relative;
    top: -6px;
    margin-bottom: 16px;
}
.ranks_enter{position:relative;margin-top:6px;}
.ranks_enter .rank_icon{display:inline-block;width:26px;height:24px;background-image: url(https://static.fangxiaoer.com/web/images/brand/rank.svg);}
.ranks_enter a{display:flex;text-decoration: none;}
.ranks_enter p{display:inline-block;color:#dbdbdb;line-height:23px;padding:0 14px;padding-right:11px}
.ranks_enter p:hover{color:#ff5200}
.ranks_enter:hover span{color:#ff5200}
.ranks_enter span{color:#333;margin-right: 0;display: inline-block;}
@font-face {
    font-family: 'dinot-bold';   /*字体名称*//*/font/*/
    src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');       /*字体源文件*/
}
.determined{
    font-size: 22px !important;
    font-family: Microsoft YaHe !important;
    margin-right: -17px;
}

/*  看房团*/
.ranks_KFenter {
    position: relative;
    margin-top: 6px;
    padding-bottom: 14px;
}
.ranks_KFenter .rank_KFicon {
    display: inline-block;
    width: 26px;
    height: 24px;
    background-image: url(https://static.fangxiaoer.com/web/images/brand/tuan.png);
}
.ranks_KFenter span {
    display: inline-block;
    color: #333;
    padding-left: 10px;
    padding-right: 26px;
    line-height: 22px;
}
.ranks_KFenter a {
    display: flex;
    text-decoration: none;
}
.ranks_enter span {
    display: inline-block;
    color: #333;
    padding-left: 10px;
    padding-right: 26px;
    line-height: 23px;
}