@charset "utf-8";
/*
修改时间：2018-04-10
用处：二手房小屋列表
*/
#main {
	width: 1170px;
	margin: 20px auto 0;
}

/*二手房小区页头部*/
.listHeader{
    overflow: hidden;
}
.listHeader h4{
    font-size:  26px;
    color:  #333;
    font-weight:  normal;
    float: left;
}
.listHeader p{
    margin-left:  20px;
    float:  left;
    line-height: 39px;
    color:  #333;
}
.listHeader p .MapVillage{
    margin-left: 15px;
    cursor:  pointer;
}
.listHeader p .MapVillage s{
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/listIcon_dw_hover.png) no-repeat;
    width: 15px;
    height: 19px;
    display: inline-block;
    cursor: pointer;
    margin-right: 5px;
    background-size: 100%;
}
.villageNav{
    overflow:  hidden;
    border-bottom:  2px solid  #ff5200;
}
.villageNav li{
    float:  left;
    width:  112px;
    height:  35px;
    border: 1px solid #e8e8e8;
    line-height:  35px;
    text-align:  center;
    margin-bottom: -1px;
    cursor:  pointer;
    color:  #333;
}
.villageNav li.hover{background:  #ff5200;color: #fff;border:  1px solid #ff5200;}
