@import url(https://fonts.googleapis.com/css?family=Raleway);
/* line 2, ../sass/inc/_base.scss */
body {
  background-color: #fff;
  font-family: Raleway, sans-serif;
  margin: 0;
  padding: 0;
}

/* line 8, ../sass/inc/_base.scss */
* {
  box-sizing: border-box;
}

/* line 9, ../sass/inc/_base.scss */
a {
  color: #fff;
  font-size: 14px;
  letter-spacing: 0.125em;
  text-decoration: none;
  text-transform: uppercase;
}

/* line 16, ../sass/inc/_base.scss */
h1 {
  text-transform: uppercase;
  margin: 0;
  padding: 0;
  font-size: 2.5rem;
}

/* line 22, ../sass/inc/_base.scss */
.pre {
  display: inline-block;
  padding: 20px;
  background: #f1f1f1;
  box-shadow: 3px 3px 0 1px rgba(0, 0, 0, 0.1);
  width: 100%;
}

@-webkit-keyframes ball-scale-multiple {
  0% {
    transform: scale(0) rotate(-90deg);
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  100% {
    transform: scale(1.2) rotate(90deg);
    opacity: 0;
  }
}
@keyframes ball-scale-multiple {
  0% {
    transform: scale(0) rotate(-90deg);
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  100% {
    transform: scale(1.2) rotate(90deg);
    opacity: 0;
  }
}
/* line 1, ../sass/inc/_loader.scss */
.loaderWrap {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 123333333;
}

/* line 10, ../sass/inc/_loader.scss */
.ball-scale-multiple {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 50%);
}
/* line 15, ../sass/inc/_loader.scss */
.ball-scale-multiple > div {
  background-color: #fff;
  border-radius: 0;
  animation-fill-mode: both;
  position: absolute;
  left: -60px;
  top: -60px;
  opacity: 0;
  margin: 0;
  width: 120px;
  height: 120px;
  animation: ball-scale-multiple 1s 0s linear infinite;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2);
}
/* line 28, ../sass/inc/_loader.scss */
.ball-scale-multiple > div:nth-child(1) {
  animation-delay: -1s;
}
/* line 31, ../sass/inc/_loader.scss */
.ball-scale-multiple > div:nth-child(2) {
  animation-delay: -0.8s;
}
/* line 34, ../sass/inc/_loader.scss */
.ball-scale-multiple > div:nth-child(3) {
  animation-delay: -0.6s;
}
/* line 37, ../sass/inc/_loader.scss */
.ball-scale-multiple > div:nth-child(4) {
  animation-delay: -0.4s;
}
/* line 40, ../sass/inc/_loader.scss */
.ball-scale-multiple > div:nth-child(5) {
  animation-delay: -0.2s;
}

/* line 7, ../sass/style.scss */
.a-container {
  width: 1170px;
  margin: 20px auto;
}

/* line 12, ../sass/style.scss */
.a-1_2 {
  width: 48%;
  display: inline-block;
  margin: 0 1%;
}

/* line 17, ../sass/style.scss */
.o-sliderContainer {
  overflow: hidden;
  position: relative;
  width: 100%;
  min-height: 200px;
  background: #fff;
  float: left;
  margin: 30px 0;
  height: auto;
}
/* line 27, ../sass/style.scss */
.o-sliderContainer.hasShadow {
  box-shadow: 0 39px 22px -29px rgba(0, 0, 0, 0.2);
}
/* line 31, ../sass/style.scss */
.o-sliderContainer:hover .o-slider-next {
  right: 0;
}
/* line 34, ../sass/style.scss */
.o-sliderContainer:hover .o-slider-prev {
  left: 0;
}

/* line 39, ../sass/style.scss */
.o-slider--item {
  height: auto;
  width: 0;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  float: left;
  transition: all 0.2s cubic-bezier(0.7, 0.18, 0.53, 0.75);
  opacity: 0.6;
  transform: scale(0.95, 0.95);
  transform-origin: center center;
  z-index: 99;
}
/* line 52, ../sass/style.scss */
.o-slider--item::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.3);
  background: -moz-linear-gradient(45deg, rgba(0, 0, 0, 0.6) 0%, transparent 70%);
  background: -webkit-gradient(left bottom, right top, color-stop(0%, rgba(0, 0, 0, 0.6)), color-stop(70%, transparent));
  background: -webkit-linear-gradient(45deg, rgba(0, 0, 0, 0.6) 0%, transparent 70%);
  background: -o-linear-gradient(45deg, rgba(0, 0, 0, 0.6) 0%, transparent 70%);
  background: -ms-linear-gradient(45deg, rgba(0, 0, 0, 0.6) 0%, transparent 70%);
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.6) 0%, transparent 70%);
  z-index: 0;
}
/* line 65, ../sass/style.scss */
.o-slider--item.isActive {
  opacity: 1;
  transform: scale(1, 1);
}
/* line 69, ../sass/style.scss */
.o-slider--item.isMoving {
  opacity: 0.6;
  transform: scale(0.95, 0.95);
  z-index: 100;
}

/* line 76, ../sass/style.scss */
.o-slider {
  width: auto;
  position: relative;
  display: none;
}
/* line 81, ../sass/style.scss */
.o-slider.isVisible {
  display: table;
  background: #000;
}
/* line 86, ../sass/style.scss */
.o-slider.isDraggable {
  cursor: move;
}

/* line 91, ../sass/style.scss */
.o-slider-textWrap {
  left: 2%;
  bottom: 45px;
  height: auto;
  position: absolute;
  text-align: left;
  padding: 0 0 20px 0;
  width: 96%;
  transition: all 0.4s;
  opacity: 1;
  overflow: visible;
  perspective: 1000px;
  z-index: 2;
}

/* line 107, ../sass/style.scss */
.isActive .o-slider-textWrap::after {
  width: 100%;
  left: 0;
}
/* line 112, ../sass/style.scss */
.isActive .o-slider-paragraph {
  opacity: 1;
  transform: perspective(0) rotateY(0) translate(0, 0);
  transform-origin: 0 0;
  transition-delay: 1s;
}
/* line 119, ../sass/style.scss */
.isActive .o-slider-title {
  opacity: 1;
  transform: perspective(0) rotateY(0) translate(0, 0);
  transform-origin: 0 0;
  transition-delay: 0.2s;
}
/* line 126, ../sass/style.scss */
.isActive .o-slider-subTitle {
  opacity: 1;
  transform: perspective(0) rotateY(0) translate(0, 0);
  transform-origin: 0 0;
  transition-delay: 0.6s;
}

/* line 155, ../sass/style.scss */
.o-slider-title {
  width: auto;
  margin: 0 0 5px 0;
  height: auto;
  color: #fff;
  text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
  font-size: 1.4rem;
  display: inline-block;
  padding: 0;
  transition: all 0.4s;
  transform-origin: 0 0;
  transform: perspective(1000px) rotateY(120deg) translate(100px, -100px);
  opacity: 0;
  position: relative;
  z-index: 1;
}

/* line 172, ../sass/style.scss */
.o-slider-subTitle {
  width: auto;
  margin: 0 0 5px 0;
  height: auto;
  color: #fff;
  text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
  font-size: 1.4rem;
  display: inline-block;
  padding: 0;
  transition: all 0.4s;
  transform-origin: 0 0;
  transform: perspective(1000px) rotateY(120deg) translate(100px, -100px);
  opacity: 0;
  position: relative;
  z-index: 1;
  font-size: 1.2rem;
}

/* line 190, ../sass/style.scss */
.o-slider-paragraph {
  width: auto;
  margin: 0 0 5px 0;
  height: auto;
  color: #fff;
  text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
  font-size: 1.4rem;
  display: inline-block;
  padding: 0;
  transition: all 0.4s;
  transform-origin: 0 0;
  transform: perspective(1000px) rotateY(120deg) translate(100px, -100px);
  opacity: 0;
  position: relative;
  z-index: 1;
  font-size: 0.8rem;
  max-width: 100%;
  display: none;
}

@media screen and (min-width: 768px) {
  /* line 211, ../sass/style.scss */
  .o-slider-textWrap {
    left: 5%;
    width: 90%;
  }

  /* line 216, ../sass/style.scss */
  .o-slider-title {
    font-size: 1.8rem;
  }

  /* line 220, ../sass/style.scss */
  .o-slider-subTitle {
    font-size: 1.4rem;
  }

  /* line 224, ../sass/style.scss */
  .o-slider-paragraph {
    max-width: 60%;
    font-size: 1rem;
    display: block;
  }
}
@media screen and (min-width: 1024px) {
  /* line 232, ../sass/style.scss */
  .o-slider-pagination {
    bottom: 6%;
  }

  /* line 234, ../sass/style.scss */
  .o-slider-textWrap {
    left: 70px;
    width: 80%;
    bottom: 50px;
  }

  /* line 240, ../sass/style.scss */
  .o-slider-title {
    font-size: 2rem;
  }

  /* line 244, ../sass/style.scss */
  .o-slider-subTitle {
    font-size: 1.6rem;
  }

  /* line 248, ../sass/style.scss */
  .o-slider-paragraph {
    max-width: 40%;
    font-size: 1.2rem;
  }
}
/* line 299, ../sass/style.scss */
.a-divider {
  display: block;
  width: 100%;
  height: 1px;
  margin: 2px 0;
}

/* line 306, ../sass/style.scss */
.o-slider-controls {
  display: none;
}
/* line 309, ../sass/style.scss */
.o-slider-controls.isVisible {
  display: block;
}

/* line 314, ../sass/style.scss */
.o-slider-pagination {
  bottom: 2%;
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%;
  z-index: 1444000;
  margin: 0;
  padding: 0;
}
/* line 324, ../sass/style.scss */
.o-slider-pagination > li {
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.9), 0 0 2px 0 rgba(0, 0, 0, 0.4) inset;
  display: inline-block;
  position: relative;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 20px;
  width: 20px;
  margin-left: 6px;
  margin-right: 6px;
  transition: all 250ms;
  cursor: pointer;
  background-clip: padding-box;
  z-index: 100;
}
/* line 340, ../sass/style.scss */
.o-slider-pagination > li.isActive {
  background-color: rgba(255, 255, 255, 0.9);
  transform: scale(1.3, 1.3);
}

/* line 347, ../sass/style.scss */
.o-slider--preview {
  position: absolute;
  top: -70px;
  right: auto;
  bottom: auto;
  left: -65px;
  opacity: 0;
  width: 140px;
  height: 60px;
  background-size: cover;
  background-position: center center;
  transition: all 250ms;
  z-index: -1;
  border: 3px solid #000;
  transform: scale(0.2, 0.2);
  transform-origin: center bottom;
  display: none;
}
/* line 362, ../sass/style.scss */
.o-slider--preview::before {
  content: "";
  position: absolute;
  top: auto;
  right: auto;
  bottom: -13px;
  left: 50%;
  margin: 0 0 0 -5px;
  height: 0;
  width: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #000;
}

/* line 374, ../sass/style.scss */
.o-slider--preview.isActive,
.o-slider-pagination li:hover .o-slider--preview {
  opacity: 1;
  position: absolute;
  top: -76px;
  right: auto;
  bottom: auto;
  left: -65px;
  transform: scale(1, 1);
}

@media screen and (min-width: 768px) {
  /* line 384, ../sass/style.scss */
  .o-slider--preview {
    display: block;
  }
}
/* line 389, ../sass/style.scss */
.o-slider-arrows {
  margin: 0;
  list-style: none;
  display: block;
}

/* line 395, ../sass/style.scss */
.o-slider-next,
.o-slider-prev {
  position: absolute;
  top: 0;
  left: -100px;
  margin: -12px 0 0;
  width: auto;
  z-index: 1200122;
  padding: 0;
  margin: 0;
  height: 100%;
  list-style: none;
  cursor: pointer;
  text-align: center;
  padding: 10px 25px;
  background: rgba(0, 0, 0, 0.4);
  display: inline-block;
  vertical-align: middle;
  line-height: 100%;
  transition: all 0.4s;
}
/* line 416, ../sass/style.scss */
.o-slider-next i,
.o-slider-prev i {
  position: absolute;
  top: 50%;
  left: 0;
  margin: -12px 0 0;
  text-align: center;
  width: 100%;
  font-size: 1.8rem;
  color: #fff;
}

/* line 428, ../sass/style.scss */
.o-slider-next {
  left: auto;
  right: -100px;
}

/* line 433, ../sass/style.scss */
.isDisabled {
  opacity: 0.1;
  cursor: auto;
}

@media screen and (max-width: 1280px) {
  /* line 439, ../sass/style.scss */
  .a-container {
    width: 970px;
  }
}
@media screen and (max-width: 1024px) {
  /* line 445, ../sass/style.scss */
  .o-slider-arrows {
    display: none;
  }

  /* line 449, ../sass/style.scss */
  .o-sliderContainer {
    height: 450px;
    width: 100%;
    margin: 0 0 50px 0;
  }

  /* line 455, ../sass/style.scss */
  .o-slider--item {
    height: 450px;
  }
}
@media screen and (max-width: 768px) {
  /* line 461, ../sass/style.scss */
  .a-container {
    width: 90%;
    margin: 20px auto;
  }

  /* line 466, ../sass/style.scss */
  .a-1_2 {
    width: 100%;
    display: block;
    margin: 0;
  }

  /* line 472, ../sass/style.scss */
  .o-slider-arrows {
    display: none;
  }

  /* line 476, ../sass/style.scss */
  .o-sliderContainer {
    height: 350px;
  }

  /* line 480, ../sass/style.scss */
  .o-slider--item {
    height: 350px;
  }

  /* line 484, ../sass/style.scss */
  .o-slider-panel img {
    left: -15%;
    width: 130%;
  }
}
@media screen and (max-width: 480px) {
  /* line 491, ../sass/style.scss */
  .o-slider--item {
    height: 270px;
  }
  /* line 494, ../sass/style.scss */
  .o-slider--item img {
    left: -20%;
    width: 140%;
  }

  /* line 500, ../sass/style.scss */
  .o-sliderContainer {
    height: 270px;
  }
}
/* line 505, ../sass/style.scss */
.slider-pagination > li:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: scale(1.3, 1.3);
}
