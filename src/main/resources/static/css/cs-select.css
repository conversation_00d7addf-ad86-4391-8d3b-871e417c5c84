/* Default custom select styles */

div.cs-select {
	display: inline-block;
	vertical-align: middle;
	position: relative;
	text-align: left;
	background: #fff;
	z-index: 100;
	width: 100%;
	max-width: 500px;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

div.cs-select:focus {
	outline: none;
	/* For better accessibility add a style for this in your skin */
}

.cs-select select {
	display: none;
}

.cs-select span {
	display: block;
	position: relative;
	cursor: pointer;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 42px;
    line-height: 42px;
    width: 80px;
}



/* Placeholder and selected option */

.cs-select>span::after,
.cs-select .cs-selected span::after {
	speak: none;
	position: absolute;
	top: 50%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.cs-select>span::after {
	background: url(https://static.fangxiaoer.com//web/images/sy/sale/xia.png) right center no-repeat;
	background-size: 10px 7px;
}

.cs-select .cs-selected span::after {
	background: url(https://static.fangxiaoer.com//web/images/sy/sale/xia.png) right center no-repeat;
	background-size: 10px 7px;
}

.cs-select.cs-active>span::after {
	background: url(https://static.fangxiaoer.com//web/images/sy/sale/xia.png) right center no-repeat;
	background-size: 10px 7px;
}


/* Options */

.cs-select .cs-options {
	position: absolute;
	overflow: hidden;
	width: 100%;
	background: #fff;
	visibility: hidden;
}

.cs-select.cs-active .cs-options {
	visibility: visible;
}

.cs-select ul {
	list-style: none;
	margin: 0;
	padding: 0;
	width: 100%;
}

.cs-select ul li.cs-focus span {
	background-color: #ddd;
}

.cs-select li.cs-optgroup>span {
	cursor: default;
}

@font-face {
	font-weight: normal;
	font-style: normal;
	font-family: 'codropsicons';
	src: url('../fonts/codropsicons/codropsicons.eot');
	src: url('../fonts/codropsicons/codropsicons.eot?#iefix') format('embedded-opentype'), url('../fonts/codropsicons/codropsicons.woff') format('woff'), url('../fonts/codropsicons/codropsicons.ttf') format('truetype'), url('../fonts/codropsicons/codropsicons.svg#codropsicons') format('svg');
}



.clearfix:before,
.clearfix:after {
	content: '';
	display: table;
}

.clearfix:after {
	clear: both;
}








/* Demo themes */

.color-2 {
	background: #bbc7c8;
	color: #fff;
}

.color-2 a {
	color: #566473;
}

.color-2 a:hover,
.color-2 a:focus {
	color: #34495e;
}

.color-3 {
	background: #00b6ad;
	color: #fff;
}

.color-3 a {
	color: #04706b;
}

.color-3 a:hover,
.color-3 a:focus {
	color: #03514d;
}

.color-4 {
	background: #3b3f45;
	color: #f9f9f9;
}

.color-4 a {
	color: #eb7e7f;
}

.color-4 a:hover,
.color-4 a:focus {
	color: #c56667;
}

.color-5 {
	background: #f06d54;
	color: #fff;
}

.color-5 a {
	color: #a35749;
}

.color-5 a:hover,
.color-5 a:focus {
	color: #6d3126;
}

.color-6 {
	background: #f9f9f9;
	color: #62706c;
}

.color-6 a {
	color: #1ecd97;
}

.color-6 a:hover,
.color-6 a:focus {
	color: #1ab585;
}

.color-7 {
	background: #fffed8;
	color: #6bccb4;
}

.color-7 a {
	color: #eb7e7f;
}

.color-7 a:hover,
.color-6 a:focus {
	color: #c36263;
}

.color-8 {
	background: #415c71;
	color: #fefef8;
}

.color-8 a {
	color: #7ad7ee;
}

.color-8 a:hover,
.color-8 a:focus {
	color: #539eb1;
}

section {
	padding: 4% 1em 10%;
	text-align: center;
}

label.select-label {
	display: block;
	text-transform: uppercase;
	padding: 1em 0 1.5em;
	font-size: 75%;
	letter-spacing: 1px;
	font-weight: 700;
	width: 300px;
	text-align: left;
	margin: 0 auto;
	color: #c0c6c4;
}

.color-4 label.select-label {
	color: #282b30;
	font-size: 1em;
}


/* Header */

.codrops-header {
	margin: 0 auto;
	padding: 2em;
	text-align: center;
}

.codrops-header h1 span {
	display: block;
	font-size: 30%;
	text-transform: uppercase;
	letter-spacing: 2px;
	opacity: 0.8;
}

.codrops-header h1 {
	margin: 0;
	font-weight: 700;
	font-size: 3.5em;
	line-height: 1.3;
}


/* To Navigation Style */

.codrops-top {
	width: 100%;
	text-transform: uppercase;
	font-weight: 700;
	font-size: 0.69em;
	line-height: 2.2;
}

.codrops-top a {
	display: inline-block;
	padding: 0 1em;
	text-decoration: none;
	letter-spacing: 1px;
}

.codrops-top span.right {
	float: right;
}

.codrops-top span.right a {
	display: block;
	float: left;
}

.codrops-icon:before {
	margin: 0 4px;
	text-transform: none;
	font-weight: normal;
	font-style: normal;
	font-variant: normal;
	font-family: 'codropsicons';
	line-height: 1;
	speak: none;
	-webkit-font-smoothing: antialiased;
}

.codrops-icon-drop:before {
	content: "\e001";
}

.codrops-icon-prev:before {
	content: "\e004";
}


/* Demo Buttons Style */

.codrops-demos {
	padding-top: 1em;
	font-size: 0.9em;
}

.codrops-demos a {
	display: inline-block;
	margin: 0.5em;
	padding: 0.7em 1.1em;
	outline: none;
	text-decoration: none;
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: 700;
}

.codrops-demos a.current-demo {
	color: inherit;
}


/* Related demos */

.related {
	padding-top: 20em;
}

.related p {
	font-size: 1.6em;
}

.related>a {
	border: 3px solid;
	border-color: initial;
	display: inline-block;
	vertical-align: top;
	text-align: center;
	margin: 20px 10px;
	padding: 25px;
}

.related a img {
	max-width: 100%;
	opacity: 0.8;
}

.related a:hover img,
.related a:active img {
	opacity: 1;
}

.related a h3 {
	margin: 0;
	padding: 0.5em 0 0.3em;
	max-width: 300px;
	text-align: left;
}

body #cdawrap {
	top: auto;
	bottom: 30px;
	right: 30px;
}

@media screen and (max-width: 30em) {
	body::before,
	body::after {
		display: none !important;
	}
	body {
		border: none !important;
		padding: 0 !important;
	}
	.codrops-header h1 {
		font-size: 2em;
	}
	.codrops-icon span {
		display: none;
	}
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
	display: block;
}

audio,
canvas,
video {
	display: inline-block;
}

audio:not([controls]) {
	display: none;
	height: 0;
}

[hidden] {
	display: none;
}

html {
	font-family: sans-serif;
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
}

body {
	margin: 0;
}



h1 {
	font-size: 2em;
	margin: 0.67em 0;
}

abbr[title] {
	border-bottom: 1px dotted;
}

b,
strong {
	font-weight: bold;
}

dfn {
	font-style: italic;
}

hr {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	height: 0;
}

mark {
	background: #ff0;
	color: #000;
}

code,
kbd,
pre,
samp {
	font-family: monospace, serif;
	font-size: 1em;
}

pre {
	white-space: pre-wrap;
}

q {
	quotes: "\201C" "\201D" "\2018" "\2019";
}

small {
	font-size: 80%;
}

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sup {
	top: -0.5em;
}

sub {
	bottom: -0.25em;
}

img {
	border: 0;
}

svg:not(:root) {
	overflow: hidden;
}

figure {
	margin: 0;
}

fieldset {
	border: 1px solid #c0c0c0;
	margin: 0 2px;
	padding: 0.35em 0.625em 0.75em;
}

legend {
	border: 0;
	padding: 0;
}

button,
input,
select,
textarea {
	font-family: inherit;
	font-size: 100%;
	margin: 0;
}

button,
input {
	line-height: normal;
}

button,
select {
	text-transform: none;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
	-webkit-appearance: button;
	cursor: pointer;
}

button[disabled],
html input[disabled] {
	cursor: default;
}

input[type="checkbox"],
input[type="radio"] {
	box-sizing: border-box;
	padding: 0;
}

input[type="search"] {
	-webkit-appearance: textfield;
	-moz-box-sizing: content-box;
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
	border: 0;
	padding: 0;
}

textarea {
	overflow: auto;
	vertical-align: top;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

@font-face {
	font-family: 'icomoon';
	src: url('../fonts/icomoon/icomoon.eot?-rdnm34');
	src: url('../fonts/icomoon/icomoon.eot?#iefix-rdnm34') format('embedded-opentype'), url('../fonts/icomoon/icomoon.woff?-rdnm34') format('woff'), url('../fonts/icomoon/icomoon.ttf?-rdnm34') format('truetype'), url('../fonts/icomoon/icomoon.svg?-rdnm34#icomoon') format('svg');
	font-weight: normal;
	font-style: normal;
}

div.cs-skin-border {
	background: transparent;
	font-size: 14px;
	max-width: 90px;
	color: #222222;
	    margin-right: 46px;
    margin-left: 20px;
	
}

.cs-skin-border>span {
	border-color: inherit;
	color: #222222;
    font-size: 14px;
	-webkit-transition: background 0.2s, border-color 0.2s;
	transition: background 0.2s, border-color 0.2s;
}

.cs-skin-border ul span::after {
	content: '';
	opacity: 0;
}

.cs-skin-border.cs-active>span {
	background: #fff;
	border-color: #fff;
}

.cs-skin-border .cs-options {
	font-size: 12px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #8D92A1;
	width: 149px;
	opacity: 0;
	-webkit-transition: opacity 0.2s, visibility 0s 0.2s;
	transition: opacity 0.2s, visibility 0s 0.2s;
}

.cs-skin-border.cs-active .cs-options {
	opacity: 1;
	-webkit-transition: opacity 0.2s;
	transition: opacity 0.2s;
}

.cs-skin-border ul span {
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
}

.cs-skin-border .cs-options li span:hover,
.cs-skin-border li.cs-focus span {
	background: #F6F6F8;
}
