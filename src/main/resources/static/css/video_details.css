@charset "utf-8";
body {
	line-height: normal;
	background: #f5f5f5;
}

.clearfix {
	clear: both
}

.position .content_box {
	width: 1170px;
	margin: 0 auto
}

.position .content_box h1 {
	font-size: 12px;
	color: #999999;
	font-weight: normal;
	line-height: 50px
}

.position .content_box h1 a {
	color: #999999
}

.video_box {
	background-color: #292929;
	padding-bottom: 30px
}

.video_box .content_box {
	width: 1170px;
	margin: 0 auto;
}

.video_box .content_box .title {
	padding-top: 29px
}

.video_box .content_box .title .left_box {
	float: left
}

.video_box .content_box .title .right_box {
	float: right;
	padding-top: 13px
}

.video_box .content_box .title .left_box h1 {
	font-size: 26px;
	color: #fff;
	height: 28px;
	line-height: 28px;
	font-weight: normal;
}

.video_box .content_box .title .right_box a {
	text-decoration: none
}

.video_box .content_box .title .right_box .volume a {
	background-image: url("https://static.fangxiaoer.com/web/images/video/player.png");
	background-repeat: no-repeat;
	background-position: left center;
	font-size: 14px;
	height: 14px;
	line-height: 14px;
	color: #999999;
	font-weight: normal;
	padding-left: 23px
}

.video_box .content_box .title .right_box .volume {
	float: left;
	margin-right: 18px
}

.video_box .content_box .title .right_box .share {
	float: left;
	margin-right: 18px
}

.video_box .content_box .title .right_box .share a {
	background-image: url("https://static.fangxiaoer.com/web/images/video/share.png");
	background-repeat: no-repeat;
	background-position: left center;
	font-size: 14px;
	height: 14px;
	line-height: 14px;
	color: #999999;
	font-weight: normal;
	padding-left: 22px
}

.video_box .content_box .title .right_box .edit {
	float: left
}

.video_box .content_box .title .right_box .edit h1 {
	font-size: 14px;
	height: 14px;
	line-height: 14px;
	color: #999999;
	font-weight: normal;
	display: inline-block
}

.video_box .content_box .title .right_box .time {
	float: left;
}

.video_box .content_box .title .right_box .time h1 {
	font-size: 14px;
	height: 14px;
	line-height: 14px;
	color: #999999;
	font-weight: normal;
	display: inline-block;
	border-left: 1px solid #999999;
	padding-left: 10px;
	margin-left: 10px
}

.video_box .content_box .content {
	margin-top: 19px;
	background-color: #1f1f1f
}

.video_box .content_box .content .left_box {
	float: left
}

.video_box .content_box .content .right_box {
	float: right;
	width: 264px;
	margin-right: 14px
}

.video_box .content_box .content .right_box .name h1 {
	font-size: 15px;
	height: 15px;
	line-height: 15px;
	color: #ffffff;
	padding-top: 15px;
	padding-bottom: 15px;
	border-bottom: 1px solid #333333;
}

.video_box .content_box .content .right_box .list {
	padding-top: 12px
}

.video_box .content_box .content .right_box .list .pic {
	float: left;
	border: 2px solid #1f1f1f
}

.video_box .content_box .content .right_box .list .pic img {
	width: 104px;
	height: 60px
}

.video_box .content_box .content .right_box .list .text {
	float: right;
	width: 147px
}

.video_box .content_box .content .right_box .list .text .name_sun h1 {
	font-size: 12px;
	color: #999999;
	line-height: 12px;
	height: 12px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-top: 6px;
	font-weight: normal
}

.video_box .content_box .content .right_box .list .text .name_sun a {
	text-decoration: none
}

.video_box .content_box .content .right_box .list .text .time h1 {
	font-size: 12px;
	color: #999999;
	line-height: 14px;
	height: 14px;
	background-image: url("https://static.fangxiaoer.com/web/images/video/time02.png");
	background-repeat: no-repeat;
	background-position: left center;
	padding-left: 19px;
	margin-top: 22px;
	font-weight: normal
}

.video_box .content_box .content .right_box .list ul li {
	margin-bottom: 9px;
	cursor: pointer
}

.video_box .content_box .content .right_box .list ul li:hover .pic {
	border: 2px solid #ff5200
}

.video_box .content_box .content .right_box .list ul li:hover .name_sun h1 {
	color: #ff5200
}

.video_information .content_box {
	width: 1170px;
	margin: 0 auto;
	padding-top: 30px
}

.video_information .content_box .name h1 {
	font-size: 26px;
	color: #333333;
	font-weight: normal;
	height: 26px;
	line-height: 26px
}

.video_information .content_box .name {
	width: 1114px;
	margin: 0 auto
}

.video_information .content_box .type {
	width: 1114px;
	margin: 0 auto;
	margin-top: 20px
}

.video_information .content_box .type ul li {
	float: left;
	margin-right: 30px
}

.video_information .content_box .type ul li h1 {
	font-size: 14px;
	line-height: 14px;
	height: 14px;
	font-weight: normal;
	color: #666666;
}

.video_information .content_box .information {
	width: 100%;
	margin: 0 auto;
	margin-top: 22px
}

.video_information .content_box .information p {
	font-size: 14px;
	line-height: 20px;
	color: #666;
	padding-right: 43px;
}

.activity {
	/*margin-top: 27px;*/
}

.activity .content_box {
	width: 1200px;
	margin: 0 auto;
	background: #fff;
	/*box-shadow: 0px 0px 20px 0px rgba( 163, 163, 166, 0.3);*/
}
.activity .content_box:last-child {
	padding-bottom:47px ;
	/*box-shadow: 0px 0px 20px 0px rgba( 163, 163, 166, 0.3);*/
}

.activity .content_box .title {
	width: 1170px;
	margin: 0 auto;
	    padding-top: 26px;
    padding-bottom: 20px;
}

.activity .content_box .title h1 {
	line-height: 26px;
	font-size: 26px;
	color: #222;
	font-weight: normal;
	float: left;
	font-weight: bold;
	
}

.activity .content_box .title a {
	float: right;
	font-size: 14px;
	color: #666666;
	line-height: 20px;
	text-decoration: none;
	padding-right: 16px;
	background-image: url("https://static.fangxiaoer.com/web/images/video/more.png");
	background-position: right center;
	background-repeat: no-repeat;
	margin-right: 6px;
	    margin-top: 6px;
}

.activity .content_box .title a:hover {
	color: #ff5200
}

.activity .content_box .title a:hover {
	color: #ff5200 !important;
}

.activity .content_box .title a:hover {
	background-image: url(https://static.fangxiaoer.com/web/images/video/more02.png) !important;
}

.activity .content_box .content {
	width: 1170px;
	margin: 0 auto;

}



.activity .content_box .content ul li .photo {
	position: relative
}

.activity .content_box .content ul li .photo .pic {
	width: 282px
}

.activity .content_box .content ul li .photo .pic img {
	width: 282px;
	height: 158px
}

.activity .content_box .content ul li .photo .time {
	position: absolute;
	right: 0px;
	bottom: 0px;
	background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: -o-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	width: 100%;
	padding-left: 0px;
	padding-right: 0px;
	border-radius: 0px
}

.activity .content_box .content ul li .photo .time h1 {
	    font-size: 12px;
    color: #fff;
    font-weight: normal;
    line-height: 33px;
    padding-right: 6px;
    /* background-image: url(https://static.fangxiaoer.com/web/images/video/time03.png); */
    background-position: left center;
    background-repeat: no-repeat;
    text-align: right;
}

.activity .content_box .content ul li .name {
	padding-top: 22px;
    padding-bottom: 10px;
}

.activity .content_box .content ul li .name h1 {
	    font-size: 14px;
    height: 14px;
    line-height: 14px;
    font-weight: normal;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
}

.activity .content_box .content ul li .name h1:hover {
	color: #ff5200
}

.activity .content_box .content ul li .photo .pic {
	overflow: hidden;
}

.activity .content_box .content ul li .photo .pic img {
	transition: all 0.6s;
}

.activity .content_box .content ul li .photo .pic:hover img {
	transform: scale(1.1);
}

.activity .content_box .content ul li a {
	text-decoration: none
}

.activity .content_box .content ul li {
	margin-right: 14px;
	    float: left;
    width: 282px;
    	margin-bottom: 4px;
}

.activity .content_box .content ul li:nth-child(4) {
	margin-right: 0px
}

.qiye {
	margin-bottom: 28px
}

.share {
	position: relative
}

.share .share_tan_main {
	position: absolute;
	left: -70px;
	top: 38px;
	width: 194px;
	height: 178px;
	background-color: #fff;
	z-index: 999
}

#qrcode {
	float: none;
	margin: 0px;
	margin-top: 20px
}

#qrcode canvas {
	display: block;
	margin: 0 auto
}

.share_tan_main h1 {
	font-size: 14px;
	color: #333;
	text-align: center;
	height: 14px;
	line-height: 14px;
	font-weight: normal;
	padding-top: 13px
}

.share_tan_main h2 {
	font-size: 12px;
	color: #999;
	text-align: center;
	height: 12px;
	line-height: 12px;
	font-weight: normal;
	padding-top: 10px
}

.share_tan_main img {
	position: absolute;
	top: -5px;
	left: 50%;
	margin-left: -6px
}

.ewm {
	padding-left: 0 !important;
	background: none !important;
}

.prism-player .prism-big-play-btn .outter {
	width: 120px !important;
	height: 120px !important;
	border: 0 !important;
}

.prism-player .prism-big-play-btn {
	background-image: url("https://static.fangxiaoer.com/web/images/video/player02.png") !important;
	width: 120px !important;
	height: 120px !important;
	left: 50% !important;
	bottom: 50% !important;
	margin-left: -60px;
	margin-bottom: -60px;
}

.prism-cc-btn {
	display: none !important;
}

.prism-setting-btn {
	display: none !important;
}

.prism-controlbar-bg {
	height: 50px !important;
}

.prism-player .prism-controlbar {
	height: 50px !important;
}

.prism-progress {
	bottom: auto !important;
	top: 0 !important;
}

.prism-play-btn {
	margin-top: 20px !important;
}

.prism-player .prism-fullscreen-btn {
	background-image: url("https://static.fangxiaoer.com/web/images/video/screen.png") !important;
	width: 17px !important;
	height: 17px !important;
	margin-top: 20px !important;
	margin-right: 30px !important;
}

.prism-controlbar-bg {
	background-color: rgba(0, 0, 0, 0.7) !important;
}

.prism-player .prism-volume .volume-icon {
	background-image: url("https://static.fangxiaoer.com/web/images/video/voice.png") !important;
	width: 22px !important;
	height: 19px !important;
	background-size: 100% !important;
}

.prism-volume {
	margin-right: 33px !important;
	margin-top: 17px !important;
}

.short-horizontal {
	display: none !important;
}

.long-horizontal {
	display: none !important;
}

.prism-player .prism-play-btn {
	background-image: url("https://static.fangxiaoer.com/web/images/video/player03.png") !important;
	width: 16px !important;
	height: 18px !important;
	background-size: 100% !important;
}

.prism-player .prism-play-btn.playing {
	background-image: url("https://static.fangxiaoer.com/web/images/video/suspend.png") !important;
	width: 16px;
	height: 18px;
	background-size: 100%
}

.prism-player .prism-fullscreen-btn.fullscreen {
	background-image: url("https://static.fangxiaoer.com/web/images/video/narrow.png");
	width: 17px;
	height: 17px;
	margin-top: 20px !important;
	margin-right: 30px !important;
}

.position .content_box h1 a:hover {
	color: #ff5200
}

.prism-cover {
	background-size: 100% !important;
}

#MEIQIA-BTN-HOLDER {
	z-index: 9!important
}
.main_info{
	height: 50px;
    line-height: 50px;
}
.main_info .touxiang{
	    width: 50px;
    height: 50px;
    border-radius: 50%;
    float: left;
    background: url(https://static.fangxiaoer.com/m/static/images/video/noagent.png) no-repeat center;
    background-size: 100% 100%;
    margin-right: 12px;
}
.touxiang img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}
.infoname{
	font-size: 22px;
font-weight: bold;
color: #222222;
line-height: 50px;
}
.hot_info{
	margin-top: 25px;
	font-size: 14px;
	padding-right: 43px;
}

.hot_info a{
    font-size: 14px;
    color: #666666;
    line-height: 66px;
    text-decoration: none;
    padding-right: 16px;
    background-image: url(https://static.fangxiaoer.com/web/images/video/more.png);
    background-position: right center;
    background-repeat: no-repeat;
    margin-right: 6px;
    width: 100%;
    display: block;
}
.dz{
	margin-top: 62px;
	margin: auto;
	text-align: center;
}
.dz span{
	
font-size: 14px;
color: #666666;
display: block;
margin-top: 8px;
}
.dzimg{
	margin: auto;
    margin-top: 22px;
    width: 40px;
    cursor: pointer;
}
.dzimg img{
	width: 40px;
	height: 40px;
}


.info{
	height: 30px;
	line-height: 30px;
}
.info .touxiang {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    float: left;
    background: url(https://static.fangxiaoer.com/m/static/images/video/noagent.png) no-repeat center;
    background-size: 100% 100%;
    margin-right: 9px;
    
}
.touxiang img{
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.video_r{
	float: right;
	    margin-right: 8px;
	        margin-top: 4px;
	
}
.video_ll {
    width: 16px;
    height: 12px;
    background-size: 100%;
    font-style: normal;
    float: left;
    margin-right: 4px;
        margin-top: 5px;
}
.mr14{
	    margin-right: 14px;
	
}

.video_r span {
    font-size: 14px;
    color: #535353;
}
.video_dz {
  	width: 17px;
	height: 13px;
    background-size: 100%;
    margin-left: 4px;
    font-style: normal;
    float: left;
    margin-right: 4px;
        margin-top: 4px;
}
.video_r img {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    display: block;
}
.span_div {
    display: block;
    float: left;
    font-size: 14px;
    line-height: 22px;
}
#search2017 {
    height: 66px;
    background: #f5f5f5;
    min-width: 1170px;
}
#search2017 .search .ac_input{
	border: 0;
}
.position{
	background: #fff;
}
.video_information{
	margin-bottom: 66px;
}
