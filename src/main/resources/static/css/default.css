@charset "utf-8";
a {
    color: #333;
}

a:hover {
    color: #ff5200;
}

.crumbs {
    margin-top: 10px;
}

.flash li {
    position: relative
}

sup {
    position: absolute;
    bottom: 0px;
    left: 0px;
    z-index: 10;
}

.container {
}

.left-w .news {
    border-bottom: 1px solid #ededed;
    /*padding-bottom: 34px;*/
    /*padding-top: 10px;*/
    height: 190px
}

.left-w .news a:hover {

}
a:active{
    text-decoration: none
}
.left-w .news:hover {
    background: #eee
}

.left-w .news p {
    font-size: 22px;
    line-height: 40px;
    /* margin-left: 10px; */
}

.left-w .news p a {
    color: #343434;
}

.left-w .news p s {
    color: #ff5200;
    text-decoration: initial
}

.left-w .news p a:hover {
    color: #ff5200;
}

.left-w .news p span {
    font-size: 14px;
    color: #999999;
}

.left-w .news p span i {
    color: #ff5200;
    float: right;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/yee.png) no-repeat 2px center;
    padding-left: 27px;
    margin-right: 10px;
}

.left-w .news .left {
    /*width: 251px;*/
    /*height: 184px;*/
    float: left;
    position: relative;
    margin-left: 20px;
    margin-top: 24px;
}

.left-w .news .left img {
    width: 251px;
    height: 184px;
}

.left-w .news .right {
    width: 430px;
    height: 177px;
    position: relative;
    font-size: 14px;
    line-height: 36px;
    color: #666;
    float: left;
    padding: 5px 0 0 20px;
    font-size: 14px;
}

.left-w .news .right a {
    /* position: absolute; */
    /* right: 0; */
    /* bottom: 0; */
    font-size: 12px;
    color: #08c;
}

.left-w .news .right a.content {
    color: #333333;
    /* position: relative; */
    font-size: 14px;
    /* width: 610px; */
    /* overflow: hidden; */
    display: inline-block;
    /*height: 153px;*/
    overflow: hidden;
    text-overflow: ellipsis;
    /*height: 56px;*/
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    line-height: 28px;
}
a:focus{
    text-decoration: none;
}
.right-w {
    padding-top: 27px;
}

.right-w .title {
    font-size: 18px;
    color: #333;
    height: 30px;
    border-bottom-width: 4px;
    border-bottom: solid #f6f6f6;
    margin-bottom: 1px;
    padding-left: 5px;
}

.hot li {
    font-size: 12px;
    line-height: 36px;
    color: #666;
    background: url(
    http: / / static . fangxiaoer . com /web/ images /ico/ sign / news_d . gif) no-repeat left;
    padding-left: 15px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.hot li.news_list_tit {
    font-weight: bold;
    font-size: 15px;
    background: none;
}

.right-w .flash {
    margin: 0px 0 18px 0;
}

.right-w .video .shipin {
    font-size: 14px;
    line-height: 30px;
    color: #666;
}

.right-w .video .shipin a {
    position: relative;
    /* display:block; *//*

    overflow:hidden; *//* white-space:nowrap; *//* text-overflow:ellipsis; */
}

.right-w .video .shipin img {
    width: 100%;
    height: auto;
}

.right-w .video .shipin i img {
    width: 70px;
    height: 69px;
    position: absolute;
    left: 112px;
    top: 81px;
}

.right-w .video .shipin2 {
    font-size: 12px;
    line-height: 30px;
    color: #666;
    margin: 23px 0 8px 0;
}

.right-w .video .shipin2 .shipinimg {
    width: 160px;
    height: 105px;
    float: left;
}

.right-w .video .shipin2 .shipinimg img {
    width: 160px;
    height: 105px;
}

.right-w .video .shipin2 p {
    padding-left: 10px;
    float: left;
    margin-top: -7px;
    width: 122px;
}

a.qiandao {
    width: 260px;
    height: 52px;
    margin: 20px auto;
    background: url(
    http: / / static . fangxiaoer . com /web/ images /ico/ sign / qiandao_1 . gif) no-repeat;

    display: block;
    color: #5f6062;
    text-decoration: none;
    padding-top: 8px;
    line- height: 24px;
}

a.qiandao p {
    margin: 0px 0 0 182px;
}

a.hui {
    background: url(
    http: / / static . fangxiaoer . com /web/ images /ico/ sign / qiandao_2 . gif) no-repeat;
}

@media (min-width: 1200px) {
    .left-w .news .right {
        width: 610px;
        /* overflow: hidden; */
        /* text-overflow:

        ellipsis; */
        /* height: 162px; */
    }
}

/*楼盘专题*/
.right-w .hot {
    margin-bottom: 25px;
}

.loushi {
    padding-top: 12px;
    padding-bottom: 10px;
    width: 960px;
    margin: 0 auto;
    overflow: hidden;
    zoom: 1;
}

.loushi .title {
    width: 100%;
    border-bottom: 1px #dddddd solid;
    margin-bottom: 22px
}

.loushi .title h1 {
    height: 40px;
    line-height: 40px;
    font-size: 20px;
    font- weight: normal;
    color: #666
}

.loushi .lou_img {
    width: 177px;
    position: relative;
    float: left;
    margin- left: 18px;
    margin-bottom: 18px;
}

.loushi .lou img {
    width: 100%;
    height: 154px;
}

.loushi .lou_pos {
    position: absolute;
    bottom: 0;
    left: 0;
}

.loushi .lou_bg {
    width: 100%;
    height: 28px;
    background-color: #000;
    filter: alpha(opacity=50); /* IE */
    -moz-opacity: 0.5; /* Moz + FF */
    opacity: 0.5;
}

.loushi .lou_title {
    width: 170px;
    line-height: 28px;
    color: #fff;
    font- size: 14px;
    padding-left: 5px; /* overflow:hidden; */
    white-space: nowrap;
    /* text-

    overflow:ellipsis; */
}

@media (min-width: 1200px) {
    .loushi {
        width: 1200px;
    }

    .loushi .lou_img {
        width: 210px;
        margin-left: 37px;
    }
}

.left i {
    position: absolute;
    top: 0;
    left: 0;
    width: 23px;
    height: 26px;
    background: url( https://static.fangxiaoer.com/web/images/sy/video/ico_video.png)
}

.warning {
    font-size: 14px;
    line-height: 30px;
    padding: 40px 0 0 80px;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/icon-failure.png) no-repeat 24 px 48 px;
}

.warning ul {
    margin: 0;
}

.warning li {
    line-height: 30px;
    padding-left: 10px;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/news_d.gif) no-repeat left;
}

.warning i {
    color: #ff5200
}


/*右侧*/
#right {
    float: left;
    width: 250px;
    margin-left: 25px
}

.saleHouse {
    font-size: 18px;
    text-align: center;
    background: url(https://static.fangxiaoer.com/global/imgs/sale/release/speediness.jpg) no-repeat top left;
    /* margin-top: 20px; */
    width: 100%;
    height: 59px;
    padding-top: 156px;
}

.saleHouse div {
}

.saleHouse a {
    width: 165px;
    height: 36px;
    line-height: 36px;
    background: #fff;
    display: block;
    margin: 0 auto;
    color: #ff5200;
    border-radius: 3px;
    /* border: 1px solid #ff5200; */
}

.history {

    font-size: 12px;

    padding-top: 0;

    margin-top: 24px;

    background: #fff;
}

.history dt {
    border-bottom: 1px solid #eee;
    line-height: 44px;
    font-size: 14px;
    margin-bottom: 6px;
    padding: 0 10px;
}

.history dd {
    padding: 2px 10px;
}

.history dd a {
    line-height: 24px;
}

.history dd a span {
    display: inline-block;
    width: 100px;
}

.history dd a p {
    display: inline-block;
    width: 70px;
}

.history dd a b {
    display: inline-block;
    width: 58px;
    text-align: right;
    font-weight: 400;
    color: #ff5200;
}

.recommend {
    border: 1px solid #eaeaea;
    margin-top: 24px;
    background: #fff;
    /* padding: 0 16px; */
    margin-bottom: 24px;
    /* position: relative; */
    box-shadow: 0px 2px 6px 2px #c3c3c34a;
}

.recommend .title {
    display: none
}

.recommend dt {
    line-height: 50px;
    font-size: 18px;
    font-weight: 400;
    border-bottom: 1px solid #eaeaea;
    padding: 0 16px;
}

.recommend dt span {
    display: inline-block;
    width: 4px;
    height: 18px;
    margin-bottom: -1px;
    border-radius: 50px;
    background: #ff5200;
    margin-right: 8px;
}

.recommend .hover {

}

.recommend dd {
    overflow: hidden;
    font-size: 12px;
    color: #666;
    height: 36px;
    line-height: 36px;
    padding: 0 16px;
}

.recommend dd > div a {
    text-decoration: none;
}

.recommend dd > div span {
    display: inline-block;
    width: 103px;
    color: #444;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.recommend dd > div .hover {
    color: #ff5200;
}

.recommend dd > div p {
    display: inline-block;
    width: 30px;
    color: #cecece;
    font-size: 14px;
    font-family: Arial;
}

.recommend dd > div b {
    display: inline-block;
    text-align: right;
    width: 76px;
    color: #ff5200;
    font-weight: 400;
}

.recommend dd > a {
    overflow: hidden;
    display: block;
    text-decoration: none;
}

.recommend dd > a img {
    width: 80px;
    height: 60px;
    display: block;
    float: left;
    margin: 4px 20px 10px 20px;
}

.recommend dd > a div {
    float: left;
}

.recommend dd > a div p {
    line-height: 20px;
}

.left-w {
    width: 895px;
}

.zsfw ul {
    margin: 0
}

.zsfw ul li div select, .zsfw ul li input {
    margin: 0;
    line- height: 28px;
    padding: 0;
    border-radius: 0;
}

.zsfw .btn {
    padding: 0;
    background: #fff;
}

.recommend i {
    position: absolute;
    width: 70px;
    height: 69px;
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/play.png);
    top: 50%;
    left: 87px;
    margin-top: -35px;
}


.border img {
    float: left;
    margin-left: 245px;
}

.border p {
    float: left;
    line-height: 36px;
    margin-left: 14px;
    font-size: 20px;
    color: #333333;
}

.border {
    border-bottom: 1px #e0e0e0 dashed
}

.Recommend span {
    display: inline-block;
    width: 5px;
    height: 20px;
    background-color: #333333;
    margin-top: 5px;
    vertical-align: bottom;
    margin-right: 12px
}

.Recommend {
    font-size: 20px;
    margin-top: 10px;
    font-weight: bold;
    /* line-height: 24px; */
    padding-bottom: 10px;
    border-bottom: 1px solid #ededed;
    margin-bottom: 0;
    padding-left: 20px;
}


/*咨询改版*/
.redTab {
    height: 50px;
    line-height: 50px;
    overflow: hidden;
    border-bottom: 1px solid #ededed;
}

.redTab ul {
    overflow: hidden;
    margin: 0;
}

.redTab li {
    width: 20% !important;
    text-align: center;
    float: left;
    font-size: 16px;
    color: #333333;
    height: 50px;
    line-height: 50px;
}

.sel {
    color: #ff5200 !important;
    font-weight: bold;
}

body {
    background-color: #f4f4f4;
}

.left-w {
    background-color: #ffffff;
    box-shadow: 0px 2px 6px 2px #c3c3c34a;
}

.zsfw {
    padding: 12px;
    background: #fff;
    padding-top: 4px;
    margin: 0px 0 20px 0;
    border: 1px solid #eaeaea;
    padding-left: 0px;
    padding-right: 0;
    box-shadow: 0px 2px 6px 2px #c3c3c34a;
}

.redTabG {
    display: block;
    width: 42px;
    height: 2px;
    background-color: #ff5200;
    margin: auto;
    margin-top: -2px;
}

.left-w .news:hover {
    background: #ffffff;
}

.zsfw h1 {
    font-size: 18px;
    color: #000;
    margin: 0;
    font-weight: 400;
    border-bottom: 1px solid #eaeaea;
    line-height: 43px;
    margin-bottom: 12px;
    padding-left: 12px;
}
.swiper-slide a {
    width: 100%;
    height: 300px;
    display: block;
}
.zsfw ul {
    margin: 0;
    padding-left: 12px;
}
.left-w .news .left img {
    width: 210px;
    height: 140px;
    border: 1px solid #ededed;
}
.rhead{
    font-size: 20px !important;
    color: #333333 !important;
    font-weight: bold;
    display: inline-block;
    margin-top: 14px;
}
.rhead:hover{
    color: #ff5200 !important;
}
.nCon{
    margin-top: 15px;
    height: 56px;
}
.nDet{
    float: right;
    font-size: 14px !important;
    color: #0068b7 !important;
    margin-top: -35px;
}
.nlyun{

    margin-top: -1px;

    font-size: 14px !important;

    color: #999999;
}
.nBrand{
    width: 41px;
    display: inline-block;
    height: 36px;
    background: url("https://static.fangxiaoer.com/web/images/sy/house/house/nBrand.png") no-repeat;
    position: absolute;
    /* text-align: center; */
    padding-left: 4px;
    line-height: 16px;
    padding-top: 2px;
    font-size: 14px;
    color: #ffffff;
    margin-top: -3px;
}
#search2017 {
    height: 66px;
    background: #f4f4f4;
    min-width: 1170px;
}
.carousel {
    position: relative;
    margin-bottom: 0px;
    line-height: 1;
}
.video_box .title {
    background-color: #ffffff;
}
.nPage{
    width: 895px;
}
#newsAD1_guanggao{
    box-shadow: 0px 2px 6px 2px #c3c3c34a;
}
.video_box {
    box-shadow: 0px 2px 6px 2px #c3c3c34a;
    position: relative;
}
#newsAD2_guanggao{
    box-shadow: 0px 2px 6px 2px #c3c3c34a;

}
.none{
    margin-top: 20px;
    margin-bottom: 20px;
}
.noneImg{
    margin-top: 20px;
}
.detx{
    margin-top: -10px;
}
.redTab1 i{
    width: 17px;
    height: 21px;
    background: url(https://static.fangxiaoer.com/web/images/huohuo.png) no-repeat;
    background-size: 100% 100%;
    float: left;
    margin-top: 13px;
    margin-left:61.5px;
}
.redTab1 span{
    float: left;
    margin-left: 5px;
}
.redTab1 .redTabG{
    float: none;
    margin: -2px auto !important;
}