@charset "utf-8";

/*
制作人：张琪
修改时间：2022年8月9日
用处：sy站我要卖房通用文件
*/
.w1170 {
	width: 1170px;
	margin: 0 auto;
}

.cont_1 {
	height: 196px;
	width: 1170px;
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/0819.png) top center;
	background-size: 100% 100%;
	padding-top: 49px;
}

.cont_2 {
	margin-top: 48px;
}

.cont_3 {
	height: 170px;
	background: #FFFFFF;
	box-shadow: 0px 3px 6px 1px rgba(214, 214, 214, 0.1600);
	opacity: 1;
	border: 1px solid #F4F4F4;
	margin-top: 49px;
	padding-top: 27px;
}

.cont_4 {
	display: none;
}

.cont_5 {
	height: 247px;
	background: #FFFFFF;
	box-shadow: 0px 3px 6px 1px rgba(214, 214, 214, 0.1600);
	opacity: 1;
	border: 1px solid #F4F4F4;
	padding-top: 33px;
	margin-top: 50px;
}

.cont_6 {
	margin-top: 48px;
}

.cont_title {
	height: 30px;
	font-size: 30px;
	font-family: PingFang SC-Heavy, PingFang SC;
	font-weight: 800;
	color: #FF5200;
	line-height: 30px;
	text-align: center;
}

.cont_1 h4 {
	height: 34px;
	font-family: PingFang SC-Heavy, PingFang SC;
	text-align: center;
	margin-bottom: 45px;
	font-size: 34px;
	font-weight: 800;
	color: #FFFFFF;
}

.cont_1_main {}

.cont_1_input {
	margin-left: 380px;
	overflow: hidden;
}

.cont_1_input input {
	width: 408px;
	height: 53px;
	background: #FFFFFF;
	border-radius: 6px 0px 0px 6px;
	padding-left: 18px;
	float: left;
	border: none;
	display: none;
}

.cont_1_btn:hover {
	color: #fff !important;
	text-decoration: none !important;
}

.cont_1_btn {
	width: 200px;
	height: 53px;
	background: #FC5112;
	border-radius: 6px;
	opacity: 1;
	line-height: 53px;
	text-align: center;
	font-size: 20px;
	font-family: PingFang SC-Bold, PingFang SC;
	font-weight: bold;
	color: #FFFFFF;
	float: left;
	cursor: pointer;
}

.cont_1_btn1 {
}

.cont1_btn_a {
	width: 488px;
	margin: 0 auto;
	overflow: hidden;
}

.cont_1_btn2 {
	margin-left: 88px;
	width: 200px;
	border-radius: 6px;
	background: #FF730B;
}

.cont_1_msg {
	margin-left: 257px;
	margin-top: 6px;
	display: none;
}

.cont_1_msg p {
	height: 20px;
	font-size: 14px;
	font-family: PingFang SC-Bold, PingFang SC;
	color: #FFFFFF;
	float: left;
	line-height: 16px;
}

.cont_1_msg a {
	color: #FF5200;
}

.check_span {
	display: block;
	width: 16px;
	height: 16px;
	float: left;
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/2.png) top center;
	margin-right: 4px;
	cursor: pointer;
}

.cont_1_msg .check_span2 {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/3.png) top center;
}

.cont_2_ul {
	overflow: hidden;
	margin-left: 148px;
}

.cont_2_li {
	width: 240px;
	height: 92px;
	float: left;
	font-weight: 800;
	color: #FFFFFF;
	margin-right: 104px;
}

.cont_2_li:nth-child(3) {
	margin-right: 0;
}

.cont_2_li a {
	text-align: center;
	display: block;
	color: #FFFFFF;
	width: 100%;
	height: 100%;
	text-decoration: none;
}

.cont_2_icon {}

.cont_2_icon1 {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/4.png) top center;
	;
	background-size: 100% 100%;
}

.cont_2_icon2 {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/5.png) top center;
	;
	background-size: 100% 100%;
}

.cont_2_icon3 {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/6.png) top center;
	;
	background-size: 100% 100%;
}

.cont_2_icon1:hover {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/42.png) top center;
	;
	background-size: 100% 100%;
}

.cont_2_icon2:hover {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/52.png) top center;
	;
	background-size: 100% 100%;
}

.cont_2_icon3:hover {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/62.png) top center;
	;
	background-size: 100% 100%;
}

.cont_3_ul {
	margin-left: 58px;
	margin-top: 43px;
	height: 78px;
}

.cont_3_icon {
	display: block;
	width: 78px;
	height: 78px;
	margin-right: 15px;
	float: left;
	margin-top: -14px;
}

.cont_3_icon_1 {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/7.png) top center;
	background-size: 100% 100%;
}

.cont_3_icon_2 {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/8.png) top center;
	;
	background-size: 100% 100%;
	;
}

.cont_3_icon_3 {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/9.png) top center;background-size: 100% 100%;
}

.cont_3_li {
	float: left;
	width: 33%;
	height: 78px;
}

.cont_3_li h4 {
	height: 18px;
	font-size: 18px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: #111111;
	line-height: 18px;
	margin-bottom: 8px;
}

.cont_3_li p {
	font-size: 14px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: #707071;
	line-height: 22px;
}

.cont_4_bnt {
	margin: 47px auto 0 auto;
	width: 531px;
	height: 95px;
	display: block;
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/10.png) top center;
	background-size: 100% 100%;
}

.cont_5_ul {
	overflow: hidden;
	margin-left: 25px;
	margin-top: 36px;
}

.cont_5_ul li+li {}

.cont_5_li {
	width: 50%;
	float: left;
}

.cont_5_Q {
	margin-bottom: 11px;
	font-size: 18px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: #111111;
	line-height: 28px;
}

.cont_5_Q_icon {
	display: block;
	width: 28px;
	height: 28px;
	float: left;
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/11.png) top center;
	background-size: 100% 100%;
	margin-right: 10px;
}

.cont_5_A {}

.cont_5_A_icon {
	display: block;
	width: 28px;
	height: 28px;
	float: left;
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/12.png) top center;
	;
	background-size: 100% 100%;
	margin-right: 10px;
	;
}

.cont_5_A div {
	width: 468px;
	font-size: 14px;
	font-family: PingFang SC-Regular, PingFang SC;
	font-weight: 400;
	color: #797878;
	line-height: 20px;
	-webkit-background-clip: text;
	float: left;
}


.cont_5_A div p {
	line-height: 18px;
	font-size: 12px;
	margin-bottom: 3px;
}

.cont_6_1 {
	margin-top: 43px;
	overflow: hidden;
}

.cont_6_1_li {
	float: left;
	width: 266px;
	height: 366px;
	background: #F4F4F4;
	border-radius: 0px 0px 0px 0px;
	opacity: 1;
	margin-right: 35px;
}

.cont_6_1_li a:hover {
	text-decoration: none;
}

.cont_6_1 li:last-child {
	margin-right: 0;
}

.cont_6_1_li img {
	width: 266px;
	height: 266px;
}

.cont_6_1_li h5 {
	height: 16px;
	font-size: 16px;
	font-family: PingFang SC-Bold, PingFang SC;
	font-weight: bold;
	color: #111111;
	line-height: 16px;
	text-align: center;
	padding: 6px 0 10px 0;
}

.cont_6_1_li p {
	width: 255px;
	font-size: 12px;
	font-family: PingFang SC-Medium, PingFang SC;
	font-weight: 500;
	color: #666666;
	line-height: 18px;
	margin: 0 auto;
}

.cont_6_2 {
	margin: 48px 0 16px 0;
	overflow: hidden;
}

.cont_6_2_li {
	width: 349px;
	height: 74px;
	float: left;
	margin-right: 61.5px;
	margin-bottom: 25px;
}

.cont_6_2 li:nth-child(3n) {
	margin-right: 0;
}

.cont_6_2_li a {
	display: block;
	width: 100%;
	height: 100%;
}

.cont_6_2_li a img {}

.IWSH_txt {
	color: #161616;
	padding-bottom: 30px;
	overflow: hidden;
}

.IWSH_txt h3 {
	font-family: Microsoft YaHei-Bold, Microsoft YaHei;
	font-weight: bold;
	line-height: 24px;
	text-align: center;
	margin: 19px auto 29px auto;
	font-size: 32px;
	color: #FF5200;
}

.title_icon {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/31.png) top center;
	display: block;
	width: 198px;
	height: 19px;
	background-size: 100% 100%;
	text-align: center;
	margin: 0 auto;
}

.title_icon2 {
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/31.png) top center;
	display: block;
	width: 270px;
	height: 25.9px;
	background-size: 100% 100%;
	text-align: center;
	margin: 0 auto;
}

.IWSH_txt h4 {
	margin-bottom: 18px;
	line-height: 40px;
	margin-top: 55px;
	height: 40px;
	background: #FF5200;
	border-radius: 35px 35px 35px 35px;
	text-align: center;
	width: auto;
	display: inline-block;
	padding: 0 21px;
	font-size: 20px;
	color: #fff;
}

.IWSH_txt h5 {
	font-family: Microsoft YaHei-Regular;
	font-size: 18px;
	font-weight: bold;
	margin: 5px 0;
	line-height: 18px;
	color: #FF5200;
	margin-bottom: 10px;
}

.IWSH_txt p {
	font-size: 16px;
	line-height: 24px;
	margin-bottom: 4px;
	color: #161616;
}

.IWSH_txt img {
	display: block;
	margin: 0 auto;
	width: 100%;
}

.IWSH_txt .mt20 {
	margin-top: 20px;
}

.IWSH_txt .colorFF5200 {
	color: #FF4848;
}

.IWSH_txt .txt_top {
	width: 846px;
	/* height: 58px; */
	background: rgb(255 82 0 / 5%);
	border-radius: 21px 21px 21px 21px;
	padding: 26px 46px;
	font-size: 18px;
	font-weight: 400;
	color: #111111;
	line-height: 34px;
	/* margin-bottom: 55px; */
}

.IWSH_txt .IWSH_txt_left {
	width: 938px;
	float: left;
}

.txt_bottom {
	width: 1128px;
	margin: 0 auto;
	background: #F5F5F5;
	padding: 16px 21px;
	border-radius: 20px;
}

.txt_bottom h5 {
	color: #161616;
	font-size: 18px;
	line-height: 18px;
	margin-bottom: 10px;
}

.txt_bottom p {
	font-size: 16px;
	line-height: 24px;
}

.rightQzQgBtn {
	margin-bottom: 6px;
	float: right;
	width: 170px;
	margin-left: 0;
	overflow: hidden;
}

.rightQzQgBtn a {
	width: 168px;
	line-height: 49px;
	padding: 0;
	margin-bottom: 14px;
	height: 49px;
	background: #FFF7EE;
	border: 1px solid #EAEAEA;
	font-size: 18px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #FF5200;
	display: block;
	text-align: center;
}

.rightQzQgBtn a i {
	display: none;
}

.rightQzQgBtn a:hover {
	background-color: #FFF7EE;
	text-decoration: none;
	color: #FF5200;
}

.IWSH_txt_right {
	float: right;
	margin-top: 90px;
}

.IWSH_txt_right_ul {
	width: 168px;
	opacity: 1;
	border: 1px solid #EDEDED;
	padding-bottom: 18px;
	margin-bottom: 16px;
}

.IWSH_txt_right_ul h4 {
	margin: 0;
	background: none;
	height: 45px;
	font-size: 18px;
	font-weight: 400;
	color: #111111;
	border-bottom: 1px solid #EDEDED;
	line-height: 0px;
	border-radius: 0;
	width: 100%;
	line-height: 45px;
	padding: 0;
	text-align: left;
	margin-bottom: 18px;
}

.IWSH_txt_right_ul h4 i {
	display: block;
	width: 4px;
	height: 16px;
	float: left;
	background: #ff5200;
	margin: 14px 6px 0 23px;
}

.IWSH_txt_right_ul li {}

.IWSH_txt_right_ul li a:hover {
	text-decoration: none;
}

.IWSH_txt_right_ul li a {
	display: block;
	font-size: 16px;
	margin-bottom: 10px;
}

.IWSH_txt_right_ul li a i {
	width: 16px;
	height: 16px;
	background: #FF5200;
	opacity: 1;
	display: inline-block;
	border-radius: 16px;
	line-height: 16px;
	text-align: center;
	color: #fff;
	margin-left: 18px;
	margin-right: 8px;
}

.icon1 {
	display: inline-block;
	width: 20px;
	height: 12px;
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/35.png) top center;
	background-size: 100% 100%;
	margin-right: 8px;
}

.icon2 {
	display: inline-block;
	width: 20px;
	height: 12px;
	background: url(https://static.fangxiaoer.com/web/images/sy/IWSellouse/35.png) top center;
	background-size: 100% 100%;
	;
	margin-left: 8px;
}

.suspensionIcon>a {
	display: none !important;
}

.suspensionIcon .n1,
.suspensionIcon .n6,
.suspensionIcon .n7 {
	display: block !important;
}

/* 实名认证弹窗 */
.tc_full {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgb(0 0 0 / 40%);
	z-index: 999999;
	display: none;
}
#login{z-index: 9999999;}
.tc_realname {
	width: 870px;
	height: 586px;
	background: #FFFFFF;
	border-radius: 16px 16px 16px 16px;
	opacity: 1;
	border: 1px solid #707070;
	position: fixed;
	left: 50%;
	top: 50%;
	margin-left: -435px;
	margin-top: -305px;
	padding-top: 24px;
	z-index: 999999999;
	display: none;
}

.tc_realname>h4 {
	font-size: 26px;
	font-family: Microsoft YaHei-Regular, Microsoft YaHei;
	font-weight: 400;
	color: #111111;
	line-height: 26px;
	text-align: center;
	margin-bottom: 6px;
}

.tc_realname>h5 {
	font-size: 12px;
	font-family: Microsoft YaHei-Regular, Microsoft YaHei;
	font-weight: 400;
	color: #FF2727;
	line-height: 26px;
	background: #FFF3ED;
	width: 335px;
	margin: 0 auto;
	text-align: center;
}

.tc_realname .realname_ul {}

.tc_realname .realname_ul li {
	/* height: 46px; */
	margin-bottom: 20px;
	position: relative;
}
.tc_realname .realname_ul li.err4 s{
    padding: 0 6px;
    color: #999;
}

.tc_realname .realname_ul li span {
	/* width: 70px; */
	height: 40px;
	font-size: 14px;
	font-family: Microsoft YaHei-Bold, Microsoft YaHei;
	font-weight: bold;
	color: #666666;
	line-height: 40px;
	float: left;
	margin-right: 15px;
	min-width: 80px;
}

.tc_realname .realname_ul li .span1{
    margin-left: -74px;
    width: 100%;
}
.tc_realname .realname_ul li i {
	color: #FF5200;
}

.tc_realname .realname_ul li input {
	padding-left: 15px;
	width: 265px;
	height: 30px;
	background: #F5F5F5;
	border-radius: 6px 6px 6px 6px;
	opacity: 1;
	border: 1px solid #E3E3E3;
}
.tc_realname .realname_ul li input.idtime{
    width: 100px;
}
.tc_realname .realname_ul li input#endtime{
    /* margin-left: 35px; */
}
.realname_main {
	margin: 17px 0 25px 202px;
}

.realname_main>h5 {
	height: 24px;
	font-size: 18px;
	font-family: Microsoft YaHei-Bold, Microsoft YaHei;
	font-weight: bold;
	color: #111111;
	margin-bottom: 20px;
}

.realname_btn {
	width: 249px;
	height: 55px;
	background: #FFC2A5;
	border-radius: 6px 6px 6px 6px;
	opacity: 1;
	text-align: center;
	margin: 0 auto;
	font-size: 20px;
	font-family: Microsoft YaHei-Regular, Microsoft YaHei;
	font-weight: 400;
	color: #FFFFFF;
	line-height: 55px;
}
.errMsg{
    height: 19px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FF5200;
    line-height: 14px;
    text-align: center;
    margin: 5px 0;
    display: none;
}
.realname_btn.hover {
	background: #FF5200;
	cursor: pointer;
}

/* 委托卖房 */
.trustHouse{
    padding-top: 46px;
}
.trustHouse>h3{
    height: 45px;
    font-size: 34px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #151515;
    line-height: 45px;
    text-align: center;
    margin-bottom: 59px;
}
.trustHouse .trustHouse_ul{
    margin-left: 199px;
}
.trustHouse .trustHouse_li{
    margin-bottom: 26px;
    height: 36px;
}
.trustHouse .trustHouse_li_l{
    float: left;
    line-height: 34px;
    margin-right: 15px;
    width: 70px;
}
.trustHouse .trustHouse_li_l i{/* width: 8px; */height: 24px;font-size: 18px;font-family: Microsoft YaHei-Regular, Microsoft YaHei;font-weight: 400;color: #FF5200;line-height: 34px;display: inline-block;float: left;}
.trustHouse .trustHouse_li_l span{
    height: 19px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #111111;
    line-height: 0px;
    font-weight: bold;
}
.trustHouse .trustHouse_li_r{
    float: left;
    position: relative;
}
.trustHouse .trustHouse_li_r span.right{
    position: absolute;
    right: 11px;
    top: 6px;
    color: #777777;
}
.trustHouse .trustHouse_li_r .ui-widget{}
.trustHouse .trustHouse_li_r  #searchSubName{}
.trustHouse .trustHouse_li_r input{
    width: 250px;
    height: 32px;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    border: 1px solid #CCCCCC;
    padding-left: 12px;
    float: left;
}
.trustHouse .li_r_4 div{
	line-height: 34px;
	float: left;
	margin-left: 10px;
	color: #FF5200;
	font-size: 14px;
}
.trustHouse .li_r_4 b{color: #FF5200;text-decoration: none;cursor: pointer;line-height: 34px;padding-left: 15px;font-weight: normal;cursor: pointer;}
.trustHouse .trustHouse_li_r .ui-widget>span{
	float: left;
	line-height: 34px;
	margin-left: 10px;
}
.trustHouse .newSubList{
    width: 263px;
    border: 1px solid #ccc;
    border-radius: 5px;
    line-height: 30px;
    height: auto;
    max-height: 263px;
    overflow-y: auto;
    display: none;
    position: absolute;
    z-index: 999999999;
    background: #fff;
    top: 33px;
}
.trustHouse .newSubList li{
	padding-left: 10px;
    cursor: pointer;
}
.trustHouse .errorBox{
    position: absolute;
    left: 0;
    top: 34px;
}
.trustHouse .errorBox .error{
    color: red;
}
.trustHouse .trustHouse_li_r .lphselect{
    width: 255px;
    background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 245px 16px no-repeat;
    border: 1px solid #ccc;
    float: left;
    height: 30px;
    border-radius: 4px;
    color: #555;
    padding: 2px 6px;
    line-height: 30px;
    cursor: pointer;
    position: relative;;
}
.trustHouse .trustHouse_li_r .lphselect span{
    display: block;
    width: 100%;
}
.trustHouse .trustHouse_li_r .lphselect ul{
    position: absolute;
    top: 36px;
    left: -1px;
    z-index: 88888;
    margin-left: 0px !important;
    max-height: 258px;
    overflow-y: auto;
    background: #fff;
    border: 1px solid #ccc;
    padding-left: 6px;
    border-top: none;
    border-radius: 4px;
    width: 98%;
}
.trustHouse .trustHouse_li_r .lphselect ul li{
	    clear: both;
    position: relative;
}
.trustHouse .trustHouse_li_r  .lphselect2,.trustHouse .trustHouse_li_r .lphselect3,.trustHouse .trustHouse_li_r .lphselect4{width: 110px;margin-left: 16px;background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 105px 16px no-repeat;}
.trustHouse .trustHouse_li_r .nolph{
    position: absolute;
    right: -180px;
    top: 8px;
}
.trustHouse .trustHouse_li_r .nolph p{
    display: inline-block;
}
.trustHouse .trustHouse_li_r .nolph span{
    display: inline-block;
    cursor: pointer;
    color: #0089ff;
}
.trustHouse .trustHouse_service{
    width: 1170px;
    background: #FFFFFF;
    box-shadow: 0px 1px 6px 1px #FFDAC9;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
}
.trustHouse .trustHouse_service .trustHouse_service_title{
    height: 74px;
    width: 96%;
    font-size: 20px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #FF5200;
    line-height: 74px;
    text-align: center;
    border-bottom: 1px solid rgb(255 82 0 / 21%);
    margin: 0 auto;
    position: relative;
}
.trustHouse .trustHouse_service .trustHouse_service_title span{
    position: absolute;
    right: 325px;
    top: 42px;
    font-size: 14px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #383838;
    line-height: 0px;
}
.trustHouse .trustHouse_service_main{
    overflow: hidden;
    padding: 21px 0 13px 0;
}
.trustHouse .trustHouse_service_main>li{
    width: 24.9%;
    float: left;
    border-right: 1px solid rgb(255 82 0 / 21%);
    height: 263px;
}
.trustHouse .trustHouse_service_main li h5{
    font-size: 16px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #626262;
    line-height: 16px;
    text-align: center;
    margin-bottom: 21px;
}
.trustHouse .trustHouse_service_main li h5 i{
    display: inline-block;
    background: rgb(255 82 0 / 21%);
    width: 33px;
    height: 33px;
    border-radius: 33px;
    text-align: center;
    line-height: 33px;
    color: #ff5200;
    margin-right: 7px;
}
.trustHouse .trustHouse_service_main .mian_p{
    width: 230px;
    margin: 0 auto;
}
.trustHouse .trustHouse_service_main .mian_p p{
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    line-height: 19px;
    margin-bottom: 15px;
}
.trustHouse .trustHouse_service_main .mian_p span{}
.trustHouse .trustHouse_service_main .mian_i{
    padding-left: 105px;
}
.trustHouse .trustHouse_service_main .mian_i div{
    margin-bottom: 27px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    line-height: 20px;
    line-height: 22px;
}
.trustHouse .trustHouse_service_main .mian_i i{
    display: inline-block;
    width: 23px;
    height: 18px;
    
background: url(https://static.fangxiaoer.com/web/images/sy/trustHouse/9.png) top center;;;
 background-size: 100% 100%;;
;;;;margin-right: 11px;;;;;
;;float: left;;;
}
.trustHouse .trustHouse_service_main .mian_i .mian_i_icon1{
    margin-top: 2px;
}
.trustHouse .trustHouse_service_main .mian_i .mian_i_icon2{background: url(https://static.fangxiaoer.com/web/images/sy/trustHouse/10.png) top center;;;
    background-size: 100% 100%;;
;margin-top: 2px;;
}
.trustHouse .trustHouse_service_main .mian_i .mian_i_icon3{background: url(https://static.fangxiaoer.com/web/images/sy/trustHouse/11.png) top center;;;
    background-size: 100% 100%;;
;;;;width: 17px;;;;;
;;;;;;;;;;height: 22.34px;;;;;;;;;;;
;;;;margin-right: 16px;;;;;
}
.trustHouse .trustHouse_service_main .mian_i .mian_i_icon4{
    width: 17px;
    height: 22.34px;
background: url(https://static.fangxiaoer.com/web/images/sy/trustHouse/12.png) top center;;;
    background-size: 100% 100%;;
;margin-right: 16px;;
}
.trustHouse .trustHouse_service_main .mian_i i span{}
.trustHouse .trustHouse_p_msg{
    width: 751px;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    margin-top: 49px;
    display: block;
    line-height: 18px;
}
.trustHouse .trustHouse_agree{/* padding-left: 289px; */line-height: 17px;font-size: 12px;margin: 0 auto;width: 170px;margin-top: 41px;}
.trustHouse .trustHouse_agree .agre{
    width: 15px;
    height: 15px;
    opacity: 1;
    display: inline-block;
    border-radius: 15px;
    float: left;
    margin-right: 8px;
    background: url(https://static.fangxiaoer.com/web/images/sy/trustHouse/3.png) top center;
	background-size: 100% 100%;
}
.trustHouse .trustHouse_agree .agre.hover{
	 background: url(https://static.fangxiaoer.com/web/images/sy/trustHouse/4.png) top center;
	background-size: 100% 100%;
}
.trustHouse .trustHouse_agree a{
    color: #FF5200;
}
.trustHouse .trustHouse_agree a:hover{
    text-decoration: none;
}
.trustHouse_btn{
    width: 391px;
    height: 67px;
    background: #FF5200;
    border-radius: 6px 6px 6px 6px;
    opacity: 1;
    text-align: center;
    line-height: 67px;
    margin:  0 auto;
    margin-top: 22px;
    margin-bottom: 67px;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
}
.trustHouse .trustHouse_service_main li:last-child{border-right:none}
.trustHouse  .mian_span{
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    line-height: 24px;
    padding-left: 105px;
}
.trustHouse  .mian_span li{
    display: block;
}
.trustHouse  .mian_span li i{
    width: 5px;
    height: 5px;
    background: #898989;
    opacity: 1;
    display: inline-block;
    border-radius: 5px;
    float: left;
    margin-top: 10px;
    margin-right: 5px;
}

/* 委托发布成功 */
.tc_trust{
    width: 390px;
    height: 339px;
    background: #FFFFFF;
    border-radius: 16px 16px 16px 16px;
    opacity: 1;
    border: 1px solid #707070;
    position: fixed;
    left: 50%;
    top: 50%;
    margin-left: -195px;
    margin-top: -170px;
    display: none;
    z-index: 999999999;
}
.tc_trust .trust_colse{
    width: 23.4px;
    height: 23.4px;
    float: right;
    background: url(https://static.fangxiaoer.com/web/images/sy/trustHouse/20.png) top center;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;background-size: 100% 100%;;;;;;;;;
;;;;margin: 18px;;;;;
;;cursor: pointer;;;
}
.tc_trust .trust_img{
    width: 91.67px;
    display: block;
    float: left;
    margin-left: 155px;
    margin-top: 63px;
}
.tc_trust .trust_h4{display: block;margin-top: 185px;text-align: center;margin-bottom: 68px;height: 35px;font-size: 26px;font-family: Microsoft YaHei-Regular, Microsoft YaHei;font-weight: 400;color: #111111;}
.tc_trust .trust_p{
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    text-align: center;
}
.realname_main .errorBox,.realname_main  .errorBox1{height: 22px;margin-top:2px;overflow:hidden;position: absolute;left: 0;bottom: -15px;text-align: left;}
.realname_main .err1 .errorBox{bottom: 0}
.realname_main .errorBox .error,.realname_main  .errorBox1 .error{color:#eb0000;width: 382px;display:block;float: left;height: 22px;line-height: 22px;margin-left: 0;}
.tc_realname .realname_ul li .img_item{
    width: 212px;
    height: 130px !important;
    /* overflow: hidden; */
    float: left;
    position: relative;
}
.tc_realname .realname_ul li .real_Id_img{
    /* overflow: hidden; */
    float: left;
}
.tc_realname .realname_ul li .real_Id_img div+div{
    margin-left: 58px;
}
#uploader1>i,#uploader2>i{
    position: absolute;
    right: 0;
    top: 0;
    width: 88px;
    height: 23px;
    background: #FF7534;
    border-radius: 0px 5px 0px 0px;
    opacity: 1;
    font-size: 10px;
    color: #fff;
    text-align: center;
    z-index: 9;
}
#uploader1>p,#uploader2>p{
    width: 212px;
    height: 26px;
    background: rgb(10 10 10 / 40%);
    border-radius: 0px 0px 5px 5px;
    position: absolute;
    bottom: 0;
    left: 0;
    color: #fff;
    text-align: center;
    z-index: 9;
}
.tc_realname .realname_ul li .img_item input{
    width: 100%;
    height: 100%;
    opacity: 0;
    position: absolute;
    left: 0;
    top: 0;
}
.tc_realname .realname_ul li .img_item img{
    width: 100%;
    height: 100%;
}
.tc_realname .realname_ul li .img_item .img_del{
    width: 21px;
    height: 21px;
    position: absolute;
    right: -12px;
    top: -11px;
    cursor: pointer;
    z-index: 10;
}
.idMsg{
    float: left;
    width: 100%;
    font-size: 12px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FF5200;
    height: 12px;
    line-height: 12px;
    margin: 6px 0 25px 0;
}
#uploader1,#uploader2{float:left;position: relative;}

.idErr{
    width: 460px;
    height: 180px;
    background: #FFFFFF;
    border-radius: 16px 16px 16px 16px;
    opacity: 1;
    border: 1px solid #707070;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-left: -230px;
    margin-top: -135px;
    padding-top: 90px;
    display: none;
    z-index: 999999999;
}
.idErr p{
    text-align: center;
    font-size: 16px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FF5200;
    margin-bottom: 66px;
}
.idErr-btn{
    overflow: hidden;
    margin-left: 91px;
}
.idErr-btn>a:hover{
    text-decoration: none;
}
.idErr-btn .idErr-btn-no:hover{
color: #111111;}
.idErr-btn .idErr-btn-yes{
    display: block;
    float: left;
    width: 110px;
    height: 42px;
    background: #FF5200;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    text-align: center;
    line-height: 42px;
    color: #fff;
    font-size: 14px;
}
.idErr-btn .idErr-btn-no{
    width: 110px;
    height: 40px;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    border: 1px solid #868686;
    display: block;
    float: left;
    margin-left: 68px;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
    font-size: 14px;
}
