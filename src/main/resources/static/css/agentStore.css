@charset "utf-8";

/*
制作人：张琪
修改时间：2019-9-19
用处：纪人机构门店专用
*/


/*首页*/

.w1170Mar {
	width: 1170px;
	margin: 0 auto;
}


/*头部广告*/

.homeBanner {
	/*background: url(https://static.fangxiaoer.com/web/images/agent/homeBanner2.jpg) no-repeat;*/
	width: 1170px;
	height: 125px;
	margin-bottom: 30px;
	/*background-size: 100% 1005;*/
}

.homeBanner>img {
	display: block;
	width: 100px;
	height: 100px;
	float: left;
	margin-right: 28px;
}

.homeBanner .shoreMain {
	margin-top: 17px;
	float: left;
}

.crumbs {
	margin-top: 0px !important;
	padding-top: 12px;
	margin-bottom: 0 !important;
}

.homeBanner .shoreMain h4 {
	font-size: 30px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #333333;
	line-height: 30px;
	margin-bottom: 16px;
}

.homeBanner .shoreMain p {
	font-size: 16px;
	line-height: 20px;
	height: 20px;
	color: #333;
	opacity: .7;
}

.homeBanner .shoreMain p i.mapIcon0 {
	background: url(https://static.fangxiaoer.com/web/images/agent/map_img.png) no-repeat;
	display: inline-block;
	width: 12px;
	height: 14px;
	    margin-right: 10px;
    margin-top: 4px;
	float: left;
}


/*首页切换导航*/

.homeNav {
	overflow: hidden;
	height: 37px;
	line-height: 37px;
	text-align: center;
	margin-bottom: 38px;
	margin-top: -40px;
}

.homeNav a {
	display: block;
	float: left;
	    margin-right: 40px;
	height: 37px;
	line-height: 37px;
	border-right: none;
	text-decoration: none;
	cursor: pointer;
	color: #333;
	font-size: 14px;
	position: relative;
}

.homeNav a span {
	border-bottom: 1px solid #fff;
}

.homeNav a:hover,
.homeNav a.hover {
	height: 37px;
	line-height: 37px;
	margin-top: -1px;
	color: #333333;
}


/*门店简介*/

.storeText .homeTitle {
	margin: 50px auto 20px auto;
}

.storeText {}

.homeTitle {
	margin: 50px auto 0px auto;
	line-height: 20px;
	height: 20px;
	padding-bottom: 10px;
	position: relative;
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #333333;
}

.storeText p {
	line-height: 36px;
	font-size: 14px;
	color: #333;
	width: 846px;
	/* margin-bottom: 10px; */
}

.storeText img {
	display: block;
	max-width: 784px;
	height: auto;
	margin-bottom: 15px;
}

.four {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 4;
	overflow: hidden;
}

.storeText p a {}

.txtMain {}

.seeBtn {
	text-decoration: none;
	font-size: 16px;
	color: #ff5200;
	display: block;
	margin-left: 400px;
	width: 78px;
	margin-top: 5px;
	cursor: pointer;
}

.seeShou {
	display: none;
}

.seeBtn:hover {
	text-decoration: none;
}

.shopManageCont {
	overflow: hidden;
}

.shopManageCont .shopManageList {
	width: 888px;
	float: left;
}

.shopManageCont .shopManageList .shopManageListTitle {
	padding: 10px 20px 10px 0px;
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.shopManageCont .shopManageList .shopManageListTitle a {
	float: right;
	font-size: 14px;
	color: #ff5200;
	text-decoration: none;
}

.person {
	color: #999999;
	margin-bottom: 10px;
}

.infCtn .houseItemIcon span {
	background: #fff3ed !important;
	border: none;
	color: #ff6100 !important;
	height: 24px;
	line-height: 25px !important;
	display: block;
	float: left;
	text-align: center;
	margin-right: 5px;
	padding: 0 8px;
	font-size: 14px;
}

.infCtn .houseItemIcon .isGoodHouse {
	background: url(https://static.fangxiaoer.com/web/images/sy/house/isGoodHouseIcon.png);
	width: 60px;
	height: 22px;
	float: left;
	margin-right: 5px;
	margin-top: 1px;
	border-radius: 0;
	border: none;
}

.personIcon {
	background: url(https://static.fangxiaoer.com/web/images/sy/house/housePersonIcon.png);
	display: inline-block;
	width: 16px;
	height: 16px;
	float: left;
	margin-right: 3px;
}

.infRight p.infRightPrise {
	font-family: "微软雅黑";
	font-size: 32px !important;
}

.infRight p.infRightPrise i {
	font-family: "微软雅黑";
	font-size: 14px;
	font-weight: normal;
}

.imgNum {
	position: absolute;
	background: url(https://static.fangxiaoer.com/web/images/ico/sign/ico_duotu.png);
	width: 48px;
	height: 32px;
	line-height: 32px;
	color: #fff;
	text-align: left;
	padding-left: 6px;
	bottom: 1px;
}

.inf {
	height: 155px;
	padding-bottom: 20px;
	border-bottom: 1px solid #ededed;
	position: relative;
	padding: 20px 0;
}
.inf:hover{background:#eee}

.secList ul li:last-child,
.rentList ul li:last-child,
.shopList ul li:last-child,
.officeList ul li:last-child {
	border-bottom: none !important;
}


/*二手房列表*/

.secList,
.rentList,
.shopList,
.officeList {
	border: 1px solid #eaeaea;
	/* margin-bottom: 39px; */
	width: 846px;
	float: left;
	padding: 0 20px;
	margin-top: 30px;
}

.secList>ul li,
.rentList>ul li,
.shopList>ul li,
.officeList>ul li {
	padding: 20px 0;
}

.secList .inf .imgNum {
	position: absolute;
	background: url(https://static.fangxiaoer.com/web/images/ico/sign/ico_duotu.png);
	width: 48px;
	height: 32px;
	line-height: 32px;
	color: #fff;
	text-align: left;
	padding-left: 6px;
	bottom: 1px;
}

.secList .infLeft {
	float: left;
	display: block;
	width: 210px;
	height: 155px;
	position: relative;
}

.secList .infLeft img {
	width: 100%;
	height: 154px;
}

.secList .infLeft div {
	background: #ff5200;
	color: #fff;
	display: inline-block;
	padding: 0px 5px;
	border-radius: 3px;
	position: absolute;
	top: 5px;
	right: 5px;
	height: 20px;
	line-height: 20px;
	font-size: 12px;
}

.secList .infCtn {
	float: left;
	text-align: left;
	margin-left: 20px;
	width: 480px;
}

.secList .infCtnTitle {
	font-size: 18px;
	color: #1f80d9;
	text-decoration: none;
	line-height: 18px;
	margin-bottom: 22px;
	display: block;
	width: 84%;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	float: left;
}

.secList .infCtn>span {
	height: 25px;
	line-height: 25px;
	display: block;
	float: left;
	text-align: center;
	font-size: 12px;
	color: #999999;
	margin-right: 5px;
	padding: 0 8px;
	float: left;
	background: #FFF;
}

.secList .infCtn p {
	font-size: 14px;
	margin-bottom: 15px;
	line-height: 14px;
	overflow: hidden;
	width: 100%;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: #666;
}

.secList .infCtn p>span {
	margin-left: 30px
}

.secList .infCtn p.span4 span {
	margin-left: 0;
	margin-right: 20px;
}

.secList .infRight {
	float: right;
	margin-top: 30px;
	margin-right: 60px;
}

.secList .inf:hover .checkHouse {
	display: inline;
}

.secList .checkHouse {
	color: #1f80d9;
	text-decoration: none;
	font-size: 14px;
	display: none;
	line-height: 25px;
	width: 200px;
	position: absolute;
	bottom: 13px;
	right: -52px;
}

.secList .infRight {
	text-align: right;
	margin-top: 39px;
	margin-right: 0;
	font-size: 14px;
	line-height: 14px;
}

.secList .infRight p:first-child {
	color: #FF5200;
	font-size: 25px;
	margin-bottom: 14px;
	display: block;
	line-height: 25px;
	font-weight: bold;
}


/*租房列表*/

.rentList {}

.rentList .inf .imgNum {
	position: absolute;
	background: url(https://static.fangxiaoer.com/web/images/ico/sign/ico_duotu.png);
	width: 48px;
	height: 32px;
	line-height: 32px;
	color: #fff;
	text-align: left;
	padding-left: 6px;
	bottom: 1px;
}

.rentList .infLeft {
	float: left;
	display: block;
	width: 210px;
	height: 155px;
	position: relative;
}

.rentList .infLeft img {
	width: 100%;
}

.rentList .infLeft div {
	background: #ff5200;
	color: #fff;
	display: inline-block;
	padding: 0px 5px;
	border-radius: 3px;
	position: absolute;
	top: 5px;
	right: 5px;
	height: 20px;
	line-height: 20px;
	font-size: 12px;
}

.rentList .infCtn {
	float: left;
	text-align: left;
	margin-left: 20px;
	width: 405px;
}

.rentList .infCtnTitle {
	font-size: 18px;
	color: #1f80d9;
	text-decoration: none;
	line-height: 18px;
	margin-bottom: 22px;
	display: block;
	width: 84%;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.rentList .infCtn>span {
	height: 25px;
	line-height: 25px;
	display: block;
	float: left;
	text-align: center;
	font-size: 12px;
	color: #999999;
	margin-right: 5px;
	padding: 0 8px;
	float: left;
	background: #FFF;
}

.rentList .infCtn p {
	font-size: 14px;
	margin-bottom: 15px;
	line-height: 14px;
	overflow: hidden;
	width: 100%;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: #666;
}

.rentList .infCtn p>span {
	margin-left: 30px
}

.rentList .infCtn p.span4 span {
	margin-left: 0;
	margin-right: 20px;
}

.rentList .infRight {
	float: right;
	margin-top: 30px;
	margin-right: 60px;
}

.rentList .inf:hover .checkHouse {
	display: inline;
}

.rentList .checkHouse {
	color: #1f80d9;
	text-decoration: none;
	font-size: 14px;
	display: none;
	line-height: 25px;
	width: 200px;
	position: absolute;
	bottom: 13px;
	right: -52px;
}

.rentList .infRight {
	text-align: right;
	margin-top: 39px;
	margin-right: 0;
	font-size: 14px;
	line-height: 14px;
}

.rentList .infRight p:first-child {
	color: #FF5200;
	font-size: 25px;
	margin-bottom: 14px;
	display: block;
	line-height: 25px;
}


/*商铺列表*/

.shopList {}

.shopList .infLeft {
	float: left;
	display: block;
	width: 210px;
	height: 155px;
	position: relative;
}

.shopList .infLeft img {
	width: 100%;
}

.shopList .infLeft div {
	background: #ff5200;
	color: #fff;
	display: inline-block;
	padding: 0px 5px;
	border-radius: 3px;
	position: absolute;
	top: 5px;
	right: 5px;
	height: 20px;
	line-height: 20px;
	font-size: 12px;
}

.shopList .infCtn {
	float: left;
	text-align: left;
	margin-left: 20px;
	width: 384px;
}

.shopList .infCtnTitle {
	font-size: 18px;
	color: #1f80d9;
	text-decoration: none;
	line-height: 18px;
	margin-bottom: 22px;
	display: block;
	width: 84%;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.shopList .shopList .infCtn span {
	height: 25px;
	line-height: 25px;
	display: block;
	float: left;
	text-align: center;
	font-size: 12px;
	color: #999999;
	margin-right: 5px;
	padding: 0 8px;
	*float: left;
	*margin-top: -15px;
	background: #FFF;
}

.infCtn p {
	font-size: 14px;
	margin-bottom: 15px;
	overflow: hidden;
	width: 100%;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.shopList .infRight {
	float: right;
	margin-top: 30px;
	margin-right: 60px;
}

.shopList .infRight {
	text-align: right;
	margin-top: 39px;
	font-size: 14px;
	line-height: 14px;
	margin-right: 0;
}

.shopList .infRight p:first-child {
	color: #FF5200;
	font-size: 25px;
	margin-bottom: 14px;
	display: block;
	line-height: 25px;
}

.shopList .infRight p b {
	font-weight: normal;
	margin-left: 10px;
}


/*写字楼列表*/

.officeList {}

.officeList .infLeft {
	float: left;
	display: block;
	width: 210px;
	height: 155px;
	position: relative;
}

.officeList .infLeft img {
	width: 100%;
}

.officeList .infLeft div {
	background: #ff5200;
	color: #fff;
	display: inline-block;
	padding: 0px 5px;
	border-radius: 3px;
	position: absolute;
	top: 5px;
	right: 5px;
	height: 20px;
	line-height: 20px;
	font-size: 12px;
}

.officeList .infCtn {
	float: left;
	text-align: left;
	margin-left: 20px;
	width: 385px;
}

.officeList .infCtnTitle {
	font-size: 18px;
	color: #1f80d9;
	text-decoration: none;
	line-height: 18px;
	margin-bottom: 22px;
	display: block;
	width: 84%;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.officeList .infCtn span {
	height: 25px;
	line-height: 25px;
	display: block;
	float: left;
	text-align: center;
	font-size: 12px;
	color: #999999;
	margin-right: 5px;
	padding: 0 8px;
	*float: left;
	*margin-top: -15px;
}

.officeList .infCtn p {
	font-size: 14px;
	margin-bottom: 15px;
	line-height: 14px;
	overflow: hidden;
	width: 100%;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.officeList .infRight {
	float: right;
	margin-top: 30px;
	margin-right: 60px;
}

.officeList .inf:hover .checkHouse {
	display: inline;
}

.officeList .checkHouse {
	color: #1f80d9;
	text-decoration: none;
	font-size: 14px;
	display: none;
	line-height: 25px;
	width: 200px;
	position: absolute;
	bottom: 13px;
	right: -52px;
}

.officeList .infRight {
	text-align: right;
	margin-top: 39px;
	margin-right: 0;
	font-size: 14px;
	line-height: 14px;
}

.officeList .infRight p:first-child {
	color: #FF5200;
	font-size: 25px;
	margin-bottom: 14px;
	display: block;
	line-height: 25px;
}

.officeList .infRight p b {
	font-weight: normal
}


/*右侧内容*/

.shopManageRight {
	width: 248px;
	float: right;
}

.RightK {
	/* width: 246px; */
	overflow: hidden;
	border: 1px solid #eaeaea;
	margin-bottom: 30px;
	margin-top: 30px;
}

.shopManageRightH4 {
	line-height: 46px;
	padding-left: 12px;
	border-bottom: 1px solid #eaeaea;
	font-size: 18px;
	font-weight: normal;
	color: #000;
}

.shopManageRightH4 i {
	display: block;
	width: 4px;
	height: 18px;
	background: #ff5200;
	float: left;
	margin-top: 14px;
	margin-right: 10px;
}

.storeAddress {}

.storeAddress ul {
	padding: 0 20px;
	padding-bottom: 10px;
}

.storeAddress ul li {}

.storeAddress ul li a {
	overflow: hidden;
	width: 100%;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
	line-height: 15px;
	height: 15px;
	margin: 20px 0 10px 0;
	text-decoration: none;
	font-size: 14px;
	color: #333;
}

.storeAddress ul li a:hover {
	color: #ff5200
}

.warning p {
	height: 48px;
	padding-left: 50px;
	line-height: 48px !important;
}

.groomAgrnt {
	margin-top: 30px;
}

.groomAgrnt ul {
	padding: 0 0 0 14px;
}

.groomAgrnt ul li {
	border-bottom: 1px solid #eaeaea;
}

.groomAgrnt ul li:last-child {
	border-bottom: none
}

.groomAgrnt ul li a {
	display: block;
	padding: 16px 0 16px 12px;
	overflow: hidden;
	text-decoration: none
}

#option .fenlei a {
	margin-right: 0;
}

.groomAgrnt ul li a img {
	width: 71px;
	height: 71px;
	border-radius: 65px;
	float: left;
	margin-right: 24px;
}

.groomAgrnt ul li a h5 {
	margin-top: 18px;
	font-size: 16px;
	color: #333;
	line-height: 16px;
	height: 16px;
	position: relative;
}

.groomAgrnt ul li a h5 span {
	float: left;
	margin-right: 5px;
}

.groomAgrnt ul li a h5 i {
	margin-top: 0;
}

.groomAgrnt ul li a p {
	font-size: 14px;
	color: #ff5200;
	height: 14px;
	line-height: 14px;
	margin-top: 10px;
}

.gra1 {
	background: url(https://static.fangxiaoer.com/web/images/agent/level-1.png) no-repeat center left !important;
	background-size: 100% 100% !important;
	color: #fff;
	font-weight: bold;
	font-family: Dutch801BT ExtraBold;
	display: block;
	width: 24px;
	padding-left: 20px;
	font-size: 10px;
	height: 18px;
	line-height: 19px;
	padding-right: 7px;
	float: left;
	text-align: right;
}

.gra2 {
	background: url(https://static.fangxiaoer.com/web/images/agent/level-2.png) no-repeat center left !important;
	background-size: 100% 100% !important;
	color: #fff;
	font-weight: bold;
	font-family: Dutch801BT ExtraBold;
	display: block;
	width: 24px;
	padding-left: 20px;
	font-size: 10px;
	height: 18px;
	line-height: 19px;
	padding-right: 7px;
	float: left;
	text-align: right;
}

.gra3 {
	background: url(https://static.fangxiaoer.com/web/images/agent/level-3.png) no-repeat center left !important;
	background-size: 100% 100% !important;
	color: #fff;
	font-weight: bold;
	font-family: Dutch801BT ExtraBold;
	display: block;
	width: 24px;
	padding-left: 20px;
	font-size: 10px;
	height: 18px;
	line-height: 19px;
	padding-right: 7px;
	float: left;
	text-align: right;
}

.jiShop .urgentWant li>a {
	padding: 15px 12px 10px 12px;
	border-bottom: 1px solid #eee;
	display: block;
}

.seeMoreList {
	text-align: center;
	display: block;
	line-height: 28px;
	height: 28px;
	color: #ff5200;
	cursor: pointer;
	font-size: 14px;
	background: #f6f6f6;
	padding: 0 !important;
}

.jiShop .urgentWant li a:hover,
.jiShop .urgentWant li a {
	text-decoration: none
}

.jiShop .urgentWant li:laat-child {
	border-bottom: none
}

.jiShop .urgentWant li h5 {
	white-space: pre;
	overflow: hidden;
	width: 220px;
	text-overflow: ellipsis;
	font-size: 14px;
	color: #333;
	font-weight: normal;
}

.jiShop .urgentWant li p {
	color: #ff5200;
}

.jiShop .urgentWant li p.agentRecruitList {
	width: 220px;
	color: #999;
	font-size: 12px;
	margin: 0;
}


/*经纪人 轮播*/

.villageAgent {
	width: 1128px;
	margin: 0 auto;
	border: 1px solid #eaeaea;
	/* margin-bottom: 30px; */
	padding: 0 20px;
	position: relative;
	margin-top: 30px;
}

.villageAgent .swiper-wrapper {
	padding: 20px 22px;
}

.villageAgent .swiper-container {
	padding: 0 30px;
	margin-left: -10px;
}

.villageAgentImg {
	width: 78px;
	height: 78px;
	border-radius: 50px;
	overflow: hidden;
	float: left;
	margin-right: 15px;
}

.villageAgentImg img {
	width: 100%;
	height: 100%;
}

.villageAgentCont {
	float: left;
	margin-top: 20px;
}

.villageAgentCont h4 {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 5px;
	overflow: hidden;
}

.villageAgentCont h4 span {
	float: left;
	margin-right: 12px;
}

.villageAgentCont p {
	color: #666;
	font-size: 12px;
}

bor {
	color: #ff5200;
	font-size: 16px;
}

.villageAgentCont div span {
	float: left;
	margin-right: 8px;
	font-weight: bold;
	color: #ff5200;
}

.liaobeiBtn {
	float: left;
}

.liaobeiBtn div {
	width: 42px;
	height: 20px;
	background-color: #32a3f2;
	display: block;
	float: left;
	cursor: pointer;
	font-size: 14px;
	color: #fff !important;
	text-align: center;
	border-radius: 2px;
	line-height: 20px;
	margin-top: 2px;
}

.liaobeiBtn div img {}

.liaobeiBtn div text {}

.villageAgent .swiper-button-next,
.villageAgent .swiper-button-prev {
	width: 34px;
	height: 118px;
	background-size: 100% 100%;
}

.villageAgent .swiper-button-prev {
	background: url(https://static.fangxiaoer.com/web/images/sy/house/swiper-button-Vil.png) top center;
	background-position: 30px 0;
	/* background-image: none; */
	background-size: initial;
}

.villageAgent .swiper-button-next {
	background-position: 0px 0;
	background-size: initial;
	background: url(https://static.fangxiaoer.com/web/images/agent/storeRight.jpg) no-repeat;
	right: 0px;
	top: 1px;
	margin-top: 0px;
}

.villageAgent .swiper-button-prev:hover {
	background: url(https://static.fangxiaoer.com/web/images/agent/storeLeft.jpg) no-repeat;
	right: auto;
	top: 1px;
	margin-top: 0;
	left: 0;
}

.villageAgent .swiper-button-next:hover {
	background-position: 0px 0;
	background-size: initial;
	background: url(https://static.fangxiaoer.com/web/images/agent/storeRight.jpg) no-repeat;
	right: 0px;
	top: 1px;
	margin-top: 0;
}

.villageAgent .swiper-button-prev {
	background: url(https://static.fangxiaoer.com/web/images/agent/storeLeft.jpg) no-repeat;
	right: auto;
	top: 1px;
	margin-top: 0;
	left: 0px;
}

.villageAgent .swiper-button-next.swiper-button-disabled {
	background-position: 0px 0;
	background-size: initial;
	background: url(https://static.fangxiaoer.com/web/images/agent/storeRight.jpg) no-repeat;
	right: 0px;
	top: 1px;
	margin-top: 0;
}

.fourSpan {
	float: left;
	color: #333;
	font-size: 14px;
	margin-bottom: 17px;
	width: 520px;
	height: 14px;
	line-height: 14px;
}

.infCtn .fourSpan span {
	font-size: 14px;
	padding: 0 10px;
	border-left: 1px solid #efefef;
	height: 14px;
	line-height: 14px;
	color: #333;
}

.fourSpan .personShow {}

.fourSpan span:first-child {
	padding-left: 0;
	border-left: none;
}

.fourSpan .personShow {
	border-left: none;
	color: #999;
	margin-top: -1px;
}

.fourSpan .personShow div {
	overflow: hidden;
}

.fourSpan .personShow s {
	float: left;
	line-height: 16px;
}

.newHouseListTitle {
	font-size: 22px;
	font-weight: bold;
	color: #333;
	height: 22px;
	line-height: 22px;
	margin-bottom: 32px;
	width: 500px;
	display: block;
	float: left;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	min-width: 64px;
	margin-top: 2px;
	text-decoration: none;
}


/*精英顾问*/

.elitePage {
	overflow: hidden;
}

.elitePage li {
    width: 857px;
    height: 70px;
    background: url(https://static.fangxiaoer.com/web/images/agent/info_bg.png) no-repeat center;
    background-size: 100% 100%;
    padding: 30px 0 30px 30px;
        margin-bottom: 20px;
}

.elitePage li:nth-child(3n) {
	margin-right: 0px;
}

.elitePage li .eliteImg {
	display: block;
	width: 64px;
	height: 64px;
	overflow: hidden;
	border-radius: 50%;
	float: left;
	margin-right: 22px;
}

.elitePage li .eliteImg img {
	width: 100%;
	height: 100%;
}

.elitePage li .ren {
	 float: left; 
	 width: 400px;
}

.elitePage li .ren .name {
	display: block;
	margin-bottom: 10px;
}

.elitePage li .ren .name h4 {
	font-size: 18px;
	height: 18px;
	line-height: 18px;
	color: #333;
}

.elitePage li .ren p {
	display: block;
	font-size: 16px;
	font-weight: bold;
	color: #ff5200;
	margin-right: 12px;
	width: 115px;
	float: left;
}

.elitePage li .ren .name h4 span {
	margin-right: 10px;
	float: left;
}

.elitePage li>p {
	float: left;
	font-size: 16px;
	width: 100%;
	margin-top: 20px;
	line-height: 16px;
	height: 16px;
}

.elitePage li>p+p {
	margin-top: 12px;
}

.elitePage li .plateS {
	float: left;
	overflow: hidden;
	font-size: 16px;
	margin-top: 20px;
	line-height: 16px;
}

.elitePage li .plateS span {
	float: left;
	width: 80px;
	line-height: 20px;
}

.elitePage li .plateS div {
	float: left;
	width: 235px;
	line-height: 20px;
}


/*经纪人招聘列表页*/

.logoBanner {
	width: 1170px;
	margin: 0 auto;
}

#option p {
	width: 60px;
	text-align: right;
}

.logoBanner .bigBanner {}

.logoBanner .bigBanner img {}

.logoBanner .smallBanner {
	overflow: hidden;
	margin: 10px auto;
}

.logoBanner .smallBanner li {
	float: left;
	width: 282px;
	height: 106px;
	border: 1px solid #f4f4f4;
	margin: 0 10px 10px 0;
	box-shadow: 3px 3px 12px #eee;
}

.logoBanner .smallBanner li:nth-child(4n) {
	margin-right: 0;
}

.logoBanner .smallBanner li a {
	width: 282px;
	height: 106px;
	display: block;
	overflow: hidden;
	box-shadow: 3px 4px 2px #eee;
}

.logoBanner .smallBanner li a img {
	width: 100%;
}


/*招聘列表左侧*/

.agentRecruitList {
	overflow: hidden;
	width: 1170px;
	margin: 0 auto;
	margin-top: 20px;
	margin-bottom: 20px;
}

.NoListMsg {
	width: 470px;
	display: block;
	margin: 20px 0 20px 20px;
	color: #999;
}

.agentRecruitList .arlLeft {
	width: 900px;
	float: left;
	border: 1px solid #eee;
	margin-bottom: 20px;
}

.agentRecruitList .arlLeft .arlLeftTitle {
	overflow: hidden;
	line-height: 46px;
	height: 46px;
	border-bottom: 1px solid #eee;
}

.agentRecruitList .arlLeft .arlLeftTitle p {
	float: left;
	font-size: 16px;
	color: #ff5200;
	display: block;
	width: 110px;
	text-align: center;
	border-right: 1px solid #eee;
}

.agentRecruitList .arlLeft .arlLeftTitle span {
	float: right;
	padding-right: 20px;
	color: #ff5200;
	font-size: 14px;
}

.agentRecruitList .arlLeft ul {
	padding-bottom: 20px;
}

.agentRecruitList .arlLeft ul li {
	overflow: hidden;
	border-bottom: 1px solid #eee;
	padding: 15px 18px;
}

.agentRecruitList .arlLeft ul li>h4>a {
	display: inline-block;
	float: left;
	height: 18px;
}

.agentRecruitList .arlLeft ul li>a:hover {
	color: #333
}

.agentRecruitList .arlLeft ul li:last-child {
	border-bottom: none
}

.liaobeiICon {
	display: inline-block;
	font-size: 14px;
	font-weight: normal;
	margin-left: 8px;
	color: #02a0db;
}

.liaobeiICon div {
	background: none;
	overflow: hidden;
}

.liaobeiICon span {
	display: block
}

.liaobeiICon:hover text {
	color: #ff5200
}

.liaobeiICon:hover .liaobeiIcon {
	background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue2.png) 0px 1px no-repeat;
}

.liaobeiICon a {}

.liaobeiICon .liaobeiIcon {
	height: 14px;
	width: 14px;
	display: inline-block;
	background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue2.png) 0px -14px no-repeat;
	float: left;
	margin-top: 2px;
	margin-right: 5px;
}

.liaobeiICon text {
	color: #02a0db;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL {
	width: 550px;
	float: left;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL>a {
	overflow: hidden;
	display: block;
}


/* .agentRecruitList .arlLeft ul li>div>a:hover,.agentRecruitList .arlLeft ul li .arlLeftLiL>a:hover{text-decoration:underline} */

.agentRecruitList .arlLeft a:hover {
	text-decoration: none
}

.agentRecruitList .arlLeft ul li .arlLeftLiL h4 {
	font-size: 16px;
	color: #333;
	font-weight: normal;
	overflow: hidden;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL span {
	float: left;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL span+i+span {
	width: 450px;
	overflow: hidden;
	white-space: pre;
	text-overflow: ellipsis;
	display: inline-block;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL i {
	float: left;
	padding: 0 5px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL p {
	color: #ff5200;
	line-height: 26px;
	margin-top: 5px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL p.fldy {
	color: #999;
	margin-top: 10px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL p.fldy span {
	display: inline-block;
	padding: 0 6px;
	border: 1px solid #e2e2e2;
	margin-right: 10px;
	line-height: 24px;
	height: 22px;
	border-radius: 2px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiR {
	float: right;
	width: 300px;
	text-align: right;
	overflow: hidden;
	margin-top: -30px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiR h3 {
	font-size: 14px;
	color: #333;
}

.agentRecruitList .arlLeft ul li .agentRecruitListp {
	color: #999;
	font-size: 14px;
	margin: 0;
	margin-left: 10px;
}

.agentRecruitList .arlLeft ul li .agentRecruitList span {}

.agentRecruitList .arlLeft ul li .agentRecruitList>i {}

.agentRecruitList .arlLeft ul li .agentRecruitList span i {}

.agentRecruitList .arlLeft ul li .arlLeftLiR .arlLeftBTn,
.arlLeftBTn {
	margin-top: 10px;
	display: block;
	height: 26px;
	line-height: 26px;
	width: 95px;
	float: right;
	border: 1px solid #ff5200;
	text-align: center;
	border-radius: 13px;
	color: #ff5200;
	cursor: pointer;
	text-decoration: none;
}

.topR-login {
	margin-top: 10px;
	display: block;
	height: 26px;
	line-height: 26px;
	width: 95px;
	float: right;
	border: 1px solid #ff5200;
	text-align: center;
	border-radius: 13px;
	color: #ff5200;
	cursor: pointer;
	text-decoration: none;
}

.bkNavFloat .arlLeftBTn {
	display: block;
	height: 26px;
	line-height: 26px;
	width: 95px;
	float: right;
	text-align: center;
	border-radius: 13px;
	color: #ff5200;
	cursor: pointer;
	text-decoration: none;
	background: #fff;
	margin-top: 0;
}

.agentRecruitList .arlLeft ul li:hover {
	background: #f6f6f6;
}

.agentRecruitList .arlLeft ul .arlLeftBTn:hover {
	background: #ff5200;
	color: #fff !important;
}

body>div>.bkNavFloat .arlLeftBTn:hover {
	background: #fff;
	color: #ff5200 !important;
}


/*招聘列表右侧*/

.agentRecruitList .arlRight {
	float: right;
	width: 250px;
}

.agentRecruitList .arlRight .urgentWant {
	border: 1px solid #eee;
	margin-bottom: 20px;
}

.agentRecruitList .arlRight .urgentWant h4 {
	padding: 14px 0 14px 12px;
	border-bottom: 1px solid #eee;
	font-size: 18px;
	font-weight: normal;
	color: #333;
}

.agentRecruitList .arlRight .urgentWant h4 i {
	display: block;
	width: 4px;
	height: 18px;
	background: #ff5200;
	float: left;
	margin-top: 3px;
	margin-right: 5px;
}

.agentRecruitList .arlRight .urgentWant li {
	padding: 15px 12px 10px 12px;
	border-bottom: 1px solid #eee;
}

.agentRecruitList .arlRight .urgentWant li a:hover,
.agentRecruitList .arlRight .urgentWant li a {
	text-decoration: none
}

.agentRecruitList .arlRight .urgentWant li:laat-child {
	border-bottom: none
}

.agentRecruitList .arlRight .urgentWant li h5 {
	white-space: pre;
	overflow: hidden;
	width: 220px;
	text-overflow: ellipsis;
	font-size: 14px;
	color: #333;
	font-weight: normal;
}

.agentRecruitList .arlRight .urgentWant li p {
	color: #ff5200;
}

.agentRecruitList .arlRight .urgentWant li p.agentRecruitList {
	width: 220px;
	color: #999;
	font-size: 12px;
	margin: 0;
}

.agentRecruitList .arlRight .urgentWant li p.agentRecruitList span {}

.agentRecruitList .arlRight .urgentWant li p.agentRecruitList>i {}

.agentRecruitList .arlRight .urgentWant li p.agentRecruitList span i {}

.agentRecruitList .arlRightBanner {
	margin-bottom: 20px;
}

.agentRecruitList .arlRightBanner a {
	display: block;
	width: 250px;
	/* height:  190px; */
}

.agentRecruitList .arlRightBanner a img {
	width: 100%;
	/* height: 100%; */
}


/*简历弹窗*/

.arlTcHb {
	display: none;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 9999;
	background-color: #000;
	opacity: 0.7;
}

.arlTcMain {
	display: none;
	width: 414px;
	/* height: 338px; */
	background: #fff;
	border-radius: 4px;
	position: fixed;
	left: 50%;
	top: 50%;
	margin-left: -227px;
	margin-top: -272px;
	padding: 20px;
	z-index: 9999;
}

.arlTcMain h4 {
	font-size: 16px;
	text-align: center;
	color: #333;
	margin-bottom: 20px;
}

.arlTcMain ul {}

.arlTcMain ul li {
	overflow: hidden;
	margin-bottom: 15px;
}

.arlTcMain ul li label {
	float: left;
	display: block;
	margin-right: 5px;
	width: 110px;
	text-align: right;
}

.selectpicker {
	width: 272px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #eaeaea;
	padding: 0 5px;
}

.selectpicker option {}

.arlTcMain ul li input {
	float: left;
	width: 265px;
	height: 26px;
	line-height: 28px;
	border: 1px solid #eaeaea;
	padding-left: 5px;
}

#workedUnits {
	width: 260px;
}

.arlTcMain ul li .select_box {
	width: 96px;
	height: 25px;
	float: left;
	margin: 9px 8px 0 8px;
	position: relative;
	text-align: left;
}

.arlTcMain ul li .select_info {
	background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/sale_xl.gif) no-repeat;
	width: 73px;
	height: 25px;
	line-height: 25px;
	font-size: 12px;
	padding: 0 20px 0 7px;
	cursor: pointer;
}

.arlTcMain ul li .select_box ul {
	position: absolute;
	background: #fff;
	width: 94px;
	border: 1px solid #ccc !important;
	top: 23px;
	display: none;
	overflow-y: overlay;
	max-height: 200px;
}

.arlTcMain ul li .select_box ul li {
	height: 30px;
	line-height: 30px;
	border: none;
	/* padding-left: 6px; */
	cursor: pointer;
	font-size: 12px;
}

.arlTcMain ul li .select_box ul li a {
	width: 93%;
	height: 100%;
	display: block;
	padding-left: 6px;
}

.arlTcMain ul li .select_box ul li a:hover {
	background: #f5f5f5;
}

.arlTcMain ul li.select_box ul li:hover a {
	color: #333;
	text-decoration: none;
}

.arlTcMain ul li .select_box:hover ul {
	display: block
}

.arlTcMain ul li .my_xl {
	height: 26px;
	line-height: 26px;
	width: 110px;
	padding: 0px 6px;
	position: relative;
	font-size: 14px;
	background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 107px 11px no-repeat;
	cursor: pointer;
	display: inline-block;
	float: left;
	margin-right: 20px;
	z-index: 99;
}

.arlTcMain ul li .my_xl_txt {
	float: left;
	width: 133px;
	line-height: 26px;
	padding-right: 17px;
}

.arlTcMain ul li .my_xl,
.my_xl_list {
	border: 1px solid #ccc;
	border-radius: 2px;
}

.arlTcMain ul li .my_xl_txt,
.my_xl_list li {
	text-indent: 0px;
	overflow: hidden;
}

.arlTcMain ul li .my_xl_list {
	position: absolute;
	top: 34px;
	left: -1px;
	z-index: 88888;
	/* border-top:none; */
	width: 100%;
	display: none;
	_top: 23px;
	margin-left: 0px !important;
}

.arlTcMain ul li .my_xl_list li {
	list-style: none;
	height: 30px;
	line-height: 30px;
	cursor: default;
	background: #fff;
	padding-left: 6px;
}

.arlTcMain ul li .my_xl_list li.focus {
	background: #3399FF;
	color: #fff
}

.arlTcMain ul li .my_xl_input,
.tese_input {
	position: absolute;
	top: -999999px;
}

.arlTcMain ul li input#code {
	width: 115px !important;
}

.arlTcMain ul li i {
	color: red;
}

.arlTcMain ul li span {
	display: block;
	width: 72px;
	height: 28px;
	float: left;
	margin-right: 14px;
	text-align: center;
	line-height: 28px;
	border: 1px solid #eaeaea;
	cursor: pointer;
}

.arlTcMain ul li span.duigou {
	background: url(https://static.fangxiaoer.com/web/images/sy/agentRecruit/duigou.png) top center no-repeat;
	color: #ff5200;
}

.arlTcMain ul li b {
	width: 101px;
	background: #f1f2f4;
	font-weight: 400;
	display: block;
	float: left;
	text-align: center;
	cursor: pointer;
	width: 130px;
	line-height: 28px;
	height: 28px;
	margin-left: 20px;
}

.arlTcMain ul li b.fxe_validateCode {
	display: none;
}

.arlTcMain ul .arlTcMainBtn {
	width: 246px;
	height: 32px;
	background: #ff5200;
	color: #fff;
	display: block;
	text-align: center;
	border-radius: 2px;
	margin: 0 auto;
	line-height: 32px;
	font-size: 14px;
	cursor: pointer;
	text-decoration: none;
	margin-top: 22px;
}

.arlTcMain .closeReport {
	font-style: normal;
	font-size: 12pt;
	color: #999;
	background: url(https://static.fangxiaoer.com/m/images/sale/reportClose.jpg);
	display: block;
	width: 16px;
	height: 16px;
	background-size: cover;
	position: absolute;
	right: 10px;
	top: 10px;
	cursor: pointer;
	opacity: 0.6;
}

.experienceHide {
	display: none;
}

.living {}

.living select {
	float: left;
	width: 132px;
	height: 26px;
	line-height: 28px;
	border: 1px solid #eaeaea;
	padding-left: 5px;
}

.living select+select {
	margin-left: 10px;
}


/*搜索框*/

.searchEsfMap {
	width: 1170px;
	margin: 0px auto 0 auto;
	overflow: hidden;
	position: relative;
}

.searchEsfMap .searchMapInput {
	padding-left: 5px;
	width: 430px;
	height: 44px;
	line-height: 44px;
	border: 1px solid #ddd;
	float: left;
}

.searchEsfMap .searchMapBtn {
	width: 99px;
	height: 46px;
	/* height: 33px; */
	line-height: 46px;
	float: left;
	background: #ff7200;
	border: none;
	color: #fff;
	margin-left: -1px;
	font-size: 16px;
	cursor: pointer;
}

#deleteButton {
	position: absolute;
	top: 14px;
	left: 402px;
	cursor: pointer;
	display: none;
}


/*公司详情页*/

.companyMain {
	width: 1170px;
	margin: 0 auto;
	border: 1px solid #eee;
	margin-bottom: 20px;
}

.companyBanner {
	width: 1170px;
	margin: 0 auto;
	height: 126px;
	overflow: hidden;
	background: url(https://static.fangxiaoer.com/web/images/sy/agentRecruit/companyBg.jpg);
	/* margin-bottom:  20px; */
}

.companyBanner h4 {
	color: #fff;
	font-size: 30px;
	line-height: 126px;
	padding-left: 20px;
	font-weight: 500;
}

.companyintroduce {
	width: 1170px;
	margin: 0 auto;
	overflow: hidden;
	padding: 20px 0;
}

.companyintroduce .L {
	width: 874px;
	float: left;
	padding: 20px;
}

.companyintroduce .L h4 {
	font-size: 20px;
	margin-bottom: 20px;
	font-weight: 600;
	color: #333;
}

.companyintroduce .L p {
	font-size: 14px;
	line-height: 30px;
	color: #666;
}

.companyintroduce .R {
	float: right;
	width: 236px;
	height: 174px;
	overflow: hidden;
	margin-right: 20px;
}

.companyintroduce .R img {
	width: 100%;
	height: 100%;
}


/* 地图 */

.arlRightMap {
	border: 1px solid #eee;
}

.arlRightMap h4 {
	font-size: 14px;
	font-weight: normal;
	color: #333;
	padding-left: 5px;
	white-space: pre;
	text-overflow: ellipsis;
	width: 245px;
	padding-top: 5px;
	overflow: hidden;
}

.bigAllmap h4 {
	width: 97%;
	padding-left: 2%;
	font-size: 18px;
}

.arlRightMap p {
	color: #666;
	padding-left: 5px;
	/* white-space:  pre; */
	/* text-overflow: ellipsis; */
	width: 245px;
	overflow: hidden;
}

.arlRightMap>div {
	/* height:250px */
}

.hoverMap {
	display: block;
}

.arlRightMap>div>div {
	position: relative;
}


/* .hoverMap:hover .hoverMapShow{display:block} */

.hoverMapShow {
	border-radius: 1px;
	position: absolute;
	right: 0;
	bottom: 0;
	z-index: 9;
	background: rgba(0, 0, 0, 0.5);
	padding: 0 5px;
	font-size: 12px;
	color: #fff;
	cursor: pointer;
}

.hoverMapShow:hover {
	color: #fff;
	background: rgba(0, 0, 0, 0.7);
}


/*职位介绍*/

.agentPosition {
	width: 1130px;
	margin: 0 auto;
	padding: 20px;
	overflow: hidden;
	margin-bottom: 20px;
	background: #f6f7fc;
}

.agentPosition:hover {}

.agentPosition .L {
	overflow: hidden;
	width: 950px;
	float: left;
}

.agentPosition .L h4 {
	font-size: 22px;
	color: #333;
	display: inline-block;
	float: left;
}

.agentPosition .L>span {
	font-size: 14px;
	margin-left: 15px;
}

.agentPosition .L>div {
	line-height: 30px;
}

.agentPosition .L>div>p {
	color: #ff5200;
	line-height: 26px;
	margin-top: 5px;
	display: inline-block;
	font-size: 16px;
}

.agentPosition .L>div>p.agentRecruitListp {
	color: #999;
	margin: 5px 0;
	font-size: 14px;
	margin-left: 20px;
}

.agentPosition .L>div>p.agentRecruitListp span {}

.agentPosition .L>div>p.agentRecruitListp>i {}

.agentPosition .L>div>p.agentRecruitListp span i {}

.agentPosition .L>p.fldy {
	color: #999;
	margin-top: 10px;
}

.agentPosition .L>p.fldy span {
	display: inline-block;
	padding: 0 6px;
	border: 1px solid #e2e2e2;
	margin-right: 10px;
	line-height: 24px;
	height: 22px;
	border-radius: 2px;
	background: #fff;
}

.agentPosition .R .liaobeiICon {
	float: right;
	margin-top: 36px;
	margin-left: 14px;
}

.agentPosition .R .arlLeftBTn {
	margin-top: 35px;
	display: block;
	height: 26px;
	line-height: 26px;
	width: 95px;
	float: right;
	border: 1px solid #ff5200;
	text-align: center;
	border-radius: 13px;
	color: #ff5200;
	cursor: pointer;
	text-decoration: none;
}

.agentPosition .R .arlLeftBTn:hover,
.arlLeftBTn:hover {
	background: #ff5200;
	color: #fff
}

.companyRLogo {
	width: 250px;
	height: 200px;
	margin-bottom: 20px;
}

.companyRLogo img {
	width: 100%;
	height: 100%;
}

.arlLeft .txtMain {
	width: 860px;
	padding: 20px;
	line-height: 30px;
	font-size: 14px;
}

.arlLeft .txtMain h5 {
	font-size: 14px;
	font-weight: normal;
	color: #333;
}

.arlLeft .txtMain p {
	color: #666;
}

.bigAllmap {
	display: none;
	position: relative;
	width: 800px;
	height: 500px;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -400px;
	margin-top: -250px;
	background: #fff;
	z-index: 99999;
}

.heiMuAllmap {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 9999;
	display: none;
}

.allmapBigShow {
	width: 800px;
	height: 500px;
}

.BMapLabel {
	background: rgba(255, 6, 0, 0.6) !important;
	padding: 2px 8px !important;
	display: block !important;
	color: #fff !important;
	border-radius: 2px !important;
}

.bigAllmapClose {
	z-index: 99999999999999999999;
	cursor: pointer;
	top: 10%;
	display: block;
	width: 20px;
	height: 20px;
	float: right;
}

.bigAllmapClose img {
	width: 100%;
	height: 100%;
}

.showTitle {
	display: none;
}

#allmap {
	height: 250px;
	width: 248px
}


/*页面滚动后固定页面*/

.fixbkb {
	position: fixed;
	top: 0;
	left: 0;
	background: rgba(255, 82, 0, 0.9);
}

.bkNavFloat {
	width: 100%;
	height: 42px;
	padding-top: 4px;
	margin: 0px 0;
	z-index: 999;
	overflow: hidden;
	color: #fff;
	display: block !important;
	background: #ff5200;
	position: fixed;
}

.bkNavFloat h4 {
	float: left;
	font-size: 18px;
	width: auto;
}

.bkNavFloat .liaobeiICon {
	float: right;
	line-height: 30px;
	margin-left: 14px;
}

.bkNavFloat .liaobeiICon .liaobeiIcon {
	margin-top: 9px;
	background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue2.png) 0px -30px no-repeat;
}

.bkNavFloat .liaobeiICon:hover .liaobeiIcon {
	background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue2.png) 0px -30px no-repeat !important;
}

.bkNavFloat .liaobeiICon text {
	color: #fff;
	font-size: 12px;
}

.bkNavFloat>div {
	overflow: hidden;
	padding-top: 5px;
}

.container div div {
	float: right;
}

.container div>p {
	font-size: 14px;
	float: left;
	line-height: 30px;
	margin-left: 40px;
}

.container div i {
	background: url(https://static.fangxiaoer.com/web/images/ico/sign/fixbkb-phone.png)no-repeat;
	display: block;
	width: 22px;
	height: 22px;
	float: left;
	margin-right: 15px;
}

.container div div p {}


/* 精英顾问-员工介绍版本 */

.elite-adviser {
	margin: 0 auto;
	position: relative;
	margin-top: 30px;
	width: 1170px !important;
}

.elite-k {}

.elite-btn {}

.elite-btn div {}

.elite-btn-left {
	display: inline-block;
	width: 28px;
	height: 28px;
	background-image: url(https://static.fangxiaoer.com/web/images/sercoudo/left-arrow-hover.png);
	background-repeat: no-repeat;
	background-position: left center;
	margin: 0 2.5px;
	position: absolute;
	left: -2px;
	top: 50%;
	margin-top: -14px;
}

.elite-btn-right {
	display: inline-block;
	width: 28px;
	height: 28px;
	background-image: url(https://static.fangxiaoer.com/web/images/sercoudo/left-arrow-hover.png);
	background-repeat: no-repeat;
	background-position: left center;
	margin: 0 2.5px;
	position: absolute;
	right: -2px;
	top: 50%;
	margin-top: -14px;
}

.elite-main {
	padding: 0;
	border: none;
}

.elite-Swiper {
	width: 857px;
	height: 70px;
	background: url(https://static.fangxiaoer.com/web/images/agent/info_bg.png) no-repeat center;
	background-size: 100% 100%;
	padding: 30px 0 30px 30px;
	margin-bottom: 10px;
}

.elite-Swiper {
	text-align: center;
	display: none;
}

.elite-slide {
	/* overflow: hidden; */
}

.elite-slide-a {
	display: block;
	text-decoration: none;
	color: #333;
	overflow: hidden;
	float: left;
	line-height: normal;
	margin-right: 14px;
}

.elite-slide-img {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	overflow: hidden;
	float: left;
}

.elite-slide-img img {
	width: 100%;
	height: 100%;
}

.elite-slide-cont {
	float: left;
	margin-top: 5px;
}

.elite-slide-to {
	display: block;
	text-decoration: none;
	color: #333;
}

.elite-slide-h4 {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	overflow: hidden;
}

.elite-slide-h4 i {
	margin-top: 0;
}

.elite-slide-name {
	float: left;
	margin-right: 12px;
	font-size: 18px;
	height: 18px;
	line-height: 18px;
}

.suggest-show-txt {
	float: left;
	text-align: left;
	line-height: 21px;
	font-size: 14px;
	width: 237px;
	margin-top: 16px;
}

.elite-slide-liaobei {
	width: 80px;
	height: 24px;
	float: left;
	border-radius: 2px;
	overflow: hidden;
	background: #32A3F2;
	margin-top: 12px;
	display: block;
	text-decoration: none;
	color: #333;
	text-align: center;
	display: block;
	color: #fff;
}

.elite-slide-liaobei:hover {
	color: #fff;
	text-decoration: none
}

.hasResume {
	position: fixed;
	left: 50%;
	width: 454px;
	height: 232px;
	top: 50%;
	background: #fff;
	margin-left: -227px;
	margin-top: -126px;
	text-align: center;
	padding-top: 20px;
	display: none;
	z-index: 99999;
}

.hasResume h5 {
	color: #333;
	font-size: 18px;
	margin-bottom: 50px;
}

.hasResume p {
	font-size: 18px;
	line-height: 30px;
}

.hasResume p+p {}

.hasResume .hasBtn {
	border-top: 1px solid #eee;
	height: 58px;
	line-height: 58px;
	margin-top: 38px;
}

.hasResume .hasBtn span {
	width: 50%;
	display: inline-block;
	float: left;
	color: #ff5200;
	font-size: 18px;
	cursor: pointer;
}

.hasResume .hasBtn span+span {
	border-left: 1px solid #eee;
	width: 49%;
	color: #333;
}

.closeReport {
	font-style: normal;
	font-size: 12pt;
	color: #999;
	background: url(https://static.fangxiaoer.com/m/images/sale/reportClose.jpg);
	display: block;
	width: 16px;
	height: 16px;
	background-size: cover;
	position: absolute;
	right: 10px;
	top: 10px;
	cursor: pointer;
	opacity: 0.6;
}

.otherJob {
	border: 1px solid #eee;
}

.otherJobLi {
	border-bottom: 1px solid #eee;
	padding: 20px 20px;
	padding-top: 17px;
	overflow: hidden;
}

.otherJobH4 {
	display: inline-block;
	float: left;
	font-size: 18px;
	font-weight: bold;
	height: 20px;
	line-height: 18px;
	margin-bottom: 20px;
	width: 100%;
}

.otherJobH4>a {
	float: left;
}

.otherJobH4 .otherJobH4 {}

.otherJobH4div {
	float: left;
}

.agentSpan {
	line-height: 26px;
	font-size: 14px;
	font-weight: bold;
	color: #999;
	margin-top: 14px;
}

.agentSpan span {
	display: inline-block;
	padding: 0 6px;
	border: 1px solid #e2e2e2;
	margin-right: 10px;
	line-height: 22px;
	height: 22px;
	border-radius: 2px;
	font-weight: normal;
	float: left;
}

.otherJobLiL {
	width: 550px;
	float: left;
}

.otherJobLiR {
	float: right;
	width: 300px;
	text-align: right;
	overflow: hidden;
	margin-top: -36px;
}

.otherJobLiL-Prise {
	color: #ff5200;
	line-height: 14px;
	font-size: 14px;
	font-weight: bold;
	float: left;
}

.otherJobLi-login {
	padding: 0 10px;
	border: 1px solid #ff6100;
	border-radius: 23px;
	color: #ff6100;
	line-height: 24px;
	height: 24px;
	display: block;
	width: 60px !important;
	float: right;
	text-align: center;
	margin-top: 39px;
}

.otherJobLiL-txt {
	overflow: hidden;
}

.agentRecruitListp {
	float: left;
	height: 14px;
	color: #333333;
	line-height: 14px;
	margin-left: 28px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL span {
	float: left;
	width: auto !important;
}

.arlLeftLiL-Txt .agentSpan {
	float: left;
	height: 25px;
	width: 360px;
	color: #999;
}

.otherJobH4Span1 {
	float: left;
}

.otherJobH4Span2 {
	float: left;
	display: inline-block;
	max-width: 410px;
	text-overflow: ellipsis;
	white-space: pre;
	overflow: hidden;
}

.otherJobH4I {
	float: left;
	margin: 0 5px;
	font-size: 16px;
}

.arlLeftLiRTitle {
	float: none !important;
}

.head_info {
	padding: 17px 0;
	    box-shadow: 0px 5px 7px 0px rgb(0,0,0,.1);
}

.head_shadow {
}

.homeNav .hover span {
	width: 28px;
	height: 2px;
	background: #F9511B;
	border-radius: 1px;
	margin: auto;
	text-align: center;
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
}

.titleIco {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 20px;
	height: 2px;
	background: #F9511B;
	border-radius: 1px;
}

.agent-cardTels {
	width: 88px;
	height: 24px;
	text-align: center;
	line-height: 32px;
	cursor: pointer;
	position: relative;
	background: #ff6100;
	border-radius: 2px;
	color: #fff;
	float: left;
	margin-right: 8px;
	margin-top: 12px;
}

.agent-cardTels span {
	width: 88px;
	height: 24px;
	line-height: 24px;
	text-align: center;
	display: block;
}
.jiangbei{
	    background: url(https://static.fangxiaoer.com/web/images/agent/jiangbei.png) no-repeat center;
    background-size: 100% 100%;
    width: 54px;
    height: 60px;
    float: right;
    margin-top: 10px;
    margin-right: 40px;
}

.seeshop div {
        float: initial;
    margin-top: auto;
    margin-right: auto;
    text-align: right;
}
.seeshop div p {
    font-size: 12px;
    color: #222222;
    line-height: 21px;
    height: 21px;
    text-align: center;
}
.seeshop div p+p {
    text-align: center;
}
.seeshop {
    float: right;
    overflow: hidden;
     margin-top: 0px; 
    margin-right: 30px;
    width: 160px;
    height: 200px;
    text-align: center;
}
.seeshop img {
    width: 112px;
    height: 112px;
    display: block;
    float: initial;
    background: #fff;
    text-align: center;
    margin: auto;
}
.chat_div{
	display: none;
}
