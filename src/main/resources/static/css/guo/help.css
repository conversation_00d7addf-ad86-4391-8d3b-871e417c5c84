body{ background: unset;}
.ghelp{ margin: 30px auto 20px auto; width: 1170px !important; background: #FFFFFF; border: 1px solid #EDEDED;border-radius: 10px; box-sizing: border-box; padding: 30px 45px;}
.gtit{ font-size: 26px; color: #333333; font-weight: 500;}
.gtit span{ color: #999999; font-size: 13px; margin-left: 10px;}
.gmain{ width: 100%; display: flex; margin-top: 25px;}
.gml{ flex: 1;}
.gmr{ flex: 1; box-sizing: border-box;
    padding-left: 80px;}
.gmli{ width: 100%; margin-bottom: 37px;}
.glt{ color: #999999; font-size: 18px;}
.glt em{ font-style: normal; color: #FF3838; font-size: 17px; margin-left: 5px; font-weight: bolder;}
.glt span{ font-style: normal; color: #FF5200; font-weight: bold; font-size: 20px;}
.gtl i{ font-size: 18px;}
.gmp2{ width: 100%; margin-top: 17px; position: relative;}
.gmp{ width: 100%; margin-top: 17px;}
.gmp span{
    color: #333333;
    border: 1px solid #EEEEEE;
    border-radius: 2px;
    line-height: 38px;
    text-align: center;
    width: 106px;
    display: inline-block;
    margin-right: 12px; margin-bottom: 16px; font-size: 16px;}
.gmp span:nth-child(4){ margin-right: 0px !important;}
.gmp span:nth-child(4){ margin-right: 0px !important;}
.iv{ border: 1px solid #FFD6C2 !important; color: #FF5200 !important; font-weight: bolder !important;}

/*进度条*/
.range-slider {  height: 30px; padding: 0px; margin: 6px 0 52px 0; border-bottom: 8px solid #E4E4E4;}
.range-slider input[type="range"] {  width: 100%;
    height: 7px; border-radius: 5px; outline: none; float: left; -webkit-appearance: none; position: relative;
    top: 30px; background-color: rgba(0,0,0,0);
}
.range-slider input[type="range"]::-webkit-slider-thumb {
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.15s ease-in-out 0s;
    -webkit-appearance: none;
    appearance: none;
    background-image: url("../../images/guo/button.png"); background-repeat: no-repeat; background-size: 100% 100%; background-position: center center; border-radius: 50%; border: 1px solid #FF5200;}
.slbox{ width: 97%; position: relative;}
.slbp{
    position: absolute;
    top: -38px;
    left: 0;
    right: 0;
    margin: 0 auto;
    display: block;
    text-align: center;
    color: #FF6F28;
    font-size: 22px;
}
.bgtop{ height: 30px;  position: absolute; bottom: 0px; left: 0; box-sizing: border-box; border-bottom: 8px solid #FF5200;}
/*.range-slider { background-color: #fff; height: 30px; padding: 0px; margin: 17px 0 38px 0; }
.range-slider input[type="range"] {  width: 100%;
    height: 7px; border-radius: 5px; outline: none; float: left; -webkit-appearance: none; position: relative;
    top: 12px; background-color: #E4E4E4;
}
.range-slider input[type="range"]::-webkit-slider-thumb {
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.15s ease-in-out 0s;
    -webkit-appearance: none;
    appearance: none;
    background-image: url("../../images/guo/button.png"); background-repeat: no-repeat; background-size: 100% 100%; background-position: center center; z-index: 2;
}
.slbox{ width: 100%; position: relative;    }
.slbp{
    position: absolute;
    top: -38px;
    left: 0;
    right: 0;
    margin: 0 auto;
    display: block;
    text-align: center;
    color: #FF6F28;
    font-size: 22px;
}
.bgtop{ height: 7px; width: 0%;  position: absolute; bottom: 11px; left: 0; box-sizing: border-box; background-color: #fff;background-color: #712a2a; z-index: 1; border-radius: 5px;}*/
.sl{ position: absolute; left: 0; bottom: -38px;  color: #999999; font-size: 14px; }
.sr{ position: absolute; right: -1px; bottom: -38px; color: #999999; font-size: 14px;}
.gtitt{ color: #999999; font-size: 18px;}
.gtitt i{
    font-style: normal;
    width: 214px;
    display: inline-block;
    margin-bottom: 30px;}
.gtitt i em{
    font-style: normal;
    color: #FF3838;
    font-size: 17px;
    margin-left: 5px;
    font-weight: bolder;
}
#viewBox{ position: absolute; bottom: 5px; right: 5px; color: #999999; font-size: 13px;}
.gmp2 textarea{width: 100%;box-sizing: border-box;padding: 8px;border: 2px solid #EEEEEE;font-size: 14px;color: #545252;line-height: 21px;text-align: justify;border-radius: 4px;resize: none;}
.gmp2 textarea:focus {
    outline: none !important;
    border-color: #FF5200;
}
/*//服务协议*/
.checkagreeInput .checked {
    background: url("../../images/guo/ty.png") top center !important;
    background-size: 100% 100% !important;
    border-radius:unset;
}
.checkagreeInput .checkimg{ background: url("../../images/guo/wty.png") top center; background-size: 12px 12px; border-radius:unset;}
.bt1{ background: #FF5200; border-radius: 8px; text-align: center; color: #fff; font-size: 24px; line-height: 60px;
    cursor:pointer; font-weight: bold;}
/*弹窗*/
.iph{ position: fixed; width: 100%; height: 100vh; top: 0; left: 0; background-color: rgba(0,0,0,.8); z-index: 99999; display: none; }
.ite{ width: 325px; height: 240px; background-color: #fff; border-radius: 2px; position: fixed; top: 30vh; left: 0; right: 0; margin: 0 auto; box-sizing: border-box; padding: 16px; }
.ith{ color: #333333; font-weight: bold; font-size: 16px; margin-bottom: 0px;}
.iip{ with: 100%;}
.iip input{ border: 1px solid #EEEEEE; border-radius: 4px; width: 100%; line-height: 38px; margin-bottom: 11px;padding-left: 13px; box-sizing: border-box;}
.sh{ line-height: 38px; margin-top: 6px;}
.iip2{ with: 100%; display: flex; justify-content:space-between;}
.iip2 input{ border: 1px solid #EEEEEE; border-radius: 4px; width: 60%; line-height: 38px; margin-bottom: 11px; box-sizing: border-box;padding-left: 13px; box-sizing: border-box;}
.iip2 em{ display: inline-block; color: #FF6F28; font-style: normal;line-height: 37px;  box-sizing: border-box;    border: 1px solid #FF6F28;height: 38px; padding: 0 10px; border-radius: 2px; min-width: 82px; text-align: center;}
.sle{ position: absolute; left: 0; bottom: -15px; color: #FF6F28; font-size: 11px; font-weight: 500;}
.sri{ position: absolute; right: 0; bottom: -15px; color: #999999; font-size: 11px; font-weight: 500;}
.closex{ position: absolute; top: 0px; right: 0px;  background-image: url("../../images/guo/alertX.png"); background-repeat: no-repeat; background-size: 14px 14px; background-position: center center; width: 36px; height: 36px;
    cursor:pointer; }
.bt2 { color: #FFFFFF; background-color: #FF5200; border-radius: 4px; line-height: 46px; font-size: 16px; text-align: center; -webkit-user-select: none;}
.sh{ line-height: 42px; margin-top: 6px;}
/*--*/
.dias{ width: 100%; height: 28px; font-size: 12px; color: red;}
.success_tt{
    width: 400px;
    height: 223px;
    border: 1px solid #ccc;
    position: fixed;
    top: 230px;
    background-color: #fff;
    padding: 25px;
    box-sizing: border-box;
    font-size: 14px;
    z-index: 1001;
    left: 0;
    right: 0;
    margin: 0 auto;     border-radius: 10px;}
span{-webkit-user-select: none;    cursor:pointer;}
.sutii{ width: 100%; margin-top: 21px;}
.sutii span{ display: block;    color: #333333;      font-size: 15px;     font-size: 16px;
    text-align: center; line-height: 30px;}
.su,.dia{ display: none}
.btst{     position: absolute;
    bottom: 42px;
    width: 81%;
    right: 0;
    left: 0;
    margin: 0 auto;}
.dlog{ font-size: 16px; text-align: center;}
.a2{ display: none;}
.sj{ width: 100%;}
.sj input{border: 2px solid #EEEEEE;    border-radius: 4px;     line-height: 30px;
    margin: 0 7px; box-sizing: border-box;
    padding: 0 10px; width: 150px;
    color: #FF5200; font-size: 15px;
    font-weight: bold; text-align: center;}
.sj input::-webkit-input-placeholder {
    font-weight: 300 !important; font-size: 14px !important;
}



.glt{     position: relative;
    height: 20px;
    margin-bottom: 30px;}
.glt i{     display: inline-block;
    position: absolute;
    left: 0;
    top: 3px;}
.glt span{    display: inline-block;
    left: 126px;
    top: 3px;
    position: absolute;}
.my_xl { font-size: 16px !important;}


.at2,.at5,.at17{ min-height: 56px;}
.at3,.at7,.at11,.at15,.at18{ min-height: 168px;}
.at16{ min-height: 112px;}
.ig{ width: 32px; height: 32px; display: block; margin: 0 auto;}

.c{ height: 0;
    border: 0px solid #EEEEEE !important;}