.payment{
    background: url(https://static.fangxiaoer.com/web/images/sy/house/payBg.png) no-repeat;
    background-size: 100%;
    padding-left: 0;
    width: 1188px;
    margin: 0 auto;
    color: #fff;
    height: 118px;
    /*box-shadow: 3px 4px 10px rgba( 154, 31, 2,0.5 );*/
}
.payL{
    float: left;
    width: 440px;
    text-align: center;
}
.payR{
    width: 730px;
    float: left;
}
.payLp1{
    margin: auto;
    color: #ffffff;
    font-size: 20px;
    margin-top: 33px;
    width: 200px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    text-align: center;
    margin-left: 130px;
}
.payLp2{
    font-size: 30px;
    color: #ffe59b;
    font-weight: bold;
    margin-top: 18px;
    margin-left: 15px;
}
.payC{
    width: 460px;
    margin-left: 75px;
    font-size: 18px;
    color: #ffffff;
    float: left;
    position: relative;
    height:130px;
}
.payCp1{
    margin-top: 25px;
    margin-bottom: 12px;
}
.payCp2{
    position: absolute;
    bottom:20px;
}
.payCp2Time{
    font-weight: bold;
}
.payStar{
    float: left;
    margin-top: 20px;
}
.payStar p{
    font-size: 14px;
    color: #ffe59b;
    text-align: center;
    margin-top: -10px;
}
.payStarImg{
    margin-top: 10px;
}
.payCyImg{
    margin-top: 35px;
}
.poPup{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999999;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
}
.popPay{
    width: 500px;
    height: 410px;
    position: fixed;
    left: 50%;
    top: 50%;
    margin-top: -192px;
    margin-left: -250px;
    background: #ffffff;
    text-align: center;
    border-radius: 10px;
    display: none;
    z-index: 9999999;
}
.popPayP1{
    font-size: 20px;
    color: #333333;
    font-weight: bolder;
    margin-top: 35px;
    margin-bottom: 4px;
    width: 100%;
    text-align: center;
    display: block;
    height: 21px;
    line-height: 21px;
}
.popPayP2{
    font-size: 16px;
    color: #333333;
    font-weight: bolder;
    width: 100%;
    text-align: center;
}
.popPayP3{
    font-size: 16px;
    color: #999;
    margin-top: 2px;
    margin-bottom: 20px;
    text-align: center;
    height: 17px;
    line-height: 17px;
}
.popPayP4{
    font-size: 20px;
    color: #333333;
    margin-top: 16px;
    height: 21px;
    line-height: 21px;
}
.popPayP5{
     font-size: 16px;
     color: #333333;
     margin: 32px 0px;
    height: 17px;
    line-height: 17px;
}
.popCet .checkagreeInput .checkimg {
    display: block;
    width: 14px;
    height: 14px;
    border-radius: 2px;
    margin-top: 3px;
    margin-right: 10px;
    cursor: pointer;
    background: #ff5200;
}
.popCet .checkagreeInput .checked {
    background: #ff5200 url(https://static.fangxiaoer.com/m/static/images/kimgChec.png) 2px center no-repeat !important;
    background-size: 11px 8px !important;
}
.payClose{
    position: absolute;
    right: 20px;
    top: 20px;
}
.popMess{
    overflow: auto;
    height: 300px;
}
.popCode .item-left{
    display: inline-block;width: 118px;height: 118px;padding: 20px;border: 1px solid #45B31D;margin-right: 20px;
}
.popCode .item-right{
    display: inline-block;width: 118px;height: 118px;padding: 20px;border: 1px solid #0BAAED;
}
.popCode .left-img{
    display: inline-block;width: 160px;text-align: center;display:flex;float:left;align-items:center;margin-left: 90px;margin-top: 5px;font-size: 16px;color: #000;
}
.popCode .right-img{
    display: inline-block;width: 160px;text-align: center;display:flex;float:right;align-items:center;margin-right: 70px;margin-top: 5px;font-size: 16px;color: #000;
}
.item-left img,.item-right img{
    width:100%;
    height:100%;
}
.popCet input{
    width: 410px;
    height: 44px;
    border: 1px solid #f0f0f0;
    border-radius: 5px;
    margin-bottom: 12px;
    padding-left: 12px;
    color: #666666;
    font-size: 14px;
}
.popCet .popSure{
    height: 42px;
    background-color: #ff5200;
    color: #ffffff;
    font-size: 14px;
    margin-top: 10px;
}
.checkagreeInput span{
    color: #ff5200;
    cursor: pointer;
}
.checkagreeInput{
    text-align: left;
}
.favorable-price{
    background: url(https://static.fangxiaoer.com/web/images/sy/house/favorable_price.png) no-repeat;
    padding-left: 0;
    width: 1188px;
    margin: 0 auto;
    color: #fff;
    height: 144px;
    /*box-shadow: 3px 4px 10px rgba( 154, 31, 2,0.5 );*/
}
.favorable{
    background: url(https://static.fangxiaoer.com/web/images/sy/house/favorable_noprice.png) no-repeat;
    padding-left: 0;
    width: 1188px;
    margin: 0 auto;
    color: #fff;
    height: 144px;
    /*box-shadow: 3px 4px 10px rgba( 154, 31, 2,0.5 );*/
}
.favorable-price .border{
    float: left;
    /*padding-left: 50px;*/
    width: 350px;
    margin-top: 45px;
    position: relative;
    margin-left: 90px;
}
.favorable-price .border p span {
    color: #ffe59b;
    display: inline-block;
    font-size: 22px;
    margin-bottom: 3px;
    font-weight: bold;
}
.favorable-price .border p{
    color: #fff;
    font-size: 18px;
    line-height: 25px;
    margin: 0 auto;
    /* font-weight: bold; */
    /* width: 130px; */
    float: left;
}
.favorable-price .border .prices{
    width: auto;
    line-height: 10px;
}
.favorable-price .border .prices span {
    font-size: 58px;
    font-weight: bold;
    font-family: dinot-bold;
    color: #fff;
    margin-top: 20px;
}