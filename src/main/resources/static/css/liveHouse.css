@charset "utf-8";

body {
    color: #333;
    font-size: 14px;
}
/*直播列表*/
.new_live_ul{
    overflow: hidden;
    margin: 20px 0;
}
.new_live_li{
    width: 278px;
    float: left;
    overflow: hidden;
    float: left;
    margin-right: 19px;
    position: relative;
    margin-bottom: 40px;
}
.new_live_ul li:last-child {
    margin-right: 0;
}
.new_live_li a{}
.new_live_li img{
    width: 278px;
    height: 156px;
}
.new_live_type{
    position: absolute;
    left: 11px;
    top: 10px;
    color: #fff;
}
.new_live_type i{
    width: 51px;
    height: 23px;
    background: #FF3B00;
    border-radius: 0px 0px 0px 0px;
    opacity: 1;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 23px;
    display: inline-block;
    text-align: center;
}
.new_live_type span{
    height: 23px;
    background: rgb(0 0 0 / 60%);
    border-radius: 0px 0px 0px 0px;
    display: inline-block;
    padding: 0 9px;
}
.new_live_txt{
    width: 278px;
    line-height: 20px;
    margin-top: 14px;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #111111;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    white-space: pre;
    display: inline-block;
    text-overflow: ellipsis;
}
.live_type{}
.lIcon1{
    background: #11B488 !important;
}
.lIcon2{
    background: #FF3B00 !important;
}
.lIcon3{
    background: #FF7F00  !important;;
}
.new_live_ul li:nth-child(4n){margin-right: 0}
.liveBottom{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
}
.width1170{width: 1170px;margin:0 auto;margin-bottom: 150px;}
