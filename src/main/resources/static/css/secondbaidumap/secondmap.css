@charset "utf-8";
/*
制作人：张琪
修改时间：2018-12-3
用处：二手房地图找房左侧列表
*/
.searchEsfMap{
    margin: 10px 0 10px 10px;
    width: 530px;
    overflow: hidden;
    position:  relative;
}
.searchEsfMap .searchMapInput{
    padding-left:5px;
    width: 430px;
    height: 33px;
    line-height: 33px;
    border: 1px solid #ddd;
    float: left;
}
.searchEsfMap .searchMapBtn{
    width: 80px;
    height: 35px;
    /* height: 33px; */
    line-height: 35px;
    float: left;
    background: #ff5200;
    border: none;
    color: #fff;
    margin-left: -1px;
}
#deleteButton{
    position: absolute;
    top: 8px;
    right: 105px;
    cursor: pointer;
    display: none;
}
#map {
    min-width: 750px;
}

.mapNav {
    background: #313131;
    width: 60px;
    color: #fff;
    text-align: center;
    float: left;
    height: 100%;
}

.mapNav li {
    cursor: pointer;
    height: 85px;
}

.mapNav li:hover {
    background: #535353
}

.mapNav .hover {
    background: #535353
}

.mapNav i {
    display: block;
    margin: 0 auto;
    text-align: center;
    width: 20px;
    height: 18px;
    padding-top: 29px;
}

.mapNav .img1 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -58px;
}

.mapNav .img2 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -95px;
}

.mapNav .img3 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -126px;
}

.mapNav .img4 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -22px;
}

.mapNav .img5 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px 18px;
}

.mapNav .img6 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 20px 22px;
}

.mapNav .img7 {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") -30px -19px;
}

.mapList {
    float: left;
    width: 420px;
    height: 100%;
    overflow: hidden;
    position: relative;
}

.mapList .mapSelect {
    background: #eee;
    color: #666;
    /* height: 60px; */
    /* position: absolute; */
    width: 100%;
    overflow:  hidden;
    padding-bottom: 20px;
}

.mapList .mapSelect>div {
    padding-left: 8px;
    float: left;
    margin-top: 13px;
    /* position: relative; */
}

.mapList .mapSelect>div>p {
    width: 120px;
    height: 23px;
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 103px -274px no-repeat #fff;
    display: block;
    padding: 3px 5px;
    cursor: pointer;
    overflow: hidden;
}
.mapList .mapSelect .noHouseMap {
    text-align:  center;
    width:  100%;
    background:  #fff;
    padding: 50px 0;

}
.mapList .mapSelect .noHouseMap h4{}
.mapList .mapSelect .noHouseMap p{
    width:  100%;
}
.mapList .mapSelect .noHouseMap p a{
    color: #ff5200;
    font-weight:  bold;
    font-size: 14px;
}
.mapList .mapSelect>div>ul {
    position: absolute;
    z-index: 9999;
}

.mapList .mapSelect>div>ul>li {
    width: 113px;
    font-size: 13px;
    height: 23px;
    background: #fff;
    display: block;
    padding: 3px 5px;
    cursor: pointer;
}

.mapList .mapSelect>div>ul>li:hover {
    background: #f3f3f3;
    color: #ff5200;
}

.mapList .mapHouseList {
    overflow-y: scroll;
    margin-top: 60px;
}

.mapList .mapHouseList li {
    padding: 8px 10px;
    overflow: hidden;
    border-bottom: 1px solid #eee;
    min-height: 118px;
    cursor: pointer;
    position: relative;
}

.mapList .mapHouseList li a {
    text-decoration: none;
    color: #333;
}

.mapList .mapHouseList li a:hover {
    #333;
}

.mapList .mapHouseList li a img {
    width: 160px;
    float: left;
    top: 50%;
    position: absolute;
    margin-top: -59px;
    left: 10px;
}

.mapList .mapHouseList li a div {
    padding-left: 176px;
    float: left;
    width: 200px;
    line-height: 24px;
    margin-top: -6px;
}

.mapList .mapHouseList li a div>h1 {
    font-size: 16px;
    line-height: 30px;
    float: left;
}

.mapList .mapHouseList li a div>p {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.mapList .mapHouseList li a div>span {
    display: block;
    color: #fff;
    float: left;
    background-color: #8099af;
    padding-left: 10px;
    padding-right: 10px;
    margin-top: 1px;
    border-radius: 3px;
    margin-left: 6px;
}

.mapList .mapHouseList li a div>b {
    display: block;
    font-weight: 400;
}

.mapList .mapHouseList li a div>b span {
    color: #ff5200;
}

.mapSaleHouseList {
    margin-top: 13px;
    overflow-y: scroll;
    /* position: relative; */
    /* float:  left; */
}

#loading {
    position: absolute;
    top: 50%;
    left: 234px;
    display: none;
}

.mapSaleHouseList ul li {
    cursor: pointer;
    overflow: hidden;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin: 0 10px;
}
.mapSaleHouseList ul li .move{
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") -35px -449px;
    display: block;
    width: 15px;
    height: 18px;
    cursor: pointer;
}
.mapSaleHouseList ul li>a {
    overflow: hidden;
}

.mapSaleHouseList ul li>a:hover {
    color: #333;
    text-decoration: none;
}

.mapSaleHouseList ul li>a>img {
    width: 160px;
    display: block;
    float: left;
    margin-right: 10px;
}

.mapSaleHouseList ul li>a>div {
    float: left;
    width: 320px;
}

.mapSaleHouseList ul li>a>div h1 {
    font-size: 16px;
    display: block;
    width: 280px;
    position: relative;
}
.mapSaleHouseList ul li>a>div h1 i{
    position:absolute;
    top: 5px;
    right: -30px;
}
.mapSaleHouseList ul li>a>div h1 p{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.mapSaleHouseList ul li>a>div>div {
    line-height: 30px;
    overflow: hidden;
}

.mapSaleHouseList ul li>a>div>div p {
    display: block;
    float: left;
    max-width: 230px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.mapSaleHouseList ul li>a>div>div span {
    text-align: right;
    float: right;
    font-size: 12px;
}

.mapSaleHouseList ul li>a>div>div h4 span {
    font-size: 18px;
    color: #ff5200;
    font-weight: 400;
}

.mapSaleHouseList ul li>a>div>div div {
    float: left;
    border: 1px solid #eee;
    padding: 0 6px;
    margin-right: 4px;
    line-height: 26px;
    font-size: 12px;
}

.mapSaleHouseList .tese_1 {
    border: 1px #f5b3bb solid;
}

.mapSaleHouseList .tese_2 {
    border: 1px #91d9ff solid;
}

.mapSaleHouseList .tese_3 {
    border: 1px #bde980 solid;
}

.mapSaleHouseList .tese_4 {
    border: 1px #c7adef solid;
}

#baiduMap {
    float: left;
    height: 100%;
}

.showPlat,
.showRegion {
    width: 76px;
    padding-top: 14px;
    height: 62px;
    display: block;
    background: rgba(255, 82, 0, 0.9);
    color: #fff;
    text-align: center;
    border-radius: 100%;
}
.showPlat{
    width: 76px;
    height: 64px;
    padding-top: 12px;
}
.showPlat span,
.showRegion span {
    display: block;
    white-space: nowrap;
    text-align: center;
    width: 200px;
    margin-left: -65px;
    margin-top: 1px;
    font-size:  14px;
    font-weight: normal;
}
.showPlat b,
.showRegion b {
    font-size:  12px;
    font-weight: bold;
    line-height: 17px;
    width:  auto;
    display: block;
    line-height: 9px;
}
.showPlat s,
.showRegion s {
    font-size:  12px;
    display: block;
}
.showPlat .shadow,
.showRegion .shadow {
    text-shadow: 1px 1px 1px #000;
}

.showRegion span{
    margin-left: -63px;
    margin-top: -5px;
    font-size:  14px;
    font-weight: normal;
}
.showRegion b{
    font-weight: bold;
    font-size:  12px;
    width:  auto;
    display:  block;
    line-height: 14px;
}
.showRegion s{
    font-size:  12px;
    font-weight:  bold;
}
.showHouse,
.showsubWay,.newShowHouse{
    position: absolute;
    height: 20px;
    padding: 2px 5px;
    line-height: 20px;
    white-space: nowrap;
    font-size: 12px;
    background-color: #ff5200;
    display: block;
    border-radius: 3px;
    text-align: center;
    color: #fff;
}

.newShowHouse b{background:#fff;border:1px solid #41a8f3;}
.newShowHouse b s{/* border-left:1px solid #999; */display: inline-block;margin-left：4px;width:  1px;background: #949494;height: 10px;line-height: 24px;margin: 0 5px;}

.showsubWay {
    background: #fff;
    color: #666;
    border: 1px solid #e66a6a;
    z-index: 10000;
}

.showHouseHover b,
.showsubWay b ,.newShowHouse b{
    background-color: #fff;
    position: absolute;
    height: 18px;
    top: 0px;
    left: 100%;
    overflow: hidden;
    display: blok;
    color: #666;
    padding: 2px 4px;
    font-weight: 400;
}

.showHouseHover,
.showsubWayHover {
    z-index: 20000!important;
    position: absolute;
    height: 20px;
    padding: 2px 5px;
    line-height: 20px;
    white-space: nowrap;
    font-size: 12px;
    background-color: #41a8f3;
    display: block;
    border-radius: 3px;
    text-align: center;
    color: #fff;
}

.showHouseHover ,.newShowHouse:hover{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.showsubWayHover,.newShowHouse:hover {
    background-color: #e66a6a;
}

.showHouse b,
.showsubWay b,.newShowHouse b{
    display: none;
}
.showHouseHover b{background:#fff;border:1px solid #41a8f3;}
.showHouseHover b s{/* border-left:1px solid #999; */display: inline-block;margin-left：4px;width:  1px;background: #949494;height: 10px;line-height: 24px;margin: 0 5px;}
.newShowHouse:hover b{
    display: block;
}
.showHouse div,
.showsubWay divÃ¯Â¼Å’ .newShowHouse div{
    background: url(https://static.fangxiaoer.com/web/images/ico/map/mapicon.png) 0px -317px no-repeat;
    position: absolute;
    width: 11px;
    height: 10px;
    top: 24px;
    left: 45%;
    overflow: hidden;
}

.showsubWay div {
    background: url("https://static.fangxiaoer.com/web/images/ico/map/mapicon.png") 0px -359px no-repeat;
}

.showHouseHover div,
.showsubWayHover div .newShowHouse:hover{
    background: url(https://static.fangxiaoer.com/web/images/ico/map/mapicon.png) 0px -330px no-repeat;
    position: absolute;
    width: 11px;
    height: 10px;
    top: 24px;
    left: 45%;
    overflow: hidden;
}

.showsubWayHover div {
    background: url(https://static.fangxiaoer.com/web/images/ico/map/mapicon.png) 0px -380px no-repeat;
}

.hint {
    position: fixed;
    top: 0;
    left: 0;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b60.png");
    width: 100%;
    height: 100%;
    z-index: 99999999;
}

.hint dl {
    width: 396px;
    margin: 0 auto;
    border-radius: 10px;
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -198px;
    margin-top: -160px;
}

.hint dl dt {
    text-align: right;
}

.hint dl dt img {
    cursor: pointer;
}

.hint dl dd {
    background: #fff;
    overflow: hidden;
}

.hint dl dd p {
    font-size: 20px;
    text-align: center;
    padding: 30px;
    line-height: 30px;
}

.hint dl dd img {}

.hint dl dd a {
    display: inline-block;
    width: 50%;
    float: left;
    text-align: center;
    box-sizing: border-box;
    line-height: 56px;
    font-size: 16px;
    text-decoration: none;
}

.hint dl dd a:hover {
    color: #666
}

.bw{float:none !important;}
.ac_results ul{height:auto !important; max-height:408px !important}


/* 地图找房列表左侧已选求购展示 */
#map .selected_condition{ padding-top: 10px;border-bottom: 1px solid #ededed}
#map .selected_condition h1{font-size: 12px;float: left;margin-left: 20px;line-height: 26px;}
#map .selected_condition p{float: left;line-height: 24px;border: 1px solid #e6e5e5;padding-right: 1px;margin-right: 4px;width: 92px;margin-bottom: 10px;}
#map .selected_condition p img{margin-left: 5px;padding-top: 1px;display: block;float: left;cursor: pointer;}
#map .selected_condition p label{
    display: block;
    width: 56px;
    float: left;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    margin-left: 8px;
}
#map .cancelAll{float: left; padding-left: 17px;background-image: url("https://static.fangxiaoer.com/web/images/ico/map/Trash.png");background-position: left center; background-repeat: no-repeat;margin-left: 5px;cursor: pointer}
#map .selected_condition .box{float: left;width: 474px;}

#map .form_h{/* width: 800px; */margin:0 auto;font-size: 14px;/* overflow:  hidden; */margin-bottom: 5px;}
#map .form_h .form_l{width: 110px;text-align:right;float:left;margin-right: 10px;font-size: 14px;line-height: 38px;}
#map .form_h .form_l i{color:red;font-size:20px;margin:0 5px 0 0;vertical-align: middle;}
#map .form_h .form_r{/* width: 300px; */text-align:left;float:left;}

#map .form_h .error{font-size:12px;color:#ff5200;height: 30px;visibility:hidden}
#map .form_h .form_r input{width: 181px;padding-left: 20px !important;height: 34px;line-height: 35px;color: #666 !important;border: 1px solid #ccc;border-radius: 3px;margin-right: 10px;}
#map .form_btn{/* width: 800px; *//* margin: 0 auto 45px; */}
#map .form_btn #submit{width: 317px;height: 32px;border: none;color: #fff;font-size: 16px;background-color: #fe6218;cursor: pointer;display: block;text-align: center;line-height: 32px;margin-left: 20px;border-radius: 2px;text-decoration:  none;}
#map .form_h .form_r input.yzm{width: 178px;float: left;margin-left: 24px;}
#map .form_h .form_r a.yzm_btn,.form_h .form_r a.yzm_btn_time{float:left;padding: 0 14px;text-align: center;line-height: 31px;font-size: 12px;color: #333;border: 1px solid #e8e8e8;background-color: #e8e8e8;margin-top: 2px;cursor: pointer;}
#map .form_h .form_r a.yzm_btn_time{display:none;}
#map .form_h .form_r .ms{width: 302px;/* height: 78px; */padding: 6px;border: 1px solid #ccc;resize:  vertical;min-height: 50px;}
/*下拉*/
#map .my_xl{height: 26px;line-height: 30px;width: 176px;padding: 4px 6px 4px 20px;position:relative;font-size: 14px;background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 175px 16px no-repeat;cursor:pointer;display:inline-block;margin-right:20px;}
#map .my_xl_txt{float:left;width: 150px;line-height: 28px;padding-right:17px;color:#666;}
#map .my_xl,.my_xl_list{border:1px solid #ccc; border-radius:4px}
#map .my_xl_txt,.my_xl_list li{text-indent:0px;overflow:hidden;}
#map .my_xl_list{z-index:99;position:absolute;top: 37px;left:-1px;z-index:88888;border-top:none;width:100%;display:none;_top:23px;margin-left:0px !important;}
#map .my_xl_list li{list-style:none;height: 30px;line-height: 31px;cursor:default;background:#fff;padding-left: 20px;color:#666;border-bottom: none;padding:  0;margin:  0;padding-left:  20px;}
#map .my_xl_list li.focus{background:#3399FF;color:#fff}
.noHouseMap>div>h4{
    text-align:  center;
}
.noHouseMap>div>p{
    text-align: center;
}
.noHouseMap>div>p>a{color:#ff5200}
.want_buy h1{
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    line-height: 18px;
    height: 18px;
    margin-left: 10px;
    padding-top: 26px;
}
.noHouseMap .form_hP{
    text-align:  left;
    padding-left:  20px;
}
.want_buy .form_r textarea{
    margin-left:  20px;
    width: 316px;
}
.want_buy{
    width: 355px;
    margin:  0 auto;
}
.noHouseMap{
    display: none;}

.village_details{
    width: 96%;
    padding-top: 13px;
    margin-bottom:  -15px;
    margin-left: 4%;
    overflow:  hidden;
    border-bottom: 1px solid #e6e5e5;
    padding-bottom:  5px;
    margin-bottom: -5px;
}
.village_details .village_left{
    line-height: 26px;
}
.village_details .su_title{
    font-size:  18px;
    color:  #333;
    font-weight: 500;
    font-weight:  bold;
}
.village_details .su_build{
    padding-left:  10px;
    font-size: 14px;
    color: #666;
}
.village_details .village_right{
    overflow:  hidden;
    width:  100%;
}
.village_details p{
    float:  left;
    width: 72%;
    overflow:  hidden;
    text-overflow: ellipsis;
    white-space:  pre;
    line-height:  28px;
    font-size: 12px;
    color: #666;
}
.village_details i{
    width: 13px;
    height: 17px;
    display:  inline-block;
    float:  left;
    margin-top:  5px;
    margin-right: 3px;
}
.village_details i img{
    width:  100%;
    height:  100%;
}
.village_details .jun_priceSpan{
    float:  right;
    padding-right:  5px;
}
.su_chainSpan{
    float:  right;
    padding-right: 5px;
}
.village_details .jun_price{
    font-size:  12px;
    color: #999;
}
.village_details .su_unitprice{
    color:  #ff5200;
    font-size: 22px;
    font-weight: 600;
    padding: 0 4px;
}
.village_details .jun_unit{
    font-size:  12px;
    color: #999;
}
.village_details .su_chain{
    font-size:  12px;
    color: #999;
}
.village_details .su_rate{
    font-size:  12px;
    color:  #ff5200;
    padding: 0 4px;
    color: #999;
}

