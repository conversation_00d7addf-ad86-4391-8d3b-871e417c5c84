@charset "utf-8";
/*
制作人：张琪
修改时间：2018-11-19
用处：sy经纪人招聘相关页面
*/


/*经纪人招聘列表页*/
.logoBanner{
    width:  1170px;
    margin:  0 auto;
}
#option p{width: 60px;text-align:  right;}
.logoBanner .bigBanner{}
.logoBanner .bigBanner img{}
.logoBanner .smallBanner{
    overflow:  hidden;
    margin: 10px auto;
}
.logoBanner .smallBanner li{
    float:  left;
    width: 282px;
    height:  106px;
    border:  1px solid #f4f4f4;
    margin: 0 10px 10px 0;
    box-shadow: 3px 3px 12px #eee;
}
.logoBanner .smallBanner li:nth-child(4n){
    margin-right:  0;
}
.logoBanner .smallBanner li a{
    width: 282px;
    height: 106px;
    display:  block;
    overflow:  hidden;
    box-shadow: 3px 4px 2px #eee;
}
.logoBanner .smallBanner li a img{
    width:  100%;
}

/*招聘列表左侧*/
.agentRecruitList{
    overflow:  hidden;
    width:  1170px;
    margin:  0 auto;
    margin-top:  20px;
    margin-bottom:  20px;
}
.NoListMsg{
    width: 470px;
    display: block;
    margin: 20px 0 20px 20px;
    color: #999;
}
.agentRecruitList .arlLeft{
    width: 900px;
    float:  left;
    border:  1px solid #eee;
    margin-bottom:  20px;
}
.agentRecruitList .arlLeft .arlLeftTitle{
    overflow:  hidden;
    line-height:  46px;
    height:  46px;
    border-bottom: 1px solid #eee;
}
.agentRecruitList .arlLeft .arlLeftTitle p{
    float:  left;
    font-size:  16px;
    color:  #ff5200;
    display:  block;
    width:  110px;
    text-align:  center;
    border-right: 1px solid #eee;
}
.agentRecruitList .arlLeft .arlLeftTitle span{
    float:  right;
    padding-right:  20px;
    color:  #ff5200;
    font-size: 14px;
}
.agentRecruitList .arlLeft ul{
    padding-bottom:  20px;
}
.agentRecruitList .arlLeft ul li{
    overflow:  hidden;
    border-bottom: 1px solid #eee;
    padding: 15px 18px;
}
.agentRecruitList .arlLeft ul li>h4>a{
    display:  inline-block;
    float:  left;
}
.agentRecruitList .arlLeft ul li>a:hover{color:#333}
.agentRecruitList .arlLeft ul li:last-child{border-bottom:none}
.liaobeiICon{
    display:  inline-block;
    font-size:  14px;
    font-weight: normal;
    margin-left: 8px;
    color:  #02a0db;
}
.liaobeiICon div{
    background: none;
    overflow:  hidden;
}
.liaobeiICon span{display:block}
.liaobeiICon:hover text{color:#ff5200}
.liaobeiICon:hover .liaobeiIcon{background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue.png) 0px 1px no-repeat;}
.liaobeiICon a{ }
.liaobeiICon .liaobeiIcon{
    height:  14px;
    width:  14px;
    display:  inline-block;
    background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue.png) 0px -14px no-repeat;
    float:  left;
    margin-top: 4px;
    margin-right: 5px;
}
.liaobeiICon text{
    color: #02a0db;
}
.agentRecruitList .arlLeft ul li .arlLeftLiL{
    width:  550px;
    float:  left;
}
.agentRecruitList .arlLeft ul li .arlLeftLiL>a{
    overflow:  hidden;
    display:  block;
}
/* .agentRecruitList .arlLeft ul li>div>a:hover,.agentRecruitList .arlLeft ul li .arlLeftLiL>a:hover{text-decoration:underline} */
.agentRecruitList .arlLeft  a:hover{text-decoration:none }
.agentRecruitList .arlLeft ul li .arlLeftLiL h4{
    font-size:  16px;
    color:  #333;
    font-weight:  normal;
    overflow:  hidden;
}
.agentRecruitList .arlLeft ul li .arlLeftLiL span{
    float:  left;
}
.agentRecruitList .arlLeft ul li .arlLeftLiL span+i+span{
    width: 450px;
    overflow:  hidden;
    white-space:  pre;
    text-overflow: ellipsis;
    display:  inline-block;
}
.agentRecruitList .arlLeft ul li .arlLeftLiL i{
    float:  left;
    padding:  0 5px;
}
.agentRecruitList .arlLeft ul li .arlLeftLiL p{
    color:  #ff5200;
    line-height: 26px;
    margin-top:  5px;
}
.agentRecruitList .arlLeft ul li .arlLeftLiL p.fldy{
    color: #999;
    margin-top:  10px;
}
.agentRecruitList .arlLeft ul li .arlLeftLiL p.fldy span{
    display:  inline-block;
    padding: 0 6px;
    border: 1px solid #e2e2e2;
    margin-right: 10px;
    line-height:  24px;
    height:  22px;
    border-radius:  2px;
}
.agentRecruitList .arlLeft ul li .arlLeftLiR{
    float:  right;
    width: 300px;
    text-align:  right;
    overflow:  hidden;
}
.agentRecruitList .arlLeft ul li .arlLeftLiR h3{
    font-size:  14px;
    color:  #333;
}
.agentRecruitList .arlLeft ul li .agentRecruitListp{
    color:  #999;
    font-size: 14px;
    margin: 5px 0;
}
.agentRecruitList .arlLeft ul li .agentRecruitList span{}
.agentRecruitList .arlLeft ul li .agentRecruitList>i{}
.agentRecruitList .arlLeft ul li .agentRecruitList span i{}
.agentRecruitList .arlLeft ul li .arlLeftLiR .arlLeftBTn,.arlLeftBTn{
    margin-top: 10px;
    display:  block;
    height:  26px;
    line-height:  26px;
    width:  95px;
    float:  right;
    border: 1px solid #ff5200;
    text-align:  center;
    border-radius: 13px;
    color: #ff5200;
    cursor: pointer;
    text-decoration:  none;
}
.bkNavFloat .arlLeftBTn{
    display:  block;
    height:  26px;
    line-height:  26px;
    width:  95px;
    float:  right;
    text-align:  center;
    border-radius: 13px;
    color: #ff5200;
    cursor: pointer;
    text-decoration:  none;
    background:  #fff;
    margin-top:  0;
}
.agentRecruitList .arlLeft ul li:hover{background: #f6f6f6;}
.agentRecruitList .arlLeft ul .arlLeftBTn:hover{background:#ff5200;color:#fff;color:#fff !important}

/*招聘列表右侧*/
.agentRecruitList .arlRight{
    float:  right;
    width:  250px;
}
.agentRecruitList .arlRight .urgentWant{
    border: 1px solid #eee;
    margin-bottom:  20px;
}
.agentRecruitList .arlRight .urgentWant h4{padding: 14px 0 14px 12px;border-bottom: 1px solid #eee;font-size:  18px;font-weight:  normal;color:  #333;}
.agentRecruitList .arlRight .urgentWant h4 i{
    display:  block;
    width:  4px;
    height:  18px;
    background:  #ff5200;
    float:  left;
    margin-top:  3px;
    margin-right:  5px;
}
.agentRecruitList .arlRight .urgentWant li{
    padding: 15px 12px 10px 12px;
    border-bottom:  1px solid #eee;
}
.agentRecruitList .arlRight .urgentWant li a:hover,.agentRecruitList .arlRight .urgentWant li a{text-decoration:none}
.agentRecruitList .arlRight .urgentWant li:laat-child{border-bottom:none}
.agentRecruitList .arlRight .urgentWant li h5{
    white-space:  pre;
    overflow:  hidden;
    width:  220px;
    text-overflow: ellipsis;
    font-size:  14px;
    color:  #333;
    font-weight:  normal;
}
.agentRecruitList .arlRight .urgentWant li p{
    color: #ff5200;
}
.agentRecruitList .arlRight .urgentWant li p.agentRecruitList{
    width: 220px;
    color:  #999;
    font-size:  12px;
    margin: 0;
}
.agentRecruitList .arlRight .urgentWant li p.agentRecruitList span{}
.agentRecruitList .arlRight .urgentWant li p.agentRecruitList>i{}
.agentRecruitList .arlRight .urgentWant li p.agentRecruitList span i{}
.agentRecruitList .arlRightBanner{}
.agentRecruitList .arlRightBanner a{display:  block;width:  250px;height:  190px;}
.agentRecruitList .arlRightBanner a img{
    width: 100%;
}


/*简历弹窗*/
.arlTcHb{
    display: none;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    background-color: #000;
    opacity:  0.6;
}
.arlTcMain{
    display: none;
    width: 414px;
    /* height: 338px; */
    background:  #fff;
    border-radius:  4px;
    position:  fixed;
    left:  50%;
    top: 50%;
    margin-left:  -227px;
    margin-top:  -169px;
    padding:  20px;
    z-index: 9999;
}
.arlTcMain h4{
    font-size:  16px;
    text-align:  center;
    color:  #333;
    margin-bottom:  20px;
}
.arlTcMain ul{}
.arlTcMain ul li{
    overflow: hidden;
    margin-bottom: 15px;
}
.arlTcMain ul li label{
    float:  left;
    display:  block;
    margin-right:  5px;
    width: 83px;
    text-align: right;
}
.selectpicker{
    width: 322px;
    height:  28px;
    line-height:  28px;
    border: 1px solid #eaeaea;
    padding:  0 5px;
}
.selectpicker option{}
.arlTcMain ul li input{
    float:  left;
    width: 315px;
    height:  26px;
    line-height:  28px;
    border: 1px solid #eaeaea;
    padding-left:  5px;
}
.arlTcMain ul li .select_box{width:96px;height:25px;float: left;margin: 9px 8px 0 8px;position:relative;text-align: left;}
.arlTcMain ul li .select_info{background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/sale_xl.gif) no-repeat;width: 73px;height:25px;line-height:25px;font-size:12px;padding: 0 20px 0 7px;cursor: pointer;}
.arlTcMain ul li .select_box ul{position:absolute;background: #fff;width: 94px;border: 1px solid #ccc !important;top: 23px;display: none;overflow-y: overlay;max-height: 200px;}
.arlTcMain ul li .select_box ul li{height: 30px;line-height: 30px;border:none;/* padding-left: 6px; */cursor: pointer;font-size: 12px;}
.arlTcMain ul li .select_box ul li a{width: 93%;height:100%;display:block;padding-left: 6px;}
.arlTcMain ul li .select_box ul li a:hover{background: #f5f5f5;}
.arlTcMain ul li.select_box ul li:hover a{color: #333;text-decoration:none;}
.arlTcMain ul li .select_box:hover ul{display:block}

.arlTcMain ul li .my_xl{height: 26px;line-height: 26px;width: 110px;padding: 0px 6px;position:relative;font-size: 14px;background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 107px 11px no-repeat;cursor:pointer;display:inline-block;float: left;margin-right:20px;z-index:99;}
.arlTcMain ul li .my_xl_txt{float:left;width: 133px;line-height: 26px;padding-right:17px;}
.arlTcMain ul li .my_xl,.my_xl_list{border:1px solid #ccc;border-radius: 2px;}
.arlTcMain ul li .my_xl_txt,.my_xl_list li{text-indent:0px;overflow:hidden;}
.arlTcMain ul li .my_xl_list{position:absolute;top: 34px;left:-1px;z-index:88888;/* border-top:none; */width:100%;display:none;_top:23px;margin-left:0px !important;}
.arlTcMain ul li .my_xl_list li{list-style:none;height: 30px;line-height: 30px;cursor:default;background:#fff;padding-left: 6px;}
.arlTcMain ul li .my_xl_list li.focus{background:#3399FF;color:#fff}
.arlTcMain ul li .my_xl_input,.tese_input{position: absolute;top: -999999px;}

.arlTcMain ul li input#code{
    width:  185px;
}
.arlTcMain ul li i{
    color: red;
}
.arlTcMain ul li span{
    display:  block;
    width: 72px;
    height:  28px;
    float:  left;
    margin-right:  14px;
    text-align:  center;
    line-height:  28px;
    border: 1px solid #eaeaea;
    cursor:  pointer;
}
.arlTcMain ul li span.duigou{background: url(/agentrecruitimg/duigou.png) top center no-repeat;color: #ff5200;}
.arlTcMain ul li  b{
    width: 101px;
    background: #f1f2f4;
    font-weight: 400;
    display: block;
    float: left;
    text-align: center;
    cursor: pointer;
    width:  130px;
    line-height:  28px;
    height:  28px;
    margin-left: 20px;
}
.arlTcMain ul li b.fxe_validateCode{
    display:none;
}
.arlTcMain ul .arlTcMainBtn{
    width:  246px;
    height: 32px;
    background:  #ff5200;
    color:  #fff;
    display:  block;
    text-align:  center;
    border-radius:  2px;
    margin:  0 auto;
    line-height:  32px;
    font-size:  14px;
    cursor:  pointer;
    text-decoration: none;
    margin-top: 22px;
}
.arlTcMain .closeReport {
    font-style: normal;
    font-size: 12pt;
    color: #999;
    background: url(https://static.fangxiaoer.com/m/images/sale/reportClose.jpg);
    display: block;
    width: 16px;
    height: 16px;
    background-size: cover;
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    opacity:  0.6;
}
/*搜索框*/
.searchEsfMap{
    width:  1170px;
    margin: 0px auto 0 auto;
    overflow: hidden;
}
.searchEsfMap .searchMapInput{
    padding-left:5px;
    width: 430px;
    height: 44px;
    line-height: 44px;
    border: 1px solid #ddd;
    float: left;
}
.searchEsfMap .searchMapBtn{
    width: 99px;
    height: 46px;
    /* height: 33px; */
    line-height: 46px;
    float: left;
    background: #ff7200;
    border: none;
    color: #fff;
    margin-left: -1px;
    font-size: 16px;
    cursor:  pointer;
}
#deleteButton{
    position: absolute;
    top: 8px;
    right: 105px;
    cursor: pointer;
    display: none;
}
/*公司详情页*/
.companyMain{
    width:  1170px;
    margin:  0 auto;
    border:  1px solid #eee;
    margin-bottom:  20px;
}
.companyBanner{
    width:  1170px;
    margin: 0 auto;
    height:  126px;
    overflow:  hidden;
    background: url(/agentrecruitimg/companyBg.jpg);
    /* margin-bottom:  20px; */
}
.companyBanner h4{
    color:  #fff;
    font-size:  30px;
    line-height:  126px;
    padding-left:  20px;
    font-weight: 500;
}
.companyintroduce{
    width:  1170px;
    margin: 0 auto;
    overflow:  hidden;
    padding: 20px 0;
}
.companyintroduce .L{
    width: 874px;
    float:  left;
    padding: 20px;
}
.companyintroduce .L h4{
    font-size:  20px;
    margin-bottom:  20px;
    font-weight: 600;
    color: #333;
}
.companyintroduce .L p{
    font-size:  14px;
    line-height:  30px;
    color:  #666;
}
.companyintroduce .R{
    float:  right;
    width:  236px;
    height:  174px;
    overflow: hidden;
    margin-right: 20px;
}
.companyintroduce .R img{
    width:  100%;
    height:  100%;
}
/* 地图 */
.arlRightMap{
    border:  1px solid #eee;
}
.arlRightMap h4{
    font-size:  14px;
    font-weight: normal;
    color:  #333;
    padding-left:  5px;
    white-space: pre;
    text-overflow: ellipsis;
    width: 245px;
    padding-top:  5px;
    overflow: hidden;
}
.bigAllmap h4{
    width: 97%;
    padding-left: 2%;
    font-size:  18px;
}
.arlRightMap p{
    color:  #666;
    padding-left:  5px;
    /* white-space:  pre; */
    /* text-overflow: ellipsis; */
    width: 245px;
    overflow: hidden;
}
.arlRightMap>div{/* height:250px */}
.hoverMap{
    display:  block;
}
.arlRightMap>div>div{
    position:  relative;
}
/* .hoverMap:hover .hoverMapShow{display:block} */
.hoverMapShow{
    border-radius: 1px;
    position: absolute;
    right:  0;
    bottom: 0;
    z-index:  9;
    background: rgba(0, 0, 0, 0.5);
    padding:  0 5px;
    font-size:  12px;
    color:  #fff;
    cursor:  pointer;
}
.hoverMapShow:hover{color: #fff;background: rgba(0, 0, 0, 0.7);}

/*职位介绍*/
.agentPosition{
    width: 1130px;
    margin:  0 auto;
    padding: 20px;
    overflow:  hidden;
    margin-bottom:  20px;
    background:#f6f7fc;
}
.agentPosition:hover{

}
.agentPosition .L{
    overflow:  hidden;
    width:  950px;
    float:  left;
}
.agentPosition .L h4{
    font-size:  22px;
    color:  #333;
    display:  inline-block;
    float:  left;
}
.agentPosition .L>span{
    font-size:  14px;
    margin-left:  15px;
}
.agentPosition .L>div{
    line-height:  30px;
}
.agentPosition .L>div>p{
    color: #ff5200;
    line-height: 26px;
    margin-top: 5px;
    display:  inline-block;
    font-size:  16px;
}
.agentPosition .L>div>p.agentRecruitListp{
    color: #999;
    margin: 5px 0;
    font-size:  14px;
    margin-left:  20px;
}
.agentPosition .L>div>p.agentRecruitListp span{}
.agentPosition .L>div>p.agentRecruitListp>i{}
.agentPosition .L>div>p.agentRecruitListp span i{}
.agentPosition .L>p.fldy{
    color: #999;
    margin-top: 10px;
}
.agentPosition .L>p.fldy span{
    display: inline-block;
    padding: 0 6px;
    border: 1px solid #e2e2e2;
    margin-right: 10px;
    line-height: 24px;
    height: 22px;
    border-radius: 2px;
    background:  #fff;
}
.agentPosition .R .liaobeiICon{
    float:  right;
    margin-top: 36px;
    margin-left: 14px;
}
.agentPosition .R .arlLeftBTn{
    margin-top: 35px;
    display: block;
    height: 26px;
    line-height: 26px;
    width: 95px;
    float: right;
    border: 1px solid #ff5200;
    text-align: center;
    border-radius: 13px;
    color: #ff5200;
    cursor: pointer;
    text-decoration: none;
}
.agentPosition .R .arlLeftBTn:hover,.arlLeftBTn:hover{background:#ff5200;color:#fff}
.companyRLogo{
    width: 250px;
    height:  200px;
    margin-bottom:  20px;
}
.companyRLogo img{
    width:  100%;
    height:  100%;
}
.arlLeft .txtMain{
    width: 860px;
    padding:  20px;
    line-height: 30px;
    font-size:  14px;
}
.arlLeft .txtMain h5{
    font-size:  14px;
    font-weight:  normal;
    color:  #333;
}
.arlLeft .txtMain p{
    color:  #666;
}
.bigAllmap{
    display: none;
    position:  relative;
    width: 800px;
    height: 500px;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-left: -400px;
    margin-top: -250px;
    background: #fff;
    z-index: 99999;
}
.heiMuAllmap{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    display: none;
}
.allmapBigShow{width: 800px;height: 500px;}
.BMapLabel{background:rgba(255, 6, 0, 0.6) !important; padding:2px 8px !important;display:block !important;color:#fff !important;border-radius:2px !important;}
.bigAllmapClose{
    z-index: 99999999999999999999;
    cursor: pointer;
    top: 10%;
    display:  block;
    width: 20px;
    height: 20px;
    float:  right;
}
.bigAllmapClose img{width:  100%;height: 100%;}
.showTitle{
    display: none;}
#allmap{height:250px;width:248px}
/*页面滚动后固定页面*/
.fixbkb{position:fixed;top:0;left:0;background: rgba(255, 82, 0, 0.9);}
.bkNavFloat{width:100%;height: 42px;padding-top:4px;margin: 0px 0;z-index: 999;overflow:  hidden;color:  #fff;}
.bkNavFloat h4{
    float:  left;
    font-size: 18px;
    width: auto;
}
.bkNavFloat>div{
    overflow:  hidden;
    padding-top: 5px;
}
.container div div{
    float: right;
}
.container div>p{
    font-size: 14px;
    float:  left;
    line-height:  30px;
    margin-left: 40px;
}
.container div i{
    background: url(https://static.fangxiaoer.com/web/images/ico/sign/fixbkb-phone.png)no-repeat;
    display:  block;
    width: 22px;
    height: 22px;
    float:  left;
    margin-right: 15px;
}
.container div div p{}