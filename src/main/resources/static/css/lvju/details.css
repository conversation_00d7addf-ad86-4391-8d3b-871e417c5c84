body {
    overflow-x: hidden
}

.content {
    width: 1170px;
    margin: auto
}

.decomLocation {
    margin: 15px 0 30px 0
}

.detemperature {
    margin-left: 16px;
    font-size: 22px;
    color: #272727;
    display: inline-block
}

.deSelection {
    overflow: hidden
}

.dephone {
    float: right;
    margin-top: -24px
}

.btn_1 {
    width: 180px;
    height: 44px;
    color: #fff;
    background-color: #ff6100;
    border: 0;
    border-radius: 3px;
    margin-left: 20px;
    cursor: pointer;
    margin-top: 0
}

.btn_1:hover {
    color: #fff
}

.dephone p {
    font-size: 14px;
    color: #666
}

.dephone span {
    font-size: 24px !important;
    color: #272727 !important;
    margin-left: 8px !important;
    font-family: "dinot-bold";
    background: #fff !important
}

.dephone b {
    font-size: 14px;
    font-weight: 400;
    position: relative;
    top: -3px;
}

.deInsale i {
    font-size: 14px;
    color: #fff;
    padding: 4px 16px;
    background-color: #ff6100;
    border-radius: 3px;
    margin-right: 2px
}

.deInsale span {
    padding: 4px 8px;
    font-size: 14px;
    color: #596c91;
    background-color: #f3f5f7;
    margin-left: 2px;
    position: relative;
    top: 3px;
}

.deInsale {
    margin-top: 10px;
    margin-bottom: 10px
}

.deCategory {
    overflow: hidden;
    margin-top:;
    margin-top: 15px
}

.deCategoryUl1 > li {
    float: left;
    margin-right: 26px
}

.deCategoryUl1 > li > span {
    font-size: 16px;
    color: #272727;
    font-family: "dinot-bold"
}

.deCategoryUl1 > li > span > b {
    font-size: 30px;
    color: #ff5200
}

.deCategoryUl1 > li > span > i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat;
    background-size: 100% 100%;
    padding-left: 5px;
    text-align: center
}

.deCategoryUl2 > li {
    overflow: hidden;
    margin-bottom: 10px
}

.deCategoryUl2 > li:last-child {
    margin-bottom: 0
}

.deCategoryUl2 > li > span {
    font-size: 16px;
    color: #272727;
    font-family: "dinot-bold";
    float: right;
    margin-left: 5px;
    margin-top: -3px
}

.deCategoryUl2 > li > span > b {
    font-size: 18px;
    color: #333
}

.deCategoryUl2 > li > span > i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat;
    background-size: 100% 100%;
    padding-left: 5px;
    text-align: center
}

.deCategorySpan {
    margin-top: 12px;
    display: inline-block;
    width: 98px;
    height: 28px;
    text-align: center;
    border: 1px solid #ebebeb;
    line-height: 30px;
    padding-right: 20px;
    font-size: 16px;
    color: #333 !important;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/deGD.png) no-repeat 95px 12px
}

.deAddress img {
    margin-bottom: 0;
    margin-right: 0;
    margin-top: 4px
}

.deAddGD {
    float: right;
    font-size: 16px;
    color: #ff5200
}

.deAddress {
    margin-top: 14px;
    float: right
}

.comtittle {
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    padding-left: 40px;
    background-color: #f5f5f5;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin-top: 12px;
    z-index: 999;
    margin-bottom: 20px
}

.comtittle li {
    float: left;
    /* width: 14.28% */
}

.comtittle span {
    cursor: pointer
}

.deselect {
    color: #ff5200;
    border-bottom: 2px solid #ff5200;
    padding: 0 5px 12px 5px
}

#deVideo {
    width: 782px;
    width: 100;
    object-fit: fill
}

.deImage {
    overflow: hidden
}

.deImageR {
    float: right;
    width: 376px
}

.deImageL {
    float: left;
    position: relative;
    height: 424px
}

.dexiaoguo {
    position: absolute;
    display: block;
    width: 100%;
    line-height: 40px;
    bottom: 0;
    height: 40px;
    color: #fff;
    background-color: #00000070;
    text-align: center
}

.deImageR img {
    width: 376px;
    height: 206px
}

.deImageR div {
    height: 206px;
    margin-bottom: 12px;
    position: relative
}

.deBuilding {
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #ededed;
}

.deDoordetailsdiv {
    float: left;
    margin-top: 41px;
    width: 359px;
    border-right: 1px solid #ebebeb
}

.deDoordetailsdiv h1:hover {
    color: #ff5200
}

.deCategoryDiv {
    position: absolute;
    position: absolute;
    background: #fff;
    padding: 10px 24px;
    margin-left: -48px;
    margin-top: 11px;
    z-index: 9;
    box-shadow: 0 2px 7px -1px #63636463;
    display: none;
    border: 1px solid #ededed;
}

.deBedroom a:nth-last-child(2) {
    display: inline-block;
    border-right: 1px solid #eaeaea;
    height: 14px;
    line-height: 1
}

.H3 {
    margin-left: 24px;
    float: left;
    font-size: 20px;
    color: #272727
}

.deBedroom {
    float: right;
    font-size: 14px;
    color: #666
}

.deBedroom span {
    margin-right: 20px;
}

.deBedroommore {
    font-size: 14px;
    color: #666;
    margin-left: 14px
}

.deBedroommore:hover {
    color: #ff5200
}

.deBedroommore {
    float: right;
    margin-right: 20px;
    cursor: pointer;
    display: inline-block;
    background: url("https://static.fangxiaoer.com/web/images/lvjuImg/gdspan.png") no-repeat 59px 26px;
    /* background-size: 100% 100%; */
}

.deDoordetails {
    overflow: hidden
}

.deDoordetailsL {
    width: 584px;
    float: left
}

.deDoordetailsIMG {
    display: inline-block;
    width: 150px;
    height: 150px;
    float: left;
    margin: 30px 28px 30px 46px;
    overflow: hidden;
    text-align: center
}

.deDoordetailsIMG img {
    height: 150px
}

.deDoordetailsdiv h1 {
    font-size: 20px;
    color: #272727;
    font-weight: bold;
    margin-bottom: 18px;
    display: inline-block;
}

.deDoordetailsdiv1 {
    font-size: 14px;
    color: #666;
    margin-bottom: 26px
}

.deDoordetailsdiv2 {
    font-size: 14px;
    color: #ff5200
}

.deDoordetailsdiv2 span {
    font-size: 22px;
    font-family: "dinot-bold";
    font-weight: bold
}

.deDoordetailsdiv1 span {
    margin-right: 30px
}

.deDoormodel {
    border: 1px solid #ededed;
    margin-top: 16px;
}

.kitmore {
    margin-right: 20px
}

.kitshead {
    border-bottom: 1px solid #ededed;
    height: 60px;
    line-height: 60px
}

.kitsdiv {
    width: 890px;
    float: left
}

.kitsdis li {
    margin-top: 20px;
    margin-left: 26px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ededed
}

.kitsdis li:last-child {
    border-bottom: 0
}

.kitsdis h1 {
    font-size: 16px;
    color: #272727;
    font-weight: bold
}

.kitsdis h1:hover {
    color: #ff5200
}

.kitsdis li:last-child p {
    border-bottom: 0
}

.kitsdis p {
    width: 800px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    font-size: 14px;
    color: #666;
    margin-top: 15px;
    padding-right: 18px;
    height: 42px
}

.jinnangdisB {
    display: inline-block;
    border-radius: 2px;
    width: 72px;
    height: 26px;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    line-height: 26px;
    text-align: center;
    margin-right: 10px
}

.jdY {
    background-color: #ff9d00;
    box-shadow: 0 3px 7px 0 #f4b44df0
}

.jdB {
    background-color: #5189f9;
    box-shadow: 0 3px 7px 0 #759ff2c2
}

.jdR {
    background-color: #fd494e;
    box-shadow: 0 3px 7px 0 #ec767ab3
}

.kitsdiv {
    border: 1px solid #ededed
}

.kitscont {
    margin-top: 28px
}

.kitsDT {
    width: 250px;
    float: right;
    border: 1px solid #ededed
}

.kitsDThead {
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #ededed
}

.kitsDTdiv {
    margin-left: 20px;
    margin-right: 14px;
    border-left: 1px solid #ededed;
    padding-left: 14px;
    margin-top: 24px;
    height: 331px
}

.H3DT {
    margin-left: 20px
}

.spanDT {
    margin-right: 20px
}

.kitsDTdiv li {
    position: relative
}

.kitsDTdiv li img {
    position: absolute;
    left: -18px
}

.timeDT i {
    font-size: 16px;
    color: #666;
    font-family: "dinot-bold"
}

.DT {
    font-size: 14px;
    color: #668ae9;
    margin-left: 14px
}

.JF {
    font-size: 14px;
    color: #fe1324;
    margin-left: 14px
}

.KP {
    font-size: 14px;
    color: #22ac38;
    margin-left: 14px
}

.timeDT {
    position: absolute;
    top: -8px
}

.DTnews {
    font-size: 14px;
    line-height: 26px;
    color: #333;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden
}

.DTnews:hover {
    color: #ff5200
}

.DTnews i {
    margin-left: -7px
}

.DTdiv {
    padding-top: 24px;
    margin-bottom: 15px
}

.kitsdis {
    padding: 6px 0
}

.demapcity {
    margin-top: 26px;
    border: 1px solid #ededed
}

.demapH3 {
    height: 60px;
    line-height: 60px
}

.map {
    margin-top: 26px;
    border: 1px solid #ededed
}

.map .title {
    width: 9.2rem;
    margin: 0 auto
}

.map .title {
    width: 9.2rem;
    margin: 0 auto
}

.map .title h1 {
    font-size: .4rem;
    color: #333;
    line-height: 1.2rem
}

.map .content {
    width: 1020px;
    height: 170px;
    position: relative;
    top: 45px;
    overflow: hidden;
    background-color: #ffffffd1
}

.content_box1 {
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/map.jpg) no-repeat;
    height: 260px;
    background-size: 100% 100%;
    overflow: hidden
}

.already {
    display: block
}

.not {
    display: none
}

.map .content .btn {
    width: 430px;
    margin-top: 45px;
    margin-left: 50px
}

.map .content .btn ul li {
    width: 25%;
    float: left;
    cursor: pointer
}

.map .content .btn ul li .pic img {
    margin: 0 auto;
    display: block
}

.map .content .btn ul li .pic .already {
    display: none
}

.map .content .btn ul li .text h1 {
    line-height: 26px;
    margin-left: 10px
}

.map .content .btn ul li .text {
    margin-top: .21333rem;
    padding-bottom: .4rem
}

.map .content .btn ul .color .already {
    display: block !important
}

.map .content .btn ul .color .not {
    display: none
}

.map .map_sun .content_sun {
    border-radius: 5px
}

.map .map_sun .content_sun .left {
    float: left
}

.map .map_sun .content_sun .left h1 {
    font-size: 26px;
    font-weight: bold;
    color: #272727;
    margin-left: 50px;
    margin-right: 35px
}

.map .map_sun .content_sun .left .show {
    overflow: hidden;
    height: 40px !important;
    margin-top: 10px
}

.show li {
    height: 40px !important;
    line-height: 40px
}

.show li h1, h2 {
    float: left
}

.pic {
    float: left
}

.text {
    float: left;
    font-size: 16px;
    color: #272727
}

.map .map_sun .content_sun .left .lunbo {
    position: relative
}

.map .map_sun .content_sun .left h2 {
    font-size: 16px;
    color: #666
}

.map .map_sun .content_sun .left ul {
    display: none
}

.map .map_sun .content_sun .left ul:nth-child(-n+1) {
    display: block
}

.map .map_sun .content_sun .right {
    float: right;
    margin-right: 0;
    margin-top: -35px;
    margin-right: 60px
}

.map .map_sun .content_sun .right img {
    display: block;
    margin: 0 auto
}

.map .map_sun .content_sun .right h1 {
    font-size: 16px;
    color: #596c91;
    text-align: center;
    margin-top: 15px
}

.dewherePlay {
    border: 1px solid #ededed;
    margin-top: 25px;
    margin-bottom: 25px
}

.deWPswiper {
    height: 246px
}

.swiper-slide {
    height: 246px;
    position: relative;
    cursor: pointer
}

.swiper-slide img {
    width: 380px;
    height: 246px
}

.deslidBg {
    position: absolute;
    width: 100%;
    height: 130px;
    margin-l: 20px;
    bottom: 0;
    background: linear-gradient(#ffffff08, #050505e0)
}

.deslidBgP {
    margin-left: 20px;
    margin-top: 70px;
    font-size: 18px;
    color: #fff;
    font-weight: bold
}

.deslidBgSpan {
    display: inline-block;
    margin-left: 20px;
    width: 315px;
    font-size: 14px;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.swiper-button-prev, .swiper-container-rtl .swiper-button-next {
    left: 0;
    right: auto;
    height: 78px;
    width: 40px;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/swileft.png);
    background-size: 100% 100%
}

.swiper-button-next, .swiper-container-rtl .swiper-button-prev {
    right: 0;
    left: auto;
    height: 78px;
    width: 40px;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/swiright.png);
    background-size: 100% 100%
}

.decommentshead {
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #ededed
}

.decomments {
    margin-top: 25px;
    border: 1px solid #ededed
}

.none {
    height: 140px
}

.none .askbutton {
    margin-top: 53px;
    margin-right: 490px
}

.decommentPJ {
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #ededed;
    margin: 0 30px
}

.decommentPJdiv {
    float: right
}

.decommentPJ span {
    font-size: 14px;
    color: #333
}

.decommentPJ button {
    width: 130px;
    height: 30px;
    border: 0;
    color: #fff;
    font-size: 14px;
    background-color: #fe6116;
    border-radius: 2px;
    margin-left: 14px;
    cursor: pointer
}

.decommentuseImg {
    width: 50px;
    height: 50px;
    background: #ededed;
    overflow: hidden;
    border-radius: 90px;
    margin-top: 6px
}

.decommentuse {
    width: 190px;
    text-align: center;
    float: left;
    position: absolute;
    top: 50%;
    margin-top: -47px
}

.decommentusechi {
    height: 94px;
    border-right: 1px dashed #e7e7e7
}

.decommentusechi p {
    font-size: 14px;
    color: #333;
    margin-top: 10px
}

.decommentMore {
    float: right;
    width: 910px
}

.decommentMoreP {
    font-size: 14px;
    color: #333;
    width: 595px;
    margin-top: 30px;
    margin-bottom: 16px
}

.decomMoreImg img {
    width: 110px;
    height: 80px;
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
}

.decomDetails {
    color: #666;
    margin-bottom: 30px
}

.decomDetails i {
    font-size: 12px
}

.decomDetails1 {
    font-size: 14px;
    margin-left: 404px;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/quanwen.png) no-repeat right;
    padding-right: 15px;
    cursor: pointer
}

.decomDetails2 {
    font-size: 14px !important;
    float: right;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/fenxiang.png) no-repeat left !important
}

.userSynthesizeli {
    position: relative;
    border-bottom: 1px solid #ededed
}

.userSynthesizeli:last-child {
    border-bottom: 0
}

.deAnswerImg {
    width: 90px;
    float: left
}

.deAnswerImg img {
    margin-left: 50px;
    margin-top: 30px
}

.deAnswerCon {
    float: left;
    margin-top: 30px;
    width: 1078px;
    padding-bottom: 5px
}

.deAnswerCon h1 {
    font-size: 18px;
    color: #333;
    font-weight: bold;
    margin-bottom: 13px;
    width: 1020px;
    word-wrap: break-word
}

.deAnswerConPho {
    margin-bottom: 12px
}

.deAnswerConPho span {
    font-size: 14px;
    color: #333
}

.deAnswerConPho i {
    margin-right: 70px;
    float: right;
    font-size: 14px;
    color: #666
}

.deAnswer li:last-child {
    padding-bottom: 30px
}

.deAnswerConQuestion {
    width: 960px;
    padding: 25px;
    background-color: #f5f5f5;
    font-size: 14px;
    color: #333;
    line-height: 26px
}

.deQueAnswer {
    border: 1px solid #ededed;
    margin-top: 25px
}

.deConsulting {
    padding-top: 30px;
    padding-left: 46px;
    padding-bottom: 30px;
    border: 1px solid #ededed;
    border-top: 0
}

.deConsulting p {
    font-size: 12px;
    color: #333;
    line-height: 30px
}

.Where_play {
    display: none;
    position: fixed;
    width: 100%;
    z-index: 10000;
    height: 100%;
    top: 0;
    text-align: center;
    background-color: #0000008a
}

.Where_play_02 {
    display: none;
    position: fixed;
    width: 100%;
    z-index: 99999;
    height: 100%;
    top: 0;
    text-align: center;
    background-color: #0000008a
}

.expandBg {
    width: 660px;
    margin: auto;
    background-color: #000;
    margin-left: 120px
}

.expandBg_02 {
    width: 660px;
    background-color: #000;
    margin-left: 120px
}

.Where_play .swiper-slide {
    height: 100%
}

.Where_play_02 .swiper-slide {
    height: 100%
}

.Where_play img {
    width: 100%;
    height: 440px
}

.Where_play_02 img {
    width: 100%;
    height: 440px
}

.Where_play_bg {
    position: absolute;
    top: 50%;
    margin-top: -311px;
    width: 900px;
    height: 680px;
    left: 50%;
    background-color: #000;
    margin-left: -450px;
}

.swiIl {
    display: inline-block;
    position: inherit;
    width: 48px
}

.swiHuxing {
    font-size: 14px;
    color: #fff;
    margin-top: 30px;
    margin-bottom: 30px;
}

.WhereH1 {
    text-align: left;
    color: #fff;
    font-weight: bold;
    font-size: 20px;
    margin-top: 23px;
    margin-bottom: 8px
}

.WhereP {
    text-align: left;
    font-size: 14px;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3
}

.sn {
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/spR.png);
    position: fixed;
    right: 50%;
    margin-right: -520px
}

.sp {
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/snL.png);
    position: fixed;
    left: 50%;
    margin-left: -520px
}

.bigClo {
    width: 18px !important;
    height: 18px !important;
    float: right;
    margin-top: 22px;
    margin-right: 25px
}

.open {
    background: url("https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png") no-repeat right
}

.userSynthesize {
    width: 100%
}

.userSynthesize .li {
    overflow: hidden;
    border-bottom: 1px solid #ededed;
    position: relative
}

.userSynthesize td {
    padding-top: 20px;
    padding-bottom: 20px
}

.userSynthesize .contentRight {
    float: left;
    width: 198px;
    margin: 31px 0
}

.userSynthesize .contentRight .headerImg {
    position: relative
}

.userSynthesize .contentRight .headerImg .headerImges {
    width: 50px;
    height: 50px;
    border-radius: 50px;
    overflow: hidden;
    margin: 0 auto;
    position: relative
}

.userSynthesize .contentRight .headerImg.hover .ueserName {
    padding-top: 10px
}

.userSynthesize .contentRight .headerImg .ueserName {
    text-align: center
}

.userSynthesize .contentRight .headerImg .headerImges img {
    width: 100%;
    height: 100%
}

.userSynthesize .contentLeft {
    float: left;
    width: 650px;
    padding-left: 40px;
    position: relative;
    border-left: 1px #ededed dashed
}

.userSynthesize .contentLeft .contentInfoMmore {
    width: 500px;
    position: relative;
    overflow: hidden
}

.userSynthesize .contentLeft .contentInfoMmore li {
    border: 0;
    line-height: 30px
}

.userSynthesize .contentLeft .contentBottom {
    margin: 30px 0 20px 0;
    overflow: hidden
}

.bdshare-button-style0-16 {
    zoom: 1;
    margin-right: 25px
}

.userSynthesize .contentLeft .contentBottom .contentShare {
    font-size: 12px;
    color: #666;
    float: right;
    background: url("https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon2.jpg") no-repeat 0 0;
    padding-left: 20px;
    padding-right: 19px;
    margin-top: 5px
}

.userSynthesize .contentLeft .contentBottom .contentTime {
    font-size: 12px;
    color: #666;
    float: left
}

.contentBottom .contentShow {
    font-size: 14px;
    color: #666;
    cursor: pointer;
    float: left;
    padding-right: 30px
}

.contentBottom .contentShow.openAndClose.open {
    background: url("https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png") no-repeat 62px 9px;
    margin: -2px 0 0 297px
}

.contentBottom .contentShow.openAndClose {
    background: url("https://static.fangxiaoer.com/web/images/sy/house/userEvaluate2.png") no-repeat 62px 9px;
    margin: -2px 0 0 297px
}

.ov {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical
}

.content_box {
    width: 500px;
    height: 435px;
    background: #fff;
    border-radius: 10px;
    position: fixed;
    top: 50%;
    margin-top: -220px;
    left: 50%;
    margin-left: -250px
}

.yuyueP {
    font-size: 20px;
    color: #333;
    font-weight: bold;
    text-align: center;
    margin-top: 30px
}

.properties {
    width: 410px;
    margin: auto;
    height: 43px;
    border: 1px solid #f0f0f0;
    line-height: 43px;
    margin-top: 25px
}

.name, .phone, .Verification {
    margin-top: 15px;
    text-align: center;
    position: relative
}

.content_box input {
    width: 396px;
    height: 43px;
    border: 1px solid #f0f0f0;
    padding-left: 14px
}

.properties h1 {
    margin-left: 14px
}

.btn {
    text-align: center
}

.btn button {
    width: 420px;
    height: 45px;
    border: 0;
    background: #fe6116;
    color: #fff;
    font-size: 14px;
    margin-top: 35px;
    border-radius: 4px
}

.list_box {
    background: #fff;
    width: 409px
}

.list_box li {
    width: 408px;
    text-align: center;
    height: 45px;
    font-size: 14px;
    line-height: 45px;
    color: #333;
    border-bottom: 1px solid #f0f0f0
}

.cityBra {
    color: #333;
    padding: 0;
    float: left;
    width: 410px;
    width: 409px;
    margin-right: 0;
    margin-top: -90px;
    position: fixed;
    margin-left: -206px;
    font-size: 14px;
    top: 50%;
    left: 50%;
    box-shadow: 2px 5px 7px 2px #dedbdb;
    display: none;
    overflow-x: hidden
}

.properties_box {
    width: 409px;
    display: none;
    background: #fff;
    color: #333;
    position: fixed;
    box-shadow: 2px 5px 7px 2px #dedbdb;
    margin-top: -90px;
    position: fixed;
    margin-left: -206px;
    font-size: 14px;
    top: 50%;
    left: 50%;
    overflow-x: hidden
}

.properties_box li {
    width: 408px;
    text-align: center;
    height: 45px;
    font-size: 14px;
    line-height: 45px;
    color: #333;
    border-bottom: 1px solid #f0f0f0
}

.yuyueC {
    float: right;
    position: absolute;
    right: 55px;
    top: 20px
}

.delxl {
    float: right;
    margin-top: -30px;
    margin-right: 18px
}

.block {
    display: block
}

.hide {
    display: none
}

.deImageL img {
    width: 782px;
    height: 424px
}

#qrcode {
    float: left;
    height: 70px;
    margin: 0;
    width: 70px;
    margin-right: 10px;
    margin-top: 7px
}

#qrcode .layer_wei {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .85);
    display: none
}

#qrcode .layer_wei h1 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 15px
}

#qrcode .layer_wei h2 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 5px
}

.layer_wei h2 {
    float: inherit
}

.zai1 {
    box-shadow: 1px 1.732px 5px 0 rgba(254, 97, 16, 0.3);
    background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/Now.png)
}

.decommentPJ a {
    display: inline-block;
    width: 130px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border: 0;
    color: #fff;
    font-size: 14px;
    background-color: #fe6116;
    border-radius: 2px;
    margin-left: 14px;
    cursor: pointer
}

.decommentPJ a:hover {
    color: #fff
}

.none h1 {
    font-size: 14px;
    color: #999;
    float: left;
    margin-left: 250px;
    margin-top: 60px
}

.deCategoryDivSpan2 {
    display: block;
    width: 0;
    height: 0;
    /* border-left: 17px solid #ededed2e; */
    /* border-right: 17px solid #ededed2e; */
    border-bottom: 16px solid #fff;
    position: absolute;
    margin-top: -17px;
    margin-left: 116px;
    /* box-shadow: 0 0 7px -1px #63636478; */
}

.delimorePic:hover .deCategoryDiv {
    display: block
}

.infoType1 {
    font-size: 14px;
    color: #668ae9;
    margin-left: 14px;
    width: 52px;
    height: 26px;
    background: #eff3fd;
    display: inline-block;
    text-align: center;
    line-height: 26px
}

.infoType2 {
    font-size: 14px;
    color: #fe1324;
    margin-left: 14px;
    width: 52px;
    height: 26px;
    background: #fff0f1;
    display: inline-block;
    text-align: center;
    line-height: 26px
}

.infoType3 {
    font-size: 14px;
    color: #22ac38;
    margin-left: 14px;
    width: 52px;
    height: 26px;
    background: #e8f7eb;
    display: inline-block;
    text-align: center;
    line-height: 26px
}

.kitsDTdivOF {
    padding-top: 5px;
    height: 358px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    padding-bottom: 20px
}

.deDdesspan1 {
    display: inline-block;
    width: 44px;
    position: inherit;
    padding: 0;
    position: relative;
    font-size: 14px;
    top: 3px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    background: #ff5200;
    vertical-align: 5px;
    color: #fff;
    margin-left: 8px
}

.deDdesspan2 {
    background: #ffa626;
    display: inline-block;
    width: 44px;
    position: inherit;
    padding: 0;
    position: relative;
    font-size: 14px;
    top: 3px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    vertical-align: 5px;
    margin-left: 8px;
    color: #fff
}

.deDdesspan3 {
    display: inline-block;
    width: 44px;
    position: inherit;
    padding: 0;
    position: relative;
    font-size: 14px;
    top: 3px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    vertical-align: 5px;
    margin-left: 8px;
    background: #ecf0f3;
    color: #8099af
}

.infoType4 {
    font-size: 14px;
    color: #ffc001;
    margin-left: 14px;
    width: 52px;
    height: 26px;
    background: #fef4e5;
    display: inline-block;
    text-align: center;
    line-height: 26px
}

.videoImg {
    width: 102px !important;
    height: 102px !important;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -51px;
    margin-top: -51px;
    z-index: 9;
    background: url("https://static.fangxiaoer.com/web/images/lvjuImg/videoIco.png") no-repeat;
    background-size: 100% 100%
}

.videoImg:hover {
    cursor: pointer
}

.H1 {
    font-size: 32px;
    color: #272727;
    line-height: 32px;
    font-weight: bold;
    display: inline-block;
    margin-top: 5px
}
.detaiDd{
    font-size: 16px !important;
}
.deCategoryUl1Dd{
    font-size: 22px !important;
    margin-left: 5px;
}
.deDoordetailsIMGBg{
    height: 150px;
    /*background-size: 100%;*/
    background-size: cover;
    background-repeat: no-repeat;
}


.bigImgShow {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1000000;
    /* background: url(https://static.fangxiaoer.com/global/imgs/ico/b60.png); */
    background: rgba(0, 0, 0, 0.85);
}

.bigImgShow .showImg {
    overflow: hidden;
    width: 100%;
    max-width: 1170px;
    height: 75%;
    margin: 0 auto;
    margin-top: 6.5%;
}

.bigImgShow .showImg ul {
    overflow: hidden;
    width: 99999999999999px;
    height: 100%;
}

.bigImgShow .showImg ul li {
    overflow: hidden;
    position: relative;
    float: left;
    width: 1170px;
    height: 100%;
}

.bigImgShow .showImg ul li img {
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
}

.bigImgShow .close {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
}

.bigImgShow .prev {
    position: absolute;
    top: 50%;
    left: 0;
    width: 60px;
    margin-top: -57px;
    height: 114px;
    background: url(https://static.fangxiaoer.com/global/imgs/ico/white_ico1.png)no-repeat top left;
    cursor: pointer;
}
.bigImgShow .next {
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -57px;
    width: 64px;
    height: 114px;
    background: url(https://static.fangxiaoer.com/global/imgs/ico/white_ico1.png)no-repeat top right;
    cursor: pointer;
}
.expertTellK .bigImgShow {
    background: none;
    display: none;
}
.bigImgShow .showImg ul li img {
    margin-top: -220px;
    margin-left: -250px;
    width: 500px;
}
.zaiSp{
    top: 0 !important;
}