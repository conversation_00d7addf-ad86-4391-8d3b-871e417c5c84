.banner {
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/index_banner.png) no-repeat;
    width: 100%;
    height: 406px;
    min-width: 1170px;
    position: relative;
    background-position: center
}

.content {
    width: 1170px;
    margin: auto;
    margin-bottom: 30px
}

.comLocation {
    font-size: 12px;
    color: #999;
    margin: 20px 0
}

.comLocation a {
    color: #999
}

.info h1 {
    font-size: 24px;
    color: #272727;
    display: inline-block;
    font-weight: bold
}

.info h1:hover {
    color: #ff5200
}

.H1 {
    font-size: 32px;
    color: #272727;
    line-height: 32px;
    font-weight: bold;
    display: inline-block
}

.swiper-container {
    width: 100%;
    height: 100%
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center
}

.inSelSwi {
    width: 810px;
    height: 400px
}

.inSelSwi .swiper-container {
    width: 100%;
    height: 100%
}

.node {
    width: 260px;
    height: 150px;
    position: absolute;
    bottom: 0;
    right: 0
}

.activity-photos-bg {
    position: relative
}

.node_1 {
    width: 260px;
    display: inline-block;
    height: 55px;
    background-color: #00000069;
    color: #fff;
    font-size: 24px;
    line-height: 55px;
    margin-bottom: 2px
}

.node_2 {
    width: 260px;
    height: 55px;
    display: inline-block;
    color: #fff;
    font-size: 16px;
    background-color: #00000069;
    line-height: 55px
}

.inEWM {
    width: 200px;
    height: 400px;
    margin: auto;
    background-color: #fff;
    border-radius: 19px;
    margin-top: 20px;
    box-shadow: 0 -2px 22px -5px #999;
    box-shadow: 0 2px 36px 1px #99999942
}

.main_window {
    overflow: hidden;
    display: block;
    padding: 0;
    float: right;
    margin: 0 auto;
    width: 250px;
    height: 440px;
    position: absolute;
    top: 20px;
    padding-top: 0 !important
}

div {
    margin: 0;
    padding: 0
}

.cloud {
    width: 240px;
    position: absolute;
    top: 100
}

[data-type=white_1]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff
}

[data-type=white_2]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff
}

[data-type=white_3]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff
}

[data-type=white_4]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff, 80px 25px 0 10px #fff, 40px 25px 0 20px #fff, 0 40px 0 -5px #fff
}

[data-type=white_5]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff, 20px 30px 0 20px #fff, 70px 30px 0 10px #fff, 105px 40px 0 -10px #fff
}

[data-type=white_6]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff, 20px 30px 0 15px #fff, 80px 40px 0 15px #fff
}

@-webkit-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-moz-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-ms-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-o-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@keyframes linemove {
    from {
        left: 0
    }
    to {
        left: 320px
    }
}

@-webkit-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

[data-speed="1"] {
    animation: linemove1 3.01936s;
    -webkit-animation: linemove1 3.01936s
}

[data-speed="2"] {
    animation: linemove2 6.67113s;
    -webkit-animation: linemove2 6.67113s
}

[data-speed="3"] {
    animation: linemove3 1.99853s;
    -webkit-animation: linemove3 1.99853s
}

[data-speed="4"] {
    animation: linemove4 3.01936s;
    -webkit-animation: linemove4 3.01936s
}

[data-speed="5"] {
    animation: linemove2 6.20363s;
    -webkit-animation: linemove2 6.20363s
}

[data-speed="6"] {
    animation: linemove 12.46404s;
    -webkit-animation: linemove 12.46404s
}

.cloud {
    -webkit-animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    -webkit-animation-delay: -50s
}

.inSelection {
    overflow: hidden;
    height: 495px
}

.geng {
    width: 820px;
    margin: 22px 0 20px 0
}

.swiper-button-prev {
    left: 0 !important
}

.swiper-button-next {
    right: 0 !important
}

.swiper-button-prev {
    position: absolute;
    top: 50%;
    width: 47px;
    color: #fff;
    height: 47px;
    margin-top: -22px;
    z-index: 10;
    cursor: pointer;
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/swiL.png);
    background-size: 47px 47px !important;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #6e6e6d6e
}

.swiper-button-next {
    position: absolute;
    top: 50%;
    width: 47px;
    color: #fff;
    height: 47px;
    margin-top: -22px;
    z-index: 10;
    cursor: pointer;
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/swiR.png);
    background-size: 47px 47px !important;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #6e6e6d6e
}

.node {
    display: none
}

.swiper-slide-next .node {
    display: block
}

.estate {
    margin-top: 25px
}

.h1span {
    float: right;
    font-size: 14px;
    color: #333;
    line-height: 35px
}

.gengg {
    display: block;
    margin: 26px 0
}

.estateR {
    width: 640px;
    height: 290px;
    margin-right: 5px;
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/estateBg.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    float: right;
    margin-top: -320px;
    z-index: 9999;
    box-shadow: 0 3px 17px -6px #aca8a8;
    box-shadow: 3px 1px 10px 2px #aca8a836;
}

.estateimg {
    /* width: 400px; */
    height: 350px;
    float: left;
}

.estateDiv {
    width: 900px;
    height: 350px;
    margin-bottom: 10px
}

.right {
    display: block;
    width: 240px;
    float: right;
    position: relative
}

.estateCon {
    margin: 30px 38px 25px 42px
}

.inlocation {
    overflow: hidden
}

.inlocation span {
    display: block;
    float: left;
    margin-left: 12px;
    margin-top: -3px;
    font-size: 18px;
    color: #333
}

.coordinatesimg {
    float: left
}

.info {
    margin-top: 10px;
    margin-bottom: 8px
}

.info a {
    font-size: 24px;
    color: #272727;
    font-weight: bold
}

.inestate {
    float: right
}

.inestate span {
    display: inline-block;
    height: 24px;
    line-height: 24px;
    font-size: 14px;
    color: #596c91;
    padding: 0 10px;
    background-color: #f3f5f7
}

.inhouse {
    font-size: 16px;
    color: #666;
    float: left
}

.inhouse b {
    font-size: 30px;
    font-family: "dinot-bold";
    color: #ff5200
}

.inhouse i {
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/border.png) no-repeat;
    background-size: 100% 100%;
    font-size: 14px;
    display: inline-block;
    text-align: center;
    width: 27px;
    padding-left: 3px;
    height: 20px;
    margin-left: 8px
}

.inholder {
    float: right;
    margin-top: 16px
}

.inholder span {
    margin-left: 20px;
    font-size: 16px;
    color: #333;
    margin-top: 10px
}

.indetails {
    font-size: 16px;
    color: #666;
    margin-top: 15px;
    text-align: justify;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -ms-inline-flexbox;
    display: -ms-flexbox;
    box-orient: vertical;
    line-clamp: 2;
    -o---box-orient: vertical;
    -o--line-clamp: 2;
    -moz-box-orient: vertical;
    -moz-line-clamp: 2;
    -ms-box-orient: vertical;
    -ms-line-clamp: 2;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2
}

.innecessary img {
    float: left
}

.innecessary span {
    float: left;
    margin: 4px 10px 0 0;
    font-size: 16px;
    color: #333
}

.innecessary {
    margin-top: 14px
}

.inkits {
    margin-top: 20px
}

.inkits li {
    float: left;
    width: 382px;
    height: 354px;
    margin-right: 12px
}

.inkits li:last-child {
    margin-right: 0
}

.inkitsdiv {
    background-color: #f5f5f5
}

.inkitsdiv h2 {
    font-size: 18px;
    margin-left: 32px;
    margin-top: 25px;
    color: #272727;
    margin-bottom: 15px
}

.inkitsdiv p {
    margin-left: 32px;
    color: #666;
    font-size: 14px
}

.aircraft {
    width: 239px;
    height: 174px;
    background: url("https://static.fangxiaoer.com/web/images/lvjuImg/aircraft.png") no-repeat;
    background-size: 100%;
    position: fixed;
    right: 0;
    bottom: 30px;
    z-index: 9;
    animation: aircraft 5s ease-in-out, shake1 4s linear 5s infinite;
    -webkit-animation: aircraft 5s ease-in-out, shake1 4s linear 5s infinite
}

.ai {
    width: 111px;
    height: 87px;
    margin-left: 27px;
    margin-top: 31px;
    text-align: center
}

.ai h1 {
    font-size: 17px;
    color: #333;
    font-weight: bold
}

.ai p {
    font-size: 15px;
    color: #333;
    margin-top: 2px
}

.ai p span {
    font-size: 16px;
    font-weight: bold;
    color: #ff5200
}

@-webkit-keyframes aircraft {
    0% {
        transform: translate(300px, -300px);
        -webkit-transform: translate(300px, -300px)
    }
    100% {
        transform: translate(0px, 0px);
        -webkit-transform: translate(0px, 0px)
    }
}

.lvListPos {
    position: fixed
}
.estateOver{

    width: 400px;

    height: 350px;

    overflow: hidden;
}