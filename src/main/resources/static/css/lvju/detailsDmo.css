/*æˆ·åž‹å›¾housestyle*/

@charset "utf-8";
/* CSS Document */
.w1210 .w{width:1170px}
.crumbs{margin-top:39px;}
.title{
    height: 50px;
    margin: 27px  auto 13px;
    background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/title_line.gif);
    background-repeat: repeat-x;
    background-position: bottom;
}
.title p{font-size:24px; font-family:"å¾®è½¯é›…é»‘"; color:#ff6600; border-bottom:5px solid #ff6600; float:left;padding:0 16px; line-height:46px;}
.con_left{float:left;width:750px;}
.con_right{float:left;width:435px;margin-left:25px; display:_inline}

/*ä¼˜æƒ */
.privilege{height:170px; background: url(https://static.fangxiaoer.com/web/images/sy/house/view/privilege.jpg) no-repeat;margin-bottom:20px; position:relative;}
.privilege_l{width:270px;text-align:center;color:#fff; font-size:36px; font-weight:bold; line-height:170px; float:left}
/*.privilege .time{height:39px; line-height:39px;margin:8px 0;font-size:30px; color:#FFF; font-weight:normal;margin-top:20px;}
.privilege .time em{width:39px;height:39px;margin:0 2px 0 4px; background:url(https://static.fangxiaoer.com/web/images/sy/house/view/privilege_time.png);float:left; text-align:center}
.privilege .time p{float:left;}
.privilege .time i{float:left;margin:0 4px 0 2px}*/
.privilege_btn{width:122px;height:38px;margin:0 auto;}
.privilege_c{width:680px;line-height:36px; color:#f7dd83;font-size:34px; float:left; font-weight:bold;padding-top:35px;}
.privilege_r{width: 260px;float: left;height: 140px;color: #FFF;padding-top: 40px;line-height: 30px;font-size: 14px;}

.place {color: #999;margin: 10px auto;height:30px;line-height:30px;}
.place a{color:#999;}
.place a:hover{color:red;text-decoration:none;}
.place span{color:#333;}
.place .news {float: right;padding-left: 30px;color: #ff6600;background: url(https://static.fangxiaoer.com/web/images/ico/sign/news.gif) 8px 9px no-repeat}
.place .news a{color:#ff6600;}
.place .news a:hover{ text-decoration:underline;}

.pro_name{margin-bottom:15px;font-size:24px;color:#333;height: 63px;line-height:28px;overflow:  hidden;}
.pro_name p{ float:left; margin-right:19px;}
.pro_name a{float:left;/* width: 40px; */font-size:14px;color:#FFF;height: 20px;line-height: 20px;text-align:center;border: 1px solid #eff3fd;color: #668ae9;/* margin-left:10px; */border-radius:2px;background:  #eff3fd;margin-top: 10px;}
.pro_name a:hover{text-decoration:none;color:#FFF;border:1px solid #ff9766;background:#ff5200}
.box_sun{ float:left}
.pro_name .type_sun{ width:60px; height:26px; display:block; float:left; font-size:14px;  line-height:26px;; text-align:center;

    color:#fff;border-radius:3px
}
.pro_name .wait_1{background-color:#ff6100;box-shadow: 1px 1.732px 5px 0px rgba( 254, 97, 16,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/Now.png)}
.pro_name .wait_2{background-color:#fe9c02;box-shadow: 1px 1.732px 5px 0px rgba( 254, 156, 2,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/wait.png)}
.pro_name .wait_3{background-color:#8099af;box-shadow: 1px 1.732px 5px 0px rgba( 128, 153, 175,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/sell_out.png)}

.zhuce{float:right;}
.zhuce img{float:left}
.zhuce a{float:left;color:#ff5200;margin:0;border:none;border-top:1px solid #ff5200;border-bottom:1px solid #ff5200;width:auto;border-radius:0;cursor:pointer}
.zhuce a:hover{color:#900;border:none;border-top:1px solid #ff5200;border-bottom:1px solid #ff5200;}


/*åˆ‡æ¢*/
.house_move_act{}
.house_move_act ul{overflow:  hidden;height: 50px;}
.nav_house{line-height: 50px;height: 50px;text-align:center;margin-bottom:20px;background:  #f5f5f5;}
.nav_house li{float:left;margin: 0 2px;color:#ff6600;font-size:14px;font-weight:bold;line-height: 46px;}
.nav_house li.hover{color: #ff5200;text-decoration:  none;}
.nav_house li.hover a{color: #ff5200;border-bottom:  2px solid #ff5200;text-decoration:none;}
.nav_house li a{display:  inline-block;padding: 0 24px;font-size: 16px;height: 48px;}
.nav_house li a:hover{text-decoration:none}
.nav_house a.ewm{float:right;position:relative;cursor:pointer;margin-right:25px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_ewm.gif) no-repeat left;padding-left:25px}
.nav_house span{display:none;position:absolute;z-index:1000;border:1px solid #cccdd4;/* padding:15px; */background:#fff;right:-25px;width: 200px;height: 120px;}
.nav_house a.ewm:hover span{display:block;text-decoration:  none;}
.nav_house a.ewm i{color:#888;float: left;text-align: left;line-height: 31px;margin-top: 11px;}
.nav_house a.ewm img{width: 120px;margin-bottom: -14px;float: left;}
.nav_house .soucang{float:right;margin-right:20px;cursor: pointer;}
.nav_house .soucang i{float:left;width:18px;height:17px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang.gif) 0px 0px;margin: 9px 7px 0 0;}
.nav_house .soucang i.hover{background: url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang.gif) 0px -17px;}
#qrcode{float: left;height: 60px;margin:  0;width:  60px;margin-right:  10px;}

.nav_house .soucang{float:right;margin-right:20px;cursor: pointer;}
.nav_house .soucang i{float:left;width:18px;height:17px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang.gif) 0px 0px;margin: 9px 7px 0 0;}
.nav_house .soucang i.hover{background: url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang.gif) 0px -17px;}
.info_btn input{width:221px;float:left; border-radius:3px;font-size:20px;height:48px;}
.btn_1{color:#ff6600;margin-right:36px; border:1px solid #ff6600; text-align:center; width:221px; border-radius:3px;font-size:20px;height:44px; line-height:44px;}
a.btn_2{margin-right:15px; border:1px solid #27b779; float:left;width:221px; border-radius:3px;color:#27b779; font-size:20px;}
a.btn_2:hover{color:#249E6A; text-decoration:none;border:1px solid #249E6A}

/*æˆ·åž‹ç­›é€‰*/
.houseTypeYes{overflow:hidden}
.housetype{font-size:16px;color:#ddd;margin:25px auto;width:1180px;}
.housetype a{color:#333; text-decoration:none;margin-right:20px;}
.housetype a:hover{color:#900}
.housetype a.hover{color:#ff6600}
.search{position:relative;width: 1170px;margin:0 auto;}
.houseTypeYes .search{position:relative;width: 1180px;margin:0 auto;}
.search ul{width:20000px; position:absolute}
.search li{width: 148px;float:left;text-align:center;border: 1px solid #ededed;line-height: 20px;height: 52px;margin-right: 9px;}
.search_gd{width: 1105px;position:relative;height:62px;overflow:hidden;/* margin: 0 36px; */}
.search li a{color:#333;text-decoration:none;display:block;width: 115px;padding-top:7px;height:53px;position:  relative;text-align:  left;padding-left: 18px;}
.saleTypeIcon{/* position: absolute; *//* right: 5px; *//* top: 8px; */background:  #ff5200;color: #FFF;line-height: 18px;font-size: 12px;padding: 1px 2px;border-radius: 2px;width:  35px;height:  18px;text-align:  center;vertical-align: 5px;}
.saleTypeIcon1{
    background: #ff5200;
    vertical-align: 5px;
}
.saleTypeIcon2{background: #ffa626;}
.saleTypeIcon3{background: #ecf0f3;color:#8099af}
.search li:hover{border:1px solid #f60;}
.search li:hover a{color:#666}
.search li.hover{border:1px solid #f60;}
.search li.hover a{/* color:#f60 */}
.btn_left{float:left;height: 54px;width: 24px;/* margin-top: 2px; */overflow: hidden;background: url(https://static.fangxiaoer.com/web/images/ico/houseTypeicon.png);cursor:  pointer;}
.btn_right{float:right;height: 54px;width: 24px;overflow: hidden;background: url(https://static.fangxiaoer.com/web/images/ico/houseTypeicon.png) 23px -55px;/* margin-top:7px; */}
.next,.before{cursor:pointer;background-position: 40px 104px;}
.next{background-position: 22px 109px;}
.znext{background-position: -17px -54px;}
.zbefore{background-position: 0 -54px;}

/*æœç´¢*/
.forsearch{line-height:12px;font-size: 14px;margin: 22px auto;width: 1160px;}
.forsearch li{ clear:both; height: 40px; line-height: 40px;}
.forsearch li p{/*width:60px;*/float:left; color:#999}
.forsearch li a{float:left;padding:0 14px;border-left:1px solid #dfdfdf; line-height:14px; height:14px;margin-top: 13px;}
.forsearch li :nth-child(2){border-left:none;}
.forsearch li a.hover{color:#ff6600}
.forsearch li span{background:#f4f4f6; clear:both;margin:10px 0 20px;float:left;margin-left:60px;padding:8px 0; width:880px;}
.forsearch li span a{line-height:30px; border:none; height:30px;}

/*å·¦*/
.pic{width:400px;/* height:400px; */float:left;padding: 0 68px;position:relative;border-right: 1px solid #ededed;}
.pic img{width:400px;height:400px;cursor:  pointer;}
.pic .show{overflow:hidden;/* margin-top: 20px; */}
.pic .show .lunbo{ position:relative}
.pic .left_btn{background-color:#bfbfbf;position:absolute;left:0;top:136px;cursor:pointer;display:none;}
.pic .left_btn img{ width:22px;height:43px; padding:32px 14px;}
.pic .right_btn{background-color:#bfbfbf;position:absolute;right: 0;top:136px;cursor:pointer;display:none;}
.pic .right_btn img{ width:22px;height:43px;padding:32px 14px; }
.pic .right_btn:hover{ background-color:#5c5c5c}
.pic .left_btn:hover{ background-color:#5c5c5c}
.pic .number h1{text-align:center;font-size:14px;font-weight:normal;color:#333333}
.pic .show .look{background-color: rgba(0,0,0,0.5);position: absolute;right: 68px;bottom: 0;}
.pic .show .look h1{font-size: 12px;color: #fff;line-height: 30px;padding-right: 11px;padding-left: 30px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Magnifier.png);background-repeat: no-repeat;background-position: 11px;cursor: pointer;}

/*å³*/
.right{float: right;width: 530px;/* height: 375px; *//* margin-left:60px; */position:relative;/* border-left: 1px solid #ededed; */overflow:  hidden;margin-right: 50px;padding-left: 50px;}
.info{color:#808082;/* border-bottom: 1px solid #eee; */}
.info li{line-height:34px;overflow: hidden;font-size:14px;}
.info li label,.info div label{
    float: left;
    color: #999;
    line-height: 34px;
    margin-right: 30px;
    font-size: 14px;
}
.info li p{float:left;color: #666666;/* line-height: 26px; */font-size: 14px;}
.info li a{float:left; border:1px solid #eee;}
.info li span{margin-right:10px; float:left;padding: 0 20px;color:#FFF;line-height:28px;margin-top:4px;}
.info li span.t1{ background:#23b4ff;}
.info li span.t2{ background:#7cd301;}
.info li span.t3{ background:#905be0;}
.tel{color:#ff6600;	padding-left: 60px;font-family: Arial;font-weight: bold;font-size: 28px;background:url(https://static.fangxiaoer.com/web/images/ico/sign/house_tel.gif) no-repeat 2px 12px;height:69px;line-height: normal; margin-top:40px;}
.tel i{	font-size:12px;color:#a6a6a6;font-family:"å®‹ä½“";font-weight:normal;}
.tel1{float:left;margin: 10px 0 0 0px !important;}
.tel1 b{
    font-size: 28px;
    font-family: dinot-bold;
}
.tel1 i{color:#999;margin-right:10px;font-size: 14px;}
.tel1 b b{
    font-size:  14px;
    padding: 0 2px;
}
.tel2{float: left;margin: 7px 0px 0 0px !important;display:none;}
.tel2 a{width: 120px;float:left;height: 30px;background:#ff6600;border:none;border-radius:5px;color:#FFF;font-family:"å¾®è½¯é›…é»‘";font-size:14px;text-align:center;line-height: 30px;margin-left: 15px;}
.tel3{	float:left;font-size:12px;color:#a3a3a3;	font-weight:normal;background:url(https://static.fangxiaoer.com/web/images/ico/sign/time.gif) no-repeat left;padding-left:30px;margin-top:20px;width:70px;}
.price p{width:70px;float:left;font-size:14px;line-height:40px;}
.price ul{float:left;width: 430px;}
.price ul .prices {float:left}
.price li{float: left;line-height: 34px;font-size: 14px;/* padding: 0 5px; */color: #000;}
.price li img{ vertical-align: bottom;margin-bottom:8px;margin-left:5px;}
.price i{font-size:28px;color:#ff6600;font-weight: bold;font-family: dinot-bold;}
.price s{color:#333;padding:0 5px}
.price em{font-size:28px; color:#ff6600;}
.price{height:40px;padding-bottom: 10px;}
.style{cursor:pointer; font-size:24px;color:#333;/* margin: 60px auto 10px; */width: 1210px;height: 28px;font-weight: bold;line-height: 24px;overflow:  hidden;/* line-height: 28px; */margin-top: 20px;}
.style .roomTitle{
    float:  left;
}
.style:hover{
    color:#ff5200;
}
.style .titleIcon{
    float:  left;
    margin-top:  -5px;
    margin-left: 10px;
}
.style i{
    font-size: 14px;
    line-height: 26px;
    /* padding: 2px 7px; */
    /* width: 46px; */
    position: relative;
    height: 26px;
    top: 2px;
    padding: 0px 9px;
    display: inline-block;
    text-align: center;
    margin-left: 2px;
    font-weight:normal;
    color:  #668ae9 !important;
    vertical-align: 5px;
}
.style p{float:left;width: 42px;height: 48px;font-size: 12px;line-height: 44px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/greenFlag.gif) 0px 0px no-repeat;text-align: center;color: #fff;margin-right: 20px;}
.style p.sp{background-position: 0px -54px;}

/*é¡¶éƒ¨å¼¹å‡º*/
.mk{position:fixed;left: 50%;top: 50%;margin-top:-230px;margin-left:-345px;z-index:22222;display: none;background: #FFF}
.main{width:690px;height:390px;position:relative;padding-top:46px;}
.mk .main a.btn_tc:hover{color:#fff;}
.tbForm{width:330px;margin:25px auto 0; border-top:1px solid #CCC;}
.tbForm p{  line-height:40px;font-size:14px;color:#000;}
.tbForm li{width:314px; height:44px; line-height:49px; border-bottom:1px solid #CCC; border-left:1px solid #CCC; border-right:1px solid #CCC;padding:3px 0 0 15px;}
.tbForm label{line-height:40px;float: left;margin-left: 15px;}
.tbForm input.inputStyle{  z-index:1; background:none;outline:none; border:none; line-height:40px; padding:0 ;height:40px; width:236px;font-size:14px;color:#999;}

.tbForm p.yz{margin-top:4px;padding:5px 2px;border: 1px solid #F03;line-height:20px;float:right}
.tbForm p.yz a{color: #F03;}
.tbForm p.mima{text-align:center;}
.tbForm p.mima a{color:#fe5200;font-size:14px;}
.tbForm p.mima a:hover{cursor:pointer;text-decoration: none;}

.youhuixieyi{ margin-bottom:15px;width:330px;margin:0px auto;}

span.error{position: absolute;color:#F50C0C;font-size:14px;}
span.ename{top: 121px;left: 520px;}
span.emobile{top: 170px;left: 520px;}
span.eyz{top: 220px;left: 520px;}
span.epassword{top: 264px;left: 520px;}
.cha{position: absolute;top: 0px;right: -52px;}
.cha:hover{cursor:pointer;}
.tcbg{position: absolute;top: 0;left: 0;
    width: 100%;
    z-index: 1100;
    display: none;filter:alpha(opacity=70) !important;
}

/* æˆ·åž‹æš‚æ— æ—¶å ä½æç¤º */
.newHouseTypeNo{
    overflow:  hidden;
    width:  1170px;
    margin:  50px auto;
    /* display: none; */
    height: 363px;
}
.newHouseTypeNo img{
    width:  120px;
    height:  100px;
    margin-left:  50px;
    float:  left;
}
.newHouseTypeNo>div{
    float:  left;
    margin-left:  40px;
    font-size:  16px;
    margin-top:  20px;
    line-height:  30px;
    font-family: dinot-bold;
}
.newHouseTypeNo>div p{}
.newHouseTypeNo>div p span{}
.getChangeBtn{
    display:  inline-block;
    width: 126px;
    line-height: 24px;
    color:  #fff;
    cursor:  pointer;
    background:#ff5200;
    text-align:  center;
    border-radius: 2px;
    float:  left;
    margin-top: 8px;
}
.getChangeBtn:hover{background:#ff5200;color:#fff}

/* æˆ·åž‹ */
.huxingMain{
    /* height: 438px; */
    border: 1px solid #ededed;
    padding: 30px 0;
    margin-top:  25px;
    overflow: hidden;
    margin-bottom: 30px;
}

.huxing_wei{float:left;width:215px;line-height:  14px !important;margin-top: 30px;}
.huxing_wei label{line-height: 14px !important;}
.type_wei{float:left;width:215px;line-height: 14px !important;margin-top: 30px;}
.type_wei label{line-height: 14px !important;}
.jian_wei{float:left;width:215px;line-height: 14px !important;margin-top: 30px;}
.jian_wei label{line-height: 14px !important;}
.zhengf_wei{float:left;width:215px;margin-top: 20px;}
.layer_box{position: fixed;left: 0;top: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0.5);z-index: 100000;display: none;}
.layer_box table{width: 100%; height: 100%;;}
.layer_box table .layer{width: 900px;height: 710px;background-color: #000;margin: 0 auto;position: relative;}
.layer_box table .layer ul li img{max-width: 600px;}
.layer_box table .layer .close{position: absolute;;top: 20px; right: 20px; cursor: pointer;}
.layer_box table .layer ul li{width: 600px; height: 600px;}
.layer_box .layer .left_btn{position: absolute;left: 40px;top: 50%;}
.layer_box .layer .right_btn{position: absolute;right: 40px;top: 50%;}
.layer_box .layer .lunbo{position: relative;}
.layer_box .layer .left_btn img{margin-top: -40px;cursor: pointer;}
.layer_box .layer .right_btn img{margin-top: -40px;cursor: pointer;}
.layer_box .layer .photo{overflow: hidden;width:  600px;margin:  0 auto;}
.layer_box .layer .photo ul li{ float: left;}
.layer_box .layer .photo ul li img{display: block; margin: 0 auto;}
.layer_box .layer h1 {
    text-align: center;
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    padding-top:23px;
    padding-bottom:20px
}
.layer_box .layer .left_btn .hover{display:none}
.layer_box .layer .right_btn .hover{display:none}
.layer_box .layer .left_btn:hover img{display:none}
.layer_box .layer .left_btn:hover .hover{display:block}

.layer_box .layer .right_btn:hover img{display:none}
.layer_box .layer .right_btn:hover .hover{display:block}
.huxing_p{ width:437px}
.number h1{ margin-left:227px;
    font-size:14px;
    font-weight:normal}

.pic .look{background-color: rgba(0,0,0,0.5);position: absolute;right: 68px;bottom: 0;}
.pic .look h1{font-size: 12px;color: #fff;line-height: 30px;padding-right: 11px;padding-left: 30px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Magnifier.png);background-repeat: no-repeat;background-position: 11px;cursor: pointer;}
.layer_box table{width: 100%; height: 100%;;}


/* è¯­éŸ³æˆ·åž‹äºŒç»´ç  */
#voiceCode{
    width: 91px;
    height: 120px;
    float: right;
    position: absolute;
    top: 3px;
    left: 425px;
    text-align: center;
}

#voiceCode h6{
    font-size: 12px;
    color: #333333;
    line-height: 17px;
    /* font-weight: 100; */
    margin-top: 2px;
}

#voiceCode img{
    width: 71px;
    height: 71px;
}
/* è¯­éŸ³æˆ·åž‹äºŒç»´ç  */
.content {
    width: 1170px;
    margin: auto;
}

.decomLocation {
    margin: 15px 0 30px 0;
}

.detemperature {
    margin-left: 16px;
    font-size: 18px;
    color: #272727;
    margin-top: 8px;
    display: inline-block;
}

.deSelection {
    overflow: hidden;
}

.dephone {
    float: right;
}

.btn_1 {
    width: 180px;
    height: 44px;
    color: #ffffff;
    background-color: #ff6100;
    border: none;
    border-radius: 3px;
    margin-left: 20px;
    cursor: pointer;
}

.dephone p {
    font-size: 14px;
    color: #666666;
}

.dephone span {
    font-size: 24px;
    color: #272727;
    margin-left: 8px;
    font-family: "dinot-bold";
}

.dephone b {
    font-size: 20px;
    font-family: "ÃƒÂ¥Ã‚Â¾Ã‚Â®ÃƒÂ¨Ã‚Â½Ã‚Â¯ÃƒÂ©Ã¢â‚¬ÂºÃ¢â‚¬Â¦ÃƒÂ©Ã‚Â»Ã¢â‚¬Ëœ";
    font-weight: 400;
}

.deInsale i {
    font-size: 14px;
    color: #ffffff;
    padding: 4px 16px;
    background-color: #ff6100;
    border-radius: 3px;
    margin-right: 2px;
}

.deInsale span {
    padding: 4px 8px;
    font-size: 14px;
    color: #596c91;
    background-color: #f3f5f7;
    margin-left: 2px;
}

.deInsale {
    margin-top: 10px;
}

.deCategory {
    overflow: hidden;
}

.deCategoryUl1>li {
    float: left;
    margin-right: 26px;
}

.deCategoryUl1>li>span {
    font-size: 16px;
    color: #272727;
    font-family: "dinot-bold";
}

.deCategoryUl1>li>span>b {
    font-size: 30px;
    color: #ff5200;
}

.deCategoryUl1>li>span>i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat;
    background-size: 100% 100%;
    /* margin-left: 5px; */
    padding-left: 5px;
    text-align: center;
}

.deCategoryUl2>li {
    /* float: left; */
}

.deCategoryUl2>li>span {
    font-size: 16px;
    color: #272727;
    font-family: "dinot-bold";
}

.deCategoryUl2>li>span>b {
    font-size: 30px;
    color: #ff5200;
}

.deCategoryUl2>li>span>i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat;
    background-size: 100% 100%;
    /* margin-left: 5px; */
    padding-left: 5px;
    text-align: center;
}

.deCategorySpan {
    margin-top: 10px;
    display: inline-block;
    width: 118px;
    height: 30px;
    text-align: center;
    border: 1px solid #ebebeb;
    line-height: 30px;
    font-size: 16px;
    color: #333333 !important;
}

.deCategoryUl2 {
    display: none;
}

.deAddress img {
    margin-bottom: -5px;
    margin-right: 5px;
}

.deAddGD {
    float: right;
    font-size: 16px;
    color: #ff5200;
}

.deAddress {
    margin-top: 12px;
}

.comtittle {
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    padding-left: 40px;
    background-color: #f5f5f5;
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    margin-top: 25px;
    margin-bottom: 20px;
}

.comtittle li {
    float: left;
    /* height: 50px; */
    /* width: 14.28%; */
}

.comtittle span {
    cursor: pointer;
}

.deselect {
    color: #FF5200;
    border-bottom: 2px solid #FF5200;
    padding: 0px 5px 12px 5px;
}

#deVideo {
    width: 782px;
    width: 100;
    /* background-color: #007AFF; */
    object-fit: fill;
}

.deImage {
    overflow: hidden;
}

.deImageR {
    float: right;
    width: 376px;
}

.deImageL {
    float: left;
    height: 450px;
}

.dexiaoguo {
    position: relative;
    display: block;
    line-height: 40px;
    bottom: 47px;
    height: 40px;
    color: #ffffff;
    background-color: #00000070;
    text-align: center;
}

.deoList{
    font-size: 14px;
    margin-bottom: 20px;
}
.deoList p{
    color: #999999;
    width: 70px;
    float: left;
    margin-right: 10px;
}
.deoList a{
    color: #333333;
    border-right: 1px solid #dfdfdf;
    line-height: 14px;
    height: 14px;
    /* float: left; */
    display: inline-block;
    padding-right: 14px;
    margin-right: 10px;
}
.deoList a:hover{
    color:#ff5200;
}
.deoList a:last-child{
    border-right: 0;
}
.deoSel{
    color: #FF5200 !important;
}
/*æˆ·åž‹è½®æ’­*/
.houseTypeYes{overflow:hidden}
.housetype{font-size:16px;color:#ddd;margin:25px auto;width:1180px;}
.housetype a{color:#333; text-decoration:none;margin-right:20px;}
.housetype a:hover{color:#900}
.housetype a.hover{color:#ff6600}
.search{position:relative;width: 1170px;margin:0 auto;}
.houseTypeYes .search{position:relative;width: 1180px;margin:0 auto;}
.search ul{width:20000px; position:absolute}
.search li{width: 147px;float:left;text-align:center;border: 1px solid #ededed;line-height: 20px;height: 52px;margin-right: 9px;}
.search_gd{/* width: 1105px; */position:relative;height:62px;overflow:hidden;margin: 0 36px;}
.search li a{font-size: 14px;color:#333;text-decoration:none;display:block;width: 115px;padding-top:7px;height:53px;position:  relative;text-align:  left;padding-left: 18px;}
.saleTypeIcon{position: absolute;/* right: 0px; */top: 8px;background:  #ff5200;color: #FFF;line-height: 18px;font-size: 12px;/* padding: 0 2px; */border-radius: 2px;width:  35px;height:  18px;text-align:  center;vertical-align: 5px;}
.saleTypeIcon1{
    background: #ff5200;
    vertical-align: 5px;
    margin-left: 12px;
}
.saleTypeIcon2{background: #ffa626;vertical-align: 5px;margin-left: 8px;/* vertical-align: 5px; */}
.saleTypeIcon3{background: #ecf0f3;color:#8099af;margin-left: 6px;}
.search li:hover{border:1px solid #f60;}
.search li:hover a{color:#666}
.search li.hover{border:1px solid #f60;}
.search li.hover a{/* color:#f60 */}
.btn_left{float:left;height: 54px;width: 24px;/* margin-top: 2px; */overflow: hidden;background: url(https://static.fangxiaoer.com/web/images/ico/houseTypeicon.png);cursor:  pointer;}
.btn_right{float:right;height: 54px;width: 24px;overflow: hidden;background: url(https://static.fangxiaoer.com/web/images/ico/houseTypeicon.png) 23px -55px;/* margin-top:7px; */}
.next,.before{cursor:pointer;background-position: 40px 104px;}
.next{background-position: 22px 109px;}
.znext{background-position: -17px -54px;}
.zbefore{background-position: 0 -54px;margin-right: 8px;}
.titleTypeR{

    display: inline-block;

    width: 46px;

    position: inherit;

    /* float: left; */

    padding: 0;

    position: relative;

    font-size: 14px;

    top: 2px;

    height: 26px;

    text-align: center;

    line-height: 26px;

    /* margin-top: 2px; */

    margin-left: 8px;
}
.type_wei_wun{
    overflow: hidden;
}
.dmoPri{
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid #ededed;
}
.dmoLab{
    margin-right: 25px !important;
}
.dmoDmo{
    margin-top: 10px;
}
.dmoLabLi b{
    font-size: 22px !important;
    color: #ff5200 !important;
    font-weight: bold;
}


/*æˆ·åž‹å¼¹çª—*/
.huxing_wei{float:left;width:188px;line-height:  14px !important;margin-top: 30px;}
.huxing_wei label{line-height: 14px !important;}
.type_wei{float:left;width:188px;line-height: 14px !important;margin-top: 30px;}
.type_wei label{line-height: 14px !important;}
.jian_wei{float:left;width:188px;line-height: 14px !important;margin-top: 30px;}
.jian_wei label{line-height: 14px !important;}
.zhengf_wei{float:left;width:188px;margin-top: 20px;}
.layer_box table{width: 100%; height: 100%;;}
.layer_box table .layer{width: 900px;height: 710px;background-color: #000;margin: 0 auto;position: relative;}
.layer_box table .layer ul li img{max-width: 600px;}
.layer_box table .layer .close{position: absolute;;top: 20px; right: 20px; cursor: pointer;}
.layer_box table .layer ul li{width: 600px; height: 600px;}
.layer_box .layer .left_btn{position: absolute;left: 40px;top: 50%;}
.layer_box .layer .right_btn{position: absolute;right: 40px;top: 50%;}
.layer_box .layer .lunbo{position: relative;}
.layer_box .layer .left_btn img{margin-top: -40px;cursor: pointer;}
.layer_box .layer .right_btn img{margin-top: -40px;cursor: pointer;}
.layer_box .layer .photo{overflow: hidden;width:  600px;margin:  0 auto;}
.layer_box .layer .photo ul li{ float: left;}
.layer_box .layer .photo ul li img{display: block;margin: 0 auto;width: 600px;height: 600px;}
.layer_box .layer p {
    text-align: center;
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    padding-top: 17px;
    padding-bottom: 17px;
}
.layer_box .layer .left_btn .hover{display:none}
.layer_box .layer .right_btn .hover{display:none}
.layer_box .layer .left_btn:hover img{display:none}
.layer_box .layer .left_btn:hover .hover{display:block}

.layer_box .layer .right_btn:hover img{display:none}
.layer_box .layer .right_btn:hover .hover{display:block}
.huxing_p{width:437px;margin-top: 5px;line-height: 26px; */}
.number h1{ margin-left:227px;
    font-size:14px;
    font-weight:normal}

.pic .look{background-color: rgba(0,0,0,0.5);position: absolute;right: 68px;bottom: 0;}
.pic .look h1{font-size: 12px;color: #fff;line-height: 30px;padding-right: 11px;padding-left: 30px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Magnifier.png);background-repeat: no-repeat;background-position: 11px;cursor: pointer;}
.layer_box table{width: 100%; height: 100%;;}
.lunbohx{
    width: 400px;
    text-align: center;
    margin-bottom: 20px;
}
.pic .show{float: initial !important;
    border: 0 !important;
}