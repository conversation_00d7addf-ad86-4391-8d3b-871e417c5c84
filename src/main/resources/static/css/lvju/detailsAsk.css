.content {
    width: 1170px;
    margin: auto
}

.decomLocation {
    margin: 15px 0 30px 0
}

.detemperature {
    margin-left: 16px;
    font-size: 18px;
    color: #272727;
    margin-top: 8px;
    display: inline-block
}

.deSelection {
    overflow: hidden
}

.dephone {
    float: right
}

.btn_1 {
    width: 180px;
    height: 44px;
    color: #fff;
    background-color: #ff6100;
    border: 0;
    border-radius: 3px;
    margin-left: 20px;
    cursor: pointer
}

.dephone p {
    font-size: 14px;
    color: #666
}

.dephone span {
    font-size: 24px;
    color: #272727;
    margin-left: 8px;
    font-family: "dinot-bold"
}

.dephone b {
    font-size: 20px;
    font-weight: 400
}

.deInsale i {
    font-size: 14px;
    color: #fff;
    padding: 4px 16px;
    background-color: #ff6100;
    border-radius: 3px;
    margin-right: 2px
}

.deInsale span {
    padding: 4px 8px;
    font-size: 14px;
    color: #596c91;
    background-color: #f3f5f7;
    margin-left: 2px
}

.deInsale {
    margin-top: 10px
}

.deCategory {
    overflow: hidden
}

.deCategoryUl1 > li {
    float: left;
    margin-right: 26px
}

.deCategoryUl1 > li > span {
    font-size: 16px;
    color: #272727;
    font-family: "dinot-bold"
}

.deCategoryUl1 > li > span > b {
    font-size: 30px;
    color: #ff5200
}

.deCategoryUl1 > li > span > i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat;
    background-size: 100% 100%;
    padding-left: 5px;
    text-align: center
}

.deCategoryUl2 > li > span {
    font-size: 16px;
    color: #272727;
    font-family: "dinot-bold"
}

.deCategoryUl2 > li > span > b {
    font-size: 30px;
    color: #ff5200
}

.deCategoryUl2 > li > span > i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat;
    background-size: 100% 100%;
    padding-left: 5px;
    text-align: center
}

.deCategorySpan {
    margin-top: 10px;
    display: inline-block;
    width: 118px;
    height: 30px;
    text-align: center;
    border: 1px solid #ebebeb;
    line-height: 30px;
    font-size: 16px;
    color: #333 !important
}

.deCategoryUl2 {
    display: none
}

.deAddress img {
    margin-bottom: -5px;
    margin-right: 5px
}

.deAddGD {
    float: right;
    font-size: 16px;
    color: #ff5200
}

.deAddress {
    margin-top: 12px
}

.comtittle {
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    padding-left: 40px;
    background-color: #f5f5f5;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin-top: 25px;
    margin-bottom: 20px
}

.comtittle li {
    float: left;
    /* width: 14.28% */
}

.comtittle span {
    cursor: pointer
}

.deselect {
    color: #ff5200;
    border-bottom: 2px solid #ff5200;
    padding: 0 5px 12px 5px
}

#deVideo {
    width: 782px;
    object-fit: fill
}

.deImage {
    overflow: hidden
}

.deImageR {
    float: right;
    width: 376px
}

.deImageL {
    float: left;
    height: 450px
}

.dexiaoguo {
    position: relative;
    display: block;
    line-height: 40px;
    bottom: 47px;
    height: 40px;
    color: #fff;
    background-color: #00000070;
    text-align: center
}

.deImageR img {
    width: 376px;
    height: 206px
}

.deImageR div {
    height: 206px;
    margin-bottom: 12px
}

.deBuilding {
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #ededed
}

.deDoordetailsdiv {
    float: left;
    margin-top: 45px
}

.H3 {
    margin-left: 24px;
    float: left;
    font-size: 20px;
    color: #272727
}

.decommentshead {
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #ededed
}

.decomments {
    margin-top: 25px;
    border: 1px solid #ededed;
    border-bottom: 0
}

.decommentPJ {
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #ededed;
    margin: 0 30px
}

.decommentPJdiv {
    float: right
}

.decommentPJ span {
    font-size: 18px;
    color: #333
}

.decommentPJ button {
    width: 130px;
    height: 30px;
    border: 0;
    color: #fff;
    font-size: 14px;
    background-color: #fe6116;
    border-radius: 2px;
    margin-left: 14px;
    cursor: pointer;
    float: right;
    margin-top: 15px
}

.decommentuseImg {
    width: 50px;
    height: 50px;
    background: #ededed;
    overflow: hidden;
    border-radius: 90px;
    margin-top: 6px
}

.decommentuse {
    width: 190px;
    text-align: center;
    float: left;
    position: absolute;
    top: 50%;
    margin-top: -47px
}

.decommentusechi {
    height: 94px;
    border-right: 1px dashed #e7e7e7
}

.decommentusechi p {
    font-size: 14px;
    color: #333;
    margin-top: 10px
}

.decommentMore {
    float: right;
    width: 910px
}

.decommentMoreP {
    font-size: 14px;
    color: #333;
    width: 595px;
    margin-top: 30px;
    margin-bottom: 16px
}

.decomMoreImg img {
    width: 110px;
    height: 80px;
    margin-right: 4px
}

.decomDetails {
    color: #666;
    margin-bottom: 30px
}

.decomDetails i {
    font-size: 12px
}

.decomDetails1 {
    font-size: 14px;
    margin-left: 404px;
    background: url(../img/quanwen.png) no-repeat right;
    padding-right: 15px
}

.decomDetails2 {
    font-size: 14px;
    float: right;
    background: url(../img/fenxiang.png) no-repeat left;
    padding-left: 24px;
    margin-right: 20px
}

.userSynthesizeli {
    position: relative;
    border-bottom: 1px solid #ededed
}

.deAnswerImg {
    width: 85px;
    float: left
}

.deAnswerImg img {
    margin-left: 50px;
    margin-top: 30px
}

.deAnswerCon {
    float: left;
    margin-top: 30px;
    width: 815px
}

.deAnswerCon h1 {
    font-size: 18px;
    color: #333;
    font-weight: bold;
    margin-bottom: 13px;
    margin-left: 5px;
    width: 770px;
    word-wrap: break-word
}

.deAnswerConPho {
    margin-bottom: 12px
}

.deAnswerConPho span {
    font-size: 14px;
    color: #333;
    margin-left: 5px
}

.deAnswerConPho i {
    margin-right: 45px;
    float: right;
    font-size: 14px;
    color: #666
}

.deAnswerConQuestion {
    width: 720px;
    padding: 25px;
    background-color: #f5f5f5;
    font-size: 14px;
    color: #333;
    line-height: 26px
}

.deAnswer {
    margin-bottom: 30px
}

.deQueAnswer {
    border: 1px solid #ededed;
    width: 900px;
    float: left;
    border-bottom: 0
}

.deConsulting {
    border: 1px solid #ededed;
    border-top: 0;
    width: 900px;
    overflow: hidden;
    margin-bottom: 30px
}

.deConsulting p {
    font-size: 12px;
    color: #333;
    line-height: 30px
}

.pro_name {
    margin-bottom: 15px;
    font-size: 24px;
    color: #333;
    height: 65px;
    line-height: 28px;
    margin-top: 12px;
    position: relative
}

.pro_name #qrcode {
    float: left;
    height: 60px;
    margin: 0;
    width: 60px;
    margin-right: 10px
}

#type1:hover {
    background: #ff6100 !important;
    color: #fff !important
}

#qrcode {
    position: relative
}

#qrcode .layer_wei {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .85);
    display: none
}

#qrcode .layer_wei h1 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 15px
}

#qrcode .layer_wei h2 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 5px
}

#qrcode img {
    width: 100%;
    height: 100%
}

.box_sun {
    float: left
}

.pro_name p {
    float: none;
    margin-right: 19px
}

.centigrade {
    margin-left: 20px
}

.pro_name .wait_1 {
    background-color: #ff6100;
    box-shadow: 1px 1.732px 5px 0 rgba(254, 97, 16, 0.3)
}

.pro_name .type_sun {
    width: 58px;
    height: 25px;
    display: block;
    float: left;
    font-size: 15px;
    line-height: 25px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
    font-weight: bold;
    margin-top: 4px
}

.pro_name a {
    float: left;
    font-size: 14px;
    color: #FFF;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border: 1px solid #eff3fd;
    color: #586c94;
    border-radius: 2px;
    padding: 0 6px;
    background: #f3f5f7;
    margin-top: 7px;
    margin-left: 10px
}

.pro_name .s_time {
    padding-left: 40px;
    position: absolute;
    right: 0;
    font-size: 12px;
    bottom: -7px;
    color: #999;
    z-index: 9999
}

.deCondiv {
    margin: 21px 48px 11px 64px;
    border-top: 1px solid #ededed;
    padding-top: 21px
}

.salesOffice {
    padding: 30px 25px;
    border: 1px solid #ededed;
    width: 198px
}

.salesOffice p {
    font-size: 14px;
    color: #333;
    margin-bottom: 6px
}

.salesOffice span {
    font-size: 20px;
    color: #ff5200;
    font-family: "dinot-bold";
    margin-top: 6px
}

.salesOffice b {
    font-size: 14px
}

.salesOffice a {
    text-align: center;
    font-size: 14px;
    color: #fff;
    height: 30px;
    background-color: #ff6100;
    line-height: 30px;
    margin-top: 25px
}

.deqA div {
    width: 500px;
    text-align: right;
    float: left;
    font-size: 14px;
    color: #999
}

.deqA button {
    width: 130px;
    height: 30px;
    border: 0;
    color: #fff;
    font-size: 14px;
    background-color: #fe6116;
    border-radius: 2px;
    margin-left: 50px;
    cursor: pointer;
    float: left;
    margin-top: 53px
}

.deqA {
    height: 135px;
    line-height: 135px;
    border-bottom: 1px solid #ededed
}

#qrcode:hover .layer_wei {
    display: block
}