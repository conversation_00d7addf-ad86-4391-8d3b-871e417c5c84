.housesRight{
	float: right;
	width: 250px;
}
.salesOffice {
	padding: 30px 25px;
	border: 1px solid #ededed;
}

.salesOffice p {
	font-size: 14px;
	color: #333333;
	margin-bottom: 6px;
}

.salesOffice span {
	font-size: 20px;
	color: #ff5200;
	font-family: "dinot-bold";
	margin-top: 6px;
}

.salesOffice b {
	font-size: 14px;
}

.salesOffice a {
	text-align: center;
	font-size: 14px;
	color: #ffffff;
	height: 30px;
	background-color: #ff6100;
	line-height: 30px;
	margin-top: 25px;
}



/*æ—…å±…é”¦å›Š*/
.houseRightBtn {
	background: #ff6100;
	color: #fff;
	display: block;
	text-align: center;
	font-size: 14px;
	line-height: 30px;
	border-radius: 4px;
	/* margin-bottom: 20px; */
	margin-top: 18px !important;
}
.houseRight {
	border: 1px solid #ededed;
	margin-bottom: 20px;
	padding: 0 20px;
	overflow: hidden;
	width: 208px;
}
.houseRinghtTitle{
	font-size: 18px;
	color: #333333;
	line-height: 55px;
	border-bottom: 1px solid #ededed;
}
.houseRight ul li{
	margin-top: 10px;
	border-bottom: 1px solid #ededed;
}
.houseRight ul li a{
	width: 100%;
	height: 100%;
}
.lj_tit{
	width: 71px;
	height: 26px;
	line-height: 26px;
	color: #FFFFFF;
	font-size: 14px;
	text-align: center;
	border-radius: 3px;
}
.houseRight ul li a b{
	width: 100%;
	font-size: 14px;
	color: #333333;
	display: block;
	margin-top: 7px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.houseRight ul li a b:hover{
	color:#ff5200;
}
.houseRight ul li a p{
	width: 100%;
	font-size: 14px;
	color: #333333;
	margin-top: 2px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	margin-bottom: 10px;
}
.houseRight ul li:nth-child(1) .lj_tit{
	background: #ff9d00;
	box-shadow: 0 0 3px #ff9d00;
}
.houseRight ul li:nth-child(2) .lj_tit{
	background: #fd494e;
	box-shadow: 0 0 3px #fd494e;
}
.houseRight ul li:nth-child(3) .lj_tit{
	background: #5189f9;
	box-shadow: 0 0 3px #5189f9;
}

/*å¤´éƒ¨æ¨¡å—*/
.pro_name {
	margin-bottom: 15px;
	font-size: 24px;
	color: #333;
	height: 65px;
	line-height: 28px;
	margin-top: 12px;
	position: relative;
}

.pro_name #qrcode {
	float: left;
	height: 60px;
	margin: 0;
	width: 60px;
	margin-right: 10px;
}

#type1:hover {
	background: #ff6100 !important;
	color: #ffffff !important;
}

#qrcode {
	position: relative;
}

#qrcode .layer_wei {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(255, 255, 255, .85);
	display: none;
}

#qrcode .layer_wei h1 {
	font-size: 12px;
	font-weight: 400;
	text-align: center;
	line-height: 14px;
	padding-top: 15px;
}

#qrcode .layer_wei h2 {
	font-size: 12px;
	font-weight: 400;
	text-align: center;
	line-height: 14px;
	padding-top: 5px;
}

#qrcode img {
	width: 100%;
	height: 100%;
}

.box_sun {
	float: left;
}
.box_sun p span {
	font-weight:bold
}

.pro_name p {
	float: none !important;
	margin-right: 19px;
	margin-top: -4px;
	margin-bottom: 7px;
}

.centigrade {
	font-size: 22px;
	margin-left: 20px;
	font-weight: 400 !important;
}

.pro_name .wait_1 {
	background-color: #ff6100;
	box-shadow: 1px 1.732px 5px 0px rgba( 254, 97, 16, 0.3);
}

.pro_name .type_sun {
	width: 58px;
	height: 25px;
	display: block;
	float: left;
	font-size: 15px;
	line-height: 25px;
	text-align: center;
	color: #fff;
	border-radius: 3px;
	font-weight: bold;
	margin-top: 4px;
}

.pro_name a {
	float: left;
	font-size: 14px;
	color: #FFF;
	height: 20px;
	line-height: 20px;
	text-align: center;
	border: 1px solid #eff3fd;
	color: #586c94;
	border-radius: 2px;
	padding: 0 6px;
	background: #f3f5f7;
	margin-top: 7px;
	margin-left: 10px;
}

.pro_name .s_time {
	padding-left: 40px;
	/* background: url(https://static.fangxiaoer.com/web/images/sy/house/view/s_time.gif) 20px no-repeat; */
	position: absolute;
	right: 0;
	font-size: 12px;
	bottom: -7px;
	color: #999;
	z-index: 9999;
}