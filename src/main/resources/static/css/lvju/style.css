.main_window{
  overflow: hidden;
  display: block;
  padding: 0;
  margin: 0 auto;
  width: 320px;
  height: 240px;
  position: relative;
}
div{
  margin: 0px;
  padding: 0px;
}

.cloud{
	width: 240px;
  position: absolute; 
  top:100px;
}

[data-type=white_1]:before{
  content: "";
  display: block;
  width: 50px;
  height: 50px;
  top: -20px;
  left: 20px;
  background-color: #fff;   
  border-radius: 30px;
  position: absolute;
  box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff;  
}

[data-type=white_2]:before{
  content: "";
  display: block;
  width: 50px;
  height: 50px;
  top: -20px;
  left: 25px;
  background-color: #fff;   
  border-radius: 30px;
  position: absolute;
  box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff;
}

[data-type=white_3]:before{
  content: "";
  display: block;
  width: 50px;
  height: 50px;
  top: -20px;
  left: 25px;
  background-color: #fff;   
  border-radius: 30px;
  position: absolute;
  box-shadow: 40px -10px 0 15px #fff, 90px 0px 0 15px #fff;
}

[data-type=white_4]:before{
  content: "";
  display: block;
  width: 50px;
  height: 50px;
  top: -20px;
  left: 20px;
  background-color: #fff;   
  border-radius: 30px;
  position: absolute;
  box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff,
              80px 25px 0 10px #fff, 40px 25px 0 20px #fff, 0 40px 0 -5px #fff;
}

[data-type=white_5]:before{
  content: "";
  display: block;
  width: 50px;
  height: 50px;
  top: -20px;
  left: 25px;
  background-color: #fff;   
  border-radius: 30px;
  position: absolute;
  box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff,
              20px 30px 0 20px #fff, 70px 30px 0 10px #fff, 105px 40px 0 -10px #fff;
}

[data-type=white_6]:before{
  content: "";
  display: block;
  width: 50px;
  height: 50px;
  top: -20px;
  left: 25px;
  background-color: #fff;   
  border-radius: 30px;
  position: absolute;
  box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff,
              20px 30px 0 15px #fff, 80px 40px 0 15px #fff;
}

@-webkit-keyframes linemove
{
from {left:00px;}
to {left:320px;}
}
@-moz-keyframes linemove
{
from {left:00px;}
to {left:320px;}
}
@-ms-keyframes linemove
{
from {left:00px;}
to {left:320px;}
}
@-o-keyframes linemove
{
from {left:00px;}
to {left:320px;}
}
@keyframes linemove
{
from {left:0px;}
to {left:320px;}
}


[data-speed="1"]{
  animation: linemove 11.01936s;
  -webkit-animation: linemove 11.01936s;

}
[data-speed="2"]{
  animation: linemove 35.67113s;
  -webkit-animation: linemove 35.67113s;

}
[data-speed="3"]{
  animation: linemove 20.99853s;
  -webkit-animation: linemove 20.99853s;

}
[data-speed="4"]{
  animation: linemove 17.6682s;
  -webkit-animation: linemove 17.6682s;

}
[data-speed="5"]{
  animation: linemove 8.20363s;
  -webkit-animation: linemove 8.20363s;

}
[data-speed="6"]{
  animation: linemove 36.46404s;
  -webkit-animation: linemove 36.46404s;

}
[data-speed="7"]{
  animation: linemove 45.58272s;
  -webkit-animation: linemove 45.58272s;

}
[data-speed="8"]{
  animation: linemove 31.02697s;
  -webkit-animation: linemove 31.02697s;

}
[data-speed="9"]{
  animation: linemove 23.79332s;
  -webkit-animation: linemove 23.79332s;

}
[data-speed="10"]{
  animation: linemove 35.69691s;
  -webkit-animation: linemove 35.69691s;

}
[data-speed="11"]{
  animation: linemove 36.87823s;
  -webkit-animation: linemove 36.87823s;

}
[data-speed="12"]{
  animation: linemove 20.158s;
  -webkit-animation: linemove 20.158s;

}
[data-speed="13"]{
  animation: linemove 17.34752s;
  -webkit-animation: linemove 17.34752s;

}
[data-speed="14"]{
  animation: linemove 22.19251s;
  -webkit-animation: linemove 22.19251s;

}
[data-speed="15"]{
  animation: linemove 25.91683s;
  -webkit-animation: linemove 25.91683s;

}
[data-speed="16"]{
  animation: linemove 17.46605s;
  -webkit-animation: linemove 17.46605s;

}
[data-speed="17"]{
  animation: linemove 22.15188s;
  -webkit-animation: linemove 22.15188s;

}
[data-speed="18"]{
  animation: linemove 27.5939s;
  -webkit-animation: linemove 27.5939s;

}
[data-speed="19"]{
  animation: linemove 40.26929s;
  -webkit-animation: linemove 40.26929s;

}
[data-speed="20"]{
  animation: linemove 12.73873s;
  -webkit-animation: linemove 12.73873s;

}
[data-speed="21"]{
  animation: linemove 11.34359s;
  -webkit-animation: linemove 11.34359s;

}
[data-speed="22"]{
  animation: linemove 24.98947s;
  -webkit-animation: linemove 24.98947s;

}
[data-speed="23"]{
  animation: linemove 43.17049s;
  -webkit-animation: linemove 43.17049s;

}
[data-speed="24"]{
  animation: linemove 28.77146s;
  -webkit-animation: linemove 28.77146s;

}
[data-speed="25"]{
  animation: linemove 19.03643s;
  -webkit-animation: linemove 19.03643s;

}
[data-speed="26"]{
  animation: linemove 44.53789s;
  -webkit-animation: linemove 44.53789s;

}
[data-speed="27"]{
  animation: linemove 26.64436s;
  -webkit-animation: linemove 26.64436s;

}
[data-speed="28"]{
  animation: linemove 41.99245s;
  -webkit-animation: linemove 41.99245s;

}
[data-speed="29"]{
  animation: linemove 18.44574s;
  -webkit-animation: linemove 18.44574s;

}
[data-speed="30"]{
  animation: linemove 19.09319s;
  -webkit-animation: linemove 19.09319s;

}
[data-speed="31"]{
  animation: linemove 34.62887s;
  -webkit-animation: linemove 34.62887s;

}
[data-speed="32"]{
  animation: linemove 33.08773s;
  -webkit-animation: linemove 33.08773s;

}
[data-speed="33"]{
  animation: linemove 10.0134s;
  -webkit-animation: linemove 10.0134s;

}
[data-speed="34"]{
  animation: linemove 39.82966s;
  -webkit-animation: linemove 39.82966s;

}
[data-speed="35"]{
  animation: linemove 36.10109s;
  -webkit-animation: linemove 36.10109s;

}
[data-speed="36"]{
  animation: linemove 47.62616s;
  -webkit-animation: linemove 47.62616s;

}
[data-speed="37"]{
  animation: linemove 39.53287s;
  -webkit-animation: linemove 39.53287s;

}
[data-speed="38"]{
  animation: linemove 21.86695s;
  -webkit-animation: linemove 21.86695s;

}
[data-speed="39"]{
  animation: linemove 48.20212s;
  -webkit-animation: linemove 48.20212s;

}
[data-speed="40"]{
  animation: linemove 26.6844s;
  -webkit-animation: linemove 26.6844s;
}

.cloud{
  -webkit-animation-timing-function:linear;
  -webkit-animation-iteration-count:infinite;
  animation-timing-function:linear;
  animation-iteration-count:infinite;
  -webkit-animation-delay: -50s;
}