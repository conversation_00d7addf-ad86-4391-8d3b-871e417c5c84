* {
    margin: 0;
    padding: 0
}

b {
    font-weight: 400
}

.banner2 {
    height: 300px;
    width: 100%;
    min-width: 1170px;
    background-position: top;
    background-size: cover
}

.banner2 p {
    padding: 3px 15px;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 12px;
    position: absolute;
    right: 0;
    float: right;
    margin-top: 250px
}

a:link {
    text-decoration: none !important
}

a:active {
    text-decoration: none !important
}

a:hover {
    text-decoration: none !important
}

a:visited {
    text-decoration: none !important
}

.banner {
    width: 100%;
    height: 300px;
    margin: 0 auto
}

.swiper-container {
    width: 100%;
    height: 100%;
    top: -1px
}

.swiper-slide {
    position: relative;
    text-align: center;
    font-size: 18px;
    background: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center
}

.swiper-slide a {
    width: 100%;
    height: 100%
}

.swiper-slide img {
    width: 100%;
    height: 100%
}

.swiper-slide p {
    padding: 3px 15px;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 12px;
    position: absolute;
    bottom: 28px;
    right: 18%;
    border-radius: 3px
}

.swiper-button-next {
    width: 27px !important;
    height: 51px !important;
    background: url("https://static.fangxiaoer.com/global/imgs/ico/banner_qh0.png") no-repeat !important;
    background-position: -36px 0 !important;
    background-size: 236% !important;
    right: 50px !important
}

.swiper-button-prev {
    width: 27px !important;
    height: 51px !important;
    background: url("https://static.fangxiaoer.com/global/imgs/ico/banner_qh0.png") no-repeat !important;
    background-size: 236% !important;
    left: 50px !important
}

.content {
    width: 1170px;
    margin: 0 auto
}

.comLocation {
    font-size: 12px;
    color: #999;
    margin: 20px 0
}

.comLocation a {
    color: #999
}

.left {
    width: 865px;
    overflow: hidden;
    float: left;
    margin-right: 25px
}

.left_tit {
    width: 100%;
    height: 50px;
    background: #f5f5f5
}

.left_tit li {
    width: 33.33%;
    height: 50px;
    float: left;
    line-height: 50px;
    text-align: center
}

.left_tit li a {
    display: block;
    width: 88px;
    text-align: center;
    height: 48px;
    color: #333;
    margin: 0 auto;
    font-size: 18px
}

.left_tit li a:hover {
    color: #ff5200;
    /* font-weight: bold */
}

.aa {
    color: #ff5200 !important;
    border-bottom: 2px solid #ff5200;
    font-weight: bold
}

.left_list {
    width: 100%;
    overflow: hidden
}

.left_list ul li {
    width: 100%;
    height: auto;
    border-bottom: 1px solid #dfdfdf
}

.left_list ul li:last-child {
    margin-bottom: 30px
}

.left_list li:hover {
    background: #f5f5f5 !important
}

.left_list ul li h1 {
    width: 100%;
    height: 70px;
    line-height: 70px;
    font-size: 23px;
    color: #343434;
    margin-left: 10px
}

.left_list h1 span:hover {
    color: #ff5200 !important
}

.info {
    width: 100%;
    overflow: hidden;
    margin-left: 10px
}

.info p {
    font-size: 13px;
    color: #b1b2cb;
    float: left
}

.info span {
    float: right;
    color: #fda26a;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/yee.png") no-repeat;
    font-size: 14px;
    margin-right: 20px;
    text-indent: 1.8em;
    line-height: 17px
}

.inlocation {
    width: 100%;
    height: 184px;
    margin-top: 20px;
    margin-bottom: 34px;
    margin-left: 10px;
    padding-bottom: 2px
}

.inlocation img {
    width: 251px;
    height: 184px;
    float: left
}

.indetails {
    width: 67%;
    height: 133px;
    float: left;
    margin: 13px 16px 20px 16px;
    font-size: 14px;
    color: #3e3e3e;
    line-height: 34px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical
}

.inestate {
    float: right;
    font-size: 13px;
    margin-right: 23px
}

.inestate a {
    color: #1389ca
}

.right {
    display: block;
    width: 280px;
    float: right;
    position: relative
}

.main_window {
    overflow: hidden;
    display: block;
    padding: 0;
    float: right;
    margin: 0 auto;
    width: 250px;
    height: 460px;
    position: relative
}

.inEWM {
    width: 200px;
    height: 400px;
    margin: auto;
    background-color: #fff;
    border-radius: 19px;
    margin-top: 1px;
    box-shadow: 0 5px 22px -6px #999
}

.inestate a:hover {
    color: #1389ca !important
}

.cloud {
    width: 200px;
    position: absolute;
    top: 100px
}

[data-type=white_1]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff
}

[data-type=white_2]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff
}

[data-type=white_3]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff
}

[data-type=white_4]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff, 80px 25px 0 10px #fff, 40px 25px 0 20px #fff, 0 40px 0 -5px #fff
}

[data-type=white_5]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff, 20px 30px 0 20px #fff, 70px 30px 0 10px #fff, 105px 40px 0 -10px #fff
}

[data-type=white_6]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff, 20px 30px 0 15px #fff, 80px 40px 0 15px #fff
}

@-webkit-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-moz-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-ms-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-o-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@keyframes linemove {
    from {
        left: 0
    }
    to {
        left: 320px
    }
}

@-webkit-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

[data-speed="1"] {
    animation: linemove1 3.01936s;
    -webkit-animation: linemove1 3.01936s
}

[data-speed="2"] {
    animation: linemove2 6.67113s;
    -webkit-animation: linemove2 6.67113s
}

[data-speed="3"] {
    animation: linemove3 1.99853s;
    -webkit-animation: linemove3 1.99853s
}

[data-speed="4"] {
    animation: linemove4 3.01936s;
    -webkit-animation: linemove4 3.01936s
}

[data-speed="5"] {
    animation: linemove2 6.20363s;
    -webkit-animation: linemove2 6.20363s
}

[data-speed="6"] {
    animation: linemove 12.46404s;
    -webkit-animation: linemove 12.46404s
}

.cloud {
    -webkit-animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    -webkit-animation-delay: -50s
}

.recommend {
    width: 275px;
    overflow: hidden;
    border: 1px solid #eaeaea;
    margin: 0 auto
}

.recommend-title {
    width: 275px;
    height: 46px;
    overflow: hidden;
    border-bottom: 1px solid #eaeaea;
    margin: 0 auto
}

.chengse {
    width: 4px;
    height: 18px;
    border-radius: 5px;
    background: #ff5200;
    margin: 14px 12px 14px 12px;
    float: left
}

.tuijian {
    float: left;
    font-size: 17px;
    color: #000;
    line-height: 45px
}

.right_list {
    width: 224px;
    height: auto;
    margin: 0 auto
}

.right_list ul li {
    width: 224px;
    height: auto;
    border-bottom: 1px solid #eaeaea;
    margin-top: 16px
}

.recommend-img {
    width: 100%;
    height: 172px;
    position: relative
}

.recommend-img img {
    width: 100%;
    height: 100%
}

.recommend-img p {
    width: 100%;
    height: 30px;
    background: rgba(0, 0, 0, 0.48);
    position: absolute;
    left: 0;
    bottom: 0;
    line-height: 30px;
    text-indent: 1em;
    color: #fff;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.unstyled {
    overflow: hidden;
    margin-top: 11px;
    font-size: 13px;
    color: #000
}

.unstyled span {
    float: right;
    margin-right: 7px
}

.unstyled b {
    font-size: 14px;
    font-weight: bold;
    color: #ff5200
}

.ts_desc {
    font-size: 13px;
    color: #ff5200;
    margin-top: 6px;
    margin-bottom: 14px
}

.aircraft {
    width: 239px;
    height: 174px;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/aircraft.png) no-repeat;
    background-size: 100%;
    position: fixed;
    right: 0;
    z-index: 9;
    animation: aircraft 5s ease-in-out, shake1 4s linear 5s infinite;
    -webkit-animation: aircraft 5s ease-in-out, shake1 4s linear 5s infinite
}

.ai {
    width: 111px;
    height: 87px;
    margin-left: 27px;
    margin-top: 31px;
    text-align: center
}

.ai h1 {
    font-size: 17px;
    color: #333;
    font-weight: bold
}

.ai p {
    font-size: 15px;
    color: #333;
    margin-top: 2px
}

.ai p span {
    font-size: 16px;
    font-weight: bold;
    color: #ff5200
}

.container {
    display: none
}

@-webkit-keyframes aircraft {
    0% {
        transform: translate(400px, -300px);
        -webkit-transform: translate(400px, -300px)
    }
    100% {
        transform: translate(0px, 0px);
        -webkit-transform: translate(0px, 0px)
    }
}

.guang {
    width: 1170px;
    height: 300px;
    margin: auto;
    position: relative
}

.guang2 {
    height: 24px;
    margin-top: -24px
}

.guang2 p {
    right: 0
}