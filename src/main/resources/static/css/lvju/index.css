.content{
    width: 1170px;
    margin: auto;
    position: relative;
}
.comLocation{
    font-size: 12px;
    color: #999999;
    margin: 20px 0;
}
.comLocation a{
    color: #999999;
}
.H1{
    font-size: 32px;
    color: #272727;
    line-height: 32px;
    font-weight: bold;
    display: inline-block;
}
.banner{
    width: 100%;

}

/*swiper*/
.swiper-container {
    width: 100%;
    height: 100%;
}
.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}
.inSelSwi{
    width: 810px;
    height: 400px;
}
.inSelSwi .swiper-container {
    width: 100%;
    height: 100%;
}
.node{
    width: 260px;
    height: 150px;
    position: absolute;
    bottom: 0;
    right: 0;
}
.activity-photos-bg{
    position: relative;
}
.node_1{

    width: 260px;

    display: inline-block;

    height: 55px;

    background-color: #00000069;

    color: #ffffff;

    font-size: 24px;

    line-height: 55px;

    margin-bottom: 2px;
}
.node_2{

    width: 260px;

    height: 55px;

    display: inline-block;

    color: #ffffff;

    font-size: 16px;

    background-color: #00000069;

    line-height: 55px;
}
.inEWM{
    width:240px;
    height:430px;
    margin:auto;
    background-color:#fff;
    border-radius: 19px;
    margin-top: 1px;
    box-shadow: 0px 5px 22px 0px #999999c4;
    margin-top: 18px;
}




/*ÃƒÂ©Ã‚Â£Ã‹Å“ÃƒÂ¥Ã…Â Ã‚Â¨ÃƒÂ¦Ã¢â‚¬Â¢Ã‹â€ ÃƒÂ¦Ã…Â¾Ã…â€œ*/

.main_window{
    overflow: hidden;
    display: block;
    padding: 0;
    float: right;
    margin: 0 auto;
    width: 318px;
    height: 533px;
    position: relative;
    top: -485px;
}
div{
    margin: 0px;
    padding: 0px;
}
.inhouseholder{
    margin-top: 12px;
}

.cloud{
    width: 240px;
    position: absolute;
    top:100;
}

[data-type=white_1]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff;
}

[data-type=white_2]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff;
}

[data-type=white_3]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0px 0 15px #fff;
}

[data-type=white_4]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff,
    80px 25px 0 10px #fff, 40px 25px 0 20px #fff, 0 40px 0 -5px #fff;
}

[data-type=white_5]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff,
    20px 30px 0 20px #fff, 70px 30px 0 10px #fff, 105px 40px 0 -10px #fff;
}

[data-type=white_6]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff,
    20px 30px 0 15px #fff, 80px 40px 0 15px #fff;
}

@-webkit-keyframes linemove
{
    from {left:00px;}
    to {left:320px;}
}
@-moz-keyframes linemove
{
    from {left:00px;}
    to {left:320px;}
}
@-ms-keyframes linemove
{
    from {left:00px;}
    to {left:320px;}
}
@-o-keyframes linemove
{
    from {left:00px;}
    to {left:320px;}
}
@keyframes linemove
{
    from {left:0px;}
    to {left:320px;}
}
@-webkit-keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@-moz-keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@-ms-keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@-o-keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@-webkit-keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@-moz-keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@-ms-keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@-o-keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@-webkit-keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@-moz-keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@-ms-keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@-o-keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@-moz-keyframes linemove4
{
    from {left:-110px;}
    to {left:300px;}
}
@-ms-keyframes linemove4
{
    from {left:-110px;}
    to {left:300px;}
}
@-o-keyframes linemove4
{
    from {left:-110px;}
    to {left:300px;}
}
@keyframes linemove4
{
    from {left:-110px;}
    to {left:300px;}
}
@-webkit-keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
@-moz-keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
@-ms-keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
@-o-keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
@keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
[data-speed="1"]{
    animation: linemove1 3.01936s;
    -webkit-animation: linemove1 3.01936s;

}
[data-speed="2"]{
    animation: linemove2 3.01936s;
    -webkit-animation: linemove2 3.01936s;
}
[data-speed="3"]{
    animation: linemove3 3.01936s;
    -webkit-animation: linemove3 3.01936s;

}
[data-speed="4"]{
    animation: linemove4 3.01936s;
    -webkit-animation: linemove4 3.01936s;

}
[data-speed="5"]{
    animation: linemove2 3.01936s;
    -webkit-animation: linemove2 3.01936s;

}
[data-speed="6"]{
    animation: linemove2 3.01936s;
    -webkit-animation: linemove2 3.01936s;

}


.cloud{
    -webkit-animation-timing-function:linear;
    -webkit-animation-iteration-count:infinite;
    animation-timing-function:linear;
    animation-iteration-count:infinite;
    -webkit-animation-delay: -50s;
}
.inSelection{
    overflow: hidden;
    height: 520px;
}
.geng{
    width: 820px;
    margin: 22px 0 20px 0;
}
.swiper-button-prev{
    left: 0 !important;
}
.swiper-button-next{
    right: 0 !important;
}
.swiper-button-prev {
    position: absolute;
    top: 50%;
    width: 47px;
    color: #ffffff;
    height: 47px;
    margin-top: -22px;
    z-index: 10;
    cursor: pointer;
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/swiL.png);
    background-size: 47px 47px !important;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #6e6e6d6e;
}
.swiper-slide-duplicate-prev .bgclo{
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    background: #00000085;
}
.swiper-slide-active .bgclo{
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    background: #00000085;
}
.node{
    display: none;
}
.swiper-slide-next .node{
    display: block;
}
.estate{

    /* margin-top: 25px; */
}
.h1span{
    float:right;
    font-size: 14px;
    color: #333333;
    line-height: 35px;
}
.h1span:hover{
    color:#ff5200;
}
.gengg{

    display: block;

    margin: 26px 0;
}
.estateR{
    width: 790px;
    height: 354px;
    margin-right: 5px;
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/estateBg.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 790px 354px;
    float: right;
    margin-top: -382px;
    z-index: 9999;
    box-shadow: 3px 1px 10px 2px #aca8a85c;
}

.estateDiv{
    /*overflow: hidden;*/
}
.estateCon{

    margin: 47px 50px;
}
.inlocation{

    overflow: hidden;
}
.inlocation span{

    display: block;

    float: left;

    margin-left: 12px;

    margin-top: -3px;

    font-size: 18px;

    color: #333333;
}
.coordinatesimg{

    float: left;
}
.info{

    margin-top: 16px;
}
.info a{

    font-size: 24px;

    color: #272727;

    font-weight: bold;
}
.inestate{

    float: right;
}
.inestate span{

    display: inline-block;

    height: 24px;

    line-height: 24px;

    font-size: 14px;

    color: #596c91;

    padding: 0 10px;

    background-color: #f3f5f7;
}
.inhouse{

    font-size: 16px;

    color: #666666;

    float: left;
}
.inhouse b{

    font-size: 30px;
    font-family: "dinot-bold";
    color: #ff5200;
}
.inhouse i{
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/border.png) no-repeat;
    background-size: 100% 100%;
    font-size: 14px;
    display: inline-block;
    text-align: center;
    /* padding: 5px; */
    width: 27px;
    padding-left: 3px;
    height: 20px;
    margin-left: 8px;
}
/*.inhouseholder{
	overflow: hidden;
}*/
.inholder{
    float: right;
    margin-top: 16px;
}
.inholder span{
    margin-left:20px;
    font-size:16px;
    color:#333333;
    margin-top: 10px;
}
.indetails{
    font-size: 16px;
    color: #666666;
    margin-top: 36px;
    overflow: hidden;
    text-overflow:ellipsis;
    display:-webkit-box;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:2;
    height: 48px;
}
.innecessary img{
    float: left;
}
.innecessary span{
    float: left;
    margin: 4px 10px 0 0px;
    font-size: 16px;
    color: #333333;
}
.innecessary{
    margin-top:14px;
}
.estateDiv:nth-child(even) .estateimg{
    float: right;
}
.estateDiv:nth-child(even) .estateR{
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/estateBg2.png);
    float: left;
    margin-left: 5px;
    box-shadow: -3px 1px 10px 2px #aca8a85c;
}
.inkits{
    margin-top: 20px;
}
.inkits li{
    float: left;
    width: 382px;
    height: 354px;
    margin-right: 12px;
}
.inkits li:last-child{
    margin-right: 0;
}
.inkitsdiv{
    background-color: #f5f5f5;
    padding-right: 32px;
    padding-top: 18px;
    padding-bottom: 20px;
    /* height: 112px; */
}
.inkitsdiv h2{

    font-size: 18px;

    margin-left: 32px;

    /* margin-top: 25px; */

    color: #272727;

    margin-bottom: 5px;

    overflow: hidden;

    /* width: 318px; */

    text-overflow: ellipsis;

    /* display: -webkit-box; */

    overflow: hidden;

    text-overflow: ellipsis;

    white-space: nowrap;
}
.inkitsdiv h2:hover{
    color:#ff5200;
}
.inkitsdiv p{

    margin-left: 32px;

    color: #666666;

    font-size: 14px;

    text-overflow: -o-ellipsis-lastline;

    overflow: hidden;

    text-overflow: ellipsis;

    display: -webkit-box;

    -webkit-line-clamp: 2;

    line-clamp: 2;

    -webkit-box-orient: vertical;
}
.disclaimer {
    width: 1130px;
    line-height: 20px;
    margin: 30px auto 28px auto;
    padding: 15px 20px;
    background: #eee;
    font-size: 12px;
}
.inkits ul{
    height: 466px;
}
.jinnangdisB {
    display: inline-block;
    border-radius: 2px;
    width: 72px;
    height: 26px;
    color: #FFFFFF !important;
    font-size: 14px !important;
    font-weight: bold;
    /* background-color: #ff9d00; */
    line-height: 26px;
    text-align: center;
    margin-right: 10px;
}
.info p{

    display: inline-block;

    font-size: 24px;

    color: #333333;

    font-weight: bold;

    margin-top: -8px;
}
.info p:hover{
    color:#ff5200
}
.swiper-container img{
    width: 590px;
    height: 400px;
}

/*Ã©Â£Å¾Ã¦Å“Âº*/
.aircraft2 {
    /* width: 239px; */
    /* height: 174px; */
    background-size: 100%;
    position: absolute;
    top: 357px;
    right: -244px;
    z-index: 9;
    animation: aircraft 5s;
}



/*æµ®åŠ¨*/
.loading {
    position: absolute;
    overflow: hidden;
    width: 320px;
    margin-top: 20px;
}
.loading-orange-right ul {
    width: 636px;
    animation: swipe 5s infinite linear;
    -webkit-animation: swipe 5s infinite linear;
    -moz-animation: swipe 5s infinite linear;
    -ms-animation: swipe 5s infinite linear;
    -o-animation: swipe 5s infinite linear;
}
.loading-orange-right ul li {
    float: left;
}
@keyframes swipe {
    0% {
        transform: translateX(0px)
    }
    to {
        transform: translateX(-318px)
    }
}.content{
     width: 1170px;
     margin: auto;
     position: relative;
 }
.comLocation{
    font-size: 12px;
    color: #999999;
    margin: 20px 0;
}
.comLocation a{
    color: #999999;
}
.H1{
    font-size: 32px;
    color: #272727;
    line-height: 32px;
    font-weight: bold;
    display: inline-block;
}
.banner{
    width: 100%;

}
.banner img{
    width: 100%;
    margin-top: -1px;
}
/*swiper*/
.swiper-container {
    width: 100%;
    height: 100%;
}
.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}
.inSelSwi{
    width: 810px;
    height: 400px;
}
.inSelSwi .swiper-container {
    width: 100%;
    height: 100%;
}
.node{
    width: 260px;
    height: 150px;
    position: absolute;
    bottom: 0;
    right: 0;
}
.activity-photos-bg{
    position: relative;
}
.node_1{

    width: 260px;

    display: inline-block;

    height: 55px;

    background-color: #00000069;

    color: #ffffff;

    font-size: 24px;

    line-height: 55px;

    margin-bottom: 2px;
}
.node_2{

    width: 260px;

    height: 55px;

    display: inline-block;

    color: #ffffff;

    font-size: 16px;

    background-color: #00000069;

    line-height: 55px;
}
.inEWM{
    width:240px;
    height:430px;
    margin:auto;
    background-color:#fff;
    border-radius: 19px;
    margin-top: 1px;
    box-shadow: 0px 5px 22px 0px #999999c4;
    margin-top: 18px;
}




/*ÃƒÂ©Ã‚Â£Ã‹Å“ÃƒÂ¥Ã…Â Ã‚Â¨ÃƒÂ¦Ã¢â‚¬Â¢Ã‹â€ ÃƒÂ¦Ã…Â¾Ã…â€œ*/

.main_window{
    overflow: hidden;
    display: block;
    padding: 0;
    float: right;
    margin: 0 auto;
    width: 318px;
    height: 533px;
    position: relative;
    top: -485px;
}
div{
    margin: 0px;
    padding: 0px;
}
.inhouseholder{
    margin-top: 12px;
}

.cloud{
    width: 240px;
    position: absolute;
    top:100;
}

[data-type=white_1]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff;
}

[data-type=white_2]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff;
}

[data-type=white_3]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0px 0 15px #fff;
}

[data-type=white_4]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff,
    80px 25px 0 10px #fff, 40px 25px 0 20px #fff, 0 40px 0 -5px #fff;
}

[data-type=white_5]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff,
    20px 30px 0 20px #fff, 70px 30px 0 10px #fff, 105px 40px 0 -10px #fff;
}

[data-type=white_6]:before{
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff,
    20px 30px 0 15px #fff, 80px 40px 0 15px #fff;
}

@-webkit-keyframes linemove
{
    from {left:00px;}
    to {left:320px;}
}
@-moz-keyframes linemove
{
    from {left:00px;}
    to {left:320px;}
}
@-ms-keyframes linemove
{
    from {left:00px;}
    to {left:320px;}
}
@-o-keyframes linemove
{
    from {left:00px;}
    to {left:320px;}
}
@keyframes linemove
{
    from {left:0px;}
    to {left:320px;}
}
@-webkit-keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@-moz-keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@-ms-keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@-o-keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@keyframes linemove1
{
    from {left:-50px;}
    to {left:300px;}
}
@-webkit-keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@-moz-keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@-ms-keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@-o-keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@keyframes linemove2
{
    from {left:-210px;}
    to {left:300px;}
}
@-webkit-keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@-moz-keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@-ms-keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@-o-keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@keyframes linemove3
{
    from {left:-120px;}
    to {left:300px;}
}
@-moz-keyframes linemove4
{
    from {left:-110px;}
    to {left:300px;}
}
@-ms-keyframes linemove4
{
    from {left:-110px;}
    to {left:300px;}
}
@-o-keyframes linemove4
{
    from {left:-110px;}
    to {left:300px;}
}
@keyframes linemove4
{
    from {left:-110px;}
    to {left:300px;}
}
@-webkit-keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
@-moz-keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
@-ms-keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
@-o-keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
@keyframes linemove6
{
    from {left:-30px;}
    to {left:300px;}
}
[data-speed="1"]{
    animation: linemove1 3.01936s;
    -webkit-animation: linemove1 3.01936s;

}
[data-speed="2"]{
    animation: linemove2 3.01936s;
    -webkit-animation: linemove2 3.01936s;
}
[data-speed="3"]{
    animation: linemove3 3.01936s;
    -webkit-animation: linemove3 3.01936s;

}
[data-speed="4"]{
    animation: linemove4 3.01936s;
    -webkit-animation: linemove4 3.01936s;

}
[data-speed="5"]{
    animation: linemove2 3.01936s;
    -webkit-animation: linemove2 3.01936s;

}
[data-speed="6"]{
    animation: linemove2 3.01936s;
    -webkit-animation: linemove2 3.01936s;

}


.cloud{
    -webkit-animation-timing-function:linear;
    -webkit-animation-iteration-count:infinite;
    animation-timing-function:linear;
    animation-iteration-count:infinite;
    -webkit-animation-delay: -50s;
}
.inSelection{
    overflow: hidden;
    height: 520px;
}
.geng{
    width: 820px;
    margin: 22px 0 20px 0;
}
.swiper-button-prev{
    left: 0 !important;
}
.swiper-button-next{
    right: 0 !important;
}
.swiper-button-prev {
    position: absolute;
    top: 50%;
    width: 47px;
    color: #ffffff;
    height: 47px;
    margin-top: -22px;
    z-index: 10;
    cursor: pointer;
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/swiL.png);
    background-size: 47px 47px !important;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #0000005c;
}
.swiper-button-prev:hover{
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/swiL2.png);
}
.swiper-button-next{
    position: absolute;
    top: 50%;
    width: 47px;
    color: #ffffff;
    height: 47px;
    margin-top: -22px;
    z-index: 10;
    cursor: pointer;
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/swiR.png);
    background-size: 47px 47px !important;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #0000005c;
}
.swiper-button-next:hover{
    /* background-color: #0000005c; */
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/swiR2.png);
}
.node{
    display: none;
}
.swiper-slide-next .node{
    display: block;
}
.estate{

    /* margin-top: 25px; */
}
.h1span{
    float:right;
    font-size: 14px;
    color: #333333;
    line-height: 35px;
    margin-top: 8px;
}
.h1span:hover{
    color:#ff5200;
}
.gengg{

    display: block;

    margin: 26px 0;
}
.estateR{
    width: 790px;
    height: 354px;
    margin-right: 0px;
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/estateBg.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 790px 354px;
    float: right;
    margin-top: -381px;
    z-index: 9999;
    box-shadow: 3px 1px 10px 2px #aca8a836;
}
.estateimg{
    /* width:470px; */
    height:410px;
    float: left;
}
.estateDiv{
    /*overflow: hidden;*/
    height: 410px;
}
.estateCon{

    margin: 47px 50px;
}
.inlocation{

    overflow: hidden;
}
.inlocation span{

    display: block;

    float: left;

    margin-left: 12px;

    margin-top: -3px;

    font-size: 18px;

    color: #333333;
}
.coordinatesimg{

    float: left;
}
.info{

    margin-top: 16px;
}
.info a{

    font-size: 24px;

    color: #272727;

    font-weight: bold;
}
.inestate{

    float: right;
}
.inestate span{

    display: inline-block;

    height: 24px;

    line-height: 24px;

    font-size: 14px;

    color: #596c91;

    padding: 0 10px;

    background-color: #f3f5f7;

    margin-left: 8px;
}
.inhouse{

    font-size: 16px;

    color: #666666;

    float: left;
}
.inhouse b{

    font-size: 30px;
    font-family: "dinot-bold";
    color: #ff5200;
}
.inhouse i{
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/border.png) no-repeat;
    background-size: 100% 100%;
    font-size: 14px;
    display: inline-block;
    text-align: center;
    /* padding: 5px; */
    width: 27px;
    padding-left: 3px;
    height: 20px;
    margin-left: 8px;
}
/*.inhouseholder{
	overflow: hidden;
}*/
.inholder{
    float: right;
    margin-top: 16px;
}
.inholder span{
    margin-left:20px;
    font-size:16px;
    color:#333333;
    margin-top: 10px;
}
.indetails{
    font-size: 16px;
    color: #666666;
    margin-top: 36px;
    overflow: hidden;
    text-overflow:ellipsis;
    display:-webkit-box;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:2;
    height: 48px;
}
.innecessary img{
    float: left;
}
.innecessary span{
    float: left;
    margin: 4px 10px 0 0px;
    font-size: 16px;
    color: #333333;
}
.innecessary{
    margin-top:14px;
}
.estateDiv:nth-child(even) .estateimg{
    float: right;
}
.estateDiv:nth-child(even) .estateDivOver{
    float:right;
}
.estateDiv:nth-child(even) .estateR{
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/estateBg2.png);
    float: left;
    margin-left: 0px;
    box-shadow: -3px 0px 15px 2px #aca8a836;
}
.inkits{
    margin-top: 20px;
}
.inkits li{
    float: left;
    width: 382px;
    height: 354px;
    margin-right: 12px;
}
.inkits li:last-child{
    margin-right: 0;
}
.inkitsdiv{
    background-color: #f5f5f5;
    padding-right: 32px;
    padding-top: 18px;
    padding-bottom: 20px;
    /* height: 112px; */
}
.inkitsdiv h2{

    font-size: 18px;

    margin-left: 32px;

    /* margin-top: 25px; */

    color: #272727;

    margin-bottom: 5px;

    overflow: hidden;

    /* width: 318px; */

    text-overflow: ellipsis;

    /* display: -webkit-box; */

    overflow: hidden;

    text-overflow: ellipsis;

    white-space: nowrap;
}
.inkitsdiv h2:hover{
    color:#ff5200;
}
.inkitsdiv p{

    margin-left: 32px;

    color: #666666;

    font-size: 14px;

    text-overflow: -o-ellipsis-lastline;

    overflow: hidden;

    text-overflow: ellipsis;

    display: -webkit-box;

    -webkit-line-clamp: 2;

    line-clamp: 2;

    -webkit-box-orient: vertical;
}
.disclaimer {
    width: 1130px;
    line-height: 20px;
    margin: 30px auto 28px auto;
    padding: 15px 20px;
    background: #eee;
    font-size: 12px;
}
.inkits ul{
    height: 466px;
}
.jinnangdisB {
    display: inline-block;
    border-radius: 2px;
    width: 72px;
    height: 26px;
    color: #FFFFFF !important;
    font-size: 14px !important;
    font-weight: bold;
    /* background-color: #ff9d00; */
    line-height: 26px;
    text-align: center;
    margin-right: 10px;
}
.info p{

    display: inline-block;

    font-size: 24px;

    color: #333333;

    font-weight: bold;

    margin-top: -8px;
}
.info p:hover{
    color:#ff5200
}
.swiper-container img{
    width: 590px;
    height: 400px;
}


.aircraft2 {
    /* width: 239px; */
    /* height: 174px; */
    background-size: 100%;
    position: absolute;
    top: 371px;
    right: -244px;
    z-index: 9;
    animation: aircraft 5s;
}

.estateDivOver{

    width: 470px;

    height: 410px;

    overflow: hidden;
}


.loading {
    position: absolute;
    overflow: hidden;
    width: 320px;
    margin-top: 36px;
}
.loading-orange-right ul {
    width: 636px;
    animation: swipe 5s infinite linear;
    -webkit-animation: swipe 5s infinite linear;
    -moz-animation: swipe 5s infinite linear;
    -ms-animation: swipe 5s infinite linear;
    -o-animation: swipe 5s infinite linear;
}
.loading-orange-right ul li {
    float: left;
}
@keyframes swipe {
    0% {
        transform: translateX(0px)
    }
    to {
        transform: translateX(-318px)
    }
}