.content {
    width: 1170px;
    margin: auto
}

.decomLocation {
    margin: 15px 0 30px 0
}

.detemperature {
    margin-left: 16px;
    font-size: 18px;
    color: #272727;
    margin-top: 8px;
    display: inline-block
}

.deSelection {
    overflow: hidden
}

.dephone {
    float: right
}

.btn_1 {
    width: 180px;
    height: 44px;
    color: #fff;
    background-color: #ff6100;
    border: 0;
    border-radius: 3px;
    margin-left: 20px;
    cursor: pointer
}

.dephone p {
    font-size: 14px;
    color: #666
}

.dephone span {
    font-size: 24px;
    color: #272727;
    margin-left: 8px;
    font-family: "dinot-bold"
}

.dephone b {
    font-size: 20px;
    font-weight: 400
}

.deInsale i {
    font-size: 14px;
    color: #fff;
    padding: 4px 16px;
    background-color: #ff6100;
    border-radius: 3px;
    margin-right: 2px
}

.deInsale span {
    padding: 4px 8px;
    font-size: 14px;
    color: #596c91;
    background-color: #f3f5f7;
    margin-left: 2px
}

.deInsale {
    margin-top: 10px
}

.deCategory {
    overflow: hidden
}

.deCategoryUl1 > li {
    float: left;
    margin-right: 26px
}

.deCategoryUl1 > li > span {
    font-size: 16px;
    color: #272727;
    font-family: "dinot-bold"
}

.deCategoryUl1 > li > span > b {
    font-size: 30px;
    color: #ff5200
}

.deCategoryUl1 > li > span > i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat;
    background-size: 100% 100%;
    padding-left: 5px;
    text-align: center
}

.deCategoryUl2 > li > span {
    font-size: 16px;
    color: #272727;
    font-family: "dinot-bold"
}

.deCategoryUl2 > li > span > b {
    font-size: 30px;
    color: #ff5200
}

.deCategoryUl2 > li > span > i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat;
    background-size: 100% 100%;
    padding-left: 5px;
    text-align: center
}

.deCategorySpan {
    margin-top: 10px;
    display: inline-block;
    width: 118px;
    height: 30px;
    text-align: center;
    border: 1px solid #ebebeb;
    line-height: 30px;
    font-size: 16px;
    color: #333 !important
}

.deCategoryUl2 {
    display: none
}

.deAddress img {
    margin-bottom: -5px;
    margin-right: 5px
}

.deAddGD {
    float: right;
    font-size: 16px;
    color: #ff5200
}

.deAddress {
    margin-top: 12px
}

.comtittle {
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    padding-left: 40px;
    background-color: #f5f5f5;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin-top: 25px;
    margin-bottom: 20px
}

.comtittle li {
    float: left;
    /* width: 14.28% */
}

.comtittle span {
    cursor: pointer
}

.deselect {
    color: #ff5200;
    border-bottom: 2px solid #ff5200;
    padding: 0 5px 12px 5px
}

#deVideo {
    width: 782px;
    width: 100;
    object-fit: fill
}

.deAnswer ul {
    overflow: hidden
}

.pro_name {
    margin-bottom: 15px;
    font-size: 24px;
    color: #333;
    height: 65px;
    line-height: 28px;
    margin-top: 12px;
    position: relative
}

.pro_name #qrcode {
    float: left;
    height: 60px;
    margin: 0;
    width: 60px;
    margin-right: 10px
}

#type1:hover {
    background: #ff6100 !important;
    color: #fff !important
}

#qrcode {
    position: relative
}

#qrcode .layer_wei {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .85);
    display: none
}

#qrcode .layer_wei h1 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 15px
}

#qrcode .layer_wei h2 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 5px
}

#qrcode img {
    width: 100%;
    height: 100%
}

.box_sun {
    float: left
}

.pro_name p {
    float: none;
    margin-right: 19px
}

.centigrade {
    margin-left: 20px
}

.pro_name .wait_1 {
    background-color: #ff6100;
    box-shadow: 1px 1.732px 5px 0 rgba(254, 97, 16, 0.3)
}

.pro_name .type_sun {
    width: 58px;
    height: 25px;
    display: block;
    float: left;
    font-size: 15px;
    line-height: 25px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
    font-weight: bold;
    margin-top: 4px
}

.pro_name a {
    float: left;
    font-size: 14px;
    color: #FFF;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border: 1px solid #eff3fd;
    color: #586c94;
    border-radius: 2px;
    padding: 0 6px;
    background: #f3f5f7;
    margin-top: 7px;
    margin-left: 10px
}

.pro_name .s_time {
    padding-left: 40px;
    position: absolute;
    right: 0;
    font-size: 12px;
    bottom: -7px;
    color: #999;
    z-index: 9999
}

.deCondiv {
    margin: 21px 48px 11px 64px;
    border-top: 1px solid #ededed;
    padding-top: 21px
}

.salesOffice {
    padding: 30px 25px;
    border: 1px solid #ededed;
    width: 198px
}

.salesOffice p {
    font-size: 14px;
    color: #333;
    margin-bottom: 6px
}

.salesOffice span {
    font-size: 20px;
    color: #ff5200;
    font-family: "dinot-bold";
    margin-top: 6px
}

.salesOffice b {
    font-size: 14px
}

.salesOffice a {
    text-align: center;
    font-size: 14px;
    color: #fff;
    height: 30px;
    background-color: #ff6100;
    line-height: 30px;
    margin-top: 25px
}

.deAnswer {
    padding: 21px 30px 20px 30px;
    border: 1px solid #ededed;
    overflow: hidden;
    margin-bottom: 10px
}

.deAnswer:last-child {
    margin-bottom: 30px
}

.deQueAnswer {
    width: 900px;
    float: left
}

.deAnswer li {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 385px;
    float: left;
    font-size: 14px;
    color: #333;
    margin-bottom: 17px
}

.deAnswer h1 {
    font-size: 18px;
    color: #333;
    margin-bottom: 22px
}

.deAnswer p {
    font-size: 14px;
    color: #333;
    line-height: 30px
}

.detTable {
    font-size: 14px;
    color: #333;
    border: 1px solid #ededed
}

.detTable td {
    text-align: center;
    height: 50px
}

.detTable th {
    height: 50px
}

.detTable tr td:last-child {
    text-align: left;
    padding: 0 10px
}

.dettage {
    font-size: 12px;
    color: #333;
    text-align: center
}

. .dettage span {
    display: inline-block;
    border: 1px solid #ededed;
    border-top: 0;
    padding-right: 31px;
    padding-left: 15px;
    line-height: 24px;
    background-image: url(https://static.fangxiaoer.com/web/images/Villa/op_sun02.png);
    background-repeat: no-repeat;
    background-position: 48px center;
    cursor: pointer
}

.deSpan {
    background-image: url(https://static.fangxiaoer.com/web/images/lvjuImg/op_sun01.png) !important
}

.deAnswer li:hover > p {
    display: block
}

.deAnswer li:hover > .content_pop {
    display: block
}

.deAnswer li > .content_pop {
    display: none;
    max-width: 500px;
    white-space: initial;
    position: absolute;
    background: #fff;
    box-shadow: .5px .866px 10px 0 rgba(0, 0, 0, 0.3);
    padding: 0 20px;
    margin-left: 70px;
    margin-top: 10px
}