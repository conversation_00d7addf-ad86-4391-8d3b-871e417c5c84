html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, textarea, input {
    margin: 0;
    padding: 0
}

address, cite, dfn, em, var, i {
    font-style: normal
}

body {
    font-size: 16px;
    line-height: 1.5;
    font-family: 'Microsoft Yahei', 'simsun', 'arial', 'tahoma';
    color: #333
}

table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%
}

body {
    overflow-x: hidden
}

h1, h2, h3, h4, h5, h6, th {
    font-size: 100%;
    font-weight: normal
}

fieldset, img {
    border: 0
}

a, img {
    -webkit-touch-callout: none
}

a, a:active, a:focus, a:hover, a:visited {
    text-decoration: none
}

input[type=password], input[type=text], textarea {
    resize: none;
    outline: 0;
    -webkit-appearance: none;
    white-space: pre-wrap;
    word-wrap: break-word;
    background: #fff
}

ul, ol {
    list-style: none
}

:focus {
    outline: 0
}

.clearfix {
    clear: both;
    content: "";
    display: block;
    overflow: hidden
}

.clear {
    clear: both
}

.fl {
    float: left
}

.fr {
    float: right
}

.main {
    background: url(../img/bg1.jpg) no-repeat;
    height: 100%;
    background-size: 100% 100%
}

@font-face {
    font-family: 'dinot-bold';
    src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff')
}

.comLocation {
    font-size: 12px;
    color: #999;
    margin: 20px 0 !important
}

.comLocation a {
    color: #999
}

.H1 {
    font-size: 32px;
    color: #272727;
    line-height: 32px;
    font-weight: bold;
    display: inline-block
}

.fxe-alert {
    display: none !important
}

.labels1 {
    background-color: #5189f9;
    box-shadow: 0 3px 7px 0 #759ff2c2
}

.labels3 {
    background-color: #ff9d00;
    box-shadow: 0 3px 7px 0 #f4b44df0
}

.labels2 {
    background-color: #fd494e;
    box-shadow: 0 3px 7px 0 #ec767ab3
}

.comtittle span {
    cursor: pointer;
    padding: 0 5px 12px 5px
}

.comtittle span:hover {
    color: #ff5200;
    padding: 0 5px 12px 5px
}

.disclaimer {
    width: 1130px;
    line-height: 20px;
    margin: 30px auto 28px auto;
    padding: 15px 20px;
    background: #eee;
    font-size: 12px
}

.btn_1 {
    display: inline-block !important
}

.box_span {
    float: left;
    font-size: 14px;
    color: #FFF;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border: 1px solid #eff3fd;
    color: #586c94;
    border-radius: 2px;
    padding: 0 6px;
    background: #f3f5f7;
    margin-top: 7px;
    margin-left: 10px
}

.box_sun p span {
    font-weight: bold
}

.deCategoryUl1 > li {
    float: left;
    margin-right: 10px !important;
    margin-top: -2px
}

.comtittle span {
    cursor: pointer;
    padding: 0 5px 13px 5px
}

.comtittle span:hover {
    color: #ff5200;
    padding: 0 5px 12px 5px
}

.comtittle {
    overflow: hidden;
    height: 50px;
    line-height: 50px;
    background-color: #f5f5f5;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin-top: 25px;
    margin-bottom: 20px
}

.comtittle li {
    float: left;
    /* width: 14.28% */
    margin-right: 104px;
}
.comtittle li:last-child{
    margin-right:0;
}

.comtittle span {
    cursor: pointer
}

.deselect {
    color: #ff5200;
    border-bottom: 2px solid #ff5200;
    padding: 0 5px 12px 5px
}

.ai {
    width: 111px;
    height: 87px;
    margin-left: 27px;
    margin-top: 31px;
    text-align: center
}

.ai h1 {
    font-size: 17px;
    color: #333;
    font-weight: bold
}

.ai p {
    font-size: 15px;
    color: #333;
    margin-top: 2px
}

.ai p span {
    font-size: 16px;
    font-weight: bold;
    color: #ff5200
}

@-webkit-keyframes aircraft {
    0% {
        transform: translate(400px, -300px);
        -webkit-transform: translate(400px, -300px)
    }
    100% {
        transform: translate(0px, 0px);
        -webkit-transform: translate(0px, 0px)
    }
}

#qrcode {
    position: relative
}

#qrcode:hover .layer_wei {
    display: block !important
}

#qrcode2 {
    position: relative
}

#qrcode2 .layer_wei {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .85);
    display: none
}

#qrcode2:hover .layer_wei {
    display: block !important
}

#qrcode2 .layer_wei h1 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 15px
}

#qrcode2 .layer_wei h2 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 5px
}

#qrcode2 {
    float: left;
    height: 60px;
    margin: 0;
    width: 60px;
    margin-right: 10px;
    margin-top: 6px
}

.qrdRightD i {
    float: left;
    font-size: 14px;
    color: #fff;
    padding: 3px 16px;
    background-color: #ff6100;
    border-radius: 3px;
    margin-top: 8px;
    margin-right: 2px;
    box-shadow: 1px 1.732px 5px 0 rgba(254, 97, 16, 0.3);
    background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/Now.png);
}

.detHeader {
    background: #fff;
    width: 1170px;
    margin: auto
}

.detHeader .deCategoryDiv {
    position: absolute;
    background: #fff;
    padding: 12px 18px;
    margin-left: -63px;
    margin-top: 9px;
    box-shadow: 0 1px 9px -2px #636364c2;
    box-shadow: 0 2px 7px -1px #63636463;
    display: none;
}

.detHeader .delimorePic:hover .deCategoryDiv {
    display: block
}

.detHeader .deCategoryDiv li {
    margin-bottom: 8px;
    overflow: hidden;
}

.detHeader .deCategoryDiv li:last-child {
    margin-bottom: 0 !important
}

.detHeader .deCategoryDiv b {
    font-size: 18px;
    color: #333;
    font-family: "dinot-bold"
}

.detHeader .deCategoryDiv span {
    font-size: 16px;
    float: right;
    margin-top: -2px;
    margin-left: 4px
}

.detHeader .deCategoryDiv span i {
    margin-left: 3px;
}

.qrdRight .H1 {
    font-size: 24px !important;
    color: #333;
    font-weight: bold;
    float: left;
    margin-top: 5px;
    margin-right: 10px;
}

.headP {
    float: right;
    font-size: 16px;
    color: #ff5200;
    font-weight: bold;
    margin-top: -8px
}

.qrdRightD {
    overflow: hidden
}

.headP span {
    font-size: 20px;
    color: #ff5200;
    margin-left: 8px;
    font-family: "dinot-bold";
    font-weight: bold
}

.headP b {
    font-size: 16px
}

.detHeader .deCategoryUl1 > li > span {
    font-size: 14px;
    color: #272727;
    font-family: "dinot-bold"
}

.detHeader .deCategoryUl1 > li > span > b {
    font-size: 16px;
    color: #ff5200;
    font-family: "dinot-bold";
    margin-left: 5px;
}

.headC {
    float: left;
    font-size: 14px;
    color: #272727
}

.detHeader .btn_1 {
    width: 120px;
    height: 34px;
    color: #fff;
    background-color: #ff6100;
    border: 0;
    border-radius: 3px;
    margin-left: 20px;
    font-size: 14px;
    cursor: pointer;
    line-height: 34px;
    margin-top: 0
}

.detHeader .deCategory {
    overflow: hidden;
    margin-top: 5px;
}

.comtittle2 {
    width: 100%;
    margin: auto;
    margin-top: 0 !important;
    margin-bottom: 0 !important
}

.comtittleDiv {
    background-color: #f5f5f5
}

.headFixed {
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 9999;
    left: 0;
    display: none;
    background: #fff
}

.comtittle2 ul {
    overflow: hidden;
    width: 1170px;
    margin: auto;
    padding-left: 40px
}

.comtittle ul {
    overflow: hidden;
    width: 1130px;
    margin: auto;
    padding-left: 40px
}

.block {
    display: block
}

.qrdRight {
    margin-bottom: 10px
}

.pro_name .wait_1 {
    background-color: #ff6100;
    box-shadow: 1px 1.732px 5px 0 rgba(254, 97, 16, 0.3);
    background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/Now.png)
}

.loginQASK {
    width: 130px;
    height: 30px;
    border: 0;
    line-height: 30px;
    color: #fff;
    text-align: center;
    font-size: 14px;
    background-color: #fe6116;
    border-radius: 2px;
    margin-left: 50px;
    cursor: pointer;
    float: left;
    margin-top: 53px
}

.loginQASK:hover {
    color: #fff
}

.askbutton {
    width: 130px;
    height: 30px;
    line-height: 30px;
    border: 0;
    color: #fff;
    font-size: 14px;
    background-color: #fe6116;
    border-radius: 2px;
    margin-left: 14px;
    cursor: pointer;
    float: right;
    margin-top: 15px;
    text-align: center
}

.askbutton:hover {
    color: #fff
}

.jinnangdisB {
    display: inline-block;
    border-radius: 2px;
    width: 72px;
    height: 26px;
    color: #fff !important;
    font-size: 14px !important;
    font-weight: bold;
    line-height: 26px;
    text-align: center;
    margin-right: 10px
}

.tcbg {
    background: #00000080;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999;
    height: 100%;
    display: none
}

.content_box {
    width: 500px;
    height: 435px;
    background: #fff;
    border-radius: 10px;
    position: fixed;
    top: 50%;
    margin-top: -220px;
    left: 50%;
    margin-left: -250px
}

.yuyueP {
    font-size: 20px;
    color: #333;
    font-weight: bold;
    text-align: center;
    margin-top: 30px
}

.properties {
    width: 410px;
    margin: auto;
    height: 43px;
    border: 1px solid #f0f0f0;
    line-height: 43px;
    margin-top: 25px
}

.name, .phone, .Verification {
    margin-top: 15px;
    text-align: center;
    position: relative
}

.content_box input {
    width: 396px;
    height: 43px;
    border: 1px solid #f0f0f0;
    padding-left: 14px
}

.properties h1 {
    margin-left: 14px;
    font-size: 14px;
    color: #333 !important
}

.Verification button {
    right: 65px;
    background: #fff;
    position: absolute;
    font-size: 14px;
    color: #333;
    border: 0;
    border-left: 1px solid #f0f0f0;
    padding-left: 18px;
    top: 12px;
    width: 95px;
    cursor: pointer
}

.btn {
    text-align: center
}

.formDiv {
    width: 420px;
    margin: auto;
    height: 225px;
    background: #fff;
    border-radius: 5px;
    text-align: center;
    position: fixed;
    left: 50%;
    margin-left: -210px;
    top: 50%;
    margin-top: -112px
}

.formDiv img {
    margin-top: 50px
}

.formDiv h2 {
    margin-top: 6px;
    color: #ff5200;
    margin-bottom: 32px;
    float: inherit
}

.formDiv p {
    font-size: 14px;
    color: #666
}

.btn button {
    width: 420px;
    height: 45px;
    border: 0;
    background: #fe6116;
    color: #fff;
    font-size: 14px;
    margin-top: 35px;
    border-radius: 4px;
    cursor: pointer
}

.list_box {
    background: #fff;
    width: 409px
}

.list_box li {
    width: 408px;
    text-align: center;
    height: 45px;
    font-size: 14px;
    line-height: 45px;
    color: #333;
    border-bottom: 1px solid #f0f0f0
}

.cityBra {
    color: #333;
    padding: 0;
    float: left;
    width: 410px;
    width: 409px;
    margin-right: 0;
    margin-top: -90px;
    position: fixed;
    margin-left: -206px;
    font-size: 14px;
    top: 50%;
    left: 50%;
    box-shadow: 2px 5px 7px 2px #dedbdb;
    display: none;
    overflow-x: hidden
}

.cityBra {
    color: #333;
    padding: 0;
    float: left;
    width: 410px;
    width: 409px;
    margin-right: 0;
    margin-top: -90px;
    position: fixed;
    margin-left: -206px;
    font-size: 14px;
    top: 50%;
    left: 50%;
    box-shadow: 2px 5px 7px 2px #dedbdb;
    display: none;
    overflow-x: hidden
}

.properties_box {
    width: 409px;
    display: none;
    background: #fff;
    color: #333;
    position: fixed;
    box-shadow: 2px 5px 7px 2px #dedbdb;
    margin-top: -90px;
    position: fixed;
    margin-left: -206px;
    font-size: 14px;
    top: 50%;
    left: 50%;
    overflow-x: hidden
}

.cityScoll {
    overflow-y: scroll;
    height: 184px
}

.properties_box li {
    width: 408px;
    text-align: center;
    height: 45px;
    font-size: 14px;
    line-height: 45px;
    color: #333;
    border-bottom: 1px solid #f0f0f0
}

.yuyueC {
    float: right;
    position: absolute;
    right: 55px;
    top: 20px
}

.delxl {
    float: right;
    margin-top: -30px;
    margin-right: 18px
}

.block {
    display: block
}

.hide {
    display: none
}

.aircraft {
    width: 239px;
    height: 174px;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/aircraft.png) no-repeat;
    background-size: 100%;
    position: fixed;
    cursor: pointer;
    right: 0;
    z-index: 9;
    bottom: 150px !important;
    margin-top: 58px;
    animation: aircraft 5s ease-in-out, shake1 4s linear 5s infinite;
    -webkit-animation: aircraft 5s ease-in-out, shake1 4s linear 5s infinite;
}

.deImageL img {
    width: 782px;
    height: 424px
}

.contentShare {
    font-size: 14px !important;
    color: #666 !important;
    float: right;
    line-height: 21px !important;
    height: 21px !important;
    background: url(https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon2.jpg) no-repeat 0 3px !important;
    padding-left: 25px !important;
    margin: 0 !important
}

.comtittle {
    padding-left: 0 !important
}

.detHeader .deCategorySpan {
    margin-top: 2px;
    display: inline-block;
    width: 68px;
    padding-right: 18px;
    height: 18px;
    text-align: center;
    border: 1px solid #ebebeb;
    line-height: 18px;
    font-size: 16px;
    color: #333 !important;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/deGD.png) no-repeat 70px 7px
}

.deCategoryDivSpan {
    display: block;
    width: 0;
    height: 0;
    /* border-left: 10px solid #00000003; */
    /* border-right: 8px solid #00000003; */
    border-bottom: 16px solid #fff;
    position: absolute;
    margin-top: -19px !important;
    margin-left: 113px !important;
}

.deCategoryUl2 {
    display: block !important
}

.deCategoryUl2 > li > span > i {
    font-size: 14px;
    width: 22px;
    height: 20px;
    display: inline-block;
    color: #666;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/jun.png) no-repeat !important;
    background-size: 100% 100%;
    padding-left: 5px;
    text-align: center
}

.formDivbg {
    background: #00000080;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999;
    height: 100%
}

.main_window {
    margin-top: -20px !important;
    padding-top: 20px !important
}

.inEWM {
    box-shadow: 0 5px 22px 0 #999999c4 !important
}

@-webkit-keyframes shake1 {
    0% {
        transform: translateY(0px);
        -webkit-transform: translateY(0px)
    }
    50% {
        transform: translateY(-13px);
        -webkit-transform: translateY(-13px)
    }
    100% {
        transform: translateY(0px);
        -webkit-transform: translateY(0px)
    }
}

.loading-orange {
    position: absolute;
    overflow: hidden;
    width: 250px;
    margin-top: 20px;
    left: 0
}

.loading-orange ul {
    width: 500px;
    animation: swipeB 4s infinite linear;
    -webkit-animation: swipeB 4s infinite linear;
    -moz-animation: swipeB 4s infinite linear;
    -ms-animation: swipeB 4s infinite linear;
    -o-animation: swipeB 4s infinite linear
}

.loading-orange ul li {
    float: left
}

@keyframes swipeB {
    0% {
        transform: translateX(0)
    }
    to {
        transform: translateX(-250px)
    }
}