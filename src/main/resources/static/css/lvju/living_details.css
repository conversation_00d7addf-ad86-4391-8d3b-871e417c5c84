* {
    margin: 0;
    padding: 0
}

b {
    font-weight: 400
}

.banner {
    width: 100%;
    height: 300px;
    margin: 0 auto
}

.swiper-container {
    width: 100%;
    height: 100%
}

.swiper-slide {
    position: relative;
    text-align: center;
    font-size: 18px;
    background: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center
}

.swiper-slide img {
    width: 100%;
    height: 100%
}

.swiper-slide p {
    padding: 3px 15px;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 12px;
    position: absolute;
    bottom: 28px;
    right: 18%;
    border-radius: 3px
}

.swiper-button-next {
    width: 27px !important;
    height: 51px !important;
    background: url("https://static.fangxiaoer.com/global/imgs/ico/banner_qh0.png") no-repeat !important;
    background-position: -36px 0 !important;
    background-size: 236% !important;
    right: 50px !important
}

.swiper-button-prev {
    width: 27px !important;
    height: 51px !important;
    background: url("https://static.fangxiaoer.com/global/imgs/ico/banner_qh0.png") no-repeat !important;
    background-size: 236% !important;
    left: 50px !important
}

.content {
    width: 1170px;
    margin: 0 auto
}

.comLocation {
    font-size: 12px;
    color: #999;
    margin: 20px 0 0 0
}

.comLocation a {
    color: #999
}

.left {
    width: 865px;
    overflow: hidden;
    float: left;
    margin-right: 25px
}

.left_tit {
    width: 100%;
    height: 70px;
    line-height: 70px;
    font-size: 25px;
    color: #333
}

.info {
    width: 100%;
    height: 30px;
    overflow: hidden;
    border-bottom: 1px solid #ddd
}

.info p {
    font-size: 13px;
    color: #b1b2cb;
    float: left
}

.bdshare-button-style0-16 a, .bdshare-button-style0-16 .bds_more {
    float: left;
    font-size: 12px;
    padding-left: 17px;
    line-height: 16px;
    height: 16px;
    background-image: url(../img/share/icons_0_16.png?v=91362611.png);
    background-repeat: no-repeat;
    cursor: pointer;
    margin: 6px 6px 6px 0
}

.info .inspan {
    float: right;
    color: #f60;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/guanzhu.gif") no-repeat;
    font-size: 19px;
    margin-right: 15px;
    text-indent: 1.3em;
    line-height: 16px
}

.question {
    width: 100%;
    margin-top: 15px;
    overflow: hidden
}

.question P {
    font-size: 15px;
    color: #000;
    line-height: 33px;
    text-indent: 2em
}

.question P img {
    display: block;
    margin: 45px auto
}

.question h2 {
    color: #000 !important;
    text-indent: 1.8em;
    font-weight: bold !important;
    margin-bottom: 4px;
    margin-top: 30px
}

.right {
    display: block;
    width: 280px;
    float: right;
    position: relative;
    margin-top: 25px;
    margin-bottom: 30px
}

.main_window {
    overflow: hidden;
    display: block;
    padding: 0;
    float: right;
    margin: 0 auto;
    width: 250px;
    height: 460px;
    position: relative
}

.inEWM {
    width: 200px;
    height: 400px;
    margin: auto;
    background-color: #fff;
    border-radius: 19px;
    margin-top: 1px;
    box-shadow: 0 5px 22px -6px #999
}

div {
    margin: 0;
    padding: 0
}

.cloud {
    width: 208px;
    position: absolute;
    top: 100
}

[data-type=white_1]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff
}

[data-type=white_2]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff
}

[data-type=white_3]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff
}

[data-type=white_4]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 20px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 80px 0 0 10px #fff, 40px 0 0 20px #fff, 110px 0 0 -5px #fff, 80px 25px 0 10px #fff, 40px 25px 0 20px #fff, 0 40px 0 -5px #fff
}

[data-type=white_5]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 20px #fff, 80px -10px 0 10px #fff, 115px -5px 0 -10px #fff, 20px 30px 0 20px #fff, 70px 30px 0 10px #fff, 105px 40px 0 -10px #fff
}

[data-type=white_6]:before {
    content: "";
    display: block;
    width: 50px;
    height: 50px;
    top: -20px;
    left: 25px;
    background-color: #fff;
    border-radius: 30px;
    position: absolute;
    box-shadow: 40px -10px 0 15px #fff, 90px 0 0 15px #fff, 20px 30px 0 15px #fff, 80px 40px 0 15px #fff
}

@-webkit-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-moz-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-ms-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@-o-keyframes linemove {
    from {
        left: 00px
    }
    to {
        left: 320px
    }
}

@keyframes linemove {
    from {
        left: 0
    }
    to {
        left: 320px
    }
}

@-webkit-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@keyframes linemove1 {
    from {
        left: -50px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@keyframes linemove2 {
    from {
        left: -210px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@keyframes linemove3 {
    from {
        left: -120px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@keyframes linemove4 {
    from {
        left: -110px
    }
    to {
        left: 300px
    }
}

@-webkit-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-moz-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-ms-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@-o-keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

@keyframes linemove6 {
    from {
        left: -30px
    }
    to {
        left: 300px
    }
}

[data-speed="1"] {
    animation: linemove1 3.01936s;
    -webkit-animation: linemove1 3.01936s
}

[data-speed="2"] {
    animation: linemove2 6.67113s;
    -webkit-animation: linemove2 6.67113s
}

[data-speed="3"] {
    animation: linemove3 1.99853s;
    -webkit-animation: linemove3 1.99853s
}

[data-speed="4"] {
    animation: linemove4 3.01936s;
    -webkit-animation: linemove4 3.01936s
}

[data-speed="5"] {
    animation: linemove2 6.20363s;
    -webkit-animation: linemove2 6.20363s
}

[data-speed="6"] {
    animation: linemove 12.46404s;
    -webkit-animation: linemove 12.46404s
}

.cloud {
    -webkit-animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    -webkit-animation-delay: -50s
}

.recommend {
    width: 275px;
    overflow: hidden;
    border: 1px solid #eaeaea;
    margin: 0 auto
}

.recommend-title {
    width: 275px;
    height: 46px;
    overflow: hidden;
    border-bottom: 1px solid #eaeaea;
    margin: 0 auto
}

.chengse {
    width: 4px;
    height: 18px;
    border-radius: 5px;
    background: #ff5200;
    margin: 14px 12px 14px 12px;
    float: left
}

.tuijian {
    float: left;
    font-size: 17px;
    color: #000;
    line-height: 45px
}

.right_list {
    width: 224px;
    height: auto;
    margin: 0 auto
}

.right_list ul li {
    width: 224px;
    height: auto;
    border-bottom: 1px solid #eaeaea;
    margin-top: 16px
}

.right_list ul li:last-child {
    border-bottom: 0
}

.recommend-img {
    width: 100%;
    height: 172px;
    position: relative
}

.recommend-img img {
    width: 100%;
    height: 100%
}

.recommend-img p {
    width: 100%;
    height: 30px;
    background: rgba(0, 0, 0, 0.48);
    position: absolute;
    left: 0;
    bottom: 0;
    line-height: 30px;
    text-indent: 1em;
    color: #fff;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.unstyled {
    overflow: hidden;
    margin-top: 11px;
    font-size: 13px;
    color: #000
}

.unstyled span {
    float: right;
    margin-right: 7px
}

.unstyled b {
    font-size: 14px;
    font-weight: bold;
    color: #ff5200
}

.ts_desc {
    font-size: 13px;
    color: #ff5200;
    margin-top: 6px;
    margin-bottom: 14px
}

.aircraft {
    width: 239px;
    height: 174px;
    background: url(https://static.fangxiaoer.com/web/images/lvjuImg/aircraft.png) no-repeat;
    background-size: 100%;
    position: fixed;
    right: 0;
    bottom: 35px;
    z-index: 9;
    animation: aircraft 5s ease-in-out, shake1 4s linear 5s infinite;
    -webkit-animation: aircraft 5s ease-in-out, shake1 4s linear 5s infinite
}

.ai {
    width: 111px;
    height: 87px;
    margin-left: 27px;
    margin-top: 31px;
    text-align: center
}

.ai h1 {
    font-size: 17px;
    color: #333;
    font-weight: bold
}

.ai p {
    font-size: 15px;
    color: #333;
    margin-top: 2px
}

.ai p span {
    font-size: 16px;
    font-weight: bold;
    color: #ff5200
}

@-webkit-keyframes aircraft {
    0% {
        transform: translate(400px, -300px);
        -webkit-transform: translate(400px, -300px)
    }
    100% {
        transform: translate(0px, 0px);
        -webkit-transform: translate(0px, 0px)
    }
}

.houseContext {
    display: block;
    line-height: 35px !important;
    border: 1px solid #eee;
    text-indent: 0;
    text-align: left;
    padding-top: 14px;
    padding-bottom: 14px;
    margin-bottom: 20px;
    margin-top: 20px
}

.houseContext span {
    line-height: 35px !important;
    font-size: 15px !important;
    color: #000 !important
}

.question a {
    border-bottom: 1px dashed #06f;
    color: #06f;
    text-decoration: none
}

.houseleft {
    display: block;
    float: left;
    position: relative;
    width: 210px;
    height: 154px;
    margin-left: 15px
}

.houseleft .pic {
    width: 210px;
    height: 154px;
    margin-top: 0;
    text-indent: 0
}

.houseleft .play {
    position: absolute;
    left: 50%;
    top: 50px;
    margin-left: -31px;
    margin-top: 0
}

.houseContext .houseRight {
    display: block;
    float: right;
    width: 608px;
    position: relative
}

.houseTitle {
    font-size: 18px;
    color: #57a0e7;
    cursor: pointer;
    line-height: 22px;
    display: inline-block
}

.houseLayout {
    display: block;
    line-height: normal !important;
    margin-top: 8px;
    height: 35px
}

.houseContext .houseRight .price {
    position: absolute;
    top: 35px;
    right: 20px;
    color: #a9b6ca !important;
    line-height: 26px
}

.houseContext .houseRight .price .priceNum {
    color: #ff5200;
    font-size: 26px;
    font-family: Georgia;
    font-weight: bold;
    margin-left: 5px;
    margin-right: 5px
}

.houseContext .houseRight .price .priceSquaermeter {
    color: #ff5200;
    font-size: 14px
}

.street {
    margin-top: 8px;
    display: block;
    width: 400px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.houseTel {
    margin-top: 10px;
    color: #333 !important;
    font-family: dinot-bold;
    font-size: 16px;
    padding-left: 20px;
    background-image: url("https://static.fangxiaoer.com/web/images/ico/phone.png");
    background-repeat: no-repeat;
    background-position: left center
}

.houseTel .tip {
    font-size: 14px !important;
    margin-left: 5px;
    margin-right: 5px
}

.contentShare {
    background: url(https://static.fangxiaoer.com/web/images/sy/sale/fenxiang.gif) no-repeat 0 5px !important
}

.clearfixwen {
    clear: both;
    display: block
}

.container {
    display: none
}

.loading-orange span {
    background: url("https://static.fangxiaoer.com/web/images/lvjuImg/dm3.png") no-repeat;
    background-size: 100% 100%;
    width: 250px;
    height: 165px;
    display: inline-block
}