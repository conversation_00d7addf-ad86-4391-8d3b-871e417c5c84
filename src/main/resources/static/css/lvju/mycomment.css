* {
    margin: 0;
    padding: 0
}
a:hover {
    text-decoration: none !important
}

.content {
    width: 960px;
    margin: 0 auto
}

.comLocation {
    font-size: 12px;
    color: #999;
    margin: 20px 0
}

.comLocation a {
    color: #999
}

.pro_name {
    width: 958px;
    border: 1px solid #ddd;
    margin-bottom: 15px;
    color: #333;
    line-height: 28px;
    margin-top: 12px;
    overflow: hidden;
    position: relative
}

.containerHeader {
    font-weight: 400;
    font-size: 18px;
    padding-left: 35px;
    background: #f6f6f6;
    line-height: 55px;
    margin: 0;
    color: #333;
    border-bottom: 1px solid #ddd
}

.xiangxi, .photo {
    width: 100%;
    overflow: hidden
}

.xiangxi .left, .photo .left {
    width: 200px;
    float: left;
    font-size: 14px;
    padding-left: 50px;
    line-height: 60px;
    font-weight: bold;
    color: #333
}

.xiangxi .left i {
    color: #ff6820;
    padding-right: 5px
}

.xiangxi .bottom, .photo .bottom {
    width: 100%;
    float: left
}

.xiangxi .bottom p, .xiangxi .bottom .warm, .photo .bottom p, .photo .bottom .warm {
    margin-left: 100px;
    text-indent: 0;
    text-align: right;
    margin-top: -40px;
    padding-right: 50px;
    font-size: 14px
}

i, em, s {
    font-style: normal;
    text-decoration: none
}

#virtues {
    width: 860px;
    height: 100px;
    margin-left: 50px;
    border: 1px solid #ececec;
    resize: none;
    padding: 6px 8px;
    font-family: 'å¾®è½¯é›…é»‘';
    font-weight: normal;
    font-size: 13px;
    border-radius: 5px
}

.photoTitle {
    line-height: 29px;
    font-weight: bold;
    padding-left: 50px;
    margin-top: 6px;
    font-size: 14px;
    margin-bottom: 10px
}

#zi {
    color: #ff5200
}

#fileImg {
    height: 158px;
    border: 1px solid #ececec
}

#fileImg {
    position: relative;
    height: 200px !important;
    width: 877px
}

.webuploader-container {
    position: relative
}

#fileImg > div {
    margin-left: 100px;
    top: 25px
}

.webuploader-pick {
    position: relative;
    display: inline-block;
    cursor: pointer;
    background: url("https://static.fangxiaoer.com/web/images/sy/sale/form/icons_pic.png");
    color: #fff;
    text-align: center;
    border-radius: 3px;
    overflow: hidden
}

.webuploader-pick {
    width: 96px;
    height: 84px;
    line-height: 40px;
    font-size: 16px;
    margin: 34px 0 0 50px
}

#preview {
    position: absolute;
    top: 0 !important;
    left: 0;
    width: 96px;
    height: 84px;
    overflow: hidden;
    bottom: auto;
    right: auto;
    z-index: 99999
}

.xiaotieshi {
    width: 562px !important;
    position: absolute;
    top: 33px !important;
    left: 331px !important;
    font-size: 14px;
    line-height: 28px
}

.xiaotieshi h3 {
    font-size: 16px;
    font-weight: bold
}

.tijiao {
    width: 100%;
    float: left;
    padding: 25px 0 25px 0;
    position: relative
}

.tijiao input {
    float: left;
    display: block;
    margin: 0 auto
}

.tijiao_btn {
    background: #ff5200;
    color: #fff;
    width: 290px !important;
    height: 50px;
    font-size: 18px;
    text-align: center;
    border: navajowhite;
    border-radius: 5px
}

.bt {
    width: 15px;
    height: 15px;
    border: 1px solid #dbdbdb;
    float: left;
    margin-left: 30px;
    margin-right: 6px;
    float: left;
    margin-top: 18px
}

#cb_IsNiming {
    margin: 30px 10px 0 0;
    display: none
}

.tijiao label {
    margin-top: 17px;
    margin-left: 10px;
    float: left
}

label {
    display: block;
    margin-bottom: 5px
}

label, input, button, select, textarea {
    font-size: 14px;
    font-weight: normal;
    line-height: 20px
}

textarea:focus {
    box-shadow: 0 0 5px 1px rgba(130, 205, 251, 0.63);
    transition: border linear .2s, box-shadow linear .2s
}

.tijiao a {
    float: right;
    margin-top: 15px;
    margin-right: 23px;
    text-decoration: none;
    color: #333;
    font-size: 13px
}

.color {
    background: url("https://static.fangxiaoer.com/web/images/ico/Select_sun.png") no-repeat center
}

.comLocation a:hover {
    color: #ff5200
}

#preview:hover {
    background: url("https://static.fangxiaoer.com/web/images/sy/sale/form/icons_pic.png") -127px 0
}

.pic img {
    width: 85%;
    height: 90%;
    margin: 5% 7.5%
}

.parentFileBox > .fileBoxUl > li > .diyCancel, .parentFileBox > .fileBoxUl > li > .diySuccess {
    bottom: 3px !important
}