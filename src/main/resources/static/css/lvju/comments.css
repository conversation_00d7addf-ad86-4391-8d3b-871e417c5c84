*{
    margin: 0;
    padding: 0;
}
a:hover{
    text-decoration: none !important;
}
.content{
    width: 1170px;
    margin: 0 auto;
    margin-bottom: 27px;
}
.housesLeftNo{

    font-size:14px !important;
    color: #99999 !important;
}
.comLocation{
    font-size: 12px;
    color: #999999;
    margin: 20px 0;
}
.comLocation a{
    color: #999999;
}
.left{
    width: 865px;
    overflow: hidden;
    float: left;
    margin-right: 25px;
}
.right{
    display: block;
    width: 258px;
    float: right;
    position: relative;
}
.pro_name{
    margin-bottom: 15px;
    font-size: 24px;
    color: #333;
    height: 65px;
    line-height: 28px;
    margin-top: 12px;
    position: relative;
}
.pro_name #qrcode{
    float: left;
    height: 60px;
    margin: 0;
    width: 60px;
    margin-right: 10px;
}
#type1:hover{
    background:#ff6100 !important;
    color: #ffffff !important;
}
#qrcode{
    position: relative;
}
#qrcode .layer_wei{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .85);
    display: none;
}
#qrcode .layer_wei h1{
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 15px;
}
#qrcode .layer_wei h2{
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 5px;
}
#qrcode img{
    width: 100%;
    height: 100%;
}
.box_sun {
    float: left;
}
.pro_name p{
    float: none;
    margin-right: 19px;
}
.centigrade{
    margin-left: 20px;
    font-weight: 400 !important;
}
.pro_name .wait_1 {
    background-color: #ff6100;
    box-shadow: 1px 1.732px 5px 0px rgba( 254, 97, 16,0.3 );
}
.pro_name .type_sun {
    width: 58px;
    height: 25px;
    display: block;
    float: left;
    font-size: 15px;
    line-height: 25px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
    font-weight: bold;
    margin-top: 4px;
}
.wait_2 span{
    float: left;
    font-size: 14px;
    color: #FFF;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border: 1px solid #eff3fd;
    color: #586c94;
    border-radius: 2px;
    padding: 0 6px;
    background: #f3f5f7;
    margin-top: 7px;
    margin-left: 10px;
}
.pro_name .s_time {
    padding-left: 40px;
    /* background: url(https://static.fangxiaoer.com/web/images/sy/house/view/s_time.gif) 20px no-repeat; */
    position: absolute;
    right: 0;
    font-size: 12px;
    bottom: -7px;
    color: #999;
    z-index: 9999;
}
.w{
    margin: 0 auto;
    height: auto;
    width: 1170px !important;
}
.nav_house {
    line-height: 50px;
    height: 50px;
    text-align: center;
    margin-bottom: 20px;
    background: #f5f5f5;
    margin-bottom: 20px !important;
}
.house_move_act ul{
    /*overflow: hidden;*/
    height: 50px;
    list-style: none;
}
.nav_house li{
    float: left;
    margin: 0 !important;
    color: #ff6600;
    font-size: 14px;
    font-weight: bold;
    line-height: 46px;
}
.comtittle li a{
    display: inline-block;
    /* padding: 0 25px; */
    font-size: 16px;
    height: 48px;
    text-decoration: none;
    color: #333333;
}
.uuipj{
    width: 898px;
    float: left;
    border: 1px solid #ededed;
    border-bottom: none;
}
.wuuiptyi{
    overflow: hidden;
    margin-top: 25px;
}
.comtittle li .hover{
    color: #ff5200 !important;
    border-bottom: 2px solid #ff5200 !important;
}
.housesLeft {
    width: 100%;
    float: left;
    padding-top: 0;
    height: 135px;
    line-height: 135px;
    margin-top: 25px;
}
.housesLeft {
    padding: 0;
    background: #fff;
    margin: 0 auto;
    overflow: hidden;
    border-bottom: 1px solid #ededed;
}
.housesLeft div{
    width: 500px;
    text-align: right;
    float: left;
    font-size: 16px;
    color: #999999;
}
.housesLeft button{
    width: 130px;
    height: 30px;
    border: none;
    color: #ffffff;
    font-size: 14px;
    background-color: #fe6116;
    border-radius: 2px;
    margin-left: 50px;
    cursor: pointer;
    float: left;
    margin-top: 53px;
}
.synthesize {
    width: 90%;
    overflow: hidden;
    padding: 10px 0 10px 30px;
    border-bottom: 1px solid #ededed;
    margin: 0 auto;
}
.synthesize .synthesizeRight {
    float: right;
    font-size: 14px;
    color: #ff5200;
    padding-top: 10px;
    padding-left: 20px;
    padding-bottom: 10px;
    margin-top: 2px;
}
.synthesize .synthesizeRight #CommentListAdd {
    color: #fff;
    font-size: 14px;
    background: #ff5200;
    display: inline-block;
    width: 130px;
    line-height: 30px;
    text-align: center;
    border-radius: 2px;
    margin-left: 20px;
    cursor: pointer;
}
.synthesizeRight span{
    color: #333333;
}
.userSynthesize{
    width: 100%;
}
.userSynthesize .li{
    overflow: hidden;
    border-bottom: 1px solid #ededed;
    position: relative;
    list-style: none;
}
.userSynthesize td{
    padding-top: 20px;
    padding-bottom: 20px;
}
.userSynthesize .contentRight{
    float: left;
    width: 198px;
    margin: 31px 0;
    /* border-right: 1px #ededed dashed; */
}
.userSynthesize .contentRight .headerImg{
    position: relative;
}
.userSynthesize .contentRight .headerImg .headerImges{
    width: 50px;
    height: 50px;
    border-radius: 50px;
    overflow: hidden;
    margin: 0 auto;
    position: relative;
}
.userSynthesize .contentRight .headerImg.hover .ueserName{
    padding-top: 10px;
}
.userSynthesize .contentRight .headerImg .ueserName{
    text-align: center;
}
.userSynthesize .contentRight .headerImg .headerImges img{
    width: 100%;
    height: 100%;
}
.userSynthesize .contentLeft {
    float: left;
    width: 650px;
    padding-left: 40px;
    position: relative;
    border-left: 1px #ededed dashed;
}
.userSynthesize .contentLeft .contentInfoMmore{
    width: 500px;
    position: relative;
    overflow: hidden;
}
.userSynthesize .contentLeft .contentInfoMmore li{
    border: none;
    line-height: 30px;
    list-style: none;
}
.userSynthesize .contentLeft .contentBottom {
    margin: 24px 0 0 0;
    overflow: hidden;
}
.bdshare-button-style0-16 {
    zoom: 1;
}
.userSynthesize .contentLeft .contentBottom .contentShare {
    font-size: 12px;
    color: #666;
    float: right;
    background: url("https://static.fangxiaoer.com/web/images/sy/comment/remark_tu_icon2.jpg") no-repeat 0 0px;
    padding-left: 20px;
    padding-right: 19px;
    margin-top: 5px;
    line-height: 17px;
}
.userSynthesize .contentLeft .contentBottom .contentTime {
    font-size: 12px;
    color: #666;
    float: left;
    line-height: 23px;
}
.right .salesOffice {
    height: 152px;
    padding-bottom: 20px;
}
.right .salesOffice p {
    margin-top: 23px;
}
.right .houseRight {
    border: 1px solid #ededed;
    margin-bottom: 20px;
    padding: 0 20px;
    overflow: hidden;
}
.right .salesOffice span {
    color: #ff5200;
    display: inline-block;
    padding-top: 6px;
}
.right .salesOffice span b {
    font-size: 20px;
    font-family: dinot-bold;
    font-weight: normal;
}
.right .salesOffice span b b {
    font-size: 14px;
    padding: 0 2px;
}
.right .houseRightBtn {
    background: #ff6100;
    color: #fff;
    display: block;
    text-align: center;
    font-size: 14px;
    line-height: 30px;
    border-radius: 4px;
    margin-bottom: 20px;
    margin-top: 18px !important;
}
.right .houseRight {
    border: 1px solid #ededed;
    margin-bottom: 20px;
    padding: 0 20px;
    overflow: hidden;
}
.right .houseRinghtTitle{
    font-size: 18px;
    color: #333333;
    line-height: 55px;
    border-bottom: 1px solid #ededed;
}
.houseRight ul li{
    margin-top: 10px;
    border-bottom: 1px solid #ededed;
    list-style: none;
}
.houseRight ul li a{
    width: 100%;
    height: 100%;
}
.lj_tit{
    width: 71px;
    height: 26px;
    line-height: 26px;
    color: #FFFFFF;
    font-size: 14px;
    text-align: center;
    border-radius: 3px;
}
.houseRight ul li a b{
    width: 100%;
    font-size: 14px;
    color: #333333;
    display: block;
    margin-top: 7px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.houseRight ul li a b:hover{
    color:#ff5200;
}
.houseRight ul li a p{
    width: 100%;
    font-size: 14px;
    color: #333333;
    margin-top: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: 10px;
}
.houseRight ul li:nth-child(1) .lj_tit{
    background: #ff9d00;
    box-shadow: 0 0 3px #ff9d00;
}
.houseRight ul li:nth-child(2) .lj_tit{
    background: #fd494e;
    box-shadow: 0 0 3px #fd494e;
}
.houseRight ul li:nth-child(3) .lj_tit{
    background: #5189f9;
    box-shadow: 0 0 3px #5189f9;
}
.contentBottom .contentShow{
    font-size: 14px;
    color: #666;
    cursor: pointer;
    float: left;
    padding-right: 30px;
}
.contentBottom .contentShow.openAndClose.open {
    background: url("https://static.fangxiaoer.com/web/images/sy/house/userEvaluate1.png") no-repeat 62px 9px;
    margin: -2px 0 0 297px;
    line-height: 23px;
}
.contentBottom .contentShow.openAndClose{
    background: url("https://static.fangxiaoer.com/web/images/sy/house/userEvaluate2.png") no-repeat 62px 9px;
    margin: -2px 0 0 297px;
    line-height: 23px;
}
.ov{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.contentInfoImg{
    overflow: hidden;
    padding-top: 20px;
    /* padding-bottom: 20px; */
    width: 500px;
}
.contentInfoImg div{
    float: left;
    padding-right: 8px;
    padding-top: 8px;
}
.contentInfoImg div img {
    width: 110px;
    height: 80px;
    cursor: pointer;
}
.webuploader-element-invisible {
    position: absolute !important;
    clip: rect(1px,1px,1px,1px);
}


.bigImgShow {
    display: none;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1000000;
    /* background: url(https://static.fangxiaoer.com/global/imgs/ico/b60.png); */
    background: rgba(0, 0, 0, 0.85);
}

.bigImgShow .showImg {
    overflow: hidden;
    width: 100%;
    max-width: 1170px;
    height: 75%;
    margin: 0 auto;
    margin-top: 6.5%;
}

.bigImgShow .showImg ul {
    overflow: hidden;
    width: 99999999999999px;
    height: 100%;
}

.bigImgShow .showImg ul li {
    overflow: hidden;
    position: relative;
    float: left;
    width: 1170px;
    height: 100%;
}

.bigImgShow .showImg ul li img {
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
}

.bigImgShow .close {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
}

.bigImgShow .prev {
    position: absolute;
    top: 50%;
    left: 0;
    width: 60px;
    margin-top: -57px;
    height: 114px;
    background: url(https://static.fangxiaoer.com/global/imgs/ico/white_ico1.png)no-repeat top left;
    cursor: pointer;
}

.bigImgShow .next {
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -57px;
    width: 64px;
    height: 114px;
    background: url(https://static.fangxiaoer.com/global/imgs/ico/white_ico1.png)no-repeat top right;
    cursor: pointer;
}
.expertTellK .bigImgShow {
    background: none;
    display: none;
}
.bigImgShow .showImg ul li img {
    margin-top: -220px;
    margin-left: -250px;
    width: 500px;
}