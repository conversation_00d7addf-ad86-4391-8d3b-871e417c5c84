* {
    margin: 0;
    padding: 0
}

.content {
    width: 1170px;
    margin: 0 auto
}

.comLocation {
    font-size: 12px;
    color: #999;
    margin: 20px 0
}

.comLocation a {
    color: #999
}

.pro_name {
    margin-bottom: 15px;
    font-size: 24px;
    color: #333;
    height: 65px;
    line-height: 28px;
    margin-top: 12px;
    position: relative
}

.pro_name #qrcode {
    float: left;
    height: 60px;
    margin: 0;
    width: 60px;
    margin-right: 10px
}

#type1:hover {
    background: #ff6100 !important;
    color: #fff !important
}

#qrcode {
    position: relative
}

#qrcode .layer_wei {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .85);
    display: none
}

#qrcode .layer_wei h1 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 15px
}

#qrcode .layer_wei h2 {
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    line-height: 14px;
    padding-top: 5px
}

#qrcode img {
    width: 100%;
    height: 100%
}

.box_sun {
    float: left
}

.pro_name p {
    float: none;
    margin-right: 19px
}

.centigrade {
    margin-left: 20px
}

.pro_name .wait_1 {
    background-color: #ff6100;
    box-shadow: 1px 1.732px 5px 0 rgba(254, 97, 16, 0.3)
}

.pro_name .type_sun {
    width: 58px;
    height: 25px;
    display: block;
    float: left;
    font-size: 15px;
    line-height: 25px;
    text-align: center;
    color: #fff;
    border-radius: 3px;
    font-weight: bold;
    margin-top: 4px
}

.pro_name a {
    float: left;
    font-size: 14px;
    color: #FFF;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border: 1px solid #eff3fd;
    color: #586c94;
    border-radius: 2px;
    padding: 0 6px;
    background: #f3f5f7;
    margin-top: 7px;
    margin-left: 10px
}

.pro_name .s_time {
    padding-left: 40px;
    position: absolute;
    right: 0;
    font-size: 12px;
    bottom: -7px;
    color: #999;
    z-index: 9999
}

.w {
    margin: 0 auto;
    width: 1170px !important
}

.nav_house {
    line-height: 50px;
    height: 50px;
    text-align: center;
    margin-bottom: 20px;
    background: #f5f5f5
}

.house_move_act ul {
    overflow: hidden;
    height: 50px
}

.nav_house li {
    float: left;
    margin: 0 !important;
    color: #f60;
    font-size: 14px;
    font-weight: bold;
    line-height: 46px
}

.nav_house li a {
    display: inline-block;
    padding: 0 25px;
    font-size: 16px;
    height: 48px;
    text-decoration: none;
    color: #333
}

.nav_house li.hover a {
    color: #ff5200;
    border-bottom: 2px solid #ff5200
}

.left {
    width: 894px;
    height: auto;
    float: left
}

ul.bigImg {
    width: 100%;
    height: auto;
    overflow: hidden
}

.bigImg li {
    float: left;
    width: 280px;
    overflow: hidden;
    border: 1px solid #eee;
    border-top: 0;
    cursor: pointer;
    margin-right: 16px;
    margin-bottom: 23px;
    position: relative
}

.bigImg li div {
    height: 200px;
    overflow: hidden;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    text-align: center
}

.bigImg li img {
    height: 200px
}

.bigImg li p {
    text-align: center;
    text-overflow: ellipsis;
    display: block;
    overflow: hidden;
    height: 50px;
    white-space: nowrap;
    line-height: 50px
}

.photoSearch {
    margin-left: 12px;
    background: red
}

.photoSearch {
    border: 1px solid #e6e6e6;
    background: #f9f8f8;
    width: 250px;
    margin: 0 auto;
    line-height: 38px;
    position: relative;
    margin-left: 24px;
    float: right
}

.photoSearch a {
    color: #333;
    padding: 0 27px;
    line-height: 50px;
    margin: 2px 0 0 0;
    text-decoration: none;
    background: #fff;
    display: block;
    position: relative;
    font-size: 14px;
    border-bottom: 1px solid #ebebeb
}

.photoSearch a span {
    display: inline-block;
    float: right
}

.photoSearch a:hover {
    color: #ff5200
}

.photoSearch a.hover {
    background: #f60;
    color: #fff;
    box-shadow: -3px 3px 3px rgba(255, 82, 0, 0.4);
    margin: -1px -5px 0 -5px;
    line-height: 54px;
    border-bottom: 0
}

.highDefinition {
    width: 900px;
    height: auto;
    overflow: hidden;
    display: none
}

.highDefinition .highImg {
    width: 900px;
    height: 600px;
    overflow: hidden;
    position: relative
}

.highDefinition .highImg {
    width: 900px;
    height: 600px
}

.highDefinition .highImg ul li {
    float: left;
    position: relative;
    height: 600px;
    width: 900px
}

.highDefinition .highImg ul li img {
    height: 600px;
    width: 900px
}

.highDefinition .highImg ul li p {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 98%;
    line-height: 46px;
    color: #fff;
    font-size: 14px;
    text-align: left;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
    padding-left: 2% !important
}

.highDefinition .highImg .highImgPrev {
    background: url("https://static.fangxiaoer.com/web/images/Villa/big_left.png") no-repeat top left;
    position: absolute;
    top: 50%;
    left: 0;
    width: 54px;
    height: 114px;
    margin-top: -57px;
    cursor: pointer
}

.highDefinition .highImg .highImgNext {
    background: url("https://static.fangxiaoer.com/web/images/Villa/big_right.png") no-repeat top left;
    background-position: 0 0;
    position: absolute;
    top: 50%;
    right: 0;
    width: 50px;
    height: 114px;
    margin-top: -57px;
    cursor: pointer
}

.photoSearch p {
    float: right;
    margin: 6px 10px 0 0;
    position: absolute;
    top: 17px;
    left: -170px;
    cursor: pointer;
    line-height: 40px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding-right: 17px;
    display: none
}

.photoSearch p img {
    padding-right: 10px;
    padding-left: 19px;
    padding-top: 10px
}

.highDefinition .highImg {
    width: 900px;
    height: 600px;
    overflow: hidden;
    position: relative
}

.highDefinition .highImg ul {
    overflow: hidden;
    width: 90000px
}

.highDefinition .highImg ul li {
    float: left;
    position: relative;
    height: 600px;
    width: 900px
}

.highDefinition .highImg ul li p {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 98%;
    line-height: 46px;
    color: #fff;
    font-size: 14px;
    text-align: center;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
    padding-left: 2% !important
}

.highDefinition .highImg ul li img {
    width: 900px;
    display: block;
    margin: 0 auto
}

.highDefinition .highImg ul li span {
    position: absolute;
    top: 0;
    right: 0;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
    color: #fff;
    padding: 4px 12px
}

.highDefinition .highImg .highImgPrev {
    background: url(https://static.fangxiaoer.com/web/images/Villa/big_left.png) no-repeat top left;
    position: absolute;
    top: 50%;
    left: 0;
    width: 54px;
    height: 114px;
    margin-top: -57px;
    cursor: pointer
}

.highDefinition .highImg .firstImg {
    position: absolute;
    top: 47%;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
    display: none;
    left: 46%
}

.highDefinition .highImg .firstImg span {
    display: block;
    width: 120px;
    height: 16px
}

.highDefinition .highImg .highImgNext {
    background: url(https://static.fangxiaoer.com/web/images/Villa/big_right.png) no-repeat top left;
    background-position: 0 0;
    position: absolute;
    top: 50%;
    right: 0;
    width: 50px;
    height: 114px;
    margin-top: -57px;
    cursor: pointer
}

.highDefinition .highList {
    width: 900px;
    padding-top: 12px;
    height: 100px;
    position: relative
}

.highDefinition .highListShow {
    width: 806px;
    overflow: hidden;
    margin: 0 auto
}

.highDefinition .highListShow ul {
    width: 900000px;
    overflow: hidden
}

.highDefinition .highListShow ul li {
    float: left;
    width: 150px;
    margin-right: 14px;
    height: 100px;
    overflow: hidden
}

.highDefinition .highListShow ul li img {
    width: 100%
}

.highDefinition .highListPrev {
    background: url(https://static.fangxiaoer.com/web/images/Villa/small_left.png) no-repeat top left;
    position: absolute;
    top: 12px;
    left: 0;
    width: 30px;
    height: 100px;
    cursor: pointer
}

.highDefinition .highListNext {
    background: url(https://static.fangxiaoer.com/web/images/Villa/small_right.png) no-repeat top left;
    top: 12px;
    right: 5px;
    position: absolute;
    width: 30px;
    height: 100px;
    cursor: pointer
}

.leftImg {
    overflow: hidden;
    float: left;
    width: 893px;
    margin-bottom: 20px
}

.imgPage {
    overflow: hidden;
    margin-top: 16px;
    margin: 20px auto 0 auto;
    width: 378px;
    text-align: center
}

.imgPage div {
    float: left;
    border: 1px solid #eee;
    padding: 4px 6px;
    cursor: pointer;
    margin: 0 2px;
    font-size: 14px
}

.imgPage ul {
    float: left
}

.imgPage ul .hover {
    background: #ff5200;
    color: #fff;
    border: 1px solid #ff5200
}

.imgPage ul li {
    float: left;
    border: 1px solid #eee;
    padding: 3px 9px;
    margin: 0 2px;
    cursor: pointer
}

.position_pbl {
    width: 916px;
    overflow: hidden;
    max-height: 855px
}

.position_pbl ul {
    overflow: hidden;
    width: 913px
}

.position_pbl ul li {
    float: left;
    width: 280px;
    overflow: hidden;
    border: 1px solid #eee;
    border-top: 0;
    cursor: pointer;
    margin-right: 19px;
    margin-bottom: 23px;
    position: relative;
    font-size: 14px
}

.position_pbl ul li .wait_1 {
    position;
    absolute;
    position: absolute;
    left: 0;
    top: 0;
    background-color: #ff5200;
    color: #fff;
    display: inline-block;
    line-height: 20px;
    padding-left: 7px;
    padding-right: 7px
}

.position_pbl ul li .wait_2 {
    position;
    absolute;
    position: absolute;
    left: 0;
    top: 0;
    background-color: #ffa532;
    color: #fff;
    display: inline-block;
    line-height: 20px;
    padding-left: 7px;
    padding-right: 7px
}

.position_pbl ul li .wait_3 {
    position;
    absolute;
    position: absolute;
    left: 0;
    top: 0;
    background-color: #ecf0f3;
    color: #839aaa;
    display: inline-block;
    line-height: 20px;
    padding-left: 7px;
    padding-right: 7px
}

.position_pbl ul li div {
    height: 200px;
    overflow: hidden;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    text-align: center
}

.position_pbl ul li img {
    height: 200px
}

.position_pbl ul li p {
    text-align: center;
    text-overflow: ellipsis;
    display: block;
    overflow: hidden;
    height: 60px;
    font-size: 14px;
    white-space: nowrap;
    line-height: 60px;
    color: #333
}

.photoSearch a img {
    display: none
}