@charset "utf-8";
/* CSS Document */
.crumbs{margin-top:39px;}
.w1210 .w{width:1200px !important;}
.place {color: #999;margin: 10px auto;height:30px;line-height:30px;}
.place a{color:#999;}
.place a:hover{color:red;text-decoration:none;}
.place span{color:#333;}
.place .news {float: right;padding-left: 30px;color: #ff6600;background: url(https://static.fangxiaoer.com/web/images/ico/sign/news.gif) 8px 9px no-repeat}
.place .news a{color:#ff6600;}
.place .news a:hover{ text-decoration:underline;}
.pro_name{margin-bottom:15px;font-size:24px;color:#333; height:28px; line-height:28px;margin-top: 12px;}
.pro_name p{float:left;margin-right:19px;}
.pro_name a{float:left;width:48px; font-size:14px;color:#FFF;height:28px; line-height:28px; text-align:center;border:1px solid #63acef; color:#63acef;margin-left:10px;border-radius:2px;}
.pro_name a:hover{text-decoration:none;color:#ff9766;border:1px solid #ff9766}
.pro_name a.hover{text-decoration:none;color:#ff5200;border:1px solid #ff5200}


.pro_name{font-size:24px;background: url(https://static.fangxiaoer.com/web/images/sy/dealer/view/ico_vip.png) no-repeat 0px 3px;margin-top: 10px;height: 60px;position: relative;}
.pro_name a{
    float: left;
    /* width: 40px; */
    font-size: 14px;
    color: #FFF;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border: 1px solid #eff3fd;
    color: #668ae9;
    margin-left: 0;
    border-radius: 2px;
    background: #eff3fd;
    margin-top: 10px;
}
.box_sun{float:left;}
.pro_name .type_sun{ width:60px; height:26px; display:block; float:left; font-size:14px;  line-height:26px;; text-align:center;

    color:#fff;border-radius:3px
}
.pro_name .wait_1{background-color:#ff6100;box-shadow: 1px 1.732px 5px 0px rgba( 254, 97, 16,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/Now.png)}
.pro_name .wait_2{background-color:#fe9c02;box-shadow: 1px 1.732px 5px 0px rgba( 254, 156, 2,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/wait.png)}
.pro_name .wait_3{background-color:#8099af;box-shadow: 1px 1.732px 5px 0px rgba( 128, 153, 175,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/sell_out.png)}.pro_name a:hover{text-decoration:none;color:#ff9766;border:1px solid #ff9766}
.pro_name a.hover{text-decoration:none;color:#ff5200;border:1px solid #ff5200}
.pro_name p{/* margin-left: 42px; */color: #333;float: none;margin-right: 30px;float:  left;}
.pro_name img{position:absolute;right:0;top: -30px;}
.house_move_act{}
.house_move_act ul{overflow:  hidden;height: 50px;}
.nav_house{line-height: 50px;height: 50px;text-align:center;margin-bottom: 20px;background:  #f5f5f5;}
.nav_house li{float:left;margin: 0 2px;color:#ff6600;font-size:14px;font-weight:bold;line-height: 46px;}
.nav_house li.hover{color: #ff5200;}
.nav_house li.hover a{color: #ff5200;border-bottom:  2px solid #ff5200;}
.nav_house li a{
    display:  inline-block;
    padding: 0 25px;
    font-size: 16px;
    height: 48px;
}
/*切换*/
.house_move_act{}
.house_move_act ul{overflow:  hidden;height: 50px;}
.nav_house{line-height: 50px;height: 50px;text-align:center;margin-bottom:20px;background:  #f5f5f5;}
.nav_house li{float:left;margin: 0 !important;color:#ff6600;font-size:14px;font-weight:bold;line-height: 46px;}
.nav_house li.hover{color: #ff5200;text-decoration:  none;}
.nav_house li.hover a{color: #ff5200;border-bottom:  2px solid #ff5200;text-decoration:none;}
.nav_house li a{display:  inline-block;padding: 0 25px;font-size: 16px;height: 48px;color: #333;}
.nav_house li a:hover{text-decoration:none}
.nav_house a.ewm{float:right;position:relative;cursor:pointer;margin-right:25px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_ewm.gif) no-repeat left;padding-left:25px}
.nav_house span{display:none;position:absolute;z-index:1000;border:1px solid #cccdd4;/* padding:15px; */background:#fff;right:-25px;width: 200px;height: 120px;}
.nav_house a.ewm:hover span{display:block;text-decoration:  none;}
.nav_house a.ewm i{color:#888;float: left;text-align: left;line-height: 31px;margin-top: 11px;}
.nav_house a.ewm img{width: 120px;margin-bottom: -14px;float: left;}
.nav_house .soucang{float:right;margin-right:20px;cursor: pointer;}
.nav_house .soucang i{float:left;width:18px;height:17px;background: url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang.gif) no-repeat !important;margin: 19px 7px 0 0 !important;}
.nav_house .soucang i.hover{background: url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang2.gif) no-repeat !important;margin: 19px 7px 0 0 !important;}
#qrcode{float: left;height: 60px;margin:  0;width:  60px;margin-right:  10px;}

/*优惠*/
.privilege{height:170px; background: url(https://static.fangxiaoer.com/web/images/sy/house/view/privilege.jpg) no-repeat;margin-bottom:20px; position:relative;}
.privilege_l{width:270px;text-align:center;color:#fff; font-size:36px; font-weight:bold; line-height:170px; float:left}
/*.privilege .time{height:39px; line-height:39px;margin:8px 0;font-size:30px; color:#FFF; font-weight:normal;margin-top:20px;}
.privilege .time em{width:39px;height:39px;margin:0 2px 0 4px; background:url(https://static.fangxiaoer.com/web/images/sy/house/view/privilege_time.png);float:left; text-align:center}
.privilege .time p{float:left;}
.privilege .time i{float:left;margin:0 4px 0 2px}*/
.privilege_btn{width:122px;height:38px;margin:0 auto;}
.privilege_c{width:680px;line-height:36px; color:#f7dd83;font-size:34px; float:left; font-weight:bold;padding-top:35px;}
.privilege_r{width: 260px;float: left;height: 140px;color: #FFF;padding-top: 40px;line-height: 30px;font-size: 14px;}

/*主样式*/
br{clear:both;line-height:0;}
.leftImg{
    overflow: hidden;
    float: left;
    width: 914px;
    margin-bottom: 20px;
}
.imgPage{
    /* float: none; */
    overflow: hidden;
    margin-top: 16px;
    margin: 20px auto 0 auto;
    width: 400px;
    text-align:  center;
}
.imgPage div{
    float: left;
    border: 1px solid #eee;
    padding: 4px 6px;
    cursor: pointer;
    margin: 0 2px;
}
.imgPage ul{
    float: left;
}
.imgPage ul .hover{
    background: #ff5200;
    color: #fff;
    border: 1px solid #ff5200;
}
.imgPage ul li{
    float: left;
    border: 1px solid #eee;
    padding: 3px 9px;
    margin: 0 2px;
    cursor: pointer;
}
.position_pbl{margin:20px auto 0 auto;width: 916px;overflow:hidden;max-height: 855px;}
.position_pbl ul{
    /* border-top: 1px solid #eee; */
    /* border-left: 1px solid #eee; */
    overflow: hidden;
    width: 913px;
}
.position_pbl ul li{
    float: left;
    width: 280px;
    overflow: hidden;
    /* padding: 10px; */
    border: 1px solid #eee;
    border-top: 0;
    /* border-left: 0; */
    cursor: pointer;
    margin-right: 19px;
    margin-bottom: 23px;
    position:  relative;
}
.position_pbl ul li .wait_1{position;absolute;position:  absolute;left:0;top:0;background-color:#ff5200;color: #fff;display:  inline-block;line-height: 20px;padding-left: 7px;padding-right: 7px;}
.position_pbl ul li .wait_2{position;absolute;position:  absolute;left:0;top:0;background-color:#ffa532;color: #fff;display:  inline-block;line-height: 20px;padding-left: 7px;padding-right: 7px;}
.position_pbl ul li .wait_3{position;absolute;position:  absolute;left:0;top:0;background-color:#ecf0f3;color: #839aaa;display:  inline-block;line-height: 20px;padding-left: 7px;padding-right: 7px;}
.position_pbl ul li div{
    /* margin-bottom: 8px; */
    height: 200px;
    overflow: hidden;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
    text-align: center;
}
.position_pbl ul li img{
    /* width: 280px; */
    height: 200px;
}
.position_pbl ul li p{
    text-align: center;
    text-overflow: ellipsis;
    display: block;
    overflow: hidden;
    height: 60px;
    white-space: nowrap;
    line-height: 60px;
}
.main{width: 1299px;margin: auto;}
.main ul{*overflow:hidden;_height:1%;}
.main ul li{border: 1px solid #CCC;border-bottom: 2px solid #CCC;border-top: 1px solid #DBDBDB;float: left;display: block;list-style: none;margin: 6px 0;background: #FFF;width: 360px;}
.main ul li.hover{border:1px solid #fa3241;border-bottom:2px solid #fa3241;}
.main ul li img{width: 342px;display: block;}
.main ul li img.hover{filter:Alpha(opacity=90);-moz-opacity:.9;opacity:0.9;}
.main ul li p{text-align:center;font-size:14px;line-height:24px;}
.img_block{position:relative;padding:10px;}
.img_block img{width:100%}
.zoom,.ilike{line-height:25px;text-align:center;top:14px;display:block;text-decoration:none;}
.zoom{left:14px;width:42px;background-position:0 -71px;}
.zoom:hover{background-position:0 -99px;}
.ilike{right:14px;width:57px;background-position:0 -140px;}
.ilike:hover{background-position:0 -167px;}
.loading{text-align:center;padding:30px 0;}

.search{/* border:1px solid #e6e6e6; *//* background: #f9f8f8; *//* width: 226px; */margin:0 auto;line-height:38px;position: relative;/* height:38px; */margin-left: 24px;margin-top: 20px;float: left;padding-bottom: 10px;}
.search a{/* float:left; */padding: 0 27px;line-height: 46px;margin: 9px 0px 0 -4px;text-decoration:none;display: block;position: relative;}
.search a.hover{background:#ff6600;color:#fff;box-shadow: 3px 3px 3px #ccc;}
.search p{float:right;margin:6px 10px 0 0;position: absolute;top: -40px;left: -120px;cursor: pointer;line-height: 26px;}
.search a span{
    display: inline-block;
    float: right;
}
.search a img{
    position: absolute;
    width: 5px;
    height: 13px;
    top: 16px;
    left: 0;
    display: none;
}
.search a.hover img{
    display: block;
}
.search p img{
    display: inline-block;
    float: left;
    margin-right: 12px;
}

/* 新房房源相册右侧搜索 */
.photoSearch{border:1px solid #e6e6e6;background: #f9f8f8;width: 250px;margin: 0px auto;line-height:38px;position: relative;/* height:38px; */margin-left: 24px;margin-top: 20px;float: left;/* padding-bottom: 10px; */}
.photoSearch  a{/* float:left; */padding: 0 27px;line-height: 50px;margin: 2px 0px 0 0px;text-decoration:none;display: block;position: relative;border-bottom: 1px solid #ebebeb;}
.photoSearch  a.hover{background:#ff6600;color:#fff;box-shadow: 3px 3px 3px rgba(255,82,0,0.4);margin: -1px -5px 0px -5px;line-height: 54px;border-bottom: 0px;}
.photoSearch  p{float:right;margin:6px 10px 0 0;position: absolute;top: 18px;left: -153px;cursor: pointer;line-height: 40px;background-color: rgba(0,0,0,0.5);color: #fff;padding-right: 17px;}
.photoSearch  p img{ padding-right: 10px; padding-left: 19px; padding-top: 10px;}
.photoSearch  a span{
    display: inline-block;
    float: right;
}
.photoSearch  a:last-of-type{border-bottom:0}
.photoSearch  a img{
    position: absolute;
    width: 5px;
    height: 13px;
    top: 16px;
    left: 0;
    display: none;
}
.photoSearch a.hover img{
    display: none;
}
.photoSearch .search p img{
    display: inline-block;
    float: left;
    margin-right: 12px;
}

.info_btn input{width:221px;float:left; border-radius:3px;font-size:20px;height:48px;}
.btn_1{color:#ff6600;margin-right:36px; border:1px solid #ff6600; text-align:center; width:221px;float:left; border-radius:3px;font-size:20px;height:44px; line-height:44px;}
a.btn_2{margin-right:15px; border:1px solid #27b779; float:left;width:221px; border-radius:3px;color:#27b779; font-size:20px;}
a.btn_2:hover{color:#249E6A; text-decoration:none;border:1px solid #249E6A}
.btn_1:hover{color:red;border:1px solid red;}

/*顶部弹出*/
.mk{position:fixed;left: 50%;top: 50%;margin-top:-230px;margin-left:-345px;z-index:22222;display: none;background: #FFF}
.mk .main{width:690px;height:390px;position:relative;padding-top:46px;}
.mk .main a.btn_tc:hover{color:#fff;}
.tbForm{width:330px;margin:25px auto 0; border-top:1px solid #CCC;}
.main .tbForm p{  line-height:40px;font-size:14px;color:#000;}
.main .tbForm li{width: 314px;border: none;height:44px;line-height:49px;border-bottom:1px solid #CCC;border-left:1px solid #CCC;border-right:1px solid #CCC;padding:3px 0 0 15px;margin: 0;}
.main .tbForm label{line-height:40px;float: left;margin-left: 15px;}
.main .tbForm input.inputStyle{  z-index:1; background:none;outline:none; border:none; line-height:40px; padding:0 ;height:40px; width:236px;font-size:14px;color:#999;}

.tbForm p.yz{margin-top:4px;padding:5px 2px;border: 1px solid #F03;line-height:20px;float:right}
.tbForm p.yz a{color: #F03;}
.tbForm p.mima{text-align:center;}
.tbForm p.mima a{color:#fe5200;font-size:14px;}
.tbForm p.mima a:hover{cursor:pointer;text-decoration: none;}

.youhuixieyi{ margin-bottom:15px;width:330px;margin:0px auto;}

span.error{position: absolute;color:#F50C0C;font-size:14px;}
span.ename{top: 121px;left: 520px;}
span.emobile{top: 170px;left: 520px;}
span.eyz{top: 220px;left: 520px;}
span.epassword{top: 264px;left: 520px;}
.cha{position: absolute;top: 0px;right: -52px;}
.cha:hover{cursor:pointer;}
.tcbg{position: absolute;top: 0;left: 0;
    width: 100%;
    z-index: 1100;
    display: none;filter:alpha(opacity=70) !important;
}
.libao .yhqsygz{margin-top: -64px !important;}
.imgList {
    display:none;
    overflow: hidden;
}
.highDefinition {
    margin-top: 22px;
    display: none;
}
.highDefinition .highImg{
    width: 900px;
    height: 600px;
    overflow: hidden;
    position: relative;
}
.highDefinition .highImg ul{
    overflow: hidden;
    width: 90000px;
}
.highDefinition .highImg ul li{
    float: left;
    position: relative;
    height: 600px;
    width:900px;
}
.highDefinition .highImg ul li p{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 98%;
    line-height: 46px;
    color: #fff;
    font-size: 14px;
    text-align: center;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
    padding-left: 2% !important;
}
.highDefinition .highImg ul li img{
    width: 900px;
    display: block;
    margin:  0 auto;
}
.highDefinition .highImg ul li  span{
    position: absolute;
    top: 0;
    right: 0;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
    color: #fff;
    padding: 4px 12px;
}
.highDefinition .highImg .highImgPrev{
    background: url(https://static.fangxiaoer.com/web/images/Villa/big_left.png)no-repeat top left;
    position: absolute;
    top: 50%;
    left: 0px;
    width: 54px;
    height: 114px;
    margin-top: -57px;
    cursor: pointer;
}



.highDefinition .highImg .firstImg{
    position: absolute;
    top: 47%;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    background: url("https://static.fangxiaoer.com/web/images/ico/sign/b50.png");
    display: none;
    left: 46%;
}
.highDefinition .highImg .firstImg span{
    display:block;
    width: 120px;
    height: 16px;
}
.highDefinition .highImg .highImgNext{
    background: url(https://static.fangxiaoer.com/web/images/Villa/big_right.png)no-repeat top left;
    background-position: 0px 0;
    position: absolute;
    top: 50%;
    right: 0px;
    width: 50px;
    height: 114px;
    margin-top: -57px;
    cursor: pointer;
}
.highDefinition .highList {
    width: 900px;
    padding-top: 12px;
    height: 100px;
    position: relative;
    /* background: #eee; */
}
.highDefinition .highListShow {
    width: 806px;
    overflow: hidden;
    margin: 0 auto;
}
.highDefinition .highListShow ul{
    width: 900000px;
    overflow: hidden;
}
.highDefinition .highListShow ul li{
    float: left;
    width: 150px;
    margin-right: 14px;
    height: 100px;
    overflow: hidden;
    position: relative;
}
.highDefinition .highListShow ul li>div{ height: 100%;}
.highDefinition .highListShow ul li img{
    width: 100%;
    height: 100%;
}
.highDefinition .highListPrev{
    background: url(https://static.fangxiaoer.com/web/images/Villa/small_left.png)no-repeat top left;
    position: absolute;
    top: 12px;
    left: 0;
    width: 30px;
    height: 100px;
    cursor: pointer;
}
.highDefinition .highListNext{
    background: url(https://static.fangxiaoer.com/web/images/Villa/small_right.png)no-repeat top left;
    top: 12px;
    right: 0;
    position: absolute;
    width: 30px;
    height: 100px;
    cursor: pointer;
}





.photo_wei{background-color: #fff;margin-top: 20px;}
.leftImg{margin-left: 15px;width: 900px;}
.photoSearch{margin-left: 30px}
.right_wei{float: right;margin-right: 0px;}
.right_wei .photoSearch{float: none; margin-left:0}
.housesRight{margin-left: 10px !important;}
.right_wei .housesRight{margin-left: 0 !important;}
.photoSearch{background-color: #fff}

.right_wei .photoSearch p{
    position: absolute;
    top: 18px;
    left: -140px;
}


/*直播看房相关样式*/
.swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: #fff;

    /* Center slide text vertically */
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -webkit-align-items: center;
    align-items: center;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.swiper {
    width: 100%;
    height: 300px;
    margin-left: auto;
    margin-right: auto;
}

.swiper-slide {
    background-size: cover;
    background-position: center;
}

.mySwiper2 {
    height: 600px;
    width: 899px;
}

.mySwiper {
    height: 100px;
    box-sizing: border-box;
    padding: 0 47px;
    margin-top: 12px;
}

.mySwiper .swiper-slide {
    width: 25%;
    height: 100px;
    opacity: 0.4;
}

.mySwiper .swiper-slide-thumb-active {
    opacity: 1;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}
#zbkfBtn{
    padding: 0 27px;
    line-height: 50px;
    margin: 2px 0px 0 0px;
    text-decoration: none;
    display: block;
    position: relative;
    border-bottom: 1px solid #ebebeb;
    cursor: pointer;
}
#zbkfBtn:hover{color:#ff5200}
#zbkfBtn span {
    display: inline-block;
    float: right;
}
#zbkfBtn.hover {
    background: #ff6600;
    color: #fff;
    box-shadow: 3px 3px 3px rgb(255 82 0 / 40%);
    margin: -1px -5px 0px -5px;
    line-height: 54px;
    border-bottom: 0px;
}
.liveKuang{
    position: relative;
    width: 899px;
}

.liveKuang .liveNext{
    background: url(https://static.fangxiaoer.com/web/images/Villa/small_left.png)no-repeat top left;
    position: absolute;
;;;;;;;;;;;bottom: 0px;;;;;;;;;;;;
    left: 0;
    width: 30px;
    height: 100px;
    cursor: pointer;
;right: 0;;
;;;;z-index: 9999;;;;;
;;top: auto;;;
}

.liveKuang .livePrev{
    background: url(https://static.fangxiaoer.com/web/images/Villa/small_right.png)no-repeat top left;
;;bottom: 0;;;
    right: 0;
    position: absolute;
    width: 30px;
    height: 100px;
    cursor: pointer;
;;;;;left: auto;;;;;;
;;top: auto;;;
}
.mySwiper2 p{
    position: absolute;
    left: 0;
    bottom: 0;
    height: 46px;
    line-height: 46px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #FFFFFF;
    padding-left: 3%;
    width: 97%;
    text-align: left;
    background: rgb(0 0 0 / 50%);
}
.liveBtn{
    width: 80px;
    height: 80px;
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    background-image: url(https://static.fangxiaoer.com/images/coin/liveBtn1.gif);
    background-size: 60% 60%;
    border: 1px solid #ccc;
    background-color: rgb(0 0 0 / 50%);
    border-radius: 40px;
    background-position: center;
    background-repeat: no-repeat;
}
.smallIcon{
    width: 24px;
    height: 24px;
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -12px;
    margin-top: -12px;
    background-image: url(https://static.fangxiaoer.com/images/coin/liveBtn1.gif);
    background-size: 60% 60%;
    border: 1px solid #ccc;
    background-color: rgb(0 0 0 / 50%);
    border-radius: 12px;
    background-position: center;
    background-repeat: no-repeat;
}