@charset "UTF-8";
* {
  padding: 0;
  margin: 0; }

ul, li {
  list-style: none; }

.zane-calendar {
  box-sizing: initial;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 14px;
  width: 280px;
  border: solid 1px #eee;
  border-left: none;
  background: #fff;
  overflow: hidden;
  -moz-user-select: none;
  -o-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none; }
  .zane-calendar .main-color {
    color: #46d7a2; }
  .zane-calendar .zane-date-left {
    float: left; }
  .zane-calendar .zane-calendar-one {
    width: 280px; }
    .zane-calendar .zane-calendar-one .zane-date-top {
      height: 40px;
      line-height: 40px;
      position: relative;
      text-align: center;
      border-bottom: solid 1px #eee;
      border-left: solid 1px #eee;
      font-size: 16px; }
      .zane-calendar .zane-calendar-one .zane-date-top .zane-date-icom {
        width: 40px;
        height: 40px;
        position: absolute; }
      .zane-calendar .zane-calendar-one .zane-date-top .zane-icon-left {
        left: 0;
        top: 0;
        background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAA6ElEQVRYR9XXXQ7CIAwHcDiBeAN5oM8eSW+mJ9Fz8FIPQlJDshjnPigbLXGvI/x/oaHdrOn82M755n8BiHjKp+e9f+05xU0nEGM8W2sfOTiEcFQFfIU7IroDwEUN0Do8w9klkAhnA6TCWQDJ8CJAOnwVoBG+CNAKnwVohk8A2uEjACK6lBIaY5p0OG53/DSiAZAHy4GIbgBw5W6yZ92oEw4leGoiJq1YGzE7CzQRi8NIC7E6DTUQxXEsjSgC8hWTRLAAkgg2QApRBZBAVAN+EPmz3DVrxTUbdf0xqYGW1m4qQWnTmvfdAW+Xz8YhHJFj1gAAAABJRU5ErkJggg==") no-repeat center center;
        background-size: 55%; }
      .zane-calendar .zane-calendar-one .zane-date-top .zane-icon-right {
        position: absolute;
        top: 0;
        right: 0;
        background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAA2klEQVRYR+XXwQ3CMAxAUWeY+MwIjFI2gElgA2ASVuBsDxMUCaTSQ2s7NlFLj1Wl/+RISZqg85M692EbACLaIeLTMs3mCTDzEQDOpZQbIh60CA/AHgAeNWxBNANqmIiGlNLVgnABtCDcAFaEK8CCcAdoESEADSIMIEWEAiSIcMAS4ieAKQIATjnnS33/H4DJVn1HxOFzaIVPYC4evgRL8VCAJB4GkMZDAJq4O0AbdwVY4m4Aa9wFwMzjS+nXJiO5ITdvRKNruTruMoH3QdPvx0Qy5rlvmpdg9YAX88PCIUoA0kEAAAAASUVORK5CYII=") no-repeat center center;
        background-size: 55%; }
      .zane-calendar .zane-calendar-one .zane-date-top .zane-icon-center span:hover {
        color: #46d7a2;
        cursor: pointer; }
      .zane-calendar .zane-calendar-one .zane-date-top .zane-icon-center span:nth-child(1) {
        margin-right: 8px; }
    .zane-calendar .zane-calendar-one .zane-date-main {
      overflow: hidden;
      height: 220px;
      border-left: solid 1px #eee; }
      .zane-calendar .zane-calendar-one .zane-date-main .week-day table.day {
        width: 100%; }
      .zane-calendar .zane-calendar-one .zane-date-main .week-day th {
        font-size: 13px; }
      .zane-calendar .zane-calendar-one .zane-date-main .week-day th, .zane-calendar .zane-calendar-one .zane-date-main .week-day td {
        text-align: center; }
      .zane-calendar .zane-calendar-one .zane-date-main .week-day th.light, .zane-calendar .zane-calendar-one .zane-date-main .week-day td.light {
        color: #aaa; }
      .zane-calendar .zane-calendar-one .zane-date-main .week-day td {
        font-size: 12px;
        cursor: pointer; }
        .zane-calendar .zane-calendar-one .zane-date-main .week-day td.calendar-disabled {
          cursor: not-allowed;
          color: #ddd; }
        .zane-calendar .zane-calendar-one .zane-date-main .week-day td span {
          box-sizing: initial;
          display: inline-block;
          width: 10px;
          height: 10px;
          padding: 9px;
          padding-left: 8px;
          padding-right: 10px;
          text-align: center;
          line-height: 10px;
          border-radius: 100%; }
      .zane-calendar .zane-calendar-one .zane-date-main .week-day td:hover span {
        background: #f4f4f4; }
      .zane-calendar .zane-calendar-one .zane-date-main .week-day td.active {
        color: #fff; }
        .zane-calendar .zane-calendar-one .zane-date-main .week-day td.active span {
          background: #46d7a2; }
      .zane-calendar .zane-calendar-one .zane-date-main .week-day .border-day td span {
        width: auto;
        border-radius: 15px; }
      .zane-calendar .zane-calendar-one .zane-date-main .main-check-month .week-day td {
        width: 33.333%; }
        .zane-calendar .zane-calendar-one .zane-date-main .main-check-month .week-day td.calendar-disabled {
          cursor: not-allowed;
          color: #ccc; }
      .zane-calendar .zane-calendar-one .zane-date-main .main-check-year .week-day td {
        width: 33.333%; }
        .zane-calendar .zane-calendar-one .zane-date-main .main-check-year .week-day td.calendar-disabled {
          cursor: not-allowed;
          color: #ccc; }
      .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day {
        padding: 5px; }
        .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .nav {
          overflow: hidden;
          height: 25px;
          line-height: 25px; }
          .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .nav li {
            width: 33.333%;
            float: left;
            text-align: center; }
        .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .select-time {
          overflow: hidden;
          text-align: center;
          height: 176px;
          /*滚动条样式*/ }
          .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .select-time ul {
            height: 99%;
            display: inline-block;
            overflow-y: scroll;
            width: 28%;
            margin: 0 2%;
            float: left;
            border: solid 1px #eee; }
            .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .select-time ul li {
              height: 25px;
              line-height: 25px;
              cursor: pointer; }
              .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .select-time ul li:hover {
                background: #f4f4f4; }
              .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .select-time ul li.active {
                background: #46d7a2;
                color: #fff; }
          .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .select-time ul::-webkit-scrollbar {
            width: 4px;
            height: 4px; }
          .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .select-time ul::-webkit-scrollbar-thumb {
            border-radius: 5px;
            -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: rgba(0, 0, 0, 0.6); }
          .zane-calendar .zane-calendar-one .zane-date-main .main-check-time .week-day .select-time ul::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            border-radius: 0;
            background: rgba(0, 0, 0, 0.1); }
    .zane-calendar .zane-calendar-one .zane-date-bottom {
      border-top: solid 1px #eee;
      border-left: solid 1px #eee;
      overflow: hidden;
      height: 40px;
      padding: 0 8px;
      font-size: 12px; }
      .zane-calendar .zane-calendar-one .zane-date-bottom .zane-date-right {
        float: right; }
        .zane-calendar .zane-calendar-one .zane-date-bottom .zane-date-right .button.no-right-line {
          border-right: none; }
      .zane-calendar .zane-calendar-one .zane-date-bottom .button {
        margin-top: 6px;
        cursor: pointer;
        padding: 6px 8px;
        float: left;
        background: #fff;
        color: #999; }
        .zane-calendar .zane-calendar-one .zane-date-bottom .button:hover {
          color: #46d7a2; }
