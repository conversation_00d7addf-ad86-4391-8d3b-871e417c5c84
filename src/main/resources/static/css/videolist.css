@charset "utf-8";
body {
	line-height: normal
}

.clearfix {
	clear: both
}

.position .content_box {
	width: 1170px;
	margin: 0 auto
}

.position .content_box h1 {
	font-size: 12px;
	color: #999999;
	font-weight: normal;
	line-height: 50px
}

.position .content_box h1 a {
	color: #999999
}

.nav_box .content_box {
	width: 1170px;
	margin: 0 auto;
	background-color: #f3f3f3
}

.nav_box .content_box ul li {
	float: left
}

.nav_box .content_box ul li a {
	display: block;
	font-size: 16px;
	line-height: 50px;
	padding-left: 37px;
	padding-right: 37px;
	background-image: url("https://static.fangxiaoer.com/web/images/video/line.png");
	background-repeat: no-repeat;
	background-position: right center;
	text-decoration: none;
}

.nav_box .content_box ul li:nth-child(8) a {
	background: none;
}

.nav_box .content_box ul .color a {
	color: #ff5200
}

.nav_box .content_box ul .color {
	background-image: url("https://static.fangxiaoer.com/web/images/video/lin02.png");
	background-repeat: no-repeat;
	background-position: center bottom
}

.list_box .content_box .content {
	width: 1170px;
	margin: 0 auto;
	padding-top: 20px
}

.list_box .content_box .content ul li {
	float: left;
	width: 282px
}

.list_box .content_box .content ul li .photo {
	position: relative
}

.list_box .content_box .content ul li .photo .pic img {
	width: 282px;
	height: 158px
}

.list_box .content_box .content ul li .photo .time {
	position: absolute;
	right: 0px;
	bottom: 0px;
	background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: -o-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, .5));
	width: 100%;
	padding-left: 0px;
	padding-right: 0px;
	border-radius: 0px
}

.list_box .content_box .content ul li .photo .time h1 {
	font-size: 12px;
    color: #fff;
    font-weight: normal;
    line-height: 33px;
    padding-right: 6px;
    /* background-image: url(https://static.fangxiaoer.com/web/images/video/time03.png); */
    background-position: left center;
    background-repeat: no-repeat;
    text-align: right;
}

.list_box .content_box .content ul li .name {
	padding-top: 20px;
	padding-bottom: 11px
}

.list_box .content_box .content ul li .name h1 {
	font-size: 14px;
	height: 14px;
	line-height: 14px;
	font-weight: normal;
	text-align: left;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	    font-weight: bold;
}



.list_box .content_box .content ul li {
	margin-right: 14px;
	    margin-bottom: 20px;
}

.list_box .content_box .content ul li:nth-child(4n) {
	margin-right: 0px
}

.list_box .content_box .content ul li .name a {
	text-decoration: none
}

.list_box .content_box .content ul li a {
	text-decoration: none !important;
}

.content ul li a:hover {
	color: #ff5200
}

.minPage {
	width: 100%
}

.list_box .content_box .content ul li .photo .pic {
	overflow: hidden;
}

.list_box .content_box .content ul li .photo .pic img {
	transition: all 0.6s;
}

.list_box .content_box .content ul li .photo .pic:hover img {
	transform: scale(1.1);
}
.info{
	height: 30px;
	line-height: 30px;
}
.info .touxiang {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    float: left;
    background: url(https://static.fangxiaoer.com/m/static/images/video/noagent.png) no-repeat center;
    background-size: 100% 100%;
    margin-right: 9px;
    
}
.touxiang img{
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.video_r{
	float: right;
	    margin-right: 8px;
	        margin-top: 4px;
	
}
.video_ll {
    width: 16px;
    height: 12px;
    background-size: 100%;
    font-style: normal;
    float: left;
    margin-right: 4px;
        margin-top: 5px;
}
.mr14{
	    margin-right: 14px;
	
}

.video_r span {
    font-size: 14px;
    color: #535353;
}
.video_dz {
  	width: 17px;
	height: 13px;
    background-size: 100%;
    margin-left: 4px;
    font-style: normal;
    float: left;
    margin-right: 4px;
        margin-top: 4px;
}
.video_r img {
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    display: block;
}
.span_div {
    display: block;
    float: left;
    font-size: 14px;
    line-height: 22px;
}