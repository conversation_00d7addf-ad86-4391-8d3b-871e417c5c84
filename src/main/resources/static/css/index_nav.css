.clearfix{ clear: both}
.zhaofang .index_content .Admission ul li img{ display: block}
.headTOP h1{ float: left; padding-top: 10px;}
.head_nav ul li{float: left; margin-right: 26px;}
.head_nav ul li a{display: block;font-size: 16px !important;}
.head_nav ul li a:first-child{line-height: 60px;}
.head_nav ul li p a span{font-size: 14px !important;}
.head_right_login .register{margin-left: 10px;color: #333;}
.city{ line-height: 60px; margin-top: 0px; background-image:url("https://static.fangxiaoer.com/global/imgs/ico/address.png");background-position: 68px center !important;background-size: 10px;font-size: 16px !important;}
.headTOP{ position:fixed; left: 0; top: -0px; width: 100%;z-index:222; background-color:#fff}
.headTOP h1 img{width:108px;height:26px;padding-top:8px}
.head_nav{ float: left}
.headTOP .login a{display: block;line-height: 34px;color: #333;}
.headTOP .login{float: left;margin-top: 13px;margin-left: 5px;}
.headTOP .login span{padding-right:5px}
.headTOP .login p{width:  108px;background-color: #fff;position:  absolute;left: -39px;top: 52px;text-align:  center;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.19); opacity: 0;transition: all 0.6s;z-index:0;visibility:hidden;}
.headTOP .login p i{    position: absolute;
    width: 10px;
    height: 8px;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);
    top: -8px;
    left: 50%;
    margin-left: -5px;
    }
.headTOP{box-shadow:0 0 12px rgba(0, 0, 0, 0.26);}
    .headTOP .login a:hover{color:#f24d00;}
.headTOP .login:hover p{opacity: 1;position: absolute; left: -39px;top:42px;visibility:visible;}
.headTOP .head_right_login{float: right;padding-top:0px;line-height: 60px;position:  relative;}
.headTOP .head_right_login em{display:inline-block;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;float:  left;    margin-right: 7px;}
.headTOP .head_right_login b{margin-top: 27px;display: initial;}
.headTOP .head_right_login .recode{line-height: 60px;float: left;background: url(https://static.fangxiaoer.com/global/imgs/index/recodePhone02.png)no-repeat 0px 21px;padding-left: 20px;position:relative;color: #333;}
.headTOP .head_right_login .recode span{color: #b8b8b8 !important;}
.headTOP .head_right_login .shuxian{color: #b8b8b8 !important;}
.headTOP .head_right_login .recode div{position:absolute; left:-15px;top:42px; width:133px; height:147px;background: url(https://static.fangxiaoer.com/global/imgs/index/recode.png)no-repeat; opacity: 0;transition: all 0.6s;z-index:0;visibility:hidden;}
.headTOP .head_right_login .recode:hover div{opacity: 1;position: absolute; left: -15px;top:30px;visibility:visible;}
.headTOP .head_right_login .recode div img{ margin:0 auto;display:block; padding-top:35px}
.headTOP .login a:first-child{ margin-right: 9px}
.w1170{ margin-top: 0px}
.head_nav{ margin-top: 0px}
.head_nav ul li{ position: relative}
.head_nav ul li:nth-child(10) p{position: absolute;left: -19px;top:70px;}
.head_nav ul li:nth-child(10):hover p{opacity: 1;position: absolute; left: -19px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li p{width: 103px;position: absolute;left: -30px;top:70px;text-align: center;background-color: #fff;z-index: 0;box-shadow: 1px 1.732px 4px 2px rgba( 127, 126, 126,0.3 );padding-bottom: 10px;opacity: 0;transition: all 0.6s;visibility:hidden;}
.head_nav ul li:nth-child(8) p{position: absolute;left: -37px;top:70px;}
.head_nav ul li:nth-child(8):hover p{opacity: 1;position: absolute; left: -37px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li:nth-child(9) p{position: absolute;left: -38px;top:70px;}
.head_nav ul li:nth-child(9):hover p{opacity: 1;position: absolute; left: -38px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li:nth-child(7) p{position: absolute;left: -36px;top:70px;}
.head_nav ul li:nth-child(7):hover p{opacity: 1;position: absolute; left: -36px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li:nth-child(6) p{position: absolute;left: -29px;top:70px;}
.head_nav ul li:nth-child(6):hover p{opacity: 1;position: absolute; left: -29px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li:nth-child(4) p{position: absolute;left: -35px;top:70px;}
.head_nav ul li:nth-child(4):hover p{opacity: 1;position: absolute; left: -35px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li:nth-child(5) p{position: absolute;left: -35px;top:70px;}
.head_nav ul li:nth-child(5):hover p{opacity: 1;position: absolute; left: -35px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li:nth-child(3) p{position: absolute;left: -29px;top:70px;}
.head_nav ul li:nth-child(3):hover p{opacity: 1;position: absolute; left: -29px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li:nth-child(2) p{position: absolute;left: -36px;top:70px;}
.head_nav ul li:nth-child(2):hover p{opacity: 1;position: absolute; left: -36px;top:60px;z-index: 22;visibility: visible;}
.head_nav ul li p i{position: absolute;
    width: 10px;
    height: 8px;
    background: url(https://static.fangxiaoer.com/web/images/ico/head/ico_head_jian.png);
    top: -8px;
    left: 50%;
    margin-left: -5px;}

.head_nav ul li p a{font-size: 16px;margin-top: 10px;}

.navigation ul a:nth-child(2n) li{ margin-right: 0}
.navigation ul li{margin-right: 14px;}
.navigation .box h2{font-size: 20px;width: auto;}
.navigation{ background-color: #f5f5f5}
.hot_red{color:#f24d00}
.zhaofang .index_title .xegz{ float: right; width: auto}
.zhaofang .index_content{margin-top: 20px}
.zhaofang .index_content .type{ width: 218px; float: left}
.zhaofang .index_content .type ul li{ cursor: pointer; position: relative;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
background-position: 170px center;}
.zhaofang .index_content .type ul li:hover{background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow02.png");}
.zhaofang .index_content .type ul li i{ display: block; float: left; margin-left: 8px; margin-right: 12px}
.zhaofang .index_content .type ul li  .type_title{font-size: 14px; color: #666666; display: block;float: left; line-height: 44px}
.zhaofang .index_content .type ul li:nth-child(1) i{width: 16px; height: 14px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/new.png"); margin-top: 15px}
.zhaofang .index_content .type ul li:nth-child(2) i{width: 17px; height: 15px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/Second.png");margin-top: 15px}
.zhaofang .index_content .type ul li:nth-child(3) i{width: 16px; height: 14px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/lease.png");margin-top: 15px}
.zhaofang .index_content .type ul li:nth-child(4) i{width: 15px;height: 14px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/shops.png");margin-top: 15px;}
.zhaofang .index_content .type ul li:nth-child(5) i{width: 16px; height: 14px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/Building.png");margin-top: 15px}
.zhaofang .index_content .type ul li:hover{box-shadow: 0px 0px 7px 2px rgba( 142, 142, 142 ,0.3);}
.zhaofang .index_content .type ul li .layer{ width: 953px; height: 220px; position: absolute; left: 217px;; top:0 ;box-shadow: 0px 0px 7px 2px rgba( 142, 142, 142 ,0.3);background-color: #fff;display: none;z-index: 99}
.zhaofang .index_content .type ul li .patch{ width: 30px; height: 44px; position: absolute;right: -11px;top: 0px;z-index: 555; background-color: #fff; display: none}
.zhaofang .index_content .type ul li:hover .layer{ display: block}
.zhaofang .index_content .type ul li:hover .patch{ display: block}
.zhaofang .index_content .type ul li:nth-child(2) .layer{position: absolute; left: 217px;; top:-44px ;}
.zhaofang .index_content .type ul li:nth-child(3) .layer{position: absolute; left: 217px;; top:-88px ;}
.zhaofang .index_content .type ul li:nth-child(4) .layer{position: absolute; left: 217px;; top:-132px ;}
.zhaofang .index_content .type ul li:nth-child(5) .layer{position: absolute; left: 217px;; top:-176px ;}
.zhaofang .index_content .type ul li:hover .type_title{ color:#ff6100;}
.zhaofang .index_content .type ul li:nth-child(1):hover i{background-image: url("https://static.fangxiaoer.com/global/imgs/ico/new02.png");}
.zhaofang .index_content .type ul li:nth-child(2):hover i{background-image: url("https://static.fangxiaoer.com/global/imgs/ico/Second02.png");}
.zhaofang .index_content .type ul li:nth-child(3):hover i{background-image: url("https://static.fangxiaoer.com/global/imgs/ico/lease02.png");}
.zhaofang .index_content .type ul li:nth-child(4):hover i{background-image: url("https://static.fangxiaoer.com/global/imgs/ico/shops02.png");}
.zhaofang .index_content .type ul li:nth-child(5):hover i{background-image: url("https://static.fangxiaoer.com/global/imgs/ico/Building02.png");}
.zhaofang .index_content .type ul li .layer .region{padding-top: 22px }
.zhaofang .index_content .type ul li .layer .region h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .region a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .region a:nth-last-of-type(1){border: 0}

.zhaofang .index_content .type ul li .layer .Price{padding-top: 26px }
.zhaofang .index_content .type ul li .layer .Price h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
    background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .Price a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .Price a:nth-last-of-type(1){border: 0}

.zhaofang .index_content .type ul li .layer .Huxing{padding-top: 26px }
.zhaofang .index_content .type ul li .layer .Huxing h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
    background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .Huxing a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .Huxing a:last-child{border: 0}
.zhaofang .index_content .type ul li .layer .Huxing a:nth-last-of-type(1){border: 0}
.zhaofang .index_content .type ul li .layer .type_sun{padding-top: 26px }
.zhaofang .index_content .type ul li .layer .type_sun h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
    background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .type_sun a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .type_sun a:last-child{border: 0}
.zhaofang .index_content .type ul li .layer .type_sun a:nth-last-of-type(1){border: 0}
.zhaofang .index_content .type ul li .layer .characteristic{padding-top: 26px }
.zhaofang .index_content .type ul li .layer .characteristic h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
    background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .characteristic a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .characteristic a:last-child{border: 0}
.zhaofang .index_content .type ul li .layer .characteristic a:nth-last-of-type(1){border: 0}
.zhaofang .index_content .type ul li .layer .measure{padding-top: 26px }
.zhaofang .index_content .type ul li .layer .measure h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
    background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .measure a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .measure a:last-child{border: 0}
.zhaofang .index_content .type ul li .layer .measure a:nth-last-of-type(1){border: 0}
.zhaofang .index_content .type ul li .layer .Mode{padding-top: 26px }
.zhaofang .index_content .type ul li .layer .Mode h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
    background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .Mode a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .Mode a:last-child{border: 0}
.zhaofang .index_content .type ul li .layer .Mode a:nth-last-of-type(1){border: 0}
.zhaofang .index_content .type ul li .layer .supply{padding-top: 26px }
.zhaofang .index_content .type ul li .layer .supply h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
    background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .supply a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .supply a:last-child{border: 0}
.zhaofang .index_content .type ul li .layer .supply a:nth-last-of-type(1){border: 0}
.zhaofang .index_content .type ul li .layer .type_sun02{padding-top: 26px }
.zhaofang .index_content .type ul li .layer .type_sun02 h1{ display: block; float: left; margin-left: 18px; font-size: 14px; color: #333333; font-weight: bold;  padding-right: 13px;background-image: url("https://static.fangxiaoer.com/global/imgs/ico/right_Arrow.png"); background-repeat: no-repeat;
    background-position: right center; line-height: 14px}
.zhaofang .index_content .type ul li .layer .type_sun02 a{ display: inline-block; float: left;  padding-left: 8px;padding-right: 8px; border-right: #ebebeb solid 1px; line-height: 14px}
.zhaofang .index_content .type ul li .layer .type_sun02 a:last-child{border: 0}
.zhaofang .index_content .type ul li .layer .type_sun02 a:nth-last-of-type(1){border: 0}
.zhaofang .index_content .Housekeeper{ float: right; width: 270px; height: 210px;}
.zhaofang .index_content .Housekeeper img{ width: 100%; height: 100%;}
.zhaofang .index_content .Admission{ width: 659px; float: left}
.zhaofang .index_content .Admission ul li{ width: 210px; height: 160px; border: 1px solid #ebebeb; border-radius: 5px; float: left; margin-right: 11px;cursor: pointer}
.zhaofang .index_content .Admission ul li .name{ margin-top: 19px; position: relative;transition:All 0.4s ease-in-out;
    -webkit-transition:All 0.4s ease-in-out;
    -moz-transition:All 0.4s ease-in-out;
    -o-transition:All 0.4s ease-in-out;}
.zhaofang .index_content .Admission ul a:nth-child(3) li{margin-right: 0}
.zhaofang .index_content .Admission ul li .name img{width: 47px; height: 51px; margin: 0 auto}
.zhaofang .index_content .Admission ul li .name h1{ text-align:center; font-size: 18px; color: #333333; font-weight: normal; margin-top: 8px}
.zhaofang .index_content .Admission ul li .name h1 span{font-weight: bold}

.zhaofang .index_content .Admission ul li:hover .name{
    transform:translate(0,-10px);
    -webkit-transform:translate(0,-10px);
    -moz-transform:translate(0,-10px);
    -o-transform:translate(0,-10px);
    -ms-transform:translate(0,-10px);
}
.zhaofang .index_content .Admission ul li .text{margin-top: 3px;}
.zhaofang .index_content .Admission ul li .text h2{font-size: 14px;color: #999999;font-weight:  normal;}
.zhaofang .index_content .Admission ul li .text h3{font-size: 14px;color: #999999;font-weight: normal;margin-top: -6px;}
.zhaofang .index_content .Admission .phone #userPhoneNum{width: 446px;height: 40px;background-color: #f0f0f0;border: 0;display: block;float: left;border-radius: 3px;padding-left: 24px;font-size: 14px;}
.zhaofang .index_content .Admission .phone #userPhoneNum::-webkit-input-placeholder{
            color:#999999;
        }
        input::-moz-placeholder{   /* Mozilla Firefox 19+ */
            color:#999999;
        }
        input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
            color:#999999;
        }
        input:-ms-input-placeholder{  /* Internet Explorer 10-11 */ 
           color:#999999;
        }
.zhaofang .index_content .Admission .phone #submitPhoneNum{width: 170px;height: 40px;background-color: #ff6100;border: 0;font-size: 14px;color: #f5f5f5;display: block;float: right;border-radius: 3px;cursor: pointer;font-weight:  bold;}
.zhaofang .index_content .Admission .phone{ margin-top: 10px}
.zhaofang .index_content .Admission ul a:nth-child(1) .text{width: 98px;margin:0 auto;margin-top: 3px;padding-left: 13px;}
.zhaofang .index_content .Admission ul a:nth-child(2) h2{text-align:center}
.zhaofang .index_content .Admission ul a:nth-child(2) h3{text-align:center}
.zhaofang .index_content .Admission ul a:nth-child(3) .text{width: 84px;margin:0 auto;margin-top: 3px;padding-left: 13px;}
.Advertisement{ margin-top: 20px; margin-bottom: 20px; }

.bnzfTC_heibu{ width: 100%; height: 100%; background-color: rgba(0,0,0,0.6); position: fixed; left: 0; top: 0; z-index: 999}
.bnzfTC{ height: 100%; position:relative}
.bnzfTC_box{ width: 500px; height: 300px; position: absolute; left: 50%;top: 50%; margin-left: -250px; margin-top: -150px;background-color: #fff; position: relative; border-radius: 7px}
.bnzfTC_box .Verification{ font-size: 14px; color: #333333; width: 410px; margin: 0 auto}
.bnzfTCSendCode { font-size: 12px; color: #999999; width: 124px; height: 30px; border: 1px solid #ebebeb; display:inline-block; line-height: 30px;text-align: center;float: right; border-radius: 5px;}
.bnzfTC_box .Verification{ padding-top: 49px;}
.bnzfTC_box .Verification input{ width: 78px; font-size: 12px; border: 0}
#bnzfTCCode{width:402px;display: block;margin: 0 auto;margin-top: 10px;height: 42px;padding-left: 8px;border: 1px solid #ebebeb;border-radius: 5px;font-size: 14px;color: #999;}
#bnzfTCCode::-webkit-input-placeholder{
            color:#999999;
        }
        input::-moz-placeholder{   /* Mozilla Firefox 19+ */
            color:#999999;
        }
        input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
            color:#999999;
        }
        input:-ms-input-placeholder{  /* Internet Explorer 10-11 */ 
           color:#999999;
        }
.bnzfTC_btn{width: 412px; height: 42px; margin: 0 auto;background-color: #ff5200; border-radius: 5px; margin-top: 12px}
.bnzfTC_btn a{ line-height: 42px; display: block; text-align: center; font-size: 14px; color: #ffffff;}
.phone_sun{width: 412px;margin: 0 auto;margin-top: 6px;}
.kbTc_txt1{display: block;float: left; color: #ff5200}
.kbTc_txt2{display: block;float: right;color: #2a95fe;cursor:  pointer;}
.bnzfTC_close{position: absolute;top:20px;right: 16px;cursor:  pointer;}
.bnzfTC_form .Prompt{width:412px; margin:0 auto;height:30px}
.bnzfTC_form .Prompt span{font-size:12px; color:#e60012;line-height:30px}


.bnzfTCss_heibu{ width: 100%; height: 100%; background-color: rgba(0,0,0,0.6); left: 0; top: 0; z-index: 999;position: fixed; left: 0; top: 0}
 .bd{    background: url(https://static.fangxiaoer.com/web/images/ico/sign/select.png)no-repeat;
     background-size: 100% 100%;}
.bnzfTC_box .Agreement i{ display: block;width: 20px;
    height: 20px;
    border: 1px solid #ededed;
    display: block;
    cursor: pointer;
    float: left;
    margin-left: 43px; margin-right: 7px}
.bnzfTC_box .Agreement input{ display: none}
.bnzfTC_box .Agreement h1{ font-size: 14px; color: #333333;font-weight: normal;}
.bnzfTC_box .Agreement h1 a{color: #ff5200}
.bnzfTC_box .Agreement h1 a:hover{text-decoration:underline;}

.bnzfTCss{width: 420px;height: 224px; background-color: #fff;position: absolute; left: 50%; top: 50%; margin-left: -210px; margin-top: -112px; border-radius: 7px}
.bnzfTCss_close img{display:none;}
.bnzfTCss .yyDkImg img{ margin: 0 auto; display: block; padding-top: 49px;}
.bnzfTCss p{ font-size: 14px; color: #666666; text-align: center; margin-top: 40px}
.bnzfTCss #guide_result{font-size: 16px; color: #ff5200; margin-top: 14px;}


.zhaofang .index_content .type ul li:nth-child(2) .layer .Price{padding-top: 36px}
.zhaofang .index_content .type ul li:nth-child(2) .layer .measure{padding-top: 36px}
.zhaofang .index_content .type ul li:nth-child(2) .layer .Huxing{padding-top: 36px}

.zhaofang .index_content .type ul li:nth-child(3) .layer .Price{padding-top: 36px}
.zhaofang .index_content .type ul li:nth-child(3) .layer .Mode {padding-top: 36px}
.zhaofang .index_content .type ul li:nth-child(3) .layer .Huxing{padding-top: 36px}



.window_sun {
	position: fixed;
	top: 48%;
	left:50%;
	padding-left:40px;
	padding-right:40px;
	margin: 0 30%;
	background: url("https://static.fangxiaoer.com/global/imgs/ico/b60.png");
	color: #fff;
	font-size: 11pt;
	text-align: center;
	border-radius: 6px;
	padding: 10px;
	z-index: 1000000;
	 transform:translate(-50%,-50%)

}

.fxe_validateCode{
    width: 124px;
    border: 1px solid #ebebeb;
    display: inline-block;
    line-height: 30px;
    text-align: center;
    font-weight:normal;
    font-size:12px;
    margin-left:5px;
    border-radius:3px;
    cursor:  pointer;
    }