@charset "utf-8";
/* CSS Document */
@font-face {
	font-family: 'dinot-bold';   /*字体名称*//*/font/*/
	src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');       /*字体源文件*/
}
.crumbs{margin-top:39px;}
.w1210 .w{width:1170px}
.newHouseBasicInfo{
	/* overflow: hidden; */
}
.newHouseViewChunk{
	width:  1170px;
	margin: 0 auto;
	border:  1px solid #ededed;
	margin-bottom: 28px;

}
.Freefone {
	margin: 44px auto 20px auto;
	cursor: pointer;
}
.newHouseViewChunk .xqjj_con {
	border: none;
	padding-top: 30px;
}
.newHouseViewChunk .houseAnswer{
	padding: 0 33px;
}
#js202 p{
	padding-left: 26px;
}
.title{/* background:url(https://static.fangxiaoer.com/web/images/ico/sign/title_line.gif) repeat-x bottom; */height: 60px;/* margin: 17px auto 12px; */border-bottom:  1px solid #ededed;line-height:  60px;}
.title p{font-size:20px;color: #333;/* border-bottom:5px solid #ff6600; */float:left;padding: 0 30px;line-height: 60px;margin-right: 32px;}
.title a{/* float:right; */font-size: 12px;line-height: 60px;color: #666;margin-right: 30px;}
.con_left{float:left;width: 100%;}
.con_right{float:left;width: 395px;margin-left:25px;display:_inline;margin-top: 80px;display: none;}
.title .more:hover{color:#ff5200}
.place {color: #999;margin: 10px auto;height:30px;line-height:30px;}
.place a{color:#999;}
.place a:hover{color:red;text-decoration:none;}
.place span{color:#333;}
.place .news {float: right;padding-left: 30px;color: #ff6600;background: url(https://static.fangxiaoer.com/web/images/ico/sign/news.gif) 8px 9px no-repeat}
.place .news a{color:#ff6600;}
.place .news a:hover{ text-decoration:underline;}

.pro_name{margin-bottom:15px;font-size:24px;color:#333;height: 63px;line-height:28px;overflow:  hidden;}

.pro_name p{/* float:left; */margin-right:19px;float: left;}
.pro_name a{float:left;width: 40px;font-size:14px;color:#FFF;height: 20px;line-height: 20px;text-align:center;border: 1px solid #eff3fd;color: #668ae9;/* margin-left:10px; */border-radius:2px;background:  #eff3fd;margin-top: 10px;}
.pro_name a:hover{text-decoration:none;color:#FFF;border:1px solid #ff9766;background:#ff5200}
.zhuce{float:right;}
.zhuce img{float:left}
.zhuce a{float:left;color:#ff5200;margin:0;border:none;border-top:1px solid #ff5200;border-bottom:1px solid #ff5200;width:auto;border-radius:0;cursor:pointer}
.zhuce a:hover{color:#900;border:none;border-top:1px solid #ff5200;border-bottom:1px solid #ff5200;}


/*切换*/
.house_move_act{}
.house_move_act ul{overflow:  hidden;height: 50px;}
.nav_house{line-height: 50px;height: 50px;text-align:center;margin-bottom: 20px;background:  #f5f5f5;}
.nav_house li{float:left;margin: 0 2px;color:#ff6600;font-size:14px;font-weight:bold;line-height: 46px;}
.nav_house li.hover{color: #ff5200;}
.nav_house li.hover a{color: #ff5200;border-bottom:  2px solid #ff5200;}
.nav_house li a{
	display:  inline-block;
	padding: 0 24px;
	font-size: 16px;
	height: 48px;
}
.nav_house a.ewm{float:right;position:relative;cursor:pointer;margin-right:25px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_ewm.gif) no-repeat left;padding-left:25px}
.nav_house span{display:none;position:absolute;z-index:1000;border:1px solid #cccdd4;/* padding:15px; */background:#fff;right:-25px;width: 200px;height: 120px;}
.nav_house a.ewm:hover span{display:block}
.nav_house a.ewm i{color:#888;float: left;text-align: left;line-height: 31px;margin-top: 11px;}
.nav_house a.ewm img{width: 120px;margin-bottom: -14px;float: left;}
.nav_house .soucang{float:right;margin-right:20px;cursor: pointer;}
.nav_house .soucang i{float:left;width:18px;height:17px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang.gif) 0px 0px;margin: 9px 7px 0 0;}
.nav_house .soucang i.hover{background: url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang.gif) 0px -17px;}
#qrcode{float: left;height: 60px;margin:  0;width:  60px;margin-right:  10px;}

/*优惠*/
.privilege{height:170px; background: url(https://static.fangxiaoer.com/web/images/sy/house/view/privilege.jpg) no-repeat;margin-bottom:20px; position:relative;}
.privilege_l{width:270px;text-align:center;color:#fff; font-size:36px; font-weight:bold; line-height:170px; float:left}
/*.privilege .time{height:39px; line-height:39px;margin:8px 0;font-size:30px; color:#FFF; font-weight:normal;margin-top:20px;}
.privilege .time em{width:39px;height:39px;margin:0 2px 0 4px; background:url(https://static.fangxiaoer.com/web/images/sy/house/view/privilege_time.png);float:left; text-align:center}
.privilege .time p{float:left;}
.privilege .time i{float:left;margin:0 4px 0 2px}*/
.privilege_btn{width:122px;height:38px;margin:0 auto;}
.privilege_c{width:680px;line-height:36px; color:#f7dd83;font-size:34px; float:left; font-weight:bold;padding-top:35px;}
.privilege_r{width: 260px;float: left;height: 140px;color: #FFF;padding-top: 40px;line-height: 30px;font-size: 14px;}

/*图片切换*/
.newHouseBasicInfo{}
.pic{width: 600px;float: left;position: relative;height: 490px;}
#PicSlide .img{height: 400px;overflow:hidden;}
#PicSlide .img img{width: 598px;height: 398px;border:1px solid #dfe1e0;}
#PicSlide .img li{display:none;position:absolute;top:0;left:0;}
#PicSlide .title{bottom:70px;width:100%;position:absolute;}
#PicSlide .shadow{background-color:#000;opacity:.5;filter:alpha(opacity=50);height:70px;}
#PicSlide .title .h3{z-index:30;line-height:20px;margin:4px 10px 0 10px;position:absolute;}
#PicSlide .title .h3 a{font-weight:normal;font-size:12px;color:#fff;}
#PicSlide .thumb{top: 400px;width: 600px;left: 1px;position: absolute;overflow: hidden;_zoom: 1;height: 90px;}
#PicSlide .thumb ul{z-index:20;width:400px;overflow:hidden;_zoom:1;margin-top: 8px;width:600px;}
#PicSlide .thumb li{width: 112px;height: 79px;float:left;_display:inline;text-align:left;position:relative;margin-right: 10px;}
#PicSlide .thumb li:last-child{margin-right:0}
#PicSlide .thumb li img{width: 113px;height: 80px;/* border:1px solid #dedfe1; */}
#PicSlide .thumb .now-status{position:absolute;width: 113px;height: 80px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/house_qhbig.png) no-repeat;_background: url(https://static.fangxiaoer.com/web/images/ico/sign/house_qh_ie6.gif) no-repeat;z-index: 999;top: 8px;_top:-5px;left:0;background-size: 100%;}
#PicSlide .thumb li em{position: absolute;bottom: 0px;width: 100%;height: 24px;background: rgb(0, 0, 0);opacity: 0.6;z-index: 100;left:1px;}
#PicSlide .thumb li p{position: absolute;bottom: 0px;width: 100%;height: 24px;line-height: 24px;text-align: center;z-index: 101;left:1px;color:#fff;}
/*中*/
.right{float:left;width: 550px;margin-left: 20px;position:relative;}
.newHouseviewPz .right{
	width: 508px;
	padding: 0 10px 18px 30px;
	border: 1px solid #ededed;
	/* height: 467px; */
}
.price{border-bottom:  1px solid #ededed;line-height: 49px;padding: 26px 0 15px 0;font-size: 30px;}
.price s{font-weight: normal;padding-left: 17px;}
.price ul li:nth-child(1){ width: 100%;}
.price .getChangePzBtn{
	width: 126px;
	height: 24px;
	line-height:  24px;
	color:  #fff;
	background: #ff6100;
	border-radius: 2px;
	margin-top: 11px;
	cursor:  pointer;
	/* position: absolute; */
	margin-left: 10px;
	position: absolute;
	right: 70px;
	top: 0;
}

.layer{ width: 204px;  position: absolute; left: 100px; top: 34px; background-color:#fff;box-shadow: 2px 3.464px 10px 0px rgba( 156, 155, 155,0.77 );
	z-index: 99; display: none;padding-right: 25px;}
.price .layer li:nth-child(1){width: 100%;}
.price .layer li{ font-size: 14px; color: #333333}
.price .layer li i{font-size: 16px;color: #ff5200;font-weight: 300;}
.price .layer li span{ margin-left: 32px;}

.huxing .getChangePzBtn{
	width: 126px;
	height: 24px;
	line-height:  24px;
	color:  #fff;
	background: #ff6100;
	border-radius: 2px;
	margin-top: 11px;
	cursor:  pointer;
	/* position: absolute; */
	margin-left: 10px;

}
.price em{width:24px;height:23px;display:inline-block;margin: 0 0 -5px 0px;}
.price .jun em{background-position: 0px 3px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/price_44.gif) no-repeat;}
.price .qi em{background-position: 0px 0px;background: url(https://static.fangxiaoer.com/web/images/ico/sign/price_33.gif) no-repeat;}
.price p{width: 56px;float:left;font-size:14px;line-height: 48px;color: #999;margin-right: 14px;}
.price ul{float:left;width: 437px;/* background:#f6f6f6; */position: relative;}
.price li{float: left;width: 210px;line-height: 34px;font-size: 14px;/* padding: 0 14px; */height: 38px;}
.price li img{ vertical-align: bottom;margin-bottom:8px;margin-left:5px;}
.price i{font-size: 30px;color:#ff6600;font-family: dinot-bold;margin-top: 0px;display:  inline-block;}
.price ul>i{font-size: 25px;margin-top: -22px;display:  inline-block;}
.price em{font-size:28px; color:#ff6600;}
.info{/* height: 234px; */margin-bottom: 20px;margin-top: 12px;}
.info li{line-height: 29px;font-size:14px;margin-top: 4px;/* white-space: nowrap; *//* overflow: hidden; */}
.info li i{float:left;color: #999;margin-right: 30px;}
.info li p{float:left;margin-right: 10px;}
.info li p{max-width: 310px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}
#traffic{width: 311px;}
.info li .seeAdderss{max-width: 300px;}
.info li a{float:left;line-height: 24px;padding: 0 10px;margin-top: 4px;color: #999;cursor:pointer;font-size: 12px;border: 1px solid #fff;margin-left: 7px;font-weight: normal;padding-right: 0;}
.info li a table{display:none;position: absolute;right: 0;top: 0;}
.info li a:hover table{display:inline;position:absolute;top:40px; width:400px; color:#999;border-collapse:collapse; margin-left: -200px;margin-top:0px; z-index:10; background:#fff;overflow:hidden;z-index:999;}
.info li a:hover td{border:1px solid #ebebeb; border-top:0;line-height:27px;padding-left:10px;font-size:12px;color: #333;}
.info li a:hover th{font-size:16px; font-weight:100;color:#333}
.info li a:hover{color:#ff5200;}
.info li span{margin-right:10px; float:left;padding: 0 20px;color:#FFF;line-height:28px;margin-top:4px;}
.
.info li span.t1{ background:#23b4ff;}
.info li span.t2{ background:#7cd301;}
.info li span.t3{ background:#905be0;}
.info li .infoIconHx{
	border:  none;
	margin-left:  5px;
	border:  1px solid #fff;
	overflow:  hidden;
	font-weight: normal;
}
.info li .infoIconHx s{background: url(https://static.fangxiaoer.com/web/images/sy/house/view/newHouseinfohx.jpg) no-repeat;display:  inline-block;height: 15px;width: 15px;margin-right:  6px;float:  left;margin-top: 6px;}
.info li .infoIcontime{
	border:  none;
	background: url(https://static.fangxiaoer.com/web/images/sy/house/view/time_wei.png) no-repeat 0 6px;
	padding-left:  19px;
	border: 1px solid #fff;
	margin-top: 2px;
}
.info li .infoIconMap{
	border:1px solid #fff;
	height: 22px;
	text-align:  center;
	padding: 0 6px;
	overflow:  hidden;
}
.info li .infoIconMap s{background: url(https://static.fangxiaoer.com/web/images/sy/house/view/newHouseinfodt.jpg) no-repeat;display:  block;height:  15px;width:  15px;float:  left;margin-top: 5px;margin-right:  6px;}
.info li a:hover{border: 1px solid #ebebeb;color:#666;}
.ourPhone{/* overflow:hidden; *//* height: 47px; */margin-top: 20px;}
.tel{color:#ff6600;padding-left: 0;font-weight: bold;font-size: 24px;/* background: url(https://static.fangxiaoer.com/web/images/ico/sign/ourPhone.png) no-repeat 2px -2px; */line-height: normal;position:relative;/* overflow: hidden; *//* width: 310px; */float: left;}
.tel i{font-size: 14px;color:#a6a6a6;font-weight:normal;padding-top:2px;padding-bottom:2px;}
.tel0{float:left;font-size:12px;color:#a6a6a6;background: url(https://static.fangxiaoer.com/web/images/ico/sign/ourPhone.png) no-repeat 2px -53px;padding-left: 55px;width: 105px;}
.tel0 span{padding-left:40px;background: url(https://static.fangxiaoer.com/web/images/sy/dealer/view/ico_vip.png) 3px -116px no-repeat;}
.tel0 p{color: #ff6600;font-weight:bold;font-size: 24px;}
.tel1{float:left;width: auto;font-family: dinot-bold;color:  #333;font-size: 30px;}
.tel1 b{
	font-size:  14px;
}
.tel2{	float: left;margin: 20px 20px 0 0px;display:none;}
.tel2 a{width:118px;float:left;height:33px;background:#ff6600;border:none;border-radius:5px;color:#FFF;font-family:"微软雅黑";font-size:14px;text-align:center;line-height:33px;}
.tel3{	float:left;font-size:12px;color:#a3a3a3;	font-weight:normal;background:url(https://static.fangxiaoer.com/web/images/ico/sign/time.gif) no-repeat left;padding-left:30px;margin-top:20px;width:70px;}
.tel span{float: left;margin-top: 35px;width: 282px;height: 25px;font-size: 12px;background: #f6f6f6;color: #bebebe;line-height: 25px;text-indent: 5px;display: block;font-weight: normal;}
.s_time{padding-left:40px;/* background: url(https://static.fangxiaoer.com/web/images/sy/house/view/s_time.gif) 20px no-repeat; */position:  absolute;right:  0;font-size:  14px;bottom:  0;color:  #999;}
.info_btn{overflow: hidden;margin-top: 10px;}

.info_btn input{width: 180px;float:left;border-radius:3px;font-size: 16px;height: 44px;line-height: 44px;background:  #ffefe7;border:  none;}
.info_btn input+input{
	background:  #ff5200;
	color: #fff;
}
.info_btn input:hover{color: #fff;background: #e03700;}
.btn_1{color:#ff6600;margin-right: 15px; border:1px solid #ff6600; text-align:center; width:221px;float:left; border-radius:3px;font-size:20px;height:44px; line-height:44px;margin-top: 0;}
a.btn_2{margin-right:15px; border:1px solid #27b779; float:left;width:221px; border-radius:3px;color:#27b779; font-size:20px;}
a.btn_2:hover{color:#249E6A; text-decoration:none;border:1px solid #249E6A}
.btn_1:hover{}
.info_btn p{float:left;line-height:15px;margin-top:24px; width:116px;}
.info_btn span{float:left;width:24px;}
.info_btn img{float:left;width:30px;height:30px;margin:0 5px;}
.online{ position:absolute;bottom:0;right:0;width:175px;height:137px;}
.online img{float:left;border:1px solid #ff6600;width:135px;height:135px;}
.online a.online-font{float:left;width:33px;height:117px; color:#fff; background:#ff6600; font-size:20px; margin-left:5px; text-align:center; text-decoration:none;padding-top:20px; line-height:24px;}


/* 新闻切换 */
.slide{width: 1170px;	overflow: hidden;position: relative;margin: 30px auto 0}
.slide-ul{position:relative;width:8640px;margin:0 0 0 -5px;}
.slide-ul li{display:inline;float:left;width:390px;height:250px;overflow:hidden; background:url(https://static.fangxiaoer.com/web/images/ico/sign/house_news_qh.gif) 0 70px no-repeat; text-align:center; font-size:14px; }
.slide-ul a{display:block;width:230px;height:350px;overflow:hidden;color:#fff;}
.slide-ul a:hover .pic-txt{color:#fff;}
.slide-ul .pic-txt{display:block;bottom:-30px;position:relative;width:220px;height:30px;margin:-30px 0 0;padding:0 5px;font:14px/30px Microsoft YaHei;text-align:center;z-index:1;background:rgba(0,0,0,0.5);filter:progid:DXImageTransform.Microsoft.gradient(GradientType=1,startColorstr=#80000000,endColorstr=#80000000);cursor:pointer;}
.info-btn{width:90px;height:22px;overflow:hidden;margin:0 auto;padding:11px 0;zoom:1;}
.info-btn li{float:left;padding:0 3px;}
.info-btn li span{display:block;width:22px;height:22px;background:url(https://static.fangxiaoer.com/web/images/ico/sign/sjbz_infor-btn.png) 0 -27px no-repeat;text-indent:-9999em;overflow:hidden;cursor:pointer;}
.info-btn .info-cur span,.info-btn .info-hover span{background-position:0 0;}
.slide-ul li span{font-size:16px; line-height:35px;}
.slide-ul li p{width:346px;margin:60px auto 0; text-align:left;height:200px;}
.news_x{width:283px;margin:0 auto;}
.news_x i{float:left; font-family:"宋体"; line-height:24px;}
.news_x i.fenxiang{float:right;background:url(https://static.fangxiaoer.com/web/images/ico/sign/house_fenxiang.gif) no-repeat left;padding-left:30px;}
.news_x i.zhan{float:right;background:url(https://static.fangxiaoer.com/web/images/ico/sign/house_zhan.gif) no-repeat left;padding-left:20px;margin-right:20px;}

/*在线选房*/
.zsxx_list{overflow:hidden;margin: 17px 0 12px 10px;display: none;}
.zsxx_list ul{display:block;margin-left: -1px !important;}
.zsxx_list li{float:left;padding:0 25px;border-left:1px solid #e5e5e5;height:19px;line-height:19px;cursor: pointer;}
.zsxx_list li.hover{color:#ff3333}
.mappy_info{position:absolute;width: 401px;height:348px;border:1px solid #ddd;background:#fff;z-index: 1000;right: 17px;top: 24px;display: none;}
.mappy_t{font-size:14px;height:40px;line-height:40px;padding-left:23px;}
.mappy_close{background: url(https://static.fangxiaoer.com/web/images/sy/dealer/view/close.png) no-repeat;width:16px;height:16px;position: absolute;right: 9px;top: 9px;cursor: pointer;}
.mappy_xx{padding-left:23px;overflow: hidden;}
.mappy_xx li{float:left;width:186px;line-height:36px; ;background: url(https://static.fangxiaoer.com/web/images/sy/dealer/view/ico_vip.png) no-repeat}
.mappy_xx li p{float:left;width:72px;color:#999;padding-left:21px;}
.mappy_xx li.n1{background-position: -18px -262px;}
.mappy_xx li.n2{background-position: -16px -405px;}
.mappy_xx li.n3{background-position: -18px -298px;}
.mappy_xx li.n4{background-position: -16px -443px;}
.mappy_xx li.n5{background-position: -18px -334px;}
.mappy_xx li.n6{background-position: -16px -476px;}
.mappy_hx{float:left;width: 68px;line-height:36px; background: url(https://static.fangxiaoer.com/web/images/sy/dealer/view/ico_vip.png)  -16px -370px no-repeat;margin-left: 23px;padding-left: 21px;color: #999;}
/*滚动*/
.scroll_out{float:left;width: 286px;position: relative;margin-top: 2px;}
.scroll{position: relative;}
.scroll li{padding-bottom: 9px;font-size: 14px;line-height: 35px;}
.scroll p{float:left;width:33%}

#zxxf{
	margin-top: 0px;
}
#worldMap img{height:inherit}
.zxxf_l{
	width: 750px;
	float: left;
	height: 550px;
	text-align: center;
	position: relative
}
.zxxf_l .picture{
	width:698px;
	height:388px;
}
.zxxf_r{
	width: 397px;
	margin-left: 20px;
	float: left;
	height: 235px;
	display: inline;
}
.zxxf_r h2{
	color: #666;
	line-height: 32px;
	padding-left: 16px;
	font-size: 16px;
	margin-bottom:5px;
	font-weight: normal;
	font-family: "微软雅黑";
}
.zxxf_r h2 a{float:right;font-size: 12px; line-height: 34px;color: #ff5200;}
.zxxf_r .xiala div{
	line-height: 24px;
	height: 24px;
	border: 1px solid #cecece;
	background:  #f7f7f7;
	padding-left: 24px;
	cursor: pointer;
	margin-bottom: 9px;
}
.zxxf_r .xiala div.xiala_gd{
	line-height: 30px;
	height: auto;
	border: none;
	background: none;
	padding-left:0;
	cursor: default;
	margin-bottom:0;
	height:100px;
	overflow-x: hidden;
	scrollbar-face-color: white;
	scrollbar-highlight-color: #ccc;
	scrollbar-shadow-color: #ccc;
	scrollbar-3dlight-color: white;
	scrollbar-arrow-color: #ccc;
	scrollbar-track-color: white;
	scrollbar-darkshadow-color: white
}
.zxxf_r .xiala div.xiala_gd li{
	overflow: hidden;
}
.zxxf_r .xiala div.xiala_gd li p{
	float:left;
	text-align:center;
	width: 106px;
	margin-right:20px;
	height: 30px;
	line-height: 30px;
}
.zxxf_r .xiala div.xiala_gd li a{
	display:block;
	height:30px;
}
.zxxf_r .xiala div.xiala_gd li a:hover{
	background:#ddd;
	color:#666;
}
.zxxf_r table{
	padding: 0 14px;
	width: 100%;
}
.zxxf_r table th,.zxxf_r table td{
	font-weight:normal;
	line-height:25px;
	text-align:left;
	padding-left:20px;
}
.zxxf_r table th{
	border-bottom:1px solid #f2d6c5;
}
.zxxf_r table a{
	color:#353a87;
}
/*图片定位*/
.drow{
	position: absolute;
	background: url(https://static.fangxiaoer.com/web/images/sy/house/view/mapbsbbg.png) right no-repeat;
	height: 40px;
	text-align: center;
	display: block;
	left: 140px;
	top: 206px;
	padding-right:15px;
	color:#fff;
	line-height:28px;
	cursor:pointer;
}
.drow span{
	display:block;
	height:40px;
	padding-left:15px;
	background: url(https://static.fangxiaoer.com/web/images/sy/house/view/mapbsbbg.png) left no-repeat;
	margin-left:-3px;
}
/* .zxxf_l li.hover{
	background: url(https://static.fangxiaoer.com/web/images/sy/house/view/mapbsobg.png) right no-repeat;
} */
/* .zxxf_l li.hover span{
	background: url(https://static.fangxiaoer.com/web/images/sy/house/view/mapbsobg.png) left no-repeat;
} */
.i-list{
	position: absolute;
	background:#fff;
	border:2px solid #ff6600;
	padding:5px;
	width:140px;
	text-align:left;
	line-height:24px;
	border-radius:10px;
	display:none;
	color:#000;
}
.i-list b{
	color:#ff6600;
}
.i-list a{
	color:#999;
}
.i-list a:hover{
	color:red;
}


/*基础资料*/
.jczl table{border-collapse:collapse;}
.jczl td{font-size:14px;padding-left:16px;border:1px solid #ebebeb;padding: 15px 16px 15px;}
.jczl td.even{text-align:right; padding-right:10px; background:#f8f8f8;}

/*点击*/
.dianping{border:1px solid #ccc;height:497px;margin-top:27px;}
.dianping h2{ font-weight:normal;font-size:16px;border-bottom:1px solid #ccc; line-height:50px; color:#555;background:url(https://static.fangxiaoer.com/web/images/ico/sign/dp.gif) no-repeat 31px 18px;padding-left:61px;margin-top:10px;}
.zhi{height:94px; position:relative; background:url(https://static.fangxiaoer.com/web/images/ico/sign/zhi.gif) no-repeat center; text-align:center; color:#acacac;padding-top:10px;}
.zhi p{font-size:20px;color:#d62222; margin-bottom:30px;}
.zhi em{width:234px;;height:18px; display:block; position:absolute; top:42px;left:103px;}
.zhi i{display:block;height:18px; background:#f04848}
.ren{width:404px;margin:0 auto;border-top:1px solid #ddd;}
.ren p{line-height:20px; color:#b2b2b2;}
.ren p img.people{width:50px;height:60px;float:left;margin-right:15px;}
.ren b{font-size:14px;line-height:35px;color:#555;}
.ren span{float:right;}
.ren span img{margin:10px 5px 0 5px;float:left;}
.ren .weixin{width:117px;margin-top:10px;line-height:24px;color:#666;}
.dianping h3{background:url(https://static.fangxiaoer.com/web/images/ico/sign/dianp.gif) no-repeat center left;padding-left:15px;margin:30px 0 0 15px;}
.dianping p.txt{line-height:18px;width:370px;margin:10px auto;height:84px}
.dianping p.txt a{color:#ff6600;}
.sm{width:196px;margin:0 16px 0 14px;float:left; _display:inline}
.sm h4{font-size:12px; font-weight: normal; border-bottom:1px solid #e8e8e8;line-height:24px;height:24px;margin-bottom:10px;}
.sm h4 i{padding:0 5px;float:left;border-bottom:1px solid #11880b;color:#11880b;}
.sm p{line-height:18px;}
.qd{margin:0;}
.qd h4 i{color:#b2b2b2;border-bottom:1px solid #b2b2b2;}

/*户型*/
.layoutTypeR{
	float:  right;
	margin-right: 15px;
}
.layoutTypeR a{
	font-size:  14px;
	color: #666;
	margin-right: 10px;
	/* background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/right_arrw.png); */
	background-repeat: no-repeat;
	background-position: 64px center;
	padding-right: 15px;
}
.layoutTypeR a:last-child{
	background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/right_arrw.png);
}
.layoutTypeR a:hover{color:#ff5200}
.layoutTypeR i{
	margin-left: -10px;
	color:  #eaeaea;
	height:  12px;
}
.huxing{ overflow:hidden}
.huxing ul{
	overflow:  hidden;
	padding: 0 30px;
}
.huxing li{float:left;border-bottom:  1px solid #ededed;height: 140px;width: 1095px;margin:  0 auto;padding: 15px 0 35px 0;}
.huxing li:last-child{border-bottom:none}
.huxing li img{width: 100%;}
.huxing li>a{
	display:  block;
	width:  160px;
	height:  160px;
	float:  left;
	margin-right:  30px;
	overflow:  hidden;
}
.newHouseHxMain>div{line-height: 31px;font-size:14px;overflow:  hidden;}
.newHouseHxMain>h4{
	margin-bottom: 20px;
}
.newHouseHxMain>div p{
	float:  left;
	margin-right: 15px;
}
.huxing p span{}
.huxing-seemore{
	float:  right;
	color: #999;
}
.huxing .newHouseHxMain{
	float:  left;
	width:  370px;
	margin-top: 17px;
}
.huxing .newHouseHxRight{
	float:  right;
	width: 220px;
	margin-top: 40px;
	text-align: right;
}
.huxing .newHouseHxRight .hxPrise{
	font-size:  14px;
	color:  #ff5200;
	line-height: 31px;
	overflow: hidden;
}
.huxing .newHouseHxRight .hxPrise>div{display:inline-block;float:right}
.huxing .newHouseHxRight .hxPrise>span{float: right;line-height: 37px;}
.huxing .newHouseHxRight .hxPrise .yfyj {
	font-size:  30px;
}
.huxing .newHouseHxRight .hxPrise .yfyj p{font-size: 24px;float:  right;}
.huxing .newHouseHxRight .hxPrise span{
	font-family: dinot-bold;
}
/*评论*/
#xmpj{/* margin-top: 25px; */}
#xmpj .remark_classify{
	margin-top: 20px;
}
#comment{margin-top:20px;}
.comment_tit{margin-bottom:20px;}
.comment_tit ul{ background:url(https://static.fangxiaoer.com/web/images/ico/sign/title_line.gif) repeat-x bottom;height:50px;margin:27px auto 10px;}
.comment_tit li{font-size:20px;  color:#999; border-bottom:5px solid #f6f6f6; float:left;padding:0 16px; line-height:46px; cursor:pointer;}
.comment_tit li.hover{color:#ff6600;border-bottom:5px solid #FF6600;}
.pingjia{width:1208px; border:1px solid #ddd;height:112px;margin:20px 0}
#comment .rate{width: 190px;float: left;padding: 30px 0 0;text-align: center;}
#comment .rate strong {font: 400 46px/30px arial;color: #e4393c;}
#comment .rate strong span {font-size: 24px;color: #e4393c;}
#comment .rate span {color: #999;font-family: arial;}
#comment .percent {float: left;width: 186px;height: 74px;padding: 10px 0 0;border-right: 1px solid #E4E4E4;margin-top:15px;}
#comment .percent dl {padding: 4px 0;overflow: hidden;zoom: 1;}
#comment .percent dt {float: left;width: 70px;}
#comment .percent span {color: #9C9A9C;}
#comment .percent dd {float: left;width: 100px;height: 10px;margin-top: 2px;overflow: hidden;background: #efefef;}
#comment .percent dd div {overflow: hidden;height: 10px;width: 0;background-color: #ED0000;background-image: -ms-linear-gradient(top,#ED0000 0,#A50000 100%);background-image: -moz-linear-gradient(top,#ED0000 0,#A50000 100%);background-image: -o-linear-gradient(top,#ED0000 0,#A50000 100%);background-image: -webkit-gradient(linear,left top,left bottom,color-stop(0,#ED0000),color-stop(1,#A50000));background-image: -webkit-linear-gradient(top,#ED0000 0,#A50000 100%);background-image: linear-gradient(to bottom,#ED0000 0,#A50000 100%);}
#comment .actor-new{width:355px;float:left;line-height:24px;padding:20px 0 0 30px}
#comment .actor-new dd{float:left;width:160px;margin-right:15px;}
#comment .actor-new dd i{color:#0159a0;}
#comment .actor-new dd span{float:right;color:#a9a5c6;}

#comments-list {
	width: 100%;
}
.m-tab-trigger-wrap {
	position: relative;
	height: 30px;
	line-height: 30px;
	margin-top: 4px;
	border-right: 1px solid #DEDFDE;
	border-bottom: 1px solid #DEDFDE;
	border-left: 1px solid #DEDFDE;
	border-top: 2px solid #999;
	background-color: #F7F7F7;
	overflow: visible;
}
.m-tab-trigger {
	float: left;
	height: 31px;
	margin-bottom: -1px;
}
.m-tab-trigger .trig-item {
	float: left;
	height: 30px;
	line-height:30px;
}
.m-tab-trigger .trig-item.curr a {
	position: relative;
	height: 35px;
	line-height: 35px;
	margin-top: -6px;
	margin-left: -1px;
	border-top: 2px solid #e4393c;
	border-left: 1px solid #DEDFDE;
	border-right: 1px solid #DEDFDE;
	background-color: #fff;
}
.m-tab-trigger .trig-item a {
	display: inline-block;
	padding: 0 10px;
	font-family: '\5fae\8f6f\96c5\9ed1';
	font-size: 14px;
}
.comments-table {
	margin-top: 10px;
	width: 100%;
}
.com-table-header {
	height: 30px;
	line-height: 30px;
	border: 1px solid #ddd;
	overflow: hidden;
	background-color: #f7f7f7;
	padding: 0 20px;
}
.com-table-header .column1 {
	width: 655px;
	text-align: center;
	margin-right: 100px;
}
.com-table-header .item {
	float: left;
	font-weight: 700;
}
.com-table-header .column2 {
	width: 75px;
	padding-right: 100px;
	text-align: center;
}
.com-table-header .column3 {
	width: 120px;
	padding-right: 60px;
	text-align: center;
}

.com-table-header .column5 {
	width: 135px;
	_width: 132px;
	text-align: center;
}
.com-table-main {
	width: 100%;
}
.comments-item {
	margin-top: -1px;
	padding: 20px;
	border: 1px solid #ddd;
}
div.com-i-column {
	float: left;
}
div.com-item-main .column1 {
	width: 655px;
	margin-right: 100px;
}
div.com-i-column .p-comment {
	margin-bottom: 5px;
	line-height: 22px;
	word-break: break-all;
}
div.com-i-column .p-comment .time {
	display: inline-block;
	font-weight: 400;
	color: #999;
}
div.com-i-column .p-show-img {
	margin-bottom: 10px;
}
div.com-i-column .p-show-img table {
	float: left;
	margin-right: 5px;
	empty-cells: show;
}
div.com-i-column .p-show-img td {
	width: 80px;
	height: 80px;
	border: 1px solid #ddd;
	text-align: center;
	vertical-align: middle;
}
div.com-i-column .p-show-img .show-number {
	display: inline-block;
	margin-top: 64px;
	color: #999;
}
div.com-i-column .p-show-img .view-show {
	display: inline-block;
	margin-top: 64px;
	color: #005aa0;
}
div.com-i-column .p-tabs {
	margin-bottom: 5px;
}
div.com-i-column .p-tabs .item {
	display: inline-block;
	height: 22px;
	line-height: 22px;
	margin-right: 3px;
	padding: 0 5px;
	background-color: #fff1e4;
}
div.com-i-column .p-tabs .num {
	color: #999;
}
div.com-item-main .column2 {
	width: 85px;
	margin-right: 130px;
}
div.com-i-column .grade-star.g-star5 {
	background-position: -109px -239px;
}
div.com-i-column .grade-star {
	width: 75px;
	height: 14px;
	background: url(https://misc.360buyimg.com/product/item/1.0.0/css/i/newicon20140910.png) no-repeat -183px -239px;
}
div.com-item-main .column3 {
	padding-right: 40px;
	width: 160px;
}
div.com-i-column .type-item {
	line-height: 22px;
}
div.com-i-column .type-item .label {
	color: #999;
}
div.com-item-main .column5 {
	width: 135px;
}
div.com-i-column .user-item {
	line-height: 22px;
}
div.com-i-column .user-item .user-ico {
	width: 16px;
	height: 16px;
	float: left;
	margin-right: 5px;
	margin-top: 3px;
}
div.com-i-column .user-item .u-vip-level {
	margin-right: 5px;
}
div.com-i-column .user-item .u-addr {
	color: #999;
}
.comment-operate {
	margin-bottom: 20px;
}
.comment-operate .reply {
	margin-right: 20px;
}
.comment-operate .nice, .comment-operate .reply {
	color: #999;
	cursor: pointer;
}
.reply-textarea {
	width: 100%;
}
.reply-textarea .reply-arrow {
	width: 100%;
	height: 12px;
	position: relative;
	bottom: -1px;
}
.reply-textarea .reply-arrow .layer1 {
	top: -1px;
	border-color: transparent transparent #d9d9d9;
}
.reply-textarea .reply-arrow b {
	position: absolute;
	left: 20px;
	top: 0;
	width: 0;
	height: 0;
	overflow: hidden;
	border-width: 6px;
	border-style: dashed dashed solid;
}
.reply-textarea .reply-arrow .layer2 {
	border-color: transparent transparent #f5f5f5;
}
.reply-textarea .inner {
	padding: 20px 30px 15px 20px;
	border: 1px solid #d9d9d9;
	background-color: #f5f5f5;
}
.reply-textarea .reply-input {
	display: block;
	width: 100%;
	height: 20px;
	padding: 3px 5px;
	line-height: 20px;
	border: 1px solid #ddd;
	background-color: #fff;
}
.reply-textarea .btnbox {
	margin-top: 10px;
	text-align: right;
}
.reply-textarea .btnbox button.reply-submit {
	cursor: pointer;
	height: 25px;
	line-height: 25px;
	margin: 0;
}
.reply-textarea .btnbox .reply-submit {
	display: inline-block;
	height: 23px;
	line-height: 23px;
	padding: 0 14px;
	border: 1px solid #ddd;
	background-color: #f5f5f5;
}
.comment-replylist {
	width: 100%;
	overflow: hidden;
}
.comment-reply-item {
	padding: 15px 0;
	border-top: 1px dotted #ccc;
}
.comment-reply-item .reply-infor {
	margin-bottom: 7px;
	padding: 0 15px;
	line-height: 20px;
}
.comment-reply-item .reply-infor .user-name {
	display: inline-block;
	color: #005aa0;
}
.comment-reply-item .reply-infor .words {
	color: #333;
}
.comment-reply-item .comment-operate {
	margin: 0 15px;
}
.comment-operate .reply {
	margin-right: 20px;
}
.comment-operate .nice, .comment-operate .reply {
	color: #999;
	cursor: pointer;
}
.reply-textarea {
	width: 100%;
}
.clearfix:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
#cm1{display:none}

/*项目咨询*/
.cm_2_tit{height:25px;border:1px solid #ddd;}
.cm_2_tit li{padding:0 20px;line-height:26px;color:#676767;float:left; position:relative; cursor:pointer;}
.cm_2_tit li.hover{border-top:2px solid #ce0307; color:#ce0307; height:25px;border-left:1px solid #ddd;border-right:1px solid #ddd;margin:-2px 0 0 -1px;border-bottom:1px solid #FFF;}
.zx{border-left:1px solid #ddd; border-right:1px solid #ddd;border-bottom:1px solid #ddd;}
.zx_txt{border-bottom:1px solid #ddd; margin:0 20px;}
.zx_txt p{color:#666;line-height:20px;width:608px;padding:18px 0;}
.zx_txt a{float:right;padding:20px 0 0 0;display: none;}
.ask{color:#656565; background:url(https://static.fangxiaoer.com/web/images/sy/house/house/ask.gif) no-repeat 11px 14px; line-height:20px;padding:13px 10px 13px 34px;margin:0 18px;}
.zx .time{float:right; color:#999;}
.answer{color:#ce0306; background:url(https://static.fangxiaoer.com/web/images/sy/house/house/answer.gif) no-repeat 11px 14px #faf8f9; line-height:20px;padding:13px 10px 13px 34px;margin:0 18px;}
.answer p{width:900px;}
.question{border:1px solid #ddd;margin-top:10px;}
.question_l{width: 837px;float:left;padding:8px;}
.question_tit{background:url(https://static.fangxiaoer.com/web/images/sy/house/house/question.gif) left;height:34px;font-size:16px;line-height:24px;padding-left:40px;margin-bottom: 22px;}
.question .login{color:#c90104;margin:18px 0 18px 36px;display: none;}
.question .login a{color:#0066cb;}
.question label{overflow: hidden;padding-right: 18px;float: left;height: 28px;line-height: 28px;}
.question label input {margin: 0 5px 0 0;vertical-align: middle;margin-bottom: 4px;}
.ask_type li{ clear:both;line-height:30px;margin-left:40px;}
.ask_type b{float:left;width:72px;}
.ask_type textarea{width:710px;height:80px;}
.ask_btn{width: 86px!important;height:27px;line-height:27px; text-align:center; background:#d10508; color:#fff; border:none;}
.question_r{width:284px;float:left;border-left:1px solid #ddd; line-height:24px; padding-left:7px;}
.question_r b{line-height:50px;}
#AskList_txt{border:1px solid #ddd}


/*顶部弹出*/
.mk{position:fixed;left: 50%;top: 50%;margin-top:-230px;margin-left:-345px;z-index:22222;display: none;background: #FFF}
.main{width:690px;height:390px;position:relative;padding-top:46px;}
.mk .main a.btn_tc:hover{color:#fff;}
.tbForm{width:330px;margin:25px auto 0; border-top:1px solid #CCC;}
.tbForm p{  line-height:40px;font-size:14px;color:#000;}
.tbForm li{width:314px; height:44px; line-height:49px; border-bottom:1px solid #CCC; border-left:1px solid #CCC; border-right:1px solid #CCC;padding:3px 0 0 15px;}
.tbForm label{line-height:40px;float: left;margin-left: 15px;}
.tbForm input.inputStyle{  z-index:1; background:none;outline:none; border:none; line-height:40px; padding:0 ;height:40px; width:236px;font-size:14px;color:#999;}

.tbForm p.yz{margin-top:4px;padding:5px 2px;border: 1px solid #F03;line-height:20px;float:right}
.tbForm p.yz a{color: #F03;}
.tbForm p.mima{text-align:center;}
.tbForm p.mima a{color:#fe5200;font-size:14px;}
.tbForm p.mima a:hover{cursor:pointer;text-decoration: none;}

.youhuixieyi{ margin-bottom:15px;width:330px;margin:0px auto;}

span.error{position: absolute;color:#F50C0C;font-size:14px;}
span.ename{top: 121px;left: 520px;}
span.emobile{top: 170px;left: 520px;}
span.eyz{top: 220px;left: 520px;}
span.epassword{top: 264px;left: 520px;}
.cha{position: absolute;top: 0px;right: -52px;}
.cha:hover{cursor:pointer;}
.tcbg{position: absolute;top: 0;left: 0;
	width: 100%;
	z-index: 1100;
	display: none;filter:alpha(opacity=70) !important;
}




/*服务*/
.need{height: 458px;/* width: 378px; */float:left;background-color: #f3f3f3;padding: 0 10px;border:1px solid #ccc;}
.need_info{font-size: 14px; color:#999;margin-bottom: 8px;margin-top: 8px;}
.my_xl{height: 28px;line-height: 30px;width: 167px;padding: 4px 6px 4px 20px;position:relative;font-size: 14px;background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 175px 16px no-repeat;cursor:pointer; display:inline-block;float: left; margin-right:20px;}
.my_xl_txt{float:left;width: 150px;line-height: 28px;padding-right:17px;color:#666;}
.my_xl,.my_xl_list{border:1px solid #ccc; border-radius:4px}
.my_xl_txt,.my_xl_list li{text-indent:0px;overflow:hidden;}
.my_xl_list{z-index:99;position:absolute;top: 39px;left:-1px;z-index:88888;border-top:none;width:100%;display:none;_top:23px;margin-left:0px !important}
.my_xl_list li{list-style:none;height: 30px;line-height: 30px;cursor:default;background:#fff;padding-left: 20px;color:#666}
.my_xl_list li.focus{background:#3399FF;color:#fff}
.hf_ul>li{height: 45px;line-height:37px;margin-bottom:5px;}
.hf_ts{width: 133px;float:left;font-size:16px;color:#333;}
.letter4{ letter-spacing:4px}
.hf_ts span{width:20px;display:block;float:left;margin-top:11px;color:#f00;font-size:26px; line-height:26px}
.hf_input{width:173px;height:35px !important;line-height:35px;color:#666 !important;border:1px solid #ccc;border-radius:3px;margin-right:10px;float: left;}
::-webkit-input-placeholder { /* WebKit browsers */
	color: #666; font-family:"Microsoft YaHei"
}
:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
	color: #666; font-family:"Microsoft YaHei"
}
::-moz-placeholder { /* Mozilla Firefox 19+ */
	color: #666; font-family:"Microsoft YaHei"
}
:-ms-input-placeholder { /* Internet Explorer 10+ */
	color: #666; font-family:"Microsoft YaHei"
}
.yzm_input{width:105px}
.yzm_btn{padding:0 14px;text-align:center;line-height:31px;font-size:12px;color:#333;border:1px solid #ccc;background-color:#fff;margin-top:2px; cursor:pointer;float:left}
.area_li{height: 100px !important;}
.hf_textarea{width: 167px;height: 65px !important;padding:6px;margin-bottom: 10px;}
#need_btn{width: 160px;height: 35px;margin-left:145px;border:none;color:#fff;font-size:16px;background-color:#fe6218;cursor: pointer; display:block; text-align:center;line-height: 35px;}
.ljz{width: 34px; height: 23px;background: url(https://static.fangxiaoer.com/web/images/sy/formulate/checked.gif) center no-repeat;margin-top: 0; /* display:none; */padding: 0 10px;}
.errorBox .error{margin-left:145px;color:#eb0000;width:300px;display:block;float:left;height:17px;font-size:12px;line-height:20px;}
input.error{border: 1px solid #fd634f !important;}
#xf_djs{display:none;}


.zytc{width:156px; text-align:center; position:absolute;right:0;font-size:14px;top:7px;}
.zytc img{width:95px;height:95px; border-radius:100%;}
.zytc_pic{width:95px;height:95px; margin:0 auto;margin-bottom:10px; position:relative}
.zytc a{ position:absolute;width:95px; line-height:95px;top:0;left:0;color:#fff; background:url(https://static.fangxiaoer.com/web/images/ico/sign/b50.png); border-radius:100%; text-decoration:none; display:none}

#datetimepicker_mask{width:296px;color:#000;z-index:20;font-size:14px;position: absolute;left: 0px;top: 0px;padding-left:5px;line-height:42px;}
#datetimepicker_mask:hover{cursor:pointer;}
.bgjiantou{background:url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 296px 18px no-repeat;}
.timename{color:#000;z-index:1;width:110px;line-height: 39px;}
.info2{text-align:center;margin-top:15px;}
.main2{display:none;width:690px;height: 430px;_height:450px;*height:450px;position:relative;padding-top:46px;z-index: 1101;background-color:#fff;position: fixed;left: 50%;top: 50%;margin-top: -230px;margin-left: -345px;}
.title2{font-size:26px;text-align:center;}
.main2 .hf_input{width: 173px;height: 35px !important;line-height: 35px;color: #666 !important;border: none;border-radius:3px;margin-right: 10px;float: left;}
.info2 input{color:#666 !important;}
.tbForm{width:321px;/* margin: 75px auto 0 !important; */border-top:1px solid #CCC;}
.tbForm p{line-height:40px;font-size:14px;color:#000;}
.tbForm li{height: 44px;line-height: 49px;border-bottom: 1px solid #CCC;border-left: 1px solid #CCC;border-right: 1px solid #CCC;padding: 3px 0 0 5px;position: relative;}
.tbForm li .error{position: absolute;top:0;left: 330px;color:#F00;display:none;width: 170px;}
.emobile,.eyz{left: 330px !important;}
.tbForm label{line-height:40px;float: left;margin-left: 15px;}
.tbForm input.inputStyle{z-index:1;background:none;outline:none;border:none;line-height:40px;padding:0 ;height:40px;width:233px;font-size:14px;color:#999;}
.tbForm input.widthMd{width:130px !important;}
.tbForm input.widthBd{width:164px;}
.tbForm p.yz{margin-top:4px;padding:5px 2px;border: 1px solid #F03;line-height:20px;}
.tbForm p.yz a{color: #F03;}
.tbForm p.yz span{}
.tbForm p.mima{text-align:center;}
.tbForm p.mima a{color:#fe5200;font-size:14px;}
.tbForm p.mima a:hover{cursor:pointer;text-decoration: none;}
.btn{width:330px;line-height:45px;display:block;margin:20px auto 5px;background:#ff6600;color:#FFF;font-size:20px;text-align:center;}
.btn:hover{text-decoration: none;color: #333;}
.main2 .btn{margin-bottom: 39px;}
.goufangxieyi{width:330px;margin:15px auto;padding-left:12px;}
span.error{position: absolute;color:#F50C0C;font-size:14px;}
span.ename{top: 109px;left: 520px;}
span.emobile{top: 159px;left: 520px;}
span.eyz{top: 205px;left: 520px;}
span.epassword{top: 205px;left: 520px;}
.cha{position: absolute;top: 0px;right: -52px;}
.cha:hover{cursor:pointer;}
.tcbg2{display:none;position: fixed;top: 0;left: 0;height: 100%;width: 100%;z-index: 1100;filter:alpha(opacity=70) !important;}
.tbForm .my_xl{border:none;height: 30px;line-height: 30px;width:95%;padding: 4px 6px 4px 0;position:relative;font-size: 14px;background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 291px 16px no-repeat;cursor:pointer;display:inline-block;float: left;z-index:99}
.tbForm .my_xl_txt{float:left;width: 100%;line-height: 30px;color:#000;}
.tbForm .my_xl,.my_xl_list{border-radius:4px}
.tbForm .my_xl_txt,.my_xl_list li{text-indent:0px;overflow:hidden;}
.tbForm .my_xl_list{position:absolute;top: 46px;left:-6px;z-index:88888;border-top:none;width:200px;display:none;margin-left:0px !important}
.tbForm .my_xl_list li{list-style:none;height: 30px;line-height: 30px;cursor:default;background:#fff;padding-left: 6px;width: 311px;}
.tbForm .my_xl_list li.focus{background:#3399FF;color:#fff}
.tbForm .my_xl_input,.tese_input{position: absolute;top: -999999px;}
.bgblue{background-color: #03F !important;color:#FFF !important;}
.house_header{z-index:2;}
.school_district{margin-top: 6px;font-size: 12px;}
.school_district .schdis_title{float: left;background-color: #50c8c8;color: #fff;width: 45px;height: 25px;text-align: center;display: inline-block;line-height: 25px;}
.school_district div{float: left;border: 1px solid #d2d2d2;line-height: 23px;}
.school_district div span{margin: 0 15px;}
.house_charact{position: absolute;left: 0;top: 0px;z-index: 1;}
.house_charact span{width: 40px;height: 35px;float: left;display: block;color: #fff;text-align: center;line-height: 15px;font-size: 12px;padding-top: 5px;}
.house_charact .low{background-color: #f88a75;}
.house_charact .big{background-color: #74d6f0;}
.house_charact .live{background-color: #ffb43c;}
.house_charact .three{background-color: #f03b44;}
.house_charact .edu_house{background-color: #50c8c8;}
.xmts{font-size: 14px;}
.xmts_xqf{margin-top: 5px;margin-bottom: 20px;position: relative;}
.xmts .xmts_title{color: #f60;border-left: 4px solid #f60;line-height: 14px;padding-left: 14px;}
.xmts .xmts_weight{font-weight: bold;margin-top: 20px;}
.xmts p{line-height: 28px;color: #666;}
.edu_img{width: 160px;height: 110px;display: block;margin: 10px 0 10px 0;}
.lxw_img{width: 290px;height: 175px;display: block;position: absolute;right: 33px;top: 43px;}
.edu_img img, .lxw_img img{width: 100%;}

.disclaimer{width: 1130px;line-height:20px;margin:10px auto 8px auto;padding:15px 20px;background:#eee;font-size:12px;}

.ask_type a{width:86px;height:27px;line-height:27px; text-align:center; background:#d10508; color:#fff; border:none;/* display:block; */padding: 5px 14px;}

/*IM*/
.zxim{float:left;width: auto!important;}
.imrygl{float:left;width: auto!important;margin-right: 5px;}
.zxim img{float:left;width:50px;height:50px;border-radius:50%;}
.zxim p{width:67px;margin-top: 5px;line-height: 20px;}
.zxim p.unSupport{display: none;margin-top: 6px;}
.imrygl a{float:left;width: 220px!important;height: 46px;line-height:23px;border:1px solid #27b779; text-align:center;color:#27b779;margin-top: 0!important;font-size: 20px;line-height: 55px!important;border-radius: 3px;}
.im-move{color:#666;font-size: 20px;margin-top: 9px;float:left;margin-left:10px;cursor:pointer}
/*评价图片*/
.bigPic img{max-width:800px!important;}

/*销售动态  2016-5-24*/
.newHouseViewChunk .normalHouseMain{width:  900px;float:  left;}
.xstd{width: 1140px;margin:0 auto !important;display:block;font-size: 14px;overflow: hidden;}
.xstd li{line-height:32px;margin-bottom: 10px;border-top:1px dashed #eee;margin-top: -1px;padding: 6px 14px;}
.xstd li:first-child{border-top:none}
.xstd span{color:#aab2bd;padding-left: 10px;font-family: dinot-bold;font-style: italic;/* font-weight:  bold; */}
.xstd p{color:#666;padding-left: 10px;height: 53px;text-overflow:  ellipsis;width: 98%;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;margin-top: -5px;line-height: 29px;text-indent: -6px;}
.hj-col-lg-22{width: auto !important;}

/*右侧领房款浮动*/
.lingfangkuan{position:fixed;right:0;top:50%;margin-top: -80px;}
.lingfangkuan a{width: 167px;display: block;height: 295px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_lipin.png);}
.lingfangkuan p{padding: 57px 36px 0 60px;text-align: center;color: #ff5200;}
.lfk_close{position:absolute;top: -18px;right:0;cursor:pointer}
.lq_close2{top:-9px !important}

.left_fdgg{display:none}
.yzm_btn3{cursor:pointer}

#PicslidePrev,#PicslideNext{ position:absolute;left: 0;top: 40%; margin-top:-25px; display:block;width: 50px;height: 100px;background: url(https://static.fangxiaoer.com/web/images/download/appLoadDown.png) 0px 0px no-repeat;cursor: pointer;}
#PicslideNext{ left:auto;right: 0;background-position: -100px 0px;}

.lq_obj,.lq_close{display:none}

.huxing_move a{display:block;width:240px;height:40px;line-height:40px;margin:0 auto;border:1px solid #ff5200;text-align:center;color:#ff5200;font-size:14px;border-radius:8px;margin-top:20px}
.huxing_move a:hover{border:1px solid #900;color:#900}
.ck_pic ul li img{cursor:pointer}

/*收藏*/
.newHouseInfoRicn .soucang{float:right;/* margin-right:20px; */cursor: pointer;color:  #666;font-size:  12px;margin-right: 5px;}
.newHouseInfoRicn .soucang i{float:left;width: 16px;height:17px;background: url(https://static.fangxiaoer.com/web/images/sy/house/view/shouchang.png) no-repeat 0 3px;margin: 12px 7px 0 0;}
.newHouseInfoRicn .soucang i.hover{background: url(https://static.fangxiaoer.com/web/images/sy/house/view/ico_soucang2.gif) 0 3px no-repeat;margin: 12px 6px 0 0;}

.newHouseInfoRicn{
	line-height:  40px;
	padding-bottom: 20px;
	border-bottom:  1px solid #ededed;
	margin-top: 7px;
	overflow: hidden;
}
.newHouseInfoRicn .seemore{
	color:  #ff5200;
	font-weight:  bold;
}
.newHouseInfoRicn .contrast{
	float:  right;
	margin-right: 16px;
	background: url(https://static.fangxiaoer.com/web/images/sy/house/Contrast.png) no-repeat 0 center;
	padding-left: 21px;
	cursor:  pointer;
	color:  #666;
	font-size:  12px;
}

@charset "utf-8";
/* CSS Document */
/*学区简介*/
.xqjj_r{float:right; list-style:none;}
.xqjj_r li{float:left;line-height:48px;margin-left:65px;color:#ff6600;font-size:14px;}
.xqjj_r li img{width:20px;padding: 14px 5px 4px 5px;}
.xqjj_r li a{font-size:12px;color:#666;}
.xqjj_con{margin:0 10px 20px;border:1px solid #ddd;}
.xqjj_con_l{width: 779px;float:left;font-size:14px;line-height: 26px;color:#999;}
.xqjj_con_l .xqjj_name{color:#333;font-size: 20px;font-weight:  bold;line-height: 20px;padding-bottom: 12px;}
.xqjj_con_l .xqjj_name span{
	font-size:  12px;
	padding:  0 10px;
	font-weight:  normal;
	color:  #668ae9;
	background:  #eff3fd;
	line-height:  24px;
	height:  24px;
	display:  inline-block;
	margin-left: 10px;
}
.xqjj_con_l .xqjj_name .xq_name_r{float:right;font-size:14px;color:#999;}
.xqjj_con_l .view_icon{width:13px;height:18px; display:inline-block;margin-right:10px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/view_new_icon.png) no-repeat;}
.xqjj_con_l .xq_tel{height:16px;margin-right: 4px;line-height:16px;}
.xqjj_con_l .xqjj_address .view_icon{ background-position:0 -20px;}
.xqjj_con_l .xqjj_dir .view_icon{ background-position:0 -40px;}
.xqjj_con_l .xqjj_qy{color: #333;font-size:  16px;font-weight:  bold;line-height: 34px;}
.xqjj_con_l .xqjj_address,.xqjj_con_l .xqjj_dir{
	font-size:  14px;
	color:  #666;
	line-height: 32px;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 4;
	/* overflow: hidden; */
	position: relative;
}
.xqjj_dir .show{overflow:hidden}
.xqjj_dir .layer_sun{z-index:9999999;position:absolute;left: 0px;top: 54px;width:700px;background-color:#fff;display: none;box-shadow: 0.5px 0.866px 10px 0px rgba( 0, 0, 0, 0.3 );padding:20px;}

.xqjj_dir:hover .layer_sun{display:block}
.xq_imgs img{width: 300px;height: 220px;}

.xqjj_con_r{float:left;width: 300px;height: 220px;margin: 0px 25px 0 25px;}
.xqjj_caus{width:100%;position:relative;line-height:1;}
.carousel-inner{position:relative;width:100%;overflow:hidden;height: 220px;}
.carousel-inner > .item {
	position: relative;
	height: 220px;
	display: none;
	-webkit-transition: 0.6s ease-in-out left;
	-moz-transition: 0.6s ease-in-out left;
	-o-transition: 0.6s ease-in-out left;
	transition: 0.6s ease-in-out left;
}
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {  display: block;  line-height: 1}
.carousel-inner > .active,
.carousel-inner > .next,
.carousel-inner > .prev {  display: block}
.carousel-inner > .active {  left: 0}
.carousel-inner > .next,
.carousel-inner > .prev {  position: absolute;  top: 0;  width: 100%;}
.carousel-inner > .next {  left: 100%;}
.carousel-inner > .prev {  left: -100%;}
.carousel-inner > .next.left,
.carousel-inner > .prev.right {  left: 0;}
.carousel-inner > .active.left {  left: -100%;}
.carousel-inner > .active.right {  left: 100%;}
.carousel-control {position:absolute;top:41%;left:12px;width:32px;height:60px; border: none;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/view_new_icon.png) no-repeat -13px 0;opacity: inherit;}
.carousel-control.right{right:12px;left:auto; background-position:-51px 0;}
.xqjj_num{ position:absolute;top:15px;right:22px;color:#fff;font-size:24px;}
.xqjj_num_bg{width:62px;height:34px;position:absolute;top:11px;right:11px; background-color:rgba(0,0,0,.5);border-radius:15px;}
.xqjj_item_bg{width:100%;height:34px; position:absolute;bottom:0;left:0;background-color:rgba(0,0,0,.5);}
.xq_imgs p{position:absolute;bottom:0;left: 12px;color:#fff;line-height:34px;/* display:  none; */text-align:right;font-size:14px;}
/*项目特色*/
.vfloat_yh span{ position:absolute;width:20px;height:20px;right:13px;bottom:8px;}
.vfloat_yh{ position:fixed;left:50%;margin-left:460px;top:310px; z-index:99;display:none;}
.view_xmts{ position:relative;}
.right .view_xmts span{width: 46px;height: 20px;padding:0 !important;margin-right: 6px !important;cursor:pointer;background-color:#eb6877;line-height: 20px;text-align:center;margin-top: 4px;}
.view_desc{position:absolute;top:45px;left:0;}
.info li .view_ts1{
	position:  relative;

	background-color: #eff3fd !important;color: #668ae9 !important;
}
.view_ts2{background-color: #fff0f1 !important;color: #f7081a !important;cursor: context-menu!important;}
.view_ts3{background-color: rgba(192, 182, 255, 0.25) !important;color: #8a77f9 !important;}
.view_ts4{  background: #fef4e5 !important;
	color:  #ffc001 !important;}

.miaoshu{position:absolute;width: 205px;padding:5px 10px;border:#8cb43c solid 1px;display:none;background-color:#fff;line-height:28px;margin-top: 14px;color: #333;text-align: left;z-index: 999;}
.dt_ms{left: 0px;border-color: #ededed;}
.xf_ms{left:155px;border-color:#8cb43c;}
.dsf_ms{/* left: 177px; */border-color: #ededed;}
.pp_ms{/* left:325px; */border-color: #ededed;}
.ms_sanjiao{width:22px;height:11px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/view_ts_icon.jpg) no-repeat;margin-top: -13px;position: absolute;left: 35px;top: 5px;}
.xf_sj{ background-position: 0 -11px;}
.dsf_sj{}
.pp_sj{}

/* 项目问答 */
.newHouseViewChunk .housesLeft{
	width: 1170px;
	padding:  0;
	border:  none;
	border-bottom: 1px solid #ededed;
}
.newHouseViewChunk .housesLeft .title{
	padding: 0 20px;
}
.newHouseViewChunk .housesLeft .title p{
	padding:  0;
	padding-left: 30px;
}
.titleAskbtn{
	overflow: hidden;
	margin-right: 30px;
	width:  95%;
	margin: 0 auto;
	border-bottom:  1px solid #ededed;
	padding: 20px 0;
	line-height:  30px;
}
.titleAskbtn>span{
	width: 130px;
	height: 30px;
	background-color: #ff6100;
	display: block;
	font-size: 14px;
	float: right;
	color: #fff;
	border: none;
	cursor: pointer;
	line-height: 30px;
	text-align: center;
	border-radius: 2px;
	margin-top: 5px;
}
.newHouseViewChunk .housesLeft .seeMoreAsk{
	float:  right;
	color: #666;
	margin-top:  0;
	margin-right: 10px;
}
.newHouseViewChunk .titleAskbtn p{
	float:  right;
	line-height: 41px;
	margin-right: 15px;
}
.newHouseViewChunk .titleAskbtn>div{}

.newHouseViewChunk .question_r{
	border-left:  none;
	width: 100%;
	padding-bottom:  20px;
	border-top:  1px solid #ededed;
	margin-top:  30px;
	padding-top: 10px;
	padding-left: 94px;
}
.newHouseViewChunk .referesult-title a{
	color: #666;
	font-size:14px;
	float:right;
	margin-right: -154px;
	margin-top: -70px;
}
.newHouseViewChunk .nmaptitle {
	height: 40px;
	line-height: 40px;
	margin-bottom: 10px;
	padding-right: 20px;
}
.newHouseViewChunk .nmaptitleleft {
	background: none;
	height: 50px;
	margin: 5px auto;
}
.map{position:relative}
.dummyMap{position:absolute;top:-150px}
.newHouseViewChunk .nmaptitleleft p{
	border-bottom: none;
	color: #333;
}
.newHouseViewChunk #online{
	position:  relative;
}
.newHouseViewChunk .zxxf_r .more{
	position:  absolute;
	right:  0;
	top: 7px;
	color:  #666;
}

.newHouseViewChunk .hj-col-lg-22{border:none}

/* 销售动态 */
.houseSaleDT{
	width:  1170px;
	margin: 0 auto;
	overflow:  hidden;
	margin-bottom: 30px;
}
.houseSaleDT .normalHouseMain{
	width:  900px;
	border: 1px solid #ededed;
	float:  left;
	height: 270px;
}
.houseSaleDT .normalHouseMain .xstd{
	width:  890px;
	padding-top: 6px;
}
.houseSaleDT .normalHouselpDy{
	width: 228px;
	float:  right;
	overflow: hidden;
	border:  1px solid #ededed;
	padding: 0 10px;
	height:  270px;
}
/* .houseSaleDT  .houseRight{width: 210px;position: inherit;height: 270px;color:  #333;background:  #fff;} */
.houseSaleDT  .houseRight ul{
	overflow: hidden;
	padding-bottom: 12px;
}
.houseSaleDT  .houseRight ul li{
	overflow: hidden;
	float: left;
	line-height: 40px;
	width: 50%;
}
.houseSaleDT .houseRight ul li b{
	display: inline-block;
	width: 20px;
	height: 20px;
	border:  1px solid #ededed;
	box-sizing: border-box;
	float: left;
	margin-top: 10px;
	margin-right: 10px;
	cursor: pointer;
}
.houseSaleDT  .houseRight ul li.hover b{
	background: url(https://static.fangxiaoer.com/web/images/ico/sign/select.png)no-repeat;
	border:  none;
}
.houseSaleDT .houseRight ul li span{}
.houseSaleDT>.houseRight {
	margin-bottom: 20px;
	padding: 0 20px;
	overflow: hidden;
}
.houseSaleDT  .houseRight .houseRinghtTitle{
	font-size: 18px;
	color: #333333;
	line-height: 59px;
	border-bottom: 1px solid #ededed;
	margin-bottom: 20px;
	padding-left: 10px;
}
.houseSaleDT  .houseRight>a{
	background: #ff6100;
	color: #fff;
	display: block;
	text-align: center;
	font-size: 14px;
	line-height: 30px;
	border-radius: 2px;
	margin-bottom: 20px;
	width:  200px;
	margin:  0 auto;
}
.houseSaleDT  .houseRight ul{
	margin-top: -10px;
	width: 88%;
	margin: 13px auto 0 auto;
}
.houseSaleDT  .houseRight ul li{
	height: 40px;
	overflow: hidden;
}
.houseSaleDT  .houseRight ul li a>p{
	line-height: 36px;
}
.houseSaleDT  .houseRight ul li a>p b{
	font-weight: 400;
}
.houseSaleDT  .houseRight ul li a>p span{
	float: right;
	color: #ff5200;
}
.houseSaleDT  .houseRight ul li a>div{
	/* display: none; */
	padding: 12px 0;
}
.houseSaleDT .houseRight ul li a>div img{
	width: 90px;
	float: left;
}
.houseSaleDT .houseRight ul li a>div>div{

	float: left;

	margin-left: 10px;

	width: 105px;
}
.houseSaleDT  .houseRight ul li a>div h3{
	font-size: 14px;
	font-weight: 400;
	white-space:  nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.houseSaleDT .houseRight ul li a>div h4{
	font-size: 14px;
	font-weight: 400;
	color: #999;
}
.houseSaleDT .houseRight ul li a>div h5{
	font-size: 14px;
	font-weight: 400;
	color: #ff5200;
}
.housesRight .houseRight ul li.hover{
	height:auto;
}
.houseSaleDT .houseRight ul li.hover a p{
	display:none;
}

.newHouseViewChunk .remark_btn a {
	width: 130px;
	height: 30px;
	background-color: #ff6100;
	display: block;
	font-size: 14px;
	float: right;
	color: #fff;
	border: none;
	cursor: pointer;
	line-height: 30px;
	text-align: center;
	border-radius: 2px;
	margin-top: 5px;
}
.footer{margin-top:0}
.info li{ position: relative}
.info li .layer_sun{padding-left:15px;position:absolute;left: 120px;top:40px;line-height:50px;background-color:#fff;box-shadow: 0.5px 0.866px 10px 0px rgba( 0, 0, 0 ,0.3);
	padding-left:17px;padding-right:17px;z-index:99;max-width: 350px;display: none;}
.info li:hover .layer_sun{display: block;}
.box_sun{ float:left}
.pro_name .type_sun{ width:60px; height:26px; display:block; float:left; font-size:14px;  line-height:26px;; text-align:center;

	color:#fff;border-radius:3px
}
.pro_name .wait_1{background-color:#ff6100;box-shadow: 1px 1.732px 5px 0px rgba( 254, 97, 16,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/Now.png)}
.pro_name .wait_2{background-color:#fe9c02;box-shadow: 1px 1.732px 5px 0px rgba( 254, 156, 2,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/wait.png)}
.pro_name .wait_3{background-color:#8099af;box-shadow: 1px 1.732px 5px 0px rgba( 128, 153, 175,0.3 );background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/sell_out.png)}

.huxing ul li h4 span{ display:block; font-size:14px; width:52px; height:23px; float:left; text-align:center;color:#fff; margin-left:10px;}
.huxing ul li h4 a{display:block;float:left}
.huxing ul li h4 .wait_1{background-color:#ff6100;}
.huxing ul li h4 .wait_2{background-color:#fe9c02;}
.huxing ul li h4 .wait_3{background-color:#8099af;}
.newHouseviewPz{ margin-top:20px}

.normalHouseMain li p:hover {color:#ff5200}

.player_btn{position:absolute;left: 43px;top: 15px;width: 30px !important;height: auto !important;}

.video-js .vjs-big-play-button .vjs-icon-placeholder:before,  .vjs-icon-play:before{
	content:  none !important;
}
.video-js .vjs-big-play-button{background:none !important;width:100px !important;
	height:100px !important;margin-top:-50px !important; margin-left:-50px !important;
	border-radius:50% !important;background-image:url(https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png)!important;background-size:100% !important}
.infoIcontime table th{background-color:#f5f5f5;font-size:16px;height: 34px;padding-left: 10px;}
.infoIcontime table{box-shadow: 0px 0px 10px rgba( 119, 114, 114,0.5 );}

#qrcode{ position:relative}
#qrcode .layer_wei{position:absolute;left:0;top:0;width:100%;height:100%;
	background-color:rgba(255,255,255,0.85)}
#qrcode .layer_wei h2{font-size:12px;font-weight:normal;text-align:center;line-height: 14px;padding-top: 5px;}
#qrcode .layer_wei h1{font-size:12px;font-weight:normal;text-align:center;line-height: 14px;padding-top: 15px;}
#qrcode .layer_wei{display:none}
#qrcode:hover .layer_wei{display:block; cursor:pointer}

.sec-input{margin-bottom:20px}
.seemore{border:0 !important;color:#ff5200 !important;font-weight: bold !important;}
.pinpai{/* background-color:#fd4032; */line-height:38px;/* display:inline-block; */width:266px;margin-right: 24px;color: #333333!important;/* box-shadow: 1.5px 2.598px 5px 0px rgb( 255, 30, 28 ); */border-radius:0 19px 19px 0;background-image: url(https://static.fangxiaoer.com/web/images/Housekeeper/more.png);background-repeat: no-repeat;background-position: 246px center;}
.newHouseInfoRicn label{padding-left:15px;display:inline-block;padding-right:13px;
	border-right:1px solid #fff;line-height:12px;margin-right:14px}
.Sales{float:left;padding-top: 8px;position: relative;}
.Sales h4{font-size:12px;font-weight:normal;line-height:22px;color:#999999;padding-right: 5px;padding-left: 23px;border: 1px solid #fff;background-image: url(https://static.fangxiaoer.com/web/images/ico/Sales.png);background-repeat: no-repeat;background-position: 4px center;cursor: pointer;}
.Sales h4:hover{border: 1px solid #ebebeb;}
h4 {}

h4 {}

.Sales .layer_sun{padding-left:15px;position:absolute;right: 0px;top:40px;line-height:50px;background-color:#fff;box-shadow: 0.5px 0.866px 10px 0px rgba( 0, 0, 0 ,0.3);padding-left:17px;padding-right:17px;z-index:99;width: auto !important;display: none;white-space: nowrap;font-size: 14px;font-weight: normal;color: #333;}
.Sales:hover .layer_sun{display: block;}
.Sales .ms_sanjiao{width:22px;height:11px;background:url(https://static.fangxiaoer.com/web/images/sy/house/view/view_ts_icon.jpg) no-repeat;margin-top: -13px;position: absolute;right: 35px;top: 5px;left: auto;}

.newHouseInfoRicn .seemore{
	padding-top: 10px !important;
	margin-left: 0px !important;
}


/*牌榜*/
.iconDiv {
	width: 360px;
	height: 36px;
	float: left;
	margin-top: 4px;
	background-color: #f2f3f5;
	vertical-align: middle;
}
.barnd_icon {
	display: inline-block;
	width: 26px;
	height: 24px;
	margin-bottom: -6px;
	margin-left: 6px;
	margin-right: 10px;
	background-image: url(https://static.fangxiaoer.com/web/images/brand/brand.svg);
}
.rank_icon {
	display: inline-block;
	width: 26px;
	height: 24px;
	margin-right: 10px;
	margin-bottom: -6px;
	background-image: url(https://static.fangxiaoer.com/web/images/brand/rank.svg);
}


/*看房团*/
.houseKF{
	width: 1170px;
	margin: 0 auto;
	margin-top: 24px;
	border: 1px solid #ededed;
	margin-bottom: 30px;
}
.checking{
	height: 60px;
	/* margin: 17px auto 0px; */
	border-bottom: 1px solid #ededed;
	padding-left: 31px;
}
.checking p {
	font-size: 20px;
	color: #333;
	/* border-bottom: 5px solid #ff6600; */
	float: left;
	/* padding: 0 16px; */
	line-height: 60px;
	margin-right: 32px;
}
.checking .more{font-size:12px;margin-right: 30px;    line-height: 60px;
	color: #666;}
.checking .more:hover{
	color:#ff5200;
	cursor:pointer;

}
.KFcontent{
	height: 132px;
}
.imgCar{
	width: 120px;
	height: 130px;
	vertical-align: middle;
}
.imgCar img{
	/* vertical-align: middle; */
	margin-left: 35px;
	margin-top: 40px;
}
.KFcontent ul>li{
	float:left;
}
.KFtimeH1{
	margin-top: 38px;
	font-size: 20px;
	color: #333333;
	margin-bottom: 11px;
	margin-left: -8px;
}
.KFtimeH1 span{
}
.KFdetails{
	font-size: 14px;
	color: #333333;
	font-weight: bold;
}
.KFdetails span{
	color: #666666;
	font-weight: 400;
}
.KFdetailsli{
	margin-right: 18px;
}
.KFdetailsli span{
	margin-right: 10px;
}
.KFdetailsli2{
	margin-right: 28px;
}
.KFdetailsli3 span{
	margin-right: 20px;
}
.KFtimeLi2 a{
	display: block;
	width: 150px;
	height: 30px;
	color: #ffffff;
	text-align: center;
	background-color: #ff6100;
	line-height: 30px;
	font-size: 14px;
	margin-top: 36px;
}
.KFtimeLi2{
	float:right !important;
	width:190px

}
.KFtimeLi2 span{
	width: 150px;
	text-align:center;
	display: block;
	color: #666666;
	font-size: 14px;
	margin-top: 10px;
}
.KFtimeLi2 span b {
	color: #ff5200;
	/* margin-top: 10px; */
}