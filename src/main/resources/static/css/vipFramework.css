
div {
	margin: 0px; padding: 0px;
}
dl {
	margin: 0px; padding: 0px;
}
dt {
	margin: 0px; padding: 0px;
}
dd {
	margin: 0px; padding: 0px;
}
ul {
	margin: 0px; padding: 0px;
}
ol {
	margin: 0px; padding: 0px;
}
li {
	margin: 0px; padding: 0px;
}
h1 {
	margin: 0px; padding: 0px;
}
h2 {
	margin: 0px; padding: 0px;
}
h3 {
	margin: 0px; padding: 0px;
}
h4 {
	margin: 0px; padding: 0px;
}
h5 {
	margin: 0px; padding: 0px;
}
h6 {
	margin: 0px; padding: 0px;
}
pre {
	margin: 0px; padding: 0px;
}
code {
	margin: 0px; padding: 0px;
}
form {
	margin: 0px; padding: 0px;
}
fieldset {
	margin: 0px; padding: 0px;
}
legend {
	margin: 0px; padding: 0px;
}
input {
	margin: 0px; padding: 0px;
}
button {
	margin: 0px; padding: 0px;
}
textarea {
	margin: 0px; padding: 0px;
}
p {
	margin: 0px; padding: 0px;
}
blockquote {
	margin: 0px; padding: 0px;
}
th {
	margin: 0px; padding: 0px;
}
td {
	margin: 0px;
	padding: 0px;
	height:50px;

}
.jczl table{border: 1px solid #ededed}
.jczl table td{/* border: 1px solid #ededed; */}
.odd{padding-left:15px;border: 1px solid #ededed; */}
.even{background-color:#f5f5f5;text-align:center;border: 1px solid #f5f5f5;}

.hj-bg-gray {
	background: rgb(238, 238, 238);
}
.hj-bg-white {
	background: rgb(255, 255, 255);
}
.hj-bg-ashen {
	background: rgb(248, 248, 248);
}
.no-height {
	height: auto !important;
}
.hj-fl {
	float: left !important; display: inline-block;
}
.hj-fr {
	float: right !important; display: inline-block;
}
.hj-clearf {
	clear: both; display: block;
}
.hj-clearf::after {
	width: 0px; clear: both; font-size: 0px; display: block; content: "";
}
.hide {
	display: none;
}
.hj-lg-hidden {
	display: none;
}
li {
	list-style: none;
}
a {
	text-decoration: none;
}
a:link {
	text-decoration: none;
}
a:hover {
	text-decoration: none;
}
i {
	font-style: normal;
	color: #eaeaea;
}
em {
	font-style: normal;
}
address {
	font-style: normal;
}
input:focus {

}
textarea:focus {

}
button:focus {

}
input[type=text] {
	font-family: "Microsoft YaHei","Heiti SC","Droidsansfallback","Helvetica","monospace", "sans-serif", "serif";
}
textarea {
	font-family: "Microsoft YaHei","Heiti SC","Droidsansfallback","Helvetica","monospace", "sans-serif", "serif";
}
input {
	border: currentColor; border-image: none;
}
button {
	border: currentColor; border-image: none;
}
#alertDom a:hover{color:#fff;}
img {
	border: currentColor; border-image: none;
}
textarea {
	resize: none;
}
.hj-text-gray-deep {
	color: rgb(51, 51, 51) !important;
}
.hj-text-gray {
	color: rgb(102, 102, 102) !important;
}
.hj-text-gray-shallow {
	color: rgb(153, 153, 153) !important;
}
.hj-text-red-shallow {
	color: rgb(218, 92, 79) !important;
}
.hj-text-red {
	color: rgb(255, 51, 51) !important;
}
.hj-text-red-std {
	color: rgb(255, 0, 0) !important;
}
.hj-text-green {
	color: rgb(98, 171, 0) !important;
}
.hj-text-blue {
	color: rgb(53, 158, 209) !important;
}
.hj-text-orange {
	color: rgb(253, 149, 0) !important;
}
.hj-link-gray-deep {
	color: rgb(51, 51, 51);
}
.hj-link-gray-deep:hover {
	color: rgb(218, 92, 79);
}
.hj-link-gray {
	color: rgb(102, 102, 102);
}
.hj-link-gray:hover {
	color: rgb(218, 92, 79);
}
.hj-link-red {
	color: rgb(218, 92, 79);
}
.hj-link-red:hover {
	text-decoration: underline;
}
.hj-text-center {
	text-align: center !important;
}
.hj-text-left {
	text-align: left !important;
}
.hj-text-right {
	text-align: right !important;
}
.hj-btn-xxs {
	width: 60px; height: 26px; line-height: 26px; font-size: 14px;
}
.hj-btn-xs {
	width: 110px; height: 36px; line-height: 36px; font-size: 16px;
}
.hj-btn-sm {
	width: 100px; height: 42px; line-height: 42px; font-size: 16px;
}
.hj-btn-lg {
	width: 150px; height: 52px; line-height: 52px; font-size: 18px;
}
.hj-btn-red {
	background:#ff6600;width:147px;height:50px; color:#fff; font-size:16px; border-radius:3px; font-family:"??¨°n????o??";position: absolute;top: -205px;left: 310px;z-index: 1; cursor:pointer; outline:medium}
.hj-btn-red:hover {
	background: #f26100
}
.hj-btn-gray {
	background: rgb(204, 204, 204);
}
.hj-btn-gray:hover {
	background: rgb(204, 204, 204);
}
.hj-btn-yellow {
	background: rgb(254, 213, 84); color: rgb(171, 43, 45);
}
.hj-btn-yellow:hover {
	background: rgb(254, 221, 118);
}
.hj-btn img {
	margin-right: 5px;
}
.hj-btn-little {
	padding: 0px 30px; height: 40px; line-height: 40px; font-size: 16px;
}
.hj-btn-gray-deep {
	background: rgb(166, 166, 166);
}
.hj-btn-seagreen {
	background: rgb(53, 184, 174);
}
.hj-backtop {
	background: url("../image/backTop.png") no-repeat center; width: 60px; height: 60px; display: block;
}
.hj-backtop:hover {
	background: url("../image/backTop-hover.png") no-repeat center;
}
.hj-tag {
	font-weight: normal; display: inline-block;
}
.hj-tag-sm {
	padding: 0px 6px; line-height: 21px; font-size: 12px;
}
.hj-tag-md {
	padding: 0px 10px; line-height: 23px; font-size: 12px;
}
.hj-tag-red {
	background: rgb(218, 92, 79); color: rgb(255, 255, 255);
}
.hj-tag-group .hj-tag {
	margin: 0px 10px 10px 0px;
}
.hj-paging {
	padding: 15px 0px; overflow: hidden; clear: both;
}
.hj-paging a {
	background: rgb(243, 243, 243); border: 1px solid rgb(221, 221, 221); border-image: none; text-align: center; color: rgb(102, 102, 102); text-decoration: none; display: inline-block;
}
.hj-paging a:hover {
	background: rgb(218, 92, 79); border: 1px solid rgb(218, 92, 79); border-image: none; color: rgb(255, 255, 255);
}
.hj-paging .hj-total {
	color: rgb(102, 102, 102);
}
.hj-paging form {
	color: rgb(102, 102, 102); display: inline-block; _display: inline;
}
.hj-paging form input {
	background: rgb(255, 255, 255); border: 1px solid rgb(221, 221, 221); border-image: none; text-align: center; color: rgb(102, 102, 102);
}
.hj-paging form input:focus {
	border: 1px solid rgb(218, 92, 79); border-image: none;
}
.hj-paging form button {
	background: rgb(243, 243, 243); border: 1px solid rgb(221, 221, 221); border-image: none; text-align: center; color: rgb(102, 102, 102); cursor: pointer;
}
.hj-paging form button:hover {
	background: rgb(218, 92, 79); border-color: rgb(218, 92, 79); color: rgb(255, 255, 255);
}
.hj-paging-lg a {
	background: rgb(255, 255, 255); width: 38px; height: 38px; line-height: 38px; font-size: 14px; margin-left: 5px;
}
.hj-paging-lg span {
	margin-left: 5px;
}
.hj-paging-lg a.nextprev {
	padding: 0px 10px; width: auto;
}
.hj-paging-lg span.nextprev {
	background: rgb(251, 251, 251); padding: 0px 10px; border: 1px solid rgb(238, 238, 238); border-image: none; width: auto; height: 38px; text-align: center; color: rgb(170, 170, 170); line-height: 38px; font-size: 14px; margin-left: 5px; display: inline-block;
}
.hj-paging-lg span.current {
	background: rgb(218, 92, 79); width: 40px; height: 40px; text-align: center; color: rgb(255, 255, 255); line-height: 38px; font-size: 14px; margin-left: 5px; vertical-align: 1px; display: inline-block;
}
.hj-paging-lg .hj-prev {
	padding: 0px 20px; width: auto;
}
.hj-paging-lg .hj-next {
	padding: 0px 20px; width: auto;
}
.hj-paging-lg .hj-prev-disabled {
	padding: 0px 20px; width: auto;
}
.hj-paging-lg .hj-prev {
	background: url("../image/prev-enabled.png") no-repeat 10px;
}
.hj-paging-lg .hj-prev:hover {
	background: url("../image/prev-hover.png") no-repeat 10px rgb(218, 92, 79); color: rgb(255, 255, 255);
}
.hj-paging-lg .hj-next {
	background: url("../image/next-enabled.png") no-repeat 10px;
}
.hj-paging-lg .hj-next:hover {
	background: url("../image/next-hover.png") no-repeat 10px rgb(218, 92, 79); color: rgb(255, 255, 255);
}
.hj-paging-lg .hj-prev-disabled {
	background: url("../image/prev-disabled.png") no-repeat 10px;
}
.hj-paging-lg .hj-prev-disabled:hover {
	background: url("../image/prev-disabled.png") no-repeat 10px; border-color: rgb(221, 221, 221); color: rgb(102, 102, 102);
}
.hj-paging-lg .hj-current {
	background: rgb(255, 255, 255); border: currentColor; border-image: none; width: 39px; height: 39px; color: rgb(218, 92, 79);
}
.hj-paging-lg .hj-more {
	background: rgb(255, 255, 255); border: currentColor; border-image: none; width: 39px; height: 39px; color: rgb(218, 92, 79);
}
.hj-paging-lg .hj-current:hover {
	background: rgb(255, 255, 255); border: currentColor; border-image: none; color: rgb(218, 92, 79);
}
.hj-paging-lg .hj-more:hover {
	background: rgb(255, 255, 255); border: currentColor; border-image: none; color: rgb(218, 92, 79);
}
.hj-paging-lg .hj-more {
	color: rgb(102, 102, 102);
}
.hj-paging-lg .hj-more:hover {
	color: rgb(102, 102, 102);
}
.hj-paging-lg .hj-total {
	padding: 0px 10px; line-height: 39px;
}
.hj-paging-lg form input {
	width: 38px; height: 38px; line-height: 38px; font-size: 14px; margin-right: 5px;
}
.hj-paging-lg form button {
	padding: 0px 15px; height: 38px; line-height: 38px; font-size: 14px; margin-left: 5px;
}
.hj-paging-sm a {
	width: 22px; height: 22px; line-height: 22px; font-size: 12px;
}
.hj-paging-sm .hj-more {
	background: rgb(255, 255, 255); border-color: rgb(255, 255, 255);
}
.hj-paging-sm .hj-more:hover {
	background: rgb(255, 255, 255); border-color: rgb(255, 255, 255); color: rgb(102, 102, 102);
}
.hj-paging-sm .hj-prev {
	padding: 0px 10px; width: auto;
}
.hj-paging-sm .hj-next {
	padding: 0px 10px; width: auto;
}
.hj-paging-sm .hj-prev-disabled {
	padding: 0px 10px; width: auto;
}
.hj-paging-sm .hj-prev-disabled {
	background: rgb(255, 255, 255); color: rgb(204, 204, 204);
}
.hj-paging-sm .hj-prev-disabled:hover {
	background: rgb(255, 255, 255); border-color: rgb(221, 221, 221); color: rgb(204, 204, 204);
}
.hj-paging-sm .hj-current {
	background: rgb(218, 92, 79); border: 1px solid rgb(218, 92, 79); border-image: none; color: rgb(255, 255, 255);
}
.hj-crumb {
	padding: 15px 0px; color: rgb(153, 153, 153); overflow: hidden; clear: both; font-size: 14px;
}
.hj-crumb a {
	color: rgb(102, 102, 102);
}
.hj-crumb a:hover {
	color: rgb(218, 92, 79);
}
.hj-crumb .hj-arrow {
	padding: 0px 5px;
}
.hj-tab {
	clear: both;
}
.hj-tab .hj-tab-heading {
	height: 38px; border-bottom-color: rgb(221, 221, 221); border-bottom-width: 1px; border-bottom-style: solid; position: relative;
}
.hj-tab .hj-tab-heading li {
	border-right-color: rgb(221, 221, 221); border-right-width: 1px; border-right-style: solid; float: left;
}
.hj-tab .hj-tab-heading li a {
	margin: 0px 20px; color: rgb(51, 51, 51); font-size: 14px;
}
.hj-tab .hj-tab-heading .hj-slider {
	left: 0px; width: 100%; height: 1px; bottom: -1px; position: absolute; z-index: 2;
}
.hj-tab .hj-tab-heading .hj-slider .hj-line {
	background: rgb(218, 92, 79); height: 1px; font-size: 0px; position: absolute; z-index: 3;
}
.hj-tab .hj-tab-body {
	padding: 10px;
}
.hj-tab2 {
	clear: both;
}
.hj-tab2 .hj-tab2-heading {
	padding: 10px 0px; overflow: hidden;
}
.hj-tab2 .hj-tab2-heading a {
	padding: 0px 15px; height: 30px; color: rgb(51, 51, 51); line-height: 30px; font-size: 14px; display: inline-block;
}
.hj-tab2 .hj-tab2-heading .hj-active {
	background: rgb(218, 92, 79); color: rgb(255, 255, 255);
}
.hj-tab2 .hj-tab2-heading .hj-label {
	color: rgb(51, 51, 51); font-size: 30px; vertical-align: -5px;
}
.hj-tab2 .hj-tab2-body {
	padding: 10px;
}
.hj-sortitle {
	background: rgb(255, 255, 255); height: 52px; line-height: 52px; overflow: hidden; clear: both; border-bottom-color: rgb(221, 221, 221); border-bottom-width: 3px; border-bottom-style: solid;
}
.hj-sortitle h2 {
	color: rgb(51, 51, 51); font-weight: normal; border-bottom-color: rgb(218, 92, 79); border-bottom-width: 3px; border-bottom-style: solid; float: left;
}
.hj-form-input {
	background: rgb(255, 255, 255); padding: 0px 5px 0px 37px; border: 1px solid rgb(204, 204, 204); border-image: none; height: 38px; color: rgb(102, 102, 102); line-height: 38px; font-size: 14px;
}
.hj-form-input:focus {
	border: 1px solid rgb(218, 92, 79); border-image: none;
}
.hj-form-textarea {
	background: rgb(255, 255, 255); padding: 5px; border: 1px solid rgb(204, 204, 204); border-image: none; width: 400px; color: rgb(102, 102, 102); font-size: 14px;
}
.hj-form-textarea:focus {
	border: 1px solid rgb(218, 92, 79); border-image: none;
}
.hj-user-icon {
	background: url("../image/user-icon.png") no-repeat 10px rgb(255, 255, 255);
}
.hj-mobile-icon {
	background: url("../image/mobile-icon.png") no-repeat 10px 12px rgb(255, 255, 255);
}
.hj-password-icon {
	background: url("../image/mobile-icon.png") no-repeat 10px -28px rgb(255, 255, 255);
}
.hj-pencil-icon {
	background: url("../image/mobile-icon.png") no-repeat 10px -69px rgb(255, 255, 255);
}
.hj-check-group {
	overflow: hidden; clear: both;
}
.hj-check-group::after {
	width: 0px; height: 0px; clear: both; font-size: 0px; display: block; content: "";
}
.hj-check-group .hj-check-item {
	margin-right: 10px; display: inline-block;
}
.hj-check-group .hj-check-item label {
	background: url("../image/check-icon.png") no-repeat center; width: 16px; height: 16px; vertical-align: -2px; display: inline-block; cursor: pointer;
}
.hj-check-group .hj-check-item .hj-on {
	background: url("../image/checked-icon.png") no-repeat center;
}
.hj-check-group .hj-check-item .hj-check-text {
	color: rgb(102, 102, 102); font-size: 14px;
}
.hj-check-group .hj-check-item input {
	display: none;
}
.hj-radio-group {
	overflow: hidden; clear: both;
}
.hj-radio-group::after {
	width: 0px; height: 0px; clear: both; font-size: 0px; display: block; content: "";
}
.hj-radio-group .hj-radio-item {
	margin-right: 10px; display: inline-block;
}
.hj-radio-group .hj-radio-item label {
	background: url("../image/radio-icon.png") no-repeat center; width: 13px; height: 13px; vertical-align: -2px; display: inline-block; cursor: pointer;
}
.hj-radio-group .hj-radio-item .hj-on {
	background: url("../image/radioed-icon.png") no-repeat center;
}
.hj-radio-group .hj-radio-item .hj-radio-text {
	color: rgb(102, 102, 102); font-size: 14px;
}
.hj-radio-group .hj-radio-item input {
	display: none;
}
.hj-select-group {
	clear: both;
}
.hj-select-group .hj-select-item {
	display: inline-block;
}
.hj-select-group .hj-select-item .hj-select-mask {
	border: 1px solid rgb(204, 204, 204); border-image: none; width: auto; position: relative;
}
.hj-select-group .hj-select-item .hj-select-mask .hj-select-text {
	color: rgb(102, 102, 102); padding-left: 5px; float: left;
}
.hj-select-group .hj-select-item .hj-select-mask .hj-select-arrow {
	float: left; display: inline-block; cursor: pointer;
}
.hj-select-group .hj-select-item .hj-select-mask .hj-select-list {
	border-width: 0px 1px 1px; border-style: none solid solid; border-color: currentColor rgb(204, 204, 204) rgb(204, 204, 204); border-image: none; display: none; position: absolute; z-index: 2;
}
.hj-select-group .hj-select-item .hj-select-mask .hj-select-list li {
	background: rgb(255, 255, 255); text-align: center; cursor: pointer;
}
.hj-select-group .hj-select-item .hj-select-mask .hj-select-list li a {
	color: rgb(102, 102, 102);
}
.hj-select-group .hj-select-item .hj-select-mask .hj-select-list li a:hover {
	color: rgb(218, 92, 79);
}
.hj-select-group .hj-select-item select {
	display: none;
}
.hj-select-group .hj-select-lg .hj-select-mask {
	width: 150px; height: 38px;
}
.hj-select-group .hj-select-lg .hj-select-mask .hj-select-text {
	width: 107px; height: 38px; line-height: 38px; font-size: 14px;
}
.hj-select-group .hj-select-lg .hj-select-mask .hj-select-arrow {
	background: url("../image/select-arrow.png") no-repeat center; width: 38px; height: 38px;
}
.hj-select-group .hj-select-lg .hj-select-mask .hj-select-list {
	left: -1px; top: 39px;
}
.hj-select-group .hj-select-lg .hj-select-mask .hj-select-list li {
	width: 150px; height: 38px; line-height: 38px;
}
.hj-select-group .hj-select-lg .hj-select-mask .hj-select-list li a {
	font-size: 14px;
}
.hj-select-group .hj-select-lg .hj-on {
	background: rgb(218, 92, 79); border-color: rgb(218, 92, 79);
}
.hj-select-group .hj-select-lg .hj-on .hj-select-text {
	color: rgb(255, 255, 255);
}
.hj-select-group .hj-select-lg .hj-on .hj-select-arrow {
	background: url("../image/select-arrow-focu.png") no-repeat center;
}
.hj-select-group .hj-select-sm .hj-select-mask {
	width: 65px; height: 28px;
}
.hj-select-group .hj-select-sm .hj-select-mask .hj-select-text {
	width: 32px; height: 28px; line-height: 28px; font-size: 12px;
}
.hj-select-group .hj-select-sm .hj-select-mask .hj-select-arrow {
	background: url("../image/select-arrow2.png") no-repeat center; width: 28px; height: 28px;
}
.hj-select-group .hj-select-sm .hj-select-mask .hj-select-list {
	left: -1px; top: 27px;
}
.hj-select-group .hj-select-sm .hj-select-mask .hj-select-list li {
	width: 65px; height: 28px; line-height: 28px;
}
.hj-select-group .hj-select-sm .hj-select-mask .hj-select-list li a {
	font-size: 12px;
}
.hj-form-search {
	height: 30px; clear: both; display: inline-block;
}
.hj-form-search .hj-select-group {
	float: left;
}
.hj-form-search .hj-input-box {
	background: rgb(255, 255, 255); padding: 0px 5px; width: 240px; height: 28px; color: rgb(102, 102, 102); line-height: 28px; border-top-color: rgb(204, 204, 204); border-bottom-color: rgb(204, 204, 204); border-top-width: 1px; border-bottom-width: 1px; border-top-style: solid; border-bottom-style: solid; float: left; display: block;
}
.hj-form-search .hj-submit-btn {
	background: rgb(238, 238, 238); padding: 0px 15px; border: 1px solid rgb(204, 204, 204); border-image: none; height: 30px; color: rgb(102, 102, 102); float: left; display: block; cursor: pointer;
}
.hj-form-search-active .hj-select-group .hj-select-mask {
	border-color: rgb(218, 92, 79);
}
.hj-form-search-active .hj-select-group .hj-select-mask .hj-select-list {
	border-color: rgb(218, 92, 79);
}
.hj-form-search-active .hj-input-box {
	border-color: rgb(218, 92, 79);
}
.hj-form-search-active .hj-submit-btn {
	background: rgb(218, 92, 79); border-color: rgb(218, 92, 79); color: rgb(255, 255, 255);
}
.hj-form-search2 {
	height: 34px; clear: both; display: inline-block;
}
.hj-form-search2 .hj-input-box {
	background: rgb(255, 255, 255); border-width: 1px 0px 1px 1px; border-style: solid none solid solid; border-color: rgb(221, 221, 221) currentColor rgb(221, 221, 221) rgb(221, 221, 221); border-image: none; width: 200px; height: 32px; color: rgb(102, 102, 102); padding-left: 10px; float: left;
}
.hj-form-search2 .hj-submit-btn {
	background: url("../image/zoom-icon.png") no-repeat center; border-width: 1px 1px 1px 0px; border-style: solid solid solid none; border-color: rgb(221, 221, 221) rgb(221, 221, 221) rgb(221, 221, 221) currentColor; border-image: none; width: 34px; height: 34px; float: left; cursor: pointer;
}
.hj-form-search2::after {
	width: 0px; clear: both; font-size: 0px; display: block; content: ""; hegiht: 0;
}
.hj-form-search2-active .hj-input-box {
	border-color: rgb(218, 92, 79);
}
.hj-form-search2-active .hj-submit-btn {
	border-color: rgb(218, 92, 79);
}
.hj-form-search2-active .hj-submit-btn {
	background: url("../image/zoom-icon-focu.png") no-repeat center;
}
.hj-progressbar {
	width: 380px; clear: both;
}
.hj-progressbar::after {
	width: 0px; height: 0px; clear: both; font-size: 0px; display: block; content: "";
}
.hj-progressbar .hj-min {
	width: 40px; color: rgb(102, 102, 102); margin-top: -5px;
}
.hj-progressbar .hj-max {
	width: 40px; color: rgb(102, 102, 102); margin-top: -5px;
}
.hj-progressbar .hj-min {
	text-align: center; float: left;
}
.hj-progressbar .hj-max {
	text-align: right; float: right;
}
.hj-progressbar .hj-progresslider {
	background: rgb(221, 221, 221); border-radius: 3px; width: 300px; height: 5px; float: left; position: relative; -webkit-border-radius: 3px; -moz-border-radius: 3px; -o-border-radius: 3px;
}
.hj-progressbar .hj-progresslider .hj-loaded {
	border-radius: 3px; left: 0px; top: 0px; height: 5px; position: absolute; z-index: 2; -webkit-border-radius: 3px; -moz-border-radius: 3px; -o-border-radius: 3px;
}
.hj-progressbar .hj-progresslider .hj-dot {
	top: -5px; width: 12px; height: 12px; position: absolute; z-index: 3; cursor: pointer;
}
.hj-progressbar .hj-progresslider .hj-count {
	padding: 0px 6px; border-radius: 3px; top: -40px; height: 22px; text-align: center; color: rgb(255, 255, 255); line-height: 22px; display: inline-block; position: absolute; z-index: 4; -webkit-border-radius: 3px; -moz-border-radius: 3px; -o-border-radius: 3px;
}
.hj-progressbar .hj-slider-blue .hj-dot {
	background: url("../image/dot-blue.png") no-repeat center;
}
.hj-progressbar .hj-slider-blue .hj-count {
	background: rgb(78, 143, 199);
}
.hj-progressbar .hj-slider-blue .hj-count .hj-count-arrow {
	border-width: 5px; border-style: solid; border-color: rgb(78, 143, 199) transparent transparent; margin: 0px auto; border-image: none; width: 0px; height: 0px; display: block;
}
.hj-progressbar .hj-slider-orange .hj-dot {
	background: url("../image/dot-orange.png") no-repeat center;
}
.hj-progressbar .hj-slider-orange .hj-loaded {
	background: rgb(255, 161, 47);
}
.hj-progressbar .hj-slider-orange .hj-count {
	background: rgb(255, 161, 47);
}
.hj-progressbar .hj-slider-orange .hj-count .hj-count-arrow {
	border-width: 5px; border-style: solid; border-color: rgb(255, 161, 47) transparent transparent; margin: 0px auto; border-image: none; width: 0px; height: 0px; display: block;
}
.range-tip {
	background: rgba(0, 0, 0, 0.5); padding: 8px; border-radius: 3px; left: 40%; color: rgb(255, 255, 255); position: absolute; z-index: 2; opacity: 0; -webkit-border-radius: 3px; -moz-border-radius: 3px; -o-border-radius: 3px;
}
.range-tip .arrow {
	border-width: 5px; border-style: solid; border-color: transparent transparent rgba(0, 0, 0, 0.5); margin: 0px auto; border-image: none; left: 48%; top: -10px; width: 0px; height: 0px; display: block; position: absolute; z-index: 3;
}
.FlipInY {
	animation:FlipInY 1s forwards; -moz-animation: FlipInY 1s forwards; -webkit-animation: FlipInY 1s forwards; -o-animation: FlipInY 1s forwards;
}
.hj-container {
	padding: 0px 10px; width: 1190px; clear: both; margin-right: auto; margin-left: auto;
}
.hj-container::after {
	width: 0px; height: 0px; clear: both; font-size: 0px; display: block; content: "";
}
.hj-row::after {
	width: 0px; height: 0px; clear: both; font-size: 0px; display: block; content: "";
}
.hj-col-sm-1 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-1 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-1 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-2 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-2 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-2 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-3 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-3 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-3 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-4 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-4 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-4 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-5 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-5 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-5 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-6 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-6 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-6 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-7 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-7 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-7 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-8 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-8 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-8 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-9 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-9 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-9 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-10 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-10 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-10 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-11 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-11 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-11 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-12 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-12 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-12 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-13 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-13 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-13 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-14 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-14 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-14 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-15 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-15 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-15 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-16 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-16 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-16 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-17 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-17 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-17 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-18 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-18 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-18 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-19 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-19 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-19 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-20 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-20 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-20 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-21 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-21 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-21 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-sm-22 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-md-22 {
	padding: 0px 10px; float: left; position: relative; min-height: 1px;
}
.hj-col-lg-1 {
	width: 35px;
}
.hj-col-lg-2 {
	width: 90px;
}
.hj-col-lg-3 {
	width: 145px;
}
.hj-col-lg-4 {
	width: 200px;
}
.hj-col-lg-5 {
	width: 255px;
}
.hj-col-lg-6 {
	width: 310px;
}
.hj-col-lg-7 {
	width: 365px;
}
.hj-col-lg-8 {
	width: 420px;
}
.hj-col-lg-9 {
	width: 475px;
}
.hj-col-lg-10 {
	width: 530px;
}
.hj-col-lg-11 {
	width: 585px;
}
.hj-col-lg-12 {
	width: 640px;
}
.hj-col-lg-13 {
	width: 695px;
}
.hj-col-lg-14 {
	width: 750px;
}
.hj-col-lg-15 {
	width: 805px;
}
.hj-col-lg-16 {
	width: 860px;
}
.hj-col-lg-17 {
	width: 915px;
}
.hj-col-lg-18 {
	width: 970px;
}
.hj-col-lg-19 {
	width: 1025px;
}
.hj-col-lg-20 {
	width: 1080px;
}
.hj-col-lg-21 {
	width: 1135px;
}
.hj-col-lg-22 {
	width: 1170px;
	border:1px solid #e6e6e6;
	position: relative;
}
@media screen and (max-width:1190px)
{
	.hj-container-md {
		padding: 0px 10px; width: 970px; clear: both; margin-right: auto; margin-left: auto;
	}
	.hj-container-md::after {
		width: 0px; height: 0px; clear: both; font-size: 0px; display: block; content: "";
	}
	.hj-md-hidden {
		display: none;
	}
	.hj-lg-hidden {
		display: block;
	}
	.hj-col-md-1 {
		width: 35px;
	}
	.hj-col-md-2 {
		width: 90px;
	}
	.hj-col-md-3 {
		width: 145px;
	}
	.hj-col-md-4 {
		width: 200px;
	}
	.hj-col-md-5 {
		width: 255px;
	}
	.hj-col-md-6 {
		width: 310px;
	}
	.hj-col-md-7 {
		width: 365px;
	}
	.hj-col-md-8 {
		width: 420px;
	}
	.hj-col-md-9 {
		width: 475px;
	}
	.hj-col-md-10 {
		width: 530px;
	}
	.hj-col-md-11 {
		width: 585px;
	}
	.hj-col-md-12 {
		width: 640px;
	}
	.hj-col-md-13 {
		width: 695px;
	}
	.hj-col-md-14 {
		width: 750px;
	}
	.hj-col-md-15 {
		width: 805px;
	}
	.hj-col-md-16 {
		width: 860px;
	}
	.hj-col-md-17 {
		width: 915px;
	}
	.hj-col-md-18 {
		width: 970px;
	}
}
.tgou-block {
	background: rgb(255, 255, 255); border: 1px solid rgb(221, 221, 221); border-image: none; width: auto; height: 424px; margin-bottom: 20px; position: relative;
}
.tgou-block img {
	width: 418px; height: 307px;
}
.tgou-block .summary {
	margin: 14px 15px; line-height: 22px; font-size: 14px;
}
.tgou-block .summary a {
	color: rgb(102, 102, 102);
}
.tgou-block .tgou-block-btm {
	padding: 0px 15px;
}
.tgou-block .tgou-block-btm h3 {
	color: rgb(218, 92, 79); font-size: 18px; font-weight: normal;
}
.tgou-block .tgou-block-btm h3 del {
	color: rgb(153, 153, 153); font-size: 12px; margin-left: 5px;
}
.tgou-block .tgou-block-btm .total {
	color: rgb(153, 153, 153); font-size: 14px;
}
.tgou-block .tgou-block-btm .total b {
	color: rgb(218, 92, 79); font-size: 16px;
}
.tgou-block .tgou-block-btm .join {
	right: 15px; bottom: 20px; display: none; position: absolute; z-index: 2;
}
.tgou-block:hover {
	background: rgb(218, 92, 79);
}
.tgou-block:hover .summary a {
	color: rgb(255, 255, 255);
}
.tgou-block:hover .tgou-block-btm h3 {
	color: rgb(255, 255, 255);
}
.tgou-block:hover .tgou-block-btm h3 del {
	color: rgb(255, 255, 255);
}
.tgou-block:hover .tgou-block-btm .total {
	display: none;
}
.tgou-block:hover .tgou-block-btm .join {
	display: block;
}
.tgou-block:hover img {
	border: 3px solid rgb(218, 92, 79); border-image: none; width: 412px; height: 301px;
}
.tgou-about {
	clear: both;
}
.tgou-about-item {
	padding: 15px 0px; border-bottom-color: rgb(221, 221, 221); border-bottom-width: 1px; border-bottom-style: dotted;
}
:last-child.tgou-about-item {
	border-bottom-color: currentColor; border-bottom-width: 0px; border-bottom-style: none;
}
.tgou-item-title {
	padding: 5px 0px; color: rgb(51, 51, 51); overflow: hidden; font-size: 14px;
}
.tgou-item-title span {
	color: rgb(153, 153, 153); margin-right: 5px;
}
.tgou-item-title .hj-fl a {
	color: rgb(51, 51, 51);
}
.tgou-item-title .hj-fl a:hover {
	color: rgb(218, 92, 79);
}
.tgou-item-title .hj-fr {
	color: rgb(218, 92, 79);
}
.tgou-item-body {
	font-size: 14px;
}
.tgou-item-body .privilege {
	margin: 5px 0px; color: rgb(153, 153, 153);
}
.tgou-item-body .privilege span {
	margin-right: 5px;
}
.tgou-item-body .join {
	overflow: hidden; margin-top: 6px; display: none; position: relative;
}
.tgou-item-body .join img {
	width: 120px; height: 80px; float: left;
}
.tgou-item-body .join .join-intro {
	width: 180px; float: right; -ms-word-wrap: break-word;
}
.tgou-item-body .join .join-intro p {
	margin-bottom: 5px;
}
.tgou-item-body .join .join-intro .text {
	color: rgb(153, 153, 153); line-height: 22px; margin-left: 5px;
}
.tgou-item-body .join .join-intro a {
	left: 130px; bottom: 0px; position: absolute; z-index: 2;
}
.active.tgou-about-item .privilege {
	display: none;
}
.active.tgou-about-item .join {
	display: block;
}
.tgou-sm-block {
	width: auto; clear: both;
}
.tgou-sm-block .imgText {
	width: 200px; height: 145px; position: relative;
}
.tgou-sm-block .imgText img {
	width: 200px; height: 145px;
}
.tgou-sm-block .imgText .text {
	background: rgba(0, 0, 0, 0.5); left: 0px; width: 100%; height: 26px; bottom: 0px; color: rgb(255, 255, 255); line-height: 26px; position: absolute; z-index: 2;
}
.tgou-sm-block .imgText .text span {
	margin: 0px 10px;
}
.tgou-sm-block .total {
	padding: 10px 0px; overflow: hidden; position: relative;
}
.tgou-sm-block .total .hj-fl {
	color: rgb(51, 51, 51); font-size: 14px;
}
.tgou-sm-block .total .hj-fr {
	color: rgb(153, 153, 153); font-size: 12px;
}
.tgou-sm-block .total .hj-fr b {
	color: rgb(218, 92, 79); font-weight: normal;
}
.tgou-sm-block .total a {
	top: 5px; right: 0px; display: none; position: absolute; z-index: 2;
}
.tgou-sm-block .condition-hide {
	padding: 10px 0px; color: rgb(153, 153, 153); clear: both; display: none;
}
.tgou-sm-block .condition-show {
	padding: 10px 0px; color: rgb(153, 153, 153); clear: both;
}
.tgou-sm-block:hover .total .hj-fr {
	display: none;
}
.tgou-sm-block:hover .total a {
	display: block;
}
.tgou-sm-block:hover .condition-hide {
	display: block;
}
.tgou-list {
	width: auto;
}
.tgou-list ul li {
	clear: both; font-size: 14px;
}
.tgou-list ul li .area {
	color: rgb(153, 153, 153); margin-right: 5px;
}
.tgou-list ul li .link {
	color: rgb(51, 51, 51);
}
.tgou-list ul li .link:hover {
	color: rgb(218, 92, 79);
}
.tgou-list ul li .payment {
	color: rgb(218, 92, 79); float: right;
}
.tgou-list ul li .rank {
	background: rgb(156, 156, 156); width: 20px; height: 20px; text-align: center; color: rgb(255, 255, 255); line-height: 20px; font-size: 14px; margin-top: 10px; margin-right: 5px; float: left; display: block;
}
.tgou-list ul li:nth-of-type(1) .rank {
	background: rgb(218, 92, 79);
}
.tgou-list ul li:nth-of-type(2) .rank {
	background: rgb(218, 92, 79);
}
.tgou-list ul li:nth-of-type(3) .rank {
	background: rgb(218, 92, 79);
}
.tgou-list .imgText {
	height: 80px; margin-left: 5px; float: left; display: none;
}
.tgou-list .imgText img {
	width: 120px; height: 80px; margin-right: 10px; float: left;
}
.tgou-list .imgText .text {
	line-height: normal !important; float: left; min-width: 150px;
}
.tgou-list .imgText .text h3 a {
	color: rgb(51, 51, 51); font-size: 14px; font-weight: normal;
}
.tgou-list .imgText .text p {
	margin: 5px 0px 0px; color: rgb(153, 153, 153);
}
.tgou-list .imgText .text-price {
	color: rgb(218, 92, 79);
}
.tgou-list ul .active .link {
	display: none;
}
.tgou-list ul .active .payment {
	display: none;
}
.tgou-list ul .active .imgText {
	display: block;
}
.tgou-list-border ul li {
	border-bottom-color: rgb(218, 218, 218); border-bottom-width: 1px; border-bottom-style: dotted;
}
.tgou-high ul li {
	height: 48px; line-height: 48px;
}
.tgou-middle ul li {
	height: 40px; line-height: 40px;
}
.tgou-low ul li {
	height: 34px; line-height: 34px;
}
.xuboxPageHtml {
	padding: 15px;
}
.xuboxPageHtml::after {
	width: 0px; height: 0px; clear: both; font-size: 0px; display: block; content: "";
}
#back-to-top {
	width: 60px; height: 60px; bottom: 86px; position: fixed; z-index: 999;
}
#back-to-top i {
	background: url("../image/backTop.png") no-repeat center; width: 60px; height: 60px; display: block;
}
#back-to-top:hover i {
	background: url("../image/backTop-hover.png") no-repeat center;
}
.hj-pop {
	padding: 0px 15px; width: 500px;
}
.hj-poptab {
	height: 36px; border-bottom-color: rgb(238, 238, 238); border-bottom-width: 2px; border-bottom-style: solid;
}
.hj-poptab li {
	float: left;
}
.hj-poptab li a {
	padding: 0px 20px; height: 36px; color: rgb(102, 102, 102); line-height: 36px; font-size: 16px; display: block;
}
.hj-poptab .hj-active a {
	color: rgb(51, 51, 51); border-bottom-color: rgb(218, 92, 79); border-bottom-width: 2px; border-bottom-style: solid;
}
.hj-popbody {
	padding: 40px 20px 0px;
}
.hj-popbody .hj-form-input {
	width: 234px; margin-bottom: 20px; margin-left: 91px; display: block;
}
.hj-popbody .hj-form-checkcode {
	background: rgb(255, 255, 255); padding: 0px 10px; border: 1px solid rgb(204, 204, 204); border-image: none; width: 121px; height: 38px; color: rgb(102, 102, 102); line-height: 38px; font-size: 14px; margin-bottom: 20px; margin-left: 91px;
}
.hj-popbody .hj-form-checkcode:focus {
	border: 1px solid rgb(218, 92, 79); border-image: none;
}
.hj-popbody .hj-get-btn {
	background: rgb(244, 244, 244); border: 1px solid rgb(204, 204, 204); border-image: none; width: 130px; height: 38px; text-align: center; color: rgb(102, 102, 102); line-height: 38px; vertical-align: top; display: inline-block;
}
.hj-popsubmit {
	margin-left: 91px;
}
.hj-form-subjoin {
	margin-left: 91px;
}
.hj-popsubmit .hj-text-blue {
	margin-left: 20px;
}
.hj-form-subjoin {
	margin-bottom: 20px;
}
.hj-form-subjoin label {
	color: rgb(102, 102, 102); font-size: 14px;
}
.hj-form-subjoin label i {
	margin: 0px 5px; color: rgb(218, 92, 79);
}
.hj-form-subjoin label span {
	background: url("../image/nh-operate.png") no-repeat -64px -62px; width: 12px; height: 12px; margin-right: 5px; vertical-align: -1px; display: inline-block;
}
.hj-form-subjoin label.on span {
	background: url("../image/nh-operate.png") no-repeat -64px -79px;
}
.hj-form-subjoin input {
	display: none;
}
.hj-popbody .hj-hint {
	color: rgb(51, 51, 51); line-height: 24px; font-size: 14px;
}
.hj-popbody .hj-hint span {
	color: rgb(153, 153, 153);
}
.hj-popbody .hj-hint i {
	color: rgb(218, 92, 79);
}
.hj-padding-0 {
	padding: 0px;
}
.hj-pt-20 {
	padding-top: 20px;
}
.hj-pt-10 {
	padding-top: 10px;
}
.hj-pb-20 {
	padding-bottom: 20px;
}
.hj-pb-10 {
	padding-bottom: 10px;
}
.hj-pl-20 {
	padding-left: 20px;
}
.hj-pl-10 {
	padding-left: 10px;
}
.hj-pr-20 {
	padding-right: 20px;
}
.hj-pr-10 {
	padding-right: 10px;
}
.hj-margin-0 {
	margin: 0px;
}
.hj-mt-30 {
	margin-top: 30px;
}
.hj-mt-20 {
	margin-top: 20px;
}
.hj-mt-15 {
	margin-top: 15px;
}
.hj-mt-10 {
	margin-top: 10px;
}
.hj-mb-20 {
	margin-bottom: 20px;
}
.hj-mb-15 {
	margin-bottom: 15px;
}
.hj-mb-10 {
	margin-bottom: 10px;
}
.hj-ml-20 {
	margin-left: 20px;
}
.hj-ml-15 {
	margin-left: 15px;
}
.hj-ml-10 {
	margin-left: 10px;
}
.hj-mr-20 {
	margin-right: 20px;
}
.hj-mr-15 {
	margin-right: 15px;
}
.hj-mr-10 {
	margin-right: 10px;
}
.mp-input {
	background: rgb(255, 255, 255); padding: 0px 5px; border: 1px solid rgb(204, 204, 204); border-image: none; display: inline-block;
}
.mp-input-md {
	width: 120px; height: 45px; color: rgb(51, 51, 51); font-size: 16px;
}
.jsq_l{width:499px;border-right:1px solid #e6e6e6;float:left;padding-left:10px; background:#fff}
.jsq_bg{position: absolute;top: -215px;left: 383px;}
.fieldt{font-size:20px;}
#average_month_pay{margin-left:10px; font:400 28px/28px "Arial"}
.result-tips{color: #999;font-size: 14px;position: absolute;top: 380px;left: 200px;width: 300px;}
.hj-row{background: rgb(249, 249, 249)}

.jczl table{border:1px solid #ededed;border-top:0px solid #ededed}
.jczl table tr td{ border-right:0px; border-bottom:0;}
.jczl  i{color:#eaeaea}
#group-dk-total{color:#000}

.voiceTabSy:hover {
	width: 80px!important;
	padding-left: 10px;
	background: #f5f5f5 url(https://sy.fangxiaoer.com/img/vrcode_aduio_hover.png);
	color: #ff5200!important;
	font-weight: 600;
	background-repeat: no-repeat;
	background-position: 9px;
	background-size: 9px;
	cursor: pointer
}

.vrcodeall {
	position: relative;
	width: 76px;
	height: 132px;
	background: #fff;
	padding: 12px 33px 0;
	left: 510px;
	z-index: 2;
	box-shadow: #d2d2d2 5px 5px 25px 0;
	font-weight: 600;
	overflow: initial!important;
	top: -2px;
}

.vrcodeall span {
	background: #fff;
	height: 23px!important;
	width: 23px!important;
	position: absolute;
	transform: rotate(45deg);
	margin-left: 0!important;
	left: -3px
}

.vrcodeall img {
	width: 100%;
	top: -128px;
	position: relative;
}

.vrcodeall i {
	color: #333;
	width: 84px;
	display: block;
	position: relative;
	margin: 0 auto;
	left: -4px;
	top: -122px;
	line-height: 17px;
	font-size: 12px;
	text-align: center;
}

.voiceTabSy {
	width: 80px!important;
	padding-left: 10px;
	background: #f5f5f5 url(https://sy.fangxiaoer.com/img/vrcode_aduio.png);
	color: #333!important;
	font-weight: 600;
	background-repeat: no-repeat;
	background-position: 9px;
	background-size: 9px;
	cursor: pointer
}
.barnd_icon{
	display: inline-block;
	width: 26px;
	height: 24px;
	margin-bottom: -6px;
	margin-left: 6px;
	margin-right: 10px;
	background-image: url(https://static.fangxiaoer.com/web/images/brand/brand.svg);
}
.rank_icon{
	display: inline-block;
	width: 26px;
	height: 24px;
	margin-right: 10px;
	margin-bottom: -6px;
	background-image: url(https://static.fangxiaoer.com/web/images/brand/rank.svg);
}
.iconDiv{width: 360px;height: 36px;float: left;margin-top: 4px;background-color: #f2f3f5;vertical-align: middle;}