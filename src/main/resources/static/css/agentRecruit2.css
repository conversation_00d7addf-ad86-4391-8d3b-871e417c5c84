@charset "utf-8";

/*
制作人：张琪
修改时间：2018-11-19
用处：sy经纪人招聘相关页面
*/

a:hover {
	text-decoration: none
}


/*经纪人招聘列表页*/

.logoBanner {
	width: 1170px;
	margin: 0 auto;
}

#option p {
	width: 60px;
	text-align: right;
}

.logoBanner .bigBanner {}

.logoBanner .bigBanner img {}

.logoBanner .smallBanner {
	overflow: hidden;
	margin: 10px auto;
}

.logoBanner .smallBanner li {
	float: left;
	width: 282px;
	height: 106px;
	border: 1px solid #f4f4f4;
	margin: 0 10px 10px 0;
	box-shadow: 3px 3px 12px #eee;
}

.logoBanner .smallBanner li:nth-child(4n) {
	margin-right: 0;
}

.logoBanner .smallBanner li a {
	width: 282px;
	height: 106px;
	display: block;
	overflow: hidden;
	box-shadow: 3px 4px 2px #eee;
}

.logoBanner .smallBanner li a img {
	width: 100%;
}


/*招聘列表左侧*/

.agentRecruitList {
	overflow: hidden;
	width: 1170px;
	margin: 0 auto;
	margin-top: 40px;
	margin-bottom: 20px;
}

.NoListMsg {
	width: 470px;
	display: block;
	margin: 20px 0 20px 20px;
	color: #999;
}

.agentRecruitList .arlLeft {
	width: 900px;
	float: left;
	border: 1px solid #eee;
	margin-bottom: 20px;
}

.agentRecruitList .arlLeft .arlLeftTitle {
	overflow: hidden;
	line-height: 46px;
	height: 46px;
	border-bottom: 1px solid #eee;
}

.agentRecruitList .arlLeft .arlLeftTitle p {
	float: left;
	font-size: 16px;
	color: #ff5200;
	display: block;
	width: 110px;
	text-align: center;
	border-right: 1px solid #eee;
}

.agentRecruitList .arlLeft .arlLeftTitle span {
	float: right;
	padding-right: 20px;
	color: #ff5200;
	font-size: 14px;
}

.agentRecruitList .arlLeft ul {
	padding-bottom: 20px;
}

.agentRecruitList .arlLeft ul li {
	overflow: hidden;
	border-bottom: 1px solid #eee;
	padding: 15px 18px;
}

.agentRecruitList .arlLeft ul li>h4>a {
	display: inline-block;
	float: left;
}

.agentRecruitList .arlLeft ul li>a:hover {
	color: #333
}

.agentRecruitList .arlLeft ul li:last-child {
	border-bottom: none
}

.liaobeiICon {
	display: inline-block;
	font-size: 14px;
	font-weight: normal;
	margin-right: 10px;
	color: #02a0db;
	float: right;
}

.liaobeiICon div {
	background: none;
	overflow: hidden;
}

.liaobeiICon span {
	display: block
}

.liaobeiICon:hover text {
	color: #ff5200
}

.liaobeiICon:hover .liaobeiIcon {
	background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue2.png) 0px 1px no-repeat;
}

.liaobeiICon a {}

.liaobeiICon .liaobeiIcon {
	height: 14px;
	width: 14px;
	display: inline-block;
	background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue2.png) 0px -14px no-repeat;
	float: left;
	margin-top: 1px;
	margin-right: 5px;
}


.agentRecruitList .arlLeft ul li .arlLeftLiL {
	width: 550px;
	float: left;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL>a {
	overflow: hidden;
	display: block;
}


/* .agentRecruitList .arlLeft ul li>div>a:hover,.agentRecruitList .arlLeft ul li .arlLeftLiL>a:hover{text-decoration:underline} */

.agentRecruitList .arlLeft a:hover {
	text-decoration: none
}

.agentRecruitList .arlLeft ul li .arlLeftLiL h4 {
	font-size: 16px;
	color: #333;
	font-weight: normal;
	overflow: hidden;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL span {
	float: left;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL span+i+span {
	width: 450px;
	overflow: hidden;
	white-space: pre;
	text-overflow: ellipsis;
	display: inline-block;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL i {
	float: left;
	padding: 0 5px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL p {
	color: #ff5200;
	line-height: 26px;
	margin-top: 5px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL p.fldy {
	color: #999;
	margin-top: 10px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiL p.fldy span {
	display: inline-block;
	padding: 0 6px;
	border: 1px solid #e2e2e2;
	margin-right: 10px;
	line-height: 24px;
	height: 22px;
	border-radius: 2px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiR {
	float: right;
	width: 300px;
	text-align: right;
	overflow: hidden;
	margin-top: -30px;
}

.agentRecruitList .arlLeft ul li .arlLeftLiR h3 {
	font-size: 14px;
	color: #333;
}

.agentRecruitList .arlLeft ul li .agentRecruitListp {
	color: #999;
	font-size: 14px;
	margin: 5px 0;
}

.agentRecruitList .arlLeft ul li .agentRecruitList span {}

.agentRecruitList .arlLeft ul li .agentRecruitList>i {}

.agentRecruitList .arlLeft ul li .agentRecruitList span i {}

.agentRecruitList .arlLeft ul li .arlLeftLiR .arlLeftBTn,
.arlLeftBTn {
	margin-top: 10px;
	display: block;
	height: 26px;
	line-height: 26px;
	width: 95px;
	float: right;
	border: 1px solid #ff5200;
	text-align: center;
	border-radius: 13px;
	color: #ff5200;
	cursor: pointer;
	text-decoration: none;
}

.bkNavFloat .arlLeftBTn {
	display: block;
	height: 26px;
	line-height: 26px;
	width: 95px;
	float: right;
	text-align: center;
	border-radius: 13px;
	color: #ff5200;
	cursor: pointer;
	text-decoration: none;
	background: #fff;
	margin-top: 0;
}

.agentRecruitList .arlLeft ul li:hover {
	background: #f6f6f6;
}

.agentRecruitList .arlLeft ul .arlLeftBTn:hover {
	background: #ff5200;
	color: #fff !important;
}

body>div>.bkNavFloat .arlLeftBTn:hover {
	background: #fff;
	color: #ff5200 !important;
}


/*招聘列表右侧*/

.agentRecruitList .arlRight {
	float: right;
	width: 250px;
}

.agentRecruitList .arlRight .urgentWant {
	border: 1px solid #eee;
	margin-bottom: 20px;
}

.agentRecruitList .arlRight .urgentWant h4 {
	padding: 14px 0 14px 12px;
	border-bottom: 1px solid #eee;
	font-size: 18px;
	font-weight: normal;
	color: #333;
}

.agentRecruitList .arlRight .urgentWant h4 i {
	display: block;
	width: 4px;
	height: 18px;
	background: #ff5200;
	float: left;
	margin-top: 3px;
	margin-right: 5px;
}

.agentRecruitList .arlRight .urgentWant li {
	padding: 15px 12px 10px 12px;
	border-bottom: 1px solid #eee;
}

.agentRecruitList .arlRight .urgentWant li a:hover,
.agentRecruitList .arlRight .urgentWant li a {
	text-decoration: none
}

.agentRecruitList .arlRight .urgentWant li:laat-child {
	border-bottom: none
}

.agentRecruitList .arlRight .urgentWant li h5 {
	white-space: pre;
	overflow: hidden;
	width: 220px;
	text-overflow: ellipsis;
	font-size: 14px;
	color: #333;
	font-weight: normal;
}

.agentRecruitList .arlRight .urgentWant li p {
	color: #ff5200;
}

.agentRecruitList .arlRight .urgentWant li p.agentRecruitList {
	width: 220px;
	color: #999;
	font-size: 12px;
	margin: 0;
}

.agentRecruitList .arlRight .urgentWant li p.agentRecruitList span {}

.agentRecruitList .arlRight .urgentWant li p.agentRecruitList>i {}

.agentRecruitList .arlRight .urgentWant li p.agentRecruitList span i {}

.agentRecruitList .arlRightBanner {
	margin-bottom: 20px;
}

.agentRecruitList .arlRightBanner a {
	display: block;
	width: 250px;
	/* height:  190px; */
}

.agentRecruitList .arlRightBanner a img {
	width: 100%;
	/* height: 100%; */
}


/*简历弹窗*/

.arlTcHb {
	display: none;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 9999;
	background-color: #000;
	opacity: 0.7;
}

.arlTcMain {
	display: none;
	width: 414px;
	/* height: 338px; */
	background: #fff;
	border-radius: 4px;
	position: fixed;
	left: 50%;
	top: 50%;
	margin-left: -227px;
	margin-top: -272px;
	padding: 20px;
	z-index: 9999;
}

.arlTcMain h4 {
	font-size: 16px;
	text-align: center;
	color: #333;
	margin-bottom: 20px;
}

.arlTcMain ul {}

.arlTcMain ul li {
	overflow: hidden;
	margin-bottom: 15px;
}

.arlTcMain ul li label {
	float: left;
	display: block;
	margin-right: 5px;
	width: 110px;
	text-align: right;
}

.selectpicker {
	width: 272px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #eaeaea;
	padding: 0 5px;
}

.selectpicker option {}

.arlTcMain ul li input {
	float: left;
	width: 265px;
	height: 26px;
	line-height: 28px;
	border: 1px solid #eaeaea;
	padding-left: 5px;
}

#workedUnits {
	width: 260px;
}

.arlTcMain ul li .select_box {
	width: 96px;
	height: 25px;
	float: left;
	margin: 9px 8px 0 8px;
	position: relative;
	text-align: left;
}

.arlTcMain ul li .select_info {
	background: url(https://static.fangxiaoer.com/web/images/sy/sale/list/sale_xl.gif) no-repeat;
	width: 73px;
	height: 25px;
	line-height: 25px;
	font-size: 12px;
	padding: 0 20px 0 7px;
	cursor: pointer;
}

.arlTcMain ul li .select_box ul {
	position: absolute;
	background: #fff;
	width: 94px;
	border: 1px solid #ccc !important;
	top: 23px;
	display: none;
	overflow-y: overlay;
	max-height: 200px;
}

.arlTcMain ul li .select_box ul li {
	height: 30px;
	line-height: 30px;
	border: none;
	/* padding-left: 6px; */
	cursor: pointer;
	font-size: 12px;
}

.arlTcMain ul li .select_box ul li a {
	width: 93%;
	height: 100%;
	display: block;
	padding-left: 6px;
}

.arlTcMain ul li .select_box ul li a:hover {
	background: #f5f5f5;
}

.arlTcMain ul li.select_box ul li:hover a {
	color: #333;
	text-decoration: none;
}

.arlTcMain ul li .select_box:hover ul {
	display: block
}

.arlTcMain ul li .my_xl {
	height: 26px;
	line-height: 26px;
	width: 110px;
	padding: 0px 6px;
	position: relative;
	font-size: 14px;
	background: #fff url(https://static.fangxiaoer.com/web/images/my/admin/my_xiala.gif) 107px 11px no-repeat;
	cursor: pointer;
	display: inline-block;
	float: left;
	margin-right: 20px;
	z-index: 99;
}

.arlTcMain ul li .my_xl_txt {
	float: left;
	width: 133px;
	line-height: 26px;
	padding-right: 17px;
}

.arlTcMain ul li .my_xl,
.my_xl_list {
	border: 1px solid #ccc;
	border-radius: 2px;
}

.arlTcMain ul li .my_xl_txt,
.my_xl_list li {
	text-indent: 0px;
	overflow: hidden;
}

.arlTcMain ul li .my_xl_list {
	position: absolute;
	top: 34px;
	left: -1px;
	z-index: 88888;
	/* border-top:none; */
	width: 100%;
	display: none;
	_top: 23px;
	margin-left: 0px !important;
}

.arlTcMain ul li .my_xl_list li {
	list-style: none;
	height: 30px;
	line-height: 30px;
	cursor: default;
	background: #fff;
	padding-left: 6px;
}

.arlTcMain ul li .my_xl_list li.focus {
	background: #3399FF;
	color: #fff
}

.arlTcMain ul li .my_xl_input,
.tese_input {
	position: absolute;
	top: -999999px;
}

.arlTcMain ul li input#code {
	width: 115px !important;
}

.arlTcMain ul li i {
	color: red;
}

.arlTcMain ul li span {
	display: block;
	width: 72px;
	height: 28px;
	float: left;
	margin-right: 14px;
	text-align: center;
	line-height: 28px;
	border: 1px solid #eaeaea;
	cursor: pointer;
}

.arlTcMain ul li span.duigou {
	background: url(https://static.fangxiaoer.com/web/images/sy/agentRecruit/duigou.png) top center no-repeat;
	color: #ff5200;
}

.arlTcMain ul li b {
	width: 101px;
	background: #f1f2f4;
	font-weight: 400;
	display: block;
	float: left;
	text-align: center;
	cursor: pointer;
	width: 130px;
	line-height: 28px;
	height: 28px;
	margin-left: 20px;
}

.arlTcMain ul li b.fxe_validateCode {
	display: none;
}

.arlTcMain ul .arlTcMainBtn {
	width: 246px;
	height: 32px;
	background: #ff5200;
	color: #fff;
	display: block;
	text-align: center;
	border-radius: 2px;
	margin: 0 auto;
	line-height: 32px;
	font-size: 14px;
	cursor: pointer;
	text-decoration: none;
	margin-top: 22px;
}

.arlTcMain .closeReport {
	font-style: normal;
	font-size: 12pt;
	color: #999;
	background: url(https://static.fangxiaoer.com/m/images/sale/reportClose.jpg);
	display: block;
	width: 16px;
	height: 16px;
	background-size: cover;
	position: absolute;
	right: 10px;
	top: 10px;
	cursor: pointer;
	opacity: 0.6;
}

.experienceHide {
	display: none;
}

.living {}

.living select {
	float: left;
	width: 132px;
	height: 26px;
	line-height: 28px;
	border: 1px solid #eaeaea;
	padding-left: 5px;
}

.living select+select {
	margin-left: 10px;
}


/*搜索框*/

.searchEsfMap {
	width: 1170px;
	margin: 0px auto 0 auto;
	overflow: hidden;
	position: relative;
}

.searchEsfMap .searchMapInput {
	padding-left: 5px;
	width: 430px;
	height: 44px;
	line-height: 44px;
	border: 1px solid #ddd;
	float: left;
}

.searchEsfMap .searchMapBtn {
	width: 99px;
	height: 46px;
	/* height: 33px; */
	line-height: 46px;
	float: left;
	background: #ff7200;
	border: none;
	color: #fff;
	margin-left: -1px;
	font-size: 16px;
	cursor: pointer;
}

#deleteButton {
	position: absolute;
	top: 14px;
	left: 402px;
	cursor: pointer;
	display: none;
}


/*公司详情页*/

.companyMain {
	width: 1170px;
	margin: 0 auto;
	border: 1px solid #eee;
	margin-bottom: 20px;
}

.companyBanner {
	width: 1170px;
	margin: 0 auto;
	height: 126px;
	overflow: hidden;
	background: url(https://static.fangxiaoer.com/web/images/sy/agentRecruit/companyBg.jpg);
	/* margin-bottom:  20px; */
}

.companyBanner h4 {
	color: #fff;
	font-size: 30px;
	line-height: 126px;
	padding-left: 20px;
	font-weight: 500;
}

.companyintroduce {
	width: 1170px;
	margin: 0 auto;
	overflow: hidden;
	padding: 20px 0;
}

.companyintroduce .L {
	width: 874px;
	float: left;
	padding: 20px;
}

.companyintroduce .L h4 {
	font-size: 20px;
	margin-bottom: 20px;
	font-weight: 600;
	color: #333;
}

.companyintroduce .L p {
	font-size: 14px;
	line-height: 30px;
	color: #666;
}

.companyintroduce .R {
	float: right;
	width: 236px;
	height: 174px;
	overflow: hidden;
	margin-right: 20px;
}

.companyintroduce .R img {
	width: 100%;
	height: 100%;
}


/* 地图 */

.arlRightMap {
	border: 1px solid #eee;
}

.arlRightMap h4 {
	font-size: 14px;
	font-weight: normal;
	color: #333;
	padding-left: 5px;
	white-space: pre;
	text-overflow: ellipsis;
	width: 245px;
	padding-top: 5px;
	overflow: hidden;
}

.bigAllmap h4 {
	width: 97%;
	padding-left: 2%;
	font-size: 18px;
}

.arlRightMap p {
	color: #666;
	padding-left: 5px;
	/* white-space:  pre; */
	/* text-overflow: ellipsis; */
	width: 245px;
	overflow: hidden;
}

.arlRightMap>div {
	/* height:250px */
}

.hoverMap {
	display: block;
}

.arlRightMap>div>div {
	position: relative;
}


/* .hoverMap:hover .hoverMapShow{display:block} */

.hoverMapShow {
	    border-radius: 1px;
    position: absolute;
    right: 64px;
    bottom: 16px;
    z-index: 9;
    background: rgba(255, 255, 255, 1);
    padding: 0 12px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
}




/*职位介绍*/

.agentPosition {
	width: 1130px;
	margin: 0 auto;
	padding: 20px;
	overflow: hidden;
	margin-bottom: 20px;
	background: #f6f7fc;
}

.agentPosition:hover {}

.agentPosition .L {
	overflow: hidden;
	width: 950px;
	float: left;
}

.agentPosition .L h4 {
	font-size: 22px;
	color: #333;
	display: inline-block;
	float: left;
}

.agentPosition .L>span {
	font-size: 14px;
	margin-left: 15px;
}

.agentPosition .L>div {
	line-height: 30px;
}

.agentPosition .L>div>p {
	color: #ff5200;
	line-height: 26px;
	margin-top: 5px;
	display: inline-block;
	font-size: 16px;
}

.agentPosition .L>div>p.agentRecruitListp {
	color: #999;
	margin: 5px 0;
	font-size: 14px;
	margin-left: 20px;
}

.agentPosition .L>div>p.agentRecruitListp span {}

.agentPosition .L>div>p.agentRecruitListp>i {}

.agentPosition .L>div>p.agentRecruitListp span i {}

.agentPosition .L>p.fldy {
	color: #999;
	margin-top: 10px;
}

.agentPosition .L>p.fldy span {
	display: inline-block;
	padding: 0 6px;
	border: 1px solid #e2e2e2;
	margin-right: 10px;
	line-height: 24px;
	height: 22px;
	border-radius: 2px;
	background: #fff;
}

.agentPosition .R .liaobeiICon {
	float: right;
	margin-top: 36px;
	margin-left: 14px;
}

.agentPosition .R .arlLeftBTn {
	margin-top: 35px;
	display: block;
	height: 26px;
	line-height: 26px;
	width: 95px;
	float: right;
	border: 1px solid #ff5200;
	text-align: center;
	border-radius: 13px;
	color: #ff5200;
	cursor: pointer;
	text-decoration: none;
}

.agentPosition .R .arlLeftBTn:hover,
.arlLeftBTn:hover {
	background: #ff5200;
	color: #fff
}

.company {
	height: 72px;
	margin-bottom: 12px;
}

.companyRLogo {
	width: 72px;
	height: 72px;
	float: left;
	margin-right: 20px;
}

.companyRLogo img {
	width: 100%;
	height: 100%;
	border-radius: 2px;
}

.arlLeft .txtMain {
	width: 860px;
	padding: 20px;
	line-height: 30px;
	font-size: 14px;
}

.arlLeft .txtMain h5 {
	font-size: 14px;
	font-weight: normal;
	color: #333;
}

.arlLeft .txtMain p {
	color: #666;
}

.bigAllmap {
	display: none;
	position: relative;
	width: 800px;
	height: 500px;
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -400px;
	margin-top: -250px;
	background: #fff;
	z-index: 99999;
}

.heiMuAllmap {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 9999;
	display: none;
}

.allmapBigShow {
	width: 800px;
	height: 500px;
}

.BMapLabel {
	background: rgba(255, 6, 0, 0.6) !important;
	padding: 2px 8px !important;
	display: block !important;
	color: #fff !important;
	border-radius: 2px !important;
}

.bigAllmapClose {
	z-index: 99999999999999999999;
	cursor: pointer;
	top: 10%;
	display: block;
	width: 20px;
	height: 20px;
	float: right;
}

.bigAllmapClose img {
	width: 100%;
	height: 100%;
}

.showTitle {
	display: none;
}

#allmap {
	height: 250px;
	width: 248px
}


/*页面滚动后固定页面*/

.fixbkb {
	position: fixed;
	top: 0;
	left: 0;
	background: rgba(255, 82, 0, 0.9);
}

.bkNavFloat {
	width: 100%;
	height: 42px;
	padding-top: 4px;
	margin: 0px 0;
	z-index: 999;
	overflow: hidden;
	color: #fff;
	display: block !important;
	background: #ff5200;
	position: fixed;
}

.bkNavFloat h4 {
	float: left;
	font-size: 18px;
	width: auto;
}

.bkNavFloat .liaobeiICon {
	float: right;
	line-height: 30px;
	margin-left: 14px;
}

.bkNavFloat .liaobeiICon .liaobeiIcon {
	margin-top: 9px;
	background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue2.png) 0px -30px no-repeat;
}

.bkNavFloat .liaobeiICon:hover .liaobeiIcon {
	background: url(https://static.fangxiaoer.com/web/images/ico/liaobeiBlue2.png) 0px -30px no-repeat !important;
}

.bkNavFloat .liaobeiICon text {
	color: #fff;
	font-size: 12px;
}

.bkNavFloat>div {
	overflow: hidden;
	padding-top: 5px;
}

.container div div {
	float: right;
}

.container div>p {
	font-size: 14px;
	float: left;
	line-height: 30px;
	margin-left: 40px;
}

.container div i {
	background: url(https://static.fangxiaoer.com/web/images/ico/sign/fixbkb-phone.png)no-repeat;
	display: block;
	width: 22px;
	height: 22px;
	float: left;
	margin-right: 15px;
}

.container div div p {}

.topR {
	float: right;
}

.topR .liaobeiICon {
	margin-top: 10px;
	float: left;
}

.topR .topR-login {
	padding: 0 10px;
	color: #ffffff;
	line-height: 45px;
	height: 45px;
	display: block;
	width: 276px;
	text-align: center;
	background: #FF3E00;
	border-radius: 2px;
}

.topR .topR-liaobeiICon {
	padding: 0 10px;
	color: #ffffff;
	line-height: 45px;
	height: 45px;
	display: block;
	text-align: center;
	font-size: 16px;
	margin-top: 14px;
	width: 276px;
	border: 1px solid #FF3E00;
	border-radius: 2px;
}

.topR a:hover {
	text-decoration: none
}

.topR .topR-liaobeiICon text {
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #FF3E00;
}

.topPrise {
	font-size: 20px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #FF3E00;
	float: right;
}

.topArea {}

.topArea span {
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
}

.topBg {
	background: #FDFDFD;
	box-shadow: 0px 2px 7px 0px rgba(0, 0, 0, 0.1);
}

.topBg .crumbs,
.topBg .crumbs a {
	font-size: 12px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #999999;
	margin-top: 0px !important;
	padding-top: 12px;
	margin-bottom: 0 !important;
}

.agentTop {
	width: 1170px;
	margin: 0 auto;
	padding: 17px 0;
	overflow: hidden;
	color: #333;
}

.agentTop .L h4 {
	font-size: 24px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #333333;
	display: inline-block;
	max-width: 620px;
	text-overflow: ellipsis;
	overflow: hidden;
	line-height: 29px;
	height: 29px;
	float: left;
}

.agentTop .topL-title>span {
	font-size: 22px;
	margin-left: 20px;
	float: left;
	margin-top: 5px;
}

.topL-title {
	overflow: hidden;
	margin-bottom: 19px;
}

.agentTop .L {
	overflow: hidden;
	width: 784px;
	float: left;
	    margin-top: 10px;
}

.bottomListL {
	width: 900px;
	float: left;
}

.bottomListLcont {}

.txtMain h5 {
	font-size: 16px;
	color: #333;
	position: relative;
	padding-bottom: 10px;
	margin-bottom: 25px;
}

.bottomListLcont .txtMain p {
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	margin-bottom: 18px;
}

.hideMore {
	word-break: break-all;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 20;
	overflow: hidden;
}

.seeMoreBtn {
	color: #379df5;
	cursor: pointer;
	font-size: 16px;
	text-align: center;
	display: none;
}

.nojobDesc {
	font-size: 16px;
	/* text-align: center; */
	margin: 20px 0 20px 0;
	display: none;
}

.titleIco {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 20px;
	height: 2px;
	background: #F9511B;
	border-radius: 1px;
}

.fldyLi {
	overflow: hidden;
	margin-bottom: 40px;
}

.fldyLi span {
	float: left;
    text-align: center;
    line-height: 26px;
    color: #ff5200;
    border-radius: 2px;
    margin-right: 20px;
    width: 80px;
    height: 26px;
    border: 1px solid #F9511B;
}

.mapIcoico {
	background: url(https://static.fangxiaoer.com/web/images/ico/sign/ico_map.gif);
	width: 13px;
	height: 15px;
	display: inline-block;
	cursor: pointer;
	background-size: 100% 100%;
	margin: 17px 5px 0 0;
	float: left;
}

.showMap {
	box-shadow: 3px 3px 10px #00000052;
	margin-left: 2px;
	position: relative;
	overflow: hidden;
}

.showMap #companyAddress {
	font-size: 14px;
	height: 50px;
	line-height: 50px;
	padding-left: 20px;
}

.bottomListMap {
	margin-bottom: 40px
}

.padding24 {
    padding-left: 24px;
    padding-right: 24px;
}

.otherJobLi {
	overflow: hidden;
    border: 1px solid #eee;
    padding: 24px 0 0 0;
    padding-top: 17px;
    margin-bottom: 10px;
    border-radius: 6px;
    background: #fdfdfd;
}

.otherJobH4 {
	display: inline-block;
	float: left;
	font-size: 18px;
	font-weight: bold;
	height: 20px;
	line-height: 24px;
	margin-bottom: 13px;
	width: 100%;
}

.otherJobH4>a {
	float: left;
}

.otherJobH4 .otherJobH4 {}

.otherJobH4div {
	float: left;
}

.otherJobH4 span {
	    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #0058A6;
}

.otherJobH4>a i {
	float: left;
	padding: 0 4px;
	height: 18px;
	overflow: hidden;
}

.otherJobH4>a span+i+span {
	max-width: 440px;
	overflow: hidden;
	display: inline-block;
	white-space: pre;
	text-overflow: ellipsis;
}

.agentSpan {
	    margin-top: 20px;
    width: 100%;
    display: block;
    float: left;
    background: #F8F9FC;
    height: 40px;
    line-height: 40px;
    border-radius: 0px 0px 6px 6px;
}

.agentSpan span {
    display: inline-block;
    padding: 0 18px;
    border: 1px solid #EEEEEE;
    margin-right: 10px;
    line-height: 24px;
    height: 24px;
    border-radius: 2px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #61687C;
}

.otherJobLiL {
	width: 550px;
	float: left;
}

.otherJobLiR {
	float: right;
	width: 300px;
	text-align: right;
	overflow: hidden;
	margin-top: -42px;
}

.otherJobLiL-Prise {
	    line-height: 26px;
    margin-top: -1px;
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #FF3E00;
	float: left;
	margin-right: 15px;
}

.otherJobLi-login {
	padding: 0 10px;
	border: 1px solid #ff6100;
	border-radius: 23px;
	color: #ff6100;
	line-height: 24px;
	height: 24px;
	display: block;
	width: 60px !important;
	float: right;
	text-align: center;
	margin-top: 48px;
}

.otherJobLiL-txt {
	overflow: hidden;
}

.agentRecruitListp {
	float: left;
    height: 25px;
    width: 360px;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #666666;
}

.agentRecruitListp span {}

.companyDesc {
	width: 215px;
	box-shadow: 1px 2px 4px #0000004a;
	padding: 15px;
	margin-top: 5px;
}

.companyDesc a:hover {
	color: #333
}

.companyDesc .txtMain {
	margin-top: -5px;
}

.companyDesc-logo .companyRLogo {
	width: 100px;
	height: 79px;
	margin-bottom: 14px;
	float: left;
}

.companyDesc-name {
	font-size: 18px;
	color: #333;
	font-weight: bold;
	margin-top: -7px;
	margin-bottom: 20px;
	overflow: hidden;
}

.companyDesc-name p {
	display: inline-block;
	max-width: 168px;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: pre;
	line-height: 29px;
	float: left;
}

.companyDesc-name span {
	display: inline-block;
	border: 1px solid #ff5200;
	font-size: 13px;
	font-weight: normal;
	height: 16px;
	line-height: 16px;
	padding: 0 4px;
	color: #ff5200;
	margin-left: 5px;
	float: left;
	margin-top: 6px;
}

.companyDesc-logo {
	overflow: hidden;
	margin-right: 8px;
}

.companyDesc-logo>a {
	display: block;
	float: left;
}

.companyDesc-area {
	width: 100%;
	height: 20px;
	line-height: 20px;
	margin-bottom: 7px;
}

.companyDesc-area label {
	color: #333;
	display: block;
	float: left;
}

.companyDesc-area>span {
	font-size: 14px;
	height: 14px;
	display: block;
	float: left;
}

.companyDesc-txt label {
	color: #333;
	font-weight: bold;
}

.companyDesc-txt p {
	display: inline-block;
}

.hasResume {
	position: fixed;
	left: 50%;
	width: 454px;
	height: 232px;
	top: 50%;
	background: #fff;
	margin-left: -227px;
	margin-top: -126px;
	text-align: center;
	padding-top: 20px;
	display: none;
}

.hasResume h5 {
	color: #333;
	font-size: 18px;
	margin-bottom: 50px;
}

.hasResume p {
	font-size: 18px;
	line-height: 30px;
}

.hasResume p+p {}

.hasResume .hasBtn {
	border-top: 1px solid #eee;
	height: 58px;
	line-height: 58px;
	margin-top: 38px;
}

.hasResume .hasBtn span {
	width: 50%;
	display: inline-block;
	float: left;
	color: #ff5200;
	font-size: 18px;
	cursor: pointer;
}

.hasResume .hasBtn span+span {
	border-left: 1px solid #eee;
	width: 49%;
	color: #333;
}

.closeReport {
	font-style: normal;
	font-size: 12pt;
	color: #999;
	background: url(https://static.fangxiaoer.com/m/images/sale/reportClose.jpg);
	display: block;
	width: 16px;
	height: 16px;
	background-size: cover;
	position: absolute;
	right: 10px;
	top: 10px;
	cursor: pointer;
	opacity: 0.6;
}

.infomation_float {
	width: 100%;
	height: 80px;
	margin: 0px 0;
	z-index: 999;
	overflow: hidden;
	display: block !important;
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px 4px 7px 0px rgba(23, 23, 23, 0.1);
	position: fixed;
}

.infomation_float .arlLeftBTn {
	width: 130px;
	height: 45px;
	line-height: 45px;
	border-radius: 0;
	background: #ff5200;
	color: #fff;
	font-size: 16px;
	margin-top: 16px;
	float: right;
}

.infomation_float .otherJobLi-login {
	width: 130px !important;
	height: 45px;
	line-height: 45px;
	background: #FF3E00;
	border-radius: 2px;
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #FFFFFF;
	margin-top: 16px;
	float: right;
}

.infomation_float .liaobeiICon a {
	padding: 0 10px;
	color: #ffffff;
	line-height: 45px;
	height: 45px;
	display: block;
	width: 264px;
	text-align: center;
	font-size: 16px;
	margin-top: 16px;
	border: 1px solid #FF3E00;
	border-radius: 2px;
	width: 130px;
	height: 45px;
}

.infomation_float a {
	text-decoration: none
}

.infomation_float .liaobeiICon text {
	display: inline-block;
	width: 100%;
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #FF3E00;
}

.infomation_float h4 {
	display: inline-block;
	float: left;
	width: 655px;
	overflow: hidden;
}

.infomation_float .container div>p {
	font-size: 20px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #FF3E00;
	margin-left: 28px;
	line-height: 80px;
}

.blueBg {
	position: fixed;
	top: 0;
	left: 0;
}

.job_subT {
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	margin-bottom: 20px;
}

.unstyled h4 div {
	height: 16px;
    line-height: 16px;
    float: initial;
    max-width: 655px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.unstyled h4 div:first-child {
	font-size: 18px;
    font-family: Microsoft YaHei;
    height: 18px;
    font-weight: bold;
    color: #333333;
    line-height: 18px;
    margin-top: 16px;
    margin-bottom: 15px;
}

.unstyled_2 {
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.company_info {
	width: 742px;
	font-size: 14px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	line-height: 36px;
}

.companyName {
	font-size: 16px;
	font-family: Microsoft YaHei;
	font-weight: 400;
	color: #333333;
	line-height: 16px;

}
.com_a{
	    display: block;
    height: 16px;
    margin-bottom: 12px;
}

.companyPhone {
	height: 20px;
	line-height: 20px;
}
.txtMain{
	margin-bottom: 40px;
}
.arlLeftLiR {
    float: right;
    width: 280px;
    text-align: right;
    overflow: hidden;
    margin-right: 24px;
    margin-top: -97px;
}

.arlLeftLiR h3 {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #0058A6;
}
.arrSq {
    float: right;
    width: 270px;
    text-align: right;
    overflow: hidden;
    margin-right: 24px;
        margin-top: -15px;
    display: none;
}
.lbdiv {
    float: left;
    margin-right: 10px;
}
.liaobeiICon {
    display: inline-block;
    font-size: 14px;
    font-weight: normal;
    margin-left: 8px;
    color: #02a0db;
}
.liaobei {
    width: 120px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #FF3E00;
    border-radius: 2px;
    text-align: center;
    color: #FF3E00;
    font-size: 14px;
}

.toGetRecruit {
    width: 120px;
    height: 40px;
    line-height: 40px;
    background: #FF3E00;
    border-radius: 2px;
    text-align: center;
    color: #fff !important;
    font-size: 14px;
    display: block;
    float: right;
}
.toGetRecruit:hover{
    color: #fff !important;
	
}

.otherJobLi a{
	color: #0058A6;
}

.otherJobLi a:hover{
	color: #0058A6;
}
.fadeInRight {
	animation-name: fadeInRight;
	-webkit-animation-name: fadeInRight;
	animation-duration: .3s;
	-webkit-animation-duration: .3s;
	animation-timing-function: ease-in-out;
	-webkit-animation-timing-function: ease-in-out;
}

    
.hoverMapShow span{
	background: url(https://static.fangxiaoer.com/web/images/agent/map_point.png) no-repeat center;
    background-size: 100% 100%;
    width: 16px;
    height: 13px;
    display: block;
    float: left;
    margin-right: 7px;
    margin-top: 8px;
}