@charset "utf-8";
*{margin: 0; padding: 0; text-decoration: none; list-style: none; font-family:"微软雅黑"; outline: none; font-weight: normal;}
.clearfix{/* clear: both; */}
@font-face {
	font-family: 'dinot-bold';  
	src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');
	}
body{background-color: #f5f5f5;}
.search_wei{margin-top: 10px;}
.search_wei .content_box{width: 1170px; margin: 0 auto;}
.search_wei .content_box input{ display: block; float: left; width: 459px; height: 46px; border: 1px solid #f0f0f0; border-radius: 4px 0 0 4px;
font-size: 14px;padding-left: 11px;}
.search_wei .content_box input::-webkit-input-placeholder{
            color:#999999;
        }
.search_wei .content_box input::-moz-placeholder{   /* Mozilla Firefox 19+ */
            color:#999999;
        }
.search_wei .content_box input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
            color:#999999;
        }
.search_wei .content_box input:-ms-input-placeholder{  /* Internet Explorer 10-11 */ 
            color:#999999;
        }
.search_wei .content_box button{ display: block; float: left;width: 102px; height: 47px; border: 1px solid #ff6100; background-color: #FF6100;
border-radius: 0px 4px 4px 0px; font-size: 16px; color: #ffffff;}
.banner_wei{margin-top: 40px;position: relative;}
.banner_wei .left_wei{ width:33%;height: 430px;position: relative;background-position: top right; background-repeat: no-repeat;float: left;}
.banner_wei .left_wei .hei{position: absolute;; left: 0; top: 0; background-color: rgba(0,0,0,0.5); width: 100%; height: 100%;;}
.banner_wei .left_wei img{display: none;}

.banner_wei .right_wei{ width:35%;height: 430px;position: relative;background-position: top left; background-repeat: no-repeat;float: right;}
.banner_wei .right_wei .hei{position: absolute;; left: 0; top: 0; background-color: rgba(0,0,0,0.5); width: 100%; height: 100%;;}
.banner_wei .right_wei img{display: none}
.banner_wei .Middle_wei{position: absolute; left: 50%;top:-30px;margin-left: -450px;z-index: 2; width: 900px; height: 490px;
 box-shadow: 7.5px 12.99px 21px 22px rgb( 89, 88, 88,0.1 );}
.banner_wei .Middle_wei img{ display: none;}
.banner_wei .Middle_wei .hei{position: absolute;; left: 0; top: 0; width: 100%; height: 100%;background: -webkit-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.5)); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7)); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7)); /* Firefox 3.6 - 15 */
  background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7)); }
  .banner_wei .Middle_wei .hei h1{font-size: 42px; color: #ffffff; text-align: center;margin-top: 308px;}
.banner_wei .Middle_wei .hei h1 span{font-size: 42px;margin-left: 24px; margin-right: 24px;}
.banner_wei .Middle_wei .hei h2{font-size: 18px; color: #ffffff; text-align: center;margin-top: 308px;margin-top: 24px;}
.banner_wei .Middle_wei .hei .decorate{display: block;margin: 0 auto;margin-top: 34px;}
.nav_wei{width: 868px; margin: 0 auto;margin-top: 93px;}
.nav_wei ul li{ float: left;padding-left:46.5px;padding-right:52.5px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Division.png); background-repeat: no-repeat;
background-position: right center; }
.nav_wei ul li:last-of-type{background:none}
.nav_wei ul li a{ font-size:16px; color: #333333;display: block;}
.nav_wei ul li:nth-child(1){padding-left: 0; }
/*.nav_wei ul li:nth-child(6){padding-right: 0;background: none;}*/
.nav_wei ul li a:hover{color: #ff6100;}
.big_wei {margin-top: 20px;/* border-top: 1px solid #ebebeb; *//* padding-top: 20px; */}
.big_wei .content_box{width: 1170px;margin: 0 auto;background-color: #fff;padding-left: 15px;padding-right: 15px;padding-bottom: 20px;margin-bottom: 20px;}
.big_wei .content_box .btn_box{border: 1px solid #ededed;max-width: 100%;height: 60px;/* margin-left: 36px; */}
.big_wei .content_box .border{padding-top: 32px;position: relative;/* overflow: hidden; */padding-bottom: 4px;}
.big_wei .content_box .border .left_wei{width: 28px; height: 60px; cursor: pointer; float: left;border-right: 1px solid #ededed;}
.big_wei .content_box .border .left_wei img{margin: 0 auto;display: block;padding-top: 23px;}
.big_wei .content_box .border  .btn {float: left;width: 1110px;position: relative;}
.big_wei .content_box .border  .btn .show{overflow: hidden;}
.big_wei .content_box .border  .btn ul li{float: left;position: relative;cursor: pointer;width: 159px;height: 60px;position:relative}
.big_wei .content_box .border  .btn ul li:hover a{text-decoration:none}
.big_wei .content_box .border  .btn ul li h1{float: left;font-size: 14px;color: #333333;margin-top: 7px;line-height: 14px;}
.big_wei .content_box .border  .btn ul li span{display: block;width: 38px;height: 20px;color: #fff;float: left;font-size: 12px;text-align: center;line-height: 20px;border-radius: 3px;margin-left: 2px;margin-top: 2px;}
.big_wei .content_box .border  .btn ul li .line_sun{ width:1px; height:26px; background-color:#ededed; position:absolute; left:0; top:17px}
.big_wei .content_box .border  .btn ul li:nth-child(1) .border_sun{border: 0; padding-left: 20px;}
.big_wei .content_box .border  .btn ul li .type_1{ background-color: #ff6100;}
.big_wei .content_box .border  .btn ul li .type_2{ background-color: #ffa72d;}
.big_wei .content_box .border  .btn ul li .type_3{ background-color: #edf1f4; color: #7f96b5;}
.big_wei .content_box .border  .btn ul li:hover h1{color:#ff5200}
.big_wei .content_box .border  .btn ul li:hover h2{color:#ff5200}
.big_wei .content_box .border  .btn ul li h2{font-size: 14px;color: #333333;margin-top: 0px;}
.big_wei .content_box .border  .btn ul li .border_sun{/* border-left:1px solid #ededed; */margin-top: 6px;padding-left: 29px;padding-right: 12px;}
.big_wei .content_box .border .right_wei{width: 28px; height: 60px; cursor: pointer; float: left;border-left: 1px solid #ededed;}
.big_wei .content_box .border .right_wei img{margin: 0 auto;display: block;padding-top: 23px;}
.big_wei .content_box .border   .Select{width: 159px;height: 72px;background-color: #ff6100;position: absolute;left: 29px;top: 26px;box-shadow: 2px 2px 12px 2px rgba( 255, 97, 0,0.5 );}
.big_wei .content_box .border   .Select span{display:block;float:left;font-size:12px;background-color:#fff;line-height:  20px;padding-left:  5px;padding-right:  5px;border-radius:  3px;margin-top: 14px;color: #ff5200;margin-left: 5px;}
.big_wei .content_box .border   .Select h3{font-size: 14px;color: #fff;padding-left: 27px;padding-top: 13px;float:  left;}
.big_wei .content_box .border   .Select h4{font-size: 14px;color: #fff;padding-left: 27px;}
.big_wei .content_box .border  .btn .lunbo{position: relative;}
.big_wei .content_box .information{border: 1px solid #ededed;margin-top: 29px;padding-bottom: 20px;}
.big_wei .content_box .information .photo img{width: 400px;height: 400px;float:  left;}
.big_wei .content_box .information .photo{float: left;/* margin-top: 13px; */position: relative;cursor: pointer;/* margin-left:  72px; */padding-left: 72px;padding-right: 67px;}
.big_wei .content_box .information .photo  h1{text-align:center;font-size:14px;font-weight:normal;color:#333333}


.big_wei .content_box .information .information_sun{float: right;margin-right: 50px;margin-top: 62px;width: 530px;}
.big_wei .content_box .information .information_sun .name_box h1{font-size: 26px; color: #333333;font-weight: bold;float: left;font-family: dinot-bold;}
.big_wei .content_box .information .information_sun .name_box span{display: block;width: 44px;height: 24px;border-radius: 3px;float: left;text-align: center;/* margin-top: 5px; */margin-left: 8px;}
.big_wei .content_box .information .information_sun .name_box .type_1{ background-color: #ff5200; color: #fff;}
.big_wei .content_box .information .information_sun .name_box .type_2{ background-color: #ffa72d; color: #fff;}
.big_wei .content_box .information .information_sun .name_box .type_3{ background-color: #edf1f4; color: #7f96b5;}
.big_wei .content_box .information .information_sun  .huxing{float:left;width: 180px;}
.big_wei .content_box .information .information_sun  .huxing {margin-top: 29px;}
.big_wei .content_box .information .information_sun  .huxing .name{ float: left;}
.big_wei .content_box .information .information_sun  .huxing .text{ float: left;margin-left: 29px;}
.big_wei .content_box .information .information_sun  .leix{float:left;width: 180px;}
.big_wei .content_box .information .information_sun  .leix {margin-top: 29px;}
.big_wei .content_box .information .information_sun  .leix .name{ float: left;}
.big_wei .content_box .information .information_sun  .leix .text{ float: left;margin-left: 29px;}

.big_wei .content_box .information .information_sun  .mianji{margin-top: 12px;float:left;width: 180px;}
.big_wei .content_box .information .information_sun  .mianji .name{ float: left;}
.big_wei .content_box .information .information_sun  .mianji .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .leix .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .zhengshong{margin-top: 12px;float:left;width: 180px;}
.big_wei .content_box .information .information_sun  .zhengshong .name{ float: left;}
.big_wei .content_box .information .information_sun  .zhengshong .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .zhengshong .text{ float: left;margin-left: 29px;}
.big_wei .content_box .information .information_sun  .huxing .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .dianping .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .mianji .text{ float: left;margin-left: 29px;}
.big_wei .content_box .information .information_sun  .dianping {margin-top: 12px;border-top: 1px solid #ededed;padding-top:  12px;}
.big_wei .content_box .information .information_sun  .dianping .name{ float: left;}
.big_wei .content_box .information .information_sun  .dianping .text{ float: left;margin-left: 29px;width: 437px;}
.big_wei .content_box .information .information_sun  .phone{margin-top: 12px;padding-top: 7px;border-top: 1px solid #ededed;}
.big_wei .content_box .information .information_sun .phone h1{font-size: 14px;color: #999999;font-family: dinot-bold;margin-bottom: 8px;}
.big_wei .content_box .information .information_sun .phone h2{font-size: 30px; color: #333333;font-family: dinot-bold; font-weight: bold; float: left;}
.big_wei .content_box .information .information_sun .phone h2 span{font-size: 14px;font-weight: bold;}
.big_wei .content_box .information .information_sun .phone button{width: 120px;height: 30px;background-color: #fe6116;color: #fff;font-size: 14px;border:0;display: block;float: left;cursor: pointer;margin-left: 14px;/* margin-top: 5px; */}
.big_wei .content_box .information .photo .look{background-color: rgba(0,0,0,0.5);position: absolute;right: 68px;bottom: 0;}
.big_wei .content_box .information .photo .look h1{font-size: 12px;color: #fff;line-height: 30px;padding-right: 11px;padding-left: 30px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Magnifier.png);background-repeat: no-repeat;background-position: 11px;cursor: pointer;}
.layer_box{position: fixed;left: 0;top: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0.5);z-index: 1000;display: none;}
.layer_box table{width: 100%; height: 100%;;}
.layer_box table .layer{width: 900px;height: 710px;background-color: #000;margin: 0 auto;position: relative;}
.layer_box table .layer ul li img{max-width: 600px;}
.layer_box table .layer .close{position: absolute;;top: 20px; right: 20px; cursor: pointer;}
.layer_box table .layer ul li{width: 600px; height: 600px;}
.layer_box .layer .left_btn{position: absolute;left: 45px;top: 50%;}
.layer_box .layer .right_btn{position: absolute;right: 45px;top: 50%;}
.layer_box .layer .lunbo{position: relative;}
.layer_box .layer .left_btn img{margin-top: -40px;cursor: pointer;}
.layer_box .layer .right_btn img{margin-top: -40px;cursor: pointer;}
.layer_box .layer .photo{overflow: hidden;width: 600px;margin:  0 auto;}
.layer_box .layer .photo ul li{ float: left;}
.layer_box .layer .photo ul li img{display: block; margin: 0 auto;}
.footer{ margin-top:0!important ;}

.big_wei .content_box .information .photo .show_sun{overflow:hidden;width: 400px;}
.big_wei .content_box .information .photo .left_btn{background-color:#bfbfbf;position:absolute;left: 0px;top:136px;cursor:pointer;display:none;}
.big_wei .content_box .information .photo .left_btn img{ width:22px;height:43px; padding:32px 14px;}
.big_wei .content_box .information .photo .right_btn{background-color:#bfbfbf;position:absolute;right: 0px;top:136px;cursor:pointer;display:none;}
.big_wei .content_box .information .photo .right_btn img{ width:22px;height:43px;padding:32px 14px; }
.big_wei .content_box .information .photo .right_btn:hover{ background-color:#5c5c5c}
.big_wei .content_box .information .photo .left_btn:hover{ background-color:#5c5c5c}
.big_wei .content_box .information .photo .show_sun .lunbo{position:relative}

.layer_box .layer h1 {
    text-align: center;
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    padding-top:23px;
    padding-bottom:20px
}
.layer_box .layer .left_btn .hover{display:none}
.layer_box .layer .right_btn .hover{display:none}
.layer_box .layer .left_btn:hover img{display:none}
.layer_box .layer .left_btn:hover .hover{display:block}

.layer_box .layer .right_btn:hover img{display:none}
.layer_box .layer .right_btn:hover .hover{display:block}@charset "utf-8";
*{margin: 0; padding: 0; text-decoration: none; list-style: none; font-family:"微软雅黑"; outline: none; font-weight: normal;}
.clearfix{/* clear: both; */}
@font-face {
	font-family: 'dinot-bold';  
	src: url('https://static.fangxiaoer.com/web/styles/new_sy/house/dinot-bold.woff');
	}
body{background-color: #f5f5f5;}
.search_wei{margin-top: 10px;}
.search_wei .content_box{width: 1170px; margin: 0 auto;}
.search_wei .content_box input{ display: block; float: left; width: 459px; height: 46px; border: 1px solid #f0f0f0; border-radius: 4px 0 0 4px;
font-size: 14px;padding-left: 11px;}
.search_wei .content_box input::-webkit-input-placeholder{
            color:#999999;
        }
.search_wei .content_box input::-moz-placeholder{   /* Mozilla Firefox 19+ */
            color:#999999;
        }
.search_wei .content_box input:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
            color:#999999;
        }
.search_wei .content_box input:-ms-input-placeholder{  /* Internet Explorer 10-11 */ 
            color:#999999;
        }
.search_wei .content_box button{ display: block; float: left;width: 102px; height: 47px; border: 1px solid #ff6100; background-color: #FF6100;
border-radius: 0px 4px 4px 0px; font-size: 16px; color: #ffffff;}
.banner_wei{margin-top: 40px;position: relative;}
.banner_wei .left_wei{ width:33%;height: 430px;position: relative;background-position: top right; background-repeat: no-repeat;float: left;}
.banner_wei .left_wei .hei{position: absolute;; left: 0; top: 0; background-color: rgba(0,0,0,0.5); width: 100%; height: 100%;;}
.banner_wei .left_wei img{display: none;}

.banner_wei .right_wei{ width:35%;height: 430px;position: relative;background-position: top left; background-repeat: no-repeat;float: right;}
.banner_wei .right_wei .hei{position: absolute;; left: 0; top: 0; background-color: rgba(0,0,0,0.5); width: 100%; height: 100%;;}
.banner_wei .right_wei img{display: none}
.banner_wei .Middle_wei{position: absolute; left: 50%;top:-30px;margin-left: -450px;z-index: 2; width: 900px; height: 490px;
 box-shadow: 7.5px 12.99px 21px 22px rgb( 89, 88, 88,0.1 );}
.banner_wei .Middle_wei img{ display: none;}
.banner_wei .Middle_wei .hei{position: absolute;; left: 0; top: 0; width: 100%; height: 100%;background: -webkit-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.5)); /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7)); /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7)); /* Firefox 3.6 - 15 */
  background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.7)); }
  .banner_wei .Middle_wei .hei h1{font-size: 42px; color: #ffffff; text-align: center;margin-top: 308px;}
.banner_wei .Middle_wei .hei h1 span{font-size: 42px;margin-left: 24px; margin-right: 24px;}
.banner_wei .Middle_wei .hei h2{font-size: 18px; color: #ffffff; text-align: center;margin-top: 308px;margin-top: 24px;}
.banner_wei .Middle_wei .hei .decorate{display: block;margin: 0 auto;margin-top: 34px;}
.nav_wei{width: 868px; margin: 0 auto;margin-top: 93px;}
.nav_wei ul li{ float: left;padding-left: 51px; padding-right:58px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Division.png); background-repeat: no-repeat;
background-position: right center; }
.nav_wei ul li:last-of-type{background:none}
.nav_wei ul li a{ font-size:16px; color: #333333;display: block;}
.nav_wei ul li:nth-child(1){padding-left: 0; }
/*.nav_wei ul li:nth-child(6){padding-right: 0;background: none;}*/
.nav_wei ul li a:hover{color: #ff6100;}
.big_wei {margin-top: 20px;/* border-top: 1px solid #ebebeb; *//* padding-top: 20px; */}
.big_wei .content_box{width: 1170px;margin: 0 auto;background-color: #fff;padding-left: 15px;padding-right: 15px;padding-bottom: 20px;margin-bottom: 20px;}
.big_wei .content_box .btn_box{border: 1px solid #ededed;max-width: 100%;height: 60px;/* margin-left: 36px; */}
.big_wei .content_box .border{padding-top: 32px;position: relative;/* overflow: hidden; */padding-bottom: 26px;}
.big_wei .content_box .border .left_wei{width: 28px; height: 60px; cursor: pointer; float: left;border-right: 1px solid #ededed;}
.big_wei .content_box .border .left_wei img{margin: 0 auto;display: block;padding-top: 23px;}
.big_wei .content_box .border  .btn {float: left;width: 1110px;position: relative;}
.big_wei .content_box .border  .btn .show{overflow: hidden;}
.big_wei .content_box .border  .btn ul li{float: left;position: relative;cursor: pointer;width: 159px;height: 60px;position:relative}
.big_wei .content_box .border  .btn ul li:hover a{text-decoration:none}
.big_wei .content_box .border  .btn ul li h1{float: left;font-size: 14px;color: #333333;margin-top: 7px;line-height: 14px;}
.big_wei .content_box .border  .btn ul li span{display: block;width: 38px;height: 20px;color: #fff;float: left;font-size: 12px;text-align: center;line-height: 20px;border-radius: 3px;margin-left: 2px;margin-top: 2px;}
.big_wei .content_box .border  .btn ul li .line_sun{ width:1px; height:26px; background-color:#ededed; position:absolute; left:0; top:17px}
.big_wei .content_box .border  .btn ul li:nth-child(1) .border_sun{border: 0; padding-left: 20px;}
.big_wei .content_box .border  .btn ul li .type_1{ background-color: #ff6100;}
.big_wei .content_box .border  .btn ul li .type_2{ background-color: #ffa72d;}
.big_wei .content_box .border  .btn ul li .type_3{ background-color: #edf1f4; color: #7f96b5;}
.big_wei .content_box .border  .btn ul li:hover h1{color:#ff5200}
.big_wei .content_box .border  .btn ul li:hover h2{color:#ff5200}
.big_wei .content_box .border  .btn ul li h2{font-size: 14px;color: #333333;margin-top: 0px;}
.big_wei .content_box .border  .btn ul li .border_sun{/* border-left:1px solid #ededed; */margin-top: 6px;padding-left: 29px;padding-right: 12px;}
.big_wei .content_box .border .right_wei{width: 28px; height: 60px; cursor: pointer; float: left;border-left: 1px solid #ededed;}
.big_wei .content_box .border .right_wei img{margin: 0 auto;display: block;padding-top: 23px;}
.big_wei .content_box .border   .Select{width: 159px;height: 72px;background-color: #ff6100;position: absolute;left: 29px;top: 26px;box-shadow: 2px 2px 12px 2px rgba( 255, 97, 0,0.5 );}
.big_wei .content_box .border   .Select span{display:block;float:left;font-size:12px;background-color:#fff;line-height:  20px;padding-left:  5px;padding-right:  5px;border-radius:  3px;margin-top: 14px;color: #ff5200;margin-left: 5px;}
.big_wei .content_box .border   .Select h3{font-size: 14px;color: #fff;padding-left: 27px;padding-top: 13px;float:  left;}
.big_wei .content_box .border   .Select h4{font-size: 14px;color: #fff;padding-left: 27px;}
.big_wei .content_box .border  .btn .lunbo{position: relative;}
.big_wei .content_box .information{border: 1px solid #ededed;margin-top: 29px;padding-bottom: 20px;margin-top: 0;}
.big_wei .content_box .information .photo img{width: 400px;height: 400px;float:  left;}
.big_wei .content_box .information .photo{float: left;/* margin-top: 13px; */position: relative;cursor: pointer;/* margin-left:  72px; */padding-left: 68px;padding-right: 68px;border-right: 1px solid #ededed;}
.big_wei .content_box .information .photo  h1{text-align:center;font-size:14px;font-weight:normal;color:#333333}


.big_wei .content_box .information .information_sun{float: right;margin-right: 50px;margin-top: 0px;width: 530px;padding-top: 27px;}
.big_wei .content_box .information .information_sun .name_box h1{font-size: 26px; color: #333333;font-weight: bold;float: left;font-family: dinot-bold;}
.big_wei .content_box .information .information_sun .name_box span{display: block;width: 44px;height: 24px;border-radius: 3px;float: left;text-align: center;/* margin-top: 5px; */margin-left: 8px;}
.big_wei .content_box .information .information_sun .name_box .type_1{ background-color: #ff5200; color: #fff;}
.big_wei .content_box .information .information_sun .name_box .type_2{ background-color: #ffa72d; color: #fff;}
.big_wei .content_box .information .information_sun .name_box .type_3{ background-color: #edf1f4; color: #7f96b5;}
.big_wei .content_box .information .information_sun  .huxing{float:left;width: 180px;}
.big_wei .content_box .information .information_sun  .huxing {margin-top: 42px;}
.big_wei .content_box .information .information_sun  .huxing .name{ float: left;}
.big_wei .content_box .information .information_sun  .huxing .text{ float: left;margin-left: 29px;}
.big_wei .content_box .information .information_sun  .leix{float:left;width: 180px;}
.big_wei .content_box .information .information_sun  .leix {margin-top: 42px;}
.big_wei .content_box .information .information_sun  .leix .name{ float: left;}
.big_wei .content_box .information .information_sun  .leix .text{ float: left;margin-left: 29px;}

.big_wei .content_box .information .information_sun  .mianji{margin-top: 22px;float:left;width: 180px;}
.big_wei .content_box .information .information_sun  .mianji .name{ float: left;}
.big_wei .content_box .information .information_sun  .mianji .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .leix .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .zhengshong{margin-top: 22px;float:left;width: 180px;}
.big_wei .content_box .information .information_sun  .zhengshong .name{ float: left;}
.big_wei .content_box .information .information_sun  .zhengshong .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .zhengshong .text{ float: left;margin-left: 29px;}
.big_wei .content_box .information .information_sun  .huxing .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .dianping .name h1{font-size:14px;color: #999999;}
.big_wei .content_box .information .information_sun  .mianji .text{ float: left;margin-left: 29px;}
.big_wei .content_box .information .information_sun  .dianping {margin-top: 32px;border-top: 1px solid #ededed;padding-top:  12px;}
.big_wei .content_box .information .information_sun  .dianping .name{ float: left;}
.big_wei .content_box .information .information_sun  .dianping .text{ float: left;margin-left: 29px;width: 437px;}
.big_wei .content_box .information .information_sun  .phone{margin-top: 30px;padding-top: 30px;border-top: 1px solid #ededed;}
.big_wei .content_box .information .information_sun .phone h1{font-size: 14px;color: #999999;font-family: dinot-bold;margin-bottom: 8px;float:  left;margin-right: 10px;}
.big_wei .content_box .information .information_sun .phone h2{font-size: 30px; color: #333333;font-family: dinot-bold; font-weight: bold; float: left;}
.big_wei .content_box .information .information_sun .phone h2 span{font-size: 14px;font-weight: bold;}
.big_wei .content_box .information .information_sun .phone button{width: 120px;height: 30px;background-color: #fe6116;color: #fff;font-size: 14px;border:0;display: block;float: left;cursor: pointer;margin-left: 14px;/* margin-top: 5px; */border-radius: 5px;}
.big_wei .content_box .information .photo .look{background-color: rgba(0,0,0,0.5);position: absolute;right: 68px;bottom: 0;}
.big_wei .content_box .information .photo .look h1{font-size: 12px;color: #fff;line-height: 30px;padding-right: 11px;padding-left: 30px;background-image: url(https://static.fangxiaoer.com/web/images/Villa/Magnifier.png);background-repeat: no-repeat;background-position: 11px;cursor: pointer;}
.layer_box{position: fixed;left: 0;top: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0.5);z-index: 99999;display: none;}
.layer_box table{width: 100%; height: 100%;;}
.layer_box table .layer{width: 900px;height: 710px;background-color: #000;margin: 0 auto;position: relative;}
.layer_box table .layer ul li img{max-width: 600px;}
.layer_box table .layer .close{position: absolute;;top: 20px; right: 20px; cursor: pointer;}
.layer_box table .layer ul li{width: 600px; height: 600px;}
.layer_box .layer .left_btn{position: absolute;left: 45px;top: 50%;}
.layer_box .layer .right_btn{position: absolute;right: 45px;top: 50%;}
.layer_box .layer .lunbo{position: relative;}
.layer_box .layer .left_btn img{margin-top: -40px;cursor: pointer;}
.layer_box .layer .right_btn img{margin-top: -40px;cursor: pointer;}
.layer_box .layer .photo{overflow: hidden;width: 600px;margin:  0 auto;}
.layer_box .layer .photo ul li{ float: left;}
.layer_box .layer .photo ul li img{display: block; margin: 0 auto;}
.footer{ margin-top:0!important ;}

.big_wei .content_box .information .photo .show_sun{overflow:hidden;width: 400px;}
.big_wei .content_box .information .photo .left_btn{background-color:#bfbfbf;position:absolute;left: 0px;top:136px;cursor:pointer;display:none;}
.big_wei .content_box .information .photo .left_btn img{ width:22px;height:43px; padding:32px 14px;}
.big_wei .content_box .information .photo .right_btn{background-color:#bfbfbf;position:absolute;right: 0px;top:136px;cursor:pointer;display:none;}
.big_wei .content_box .information .photo .right_btn img{ width:22px;height:43px;padding:32px 14px; }
.big_wei .content_box .information .photo .right_btn:hover{ background-color:#5c5c5c}
.big_wei .content_box .information .photo .left_btn:hover{ background-color:#5c5c5c}
.big_wei .content_box .information .photo .show_sun .lunbo{position:relative}

.layer_box .layer h1 {
    text-align: center;
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    padding-top:23px;
    padding-bottom:20px
}
.layer_box .layer .left_btn .hover{display:none}
.layer_box .layer .right_btn .hover{display:none}
.layer_box .layer .left_btn:hover img{display:none}
.layer_box .layer .left_btn:hover .hover{display:block}

.layer_box .layer .right_btn:hover img{display:none}
.layer_box .layer .right_btn:hover .hover{display:block}
.big_wei .content_box .information .huxing_wei{font-size: 14px;color: #333333;font-weight: normal;line-height: 60px;margin-left: 227px;}
/* 语音户型二维码 */
#voiceCode{width: 91px;height: 120px;float: right;position: relative;top: -350px;left: 0px;text-align: center;}
#voiceCode h6{font-size: 12px;color: #333333;line-height: 17px;/* font-weight: 100; */margin-top: 2px;}
#voiceCode img{width: 71px;height: 71px;}
/* 语音户型二维码 */