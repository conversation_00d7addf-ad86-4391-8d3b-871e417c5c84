2025-07-22 08:23:54.720 DEBUG   --- [main] o.s.b.d.settings.DevToolsSettings        : Included patterns for restart : []
2025-07-22 08:23:54.728 DEBUG   --- [main] o.s.b.d.settings.DevToolsSettings        : Excluded patterns for restart : [/spring-boot-starter/target/classes/, /spring-boot-autoconfigure/target/classes/, /spring-boot-starter-[\w-]+/, /spring-boot/target/classes/, /spring-boot-actuator/target/classes/, /spring-boot-devtools/target/classes/]
2025-07-22 08:23:54.729 DEBUG   --- [main] o.s.b.devtools.restart.ChangeableUrls    : Matching URLs for reloading : [file:/Users/<USER>/gitLib/sysite/target/classes/]
2025-07-22 08:23:55.014 DEBUG   --- [restartedMain] o.s.c.e.PropertySourcesPropertyResolver  : Could not find key 'logging.exception-conversion-word' in any property source
2025-07-22 08:23:55.014 DEBUG   --- [restartedMain] o.s.c.e.PropertySourcesPropertyResolver  : Could not find key 'logging.pattern.console' in any property source
2025-07-22 08:23:55.014 DEBUG   --- [restartedMain] o.s.c.e.PropertySourcesPropertyResolver  : Could not find key 'logging.pattern.file' in any property source
2025-07-22 08:23:55.014 DEBUG   --- [restartedMain] o.s.c.e.PropertySourcesPropertyResolver  : Could not find key 'logging.pattern.level' in any property source
2025-07-22 08:23:57.251  INFO 92258 --- [localhost-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 2042 ms
2025-07-22 08:23:58.291  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerAdapter : Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6301fc98: startup date [Tue Jul 22 08:23:55 CST 2025]; root of context hierarchy
2025-07-22 08:23:58.357  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/marketing/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.Marketing(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.358  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/helpSearch || /helpSearch.htm]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.helpSearch(org.springframework.ui.Model,java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.358  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewActivity2],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewActivity(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.358  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewManagerDiscount || /viewManagerDiscount/{params}]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.viewManagerDiscount(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.359  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveCollection],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saveCollection(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.359  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pay_getQR],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.pay_getQR(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.360  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderPaySuccess],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewPayOrderDetail(java.lang.String)
2025-07-22 08:23:58.360  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/contactInfo/{orderId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.viewContract(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.360  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getLayOut],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getLayOut(java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.361  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getBuildName],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getLayOut(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.361  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/createOrder],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.createOrder(java.lang.String,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.361  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOrderDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getOrderDetail(java.lang.String)
2025-07-22 08:23:58.361  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/payForSuccess],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.payForSuccess(java.lang.String)
2025-07-22 08:23:58.362  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkIsSign],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.checkIsSign(java.lang.String)
2025-07-22 08:23:58.362  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleOthersFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleOthersFilter()
2025-07-22 08:23:58.362  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleInfoFilter()
2025-07-22 08:23:58.362  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.rentInfoFilter()
2025-07-22 08:23:58.363  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shopInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.shopInfoFilter()
2025-07-22 08:23:58.363  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleFeatureFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleFeatureFilter()
2025-07-22 08:23:58.363  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentFeatureFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.rentFeatureFilter()
2025-07-22 08:23:58.363  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/newRegionFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.newRegionFilter()
2025-07-22 08:23:58.364  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/usedRegionFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.usedRegionFilter()
2025-07-22 08:23:58.364  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/layoutFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.layoutFilter()
2025-07-22 08:23:58.364  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/relationShipFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.relationShipFilter()
2025-07-22 08:23:58.365  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewSubsidyForProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewSubsidyForProject(java.lang.String)
2025-07-22 08:23:58.365  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fetchSubsidyForProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.fetchSubsidyForProject(java.lang.String,java.lang.Integer,java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.365  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/discount/{input} || /discount]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.discount(org.springframework.ui.Model,java.lang.String,java.lang.String)
2025-07-22 08:23:58.365  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getUnit],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getUnit(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.365  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRoom],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getRoom(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.366  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderPay],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.orderPay(java.lang.String,java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 08:23:58.376  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/sale.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishshopSUPdateMapping(java.lang.String)
2025-07-22 08:23:58.376  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/rent.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopRUPdateMapping(java.lang.String)
2025-07-22 08:23:58.377  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/attorn.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopTUPdateMapping(java.lang.String)
2025-07-22 08:23:58.377  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/view/{houseId} || /UHouse/sale/SaleFullDetail/{houseId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseDetailMapping(java.lang.String)
2025-07-22 08:23:58.377  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/SubdistrictList || /UHouse/Subdistrict/SubdistrictList/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondHouseVillagesMapping()
2025-07-22 08:23:58.377  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/online/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeOnlineMapping(java.lang.Integer)
2025-07-22 08:23:58.377  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/room/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.onlineDetailMapping(java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.378  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/photo/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officePhoto(java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.378  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/photo/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaPhoto(java.lang.Integer)
2025-07-22 08:23:58.378  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/picture/view/{projectType}_{projectId}_{photoType}_{photoId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaPhotoDetail(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.378  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/album/{projectId:\d+}-{projectType:\d+}.htm || /house/album/{projectId:\d+}-{projectType:\d+}-{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resAndOfficeAlbumMapping(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.379  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/photos/{projectId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaAlbumMapping(java.lang.String)
2025-07-22 08:23:58.379  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/appraise/{projectId:[0-9]+}-{projectType:[0-9]+}.htm{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseAppraiseMapping(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.379  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id}#xmpj],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resComment(java.lang.Integer)
2025-07-22 08:23:58.380  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id}#xmpj],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeComment(java.lang.Integer)
2025-07-22 08:23:58.380  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/ask/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/ask/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseAskMapping(java.lang.Integer,java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.380  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id}#xmzx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeAsk(java.lang.Integer)
2025-07-22 08:23:58.381  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/news/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/news/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaNewsMapping(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.381  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/news/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaSaleInfo(java.lang.Integer)
2025-07-22 08:23:58.381  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/news/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeSaleInfo(java.lang.Integer)
2025-07-22 08:23:58.381  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/news/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseNews(java.lang.String)
2025-07-22 08:23:58.382  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/say/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/say/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewHouseSayMapping(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.382  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/say/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewHouseSay(java.lang.Integer)
2025-07-22 08:23:58.382  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pic720/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /pic720/{projectId:[0-9]+}-{projectType:[0-9]+}-{panId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getPictureMapper(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.382  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activity]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mDiscountList()
2025-07-22 08:23:58.383  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/view/{id} || /Uhouse/shangpu/view/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopDetial(java.lang.String)
2025-07-22 08:23:58.383  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/broker/{type}/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.agentHouse(java.lang.String,java.lang.String)
2025-07-22 08:23:58.383  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/homeAct.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseActivityMapping()
2025-07-22 08:23:58.383  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseKeeperMapping()
2025-07-22 08:23:58.383  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/integral.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.pointsMallMapping()
2025-07-22 08:23:58.384  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/strategy.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.strategyMapping()
2025-07-22 08:23:58.384  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/goods.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getGoodsMapping(java.lang.String)
2025-07-22 08:23:58.384  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/SaleHouse.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishSaleMapping(java.lang.String)
2025-07-22 08:23:58.384  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/rent_form_1.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishRentWMapping(java.lang.String)
2025-07-22 08:23:58.385  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/rent_form_2.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishRentSMapping(java.lang.String)
2025-07-22 08:23:58.385  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/sale.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopSMapping()
2025-07-22 08:23:58.385  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/rent.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopRMapping()
2025-07-22 08:23:58.386  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/attorn.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopTMapping()
2025-07-22 08:23:58.386  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/view/{path}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityDetailMapping(java.lang.String)
2025-07-22 08:23:58.387  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{value}_{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoMapping(java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.387  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsMapping(java.lang.String)
2025-07-22 08:23:58.387  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent//RentRegion/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsRegionMapping(java.lang.String)
2025-07-22 08:23:58.387  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/RentSubway/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsSubwayMapping(java.lang.String)
2025-07-22 08:23:58.387  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/view/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentMapping(java.lang.String)
2025-07-22 08:23:58.388  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/SubdistrictListRt/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.valligesMapping()
2025-07-22 08:23:58.388  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/view/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.valligeMapping(java.lang.String)
2025-07-22 08:23:58.388  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent/housemap/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentMapMapping()
2025-07-22 08:23:58.388  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/goufangnenglipinggu.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.abilityMapping()
2025-07-22 08:23:58.388  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/tiqianhuankuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.advanceMapping()
2025-07-22 08:23:58.388  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/zuhedaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.assembleMapping()
2025-07-22 08:23:58.388  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shangyedaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.businessMapping()
2025-07-22 08:23:58.389  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/gongjijindaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fundMapping()
2025-07-22 08:23:58.389  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/gongjijindaikuanedupinggu.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fundAssessMapping()
2025-07-22 08:23:58.389  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shuifei.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.tallageMapping()
2025-07-22 08:23:58.389  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale || /UHouse/sale/ || /UHouse/sale/Region || /UHouse/sale/UHouseSubway || /UHouse/sale/Region/{param:^[-?\d?_?]+$} || /UHouse/sale/UHouseSubway/{param:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseListMapping(java.lang.String)
2025-07-22 08:23:58.389  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/housemap/ || /UHouse/housemap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseMapMapping()
2025-07-22 08:23:58.389  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/View/{para}_all],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsDetailList(java.lang.String)
2025-07-22 08:23:58.389  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/baike.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.encyclopedia()
2025-07-22 08:23:58.390  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/formulate/baike.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.encyclopediaB()
2025-07-22 08:23:58.390  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/0929/ms/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentThree()
2025-07-22 08:23:58.390  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/View/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsDetail(java.lang.String)
2025-07-22 08:23:58.390  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/search.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectMapping(java.lang.String)
2025-07-22 08:23:58.390  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schoolhouse/{id:^[-?\d?_?]+$} || /schoolhouse/{id:^[-?\d?_?]+$}/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.schoolListMapping(java.lang.String)
2025-07-22 08:23:58.390  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/JingZhuangHouseDetail.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decoration(java.lang.Integer)
2025-07-22 08:23:58.390  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decoration/{projectId:[0-9]+}-{projectType:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decorationMapping(java.lang.String,java.lang.String)
2025-07-22 08:23:58.390  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Sitemap.html],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.AddComment()
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/AddComment.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.AddComment(java.lang.String,java.lang.String)
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Index],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangIndex()
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/About || /About/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangAbouts(java.lang.String)
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Help || /Help/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangHelps(java.lang.String)
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap1.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseMap()
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap2.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondMap()
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap3.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.syRentMap()
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mVideosList()
2025-07-22 08:23:58.391  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/xegj.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mKeeperList()
2025-07-22 08:23:58.392  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/xelist.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mKeeperMoreList()
2025-07-22 08:23:58.392  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/jjrsf/{a}-{b}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mAgentShopList(java.lang.String,java.lang.String)
2025-07-22 08:23:58.392  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzsc{a}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPointsMall(java.lang.String)
2025-07-22 08:23:58.392  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzsc/{a}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPointsMallDetail(java.lang.String)
2025-07-22 08:23:58.392  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzgl.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mStrategyList()
2025-07-22 08:23:58.392  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/publish || /pubSale.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishSecondList()
2025-07-22 08:23:58.392  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pubRent.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishRentList()
2025-07-22 08:23:58.392  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pubShop.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishShopsList()
2025-07-22 08:23:58.393  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalnew.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindHouseList()
2025-07-22 08:23:58.393  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalsale.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindSecondList()
2025-07-22 08:23:58.393  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalRent.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindRentList()
2025-07-22 08:23:58.393  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalShop.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindShopList()
2025-07-22 08:23:58.393  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/kfztc.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mLookForHouseList()
2025-07-22 08:23:58.393  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/ghdb.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mGuoHuDaiBanList()
2025-07-22 08:23:58.393  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1l || /fang1l/ || /fang1l/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseList(java.lang.String)
2025-07-22 08:23:58.393  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseDetail(java.lang.String)
2025-07-22 08:23:58.394  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1dt || /fang1dt/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseSubwayList(java.lang.String)
2025-07-22 08:23:58.394  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1xq || /fang1xq/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseSchoolList(java.lang.String)
2025-07-22 08:23:58.394  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorates || /decorates/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseDecorationsList(java.lang.String)
2025-07-22 08:23:58.394  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorate/{projectId}-{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decorationDetail(java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.394  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/school/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.schoolDetail(java.lang.Integer)
2025-07-22 08:23:58.394  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/nphotos/{projectId}-{photoType}-{projectType}.htm || /nphotos/{projectId}-{photoType}-{projectType}-{photoId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectPhoto(java.lang.Integer,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.394  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/dy || /fang1/{projectId}-{projectType}/dy-{infoType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.dynamicInfo(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.395  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/appraise.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectComment(java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.395  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/ask.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectAsk(java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.395  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/layout/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLayoutInfos(java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.395  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang2 || /fang2/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondList(java.lang.String)
2025-07-22 08:23:58.395  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang2/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondDetail(java.lang.String)
2025-07-22 08:23:58.395  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang3/{a} || /fang3],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mRentList(java.lang.String)
2025-07-22 08:23:58.396  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang3/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mRentDetail(java.lang.String)
2025-07-22 08:23:58.396  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang4 || /fang4/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mShopsList(java.lang.String)
2025-07-22 08:23:58.396  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang4/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mShopDetail(java.lang.String)
2025-07-22 08:23:58.396  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang5 || /fang5/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.oldOffice(java.lang.String)
2025-07-22 08:23:58.396  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang5/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.oldOfficeDetail(java.lang.Integer)
2025-07-22 08:23:58.396  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoIndex.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoInfo()
2025-07-22 08:23:58.397  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoByType{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoList(java.lang.Integer)
2025-07-22 08:23:58.397  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/audio.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.audioList()
2025-07-22 08:23:58.397  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sub/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSubDetail(java.lang.String)
2025-07-22 08:23:58.397  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/txnSecond],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.dealInfos()
2025-07-22 08:23:58.397  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/pic720.htm || /fang1/{projectId}-{projectType}/pic720/{panId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.panoramic(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.397  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/project1l || /project1l/ || /project1l/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newProjectList(java.lang.String)
2025-07-22 08:23:58.397  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/dy/-n{page:[0-9]+} || /tourist/{id}/dy-{dyType}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristDyInfos(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.398  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/ask/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristAsks(java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.398  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/comment/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristComment(java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.398  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:[0-9]+}/layout.htm || /tourist/{projectId:[0-9]+}/layout/pid{projectId:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristLayout(java.lang.Integer)
2025-07-22 08:23:58.398  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/formulate/house.aspx || /formulate//formulate/house.aspx{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.helpSearch(java.lang.String)
2025-07-22 08:23:58.398  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.residenceDetailMapping(java.lang.Integer)
2025-07-22 08:23:58.398  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaDetailMapping(java.lang.Integer)
2025-07-22 08:23:58.398  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeDetailMapping(java.lang.Integer)
2025-07-22 08:23:58.399  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealer/house/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.vipDetailMapping(java.lang.Integer)
2025-07-22 08:23:58.399  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/Default.aspx || /house/ || /house || /house/{type:[-?\d+_]{5,}}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newHouseMapping()
2025-07-22 08:23:58.399  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/foreignhouse/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.foregionHouseMapping()
2025-07-22 08:23:58.399  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/shops/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopsMapping()
2025-07-22 08:23:58.399  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office || /office/ || /office/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeMapping()
2025-07-22 08:23:58.399  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/ || /villa],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaMapping()
2025-07-22 08:23:58.399  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/view/{path}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityMapping(java.lang.String)
2025-07-22 08:23:58.399  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/marketing/default.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityMapping()
2025-07-22 08:23:58.400  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housestyle.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewVillas(java.lang.String)
2025-07-22 08:23:58.400  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housestyle/{pid:^\d+$} || /house/list/{pid:^\d+$} || /house/list/{pid}_{lid} || /house/listd/{pid}_{lid}_{hid}_{rid}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLayouts(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.400  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/jingzhuang/s/ || /jingzhuang/s/*],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewDecorations()
2025-07-22 08:23:58.400  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Brand/s/ || /Brand/s/*],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewBrands()
2025-07-22 08:23:58.400  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/villa_details/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaInfoMapping(java.lang.Integer)
2025-07-22 08:23:58.400  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/subway || /subway/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewSubways()
2025-07-22 08:23:58.400  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/school || /schoolhouse || /school/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewSchools()
2025-07-22 08:23:58.400  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/logpay || /logpay/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLowPays()
2025-07-22 08:23:58.401  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housemap/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewNewHouseMap()
2025-07-22 08:23:58.401  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/info/{projectId:[0-9]+}-{projectType:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseInfoMapping(java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.401  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/info/{id:^\d+} || /house/info/{id:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseInfoMapping(java.lang.Integer)
2025-07-22 08:23:58.401  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/layout/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getRoomListMapping(java.lang.String)
2025-07-22 08:23:58.402  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/layout/{projectId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaLayout(java.lang.String)
2025-07-22 08:23:58.402  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/villastyle/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewVillaLayout(java.lang.Integer)
2025-07-22 08:23:58.402  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/sublayout/{projectId}-{layId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaSubLayout(java.lang.String,java.lang.String)
2025-07-22 08:23:58.402  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/online/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resOnlineMapping(java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.402  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/{b}/{c}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.403  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/photo/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resPhoto(java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.403  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id}#xmzx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resAsk(java.lang.Integer)
2025-07-22 08:23:58.403  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/plan/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewPlan(java.lang.Integer)
2025-07-22 08:23:58.404  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/ || /UHouse/shangpu || /uhouse/shangpu/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopList(java.lang.String)
2025-07-22 08:23:58.404  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsList(java.lang.Integer)
2025-07-22 08:23:58.404  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/FeaturedList/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsList(java.lang.String)
2025-07-22 08:23:58.404  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/0929/pp1/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentOne()
2025-07-22 08:23:58.404  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/1231/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentTwo()
2025-07-22 08:23:58.404  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/{c}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event1(java.lang.String,java.lang.String)
2025-07-22 08:23:58.405  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event2(java.lang.String)
2025-07-22 08:23:58.405  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2010/1208/default.html],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event3()
2025-07-22 08:23:58.405  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/village/{para}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.village(java.lang.String)
2025-07-22 08:23:58.405  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Join || /Join/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangJoin(java.lang.String)
2025-07-22 08:23:58.405  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brand],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.brand()
2025-07-22 08:23:58.405  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sub],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSubList()
2025-07-22 08:23:58.407  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agent/{type}/{agentId:^[0-9]+$}/{params} || /agent/{type}/{agentId:^[0-9]+$}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.agentHouse(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.407  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberTopListOnload]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentController.memberTopListOnload(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.407  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberTopList || /memberTopList/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.memberTopList(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.407  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/findAgent/{params} || /findAgent],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.findAgent(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.407  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/findGoldAgent],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.findGoldAgent(org.springframework.ui.Model)
2025-07-22 08:23:58.407  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/closeShop],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.closeShop(org.springframework.ui.Model)
2025-07-22 08:23:58.407  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agent/intermediary/{type}/{companyId}/{params} || /agent/intermediary/{type}/{companyId}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.agentIntermediary(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.408  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewRecommandYiAgent],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentController.viewRecommandYiAgent(java.lang.Integer)
2025-07-22 08:23:58.408  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentIntermediary/{type}/{agentIntermediaryId}/{params} || /agentIntermediary/{type}/{agentIntermediaryId}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.intermediary(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.408  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentRecruit/{params} || /agentRecruit],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.agentRecruit(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.408  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/companyDetail/{companyId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.getSeDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.409  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/positionDetail/{positionId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.getPositionDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.409  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommonMemberResume],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentRecruitController.getCommonMemberResume(java.lang.String)
2025-07-22 08:23:58.409  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/applyPosition],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentRecruitController.getPosition(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.411  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/block],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.showBlockPage()
2025-07-22 08:23:58.412  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/temp],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.showTempPage(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.412  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyBlocker],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.getVerifycode(javax.servlet.http.HttpServletRequest,java.lang.Integer)
2025-07-22 08:23:58.412  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getSysTime],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.BlackListController.getSysTime()
2025-07-22 08:23:58.413  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyImageCode],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.BlackListController.verifyImageCode(javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.Integer,java.lang.Long) throws java.io.IOException
2025-07-22 08:23:58.413  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uploadNewCommentPic],methods=[POST]}" onto public void com.fangxiaoer.controller.CommonPicController.photoUpload(javax.servlet.http.HttpServletResponse,org.springframework.web.multipart.MultipartFile,java.lang.String)
2025-07-22 08:23:58.413  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uploadHousePic],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.CommonPicController.uploadHousePic(org.springframework.web.multipart.MultipartFile,java.lang.String)
2025-07-22 08:23:58.413  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gotoContrastHouse]}" onto public java.lang.String com.fangxiaoer.controller.ContrastController.gotoContrastHouse(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.414  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.addContrastHouse(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.414  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.getContrastHouse(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-07-22 08:23:58.414  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/deleteContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.deleteContrastHouse(java.lang.String,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.414  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/deleteAllContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.deleteAllContrastHouse(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.414  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewHouse],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.ContrastController.search(java.lang.String,java.lang.String)
2025-07-22 08:23:58.415  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentsingls || /rentsingls/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getRentSingls(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveRentSingls],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveRentSingls(com.fangxiaoer.model.RentHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shopsell || /shopsell/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopSell(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopSell],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopSell(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shoprent || /shoprent/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopRent],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopRent(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shoptransfer || /shoptransfer/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopTransfer(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopTransfer],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopTransfer(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getPlates],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.FreePublishController.getPlates(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchPhone],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchPhone(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchUser],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchUser(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.416  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSub],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchSub(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.417  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeSell || /officeSell/{shopId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getOfficeSell(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.417  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeRent || /officeRent/{shopId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getOfficeRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.417  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveOffice || /saveOffice/{houseID}],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.FreePublishController.getsaveOffice(com.fangxiaoer.model.OfficeEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.417  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOfficeDictionary],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.FreePublishController.getOfficeDictionary(java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.417  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secondPublish],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.download_app()
2025-07-22 08:23:58.417  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/publishRealEstate],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.publishRealEstate(com.fangxiaoer.model.RealEstate,javax.servlet.http.HttpServletRequest,java.lang.Integer)
2025-07-22 08:23:58.417  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkRealEstate],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.checkRealEstate(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.418  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRealEstateInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.getRealEstateInfo(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.418  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realRent/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealRent(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.418  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realShop/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealShop(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.418  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realOffice/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealOffice(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.418  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/entrustedLease],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.entrustReleaseHouse()
2025-07-22 08:23:58.418  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/seekOrder || /seekOrder.htm]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.seekOrder(org.springframework.ui.Model,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.419  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/seekOrder/{houseType}/ || /seekOrder/{houseType}/{params}]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.seekOrderList(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.419  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/new_secondPublish || /secondPublish/{houseID} || /myPublish/{check}/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getSecondHouse(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.419  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveSecondHouse],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveSecondHouse(com.fangxiaoer.model.SaleHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.419  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentwhole || /rentwhole/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getRentWhole(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.419  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveRentWhole],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveRentWhole(com.fangxiaoer.model.RentHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.419  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyIt/{houseId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.verifyIt(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.420  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchs],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.FreePublishController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.420  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/freeServiceIndex],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreeServiceController.freeServiceIndex(org.springframework.ui.Model)
2025-07-22 08:23:58.420  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/freeService/{companyId:[0-9]+}-{serviceId:[0-9]+}.htm || /freeService/{companyId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreeServiceController.freeServiceDetial(org.springframework.ui.Model,java.lang.String,java.lang.String)
2025-07-22 08:23:58.420  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreeServiceController.serviceGuide(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:23:58.420  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceTypes],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreeServiceController.getServiceTypes(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:23:58.421  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveGuide]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.saveGuide(com.fangxiaoer.model.Guide)
2025-07-22 08:23:58.421  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifySmsCode],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.verifySmsCode(java.lang.String,java.lang.String) throws java.io.IOException
2025-07-22 08:23:58.421  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getPayOrder],methods=[POST]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.getPayOrder(java.util.HashMap)
2025-07-22 08:23:58.421  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.orderGuide(java.lang.String,java.lang.String,java.lang.Integer,java.lang.String,java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.421  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkGuideState],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.checkGuideState(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/cancelGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.cancelGuide(java.lang.String,java.lang.String,java.lang.Integer,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveHouseOrder]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.saveOrder(com.fangxiaoer.model.Guide)
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addSessionPopup],methods=[POST]}" onto public void com.fangxiaoer.controller.GuideController.saveSessionPopup(javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuarantee#kgdp || /serviceGuarantee#gfdjt || /serviceGuarantee#mfgj || /serviceGuarantee],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.GuideController.serviceGuarantee()
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuarantee],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Integer> com.fangxiaoer.controller.GuideController.serviceGuaranteeInfo()
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAsk],methods=[POST],produces=[application/json]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.addAsk(java.util.HashMap)
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/loan/{params} || /toLoan],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.GuideController.toLoan(java.lang.String)
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveLoan],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.saveLoan(com.fangxiaoer.model.Loan,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.422  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addMessageToAgent],methods=[POST],produces=[application/json]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.add(java.util.HashMap)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchClickAnalyze]}" onto public void com.fangxiaoer.controller.HomeController.searchClickAnalyze(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fuzzySearchSubd],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLike(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commonSearch],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLikes(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/redirectToMyOrAgent],methods=[GET]}" onto public void com.fangxiaoer.controller.HomeController.redirectToMyOrAgent(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/subscription],methods=[POST]}" onto public void com.fangxiaoer.controller.HomeController.subscription(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/download],methods=[GET || POST]}" onto public void com.fangxiaoer.controller.HomeController.downloadFile(javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewProject],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLikeMap(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewProjectMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HomeController.queryLikeMap(java.util.HashMap)
2025-07-22 08:23:58.423  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchStationMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HomeController.searchStationMap(java.util.HashMap)
2025-07-22 08:23:58.424  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HomeController.index(org.springframework.ui.Model,org.springframework.mobile.device.Device,java.lang.String)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeper{type:[0-9]+}.htm || /houseKeeper{type:[0-9]+}/{page}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeepersInfoByType(org.springframework.ui.Model,javax.servlet.http.HttpSession,java.lang.String,java.lang.String)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pointsMall],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getPointsMall(org.springframework.ui.Model)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pointsMall/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getPointsMallBySort(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activities],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivities(org.springframework.ui.Model)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activities/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivities(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activity/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivity(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/keeperMore/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeeperMore(org.springframework.ui.Model)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/skipToVip],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.skipToVip(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.425  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/status],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getStickOrderStatus(java.lang.String)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeper],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeepers(org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getDaelstories],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseCenterController.getDaelstories(java.util.HashMap<java.lang.String, java.lang.String>)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/festivalNews/{params} || /festivalNews],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getFestivalNews(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/afestivalNews/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getFestivalNewsDetial(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewLookHouse || /viewLookHouse/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.viewLookHouse(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/lookHouseDetail/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.lookHouseDetail(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/keeperMember || /keeperMember/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeeperMember(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getbrandCompany(org.springframework.ui.Model)
2025-07-22 08:23:58.426  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany/{brandId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getCompanyDetial(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany2/{brandId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getCompanyDetial2(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompanyNews],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseCenterController.getCompanyNews(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewBrandMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.viewBrandMap(java.lang.Integer)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeperTour],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getHouseKeepersTour(java.lang.String,java.lang.String)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeperAsk],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getHouseKeepersAsk(java.lang.String)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/strategy/{params} || /strategy],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getStrategy(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/{inputParams}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getGoods(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/payVip],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.payVip(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpSession,java.lang.String)
2025-07-22 08:23:58.427  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/search],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HouseCenterController.search(org.springframework.ui.Model,java.lang.String) throws java.io.UnsupportedEncodingException
2025-07-22 08:23:58.428  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/takeOrder],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getOrder(java.lang.Integer,java.lang.Integer,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.430  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houses/{params} || /houses || /subways/{params} || /subways || /exits/{params} || /exits || /lowpays/{params} || /lowpays || /brands/{params} || /brands],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showHouseList(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.430  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorations || /decorations/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showDecorationList(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.430  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schoolhouses/{params}]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.schoolhouse(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.430  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}.htm || /house/{projectId}-{projectType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showHouseDetail(javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.430  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/layout.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/layout/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getRoomList(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.430  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/appraise{params}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.houseAppraiseTest(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.430  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/news.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/{page:[0-9]+}.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/dy{dyType}.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/dy{dyType}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaNews(java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/pic720.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/pic720/{panId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getPicture(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/onlines/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getOnlineInfo(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/online/{projectId}-{projectType}-{roomId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getRoomDetail(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commentAgent/{projectId}/{projectType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.commentAgent(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/editComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.editComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/editAgentComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.editAgentComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tjf],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.viewParticularHouse()
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewDealRankingList],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.viewDealRankingList(org.springframework.ui.Model)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommentCount],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.getCommentCount(com.fangxiaoer.model.search.CommentCountModel)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAskCount],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.getAskCount(java.lang.String)
2025-07-22 08:23:58.431  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommentInfo],methods=[POST],consumes=[*/*],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.CommentHandler(com.fangxiaoer.model.search.CommentSearchModel)
2025-07-22 08:23:58.432  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAskInfo],methods=[POST],consumes=[*/*],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.AskHandler(com.fangxiaoer.model.search.AskSearchModel)
2025-07-22 08:23:58.432  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOnlineJson],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HouseController.getOnlineJson(java.lang.String)
2025-07-22 08:23:58.432  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveComment],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveComment(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 08:23:58.432  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveCommentAgent],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveCommentAgent(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 08:23:58.432  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveReply],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveReply(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 08:23:58.432  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderActivity],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.orderActivity(java.lang.String,java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.432  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAdvertisementList],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getAdvertisementList(java.lang.String,java.lang.String)
2025-07-22 08:23:58.432  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fetchNearByProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.fetchNearByProject(java.lang.Integer,java.math.BigDecimal,java.math.BigDecimal,java.lang.Integer)
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId}-{projectType}/index/{page:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaForComment(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/relasionHousePush],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getRelasionHousePush(java.lang.String,java.math.BigDecimal,java.math.BigDecimal)
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/projectRank/{type:^[0-9]+$} || /projectRank/{type}_{regionId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.videoSearch(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxACodeByAgentId],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxACodeByMemberId(java.lang.String)
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxACode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxACode(java.lang.String)
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxSecCode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxSecCode(java.lang.String)
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getNewWxCode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getNewWxCode(java.lang.String)
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/contrastLayout],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.contrastLayout()
2025-07-22 08:23:58.433  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/doContrastLayout],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.doContrastLayout(java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.434  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRecommendLayout],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getRecommendLayout(java.lang.Integer)
2025-07-22 08:23:58.434  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manageCollect],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.manageCollect(java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 08:23:58.434  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewNewSchoolList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.viewNewSchoolList(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.434  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSchoolListForMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.searchSchoolListForMap(java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Double)
2025-07-22 08:23:58.434  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewNewSchoolHouseList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.viewNewSchoolHouseList(java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Integer)
2025-07-22 08:23:58.434  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/decoration.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.decorationDetail(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.434  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAgentInfo],methods=[POST],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getAgentInfo(java.lang.String)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/helpSearch],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.HouseController.helpSearch(com.fangxiaoer.model.Guide,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/sublayout/{layId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaSubLayout(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decolayout/{layoutId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.jzDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schools || /schools/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.schools(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/info.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getBase(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/ask.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/ask/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.houseAsk(java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/say.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/say/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getSay(java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:\d+}-{projectType:\d+}/album.htm || /house/{projectId:\d+}-{projectType:\d+}/album/{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getPhoto(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.435  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comment/{projectId}/{projectType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.comment(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.436  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housingprice/{params} || /housingprice],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HousingPriceController.housingprice(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.436  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housingpriceDetail],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HousingPriceController.freeEvaluationDetail(com.fangxiaoer.model.HousingPriceEntity,org.springframework.ui.Model,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.437  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/calForLoan],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.calForLoan(java.lang.Double,java.lang.Double,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Integer,java.lang.Double)
2025-07-22 08:23:58.437  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewLoanFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.viewLoanFilter(java.lang.Integer,java.lang.Double)
2025-07-22 08:23:58.437  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/LPRHistoryFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.LPRHistoryFilter()
2025-07-22 08:23:58.437  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/bigdata],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.bigData(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.438  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imToAgent],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.imToAgent(java.lang.String)
2025-07-22 08:23:58.438  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imRecruit/{params} || /imRecruit],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.imRecruit(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.438  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/serviceIM],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.serviceIM(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.438  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/service/{param} || /im/service],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.serviceIM1(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.438  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/new/service/{serviceType}],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ImController.serviceIMNew(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String)
2025-07-22 08:23:58.438  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/{params} || /im],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.housingprice(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.438  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/cloudMsg],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.cloudMsg(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.439  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getMyIm],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.getMyIm(java.lang.String)
2025-07-22 08:23:58.439  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imPolt/{params} || /imPolt],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.imPolt(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.439  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/chat/{memberId} || /im/chat],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.agentIM(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:23:58.439  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/needSecond/{param}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NeedHouseController.needSecond(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.439  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/needRent/{param}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NeedHouseController.needRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.440  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoSearch],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.getVideosSearchResultList(java.util.HashMap)
2025-07-22 08:23:58.440  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/{params} || /news],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVillages(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.440  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getNewsDetail(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.440  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/getNewsCommentsAndLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.getNewsCommentsAndLike(javax.servlet.http.HttpSession,java.lang.Integer)
2025-07-22 08:23:58.440  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videos/{params} || /videos],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideosList(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{videoId:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideoDetail(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{videoId:[0-9]+}-{projectId:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.viewVideoWithProject(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/audios/{params} || /audios],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAudiosList2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentnews/{params} || /agentnews],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAgentNewsList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentnews/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAgentNewsDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoSearchlist/{params} || /videoSearchlist],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideosResultList(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAudioNum],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.addAudioNum(java.util.HashMap)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manageMyCollection],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.manageMyCollection(java.util.HashMap)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/newsCommentLike],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.newsCommentLike(java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/setVideoLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.setVideoLike(java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.441  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsComment],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsComment(java.lang.String,java.lang.Integer,java.lang.String)
2025-07-22 08:23:58.442  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsReply],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsReply(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.442  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsCommentLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsCommentLike(java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.442  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewSimilarProjectDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewSimilarProjectDetail(java.lang.Integer)
2025-07-22 08:23:58.442  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewContrastProjects],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewContrastProjects(java.lang.Integer)
2025-07-22 08:23:58.442  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/liveDetail/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.liveDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.442  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewWeizanList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewWeizanList(com.fangxiaoer.model.WeizanSearch)
2025-07-22 08:23:58.442  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/projectnews || /projectnews/{params}]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.newProjectDynamics(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.442  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/liveList],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.liveList()
2025-07-22 08:23:58.443  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/index.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotOverview(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.443  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages2/{subId}/index.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotOverview2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.443  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotSecondVillage/{params} || /saleVillages/{subId:[0-9]+}/plotSecondVillage || /saleVillages/{subId:[0-9]+}/plotSecondVillage/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotSecondHouse(java.lang.String,java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.443  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotRentVillage/{params} || /saleVillages/{subId:[0-9]+}/plotRentVillage || /saleVillages/{subId:[0-9]+}/plotRentVillage/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotRentHouse(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.443  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/plotRealImage.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotRealImage(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.443  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{askId:[0-9]+}/getAskDetail.htm || /saleVillages/{askId:[0-9]+}/getAskDetail/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getAskDetail(java.lang.String,java.lang.Integer,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.443  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/forAddPlotAsk.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.forAddPlotAsk(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAskWithSub],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.PlotController.addAskWithSub(java.lang.Integer,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotExpertInterpretation.htm || /plotExpertInterpretation/{subId:[0-9]+}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotExpertInterpretation(java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/getAskNoReply.htm || /saleVillages/{subId:[0-9]+}/getAskNoReply/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getAskNoReply(java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/plotSupport.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getPlotSupport(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages2/{subId}/plotSupport.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getPlotSupport2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/msgIsRead],methods=[POST]}" onto public void com.fangxiaoer.controller.PlotController.msgIsRead(java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/acceptReplay],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.PlotController.acceptReplay(java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotAsk.htm || /saleVillages/{subId:[0-9]+}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotAsk(java.lang.String,java.lang.Integer,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.444  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addReply],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.PlotController.addReply(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.445  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villages/{params} || /villages],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getVillages(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.445  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rents/{params} || /rents],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.showRentsHouseList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.445  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealRents/{params} || /dealRents],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.dealRentsList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.445  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealRent/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getDealRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.445  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentmap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRentMap(org.springframework.ui.Model)
2025-07-22 08:23:58.445  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentOneInfo],methods=[POST]}" onto public java.util.List com.fangxiaoer.controller.RentController.searchOne(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.446  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryRentMap(java.util.HashMap)
2025-07-22 08:23:58.446  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryRentMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:23:58.446  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/queryLocationByAddress],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryLocationByAddress(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:23:58.446  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentMapDualInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.rentStationMap(java.util.HashMap)
2025-07-22 08:23:58.446  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRentMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.getRentMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:23:58.446  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/village/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getVillage(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.446  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRent(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.446  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent2/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRent2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.447  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentByTitle],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.RentController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.447  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeProjects/{params} || /officeProjects],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.officeProjects(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.448  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeProject/{officeId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.officeProject(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.448  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/scriptoriums/{params} || /scriptoriums],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.shops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.448  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/scriptorium/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.shop(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.449  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberIdCardAuthentication],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.memberIdCardAuthentication(java.util.HashMap,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.450  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/confirmAuthenticationInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.confirmAuthenticationInfo(java.util.HashMap,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.450  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleHouses/{params} || /saleHouses],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSecondHouseList(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.450  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchOneInfo],methods=[POST]}" onto public java.util.List com.fangxiaoer.controller.SecondController.searchOne(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.450  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealSales/{params} || /dealSales],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getDealSecondList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.450  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salehouse2/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeDetail2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.450  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealSale/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getDealSaleDetail(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.450  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{params} || /saleVillages],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeVillage(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.450  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleHouses/subway/{input}]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeSubwayList(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.451  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salemap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSaleMap()
2025-07-22 08:23:58.451  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salemap2],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSaleMap2()
2025-07-22 08:23:58.451  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSecMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySecMap(java.util.HashMap)
2025-07-22 08:23:58.451  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSecMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySecMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:23:58.451  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchForSubway],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.SecondController.queryForSubway(java.lang.String)
2025-07-22 08:23:58.451  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleMetroStation],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secStationMap(java.util.HashMap)
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleMetroHouseList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secStationMapHouse(java.util.HashMap)
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secPlotDetailList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secMapPlot(java.lang.String)
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secondhouse/entrustReleaseHouse],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.entrustReleaseHouse()
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salehouse/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/judicial.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.Judicial()
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dashuju],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.Dashuju()
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/querySubDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySub(java.lang.String)
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/falsity],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.falsity(java.lang.String,java.lang.String,java.lang.Integer,java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.452  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchByTitle],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.SecondController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.453  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealShops/{params} || /dealShops],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.dealShops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.453  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shops/{params} || /shops],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.shops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:23:58.453  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shop/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.shop(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.453  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealShop/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.dealShop(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.454  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkSignin]}" onto public java.lang.String com.fangxiaoer.controller.SignController.checkSignin(javax.servlet.http.HttpSession)
2025-07-22 08:23:58.454  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sendSmsCode],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.sendSMSCode(java.lang.String,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.454  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/quickregist],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.quickRegist(java.lang.String,java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,java.lang.String)
2025-07-22 08:23:58.454  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkPhone],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.SignController.checkPhone(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.454  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkPasscode],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.SignController.checkPasscode(java.lang.String,java.lang.String)
2025-07-22 08:23:58.454  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkOCRIDCard],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.checkOCRIDCard(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:23:58.454  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/signin],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.SignController.login(java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpSession)
2025-07-22 08:23:58.455  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/login],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.login(java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String)
2025-07-22 08:23:58.455  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/quit || /Action/logout.aspx]}" onto public java.lang.String com.fangxiaoer.controller.SignController.logout(javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.455  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/signin]}" onto public java.lang.String com.fangxiaoer.controller.SignController.signin(org.springframework.ui.Model,java.lang.String,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpSession,java.lang.String)
2025-07-22 08:23:58.455  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/regist]}" onto public java.lang.String com.fangxiaoer.controller.SignController.regist(org.springframework.ui.Model)
2025-07-22 08:23:58.455  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/transit]}" onto public java.lang.String com.fangxiaoer.controller.SignController.transit(org.springframework.ui.Model)
2025-07-22 08:23:58.455  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/{dir}/{path}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.staticPage(java.lang.String,java.lang.String)
2025-07-22 08:23:58.456  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/{path}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.staticPage(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.456  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fastSeek]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.fastSeek(org.springframework.ui.Model)
2025-07-22 08:23:58.456  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/goufangnenglipinggu.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Gufangnenglipinggu()
2025-07-22 08:23:58.456  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/tiqianhuankuan.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Huankuan()
2025-07-22 08:23:58.456  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/loan_calculator]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Daikuan()
2025-07-22 08:23:58.456  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shuifei.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Shuifei()
2025-07-22 08:23:58.457  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTouristIndex(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.457  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tNews.htm || /tNews{id}.htm || /tNews{id}/-n{page:[0-9]+}]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTNewsList(java.lang.Integer,org.springframework.ui.Model,java.lang.String,java.lang.Integer)
2025-07-22 08:23:58.457  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/touristList || /touristList/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTouristList(org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:23:58.457  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/touristGuideCount],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.touristGuideCount()
2025-07-22 08:23:58.457  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectDetail(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.458  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tNews/{id}.htm]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewNewsDetail(java.lang.Integer,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:23:58.458  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/info.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectInfo(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.458  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/ask.htm || /tourist/{id}/ask/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewAskList(java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:23:58.458  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/comment.htm || /tourist/{id}/comment/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristCommentList(java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:23:58.458  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/dy.htm || /tourist/{id}/dy-{dyType}.htm || /tourist/{id}/dy/{page:[0-9]+}.htm || /tourist/{id}/dy-{dyType}/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectDy(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:23:58.458  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/layout-{id}/{params} || /tourist/layout-{id}/{params}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewLayoutDetail(java.lang.Integer,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:23:58.458  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:\d+}/album.htm || /tourist/{projectId:\d+}/album/{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.fetchProjectPics(java.lang.Integer,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:23:58.458  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:\d+}/pushcomment],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.commentProject(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.459  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getTouristFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.getTouristFilter()
2025-07-22 08:23:58.459  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addTouristOrder],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.addTouristOrder(com.fangxiaoer.model.TouristOrder)
2025-07-22 08:23:58.459  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.orderProject(java.lang.String)
2025-07-22 08:23:58.460  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/editComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.editComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:23:58.460  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/saveComment],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.TouristController.saveComment(java.util.HashMap)
2025-07-22 08:23:58.461  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addTouristAsk],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.addAsk(java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 08:23:58.462  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error]}" onto public java.lang.String com.fangxiaoer.error.CustomErrorController.generalError(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,org.springframework.ui.Model)
2025-07-22 08:23:58.533  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 08:23:58.533  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 08:23:58.563  INFO 92258 --- [restartedMain] .m.m.a.ExceptionHandlerExceptionResolver : Detected @ExceptionHandler methods in exceptionHandler
2025-07-22 08:23:58.620  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 08:25:34.148  INFO 92258 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : FrameworkServlet 'dispatcherServlet': initialization started
2025-07-22 08:25:34.169  INFO 92258 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : FrameworkServlet 'dispatcherServlet': initialization completed in 21 ms
2025-07-22 08:30:00.001 ERROR 92258 --- [pool-3-thread-1] com.fangxiaoer.common.IPMonitor          : =======start to clean======= Block size: 0, Frequency: 2
2025-07-22 08:36:40.853  INFO 92258 --- [localhost-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 677 ms
2025-07-22 08:36:41.445  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerAdapter : Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@11fcfaf2: startup date [Tue Jul 22 08:36:40 CST 2025]; root of context hierarchy
2025-07-22 08:36:41.473  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/marketing/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.Marketing(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.473  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/helpSearch || /helpSearch.htm]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.helpSearch(org.springframework.ui.Model,java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.473  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewActivity2],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewActivity(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.474  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewManagerDiscount || /viewManagerDiscount/{params}]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.viewManagerDiscount(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.474  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveCollection],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saveCollection(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.474  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pay_getQR],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.pay_getQR(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.474  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderPaySuccess],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewPayOrderDetail(java.lang.String)
2025-07-22 08:36:41.474  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/contactInfo/{orderId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.viewContract(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.474  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getLayOut],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getLayOut(java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.474  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getBuildName],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getLayOut(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/createOrder],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.createOrder(java.lang.String,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOrderDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getOrderDetail(java.lang.String)
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/payForSuccess],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.payForSuccess(java.lang.String)
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkIsSign],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.checkIsSign(java.lang.String)
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleOthersFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleOthersFilter()
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleInfoFilter()
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.rentInfoFilter()
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shopInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.shopInfoFilter()
2025-07-22 08:36:41.475  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleFeatureFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleFeatureFilter()
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentFeatureFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.rentFeatureFilter()
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/newRegionFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.newRegionFilter()
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/usedRegionFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.usedRegionFilter()
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/layoutFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.layoutFilter()
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/relationShipFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.relationShipFilter()
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewSubsidyForProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewSubsidyForProject(java.lang.String)
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fetchSubsidyForProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.fetchSubsidyForProject(java.lang.String,java.lang.Integer,java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/discount/{input} || /discount]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.discount(org.springframework.ui.Model,java.lang.String,java.lang.String)
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getUnit],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getUnit(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRoom],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getRoom(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.476  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderPay],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.orderPay(java.lang.String,java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 08:36:41.481  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/sale.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishshopSUPdateMapping(java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/rent.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopRUPdateMapping(java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/attorn.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopTUPdateMapping(java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/view/{houseId} || /UHouse/sale/SaleFullDetail/{houseId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseDetailMapping(java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/SubdistrictList || /UHouse/Subdistrict/SubdistrictList/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondHouseVillagesMapping()
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/online/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeOnlineMapping(java.lang.Integer)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/room/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.onlineDetailMapping(java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/photo/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officePhoto(java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/photo/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaPhoto(java.lang.Integer)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/picture/view/{projectType}_{projectId}_{photoType}_{photoId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaPhotoDetail(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/album/{projectId:\d+}-{projectType:\d+}.htm || /house/album/{projectId:\d+}-{projectType:\d+}-{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resAndOfficeAlbumMapping(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/photos/{projectId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaAlbumMapping(java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/appraise/{projectId:[0-9]+}-{projectType:[0-9]+}.htm{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseAppraiseMapping(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id}#xmpj],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resComment(java.lang.Integer)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id}#xmpj],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeComment(java.lang.Integer)
2025-07-22 08:36:41.482  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/ask/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/ask/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseAskMapping(java.lang.Integer,java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id}#xmzx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeAsk(java.lang.Integer)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/news/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/news/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaNewsMapping(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/news/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaSaleInfo(java.lang.Integer)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/news/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeSaleInfo(java.lang.Integer)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/news/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseNews(java.lang.String)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/say/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/say/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewHouseSayMapping(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/say/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewHouseSay(java.lang.Integer)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pic720/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /pic720/{projectId:[0-9]+}-{projectType:[0-9]+}-{panId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getPictureMapper(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activity]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mDiscountList()
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/view/{id} || /Uhouse/shangpu/view/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopDetial(java.lang.String)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/broker/{type}/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.agentHouse(java.lang.String,java.lang.String)
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/homeAct.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseActivityMapping()
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseKeeperMapping()
2025-07-22 08:36:41.483  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/integral.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.pointsMallMapping()
2025-07-22 08:36:41.484  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/strategy.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.strategyMapping()
2025-07-22 08:36:41.484  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/goods.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getGoodsMapping(java.lang.String)
2025-07-22 08:36:41.484  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/SaleHouse.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishSaleMapping(java.lang.String)
2025-07-22 08:36:41.484  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/rent_form_1.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishRentWMapping(java.lang.String)
2025-07-22 08:36:41.484  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/rent_form_2.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishRentSMapping(java.lang.String)
2025-07-22 08:36:41.484  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/sale.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopSMapping()
2025-07-22 08:36:41.484  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/rent.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopRMapping()
2025-07-22 08:36:41.485  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/attorn.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopTMapping()
2025-07-22 08:36:41.485  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/view/{path}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityDetailMapping(java.lang.String)
2025-07-22 08:36:41.485  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{value}_{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoMapping(java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.485  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsMapping(java.lang.String)
2025-07-22 08:36:41.485  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent//RentRegion/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsRegionMapping(java.lang.String)
2025-07-22 08:36:41.485  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/RentSubway/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsSubwayMapping(java.lang.String)
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/view/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentMapping(java.lang.String)
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/SubdistrictListRt/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.valligesMapping()
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/view/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.valligeMapping(java.lang.String)
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent/housemap/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentMapMapping()
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/goufangnenglipinggu.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.abilityMapping()
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/tiqianhuankuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.advanceMapping()
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/zuhedaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.assembleMapping()
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shangyedaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.businessMapping()
2025-07-22 08:36:41.486  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/gongjijindaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fundMapping()
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/gongjijindaikuanedupinggu.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fundAssessMapping()
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shuifei.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.tallageMapping()
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale || /UHouse/sale/ || /UHouse/sale/Region || /UHouse/sale/UHouseSubway || /UHouse/sale/Region/{param:^[-?\d?_?]+$} || /UHouse/sale/UHouseSubway/{param:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseListMapping(java.lang.String)
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/housemap/ || /UHouse/housemap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseMapMapping()
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/View/{para}_all],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsDetailList(java.lang.String)
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/baike.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.encyclopedia()
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/formulate/baike.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.encyclopediaB()
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/0929/ms/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentThree()
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/View/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsDetail(java.lang.String)
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/search.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectMapping(java.lang.String)
2025-07-22 08:36:41.487  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schoolhouse/{id:^[-?\d?_?]+$} || /schoolhouse/{id:^[-?\d?_?]+$}/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.schoolListMapping(java.lang.String)
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/JingZhuangHouseDetail.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decoration(java.lang.Integer)
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decoration/{projectId:[0-9]+}-{projectType:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decorationMapping(java.lang.String,java.lang.String)
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Sitemap.html],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.AddComment()
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/AddComment.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.AddComment(java.lang.String,java.lang.String)
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Index],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangIndex()
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/About || /About/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangAbouts(java.lang.String)
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Help || /Help/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangHelps(java.lang.String)
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap1.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseMap()
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap2.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondMap()
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap3.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.syRentMap()
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mVideosList()
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/xegj.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mKeeperList()
2025-07-22 08:36:41.488  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/xelist.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mKeeperMoreList()
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/jjrsf/{a}-{b}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mAgentShopList(java.lang.String,java.lang.String)
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzsc{a}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPointsMall(java.lang.String)
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzsc/{a}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPointsMallDetail(java.lang.String)
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzgl.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mStrategyList()
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/publish || /pubSale.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishSecondList()
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pubRent.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishRentList()
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pubShop.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishShopsList()
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalnew.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindHouseList()
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalsale.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindSecondList()
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalRent.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindRentList()
2025-07-22 08:36:41.489  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalShop.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindShopList()
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/kfztc.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mLookForHouseList()
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/ghdb.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mGuoHuDaiBanList()
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1l || /fang1l/ || /fang1l/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseList(java.lang.String)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseDetail(java.lang.String)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1dt || /fang1dt/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseSubwayList(java.lang.String)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1xq || /fang1xq/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseSchoolList(java.lang.String)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorates || /decorates/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseDecorationsList(java.lang.String)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorate/{projectId}-{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decorationDetail(java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/school/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.schoolDetail(java.lang.Integer)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/nphotos/{projectId}-{photoType}-{projectType}.htm || /nphotos/{projectId}-{photoType}-{projectType}-{photoId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectPhoto(java.lang.Integer,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/dy || /fang1/{projectId}-{projectType}/dy-{infoType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.dynamicInfo(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/appraise.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectComment(java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.490  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/ask.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectAsk(java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/layout/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLayoutInfos(java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang2 || /fang2/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondList(java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang2/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondDetail(java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang3/{a} || /fang3],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mRentList(java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang3/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mRentDetail(java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang4 || /fang4/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mShopsList(java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang4/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mShopDetail(java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang5 || /fang5/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.oldOffice(java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang5/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.oldOfficeDetail(java.lang.Integer)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoIndex.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoInfo()
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoByType{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoList(java.lang.Integer)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/audio.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.audioList()
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sub/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSubDetail(java.lang.String)
2025-07-22 08:36:41.491  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/txnSecond],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.dealInfos()
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/pic720.htm || /fang1/{projectId}-{projectType}/pic720/{panId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.panoramic(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/project1l || /project1l/ || /project1l/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newProjectList(java.lang.String)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/dy/-n{page:[0-9]+} || /tourist/{id}/dy-{dyType}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristDyInfos(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/ask/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristAsks(java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/comment/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristComment(java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:[0-9]+}/layout.htm || /tourist/{projectId:[0-9]+}/layout/pid{projectId:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristLayout(java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/formulate/house.aspx || /formulate//formulate/house.aspx{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.helpSearch(java.lang.String)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.residenceDetailMapping(java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaDetailMapping(java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeDetailMapping(java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealer/house/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.vipDetailMapping(java.lang.Integer)
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/Default.aspx || /house/ || /house || /house/{type:[-?\d+_]{5,}}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newHouseMapping()
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/foreignhouse/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.foregionHouseMapping()
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/shops/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopsMapping()
2025-07-22 08:36:41.492  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office || /office/ || /office/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeMapping()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/ || /villa],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaMapping()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/view/{path}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityMapping(java.lang.String)
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/marketing/default.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityMapping()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housestyle.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewVillas(java.lang.String)
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housestyle/{pid:^\d+$} || /house/list/{pid:^\d+$} || /house/list/{pid}_{lid} || /house/listd/{pid}_{lid}_{hid}_{rid}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLayouts(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/jingzhuang/s/ || /jingzhuang/s/*],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewDecorations()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Brand/s/ || /Brand/s/*],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewBrands()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/villa_details/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaInfoMapping(java.lang.Integer)
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/subway || /subway/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewSubways()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/school || /schoolhouse || /school/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewSchools()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/logpay || /logpay/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLowPays()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housemap/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewNewHouseMap()
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/info/{projectId:[0-9]+}-{projectType:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseInfoMapping(java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/info/{id:^\d+} || /house/info/{id:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseInfoMapping(java.lang.Integer)
2025-07-22 08:36:41.493  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/layout/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getRoomListMapping(java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/layout/{projectId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaLayout(java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/villastyle/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewVillaLayout(java.lang.Integer)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/sublayout/{projectId}-{layId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaSubLayout(java.lang.String,java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/online/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resOnlineMapping(java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/{b}/{c}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/photo/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resPhoto(java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id}#xmzx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resAsk(java.lang.Integer)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/plan/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewPlan(java.lang.Integer)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/ || /UHouse/shangpu || /uhouse/shangpu/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopList(java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsList(java.lang.Integer)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/FeaturedList/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsList(java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/0929/pp1/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentOne()
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/1231/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentTwo()
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/{c}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event1(java.lang.String,java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event2(java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2010/1208/default.html],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event3()
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/village/{para}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.village(java.lang.String)
2025-07-22 08:36:41.494  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Join || /Join/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangJoin(java.lang.String)
2025-07-22 08:36:41.495  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brand],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.brand()
2025-07-22 08:36:41.495  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sub],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSubList()
2025-07-22 08:36:41.495  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agent/{type}/{agentId:^[0-9]+$}/{params} || /agent/{type}/{agentId:^[0-9]+$}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.agentHouse(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.495  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberTopListOnload]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentController.memberTopListOnload(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.495  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberTopList || /memberTopList/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.memberTopList(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.495  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/findAgent/{params} || /findAgent],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.findAgent(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.495  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/findGoldAgent],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.findGoldAgent(org.springframework.ui.Model)
2025-07-22 08:36:41.496  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/closeShop],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.closeShop(org.springframework.ui.Model)
2025-07-22 08:36:41.496  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agent/intermediary/{type}/{companyId}/{params} || /agent/intermediary/{type}/{companyId}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.agentIntermediary(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.496  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewRecommandYiAgent],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentController.viewRecommandYiAgent(java.lang.Integer)
2025-07-22 08:36:41.496  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentIntermediary/{type}/{agentIntermediaryId}/{params} || /agentIntermediary/{type}/{agentIntermediaryId}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.intermediary(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.496  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentRecruit/{params} || /agentRecruit],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.agentRecruit(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.497  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/companyDetail/{companyId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.getSeDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.497  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/positionDetail/{positionId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.getPositionDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.497  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommonMemberResume],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentRecruitController.getCommonMemberResume(java.lang.String)
2025-07-22 08:36:41.497  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/applyPosition],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentRecruitController.getPosition(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.498  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/block],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.showBlockPage()
2025-07-22 08:36:41.498  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/temp],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.showTempPage(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.498  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyBlocker],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.getVerifycode(javax.servlet.http.HttpServletRequest,java.lang.Integer)
2025-07-22 08:36:41.498  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getSysTime],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.BlackListController.getSysTime()
2025-07-22 08:36:41.498  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyImageCode],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.BlackListController.verifyImageCode(javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.Integer,java.lang.Long) throws java.io.IOException
2025-07-22 08:36:41.498  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uploadNewCommentPic],methods=[POST]}" onto public void com.fangxiaoer.controller.CommonPicController.photoUpload(javax.servlet.http.HttpServletResponse,org.springframework.web.multipart.MultipartFile,java.lang.String)
2025-07-22 08:36:41.499  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uploadHousePic],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.CommonPicController.uploadHousePic(org.springframework.web.multipart.MultipartFile,java.lang.String)
2025-07-22 08:36:41.499  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gotoContrastHouse]}" onto public java.lang.String com.fangxiaoer.controller.ContrastController.gotoContrastHouse(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.499  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.addContrastHouse(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.499  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.getContrastHouse(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-07-22 08:36:41.499  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/deleteContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.deleteContrastHouse(java.lang.String,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.499  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/deleteAllContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.deleteAllContrastHouse(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.499  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewHouse],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.ContrastController.search(java.lang.String,java.lang.String)
2025-07-22 08:36:41.500  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentsingls || /rentsingls/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getRentSingls(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.500  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveRentSingls],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveRentSingls(com.fangxiaoer.model.RentHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.500  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shopsell || /shopsell/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopSell(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.500  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopSell],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopSell(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.500  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shoprent || /shoprent/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.501  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopRent],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopRent(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.501  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shoptransfer || /shoptransfer/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopTransfer(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.501  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopTransfer],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopTransfer(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.501  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getPlates],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.FreePublishController.getPlates(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.501  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchPhone],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchPhone(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.501  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchUser],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchUser(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.501  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSub],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchSub(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.501  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeSell || /officeSell/{shopId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getOfficeSell(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeRent || /officeRent/{shopId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getOfficeRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveOffice || /saveOffice/{houseID}],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.FreePublishController.getsaveOffice(com.fangxiaoer.model.OfficeEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOfficeDictionary],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.FreePublishController.getOfficeDictionary(java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secondPublish],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.download_app()
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/publishRealEstate],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.publishRealEstate(com.fangxiaoer.model.RealEstate,javax.servlet.http.HttpServletRequest,java.lang.Integer)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkRealEstate],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.checkRealEstate(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRealEstateInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.getRealEstateInfo(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realRent/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealRent(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realShop/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealShop(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.502  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realOffice/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealOffice(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.503  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/entrustedLease],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.entrustReleaseHouse()
2025-07-22 08:36:41.503  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/seekOrder || /seekOrder.htm]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.seekOrder(org.springframework.ui.Model,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.503  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/seekOrder/{houseType}/ || /seekOrder/{houseType}/{params}]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.seekOrderList(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.503  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/new_secondPublish || /secondPublish/{houseID} || /myPublish/{check}/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getSecondHouse(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.503  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveSecondHouse],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveSecondHouse(com.fangxiaoer.model.SaleHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.503  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentwhole || /rentwhole/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getRentWhole(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.503  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveRentWhole],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveRentWhole(com.fangxiaoer.model.RentHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.503  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyIt/{houseId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.verifyIt(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.504  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchs],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.FreePublishController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.504  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/freeServiceIndex],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreeServiceController.freeServiceIndex(org.springframework.ui.Model)
2025-07-22 08:36:41.504  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/freeService/{companyId:[0-9]+}-{serviceId:[0-9]+}.htm || /freeService/{companyId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreeServiceController.freeServiceDetial(org.springframework.ui.Model,java.lang.String,java.lang.String)
2025-07-22 08:36:41.504  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreeServiceController.serviceGuide(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:36:41.504  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceTypes],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreeServiceController.getServiceTypes(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveGuide]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.saveGuide(com.fangxiaoer.model.Guide)
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifySmsCode],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.verifySmsCode(java.lang.String,java.lang.String) throws java.io.IOException
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getPayOrder],methods=[POST]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.getPayOrder(java.util.HashMap)
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.orderGuide(java.lang.String,java.lang.String,java.lang.Integer,java.lang.String,java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkGuideState],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.checkGuideState(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/cancelGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.cancelGuide(java.lang.String,java.lang.String,java.lang.Integer,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveHouseOrder]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.saveOrder(com.fangxiaoer.model.Guide)
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addSessionPopup],methods=[POST]}" onto public void com.fangxiaoer.controller.GuideController.saveSessionPopup(javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuarantee#kgdp || /serviceGuarantee#gfdjt || /serviceGuarantee#mfgj || /serviceGuarantee],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.GuideController.serviceGuarantee()
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuarantee],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Integer> com.fangxiaoer.controller.GuideController.serviceGuaranteeInfo()
2025-07-22 08:36:41.505  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAsk],methods=[POST],produces=[application/json]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.addAsk(java.util.HashMap)
2025-07-22 08:36:41.506  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/loan/{params} || /toLoan],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.GuideController.toLoan(java.lang.String)
2025-07-22 08:36:41.506  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveLoan],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.saveLoan(com.fangxiaoer.model.Loan,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.506  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addMessageToAgent],methods=[POST],produces=[application/json]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.add(java.util.HashMap)
2025-07-22 08:36:41.506  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/homeBackup],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HomeController.homeBackup(org.springframework.ui.Model,org.springframework.mobile.device.Device,java.lang.String)
2025-07-22 08:36:41.506  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchClickAnalyze]}" onto public void com.fangxiaoer.controller.HomeController.searchClickAnalyze(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.506  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fuzzySearchSubd],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLike(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.506  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commonSearch],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLikes(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.506  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/redirectToMyOrAgent],methods=[GET]}" onto public void com.fangxiaoer.controller.HomeController.redirectToMyOrAgent(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.507  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/subscription],methods=[POST]}" onto public void com.fangxiaoer.controller.HomeController.subscription(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.507  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/download],methods=[GET || POST]}" onto public void com.fangxiaoer.controller.HomeController.downloadFile(javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.507  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewProject],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLikeMap(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.507  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewProjectMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HomeController.queryLikeMap(java.util.HashMap)
2025-07-22 08:36:41.507  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchStationMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HomeController.searchStationMap(java.util.HashMap)
2025-07-22 08:36:41.507  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HomeController.index(org.springframework.ui.Model,org.springframework.mobile.device.Device,java.lang.String)
2025-07-22 08:36:41.507  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeper{type:[0-9]+}.htm || /houseKeeper{type:[0-9]+}/{page}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeepersInfoByType(org.springframework.ui.Model,javax.servlet.http.HttpSession,java.lang.String,java.lang.String)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pointsMall],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getPointsMall(org.springframework.ui.Model)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pointsMall/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getPointsMallBySort(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activities],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivities(org.springframework.ui.Model)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activities/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivities(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activity/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivity(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/keeperMore/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeeperMore(org.springframework.ui.Model)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/skipToVip],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.skipToVip(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/status],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getStickOrderStatus(java.lang.String)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeper],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeepers(org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getDaelstories],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseCenterController.getDaelstories(java.util.HashMap<java.lang.String, java.lang.String>)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/festivalNews/{params} || /festivalNews],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getFestivalNews(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/afestivalNews/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getFestivalNewsDetial(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewLookHouse || /viewLookHouse/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.viewLookHouse(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/lookHouseDetail/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.lookHouseDetail(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.508  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/keeperMember || /keeperMember/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeeperMember(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getbrandCompany(org.springframework.ui.Model)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany/{brandId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getCompanyDetial(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany2/{brandId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getCompanyDetial2(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompanyNews],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseCenterController.getCompanyNews(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewBrandMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.viewBrandMap(java.lang.Integer)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeperTour],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getHouseKeepersTour(java.lang.String,java.lang.String)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeperAsk],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getHouseKeepersAsk(java.lang.String)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/strategy/{params} || /strategy],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getStrategy(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/{inputParams}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getGoods(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/payVip],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.payVip(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpSession,java.lang.String)
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/search],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HouseCenterController.search(org.springframework.ui.Model,java.lang.String) throws java.io.UnsupportedEncodingException
2025-07-22 08:36:41.509  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/takeOrder],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getOrder(java.lang.Integer,java.lang.Integer,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houses/{params} || /houses || /subways/{params} || /subways || /exits/{params} || /exits || /lowpays/{params} || /lowpays || /brands/{params} || /brands],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showHouseList(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorations || /decorations/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showDecorationList(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schoolhouses/{params}]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.schoolhouse(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}.htm || /house/{projectId}-{projectType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showHouseDetail(javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/layout.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/layout/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getRoomList(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/appraise{params}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.houseAppraiseTest(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/news.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/{page:[0-9]+}.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/dy{dyType}.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/dy{dyType}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaNews(java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/pic720.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/pic720/{panId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getPicture(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/onlines/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getOnlineInfo(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/online/{projectId}-{projectType}-{roomId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getRoomDetail(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commentAgent/{projectId}/{projectType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.commentAgent(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.511  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/editComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.editComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/editAgentComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.editAgentComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tjf],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.viewParticularHouse()
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewDealRankingList],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.viewDealRankingList(org.springframework.ui.Model)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommentCount],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.getCommentCount(com.fangxiaoer.model.search.CommentCountModel)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAskCount],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.getAskCount(java.lang.String)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommentInfo],methods=[POST],consumes=[*/*],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.CommentHandler(com.fangxiaoer.model.search.CommentSearchModel)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAskInfo],methods=[POST],consumes=[*/*],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.AskHandler(com.fangxiaoer.model.search.AskSearchModel)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOnlineJson],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HouseController.getOnlineJson(java.lang.String)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveComment],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveComment(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveCommentAgent],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveCommentAgent(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveReply],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveReply(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 08:36:41.512  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderActivity],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.orderActivity(java.lang.String,java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAdvertisementList],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getAdvertisementList(java.lang.String,java.lang.String)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fetchNearByProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.fetchNearByProject(java.lang.Integer,java.math.BigDecimal,java.math.BigDecimal,java.lang.Integer)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId}-{projectType}/index/{page:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaForComment(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/relasionHousePush],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getRelasionHousePush(java.lang.String,java.math.BigDecimal,java.math.BigDecimal)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/projectRank/{type:^[0-9]+$} || /projectRank/{type}_{regionId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.videoSearch(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxACodeByAgentId],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxACodeByMemberId(java.lang.String)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxACode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxACode(java.lang.String)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxSecCode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxSecCode(java.lang.String)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getNewWxCode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getNewWxCode(java.lang.String)
2025-07-22 08:36:41.513  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/contrastLayout],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.contrastLayout()
2025-07-22 08:36:41.514  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/doContrastLayout],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.doContrastLayout(java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.514  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRecommendLayout],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getRecommendLayout(java.lang.Integer)
2025-07-22 08:36:41.514  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manageCollect],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.manageCollect(java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 08:36:41.514  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewNewSchoolList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.viewNewSchoolList(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.514  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSchoolListForMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.searchSchoolListForMap(java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Double)
2025-07-22 08:36:41.514  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewNewSchoolHouseList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.viewNewSchoolHouseList(java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Integer)
2025-07-22 08:36:41.514  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/decoration.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.decorationDetail(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.514  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAgentInfo],methods=[POST],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getAgentInfo(java.lang.String)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/helpSearch],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.HouseController.helpSearch(com.fangxiaoer.model.Guide,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/sublayout/{layId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaSubLayout(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decolayout/{layoutId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.jzDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schools || /schools/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.schools(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/info.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getBase(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/ask.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/ask/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.houseAsk(java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/say.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/say/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getSay(java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:\d+}-{projectType:\d+}/album.htm || /house/{projectId:\d+}-{projectType:\d+}/album/{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getPhoto(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comment/{projectId}/{projectType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.comment(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housingprice/{params} || /housingprice],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HousingPriceController.housingprice(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housingpriceDetail],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HousingPriceController.freeEvaluationDetail(com.fangxiaoer.model.HousingPriceEntity,org.springframework.ui.Model,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.515  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/calForLoan],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.calForLoan(java.lang.Double,java.lang.Double,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Integer,java.lang.Double)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewLoanFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.viewLoanFilter(java.lang.Integer,java.lang.Double)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/LPRHistoryFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.LPRHistoryFilter()
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/bigdata],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.bigData(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imToAgent],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.imToAgent(java.lang.String)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imRecruit/{params} || /imRecruit],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.imRecruit(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/serviceIM],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.serviceIM(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/service/{param} || /im/service],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.serviceIM1(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/new/service/{serviceType}],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ImController.serviceIMNew(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/{params} || /im],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.housingprice(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/cloudMsg],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.cloudMsg(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getMyIm],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.getMyIm(java.lang.String)
2025-07-22 08:36:41.516  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imPolt/{params} || /imPolt],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.imPolt(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.517  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/chat/{memberId} || /im/chat],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.agentIM(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 08:36:41.517  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/needSecond/{param}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NeedHouseController.needSecond(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.517  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/needRent/{param}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NeedHouseController.needRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.518  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoSearch],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.getVideosSearchResultList(java.util.HashMap)
2025-07-22 08:36:41.519  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/{params} || /news],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVillages(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.519  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getNewsDetail(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.519  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/getNewsCommentsAndLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.getNewsCommentsAndLike(javax.servlet.http.HttpSession,java.lang.Integer)
2025-07-22 08:36:41.519  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videos/{params} || /videos],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideosList(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.519  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{videoId:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideoDetail(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.519  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{videoId:[0-9]+}-{projectId:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.viewVideoWithProject(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.519  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/audios/{params} || /audios],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAudiosList2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.519  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentnews/{params} || /agentnews],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAgentNewsList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentnews/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAgentNewsDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoSearchlist/{params} || /videoSearchlist],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideosResultList(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAudioNum],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.addAudioNum(java.util.HashMap)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manageMyCollection],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.manageMyCollection(java.util.HashMap)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/newsCommentLike],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.newsCommentLike(java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/setVideoLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.setVideoLike(java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsComment],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsComment(java.lang.String,java.lang.Integer,java.lang.String)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsReply],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsReply(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsCommentLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsCommentLike(java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewSimilarProjectDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewSimilarProjectDetail(java.lang.Integer)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewContrastProjects],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewContrastProjects(java.lang.Integer)
2025-07-22 08:36:41.520  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/liveDetail/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.liveDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewWeizanList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewWeizanList(com.fangxiaoer.model.WeizanSearch)
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/projectnews || /projectnews/{params}]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.newProjectDynamics(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/liveList],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.liveList()
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/index.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotOverview(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages2/{subId}/index.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotOverview2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotSecondVillage/{params} || /saleVillages/{subId:[0-9]+}/plotSecondVillage || /saleVillages/{subId:[0-9]+}/plotSecondVillage/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotSecondHouse(java.lang.String,java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotRentVillage/{params} || /saleVillages/{subId:[0-9]+}/plotRentVillage || /saleVillages/{subId:[0-9]+}/plotRentVillage/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotRentHouse(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/plotRealImage.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotRealImage(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.521  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{askId:[0-9]+}/getAskDetail.htm || /saleVillages/{askId:[0-9]+}/getAskDetail/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getAskDetail(java.lang.String,java.lang.Integer,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/forAddPlotAsk.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.forAddPlotAsk(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAskWithSub],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.PlotController.addAskWithSub(java.lang.Integer,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotExpertInterpretation.htm || /plotExpertInterpretation/{subId:[0-9]+}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotExpertInterpretation(java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/getAskNoReply.htm || /saleVillages/{subId:[0-9]+}/getAskNoReply/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getAskNoReply(java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/plotSupport.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getPlotSupport(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages2/{subId}/plotSupport.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getPlotSupport2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/msgIsRead],methods=[POST]}" onto public void com.fangxiaoer.controller.PlotController.msgIsRead(java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/acceptReplay],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.PlotController.acceptReplay(java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotAsk.htm || /saleVillages/{subId:[0-9]+}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotAsk(java.lang.String,java.lang.Integer,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.522  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addReply],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.PlotController.addReply(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villages/{params} || /villages],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getVillages(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rents/{params} || /rents],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.showRentsHouseList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealRents/{params} || /dealRents],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.dealRentsList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealRent/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getDealRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentmap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRentMap(org.springframework.ui.Model)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentOneInfo],methods=[POST]}" onto public java.util.List com.fangxiaoer.controller.RentController.searchOne(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryRentMap(java.util.HashMap)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryRentMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/queryLocationByAddress],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryLocationByAddress(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentMapDualInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.rentStationMap(java.util.HashMap)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRentMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.getRentMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/village/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getVillage(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.523  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRent(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.524  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent2/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRent2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.524  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentByTitle],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.RentController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.524  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeProjects/{params} || /officeProjects],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.officeProjects(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.524  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeProject/{officeId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.officeProject(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.524  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/scriptoriums/{params} || /scriptoriums],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.shops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.524  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/scriptorium/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.shop(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberIdCardAuthentication],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.memberIdCardAuthentication(java.util.HashMap,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/confirmAuthenticationInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.confirmAuthenticationInfo(java.util.HashMap,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleHouses/{params} || /saleHouses],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSecondHouseList(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchOneInfo],methods=[POST]}" onto public java.util.List com.fangxiaoer.controller.SecondController.searchOne(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealSales/{params} || /dealSales],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getDealSecondList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salehouse2/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeDetail2(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealSale/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getDealSaleDetail(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{params} || /saleVillages],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeVillage(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleHouses/subway/{input}]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeSubwayList(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salemap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSaleMap()
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salemap2],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSaleMap2()
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSecMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySecMap(java.util.HashMap)
2025-07-22 08:36:41.525  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSecMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySecMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchForSubway],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.SecondController.queryForSubway(java.lang.String)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleMetroStation],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secStationMap(java.util.HashMap)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleMetroHouseList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secStationMapHouse(java.util.HashMap)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secPlotDetailList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secMapPlot(java.lang.String)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secondhouse/entrustReleaseHouse],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.entrustReleaseHouse()
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salehouse/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/judicial.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.Judicial()
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dashuju],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.Dashuju()
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/querySubDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySub(java.lang.String)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/falsity],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.falsity(java.lang.String,java.lang.String,java.lang.Integer,java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchByTitle],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.SecondController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealShops/{params} || /dealShops],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.dealShops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shops/{params} || /shops],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.shops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 08:36:41.526  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shop/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.shop(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealShop/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.dealShop(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkSignin]}" onto public java.lang.String com.fangxiaoer.controller.SignController.checkSignin(javax.servlet.http.HttpSession)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sendSmsCode],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.sendSMSCode(java.lang.String,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/quickregist],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.quickRegist(java.lang.String,java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,java.lang.String)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkPhone],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.SignController.checkPhone(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkPasscode],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.SignController.checkPasscode(java.lang.String,java.lang.String)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkOCRIDCard],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.checkOCRIDCard(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/signin],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.SignController.login(java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpSession)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/login],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.login(java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/quit || /Action/logout.aspx]}" onto public java.lang.String com.fangxiaoer.controller.SignController.logout(javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.527  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/signin]}" onto public java.lang.String com.fangxiaoer.controller.SignController.signin(org.springframework.ui.Model,java.lang.String,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpSession,java.lang.String)
2025-07-22 08:36:41.528  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/regist]}" onto public java.lang.String com.fangxiaoer.controller.SignController.regist(org.springframework.ui.Model)
2025-07-22 08:36:41.528  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/transit]}" onto public java.lang.String com.fangxiaoer.controller.SignController.transit(org.springframework.ui.Model)
2025-07-22 08:36:41.528  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/{dir}/{path}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.staticPage(java.lang.String,java.lang.String)
2025-07-22 08:36:41.528  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/{path}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.staticPage(java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.528  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fastSeek]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.fastSeek(org.springframework.ui.Model)
2025-07-22 08:36:41.528  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/goufangnenglipinggu.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Gufangnenglipinggu()
2025-07-22 08:36:41.528  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/tiqianhuankuan.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Huankuan()
2025-07-22 08:36:41.529  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/loan_calculator]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Daikuan()
2025-07-22 08:36:41.529  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shuifei.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Shuifei()
2025-07-22 08:36:41.529  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTouristIndex(org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.529  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tNews.htm || /tNews{id}.htm || /tNews{id}/-n{page:[0-9]+}]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTNewsList(java.lang.Integer,org.springframework.ui.Model,java.lang.String,java.lang.Integer)
2025-07-22 08:36:41.529  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/touristList || /touristList/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTouristList(org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:36:41.529  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/touristGuideCount],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.touristGuideCount()
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectDetail(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tNews/{id}.htm]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewNewsDetail(java.lang.Integer,org.springframework.ui.Model,java.lang.String)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/info.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectInfo(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/ask.htm || /tourist/{id}/ask/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewAskList(java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/comment.htm || /tourist/{id}/comment/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristCommentList(java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/dy.htm || /tourist/{id}/dy-{dyType}.htm || /tourist/{id}/dy/{page:[0-9]+}.htm || /tourist/{id}/dy-{dyType}/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectDy(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/layout-{id}/{params} || /tourist/layout-{id}/{params}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewLayoutDetail(java.lang.Integer,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:\d+}/album.htm || /tourist/{projectId:\d+}/album/{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.fetchProjectPics(java.lang.Integer,java.lang.String,org.springframework.ui.Model)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:\d+}/pushcomment],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.commentProject(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getTouristFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.getTouristFilter()
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addTouristOrder],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.addTouristOrder(com.fangxiaoer.model.TouristOrder)
2025-07-22 08:36:41.530  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.orderProject(java.lang.String)
2025-07-22 08:36:41.531  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/editComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.editComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 08:36:41.531  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/saveComment],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.TouristController.saveComment(java.util.HashMap)
2025-07-22 08:36:41.531  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addTouristAsk],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.addAsk(java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 08:36:41.531  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error]}" onto public java.lang.String com.fangxiaoer.error.CustomErrorController.generalError(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,org.springframework.ui.Model)
2025-07-22 08:36:41.578  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 08:36:41.578  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 08:36:41.599  INFO 92258 --- [restartedMain] .m.m.a.ExceptionHandlerExceptionResolver : Detected @ExceptionHandler methods in exceptionHandler
2025-07-22 08:36:41.614  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 08:36:46.750  INFO 92258 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : FrameworkServlet 'dispatcherServlet': initialization started
2025-07-22 08:36:46.761  INFO 92258 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : FrameworkServlet 'dispatcherServlet': initialization completed in 10 ms
2025-07-22 08:36:50.082 ERROR 92258 --- [http-nio-8080-exec-6] com.fangxiaoer.common.AllInterceptor     : Block,outside POST: 0:0:0:0:0:0:0:1: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-22 08:39:59.970 ERROR 92258 --- [pool-6-thread-1] com.fangxiaoer.common.IPMonitor          : =======start to clean======= Block size: 0, Frequency: 2
2025-07-22 08:45:14.768 ERROR 92258 --- [http-nio-8080-exec-2] com.fangxiaoer.common.AllInterceptor     : Block,outside POST: 0:0:0:0:0:0:0:1: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-22 08:45:22.905 ERROR 92258 --- [http-nio-8080-exec-5] com.fangxiaoer.common.AllInterceptor     : Block,outside POST: 0:0:0:0:0:0:0:1: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-22 08:49:59.997 ERROR 92258 --- [pool-6-thread-1] com.fangxiaoer.common.IPMonitor          : =======start to clean======= Block size: 0, Frequency: 5
2025-07-22 08:59:59.890 ERROR 92258 --- [pool-6-thread-1] com.fangxiaoer.common.IPMonitor          : =======start to clean======= Block size: 0, Frequency: 0
2025-07-22 09:09:59.990 ERROR 92258 --- [pool-6-thread-1] com.fangxiaoer.common.IPMonitor          : =======start to clean======= Block size: 0, Frequency: 0
2025-07-22 09:16:43.564 ERROR 92258 --- [http-nio-8080-exec-7] com.fangxiaoer.common.AllInterceptor     : Block,outside POST: 0:0:0:0:0:0:0:1: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-22 09:17:23.117  INFO 92258 --- [localhost-startStop-1] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 886 ms
2025-07-22 09:17:23.744  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerAdapter : Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@17656a8c: startup date [Tue Jul 22 09:17:22 CST 2025]; root of context hierarchy
2025-07-22 09:17:23.780  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/marketing/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.Marketing(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.780  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/helpSearch || /helpSearch.htm]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.helpSearch(org.springframework.ui.Model,java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.780  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewActivity2],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewActivity(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.781  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewManagerDiscount || /viewManagerDiscount/{params}]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.viewManagerDiscount(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.781  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveCollection],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saveCollection(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.781  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pay_getQR],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.pay_getQR(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.781  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderPaySuccess],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewPayOrderDetail(java.lang.String)
2025-07-22 09:17:23.781  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/contactInfo/{orderId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.viewContract(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.782  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getLayOut],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getLayOut(java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.782  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getBuildName],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getLayOut(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.782  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/createOrder],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.createOrder(java.lang.String,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.782  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOrderDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getOrderDetail(java.lang.String)
2025-07-22 09:17:23.782  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/payForSuccess],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.payForSuccess(java.lang.String)
2025-07-22 09:17:23.783  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkIsSign],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.checkIsSign(java.lang.String)
2025-07-22 09:17:23.783  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleOthersFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleOthersFilter()
2025-07-22 09:17:23.783  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleInfoFilter()
2025-07-22 09:17:23.783  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.rentInfoFilter()
2025-07-22 09:17:23.783  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shopInfoFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.shopInfoFilter()
2025-07-22 09:17:23.783  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleFeatureFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.saleFeatureFilter()
2025-07-22 09:17:23.783  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentFeatureFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.rentFeatureFilter()
2025-07-22 09:17:23.783  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/newRegionFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.newRegionFilter()
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/usedRegionFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.usedRegionFilter()
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/layoutFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.layoutFilter()
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/relationShipFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.relationShipFilter()
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewSubsidyForProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.viewSubsidyForProject(java.lang.String)
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fetchSubsidyForProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.fetchSubsidyForProject(java.lang.String,java.lang.Integer,java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/discount/{input} || /discount]}" onto public java.lang.String com.fangxiaoer.controller.ActivityController.discount(org.springframework.ui.Model,java.lang.String,java.lang.String)
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getUnit],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getUnit(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRoom],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.ActivityController.getRoom(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.784  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderPay],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ActivityController.orderPay(java.lang.String,java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 09:17:23.788  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/sale.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishshopSUPdateMapping(java.lang.String)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/rent.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopRUPdateMapping(java.lang.String)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/attorn.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopTUPdateMapping(java.lang.String)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/view/{houseId} || /UHouse/sale/SaleFullDetail/{houseId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseDetailMapping(java.lang.String)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/SubdistrictList || /UHouse/Subdistrict/SubdistrictList/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondHouseVillagesMapping()
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/online/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeOnlineMapping(java.lang.Integer)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/room/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.onlineDetailMapping(java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/photo/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officePhoto(java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/photo/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaPhoto(java.lang.Integer)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/picture/view/{projectType}_{projectId}_{photoType}_{photoId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaPhotoDetail(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.789  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/album/{projectId:\d+}-{projectType:\d+}.htm || /house/album/{projectId:\d+}-{projectType:\d+}-{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resAndOfficeAlbumMapping(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.790  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/photos/{projectId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaAlbumMapping(java.lang.String)
2025-07-22 09:17:23.790  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/appraise/{projectId:[0-9]+}-{projectType:[0-9]+}.htm{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseAppraiseMapping(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.790  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id}#xmpj],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resComment(java.lang.Integer)
2025-07-22 09:17:23.790  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id}#xmpj],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeComment(java.lang.Integer)
2025-07-22 09:17:23.790  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/ask/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/ask/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseAskMapping(java.lang.Integer,java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.790  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id}#xmzx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeAsk(java.lang.Integer)
2025-07-22 09:17:23.790  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/news/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/news/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaNewsMapping(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/news/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaSaleInfo(java.lang.Integer)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/news/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeSaleInfo(java.lang.Integer)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/news/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseNews(java.lang.String)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/say/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /house/say/{projectId:[0-9]+}-{projectType:[0-9]+}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewHouseSayMapping(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/say/{id:^\d+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewHouseSay(java.lang.Integer)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pic720/{projectId:[0-9]+}-{projectType:[0-9]+}.htm || /pic720/{projectId:[0-9]+}-{projectType:[0-9]+}-{panId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getPictureMapper(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activity]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mDiscountList()
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uhouse/shangpu/view/{id} || /Uhouse/shangpu/view/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopDetial(java.lang.String)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/broker/{type}/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.agentHouse(java.lang.String,java.lang.String)
2025-07-22 09:17:23.791  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/homeAct.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseActivityMapping()
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseKeeperMapping()
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/integral.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.pointsMallMapping()
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/strategy.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.strategyMapping()
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/goods.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getGoodsMapping(java.lang.String)
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/SaleHouse.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishSaleMapping(java.lang.String)
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/rent_form_1.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishRentWMapping(java.lang.String)
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/rent_form_2.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishRentSMapping(java.lang.String)
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/sale.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopSMapping()
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/rent.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopRMapping()
2025-07-22 09:17:23.792  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/attorn.aspx]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.publishShopTMapping()
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/view/{path}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityDetailMapping(java.lang.String)
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{value}_{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoMapping(java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsMapping(java.lang.String)
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent//RentRegion/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsRegionMapping(java.lang.String)
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/RentSubway/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentsSubwayMapping(java.lang.String)
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/view/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentMapping(java.lang.String)
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/SubdistrictListRt/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.valligesMapping()
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/Subdistrict/view/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.valligeMapping(java.lang.String)
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent/housemap/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.rentMapMapping()
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/goufangnenglipinggu.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.abilityMapping()
2025-07-22 09:17:23.793  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/tiqianhuankuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.advanceMapping()
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/zuhedaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.assembleMapping()
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shangyedaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.businessMapping()
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/gongjijindaikuan.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fundMapping()
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/gongjijindaikuanedupinggu.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fundAssessMapping()
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shuifei.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.tallageMapping()
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale || /UHouse/sale/ || /UHouse/sale/Region || /UHouse/sale/UHouseSubway || /UHouse/sale/Region/{param:^[-?\d?_?]+$} || /UHouse/sale/UHouseSubway/{param:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseListMapping(java.lang.String)
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/housemap/ || /UHouse/housemap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.secondeHouseMapMapping()
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/View/{para}_all],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsDetailList(java.lang.String)
2025-07-22 09:17:23.794  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/baike.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.encyclopedia()
2025-07-22 09:17:23.795  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/formulate/baike.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.encyclopediaB()
2025-07-22 09:17:23.795  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/0929/ms/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentThree()
2025-07-22 09:17:23.795  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/View/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsDetail(java.lang.String)
2025-07-22 09:17:23.795  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/search.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectMapping(java.lang.String)
2025-07-22 09:17:23.795  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schoolhouse/{id:^[-?\d?_?]+$} || /schoolhouse/{id:^[-?\d?_?]+$}/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.schoolListMapping(java.lang.String)
2025-07-22 09:17:23.795  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/JingZhuangHouseDetail.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decoration(java.lang.Integer)
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decoration/{projectId:[0-9]+}-{projectType:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decorationMapping(java.lang.String,java.lang.String)
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Sitemap.html],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.AddComment()
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/AddComment.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.AddComment(java.lang.String,java.lang.String)
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Index],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangIndex()
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/About || /About/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangAbouts(java.lang.String)
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Help || /Help/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangHelps(java.lang.String)
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap1.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseMap()
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap2.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondMap()
2025-07-22 09:17:23.796  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housemap3.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.syRentMap()
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mVideosList()
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/xegj.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mKeeperList()
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/xelist.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mKeeperMoreList()
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/jjrsf/{a}-{b}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mAgentShopList(java.lang.String,java.lang.String)
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzsc{a}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPointsMall(java.lang.String)
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzsc/{a}.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPointsMallDetail(java.lang.String)
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fzgl.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mStrategyList()
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/publish || /pubSale.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishSecondList()
2025-07-22 09:17:23.797  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pubRent.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishRentList()
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pubShop.htm]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mPublishShopsList()
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalnew.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindHouseList()
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalsale.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindSecondList()
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalRent.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindRentList()
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/normalShop.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHelpFindShopList()
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/kfztc.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mLookForHouseList()
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/ghdb.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mGuoHuDaiBanList()
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1l || /fang1l/ || /fang1l/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseList(java.lang.String)
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseDetail(java.lang.String)
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1dt || /fang1dt/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseSubwayList(java.lang.String)
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1xq || /fang1xq/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseSchoolList(java.lang.String)
2025-07-22 09:17:23.798  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorates || /decorates/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mHouseDecorationsList(java.lang.String)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorate/{projectId}-{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.decorationDetail(java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/school/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.schoolDetail(java.lang.Integer)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/nphotos/{projectId}-{photoType}-{projectType}.htm || /nphotos/{projectId}-{photoType}-{projectType}-{photoId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectPhoto(java.lang.Integer,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/dy || /fang1/{projectId}-{projectType}/dy-{infoType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.dynamicInfo(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/appraise.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectComment(java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/ask.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.projectAsk(java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/layout/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLayoutInfos(java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang2 || /fang2/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondList(java.lang.String)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang2/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSecondDetail(java.lang.String)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang3/{a} || /fang3],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mRentList(java.lang.String)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang3/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mRentDetail(java.lang.String)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang4 || /fang4/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mShopsList(java.lang.String)
2025-07-22 09:17:23.799  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang4/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mShopDetail(java.lang.String)
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang5 || /fang5/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.oldOffice(java.lang.String)
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang5/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.oldOfficeDetail(java.lang.Integer)
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoIndex.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoInfo()
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoByType{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.videoList(java.lang.Integer)
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/audio.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.audioList()
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sub/{a}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSubDetail(java.lang.String)
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/txnSecond],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.dealInfos()
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fang1/{projectId}-{projectType}/pic720.htm || /fang1/{projectId}-{projectType}/pic720/{panId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.panoramic(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.800  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/project1l || /project1l/ || /project1l/{a}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newProjectList(java.lang.String)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/dy/-n{page:[0-9]+} || /tourist/{id}/dy-{dyType}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristDyInfos(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/ask/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristAsks(java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/comment/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristComment(java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:[0-9]+}/layout.htm || /tourist/{projectId:[0-9]+}/layout/pid{projectId:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.touristLayout(java.lang.Integer)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/formulate/house.aspx || /formulate//formulate/house.aspx{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.helpSearch(java.lang.String)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.residenceDetailMapping(java.lang.Integer)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaDetailMapping(java.lang.Integer)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeDetailMapping(java.lang.Integer)
2025-07-22 09:17:23.801  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealer/house/view/{id:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.vipDetailMapping(java.lang.Integer)
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/Default.aspx || /house/ || /house || /house/{type:[-?\d+_]{5,}}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newHouseMapping()
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/foreignhouse/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.foregionHouseMapping()
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/shops/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopsMapping()
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office || /office/ || /office/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.officeMapping()
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/ || /villa],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaMapping()
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/need/view/{path}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityMapping(java.lang.String)
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/marketing/default.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.activityMapping()
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housestyle.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewVillas(java.lang.String)
2025-07-22 09:17:23.802  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housestyle/{pid:^\d+$} || /house/list/{pid:^\d+$} || /house/list/{pid}_{lid} || /house/listd/{pid}_{lid}_{hid}_{rid}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLayouts(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/jingzhuang/s/ || /jingzhuang/s/*],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewDecorations()
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Brand/s/ || /Brand/s/*],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewBrands()
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/villa_details/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaInfoMapping(java.lang.Integer)
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/subway || /subway/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewSubways()
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/school || /schoolhouse || /school/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewSchools()
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/logpay || /logpay/{type:^[-?\d?_?]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewLowPays()
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/housemap/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewNewHouseMap()
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/info/{projectId:[0-9]+}-{projectType:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseInfoMapping(java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/info/{id:^\d+} || /house/info/{id:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.houseInfoMapping(java.lang.Integer)
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/layout/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.getRoomListMapping(java.lang.String)
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/layout/{projectId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaLayout(java.lang.String)
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/villastyle/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewVillaLayout(java.lang.Integer)
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villa/sublayout/{projectId}-{layId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.villaSubLayout(java.lang.String,java.lang.String)
2025-07-22 09:17:23.803  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/online/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resOnlineMapping(java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.804  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/{b}/{c}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.804  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/photo/{a}_{b}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resPhoto(java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.804  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/view/{id}#xmzx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.resAsk(java.lang.Integer)
2025-07-22 09:17:23.804  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/office/plan/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.viewPlan(java.lang.Integer)
2025-07-22 09:17:23.804  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/shangpu/ || /UHouse/shangpu || /uhouse/shangpu/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.shopList(java.lang.String)
2025-07-22 09:17:23.804  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsList(java.lang.Integer)
2025-07-22 09:17:23.804  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/News/FeaturedList/{para}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.newsList(java.lang.String)
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/0929/pp1/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentOne()
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2015/1231/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.enentTwo()
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/{c}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event1(java.lang.String,java.lang.String)
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/{a}/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event2(java.lang.String)
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/event/2010/1208/default.html],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.event3()
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/rent/village/{para}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.village(java.lang.String)
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/Join || /Join/{param}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.fangJoin(java.lang.String)
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brand],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.brand()
2025-07-22 09:17:23.805  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sub],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AdapterController.mSubList()
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agent/{type}/{agentId:^[0-9]+$}/{params} || /agent/{type}/{agentId:^[0-9]+$}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.agentHouse(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberTopListOnload]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentController.memberTopListOnload(java.lang.Integer,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberTopList || /memberTopList/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.memberTopList(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/findAgent/{params} || /findAgent],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.findAgent(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/findGoldAgent],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.findGoldAgent(org.springframework.ui.Model)
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/closeShop],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.closeShop(org.springframework.ui.Model)
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agent/intermediary/{type}/{companyId}/{params} || /agent/intermediary/{type}/{companyId}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.agentIntermediary(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewRecommandYiAgent],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentController.viewRecommandYiAgent(java.lang.Integer)
2025-07-22 09:17:23.806  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentIntermediary/{type}/{agentIntermediaryId}/{params} || /agentIntermediary/{type}/{agentIntermediaryId}]}" onto public java.lang.String com.fangxiaoer.controller.AgentController.intermediary(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.807  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentRecruit/{params} || /agentRecruit],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.agentRecruit(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.807  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/companyDetail/{companyId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.getSeDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.807  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/positionDetail/{positionId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.AgentRecruitController.getPositionDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.807  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommonMemberResume],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentRecruitController.getCommonMemberResume(java.lang.String)
2025-07-22 09:17:23.807  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/applyPosition],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.AgentRecruitController.getPosition(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.808  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/block],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.showBlockPage()
2025-07-22 09:17:23.808  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/temp],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.showTempPage(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.808  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyBlocker],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.BlackListController.getVerifycode(javax.servlet.http.HttpServletRequest,java.lang.Integer)
2025-07-22 09:17:23.808  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getSysTime],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.BlackListController.getSysTime()
2025-07-22 09:17:23.808  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyImageCode],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.BlackListController.verifyImageCode(javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.Integer,java.lang.Long) throws java.io.IOException
2025-07-22 09:17:23.809  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uploadNewCommentPic],methods=[POST]}" onto public void com.fangxiaoer.controller.CommonPicController.photoUpload(javax.servlet.http.HttpServletResponse,org.springframework.web.multipart.MultipartFile,java.lang.String)
2025-07-22 09:17:23.809  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/uploadHousePic],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.CommonPicController.uploadHousePic(org.springframework.web.multipart.MultipartFile,java.lang.String)
2025-07-22 09:17:23.810  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gotoContrastHouse]}" onto public java.lang.String com.fangxiaoer.controller.ContrastController.gotoContrastHouse(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.810  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.addContrastHouse(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.810  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.getContrastHouse(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model)
2025-07-22 09:17:23.811  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/deleteContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.deleteContrastHouse(java.lang.String,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.811  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/deleteAllContrastHouse],methods=[POST]}" onto public java.util.ArrayList com.fangxiaoer.controller.ContrastController.deleteAllContrastHouse(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.811  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewHouse],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.ContrastController.search(java.lang.String,java.lang.String)
2025-07-22 09:17:23.812  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentsingls || /rentsingls/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getRentSingls(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.812  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveRentSingls],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveRentSingls(com.fangxiaoer.model.RentHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.812  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shopsell || /shopsell/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopSell(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.812  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopSell],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopSell(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.812  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shoprent || /shoprent/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.813  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopRent],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopRent(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.813  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shoptransfer || /shoptransfer/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getShopTransfer(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.813  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveShopTransfer],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveShopTransfer(com.fangxiaoer.model.ShopEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.813  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getPlates],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.FreePublishController.getPlates(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.813  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchPhone],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchPhone(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.813  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchUser],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchUser(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.814  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSub],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.searchSub(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.814  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeSell || /officeSell/{shopId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getOfficeSell(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.814  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeRent || /officeRent/{shopId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getOfficeRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.814  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveOffice || /saveOffice/{houseID}],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.FreePublishController.getsaveOffice(com.fangxiaoer.model.OfficeEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.814  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOfficeDictionary],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.FreePublishController.getOfficeDictionary(java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.814  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secondPublish],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.download_app()
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/publishRealEstate],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.publishRealEstate(com.fangxiaoer.model.RealEstate,javax.servlet.http.HttpServletRequest,java.lang.Integer)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkRealEstate],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.checkRealEstate(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRealEstateInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreePublishController.getRealEstateInfo(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realRent/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealRent(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realShop/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealShop(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/realOffice/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.viewRealOffice(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/entrustedLease],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.entrustReleaseHouse()
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/seekOrder || /seekOrder.htm]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.seekOrder(org.springframework.ui.Model,java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/seekOrder/{houseType}/ || /seekOrder/{houseType}/{params}]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.seekOrderList(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/new_secondPublish || /secondPublish/{houseID} || /myPublish/{check}/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getSecondHouse(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.815  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveSecondHouse],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveSecondHouse(com.fangxiaoer.model.SaleHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.816  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentwhole || /rentwhole/{houseID}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.getRentWhole(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.816  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveRentWhole],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.saveRentWhole(com.fangxiaoer.model.RentHouseEntity,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.816  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifyIt/{houseId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreePublishController.verifyIt(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.816  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchs],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.FreePublishController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.816  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/freeServiceIndex],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreeServiceController.freeServiceIndex(org.springframework.ui.Model)
2025-07-22 09:17:23.816  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/freeService/{companyId:[0-9]+}-{serviceId:[0-9]+}.htm || /freeService/{companyId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.FreeServiceController.freeServiceDetial(org.springframework.ui.Model,java.lang.String,java.lang.String)
2025-07-22 09:17:23.816  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreeServiceController.serviceGuide(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 09:17:23.817  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceTypes],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.FreeServiceController.getServiceTypes(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 09:17:23.818  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveGuide]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.saveGuide(com.fangxiaoer.model.Guide)
2025-07-22 09:17:23.818  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/verifySmsCode],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.verifySmsCode(java.lang.String,java.lang.String) throws java.io.IOException
2025-07-22 09:17:23.818  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getPayOrder],methods=[POST]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.getPayOrder(java.util.HashMap)
2025-07-22 09:17:23.818  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.orderGuide(java.lang.String,java.lang.String,java.lang.Integer,java.lang.String,java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.819  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkGuideState],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.checkGuideState(java.lang.String,java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.819  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/cancelGuide],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.cancelGuide(java.lang.String,java.lang.String,java.lang.Integer,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.819  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveHouseOrder]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.saveOrder(com.fangxiaoer.model.Guide)
2025-07-22 09:17:23.819  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addSessionPopup],methods=[POST]}" onto public void com.fangxiaoer.controller.GuideController.saveSessionPopup(javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.819  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuarantee#kgdp || /serviceGuarantee#gfdjt || /serviceGuarantee#mfgj || /serviceGuarantee],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.GuideController.serviceGuarantee()
2025-07-22 09:17:23.820  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/serviceGuarantee],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Integer> com.fangxiaoer.controller.GuideController.serviceGuaranteeInfo()
2025-07-22 09:17:23.820  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAsk],methods=[POST],produces=[application/json]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.addAsk(java.util.HashMap)
2025-07-22 09:17:23.820  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/loan/{params} || /toLoan],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.GuideController.toLoan(java.lang.String)
2025-07-22 09:17:23.820  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveLoan],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.GuideController.saveLoan(com.fangxiaoer.model.Loan,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.820  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addMessageToAgent],methods=[POST],produces=[application/json]}" onto public java.util.Map com.fangxiaoer.controller.GuideController.add(java.util.HashMap)
2025-07-22 09:17:23.821  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/homeBackup],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HomeController.homeBackup(org.springframework.ui.Model,org.springframework.mobile.device.Device,java.lang.String)
2025-07-22 09:17:23.821  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchClickAnalyze]}" onto public void com.fangxiaoer.controller.HomeController.searchClickAnalyze(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.821  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fuzzySearchSubd],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLike(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.822  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commonSearch],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLikes(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.822  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/redirectToMyOrAgent],methods=[GET]}" onto public void com.fangxiaoer.controller.HomeController.redirectToMyOrAgent(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.822  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/subscription],methods=[POST]}" onto public void com.fangxiaoer.controller.HomeController.subscription(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.822  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/download],methods=[GET || POST]}" onto public void com.fangxiaoer.controller.HomeController.downloadFile(javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.823  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewProject],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HomeController.queryLikeMap(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.823  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchNewProjectMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HomeController.queryLikeMap(java.util.HashMap)
2025-07-22 09:17:23.823  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchStationMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HomeController.searchStationMap(java.util.HashMap)
2025-07-22 09:17:23.823  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HomeController.index(org.springframework.ui.Model,org.springframework.mobile.device.Device,java.lang.String)
2025-07-22 09:17:23.824  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeper{type:[0-9]+}.htm || /houseKeeper{type:[0-9]+}/{page}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeepersInfoByType(org.springframework.ui.Model,javax.servlet.http.HttpSession,java.lang.String,java.lang.String)
2025-07-22 09:17:23.824  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pointsMall],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getPointsMall(org.springframework.ui.Model)
2025-07-22 09:17:23.824  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/pointsMall/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getPointsMallBySort(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.824  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activities],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivities(org.springframework.ui.Model)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activities/{inputParams}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivities(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/activity/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getActivity(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/keeperMore/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeeperMore(org.springframework.ui.Model)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/skipToVip],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.skipToVip(javax.servlet.http.HttpServletRequest,org.springframework.ui.Model,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/status],methods=[POST],produces=[application/json]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getStickOrderStatus(java.lang.String)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeper],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeepers(org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getDaelstories],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseCenterController.getDaelstories(java.util.HashMap<java.lang.String, java.lang.String>)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/festivalNews/{params} || /festivalNews],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getFestivalNews(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/afestivalNews/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getFestivalNewsDetial(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.825  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewLookHouse || /viewLookHouse/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.viewLookHouse(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.826  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/lookHouseDetail/{id}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.lookHouseDetail(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.826  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/keeperMember || /keeperMember/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getHouseKeeperMember(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.826  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getbrandCompany(org.springframework.ui.Model)
2025-07-22 09:17:23.826  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany/{brandId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getCompanyDetial(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.826  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompany2/{brandId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getCompanyDetial2(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.826  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/brandCompanyNews],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseCenterController.getCompanyNews(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.827  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewBrandMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.viewBrandMap(java.lang.Integer)
2025-07-22 09:17:23.827  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeperTour],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getHouseKeepersTour(java.lang.String,java.lang.String)
2025-07-22 09:17:23.827  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houseKeeperAsk],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.getHouseKeepersAsk(java.lang.String)
2025-07-22 09:17:23.827  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/strategy/{params} || /strategy],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getStrategy(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.827  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/goods/{inputParams}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getGoods(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.827  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/payVip],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseCenterController.payVip(java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpSession,java.lang.String)
2025-07-22 09:17:23.827  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/search],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HouseCenterController.search(org.springframework.ui.Model,java.lang.String) throws java.io.UnsupportedEncodingException
2025-07-22 09:17:23.827  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/takeOrder],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.HouseCenterController.getOrder(java.lang.Integer,java.lang.Integer,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.829  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/houses/{params} || /houses || /subways/{params} || /subways || /exits/{params} || /exits || /lowpays/{params} || /lowpays || /brands/{params} || /brands],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showHouseList(javax.servlet.http.HttpServletRequest,java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.830  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decorations || /decorations/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showDecorationList(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.830  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schoolhouses/{params}]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.schoolhouse(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.830  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}.htm || /house/{projectId}-{projectType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.showHouseDetail(javax.servlet.http.HttpServletRequest,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.830  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/layout.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/layout/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getRoomList(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.830  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/appraise{params}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.houseAppraiseTest(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.830  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/news.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/{page:[0-9]+}.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/dy{dyType}.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/news/dy{dyType}-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaNews(java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.831  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/pic720.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/pic720/{panId:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getPicture(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.831  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/onlines/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getOnlineInfo(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.831  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/online/{projectId}-{projectType}-{roomId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getRoomDetail(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.831  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commentAgent/{projectId}/{projectType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.commentAgent(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.831  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/editComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.editComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.831  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/editAgentComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.editAgentComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.831  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tjf],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.viewParticularHouse()
2025-07-22 09:17:23.832  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewDealRankingList],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.viewDealRankingList(org.springframework.ui.Model)
2025-07-22 09:17:23.832  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommentCount],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.getCommentCount(com.fangxiaoer.model.search.CommentCountModel)
2025-07-22 09:17:23.832  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAskCount],methods=[POST]}" onto public com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.getAskCount(java.lang.String)
2025-07-22 09:17:23.832  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getCommentInfo],methods=[POST],consumes=[*/*],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.CommentHandler(com.fangxiaoer.model.search.CommentSearchModel)
2025-07-22 09:17:23.832  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAskInfo],methods=[POST],consumes=[*/*],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.AskHandler(com.fangxiaoer.model.search.AskSearchModel)
2025-07-22 09:17:23.832  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getOnlineJson],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.HouseController.getOnlineJson(java.lang.String)
2025-07-22 09:17:23.832  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveComment],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveComment(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 09:17:23.832  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveCommentAgent],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveCommentAgent(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saveReply],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.saveReply(java.util.HashMap,org.springframework.ui.Model)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderActivity],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.orderActivity(java.lang.String,java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAdvertisementList],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getAdvertisementList(java.lang.String,java.lang.String)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fetchNearByProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.fetchNearByProject(java.lang.Integer,java.math.BigDecimal,java.math.BigDecimal,java.lang.Integer)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId}-{projectType}/index/{page:^[0-9]+$}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaForComment(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/relasionHousePush],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getRelasionHousePush(java.lang.String,java.math.BigDecimal,java.math.BigDecimal)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/projectRank/{type:^[0-9]+$} || /projectRank/{type}_{regionId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.videoSearch(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxACodeByAgentId],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxACodeByMemberId(java.lang.String)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxACode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxACode(java.lang.String)
2025-07-22 09:17:23.833  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getWxSecCode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getWxSecCode(java.lang.String)
2025-07-22 09:17:23.834  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getNewWxCode],produces=[application/json;charset=UTF-8]}" onto public java.util.Map com.fangxiaoer.controller.HouseController.getNewWxCode(java.lang.String)
2025-07-22 09:17:23.834  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/contrastLayout],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.contrastLayout()
2025-07-22 09:17:23.834  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/doContrastLayout],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.doContrastLayout(java.lang.Integer,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.834  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRecommendLayout],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.getRecommendLayout(java.lang.Integer)
2025-07-22 09:17:23.834  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manageCollect],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.HouseController.manageCollect(java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 09:17:23.834  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewNewSchoolList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.viewNewSchoolList(java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.834  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSchoolListForMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.searchSchoolListForMap(java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Double)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewNewSchoolHouseList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HouseController.viewNewSchoolHouseList(java.lang.String,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Double,java.lang.Integer)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/decoration.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.decorationDetail(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getAgentInfo],methods=[POST],produces=[text/html;charset=UTF-8]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getAgentInfo(java.lang.String)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/helpSearch],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.HouseController.helpSearch(com.fangxiaoer.model.Guide,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/sublayout/{layId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.villaSubLayout(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/decolayout/{layoutId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.jzDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/schools || /schools/{params}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.schools(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/info.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getBase(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.835  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/ask.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/ask/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.houseAsk(java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:[0-9]+}-{projectType:[0-9]+}/say.htm || /house/{projectId:[0-9]+}-{projectType:[0-9]+}/say/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getSay(java.lang.String,java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/house/{projectId:\d+}-{projectType:\d+}/album.htm || /house/{projectId:\d+}-{projectType:\d+}/album/{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.getPhoto(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/comment/{projectId}/{projectType}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HouseController.comment(java.lang.String,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housingprice/{params} || /housingprice],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HousingPriceController.housingprice(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/housingpriceDetail],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.HousingPriceController.freeEvaluationDetail(com.fangxiaoer.model.HousingPriceEntity,org.springframework.ui.Model,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/calForLoan],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.calForLoan(java.lang.Double,java.lang.Double,java.lang.Integer,java.lang.Integer,java.lang.Integer,java.lang.Double,java.lang.Integer,java.lang.Double)
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewLoanFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.viewLoanFilter(java.lang.Integer,java.lang.Double)
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/LPRHistoryFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.LPRHistoryFilter()
2025-07-22 09:17:23.836  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/bigdata],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.HousingPriceController.bigData(java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.837  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imToAgent],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.imToAgent(java.lang.String)
2025-07-22 09:17:23.837  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imRecruit/{params} || /imRecruit],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.imRecruit(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.837  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/serviceIM],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.serviceIM(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.837  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/service/{param} || /im/service],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.serviceIM1(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.837  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/new/service/{serviceType}],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.ImController.serviceIMNew(org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String)
2025-07-22 09:17:23.837  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/{params} || /im],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.housingprice(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.838  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/cloudMsg],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.cloudMsg(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.838  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getMyIm],methods=[POST]}" onto public java.lang.Object com.fangxiaoer.controller.ImController.getMyIm(java.lang.String)
2025-07-22 09:17:23.838  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/imPolt/{params} || /imPolt],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.imPolt(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.838  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/im/chat/{memberId} || /im/chat],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ImController.agentIM(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-07-22 09:17:23.838  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/needSecond/{param}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NeedHouseController.needSecond(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.838  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/needRent/{param}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NeedHouseController.needRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoSearch],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.getVideosSearchResultList(java.util.HashMap)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/{params} || /news],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVillages(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getNewsDetail(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/news/getNewsCommentsAndLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.getNewsCommentsAndLike(javax.servlet.http.HttpSession,java.lang.Integer)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videos/{params} || /videos],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideosList(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{videoId:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideoDetail(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/video/{videoId:[0-9]+}-{projectId:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.viewVideoWithProject(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/audios/{params} || /audios],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAudiosList2(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentnews/{params} || /agentnews],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAgentNewsList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/agentnews/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getAgentNewsDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/videoSearchlist/{params} || /videoSearchlist],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.getVideosResultList(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.840  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAudioNum],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.addAudioNum(java.util.HashMap)
2025-07-22 09:17:23.841  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/manageMyCollection],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.manageMyCollection(java.util.HashMap)
2025-07-22 09:17:23.841  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/newsCommentLike],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.NewsController.newsCommentLike(java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.841  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/setVideoLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.setVideoLike(java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.841  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsComment],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsComment(java.lang.String,java.lang.Integer,java.lang.String)
2025-07-22 09:17:23.841  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsReply],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsReply(java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.841  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addNewsCommentLike],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.addNewsCommentLike(java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.841  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewSimilarProjectDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewSimilarProjectDetail(java.lang.Integer)
2025-07-22 09:17:23.842  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewContrastProjects],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewContrastProjects(java.lang.Integer)
2025-07-22 09:17:23.842  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/liveDetail/{id}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.liveDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.842  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/viewWeizanList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.NewsController.viewWeizanList(com.fangxiaoer.model.WeizanSearch)
2025-07-22 09:17:23.842  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/projectnews || /projectnews/{params}]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.newProjectDynamics(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.842  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/liveList],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.NewsController.liveList()
2025-07-22 09:17:23.843  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/index.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotOverview(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.843  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages2/{subId}/index.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotOverview2(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.843  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotSecondVillage/{params} || /saleVillages/{subId:[0-9]+}/plotSecondVillage || /saleVillages/{subId:[0-9]+}/plotSecondVillage/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotSecondHouse(java.lang.String,java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotRentVillage/{params} || /saleVillages/{subId:[0-9]+}/plotRentVillage || /saleVillages/{subId:[0-9]+}/plotRentVillage/],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotRentHouse(java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/plotRealImage.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotRealImage(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{askId:[0-9]+}/getAskDetail.htm || /saleVillages/{askId:[0-9]+}/getAskDetail/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getAskDetail(java.lang.String,java.lang.Integer,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/forAddPlotAsk.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.forAddPlotAsk(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addAskWithSub],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.PlotController.addAskWithSub(java.lang.Integer,java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotExpertInterpretation.htm || /plotExpertInterpretation/{subId:[0-9]+}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotExpertInterpretation(java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/getAskNoReply.htm || /saleVillages/{subId:[0-9]+}/getAskNoReply/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getAskNoReply(java.lang.String,java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId}/plotSupport.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getPlotSupport(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages2/{subId}/plotSupport.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.getPlotSupport2(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.844  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/msgIsRead],methods=[POST]}" onto public void com.fangxiaoer.controller.PlotController.msgIsRead(java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.845  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/acceptReplay],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.PlotController.acceptReplay(java.lang.String,java.lang.Integer,java.lang.Integer)
2025-07-22 09:17:23.845  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{subId:[0-9]+}/plotAsk.htm || /saleVillages/{subId:[0-9]+}/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.PlotController.plotAsk(java.lang.String,java.lang.Integer,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.845  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addReply],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.PlotController.addReply(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.846  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/villages/{params} || /villages],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getVillages(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.846  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rents/{params} || /rents],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.showRentsHouseList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.846  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealRents/{params} || /dealRents],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.dealRentsList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.846  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealRent/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getDealRent(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.846  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentmap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRentMap(org.springframework.ui.Model)
2025-07-22 09:17:23.846  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentOneInfo],methods=[POST]}" onto public java.util.List com.fangxiaoer.controller.RentController.searchOne(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.846  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryRentMap(java.util.HashMap)
2025-07-22 09:17:23.847  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryRentMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 09:17:23.847  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/queryLocationByAddress],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.queryLocationByAddress(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rentMapDualInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.rentStationMap(java.util.HashMap)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getRentMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.RentController.getRentMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/village/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getVillage(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRent(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/rent2/{input}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.RentController.getRent2(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchRentByTitle],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.RentController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeProjects/{params} || /officeProjects],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.officeProjects(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/officeProject/{officeId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.officeProject(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.848  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/scriptoriums/{params} || /scriptoriums],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.shops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.849  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/scriptorium/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ScriptoriumControler.shop(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.851  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/memberIdCardAuthentication],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.memberIdCardAuthentication(java.util.HashMap,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.851  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/confirmAuthenticationInfo],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.confirmAuthenticationInfo(java.util.HashMap,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleHouses/{params} || /saleHouses],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSecondHouseList(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchOneInfo],methods=[POST]}" onto public java.util.List com.fangxiaoer.controller.SecondController.searchOne(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealSales/{params} || /dealSales],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getDealSecondList(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salehouse2/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeDetail2(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealSale/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getDealSaleDetail(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleVillages/{params} || /saleVillages],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeVillage(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleHouses/subway/{input}]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeSubwayList(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salemap],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSaleMap()
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salemap2],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSaleMap2()
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSecMap],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySecMap(java.util.HashMap)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchSecMapLeftList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySecMapLeftList(java.util.HashMap<java.lang.String, java.lang.Object>)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchForSubway],methods=[POST]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.SecondController.queryForSubway(java.lang.String)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleMetroStation],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secStationMap(java.util.HashMap)
2025-07-22 09:17:23.852  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/saleMetroHouseList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secStationMapHouse(java.util.HashMap)
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secPlotDetailList],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.secMapPlot(java.lang.String)
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/secondhouse/entrustReleaseHouse],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.entrustReleaseHouse()
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/salehouse/{houseId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.getSeDetail(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/UHouse/sale/judicial.aspx],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.Judicial()
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dashuju],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.SecondController.Dashuju()
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/querySubDetail],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.querySub(java.lang.String)
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/falsity],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SecondController.falsity(java.lang.String,java.lang.String,java.lang.Integer,java.lang.String,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/searchByTitle],methods=[GET]}" onto public java.util.List<com.google.gson.internal.LinkedTreeMap<java.lang.String, java.lang.Object>> com.fangxiaoer.controller.SecondController.search(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealShops/{params} || /dealShops],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.dealShops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shops/{params} || /shops],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.shops(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest,java.lang.String)
2025-07-22 09:17:23.853  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/shop/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.shop(java.lang.String,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.854  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/dealShop/{shopId}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.ShopController.dealShop(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.854  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkSignin]}" onto public java.lang.String com.fangxiaoer.controller.SignController.checkSignin(javax.servlet.http.HttpSession)
2025-07-22 09:17:23.854  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/sendSmsCode],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.sendSMSCode(java.lang.String,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.854  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/quickregist],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.quickRegist(java.lang.String,java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,java.lang.String)
2025-07-22 09:17:23.854  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkPhone],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.SignController.checkPhone(java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.854  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkPasscode],methods=[POST]}" onto public java.lang.Integer com.fangxiaoer.controller.SignController.checkPasscode(java.lang.String,java.lang.String)
2025-07-22 09:17:23.855  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/checkOCRIDCard],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.checkOCRIDCard(java.lang.String,java.lang.String,java.lang.String)
2025-07-22 09:17:23.855  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/signin],methods=[POST]}" onto public java.lang.String com.fangxiaoer.controller.SignController.login(java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpSession)
2025-07-22 09:17:23.855  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/login],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.SignController.login(java.lang.String,java.lang.String,java.lang.String,javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,java.lang.String,java.lang.String)
2025-07-22 09:17:23.855  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/quit || /Action/logout.aspx]}" onto public java.lang.String com.fangxiaoer.controller.SignController.logout(javax.servlet.http.HttpSession,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.855  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/signin]}" onto public java.lang.String com.fangxiaoer.controller.SignController.signin(org.springframework.ui.Model,java.lang.String,javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,javax.servlet.http.HttpSession,java.lang.String)
2025-07-22 09:17:23.855  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/regist]}" onto public java.lang.String com.fangxiaoer.controller.SignController.regist(org.springframework.ui.Model)
2025-07-22 09:17:23.855  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/transit]}" onto public java.lang.String com.fangxiaoer.controller.SignController.transit(org.springframework.ui.Model)
2025-07-22 09:17:23.856  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/{dir}/{path}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.staticPage(java.lang.String,java.lang.String)
2025-07-22 09:17:23.856  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/static/{path}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.staticPage(java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.856  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/fastSeek]}" onto public java.lang.String com.fangxiaoer.controller.StaticController.fastSeek(org.springframework.ui.Model)
2025-07-22 09:17:23.856  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/goufangnenglipinggu.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Gufangnenglipinggu()
2025-07-22 09:17:23.856  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/tiqianhuankuan.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Huankuan()
2025-07-22 09:17:23.856  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/loan_calculator]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Daikuan()
2025-07-22 09:17:23.856  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tool/jsq/shuifei.aspx]}" onto public java.lang.String com.fangxiaoer.controller.ToolController.Shuifei()
2025-07-22 09:17:23.857  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTouristIndex(org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.857  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tNews.htm || /tNews{id}.htm || /tNews{id}/-n{page:[0-9]+}]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTNewsList(java.lang.Integer,org.springframework.ui.Model,java.lang.String,java.lang.Integer)
2025-07-22 09:17:23.857  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/touristList || /touristList/-n{page:[0-9]+}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewTouristList(org.springframework.ui.Model,java.lang.Integer)
2025-07-22 09:17:23.857  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/touristGuideCount],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.touristGuideCount()
2025-07-22 09:17:23.857  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id:[0-9]+}.htm]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectDetail(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.857  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tNews/{id}.htm]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewNewsDetail(java.lang.Integer,org.springframework.ui.Model,java.lang.String)
2025-07-22 09:17:23.857  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/info.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectInfo(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.858  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/ask.htm || /tourist/{id}/ask/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewAskList(java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 09:17:23.858  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/comment.htm || /tourist/{id}/comment/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristCommentList(java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 09:17:23.858  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{id}/dy.htm || /tourist/{id}/dy-{dyType}.htm || /tourist/{id}/dy/{page:[0-9]+}.htm || /tourist/{id}/dy-{dyType}/{page:[0-9]+}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.touristProjectDy(java.lang.Integer,java.lang.Integer,org.springframework.ui.Model,java.lang.Integer)
2025-07-22 09:17:23.858  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/layout-{id}/{params} || /tourist/layout-{id}/{params}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.viewLayoutDetail(java.lang.Integer,java.lang.String,org.springframework.ui.Model,javax.servlet.http.HttpServletRequest)
2025-07-22 09:17:23.858  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:\d+}/album.htm || /tourist/{projectId:\d+}/album/{photoType}.htm],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.fetchProjectPics(java.lang.Integer,java.lang.String,org.springframework.ui.Model)
2025-07-22 09:17:23.858  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/{projectId:\d+}/pushcomment],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.commentProject(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.858  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/getTouristFilter],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.getTouristFilter()
2025-07-22 09:17:23.859  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addTouristOrder],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.addTouristOrder(com.fangxiaoer.model.TouristOrder)
2025-07-22 09:17:23.859  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/orderProject],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.orderProject(java.lang.String)
2025-07-22 09:17:23.859  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/editComment/{commentId}],methods=[GET]}" onto public java.lang.String com.fangxiaoer.controller.TouristController.editComment(java.lang.Integer,org.springframework.ui.Model)
2025-07-22 09:17:23.859  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/tourist/saveComment],methods=[POST]}" onto public java.util.HashMap com.fangxiaoer.controller.TouristController.saveComment(java.util.HashMap)
2025-07-22 09:17:23.859  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/addTouristAsk],methods=[POST]}" onto public java.util.HashMap<java.lang.String, java.lang.Object> com.fangxiaoer.controller.TouristController.addAsk(java.lang.Integer,java.lang.String,java.lang.String)
2025-07-22 09:17:23.859  INFO 92258 --- [restartedMain] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error]}" onto public java.lang.String com.fangxiaoer.error.CustomErrorController.generalError(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse,org.springframework.ui.Model)
2025-07-22 09:17:23.918  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 09:17:23.918  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 09:17:23.940  INFO 92258 --- [restartedMain] .m.m.a.ExceptionHandlerExceptionResolver : Detected @ExceptionHandler methods in exceptionHandler
2025-07-22 09:17:23.957  INFO 92258 --- [restartedMain] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-07-22 09:17:26.990  INFO 92258 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : FrameworkServlet 'dispatcherServlet': initialization started
2025-07-22 09:17:27.005  INFO 92258 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : FrameworkServlet 'dispatcherServlet': initialization completed in 15 ms
2025-07-22 09:17:29.286 ERROR 92258 --- [http-nio-8080-exec-4] com.fangxiaoer.common.AllInterceptor     : Block,outside POST: 0:0:0:0:0:0:0:1: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-22 09:17:34.749 ERROR 92258 --- [http-nio-8080-exec-7] com.fangxiaoer.common.AllInterceptor     : Block,outside POST: 0:0:0:0:0:0:0:1: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
