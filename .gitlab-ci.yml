stages:
  - build
  - deploy

build-sysite-job:
  stage: build
  tags:
    - java8_build
  script:
    - echo "开始构建sy站"
    - echo "版本分支：$CI_DEFAULT_BRANCH"
    - echo "版本号：$CI_COMMIT_TAG"
    - /usr/local/apache-maven-3.9.9/bin/mvn clean package -Dmaven.test.skip=true
  artifacts:
    paths:
      - target/*.war
  rules:
    - if: '$CI_DEFAULT_BRANCH == "main" && $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: always
    - when: never
  allow_failure: false

deploy-sysite-job:
  stage: deploy
  tags:
    - python3_deploy
  script:
    - echo "执行python脚本，开始把新构建的sy站部署到服务端..."
    - python3 /etc/gitlab-runner/python-shell/gitlab_deploy_sy_war.py
  dependencies:
    - build-sysite-job
  rules:
    - if: '$CI_DEFAULT_BRANCH == "main" && $CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: manual
    - when: never
  interruptible: true