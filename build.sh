#!/bin/sh  
git pull
mvn clean package  -Dmaven.test.skip=true
WAR=`ls target/*.war`
if [ "$WAR" = "" ];
then
 echo "Build failed!!! Please double check"
 exit 0
fi
echo $WAR
read -p "Enter password: "
echo $REPLY
if [ "$REPLY" != "orlando" ];
then
 echo "Incorrect password! Exit package processing"
 exit 0
fi
DATE=`date +%Y%m%d%H%M`
DATE=$DATE.backup
mv $WAR target/$DATE
ftp *************************************** <<FTPEND
cd sy.fangxiaoer.com
#ftp **************************************** <<FTPEND
put target/$DATE $DATE
rename ROOT.war backup/_$DATE
rename $DATE ROOT.war
close
exit
FTPEND


